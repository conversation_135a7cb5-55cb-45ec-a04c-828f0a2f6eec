{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"householdDialog\"];\nfunction HouseholdBindingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"nb-icon\", 11);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.reminderText);\n  }\n}\nfunction HouseholdBindingComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵelement(3, \"nb-icon\", 15);\n    i0.ɵɵelementStart(4, \"span\", 16);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_2_Template_button_click_6_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.onClearAll();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 18)(9, \"span\", 19);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"nb-icon\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const detailsPopover_r4 = i0.ɵɵreference(10);\n    i0.ɵɵproperty(\"nbPopover\", detailsPopover_r4);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.displayText.selectedPrefix, \" (\", ctx_r1.getSelectedCount(), \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.getGroupedSummary());\n  }\n}\nfunction HouseholdBindingComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nb-icon\", 21);\n    i0.ɵɵtext(2, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getSelectedCount() > 0 ? \"\\u5DF2\\u9078\\u64C7 \" + ctx_r1.getSelectedCount() + \" \" + ctx_r1.displayText.selectedCount : ctx_r1.placeholder || ctx_r1.displayText.placeholder, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_9_ng_container_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r5.floor);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_9_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 25)(2, \"span\", 26);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 27);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_9_ng_container_2_span_6_Template, 2, 1, \"span\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r5.building);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.houseName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.useHouseNameMode && item_r5.floor);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"ul\", 23);\n    i0.ɵɵtemplate(2, HouseholdBindingComponent_ng_template_9_ng_container_2_Template, 7, 3, \"ng-container\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getAllSelectedItems());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"nb-icon\", 56);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.reminderText);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58);\n    i0.ɵɵelement(2, \"nb-icon\", 59);\n    i0.ɵɵelementStart(3, \"p\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\\u8F09\\u5165\", ctx_r1.displayText.unitType, \"\\u8CC7\\u6599\\u4E2D...\");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_11_div_14_button_6_Template_button_click_0_listener() {\n      const building_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onBuildingSelect(building_r8));\n    });\n    i0.ɵɵelementStart(1, \"span\", 79);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 80);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.selectedBuilding === building_r8 ? \"#e3f2fd\" : \"transparent\")(\"border-left\", ctx_r1.selectedBuilding === building_r8 ? \"3px solid #007bff\" : \"3px solid transparent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r8);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getBuildingCount(building_r8), \"\\u6236 \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵelement(1, \"nb-icon\", 82);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedBuilding, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 83);\n    i0.ɵɵelement(1, \"nb-icon\", 84);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_15_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_11_div_14_div_15_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onUnselectAllBuilding());\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_11_div_14_div_15_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllFiltered());\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_11_div_14_div_15_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllBuilding());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_11_div_14_div_15_button_5_Template, 2, 0, \"button\", 88);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"opacity\", ctx_r1.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"opacity\", ctx_r1.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u9078\", ctx_r1.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSomeBuildingSelected());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.displayText.noResults, \" \\\"\", ctx_r1.searchTerm, \"\\\" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"div\", 91)(2, \"input\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingComponent_ng_template_11_div_14_div_16_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchTerm, $event) || (ctx_r1.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdBindingComponent_ng_template_11_div_14_div_16_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"nb-icon\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_ng_template_11_div_14_div_16_div_4_Template, 2, 2, \"div\", 94);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchTerm);\n    i0.ɵɵproperty(\"placeholder\", ctx_r1.displayText.searchPlaceholder);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchTerm && ctx_r1.hasNoSearchResults());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_17_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_17_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_11_div_14_div_17_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onFloorSelect(\"\"));\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_17_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_11_div_14_div_17_button_8_Template_button_click_0_listener() {\n      const floor_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onFloorSelect(floor_r14));\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 105);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 106);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const floor_r14 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.selectedFloor === floor_r14 ? \"#007bff\" : \"#f8f9fa\")(\"color\", ctx_r1.selectedFloor === floor_r14 ? \"#fff\" : \"#495057\")(\"border\", ctx_r1.selectedFloor === floor_r14 ? \"2px solid #007bff\" : \"1px solid #dee2e6\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", floor_r14, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.getFloorCount(floor_r14), \")\");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"div\", 97);\n    i0.ɵɵelement(2, \"nb-icon\", 98);\n    i0.ɵɵelementStart(3, \"span\", 99);\n    i0.ɵɵtext(4, \"\\u6A13\\u5C64\\u7BE9\\u9078:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_11_div_14_div_17_span_5_Template, 2, 1, \"span\", 71)(6, HouseholdBindingComponent_ng_template_11_div_14_div_17_button_6_Template, 2, 0, \"button\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 101);\n    i0.ɵɵtemplate(8, HouseholdBindingComponent_ng_template_11_div_14_div_17_button_8_Template, 5, 8, \"button\", 102);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.floors);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"nb-icon\", 108);\n    i0.ɵɵelementStart(2, \"p\", 60);\n    i0.ɵɵtext(3, \"\\u8ACB\\u5148\\u9078\\u64C7\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_20_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 114);\n    i0.ɵɵelement(1, \"nb-icon\", 115);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const household_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.isHouseholdSelected(household_r16.houseId) ? \"rgba(255,255,255,0.9)\" : \"#28a745\")(\"color\", ctx_r1.isHouseholdSelected(household_r16.houseId) ? \"#007bff\" : \"#fff\")(\"border\", ctx_r1.isHouseholdSelected(household_r16.houseId) ? \"1px solid rgba(0,123,255,0.3)\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", household_r16.floor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_20_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵtext(1, \" \\u2715 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_20_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_11_div_14_div_20_ng_container_1_Template_button_click_1_listener() {\n      const household_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onHouseholdToggle(household_r16.houseId));\n    });\n    i0.ɵɵelementStart(2, \"span\", 111);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_ng_template_11_div_14_div_20_ng_container_1_span_4_Template, 3, 7, \"span\", 112)(5, HouseholdBindingComponent_ng_template_11_div_14_div_20_ng_container_1_div_5_Template, 2, 0, \"div\", 113);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const household_r16 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.isHouseholdSelected(household_r16.houseId) ? \"#007bff\" : ctx_r1.isHouseholdExcluded(household_r16.houseId) ? \"#f8f9fa\" : \"#fff\")(\"color\", ctx_r1.isHouseholdSelected(household_r16.houseId) ? \"#fff\" : ctx_r1.isHouseholdExcluded(household_r16.houseId) ? \"#6c757d\" : \"#495057\")(\"border\", ctx_r1.isHouseholdSelected(household_r16.houseId) ? \"2px solid #007bff\" : ctx_r1.isHouseholdExcluded(household_r16.houseId) ? \"1px solid #dee2e6\" : \"1px solid #ced4da\")(\"opacity\", ctx_r1.isHouseholdDisabled(household_r16.houseId) ? \"0.6\" : \"1\")(\"cursor\", ctx_r1.isHouseholdDisabled(household_r16.houseId) ? \"not-allowed\" : \"pointer\");\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isHouseholdDisabled(household_r16.houseId));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"text-decoration\", ctx_r1.isHouseholdExcluded(household_r16.houseId) ? \"line-through\" : \"none\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", household_r16.houseName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.useHouseNameMode && household_r16.floor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isHouseholdExcluded(household_r16.houseId));\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_ng_template_11_div_14_div_20_ng_container_1_Template, 6, 16, \"ng-container\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getUniqueHouseholdsForDisplay());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"nb-icon\", 117);\n    i0.ɵɵelementStart(2, \"p\", 60);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.displayText.noAvailable);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"div\", 63)(3, \"h6\", 64);\n    i0.ɵɵtext(4, \"\\u68DF\\u5225\\u5217\\u8868\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 65);\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_11_div_14_button_6_Template, 5, 6, \"button\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 67)(8, \"div\", 63)(9, \"div\", 68)(10, \"div\", 32)(11, \"h6\", 69);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, HouseholdBindingComponent_ng_template_11_div_14_span_13_Template, 3, 1, \"span\", 70)(14, HouseholdBindingComponent_ng_template_11_div_14_span_14_Template, 3, 1, \"span\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, HouseholdBindingComponent_ng_template_11_div_14_div_15_Template, 6, 8, \"div\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, HouseholdBindingComponent_ng_template_11_div_14_div_16_Template, 5, 3, \"div\", 73)(17, HouseholdBindingComponent_ng_template_11_div_14_div_17_Template, 9, 3, \"div\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 75);\n    i0.ɵɵtemplate(19, HouseholdBindingComponent_ng_template_11_div_14_div_19_Template, 4, 0, \"div\", 76)(20, HouseholdBindingComponent_ng_template_11_div_14_div_20_Template, 2, 1, \"div\", 77)(21, HouseholdBindingComponent_ng_template_11_div_14_div_21_Template, 4, 1, \"div\", 76);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.displayText.unitSelection, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.useHouseNameMode && ctx_r1.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowBatchSelect && ctx_r1.selectedBuilding && ctx_r1.buildingData[ctx_r1.selectedBuilding] && ctx_r1.buildingData[ctx_r1.selectedBuilding].length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.useHouseNameMode && ctx_r1.selectedBuilding && ctx_r1.floors.length > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding && ctx_r1.buildingData[ctx_r1.selectedBuilding] && ctx_r1.buildingData[ctx_r1.selectedBuilding].length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.selectedBuilding && ctx_r1.buildings.length > 0 && (!ctx_r1.buildingData[ctx_r1.selectedBuilding] || ctx_r1.buildingData[ctx_r1.selectedBuilding].length === 0) && !ctx_r1.searchTerm);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵelement(1, \"nb-icon\", 118);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u9650\\u5236: \\u6700\\u591A \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" \\u500B\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.maxSelections);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_26_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 122);\n    i0.ɵɵelement(1, \"nb-icon\", 84);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"nb-icon\", 119);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u7576\\u524D\\u68DF\\u5225: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 120);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_11_div_26_span_6_Template, 3, 1, \"span\", 121);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.useHouseNameMode && ctx_r1.selectedFloor);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u641C\\u5C0B: \\\"\", ctx_r1.searchTerm, \"\\\" (\", ctx_r1.getFilteredHouseholdsCount(), \" \\u500B\\u7D50\\u679C) \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 124);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_11_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClearAll());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 125);\n    i0.ɵɵtext(2, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_button_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 126);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_11_button_31_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.resetSearch());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 127);\n    i0.ɵɵtext(2, \" \\u91CD\\u7F6E\\u641C\\u5C0B \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 30)(1, \"nb-card-header\")(2, \"div\", 31)(3, \"div\", 32);\n    i0.ɵɵelement(4, \"nb-icon\", 33);\n    i0.ɵɵelementStart(5, \"span\", 34);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 35);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 35);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, HouseholdBindingComponent_ng_template_11_div_11_Template, 4, 1, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-card-body\", 37);\n    i0.ɵɵtemplate(13, HouseholdBindingComponent_ng_template_11_div_13_Template, 5, 1, \"div\", 38)(14, HouseholdBindingComponent_ng_template_11_div_14_Template, 22, 10, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"nb-card-footer\", 40)(16, \"div\", 41)(17, \"div\", 42)(18, \"div\", 43);\n    i0.ɵɵelement(19, \"nb-icon\", 44);\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21, \"\\u5DF2\\u9078\\u64C7: \");\n    i0.ɵɵelementStart(22, \"strong\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, HouseholdBindingComponent_ng_template_11_div_25_Template, 7, 1, \"div\", 45)(26, HouseholdBindingComponent_ng_template_11_div_26_Template, 7, 2, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, HouseholdBindingComponent_ng_template_11_div_27_Template, 2, 2, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 48)(29, \"div\", 49);\n    i0.ɵɵtemplate(30, HouseholdBindingComponent_ng_template_11_button_30_Template, 3, 0, \"button\", 50)(31, HouseholdBindingComponent_ng_template_11_button_31_Template, 3, 0, \"button\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 49)(33, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_11_Template_button_click_33_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r6).dialogRef;\n      return i0.ɵɵresetView(ref_r19.close());\n    });\n    i0.ɵɵtext(34, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_11_Template_button_click_35_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r6).dialogRef;\n      return i0.ɵɵresetView(ref_r19.close());\n    });\n    i0.ɵɵelement(36, \"nb-icon\", 54);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.displayText.selectUnit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.buildings.length, \" \\u500B\\u68DF\\u5225)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx_r1.getSelectedCount(), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.reminderText);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.getSelectedCount());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.displayText.selectedCount, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maxSelections);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchTerm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedHouseIds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchTerm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r1.getSelectedCount(), \") \");\n  }\n}\nexport let HouseholdBindingComponent = /*#__PURE__*/(() => {\n  class HouseholdBindingComponent {\n    constructor(cdr, dialogService) {\n      this.cdr = cdr;\n      this.dialogService = dialogService;\n      this.placeholder = '請選擇戶別';\n      this.maxSelections = null;\n      this.disabled = false;\n      this.buildCaseId = null; // 建案ID（用於識別）\n      this.buildingData = {};\n      this.allowBatchSelect = true;\n      this.excludedHouseIds = []; // 改為：排除的戶別ID（已被其他元件選擇）\n      this.useHouseNameMode = false; // 新增：使用戶別名稱模式\n      this.preFilterHouseType = null; // 新增：預先篩選的戶別類型（1=地主戶，2=銷售戶）\n      this.reminderText = ''; // 新增：提醒文案\n      this.selectionChange = new EventEmitter();\n      this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n      this.houseNameChange = new EventEmitter(); // 新增：useHouseNameMode 時回傳戶別名稱陣列\n      this.isOpen = false;\n      this.selectedBuilding = '';\n      this.searchTerm = '';\n      this.selectedFloor = ''; // 新增：選中的樓層\n      this.selectedHouseIds = []; // 改為：使用 houseId 作為選擇的key\n      this.selectedHouseNames = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列\n      this.buildings = [];\n      this.floors = []; // 新增：當前棧別的樓層列表\n      this.filteredHouseholds = []; // 保持為字串陣列用於UI顯示\n      this.selectedByBuilding = {}; // 改為：儲存 houseId\n      this.isLoading = false; // 新增：載入狀態  // ControlValueAccessor implementation\n      this.onChange = value => {};\n      this.onTouched = () => {};\n    }\n    /**\n     * [優化] 產生已選戶別的分組摘要文字。\n     * 用於簡潔地顯示選擇結果。\n     * @returns string - 例如: \"棟 A (10戶), 棟 B (5戶)\"\n     */\n    getGroupedSummary() {\n      if (this.selectedHouseIds.length === 0) {\n        return '';\n      }\n      return this.buildings.map(building => {\n        const count = this.selectedByBuilding[building]?.length || 0;\n        return count > 0 ? `${building} (${count}戶)` : null;\n      }).filter(Boolean) // 過濾掉沒有選擇的棟別\n      .join(', ');\n    }\n    /**\n     * [優化] 獲取所有已選戶別的詳細資訊列表。\n     * 用於在 Popover 中顯示。\n     * @returns HouseholdItem[]\n     */\n    getAllSelectedItems() {\n      return this.selectedHouseIds.map(id => this.getHouseholdByHouseId(id)).filter(item => !!item);\n    }\n    writeValue(value) {\n      if (!value || value.length === 0) {\n        this.selectedHouseIds = [];\n        this.selectedHouseNames = [];\n      } else {\n        const firstItem = value[0];\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 期望接收戶別名稱陣列\n          if (typeof firstItem === 'string') {\n            this.selectedHouseNames = [...new Set(value)]; // 去除重複的戶別名稱\n            // 將戶別名稱轉換為 houseId（用於內部邏輯），會自動處理重複名稱\n            this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\n            console.log('useHouseNameMode: 使用傳入的戶別名稱陣列（已去重）');\n          } else {\n            console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);\n            this.selectedHouseNames = [];\n            this.selectedHouseIds = [];\n          }\n        } else {\n          // 一般模式: 期望接收 houseId 陣列\n          if (typeof firstItem === 'number') {\n            this.selectedHouseIds = value;\n            // 將 houseId 轉換為戶別名稱（用於顯示）\n            this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n            console.log('一般模式: 使用傳入的 houseId 陣列');\n          } else if (typeof firstItem === 'string') {\n            // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告\n            console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');\n            console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');\n            return;\n          } else {\n            console.error('writeValue 收到未知格式的資料:', value);\n            this.selectedHouseIds = [];\n            this.selectedHouseNames = [];\n          }\n        }\n      }\n      this.updateSelectedByBuilding();\n    }\n    registerOnChange(fn) {\n      this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onTouched = fn;\n    }\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n    }\n    ngOnInit() {\n      this.initializeData();\n    }\n    ngOnChanges(changes) {\n      if (changes['buildingData']) {\n        // 當 buildingData 變更時，重新初始化\n        const newBuildingData = changes['buildingData'].currentValue;\n        // 如果 buildingData 為空物件或 undefined，可能正在載入\n        if (!newBuildingData || Object.keys(newBuildingData).length === 0) {\n          this.isLoading = false; // 假設空物件表示載入完成但無資料\n        } else {\n          this.isLoading = false; // 有資料表示載入完成\n        }\n        this.buildings = Object.keys(this.buildingData || {});\n        console.log('buildingData updated:', this.buildingData);\n        this.updateFilteredHouseholds();\n        this.updateSelectedByBuilding();\n      }\n      if (changes['excludedHouseIds']) {\n        // 當排除列表變化時，重新更新顯示\n        this.updateFilteredHouseholds();\n      }\n      if (changes['useHouseNameMode']) {\n        if (this.useHouseNameMode) {\n          this.selectedFloor = '';\n        }\n      }\n    }\n    initializeData() {\n      // 使用傳入的 buildingData\n      if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n      } else {\n        // 沒有 buildingData，保持空狀態\n        this.buildings = [];\n        console.log('No buildingData provided');\n      }\n    }\n    updateSelectedByBuilding() {\n      const grouped = {};\n      this.selectedHouseIds.forEach(houseId => {\n        for (const building of this.buildings) {\n          const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n          if (item) {\n            if (!grouped[building]) grouped[building] = [];\n            grouped[building].push(houseId);\n            break;\n          }\n        }\n      });\n      this.selectedByBuilding = grouped;\n    }\n    onBuildingSelect(building) {\n      console.log('Building selected:', building);\n      this.selectedBuilding = building;\n      this.selectedFloor = ''; // 重置樓層選擇\n      this.searchTerm = '';\n      this.updateFloorsForBuilding(); // 更新樓層列表\n      this.updateFilteredHouseholds();\n      console.log('Filtered households count:', this.filteredHouseholds.length);\n      // 手動觸發變更偵測\n      this.cdr.detectChanges();\n    }\n    onBuildingClick(building) {\n      console.log('Building clicked (mousedown):', building);\n    }\n    updateFilteredHouseholds() {\n      if (!this.selectedBuilding) {\n        this.filteredHouseholds = [];\n        return;\n      }\n      const households = this.buildingData[this.selectedBuilding] || [];\n      console.log('Available households for building:', households.length);\n      // 提取戶別代碼並進行搜尋、樓層和戶別類型過濾\n      const filteredItems = households.filter(h => {\n        // 樓層篩選：在 useHouseNameMode 時跳過樓層篩選，否則按原邏輯篩選\n        const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n        // 搜尋篩選：戶別代碼包含搜尋詞\n        const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n        // 戶別類型篩選：如果有指定預篩選類型，則只顯示符合的戶別\n        const houseTypeMatch = this.preFilterHouseType === null || h.houseType === this.preFilterHouseType;\n        return floorMatch && searchMatch && houseTypeMatch;\n      });\n      // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\n      this.filteredHouseholds = filteredItems.map(h => h.houseName);\n      console.log('Filtered households result:', this.filteredHouseholds.length);\n      console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId}, Type:${h.houseType})`));\n      if (this.preFilterHouseType !== null) {\n        console.log('Pre-filter house type:', this.preFilterHouseType);\n      }\n    }\n    onSearchChange(event) {\n      this.searchTerm = event.target.value;\n      this.updateFilteredHouseholds();\n      console.log('Search term changed:', this.searchTerm);\n    }\n    resetSearch() {\n      this.searchTerm = '';\n      this.updateFilteredHouseholds();\n      console.log('Search reset');\n    }\n    onHouseholdToggle(houseId) {\n      console.log('onHouseholdToggle called with houseId:', houseId);\n      console.log('Current selectedHouseIds:', this.selectedHouseIds);\n      if (!houseId) {\n        console.log(`無效的 houseId: ${houseId}`);\n        return;\n      }\n      // 防止選擇已排除的戶別\n      if (this.isHouseIdExcluded(houseId)) {\n        console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\n        return;\n      }\n      // 取得被點擊戶別的名稱\n      const clickedHousehold = this.getHouseholdByHouseId(houseId);\n      if (!clickedHousehold) {\n        console.log(`找不到 houseId ${houseId} 對應的戶別資訊`);\n        return;\n      }\n      let newSelection;\n      if (this.useHouseNameMode) {\n        // useHouseNameMode: 處理同名戶別的邏輯\n        const houseName = clickedHousehold.houseName;\n        const allMatchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n        // 檢查是否有任何同名戶別已被選中\n        const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n        if (hasAnySelected) {\n          // 如果有同名戶別被選中，移除所有同名戶別\n          newSelection = this.selectedHouseIds.filter(id => !allMatchingHouseIds.includes(id));\n          console.log(`useHouseNameMode: 移除所有同名戶別 \"${houseName}\":`, allMatchingHouseIds);\n        } else {\n          // 如果沒有同名戶別被選中，只添加第一個（通常是當前點擊的）\n          if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n            console.log('已達到最大選擇數量');\n            return;\n          }\n          newSelection = [...this.selectedHouseIds, allMatchingHouseIds[0]];\n          console.log(`useHouseNameMode: 添加戶別 \"${houseName}\" 的第一個項目:`, allMatchingHouseIds[0]);\n        }\n      } else {\n        // 一般模式: 原有邏輯\n        const isSelected = this.isHouseIdSelected(houseId);\n        if (isSelected) {\n          newSelection = this.selectedHouseIds.filter(id => id !== houseId);\n        } else {\n          if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n            console.log('已達到最大選擇數量');\n            return;\n          }\n          newSelection = [...this.selectedHouseIds, houseId];\n        }\n      }\n      this.selectedHouseIds = newSelection;\n      this.emitChanges();\n    }\n    onRemoveHousehold(houseId) {\n      this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\n      this.emitChanges();\n    }\n    onSelectAllFiltered() {\n      console.log('onSelectAllFiltered called');\n      console.log('selectedBuilding:', this.selectedBuilding);\n      console.log('selectedFloor:', this.selectedFloor);\n      console.log('searchTerm:', this.searchTerm);\n      if (!this.selectedBuilding) {\n        console.log('No building selected');\n        return;\n      }\n      // 使用 getUniqueHouseholdsForDisplay 方法來獲取要處理的戶別列表\n      const filteredHouseholdItems = this.getUniqueHouseholdsForDisplay();\n      console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n      if (filteredHouseholdItems.length === 0) {\n        console.log('No filtered households found');\n        return;\n      }\n      // 計算可以新增的戶別數量\n      const currentCount = this.selectedHouseIds.length;\n      const maxAllowed = this.maxSelections || Infinity;\n      const remainingSlots = maxAllowed - currentCount;\n      // 取得尚未選擇且未被排除的過濾戶別ID\n      const unselectedFilteredIds = [];\n      for (const household of filteredHouseholdItems) {\n        if (household.houseId) {\n          if (this.useHouseNameMode) {\n            // useHouseNameMode: 檢查是否有任何同名戶別已被選擇\n            const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n            const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n            const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseIdExcluded(id));\n            if (!hasAnySelected && !hasAnyExcluded) {\n              unselectedFilteredIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n            }\n          } else {\n            // 一般模式: 原有邏輯\n            if (!this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\n              unselectedFilteredIds.push(household.houseId);\n            }\n          }\n        }\n      }\n      console.log('Unselected filtered IDs:', unselectedFilteredIds);\n      // 根據剩餘空間決定要新增的戶別\n      const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\n      if (toAdd.length > 0) {\n        this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n        this.emitChanges();\n        console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\n      } else {\n        console.log('No households to add');\n      }\n    }\n    onSelectAllBuilding() {\n      if (!this.selectedBuilding) return;\n      // 取得當前棟別的所有戶別\n      const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n      // 計算可以新增的戶別數量\n      const currentCount = this.selectedHouseIds.length;\n      const maxAllowed = this.maxSelections || Infinity;\n      const remainingSlots = maxAllowed - currentCount;\n      // 取得尚未選擇且未被排除的棟別戶別 ID\n      const unselectedBuildingIds = [];\n      if (this.useHouseNameMode) {\n        // useHouseNameMode: 只選擇唯一的戶別名稱\n        const processedHouseNames = new Set();\n        for (const household of buildingHouseholds) {\n          if (household.houseId && household.houseName && !processedHouseNames.has(household.houseName)) {\n            processedHouseNames.add(household.houseName);\n            const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n            const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n            const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseholdExcluded(id));\n            if (!hasAnySelected && !hasAnyExcluded) {\n              unselectedBuildingIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n            }\n          }\n        }\n      } else {\n        // 一般模式: 原有邏輯\n        for (const household of buildingHouseholds) {\n          if (household.houseId && !this.selectedHouseIds.includes(household.houseId) && !this.isHouseholdExcluded(household.houseId)) {\n            unselectedBuildingIds.push(household.houseId);\n          }\n        }\n      }\n      // 根據剩餘空間決定要新增的戶別\n      const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\n      if (toAdd.length > 0) {\n        this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n        this.emitChanges();\n        console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n      }\n    }\n    onUnselectAllBuilding() {\n      if (!this.selectedBuilding) return;\n      const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n      const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined);\n      this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\n      this.emitChanges();\n    }\n    onClearAll() {\n      this.selectedHouseIds = [];\n      this.emitChanges();\n    }\n    emitChanges() {\n      this.updateSelectedByBuilding();\n      console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds); // 根據模式決定要回傳的資料格式\n      if (this.useHouseNameMode) {\n        // useHouseNameMode: 回傳戶別名稱陣列（去重複）\n        this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n        const uniqueHouseNames = [...new Set(this.selectedHouseNames)]; // 確保去重複\n        console.log('useHouseNameMode - 回傳戶別名稱陣列（已去重）:', uniqueHouseNames);\n        this.onChange([...uniqueHouseNames]);\n        this.houseNameChange.emit([...uniqueHouseNames]);\n      } else {\n        // 一般模式: 回傳 houseId 陣列\n        console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);\n        this.onChange([...this.selectedHouseIds]);\n        // 回傳 houseId 陣列\n        const houseIds = this.selectedHouseIds.filter(id => id !== undefined);\n        console.log('House IDs to emit:', houseIds);\n        this.houseIdChange.emit(houseIds);\n      }\n      this.onTouched();\n      // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）\n      const selectedItems = this.selectedHouseIds.map(houseId => {\n        for (const building of this.buildings) {\n          const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n          if (item) return item;\n        }\n        return null;\n      }).filter(item => item !== null);\n      console.log('Selected items to emit:', selectedItems);\n      this.selectionChange.emit(selectedItems);\n    }\n    toggleDropdown() {\n      if (!this.disabled) {\n        this.openDialog();\n        console.log('Opening household selection dialog');\n        console.log('Buildings available:', this.buildings);\n      }\n    }\n    openDialog() {\n      this.dialogService.open(this.householdDialog, {\n        context: {},\n        closeOnBackdropClick: false,\n        closeOnEsc: true,\n        autoFocus: false\n      });\n    }\n    closeDropdown() {\n      // 這個方法現在用於關閉對話框\n      // 對話框的關閉將由 NbDialogRef 處理\n    }\n    isHouseholdSelected(houseId) {\n      if (!houseId) return false;\n      if (this.useHouseNameMode) {\n        // useHouseNameMode: 檢查是否有任何同名戶別被選中\n        const household = this.getHouseholdByHouseId(houseId);\n        if (household) {\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          return allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n        }\n        return false;\n      } else {\n        // 一般模式: 直接檢查 houseId\n        return this.selectedHouseIds.includes(houseId);\n      }\n    }\n    isHouseholdExcluded(houseId) {\n      if (!houseId) return false;\n      return this.excludedHouseIds.includes(houseId);\n    }\n    isHouseholdDisabled(houseId) {\n      if (!houseId) return true;\n      return this.isHouseholdExcluded(houseId) || !this.canSelectMore() && !this.isHouseholdSelected(houseId);\n    }\n    canSelectMore() {\n      return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\n    }\n    isAllBuildingSelected() {\n      if (!this.selectedBuilding) return false;\n      const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled && h.houseId !== undefined);\n      return buildingHouseholds.length > 0 && buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n    }\n    isSomeBuildingSelected() {\n      if (!this.selectedBuilding) return false;\n      const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n      return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n    }\n    getSelectedByBuilding() {\n      return this.selectedByBuilding;\n    }\n    getBuildingCount(building) {\n      if (this.useHouseNameMode) {\n        // useHouseNameMode: 返回唯一戶別名稱的數量\n        const households = this.buildingData[building] || [];\n        const uniqueHouseNames = new Set(households.map(h => h.houseName));\n        return uniqueHouseNames.size;\n      } else {\n        // 一般模式: 返回總戶別數量\n        return this.buildingData[building]?.length || 0;\n      }\n    }\n    getSelectedCount() {\n      if (this.useHouseNameMode) {\n        // useHouseNameMode: 返回唯一戶別名稱的數量\n        const uniqueHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n        return uniqueHouseNames.length;\n      } else {\n        // 一般模式: 返回實際選中的戶別數量\n        return this.selectedHouseIds.length;\n      }\n    }\n    // 輔助方法：安全地獲取建築物的已選戶別 ID\n    getBuildingSelectedHouseIds(building) {\n      return this.selectedByBuilding[building] || [];\n    }\n    // 輔助方法：檢查建築物是否有已選戶別\n    hasBuildingSelected(building) {\n      return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n    }\n    // 新增：更新當前棧別的樓層計數\n    updateFloorsForBuilding() {\n      if (!this.selectedBuilding) {\n        this.floors = [];\n        return;\n      }\n      const households = this.buildingData[this.selectedBuilding] || [];\n      const floorSet = new Set();\n      households.forEach(household => {\n        if (household.floor) {\n          floorSet.add(household.floor);\n        }\n      });\n      // 對樓層進行自然排序\n      this.floors = Array.from(floorSet).sort((a, b) => {\n        const numA = parseInt(a.replace(/[^0-9]/g, ''));\n        const numB = parseInt(b.replace(/[^0-9]/g, ''));\n        return numA - numB;\n      });\n      console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n    }\n    // 新增：樓層選擇處理\n    onFloorSelect(floor) {\n      console.log('Floor selected:', floor);\n      this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n      this.updateFilteredHouseholds();\n      this.cdr.detectChanges();\n    }\n    // 新增：取得當前棧別的樓層計數\n    getFloorCount(floor) {\n      if (!this.selectedBuilding) return 0;\n      const households = this.buildingData[this.selectedBuilding] || [];\n      return households.filter(h => h.floor === floor).length;\n    }\n    // 新增：取得戶別的樓層資訊\n    getHouseholdFloor(householdCode) {\n      if (!this.selectedBuilding) return '';\n      const households = this.buildingData[this.selectedBuilding] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      return household?.floor || '';\n    }\n    // 新增：取得戶別的完整資訊（包含樓層）\n    getHouseholdInfo(householdCode) {\n      for (const building of this.buildings) {\n        const households = this.buildingData[building] || [];\n        const household = households.find(h => h.houseName === householdCode);\n        if (household) {\n          return {\n            houseName: household.houseName,\n            floor: household.floor || ''\n          };\n        }\n      }\n      return {\n        houseName: householdCode,\n        floor: ''\n      };\n    }\n    // 新增：根據 houseId 取得戶別的完整資訊\n    getHouseholdInfoById(houseId) {\n      for (const building of this.buildings) {\n        const households = this.buildingData[building] || [];\n        const household = households.find(h => h.houseId === houseId);\n        if (household) {\n          return {\n            houseName: household.houseName,\n            floor: household.floor || ''\n          };\n        }\n      }\n      return {\n        houseName: `ID:${houseId}`,\n        floor: ''\n      };\n    }\n    // 新增：檢查搜尋是否有結果\n    hasNoSearchResults() {\n      if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n        return false;\n      }\n      const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n        const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n        const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n        const houseTypeMatch = this.preFilterHouseType === null || h.houseType === this.preFilterHouseType;\n        return floorMatch && searchMatch && houseTypeMatch;\n      });\n      return filtered.length === 0;\n    }\n    // 新增：取得過濾後的戶別數量\n    getFilteredHouseholdsCount() {\n      return this.getUniqueHouseholdsForDisplay().length;\n    }\n    // 新增：產生戶別的唯一識別符\n    getHouseholdUniqueId(household) {\n      return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n    }\n    // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\n    getHouseholdByHouseId(houseId) {\n      for (const building of this.buildings) {\n        const households = this.buildingData[building] || [];\n        const household = households.find(h => h.houseId === houseId);\n        if (household) return household;\n      }\n      return null;\n    } // 新增：輔助方法 - 根據 houseName 查找 houseId\n    getHouseIdByHouseName(houseName) {\n      const matchingHouseholds = [];\n      // 收集所有符合名稱的戶別\n      for (const building of this.buildings) {\n        const households = this.buildingData[building] || [];\n        const matches = households.filter(h => h.houseName === houseName);\n        matches.forEach(household => {\n          matchingHouseholds.push({\n            building,\n            household\n          });\n        });\n      }\n      console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\n      if (matchingHouseholds.length === 0) {\n        console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n        return null;\n      }\n      if (matchingHouseholds.length > 1) {\n        console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\n        console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\n      }\n      const firstMatch = matchingHouseholds[0];\n      return firstMatch.household.houseId || null;\n    }\n    // 新增：輔助方法 - 根據 houseName 查找所有對應的 houseId（處理重複名稱）\n    getAllHouseIdsByHouseName(houseName) {\n      const houseIds = [];\n      // 收集所有符合名稱的戶別\n      for (const building of this.buildings) {\n        const households = this.buildingData[building] || [];\n        const matches = households.filter(h => h.houseName === houseName);\n        matches.forEach(household => {\n          if (household.houseId) {\n            houseIds.push(household.houseId);\n          }\n        });\n      }\n      return houseIds;\n    }\n    // 新增：輔助方法 - 檢查 houseId 是否被選中\n    isHouseIdSelected(houseId) {\n      return this.selectedHouseIds.includes(houseId);\n    }\n    // 新增：輔助方法 - 檢查 houseId 是否被排除\n    isHouseIdExcluded(houseId) {\n      return this.excludedHouseIds.includes(houseId);\n    }\n    // 新增：從唯一識別符獲取戶別物件\n    getHouseholdFromUniqueId(uniqueId) {\n      for (const building of this.buildings) {\n        const households = this.buildingData[building] || [];\n        const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n        if (household) return household;\n      }\n      return null;\n    } // 新增：將戶別名稱陣列轉換為 houseId 陣列（在 useHouseNameMode 下只選擇第一個匹配項）\n    convertHouseNamesToIds(houseNames) {\n      const houseIds = [];\n      const uniqueHouseNames = [...new Set(houseNames)]; // 去除重複的戶別名稱\n      for (const houseName of uniqueHouseNames) {\n        const matchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n        if (matchingHouseIds.length > 0) {\n          if (this.useHouseNameMode) {\n            // useHouseNameMode: 只選擇第一個匹配的 houseId，避免多個同名戶別都被選中\n            houseIds.push(matchingHouseIds[0]);\n            if (matchingHouseIds.length > 1) {\n              console.log(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，在 useHouseNameMode 下只選擇第一個:`, matchingHouseIds[0]);\n            }\n          } else {\n            // 一般模式: 將所有對應的 houseId 都加入（保持原有邏輯）\n            houseIds.push(...matchingHouseIds);\n            if (matchingHouseIds.length > 1) {\n              console.warn(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，已全部加入選擇:`, matchingHouseIds);\n            }\n          }\n        } else {\n          console.warn(`無法找到戶別名稱 \"${houseName}\" 對應的 houseId`);\n        }\n      }\n      // 去除重複的 houseId\n      return [...new Set(houseIds)];\n    }\n    // 新增：將 houseId 陣列轉換為戶別名稱陣列（去重複）\n    convertIdsToHouseNames(houseIds) {\n      const houseNames = [];\n      const uniqueHouseIds = [...new Set(houseIds)]; // 去除重複的 houseId\n      for (const houseId of uniqueHouseIds) {\n        const householdInfo = this.getHouseholdInfoById(houseId);\n        if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {\n          houseNames.push(householdInfo.houseName);\n        } else {\n          console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);\n        }\n      }\n      // 去除重複的戶別名稱\n      return [...new Set(houseNames)];\n    }\n    // 新增：取得去重複的戶別列表（用於 useHouseNameMode 顯示）\n    getUniqueHouseholdsForDisplay() {\n      if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n        return [];\n      }\n      const households = this.buildingData[this.selectedBuilding] || [];\n      if (!this.useHouseNameMode) {\n        // 一般模式：返回所有戶別\n        return households.filter(h => {\n          const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n          const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n          const houseTypeMatch = this.preFilterHouseType === null || h.houseType === this.preFilterHouseType;\n          return floorMatch && searchMatch && houseTypeMatch;\n        });\n      }\n      // useHouseNameMode：只返回唯一的戶別名稱\n      const uniqueHouseNames = new Set();\n      const uniqueHouseholds = [];\n      for (const household of households) {\n        // 搜尋篩選\n        const searchMatch = !this.searchTerm || household.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n        // 戶別類型篩選\n        const houseTypeMatch = this.preFilterHouseType === null || household.houseType === this.preFilterHouseType;\n        if (searchMatch && houseTypeMatch && !uniqueHouseNames.has(household.houseName)) {\n          uniqueHouseNames.add(household.houseName);\n          uniqueHouseholds.push(household);\n        }\n      }\n      return uniqueHouseholds;\n    }\n    // 新增：動態獲取文案的getter方法\n    get displayText() {\n      return {\n        unitType: this.useHouseNameMode ? '戶型' : '戶別',\n        placeholder: this.useHouseNameMode ? '請選擇戶型' : '請選擇戶別',\n        selectedPrefix: this.useHouseNameMode ? '已選擇戶型' : '已選擇戶別',\n        selectUnit: this.useHouseNameMode ? '選擇戶型' : '選擇戶別',\n        unitSelection: this.useHouseNameMode ? '戶型選擇' : '戶別選擇',\n        selectedCount: this.useHouseNameMode ? '個戶型' : '個戶',\n        searchPlaceholder: this.useHouseNameMode ? '搜尋戶型...' : '搜尋戶別...',\n        noResults: this.useHouseNameMode ? '找不到符合的戶型' : '找不到符合的戶別',\n        noAvailable: this.useHouseNameMode ? '此棟別沒有可選擇的戶型' : '此棟別沒有可選擇的戶別'\n      };\n    }\n    static {\n      this.ɵfac = function HouseholdBindingComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HouseholdBindingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NbDialogService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HouseholdBindingComponent,\n        selectors: [[\"app-household-binding\"]],\n        viewQuery: function HouseholdBindingComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.householdDialog = _t.first);\n          }\n        },\n        inputs: {\n          placeholder: \"placeholder\",\n          maxSelections: \"maxSelections\",\n          disabled: \"disabled\",\n          buildCaseId: \"buildCaseId\",\n          buildingData: \"buildingData\",\n          allowBatchSelect: \"allowBatchSelect\",\n          excludedHouseIds: \"excludedHouseIds\",\n          useHouseNameMode: \"useHouseNameMode\",\n          preFilterHouseType: \"preFilterHouseType\",\n          reminderText: \"reminderText\"\n        },\n        outputs: {\n          selectionChange: \"selectionChange\",\n          houseIdChange: \"houseIdChange\",\n          houseNameChange: \"houseNameChange\"\n        },\n        features: [i0.ɵɵProvidersFeature([{\n          provide: NG_VALUE_ACCESSOR,\n          useExisting: forwardRef(() => HouseholdBindingComponent),\n          multi: true\n        }]), i0.ɵɵNgOnChangesFeature],\n        decls: 13,\n        vars: 7,\n        consts: [[\"detailsPopover\", \"\"], [\"householdDialog\", \"\"], [1, \"household-binding-container\"], [\"class\", \"reminder-text-container\", \"style\", \"margin-bottom: 8px; padding: 8px 12px; background-color: #e3f2fd; border: 1px solid #bbdefb; border-radius: 4px; font-size: 0.875rem; color: #1976d2;\", 4, \"ngIf\"], [\"class\", \"selected-households-summary\", \"nbPopoverTrigger\", \"hover\", \"nbPopoverPlacement\", \"bottom\", 3, \"nbPopover\", 4, \"ngIf\"], [1, \"selector-container\"], [\"type\", \"button\", 1, \"selector-button\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"padding\", \"0.5rem 0.75rem\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"0.375rem\", \"background-color\", \"#fff\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [1, \"selector-text\"], [4, \"ngIf\"], [\"icon\", \"home-outline\", 1, \"chevron-icon\"], [1, \"reminder-text-container\", 2, \"margin-bottom\", \"8px\", \"padding\", \"8px 12px\", \"background-color\", \"#e3f2fd\", \"border\", \"1px solid #bbdefb\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"color\", \"#1976d2\"], [\"icon\", \"info-outline\", 2, \"margin-right\", \"6px\", \"color\", \"#1976d2\"], [\"nbPopoverTrigger\", \"hover\", \"nbPopoverPlacement\", \"bottom\", 1, \"selected-households-summary\", 3, \"nbPopover\"], [1, \"summary-header\"], [1, \"summary-info\"], [\"icon\", \"people-outline\", 1, \"text-primary\"], [1, \"summary-count\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"summary-content\"], [1, \"summary-text\"], [\"icon\", \"info-outline\", 1, \"info-icon\"], [\"icon\", \"loader-outline\", 1, \"spin\"], [1, \"details-popover-content\"], [1, \"details-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"details-list-item\"], [1, \"building-name\"], [1, \"house-name\"], [\"class\", \"floor-badge\", 4, \"ngIf\"], [1, \"floor-badge\"], [2, \"width\", \"95vw\", \"max-width\", \"1200px\", \"max-height\", \"90vh\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\", \"font-size\", \"1.5rem\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\", \"font-size\", \"1.25rem\"], [2, \"font-size\", \"0.875rem\", \"color\", \"#6c757d\"], [\"style\", \"margin-top: 12px; padding: 8px 12px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; font-size: 0.875rem; color: #856404;\", 4, \"ngIf\"], [2, \"padding\", \"0\", \"overflow\", \"hidden\"], [\"style\", \"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\", 4, \"ngIf\"], [\"style\", \"display: flex; height: 60vh; min-height: 400px;\", 4, \"ngIf\"], [2, \"padding\", \"16px\", \"border-top\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"font-size\", \"0.875rem\", \"color\", \"#495057\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\"], [\"icon\", \"checkmark-circle-outline\", 2, \"color\", \"#28a745\"], [\"style\", \"display: flex; align-items: center; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"display: flex; align-items: center; gap: 8px;\", 4, \"ngIf\"], [\"style\", \"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 4px;\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"gap\", \"8px\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"8px 16px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"8px 20px\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"checkmark-outline\"], [2, \"margin-top\", \"12px\", \"padding\", \"8px 12px\", \"background-color\", \"#fff3cd\", \"border\", \"1px solid #ffeaa7\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"color\", \"#856404\"], [\"icon\", \"alert-triangle-outline\", 2, \"margin-right\", \"6px\", \"color\", \"#856404\"], [2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"40px\"], [2, \"text-align\", \"center\", \"color\", \"#6c757d\"], [\"icon\", \"loader-outline\", 1, \"spin\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\"], [2, \"display\", \"flex\", \"height\", \"60vh\", \"min-height\", \"400px\"], [2, \"width\", \"300px\", \"border-right\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\", 3, \"background-color\", \"border-left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"600\", \"color\", \"#495057\"], [\"style\", \"background-color: #007bff; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\", 4, \"ngIf\"], [\"style\", \"background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\", 4, \"ngIf\"], [\"style\", \"display: flex; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 8px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 12px;\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"padding\", \"16px\", \"overflow-y\", \"auto\"], [\"style\", \"text-align: center; padding: 40px 20px; color: #6c757d;\", 4, \"ngIf\"], [\"style\", \"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"width\", \"100%\", \"text-align\", \"left\", \"padding\", \"12px 16px\", \"border\", \"none\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", 3, \"click\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"2px 6px\", \"border-radius\", \"10px\"], [2, \"background-color\", \"#007bff\", \"color\", \"white\", \"padding\", \"3px 8px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"icon\", \"home-outline\", 2, \"margin-right\", \"4px\", \"font-size\", \"0.7rem\"], [2, \"background-color\", \"#28a745\", \"color\", \"white\", \"padding\", \"3px 8px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"4px\", \"font-size\", \"0.7rem\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#28a745\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\"], [2, \"margin-top\", \"8px\"], [2, \"position\", \"relative\"], [\"type\", \"text\", 2, \"width\", \"100%\", \"padding\", \"6px 32px 6px 12px\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"outline\", \"none\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"placeholder\"], [\"icon\", \"search-outline\", 2, \"position\", \"absolute\", \"right\", \"10px\", \"top\", \"50%\", \"transform\", \"translateY(-50%)\", \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [\"style\", \"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\", 4, \"ngIf\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#dc3545\", \"margin-top\", \"4px\"], [2, \"margin-top\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"margin-bottom\", \"8px\"], [\"icon\", \"layers-outline\", 2, \"color\", \"#6c757d\", \"font-size\", \"1rem\"], [2, \"font-size\", \"0.875rem\", \"font-weight\", \"600\", \"color\", \"#495057\"], [\"type\", \"button\", \"style\", \"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"flex-wrap\", \"wrap\", \"gap\", \"4px\", \"max-height\", \"100px\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"padding: 6px 10px; border-radius: 3px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\", 3, \"background-color\", \"color\", \"border\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"font-size\", \"0.75rem\", \"color\", \"#007bff\", \"background\", \"none\", \"border\", \"none\", \"text-decoration\", \"underline\", \"cursor\", \"pointer\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"6px 10px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"500\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"white-space\", \"nowrap\", 3, \"click\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"3px\", \"font-size\", \"0.7rem\"], [2, \"font-size\", \"0.7rem\", \"opacity\", \"0.7\"], [2, \"text-align\", \"center\", \"padding\", \"40px 20px\", \"color\", \"#6c757d\"], [\"icon\", \"home-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(auto-fill, minmax(90px, 1fr))\", \"gap\", \"8px\"], [\"type\", \"button\", 2, \"padding\", \"8px 6px\", \"border-radius\", \"4px\", \"transition\", \"all 0.15s ease\", \"font-size\", \"0.75rem\", \"text-align\", \"center\", \"min-height\", \"55px\", \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\", \"justify-content\", \"center\", \"align-items\", \"center\", \"gap\", \"3px\", 3, \"click\", \"disabled\"], [2, \"font-weight\", \"600\", \"line-height\", \"1.2\", \"font-size\", \"0.85rem\"], [\"style\", \"font-size: 0.7rem; font-weight: 600; padding: 2px 6px; border-radius: 3px; display: inline-flex; align-items: center; justify-content: center; min-width: 22px;\", 3, \"background-color\", \"color\", \"border\", 4, \"ngIf\"], [\"style\", \"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\", 4, \"ngIf\"], [2, \"font-size\", \"0.7rem\", \"font-weight\", \"600\", \"padding\", \"2px 6px\", \"border-radius\", \"3px\", \"display\", \"inline-flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"22px\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"2px\", \"font-size\", \"0.6rem\"], [2, \"position\", \"absolute\", \"top\", \"-8px\", \"right\", \"-8px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border-radius\", \"50%\", \"width\", \"16px\", \"height\", \"16px\", \"font-size\", \"10px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [\"icon\", \"alert-circle-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [\"icon\", \"alert-circle-outline\", 2, \"color\", \"#ffc107\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\"], [2, \"background-color\", \"#007bff\", \"color\", \"white\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"style\", \"background-color: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600; margin-left: 4px;\", 4, \"ngIf\"], [2, \"background-color\", \"#28a745\", \"color\", \"white\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\", \"margin-left\", \"4px\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"trash-2-outline\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"refresh-outline\"]],\n        template: function HouseholdBindingComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2);\n            i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_Template, 4, 1, \"div\", 3)(2, HouseholdBindingComponent_div_2_Template, 12, 5, \"div\", 4);\n            i0.ɵɵelementStart(3, \"div\", 5)(4, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_Template_button_click_4_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.toggleDropdown());\n            });\n            i0.ɵɵelementStart(5, \"span\", 7);\n            i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_container_6_Template, 3, 0, \"ng-container\", 8)(7, HouseholdBindingComponent_ng_container_7_Template, 2, 1, \"ng-container\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(8, \"nb-icon\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(9, HouseholdBindingComponent_ng_template_9_Template, 3, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(11, HouseholdBindingComponent_ng_template_11_Template, 38, 14, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.reminderText);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedHouseIds.length > 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"disabled\", ctx.disabled || ctx.isLoading);\n            i0.ɵɵproperty(\"disabled\", ctx.disabled || ctx.isLoading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent, i1.NbIconComponent, i1.NbPopoverDirective],\n        styles: [\".household-binding-container[_ngcontent-%COMP%]{position:relative;width:100%}.household-binding-container[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]{margin-bottom:1rem;padding:1rem;background-color:#f8f9fa;border:1px solid #e9ecef;border-radius:.375rem}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.75rem}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500;color:#495057}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:.5rem;margin-bottom:.5rem}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]:last-child{margin-bottom:0}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%]{min-width:80px;font-weight:500;color:#495057;font-size:.875rem}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.25rem;flex:1}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.375rem;padding:.375rem .75rem;background-color:#e3f2fd;color:#1976d2;font-size:.75rem;border-radius:.25rem;border:1px solid #bbdefb;transition:all .2s ease}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover{background-color:#bbdefb}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start;gap:.1rem;line-height:1.2;min-width:0}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%]{font-weight:600;color:#0d47a1}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%]{font-size:.7rem;font-weight:600;color:#fff;background-color:#28a745;padding:.15rem .4rem;border-radius:.25rem;min-width:fit-content;text-align:center;letter-spacing:.02em}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]{background:#ffffffe6;border:1px solid #90caf9;padding:.1rem;margin:0;cursor:pointer;color:#0d47a1;border-radius:.25rem;width:1.2rem;height:1.2rem;display:flex;align-items:center;justify-content:center;transition:all .2s ease;flex-shrink:0}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover{background-color:#f44336;border-color:#f44336;color:#fff}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled:hover{background-color:#ffffffe6;border-color:#90caf9;color:#0d47a1}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%]{font-size:.75rem;line-height:1}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]{position:relative}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;justify-content:space-between;padding:.5rem .75rem;border:1px solid #ced4da;border-radius:.375rem;background-color:#fff;cursor:pointer;transition:all .15s ease}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled){border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:focus{outline:none;border-color:#80bdff;box-shadow:0 0 0 .2rem #007bff40}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%]{opacity:.6;cursor:not-allowed!important;background-color:#e9ecef}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%]{color:#495057;font-size:.875rem}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%]{transition:transform .15s ease;color:#6c757d}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon.rotated[_ngcontent-%COMP%]{transform:rotate(180deg)}.flat-badge[_ngcontent-%COMP%]{padding:4px 8px;border-radius:4px;font-size:.75rem;font-weight:600;display:inline-flex;align-items:center;gap:4px}.flat-badge.building[_ngcontent-%COMP%]{background-color:#007bff;color:#fff}.flat-badge.floor[_ngcontent-%COMP%]{background-color:#28a745;color:#fff}.flat-badge.search[_ngcontent-%COMP%]{background-color:#e9ecef;color:#6c757d}.flat-badge.selected[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1976d2;border:1px solid #bbdefb}.flat-household-tag[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.375rem;padding:.375rem .75rem;background-color:#e3f2fd;color:#1976d2;font-size:.75rem;border-radius:4px;border:1px solid #bbdefb;transition:background-color .2s ease}.flat-household-tag[_ngcontent-%COMP%]:hover{background-color:#bbdefb}.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start;gap:.1rem;line-height:1.2;min-width:0}.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%]{font-weight:600;color:#0d47a1}.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%]{font-size:.7rem;font-weight:600;color:#fff;background-color:#28a745;padding:.15rem .4rem;border-radius:4px;min-width:fit-content;text-align:center;letter-spacing:.02em}.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]{background:#fff;border:1px solid #90caf9;padding:.1rem;margin:0;cursor:pointer;color:#0d47a1;border-radius:4px;width:1.2rem;height:1.2rem;display:flex;align-items:center;justify-content:center;transition:all .2s ease;flex-shrink:0}.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover{background-color:#f44336;border-color:#f44336;color:#fff}.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled:hover{background-color:#fff;border-color:#90caf9;color:#0d47a1}.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%]{font-size:.75rem;line-height:1}.flat-button[_ngcontent-%COMP%]{border:1px solid;border-radius:4px;transition:all .2s ease;cursor:pointer}.flat-button.primary[_ngcontent-%COMP%]{background-color:#007bff;color:#fff;border-color:#007bff}.flat-button.primary[_ngcontent-%COMP%]:hover, .flat-button.primary.selected[_ngcontent-%COMP%]{background-color:#0056b3;border-color:#0056b3}.flat-button.success[_ngcontent-%COMP%]{background-color:#28a745;color:#fff;border-color:#28a745}.flat-button.success[_ngcontent-%COMP%]:hover, .flat-button.success.selected[_ngcontent-%COMP%]{background-color:#1e7e34;border-color:#1e7e34}.flat-button.light[_ngcontent-%COMP%]{background-color:#f8f9fa;color:#495057;border-color:#ced4da}.flat-button.light[_ngcontent-%COMP%]:hover{background-color:#e9ecef;border-color:#adb5bd}.flat-button.light.selected[_ngcontent-%COMP%]{background-color:#007bff;color:#fff;border-color:#007bff}.flat-button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.flat-button[_ngcontent-%COMP%]:disabled:hover{opacity:.6}@media (max-width: 768px){.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.25rem}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%]{min-width:auto}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]{width:100%}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%]{font-size:.6rem}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]{padding:.75rem}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%]{font-size:1rem}}@media (prefers-color-scheme: dark){.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]{background-color:#343a40;border-color:#495057;color:#f8f9fa}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]{color:#f8f9fa}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%]{color:#adb5bd}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]{background-color:#495057;color:#bbdefb;border-color:#6c757d}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover{background-color:#5a6268}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%]{color:#bbdefb}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%]{color:#90caf9;background-color:#0003}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]{background:#0000004d;border-color:#6c757d;color:#bbdefb}.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover{background-color:#f44336;border-color:#f44336;color:#fff}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]{background-color:#495057;border-color:#6c757d;color:#f8f9fa}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled){background-color:#5a6268}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%]{background-color:#6c757d}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%]{color:#f8f9fa}.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%]{color:#adb5bd}}.selected-households-summary[_ngcontent-%COMP%]{margin-bottom:1rem;padding:1rem;background-color:#f8f9fa;border:1px solid #e9ecef;border-radius:.375rem;cursor:pointer;transition:all .2s ease}.selected-households-summary[_ngcontent-%COMP%]:hover{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff1a}.selected-households-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.75rem}.selected-households-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.selected-households-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .summary-count[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500;color:#495057}.selected-households-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:.5rem;background-color:#fff;border-radius:.25rem}.selected-households-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]{color:#1976d2;font-size:.875rem;font-weight:500}.selected-households-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%]{color:#6c757d}.details-popover-content[_ngcontent-%COMP%]{max-height:300px;overflow:hidden;padding:.5rem}.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0;max-height:280px;overflow-y:auto}.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .details-list-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.5rem;border-bottom:1px solid #e9ecef}.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .details-list-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .details-list-item[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%]{font-weight:600;color:#495057;min-width:50px}.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .details-list-item[_ngcontent-%COMP%]   .house-name[_ngcontent-%COMP%]{color:#0d47a1}.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .details-list-item[_ngcontent-%COMP%]   .floor-badge[_ngcontent-%COMP%]{font-size:.7rem;font-weight:600;color:#fff;background-color:#28a745;padding:.15rem .4rem;border-radius:.25rem;margin-left:auto}\"]\n      });\n    }\n  }\n  return HouseholdBindingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}