{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as singleAxisHelper from '../../coord/single/singleAxisHelper.js';\nimport AxisView from './AxisView.js';\nimport { rectCoordAxisBuildSplitArea, rectCoordAxisHandleRemove } from './axisSplitHelper.js';\nvar axisBuilderAttrs = ['axisLine', 'axisTickLabel', 'axisName'];\nvar selfBuilderAttrs = ['splitArea', 'splitLine'];\nvar SingleAxisView = /** @class */function (_super) {\n  __extends(SingleAxisView, _super);\n  function SingleAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SingleAxisView.type;\n    _this.axisPointerClass = 'SingleAxisPointer';\n    return _this;\n  }\n  SingleAxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    var group = this.group;\n    group.removeAll();\n    var oldAxisGroup = this._axisGroup;\n    this._axisGroup = new graphic.Group();\n    var layout = singleAxisHelper.layout(axisModel);\n    var axisBuilder = new AxisBuilder(axisModel, layout);\n    zrUtil.each(axisBuilderAttrs, axisBuilder.add, axisBuilder);\n    group.add(this._axisGroup);\n    group.add(axisBuilder.getGroup());\n    zrUtil.each(selfBuilderAttrs, function (name) {\n      if (axisModel.get([name, 'show'])) {\n        axisElementBuilders[name](this, this.group, this._axisGroup, axisModel);\n      }\n    }, this);\n    graphic.groupTransition(oldAxisGroup, this._axisGroup, axisModel);\n    _super.prototype.render.call(this, axisModel, ecModel, api, payload);\n  };\n  SingleAxisView.prototype.remove = function () {\n    rectCoordAxisHandleRemove(this);\n  };\n  SingleAxisView.type = 'singleAxis';\n  return SingleAxisView;\n}(AxisView);\nvar axisElementBuilders = {\n  splitLine: function (axisView, group, axisGroup, axisModel) {\n    var axis = axisModel.axis;\n    if (axis.scale.isBlank()) {\n      return;\n    }\n    var splitLineModel = axisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var lineWidth = lineStyleModel.get('width');\n    var gridRect = axisModel.coordinateSystem.getRect();\n    var isHorizontal = axis.isHorizontal();\n    var splitLines = [];\n    var lineCount = 0;\n    var ticksCoords = axis.getTicksCoords({\n      tickModel: splitLineModel\n    });\n    var p1 = [];\n    var p2 = [];\n    for (var i = 0; i < ticksCoords.length; ++i) {\n      var tickCoord = axis.toGlobalCoord(ticksCoords[i].coord);\n      if (isHorizontal) {\n        p1[0] = tickCoord;\n        p1[1] = gridRect.y;\n        p2[0] = tickCoord;\n        p2[1] = gridRect.y + gridRect.height;\n      } else {\n        p1[0] = gridRect.x;\n        p1[1] = tickCoord;\n        p2[0] = gridRect.x + gridRect.width;\n        p2[1] = tickCoord;\n      }\n      var line = new graphic.Line({\n        shape: {\n          x1: p1[0],\n          y1: p1[1],\n          x2: p2[0],\n          y2: p2[1]\n        },\n        silent: true\n      });\n      graphic.subPixelOptimizeLine(line.shape, lineWidth);\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(line);\n    }\n    var lineStyle = lineStyleModel.getLineStyle(['color']);\n    for (var i = 0; i < splitLines.length; ++i) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length]\n        }, lineStyle),\n        silent: true\n      }));\n    }\n  },\n  splitArea: function (axisView, group, axisGroup, axisModel) {\n    rectCoordAxisBuildSplitArea(axisView, axisGroup, axisModel, axisModel);\n  }\n};\nexport default SingleAxisView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "AxisBuilder", "graphic", "singleAxisHelper", "AxisView", "rectCoordAxisBuildSplitArea", "rectCoordAxisHandleRemove", "axisBuilderAttrs", "selfBuilderAttrs", "SingleAxisView", "_super", "_this", "apply", "arguments", "type", "axisPointerClass", "prototype", "render", "axisModel", "ecModel", "api", "payload", "group", "removeAll", "oldAxisGroup", "_axisGroup", "Group", "layout", "axisBuilder", "each", "add", "getGroup", "name", "get", "axisElementBuilders", "groupTransition", "call", "remove", "splitLine", "axisView", "axisGroup", "axis", "scale", "isBlank", "splitLineModel", "getModel", "lineStyleModel", "lineColors", "Array", "lineWidth", "gridRect", "coordinateSystem", "getRect", "isHorizontal", "splitLines", "lineCount", "ticksCoords", "getTicksCoords", "tickModel", "p1", "p2", "i", "length", "tickCoord", "toGlobalCoord", "coord", "y", "height", "x", "width", "line", "Line", "shape", "x1", "y1", "x2", "y2", "silent", "subPixelOptimizeLine", "colorIndex", "push", "lineStyle", "getLineStyle", "mergePath", "style", "defaults", "stroke", "splitArea"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/component/axis/SingleAxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as singleAxisHelper from '../../coord/single/singleAxisHelper.js';\nimport AxisView from './AxisView.js';\nimport { rectCoordAxisBuildSplitArea, rectCoordAxisHandleRemove } from './axisSplitHelper.js';\nvar axisBuilderAttrs = ['axisLine', 'axisTickLabel', 'axisName'];\nvar selfBuilderAttrs = ['splitArea', 'splitLine'];\nvar SingleAxisView = /** @class */function (_super) {\n  __extends(SingleAxisView, _super);\n  function SingleAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SingleAxisView.type;\n    _this.axisPointerClass = 'SingleAxisPointer';\n    return _this;\n  }\n  SingleAxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    var group = this.group;\n    group.removeAll();\n    var oldAxisGroup = this._axisGroup;\n    this._axisGroup = new graphic.Group();\n    var layout = singleAxisHelper.layout(axisModel);\n    var axisBuilder = new AxisBuilder(axisModel, layout);\n    zrUtil.each(axisBuilderAttrs, axisBuilder.add, axisBuilder);\n    group.add(this._axisGroup);\n    group.add(axisBuilder.getGroup());\n    zrUtil.each(selfBuilderAttrs, function (name) {\n      if (axisModel.get([name, 'show'])) {\n        axisElementBuilders[name](this, this.group, this._axisGroup, axisModel);\n      }\n    }, this);\n    graphic.groupTransition(oldAxisGroup, this._axisGroup, axisModel);\n    _super.prototype.render.call(this, axisModel, ecModel, api, payload);\n  };\n  SingleAxisView.prototype.remove = function () {\n    rectCoordAxisHandleRemove(this);\n  };\n  SingleAxisView.type = 'singleAxis';\n  return SingleAxisView;\n}(AxisView);\nvar axisElementBuilders = {\n  splitLine: function (axisView, group, axisGroup, axisModel) {\n    var axis = axisModel.axis;\n    if (axis.scale.isBlank()) {\n      return;\n    }\n    var splitLineModel = axisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var lineWidth = lineStyleModel.get('width');\n    var gridRect = axisModel.coordinateSystem.getRect();\n    var isHorizontal = axis.isHorizontal();\n    var splitLines = [];\n    var lineCount = 0;\n    var ticksCoords = axis.getTicksCoords({\n      tickModel: splitLineModel\n    });\n    var p1 = [];\n    var p2 = [];\n    for (var i = 0; i < ticksCoords.length; ++i) {\n      var tickCoord = axis.toGlobalCoord(ticksCoords[i].coord);\n      if (isHorizontal) {\n        p1[0] = tickCoord;\n        p1[1] = gridRect.y;\n        p2[0] = tickCoord;\n        p2[1] = gridRect.y + gridRect.height;\n      } else {\n        p1[0] = gridRect.x;\n        p1[1] = tickCoord;\n        p2[0] = gridRect.x + gridRect.width;\n        p2[1] = tickCoord;\n      }\n      var line = new graphic.Line({\n        shape: {\n          x1: p1[0],\n          y1: p1[1],\n          x2: p2[0],\n          y2: p2[1]\n        },\n        silent: true\n      });\n      graphic.subPixelOptimizeLine(line.shape, lineWidth);\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(line);\n    }\n    var lineStyle = lineStyleModel.getLineStyle(['color']);\n    for (var i = 0; i < splitLines.length; ++i) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length]\n        }, lineStyle),\n        silent: true\n      }));\n    }\n  },\n  splitArea: function (axisView, group, axisGroup, axisModel) {\n    rectCoordAxisBuildSplitArea(axisView, axisGroup, axisModel, axisModel);\n  }\n};\nexport default SingleAxisView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,gBAAgB,MAAM,wCAAwC;AAC1E,OAAOC,QAAQ,MAAM,eAAe;AACpC,SAASC,2BAA2B,EAAEC,yBAAyB,QAAQ,sBAAsB;AAC7F,IAAIC,gBAAgB,GAAG,CAAC,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC;AAChE,IAAIC,gBAAgB,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC;AACjD,IAAIC,cAAc,GAAG,aAAa,UAAUC,MAAM,EAAE;EAClDX,SAAS,CAACU,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAAA,EAAG;IACxB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,cAAc,CAACK,IAAI;IAChCH,KAAK,CAACI,gBAAgB,GAAG,mBAAmB;IAC5C,OAAOJ,KAAK;EACd;EACAF,cAAc,CAACO,SAAS,CAACC,MAAM,GAAG,UAAUC,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAC5E,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtBA,KAAK,CAACC,SAAS,CAAC,CAAC;IACjB,IAAIC,YAAY,GAAG,IAAI,CAACC,UAAU;IAClC,IAAI,CAACA,UAAU,GAAG,IAAIvB,OAAO,CAACwB,KAAK,CAAC,CAAC;IACrC,IAAIC,MAAM,GAAGxB,gBAAgB,CAACwB,MAAM,CAACT,SAAS,CAAC;IAC/C,IAAIU,WAAW,GAAG,IAAI3B,WAAW,CAACiB,SAAS,EAAES,MAAM,CAAC;IACpD3B,MAAM,CAAC6B,IAAI,CAACtB,gBAAgB,EAAEqB,WAAW,CAACE,GAAG,EAAEF,WAAW,CAAC;IAC3DN,KAAK,CAACQ,GAAG,CAAC,IAAI,CAACL,UAAU,CAAC;IAC1BH,KAAK,CAACQ,GAAG,CAACF,WAAW,CAACG,QAAQ,CAAC,CAAC,CAAC;IACjC/B,MAAM,CAAC6B,IAAI,CAACrB,gBAAgB,EAAE,UAAUwB,IAAI,EAAE;MAC5C,IAAId,SAAS,CAACe,GAAG,CAAC,CAACD,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE;QACjCE,mBAAmB,CAACF,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAACV,KAAK,EAAE,IAAI,CAACG,UAAU,EAAEP,SAAS,CAAC;MACzE;IACF,CAAC,EAAE,IAAI,CAAC;IACRhB,OAAO,CAACiC,eAAe,CAACX,YAAY,EAAE,IAAI,CAACC,UAAU,EAAEP,SAAS,CAAC;IACjER,MAAM,CAACM,SAAS,CAACC,MAAM,CAACmB,IAAI,CAAC,IAAI,EAAElB,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,CAAC;EACtE,CAAC;EACDZ,cAAc,CAACO,SAAS,CAACqB,MAAM,GAAG,YAAY;IAC5C/B,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EACDG,cAAc,CAACK,IAAI,GAAG,YAAY;EAClC,OAAOL,cAAc;AACvB,CAAC,CAACL,QAAQ,CAAC;AACX,IAAI8B,mBAAmB,GAAG;EACxBI,SAAS,EAAE,SAAAA,CAAUC,QAAQ,EAAEjB,KAAK,EAAEkB,SAAS,EAAEtB,SAAS,EAAE;IAC1D,IAAIuB,IAAI,GAAGvB,SAAS,CAACuB,IAAI;IACzB,IAAIA,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,CAAC,EAAE;MACxB;IACF;IACA,IAAIC,cAAc,GAAG1B,SAAS,CAAC2B,QAAQ,CAAC,WAAW,CAAC;IACpD,IAAIC,cAAc,GAAGF,cAAc,CAACC,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAIE,UAAU,GAAGD,cAAc,CAACb,GAAG,CAAC,OAAO,CAAC;IAC5Cc,UAAU,GAAGA,UAAU,YAAYC,KAAK,GAAGD,UAAU,GAAG,CAACA,UAAU,CAAC;IACpE,IAAIE,SAAS,GAAGH,cAAc,CAACb,GAAG,CAAC,OAAO,CAAC;IAC3C,IAAIiB,QAAQ,GAAGhC,SAAS,CAACiC,gBAAgB,CAACC,OAAO,CAAC,CAAC;IACnD,IAAIC,YAAY,GAAGZ,IAAI,CAACY,YAAY,CAAC,CAAC;IACtC,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,WAAW,GAAGf,IAAI,CAACgB,cAAc,CAAC;MACpCC,SAAS,EAAEd;IACb,CAAC,CAAC;IACF,IAAIe,EAAE,GAAG,EAAE;IACX,IAAIC,EAAE,GAAG,EAAE;IACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,WAAW,CAACM,MAAM,EAAE,EAAED,CAAC,EAAE;MAC3C,IAAIE,SAAS,GAAGtB,IAAI,CAACuB,aAAa,CAACR,WAAW,CAACK,CAAC,CAAC,CAACI,KAAK,CAAC;MACxD,IAAIZ,YAAY,EAAE;QAChBM,EAAE,CAAC,CAAC,CAAC,GAAGI,SAAS;QACjBJ,EAAE,CAAC,CAAC,CAAC,GAAGT,QAAQ,CAACgB,CAAC;QAClBN,EAAE,CAAC,CAAC,CAAC,GAAGG,SAAS;QACjBH,EAAE,CAAC,CAAC,CAAC,GAAGV,QAAQ,CAACgB,CAAC,GAAGhB,QAAQ,CAACiB,MAAM;MACtC,CAAC,MAAM;QACLR,EAAE,CAAC,CAAC,CAAC,GAAGT,QAAQ,CAACkB,CAAC;QAClBT,EAAE,CAAC,CAAC,CAAC,GAAGI,SAAS;QACjBH,EAAE,CAAC,CAAC,CAAC,GAAGV,QAAQ,CAACkB,CAAC,GAAGlB,QAAQ,CAACmB,KAAK;QACnCT,EAAE,CAAC,CAAC,CAAC,GAAGG,SAAS;MACnB;MACA,IAAIO,IAAI,GAAG,IAAIpE,OAAO,CAACqE,IAAI,CAAC;QAC1BC,KAAK,EAAE;UACLC,EAAE,EAAEd,EAAE,CAAC,CAAC,CAAC;UACTe,EAAE,EAAEf,EAAE,CAAC,CAAC,CAAC;UACTgB,EAAE,EAAEf,EAAE,CAAC,CAAC,CAAC;UACTgB,EAAE,EAAEhB,EAAE,CAAC,CAAC;QACV,CAAC;QACDiB,MAAM,EAAE;MACV,CAAC,CAAC;MACF3E,OAAO,CAAC4E,oBAAoB,CAACR,IAAI,CAACE,KAAK,EAAEvB,SAAS,CAAC;MACnD,IAAI8B,UAAU,GAAGxB,SAAS,EAAE,GAAGR,UAAU,CAACe,MAAM;MAChDR,UAAU,CAACyB,UAAU,CAAC,GAAGzB,UAAU,CAACyB,UAAU,CAAC,IAAI,EAAE;MACrDzB,UAAU,CAACyB,UAAU,CAAC,CAACC,IAAI,CAACV,IAAI,CAAC;IACnC;IACA,IAAIW,SAAS,GAAGnC,cAAc,CAACoC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC;IACtD,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,UAAU,CAACQ,MAAM,EAAE,EAAED,CAAC,EAAE;MAC1CvC,KAAK,CAACQ,GAAG,CAAC5B,OAAO,CAACiF,SAAS,CAAC7B,UAAU,CAACO,CAAC,CAAC,EAAE;QACzCuB,KAAK,EAAEpF,MAAM,CAACqF,QAAQ,CAAC;UACrBC,MAAM,EAAEvC,UAAU,CAACc,CAAC,GAAGd,UAAU,CAACe,MAAM;QAC1C,CAAC,EAAEmB,SAAS,CAAC;QACbJ,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EACDU,SAAS,EAAE,SAAAA,CAAUhD,QAAQ,EAAEjB,KAAK,EAAEkB,SAAS,EAAEtB,SAAS,EAAE;IAC1Db,2BAA2B,CAACkC,QAAQ,EAAEC,SAAS,EAAEtB,SAAS,EAAEA,SAAS,CAAC;EACxE;AACF,CAAC;AACD,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}