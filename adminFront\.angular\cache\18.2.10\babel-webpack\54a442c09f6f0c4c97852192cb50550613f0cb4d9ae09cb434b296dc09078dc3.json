{"ast": null, "code": "import { AsyncScheduler } from './AsyncScheduler';\nexport class AnimationFrameScheduler extends AsyncScheduler {\n  flush(action) {\n    this._active = true;\n    const flushId = this._scheduled;\n    this._scheduled = undefined;\n    const {\n      actions\n    } = this;\n    let error;\n    action = action || actions.shift();\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while ((action = actions[0]) && action.id === flushId && actions.shift());\n    this._active = false;\n    if (error) {\n      while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}\n//# sourceMappingURL=AnimationFrameScheduler.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}