# 父元件使用範例

## 在父元件的模板中使用 SpaceComponent

```html
<ngx-space (submitSpace)="onSpaceSubmit($event)"></ngx-space>
```

## 在父元件的 TypeScript 中處理事件

```typescript
import { Component } from '@angular/core';
import { SpaceService } from 'src/services/api/services';
import { MessageService } from 'src/app/shared/services/message.service';
import { SaveSpaceRequest } from 'src/services/api/models';
import { tap } from 'rxjs';

@Component({
  selector: 'app-parent',
  templateUrl: './parent.component.html'
})
export class ParentComponent {
  
  constructor(
    private spaceService: SpaceService,
    private message: MessageService
  ) {}

  onSpaceSubmit(event: { action: 'create' | 'update', data: SaveSpaceRequest, ref: any }): void {
    console.log('收到子元件提交事件:', event);
    
    // 執行 API 調用
    this.spaceService.apiSpaceSaveSpacePost$Json({ body: event.data }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.message.showSucessMSG(event.action === 'update' ? '更新空間成功' : '新增空間成功');
          event.ref.close(); // 關閉對話框
          
          // 可以在這裡執行其他父元件的業務邏輯
          this.handleSpaceSubmitSuccess(event.action, event.data);
        } else {
          this.message.showErrorMSG(response.Message || '儲存空間失敗');
        }
      })
    ).subscribe();
  }

  private handleSpaceSubmitSuccess(action: 'create' | 'update', data: SaveSpaceRequest): void {
    // 處理成功後的業務邏輯
    if (action === 'create') {
      console.log('新增空間成功，執行額外邏輯');
    } else {
      console.log('更新空間成功，執行額外邏輯');
    }
    
    // 例如：重新載入相關資料、更新其他元件狀態等
  }
}
```

## 修改說明

### 子元件 (SpaceComponent) 的變更：

1. **加入 Output 事件發射器**：
   ```typescript
   @Output() submitSpace = new EventEmitter<{ action: 'create' | 'update', data: SaveSpaceRequest, ref: any }>();
   ```

2. **修改 onSubmit 方法**：
   - 移除直接的 API 調用
   - 發射事件給父元件
   - 傳遞動作類型 ('create' 或 'update')、資料和對話框參考

### 優點：

1. **關注點分離**：子元件專注於表單驗證和 UI 邏輯，父元件處理業務邏輯
2. **可重用性**：SpaceComponent 可以在不同的父元件中使用，並有不同的處理邏輯
3. **彈性**：父元件可以在 API 調用前後執行額外的業務邏輯
4. **測試友善**：更容易進行單元測試
