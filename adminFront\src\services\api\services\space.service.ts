/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { apiSpaceDeleteSpacePost$Json } from '../fn/space/api-space-delete-space-post-json';
import { ApiSpaceDeleteSpacePost$Json$Params } from '../fn/space/api-space-delete-space-post-json';
import { apiSpaceDeleteSpacePost$Plain } from '../fn/space/api-space-delete-space-post-plain';
import { ApiSpaceDeleteSpacePost$Plain$Params } from '../fn/space/api-space-delete-space-post-plain';
import { apiSpaceGetSpaceByIdPost$Json } from '../fn/space/api-space-get-space-by-id-post-json';
import { ApiSpaceGetSpaceByIdPost$Json$Params } from '../fn/space/api-space-get-space-by-id-post-json';
import { apiSpaceGetSpaceByIdPost$Plain } from '../fn/space/api-space-get-space-by-id-post-plain';
import { ApiSpaceGetSpaceByIdPost$Plain$Params } from '../fn/space/api-space-get-space-by-id-post-plain';
import { apiSpaceGetSpaceListForTemplatePost$Json } from '../fn/space/api-space-get-space-list-for-template-post-json';
import { ApiSpaceGetSpaceListForTemplatePost$Json$Params } from '../fn/space/api-space-get-space-list-for-template-post-json';
import { apiSpaceGetSpaceListForTemplatePost$Plain } from '../fn/space/api-space-get-space-list-for-template-post-plain';
import { ApiSpaceGetSpaceListForTemplatePost$Plain$Params } from '../fn/space/api-space-get-space-list-for-template-post-plain';
import { apiSpaceGetSpaceListPost$Json } from '../fn/space/api-space-get-space-list-post-json';
import { ApiSpaceGetSpaceListPost$Json$Params } from '../fn/space/api-space-get-space-list-post-json';
import { apiSpaceGetSpaceListPost$Plain } from '../fn/space/api-space-get-space-list-post-plain';
import { ApiSpaceGetSpaceListPost$Plain$Params } from '../fn/space/api-space-get-space-list-post-plain';
import { apiSpaceSaveSpacePost$Json } from '../fn/space/api-space-save-space-post-json';
import { ApiSpaceSaveSpacePost$Json$Params } from '../fn/space/api-space-save-space-post-json';
import { apiSpaceSaveSpacePost$Plain } from '../fn/space/api-space-save-space-post-plain';
import { ApiSpaceSaveSpacePost$Plain$Params } from '../fn/space/api-space-save-space-post-plain';
import { GetSpaceListResponseListResponseBase } from '../models/get-space-list-response-list-response-base';
import { GetSpaceListResponseResponseBase } from '../models/get-space-list-response-response-base';
import { StringResponseBase } from '../models/string-response-base';

@Injectable({ providedIn: 'root' })
export class SpaceService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `apiSpaceGetSpaceListPost()` */
  static readonly ApiSpaceGetSpaceListPostPath = '/api/Space/GetSpaceList';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiSpaceGetSpaceListPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceListPost$Plain$Response(params?: ApiSpaceGetSpaceListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseListResponseBase>> {
    return apiSpaceGetSpaceListPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiSpaceGetSpaceListPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceListPost$Plain(params?: ApiSpaceGetSpaceListPost$Plain$Params, context?: HttpContext): Observable<GetSpaceListResponseListResponseBase> {
    return this.apiSpaceGetSpaceListPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetSpaceListResponseListResponseBase>): GetSpaceListResponseListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiSpaceGetSpaceListPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceListPost$Json$Response(params?: ApiSpaceGetSpaceListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseListResponseBase>> {
    return apiSpaceGetSpaceListPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiSpaceGetSpaceListPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceListPost$Json(params?: ApiSpaceGetSpaceListPost$Json$Params, context?: HttpContext): Observable<GetSpaceListResponseListResponseBase> {
    return this.apiSpaceGetSpaceListPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetSpaceListResponseListResponseBase>): GetSpaceListResponseListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiSpaceGetSpaceByIdPost()` */
  static readonly ApiSpaceGetSpaceByIdPostPath = '/api/Space/GetSpaceById';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiSpaceGetSpaceByIdPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceByIdPost$Plain$Response(params?: ApiSpaceGetSpaceByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseResponseBase>> {
    return apiSpaceGetSpaceByIdPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiSpaceGetSpaceByIdPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceByIdPost$Plain(params?: ApiSpaceGetSpaceByIdPost$Plain$Params, context?: HttpContext): Observable<GetSpaceListResponseResponseBase> {
    return this.apiSpaceGetSpaceByIdPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetSpaceListResponseResponseBase>): GetSpaceListResponseResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiSpaceGetSpaceByIdPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceByIdPost$Json$Response(params?: ApiSpaceGetSpaceByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseResponseBase>> {
    return apiSpaceGetSpaceByIdPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiSpaceGetSpaceByIdPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceByIdPost$Json(params?: ApiSpaceGetSpaceByIdPost$Json$Params, context?: HttpContext): Observable<GetSpaceListResponseResponseBase> {
    return this.apiSpaceGetSpaceByIdPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetSpaceListResponseResponseBase>): GetSpaceListResponseResponseBase => r.body)
    );
  }

  /** Path part for operation `apiSpaceSaveSpacePost()` */
  static readonly ApiSpaceSaveSpacePostPath = '/api/Space/SaveSpace';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiSpaceSaveSpacePost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceSaveSpacePost$Plain$Response(params?: ApiSpaceSaveSpacePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiSpaceSaveSpacePost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiSpaceSaveSpacePost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceSaveSpacePost$Plain(params?: ApiSpaceSaveSpacePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiSpaceSaveSpacePost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiSpaceSaveSpacePost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceSaveSpacePost$Json$Response(params?: ApiSpaceSaveSpacePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiSpaceSaveSpacePost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiSpaceSaveSpacePost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceSaveSpacePost$Json(params?: ApiSpaceSaveSpacePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiSpaceSaveSpacePost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /** Path part for operation `apiSpaceDeleteSpacePost()` */
  static readonly ApiSpaceDeleteSpacePostPath = '/api/Space/DeleteSpace';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiSpaceDeleteSpacePost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceDeleteSpacePost$Plain$Response(params?: ApiSpaceDeleteSpacePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiSpaceDeleteSpacePost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiSpaceDeleteSpacePost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceDeleteSpacePost$Plain(params?: ApiSpaceDeleteSpacePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiSpaceDeleteSpacePost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiSpaceDeleteSpacePost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceDeleteSpacePost$Json$Response(params?: ApiSpaceDeleteSpacePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiSpaceDeleteSpacePost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiSpaceDeleteSpacePost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceDeleteSpacePost$Json(params?: ApiSpaceDeleteSpacePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiSpaceDeleteSpacePost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /** Path part for operation `apiSpaceGetSpaceListForTemplatePost()` */
  static readonly ApiSpaceGetSpaceListForTemplatePostPath = '/api/Space/GetSpaceListForTemplate';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiSpaceGetSpaceListForTemplatePost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceListForTemplatePost$Plain$Response(params?: ApiSpaceGetSpaceListForTemplatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseListResponseBase>> {
    return apiSpaceGetSpaceListForTemplatePost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiSpaceGetSpaceListForTemplatePost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceListForTemplatePost$Plain(params?: ApiSpaceGetSpaceListForTemplatePost$Plain$Params, context?: HttpContext): Observable<GetSpaceListResponseListResponseBase> {
    return this.apiSpaceGetSpaceListForTemplatePost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetSpaceListResponseListResponseBase>): GetSpaceListResponseListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiSpaceGetSpaceListForTemplatePost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceListForTemplatePost$Json$Response(params?: ApiSpaceGetSpaceListForTemplatePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseListResponseBase>> {
    return apiSpaceGetSpaceListForTemplatePost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiSpaceGetSpaceListForTemplatePost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiSpaceGetSpaceListForTemplatePost$Json(params?: ApiSpaceGetSpaceListForTemplatePost$Json$Params, context?: HttpContext): Observable<GetSpaceListResponseListResponseBase> {
    return this.apiSpaceGetSpaceListForTemplatePost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetSpaceListResponseListResponseBase>): GetSpaceListResponseListResponseBase => r.body)
    );
  }

}
