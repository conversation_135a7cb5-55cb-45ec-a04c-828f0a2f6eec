{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport env from 'zrender/lib/core/env.js';\nimport { enableClassExtend, enableClassCheck } from '../util/clazz.js';\nimport { AreaStyleMixin } from './mixin/areaStyle.js';\nimport TextStyleMixin from './mixin/textStyle.js';\nimport { LineStyleMixin } from './mixin/lineStyle.js';\nimport { ItemStyleMixin } from './mixin/itemStyle.js';\nimport { mixin, clone, merge } from 'zrender/lib/core/util.js';\nvar Model = /** @class */function () {\n  function Model(option, parentModel, ecModel) {\n    this.parentModel = parentModel;\n    this.ecModel = ecModel;\n    this.option = option;\n    // Simple optimization\n    // if (this.init) {\n    //     if (arguments.length <= 4) {\n    //         this.init(option, parentModel, ecModel, extraOpt);\n    //     }\n    //     else {\n    //         this.init.apply(this, arguments);\n    //     }\n    // }\n  }\n  Model.prototype.init = function (option, parentModel, ecModel) {\n    var rest = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n      rest[_i - 3] = arguments[_i];\n    }\n  };\n  /**\r\n   * Merge the input option to me.\r\n   */\n  Model.prototype.mergeOption = function (option, ecModel) {\n    merge(this.option, option, true);\n  };\n  // `path` can be 'a.b.c', so the return value type have to be `ModelOption`\n  // TODO: TYPE strict key check?\n  // get(path: string | string[], ignoreParent?: boolean): ModelOption;\n  Model.prototype.get = function (path, ignoreParent) {\n    if (path == null) {\n      return this.option;\n    }\n    return this._doGet(this.parsePath(path), !ignoreParent && this.parentModel);\n  };\n  Model.prototype.getShallow = function (key, ignoreParent) {\n    var option = this.option;\n    var val = option == null ? option : option[key];\n    if (val == null && !ignoreParent) {\n      var parentModel = this.parentModel;\n      if (parentModel) {\n        // FIXME:TS do not know how to make it works\n        val = parentModel.getShallow(key);\n      }\n    }\n    return val;\n  };\n  // `path` can be 'a.b.c', so the return value type have to be `Model<ModelOption>`\n  // getModel(path: string | string[], parentModel?: Model): Model;\n  // TODO 'a.b.c' is deprecated\n  Model.prototype.getModel = function (path, parentModel) {\n    var hasPath = path != null;\n    var pathFinal = hasPath ? this.parsePath(path) : null;\n    var obj = hasPath ? this._doGet(pathFinal) : this.option;\n    parentModel = parentModel || this.parentModel && this.parentModel.getModel(this.resolveParentPath(pathFinal));\n    return new Model(obj, parentModel, this.ecModel);\n  };\n  /**\r\n   * If model has option\r\n   */\n  Model.prototype.isEmpty = function () {\n    return this.option == null;\n  };\n  Model.prototype.restoreData = function () {};\n  // Pending\n  Model.prototype.clone = function () {\n    var Ctor = this.constructor;\n    return new Ctor(clone(this.option));\n  };\n  // setReadOnly(properties): void {\n  // clazzUtil.setReadOnly(this, properties);\n  // }\n  // If path is null/undefined, return null/undefined.\n  Model.prototype.parsePath = function (path) {\n    if (typeof path === 'string') {\n      return path.split('.');\n    }\n    return path;\n  };\n  // Resolve path for parent. Perhaps useful when parent use a different property.\n  // Default to be a identity resolver.\n  // Can be modified to a different resolver.\n  Model.prototype.resolveParentPath = function (path) {\n    return path;\n  };\n  // FIXME:TS check whether put this method here\n  Model.prototype.isAnimationEnabled = function () {\n    if (!env.node && this.option) {\n      if (this.option.animation != null) {\n        return !!this.option.animation;\n      } else if (this.parentModel) {\n        return this.parentModel.isAnimationEnabled();\n      }\n    }\n  };\n  Model.prototype._doGet = function (pathArr, parentModel) {\n    var obj = this.option;\n    if (!pathArr) {\n      return obj;\n    }\n    for (var i = 0; i < pathArr.length; i++) {\n      // Ignore empty\n      if (!pathArr[i]) {\n        continue;\n      }\n      // obj could be number/string/... (like 0)\n      obj = obj && typeof obj === 'object' ? obj[pathArr[i]] : null;\n      if (obj == null) {\n        break;\n      }\n    }\n    if (obj == null && parentModel) {\n      obj = parentModel._doGet(this.resolveParentPath(pathArr), parentModel.parentModel);\n    }\n    return obj;\n  };\n  return Model;\n}();\n;\n// Enable Model.extend.\nenableClassExtend(Model);\nenableClassCheck(Model);\nmixin(Model, LineStyleMixin);\nmixin(Model, ItemStyleMixin);\nmixin(Model, AreaStyleMixin);\nmixin(Model, TextStyleMixin);\nexport default Model;", "map": {"version": 3, "names": ["env", "enableClassExtend", "enableClassCheck", "AreaStyleMixin", "TextStyleMixin", "LineStyleMixin", "ItemStyleMixin", "mixin", "clone", "merge", "Model", "option", "parentModel", "ecModel", "prototype", "init", "rest", "_i", "arguments", "length", "mergeOption", "get", "path", "ignoreParent", "_doGet", "parsePath", "getShallow", "key", "val", "getModel", "<PERSON><PERSON><PERSON>", "pathFinal", "obj", "resolveParentPath", "isEmpty", "restoreData", "Ctor", "constructor", "split", "isAnimationEnabled", "node", "animation", "pathArr", "i"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/model/Model.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport env from 'zrender/lib/core/env.js';\nimport { enableClassExtend, enableClassCheck } from '../util/clazz.js';\nimport { AreaStyleMixin } from './mixin/areaStyle.js';\nimport TextStyleMixin from './mixin/textStyle.js';\nimport { LineStyleMixin } from './mixin/lineStyle.js';\nimport { ItemStyleMixin } from './mixin/itemStyle.js';\nimport { mixin, clone, merge } from 'zrender/lib/core/util.js';\nvar Model = /** @class */function () {\n  function Model(option, parentModel, ecModel) {\n    this.parentModel = parentModel;\n    this.ecModel = ecModel;\n    this.option = option;\n    // Simple optimization\n    // if (this.init) {\n    //     if (arguments.length <= 4) {\n    //         this.init(option, parentModel, ecModel, extraOpt);\n    //     }\n    //     else {\n    //         this.init.apply(this, arguments);\n    //     }\n    // }\n  }\n  Model.prototype.init = function (option, parentModel, ecModel) {\n    var rest = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n      rest[_i - 3] = arguments[_i];\n    }\n  };\n  /**\r\n   * Merge the input option to me.\r\n   */\n  Model.prototype.mergeOption = function (option, ecModel) {\n    merge(this.option, option, true);\n  };\n  // `path` can be 'a.b.c', so the return value type have to be `ModelOption`\n  // TODO: TYPE strict key check?\n  // get(path: string | string[], ignoreParent?: boolean): ModelOption;\n  Model.prototype.get = function (path, ignoreParent) {\n    if (path == null) {\n      return this.option;\n    }\n    return this._doGet(this.parsePath(path), !ignoreParent && this.parentModel);\n  };\n  Model.prototype.getShallow = function (key, ignoreParent) {\n    var option = this.option;\n    var val = option == null ? option : option[key];\n    if (val == null && !ignoreParent) {\n      var parentModel = this.parentModel;\n      if (parentModel) {\n        // FIXME:TS do not know how to make it works\n        val = parentModel.getShallow(key);\n      }\n    }\n    return val;\n  };\n  // `path` can be 'a.b.c', so the return value type have to be `Model<ModelOption>`\n  // getModel(path: string | string[], parentModel?: Model): Model;\n  // TODO 'a.b.c' is deprecated\n  Model.prototype.getModel = function (path, parentModel) {\n    var hasPath = path != null;\n    var pathFinal = hasPath ? this.parsePath(path) : null;\n    var obj = hasPath ? this._doGet(pathFinal) : this.option;\n    parentModel = parentModel || this.parentModel && this.parentModel.getModel(this.resolveParentPath(pathFinal));\n    return new Model(obj, parentModel, this.ecModel);\n  };\n  /**\r\n   * If model has option\r\n   */\n  Model.prototype.isEmpty = function () {\n    return this.option == null;\n  };\n  Model.prototype.restoreData = function () {};\n  // Pending\n  Model.prototype.clone = function () {\n    var Ctor = this.constructor;\n    return new Ctor(clone(this.option));\n  };\n  // setReadOnly(properties): void {\n  // clazzUtil.setReadOnly(this, properties);\n  // }\n  // If path is null/undefined, return null/undefined.\n  Model.prototype.parsePath = function (path) {\n    if (typeof path === 'string') {\n      return path.split('.');\n    }\n    return path;\n  };\n  // Resolve path for parent. Perhaps useful when parent use a different property.\n  // Default to be a identity resolver.\n  // Can be modified to a different resolver.\n  Model.prototype.resolveParentPath = function (path) {\n    return path;\n  };\n  // FIXME:TS check whether put this method here\n  Model.prototype.isAnimationEnabled = function () {\n    if (!env.node && this.option) {\n      if (this.option.animation != null) {\n        return !!this.option.animation;\n      } else if (this.parentModel) {\n        return this.parentModel.isAnimationEnabled();\n      }\n    }\n  };\n  Model.prototype._doGet = function (pathArr, parentModel) {\n    var obj = this.option;\n    if (!pathArr) {\n      return obj;\n    }\n    for (var i = 0; i < pathArr.length; i++) {\n      // Ignore empty\n      if (!pathArr[i]) {\n        continue;\n      }\n      // obj could be number/string/... (like 0)\n      obj = obj && typeof obj === 'object' ? obj[pathArr[i]] : null;\n      if (obj == null) {\n        break;\n      }\n    }\n    if (obj == null && parentModel) {\n      obj = parentModel._doGet(this.resolveParentPath(pathArr), parentModel.parentModel);\n    }\n    return obj;\n  };\n  return Model;\n}();\n;\n// Enable Model.extend.\nenableClassExtend(Model);\nenableClassCheck(Model);\nmixin(Model, LineStyleMixin);\nmixin(Model, ItemStyleMixin);\nmixin(Model, AreaStyleMixin);\nmixin(Model, TextStyleMixin);\nexport default Model;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,GAAG,MAAM,yBAAyB;AACzC,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,kBAAkB;AACtE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAOC,cAAc,MAAM,sBAAsB;AACjD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,KAAK,EAAEC,KAAK,EAAEC,KAAK,QAAQ,0BAA0B;AAC9D,IAAIC,KAAK,GAAG,aAAa,YAAY;EACnC,SAASA,KAAKA,CAACC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IAC3C,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EACAD,KAAK,CAACI,SAAS,CAACC,IAAI,GAAG,UAAUJ,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IAC7D,IAAIG,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC5CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC9B;EACF,CAAC;EACD;AACF;AACA;EACEP,KAAK,CAACI,SAAS,CAACM,WAAW,GAAG,UAAUT,MAAM,EAAEE,OAAO,EAAE;IACvDJ,KAAK,CAAC,IAAI,CAACE,MAAM,EAAEA,MAAM,EAAE,IAAI,CAAC;EAClC,CAAC;EACD;EACA;EACA;EACAD,KAAK,CAACI,SAAS,CAACO,GAAG,GAAG,UAAUC,IAAI,EAAEC,YAAY,EAAE;IAClD,IAAID,IAAI,IAAI,IAAI,EAAE;MAChB,OAAO,IAAI,CAACX,MAAM;IACpB;IACA,OAAO,IAAI,CAACa,MAAM,CAAC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,EAAE,CAACC,YAAY,IAAI,IAAI,CAACX,WAAW,CAAC;EAC7E,CAAC;EACDF,KAAK,CAACI,SAAS,CAACY,UAAU,GAAG,UAAUC,GAAG,EAAEJ,YAAY,EAAE;IACxD,IAAIZ,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIiB,GAAG,GAAGjB,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGA,MAAM,CAACgB,GAAG,CAAC;IAC/C,IAAIC,GAAG,IAAI,IAAI,IAAI,CAACL,YAAY,EAAE;MAChC,IAAIX,WAAW,GAAG,IAAI,CAACA,WAAW;MAClC,IAAIA,WAAW,EAAE;QACf;QACAgB,GAAG,GAAGhB,WAAW,CAACc,UAAU,CAACC,GAAG,CAAC;MACnC;IACF;IACA,OAAOC,GAAG;EACZ,CAAC;EACD;EACA;EACA;EACAlB,KAAK,CAACI,SAAS,CAACe,QAAQ,GAAG,UAAUP,IAAI,EAAEV,WAAW,EAAE;IACtD,IAAIkB,OAAO,GAAGR,IAAI,IAAI,IAAI;IAC1B,IAAIS,SAAS,GAAGD,OAAO,GAAG,IAAI,CAACL,SAAS,CAACH,IAAI,CAAC,GAAG,IAAI;IACrD,IAAIU,GAAG,GAAGF,OAAO,GAAG,IAAI,CAACN,MAAM,CAACO,SAAS,CAAC,GAAG,IAAI,CAACpB,MAAM;IACxDC,WAAW,GAAGA,WAAW,IAAI,IAAI,CAACA,WAAW,IAAI,IAAI,CAACA,WAAW,CAACiB,QAAQ,CAAC,IAAI,CAACI,iBAAiB,CAACF,SAAS,CAAC,CAAC;IAC7G,OAAO,IAAIrB,KAAK,CAACsB,GAAG,EAAEpB,WAAW,EAAE,IAAI,CAACC,OAAO,CAAC;EAClD,CAAC;EACD;AACF;AACA;EACEH,KAAK,CAACI,SAAS,CAACoB,OAAO,GAAG,YAAY;IACpC,OAAO,IAAI,CAACvB,MAAM,IAAI,IAAI;EAC5B,CAAC;EACDD,KAAK,CAACI,SAAS,CAACqB,WAAW,GAAG,YAAY,CAAC,CAAC;EAC5C;EACAzB,KAAK,CAACI,SAAS,CAACN,KAAK,GAAG,YAAY;IAClC,IAAI4B,IAAI,GAAG,IAAI,CAACC,WAAW;IAC3B,OAAO,IAAID,IAAI,CAAC5B,KAAK,CAAC,IAAI,CAACG,MAAM,CAAC,CAAC;EACrC,CAAC;EACD;EACA;EACA;EACA;EACAD,KAAK,CAACI,SAAS,CAACW,SAAS,GAAG,UAAUH,IAAI,EAAE;IAC1C,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOA,IAAI,CAACgB,KAAK,CAAC,GAAG,CAAC;IACxB;IACA,OAAOhB,IAAI;EACb,CAAC;EACD;EACA;EACA;EACAZ,KAAK,CAACI,SAAS,CAACmB,iBAAiB,GAAG,UAAUX,IAAI,EAAE;IAClD,OAAOA,IAAI;EACb,CAAC;EACD;EACAZ,KAAK,CAACI,SAAS,CAACyB,kBAAkB,GAAG,YAAY;IAC/C,IAAI,CAACvC,GAAG,CAACwC,IAAI,IAAI,IAAI,CAAC7B,MAAM,EAAE;MAC5B,IAAI,IAAI,CAACA,MAAM,CAAC8B,SAAS,IAAI,IAAI,EAAE;QACjC,OAAO,CAAC,CAAC,IAAI,CAAC9B,MAAM,CAAC8B,SAAS;MAChC,CAAC,MAAM,IAAI,IAAI,CAAC7B,WAAW,EAAE;QAC3B,OAAO,IAAI,CAACA,WAAW,CAAC2B,kBAAkB,CAAC,CAAC;MAC9C;IACF;EACF,CAAC;EACD7B,KAAK,CAACI,SAAS,CAACU,MAAM,GAAG,UAAUkB,OAAO,EAAE9B,WAAW,EAAE;IACvD,IAAIoB,GAAG,GAAG,IAAI,CAACrB,MAAM;IACrB,IAAI,CAAC+B,OAAO,EAAE;MACZ,OAAOV,GAAG;IACZ;IACA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,CAACvB,MAAM,EAAEwB,CAAC,EAAE,EAAE;MACvC;MACA,IAAI,CAACD,OAAO,CAACC,CAAC,CAAC,EAAE;QACf;MACF;MACA;MACAX,GAAG,GAAGA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,GAAGA,GAAG,CAACU,OAAO,CAACC,CAAC,CAAC,CAAC,GAAG,IAAI;MAC7D,IAAIX,GAAG,IAAI,IAAI,EAAE;QACf;MACF;IACF;IACA,IAAIA,GAAG,IAAI,IAAI,IAAIpB,WAAW,EAAE;MAC9BoB,GAAG,GAAGpB,WAAW,CAACY,MAAM,CAAC,IAAI,CAACS,iBAAiB,CAACS,OAAO,CAAC,EAAE9B,WAAW,CAACA,WAAW,CAAC;IACpF;IACA,OAAOoB,GAAG;EACZ,CAAC;EACD,OAAOtB,KAAK;AACd,CAAC,CAAC,CAAC;AACH;AACA;AACAT,iBAAiB,CAACS,KAAK,CAAC;AACxBR,gBAAgB,CAACQ,KAAK,CAAC;AACvBH,KAAK,CAACG,KAAK,EAAEL,cAAc,CAAC;AAC5BE,KAAK,CAACG,KAAK,EAAEJ,cAAc,CAAC;AAC5BC,KAAK,CAACG,KAAK,EAAEP,cAAc,CAAC;AAC5BI,KAAK,CAACG,KAAK,EAAEN,cAAc,CAAC;AAC5B,eAAeM,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}