{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiHouseGetHousePreorderRequirementPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiHouseGetHousePreorderRequirementPost$Json.PATH, 'post');\n  if (params) {}\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiHouseGetHousePreorderRequirementPost$Json.PATH = '/api/House/GetHousePreorderRequirement';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiHouseGetHousePreorderRequirementPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\house\\api-house-get-house-preorder-requirement-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { TblHousePreorderDetailListResponseBase } from '../../models/tbl-house-preorder-detail-list-response-base';\r\n\r\nexport interface ApiHouseGetHousePreorderRequirementPost$Json$Params {\r\n}\r\n\r\nexport function apiHouseGetHousePreorderRequirementPost$Json(http: HttpClient, rootUrl: string, params?: ApiHouseGetHousePreorderRequirementPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TblHousePreorderDetailListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiHouseGetHousePreorderRequirementPost$Json.PATH, 'post');\r\n  if (params) {\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<TblHousePreorderDetailListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiHouseGetHousePreorderRequirementPost$Json.PATH = '/api/House/GetHousePreorderRequirement';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAOtD,OAAM,SAAUC,4CAA4CA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA4D,EAAEC,OAAqB;EACjL,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,4CAA4C,CAACM,IAAI,EAAE,MAAM,CAAC;EACjG,IAAIH,MAAM,EAAE,CACZ;EAEA,OAAOF,IAAI,CAACM,OAAO,CACjBF,EAAE,CAACG,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEN;EAAO,CAAE,CAAC,CACjE,CAACO,IAAI,CACJd,MAAM,CAAEe,CAAM,IAA6BA,CAAC,YAAYhB,YAAY,CAAC,EACrEE,GAAG,CAAEc,CAAoB,IAAI;IAC3B,OAAOA,CAA+D;EACxE,CAAC,CAAC,CACH;AACH;AAEAZ,4CAA4C,CAACM,IAAI,GAAG,wCAAwC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}