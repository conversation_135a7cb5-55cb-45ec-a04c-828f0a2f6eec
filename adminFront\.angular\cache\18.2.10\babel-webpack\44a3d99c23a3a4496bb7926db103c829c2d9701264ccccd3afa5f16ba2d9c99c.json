{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../shared/components/household-binding/household-binding.component\";\nfunction DetailContentManagementSalesAccountComponent_div_28_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_28_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 28);\n    i0.ɵɵelement(2, \"path\", 56);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_28_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u627E\\u5230 \", ctx_r1.filteredArrListFormItemReq.length, \" / \", ctx_r1.arrListFormItemReq.length, \" \\u500B\\u9805\\u76EE \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 13)(2, \"div\", 48)(3, \"div\", 49);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 50);\n    i0.ɵɵelement(5, \"path\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"input\", 52);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_div_28_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchQuery, $event) || (ctx_r1.searchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function DetailContentManagementSalesAccountComponent_div_28_Template_input_input_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, DetailContentManagementSalesAccountComponent_div_28_button_7_Template, 3, 0, \"button\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_div_28_div_8_Template, 2, 2, \"div\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchQuery);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"div\", 60)(3, \"div\", 61)(4, \"span\", 20);\n    i0.ɵɵtext(5, \"\\u5C55\\u958B\\u63A7\\u5236:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 62)(7, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_29_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.expandAll());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 64);\n    i0.ɵɵelement(9, \"path\", 65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_29_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.collapseAll());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 64);\n    i0.ɵɵelement(12, \"path\", 67);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"div\", 68)(14, \"div\", 69);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(15, \"svg\", 70);\n    i0.ɵɵelement(16, \"path\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" \\u4F7F\\u7528\\u53F3\\u5074\\u6D6E\\u52D5\\u6309\\u9215\\u5FEB\\u901F\\u64CD\\u4F5C \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 73);\n    i0.ɵɵelement(3, \"path\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\", 75);\n    i0.ɵɵtext(5, \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 76);\n    i0.ɵɵtext(7, \"\\u8ACB\\u5617\\u8A66\\u8ABF\\u6574\\u641C\\u5C0B\\u95DC\\u9375\\u5B57\\u6216\\u6E05\\u9664\\u641C\\u5C0B\\u689D\\u4EF6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_30_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(9, \" \\u6E05\\u9664\\u641C\\u5C0B \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_31_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const idx_r9 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.scrollToItem(idx_r9 - 1));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 91);\n    i0.ɵɵelement(2, \"path\", 92);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_31_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const idx_r9 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.scrollToItem(idx_r9 + 1));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 91);\n    i0.ɵɵelement(2, \"path\", 94);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 147);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r1.getCurrentImage(formItemReq_r7)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_button_8_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.prevImage(formItemReq_r7);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 28);\n    i0.ɵɵelement(2, \"path\", 149);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 150);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_button_9_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.nextImage(formItemReq_r7);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 28);\n    i0.ɵɵelement(2, \"path\", 151);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 152);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", (formItemReq_r7.currentImageIndex || 0) + 1, \" / \", formItemReq_r7.CMatrialUrl.length, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_div_11_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_div_11_button_1_Template_button_click_0_listener() {\n      const i_r16 = i0.ɵɵrestoreView(_r15).index;\n      const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openImageModal(formItemReq_r7, i_r16));\n    });\n    i0.ɵɵelement(1, \"img\", 156);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r17 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵclassProp(\"border-blue-500\", i_r16 === (formItemReq_r7.currentImageIndex || 0))(\"border-gray-300\", i_r16 !== (formItemReq_r7.currentImageIndex || 0))(\"ring-2\", i_r16 === (formItemReq_r7.currentImageIndex || 0))(\"ring-blue-200\", i_r16 === (formItemReq_r7.currentImageIndex || 0));\n    i0.ɵɵproperty(\"title\", \"\\u9EDE\\u9078\\u653E\\u5927\\u7B2C \" + (i_r16 + 1) + \" \\u5F35\\u5716\\u7247\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 10, imageUrl_r17), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 153);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_div_11_button_1_Template, 3, 12, \"button\", 154);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r7.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 136);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const formItemReq_r7 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openImageModal(formItemReq_r7));\n    });\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_img_2_Template, 2, 3, \"img\", 137);\n    i0.ɵɵelementStart(3, \"div\", 138)(4, \"div\", 139)(5, \"div\", 140);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 141);\n    i0.ɵɵelement(7, \"path\", 142);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_button_8_Template, 3, 0, \"button\", 143)(9, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_button_9_Template, 3, 0, \"button\", 144)(10, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_div_10_Template, 2, 2, \"div\", 145);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_div_11_Template, 2, 1, \"div\", 146);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentImage(formItemReq_r7));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 157);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 158);\n    i0.ɵɵelement(2, \"path\", 159);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 160);\n    i0.ɵɵtext(4, \"\\u7121\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_nb_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 161);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r18);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r18.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_50_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"div\", 165);\n    i0.ɵɵelement(2, \"img\", 166)(3, \"div\", 167);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 168);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_50_div_1_Template_input_blur_4_listener($event) {\n      const i_r21 = i0.ɵɵrestoreView(_r20).index;\n      const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.renameFile($event, i_r21, formItemReq_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 169);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_50_div_1_Template_button_click_5_listener() {\n      const picture_r22 = i0.ɵɵrestoreView(_r20).$implicit;\n      const formItemReq_r7 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.removeImage(picture_r22.id, formItemReq_r7));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 170);\n    i0.ɵɵelement(7, \"path\", 171);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" \\u522A\\u9664\\u5716\\u7247 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_12_0;\n    const picture_r22 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r22.data, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r22.name)(\"disabled\", (tmp_11_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", (tmp_12_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_12_0 !== undefined ? tmp_12_0 : false);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 162);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_50_div_1_Template, 9, 4, \"div\", 163);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r7.listPictures);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 172)(1, \"label\", 173);\n    i0.ɵɵtext(2, \"\\u9810\\u8A2D\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 165);\n    i0.ɵɵelement(4, \"img\", 174);\n    i0.ɵɵpipe(5, \"base64Image\");\n    i0.ɵɵelement(6, \"div\", 167);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(5, 1, formItemReq_r7.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 175);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 176);\n    i0.ɵɵelement(2, \"path\", 177);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 178);\n    i0.ɵɵtext(4, \"\\u7121\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 179);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 180);\n    i0.ɵɵelement(2, \"path\", 181);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 182);\n    i0.ɵɵtext(4, \"\\u5C1A\\u7121\\u6236\\u5225\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_label_1_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 193);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const remark_r24 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r7.selectedRemarkType[remark_r24], $event) || (formItemReq_r7.selectedRemarkType[remark_r24] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const remark_r24 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckboxRemarkChange($event, remark_r24, formItemReq_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r24 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r7.selectedRemarkType[remark_r24]);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 190);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_label_1_nb_checkbox_1_Template, 1, 2, \"nb-checkbox\", 191);\n    i0.ɵɵelementStart(2, \"span\", 192);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const remark_r24 = ctx.$implicit;\n    const formItemReq_r7 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.selectedRemarkType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(remark_r24);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 188);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_label_1_Template, 4, 2, \"label\", 189);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 179);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 180);\n    i0.ɵɵelement(2, \"path\", 194);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 182);\n    i0.ɵɵtext(4, \"\\u5C1A\\u7121\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 183)(1, \"div\", 106);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 184);\n    i0.ɵɵelement(3, \"path\", 185);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h5\", 186);\n    i0.ɵɵtext(5, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_Template, 2, 1, \"div\", 187)(7, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_ng_template_7_Template, 5, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noRemarkOptions_r25 = i0.ɵɵreference(8);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.CRemarkTypeOptions && ctx_r1.CRemarkTypeOptions.length > 0)(\"ngIfElse\", noRemarkOptions_r25);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 96)(2, \"div\", 97)(3, \"div\", 98)(4, \"div\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 99);\n    i0.ɵɵelement(6, \"path\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"label\", 101);\n    i0.ɵɵtext(8, \"\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_Template, 12, 5, \"div\", 102)(10, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_10_Template, 5, 0, \"div\", 103);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 104)(12, \"div\", 105)(13, \"div\", 106);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 99);\n    i0.ɵɵelement(15, \"path\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"label\", 101);\n    i0.ɵɵtext(17, \"\\u57FA\\u672C\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 98)(19, \"div\", 107)(20, \"label\", 108);\n    i0.ɵɵtext(21, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 109)(23, \"span\", 110);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 111);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r7.CItemName, $event) || (formItemReq_r7.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 107)(27, \"label\", 108);\n    i0.ɵɵtext(28, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 33)(30, \"input\", 112);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r7.CRequireAnswer, $event) || (formItemReq_r7.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 107)(32, \"label\", 108);\n    i0.ɵɵtext(33, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"nb-select\", 113);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_nb_select_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r7.selectedCUiType, $event) || (formItemReq_r7.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_nb_select_selectedChange_34_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeSelectCUiType(formItemReq_r7));\n    });\n    i0.ɵɵtemplate(35, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_nb_option_35_Template, 2, 2, \"nb-option\", 114);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(36, \"div\", 97)(37, \"div\", 98)(38, \"div\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(39, \"svg\", 99);\n    i0.ɵɵelement(40, \"path\", 115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"label\", 101);\n    i0.ɵɵtext(42, \"\\u6982\\u5FF5\\u8A2D\\u8A08\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const inputFile_r19 = i0.ɵɵreference(49);\n      return i0.ɵɵresetView(inputFile_r19.click());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(44, \"svg\", 117);\n    i0.ɵɵelement(45, \"path\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(46, \"span\");\n    i0.ɵɵtext(47, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"input\", 119, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_input_change_48_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.detectFiles($event, formItemReq_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(50, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_50_Template, 2, 1, \"div\", 120)(51, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_51_Template, 7, 3, \"div\", 121)(52, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_52_Template, 5, 0, \"div\", 122);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 123)(54, \"div\", 33)(55, \"div\", 124);\n    i0.ɵɵelement(56, \"div\", 125);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 126)(58, \"span\", 127);\n    i0.ɵɵtext(59, \"\\u8A2D\\u5B9A\\u9078\\u9805\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(60, \"div\", 128)(61, \"div\", 129)(62, \"div\", 106);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(63, \"svg\", 130);\n    i0.ɵɵelement(64, \"path\", 131);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(65, \"h5\", 132);\n    i0.ɵɵtext(66, \"\\u9069\\u7528\\u6236\\u578B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"app-household-binding\", 133);\n    i0.ɵɵlistener(\"selectionChange\", function DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_app_household_binding_selectionChange_67_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const formItemReq_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onHouseholdSelectionChange(ctx_r1.extractHouseholdCodes($event), formItemReq_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(68, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_68_Template, 5, 0, \"div\", 134);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(69, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_Template, 9, 2, \"div\", 135);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_15_0;\n    let tmp_19_0;\n    let tmp_27_0;\n    const ctx_r25 = i0.ɵɵnextContext();\n    const formItemReq_r7 = ctx_r25.$implicit;\n    const idx_r9 = ctx_r25.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CMatrialUrl && formItemReq_r7.CMatrialUrl.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r7.CMatrialUrl || formItemReq_r7.CMatrialUrl.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"for\", \"CItemName_\" + idx_r9);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r7.CName, \"-\", formItemReq_r7.CPart, \"-\", formItemReq_r7.CLocation, \": \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"CItemName_\" + idx_r9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r7.CItemName);\n    i0.ɵɵproperty(\"disabled\", (tmp_11_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"cRequireAnswer_\" + idx_r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"id\", \"cRequireAnswer_\" + idx_r9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r7.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r7.selectedCUiType.value === 3 || ((tmp_15_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_15_0 !== undefined ? tmp_15_0 : false));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"uiType_\" + idx_r9);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"uiType_\" + idx_r9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r7.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_19_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_19_0 !== undefined ? tmp_19_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.CUiTypeOptions);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.listPictures && formItemReq_r7.listPictures.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.CDesignFileUrl && (!formItemReq_r7.listPictures || formItemReq_r7.listPictures.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r7.CDesignFileUrl && (!formItemReq_r7.listPictures || formItemReq_r7.listPictures.length === 0));\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"buildingData\", ctx_r1.buildingData)(\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u9069\\u7528\\u6236\\u578B\")(\"disabled\", (tmp_27_0 = ctx_r1.listFormItem == null ? null : ctx_r1.listFormItem.CIsLock) !== null && tmp_27_0 !== undefined ? tmp_27_0 : false)(\"allowBatchSelect\", true)(\"ngModel\", formItemReq_r7.selectedHouseholdsCached)(\"useHouseNameMode\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.houseHoldList.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r7.selectedCUiType.value === 3);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 78)(2, \"div\", 79)(3, \"div\", 5)(4, \"div\", 13)(5, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_31_Template_button_click_5_listener() {\n      const formItemReq_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleItemCollapse(formItemReq_r7));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 81);\n    i0.ɵɵelement(7, \"path\", 67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"div\", 82);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 83)(11, \"h4\", 84);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 57);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 13)(16, \"span\", 85);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 86);\n    i0.ɵɵtemplate(19, DetailContentManagementSalesAccountComponent_ng_container_31_button_19_Template, 3, 0, \"button\", 87)(20, DetailContentManagementSalesAccountComponent_ng_container_31_button_20_Template, 3, 0, \"button\", 88);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(21, DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template, 70, 30, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r7 = ctx.$implicit;\n    const idx_r9 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"form-item-\" + idx_r9);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"title\", formItemReq_r7.isCollapsed ? \"\\u5C55\\u958B\\u9805\\u76EE\" : \"\\u6536\\u5408\\u9805\\u76EE\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"rotate-180\", formItemReq_r7.isCollapsed);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", idx_r9 + 1, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r7.CName, \"-\", formItemReq_r7.CPart, \"-\", formItemReq_r7.CLocation, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u9805\\u76EE\\u7DE8\\u865F #\", idx_r9 + 1, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (formItemReq_r7.selectedCUiType == null ? null : formItemReq_r7.selectedCUiType.label) || \"\\u672A\\u8A2D\\u5B9A\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", idx_r9 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", idx_r9 < ctx_r1.arrListFormItemReq.length - 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r7.isCollapsed);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_47_div_1_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 209);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r28 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r1.getCurrentImage(formItemReq_r28)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_47_div_1_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 210);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_47_div_1_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const formItemReq_r28 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prevImageModal(formItemReq_r28));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 211);\n    i0.ɵɵelement(2, \"path\", 149);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_47_div_1_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 212);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_47_div_1_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const formItemReq_r28 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextImageModal(formItemReq_r28));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 211);\n    i0.ɵɵelement(2, \"path\", 151);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_47_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 213)(1, \"div\", 13);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 117);\n    i0.ɵɵelement(3, \"path\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\", 214);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r28 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", (formItemReq_r28.currentImageIndex || 0) + 1, \" / \", formItemReq_r28.CMatrialUrl.length, \"\");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_47_div_1_div_16_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 218);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_47_div_1_div_16_button_2_Template_button_click_0_listener() {\n      const i_r32 = i0.ɵɵrestoreView(_r31).index;\n      const formItemReq_r28 = i0.ɵɵnextContext(3).$implicit;\n      return i0.ɵɵresetView(formItemReq_r28.currentImageIndex = i_r32);\n    });\n    i0.ɵɵelement(1, \"img\", 219);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r33 = ctx.$implicit;\n    const i_r32 = ctx.index;\n    const formItemReq_r28 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵclassProp(\"border-white\", i_r32 === (formItemReq_r28.currentImageIndex || 0))(\"border-gray-400\", i_r32 !== (formItemReq_r28.currentImageIndex || 0))(\"ring-3\", i_r32 === (formItemReq_r28.currentImageIndex || 0))(\"ring-white\", i_r32 === (formItemReq_r28.currentImageIndex || 0))(\"ring-opacity-50\", i_r32 === (formItemReq_r28.currentImageIndex || 0));\n    i0.ɵɵproperty(\"title\", \"\\u8DF3\\u81F3\\u7B2C \" + (i_r32 + 1) + \" \\u5F35\\u5716\\u7247\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 12, imageUrl_r33), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_47_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 215)(1, \"div\", 216);\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_47_div_1_div_16_button_2_Template, 3, 14, \"button\", 217);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r28 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r28.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_47_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 196);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_47_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const formItemReq_r28 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeImageModal(formItemReq_r28));\n    })(\"keydown\", function DetailContentManagementSalesAccountComponent_ng_container_47_div_1_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const formItemReq_r28 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onKeydown($event, formItemReq_r28));\n    });\n    i0.ɵɵelementStart(1, \"button\", 197);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_47_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const formItemReq_r28 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeImageModal(formItemReq_r28));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 198);\n    i0.ɵɵelement(3, \"path\", 199);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\", 200);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_47_div_1_Template_div_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(5, \"div\", 201);\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_47_div_1_img_6_Template, 2, 3, \"img\", 202)(7, DetailContentManagementSalesAccountComponent_ng_container_47_div_1_button_7_Template, 3, 0, \"button\", 203)(8, DetailContentManagementSalesAccountComponent_ng_container_47_div_1_button_8_Template, 3, 0, \"button\", 204);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, DetailContentManagementSalesAccountComponent_ng_container_47_div_1_div_9_Template, 6, 2, \"div\", 205);\n    i0.ɵɵelementStart(10, \"div\", 206)(11, \"div\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 28);\n    i0.ɵɵelement(13, \"path\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(14, \"span\", 207);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(16, DetailContentManagementSalesAccountComponent_ng_container_47_div_1_div_16_Template, 3, 1, \"div\", 208);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r28 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentImage(formItemReq_r28));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r28.CMatrialUrl && formItemReq_r28.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r28.CMatrialUrl && formItemReq_r28.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r28.CMatrialUrl && formItemReq_r28.CMatrialUrl.length > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate3(\"\", formItemReq_r28.CName, \"-\", formItemReq_r28.CPart, \"-\", formItemReq_r28.CLocation, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r28.CMatrialUrl && formItemReq_r28.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_47_div_1_Template, 17, 8, \"div\", 195);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r28 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r28.isModalOpen);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent__svg_svg_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 220);\n    i0.ɵɵelement(1, \"path\", 221);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent__svg_svg_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 40);\n    i0.ɵɵelement(1, \"path\", 222);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_button_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 223);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_button_55_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.copyToNewForm());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 40);\n    i0.ɵɵelement(2, \"path\", 224);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵtext(4, \" \\u8907\\u88FD\\u5230\\u65B0\\u8868\\u55AE \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isSubmitting);\n  }\n}\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService, _houseService, cdr) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this._houseService = _houseService;\n    this.cdr = cdr;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    // 通知類型選項映射\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: EnumHouseType.地主戶\n    }, {\n      label: '銷售戶',\n      value: EnumHouseType.銷售戶\n    }];\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.isSubmitting = false;\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    // 新增：戶別選擇器相關屬性\n    this.buildingData = {}; // 存放建築物戶別資料\n    this.listFormItem = null;\n    this.isNew = true;\n    this.arrListFormItemReq = [];\n    this.filteredArrListFormItemReq = [];\n    this.searchQuery = '';\n  }\n  // 動態獲取標題文字\n  get dynamicTitle() {\n    const option = this.cNoticeTypeOptions.find(option => option.value === this.typeContentManagementSalesAccount.CNoticeType);\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\n  }\n  // 設置通知類型（可供外部調用）\n  setCNoticeType(noticeType) {\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\n      // 同時設定 CFormType 以保持一致性\n      this.typeContentManagementSalesAccount.CFormType = noticeType;\n    }\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId > 0) {\n          this.getListRegularNoticeFileHouseHold();\n        } else {\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\n          console.error('Invalid buildCaseId:', this.buildCaseId);\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\n          this.goBack();\n        }\n      }\n    });\n    // 處理查詢參數中的戶型\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['houseType']) {\n        const houseType = +queryParams['houseType'];\n        this.setCNoticeType(houseType);\n      }\n    });\n  }\n  ngOnDestroy() {\n    // 確保在組件銷毀時恢復body的滾動\n    document.body.style.overflow = 'auto';\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      formItemReq.isModalOpen = true;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\n  extractHouseholdCodes(households) {\n    if (!households || !Array.isArray(households)) {\n      return [];\n    }\n    return households.map(h => h.code || h);\n  }\n  // 新增：處理戶別選擇變更\n  onHouseholdSelectionChange(selectedHouseholds, formItemReq) {\n    // 重置所有戶別選擇狀態\n    Object.keys(formItemReq.selectedItems).forEach(key => {\n      formItemReq.selectedItems[key] = false;\n    });\n    // 設置選中的戶別\n    selectedHouseholds.forEach(household => {\n      formItemReq.selectedItems[household] = true;\n    });\n    // 更新全選狀態\n    formItemReq.allSelected = this.houseHoldList.length > 0 && this.houseHoldList.every(item => formItemReq.selectedItems[item]);\n    // 更新緩存\n    this.updateSelectedHouseholdsCache(formItemReq);\n  }\n  // 新增：取得已選戶別數組\n  getSelectedHouseholds(formItemReq) {\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\n  }\n  // 新增：更新已選戶別緩存\n  updateSelectedHouseholdsCache(formItemReq) {\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\n  }\n  // 新增：更新所有項目的緩存\n  updateAllSelectedHouseholdsCache() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(formItemReq => {\n        this.updateSelectedHouseholdsCache(formItemReq);\n      });\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            currentImageIndex: 0,\n            isModalOpen: false,\n            isCollapsed: true,\n            // 新項目默認收合\n            CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64).filter(url => url != null) : []\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n        // 初始化過濾列表\n        this.updateFilteredList();\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem?.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0,\n              isModalOpen: false,\n              isCollapsed: true,\n              // 現有項目默認收合\n              selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\n            };\n          });\n          // 初始化過濾列表\n          this.updateFilteredList();\n          // 手動觸發變更檢測\n          this.cdr.detectChanges();\n        } else {\n          // 當無資料時，載入材料清單供新增使用\n          this.getMaterialList();\n        }\n        // 初始化所有項目的緩存\n        this.updateAllSelectedHouseholdsCache();\n        // 最終觸發變更檢測\n        this.cdr.detectChanges();\n      } else {\n        console.error('getListFormItem failed:', res);\n      }\n    })).subscribe({\n      error: error => {\n        console.error('getListFormItem error:', error);\n      }\n    });\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    // 設置提交狀態\n    this.isSubmitting = true;\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem?.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      // 滾動到第一個有錯誤的項目\n      this.scrollToFirstErrorItem();\n      this.isSubmitting = false;\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe({\n      next: res => {\n        this.isSubmitting = false;\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          // this.getListFormItem()\n          this.goBack();\n        }\n      },\n      error: error => {\n        this.isSubmitting = false;\n        console.error('Save error:', error);\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe({\n      next: res => {\n        this.isSubmitting = false;\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          // this.getListFormItem()\n          this.goBack();\n        }\n      },\n      error: error => {\n        this.isSubmitting = false;\n        console.error('Create error:', error);\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  /**\n   * 複製當前表單到新表單\n   */\n  copyToNewForm() {\n    // 先取得當前有效的材料清單\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        // 建立有效材料清單的鍵值對應\n        const validMaterialKeys = new Set();\n        res.Entries.forEach(material => {\n          const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\n          validMaterialKeys.add(key);\n        });\n        // 篩選出仍然有效的表單項目\n        const validFormItems = this.arrListFormItemReq.filter(item => {\n          const itemKey = `${item.CLocation}_${item.CName}_${item.CPart}`;\n          return validMaterialKeys.has(itemKey);\n        });\n        if (validFormItems.length === 0) {\n          this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\n          return;\n        }\n        // 準備複製的表單項目數據\n        this.saveListFormItemReq = validFormItems.map(e => {\n          return {\n            CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n            CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n            CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n            CFormItemId: null,\n            // 設為 null 以建立新項目\n            CFormID: null,\n            // 設為 null 以建立新表單\n            CName: e.CName,\n            CPart: e.CPart,\n            CLocation: e.CLocation,\n            CItemName: e.CItemName,\n            CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n            CTotalAnswer: e.CTotalAnswer,\n            CRequireAnswer: e.CRequireAnswer,\n            CUiType: e.selectedCUiType.value\n          };\n        });\n        // 執行驗證\n        this.validation();\n        if (this.valid.errorMessages.length > 0) {\n          this.message.showErrorMSGs(this.valid.errorMessages);\n          return;\n        }\n        // 建立複製的表單\n        this.creatListFormItem = {\n          CBuildCaseId: this.buildCaseId,\n          CFormItem: this.saveListFormItemReq || null,\n          CFormType: this.typeContentManagementSalesAccount.CFormType\n        };\n        this._formItemService.apiFormItemCreateListFormItemPost$Json({\n          body: this.creatListFormItem\n        }).subscribe(createRes => {\n          if (createRes.StatusCode == 0) {\n            this.message.showSucessMSG(`複製表單成功，已篩選 ${validFormItems.length} 個有效項目`);\n            // 重新載入資料以顯示新的未鎖定表單\n            this.getListFormItem();\n          }\n        });\n      } else {\n        this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\n      }\n    })).subscribe();\n  }\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\n  loadBuildingDataFromAPI() {\n    if (!this.buildCaseId) return;\n    this._houseService.apiHouseGetDropDownPost$Json({\n      buildCaseId: this.buildCaseId\n    }).subscribe({\n      next: response => {\n        console.log('GetDropDown API response:', response);\n        if (response.Entries) {\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n          console.log('Converted buildingData:', this.buildingData);\n        }\n      },\n      error: error => {\n        console.error('Error loading building data from API:', error);\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\n        }\n      }\n    });\n  }\n  // 新增：將 API 回應轉換為建築物資料格式\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        code: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseName: house.HouseName,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  // 新增：將戶別清單轉換為建築物資料格式\n  convertHouseHoldListToBuildingData(houseHoldList) {\n    if (!houseHoldList || houseHoldList.length === 0) {\n      return {};\n    }\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\n    const buildingData = {};\n    houseHoldList.forEach(household => {\n      // 嘗試從戶別名稱中提取建築物代碼\n      const buildingMatch = household.match(/^([A-Z]+)/);\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\n      if (!buildingData[building]) {\n        buildingData[building] = [];\n      }\n      // 計算樓層（假設每4戶為一層）\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\n      const floor = Math.ceil(houseNumber / 4);\n      buildingData[building].push({\n        code: household,\n        building: building,\n        floor: `${floor}F`,\n        isSelected: false,\n        isDisabled: false\n      });\n    });\n    return buildingData;\n  }\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        // 載入建築物資料 (只呼叫一次 GetDropDown API)\n        this.loadBuildingDataFromAPI();\n        this.getListFormItem();\n      } else {\n        console.error('getListRegularNoticeFileHouseHold failed:', res);\n      }\n    })).subscribe({\n      error: error => {\n        console.error('getListRegularNoticeFileHouseHold error:', error);\n      }\n    });\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  // UI優化相關方法\n  /**\n   * 檢查項目是否已完成\n   */\n  isItemCompleted(formItemReq) {\n    // 檢查必填欄位是否都已填寫\n    const hasItemName = !!formItemReq.CItemName && formItemReq.CItemName.trim() !== '';\n    const hasUiType = !!formItemReq.selectedCUiType && formItemReq.selectedCUiType.value;\n    const hasRequireAnswer = !!formItemReq.CRequireAnswer && formItemReq.CRequireAnswer > 0;\n    // 如果是建材選樣類型，檢查是否有選擇備註類型\n    let hasRemarkType = true;\n    if (formItemReq.selectedCUiType?.value === 3) {\n      hasRemarkType = !!formItemReq.selectedRemarkType && Object.values(formItemReq.selectedRemarkType).some(selected => selected);\n    }\n    return hasItemName && hasUiType && hasRequireAnswer && hasRemarkType;\n  }\n  /**\n   * 獲取已完成項目數量\n   */\n  getCompletedItemsCount() {\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\n    return this.arrListFormItemReq.filter(item => this.isItemCompleted(item)).length;\n  }\n  /**\n   * 獲取進度百分比\n   */\n  getProgressPercentage() {\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\n    const completed = this.getCompletedItemsCount();\n    return Math.round(completed / this.arrListFormItemReq.length * 100);\n  }\n  /**\n   * 滾動到指定項目\n   */\n  scrollToItem(index) {\n    const element = document.getElementById(`form-item-${index}`);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start',\n        inline: 'nearest'\n      });\n      // 添加高亮效果\n      element.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-75');\n      setTimeout(() => {\n        element.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-75');\n      }, 2000);\n    }\n  }\n  /**\n   * 滾動到第一個未完成的項目\n   */\n  scrollToFirstIncompleteItem() {\n    if (!this.arrListFormItemReq) return;\n    const firstIncompleteIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\n    if (firstIncompleteIndex !== -1) {\n      this.scrollToItem(firstIncompleteIndex);\n    }\n  }\n  /**\n   * 滾動到第一個有錯誤的項目\n   */\n  scrollToFirstErrorItem() {\n    if (!this.arrListFormItemReq) return;\n    // 找到第一個有錯誤的項目\n    const firstErrorIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\n    if (firstErrorIndex !== -1) {\n      this.scrollToItem(firstErrorIndex);\n    }\n  }\n  /**\n   * 滾動到頂部\n   */\n  scrollToTop() {\n    window.scrollTo(0, 0);\n  }\n  /**\n   * 回到頂部\n   */\n  goToTop() {\n    console.log('goToTop clicked - method called');\n    // 優先滾動到頁面最頂部的header區塊\n    const headerElement = document.querySelector('nb-card-header') || document.querySelector('.card-header') || document.querySelector('nb-card') || document.querySelector('.header') || document.querySelector('h1, h2, h3') || document.body.firstElementChild;\n    if (headerElement) {\n      headerElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n      console.log('Scrolled to header element:', headerElement.tagName);\n      // 額外向上滾動一點，確保header完全可見\n      setTimeout(() => {\n        window.scrollBy({\n          top: -50,\n          // 向上滾動50px\n          behavior: 'smooth'\n        });\n      }, 500);\n      return;\n    }\n    // 備用方案：滾動到第一個表單項目的上方\n    if (this.arrListFormItemReq && this.arrListFormItemReq.length > 0) {\n      const firstElement = document.getElementById('form-item-0');\n      if (firstElement) {\n        firstElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        });\n        // 向上滾動更多距離，確保看到header\n        setTimeout(() => {\n          window.scrollBy({\n            top: -200,\n            // 向上滾動200px\n            behavior: 'smooth'\n          });\n        }, 500);\n        console.log('Scrolled to first form item with extra offset');\n        return;\n      }\n    }\n    // 最後的備用方法\n    console.log('Using fallback scroll methods');\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  }\n  /**\n   * 測試按鈕點擊事件\n   */\n  testButtonClick(message) {\n    console.log(message);\n  }\n  /**\n   * 滾動到底部\n   */\n  scrollToBottom() {\n    console.log('=== 至底功能被點擊 ===');\n    // 立即顯示一個簡短的視覺反饋\n    const button = document.querySelector('button[title=\"到底部\"]');\n    if (button) {\n      button.style.transform = 'scale(0.95)';\n      setTimeout(() => {\n        button.style.transform = '';\n      }, 150);\n    }\n    // 嘗試多種方法確保滾動到底部\n    setTimeout(() => {\n      // 方法1: 滾動到最後一個表單項目\n      if (this.filteredArrListFormItemReq && this.filteredArrListFormItemReq.length > 0) {\n        const lastIndex = this.filteredArrListFormItemReq.length - 1;\n        const lastElement = document.getElementById(`form-item-${lastIndex}`);\n        if (lastElement) {\n          console.log(`找到最後一個表單項目: form-item-${lastIndex}`);\n          lastElement.scrollIntoView({\n            behavior: 'smooth',\n            block: 'end',\n            inline: 'nearest'\n          });\n          // 額外向下滾動一點，確保完全看到底部\n          setTimeout(() => {\n            window.scrollBy({\n              top: 100,\n              behavior: 'smooth'\n            });\n          }, 500);\n          console.log('已滾動到最後一個表單項目');\n          return;\n        }\n      }\n      // 方法2: 滾動到頁面絕對底部\n      console.log('使用備用方法：滾動到頁面絕對底部');\n      const scrollHeight = Math.max(document.body.scrollHeight, document.body.offsetHeight, document.documentElement.clientHeight, document.documentElement.scrollHeight, document.documentElement.offsetHeight);\n      console.log('滾動高度:', scrollHeight);\n      window.scrollTo({\n        top: scrollHeight,\n        behavior: 'smooth'\n      });\n    }, 100);\n  }\n  /**\n   * 切換項目收合狀態\n   */\n  toggleItemCollapse(formItemReq) {\n    formItemReq.isCollapsed = !formItemReq.isCollapsed;\n  }\n  /**\n   * 全部展開\n   */\n  expandAll() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = false;\n      });\n      this.cdr.detectChanges();\n    }\n  }\n  /**\n   * 全部收合\n   */\n  collapseAll() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = true;\n      });\n      this.cdr.detectChanges();\n    }\n  }\n  /**\n   * 只展開未完成的項目\n   */\n  expandIncompleteOnly() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = this.isItemCompleted(item);\n      });\n      this.cdr.detectChanges();\n    }\n  }\n  /**\n   * 搜尋功能\n   */\n  onSearch() {\n    if (!this.searchQuery.trim()) {\n      this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\n    } else {\n      const query = this.searchQuery.toLowerCase().trim();\n      this.filteredArrListFormItemReq = this.arrListFormItemReq.filter(item => {\n        return item.CName?.toLowerCase().includes(query) || item.CPart?.toLowerCase().includes(query) || item.CLocation?.toLowerCase().includes(query) || item.CItemName?.toLowerCase().includes(query);\n      });\n    }\n    this.cdr.detectChanges();\n  }\n  /**\n   * 清除搜尋\n   */\n  clearSearch() {\n    this.searchQuery = '';\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\n    this.cdr.detectChanges();\n  }\n  /**\n   * 更新過濾列表\n   */\n  updateFilteredList() {\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\n    if (this.searchQuery.trim()) {\n      this.onSearch();\n    }\n  }\n  static {\n    this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService), i0.ɵɵdirectiveInject(i4.HouseService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-detail-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 71,\n      vars: 18,\n      consts: [[\"inputFile\", \"\"], [\"noRemarkOptions\", \"\"], [1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"to-gray-100\"], [1, \"shadow-xl\", \"border-0\", \"rounded-xl\"], [1, \"bg-white\", \"border-b\", \"border-gray-200\", \"p-6\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"w-1\", \"h-8\", \"bg-green-500\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"bg-green-100\", \"text-green-800\", \"rounded-full\", \"font-medium\"], [1, \"p-6\", \"bg-gray-50\"], [1, \"space-y-8\"], [1, \"bg-white\", \"rounded-xl\", \"p-6\", \"shadow-sm\", \"border\", \"border-gray-200\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-10\", \"h-10\", \"bg-blue-100\", \"rounded-lg\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-blue-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-bold\", \"text-gray-800\"], [1, \"text-sm\", \"text-gray-600\", \"mt-1\"], [1, \"text-right\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"class\", \"mt-4 mb-4\", 4, \"ngIf\"], [\"class\", \"mt-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 border border-gray-200\", 4, \"ngIf\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"bg-white/95\", \"backdrop-blur-sm\", \"border-t\", \"border-gray-200\", \"p-4\", \"sticky\", \"bottom-0\", \"shadow-lg\", \"z-30\"], [1, \"flex\", \"items-center\", \"justify-center\"], [1, \"flex\", \"items-center\", \"space-x-6\", \"text-sm\", \"text-gray-600\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"w-3\", \"h-3\", \"bg-blue-500\", \"rounded-full\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"fixed\", \"right-6\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"z-50\", \"flex\", \"flex-col\", \"space-y-3\"], [1, \"relative\"], [\"title\", \"\\u5132\\u5B58\\u8B8A\\u66F4\", 1, \"w-16\", \"h-16\", \"bg-gradient-to-r\", \"from-green-500\", \"to-green-600\", \"hover:from-green-600\", \"hover:to-green-700\", \"text-white\", \"rounded-full\", \"shadow-xl\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", \"group\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"click\", \"disabled\"], [\"class\", \"w-6 h-6 animate-spin\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"class\", \"w-6 h-6\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"absolute\", \"right-20\", \"bg-gray-800\", \"text-white\", \"text-sm\", \"px-3\", \"py-2\", \"rounded-lg\", \"shadow-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"whitespace-nowrap\"], [\"class\", \"w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center group\", \"title\", \"\\u8907\\u88FD\\u5230\\u65B0\\u8868\\u55AE\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"title\", \"\\u53D6\\u6D88\\u4E26\\u8FD4\\u56DE\", 1, \"w-14\", \"h-14\", \"bg-gray-500\", \"hover:bg-gray-600\", \"text-white\", \"rounded-full\", \"shadow-xl\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", \"group\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [1, \"absolute\", \"right-16\", \"bg-gray-800\", \"text-white\", \"text-sm\", \"px-3\", \"py-2\", \"rounded-lg\", \"shadow-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"whitespace-nowrap\"], [\"title\", \"\\u56DE\\u5230\\u9802\\u90E8\", 1, \"w-14\", \"h-14\", \"bg-purple-500\", \"hover:bg-purple-600\", \"text-white\", \"rounded-full\", \"shadow-xl\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", \"group\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 10l7-7m0 0l7 7m-7-7v18\"], [\"title\", \"\\u5230\\u5E95\\u90E8\", 1, \"w-14\", \"h-14\", \"bg-indigo-500\", \"hover:bg-indigo-600\", \"text-white\", \"rounded-full\", \"shadow-xl\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", \"group\", \"relative\", \"z-10\", 2, \"pointer-events\", \"auto\", 3, \"click\", \"mousedown\", \"mouseup\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 14l-7 7m0 0l-7-7m7 7V3\"], [1, \"mt-4\", \"mb-4\"], [1, \"flex-1\", \"relative\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-gray-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31\\u3001\\u90E8\\u4F4D\\u3001\\u4F4D\\u7F6E...\", 1, \"block\", \"w-full\", \"pl-10\", \"pr-10\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-blue-500\", \"text-sm\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"class\", \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"text-sm text-gray-600\", 4, \"ngIf\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"pr-3\", \"flex\", \"items-center\", \"text-gray-400\", \"hover:text-gray-600\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"mt-6\", \"bg-gradient-to-r\", \"from-gray-50\", \"to-blue-50\", \"rounded-xl\", \"p-4\", \"border\", \"border-gray-200\"], [1, \"flex\", \"items-center\", \"justify-between\", \"flex-wrap\", \"gap-4\"], [1, \"flex\", \"items-center\", \"flex-wrap\", \"gap-3\"], [1, \"flex\", \"items-center\", \"gap-2\"], [1, \"flex\", \"items-center\", \"bg-white\", \"rounded-lg\", \"shadow-sm\", \"border\", \"border-gray-200\", \"overflow-hidden\"], [\"title\", \"\\u5168\\u90E8\\u5C55\\u958B\", 1, \"px-3\", \"py-2\", \"text-sm\", \"bg-white\", \"hover:bg-purple-50\", \"text-purple-700\", \"transition-colors\", \"border-r\", \"border-gray-200\", \"group\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"], [\"title\", \"\\u5168\\u90E8\\u6536\\u5408\", 1, \"px-3\", \"py-2\", \"text-sm\", \"bg-white\", \"hover:bg-purple-50\", \"text-purple-700\", \"transition-colors\", \"border-r\", \"border-gray-200\", \"group\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 9l-7 7-7-7\"], [1, \"flex\", \"items-center\", \"gap-4\"], [1, \"hidden\", \"lg:flex\", \"items-center\", \"gap-2\", \"text-xs\", \"text-gray-500\", \"bg-blue-50\", \"rounded-lg\", \"px-3\", \"py-2\", \"border\", \"border-blue-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-blue-500\"], [1, \"text-center\", \"py-12\"], [1, \"bg-gray-50\", \"rounded-xl\", \"p-8\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-16\", \"h-16\", \"text-gray-400\", \"mx-auto\", \"mb-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\", \"mb-2\"], [1, \"text-gray-500\", \"mb-4\"], [1, \"px-4\", \"py-2\", \"bg-blue-500\", \"text-white\", \"rounded-lg\", \"hover:bg-blue-600\", \"transition-colors\", 3, \"click\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-lg\", \"border\", \"border-gray-200\", \"overflow-hidden\", \"transition-all\", \"duration-300\", \"hover:shadow-xl\", 3, \"id\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-indigo-50\", \"px-6\", \"py-4\", \"border-b\", \"border-gray-200\"], [1, \"w-8\", \"h-8\", \"bg-gray-100\", \"hover:bg-gray-200\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"click\", \"title\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-gray-600\", \"transition-transform\", \"duration-200\"], [1, \"w-8\", \"h-8\", \"bg-blue-500\", \"text-white\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-sm\", \"font-bold\"], [1, \"flex-1\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [1, \"px-2\", \"py-1\", \"text-xs\", \"bg-blue-100\", \"text-blue-800\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"class\", \"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\", \"title\", \"\\u4E0A\\u4E00\\u500B\\u9805\\u76EE\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\", \"title\", \"\\u4E0B\\u4E00\\u500B\\u9805\\u76EE\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-6\", 4, \"ngIf\"], [\"title\", \"\\u4E0A\\u4E00\\u500B\\u9805\\u76EE\", 1, \"w-7\", \"h-7\", \"bg-gray-100\", \"hover:bg-gray-200\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 19l-7-7 7-7\"], [\"title\", \"\\u4E0B\\u4E00\\u500B\\u9805\\u76EE\", 1, \"w-7\", \"h-7\", \"bg-gray-100\", \"hover:bg-gray-200\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"p-6\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-4\", \"gap-6\"], [1, \"lg:col-span-1\"], [1, \"space-y-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-700\"], [\"class\", \"relative\", 4, \"ngIf\"], [\"class\", \"aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"lg:col-span-2\"], [1, \"space-y-6\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mb-4\"], [1, \"group\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\", 3, \"for\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"bg-gray-50\", \"p-3\", \"rounded-lg\", \"border\", \"border-gray-200\"], [1, \"text-sm\", \"text-gray-600\", \"font-medium\", \"px-2\", \"py-1\", \"bg-blue-100\", \"rounded-md\", \"whitespace-nowrap\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u4F8B\\u5982\\uFF1A\\u5EDA\\u623F\\u6AAF\\u9762\", 1, \"flex-1\", \"border-0\", \"bg-transparent\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-transparent\", \"rounded-md\", \"p-2\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u8F38\\u5165\\u6578\\u91CF\", 1, \"w-full\", \"border-2\", \"border-gray-200\", \"focus:border-blue-500\", \"focus:ring-2\", \"focus:ring-blue-200\", \"rounded-lg\", \"p-3\", \"transition-all\", \"duration-200\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [\"placeholder\", \"\\u9078\\u64C7UI\\u985E\\u578B\", 1, \"w-full\", \"border-2\", \"border-gray-200\", \"focus:border-blue-500\", \"rounded-lg\", \"transition-all\", \"duration-200\", 3, \"ngModelChange\", \"selectedChange\", \"id\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-blue-500\", \"to-blue-600\", \"hover:from-blue-600\", \"hover:to-blue-700\", \"text-white\", \"font-medium\", \"py-3\", \"px-4\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"shadow-md\", \"hover:shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [\"class\", \"space-y-3\", 4, \"ngIf\"], [\"class\", \"space-y-2\", 4, \"ngIf\"], [\"class\", \"h-32 w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"my-8\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\"], [1, \"w-full\", \"border-t\", \"border-gray-300\"], [1, \"relative\", \"flex\", \"justify-center\", \"text-sm\"], [1, \"px-4\", \"bg-white\", \"text-gray-500\", \"font-medium\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-2\", \"gap-6\"], [1, \"bg-blue-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-blue-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"font-semibold\", \"text-blue-800\"], [1, \"w-full\", 3, \"selectionChange\", \"buildingData\", \"placeholder\", \"disabled\", \"allowBatchSelect\", \"ngModel\", \"useHouseNameMode\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"bg-orange-50 p-4 rounded-lg border border-orange-200\", 4, \"ngIf\"], [1, \"aspect-square\", \"w-full\", \"relative\", \"overflow-hidden\", \"rounded-xl\", \"border-2\", \"border-gray-200\", \"cursor-pointer\", \"group\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"duration-300\", 3, \"click\"], [\"class\", \"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\", 3, \"src\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-30\", \"transition-all\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [1, \"transform\", \"scale-75\", \"group-hover:scale-100\", \"transition-transform\", \"duration-300\"], [1, \"w-12\", \"h-12\", \"bg-white\", \"bg-opacity-90\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-gray-800\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"], [\"class\", \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"flex gap-2 mt-3 overflow-x-auto pb-2\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-500\", \"group-hover:scale-110\", 3, \"src\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 1, \"absolute\", \"left-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M15 19l-7-7 7-7\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 1, \"absolute\", \"right-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M9 5l7 7-7 7\"], [1, \"absolute\", \"bottom-2\", \"right-2\", \"bg-black\", \"bg-opacity-80\", \"text-white\", \"text-xs\", \"px-2\", \"py-1\", \"rounded-lg\", \"backdrop-blur-sm\"], [1, \"flex\", \"gap-2\", \"mt-3\", \"overflow-x-auto\", \"pb-2\"], [\"class\", \"flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\", 3, \"border-blue-500\", \"border-gray-300\", \"ring-2\", \"ring-blue-200\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-12\", \"h-12\", \"border-2\", \"rounded-lg\", \"overflow-hidden\", \"hover:border-blue-400\", \"transition-all\", \"duration-200\", \"cursor-pointer\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"hover:scale-110\", 3, \"src\"], [1, \"aspect-square\", \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-xl\", \"bg-gray-50\", \"text-gray-400\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-12\", \"h-12\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\"], [3, \"value\"], [1, \"space-y-3\"], [\"class\", \"bg-gray-50 border border-gray-200 p-3 rounded-lg hover:shadow-md transition-all duration-200\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-gray-50\", \"border\", \"border-gray-200\", \"p-3\", \"rounded-lg\", \"hover:shadow-md\", \"transition-all\", \"duration-200\"], [1, \"relative\", \"group\"], [1, \"w-full\", \"h-32\", \"object-cover\", \"rounded-lg\", \"mb-3\", \"border\", \"border-gray-200\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-20\", \"transition-all\", \"duration-200\", \"rounded-lg\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5716\\u7247\\u8AAA\\u660E/\\u6A94\\u540D\", 1, \"w-full\", \"p-2\", \"text-sm\", \"mb-2\", \"border\", \"border-gray-200\", \"rounded-md\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-transparent\", 3, \"blur\", \"value\", \"disabled\"], [1, \"w-full\", \"bg-red-100\", \"hover:bg-red-200\", \"text-red-700\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-md\", \"transition-colors\", \"duration-200\", \"text-sm\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"space-y-2\"], [1, \"block\", \"text-xs\", \"font-medium\", \"text-gray-600\"], [1, \"w-full\", \"h-32\", \"object-cover\", \"rounded-lg\", \"border\", \"border-gray-200\", 3, \"src\"], [1, \"h-32\", \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-lg\", \"bg-gray-50\", \"text-gray-400\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"mb-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"], [1, \"text-xs\"], [1, \"text-center\", \"py-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-gray-400\", \"mx-auto\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-gray-500\", \"text-sm\"], [1, \"bg-orange-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-orange-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-orange-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [1, \"font-semibold\", \"text-orange-800\"], [\"class\", \"grid grid-cols-1 gap-2\", 4, \"ngIf\", \"ngIfElse\"], [1, \"grid\", \"grid-cols-1\", \"gap-2\"], [\"class\", \"flex items-center cursor-pointer hover:bg-orange-100 p-2 rounded-md transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"cursor-pointer\", \"hover:bg-orange-100\", \"p-2\", \"rounded-md\", \"transition-colors\"], [\"value\", \"item\", \"class\", \"mr-3\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"], [1, \"text-gray-700\"], [\"value\", \"item\", 1, \"mr-3\", 3, \"checkedChange\", \"checked\", \"disabled\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [\"class\", \"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fade-in-up\", \"tabindex\", \"0\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"fixed\", \"inset-0\", \"z-[9999]\", \"flex\", \"items-center\", \"justify-center\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-sm\", \"p-4\", \"animate-fade-in-up\", 3, \"click\", \"keydown\"], [\"title\", \"\\u95DC\\u9589\\u5716\\u7247\\u6AA2\\u8996 (\\u6309 ESC \\u9375)\", 1, \"modal-close-btn\", \"fixed\", \"top-6\", \"right-6\", \"z-[60]\", \"bg-red-500\", \"bg-opacity-95\", \"hover:bg-red-600\", \"hover:bg-opacity-100\", \"text-white\", \"rounded-full\", \"w-14\", \"h-14\", \"flex\", \"items-center\", \"justify-center\", \"shadow-2xl\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-7\", \"h-7\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"relative\", \"max-w-7xl\", \"max-h-full\", \"w-full\", \"h-full\", \"flex\", \"items-center\", \"justify-center\", \"animate-slide-in-left\", 3, \"click\"], [1, \"relative\", \"max-w-full\", \"max-h-full\", \"bg-white\", \"rounded-2xl\", \"p-2\", \"shadow-2xl\"], [\"class\", \"max-w-full max-h-[85vh] object-contain rounded-xl animate-fade-in-up\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"modal-nav-btn absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"modal-nav-btn absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-6\", \"right-6\", \"bg-gradient-to-r\", \"from-blue-600\", \"to-blue-700\", \"text-white\", \"px-4\", \"py-3\", \"rounded-lg\", \"text-sm\", \"backdrop-blur-sm\", \"shadow-lg\"], [1, \"font-medium\"], [\"class\", \"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\", 4, \"ngIf\"], [1, \"max-w-full\", \"max-h-[85vh]\", \"object-contain\", \"rounded-xl\", \"animate-fade-in-up\", 3, \"src\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 1, \"modal-nav-btn\", \"absolute\", \"left-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"z-[55]\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 1, \"modal-nav-btn\", \"absolute\", \"right-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"z-[55]\", 3, \"click\"], [1, \"absolute\", \"bottom-24\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-90\", \"text-white\", \"px-6\", \"py-3\", \"rounded-full\", \"backdrop-blur-sm\", \"shadow-lg\"], [1, \"font-medium\", \"text-lg\"], [1, \"absolute\", \"bottom-32\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-md\", \"p-4\", \"rounded-xl\", \"shadow-2xl\", \"max-w-full\"], [1, \"flex\", \"gap-3\", \"overflow-x-auto\", \"max-w-[80vw]\", \"modal-thumbnails\"], [\"class\", \"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105\", 3, \"border-white\", \"border-gray-400\", \"ring-3\", \"ring-white\", \"ring-opacity-50\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-20\", \"h-20\", \"border-3\", \"rounded-xl\", \"overflow-hidden\", \"hover:border-white\", \"transition-all\", \"duration-200\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-200\", 3, \"src\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"animate-spin\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 13l4 4L19 7\"], [\"title\", \"\\u8907\\u88FD\\u5230\\u65B0\\u8868\\u55AE\", 1, \"w-14\", \"h-14\", \"bg-blue-500\", \"hover:bg-blue-600\", \"text-white\", \"rounded-full\", \"shadow-xl\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", \"group\", 3, \"click\", \"disabled\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"]],\n      template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"nb-card\", 3)(2, \"nb-card-header\", 4)(3, \"div\", 5)(4, \"div\", 6);\n          i0.ɵɵelement(5, \"div\", 7);\n          i0.ɵɵelementStart(6, \"div\");\n          i0.ɵɵelement(7, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"span\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(11, \"nb-card-body\", 10)(12, \"div\", 11)(13, \"div\", 12)(14, \"div\", 5)(15, \"div\", 13)(16, \"div\", 14);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(17, \"svg\", 15);\n          i0.ɵɵelement(18, \"path\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(19, \"div\")(20, \"h3\", 17);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\", 18);\n          i0.ɵɵtext(23, \"\\u7BA1\\u7406\\u9078\\u6A23\\u9805\\u76EE\\u7684\\u8A73\\u7D30\\u8A2D\\u5B9A\\u8207\\u914D\\u7F6E\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 6)(25, \"div\", 19)(26, \"div\", 20);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(28, DetailContentManagementSalesAccountComponent_div_28_Template, 9, 3, \"div\", 21)(29, DetailContentManagementSalesAccountComponent_div_29_Template, 18, 0, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(30, DetailContentManagementSalesAccountComponent_div_30_Template, 10, 0, \"div\", 23)(31, DetailContentManagementSalesAccountComponent_ng_container_31_Template, 22, 13, \"ng-container\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"nb-card-footer\", 25)(33, \"div\", 26)(34, \"div\", 27)(35, \"div\", 8);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(36, \"svg\", 28);\n          i0.ɵɵelement(37, \"path\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(38, \"span\");\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 6)(41, \"div\", 8);\n          i0.ɵɵelement(42, \"div\", 30);\n          i0.ɵɵelementStart(43, \"span\");\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 31);\n          i0.ɵɵtext(46, \" \\u4F7F\\u7528\\u53F3\\u5074\\u61F8\\u6D6E\\u6309\\u9215\\u9032\\u884C\\u64CD\\u4F5C \");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(47, DetailContentManagementSalesAccountComponent_ng_container_47_Template, 2, 1, \"ng-container\", 24);\n          i0.ɵɵelementStart(48, \"div\", 32)(49, \"div\", 33)(50, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_50_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(51, DetailContentManagementSalesAccountComponent__svg_svg_51_Template, 2, 0, \"svg\", 35)(52, DetailContentManagementSalesAccountComponent__svg_svg_52_Template, 2, 0, \"svg\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 37);\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(55, DetailContentManagementSalesAccountComponent_button_55_Template, 5, 1, \"button\", 38);\n          i0.ɵɵelementStart(56, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_56_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(57, \"svg\", 40);\n          i0.ɵɵelement(58, \"path\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(59, \"div\", 42);\n          i0.ɵɵtext(60, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_61_listener() {\n            return ctx.goToTop();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(62, \"svg\", 40);\n          i0.ɵɵelement(63, \"path\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(64, \"div\", 42);\n          i0.ɵɵtext(65, \" \\u56DE\\u5230\\u9802\\u90E8 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"button\", 45);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_66_listener() {\n            return ctx.scrollToBottom();\n          })(\"mousedown\", function DetailContentManagementSalesAccountComponent_Template_button_mousedown_66_listener() {\n            return ctx.console.log(\"\\u81F3\\u5E95\\u6309\\u9215\\u88AB\\u6309\\u4E0B\");\n          })(\"mouseup\", function DetailContentManagementSalesAccountComponent_Template_button_mouseup_66_listener() {\n            return ctx.console.log(\"\\u81F3\\u5E95\\u6309\\u9215\\u88AB\\u91CB\\u653E\");\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(67, \"svg\", 40);\n          i0.ɵɵelement(68, \"path\", 46);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(69, \"div\", 42);\n          i0.ɵɵtext(70, \" \\u5230\\u5E95\\u90E8 \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_11_0;\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\" \", ctx.dynamicTitle, \" \");\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.dynamicTitle);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \\u7E3D\\u8A08\\uFF1A\", (ctx.arrListFormItemReq == null ? null : ctx.arrListFormItemReq.length) || 0, \" \\u500B\\u9805\\u76EE \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.arrListFormItemReq.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.arrListFormItemReq.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchQuery && ctx.filteredArrListFormItemReq.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredArrListFormItemReq);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx.arrListFormItemReq.length || 0, \" \\u500B\\u9078\\u6A23\\u9805\\u76EE\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\\u7E3D\\u8A08 \", ctx.arrListFormItemReq.length || 0, \" \\u500B\\u9805\\u76EE\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"animate-pulse\", ctx.isSubmitting);\n          i0.ɵɵproperty(\"disabled\", ((tmp_11_0 = ctx.listFormItem == null ? null : ctx.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false) || ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"\\u5132\\u5B58\\u4E2D...\" : \"\\u5132\\u5B58\\u8B8A\\u66F4\", \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem == null ? null : ctx.listFormItem.CIsLock);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, FormsModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, SharedModule, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, AppSharedModule, i12.HouseholdBindingComponent, NbCheckboxModule, Base64ImagePipe],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  min-height: 100vh;\\n}\\n\\n.page-background[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  animation: _ngcontent-%COMP%_gradientShift 20s ease infinite;\\n  background-size: 400% 400%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientShift {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\nnb-card[_ngcontent-%COMP%] {\\n  border-radius: 1rem !important;\\n  overflow: hidden;\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\nnb-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;\\n}\\n\\n.item-card[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.item-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);\\n}\\n\\ninput[nbInput][_ngcontent-%COMP%], \\nnb-select[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out !important;\\n}\\ninput[nbInput][_ngcontent-%COMP%]:focus, \\nnb-select[_ngcontent-%COMP%]:focus {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;\\n}\\n\\n.btn-enhanced[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.btn-enhanced[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.btn-enhanced[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n.image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.image-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.image-container[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n}\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.status-badge[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: inherit;\\n  border-radius: inherit;\\n  opacity: 0.1;\\n  transform: scale(0);\\n  transition: transform 0.3s ease;\\n}\\n.status-badge[_ngcontent-%COMP%]:hover::before {\\n  transform: scale(1.1);\\n}\\n\\n.responsive-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1.5rem;\\n}\\n@media (min-width: 768px) {\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    gap: 2rem;\\n  }\\n}\\n\\n.image-carousel[_ngcontent-%COMP%]:hover   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.carousel-btn[_ngcontent-%COMP%] {\\n  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n.carousel-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-50%) scale(1.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n}\\n.carousel-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n@media (max-width: 768px) {\\n  .carousel-btn[_ngcontent-%COMP%] {\\n    opacity: 1 !important;\\n    width: 2.5rem;\\n    height: 2.5rem;\\n  }\\n  .carousel-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1rem;\\n    height: 1rem;\\n  }\\n}\\n\\n.thumbnail-navigation[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 6px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 3px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6);\\n  border-radius: 3px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, #2563eb, #7c3aed);\\n}\\n\\n.image-modal[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-image[_ngcontent-%COMP%] {\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);\\n  transition: transform 0.3s ease;\\n  border-radius: 0.75rem;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n  max-height: 20vh;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.5));\\n  border-radius: 4px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.7));\\n}\\n\\n.modal-nav-btn[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  z-index: 9995 !important;\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n@media (max-width: 768px) {\\n  .modal-nav-btn[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n  }\\n  .modal-nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.5rem;\\n    height: 1.5rem;\\n  }\\n}\\n\\n.modal-close-btn[_ngcontent-%COMP%] {\\n  z-index: 9999 !important;\\n  pointer-events: auto;\\n  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4), 0 0 0 2px rgba(255, 255, 255, 0.1) !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-close-btn[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 30px rgba(239, 68, 68, 0.5), 0 0 0 3px rgba(255, 255, 255, 0.2) !important;\\n  transform: scale(1.1) rotate(90deg);\\n}\\n.modal-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .modal-close-btn[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n    top: 1rem;\\n    right: 1rem;\\n  }\\n  .modal-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.5rem;\\n    height: 1.5rem;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalFadeIn {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.9) translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromLeft {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(-20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromRight {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.animate-slide-in-left[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromLeft 0.5s ease-out;\\n}\\n\\n.animate-slide-in-right[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromRight 0.5s ease-out;\\n}\\n\\n.animate-fade-in-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n}\\n\\n@media (max-width: 768px) {\\n  .image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    margin: 1rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n    width: 3rem;\\n    height: 3rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.25rem;\\n    height: 1.25rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n    max-height: 15vh;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n  }\\n  .item-card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .carousel-btn[_ngcontent-%COMP%], \\n   .modal-nav-btn[_ngcontent-%COMP%] {\\n    border: 2px solid currentColor;\\n    background: rgba(255, 255, 255, 0.95);\\n  }\\n  .modal-close-btn[_ngcontent-%COMP%] {\\n    border: 2px solid currentColor;\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  *[_ngcontent-%COMP%], \\n   *[_ngcontent-%COMP%]::before, \\n   *[_ngcontent-%COMP%]::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .page-background[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\\n  }\\n  .item-card[_ngcontent-%COMP%] {\\n    background: #374151;\\n    border-color: #4b5563;\\n  }\\n  .carousel-btn[_ngcontent-%COMP%], \\n   .modal-nav-btn[_ngcontent-%COMP%] {\\n    background: rgba(31, 41, 55, 0.9);\\n    border-color: rgba(156, 163, 175, 0.3);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "NbCheckboxModule", "tap", "SharedModule", "AppSharedModule", "BaseComponent", "EEvent", "Base64ImagePipe", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵlistener", "DetailContentManagementSalesAccountComponent_div_28_button_7_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "filteredArrListFormItemReq", "length", "arrListFormItemReq", "ɵɵtwoWayListener", "DetailContentManagementSalesAccountComponent_div_28_Template_input_ngModelChange_6_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "searchQuery", "DetailContentManagementSalesAccountComponent_div_28_Template_input_input_6_listener", "onSearch", "ɵɵtemplate", "DetailContentManagementSalesAccountComponent_div_28_button_7_Template", "DetailContentManagementSalesAccountComponent_div_28_div_8_Template", "ɵɵtwoWayProperty", "ɵɵproperty", "DetailContentManagementSalesAccountComponent_div_29_Template_button_click_7_listener", "_r4", "expandAll", "DetailContentManagementSalesAccountComponent_div_29_Template_button_click_10_listener", "collapseAll", "DetailContentManagementSalesAccountComponent_div_30_Template_button_click_8_listener", "_r5", "DetailContentManagementSalesAccountComponent_ng_container_31_button_19_Template_button_click_0_listener", "_r8", "idx_r9", "index", "scrollToItem", "DetailContentManagementSalesAccountComponent_ng_container_31_button_20_Template_button_click_0_listener", "_r10", "ɵɵpipeBind1", "getCurrentImage", "formItemReq_r7", "ɵɵsanitizeUrl", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_button_8_Template_button_click_0_listener", "_r13", "$implicit", "prevImage", "stopPropagation", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_button_9_Template_button_click_0_listener", "_r14", "nextImage", "currentImageIndex", "CMatrialUrl", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_div_11_button_1_Template_button_click_0_listener", "i_r16", "_r15", "openImageModal", "ɵɵclassProp", "imageUrl_r17", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_div_11_button_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_Template_div_click_1_listener", "_r12", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_img_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_button_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_button_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_div_10_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_div_11_Template", "case_r18", "ɵɵtextInterpolate1", "label", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_50_div_1_Template_input_blur_4_listener", "i_r21", "_r20", "renameFile", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_50_div_1_Template_button_click_5_listener", "picture_r22", "removeImage", "id", "data", "name", "tmp_11_0", "listFormItem", "CIsLock", "undefined", "tmp_12_0", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_50_div_1_Template", "listPictures", "CDesignFileUrl", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r23", "remark_r24", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_label_1_nb_checkbox_1_Template", "ɵɵtextInterpolate", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_label_1_Template", "CRemarkTypeOptions", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_div_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_ng_template_7_Template", "ɵɵtemplateRefExtractor", "noRemarkOptions_r25", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_10_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_input_ngModelChange_25_listener", "_r11", "CItemName", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_input_ngModelChange_30_listener", "CRequireAnswer", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_nb_select_ngModelChange_34_listener", "selectedCUiType", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_nb_select_selected<PERSON><PERSON>e_34_listener", "changeSelectCUiType", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_nb_option_35_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_button_click_43_listener", "inputFile_r19", "ɵɵreference", "click", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_input_change_48_listener", "detectFiles", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_50_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_51_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_52_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template_app_household_binding_selectionChange_67_listener", "onHouseholdSelectionChange", "extractHouseholdCodes", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_68_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_div_69_Template", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "value", "tmp_15_0", "tmp_19_0", "CUiTypeOptions", "buildingData", "tmp_27_0", "selectedHouseholdsCached", "houseHoldList", "ɵɵelementContainerStart", "DetailContentManagementSalesAccountComponent_ng_container_31_Template_button_click_5_listener", "_r6", "toggleItemCollapse", "DetailContentManagementSalesAccountComponent_ng_container_31_button_19_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_button_20_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_div_21_Template", "isCollapsed", "formItemReq_r28", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_button_7_Template_button_click_0_listener", "_r29", "prevImageModal", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_button_8_Template_button_click_0_listener", "_r30", "nextImageModal", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_div_16_button_2_Template_button_click_0_listener", "i_r32", "_r31", "imageUrl_r33", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_div_16_button_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_Template_div_click_0_listener", "_r27", "closeImageModal", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_Template_div_keydown_0_listener", "onKeydown", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_Template_button_click_1_listener", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_Template_div_click_4_listener", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_img_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_button_7_Template", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_button_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_div_16_Template", "DetailContentManagementSalesAccountComponent_ng_container_47_div_1_Template", "isModalOpen", "DetailContentManagementSalesAccountComponent_button_55_Template_button_click_0_listener", "_r34", "copyToNewForm", "isSubmitting", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "_houseService", "cdr", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "cNoticeTypeOptions", "地主戶", "銷售戶", "selectedItems", "isNew", "dynamicTitle", "option", "find", "setCNoticeType", "noticeType", "some", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "console", "error", "showErrorMSG", "goBack", "queryParams", "houseType", "ngOnDestroy", "document", "body", "style", "overflow", "getItemByValue", "options", "item", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "formItemReq", "imageIndex", "key", "preventDefault", "households", "Array", "isArray", "map", "h", "code", "selectedHouseholds", "Object", "keys", "for<PERSON>ach", "household", "allSelected", "every", "updateSelectedHouseholdsCache", "getSelectedHouseholds", "updateAllSelectedHouseholdsCache", "checked", "createRemarkObject", "CRemarkType", "remarkObject", "remarkTypes", "includes", "mergeItems", "items", "Map", "has", "existing", "count", "set", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "o", "CFormItemHouseHold", "CFormId", "CUiType", "CSelectPicture", "x1", "CBase64", "url", "updateFilteredList", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFirstMatrialUrl", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "detectChanges", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "scrollToFirstErrorItem", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "next", "showSucessMSG", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "bItem", "CHousehold", "CIsSelect", "validMaterialKeys", "Set", "material", "add", "validFormItems", "itemKey", "createRes", "loadBuildingDataFromAPI", "apiHouseGetDropDownPost$Json", "response", "log", "convertApiResponseToBuildingData", "convertHouseHoldListToBuildingData", "entries", "building", "houses", "house", "HouseName", "Building", "floor", "Floor", "houseId", "HouseId", "houseName", "isSelected", "isDisabled", "buildingMatch", "match", "houseNumber", "parseInt", "replace", "Math", "ceil", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "isItemCompleted", "hasItemName", "trim", "hasUiType", "hasRequireAnswer", "hasRemarkType", "selected", "getCompletedItemsCount", "getProgressPercentage", "completed", "round", "element", "getElementById", "scrollIntoView", "behavior", "block", "inline", "classList", "setTimeout", "remove", "scrollToFirstIncompleteItem", "firstIncompleteIndex", "findIndex", "firstErrorIndex", "scrollToTop", "window", "scrollTo", "goToTop", "headerElement", "querySelector", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "tagName", "scrollBy", "top", "firstElement", "testButtonClick", "scrollToBottom", "button", "transform", "lastIndex", "lastElement", "scrollHeight", "max", "offsetHeight", "documentElement", "clientHeight", "expandIncompleteOnly", "query", "toLowerCase", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "HouseService", "ChangeDetectorRef", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementSalesAccountComponent_Template", "rf", "ctx", "DetailContentManagementSalesAccountComponent_div_28_Template", "DetailContentManagementSalesAccountComponent_div_29_Template", "DetailContentManagementSalesAccountComponent_div_30_Template", "DetailContentManagementSalesAccountComponent_ng_container_31_Template", "DetailContentManagementSalesAccountComponent_ng_container_47_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_50_listener", "DetailContentManagementSalesAccountComponent__svg_svg_51_Template", "DetailContentManagementSalesAccountComponent__svg_svg_52_Template", "DetailContentManagementSalesAccountComponent_button_55_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_56_listener", "DetailContentManagementSalesAccountComponent_Template_button_click_61_listener", "DetailContentManagementSalesAccountComponent_Template_button_click_66_listener", "DetailContentManagementSalesAccountComponent_Template_button_mousedown_66_listener", "DetailContentManagementSalesAccountComponent_Template_button_mouseup_66_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "BreadcrumbComponent", "i12", "HouseholdBindingComponent", "styles", "changeDetection"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService, HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq, GetMaterialListResponse } from 'src/services/api/models';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n  selectedHouseholdsCached?: string[]; // 緩存已選戶別，避免重複計算\r\n  isCollapsed?: boolean; // 是否收合狀態\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, SharedModule, AppSharedModule, NbCheckboxModule, Base64ImagePipe],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit, OnDestroy {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService,\r\n    private _houseService: HouseService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    super(_allow)\r\n  }\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n  // 通知類型選項映射\r\n  cNoticeTypeOptions = [\r\n    { label: '地主戶', value: EnumHouseType.地主戶 },\r\n    { label: '銷售戶', value: EnumHouseType.銷售戶 }\r\n  ];\r\n  // 動態獲取標題文字\r\n  get dynamicTitle(): string {\r\n    const option = this.cNoticeTypeOptions.find(option =>\r\n      option.value === this.typeContentManagementSalesAccount.CNoticeType\r\n    );\r\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\r\n  }\r\n  // 設置通知類型（可供外部調用）\r\n  setCNoticeType(noticeType: EnumHouseType): void {\r\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\r\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\r\n      // 同時設定 CFormType 以保持一致性\r\n      this.typeContentManagementSalesAccount.CFormType = noticeType;\r\n    }\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }];\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"];\r\n  buildCaseId: number;\r\n  isSubmitting: boolean = false;\r\n\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n\r\n        if (this.buildCaseId > 0) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        } else {\r\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\r\n          console.error('Invalid buildCaseId:', this.buildCaseId);\r\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\r\n          this.goBack();\r\n        }\r\n      }\r\n    });\r\n\r\n    // 處理查詢參數中的戶型\r\n    this.route.queryParams.subscribe(queryParams => {\r\n      if (queryParams['houseType']) {\r\n        const houseType = +queryParams['houseType'];\r\n        this.setCNoticeType(houseType);\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保在組件銷毀時恢復body的滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n  // 新增：戶別選擇器相關屬性\r\n  buildingData: any = {}; // 存放建築物戶別資料\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      formItemReq.isModalOpen = true;\r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\r\n  extractHouseholdCodes(households: any[]): string[] {\r\n    if (!households || !Array.isArray(households)) {\r\n      return [];\r\n    }\r\n    return households.map(h => h.code || h);\r\n  }\r\n  // 新增：處理戶別選擇變更\r\n  onHouseholdSelectionChange(selectedHouseholds: string[], formItemReq: any) {\r\n    // 重置所有戶別選擇狀態\r\n    Object.keys(formItemReq.selectedItems).forEach(key => {\r\n      formItemReq.selectedItems[key] = false;\r\n    });\r\n\r\n    // 設置選中的戶別\r\n    selectedHouseholds.forEach(household => {\r\n      formItemReq.selectedItems[household] = true;\r\n    });\r\n\r\n    // 更新全選狀態\r\n    formItemReq.allSelected = this.houseHoldList.length > 0 &&\r\n      this.houseHoldList.every(item => formItemReq.selectedItems[item]);\r\n\r\n    // 更新緩存\r\n    this.updateSelectedHouseholdsCache(formItemReq);\r\n  }\r\n\r\n  // 新增：取得已選戶別數組\r\n  getSelectedHouseholds(formItemReq: any): string[] {\r\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\r\n  }\r\n\r\n  // 新增：更新已選戶別緩存\r\n  private updateSelectedHouseholdsCache(formItemReq: any): void {\r\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\r\n  }\r\n\r\n  // 新增：更新所有項目的緩存\r\n  private updateAllSelectedHouseholdsCache(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(formItemReq => {\r\n        this.updateSelectedHouseholdsCache(formItemReq);\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: GetMaterialListResponse) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0, selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [], selectedCUiType: this.CUiTypeOptions[0],\r\n              currentImageIndex: 0,\r\n              isModalOpen: false,\r\n              isCollapsed: true, // 新項目默認收合\r\n              CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64).filter((url: any) => url != null) as string[] : []\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n\r\n          // 初始化過濾列表\r\n          this.updateFilteredList()\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes | null = null\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem?.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [], selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0,\r\n                isModalOpen: false,\r\n                isCollapsed: true, // 現有項目默認收合\r\n                selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\r\n              }\r\n            })\r\n\r\n            // 初始化過濾列表\r\n            this.updateFilteredList();\r\n\r\n            // 手動觸發變更檢測\r\n            this.cdr.detectChanges();\r\n          } else {\r\n            // 當無資料時，載入材料清單供新增使用\r\n            this.getMaterialList();\r\n          }\r\n\r\n          // 初始化所有項目的緩存\r\n          this.updateAllSelectedHouseholdsCache();\r\n\r\n          // 最終觸發變更檢測\r\n          this.cdr.detectChanges();\r\n        } else {\r\n          console.error('getListFormItem failed:', res);\r\n        }\r\n      })\r\n    ).subscribe({\r\n      error: (error) => {\r\n        console.error('getListFormItem error:', error);\r\n      }\r\n    })\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[] = []\r\n  filteredArrListFormItemReq: ExtendedSaveListFormItemReq[] = []\r\n  searchQuery: string = ''\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    // 設置提交狀態\r\n    this.isSubmitting = true;\r\n\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem?.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      // 滾動到第一個有錯誤的項目\r\n      this.scrollToFirstErrorItem();\r\n      this.isSubmitting = false;\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe({\r\n      next: (res) => {\r\n        this.isSubmitting = false;\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          // this.getListFormItem()\r\n          this.goBack()\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isSubmitting = false;\r\n        console.error('Save error:', error);\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe({\r\n      next: (res) => {\r\n        this.isSubmitting = false;\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          // this.getListFormItem()\r\n          this.goBack()\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isSubmitting = false;\r\n        console.error('Create error:', error);\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n  /**\r\n   * 複製當前表單到新表單\r\n   */\r\n  copyToNewForm() {\r\n    // 先取得當前有效的材料清單\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          // 建立有效材料清單的鍵值對應\r\n          const validMaterialKeys = new Set<string>();\r\n          res.Entries.forEach((material: any) => {\r\n            const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\r\n            validMaterialKeys.add(key);\r\n          });\r\n\r\n          // 篩選出仍然有效的表單項目\r\n          const validFormItems = this.arrListFormItemReq.filter((item: any) => {\r\n            const itemKey = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n            return validMaterialKeys.has(itemKey);\r\n          });\r\n\r\n          if (validFormItems.length === 0) {\r\n            this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\r\n            return;\r\n          }\r\n\r\n          // 準備複製的表單項目數據\r\n          this.saveListFormItemReq = validFormItems.map((e: any) => {\r\n            return {\r\n              CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n              CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n              CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n              CFormItemId: null, // 設為 null 以建立新項目\r\n              CFormID: null, // 設為 null 以建立新表單\r\n              CName: e.CName,\r\n              CPart: e.CPart,\r\n              CLocation: e.CLocation,\r\n              CItemName: e.CItemName,\r\n              CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n              CTotalAnswer: e.CTotalAnswer,\r\n              CRequireAnswer: e.CRequireAnswer,\r\n              CUiType: e.selectedCUiType.value,\r\n            }\r\n          });\r\n\r\n          // 執行驗證\r\n          this.validation()\r\n          if (this.valid.errorMessages.length > 0) {\r\n            this.message.showErrorMSGs(this.valid.errorMessages);\r\n            return\r\n          }\r\n\r\n          // 建立複製的表單\r\n          this.creatListFormItem = {\r\n            CBuildCaseId: this.buildCaseId,\r\n            CFormItem: this.saveListFormItemReq || null,\r\n            CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n          }\r\n\r\n          this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n            body: this.creatListFormItem\r\n          }).subscribe(createRes => {\r\n            if (createRes.StatusCode == 0) {\r\n              this.message.showSucessMSG(`複製表單成功，已篩選 ${validFormItems.length} 個有效項目`);\r\n              // 重新載入資料以顯示新的未鎖定表單\r\n              this.getListFormItem()\r\n            }\r\n          })\r\n        } else {\r\n          this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\r\n  private loadBuildingDataFromAPI(): void {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this._houseService.apiHouseGetDropDownPost$Json({ buildCaseId: this.buildCaseId }).subscribe({\r\n      next: (response) => {\r\n        console.log('GetDropDown API response:', response);\r\n        if (response.Entries) {\r\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n          console.log('Converted buildingData:', this.buildingData);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading building data from API:', error);\r\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\r\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\r\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：將 API 回應轉換為建築物資料格式\r\n  private convertApiResponseToBuildingData(entries: any): any {\r\n    const buildingData: any = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]: [string, any]) => {\r\n      buildingData[building] = houses.map((house: any) => ({\r\n        code: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseName: house.HouseName,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  // 新增：將戶別清單轉換為建築物資料格式\r\n  convertHouseHoldListToBuildingData(houseHoldList: string[]): any {\r\n    if (!houseHoldList || houseHoldList.length === 0) {\r\n      return {};\r\n    }\r\n\r\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\r\n    const buildingData: any = {};\r\n\r\n    houseHoldList.forEach(household => {\r\n      // 嘗試從戶別名稱中提取建築物代碼\r\n      const buildingMatch = household.match(/^([A-Z]+)/);\r\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\r\n\r\n      if (!buildingData[building]) {\r\n        buildingData[building] = [];\r\n      }\r\n\r\n      // 計算樓層（假設每4戶為一層）\r\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\r\n      const floor = Math.ceil(houseNumber / 4);\r\n\r\n      buildingData[building].push({\r\n        code: household,\r\n        building: building,\r\n        floor: `${floor}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      });\r\n    }); return buildingData;\r\n  }\r\n\r\n  houseHoldList: any[];\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n\r\n          // 載入建築物資料 (只呼叫一次 GetDropDown API)\r\n          this.loadBuildingDataFromAPI();\r\n\r\n          this.getListFormItem()\r\n        } else {\r\n          console.error('getListRegularNoticeFileHouseHold failed:', res);\r\n        }\r\n      })\r\n    ).subscribe({\r\n      error: (error) => {\r\n        console.error('getListRegularNoticeFileHouseHold error:', error);\r\n      }\r\n    })\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  // UI優化相關方法\r\n\r\n  /**\r\n   * 檢查項目是否已完成\r\n   */\r\n  isItemCompleted(formItemReq: ExtendedSaveListFormItemReq): boolean {\r\n    // 檢查必填欄位是否都已填寫\r\n    const hasItemName = !!formItemReq.CItemName && formItemReq.CItemName.trim() !== '';\r\n    const hasUiType = !!formItemReq.selectedCUiType && formItemReq.selectedCUiType.value;\r\n    const hasRequireAnswer = !!formItemReq.CRequireAnswer && formItemReq.CRequireAnswer > 0;\r\n\r\n    // 如果是建材選樣類型，檢查是否有選擇備註類型\r\n    let hasRemarkType = true;\r\n    if (formItemReq.selectedCUiType?.value === 3) {\r\n      hasRemarkType = !!formItemReq.selectedRemarkType &&\r\n        Object.values(formItemReq.selectedRemarkType).some(selected => selected);\r\n    }\r\n\r\n    return hasItemName && hasUiType && hasRequireAnswer && hasRemarkType;\r\n  }\r\n\r\n  /**\r\n   * 獲取已完成項目數量\r\n   */\r\n  getCompletedItemsCount(): number {\r\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\r\n    return this.arrListFormItemReq.filter(item => this.isItemCompleted(item)).length;\r\n  }\r\n\r\n  /**\r\n   * 獲取進度百分比\r\n   */\r\n  getProgressPercentage(): number {\r\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\r\n    const completed = this.getCompletedItemsCount();\r\n    return Math.round((completed / this.arrListFormItemReq.length) * 100);\r\n  }\r\n\r\n  /**\r\n   * 滾動到指定項目\r\n   */\r\n  scrollToItem(index: number): void {\r\n    const element = document.getElementById(`form-item-${index}`);\r\n    if (element) {\r\n      element.scrollIntoView({\r\n        behavior: 'smooth',\r\n        block: 'start',\r\n        inline: 'nearest'\r\n      });\r\n\r\n      // 添加高亮效果\r\n      element.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-75');\r\n      setTimeout(() => {\r\n        element.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-75');\r\n      }, 2000);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到第一個未完成的項目\r\n   */\r\n  scrollToFirstIncompleteItem(): void {\r\n    if (!this.arrListFormItemReq) return;\r\n\r\n    const firstIncompleteIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\r\n    if (firstIncompleteIndex !== -1) {\r\n      this.scrollToItem(firstIncompleteIndex);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到第一個有錯誤的項目\r\n   */\r\n  scrollToFirstErrorItem(): void {\r\n    if (!this.arrListFormItemReq) return;\r\n\r\n    // 找到第一個有錯誤的項目\r\n    const firstErrorIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\r\n    if (firstErrorIndex !== -1) {\r\n      this.scrollToItem(firstErrorIndex);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到頂部\r\n   */\r\n  scrollToTop(): void {\r\n    window.scrollTo(0, 0);\r\n  }\r\n\r\n  /**\r\n   * 回到頂部\r\n   */\r\n  goToTop(): void {\r\n    console.log('goToTop clicked - method called');\r\n\r\n    // 優先滾動到頁面最頂部的header區塊\r\n    const headerElement = document.querySelector('nb-card-header') ||\r\n      document.querySelector('.card-header') ||\r\n      document.querySelector('nb-card') ||\r\n      document.querySelector('.header') ||\r\n      document.querySelector('h1, h2, h3') ||\r\n      document.body.firstElementChild;\r\n\r\n    if (headerElement) {\r\n      (headerElement as HTMLElement).scrollIntoView({\r\n        behavior: 'smooth',\r\n        block: 'start'\r\n      });\r\n      console.log('Scrolled to header element:', headerElement.tagName);\r\n\r\n      // 額外向上滾動一點，確保header完全可見\r\n      setTimeout(() => {\r\n        window.scrollBy({\r\n          top: -50, // 向上滾動50px\r\n          behavior: 'smooth'\r\n        });\r\n      }, 500);\r\n      return;\r\n    }\r\n\r\n    // 備用方案：滾動到第一個表單項目的上方\r\n    if (this.arrListFormItemReq && this.arrListFormItemReq.length > 0) {\r\n      const firstElement = document.getElementById('form-item-0');\r\n      if (firstElement) {\r\n        firstElement.scrollIntoView({\r\n          behavior: 'smooth',\r\n          block: 'start'\r\n        });\r\n\r\n        // 向上滾動更多距離，確保看到header\r\n        setTimeout(() => {\r\n          window.scrollBy({\r\n            top: -200, // 向上滾動200px\r\n            behavior: 'smooth'\r\n          });\r\n        }, 500);\r\n        console.log('Scrolled to first form item with extra offset');\r\n        return;\r\n      }\r\n    }\r\n\r\n    // 最後的備用方法\r\n    console.log('Using fallback scroll methods');\r\n    window.scrollTo({ top: 0, behavior: 'smooth' });\r\n  }\r\n\r\n  /**\r\n   * 測試按鈕點擊事件\r\n   */\r\n  testButtonClick(message: string): void {\r\n    console.log(message);\r\n  }\r\n\r\n  /**\r\n   * 滾動到底部\r\n   */\r\n  scrollToBottom(): void {\r\n    console.log('=== 至底功能被點擊 ===');\r\n\r\n    // 立即顯示一個簡短的視覺反饋\r\n    const button = document.querySelector('button[title=\"到底部\"]') as HTMLElement;\r\n    if (button) {\r\n      button.style.transform = 'scale(0.95)';\r\n      setTimeout(() => {\r\n        button.style.transform = '';\r\n      }, 150);\r\n    }\r\n\r\n    // 嘗試多種方法確保滾動到底部\r\n    setTimeout(() => {\r\n      // 方法1: 滾動到最後一個表單項目\r\n      if (this.filteredArrListFormItemReq && this.filteredArrListFormItemReq.length > 0) {\r\n        const lastIndex = this.filteredArrListFormItemReq.length - 1;\r\n        const lastElement = document.getElementById(`form-item-${lastIndex}`);\r\n        if (lastElement) {\r\n          console.log(`找到最後一個表單項目: form-item-${lastIndex}`);\r\n          lastElement.scrollIntoView({\r\n            behavior: 'smooth',\r\n            block: 'end',\r\n            inline: 'nearest'\r\n          });\r\n\r\n          // 額外向下滾動一點，確保完全看到底部\r\n          setTimeout(() => {\r\n            window.scrollBy({\r\n              top: 100,\r\n              behavior: 'smooth'\r\n            });\r\n          }, 500);\r\n\r\n          console.log('已滾動到最後一個表單項目');\r\n          return;\r\n        }\r\n      }\r\n\r\n      // 方法2: 滾動到頁面絕對底部\r\n      console.log('使用備用方法：滾動到頁面絕對底部');\r\n      const scrollHeight = Math.max(\r\n        document.body.scrollHeight,\r\n        document.body.offsetHeight,\r\n        document.documentElement.clientHeight,\r\n        document.documentElement.scrollHeight,\r\n        document.documentElement.offsetHeight\r\n      );\r\n\r\n      console.log('滾動高度:', scrollHeight);\r\n\r\n      window.scrollTo({\r\n        top: scrollHeight,\r\n        behavior: 'smooth'\r\n      });\r\n    }, 100);\r\n  }\r\n\r\n  /**\r\n   * 切換項目收合狀態\r\n   */\r\n  toggleItemCollapse(formItemReq: ExtendedSaveListFormItemReq): void {\r\n    formItemReq.isCollapsed = !formItemReq.isCollapsed;\r\n  }\r\n\r\n  /**\r\n   * 全部展開\r\n   */\r\n  expandAll(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = false;\r\n      });\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 全部收合\r\n   */\r\n  collapseAll(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = true;\r\n      });\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 只展開未完成的項目\r\n   */\r\n  expandIncompleteOnly(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = this.isItemCompleted(item);\r\n      });\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 搜尋功能\r\n   */\r\n  onSearch(): void {\r\n    if (!this.searchQuery.trim()) {\r\n      this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\r\n    } else {\r\n      const query = this.searchQuery.toLowerCase().trim();\r\n      this.filteredArrListFormItemReq = this.arrListFormItemReq.filter(item => {\r\n        return (\r\n          item.CName?.toLowerCase().includes(query) ||\r\n          item.CPart?.toLowerCase().includes(query) ||\r\n          item.CLocation?.toLowerCase().includes(query) ||\r\n          item.CItemName?.toLowerCase().includes(query)\r\n        );\r\n      });\r\n    }\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  /**\r\n   * 清除搜尋\r\n   */\r\n  clearSearch(): void {\r\n    this.searchQuery = '';\r\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  /**\r\n   * 更新過濾列表\r\n   */\r\n  private updateFilteredList(): void {\r\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\r\n    if (this.searchQuery.trim()) {\r\n      this.onSearch();\r\n    }\r\n  }\r\n\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\"> <nb-card class=\"shadow-xl border-0 rounded-xl\">\r\n    <nb-card-header class=\"bg-white border-b border-gray-200 p-6\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"w-1 h-8 bg-green-500 rounded-full\"></div>\r\n          <div>\r\n            <ngx-breadcrumb></ngx-breadcrumb>\r\n          </div>\r\n        </div>\r\n        <div class=\"flex items-center space-x-2\">\r\n          <span class=\"px-3 py-1 text-sm bg-green-100 text-green-800 rounded-full font-medium\">\r\n            {{ dynamicTitle }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"p-6 bg-gray-50\">\r\n      <div class=\"space-y-8\">\r\n        <!-- Page Title Section -->\r\n        <div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\r\n          <div class=\"flex items-center justify-between\">\r\n            <div class=\"flex items-center space-x-3\">\r\n              <div class=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n                <svg class=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                    d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\">\r\n                  </path>\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <h3 class=\"text-xl font-bold text-gray-800\">{{ dynamicTitle }}</h3>\r\n                <p class=\"text-sm text-gray-600 mt-1\">管理選樣項目的詳細設定與配置</p>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Progress Indicator -->\r\n            <div class=\"flex items-center space-x-4\">\r\n              <div class=\"text-right\">\r\n                <div class=\"text-sm font-medium text-gray-700\">\r\n                  總計：{{ arrListFormItemReq?.length || 0 }} 個項目\r\n                </div>\r\n              </div>\r\n\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Search Bar -->\r\n          <div class=\"mt-4 mb-4\" *ngIf=\"arrListFormItemReq.length > 0\">\r\n            <div class=\"flex items-center space-x-3\">\r\n              <div class=\"flex-1 relative\">\r\n                <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <svg class=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                      d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n                  </svg>\r\n                </div>\r\n                <input type=\"text\" [(ngModel)]=\"searchQuery\" (input)=\"onSearch()\" placeholder=\"搜尋項目名稱、部位、位置...\"\r\n                  class=\"block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\" />\r\n                <button *ngIf=\"searchQuery\" (click)=\"clearSearch()\"\r\n                  class=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\">\r\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\">\r\n                    </path>\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 搜尋結果統計 -->\r\n              <div class=\"text-sm text-gray-600\" *ngIf=\"searchQuery\">\r\n                找到 {{ filteredArrListFormItemReq.length }} / {{ arrListFormItemReq.length }} 個項目\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Enhanced Header Controls -->\r\n          <div class=\"mt-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 border border-gray-200\"\r\n            *ngIf=\"arrListFormItemReq.length > 0\">\r\n            <div class=\"flex items-center justify-between flex-wrap gap-4\">\r\n              <!-- Left Side: Navigation & Actions -->\r\n              <div class=\"flex items-center flex-wrap gap-3\">\r\n\r\n\r\n                <!-- Collapse Controls Group -->\r\n                <div class=\"flex items-center gap-2\">\r\n                  <span class=\"text-sm font-medium text-gray-700\">展開控制:</span>\r\n                  <div class=\"flex items-center bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\r\n                    <button\r\n                      class=\"px-3 py-2 text-sm bg-white hover:bg-purple-50 text-purple-700 transition-colors border-r border-gray-200 group\"\r\n                      (click)=\"expandAll()\" title=\"全部展開\">\r\n                      <svg class=\"w-4 h-4 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\">\r\n                        </path>\r\n                      </svg>\r\n                    </button>\r\n                    <button\r\n                      class=\"px-3 py-2 text-sm bg-white hover:bg-purple-50 text-purple-700 transition-colors border-r border-gray-200 group\"\r\n                      (click)=\"collapseAll()\" title=\"全部收合\">\r\n                      <svg class=\"w-4 h-4 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"></path>\r\n                      </svg>\r\n                    </button>\r\n\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Right Side: Tips -->\r\n              <div class=\"flex items-center gap-4\">\r\n                <!-- Tips -->\r\n                <div\r\n                  class=\"hidden lg:flex items-center gap-2 text-xs text-gray-500 bg-blue-50 rounded-lg px-3 py-2 border border-blue-200\">\r\n                  <svg class=\"w-4 h-4 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                      d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  使用右側浮動按鈕快速操作\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div> <!-- Form Items Section -->\r\n\r\n        <!-- 無搜尋結果提示 -->\r\n        <div *ngIf=\"searchQuery && filteredArrListFormItemReq.length === 0\" class=\"text-center py-12\">\r\n          <div class=\"bg-gray-50 rounded-xl p-8\">\r\n            <svg class=\"w-16 h-16 text-gray-400 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n            </svg>\r\n            <h3 class=\"text-lg font-medium text-gray-900 mb-2\">找不到符合條件的項目</h3>\r\n            <p class=\"text-gray-500 mb-4\">請嘗試調整搜尋關鍵字或清除搜尋條件</p>\r\n            <button (click)=\"clearSearch()\"\r\n              class=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\">\r\n              清除搜尋\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <ng-container *ngFor=\"let formItemReq of filteredArrListFormItemReq; let idx = index\">\r\n          <div [id]=\"'form-item-' + idx\"\r\n            class=\"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-xl\">\r\n            <!-- Item Header -->\r\n            <div class=\"bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200\">\r\n              <div class=\"flex items-center justify-between\">\r\n                <div class=\"flex items-center space-x-3\">\r\n                  <!-- 收合/展開按鈕 -->\r\n                  <button\r\n                    class=\"w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\"\r\n                    (click)=\"toggleItemCollapse(formItemReq)\" [title]=\"formItemReq.isCollapsed ? '展開項目' : '收合項目'\">\r\n                    <svg class=\"w-4 h-4 text-gray-600 transition-transform duration-200\"\r\n                      [class.rotate-180]=\"formItemReq.isCollapsed\" fill=\"none\" stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"></path>\r\n                    </svg>\r\n                  </button>\r\n\r\n                  <div\r\n                    class=\"w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\r\n                    {{idx + 1}}\r\n                  </div>\r\n                  <div class=\"flex-1\">\r\n                    <h4 class=\"text-lg font-semibold text-gray-800\">\r\n                      {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n                    </h4>\r\n                    <p class=\"text-sm text-gray-600\">項目編號 #{{idx + 1}}</p>\r\n                  </div>\r\n                </div>\r\n                <div class=\"flex items-center space-x-3\">\r\n                  <!-- UI Type Badge -->\r\n                  <span class=\"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full\">\r\n                    {{formItemReq.selectedCUiType?.label || '未設定'}}\r\n                  </span>\r\n\r\n                  <!-- Item Navigation -->\r\n                  <div class=\"flex items-center space-x-1\">\r\n                    <button *ngIf=\"idx > 0\"\r\n                      class=\"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\"\r\n                      (click)=\"scrollToItem(idx - 1)\" title=\"上一個項目\">\r\n                      <svg class=\"w-4 h-4 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\">\r\n                        </path>\r\n                      </svg>\r\n                    </button>\r\n                    <button *ngIf=\"idx < arrListFormItemReq.length - 1\"\r\n                      class=\"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\"\r\n                      (click)=\"scrollToItem(idx + 1)\" title=\"下一個項目\">\r\n                      <svg class=\"w-4 h-4 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Main Content Area -->\r\n            <div class=\"p-6\" *ngIf=\"!formItemReq.isCollapsed\">\r\n              <div class=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\r\n\r\n                <!-- Material Images Section (Enhanced) -->\r\n                <div class=\"lg:col-span-1\">\r\n                  <div class=\"space-y-4\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">主要材料示意</label>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0\" class=\"relative\">\r\n                      <!-- Enhanced Image carousel container -->\r\n                      <div\r\n                        class=\"aspect-square w-full relative overflow-hidden rounded-xl border-2 border-gray-200 cursor-pointer group shadow-md hover:shadow-lg transition-all duration-300\"\r\n                        (click)=\"openImageModal(formItemReq)\">\r\n                        <img class=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\r\n                          [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n                        <!-- Enhanced Zoom overlay -->\r\n                        <div\r\n                          class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center\">\r\n                          <div class=\"transform scale-75 group-hover:scale-100 transition-transform duration-300\">\r\n                            <div class=\"w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center\">\r\n                              <svg class=\"w-6 h-6 text-gray-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                                  d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"></path>\r\n                              </svg>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- Navigation buttons -->\r\n                        <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n                          (click)=\"prevImage(formItemReq); $event.stopPropagation()\" title=\"上一張圖片\">\r\n                          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\">\r\n                            </path>\r\n                          </svg>\r\n                        </button>\r\n\r\n                        <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n                          (click)=\"nextImage(formItemReq); $event.stopPropagation()\" title=\"下一張圖片\">\r\n                          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\">\r\n                            </path>\r\n                          </svg>\r\n                        </button>\r\n\r\n                        <!-- Enhanced Image counter -->\r\n                        <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\">\r\n                          {{(formItemReq.currentImageIndex || 0) + 1}} / {{formItemReq.CMatrialUrl.length}}\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- Enhanced Thumbnail navigation -->\r\n                      <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\" class=\"flex gap-2 mt-3 overflow-x-auto pb-2\">\r\n                        <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n                          class=\"flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\"\r\n                          [class.border-blue-500]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          [class.border-gray-300]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n                          [class.ring-2]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          [class.ring-blue-200]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          (click)=\"openImageModal(formItemReq, i)\" [title]=\"'點選放大第 ' + (i + 1) + ' 張圖片'\">\r\n                          <img class=\"w-full h-full object-cover transition-transform hover:scale-110\"\r\n                            [src]=\"imageUrl | base64Image\">\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"!formItemReq.CMatrialUrl || formItemReq.CMatrialUrl.length === 0\"\r\n                      class=\"aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\">\r\n                      <svg class=\"w-12 h-12 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-sm\">無主要材料示意</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Form Fields Section (Enhanced) -->\r\n                <div class=\"lg:col-span-2\">\r\n                  <div class=\"space-y-6\">\r\n                    <div class=\"flex items-center space-x-2 mb-4\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">基本設定</label>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Form Groups -->\r\n                    <div class=\"space-y-4\">\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'CItemName_' + idx\"\r\n                          class=\"block text-sm font-medium text-gray-700 mb-2\">項目名稱</label>\r\n                        <div class=\"flex items-center space-x-3 bg-gray-50 p-3 rounded-lg border border-gray-200\">\r\n                          <span\r\n                            class=\"text-sm text-gray-600 font-medium px-2 py-1 bg-blue-100 rounded-md whitespace-nowrap\">\r\n                            {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}:\r\n                          </span>\r\n                          <input type=\"text\" [id]=\"'CItemName_' + idx\"\r\n                            class=\"flex-1 border-0 bg-transparent focus:ring-2 focus:ring-blue-500 focus:border-transparent rounded-md p-2\"\r\n                            nbInput [(ngModel)]=\"formItemReq.CItemName\" placeholder=\"例如：廚房檯面\"\r\n                            [disabled]=\"listFormItem?.CIsLock ?? false\" />\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'cRequireAnswer_' + idx\"\r\n                          class=\"block text-sm font-medium text-gray-700 mb-2\">必填數量</label>\r\n                        <div class=\"relative\">\r\n                          <input type=\"number\" [id]=\"'cRequireAnswer_' + idx\"\r\n                            class=\"w-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 rounded-lg p-3 transition-all duration-200\"\r\n                            nbInput placeholder=\"輸入數量\" [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n                            [disabled]=\"formItemReq.selectedCUiType.value === 3 || (listFormItem?.CIsLock ?? false)\" />\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'uiType_' + idx\"\r\n                          class=\"block text-sm font-medium text-gray-700 mb-2\">前台UI類型</label>\r\n                        <nb-select placeholder=\"選擇UI類型\" [id]=\"'uiType_' + idx\" [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n                          class=\"w-full border-2 border-gray-200 focus:border-blue-500 rounded-lg transition-all duration-200\"\r\n                          (selectedChange)=\"changeSelectCUiType(formItemReq)\"\r\n                          [disabled]=\"listFormItem?.CIsLock ?? false\">\r\n                          <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                            {{ case.label }}\r\n                          </nb-option>\r\n                        </nb-select>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Concept Design Section (Enhanced) -->\r\n                <div class=\"lg:col-span-1\">\r\n                  <div class=\"space-y-4\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">概念設計</label>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Upload Button -->\r\n                    <button\r\n                      class=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      [disabled]=\"listFormItem?.CIsLock\" (click)=\"inputFile.click()\">\r\n                      <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\">\r\n                        </path>\r\n                      </svg>\r\n                      <span>上傳概念設計圖</span>\r\n                    </button>\r\n                    <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n                      accept=\"image/png, image/gif, image/jpeg\">\r\n\r\n                    <!-- Enhanced Uploaded Pictures List -->\r\n                    <div *ngIf=\"formItemReq.listPictures && formItemReq.listPictures.length > 0\" class=\"space-y-3\">\r\n                      <div *ngFor=\"let picture of formItemReq.listPictures; let i = index\"\r\n                        class=\"bg-gray-50 border border-gray-200 p-3 rounded-lg hover:shadow-md transition-all duration-200\">\r\n                        <div class=\"relative group\">\r\n                          <img class=\"w-full h-32 object-cover rounded-lg mb-3 border border-gray-200\"\r\n                            [src]=\"picture.data\">\r\n                          <div\r\n                            class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg\">\r\n                          </div>\r\n                        </div>\r\n                        <input nbInput\r\n                          class=\"w-full p-2 text-sm mb-2 border border-gray-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                          type=\"text\" placeholder=\"圖片說明/檔名\" [value]=\"picture.name\"\r\n                          (blur)=\"renameFile($event, i, formItemReq)\" [disabled]=\"listFormItem?.CIsLock ?? false\">\r\n                        <button\r\n                          class=\"w-full bg-red-100 hover:bg-red-200 text-red-700 font-medium py-2 px-3 rounded-md transition-colors duration-200 text-sm\"\r\n                          (click)=\"removeImage(picture.id, formItemReq)\" [disabled]=\"listFormItem?.CIsLock ?? false\">\r\n                          <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                              d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\">\r\n                            </path>\r\n                          </svg>\r\n                          刪除圖片\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Default Concept Design Image -->\r\n                    <div class=\"space-y-2\"\r\n                      *ngIf=\"formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\">\r\n                      <label class=\"block text-xs font-medium text-gray-600\">預設概念圖</label>\r\n                      <div class=\"relative group\">\r\n                        <img class=\"w-full h-32 object-cover rounded-lg border border-gray-200\"\r\n                          [src]=\"formItemReq.CDesignFileUrl | base64Image\">\r\n                        <div\r\n                          class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg\">\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div\r\n                      *ngIf=\"!formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\"\r\n                      class=\"h-32 w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 text-gray-400\">\r\n                      <svg class=\"w-8 h-8 mb-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-xs\">無概念設計圖</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Enhanced Separator -->\r\n              <div class=\"my-8\">\r\n                <div class=\"relative\">\r\n                  <div class=\"absolute inset-0 flex items-center\">\r\n                    <div class=\"w-full border-t border-gray-300\"></div>\r\n                  </div>\r\n                  <div class=\"relative flex justify-center text-sm\">\r\n                    <span class=\"px-4 bg-white text-gray-500 font-medium\">設定選項</span>\r\n                  </div>\r\n                </div>\r\n              </div> <!-- Enhanced Applicable Households Section -->\r\n              <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                <div class=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\r\n                  <div class=\"flex items-center space-x-2 mb-4\">\r\n                    <svg class=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                        d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\">\r\n                      </path>\r\n                    </svg>\r\n                    <h5 class=\"font-semibold text-blue-800\">適用戶型</h5>\r\n                  </div> <!-- 新的戶別選擇器 -->\r\n                  <app-household-binding [buildingData]=\"buildingData\" [placeholder]=\"'請選擇適用戶型'\"\r\n                    [disabled]=\"listFormItem?.CIsLock ?? false\" [allowBatchSelect]=\"true\"\r\n                    [ngModel]=\"formItemReq.selectedHouseholdsCached\"\r\n                    (selectionChange)=\"onHouseholdSelectionChange(extractHouseholdCodes($event), formItemReq)\"\r\n                    class=\"w-full\" [useHouseNameMode]=\"true\">\r\n                  </app-household-binding>\r\n\r\n                  <!-- 無戶別資料時的顯示 -->\r\n                  <div class=\"text-center py-4\" *ngIf=\"houseHoldList.length === 0\">\r\n                    <svg class=\"w-8 h-8 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                        d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\">\r\n                      </path>\r\n                    </svg>\r\n                    <span class=\"text-gray-500 text-sm\">尚無戶別資料</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Enhanced Remark Options Section -->\r\n                <div class=\"bg-orange-50 p-4 rounded-lg border border-orange-200\"\r\n                  *ngIf=\"formItemReq.selectedCUiType.value === 3\">\r\n                  <div class=\"flex items-center space-x-2 mb-4\">\r\n                    <svg class=\"w-5 h-5 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                        d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\">\r\n                      </path>\r\n                    </svg>\r\n                    <h5 class=\"font-semibold text-orange-800\">備註選項</h5>\r\n                  </div>\r\n\r\n                  <div class=\"grid grid-cols-1 gap-2\"\r\n                    *ngIf=\"CRemarkTypeOptions && CRemarkTypeOptions.length > 0; else noRemarkOptions\">\r\n                    <label *ngFor=\"let remark of CRemarkTypeOptions\"\r\n                      class=\"flex items-center cursor-pointer hover:bg-orange-100 p-2 rounded-md transition-colors\">\r\n                      <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\"\r\n                        [(checked)]=\"formItemReq.selectedRemarkType[remark]\" [disabled]=\"listFormItem?.CIsLock\"\r\n                        value=\"item\" (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\" class=\"mr-3\">\r\n                      </nb-checkbox>\r\n                      <span class=\"text-gray-700\">{{ remark }}</span>\r\n                    </label>\r\n                  </div>\r\n\r\n                  <ng-template #noRemarkOptions>\r\n                    <div class=\"text-center py-4\">\r\n                      <svg class=\"w-8 h-8 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-gray-500 text-sm\">尚無備註選項</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <!-- Simplified Footer with Info Only -->\r\n    <nb-card-footer class=\"bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4 sticky bottom-0 shadow-lg z-30\">\r\n      <div class=\"flex items-center justify-center\">\r\n        <div class=\"flex items-center space-x-6 text-sm text-gray-600\">\r\n          <div class=\"flex items-center space-x-2\">\r\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n            </svg>\r\n            <span>共 {{arrListFormItemReq.length || 0}} 個選樣項目</span>\r\n          </div>\r\n\r\n          <div class=\"flex items-center space-x-4\">\r\n            <div class=\"flex items-center space-x-2\">\r\n              <div class=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\r\n              <span>總計 {{ arrListFormItemReq.length || 0 }} 個項目</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"text-xs text-gray-500\">\r\n            使用右側懸浮按鈕進行操作\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</div>\r\n\r\n<!-- Enhanced Image Modal for each formItemReq -->\r\n<ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n  <div *ngIf=\"formItemReq.isModalOpen\"\r\n    class=\"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fade-in-up\"\r\n    (click)=\"closeImageModal(formItemReq)\" (keydown)=\"onKeydown($event, formItemReq)\" tabindex=\"0\">\r\n\r\n    <!-- Enhanced Close Button -->\r\n    <button\r\n      class=\"modal-close-btn fixed top-6 right-6 z-[60] bg-red-500 bg-opacity-95 hover:bg-red-600 hover:bg-opacity-100 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-2xl\"\r\n      (click)=\"closeImageModal(formItemReq)\" title=\"關閉圖片檢視 (按 ESC 鍵)\">\r\n      <svg class=\"w-7 h-7\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n      </svg>\r\n    </button>\r\n\r\n    <!-- Enhanced Modal Content -->\r\n    <div class=\"relative max-w-7xl max-h-full w-full h-full flex items-center justify-center animate-slide-in-left\"\r\n      (click)=\"$event.stopPropagation()\">\r\n\r\n      <!-- Main Image Container -->\r\n      <div class=\"relative max-w-full max-h-full bg-white rounded-2xl p-2 shadow-2xl\">\r\n        <img class=\"max-w-full max-h-[85vh] object-contain rounded-xl animate-fade-in-up\"\r\n          [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n        <!-- Enhanced Navigation Buttons -->\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"modal-nav-btn absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\"\r\n          (click)=\"prevImageModal(formItemReq)\" title=\"上一張圖片 (按 ← 鍵)\">\r\n          <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\"></path>\r\n          </svg>\r\n        </button>\r\n\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"modal-nav-btn absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\"\r\n          (click)=\"nextImageModal(formItemReq)\" title=\"下一張圖片 (按 → 鍵)\">\r\n          <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\"></path>\r\n          </svg>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Enhanced Image Counter -->\r\n      <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n        class=\"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\">\r\n        <div class=\"flex items-center space-x-3\">\r\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n              d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n            </path>\r\n          </svg>\r\n          <span class=\"font-medium text-lg\">{{(formItemReq.currentImageIndex || 0) + 1}} /\r\n            {{formItemReq.CMatrialUrl.length}}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Enhanced Image Info -->\r\n      <div\r\n        class=\"absolute bottom-6 right-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-lg text-sm backdrop-blur-sm shadow-lg\">\r\n        <div class=\"flex items-center space-x-2\">\r\n          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n              d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n          </svg>\r\n          <span class=\"font-medium\">{{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Enhanced Thumbnail Strip for Modal -->\r\n      <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n        class=\"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\">\r\n        <div class=\"flex gap-3 overflow-x-auto max-w-[80vw] modal-thumbnails\">\r\n          <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n            class=\"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105\"\r\n            [class.border-white]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.border-gray-400]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-3]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-white]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-opacity-50]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            (click)=\"formItemReq.currentImageIndex = i\" [title]=\"'跳至第 ' + (i + 1) + ' 張圖片'\">\r\n            <img class=\"w-full h-full object-cover transition-transform duration-200\" [src]=\"imageUrl | base64Image\">\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- Right Side Floating Action Buttons -->\r\n<div class=\"fixed right-6 top-1/2 transform -translate-y-1/2 z-50 flex flex-col space-y-3\">\r\n  <!-- Save Button -->\r\n  <div class=\"relative\">\r\n    <button\r\n      class=\"w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-full shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center group disabled:opacity-50 disabled:cursor-not-allowed\"\r\n      [class.animate-pulse]=\"isSubmitting\" (click)=\"onSubmit()\"\r\n      [disabled]=\"((listFormItem?.CIsLock ?? false) || isSubmitting)\" title=\"儲存變更\">\r\n\r\n      <!-- Loading Spinner -->\r\n      <svg *ngIf=\"isSubmitting\" class=\"w-6 h-6 animate-spin\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n          d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\">\r\n        </path>\r\n      </svg>\r\n\r\n      <!-- Save Icon -->\r\n      <svg *ngIf=\"!isSubmitting\" class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"></path>\r\n      </svg>\r\n    </button>\r\n\r\n    <!-- Tooltip -->\r\n    <div\r\n      class=\"absolute right-20 bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n      {{ isSubmitting ? '儲存中...' : '儲存變更' }}\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Copy to New Form Button (only show when locked) -->\r\n  <button *ngIf=\"listFormItem?.CIsLock\"\r\n    class=\"w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center group\"\r\n    (click)=\"copyToNewForm()\" [disabled]=\"isSubmitting\" title=\"複製到新表單\">\r\n    <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n        d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\">\r\n      </path>\r\n    </svg>\r\n\r\n    <!-- Tooltip -->\r\n    <div\r\n      class=\"absolute right-16 bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n      複製到新表單\r\n    </div>\r\n  </button>\r\n\r\n  <!-- Cancel Button -->\r\n  <button\r\n    class=\"w-14 h-14 bg-gray-500 hover:bg-gray-600 text-white rounded-full shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center group\"\r\n    (click)=\"goBack()\" [disabled]=\"isSubmitting\" title=\"取消並返回\">\r\n    <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"></path>\r\n    </svg>\r\n\r\n    <!-- Tooltip -->\r\n    <div\r\n      class=\"absolute right-16 bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n      取消\r\n    </div>\r\n  </button>\r\n\r\n  <!-- Scroll to Top Button -->\r\n  <button\r\n    class=\"w-14 h-14 bg-purple-500 hover:bg-purple-600 text-white rounded-full shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center group\"\r\n    (click)=\"goToTop()\" title=\"回到頂部\">\r\n    <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 10l7-7m0 0l7 7m-7-7v18\"></path>\r\n    </svg>\r\n\r\n    <!-- Tooltip -->\r\n    <div\r\n      class=\"absolute right-16 bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n      回到頂部\r\n    </div>\r\n  </button>\r\n\r\n  <!-- Scroll to Bottom Button -->\r\n  <button\r\n    class=\"w-14 h-14 bg-indigo-500 hover:bg-indigo-600 text-white rounded-full shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center group relative z-10\"\r\n    (click)=\"scrollToBottom()\" (mousedown)=\"console.log('至底按鈕被按下')\" (mouseup)=\"console.log('至底按鈕被釋放')\" title=\"到底部\"\r\n    style=\"pointer-events: auto;\">\r\n    <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 14l-7 7m0 0l-7-7m7 7V3\"></path>\r\n    </svg>\r\n\r\n    <!-- Tooltip -->\r\n    <div\r\n      class=\"absolute right-16 bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n      到底部\r\n    </div>\r\n  </button>\r\n\r\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAI1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASA,YAAY,IAAIC,eAAe,QAAQ,8BAA8B;AAC9E,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAAuBC,MAAM,QAAQ,uCAAuC;AAC5E,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;;;;IC2CjDC,EAAA,CAAAC,cAAA,iBAC8F;IADlED,EAAA,CAAAE,UAAA,mBAAAC,8FAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;;IAEjDT,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAU,SAAA,eACO;IAEXV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;IAIXX,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,mBAAAR,MAAA,CAAAS,0BAAA,CAAAC,MAAA,SAAAV,MAAA,CAAAW,kBAAA,CAAAD,MAAA,yBACF;;;;;;IApBEhB,EAHN,CAAAC,cAAA,cAA6D,cAClB,cACV,cACuD;;IAChFD,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,eACyD;IAE7DV,EADE,CAAAW,YAAA,EAAM,EACF;;IACNX,EAAA,CAAAC,cAAA,gBAC2I;IADxHD,EAAA,CAAAkB,gBAAA,2BAAAC,4FAAAC,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiB,GAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsB,kBAAA,CAAAhB,MAAA,CAAAiB,WAAA,EAAAH,MAAA,MAAAd,MAAA,CAAAiB,WAAA,GAAAH,MAAA;MAAA,OAAApB,EAAA,CAAAQ,WAAA,CAAAY,MAAA;IAAA,EAAyB;IAACpB,EAAA,CAAAE,UAAA,mBAAAsB,oFAAA;MAAAxB,EAAA,CAAAI,aAAA,CAAAiB,GAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmB,QAAA,EAAU;IAAA,EAAC;IAAjEzB,EAAA,CAAAW,YAAA,EAC2I;IAC3IX,EAAA,CAAA0B,UAAA,IAAAC,qEAAA,qBAC8F;IAMhG3B,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA0B,UAAA,IAAAE,kEAAA,kBAAuD;IAI3D5B,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAhBmBX,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAA6B,gBAAA,YAAAvB,MAAA,CAAAiB,WAAA,CAAyB;IAEnCvB,EAAA,CAAAa,SAAA,EAAiB;IAAjBb,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAiB,WAAA,CAAiB;IAUQvB,EAAA,CAAAa,SAAA,EAAiB;IAAjBb,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAiB,WAAA,CAAiB;;;;;;IAgBjDvB,EATR,CAAAC,cAAA,cACwC,cACyB,cAEd,cAIR,eACa;IAAAD,EAAA,CAAAY,MAAA,gCAAK;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAE1DX,EADF,CAAAC,cAAA,cAAoG,iBAG7D;IAAnCD,EAAA,CAAAE,UAAA,mBAAA6B,qFAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2B,SAAA,EAAW;IAAA,EAAC;;IACrBjC,EAAA,CAAAC,cAAA,cACsB;IACpBD,EAAA,CAAAU,SAAA,eAEO;IAEXV,EADE,CAAAW,YAAA,EAAM,EACC;;IACTX,EAAA,CAAAC,cAAA,kBAEuC;IAArCD,EAAA,CAAAE,UAAA,mBAAAgC,sFAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6B,WAAA,EAAa;IAAA,EAAC;;IACvBnC,EAAA,CAAAC,cAAA,eACsB;IACpBD,EAAA,CAAAU,SAAA,gBAAgG;IAM1GV,EALQ,CAAAW,YAAA,EAAM,EACC,EAEL,EACF,EACF;;IAKJX,EAFF,CAAAC,cAAA,eAAqC,eAGsF;;IACvHD,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAU,SAAA,gBACuE;IACzEV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAY,MAAA,kFACF;IAGNZ,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;;;;;;IAKNX,EADF,CAAAC,cAAA,cAA8F,cACrD;;IACrCD,EAAA,CAAAC,cAAA,cAAwG;IACtGD,EAAA,CAAAU,SAAA,eACyD;IAC3DV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,aAAmD;IAAAD,EAAA,CAAAY,MAAA,mEAAU;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAClEX,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAY,MAAA,6GAAiB;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IACnDX,EAAA,CAAAC,cAAA,iBAC0F;IADlFD,EAAA,CAAAE,UAAA,mBAAAkC,qFAAA;MAAApC,EAAA,CAAAI,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAE7BT,EAAA,CAAAY,MAAA,iCACF;IAEJZ,EAFI,CAAAW,YAAA,EAAS,EACL,EACF;;;;;;IAuCMX,EAAA,CAAAC,cAAA,iBAEgD;IAA9CD,EAAA,CAAAE,UAAA,mBAAAoC,wGAAA;MAAAtC,EAAA,CAAAI,aAAA,CAAAmC,GAAA;MAAA,MAAAC,MAAA,GAAAxC,EAAA,CAAAO,aAAA,GAAAkC,KAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoC,YAAA,CAAAF,MAAA,GAAmB,CAAC,CAAC;IAAA,EAAC;;IAC/BxC,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,eACO;IAEXV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;;IACTX,EAAA,CAAAC,cAAA,iBAEgD;IAA9CD,EAAA,CAAAE,UAAA,mBAAAyC,wGAAA;MAAA3C,EAAA,CAAAI,aAAA,CAAAwC,IAAA;MAAA,MAAAJ,MAAA,GAAAxC,EAAA,CAAAO,aAAA,GAAAkC,KAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoC,YAAA,CAAAF,MAAA,GAAmB,CAAC,CAAC;IAAA,EAAC;;IAC/BxC,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,eAA8F;IAElGV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;IA2BLX,EAAA,CAAAU,SAAA,eAC0F;;;;;;IAAxFV,EAAA,CAAA8B,UAAA,QAAA9B,EAAA,CAAA6C,WAAA,OAAAvC,MAAA,CAAAwC,eAAA,CAAAC,cAAA,IAAA/C,EAAA,CAAAgD,aAAA,CAAkD;;;;;;IAgBpDhD,EAAA,CAAAC,cAAA,kBAE2E;IAAzED,EAAA,CAAAE,UAAA,mBAAA+C,oHAAA7B,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAA8C,IAAA;MAAA,MAAAH,cAAA,GAAA/C,EAAA,CAAAO,aAAA,IAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA8C,SAAA,CAAAL,cAAA,CAAsB;MAAA,OAAA/C,EAAA,CAAAQ,WAAA,CAAEY,MAAA,CAAAiC,eAAA,EAAwB;IAAA,EAAC;;IAC1DrD,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAU,SAAA,gBACO;IAEXV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;;IAETX,EAAA,CAAAC,cAAA,kBAE2E;IAAzED,EAAA,CAAAE,UAAA,mBAAAoD,oHAAAlC,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAmD,IAAA;MAAA,MAAAR,cAAA,GAAA/C,EAAA,CAAAO,aAAA,IAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAkD,SAAA,CAAAT,cAAA,CAAsB;MAAA,OAAA/C,EAAA,CAAAQ,WAAA,CAAEY,MAAA,CAAAiC,eAAA,EAAwB;IAAA,EAAC;;IAC1DrD,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAU,SAAA,gBACO;IAEXV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;IAGTX,EAAA,CAAAC,cAAA,eACoH;IAClHD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,OAAAiC,cAAA,CAAAU,iBAAA,mBAAAV,cAAA,CAAAW,WAAA,CAAA1C,MAAA,MACF;;;;;;IAKAhB,EAAA,CAAAC,cAAA,kBAMiF;IAA/ED,EAAA,CAAAE,UAAA,mBAAAyD,2HAAA;MAAA,MAAAC,KAAA,GAAA5D,EAAA,CAAAI,aAAA,CAAAyD,IAAA,EAAApB,KAAA;MAAA,MAAAM,cAAA,GAAA/C,EAAA,CAAAO,aAAA,IAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,cAAA,CAAAf,cAAA,EAAAa,KAAA,CAA8B;IAAA,EAAC;IACxC5D,EAAA,CAAAU,SAAA,eACiC;;IACnCV,EAAA,CAAAW,YAAA,EAAS;;;;;;IAJPX,EAHA,CAAA+D,WAAA,oBAAAH,KAAA,MAAAb,cAAA,CAAAU,iBAAA,OAAoE,oBAAAG,KAAA,MAAAb,cAAA,CAAAU,iBAAA,OACA,WAAAG,KAAA,MAAAb,cAAA,CAAAU,iBAAA,OACT,kBAAAG,KAAA,MAAAb,cAAA,CAAAU,iBAAA,OACO;IACzBzD,EAAA,CAAA8B,UAAA,+CAAA8B,KAAA,8BAAqC;IAE5E5D,EAAA,CAAAa,SAAA,EAA8B;IAA9Bb,EAAA,CAAA8B,UAAA,QAAA9B,EAAA,CAAA6C,WAAA,QAAAmB,YAAA,GAAAhE,EAAA,CAAAgD,aAAA,CAA8B;;;;;IATpChD,EAAA,CAAAC,cAAA,eAA6F;IAC3FD,EAAA,CAAA0B,UAAA,IAAAuC,kGAAA,uBAMiF;IAInFjE,EAAA,CAAAW,YAAA,EAAM;;;;IAVyBX,EAAA,CAAAa,SAAA,EAA4B;IAA5Bb,EAAA,CAAA8B,UAAA,YAAAiB,cAAA,CAAAW,WAAA,CAA4B;;;;;;IA/C3D1D,EAFF,CAAAC,cAAA,cAA4F,eAIlD;IAAtCD,EAAA,CAAAE,UAAA,mBAAAgE,wGAAA;MAAAlE,EAAA,CAAAI,aAAA,CAAA+D,IAAA;MAAA,MAAApB,cAAA,GAAA/C,EAAA,CAAAO,aAAA,IAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,cAAA,CAAAf,cAAA,CAA2B;IAAA,EAAC;IACrC/C,EAAA,CAAA0B,UAAA,IAAA0C,wFAAA,mBAC0F;IAMtFpE,EAHJ,CAAAC,cAAA,eACwI,eAC9C,eACM;;IAC1FD,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAU,SAAA,gBACmF;IAI3FV,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;IAsBNX,EAnBA,CAAA0B,UAAA,IAAA2C,2FAAA,sBAE2E,IAAAC,2FAAA,sBASA,KAAAC,yFAAA,mBASyC;IAGtHvE,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA0B,UAAA,KAAA8C,yFAAA,mBAA6F;IAY/FxE,EAAA,CAAAW,YAAA,EAAM;;;;;IAtDoDX,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAwC,eAAA,CAAAC,cAAA,EAAkC;IAgB/E/C,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAA8B,UAAA,SAAAiB,cAAA,CAAAW,WAAA,CAAA1C,MAAA,KAAwC;IASxChB,EAAA,CAAAa,SAAA,EAAwC;IAAxCb,EAAA,CAAA8B,UAAA,SAAAiB,cAAA,CAAAW,WAAA,CAAA1C,MAAA,KAAwC;IAU3ChB,EAAA,CAAAa,SAAA,EAAwC;IAAxCb,EAAA,CAAA8B,UAAA,SAAAiB,cAAA,CAAAW,WAAA,CAAA1C,MAAA,KAAwC;IAO1ChB,EAAA,CAAAa,SAAA,EAAwC;IAAxCb,EAAA,CAAA8B,UAAA,SAAAiB,cAAA,CAAAW,WAAA,CAAA1C,MAAA,KAAwC;;;;;IAchDhB,EAAA,CAAAC,cAAA,eACoJ;;IAClJD,EAAA,CAAAC,cAAA,eAAkF;IAChFD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAY,MAAA,iDAAO;IAC/BZ,EAD+B,CAAAW,YAAA,EAAO,EAChC;;;;;IAmDAX,EAAA,CAAAC,cAAA,qBAA8D;IAC5DD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAY;;;;IAFmCX,EAAA,CAAA8B,UAAA,UAAA2C,QAAA,CAAc;IAC3DzE,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAA0E,kBAAA,MAAAD,QAAA,CAAAE,KAAA,MACF;;;;;;IAqCF3E,EAFF,CAAAC,cAAA,eACuG,eACzE;IAG1BD,EAFA,CAAAU,SAAA,eACuB,eAGjB;IACRV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,iBAG0F;IAAxFD,EAAA,CAAAE,UAAA,kBAAA0E,gHAAAxD,MAAA;MAAA,MAAAyD,KAAA,GAAA7E,EAAA,CAAAI,aAAA,CAAA0E,IAAA,EAAArC,KAAA;MAAA,MAAAM,cAAA,GAAA/C,EAAA,CAAAO,aAAA,IAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAyE,UAAA,CAAA3D,MAAA,EAAAyD,KAAA,EAAA9B,cAAA,CAAkC;IAAA,EAAC;IAH7C/C,EAAA,CAAAW,YAAA,EAG0F;IAC1FX,EAAA,CAAAC,cAAA,kBAE6F;IAA3FD,EAAA,CAAAE,UAAA,mBAAA8E,kHAAA;MAAA,MAAAC,WAAA,GAAAjF,EAAA,CAAAI,aAAA,CAAA0E,IAAA,EAAA3B,SAAA;MAAA,MAAAJ,cAAA,GAAA/C,EAAA,CAAAO,aAAA,IAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4E,WAAA,CAAAD,WAAA,CAAAE,EAAA,EAAApC,cAAA,CAAoC;IAAA,EAAC;;IAC9C/C,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAY,MAAA,iCACF;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;;;;;;;IAnBAX,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAA8B,UAAA,QAAAmD,WAAA,CAAAG,IAAA,EAAApF,EAAA,CAAAgD,aAAA,CAAoB;IAOYhD,EAAA,CAAAa,SAAA,GAAsB;IACZb,EADV,CAAA8B,UAAA,UAAAmD,WAAA,CAAAI,IAAA,CAAsB,cAAAC,QAAA,GAAAhF,MAAA,CAAAiF,YAAA,kBAAAjF,MAAA,CAAAiF,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SAC+B;IAGxCtF,EAAA,CAAAa,SAAA,EAA2C;IAA3Cb,EAAA,CAAA8B,UAAA,cAAA4D,QAAA,GAAApF,MAAA,CAAAiF,YAAA,kBAAAjF,MAAA,CAAAiF,YAAA,CAAAC,OAAA,cAAAE,QAAA,KAAAD,SAAA,GAAAC,QAAA,SAA2C;;;;;IAhBhG1F,EAAA,CAAAC,cAAA,eAA+F;IAC7FD,EAAA,CAAA0B,UAAA,IAAAiE,yFAAA,mBACuG;IAuBzG3F,EAAA,CAAAW,YAAA,EAAM;;;;IAxBqBX,EAAA,CAAAa,SAAA,EAA6B;IAA7Bb,EAAA,CAAA8B,UAAA,YAAAiB,cAAA,CAAA6C,YAAA,CAA6B;;;;;IA6BtD5F,EAFF,CAAAC,cAAA,eAC6G,iBACpD;IAAAD,EAAA,CAAAY,MAAA,qCAAK;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;IACpEX,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAU,SAAA,eACmD;;IACnDV,EAAA,CAAAU,SAAA,eAEM;IAEVV,EADE,CAAAW,YAAA,EAAM,EACF;;;;IALAX,EAAA,CAAAa,SAAA,GAAgD;IAAhDb,EAAA,CAAA8B,UAAA,QAAA9B,EAAA,CAAA6C,WAAA,OAAAE,cAAA,CAAA8C,cAAA,GAAA7F,EAAA,CAAAgD,aAAA,CAAgD;;;;;IAOtDhD,EAAA,CAAAC,cAAA,eAE2I;;IACzID,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAY,MAAA,2CAAM;IAC9BZ,EAD8B,CAAAW,YAAA,EAAO,EAC/B;;;;;IAkCRX,EAAA,CAAAC,cAAA,eAAiE;;IAC/DD,EAAA,CAAAC,cAAA,eACsB;IACpBD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAY,MAAA,2CAAM;IAC5CZ,EAD4C,CAAAW,YAAA,EAAO,EAC7C;;;;;;IAmBFX,EAAA,CAAAC,cAAA,uBAEkG;IADhGD,EAAA,CAAAkB,gBAAA,2BAAA4E,qJAAA1E,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAA2F,IAAA;MAAA,MAAAC,UAAA,GAAAhG,EAAA,CAAAO,aAAA,GAAA4C,SAAA;MAAA,MAAAJ,cAAA,GAAA/C,EAAA,CAAAO,aAAA,IAAA4C,SAAA;MAAAnD,EAAA,CAAAsB,kBAAA,CAAAyB,cAAA,CAAAkD,kBAAA,CAAAD,UAAA,GAAA5E,MAAA,MAAA2B,cAAA,CAAAkD,kBAAA,CAAAD,UAAA,IAAA5E,MAAA;MAAA,OAAApB,EAAA,CAAAQ,WAAA,CAAAY,MAAA;IAAA,EAAoD;IACvCpB,EAAA,CAAAE,UAAA,2BAAA4F,qJAAA1E,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAA2F,IAAA;MAAA,MAAAC,UAAA,GAAAhG,EAAA,CAAAO,aAAA,GAAA4C,SAAA;MAAA,MAAAJ,cAAA,GAAA/C,EAAA,CAAAO,aAAA,IAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAA4F,sBAAA,CAAA9E,MAAA,EAAA4E,UAAA,EAAAjD,cAAA,CAAmD;IAAA,EAAC;IACpF/C,EAAA,CAAAW,YAAA,EAAc;;;;;;IAFZX,EAAA,CAAA6B,gBAAA,YAAAkB,cAAA,CAAAkD,kBAAA,CAAAD,UAAA,EAAoD;IAAChG,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAiF,YAAA,kBAAAjF,MAAA,CAAAiF,YAAA,CAAAC,OAAA,CAAkC;;;;;IAH3FxF,EAAA,CAAAC,cAAA,iBACgG;IAC9FD,EAAA,CAAA0B,UAAA,IAAAyE,+GAAA,2BAEkG;IAElGnG,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAY,MAAA,GAAY;IAC1CZ,EAD0C,CAAAW,YAAA,EAAO,EACzC;;;;;IALQX,EAAA,CAAAa,SAAA,EAAoC;IAApCb,EAAA,CAAA8B,UAAA,SAAAiB,cAAA,CAAAkD,kBAAA,CAAoC;IAItBjG,EAAA,CAAAa,SAAA,GAAY;IAAZb,EAAA,CAAAoG,iBAAA,CAAAJ,UAAA,CAAY;;;;;IAR5ChG,EAAA,CAAAC,cAAA,eACoF;IAClFD,EAAA,CAAA0B,UAAA,IAAA2E,iGAAA,qBACgG;IAOlGrG,EAAA,CAAAW,YAAA,EAAM;;;;IARsBX,EAAA,CAAAa,SAAA,EAAqB;IAArBb,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAAgG,kBAAA,CAAqB;;;;;IAW/CtG,EAAA,CAAAC,cAAA,eAA8B;;IAC5BD,EAAA,CAAAC,cAAA,eACsB;IACpBD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAY,MAAA,2CAAM;IAC5CZ,EAD4C,CAAAW,YAAA,EAAO,EAC7C;;;;;IA9BRX,EAFF,CAAAC,cAAA,eACkD,eACF;;IAC5CD,EAAA,CAAAC,cAAA,eAA2F;IACzFD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAY,MAAA,+BAAI;IAChDZ,EADgD,CAAAW,YAAA,EAAK,EAC/C;IAcNX,EAZA,CAAA0B,UAAA,IAAA6E,yFAAA,mBACoF,IAAAC,iGAAA,gCAAAxG,EAAA,CAAAyG,sBAAA,CAWtD;IAWhCzG,EAAA,CAAAW,YAAA,EAAM;;;;;IAtBDX,EAAA,CAAAa,SAAA,GAA2D;IAAAb,EAA3D,CAAA8B,UAAA,SAAAxB,MAAA,CAAAgG,kBAAA,IAAAhG,MAAA,CAAAgG,kBAAA,CAAAtF,MAAA,KAA2D,aAAA0F,mBAAA,CAAoB;;;;;;IAjRhF1G,EANR,CAAAC,cAAA,cAAkD,cACG,cAGtB,cACF,aACoB;;IACvCD,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,iBAAmD;IAAAD,EAAA,CAAAY,MAAA,2CAAM;IAC3DZ,EAD2D,CAAAW,YAAA,EAAQ,EAC7D;IAgENX,EA9DA,CAAA0B,UAAA,IAAAiF,kFAAA,oBAA4F,KAAAC,mFAAA,mBA+DwD;IASxJ5G,EADE,CAAAW,YAAA,EAAM,EACF;IAKFX,EAFJ,CAAAC,cAAA,gBAA2B,gBACF,gBACyB;;IAC5CD,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,kBAAmD;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IACzDZ,EADyD,CAAAW,YAAA,EAAQ,EAC3D;IAKFX,EAFJ,CAAAC,cAAA,eAAuB,gBACF,kBAEsC;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;IAEjEX,EADF,CAAAC,cAAA,gBAA0F,iBAEO;IAC7FD,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,kBAGgD;IADtCD,EAAA,CAAAkB,gBAAA,2BAAA2F,6GAAAzF,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAA/D,cAAA,GAAA/C,EAAA,CAAAO,aAAA,GAAA4C,SAAA;MAAAnD,EAAA,CAAAsB,kBAAA,CAAAyB,cAAA,CAAAgE,SAAA,EAAA3F,MAAA,MAAA2B,cAAA,CAAAgE,SAAA,GAAA3F,MAAA;MAAA,OAAApB,EAAA,CAAAQ,WAAA,CAAAY,MAAA;IAAA,EAAmC;IAGjDpB,EALI,CAAAW,YAAA,EAGgD,EAC5C,EACF;IAGJX,EADF,CAAAC,cAAA,gBAAmB,kBAEsC;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;IAEjEX,EADF,CAAAC,cAAA,eAAsB,kBAIyE;IADhED,EAAA,CAAAkB,gBAAA,2BAAA8F,6GAAA5F,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAA/D,cAAA,GAAA/C,EAAA,CAAAO,aAAA,GAAA4C,SAAA;MAAAnD,EAAA,CAAAsB,kBAAA,CAAAyB,cAAA,CAAAkE,cAAA,EAAA7F,MAAA,MAAA2B,cAAA,CAAAkE,cAAA,GAAA7F,MAAA;MAAA,OAAApB,EAAA,CAAAQ,WAAA,CAAAY,MAAA;IAAA,EAAwC;IAGzEpB,EALI,CAAAW,YAAA,EAG6F,EACzF,EACF;IAGJX,EADF,CAAAC,cAAA,gBAAmB,kBAEsC;IAAAD,EAAA,CAAAY,MAAA,kCAAM;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;IACrEX,EAAA,CAAAC,cAAA,sBAG8C;IAHSD,EAAA,CAAAkB,gBAAA,2BAAAgG,iHAAA9F,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAA/D,cAAA,GAAA/C,EAAA,CAAAO,aAAA,GAAA4C,SAAA;MAAAnD,EAAA,CAAAsB,kBAAA,CAAAyB,cAAA,CAAAoE,eAAA,EAAA/F,MAAA,MAAA2B,cAAA,CAAAoE,eAAA,GAAA/F,MAAA;MAAA,OAAApB,EAAA,CAAAQ,WAAA,CAAAY,MAAA;IAAA,EAAyC;IAE9FpB,EAAA,CAAAE,UAAA,4BAAAkH,kHAAA;MAAApH,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAA/D,cAAA,GAAA/C,EAAA,CAAAO,aAAA,GAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAkBF,MAAA,CAAA+G,mBAAA,CAAAtE,cAAA,CAAgC;IAAA,EAAC;IAEnD/C,EAAA,CAAA0B,UAAA,KAAA4F,yFAAA,yBAA8D;IAOxEtH,EAJQ,CAAAW,YAAA,EAAY,EACR,EACF,EACF,EACF;IAKFX,EAFJ,CAAAC,cAAA,eAA2B,eACF,cACoB;;IACvCD,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAU,SAAA,iBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,kBAAmD;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IACzDZ,EADyD,CAAAW,YAAA,EAAQ,EAC3D;IAGNX,EAAA,CAAAC,cAAA,mBAEiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAqH,sGAAA;MAAAvH,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAU,aAAA,GAAAxH,EAAA,CAAAyH,WAAA;MAAA,OAAAzH,EAAA,CAAAQ,WAAA,CAASgH,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;;IAC9D1H,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAU,SAAA,iBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,kDAAO;IACfZ,EADe,CAAAW,YAAA,EAAO,EACb;IACTX,EAAA,CAAAC,cAAA,qBAC4C;IADCD,EAAA,CAAAE,UAAA,oBAAAyH,sGAAAvG,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAA/D,cAAA,GAAA/C,EAAA,CAAAO,aAAA,GAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAsH,WAAA,CAAAxG,MAAA,EAAA2B,cAAA,CAAgC;IAAA,EAAC;IAAxF/C,EAAA,CAAAW,YAAA,EAC4C;IA2C5CX,EAxCA,CAAA0B,UAAA,KAAAmG,mFAAA,mBAA+F,KAAAC,mFAAA,mBA6Bc,KAAAC,mFAAA,mBAa8B;IAUjJ/H,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;IAKFX,EAFJ,CAAAC,cAAA,gBAAkB,eACM,gBAC4B;IAC9CD,EAAA,CAAAU,SAAA,gBAAmD;IACrDV,EAAA,CAAAW,YAAA,EAAM;IAEJX,EADF,CAAAC,cAAA,gBAAkD,iBACM;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAGhEZ,EAHgE,CAAAW,YAAA,EAAO,EAC7D,EACF,EACF;IAGFX,EAFJ,CAAAC,cAAA,gBAAmD,gBACa,gBACd;;IAC5CD,EAAA,CAAAC,cAAA,gBAAyF;IACvFD,EAAA,CAAAU,SAAA,iBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAC9CZ,EAD8C,CAAAW,YAAA,EAAK,EAC7C;IACNX,EAAA,CAAAC,cAAA,kCAI2C;IADzCD,EAAA,CAAAE,UAAA,6BAAA8H,+HAAA5G,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAA/D,cAAA,GAAA/C,EAAA,CAAAO,aAAA,GAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAmBF,MAAA,CAAA2H,0BAAA,CAA2B3H,MAAA,CAAA4H,qBAAA,CAAA9G,MAAA,CAA6B,EAAA2B,cAAA,CAAc;IAAA,EAAC;IAE5F/C,EAAA,CAAAW,YAAA,EAAwB;IAGxBX,EAAA,CAAA0B,UAAA,KAAAyG,mFAAA,mBAAiE;IASnEnI,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA0B,UAAA,KAAA0G,mFAAA,mBACkD;IAmCtDpI,EADE,CAAAW,YAAA,EAAM,EACF;;;;;;;;;;;IAhSQX,EAAA,CAAAa,SAAA,GAAmE;IAAnEb,EAAA,CAAA8B,UAAA,SAAAiB,cAAA,CAAAW,WAAA,IAAAX,cAAA,CAAAW,WAAA,CAAA1C,MAAA,KAAmE;IA8DnEhB,EAAA,CAAAa,SAAA,EAAsE;IAAtEb,EAAA,CAAA8B,UAAA,UAAAiB,cAAA,CAAAW,WAAA,IAAAX,cAAA,CAAAW,WAAA,CAAA1C,MAAA,OAAsE;IA2BjEhB,EAAA,CAAAa,SAAA,IAA0B;IAA1Bb,EAAA,CAAA8B,UAAA,uBAAAU,MAAA,CAA0B;IAK7BxC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAqI,kBAAA,MAAAtF,cAAA,CAAAuF,KAAA,OAAAvF,cAAA,CAAAwF,KAAA,OAAAxF,cAAA,CAAAyF,SAAA,OACF;IACmBxI,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAA8B,UAAA,sBAAAU,MAAA,CAAyB;IAElCxC,EAAA,CAAA6B,gBAAA,YAAAkB,cAAA,CAAAgE,SAAA,CAAmC;IAC3C/G,EAAA,CAAA8B,UAAA,cAAAwD,QAAA,GAAAhF,MAAA,CAAAiF,YAAA,kBAAAjF,MAAA,CAAAiF,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SAA2C;IAKxCtF,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAA8B,UAAA,4BAAAU,MAAA,CAA+B;IAGfxC,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAA8B,UAAA,2BAAAU,MAAA,CAA8B;IAEtBxC,EAAA,CAAA6B,gBAAA,YAAAkB,cAAA,CAAAkE,cAAA,CAAwC;IACnEjH,EAAA,CAAA8B,UAAA,aAAAiB,cAAA,CAAAoE,eAAA,CAAAsB,KAAA,YAAAC,QAAA,GAAApI,MAAA,CAAAiF,YAAA,kBAAAjF,MAAA,CAAAiF,YAAA,CAAAC,OAAA,cAAAkD,QAAA,KAAAjD,SAAA,GAAAiD,QAAA,UAAwF;IAKrF1I,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAA8B,UAAA,oBAAAU,MAAA,CAAuB;IAEExC,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAA8B,UAAA,mBAAAU,MAAA,CAAsB;IAACxC,EAAA,CAAA6B,gBAAA,YAAAkB,cAAA,CAAAoE,eAAA,CAAyC;IAG9FnH,EAAA,CAAA8B,UAAA,cAAA6G,QAAA,GAAArI,MAAA,CAAAiF,YAAA,kBAAAjF,MAAA,CAAAiF,YAAA,CAAAC,OAAA,cAAAmD,QAAA,KAAAlD,SAAA,GAAAkD,QAAA,SAA2C;IACf3I,EAAA,CAAAa,SAAA,EAAiB;IAAjBb,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAAsI,cAAA,CAAiB;IAwBjD5I,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAiF,YAAA,kBAAAjF,MAAA,CAAAiF,YAAA,CAAAC,OAAA,CAAkC;IAY9BxF,EAAA,CAAAa,SAAA,GAAqE;IAArEb,EAAA,CAAA8B,UAAA,SAAAiB,cAAA,CAAA6C,YAAA,IAAA7C,cAAA,CAAA6C,YAAA,CAAA5E,MAAA,KAAqE;IA6BxEhB,EAAA,CAAAa,SAAA,EAAwG;IAAxGb,EAAA,CAAA8B,UAAA,SAAAiB,cAAA,CAAA8C,cAAA,MAAA9C,cAAA,CAAA6C,YAAA,IAAA7C,cAAA,CAAA6C,YAAA,CAAA5E,MAAA,QAAwG;IAYxGhB,EAAA,CAAAa,SAAA,EAAyG;IAAzGb,EAAA,CAAA8B,UAAA,UAAAiB,cAAA,CAAA8C,cAAA,MAAA9C,cAAA,CAAA6C,YAAA,IAAA7C,cAAA,CAAA6C,YAAA,CAAA5E,MAAA,QAAyG;IAkCvFhB,EAAA,CAAAa,SAAA,IAA6B;IAInCb,EAJM,CAAA8B,UAAA,iBAAAxB,MAAA,CAAAuI,YAAA,CAA6B,6DAA0B,cAAAC,QAAA,GAAAxI,MAAA,CAAAiF,YAAA,kBAAAjF,MAAA,CAAAiF,YAAA,CAAAC,OAAA,cAAAsD,QAAA,KAAArD,SAAA,GAAAqD,QAAA,SACjC,0BAA0B,YAAA/F,cAAA,CAAAgG,wBAAA,CACrB,0BAER;IAIX/I,EAAA,CAAAa,SAAA,EAAgC;IAAhCb,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA0I,aAAA,CAAAhI,MAAA,OAAgC;IAa9DhB,EAAA,CAAAa,SAAA,EAA6C;IAA7Cb,EAAA,CAAA8B,UAAA,SAAAiB,cAAA,CAAAoE,eAAA,CAAAsB,KAAA,OAA6C;;;;;;IAtUxDzI,EAAA,CAAAiJ,uBAAA,GAAsF;IAQ5EjJ,EAPR,CAAAC,cAAA,cAC2H,cAE9B,aAC1C,cACJ,iBAIyD;IAA9FD,EAAA,CAAAE,UAAA,mBAAAgJ,8FAAA;MAAA,MAAAnG,cAAA,GAAA/C,EAAA,CAAAI,aAAA,CAAA+I,GAAA,EAAAhG,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8I,kBAAA,CAAArG,cAAA,CAA+B;IAAA,EAAC;;IACzC/C,EAAA,CAAAC,cAAA,cAEsB;IACpBD,EAAA,CAAAU,SAAA,eAAgG;IAEpGV,EADE,CAAAW,YAAA,EAAM,EACC;;IAETX,EAAA,CAAAC,cAAA,cACyG;IACvGD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;IAEJX,EADF,CAAAC,cAAA,eAAoB,cAC8B;IAC9CD,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAY,MAAA,IAAiB;IAEtDZ,EAFsD,CAAAW,YAAA,EAAI,EAClD,EACF;IAGJX,EAFF,CAAAC,cAAA,eAAyC,gBAEgC;IACrED,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAGPX,EAAA,CAAAC,cAAA,eAAyC;IASvCD,EARA,CAAA0B,UAAA,KAAA2H,+EAAA,qBAEgD,KAAAC,+EAAA,qBAQA;IAQxDtJ,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;IAGNX,EAAA,CAAA0B,UAAA,KAAA6H,4EAAA,oBAAkD;IAgTpDvJ,EAAA,CAAAW,YAAA,EAAM;;;;;;;IAzWDX,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAA8B,UAAA,sBAAAU,MAAA,CAAyB;IASsBxC,EAAA,CAAAa,SAAA,GAAmD;IAAnDb,EAAA,CAAA8B,UAAA,UAAAiB,cAAA,CAAAyG,WAAA,2DAAmD;IAE3FxJ,EAAA,CAAAa,SAAA,EAA4C;IAA5Cb,EAAA,CAAA+D,WAAA,eAAAhB,cAAA,CAAAyG,WAAA,CAA4C;IAQ9CxJ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0E,kBAAA,MAAAlC,MAAA,UACF;IAGIxC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAqI,kBAAA,MAAAtF,cAAA,CAAAuF,KAAA,OAAAvF,cAAA,CAAAwF,KAAA,OAAAxF,cAAA,CAAAyF,SAAA,MACF;IACiCxI,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAA0E,kBAAA,+BAAAlC,MAAA,SAAiB;IAMlDxC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0E,kBAAA,OAAA3B,cAAA,CAAAoE,eAAA,kBAAApE,cAAA,CAAAoE,eAAA,CAAAxC,KAAA,+BACF;IAIW3E,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAA8B,UAAA,SAAAU,MAAA,KAAa;IAQbxC,EAAA,CAAAa,SAAA,EAAyC;IAAzCb,EAAA,CAAA8B,UAAA,SAAAU,MAAA,GAAAlC,MAAA,CAAAW,kBAAA,CAAAD,MAAA,KAAyC;IAaxChB,EAAA,CAAAa,SAAA,EAA8B;IAA9Bb,EAAA,CAAA8B,UAAA,UAAAiB,cAAA,CAAAyG,WAAA,CAA8B;;;;;IAsWpDxJ,EAAA,CAAAU,SAAA,eAC0F;;;;;;IAAxFV,EAAA,CAAA8B,UAAA,QAAA9B,EAAA,CAAA6C,WAAA,OAAAvC,MAAA,CAAAwC,eAAA,CAAA2G,eAAA,IAAAzJ,EAAA,CAAAgD,aAAA,CAAkD;;;;;;IAGpDhD,EAAA,CAAAC,cAAA,kBAE8D;IAA5DD,EAAA,CAAAE,UAAA,mBAAAwJ,6GAAA;MAAA1J,EAAA,CAAAI,aAAA,CAAAuJ,IAAA;MAAA,MAAAF,eAAA,GAAAzJ,EAAA,CAAAO,aAAA,IAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsJ,cAAA,CAAAH,eAAA,CAA2B;IAAA,EAAC;;IACrCzJ,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAU,SAAA,gBAAmG;IAEvGV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;;IAETX,EAAA,CAAAC,cAAA,kBAE8D;IAA5DD,EAAA,CAAAE,UAAA,mBAAA2J,6GAAA;MAAA7J,EAAA,CAAAI,aAAA,CAAA0J,IAAA;MAAA,MAAAL,eAAA,GAAAzJ,EAAA,CAAAO,aAAA,IAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyJ,cAAA,CAAAN,eAAA,CAA2B;IAAA,EAAC;;IACrCzJ,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAU,SAAA,gBAAgG;IAEpGV,EADE,CAAAW,YAAA,EAAM,EACC;;;;;IAMTX,EAFF,CAAAC,cAAA,eACqJ,cAC1G;;IACvCD,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAY,MAAA,GACE;IAExCZ,EAFwC,CAAAW,YAAA,EAAO,EACvC,EACF;;;;IAHgCX,EAAA,CAAAa,SAAA,GACE;IADFb,EAAA,CAAAc,kBAAA,MAAA2I,eAAA,CAAAhG,iBAAA,mBAAAgG,eAAA,CAAA/F,WAAA,CAAA1C,MAAA,KACE;;;;;;IAoBpChB,EAAA,CAAAC,cAAA,kBAOkF;IAAhFD,EAAA,CAAAE,UAAA,mBAAA8J,oHAAA;MAAA,MAAAC,KAAA,GAAAjK,EAAA,CAAAI,aAAA,CAAA8J,IAAA,EAAAzH,KAAA;MAAA,MAAAgH,eAAA,GAAAzJ,EAAA,CAAAO,aAAA,IAAA4C,SAAA;MAAA,OAAAnD,EAAA,CAAAQ,WAAA,CAAAiJ,eAAA,CAAAhG,iBAAA,GAAAwG,KAAA;IAAA,EAA2C;IAC3CjK,EAAA,CAAAU,SAAA,eAAyG;;IAC3GV,EAAA,CAAAW,YAAA,EAAS;;;;;;IAHPX,EAJA,CAAA+D,WAAA,iBAAAkG,KAAA,MAAAR,eAAA,CAAAhG,iBAAA,OAAiE,oBAAAwG,KAAA,MAAAR,eAAA,CAAAhG,iBAAA,OACG,WAAAwG,KAAA,MAAAR,eAAA,CAAAhG,iBAAA,OACT,eAAAwG,KAAA,MAAAR,eAAA,CAAAhG,iBAAA,OACI,oBAAAwG,KAAA,MAAAR,eAAA,CAAAhG,iBAAA,OACK;IACxBzD,EAAA,CAAA8B,UAAA,mCAAAmI,KAAA,8BAAmC;IACLjK,EAAA,CAAAa,SAAA,EAA8B;IAA9Bb,EAAA,CAAA8B,UAAA,QAAA9B,EAAA,CAAA6C,WAAA,QAAAsH,YAAA,GAAAnK,EAAA,CAAAgD,aAAA,CAA8B;;;;;IAT5GhD,EAFF,CAAAC,cAAA,eAC8I,eACtE;IACpED,EAAA,CAAA0B,UAAA,IAAA0I,2FAAA,uBAOkF;IAItFpK,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAX2BX,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAA8B,UAAA,YAAA2H,eAAA,CAAA/F,WAAA,CAA4B;;;;;;IAtEjE1D,EAAA,CAAAC,cAAA,eAEiG;IAAxDD,EAAvC,CAAAE,UAAA,mBAAAmK,iGAAA;MAAArK,EAAA,CAAAI,aAAA,CAAAkK,IAAA;MAAA,MAAAb,eAAA,GAAAzJ,EAAA,CAAAO,aAAA,GAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiK,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC,qBAAAe,mGAAApJ,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAkK,IAAA;MAAA,MAAAb,eAAA,GAAAzJ,EAAA,CAAAO,aAAA,GAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAmK,SAAA,CAAArJ,MAAA,EAAAqI,eAAA,CAA8B;IAAA,EAAC;IAGjFzJ,EAAA,CAAAC,cAAA,kBAEkE;IAAhED,EAAA,CAAAE,UAAA,mBAAAwK,oGAAA;MAAA1K,EAAA,CAAAI,aAAA,CAAAkK,IAAA;MAAA,MAAAb,eAAA,GAAAzJ,EAAA,CAAAO,aAAA,GAAA4C,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiK,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC;;IACtCzJ,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAU,SAAA,gBAAwG;IAE5GV,EADE,CAAAW,YAAA,EAAM,EACC;;IAGTX,EAAA,CAAAC,cAAA,eACqC;IAAnCD,EAAA,CAAAE,UAAA,mBAAAyK,iGAAAvJ,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAkK,IAAA;MAAA,OAAAtK,EAAA,CAAAQ,WAAA,CAASY,MAAA,CAAAiC,eAAA,EAAwB;IAAA,EAAC;IAGlCrD,EAAA,CAAAC,cAAA,eAAgF;IAa9ED,EAZA,CAAA0B,UAAA,IAAAkJ,iFAAA,mBAC0F,IAAAC,oFAAA,sBAK5B,IAAAC,oFAAA,sBAQA;IAKhE9K,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAA0B,UAAA,IAAAqJ,iFAAA,mBACqJ;IAenJ/K,EAFF,CAAAC,cAAA,gBACkJ,cACvG;;IACvCD,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAU,SAAA,gBACuE;IACzEV,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAY,MAAA,IAAqE;IAEnGZ,EAFmG,CAAAW,YAAA,EAAO,EAClG,EACF;IAGNX,EAAA,CAAA0B,UAAA,KAAAsJ,kFAAA,mBAC8I;IAelJhL,EADE,CAAAW,YAAA,EAAM,EACF;;;;;IA/DsDX,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAwC,eAAA,CAAA2G,eAAA,EAAkC;IAG/EzJ,EAAA,CAAAa,SAAA,EAAmE;IAAnEb,EAAA,CAAA8B,UAAA,SAAA2H,eAAA,CAAA/F,WAAA,IAAA+F,eAAA,CAAA/F,WAAA,CAAA1C,MAAA,KAAmE;IAQnEhB,EAAA,CAAAa,SAAA,EAAmE;IAAnEb,EAAA,CAAA8B,UAAA,SAAA2H,eAAA,CAAA/F,WAAA,IAAA+F,eAAA,CAAA/F,WAAA,CAAA1C,MAAA,KAAmE;IAUxEhB,EAAA,CAAAa,SAAA,EAAmE;IAAnEb,EAAA,CAAA8B,UAAA,SAAA2H,eAAA,CAAA/F,WAAA,IAAA+F,eAAA,CAAA/F,WAAA,CAAA1C,MAAA,KAAmE;IAqB3ChB,EAAA,CAAAa,SAAA,GAAqE;IAArEb,EAAA,CAAAqI,kBAAA,KAAAoB,eAAA,CAAAnB,KAAA,OAAAmB,eAAA,CAAAlB,KAAA,OAAAkB,eAAA,CAAAjB,SAAA,KAAqE;IAK7FxI,EAAA,CAAAa,SAAA,EAAmE;IAAnEb,EAAA,CAAA8B,UAAA,SAAA2H,eAAA,CAAA/F,WAAA,IAAA+F,eAAA,CAAA/F,WAAA,CAAA1C,MAAA,KAAmE;;;;;IApE/EhB,EAAA,CAAAiJ,uBAAA,GAA8E;IAC5EjJ,EAAA,CAAA0B,UAAA,IAAAuJ,2EAAA,oBAEiG;;;;;IAF3FjL,EAAA,CAAAa,SAAA,EAA6B;IAA7Bb,EAAA,CAAA8B,UAAA,SAAA2H,eAAA,CAAAyB,WAAA,CAA6B;;;;;;IAgG/BlL,EAAA,CAAAC,cAAA,eAA6G;IAC3GD,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;;;;;IAGNX,EAAA,CAAAC,cAAA,cAAiG;IAC/FD,EAAA,CAAAU,SAAA,gBAAgG;IAClGV,EAAA,CAAAW,YAAA,EAAM;;;;;;IAWVX,EAAA,CAAAC,cAAA,kBAEqE;IAAnED,EAAA,CAAAE,UAAA,mBAAAiL,wFAAA;MAAAnL,EAAA,CAAAI,aAAA,CAAAgL,IAAA;MAAA,MAAA9K,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+K,aAAA,EAAe;IAAA,EAAC;;IACzBrL,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAU,SAAA,gBAEO;IACTV,EAAA,CAAAW,YAAA,EAAM;;IAGNX,EAAA,CAAAC,cAAA,cAC8K;IAC5KD,EAAA,CAAAY,MAAA,6CACF;IACFZ,EADE,CAAAW,YAAA,EAAM,EACC;;;;IAZmBX,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAgL,YAAA,CAAyB;;;AD3lBvD,OAAM,MAAOC,4CAA6C,SAAQ3L,aAAa;EAC7E4L,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B,EAC3BC,aAA2B,EAC3BC,GAAsB;IAE9B,KAAK,CAACX,MAAM,CAAC;IAbL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,GAAG,GAAHA,GAAG;IAIb,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IACD;IACA,KAAAC,kBAAkB,GAAG,CACnB;MAAE7H,KAAK,EAAE,KAAK;MAAE8D,KAAK,EAAE1I,aAAa,CAAC0M;IAAG,CAAE,EAC1C;MAAE9H,KAAK,EAAE,KAAK;MAAE8D,KAAK,EAAE1I,aAAa,CAAC2M;IAAG,CAAE,CAC3C;IAiBD,KAAA9D,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAE9D,KAAK,EAAE;KAClB,EACD;MACE8D,KAAK,EAAE,CAAC;MAAE9D,KAAK,EAAE;KAClB,EAAE;MACD8D,KAAK,EAAE,CAAC;MAAE9D,KAAK,EAAE;KAClB,CAAC;IACJ,KAAA2B,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAEjC,KAAAgF,YAAY,GAAY,KAAK;IA4C7B,KAAAqB,aAAa,GAA+B,EAAE;IAC9C,KAAA1G,kBAAkB,GAA+B,EAAE;IAEnD;IACA,KAAA4C,YAAY,GAAQ,EAAE,CAAC,CAAC;IAiPxB,KAAAtD,YAAY,GAA8B,IAAI;IAC9C,KAAAqH,KAAK,GAAY,IAAI;IAqFrB,KAAA3L,kBAAkB,GAAkC,EAAE;IACtD,KAAAF,0BAA0B,GAAkC,EAAE;IAC9D,KAAAQ,WAAW,GAAW,EAAE;EA9ZxB;EAUA;EACA,IAAIsL,YAAYA,CAAA;IACd,MAAMC,MAAM,GAAG,IAAI,CAACN,kBAAkB,CAACO,IAAI,CAACD,MAAM,IAChDA,MAAM,CAACrE,KAAK,KAAK,IAAI,CAAC4D,iCAAiC,CAACE,WAAW,CACpE;IACD,OAAOO,MAAM,GAAG,QAAQA,MAAM,CAACnI,KAAK,EAAE,GAAG,WAAW;EACtD;EACA;EACAqI,cAAcA,CAACC,UAAyB;IACtC,IAAI,IAAI,CAACT,kBAAkB,CAACU,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAACrE,KAAK,KAAKwE,UAAU,CAAC,EAAE;MACvE,IAAI,CAACZ,iCAAiC,CAACE,WAAW,GAAGU,UAAU;MAC/D;MACA,IAAI,CAACZ,iCAAiC,CAACC,SAAS,GAAGW,UAAU;IAC/D;EACF;EAgBSE,QAAQA,CAAA;IACf,IAAI,CAACzB,KAAK,CAAC0B,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMrI,EAAE,GAAGoI,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAGtI,EAAE;QAErB,IAAI,IAAI,CAACsI,WAAW,GAAG,CAAC,EAAE;UACxB,IAAI,CAACC,iCAAiC,EAAE;QAC1C,CAAC,MAAM;UACL;UACAC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAACH,WAAW,CAAC;UACvD,IAAI,CAAC9B,OAAO,CAACkC,YAAY,CAAC,iBAAiB,CAAC;UAC5C,IAAI,CAACC,MAAM,EAAE;QACf;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACpC,KAAK,CAACqC,WAAW,CAACV,SAAS,CAACU,WAAW,IAAG;MAC7C,IAAIA,WAAW,CAAC,WAAW,CAAC,EAAE;QAC5B,MAAMC,SAAS,GAAG,CAACD,WAAW,CAAC,WAAW,CAAC;QAC3C,IAAI,CAACf,cAAc,CAACgB,SAAS,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAC,cAAcA,CAAC7F,KAAU,EAAE8F,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC/F,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO+F,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQA5G,WAAWA,CAAC6G,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAAC9I,YAAY,CAAC5E,MAAM,GAAG,CAAC,EAAE;UACxC0N,YAAY,CAAC9I,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BT,EAAE,EAAE,IAAIiK,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBhK,IAAI,EAAEsJ,IAAI,CAACtJ,IAAI,CAACiK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BlK,IAAI,EAAE8J,SAAS;YACfK,SAAS,EAAE,IAAI,CAACzD,eAAe,CAAC0D,gBAAgB,CAACb,IAAI,CAACtJ,IAAI,CAAC;YAC3DoK,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAAC9I,YAAY,CAAC8J,IAAI,CAAC;YAC7BvK,EAAE,EAAE,IAAIiK,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBhK,IAAI,EAAEsJ,IAAI,CAACtJ,IAAI,CAACiK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BlK,IAAI,EAAE8J,SAAS;YACfK,SAAS,EAAE,IAAI,CAACzD,eAAe,CAAC0D,gBAAgB,CAACb,IAAI,CAACtJ,IAAI,CAAC;YAC3DoK,KAAK,EAAEd;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACnG,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEAvD,WAAWA,CAACyK,SAAiB,EAAEjB,YAAiB;IAC9C,IAAIA,YAAY,CAAC9I,YAAY,CAAC5E,MAAM,EAAE;MACpC0N,YAAY,CAAC9I,YAAY,GAAG8I,YAAY,CAAC9I,YAAY,CAACgK,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC1K,EAAE,IAAIwK,SAAS,CAAC;IAC7F;EACF;EACA5K,UAAUA,CAAC0J,KAAU,EAAEhM,KAAa,EAAEiM,YAAiB;IACrD,IAAIoB,IAAI,GAAGpB,YAAY,CAAC9I,YAAY,CAACnD,KAAK,CAAC,CAACgN,KAAK,CAACM,KAAK,CAAC,CAAC,EAAErB,YAAY,CAAC9I,YAAY,CAACnD,KAAK,CAAC,CAACgN,KAAK,CAACO,IAAI,EAAEtB,YAAY,CAAC9I,YAAY,CAACnD,KAAK,CAAC,CAACgN,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACG,MAAM,CAACnG,KAAK,GAAG,GAAG,GAAGiG,YAAY,CAAC9I,YAAY,CAACnD,KAAK,CAAC,CAAC8M,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEvB,YAAY,CAAC9I,YAAY,CAACnD,KAAK,CAAC,CAACgN,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKvB,YAAY,CAAC9I,YAAY,CAACnD,KAAK,CAAC,CAACgN,KAAK,GAAGS,OAAO;EAClD;EAEA;EACA1M,SAASA,CAAC4M,WAAgB;IACxB,IAAIA,WAAW,CAAC1M,WAAW,IAAI0M,WAAW,CAAC1M,WAAW,CAAC1C,MAAM,GAAG,CAAC,EAAE;MACjEoP,WAAW,CAAC3M,iBAAiB,GAAG,CAAC2M,WAAW,CAAC3M,iBAAiB,GAAG,CAAC,IAAI2M,WAAW,CAAC1M,WAAW,CAAC1C,MAAM;IACtG;EACF;EAEAoC,SAASA,CAACgN,WAAgB;IACxB,IAAIA,WAAW,CAAC1M,WAAW,IAAI0M,WAAW,CAAC1M,WAAW,CAAC1C,MAAM,GAAG,CAAC,EAAE;MACjEoP,WAAW,CAAC3M,iBAAiB,GAAG2M,WAAW,CAAC3M,iBAAiB,KAAK,CAAC,GAC/D2M,WAAW,CAAC1M,WAAW,CAAC1C,MAAM,GAAG,CAAC,GAClCoP,WAAW,CAAC3M,iBAAiB,GAAG,CAAC;IACvC;EACF;EACAX,eAAeA,CAACsN,WAAgB;IAC9B,IAAIA,WAAW,CAAC1M,WAAW,IAAI0M,WAAW,CAAC1M,WAAW,CAAC1C,MAAM,GAAG,CAAC,IAAIoP,WAAW,CAAC3M,iBAAiB,KAAKgC,SAAS,EAAE;MAChH,OAAO2K,WAAW,CAAC1M,WAAW,CAAC0M,WAAW,CAAC3M,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EAEA;EACAK,cAAcA,CAACsM,WAAgB,EAAEC,UAAmB;IAClD,IAAID,WAAW,CAAC1M,WAAW,IAAI0M,WAAW,CAAC1M,WAAW,CAAC1C,MAAM,GAAG,CAAC,EAAE;MACjE,IAAIqP,UAAU,KAAK5K,SAAS,EAAE;QAC5B2K,WAAW,CAAC3M,iBAAiB,GAAG4M,UAAU;MAC5C;MACAD,WAAW,CAAClF,WAAW,GAAG,IAAI;MAC9B;MACAgD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;EACF;EAEA9D,eAAeA,CAAC6F,WAAgB;IAC9BA,WAAW,CAAClF,WAAW,GAAG,KAAK;IAC/B;IACAgD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACAtE,cAAcA,CAACqG,WAAgB;IAC7B,IAAI,CAAC5M,SAAS,CAAC4M,WAAW,CAAC;EAC7B;EAEAxG,cAAcA,CAACwG,WAAgB;IAC7B,IAAI,CAAChN,SAAS,CAACgN,WAAW,CAAC;EAC7B;EAEA;EACA3F,SAASA,CAACgE,KAAoB,EAAE2B,WAAgB;IAC9C,IAAIA,WAAW,CAAClF,WAAW,EAAE;MAC3B,QAAQuD,KAAK,CAAC6B,GAAG;QACf,KAAK,WAAW;UACd7B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAAC3G,cAAc,CAACwG,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACf3B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAACxG,cAAc,CAACqG,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACX3B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAAChG,eAAe,CAAC6F,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEA;EACAlI,qBAAqBA,CAACsI,UAAiB;IACrC,IAAI,CAACA,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;IACX;IACA,OAAOA,UAAU,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,IAAID,CAAC,CAAC;EACzC;EACA;EACA3I,0BAA0BA,CAAC6I,kBAA4B,EAAEV,WAAgB;IACvE;IACAW,MAAM,CAACC,IAAI,CAACZ,WAAW,CAACzD,aAAa,CAAC,CAACsE,OAAO,CAACX,GAAG,IAAG;MACnDF,WAAW,CAACzD,aAAa,CAAC2D,GAAG,CAAC,GAAG,KAAK;IACxC,CAAC,CAAC;IAEF;IACAQ,kBAAkB,CAACG,OAAO,CAACC,SAAS,IAAG;MACrCd,WAAW,CAACzD,aAAa,CAACuE,SAAS,CAAC,GAAG,IAAI;IAC7C,CAAC,CAAC;IAEF;IACAd,WAAW,CAACe,WAAW,GAAG,IAAI,CAACnI,aAAa,CAAChI,MAAM,GAAG,CAAC,IACrD,IAAI,CAACgI,aAAa,CAACoI,KAAK,CAAC5C,IAAI,IAAI4B,WAAW,CAACzD,aAAa,CAAC6B,IAAI,CAAC,CAAC;IAEnE;IACA,IAAI,CAAC6C,6BAA6B,CAACjB,WAAW,CAAC;EACjD;EAEA;EACAkB,qBAAqBA,CAAClB,WAAgB;IACpC,OAAOW,MAAM,CAACC,IAAI,CAACZ,WAAW,CAACzD,aAAa,CAAC,CAACiD,MAAM,CAACU,GAAG,IAAIF,WAAW,CAACzD,aAAa,CAAC2D,GAAG,CAAC,CAAC;EAC7F;EAEA;EACQe,6BAA6BA,CAACjB,WAAgB;IACpDA,WAAW,CAACrH,wBAAwB,GAAG,IAAI,CAACuI,qBAAqB,CAAClB,WAAW,CAAC;EAChF;EAEA;EACQmB,gCAAgCA,CAAA;IACtC,IAAI,IAAI,CAACtQ,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACgQ,OAAO,CAACb,WAAW,IAAG;QAC5C,IAAI,CAACiB,6BAA6B,CAACjB,WAAW,CAAC;MACjD,CAAC,CAAC;IACJ;EACF;EAIAlK,sBAAsBA,CAACsL,OAAgB,EAAEhD,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAACzI,kBAAkB,CAACuI,IAAI,CAAC,GAAGgD,OAAO;EACjD;EAEAC,kBAAkBA,CAACnL,kBAA4B,EAAEoL,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAM7E,MAAM,IAAIxG,kBAAkB,EAAE;MACvCqL,YAAY,CAAC7E,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAM8E,WAAW,GAAGF,WAAW,CAACpC,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAI2B,WAAW,EAAE;MAC9B,IAAItL,kBAAkB,CAACuL,QAAQ,CAAC5B,IAAI,CAAC,EAAE;QACrC0B,YAAY,CAAC1B,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAO0B,YAAY;EACrB;EAEAG,UAAUA,CAACC,KAAoC;IAC7C,MAAMpB,GAAG,GAAG,IAAIqB,GAAG,EAAgE;IAEnFD,KAAK,CAACd,OAAO,CAACzC,IAAI,IAAG;MACnB,MAAM8B,GAAG,GAAG,GAAG9B,IAAI,CAAChG,SAAS,IAAIgG,IAAI,CAAClG,KAAK,IAAIkG,IAAI,CAACjG,KAAK,EAAE;MAC3D,IAAIoI,GAAG,CAACsB,GAAG,CAAC3B,GAAG,CAAC,EAAE;QAChB,MAAM4B,QAAQ,GAAGvB,GAAG,CAACnD,GAAG,CAAC8C,GAAG,CAAE;QAC9B4B,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLxB,GAAG,CAACyB,GAAG,CAAC9B,GAAG,EAAE;UAAE9B,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAE2D,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAO1B,KAAK,CAAC4B,IAAI,CAAC1B,GAAG,CAAC2B,MAAM,EAAE,CAAC,CAAC3B,GAAG,CAAC,CAAC;MAAEnC,IAAI;MAAE2D;IAAK,CAAE,MAAM;MACxD,GAAG3D,IAAI;MACP+D,YAAY,EAAEJ;KACf,CAAC,CAAC;EACL;EAGAK,eAAeA,CAAA;IACb,IAAI,CAACvG,gBAAgB,CAACwG,mCAAmC,CAAC;MACxDtE,IAAI,EAAE;QACJuE,YAAY,EAAE,IAAI,CAACjF,WAAW;QAC9BkF,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLnT,GAAG,CAACoT,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAGtC,IAAI,CAAC9R,kBAAkB,GAAG4R,GAAG,CAACC,OAAO,CAACnC,GAAG,CAAEqC,CAA0B,IAAI;UACvE,OAAO;YACLnN,cAAc,EAAE,IAAI;YACpBoN,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACb1K,SAAS,EAAEwK,CAAC,CAACxK,SAAS;YACtBF,KAAK,EAAE0K,CAAC,CAAC1K,KAAK;YACdC,KAAK,EAAEyK,CAAC,CAACzK,KAAK;YACdxB,SAAS,EAAE,GAAGiM,CAAC,CAAC1K,KAAK,IAAI0K,CAAC,CAACzK,KAAK,IAAIyK,CAAC,CAACxK,SAAS,EAAE;YACjDkJ,WAAW,EAAE,IAAI;YACjBa,YAAY,EAAE,CAAC;YACftL,cAAc,EAAE,CAAC;YACjBkM,OAAO,EAAE,CAAC;YAAExG,aAAa,EAAE,EAAE;YAC7B1G,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3CkL,WAAW,EAAE,KAAK;YAClBvL,YAAY,EAAE,EAAE;YAAEuB,eAAe,EAAE,IAAI,CAACyB,cAAc,CAAC,CAAC,CAAC;YACzDnF,iBAAiB,EAAE,CAAC;YACpByH,WAAW,EAAE,KAAK;YAClB1B,WAAW,EAAE,IAAI;YAAE;YACnB9F,WAAW,EAAEsP,CAAC,CAACI,cAAc,GAAGJ,CAAC,CAACI,cAAc,CAACzC,GAAG,CAAC0C,EAAE,IAAIA,EAAE,CAACC,OAAO,CAAC,CAAC1D,MAAM,CAAE2D,GAAQ,IAAKA,GAAG,IAAI,IAAI,CAAa,GAAG;WACxH;QACH,CAAC,CAAC;QACF,IAAI,CAACtS,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAAC6Q,UAAU,CAAC,IAAI,CAAC7Q,kBAAkB,CAAC,CAAC;QAEvE;QACA,IAAI,CAACuS,kBAAkB,EAAE;MAC3B;IACF,CAAC,CAAC,CACH,CAACnG,SAAS,EAAE;EACf;EAKAoG,eAAeA,CAAA;IACb,IAAI,CAAC7H,gBAAgB,CAAC8H,mCAAmC,CAAC;MACxDvF,IAAI,EAAE;QACJuE,YAAY,EAAE,IAAI,CAACjF,WAAW;QAC9BnB,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DqH,SAAS,EAAE;;KAEd,CAAC,CAACf,IAAI,CACLnT,GAAG,CAACoT,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACxN,YAAY,GAAGsN,GAAG,CAACC,OAAO;QAC/B,IAAI,CAAClG,KAAK,GAAGiG,GAAG,CAACC,OAAO,CAACc,SAAS,GAAG,KAAK,GAAG,IAAI;QAEjD,IAAIf,GAAG,CAACC,OAAO,CAACc,SAAS,EAAE;UACzB,IAAI,CAAC5K,aAAa,CAACiI,OAAO,CAACzC,IAAI,IAAI,IAAI,CAAC7B,aAAa,CAAC6B,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAAClI,kBAAkB,CAAC2K,OAAO,CAACzC,IAAI,IAAI,IAAI,CAACvI,kBAAkB,CAACuI,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAACvN,kBAAkB,GAAG4R,GAAG,CAACC,OAAO,CAACc,SAAS,CAACjD,GAAG,CAAEqC,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAAC3N,YAAY,EAAE2N,OAAO;cACnCrN,cAAc,EAAEmN,CAAC,CAACnN,cAAc;cAChCnC,WAAW,EAAEsP,CAAC,CAACtP,WAAW,KAAKsP,CAAC,CAACa,gBAAgB,GAAG,CAACb,CAAC,CAACa,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9EpE,KAAK,EAAEuD,CAAC,CAACvD,KAAK;cACdwD,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCa,WAAW,EAAEd,CAAC,CAACc,WAAW;cAC1BtL,SAAS,EAAEwK,CAAC,CAACxK,SAAS;cACtBF,KAAK,EAAE0K,CAAC,CAAC1K,KAAK;cACdC,KAAK,EAAEyK,CAAC,CAACzK,KAAK;cACdxB,SAAS,EAAEiM,CAAC,CAACjM,SAAS,GAAGiM,CAAC,CAACjM,SAAS,GAAG,GAAGiM,CAAC,CAAC1K,KAAK,IAAI0K,CAAC,CAACzK,KAAK,IAAIyK,CAAC,CAACxK,SAAS,EAAE;cAC7EkJ,WAAW,EAAEsB,CAAC,CAACtB,WAAW;cAC1Ba,YAAY,EAAES,CAAC,CAACT,YAAY;cAC5BtL,cAAc,EAAE+L,CAAC,CAACG,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGH,CAAC,CAAC/L,cAAc;cACtDkM,OAAO,EAAEH,CAAC,CAACG,OAAO;cAClBxG,aAAa,EAAEqG,CAAC,CAACe,qBAAqB,CAAC/S,MAAM,GAAG,IAAI,CAACgT,0BAA0B,CAAC,IAAI,CAAChL,aAAa,EAAEgK,CAAC,CAACe,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACpH;cAAa,CAAE;cAAE1G,kBAAkB,EAAE+M,CAAC,CAACtB,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAACnL,kBAAkB,EAAE0M,CAAC,CAACtB,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACzL;cAAkB,CAAE;cAC9RkL,WAAW,EAAE6B,CAAC,CAACe,qBAAqB,CAAC/S,MAAM,KAAK,IAAI,CAACgI,aAAa,CAAChI,MAAM;cACzE4E,YAAY,EAAE,EAAE;cAAEuB,eAAe,EAAE6L,CAAC,CAACG,OAAO,GAAG,IAAI,CAAC7E,cAAc,CAAC0E,CAAC,CAACG,OAAO,EAAE,IAAI,CAACvK,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cAC3HnF,iBAAiB,EAAE,CAAC;cACpByH,WAAW,EAAE,KAAK;cAClB1B,WAAW,EAAE,IAAI;cAAE;cACnBT,wBAAwB,EAAE,EAAE,CAAC;aAC9B;UACH,CAAC,CAAC;UAEF;UACA,IAAI,CAACyK,kBAAkB,EAAE;UAEzB;UACA,IAAI,CAACpH,GAAG,CAAC6H,aAAa,EAAE;QAC1B,CAAC,MAAM;UACL;UACA,IAAI,CAACzB,eAAe,EAAE;QACxB;QAEA;QACA,IAAI,CAACjB,gCAAgC,EAAE;QAEvC;QACA,IAAI,CAACnF,GAAG,CAAC6H,aAAa,EAAE;MAC1B,CAAC,MAAM;QACLtG,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEiF,GAAG,CAAC;MAC/C;IACF,CAAC,CAAC,CACH,CAACxF,SAAS,CAAC;MACVO,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAvG,mBAAmBA,CAAC+I,WAAgB;IAClC,IAAIA,WAAW,CAACjJ,eAAe,IAAIiJ,WAAW,CAACjJ,eAAe,CAACsB,KAAK,KAAK,CAAC,EAAE;MAC1E2H,WAAW,CAACnJ,cAAc,GAAG,CAAC;IAChC;EACF;EACAiN,4BAA4BA,CAAC9O,IAAW;IACtC,KAAK,IAAIoJ,IAAI,IAAIpJ,IAAI,EAAE;MACrB,IAAIoJ,IAAI,CAACjC,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOiC,IAAI,CAAC2F,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAMAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOtD,MAAM,CAACC,IAAI,CAACqD,GAAG,CAAC,CAACzE,MAAM,CAACU,GAAG,IAAI+D,GAAG,CAAC/D,GAAG,CAAC,CAAC;EACjD;EAEAgE,0BAA0BA,CAACD,GAA4B;IACrD,OAAOtD,MAAM,CAACC,IAAI,CAACqD,GAAG,CAAC,CACpBzE,MAAM,CAACU,GAAG,IAAI+D,GAAG,CAAC/D,GAAG,CAAC,CAAC,CACvBiE,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACrN,eAAoB,EAAElB,kBAAuB;IAC1D,IAAIkB,eAAe,IAAIA,eAAe,CAACsB,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC6L,0BAA0B,CAACrO,kBAAkB,CAAC;IAC5D;EACF;EAEAwO,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACpF,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIqF,KAAK,CAAC3T,MAAM,GAAG,CAAC,EAAE;MACpB,OAAO2T,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAChP,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAAC5E,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACL6T,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAAC7O,YAAY,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,IAAI,IAAI;QACpE0P,aAAa,EAAElP,YAAY,CAAC,CAAC,CAAC,CAAC2J,SAAS,IAAI,IAAI;QAChDwF,QAAQ,EAAEnP,YAAY,CAAC,CAAC,CAAC,CAAC6J,KAAK,CAACpK,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOI,SAAS;EAEzB;EAGAuP,UAAUA,CAAA;IACR,IAAI,CAACjJ,KAAK,CAACkJ,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAM5G,IAAI,IAAI,IAAI,CAAC6G,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAC1G,IAAI,CAAC2E,OAAQ,EAAE;QACzC+B,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAC3G,IAAI,CAACvH,cAAe,EAAE;QACvDkO,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAI3G,IAAI,CAAC+D,YAAY,IAAI/D,IAAI,CAACvH,cAAc,EAAE;QAC5C,IAAIuH,IAAI,CAACvH,cAAc,GAAGuH,IAAI,CAAC+D,YAAY,IAAI/D,IAAI,CAACvH,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAAC8E,KAAK,CAACuJ,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAG9G,IAAI,CAAC+D,YAAY,GAAG,KAAK/D,IAAI,CAACzH,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACqO,kBAAkB,IAAK,CAAC5G,IAAI,CAACzH,SAAU,EAAE;QAC5CqO,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAACnJ,KAAK,CAACuJ,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAACpJ,KAAK,CAACuJ,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAACrJ,KAAK,CAACuJ,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACjK,YAAY,GAAG,IAAI;IAExB,IAAI,CAAC+J,mBAAmB,GAAG,IAAI,CAACpU,kBAAkB,CAAC0P,GAAG,CAAE6E,CAAM,IAAI;MAChE,OAAO;QACL3P,cAAc,EAAE2P,CAAC,CAAC3P,cAAc,GAAG2P,CAAC,CAAC3P,cAAc,GAAG,IAAI;QAC1D4J,KAAK,EAAE+F,CAAC,CAAC5P,YAAY,GAAG,IAAI,CAACgP,UAAU,CAACY,CAAC,CAAC5P,YAAY,CAAC,GAAGH,SAAS;QACnEwN,kBAAkB,EAAE,IAAI,CAACmB,oBAAoB,CAACoB,CAAC,CAAC7I,aAAa,CAAC;QAC9DmH,WAAW,EAAE0B,CAAC,CAAC1B,WAAW,GAAG0B,CAAC,CAAC1B,WAAW,GAAG,IAAI;QACjD2B,OAAO,EAAE,IAAI,CAAC7I,KAAK,GAAG,IAAI,GAAG,IAAI,CAACrH,YAAY,EAAE2N,OAAO;QACvD5K,KAAK,EAAEkN,CAAC,CAAClN,KAAK;QACdC,KAAK,EAAEiN,CAAC,CAACjN,KAAK;QACdC,SAAS,EAAEgN,CAAC,CAAChN,SAAS;QACtBzB,SAAS,EAAEyO,CAAC,CAACzO,SAAS;QAAE;QACxB2K,WAAW,EAAE8D,CAAC,CAACrO,eAAe,CAACsB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC+L,cAAc,CAACgB,CAAC,CAACrO,eAAe,EAAEqO,CAAC,CAACvP,kBAAkB,CAAC,IAAI,IAAI;QACxHsM,YAAY,EAAEiD,CAAC,CAACjD,YAAY;QAC5BtL,cAAc,EAAEuO,CAAC,CAACvO,cAAc;QAChCkM,OAAO,EAAEqC,CAAC,CAACrO,eAAe,CAACsB;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACuM,UAAU,EAAE;IACjB,IAAI,IAAI,CAACjJ,KAAK,CAAC2J,aAAa,CAAC1U,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC2K,OAAO,CAACgK,aAAa,CAAC,IAAI,CAAC5J,KAAK,CAAC2J,aAAa,CAAC;MACpD;MACA,IAAI,CAACE,sBAAsB,EAAE;MAC7B,IAAI,CAACtK,YAAY,GAAG,KAAK;MACzB;IACF;IACA,IAAI,IAAI,CAACsB,KAAK,EAAE;MACd,IAAI,CAACiJ,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAClK,gBAAgB,CAACmK,oCAAoC,CAAC;MACzD5H,IAAI,EAAE,IAAI,CAACkH;KACZ,CAAC,CAAChI,SAAS,CAAC;MACX2I,IAAI,EAAGnD,GAAG,IAAI;QACZ,IAAI,CAACvH,YAAY,GAAG,KAAK;QACzB,IAAIuH,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACpH,OAAO,CAACsK,aAAa,CAAC,MAAM,CAAC;UAClC;UACA,IAAI,CAACnI,MAAM,EAAE;QACf;MACF,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtC,YAAY,GAAG,KAAK;QACzBqC,OAAO,CAACC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;KACD,CAAC;EACJ;EAIAiI,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvBxD,YAAY,EAAE,IAAI,CAACjF,WAAW;MAC9B0I,SAAS,EAAE,IAAI,CAACd,mBAAmB,IAAI,IAAI;MAC3C/I,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACV,gBAAgB,CAACwK,sCAAsC,CAAC;MAC3DjI,IAAI,EAAE,IAAI,CAAC+H;KACZ,CAAC,CAAC7I,SAAS,CAAC;MACX2I,IAAI,EAAGnD,GAAG,IAAI;QACZ,IAAI,CAACvH,YAAY,GAAG,KAAK;QACzB,IAAIuH,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACpH,OAAO,CAACsK,aAAa,CAAC,MAAM,CAAC;UAClC;UACA,IAAI,CAACnI,MAAM,EAAE;QACf;MACF,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtC,YAAY,GAAG,KAAK;QACzBqC,OAAO,CAACC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACvC;KACD,CAAC;EACJ;EAEAoG,0BAA0BA,CAACqC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAM/H,IAAI,IAAI6H,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACvJ,IAAI,CAAC0J,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKlI,IAAI,IAAIiI,KAAK,CAACE,SAAS,CAAC;MAClFJ,CAAC,CAAC/H,IAAI,CAAC,GAAG,CAAC,CAACgI,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAEF;;;EAGAlL,aAAaA,CAAA;IACX;IACA,IAAI,CAACY,gBAAgB,CAACwG,mCAAmC,CAAC;MACxDtE,IAAI,EAAE;QACJuE,YAAY,EAAE,IAAI,CAACjF,WAAW;QAC9BkF,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLnT,GAAG,CAACoT,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC;QACA,MAAM6D,iBAAiB,GAAG,IAAIC,GAAG,EAAU;QAC3ChE,GAAG,CAACC,OAAO,CAAC7B,OAAO,CAAE6F,QAAa,IAAI;UACpC,MAAMxG,GAAG,GAAG,GAAGwG,QAAQ,CAACtO,SAAS,IAAIsO,QAAQ,CAACxO,KAAK,IAAIwO,QAAQ,CAACvO,KAAK,EAAE;UACvEqO,iBAAiB,CAACG,GAAG,CAACzG,GAAG,CAAC;QAC5B,CAAC,CAAC;QAEF;QACA,MAAM0G,cAAc,GAAG,IAAI,CAAC/V,kBAAkB,CAAC2O,MAAM,CAAEpB,IAAS,IAAI;UAClE,MAAMyI,OAAO,GAAG,GAAGzI,IAAI,CAAChG,SAAS,IAAIgG,IAAI,CAAClG,KAAK,IAAIkG,IAAI,CAACjG,KAAK,EAAE;UAC/D,OAAOqO,iBAAiB,CAAC3E,GAAG,CAACgF,OAAO,CAAC;QACvC,CAAC,CAAC;QAEF,IAAID,cAAc,CAAChW,MAAM,KAAK,CAAC,EAAE;UAC/B,IAAI,CAAC2K,OAAO,CAACkC,YAAY,CAAC,eAAe,CAAC;UAC1C;QACF;QAEA;QACA,IAAI,CAACwH,mBAAmB,GAAG2B,cAAc,CAACrG,GAAG,CAAE6E,CAAM,IAAI;UACvD,OAAO;YACL3P,cAAc,EAAE2P,CAAC,CAAC3P,cAAc,GAAG2P,CAAC,CAAC3P,cAAc,GAAG,IAAI;YAC1D4J,KAAK,EAAE+F,CAAC,CAAC5P,YAAY,GAAG,IAAI,CAACgP,UAAU,CAACY,CAAC,CAAC5P,YAAY,CAAC,GAAGH,SAAS;YACnEwN,kBAAkB,EAAE,IAAI,CAACmB,oBAAoB,CAACoB,CAAC,CAAC7I,aAAa,CAAC;YAC9DmH,WAAW,EAAE,IAAI;YAAE;YACnB2B,OAAO,EAAE,IAAI;YAAE;YACfnN,KAAK,EAAEkN,CAAC,CAAClN,KAAK;YACdC,KAAK,EAAEiN,CAAC,CAACjN,KAAK;YACdC,SAAS,EAAEgN,CAAC,CAAChN,SAAS;YACtBzB,SAAS,EAAEyO,CAAC,CAACzO,SAAS;YACtB2K,WAAW,EAAE8D,CAAC,CAACrO,eAAe,CAACsB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC+L,cAAc,CAACgB,CAAC,CAACrO,eAAe,EAAEqO,CAAC,CAACvP,kBAAkB,CAAC,IAAI,IAAI;YACxHsM,YAAY,EAAEiD,CAAC,CAACjD,YAAY;YAC5BtL,cAAc,EAAEuO,CAAC,CAACvO,cAAc;YAChCkM,OAAO,EAAEqC,CAAC,CAACrO,eAAe,CAACsB;WAC5B;QACH,CAAC,CAAC;QAEF;QACA,IAAI,CAACuM,UAAU,EAAE;QACjB,IAAI,IAAI,CAACjJ,KAAK,CAAC2J,aAAa,CAAC1U,MAAM,GAAG,CAAC,EAAE;UACvC,IAAI,CAAC2K,OAAO,CAACgK,aAAa,CAAC,IAAI,CAAC5J,KAAK,CAAC2J,aAAa,CAAC;UACpD;QACF;QAEA;QACA,IAAI,CAACQ,iBAAiB,GAAG;UACvBxD,YAAY,EAAE,IAAI,CAACjF,WAAW;UAC9B0I,SAAS,EAAE,IAAI,CAACd,mBAAmB,IAAI,IAAI;UAC3C/I,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;SACnD;QAED,IAAI,CAACV,gBAAgB,CAACwK,sCAAsC,CAAC;UAC3DjI,IAAI,EAAE,IAAI,CAAC+H;SACZ,CAAC,CAAC7I,SAAS,CAAC6J,SAAS,IAAG;UACvB,IAAIA,SAAS,CAACnE,UAAU,IAAI,CAAC,EAAE;YAC7B,IAAI,CAACpH,OAAO,CAACsK,aAAa,CAAC,cAAce,cAAc,CAAChW,MAAM,QAAQ,CAAC;YACvE;YACA,IAAI,CAACyS,eAAe,EAAE;UACxB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAC9H,OAAO,CAACkC,YAAY,CAAC,eAAe,CAAC;MAC5C;IACF,CAAC,CAAC,CACH,CAACR,SAAS,EAAE;EACf;EAEA;EACQ8J,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC1J,WAAW,EAAE;IAEvB,IAAI,CAACtB,aAAa,CAACiL,4BAA4B,CAAC;MAAE3J,WAAW,EAAE,IAAI,CAACA;IAAW,CAAE,CAAC,CAACJ,SAAS,CAAC;MAC3F2I,IAAI,EAAGqB,QAAQ,IAAI;QACjB1J,OAAO,CAAC2J,GAAG,CAAC,2BAA2B,EAAED,QAAQ,CAAC;QAClD,IAAIA,QAAQ,CAACvE,OAAO,EAAE;UACpB,IAAI,CAACjK,YAAY,GAAG,IAAI,CAAC0O,gCAAgC,CAACF,QAAQ,CAACvE,OAAO,CAAC;UAC3EnF,OAAO,CAAC2J,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACzO,YAAY,CAAC;QAC3D;MACF,CAAC;MACD+E,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D;QACA,IAAI,IAAI,CAAC5E,aAAa,IAAI,IAAI,CAACA,aAAa,CAAChI,MAAM,GAAG,CAAC,EAAE;UACvD,IAAI,CAAC6H,YAAY,GAAG,IAAI,CAAC2O,kCAAkC,CAAC,IAAI,CAACxO,aAAa,CAAC;QACjF;MACF;KACD,CAAC;EACJ;EAEA;EACQuO,gCAAgCA,CAACE,OAAY;IACnD,MAAM5O,YAAY,GAAQ,EAAE;IAE5BkI,MAAM,CAAC0G,OAAO,CAACA,OAAO,CAAC,CAACxG,OAAO,CAAC,CAAC,CAACyG,QAAQ,EAAEC,MAAM,CAAgB,KAAI;MACpE9O,YAAY,CAAC6O,QAAQ,CAAC,GAAGC,MAAM,CAAChH,GAAG,CAAEiH,KAAU,KAAM;QACnD/G,IAAI,EAAE+G,KAAK,CAACC,SAAS;QACrBH,QAAQ,EAAEE,KAAK,CAACE,QAAQ;QACxBC,KAAK,EAAEH,KAAK,CAACI,KAAK;QAClBC,OAAO,EAAEL,KAAK,CAACM,OAAO;QACtBC,SAAS,EAAEP,KAAK,CAACC,SAAS;QAC1BO,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOxP,YAAY;EACrB;EAEA;EACA2O,kCAAkCA,CAACxO,aAAuB;IACxD,IAAI,CAACA,aAAa,IAAIA,aAAa,CAAChI,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,EAAE;IACX;IAEA;IACA,MAAM6H,YAAY,GAAQ,EAAE;IAE5BG,aAAa,CAACiI,OAAO,CAACC,SAAS,IAAG;MAChC;MACA,MAAMoH,aAAa,GAAGpH,SAAS,CAACqH,KAAK,CAAC,WAAW,CAAC;MAClD,MAAMb,QAAQ,GAAGY,aAAa,GAAG,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM;MAEhE,IAAI,CAACzP,YAAY,CAAC6O,QAAQ,CAAC,EAAE;QAC3B7O,YAAY,CAAC6O,QAAQ,CAAC,GAAG,EAAE;MAC7B;MAEA;MACA,MAAMc,WAAW,GAAGC,QAAQ,CAACvH,SAAS,CAACwH,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MAC7D,MAAMX,KAAK,GAAGY,IAAI,CAACC,IAAI,CAACJ,WAAW,GAAG,CAAC,CAAC;MAExC3P,YAAY,CAAC6O,QAAQ,CAAC,CAAChI,IAAI,CAAC;QAC1BmB,IAAI,EAAEK,SAAS;QACfwG,QAAQ,EAAEA,QAAQ;QAClBK,KAAK,EAAE,GAAGA,KAAK,GAAG;QAClBK,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;IAAE,OAAOxP,YAAY;EACzB;EAIA6E,iCAAiCA,CAAA;IAC/B,IAAI,CAAC7B,yBAAyB,CAACgN,8DAA8D,CAAC;MAC5F1K,IAAI,EAAE,IAAI,CAACV;KACZ,CAAC,CAACmF,IAAI,CACLnT,GAAG,CAACoT,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC/J,aAAa,GAAG,IAAI,CAACkL,4BAA4B,CAACrB,GAAG,CAACC,OAAO,CAAC;QAEnE;QACA,IAAI,CAACqE,uBAAuB,EAAE;QAE9B,IAAI,CAAC1D,eAAe,EAAE;MACxB,CAAC,MAAM;QACL9F,OAAO,CAACC,KAAK,CAAC,2CAA2C,EAAEiF,GAAG,CAAC;MACjE;IACF,CAAC,CAAC,CACH,CAACxF,SAAS,CAAC;MACVO,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAClE;KACD,CAAC;EACJ;EACAE,MAAMA,CAAA;IACJ,IAAI,CAAC5B,aAAa,CAACwD,IAAI,CAAC;MACtBoJ,MAAM;MACNC,OAAO,EAAE,IAAI,CAACtL;KACf,CAAC;IACF,IAAI,CAACzB,QAAQ,CAACgN,IAAI,EAAE;EACtB;EAEA;EAEA;;;EAGAC,eAAeA,CAAC7I,WAAwC;IACtD;IACA,MAAM8I,WAAW,GAAG,CAAC,CAAC9I,WAAW,CAACrJ,SAAS,IAAIqJ,WAAW,CAACrJ,SAAS,CAACoS,IAAI,EAAE,KAAK,EAAE;IAClF,MAAMC,SAAS,GAAG,CAAC,CAAChJ,WAAW,CAACjJ,eAAe,IAAIiJ,WAAW,CAACjJ,eAAe,CAACsB,KAAK;IACpF,MAAM4Q,gBAAgB,GAAG,CAAC,CAACjJ,WAAW,CAACnJ,cAAc,IAAImJ,WAAW,CAACnJ,cAAc,GAAG,CAAC;IAEvF;IACA,IAAIqS,aAAa,GAAG,IAAI;IACxB,IAAIlJ,WAAW,CAACjJ,eAAe,EAAEsB,KAAK,KAAK,CAAC,EAAE;MAC5C6Q,aAAa,GAAG,CAAC,CAAClJ,WAAW,CAACnK,kBAAkB,IAC9C8K,MAAM,CAACuB,MAAM,CAAClC,WAAW,CAACnK,kBAAkB,CAAC,CAACiH,IAAI,CAACqM,QAAQ,IAAIA,QAAQ,CAAC;IAC5E;IAEA,OAAOL,WAAW,IAAIE,SAAS,IAAIC,gBAAgB,IAAIC,aAAa;EACtE;EAEA;;;EAGAE,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACvY,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC9E,OAAO,IAAI,CAACC,kBAAkB,CAAC2O,MAAM,CAACpB,IAAI,IAAI,IAAI,CAACyK,eAAe,CAACzK,IAAI,CAAC,CAAC,CAACxN,MAAM;EAClF;EAEA;;;EAGAyY,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACxY,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC9E,MAAM0Y,SAAS,GAAG,IAAI,CAACF,sBAAsB,EAAE;IAC/C,OAAOb,IAAI,CAACgB,KAAK,CAAED,SAAS,GAAG,IAAI,CAACzY,kBAAkB,CAACD,MAAM,GAAI,GAAG,CAAC;EACvE;EAEA;;;EAGA0B,YAAYA,CAACD,KAAa;IACxB,MAAMmX,OAAO,GAAG1L,QAAQ,CAAC2L,cAAc,CAAC,aAAapX,KAAK,EAAE,CAAC;IAC7D,IAAImX,OAAO,EAAE;MACXA,OAAO,CAACE,cAAc,CAAC;QACrBC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE;OACT,CAAC;MAEF;MACAL,OAAO,CAACM,SAAS,CAACnD,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,iBAAiB,CAAC;MACnEoD,UAAU,CAAC,MAAK;QACdP,OAAO,CAACM,SAAS,CAACE,MAAM,CAAC,QAAQ,EAAE,eAAe,EAAE,iBAAiB,CAAC;MACxE,CAAC,EAAE,IAAI,CAAC;IACV;EACF;EAEA;;;EAGAC,2BAA2BA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACpZ,kBAAkB,EAAE;IAE9B,MAAMqZ,oBAAoB,GAAG,IAAI,CAACrZ,kBAAkB,CAACsZ,SAAS,CAAC/L,IAAI,IAAI,CAAC,IAAI,CAACyK,eAAe,CAACzK,IAAI,CAAC,CAAC;IACnG,IAAI8L,oBAAoB,KAAK,CAAC,CAAC,EAAE;MAC/B,IAAI,CAAC5X,YAAY,CAAC4X,oBAAoB,CAAC;IACzC;EACF;EAEA;;;EAGA1E,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC3U,kBAAkB,EAAE;IAE9B;IACA,MAAMuZ,eAAe,GAAG,IAAI,CAACvZ,kBAAkB,CAACsZ,SAAS,CAAC/L,IAAI,IAAI,CAAC,IAAI,CAACyK,eAAe,CAACzK,IAAI,CAAC,CAAC;IAC9F,IAAIgM,eAAe,KAAK,CAAC,CAAC,EAAE;MAC1B,IAAI,CAAC9X,YAAY,CAAC8X,eAAe,CAAC;IACpC;EACF;EAEA;;;EAGAC,WAAWA,CAAA;IACTC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB;EAEA;;;EAGAC,OAAOA,CAAA;IACLjN,OAAO,CAAC2J,GAAG,CAAC,iCAAiC,CAAC;IAE9C;IACA,MAAMuD,aAAa,GAAG3M,QAAQ,CAAC4M,aAAa,CAAC,gBAAgB,CAAC,IAC5D5M,QAAQ,CAAC4M,aAAa,CAAC,cAAc,CAAC,IACtC5M,QAAQ,CAAC4M,aAAa,CAAC,SAAS,CAAC,IACjC5M,QAAQ,CAAC4M,aAAa,CAAC,SAAS,CAAC,IACjC5M,QAAQ,CAAC4M,aAAa,CAAC,YAAY,CAAC,IACpC5M,QAAQ,CAACC,IAAI,CAAC4M,iBAAiB;IAEjC,IAAIF,aAAa,EAAE;MAChBA,aAA6B,CAACf,cAAc,CAAC;QAC5CC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;OACR,CAAC;MACFrM,OAAO,CAAC2J,GAAG,CAAC,6BAA6B,EAAEuD,aAAa,CAACG,OAAO,CAAC;MAEjE;MACAb,UAAU,CAAC,MAAK;QACdO,MAAM,CAACO,QAAQ,CAAC;UACdC,GAAG,EAAE,CAAC,EAAE;UAAE;UACVnB,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;MACP;IACF;IAEA;IACA,IAAI,IAAI,CAAC9Y,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACD,MAAM,GAAG,CAAC,EAAE;MACjE,MAAMma,YAAY,GAAGjN,QAAQ,CAAC2L,cAAc,CAAC,aAAa,CAAC;MAC3D,IAAIsB,YAAY,EAAE;QAChBA,YAAY,CAACrB,cAAc,CAAC;UAC1BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;SACR,CAAC;QAEF;QACAG,UAAU,CAAC,MAAK;UACdO,MAAM,CAACO,QAAQ,CAAC;YACdC,GAAG,EAAE,CAAC,GAAG;YAAE;YACXnB,QAAQ,EAAE;WACX,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;QACPpM,OAAO,CAAC2J,GAAG,CAAC,+CAA+C,CAAC;QAC5D;MACF;IACF;IAEA;IACA3J,OAAO,CAAC2J,GAAG,CAAC,+BAA+B,CAAC;IAC5CoD,MAAM,CAACC,QAAQ,CAAC;MAAEO,GAAG,EAAE,CAAC;MAAEnB,QAAQ,EAAE;IAAQ,CAAE,CAAC;EACjD;EAEA;;;EAGAqB,eAAeA,CAACzP,OAAe;IAC7BgC,OAAO,CAAC2J,GAAG,CAAC3L,OAAO,CAAC;EACtB;EAEA;;;EAGA0P,cAAcA,CAAA;IACZ1N,OAAO,CAAC2J,GAAG,CAAC,iBAAiB,CAAC;IAE9B;IACA,MAAMgE,MAAM,GAAGpN,QAAQ,CAAC4M,aAAa,CAAC,qBAAqB,CAAgB;IAC3E,IAAIQ,MAAM,EAAE;MACVA,MAAM,CAAClN,KAAK,CAACmN,SAAS,GAAG,aAAa;MACtCpB,UAAU,CAAC,MAAK;QACdmB,MAAM,CAAClN,KAAK,CAACmN,SAAS,GAAG,EAAE;MAC7B,CAAC,EAAE,GAAG,CAAC;IACT;IAEA;IACApB,UAAU,CAAC,MAAK;MACd;MACA,IAAI,IAAI,CAACpZ,0BAA0B,IAAI,IAAI,CAACA,0BAA0B,CAACC,MAAM,GAAG,CAAC,EAAE;QACjF,MAAMwa,SAAS,GAAG,IAAI,CAACza,0BAA0B,CAACC,MAAM,GAAG,CAAC;QAC5D,MAAMya,WAAW,GAAGvN,QAAQ,CAAC2L,cAAc,CAAC,aAAa2B,SAAS,EAAE,CAAC;QACrE,IAAIC,WAAW,EAAE;UACf9N,OAAO,CAAC2J,GAAG,CAAC,yBAAyBkE,SAAS,EAAE,CAAC;UACjDC,WAAW,CAAC3B,cAAc,CAAC;YACzBC,QAAQ,EAAE,QAAQ;YAClBC,KAAK,EAAE,KAAK;YACZC,MAAM,EAAE;WACT,CAAC;UAEF;UACAE,UAAU,CAAC,MAAK;YACdO,MAAM,CAACO,QAAQ,CAAC;cACdC,GAAG,EAAE,GAAG;cACRnB,QAAQ,EAAE;aACX,CAAC;UACJ,CAAC,EAAE,GAAG,CAAC;UAEPpM,OAAO,CAAC2J,GAAG,CAAC,cAAc,CAAC;UAC3B;QACF;MACF;MAEA;MACA3J,OAAO,CAAC2J,GAAG,CAAC,kBAAkB,CAAC;MAC/B,MAAMoE,YAAY,GAAG/C,IAAI,CAACgD,GAAG,CAC3BzN,QAAQ,CAACC,IAAI,CAACuN,YAAY,EAC1BxN,QAAQ,CAACC,IAAI,CAACyN,YAAY,EAC1B1N,QAAQ,CAAC2N,eAAe,CAACC,YAAY,EACrC5N,QAAQ,CAAC2N,eAAe,CAACH,YAAY,EACrCxN,QAAQ,CAAC2N,eAAe,CAACD,YAAY,CACtC;MAEDjO,OAAO,CAAC2J,GAAG,CAAC,OAAO,EAAEoE,YAAY,CAAC;MAElChB,MAAM,CAACC,QAAQ,CAAC;QACdO,GAAG,EAAEQ,YAAY;QACjB3B,QAAQ,EAAE;OACX,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGA3Q,kBAAkBA,CAACgH,WAAwC;IACzDA,WAAW,CAAC5G,WAAW,GAAG,CAAC4G,WAAW,CAAC5G,WAAW;EACpD;EAEA;;;EAGAvH,SAASA,CAAA;IACP,IAAI,IAAI,CAAChB,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACgQ,OAAO,CAACzC,IAAI,IAAG;QACrCA,IAAI,CAAChF,WAAW,GAAG,KAAK;MAC1B,CAAC,CAAC;MACF,IAAI,CAAC4C,GAAG,CAAC6H,aAAa,EAAE;IAC1B;EACF;EAEA;;;EAGA9R,WAAWA,CAAA;IACT,IAAI,IAAI,CAAClB,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACgQ,OAAO,CAACzC,IAAI,IAAG;QACrCA,IAAI,CAAChF,WAAW,GAAG,IAAI;MACzB,CAAC,CAAC;MACF,IAAI,CAAC4C,GAAG,CAAC6H,aAAa,EAAE;IAC1B;EACF;EAEA;;;EAGA8H,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC9a,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACgQ,OAAO,CAACzC,IAAI,IAAG;QACrCA,IAAI,CAAChF,WAAW,GAAG,IAAI,CAACyP,eAAe,CAACzK,IAAI,CAAC;MAC/C,CAAC,CAAC;MACF,IAAI,CAACpC,GAAG,CAAC6H,aAAa,EAAE;IAC1B;EACF;EAEA;;;EAGAxS,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACF,WAAW,CAAC4X,IAAI,EAAE,EAAE;MAC5B,IAAI,CAACpY,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAACE,kBAAkB,CAAC;IAChE,CAAC,MAAM;MACL,MAAM+a,KAAK,GAAG,IAAI,CAACza,WAAW,CAAC0a,WAAW,EAAE,CAAC9C,IAAI,EAAE;MACnD,IAAI,CAACpY,0BAA0B,GAAG,IAAI,CAACE,kBAAkB,CAAC2O,MAAM,CAACpB,IAAI,IAAG;QACtE,OACEA,IAAI,CAAClG,KAAK,EAAE2T,WAAW,EAAE,CAACpK,QAAQ,CAACmK,KAAK,CAAC,IACzCxN,IAAI,CAACjG,KAAK,EAAE0T,WAAW,EAAE,CAACpK,QAAQ,CAACmK,KAAK,CAAC,IACzCxN,IAAI,CAAChG,SAAS,EAAEyT,WAAW,EAAE,CAACpK,QAAQ,CAACmK,KAAK,CAAC,IAC7CxN,IAAI,CAACzH,SAAS,EAAEkV,WAAW,EAAE,CAACpK,QAAQ,CAACmK,KAAK,CAAC;MAEjD,CAAC,CAAC;IACJ;IACA,IAAI,CAAC5P,GAAG,CAAC6H,aAAa,EAAE;EAC1B;EAEA;;;EAGAxT,WAAWA,CAAA;IACT,IAAI,CAACc,WAAW,GAAG,EAAE;IACrB,IAAI,CAACR,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAACE,kBAAkB,CAAC;IAC9D,IAAI,CAACmL,GAAG,CAAC6H,aAAa,EAAE;EAC1B;EAEA;;;EAGQT,kBAAkBA,CAAA;IACxB,IAAI,CAACzS,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAACE,kBAAkB,CAAC;IAC9D,IAAI,IAAI,CAACM,WAAW,CAAC4X,IAAI,EAAE,EAAE;MAC3B,IAAI,CAAC1X,QAAQ,EAAE;IACjB;EACF;;;uCAnjCW8J,4CAA4C,EAAAvL,EAAA,CAAAkc,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApc,EAAA,CAAAkc,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAtc,EAAA,CAAAkc,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAxc,EAAA,CAAAkc,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA1c,EAAA,CAAAkc,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAA3c,EAAA,CAAAkc,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA7c,EAAA,CAAAkc,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAA/c,EAAA,CAAAkc,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAjd,EAAA,CAAAkc,iBAAA,CAAAO,EAAA,CAAAS,eAAA,GAAAld,EAAA,CAAAkc,iBAAA,CAAAiB,EAAA,CAAAC,YAAA,GAAApd,EAAA,CAAAkc,iBAAA,CAAAO,EAAA,CAAAY,YAAA,GAAArd,EAAA,CAAAkc,iBAAA,CAAAlc,EAAA,CAAAsd,iBAAA;IAAA;EAAA;;;YAA5C/R,4CAA4C;MAAAgS,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzd,EAAA,CAAA0d,0BAAA,EAAA1d,EAAA,CAAA2d,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnDjDje,EAHR,CAAAC,cAAA,aAAqE,iBAAgD,wBACnD,aACb,aACJ;UACvCD,EAAA,CAAAU,SAAA,aAAqD;UACrDV,EAAA,CAAAC,cAAA,UAAK;UACHD,EAAA,CAAAU,SAAA,qBAAiC;UAErCV,EADE,CAAAW,YAAA,EAAM,EACF;UAEJX,EADF,CAAAC,cAAA,aAAyC,cAC8C;UACnFD,EAAA,CAAAY,MAAA,IACF;UAGNZ,EAHM,CAAAW,YAAA,EAAO,EACH,EACF,EACS;UAQPX,EANV,CAAAC,cAAA,wBAAqC,eACZ,eAEiD,cACrB,eACJ,eACwC;;UAC7ED,EAAA,CAAAC,cAAA,eAAyF;UACvFD,EAAA,CAAAU,SAAA,gBAEO;UAEXV,EADE,CAAAW,YAAA,EAAM,EACF;;UAEJX,EADF,CAAAC,cAAA,WAAK,cACyC;UAAAD,EAAA,CAAAY,MAAA,IAAkB;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACnEX,EAAA,CAAAC,cAAA,aAAsC;UAAAD,EAAA,CAAAY,MAAA,4FAAc;UAExDZ,EAFwD,CAAAW,YAAA,EAAI,EACpD,EACF;UAKFX,EAFJ,CAAAC,cAAA,cAAyC,eACf,eACyB;UAC7CD,EAAA,CAAAY,MAAA,IACF;UAINZ,EAJM,CAAAW,YAAA,EAAM,EACF,EAEF,EACF;UA+BNX,EA5BA,CAAA0B,UAAA,KAAAyc,4DAAA,kBAA6D,KAAAC,4DAAA,mBA6BrB;UA+C1Cpe,EAAA,CAAAW,YAAA,EAAM;UAkBNX,EAfA,CAAA0B,UAAA,KAAA2c,4DAAA,mBAA8F,KAAAC,qEAAA,6BAeR;UA6W1Fte,EADE,CAAAW,YAAA,EAAM,EACO;UAMTX,EAHN,CAAAC,cAAA,0BAAiH,eACjE,eACmB,cACpB;;UACvCD,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAU,SAAA,gBACuE;UACzEV,EAAA,CAAAW,YAAA,EAAM;;UACNX,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAY,MAAA,IAA0C;UAClDZ,EADkD,CAAAW,YAAA,EAAO,EACnD;UAGJX,EADF,CAAAC,cAAA,cAAyC,cACE;UACvCD,EAAA,CAAAU,SAAA,eAAoD;UACpDV,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAY,MAAA,IAA2C;UAErDZ,EAFqD,CAAAW,YAAA,EAAO,EACpD,EACF;UAENX,EAAA,CAAAC,cAAA,eAAmC;UACjCD,EAAA,CAAAY,MAAA,kFACF;UAKVZ,EALU,CAAAW,YAAA,EAAM,EACF,EACF,EACS,EACT,EACN;UAGNX,EAAA,CAAA0B,UAAA,KAAA6c,qEAAA,2BAA8E;UA2F1Eve,EAHJ,CAAAC,cAAA,eAA2F,eAEnE,kBAI2D;UADxCD,EAAA,CAAAE,UAAA,mBAAAse,+EAAA;YAAA,OAASN,GAAA,CAAA3I,QAAA,EAAU;UAAA,EAAC;UAWzDvV,EAPA,CAAA0B,UAAA,KAAA+c,iEAAA,kBAA6G,KAAAC,iEAAA,kBAOZ;UAGnG1e,EAAA,CAAAW,YAAA,EAAS;UAGTX,EAAA,CAAAC,cAAA,eAC8K;UAC5KD,EAAA,CAAAY,MAAA,IACF;UACFZ,EADE,CAAAW,YAAA,EAAM,EACF;UAGNX,EAAA,CAAA0B,UAAA,KAAAid,+DAAA,qBAEqE;UAerE3e,EAAA,CAAAC,cAAA,kBAE6D;UAA3DD,EAAA,CAAAE,UAAA,mBAAA0e,+EAAA;YAAA,OAASV,GAAA,CAAApQ,MAAA,EAAQ;UAAA,EAAC;;UAClB9N,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAU,SAAA,gBAA6G;UAC/GV,EAAA,CAAAW,YAAA,EAAM;;UAGNX,EAAA,CAAAC,cAAA,eAC8K;UAC5KD,EAAA,CAAAY,MAAA,sBACF;UACFZ,EADE,CAAAW,YAAA,EAAM,EACC;UAGTX,EAAA,CAAAC,cAAA,kBAEmC;UAAjCD,EAAA,CAAAE,UAAA,mBAAA2e,+EAAA;YAAA,OAASX,GAAA,CAAAtD,OAAA,EAAS;UAAA,EAAC;;UACnB5a,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAU,SAAA,gBAA2G;UAC7GV,EAAA,CAAAW,YAAA,EAAM;;UAGNX,EAAA,CAAAC,cAAA,eAC8K;UAC5KD,EAAA,CAAAY,MAAA,kCACF;UACFZ,EADE,CAAAW,YAAA,EAAM,EACC;UAGTX,EAAA,CAAAC,cAAA,kBAGgC;UADkCD,EAAhE,CAAAE,UAAA,mBAAA4e,+EAAA;YAAA,OAASZ,GAAA,CAAA7C,cAAA,EAAgB;UAAA,EAAC,uBAAA0D,mFAAA;YAAA,OAAcb,GAAA,CAAAvQ,OAAA,CAAA2J,GAAA,CAAY,4CAAS,CAAC;UAAA,EAAC,qBAAA0H,iFAAA;YAAA,OAAYd,GAAA,CAAAvQ,OAAA,CAAA2J,GAAA,CAAY,4CAAS,CAAC;UAAA,EAAC;;UAElGtX,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAU,SAAA,gBAA4G;UAC9GV,EAAA,CAAAW,YAAA,EAAM;;UAGNX,EAAA,CAAAC,cAAA,eAC8K;UAC5KD,EAAA,CAAAY,MAAA,4BACF;UAGJZ,EAHI,CAAAW,YAAA,EAAM,EACC,EAEL;;;;UAlsBMX,EAAA,CAAAa,SAAA,IACF;UADEb,EAAA,CAAA0E,kBAAA,MAAAwZ,GAAA,CAAArR,YAAA,MACF;UAmBkD7M,EAAA,CAAAa,SAAA,IAAkB;UAAlBb,EAAA,CAAAoG,iBAAA,CAAA8X,GAAA,CAAArR,YAAA,CAAkB;UAS5D7M,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAA0E,kBAAA,yBAAAwZ,GAAA,CAAAjd,kBAAA,kBAAAid,GAAA,CAAAjd,kBAAA,CAAAD,MAAA,+BACF;UAOkBhB,EAAA,CAAAa,SAAA,EAAmC;UAAnCb,EAAA,CAAA8B,UAAA,SAAAoc,GAAA,CAAAjd,kBAAA,CAAAD,MAAA,KAAmC;UA6BxDhB,EAAA,CAAAa,SAAA,EAAmC;UAAnCb,EAAA,CAAA8B,UAAA,SAAAoc,GAAA,CAAAjd,kBAAA,CAAAD,MAAA,KAAmC;UAkDlChB,EAAA,CAAAa,SAAA,EAA4D;UAA5Db,EAAA,CAAA8B,UAAA,SAAAoc,GAAA,CAAA3c,WAAA,IAAA2c,GAAA,CAAAnd,0BAAA,CAAAC,MAAA,OAA4D;UAe5BhB,EAAA,CAAAa,SAAA,EAA+B;UAA/Bb,EAAA,CAAA8B,UAAA,YAAAoc,GAAA,CAAAnd,0BAAA,CAA+B;UAwX3Df,EAAA,CAAAa,SAAA,GAA0C;UAA1Cb,EAAA,CAAA0E,kBAAA,YAAAwZ,GAAA,CAAAjd,kBAAA,CAAAD,MAAA,yCAA0C;UAMxChB,EAAA,CAAAa,SAAA,GAA2C;UAA3Cb,EAAA,CAAA0E,kBAAA,kBAAAwZ,GAAA,CAAAjd,kBAAA,CAAAD,MAAA,6BAA2C;UAczBhB,EAAA,CAAAa,SAAA,GAAuB;UAAvBb,EAAA,CAAA8B,UAAA,YAAAoc,GAAA,CAAAjd,kBAAA,CAAuB;UA6FvDjB,EAAA,CAAAa,SAAA,GAAoC;UAApCb,EAAA,CAAA+D,WAAA,kBAAAma,GAAA,CAAA5S,YAAA,CAAoC;UACpCtL,EAAA,CAAA8B,UAAA,eAAAwD,QAAA,GAAA4Y,GAAA,CAAA3Y,YAAA,kBAAA2Y,GAAA,CAAA3Y,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,aAAA4Y,GAAA,CAAA5S,YAAA,CAA+D;UAGzDtL,EAAA,CAAAa,SAAA,EAAkB;UAAlBb,EAAA,CAAA8B,UAAA,SAAAoc,GAAA,CAAA5S,YAAA,CAAkB;UAOlBtL,EAAA,CAAAa,SAAA,EAAmB;UAAnBb,EAAA,CAAA8B,UAAA,UAAAoc,GAAA,CAAA5S,YAAA,CAAmB;UAQzBtL,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAA0E,kBAAA,MAAAwZ,GAAA,CAAA5S,YAAA,6DACF;UAIOtL,EAAA,CAAAa,SAAA,EAA2B;UAA3Bb,EAAA,CAAA8B,UAAA,SAAAoc,GAAA,CAAA3Y,YAAA,kBAAA2Y,GAAA,CAAA3Y,YAAA,CAAAC,OAAA,CAA2B;UAmBfxF,EAAA,CAAAa,SAAA,EAAyB;UAAzBb,EAAA,CAAA8B,UAAA,aAAAoc,GAAA,CAAA5S,YAAA,CAAyB;;;qBDhnBpChM,YAAY,EAAA0d,EAAA,CAAAiC,OAAA,EAAAjC,EAAA,CAAAkC,IAAA,EAAE3f,WAAW,EAAA4f,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAE7f,YAAY,EAAA8f,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAEvgB,eAAe,EAAAwgB,GAAA,CAAAC,yBAAA,EAAE5gB,gBAAgB,EAAEM,eAAe;MAAAugB,MAAA;MAAAC,eAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}