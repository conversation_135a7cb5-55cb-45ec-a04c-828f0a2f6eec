<!-- 戶別綁定元件使用說明 -->

<!--
在 notice-management.component.html 中新增戶別綁定元件的使用範例：

1. 首先確保在模組中導入 SharedModule
2. 在模板中使用新的戶別綁定元件

使用方式：
-->

<!-- 替換原有的戶別選擇表格 -->
<div class="form-group d-flex mb-0">
  <label for="houseList2D" baseLabel class="required-field mr-3" style="min-width:75px">適用戶別</label>
</div>

<!-- 新的戶別綁定元件 -->
<div class="household-binding-wrapper mt-2" *ngIf="isHouseList">
  <app-household-binding
    [(ngModel)]="selectedHouseholds"
    placeholder="請選擇適用戶別"
    [maxSelections]="maxSelections"
    [disabled]="saveSpecialNoticeFile?.selectedCNoticeType?.value == 1 || saveSpecialNoticeFile?.CExamineStauts == 0"
    [buildingData]="convertToHouseholdData()"
    [allowBatchSelect]="true"
    (selectionChange)="onHouseholdSelectionChange($event)">
  </app-household-binding>
</div>

<!-- 
在 notice-management.component.ts 中新增以下代碼：

import { HouseholdItem, BuildingData } from '../../shared/components/household-binding/household-binding.component';

export class NoticeManagementComponent extends BaseComponent implements OnInit {
  selectedHouseholds: string[] = [];
  maxSelections: number = 20;

  // 轉換現有的 houseList2D 為新元件所需的格式
  convertToHouseholdData(): BuildingData {
    const buildingData: BuildingData = {};
    
    if (!this.houseList2D || this.houseList2D.length === 0) {
      return buildingData;
    }

    // 將二維陣列轉換為以棟別分組的格式
    this.houseList2D.forEach(row => {
      row.forEach(house => {
        if (!house.CHouseHold) return;
        
        // 假設戶別格式為 A001, B002 等，第一個字母為棟別
        const building = house.CHouseHold.charAt(0) + '棟';
        
        if (!buildingData[building]) {
          buildingData[building] = [];
        }
        
        buildingData[building].push({
          code: house.CHouseHold,
          building: building,
          floor: house.CFloor ? `${house.CFloor}F` : undefined,
          houseId: house.CHouseID,
          houseType: house.CHouseType, // 保留戶別類型資訊供子元件篩選使用
          isSelected: house.CIsSelect || false,
          isDisabled: !house.CIsEnable || this.saveSpecialNoticeFile?.CExamineStauts === 0
          // 注意：不要在父元件中根據 selectedCNoticeType 預先篩選
          // 篩選邏輯應該由戶別綁定元件根據 preFilterHouseType 參數處理
        });
      });
    });
    
    return buildingData;
  }

  // 處理戶別選擇變更
  onHouseholdSelectionChange(selectedItems: HouseholdItem[]) {
    // 更新原有的 houseList2D 選擇狀態
    this.updateHouseListSelection(selectedItems.map(item => item.code));
    
    // 可以在這裡處理其他業務邏輯
    console.log('選中的戶別:', selectedItems);
  }

  // 更新原有資料結構的選擇狀態
  private updateHouseListSelection(selectedCodes: string[]) {
    if (!this.houseList2D) return;
    
    this.houseList2D.forEach(row => {
      row.forEach(house => {
        if (house.CHouseHold) {
          house.CIsSelect = selectedCodes.includes(house.CHouseHold);
        }
      });
    });
  }

  // 在 openModel 方法中初始化選中的戶別
  openModel(dialog: any, item?: any) {
    // ...existing code...
    
    if (item) {
      // 編輯模式：從現有數據初始化選中的戶別
      this.selectedHouseholds = item.CHouseHold || [];
    } else {
      // 新增模式：清空選中的戶別
      this.selectedHouseholds = [];
    }
    
    // ...existing code...
  }

  // 在保存時處理選中的戶別
  onSaveSpecialNoticeFile(ref: any) {
    // ...existing code...
    
    // 確保選中的戶別資料正確
    const selectedHouseholdData = this.houseList2D
      .flat()
      .filter(house => house.CIsSelect)
      .map(house => house.CHouseHold)
      .filter(household => household);
    
    this.saveSpecialNoticeFile.CHouse = selectedHouseholdData;
    
    // ...existing code...
  }
}
-->

<!-- 樣式調整 (可選) -->
<style>
.household-binding-wrapper {
  min-height: 200px;
  margin-bottom: 1rem;
}

.household-binding-wrapper .household-binding-container {
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  padding: 0.5rem;
  background-color: #fff;
}
</style>
