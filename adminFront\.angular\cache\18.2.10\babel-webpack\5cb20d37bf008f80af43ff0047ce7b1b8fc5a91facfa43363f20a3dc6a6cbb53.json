{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';\nimport { RequirementTemplateSelectorButtonComponent } from 'src/app/shared/components/requirement-template-selector-button/requirement-template-selector-button.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/components/space-template-selector/space-template-selector.service\";\nimport * as i10 from \"@angular/forms\";\nconst _c0 = [\"batchEditDialog\"];\nconst _c1 = () => [];\nfunction RequirementManagementComponent_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_68_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const batchEditDialog_r6 = i0.ɵɵreference(104);\n      return i0.ɵɵresetView(ctx_r4.openBatchEdit(batchEditDialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u6279\\u6B21\\u7DE8\\u8F2F (\", ctx_r4.selectedItems.length, \")\");\n  }\n}\nfunction RequirementManagementComponent_button_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_69_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r8 = i0.ɵɵreference(102);\n      return i0.ɵɵresetView(ctx_r4.add(dialog_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_99_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_99_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const data_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r8 = i0.ɵɵreference(102);\n      return i0.ɵɵresetView(ctx_r4.onEdit(data_r10, dialog_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_99_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_99_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const data_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onDelete(data_r10));\n    });\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_99_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 46)(1, \"td\", 47)(2, \"nb-checkbox\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function RequirementManagementComponent_tr_99_Template_nb_checkbox_ngModelChange_2_listener() {\n      const data_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleItemSelection(data_r10));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 48);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 48);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 49);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 49);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 49);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 49);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 49);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 49);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 49);\n    i0.ɵɵtemplate(22, RequirementManagementComponent_tr_99_button_22_Template, 3, 0, \"button\", 50)(23, RequirementManagementComponent_tr_99_button_23_Template, 3, 0, \"button\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r10 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.isItemSelected(data_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getHouseType(data_r10.CHouseType || i0.ɵɵpureFunction0(15, _c1)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 11, data_r10.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsShowText(data_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsSimpleText(data_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 13, data_r10.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_101_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_101_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_101_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r14.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r14.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_101_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r15.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r15.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_101_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 55)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_101_span_2_Template, 2, 0, \"span\", 56)(3, RequirementManagementComponent_ng_template_101_span_3_Template, 2, 0, \"span\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 57)(5, \"div\", 5)(6, \"div\", 58)(7, \"div\", 5)(8, \"app-form-group\", 59)(9, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_101_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CLocation, $event) || (ctx_r4.saveRequirement.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"app-form-group\", 59)(11, \"input\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_101_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRequirement, $event) || (ctx_r4.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"app-form-group\", 59)(13, \"input\", 62);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_101_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CSort, $event) || (ctx_r4.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"app-form-group\", 59)(15, \"nb-select\", 63);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_101_Template_nb_select_selectedChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CHouseType, $event) || (ctx_r4.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(16, RequirementManagementComponent_ng_template_101_nb_option_16_Template, 2, 2, \"nb-option\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 59)(18, \"nb-select\", 65);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_101_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CStatus, $event) || (ctx_r4.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_101_nb_option_19_Template, 2, 2, \"nb-option\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 59)(21, \"input\", 66);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_101_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnitPrice, $event) || (ctx_r4.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"app-form-group\", 59)(23, \"input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_101_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnit, $event) || (ctx_r4.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"app-form-group\", 59)(25, \"nb-checkbox\", 68);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_101_Template_nb_checkbox_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsShow, $event) || (ctx_r4.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(26, \" \\u986F\\u793A\\u5728\\u9810\\u7D04\\u9700\\u6C42\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 59)(28, \"nb-checkbox\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_101_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsSimple, $event) || (ctx_r4.saveRequirement.CIsSimple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u8A2D\\u5B9A\\u70BA\\u7C21\\u6613\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 59)(31, \"textarea\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_101_Template_textarea_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRemark, $event) || (ctx_r4.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\")(33, \"div\", 5)(34, \"div\", 71)(35, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_101_Template_button_click_35_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r13).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.save(ref_r16));\n    });\n    i0.ɵɵtext(36, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_101_Template_button_click_37_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r13).dialogRef;\n      return i0.ɵɵresetView(ref_r16.close());\n    });\n    i0.ɵɵtext(38, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5340\\u57DF\")(\"labelFor\", \"CLocation\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CLocation);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u9810\\u7D04\\u9700\\u6C42\")(\"labelFor\", \"CIsShow\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u7C21\\u6613\\u5BA2\\u8B8A\")(\"labelFor\", \"CIsSimple\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsSimple);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_103_li_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r18 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r18);\n  }\n}\nfunction RequirementManagementComponent_ng_template_103_div_16_nb_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r22.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r22.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_ng_template_103_div_16_nb_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r23.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_ng_template_103_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"nb-card\")(2, \"nb-card-header\", 84)(3, \"h6\", 85);\n    i0.ɵɵelement(4, \"i\", 86);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_103_div_16_Template_button_click_6_listener() {\n      const i_r20 = i0.ɵɵrestoreView(_r19).index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.resetBatchEditItem(i_r20));\n    });\n    i0.ɵɵelement(7, \"i\", 24);\n    i0.ɵɵtext(8, \"\\u91CD\\u7F6E \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"nb-card-body\", 88)(10, \"div\", 5)(11, \"div\", 21)(12, \"app-form-group\", 59)(13, \"input\", 89);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_103_div_16_Template_input_ngModelChange_13_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CLocation, $event) || (item_r21.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 21)(15, \"app-form-group\", 59)(16, \"input\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_103_div_16_Template_input_ngModelChange_16_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CRequirement, $event) || (item_r21.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 21)(18, \"app-form-group\", 59)(19, \"nb-select\", 91);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_103_div_16_Template_nb_select_selectedChange_19_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CHouseType, $event) || (item_r21.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(20, RequirementManagementComponent_ng_template_103_div_16_nb_option_20_Template, 2, 2, \"nb-option\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 21)(22, \"app-form-group\", 59)(23, \"input\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_103_div_16_Template_input_ngModelChange_23_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CSort, $event) || (item_r21.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 21)(25, \"app-form-group\", 59)(26, \"nb-select\", 93);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_103_div_16_Template_nb_select_selectedChange_26_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CStatus, $event) || (item_r21.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(27, RequirementManagementComponent_ng_template_103_div_16_nb_option_27_Template, 2, 2, \"nb-option\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 21)(29, \"app-form-group\", 59)(30, \"input\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_103_div_16_Template_input_ngModelChange_30_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CUnitPrice, $event) || (item_r21.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 21)(32, \"app-form-group\", 59)(33, \"input\", 95);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_103_div_16_Template_input_ngModelChange_33_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CUnit, $event) || (item_r21.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 21)(35, \"app-form-group\", 59)(36, \"nb-checkbox\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_103_div_16_Template_nb_checkbox_ngModelChange_36_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CIsShow, $event) || (item_r21.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(37, \" \\u986F\\u793A\\u5728\\u9810\\u7D04\\u9700\\u6C42\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 21)(39, \"app-form-group\", 59)(40, \"nb-checkbox\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_103_div_16_Template_nb_checkbox_ngModelChange_40_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CIsSimple, $event) || (item_r21.CIsSimple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(41, \" \\u8A2D\\u5B9A\\u70BA\\u7C21\\u6613\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"div\", 4)(43, \"app-form-group\", 59)(44, \"textarea\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_103_div_16_Template_textarea_ngModelChange_44_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CRemark, $event) || (item_r21.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(45, \"                      \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const item_r21 = ctx.$implicit;\n    const i_r20 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \\u9805\\u76EE \", i_r20 + 1, \": \", item_r21.CRequirement, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"label\", \"\\u5340\\u57DF\")(\"labelFor\", \"location_\" + i_r20)(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"location_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"requirement_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"requirement_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"houseType_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"houseType_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"selected\", item_r21.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"sort_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"sort_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"status_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"status_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"selected\", item_r21.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"unitPrice_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"unitPrice_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CUnitPrice);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"unit_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"unit_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CUnit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u9810\\u7D04\\u9700\\u6C42\")(\"labelFor\", \"isShow_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"isShow_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CIsShow);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", \"\\u7C21\\u6613\\u5BA2\\u8B8A\")(\"labelFor\", \"isSimple_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"isSimple_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CIsSimple);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"remark_\" + i_r20)(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"remark_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_103_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 74)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 75)(5, \"div\", 5)(6, \"div\", 4)(7, \"div\", 76);\n    i0.ɵɵelement(8, \"i\", 77);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 78);\n    i0.ɵɵelement(11, \"i\", 79);\n    i0.ɵɵelementStart(12, \"strong\");\n    i0.ɵɵtext(13, \"\\u6CE8\\u610F\\u4E8B\\u9805\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ul\", 80);\n    i0.ɵɵtemplate(15, RequirementManagementComponent_ng_template_103_li_15_Template, 2, 1, \"li\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, RequirementManagementComponent_ng_template_103_div_16_Template, 46, 54, \"div\", 82);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"nb-card-footer\")(18, \"div\", 5)(19, \"div\", 71)(20, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_103_Template_button_click_20_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.batchSave(ref_r24));\n    });\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_103_Template_button_click_22_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.cancelBatchEdit(ref_r24));\n    });\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.batchEditConfig.title, \" (\\u5171 \", ctx_r4.batchEditItems.length, \" \\u500B\\u9805\\u76EE)\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.batchEditConfig.noticeText, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.batchEditConfig.noticeItems);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.batchEditItems);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.batchEditConfig.confirmButtonText);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.batchEditConfig.cancelButtonText);\n  }\n}\nexport let RequirementManagementComponent = /*#__PURE__*/(() => {\n  class RequirementManagementComponent extends BaseComponent {\n    constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref, spaceTemplateSelectorService) {\n      super(_allow);\n      this._allow = _allow;\n      this.enumHelper = enumHelper;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this.buildCaseService = buildCaseService;\n      this.requirementService = requirementService;\n      this.pettern = pettern;\n      this.router = router;\n      this.destroyref = destroyref;\n      this.spaceTemplateSelectorService = spaceTemplateSelectorService;\n      // request\n      this.getListRequirementRequest = {};\n      this.getRequirementRequest = {};\n      // response\n      this.buildCaseList = [];\n      this.requirementList = [];\n      this.saveRequirement = {\n        CHouseType: []\n      };\n      this.statusOptions = [{\n        value: 0,\n        label: '停用'\n      }, {\n        value: 1,\n        label: '啟用'\n      }];\n      this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n      this.isNew = false;\n      this.currentBuildCase = 0;\n      // 批次編輯相關屬性\n      this.selectedItems = [];\n      this.isAllSelected = false;\n      this.isBatchEditMode = false;\n      // 批次編輯時的項目資料副本\n      this.batchEditItems = [];\n      // 批次編輯配置\n      this.batchEditConfig = this.getDefaultBatchEditConfig();\n      this.initializeSearchForm();\n      this.getBuildCaseList();\n    }\n    ngOnInit() {}\n    // 初始化搜尋表單\n    initializeSearchForm() {\n      this.getListRequirementRequest.CStatus = -1;\n      this.getListRequirementRequest.CIsShow = null;\n      this.getListRequirementRequest.CIsSimple = null;\n      this.getListRequirementRequest.CRequirement = '';\n      this.getListRequirementRequest.CLocation = '';\n      // 預設全選所有房屋類型\n      this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n    }\n    // 取得預設批次編輯配置\n    getDefaultBatchEditConfig() {\n      return {\n        title: '批次編輯建案需求',\n        noticeText: '您可以個別修改每個項目的欄位',\n        noticeItems: ['工程項目、類型、排序、狀態、單價、單位為必填欄位', '排序和單價不能為負數', '區域最多20個字，工程項目最多50個字，備註說明最多100個字'],\n        confirmButtonText: '確定批次更新',\n        cancelButtonText: '取消'\n      };\n    }\n    // 取得模板新增批次編輯配置\n    getTemplateAddBatchEditConfig() {\n      return {\n        title: '模板新增批次編輯',\n        noticeText: '從模板載入的項目，您可以個別修改每個項目的欄位',\n        noticeItems: ['工程項目、類型、排序、狀態、單價、單位為必填欄位', '排序和單價不能為負數', '區域最多20個字，工程項目最多50個字，備註說明最多100個字', '模板項目已自動填入預設值，請檢查並調整各項目設定'],\n        confirmButtonText: '確定新增項目',\n        cancelButtonText: '取消'\n      };\n    }\n    // 建案切換事件處理\n    onBuildCaseChange(newBuildCaseId) {\n      // 如果在批次編輯模式下切換建案，給予警告\n      if (this.isBatchEditMode || this.selectedItems.length > 0) {\n        const confirmMessage = this.isBatchEditMode ? '切換建案將會關閉批次編輯對話框並清除所有選擇的項目，是否繼續？' : `切換建案將會清除已選擇的 ${this.selectedItems.length} 個項目，是否繼續？`;\n        if (!confirm(confirmMessage)) {\n          // 使用者取消，恢復原來的建案選擇\n          setTimeout(() => {\n            this.getListRequirementRequest.CBuildCaseID = this.currentBuildCase;\n          }, 0);\n          return;\n        }\n      }\n      // 重置批次選擇的項目\n      this.selectedItems = [];\n      this.isAllSelected = false;\n      this.batchEditItems = [];\n      this.isBatchEditMode = false;\n      // 更新當前建案並重新載入資料\n      this.currentBuildCase = newBuildCaseId;\n      this.getList();\n    }\n    // 重置搜尋\n    resetSearch() {\n      this.initializeSearchForm();\n      // 清除選擇狀態和批次編輯相關狀態\n      this.selectedItems = [];\n      this.isAllSelected = false;\n      this.batchEditItems = [];\n      this.isBatchEditMode = false;\n      // 重置後如果有建案資料，重新設定預設選擇第一個建案\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\n        setTimeout(() => {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n          this.getList();\n        }, 0);\n      } else {\n        this.getList();\n      }\n    }\n    getHouseType(hTypes) {\n      if (!hTypes) {\n        return '';\n      }\n      if (!Array.isArray(hTypes)) {\n        hTypes = [hTypes];\n      }\n      let labels = [];\n      hTypes.forEach(htype => {\n        let findH = this.houseType.find(x => x.value == htype);\n        if (findH) {\n          labels.push(findH.label);\n        }\n      });\n      return labels.join(', ');\n    }\n    validation() {\n      this.valid.clear();\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n      this.valid.required('[需求]', this.saveRequirement.CRequirement);\n      this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n      this.valid.required('[排序]', this.saveRequirement.CSort);\n      this.valid.required('[狀態]', this.saveRequirement.CStatus);\n      this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n      this.valid.required('[單位]', this.saveRequirement.CUnit);\n      // 數值範圍驗證\n      if (this.saveRequirement.CSort !== null && this.saveRequirement.CSort !== undefined && this.saveRequirement.CSort < 0) {\n        this.valid.errorMessages.push('[排序] 不能為負數');\n      }\n      if (this.saveRequirement.CUnitPrice !== null && this.saveRequirement.CUnitPrice !== undefined && this.saveRequirement.CUnitPrice < 0) {\n        this.valid.errorMessages.push('[單價] 不能為負數');\n      }\n      // 長度驗證\n      if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {\n        this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n      }\n      if (this.saveRequirement.CRequirement && this.saveRequirement.CRequirement.length > 50) {\n        this.valid.errorMessages.push('[工程項目] 不能超過50個字');\n      }\n      // 備註說明長度驗證\n      if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n        this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n      }\n    }\n    add(dialog) {\n      this.isNew = true;\n      this.saveRequirement = {\n        CHouseType: [],\n        CIsShow: false,\n        CIsSimple: false\n      };\n      this.saveRequirement.CStatus = 1;\n      this.saveRequirement.CUnitPrice = 0;\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n      this.dialogService.open(dialog);\n    }\n    onEdit(data, dialog) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n        _this.isNew = false;\n        try {\n          yield _this.getData();\n          _this.dialogService.open(dialog);\n        } catch (error) {\n          console.log(\"Failed to get function data\", error);\n        }\n      })();\n    }\n    save(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this.requirementService.apiRequirementSaveDataPost$Json({\n        body: this.saveRequirement\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getList();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n      ref.close();\n    }\n    onDelete(data) {\n      this.saveRequirement.CRequirementID = data.CRequirementID;\n      this.isNew = false;\n      if (window.confirm('是否確定刪除?')) {\n        this.remove();\n      } else {\n        return;\n      }\n    }\n    remove() {\n      this.requirementService.apiRequirementDeleteDataPost$Json({\n        body: {\n          CRequirementID: this.saveRequirement.CRequirementID\n        }\n      }).subscribe(res => {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      });\n    }\n    getBuildCaseList() {\n      this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n        body: {}\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n        this.buildCaseList = res.Entries;\n        // 如果有建案時才查詢\n        if (this.buildCaseList.length > 0) {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n          this.getList();\n        }\n      });\n    }\n    getList() {\n      this.getListRequirementRequest.PageSize = this.pageSize;\n      this.getListRequirementRequest.PageIndex = this.pageIndex;\n      this.requirementList = [];\n      this.totalRecords = 0;\n      // 建案頁面的邏輯\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n      this.requirementService.apiRequirementGetListPost$Json({\n        body: this.getListRequirementRequest\n      }).pipe().subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries) {\n            this.requirementList = res.Entries;\n            this.totalRecords = res.TotalItems;\n            // 清理已選擇的項目，移除不存在於新列表中的項目\n            const originalSelectedCount = this.selectedItems.length;\n            this.selectedItems = this.selectedItems.filter(selectedItem => this.requirementList.some(listItem => listItem.CRequirementID === selectedItem.CRequirementID));\n            // 如果選擇的項目數量有變化，清理批次編輯狀態\n            if (originalSelectedCount !== this.selectedItems.length || this.selectedItems.length === 0) {\n              this.batchEditItems = [];\n              this.isBatchEditMode = false;\n            }\n            // 更新選擇狀態\n            this.updateSelectAllState();\n          }\n        }\n      });\n    }\n    getData() {\n      this.requirementService.apiRequirementGetDataPost$Json({\n        body: this.getRequirementRequest\n      }).pipe().subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries) {\n            this.saveRequirement = {\n              CHouseType: [],\n              CIsShow: false,\n              CIsSimple: false\n            };\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n            this.saveRequirement.CLocation = res.Entries.CLocation;\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n            this.saveRequirement.CRemark = res.Entries.CRemark;\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n            this.saveRequirement.CSort = res.Entries.CSort;\n            this.saveRequirement.CStatus = res.Entries.CStatus;\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\n            this.saveRequirement.CUnit = res.Entries.CUnit;\n            this.saveRequirement.CSpaceId = res.Entries.CSpaceId || null;\n            this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n            this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\n          }\n        }\n      });\n    }\n    onHouseTypeChange(value, checked) {\n      console.log(checked);\n      if (checked) {\n        if (!this.saveRequirement.CHouseType?.includes(value)) {\n          this.saveRequirement.CHouseType?.push(value);\n        }\n        console.log(this.saveRequirement.CHouseType);\n      } else {\n        this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n      }\n    }\n    getCIsShowText(data) {\n      return data.CIsShow ? '是' : '否';\n    }\n    getCIsSimpleText(data) {\n      return data.CIsSimple ? '是' : '否';\n    }\n    // 空間模板相關方法\n    onSpaceTemplateApplied(config) {\n      console.log('套用空間模板配置:', config);\n      // 直接將模板項目轉換為批次編輯項目，使用從模板選擇器傳來的明細數據\n      this.convertTemplatesToBatchEdit(config.selectedItems, config.templateDetails);\n    }\n    onTemplateError(errorMessage) {\n      this.message.showErrorMSG(errorMessage);\n    }\n    // 需求選擇相關方法\n    onRequirementSelectionConfirmed(config) {\n      console.log('確認需求選擇配置:', config);\n      // 將選擇的需求項目加入到批次編輯\n      this.convertRequirementsToSelection(config.selectedItems);\n      this.message.showSucessMSG(`已選擇 ${config.selectedItems.length} 個需求項目`);\n    }\n    onRequirementSelectionCancelled() {\n      console.log('取消需求選擇');\n    }\n    onRequirementSelectionError(errorMessage) {\n      this.message.showErrorMSG(errorMessage);\n    }\n    // 將選擇的需求項目轉換為選擇狀態\n    convertRequirementsToSelection(selectedRequirements) {\n      if (!selectedRequirements || selectedRequirements.length === 0) {\n        return;\n      }\n      // 清除當前選擇\n      this.selectedItems = [];\n      this.isAllSelected = false;\n      // 將選擇的需求項目加入到當前選擇列表\n      selectedRequirements.forEach(requirement => {\n        const existingItem = this.requirementList.find(item => item.CRequirementID === requirement.CRequirementID);\n        if (existingItem) {\n          this.selectedItems.push(existingItem);\n        }\n      });\n      // 更新全選狀態\n      this.updateSelectAllState();\n    }\n    // 將模板項目轉換為批次編輯項目\n    convertTemplatesToBatchEdit(selectedTemplates, templateDetails) {\n      if (!this.getListRequirementRequest.CBuildCaseID) {\n        this.message.showErrorMSG('建案 ID 不存在');\n        return;\n      }\n      // 清除當前選擇的項目\n      this.selectedItems = [];\n      this.isAllSelected = false;\n      // 取得預設排序值 - 從列表最大排序值開始\n      const maxSort = this.requirementList.length > 0 ? Math.max(...this.requirementList.map(item => item.CSort || 0)) : 0;\n      let currentSortIndex = 0;\n      const allBatchEditItems = [];\n      // 處理每個模板的明細項目\n      selectedTemplates.forEach(template => {\n        const details = templateDetails.get(template.CTemplateId) || [];\n        if (details && details.length > 0) {\n          // 將每個明細項目轉換為批次編輯項目\n          details.forEach((detail, detailIndex) => {\n            const batchEditItem = {\n              CRequirementID: undefined,\n              // 新項目沒有 ID\n              CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n              CLocation: detail.CLocation || template.CLocation || '',\n              // 優先使用明細的位置，其次使用模板位置\n              CRequirement: detail.CPart || `${template.CTemplateName} - 項目 ${detailIndex + 1}`,\n              // 使用明細名稱作為工程項目\n              CHouseType: template.CHouseType || [],\n              // 從模板獲取房屋類型，預設為空陣列\n              CSort: maxSort + currentSortIndex + 1,\n              // 從最大排序值往上遞增\n              CStatus: 1,\n              // 預設啟用\n              CUnitPrice: detail.CUnitPrice || template.CUnitPrice || 0,\n              // 優先使用明細單價，其次使用模板單價\n              CUnit: detail.CUnit || template.CUnit || '式',\n              // 優先使用明細單位，其次使用模板單位\n              CSpaceId: detail.CReleateId || template.cRelateID || null,\n              // 從明細獲取關聯空間 ID\n              CIsShow: template.CIsShow !== undefined ? template.CIsShow : false,\n              // 預設不顯示在預約需求\n              CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false,\n              // 預設不是簡易客變\n              CRemark: detail.CRemark || `從模板「${template.CTemplateName}」的明細項目「${detail.CPart}」產生` // 記錄來源模板和明細\n            };\n            allBatchEditItems.push(batchEditItem);\n            currentSortIndex++;\n          });\n        } else {\n          // 如果模板沒有明細，則將模板本身作為一個項目\n          const batchEditItem = {\n            CRequirementID: undefined,\n            // 新項目沒有 ID\n            CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n            CLocation: template.CLocation || '',\n            // 從模板獲取區域資訊\n            CRequirement: template.CTemplateName || `模板項目 ${currentSortIndex + 1}`,\n            // 使用模板名稱作為工程項目\n            CHouseType: template.CHouseType || [],\n            // 從模板獲取房屋類型，預設為空陣列\n            CSort: maxSort + currentSortIndex + 1,\n            // 從最大排序值往上遞增\n            CStatus: 1,\n            // 預設啟用\n            CUnitPrice: template.CUnitPrice || 0,\n            // 從模板獲取單價，預設為 0\n            CUnit: template.CUnit || '式',\n            // 從模板獲取單位，預設為 '式'\n            CSpaceId: template.cRelateID || null,\n            // 從模板獲取關聯空間 ID\n            CIsShow: template.CIsShow !== undefined ? template.CIsShow : false,\n            // 預設不顯示在預約需求\n            CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false,\n            // 預設不是簡易客變\n            CRemark: template.CRemark || `從模板「${template.CTemplateName}」產生` // 記錄來源模板\n          };\n          allBatchEditItems.push(batchEditItem);\n          currentSortIndex++;\n        }\n      });\n      // 設定批次編輯項目\n      this.batchEditItems = allBatchEditItems;\n      // 設定為模板新增批次編輯模式，使用專用配置\n      this.batchEditConfig = this.getTemplateAddBatchEditConfig();\n      this.isBatchEditMode = true;\n      // 計算總明細數量\n      const totalDetailCount = selectedTemplates.reduce((sum, template) => {\n        const details = templateDetails.get(template.CTemplateId) || [];\n        return sum + (details.length || 1);\n      }, 0);\n      // 直接開啟批次編輯對話框\n      setTimeout(() => {\n        this.dialogService.open(this.batchEditDialog);\n      }, 100);\n    } // 批次編輯相關方法\n    // 切換單一項目選擇狀態\n    toggleItemSelection(item) {\n      const index = this.selectedItems.findIndex(selected => selected.CRequirementID === item.CRequirementID);\n      if (index > -1) {\n        this.selectedItems.splice(index, 1);\n      } else {\n        this.selectedItems.push(item);\n      }\n      this.updateSelectAllState();\n    }\n    // 切換全選狀態\n    toggleSelectAll(newValue) {\n      if (this.requirementList.length === 0) {\n        this.selectedItems = [];\n        this.isAllSelected = false;\n        return;\n      }\n      // 更新 isAllSelected 狀態\n      this.isAllSelected = newValue;\n      // 根據新值更新 selectedItems\n      if (this.isAllSelected) {\n        this.selectedItems = [...this.requirementList];\n      } else {\n        this.selectedItems = [];\n      }\n    }\n    // 更新全選狀態\n    updateSelectAllState() {\n      if (this.requirementList.length === 0) {\n        this.isAllSelected = false;\n      } else {\n        this.isAllSelected = this.selectedItems.length === this.requirementList.length;\n      }\n    }\n    // 檢查項目是否被選中\n    isItemSelected(item) {\n      return this.selectedItems.some(selected => selected.CRequirementID === item.CRequirementID);\n    }\n    // 開啟批次編輯對話框\n    openBatchEdit(dialog, config) {\n      if (this.selectedItems.length === 0) {\n        this.message.showErrorMSG('請先選擇要編輯的項目');\n        return;\n      }\n      // 設定批次編輯配置\n      this.batchEditConfig = config || this.getDefaultBatchEditConfig();\n      this.isBatchEditMode = true;\n      // 初始化批次編輯項目資料\n      this.batchEditItems = this.selectedItems.map(item => ({\n        CRequirementID: item.CRequirementID,\n        CBuildCaseID: item.CBuildCaseID,\n        CLocation: item.CLocation,\n        CRequirement: item.CRequirement,\n        CHouseType: item.CHouseType ? [...item.CHouseType] : [],\n        CSort: item.CSort,\n        CStatus: item.CStatus,\n        CUnitPrice: item.CUnitPrice || 0,\n        CUnit: item.CUnit,\n        CSpaceId: item.CSpaceId || null,\n        CIsShow: item.CIsShow || false,\n        CIsSimple: item.CIsSimple || false,\n        CRemark: item.CRemark\n      }));\n      this.dialogService.open(dialog);\n    }\n    // 批次驗證方法\n    batchValidation() {\n      const errorMessages = [];\n      this.batchEditItems.forEach((item, index) => {\n        const itemNum = index + 1;\n        // 必填欄位檢核\n        if (!item.CBuildCaseID) {\n          errorMessages.push(`[項目 ${itemNum}] 建案名稱為必填欄位`);\n        }\n        if (!item.CRequirement || item.CRequirement.trim() === '') {\n          errorMessages.push(`[項目 ${itemNum}] 工程項目為必填欄位`);\n        }\n        if (!item.CHouseType || item.CHouseType.length === 0) {\n          errorMessages.push(`[項目 ${itemNum}] 類型為必填欄位`);\n        }\n        if (item.CSort === null || item.CSort === undefined || item.CSort < 0) {\n          errorMessages.push(`[項目 ${itemNum}] 排序為必填欄位且不能為負數`);\n        }\n        if (item.CStatus === null || item.CStatus === undefined) {\n          errorMessages.push(`[項目 ${itemNum}] 狀態為必填欄位`);\n        }\n        if (item.CUnitPrice === null || item.CUnitPrice === undefined || item.CUnitPrice < 0) {\n          errorMessages.push(`[項目 ${itemNum}] 單價為必填欄位且不能為負數`);\n        }\n        if (!item.CUnit || item.CUnit.trim() === '') {\n          errorMessages.push(`[項目 ${itemNum}] 單位為必填欄位`);\n        }\n        // 長度驗證\n        if (item.CLocation && item.CLocation.length > 20) {\n          errorMessages.push(`[項目 ${itemNum}] 區域不能超過20個字`);\n        }\n        if (item.CRequirement && item.CRequirement.length > 50) {\n          errorMessages.push(`[項目 ${itemNum}] 工程項目不能超過50個字`);\n        }\n        if (item.CRemark && item.CRemark.length > 100) {\n          errorMessages.push(`[項目 ${itemNum}] 備註說明不能超過100個字`);\n        }\n      });\n      return errorMessages;\n    }\n    // 批次保存\n    batchSave(ref) {\n      if (this.batchEditItems.length === 0) {\n        this.message.showErrorMSG('沒有要更新的項目');\n        return;\n      }\n      // 執行批次驗證\n      const validationErrors = this.batchValidation();\n      if (validationErrors.length > 0) {\n        this.message.showErrorMSGs(validationErrors);\n        return;\n      }\n      // 分離新增項目和更新項目\n      const newItems = this.batchEditItems.filter(item => !item.CRequirementID);\n      const updateItems = this.batchEditItems.filter(item => item.CRequirementID);\n      // 建立請求陣列\n      const requests = [];\n      // 新增項目的請求\n      newItems.forEach(item => {\n        const newItemData = {\n          CBuildCaseID: item.CBuildCaseID,\n          CLocation: item.CLocation,\n          CRequirement: item.CRequirement,\n          CHouseType: item.CHouseType,\n          CSort: item.CSort,\n          CStatus: item.CStatus,\n          CUnitPrice: item.CUnitPrice,\n          CUnit: item.CUnit,\n          CSpaceId: item.CSpaceId,\n          CIsShow: item.CIsShow,\n          CIsSimple: item.CIsSimple,\n          CRemark: item.CRemark\n        };\n        requests.push(this.requirementService.apiRequirementSaveDataPost$Json({\n          body: newItemData\n        }).toPromise());\n      });\n      // 更新項目的請求（如果有的話）\n      if (updateItems.length > 0) {\n        const updateData = updateItems.map(item => ({\n          CRequirementID: item.CRequirementID,\n          CBuildCaseID: item.CBuildCaseID,\n          CLocation: item.CLocation,\n          CRequirement: item.CRequirement,\n          CHouseType: item.CHouseType,\n          CSort: item.CSort,\n          CStatus: item.CStatus,\n          CUnitPrice: item.CUnitPrice,\n          CUnit: item.CUnit,\n          CSpaceId: item.CSpaceId,\n          CIsShow: item.CIsShow,\n          CIsSimple: item.CIsSimple,\n          CRemark: item.CRemark\n        }));\n        requests.push(this.requirementService.apiRequirementBatchSaveDataPost$Json({\n          body: updateData\n        }).toPromise());\n      }\n      // 執行所有請求\n      Promise.all(requests).then(responses => {\n        const successCount = responses.filter(res => res?.StatusCode === 0).length;\n        const totalItems = this.batchEditItems.length;\n        if (successCount === responses.length) {\n          this.message.showSucessMSG(`成功處理 ${totalItems} 個項目 (新增: ${newItems.length}, 更新: ${updateItems.length})`);\n        } else {\n          this.message.showSucessMSG(`成功處理 ${successCount} 個項目，${responses.length - successCount} 個失敗`);\n        }\n        // 清理狀態並重新載入資料\n        this.selectedItems = [];\n        this.batchEditItems = [];\n        this.isBatchEditMode = false;\n        this.updateSelectAllState();\n        this.getList();\n        ref.close();\n      }).catch(error => {\n        console.error('批次保存失敗:', error);\n        this.message.showErrorMSG('批次保存時發生錯誤');\n      });\n    }\n    // 重置批次編輯中的單一項目到原始狀態\n    resetBatchEditItem(index) {\n      const originalItem = this.selectedItems[index];\n      if (originalItem) {\n        this.batchEditItems[index] = {\n          CRequirementID: originalItem.CRequirementID,\n          CBuildCaseID: originalItem.CBuildCaseID,\n          CLocation: originalItem.CLocation,\n          CRequirement: originalItem.CRequirement,\n          CHouseType: originalItem.CHouseType ? [...originalItem.CHouseType] : [],\n          CSort: originalItem.CSort,\n          CStatus: originalItem.CStatus,\n          CUnitPrice: originalItem.CUnitPrice || 0,\n          CUnit: originalItem.CUnit,\n          CSpaceId: originalItem.CSpaceId || null,\n          CIsShow: originalItem.CIsShow || false,\n          CIsSimple: originalItem.CIsSimple || false,\n          CRemark: originalItem.CRemark\n        };\n      }\n    }\n    // 取消批次編輯\n    cancelBatchEdit(ref) {\n      this.isBatchEditMode = false;\n      this.batchEditItems = [];\n      ref.close();\n    }\n    // 備用：如果需要手動建立需求項目的方法\n    batchCreateRequirements(requirements) {\n      const batchRequests = requirements.map(requirement => this.requirementService.apiRequirementSaveDataPost$Json({\n        body: requirement\n      }));\n      Promise.all(batchRequests.map(req => req.toPromise())).then(responses => {\n        const successCount = responses.filter(res => res?.StatusCode === 0).length;\n        this.message.showSucessMSG(`成功建立 ${successCount} 個需求項目`);\n        this.getList();\n      }).catch(error => {\n        console.error('批次建立需求失敗:', error);\n        this.message.showErrorMSG('批次建立需求時發生錯誤');\n      });\n    }\n    static {\n      this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef), i0.ɵɵdirectiveInject(i9.SpaceTemplateSelectorService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RequirementManagementComponent,\n        selectors: [[\"app-requirement-management\"]],\n        viewQuery: function RequirementManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchEditDialog = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 105,\n        vars: 36,\n        consts: [[\"dialog\", \"\"], [\"batchEditDialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\", \"pb-0\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u5340\\u57DF\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [3, \"value\"], [\"for\", \"isShow\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5168\\u90E8\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"isSimple\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [3, \"templateApplied\", \"error\", \"buildCaseId\", \"text\", \"icon\", \"buttonClass\", \"disabled\"], [3, \"selectionConfirmed\", \"selectionCancelled\", \"error\", \"buildCaseId\", \"text\", \"icon\", \"buttonClass\", \"disabled\", \"multiple\"], [\"class\", \"btn btn-primary mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"col-12\", \"mt-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\", \"d-flex\", \"flex-column\", \"align-items-center\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"text-white\", \"mt-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"btn\", \"btn-primary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-1\", \"text-center\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CLocation\", \"name\", \"CLocation\", \"placeholder\", \"\\u5340\\u57DF\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CSort\", \"name\", \"CSort\", \"placeholder\", \"\\u6392\\u5E8F\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", \"step\", \"0.01\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CUnit\", \"name\", \"CUnit\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsShow\", \"name\", \"CIsShow\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsSimple\", \"name\", \"CIsSimple\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"langg\", \"\", 3, \"value\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"900px\"], [2, \"padding\", \"1rem 2rem\", \"max-height\", \"70vh\", \"overflow-y\", \"auto\"], [1, \"alert\", \"alert-info\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [1, \"alert\", \"alert-warning\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-2\"], [1, \"mb-0\", \"mt-2\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"mb-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"mb-4\"], [1, \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [\"type\", \"button\", \"title\", \"\\u91CD\\u7F6E\\u70BA\\u539F\\u59CB\\u503C\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"py-3\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5340\\u57DF\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"id\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6392\\u5E8F\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"id\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u55AE\\u50F9\", \"step\", \"0.01\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"nbInput\", \"\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"2\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"]],\n        template: function RequirementManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"label\", 7);\n            i0.ɵɵtext(8, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"nb-select\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CBuildCaseID, $event) || (ctx.getListRequirementRequest.CBuildCaseID = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onBuildCaseChange($event));\n            });\n            i0.ɵɵtemplate(10, RequirementManagementComponent_nb_option_10_Template, 2, 2, \"nb-option\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 6)(12, \"label\", 10);\n            i0.ɵɵtext(13, \"\\u5340\\u57DF\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CLocation, $event) || (ctx.getListRequirementRequest.CLocation = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 6)(16, \"label\", 12);\n            i0.ɵɵtext(17, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_18_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"div\", 5)(20, \"div\", 6)(21, \"label\", 14);\n            i0.ɵɵtext(22, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"nb-select\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_23_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(24, RequirementManagementComponent_nb_option_24_Template, 2, 2, \"nb-option\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"div\", 6)(26, \"label\", 16);\n            i0.ɵɵtext(27, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"nb-select\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_28_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(29, \"nb-option\", 17);\n            i0.ɵɵtext(30, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"nb-option\", 17);\n            i0.ɵɵtext(32, \"\\u555F\\u7528\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"nb-option\", 17);\n            i0.ɵɵtext(34, \"\\u505C\\u7528\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(35, \"div\", 6)(36, \"label\", 18);\n            i0.ɵɵtext(37, \"\\u9810\\u7D04\\u9700\\u6C42\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"nb-select\", 19);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_38_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsShow, $event) || (ctx.getListRequirementRequest.CIsShow = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(39, \"nb-option\", 17);\n            i0.ɵɵtext(40, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"nb-option\", 17);\n            i0.ɵɵtext(42, \"\\u662F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"nb-option\", 17);\n            i0.ɵɵtext(44, \"\\u5426\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(45, \"div\", 5)(46, \"div\", 6)(47, \"label\", 20);\n            i0.ɵɵtext(48, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"nb-select\", 19);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_49_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsSimple, $event) || (ctx.getListRequirementRequest.CIsSimple = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(50, \"nb-option\", 17);\n            i0.ɵɵtext(51, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"nb-option\", 17);\n            i0.ɵɵtext(53, \"\\u662F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"nb-option\", 17);\n            i0.ɵɵtext(55, \"\\u5426\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelement(56, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"div\", 5);\n            i0.ɵɵelement(58, \"div\", 21);\n            i0.ɵɵelementStart(59, \"div\", 22)(60, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_60_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.resetSearch());\n            });\n            i0.ɵɵelement(61, \"i\", 24);\n            i0.ɵɵtext(62, \"\\u91CD\\u7F6E\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_63_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getList());\n            });\n            i0.ɵɵelement(64, \"i\", 26);\n            i0.ɵɵtext(65, \"\\u67E5\\u8A62\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"app-space-template-selector-button\", 27);\n            i0.ɵɵlistener(\"templateApplied\", function RequirementManagementComponent_Template_app_space_template_selector_button_templateApplied_66_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSpaceTemplateApplied($event));\n            })(\"error\", function RequirementManagementComponent_Template_app_space_template_selector_button_error_66_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onTemplateError($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"app-requirement-template-selector-button\", 28);\n            i0.ɵɵlistener(\"selectionConfirmed\", function RequirementManagementComponent_Template_app_requirement_template_selector_button_selectionConfirmed_67_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onRequirementSelectionConfirmed($event));\n            })(\"selectionCancelled\", function RequirementManagementComponent_Template_app_requirement_template_selector_button_selectionCancelled_67_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onRequirementSelectionCancelled());\n            })(\"error\", function RequirementManagementComponent_Template_app_requirement_template_selector_button_error_67_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onRequirementSelectionError($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(68, RequirementManagementComponent_button_68_Template, 3, 1, \"button\", 29)(69, RequirementManagementComponent_button_69_Template, 3, 0, \"button\", 30);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(70, \"nb-card-body\", 3)(71, \"div\", 31)(72, \"div\", 32)(73, \"table\", 33)(74, \"thead\")(75, \"tr\", 34)(76, \"th\", 35)(77, \"nb-checkbox\", 36);\n            i0.ɵɵlistener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_checkbox_ngModelChange_77_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.toggleSelectAll($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"small\", 37);\n            i0.ɵɵtext(79, \"\\u5168\\u9078\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(80, \"th\", 38);\n            i0.ɵɵtext(81, \"\\u5340\\u57DF\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(82, \"th\", 38);\n            i0.ɵɵtext(83, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(84, \"th\", 39);\n            i0.ɵɵtext(85, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"th\", 39);\n            i0.ɵɵtext(87, \"\\u6392\\u5E8F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(88, \"th\", 39);\n            i0.ɵɵtext(89, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(90, \"th\", 39);\n            i0.ɵɵtext(91, \"\\u9810\\u7D04\\u9700\\u6C42\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(92, \"th\", 39);\n            i0.ɵɵtext(93, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(94, \"th\", 39);\n            i0.ɵɵtext(95, \"\\u55AE\\u50F9\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(96, \"th\", 39);\n            i0.ɵɵtext(97, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(98, \"tbody\");\n            i0.ɵɵtemplate(99, RequirementManagementComponent_tr_99_Template, 24, 16, \"tr\", 40);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(100, \"ngx-pagination\", 41);\n            i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_100_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_100_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getList());\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(101, RequirementManagementComponent_ng_template_101_Template, 39, 44, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(103, RequirementManagementComponent_ng_template_103_Template, 24, 7, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CBuildCaseID);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CLocation);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", -1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsShow);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", false);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsSimple);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", false);\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"buildCaseId\", (ctx.getListRequirementRequest.CBuildCaseID == null ? null : ctx.getListRequirementRequest.CBuildCaseID.toString()) || \"\")(\"text\", \"\\u7A7A\\u9593\\u6A21\\u677F\\u532F\\u5165\")(\"icon\", \"fas fa-layer-group\")(\"buttonClass\", \"btn btn-warning mr-2\")(\"disabled\", !ctx.getListRequirementRequest.CBuildCaseID);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"buildCaseId\", (ctx.getListRequirementRequest.CBuildCaseID == null ? null : ctx.getListRequirementRequest.CBuildCaseID.toString()) || \"\")(\"text\", \"\\u9700\\u6C42\\u9805\\u76EE\\u9078\\u64C7\")(\"icon\", \"fas fa-list-check\")(\"buttonClass\", \"btn btn-info mr-2\")(\"disabled\", !ctx.getListRequirementRequest.CBuildCaseID)(\"multiple\", true);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedItems.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngModel\", ctx.isAllSelected);\n            i0.ɵɵadvance(22);\n            i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          }\n        },\n        dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i10.DefaultValueAccessor, i10.NumberValueAccessor, i10.NgControlStatus, i10.MaxLengthValidator, i10.MinValidator, i10.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent, NumberWithCommasPipe, SpaceTemplateSelectorButtonComponent, RequirementTemplateSelectorButtonComponent],\n        styles: [\".table-active[_ngcontent-%COMP%]{background-color:#e3f2fd!important;border-left:3px solid #2196f3}.page-description-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa,#e9ecef);padding:1.25rem;border-radius:10px;border:1px solid #dee2e6;box-shadow:0 2px 4px #0000000d;margin-bottom:1rem}.page-description-card[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-weight:600;color:#007bff}.page-description-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem;line-height:1.5;margin-bottom:.75rem}.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background-color:#fff;color:#6c757d;border:1px solid #dee2e6;font-size:.8rem;padding:.4rem .8rem;font-weight:500}.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#007bff}.template-creation-controls[_ngcontent-%COMP%]{background:#f8f9fa;padding:1rem;border-radius:8px;margin-bottom:1rem;border:1px solid #e9ecef}.template-creation-controls[_ngcontent-%COMP%]   .template-action-buttons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.template-creation-controls[_ngcontent-%COMP%]   .template-status-info[_ngcontent-%COMP%]{margin-left:1rem}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff,#0056b3);border:none;font-weight:500;transition:all .2s ease}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 4px 12px #007bff4d}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:disabled{background:#6c757d;cursor:not-allowed}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background:#fff3;color:#fff;font-size:.75rem}.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]{border-color:#6c757d;color:#6c757d;transition:all .2s ease}.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#6c757d;border-color:#6c757d;color:#fff}.template-creation-controls[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{font-size:.875rem;font-style:italic}.template-creation-controls[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]{transform:scale(1.1);margin:0}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:all .2s ease}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-active[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_selectRow .3s ease-out}@keyframes _ngcontent-%COMP%_selectRow{0%{background-color:transparent;transform:scale(1)}50%{transform:scale(1.01)}to{background-color:#e3f2fd;transform:scale(1)}}@media (max-width: 768px){.template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}.template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:row;gap:.25rem}.template-creation-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.875rem;padding:.375rem .75rem}}\"]\n      });\n    }\n  }\n  return RequirementManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}