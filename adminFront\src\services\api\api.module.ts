/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { NgModule, ModuleWithProviders, SkipSelf, Optional } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiConfiguration, ApiConfigurationParams } from './api-configuration';

import { BaseFunctionService } from './services/base-function.service';
import { BuildCaseService } from './services/build-case.service';
import { BuildCaseFileService } from './services/build-case-file.service';
import { BuildCaseMailService } from './services/build-case-mail.service';
import { FileService } from './services/file.service';
import { FinalDocumentService } from './services/final-document.service';
import { FormItemService } from './services/form-item.service';
import { HouseService } from './services/house.service';
import { HouseHoldMainService } from './services/house-hold-main.service';
import { MaterialService } from './services/material.service';
import { PictureService } from './services/picture.service';
import { PreOrderSettingService } from './services/pre-order-setting.service';
import { QuotationService } from './services/quotation.service';
import { RegularChangeItemService } from './services/regular-change-item.service';
import { RegularNoticeFileService } from './services/regular-notice-file.service';
import { RequirementService } from './services/requirement.service';
import { ReviewService } from './services/review.service';
import { SpaceService } from './services/space.service';
import { SpecialChangeService } from './services/special-change.service';
import { SpecialNoticeFileService } from './services/special-notice-file.service';
import { TemplateService } from './services/template.service';
import { UserService } from './services/user.service';
import { UserGroupService } from './services/user-group.service';

/**
 * Module that provides all services and configuration.
 */
@NgModule({
  imports: [],
  exports: [],
  declarations: [],
  providers: [
    BaseFunctionService,
    BuildCaseService,
    BuildCaseFileService,
    BuildCaseMailService,
    FileService,
    FinalDocumentService,
    FormItemService,
    HouseService,
    HouseHoldMainService,
    MaterialService,
    PictureService,
    PreOrderSettingService,
    QuotationService,
    RegularChangeItemService,
    RegularNoticeFileService,
    RequirementService,
    ReviewService,
    SpaceService,
    SpecialChangeService,
    SpecialNoticeFileService,
    TemplateService,
    UserService,
    UserGroupService,
    ApiConfiguration
  ],
})
export class ApiModule {
  static forRoot(params: ApiConfigurationParams): ModuleWithProviders<ApiModule> {
    return {
      ngModule: ApiModule,
      providers: [
        {
          provide: ApiConfiguration,
          useValue: params
        }
      ]
    }
  }

  constructor( 
    @Optional() @SkipSelf() parentModule: ApiModule,
    @Optional() http: HttpClient
  ) {
    if (parentModule) {
      throw new Error('ApiModule is already loaded. Import in your base AppModule only.');
    }
    if (!http) {
      throw new Error('You need to import the HttpClientModule in your AppModule! \n' +
      'See also https://github.com/angular/angular/issues/20575');
    }
  }
}
