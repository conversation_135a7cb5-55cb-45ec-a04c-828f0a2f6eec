.requirement-template-dialog {
  width: 90vw;
  max-width: 1200px;
  max-height: 90vh;
  margin: 0 auto;

  .requirement-template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e4e9f2;
    background-color: #f7f9fc;

    .requirement-template-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;

      nb-icon {
        margin-right: 0.5rem;
        color: #3366ff;
      }
    }

    .close-btn {
      padding: 0.25rem;
      min-width: auto;
      
      nb-icon {
        font-size: 1.25rem;
      }
    }
  }

  .requirement-template-body {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;

    .section-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #3366ff;

      nb-icon {
        color: #3366ff;
      }

      .selected-count {
        margin-left: 0.5rem;
        font-size: 0.9rem;
        color: #27ae60;
        font-weight: 500;
      }
    }

    // 搜尋區域樣式
    .search-section {
      margin-bottom: 2rem;

      .search-form {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .search-label {
          display: block;
          font-weight: 500;
          color: #495057;
          margin-bottom: 0.5rem;
          font-size: 0.9rem;
        }

        .row {
          margin-left: -0.5rem;
          margin-right: -0.5rem;

          > div {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
          }
        }

        .search-actions {
          text-align: right;
          padding-top: 1rem;
          border-top: 1px solid #dee2e6;

          button {
            min-width: 100px;
          }
        }
      }
    }

    // 選擇區域樣式
    .selection-section {
      .select-all-control {
        margin-bottom: 1rem;
        padding: 0.75rem;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
      }

      .loading-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;

        .spinning {
          animation: spin 1s linear infinite;
          font-size: 2rem;
          margin-bottom: 1rem;
        }
      }

      .requirement-list {
        .requirement-item {
          display: flex;
          align-items: flex-start;
          padding: 1rem;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          margin-bottom: 0.75rem;
          background-color: #ffffff;
          transition: all 0.2s ease;
          cursor: pointer;

          &:hover {
            border-color: #3366ff;
            box-shadow: 0 2px 8px rgba(51, 102, 255, 0.1);
          }

          &.selected {
            border-color: #27ae60;
            background-color: #f8fff9;
            box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
          }

          .requirement-checkbox {
            margin-right: 1rem;
            margin-top: 0.25rem;
          }

          .requirement-info {
            flex: 1;

            .requirement-main {
              margin-bottom: 0.75rem;

              .requirement-name {
                font-size: 1.1rem;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 0.25rem;
              }

              .requirement-location {
                font-size: 0.9rem;
                color: #6c757d;
              }
            }

            .requirement-details {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 0.5rem;

              .detail-item {
                display: flex;
                align-items: center;
                font-size: 0.85rem;

                .detail-label {
                  font-weight: 500;
                  color: #495057;
                  margin-right: 0.5rem;
                  min-width: 60px;
                }

                .detail-value {
                  color: #6c757d;
                }
              }
            }
          }
        }
      }

      .no-data-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;

        .no-data-icon {
          font-size: 3rem;
          margin-bottom: 1rem;
          opacity: 0.5;
        }

        .no-data-text {
          font-size: 1.1rem;
        }
      }

      .pagination-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1.5rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;

        .pagination-info {
          font-size: 0.9rem;
          color: #6c757d;
        }

        .pagination-controls {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          .page-info {
            margin: 0 0.5rem;
            font-size: 0.9rem;
            color: #495057;
            min-width: 60px;
            text-align: center;
          }
        }
      }
    }

    // 選擇摘要樣式
    .selection-summary {
      margin-top: 2rem;
      padding: 1rem;
      background-color: #e8f5e8;
      border: 1px solid #27ae60;
      border-radius: 8px;

      .summary-title {
        font-weight: 600;
        color: #27ae60;
        margin-bottom: 0.75rem;
        font-size: 1rem;
      }

      .summary-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .summary-item {
          display: flex;
          align-items: center;

          .summary-label {
            font-weight: 500;
            color: #495057;
            margin-right: 0.5rem;
          }

          .summary-value {
            color: #2c3e50;
            font-weight: 600;

            &.total-price {
              color: #27ae60;
              font-size: 1.1rem;
            }
          }
        }
      }
    }
  }

  .requirement-template-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e4e9f2;
    background-color: #f7f9fc;

    .footer-actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      button {
        min-width: 120px;
      }
    }
  }
}

// 動畫效果
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 響應式設計
@media (max-width: 768px) {
  .requirement-template-dialog {
    width: 95vw;
    max-height: 95vh;

    .requirement-template-body {
      padding: 1rem;

      .search-section .search-form {
        padding: 1rem;

        .requirement-details {
          grid-template-columns: 1fr;
        }
      }

      .selection-section .requirement-list .requirement-item {
        flex-direction: column;
        align-items: flex-start;

        .requirement-checkbox {
          margin-bottom: 0.5rem;
        }

        .requirement-info .requirement-details {
          grid-template-columns: 1fr;
        }
      }

      .selection-summary .summary-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }
    }

    .requirement-template-footer .footer-actions {
      flex-direction: column;
      gap: 0.5rem;

      button {
        width: 100%;
      }
    }
  }
}
