{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../shared/components/household-binding/household-binding.component\";\nfunction DetailContentManagementSalesAccountComponent_div_37_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_37_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.scrollToFirstIncompleteItem());\n    });\n    i0.ɵɵtext(1, \" \\u8DF3\\u81F3\\u672A\\u5B8C\\u6210\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 8);\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_div_37_button_2_Template, 2, 0, \"button\", 48);\n    i0.ɵɵelementStart(3, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_37_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.scrollToTop());\n    });\n    i0.ɵɵtext(4, \" \\u56DE\\u5230\\u9802\\u90E8 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 50)(6, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_37_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.expandAll());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 52);\n    i0.ɵɵelement(8, \"path\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_37_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.collapseAll());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(10, \"svg\", 52);\n    i0.ɵɵelement(11, \"path\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(12, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_37_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.expandIncompleteOnly());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(13, \"svg\", 52);\n    i0.ɵɵelement(14, \"path\", 57);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(15, \"div\", 21);\n    i0.ɵɵtext(16, \" \\u4F7F\\u7528\\u6D6E\\u52D5\\u6309\\u9215\\u5FEB\\u901F\\u5132\\u5B58\\uFF0C\\u6216\\u6EFE\\u52D5\\u81F3\\u5E95\\u90E8\\u4F7F\\u7528\\u5B8C\\u6574\\u64CD\\u4F5C\\u9078\\u9805 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getCompletedItemsCount() < ctx_r2.arrListFormItemReq.length);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 76);\n    i0.ɵɵelement(2, \"path\", 77);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 76);\n    i0.ɵɵelement(2, \"path\", 57);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_38_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const idx_r7 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.scrollToItem(idx_r7 - 1));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 80);\n    i0.ɵɵelement(2, \"path\", 81);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_38_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const idx_r7 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.scrollToItem(idx_r7 + 1));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 80);\n    i0.ɵɵelement(2, \"path\", 83);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 137);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r5 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r2.getCurrentImage(formItemReq_r5)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_button_8_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const formItemReq_r5 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.prevImage(formItemReq_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 33);\n    i0.ɵɵelement(2, \"path\", 139);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 140);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_button_9_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const formItemReq_r5 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.nextImage(formItemReq_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 33);\n    i0.ɵɵelement(2, \"path\", 141);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r5 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", (formItemReq_r5.currentImageIndex || 0) + 1, \" / \", formItemReq_r5.CMatrialUrl.length, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_div_11_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_div_11_button_1_Template_button_click_0_listener() {\n      const i_r14 = i0.ɵɵrestoreView(_r13).index;\n      const formItemReq_r5 = i0.ɵɵnextContext(4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openImageModal(formItemReq_r5, i_r14));\n    });\n    i0.ɵɵelement(1, \"img\", 146);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r15 = ctx.$implicit;\n    const i_r14 = ctx.index;\n    const formItemReq_r5 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵclassProp(\"border-blue-500\", i_r14 === (formItemReq_r5.currentImageIndex || 0))(\"border-gray-300\", i_r14 !== (formItemReq_r5.currentImageIndex || 0))(\"ring-2\", i_r14 === (formItemReq_r5.currentImageIndex || 0))(\"ring-blue-200\", i_r14 === (formItemReq_r5.currentImageIndex || 0));\n    i0.ɵɵproperty(\"title\", \"\\u9EDE\\u9078\\u653E\\u5927\\u7B2C \" + (i_r14 + 1) + \" \\u5F35\\u5716\\u7247\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 10, imageUrl_r15), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 143);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_div_11_button_1_Template, 3, 12, \"button\", 144);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r5 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r5.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"div\", 126);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const formItemReq_r5 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openImageModal(formItemReq_r5));\n    });\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_img_2_Template, 2, 3, \"img\", 127);\n    i0.ɵɵelementStart(3, \"div\", 128)(4, \"div\", 129)(5, \"div\", 130);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 131);\n    i0.ɵɵelement(7, \"path\", 132);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_button_8_Template, 3, 0, \"button\", 133)(9, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_button_9_Template, 3, 0, \"button\", 134)(10, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_div_10_Template, 2, 2, \"div\", 135);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_div_11_Template, 2, 1, \"div\", 136);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getCurrentImage(formItemReq_r5));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r5.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r5.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r5.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r5.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 148);\n    i0.ɵɵelement(2, \"path\", 149);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 150);\n    i0.ɵɵtext(4, \"\\u7121\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_nb_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 151);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r16);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r16.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_50_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 154)(1, \"div\", 155);\n    i0.ɵɵelement(2, \"img\", 156)(3, \"div\", 157);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 158);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_50_div_1_Template_input_blur_4_listener($event) {\n      const i_r19 = i0.ɵɵrestoreView(_r18).index;\n      const formItemReq_r5 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.renameFile($event, i_r19, formItemReq_r5));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 159);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_50_div_1_Template_button_click_5_listener() {\n      const picture_r20 = i0.ɵɵrestoreView(_r18).$implicit;\n      const formItemReq_r5 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeImage(picture_r20.id, formItemReq_r5));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 160);\n    i0.ɵɵelement(7, \"path\", 161);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" \\u522A\\u9664\\u5716\\u7247 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_12_0;\n    const picture_r20 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r20.data, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r20.name)(\"disabled\", (tmp_11_0 = ctx_r2.listFormItem == null ? null : ctx_r2.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", (tmp_12_0 = ctx_r2.listFormItem == null ? null : ctx_r2.listFormItem.CIsLock) !== null && tmp_12_0 !== undefined ? tmp_12_0 : false);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 152);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_50_div_1_Template, 9, 4, \"div\", 153);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r5.listPictures);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 162)(1, \"label\", 163);\n    i0.ɵɵtext(2, \"\\u9810\\u8A2D\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 155);\n    i0.ɵɵelement(4, \"img\", 164);\n    i0.ɵɵpipe(5, \"base64Image\");\n    i0.ɵɵelement(6, \"div\", 157);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(5, 1, formItemReq_r5.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 165);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 166);\n    i0.ɵɵelement(2, \"path\", 167);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 168);\n    i0.ɵɵtext(4, \"\\u7121\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 169);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 170);\n    i0.ɵɵelement(2, \"path\", 171);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 172);\n    i0.ɵɵtext(4, \"\\u5C1A\\u7121\\u6236\\u5225\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_label_1_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 183);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const remark_r22 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r5 = i0.ɵɵnextContext(4).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r5.selectedRemarkType[remark_r22], $event) || (formItemReq_r5.selectedRemarkType[remark_r22] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const remark_r22 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r5 = i0.ɵɵnextContext(4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxRemarkChange($event, remark_r22, formItemReq_r5));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r22 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r5 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r5.selectedRemarkType[remark_r22]);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem == null ? null : ctx_r2.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 180);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_label_1_nb_checkbox_1_Template, 1, 2, \"nb-checkbox\", 181);\n    i0.ɵɵelementStart(2, \"span\", 182);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const remark_r22 = ctx.$implicit;\n    const formItemReq_r5 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r5.selectedRemarkType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(remark_r22);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 178);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_label_1_Template, 4, 2, \"label\", 179);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 169);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 170);\n    i0.ɵɵelement(2, \"path\", 184);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 172);\n    i0.ɵɵtext(4, \"\\u5C1A\\u7121\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 173)(1, \"div\", 95);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 174);\n    i0.ɵɵelement(3, \"path\", 175);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h5\", 176);\n    i0.ɵɵtext(5, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_Template, 2, 1, \"div\", 177)(7, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_ng_template_7_Template, 5, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noRemarkOptions_r23 = i0.ɵɵreference(8);\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.CRemarkTypeOptions && ctx_r2.CRemarkTypeOptions.length > 0)(\"ngIfElse\", noRemarkOptions_r23);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"div\", 85)(2, \"div\", 86)(3, \"div\", 87)(4, \"div\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 88);\n    i0.ɵɵelement(6, \"path\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"label\", 90);\n    i0.ɵɵtext(8, \"\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_Template, 12, 5, \"div\", 91)(10, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_10_Template, 5, 0, \"div\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 93)(12, \"div\", 94)(13, \"div\", 95);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 88);\n    i0.ɵɵelement(15, \"path\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"label\", 90);\n    i0.ɵɵtext(17, \"\\u57FA\\u672C\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 87)(19, \"div\", 96)(20, \"label\", 97);\n    i0.ɵɵtext(21, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 98)(23, \"span\", 99);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 100);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r5 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r5.CItemName, $event) || (formItemReq_r5.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 96)(27, \"label\", 97);\n    i0.ɵɵtext(28, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 101)(30, \"input\", 102);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r5 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r5.CRequireAnswer, $event) || (formItemReq_r5.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 96)(32, \"label\", 97);\n    i0.ɵɵtext(33, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"nb-select\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_nb_select_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r5 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r5.selectedCUiType, $event) || (formItemReq_r5.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_nb_select_selectedChange_34_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.changeSelectCUiType(formItemReq_r5));\n    });\n    i0.ɵɵtemplate(35, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_nb_option_35_Template, 2, 2, \"nb-option\", 104);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(36, \"div\", 86)(37, \"div\", 87)(38, \"div\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(39, \"svg\", 88);\n    i0.ɵɵelement(40, \"path\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"label\", 90);\n    i0.ɵɵtext(42, \"\\u6982\\u5FF5\\u8A2D\\u8A08\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"button\", 106);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const inputFile_r17 = i0.ɵɵreference(49);\n      return i0.ɵɵresetView(inputFile_r17.click());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(44, \"svg\", 107);\n    i0.ɵɵelement(45, \"path\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(46, \"span\");\n    i0.ɵɵtext(47, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"input\", 109, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_input_change_48_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.detectFiles($event, formItemReq_r5));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(50, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_50_Template, 2, 1, \"div\", 110)(51, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_51_Template, 7, 3, \"div\", 111)(52, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_52_Template, 5, 0, \"div\", 112);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 113)(54, \"div\", 101)(55, \"div\", 114);\n    i0.ɵɵelement(56, \"div\", 115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 116)(58, \"span\", 117);\n    i0.ɵɵtext(59, \"\\u8A2D\\u5B9A\\u9078\\u9805\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(60, \"div\", 118)(61, \"div\", 119)(62, \"div\", 95);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(63, \"svg\", 120);\n    i0.ɵɵelement(64, \"path\", 121);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(65, \"h5\", 122);\n    i0.ɵɵtext(66, \"\\u9069\\u7528\\u6236\\u578B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"app-household-binding\", 123);\n    i0.ɵɵlistener(\"selectionChange\", function DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_app_household_binding_selectionChange_67_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onHouseholdSelectionChange(ctx_r2.extractHouseholdCodes($event), formItemReq_r5));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(68, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_68_Template, 5, 0, \"div\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(69, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_Template, 9, 2, \"div\", 125);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_15_0;\n    let tmp_19_0;\n    let tmp_27_0;\n    const ctx_r23 = i0.ɵɵnextContext();\n    const formItemReq_r5 = ctx_r23.$implicit;\n    const idx_r7 = ctx_r23.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r5.CMatrialUrl && formItemReq_r5.CMatrialUrl.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r5.CMatrialUrl || formItemReq_r5.CMatrialUrl.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"for\", \"CItemName_\" + idx_r7);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r5.CName, \"-\", formItemReq_r5.CPart, \"-\", formItemReq_r5.CLocation, \": \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"CItemName_\" + idx_r7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r5.CItemName);\n    i0.ɵɵproperty(\"disabled\", (tmp_11_0 = ctx_r2.listFormItem == null ? null : ctx_r2.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"cRequireAnswer_\" + idx_r7);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"id\", \"cRequireAnswer_\" + idx_r7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r5.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r5.selectedCUiType.value === 3 || ((tmp_15_0 = ctx_r2.listFormItem == null ? null : ctx_r2.listFormItem.CIsLock) !== null && tmp_15_0 !== undefined ? tmp_15_0 : false));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"uiType_\" + idx_r7);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"uiType_\" + idx_r7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r5.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_19_0 = ctx_r2.listFormItem == null ? null : ctx_r2.listFormItem.CIsLock) !== null && tmp_19_0 !== undefined ? tmp_19_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CUiTypeOptions);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem == null ? null : ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r5.listPictures && formItemReq_r5.listPictures.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r5.CDesignFileUrl && (!formItemReq_r5.listPictures || formItemReq_r5.listPictures.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r5.CDesignFileUrl && (!formItemReq_r5.listPictures || formItemReq_r5.listPictures.length === 0));\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"buildingData\", ctx_r2.buildingData)(\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u9069\\u7528\\u6236\\u578B\")(\"disabled\", (tmp_27_0 = ctx_r2.listFormItem == null ? null : ctx_r2.listFormItem.CIsLock) !== null && tmp_27_0 !== undefined ? tmp_27_0 : false)(\"allowBatchSelect\", true)(\"ngModel\", formItemReq_r5.selectedHouseholdsCached)(\"useHouseNameMode\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.houseHoldList.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r5.selectedCUiType.value === 3);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 59)(2, \"div\", 60)(3, \"div\", 5)(4, \"div\", 13)(5, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_38_Template_button_click_5_listener() {\n      const formItemReq_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleItemCollapse(formItemReq_r5));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 62);\n    i0.ɵɵelement(7, \"path\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"div\", 64);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 65)(11, \"h4\", 66);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 67);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 13)(16, \"div\", 8);\n    i0.ɵɵtemplate(17, DetailContentManagementSalesAccountComponent_ng_container_38_div_17_Template, 3, 0, \"div\", 68)(18, DetailContentManagementSalesAccountComponent_ng_container_38_div_18_Template, 3, 0, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 70);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 71);\n    i0.ɵɵtemplate(22, DetailContentManagementSalesAccountComponent_ng_container_38_button_22_Template, 3, 0, \"button\", 72)(23, DetailContentManagementSalesAccountComponent_ng_container_38_button_23_Template, 3, 0, \"button\", 73);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(24, DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template, 70, 30, \"div\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r5 = ctx.$implicit;\n    const idx_r7 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"form-item-\" + idx_r7);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"title\", formItemReq_r5.isCollapsed ? \"\\u5C55\\u958B\\u9805\\u76EE\" : \"\\u6536\\u5408\\u9805\\u76EE\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"rotate-180\", formItemReq_r5.isCollapsed);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", idx_r7 + 1, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r5.CName, \"-\", formItemReq_r5.CPart, \"-\", formItemReq_r5.CLocation, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u9805\\u76EE\\u7DE8\\u865F #\", idx_r7 + 1, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemCompleted(formItemReq_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isItemCompleted(formItemReq_r5));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (formItemReq_r5.selectedCUiType == null ? null : formItemReq_r5.selectedCUiType.label) || \"\\u672A\\u8A2D\\u5B9A\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", idx_r7 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", idx_r7 < ctx_r2.arrListFormItemReq.length - 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r5.isCollapsed);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_58_div_1_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 199);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r26 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r2.getCurrentImage(formItemReq_r26)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_58_div_1_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 200);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_58_div_1_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const formItemReq_r26 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.prevImageModal(formItemReq_r26));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 201);\n    i0.ɵɵelement(2, \"path\", 139);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_58_div_1_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 202);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_58_div_1_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const formItemReq_r26 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.nextImageModal(formItemReq_r26));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 201);\n    i0.ɵɵelement(2, \"path\", 141);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_58_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 203)(1, \"div\", 13);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 107);\n    i0.ɵɵelement(3, \"path\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\", 204);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r26 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", (formItemReq_r26.currentImageIndex || 0) + 1, \" / \", formItemReq_r26.CMatrialUrl.length, \"\");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_58_div_1_div_16_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 208);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_58_div_1_div_16_button_2_Template_button_click_0_listener() {\n      const i_r30 = i0.ɵɵrestoreView(_r29).index;\n      const formItemReq_r26 = i0.ɵɵnextContext(3).$implicit;\n      return i0.ɵɵresetView(formItemReq_r26.currentImageIndex = i_r30);\n    });\n    i0.ɵɵelement(1, \"img\", 209);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r31 = ctx.$implicit;\n    const i_r30 = ctx.index;\n    const formItemReq_r26 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵclassProp(\"border-white\", i_r30 === (formItemReq_r26.currentImageIndex || 0))(\"border-gray-400\", i_r30 !== (formItemReq_r26.currentImageIndex || 0))(\"ring-3\", i_r30 === (formItemReq_r26.currentImageIndex || 0))(\"ring-white\", i_r30 === (formItemReq_r26.currentImageIndex || 0))(\"ring-opacity-50\", i_r30 === (formItemReq_r26.currentImageIndex || 0));\n    i0.ɵɵproperty(\"title\", \"\\u8DF3\\u81F3\\u7B2C \" + (i_r30 + 1) + \" \\u5F35\\u5716\\u7247\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 12, imageUrl_r31), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_58_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 205)(1, \"div\", 206);\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_58_div_1_div_16_button_2_Template, 3, 14, \"button\", 207);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r26 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r26.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_58_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 186);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_58_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const formItemReq_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeImageModal(formItemReq_r26));\n    })(\"keydown\", function DetailContentManagementSalesAccountComponent_ng_container_58_div_1_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const formItemReq_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onKeydown($event, formItemReq_r26));\n    });\n    i0.ɵɵelementStart(1, \"button\", 187);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_58_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const formItemReq_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeImageModal(formItemReq_r26));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 188);\n    i0.ɵɵelement(3, \"path\", 189);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\", 190);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_58_div_1_Template_div_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(5, \"div\", 191);\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_58_div_1_img_6_Template, 2, 3, \"img\", 192)(7, DetailContentManagementSalesAccountComponent_ng_container_58_div_1_button_7_Template, 3, 0, \"button\", 193)(8, DetailContentManagementSalesAccountComponent_ng_container_58_div_1_button_8_Template, 3, 0, \"button\", 194);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, DetailContentManagementSalesAccountComponent_ng_container_58_div_1_div_9_Template, 6, 2, \"div\", 195);\n    i0.ɵɵelementStart(10, \"div\", 196)(11, \"div\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 33);\n    i0.ɵɵelement(13, \"path\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(14, \"span\", 197);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(16, DetailContentManagementSalesAccountComponent_ng_container_58_div_1_div_16_Template, 3, 1, \"div\", 198);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getCurrentImage(formItemReq_r26));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r26.CMatrialUrl && formItemReq_r26.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r26.CMatrialUrl && formItemReq_r26.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r26.CMatrialUrl && formItemReq_r26.CMatrialUrl.length > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate3(\"\", formItemReq_r26.CName, \"-\", formItemReq_r26.CPart, \"-\", formItemReq_r26.CLocation, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r26.CMatrialUrl && formItemReq_r26.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_58_div_1_Template, 17, 8, \"div\", 185);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r26 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r26.isModalOpen);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_59__svg_svg_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 225);\n    i0.ɵɵelement(1, \"path\", 77);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_59__svg_svg_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 226);\n    i0.ɵɵelement(1, \"path\", 227);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_59_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 228);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_59_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.scrollToFirstIncompleteItem());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 107);\n    i0.ɵɵelement(2, \"path\", 57);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 210)(1, \"div\", 101)(2, \"button\", 211);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_59_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 212);\n    i0.ɵɵelement(4, \"path\", 213)(5, \"path\", 214);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_div_59__svg_svg_6_Template, 2, 0, \"svg\", 215)(7, DetailContentManagementSalesAccountComponent_div_59__svg_svg_7_Template, 2, 0, \"svg\", 216);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"div\", 217);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 218)(11, \"button\", 219);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_59_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.goBack());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 107);\n    i0.ɵɵelement(13, \"path\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(14, \"button\", 220);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_59_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.scrollToTop());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(15, \"svg\", 107);\n    i0.ɵɵelement(16, \"path\", 221);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, DetailContentManagementSalesAccountComponent_div_59_button_17_Template, 3, 0, \"button\", 222);\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(18, \"button\", 223);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_div_59_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.expandIncompleteOnly());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(19, \"svg\", 107);\n    i0.ɵɵelement(20, \"path\", 53);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(21, \"div\", 224);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"animate-pulse\", ctx_r2.isSubmitting);\n    i0.ɵɵpropertyInterpolate2(\"title\", \"\\u5FEB\\u901F\\u5132\\u5B58 (\", ctx_r2.getCompletedItemsCount(), \"/\", ctx_r2.arrListFormItemReq.length || 0, \" \\u5B8C\\u6210)\");\n    i0.ɵɵproperty(\"disabled\", ((tmp_3_0 = ctx_r2.listFormItem == null ? null : ctx_r2.listFormItem.CIsLock) !== null && tmp_3_0 !== undefined ? tmp_3_0 : false) || ctx_r2.isSubmitting);\n    i0.ɵɵadvance(3);\n    i0.ɵɵattribute(\"stroke-dasharray\", ctx_r2.getProgressPercentage() + \", 100\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSubmitting);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getProgressPercentage(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isSubmitting);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getCompletedItemsCount() < ctx_r2.arrListFormItemReq.length);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \", ctx_r2.getProgressPercentage(), \"% \\u5B8C\\u6210 (\", ctx_r2.getCompletedItemsCount(), \"/\", ctx_r2.arrListFormItemReq.length || 0, \") \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_button_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 229);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_button_66_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.copyToNewForm());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 40);\n    i0.ɵɵelement(2, \"path\", 230);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵtext(4, \" \\u8907\\u88FD\\u5230\\u65B0\\u8868\\u55AE \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isSubmitting);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent__svg_svg_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 231);\n    i0.ɵɵelement(1, \"path\", 227);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent__svg_svg_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 40);\n    i0.ɵɵelement(1, \"path\", 77);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService, _houseService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this._houseService = _houseService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    // 通知類型選項映射\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: EnumHouseType.地主戶\n    }, {\n      label: '銷售戶',\n      value: EnumHouseType.銷售戶\n    }];\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.isSubmitting = false;\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    // 新增：戶別選擇器相關屬性\n    this.buildingData = {}; // 存放建築物戶別資料\n    this.listFormItem = null;\n    this.isNew = true;\n    this.arrListFormItemReq = [];\n  }\n  // 動態獲取標題文字\n  get dynamicTitle() {\n    const option = this.cNoticeTypeOptions.find(option => option.value === this.typeContentManagementSalesAccount.CNoticeType);\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\n  }\n  // 設置通知類型（可供外部調用）\n  setCNoticeType(noticeType) {\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\n      // 同時設定 CFormType 以保持一致性\n      this.typeContentManagementSalesAccount.CFormType = noticeType;\n    }\n  }\n  ngOnInit() {\n    console.log('ngOnInit called');\n    this.route.paramMap.subscribe(params => {\n      console.log('Route params:', params);\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        console.log('buildCaseId set to:', this.buildCaseId);\n        if (this.buildCaseId > 0) {\n          console.log('Calling getListRegularNoticeFileHouseHold...');\n          this.getListRegularNoticeFileHouseHold();\n        } else {\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\n          console.error('Invalid buildCaseId:', this.buildCaseId);\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\n          this.goBack();\n        }\n      }\n    });\n    // 處理查詢參數中的戶型\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['houseType']) {\n        const houseType = +queryParams['houseType'];\n        this.setCNoticeType(houseType);\n      }\n    });\n  }\n  ngOnDestroy() {\n    // 確保在組件銷毀時恢復body的滾動\n    document.body.style.overflow = 'auto';\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      formItemReq.isModalOpen = true;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\n  extractHouseholdCodes(households) {\n    if (!households || !Array.isArray(households)) {\n      return [];\n    }\n    return households.map(h => h.code || h);\n  }\n  // 新增：處理戶別選擇變更\n  onHouseholdSelectionChange(selectedHouseholds, formItemReq) {\n    // 重置所有戶別選擇狀態\n    Object.keys(formItemReq.selectedItems).forEach(key => {\n      formItemReq.selectedItems[key] = false;\n    });\n    // 設置選中的戶別\n    selectedHouseholds.forEach(household => {\n      formItemReq.selectedItems[household] = true;\n    });\n    // 更新全選狀態\n    formItemReq.allSelected = this.houseHoldList.length > 0 && this.houseHoldList.every(item => formItemReq.selectedItems[item]);\n    // 更新緩存\n    this.updateSelectedHouseholdsCache(formItemReq);\n  }\n  // 新增：取得已選戶別數組\n  getSelectedHouseholds(formItemReq) {\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\n  }\n  // 新增：更新已選戶別緩存\n  updateSelectedHouseholdsCache(formItemReq) {\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\n  }\n  // 新增：更新所有項目的緩存\n  updateAllSelectedHouseholdsCache() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(formItemReq => {\n        this.updateSelectedHouseholdsCache(formItemReq);\n      });\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            currentImageIndex: 0,\n            isModalOpen: false,\n            isCollapsed: false,\n            // 新項目默認展開\n            CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64).filter(url => url != null) : []\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    console.log('getListFormItem called with:', {\n      CBuildCaseId: this.buildCaseId,\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\n      CIsPaging: false\n    });\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      console.log('getListFormItem response:', res);\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        console.log('isNew:', this.isNew);\n        console.log('formItems exists:', !!res.Entries.formItems);\n        console.log('houseHoldList:', this.houseHoldList);\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem?.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0,\n              isModalOpen: false,\n              isCollapsed: false,\n              // 現有項目默認展開\n              selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\n            };\n          });\n          console.log('arrListFormItemReq set to:', this.arrListFormItemReq);\n          console.log('arrListFormItemReq length:', this.arrListFormItemReq.length);\n        } else {\n          // 當無資料時，載入材料清單供新增使用\n          this.getMaterialList();\n        }\n        // 初始化所有項目的緩存\n        this.updateAllSelectedHouseholdsCache();\n      } else {\n        console.error('getListFormItem failed:', res);\n      }\n    })).subscribe({\n      error: error => {\n        console.error('getListFormItem error:', error);\n      }\n    });\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    // 設置提交狀態\n    this.isSubmitting = true;\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem?.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      // 滾動到第一個有錯誤的項目\n      this.scrollToFirstErrorItem();\n      this.isSubmitting = false;\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe({\n      next: res => {\n        this.isSubmitting = false;\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          // this.getListFormItem()\n          this.goBack();\n        }\n      },\n      error: error => {\n        this.isSubmitting = false;\n        console.error('Save error:', error);\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe({\n      next: res => {\n        this.isSubmitting = false;\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          // this.getListFormItem()\n          this.goBack();\n        }\n      },\n      error: error => {\n        this.isSubmitting = false;\n        console.error('Create error:', error);\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  /**\n   * 複製當前表單到新表單\n   */\n  copyToNewForm() {\n    // 先取得當前有效的材料清單\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        // 建立有效材料清單的鍵值對應\n        const validMaterialKeys = new Set();\n        res.Entries.forEach(material => {\n          const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\n          validMaterialKeys.add(key);\n        });\n        // 篩選出仍然有效的表單項目\n        const validFormItems = this.arrListFormItemReq.filter(item => {\n          const itemKey = `${item.CLocation}_${item.CName}_${item.CPart}`;\n          return validMaterialKeys.has(itemKey);\n        });\n        if (validFormItems.length === 0) {\n          this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\n          return;\n        }\n        // 準備複製的表單項目數據\n        this.saveListFormItemReq = validFormItems.map(e => {\n          return {\n            CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n            CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n            CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n            CFormItemId: null,\n            // 設為 null 以建立新項目\n            CFormID: null,\n            // 設為 null 以建立新表單\n            CName: e.CName,\n            CPart: e.CPart,\n            CLocation: e.CLocation,\n            CItemName: e.CItemName,\n            CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n            CTotalAnswer: e.CTotalAnswer,\n            CRequireAnswer: e.CRequireAnswer,\n            CUiType: e.selectedCUiType.value\n          };\n        });\n        // 執行驗證\n        this.validation();\n        if (this.valid.errorMessages.length > 0) {\n          this.message.showErrorMSGs(this.valid.errorMessages);\n          return;\n        }\n        // 建立複製的表單\n        this.creatListFormItem = {\n          CBuildCaseId: this.buildCaseId,\n          CFormItem: this.saveListFormItemReq || null,\n          CFormType: this.typeContentManagementSalesAccount.CFormType\n        };\n        this._formItemService.apiFormItemCreateListFormItemPost$Json({\n          body: this.creatListFormItem\n        }).subscribe(createRes => {\n          if (createRes.StatusCode == 0) {\n            this.message.showSucessMSG(`複製表單成功，已篩選 ${validFormItems.length} 個有效項目`);\n            // 重新載入資料以顯示新的未鎖定表單\n            this.getListFormItem();\n          }\n        });\n      } else {\n        this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\n      }\n    })).subscribe();\n  }\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\n  loadBuildingDataFromAPI() {\n    if (!this.buildCaseId) return;\n    this._houseService.apiHouseGetDropDownPost$Json({\n      buildCaseId: this.buildCaseId\n    }).subscribe({\n      next: response => {\n        console.log('GetDropDown API response:', response);\n        if (response.Entries) {\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n          console.log('Converted buildingData:', this.buildingData);\n        }\n      },\n      error: error => {\n        console.error('Error loading building data from API:', error);\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\n        }\n      }\n    });\n  }\n  // 新增：將 API 回應轉換為建築物資料格式\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        code: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseName: house.HouseName,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  // 新增：將戶別清單轉換為建築物資料格式\n  convertHouseHoldListToBuildingData(houseHoldList) {\n    if (!houseHoldList || houseHoldList.length === 0) {\n      return {};\n    }\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\n    const buildingData = {};\n    houseHoldList.forEach(household => {\n      // 嘗試從戶別名稱中提取建築物代碼\n      const buildingMatch = household.match(/^([A-Z]+)/);\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\n      if (!buildingData[building]) {\n        buildingData[building] = [];\n      }\n      // 計算樓層（假設每4戶為一層）\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\n      const floor = Math.ceil(houseNumber / 4);\n      buildingData[building].push({\n        code: household,\n        building: building,\n        floor: `${floor}F`,\n        isSelected: false,\n        isDisabled: false\n      });\n    });\n    return buildingData;\n  }\n  getListRegularNoticeFileHouseHold() {\n    console.log('getListRegularNoticeFileHouseHold called with buildCaseId:', this.buildCaseId);\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      console.log('getListRegularNoticeFileHouseHold response:', res);\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        // 載入建築物資料 (只呼叫一次 GetDropDown API)\n        this.loadBuildingDataFromAPI();\n        this.getListFormItem();\n      } else {\n        console.error('getListRegularNoticeFileHouseHold failed:', res);\n      }\n    })).subscribe({\n      error: error => {\n        console.error('getListRegularNoticeFileHouseHold error:', error);\n      }\n    });\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  // UI優化相關方法\n  /**\n   * 檢查項目是否已完成\n   */\n  isItemCompleted(formItemReq) {\n    // 檢查必填欄位是否都已填寫\n    const hasItemName = !!formItemReq.CItemName && formItemReq.CItemName.trim() !== '';\n    const hasUiType = !!formItemReq.selectedCUiType && formItemReq.selectedCUiType.value;\n    const hasRequireAnswer = !!formItemReq.CRequireAnswer && formItemReq.CRequireAnswer > 0;\n    // 如果是建材選樣類型，檢查是否有選擇備註類型\n    let hasRemarkType = true;\n    if (formItemReq.selectedCUiType?.value === 3) {\n      hasRemarkType = !!formItemReq.selectedRemarkType && Object.values(formItemReq.selectedRemarkType).some(selected => selected);\n    }\n    return hasItemName && hasUiType && hasRequireAnswer && hasRemarkType;\n  }\n  /**\n   * 獲取已完成項目數量\n   */\n  getCompletedItemsCount() {\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\n    return this.arrListFormItemReq.filter(item => this.isItemCompleted(item)).length;\n  }\n  /**\n   * 獲取進度百分比\n   */\n  getProgressPercentage() {\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\n    const completed = this.getCompletedItemsCount();\n    return Math.round(completed / this.arrListFormItemReq.length * 100);\n  }\n  /**\n   * 滾動到指定項目\n   */\n  scrollToItem(index) {\n    const element = document.getElementById(`form-item-${index}`);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start',\n        inline: 'nearest'\n      });\n      // 添加高亮效果\n      element.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-75');\n      setTimeout(() => {\n        element.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-75');\n      }, 2000);\n    }\n  }\n  /**\n   * 滾動到第一個未完成的項目\n   */\n  scrollToFirstIncompleteItem() {\n    if (!this.arrListFormItemReq) return;\n    const firstIncompleteIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\n    if (firstIncompleteIndex !== -1) {\n      this.scrollToItem(firstIncompleteIndex);\n    }\n  }\n  /**\n   * 滾動到第一個有錯誤的項目\n   */\n  scrollToFirstErrorItem() {\n    if (!this.arrListFormItemReq) return;\n    // 找到第一個有錯誤的項目\n    const firstErrorIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\n    if (firstErrorIndex !== -1) {\n      this.scrollToItem(firstErrorIndex);\n    }\n  }\n  /**\n   * 滾動到頂部\n   */\n  scrollToTop() {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  }\n  /**\n   * 切換項目收合狀態\n   */\n  toggleItemCollapse(formItemReq) {\n    formItemReq.isCollapsed = !formItemReq.isCollapsed;\n  }\n  /**\n   * 全部展開\n   */\n  expandAll() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = false;\n      });\n    }\n  }\n  /**\n   * 全部收合\n   */\n  collapseAll() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = true;\n      });\n    }\n  }\n  /**\n   * 只展開未完成的項目\n   */\n  expandIncompleteOnly() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = this.isItemCompleted(item);\n      });\n    }\n  }\n  static {\n    this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService), i0.ɵɵdirectiveInject(i4.HouseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-detail-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 72,\n      vars: 22,\n      consts: [[\"inputFile\", \"\"], [\"noRemarkOptions\", \"\"], [1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"to-gray-100\"], [1, \"shadow-xl\", \"border-0\", \"rounded-xl\"], [1, \"bg-white\", \"border-b\", \"border-gray-200\", \"p-6\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"w-1\", \"h-8\", \"bg-green-500\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"bg-green-100\", \"text-green-800\", \"rounded-full\", \"font-medium\"], [1, \"p-6\", \"bg-gray-50\"], [1, \"space-y-8\"], [1, \"bg-white\", \"rounded-xl\", \"p-6\", \"shadow-sm\", \"border\", \"border-gray-200\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-10\", \"h-10\", \"bg-blue-100\", \"rounded-lg\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-blue-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-bold\", \"text-gray-800\"], [1, \"text-sm\", \"text-gray-600\", \"mt-1\"], [1, \"text-right\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"relative\", \"w-16\", \"h-16\"], [\"viewBox\", \"0 0 36 36\", 1, \"w-16\", \"h-16\", \"transform\", \"-rotate-90\"], [\"stroke\", \"currentColor\", \"stroke-width\", \"3\", \"fill\", \"none\", \"d\", \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\", 1, \"text-gray-200\"], [\"stroke\", \"currentColor\", \"stroke-width\", \"3\", \"fill\", \"none\", \"d\", \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\", 1, \"text-blue-500\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\"], [1, \"text-xs\", \"font-bold\", \"text-gray-700\"], [\"class\", \"mt-4 flex items-center justify-between\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"bg-white/95\", \"backdrop-blur-sm\", \"border-t\", \"border-gray-200\", \"p-4\", \"sticky\", \"bottom-0\", \"shadow-lg\", \"z-30\"], [1, \"flex\", \"items-center\", \"justify-center\"], [1, \"flex\", \"items-center\", \"space-x-6\", \"text-sm\", \"text-gray-600\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"w-3\", \"h-3\", \"bg-green-500\", \"rounded-full\"], [1, \"w-3\", \"h-3\", \"bg-orange-400\", \"rounded-full\"], [\"class\", \"fixed bottom-6 right-6 z-50 flex flex-col items-end space-y-3\", 4, \"ngIf\"], [1, \"fixed\", \"right-6\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"z-50\", \"flex\", \"flex-col\", \"space-y-3\"], [\"title\", \"\\u53D6\\u6D88\\u4E26\\u8FD4\\u56DE\", 1, \"w-14\", \"h-14\", \"bg-gray-500\", \"hover:bg-gray-600\", \"text-white\", \"rounded-full\", \"shadow-xl\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", \"group\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [1, \"absolute\", \"right-16\", \"bg-gray-800\", \"text-white\", \"text-sm\", \"px-3\", \"py-2\", \"rounded-lg\", \"shadow-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"whitespace-nowrap\"], [\"class\", \"w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center group\", \"title\", \"\\u8907\\u88FD\\u5230\\u65B0\\u8868\\u55AE\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"title\", \"\\u5132\\u5B58\\u8B8A\\u66F4\", 1, \"w-14\", \"h-14\", \"bg-gradient-to-r\", \"from-green-500\", \"to-green-600\", \"hover:from-green-600\", \"hover:to-green-700\", \"text-white\", \"rounded-full\", \"shadow-xl\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", \"group\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"relative\", 3, \"click\", \"disabled\"], [\"class\", \"w-6 h-6 animate-spin\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"class\", \"w-6 h-6\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"mt-4\", \"flex\", \"items-center\", \"justify-between\"], [\"class\", \"px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-full transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"px-3\", \"py-1\", \"text-xs\", \"bg-green-100\", \"hover:bg-green-200\", \"text-green-700\", \"rounded-full\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-center\", \"space-x-1\", \"border-l\", \"border-gray-300\", \"pl-2\", \"ml-2\"], [\"title\", \"\\u5168\\u90E8\\u5C55\\u958B\", 1, \"px-2\", \"py-1\", \"text-xs\", \"bg-purple-100\", \"hover:bg-purple-200\", \"text-purple-700\", \"rounded\", \"transition-colors\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"], [\"title\", \"\\u5168\\u90E8\\u6536\\u5408\", 1, \"px-2\", \"py-1\", \"text-xs\", \"bg-purple-100\", \"hover:bg-purple-200\", \"text-purple-700\", \"rounded\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 9l6 6m0 0l6-6m-6 6L9 3\"], [\"title\", \"\\u53EA\\u5C55\\u958B\\u672A\\u5B8C\\u6210\", 1, \"px-2\", \"py-1\", \"text-xs\", \"bg-orange-100\", \"hover:bg-orange-200\", \"text-orange-700\", \"rounded\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"], [1, \"px-3\", \"py-1\", \"text-xs\", \"bg-blue-100\", \"hover:bg-blue-200\", \"text-blue-700\", \"rounded-full\", \"transition-colors\", 3, \"click\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-lg\", \"border\", \"border-gray-200\", \"overflow-hidden\", \"transition-all\", \"duration-300\", \"hover:shadow-xl\", 3, \"id\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-indigo-50\", \"px-6\", \"py-4\", \"border-b\", \"border-gray-200\"], [1, \"w-8\", \"h-8\", \"bg-gray-100\", \"hover:bg-gray-200\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"click\", \"title\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-gray-600\", \"transition-transform\", \"duration-200\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 9l-7 7-7-7\"], [1, \"w-8\", \"h-8\", \"bg-blue-500\", \"text-white\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-sm\", \"font-bold\"], [1, \"flex-1\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [1, \"text-sm\", \"text-gray-600\"], [\"class\", \"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\", \"title\", \"\\u9805\\u76EE\\u5DF2\\u5B8C\\u6210\", 4, \"ngIf\"], [\"class\", \"w-6 h-6 bg-orange-400 rounded-full flex items-center justify-center\", \"title\", \"\\u9805\\u76EE\\u672A\\u5B8C\\u6210\", 4, \"ngIf\"], [1, \"px-2\", \"py-1\", \"text-xs\", \"bg-blue-100\", \"text-blue-800\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"class\", \"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\", \"title\", \"\\u4E0A\\u4E00\\u500B\\u9805\\u76EE\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\", \"title\", \"\\u4E0B\\u4E00\\u500B\\u9805\\u76EE\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-6\", 4, \"ngIf\"], [\"title\", \"\\u9805\\u76EE\\u5DF2\\u5B8C\\u6210\", 1, \"w-6\", \"h-6\", \"bg-green-500\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 13l4 4L19 7\"], [\"title\", \"\\u9805\\u76EE\\u672A\\u5B8C\\u6210\", 1, \"w-6\", \"h-6\", \"bg-orange-400\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"title\", \"\\u4E0A\\u4E00\\u500B\\u9805\\u76EE\", 1, \"w-7\", \"h-7\", \"bg-gray-100\", \"hover:bg-gray-200\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 19l-7-7 7-7\"], [\"title\", \"\\u4E0B\\u4E00\\u500B\\u9805\\u76EE\", 1, \"w-7\", \"h-7\", \"bg-gray-100\", \"hover:bg-gray-200\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"p-6\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-4\", \"gap-6\"], [1, \"lg:col-span-1\"], [1, \"space-y-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-700\"], [\"class\", \"relative\", 4, \"ngIf\"], [\"class\", \"aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"lg:col-span-2\"], [1, \"space-y-6\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mb-4\"], [1, \"group\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\", 3, \"for\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"bg-gray-50\", \"p-3\", \"rounded-lg\", \"border\", \"border-gray-200\"], [1, \"text-sm\", \"text-gray-600\", \"font-medium\", \"px-2\", \"py-1\", \"bg-blue-100\", \"rounded-md\", \"whitespace-nowrap\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u4F8B\\u5982\\uFF1A\\u5EDA\\u623F\\u6AAF\\u9762\", 1, \"flex-1\", \"border-0\", \"bg-transparent\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-transparent\", \"rounded-md\", \"p-2\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"relative\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u8F38\\u5165\\u6578\\u91CF\", 1, \"w-full\", \"border-2\", \"border-gray-200\", \"focus:border-blue-500\", \"focus:ring-2\", \"focus:ring-blue-200\", \"rounded-lg\", \"p-3\", \"transition-all\", \"duration-200\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [\"placeholder\", \"\\u9078\\u64C7UI\\u985E\\u578B\", 1, \"w-full\", \"border-2\", \"border-gray-200\", \"focus:border-blue-500\", \"rounded-lg\", \"transition-all\", \"duration-200\", 3, \"ngModelChange\", \"selectedChange\", \"id\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-blue-500\", \"to-blue-600\", \"hover:from-blue-600\", \"hover:to-blue-700\", \"text-white\", \"font-medium\", \"py-3\", \"px-4\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"shadow-md\", \"hover:shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [\"class\", \"space-y-3\", 4, \"ngIf\"], [\"class\", \"space-y-2\", 4, \"ngIf\"], [\"class\", \"h-32 w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"my-8\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\"], [1, \"w-full\", \"border-t\", \"border-gray-300\"], [1, \"relative\", \"flex\", \"justify-center\", \"text-sm\"], [1, \"px-4\", \"bg-white\", \"text-gray-500\", \"font-medium\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-2\", \"gap-6\"], [1, \"bg-blue-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-blue-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"font-semibold\", \"text-blue-800\"], [1, \"w-full\", 3, \"selectionChange\", \"buildingData\", \"placeholder\", \"disabled\", \"allowBatchSelect\", \"ngModel\", \"useHouseNameMode\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"bg-orange-50 p-4 rounded-lg border border-orange-200\", 4, \"ngIf\"], [1, \"aspect-square\", \"w-full\", \"relative\", \"overflow-hidden\", \"rounded-xl\", \"border-2\", \"border-gray-200\", \"cursor-pointer\", \"group\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"duration-300\", 3, \"click\"], [\"class\", \"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\", 3, \"src\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-30\", \"transition-all\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [1, \"transform\", \"scale-75\", \"group-hover:scale-100\", \"transition-transform\", \"duration-300\"], [1, \"w-12\", \"h-12\", \"bg-white\", \"bg-opacity-90\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-gray-800\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"], [\"class\", \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"flex gap-2 mt-3 overflow-x-auto pb-2\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-500\", \"group-hover:scale-110\", 3, \"src\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 1, \"absolute\", \"left-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M15 19l-7-7 7-7\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 1, \"absolute\", \"right-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M9 5l7 7-7 7\"], [1, \"absolute\", \"bottom-2\", \"right-2\", \"bg-black\", \"bg-opacity-80\", \"text-white\", \"text-xs\", \"px-2\", \"py-1\", \"rounded-lg\", \"backdrop-blur-sm\"], [1, \"flex\", \"gap-2\", \"mt-3\", \"overflow-x-auto\", \"pb-2\"], [\"class\", \"flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\", 3, \"border-blue-500\", \"border-gray-300\", \"ring-2\", \"ring-blue-200\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-12\", \"h-12\", \"border-2\", \"rounded-lg\", \"overflow-hidden\", \"hover:border-blue-400\", \"transition-all\", \"duration-200\", \"cursor-pointer\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"hover:scale-110\", 3, \"src\"], [1, \"aspect-square\", \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-xl\", \"bg-gray-50\", \"text-gray-400\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-12\", \"h-12\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\"], [3, \"value\"], [1, \"space-y-3\"], [\"class\", \"bg-gray-50 border border-gray-200 p-3 rounded-lg hover:shadow-md transition-all duration-200\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-gray-50\", \"border\", \"border-gray-200\", \"p-3\", \"rounded-lg\", \"hover:shadow-md\", \"transition-all\", \"duration-200\"], [1, \"relative\", \"group\"], [1, \"w-full\", \"h-32\", \"object-cover\", \"rounded-lg\", \"mb-3\", \"border\", \"border-gray-200\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-20\", \"transition-all\", \"duration-200\", \"rounded-lg\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5716\\u7247\\u8AAA\\u660E/\\u6A94\\u540D\", 1, \"w-full\", \"p-2\", \"text-sm\", \"mb-2\", \"border\", \"border-gray-200\", \"rounded-md\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-transparent\", 3, \"blur\", \"value\", \"disabled\"], [1, \"w-full\", \"bg-red-100\", \"hover:bg-red-200\", \"text-red-700\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-md\", \"transition-colors\", \"duration-200\", \"text-sm\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"space-y-2\"], [1, \"block\", \"text-xs\", \"font-medium\", \"text-gray-600\"], [1, \"w-full\", \"h-32\", \"object-cover\", \"rounded-lg\", \"border\", \"border-gray-200\", 3, \"src\"], [1, \"h-32\", \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-lg\", \"bg-gray-50\", \"text-gray-400\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"mb-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"], [1, \"text-xs\"], [1, \"text-center\", \"py-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-gray-400\", \"mx-auto\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-gray-500\", \"text-sm\"], [1, \"bg-orange-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-orange-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-orange-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [1, \"font-semibold\", \"text-orange-800\"], [\"class\", \"grid grid-cols-1 gap-2\", 4, \"ngIf\", \"ngIfElse\"], [1, \"grid\", \"grid-cols-1\", \"gap-2\"], [\"class\", \"flex items-center cursor-pointer hover:bg-orange-100 p-2 rounded-md transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"cursor-pointer\", \"hover:bg-orange-100\", \"p-2\", \"rounded-md\", \"transition-colors\"], [\"value\", \"item\", \"class\", \"mr-3\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"], [1, \"text-gray-700\"], [\"value\", \"item\", 1, \"mr-3\", 3, \"checkedChange\", \"checked\", \"disabled\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [\"class\", \"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fade-in-up\", \"tabindex\", \"0\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"fixed\", \"inset-0\", \"z-[9999]\", \"flex\", \"items-center\", \"justify-center\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-sm\", \"p-4\", \"animate-fade-in-up\", 3, \"click\", \"keydown\"], [\"title\", \"\\u95DC\\u9589\\u5716\\u7247\\u6AA2\\u8996 (\\u6309 ESC \\u9375)\", 1, \"modal-close-btn\", \"fixed\", \"top-6\", \"right-6\", \"z-[60]\", \"bg-red-500\", \"bg-opacity-95\", \"hover:bg-red-600\", \"hover:bg-opacity-100\", \"text-white\", \"rounded-full\", \"w-14\", \"h-14\", \"flex\", \"items-center\", \"justify-center\", \"shadow-2xl\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-7\", \"h-7\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"relative\", \"max-w-7xl\", \"max-h-full\", \"w-full\", \"h-full\", \"flex\", \"items-center\", \"justify-center\", \"animate-slide-in-left\", 3, \"click\"], [1, \"relative\", \"max-w-full\", \"max-h-full\", \"bg-white\", \"rounded-2xl\", \"p-2\", \"shadow-2xl\"], [\"class\", \"max-w-full max-h-[85vh] object-contain rounded-xl animate-fade-in-up\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"modal-nav-btn absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"modal-nav-btn absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-6\", \"right-6\", \"bg-gradient-to-r\", \"from-blue-600\", \"to-blue-700\", \"text-white\", \"px-4\", \"py-3\", \"rounded-lg\", \"text-sm\", \"backdrop-blur-sm\", \"shadow-lg\"], [1, \"font-medium\"], [\"class\", \"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\", 4, \"ngIf\"], [1, \"max-w-full\", \"max-h-[85vh]\", \"object-contain\", \"rounded-xl\", \"animate-fade-in-up\", 3, \"src\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 1, \"modal-nav-btn\", \"absolute\", \"left-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"z-[55]\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 1, \"modal-nav-btn\", \"absolute\", \"right-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"z-[55]\", 3, \"click\"], [1, \"absolute\", \"bottom-24\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-90\", \"text-white\", \"px-6\", \"py-3\", \"rounded-full\", \"backdrop-blur-sm\", \"shadow-lg\"], [1, \"font-medium\", \"text-lg\"], [1, \"absolute\", \"bottom-32\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-md\", \"p-4\", \"rounded-xl\", \"shadow-2xl\", \"max-w-full\"], [1, \"flex\", \"gap-3\", \"overflow-x-auto\", \"max-w-[80vw]\", \"modal-thumbnails\"], [\"class\", \"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105\", 3, \"border-white\", \"border-gray-400\", \"ring-3\", \"ring-white\", \"ring-opacity-50\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-20\", \"h-20\", \"border-3\", \"rounded-xl\", \"overflow-hidden\", \"hover:border-white\", \"transition-all\", \"duration-200\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-200\", 3, \"src\"], [1, \"fixed\", \"bottom-6\", \"right-6\", \"z-50\", \"flex\", \"flex-col\", \"items-end\", \"space-y-3\"], [1, \"w-16\", \"h-16\", \"bg-blue-500\", \"hover:bg-blue-600\", \"text-white\", \"rounded-full\", \"shadow-2xl\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", \"group\", 3, \"click\", \"disabled\", \"title\"], [\"viewBox\", \"0 0 36 36\", 1, \"absolute\", \"inset-0\", \"w-16\", \"h-16\", \"transform\", \"-rotate-90\"], [\"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"fill\", \"none\", \"d\", \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\", 1, \"text-blue-300\"], [\"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"fill\", \"none\", \"d\", \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\", 1, \"text-white\"], [\"class\", \"w-6 h-6 z-10\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"class\", \"w-6 h-6 z-10 animate-spin\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"absolute\", \"-top-2\", \"-right-2\", \"w-6\", \"h-6\", \"bg-green-500\", \"text-white\", \"text-xs\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"font-bold\"], [1, \"flex\", \"flex-col\", \"space-y-2\"], [\"title\", \"\\u53D6\\u6D88\\u4E26\\u8FD4\\u56DE\", 1, \"w-12\", \"h-12\", \"bg-gray-500\", \"hover:bg-gray-600\", \"text-white\", \"rounded-full\", \"shadow-lg\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\", \"disabled\"], [\"title\", \"\\u56DE\\u5230\\u9802\\u90E8\", 1, \"w-12\", \"h-12\", \"bg-indigo-500\", \"hover:bg-indigo-600\", \"text-white\", \"rounded-full\", \"shadow-lg\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 10l7-7m0 0l7 7m-7-7v18\"], [\"class\", \"w-12 h-12 bg-orange-500 hover:bg-orange-600 text-white rounded-full shadow-lg transition-all duration-300 hover:scale-110 flex items-center justify-center\", \"title\", \"\\u8DF3\\u81F3\\u672A\\u5B8C\\u6210\\u9805\\u76EE\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"\\u53EA\\u5C55\\u958B\\u672A\\u5B8C\\u6210\\u9805\\u76EE\", 1, \"w-12\", \"h-12\", \"bg-purple-500\", \"hover:bg-purple-600\", \"text-white\", \"rounded-full\", \"shadow-lg\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [1, \"absolute\", \"right-20\", \"top-0\", \"bg-gray-800\", \"text-white\", \"text-sm\", \"px-3\", \"py-2\", \"rounded-lg\", \"shadow-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"duration-300\", \"whitespace-nowrap\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"z-10\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"z-10\", \"animate-spin\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"], [\"title\", \"\\u8DF3\\u81F3\\u672A\\u5B8C\\u6210\\u9805\\u76EE\", 1, \"w-12\", \"h-12\", \"bg-orange-500\", \"hover:bg-orange-600\", \"text-white\", \"rounded-full\", \"shadow-lg\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [\"title\", \"\\u8907\\u88FD\\u5230\\u65B0\\u8868\\u55AE\", 1, \"w-14\", \"h-14\", \"bg-blue-500\", \"hover:bg-blue-600\", \"text-white\", \"rounded-full\", \"shadow-xl\", \"transition-all\", \"duration-300\", \"hover:scale-110\", \"flex\", \"items-center\", \"justify-center\", \"group\", 3, \"click\", \"disabled\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"animate-spin\"]],\n      template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"nb-card\", 3)(2, \"nb-card-header\", 4)(3, \"div\", 5)(4, \"div\", 6);\n          i0.ɵɵelement(5, \"div\", 7);\n          i0.ɵɵelementStart(6, \"div\");\n          i0.ɵɵelement(7, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"span\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(11, \"nb-card-body\", 10)(12, \"div\", 11)(13, \"div\", 12)(14, \"div\", 5)(15, \"div\", 13)(16, \"div\", 14);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(17, \"svg\", 15);\n          i0.ɵɵelement(18, \"path\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(19, \"div\")(20, \"h3\", 17);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\", 18);\n          i0.ɵɵtext(23, \"\\u7BA1\\u7406\\u9078\\u6A23\\u9805\\u76EE\\u7684\\u8A73\\u7D30\\u8A2D\\u5B9A\\u8207\\u914D\\u7F6E\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 6)(25, \"div\", 19)(26, \"div\", 20);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 21);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 22);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(31, \"svg\", 23);\n          i0.ɵɵelement(32, \"path\", 24)(33, \"path\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(34, \"div\", 26)(35, \"span\", 27);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(37, DetailContentManagementSalesAccountComponent_div_37_Template, 17, 1, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(38, DetailContentManagementSalesAccountComponent_ng_container_38_Template, 25, 15, \"ng-container\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"nb-card-footer\", 30)(40, \"div\", 31)(41, \"div\", 32)(42, \"div\", 8);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(43, \"svg\", 33);\n          i0.ɵɵelement(44, \"path\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(45, \"span\");\n          i0.ɵɵtext(46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 6)(48, \"div\", 8);\n          i0.ɵɵelement(49, \"div\", 35);\n          i0.ɵɵelementStart(50, \"span\");\n          i0.ɵɵtext(51);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 8);\n          i0.ɵɵelement(53, \"div\", 36);\n          i0.ɵɵelementStart(54, \"span\");\n          i0.ɵɵtext(55);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(56, \"div\", 21);\n          i0.ɵɵtext(57, \" \\u4F7F\\u7528\\u53F3\\u5074\\u61F8\\u6D6E\\u6309\\u9215\\u9032\\u884C\\u64CD\\u4F5C \");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(58, DetailContentManagementSalesAccountComponent_ng_container_58_Template, 2, 1, \"ng-container\", 29)(59, DetailContentManagementSalesAccountComponent_div_59_Template, 23, 15, \"div\", 37);\n          i0.ɵɵelementStart(60, \"div\", 38)(61, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_61_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(62, \"svg\", 40);\n          i0.ɵɵelement(63, \"path\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(64, \"div\", 42);\n          i0.ɵɵtext(65, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(66, DetailContentManagementSalesAccountComponent_button_66_Template, 5, 1, \"button\", 43);\n          i0.ɵɵelementStart(67, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_67_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(68, DetailContentManagementSalesAccountComponent__svg_svg_68_Template, 2, 0, \"svg\", 45)(69, DetailContentManagementSalesAccountComponent__svg_svg_69_Template, 2, 0, \"svg\", 46);\n          i0.ɵɵelementStart(70, \"div\", 42);\n          i0.ɵɵtext(71);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_16_0;\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\" \", ctx.dynamicTitle, \" \");\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.dynamicTitle);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate2(\" \\u9032\\u5EA6\\uFF1A\", ctx.getCompletedItemsCount(), \" / \", (ctx.arrListFormItemReq == null ? null : ctx.arrListFormItemReq.length) || 0, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getProgressPercentage(), \"% \\u5B8C\\u6210 \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵattribute(\"stroke-dasharray\", ctx.getProgressPercentage() + \", 100\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getProgressPercentage(), \"%\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.arrListFormItemReq.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx.arrListFormItemReq.length || 0, \" \\u500B\\u9078\\u6A23\\u9805\\u76EE\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getCompletedItemsCount(), \" \\u5DF2\\u5B8C\\u6210\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"\", (ctx.arrListFormItemReq.length || 0) - ctx.getCompletedItemsCount(), \" \\u5F85\\u5B8C\\u6210\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.arrListFormItemReq && ctx.arrListFormItemReq.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem == null ? null : ctx.listFormItem.CIsLock);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"animate-pulse\", ctx.isSubmitting);\n          i0.ɵɵproperty(\"disabled\", ((tmp_16_0 = ctx.listFormItem == null ? null : ctx.listFormItem.CIsLock) !== null && tmp_16_0 !== undefined ? tmp_16_0 : false) || ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"\\u5132\\u5B58\\u4E2D...\" : \"\\u5132\\u5B58\\u8B8A\\u66F4\", \" \");\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, AppSharedModule, i12.HouseholdBindingComponent, NbCheckboxModule, Base64ImagePipe],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  min-height: 100vh;\\n}\\n\\n.page-background[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  animation: _ngcontent-%COMP%_gradientShift 20s ease infinite;\\n  background-size: 400% 400%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientShift {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\nnb-card[_ngcontent-%COMP%] {\\n  border-radius: 1rem !important;\\n  overflow: hidden;\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\nnb-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;\\n}\\n\\n.item-card[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.item-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);\\n}\\n\\ninput[nbInput][_ngcontent-%COMP%], \\nnb-select[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out !important;\\n}\\ninput[nbInput][_ngcontent-%COMP%]:focus, \\nnb-select[_ngcontent-%COMP%]:focus {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;\\n}\\n\\n.btn-enhanced[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.btn-enhanced[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.btn-enhanced[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n.image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.image-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.image-container[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n}\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.status-badge[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: inherit;\\n  border-radius: inherit;\\n  opacity: 0.1;\\n  transform: scale(0);\\n  transition: transform 0.3s ease;\\n}\\n.status-badge[_ngcontent-%COMP%]:hover::before {\\n  transform: scale(1.1);\\n}\\n\\n.responsive-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1.5rem;\\n}\\n@media (min-width: 768px) {\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    gap: 2rem;\\n  }\\n}\\n\\n.image-carousel[_ngcontent-%COMP%]:hover   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.carousel-btn[_ngcontent-%COMP%] {\\n  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n.carousel-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-50%) scale(1.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n}\\n.carousel-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n@media (max-width: 768px) {\\n  .carousel-btn[_ngcontent-%COMP%] {\\n    opacity: 1 !important;\\n    width: 2.5rem;\\n    height: 2.5rem;\\n  }\\n  .carousel-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1rem;\\n    height: 1rem;\\n  }\\n}\\n\\n.thumbnail-navigation[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 6px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 3px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6);\\n  border-radius: 3px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, #2563eb, #7c3aed);\\n}\\n\\n.image-modal[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-image[_ngcontent-%COMP%] {\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);\\n  transition: transform 0.3s ease;\\n  border-radius: 0.75rem;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n  max-height: 20vh;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.5));\\n  border-radius: 4px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.7));\\n}\\n\\n.modal-nav-btn[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  z-index: 9995 !important;\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n@media (max-width: 768px) {\\n  .modal-nav-btn[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n  }\\n  .modal-nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.5rem;\\n    height: 1.5rem;\\n  }\\n}\\n\\n.modal-close-btn[_ngcontent-%COMP%] {\\n  z-index: 9999 !important;\\n  pointer-events: auto;\\n  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4), 0 0 0 2px rgba(255, 255, 255, 0.1) !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-close-btn[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 30px rgba(239, 68, 68, 0.5), 0 0 0 3px rgba(255, 255, 255, 0.2) !important;\\n  transform: scale(1.1) rotate(90deg);\\n}\\n.modal-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .modal-close-btn[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n    top: 1rem;\\n    right: 1rem;\\n  }\\n  .modal-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.5rem;\\n    height: 1.5rem;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalFadeIn {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.9) translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromLeft {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(-20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromRight {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.animate-slide-in-left[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromLeft 0.5s ease-out;\\n}\\n\\n.animate-slide-in-right[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromRight 0.5s ease-out;\\n}\\n\\n.animate-fade-in-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n}\\n\\n@media (max-width: 768px) {\\n  .image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    margin: 1rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n    width: 3rem;\\n    height: 3rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.25rem;\\n    height: 1.25rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n    max-height: 15vh;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n  }\\n  .item-card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .carousel-btn[_ngcontent-%COMP%], \\n   .modal-nav-btn[_ngcontent-%COMP%] {\\n    border: 2px solid currentColor;\\n    background: rgba(255, 255, 255, 0.95);\\n  }\\n  .modal-close-btn[_ngcontent-%COMP%] {\\n    border: 2px solid currentColor;\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  *[_ngcontent-%COMP%], \\n   *[_ngcontent-%COMP%]::before, \\n   *[_ngcontent-%COMP%]::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .page-background[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\\n  }\\n  .item-card[_ngcontent-%COMP%] {\\n    background: #374151;\\n    border-color: #4b5563;\\n  }\\n  .carousel-btn[_ngcontent-%COMP%], \\n   .modal-nav-btn[_ngcontent-%COMP%] {\\n    background: rgba(31, 41, 55, 0.9);\\n    border-color: rgba(156, 163, 175, 0.3);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NbCheckboxModule", "tap", "SharedModule", "AppSharedModule", "BaseComponent", "EEvent", "Base64ImagePipe", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵlistener", "DetailContentManagementSalesAccountComponent_div_37_button_2_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "scrollToFirstIncompleteItem", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "DetailContentManagementSalesAccountComponent_div_37_button_2_Template", "DetailContentManagementSalesAccountComponent_div_37_Template_button_click_3_listener", "_r1", "scrollToTop", "DetailContentManagementSalesAccountComponent_div_37_Template_button_click_6_listener", "expandAll", "ɵɵelement", "DetailContentManagementSalesAccountComponent_div_37_Template_button_click_9_listener", "collapseAll", "DetailContentManagementSalesAccountComponent_div_37_Template_button_click_12_listener", "expandIncompleteOnly", "ɵɵadvance", "ɵɵproperty", "getCompletedItemsCount", "arrListFormItemReq", "length", "DetailContentManagementSalesAccountComponent_ng_container_38_button_22_Template_button_click_0_listener", "_r6", "idx_r7", "index", "scrollToItem", "DetailContentManagementSalesAccountComponent_ng_container_38_button_23_Template_button_click_0_listener", "_r8", "ɵɵpipeBind1", "getCurrentImage", "formItemReq_r5", "ɵɵsanitizeUrl", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_button_8_Template_button_click_0_listener", "$event", "_r11", "$implicit", "prevImage", "stopPropagation", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_button_9_Template_button_click_0_listener", "_r12", "nextImage", "ɵɵtextInterpolate2", "currentImageIndex", "CMatrialUrl", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_div_11_button_1_Template_button_click_0_listener", "i_r14", "_r13", "openImageModal", "ɵɵclassProp", "imageUrl_r15", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_div_11_button_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_Template_div_click_1_listener", "_r10", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_img_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_button_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_button_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_div_10_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_div_11_Template", "case_r16", "ɵɵtextInterpolate1", "label", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_50_div_1_Template_input_blur_4_listener", "i_r19", "_r18", "renameFile", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_50_div_1_Template_button_click_5_listener", "picture_r20", "removeImage", "id", "data", "name", "tmp_11_0", "listFormItem", "CIsLock", "undefined", "tmp_12_0", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_50_div_1_Template", "listPictures", "CDesignFileUrl", "ɵɵtwoWayListener", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r21", "remark_r22", "ɵɵtwoWayBindingSet", "selectedRemarkType", "onCheckboxRemarkChange", "ɵɵtwoWayProperty", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_label_1_nb_checkbox_1_Template", "ɵɵtextInterpolate", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_label_1_Template", "CRemarkTypeOptions", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_div_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_ng_template_7_Template", "ɵɵtemplateRefExtractor", "noRemarkOptions_r23", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_10_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_input_ngModelChange_25_listener", "_r9", "CItemName", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_input_ngModelChange_30_listener", "CRequireAnswer", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_nb_select_ngModelChange_34_listener", "selectedCUiType", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_nb_select_selected<PERSON><PERSON>e_34_listener", "changeSelectCUiType", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_nb_option_35_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_button_click_43_listener", "inputFile_r17", "ɵɵreference", "click", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_input_change_48_listener", "detectFiles", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_50_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_51_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_52_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template_app_household_binding_selectionChange_67_listener", "onHouseholdSelectionChange", "extractHouseholdCodes", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_68_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_div_69_Template", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "value", "tmp_15_0", "tmp_19_0", "CUiTypeOptions", "buildingData", "tmp_27_0", "selectedHouseholdsCached", "houseHoldList", "ɵɵelementContainerStart", "DetailContentManagementSalesAccountComponent_ng_container_38_Template_button_click_5_listener", "_r4", "toggleItemCollapse", "DetailContentManagementSalesAccountComponent_ng_container_38_div_17_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_18_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_button_22_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_button_23_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_div_24_Template", "isCollapsed", "isItemCompleted", "formItemReq_r26", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_button_7_Template_button_click_0_listener", "_r27", "prevImageModal", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_button_8_Template_button_click_0_listener", "_r28", "nextImageModal", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_div_16_button_2_Template_button_click_0_listener", "i_r30", "_r29", "imageUrl_r31", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_div_16_button_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_Template_div_click_0_listener", "_r25", "closeImageModal", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_Template_div_keydown_0_listener", "onKeydown", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_Template_button_click_1_listener", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_Template_div_click_4_listener", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_img_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_button_7_Template", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_button_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_div_16_Template", "DetailContentManagementSalesAccountComponent_ng_container_58_div_1_Template", "isModalOpen", "DetailContentManagementSalesAccountComponent_div_59_button_17_Template_button_click_0_listener", "_r33", "DetailContentManagementSalesAccountComponent_div_59_Template_button_click_2_listener", "_r32", "onSubmit", "DetailContentManagementSalesAccountComponent_div_59__svg_svg_6_Template", "DetailContentManagementSalesAccountComponent_div_59__svg_svg_7_Template", "DetailContentManagementSalesAccountComponent_div_59_Template_button_click_11_listener", "goBack", "DetailContentManagementSalesAccountComponent_div_59_Template_button_click_14_listener", "DetailContentManagementSalesAccountComponent_div_59_button_17_Template", "DetailContentManagementSalesAccountComponent_div_59_Template_button_click_18_listener", "isSubmitting", "ɵɵpropertyInterpolate2", "tmp_3_0", "getProgressPercentage", "DetailContentManagementSalesAccountComponent_button_66_Template_button_click_0_listener", "_r34", "copyToNewForm", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "_houseService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "cNoticeTypeOptions", "地主戶", "銷售戶", "selectedItems", "isNew", "dynamicTitle", "option", "find", "setCNoticeType", "noticeType", "some", "ngOnInit", "console", "log", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "error", "showErrorMSG", "queryParams", "houseType", "ngOnDestroy", "document", "body", "style", "overflow", "getItemByValue", "options", "item", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "formItemReq", "imageIndex", "key", "preventDefault", "households", "Array", "isArray", "map", "h", "code", "selectedHouseholds", "Object", "keys", "for<PERSON>ach", "household", "allSelected", "every", "updateSelectedHouseholdsCache", "getSelectedHouseholds", "updateAllSelectedHouseholdsCache", "checked", "createRemarkObject", "CRemarkType", "remarkObject", "remarkTypes", "includes", "mergeItems", "items", "Map", "has", "existing", "count", "set", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "o", "CFormItemHouseHold", "CFormId", "CUiType", "CSelectPicture", "x1", "CBase64", "url", "getListFormItem", "CIsPaging", "apiFormItemGetListFormItemPost$Json", "formItems", "CFirstMatrialUrl", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "e", "CFormID", "errorMessages", "showErrorMSGs", "scrollToFirstErrorItem", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "next", "showSucessMSG", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "bItem", "CHousehold", "CIsSelect", "validMaterialKeys", "Set", "material", "add", "validFormItems", "itemKey", "createRes", "loadBuildingDataFromAPI", "apiHouseGetDropDownPost$Json", "response", "convertApiResponseToBuildingData", "convertHouseHoldListToBuildingData", "entries", "building", "houses", "house", "HouseName", "Building", "floor", "Floor", "houseId", "HouseId", "houseName", "isSelected", "isDisabled", "buildingMatch", "match", "houseNumber", "parseInt", "replace", "Math", "ceil", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "hasItemName", "trim", "hasUiType", "hasRequireAnswer", "hasRemarkType", "selected", "completed", "round", "element", "getElementById", "scrollIntoView", "behavior", "block", "inline", "classList", "setTimeout", "remove", "firstIncompleteIndex", "findIndex", "firstErrorIndex", "window", "scrollTo", "top", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "HouseService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementSalesAccountComponent_Template", "rf", "ctx", "DetailContentManagementSalesAccountComponent_div_37_Template", "DetailContentManagementSalesAccountComponent_ng_container_38_Template", "DetailContentManagementSalesAccountComponent_ng_container_58_Template", "DetailContentManagementSalesAccountComponent_div_59_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_61_listener", "DetailContentManagementSalesAccountComponent_button_66_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_67_listener", "DetailContentManagementSalesAccountComponent__svg_svg_68_Template", "DetailContentManagementSalesAccountComponent__svg_svg_69_Template", "tmp_16_0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "BreadcrumbComponent", "i12", "HouseholdBindingComponent", "styles", "changeDetection"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService, HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq, GetMaterialListResponse } from 'src/services/api/models';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n  selectedHouseholdsCached?: string[]; // 緩存已選戶別，避免重複計算\r\n  isCollapsed?: boolean; // 是否收合狀態\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, AppSharedModule, NbCheckboxModule, Base64ImagePipe],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit, OnDestroy {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService,\r\n    private _houseService: HouseService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n  // 通知類型選項映射\r\n  cNoticeTypeOptions = [\r\n    { label: '地主戶', value: EnumHouseType.地主戶 },\r\n    { label: '銷售戶', value: EnumHouseType.銷售戶 }\r\n  ];\r\n  // 動態獲取標題文字\r\n  get dynamicTitle(): string {\r\n    const option = this.cNoticeTypeOptions.find(option =>\r\n      option.value === this.typeContentManagementSalesAccount.CNoticeType\r\n    );\r\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\r\n  }\r\n  // 設置通知類型（可供外部調用）\r\n  setCNoticeType(noticeType: EnumHouseType): void {\r\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\r\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\r\n      // 同時設定 CFormType 以保持一致性\r\n      this.typeContentManagementSalesAccount.CFormType = noticeType;\r\n    }\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }];\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"];\r\n  buildCaseId: number;\r\n  isSubmitting: boolean = false;\r\n\r\n\r\n  override ngOnInit(): void {\r\n    console.log('ngOnInit called');\r\n    this.route.paramMap.subscribe(params => {\r\n      console.log('Route params:', params);\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        console.log('buildCaseId set to:', this.buildCaseId);\r\n\r\n        if (this.buildCaseId > 0) {\r\n          console.log('Calling getListRegularNoticeFileHouseHold...');\r\n          this.getListRegularNoticeFileHouseHold()\r\n        } else {\r\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\r\n          console.error('Invalid buildCaseId:', this.buildCaseId);\r\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\r\n          this.goBack();\r\n        }\r\n      }\r\n    });\r\n\r\n    // 處理查詢參數中的戶型\r\n    this.route.queryParams.subscribe(queryParams => {\r\n      if (queryParams['houseType']) {\r\n        const houseType = +queryParams['houseType'];\r\n        this.setCNoticeType(houseType);\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保在組件銷毀時恢復body的滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n  // 新增：戶別選擇器相關屬性\r\n  buildingData: any = {}; // 存放建築物戶別資料\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      formItemReq.isModalOpen = true;\r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\r\n  extractHouseholdCodes(households: any[]): string[] {\r\n    if (!households || !Array.isArray(households)) {\r\n      return [];\r\n    }\r\n    return households.map(h => h.code || h);\r\n  }\r\n  // 新增：處理戶別選擇變更\r\n  onHouseholdSelectionChange(selectedHouseholds: string[], formItemReq: any) {\r\n    // 重置所有戶別選擇狀態\r\n    Object.keys(formItemReq.selectedItems).forEach(key => {\r\n      formItemReq.selectedItems[key] = false;\r\n    });\r\n\r\n    // 設置選中的戶別\r\n    selectedHouseholds.forEach(household => {\r\n      formItemReq.selectedItems[household] = true;\r\n    });\r\n\r\n    // 更新全選狀態\r\n    formItemReq.allSelected = this.houseHoldList.length > 0 &&\r\n      this.houseHoldList.every(item => formItemReq.selectedItems[item]);\r\n\r\n    // 更新緩存\r\n    this.updateSelectedHouseholdsCache(formItemReq);\r\n  }\r\n\r\n  // 新增：取得已選戶別數組\r\n  getSelectedHouseholds(formItemReq: any): string[] {\r\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\r\n  }\r\n\r\n  // 新增：更新已選戶別緩存\r\n  private updateSelectedHouseholdsCache(formItemReq: any): void {\r\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\r\n  }\r\n\r\n  // 新增：更新所有項目的緩存\r\n  private updateAllSelectedHouseholdsCache(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(formItemReq => {\r\n        this.updateSelectedHouseholdsCache(formItemReq);\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: GetMaterialListResponse) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0, selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [], selectedCUiType: this.CUiTypeOptions[0],\r\n              currentImageIndex: 0,\r\n              isModalOpen: false,\r\n              isCollapsed: false, // 新項目默認展開\r\n              CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64).filter((url: any) => url != null) as string[] : []\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes | null = null\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    console.log('getListFormItem called with:', {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n      CIsPaging: false\r\n    });\r\n\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        console.log('getListFormItem response:', res);\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          console.log('isNew:', this.isNew);\r\n          console.log('formItems exists:', !!res.Entries.formItems);\r\n          console.log('houseHoldList:', this.houseHoldList);\r\n\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem?.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [], selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0,\r\n                isModalOpen: false,\r\n                isCollapsed: false, // 現有項目默認展開\r\n                selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\r\n              }\r\n            })\r\n            console.log('arrListFormItemReq set to:', this.arrListFormItemReq);\r\n            console.log('arrListFormItemReq length:', this.arrListFormItemReq.length);\r\n          } else {\r\n            // 當無資料時，載入材料清單供新增使用\r\n            this.getMaterialList();\r\n          }\r\n\r\n          // 初始化所有項目的緩存\r\n          this.updateAllSelectedHouseholdsCache();\r\n        } else {\r\n          console.error('getListFormItem failed:', res);\r\n        }\r\n      })\r\n    ).subscribe({\r\n      error: (error) => {\r\n        console.error('getListFormItem error:', error);\r\n      }\r\n    })\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[] = []\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    // 設置提交狀態\r\n    this.isSubmitting = true;\r\n\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem?.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      // 滾動到第一個有錯誤的項目\r\n      this.scrollToFirstErrorItem();\r\n      this.isSubmitting = false;\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe({\r\n      next: (res) => {\r\n        this.isSubmitting = false;\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          // this.getListFormItem()\r\n          this.goBack()\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isSubmitting = false;\r\n        console.error('Save error:', error);\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe({\r\n      next: (res) => {\r\n        this.isSubmitting = false;\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          // this.getListFormItem()\r\n          this.goBack()\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isSubmitting = false;\r\n        console.error('Create error:', error);\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n  /**\r\n   * 複製當前表單到新表單\r\n   */\r\n  copyToNewForm() {\r\n    // 先取得當前有效的材料清單\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          // 建立有效材料清單的鍵值對應\r\n          const validMaterialKeys = new Set<string>();\r\n          res.Entries.forEach((material: any) => {\r\n            const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\r\n            validMaterialKeys.add(key);\r\n          });\r\n\r\n          // 篩選出仍然有效的表單項目\r\n          const validFormItems = this.arrListFormItemReq.filter((item: any) => {\r\n            const itemKey = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n            return validMaterialKeys.has(itemKey);\r\n          });\r\n\r\n          if (validFormItems.length === 0) {\r\n            this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\r\n            return;\r\n          }\r\n\r\n          // 準備複製的表單項目數據\r\n          this.saveListFormItemReq = validFormItems.map((e: any) => {\r\n            return {\r\n              CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n              CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n              CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n              CFormItemId: null, // 設為 null 以建立新項目\r\n              CFormID: null, // 設為 null 以建立新表單\r\n              CName: e.CName,\r\n              CPart: e.CPart,\r\n              CLocation: e.CLocation,\r\n              CItemName: e.CItemName,\r\n              CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n              CTotalAnswer: e.CTotalAnswer,\r\n              CRequireAnswer: e.CRequireAnswer,\r\n              CUiType: e.selectedCUiType.value,\r\n            }\r\n          });\r\n\r\n          // 執行驗證\r\n          this.validation()\r\n          if (this.valid.errorMessages.length > 0) {\r\n            this.message.showErrorMSGs(this.valid.errorMessages);\r\n            return\r\n          }\r\n\r\n          // 建立複製的表單\r\n          this.creatListFormItem = {\r\n            CBuildCaseId: this.buildCaseId,\r\n            CFormItem: this.saveListFormItemReq || null,\r\n            CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n          }\r\n\r\n          this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n            body: this.creatListFormItem\r\n          }).subscribe(createRes => {\r\n            if (createRes.StatusCode == 0) {\r\n              this.message.showSucessMSG(`複製表單成功，已篩選 ${validFormItems.length} 個有效項目`);\r\n              // 重新載入資料以顯示新的未鎖定表單\r\n              this.getListFormItem()\r\n            }\r\n          })\r\n        } else {\r\n          this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\r\n  private loadBuildingDataFromAPI(): void {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this._houseService.apiHouseGetDropDownPost$Json({ buildCaseId: this.buildCaseId }).subscribe({\r\n      next: (response) => {\r\n        console.log('GetDropDown API response:', response);\r\n        if (response.Entries) {\r\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n          console.log('Converted buildingData:', this.buildingData);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading building data from API:', error);\r\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\r\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\r\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：將 API 回應轉換為建築物資料格式\r\n  private convertApiResponseToBuildingData(entries: any): any {\r\n    const buildingData: any = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]: [string, any]) => {\r\n      buildingData[building] = houses.map((house: any) => ({\r\n        code: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseName: house.HouseName,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  // 新增：將戶別清單轉換為建築物資料格式\r\n  convertHouseHoldListToBuildingData(houseHoldList: string[]): any {\r\n    if (!houseHoldList || houseHoldList.length === 0) {\r\n      return {};\r\n    }\r\n\r\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\r\n    const buildingData: any = {};\r\n\r\n    houseHoldList.forEach(household => {\r\n      // 嘗試從戶別名稱中提取建築物代碼\r\n      const buildingMatch = household.match(/^([A-Z]+)/);\r\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\r\n\r\n      if (!buildingData[building]) {\r\n        buildingData[building] = [];\r\n      }\r\n\r\n      // 計算樓層（假設每4戶為一層）\r\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\r\n      const floor = Math.ceil(houseNumber / 4);\r\n\r\n      buildingData[building].push({\r\n        code: household,\r\n        building: building,\r\n        floor: `${floor}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      });\r\n    }); return buildingData;\r\n  }\r\n\r\n  houseHoldList: any[];\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    console.log('getListRegularNoticeFileHouseHold called with buildCaseId:', this.buildCaseId);\r\n\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        console.log('getListRegularNoticeFileHouseHold response:', res);\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n\r\n          // 載入建築物資料 (只呼叫一次 GetDropDown API)\r\n          this.loadBuildingDataFromAPI();\r\n\r\n          this.getListFormItem()\r\n        } else {\r\n          console.error('getListRegularNoticeFileHouseHold failed:', res);\r\n        }\r\n      })\r\n    ).subscribe({\r\n      error: (error) => {\r\n        console.error('getListRegularNoticeFileHouseHold error:', error);\r\n      }\r\n    })\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  // UI優化相關方法\r\n\r\n  /**\r\n   * 檢查項目是否已完成\r\n   */\r\n  isItemCompleted(formItemReq: ExtendedSaveListFormItemReq): boolean {\r\n    // 檢查必填欄位是否都已填寫\r\n    const hasItemName = !!formItemReq.CItemName && formItemReq.CItemName.trim() !== '';\r\n    const hasUiType = !!formItemReq.selectedCUiType && formItemReq.selectedCUiType.value;\r\n    const hasRequireAnswer = !!formItemReq.CRequireAnswer && formItemReq.CRequireAnswer > 0;\r\n\r\n    // 如果是建材選樣類型，檢查是否有選擇備註類型\r\n    let hasRemarkType = true;\r\n    if (formItemReq.selectedCUiType?.value === 3) {\r\n      hasRemarkType = !!formItemReq.selectedRemarkType &&\r\n        Object.values(formItemReq.selectedRemarkType).some(selected => selected);\r\n    }\r\n\r\n    return hasItemName && hasUiType && hasRequireAnswer && hasRemarkType;\r\n  }\r\n\r\n  /**\r\n   * 獲取已完成項目數量\r\n   */\r\n  getCompletedItemsCount(): number {\r\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\r\n    return this.arrListFormItemReq.filter(item => this.isItemCompleted(item)).length;\r\n  }\r\n\r\n  /**\r\n   * 獲取進度百分比\r\n   */\r\n  getProgressPercentage(): number {\r\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\r\n    const completed = this.getCompletedItemsCount();\r\n    return Math.round((completed / this.arrListFormItemReq.length) * 100);\r\n  }\r\n\r\n  /**\r\n   * 滾動到指定項目\r\n   */\r\n  scrollToItem(index: number): void {\r\n    const element = document.getElementById(`form-item-${index}`);\r\n    if (element) {\r\n      element.scrollIntoView({\r\n        behavior: 'smooth',\r\n        block: 'start',\r\n        inline: 'nearest'\r\n      });\r\n\r\n      // 添加高亮效果\r\n      element.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-75');\r\n      setTimeout(() => {\r\n        element.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-75');\r\n      }, 2000);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到第一個未完成的項目\r\n   */\r\n  scrollToFirstIncompleteItem(): void {\r\n    if (!this.arrListFormItemReq) return;\r\n\r\n    const firstIncompleteIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\r\n    if (firstIncompleteIndex !== -1) {\r\n      this.scrollToItem(firstIncompleteIndex);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到第一個有錯誤的項目\r\n   */\r\n  scrollToFirstErrorItem(): void {\r\n    if (!this.arrListFormItemReq) return;\r\n\r\n    // 找到第一個有錯誤的項目\r\n    const firstErrorIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\r\n    if (firstErrorIndex !== -1) {\r\n      this.scrollToItem(firstErrorIndex);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到頂部\r\n   */\r\n  scrollToTop(): void {\r\n    window.scrollTo({\r\n      top: 0,\r\n      behavior: 'smooth'\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 切換項目收合狀態\r\n   */\r\n  toggleItemCollapse(formItemReq: ExtendedSaveListFormItemReq): void {\r\n    formItemReq.isCollapsed = !formItemReq.isCollapsed;\r\n  }\r\n\r\n  /**\r\n   * 全部展開\r\n   */\r\n  expandAll(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = false;\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 全部收合\r\n   */\r\n  collapseAll(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = true;\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 只展開未完成的項目\r\n   */\r\n  expandIncompleteOnly(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = this.isItemCompleted(item);\r\n      });\r\n    }\r\n  }\r\n\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\"> <nb-card class=\"shadow-xl border-0 rounded-xl\">\r\n    <nb-card-header class=\"bg-white border-b border-gray-200 p-6\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"w-1 h-8 bg-green-500 rounded-full\"></div>\r\n          <div>\r\n            <ngx-breadcrumb></ngx-breadcrumb>\r\n          </div>\r\n        </div>\r\n        <div class=\"flex items-center space-x-2\">\r\n          <span class=\"px-3 py-1 text-sm bg-green-100 text-green-800 rounded-full font-medium\">\r\n            {{ dynamicTitle }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"p-6 bg-gray-50\">\r\n      <div class=\"space-y-8\">\r\n        <!-- Page Title Section -->\r\n        <div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\r\n          <div class=\"flex items-center justify-between\">\r\n            <div class=\"flex items-center space-x-3\">\r\n              <div class=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n                <svg class=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                    d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\">\r\n                  </path>\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <h3 class=\"text-xl font-bold text-gray-800\">{{ dynamicTitle }}</h3>\r\n                <p class=\"text-sm text-gray-600 mt-1\">管理選樣項目的詳細設定與配置</p>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Progress Indicator -->\r\n            <div class=\"flex items-center space-x-4\">\r\n              <div class=\"text-right\">\r\n                <div class=\"text-sm font-medium text-gray-700\">\r\n                  進度：{{ getCompletedItemsCount() }} / {{ arrListFormItemReq?.length || 0 }}\r\n                </div>\r\n                <div class=\"text-xs text-gray-500\">\r\n                  {{ getProgressPercentage() }}% 完成\r\n                </div>\r\n              </div>\r\n              <div class=\"relative w-16 h-16\">\r\n                <svg class=\"w-16 h-16 transform -rotate-90\" viewBox=\"0 0 36 36\">\r\n                  <path class=\"text-gray-200\" stroke=\"currentColor\" stroke-width=\"3\" fill=\"none\"\r\n                    d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\" />\r\n                  <path class=\"text-blue-500\" stroke=\"currentColor\" stroke-width=\"3\" fill=\"none\"\r\n                    [attr.stroke-dasharray]=\"getProgressPercentage() + ', 100'\"\r\n                    d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\" />\r\n                </svg>\r\n                <div class=\"absolute inset-0 flex items-center justify-center\">\r\n                  <span class=\"text-xs font-bold text-gray-700\">{{ getProgressPercentage() }}%</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Quick Navigation -->\r\n          <div class=\"mt-4 flex items-center justify-between\" *ngIf=\"arrListFormItemReq.length > 1\">\r\n            <div class=\"flex items-center space-x-2\">\r\n              <button\r\n                class=\"px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-full transition-colors\"\r\n                (click)=\"scrollToFirstIncompleteItem()\" *ngIf=\"getCompletedItemsCount() < arrListFormItemReq.length\">\r\n                跳至未完成項目\r\n              </button>\r\n              <button\r\n                class=\"px-3 py-1 text-xs bg-green-100 hover:bg-green-200 text-green-700 rounded-full transition-colors\"\r\n                (click)=\"scrollToTop()\">\r\n                回到頂部\r\n              </button>\r\n\r\n              <!-- 收合控制按鈕 -->\r\n              <div class=\"flex items-center space-x-1 border-l border-gray-300 pl-2 ml-2\">\r\n                <button\r\n                  class=\"px-2 py-1 text-xs bg-purple-100 hover:bg-purple-200 text-purple-700 rounded transition-colors\"\r\n                  (click)=\"expandAll()\" title=\"全部展開\">\r\n                  <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                      d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\">\r\n                    </path>\r\n                  </svg>\r\n                </button>\r\n                <button\r\n                  class=\"px-2 py-1 text-xs bg-purple-100 hover:bg-purple-200 text-purple-700 rounded transition-colors\"\r\n                  (click)=\"collapseAll()\" title=\"全部收合\">\r\n                  <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 9l6 6m0 0l6-6m-6 6L9 3\">\r\n                    </path>\r\n                  </svg>\r\n                </button>\r\n                <button\r\n                  class=\"px-2 py-1 text-xs bg-orange-100 hover:bg-orange-200 text-orange-700 rounded transition-colors\"\r\n                  (click)=\"expandIncompleteOnly()\" title=\"只展開未完成\">\r\n                  <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                      d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\">\r\n                    </path>\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n            <div class=\"text-xs text-gray-500\">\r\n              使用浮動按鈕快速儲存，或滾動至底部使用完整操作選項\r\n            </div>\r\n          </div>\r\n        </div> <!-- Form Items Section -->\r\n        <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n          <div [id]=\"'form-item-' + idx\"\r\n            class=\"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-xl\">\r\n            <!-- Item Header -->\r\n            <div class=\"bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200\">\r\n              <div class=\"flex items-center justify-between\">\r\n                <div class=\"flex items-center space-x-3\">\r\n                  <!-- 收合/展開按鈕 -->\r\n                  <button\r\n                    class=\"w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\"\r\n                    (click)=\"toggleItemCollapse(formItemReq)\" [title]=\"formItemReq.isCollapsed ? '展開項目' : '收合項目'\">\r\n                    <svg class=\"w-4 h-4 text-gray-600 transition-transform duration-200\"\r\n                      [class.rotate-180]=\"formItemReq.isCollapsed\" fill=\"none\" stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"></path>\r\n                    </svg>\r\n                  </button>\r\n\r\n                  <div\r\n                    class=\"w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\r\n                    {{idx + 1}}\r\n                  </div>\r\n                  <div class=\"flex-1\">\r\n                    <h4 class=\"text-lg font-semibold text-gray-800\">\r\n                      {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n                    </h4>\r\n                    <p class=\"text-sm text-gray-600\">項目編號 #{{idx + 1}}</p>\r\n                  </div>\r\n                </div>\r\n                <div class=\"flex items-center space-x-3\">\r\n                  <!-- Completion Status -->\r\n                  <div class=\"flex items-center space-x-2\">\r\n                    <div *ngIf=\"isItemCompleted(formItemReq)\"\r\n                      class=\"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\" title=\"項目已完成\">\r\n                      <svg class=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"></path>\r\n                      </svg>\r\n                    </div>\r\n                    <div *ngIf=\"!isItemCompleted(formItemReq)\"\r\n                      class=\"w-6 h-6 bg-orange-400 rounded-full flex items-center justify-center\" title=\"項目未完成\">\r\n                      <svg class=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\">\r\n                        </path>\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- UI Type Badge -->\r\n                  <span class=\"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full\">\r\n                    {{formItemReq.selectedCUiType?.label || '未設定'}}\r\n                  </span>\r\n\r\n                  <!-- Item Navigation -->\r\n                  <div class=\"flex items-center space-x-1\">\r\n                    <button *ngIf=\"idx > 0\"\r\n                      class=\"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\"\r\n                      (click)=\"scrollToItem(idx - 1)\" title=\"上一個項目\">\r\n                      <svg class=\"w-4 h-4 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\">\r\n                        </path>\r\n                      </svg>\r\n                    </button>\r\n                    <button *ngIf=\"idx < arrListFormItemReq.length - 1\"\r\n                      class=\"w-7 h-7 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors\"\r\n                      (click)=\"scrollToItem(idx + 1)\" title=\"下一個項目\">\r\n                      <svg class=\"w-4 h-4 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Main Content Area -->\r\n            <div class=\"p-6\" *ngIf=\"!formItemReq.isCollapsed\">\r\n              <div class=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\r\n\r\n                <!-- Material Images Section (Enhanced) -->\r\n                <div class=\"lg:col-span-1\">\r\n                  <div class=\"space-y-4\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">主要材料示意</label>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0\" class=\"relative\">\r\n                      <!-- Enhanced Image carousel container -->\r\n                      <div\r\n                        class=\"aspect-square w-full relative overflow-hidden rounded-xl border-2 border-gray-200 cursor-pointer group shadow-md hover:shadow-lg transition-all duration-300\"\r\n                        (click)=\"openImageModal(formItemReq)\">\r\n                        <img class=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\r\n                          [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n                        <!-- Enhanced Zoom overlay -->\r\n                        <div\r\n                          class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center\">\r\n                          <div class=\"transform scale-75 group-hover:scale-100 transition-transform duration-300\">\r\n                            <div class=\"w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center\">\r\n                              <svg class=\"w-6 h-6 text-gray-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                                  d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"></path>\r\n                              </svg>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- Navigation buttons -->\r\n                        <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n                          (click)=\"prevImage(formItemReq); $event.stopPropagation()\" title=\"上一張圖片\">\r\n                          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\">\r\n                            </path>\r\n                          </svg>\r\n                        </button>\r\n\r\n                        <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n                          (click)=\"nextImage(formItemReq); $event.stopPropagation()\" title=\"下一張圖片\">\r\n                          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\">\r\n                            </path>\r\n                          </svg>\r\n                        </button>\r\n\r\n                        <!-- Enhanced Image counter -->\r\n                        <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\">\r\n                          {{(formItemReq.currentImageIndex || 0) + 1}} / {{formItemReq.CMatrialUrl.length}}\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- Enhanced Thumbnail navigation -->\r\n                      <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\" class=\"flex gap-2 mt-3 overflow-x-auto pb-2\">\r\n                        <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n                          class=\"flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\"\r\n                          [class.border-blue-500]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          [class.border-gray-300]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n                          [class.ring-2]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          [class.ring-blue-200]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          (click)=\"openImageModal(formItemReq, i)\" [title]=\"'點選放大第 ' + (i + 1) + ' 張圖片'\">\r\n                          <img class=\"w-full h-full object-cover transition-transform hover:scale-110\"\r\n                            [src]=\"imageUrl | base64Image\">\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"!formItemReq.CMatrialUrl || formItemReq.CMatrialUrl.length === 0\"\r\n                      class=\"aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\">\r\n                      <svg class=\"w-12 h-12 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-sm\">無主要材料示意</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Form Fields Section (Enhanced) -->\r\n                <div class=\"lg:col-span-2\">\r\n                  <div class=\"space-y-6\">\r\n                    <div class=\"flex items-center space-x-2 mb-4\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">基本設定</label>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Form Groups -->\r\n                    <div class=\"space-y-4\">\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'CItemName_' + idx\"\r\n                          class=\"block text-sm font-medium text-gray-700 mb-2\">項目名稱</label>\r\n                        <div class=\"flex items-center space-x-3 bg-gray-50 p-3 rounded-lg border border-gray-200\">\r\n                          <span\r\n                            class=\"text-sm text-gray-600 font-medium px-2 py-1 bg-blue-100 rounded-md whitespace-nowrap\">\r\n                            {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}:\r\n                          </span>\r\n                          <input type=\"text\" [id]=\"'CItemName_' + idx\"\r\n                            class=\"flex-1 border-0 bg-transparent focus:ring-2 focus:ring-blue-500 focus:border-transparent rounded-md p-2\"\r\n                            nbInput [(ngModel)]=\"formItemReq.CItemName\" placeholder=\"例如：廚房檯面\"\r\n                            [disabled]=\"listFormItem?.CIsLock ?? false\" />\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'cRequireAnswer_' + idx\"\r\n                          class=\"block text-sm font-medium text-gray-700 mb-2\">必填數量</label>\r\n                        <div class=\"relative\">\r\n                          <input type=\"number\" [id]=\"'cRequireAnswer_' + idx\"\r\n                            class=\"w-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 rounded-lg p-3 transition-all duration-200\"\r\n                            nbInput placeholder=\"輸入數量\" [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n                            [disabled]=\"formItemReq.selectedCUiType.value === 3 || (listFormItem?.CIsLock ?? false)\" />\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'uiType_' + idx\"\r\n                          class=\"block text-sm font-medium text-gray-700 mb-2\">前台UI類型</label>\r\n                        <nb-select placeholder=\"選擇UI類型\" [id]=\"'uiType_' + idx\" [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n                          class=\"w-full border-2 border-gray-200 focus:border-blue-500 rounded-lg transition-all duration-200\"\r\n                          (selectedChange)=\"changeSelectCUiType(formItemReq)\"\r\n                          [disabled]=\"listFormItem?.CIsLock ?? false\">\r\n                          <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                            {{ case.label }}\r\n                          </nb-option>\r\n                        </nb-select>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Concept Design Section (Enhanced) -->\r\n                <div class=\"lg:col-span-1\">\r\n                  <div class=\"space-y-4\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">概念設計</label>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Upload Button -->\r\n                    <button\r\n                      class=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      [disabled]=\"listFormItem?.CIsLock\" (click)=\"inputFile.click()\">\r\n                      <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\">\r\n                        </path>\r\n                      </svg>\r\n                      <span>上傳概念設計圖</span>\r\n                    </button>\r\n                    <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n                      accept=\"image/png, image/gif, image/jpeg\">\r\n\r\n                    <!-- Enhanced Uploaded Pictures List -->\r\n                    <div *ngIf=\"formItemReq.listPictures && formItemReq.listPictures.length > 0\" class=\"space-y-3\">\r\n                      <div *ngFor=\"let picture of formItemReq.listPictures; let i = index\"\r\n                        class=\"bg-gray-50 border border-gray-200 p-3 rounded-lg hover:shadow-md transition-all duration-200\">\r\n                        <div class=\"relative group\">\r\n                          <img class=\"w-full h-32 object-cover rounded-lg mb-3 border border-gray-200\"\r\n                            [src]=\"picture.data\">\r\n                          <div\r\n                            class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg\">\r\n                          </div>\r\n                        </div>\r\n                        <input nbInput\r\n                          class=\"w-full p-2 text-sm mb-2 border border-gray-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                          type=\"text\" placeholder=\"圖片說明/檔名\" [value]=\"picture.name\"\r\n                          (blur)=\"renameFile($event, i, formItemReq)\" [disabled]=\"listFormItem?.CIsLock ?? false\">\r\n                        <button\r\n                          class=\"w-full bg-red-100 hover:bg-red-200 text-red-700 font-medium py-2 px-3 rounded-md transition-colors duration-200 text-sm\"\r\n                          (click)=\"removeImage(picture.id, formItemReq)\" [disabled]=\"listFormItem?.CIsLock ?? false\">\r\n                          <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                              d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\">\r\n                            </path>\r\n                          </svg>\r\n                          刪除圖片\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Default Concept Design Image -->\r\n                    <div class=\"space-y-2\"\r\n                      *ngIf=\"formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\">\r\n                      <label class=\"block text-xs font-medium text-gray-600\">預設概念圖</label>\r\n                      <div class=\"relative group\">\r\n                        <img class=\"w-full h-32 object-cover rounded-lg border border-gray-200\"\r\n                          [src]=\"formItemReq.CDesignFileUrl | base64Image\">\r\n                        <div\r\n                          class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg\">\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div\r\n                      *ngIf=\"!formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\"\r\n                      class=\"h-32 w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 text-gray-400\">\r\n                      <svg class=\"w-8 h-8 mb-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-xs\">無概念設計圖</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Enhanced Separator -->\r\n              <div class=\"my-8\">\r\n                <div class=\"relative\">\r\n                  <div class=\"absolute inset-0 flex items-center\">\r\n                    <div class=\"w-full border-t border-gray-300\"></div>\r\n                  </div>\r\n                  <div class=\"relative flex justify-center text-sm\">\r\n                    <span class=\"px-4 bg-white text-gray-500 font-medium\">設定選項</span>\r\n                  </div>\r\n                </div>\r\n              </div> <!-- Enhanced Applicable Households Section -->\r\n              <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                <div class=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\r\n                  <div class=\"flex items-center space-x-2 mb-4\">\r\n                    <svg class=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                        d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\">\r\n                      </path>\r\n                    </svg>\r\n                    <h5 class=\"font-semibold text-blue-800\">適用戶型</h5>\r\n                  </div> <!-- 新的戶別選擇器 -->\r\n                  <app-household-binding [buildingData]=\"buildingData\" [placeholder]=\"'請選擇適用戶型'\"\r\n                    [disabled]=\"listFormItem?.CIsLock ?? false\" [allowBatchSelect]=\"true\"\r\n                    [ngModel]=\"formItemReq.selectedHouseholdsCached\"\r\n                    (selectionChange)=\"onHouseholdSelectionChange(extractHouseholdCodes($event), formItemReq)\"\r\n                    class=\"w-full\" [useHouseNameMode]=\"true\">\r\n                  </app-household-binding>\r\n\r\n                  <!-- 無戶別資料時的顯示 -->\r\n                  <div class=\"text-center py-4\" *ngIf=\"houseHoldList.length === 0\">\r\n                    <svg class=\"w-8 h-8 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                        d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\">\r\n                      </path>\r\n                    </svg>\r\n                    <span class=\"text-gray-500 text-sm\">尚無戶別資料</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Enhanced Remark Options Section -->\r\n                <div class=\"bg-orange-50 p-4 rounded-lg border border-orange-200\"\r\n                  *ngIf=\"formItemReq.selectedCUiType.value === 3\">\r\n                  <div class=\"flex items-center space-x-2 mb-4\">\r\n                    <svg class=\"w-5 h-5 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                        d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\">\r\n                      </path>\r\n                    </svg>\r\n                    <h5 class=\"font-semibold text-orange-800\">備註選項</h5>\r\n                  </div>\r\n\r\n                  <div class=\"grid grid-cols-1 gap-2\"\r\n                    *ngIf=\"CRemarkTypeOptions && CRemarkTypeOptions.length > 0; else noRemarkOptions\">\r\n                    <label *ngFor=\"let remark of CRemarkTypeOptions\"\r\n                      class=\"flex items-center cursor-pointer hover:bg-orange-100 p-2 rounded-md transition-colors\">\r\n                      <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\"\r\n                        [(checked)]=\"formItemReq.selectedRemarkType[remark]\" [disabled]=\"listFormItem?.CIsLock\"\r\n                        value=\"item\" (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\" class=\"mr-3\">\r\n                      </nb-checkbox>\r\n                      <span class=\"text-gray-700\">{{ remark }}</span>\r\n                    </label>\r\n                  </div>\r\n\r\n                  <ng-template #noRemarkOptions>\r\n                    <div class=\"text-center py-4\">\r\n                      <svg class=\"w-8 h-8 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-gray-500 text-sm\">尚無備註選項</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <!-- Simplified Footer with Info Only -->\r\n    <nb-card-footer class=\"bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4 sticky bottom-0 shadow-lg z-30\">\r\n      <div class=\"flex items-center justify-center\">\r\n        <div class=\"flex items-center space-x-6 text-sm text-gray-600\">\r\n          <div class=\"flex items-center space-x-2\">\r\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n            </svg>\r\n            <span>共 {{arrListFormItemReq.length || 0}} 個選樣項目</span>\r\n          </div>\r\n\r\n          <div class=\"flex items-center space-x-4\">\r\n            <div class=\"flex items-center space-x-2\">\r\n              <div class=\"w-3 h-3 bg-green-500 rounded-full\"></div>\r\n              <span>{{ getCompletedItemsCount() }} 已完成</span>\r\n            </div>\r\n            <div class=\"flex items-center space-x-2\">\r\n              <div class=\"w-3 h-3 bg-orange-400 rounded-full\"></div>\r\n              <span>{{ (arrListFormItemReq.length || 0) - getCompletedItemsCount() }} 待完成</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"text-xs text-gray-500\">\r\n            使用右側懸浮按鈕進行操作\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</div>\r\n\r\n<!-- Enhanced Image Modal for each formItemReq -->\r\n<ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n  <div *ngIf=\"formItemReq.isModalOpen\"\r\n    class=\"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fade-in-up\"\r\n    (click)=\"closeImageModal(formItemReq)\" (keydown)=\"onKeydown($event, formItemReq)\" tabindex=\"0\">\r\n\r\n    <!-- Enhanced Close Button -->\r\n    <button\r\n      class=\"modal-close-btn fixed top-6 right-6 z-[60] bg-red-500 bg-opacity-95 hover:bg-red-600 hover:bg-opacity-100 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-2xl\"\r\n      (click)=\"closeImageModal(formItemReq)\" title=\"關閉圖片檢視 (按 ESC 鍵)\">\r\n      <svg class=\"w-7 h-7\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n      </svg>\r\n    </button>\r\n\r\n    <!-- Enhanced Modal Content -->\r\n    <div class=\"relative max-w-7xl max-h-full w-full h-full flex items-center justify-center animate-slide-in-left\"\r\n      (click)=\"$event.stopPropagation()\">\r\n\r\n      <!-- Main Image Container -->\r\n      <div class=\"relative max-w-full max-h-full bg-white rounded-2xl p-2 shadow-2xl\">\r\n        <img class=\"max-w-full max-h-[85vh] object-contain rounded-xl animate-fade-in-up\"\r\n          [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n        <!-- Enhanced Navigation Buttons -->\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"modal-nav-btn absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\"\r\n          (click)=\"prevImageModal(formItemReq)\" title=\"上一張圖片 (按 ← 鍵)\">\r\n          <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\"></path>\r\n          </svg>\r\n        </button>\r\n\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"modal-nav-btn absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\"\r\n          (click)=\"nextImageModal(formItemReq)\" title=\"下一張圖片 (按 → 鍵)\">\r\n          <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\"></path>\r\n          </svg>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Enhanced Image Counter -->\r\n      <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n        class=\"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\">\r\n        <div class=\"flex items-center space-x-3\">\r\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n              d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n            </path>\r\n          </svg>\r\n          <span class=\"font-medium text-lg\">{{(formItemReq.currentImageIndex || 0) + 1}} /\r\n            {{formItemReq.CMatrialUrl.length}}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Enhanced Image Info -->\r\n      <div\r\n        class=\"absolute bottom-6 right-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-lg text-sm backdrop-blur-sm shadow-lg\">\r\n        <div class=\"flex items-center space-x-2\">\r\n          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n              d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n          </svg>\r\n          <span class=\"font-medium\">{{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Enhanced Thumbnail Strip for Modal -->\r\n      <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n        class=\"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\">\r\n        <div class=\"flex gap-3 overflow-x-auto max-w-[80vw] modal-thumbnails\">\r\n          <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n            class=\"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105\"\r\n            [class.border-white]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.border-gray-400]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-3]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-white]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-opacity-50]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            (click)=\"formItemReq.currentImageIndex = i\" [title]=\"'跳至第 ' + (i + 1) + ' 張圖片'\">\r\n            <img class=\"w-full h-full object-cover transition-transform duration-200\" [src]=\"imageUrl | base64Image\">\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- Floating Action Buttons -->\r\n<div class=\"fixed bottom-6 right-6 z-50 flex flex-col items-end space-y-3\"\r\n  *ngIf=\"arrListFormItemReq && arrListFormItemReq.length > 0\">\r\n  <!-- Progress Ring Button -->\r\n  <div class=\"relative\">\r\n    <button\r\n      class=\"w-16 h-16 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-2xl transition-all duration-300 hover:scale-110 flex items-center justify-center group\"\r\n      [class.animate-pulse]=\"isSubmitting\" (click)=\"onSubmit()\"\r\n      [disabled]=\"(listFormItem?.CIsLock ?? false) || isSubmitting\"\r\n      title=\"快速儲存 ({{ getCompletedItemsCount() }}/{{ arrListFormItemReq.length || 0 }} 完成)\">\r\n\r\n      <!-- Progress Ring -->\r\n      <svg class=\"absolute inset-0 w-16 h-16 transform -rotate-90\" viewBox=\"0 0 36 36\">\r\n        <path class=\"text-blue-300\" stroke=\"currentColor\" stroke-width=\"2\" fill=\"none\"\r\n          d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\" />\r\n        <path class=\"text-white\" stroke=\"currentColor\" stroke-width=\"2\" fill=\"none\"\r\n          [attr.stroke-dasharray]=\"getProgressPercentage() + ', 100'\"\r\n          d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\" />\r\n      </svg>\r\n\r\n      <!-- Save Icon -->\r\n      <svg *ngIf=\"!isSubmitting\" class=\"w-6 h-6 z-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"></path>\r\n      </svg>\r\n\r\n      <!-- Loading Spinner -->\r\n      <svg *ngIf=\"isSubmitting\" class=\"w-6 h-6 z-10 animate-spin\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n          d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\">\r\n        </path>\r\n      </svg>\r\n    </button>\r\n\r\n    <!-- Progress Percentage -->\r\n    <div\r\n      class=\"absolute -top-2 -right-2 w-6 h-6 bg-green-500 text-white text-xs rounded-full flex items-center justify-center font-bold\">\r\n      {{ getProgressPercentage() }}\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Secondary Actions -->\r\n  <div class=\"flex flex-col space-y-2\">\r\n    <!-- Cancel Button -->\r\n    <button\r\n      class=\"w-12 h-12 bg-gray-500 hover:bg-gray-600 text-white rounded-full shadow-lg transition-all duration-300 hover:scale-110 flex items-center justify-center\"\r\n      (click)=\"goBack()\" [disabled]=\"isSubmitting\" title=\"取消並返回\">\r\n      <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"></path>\r\n      </svg>\r\n    </button>\r\n\r\n    <!-- Scroll to Top Button -->\r\n    <button\r\n      class=\"w-12 h-12 bg-indigo-500 hover:bg-indigo-600 text-white rounded-full shadow-lg transition-all duration-300 hover:scale-110 flex items-center justify-center\"\r\n      (click)=\"scrollToTop()\" title=\"回到頂部\">\r\n      <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 10l7-7m0 0l7 7m-7-7v18\"></path>\r\n      </svg>\r\n    </button>\r\n\r\n    <!-- Jump to Incomplete Item Button -->\r\n    <button *ngIf=\"getCompletedItemsCount() < arrListFormItemReq.length\"\r\n      class=\"w-12 h-12 bg-orange-500 hover:bg-orange-600 text-white rounded-full shadow-lg transition-all duration-300 hover:scale-110 flex items-center justify-center\"\r\n      (click)=\"scrollToFirstIncompleteItem()\" title=\"跳至未完成項目\">\r\n      <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n          d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\">\r\n        </path>\r\n      </svg>\r\n    </button>\r\n\r\n    <!-- Expand Incomplete Only Button -->\r\n    <button\r\n      class=\"w-12 h-12 bg-purple-500 hover:bg-purple-600 text-white rounded-full shadow-lg transition-all duration-300 hover:scale-110 flex items-center justify-center\"\r\n      (click)=\"expandIncompleteOnly()\" title=\"只展開未完成項目\">\r\n      <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n          d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"></path>\r\n      </svg>\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Floating Tooltip -->\r\n  <div\r\n    class=\"absolute right-20 top-0 bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n    {{ getProgressPercentage() }}% 完成 ({{ getCompletedItemsCount() }}/{{ arrListFormItemReq.length || 0 }})\r\n  </div>\r\n</div>\r\n\r\n<!-- Right Side Floating Action Buttons -->\r\n<div class=\"fixed right-6 top-1/2 transform -translate-y-1/2 z-50 flex flex-col space-y-3\">\r\n  <!-- Cancel Button -->\r\n  <button\r\n    class=\"w-14 h-14 bg-gray-500 hover:bg-gray-600 text-white rounded-full shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center group\"\r\n    (click)=\"goBack()\" [disabled]=\"isSubmitting\" title=\"取消並返回\">\r\n    <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"></path>\r\n    </svg>\r\n\r\n    <!-- Tooltip -->\r\n    <div\r\n      class=\"absolute right-16 bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n      取消\r\n    </div>\r\n  </button>\r\n\r\n  <!-- Copy to New Form Button (only show when locked) -->\r\n  <button *ngIf=\"listFormItem?.CIsLock\"\r\n    class=\"w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center group\"\r\n    (click)=\"copyToNewForm()\" [disabled]=\"isSubmitting\" title=\"複製到新表單\">\r\n    <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n        d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\">\r\n      </path>\r\n    </svg>\r\n\r\n    <!-- Tooltip -->\r\n    <div\r\n      class=\"absolute right-16 bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n      複製到新表單\r\n    </div>\r\n  </button>\r\n\r\n  <!-- Save Button -->\r\n  <button\r\n    class=\"w-14 h-14 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-full shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center group disabled:opacity-50 disabled:cursor-not-allowed relative\"\r\n    [class.animate-pulse]=\"isSubmitting\" (click)=\"onSubmit()\"\r\n    [disabled]=\"((listFormItem?.CIsLock ?? false) || isSubmitting)\" title=\"儲存變更\">\r\n\r\n    <!-- Loading Spinner -->\r\n    <svg *ngIf=\"isSubmitting\" class=\"w-6 h-6 animate-spin\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n        d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\">\r\n      </path>\r\n    </svg>\r\n\r\n    <!-- Save Icon -->\r\n    <svg *ngIf=\"!isSubmitting\" class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"></path>\r\n    </svg>\r\n\r\n    <!-- Tooltip -->\r\n    <div\r\n      class=\"absolute right-16 bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap\">\r\n      {{ isSubmitting ? '儲存中...' : '儲存變更' }}\r\n    </div>\r\n  </button>\r\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAI1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASA,YAAY,IAAIC,eAAe,QAAQ,8BAA8B;AAC9E,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAAuBC,MAAM,QAAQ,uCAAuC;AAC5E,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;;;;ICiDnDC,EAAA,CAAAC,cAAA,iBAEuG;IAArGD,EAAA,CAAAE,UAAA,mBAAAC,8FAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,2BAAA,EAA6B;IAAA,EAAC;IACvCT,EAAA,CAAAU,MAAA,mDACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;;IALXX,EADF,CAAAC,cAAA,cAA0F,aAC/C;IACvCD,EAAA,CAAAY,UAAA,IAAAC,qEAAA,qBAEuG;IAGvGb,EAAA,CAAAC,cAAA,iBAE0B;IAAxBD,EAAA,CAAAE,UAAA,mBAAAY,qFAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAW,GAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,WAAA,EAAa;IAAA,EAAC;IACvBhB,EAAA,CAAAU,MAAA,iCACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAIPX,EADF,CAAAC,cAAA,cAA4E,iBAGrC;IAAnCD,EAAA,CAAAE,UAAA,mBAAAe,qFAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAW,GAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,SAAA,EAAW;IAAA,EAAC;;IACrBlB,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAmB,SAAA,eAEO;IAEXnB,EADE,CAAAW,YAAA,EAAM,EACC;;IACTX,EAAA,CAAAC,cAAA,iBAEuC;IAArCD,EAAA,CAAAE,UAAA,mBAAAkB,qFAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAW,GAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,WAAA,EAAa;IAAA,EAAC;;IACvBrB,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAmB,SAAA,gBACO;IAEXnB,EADE,CAAAW,YAAA,EAAM,EACC;;IACTX,EAAA,CAAAC,cAAA,kBAEkD;IAAhDD,EAAA,CAAAE,UAAA,mBAAAoB,sFAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAW,GAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiB,oBAAA,EAAsB;IAAA,EAAC;;IAChCvB,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAmB,SAAA,gBAEO;IAIfnB,EAHM,CAAAW,YAAA,EAAM,EACC,EACL,EACF;;IACNX,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAU,MAAA,gKACF;IACFV,EADE,CAAAW,YAAA,EAAM,EACF;;;;IA1CyCX,EAAA,CAAAwB,SAAA,GAA0D;IAA1DxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAoB,sBAAA,KAAApB,MAAA,CAAAqB,kBAAA,CAAAC,MAAA,CAA0D;;;;;IA4E/F5B,EAAA,CAAAC,cAAA,cAC2F;;IACzFD,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAmB,SAAA,eAAgG;IAEpGnB,EADE,CAAAW,YAAA,EAAM,EACF;;;;;IACNX,EAAA,CAAAC,cAAA,cAC4F;;IAC1FD,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAmB,SAAA,eAEO;IAEXnB,EADE,CAAAW,YAAA,EAAM,EACF;;;;;;IAUNX,EAAA,CAAAC,cAAA,iBAEgD;IAA9CD,EAAA,CAAAE,UAAA,mBAAA2B,wGAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAA0B,GAAA;MAAA,MAAAC,MAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,KAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2B,YAAA,CAAAF,MAAA,GAAmB,CAAC,CAAC;IAAA,EAAC;;IAC/B/B,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAmB,SAAA,eACO;IAEXnB,EADE,CAAAW,YAAA,EAAM,EACC;;;;;;IACTX,EAAA,CAAAC,cAAA,iBAEgD;IAA9CD,EAAA,CAAAE,UAAA,mBAAAgC,wGAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAAJ,MAAA,GAAA/B,EAAA,CAAAO,aAAA,GAAAyB,KAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2B,YAAA,CAAAF,MAAA,GAAmB,CAAC,CAAC;IAAA,EAAC;;IAC/B/B,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAmB,SAAA,eAA8F;IAElGnB,EADE,CAAAW,YAAA,EAAM,EACC;;;;;IA2BLX,EAAA,CAAAmB,SAAA,eAC0F;;;;;;IAAxFnB,EAAA,CAAAyB,UAAA,QAAAzB,EAAA,CAAAoC,WAAA,OAAA9B,MAAA,CAAA+B,eAAA,CAAAC,cAAA,IAAAtC,EAAA,CAAAuC,aAAA,CAAkD;;;;;;IAgBpDvC,EAAA,CAAAC,cAAA,kBAE2E;IAAzED,EAAA,CAAAE,UAAA,mBAAAsC,oHAAAC,MAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsC,IAAA;MAAA,MAAAJ,cAAA,GAAAtC,EAAA,CAAAO,aAAA,IAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsC,SAAA,CAAAN,cAAA,CAAsB;MAAA,OAAAtC,EAAA,CAAAQ,WAAA,CAAEiC,MAAA,CAAAI,eAAA,EAAwB;IAAA,EAAC;;IAC1D7C,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAmB,SAAA,gBACO;IAEXnB,EADE,CAAAW,YAAA,EAAM,EACC;;;;;;IAETX,EAAA,CAAAC,cAAA,kBAE2E;IAAzED,EAAA,CAAAE,UAAA,mBAAA4C,oHAAAL,MAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAA2C,IAAA;MAAA,MAAAT,cAAA,GAAAtC,EAAA,CAAAO,aAAA,IAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA0C,SAAA,CAAAV,cAAA,CAAsB;MAAA,OAAAtC,EAAA,CAAAQ,WAAA,CAAEiC,MAAA,CAAAI,eAAA,EAAwB;IAAA,EAAC;;IAC1D7C,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAmB,SAAA,gBACO;IAEXnB,EADE,CAAAW,YAAA,EAAM,EACC;;;;;IAGTX,EAAA,CAAAC,cAAA,eACoH;IAClHD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAAiD,kBAAA,OAAAX,cAAA,CAAAY,iBAAA,mBAAAZ,cAAA,CAAAa,WAAA,CAAAvB,MAAA,MACF;;;;;;IAKA5B,EAAA,CAAAC,cAAA,kBAMiF;IAA/ED,EAAA,CAAAE,UAAA,mBAAAkD,2HAAA;MAAA,MAAAC,KAAA,GAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA,EAAAtB,KAAA;MAAA,MAAAM,cAAA,GAAAtC,EAAA,CAAAO,aAAA,IAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiD,cAAA,CAAAjB,cAAA,EAAAe,KAAA,CAA8B;IAAA,EAAC;IACxCrD,EAAA,CAAAmB,SAAA,eACiC;;IACnCnB,EAAA,CAAAW,YAAA,EAAS;;;;;;IAJPX,EAHA,CAAAwD,WAAA,oBAAAH,KAAA,MAAAf,cAAA,CAAAY,iBAAA,OAAoE,oBAAAG,KAAA,MAAAf,cAAA,CAAAY,iBAAA,OACA,WAAAG,KAAA,MAAAf,cAAA,CAAAY,iBAAA,OACT,kBAAAG,KAAA,MAAAf,cAAA,CAAAY,iBAAA,OACO;IACzBlD,EAAA,CAAAyB,UAAA,+CAAA4B,KAAA,8BAAqC;IAE5ErD,EAAA,CAAAwB,SAAA,EAA8B;IAA9BxB,EAAA,CAAAyB,UAAA,QAAAzB,EAAA,CAAAoC,WAAA,QAAAqB,YAAA,GAAAzD,EAAA,CAAAuC,aAAA,CAA8B;;;;;IATpCvC,EAAA,CAAAC,cAAA,eAA6F;IAC3FD,EAAA,CAAAY,UAAA,IAAA8C,kGAAA,uBAMiF;IAInF1D,EAAA,CAAAW,YAAA,EAAM;;;;IAVyBX,EAAA,CAAAwB,SAAA,EAA4B;IAA5BxB,EAAA,CAAAyB,UAAA,YAAAa,cAAA,CAAAa,WAAA,CAA4B;;;;;;IA/C3DnD,EAFF,CAAAC,cAAA,eAA4F,eAIlD;IAAtCD,EAAA,CAAAE,UAAA,mBAAAyD,wGAAA;MAAA3D,EAAA,CAAAI,aAAA,CAAAwD,IAAA;MAAA,MAAAtB,cAAA,GAAAtC,EAAA,CAAAO,aAAA,IAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiD,cAAA,CAAAjB,cAAA,CAA2B;IAAA,EAAC;IACrCtC,EAAA,CAAAY,UAAA,IAAAiD,wFAAA,mBAC0F;IAMtF7D,EAHJ,CAAAC,cAAA,eACwI,eAC9C,eACM;;IAC1FD,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAmB,SAAA,gBACmF;IAI3FnB,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;IAsBNX,EAnBA,CAAAY,UAAA,IAAAkD,2FAAA,sBAE2E,IAAAC,2FAAA,sBASA,KAAAC,yFAAA,mBASyC;IAGtHhE,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAY,UAAA,KAAAqD,yFAAA,mBAA6F;IAY/FjE,EAAA,CAAAW,YAAA,EAAM;;;;;IAtDoDX,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAA+B,eAAA,CAAAC,cAAA,EAAkC;IAgB/EtC,EAAA,CAAAwB,SAAA,GAAwC;IAAxCxB,EAAA,CAAAyB,UAAA,SAAAa,cAAA,CAAAa,WAAA,CAAAvB,MAAA,KAAwC;IASxC5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAyB,UAAA,SAAAa,cAAA,CAAAa,WAAA,CAAAvB,MAAA,KAAwC;IAU3C5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAyB,UAAA,SAAAa,cAAA,CAAAa,WAAA,CAAAvB,MAAA,KAAwC;IAO1C5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAyB,UAAA,SAAAa,cAAA,CAAAa,WAAA,CAAAvB,MAAA,KAAwC;;;;;IAchD5B,EAAA,CAAAC,cAAA,eACoJ;;IAClJD,EAAA,CAAAC,cAAA,eAAkF;IAChFD,EAAA,CAAAmB,SAAA,gBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAU,MAAA,iDAAO;IAC/BV,EAD+B,CAAAW,YAAA,EAAO,EAChC;;;;;IAmDAX,EAAA,CAAAC,cAAA,qBAA8D;IAC5DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAY;;;;IAFmCX,EAAA,CAAAyB,UAAA,UAAAyC,QAAA,CAAc;IAC3DlE,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAAmE,kBAAA,MAAAD,QAAA,CAAAE,KAAA,MACF;;;;;;IAqCFpE,EAFF,CAAAC,cAAA,eACuG,eACzE;IAG1BD,EAFA,CAAAmB,SAAA,eACuB,eAGjB;IACRnB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,iBAG0F;IAAxFD,EAAA,CAAAE,UAAA,kBAAAmE,gHAAA5B,MAAA;MAAA,MAAA6B,KAAA,GAAAtE,EAAA,CAAAI,aAAA,CAAAmE,IAAA,EAAAvC,KAAA;MAAA,MAAAM,cAAA,GAAAtC,EAAA,CAAAO,aAAA,IAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAkE,UAAA,CAAA/B,MAAA,EAAA6B,KAAA,EAAAhC,cAAA,CAAkC;IAAA,EAAC;IAH7CtC,EAAA,CAAAW,YAAA,EAG0F;IAC1FX,EAAA,CAAAC,cAAA,kBAE6F;IAA3FD,EAAA,CAAAE,UAAA,mBAAAuE,kHAAA;MAAA,MAAAC,WAAA,GAAA1E,EAAA,CAAAI,aAAA,CAAAmE,IAAA,EAAA5B,SAAA;MAAA,MAAAL,cAAA,GAAAtC,EAAA,CAAAO,aAAA,IAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqE,WAAA,CAAAD,WAAA,CAAAE,EAAA,EAAAtC,cAAA,CAAoC;IAAA,EAAC;;IAC9CtC,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAmB,SAAA,gBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAU,MAAA,iCACF;IACFV,EADE,CAAAW,YAAA,EAAS,EACL;;;;;;;IAnBAX,EAAA,CAAAwB,SAAA,GAAoB;IAApBxB,EAAA,CAAAyB,UAAA,QAAAiD,WAAA,CAAAG,IAAA,EAAA7E,EAAA,CAAAuC,aAAA,CAAoB;IAOYvC,EAAA,CAAAwB,SAAA,GAAsB;IACZxB,EADV,CAAAyB,UAAA,UAAAiD,WAAA,CAAAI,IAAA,CAAsB,cAAAC,QAAA,GAAAzE,MAAA,CAAA0E,YAAA,kBAAA1E,MAAA,CAAA0E,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SAC+B;IAGxC/E,EAAA,CAAAwB,SAAA,EAA2C;IAA3CxB,EAAA,CAAAyB,UAAA,cAAA0D,QAAA,GAAA7E,MAAA,CAAA0E,YAAA,kBAAA1E,MAAA,CAAA0E,YAAA,CAAAC,OAAA,cAAAE,QAAA,KAAAD,SAAA,GAAAC,QAAA,SAA2C;;;;;IAhBhGnF,EAAA,CAAAC,cAAA,eAA+F;IAC7FD,EAAA,CAAAY,UAAA,IAAAwE,yFAAA,mBACuG;IAuBzGpF,EAAA,CAAAW,YAAA,EAAM;;;;IAxBqBX,EAAA,CAAAwB,SAAA,EAA6B;IAA7BxB,EAAA,CAAAyB,UAAA,YAAAa,cAAA,CAAA+C,YAAA,CAA6B;;;;;IA6BtDrF,EAFF,CAAAC,cAAA,eAC6G,iBACpD;IAAAD,EAAA,CAAAU,MAAA,qCAAK;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACpEX,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAmB,SAAA,eACmD;;IACnDnB,EAAA,CAAAmB,SAAA,eAEM;IAEVnB,EADE,CAAAW,YAAA,EAAM,EACF;;;;IALAX,EAAA,CAAAwB,SAAA,GAAgD;IAAhDxB,EAAA,CAAAyB,UAAA,QAAAzB,EAAA,CAAAoC,WAAA,OAAAE,cAAA,CAAAgD,cAAA,GAAAtF,EAAA,CAAAuC,aAAA,CAAgD;;;;;IAOtDvC,EAAA,CAAAC,cAAA,eAE2I;;IACzID,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAmB,SAAA,gBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAU,MAAA,2CAAM;IAC9BV,EAD8B,CAAAW,YAAA,EAAO,EAC/B;;;;;IAkCRX,EAAA,CAAAC,cAAA,eAAiE;;IAC/DD,EAAA,CAAAC,cAAA,eACsB;IACpBD,EAAA,CAAAmB,SAAA,gBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAU,MAAA,2CAAM;IAC5CV,EAD4C,CAAAW,YAAA,EAAO,EAC7C;;;;;;IAmBFX,EAAA,CAAAC,cAAA,uBAEkG;IADhGD,EAAA,CAAAuF,gBAAA,2BAAAC,qJAAA/C,MAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAqF,IAAA;MAAA,MAAAC,UAAA,GAAA1F,EAAA,CAAAO,aAAA,GAAAoC,SAAA;MAAA,MAAAL,cAAA,GAAAtC,EAAA,CAAAO,aAAA,IAAAoC,SAAA;MAAA3C,EAAA,CAAA2F,kBAAA,CAAArD,cAAA,CAAAsD,kBAAA,CAAAF,UAAA,GAAAjD,MAAA,MAAAH,cAAA,CAAAsD,kBAAA,CAAAF,UAAA,IAAAjD,MAAA;MAAA,OAAAzC,EAAA,CAAAQ,WAAA,CAAAiC,MAAA;IAAA,EAAoD;IACvCzC,EAAA,CAAAE,UAAA,2BAAAsF,qJAAA/C,MAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAqF,IAAA;MAAA,MAAAC,UAAA,GAAA1F,EAAA,CAAAO,aAAA,GAAAoC,SAAA;MAAA,MAAAL,cAAA,GAAAtC,EAAA,CAAAO,aAAA,IAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAuF,sBAAA,CAAApD,MAAA,EAAAiD,UAAA,EAAApD,cAAA,CAAmD;IAAA,EAAC;IACpFtC,EAAA,CAAAW,YAAA,EAAc;;;;;;IAFZX,EAAA,CAAA8F,gBAAA,YAAAxD,cAAA,CAAAsD,kBAAA,CAAAF,UAAA,EAAoD;IAAC1F,EAAA,CAAAyB,UAAA,aAAAnB,MAAA,CAAA0E,YAAA,kBAAA1E,MAAA,CAAA0E,YAAA,CAAAC,OAAA,CAAkC;;;;;IAH3FjF,EAAA,CAAAC,cAAA,iBACgG;IAC9FD,EAAA,CAAAY,UAAA,IAAAmF,+GAAA,2BAEkG;IAElG/F,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAU,MAAA,GAAY;IAC1CV,EAD0C,CAAAW,YAAA,EAAO,EACzC;;;;;IALQX,EAAA,CAAAwB,SAAA,EAAoC;IAApCxB,EAAA,CAAAyB,UAAA,SAAAa,cAAA,CAAAsD,kBAAA,CAAoC;IAItB5F,EAAA,CAAAwB,SAAA,GAAY;IAAZxB,EAAA,CAAAgG,iBAAA,CAAAN,UAAA,CAAY;;;;;IAR5C1F,EAAA,CAAAC,cAAA,eACoF;IAClFD,EAAA,CAAAY,UAAA,IAAAqF,iGAAA,qBACgG;IAOlGjG,EAAA,CAAAW,YAAA,EAAM;;;;IARsBX,EAAA,CAAAwB,SAAA,EAAqB;IAArBxB,EAAA,CAAAyB,UAAA,YAAAnB,MAAA,CAAA4F,kBAAA,CAAqB;;;;;IAW/ClG,EAAA,CAAAC,cAAA,eAA8B;;IAC5BD,EAAA,CAAAC,cAAA,eACsB;IACpBD,EAAA,CAAAmB,SAAA,gBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAU,MAAA,2CAAM;IAC5CV,EAD4C,CAAAW,YAAA,EAAO,EAC7C;;;;;IA9BRX,EAFF,CAAAC,cAAA,eACkD,cACF;;IAC5CD,EAAA,CAAAC,cAAA,eAA2F;IACzFD,EAAA,CAAAmB,SAAA,gBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAU,MAAA,+BAAI;IAChDV,EADgD,CAAAW,YAAA,EAAK,EAC/C;IAcNX,EAZA,CAAAY,UAAA,IAAAuF,yFAAA,mBACoF,IAAAC,iGAAA,gCAAApG,EAAA,CAAAqG,sBAAA,CAWtD;IAWhCrG,EAAA,CAAAW,YAAA,EAAM;;;;;IAtBDX,EAAA,CAAAwB,SAAA,GAA2D;IAAAxB,EAA3D,CAAAyB,UAAA,SAAAnB,MAAA,CAAA4F,kBAAA,IAAA5F,MAAA,CAAA4F,kBAAA,CAAAtE,MAAA,KAA2D,aAAA0E,mBAAA,CAAoB;;;;;;IAjRhFtG,EANR,CAAAC,cAAA,cAAkD,cACG,cAGtB,cACF,aACoB;;IACvCD,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAmB,SAAA,eAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAU,MAAA,2CAAM;IAC3DV,EAD2D,CAAAW,YAAA,EAAQ,EAC7D;IAgENX,EA9DA,CAAAY,UAAA,IAAA2F,kFAAA,mBAA4F,KAAAC,mFAAA,kBA+DwD;IASxJxG,EADE,CAAAW,YAAA,EAAM,EACF;IAKFX,EAFJ,CAAAC,cAAA,eAA2B,eACF,eACyB;;IAC5CD,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAmB,SAAA,gBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,iBAAmD;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IACzDV,EADyD,CAAAW,YAAA,EAAQ,EAC3D;IAKFX,EAFJ,CAAAC,cAAA,eAAuB,eACF,iBAEsC;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAEjEX,EADF,CAAAC,cAAA,eAA0F,gBAEO;IAC7FD,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,kBAGgD;IADtCD,EAAA,CAAAuF,gBAAA,2BAAAkB,6GAAAhE,MAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsG,GAAA;MAAA,MAAApE,cAAA,GAAAtC,EAAA,CAAAO,aAAA,GAAAoC,SAAA;MAAA3C,EAAA,CAAA2F,kBAAA,CAAArD,cAAA,CAAAqE,SAAA,EAAAlE,MAAA,MAAAH,cAAA,CAAAqE,SAAA,GAAAlE,MAAA;MAAA,OAAAzC,EAAA,CAAAQ,WAAA,CAAAiC,MAAA;IAAA,EAAmC;IAGjDzC,EALI,CAAAW,YAAA,EAGgD,EAC5C,EACF;IAGJX,EADF,CAAAC,cAAA,eAAmB,iBAEsC;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAEjEX,EADF,CAAAC,cAAA,gBAAsB,kBAIyE;IADhED,EAAA,CAAAuF,gBAAA,2BAAAqB,6GAAAnE,MAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsG,GAAA;MAAA,MAAApE,cAAA,GAAAtC,EAAA,CAAAO,aAAA,GAAAoC,SAAA;MAAA3C,EAAA,CAAA2F,kBAAA,CAAArD,cAAA,CAAAuE,cAAA,EAAApE,MAAA,MAAAH,cAAA,CAAAuE,cAAA,GAAApE,MAAA;MAAA,OAAAzC,EAAA,CAAAQ,WAAA,CAAAiC,MAAA;IAAA,EAAwC;IAGzEzC,EALI,CAAAW,YAAA,EAG6F,EACzF,EACF;IAGJX,EADF,CAAAC,cAAA,eAAmB,iBAEsC;IAAAD,EAAA,CAAAU,MAAA,kCAAM;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACrEX,EAAA,CAAAC,cAAA,sBAG8C;IAHSD,EAAA,CAAAuF,gBAAA,2BAAAuB,iHAAArE,MAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsG,GAAA;MAAA,MAAApE,cAAA,GAAAtC,EAAA,CAAAO,aAAA,GAAAoC,SAAA;MAAA3C,EAAA,CAAA2F,kBAAA,CAAArD,cAAA,CAAAyE,eAAA,EAAAtE,MAAA,MAAAH,cAAA,CAAAyE,eAAA,GAAAtE,MAAA;MAAA,OAAAzC,EAAA,CAAAQ,WAAA,CAAAiC,MAAA;IAAA,EAAyC;IAE9FzC,EAAA,CAAAE,UAAA,4BAAA8G,kHAAA;MAAAhH,EAAA,CAAAI,aAAA,CAAAsG,GAAA;MAAA,MAAApE,cAAA,GAAAtC,EAAA,CAAAO,aAAA,GAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAkBF,MAAA,CAAA2G,mBAAA,CAAA3E,cAAA,CAAgC;IAAA,EAAC;IAEnDtC,EAAA,CAAAY,UAAA,KAAAsG,yFAAA,yBAA8D;IAOxElH,EAJQ,CAAAW,YAAA,EAAY,EACR,EACF,EACF,EACF;IAKFX,EAFJ,CAAAC,cAAA,eAA2B,eACF,cACoB;;IACvCD,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAmB,SAAA,iBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,iBAAmD;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IACzDV,EADyD,CAAAW,YAAA,EAAQ,EAC3D;IAGNX,EAAA,CAAAC,cAAA,mBAEiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAiH,sGAAA;MAAAnH,EAAA,CAAAI,aAAA,CAAAsG,GAAA;MAAA,MAAAU,aAAA,GAAApH,EAAA,CAAAqH,WAAA;MAAA,OAAArH,EAAA,CAAAQ,WAAA,CAAS4G,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;;IAC9DtH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAmB,SAAA,iBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,kDAAO;IACfV,EADe,CAAAW,YAAA,EAAO,EACb;IACTX,EAAA,CAAAC,cAAA,qBAC4C;IADCD,EAAA,CAAAE,UAAA,oBAAAqH,sGAAA9E,MAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsG,GAAA;MAAA,MAAApE,cAAA,GAAAtC,EAAA,CAAAO,aAAA,GAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAkH,WAAA,CAAA/E,MAAA,EAAAH,cAAA,CAAgC;IAAA,EAAC;IAAxFtC,EAAA,CAAAW,YAAA,EAC4C;IA2C5CX,EAxCA,CAAAY,UAAA,KAAA6G,mFAAA,mBAA+F,KAAAC,mFAAA,mBA6Bc,KAAAC,mFAAA,mBAa8B;IAUjJ3H,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;IAKFX,EAFJ,CAAAC,cAAA,gBAAkB,gBACM,gBAC4B;IAC9CD,EAAA,CAAAmB,SAAA,gBAAmD;IACrDnB,EAAA,CAAAW,YAAA,EAAM;IAEJX,EADF,CAAAC,cAAA,gBAAkD,iBACM;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAGhEV,EAHgE,CAAAW,YAAA,EAAO,EAC7D,EACF,EACF;IAGFX,EAFJ,CAAAC,cAAA,gBAAmD,gBACa,eACd;;IAC5CD,EAAA,CAAAC,cAAA,gBAAyF;IACvFD,EAAA,CAAAmB,SAAA,iBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAC9CV,EAD8C,CAAAW,YAAA,EAAK,EAC7C;IACNX,EAAA,CAAAC,cAAA,kCAI2C;IADzCD,EAAA,CAAAE,UAAA,6BAAA0H,+HAAAnF,MAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsG,GAAA;MAAA,MAAApE,cAAA,GAAAtC,EAAA,CAAAO,aAAA,GAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAmBF,MAAA,CAAAuH,0BAAA,CAA2BvH,MAAA,CAAAwH,qBAAA,CAAArF,MAAA,CAA6B,EAAAH,cAAA,CAAc;IAAA,EAAC;IAE5FtC,EAAA,CAAAW,YAAA,EAAwB;IAGxBX,EAAA,CAAAY,UAAA,KAAAmH,mFAAA,mBAAiE;IASnE/H,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAY,UAAA,KAAAoH,mFAAA,mBACkD;IAmCtDhI,EADE,CAAAW,YAAA,EAAM,EACF;;;;;;;;;;;IAhSQX,EAAA,CAAAwB,SAAA,GAAmE;IAAnExB,EAAA,CAAAyB,UAAA,SAAAa,cAAA,CAAAa,WAAA,IAAAb,cAAA,CAAAa,WAAA,CAAAvB,MAAA,KAAmE;IA8DnE5B,EAAA,CAAAwB,SAAA,EAAsE;IAAtExB,EAAA,CAAAyB,UAAA,UAAAa,cAAA,CAAAa,WAAA,IAAAb,cAAA,CAAAa,WAAA,CAAAvB,MAAA,OAAsE;IA2BjE5B,EAAA,CAAAwB,SAAA,IAA0B;IAA1BxB,EAAA,CAAAyB,UAAA,uBAAAM,MAAA,CAA0B;IAK7B/B,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAiI,kBAAA,MAAA3F,cAAA,CAAA4F,KAAA,OAAA5F,cAAA,CAAA6F,KAAA,OAAA7F,cAAA,CAAA8F,SAAA,OACF;IACmBpI,EAAA,CAAAwB,SAAA,EAAyB;IAAzBxB,EAAA,CAAAyB,UAAA,sBAAAM,MAAA,CAAyB;IAElC/B,EAAA,CAAA8F,gBAAA,YAAAxD,cAAA,CAAAqE,SAAA,CAAmC;IAC3C3G,EAAA,CAAAyB,UAAA,cAAAsD,QAAA,GAAAzE,MAAA,CAAA0E,YAAA,kBAAA1E,MAAA,CAAA0E,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SAA2C;IAKxC/E,EAAA,CAAAwB,SAAA,GAA+B;IAA/BxB,EAAA,CAAAyB,UAAA,4BAAAM,MAAA,CAA+B;IAGf/B,EAAA,CAAAwB,SAAA,GAA8B;IAA9BxB,EAAA,CAAAyB,UAAA,2BAAAM,MAAA,CAA8B;IAEtB/B,EAAA,CAAA8F,gBAAA,YAAAxD,cAAA,CAAAuE,cAAA,CAAwC;IACnE7G,EAAA,CAAAyB,UAAA,aAAAa,cAAA,CAAAyE,eAAA,CAAAsB,KAAA,YAAAC,QAAA,GAAAhI,MAAA,CAAA0E,YAAA,kBAAA1E,MAAA,CAAA0E,YAAA,CAAAC,OAAA,cAAAqD,QAAA,KAAApD,SAAA,GAAAoD,QAAA,UAAwF;IAKrFtI,EAAA,CAAAwB,SAAA,GAAuB;IAAvBxB,EAAA,CAAAyB,UAAA,oBAAAM,MAAA,CAAuB;IAEE/B,EAAA,CAAAwB,SAAA,GAAsB;IAAtBxB,EAAA,CAAAyB,UAAA,mBAAAM,MAAA,CAAsB;IAAC/B,EAAA,CAAA8F,gBAAA,YAAAxD,cAAA,CAAAyE,eAAA,CAAyC;IAG9F/G,EAAA,CAAAyB,UAAA,cAAA8G,QAAA,GAAAjI,MAAA,CAAA0E,YAAA,kBAAA1E,MAAA,CAAA0E,YAAA,CAAAC,OAAA,cAAAsD,QAAA,KAAArD,SAAA,GAAAqD,QAAA,SAA2C;IACfvI,EAAA,CAAAwB,SAAA,EAAiB;IAAjBxB,EAAA,CAAAyB,UAAA,YAAAnB,MAAA,CAAAkI,cAAA,CAAiB;IAwBjDxI,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAyB,UAAA,aAAAnB,MAAA,CAAA0E,YAAA,kBAAA1E,MAAA,CAAA0E,YAAA,CAAAC,OAAA,CAAkC;IAY9BjF,EAAA,CAAAwB,SAAA,GAAqE;IAArExB,EAAA,CAAAyB,UAAA,SAAAa,cAAA,CAAA+C,YAAA,IAAA/C,cAAA,CAAA+C,YAAA,CAAAzD,MAAA,KAAqE;IA6BxE5B,EAAA,CAAAwB,SAAA,EAAwG;IAAxGxB,EAAA,CAAAyB,UAAA,SAAAa,cAAA,CAAAgD,cAAA,MAAAhD,cAAA,CAAA+C,YAAA,IAAA/C,cAAA,CAAA+C,YAAA,CAAAzD,MAAA,QAAwG;IAYxG5B,EAAA,CAAAwB,SAAA,EAAyG;IAAzGxB,EAAA,CAAAyB,UAAA,UAAAa,cAAA,CAAAgD,cAAA,MAAAhD,cAAA,CAAA+C,YAAA,IAAA/C,cAAA,CAAA+C,YAAA,CAAAzD,MAAA,QAAyG;IAkCvF5B,EAAA,CAAAwB,SAAA,IAA6B;IAInCxB,EAJM,CAAAyB,UAAA,iBAAAnB,MAAA,CAAAmI,YAAA,CAA6B,6DAA0B,cAAAC,QAAA,GAAApI,MAAA,CAAA0E,YAAA,kBAAA1E,MAAA,CAAA0E,YAAA,CAAAC,OAAA,cAAAyD,QAAA,KAAAxD,SAAA,GAAAwD,QAAA,SACjC,0BAA0B,YAAApG,cAAA,CAAAqG,wBAAA,CACrB,0BAER;IAIX3I,EAAA,CAAAwB,SAAA,EAAgC;IAAhCxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAsI,aAAA,CAAAhH,MAAA,OAAgC;IAa9D5B,EAAA,CAAAwB,SAAA,EAA6C;IAA7CxB,EAAA,CAAAyB,UAAA,SAAAa,cAAA,CAAAyE,eAAA,CAAAsB,KAAA,OAA6C;;;;;;IAxVxDrI,EAAA,CAAA6I,uBAAA,GAA8E;IAQpE7I,EAPR,CAAAC,cAAA,cAC2H,cAE9B,aAC1C,cACJ,iBAIyD;IAA9FD,EAAA,CAAAE,UAAA,mBAAA4I,8FAAA;MAAA,MAAAxG,cAAA,GAAAtC,EAAA,CAAAI,aAAA,CAAA2I,GAAA,EAAApG,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA0I,kBAAA,CAAA1G,cAAA,CAA+B;IAAA,EAAC;;IACzCtC,EAAA,CAAAC,cAAA,cAEsB;IACpBD,EAAA,CAAAmB,SAAA,eAAgG;IAEpGnB,EADE,CAAAW,YAAA,EAAM,EACC;;IAETX,EAAA,CAAAC,cAAA,cACyG;IACvGD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAEJX,EADF,CAAAC,cAAA,eAAoB,cAC8B;IAC9CD,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAU,MAAA,IAAiB;IAEtDV,EAFsD,CAAAW,YAAA,EAAI,EAClD,EACF;IAGJX,EAFF,CAAAC,cAAA,eAAyC,cAEE;IAOvCD,EANA,CAAAY,UAAA,KAAAqI,4EAAA,kBAC2F,KAAAC,4EAAA,kBAMC;IAO9FlJ,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGPX,EAAA,CAAAC,cAAA,eAAyC;IASvCD,EARA,CAAAY,UAAA,KAAAuI,+EAAA,qBAEgD,KAAAC,+EAAA,qBAQA;IAQxDpJ,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;IAGNX,EAAA,CAAAY,UAAA,KAAAyI,4EAAA,oBAAkD;IAgTpDrJ,EAAA,CAAAW,YAAA,EAAM;;;;;;;IA3XDX,EAAA,CAAAwB,SAAA,EAAyB;IAAzBxB,EAAA,CAAAyB,UAAA,sBAAAM,MAAA,CAAyB;IASsB/B,EAAA,CAAAwB,SAAA,GAAmD;IAAnDxB,EAAA,CAAAyB,UAAA,UAAAa,cAAA,CAAAgH,WAAA,2DAAmD;IAE3FtJ,EAAA,CAAAwB,SAAA,EAA4C;IAA5CxB,EAAA,CAAAwD,WAAA,eAAAlB,cAAA,CAAAgH,WAAA,CAA4C;IAQ9CtJ,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAmE,kBAAA,MAAApC,MAAA,UACF;IAGI/B,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAiI,kBAAA,MAAA3F,cAAA,CAAA4F,KAAA,OAAA5F,cAAA,CAAA6F,KAAA,OAAA7F,cAAA,CAAA8F,SAAA,MACF;IACiCpI,EAAA,CAAAwB,SAAA,GAAiB;IAAjBxB,EAAA,CAAAmE,kBAAA,+BAAApC,MAAA,SAAiB;IAM5C/B,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAiJ,eAAA,CAAAjH,cAAA,EAAkC;IAMlCtC,EAAA,CAAAwB,SAAA,EAAmC;IAAnCxB,EAAA,CAAAyB,UAAA,UAAAnB,MAAA,CAAAiJ,eAAA,CAAAjH,cAAA,EAAmC;IAYzCtC,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAmE,kBAAA,OAAA7B,cAAA,CAAAyE,eAAA,kBAAAzE,cAAA,CAAAyE,eAAA,CAAA3C,KAAA,+BACF;IAIWpE,EAAA,CAAAwB,SAAA,GAAa;IAAbxB,EAAA,CAAAyB,UAAA,SAAAM,MAAA,KAAa;IAQb/B,EAAA,CAAAwB,SAAA,EAAyC;IAAzCxB,EAAA,CAAAyB,UAAA,SAAAM,MAAA,GAAAzB,MAAA,CAAAqB,kBAAA,CAAAC,MAAA,KAAyC;IAaxC5B,EAAA,CAAAwB,SAAA,EAA8B;IAA9BxB,EAAA,CAAAyB,UAAA,UAAAa,cAAA,CAAAgH,WAAA,CAA8B;;;;;IA0WpDtJ,EAAA,CAAAmB,SAAA,eAC0F;;;;;;IAAxFnB,EAAA,CAAAyB,UAAA,QAAAzB,EAAA,CAAAoC,WAAA,OAAA9B,MAAA,CAAA+B,eAAA,CAAAmH,eAAA,IAAAxJ,EAAA,CAAAuC,aAAA,CAAkD;;;;;;IAGpDvC,EAAA,CAAAC,cAAA,kBAE8D;IAA5DD,EAAA,CAAAE,UAAA,mBAAAuJ,6GAAA;MAAAzJ,EAAA,CAAAI,aAAA,CAAAsJ,IAAA;MAAA,MAAAF,eAAA,GAAAxJ,EAAA,CAAAO,aAAA,IAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqJ,cAAA,CAAAH,eAAA,CAA2B;IAAA,EAAC;;IACrCxJ,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAmB,SAAA,gBAAmG;IAEvGnB,EADE,CAAAW,YAAA,EAAM,EACC;;;;;;IAETX,EAAA,CAAAC,cAAA,kBAE8D;IAA5DD,EAAA,CAAAE,UAAA,mBAAA0J,6GAAA;MAAA5J,EAAA,CAAAI,aAAA,CAAAyJ,IAAA;MAAA,MAAAL,eAAA,GAAAxJ,EAAA,CAAAO,aAAA,IAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwJ,cAAA,CAAAN,eAAA,CAA2B;IAAA,EAAC;;IACrCxJ,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAmB,SAAA,gBAAgG;IAEpGnB,EADE,CAAAW,YAAA,EAAM,EACC;;;;;IAMTX,EAFF,CAAAC,cAAA,eACqJ,cAC1G;;IACvCD,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAmB,SAAA,eAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAU,MAAA,GACE;IAExCV,EAFwC,CAAAW,YAAA,EAAO,EACvC,EACF;;;;IAHgCX,EAAA,CAAAwB,SAAA,GACE;IADFxB,EAAA,CAAAiD,kBAAA,MAAAuG,eAAA,CAAAtG,iBAAA,mBAAAsG,eAAA,CAAArG,WAAA,CAAAvB,MAAA,KACE;;;;;;IAoBpC5B,EAAA,CAAAC,cAAA,kBAOkF;IAAhFD,EAAA,CAAAE,UAAA,mBAAA6J,oHAAA;MAAA,MAAAC,KAAA,GAAAhK,EAAA,CAAAI,aAAA,CAAA6J,IAAA,EAAAjI,KAAA;MAAA,MAAAwH,eAAA,GAAAxJ,EAAA,CAAAO,aAAA,IAAAoC,SAAA;MAAA,OAAA3C,EAAA,CAAAQ,WAAA,CAAAgJ,eAAA,CAAAtG,iBAAA,GAAA8G,KAAA;IAAA,EAA2C;IAC3ChK,EAAA,CAAAmB,SAAA,eAAyG;;IAC3GnB,EAAA,CAAAW,YAAA,EAAS;;;;;;IAHPX,EAJA,CAAAwD,WAAA,iBAAAwG,KAAA,MAAAR,eAAA,CAAAtG,iBAAA,OAAiE,oBAAA8G,KAAA,MAAAR,eAAA,CAAAtG,iBAAA,OACG,WAAA8G,KAAA,MAAAR,eAAA,CAAAtG,iBAAA,OACT,eAAA8G,KAAA,MAAAR,eAAA,CAAAtG,iBAAA,OACI,oBAAA8G,KAAA,MAAAR,eAAA,CAAAtG,iBAAA,OACK;IACxBlD,EAAA,CAAAyB,UAAA,mCAAAuI,KAAA,8BAAmC;IACLhK,EAAA,CAAAwB,SAAA,EAA8B;IAA9BxB,EAAA,CAAAyB,UAAA,QAAAzB,EAAA,CAAAoC,WAAA,QAAA8H,YAAA,GAAAlK,EAAA,CAAAuC,aAAA,CAA8B;;;;;IAT5GvC,EAFF,CAAAC,cAAA,eAC8I,eACtE;IACpED,EAAA,CAAAY,UAAA,IAAAuJ,2FAAA,uBAOkF;IAItFnK,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAX2BX,EAAA,CAAAwB,SAAA,GAA4B;IAA5BxB,EAAA,CAAAyB,UAAA,YAAA+H,eAAA,CAAArG,WAAA,CAA4B;;;;;;IAtEjEnD,EAAA,CAAAC,cAAA,eAEiG;IAAxDD,EAAvC,CAAAE,UAAA,mBAAAkK,iGAAA;MAAApK,EAAA,CAAAI,aAAA,CAAAiK,IAAA;MAAA,MAAAb,eAAA,GAAAxJ,EAAA,CAAAO,aAAA,GAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgK,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC,qBAAAe,mGAAA9H,MAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAiK,IAAA;MAAA,MAAAb,eAAA,GAAAxJ,EAAA,CAAAO,aAAA,GAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAkK,SAAA,CAAA/H,MAAA,EAAA+G,eAAA,CAA8B;IAAA,EAAC;IAGjFxJ,EAAA,CAAAC,cAAA,kBAEkE;IAAhED,EAAA,CAAAE,UAAA,mBAAAuK,oGAAA;MAAAzK,EAAA,CAAAI,aAAA,CAAAiK,IAAA;MAAA,MAAAb,eAAA,GAAAxJ,EAAA,CAAAO,aAAA,GAAAoC,SAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgK,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC;;IACtCxJ,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAmB,SAAA,gBAAwG;IAE5GnB,EADE,CAAAW,YAAA,EAAM,EACC;;IAGTX,EAAA,CAAAC,cAAA,eACqC;IAAnCD,EAAA,CAAAE,UAAA,mBAAAwK,iGAAAjI,MAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAiK,IAAA;MAAA,OAAArK,EAAA,CAAAQ,WAAA,CAASiC,MAAA,CAAAI,eAAA,EAAwB;IAAA,EAAC;IAGlC7C,EAAA,CAAAC,cAAA,eAAgF;IAa9ED,EAZA,CAAAY,UAAA,IAAA+J,iFAAA,mBAC0F,IAAAC,oFAAA,sBAK5B,IAAAC,oFAAA,sBAQA;IAKhE7K,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAY,UAAA,IAAAkK,iFAAA,mBACqJ;IAenJ9K,EAFF,CAAAC,cAAA,gBACkJ,cACvG;;IACvCD,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAmB,SAAA,gBACuE;IACzEnB,EAAA,CAAAW,YAAA,EAAM;;IACNX,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAqE;IAEnGV,EAFmG,CAAAW,YAAA,EAAO,EAClG,EACF;IAGNX,EAAA,CAAAY,UAAA,KAAAmK,kFAAA,mBAC8I;IAelJ/K,EADE,CAAAW,YAAA,EAAM,EACF;;;;;IA/DsDX,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAA+B,eAAA,CAAAmH,eAAA,EAAkC;IAG/ExJ,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAyB,UAAA,SAAA+H,eAAA,CAAArG,WAAA,IAAAqG,eAAA,CAAArG,WAAA,CAAAvB,MAAA,KAAmE;IAQnE5B,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAyB,UAAA,SAAA+H,eAAA,CAAArG,WAAA,IAAAqG,eAAA,CAAArG,WAAA,CAAAvB,MAAA,KAAmE;IAUxE5B,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAyB,UAAA,SAAA+H,eAAA,CAAArG,WAAA,IAAAqG,eAAA,CAAArG,WAAA,CAAAvB,MAAA,KAAmE;IAqB3C5B,EAAA,CAAAwB,SAAA,GAAqE;IAArExB,EAAA,CAAAiI,kBAAA,KAAAuB,eAAA,CAAAtB,KAAA,OAAAsB,eAAA,CAAArB,KAAA,OAAAqB,eAAA,CAAApB,SAAA,KAAqE;IAK7FpI,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAyB,UAAA,SAAA+H,eAAA,CAAArG,WAAA,IAAAqG,eAAA,CAAArG,WAAA,CAAAvB,MAAA,KAAmE;;;;;IApE/E5B,EAAA,CAAA6I,uBAAA,GAA8E;IAC5E7I,EAAA,CAAAY,UAAA,IAAAoK,2EAAA,oBAEiG;;;;;IAF3FhL,EAAA,CAAAwB,SAAA,EAA6B;IAA7BxB,EAAA,CAAAyB,UAAA,SAAA+H,eAAA,CAAAyB,WAAA,CAA6B;;;;;;IA2G/BjL,EAAA,CAAAC,cAAA,eAAsG;IACpGD,EAAA,CAAAmB,SAAA,eAAgG;IAClGnB,EAAA,CAAAW,YAAA,EAAM;;;;;;IAGNX,EAAA,CAAAC,cAAA,eAAkH;IAChHD,EAAA,CAAAmB,SAAA,gBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;;;;;IA+BRX,EAAA,CAAAC,cAAA,kBAE0D;IAAxDD,EAAA,CAAAE,UAAA,mBAAAgL,+FAAA;MAAAlL,EAAA,CAAAI,aAAA,CAAA+K,IAAA;MAAA,MAAA7K,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,2BAAA,EAA6B;IAAA,EAAC;;IACvCT,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAmB,SAAA,eAEO;IAEXnB,EADE,CAAAW,YAAA,EAAM,EACC;;;;;;IAhETX,EAJJ,CAAAC,cAAA,eAC8D,eAEtC,kBAKoE;IAFjDD,EAAA,CAAAE,UAAA,mBAAAkL,qFAAA;MAAApL,EAAA,CAAAI,aAAA,CAAAiL,IAAA;MAAA,MAAA/K,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgL,QAAA,EAAU;IAAA,EAAC;;IAKzDtL,EAAA,CAAAC,cAAA,eAAiF;IAG/ED,EAFA,CAAAmB,SAAA,gBACsF,gBAGA;IACxFnB,EAAA,CAAAW,YAAA,EAAM;IAQNX,EALA,CAAAY,UAAA,IAAA2K,uEAAA,mBAAsG,IAAAC,uEAAA,mBAKY;IAKpHxL,EAAA,CAAAW,YAAA,EAAS;;IAGTX,EAAA,CAAAC,cAAA,eACmI;IACjID,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAM,EACF;IAKJX,EAFF,CAAAC,cAAA,gBAAqC,mBAI0B;IAA3DD,EAAA,CAAAE,UAAA,mBAAAuL,sFAAA;MAAAzL,EAAA,CAAAI,aAAA,CAAAiL,IAAA;MAAA,MAAA/K,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoL,MAAA,EAAQ;IAAA,EAAC;;IAClB1L,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAmB,SAAA,gBAA6G;IAEjHnB,EADE,CAAAW,YAAA,EAAM,EACC;;IAGTX,EAAA,CAAAC,cAAA,mBAEuC;IAArCD,EAAA,CAAAE,UAAA,mBAAAyL,sFAAA;MAAA3L,EAAA,CAAAI,aAAA,CAAAiL,IAAA;MAAA,MAAA/K,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,WAAA,EAAa;IAAA,EAAC;;IACvBhB,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAmB,SAAA,iBAA2G;IAE/GnB,EADE,CAAAW,YAAA,EAAM,EACC;IAGTX,EAAA,CAAAY,UAAA,KAAAgL,sEAAA,sBAE0D;;IAS1D5L,EAAA,CAAAC,cAAA,mBAEoD;IAAlDD,EAAA,CAAAE,UAAA,mBAAA2L,sFAAA;MAAA7L,EAAA,CAAAI,aAAA,CAAAiL,IAAA;MAAA,MAAA/K,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiB,oBAAA,EAAsB;IAAA,EAAC;;IAChCvB,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAmB,SAAA,gBACuG;IAG7GnB,EAFI,CAAAW,YAAA,EAAM,EACC,EACL;;IAGNX,EAAA,CAAAC,cAAA,gBACoL;IAClLD,EAAA,CAAAU,MAAA,IACF;IACFV,EADE,CAAAW,YAAA,EAAM,EACF;;;;;IAhFAX,EAAA,CAAAwB,SAAA,GAAoC;IAApCxB,EAAA,CAAAwD,WAAA,kBAAAlD,MAAA,CAAAwL,YAAA,CAAoC;IAEpC9L,EAAA,CAAA+L,sBAAA,wCAAAzL,MAAA,CAAAoB,sBAAA,SAAApB,MAAA,CAAAqB,kBAAA,CAAAC,MAAA,wBAAqF;IADrF5B,EAAA,CAAAyB,UAAA,eAAAuK,OAAA,GAAA1L,MAAA,CAAA0E,YAAA,kBAAA1E,MAAA,CAAA0E,YAAA,CAAAC,OAAA,cAAA+G,OAAA,KAAA9G,SAAA,GAAA8G,OAAA,aAAA1L,MAAA,CAAAwL,YAAA,CAA6D;IAQzD9L,EAAA,CAAAwB,SAAA,GAA2D;;IAKzDxB,EAAA,CAAAwB,SAAA,EAAmB;IAAnBxB,EAAA,CAAAyB,UAAA,UAAAnB,MAAA,CAAAwL,YAAA,CAAmB;IAKnB9L,EAAA,CAAAwB,SAAA,EAAkB;IAAlBxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAwL,YAAA,CAAkB;IAUxB9L,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAmE,kBAAA,MAAA7D,MAAA,CAAA2L,qBAAA,QACF;IAQqBjM,EAAA,CAAAwB,SAAA,GAAyB;IAAzBxB,EAAA,CAAAyB,UAAA,aAAAnB,MAAA,CAAAwL,YAAA,CAAyB;IAgBrC9L,EAAA,CAAAwB,SAAA,GAA0D;IAA1DxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAoB,sBAAA,KAAApB,MAAA,CAAAqB,kBAAA,CAAAC,MAAA,CAA0D;IAwBnE5B,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAiI,kBAAA,MAAA3H,MAAA,CAAA2L,qBAAA,wBAAA3L,MAAA,CAAAoB,sBAAA,SAAApB,MAAA,CAAAqB,kBAAA,CAAAC,MAAA,YACF;;;;;;IAqBA5B,EAAA,CAAAC,cAAA,kBAEqE;IAAnED,EAAA,CAAAE,UAAA,mBAAAgM,wFAAA;MAAAlM,EAAA,CAAAI,aAAA,CAAA+L,IAAA;MAAA,MAAA7L,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8L,aAAA,EAAe;IAAA,EAAC;;IACzBpM,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAmB,SAAA,gBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;IAGNX,EAAA,CAAAC,cAAA,cAC8K;IAC5KD,EAAA,CAAAU,MAAA,6CACF;IACFV,EADE,CAAAW,YAAA,EAAM,EACC;;;;IAZmBX,EAAA,CAAAyB,UAAA,aAAAnB,MAAA,CAAAwL,YAAA,CAAyB;;;;;;IAqBnD9L,EAAA,CAAAC,cAAA,eAA6G;IAC3GD,EAAA,CAAAmB,SAAA,gBAEO;IACTnB,EAAA,CAAAW,YAAA,EAAM;;;;;;IAGNX,EAAA,CAAAC,cAAA,cAAiG;IAC/FD,EAAA,CAAAmB,SAAA,eAAgG;IAClGnB,EAAA,CAAAW,YAAA,EAAM;;;AD7rBV,OAAM,MAAO0L,4CAA6C,SAAQzM,aAAa;EAC7E0M,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B,EAC3BC,aAA2B;IAEnC,KAAK,CAACV,MAAM,CAAC;IAZL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAIvB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IACD;IACA,KAAAC,kBAAkB,GAAG,CACnB;MAAEjJ,KAAK,EAAE,KAAK;MAAEiE,KAAK,EAAEtI,aAAa,CAACuN;IAAG,CAAE,EAC1C;MAAElJ,KAAK,EAAE,KAAK;MAAEiE,KAAK,EAAEtI,aAAa,CAACwN;IAAG,CAAE,CAC3C;IAiBD,KAAA/E,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAEjE,KAAK,EAAE;KAClB,EACD;MACEiE,KAAK,EAAE,CAAC;MAAEjE,KAAK,EAAE;KAClB,EAAE;MACDiE,KAAK,EAAE,CAAC;MAAEjE,KAAK,EAAE;KAClB,CAAC;IACJ,KAAA8B,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAEjC,KAAA4F,YAAY,GAAY,KAAK;IAgD7B,KAAA0B,aAAa,GAA+B,EAAE;IAC9C,KAAA5H,kBAAkB,GAA+B,EAAE;IAEnD;IACA,KAAA6C,YAAY,GAAQ,EAAE,CAAC,CAAC;IA8OxB,KAAAzD,YAAY,GAA8B,IAAI;IAC9C,KAAAyI,KAAK,GAAY,IAAI;IAwFrB,KAAA9L,kBAAkB,GAAkC,EAAE;EAhatD;EAUA;EACA,IAAI+L,YAAYA,CAAA;IACd,MAAMC,MAAM,GAAG,IAAI,CAACN,kBAAkB,CAACO,IAAI,CAACD,MAAM,IAChDA,MAAM,CAACtF,KAAK,KAAK,IAAI,CAAC6E,iCAAiC,CAACE,WAAW,CACpE;IACD,OAAOO,MAAM,GAAG,QAAQA,MAAM,CAACvJ,KAAK,EAAE,GAAG,WAAW;EACtD;EACA;EACAyJ,cAAcA,CAACC,UAAyB;IACtC,IAAI,IAAI,CAACT,kBAAkB,CAACU,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAACtF,KAAK,KAAKyF,UAAU,CAAC,EAAE;MACvE,IAAI,CAACZ,iCAAiC,CAACE,WAAW,GAAGU,UAAU;MAC/D;MACA,IAAI,CAACZ,iCAAiC,CAACC,SAAS,GAAGW,UAAU;IAC/D;EACF;EAgBSE,QAAQA,CAAA;IACfC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9B,IAAI,CAAC1B,KAAK,CAAC2B,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrCJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEG,MAAM,CAAC;MACpC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAM3J,EAAE,GAAG0J,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAG5J,EAAE;QACrBqJ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACM,WAAW,CAAC;QAEpD,IAAI,IAAI,CAACA,WAAW,GAAG,CAAC,EAAE;UACxBP,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3D,IAAI,CAACO,iCAAiC,EAAE;QAC1C,CAAC,MAAM;UACL;UACAR,OAAO,CAACS,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAACF,WAAW,CAAC;UACvD,IAAI,CAAC/B,OAAO,CAACkC,YAAY,CAAC,iBAAiB,CAAC;UAC5C,IAAI,CAACjD,MAAM,EAAE;QACf;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACc,KAAK,CAACoC,WAAW,CAACR,SAAS,CAACQ,WAAW,IAAG;MAC7C,IAAIA,WAAW,CAAC,WAAW,CAAC,EAAE;QAC5B,MAAMC,SAAS,GAAG,CAACD,WAAW,CAAC,WAAW,CAAC;QAC3C,IAAI,CAACf,cAAc,CAACgB,SAAS,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAC,cAAcA,CAAC9G,KAAU,EAAE+G,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAChH,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOgH,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQA7H,WAAWA,CAAC8H,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAAClK,YAAY,CAACzD,MAAM,GAAG,CAAC,EAAE;UACxC2N,YAAY,CAAClK,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BT,EAAE,EAAE,IAAIqL,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBpL,IAAI,EAAE0K,IAAI,CAAC1K,IAAI,CAACqL,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BtL,IAAI,EAAEkL,SAAS;YACfK,SAAS,EAAE,IAAI,CAACxD,eAAe,CAACyD,gBAAgB,CAACb,IAAI,CAAC1K,IAAI,CAAC;YAC3DwL,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAAClK,YAAY,CAACkL,IAAI,CAAC;YAC7B3L,EAAE,EAAE,IAAIqL,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBpL,IAAI,EAAE0K,IAAI,CAAC1K,IAAI,CAACqL,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BtL,IAAI,EAAEkL,SAAS;YACfK,SAAS,EAAE,IAAI,CAACxD,eAAe,CAACyD,gBAAgB,CAACb,IAAI,CAAC1K,IAAI,CAAC;YAC3DwL,KAAK,EAAEd;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACpH,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEA1D,WAAWA,CAAC6L,SAAiB,EAAEjB,YAAiB;IAC9C,IAAIA,YAAY,CAAClK,YAAY,CAACzD,MAAM,EAAE;MACpC2N,YAAY,CAAClK,YAAY,GAAGkK,YAAY,CAAClK,YAAY,CAACoL,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC9L,EAAE,IAAI4L,SAAS,CAAC;IAC7F;EACF;EACAhM,UAAUA,CAAC8K,KAAU,EAAEtN,KAAa,EAAEuN,YAAiB;IACrD,IAAIoB,IAAI,GAAGpB,YAAY,CAAClK,YAAY,CAACrD,KAAK,CAAC,CAACsO,KAAK,CAACM,KAAK,CAAC,CAAC,EAAErB,YAAY,CAAClK,YAAY,CAACrD,KAAK,CAAC,CAACsO,KAAK,CAACO,IAAI,EAAEtB,YAAY,CAAClK,YAAY,CAACrD,KAAK,CAAC,CAACsO,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACG,MAAM,CAACpH,KAAK,GAAG,GAAG,GAAGkH,YAAY,CAAClK,YAAY,CAACrD,KAAK,CAAC,CAACoO,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEvB,YAAY,CAAClK,YAAY,CAACrD,KAAK,CAAC,CAACsO,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKvB,YAAY,CAAClK,YAAY,CAACrD,KAAK,CAAC,CAACsO,KAAK,GAAGS,OAAO;EAClD;EAEA;EACA/N,SAASA,CAACiO,WAAgB;IACxB,IAAIA,WAAW,CAAC9N,WAAW,IAAI8N,WAAW,CAAC9N,WAAW,CAACvB,MAAM,GAAG,CAAC,EAAE;MACjEqP,WAAW,CAAC/N,iBAAiB,GAAG,CAAC+N,WAAW,CAAC/N,iBAAiB,GAAG,CAAC,IAAI+N,WAAW,CAAC9N,WAAW,CAACvB,MAAM;IACtG;EACF;EAEAgB,SAASA,CAACqO,WAAgB;IACxB,IAAIA,WAAW,CAAC9N,WAAW,IAAI8N,WAAW,CAAC9N,WAAW,CAACvB,MAAM,GAAG,CAAC,EAAE;MACjEqP,WAAW,CAAC/N,iBAAiB,GAAG+N,WAAW,CAAC/N,iBAAiB,KAAK,CAAC,GAC/D+N,WAAW,CAAC9N,WAAW,CAACvB,MAAM,GAAG,CAAC,GAClCqP,WAAW,CAAC/N,iBAAiB,GAAG,CAAC;IACvC;EACF;EACAb,eAAeA,CAAC4O,WAAgB;IAC9B,IAAIA,WAAW,CAAC9N,WAAW,IAAI8N,WAAW,CAAC9N,WAAW,CAACvB,MAAM,GAAG,CAAC,IAAIqP,WAAW,CAAC/N,iBAAiB,KAAKgC,SAAS,EAAE;MAChH,OAAO+L,WAAW,CAAC9N,WAAW,CAAC8N,WAAW,CAAC/N,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EAEA;EACAK,cAAcA,CAAC0N,WAAgB,EAAEC,UAAmB;IAClD,IAAID,WAAW,CAAC9N,WAAW,IAAI8N,WAAW,CAAC9N,WAAW,CAACvB,MAAM,GAAG,CAAC,EAAE;MACjE,IAAIsP,UAAU,KAAKhM,SAAS,EAAE;QAC5B+L,WAAW,CAAC/N,iBAAiB,GAAGgO,UAAU;MAC5C;MACAD,WAAW,CAAChG,WAAW,GAAG,IAAI;MAC9B;MACA8D,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;EACF;EAEA5E,eAAeA,CAAC2G,WAAgB;IAC9BA,WAAW,CAAChG,WAAW,GAAG,KAAK;IAC/B;IACA8D,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACApF,cAAcA,CAACmH,WAAgB;IAC7B,IAAI,CAACjO,SAAS,CAACiO,WAAW,CAAC;EAC7B;EAEAtH,cAAcA,CAACsH,WAAgB;IAC7B,IAAI,CAACrO,SAAS,CAACqO,WAAW,CAAC;EAC7B;EAEA;EACAzG,SAASA,CAAC8E,KAAoB,EAAE2B,WAAgB;IAC9C,IAAIA,WAAW,CAAChG,WAAW,EAAE;MAC3B,QAAQqE,KAAK,CAAC6B,GAAG;QACf,KAAK,WAAW;UACd7B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAACzH,cAAc,CAACsH,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACf3B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAACtH,cAAc,CAACmH,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACX3B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAAC9G,eAAe,CAAC2G,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEA;EACAnJ,qBAAqBA,CAACuJ,UAAiB;IACrC,IAAI,CAACA,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;IACX;IACA,OAAOA,UAAU,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,IAAID,CAAC,CAAC;EACzC;EACA;EACA5J,0BAA0BA,CAAC8J,kBAA4B,EAAEV,WAAgB;IACvE;IACAW,MAAM,CAACC,IAAI,CAACZ,WAAW,CAACzD,aAAa,CAAC,CAACsE,OAAO,CAACX,GAAG,IAAG;MACnDF,WAAW,CAACzD,aAAa,CAAC2D,GAAG,CAAC,GAAG,KAAK;IACxC,CAAC,CAAC;IAEF;IACAQ,kBAAkB,CAACG,OAAO,CAACC,SAAS,IAAG;MACrCd,WAAW,CAACzD,aAAa,CAACuE,SAAS,CAAC,GAAG,IAAI;IAC7C,CAAC,CAAC;IAEF;IACAd,WAAW,CAACe,WAAW,GAAG,IAAI,CAACpJ,aAAa,CAAChH,MAAM,GAAG,CAAC,IACrD,IAAI,CAACgH,aAAa,CAACqJ,KAAK,CAAC5C,IAAI,IAAI4B,WAAW,CAACzD,aAAa,CAAC6B,IAAI,CAAC,CAAC;IAEnE;IACA,IAAI,CAAC6C,6BAA6B,CAACjB,WAAW,CAAC;EACjD;EAEA;EACAkB,qBAAqBA,CAAClB,WAAgB;IACpC,OAAOW,MAAM,CAACC,IAAI,CAACZ,WAAW,CAACzD,aAAa,CAAC,CAACiD,MAAM,CAACU,GAAG,IAAIF,WAAW,CAACzD,aAAa,CAAC2D,GAAG,CAAC,CAAC;EAC7F;EAEA;EACQe,6BAA6BA,CAACjB,WAAgB;IACpDA,WAAW,CAACtI,wBAAwB,GAAG,IAAI,CAACwJ,qBAAqB,CAAClB,WAAW,CAAC;EAChF;EAEA;EACQmB,gCAAgCA,CAAA;IACtC,IAAI,IAAI,CAACzQ,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACmQ,OAAO,CAACb,WAAW,IAAG;QAC5C,IAAI,CAACiB,6BAA6B,CAACjB,WAAW,CAAC;MACjD,CAAC,CAAC;IACJ;EACF;EAIApL,sBAAsBA,CAACwM,OAAgB,EAAEhD,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAAC3J,kBAAkB,CAACyJ,IAAI,CAAC,GAAGgD,OAAO;EACjD;EAEAC,kBAAkBA,CAACpM,kBAA4B,EAAEqM,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAM7E,MAAM,IAAIzH,kBAAkB,EAAE;MACvCsM,YAAY,CAAC7E,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAM8E,WAAW,GAAGF,WAAW,CAACpC,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAI2B,WAAW,EAAE;MAC9B,IAAIvM,kBAAkB,CAACwM,QAAQ,CAAC5B,IAAI,CAAC,EAAE;QACrC0B,YAAY,CAAC1B,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAO0B,YAAY;EACrB;EAEAG,UAAUA,CAACC,KAAoC;IAC7C,MAAMpB,GAAG,GAAG,IAAIqB,GAAG,EAAgE;IAEnFD,KAAK,CAACd,OAAO,CAACzC,IAAI,IAAG;MACnB,MAAM8B,GAAG,GAAG,GAAG9B,IAAI,CAACjH,SAAS,IAAIiH,IAAI,CAACnH,KAAK,IAAImH,IAAI,CAAClH,KAAK,EAAE;MAC3D,IAAIqJ,GAAG,CAACsB,GAAG,CAAC3B,GAAG,CAAC,EAAE;QAChB,MAAM4B,QAAQ,GAAGvB,GAAG,CAACjD,GAAG,CAAC4C,GAAG,CAAE;QAC9B4B,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLxB,GAAG,CAACyB,GAAG,CAAC9B,GAAG,EAAE;UAAE9B,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAE2D,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAO1B,KAAK,CAAC4B,IAAI,CAAC1B,GAAG,CAAC2B,MAAM,EAAE,CAAC,CAAC3B,GAAG,CAAC,CAAC;MAAEnC,IAAI;MAAE2D;IAAK,CAAE,MAAM;MACxD,GAAG3D,IAAI;MACP+D,YAAY,EAAEJ;KACf,CAAC,CAAC;EACL;EAGAK,eAAeA,CAAA;IACb,IAAI,CAACtG,gBAAgB,CAACuG,mCAAmC,CAAC;MACxDtE,IAAI,EAAE;QACJuE,YAAY,EAAE,IAAI,CAAC/E,WAAW;QAC9BgF,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLhU,GAAG,CAACiU,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAGtC,IAAI,CAACjS,kBAAkB,GAAG+R,GAAG,CAACC,OAAO,CAACnC,GAAG,CAAEqC,CAA0B,IAAI;UACvE,OAAO;YACLvO,cAAc,EAAE,IAAI;YACpBwO,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACb3L,SAAS,EAAEyL,CAAC,CAACzL,SAAS;YACtBF,KAAK,EAAE2L,CAAC,CAAC3L,KAAK;YACdC,KAAK,EAAE0L,CAAC,CAAC1L,KAAK;YACdxB,SAAS,EAAE,GAAGkN,CAAC,CAAC3L,KAAK,IAAI2L,CAAC,CAAC1L,KAAK,IAAI0L,CAAC,CAACzL,SAAS,EAAE;YACjDmK,WAAW,EAAE,IAAI;YACjBa,YAAY,EAAE,CAAC;YACfvM,cAAc,EAAE,CAAC;YACjBmN,OAAO,EAAE,CAAC;YAAExG,aAAa,EAAE,EAAE;YAC7B5H,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3CoM,WAAW,EAAE,KAAK;YAClB3M,YAAY,EAAE,EAAE;YAAE0B,eAAe,EAAE,IAAI,CAACyB,cAAc,CAAC,CAAC,CAAC;YACzDtF,iBAAiB,EAAE,CAAC;YACpB+H,WAAW,EAAE,KAAK;YAClB3B,WAAW,EAAE,KAAK;YAAE;YACpBnG,WAAW,EAAE0Q,CAAC,CAACI,cAAc,GAAGJ,CAAC,CAACI,cAAc,CAACzC,GAAG,CAAC0C,EAAE,IAAIA,EAAE,CAACC,OAAO,CAAC,CAAC1D,MAAM,CAAE2D,GAAQ,IAAKA,GAAG,IAAI,IAAI,CAAa,GAAG;WACxH;QACH,CAAC,CAAC;QACF,IAAI,CAACzS,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACgR,UAAU,CAAC,IAAI,CAAChR,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAACyM,SAAS,EAAE;EACf;EAKAiG,eAAeA,CAAA;IACbpG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;MAC1CqF,YAAY,EAAE,IAAI,CAAC/E,WAAW;MAC9BrB,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;MAC3DmH,SAAS,EAAE;KACZ,CAAC;IAEF,IAAI,CAAC5H,gBAAgB,CAAC6H,mCAAmC,CAAC;MACxDvF,IAAI,EAAE;QACJuE,YAAY,EAAE,IAAI,CAAC/E,WAAW;QAC9BrB,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DmH,SAAS,EAAE;;KAEd,CAAC,CAACb,IAAI,CACLhU,GAAG,CAACiU,GAAG,IAAG;MACRzF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEwF,GAAG,CAAC;MAC7C,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC5O,YAAY,GAAG0O,GAAG,CAACC,OAAO;QAC/B,IAAI,CAAClG,KAAK,GAAGiG,GAAG,CAACC,OAAO,CAACa,SAAS,GAAG,KAAK,GAAG,IAAI;QACjDvG,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACT,KAAK,CAAC;QACjCQ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,CAAC,CAACwF,GAAG,CAACC,OAAO,CAACa,SAAS,CAAC;QACzDvG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACtF,aAAa,CAAC;QAEjD,IAAI8K,GAAG,CAACC,OAAO,CAACa,SAAS,EAAE;UACzB,IAAI,CAAC5L,aAAa,CAACkJ,OAAO,CAACzC,IAAI,IAAI,IAAI,CAAC7B,aAAa,CAAC6B,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAACnJ,kBAAkB,CAAC4L,OAAO,CAACzC,IAAI,IAAI,IAAI,CAACzJ,kBAAkB,CAACyJ,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC1N,kBAAkB,GAAG+R,GAAG,CAACC,OAAO,CAACa,SAAS,CAAChD,GAAG,CAAEqC,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAAC/O,YAAY,EAAE+O,OAAO;cACnCzO,cAAc,EAAEuO,CAAC,CAACvO,cAAc;cAChCnC,WAAW,EAAE0Q,CAAC,CAAC1Q,WAAW,KAAK0Q,CAAC,CAACY,gBAAgB,GAAG,CAACZ,CAAC,CAACY,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9EnE,KAAK,EAAEuD,CAAC,CAACvD,KAAK;cACdwD,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCY,WAAW,EAAEb,CAAC,CAACa,WAAW;cAC1BtM,SAAS,EAAEyL,CAAC,CAACzL,SAAS;cACtBF,KAAK,EAAE2L,CAAC,CAAC3L,KAAK;cACdC,KAAK,EAAE0L,CAAC,CAAC1L,KAAK;cACdxB,SAAS,EAAEkN,CAAC,CAAClN,SAAS,GAAGkN,CAAC,CAAClN,SAAS,GAAG,GAAGkN,CAAC,CAAC3L,KAAK,IAAI2L,CAAC,CAAC1L,KAAK,IAAI0L,CAAC,CAACzL,SAAS,EAAE;cAC7EmK,WAAW,EAAEsB,CAAC,CAACtB,WAAW;cAC1Ba,YAAY,EAAES,CAAC,CAACT,YAAY;cAC5BvM,cAAc,EAAEgN,CAAC,CAACG,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGH,CAAC,CAAChN,cAAc;cACtDmN,OAAO,EAAEH,CAAC,CAACG,OAAO;cAClBxG,aAAa,EAAEqG,CAAC,CAACc,qBAAqB,CAAC/S,MAAM,GAAG,IAAI,CAACgT,0BAA0B,CAAC,IAAI,CAAChM,aAAa,EAAEiL,CAAC,CAACc,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACnH;cAAa,CAAE;cAAE5H,kBAAkB,EAAEiO,CAAC,CAACtB,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAACpM,kBAAkB,EAAE2N,CAAC,CAACtB,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC3M;cAAkB,CAAE;cAC9RoM,WAAW,EAAE6B,CAAC,CAACc,qBAAqB,CAAC/S,MAAM,KAAK,IAAI,CAACgH,aAAa,CAAChH,MAAM;cACzEyD,YAAY,EAAE,EAAE;cAAE0B,eAAe,EAAE8M,CAAC,CAACG,OAAO,GAAG,IAAI,CAAC7E,cAAc,CAAC0E,CAAC,CAACG,OAAO,EAAE,IAAI,CAACxL,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cAC3HtF,iBAAiB,EAAE,CAAC;cACpB+H,WAAW,EAAE,KAAK;cAClB3B,WAAW,EAAE,KAAK;cAAE;cACpBX,wBAAwB,EAAE,EAAE,CAAC;aAC9B;UACH,CAAC,CAAC;UACFsF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvM,kBAAkB,CAAC;UAClEsM,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvM,kBAAkB,CAACC,MAAM,CAAC;QAC3E,CAAC,MAAM;UACL;UACA,IAAI,CAACyR,eAAe,EAAE;QACxB;QAEA;QACA,IAAI,CAACjB,gCAAgC,EAAE;MACzC,CAAC,MAAM;QACLnE,OAAO,CAACS,KAAK,CAAC,yBAAyB,EAAEgF,GAAG,CAAC;MAC/C;IACF,CAAC,CAAC,CACH,CAACtF,SAAS,CAAC;MACVM,KAAK,EAAGA,KAAK,IAAI;QACfT,OAAO,CAACS,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAzH,mBAAmBA,CAACgK,WAAgB;IAClC,IAAIA,WAAW,CAAClK,eAAe,IAAIkK,WAAW,CAAClK,eAAe,CAACsB,KAAK,KAAK,CAAC,EAAE;MAC1E4I,WAAW,CAACpK,cAAc,GAAG,CAAC;IAChC;EACF;EACAgO,4BAA4BA,CAAChQ,IAAW;IACtC,KAAK,IAAIwK,IAAI,IAAIxK,IAAI,EAAE;MACrB,IAAIwK,IAAI,CAACjC,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOiC,IAAI,CAACyF,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOpD,MAAM,CAACC,IAAI,CAACmD,GAAG,CAAC,CAACvE,MAAM,CAACU,GAAG,IAAI6D,GAAG,CAAC7D,GAAG,CAAC,CAAC;EACjD;EAEA8D,0BAA0BA,CAACD,GAA4B;IACrD,OAAOpD,MAAM,CAACC,IAAI,CAACmD,GAAG,CAAC,CACpBvE,MAAM,CAACU,GAAG,IAAI6D,GAAG,CAAC7D,GAAG,CAAC,CAAC,CACvB+D,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACpO,eAAoB,EAAEnB,kBAAuB;IAC1D,IAAImB,eAAe,IAAIA,eAAe,CAACsB,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC4M,0BAA0B,CAACrP,kBAAkB,CAAC;IAC5D;EACF;EAEAwP,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAAClF,KAAK,CAAC,GAAG,CAAC;IACpC,IAAImF,KAAK,CAAC1T,MAAM,GAAG,CAAC,EAAE;MACpB,OAAO0T,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAClQ,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACzD,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACL4T,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAAC/P,YAAY,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,IAAI,IAAI;QACpE4Q,aAAa,EAAEpQ,YAAY,CAAC,CAAC,CAAC,CAAC+K,SAAS,IAAI,IAAI;QAChDsF,QAAQ,EAAErQ,YAAY,CAAC,CAAC,CAAC,CAACiL,KAAK,CAACxL,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOI,SAAS;EAEzB;EAGAyQ,UAAUA,CAAA;IACR,IAAI,CAAC9I,KAAK,CAAC+I,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAM1G,IAAI,IAAI,IAAI,CAAC2G,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAACxG,IAAI,CAAC2E,OAAQ,EAAE;QACzC6B,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAACzG,IAAI,CAACxI,cAAe,EAAE;QACvDiP,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAIzG,IAAI,CAAC+D,YAAY,IAAI/D,IAAI,CAACxI,cAAc,EAAE;QAC5C,IAAIwI,IAAI,CAACxI,cAAc,GAAGwI,IAAI,CAAC+D,YAAY,IAAI/D,IAAI,CAACxI,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAACgG,KAAK,CAACoJ,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAG5G,IAAI,CAAC+D,YAAY,GAAG,KAAK/D,IAAI,CAAC1I,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACoP,kBAAkB,IAAK,CAAC1G,IAAI,CAAC1I,SAAU,EAAE;QAC5CoP,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAAChJ,KAAK,CAACoJ,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAACjJ,KAAK,CAACoJ,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAClJ,KAAK,CAACoJ,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIA3K,QAAQA,CAAA;IACN;IACA,IAAI,CAACQ,YAAY,GAAG,IAAI;IAExB,IAAI,CAACkK,mBAAmB,GAAG,IAAI,CAACrU,kBAAkB,CAAC6P,GAAG,CAAE0E,CAAM,IAAI;MAChE,OAAO;QACL5Q,cAAc,EAAE4Q,CAAC,CAAC5Q,cAAc,GAAG4Q,CAAC,CAAC5Q,cAAc,GAAG,IAAI;QAC1DgL,KAAK,EAAE4F,CAAC,CAAC7Q,YAAY,GAAG,IAAI,CAACkQ,UAAU,CAACW,CAAC,CAAC7Q,YAAY,CAAC,GAAGH,SAAS;QACnE4O,kBAAkB,EAAE,IAAI,CAACiB,oBAAoB,CAACmB,CAAC,CAAC1I,aAAa,CAAC;QAC9DkH,WAAW,EAAEwB,CAAC,CAACxB,WAAW,GAAGwB,CAAC,CAACxB,WAAW,GAAG,IAAI;QACjDyB,OAAO,EAAE,IAAI,CAAC1I,KAAK,GAAG,IAAI,GAAG,IAAI,CAACzI,YAAY,EAAE+O,OAAO;QACvD7L,KAAK,EAAEgO,CAAC,CAAChO,KAAK;QACdC,KAAK,EAAE+N,CAAC,CAAC/N,KAAK;QACdC,SAAS,EAAE8N,CAAC,CAAC9N,SAAS;QACtBzB,SAAS,EAAEuP,CAAC,CAACvP,SAAS;QAAE;QACxB4L,WAAW,EAAE2D,CAAC,CAACnP,eAAe,CAACsB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC8M,cAAc,CAACe,CAAC,CAACnP,eAAe,EAAEmP,CAAC,CAACtQ,kBAAkB,CAAC,IAAI,IAAI;QACxHwN,YAAY,EAAE8C,CAAC,CAAC9C,YAAY;QAC5BvM,cAAc,EAAEqP,CAAC,CAACrP,cAAc;QAChCmN,OAAO,EAAEkC,CAAC,CAACnP,eAAe,CAACsB;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACsN,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC9I,KAAK,CAACuJ,aAAa,CAACxU,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC6K,OAAO,CAAC4J,aAAa,CAAC,IAAI,CAACxJ,KAAK,CAACuJ,aAAa,CAAC;MACpD;MACA,IAAI,CAACE,sBAAsB,EAAE;MAC7B,IAAI,CAACxK,YAAY,GAAG,KAAK;MACzB;IACF;IACA,IAAI,IAAI,CAAC2B,KAAK,EAAE;MACd,IAAI,CAAC8I,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC9J,gBAAgB,CAAC+J,oCAAoC,CAAC;MACzDzH,IAAI,EAAE,IAAI,CAACgH;KACZ,CAAC,CAAC5H,SAAS,CAAC;MACXsI,IAAI,EAAGhD,GAAG,IAAI;QACZ,IAAI,CAAC5H,YAAY,GAAG,KAAK;QACzB,IAAI4H,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACnH,OAAO,CAACkK,aAAa,CAAC,MAAM,CAAC;UAClC;UACA,IAAI,CAACjL,MAAM,EAAE;QACf;MACF,CAAC;MACDgD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5C,YAAY,GAAG,KAAK;QACzBmC,OAAO,CAACS,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;KACD,CAAC;EACJ;EAIA6H,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvBrD,YAAY,EAAE,IAAI,CAAC/E,WAAW;MAC9BqI,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;MAC3C7I,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACT,gBAAgB,CAACoK,sCAAsC,CAAC;MAC3D9H,IAAI,EAAE,IAAI,CAAC4H;KACZ,CAAC,CAACxI,SAAS,CAAC;MACXsI,IAAI,EAAGhD,GAAG,IAAI;QACZ,IAAI,CAAC5H,YAAY,GAAG,KAAK;QACzB,IAAI4H,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACnH,OAAO,CAACkK,aAAa,CAAC,MAAM,CAAC;UAClC;UACA,IAAI,CAACjL,MAAM,EAAE;QACf;MACF,CAAC;MACDgD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5C,YAAY,GAAG,KAAK;QACzBmC,OAAO,CAACS,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACvC;KACD,CAAC;EACJ;EAEAkG,0BAA0BA,CAACmC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAM5H,IAAI,IAAI0H,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACpJ,IAAI,CAACuJ,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAK/H,IAAI,IAAI8H,KAAK,CAACE,SAAS,CAAC;MAClFJ,CAAC,CAAC5H,IAAI,CAAC,GAAG,CAAC,CAAC6H,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAEF;;;EAGA7K,aAAaA,CAAA;IACX;IACA,IAAI,CAACW,gBAAgB,CAACuG,mCAAmC,CAAC;MACxDtE,IAAI,EAAE;QACJuE,YAAY,EAAE,IAAI,CAAC/E,WAAW;QAC9BgF,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLhU,GAAG,CAACiU,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC;QACA,MAAM0D,iBAAiB,GAAG,IAAIC,GAAG,EAAU;QAC3C7D,GAAG,CAACC,OAAO,CAAC7B,OAAO,CAAE0F,QAAa,IAAI;UACpC,MAAMrG,GAAG,GAAG,GAAGqG,QAAQ,CAACpP,SAAS,IAAIoP,QAAQ,CAACtP,KAAK,IAAIsP,QAAQ,CAACrP,KAAK,EAAE;UACvEmP,iBAAiB,CAACG,GAAG,CAACtG,GAAG,CAAC;QAC5B,CAAC,CAAC;QAEF;QACA,MAAMuG,cAAc,GAAG,IAAI,CAAC/V,kBAAkB,CAAC8O,MAAM,CAAEpB,IAAS,IAAI;UAClE,MAAMsI,OAAO,GAAG,GAAGtI,IAAI,CAACjH,SAAS,IAAIiH,IAAI,CAACnH,KAAK,IAAImH,IAAI,CAAClH,KAAK,EAAE;UAC/D,OAAOmP,iBAAiB,CAACxE,GAAG,CAAC6E,OAAO,CAAC;QACvC,CAAC,CAAC;QAEF,IAAID,cAAc,CAAC9V,MAAM,KAAK,CAAC,EAAE;UAC/B,IAAI,CAAC6K,OAAO,CAACkC,YAAY,CAAC,eAAe,CAAC;UAC1C;QACF;QAEA;QACA,IAAI,CAACqH,mBAAmB,GAAG0B,cAAc,CAAClG,GAAG,CAAE0E,CAAM,IAAI;UACvD,OAAO;YACL5Q,cAAc,EAAE4Q,CAAC,CAAC5Q,cAAc,GAAG4Q,CAAC,CAAC5Q,cAAc,GAAG,IAAI;YAC1DgL,KAAK,EAAE4F,CAAC,CAAC7Q,YAAY,GAAG,IAAI,CAACkQ,UAAU,CAACW,CAAC,CAAC7Q,YAAY,CAAC,GAAGH,SAAS;YACnE4O,kBAAkB,EAAE,IAAI,CAACiB,oBAAoB,CAACmB,CAAC,CAAC1I,aAAa,CAAC;YAC9DkH,WAAW,EAAE,IAAI;YAAE;YACnByB,OAAO,EAAE,IAAI;YAAE;YACfjO,KAAK,EAAEgO,CAAC,CAAChO,KAAK;YACdC,KAAK,EAAE+N,CAAC,CAAC/N,KAAK;YACdC,SAAS,EAAE8N,CAAC,CAAC9N,SAAS;YACtBzB,SAAS,EAAEuP,CAAC,CAACvP,SAAS;YACtB4L,WAAW,EAAE2D,CAAC,CAACnP,eAAe,CAACsB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC8M,cAAc,CAACe,CAAC,CAACnP,eAAe,EAAEmP,CAAC,CAACtQ,kBAAkB,CAAC,IAAI,IAAI;YACxHwN,YAAY,EAAE8C,CAAC,CAAC9C,YAAY;YAC5BvM,cAAc,EAAEqP,CAAC,CAACrP,cAAc;YAChCmN,OAAO,EAAEkC,CAAC,CAACnP,eAAe,CAACsB;WAC5B;QACH,CAAC,CAAC;QAEF;QACA,IAAI,CAACsN,UAAU,EAAE;QACjB,IAAI,IAAI,CAAC9I,KAAK,CAACuJ,aAAa,CAACxU,MAAM,GAAG,CAAC,EAAE;UACvC,IAAI,CAAC6K,OAAO,CAAC4J,aAAa,CAAC,IAAI,CAACxJ,KAAK,CAACuJ,aAAa,CAAC;UACpD;QACF;QAEA;QACA,IAAI,CAACQ,iBAAiB,GAAG;UACvBrD,YAAY,EAAE,IAAI,CAAC/E,WAAW;UAC9BqI,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;UAC3C7I,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;SACnD;QAED,IAAI,CAACT,gBAAgB,CAACoK,sCAAsC,CAAC;UAC3D9H,IAAI,EAAE,IAAI,CAAC4H;SACZ,CAAC,CAACxI,SAAS,CAACwJ,SAAS,IAAG;UACvB,IAAIA,SAAS,CAAChE,UAAU,IAAI,CAAC,EAAE;YAC7B,IAAI,CAACnH,OAAO,CAACkK,aAAa,CAAC,cAAce,cAAc,CAAC9V,MAAM,QAAQ,CAAC;YACvE;YACA,IAAI,CAACyS,eAAe,EAAE;UACxB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAC5H,OAAO,CAACkC,YAAY,CAAC,eAAe,CAAC;MAC5C;IACF,CAAC,CAAC,CACH,CAACP,SAAS,EAAE;EACf;EAEA;EACQyJ,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACrJ,WAAW,EAAE;IAEvB,IAAI,CAACvB,aAAa,CAAC6K,4BAA4B,CAAC;MAAEtJ,WAAW,EAAE,IAAI,CAACA;IAAW,CAAE,CAAC,CAACJ,SAAS,CAAC;MAC3FsI,IAAI,EAAGqB,QAAQ,IAAI;QACjB9J,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6J,QAAQ,CAAC;QAClD,IAAIA,QAAQ,CAACpE,OAAO,EAAE;UACpB,IAAI,CAAClL,YAAY,GAAG,IAAI,CAACuP,gCAAgC,CAACD,QAAQ,CAACpE,OAAO,CAAC;UAC3E1F,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACzF,YAAY,CAAC;QAC3D;MACF,CAAC;MACDiG,KAAK,EAAGA,KAAK,IAAI;QACfT,OAAO,CAACS,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D;QACA,IAAI,IAAI,CAAC9F,aAAa,IAAI,IAAI,CAACA,aAAa,CAAChH,MAAM,GAAG,CAAC,EAAE;UACvD,IAAI,CAAC6G,YAAY,GAAG,IAAI,CAACwP,kCAAkC,CAAC,IAAI,CAACrP,aAAa,CAAC;QACjF;MACF;KACD,CAAC;EACJ;EAEA;EACQoP,gCAAgCA,CAACE,OAAY;IACnD,MAAMzP,YAAY,GAAQ,EAAE;IAE5BmJ,MAAM,CAACsG,OAAO,CAACA,OAAO,CAAC,CAACpG,OAAO,CAAC,CAAC,CAACqG,QAAQ,EAAEC,MAAM,CAAgB,KAAI;MACpE3P,YAAY,CAAC0P,QAAQ,CAAC,GAAGC,MAAM,CAAC5G,GAAG,CAAE6G,KAAU,KAAM;QACnD3G,IAAI,EAAE2G,KAAK,CAACC,SAAS;QACrBH,QAAQ,EAAEE,KAAK,CAACE,QAAQ;QACxBC,KAAK,EAAEH,KAAK,CAACI,KAAK;QAClBC,OAAO,EAAEL,KAAK,CAACM,OAAO;QACtBC,SAAS,EAAEP,KAAK,CAACC,SAAS;QAC1BO,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOrQ,YAAY;EACrB;EAEA;EACAwP,kCAAkCA,CAACrP,aAAuB;IACxD,IAAI,CAACA,aAAa,IAAIA,aAAa,CAAChH,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,EAAE;IACX;IAEA;IACA,MAAM6G,YAAY,GAAQ,EAAE;IAE5BG,aAAa,CAACkJ,OAAO,CAACC,SAAS,IAAG;MAChC;MACA,MAAMgH,aAAa,GAAGhH,SAAS,CAACiH,KAAK,CAAC,WAAW,CAAC;MAClD,MAAMb,QAAQ,GAAGY,aAAa,GAAG,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM;MAEhE,IAAI,CAACtQ,YAAY,CAAC0P,QAAQ,CAAC,EAAE;QAC3B1P,YAAY,CAAC0P,QAAQ,CAAC,GAAG,EAAE;MAC7B;MAEA;MACA,MAAMc,WAAW,GAAGC,QAAQ,CAACnH,SAAS,CAACoH,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MAC7D,MAAMX,KAAK,GAAGY,IAAI,CAACC,IAAI,CAACJ,WAAW,GAAG,CAAC,CAAC;MAExCxQ,YAAY,CAAC0P,QAAQ,CAAC,CAAC5H,IAAI,CAAC;QAC1BmB,IAAI,EAAEK,SAAS;QACfoG,QAAQ,EAAEA,QAAQ;QAClBK,KAAK,EAAE,GAAGA,KAAK,GAAG;QAClBK,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;IAAE,OAAOrQ,YAAY;EACzB;EAIAgG,iCAAiCA,CAAA;IAC/BR,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAE,IAAI,CAACM,WAAW,CAAC;IAE3F,IAAI,CAAC7B,yBAAyB,CAAC2M,8DAA8D,CAAC;MAC5FtK,IAAI,EAAE,IAAI,CAACR;KACZ,CAAC,CAACiF,IAAI,CACLhU,GAAG,CAACiU,GAAG,IAAG;MACRzF,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEwF,GAAG,CAAC;MAC/D,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAChL,aAAa,GAAG,IAAI,CAACiM,4BAA4B,CAACnB,GAAG,CAACC,OAAO,CAAC;QAEnE;QACA,IAAI,CAACkE,uBAAuB,EAAE;QAE9B,IAAI,CAACxD,eAAe,EAAE;MACxB,CAAC,MAAM;QACLpG,OAAO,CAACS,KAAK,CAAC,2CAA2C,EAAEgF,GAAG,CAAC;MACjE;IACF,CAAC,CAAC,CACH,CAACtF,SAAS,CAAC;MACVM,KAAK,EAAGA,KAAK,IAAI;QACfT,OAAO,CAACS,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAClE;KACD,CAAC;EACJ;EACAhD,MAAMA,CAAA;IACJ,IAAI,CAACsB,aAAa,CAACuD,IAAI,CAAC;MACtBgJ,MAAM;MACNC,OAAO,EAAE,IAAI,CAAChL;KACf,CAAC;IACF,IAAI,CAAC1B,QAAQ,CAAC2M,IAAI,EAAE;EACtB;EAEA;EAEA;;;EAGAlQ,eAAeA,CAAC0H,WAAwC;IACtD;IACA,MAAMyI,WAAW,GAAG,CAAC,CAACzI,WAAW,CAACtK,SAAS,IAAIsK,WAAW,CAACtK,SAAS,CAACgT,IAAI,EAAE,KAAK,EAAE;IAClF,MAAMC,SAAS,GAAG,CAAC,CAAC3I,WAAW,CAAClK,eAAe,IAAIkK,WAAW,CAAClK,eAAe,CAACsB,KAAK;IACpF,MAAMwR,gBAAgB,GAAG,CAAC,CAAC5I,WAAW,CAACpK,cAAc,IAAIoK,WAAW,CAACpK,cAAc,GAAG,CAAC;IAEvF;IACA,IAAIiT,aAAa,GAAG,IAAI;IACxB,IAAI7I,WAAW,CAAClK,eAAe,EAAEsB,KAAK,KAAK,CAAC,EAAE;MAC5CyR,aAAa,GAAG,CAAC,CAAC7I,WAAW,CAACrL,kBAAkB,IAC9CgM,MAAM,CAACuB,MAAM,CAAClC,WAAW,CAACrL,kBAAkB,CAAC,CAACmI,IAAI,CAACgM,QAAQ,IAAIA,QAAQ,CAAC;IAC5E;IAEA,OAAOL,WAAW,IAAIE,SAAS,IAAIC,gBAAgB,IAAIC,aAAa;EACtE;EAEA;;;EAGApY,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC9E,OAAO,IAAI,CAACD,kBAAkB,CAAC8O,MAAM,CAACpB,IAAI,IAAI,IAAI,CAAC9F,eAAe,CAAC8F,IAAI,CAAC,CAAC,CAACzN,MAAM;EAClF;EAEA;;;EAGAqK,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACtK,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC9E,MAAMoY,SAAS,GAAG,IAAI,CAACtY,sBAAsB,EAAE;IAC/C,OAAO0X,IAAI,CAACa,KAAK,CAAED,SAAS,GAAG,IAAI,CAACrY,kBAAkB,CAACC,MAAM,GAAI,GAAG,CAAC;EACvE;EAEA;;;EAGAK,YAAYA,CAACD,KAAa;IACxB,MAAMkY,OAAO,GAAGnL,QAAQ,CAACoL,cAAc,CAAC,aAAanY,KAAK,EAAE,CAAC;IAC7D,IAAIkY,OAAO,EAAE;MACXA,OAAO,CAACE,cAAc,CAAC;QACrBC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE;OACT,CAAC;MAEF;MACAL,OAAO,CAACM,SAAS,CAAC/C,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,iBAAiB,CAAC;MACnEgD,UAAU,CAAC,MAAK;QACdP,OAAO,CAACM,SAAS,CAACE,MAAM,CAAC,QAAQ,EAAE,eAAe,EAAE,iBAAiB,CAAC;MACxE,CAAC,EAAE,IAAI,CAAC;IACV;EACF;EAEA;;;EAGAja,2BAA2BA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACkB,kBAAkB,EAAE;IAE9B,MAAMgZ,oBAAoB,GAAG,IAAI,CAAChZ,kBAAkB,CAACiZ,SAAS,CAACvL,IAAI,IAAI,CAAC,IAAI,CAAC9F,eAAe,CAAC8F,IAAI,CAAC,CAAC;IACnG,IAAIsL,oBAAoB,KAAK,CAAC,CAAC,EAAE;MAC/B,IAAI,CAAC1Y,YAAY,CAAC0Y,oBAAoB,CAAC;IACzC;EACF;EAEA;;;EAGArE,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC3U,kBAAkB,EAAE;IAE9B;IACA,MAAMkZ,eAAe,GAAG,IAAI,CAAClZ,kBAAkB,CAACiZ,SAAS,CAACvL,IAAI,IAAI,CAAC,IAAI,CAAC9F,eAAe,CAAC8F,IAAI,CAAC,CAAC;IAC9F,IAAIwL,eAAe,KAAK,CAAC,CAAC,EAAE;MAC1B,IAAI,CAAC5Y,YAAY,CAAC4Y,eAAe,CAAC;IACpC;EACF;EAEA;;;EAGA7Z,WAAWA,CAAA;IACT8Z,MAAM,CAACC,QAAQ,CAAC;MACdC,GAAG,EAAE,CAAC;MACNX,QAAQ,EAAE;KACX,CAAC;EACJ;EAEA;;;EAGArR,kBAAkBA,CAACiI,WAAwC;IACzDA,WAAW,CAAC3H,WAAW,GAAG,CAAC2H,WAAW,CAAC3H,WAAW;EACpD;EAEA;;;EAGApI,SAASA,CAAA;IACP,IAAI,IAAI,CAACS,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACmQ,OAAO,CAACzC,IAAI,IAAG;QACrCA,IAAI,CAAC/F,WAAW,GAAG,KAAK;MAC1B,CAAC,CAAC;IACJ;EACF;EAEA;;;EAGAjI,WAAWA,CAAA;IACT,IAAI,IAAI,CAACM,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACmQ,OAAO,CAACzC,IAAI,IAAG;QACrCA,IAAI,CAAC/F,WAAW,GAAG,IAAI;MACzB,CAAC,CAAC;IACJ;EACF;EAEA;;;EAGA/H,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACI,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACmQ,OAAO,CAACzC,IAAI,IAAG;QACrCA,IAAI,CAAC/F,WAAW,GAAG,IAAI,CAACC,eAAe,CAAC8F,IAAI,CAAC;MAC/C,CAAC,CAAC;IACJ;EACF;;;uCAn5BWhD,4CAA4C,EAAArM,EAAA,CAAAib,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnb,EAAA,CAAAib,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAArb,EAAA,CAAAib,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAvb,EAAA,CAAAib,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAzb,EAAA,CAAAib,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAA1b,EAAA,CAAAib,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA5b,EAAA,CAAAib,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAA9b,EAAA,CAAAib,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAhc,EAAA,CAAAib,iBAAA,CAAAO,EAAA,CAAAS,eAAA,GAAAjc,EAAA,CAAAib,iBAAA,CAAAiB,EAAA,CAAAC,YAAA,GAAAnc,EAAA,CAAAib,iBAAA,CAAAO,EAAA,CAAAY,YAAA;IAAA;EAAA;;;YAA5C/P,4CAA4C;MAAAgQ,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvc,EAAA,CAAAwc,0BAAA,EAAAxc,EAAA,CAAAyc,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClDjD/c,EAHR,CAAAC,cAAA,aAAqE,iBAAgD,wBACnD,aACb,aACJ;UACvCD,EAAA,CAAAmB,SAAA,aAAqD;UACrDnB,EAAA,CAAAC,cAAA,UAAK;UACHD,EAAA,CAAAmB,SAAA,qBAAiC;UAErCnB,EADE,CAAAW,YAAA,EAAM,EACF;UAEJX,EADF,CAAAC,cAAA,aAAyC,cAC8C;UACnFD,EAAA,CAAAU,MAAA,IACF;UAGNV,EAHM,CAAAW,YAAA,EAAO,EACH,EACF,EACS;UAQPX,EANV,CAAAC,cAAA,wBAAqC,eACZ,eAEiD,cACrB,eACJ,eACwC;;UAC7ED,EAAA,CAAAC,cAAA,eAAyF;UACvFD,EAAA,CAAAmB,SAAA,gBAEO;UAEXnB,EADE,CAAAW,YAAA,EAAM,EACF;;UAEJX,EADF,CAAAC,cAAA,WAAK,cACyC;UAAAD,EAAA,CAAAU,MAAA,IAAkB;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACnEX,EAAA,CAAAC,cAAA,aAAsC;UAAAD,EAAA,CAAAU,MAAA,4FAAc;UAExDV,EAFwD,CAAAW,YAAA,EAAI,EACpD,EACF;UAKFX,EAFJ,CAAAC,cAAA,cAAyC,eACf,eACyB;UAC7CD,EAAA,CAAAU,MAAA,IACF;UAAAV,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAC,cAAA,eAAmC;UACjCD,EAAA,CAAAU,MAAA,IACF;UACFV,EADE,CAAAW,YAAA,EAAM,EACF;UACNX,EAAA,CAAAC,cAAA,eAAgC;;UAC9BD,EAAA,CAAAC,cAAA,eAAgE;UAG9DD,EAFA,CAAAmB,SAAA,gBACsF,gBAGA;UACxFnB,EAAA,CAAAW,YAAA,EAAM;;UAEJX,EADF,CAAAC,cAAA,eAA+D,gBACf;UAAAD,EAAA,CAAAU,MAAA,IAA8B;UAIpFV,EAJoF,CAAAW,YAAA,EAAO,EAC/E,EACF,EACF,EACF;UAGNX,EAAA,CAAAY,UAAA,KAAAqc,4DAAA,mBAA0F;UA+C5Fjd,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAY,UAAA,KAAAsc,qEAAA,6BAA8E;UA+XlFld,EADE,CAAAW,YAAA,EAAM,EACO;UAMTX,EAHN,CAAAC,cAAA,0BAAiH,eACjE,eACmB,cACpB;;UACvCD,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAmB,SAAA,gBACuE;UACzEnB,EAAA,CAAAW,YAAA,EAAM;;UACNX,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAU,MAAA,IAA0C;UAClDV,EADkD,CAAAW,YAAA,EAAO,EACnD;UAGJX,EADF,CAAAC,cAAA,cAAyC,cACE;UACvCD,EAAA,CAAAmB,SAAA,eAAqD;UACrDnB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAU,MAAA,IAAkC;UAC1CV,EAD0C,CAAAW,YAAA,EAAO,EAC3C;UACNX,EAAA,CAAAC,cAAA,cAAyC;UACvCD,EAAA,CAAAmB,SAAA,eAAsD;UACtDnB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAU,MAAA,IAAqE;UAE/EV,EAF+E,CAAAW,YAAA,EAAO,EAC9E,EACF;UAENX,EAAA,CAAAC,cAAA,eAAmC;UACjCD,EAAA,CAAAU,MAAA,kFACF;UAKVV,EALU,CAAAW,YAAA,EAAM,EACF,EACF,EACS,EACT,EACN;UA2FNX,EAxFA,CAAAY,UAAA,KAAAuc,qEAAA,2BAA8E,KAAAC,4DAAA,oBAyFhB;UA0F5Dpd,EAFF,CAAAC,cAAA,eAA2F,kBAI5B;UAA3DD,EAAA,CAAAE,UAAA,mBAAAmd,+EAAA;YAAA,OAASL,GAAA,CAAAtR,MAAA,EAAQ;UAAA,EAAC;;UAClB1L,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAmB,SAAA,gBAA6G;UAC/GnB,EAAA,CAAAW,YAAA,EAAM;;UAGNX,EAAA,CAAAC,cAAA,eAC8K;UAC5KD,EAAA,CAAAU,MAAA,sBACF;UACFV,EADE,CAAAW,YAAA,EAAM,EACC;UAGTX,EAAA,CAAAY,UAAA,KAAA0c,+DAAA,qBAEqE;UAerEtd,EAAA,CAAAC,cAAA,kBAG+E;UADxCD,EAAA,CAAAE,UAAA,mBAAAqd,+EAAA;YAAA,OAASP,GAAA,CAAA1R,QAAA,EAAU;UAAA,EAAC;UAWzDtL,EAPA,CAAAY,UAAA,KAAA4c,iEAAA,kBAA6G,KAAAC,iEAAA,kBAOZ;UAKjGzd,EAAA,CAAAC,cAAA,eAC8K;UAC5KD,EAAA,CAAAU,MAAA,IACF;UAEJV,EAFI,CAAAW,YAAA,EAAM,EACC,EACL;;;;UA/uBMX,EAAA,CAAAwB,SAAA,IACF;UADExB,EAAA,CAAAmE,kBAAA,MAAA6Y,GAAA,CAAAtP,YAAA,MACF;UAmBkD1N,EAAA,CAAAwB,SAAA,IAAkB;UAAlBxB,EAAA,CAAAgG,iBAAA,CAAAgX,GAAA,CAAAtP,YAAA,CAAkB;UAS5D1N,EAAA,CAAAwB,SAAA,GACF;UADExB,EAAA,CAAAiD,kBAAA,wBAAA+Z,GAAA,CAAAtb,sBAAA,YAAAsb,GAAA,CAAArb,kBAAA,kBAAAqb,GAAA,CAAArb,kBAAA,CAAAC,MAAA,YACF;UAEE5B,EAAA,CAAAwB,SAAA,GACF;UADExB,EAAA,CAAAmE,kBAAA,MAAA6Y,GAAA,CAAA/Q,qBAAA,sBACF;UAOIjM,EAAA,CAAAwB,SAAA,GAA2D;;UAIfxB,EAAA,CAAAwB,SAAA,GAA8B;UAA9BxB,EAAA,CAAAmE,kBAAA,KAAA6Y,GAAA,CAAA/Q,qBAAA,QAA8B;UAO/BjM,EAAA,CAAAwB,SAAA,EAAmC;UAAnCxB,EAAA,CAAAyB,UAAA,SAAAub,GAAA,CAAArb,kBAAA,CAAAC,MAAA,KAAmC;UAgDpD5B,EAAA,CAAAwB,SAAA,EAAuB;UAAvBxB,EAAA,CAAAyB,UAAA,YAAAub,GAAA,CAAArb,kBAAA,CAAuB;UA0YnD3B,EAAA,CAAAwB,SAAA,GAA0C;UAA1CxB,EAAA,CAAAmE,kBAAA,YAAA6Y,GAAA,CAAArb,kBAAA,CAAAC,MAAA,yCAA0C;UAMxC5B,EAAA,CAAAwB,SAAA,GAAkC;UAAlCxB,EAAA,CAAAmE,kBAAA,KAAA6Y,GAAA,CAAAtb,sBAAA,0BAAkC;UAIlC1B,EAAA,CAAAwB,SAAA,GAAqE;UAArExB,EAAA,CAAAmE,kBAAA,MAAA6Y,GAAA,CAAArb,kBAAA,CAAAC,MAAA,SAAAob,GAAA,CAAAtb,sBAAA,0BAAqE;UAcnD1B,EAAA,CAAAwB,SAAA,GAAuB;UAAvBxB,EAAA,CAAAyB,UAAA,YAAAub,GAAA,CAAArb,kBAAA,CAAuB;UAyF1D3B,EAAA,CAAAwB,SAAA,EAAyD;UAAzDxB,EAAA,CAAAyB,UAAA,SAAAub,GAAA,CAAArb,kBAAA,IAAAqb,GAAA,CAAArb,kBAAA,CAAAC,MAAA,KAAyD;UA4FrC5B,EAAA,CAAAwB,SAAA,GAAyB;UAAzBxB,EAAA,CAAAyB,UAAA,aAAAub,GAAA,CAAAlR,YAAA,CAAyB;UAarC9L,EAAA,CAAAwB,SAAA,GAA2B;UAA3BxB,EAAA,CAAAyB,UAAA,SAAAub,GAAA,CAAAhY,YAAA,kBAAAgY,GAAA,CAAAhY,YAAA,CAAAC,OAAA,CAA2B;UAmBlCjF,EAAA,CAAAwB,SAAA,EAAoC;UAApCxB,EAAA,CAAAwD,WAAA,kBAAAwZ,GAAA,CAAAlR,YAAA,CAAoC;UACpC9L,EAAA,CAAAyB,UAAA,eAAAic,QAAA,GAAAV,GAAA,CAAAhY,YAAA,kBAAAgY,GAAA,CAAAhY,YAAA,CAAAC,OAAA,cAAAyY,QAAA,KAAAxY,SAAA,GAAAwY,QAAA,aAAAV,GAAA,CAAAlR,YAAA,CAA+D;UAGzD9L,EAAA,CAAAwB,SAAA,EAAkB;UAAlBxB,EAAA,CAAAyB,UAAA,SAAAub,GAAA,CAAAlR,YAAA,CAAkB;UAOlB9L,EAAA,CAAAwB,SAAA,EAAmB;UAAnBxB,EAAA,CAAAyB,UAAA,UAAAub,GAAA,CAAAlR,YAAA,CAAmB;UAOvB9L,EAAA,CAAAwB,SAAA,GACF;UADExB,EAAA,CAAAmE,kBAAA,MAAA6Y,GAAA,CAAAlR,YAAA,6DACF;;;qBDvsBQvM,YAAY,EAAAwc,EAAA,CAAA4B,OAAA,EAAA5B,EAAA,CAAA6B,IAAA,EAAEle,YAAY,EAAAme,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAEjf,eAAe,EAAAkf,GAAA,CAAAC,yBAAA,EAAEtf,gBAAgB,EAAEM,eAAe;MAAAif,MAAA;MAAAC,eAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}