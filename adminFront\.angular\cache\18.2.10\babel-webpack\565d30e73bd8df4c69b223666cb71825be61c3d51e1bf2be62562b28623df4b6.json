{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { KpiCardsComponent } from './components/kpi-cards/kpi-cards.component';\nimport { ProgressChartComponent } from './components/progress-chart/progress-chart.component';\nimport { PaymentChartComponent } from './components/payment-chart/payment-chart.component';\nimport { QuotationTrendsComponent } from './components/quotation-trends/quotation-trends.component';\nimport { HeatmapChartComponent } from './components/heatmap-chart/heatmap-chart.component';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport * as i0 from \"@angular/core\";\nexport class HomeComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"ngx-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 20,\n      vars: 0,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"dashboard-subtitle\"], [1, \"dashboard-section\"], [1, \"chart-grid\"], [1, \"chart-item\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"\\u71DF\\u904B\\u5100\\u8868\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 2);\n          i0.ɵɵtext(5, \"\\u5EFA\\u6848\\u9032\\u5EA6\\u8207\\u71DF\\u904B\\u72C0\\u614B\\u7E3D\\u89BD\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"section\", 3);\n          i0.ɵɵelement(7, \"app-kpi-cards\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"section\", 3)(9, \"div\", 4)(10, \"div\", 5);\n          i0.ɵɵelement(11, \"app-progress-chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 5);\n          i0.ɵɵelement(13, \"app-payment-chart\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"section\", 3)(15, \"div\", 4)(16, \"div\", 5);\n          i0.ɵɵelement(17, \"app-quotation-trends\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 5);\n          i0.ɵɵelement(19, \"app-heatmap-chart\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      dependencies: [RouterModule, CommonModule, NgxEchartsModule, KpiCardsComponent, ProgressChartComponent, PaymentChartComponent, QuotationTrendsComponent, HeatmapChartComponent],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  text-align: center;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin: 0 0 8px 0;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .dashboard-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #7f8c8d;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n\\n.dashboard-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.dashboard-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.chart-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 24px;\\n}\\n@media (max-width: 1200px) {\\n  .chart-grid[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .chart-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n}\\n\\n.chart-item[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n[_nghost-%COMP%]     .dashboard-card {\\n  background: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #f0f0f0;\\n  transition: all 0.3s ease;\\n}\\n[_nghost-%COMP%]     .dashboard-card:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n\\n@media (max-width: 1600px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%] {\\n    margin-bottom: 24px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 28px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%]   .dashboard-subtitle[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .dashboard-section[_ngcontent-%COMP%] {\\n    margin-bottom: 24px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterModule", "CommonModule", "KpiCardsComponent", "ProgressChartComponent", "PaymentChartComponent", "QuotationTrendsComponent", "HeatmapChartComponent", "NgxEchartsModule", "HomeComponent", "constructor", "ngOnInit", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { KpiCardsComponent } from './components/kpi-cards/kpi-cards.component';\r\nimport { ProgressChartComponent } from './components/progress-chart/progress-chart.component';\r\nimport { PaymentChartComponent } from './components/payment-chart/payment-chart.component';\r\nimport { QuotationTrendsComponent } from './components/quotation-trends/quotation-trends.component';\r\nimport { HeatmapChartComponent } from './components/heatmap-chart/heatmap-chart.component';\r\nimport { BuildCaseSelectorComponent } from './components/build-case-selector/build-case-selector.component';\r\nimport { NgxEchartsModule } from 'ngx-echarts';\r\n\r\n@Component({\r\n    selector: 'ngx-home',\r\n    templateUrl: './home.component.html',\r\n    styleUrls: ['./home.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        RouterModule,\r\n        CommonModule,\r\n        NgxEchartsModule,\r\n        KpiCardsComponent,\r\n        ProgressChartComponent,\r\n        PaymentChartComponent,\r\n        QuotationTrendsComponent,\r\n        HeatmapChartComponent\r\n    ]\r\n})\r\nexport class HomeComponent implements OnInit {\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n}\r\n", "<div class=\"dashboard-container\">\n  <!-- 頁面標題 -->\n  <div class=\"dashboard-header\">\n    <h1>營運儀表板</h1>\n    <p class=\"dashboard-subtitle\">建案進度與營運狀態總覽</p>\n  </div>\n\n  <!-- 第一排：KPI 關鍵指標卡片 -->\n  <section class=\"dashboard-section\">\n    <app-kpi-cards></app-kpi-cards>\n  </section>\n\n  <!-- 第二排：業務狀態監控 -->\n  <section class=\"dashboard-section\">\n    <div class=\"chart-grid\">\n      <div class=\"chart-item\">\n        <app-progress-chart></app-progress-chart>\n      </div>\n      <div class=\"chart-item\">\n        <app-payment-chart></app-payment-chart>\n      </div>\n    </div>\n  </section>\n\n  <!-- 第三排：詳細分析 -->\n  <section class=\"dashboard-section\">\n    <div class=\"chart-grid\">\n      <div class=\"chart-item\">\n        <app-quotation-trends></app-quotation-trends>\n      </div>\n      <div class=\"chart-item\">\n        <app-heatmap-chart></app-heatmap-chart>\n      </div>\n    </div>\n  </section>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,wBAAwB,QAAQ,0DAA0D;AACnG,SAASC,qBAAqB,QAAQ,oDAAoD;AAE1F,SAASC,gBAAgB,QAAQ,aAAa;;AAkB9C,OAAM,MAAOC,aAAa;EAExBC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;;;uCALWF,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxBtBP,EAHJ,CAAAS,cAAA,aAAiC,aAED,SACxB;UAAAT,EAAA,CAAAU,MAAA,qCAAK;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACdX,EAAA,CAAAS,cAAA,WAA8B;UAAAT,EAAA,CAAAU,MAAA,yEAAW;UAC3CV,EAD2C,CAAAW,YAAA,EAAI,EACzC;UAGNX,EAAA,CAAAS,cAAA,iBAAmC;UACjCT,EAAA,CAAAY,SAAA,oBAA+B;UACjCZ,EAAA,CAAAW,YAAA,EAAU;UAKNX,EAFJ,CAAAS,cAAA,iBAAmC,aACT,cACE;UACtBT,EAAA,CAAAY,SAAA,0BAAyC;UAC3CZ,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAS,cAAA,cAAwB;UACtBT,EAAA,CAAAY,SAAA,yBAAuC;UAG7CZ,EAFI,CAAAW,YAAA,EAAM,EACF,EACE;UAKNX,EAFJ,CAAAS,cAAA,kBAAmC,cACT,cACE;UACtBT,EAAA,CAAAY,SAAA,4BAA6C;UAC/CZ,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAS,cAAA,cAAwB;UACtBT,EAAA,CAAAY,SAAA,yBAAuC;UAI/CZ,EAHM,CAAAW,YAAA,EAAM,EACF,EACE,EACN;;;qBDlBEzB,YAAY,EACZC,YAAY,EACZM,gBAAgB,EAChBL,iBAAiB,EACjBC,sBAAsB,EACtBC,qBAAqB,EACrBC,wBAAwB,EACxBC,qBAAqB;MAAAqB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}