{"ast": null, "code": "import { options as n } from \"preact\";\nvar t,\n  r,\n  u,\n  i,\n  o = 0,\n  f = [],\n  c = [],\n  e = n.__b,\n  a = n.__r,\n  v = n.diffed,\n  l = n.__c,\n  m = n.unmount;\nfunction d(t, u) {\n  n.__h && n.__h(r, t, o || u), o = 0;\n  var i = r.__H || (r.__H = {\n    __: [],\n    __h: []\n  });\n  return t >= i.__.length && i.__.push({\n    __V: c\n  }), i.__[t];\n}\nfunction p(n) {\n  return o = 1, y(B, n);\n}\nfunction y(n, u, i) {\n  var o = d(t++, 2);\n  if (o.t = n, !o.__c && (o.__ = [i ? i(u) : B(void 0, u), function (n) {\n    var t = o.__N ? o.__N[0] : o.__[0],\n      r = o.t(t, n);\n    t !== r && (o.__N = [r, o.__[1]], o.__c.setState({}));\n  }], o.__c = r, !r.u)) {\n    r.u = !0;\n    var f = r.shouldComponentUpdate;\n    r.shouldComponentUpdate = function (n, t, r) {\n      if (!o.__c.__H) return !0;\n      var u = o.__c.__H.__.filter(function (n) {\n        return n.__c;\n      });\n      if (u.every(function (n) {\n        return !n.__N;\n      })) return !f || f.call(this, n, t, r);\n      var i = !1;\n      return u.forEach(function (n) {\n        if (n.__N) {\n          var t = n.__[0];\n          n.__ = n.__N, n.__N = void 0, t !== n.__[0] && (i = !0);\n        }\n      }), !(!i && o.__c.props === n) && (!f || f.call(this, n, t, r));\n    };\n  }\n  return o.__N || o.__;\n}\nfunction h(u, i) {\n  var o = d(t++, 3);\n  !n.__s && z(o.__H, i) && (o.__ = u, o.i = i, r.__H.__h.push(o));\n}\nfunction s(u, i) {\n  var o = d(t++, 4);\n  !n.__s && z(o.__H, i) && (o.__ = u, o.i = i, r.__h.push(o));\n}\nfunction _(n) {\n  return o = 5, F(function () {\n    return {\n      current: n\n    };\n  }, []);\n}\nfunction A(n, t, r) {\n  o = 6, s(function () {\n    return \"function\" == typeof n ? (n(t()), function () {\n      return n(null);\n    }) : n ? (n.current = t(), function () {\n      return n.current = null;\n    }) : void 0;\n  }, null == r ? r : r.concat(n));\n}\nfunction F(n, r) {\n  var u = d(t++, 7);\n  return z(u.__H, r) ? (u.__V = n(), u.i = r, u.__h = n, u.__V) : u.__;\n}\nfunction T(n, t) {\n  return o = 8, F(function () {\n    return n;\n  }, t);\n}\nfunction q(n) {\n  var u = r.context[n.__c],\n    i = d(t++, 9);\n  return i.c = n, u ? (null == i.__ && (i.__ = !0, u.sub(r)), u.props.value) : n.__;\n}\nfunction x(t, r) {\n  n.useDebugValue && n.useDebugValue(r ? r(t) : t);\n}\nfunction P(n) {\n  var u = d(t++, 10),\n    i = p();\n  return u.__ = n, r.componentDidCatch || (r.componentDidCatch = function (n, t) {\n    u.__ && u.__(n, t), i[1](n);\n  }), [i[0], function () {\n    i[1](void 0);\n  }];\n}\nfunction V() {\n  var n = d(t++, 11);\n  if (!n.__) {\n    for (var u = r.__v; null !== u && !u.__m && null !== u.__;) u = u.__;\n    var i = u.__m || (u.__m = [0, 0]);\n    n.__ = \"P\" + i[0] + \"-\" + i[1]++;\n  }\n  return n.__;\n}\nfunction b() {\n  for (var t; t = f.shift();) if (t.__P && t.__H) try {\n    t.__H.__h.forEach(k), t.__H.__h.forEach(w), t.__H.__h = [];\n  } catch (r) {\n    t.__H.__h = [], n.__e(r, t.__v);\n  }\n}\nn.__b = function (n) {\n  r = null, e && e(n);\n}, n.__r = function (n) {\n  a && a(n), t = 0;\n  var i = (r = n.__c).__H;\n  i && (u === r ? (i.__h = [], r.__h = [], i.__.forEach(function (n) {\n    n.__N && (n.__ = n.__N), n.__V = c, n.__N = n.i = void 0;\n  })) : (i.__h.forEach(k), i.__h.forEach(w), i.__h = [])), u = r;\n}, n.diffed = function (t) {\n  v && v(t);\n  var o = t.__c;\n  o && o.__H && (o.__H.__h.length && (1 !== f.push(o) && i === n.requestAnimationFrame || ((i = n.requestAnimationFrame) || j)(b)), o.__H.__.forEach(function (n) {\n    n.i && (n.__H = n.i), n.__V !== c && (n.__ = n.__V), n.i = void 0, n.__V = c;\n  })), u = r = null;\n}, n.__c = function (t, r) {\n  r.some(function (t) {\n    try {\n      t.__h.forEach(k), t.__h = t.__h.filter(function (n) {\n        return !n.__ || w(n);\n      });\n    } catch (u) {\n      r.some(function (n) {\n        n.__h && (n.__h = []);\n      }), r = [], n.__e(u, t.__v);\n    }\n  }), l && l(t, r);\n}, n.unmount = function (t) {\n  m && m(t);\n  var r,\n    u = t.__c;\n  u && u.__H && (u.__H.__.forEach(function (n) {\n    try {\n      k(n);\n    } catch (n) {\n      r = n;\n    }\n  }), u.__H = void 0, r && n.__e(r, u.__v));\n};\nvar g = \"function\" == typeof requestAnimationFrame;\nfunction j(n) {\n  var t,\n    r = function () {\n      clearTimeout(u), g && cancelAnimationFrame(t), setTimeout(n);\n    },\n    u = setTimeout(r, 100);\n  g && (t = requestAnimationFrame(r));\n}\nfunction k(n) {\n  var t = r,\n    u = n.__c;\n  \"function\" == typeof u && (n.__c = void 0, u()), r = t;\n}\nfunction w(n) {\n  var t = r;\n  n.__c = n.__(), r = t;\n}\nfunction z(n, t) {\n  return !n || n.length !== t.length || t.some(function (t, r) {\n    return t !== n[r];\n  });\n}\nfunction B(n, t) {\n  return \"function\" == typeof t ? t(n) : t;\n}\nexport { T as useCallback, q as useContext, x as useDebugValue, h as useEffect, P as useErrorBoundary, V as useId, A as useImperativeHandle, s as useLayoutEffect, F as useMemo, y as useReducer, _ as useRef, p as useState };\n//# sourceMappingURL=hooks.module.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}