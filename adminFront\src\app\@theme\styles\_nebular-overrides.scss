/**
 * Nebular 組件樣式覆蓋 - 金色主題
 * 統一所有 Nebular 組件的色彩和樣式
 */

@import '_colors';

// ===== 全域 Nebular 組件覆蓋 =====
::ng-deep {

  // ===== 狀態色彩覆蓋 =====
  // 成功狀態使用綠色
  [class*="status-success"],
  [class*="accent-success"],
  .status-success,
  .accent-success {
    border-color: $success-base !important;

    &:not(.btn):not(button) {
      background-color: $success-light !important;
      color: $success-dark !important;
    }
  }

  // 警告狀態使用金色
  [class*="status-warning"],
  [class*="accent-warning"],
  .status-warning,
  .accent-warning {
    border-color: $primary-gold-light !important;

    &:not(.btn):not(button) {
      background-color: alpha-gold(0.1) !important;
      color: $primary-gold-dark !important;
    }
  }

  // 錯誤狀態使用紅色
  [class*="status-danger"],
  [class*="accent-danger"],
  .status-danger,
  .accent-danger {
    border-color: $error-base !important;

    &:not(.btn):not(button) {
      background-color: $error-light !important;
      color: $error-dark !important;
    }
  }

  [class*="status-info"],
  [class*="accent-info"],
  .status-info,
  .accent-info {
    border-color: $primary-gold-light !important;

    &:not(.btn):not(button) {
      background-color: alpha-gold(0.1) !important;
      color: $primary-gold-dark !important;
    }
  }

  // ===== 按鈕組件 =====
  nb-button {
    &.appearance-filled {
      &.status-primary {
        background: $gradient-primary;
        border-color: $primary-gold-light;
        color: $text-light;
        transition: $transition-normal;

        &:hover:not([disabled]) {
          background: $gradient-primary-hover;
          border-color: $primary-gold-hover;
          transform: translateY(-1px);
          box-shadow: $shadow-md;
        }

        &:active {
          transform: translateY(0);
          box-shadow: $shadow-sm;
        }

        &:focus {
          box-shadow: $shadow-focus;
        }

        &[disabled] {
          background: $primary-gold-disabled;
          border-color: $primary-gold-disabled;
          color: $text-muted;
        }
      }

      &.status-success {
        background: $success-gradient;
        border-color: $success-base;
        color: $text-light;
      }

      &.status-warning {
        background: $gradient-primary; // 警告使用金色
        border-color: $primary-gold-light;
        color: $text-light;
      }

      &.status-danger {
        background: $error-gradient;
        border-color: $error-base;
        color: $text-light;
      }

      &.status-info {
        background: $gradient-primary; // 統一使用金色
        border-color: $primary-gold-light;
        color: $text-light;
      }
    }

    &.appearance-outline {
      &.status-primary {
        color: $primary-gold-light;
        border-color: $primary-gold-light;
        background: transparent;

        &:hover:not([disabled]) {
          background: $gradient-primary;
          color: $text-light;
          transform: translateY(-1px);
          box-shadow: $shadow-md;
        }

        &:focus {
          box-shadow: $shadow-focus;
        }
      }
    }

    &.appearance-ghost {
      &.status-primary {
        color: $primary-gold-light;

        &:hover:not([disabled]) {
          background: alpha-gold(0.1);
          color: $primary-gold-dark;
        }
      }
    }
  }

  // ===== 輸入框組件 =====
  nb-form-field {
    &.appearance-outline {
      .form-control {
        border-color: $border-primary;
        transition: $transition-normal;

        &:focus {
          border-color: $primary-gold-light;
          box-shadow: $shadow-focus;
        }

        &:hover:not(:focus) {
          border-color: $border-focus;
        }
      }

      &.status-primary {
        .form-control:focus {
          border-color: $primary-gold-light;
          box-shadow: $shadow-focus;
        }
      }

      &.status-success {
        .form-control {
          border-color: $success-base;
        }
      }

      &.status-warning {
        .form-control {
          border-color: $primary-gold-light; // 警告使用金色
        }
      }

      &.status-danger {
        .form-control {
          border-color: $error-base;
        }
      }

      &.status-info {
        .form-control {
          border-color: $primary-gold-light; // 統一使用金色
        }
      }
    }

    // 標籤樣式
    .label {
      color: $text-secondary;
      font-weight: 500;
    }

    // 圖標樣式
    nb-icon {
      color: $primary-gold-light;
    }
  }

  // ===== 選擇框組件 =====
  nb-select {
    &.appearance-outline {
      .select-button {
        border-color: $border-primary;
        transition: $transition-normal;

        &:focus {
          border-color: $primary-gold-light;
          box-shadow: $shadow-focus;
        }

        &:hover:not(.focused) {
          border-color: $border-focus;
        }
      }

      &.status-primary {
        .select-button:focus {
          border-color: $primary-gold-light;
          box-shadow: $shadow-focus;
        }
      }
    }

    // 下拉選項
    .options-list {
      border-color: $border-primary;
      box-shadow: $shadow-lg;

      nb-option {
        &:hover {
          background-color: alpha-gold(0.1);
        }

        &.selected {
          background-color: alpha-gold(0.15);
          color: $primary-gold-dark;
        }
      }
    }
  }

  // ===== 複選框組件 =====
  nb-checkbox {
    .customised-control-indicator {
      border-color: $border-primary;
      transition: $transition-normal;
    }

    .customised-control-input:checked~.customised-control-indicator {
      background: $gradient-primary;
      border-color: $primary-gold-light;
    }

    .customised-control-input:focus~.customised-control-indicator {
      box-shadow: $shadow-focus;
    }

    &:hover .customised-control-indicator {
      border-color: $primary-gold-light;
    }

    .customised-control-description {
      color: $text-secondary;
    }
  }

  // ===== 單選框組件 =====
  nb-radio {
    .radio-indicator {
      border-color: $border-primary;
      transition: $transition-normal;
    }

    .radio-input:checked~.radio-indicator {
      background: $gradient-primary;
      border-color: $primary-gold-light;

      &::before {
        background-color: $text-light;
      }
    }

    .radio-input:focus~.radio-indicator {
      box-shadow: $shadow-focus;
    }

    &:hover .radio-indicator {
      border-color: $primary-gold-light;
    }

    .radio-description {
      color: $text-secondary;
    }
  }

  // ===== 卡片組件 =====
  nb-card {
    border: 1px solid $border-light;
    box-shadow: $shadow-sm;
    transition: $transition-normal;

    &:hover {
      box-shadow: $shadow-md;
    }

    // 強制覆蓋所有 accent 屬性的顏色
    &[accent="primary"],
    &.accent-primary {
      border-top: 3px solid $primary-gold-light !important;
    }

    &[accent="success"],
    &.accent-success {
      border-top: 3px solid $success-base !important;
    }

    &[accent="warning"],
    &.accent-warning {
      border-top: 3px solid $primary-gold-light !important;
    }

    &[accent="danger"],
    &.accent-danger {
      border-top: 3px solid $error-base !important;
    }

    &[accent="info"],
    &.accent-info {
      border-top: 3px solid $info-base !important;
    }

    nb-card-header {
      border-bottom: 1px solid $border-light;
      background: $bg-secondary;
      color: $text-primary;

      &.status-primary {
        background: $gradient-primary;
        color: $text-light;
      }
    }

    nb-card-footer {
      border-top: 1px solid $border-light;
      background: $bg-secondary;
    }
  }

  // ===== 標籤頁組件 =====
  nb-tabset {
    .tabset {
      .tab {
        color: $text-secondary;
        border-bottom: 2px solid transparent;
        transition: $transition-normal;

        &:hover {
          color: $primary-gold-light;
        }

        &.active {
          color: $primary-gold-light;
          border-bottom-color: $primary-gold-light;
        }
      }
    }
  }

  // ===== 日期選擇器組件 =====
  nb-calendar {
    .calendar-day-cell {
      &.selected {
        background: $gradient-primary;
        color: $text-light;
      }

      &.today {
        border-color: $primary-gold-light;
        color: $primary-gold-light;
      }

      &:hover:not(.selected):not(.disabled) {
        background: alpha-gold(0.1);
        color: $primary-gold-dark;
      }
    }

    .calendar-month-cell,
    .calendar-year-cell {
      &.selected {
        background: $gradient-primary;
        color: $text-light;
      }

      &:hover:not(.selected):not(.disabled) {
        background: alpha-gold(0.1);
        color: $primary-gold-dark;
      }
    }
  }

  // ===== 進度條組件 =====
  nb-progress-bar {
    .progress-container {
      background-color: $bg-secondary;

      .progress-value {
        background: $gradient-primary;
      }
    }
  }

  // ===== 提示框組件 =====
  nb-tooltip {
    background-color: $text-primary;
    color: $text-light;
    box-shadow: $shadow-md;
  }

  // ===== 對話框組件 =====
  nb-dialog-container {
    .dialog-content {
      box-shadow: $shadow-xl;
    }
  }

  // ===== 側邊欄組件 =====
  nb-sidebar {
    &.left {
      border-right: 1px solid $border-light;
    }

    &.right {
      border-left: 1px solid $border-light;
    }

    .menu-item {
      &.selected {
        background: alpha-gold(0.1);
        color: $primary-gold-dark;
      }

      &:hover:not(.selected) {
        background: alpha-gold(0.05);
      }
    }
  }

  // ===== 表格組件 =====
  nb-tree-grid,
  table {
    th {
      background: $table-header-bg;
      color: $text-secondary;
      border-bottom: 1px solid $table-border;
    }

    td {
      border-bottom: 1px solid $table-border;
    }

    tr {
      &:hover {
        background: $table-row-hover;
      }

      &.selected {
        background: $table-row-selected;
      }
    }
  }

  // ===== 標籤組件 =====
  nb-tag {
    &.status-primary {
      background: alpha-gold(0.15);
      color: $primary-gold-dark;
      border-color: $primary-gold-light;
    }

    &.status-success {
      background: $success-light;
      color: $success-dark;
      border-color: $success-base;
    }

    &.status-warning {
      background: alpha-gold(0.15); // 警告使用金色
      color: $primary-gold-dark;
      border-color: $primary-gold-light;
    }

    &.status-danger {
      background: $error-light;
      color: $error-dark;
      border-color: $error-base;
    }

    &.status-info {
      background: alpha-gold(0.15); // 統一使用金色
      color: $primary-gold-dark;
      border-color: $primary-gold-light;
    }
  }

  // ===== 手風琴組件 =====
  nb-accordion {
    nb-accordion-item {
      border-color: $border-light;

      nb-accordion-item-header {
        &:hover {
          background: alpha-gold(0.05);
        }

        &.expanded {
          background: alpha-gold(0.1);
          color: $primary-gold-dark;
        }
      }
    }
  }

  // ===== 額外的強制覆蓋 =====
  // 針對可能遺漏的組件進行強制覆蓋

  // 所有帶有狀態的邊框
  [class*="border-success"] {
    border-color: $success-base !important;
  }

  [class*="border-warning"] {
    border-color: $primary-gold-light !important;
  }

  [class*="border-danger"] {
    border-color: $error-base !important;
  }

  [class*="border-info"] {
    border-color: $info-base !important;
  }

  // 所有帶有狀態的背景
  [class*="bg-success"] {
    background-color: $success-light !important;
    color: $success-dark !important;
  }

  [class*="bg-warning"] {
    background-color: alpha-gold(0.1) !important;
    color: $primary-gold-dark !important;
  }

  [class*="bg-danger"] {
    background-color: $error-light !important;
    color: $error-dark !important;
  }

  [class*="bg-info"] {
    background-color: $info-light !important;
    color: $info-dark !important;
  }

  // 所有帶有狀態的文字
  [class*="text-success"] {
    color: $success-dark !important;
  }

  [class*="text-warning"] {
    color: $primary-gold-dark !important;
  }

  [class*="text-danger"] {
    color: $error-dark !important;
  }

  [class*="text-info"] {
    color: $info-dark !important;
  }

  // Nebular 特定的狀態類別
  .nb-theme-default {
    [class*="color-success"] {
      color: $success-base !important;
    }

    [class*="color-warning"] {
      color: $primary-gold-light !important;
    }

    [class*="color-danger"] {
      color: $error-base !important;
    }

    [class*="color-info"] {
      color: $info-base !important;
    }
  }

  // 確保所有 Nebular 組件的狀態色彩正確
  nb-alert,
  nb-chat,
  nb-progress-bar,
  nb-spinner,
  nb-stepper,
  nb-toggle {

    &.status-success {
      color: $success-dark !important;

      * {
        border-color: $success-base !important;
        background-color: $success-light !important;
      }
    }

    &.status-warning {
      color: $primary-gold-dark !important;

      * {
        border-color: $primary-gold-light !important;
        background-color: alpha-gold(0.1) !important;
      }
    }

    &.status-danger {
      color: $error-dark !important;

      * {
        border-color: $error-base !important;
        background-color: $error-light !important;
      }
    }

    &.status-info {
      color: $info-dark !important;

      * {
        border-color: $info-base !important;
        background-color: $info-light !important;
      }
    }
  }

  // 特別針對狀態元素
  .success {
    color: $success-dark !important;
    border-color: $success-base !important;
    background-color: $success-light !important;
  }

  .warning {
    color: $primary-gold-dark !important;
    border-color: $primary-gold-light !important;
    background-color: alpha-gold(0.1) !important;
  }

  .danger,
  .error {
    color: $error-dark !important;
    border-color: $error-base !important;
    background-color: $error-light !important;
  }

  .info {
    color: $info-dark !important;
    border-color: $info-base !important;
    background-color: $info-light !important;
  }

  // 針對 Nebular Toast 圖示的特殊覆蓋
  nb-toast {
    &.status-success {

      .icon-container svg,
      .icon svg,
      nb-icon svg,
      .status-icon svg {
        fill: $success-base !important;
        color: $success-base !important;
      }
    }

    &.status-danger {

      .icon-container svg,
      .icon svg,
      nb-icon svg,
      .status-icon svg {
        fill: $error-base !important;
        color: $error-base !important;
      }
    }

    &.status-warning {

      .icon-container svg,
      .icon svg,
      nb-icon svg,
      .status-icon svg {
        fill: $primary-gold-light !important;
        color: $primary-gold-light !important;
      }
    }

    &.status-info {

      .icon-container svg,
      .icon svg,
      nb-icon svg,
      .status-icon svg {
        fill: $info-base !important;
        color: $info-base !important;
      }
    }
  }
}