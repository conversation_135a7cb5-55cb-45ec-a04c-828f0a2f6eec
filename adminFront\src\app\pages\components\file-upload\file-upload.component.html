<div class="form-group d-flex align-items-center">
  <div class="form-group d-flex align-items-center w-full">
    <div class="d-flex flex-col mr-3">
      <label for="file" class="label col-3" [class.required-field]="config.required" [style.min-width]="labelMinWidth">
        {{ config.label }}
      </label>
      <h3 style="color:red;" *ngIf="config.helpText">{{ config.helpText }}</h3>
    </div>

    <div class="flex flex-col col-9 px-0 items-start">
      <input #fileInput type="file" id="fileInput" [accept]="config.acceptAttribute" [multiple]="config.multiple"
        class="hidden" style="display: none" (change)="onFileSelected($event)" [disabled]="config.disabled">

      <label for="fileInput" [class.opacity-50]="config.disabled" [class.cursor-pointer]="!config.disabled"
        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" *ngIf="!config.hideSelectButton">
        <i [class]="config.buttonIcon + ' mr-2'"></i> {{ config.buttonText }}
      </label>

      <!-- 單檔案模式：已選擇的檔案顯示 -->
      <div class="flex items-center space-x-2 mt-2" *ngIf="fileName && !config.multiple">
        <span class="text-gray-600">{{ fileName }}</span>
        <button type="button" (click)="clearFile()" class="text-red-500 hover:text-red-700"
          [disabled]="config.disabled">
          <i class="fa-solid fa-trash"></i>
        </button>
      </div>

      <!-- 單檔案模式：已存在的檔案連結 -->
      <div class="w-full mt-2" *ngIf="currentFileUrl && !fileName && !config.multiple">
        <div class="flex flex-wrap mt-2 file-type-container">
          <div class="relative w-28 h-28 mr-3 mb-6 file-item">
            <div
              class="w-full h-full border border-gray-300 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200">

              <!-- 圖片類型 - 顯示實際圖片 -->
              <div *ngIf="isImageFile(currentFileUrl) && !isPDFString(currentFileUrl) && !isCadString(currentFileUrl)"
                class="w-full h-full relative rounded-lg overflow-hidden">
                <img class="w-full h-full object-cover cursor-pointer" [src]="currentFileUrl"
                  (click)="openFile(currentFileUrl)" [title]="'點擊放大預覽: ' + currentFileUrl"
                  (error)="onImageError($event, {CFileName: currentFileUrl})">
                <div class="absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded file-type-label">
                  <i class="fa fa-image mr-1"></i>{{ getFileTypeIcon({CFileName: currentFileUrl}).label }}
                </div>
              </div>

              <!-- 非圖片類型 - 顯示檔案類型圖示 -->
              <div *ngIf="!isImageFile(currentFileUrl) || isPDFString(currentFileUrl) || isCadString(currentFileUrl)"
                class="w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg relative"
                [ngClass]="getFileTypeIcon({CFileName: currentFileUrl}).bgColor" (click)="openFile(currentFileUrl)"
                [title]="'點擊開啟: ' + currentFileUrl">

                <!-- 檔案圖示 -->
                <i
                  [class]="getFileTypeIcon({CFileName: currentFileUrl}).icon + ' text-4xl mb-2 ' + getFileTypeIcon({CFileName: currentFileUrl}).color"></i>

                <!-- 檔案類型標籤 -->
                <span [class]="'text-xs font-medium ' + getFileTypeIcon({CFileName: currentFileUrl}).color">
                  {{ getFileTypeIcon({CFileName: currentFileUrl}).label }}
                </span>
              </div>
            </div>

            <!-- 檔案名稱 -->
            <p class="absolute -bottom-6 left-0 w-full text-xs truncate px-1 text-center text-gray-600"
              [title]="currentFileUrl">{{ currentFileUrl }}</p>
          </div>
        </div>
      </div> <!-- 多檔案模式：檔案預覽和列表 -->
      <div class="w-full mt-2" *ngIf="config.multiple && config.showPreview">
        <!-- 上傳的檔案預覽 -->
        <div class="flex flex-wrap mt-2 file-type-container" *ngIf="fileList && fileList.length > 0">
          <div *ngFor="let file of fileList; let i = index" class="relative w-28 h-28 mr-3 mb-6 file-item">
            <div
              class="w-full h-full border border-gray-300 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200">

              <!-- 圖片類型 - 顯示實際圖片 -->
              <div
                *ngIf="isImage(file.CFileType) || (isImageFile(file.CFileName) && !isPdf(file.CFileType) && !isCad(file.CFileType))"
                class="w-full h-full relative rounded-lg overflow-hidden">
                <img class="w-full h-full object-cover cursor-pointer" [src]="getImageSrc(file)"
                  (click)="handleFileClick(file)" [title]="'點擊放大預覽: ' + file.CFileName"
                  (error)="onImageError($event, file)">
                <div class="absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded file-type-label">
                  <i class="fa fa-image mr-1"></i>{{ getFileTypeIcon(file).label }}
                </div>
              </div>

              <!-- 非圖片類型 - 顯示檔案類型圖示 -->
              <div
                *ngIf="!isImage(file.CFileType) && (!isImageFile(file.CFileName) || isPdf(file.CFileType) || isCad(file.CFileType))"
                class="w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg relative"
                [ngClass]="getFileTypeIcon(file).bgColor" (click)="handleFileClick(file)"
                [title]="'點擊開啟: ' + file.CFileName">

                <!-- 檔案圖示 -->
                <i [class]="getFileTypeIcon(file).icon + ' text-4xl mb-2 ' + getFileTypeIcon(file).color"></i>

                <!-- 檔案類型標籤 -->
                <span [class]="'text-xs font-medium ' + getFileTypeIcon(file).color">
                  {{ getFileTypeIcon(file).label }}
                </span>

                <!-- 檔案大小 -->
                <span class="text-xs text-gray-500 mt-1" *ngIf="getFileSize(file)">
                  {{ getFileSize(file) }}
                </span>
              </div>
            </div>

            <!-- 檔案名稱 -->
            <p class="absolute -bottom-6 left-0 w-full text-xs truncate px-1 text-center text-gray-600"
              [title]="file.CFileName">{{ file.CFileName }}</p>

            <!-- 移除按鈕 -->
            <span
              class="absolute -top-2 -right-2 cursor-pointer bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 transition-colors duration-200 remove-btn"
              (click)="removeFile(i)" title="移除檔案" [class.hidden]="config.disabled">
              <i class="fa fa-times text-xs"></i>
            </span>
          </div>
        </div> <!-- 已存在的檔案預覽（編輯模式） -->
        <div class="flex flex-wrap mt-2 file-type-container" *ngIf="existingFiles && existingFiles.length > 0">
          <div *ngFor="let file of existingFiles; let i = index" class="relative w-28 h-28 mr-3 mb-6 file-item">
            <div
              class="w-full h-full border border-gray-300 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200">

              <!-- 圖片類型 - 顯示實際圖片 -->
              <div
                *ngIf="isImageFile(file.CFileName || file.CFile) && !isPDFString(file.CFileName || file.CFile) && !isCadString(file.CFileName || file.CFile)"
                class="w-full h-full relative rounded-lg overflow-hidden">
                <img class="w-full h-full object-cover cursor-pointer" [src]="getImageSrc(file)"
                  (click)="handleFileClick({CFileName: file.CFileName, CFile: file.CFile, data: getImageSrc(file), relativePath: file.relativePath, fileName: file.fileName})"
                  [title]="'點擊放大預覽: ' + file.CFileName" (error)="onImageError($event, file)">
                <div class="absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded file-type-label">
                  <i class="fa fa-image mr-1"></i>{{ getFileTypeIcon({CFileName: file.CFileName}).label }}
                </div>
              </div>

              <!-- 非圖片類型 - 顯示檔案類型圖示 -->
              <div
                *ngIf="!isImageFile(file.CFileName || file.CFile) || isPDFString(file.CFileName || file.CFile) || isCadString(file.CFileName || file.CFile)"
                class="w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg relative"
                [ngClass]="getFileTypeIcon({CFileName: file.CFileName}).bgColor"
                (click)="handleFileClick({CFileName: file.CFileName, CFile: file.CFile, data: file.CFile, relativePath: file.relativePath, fileName: file.fileName})"
                [title]="'點擊開啟: ' + file.CFileName">

                <!-- 檔案圖示 -->
                <i
                  [class]="getFileTypeIcon({CFileName: file.CFileName}).icon + ' text-4xl mb-2 ' + getFileTypeIcon({CFileName: file.CFileName}).color"></i>

                <!-- 檔案類型標籤 -->
                <span [class]="'text-xs font-medium ' + getFileTypeIcon({CFileName: file.CFileName}).color">
                  {{ getFileTypeIcon({CFileName: file.CFileName}).label }}
                </span>
              </div>
            </div>

            <!-- 檔案名稱 -->
            <p class="absolute -bottom-6 left-0 w-full text-xs truncate px-1 text-center text-gray-600"
              [title]="file.CFileName">{{ file.CFileName }}</p>
          </div>
        </div>
      </div> <!-- 多檔案模式：簡單列表模式 -->
      <div class="w-full mt-2" *ngIf="config.multiple && !config.showPreview && fileList && fileList.length > 0">
        <div class="space-y-2">
          <div *ngFor="let file of fileList; let i = index"
            class="flex items-center justify-between bg-gray-50 p-3 rounded-lg hover:bg-gray-100 transition-colors">
            <div class="flex items-center space-x-3 cursor-pointer" (click)="handleFileClick(file)">
              <!-- 檔案類型圖示 -->
              <div class="flex-shrink-0">
                <i [class]="getFileTypeIcon(file).icon + ' text-xl ' + getFileTypeIcon(file).color"></i>
              </div>

              <!-- 檔案資訊 -->
              <div class="flex-1 min-w-0">
                <span class="text-sm font-medium text-gray-700 truncate block">{{ file.CFileName }}</span>
                <div class="flex items-center space-x-2 text-xs text-gray-500">
                  <span>{{ getFileTypeIcon(file).label }}</span>
                  <span *ngIf="getFileSize(file)">• {{ getFileSize(file) }}</span>
                </div>
              </div>
            </div>

            <!-- 移除按鈕 -->
            <button type="button" (click)="removeFile(i)"
              class="text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50 transition-colors"
              [disabled]="config.disabled" [class.hidden]="config.disabled" title="移除檔案">
              <i class="fa fa-trash text-sm"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>