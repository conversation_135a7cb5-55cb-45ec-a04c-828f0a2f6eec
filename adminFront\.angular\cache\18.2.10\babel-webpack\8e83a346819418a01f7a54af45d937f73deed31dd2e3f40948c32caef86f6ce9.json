{"ast": null, "code": "//! moment.js locale configuration\n//! locale : French (Canada) [fr-ca]\n//! author : <PERSON> : https://github.com/jonbca\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var frCa = moment.defineLocale('fr-ca', {\n    months: 'janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre'.split('_'),\n    monthsShort: 'janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi'.split('_'),\n    weekdaysShort: 'dim._lun._mar._mer._jeu._ven._sam.'.split('_'),\n    weekdaysMin: 'di_lu_ma_me_je_ve_sa'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Aujourd’hui à] LT',\n      nextDay: '[Demain à] LT',\n      nextWeek: 'dddd [à] LT',\n      lastDay: '[Hier à] LT',\n      lastWeek: 'dddd [dernier à] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'dans %s',\n      past: 'il y a %s',\n      s: 'quelques secondes',\n      ss: '%d secondes',\n      m: 'une minute',\n      mm: '%d minutes',\n      h: 'une heure',\n      hh: '%d heures',\n      d: 'un jour',\n      dd: '%d jours',\n      M: 'un mois',\n      MM: '%d mois',\n      y: 'un an',\n      yy: '%d ans'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(er|e)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        // Words with masculine grammatical gender: mois, trimestre, jour\n        default:\n        case 'M':\n        case 'Q':\n        case 'D':\n        case 'DDD':\n        case 'd':\n          return number + (number === 1 ? 'er' : 'e');\n\n        // Words with feminine grammatical gender: semaine\n        case 'w':\n        case 'W':\n          return number + (number === 1 ? 're' : 'e');\n      }\n    }\n  });\n  return frCa;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}