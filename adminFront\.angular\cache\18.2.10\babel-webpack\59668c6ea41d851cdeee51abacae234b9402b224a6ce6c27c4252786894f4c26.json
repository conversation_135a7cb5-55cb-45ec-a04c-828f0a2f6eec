{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nimport { getLayoutParams, sizeCalculable, mergeLayoutParam } from '../../util/layout.js';\nvar CalendarModel = /** @class */function (_super) {\n  __extends(CalendarModel, _super);\n  function CalendarModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CalendarModel.type;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  CalendarModel.prototype.init = function (option, parentModel, ecModel) {\n    var inputPositionParams = getLayoutParams(option);\n    _super.prototype.init.apply(this, arguments);\n    mergeAndNormalizeLayoutParams(option, inputPositionParams);\n  };\n  /**\r\n   * @override\r\n   */\n  CalendarModel.prototype.mergeOption = function (option) {\n    _super.prototype.mergeOption.apply(this, arguments);\n    mergeAndNormalizeLayoutParams(this.option, option);\n  };\n  CalendarModel.prototype.getCellSize = function () {\n    // Has been normalized\n    return this.option.cellSize;\n  };\n  CalendarModel.type = 'calendar';\n  CalendarModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    left: 80,\n    top: 60,\n    cellSize: 20,\n    // horizontal vertical\n    orient: 'horizontal',\n    // month separate line style\n    splitLine: {\n      show: true,\n      lineStyle: {\n        color: '#000',\n        width: 1,\n        type: 'solid'\n      }\n    },\n    // rect style  temporarily unused emphasis\n    itemStyle: {\n      color: '#fff',\n      borderWidth: 1,\n      borderColor: '#ccc'\n    },\n    // week text style\n    dayLabel: {\n      show: true,\n      firstDay: 0,\n      // start end\n      position: 'start',\n      margin: '50%',\n      color: '#000'\n    },\n    // month text style\n    monthLabel: {\n      show: true,\n      // start end\n      position: 'start',\n      margin: 5,\n      // center or left\n      align: 'center',\n      formatter: null,\n      color: '#000'\n    },\n    // year text style\n    yearLabel: {\n      show: true,\n      // top bottom left right\n      position: null,\n      margin: 30,\n      formatter: null,\n      color: '#ccc',\n      fontFamily: 'sans-serif',\n      fontWeight: 'bolder',\n      fontSize: 20\n    }\n  };\n  return CalendarModel;\n}(ComponentModel);\nfunction mergeAndNormalizeLayoutParams(target, raw) {\n  // Normalize cellSize\n  var cellSize = target.cellSize;\n  var cellSizeArr;\n  if (!zrUtil.isArray(cellSize)) {\n    cellSizeArr = target.cellSize = [cellSize, cellSize];\n  } else {\n    cellSizeArr = cellSize;\n  }\n  if (cellSizeArr.length === 1) {\n    cellSizeArr[1] = cellSizeArr[0];\n  }\n  var ignoreSize = zrUtil.map([0, 1], function (hvIdx) {\n    // If user have set `width` or both `left` and `right`, cellSizeArr\n    // will be automatically set to 'auto', otherwise the default\n    // setting of cellSizeArr will make `width` setting not work.\n    if (sizeCalculable(raw, hvIdx)) {\n      cellSizeArr[hvIdx] = 'auto';\n    }\n    return cellSizeArr[hvIdx] != null && cellSizeArr[hvIdx] !== 'auto';\n  });\n  mergeLayoutParam(target, raw, {\n    type: 'box',\n    ignoreSize: ignoreSize\n  });\n}\nexport default CalendarModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "ComponentModel", "getLayoutParams", "sizeCalculable", "mergeLayoutParam", "CalendarModel", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "option", "parentModel", "ecModel", "inputPositionParams", "mergeAndNormalizeLayoutParams", "mergeOption", "getCellSize", "cellSize", "defaultOption", "z", "left", "top", "orient", "splitLine", "show", "lineStyle", "color", "width", "itemStyle", "borderWidth", "borderColor", "<PERSON><PERSON><PERSON><PERSON>", "firstDay", "position", "margin", "<PERSON><PERSON><PERSON><PERSON>", "align", "formatter", "<PERSON><PERSON><PERSON><PERSON>", "fontFamily", "fontWeight", "fontSize", "target", "raw", "cellSizeArr", "isArray", "length", "ignoreSize", "map", "hvIdx"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/coord/calendar/CalendarModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nimport { getLayoutParams, sizeCalculable, mergeLayoutParam } from '../../util/layout.js';\nvar CalendarModel = /** @class */function (_super) {\n  __extends(CalendarModel, _super);\n  function CalendarModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CalendarModel.type;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  CalendarModel.prototype.init = function (option, parentModel, ecModel) {\n    var inputPositionParams = getLayoutParams(option);\n    _super.prototype.init.apply(this, arguments);\n    mergeAndNormalizeLayoutParams(option, inputPositionParams);\n  };\n  /**\r\n   * @override\r\n   */\n  CalendarModel.prototype.mergeOption = function (option) {\n    _super.prototype.mergeOption.apply(this, arguments);\n    mergeAndNormalizeLayoutParams(this.option, option);\n  };\n  CalendarModel.prototype.getCellSize = function () {\n    // Has been normalized\n    return this.option.cellSize;\n  };\n  CalendarModel.type = 'calendar';\n  CalendarModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    left: 80,\n    top: 60,\n    cellSize: 20,\n    // horizontal vertical\n    orient: 'horizontal',\n    // month separate line style\n    splitLine: {\n      show: true,\n      lineStyle: {\n        color: '#000',\n        width: 1,\n        type: 'solid'\n      }\n    },\n    // rect style  temporarily unused emphasis\n    itemStyle: {\n      color: '#fff',\n      borderWidth: 1,\n      borderColor: '#ccc'\n    },\n    // week text style\n    dayLabel: {\n      show: true,\n      firstDay: 0,\n      // start end\n      position: 'start',\n      margin: '50%',\n      color: '#000'\n    },\n    // month text style\n    monthLabel: {\n      show: true,\n      // start end\n      position: 'start',\n      margin: 5,\n      // center or left\n      align: 'center',\n      formatter: null,\n      color: '#000'\n    },\n    // year text style\n    yearLabel: {\n      show: true,\n      // top bottom left right\n      position: null,\n      margin: 30,\n      formatter: null,\n      color: '#ccc',\n      fontFamily: 'sans-serif',\n      fontWeight: 'bolder',\n      fontSize: 20\n    }\n  };\n  return CalendarModel;\n}(ComponentModel);\nfunction mergeAndNormalizeLayoutParams(target, raw) {\n  // Normalize cellSize\n  var cellSize = target.cellSize;\n  var cellSizeArr;\n  if (!zrUtil.isArray(cellSize)) {\n    cellSizeArr = target.cellSize = [cellSize, cellSize];\n  } else {\n    cellSizeArr = cellSize;\n  }\n  if (cellSizeArr.length === 1) {\n    cellSizeArr[1] = cellSizeArr[0];\n  }\n  var ignoreSize = zrUtil.map([0, 1], function (hvIdx) {\n    // If user have set `width` or both `left` and `right`, cellSizeArr\n    // will be automatically set to 'auto', otherwise the default\n    // setting of cellSizeArr will make `width` setting not work.\n    if (sizeCalculable(raw, hvIdx)) {\n      cellSizeArr[hvIdx] = 'auto';\n    }\n    return cellSizeArr[hvIdx] != null && cellSizeArr[hvIdx] !== 'auto';\n  });\n  mergeLayoutParam(target, raw, {\n    type: 'box',\n    ignoreSize: ignoreSize\n  });\n}\nexport default CalendarModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,SAASC,eAAe,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,sBAAsB;AACxF,IAAIC,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjDP,SAAS,CAACM,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,aAAa,CAACK,IAAI;IAC/B,OAAOH,KAAK;EACd;EACA;AACF;AACA;EACEF,aAAa,CAACM,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACrE,IAAIC,mBAAmB,GAAGd,eAAe,CAACW,MAAM,CAAC;IACjDP,MAAM,CAACK,SAAS,CAACC,IAAI,CAACJ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC5CQ,6BAA6B,CAACJ,MAAM,EAAEG,mBAAmB,CAAC;EAC5D,CAAC;EACD;AACF;AACA;EACEX,aAAa,CAACM,SAAS,CAACO,WAAW,GAAG,UAAUL,MAAM,EAAE;IACtDP,MAAM,CAACK,SAAS,CAACO,WAAW,CAACV,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACnDQ,6BAA6B,CAAC,IAAI,CAACJ,MAAM,EAAEA,MAAM,CAAC;EACpD,CAAC;EACDR,aAAa,CAACM,SAAS,CAACQ,WAAW,GAAG,YAAY;IAChD;IACA,OAAO,IAAI,CAACN,MAAM,CAACO,QAAQ;EAC7B,CAAC;EACDf,aAAa,CAACK,IAAI,GAAG,UAAU;EAC/BL,aAAa,CAACgB,aAAa,GAAG;IAC5B;IACAC,CAAC,EAAE,CAAC;IACJC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,EAAE;IACPJ,QAAQ,EAAE,EAAE;IACZ;IACAK,MAAM,EAAE,YAAY;IACpB;IACAC,SAAS,EAAE;MACTC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE;QACTC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,CAAC;QACRpB,IAAI,EAAE;MACR;IACF,CAAC;IACD;IACAqB,SAAS,EAAE;MACTF,KAAK,EAAE,MAAM;MACbG,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE;IACf,CAAC;IACD;IACAC,QAAQ,EAAE;MACRP,IAAI,EAAE,IAAI;MACVQ,QAAQ,EAAE,CAAC;MACX;MACAC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,KAAK;MACbR,KAAK,EAAE;IACT,CAAC;IACD;IACAS,UAAU,EAAE;MACVX,IAAI,EAAE,IAAI;MACV;MACAS,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,CAAC;MACT;MACAE,KAAK,EAAE,QAAQ;MACfC,SAAS,EAAE,IAAI;MACfX,KAAK,EAAE;IACT,CAAC;IACD;IACAY,SAAS,EAAE;MACTd,IAAI,EAAE,IAAI;MACV;MACAS,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,EAAE;MACVG,SAAS,EAAE,IAAI;MACfX,KAAK,EAAE,MAAM;MACba,UAAU,EAAE,YAAY;MACxBC,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE;IACZ;EACF,CAAC;EACD,OAAOvC,aAAa;AACtB,CAAC,CAACJ,cAAc,CAAC;AACjB,SAASgB,6BAA6BA,CAAC4B,MAAM,EAAEC,GAAG,EAAE;EAClD;EACA,IAAI1B,QAAQ,GAAGyB,MAAM,CAACzB,QAAQ;EAC9B,IAAI2B,WAAW;EACf,IAAI,CAAC/C,MAAM,CAACgD,OAAO,CAAC5B,QAAQ,CAAC,EAAE;IAC7B2B,WAAW,GAAGF,MAAM,CAACzB,QAAQ,GAAG,CAACA,QAAQ,EAAEA,QAAQ,CAAC;EACtD,CAAC,MAAM;IACL2B,WAAW,GAAG3B,QAAQ;EACxB;EACA,IAAI2B,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;IAC5BF,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;EACjC;EACA,IAAIG,UAAU,GAAGlD,MAAM,CAACmD,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAUC,KAAK,EAAE;IACnD;IACA;IACA;IACA,IAAIjD,cAAc,CAAC2C,GAAG,EAAEM,KAAK,CAAC,EAAE;MAC9BL,WAAW,CAACK,KAAK,CAAC,GAAG,MAAM;IAC7B;IACA,OAAOL,WAAW,CAACK,KAAK,CAAC,IAAI,IAAI,IAAIL,WAAW,CAACK,KAAK,CAAC,KAAK,MAAM;EACpE,CAAC,CAAC;EACFhD,gBAAgB,CAACyC,MAAM,EAAEC,GAAG,EAAE;IAC5BpC,IAAI,EAAE,KAAK;IACXwC,UAAU,EAAEA;EACd,CAAC,CAAC;AACJ;AACA,eAAe7C,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}