{"ast": null, "code": "import { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { FormsModule } from '@angular/forms';\nimport { <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as moment from 'moment';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport interactionPlugin from '@fullcalendar/interaction';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport listPlugin from '@fullcalendar/list';\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\nimport { CalendarModule } from 'primeng/calendar';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"primeng/calendar\";\nconst _c0 = [\"calendar\"];\nfunction FinaldochouseManagementComponent_tr_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 26)(1, \"td\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 29)(6, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_tr_36_Template_button_click_6_listener() {\n      const data_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openPdfInNewTab(data_r4));\n    });\n    i0.ɵɵtext(7, \"\\u9023\\u7D50\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const data_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r4.CDocumentName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r4.CSignDate);\n  }\n}\nfunction FinaldochouseManagementComponent_ng_template_42_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"span\", 53);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_ng_template_42_div_13_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearFile());\n    });\n    i0.ɵɵelement(4, \"i\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.fileName);\n  }\n}\nfunction FinaldochouseManagementComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 31)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 32)(4, \"div\", 33)(5, \"div\", 34)(6, \"label\", 35);\n    i0.ɵɵtext(7, \"\\u6587\\u4EF6 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 36)(9, \"input\", 37);\n    i0.ɵɵlistener(\"change\", function FinaldochouseManagementComponent_ng_template_42_Template_input_change_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"label\", 38);\n    i0.ɵɵelement(11, \"i\", 39);\n    i0.ɵɵtext(12, \" \\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, FinaldochouseManagementComponent_ng_template_42_div_13_Template, 5, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 41)(15, \"label\", 42);\n    i0.ɵɵtext(16, \" \\u6587\\u4EF6\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_ng_template_42_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.CDocumentName, $event) || (ctx_r4.CDocumentName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 44)(19, \"label\", 45);\n    i0.ɵɵtext(20, \"\\u9001\\u5BE9\\u8CC7\\u8A0A \");\n    i0.ɵɵelementStart(21, \"p\", 46);\n    i0.ɵɵtext(22, \"\\u5167\\u90E8\\u5BE9\\u6838\\u4EBA\\u54E1\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"textarea\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_ng_template_42_Template_textarea_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.CApproveRemark, $event) || (ctx_r4.CApproveRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(24, \"        \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 44)(26, \"label\", 48);\n    i0.ɵɵtext(27, \"\\u6458\\u8981\\u8A3B\\u8A18 \");\n    i0.ɵɵelementStart(28, \"p\", 46);\n    i0.ɵɵtext(29, \"\\u5BA2\\u6236\\u65BC\\u6587\\u4EF6\\u4E2D\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"textarea\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_ng_template_42_Template_textarea_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.CNote, $event) || (ctx_r4.CNote = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(31, \"        \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\", 24)(33, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_ng_template_42_Template_button_click_33_listener() {\n      const ref_r8 = i0.ɵɵrestoreView(_r6).dialogRef;\n      return i0.ɵɵresetView(ref_r8.close());\n    });\n    i0.ɵɵtext(34, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_ng_template_42_Template_button_click_35_listener() {\n      const ref_r8 = i0.ɵɵrestoreView(_r6).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onCreateFinalDoc(ref_r8));\n    });\n    i0.ɵɵtext(36, \"\\u78BA\\u8A8D\\u9001\\u51FA\\u5BE9\\u6838\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B > \", ctx_r4.houseByID.CHousehold, \" \", ctx_r4.houseByID.CFloor, \"F \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.fileName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.CDocumentName);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.CApproveRemark);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.CNote);\n  }\n}\nexport let FinaldochouseManagementComponent = /*#__PURE__*/(() => {\n  class FinaldochouseManagementComponent extends BaseComponent {\n    constructor(_allow, enumHelper, dialogService, message, valid, finalDocumentService, route, _eventService, location, _houseService, fileService) {\n      super(_allow);\n      this._allow = _allow;\n      this.enumHelper = enumHelper;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this.finalDocumentService = finalDocumentService;\n      this.route = route;\n      this._eventService = _eventService;\n      this.location = location;\n      this._houseService = _houseService;\n      this.fileService = fileService;\n      this.calendarOptions = {\n        plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin, timeGridPlugin, bootstrapPlugin],\n        locale: 'zh-tw',\n        headerToolbar: {\n          left: 'prev',\n          center: 'title',\n          right: 'next'\n        }\n      };\n      this.file = null;\n      // request\n      this.getListFinalDocRequest = {};\n      this.uploadFinaldocRequest = {};\n      // response\n      this.listFinalDoc = [];\n      this.maxDate = new Date();\n    }\n    ngOnInit() {\n      this.route.paramMap.subscribe(params => {\n        if (params) {\n          const idParam = params.get('id1');\n          const id = idParam ? +idParam : 0;\n          this.buildCaseId = id;\n          const idParam2 = params.get('id2');\n          const id2 = idParam2 ? +idParam2 : 0;\n          this.currentHouseID = id2;\n          this.getList();\n          this.getHouseById();\n        }\n      });\n    }\n    addNew(ref) {\n      this.CApproveRemark = null;\n      this.CDocumentName = null;\n      this.CNote = null;\n      this.file = null;\n      this.dialogService.open(ref);\n    }\n    openPdfInNewTab(data) {\n      if (data) {\n        let fileUrl;\n        if (data.CSignDate && data.CSign) {\n          fileUrl = data.CFileAfter;\n        } else {\n          fileUrl = data.CFileBefore;\n        }\n        // 檢查檔案名稱是否存在\n        if (!data.CDocumentName) {\n          console.error('檔案名稱不存在');\n          return;\n        }\n        // 使用 FileService.GetFile 取得檔案 blob\n        this.fileService.getFile(fileUrl, data.CDocumentName).subscribe({\n          next: blob => {\n            // 建立 blob URL\n            const url = URL.createObjectURL(blob);\n            // 在新分頁開啟 PDF\n            window.open(url, '_blank');\n            // 延遲清理 URL 以確保檔案能正確載入\n            setTimeout(() => URL.revokeObjectURL(url), 10000);\n          },\n          error: error => {\n            console.error('取得檔案失敗:', error);\n            this.message.showErrorMSG('無法開啟檔案，請稍後再試');\n          }\n        });\n      }\n    }\n    goBack() {\n      this._eventService.push({\n        action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n        payload: this.buildCaseId\n      });\n      this.location.back();\n    }\n    onFileSelected(event) {\n      const file = event.target.files[0];\n      const fileRegex = /pdf/i;\n      if (!fileRegex.test(file.type)) {\n        this.message.showErrorMSG('文件格式不正確，僅允許 pdf 文件');\n        return;\n      }\n      if (file) {\n        const allowedTypes = ['application/pdf'];\n        if (allowedTypes.includes(file.type)) {\n          this.fileName = file.name;\n          this.file = file;\n        }\n      }\n    }\n    clearFile() {\n      if (this.file) {\n        this.file = null;\n        this.fileName = null;\n      }\n    }\n    getHouseById() {\n      this._houseService.apiHouseGetHouseByIdPost$Json({\n        body: {\n          CHouseID: this.currentHouseID\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseByID = res.Entries;\n        }\n      });\n    }\n    getList() {\n      this.getListFinalDocRequest.PageSize = this.pageSize;\n      this.getListFinalDocRequest.PageIndex = this.pageIndex;\n      if (this.currentHouseID != 0) {\n        this.getListFinalDocRequest.CHouseID = this.currentHouseID;\n        this.finalDocumentService.apiFinalDocumentGetListFinalDocByHousePost$Json({\n          body: this.getListFinalDocRequest\n        }).pipe().subscribe(res => {\n          if (res.StatusCode == 0) {\n            if (res.Entries) {\n              this.listFinalDoc = res.Entries;\n              this.totalRecords = res.TotalItems;\n              if (this.listFinalDoc) {\n                for (let i = 0; i < this.listFinalDoc.length; i++) {\n                  if (this.listFinalDoc[i].CSignDate) this.listFinalDoc[i].CSignDate = moment(this.listFinalDoc[i].CSignDate).format(\"yyyy/MM/DD H:mm:ss\");\n                }\n              }\n            }\n          }\n        });\n      }\n    }\n    onCreateFinalDoc(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this.finalDocumentService.apiFinalDocumentUploadFinalDocPost$Json({\n        body: {\n          CHouseID: this.currentHouseID,\n          CBuildCaseID: this.buildCaseId,\n          CDocumentName: this.CDocumentName,\n          CApproveRemark: this.CApproveRemark,\n          CNote: this.CNote,\n          CFile: this.file\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.getList();\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n    convertToBlob(data, mimeType = 'application/octet-stream') {\n      if (data instanceof ArrayBuffer) {\n        return new Blob([data], {\n          type: mimeType\n        });\n      } else if (typeof data === 'string') {\n        return new Blob([data], {\n          type: mimeType\n        });\n      } else {\n        return undefined;\n      }\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[文件格式不正確]', this.file);\n      this.valid.required('[文件名稱]', this.CDocumentName);\n      this.valid.required('[送審資訊]', this.CApproveRemark);\n      this.valid.required('[系統操作說明]', this.CNote);\n    }\n    static {\n      this.ɵfac = function FinaldochouseManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FinaldochouseManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.FinalDocumentService), i0.ɵɵdirectiveInject(i7.ActivatedRoute), i0.ɵɵdirectiveInject(i8.EventService), i0.ɵɵdirectiveInject(i9.Location), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i6.FileService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FinaldochouseManagementComponent,\n        selectors: [[\"app-finaldochouse-management\"]],\n        viewQuery: function FinaldochouseManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarComponent = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 44,\n        vars: 10,\n        consts: [[\"dialogUploadFinaldoc\", \"\"], [\"accent\", \"success\"], [2, \"font-size\", \"32px\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"flex\", \"form-group\", \"col-12\", \"col-md-9\", \"text-right\"], [\"for\", \"date-select1\", 1, \"mr-3\", \"mt-2\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"dateFormat\", \"yy/mm/dd\", 3, \"ngModelChange\", \"appendTo\", \"ngModel\", \"maxDate\"], [\"for\", \"date-select1\", 1, \"mr-1\", \"ml-1\", \"mt-2\"], [1, \"form-group\", \"col-12\", \"col-md-3\", \"text-right\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-5\"], [\"scope\", \"col\", 1, \"col-4\"], [\"scope\", \"col\", 1, \"col-3\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"d-flex\"], [1, \"col-5\"], [1, \"col-4\"], [1, \"col-3\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"width\", \"1000px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\", \"align-items-baseline\"], [1, \"d-flex\", \"flex-col\", \"col-3\"], [\"for\", \"file\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", 2, \"min-width\", \"100px\", \"position\", \"static\"], [1, \"flex\", \"flex-col\", \"items-start\", \"space-y-4\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\"], [\"for\", \"fileInput\", 1, \"cursor-pointer\", \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [1, \"fa-solid\", \"fa-cloud-arrow-up\", \"mr-2\"], [\"class\", \"flex items-center space-x-2\", 4, \"ngIf\"], [1, \"form-group\"], [\"for\", \"CDocumentName\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"col-3\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6587\\u4EF6\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"col-3\"], [2, \"color\", \"red\"], [\"name\", \"remark\", \"id\", \"remark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", \"max-width\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CNote\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"col-3\"], [\"name\", \"CNote\", \"id\", \"CNote\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", \"max-width\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\"]],\n        template: function FinaldochouseManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\")(2, \"div\", 2);\n            i0.ɵɵtext(3, \"\\u6236\\u5225\\u7BA1\\u7406 / \\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(4, \"nb-card-body\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6)(8, \"span\", 7);\n            i0.ɵɵtext(9, \" \\u5EFA\\u7ACB\\u6642\\u9593 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"p-calendar\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListFinalDocRequest.CDateStart, $event) || (ctx.getListFinalDocRequest.CDateStart = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"span\", 9);\n            i0.ɵɵtext(12, \"~\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"p-calendar\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_13_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListFinalDocRequest.CDateEnd, $event) || (ctx.getListFinalDocRequest.CDateEnd = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 10)(15, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_Template_button_click_15_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getList());\n            });\n            i0.ɵɵelement(16, \"i\", 12);\n            i0.ɵɵtext(17, \"\\u67E5\\u8A62\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(18, \"div\", 5)(19, \"div\", 13)(20, \"div\", 14)(21, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_Template_button_click_21_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const dialogUploadFinaldoc_r2 = i0.ɵɵreference(43);\n              return i0.ɵɵresetView(ctx.addNew(dialogUploadFinaldoc_r2));\n            });\n            i0.ɵɵtext(22, \" \\u65B0\\u589E\\u6587\\u6A94 \");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(23, \"nb-card-body\", 3)(24, \"div\", 4)(25, \"div\", 16)(26, \"table\", 17)(27, \"thead\")(28, \"tr\", 18)(29, \"th\", 19);\n            i0.ɵɵtext(30, \"\\u6587\\u4EF6\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"th\", 20);\n            i0.ɵɵtext(32, \"\\u5BA2\\u6236\\u7C3D\\u540D\\u6642\\u9593\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"th\", 21);\n            i0.ɵɵtext(34, \"\\u9023\\u7D50\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(35, \"tbody\");\n            i0.ɵɵtemplate(36, FinaldochouseManagementComponent_tr_36_Template, 8, 2, \"tr\", 22);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"ngx-pagination\", 23);\n            i0.ɵɵtwoWayListener(\"PageChange\", function FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_37_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_37_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getList());\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(38, \"nb-card-footer\")(39, \"div\", 24)(40, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_Template_button_click_40_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goBack());\n            });\n            i0.ɵɵtext(41, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(42, FinaldochouseManagementComponent_ng_template_42_Template, 37, 6, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"appendTo\", \"body\");\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListFinalDocRequest.CDateStart);\n            i0.ɵɵproperty(\"maxDate\", ctx.maxDate);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"appendTo\", \"body\");\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListFinalDocRequest.CDateEnd);\n            i0.ɵɵproperty(\"maxDate\", ctx.maxDate);\n            i0.ɵɵadvance(23);\n            i0.ɵɵproperty(\"ngForOf\", ctx.listFinalDoc);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          }\n        },\n        dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, NbInputModule, i3.NbInputDirective, FormsModule, i10.DefaultValueAccessor, i10.NgControlStatus, i10.NgModel, NbSelectModule, NbOptionModule, NgIf, NgFor, PaginationComponent, NbCheckboxModule, FullCalendarModule, CalendarModule, i11.Calendar]\n      });\n    }\n  }\n  return FinaldochouseManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}