{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiFileGetFileGet(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiFileGetFileGet.PATH, 'get');\n  if (params) {\n    rb.query('relativePath', params.relativePath, {});\n    rb.query('fileName', params.fileName, {});\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: '*/*',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r.clone({\n      body: undefined\n    });\n  }));\n}\napiFileGetFileGet.PATH = '/api/File/GetFile';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}