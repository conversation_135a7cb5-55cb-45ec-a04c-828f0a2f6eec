{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let EnumHelper = /*#__PURE__*/(() => {\n  class EnumHelper {\n    getEnumOptions(enumObj) {\n      return Object.keys(enumObj).filter(key => !isNaN(Number(enumObj[key]))) // Lọc chỉ số (value) của enum\n      .map(key => ({\n        label: key,\n        value: enumObj[key]\n      }));\n    }\n    static {\n      this.ɵfac = function EnumHelper_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || EnumHelper)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: EnumHelper,\n        factory: EnumHelper.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return EnumHelper;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}