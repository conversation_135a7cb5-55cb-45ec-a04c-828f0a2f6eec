import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  NbThemeModule,
  NbLayoutModule,
  NbCardModule,
  NbButtonModule,
  NbSelectModule,
  NbInputModule,
  NbCheckboxModule,
  NbIconModule,
  NbListModule,
  NbTagModule,
  NbPopoverModule
} from '@nebular/theme';

import { HouseholdBindingComponent } from './components/household-binding/household-binding.component';
import { TestDropdownComponent } from './components/household-binding/test-dropdown.component';
import { SimpleDropdownTestComponent } from './components/household-binding/simple-dropdown-test.component';

@NgModule({
  declarations: [
    HouseholdBindingComponent,
    TestDropdownComponent,
    SimpleDropdownTestComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NbThemeModule,
    NbLayoutModule,
    NbCardModule,
    NbButtonModule,
    NbSelectModule,
    NbInputModule,
    NbCheckboxModule,
    NbIconModule,
    NbListModule,
    NbTagModule,
    NbPopoverModule], exports: [
      HouseholdBindingComponent,
      TestDropdownComponent,
      SimpleDropdownTestComponent,
      // 也可以導出常用的模組供其他地方使用
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      NbThemeModule,
      NbLayoutModule,
      NbCardModule,
      NbButtonModule,
      NbSelectModule,
      NbInputModule,
      NbCheckboxModule,
      NbIconModule,
      NbListModule,
      NbTagModule,
      NbPopoverModule
    ]
})
export class SharedModule { }
