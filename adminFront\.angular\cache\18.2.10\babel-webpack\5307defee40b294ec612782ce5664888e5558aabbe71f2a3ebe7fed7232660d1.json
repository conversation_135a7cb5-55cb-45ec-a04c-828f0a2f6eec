{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { NbDatepickerModule, NbDialogModule, NbMenuModule, NbSidebarModule, NbThemeModule, NbTimepickerModule, NbToastrModule, NbWindowModule } from '@nebular/theme';\nimport { ThemeModule } from './@theme/theme.module';\nimport { PagesComponent } from './pages/pages.component';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { FormsModule } from '@angular/forms';\nimport { HomeComponent } from './pages/home/<USER>';\nimport { LoginComponent } from './pages/login/login.component';\nimport { LogoutComponent } from './pages/logout/logout.component';\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\nimport { CoreModule } from './@core/core.module';\nimport { appConfig } from './app.config';\nimport { TokenInterceptor } from './shared/auth/token.interceptor';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport { SharedModule } from './pages/components/shared.module';\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\nimport { registerLocaleData } from '@angular/common';\nimport localeZh from '@angular/common/locales/zh';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport * as echarts from 'echarts';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"./@theme/theme.module\";\nimport * as i3 from \"./@core/core.module\";\nimport * as i4 from \"angular-calendar\";\nimport * as i5 from \"ngx-echarts\";\nregisterLocaleData(localeZh);\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: TokenInterceptor,\n        multi: true\n      }, appConfig.providers],\n      imports: [NgxSpinnerModule, BrowserModule, BrowserAnimationsModule, AppRoutingModule, FormsModule, SharedModule, HttpClientModule, NbThemeModule.forRoot(), ThemeModule.forRoot(), NbSidebarModule.forRoot(), NbMenuModule.forRoot(), NbDatepickerModule.forRoot(), NbDialogModule.forRoot(), NbWindowModule.forRoot(), NbToastrModule.forRoot(), NbTimepickerModule.forRoot(), NgbModule, CoreModule.forRoot(), PagesComponent, HomeComponent, LoginComponent, FullCalendarModule, CalendarModule.forRoot({\n        provide: DateAdapter,\n        useFactory: adapterFactory\n      }), NgxEchartsModule.forRoot({\n        echarts\n      })]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [NgxSpinnerModule, BrowserModule, BrowserAnimationsModule, AppRoutingModule, FormsModule, SharedModule, HttpClientModule, i1.NbThemeModule, i2.ThemeModule, i1.NbSidebarModule, i1.NbMenuModule, i1.NbDatepickerModule, i1.NbDialogModule, i1.NbWindowModule, i1.NbToastrModule, i1.NbTimepickerModule, NgbModule, i3.CoreModule, PagesComponent, HomeComponent, LoginComponent, LogoutComponent, FullCalendarModule, i4.CalendarModule, i5.NgxEchartsModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "BrowserAnimationsModule", "AppRoutingModule", "AppComponent", "NbDatepickerModule", "NbDialogModule", "NbMenuModule", "NbSidebarModule", "NbThemeModule", "NbTimepickerModule", "NbToastrModule", "NbWindowModule", "ThemeModule", "PagesComponent", "NgxSpinnerModule", "NgbModule", "FormsModule", "HomeComponent", "LoginComponent", "LogoutComponent", "HTTP_INTERCEPTORS", "HttpClientModule", "CoreModule", "appConfig", "TokenInterceptor", "FullCalendarModule", "SharedModule", "CalendarModule", "DateAdapter", "adapterFactory", "registerLocaleData", "localeZh", "NgxEchartsModule", "echarts", "AppModule", "bootstrap", "provide", "useClass", "multi", "providers", "imports", "forRoot", "useFactory", "declarations", "i1", "i2", "i3", "i4", "i5"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { NbDatepickerModule, NbDialogModule, NbMenuModule, NbSidebarModule, NbThemeModule, NbTimepickerModule, NbToastrModule, NbWindowModule } from '@nebular/theme';\r\nimport { ThemeModule } from './@theme/theme.module';\r\nimport { PagesComponent } from './pages/pages.component';\r\nimport { NgxSpinnerModule } from 'ngx-spinner';\r\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { HomeComponent } from './pages/home/<USER>';\r\nimport { LoginComponent } from './pages/login/login.component';\r\nimport { LogoutComponent } from './pages/logout/logout.component';\r\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\r\nimport { CoreModule } from './@core/core.module';\r\nimport { appConfig } from './app.config';\r\nimport { TokenInterceptor } from './shared/auth/token.interceptor';\r\nimport { FullCalendarModule } from '@fullcalendar/angular';\r\nimport { SharedModule } from './pages/components/shared.module';\r\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\r\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\r\nimport { registerLocaleData } from '@angular/common';\r\nimport  localeZh  from '@angular/common/locales/zh';\r\nimport { NgxEchartsModule } from 'ngx-echarts';\r\nimport * as echarts from 'echarts';\r\n\r\nregisterLocaleData(localeZh);\r\n\r\n@NgModule({\r\n    declarations: [AppComponent],\r\n    imports: [\r\n        NgxSpinnerModule,\r\n        BrowserModule,\r\n        BrowserAnimationsModule,\r\n        AppRoutingModule,\r\n        FormsModule,\r\n        SharedModule,\r\n        HttpClientModule,\r\n        NbThemeModule.forRoot(),\r\n        ThemeModule.forRoot(),\r\n        NbSidebarModule.forRoot(),\r\n        NbMenuModule.forRoot(),\r\n        NbDatepickerModule.forRoot(),\r\n        NbDialogModule.forRoot(),\r\n        NbWindowModule.forRoot(),\r\n        NbToastrModule.forRoot(),\r\n        NbTimepickerModule.forRoot(),\r\n        NgbModule,\r\n        CoreModule.forRoot(),\r\n        PagesComponent,\r\n        HomeComponent,\r\n        LoginComponent,\r\n        LogoutComponent,\r\n        FullCalendarModule,\r\n        CalendarModule.forRoot({\r\n            provide: DateAdapter,\r\n            useFactory: adapterFactory,\r\n        }),\r\n        NgxEchartsModule.forRoot({\r\n            echarts,\r\n        }),\r\n    ],\r\n    providers: [\r\n        { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptor, multi: true },\r\n        appConfig.providers,\r\n    ],\r\n    bootstrap: [AppComponent]\r\n})\r\nexport class AppModule { }\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,YAAY,EAAEC,eAAe,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AACrK,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,cAAc,EAAEC,WAAW,QAAQ,kBAAkB;AAC9D,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAQC,QAAQ,MAAO,4BAA4B;AACnD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,OAAO,KAAKC,OAAO,MAAM,SAAS;;;;;;;AAElCH,kBAAkB,CAACC,QAAQ,CAAC;AA0C5B,OAAM,MAAOG,SAAS;;;uCAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFNhC,YAAY;IAAA;EAAA;;;iBAJb,CACP;QAAEiC,OAAO,EAAEhB,iBAAiB;QAAEiB,QAAQ,EAAEb,gBAAgB;QAAEc,KAAK,EAAE;MAAI,CAAE,EACvEf,SAAS,CAACgB,SAAS,CACtB;MAAAC,OAAA,GAlCG1B,gBAAgB,EAChBd,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBc,WAAW,EACXU,YAAY,EACZL,gBAAgB,EAChBb,aAAa,CAACiC,OAAO,EAAE,EACvB7B,WAAW,CAAC6B,OAAO,EAAE,EACrBlC,eAAe,CAACkC,OAAO,EAAE,EACzBnC,YAAY,CAACmC,OAAO,EAAE,EACtBrC,kBAAkB,CAACqC,OAAO,EAAE,EAC5BpC,cAAc,CAACoC,OAAO,EAAE,EACxB9B,cAAc,CAAC8B,OAAO,EAAE,EACxB/B,cAAc,CAAC+B,OAAO,EAAE,EACxBhC,kBAAkB,CAACgC,OAAO,EAAE,EAC5B1B,SAAS,EACTO,UAAU,CAACmB,OAAO,EAAE,EACpB5B,cAAc,EACdI,aAAa,EACbC,cAAc,EAEdO,kBAAkB,EAClBE,cAAc,CAACc,OAAO,CAAC;QACnBL,OAAO,EAAER,WAAW;QACpBc,UAAU,EAAEb;OACf,CAAC,EACFG,gBAAgB,CAACS,OAAO,CAAC;QACrBR;OACH,CAAC;IAAA;EAAA;;;2EAQGC,SAAS;IAAAS,YAAA,GAvCHxC,YAAY;IAAAqC,OAAA,GAEvB1B,gBAAgB,EAChBd,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBc,WAAW,EACXU,YAAY,EACZL,gBAAgB,EAAAuB,EAAA,CAAApC,aAAA,EAAAqC,EAAA,CAAAjC,WAAA,EAAAgC,EAAA,CAAArC,eAAA,EAAAqC,EAAA,CAAAtC,YAAA,EAAAsC,EAAA,CAAAxC,kBAAA,EAAAwC,EAAA,CAAAvC,cAAA,EAAAuC,EAAA,CAAAjC,cAAA,EAAAiC,EAAA,CAAAlC,cAAA,EAAAkC,EAAA,CAAAnC,kBAAA,EAUhBM,SAAS,EAAA+B,EAAA,CAAAxB,UAAA,EAETT,cAAc,EACdI,aAAa,EACbC,cAAc,EACdC,eAAe,EACfM,kBAAkB,EAAAsB,EAAA,CAAApB,cAAA,EAAAqB,EAAA,CAAAhB,gBAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}