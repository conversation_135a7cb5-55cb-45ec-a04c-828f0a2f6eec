{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Northern Sami [se]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/karamell\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var se = moment.defineLocale('se', {\n    months: 'ođđajagemánnu_guovvamánnu_njukčamánnu_cuoŋománnu_miessemánnu_geassemánnu_suoidnemánnu_borgemánnu_čakčamánnu_golggotmánnu_skábmamánnu_juovlamánnu'.split('_'),\n    monthsShort: 'ođđj_guov_njuk_cuo_mies_geas_suoi_borg_čakč_golg_skáb_juov'.split('_'),\n    weekdays: 'sotnabeaivi_vuossárga_maŋŋebárga_gaskavahkku_duorastat_bearjadat_lávvardat'.split('_'),\n    weekdaysShort: 'sotn_vuos_maŋ_gask_duor_bear_láv'.split('_'),\n    weekdaysMin: 's_v_m_g_d_b_L'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'MMMM D. [b.] YYYY',\n      LLL: 'MMMM D. [b.] YYYY [ti.] HH:mm',\n      LLLL: 'dddd, MMMM D. [b.] YYYY [ti.] HH:mm'\n    },\n    calendar: {\n      sameDay: '[otne ti] LT',\n      nextDay: '[ihttin ti] LT',\n      nextWeek: 'dddd [ti] LT',\n      lastDay: '[ikte ti] LT',\n      lastWeek: '[ovddit] dddd [ti] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s geažes',\n      past: 'maŋit %s',\n      s: 'moadde sekunddat',\n      ss: '%d sekunddat',\n      m: 'okta minuhta',\n      mm: '%d minuhtat',\n      h: 'okta diimmu',\n      hh: '%d diimmut',\n      d: 'okta beaivi',\n      dd: '%d beaivvit',\n      M: 'okta mánnu',\n      MM: '%d mánut',\n      y: 'okta jahki',\n      yy: '%d jagit'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return se;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}