{"ast": null, "code": "import { Splitter, hasBgRendering, createFormatter, ViewContextType, ContentContainer, BaseComponent, DateComponent, diffDays, buildNavLinkAttrs, WeekNumberContainer, getStickyHeaderDates, ViewContainer, SimpleScrollGrid, getStickyFooterScrollbar, NowTimer, NowIndicatorContainer, renderScrollShim, rangeContainsMarker, startOfDay, asRoughMs, createDuration, RefMap, PositionCache, MoreLinkContainer, SegHierarchy, groupIntersectingEntries, binarySearch, getEntrySpanEnd, buildEntryKey, StandardEvent, memoize, sortEventSegs, DayCellContainer, hasCustomDayCellContent, getSegMeta, buildIsoString, computeEarliestSegStart, buildEventRangeKey, BgEvent, renderFill, addDurations, multiplyDuration, wholeDivideDurations, Slicer, intersectRanges, formatIsoTimeString, DayHeader, DaySeriesModel, DayTableModel, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createElement, createRef, Fragment } from '@fullcalendar/core/preact.js';\nimport { DayTable } from '@fullcalendar/daygrid/internal.js';\nclass AllDaySplitter extends Splitter {\n  getKeyInfo() {\n    return {\n      allDay: {},\n      timed: {}\n    };\n  }\n  getKeysForDateSpan(dateSpan) {\n    if (dateSpan.allDay) {\n      return ['allDay'];\n    }\n    return ['timed'];\n  }\n  getKeysForEventDef(eventDef) {\n    if (!eventDef.allDay) {\n      return ['timed'];\n    }\n    if (hasBgRendering(eventDef)) {\n      return ['timed', 'allDay'];\n    }\n    return ['allDay'];\n  }\n}\nconst DEFAULT_SLAT_LABEL_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  omitZeroMinute: true,\n  meridiem: 'short'\n});\nfunction TimeColsAxisCell(props) {\n  let classNames = ['fc-timegrid-slot', 'fc-timegrid-slot-label', props.isLabeled ? 'fc-scrollgrid-shrink' : 'fc-timegrid-slot-minor'];\n  return createElement(ViewContextType.Consumer, null, context => {\n    if (!props.isLabeled) {\n      return createElement(\"td\", {\n        className: classNames.join(' '),\n        \"data-time\": props.isoTimeStr\n      });\n    }\n    let {\n      dateEnv,\n      options,\n      viewApi\n    } = context;\n    let labelFormat =\n    // TODO: fully pre-parse\n    options.slotLabelFormat == null ? DEFAULT_SLAT_LABEL_FORMAT : Array.isArray(options.slotLabelFormat) ? createFormatter(options.slotLabelFormat[0]) : createFormatter(options.slotLabelFormat);\n    let renderProps = {\n      level: 0,\n      time: props.time,\n      date: dateEnv.toDate(props.date),\n      view: viewApi,\n      text: dateEnv.format(props.date, labelFormat)\n    };\n    return createElement(ContentContainer, {\n      elTag: \"td\",\n      elClasses: classNames,\n      elAttrs: {\n        'data-time': props.isoTimeStr\n      },\n      renderProps: renderProps,\n      generatorName: \"slotLabelContent\",\n      customGenerator: options.slotLabelContent,\n      defaultGenerator: renderInnerContent,\n      classNameGenerator: options.slotLabelClassNames,\n      didMount: options.slotLabelDidMount,\n      willUnmount: options.slotLabelWillUnmount\n    }, InnerContent => createElement(\"div\", {\n      className: \"fc-timegrid-slot-label-frame fc-scrollgrid-shrink-frame\"\n    }, createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-timegrid-slot-label-cushion', 'fc-scrollgrid-shrink-cushion']\n    })));\n  });\n}\nfunction renderInnerContent(props) {\n  return props.text;\n}\nclass TimeBodyAxis extends BaseComponent {\n  render() {\n    return this.props.slatMetas.map(slatMeta => createElement(\"tr\", {\n      key: slatMeta.key\n    }, createElement(TimeColsAxisCell, Object.assign({}, slatMeta))));\n  }\n}\nconst DEFAULT_WEEK_NUM_FORMAT = createFormatter({\n  week: 'short'\n});\nconst AUTO_ALL_DAY_MAX_EVENT_ROWS = 5;\nclass TimeColsView extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.allDaySplitter = new AllDaySplitter(); // for use by subclasses\n    this.headerElRef = createRef();\n    this.rootElRef = createRef();\n    this.scrollerElRef = createRef();\n    this.state = {\n      slatCoords: null\n    };\n    this.handleScrollTopRequest = scrollTop => {\n      let scrollerEl = this.scrollerElRef.current;\n      if (scrollerEl) {\n        // TODO: not sure how this could ever be null. weirdness with the reducer\n        scrollerEl.scrollTop = scrollTop;\n      }\n    };\n    /* Header Render Methods\n    ------------------------------------------------------------------------------------------------------------------*/\n    this.renderHeadAxis = (rowKey, frameHeight = '') => {\n      let {\n        options\n      } = this.context;\n      let {\n        dateProfile\n      } = this.props;\n      let range = dateProfile.renderRange;\n      let dayCnt = diffDays(range.start, range.end);\n      // only do in day views (to avoid doing in week views that dont need it)\n      let navLinkAttrs = dayCnt === 1 ? buildNavLinkAttrs(this.context, range.start, 'week') : {};\n      if (options.weekNumbers && rowKey === 'day') {\n        return createElement(WeekNumberContainer, {\n          elTag: \"th\",\n          elClasses: ['fc-timegrid-axis', 'fc-scrollgrid-shrink'],\n          elAttrs: {\n            'aria-hidden': true\n          },\n          date: range.start,\n          defaultFormat: DEFAULT_WEEK_NUM_FORMAT\n        }, InnerContent => createElement(\"div\", {\n          className: ['fc-timegrid-axis-frame', 'fc-scrollgrid-shrink-frame', 'fc-timegrid-axis-frame-liquid'].join(' '),\n          style: {\n            height: frameHeight\n          }\n        }, createElement(InnerContent, {\n          elTag: \"a\",\n          elClasses: ['fc-timegrid-axis-cushion', 'fc-scrollgrid-shrink-cushion', 'fc-scrollgrid-sync-inner'],\n          elAttrs: navLinkAttrs\n        })));\n      }\n      return createElement(\"th\", {\n        \"aria-hidden\": true,\n        className: \"fc-timegrid-axis\"\n      }, createElement(\"div\", {\n        className: \"fc-timegrid-axis-frame\",\n        style: {\n          height: frameHeight\n        }\n      }));\n    };\n    /* Table Component Render Methods\n    ------------------------------------------------------------------------------------------------------------------*/\n    // only a one-way height sync. we don't send the axis inner-content height to the DayGrid,\n    // but DayGrid still needs to have classNames on inner elements in order to measure.\n    this.renderTableRowAxis = rowHeight => {\n      let {\n        options,\n        viewApi\n      } = this.context;\n      let renderProps = {\n        text: options.allDayText,\n        view: viewApi\n      };\n      return (\n        // TODO: make reusable hook. used in list view too\n        createElement(ContentContainer, {\n          elTag: \"td\",\n          elClasses: ['fc-timegrid-axis', 'fc-scrollgrid-shrink'],\n          elAttrs: {\n            'aria-hidden': true\n          },\n          renderProps: renderProps,\n          generatorName: \"allDayContent\",\n          customGenerator: options.allDayContent,\n          defaultGenerator: renderAllDayInner,\n          classNameGenerator: options.allDayClassNames,\n          didMount: options.allDayDidMount,\n          willUnmount: options.allDayWillUnmount\n        }, InnerContent => createElement(\"div\", {\n          className: ['fc-timegrid-axis-frame', 'fc-scrollgrid-shrink-frame', rowHeight == null ? ' fc-timegrid-axis-frame-liquid' : ''].join(' '),\n          style: {\n            height: rowHeight\n          }\n        }, createElement(InnerContent, {\n          elTag: \"span\",\n          elClasses: ['fc-timegrid-axis-cushion', 'fc-scrollgrid-shrink-cushion', 'fc-scrollgrid-sync-inner']\n        })))\n      );\n    };\n    this.handleSlatCoords = slatCoords => {\n      this.setState({\n        slatCoords\n      });\n    };\n  }\n  // rendering\n  // ----------------------------------------------------------------------------------------------------\n  renderSimpleLayout(headerRowContent, allDayContent, timeContent) {\n    let {\n      context,\n      props\n    } = this;\n    let sections = [];\n    let stickyHeaderDates = getStickyHeaderDates(context.options);\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        chunk: {\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent\n        }\n      });\n    }\n    if (allDayContent) {\n      sections.push({\n        type: 'body',\n        key: 'all-day',\n        chunk: {\n          content: allDayContent\n        }\n      });\n      sections.push({\n        type: 'body',\n        key: 'all-day-divider',\n        outerContent:\n        // TODO: rename to cellContent so don't need to define <tr>?\n        createElement(\"tr\", {\n          role: \"presentation\",\n          className: \"fc-scrollgrid-section\"\n        }, createElement(\"td\", {\n          className: 'fc-timegrid-divider ' + context.theme.getClass('tableCellShaded')\n        }))\n      });\n    }\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      expandRows: Boolean(context.options.expandRows),\n      chunk: {\n        scrollerElRef: this.scrollerElRef,\n        content: timeContent\n      }\n    });\n    return createElement(ViewContainer, {\n      elRef: this.rootElRef,\n      elClasses: ['fc-timegrid'],\n      viewSpec: context.viewSpec\n    }, createElement(SimpleScrollGrid, {\n      liquid: !props.isHeightAuto && !props.forPrint,\n      collapsibleWidth: props.forPrint,\n      cols: [{\n        width: 'shrink'\n      }],\n      sections: sections\n    }));\n  }\n  renderHScrollLayout(headerRowContent, allDayContent, timeContent, colCnt, dayMinWidth, slatMetas, slatCoords) {\n    let ScrollGrid = this.context.pluginHooks.scrollGridImpl;\n    if (!ScrollGrid) {\n      throw new Error('No ScrollGrid implementation');\n    }\n    let {\n      context,\n      props\n    } = this;\n    let stickyHeaderDates = !props.forPrint && getStickyHeaderDates(context.options);\n    let stickyFooterScrollbar = !props.forPrint && getStickyFooterScrollbar(context.options);\n    let sections = [];\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        syncRowHeights: true,\n        chunks: [{\n          key: 'axis',\n          rowContent: arg => createElement(\"tr\", {\n            role: \"presentation\"\n          }, this.renderHeadAxis('day', arg.rowSyncHeights[0]))\n        }, {\n          key: 'cols',\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent\n        }]\n      });\n    }\n    if (allDayContent) {\n      sections.push({\n        type: 'body',\n        key: 'all-day',\n        syncRowHeights: true,\n        chunks: [{\n          key: 'axis',\n          rowContent: contentArg => createElement(\"tr\", {\n            role: \"presentation\"\n          }, this.renderTableRowAxis(contentArg.rowSyncHeights[0]))\n        }, {\n          key: 'cols',\n          content: allDayContent\n        }]\n      });\n      sections.push({\n        key: 'all-day-divider',\n        type: 'body',\n        outerContent:\n        // TODO: rename to cellContent so don't need to define <tr>?\n        createElement(\"tr\", {\n          role: \"presentation\",\n          className: \"fc-scrollgrid-section\"\n        }, createElement(\"td\", {\n          colSpan: 2,\n          className: 'fc-timegrid-divider ' + context.theme.getClass('tableCellShaded')\n        }))\n      });\n    }\n    let isNowIndicator = context.options.nowIndicator;\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      expandRows: Boolean(context.options.expandRows),\n      chunks: [{\n        key: 'axis',\n        content: arg =>\n        // TODO: make this now-indicator arrow more DRY with TimeColsContent\n        createElement(\"div\", {\n          className: \"fc-timegrid-axis-chunk\"\n        }, createElement(\"table\", {\n          \"aria-hidden\": true,\n          style: {\n            height: arg.expandRows ? arg.clientHeight : ''\n          }\n        }, arg.tableColGroupNode, createElement(\"tbody\", null, createElement(TimeBodyAxis, {\n          slatMetas: slatMetas\n        }))), createElement(\"div\", {\n          className: \"fc-timegrid-now-indicator-container\"\n        }, createElement(NowTimer, {\n          unit: isNowIndicator ? 'minute' : 'day' /* hacky */\n        }, nowDate => {\n          let nowIndicatorTop = isNowIndicator && slatCoords && slatCoords.safeComputeTop(nowDate); // might return void\n          if (typeof nowIndicatorTop === 'number') {\n            return createElement(NowIndicatorContainer, {\n              elClasses: ['fc-timegrid-now-indicator-arrow'],\n              elStyle: {\n                top: nowIndicatorTop\n              },\n              isAxis: true,\n              date: nowDate\n            });\n          }\n          return null;\n        })))\n      }, {\n        key: 'cols',\n        scrollerElRef: this.scrollerElRef,\n        content: timeContent\n      }]\n    });\n    if (stickyFooterScrollbar) {\n      sections.push({\n        key: 'footer',\n        type: 'footer',\n        isSticky: true,\n        chunks: [{\n          key: 'axis',\n          content: renderScrollShim\n        }, {\n          key: 'cols',\n          content: renderScrollShim\n        }]\n      });\n    }\n    return createElement(ViewContainer, {\n      elRef: this.rootElRef,\n      elClasses: ['fc-timegrid'],\n      viewSpec: context.viewSpec\n    }, createElement(ScrollGrid, {\n      liquid: !props.isHeightAuto && !props.forPrint,\n      forPrint: props.forPrint,\n      collapsibleWidth: false,\n      colGroups: [{\n        width: 'shrink',\n        cols: [{\n          width: 'shrink'\n        }]\n      }, {\n        cols: [{\n          span: colCnt,\n          minWidth: dayMinWidth\n        }]\n      }],\n      sections: sections\n    }));\n  }\n  /* Dimensions\n  ------------------------------------------------------------------------------------------------------------------*/\n  getAllDayMaxEventProps() {\n    let {\n      dayMaxEvents,\n      dayMaxEventRows\n    } = this.context.options;\n    if (dayMaxEvents === true || dayMaxEventRows === true) {\n      // is auto?\n      dayMaxEvents = undefined;\n      dayMaxEventRows = AUTO_ALL_DAY_MAX_EVENT_ROWS; // make sure \"auto\" goes to a real number\n    }\n    return {\n      dayMaxEvents,\n      dayMaxEventRows\n    };\n  }\n}\nfunction renderAllDayInner(renderProps) {\n  return renderProps.text;\n}\nclass TimeColsSlatsCoords {\n  constructor(positions, dateProfile, slotDuration) {\n    this.positions = positions;\n    this.dateProfile = dateProfile;\n    this.slotDuration = slotDuration;\n  }\n  safeComputeTop(date) {\n    let {\n      dateProfile\n    } = this;\n    if (rangeContainsMarker(dateProfile.currentRange, date)) {\n      let startOfDayDate = startOfDay(date);\n      let timeMs = date.valueOf() - startOfDayDate.valueOf();\n      if (timeMs >= asRoughMs(dateProfile.slotMinTime) && timeMs < asRoughMs(dateProfile.slotMaxTime)) {\n        return this.computeTimeTop(createDuration(timeMs));\n      }\n    }\n    return null;\n  }\n  // Computes the top coordinate, relative to the bounds of the grid, of the given date.\n  // A `startOfDayDate` must be given for avoiding ambiguity over how to treat midnight.\n  computeDateTop(when, startOfDayDate) {\n    if (!startOfDayDate) {\n      startOfDayDate = startOfDay(when);\n    }\n    return this.computeTimeTop(createDuration(when.valueOf() - startOfDayDate.valueOf()));\n  }\n  // Computes the top coordinate, relative to the bounds of the grid, of the given time (a Duration).\n  // This is a makeshify way to compute the time-top. Assumes all slatMetas dates are uniform.\n  // Eventually allow computation with arbirary slat dates.\n  computeTimeTop(duration) {\n    let {\n      positions,\n      dateProfile\n    } = this;\n    let len = positions.els.length;\n    // floating-point value of # of slots covered\n    let slatCoverage = (duration.milliseconds - asRoughMs(dateProfile.slotMinTime)) / asRoughMs(this.slotDuration);\n    let slatIndex;\n    let slatRemainder;\n    // compute a floating-point number for how many slats should be progressed through.\n    // from 0 to number of slats (inclusive)\n    // constrained because slotMinTime/slotMaxTime might be customized.\n    slatCoverage = Math.max(0, slatCoverage);\n    slatCoverage = Math.min(len, slatCoverage);\n    // an integer index of the furthest whole slat\n    // from 0 to number slats (*exclusive*, so len-1)\n    slatIndex = Math.floor(slatCoverage);\n    slatIndex = Math.min(slatIndex, len - 1);\n    // how much further through the slatIndex slat (from 0.0-1.0) must be covered in addition.\n    // could be 1.0 if slatCoverage is covering *all* the slots\n    slatRemainder = slatCoverage - slatIndex;\n    return positions.tops[slatIndex] + positions.getHeight(slatIndex) * slatRemainder;\n  }\n}\nclass TimeColsSlatsBody extends BaseComponent {\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let {\n      slatElRefs\n    } = props;\n    return createElement(\"tbody\", null, props.slatMetas.map((slatMeta, i) => {\n      let renderProps = {\n        time: slatMeta.time,\n        date: context.dateEnv.toDate(slatMeta.date),\n        view: context.viewApi\n      };\n      return createElement(\"tr\", {\n        key: slatMeta.key,\n        ref: slatElRefs.createRef(slatMeta.key)\n      }, props.axis && createElement(TimeColsAxisCell, Object.assign({}, slatMeta)), createElement(ContentContainer, {\n        elTag: \"td\",\n        elClasses: ['fc-timegrid-slot', 'fc-timegrid-slot-lane', !slatMeta.isLabeled && 'fc-timegrid-slot-minor'],\n        elAttrs: {\n          'data-time': slatMeta.isoTimeStr\n        },\n        renderProps: renderProps,\n        generatorName: \"slotLaneContent\",\n        customGenerator: options.slotLaneContent,\n        classNameGenerator: options.slotLaneClassNames,\n        didMount: options.slotLaneDidMount,\n        willUnmount: options.slotLaneWillUnmount\n      }));\n    }));\n  }\n}\n\n/*\nfor the horizontal \"slats\" that run width-wise. Has a time axis on a side. Depends on RTL.\n*/\nclass TimeColsSlats extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.rootElRef = createRef();\n    this.slatElRefs = new RefMap();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    return createElement(\"div\", {\n      ref: this.rootElRef,\n      className: \"fc-timegrid-slots\"\n    }, createElement(\"table\", {\n      \"aria-hidden\": true,\n      className: context.theme.getClass('table'),\n      style: {\n        minWidth: props.tableMinWidth,\n        width: props.clientWidth,\n        height: props.minHeight\n      }\n    }, props.tableColGroupNode /* relies on there only being a single <col> for the axis */, createElement(TimeColsSlatsBody, {\n      slatElRefs: this.slatElRefs,\n      axis: props.axis,\n      slatMetas: props.slatMetas\n    })));\n  }\n  componentDidMount() {\n    this.updateSizing();\n  }\n  componentDidUpdate() {\n    this.updateSizing();\n  }\n  componentWillUnmount() {\n    if (this.props.onCoords) {\n      this.props.onCoords(null);\n    }\n  }\n  updateSizing() {\n    let {\n      context,\n      props\n    } = this;\n    if (props.onCoords && props.clientWidth !== null // means sizing has stabilized\n    ) {\n      let rootEl = this.rootElRef.current;\n      if (rootEl.offsetHeight) {\n        // not hidden by css\n        props.onCoords(new TimeColsSlatsCoords(new PositionCache(this.rootElRef.current, collectSlatEls(this.slatElRefs.currentMap, props.slatMetas), false, true), this.props.dateProfile, context.options.slotDuration));\n      }\n    }\n  }\n}\nfunction collectSlatEls(elMap, slatMetas) {\n  return slatMetas.map(slatMeta => elMap[slatMeta.key]);\n}\nfunction splitSegsByCol(segs, colCnt) {\n  let segsByCol = [];\n  let i;\n  for (i = 0; i < colCnt; i += 1) {\n    segsByCol.push([]);\n  }\n  if (segs) {\n    for (i = 0; i < segs.length; i += 1) {\n      segsByCol[segs[i].col].push(segs[i]);\n    }\n  }\n  return segsByCol;\n}\nfunction splitInteractionByCol(ui, colCnt) {\n  let byRow = [];\n  if (!ui) {\n    for (let i = 0; i < colCnt; i += 1) {\n      byRow[i] = null;\n    }\n  } else {\n    for (let i = 0; i < colCnt; i += 1) {\n      byRow[i] = {\n        affectedInstances: ui.affectedInstances,\n        isEvent: ui.isEvent,\n        segs: []\n      };\n    }\n    for (let seg of ui.segs) {\n      byRow[seg.col].segs.push(seg);\n    }\n  }\n  return byRow;\n}\nclass TimeColMoreLink extends BaseComponent {\n  render() {\n    let {\n      props\n    } = this;\n    return createElement(MoreLinkContainer, {\n      elClasses: ['fc-timegrid-more-link'],\n      elStyle: {\n        top: props.top,\n        bottom: props.bottom\n      },\n      allDayDate: null,\n      moreCnt: props.hiddenSegs.length,\n      allSegs: props.hiddenSegs,\n      hiddenSegs: props.hiddenSegs,\n      extraDateSpan: props.extraDateSpan,\n      dateProfile: props.dateProfile,\n      todayRange: props.todayRange,\n      popoverContent: () => renderPlainFgSegs(props.hiddenSegs, props),\n      defaultGenerator: renderMoreLinkInner,\n      forceTimed: true\n    }, InnerContent => createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-timegrid-more-link-inner', 'fc-sticky']\n    }));\n  }\n}\nfunction renderMoreLinkInner(props) {\n  return props.shortText;\n}\n\n// segInputs assumed sorted\nfunction buildPositioning(segInputs, strictOrder, maxStackCnt) {\n  let hierarchy = new SegHierarchy();\n  if (strictOrder != null) {\n    hierarchy.strictOrder = strictOrder;\n  }\n  if (maxStackCnt != null) {\n    hierarchy.maxStackCnt = maxStackCnt;\n  }\n  let hiddenEntries = hierarchy.addSegs(segInputs);\n  let hiddenGroups = groupIntersectingEntries(hiddenEntries);\n  let web = buildWeb(hierarchy);\n  web = stretchWeb(web, 1); // all levelCoords/thickness will have 0.0-1.0\n  let segRects = webToRects(web);\n  return {\n    segRects,\n    hiddenGroups\n  };\n}\nfunction buildWeb(hierarchy) {\n  const {\n    entriesByLevel\n  } = hierarchy;\n  const buildNode = cacheable((level, lateral) => level + ':' + lateral, (level, lateral) => {\n    let siblingRange = findNextLevelSegs(hierarchy, level, lateral);\n    let nextLevelRes = buildNodes(siblingRange, buildNode);\n    let entry = entriesByLevel[level][lateral];\n    return [Object.assign(Object.assign({}, entry), {\n      nextLevelNodes: nextLevelRes[0]\n    }), entry.thickness + nextLevelRes[1] // the pressure builds\n    ];\n  });\n  return buildNodes(entriesByLevel.length ? {\n    level: 0,\n    lateralStart: 0,\n    lateralEnd: entriesByLevel[0].length\n  } : null, buildNode)[0];\n}\nfunction buildNodes(siblingRange, buildNode) {\n  if (!siblingRange) {\n    return [[], 0];\n  }\n  let {\n    level,\n    lateralStart,\n    lateralEnd\n  } = siblingRange;\n  let lateral = lateralStart;\n  let pairs = [];\n  while (lateral < lateralEnd) {\n    pairs.push(buildNode(level, lateral));\n    lateral += 1;\n  }\n  pairs.sort(cmpDescPressures);\n  return [pairs.map(extractNode), pairs[0][1] // first item's pressure\n  ];\n}\nfunction cmpDescPressures(a, b) {\n  return b[1] - a[1];\n}\nfunction extractNode(a) {\n  return a[0];\n}\nfunction findNextLevelSegs(hierarchy, subjectLevel, subjectLateral) {\n  let {\n    levelCoords,\n    entriesByLevel\n  } = hierarchy;\n  let subjectEntry = entriesByLevel[subjectLevel][subjectLateral];\n  let afterSubject = levelCoords[subjectLevel] + subjectEntry.thickness;\n  let levelCnt = levelCoords.length;\n  let level = subjectLevel;\n  // skip past levels that are too high up\n  for (; level < levelCnt && levelCoords[level] < afterSubject; level += 1); // do nothing\n  for (; level < levelCnt; level += 1) {\n    let entries = entriesByLevel[level];\n    let entry;\n    let searchIndex = binarySearch(entries, subjectEntry.span.start, getEntrySpanEnd);\n    let lateralStart = searchIndex[0] + searchIndex[1]; // if exact match (which doesn't collide), go to next one\n    let lateralEnd = lateralStart;\n    while (\n    // loop through entries that horizontally intersect\n    (entry = entries[lateralEnd]) &&\n    // but not past the whole seg list\n    entry.span.start < subjectEntry.span.end) {\n      lateralEnd += 1;\n    }\n    if (lateralStart < lateralEnd) {\n      return {\n        level,\n        lateralStart,\n        lateralEnd\n      };\n    }\n  }\n  return null;\n}\nfunction stretchWeb(topLevelNodes, totalThickness) {\n  const stretchNode = cacheable((node, startCoord, prevThickness) => buildEntryKey(node), (node, startCoord, prevThickness) => {\n    let {\n      nextLevelNodes,\n      thickness\n    } = node;\n    let allThickness = thickness + prevThickness;\n    let thicknessFraction = thickness / allThickness;\n    let endCoord;\n    let newChildren = [];\n    if (!nextLevelNodes.length) {\n      endCoord = totalThickness;\n    } else {\n      for (let childNode of nextLevelNodes) {\n        if (endCoord === undefined) {\n          let res = stretchNode(childNode, startCoord, allThickness);\n          endCoord = res[0];\n          newChildren.push(res[1]);\n        } else {\n          let res = stretchNode(childNode, endCoord, 0);\n          newChildren.push(res[1]);\n        }\n      }\n    }\n    let newThickness = (endCoord - startCoord) * thicknessFraction;\n    return [endCoord - newThickness, Object.assign(Object.assign({}, node), {\n      thickness: newThickness,\n      nextLevelNodes: newChildren\n    })];\n  });\n  return topLevelNodes.map(node => stretchNode(node, 0, 0)[1]);\n}\n// not sorted in any particular order\nfunction webToRects(topLevelNodes) {\n  let rects = [];\n  const processNode = cacheable((node, levelCoord, stackDepth) => buildEntryKey(node), (node, levelCoord, stackDepth) => {\n    let rect = Object.assign(Object.assign({}, node), {\n      levelCoord,\n      stackDepth,\n      stackForward: 0\n    });\n    rects.push(rect);\n    return rect.stackForward = processNodes(node.nextLevelNodes, levelCoord + node.thickness, stackDepth + 1) + 1;\n  });\n  function processNodes(nodes, levelCoord, stackDepth) {\n    let stackForward = 0;\n    for (let node of nodes) {\n      stackForward = Math.max(processNode(node, levelCoord, stackDepth), stackForward);\n    }\n    return stackForward;\n  }\n  processNodes(topLevelNodes, 0, 0);\n  return rects; // TODO: sort rects by levelCoord to be consistent with toRects?\n}\n// TODO: move to general util\nfunction cacheable(keyFunc, workFunc) {\n  const cache = {};\n  return (...args) => {\n    let key = keyFunc(...args);\n    return key in cache ? cache[key] : cache[key] = workFunc(...args);\n  };\n}\nfunction computeSegVCoords(segs, colDate, slatCoords = null, eventMinHeight = 0) {\n  let vcoords = [];\n  if (slatCoords) {\n    for (let i = 0; i < segs.length; i += 1) {\n      let seg = segs[i];\n      let spanStart = slatCoords.computeDateTop(seg.start, colDate);\n      let spanEnd = Math.max(spanStart + (eventMinHeight || 0),\n      // :(\n      slatCoords.computeDateTop(seg.end, colDate));\n      vcoords.push({\n        start: Math.round(spanStart),\n        end: Math.round(spanEnd) //\n      });\n    }\n  }\n  return vcoords;\n}\nfunction computeFgSegPlacements(segs, segVCoords,\n// might not have for every seg\neventOrderStrict, eventMaxStack) {\n  let segInputs = [];\n  let dumbSegs = []; // segs without coords\n  for (let i = 0; i < segs.length; i += 1) {\n    let vcoords = segVCoords[i];\n    if (vcoords) {\n      segInputs.push({\n        index: i,\n        thickness: 1,\n        span: vcoords\n      });\n    } else {\n      dumbSegs.push(segs[i]);\n    }\n  }\n  let {\n    segRects,\n    hiddenGroups\n  } = buildPositioning(segInputs, eventOrderStrict, eventMaxStack);\n  let segPlacements = [];\n  for (let segRect of segRects) {\n    segPlacements.push({\n      seg: segs[segRect.index],\n      rect: segRect\n    });\n  }\n  for (let dumbSeg of dumbSegs) {\n    segPlacements.push({\n      seg: dumbSeg,\n      rect: null\n    });\n  }\n  return {\n    segPlacements,\n    hiddenGroups\n  };\n}\nconst DEFAULT_TIME_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  meridiem: false\n});\nclass TimeColEvent extends BaseComponent {\n  render() {\n    return createElement(StandardEvent, Object.assign({}, this.props, {\n      elClasses: ['fc-timegrid-event', 'fc-v-event', this.props.isShort && 'fc-timegrid-event-short'],\n      defaultTimeFormat: DEFAULT_TIME_FORMAT\n    }));\n  }\n}\nclass TimeCol extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.sortEventSegs = memoize(sortEventSegs);\n  }\n  // TODO: memoize event-placement?\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let isSelectMirror = options.selectMirror;\n    let mirrorSegs =\n    // yuck\n    props.eventDrag && props.eventDrag.segs || props.eventResize && props.eventResize.segs || isSelectMirror && props.dateSelectionSegs || [];\n    let interactionAffectedInstances =\n    // TODO: messy way to compute this\n    props.eventDrag && props.eventDrag.affectedInstances || props.eventResize && props.eventResize.affectedInstances || {};\n    let sortedFgSegs = this.sortEventSegs(props.fgEventSegs, options.eventOrder);\n    return createElement(DayCellContainer, {\n      elTag: \"td\",\n      elRef: props.elRef,\n      elClasses: ['fc-timegrid-col', ...(props.extraClassNames || [])],\n      elAttrs: Object.assign({\n        role: 'gridcell'\n      }, props.extraDataAttrs),\n      date: props.date,\n      dateProfile: props.dateProfile,\n      todayRange: props.todayRange,\n      extraRenderProps: props.extraRenderProps\n    }, InnerContent => createElement(\"div\", {\n      className: \"fc-timegrid-col-frame\"\n    }, createElement(\"div\", {\n      className: \"fc-timegrid-col-bg\"\n    }, this.renderFillSegs(props.businessHourSegs, 'non-business'), this.renderFillSegs(props.bgEventSegs, 'bg-event'), this.renderFillSegs(props.dateSelectionSegs, 'highlight')), createElement(\"div\", {\n      className: \"fc-timegrid-col-events\"\n    }, this.renderFgSegs(sortedFgSegs, interactionAffectedInstances, false, false, false)), createElement(\"div\", {\n      className: \"fc-timegrid-col-events\"\n    }, this.renderFgSegs(mirrorSegs, {}, Boolean(props.eventDrag), Boolean(props.eventResize), Boolean(isSelectMirror), 'mirror')), createElement(\"div\", {\n      className: \"fc-timegrid-now-indicator-container\"\n    }, this.renderNowIndicator(props.nowIndicatorSegs)), hasCustomDayCellContent(options) && createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-timegrid-col-misc']\n    })));\n  }\n  renderFgSegs(sortedFgSegs, segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey) {\n    let {\n      props\n    } = this;\n    if (props.forPrint) {\n      return renderPlainFgSegs(sortedFgSegs, props);\n    }\n    return this.renderPositionedFgSegs(sortedFgSegs, segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey);\n  }\n  renderPositionedFgSegs(segs,\n  // if not mirror, needs to be sorted\n  segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey) {\n    let {\n      eventMaxStack,\n      eventShortHeight,\n      eventOrderStrict,\n      eventMinHeight\n    } = this.context.options;\n    let {\n      date,\n      slatCoords,\n      eventSelection,\n      todayRange,\n      nowDate\n    } = this.props;\n    let isMirror = isDragging || isResizing || isDateSelecting;\n    let segVCoords = computeSegVCoords(segs, date, slatCoords, eventMinHeight);\n    let {\n      segPlacements,\n      hiddenGroups\n    } = computeFgSegPlacements(segs, segVCoords, eventOrderStrict, eventMaxStack);\n    return createElement(Fragment, null, this.renderHiddenGroups(hiddenGroups, segs), segPlacements.map(segPlacement => {\n      let {\n        seg,\n        rect\n      } = segPlacement;\n      let instanceId = seg.eventRange.instance.instanceId;\n      let isVisible = isMirror || Boolean(!segIsInvisible[instanceId] && rect);\n      let vStyle = computeSegVStyle(rect && rect.span);\n      let hStyle = !isMirror && rect ? this.computeSegHStyle(rect) : {\n        left: 0,\n        right: 0\n      };\n      let isInset = Boolean(rect) && rect.stackForward > 0;\n      let isShort = Boolean(rect) && rect.span.end - rect.span.start < eventShortHeight; // look at other places for this problem\n      return createElement(\"div\", {\n        className: 'fc-timegrid-event-harness' + (isInset ? ' fc-timegrid-event-harness-inset' : ''),\n        key: forcedKey || instanceId,\n        style: Object.assign(Object.assign({\n          visibility: isVisible ? '' : 'hidden'\n        }, vStyle), hStyle)\n      }, createElement(TimeColEvent, Object.assign({\n        seg: seg,\n        isDragging: isDragging,\n        isResizing: isResizing,\n        isDateSelecting: isDateSelecting,\n        isSelected: instanceId === eventSelection,\n        isShort: isShort\n      }, getSegMeta(seg, todayRange, nowDate))));\n    }));\n  }\n  // will already have eventMinHeight applied because segInputs already had it\n  renderHiddenGroups(hiddenGroups, segs) {\n    let {\n      extraDateSpan,\n      dateProfile,\n      todayRange,\n      nowDate,\n      eventSelection,\n      eventDrag,\n      eventResize\n    } = this.props;\n    return createElement(Fragment, null, hiddenGroups.map(hiddenGroup => {\n      let positionCss = computeSegVStyle(hiddenGroup.span);\n      let hiddenSegs = compileSegsFromEntries(hiddenGroup.entries, segs);\n      return createElement(TimeColMoreLink, {\n        key: buildIsoString(computeEarliestSegStart(hiddenSegs)),\n        hiddenSegs: hiddenSegs,\n        top: positionCss.top,\n        bottom: positionCss.bottom,\n        extraDateSpan: extraDateSpan,\n        dateProfile: dateProfile,\n        todayRange: todayRange,\n        nowDate: nowDate,\n        eventSelection: eventSelection,\n        eventDrag: eventDrag,\n        eventResize: eventResize\n      });\n    }));\n  }\n  renderFillSegs(segs, fillType) {\n    let {\n      props,\n      context\n    } = this;\n    let segVCoords = computeSegVCoords(segs, props.date, props.slatCoords, context.options.eventMinHeight); // don't assume all populated\n    let children = segVCoords.map((vcoords, i) => {\n      let seg = segs[i];\n      return createElement(\"div\", {\n        key: buildEventRangeKey(seg.eventRange),\n        className: \"fc-timegrid-bg-harness\",\n        style: computeSegVStyle(vcoords)\n      }, fillType === 'bg-event' ? createElement(BgEvent, Object.assign({\n        seg: seg\n      }, getSegMeta(seg, props.todayRange, props.nowDate))) : renderFill(fillType));\n    });\n    return createElement(Fragment, null, children);\n  }\n  renderNowIndicator(segs) {\n    let {\n      slatCoords,\n      date\n    } = this.props;\n    if (!slatCoords) {\n      return null;\n    }\n    return segs.map((seg, i) => createElement(NowIndicatorContainer\n    // key doesn't matter. will only ever be one\n    , {\n      // key doesn't matter. will only ever be one\n      key: i,\n      elClasses: ['fc-timegrid-now-indicator-line'],\n      elStyle: {\n        top: slatCoords.computeDateTop(seg.start, date)\n      },\n      isAxis: false,\n      date: date\n    }));\n  }\n  computeSegHStyle(segHCoords) {\n    let {\n      isRtl,\n      options\n    } = this.context;\n    let shouldOverlap = options.slotEventOverlap;\n    let nearCoord = segHCoords.levelCoord; // the left side if LTR. the right side if RTL. floating-point\n    let farCoord = segHCoords.levelCoord + segHCoords.thickness; // the right side if LTR. the left side if RTL. floating-point\n    let left; // amount of space from left edge, a fraction of the total width\n    let right; // amount of space from right edge, a fraction of the total width\n    if (shouldOverlap) {\n      // double the width, but don't go beyond the maximum forward coordinate (1.0)\n      farCoord = Math.min(1, nearCoord + (farCoord - nearCoord) * 2);\n    }\n    if (isRtl) {\n      left = 1 - farCoord;\n      right = nearCoord;\n    } else {\n      left = nearCoord;\n      right = 1 - farCoord;\n    }\n    let props = {\n      zIndex: segHCoords.stackDepth + 1,\n      left: left * 100 + '%',\n      right: right * 100 + '%'\n    };\n    if (shouldOverlap && !segHCoords.stackForward) {\n      // add padding to the edge so that forward stacked events don't cover the resizer's icon\n      props[isRtl ? 'marginLeft' : 'marginRight'] = 10 * 2; // 10 is a guesstimate of the icon's width\n    }\n    return props;\n  }\n}\nfunction renderPlainFgSegs(sortedFgSegs, {\n  todayRange,\n  nowDate,\n  eventSelection,\n  eventDrag,\n  eventResize\n}) {\n  let hiddenInstances = (eventDrag ? eventDrag.affectedInstances : null) || (eventResize ? eventResize.affectedInstances : null) || {};\n  return createElement(Fragment, null, sortedFgSegs.map(seg => {\n    let instanceId = seg.eventRange.instance.instanceId;\n    return createElement(\"div\", {\n      key: instanceId,\n      style: {\n        visibility: hiddenInstances[instanceId] ? 'hidden' : ''\n      }\n    }, createElement(TimeColEvent, Object.assign({\n      seg: seg,\n      isDragging: false,\n      isResizing: false,\n      isDateSelecting: false,\n      isSelected: instanceId === eventSelection,\n      isShort: false\n    }, getSegMeta(seg, todayRange, nowDate))));\n  }));\n}\nfunction computeSegVStyle(segVCoords) {\n  if (!segVCoords) {\n    return {\n      top: '',\n      bottom: ''\n    };\n  }\n  return {\n    top: segVCoords.start,\n    bottom: -segVCoords.end\n  };\n}\nfunction compileSegsFromEntries(segEntries, allSegs) {\n  return segEntries.map(segEntry => allSegs[segEntry.index]);\n}\nclass TimeColsContent extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.splitFgEventSegs = memoize(splitSegsByCol);\n    this.splitBgEventSegs = memoize(splitSegsByCol);\n    this.splitBusinessHourSegs = memoize(splitSegsByCol);\n    this.splitNowIndicatorSegs = memoize(splitSegsByCol);\n    this.splitDateSelectionSegs = memoize(splitSegsByCol);\n    this.splitEventDrag = memoize(splitInteractionByCol);\n    this.splitEventResize = memoize(splitInteractionByCol);\n    this.rootElRef = createRef();\n    this.cellElRefs = new RefMap();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let nowIndicatorTop = context.options.nowIndicator && props.slatCoords && props.slatCoords.safeComputeTop(props.nowDate); // might return void\n    let colCnt = props.cells.length;\n    let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, colCnt);\n    let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, colCnt);\n    let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, colCnt);\n    let nowIndicatorSegsByRow = this.splitNowIndicatorSegs(props.nowIndicatorSegs, colCnt);\n    let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, colCnt);\n    let eventDragByRow = this.splitEventDrag(props.eventDrag, colCnt);\n    let eventResizeByRow = this.splitEventResize(props.eventResize, colCnt);\n    return createElement(\"div\", {\n      className: \"fc-timegrid-cols\",\n      ref: this.rootElRef\n    }, createElement(\"table\", {\n      role: \"presentation\",\n      style: {\n        minWidth: props.tableMinWidth,\n        width: props.clientWidth\n      }\n    }, props.tableColGroupNode, createElement(\"tbody\", {\n      role: \"presentation\"\n    }, createElement(\"tr\", {\n      role: \"row\"\n    }, props.axis && createElement(\"td\", {\n      \"aria-hidden\": true,\n      className: \"fc-timegrid-col fc-timegrid-axis\"\n    }, createElement(\"div\", {\n      className: \"fc-timegrid-col-frame\"\n    }, createElement(\"div\", {\n      className: \"fc-timegrid-now-indicator-container\"\n    }, typeof nowIndicatorTop === 'number' && createElement(NowIndicatorContainer, {\n      elClasses: ['fc-timegrid-now-indicator-arrow'],\n      elStyle: {\n        top: nowIndicatorTop\n      },\n      isAxis: true,\n      date: props.nowDate\n    })))), props.cells.map((cell, i) => createElement(TimeCol, {\n      key: cell.key,\n      elRef: this.cellElRefs.createRef(cell.key),\n      dateProfile: props.dateProfile,\n      date: cell.date,\n      nowDate: props.nowDate,\n      todayRange: props.todayRange,\n      extraRenderProps: cell.extraRenderProps,\n      extraDataAttrs: cell.extraDataAttrs,\n      extraClassNames: cell.extraClassNames,\n      extraDateSpan: cell.extraDateSpan,\n      fgEventSegs: fgEventSegsByRow[i],\n      bgEventSegs: bgEventSegsByRow[i],\n      businessHourSegs: businessHourSegsByRow[i],\n      nowIndicatorSegs: nowIndicatorSegsByRow[i],\n      dateSelectionSegs: dateSelectionSegsByRow[i],\n      eventDrag: eventDragByRow[i],\n      eventResize: eventResizeByRow[i],\n      slatCoords: props.slatCoords,\n      eventSelection: props.eventSelection,\n      forPrint: props.forPrint\n    }))))));\n  }\n  componentDidMount() {\n    this.updateCoords();\n  }\n  componentDidUpdate() {\n    this.updateCoords();\n  }\n  updateCoords() {\n    let {\n      props\n    } = this;\n    if (props.onColCoords && props.clientWidth !== null // means sizing has stabilized\n    ) {\n      props.onColCoords(new PositionCache(this.rootElRef.current, collectCellEls(this.cellElRefs.currentMap, props.cells), true,\n      // horizontal\n      false));\n    }\n  }\n}\nfunction collectCellEls(elMap, cells) {\n  return cells.map(cell => elMap[cell.key]);\n}\n\n/* A component that renders one or more columns of vertical time slots\n----------------------------------------------------------------------------------------------------------------------*/\nclass TimeCols extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.processSlotOptions = memoize(processSlotOptions);\n    this.state = {\n      slatCoords: null\n    };\n    this.handleRootEl = el => {\n      if (el) {\n        this.context.registerInteractiveComponent(this, {\n          el,\n          isHitComboAllowed: this.props.isHitComboAllowed\n        });\n      } else {\n        this.context.unregisterInteractiveComponent(this);\n      }\n    };\n    this.handleScrollRequest = request => {\n      let {\n        onScrollTopRequest\n      } = this.props;\n      let {\n        slatCoords\n      } = this.state;\n      if (onScrollTopRequest && slatCoords) {\n        if (request.time) {\n          let top = slatCoords.computeTimeTop(request.time);\n          top = Math.ceil(top); // zoom can give weird floating-point values. rather scroll a little bit further\n          if (top) {\n            top += 1; // to overcome top border that slots beyond the first have. looks better\n          }\n          onScrollTopRequest(top);\n        }\n        return true;\n      }\n      return false;\n    };\n    this.handleColCoords = colCoords => {\n      this.colCoords = colCoords;\n    };\n    this.handleSlatCoords = slatCoords => {\n      this.setState({\n        slatCoords\n      });\n      if (this.props.onSlatCoords) {\n        this.props.onSlatCoords(slatCoords);\n      }\n    };\n  }\n  render() {\n    let {\n      props,\n      state\n    } = this;\n    return createElement(\"div\", {\n      className: \"fc-timegrid-body\",\n      ref: this.handleRootEl,\n      style: {\n        // these props are important to give this wrapper correct dimensions for interactions\n        // TODO: if we set it here, can we avoid giving to inner tables?\n        width: props.clientWidth,\n        minWidth: props.tableMinWidth\n      }\n    }, createElement(TimeColsSlats, {\n      axis: props.axis,\n      dateProfile: props.dateProfile,\n      slatMetas: props.slatMetas,\n      clientWidth: props.clientWidth,\n      minHeight: props.expandRows ? props.clientHeight : '',\n      tableMinWidth: props.tableMinWidth,\n      tableColGroupNode: props.axis ? props.tableColGroupNode : null /* axis depends on the colgroup's shrinking */,\n      onCoords: this.handleSlatCoords\n    }), createElement(TimeColsContent, {\n      cells: props.cells,\n      axis: props.axis,\n      dateProfile: props.dateProfile,\n      businessHourSegs: props.businessHourSegs,\n      bgEventSegs: props.bgEventSegs,\n      fgEventSegs: props.fgEventSegs,\n      dateSelectionSegs: props.dateSelectionSegs,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      todayRange: props.todayRange,\n      nowDate: props.nowDate,\n      nowIndicatorSegs: props.nowIndicatorSegs,\n      clientWidth: props.clientWidth,\n      tableMinWidth: props.tableMinWidth,\n      tableColGroupNode: props.tableColGroupNode,\n      slatCoords: state.slatCoords,\n      onColCoords: this.handleColCoords,\n      forPrint: props.forPrint\n    }));\n  }\n  componentDidMount() {\n    this.scrollResponder = this.context.createScrollResponder(this.handleScrollRequest);\n  }\n  componentDidUpdate(prevProps) {\n    this.scrollResponder.update(prevProps.dateProfile !== this.props.dateProfile);\n  }\n  componentWillUnmount() {\n    this.scrollResponder.detach();\n  }\n  queryHit(positionLeft, positionTop) {\n    let {\n      dateEnv,\n      options\n    } = this.context;\n    let {\n      colCoords\n    } = this;\n    let {\n      dateProfile\n    } = this.props;\n    let {\n      slatCoords\n    } = this.state;\n    let {\n      snapDuration,\n      snapsPerSlot\n    } = this.processSlotOptions(this.props.slotDuration, options.snapDuration);\n    let colIndex = colCoords.leftToIndex(positionLeft);\n    let slatIndex = slatCoords.positions.topToIndex(positionTop);\n    if (colIndex != null && slatIndex != null) {\n      let cell = this.props.cells[colIndex];\n      let slatTop = slatCoords.positions.tops[slatIndex];\n      let slatHeight = slatCoords.positions.getHeight(slatIndex);\n      let partial = (positionTop - slatTop) / slatHeight; // floating point number between 0 and 1\n      let localSnapIndex = Math.floor(partial * snapsPerSlot); // the snap # relative to start of slat\n      let snapIndex = slatIndex * snapsPerSlot + localSnapIndex;\n      let dayDate = this.props.cells[colIndex].date;\n      let time = addDurations(dateProfile.slotMinTime, multiplyDuration(snapDuration, snapIndex));\n      let start = dateEnv.add(dayDate, time);\n      let end = dateEnv.add(start, snapDuration);\n      return {\n        dateProfile,\n        dateSpan: Object.assign({\n          range: {\n            start,\n            end\n          },\n          allDay: false\n        }, cell.extraDateSpan),\n        dayEl: colCoords.els[colIndex],\n        rect: {\n          left: colCoords.lefts[colIndex],\n          right: colCoords.rights[colIndex],\n          top: slatTop,\n          bottom: slatTop + slatHeight\n        },\n        layer: 0\n      };\n    }\n    return null;\n  }\n}\nfunction processSlotOptions(slotDuration, snapDurationOverride) {\n  let snapDuration = snapDurationOverride || slotDuration;\n  let snapsPerSlot = wholeDivideDurations(slotDuration, snapDuration);\n  if (snapsPerSlot === null) {\n    snapDuration = slotDuration;\n    snapsPerSlot = 1;\n    // TODO: say warning?\n  }\n  return {\n    snapDuration,\n    snapsPerSlot\n  };\n}\nclass DayTimeColsSlicer extends Slicer {\n  sliceRange(range, dayRanges) {\n    let segs = [];\n    for (let col = 0; col < dayRanges.length; col += 1) {\n      let segRange = intersectRanges(range, dayRanges[col]);\n      if (segRange) {\n        segs.push({\n          start: segRange.start,\n          end: segRange.end,\n          isStart: segRange.start.valueOf() === range.start.valueOf(),\n          isEnd: segRange.end.valueOf() === range.end.valueOf(),\n          col\n        });\n      }\n    }\n    return segs;\n  }\n}\nclass DayTimeCols extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.buildDayRanges = memoize(buildDayRanges);\n    this.slicer = new DayTimeColsSlicer();\n    this.timeColsRef = createRef();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      dateProfile,\n      dayTableModel\n    } = props;\n    let {\n      nowIndicator,\n      nextDayThreshold\n    } = context.options;\n    let dayRanges = this.buildDayRanges(dayTableModel, dateProfile, context.dateEnv);\n    // give it the first row of cells\n    // TODO: would move this further down hierarchy, but sliceNowDate needs it\n    return createElement(NowTimer, {\n      unit: nowIndicator ? 'minute' : 'day'\n    }, (nowDate, todayRange) => createElement(TimeCols, Object.assign({\n      ref: this.timeColsRef\n    }, this.slicer.sliceProps(props, dateProfile, null, context, dayRanges), {\n      forPrint: props.forPrint,\n      axis: props.axis,\n      dateProfile: dateProfile,\n      slatMetas: props.slatMetas,\n      slotDuration: props.slotDuration,\n      cells: dayTableModel.cells[0],\n      tableColGroupNode: props.tableColGroupNode,\n      tableMinWidth: props.tableMinWidth,\n      clientWidth: props.clientWidth,\n      clientHeight: props.clientHeight,\n      expandRows: props.expandRows,\n      nowDate: nowDate,\n      nowIndicatorSegs: nowIndicator && this.slicer.sliceNowDate(nowDate, dateProfile, nextDayThreshold, context, dayRanges),\n      todayRange: todayRange,\n      onScrollTopRequest: props.onScrollTopRequest,\n      onSlatCoords: props.onSlatCoords\n    })));\n  }\n}\nfunction buildDayRanges(dayTableModel, dateProfile, dateEnv) {\n  let ranges = [];\n  for (let date of dayTableModel.headerDates) {\n    ranges.push({\n      start: dateEnv.add(date, dateProfile.slotMinTime),\n      end: dateEnv.add(date, dateProfile.slotMaxTime)\n    });\n  }\n  return ranges;\n}\n\n// potential nice values for the slot-duration and interval-duration\n// from largest to smallest\nconst STOCK_SUB_DURATIONS = [{\n  hours: 1\n}, {\n  minutes: 30\n}, {\n  minutes: 15\n}, {\n  seconds: 30\n}, {\n  seconds: 15\n}];\nfunction buildSlatMetas(slotMinTime, slotMaxTime, explicitLabelInterval, slotDuration, dateEnv) {\n  let dayStart = new Date(0);\n  let slatTime = slotMinTime;\n  let slatIterator = createDuration(0);\n  let labelInterval = explicitLabelInterval || computeLabelInterval(slotDuration);\n  let metas = [];\n  while (asRoughMs(slatTime) < asRoughMs(slotMaxTime)) {\n    let date = dateEnv.add(dayStart, slatTime);\n    let isLabeled = wholeDivideDurations(slatIterator, labelInterval) !== null;\n    metas.push({\n      date,\n      time: slatTime,\n      key: date.toISOString(),\n      isoTimeStr: formatIsoTimeString(date),\n      isLabeled\n    });\n    slatTime = addDurations(slatTime, slotDuration);\n    slatIterator = addDurations(slatIterator, slotDuration);\n  }\n  return metas;\n}\n// Computes an automatic value for slotLabelInterval\nfunction computeLabelInterval(slotDuration) {\n  let i;\n  let labelInterval;\n  let slotsPerLabel;\n  // find the smallest stock label interval that results in more than one slots-per-label\n  for (i = STOCK_SUB_DURATIONS.length - 1; i >= 0; i -= 1) {\n    labelInterval = createDuration(STOCK_SUB_DURATIONS[i]);\n    slotsPerLabel = wholeDivideDurations(labelInterval, slotDuration);\n    if (slotsPerLabel !== null && slotsPerLabel > 1) {\n      return labelInterval;\n    }\n  }\n  return slotDuration; // fall back\n}\nclass DayTimeColsView extends TimeColsView {\n  constructor() {\n    super(...arguments);\n    this.buildTimeColsModel = memoize(buildTimeColsModel);\n    this.buildSlatMetas = memoize(buildSlatMetas);\n  }\n  render() {\n    let {\n      options,\n      dateEnv,\n      dateProfileGenerator\n    } = this.context;\n    let {\n      props\n    } = this;\n    let {\n      dateProfile\n    } = props;\n    let dayTableModel = this.buildTimeColsModel(dateProfile, dateProfileGenerator);\n    let splitProps = this.allDaySplitter.splitProps(props);\n    let slatMetas = this.buildSlatMetas(dateProfile.slotMinTime, dateProfile.slotMaxTime, options.slotLabelInterval, options.slotDuration, dateEnv);\n    let {\n      dayMinWidth\n    } = options;\n    let hasAttachedAxis = !dayMinWidth;\n    let hasDetachedAxis = dayMinWidth;\n    let headerContent = options.dayHeaders && createElement(DayHeader, {\n      dates: dayTableModel.headerDates,\n      dateProfile: dateProfile,\n      datesRepDistinctDays: true,\n      renderIntro: hasAttachedAxis ? this.renderHeadAxis : null\n    });\n    let allDayContent = options.allDaySlot !== false && (contentArg => createElement(DayTable, Object.assign({}, splitProps.allDay, {\n      dateProfile: dateProfile,\n      dayTableModel: dayTableModel,\n      nextDayThreshold: options.nextDayThreshold,\n      tableMinWidth: contentArg.tableMinWidth,\n      colGroupNode: contentArg.tableColGroupNode,\n      renderRowIntro: hasAttachedAxis ? this.renderTableRowAxis : null,\n      showWeekNumbers: false,\n      expandRows: false,\n      headerAlignElRef: this.headerElRef,\n      clientWidth: contentArg.clientWidth,\n      clientHeight: contentArg.clientHeight,\n      forPrint: props.forPrint\n    }, this.getAllDayMaxEventProps())));\n    let timeGridContent = contentArg => createElement(DayTimeCols, Object.assign({}, splitProps.timed, {\n      dayTableModel: dayTableModel,\n      dateProfile: dateProfile,\n      axis: hasAttachedAxis,\n      slotDuration: options.slotDuration,\n      slatMetas: slatMetas,\n      forPrint: props.forPrint,\n      tableColGroupNode: contentArg.tableColGroupNode,\n      tableMinWidth: contentArg.tableMinWidth,\n      clientWidth: contentArg.clientWidth,\n      clientHeight: contentArg.clientHeight,\n      onSlatCoords: this.handleSlatCoords,\n      expandRows: contentArg.expandRows,\n      onScrollTopRequest: this.handleScrollTopRequest\n    }));\n    return hasDetachedAxis ? this.renderHScrollLayout(headerContent, allDayContent, timeGridContent, dayTableModel.colCnt, dayMinWidth, slatMetas, this.state.slatCoords) : this.renderSimpleLayout(headerContent, allDayContent, timeGridContent);\n  }\n}\nfunction buildTimeColsModel(dateProfile, dateProfileGenerator) {\n  let daySeries = new DaySeriesModel(dateProfile.renderRange, dateProfileGenerator);\n  return new DayTableModel(daySeries, false);\n}\nvar css_248z = \".fc-v-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-v-event .fc-event-main{color:var(--fc-event-text-color);height:100%}.fc-v-event .fc-event-main-frame{display:flex;flex-direction:column;height:100%}.fc-v-event .fc-event-time{flex-grow:0;flex-shrink:0;max-height:100%;overflow:hidden}.fc-v-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-height:0}.fc-v-event .fc-event-title{bottom:0;max-height:100%;overflow:hidden;top:0}.fc-v-event:not(.fc-event-start){border-top-left-radius:0;border-top-right-radius:0;border-top-width:0}.fc-v-event:not(.fc-event-end){border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-width:0}.fc-v-event.fc-event-selected:before{left:-10px;right:-10px}.fc-v-event .fc-event-resizer-start{cursor:n-resize}.fc-v-event .fc-event-resizer-end{cursor:s-resize}.fc-v-event:not(.fc-event-selected) .fc-event-resizer{height:var(--fc-event-resizer-thickness);left:0;right:0}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-start{top:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer{left:50%;margin-left:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-start{top:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc .fc-timegrid .fc-daygrid-body{z-index:2}.fc .fc-timegrid-divider{padding:0 0 2px}.fc .fc-timegrid-body{min-height:100%;position:relative;z-index:1}.fc .fc-timegrid-axis-chunk{position:relative}.fc .fc-timegrid-axis-chunk>table,.fc .fc-timegrid-slots{position:relative;z-index:1}.fc .fc-timegrid-slot{border-bottom:0;height:1.5em}.fc .fc-timegrid-slot:empty:before{content:\\\"\\\\00a0\\\"}.fc .fc-timegrid-slot-minor{border-top-style:dotted}.fc .fc-timegrid-slot-label-cushion{display:inline-block;white-space:nowrap}.fc .fc-timegrid-slot-label{vertical-align:middle}.fc .fc-timegrid-axis-cushion,.fc .fc-timegrid-slot-label-cushion{padding:0 4px}.fc .fc-timegrid-axis-frame-liquid{height:100%}.fc .fc-timegrid-axis-frame{align-items:center;display:flex;justify-content:flex-end;overflow:hidden}.fc .fc-timegrid-axis-cushion{flex-shrink:0;max-width:60px}.fc-direction-ltr .fc-timegrid-slot-label-frame{text-align:right}.fc-direction-rtl .fc-timegrid-slot-label-frame{text-align:left}.fc-liquid-hack .fc-timegrid-axis-frame-liquid{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-timegrid-col-frame{min-height:100%;position:relative}.fc-media-screen.fc-liquid-hack .fc-timegrid-col-frame{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols{bottom:0;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols>table{height:100%}.fc-media-screen .fc-timegrid-col-bg,.fc-media-screen .fc-timegrid-col-events,.fc-media-screen .fc-timegrid-now-indicator-container{left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col-bg{z-index:2}.fc .fc-timegrid-col-bg .fc-non-business{z-index:1}.fc .fc-timegrid-col-bg .fc-bg-event{z-index:2}.fc .fc-timegrid-col-bg .fc-highlight{z-index:3}.fc .fc-timegrid-bg-harness{left:0;position:absolute;right:0}.fc .fc-timegrid-col-events{z-index:3}.fc .fc-timegrid-now-indicator-container{bottom:0;overflow:hidden}.fc-direction-ltr .fc-timegrid-col-events{margin:0 2.5% 0 2px}.fc-direction-rtl .fc-timegrid-col-events{margin:0 2px 0 2.5%}.fc-timegrid-event-harness{position:absolute}.fc-timegrid-event-harness>.fc-timegrid-event{bottom:0;left:0;position:absolute;right:0;top:0}.fc-timegrid-event-harness-inset .fc-timegrid-event,.fc-timegrid-event.fc-event-mirror,.fc-timegrid-more-link{box-shadow:0 0 0 1px var(--fc-page-bg-color)}.fc-timegrid-event,.fc-timegrid-more-link{border-radius:3px;font-size:var(--fc-small-font-size)}.fc-timegrid-event{margin-bottom:1px}.fc-timegrid-event .fc-event-main{padding:1px 1px 0}.fc-timegrid-event .fc-event-time{font-size:var(--fc-small-font-size);margin-bottom:1px;white-space:nowrap}.fc-timegrid-event-short .fc-event-main-frame{flex-direction:row;overflow:hidden}.fc-timegrid-event-short .fc-event-time:after{content:\\\"\\\\00a0-\\\\00a0\\\"}.fc-timegrid-event-short .fc-event-title{font-size:var(--fc-small-font-size)}.fc-timegrid-more-link{background:var(--fc-more-link-bg-color);color:var(--fc-more-link-text-color);cursor:pointer;margin-bottom:1px;position:absolute;z-index:9999}.fc-timegrid-more-link-inner{padding:3px 2px;top:0}.fc-direction-ltr .fc-timegrid-more-link{right:0}.fc-direction-rtl .fc-timegrid-more-link{left:0}.fc .fc-timegrid-now-indicator-arrow,.fc .fc-timegrid-now-indicator-line{pointer-events:none}.fc .fc-timegrid-now-indicator-line{border-color:var(--fc-now-indicator-color);border-style:solid;border-width:1px 0 0;left:0;position:absolute;right:0;z-index:4}.fc .fc-timegrid-now-indicator-arrow{border-color:var(--fc-now-indicator-color);border-style:solid;margin-top:-5px;position:absolute;z-index:4}.fc-direction-ltr .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 0 5px 6px;left:0}.fc-direction-rtl .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 6px 5px 0;right:0}\";\ninjectStyles(css_248z);\nexport { DayTimeCols, DayTimeColsSlicer, DayTimeColsView, TimeCols, TimeColsSlatsCoords, TimeColsView, buildDayRanges, buildSlatMetas, buildTimeColsModel };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}