{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { VISUAL_DIMENSIONS } from '../../util/types.js';\nimport SeriesDimensionDefine from '../SeriesDimensionDefine.js';\nimport { createHashMap, defaults, each, extend, isObject, isString } from 'zrender/lib/core/util.js';\nimport { createSourceFromSeriesDataOption, isSourceInstance } from '../Source.js';\nimport { CtorInt32Array } from '../DataStore.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { BE_ORDINAL, guessOrdinal } from './sourceHelper.js';\nimport { createDimNameMap, ensureSourceDimNameMap, SeriesDataSchema, shouldOmitUnusedDimensions } from './SeriesDataSchema.js';\n/**\r\n * For outside usage compat (like echarts-gl are using it).\r\n */\nexport function createDimensions(source, opt) {\n  return prepareSeriesDataSchema(source, opt).dimensions;\n}\n/**\r\n * This method builds the relationship between:\r\n * + \"what the coord sys or series requires (see `coordDimensions`)\",\r\n * + \"what the user defines (in `encode` and `dimensions`, see `opt.dimensionsDefine` and `opt.encodeDefine`)\"\r\n * + \"what the data source provids (see `source`)\".\r\n *\r\n * Some guess strategy will be adapted if user does not define something.\r\n * If no 'value' dimension specified, the first no-named dimension will be\r\n * named as 'value'.\r\n *\r\n * @return The results are always sorted by `storeDimIndex` asc.\r\n */\nexport default function prepareSeriesDataSchema(\n// TODO: TYPE completeDimensions type\nsource, opt) {\n  if (!isSourceInstance(source)) {\n    source = createSourceFromSeriesDataOption(source);\n  }\n  opt = opt || {};\n  var sysDims = opt.coordDimensions || [];\n  var dimsDef = opt.dimensionsDefine || source.dimensionsDefine || [];\n  var coordDimNameMap = createHashMap();\n  var resultList = [];\n  var dimCount = getDimCount(source, sysDims, dimsDef, opt.dimensionsCount);\n  // Try to ignore unused dimensions if sharing a high dimension datastore\n  // 30 is an experience value.\n  var omitUnusedDimensions = opt.canOmitUnusedDimensions && shouldOmitUnusedDimensions(dimCount);\n  var isUsingSourceDimensionsDef = dimsDef === source.dimensionsDefine;\n  var dataDimNameMap = isUsingSourceDimensionsDef ? ensureSourceDimNameMap(source) : createDimNameMap(dimsDef);\n  var encodeDef = opt.encodeDefine;\n  if (!encodeDef && opt.encodeDefaulter) {\n    encodeDef = opt.encodeDefaulter(source, dimCount);\n  }\n  var encodeDefMap = createHashMap(encodeDef);\n  var indicesMap = new CtorInt32Array(dimCount);\n  for (var i = 0; i < indicesMap.length; i++) {\n    indicesMap[i] = -1;\n  }\n  function getResultItem(dimIdx) {\n    var idx = indicesMap[dimIdx];\n    if (idx < 0) {\n      var dimDefItemRaw = dimsDef[dimIdx];\n      var dimDefItem = isObject(dimDefItemRaw) ? dimDefItemRaw : {\n        name: dimDefItemRaw\n      };\n      var resultItem = new SeriesDimensionDefine();\n      var userDimName = dimDefItem.name;\n      if (userDimName != null && dataDimNameMap.get(userDimName) != null) {\n        // Only if `series.dimensions` is defined in option\n        // displayName, will be set, and dimension will be displayed vertically in\n        // tooltip by default.\n        resultItem.name = resultItem.displayName = userDimName;\n      }\n      dimDefItem.type != null && (resultItem.type = dimDefItem.type);\n      dimDefItem.displayName != null && (resultItem.displayName = dimDefItem.displayName);\n      var newIdx = resultList.length;\n      indicesMap[dimIdx] = newIdx;\n      resultItem.storeDimIndex = dimIdx;\n      resultList.push(resultItem);\n      return resultItem;\n    }\n    return resultList[idx];\n  }\n  if (!omitUnusedDimensions) {\n    for (var i = 0; i < dimCount; i++) {\n      getResultItem(i);\n    }\n  }\n  // Set `coordDim` and `coordDimIndex` by `encodeDefMap` and normalize `encodeDefMap`.\n  encodeDefMap.each(function (dataDimsRaw, coordDim) {\n    var dataDims = normalizeToArray(dataDimsRaw).slice();\n    // Note: It is allowed that `dataDims.length` is `0`, e.g., options is\n    // `{encode: {x: -1, y: 1}}`. Should not filter anything in\n    // this case.\n    if (dataDims.length === 1 && !isString(dataDims[0]) && dataDims[0] < 0) {\n      encodeDefMap.set(coordDim, false);\n      return;\n    }\n    var validDataDims = encodeDefMap.set(coordDim, []);\n    each(dataDims, function (resultDimIdxOrName, idx) {\n      // The input resultDimIdx can be dim name or index.\n      var resultDimIdx = isString(resultDimIdxOrName) ? dataDimNameMap.get(resultDimIdxOrName) : resultDimIdxOrName;\n      if (resultDimIdx != null && resultDimIdx < dimCount) {\n        validDataDims[idx] = resultDimIdx;\n        applyDim(getResultItem(resultDimIdx), coordDim, idx);\n      }\n    });\n  });\n  // Apply templates and default order from `sysDims`.\n  var availDimIdx = 0;\n  each(sysDims, function (sysDimItemRaw) {\n    var coordDim;\n    var sysDimItemDimsDef;\n    var sysDimItemOtherDims;\n    var sysDimItem;\n    if (isString(sysDimItemRaw)) {\n      coordDim = sysDimItemRaw;\n      sysDimItem = {};\n    } else {\n      sysDimItem = sysDimItemRaw;\n      coordDim = sysDimItem.name;\n      var ordinalMeta = sysDimItem.ordinalMeta;\n      sysDimItem.ordinalMeta = null;\n      sysDimItem = extend({}, sysDimItem);\n      sysDimItem.ordinalMeta = ordinalMeta;\n      // `coordDimIndex` should not be set directly.\n      sysDimItemDimsDef = sysDimItem.dimsDef;\n      sysDimItemOtherDims = sysDimItem.otherDims;\n      sysDimItem.name = sysDimItem.coordDim = sysDimItem.coordDimIndex = sysDimItem.dimsDef = sysDimItem.otherDims = null;\n    }\n    var dataDims = encodeDefMap.get(coordDim);\n    // negative resultDimIdx means no need to mapping.\n    if (dataDims === false) {\n      return;\n    }\n    dataDims = normalizeToArray(dataDims);\n    // dimensions provides default dim sequences.\n    if (!dataDims.length) {\n      for (var i = 0; i < (sysDimItemDimsDef && sysDimItemDimsDef.length || 1); i++) {\n        while (availDimIdx < dimCount && getResultItem(availDimIdx).coordDim != null) {\n          availDimIdx++;\n        }\n        availDimIdx < dimCount && dataDims.push(availDimIdx++);\n      }\n    }\n    // Apply templates.\n    each(dataDims, function (resultDimIdx, coordDimIndex) {\n      var resultItem = getResultItem(resultDimIdx);\n      // Coordinate system has a higher priority on dim type than source.\n      if (isUsingSourceDimensionsDef && sysDimItem.type != null) {\n        resultItem.type = sysDimItem.type;\n      }\n      applyDim(defaults(resultItem, sysDimItem), coordDim, coordDimIndex);\n      if (resultItem.name == null && sysDimItemDimsDef) {\n        var sysDimItemDimsDefItem = sysDimItemDimsDef[coordDimIndex];\n        !isObject(sysDimItemDimsDefItem) && (sysDimItemDimsDefItem = {\n          name: sysDimItemDimsDefItem\n        });\n        resultItem.name = resultItem.displayName = sysDimItemDimsDefItem.name;\n        resultItem.defaultTooltip = sysDimItemDimsDefItem.defaultTooltip;\n      }\n      // FIXME refactor, currently only used in case: {otherDims: {tooltip: false}}\n      sysDimItemOtherDims && defaults(resultItem.otherDims, sysDimItemOtherDims);\n    });\n  });\n  function applyDim(resultItem, coordDim, coordDimIndex) {\n    if (VISUAL_DIMENSIONS.get(coordDim) != null) {\n      resultItem.otherDims[coordDim] = coordDimIndex;\n    } else {\n      resultItem.coordDim = coordDim;\n      resultItem.coordDimIndex = coordDimIndex;\n      coordDimNameMap.set(coordDim, true);\n    }\n  }\n  // Make sure the first extra dim is 'value'.\n  var generateCoord = opt.generateCoord;\n  var generateCoordCount = opt.generateCoordCount;\n  var fromZero = generateCoordCount != null;\n  generateCoordCount = generateCoord ? generateCoordCount || 1 : 0;\n  var extra = generateCoord || 'value';\n  function ifNoNameFillWithCoordName(resultItem) {\n    if (resultItem.name == null) {\n      // Duplication will be removed in the next step.\n      resultItem.name = resultItem.coordDim;\n    }\n  }\n  // Set dim `name` and other `coordDim` and other props.\n  if (!omitUnusedDimensions) {\n    for (var resultDimIdx = 0; resultDimIdx < dimCount; resultDimIdx++) {\n      var resultItem = getResultItem(resultDimIdx);\n      var coordDim = resultItem.coordDim;\n      if (coordDim == null) {\n        // TODO no need to generate coordDim for isExtraCoord?\n        resultItem.coordDim = genCoordDimName(extra, coordDimNameMap, fromZero);\n        resultItem.coordDimIndex = 0;\n        // Series specified generateCoord is using out.\n        if (!generateCoord || generateCoordCount <= 0) {\n          resultItem.isExtraCoord = true;\n        }\n        generateCoordCount--;\n      }\n      ifNoNameFillWithCoordName(resultItem);\n      if (resultItem.type == null && (guessOrdinal(source, resultDimIdx) === BE_ORDINAL.Must\n      // Consider the case:\n      // {\n      //    dataset: {source: [\n      //        ['2001', 123],\n      //        ['2002', 456],\n      //        ...\n      //        ['The others', 987],\n      //    ]},\n      //    series: {type: 'pie'}\n      // }\n      // The first column should better be treated as a \"ordinal\" although it\n      // might not be detected as an \"ordinal\" by `guessOrdinal`.\n      || resultItem.isExtraCoord && (resultItem.otherDims.itemName != null || resultItem.otherDims.seriesName != null))) {\n        resultItem.type = 'ordinal';\n      }\n    }\n  } else {\n    each(resultList, function (resultItem) {\n      // PENDING: guessOrdinal or let user specify type: 'ordinal' manually?\n      ifNoNameFillWithCoordName(resultItem);\n    });\n    // Sort dimensions: there are some rule that use the last dim as label,\n    // and for some latter travel process easier.\n    resultList.sort(function (item0, item1) {\n      return item0.storeDimIndex - item1.storeDimIndex;\n    });\n  }\n  removeDuplication(resultList);\n  return new SeriesDataSchema({\n    source: source,\n    dimensions: resultList,\n    fullDimensionCount: dimCount,\n    dimensionOmitted: omitUnusedDimensions\n  });\n}\nfunction removeDuplication(result) {\n  var duplicationMap = createHashMap();\n  for (var i = 0; i < result.length; i++) {\n    var dim = result[i];\n    var dimOriginalName = dim.name;\n    var count = duplicationMap.get(dimOriginalName) || 0;\n    if (count > 0) {\n      // Starts from 0.\n      dim.name = dimOriginalName + (count - 1);\n    }\n    count++;\n    duplicationMap.set(dimOriginalName, count);\n  }\n}\n// ??? TODO\n// Originally detect dimCount by data[0]. Should we\n// optimize it to only by sysDims and dimensions and encode.\n// So only necessary dims will be initialized.\n// But\n// (1) custom series should be considered. where other dims\n// may be visited.\n// (2) sometimes user need to calculate bubble size or use visualMap\n// on other dimensions besides coordSys needed.\n// So, dims that is not used by system, should be shared in data store?\nfunction getDimCount(source, sysDims, dimsDef, optDimCount) {\n  // Note that the result dimCount should not small than columns count\n  // of data, otherwise `dataDimNameMap` checking will be incorrect.\n  var dimCount = Math.max(source.dimensionsDetectedCount || 1, sysDims.length, dimsDef.length, optDimCount || 0);\n  each(sysDims, function (sysDimItem) {\n    var sysDimItemDimsDef;\n    if (isObject(sysDimItem) && (sysDimItemDimsDef = sysDimItem.dimsDef)) {\n      dimCount = Math.max(dimCount, sysDimItemDimsDef.length);\n    }\n  });\n  return dimCount;\n}\nfunction genCoordDimName(name, map, fromZero) {\n  if (fromZero || map.hasKey(name)) {\n    var i = 0;\n    while (map.hasKey(name + i)) {\n      i++;\n    }\n    name += i;\n  }\n  map.set(name, true);\n  return name;\n}", "map": {"version": 3, "names": ["VISUAL_DIMENSIONS", "SeriesDimensionDefine", "createHashMap", "defaults", "each", "extend", "isObject", "isString", "createSourceFromSeriesDataOption", "isSourceInstance", "CtorInt32Array", "normalizeToArray", "BE_ORDINAL", "guessOrdinal", "createDimNameMap", "ensureSourceDimNameMap", "SeriesDataSchema", "shouldOmitUnusedDimensions", "createDimensions", "source", "opt", "prepareSeriesDataSchema", "dimensions", "sysDims", "coordDimensions", "dimsDef", "dimensionsDefine", "coordDimNameMap", "resultList", "dimCount", "getDimCount", "dimensionsCount", "omitUnusedDimensions", "canOmitUnusedDimensions", "isUsingSourceDimensionsDef", "dataDimNameMap", "encodeDef", "encodeDefine", "encodeDefaulter", "encodeDefMap", "indicesMap", "i", "length", "getResultItem", "dimIdx", "idx", "dimDefItemRaw", "dimDefItem", "name", "resultItem", "userDimName", "get", "displayName", "type", "newIdx", "storeDimIndex", "push", "dataDimsRaw", "coordDim", "dataDims", "slice", "set", "validDataDims", "resultDimIdxOrName", "resultDimIdx", "applyDim", "availDimIdx", "sysDimItemRaw", "sysDimItemDimsDef", "sysDimItemOtherDims", "sysDimItem", "ordinalMeta", "otherDims", "coordDimIndex", "sysDimItemDimsDefItem", "defaultTooltip", "generateCoord", "generateCoordCount", "fromZero", "extra", "ifNoNameFillWithCoordName", "genCoordDimName", "isExtraCoord", "Must", "itemName", "seriesName", "sort", "item0", "item1", "removeDuplication", "fullDimensionCount", "dimensionOmitted", "result", "duplicationMap", "dim", "dimOriginalName", "count", "optDimCount", "Math", "max", "dimensionsDetectedCount", "map", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/data/helper/createDimensions.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { VISUAL_DIMENSIONS } from '../../util/types.js';\nimport SeriesDimensionDefine from '../SeriesDimensionDefine.js';\nimport { createHashMap, defaults, each, extend, isObject, isString } from 'zrender/lib/core/util.js';\nimport { createSourceFromSeriesDataOption, isSourceInstance } from '../Source.js';\nimport { CtorInt32Array } from '../DataStore.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { BE_ORDINAL, guessOrdinal } from './sourceHelper.js';\nimport { createDimNameMap, ensureSourceDimNameMap, SeriesDataSchema, shouldOmitUnusedDimensions } from './SeriesDataSchema.js';\n/**\r\n * For outside usage compat (like echarts-gl are using it).\r\n */\nexport function createDimensions(source, opt) {\n  return prepareSeriesDataSchema(source, opt).dimensions;\n}\n/**\r\n * This method builds the relationship between:\r\n * + \"what the coord sys or series requires (see `coordDimensions`)\",\r\n * + \"what the user defines (in `encode` and `dimensions`, see `opt.dimensionsDefine` and `opt.encodeDefine`)\"\r\n * + \"what the data source provids (see `source`)\".\r\n *\r\n * Some guess strategy will be adapted if user does not define something.\r\n * If no 'value' dimension specified, the first no-named dimension will be\r\n * named as 'value'.\r\n *\r\n * @return The results are always sorted by `storeDimIndex` asc.\r\n */\nexport default function prepareSeriesDataSchema(\n// TODO: TYPE completeDimensions type\nsource, opt) {\n  if (!isSourceInstance(source)) {\n    source = createSourceFromSeriesDataOption(source);\n  }\n  opt = opt || {};\n  var sysDims = opt.coordDimensions || [];\n  var dimsDef = opt.dimensionsDefine || source.dimensionsDefine || [];\n  var coordDimNameMap = createHashMap();\n  var resultList = [];\n  var dimCount = getDimCount(source, sysDims, dimsDef, opt.dimensionsCount);\n  // Try to ignore unused dimensions if sharing a high dimension datastore\n  // 30 is an experience value.\n  var omitUnusedDimensions = opt.canOmitUnusedDimensions && shouldOmitUnusedDimensions(dimCount);\n  var isUsingSourceDimensionsDef = dimsDef === source.dimensionsDefine;\n  var dataDimNameMap = isUsingSourceDimensionsDef ? ensureSourceDimNameMap(source) : createDimNameMap(dimsDef);\n  var encodeDef = opt.encodeDefine;\n  if (!encodeDef && opt.encodeDefaulter) {\n    encodeDef = opt.encodeDefaulter(source, dimCount);\n  }\n  var encodeDefMap = createHashMap(encodeDef);\n  var indicesMap = new CtorInt32Array(dimCount);\n  for (var i = 0; i < indicesMap.length; i++) {\n    indicesMap[i] = -1;\n  }\n  function getResultItem(dimIdx) {\n    var idx = indicesMap[dimIdx];\n    if (idx < 0) {\n      var dimDefItemRaw = dimsDef[dimIdx];\n      var dimDefItem = isObject(dimDefItemRaw) ? dimDefItemRaw : {\n        name: dimDefItemRaw\n      };\n      var resultItem = new SeriesDimensionDefine();\n      var userDimName = dimDefItem.name;\n      if (userDimName != null && dataDimNameMap.get(userDimName) != null) {\n        // Only if `series.dimensions` is defined in option\n        // displayName, will be set, and dimension will be displayed vertically in\n        // tooltip by default.\n        resultItem.name = resultItem.displayName = userDimName;\n      }\n      dimDefItem.type != null && (resultItem.type = dimDefItem.type);\n      dimDefItem.displayName != null && (resultItem.displayName = dimDefItem.displayName);\n      var newIdx = resultList.length;\n      indicesMap[dimIdx] = newIdx;\n      resultItem.storeDimIndex = dimIdx;\n      resultList.push(resultItem);\n      return resultItem;\n    }\n    return resultList[idx];\n  }\n  if (!omitUnusedDimensions) {\n    for (var i = 0; i < dimCount; i++) {\n      getResultItem(i);\n    }\n  }\n  // Set `coordDim` and `coordDimIndex` by `encodeDefMap` and normalize `encodeDefMap`.\n  encodeDefMap.each(function (dataDimsRaw, coordDim) {\n    var dataDims = normalizeToArray(dataDimsRaw).slice();\n    // Note: It is allowed that `dataDims.length` is `0`, e.g., options is\n    // `{encode: {x: -1, y: 1}}`. Should not filter anything in\n    // this case.\n    if (dataDims.length === 1 && !isString(dataDims[0]) && dataDims[0] < 0) {\n      encodeDefMap.set(coordDim, false);\n      return;\n    }\n    var validDataDims = encodeDefMap.set(coordDim, []);\n    each(dataDims, function (resultDimIdxOrName, idx) {\n      // The input resultDimIdx can be dim name or index.\n      var resultDimIdx = isString(resultDimIdxOrName) ? dataDimNameMap.get(resultDimIdxOrName) : resultDimIdxOrName;\n      if (resultDimIdx != null && resultDimIdx < dimCount) {\n        validDataDims[idx] = resultDimIdx;\n        applyDim(getResultItem(resultDimIdx), coordDim, idx);\n      }\n    });\n  });\n  // Apply templates and default order from `sysDims`.\n  var availDimIdx = 0;\n  each(sysDims, function (sysDimItemRaw) {\n    var coordDim;\n    var sysDimItemDimsDef;\n    var sysDimItemOtherDims;\n    var sysDimItem;\n    if (isString(sysDimItemRaw)) {\n      coordDim = sysDimItemRaw;\n      sysDimItem = {};\n    } else {\n      sysDimItem = sysDimItemRaw;\n      coordDim = sysDimItem.name;\n      var ordinalMeta = sysDimItem.ordinalMeta;\n      sysDimItem.ordinalMeta = null;\n      sysDimItem = extend({}, sysDimItem);\n      sysDimItem.ordinalMeta = ordinalMeta;\n      // `coordDimIndex` should not be set directly.\n      sysDimItemDimsDef = sysDimItem.dimsDef;\n      sysDimItemOtherDims = sysDimItem.otherDims;\n      sysDimItem.name = sysDimItem.coordDim = sysDimItem.coordDimIndex = sysDimItem.dimsDef = sysDimItem.otherDims = null;\n    }\n    var dataDims = encodeDefMap.get(coordDim);\n    // negative resultDimIdx means no need to mapping.\n    if (dataDims === false) {\n      return;\n    }\n    dataDims = normalizeToArray(dataDims);\n    // dimensions provides default dim sequences.\n    if (!dataDims.length) {\n      for (var i = 0; i < (sysDimItemDimsDef && sysDimItemDimsDef.length || 1); i++) {\n        while (availDimIdx < dimCount && getResultItem(availDimIdx).coordDim != null) {\n          availDimIdx++;\n        }\n        availDimIdx < dimCount && dataDims.push(availDimIdx++);\n      }\n    }\n    // Apply templates.\n    each(dataDims, function (resultDimIdx, coordDimIndex) {\n      var resultItem = getResultItem(resultDimIdx);\n      // Coordinate system has a higher priority on dim type than source.\n      if (isUsingSourceDimensionsDef && sysDimItem.type != null) {\n        resultItem.type = sysDimItem.type;\n      }\n      applyDim(defaults(resultItem, sysDimItem), coordDim, coordDimIndex);\n      if (resultItem.name == null && sysDimItemDimsDef) {\n        var sysDimItemDimsDefItem = sysDimItemDimsDef[coordDimIndex];\n        !isObject(sysDimItemDimsDefItem) && (sysDimItemDimsDefItem = {\n          name: sysDimItemDimsDefItem\n        });\n        resultItem.name = resultItem.displayName = sysDimItemDimsDefItem.name;\n        resultItem.defaultTooltip = sysDimItemDimsDefItem.defaultTooltip;\n      }\n      // FIXME refactor, currently only used in case: {otherDims: {tooltip: false}}\n      sysDimItemOtherDims && defaults(resultItem.otherDims, sysDimItemOtherDims);\n    });\n  });\n  function applyDim(resultItem, coordDim, coordDimIndex) {\n    if (VISUAL_DIMENSIONS.get(coordDim) != null) {\n      resultItem.otherDims[coordDim] = coordDimIndex;\n    } else {\n      resultItem.coordDim = coordDim;\n      resultItem.coordDimIndex = coordDimIndex;\n      coordDimNameMap.set(coordDim, true);\n    }\n  }\n  // Make sure the first extra dim is 'value'.\n  var generateCoord = opt.generateCoord;\n  var generateCoordCount = opt.generateCoordCount;\n  var fromZero = generateCoordCount != null;\n  generateCoordCount = generateCoord ? generateCoordCount || 1 : 0;\n  var extra = generateCoord || 'value';\n  function ifNoNameFillWithCoordName(resultItem) {\n    if (resultItem.name == null) {\n      // Duplication will be removed in the next step.\n      resultItem.name = resultItem.coordDim;\n    }\n  }\n  // Set dim `name` and other `coordDim` and other props.\n  if (!omitUnusedDimensions) {\n    for (var resultDimIdx = 0; resultDimIdx < dimCount; resultDimIdx++) {\n      var resultItem = getResultItem(resultDimIdx);\n      var coordDim = resultItem.coordDim;\n      if (coordDim == null) {\n        // TODO no need to generate coordDim for isExtraCoord?\n        resultItem.coordDim = genCoordDimName(extra, coordDimNameMap, fromZero);\n        resultItem.coordDimIndex = 0;\n        // Series specified generateCoord is using out.\n        if (!generateCoord || generateCoordCount <= 0) {\n          resultItem.isExtraCoord = true;\n        }\n        generateCoordCount--;\n      }\n      ifNoNameFillWithCoordName(resultItem);\n      if (resultItem.type == null && (guessOrdinal(source, resultDimIdx) === BE_ORDINAL.Must\n      // Consider the case:\n      // {\n      //    dataset: {source: [\n      //        ['2001', 123],\n      //        ['2002', 456],\n      //        ...\n      //        ['The others', 987],\n      //    ]},\n      //    series: {type: 'pie'}\n      // }\n      // The first column should better be treated as a \"ordinal\" although it\n      // might not be detected as an \"ordinal\" by `guessOrdinal`.\n      || resultItem.isExtraCoord && (resultItem.otherDims.itemName != null || resultItem.otherDims.seriesName != null))) {\n        resultItem.type = 'ordinal';\n      }\n    }\n  } else {\n    each(resultList, function (resultItem) {\n      // PENDING: guessOrdinal or let user specify type: 'ordinal' manually?\n      ifNoNameFillWithCoordName(resultItem);\n    });\n    // Sort dimensions: there are some rule that use the last dim as label,\n    // and for some latter travel process easier.\n    resultList.sort(function (item0, item1) {\n      return item0.storeDimIndex - item1.storeDimIndex;\n    });\n  }\n  removeDuplication(resultList);\n  return new SeriesDataSchema({\n    source: source,\n    dimensions: resultList,\n    fullDimensionCount: dimCount,\n    dimensionOmitted: omitUnusedDimensions\n  });\n}\nfunction removeDuplication(result) {\n  var duplicationMap = createHashMap();\n  for (var i = 0; i < result.length; i++) {\n    var dim = result[i];\n    var dimOriginalName = dim.name;\n    var count = duplicationMap.get(dimOriginalName) || 0;\n    if (count > 0) {\n      // Starts from 0.\n      dim.name = dimOriginalName + (count - 1);\n    }\n    count++;\n    duplicationMap.set(dimOriginalName, count);\n  }\n}\n// ??? TODO\n// Originally detect dimCount by data[0]. Should we\n// optimize it to only by sysDims and dimensions and encode.\n// So only necessary dims will be initialized.\n// But\n// (1) custom series should be considered. where other dims\n// may be visited.\n// (2) sometimes user need to calculate bubble size or use visualMap\n// on other dimensions besides coordSys needed.\n// So, dims that is not used by system, should be shared in data store?\nfunction getDimCount(source, sysDims, dimsDef, optDimCount) {\n  // Note that the result dimCount should not small than columns count\n  // of data, otherwise `dataDimNameMap` checking will be incorrect.\n  var dimCount = Math.max(source.dimensionsDetectedCount || 1, sysDims.length, dimsDef.length, optDimCount || 0);\n  each(sysDims, function (sysDimItem) {\n    var sysDimItemDimsDef;\n    if (isObject(sysDimItem) && (sysDimItemDimsDef = sysDimItem.dimsDef)) {\n      dimCount = Math.max(dimCount, sysDimItemDimsDef.length);\n    }\n  });\n  return dimCount;\n}\nfunction genCoordDimName(name, map, fromZero) {\n  if (fromZero || map.hasKey(name)) {\n    var i = 0;\n    while (map.hasKey(name + i)) {\n      i++;\n    }\n    name += i;\n  }\n  map.set(name, true);\n  return name;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,qBAAqB;AACvD,OAAOC,qBAAqB,MAAM,6BAA6B;AAC/D,SAASC,aAAa,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,0BAA0B;AACpG,SAASC,gCAAgC,EAAEC,gBAAgB,QAAQ,cAAc;AACjF,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,UAAU,EAAEC,YAAY,QAAQ,mBAAmB;AAC5D,SAASC,gBAAgB,EAAEC,sBAAsB,EAAEC,gBAAgB,EAAEC,0BAA0B,QAAQ,uBAAuB;AAC9H;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC5C,OAAOC,uBAAuB,CAACF,MAAM,EAAEC,GAAG,CAAC,CAACE,UAAU;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASD,uBAAuBA;AAC/C;AACAF,MAAM,EAAEC,GAAG,EAAE;EACX,IAAI,CAACX,gBAAgB,CAACU,MAAM,CAAC,EAAE;IAC7BA,MAAM,GAAGX,gCAAgC,CAACW,MAAM,CAAC;EACnD;EACAC,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAIG,OAAO,GAAGH,GAAG,CAACI,eAAe,IAAI,EAAE;EACvC,IAAIC,OAAO,GAAGL,GAAG,CAACM,gBAAgB,IAAIP,MAAM,CAACO,gBAAgB,IAAI,EAAE;EACnE,IAAIC,eAAe,GAAGzB,aAAa,CAAC,CAAC;EACrC,IAAI0B,UAAU,GAAG,EAAE;EACnB,IAAIC,QAAQ,GAAGC,WAAW,CAACX,MAAM,EAAEI,OAAO,EAAEE,OAAO,EAAEL,GAAG,CAACW,eAAe,CAAC;EACzE;EACA;EACA,IAAIC,oBAAoB,GAAGZ,GAAG,CAACa,uBAAuB,IAAIhB,0BAA0B,CAACY,QAAQ,CAAC;EAC9F,IAAIK,0BAA0B,GAAGT,OAAO,KAAKN,MAAM,CAACO,gBAAgB;EACpE,IAAIS,cAAc,GAAGD,0BAA0B,GAAGnB,sBAAsB,CAACI,MAAM,CAAC,GAAGL,gBAAgB,CAACW,OAAO,CAAC;EAC5G,IAAIW,SAAS,GAAGhB,GAAG,CAACiB,YAAY;EAChC,IAAI,CAACD,SAAS,IAAIhB,GAAG,CAACkB,eAAe,EAAE;IACrCF,SAAS,GAAGhB,GAAG,CAACkB,eAAe,CAACnB,MAAM,EAAEU,QAAQ,CAAC;EACnD;EACA,IAAIU,YAAY,GAAGrC,aAAa,CAACkC,SAAS,CAAC;EAC3C,IAAII,UAAU,GAAG,IAAI9B,cAAc,CAACmB,QAAQ,CAAC;EAC7C,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1CD,UAAU,CAACC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpB;EACA,SAASE,aAAaA,CAACC,MAAM,EAAE;IAC7B,IAAIC,GAAG,GAAGL,UAAU,CAACI,MAAM,CAAC;IAC5B,IAAIC,GAAG,GAAG,CAAC,EAAE;MACX,IAAIC,aAAa,GAAGrB,OAAO,CAACmB,MAAM,CAAC;MACnC,IAAIG,UAAU,GAAGzC,QAAQ,CAACwC,aAAa,CAAC,GAAGA,aAAa,GAAG;QACzDE,IAAI,EAAEF;MACR,CAAC;MACD,IAAIG,UAAU,GAAG,IAAIhD,qBAAqB,CAAC,CAAC;MAC5C,IAAIiD,WAAW,GAAGH,UAAU,CAACC,IAAI;MACjC,IAAIE,WAAW,IAAI,IAAI,IAAIf,cAAc,CAACgB,GAAG,CAACD,WAAW,CAAC,IAAI,IAAI,EAAE;QAClE;QACA;QACA;QACAD,UAAU,CAACD,IAAI,GAAGC,UAAU,CAACG,WAAW,GAAGF,WAAW;MACxD;MACAH,UAAU,CAACM,IAAI,IAAI,IAAI,KAAKJ,UAAU,CAACI,IAAI,GAAGN,UAAU,CAACM,IAAI,CAAC;MAC9DN,UAAU,CAACK,WAAW,IAAI,IAAI,KAAKH,UAAU,CAACG,WAAW,GAAGL,UAAU,CAACK,WAAW,CAAC;MACnF,IAAIE,MAAM,GAAG1B,UAAU,CAACc,MAAM;MAC9BF,UAAU,CAACI,MAAM,CAAC,GAAGU,MAAM;MAC3BL,UAAU,CAACM,aAAa,GAAGX,MAAM;MACjChB,UAAU,CAAC4B,IAAI,CAACP,UAAU,CAAC;MAC3B,OAAOA,UAAU;IACnB;IACA,OAAOrB,UAAU,CAACiB,GAAG,CAAC;EACxB;EACA,IAAI,CAACb,oBAAoB,EAAE;IACzB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,QAAQ,EAAEY,CAAC,EAAE,EAAE;MACjCE,aAAa,CAACF,CAAC,CAAC;IAClB;EACF;EACA;EACAF,YAAY,CAACnC,IAAI,CAAC,UAAUqD,WAAW,EAAEC,QAAQ,EAAE;IACjD,IAAIC,QAAQ,GAAGhD,gBAAgB,CAAC8C,WAAW,CAAC,CAACG,KAAK,CAAC,CAAC;IACpD;IACA;IACA;IACA,IAAID,QAAQ,CAACjB,MAAM,KAAK,CAAC,IAAI,CAACnC,QAAQ,CAACoD,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACtEpB,YAAY,CAACsB,GAAG,CAACH,QAAQ,EAAE,KAAK,CAAC;MACjC;IACF;IACA,IAAII,aAAa,GAAGvB,YAAY,CAACsB,GAAG,CAACH,QAAQ,EAAE,EAAE,CAAC;IAClDtD,IAAI,CAACuD,QAAQ,EAAE,UAAUI,kBAAkB,EAAElB,GAAG,EAAE;MAChD;MACA,IAAImB,YAAY,GAAGzD,QAAQ,CAACwD,kBAAkB,CAAC,GAAG5B,cAAc,CAACgB,GAAG,CAACY,kBAAkB,CAAC,GAAGA,kBAAkB;MAC7G,IAAIC,YAAY,IAAI,IAAI,IAAIA,YAAY,GAAGnC,QAAQ,EAAE;QACnDiC,aAAa,CAACjB,GAAG,CAAC,GAAGmB,YAAY;QACjCC,QAAQ,CAACtB,aAAa,CAACqB,YAAY,CAAC,EAAEN,QAAQ,EAAEb,GAAG,CAAC;MACtD;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;EACA,IAAIqB,WAAW,GAAG,CAAC;EACnB9D,IAAI,CAACmB,OAAO,EAAE,UAAU4C,aAAa,EAAE;IACrC,IAAIT,QAAQ;IACZ,IAAIU,iBAAiB;IACrB,IAAIC,mBAAmB;IACvB,IAAIC,UAAU;IACd,IAAI/D,QAAQ,CAAC4D,aAAa,CAAC,EAAE;MAC3BT,QAAQ,GAAGS,aAAa;MACxBG,UAAU,GAAG,CAAC,CAAC;IACjB,CAAC,MAAM;MACLA,UAAU,GAAGH,aAAa;MAC1BT,QAAQ,GAAGY,UAAU,CAACtB,IAAI;MAC1B,IAAIuB,WAAW,GAAGD,UAAU,CAACC,WAAW;MACxCD,UAAU,CAACC,WAAW,GAAG,IAAI;MAC7BD,UAAU,GAAGjE,MAAM,CAAC,CAAC,CAAC,EAAEiE,UAAU,CAAC;MACnCA,UAAU,CAACC,WAAW,GAAGA,WAAW;MACpC;MACAH,iBAAiB,GAAGE,UAAU,CAAC7C,OAAO;MACtC4C,mBAAmB,GAAGC,UAAU,CAACE,SAAS;MAC1CF,UAAU,CAACtB,IAAI,GAAGsB,UAAU,CAACZ,QAAQ,GAAGY,UAAU,CAACG,aAAa,GAAGH,UAAU,CAAC7C,OAAO,GAAG6C,UAAU,CAACE,SAAS,GAAG,IAAI;IACrH;IACA,IAAIb,QAAQ,GAAGpB,YAAY,CAACY,GAAG,CAACO,QAAQ,CAAC;IACzC;IACA,IAAIC,QAAQ,KAAK,KAAK,EAAE;MACtB;IACF;IACAA,QAAQ,GAAGhD,gBAAgB,CAACgD,QAAQ,CAAC;IACrC;IACA,IAAI,CAACA,QAAQ,CAACjB,MAAM,EAAE;MACpB,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI2B,iBAAiB,IAAIA,iBAAiB,CAAC1B,MAAM,IAAI,CAAC,CAAC,EAAED,CAAC,EAAE,EAAE;QAC7E,OAAOyB,WAAW,GAAGrC,QAAQ,IAAIc,aAAa,CAACuB,WAAW,CAAC,CAACR,QAAQ,IAAI,IAAI,EAAE;UAC5EQ,WAAW,EAAE;QACf;QACAA,WAAW,GAAGrC,QAAQ,IAAI8B,QAAQ,CAACH,IAAI,CAACU,WAAW,EAAE,CAAC;MACxD;IACF;IACA;IACA9D,IAAI,CAACuD,QAAQ,EAAE,UAAUK,YAAY,EAAES,aAAa,EAAE;MACpD,IAAIxB,UAAU,GAAGN,aAAa,CAACqB,YAAY,CAAC;MAC5C;MACA,IAAI9B,0BAA0B,IAAIoC,UAAU,CAACjB,IAAI,IAAI,IAAI,EAAE;QACzDJ,UAAU,CAACI,IAAI,GAAGiB,UAAU,CAACjB,IAAI;MACnC;MACAY,QAAQ,CAAC9D,QAAQ,CAAC8C,UAAU,EAAEqB,UAAU,CAAC,EAAEZ,QAAQ,EAAEe,aAAa,CAAC;MACnE,IAAIxB,UAAU,CAACD,IAAI,IAAI,IAAI,IAAIoB,iBAAiB,EAAE;QAChD,IAAIM,qBAAqB,GAAGN,iBAAiB,CAACK,aAAa,CAAC;QAC5D,CAACnE,QAAQ,CAACoE,qBAAqB,CAAC,KAAKA,qBAAqB,GAAG;UAC3D1B,IAAI,EAAE0B;QACR,CAAC,CAAC;QACFzB,UAAU,CAACD,IAAI,GAAGC,UAAU,CAACG,WAAW,GAAGsB,qBAAqB,CAAC1B,IAAI;QACrEC,UAAU,CAAC0B,cAAc,GAAGD,qBAAqB,CAACC,cAAc;MAClE;MACA;MACAN,mBAAmB,IAAIlE,QAAQ,CAAC8C,UAAU,CAACuB,SAAS,EAAEH,mBAAmB,CAAC;IAC5E,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,SAASJ,QAAQA,CAAChB,UAAU,EAAES,QAAQ,EAAEe,aAAa,EAAE;IACrD,IAAIzE,iBAAiB,CAACmD,GAAG,CAACO,QAAQ,CAAC,IAAI,IAAI,EAAE;MAC3CT,UAAU,CAACuB,SAAS,CAACd,QAAQ,CAAC,GAAGe,aAAa;IAChD,CAAC,MAAM;MACLxB,UAAU,CAACS,QAAQ,GAAGA,QAAQ;MAC9BT,UAAU,CAACwB,aAAa,GAAGA,aAAa;MACxC9C,eAAe,CAACkC,GAAG,CAACH,QAAQ,EAAE,IAAI,CAAC;IACrC;EACF;EACA;EACA,IAAIkB,aAAa,GAAGxD,GAAG,CAACwD,aAAa;EACrC,IAAIC,kBAAkB,GAAGzD,GAAG,CAACyD,kBAAkB;EAC/C,IAAIC,QAAQ,GAAGD,kBAAkB,IAAI,IAAI;EACzCA,kBAAkB,GAAGD,aAAa,GAAGC,kBAAkB,IAAI,CAAC,GAAG,CAAC;EAChE,IAAIE,KAAK,GAAGH,aAAa,IAAI,OAAO;EACpC,SAASI,yBAAyBA,CAAC/B,UAAU,EAAE;IAC7C,IAAIA,UAAU,CAACD,IAAI,IAAI,IAAI,EAAE;MAC3B;MACAC,UAAU,CAACD,IAAI,GAAGC,UAAU,CAACS,QAAQ;IACvC;EACF;EACA;EACA,IAAI,CAAC1B,oBAAoB,EAAE;IACzB,KAAK,IAAIgC,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAGnC,QAAQ,EAAEmC,YAAY,EAAE,EAAE;MAClE,IAAIf,UAAU,GAAGN,aAAa,CAACqB,YAAY,CAAC;MAC5C,IAAIN,QAAQ,GAAGT,UAAU,CAACS,QAAQ;MAClC,IAAIA,QAAQ,IAAI,IAAI,EAAE;QACpB;QACAT,UAAU,CAACS,QAAQ,GAAGuB,eAAe,CAACF,KAAK,EAAEpD,eAAe,EAAEmD,QAAQ,CAAC;QACvE7B,UAAU,CAACwB,aAAa,GAAG,CAAC;QAC5B;QACA,IAAI,CAACG,aAAa,IAAIC,kBAAkB,IAAI,CAAC,EAAE;UAC7C5B,UAAU,CAACiC,YAAY,GAAG,IAAI;QAChC;QACAL,kBAAkB,EAAE;MACtB;MACAG,yBAAyB,CAAC/B,UAAU,CAAC;MACrC,IAAIA,UAAU,CAACI,IAAI,IAAI,IAAI,KAAKxC,YAAY,CAACM,MAAM,EAAE6C,YAAY,CAAC,KAAKpD,UAAU,CAACuE;MAClF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAAA,GACGlC,UAAU,CAACiC,YAAY,KAAKjC,UAAU,CAACuB,SAAS,CAACY,QAAQ,IAAI,IAAI,IAAInC,UAAU,CAACuB,SAAS,CAACa,UAAU,IAAI,IAAI,CAAC,CAAC,EAAE;QACjHpC,UAAU,CAACI,IAAI,GAAG,SAAS;MAC7B;IACF;EACF,CAAC,MAAM;IACLjD,IAAI,CAACwB,UAAU,EAAE,UAAUqB,UAAU,EAAE;MACrC;MACA+B,yBAAyB,CAAC/B,UAAU,CAAC;IACvC,CAAC,CAAC;IACF;IACA;IACArB,UAAU,CAAC0D,IAAI,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;MACtC,OAAOD,KAAK,CAAChC,aAAa,GAAGiC,KAAK,CAACjC,aAAa;IAClD,CAAC,CAAC;EACJ;EACAkC,iBAAiB,CAAC7D,UAAU,CAAC;EAC7B,OAAO,IAAIZ,gBAAgB,CAAC;IAC1BG,MAAM,EAAEA,MAAM;IACdG,UAAU,EAAEM,UAAU;IACtB8D,kBAAkB,EAAE7D,QAAQ;IAC5B8D,gBAAgB,EAAE3D;EACpB,CAAC,CAAC;AACJ;AACA,SAASyD,iBAAiBA,CAACG,MAAM,EAAE;EACjC,IAAIC,cAAc,GAAG3F,aAAa,CAAC,CAAC;EACpC,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmD,MAAM,CAAClD,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAIqD,GAAG,GAAGF,MAAM,CAACnD,CAAC,CAAC;IACnB,IAAIsD,eAAe,GAAGD,GAAG,CAAC9C,IAAI;IAC9B,IAAIgD,KAAK,GAAGH,cAAc,CAAC1C,GAAG,CAAC4C,eAAe,CAAC,IAAI,CAAC;IACpD,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb;MACAF,GAAG,CAAC9C,IAAI,GAAG+C,eAAe,IAAIC,KAAK,GAAG,CAAC,CAAC;IAC1C;IACAA,KAAK,EAAE;IACPH,cAAc,CAAChC,GAAG,CAACkC,eAAe,EAAEC,KAAK,CAAC;EAC5C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASlE,WAAWA,CAACX,MAAM,EAAEI,OAAO,EAAEE,OAAO,EAAEwE,WAAW,EAAE;EAC1D;EACA;EACA,IAAIpE,QAAQ,GAAGqE,IAAI,CAACC,GAAG,CAAChF,MAAM,CAACiF,uBAAuB,IAAI,CAAC,EAAE7E,OAAO,CAACmB,MAAM,EAAEjB,OAAO,CAACiB,MAAM,EAAEuD,WAAW,IAAI,CAAC,CAAC;EAC9G7F,IAAI,CAACmB,OAAO,EAAE,UAAU+C,UAAU,EAAE;IAClC,IAAIF,iBAAiB;IACrB,IAAI9D,QAAQ,CAACgE,UAAU,CAAC,KAAKF,iBAAiB,GAAGE,UAAU,CAAC7C,OAAO,CAAC,EAAE;MACpEI,QAAQ,GAAGqE,IAAI,CAACC,GAAG,CAACtE,QAAQ,EAAEuC,iBAAiB,CAAC1B,MAAM,CAAC;IACzD;EACF,CAAC,CAAC;EACF,OAAOb,QAAQ;AACjB;AACA,SAASoD,eAAeA,CAACjC,IAAI,EAAEqD,GAAG,EAAEvB,QAAQ,EAAE;EAC5C,IAAIA,QAAQ,IAAIuB,GAAG,CAACC,MAAM,CAACtD,IAAI,CAAC,EAAE;IAChC,IAAIP,CAAC,GAAG,CAAC;IACT,OAAO4D,GAAG,CAACC,MAAM,CAACtD,IAAI,GAAGP,CAAC,CAAC,EAAE;MAC3BA,CAAC,EAAE;IACL;IACAO,IAAI,IAAIP,CAAC;EACX;EACA4D,GAAG,CAACxC,GAAG,CAACb,IAAI,EAAE,IAAI,CAAC;EACnB,OAAOA,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}