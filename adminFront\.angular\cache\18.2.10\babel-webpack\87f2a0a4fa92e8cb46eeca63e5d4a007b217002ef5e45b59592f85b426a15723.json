{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbInputModule, NbSelectModule, NbOptionModule } from '@nebular/theme';\nimport { GetRequirement } from 'src/services/api/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/requirement.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction RequirementTemplateSelectorComponent_nb_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r1.label, \" \");\n  }\n}\nfunction RequirementTemplateSelectorComponent_nb_option_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r2.label, \" \");\n  }\n}\nfunction RequirementTemplateSelectorComponent_span_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\\u5DF2\\u9078\\u64C7 \", ctx_r2.getSelectedItems().length, \" \\u9805) \");\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"nb-checkbox\", 40);\n    i0.ɵɵlistener(\"ngModelChange\", function RequirementTemplateSelectorComponent_div_64_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSelectAll($event));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D\\u9801\\u9762 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.isAllSelected())(\"indeterminate\", ctx_r2.isIndeterminate());\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"nb-icon\", 42);\n    i0.ɵɵtext(2, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_66_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"nb-checkbox\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_div_66_div_1_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const requirement_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      i0.ɵɵtwoWayBindingSet(requirement_r6.selected, $event) || (requirement_r6.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function RequirementTemplateSelectorComponent_div_66_div_1_Template_nb_checkbox_ngModelChange_2_listener() {\n      const requirement_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleItemSelection(requirement_r6));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 48)(4, \"div\", 49)(5, \"div\", 50);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 51);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 52)(10, \"div\", 53)(11, \"span\", 54);\n    i0.ɵɵtext(12, \"\\u985E\\u578B:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 55);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 53)(16, \"span\", 54);\n    i0.ɵɵtext(17, \"\\u55AE\\u50F9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 55);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 53)(22, \"span\", 54);\n    i0.ɵɵtext(23, \"\\u72C0\\u614B:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 55);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 53)(27, \"span\", 54);\n    i0.ɵɵtext(28, \"\\u9810\\u7D04\\u9700\\u6C42:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 55);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 53)(32, \"span\", 54);\n    i0.ɵɵtext(33, \"\\u7C21\\u6613\\u5BA2\\u8B8A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\", 55);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const requirement_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", requirement_r6.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", requirement_r6.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(requirement_r6.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(requirement_r6.CLocation || \"-\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseTypeText(requirement_r6.CHouseType));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind2(20, 11, requirement_r6.CUnitPrice, \"1.0-2\"), \" \", requirement_r6.CUnit || \"\", \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.getStatusText(requirement_r6.CStatus));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getIsShowText(requirement_r6.CIsShow));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getIsSimpleText(requirement_r6.CIsSimple));\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, RequirementTemplateSelectorComponent_div_66_div_1_Template, 36, 14, \"div\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.requirements);\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"nb-icon\", 57);\n    i0.ɵɵelementStart(2, \"div\", 58);\n    i0.ɵɵtext(3, \"\\u6C92\\u6709\\u627E\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u9700\\u6C42\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 61)(4, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_div_68_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onPageChange(ctx_r2.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"nb-icon\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 64);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_div_68_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onPageChange(ctx_r2.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"nb-icon\", 65);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u5171 \", ctx_r2.totalRecords, \" \\u7B46\\u8CC7\\u6599\\uFF0C\\u7B2C \", ctx_r2.currentPage, \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.currentPage <= 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.currentPage, \" / \", ctx_r2.getTotalPages(), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.currentPage >= ctx_r2.getTotalPages());\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67);\n    i0.ɵɵtext(2, \"\\u9078\\u64C7\\u6458\\u8981\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 68)(4, \"div\", 69)(5, \"span\", 70);\n    i0.ɵɵtext(6, \"\\u5DF2\\u9078\\u64C7\\u9805\\u76EE:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 71);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 69)(10, \"span\", 70);\n    i0.ɵɵtext(11, \"\\u7E3D\\u91D1\\u984D:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 72);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getSelectedItems().length, \" \\u9805\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 2, ctx_r2.getTotalPrice(), \"1.0-2\"));\n  }\n}\nexport class RequirementTemplateSelectorComponent {\n  constructor(requirementService, dialogRef) {\n    this.requirementService = requirementService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = 0;\n    this.multiple = true;\n    this.preSelectedItems = [];\n    this.selectionConfirmed = new EventEmitter();\n    this.selectionCancelled = new EventEmitter();\n    // 資料相關屬性\n    this.requirements = [];\n    this.isLoading = false;\n    // 搜尋相關屬性\n    this.searchFilters = {\n      CBuildCaseID: 0,\n      CLocation: null,\n      CRequirement: null,\n      CHouseType: null,\n      CStatus: 1,\n      // 預設只顯示啟用的\n      CIsShow: null,\n      CIsSimple: null,\n      PageIndex: 1,\n      PageSize: 20\n    };\n    // 分頁相關屬性\n    this.totalRecords = 0;\n    this.currentPage = 1;\n    this.pageSize = 20;\n    // 房屋類型選項\n    this.houseTypeOptions = [{\n      value: 1,\n      label: '套房'\n    }, {\n      value: 2,\n      label: '1房'\n    }, {\n      value: 3,\n      label: '2房'\n    }, {\n      value: 4,\n      label: '3房'\n    }, {\n      value: 5,\n      label: '4房'\n    }, {\n      value: 6,\n      label: '5房以上'\n    }];\n    // 狀態選項\n    this.statusOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: 1,\n      label: '啟用'\n    }, {\n      value: 0,\n      label: '停用'\n    }];\n  }\n  ngOnInit() {\n    this.searchFilters.CBuildCaseID = this.buildCaseId;\n    this.loadRequirements();\n    this.initializePreSelectedItems();\n  }\n  // 初始化預選項目\n  initializePreSelectedItems() {\n    if (this.preSelectedItems && this.preSelectedItems.length > 0) {\n      // 將預選項目標記為已選擇\n      this.preSelectedItems.forEach(preSelected => {\n        const existingItem = this.requirements.find(req => req.CRequirementID === preSelected.CRequirementID);\n        if (existingItem) {\n          existingItem.selected = true;\n        }\n      });\n    }\n  }\n  // 載入需求資料\n  loadRequirements() {\n    this.isLoading = true;\n    const requestParams = {\n      ...this.searchFilters,\n      PageIndex: this.currentPage,\n      PageSize: this.pageSize\n    };\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: requestParams\n    }).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.StatusCode === 0 && response.Entries) {\n          this.requirements = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n          this.totalRecords = response.TotalItems || 0;\n          this.initializePreSelectedItems();\n        } else {\n          this.requirements = [];\n          this.totalRecords = 0;\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.requirements = [];\n        this.totalRecords = 0;\n        console.error('載入需求資料失敗:', error);\n      }\n    });\n  }\n  // 搜尋功能\n  onSearch() {\n    this.currentPage = 1;\n    this.loadRequirements();\n  }\n  // 重置搜尋\n  onReset() {\n    this.searchFilters = {\n      CBuildCaseID: this.buildCaseId,\n      CLocation: null,\n      CRequirement: null,\n      CHouseType: null,\n      CStatus: 1,\n      CIsShow: null,\n      CIsSimple: null,\n      PageIndex: 1,\n      PageSize: 20\n    };\n    this.currentPage = 1;\n    this.loadRequirements();\n  }\n  // 分頁變更\n  onPageChange(page) {\n    this.currentPage = page;\n    this.loadRequirements();\n  }\n  // 切換項目選擇狀態\n  toggleItemSelection(item) {\n    if (!this.multiple) {\n      // 單選模式：取消其他項目的選擇\n      this.requirements.forEach(req => {\n        if (req.CRequirementID !== item.CRequirementID) {\n          req.selected = false;\n        }\n      });\n    }\n    item.selected = !item.selected;\n  }\n  // 全選/取消全選\n  toggleSelectAll(selectAll) {\n    this.requirements.forEach(item => {\n      item.selected = selectAll;\n    });\n  }\n  // 獲取已選擇的項目\n  getSelectedItems() {\n    return this.requirements.filter(item => item.selected);\n  }\n  // 計算總價\n  getTotalPrice() {\n    return this.getSelectedItems().reduce((total, item) => {\n      return total + (item.CUnitPrice || 0);\n    }, 0);\n  }\n  // 確認選擇\n  confirmSelection() {\n    const selectedItems = this.getSelectedItems();\n    if (selectedItems.length === 0) {\n      alert('請至少選擇一個項目');\n      return;\n    }\n    const config = {\n      selectedItems: selectedItems,\n      totalPrice: this.getTotalPrice(),\n      buildCaseId: this.buildCaseId\n    };\n    this.selectionConfirmed.emit(config);\n    this.close();\n  }\n  // 關閉對話框\n  close() {\n    this.selectionCancelled.emit();\n    this.dialogRef.close();\n  }\n  // 獲取房屋類型顯示文字\n  getHouseTypeText(houseTypes) {\n    if (!houseTypes || houseTypes.length === 0) {\n      return '-';\n    }\n    const typeNames = houseTypes.map(type => {\n      const option = this.houseTypeOptions.find(opt => opt.value === type);\n      return option ? option.label : type.toString();\n    });\n    return typeNames.join(', ');\n  }\n  // 獲取狀態顯示文字\n  getStatusText(status) {\n    return status === 1 ? '啟用' : '停用';\n  }\n  // 獲取是否顯示文字\n  getIsShowText(isShow) {\n    return isShow ? '是' : '否';\n  }\n  // 獲取簡易客變文字\n  getIsSimpleText(isSimple) {\n    return isSimple ? '是' : '否';\n  }\n  // 獲取總頁數\n  getTotalPages() {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n  // 檢查是否全選\n  isAllSelected() {\n    return this.requirements.length > 0 && this.requirements.every(item => item.selected);\n  }\n  // 檢查是否部分選擇\n  isIndeterminate() {\n    return this.requirements.some(item => item.selected) && !this.requirements.every(item => item.selected);\n  }\n  static {\n    this.ɵfac = function RequirementTemplateSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementTemplateSelectorComponent)(i0.ɵɵdirectiveInject(i1.RequirementService), i0.ɵɵdirectiveInject(i2.NbDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementTemplateSelectorComponent,\n      selectors: [[\"app-requirement-template-selector\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        multiple: \"multiple\",\n        preSelectedItems: \"preSelectedItems\"\n      },\n      outputs: {\n        selectionConfirmed: \"selectionConfirmed\",\n        selectionCancelled: \"selectionCancelled\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 76,\n      vars: 23,\n      consts: [[1, \"requirement-template-dialog\"], [1, \"requirement-template-header\"], [1, \"requirement-template-title\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"close-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"requirement-template-body\"], [1, \"search-section\"], [1, \"section-title\"], [\"icon\", \"search-outline\", 1, \"mr-2\"], [1, \"search-form\"], [1, \"row\"], [1, \"col-md-4\"], [1, \"search-label\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5340\\u57DF\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u72C0\\u614B\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"mt-3\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u985E\\u578B\", \"multiple\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u5168\\u90E8\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [1, \"search-actions\", \"mt-3\"], [\"nbButton\", \"\", \"status\", \"basic\", 1, \"mr-2\", 3, \"click\"], [\"icon\", \"refresh-outline\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\"], [\"icon\", \"search-outline\"], [1, \"selection-section\"], [\"icon\", \"list-outline\", 1, \"mr-2\"], [\"class\", \"selected-count\", 4, \"ngIf\"], [\"class\", \"select-all-control\", 4, \"ngIf\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"requirement-list\", 4, \"ngIf\"], [\"class\", \"no-data-state\", 4, \"ngIf\"], [\"class\", \"pagination-section\", 4, \"ngIf\"], [\"class\", \"selection-summary\", 4, \"ngIf\"], [1, \"requirement-template-footer\"], [1, \"footer-actions\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [1, \"selected-count\"], [1, \"select-all-control\"], [3, \"ngModelChange\", \"ngModel\", \"indeterminate\"], [1, \"loading-state\"], [\"icon\", \"loader-outline\", 1, \"spinning\"], [1, \"requirement-list\"], [\"class\", \"requirement-item\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"requirement-item\"], [1, \"requirement-checkbox\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"requirement-info\"], [1, \"requirement-main\"], [1, \"requirement-name\"], [1, \"requirement-location\"], [1, \"requirement-details\"], [1, \"detail-item\"], [1, \"detail-label\"], [1, \"detail-value\"], [1, \"no-data-state\"], [\"icon\", \"inbox-outline\", 1, \"no-data-icon\"], [1, \"no-data-text\"], [1, \"pagination-section\"], [1, \"pagination-info\"], [1, \"pagination-controls\"], [\"nbButton\", \"\", \"size\", \"small\", \"status\", \"basic\", 3, \"click\", \"disabled\"], [\"icon\", \"chevron-left-outline\"], [1, \"page-info\"], [\"icon\", \"chevron-right-outline\"], [1, \"selection-summary\"], [1, \"summary-title\"], [1, \"summary-content\"], [1, \"summary-item\"], [1, \"summary-label\"], [1, \"summary-value\"], [1, \"summary-value\", \"total-price\"]],\n      template: function RequirementTemplateSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3, \"\\u9700\\u6C42\\u9805\\u76EE\\u9078\\u64C7\\u5668\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_Template_button_click_4_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(5, \"nb-icon\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 5)(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵelement(9, \"nb-icon\", 8);\n          i0.ɵɵtext(10, \"\\u641C\\u5C0B\\u689D\\u4EF6 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"label\", 12);\n          i0.ɵɵtext(15, \"\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CLocation, $event) || (ctx.searchFilters.CLocation = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"label\", 12);\n          i0.ɵɵtext(19, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_input_ngModelChange_20_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CRequirement, $event) || (ctx.searchFilters.CRequirement = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"label\", 12);\n          i0.ɵɵtext(23, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_24_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CStatus, $event) || (ctx.searchFilters.CStatus = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(25, RequirementTemplateSelectorComponent_nb_option_25_Template, 2, 2, \"nb-option\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 17)(27, \"div\", 11)(28, \"label\", 12);\n          i0.ɵɵtext(29, \"\\u623F\\u5C4B\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"nb-select\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_30_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CHouseType, $event) || (ctx.searchFilters.CHouseType = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(31, RequirementTemplateSelectorComponent_nb_option_31_Template, 2, 2, \"nb-option\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 11)(33, \"label\", 12);\n          i0.ɵɵtext(34, \"\\u9810\\u7D04\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"nb-select\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_35_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CIsShow, $event) || (ctx.searchFilters.CIsShow = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(36, \"nb-option\", 20);\n          i0.ɵɵtext(37, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"nb-option\", 20);\n          i0.ɵɵtext(39, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"nb-option\", 20);\n          i0.ɵɵtext(41, \"\\u5426\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 11)(43, \"label\", 12);\n          i0.ɵɵtext(44, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"nb-select\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_45_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CIsSimple, $event) || (ctx.searchFilters.CIsSimple = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(46, \"nb-option\", 20);\n          i0.ɵɵtext(47, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"nb-option\", 20);\n          i0.ɵɵtext(49, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"nb-option\", 20);\n          i0.ɵɵtext(51, \"\\u5426\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(52, \"div\", 21)(53, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_Template_button_click_53_listener() {\n            return ctx.onReset();\n          });\n          i0.ɵɵelement(54, \"nb-icon\", 23);\n          i0.ɵɵtext(55, \"\\u91CD\\u7F6E \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_Template_button_click_56_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelement(57, \"nb-icon\", 25);\n          i0.ɵɵtext(58, \"\\u641C\\u5C0B \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(59, \"div\", 26)(60, \"div\", 7);\n          i0.ɵɵelement(61, \"nb-icon\", 27);\n          i0.ɵɵtext(62, \"\\u9078\\u64C7\\u9805\\u76EE \");\n          i0.ɵɵtemplate(63, RequirementTemplateSelectorComponent_span_63_Template, 2, 1, \"span\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(64, RequirementTemplateSelectorComponent_div_64_Template, 3, 2, \"div\", 29)(65, RequirementTemplateSelectorComponent_div_65_Template, 3, 0, \"div\", 30)(66, RequirementTemplateSelectorComponent_div_66_Template, 2, 1, \"div\", 31)(67, RequirementTemplateSelectorComponent_div_67_Template, 4, 0, \"div\", 32)(68, RequirementTemplateSelectorComponent_div_68_Template, 10, 6, \"div\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(69, RequirementTemplateSelectorComponent_div_69_Template, 15, 5, \"div\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"nb-card-footer\", 35)(71, \"div\", 36)(72, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_Template_button_click_72_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(73, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_Template_button_click_74_listener() {\n            return ctx.confirmSelection();\n          });\n          i0.ɵɵtext(75);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CLocation);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CRequirement);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CIsShow);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CIsSimple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.getSelectedItems().length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.multiple && ctx.requirements.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.requirements.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.requirements.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.totalRecords > ctx.pageSize);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getSelectedItems().length > 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.getSelectedItems().length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \\u78BA\\u8A8D\\u9078\\u64C7 (\", ctx.getSelectedItems().length, \") \");\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.DecimalPipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbIconModule, i2.NbIconComponent, NbCheckboxModule, i2.NbCheckboxComponent, NbInputModule, i2.NbInputDirective, NbSelectModule, i2.NbSelectComponent, i2.NbOptionComponent, NbOptionModule],\n      styles: [\".requirement-template-dialog[_ngcontent-%COMP%] {\\n  width: 90vw;\\n  max-width: 1200px;\\n  max-height: 90vh;\\n  margin: 0 auto;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e4e9f2;\\n  background-color: #f7f9fc;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-header[_ngcontent-%COMP%]   .requirement-template-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #2c3e50;\\n  display: flex;\\n  align-items: center;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-header[_ngcontent-%COMP%]   .requirement-template-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #3366ff;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  padding: 0.25rem;\\n  min-width: auto;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  max-height: 70vh;\\n  overflow-y: auto;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2c3e50;\\n  margin-bottom: 1rem;\\n  display: flex;\\n  align-items: center;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 2px solid #3366ff;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  color: #3366ff;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  font-size: 0.9rem;\\n  color: #27ae60;\\n  font-weight: 500;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1.5rem;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 500;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n  font-size: 0.9rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n  margin-left: -0.5rem;\\n  margin-right: -0.5rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%] {\\n  text-align: right;\\n  padding-top: 1rem;\\n  border-top: 1px solid #dee2e6;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .select-all-control[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 0.75rem;\\n  background-color: #f8f9fa;\\n  border-radius: 6px;\\n  border: 1px solid #e9ecef;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 3rem;\\n  color: #6c757d;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .spinning[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  font-size: 2rem;\\n  margin-bottom: 1rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  padding: 1rem;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  margin-bottom: 0.75rem;\\n  background-color: #ffffff;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover {\\n  border-color: #3366ff;\\n  box-shadow: 0 2px 8px rgba(51, 102, 255, 0.1);\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item.selected[_ngcontent-%COMP%] {\\n  border-color: #27ae60;\\n  background-color: #f8fff9;\\n  box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-checkbox[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n  margin-top: 0.25rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-main[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-main[_ngcontent-%COMP%]   .requirement-name[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2c3e50;\\n  margin-bottom: 0.25rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-main[_ngcontent-%COMP%]   .requirement-location[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #6c757d;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 0.5rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.85rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin-right: 0.5rem;\\n  min-width: 60px;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .no-data-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 3rem;\\n  color: #6c757d;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .no-data-state[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .no-data-state[_ngcontent-%COMP%]   .no-data-text[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .pagination-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 1.5rem;\\n  padding-top: 1rem;\\n  border-top: 1px solid #e9ecef;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .pagination-section[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #6c757d;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .pagination-section[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .pagination-section[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .page-info[_ngcontent-%COMP%] {\\n  margin: 0 0.5rem;\\n  font-size: 0.9rem;\\n  color: #495057;\\n  min-width: 60px;\\n  text-align: center;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1rem;\\n  background-color: #e8f5e8;\\n  border: 1px solid #27ae60;\\n  border-radius: 8px;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #27ae60;\\n  margin-bottom: 0.75rem;\\n  font-size: 1rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin-right: 0.5rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-value.total-price[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n  font-size: 1.1rem;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-footer[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid #e4e9f2;\\n  background-color: #f7f9fc;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n}\\n.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .requirement-template-dialog[_ngcontent-%COMP%] {\\n    width: 95vw;\\n    max-height: 95vh;\\n  }\\n  .requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  .requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-checkbox[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.5rem;\\n  }\\n  .requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "NbInputModule", "NbSelectModule", "NbOptionModule", "GetRequirement", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "status_r1", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "type_r2", "ctx_r2", "getSelectedItems", "length", "ɵɵlistener", "RequirementTemplateSelectorComponent_div_64_Template_nb_checkbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "toggleSelectAll", "isAllSelected", "isIndeterminate", "ɵɵelement", "ɵɵtwoWayListener", "RequirementTemplateSelectorComponent_div_66_div_1_Template_nb_checkbox_ngModelChange_2_listener", "requirement_r6", "_r5", "$implicit", "ɵɵtwoWayBindingSet", "selected", "toggleItemSelection", "ɵɵclassProp", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CRequirement", "CLocation", "getHouseTypeText", "CHouseType", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "CUnitPrice", "CUnit", "getStatusText", "CStatus", "getIsShowText", "CIsShow", "getIsSimpleText", "CIsSimple", "ɵɵtemplate", "RequirementTemplateSelectorComponent_div_66_div_1_Template", "requirements", "RequirementTemplateSelectorComponent_div_68_Template_button_click_4_listener", "_r7", "onPageChange", "currentPage", "RequirementTemplateSelectorComponent_div_68_Template_button_click_8_listener", "totalRecords", "getTotalPages", "getTotalPrice", "RequirementTemplateSelectorComponent", "constructor", "requirementService", "dialogRef", "buildCaseId", "multiple", "preSelectedItems", "selectionConfirmed", "selectionCancelled", "isLoading", "searchFilters", "CBuildCaseID", "PageIndex", "PageSize", "pageSize", "houseTypeOptions", "statusOptions", "ngOnInit", "loadRequirements", "initializePreSelectedItems", "for<PERSON>ach", "preSelected", "existingItem", "find", "req", "CRequirementID", "requestParams", "apiRequirementGetListPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "TotalItems", "error", "console", "onSearch", "onReset", "page", "selectAll", "filter", "reduce", "total", "confirmSelection", "selectedItems", "alert", "config", "totalPrice", "emit", "close", "houseTypes", "typeNames", "type", "option", "opt", "toString", "join", "status", "isShow", "isSimple", "Math", "ceil", "every", "some", "ɵɵdirectiveInject", "i1", "RequirementService", "i2", "NbDialogRef", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequirementTemplateSelectorComponent_Template", "rf", "ctx", "RequirementTemplateSelectorComponent_Template_button_click_4_listener", "RequirementTemplateSelectorComponent_Template_input_ngModelChange_16_listener", "RequirementTemplateSelectorComponent_Template_input_ngModelChange_20_listener", "RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_24_listener", "RequirementTemplateSelectorComponent_nb_option_25_Template", "RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_30_listener", "RequirementTemplateSelectorComponent_nb_option_31_Template", "RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_35_listener", "RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_45_listener", "RequirementTemplateSelectorComponent_Template_button_click_53_listener", "RequirementTemplateSelectorComponent_Template_button_click_56_listener", "RequirementTemplateSelectorComponent_span_63_Template", "RequirementTemplateSelectorComponent_div_64_Template", "RequirementTemplateSelectorComponent_div_65_Template", "RequirementTemplateSelectorComponent_div_66_Template", "RequirementTemplateSelectorComponent_div_67_Template", "RequirementTemplateSelectorComponent_div_68_Template", "RequirementTemplateSelectorComponent_div_69_Template", "RequirementTemplateSelectorComponent_Template_button_click_72_listener", "RequirementTemplateSelectorComponent_Template_button_click_74_listener", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i4", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbIconComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\requirement-template-selector\\requirement-template-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\requirement-template-selector\\requirement-template-selector.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport {\n  NbCardModule,\n  NbButtonModule,\n  NbIconModule,\n  NbCheckboxModule,\n  NbDialogRef,\n  NbInputModule,\n  NbSelectModule,\n  NbOptionModule\n} from '@nebular/theme';\nimport { RequirementService } from 'src/services/api/services/requirement.service';\nimport {\n  GetListRequirementRequest,\n  GetRequirement,\n  GetRequirementListResponseBase\n} from 'src/services/api/models';\n\n// 擴展 API 模型以支援前端選擇功能\nexport interface ExtendedRequirementItem extends GetRequirement {\n  selected?: boolean;\n}\n\n// 需求選擇配置介面\nexport interface RequirementSelectionConfig {\n  selectedItems: ExtendedRequirementItem[];\n  totalPrice: number;\n  buildCaseId: number;\n}\n\n@Component({\n  selector: 'app-requirement-template-selector',\n  templateUrl: './requirement-template-selector.component.html',\n  styleUrls: ['./requirement-template-selector.component.scss'],\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    NbCardModule,\n    NbButtonModule,\n    NbIconModule,\n    NbCheckboxModule,\n    NbInputModule,\n    NbSelectModule,\n    NbOptionModule\n  ]\n})\nexport class RequirementTemplateSelectorComponent implements OnInit {\n  @Input() buildCaseId: number = 0;\n  @Input() multiple: boolean = true;\n  @Input() preSelectedItems: ExtendedRequirementItem[] = [];\n  @Output() selectionConfirmed = new EventEmitter<RequirementSelectionConfig>();\n  @Output() selectionCancelled = new EventEmitter<void>();\n\n  // 資料相關屬性\n  requirements: ExtendedRequirementItem[] = [];\n  isLoading: boolean = false;\n\n  // 搜尋相關屬性\n  searchFilters: GetListRequirementRequest = {\n    CBuildCaseID: 0,\n    CLocation: null,\n    CRequirement: null,\n    CHouseType: null,\n    CStatus: 1, // 預設只顯示啟用的\n    CIsShow: null,\n    CIsSimple: null,\n    PageIndex: 1,\n    PageSize: 20\n  };\n\n  // 分頁相關屬性\n  totalRecords: number = 0;\n  currentPage: number = 1;\n  pageSize: number = 20;\n\n  // 房屋類型選項\n  houseTypeOptions = [\n    { value: 1, label: '套房' },\n    { value: 2, label: '1房' },\n    { value: 3, label: '2房' },\n    { value: 4, label: '3房' },\n    { value: 5, label: '4房' },\n    { value: 6, label: '5房以上' }\n  ];\n\n  // 狀態選項\n  statusOptions = [\n    { value: null, label: '全部' },\n    { value: 1, label: '啟用' },\n    { value: 0, label: '停用' }\n  ];\n\n  constructor(\n    private requirementService: RequirementService,\n    private dialogRef: NbDialogRef<RequirementTemplateSelectorComponent>\n  ) { }\n\n  ngOnInit() {\n    this.searchFilters.CBuildCaseID = this.buildCaseId;\n    this.loadRequirements();\n    this.initializePreSelectedItems();\n  }\n\n  // 初始化預選項目\n  initializePreSelectedItems() {\n    if (this.preSelectedItems && this.preSelectedItems.length > 0) {\n      // 將預選項目標記為已選擇\n      this.preSelectedItems.forEach(preSelected => {\n        const existingItem = this.requirements.find(req =>\n          req.CRequirementID === preSelected.CRequirementID\n        );\n        if (existingItem) {\n          existingItem.selected = true;\n        }\n      });\n    }\n  }\n\n  // 載入需求資料\n  loadRequirements() {\n    this.isLoading = true;\n\n    const requestParams = {\n      ...this.searchFilters,\n      PageIndex: this.currentPage,\n      PageSize: this.pageSize\n    };\n\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: requestParams\n    }).subscribe({\n      next: (response: GetRequirementListResponseBase) => {\n        this.isLoading = false;\n        if (response.StatusCode === 0 && response.Entries) {\n          this.requirements = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n          this.totalRecords = response.TotalItems || 0;\n          this.initializePreSelectedItems();\n        } else {\n          this.requirements = [];\n          this.totalRecords = 0;\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.requirements = [];\n        this.totalRecords = 0;\n        console.error('載入需求資料失敗:', error);\n      }\n    });\n  }\n\n  // 搜尋功能\n  onSearch() {\n    this.currentPage = 1;\n    this.loadRequirements();\n  }\n\n  // 重置搜尋\n  onReset() {\n    this.searchFilters = {\n      CBuildCaseID: this.buildCaseId,\n      CLocation: null,\n      CRequirement: null,\n      CHouseType: null,\n      CStatus: 1,\n      CIsShow: null,\n      CIsSimple: null,\n      PageIndex: 1,\n      PageSize: 20\n    };\n    this.currentPage = 1;\n    this.loadRequirements();\n  }\n\n  // 分頁變更\n  onPageChange(page: number) {\n    this.currentPage = page;\n    this.loadRequirements();\n  }\n\n  // 切換項目選擇狀態\n  toggleItemSelection(item: ExtendedRequirementItem) {\n    if (!this.multiple) {\n      // 單選模式：取消其他項目的選擇\n      this.requirements.forEach(req => {\n        if (req.CRequirementID !== item.CRequirementID) {\n          req.selected = false;\n        }\n      });\n    }\n    item.selected = !item.selected;\n  }\n\n  // 全選/取消全選\n  toggleSelectAll(selectAll: boolean) {\n    this.requirements.forEach(item => {\n      item.selected = selectAll;\n    });\n  }\n\n  // 獲取已選擇的項目\n  getSelectedItems(): ExtendedRequirementItem[] {\n    return this.requirements.filter(item => item.selected);\n  }\n\n  // 計算總價\n  getTotalPrice(): number {\n    return this.getSelectedItems().reduce((total, item) => {\n      return total + (item.CUnitPrice || 0);\n    }, 0);\n  }\n\n  // 確認選擇\n  confirmSelection() {\n    const selectedItems = this.getSelectedItems();\n    if (selectedItems.length === 0) {\n      alert('請至少選擇一個項目');\n      return;\n    }\n\n    const config: RequirementSelectionConfig = {\n      selectedItems: selectedItems,\n      totalPrice: this.getTotalPrice(),\n      buildCaseId: this.buildCaseId\n    };\n\n    this.selectionConfirmed.emit(config);\n    this.close();\n  }\n\n  // 關閉對話框\n  close() {\n    this.selectionCancelled.emit();\n    this.dialogRef.close();\n  }\n\n  // 獲取房屋類型顯示文字\n  getHouseTypeText(houseTypes: number[] | null | undefined): string {\n    if (!houseTypes || houseTypes.length === 0) {\n      return '-';\n    }\n\n    const typeNames = houseTypes.map(type => {\n      const option = this.houseTypeOptions.find(opt => opt.value === type);\n      return option ? option.label : type.toString();\n    });\n\n    return typeNames.join(', ');\n  }\n\n  // 獲取狀態顯示文字\n  getStatusText(status: number | undefined): string {\n    return status === 1 ? '啟用' : '停用';\n  }\n\n  // 獲取是否顯示文字\n  getIsShowText(isShow: boolean | undefined): string {\n    return isShow ? '是' : '否';\n  }\n\n  // 獲取簡易客變文字\n  getIsSimpleText(isSimple: boolean | undefined): string {\n    return isSimple ? '是' : '否';\n  }\n\n  // 獲取總頁數\n  getTotalPages(): number {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n\n  // 檢查是否全選\n  isAllSelected(): boolean {\n    return this.requirements.length > 0 && this.requirements.every(item => item.selected);\n  }\n\n  // 檢查是否部分選擇\n  isIndeterminate(): boolean {\n    return this.requirements.some(item => item.selected) && !this.requirements.every(item => item.selected);\n  }\n}\n", "<!-- 需求模板選擇器共用元件 -->\n<nb-card class=\"requirement-template-dialog\">\n  <nb-card-header class=\"requirement-template-header\">\n    <div class=\"requirement-template-title\">需求項目選擇器</div>\n    <button class=\"close-btn\" nbButton ghost (click)=\"close()\">\n      <nb-icon icon=\"close-outline\"></nb-icon>\n    </button>\n  </nb-card-header>\n\n  <nb-card-body class=\"requirement-template-body\">\n    <!-- 搜尋區域 -->\n    <div class=\"search-section\">\n      <div class=\"section-title\">\n        <nb-icon icon=\"search-outline\" class=\"mr-2\"></nb-icon>搜尋條件\n      </div>\n\n      <div class=\"search-form\">\n        <div class=\"row\">\n          <div class=\"col-md-4\">\n            <label class=\"search-label\">區域</label>\n            <input type=\"text\" nbInput placeholder=\"請輸入區域\" [(ngModel)]=\"searchFilters.CLocation\">\n          </div>\n          <div class=\"col-md-4\">\n            <label class=\"search-label\">工程項目</label>\n            <input type=\"text\" nbInput placeholder=\"請輸入工程項目\" [(ngModel)]=\"searchFilters.CRequirement\">\n          </div>\n          <div class=\"col-md-4\">\n            <label class=\"search-label\">狀態</label>\n            <nb-select [(ngModel)]=\"searchFilters.CStatus\" placeholder=\"請選擇狀態\">\n              <nb-option *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\n                {{ status.label }}\n              </nb-option>\n            </nb-select>\n          </div>\n        </div>\n\n        <div class=\"row mt-3\">\n          <div class=\"col-md-4\">\n            <label class=\"search-label\">房屋類型</label>\n            <nb-select [(ngModel)]=\"searchFilters.CHouseType\" placeholder=\"請選擇類型\" multiple>\n              <nb-option *ngFor=\"let type of houseTypeOptions\" [value]=\"type.value\">\n                {{ type.label }}\n              </nb-option>\n            </nb-select>\n          </div>\n          <div class=\"col-md-4\">\n            <label class=\"search-label\">預約需求</label>\n            <nb-select [(ngModel)]=\"searchFilters.CIsShow\" placeholder=\"全部\">\n              <nb-option [value]=\"null\">全部</nb-option>\n              <nb-option [value]=\"true\">是</nb-option>\n              <nb-option [value]=\"false\">否</nb-option>\n            </nb-select>\n          </div>\n          <div class=\"col-md-4\">\n            <label class=\"search-label\">簡易客變</label>\n            <nb-select [(ngModel)]=\"searchFilters.CIsSimple\" placeholder=\"全部\">\n              <nb-option [value]=\"null\">全部</nb-option>\n              <nb-option [value]=\"true\">是</nb-option>\n              <nb-option [value]=\"false\">否</nb-option>\n            </nb-select>\n          </div>\n        </div>\n\n        <div class=\"search-actions mt-3\">\n          <button nbButton status=\"basic\" (click)=\"onReset()\" class=\"mr-2\">\n            <nb-icon icon=\"refresh-outline\"></nb-icon>重置\n          </button>\n          <button nbButton status=\"primary\" (click)=\"onSearch()\">\n            <nb-icon icon=\"search-outline\"></nb-icon>搜尋\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 選擇區域 -->\n    <div class=\"selection-section\">\n      <div class=\"section-title\">\n        <nb-icon icon=\"list-outline\" class=\"mr-2\"></nb-icon>選擇項目\n        <span class=\"selected-count\" *ngIf=\"getSelectedItems().length > 0\">\n          (已選擇 {{ getSelectedItems().length }} 項)\n        </span>\n      </div>\n\n      <!-- 全選控制 -->\n      <div class=\"select-all-control\" *ngIf=\"multiple && requirements.length > 0\">\n        <nb-checkbox [ngModel]=\"isAllSelected()\" [indeterminate]=\"isIndeterminate()\"\n          (ngModelChange)=\"toggleSelectAll($event)\">\n          全選當前頁面\n        </nb-checkbox>\n      </div>\n\n      <!-- 載入中狀態 -->\n      <div *ngIf=\"isLoading\" class=\"loading-state\">\n        <nb-icon icon=\"loader-outline\" class=\"spinning\"></nb-icon>\n        載入中...\n      </div>\n\n      <!-- 需求項目列表 -->\n      <div *ngIf=\"!isLoading && requirements.length > 0\" class=\"requirement-list\">\n        <div *ngFor=\"let requirement of requirements\" class=\"requirement-item\" [class.selected]=\"requirement.selected\">\n          <div class=\"requirement-checkbox\">\n            <nb-checkbox [(ngModel)]=\"requirement.selected\" (ngModelChange)=\"toggleItemSelection(requirement)\">\n            </nb-checkbox>\n          </div>\n          <div class=\"requirement-info\">\n            <div class=\"requirement-main\">\n              <div class=\"requirement-name\">{{ requirement.CRequirement }}</div>\n              <div class=\"requirement-location\">{{ requirement.CLocation || '-' }}</div>\n            </div>\n            <div class=\"requirement-details\">\n              <div class=\"detail-item\">\n                <span class=\"detail-label\">類型:</span>\n                <span class=\"detail-value\">{{ getHouseTypeText(requirement.CHouseType) }}</span>\n              </div>\n              <div class=\"detail-item\">\n                <span class=\"detail-label\">單價:</span>\n                <span class=\"detail-value\">{{ requirement.CUnitPrice | number:'1.0-2' }} {{ requirement.CUnit || ''\n                  }}</span>\n              </div>\n              <div class=\"detail-item\">\n                <span class=\"detail-label\">狀態:</span>\n                <span class=\"detail-value\">{{ getStatusText(requirement.CStatus) }}</span>\n              </div>\n              <div class=\"detail-item\">\n                <span class=\"detail-label\">預約需求:</span>\n                <span class=\"detail-value\">{{ getIsShowText(requirement.CIsShow) }}</span>\n              </div>\n              <div class=\"detail-item\">\n                <span class=\"detail-label\">簡易客變:</span>\n                <span class=\"detail-value\">{{ getIsSimpleText(requirement.CIsSimple) }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 無資料狀態 -->\n      <div *ngIf=\"!isLoading && requirements.length === 0\" class=\"no-data-state\">\n        <nb-icon icon=\"inbox-outline\" class=\"no-data-icon\"></nb-icon>\n        <div class=\"no-data-text\">沒有找到符合條件的需求項目</div>\n      </div>\n\n      <!-- 分頁控制 -->\n      <div *ngIf=\"totalRecords > pageSize\" class=\"pagination-section\">\n        <div class=\"pagination-info\">\n          共 {{ totalRecords }} 筆資料，第 {{ currentPage }} 頁\n        </div>\n        <div class=\"pagination-controls\">\n          <button nbButton size=\"small\" status=\"basic\" [disabled]=\"currentPage <= 1\"\n            (click)=\"onPageChange(currentPage - 1)\">\n            <nb-icon icon=\"chevron-left-outline\"></nb-icon>\n          </button>\n          <span class=\"page-info\">{{ currentPage }} / {{ getTotalPages() }}</span>\n          <button nbButton size=\"small\" status=\"basic\" [disabled]=\"currentPage >= getTotalPages()\"\n            (click)=\"onPageChange(currentPage + 1)\">\n            <nb-icon icon=\"chevron-right-outline\"></nb-icon>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 選擇摘要 -->\n    <div *ngIf=\"getSelectedItems().length > 0\" class=\"selection-summary\">\n      <div class=\"summary-title\">選擇摘要</div>\n      <div class=\"summary-content\">\n        <div class=\"summary-item\">\n          <span class=\"summary-label\">已選擇項目:</span>\n          <span class=\"summary-value\">{{ getSelectedItems().length }} 項</span>\n        </div>\n        <div class=\"summary-item\">\n          <span class=\"summary-label\">總金額:</span>\n          <span class=\"summary-value total-price\">{{ getTotalPrice() | number:'1.0-2' }}</span>\n        </div>\n      </div>\n    </div>\n  </nb-card-body>\n\n  <!-- 操作按鈕 -->\n  <nb-card-footer class=\"requirement-template-footer\">\n    <div class=\"footer-actions\">\n      <button nbButton status=\"basic\" (click)=\"close()\" class=\"mr-2\">\n        取消\n      </button>\n      <button nbButton status=\"primary\" (click)=\"confirmSelection()\" [disabled]=\"getSelectedItems().length === 0\">\n        確認選擇 ({{ getSelectedItems().length }})\n      </button>\n    </div>\n  </nb-card-footer>\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAEhBC,aAAa,EACbC,cAAc,EACdC,cAAc,QACT,gBAAgB;AAEvB,SAEEC,cAAc,QAET,yBAAyB;;;;;;;;ICWlBC,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IACpEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IASAT,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAJ,KAAA,CAAoB;IACnEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAD,KAAA,MACF;;;;;IAoCNT,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,0BAAAG,MAAA,CAAAC,gBAAA,GAAAC,MAAA,cACF;;;;;;IAKAb,EADF,CAAAC,cAAA,cAA4E,sBAE9B;IAA1CD,EAAA,CAAAc,UAAA,2BAAAC,0FAAAC,MAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAX,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAiBT,MAAA,CAAAU,eAAA,CAAAL,MAAA,CAAuB;IAAA,EAAC;IACzChB,EAAA,CAAAE,MAAA,6CACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;;;;IAJSH,EAAA,CAAAO,SAAA,EAA2B;IAACP,EAA5B,CAAAI,UAAA,YAAAO,MAAA,CAAAW,aAAA,GAA2B,kBAAAX,MAAA,CAAAY,eAAA,GAAoC;;;;;IAO9EvB,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAwB,SAAA,kBAA0D;IAC1DxB,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMAH,EAFJ,CAAAC,cAAA,cAA+G,cAC3E,sBACmE;IAAtFD,EAAA,CAAAyB,gBAAA,2BAAAC,gGAAAV,MAAA;MAAA,MAAAW,cAAA,GAAA3B,EAAA,CAAAiB,aAAA,CAAAW,GAAA,EAAAC,SAAA;MAAA7B,EAAA,CAAA8B,kBAAA,CAAAH,cAAA,CAAAI,QAAA,EAAAf,MAAA,MAAAW,cAAA,CAAAI,QAAA,GAAAf,MAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAAAJ,MAAA;IAAA,EAAkC;IAAChB,EAAA,CAAAc,UAAA,2BAAAY,gGAAA;MAAA,MAAAC,cAAA,GAAA3B,EAAA,CAAAiB,aAAA,CAAAW,GAAA,EAAAC,SAAA;MAAA,MAAAlB,MAAA,GAAAX,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAiBT,MAAA,CAAAqB,mBAAA,CAAAL,cAAA,CAAgC;IAAA,EAAC;IAEpG3B,EADE,CAAAG,YAAA,EAAc,EACV;IAGFH,EAFJ,CAAAC,cAAA,cAA8B,cACE,cACE;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClEH,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IACtEF,EADsE,CAAAG,YAAA,EAAM,EACtE;IAGFH,EAFJ,CAAAC,cAAA,cAAiC,eACN,gBACI;IAAAD,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAC3EF,EAD2E,CAAAG,YAAA,EAAO,EAC5E;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACI;IAAAD,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IACvB;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACI;IAAAD,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAwC;IACrEF,EADqE,CAAAG,YAAA,EAAO,EACtE;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACI;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAwC;IACrEF,EADqE,CAAAG,YAAA,EAAO,EACtE;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACI;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA4C;IAI/EF,EAJ+E,CAAAG,YAAA,EAAO,EAC1E,EACF,EACF,EACF;;;;;IAlCiEH,EAAA,CAAAiC,WAAA,aAAAN,cAAA,CAAAI,QAAA,CAAuC;IAE7F/B,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAkC,gBAAA,YAAAP,cAAA,CAAAI,QAAA,CAAkC;IAKf/B,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAmC,iBAAA,CAAAR,cAAA,CAAAS,YAAA,CAA8B;IAC1BpC,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAmC,iBAAA,CAAAR,cAAA,CAAAU,SAAA,QAAkC;IAKvCrC,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAmC,iBAAA,CAAAxB,MAAA,CAAA2B,gBAAA,CAAAX,cAAA,CAAAY,UAAA,EAA8C;IAI9CvC,EAAA,CAAAO,SAAA,GACvB;IADuBP,EAAA,CAAAwC,kBAAA,KAAAxC,EAAA,CAAAyC,WAAA,SAAAd,cAAA,CAAAe,UAAA,iBAAAf,cAAA,CAAAgB,KAAA,WACvB;IAIuB3C,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAmC,iBAAA,CAAAxB,MAAA,CAAAiC,aAAA,CAAAjB,cAAA,CAAAkB,OAAA,EAAwC;IAIxC7C,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAmC,iBAAA,CAAAxB,MAAA,CAAAmC,aAAA,CAAAnB,cAAA,CAAAoB,OAAA,EAAwC;IAIxC/C,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAmC,iBAAA,CAAAxB,MAAA,CAAAqC,eAAA,CAAArB,cAAA,CAAAsB,SAAA,EAA4C;;;;;IA/BjFjD,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAkD,UAAA,IAAAC,0DAAA,oBAA+G;IAmCjHnD,EAAA,CAAAG,YAAA,EAAM;;;;IAnCyBH,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAyC,YAAA,CAAe;;;;;IAsC9CpD,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAwB,SAAA,kBAA6D;IAC7DxB,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,qFAAa;IACzCF,EADyC,CAAAG,YAAA,EAAM,EACzC;;;;;;IAIJH,EADF,CAAAC,cAAA,cAAgE,cACjC;IAC3BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAiC,iBAEW;IAAxCD,EAAA,CAAAc,UAAA,mBAAAuC,6EAAA;MAAArD,EAAA,CAAAiB,aAAA,CAAAqC,GAAA;MAAA,MAAA3C,MAAA,GAAAX,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAST,MAAA,CAAA4C,YAAA,CAAA5C,MAAA,CAAA6C,WAAA,GAA2B,CAAC,CAAC;IAAA,EAAC;IACvCxD,EAAA,CAAAwB,SAAA,kBAA+C;IACjDxB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAC,cAAA,iBAC0C;IAAxCD,EAAA,CAAAc,UAAA,mBAAA2C,6EAAA;MAAAzD,EAAA,CAAAiB,aAAA,CAAAqC,GAAA;MAAA,MAAA3C,MAAA,GAAAX,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAST,MAAA,CAAA4C,YAAA,CAAA5C,MAAA,CAAA6C,WAAA,GAA2B,CAAC,CAAC;IAAA,EAAC;IACvCxD,EAAA,CAAAwB,SAAA,kBAAgD;IAGtDxB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAbFH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAwC,kBAAA,aAAA7B,MAAA,CAAA+C,YAAA,sCAAA/C,MAAA,CAAA6C,WAAA,aACF;IAE+CxD,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA6C,WAAA,MAA6B;IAIlDxD,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAwC,kBAAA,KAAA7B,MAAA,CAAA6C,WAAA,SAAA7C,MAAA,CAAAgD,aAAA,OAAyC;IACpB3D,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA6C,WAAA,IAAA7C,MAAA,CAAAgD,aAAA,GAA2C;;;;;IAU5F3D,EADF,CAAAC,cAAA,cAAqE,cACxC;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGjCH,EAFJ,CAAAC,cAAA,cAA6B,cACD,eACI;IAAAD,EAAA,CAAAE,MAAA,sCAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAC/DF,EAD+D,CAAAG,YAAA,EAAO,EAChE;IAEJH,EADF,CAAAC,cAAA,cAA0B,gBACI;IAAAD,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,gBAAwC;IAAAD,EAAA,CAAAE,MAAA,IAAsC;;IAGpFF,EAHoF,CAAAG,YAAA,EAAO,EACjF,EACF,EACF;;;;IAP4BH,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAQ,kBAAA,KAAAG,MAAA,CAAAC,gBAAA,GAAAC,MAAA,YAAiC;IAIrBb,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAAyC,WAAA,QAAA9B,MAAA,CAAAiD,aAAA,aAAsC;;;AD1HxF,OAAM,MAAOC,oCAAoC;EA8C/CC,YACUC,kBAAsC,EACtCC,SAA4D;IAD5D,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,SAAS,GAATA,SAAS;IA/CV,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,QAAQ,GAAY,IAAI;IACxB,KAAAC,gBAAgB,GAA8B,EAAE;IAC/C,KAAAC,kBAAkB,GAAG,IAAI/E,YAAY,EAA8B;IACnE,KAAAgF,kBAAkB,GAAG,IAAIhF,YAAY,EAAQ;IAEvD;IACA,KAAA+D,YAAY,GAA8B,EAAE;IAC5C,KAAAkB,SAAS,GAAY,KAAK;IAE1B;IACA,KAAAC,aAAa,GAA8B;MACzCC,YAAY,EAAE,CAAC;MACfnC,SAAS,EAAE,IAAI;MACfD,YAAY,EAAE,IAAI;MAClBG,UAAU,EAAE,IAAI;MAChBM,OAAO,EAAE,CAAC;MAAE;MACZE,OAAO,EAAE,IAAI;MACbE,SAAS,EAAE,IAAI;MACfwB,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED;IACA,KAAAhB,YAAY,GAAW,CAAC;IACxB,KAAAF,WAAW,GAAW,CAAC;IACvB,KAAAmB,QAAQ,GAAW,EAAE;IAErB;IACA,KAAAC,gBAAgB,GAAG,CACjB;MAAEtE,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAI,CAAE,EACzB;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAI,CAAE,EACzB;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAI,CAAE,EACzB;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAI,CAAE,EACzB;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAI,CAAE,EACzB;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAM,CAAE,CAC5B;IAED;IACA,KAAAoE,aAAa,GAAG,CACd;MAAEvE,KAAK,EAAE,IAAI;MAAEG,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAI,CAAE,EACzB;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAI,CAAE,CAC1B;EAKG;EAEJqE,QAAQA,CAAA;IACN,IAAI,CAACP,aAAa,CAACC,YAAY,GAAG,IAAI,CAACP,WAAW;IAClD,IAAI,CAACc,gBAAgB,EAAE;IACvB,IAAI,CAACC,0BAA0B,EAAE;EACnC;EAEA;EACAA,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAACb,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACtD,MAAM,GAAG,CAAC,EAAE;MAC7D;MACA,IAAI,CAACsD,gBAAgB,CAACc,OAAO,CAACC,WAAW,IAAG;QAC1C,MAAMC,YAAY,GAAG,IAAI,CAAC/B,YAAY,CAACgC,IAAI,CAACC,GAAG,IAC7CA,GAAG,CAACC,cAAc,KAAKJ,WAAW,CAACI,cAAc,CAClD;QACD,IAAIH,YAAY,EAAE;UAChBA,YAAY,CAACpD,QAAQ,GAAG,IAAI;QAC9B;MACF,CAAC,CAAC;IACJ;EACF;EAEA;EACAgD,gBAAgBA,CAAA;IACd,IAAI,CAACT,SAAS,GAAG,IAAI;IAErB,MAAMiB,aAAa,GAAG;MACpB,GAAG,IAAI,CAAChB,aAAa;MACrBE,SAAS,EAAE,IAAI,CAACjB,WAAW;MAC3BkB,QAAQ,EAAE,IAAI,CAACC;KAChB;IAED,IAAI,CAACZ,kBAAkB,CAACyB,8BAA8B,CAAC;MACrDC,IAAI,EAAEF;KACP,CAAC,CAACG,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAwC,IAAI;QACjD,IAAI,CAACtB,SAAS,GAAG,KAAK;QACtB,IAAIsB,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAAC1C,YAAY,GAAGwC,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAChD,GAAGA,IAAI;YACPjE,QAAQ,EAAE;WACX,CAAC,CAAC;UACH,IAAI,CAAC2B,YAAY,GAAGkC,QAAQ,CAACK,UAAU,IAAI,CAAC;UAC5C,IAAI,CAACjB,0BAA0B,EAAE;QACnC,CAAC,MAAM;UACL,IAAI,CAAC5B,YAAY,GAAG,EAAE;UACtB,IAAI,CAACM,YAAY,GAAG,CAAC;QACvB;MACF,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5B,SAAS,GAAG,KAAK;QACtB,IAAI,CAAClB,YAAY,GAAG,EAAE;QACtB,IAAI,CAACM,YAAY,GAAG,CAAC;QACrByC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;KACD,CAAC;EACJ;EAEA;EACAE,QAAQA,CAAA;IACN,IAAI,CAAC5C,WAAW,GAAG,CAAC;IACpB,IAAI,CAACuB,gBAAgB,EAAE;EACzB;EAEA;EACAsB,OAAOA,CAAA;IACL,IAAI,CAAC9B,aAAa,GAAG;MACnBC,YAAY,EAAE,IAAI,CAACP,WAAW;MAC9B5B,SAAS,EAAE,IAAI;MACfD,YAAY,EAAE,IAAI;MAClBG,UAAU,EAAE,IAAI;MAChBM,OAAO,EAAE,CAAC;MACVE,OAAO,EAAE,IAAI;MACbE,SAAS,EAAE,IAAI;MACfwB,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IACD,IAAI,CAAClB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACuB,gBAAgB,EAAE;EACzB;EAEA;EACAxB,YAAYA,CAAC+C,IAAY;IACvB,IAAI,CAAC9C,WAAW,GAAG8C,IAAI;IACvB,IAAI,CAACvB,gBAAgB,EAAE;EACzB;EAEA;EACA/C,mBAAmBA,CAACgE,IAA6B;IAC/C,IAAI,CAAC,IAAI,CAAC9B,QAAQ,EAAE;MAClB;MACA,IAAI,CAACd,YAAY,CAAC6B,OAAO,CAACI,GAAG,IAAG;QAC9B,IAAIA,GAAG,CAACC,cAAc,KAAKU,IAAI,CAACV,cAAc,EAAE;UAC9CD,GAAG,CAACtD,QAAQ,GAAG,KAAK;QACtB;MACF,CAAC,CAAC;IACJ;IACAiE,IAAI,CAACjE,QAAQ,GAAG,CAACiE,IAAI,CAACjE,QAAQ;EAChC;EAEA;EACAV,eAAeA,CAACkF,SAAkB;IAChC,IAAI,CAACnD,YAAY,CAAC6B,OAAO,CAACe,IAAI,IAAG;MAC/BA,IAAI,CAACjE,QAAQ,GAAGwE,SAAS;IAC3B,CAAC,CAAC;EACJ;EAEA;EACA3F,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACwC,YAAY,CAACoD,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACjE,QAAQ,CAAC;EACxD;EAEA;EACA6B,aAAaA,CAAA;IACX,OAAO,IAAI,CAAChD,gBAAgB,EAAE,CAAC6F,MAAM,CAAC,CAACC,KAAK,EAAEV,IAAI,KAAI;MACpD,OAAOU,KAAK,IAAIV,IAAI,CAACtD,UAAU,IAAI,CAAC,CAAC;IACvC,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACAiE,gBAAgBA,CAAA;IACd,MAAMC,aAAa,GAAG,IAAI,CAAChG,gBAAgB,EAAE;IAC7C,IAAIgG,aAAa,CAAC/F,MAAM,KAAK,CAAC,EAAE;MAC9BgG,KAAK,CAAC,WAAW,CAAC;MAClB;IACF;IAEA,MAAMC,MAAM,GAA+B;MACzCF,aAAa,EAAEA,aAAa;MAC5BG,UAAU,EAAE,IAAI,CAACnD,aAAa,EAAE;MAChCK,WAAW,EAAE,IAAI,CAACA;KACnB;IAED,IAAI,CAACG,kBAAkB,CAAC4C,IAAI,CAACF,MAAM,CAAC;IACpC,IAAI,CAACG,KAAK,EAAE;EACd;EAEA;EACAA,KAAKA,CAAA;IACH,IAAI,CAAC5C,kBAAkB,CAAC2C,IAAI,EAAE;IAC9B,IAAI,CAAChD,SAAS,CAACiD,KAAK,EAAE;EACxB;EAEA;EACA3E,gBAAgBA,CAAC4E,UAAuC;IACtD,IAAI,CAACA,UAAU,IAAIA,UAAU,CAACrG,MAAM,KAAK,CAAC,EAAE;MAC1C,OAAO,GAAG;IACZ;IAEA,MAAMsG,SAAS,GAAGD,UAAU,CAACnB,GAAG,CAACqB,IAAI,IAAG;MACtC,MAAMC,MAAM,GAAG,IAAI,CAACzC,gBAAgB,CAACQ,IAAI,CAACkC,GAAG,IAAIA,GAAG,CAAChH,KAAK,KAAK8G,IAAI,CAAC;MACpE,OAAOC,MAAM,GAAGA,MAAM,CAAC5G,KAAK,GAAG2G,IAAI,CAACG,QAAQ,EAAE;IAChD,CAAC,CAAC;IAEF,OAAOJ,SAAS,CAACK,IAAI,CAAC,IAAI,CAAC;EAC7B;EAEA;EACA5E,aAAaA,CAAC6E,MAA0B;IACtC,OAAOA,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI;EACnC;EAEA;EACA3E,aAAaA,CAAC4E,MAA2B;IACvC,OAAOA,MAAM,GAAG,GAAG,GAAG,GAAG;EAC3B;EAEA;EACA1E,eAAeA,CAAC2E,QAA6B;IAC3C,OAAOA,QAAQ,GAAG,GAAG,GAAG,GAAG;EAC7B;EAEA;EACAhE,aAAaA,CAAA;IACX,OAAOiE,IAAI,CAACC,IAAI,CAAC,IAAI,CAACnE,YAAY,GAAG,IAAI,CAACiB,QAAQ,CAAC;EACrD;EAEA;EACArD,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC8B,YAAY,CAACvC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACuC,YAAY,CAAC0E,KAAK,CAAC9B,IAAI,IAAIA,IAAI,CAACjE,QAAQ,CAAC;EACvF;EAEA;EACAR,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC6B,YAAY,CAAC2E,IAAI,CAAC/B,IAAI,IAAIA,IAAI,CAACjE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAACqB,YAAY,CAAC0E,KAAK,CAAC9B,IAAI,IAAIA,IAAI,CAACjE,QAAQ,CAAC;EACzG;;;uCA3OW8B,oCAAoC,EAAA7D,EAAA,CAAAgI,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAlI,EAAA,CAAAgI,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApCvE,oCAAoC;MAAAwE,SAAA;MAAAC,MAAA;QAAArE,WAAA;QAAAC,QAAA;QAAAC,gBAAA;MAAA;MAAAoE,OAAA;QAAAnE,kBAAA;QAAAC,kBAAA;MAAA;MAAAmE,UAAA;MAAAC,QAAA,GAAAzI,EAAA,CAAA0I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9C7ChJ,EAFJ,CAAAC,cAAA,iBAA6C,wBACS,aACV;UAAAD,EAAA,CAAAE,MAAA,iDAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACrDH,EAAA,CAAAC,cAAA,gBAA2D;UAAlBD,EAAA,CAAAc,UAAA,mBAAAoI,sEAAA;YAAA,OAASD,GAAA,CAAAhC,KAAA,EAAO;UAAA,EAAC;UACxDjH,EAAA,CAAAwB,SAAA,iBAAwC;UAE5CxB,EADE,CAAAG,YAAA,EAAS,EACM;UAKbH,EAHJ,CAAAC,cAAA,sBAAgD,aAElB,aACC;UACzBD,EAAA,CAAAwB,SAAA,iBAAsD;UAAAxB,EAAA,CAAAE,MAAA,iCACxD;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAKAH,EAHN,CAAAC,cAAA,cAAyB,eACN,eACO,iBACQ;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,iBAAqF;UAAtCD,EAAA,CAAAyB,gBAAA,2BAAA0H,8EAAAnI,MAAA;YAAAhB,EAAA,CAAA8B,kBAAA,CAAAmH,GAAA,CAAA1E,aAAA,CAAAlC,SAAA,EAAArB,MAAA,MAAAiI,GAAA,CAAA1E,aAAA,CAAAlC,SAAA,GAAArB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqC;UACtFhB,EADE,CAAAG,YAAA,EAAqF,EACjF;UAEJH,EADF,CAAAC,cAAA,eAAsB,iBACQ;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,iBAA0F;UAAzCD,EAAA,CAAAyB,gBAAA,2BAAA2H,8EAAApI,MAAA;YAAAhB,EAAA,CAAA8B,kBAAA,CAAAmH,GAAA,CAAA1E,aAAA,CAAAnC,YAAA,EAAApB,MAAA,MAAAiI,GAAA,CAAA1E,aAAA,CAAAnC,YAAA,GAAApB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwC;UAC3FhB,EADE,CAAAG,YAAA,EAA0F,EACtF;UAEJH,EADF,CAAAC,cAAA,eAAsB,iBACQ;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,qBAAmE;UAAxDD,EAAA,CAAAyB,gBAAA,2BAAA4H,kFAAArI,MAAA;YAAAhB,EAAA,CAAA8B,kBAAA,CAAAmH,GAAA,CAAA1E,aAAA,CAAA1B,OAAA,EAAA7B,MAAA,MAAAiI,GAAA,CAAA1E,aAAA,CAAA1B,OAAA,GAAA7B,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UAC5ChB,EAAA,CAAAkD,UAAA,KAAAoG,0DAAA,wBAAuE;UAK7EtJ,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACQ;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,qBAA+E;UAApED,EAAA,CAAAyB,gBAAA,2BAAA8H,kFAAAvI,MAAA;YAAAhB,EAAA,CAAA8B,kBAAA,CAAAmH,GAAA,CAAA1E,aAAA,CAAAhC,UAAA,EAAAvB,MAAA,MAAAiI,GAAA,CAAA1E,aAAA,CAAAhC,UAAA,GAAAvB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsC;UAC/ChB,EAAA,CAAAkD,UAAA,KAAAsG,0DAAA,wBAAsE;UAI1ExJ,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,eAAsB,iBACQ;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,qBAAgE;UAArDD,EAAA,CAAAyB,gBAAA,2BAAAgI,kFAAAzI,MAAA;YAAAhB,EAAA,CAAA8B,kBAAA,CAAAmH,GAAA,CAAA1E,aAAA,CAAAxB,OAAA,EAAA/B,MAAA,MAAAiI,GAAA,CAAA1E,aAAA,CAAAxB,OAAA,GAAA/B,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UAC5ChB,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAEhCF,EAFgC,CAAAG,YAAA,EAAY,EAC9B,EACR;UAEJH,EADF,CAAAC,cAAA,eAAsB,iBACQ;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,qBAAkE;UAAvDD,EAAA,CAAAyB,gBAAA,2BAAAiI,kFAAA1I,MAAA;YAAAhB,EAAA,CAAA8B,kBAAA,CAAAmH,GAAA,CAAA1E,aAAA,CAAAtB,SAAA,EAAAjC,MAAA,MAAAiI,GAAA,CAAA1E,aAAA,CAAAtB,SAAA,GAAAjC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqC;UAC9ChB,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAGlCF,EAHkC,CAAAG,YAAA,EAAY,EAC9B,EACR,EACF;UAGJH,EADF,CAAAC,cAAA,eAAiC,kBACkC;UAAjCD,EAAA,CAAAc,UAAA,mBAAA6I,uEAAA;YAAA,OAASV,GAAA,CAAA5C,OAAA,EAAS;UAAA,EAAC;UACjDrG,EAAA,CAAAwB,SAAA,mBAA0C;UAAAxB,EAAA,CAAAE,MAAA,qBAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAuD;UAArBD,EAAA,CAAAc,UAAA,mBAAA8I,uEAAA;YAAA,OAASX,GAAA,CAAA7C,QAAA,EAAU;UAAA,EAAC;UACpDpG,EAAA,CAAAwB,SAAA,mBAAyC;UAAAxB,EAAA,CAAAE,MAAA,qBAC3C;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAIJH,EADF,CAAAC,cAAA,eAA+B,cACF;UACzBD,EAAA,CAAAwB,SAAA,mBAAoD;UAAAxB,EAAA,CAAAE,MAAA,iCACpD;UAAAF,EAAA,CAAAkD,UAAA,KAAA2G,qDAAA,mBAAmE;UAGrE7J,EAAA,CAAAG,YAAA,EAAM;UA8DNH,EA3DA,CAAAkD,UAAA,KAAA4G,oDAAA,kBAA4E,KAAAC,oDAAA,kBAQ/B,KAAAC,oDAAA,kBAM+B,KAAAC,oDAAA,kBAuCD,KAAAC,oDAAA,mBAMX;UAgBlElK,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAkD,UAAA,KAAAiH,oDAAA,mBAAqE;UAavEnK,EAAA,CAAAG,YAAA,EAAe;UAKXH,EAFJ,CAAAC,cAAA,0BAAoD,eACtB,kBACqC;UAA/BD,EAAA,CAAAc,UAAA,mBAAAsJ,uEAAA;YAAA,OAASnB,GAAA,CAAAhC,KAAA,EAAO;UAAA,EAAC;UAC/CjH,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA4G;UAA1ED,EAAA,CAAAc,UAAA,mBAAAuJ,uEAAA;YAAA,OAASpB,GAAA,CAAAtC,gBAAA,EAAkB;UAAA,EAAC;UAC5D3G,EAAA,CAAAE,MAAA,IACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;;;UAxKiDH,EAAA,CAAAO,SAAA,IAAqC;UAArCP,EAAA,CAAAkC,gBAAA,YAAA+G,GAAA,CAAA1E,aAAA,CAAAlC,SAAA,CAAqC;UAInCrC,EAAA,CAAAO,SAAA,GAAwC;UAAxCP,EAAA,CAAAkC,gBAAA,YAAA+G,GAAA,CAAA1E,aAAA,CAAAnC,YAAA,CAAwC;UAI9EpC,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAkC,gBAAA,YAAA+G,GAAA,CAAA1E,aAAA,CAAA1B,OAAA,CAAmC;UACd7C,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAA6I,GAAA,CAAApE,aAAA,CAAgB;UAUrC7E,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAkC,gBAAA,YAAA+G,GAAA,CAAA1E,aAAA,CAAAhC,UAAA,CAAsC;UACnBvC,EAAA,CAAAO,SAAA,EAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAA6I,GAAA,CAAArE,gBAAA,CAAmB;UAOtC5E,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAkC,gBAAA,YAAA+G,GAAA,CAAA1E,aAAA,CAAAxB,OAAA,CAAmC;UACjC/C,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UAKjBJ,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAkC,gBAAA,YAAA+G,GAAA,CAAA1E,aAAA,CAAAtB,SAAA,CAAqC;UACnCjD,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UAoBFJ,EAAA,CAAAO,SAAA,IAAmC;UAAnCP,EAAA,CAAAI,UAAA,SAAA6I,GAAA,CAAArI,gBAAA,GAAAC,MAAA,KAAmC;UAMlCb,EAAA,CAAAO,SAAA,EAAyC;UAAzCP,EAAA,CAAAI,UAAA,SAAA6I,GAAA,CAAA/E,QAAA,IAAA+E,GAAA,CAAA7F,YAAA,CAAAvC,MAAA,KAAyC;UAQpEb,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAA6I,GAAA,CAAA3E,SAAA,CAAe;UAMftE,EAAA,CAAAO,SAAA,EAA2C;UAA3CP,EAAA,CAAAI,UAAA,UAAA6I,GAAA,CAAA3E,SAAA,IAAA2E,GAAA,CAAA7F,YAAA,CAAAvC,MAAA,KAA2C;UAuC3Cb,EAAA,CAAAO,SAAA,EAA6C;UAA7CP,EAAA,CAAAI,UAAA,UAAA6I,GAAA,CAAA3E,SAAA,IAAA2E,GAAA,CAAA7F,YAAA,CAAAvC,MAAA,OAA6C;UAM7Cb,EAAA,CAAAO,SAAA,EAA6B;UAA7BP,EAAA,CAAAI,UAAA,SAAA6I,GAAA,CAAAvF,YAAA,GAAAuF,GAAA,CAAAtE,QAAA,CAA6B;UAmB/B3E,EAAA,CAAAO,SAAA,EAAmC;UAAnCP,EAAA,CAAAI,UAAA,SAAA6I,GAAA,CAAArI,gBAAA,GAAAC,MAAA,KAAmC;UAqBwBb,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAI,UAAA,aAAA6I,GAAA,CAAArI,gBAAA,GAAAC,MAAA,OAA4C;UACzGb,EAAA,CAAAO,SAAA,EACF;UADEP,EAAA,CAAAQ,kBAAA,gCAAAyI,GAAA,CAAArI,gBAAA,GAAAC,MAAA,OACF;;;qBDnJFvB,YAAY,EAAAgL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EACZlL,WAAW,EAAAmL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXrL,YAAY,EAAA2I,EAAA,CAAA2C,eAAA,EAAA3C,EAAA,CAAA4C,mBAAA,EAAA5C,EAAA,CAAA6C,qBAAA,EAAA7C,EAAA,CAAA8C,qBAAA,EACZxL,cAAc,EAAA0I,EAAA,CAAA+C,iBAAA,EACdxL,YAAY,EAAAyI,EAAA,CAAAgD,eAAA,EACZxL,gBAAgB,EAAAwI,EAAA,CAAAiD,mBAAA,EAChBxL,aAAa,EAAAuI,EAAA,CAAAkD,gBAAA,EACbxL,cAAc,EAAAsI,EAAA,CAAAmD,iBAAA,EAAAnD,EAAA,CAAAoD,iBAAA,EACdzL,cAAc;MAAA0L,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}