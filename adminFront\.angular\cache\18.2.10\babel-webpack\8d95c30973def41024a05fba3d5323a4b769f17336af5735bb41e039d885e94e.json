{"ast": null, "code": "import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nlet AutoFocus = /*#__PURE__*/(() => {\n  class AutoFocus {\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus = false;\n    focused = false;\n    platformId = inject(PLATFORM_ID);\n    document = inject(DOCUMENT);\n    host = inject(ElementRef);\n    ngAfterContentChecked() {\n      // This sets the `attr.autofocus` which is different than the Input `autofocus` attribute.\n      if (this.autofocus === false) {\n        this.host.nativeElement.removeAttribute('autofocus');\n      } else {\n        this.host.nativeElement.setAttribute('autofocus', true);\n      }\n      if (!this.focused) {\n        this.autoFocus();\n      }\n    }\n    ngAfterViewChecked() {\n      if (!this.focused) {\n        this.autoFocus();\n      }\n    }\n    autoFocus() {\n      if (isPlatformBrowser(this.platformId) && this.autofocus) {\n        setTimeout(() => {\n          const focusableElements = DomHandler.getFocusableElements(this.host?.nativeElement);\n          if (focusableElements.length === 0) {\n            this.host.nativeElement.focus();\n          }\n          if (focusableElements.length > 0) {\n            focusableElements[0].focus();\n          }\n          this.focused = true;\n        });\n      }\n    }\n    static ɵfac = function AutoFocus_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AutoFocus)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: AutoFocus,\n      selectors: [[\"\", \"pAutoFocus\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        autofocus: [2, \"autofocus\", \"autofocus\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n  return AutoFocus;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AutoFocusModule = /*#__PURE__*/(() => {\n  class AutoFocusModule {\n    static ɵfac = function AutoFocusModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AutoFocusModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AutoFocusModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return AutoFocusModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };\n//# sourceMappingURL=primeng-autofocus.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}