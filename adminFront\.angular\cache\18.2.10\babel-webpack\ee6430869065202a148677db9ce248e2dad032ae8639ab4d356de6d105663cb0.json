{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nlet SpaceTemplateSelectorComponent = class SpaceTemplateSelectorComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.isVisible = false;\n    this.buildCaseId = '';\n    this.templateApplied = new EventEmitter();\n    this.closed = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templates = []; // 直接使用 API 資料\n    this.selectedTemplateDetails = new Map(); // 存儲已載入的模板詳情\n  }\n  ngOnInit() {\n    // 移除自動載入，改為在開啟時才載入\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: 1,\n      // 1=客變需求\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 直接使用 API 資料，只添加 selected 屬性\n          this.templates = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        this.templates = [];\n      }\n    });\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  getSelectedItems() {\n    return this.templates.filter(item => item.selected);\n  }\n  getSelectedTotalPrice() {\n    // 由於 API 沒有價格資訊，返回 0\n    return 0;\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      if (this.currentStep === 1) {\n        // 進入步驟2前，載入選中模板的詳情\n        this.loadSelectedTemplateDetails();\n      }\n      this.currentStep++;\n    }\n  }\n  loadSelectedTemplateDetails() {\n    const selectedItems = this.getSelectedItems();\n    selectedItems.forEach(item => {\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\n        // 只載入尚未載入過的模板詳情\n        this.loadTemplateDetailById(item.CTemplateId);\n      }\n    });\n  }\n  loadTemplateDetailById(templateId) {\n    const args = {\n      templateId: templateId\n    };\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          this.selectedTemplateDetails.set(templateId, response.Entries);\n        }\n      },\n      error: () => {\n        // 錯誤處理：設置空陣列\n        this.selectedTemplateDetails.set(templateId, []);\n      }\n    });\n  }\n  getTemplateDetails(templateId) {\n    return this.selectedTemplateDetails.get(templateId) || [];\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要套用的模板項目',\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: '通用模板',\n      selectedItems: this.getSelectedItems(),\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.isVisible = false;\n    this.resetSelections();\n    this.closed.emit();\n  }\n  onBackdropClick(event) {\n    if (event.target === event.currentTarget) {\n      this.close();\n    }\n  }\n  reset() {\n    this.currentStep = 1;\n    this.templates = [];\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    // 保留 templates 資料，只重置選擇狀態\n    this.templates.forEach(template => {\n      template.selected = false;\n    });\n    // 清空詳情快取\n    this.selectedTemplateDetails.clear();\n  }\n  // 公共API方法\n  open() {\n    this.isVisible = true;\n    if (this.templates.length === 0) {\n      // 只有在沒有資料時才載入\n      this.loadTemplatesFromAPI();\n    } else {\n      // 如果已有資料，只重置選擇狀態和步驟\n      this.currentStep = 1;\n      this.templates.forEach(template => {\n        template.selected = false;\n      });\n      this.selectedTemplateDetails.clear();\n    }\n  }\n};\n__decorate([Input()], SpaceTemplateSelectorComponent.prototype, \"isVisible\", void 0);\n__decorate([Input()], SpaceTemplateSelectorComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Output()], SpaceTemplateSelectorComponent.prototype, \"templateApplied\", void 0);\n__decorate([Output()], SpaceTemplateSelectorComponent.prototype, \"closed\", void 0);\nSpaceTemplateSelectorComponent = __decorate([Component({\n  selector: 'app-space-template-selector',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './space-template-selector.component.html',\n  styleUrls: ['./space-template-selector.component.scss']\n})], SpaceTemplateSelectorComponent);\nexport { SpaceTemplateSelectorComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "FormsModule", "SpaceTemplateSelectorComponent", "constructor", "templateService", "isVisible", "buildCaseId", "templateApplied", "closed", "currentStep", "templates", "selectedTemplateDetails", "Map", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "selected", "error", "onTemplateItemChange", "getSelectedItems", "filter", "getSelectedTotalPrice", "canProceed", "length", "nextStep", "loadSelectedTemplateDetails", "selectedItems", "for<PERSON>ach", "CTemplateId", "has", "loadTemplateDetailById", "templateId", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "set", "getTemplateDetails", "get", "previousStep", "getProgressText", "progressTexts", "hasConflicts", "getConflictCount", "applyTemplate", "config", "spaceId", "spaceName", "totalPrice", "emit", "close", "resetSelections", "onBackdropClick", "event", "target", "currentTarget", "reset", "template", "clear", "open", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, TemplateGetListResponse, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';\r\n\r\n// 擴展 API 模型以支援前端選擇功能\r\nexport interface ExtendedTemplateItem extends TemplateGetListResponse {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: ExtendedTemplateItem[];\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule\r\n  ],\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() buildCaseId: string = '';\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n  @Output() closed = new EventEmitter<void>();\r\n\r\n  currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n  templates: ExtendedTemplateItem[] = []; // 直接使用 API 資料\r\n  selectedTemplateDetails: Map<number, TemplateDetailItem[]> = new Map(); // 存儲已載入的模板詳情\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    // 移除自動載入，改為在開啟時才載入\r\n  }\r\n\r\n  loadTemplatesFromAPI() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: 1, // 1=客變需求\r\n      PageIndex: 1,\r\n      PageSize: 100, // 載入足夠的資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateListForCommon API\r\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // 直接使用 API 資料，只添加 selected 屬性\r\n          this.templates = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  getSelectedItems(): ExtendedTemplateItem[] {\r\n    return this.templates.filter(item => item.selected);\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    // 由於 API 沒有價格資訊，返回 0\r\n    return 0;\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      if (this.currentStep === 1) {\r\n        // 進入步驟2前，載入選中模板的詳情\r\n        this.loadSelectedTemplateDetails();\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  loadSelectedTemplateDetails() {\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    selectedItems.forEach(item => {\r\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\r\n        // 只載入尚未載入過的模板詳情\r\n        this.loadTemplateDetailById(item.CTemplateId);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTemplateDetailById(templateId: number) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.selectedTemplateDetails.set(templateId, response.Entries);\r\n        }\r\n      },\r\n      error: () => {\r\n        // 錯誤處理：設置空陣列\r\n        this.selectedTemplateDetails.set(templateId, []);\r\n      }\r\n    });\r\n  }\r\n\r\n  getTemplateDetails(templateId: number): TemplateDetailItem[] {\r\n    return this.selectedTemplateDetails.get(templateId) || [];\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要套用的模板項目',\r\n      2: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: 'common', // 通用模板，不特定空間\r\n      spaceName: '通用模板',\r\n      selectedItems: this.getSelectedItems(),\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.isVisible = false;\r\n    this.resetSelections();\r\n    this.closed.emit();\r\n  }\r\n\r\n  onBackdropClick(event: Event) {\r\n    if (event.target === event.currentTarget) {\r\n      this.close();\r\n    }\r\n  }\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.templates = [];\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    // 保留 templates 資料，只重置選擇狀態\r\n    this.templates.forEach(template => {\r\n      template.selected = false;\r\n    });\r\n    // 清空詳情快取\r\n    this.selectedTemplateDetails.clear();\r\n  }\r\n\r\n  // 公共API方法\r\n  open() {\r\n    this.isVisible = true;\r\n    if (this.templates.length === 0) {\r\n      // 只有在沒有資料時才載入\r\n      this.loadTemplatesFromAPI();\r\n    } else {\r\n      // 如果已有資料，只重置選擇狀態和步驟\r\n      this.currentStep = 1;\r\n      this.templates.forEach(template => {\r\n        template.selected = false;\r\n      });\r\n      this.selectedTemplateDetails.clear();\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AA0BrC,IAAMC,8BAA8B,GAApC,MAAMA,8BAA8B;EAUzCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAT1B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,WAAW,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAG,IAAIV,YAAY,EAAuB;IACzD,KAAAW,MAAM,GAAG,IAAIX,YAAY,EAAQ;IAE3C,KAAAY,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAC,SAAS,GAA2B,EAAE,CAAC,CAAC;IACxC,KAAAC,uBAAuB,GAAsC,IAAIC,GAAG,EAAE,CAAC,CAAC;EAEhB;EAExDC,QAAQA,CAAA;IACN;EAAA;EAGFC,oBAAoBA,CAAA;IAClB;IACA,MAAMC,mBAAmB,GAAwB;MAC/CC,aAAa,EAAE,CAAC;MAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAACf,eAAe,CAACgB,4CAA4C,CAAC;MAChEC,IAAI,EAAEN;KACP,CAAC,CAACO,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACA,IAAI,CAAChB,SAAS,GAAGc,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C,GAAGA,IAAI;YACPC,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL;UACA,IAAI,CAACnB,SAAS,GAAG,EAAE;QACrB;MACF,CAAC;MACDoB,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAACpB,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEAqB,oBAAoBA,CAAA;IAClB;EAAA;EAGFC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACtB,SAAS,CAACuB,MAAM,CAACL,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;EACrD;EAEAK,qBAAqBA,CAAA;IACnB;IACA,OAAO,CAAC;EACV;EAEAC,UAAUA,CAAA;IACR,QAAQ,IAAI,CAAC1B,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACuB,gBAAgB,EAAE,CAACI,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACF,UAAU,EAAE,IAAI,IAAI,CAAC1B,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B;QACA,IAAI,CAAC6B,2BAA2B,EAAE;MACpC;MACA,IAAI,CAAC7B,WAAW,EAAE;IACpB;EACF;EAEA6B,2BAA2BA,CAAA;IACzB,MAAMC,aAAa,GAAG,IAAI,CAACP,gBAAgB,EAAE;IAE7CO,aAAa,CAACC,OAAO,CAACZ,IAAI,IAAG;MAC3B,IAAIA,IAAI,CAACa,WAAW,IAAI,CAAC,IAAI,CAAC9B,uBAAuB,CAAC+B,GAAG,CAACd,IAAI,CAACa,WAAW,CAAC,EAAE;QAC3E;QACA,IAAI,CAACE,sBAAsB,CAACf,IAAI,CAACa,WAAW,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAE,sBAAsBA,CAACC,UAAkB;IACvC,MAAMC,IAAI,GAA8B;MACtCD,UAAU,EAAEA;KACb;IAED,IAAI,CAACxC,eAAe,CAAC0C,yCAAyC,CAAC;MAC7DzB,IAAI,EAAEwB;KACP,CAAC,CAACvB,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAACf,uBAAuB,CAACoC,GAAG,CAACH,UAAU,EAAEpB,QAAQ,CAACE,OAAO,CAAC;QAChE;MACF,CAAC;MACDI,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACnB,uBAAuB,CAACoC,GAAG,CAACH,UAAU,EAAE,EAAE,CAAC;MAClD;KACD,CAAC;EACJ;EAEAI,kBAAkBA,CAACJ,UAAkB;IACnC,OAAO,IAAI,CAACjC,uBAAuB,CAACsC,GAAG,CAACL,UAAU,CAAC,IAAI,EAAE;EAC3D;EAEAM,YAAYA,CAAA;IACV,IAAI,IAAI,CAACzC,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEA0C,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAAC3C,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEA4C,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACrB,gBAAgB,EAAE,CAACI,MAAM,GAAG,CAAC;EAC3C;EAEAkB,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACD,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAE,aAAaA,CAAA;IACX,MAAMC,MAAM,GAAwB;MAClCC,OAAO,EAAE,QAAQ;MAAE;MACnBC,SAAS,EAAE,MAAM;MACjBnB,aAAa,EAAE,IAAI,CAACP,gBAAgB,EAAE;MACtC2B,UAAU,EAAE,IAAI,CAACzB,qBAAqB;KACvC;IAED,IAAI,CAAC3B,eAAe,CAACqD,IAAI,CAACJ,MAAM,CAAC;IACjC,IAAI,CAACK,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACxD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACyD,eAAe,EAAE;IACtB,IAAI,CAACtD,MAAM,CAACoD,IAAI,EAAE;EACpB;EAEAG,eAAeA,CAACC,KAAY;IAC1B,IAAIA,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,aAAa,EAAE;MACxC,IAAI,CAACL,KAAK,EAAE;IACd;EACF;EAEQM,KAAKA,CAAA;IACX,IAAI,CAAC1D,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,SAAS,GAAG,EAAE;EACrB;EAEQoD,eAAeA,CAAA;IACrB,IAAI,CAACrD,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACC,SAAS,CAAC8B,OAAO,CAAC4B,QAAQ,IAAG;MAChCA,QAAQ,CAACvC,QAAQ,GAAG,KAAK;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAAClB,uBAAuB,CAAC0D,KAAK,EAAE;EACtC;EAEA;EACAC,IAAIA,CAAA;IACF,IAAI,CAACjE,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACK,SAAS,CAAC0B,MAAM,KAAK,CAAC,EAAE;MAC/B;MACA,IAAI,CAACtB,oBAAoB,EAAE;IAC7B,CAAC,MAAM;MACL;MACA,IAAI,CAACL,WAAW,GAAG,CAAC;MACpB,IAAI,CAACC,SAAS,CAAC8B,OAAO,CAAC4B,QAAQ,IAAG;QAChCA,QAAQ,CAACvC,QAAQ,GAAG,KAAK;MAC3B,CAAC,CAAC;MACF,IAAI,CAAClB,uBAAuB,CAAC0D,KAAK,EAAE;IACtC;EACF;CACD;AAlMUE,UAAA,EAARzE,KAAK,EAAE,C,gEAA4B;AAC3ByE,UAAA,EAARzE,KAAK,EAAE,C,kEAA0B;AACxByE,UAAA,EAATxE,MAAM,EAAE,C,sEAA2D;AAC1DwE,UAAA,EAATxE,MAAM,EAAE,C,6DAAmC;AAJjCG,8BAA8B,GAAAqE,UAAA,EAV1C3E,SAAS,CAAC;EACT4E,QAAQ,EAAE,6BAA6B;EACvCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP1E,YAAY,EACZC,WAAW,CACZ;EACD0E,WAAW,EAAE,0CAA0C;EACvDC,SAAS,EAAE,CAAC,0CAA0C;CACvD,CAAC,C,EACW1E,8BAA8B,CAmM1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}