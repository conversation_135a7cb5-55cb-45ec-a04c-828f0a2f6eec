{"ast": null, "code": "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument :\n  // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}