{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Korean [ko]\n//! author : Kyungwook, Park : https://github.com/kyungw00k\n//! author : <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ko = moment.defineLocale('ko', {\n    months: '1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월'.split('_'),\n    monthsShort: '1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월'.split('_'),\n    weekdays: '일요일_월요일_화요일_수요일_목요일_금요일_토요일'.split('_'),\n    weekdaysShort: '일_월_화_수_목_금_토'.split('_'),\n    weekdaysMin: '일_월_화_수_목_금_토'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm',\n      LTS: 'A h:mm:ss',\n      L: 'YYYY.MM.DD.',\n      LL: 'YYYY년 MMMM D일',\n      LLL: 'YYYY년 MMMM D일 A h:mm',\n      LLLL: 'YYYY년 MMMM D일 dddd A h:mm',\n      l: 'YYYY.MM.DD.',\n      ll: 'YYYY년 MMMM D일',\n      lll: 'YYYY년 MMMM D일 A h:mm',\n      llll: 'YYYY년 MMMM D일 dddd A h:mm'\n    },\n    calendar: {\n      sameDay: '오늘 LT',\n      nextDay: '내일 LT',\n      nextWeek: 'dddd LT',\n      lastDay: '어제 LT',\n      lastWeek: '지난주 dddd LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s 후',\n      past: '%s 전',\n      s: '몇 초',\n      ss: '%d초',\n      m: '1분',\n      mm: '%d분',\n      h: '한 시간',\n      hh: '%d시간',\n      d: '하루',\n      dd: '%d일',\n      M: '한 달',\n      MM: '%d달',\n      y: '일 년',\n      yy: '%d년'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(일|월|주)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'DDD':\n          return number + '일';\n        case 'M':\n          return number + '월';\n        case 'w':\n        case 'W':\n          return number + '주';\n        default:\n          return number;\n      }\n    },\n    meridiemParse: /오전|오후/,\n    isPM: function (token) {\n      return token === '오후';\n    },\n    meridiem: function (hour, minute, isUpper) {\n      return hour < 12 ? '오전' : '오후';\n    }\n  });\n  return ko;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}