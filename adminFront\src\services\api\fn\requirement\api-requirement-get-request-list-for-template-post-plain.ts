/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetListRequirementRequest } from '../../models/get-list-requirement-request';
import { GetRequirementListResponseBase } from '../../models/get-requirement-list-response-base';

export interface ApiRequirementGetRequestListForTemplatePost$Plain$Params {
      body?: GetListRequirementRequest
}

export function apiRequirementGetRequestListForTemplatePost$Plain(http: HttpClient, rootUrl: string, params?: ApiRequirementGetRequestListForTemplatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRequirementListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiRequirementGetRequestListForTemplatePost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetRequirementListResponseBase>;
    })
  );
}

apiRequirementGetRequestListForTemplatePost$Plain.PATH = '/api/Requirement/GetRequestListForTemplate';
