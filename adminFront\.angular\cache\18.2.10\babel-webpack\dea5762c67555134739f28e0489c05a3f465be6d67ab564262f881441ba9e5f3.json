{"ast": null, "code": "import { SharedModule } from '../../../components/shared.module';\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { LabelInOptionsPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { BaseComponent } from '../../../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/@core/service/review.service\";\nimport * as i7 from \"src/app/shared/services/utility.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/directives/label.directive\";\nimport * as i13 from \"../../../components/file-upload/file-upload.component\";\nimport * as i14 from \"../../../../@theme/pipes/date-format.pipe\";\nimport * as i15 from \"../../../../shared/components/household-binding/household-binding.component\";\nconst _c0 = [\"fileInput\"];\nfunction ReviewDocumentManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_button_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_button_39_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onSearch());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const dialog_r9 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r6.openModel(dialog_r9));\n    });\n    i0.ɵɵtext(1, \" \\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_tr_60_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_tr_60_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      const dialog_r9 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r6.openModel(dialog_r9, item_r11));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_tr_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"dateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 34);\n    i0.ɵɵtemplate(18, ReviewDocumentManagementComponent_tr_60_button_18_Template, 2, 0, \"button\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 7, item_r11.CReviewType, ctx_r6.reviewTypeOptions), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CReviewName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CHouse, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(10, 10, item_r11.CStatus, ctx_r6.statusOptions), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(13, 13, item_r11.CExamineStatus, ctx_r6.examineStatusOptionsQuery), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CActionDate ? i0.ɵɵpipeBind1(16, 16, item_r11.CActionDate) : \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isUpdate);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_nb_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r13);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r13.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r14);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r14.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_32_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"dateFormatHour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r16 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, row_r16.CCreateDt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(row_r16.CCreator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.getActionName(row_r16.CAction));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r16.CExamineNote);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 41)(2, \"label\", 55);\n    i0.ɵɵtext(3, \"\\u5BE9\\u6838\\u6B77\\u7A0B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"table\", 56)(5, \"thead\")(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"\\u6642\\u9593\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"\\u4F7F\\u7528\\u8005\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"\\u52D5\\u4F5C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"tbody\");\n    i0.ɵɵtemplate(16, ReviewDocumentManagementComponent_ng_template_63_div_32_tr_16_Template, 10, 6, \"tr\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.selectedReview.tblExamineLogs);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 37)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 38)(4, \"div\", 5)(5, \"label\", 39);\n    i0.ɵɵtext(6, \"\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"nb-select\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.selectedReviewType, $event) || (ctx_r6.selectedReview.selectedReviewType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(8, ReviewDocumentManagementComponent_ng_template_63_nb_option_8_Template, 2, 2, \"nb-option\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 41)(10, \"app-file-upload\", 42);\n    i0.ɵɵlistener(\"fileSelected\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_file_upload_fileSelected_10_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onFileUploaded($event));\n    })(\"fileCleared\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_file_upload_fileCleared_10_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onFileCleared());\n    })(\"nameAutoFilled\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_file_upload_nameAutoFilled_10_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onNameAutoFilled($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 41)(12, \"div\", 5)(13, \"label\", 43);\n    i0.ɵɵtext(14, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"nb-select\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.seletedStatus, $event) || (ctx_r6.selectedReview.seletedStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(16, ReviewDocumentManagementComponent_ng_template_63_nb_option_16_Template, 2, 2, \"nb-option\", 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 41)(18, \"div\", 5)(19, \"label\", 45);\n    i0.ɵɵtext(20, \" \\u5BE9\\u6838\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"textarea\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_textarea_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.CExamineNote, $event) || (ctx_r6.selectedReview.CExamineNote = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 47)(23, \"label\", 48);\n    i0.ɵɵtext(24, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 49)(26, \"app-household-binding\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_household_binding_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedHouseholds, $event) || (ctx_r6.selectedHouseholds = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"houseIdChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_household_binding_houseIdChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onHouseholdIdChange($event));\n    })(\"selectionChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_household_binding_selectionChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onHouseholdSelectionChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 27)(28, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_Template_button_click_28_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onClose(ref_r15));\n    });\n    i0.ɵɵtext(29, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_Template_button_click_30_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onSaveReview(ref_r15));\n    });\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, ReviewDocumentManagementComponent_ng_template_63_div_32_Template, 17, 1, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.isNew ? \"\\u65B0\\u589E\\u5BE9\\u95B1\\u6587\\u4EF6\" : \"\\u7DE8\\u8F2F\\u5BE9\\u95B1\\u6587\\u4EF6\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.selectedReviewType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.reviewTypeOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"config\", ctx_r6.fileUploadConfigWithState)(\"currentFileName\", ctx_r6.fileName)(\"currentFileUrl\", ctx_r6.selectedReview.CFileUrl || null);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.seletedStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.statusOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.CExamineNote);\n    i0.ɵɵproperty(\"rows\", 4)(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedHouseholds);\n    i0.ɵɵproperty(\"buildCaseId\", ctx_r6.searchQuery.selectedBuildCase == null ? null : ctx_r6.searchQuery.selectedBuildCase.value)(\"buildingData\", ctx_r6.buildingData)(\"maxSelections\", 50)(\"disabled\", ctx_r6.latestAction === 1)(\"allowBatchSelect\", true)(\"useHouseNameMode\", false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.isNew ? \"\\u65B0\\u589E\\u4E26\\u9001\\u51FA\\u5BE9\\u6838\" : \"\\u9001\\u51FA\\u5BE9\\u6838\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isNew);\n  }\n}\nexport let ReviewDocumentManagementComponent = /*#__PURE__*/(() => {\n  class ReviewDocumentManagementComponent extends BaseComponent {\n    constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, _reviewService, reviewService, utilityService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._houseService = _houseService;\n      this._buildCaseService = _buildCaseService;\n      this._reviewService = _reviewService;\n      this.reviewService = reviewService;\n      this.utilityService = utilityService;\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      this.reviewTypeOptions = [{\n        value: 1,\n        label: '標準圖' //standard drawing\n      }, {\n        value: 2,\n        label: '設備圖' //equipment drawing\n      }];\n      this.reviewTypeOptionsQuery = [{\n        value: -1,\n        label: '全部'\n      }, {\n        value: 1,\n        label: '標準圖' //standard drawing\n      }, {\n        value: 2,\n        label: '設備圖' //equipment drawing\n      }];\n      this.examineStatusOptions = [{\n        value: -1,\n        label: '待審核' //Pending review\n      }, {\n        value: 1,\n        label: '已通過' //passed\n      }, {\n        value: 2,\n        label: '已駁回' //rejected\n      }];\n      this.examineStatusOptionsQuery = [{\n        value: -1,\n        label: '全部'\n      }, {\n        value: 0,\n        label: '待審核' //Pending review\n      }, {\n        value: 1,\n        label: '已通過' //passed\n      }, {\n        value: 2,\n        label: '已駁回' //rejected\n      }];\n      this.statusOptions = [{\n        value: 1,\n        //0停用 1啟用 9刪除\n        label: '啟用' //enable\n      }, {\n        value: 2,\n        label: '停用' //Disable\n      }];\n      this.statusOptionsQuery = [{\n        value: -1,\n        label: '全部'\n      }, {\n        value: 1,\n        //0停用 1啟用 9刪除\n        label: '啟用' //enable\n      }, {\n        value: 2,\n        label: '停用' //Disable\n      }];\n      this.buildCaseOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      // 新增：新戶別選擇器相關屬性\n      this.buildingData = {}; // 存放建築物戶別資料\n      this.selectedHouseholds = []; // 選中的戶別ID (使用 houseId)\n      this.fileName = null;\n      this.imageUrl = undefined;\n      this.fileUploadConfig = {\n        acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', '.dwg', '.dxf'],\n        acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\n        acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\n        label: '上傳檔案',\n        helpText: '*請上傳PDF格式或CAD檔案（.dwg, .dxf）',\n        required: true,\n        disabled: false,\n        autoFillName: true,\n        buttonText: '上傳',\n        buttonIcon: 'fa-solid fa-cloud-arrow-up',\n        maxFileSize: 10,\n        multiple: false,\n        showPreview: false\n      };\n      this.latestAction = 0;\n      this.isNew = true;\n    }\n    ngOnInit() {\n      if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\n        let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\n        this.searchQuery = {\n          selectedBuildCase: null,\n          selectedReviewType: this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value) ? this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value) : this.reviewTypeOptionsQuery[0],\n          selectedExamineStatus: this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value) ? this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value) : this.examineStatusOptionsQuery[0],\n          seletedStatus: this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value) ? this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value) : this.statusOptionsQuery[0],\n          CReviewName: previous_search.CReviewName\n        };\n      } else {\n        this.searchQuery = {\n          selectedBuildCase: null,\n          selectedReviewType: this.reviewTypeOptionsQuery[0],\n          selectedExamineStatus: this.examineStatusOptionsQuery[0],\n          seletedStatus: this.statusOptionsQuery[0],\n          CReviewName: ''\n        };\n      }\n      this.getUserBuildCase();\n    }\n    // 文件上傳配置\n    get fileUploadConfigWithState() {\n      return {\n        ...this.fileUploadConfig,\n        disabled: this.latestAction === 1\n      };\n    }\n    clearImage() {\n      if (this.imageUrl) {\n        this.imageUrl = null;\n        this.fileName = null;\n        if (this.fileInput) {\n          this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n        }\n      }\n    }\n    // 處理共用元件的文件選擇事件\n    onFileUploaded(result) {\n      this.fileName = result.fileName;\n      this.imageUrl = {\n        CName: result.CName,\n        CFile: result.CFile,\n        Cimg: result.Cimg,\n        CFileUpload: result.CFileUpload,\n        CFileType: result.CFileType\n      };\n    }\n    // 處理共用元件的文件清除事件\n    onFileCleared() {\n      this.fileName = null;\n      this.imageUrl = null;\n    }\n    // 處理共用元件的自動填入名稱事件\n    onNameAutoFilled(fileName) {\n      if (!this.selectedReview.CReviewName) {\n        this.selectedReview.CReviewName = fileName;\n      }\n    }\n    // 更新 disabled 狀態\n    get isFileUploadDisabled() {\n      return this.latestAction === 1;\n    }\n    getItemByValue(value, options) {\n      for (const item of options) {\n        if (item.value === value) {\n          return item;\n        }\n      }\n      return null;\n    }\n    getReviewById(item, ref) {\n      this._reviewService.apiReviewGetReviewByIdPost$Json({\n        body: item.CReviewId\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          const data = res.Entries;\n          this.selectedReview = {\n            CBuildCaseId: data.tblReview?.CBuildCaseId,\n            CReviewId: data.tblReview?.CReviewId,\n            CReviewType: data.tblReview?.CReviewType,\n            CReviewName: data.tblReview?.CReviewName ? data.tblReview?.CReviewName : '',\n            CSort: data.tblReview?.CSort,\n            CStatus: data.tblReview?.CStatus,\n            CFileUrl: data.tblReview?.CFileUrl,\n            CExamineNote: data?.CExamineNote ? data?.CExamineNote : '',\n            seletedStatus: data.tblReview?.CStatus ? this.getItemByValue(data.tblReview?.CStatus, this.statusOptions) : this.statusOptions[0],\n            selectedReviewType: data.tblReview?.CReviewType ? this.getItemByValue(data.tblReview?.CReviewType, this.reviewTypeOptions) : this.reviewTypeOptions[0],\n            tblExamineLogs: data.tblExamineLogs,\n            reviewHouseHolds: data?.reviewHouseHolds?.filter(i => i.CIsSelect),\n            tblReview: data.tblReview\n          };\n          if (data && data?.tblExamineLogs && data?.tblExamineLogs.length) {\n            if (data?.tblExamineLogs.length === 0) return undefined;\n            this.latestAction = data?.tblExamineLogs[0].CAction;\n            let latestDate = data?.tblExamineLogs[0].CCreateDt ? new Date(data?.tblExamineLogs[0].CCreateDt) : '';\n            for (let i = 1; i < data.tblExamineLogs.length; i++) {\n              if (data.tblExamineLogs[i].CCreateDt) {\n                const currentDate = new Date(data.tblExamineLogs[i].CCreateDt);\n                if (currentDate > latestDate) {\n                  latestDate = currentDate;\n                  this.latestAction = data?.tblExamineLogs[i].CAction;\n                }\n              }\n            }\n          }\n          // 先準備選中的戶別ID資料（API 已更新，ReviewHouseHold 現在包含 CHouseID 為 number 類型）\n          let selectedHouseholdIds = [];\n          if (data?.reviewHouseHolds) {\n            selectedHouseholdIds = data.reviewHouseHolds.filter(item => item.CIsSelect && item.CHouseID).map(item => item.CHouseID);\n          }\n          // 載入建築物資料，並在載入完成後設置選中的戶別\n          this.loadBuildingDataFromAPI(selectedHouseholdIds, ref);\n        }\n      });\n    }\n    onSaveReview(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      // 使用新戶別選擇器的結果\n      let houseReviews = [];\n      if (this.selectedHouseholds && this.selectedHouseholds.length > 0) {\n        houseReviews = this.convertSelectedHouseholdsToHouseReview();\n      }\n      this.saveReviewPostRes = {\n        CBuildCaseId: this.searchQuery.selectedBuildCase.value,\n        CReviewId: this.selectedReview.CReviewId,\n        CReviewType: this.selectedReview.selectedReviewType.value,\n        CReviewName: this.selectedReview.CReviewName,\n        CSort: this.selectedReview?.CSort,\n        CStatus: this.selectedReview.seletedStatus.value,\n        CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\n        CExamineNote: this.selectedReview.CExamineNote,\n        HouseReviews: houseReviews\n      };\n      this.reviewService.SaveReview(this.saveReviewPostRes).subscribe(res => {\n        if (res && res.body && res.body.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.clearImage();\n          this.getReviewList();\n          ref.close();\n        } else {\n          this.message.showErrorMSG(res && res.body && res.body.Message);\n        }\n      });\n    }\n    onSearch() {\n      let previous_search = {\n        CReviewName: this.searchQuery.CReviewName,\n        CSelectedBuildCase: this.searchQuery.selectedBuildCase,\n        CSeletedStatus: this.searchQuery.seletedStatus,\n        CReviewType: this.searchQuery.selectedReviewType,\n        CExamineStatus: this.searchQuery.selectedExamineStatus\n      };\n      LocalStorageService.AddSessionStorage(STORAGE_KEY.REVIEW_SEARCH, JSON.stringify(previous_search));\n      this.getReviewList();\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getReviewList();\n    }\n    openPdfInNewTab(CFileUrl) {\n      if (CFileUrl) {\n        this.utilityService.openFileNewTab(CFileUrl);\n      }\n    }\n    getReviewList() {\n      return this._reviewService.apiReviewGetReviewListPost$Json({\n        body: {\n          PageIndex: this.pageIndex,\n          PageSize: this.pageSize,\n          CReviewName: this.searchQuery.CReviewName,\n          CBuildCaseID: this.searchQuery.selectedBuildCase.value,\n          CStatus: this.searchQuery.seletedStatus.value,\n          CReviewType: this.searchQuery.selectedReviewType.value,\n          CExamineStatus: this.searchQuery.selectedExamineStatus.value\n        }\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.reviewList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      })).subscribe();\n    }\n    onSelectionChangeBuildCase() {\n      if (this.searchQuery.selectedBuildCase.value) {\n        this.getReviewList();\n        // 載入新戶別選擇器的建築物資料\n        this.loadBuildingDataFromAPI();\n      }\n    }\n    getUserBuildCase() {\n      this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n        body: {}\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.userBuildCaseOptions = res.Entries.map(res => {\n            return {\n              label: res.CBuildCaseName,\n              value: res.cID\n            };\n          });\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\n            if (previous_search.CSelectedBuildCase != null && previous_search.CSelectedBuildCase != undefined) {\n              let index = this.userBuildCaseOptions.findIndex(x => x.value == previous_search.CSelectedBuildCase.value);\n              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[index];\n            } else {\n              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0];\n            }\n          } else {\n            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0];\n          }\n          if (this.searchQuery.selectedBuildCase.value) {\n            this.getReviewList();\n            // 載入新戶別選擇器的建築物資料\n            this.loadBuildingDataFromAPI();\n          }\n        }\n      })).subscribe();\n    }\n    openModel(ref, item) {\n      this.latestAction = 0;\n      this.isNew = true;\n      this.clearImage();\n      // 初始化新戶別選擇器\n      this.selectedHouseholds = [];\n      this.selectedReview = {\n        selectedReviewType: this.reviewTypeOptions[0],\n        seletedStatus: this.statusOptions[0],\n        selectedExamineStatus: this.examineStatusOptions[0],\n        CReviewName: '',\n        CSort: 0,\n        CFileUrl: '',\n        CExamineNote: '',\n        CIsSelectAll: false\n      };\n      if (item) {\n        this.isNew = false;\n        this.getReviewById(item, ref);\n      } else {\n        this.isNew = true;\n        // 載入新戶別選擇器的建築物資料，並在載入完成後開啟對話框\n        this.loadBuildingDataFromAPI(undefined, ref);\n      }\n    }\n    formatDate(CChangeDate) {\n      if (CChangeDate) {\n        return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n      }\n      return '';\n    }\n    onSubmit(ref) {}\n    onClose(ref) {\n      ref.close();\n    }\n    validation() {\n      this.valid.clear();\n      if (this.isNew && !this.imageUrl) {\n        this.valid.addErrorMessage(`前台圖片`);\n      }\n      // 驗證戶別選擇\n      if (!this.selectedHouseholds || this.selectedHouseholds.length === 0) {\n        this.valid.addErrorMessage('[適用戶別]請至少選擇一個戶別');\n      }\n      this.valid.required('[送審說明]', this.selectedReview.CExamineNote);\n    }\n    getActionName(actionID) {\n      let textR = \"\";\n      if (actionID != undefined) {\n        switch (actionID) {\n          case 1:\n            textR = \"傳送\";\n            break;\n          case 2:\n            textR = \"通過\";\n            break;\n          case 3:\n            textR = \"駁回\";\n            break;\n          default:\n            break;\n        }\n      }\n      return textR;\n    }\n    // 新增：載入建築物戶別資料 (使用 GetDropDown API)\n    loadBuildingDataFromAPI(selectedHouseholdIds, dialogRef) {\n      if (!this.searchQuery.selectedBuildCase?.value) {\n        console.warn('沒有選擇建案，無法載入建築物資料');\n        return;\n      }\n      console.log('開始載入建築物資料，建案ID:', this.searchQuery.selectedBuildCase.value);\n      this._houseService.apiHouseGetDropDownPost$Json({\n        buildCaseId: this.searchQuery.selectedBuildCase.value\n      }).subscribe({\n        next: response => {\n          console.log('API 回應:', response);\n          if (response && response.Entries) {\n            this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n            console.log('轉換後的建築物資料:', this.buildingData);\n            // 在建築物資料載入完成後，設置選中的戶別ID\n            if (selectedHouseholdIds && selectedHouseholdIds.length > 0) {\n              console.log('設置選中的戶別ID:', selectedHouseholdIds);\n              this.selectedHouseholds = [...selectedHouseholdIds];\n              // 觸發 ngModel 更新以確保戶別選擇器顯示選中的戶別\n              setTimeout(() => {\n                this.selectedHouseholds = [...selectedHouseholdIds];\n              }, 50);\n            }\n            // 開啟對話框\n            if (dialogRef) {\n              setTimeout(() => {\n                this.dialogService.open(dialogRef);\n              }, 100);\n            }\n          } else {\n            console.warn('API 回應無資料');\n            this.buildingData = {};\n            if (dialogRef) {\n              this.dialogService.open(dialogRef);\n            }\n          }\n        },\n        error: error => {\n          console.error('載入建築物戶別資料失敗:', error);\n          this.buildingData = {};\n          if (dialogRef) {\n            this.dialogService.open(dialogRef);\n          }\n        }\n      });\n    }\n    // 新增：將 API 回應轉換為建築物資料格式\n    convertApiResponseToBuildingData(entries) {\n      const buildingData = {};\n      Object.entries(entries).forEach(([building, houses]) => {\n        if (Array.isArray(houses) && houses.length > 0) {\n          buildingData[building] = houses.map(house => ({\n            houseName: house.HouseName || '',\n            building: building,\n            floor: house.Floor ? `${house.Floor}F` : '',\n            houseId: house.HouseId,\n            houseType: house.HouseType,\n            isSelected: false,\n            isDisabled: false\n          }));\n        }\n      });\n      console.log('轉換後的 buildingData:', buildingData);\n      return buildingData;\n    }\n    // 新增：處理戶別選擇變更\n    onHouseholdSelectionChange(selectedItems) {\n      // 更新 selectedHouseholds (使用 houseId)\n      this.selectedHouseholds = selectedItems.map(item => item.houseId).filter(id => id !== undefined);\n    }\n    // 新增：處理戶別ID變更事件\n    onHouseholdIdChange(selectedIds) {\n      this.selectedHouseholds = selectedIds;\n    }\n    // 新增：從新戶別選擇器的選擇結果轉換為原有格式\n    convertSelectedHouseholdsToHouseReview() {\n      const result = [];\n      // 從 buildingData 中找到對應的戶別資訊\n      if (this.buildingData && this.selectedHouseholds.length > 0) {\n        Object.values(this.buildingData).forEach(houses => {\n          houses.forEach(house => {\n            if (house.houseId && this.selectedHouseholds.includes(house.houseId)) {\n              result.push({\n                CHouseID: house.houseId,\n                CIsSelect: true,\n                CFloor: house.floor ? parseInt(house.floor.replace('F', '')) : undefined,\n                CHouseHold: house.houseName\n              });\n            }\n          });\n        });\n      }\n      return result;\n    }\n    static {\n      this.ɵfac = function ReviewDocumentManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ReviewDocumentManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.ReviewService), i0.ɵɵdirectiveInject(i6.ReviewServiceCustom), i0.ɵɵdirectiveInject(i7.UtilityService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReviewDocumentManagementComponent,\n        selectors: [[\"ngx-review-document-management\"]],\n        viewQuery: function ReviewDocumentManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 65,\n        vars: 15,\n        consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cReviewType\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u985E\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cReviewName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"cReviewName\", \"nbInput\", \"\", 1, \"w-full\", \"!max-w-[290px]\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CExamineStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-end\", \"justify-end\", \"w-full\"], [\"class\", \"btn btn-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [\"for\", \"ReviewType\", 1, \"required-field\", \"label\", \"col-3\"], [\"placeholder\", \"\\u985E\\u578B\", 1, \"col-9\", \"px-0\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [3, \"fileSelected\", \"fileCleared\", \"nameAutoFilled\", \"config\", \"currentFileName\", \"currentFileUrl\"], [\"for\", \"CExamineStatus\", 1, \"label\", \"col-3\", \"required-field\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", \"px-0\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [\"for\", \"cExamineNote\", 1, \"label\", \"col-3\", \"required-field\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"col-9\", \"!max-w-[320px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"mb-0\"], [\"for\", \"houseList2D\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-3\", 2, \"min-width\", \"75px\"], [1, \"mt-1\", \"mb-3\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u9069\\u7528\\u6236\\u5225\", 3, \"ngModelChange\", \"houseIdChange\", \"selectionChange\", \"ngModel\", \"buildCaseId\", \"buildingData\", \"maxSelections\", \"disabled\", \"allowBatchSelect\", \"useHouseNameMode\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", \"min-w-[90px]\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", \"min-w-[90px]\", 3, \"click\", \"disabled\"], [\"class\", \"w-full\", 4, \"ngIf\"], [1, \"w-full\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"mr-3\", 2, \"min-width\", \"75px\"], [1, \"table\", \"table-bordered\", 2, \"background-color\", \"#f3f3f3\"]],\n        template: function ReviewDocumentManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 2);\n            i0.ɵɵtext(5, \"\\u53EF\\u4E0A\\u50B3\\u8981\\u63D0\\u4F9B\\u5BA2\\u6236\\u6A19\\u6E96\\u5716\\u8AAA\\uFF0C\\u6587\\u4EF6\\u5167\\u578B\\u5206\\u70BA\\u6A19\\u6E96\\u5716\\u53CA\\u8A2D\\u5099\\uFF0C\\u4E26\\u8A2D\\u5B9A\\u8A72\\u6A94\\u6848\\u9069\\u7528\\u7684\\u6236\\u5225\\u3002\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 5)(9, \"label\", 6);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedBuildCase, $event) || (ctx.searchQuery.selectedBuildCase = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function ReviewDocumentManagementComponent_Template_nb_select_selectedChange_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n            });\n            i0.ɵɵtemplate(12, ReviewDocumentManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 4)(14, \"div\", 5)(15, \"label\", 9);\n            i0.ɵɵtext(16, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"nb-select\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedReviewType, $event) || (ctx.searchQuery.selectedReviewType = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(18, ReviewDocumentManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"div\", 4)(20, \"div\", 5)(21, \"label\", 11);\n            i0.ɵɵtext(22, \" \\u540D\\u7A31 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"div\", 12)(24, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_input_ngModelChange_24_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CReviewName, $event) || (ctx.searchQuery.CReviewName = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(25, \"div\", 4)(26, \"div\", 5)(27, \"label\", 14);\n            i0.ɵɵtext(28, \" \\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"nb-select\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_29_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.seletedStatus, $event) || (ctx.searchQuery.seletedStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(30, ReviewDocumentManagementComponent_nb_option_30_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"div\", 4)(32, \"div\", 5)(33, \"label\", 16);\n            i0.ɵɵtext(34, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"nb-select\", 17);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_35_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedExamineStatus, $event) || (ctx.searchQuery.selectedExamineStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(36, ReviewDocumentManagementComponent_nb_option_36_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"div\", 4)(38, \"div\", 18);\n            i0.ɵɵtemplate(39, ReviewDocumentManagementComponent_button_39_Template, 3, 0, \"button\", 19)(40, ReviewDocumentManagementComponent_button_40_Template, 3, 0, \"button\", 20);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(41, \"div\", 21)(42, \"table\", 22)(43, \"thead\")(44, \"tr\", 23)(45, \"th\", 24);\n            i0.ɵɵtext(46, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"th\", 24);\n            i0.ɵɵtext(48, \"\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"th\", 25);\n            i0.ɵɵtext(50, \"\\u9069\\u7528\\u6236\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"th\", 24);\n            i0.ɵɵtext(52, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"th\", 24);\n            i0.ɵɵtext(54, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"th\", 25);\n            i0.ɵɵtext(56, \"\\u5BE9\\u6838\\u65E5\\u671F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"th\", 24);\n            i0.ɵɵtext(58, \"\\u52D5\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(59, \"tbody\");\n            i0.ɵɵtemplate(60, ReviewDocumentManagementComponent_tr_60_Template, 19, 18, \"tr\", 26);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(61, \"nb-card-footer\", 27)(62, \"ngb-pagination\", 28);\n            i0.ɵɵtwoWayListener(\"pageChange\", function ReviewDocumentManagementComponent_Template_ngb_pagination_pageChange_62_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"pageChange\", function ReviewDocumentManagementComponent_Template_ngb_pagination_pageChange_62_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(63, ReviewDocumentManagementComponent_ng_template_63_Template, 33, 23, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedBuildCase);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedReviewType);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.reviewTypeOptionsQuery);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CReviewName);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.seletedStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.statusOptionsQuery);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedExamineStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.examineStatusOptionsQuery);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(20);\n            i0.ɵɵproperty(\"ngForOf\", ctx.reviewList);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i8.NgForOf, i8.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i10.NgbPagination, i11.BreadcrumbComponent, i12.BaseLabelDirective, i13.FileUploadComponent, i14.DateFormatPipe, AppSharedModule, i15.HouseholdBindingComponent, NbDatepickerModule, NbDateFnsDateModule, LabelInOptionsPipe, DateFormatHourPipe]\n      });\n    }\n  }\n  return ReviewDocumentManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}