{"ast": null, "code": "import { NbIconModule, NbSelectModule, NbOptionModule, NbActionsModule, NbUserModule, NbButtonModule } from '@nebular/theme';\nimport { map, takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { decodeJwtPayload } from '@nebular/auth';\nimport { environment } from '../../../../environments/environment';\nimport { NbSecurityModule } from '@nebular/security';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"../../../@core/data/users\";\nimport * as i3 from \"../../../@core/utils\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"@nebular/security\";\nconst _c0 = () => [\"view\", \"user\"];\nfunction HeaderComponent_nb_action_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-action\", 8);\n    i0.ɵɵelement(1, \"nb-user\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"onlyPicture\", ctx_r0.userPictureOnly)(\"name\", ctx_r0.userName);\n  }\n}\nexport let HeaderComponent = /*#__PURE__*/(() => {\n  class HeaderComponent {\n    constructor(sidebarService, menuService, themeService, userService, layoutService, breakpointService, router,\n    // private service: BusinessUnitService,\n    message) {\n      this.sidebarService = sidebarService;\n      this.menuService = menuService;\n      this.themeService = themeService;\n      this.userService = userService;\n      this.layoutService = layoutService;\n      this.breakpointService = breakpointService;\n      this.router = router;\n      this.message = message;\n      this.destroy$ = new Subject();\n      this.userPictureOnly = false;\n      this.ownerBuId = `${environment.OWNER_BUID}`;\n      this.request = {};\n      this.currentTheme = 'default';\n    }\n    ngOnInit() {\n      this.currentTheme = this.themeService.currentTheme;\n      this.userService.getUsers().pipe(takeUntil(this.destroy$)).subscribe(users => this.user = users.nick);\n      const {\n        xl\n      } = this.breakpointService.getBreakpointsMap();\n      this.themeService.onMediaQueryChange().pipe(map(([, currentBreakpoint]) => currentBreakpoint.width < xl), takeUntil(this.destroy$)).subscribe(isLessThanXl => this.userPictureOnly = isLessThanXl);\n      this.themeService.onThemeChange().pipe(map(({\n        name\n      }) => name), takeUntil(this.destroy$)).subscribe(themeName => this.currentTheme = themeName);\n      const jwt = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN));\n      this.userName = jwt.UserName;\n      this.buId = jwt.BuId;\n      this.selectBuId = LocalStorageService.GetLocalStorage(STORAGE_KEY.BUID);\n      // if (this.buId === this.ownerBuId) {\n      //   this.service.getBusinessUnitList(this.request).subscribe(res => {\n      //     if (res.StatusCode === EnumStatusCode.Success) {\n      //       this.businessUnits = res.Entries!;\n      //     }\n      //   });\n      // }\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    changeBuId(buId) {\n      LocalStorageService.AddLocalStorage(STORAGE_KEY.BUID, buId);\n      this.message.showSucessMSG('執行成功');\n      this.router.navigateByUrl('home');\n    }\n    toggleSidebar() {\n      this.sidebarService.toggle(true, 'menu-sidebar');\n      this.layoutService.changeLayoutSize();\n      return false;\n    }\n    navigateHome() {\n      this.router.navigateByUrl('home');\n      return false;\n    }\n    logOut() {\n      this.router.navigateByUrl('logout');\n    }\n    static {\n      this.ɵfac = function HeaderComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HeaderComponent)(i0.ɵɵdirectiveInject(i1.NbSidebarService), i0.ɵɵdirectiveInject(i1.NbMenuService), i0.ɵɵdirectiveInject(i1.NbThemeService), i0.ɵɵdirectiveInject(i2.UserData), i0.ɵɵdirectiveInject(i3.LayoutService), i0.ɵɵdirectiveInject(i1.NbMediaBreakpointsService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.MessageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HeaderComponent,\n        selectors: [[\"ngx-header\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 12,\n        vars: 2,\n        consts: [[1, \"header-container\"], [1, \"logo-container\"], [\"href\", \"#\", 1, \"sidebar-toggle\", 3, \"click\"], [\"icon\", \"menu-2-outline\"], [\"href\", \"#\", 1, \"logo\", 3, \"click\"], [\"size\", \"small\"], [\"class\", \"user-action\", 4, \"nbIsGranted\"], [\"nbButton\", \"\", \"status\", \"danger\", 3, \"click\"], [1, \"user-action\"], [3, \"onlyPicture\", \"name\"]],\n        template: function HeaderComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"a\", 2);\n            i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_2_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelement(3, \"nb-icon\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"a\", 4);\n            i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_4_listener() {\n              return ctx.navigateHome();\n            });\n            i0.ɵɵtext(5, \" ADMIN \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(6, \"div\", 0)(7, \"nb-actions\", 5);\n            i0.ɵɵtemplate(8, HeaderComponent_nb_action_8_Template, 2, 2, \"nb-action\", 6);\n            i0.ɵɵelementStart(9, \"nb-action\")(10, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_10_listener() {\n              return ctx.logOut();\n            });\n            i0.ɵɵtext(11, \"\\u767B\\u51FA\");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"nbIsGranted\", i0.ɵɵpureFunction0(1, _c0));\n          }\n        },\n        dependencies: [NbIconModule, i1.NbIconComponent, NbSelectModule, NbOptionModule, NbActionsModule, i1.NbActionComponent, i1.NbActionsComponent, NbSecurityModule, i6.NbIsGrantedDirective, NbUserModule, i1.NbUserComponent, NbButtonModule, i1.NbButtonComponent],\n        styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n[_nghost-%COMP%]{display:flex;justify-content:space-between;width:100%}[_nghost-%COMP%]   .logo-container[_ngcontent-%COMP%]{display:flex;align-items:center;width:calc(var(--sidebar-width) - var(--header-padding))}[_nghost-%COMP%]   nb-action[_ngcontent-%COMP%]{height:auto;display:flex;align-content:center}[_nghost-%COMP%]   nb-user[_ngcontent-%COMP%]{cursor:pointer}[_nghost-%COMP%]     nb-search button{padding:0!important}[_nghost-%COMP%]   .header-container[_ngcontent-%COMP%]{display:flex;align-items:center;width:auto}[_nghost-%COMP%]   .header-container[_ngcontent-%COMP%]   .sidebar-toggle[_ngcontent-%COMP%]{text-decoration:none;color:var(--text-hint-color)}[dir=ltr]   [_nghost-%COMP%]   .header-container[_ngcontent-%COMP%]   .sidebar-toggle[_ngcontent-%COMP%]{padding-right:1.25rem}[dir=rtl]   [_nghost-%COMP%]   .header-container[_ngcontent-%COMP%]   .sidebar-toggle[_ngcontent-%COMP%]{padding-left:1.25rem}[_nghost-%COMP%]   .header-container[_ngcontent-%COMP%]   .sidebar-toggle[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%]{font-size:1.75rem}[_nghost-%COMP%]   .header-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{padding:0 1.25rem;font-size:1.75rem;white-space:nowrap;text-decoration:none}[dir=ltr]   [_nghost-%COMP%]   .header-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{border-left:1px solid var(--divider-color)}[dir=rtl]   [_nghost-%COMP%]   .header-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{border-right:1px solid var(--divider-color)}@media (max-width: 767.98px){[_nghost-%COMP%]   .control-item[_ngcontent-%COMP%]{display:none}[_nghost-%COMP%]   .user-action[_ngcontent-%COMP%]{border:none;padding:0}}@media (max-width: 575.98px){[_nghost-%COMP%]   nb-select[_ngcontent-%COMP%]{display:none}}\"]\n      });\n    }\n  }\n  return HeaderComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}