{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { NgbPagination } from '@ng-bootstrap/ng-bootstrap';\nimport { NgIf } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nfunction PaginationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"ngb-pagination\", 4);\n    i0.ɵɵtwoWayListener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.Page, $event) || (ctx_r1.Page = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pageChange());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"collectionSize\", ctx_r1.CollectionSize);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r1.Page);\n    i0.ɵɵproperty(\"pageSize\", ctx_r1.PageSize)(\"maxSize\", 5)(\"boundaryLinks\", true);\n  }\n}\nfunction PaginationComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u7E3D\\u8A18\\u9304\\u6578\\uFF1A\", ctx_r1.CollectionSize, \"\\n\");\n  }\n}\nfunction PaginationComponent_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 6);\n    i0.ɵɵtext(1, \"\\u7121\\u4EFB\\u4F55\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PaginationComponent {\n  constructor() {\n    this.PageChange = new EventEmitter();\n    this.PageSize = 0;\n    this.CollectionSize = 0;\n    this.PageSizeChange = new EventEmitter();\n    this.CollectionSizeChange = new EventEmitter();\n  }\n  ngOnInit() {}\n  pageChange() {\n    this.PageChange.emit(this.Page);\n  }\n  static {\n    this.ɵfac = function PaginationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PaginationComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaginationComponent,\n      selectors: [[\"ngx-pagination\"]],\n      inputs: {\n        Page: \"Page\",\n        PageSize: \"PageSize\",\n        CollectionSize: \"CollectionSize\"\n      },\n      outputs: {\n        PageChange: \"PageChange\",\n        PageSizeChange: \"PageSizeChange\",\n        CollectionSizeChange: \"CollectionSizeChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"class\", \"d-flex justify-content-center p-2 pagination-wrapper\", 4, \"ngIf\"], [\"class\", \"text-center pagination-info\", 4, \"ngIf\"], [\"class\", \"text-center text-danger fw-bold pagination-empty\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"p-2\", \"pagination-wrapper\"], [1, \"pagination-theme\", 3, \"pageChange\", \"collectionSize\", \"page\", \"pageSize\", \"maxSize\", \"boundaryLinks\"], [1, \"text-center\", \"pagination-info\"], [1, \"text-center\", \"text-danger\", \"fw-bold\", \"pagination-empty\"]],\n      template: function PaginationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, PaginationComponent_div_0_Template, 2, 5, \"div\", 0)(1, PaginationComponent_div_1_Template, 2, 1, \"div\", 1)(2, PaginationComponent_p_2_Template, 2, 0, \"p\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize === 0);\n        }\n      },\n      dependencies: [NgIf, NgbPagination],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n  .d-flex.justify-content-center {\\n  margin: 1rem 0;\\n}\\n  ngb-pagination .pagination {\\n  gap: 0.25rem;\\n  margin-bottom: 0;\\n}\\n  ngb-pagination .pagination .page-item .page-link {\\n  color: #AE9B66;\\n  background-color: #FFFFFF;\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all 0.2s ease-in-out;\\n  box-shadow: none;\\n}\\n  ngb-pagination .pagination .page-item .page-link:hover {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  color: #C4B382;\\n  border-color: rgba(184, 166, 118, 0.3);\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n  transform: translateY(-1px);\\n}\\n  ngb-pagination .pagination .page-item .page-link:focus {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  color: #C4B382;\\n  border-color: rgba(184, 166, 118, 0.5);\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n  outline: none;\\n}\\n  ngb-pagination .pagination .page-item.active .page-link {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n  border-color: #AE9B66;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n  font-weight: 600;\\n}\\n  ngb-pagination .pagination .page-item.active .page-link:hover,   ngb-pagination .pagination .page-item.active .page-link:focus {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  border-color: #C4B382;\\n  box-shadow: 0 4px 12px rgba(184, 166, 118, 0.2);\\n  transform: none;\\n}\\n  ngb-pagination .pagination .page-item.disabled .page-link {\\n  color: #CED4DA;\\n  background-color: #F8F9FA;\\n  border-color: #E9ECEF;\\n  cursor: not-allowed;\\n  opacity: 0.6;\\n}\\n  ngb-pagination .pagination .page-item.disabled .page-link:hover,   ngb-pagination .pagination .page-item.disabled .page-link:focus {\\n  background-color: #F8F9FA;\\n  color: #CED4DA;\\n  border-color: #E9ECEF;\\n  box-shadow: none;\\n  transform: none;\\n}\\n  ngb-pagination .pagination .page-item:first-child .page-link,   ngb-pagination .pagination .page-item:last-child .page-link {\\n  font-weight: 600;\\n}\\n  .text-center {\\n  margin-top: 0.75rem;\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n  font-weight: 500;\\n}\\n  .text-center.text-danger {\\n  color: #DC3545 !important;\\n  font-weight: 600;\\n  padding: 1rem;\\n  background-color: #F8D7DA;\\n  border: 1px solid #DC3545;\\n  border-radius: 0.5rem;\\n  margin: 1rem 0;\\n}\\n\\n@media (max-width: 576px) {\\n    ngb-pagination .pagination {\\n    gap: 0.125rem;\\n  }\\n    ngb-pagination .pagination .page-item .page-link {\\n    padding: 0.375rem 0.5rem;\\n    font-size: 0.75rem;\\n  }\\n    .text-center {\\n    font-size: 0.75rem;\\n  }\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item .page-link {\\n  background-color: #1A1A1A;\\n  color: #FFFFFF;\\n  border-color: #404040;\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item .page-link:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  color: #B8A676;\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item.active .page-link {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item.disabled .page-link {\\n  background-color: #2D2D2D;\\n  color: #CCCCCC;\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     .text-center {\\n  color: #CCCCCC;\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     .text-center.text-danger {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: #C82333;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "NgbPagination", "NgIf", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "Page", "ɵɵresetView", "ɵɵlistener", "pageChange", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "CollectionSize", "ɵɵtwoWayProperty", "PageSize", "ɵɵtext", "ɵɵtextInterpolate1", "PaginationComponent", "constructor", "PageChange", "PageSizeChange", "CollectionSizeChange", "ngOnInit", "emit", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PaginationComponent_Template", "rf", "ctx", "ɵɵtemplate", "PaginationComponent_div_0_Template", "PaginationComponent_div_1_Template", "PaginationComponent_p_2_Template", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\pagination\\pagination.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\pagination\\pagination.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { NgbPagination } from '@ng-bootstrap/ng-bootstrap';\r\nimport { NgIf } from '@angular/common';\r\n\r\n@Component({\r\n    selector: 'ngx-pagination',\r\n    templateUrl: './pagination.component.html',\r\n    styleUrls: ['./pagination.component.scss'],\r\n    standalone: true,\r\n    imports: [NgIf, NgbPagination]\r\n})\r\nexport class PaginationComponent implements OnInit {\r\n\r\n  @Output() PageChange = new EventEmitter();\r\n  @Input() Page: number | undefined;\r\n  @Input() PageSize: number = 0;\r\n  @Input() CollectionSize: number = 0;\r\n\r\n  @Output() PageSizeChange = new EventEmitter()\r\n  @Output() CollectionSizeChange = new EventEmitter()\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  pageChange() {\r\n    this.PageChange.emit(this.Page);\r\n  }\r\n\r\n}\r\n", "<div class=\"d-flex justify-content-center p-2 pagination-wrapper\" *ngIf=\"CollectionSize!>0\">\r\n  <ngb-pagination class=\"pagination-theme\" [collectionSize]=\"CollectionSize\" [(page)]=\"Page!\" [pageSize]=\"PageSize\"\r\n    (pageChange)=\"pageChange()\" [maxSize]=\"5\" [boundaryLinks]=\"true\">\r\n  </ngb-pagination>\r\n</div>\r\n\r\n<div class=\"text-center pagination-info\" *ngIf=\"CollectionSize>0\">\r\n  總記錄數：{{CollectionSize}}\r\n</div>\r\n\r\n<p class=\"text-center text-danger fw-bold pagination-empty\" *ngIf=\"CollectionSize===0\">無任何資料</p>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,IAAI,QAAQ,iBAAiB;;;;;ICDpCC,EADF,CAAAC,cAAA,aAA4F,wBAEvB;IADQD,EAAA,CAAAE,gBAAA,wBAAAC,wEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAG,IAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,IAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAgB;IACzFJ,EAAA,CAAAY,UAAA,wBAAAT,wEAAA;MAAAH,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAcJ,MAAA,CAAAM,UAAA,EAAY;IAAA,EAAC;IAE/Bb,EADE,CAAAc,YAAA,EAAiB,EACb;;;;IAHqCd,EAAA,CAAAe,SAAA,EAAiC;IAAjCf,EAAA,CAAAgB,UAAA,mBAAAT,MAAA,CAAAU,cAAA,CAAiC;IAACjB,EAAA,CAAAkB,gBAAA,SAAAX,MAAA,CAAAG,IAAA,CAAgB;IAC/CV,EADgD,CAAAgB,UAAA,aAAAT,MAAA,CAAAY,QAAA,CAAqB,cACtE,uBAAuB;;;;;IAIpEnB,EAAA,CAAAC,cAAA,aAAkE;IAChED,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAc,YAAA,EAAM;;;;IADJd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAqB,kBAAA,oCAAAd,MAAA,CAAAU,cAAA,OACF;;;;;IAEAjB,EAAA,CAAAC,cAAA,WAAuF;IAAAD,EAAA,CAAAoB,MAAA,qCAAK;IAAApB,EAAA,CAAAc,YAAA,EAAI;;;ADChG,OAAM,MAAOQ,mBAAmB;EAU9BC,YAAA;IARU,KAAAC,UAAU,GAAG,IAAI3B,YAAY,EAAE;IAEhC,KAAAsB,QAAQ,GAAW,CAAC;IACpB,KAAAF,cAAc,GAAW,CAAC;IAEzB,KAAAQ,cAAc,GAAG,IAAI5B,YAAY,EAAE;IACnC,KAAA6B,oBAAoB,GAAG,IAAI7B,YAAY,EAAE;EAEnC;EAEhB8B,QAAQA,CAAA,GACR;EAEAd,UAAUA,CAAA;IACR,IAAI,CAACW,UAAU,CAACI,IAAI,CAAC,IAAI,CAAClB,IAAI,CAAC;EACjC;;;uCAjBWY,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAO,SAAA;MAAAC,MAAA;QAAApB,IAAA;QAAAS,QAAA;QAAAF,cAAA;MAAA;MAAAc,OAAA;QAAAP,UAAA;QAAAC,cAAA;QAAAC,oBAAA;MAAA;MAAAM,UAAA;MAAAC,QAAA,GAAAjC,EAAA,CAAAkC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCDhCxC,EAVA,CAAA0C,UAAA,IAAAC,kCAAA,iBAA4F,IAAAC,kCAAA,iBAM1B,IAAAC,gCAAA,eAIqB;;;UAVpB7C,EAAA,CAAAgB,UAAA,SAAAyB,GAAA,CAAAxB,cAAA,KAAuB;UAMhDjB,EAAA,CAAAe,SAAA,EAAsB;UAAtBf,EAAA,CAAAgB,UAAA,SAAAyB,GAAA,CAAAxB,cAAA,KAAsB;UAIHjB,EAAA,CAAAe,SAAA,EAAwB;UAAxBf,EAAA,CAAAgB,UAAA,SAAAyB,GAAA,CAAAxB,cAAA,OAAwB;;;qBDDvElB,IAAI,EAAED,aAAa;MAAAgD,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}