{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { QUOTATION_TEMPLATE } from 'src/assets/template/quotation-template';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { CQuotationItemType } from 'src/app/models/quotation.model';\nimport { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/services/event.service\";\nimport * as i10 from \"src/app/shared/services/utility.service\";\nimport * as i11 from \"src/app/services/quotation.service\";\nimport * as i12 from \"src/app/shared/components/space-template-selector/space-template-selector.service\";\nimport * as i13 from \"@angular/common\";\nimport * as i14 from \"@angular/forms\";\nimport * as i15 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i16 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i17 from \"../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction HouseholdManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r6.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r7.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r9.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_button_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_button_74_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogHouseholdMain_r12 = i0.ɵɵreference(117);\n      return i0.ɵɵresetView(ctx_r10.openModel(dialogHouseholdMain_r12));\n    });\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵtext(2, \"\\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_111_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const item_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogUpdateHousehold_r16 = i0.ɵɵreference(115);\n      return i0.ɵɵresetView(ctx_r10.openModelDetail(dialogUpdateHousehold_r16, item_r15));\n    });\n    i0.ɵɵelement(1, \"i\", 66);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 57);\n    i0.ɵɵtemplate(20, HouseholdManagementComponent_tr_111_button_20_Template, 3, 0, \"button\", 58);\n    i0.ɵɵelementStart(21, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_Template_button_click_21_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"customer-change-picture\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵelement(22, \"i\", 60);\n    i0.ɵɵtext(23, \"\\u6D3D\\u8AC7\\u7D00\\u9304 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_Template_button_click_24_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"sample-selection-result\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵelement(25, \"i\", 61);\n    i0.ɵɵtext(26, \"\\u5BA2\\u8B8A\\u78BA\\u8A8D\\u5716\\u8AAA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_Template_button_click_27_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"finaldochouse_management\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵelement(28, \"i\", 62);\n    i0.ɵɵtext(29, \"\\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_Template_button_click_30_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.resetSecureKey(item_r15));\n    });\n    i0.ɵɵelement(31, \"i\", 63);\n    i0.ɵɵtext(32, \"\\u91CD\\u7F6E\\u5BC6\\u78BC \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_Template_button_click_33_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogQuotation_r17 = i0.ɵɵreference(119);\n      return i0.ɵɵresetView(ctx_r10.openQuotation(dialogQuotation_r17, item_r15));\n    });\n    i0.ɵɵelement(34, \"i\", 64);\n    i0.ɵɵtext(35, \"\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", item_r15.CHouseType === 2 ? \"\\u92B7\\u552E\\u6236\" : \"\", \" \", item_r15.CHouseType === 1 ? \"\\u5730\\u4E3B\\u6236\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsChange === true ? \"\\u5BA2\\u8B8A\" : item_r15.CIsChange === false ? \"\\u6A19\\u6E96\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CProgressName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", item_r15.CPayStatus === 0 ? \"\\u672A\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 1 ? \"\\u5DF2\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 2 ? \"\\u7121\\u9808\\u4ED8\\u6B3E\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CSignStatus === 0 || item_r15.CSignStatus == null ? \"\\u672A\\u7C3D\\u56DE\" : \"\\u5DF2\\u7C3D\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r10.getQuotationStatusText(item_r15.CQuotationStatus));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsEnable ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isUpdate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_nb_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r20);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r20.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_nb_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r21);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r21.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r23.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"label\", 102);\n    i0.ɵɵtext(2, \" \\u4ED8\\u6B3E\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CPayStatusSelected, $event) || (ctx_r10.detailSelected.CPayStatusSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_nb_option_4_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CPayStatusSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.payStatusOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r25);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r25.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"label\", 104);\n    i0.ɵɵtext(2, \" \\u9032\\u5EA6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CProgressSelected, $event) || (ctx_r10.detailSelected.CProgressSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_nb_option_4_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CProgressSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.progressOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-body\", 72)(1, \"div\", 73)(2, \"label\", 74);\n    i0.ɵɵtext(3, \" \\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-select\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CBuildCaseSelected, $event) || (ctx_r10.detailSelected.CBuildCaseSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_114_nb_card_body_1_nb_option_5_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 73)(7, \"label\", 76);\n    i0.ɵɵtext(8, \" \\u6236\\u578B\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 77);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CHousehold, $event) || (ctx_r10.houseDetail.CHousehold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 73)(11, \"label\", 78);\n    i0.ɵɵtext(12, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 79);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CFloor, $event) || (ctx_r10.houseDetail.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 73)(15, \"label\", 80);\n    i0.ɵɵtext(16, \" \\u5BA2\\u6236\\u59D3\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 81);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CCustomerName, $event) || (ctx_r10.houseDetail.CCustomerName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 73)(19, \"label\", 82);\n    i0.ɵɵtext(20, \" \\u8EAB\\u5206\\u8B49\\u5B57\\u865F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 83);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CNationalId, $event) || (ctx_r10.houseDetail.CNationalId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 73)(23, \"label\", 84);\n    i0.ɵɵtext(24, \" \\u96FB\\u5B50\\u90F5\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 85);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CMail, $event) || (ctx_r10.houseDetail.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 73)(27, \"label\", 86);\n    i0.ɵɵtext(28, \" \\u806F\\u7D61\\u96FB\\u8A71 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"input\", 87);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CPhone, $event) || (ctx_r10.houseDetail.CPhone = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 73)(31, \"label\", 88);\n    i0.ɵɵtext(32, \" \\u6236\\u5225\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-select\", 89);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CHouseTypeSelected, $event) || (ctx_r10.detailSelected.CHouseTypeSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(34, HouseholdManagementComponent_ng_template_114_nb_card_body_1_nb_option_34_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_Template, 5, 2, \"div\", 90)(36, HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_Template, 5, 2, \"div\", 90);\n    i0.ɵɵelementStart(37, \"div\", 73)(38, \"label\", 91);\n    i0.ɵɵtext(39, \" \\u662F\\u5426\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"nb-checkbox\", 92);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsChange, $event) || (ctx_r10.houseDetail.CIsChange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(41, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 73)(43, \"label\", 93);\n    i0.ɵɵtext(44, \" \\u662F\\u5426\\u555F\\u7528 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"nb-checkbox\", 92);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsEnable, $event) || (ctx_r10.houseDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(46, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 94)(48, \"label\", 95);\n    i0.ɵɵtext(49, \" \\u5BA2\\u8B8A\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 96)(51, \"nb-form-field\", 97);\n    i0.ɵɵelement(52, \"nb-icon\", 98);\n    i0.ɵɵelementStart(53, \"input\", 99);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeStartDate, $event) || (ctx_r10.houseDetail.changeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"nb-datepicker\", 100, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"nb-form-field\", 97);\n    i0.ɵɵelement(57, \"nb-icon\", 98);\n    i0.ɵɵelementStart(58, \"input\", 101);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_58_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeEndDate, $event) || (ctx_r10.houseDetail.changeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"nb-datepicker\", 100, 5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const StartDate_r26 = i0.ɵɵreference(55);\n    const EndDate_r27 = i0.ɵɵreference(60);\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CBuildCaseSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.userBuildCaseOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CHousehold);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CFloor);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CCustomerName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CNationalId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CMail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CPhone);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CHouseTypeSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.houseTypeOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isChangePayStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isChangeProgress);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsChange);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsEnable);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"nbDatepicker\", StartDate_r26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeStartDate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"nbDatepicker\", EndDate_r27);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeEndDate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 106);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_114_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ref_r28 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onSubmitDetail(ref_r28));\n    });\n    i0.ɵɵelement(1, \"i\", 107);\n    i0.ɵɵtext(2, \"\\u9001\\u51FA \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 67);\n    i0.ɵɵtemplate(1, HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template, 61, 18, \"nb-card-body\", 68);\n    i0.ɵɵelementStart(2, \"nb-card-footer\", 53)(3, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_114_Template_button_click_3_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r28));\n    });\n    i0.ɵɵelement(4, \"i\", 70);\n    i0.ɵɵtext(5, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, HouseholdManagementComponent_ng_template_114_button_6_Template, 3, 0, \"button\", 71);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.houseDetail);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_116_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_116_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ref_r31 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.addHouseHoldMain(ref_r31));\n    });\n    i0.ɵɵelement(1, \"i\", 117);\n    i0.ɵɵtext(2, \"\\u5132\\u5B58 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_116_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 67)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6236\\u5225\\u7BA1\\u7406 \\u300B\\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 72)(4, \"div\", 73)(5, \"label\", 108);\n    i0.ɵɵtext(6, \" \\u68DF\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 109);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_116_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CBuildingName, $event) || (ctx_r10.houseHoldMain.CBuildingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 73)(9, \"label\", 110);\n    i0.ɵɵtext(10, \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 111);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_116_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CHouseHoldCount, $event) || (ctx_r10.houseHoldMain.CHouseHoldCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 73)(13, \"label\", 112);\n    i0.ɵɵtext(14, \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 113);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_116_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CFloor, $event) || (ctx_r10.houseHoldMain.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"nb-card-footer\", 53)(17, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_116_Template_button_click_17_listener() {\n      const ref_r31 = i0.ɵɵrestoreView(_r30).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r31));\n    });\n    i0.ɵɵelement(18, \"i\", 70);\n    i0.ɵɵtext(19, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, HouseholdManagementComponent_ng_template_116_button_20_Template, 3, 0, \"button\", 115);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CBuildingName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CHouseHoldCount);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CFloor);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 156)(1, \"div\", 157)(2, \"app-space-template-selector-button\", 158);\n    i0.ɵɵlistener(\"templateApplied\", function HouseholdManagementComponent_ng_template_118_div_4_Template_app_space_template_selector_button_templateApplied_2_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.onSpaceTemplateApplied($event));\n    })(\"error\", function HouseholdManagementComponent_ng_template_118_div_4_Template_app_space_template_selector_button_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.onTemplateError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 159);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_div_4_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.addQuotationItem());\n    });\n    i0.ɵɵelement(4, \"i\", 160);\n    i0.ɵɵtext(5, \"\\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\")(7, \"button\", 161);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_div_4_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.loadDefaultItems());\n    });\n    i0.ɵɵelement(8, \"i\", 162);\n    i0.ɵɵtext(9, \"\\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_div_4_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.loadRegularItems());\n    });\n    i0.ɵɵelement(11, \"i\", 163);\n    i0.ɵɵtext(12, \"\\u8F09\\u5165\\u9078\\u6A23\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"buildCaseId\", (ctx_r10.searchQuery.CBuildCaseSelected == null ? null : ctx_r10.searchQuery.CBuildCaseSelected.cID == null ? null : ctx_r10.searchQuery.CBuildCaseSelected.cID.toString()) || \"\")(\"text\", \"\\u6A21\\u677F\\u65B0\\u589E\")(\"icon\", \"fas fa-layer-group\")(\"buttonClass\", \"btn btn-warning btn-sm me-2\")(\"disabled\", !(ctx_r10.searchQuery.CBuildCaseSelected == null ? null : ctx_r10.searchQuery.CBuildCaseSelected.cID));\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 164);\n    i0.ɵɵelement(1, \"i\", 165);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" - \\u6B64\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\\uFF0C\\u7121\\u6CD5\\u9032\\u884C\\u4FEE\\u6539\\u3002\\u60A8\\u53EF\\u4EE5\\u5217\\u5370\\u6B64\\u5831\\u50F9\\u55AE\\u6216\\u7522\\u751F\\u65B0\\u7684\\u5831\\u50F9\\u55AE\\u3002 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_tr_27_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 175);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_tr_27_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const i_r38 = i0.ɵɵnextContext().index;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.removeQuotationItem(i_r38));\n    });\n    i0.ɵɵelement(1, \"i\", 176);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_tr_27_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 177);\n    i0.ɵɵelement(1, \"i\", 178);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 166);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_2_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cLocation, $event) || (item_r36.cLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"input\", 167);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_4_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cItemName, $event) || (item_r36.cItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\")(6, \"input\", 168);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_6_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cUnitPrice, $event) || (item_r36.cUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_6_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"input\", 169);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_8_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cUnit, $event) || (item_r36.cUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"input\", 170);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_10_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cCount, $event) || (item_r36.cCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_10_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\", 171);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\")(14, \"span\", 172);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtemplate(17, HouseholdManagementComponent_ng_template_118_tr_27_button_17_Template, 3, 0, \"button\", 173)(18, HouseholdManagementComponent_ng_template_118_tr_27_span_18_Template, 2, 0, \"span\", 174);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r36 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cLocation);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cItemName);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cUnitPrice);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cUnit);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cCount);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.formatCurrency(item_r36.cUnitPrice * item_r36.cCount), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"badge-primary\", item_r36.CQuotationItemType === 1)(\"badge-info\", item_r36.CQuotationItemType === 3)(\"badge-secondary\", item_r36.CQuotationItemType !== 1 && item_r36.CQuotationItemType !== 3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getQuotationTypeText(item_r36.CQuotationItemType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 179);\n    i0.ɵɵtext(2, \" \\u8ACB\\u9EDE\\u64CA\\u300C\\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE\\u300D\\u6216\\u300C\\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42\\u300D\\u958B\\u59CB\\u5EFA\\u7ACB\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_button_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 180);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_button_65_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.createNewQuotation());\n    });\n    i0.ɵɵelement(1, \"i\", 160);\n    i0.ɵɵtext(2, \" \\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_button_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 181);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_button_70_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ref_r40 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.lockQuotation(ref_r40));\n    });\n    i0.ɵɵelement(1, \"i\", 182);\n    i0.ɵɵtext(2, \"\\u9001\\u51FA\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_button_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 183);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_button_71_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ref_r40 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.saveQuotation(ref_r40));\n    });\n    i0.ɵɵelement(1, \"i\", 117);\n    i0.ɵɵtext(2, \"\\u66AB\\u5B58\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 118)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\");\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_118_div_4_Template, 13, 5, \"div\", 119)(5, HouseholdManagementComponent_ng_template_118_div_5_Template, 5, 0, \"div\", 120);\n    i0.ɵɵelementStart(6, \"div\", 121)(7, \"table\", 122)(8, \"thead\", 49)(9, \"tr\")(10, \"th\", 123);\n    i0.ɵɵtext(11, \"\\u5340\\u57DF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 124);\n    i0.ɵɵtext(13, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 123);\n    i0.ɵɵtext(15, \"\\u55AE\\u50F9 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 125);\n    i0.ɵɵtext(17, \"\\u55AE\\u4F4D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 125);\n    i0.ɵɵtext(19, \"\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 126);\n    i0.ɵɵtext(21, \"\\u5C0F\\u8A08 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 127);\n    i0.ɵɵtext(23, \"\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 127);\n    i0.ɵɵtext(25, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"tbody\");\n    i0.ɵɵtemplate(27, HouseholdManagementComponent_ng_template_118_tr_27_Template, 19, 26, \"tr\", 52)(28, HouseholdManagementComponent_ng_template_118_tr_28_Template, 3, 0, \"tr\", 128);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 129)(30, \"div\", 130)(31, \"div\", 131)(32, \"div\", 132)(33, \"span\", 133);\n    i0.ɵɵtext(34, \"\\u5C0F\\u8A08\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"span\", 134);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 135)(38, \"div\", 136)(39, \"div\", 137);\n    i0.ɵɵelement(40, \"i\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\")(42, \"span\", 139);\n    i0.ɵɵtext(43, \"\\u71DF\\u696D\\u7A05\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\", 140);\n    i0.ɵɵtext(45, \"5%\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 141);\n    i0.ɵɵelement(47, \"i\", 142);\n    i0.ɵɵtext(48, \" \\u56FA\\u5B9A\\u70BA\\u5C0F\\u8A08\\u91D1\\u984D\\u76845% \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 143)(50, \"div\", 144);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 145);\n    i0.ɵɵtext(53, \"\\u542B\\u7A05\\u91D1\\u984D\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(54, \"hr\", 146);\n    i0.ɵɵelementStart(55, \"div\", 147)(56, \"span\", 134);\n    i0.ɵɵtext(57, \"\\u7E3D\\u91D1\\u984D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"span\", 148);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(60, \"nb-card-footer\", 149)(61, \"div\")(62, \"button\", 150);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_Template_button_click_62_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.printQuotation());\n    });\n    i0.ɵɵelement(63, \"i\", 151);\n    i0.ɵɵtext(64, \" \\u5217\\u5370\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(65, HouseholdManagementComponent_ng_template_118_button_65_Template, 3, 0, \"button\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\")(67, \"button\", 153);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_Template_button_click_67_listener() {\n      const ref_r40 = i0.ɵɵrestoreView(_r33).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r40));\n    });\n    i0.ɵɵelement(68, \"i\", 70);\n    i0.ɵɵtext(69, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(70, HouseholdManagementComponent_ng_template_118_button_70_Template, 3, 1, \"button\", 154)(71, HouseholdManagementComponent_ng_template_118_button_71_Template, 3, 1, \"button\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u5831\\u50F9\\u55AE - \", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CHouseHold, \" (\", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CFloor, \"\\u6A13) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.quotationItems);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.quotationItems.length === 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.totalAmount));\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.additionalFeeAmount));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.finalTotalAmount));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n  }\n}\nexport let HouseholdManagementComponent = /*#__PURE__*/(() => {\n  class HouseholdManagementComponent extends BaseComponent {\n    constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService, quotationService, templateService, spaceTemplateSelectorService) {\n      super(_allow);\n      this._allow = _allow;\n      this.enumHelper = enumHelper;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._houseService = _houseService;\n      this._houseHoldMainService = _houseHoldMainService;\n      this._buildCaseService = _buildCaseService;\n      this.pettern = pettern;\n      this.router = router;\n      this._eventService = _eventService;\n      this._ultilityService = _ultilityService;\n      this.quotationService = quotationService;\n      this.templateService = templateService;\n      this.spaceTemplateSelectorService = spaceTemplateSelectorService;\n      this.tempBuildCaseID = -1;\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      this.statusOptions = [{\n        value: 0,\n        key: 'allow',\n        label: '允許'\n      }, {\n        value: 1,\n        key: 'not allowed',\n        label: '不允許'\n      }];\n      this.cIsEnableOptions = [{\n        value: null,\n        key: 'all',\n        label: '全部'\n      }, {\n        value: true,\n        key: 'enable',\n        label: '啟用'\n      }, {\n        value: false,\n        key: 'deactivate',\n        label: '停用'\n      }];\n      this.buildCaseOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      this.houseHoldOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      this.progressOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.houseTypeOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.payStatusOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.signStatusOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.quotationStatusOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.options = {\n        progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\n        payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\n        houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\n        quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus)\n      };\n      this.initDetail = {\n        CHouseID: 0,\n        CMail: \"\",\n        CIsChange: false,\n        CPayStatus: 0,\n        CIsEnable: false,\n        CCustomerName: \"\",\n        CNationalID: \"\",\n        CProgress: \"\",\n        CHouseType: 0,\n        CHouseHold: \"\",\n        CPhone: \"\"\n      };\n      // 報價單相關\n      this.quotationItems = [];\n      this.totalAmount = 0;\n      // 新增：百分比費用設定\n      this.additionalFeeName = '營業稅'; // 固定名稱\n      this.additionalFeePercentage = 5; // 固定5%\n      this.additionalFeeAmount = 0; // 百分比費用金額\n      this.finalTotalAmount = 0; // 最終總金額（含百分比費用）\n      this.enableAdditionalFee = true; // 固定啟用營業稅\n      this.currentHouse = null;\n      this.currentQuotationId = 0;\n      this.isQuotationEditable = true; // 報價單是否可編輯\n      this.selectedFile = null;\n      this.buildingSelectedOptions = [{\n        value: '',\n        label: '全部'\n      }];\n      this._eventService.receive().pipe(tap(res => {\n        if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n          this.tempBuildCaseID = res.payload;\n        }\n      })).subscribe();\n    }\n    // 模板匯入相關屬性 - 使用共用元件後簡化\n    // templateList: any[] = [];\n    // templateDetailList: any[] = [];\n    // selectedTemplateForImport: any = null;\n    // templateSearchKeyword: string = '';\n    // templateCurrentPage: number = 1;\n    // templatePageSize: number = 5;\n    // paginatedTemplateList: any[] = [];\n    // templateTotalItems: number = 0;\n    ngOnInit() {\n      this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(EnumHouseProgress)];\n      this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(EnumHouseType)];\n      this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(EnumPayStatus)];\n      this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(EnumSignStatus)];\n      this.quotationStatusOptions = [...this.quotationStatusOptions, ...this.enumHelper.getEnumOptions(EnumQuotationStatus)];\n      if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n        let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n        this.searchQuery = {\n          CBuildCaseSelected: null,\n          // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\n          //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\n          //   : this.buildingSelectedOptions[0],\n          CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value) : this.houseHoldOptions[0],\n          CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value) : this.houseTypeOptions[0],\n          CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value) : this.payStatusOptions[0],\n          CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value) : this.progressOptions[0],\n          CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value) : this.signStatusOptions[0],\n          CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value) : this.quotationStatusOptions[0],\n          CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value) : this.cIsEnableOptions[0],\n          CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined ? previous_search.CFrom : '',\n          CTo: previous_search.CTo != null && previous_search.CTo != undefined ? previous_search.CTo : ''\n        };\n      } else {\n        this.searchQuery = {\n          CBuildCaseSelected: null,\n          // CBuildingNameSelected: this.buildingSelectedOptions[0],\n          CHouseHoldSelected: this.houseHoldOptions[0],\n          CHouseTypeSelected: this.houseTypeOptions[0],\n          CPayStatusSelected: this.payStatusOptions[0],\n          CProgressSelected: this.progressOptions[0],\n          CSignStatusSelected: this.signStatusOptions[0],\n          CQuotationStatusSelected: this.quotationStatusOptions[0],\n          CIsEnableSeleted: this.cIsEnableOptions[0],\n          CFrom: '',\n          CTo: ''\n        };\n      }\n      this.getListBuildCase();\n    }\n    onSearch() {\n      let sessionSave = {\n        CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n        // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\n        CFrom: this.searchQuery.CFrom,\n        CTo: this.searchQuery.CTo,\n        CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\n        CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\n        CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\n        CPayStatusSelected: this.searchQuery.CPayStatusSelected,\n        CProgressSelected: this.searchQuery.CProgressSelected,\n        CSignStatusSelected: this.searchQuery.CSignStatusSelected,\n        CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\n      };\n      LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\n      this.getHouseList().subscribe();\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getHouseList().subscribe();\n    }\n    exportHouse() {\n      if (this.searchQuery.CBuildCaseSelected.cID) {\n        this._houseService.apiHouseExportHousePost$Json({\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n        }).subscribe(res => {\n          if (res.Entries && res.StatusCode == 0) {\n            this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        });\n      }\n    }\n    triggerFileInput() {\n      this.fileInput.nativeElement.click();\n    }\n    onFileSelected(event) {\n      const input = event.target;\n      if (input.files && input.files.length > 0) {\n        this.selectedFile = input.files[0];\n        this.importExcel();\n      }\n    }\n    importExcel() {\n      if (this.selectedFile) {\n        const formData = new FormData();\n        formData.append('CFile', this.selectedFile);\n        this._houseService.apiHouseImportHousePost$Json({\n          body: {\n            CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n            CFile: this.selectedFile\n          }\n        }).subscribe(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG(res.Message);\n            this.getHouseList().subscribe();\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        });\n      }\n    }\n    getListHouseHold() {\n      this._houseService.apiHouseGetListHouseHoldPost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseHoldOptions = [{\n            value: '',\n            label: '全部'\n          }, ...res.Entries.map(e => {\n            return {\n              value: e,\n              label: e\n            };\n          })];\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n            if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\n              let index = this.houseHoldOptions.findIndex(x => x.value == previous_search.CHouseHoldSelected.value);\n              this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index];\n            } else {\n              this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n            }\n          }\n        }\n      });\n    }\n    formatQuery() {\n      this.bodyRequest = {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      };\n      if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n        this.bodyRequest['CFloor'] = {\n          CFrom: this.searchQuery.CFrom,\n          CTo: this.searchQuery.CTo\n        };\n      }\n      if (this.searchQuery.CHouseHoldSelected) {\n        this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n      }\n      if (this.searchQuery.CHouseTypeSelected.value) {\n        this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n      }\n      if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n        this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n      }\n      if (this.searchQuery.CPayStatusSelected.value) {\n        this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n      }\n      if (this.searchQuery.CProgressSelected.value) {\n        this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n      }\n      if (this.searchQuery.CSignStatusSelected.value) {\n        this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n      }\n      if (this.searchQuery.CQuotationStatusSelected.value) {\n        this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value;\n      }\n      return this.bodyRequest;\n    }\n    sortByFloorDescending(arr) {\n      return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n    }\n    getHouseList() {\n      return this._houseService.apiHouseGetHouseListPost$Json({\n        body: this.formatQuery()\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      }));\n    }\n    onSelectionChangeBuildCase() {\n      // this.getListBuilding()\n      this.getListHouseHold();\n      this.getHouseList().subscribe();\n    }\n    getListBuildCase() {\n      this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n        body: {\n          CIsPagi: false,\n          CStatus: 1\n        }\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\n            return {\n              CBuildCaseName: res.CBuildCaseName,\n              cID: res.cID\n            };\n          }) : [];\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\n              let index = this.userBuildCaseOptions.findIndex(x => x.cID == previous_search.CBuildCaseSelected.cID);\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n            } else {\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n            }\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        }\n      }), tap(() => {\n        // this.getListBuilding()\n        this.getListHouseHold();\n        setTimeout(() => {\n          this.getHouseList().subscribe();\n        }, 500);\n      })).subscribe();\n    }\n    getHouseById(CID, ref) {\n      this.detailSelected = {};\n      this._houseService.apiHouseGetHouseByIdPost$Json({\n        body: {\n          CHouseID: CID\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseDetail = {\n            ...res.Entries,\n            changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n            changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n          };\n          if (res.Entries.CBuildCaseId) {\n            this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n          }\n          this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n          if (res.Entries.CHouseType) {\n            this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n          } else {\n            this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n          }\n          this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n          if (res.Entries.CBuildCaseId) {\n            if (this.houseHoldMain) {\n              this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n            }\n          }\n          this.dialogService.open(ref);\n        }\n      });\n    }\n    findItemInArray(array, key, value) {\n      return array.find(item => item[key] === value);\n    }\n    openModelDetail(ref, item) {\n      this.getHouseById(item.CID, ref);\n    }\n    openModel(ref) {\n      this.houseHoldMain = {\n        CBuildingName: '',\n        CFloor: undefined,\n        CHouseHoldCount: undefined\n      };\n      this.dialogService.open(ref);\n    }\n    formatDate(CChangeDate) {\n      if (CChangeDate) {\n        return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n      }\n      return '';\n    }\n    onSubmitDetail(ref) {\n      this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n        CCustomerName: this.houseDetail.CCustomerName,\n        CHouseHold: this.houseDetail.CHousehold,\n        CHouseID: this.houseDetail.CId,\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n        CIsChange: this.houseDetail.CIsChange,\n        CIsEnable: this.houseDetail.CIsEnable,\n        CMail: this.houseDetail.CMail,\n        CNationalID: this.houseDetail.CNationalId,\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n        CPhone: this.houseDetail.CPhone,\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\n        CChangeEndDate: this.houseDetail.CChangeEndDate\n      };\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._houseService.apiHouseEditHousePost$Json({\n        body: this.editHouseArgsParam\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        } else {\n          this.message.showErrorMSG(res.Message);\n          ref.close();\n        }\n      }), concatMap(() => this.getHouseList())).subscribe();\n    }\n    onSubmit(ref) {\n      let bodyReq = {\n        CCustomerName: this.houseDetail.CCustomerName,\n        CHouseHold: this.houseDetail.CHousehold,\n        CHouseID: this.houseDetail.CId,\n        CHouseType: this.houseDetail.CHouseType,\n        CIsChange: this.houseDetail.CIsChange,\n        CIsEnable: this.houseDetail.CIsEnable,\n        CMail: this.houseDetail.CMail,\n        CNationalID: this.houseDetail.CNationalId,\n        CPayStatus: this.houseDetail.CPayStatus,\n        CPhone: this.houseDetail.CPhone,\n        CProgress: this.houseDetail.CProgress\n      };\n      this._houseService.apiHouseEditHousePost$Json({\n        body: bodyReq\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        }\n      });\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    onNavidateId(type, id) {\n      const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n      this.router.navigate([`/pages/household-management/${type}`, idURL]);\n    }\n    onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n      this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n    }\n    resetSecureKey(item) {\n      if (confirm(\"您想重設密碼嗎？\")) {\n        this._houseService.apiHouseResetHouseSecureKeyPost$Json({\n          body: item.CID\n        }).subscribe(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG(\"執行成功\");\n          }\n        });\n      }\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[建案名稱]', this.houseDetail.CId);\n      this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n      this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n      this.valid.required('[樓層]', this.houseDetail.CFloor);\n      this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n      // if (this.editHouseArgsParam.CNationalID) {\n      //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\n      // }\n      this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n      this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n      this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n      this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n      this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n      if (this.houseDetail.CChangeStartDate) {\n        this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n      }\n      if (this.houseDetail.CChangeEndDate) {\n        this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n      }\n      this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n    }\n    validationHouseHoldMain() {\n      this.valid.clear();\n      this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n      this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n      this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n      this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n      this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n    }\n    addHouseHoldMain(ref) {\n      this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n        body: this.houseHoldMain\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        }\n      }), concatMap(() => this.getHouseList())).subscribe();\n    } // 開啟報價單對話框\n    openQuotation(dialog, item) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.currentHouse = item;\n        _this.quotationItems = [];\n        _this.totalAmount = 0;\n        _this.currentQuotationId = 0; // 重置報價單ID\n        _this.isQuotationEditable = true; // 預設可編輯\n        // 重置百分比費用設定（固定營業稅5%）\n        _this.additionalFeeName = '營業稅';\n        _this.additionalFeePercentage = 5;\n        _this.additionalFeeAmount = 0;\n        _this.finalTotalAmount = 0;\n        _this.enableAdditionalFee = true;\n        // 載入現有報價資料\n        try {\n          const response = yield _this.quotationService.getQuotationByHouseId(item.CID).toPromise();\n          if (response && response.StatusCode === 0 && response.Entries) {\n            // 保存當前的報價單ID\n            _this.currentQuotationId = response.Entries.CQuotationVersionId || 0;\n            // 根據 cQuotationStatus 決定是否可編輯\n            if (response.Entries.CQuotationStatus === 2) {\n              // 2: 已報價\n              _this.isQuotationEditable = false;\n            } else {\n              _this.isQuotationEditable = true;\n            }\n            // 載入額外費用設定（固定營業稅5%，不從後端載入）\n            _this.enableAdditionalFee = true;\n            _this.additionalFeeName = '營業稅';\n            _this.additionalFeePercentage = 5;\n            // 檢查 Entries 是否有 Items 陣列\n            if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\n              // 將 API 回傳的資料轉換為 QuotationItem 格式\n              _this.quotationItems = response.Entries.Items.map(entry => ({\n                cHouseID: response.Entries.CHouseID || item.CID,\n                cQuotationID: response.Entries.CQuotationID,\n                cItemName: entry.CItemName || '',\n                cLocation: entry.CLocation || '',\n                cUnit: entry.CUnit || '',\n                cUnitPrice: entry.CUnitPrice || 0,\n                cCount: entry.CCount || 1,\n                cStatus: entry.CStatus || 1,\n                CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\n                cRemark: entry.CRemark || '',\n                cQuotationStatus: entry.CQuotationStatus\n              }));\n              _this.calculateTotal();\n            } else {}\n          } else {}\n        } catch (error) {\n          console.error('載入報價資料失敗:', error);\n        }\n        _this.dialogService.open(dialog, {\n          context: item,\n          closeOnBackdropClick: false\n        });\n      })();\n    }\n    // 產生新報價單\n    createNewQuotation() {\n      this.currentQuotationId = 0;\n      this.quotationItems = [];\n      this.isQuotationEditable = true;\n      this.totalAmount = 0;\n      this.finalTotalAmount = 0;\n      this.additionalFeeAmount = 0;\n      this.enableAdditionalFee = true;\n      // 顯示成功訊息\n      this.message.showSucessMSG('已產生新報價單，可開始編輯');\n    }\n    // 新增自定義報價項目\n    addQuotationItem() {\n      this.quotationItems.push({\n        cHouseID: this.currentHouse?.CID || 0,\n        cItemName: '',\n        cLocation: '',\n        cUnit: '',\n        cUnitPrice: 0,\n        cCount: 1,\n        cStatus: 1,\n        CQuotationItemType: CQuotationItemType.自定義,\n        cRemark: ''\n      });\n    }\n    // 載入客變需求\n    loadDefaultItems() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (!_this2.currentHouse?.CID) {\n            _this2.message.showErrorMSG('請先選擇戶別');\n            return;\n          }\n          const request = {\n            CBuildCaseID: _this2.searchQuery?.CBuildCaseSelected?.cID || 0,\n            CHouseID: _this2.currentHouse.CID\n          };\n          const response = yield _this2.quotationService.loadDefaultItems(request).toPromise();\n          if (response?.success && response.data) {\n            const defaultItems = response.data.map(x => ({\n              cHouseID: _this2.currentHouse?.CID,\n              cItemName: x.cItemName,\n              cLocation: x.cLocation || '',\n              cUnit: x.cUnit || '',\n              cUnitPrice: x.cUnitPrice,\n              cCount: x.cCount,\n              cStatus: x.cStatus,\n              CQuotationItemType: CQuotationItemType.客變需求,\n              cRemark: x.cRemark\n            }));\n            _this2.quotationItems.push(...defaultItems);\n            _this2.calculateTotal();\n            _this2.message.showSucessMSG('載入客變需求成功');\n          } else {\n            _this2.message.showErrorMSG(response?.message || '載入客變需求失敗');\n          }\n        } catch (error) {\n          console.error('載入客變需求錯誤:', error);\n          _this2.message.showErrorMSG('載入客變需求失敗');\n        }\n      })();\n    }\n    // 載入選樣資料\n    loadRegularItems() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (!_this3.currentHouse?.CID) {\n            _this3.message.showErrorMSG('請先選擇戶別');\n            return;\n          }\n          const request = {\n            CBuildCaseID: _this3.searchQuery?.CBuildCaseSelected?.cID || 0,\n            CHouseID: _this3.currentHouse.CID\n          };\n          const response = yield _this3.quotationService.loadRegularItems(request).toPromise();\n          if (response?.success && response.data) {\n            const regularItems = response.data.map(x => ({\n              cHouseID: _this3.currentHouse?.CID,\n              cItemName: x.cItemName,\n              cLocation: x.cLocation || '',\n              cUnit: x.cUnit || '',\n              cUnitPrice: x.cUnitPrice,\n              cCount: x.cCount,\n              cStatus: x.cStatus,\n              CQuotationItemType: CQuotationItemType.選樣,\n              // 選樣資料\n              cRemark: x.cRemark || ''\n            }));\n            _this3.quotationItems.push(...regularItems);\n            _this3.calculateTotal();\n            _this3.message.showSucessMSG('載入選樣資料成功');\n          } else {\n            _this3.message.showErrorMSG(response?.message || '載入選樣資料失敗');\n          }\n        } catch (error) {\n          console.error('載入選樣資料錯誤:', error);\n          _this3.message.showErrorMSG('載入選樣資料失敗');\n        }\n      })();\n    }\n    // 移除報價項目\n    removeQuotationItem(index) {\n      const item = this.quotationItems[index];\n      this.quotationItems.splice(index, 1);\n      this.calculateTotal();\n    }\n    // 計算總金額\n    calculateTotal() {\n      this.totalAmount = this.quotationItems.reduce((sum, item) => {\n        return sum + item.cUnitPrice * item.cCount;\n      }, 0);\n      this.calculateFinalTotal();\n    }\n    // 計算百分比費用和最終總金額（固定營業稅5%）\n    calculateFinalTotal() {\n      // 固定計算營業稅5%\n      this.additionalFeeAmount = Math.round(this.totalAmount * 0.05);\n      this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\n    }\n    // 格式化金額\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('zh-TW', {\n        style: 'currency',\n        currency: 'TWD',\n        minimumFractionDigits: 0\n      }).format(amount);\n    }\n    // 儲存報價單\n    saveQuotation(ref) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        if (_this4.quotationItems.length === 0) {\n          _this4.message.showErrorMSG('請先新增報價項目');\n          return;\n        }\n        // 驗證必填欄位 (調整：允許單價和數量為負數)\n        const invalidNameItems = _this4.quotationItems.filter(item => !item.cItemName.trim());\n        if (invalidNameItems.length > 0) {\n          _this4.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\n          return;\n        }\n        // 驗證單位欄位必填\n        const invalidUnitItems = _this4.quotationItems.filter(item => !item.cUnit || !item.cUnit.trim());\n        if (invalidUnitItems.length > 0) {\n          _this4.message.showErrorMSG('請確認所有項目的單位都已正確填寫');\n          return;\n        }\n        try {\n          const request = {\n            houseId: _this4.currentHouse.CID,\n            items: _this4.quotationItems,\n            quotationId: _this4.currentQuotationId,\n            // 傳遞當前的報價單ID\n            // 額外費用相關欄位\n            cShowOther: _this4.enableAdditionalFee,\n            // 啟用額外費用\n            cOtherName: _this4.additionalFeeName,\n            // 額外費用名稱\n            cOtherPercent: _this4.additionalFeePercentage // 額外費用百分比\n          };\n          const response = yield _this4.quotationService.saveQuotation(request).toPromise();\n          if (response?.success) {\n            _this4.message.showSucessMSG('報價單儲存成功');\n            ref.close();\n          } else {\n            _this4.message.showErrorMSG(response?.message || '儲存失敗');\n          }\n        } catch (error) {\n          _this4.message.showErrorMSG('報價單儲存失敗');\n        }\n      })();\n    }\n    // 匯出報價單\n    exportQuotation() {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const blob = yield _this5.quotationService.exportQuotation(_this5.currentHouse.CID).toPromise();\n          if (blob) {\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `報價單_${_this5.currentHouse.CHouseHold}_${_this5.currentHouse.CFloor}樓.pdf`;\n            link.click();\n            window.URL.revokeObjectURL(url);\n          } else {\n            _this5.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\n          }\n        } catch (error) {\n          _this5.message.showErrorMSG('匯出報價單失敗');\n        }\n      })();\n    }\n    // 列印報價單\n    printQuotation() {\n      if (this.quotationItems.length === 0) {\n        this.message.showErrorMSG('沒有可列印的報價項目');\n        return;\n      }\n      try {\n        // 建立列印內容\n        const printContent = this.generatePrintContent();\n        // 建立新的視窗進行列印\n        const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n        if (printWindow) {\n          printWindow.document.open();\n          printWindow.document.write(printContent);\n          printWindow.document.close();\n          // 等待內容載入完成後列印\n          printWindow.onload = function () {\n            setTimeout(() => {\n              printWindow.print();\n              // 列印後不自動關閉視窗，讓使用者可以預覽\n            }, 500);\n          };\n        } else {\n          this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\n        }\n      } catch (error) {\n        console.error('列印報價單錯誤:', error);\n        this.message.showErrorMSG('列印報價單時發生錯誤');\n      }\n    }\n    // 產生列印內容\n    generatePrintContent() {\n      // 使用導入的模板\n      const template = QUOTATION_TEMPLATE;\n      // 準備數據\n      const currentDate = new Date().toLocaleDateString('zh-TW');\n      const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\n      // 生成項目HTML\n      let itemsHtml = '';\n      this.quotationItems.forEach((item, index) => {\n        const subtotal = item.cUnitPrice * item.cCount;\n        const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\n        const unit = item.cUnit || '';\n        const location = item.cLocation || '';\n        itemsHtml += `\n          <tr>\n            <td class=\"text-center\">${index + 1}</td>\n            <td class=\"text-center\">${location}</td>\n            <td>${item.cItemName}</td>\n            <td class=\"text-right\">${this.formatCurrency(item.cUnitPrice)}</td>\n            <td class=\"text-center\">${unit}</td>\n            <td class=\"text-center\">${item.cCount}</td>\n            <td class=\"text-right\">${this.formatCurrency(subtotal)}</td>\n            <td class=\"text-center\">${quotationType}</td>\n          </tr>\n        `;\n      });\n      // 生成額外費用HTML\n      const additionalFeeHtml = this.enableAdditionalFee ? `\n        <div class=\"additional-fee\">\n          ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\n        </div>\n      ` : '';\n      // 替換模板中的占位符\n      const html = template.replace(/{{buildCaseName}}/g, buildCaseName).replace(/{{houseHold}}/g, this.currentHouse?.CHouseHold || '').replace(/{{floor}}/g, this.currentHouse?.CFloor || '').replace(/{{customerName}}/g, this.currentHouse?.CCustomerName || '').replace(/{{printDate}}/g, currentDate).replace(/{{itemsHtml}}/g, itemsHtml).replace(/{{subtotalAmount}}/g, this.formatCurrency(this.totalAmount)).replace(/{{additionalFeeHtml}}/g, additionalFeeHtml).replace(/{{totalAmount}}/g, this.formatCurrency(this.finalTotalAmount)).replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\n      return html;\n    }\n    // 鎖定報價單\n    lockQuotation(ref) {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        if (_this6.quotationItems.length === 0) {\n          _this6.message.showErrorMSG('請先新增報價項目');\n          return;\n        }\n        // 驗證必填欄位\n        const invalidNameItems = _this6.quotationItems.filter(item => !item.cItemName.trim());\n        if (invalidNameItems.length > 0) {\n          _this6.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\n          return;\n        }\n        // 驗證單位欄位必填\n        const invalidUnitItems = _this6.quotationItems.filter(item => !item.cUnit || !item.cUnit.trim());\n        if (invalidUnitItems.length > 0) {\n          _this6.message.showErrorMSG('請確認所有項目的單位都已正確填寫');\n          return;\n        }\n        if (!_this6.currentQuotationId) {\n          _this6.message.showErrorMSG('無效的報價單ID');\n          return;\n        }\n        try {\n          const response = yield _this6.quotationService.lockQuotation(_this6.currentQuotationId).toPromise();\n          if (response.success) {\n            _this6.message.showSucessMSG('報價單已成功鎖定');\n            console.log('報價單鎖定成功:', {\n              quotationId: _this6.currentQuotationId,\n              message: response.message\n            });\n          } else {\n            _this6.message.showErrorMSG(response.message || '報價單鎖定失敗');\n            console.error('報價單鎖定失敗:', response.message);\n          }\n          ref.close();\n        } catch (error) {\n          _this6.message.showErrorMSG('報價單鎖定失敗');\n          console.error('鎖定報價單錯誤:', error);\n        }\n      })();\n    }\n    // 取得報價類型文字\n    getQuotationTypeText(quotationType) {\n      switch (quotationType) {\n        case CQuotationItemType.客變需求:\n          return '客變需求';\n        case CQuotationItemType.自定義:\n          return '客變項目';\n        case CQuotationItemType.選樣:\n          return '選樣';\n        default:\n          return '未知';\n      }\n    }\n    // 空間模板相關方法 - 使用共用元件\n    onSpaceTemplateApplied(config) {\n      console.log('套用空間模板配置:', config);\n      if (!this.currentHouse) {\n        this.message.showErrorMSG('請先選擇戶別');\n        return;\n      }\n      // 將模板項目轉換為報價單項目\n      this.convertTemplatesToQuotationItems(config.selectedItems, config.templateDetails);\n    }\n    onTemplateError(errorMessage) {\n      this.message.showErrorMSG(errorMessage);\n    }\n    // 將模板項目轉換為報價單項目\n    convertTemplatesToQuotationItems(selectedTemplates, templateDetails) {\n      if (!selectedTemplates || selectedTemplates.length === 0) {\n        this.message.showErrorMSG('未選擇任何模板項目');\n        return;\n      }\n      let newQuotationItems = [];\n      // 處理每個模板的明細項目\n      selectedTemplates.forEach(template => {\n        const details = templateDetails.get(template.CTemplateId);\n        if (details && details.length > 0) {\n          details.forEach(detail => {\n            const newItem = {\n              cHouseID: this.currentHouse.CID,\n              // 設定戶別ID\n              cItemName: detail.CRequirement || detail.CPart || '',\n              // 項目名稱，優先使用 CRequirement，否則使用 CPart\n              cLocation: detail.CLocation || '',\n              // 區域欄位對應\n              cUnitPrice: detail.CUnitPrice || 0,\n              cCount: 1,\n              // 預設數量為1\n              cUnit: detail.CUnit || '',\n              CQuotationItemType: CQuotationItemType.客變需求,\n              // 從模板來的項目標記為客變需求\n              cRemark: detail.CRemark || ''\n            };\n            newQuotationItems.push(newItem);\n          });\n        }\n      });\n      if (newQuotationItems.length === 0) {\n        this.message.showErrorMSG('所選模板中沒有有效的項目');\n        return;\n      }\n      // 將新項目加入到現有報價單中\n      this.quotationItems = [...this.quotationItems, ...newQuotationItems];\n      // 重新計算總金額\n      this.calculateTotal();\n      const templateCount = selectedTemplates.length;\n      const itemCount = newQuotationItems.length;\n      this.message.showSucessMSG(`成功載入 ${templateCount} 個模板，共 ${itemCount} 個項目`);\n      console.log('模板轉換完成:', {\n        templateCount,\n        itemCount,\n        newItems: newQuotationItems\n      });\n    }\n    getQuotationStatusText(status) {\n      switch (status) {\n        case EnumQuotationStatus.待報價:\n          return '待報價';\n        case EnumQuotationStatus.已報價:\n          return '已報價';\n        case EnumQuotationStatus.已簽回:\n          return '已簽回';\n        default:\n          return '未知';\n      }\n    }\n    static {\n      this.ɵfac = function HouseholdManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HouseholdManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i6.HouseHoldMainService), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i9.EventService), i0.ɵɵdirectiveInject(i10.UtilityService), i0.ɵɵdirectiveInject(i11.QuotationService), i0.ɵɵdirectiveInject(i6.TemplateService), i0.ɵɵdirectiveInject(i12.SpaceTemplateSelectorService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HouseholdManagementComponent,\n        selectors: [[\"ngx-household-management\"]],\n        viewQuery: function HouseholdManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 120,\n        vars: 23,\n        consts: [[\"fileInput\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialogHouseholdMain\", \"\"], [\"dialogQuotation\", \"\"], [\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cHouseType\", 1, \"label\", \"col-3\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"text\", \"id\", \"CFrom\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"text\", \"id\", \"CTo\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPayStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7E73\\u6B3E\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cQuotationStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5831\\u50F9\\u55AE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-building\", \"me-1\"], [1, \"fas\", \"fa-file-export\", \"me-1\"], [\"type\", \"file\", \"accept\", \".xlsx, .xls\", 2, \"display\", \"none\", 3, \"change\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-file-import\", \"me-1\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"1000px\"], [1, \"table-header\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"fas\", \"fa-users-plus\", \"me-1\"], [1, \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [1, \"fas\", \"fa-comments\", \"me-1\"], [1, \"fas\", \"fa-file-image\", \"me-1\"], [1, \"fas\", \"fa-file-signature\", \"me-1\"], [1, \"fas\", \"fa-key\", \"me-1\"], [1, \"fas\", \"fa-file-invoice-dollar\", \"me-1\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"me-1\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [\"class\", \"px-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", \"px-8\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [\"class\", \"btn btn-primary m-2 bg-[#169BD5] px-8\", 3, \"click\", 4, \"ngIf\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"cBuildCaseId\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6236\\u578B\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6A13\\u5C64\", \"min\", \"1\", \"max\", \"100\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cCustomerName\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5BA2\\u6236\\u59D3\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cNationalId\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8EAB\\u5206\\u8B49\\u5B57\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cMail\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPhone\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u806F\\u7D61\\u96FB\\u8A71\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHouseType\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u6236\\u5225\\u985E\\u578B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"form-group\", 4, \"ngIf\"], [\"for\", \"cIsChange\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"form-group\", \"flex\", \"flex-row\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", \"content-center\", 2, \"min-width\", \"75px\"], [1, \"max-w-xs\", \"flex\", \"flex-row\"], [1, \"w-1/2\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"mr-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"ml-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"cPayStatus\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u4ED8\\u6B3E\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"m-2\", \"bg-[#169BD5]\", \"px-8\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [\"for\", \"cBuildingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u68DF\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CHouseHoldCount\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"mr-4\", 3, \"click\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-save\", \"me-1\"], [2, \"width\", \"1200px\", \"max-height\", \"95vh\"], [\"class\", \"mb-4 d-flex justify-content-between\", 4, \"ngIf\"], [\"class\", \"mb-4 alert alert-warning\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\"], [\"width\", \"12%\"], [\"width\", \"20%\"], [\"width\", \"8%\"], [\"width\", \"15%\"], [\"width\", \"10%\"], [4, \"ngIf\"], [1, \"mt-4\"], [1, \"card\", \"border-0\", \"shadow-sm\"], [1, \"card-body\", \"p-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"h6\", \"mb-0\", \"text-muted\"], [1, \"h5\", \"mb-0\", \"text-dark\", \"fw-bold\"], [1, \"tax-section\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\", \"p-3\", \"bg-light\", \"rounded\"], [1, \"d-flex\", \"align-items-center\"], [1, \"tax-icon-wrapper\", \"me-3\"], [1, \"fas\", \"fa-receipt\", \"text-info\"], [1, \"fw-medium\", \"text-dark\"], [1, \"tax-percentage\", \"ms-1\", \"badge\", \"bg-info\", \"text-white\"], [1, \"small\", \"text-muted\", \"mt-1\"], [1, \"fas\", \"fa-info-circle\", \"me-1\"], [1, \"text-end\"], [1, \"tax-amount\", \"h6\", \"mb-0\", \"text-info\", \"fw-bold\"], [1, \"small\", \"text-muted\"], [1, \"my-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"h4\", \"mb-0\", \"text-primary\", \"fw-bold\"], [1, \"d-flex\", \"justify-content-between\"], [\"title\", \"\\u5217\\u5370\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-print\", \"me-1\"], [\"class\", \"btn btn-outline-success btn-sm me-2\", \"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-warning m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"mb-4\", \"d-flex\", \"justify-content-between\"], [1, \"d-flex\"], [3, \"templateApplied\", \"error\", \"buildCaseId\", \"text\", \"icon\", \"buttonClass\", \"disabled\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-list-alt\", \"me-1\"], [1, \"fas\", \"fa-palette\", \"me-1\"], [1, \"mb-4\", \"alert\", \"alert-warning\"], [1, \"fas\", \"fa-lock\", \"me-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5340\\u57DF\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"text\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"min\", \"0\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"text-right\"], [1, \"badge\"], [\"class\", \"btn btn-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"me-1\"], [1, \"text-muted\"], [1, \"fas\", \"fa-lock\"], [\"colspan\", \"8\", 1, \"text-muted\", \"py-4\"], [\"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-warning\", \"m-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-paper-plane\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"m-2\", 3, \"click\", \"disabled\"]],\n        template: function HouseholdManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 6)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 7);\n            i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 8)(7, \"div\", 9)(8, \"div\", 10)(9, \"label\", 11);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function HouseholdManagementComponent_Template_nb_select_selectedChange_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n            });\n            i0.ɵɵtemplate(12, HouseholdManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10)(15, \"label\", 14);\n            i0.ɵɵtext(16, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"nb-select\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseTypeSelected, $event) || (ctx.searchQuery.CHouseTypeSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(18, HouseholdManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\", 16)(21, \"label\", 17);\n            i0.ɵɵtext(22, \"\\u6A13 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"nb-form-field\", 18)(24, \"input\", 19);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_24_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"label\", 20);\n            i0.ɵɵtext(26, \"~ \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"nb-form-field\", 21)(28, \"input\", 22);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_28_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelement(29, \"div\", 9);\n            i0.ɵɵelementStart(30, \"div\", 9)(31, \"div\", 10)(32, \"label\", 23);\n            i0.ɵɵtext(33, \" \\u6236\\u578B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"nb-select\", 24);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseHoldSelected, $event) || (ctx.searchQuery.CHouseHoldSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(35, HouseholdManagementComponent_nb_option_35_Template, 2, 2, \"nb-option\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(36, \"div\", 9)(37, \"div\", 10)(38, \"label\", 25);\n            i0.ɵɵtext(39, \" \\u7E73\\u6B3E\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"nb-select\", 26);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CPayStatusSelected, $event) || (ctx.searchQuery.CPayStatusSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(41, HouseholdManagementComponent_nb_option_41_Template, 2, 2, \"nb-option\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(42, \"div\", 9)(43, \"div\", 10)(44, \"label\", 27);\n            i0.ɵɵtext(45, \" \\u9032\\u5EA6 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"nb-select\", 28);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CProgressSelected, $event) || (ctx.searchQuery.CProgressSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(47, HouseholdManagementComponent_nb_option_47_Template, 2, 2, \"nb-option\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(48, \"div\", 9)(49, \"div\", 10)(50, \"label\", 29);\n            i0.ɵɵtext(51, \" \\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"nb-select\", 30);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CIsEnableSeleted, $event) || (ctx.searchQuery.CIsEnableSeleted = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(53, HouseholdManagementComponent_nb_option_53_Template, 2, 2, \"nb-option\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 10)(56, \"label\", 31);\n            i0.ɵɵtext(57, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"nb-select\", 32);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CSignStatusSelected, $event) || (ctx.searchQuery.CSignStatusSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(59, HouseholdManagementComponent_nb_option_59_Template, 2, 2, \"nb-option\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(60, \"div\", 9)(61, \"div\", 10)(62, \"label\", 33);\n            i0.ɵɵtext(63, \" \\u5831\\u50F9\\u55AE\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"nb-select\", 34);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_64_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CQuotationStatusSelected, $event) || (ctx.searchQuery.CQuotationStatusSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(65, HouseholdManagementComponent_nb_option_65_Template, 2, 2, \"nb-option\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelement(66, \"div\", 9);\n            i0.ɵɵelementStart(67, \"div\", 35)(68, \"div\", 36)(69, \"button\", 37);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_69_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelement(70, \"i\", 38);\n            i0.ɵɵtext(71, \"\\u67E5\\u8A62 \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(72, \"div\", 35)(73, \"div\", 39);\n            i0.ɵɵtemplate(74, HouseholdManagementComponent_button_74_Template, 3, 0, \"button\", 40);\n            i0.ɵɵelementStart(75, \"button\", 41);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_75_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onNavidateId(\"modify-floor-plan\"));\n            });\n            i0.ɵɵelement(76, \"i\", 42);\n            i0.ɵɵtext(77, \"\\u4FEE\\u6539\\u6A13\\u5C64\\u6236\\u578B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"button\", 41);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_78_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.exportHouse());\n            });\n            i0.ɵɵelement(79, \"i\", 43);\n            i0.ɵɵtext(80, \"\\u532F\\u51FA\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(81, \"input\", 44, 0);\n            i0.ɵɵlistener(\"change\", function HouseholdManagementComponent_Template_input_change_81_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileSelected($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(83, \"button\", 45);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_83_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.triggerFileInput());\n            });\n            i0.ɵɵelement(84, \"i\", 46);\n            i0.ɵɵtext(85, \"\\u532F\\u5165\\u66F4\\u65B0\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(86, \"div\", 47)(87, \"table\", 48)(88, \"thead\", 49)(89, \"tr\")(90, \"th\", 50);\n            i0.ɵɵtext(91, \"\\u6236\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(92, \"th\", 50);\n            i0.ɵɵtext(93, \"\\u6A13\\u5C64\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(94, \"th\", 50);\n            i0.ɵɵtext(95, \"\\u6236\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(96, \"th\", 50);\n            i0.ɵɵtext(97, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"th\", 50);\n            i0.ɵɵtext(99, \"\\u9032\\u5EA6\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(100, \"th\", 50);\n            i0.ɵɵtext(101, \"\\u7E73\\u6B3E\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(102, \"th\", 50);\n            i0.ɵɵtext(103, \"\\u7C3D\\u56DE\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(104, \"th\", 50);\n            i0.ɵɵtext(105, \"\\u5831\\u50F9\\u55AE\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(106, \"th\", 50);\n            i0.ɵɵtext(107, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(108, \"th\", 51);\n            i0.ɵɵtext(109, \"\\u64CD\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(110, \"tbody\");\n            i0.ɵɵtemplate(111, HouseholdManagementComponent_tr_111_Template, 36, 13, \"tr\", 52);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(112, \"nb-card-footer\", 53)(113, \"ngb-pagination\", 54);\n            i0.ɵɵtwoWayListener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_113_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_113_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(114, HouseholdManagementComponent_ng_template_114_Template, 7, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(116, HouseholdManagementComponent_ng_template_116_Template, 21, 4, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(118, HouseholdManagementComponent_ng_template_118_Template, 72, 13, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseTypeSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseHoldSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseHoldOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CPayStatusSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.payStatusOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CProgressSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.progressOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CIsEnableSeleted);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.cIsEnableOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CSignStatusSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.signStatusOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CQuotationStatusSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.quotationStatusOptions);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(37);\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseList);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i13.NgForOf, i13.NgIf, SharedModule, i14.DefaultValueAccessor, i14.NumberValueAccessor, i14.NgControlStatus, i14.MaxLengthValidator, i14.MinValidator, i14.MaxValidator, i14.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, i3.NbCheckboxComponent, i3.NbInputDirective, i3.NbSelectComponent, i3.NbOptionComponent, i3.NbFormFieldComponent, i3.NbPrefixDirective, i3.NbIconComponent, i3.NbDatepickerDirective, i3.NbDatepickerComponent, i15.NgbPagination, i16.BreadcrumbComponent, i17.BaseLabelDirective, NbDatepickerModule, NbDateFnsDateModule, SpaceTemplateSelectorButtonComponent],\n        styles: [\".card[_ngcontent-%COMP%]{transition:all .3s ease}.card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #0000001a!important}.tax-section[_ngcontent-%COMP%]{background-color:#f8f9fa!important;border:1px solid #e5e7eb;border-radius:8px;transition:all .2s ease;position:relative}.tax-section[_ngcontent-%COMP%]:hover{background-color:#f3f4f6!important;transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}.tax-section[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:4px;height:100%;background-color:#0891b2;border-radius:4px 0 0 4px;transition:width .2s ease}.tax-section[_ngcontent-%COMP%]:hover:before{width:6px}.tax-icon-wrapper[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background-color:#0891b2;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 8px #0891b24d;transition:all .2s ease}.tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem;color:#fff!important}.tax-icon-wrapper[_ngcontent-%COMP%]:hover{transform:scale(1.05);background-color:#0e7490;box-shadow:0 4px 12px #0891b266}.tax-percentage[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem;border-radius:12px;animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}.tax-amount[_ngcontent-%COMP%]{transition:all .2s ease;font-weight:600}.tax-amount[_ngcontent-%COMP%]:hover{transform:scale(1.02);color:#0e7490!important}.text-primary[_ngcontent-%COMP%]{color:#2563eb!important}.text-info[_ngcontent-%COMP%]{color:#0891b2!important}hr[_ngcontent-%COMP%]{border-top:1px solid #e5e7eb;opacity:1;margin:1rem 0}.h5[_ngcontent-%COMP%], .h6[_ngcontent-%COMP%]{transition:all .2s ease;font-weight:600}.text-primary.fw-bold[_ngcontent-%COMP%]{color:#2563eb!important;font-weight:700!important;transition:all .2s ease}.text-primary.fw-bold[_ngcontent-%COMP%]:hover{transform:scale(1.01);color:#1d4ed8!important}.fa-info-circle[_ngcontent-%COMP%]{opacity:.7;transition:opacity .2s ease}.fa-info-circle[_ngcontent-%COMP%]:hover{opacity:1}@media (max-width: 768px){.card-body[_ngcontent-%COMP%]{padding:1.5rem!important}.h4[_ngcontent-%COMP%], .h5[_ngcontent-%COMP%], .h6[_ngcontent-%COMP%]{font-size:1rem!important}.tax-icon-wrapper[_ngcontent-%COMP%]{width:35px;height:35px}.tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.tax-section[_ngcontent-%COMP%]{padding:1rem!important}.tax-section[_ngcontent-%COMP%]:before{width:3px}.tax-section[_ngcontent-%COMP%]:hover:before{width:4px}}.template-selection-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#495057;font-weight:600;border-bottom:2px solid #e9ecef;padding-bottom:.5rem}.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%]{background-color:#f8f9fa;border-color:#ced4da;color:#6c757d}.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-color:#d1d5db;transition:all .2s ease;border-radius:6px}.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#2563eb;box-shadow:0 0 0 3px #2563eb1a;outline:none}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]{border:1px solid #e9ecef;border-radius:.375rem;background-color:#fff;min-height:400px;max-height:500px;overflow-y:auto}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]{padding:.75rem 1rem;border-bottom:1px solid #f1f3f4;cursor:pointer;transition:all .3s ease;display:flex;justify-content:space-between;align-items:center}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;transform:translate(4px)}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]{background-color:#eff6ff;border-left:4px solid #2563eb;transform:translate(4px)}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]{color:#2563eb;font-weight:600}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]{flex:1}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]{font-weight:500;margin-bottom:.25rem;transition:color .3s ease}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#6c757d}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.4}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem;opacity:0;transition:opacity .3s ease}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:1}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{color:#6c757d}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.template-details-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#495057;font-weight:600;border-bottom:2px solid #e9ecef;padding-bottom:.5rem}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]{border:1px solid #e9ecef;border-radius:.375rem;background-color:#fff}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{padding:.75rem 1rem;border-bottom:1px solid #f1f3f4;display:flex;align-items:center;transition:background-color .3s ease}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-checkbox[_ngcontent-%COMP%]{margin-right:1rem;flex-shrink:0}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]{flex:1}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-name[_ngcontent-%COMP%]{margin-bottom:.25rem;color:#212529}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]{display:flex;gap:1rem;flex-wrap:wrap}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d;display:flex;align-items:center}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem;opacity:.7}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{color:#6c757d}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]{padding:.75rem;background-color:#f8f9fa;border-radius:.375rem;border:1px solid #e9ecef}.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{transition:all .3s ease}.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px)}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]{color:#6c757d;font-size:.875rem}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]{display:flex;align-items:center}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{transition:all .3s ease;border-radius:.375rem}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .pagination-current[_ngcontent-%COMP%]{font-weight:500;color:#495057;font-size:.875rem;white-space:nowrap}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:all .2s ease;font-size:.875rem}.btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.1)}.btn.btn-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffffffe6}.btn.btn-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover{color:#fff}.btn.btn-outline-success[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#28a745;margin-right:.25rem}.btn.btn-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .btn.btn-danger[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .btn.btn-warning[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .btn.btn-primary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffffffe6}.btn.btn-outline-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#6c757d}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin:1px;font-size:.75rem;padding:.25rem .5rem}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem;margin-right:.25rem}.d-flex[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]{box-shadow:0 2px 4px #17a2b833;transition:all .3s ease}.d-flex[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 8px #17a2b84d}.btn-secondary[_ngcontent-%COMP%]{background-color:#6c757d;border-color:#6c757d;box-shadow:0 2px 4px #6c757d33;transition:all .3s ease}.btn-secondary[_ngcontent-%COMP%]:hover{background-color:#5a6268;border-color:#545b62;transform:translateY(-1px);box-shadow:0 4px 8px #6c757d4d}nb-card-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.5rem 1.5rem;font-weight:500;border-radius:.375rem;transition:all .3s ease}nb-card-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem;margin-right:.5rem}nb-card-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 8px #00000026}\"]\n      });\n    }\n  }\n  return HouseholdManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}