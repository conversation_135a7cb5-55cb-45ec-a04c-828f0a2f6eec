/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { DeleteSpaceRequest } from '../../models/delete-space-request';
import { StringResponseBase } from '../../models/string-response-base';

export interface ApiSpaceDeleteSpacePost$Plain$Params {
      body?: DeleteSpaceRequest
}

export function apiSpaceDeleteSpacePost$Plain(http: HttpClient, rootUrl: string, params?: ApiSpaceDeleteSpacePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiSpaceDeleteSpacePost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<StringResponseBase>;
    })
  );
}

apiSpaceDeleteSpacePost$Plain.PATH = '/api/Space/DeleteSpace';
