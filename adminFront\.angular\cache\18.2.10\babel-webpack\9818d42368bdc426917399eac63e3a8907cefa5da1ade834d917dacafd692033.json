{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { tap } from 'rxjs';\nlet SpaceComponent = class SpaceComponent extends BaseComponent {\n  constructor(allow, dialogService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.spaceList = [];\n    this.spaceDetail = {};\n    this.searchKeyword = '';\n    this.searchLocation = '';\n  }\n  ngOnInit() {\n    this.getSpaceList();\n  }\n  getSpaceList() {\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CSpaceName: this.searchKeyword || null,\n        CLocation: this.searchLocation || null\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    })).subscribe();\n  }\n  onSearch() {\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getSpaceList();\n  }\n  openCreateModal(ref) {\n    this.spaceDetail = {\n      CSpaceName: '',\n      CDescription: '',\n      CIsEnable: true\n    };\n    this.dialogService.open(ref);\n  }\n  openEditModal(ref, item) {\n    this.getSpaceById(item.CSpaceId, ref);\n  }\n  getSpaceById(spaceId, ref) {\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\n      body: {\n        CSpaceID: spaceId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceDetail = {\n          ...res.Entries\n        };\n        this.dialogService.open(ref);\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    });\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._spaceService.apiSpaceSaveSpacePost$Json({\n      body: this.spaceDetail\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n        this.getSpaceList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    })).subscribe();\n  }\n  deleteSpace(item) {\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\n        body: {\n          CSpaceID: item.CSpaceId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"刪除成功\");\n          this.getSpaceList();\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除失敗');\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[空間名稱]', this.spaceDetail.CSpaceName);\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CSpaceName, 50);\n    this.valid.isStringMaxLength('[描述]', this.spaceDetail.CDescription, 200);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n};\nSpaceComponent = __decorate([Component({\n  selector: 'ngx-space',\n  templateUrl: './space.component.html',\n  styleUrls: ['./space.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule]\n})], SpaceComponent);\nexport { SpaceComponent };", "map": {"version": 3, "names": ["Component", "SharedModule", "CommonModule", "BaseComponent", "tap", "SpaceComponent", "constructor", "allow", "dialogService", "_spaceService", "message", "valid", "pageFirst", "pageSize", "pageIndex", "totalRecords", "spaceList", "spaceDetail", "searchKeyword", "searchLocation", "ngOnInit", "getSpaceList", "apiSpaceGetSpaceListPost$Json", "body", "PageIndex", "PageSize", "CSpaceName", "CLocation", "pipe", "res", "Entries", "StatusCode", "TotalItems", "showErrorMSG", "Message", "subscribe", "onSearch", "pageChanged", "newPage", "openCreateModal", "ref", "CDescription", "CIsEnable", "open", "openEditModal", "item", "getSpaceById", "CSpaceId", "spaceId", "apiSpaceGetSpaceByIdPost$Json", "CSpaceID", "onSubmit", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpaceSaveSpacePost$Json", "showSucessMSG", "close", "deleteSpace", "confirm", "apiSpaceDeleteSpacePost$Json", "clear", "required", "isStringMaxLength", "onClose", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\space\\space.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { tap } from 'rxjs';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-space',\r\n  templateUrl: './space.component.html',\r\n  styleUrls: ['./space.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n  ],\r\n})\r\n\r\nexport class SpaceComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  spaceList: any[] = [];\r\n  spaceDetail: any = {};\r\n  searchKeyword: string = '';\r\n  searchLocation: string = '';\r\n\r\n  override ngOnInit(): void {\r\n    this.getSpaceList();\r\n  }\r\n\r\n  getSpaceList() {\r\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\r\n      body: {\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CSpaceName: this.searchKeyword || null,\r\n        CLocation: this.searchLocation || null\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.spaceList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSearch() {\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  openCreateModal(ref: any) {\r\n    this.spaceDetail = {\r\n      CSpaceName: '',\r\n      CDescription: '',\r\n      CIsEnable: true\r\n    };\r\n    this.dialogService.open(ref);\r\n  }\r\n\r\n  openEditModal(ref: any, item: any) {\r\n    this.getSpaceById(item.CSpaceId, ref);\r\n  }\r\n\r\n  getSpaceById(spaceId: number, ref: any) {\r\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\r\n      body: { CSpaceID: spaceId }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.spaceDetail = { ...res.Entries };\r\n        this.dialogService.open(ref);\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this._spaceService.apiSpaceSaveSpacePost$Json({\r\n      body: this.spaceDetail\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  deleteSpace(item: any) {\r\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\r\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\r\n        body: { CSpaceID: item.CSpaceId }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"刪除成功\");\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '刪除失敗');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[空間名稱]', this.spaceDetail.CSpaceName);\r\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CSpaceName, 50);\r\n    this.valid.isStringMaxLength('[描述]', this.spaceDetail.CDescription, 200);\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,kCAAkC;AAKhE,SAASC,GAAG,QAAQ,MAAM;AAmBnB,IAAMC,cAAc,GAApB,MAAMA,cAAe,SAAQF,aAAa;EAC/CG,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACJ,KAAK,CAAC;IANO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAKN,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,WAAW,GAAQ,EAAE;IACrB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,cAAc,GAAW,EAAE;EAV3B;EAYSC,QAAQA,CAAA;IACf,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,OAAO,IAAI,CAACZ,aAAa,CAACa,6BAA6B,CAAC;MACtDC,IAAI,EAAE;QACJC,SAAS,EAAE,IAAI,CAACV,SAAS;QACzBW,QAAQ,EAAE,IAAI,CAACZ,QAAQ;QACvBa,UAAU,EAAE,IAAI,CAACR,aAAa,IAAI,IAAI;QACtCS,SAAS,EAAE,IAAI,CAACR,cAAc,IAAI;;KAErC,CAAC,CAACS,IAAI,CACLxB,GAAG,CAACyB,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACf,SAAS,GAAGa,GAAG,CAACC,OAAO;QAC5B,IAAI,CAACf,YAAY,GAAGc,GAAG,CAACG,UAAW;MACrC,CAAC,MAAM;QACL,IAAI,CAACtB,OAAO,CAACuB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACtB,SAAS,GAAG,CAAC;IAClB,IAAI,CAACO,YAAY,EAAE;EACrB;EAEAgB,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACxB,SAAS,GAAGwB,OAAO;IACxB,IAAI,CAACjB,YAAY,EAAE;EACrB;EAEAkB,eAAeA,CAACC,GAAQ;IACtB,IAAI,CAACvB,WAAW,GAAG;MACjBS,UAAU,EAAE,EAAE;MACde,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;KACZ;IACD,IAAI,CAAClC,aAAa,CAACmC,IAAI,CAACH,GAAG,CAAC;EAC9B;EAEAI,aAAaA,CAACJ,GAAQ,EAAEK,IAAS;IAC/B,IAAI,CAACC,YAAY,CAACD,IAAI,CAACE,QAAQ,EAAEP,GAAG,CAAC;EACvC;EAEAM,YAAYA,CAACE,OAAe,EAAER,GAAQ;IACpC,IAAI,CAAC/B,aAAa,CAACwC,6BAA6B,CAAC;MAC/C1B,IAAI,EAAE;QAAE2B,QAAQ,EAAEF;MAAO;KAC1B,CAAC,CAACb,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACd,WAAW,GAAG;UAAE,GAAGY,GAAG,CAACC;QAAO,CAAE;QACrC,IAAI,CAACtB,aAAa,CAACmC,IAAI,CAACH,GAAG,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAAC9B,OAAO,CAACuB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEAiB,QAAQA,CAACX,GAAQ;IACf,IAAI,CAACY,UAAU,EAAE;IACjB,IAAI,IAAI,CAACzC,KAAK,CAAC0C,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC5C,OAAO,CAAC6C,aAAa,CAAC,IAAI,CAAC5C,KAAK,CAAC0C,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC5C,aAAa,CAAC+C,0BAA0B,CAAC;MAC5CjC,IAAI,EAAE,IAAI,CAACN;KACZ,CAAC,CAACW,IAAI,CACLxB,GAAG,CAACyB,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACrB,OAAO,CAAC+C,aAAa,CAAC,MAAM,CAAC;QAClCjB,GAAG,CAACkB,KAAK,EAAE;QACX,IAAI,CAACrC,YAAY,EAAE;MACrB,CAAC,MAAM;QACL,IAAI,CAACX,OAAO,CAACuB,YAAY,CAACJ,GAAG,CAACK,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAwB,WAAWA,CAACd,IAAS;IACnB,IAAIe,OAAO,CAAC,eAAe,CAAC,EAAE;MAC5B,IAAI,CAACnD,aAAa,CAACoD,4BAA4B,CAAC;QAC9CtC,IAAI,EAAE;UAAE2B,QAAQ,EAAEL,IAAI,CAACE;QAAQ;OAChC,CAAC,CAACZ,SAAS,CAACN,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACrB,OAAO,CAAC+C,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACpC,YAAY,EAAE;QACrB,CAAC,MAAM;UACL,IAAI,CAACX,OAAO,CAACuB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,MAAM,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EACF;EAEAkB,UAAUA,CAAA;IACR,IAAI,CAACzC,KAAK,CAACmD,KAAK,EAAE;IAClB,IAAI,CAACnD,KAAK,CAACoD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9C,WAAW,CAACS,UAAU,CAAC;IAC1D,IAAI,CAACf,KAAK,CAACqD,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC/C,WAAW,CAACS,UAAU,EAAE,EAAE,CAAC;IACvE,IAAI,CAACf,KAAK,CAACqD,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC/C,WAAW,CAACwB,YAAY,EAAE,GAAG,CAAC;EAC1E;EAEAwB,OAAOA,CAACzB,GAAQ;IACdA,GAAG,CAACkB,KAAK,EAAE;EACb;CACD;AAhIYrD,cAAc,GAAA6D,UAAA,EAX1BlE,SAAS,CAAC;EACTmE,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE,CAAC,wBAAwB,CAAC;EACrCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPrE,YAAY,EACZD,YAAY;CAEf,CAAC,C,EAEWI,cAAc,CAgI1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}