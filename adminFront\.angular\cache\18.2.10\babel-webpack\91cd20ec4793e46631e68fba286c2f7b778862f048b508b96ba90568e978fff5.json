{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../components/pagination/pagination.component\";\nconst _c0 = [\"createModal\"];\nconst _c1 = [\"editModal\"];\nfunction SpaceComponent_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const createModal_r4 = i0.ɵɵreference(69);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(createModal_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_65_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_tr_65_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const editModal_r7 = i0.ɵɵreference(71);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(editModal_r7, item_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_65_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_tr_65_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteSpace(item_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 44);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 38);\n    i0.ɵɵtemplate(11, SpaceComponent_tr_65_button_11_Template, 3, 0, \"button\", 39)(12, SpaceComponent_tr_65_button_12_Template, 3, 0, \"button\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CLocation || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r6.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 6, item_r6.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction SpaceComponent_ng_template_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 45)(1, \"nb-card-header\", 46)(2, \"h5\", 47);\n    i0.ɵɵelement(3, \"i\", 48);\n    i0.ɵɵtext(4, \"\\u65B0\\u589E\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_68_Template_button_click_5_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r10));\n    });\n    i0.ɵɵelement(6, \"i\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 51)(8, \"div\", 52)(9, \"div\", 53)(10, \"div\", 54)(11, \"div\", 55)(12, \"label\", 56);\n    i0.ɵɵtext(13, \" \\u9805\\u76EE\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 57)(15, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_68_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CPart, $event) || (ctx_r2.spaceDetail.CPart = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 53)(17, \"div\", 54)(18, \"div\", 55)(19, \"label\", 59);\n    i0.ɵɵtext(20, \" \\u6240\\u5C6C\\u5340\\u57DF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 57)(22, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_68_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CLocation, $event) || (ctx_r2.spaceDetail.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(23, \"div\", 53)(24, \"div\", 54)(25, \"div\", 55)(26, \"label\", 61);\n    i0.ɵɵtext(27, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 57)(29, \"nb-form-field\", 62)(30, \"nb-select\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_68_Template_nb_select_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CStatus, $event) || (ctx_r2.spaceDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(31, \"nb-option\", 17)(32, \"span\", 4);\n    i0.ɵɵelement(33, \"i\", 64);\n    i0.ɵɵtext(34, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"nb-option\", 17)(36, \"span\", 4);\n    i0.ɵɵelement(37, \"i\", 65);\n    i0.ɵɵtext(38, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(39, \"nb-card-footer\", 66)(40, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_68_Template_button_click_40_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r10));\n    });\n    i0.ɵɵelement(41, \"i\", 68);\n    i0.ɵɵtext(42, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_68_Template_button_click_43_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r10));\n    });\n    i0.ɵɵelement(44, \"i\", 70);\n    i0.ɵɵtext(45, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CPart);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CLocation);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction SpaceComponent_ng_template_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 45)(1, \"nb-card-header\", 46)(2, \"h5\", 47);\n    i0.ɵɵelement(3, \"i\", 71);\n    i0.ɵɵtext(4, \"\\u7DE8\\u8F2F\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_70_Template_button_click_5_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(6, \"i\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 51)(8, \"div\", 52)(9, \"div\", 53)(10, \"div\", 54)(11, \"div\", 55)(12, \"label\", 72);\n    i0.ɵɵtext(13, \" \\u6240\\u5C6C\\u5340\\u57DF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 57)(15, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_70_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CLocation, $event) || (ctx_r2.spaceDetail.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 53)(17, \"div\", 54)(18, \"div\", 55)(19, \"label\", 74);\n    i0.ɵɵtext(20, \" \\u9805\\u76EE\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 57)(22, \"input\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_70_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CPart, $event) || (ctx_r2.spaceDetail.CPart = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(23, \"div\", 53)(24, \"div\", 54)(25, \"div\", 55)(26, \"label\", 76);\n    i0.ɵɵtext(27, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 57)(29, \"nb-form-field\", 62)(30, \"nb-select\", 77);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_70_Template_nb_select_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CStatus, $event) || (ctx_r2.spaceDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(31, \"nb-option\", 17)(32, \"span\", 4);\n    i0.ɵɵelement(33, \"i\", 64);\n    i0.ɵɵtext(34, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"nb-option\", 17)(36, \"span\", 4);\n    i0.ɵɵelement(37, \"i\", 65);\n    i0.ɵɵtext(38, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(39, \"nb-card-footer\", 66)(40, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_70_Template_button_click_40_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(41, \"i\", 68);\n    i0.ɵɵtext(42, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_70_Template_button_click_43_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelement(44, \"i\", 78);\n    i0.ɵɵtext(45, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CLocation);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CPart);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nexport let SpaceComponent = /*#__PURE__*/(() => {\n  class SpaceComponent extends BaseComponent {\n    constructor(allow, router, dialogService, _spaceService, message, valid) {\n      super(allow);\n      this.allow = allow;\n      this.router = router;\n      this.dialogService = dialogService;\n      this._spaceService = _spaceService;\n      this.message = message;\n      this.valid = valid;\n      this.Math = Math; // 讓模板可以使用 Math 函數\n      this.submitSpace = new EventEmitter();\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      this.spaceList = [];\n      this.spaceDetail = {};\n      this.searchKeyword = '';\n      this.searchLocation = '';\n      this.searchStatus = null;\n    }\n    ngOnInit() {\n      this.getSpaceList();\n    }\n    // 導航到模板管理頁面\n    navigateToTemplate() {\n      this.router.navigate(['/pages/template']);\n    }\n    getSpaceList() {\n      const requestData = {\n        CPart: this.searchKeyword || null,\n        CLocation: this.searchLocation || null,\n        CStatus: this.searchStatus,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      };\n      this._spaceService.apiSpaceGetSpaceListPost$Json({\n        body: requestData\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.spaceList = response.Entries || [];\n          this.totalRecords = response.TotalItems || 0;\n        } else {\n          this.message.showErrorMSG(response.Message || '載入空間列表失敗');\n        }\n      })).subscribe();\n    }\n    onSearch() {\n      this.pageIndex = 1;\n      this.getSpaceList();\n    }\n    onReset() {\n      this.searchKeyword = '';\n      this.searchLocation = '';\n      this.searchStatus = null;\n      this.pageIndex = 1;\n      this.getSpaceList();\n    }\n    pageChanged(page) {\n      this.pageIndex = page;\n      this.getSpaceList();\n    }\n    openCreateModal(modal) {\n      this.spaceDetail = {\n        CStatus: 1\n      };\n      this.dialogService.open(modal, {\n        context: {},\n        autoFocus: false\n      });\n    }\n    openEditModal(modal, item) {\n      this.spaceDetail = {\n        CSpaceID: item.CSpaceID,\n        CPart: item.CPart,\n        CLocation: item.CLocation,\n        CStatus: item.CStatus\n      };\n      this.dialogService.open(modal, {\n        context: {},\n        autoFocus: false\n      });\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    onSubmit(ref) {\n      if (!this.validateForm()) {\n        return;\n      }\n      const action = this.spaceDetail.CSpaceID ? 'update' : 'create';\n      this.submitSpace.emit({\n        action: action,\n        data: this.spaceDetail,\n        ref: ref\n      });\n    }\n    validateForm() {\n      if (!this.spaceDetail.CPart?.trim()) {\n        this.message.showErrorMSG('請輸入項目名稱');\n        return false;\n      }\n      if (!this.spaceDetail.CLocation?.trim()) {\n        this.message.showErrorMSG('請輸入所屬區域');\n        return false;\n      }\n      if (this.spaceDetail.CStatus === undefined || this.spaceDetail.CStatus === null) {\n        this.message.showErrorMSG('請選擇狀態');\n        return false;\n      }\n      return true;\n    }\n    deleteSpace(item) {\n      if (confirm(`確定要刪除空間「${item.CPart}」嗎？`)) {\n        this._spaceService.apiSpaceDeleteSpacePost$Json({\n          body: {\n            CSpaceID: item.CSpaceID\n          }\n        }).pipe(tap(response => {\n          if (response.StatusCode === 0) {\n            this.message.showSucessMSG('刪除空間成功');\n            this.getSpaceList();\n          } else {\n            this.message.showErrorMSG(response.Message || '刪除空間失敗');\n          }\n        })).subscribe();\n      }\n    }\n    static {\n      this.ɵfac = function SpaceComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SpaceComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.SpaceService), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.ValidationHelper));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SpaceComponent,\n        selectors: [[\"ngx-space\"]],\n        viewQuery: function SpaceComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.createModal = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editModal = _t.first);\n          }\n        },\n        outputs: {\n          submitSpace: \"submitSpace\"\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 72,\n        vars: 11,\n        consts: [[\"createModal\", \"\"], [\"editModal\", \"\"], [\"accent\", \"success\"], [1, \"alert\", \"alert-info\", \"mb-4\", 2, \"border-left\", \"4px solid #4a90e2\", \"background-color\", \"#f8f9ff\", \"border-radius\", \"6px\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-3\", 2, \"font-size\", \"1.2rem\"], [1, \"mb-0\", \"text-muted\", 2, \"font-size\", \"0.9rem\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"spaceName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"spaceName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"location\", 1, \"label\", \"col-3\"], [\"type\", \"text\", \"id\", \"location\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"col-3\"], [\"id\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-warning\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-layer-group\", \"me-1\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"800px\"], [1, \"table-header\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"table-actions\"], [\"class\", \"btn btn-outline-success btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"border-bottom\", \"py-3\", \"px-4\"], [1, \"mb-0\", \"text-primary\", \"font-weight-bold\"], [1, \"fas\", \"fa-plus-circle\", \"me-2\", \"text-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-light\", \"btn-sm\", \"rounded-circle\", 2, \"width\", \"32px\", \"height\", \"32px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"px-4\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"form-group\", \"mb-4\"], [1, \"d-flex\", \"align-items-start\"], [\"for\", \"spaceName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"flex-grow-1\", \"ml-3\"], [\"type\", \"text\", \"id\", \"spaceName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u9805\\u76EE\\u540D\\u7A31\", \"name\", \"spaceName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"location\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"location\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6240\\u5C6C\\u5340\\u57DF\", \"name\", \"location\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"status\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"w-full\"], [\"id\", \"status\", \"name\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\", \"me-2\"], [1, \"d-flex\", \"justify-content-end\", \"border-top\", \"pt-3\", \"px-4\", \"pb-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"btn\", \"btn-outline-secondary\", \"me-3\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", \"background\", \"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [1, \"fas\", \"fa-edit\", \"me-2\", \"text-warning\"], [\"for\", \"locationEdit\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"locationEdit\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6240\\u5C6C\\u5340\\u57DF\", \"name\", \"location\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"spaceNameEdit\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"spaceNameEdit\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u9805\\u76EE\\u540D\\u7A31\", \"name\", \"spaceName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"statusEdit\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"id\", \"statusEdit\", \"name\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-save\", \"me-1\"]],\n        template: function SpaceComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 3)(5, \"div\", 4);\n            i0.ɵɵelement(6, \"i\", 5);\n            i0.ɵɵelementStart(7, \"div\")(8, \"p\", 6);\n            i0.ɵɵtext(9, \" \\u5728\\u6B64\\u9801\\u9762\\u60A8\\u53EF\\u4EE5\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u7684\\u5404\\u500B\\u7A7A\\u9593\\u8CC7\\u8A0A\\uFF0C\\u5305\\u62EC\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u7A7A\\u9593\\uFF0C\\u4EE5\\u53CA\\u8A2D\\u5B9A\\u9805\\u76EE\\u540D\\u7A31\\u3001\\u6240\\u5C6C\\u5340\\u57DF\\u548C\\u555F\\u7528\\u72C0\\u614B\\u7B49\\u3002 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"label\", 10);\n            i0.ɵɵtext(14, \"\\u9805\\u76EE\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"nb-form-field\", 11)(16, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_input_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"keyup.enter\", function SpaceComponent_Template_input_keyup_enter_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(17, \"div\", 8)(18, \"div\", 9)(19, \"label\", 13);\n            i0.ɵɵtext(20, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"nb-form-field\", 11)(22, \"input\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_input_ngModelChange_22_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchLocation, $event) || (ctx.searchLocation = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"keyup.enter\", function SpaceComponent_Template_input_keyup_enter_22_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(23, \"div\", 8)(24, \"div\", 9)(25, \"label\", 15);\n            i0.ɵɵtext(26, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"nb-form-field\", 11)(28, \"nb-select\", 16);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_nb_select_ngModelChange_28_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchStatus, $event) || (ctx.searchStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function SpaceComponent_Template_nb_select_selectedChange_28_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelementStart(29, \"nb-option\", 17);\n            i0.ɵɵtext(30, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"nb-option\", 17);\n            i0.ɵɵtext(32, \"\\u555F\\u7528\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"nb-option\", 17);\n            i0.ɵɵtext(34, \"\\u505C\\u7528\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelement(35, \"div\", 8);\n            i0.ɵɵelementStart(36, \"div\", 18)(37, \"div\", 19)(38, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function SpaceComponent_Template_button_click_38_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onReset());\n            });\n            i0.ɵɵelement(39, \"i\", 21);\n            i0.ɵɵtext(40, \"\\u91CD\\u7F6E \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function SpaceComponent_Template_button_click_41_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelement(42, \"i\", 23);\n            i0.ɵɵtext(43, \"\\u67E5\\u8A62 \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(44, \"div\", 18)(45, \"div\", 24);\n            i0.ɵɵtemplate(46, SpaceComponent_button_46_Template, 3, 0, \"button\", 25);\n            i0.ɵɵelementStart(47, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function SpaceComponent_Template_button_click_47_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.navigateToTemplate());\n            });\n            i0.ɵɵelement(48, \"i\", 27);\n            i0.ɵɵtext(49, \"\\u7BA1\\u7406\\u6A21\\u677F \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(50, \"div\", 28)(51, \"table\", 29)(52, \"thead\")(53, \"tr\", 30)(54, \"th\", 31);\n            i0.ɵɵtext(55, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"th\", 31);\n            i0.ɵɵtext(57, \"\\u9805\\u76EE\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"th\", 31);\n            i0.ɵɵtext(59, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"th\", 32);\n            i0.ɵɵtext(61, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"th\", 32);\n            i0.ɵɵtext(63, \"\\u64CD\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(64, \"tbody\");\n            i0.ɵɵtemplate(65, SpaceComponent_tr_65_Template, 13, 9, \"tr\", 33);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(66, \"nb-card-footer\", 34)(67, \"ngx-pagination\", 35);\n            i0.ɵɵtwoWayListener(\"PageChange\", function SpaceComponent_Template_ngx_pagination_PageChange_67_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function SpaceComponent_Template_ngx_pagination_PageChange_67_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(68, SpaceComponent_ng_template_68_Template, 46, 5, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(70, SpaceComponent_ng_template_70_Template, 46, 5, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchLocation);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 0);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(19);\n            i0.ɵɵproperty(\"ngForOf\", ctx.spaceList);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DatePipe, SharedModule, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, i3.NbInputDirective, i3.NbSelectComponent, i3.NbOptionComponent, i3.NbFormFieldComponent, i9.BreadcrumbComponent, i10.PaginationComponent],\n        styles: [\".btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-right:.25rem}.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child{margin-right:0}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:var(--color-bg-2);font-weight:600;border-bottom:2px solid var(--color-bg-3)}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:var(--color-bg-1)}.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-top-left-radius:0;border-bottom-left-radius:0}nb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{color:var(--color-fg-heading)}.text-danger[_ngcontent-%COMP%]{color:var(--color-danger)!important}.alert-info[_ngcontent-%COMP%]{transition:all .3s ease}.alert-info[_ngcontent-%COMP%]:hover{box-shadow:0 2px 8px #4a90e226;transform:translateY(-1px)}.alert-info[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-size:1rem;letter-spacing:.3px}.alert-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{line-height:1.6;color:#6c757d!important}.alert-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{flex-shrink:0}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:1rem}.mt-3[_ngcontent-%COMP%]{margin-top:1rem}.btn-secondary[_ngcontent-%COMP%], .btn-outline-secondary[_ngcontent-%COMP%]{transition:all .2s ease}.btn-secondary[_ngcontent-%COMP%]:hover, .btn-outline-secondary[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.btn-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .btn-outline-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem}.btn-outline-secondary[_ngcontent-%COMP%]{border-color:#6c757d;color:#6c757d}.btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#6c757d;border-color:#6c757d;color:#fff}nb-card[style*=box-shadow][_ngcontent-%COMP%]{transition:all .3s ease-in-out}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa,#e9ecef)}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:1.1rem;letter-spacing:.5px}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{transition:all .2s ease}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:#0000000d;transform:scale(1.05)}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{position:relative}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#495057;font-size:.875rem;letter-spacing:.3px}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label.required-field[_ngcontent-%COMP%]:after{content:\\\" *\\\";color:#dc3545;font-weight:700}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[nbInput][_ngcontent-%COMP%]{transition:all .2s ease;font-size:.875rem}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[nbInput][_ngcontent-%COMP%]:focus{border-color:#4a90e2;box-shadow:0 0 0 .2rem #4a90e240;transform:translateY(-1px)}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[nbInput][_ngcontent-%COMP%]::placeholder{color:#adb5bd;font-style:italic}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]   nb-option[_ngcontent-%COMP%]{padding:8px 12px}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]   nb-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.875rem}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]   nb-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa,#e9ecef)}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500;letter-spacing:.3px;transition:all .2s ease}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 8px #00000026}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#357abd,#2c5282)}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button.btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#6c757d;border-color:#6c757d;color:#fff}nb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.form-control[_ngcontent-%COMP%]:focus-within, nb-select[_ngcontent-%COMP%]:focus-within{border-color:#4a90e2;box-shadow:0 0 0 .2rem #4a90e240}@media (max-width: 576px){nb-card[style*=\\\"width: 550px\\\"][_ngcontent-%COMP%]{width:95vw!important;margin:0 auto}}\"]\n      });\n    }\n  }\n  return SpaceComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}