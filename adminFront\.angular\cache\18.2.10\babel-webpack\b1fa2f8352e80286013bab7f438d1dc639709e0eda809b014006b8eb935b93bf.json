{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { ThemeModule } from \"src/app/@theme/theme.module\";\nimport { NbMenuModule } from \"@nebular/theme\";\nimport { SharedModule } from \"../components/shared.module\";\nimport { CustomerChangePictureComponent } from \"./customer-change-picture/customer-change-picture.component\";\nimport { ModifyFloorPlanComponent } from \"./modify-floor-plan/modify-floor-plan.component\";\nimport { ModifyHouseholdComponent } from \"./modify-household/modify-household.component\";\nimport { ModifyHouseTypeComponent } from \"./modify-house-type/modify-house-type.component\";\nimport { StandardHousePlanComponent } from \"./standard-house-plan/standard-house-plan.component\";\nimport { CalendarModule } from \"primeng/calendar\";\nimport { HouseholdRoutingModule } from \"./household-management-routing.module\";\nimport { SampleSelectionResultComponent } from \"./sample-selection-result/sample-selection-result.component\";\nimport { SpaceComponent } from \"./space/space.component\";\nimport { SpecialChangeSourcePipe } from \"../../@theme/pipes/specialChangeSource.pipe\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nexport class HouseholdManagementModule {\n  static {\n    this.ɵfac = function HouseholdManagementModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdManagementModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HouseholdManagementModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, HouseholdRoutingModule, SharedModule, ThemeModule, CalendarModule, NbMenuModule.forRoot()]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HouseholdManagementModule, {\n    declarations: [CustomerChangePictureComponent, SampleSelectionResultComponent, ModifyFloorPlanComponent, ModifyHouseholdComponent, ModifyHouseTypeComponent, StandardHousePlanComponent, SpaceComponent],\n    imports: [CommonModule, HouseholdRoutingModule, SharedModule, ThemeModule, CalendarModule, i1.NbMenuModule, SpecialChangeSourcePipe]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ThemeModule", "NbMenuModule", "SharedModule", "CustomerChangePictureComponent", "ModifyFloorPlanComponent", "ModifyHouseholdComponent", "ModifyHouseTypeComponent", "StandardHousePlanComponent", "CalendarModule", "HouseholdRoutingModule", "SampleSelectionResultComponent", "SpaceComponent", "SpecialChangeSourcePipe", "HouseholdManagementModule", "forRoot", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { CommonModule } from \"@angular/common\";\r\nimport { ThemeModule } from \"src/app/@theme/theme.module\";\r\nimport { NbMenuModule } from \"@nebular/theme\";\r\nimport { SharedModule } from \"../components/shared.module\";\r\nimport { SharedModule as AppSharedModule } from \"../../shared/shared.module\";\r\nimport { CustomerChangePictureComponent } from \"./customer-change-picture/customer-change-picture.component\";\r\nimport { ModifyFloorPlanComponent } from \"./modify-floor-plan/modify-floor-plan.component\";\r\nimport { ModifyHouseholdComponent } from \"./modify-household/modify-household.component\";\r\nimport { ModifyHouseTypeComponent } from \"./modify-house-type/modify-house-type.component\";\r\nimport { StandardHousePlanComponent } from \"./standard-house-plan/standard-house-plan.component\";\r\nimport { CalendarModule } from \"primeng/calendar\";\r\nimport { HouseholdRoutingModule } from \"./household-management-routing.module\";\r\nimport { SampleSelectionResultComponent } from \"./sample-selection-result/sample-selection-result.component\";\r\nimport { SpaceComponent } from \"./space/space.component\";\r\nimport { SpecialChangeSourcePipe } from \"../../@theme/pipes/specialChangeSource.pipe\";\r\n\r\n@NgModule({\r\n  declarations: [\r\n    CustomerChangePictureComponent,\r\n    SampleSelectionResultComponent,\r\n    ModifyFloorPlanComponent,\r\n    ModifyHouseholdComponent,\r\n    ModifyHouseTypeComponent,\r\n    StandardHousePlanComponent,\r\n    SpaceComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    HouseholdRoutingModule,\r\n    SharedModule,\r\n    ThemeModule,\r\n    CalendarModule,\r\n    NbMenuModule.forRoot(),\r\n    SpecialChangeSourcePipe\r\n  ],\r\n})\r\nexport class HouseholdManagementModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,6BAA6B;AAE1D,SAASC,8BAA8B,QAAQ,6DAA6D;AAC5G,SAASC,wBAAwB,QAAQ,iDAAiD;AAC1F,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,wBAAwB,QAAQ,iDAAiD;AAC1F,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,8BAA8B,QAAQ,6DAA6D;AAC5G,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,uBAAuB,QAAQ,6CAA6C;;;AAsBrF,OAAM,MAAOC,yBAAyB;;;uCAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA;IAAyB;EAAA;;;gBATlCd,YAAY,EACZU,sBAAsB,EACtBP,YAAY,EACZF,WAAW,EACXQ,cAAc,EACdP,YAAY,CAACa,OAAO,EAAE;IAAA;EAAA;;;2EAIbD,yBAAyB;IAAAE,YAAA,GAlBlCZ,8BAA8B,EAC9BO,8BAA8B,EAC9BN,wBAAwB,EACxBC,wBAAwB,EACxBC,wBAAwB,EACxBC,0BAA0B,EAC1BI,cAAc;IAAAK,OAAA,GAGdjB,YAAY,EACZU,sBAAsB,EACtBP,YAAY,EACZF,WAAW,EACXQ,cAAc,EAAAS,EAAA,CAAAhB,YAAA,EAEdW,uBAAuB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}