{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/services/message.service\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"fileInput\"];\nconst _c1 = a0 => ({\n  CFileName: a0\n});\nfunction FileUploadComponent_h3_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.config.helpText);\n  }\n}\nfunction FileUploadComponent_label_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 12);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"opacity-50\", ctx_r1.config.disabled)(\"cursor-pointer\", !ctx_r1.config.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.config.buttonIcon + \" mr-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.config.buttonText, \" \");\n  }\n}\nfunction FileUploadComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"span\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_10_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearFile());\n    });\n    i0.ɵɵelement(4, \"i\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.config.disabled);\n  }\n}\nfunction FileUploadComponent_div_11_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"img\", 25);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_11_div_4_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openFile(ctx_r1.currentFileUrl));\n    })(\"error\", function FileUploadComponent_div_11_div_4_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onImageError($event, {\n        CFileName: ctx_r1.currentFileUrl\n      }));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 26);\n    i0.ɵɵelement(3, \"i\", 27);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.currentFileUrl, i0.ɵɵsanitizeUrl)(\"title\", \"\\u9EDE\\u64CA\\u653E\\u5927\\u9810\\u89BD: \" + ctx_r1.currentFileUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(3, _c1, ctx_r1.currentFileUrl)).label, \" \");\n  }\n}\nfunction FileUploadComponent_div_11_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_11_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openFile(ctx_r1.currentFileUrl));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(7, _c1, ctx_r1.currentFileUrl)).bgColor)(\"title\", \"\\u9EDE\\u64CA\\u958B\\u555F: \" + ctx_r1.currentFileUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(9, _c1, ctx_r1.currentFileUrl)).icon + \" text-4xl mb-2 \" + ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(11, _c1, ctx_r1.currentFileUrl)).color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"text-xs font-medium \" + ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(13, _c1, ctx_r1.currentFileUrl)).color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(15, _c1, ctx_r1.currentFileUrl)).label, \" \");\n  }\n}\nfunction FileUploadComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"div\", 19)(3, \"div\", 20);\n    i0.ɵɵtemplate(4, FileUploadComponent_div_11_div_4_Template, 5, 5, \"div\", 21)(5, FileUploadComponent_div_11_div_5_Template, 4, 17, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 23);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isImageFile(ctx_r1.currentFileUrl) && !ctx_r1.isPDFString(ctx_r1.currentFileUrl) && !ctx_r1.isCadString(ctx_r1.currentFileUrl));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isImageFile(ctx_r1.currentFileUrl) || ctx_r1.isPDFString(ctx_r1.currentFileUrl) || ctx_r1.isCadString(ctx_r1.currentFileUrl));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", ctx_r1.currentFileUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.currentFileUrl);\n  }\n}\nfunction FileUploadComponent_div_12_div_1_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"img\", 25);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_12_div_1_div_1_div_2_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const file_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick(file_r8));\n    })(\"error\", function FileUploadComponent_div_12_div_1_div_1_div_2_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const file_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onImageError($event, file_r8));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 26);\n    i0.ɵɵelement(3, \"i\", 27);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageSrc(file_r8), i0.ɵɵsanitizeUrl)(\"title\", \"\\u9EDE\\u64CA\\u653E\\u5927\\u9810\\u89BD: \" + file_r8.CFileName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getFileTypeIcon(file_r8).label, \" \");\n  }\n}\nfunction FileUploadComponent_div_12_div_1_div_1_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r8 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileSize(file_r8), \" \");\n  }\n}\nfunction FileUploadComponent_div_12_div_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_12_div_1_div_1_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const file_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick(file_r8));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, FileUploadComponent_div_12_div_1_div_1_div_3_span_4_Template, 2, 1, \"span\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getFileTypeIcon(file_r8).bgColor)(\"title\", \"\\u9EDE\\u64CA\\u958B\\u555F: \" + file_r8.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.getFileTypeIcon(file_r8).icon + \" text-4xl mb-2 \" + ctx_r1.getFileTypeIcon(file_r8).color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"text-xs font-medium \" + ctx_r1.getFileTypeIcon(file_r8).color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileTypeIcon(file_r8).label, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileSize(file_r8));\n  }\n}\nfunction FileUploadComponent_div_12_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵtemplate(2, FileUploadComponent_div_12_div_1_div_1_div_2_Template, 5, 3, \"div\", 21)(3, FileUploadComponent_div_12_div_1_div_1_div_3_Template, 5, 8, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 23);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 31);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_12_div_1_div_1_Template_span_click_6_listener() {\n      const i_r10 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeFile(i_r10));\n    });\n    i0.ɵɵelement(7, \"i\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isImage(file_r8.CFileType) || ctx_r1.isImageFile(file_r8.CFileName) && !ctx_r1.isPdf(file_r8.CFileType) && !ctx_r1.isCad(file_r8.CFileType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isImage(file_r8.CFileType) && (!ctx_r1.isImageFile(file_r8.CFileName) || ctx_r1.isPdf(file_r8.CFileType) || ctx_r1.isCad(file_r8.CFileType)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", file_r8.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(file_r8.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"hidden\", ctx_r1.config.disabled);\n  }\n}\nfunction FileUploadComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, FileUploadComponent_div_12_div_1_div_1_Template, 8, 6, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.fileList);\n  }\n}\nfunction FileUploadComponent_div_12_div_2_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"img\", 25);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_12_div_2_div_1_div_2_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const file_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick({\n        CFileName: file_r12.CFileName,\n        CFile: file_r12.CFile,\n        data: ctx_r1.getImageSrc(file_r12),\n        relativePath: file_r12.relativePath,\n        fileName: file_r12.fileName\n      }));\n    })(\"error\", function FileUploadComponent_div_12_div_2_div_1_div_2_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const file_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onImageError($event, file_r12));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 26);\n    i0.ɵɵelement(3, \"i\", 27);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageSrc(file_r12), i0.ɵɵsanitizeUrl)(\"title\", \"\\u9EDE\\u64CA\\u653E\\u5927\\u9810\\u89BD: \" + file_r12.CFileName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(3, _c1, file_r12.CFileName)).label, \" \");\n  }\n}\nfunction FileUploadComponent_div_12_div_2_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_12_div_2_div_1_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const file_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick({\n        CFileName: file_r12.CFileName,\n        CFile: file_r12.CFile,\n        data: file_r12.CFile,\n        relativePath: file_r12.relativePath,\n        fileName: file_r12.fileName\n      }));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(7, _c1, file_r12.CFileName)).bgColor)(\"title\", \"\\u9EDE\\u64CA\\u958B\\u555F: \" + file_r12.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(9, _c1, file_r12.CFileName)).icon + \" text-4xl mb-2 \" + ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(11, _c1, file_r12.CFileName)).color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"text-xs font-medium \" + ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(13, _c1, file_r12.CFileName)).color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(15, _c1, file_r12.CFileName)).label, \" \");\n  }\n}\nfunction FileUploadComponent_div_12_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵtemplate(2, FileUploadComponent_div_12_div_2_div_1_div_2_Template, 5, 5, \"div\", 21)(3, FileUploadComponent_div_12_div_2_div_1_div_3_Template, 4, 17, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 23);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r12 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isImageFile(file_r12.CFileName || file_r12.CFile) && !ctx_r1.isPDFString(file_r12.CFileName || file_r12.CFile) && !ctx_r1.isCadString(file_r12.CFileName || file_r12.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isImageFile(file_r12.CFileName || file_r12.CFile) || ctx_r1.isPDFString(file_r12.CFileName || file_r12.CFile) || ctx_r1.isCadString(file_r12.CFileName || file_r12.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", file_r12.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(file_r12.CFileName);\n  }\n}\nfunction FileUploadComponent_div_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, FileUploadComponent_div_12_div_2_div_1_Template, 6, 4, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.existingFiles);\n  }\n}\nfunction FileUploadComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, FileUploadComponent_div_12_div_1_Template, 2, 1, \"div\", 29)(2, FileUploadComponent_div_12_div_2_Template, 2, 1, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fileList && ctx_r1.fileList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.existingFiles && ctx_r1.existingFiles.length > 0);\n  }\n}\nfunction FileUploadComponent_div_13_div_2_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r15 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u2022 \", ctx_r1.getFileSize(file_r15), \"\");\n  }\n}\nfunction FileUploadComponent_div_13_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_13_div_2_Template_div_click_1_listener() {\n      const file_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick(file_r15));\n    });\n    i0.ɵɵelementStart(2, \"div\", 39);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 40)(5, \"span\", 41);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 42)(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, FileUploadComponent_div_13_div_2_span_10_Template, 2, 1, \"span\", 43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_13_div_2_Template_button_click_11_listener() {\n      const i_r16 = i0.ɵɵrestoreView(_r14).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeFile(i_r16));\n    });\n    i0.ɵɵelement(12, \"i\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r15 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r1.getFileTypeIcon(file_r15).icon + \" text-xl \" + ctx_r1.getFileTypeIcon(file_r15).color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(file_r15.CFileName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getFileTypeIcon(file_r15).label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileSize(file_r15));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"hidden\", ctx_r1.config.disabled);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.config.disabled);\n  }\n}\nfunction FileUploadComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 35);\n    i0.ɵɵtemplate(2, FileUploadComponent_div_13_div_2_Template, 13, 8, \"div\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.fileList);\n  }\n}\nexport let FileUploadComponent = /*#__PURE__*/(() => {\n  class FileUploadComponent extends BaseComponent {\n    constructor(_allow, message, fileService) {\n      super(_allow);\n      this._allow = _allow;\n      this.message = message;\n      this.fileService = fileService;\n      this.config = {\n        acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\n        acceptedFileRegex: /pdf|jpg|jpeg|png/i,\n        acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\n        label: '上傳檔案',\n        helpText: '*請上傳PDF格式',\n        required: false,\n        disabled: false,\n        autoFillName: false,\n        buttonText: '上傳',\n        buttonIcon: 'fa-solid fa-cloud-arrow-up',\n        maxFileSize: 10,\n        multiple: false,\n        showPreview: false,\n        hideSelectButton: false\n      };\n      this.currentFileName = null;\n      this.currentFileUrl = null;\n      this.labelMinWidth = '172px';\n      this.fileList = []; // 用於多檔案模式的檔案列表\n      this.existingFiles = []; // 已存在的檔案列表（編輯模式用）\n      this.fileSelected = new EventEmitter();\n      this.fileCleared = new EventEmitter();\n      this.nameAutoFilled = new EventEmitter();\n      this.multiFileSelected = new EventEmitter(); // 多檔案選擇事件\n      this.fileRemoved = new EventEmitter(); // 檔案移除事件\n      this.fileName = null;\n    }\n    ngOnInit() {\n      this.fileName = this.currentFileName; // 設置預設配置\n      this.config = {\n        ...{\n          acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\n          acceptedFileRegex: /pdf|jpg|jpeg|png/i,\n          acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\n          label: '上傳檔案',\n          helpText: '*請上傳PDF格式',\n          required: false,\n          disabled: false,\n          autoFillName: false,\n          buttonText: '上傳',\n          buttonIcon: 'fa-solid fa-cloud-arrow-up',\n          maxFileSize: 10,\n          multiple: false,\n          showPreview: false,\n          hideSelectButton: false\n        },\n        ...this.config\n      };\n    }\n    onFileSelected(event) {\n      const files = event.target.files;\n      if (!files || files.length === 0) {\n        return;\n      }\n      if (this.config.multiple) {\n        this.handleMultipleFiles(files);\n      } else {\n        this.handleSingleFile(files[0]);\n      }\n    }\n    handleSingleFile(file) {\n      // 檔案格式檢查\n      if (!this.config.acceptedFileRegex.test(file.name)) {\n        const acceptedFormats = this.getAcceptedFormatsText();\n        this.message.showErrorMSG(`檔案格式錯誤，${acceptedFormats}`);\n        return;\n      }\n      // 檔案大小檢查\n      const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\n      if (file.size > maxSizeInBytes) {\n        this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);\n        return;\n      }\n      this.fileName = file.name;\n      // 自動填入名稱功能\n      if (this.config.autoFillName) {\n        const fileNameWithoutExtension = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;\n        this.nameAutoFilled.emit(fileNameWithoutExtension);\n      }\n      const reader = new FileReader();\n      reader.onload = e => {\n        // 判斷檔案類型\n        let fileType;\n        if (file.type.startsWith('image/')) {\n          fileType = EnumFileType.JPG; // 圖片\n        } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\n          fileType = 3; // CAD檔案\n        } else {\n          fileType = EnumFileType.PDF; // PDF\n        }\n        const result = {\n          CName: file.name,\n          CFile: e.target?.result?.toString().split(',')[1],\n          Cimg: file.name.includes('pdf') ? file : file,\n          CFileUpload: file,\n          CFileType: fileType,\n          fileName: file.name\n        };\n        this.fileSelected.emit(result);\n        if (this.fileInput) {\n          this.fileInput.nativeElement.value = null;\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n    handleMultipleFiles(files) {\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\n      const fileRegex = this.config.acceptedFileRegex || /pdf|jpg|jpeg|png|dwg|dxf/i;\n      const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\n      // 如果是第一次選擇檔案且支援自動填入名稱\n      if (this.config.autoFillName && files.length > 0) {\n        const firstFile = files[0];\n        const fileName = firstFile.name;\n        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\n        this.nameAutoFilled.emit(fileNameWithoutExtension);\n      }\n      const newFiles = [];\n      let processedCount = 0;\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        // 檔案格式檢查\n        if (!fileRegex.test(file.name)) {\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\n          processedCount++;\n          continue;\n        }\n        // 檔案大小檢查\n        if (file.size > maxSizeInBytes) {\n          this.message.showErrorMSG(`檔案 ${file.name} 大小超過限制（${this.config.maxFileSize}MB）`);\n          processedCount++;\n          continue;\n        }\n        if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\n          const reader = new FileReader();\n          reader.onload = e => {\n            // 判斷檔案類型\n            let fileType = 1; // 預設為其他\n            const fileName = file.name.toLowerCase();\n            if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\n              fileType = 2; // 圖片\n            } else if (fileName.endsWith('.pdf')) {\n              fileType = 1; // PDF\n            } else if (fileName.match(/\\.(dwg|dxf)$/)) {\n              fileType = 3; // CAD\n            }\n            newFiles.push({\n              data: e.target.result,\n              CFileBlood: this.removeBase64Prefix(e.target.result),\n              CFileName: file.name,\n              CFileType: fileType\n            });\n            processedCount++;\n            if (processedCount === files.length) {\n              this.fileList = [...this.fileList, ...newFiles];\n              this.multiFileSelected.emit(this.fileList);\n              if (this.fileInput) {\n                this.fileInput.nativeElement.value = null;\n              }\n            }\n          };\n          reader.readAsDataURL(file);\n        } else {\n          processedCount++;\n        }\n      }\n    }\n    clearFile() {\n      this.fileName = null;\n      this.fileCleared.emit();\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null;\n      }\n    }\n    openFile(url) {\n      if (url) {\n        // 如果 URL 不是完整的 http/https 連結，則添加 base URL\n        const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\n        window.open(fullUrl, '_blank');\n      }\n    }\n    removeFile(index) {\n      this.fileList.splice(index, 1);\n      this.fileRemoved.emit(index);\n      this.multiFileSelected.emit(this.fileList);\n    }\n    removeBase64Prefix(base64String) {\n      const prefixIndex = base64String.indexOf(\",\");\n      if (prefixIndex !== -1) {\n        return base64String.substring(prefixIndex + 1);\n      }\n      return base64String;\n    }\n    isImage(fileType) {\n      return fileType === 2;\n    }\n    isCad(fileType) {\n      return fileType === 3;\n    }\n    isPdf(fileType) {\n      return fileType === 1;\n    }\n    isPDFString(str) {\n      if (str) {\n        return str.toLowerCase().endsWith(\".pdf\");\n      }\n      return false;\n    }\n    isCadString(str) {\n      if (str) {\n        const lowerStr = str.toLowerCase();\n        return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n      }\n      return false;\n    } // 處理檔案點擊事件\n    handleFileClick(file) {\n      console.log('檔案點擊事件觸發:', file);\n      const fileName = file.CFileName || file.fileName || '';\n      const displayName = file.CFileName || fileName;\n      // 判斷檔案類型\n      const isImageByName = this.isImageFile(displayName);\n      const isPdfByName = this.isPDFString(displayName);\n      const isCadByName = this.isCadString(displayName);\n      console.log('檔案類型判斷:', {\n        isImageByName,\n        isPdfByName,\n        isCadByName,\n        displayName\n      });\n      // 統一使用 GetFile API 取得檔案 blob\n      const relativePath = file.relativePath || file.CFile;\n      const serverFileName = file.fileName || file.CFileName;\n      console.log('檔案路徑資訊:', {\n        relativePath,\n        serverFileName\n      });\n      if (relativePath && serverFileName) {\n        console.log('使用 GetFile API 取得檔案 blob');\n        this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\n      } else {\n        // 如果沒有路徑資訊，顯示錯誤訊息\n        console.error('檔案缺少路徑資訊，無法使用 getFile API:', file);\n        this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\n      }\n    }\n    // 處理本地檔案的後備方法 - 已廢棄，統一使用 getFile API\n    handleLocalFile(file, isImage, isPdf, isCad) {\n      const fileName = file.CFileName || file.fileName || '';\n      console.error('檔案缺少必要的路徑資訊，無法使用 getFile API:', file);\n      this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\n    }\n    // 處理本地檔案資料（新上傳的檔案）- 已廢棄，統一使用 getFile API\n    handleLocalFileData(data, fileName, isImage, isPdf, isCad) {\n      console.warn('handleLocalFileData 方法已廢棄，統一使用 getFile API');\n      this.message.showErrorMSG('檔案處理功能已更新，請重新操作');\n      /* 原有邏輯已移除，統一使用 getFile API\n      if (isImage) {\n        // 圖片預覽\n        this.openImagePreview(data, fileName);\n      } else if (isPdf) {\n        // PDF 檔案另開視窗顯示\n        this.openPdfInNewWindow(data, fileName);\n      } else {\n        // 其他檔案（如CAD）轉換為blob並下載\n        this.downloadBase64File(data, fileName);\n      }\n      */\n    }\n    // 下載base64檔案\n    downloadBase64File(base64Data, fileName) {\n      try {\n        // 移除data URL前綴（如果存在）\n        const base64String = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\n        // 轉換為blob\n        const byteCharacters = atob(base64String);\n        const byteNumbers = new Array(byteCharacters.length);\n        for (let i = 0; i < byteCharacters.length; i++) {\n          byteNumbers[i] = byteCharacters.charCodeAt(i);\n        }\n        const byteArray = new Uint8Array(byteNumbers);\n        const blob = new Blob([byteArray]);\n        // 創建下載連結\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = fileName;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        // 清理URL\n        setTimeout(() => URL.revokeObjectURL(url), 1000);\n      } catch (error) {\n        console.error('下載檔案失敗:', error);\n        this.message.showErrorMSG('檔案下載失敗');\n      }\n    }\n    // 從後端取得檔案 blob\n    getFileFromServer(relativePath, fileName, displayName, isImage, isPdf, isCad) {\n      this.fileService.getFile(relativePath, fileName).subscribe({\n        next: blob => {\n          const url = URL.createObjectURL(blob);\n          if (isImage) {\n            // 圖片預覽\n            this.openImagePreview(url, displayName);\n          } else if (isPdf) {\n            // PDF 檔案另開視窗顯示\n            this.openPdfInNewWindow(url, displayName);\n          } else {\n            // 其他檔案直接下載\n            this.downloadBlobFile(blob, displayName);\n          }\n          // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\n          setTimeout(() => URL.revokeObjectURL(url), 10000);\n        },\n        error: error => {\n          console.error('取得檔案失敗:', error);\n          this.message.showErrorMSG('取得檔案失敗，請稍後再試');\n        }\n      });\n    }\n    // 在新視窗中開啟 PDF\n    openPdfInNewWindow(blobUrl, fileName) {\n      try {\n        const newWindow = window.open('', '_blank');\n        if (newWindow) {\n          newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { margin: 0; padding: 0; }\n                iframe { width: 100vw; height: 100vh; border: none; }\n              </style>\n            </head>\n            <body>\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\n            </body>\n          </html>\n        `);\n          newWindow.document.close();\n        } else {\n          // 如果彈出視窗被阻擋，直接開啟 URL\n          window.location.href = blobUrl;\n        }\n      } catch (error) {\n        console.error('開啟 PDF 視窗失敗:', error);\n        // 後備方案：直接開啟 URL\n        window.open(blobUrl, '_blank');\n      }\n    }\n    // 下載 blob 檔案\n    downloadBlobFile(blob, fileName) {\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      // 清理 URL\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\n    }\n    // 判斷檔案是否為圖片（根據檔名）\n    isImageFile(fileName) {\n      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n      const extension = fileName.split('.').pop()?.toLowerCase();\n      return imageExtensions.includes(extension || '');\n    }\n    // 取得正確的圖片 src\n    getImageSrc(file) {\n      const fileName = file.CFileName || file.fileName || '';\n      if (!fileName) {\n        return '';\n      }\n      // 如果檔案有 data 屬性（base64格式），直接使用\n      if (file.data) {\n        return file.data;\n      }\n      // 如果有相對路徑和檔案名，構造 API URL\n      if (file.relativePath && file.fileName) {\n        return `${environment.BASE_WITHOUT_FILEROOT}/api/File/GetFile?relativePath=${encodeURIComponent(file.relativePath)}&fileName=${encodeURIComponent(file.fileName)}`;\n      }\n      // 如果是現有檔案且有 CFile 路徑\n      if (file.CFile && !file.CFile.startsWith('data:')) {\n        return `${environment.BASE_WITHOUT_FILEROOT}${file.CFile}`;\n      }\n      // 後備方案：空字串\n      return '';\n    }\n    // HTML 模板中使用的方法\n    isImageByName(fileName) {\n      return this.isImageFile(fileName);\n    }\n    isPDFByName(fileName) {\n      return this.isPDFString(fileName);\n    }\n    isCadByName(fileName) {\n      return this.isCadString(fileName);\n    } // 圖片載入錯誤處理\n    onImageError(event, file) {\n      const fileName = file.CFileName || file.fileName || '檔案';\n      console.warn('圖片載入失敗:', fileName, file);\n      // 可以設置預設圖片或其他處理\n      event.target.style.display = 'none';\n      // 在圖片載入失敗時，顯示檔案類型圖示，但保留點擊事件\n      const container = event.target.parentElement;\n      if (container) {\n        const iconInfo = this.getFileTypeIcon(file);\n        // 創建新的內容並綁定點擊事件\n        const newContent = document.createElement('div');\n        newContent.className = `w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg ${iconInfo.bgColor}`;\n        newContent.innerHTML = `\n        <i class=\"${iconInfo.icon} text-4xl mb-2 ${iconInfo.color}\"></i>\n        <span class=\"text-xs font-medium ${iconInfo.color}\">${iconInfo.label}</span>\n      `;\n        // 綁定點擊事件\n        newContent.addEventListener('click', () => {\n          this.handleFileClick(file);\n        });\n        // 替換內容\n        container.innerHTML = '';\n        container.appendChild(newContent);\n      }\n    }\n    getAcceptedFormatsText() {\n      const extensions = this.config.acceptAttribute?.split(',').map(type => {\n        if (type.includes('pdf')) return 'PDF';\n        if (type.includes('jpeg') || type.includes('jpg')) return 'JPG';\n        if (type.includes('png')) return 'PNG';\n        if (type.includes('.dwg')) return 'DWG';\n        if (type.includes('.dxf')) return 'DXF';\n        return type;\n      }).join('、');\n      return `僅限${extensions}格式`;\n    }\n    // 在瀏覽器中打開 PDF（已廢棄，統一使用 getFile API）\n    openPdfInBrowser(fileUrl) {\n      console.warn('openPdfInBrowser 方法已廢棄，請使用 getFileFromServer');\n      this.message.showErrorMSG('PDF 檢視功能已更新，請重新操作');\n    }\n    // 直接下載檔案（已廢棄，統一使用 getFile API）\n    downloadFileDirectly(fileUrl, fileName) {\n      console.warn('downloadFileDirectly 方法已廢棄，請使用 getFileFromServer');\n      this.message.showErrorMSG('檔案下載功能已更新，請重新操作');\n    }\n    // 開啟圖片預覽\n    openImagePreview(imageUrl, fileName) {\n      try {\n        const newWindow = window.open('', '_blank');\n        if (newWindow) {\n          newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body {\n                  margin: 0;\n                  padding: 20px;\n                  background-color: #f5f5f5;\n                  display: flex;\n                  justify-content: center;\n                  align-items: center;\n                  min-height: 100vh;\n                }\n                img {\n                  max-width: 100%;\n                  max-height: 100vh;\n                  object-fit: contain;\n                  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n                  background-color: white;\n                }\n                .title {\n                  position: fixed;\n                  top: 10px;\n                  left: 20px;\n                  background: rgba(0, 0, 0, 0.7);\n                  color: white;\n                  padding: 10px;\n                  border-radius: 4px;\n                  font-family: Arial, sans-serif;\n                }\n              </style>\n            </head>\n            <body>\n              <div class=\"title\">${fileName}</div>\n              <img src=\"${imageUrl}\" alt=\"${fileName}\" />\n            </body>\n          </html>\n        `);\n          newWindow.document.close();\n        }\n      } catch (error) {\n        console.error('開啟圖片預覽失敗:', error);\n        window.open(imageUrl, '_blank');\n      }\n    }\n    openNewTab(url) {\n      if (url) {\n        // 如果 URL 不是完整的 http/https 連結，則添加 base URL\n        const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\n        window.open(fullUrl, '_blank');\n      }\n    }\n    // 取得檔案類型圖示資訊\n    getFileTypeIcon(file) {\n      const fileName = file.CFileName || file.fileName || '';\n      const fileType = file.CFileType;\n      const extension = fileName.split('.').pop()?.toLowerCase() || '';\n      // 圖片類型\n      if (fileType === 2 || this.isImageFile(fileName)) {\n        if (extension === 'jpg' || extension === 'jpeg') {\n          return {\n            icon: 'fa fa-file-image-o',\n            color: 'text-blue-600',\n            bgColor: 'bg-blue-50',\n            label: 'JPG'\n          };\n        } else if (extension === 'png') {\n          return {\n            icon: 'fa fa-file-image-o',\n            color: 'text-purple-600',\n            bgColor: 'bg-purple-50',\n            label: 'PNG'\n          };\n        } else {\n          return {\n            icon: 'fa fa-file-image-o',\n            color: 'text-blue-600',\n            bgColor: 'bg-blue-50',\n            label: '圖片'\n          };\n        }\n      }\n      // PDF類型\n      if (fileType === 1 || this.isPDFString(fileName)) {\n        return {\n          icon: 'fa fa-file-pdf-o',\n          color: 'text-red-600',\n          bgColor: 'bg-red-50',\n          label: 'PDF'\n        };\n      }\n      // CAD類型\n      if (fileType === 3 || this.isCadString(fileName)) {\n        if (extension === 'dwg') {\n          return {\n            icon: 'fa fa-cube',\n            color: 'text-green-600',\n            bgColor: 'bg-green-50',\n            label: 'DWG'\n          };\n        } else if (extension === 'dxf') {\n          return {\n            icon: 'fa fa-cube',\n            color: 'text-emerald-600',\n            bgColor: 'bg-emerald-50',\n            label: 'DXF'\n          };\n        } else {\n          return {\n            icon: 'fa fa-cube',\n            color: 'text-green-600',\n            bgColor: 'bg-green-50',\n            label: 'CAD'\n          };\n        }\n      }\n      // Office文件類型\n      if (extension === 'doc' || extension === 'docx') {\n        return {\n          icon: 'fa fa-file-word-o',\n          color: 'text-blue-700',\n          bgColor: 'bg-blue-50',\n          label: 'Word'\n        };\n      }\n      if (extension === 'xls' || extension === 'xlsx') {\n        return {\n          icon: 'fa fa-file-excel-o',\n          color: 'text-green-700',\n          bgColor: 'bg-green-50',\n          label: 'Excel'\n        };\n      }\n      if (extension === 'ppt' || extension === 'pptx') {\n        return {\n          icon: 'fa fa-file-powerpoint-o',\n          color: 'text-orange-600',\n          bgColor: 'bg-orange-50',\n          label: 'PPT'\n        };\n      }\n      // 壓縮檔案\n      if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {\n        return {\n          icon: 'fa fa-file-archive-o',\n          color: 'text-yellow-600',\n          bgColor: 'bg-yellow-50',\n          label: extension.toUpperCase()\n        };\n      }\n      // 文字檔案\n      if (['txt', 'log', 'md'].includes(extension)) {\n        return {\n          icon: 'fa fa-file-text-o',\n          color: 'text-gray-600',\n          bgColor: 'bg-gray-50',\n          label: extension.toUpperCase()\n        };\n      }\n      // 預設檔案類型\n      return {\n        icon: 'fa fa-file-o',\n        color: 'text-gray-500',\n        bgColor: 'bg-gray-50',\n        label: extension ? extension.toUpperCase() : '檔案'\n      };\n    }\n    // 取得檔案大小格式化字串\n    getFileSize(file) {\n      let size = 0;\n      if (file.size) {\n        size = file.size;\n      } else if (file.CFileBlood) {\n        // base64 字串大小估算 (約為實際檔案大小的 4/3)\n        size = Math.floor(file.CFileBlood.length * 0.75);\n      }\n      if (size === 0) return '';\n      const units = ['B', 'KB', 'MB', 'GB'];\n      let unitIndex = 0;\n      while (size >= 1024 && unitIndex < units.length - 1) {\n        size /= 1024;\n        unitIndex++;\n      }\n      return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;\n    }\n    static {\n      this.ɵfac = function FileUploadComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FileUploadComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.FileService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FileUploadComponent,\n        selectors: [[\"app-file-upload\"]],\n        viewQuery: function FileUploadComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        inputs: {\n          config: \"config\",\n          currentFileName: \"currentFileName\",\n          currentFileUrl: \"currentFileUrl\",\n          labelMinWidth: \"labelMinWidth\",\n          fileList: \"fileList\",\n          existingFiles: \"existingFiles\"\n        },\n        outputs: {\n          fileSelected: \"fileSelected\",\n          fileCleared: \"fileCleared\",\n          nameAutoFilled: \"nameAutoFilled\",\n          multiFileSelected: \"multiFileSelected\",\n          fileRemoved: \"fileRemoved\"\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 14,\n        vars: 14,\n        consts: [[\"fileInput\", \"\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", 1, \"label\", \"col-3\"], [\"style\", \"color:red;\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"col-9\", \"px-0\", \"items-start\"], [\"type\", \"file\", \"id\", \"fileInput\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\", \"accept\", \"multiple\", \"disabled\"], [\"for\", \"fileInput\", \"class\", \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\", 3, \"opacity-50\", \"cursor-pointer\", 4, \"ngIf\"], [\"class\", \"flex items-center space-x-2 mt-2\", 4, \"ngIf\"], [\"class\", \"w-full mt-2\", 4, \"ngIf\"], [2, \"color\", \"red\"], [\"for\", \"fileInput\", 1, \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mt-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\", \"disabled\"], [1, \"fa-solid\", \"fa-trash\"], [1, \"w-full\", \"mt-2\"], [1, \"flex\", \"flex-wrap\", \"mt-2\", \"file-type-container\"], [1, \"relative\", \"w-28\", \"h-28\", \"mr-3\", \"mb-6\", \"file-item\"], [1, \"w-full\", \"h-full\", \"border\", \"border-gray-300\", \"rounded-lg\", \"shadow-sm\", \"bg-white\", \"hover:shadow-md\", \"transition-shadow\", \"duration-200\"], [\"class\", \"w-full h-full relative rounded-lg overflow-hidden\", 4, \"ngIf\"], [\"class\", \"w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg relative\", 3, \"ngClass\", \"title\", \"click\", 4, \"ngIf\"], [1, \"absolute\", \"-bottom-6\", \"left-0\", \"w-full\", \"text-xs\", \"truncate\", \"px-1\", \"text-center\", \"text-gray-600\", 3, \"title\"], [1, \"w-full\", \"h-full\", \"relative\", \"rounded-lg\", \"overflow-hidden\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"cursor-pointer\", 3, \"click\", \"error\", \"src\", \"title\"], [1, \"absolute\", \"top-1\", \"left-1\", \"bg-blue-500\", \"text-white\", \"text-xs\", \"px-1\", \"py-0.5\", \"rounded\", \"file-type-label\"], [1, \"fa\", \"fa-image\", \"mr-1\"], [1, \"w-full\", \"h-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"cursor-pointer\", \"rounded-lg\", \"relative\", 3, \"click\", \"ngClass\", \"title\"], [\"class\", \"flex flex-wrap mt-2 file-type-container\", 4, \"ngIf\"], [\"class\", \"relative w-28 h-28 mr-3 mb-6 file-item\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"\\u79FB\\u9664\\u6A94\\u6848\", 1, \"absolute\", \"-top-2\", \"-right-2\", \"cursor-pointer\", \"bg-red-500\", \"text-white\", \"rounded-full\", \"w-6\", \"h-6\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-red-600\", \"transition-colors\", \"duration-200\", \"remove-btn\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"text-xs\"], [\"class\", \"text-xs text-gray-500 mt-1\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-500\", \"mt-1\"], [1, \"space-y-2\"], [\"class\", \"flex items-center justify-between bg-gray-50 p-3 rounded-lg hover:bg-gray-100 transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"bg-gray-50\", \"p-3\", \"rounded-lg\", \"hover:bg-gray-100\", \"transition-colors\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"cursor-pointer\", 3, \"click\"], [1, \"flex-shrink-0\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"truncate\", \"block\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-xs\", \"text-gray-500\"], [4, \"ngIf\"], [\"type\", \"button\", \"title\", \"\\u79FB\\u9664\\u6A94\\u6848\", 1, \"text-red-500\", \"hover:text-red-700\", \"p-1\", \"rounded\", \"hover:bg-red-50\", \"transition-colors\", 3, \"click\", \"disabled\"], [1, \"fa\", \"fa-trash\", \"text-sm\"]],\n        template: function FileUploadComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"label\", 4);\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, FileUploadComponent_h3_5_Template, 2, 1, \"h3\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 6)(7, \"input\", 7, 0);\n            i0.ɵɵlistener(\"change\", function FileUploadComponent_Template_input_change_7_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileSelected($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(9, FileUploadComponent_label_9_Template, 3, 7, \"label\", 8)(10, FileUploadComponent_div_10_Template, 5, 2, \"div\", 9)(11, FileUploadComponent_div_11_Template, 8, 4, \"div\", 10)(12, FileUploadComponent_div_12_Template, 3, 2, \"div\", 10)(13, FileUploadComponent_div_13_Template, 3, 1, \"div\", 10);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵstyleProp(\"min-width\", ctx.labelMinWidth);\n            i0.ɵɵclassProp(\"required-field\", ctx.config.required);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.config.label, \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.config.helpText);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"accept\", ctx.config.acceptAttribute)(\"multiple\", ctx.config.multiple)(\"disabled\", ctx.config.disabled);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.config.hideSelectButton);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.fileName && !ctx.config.multiple);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentFileUrl && !ctx.fileName && !ctx.config.multiple);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.config.multiple && ctx.config.showPreview);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.config.multiple && !ctx.config.showPreview && ctx.fileList && ctx.fileList.length > 0);\n          }\n        },\n        dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf],\n        styles: [\".file-upload-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%}.file-upload-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%}.file-upload-label-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;margin-right:12px}.file-upload-label[_ngcontent-%COMP%]{min-width:172px;color:#333;font-weight:500}.required-field[_ngcontent-%COMP%]:after{content:\\\" *\\\";color:red}.file-upload-help-text[_ngcontent-%COMP%]{color:red;font-size:14px;margin-top:4px}.file-upload-input-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;flex:1;padding:0;align-items:flex-start}.file-input-hidden[_ngcontent-%COMP%]{display:none}.file-upload-button[_ngcontent-%COMP%]{background-color:#3b82f6;color:#fff;font-weight:700;padding:8px 16px;border-radius:4px;border:none;cursor:pointer;transition:background-color .2s;display:inline-flex;align-items:center}.file-upload-button[_ngcontent-%COMP%]:hover:not(.disabled){background-color:#1d4ed8}.file-upload-button.disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.file-display-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-top:8px}.file-name[_ngcontent-%COMP%]{color:#6b7280;font-size:14px}.file-delete-button[_ngcontent-%COMP%]{color:#ef4444;background:none;border:none;cursor:pointer;padding:4px;transition:color .2s}.file-delete-button[_ngcontent-%COMP%]:hover{color:#dc2626}.current-file-link[_ngcontent-%COMP%]{color:#3b82f6;cursor:pointer;font-size:14px;text-decoration:underline}.current-file-link[_ngcontent-%COMP%]:hover{color:#1d4ed8}.hidden[_ngcontent-%COMP%]{display:none}.upload-icon[_ngcontent-%COMP%]{margin-right:8px}.delete-icon[_ngcontent-%COMP%]{font-size:14px}\"]\n      });\n    }\n  }\n  return FileUploadComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}