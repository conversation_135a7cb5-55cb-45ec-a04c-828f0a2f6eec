{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport { tap } from 'rxjs';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nlet TemplateComponent = class TemplateComponent extends BaseComponent {\n  constructor(allow, dialogService, _templateService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._templateService = _templateService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.Math = Math; // 讓模板可以使用 Math 函數\n    this.EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\n    this.EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    // 模板相關屬性\n    this.templateList = [];\n    this.templateDetail = {};\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    // 空間選擇相關屬性\n    this.availableSpaces = [];\n    this.selectedSpacesForTemplate = [];\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.spacePageSize = 10;\n    this.spaceTotalRecords = 0;\n    this.allSpacesSelected = false;\n    // 模板明細相關屬性\n    this.selectedTemplateDetail = null;\n    this.templateDetailSpaces = [];\n    this.isLoadingTemplateDetail = false;\n  }\n  ngOnInit() {\n    this.loadTemplateList();\n    this.loadAvailableSpaces();\n  }\n  // 載入模板列表\n  loadTemplateList() {\n    const request = {\n      CTemplateName: this.searchKeyword || null,\n      CStatus: this.searchStatus,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this._templateService.apiTemplateGetTemplateListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.templateList = response.Entries?.map(item => ({\n          CTemplateId: item.CTemplateId,\n          CTemplateName: item.CTemplateName,\n          CTemplateType: item.CTemplateType,\n          // 新增模板類型\n          CCreateDt: item.CCreateDt,\n          CUpdateDt: item.CUpdateDt,\n          CCreator: item.CCreator,\n          CUpdator: item.CUpdator,\n          CStatus: item.CStatus\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板列表失敗');\n      }\n    })).subscribe();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    const request = {\n      CPart: this.spaceSearchKeyword || null,\n      CLocation: this.spaceSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.spacePageIndex,\n      PageSize: this.spacePageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.spaceTotalRecords = response.TotalItems || 0;\n        this.updateAllSpacesSelectedState();\n      }\n    })).subscribe();\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  // 空間搜尋功能\n  onSpaceSearch() {\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  onSpaceReset() {\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 分頁功能\n  pageChanged(page) {\n    this.pageIndex = page;\n    this.loadTemplateList();\n  }\n  spacePageChanged(page) {\n    this.spacePageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  // 模態框操作\n  openCreateModal(modal) {\n    this.templateDetail = {\n      CStatus: 1,\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\n    };\n    this.selectedSpacesForTemplate = [];\n    this.loadAvailableSpaces();\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  openEditModal(modal, template) {\n    this.templateDetail = {\n      CTemplateId: template.CTemplateId,\n      CTemplateName: template.CTemplateName,\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\n      CStatus: template.CStatus || 1\n    };\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onSubmit(ref) {\n    if (!this.validateTemplateForm()) {\n      return;\n    }\n    if (this.templateDetail.CTemplateId) {\n      this.updateTemplate(ref);\n    } else {\n      this.createTemplate(ref);\n    }\n  }\n  // 驗證表單\n  validateTemplateForm() {\n    if (!this.templateDetail.CTemplateName?.trim()) {\n      this.message.showErrorMSG('請輸入模板名稱');\n      return false;\n    }\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\n      this.message.showErrorMSG('請選擇模板類型');\n      return false;\n    }\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\n      this.message.showErrorMSG('請選擇模板狀態');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\n      return false;\n    }\n    return true;\n  }\n  // 建立模板\n  createTemplate(ref) {\n    const templateData = {\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0 && response.Entries) {\n        const templateId = parseInt(response.Entries, 10);\n        this.saveTemplateDetails(templateId, ref);\n      } else {\n        this.message.showErrorMSG(response.Message || '建立模板失敗');\n      }\n    })).subscribe();\n  }\n  // 更新模板\n  updateTemplate(ref) {\n    const templateData = {\n      CTemplateId: this.templateDetail.CTemplateId,\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.message.showSucessMSG('更新模板成功');\n        ref.close();\n        this.loadTemplateList();\n      } else {\n        this.message.showErrorMSG(response.Message || '更新模板失敗');\n      }\n    })).subscribe();\n  }\n  // 儲存模板詳細資料（關聯空間）\n  saveTemplateDetails(templateId, ref) {\n    // 目前 API 可能不支援模板詳細資料的保存，暫時跳過這個步驟\n    this.message.showSucessMSG('建立模板成功');\n    ref.close();\n    this.loadTemplateList();\n  }\n  // 刪除模板\n  deleteTemplate(template) {\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\n        body: {\n          CTemplateId: template.CTemplateId\n        }\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('刪除模板成功');\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '刪除模板失敗');\n        }\n      })).subscribe();\n    }\n  }\n  // 查看模板明細\n  viewTemplateDetail(template, modal) {\n    this.selectedTemplateDetail = template;\n    this.isLoadingTemplateDetail = true;\n    this.templateDetailSpaces = [];\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n    const request = {\n      templateId: template.CTemplateId\n    };\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      this.isLoadingTemplateDetail = false;\n      if (response.StatusCode === 0) {\n        this.templateDetailSpaces = response.Entries?.map(item => ({\n          CReleateId: item.CReleateId,\n          CPart: item.CPart,\n          CLocation: item.CLocation\n        })) || [];\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板明細失敗');\n      }\n    })).subscribe();\n  }\n  // 空間選擇相關方法\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n        this.selectedSpacesForTemplate.push({\n          ...space\n        });\n      }\n    } else {\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  toggleAllSpaces() {\n    this.allSpacesSelected = !this.allSpacesSelected;\n    this.availableSpaces.forEach(space => {\n      space.selected = this.allSpacesSelected;\n      if (this.allSpacesSelected) {\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else {\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n  }\n  removeSelectedSpace(space) {\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  updateAllSpacesSelectedState() {\n    this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n};\n__decorate([ViewChild('createModal', {\n  static: false\n})], TemplateComponent.prototype, \"createModal\", void 0);\n__decorate([ViewChild('editModal', {\n  static: false\n})], TemplateComponent.prototype, \"editModal\", void 0);\n__decorate([ViewChild('templateDetailModal', {\n  static: false\n})], TemplateComponent.prototype, \"templateDetailModal\", void 0);\nTemplateComponent = __decorate([Component({\n  selector: 'ngx-template',\n  templateUrl: './template.component.html',\n  styleUrls: ['./template.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, BreadcrumbComponent]\n})], TemplateComponent);\nexport { TemplateComponent };", "map": {"version": 3, "names": ["BaseComponent", "Component", "ViewChild", "CommonModule", "SharedModule", "BreadcrumbComponent", "tap", "EnumTemplateType", "EnumTemplateTypeHelper", "TemplateComponent", "constructor", "allow", "dialogService", "_templateService", "_spaceService", "message", "valid", "Math", "pageFirst", "pageSize", "pageIndex", "totalRecords", "templateList", "templateDetail", "searchKeyword", "searchStatus", "availableSpaces", "selectedSpacesForTemplate", "spaceSearchKeyword", "spaceSearchLocation", "spacePageIndex", "spacePageSize", "spaceTotalRecords", "allSpacesSelected", "selectedTemplateDetail", "templateDetailSpaces", "isLoadingTemplateDetail", "ngOnInit", "loadTemplateList", "loadAvailableSpaces", "request", "CTemplateName", "CStatus", "PageIndex", "PageSize", "apiTemplateGetTemplateListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "CTemplateType", "CCreateDt", "CUpdateDt", "CCreator", "CUpdator", "TotalItems", "showErrorMSG", "Message", "subscribe", "<PERSON>art", "CLocation", "apiSpaceGetSpaceListPost$Json", "CSpaceID", "selected", "some", "s", "updateAllSpacesSelectedState", "onSearch", "onReset", "onSpaceSearch", "onSpaceReset", "pageChanged", "page", "spacePageChanged", "openCreateModal", "modal", "SpaceTemplate", "open", "context", "autoFocus", "openEditModal", "template", "onClose", "ref", "close", "onSubmit", "validateTemplateForm", "updateTemplate", "createTemplate", "trim", "undefined", "length", "templateData", "apiTemplateSaveTemplatePost$Json", "templateId", "parseInt", "saveTemplateDetails", "showSucessMSG", "deleteTemplate", "confirm", "apiTemplateDeleteTemplatePost$Json", "viewTemplateDetail", "apiTemplateGetTemplateDetailByIdPost$Json", "CReleateId", "toggleSpaceSelection", "space", "push", "filter", "toggleAllSpaces", "for<PERSON>ach", "removeSelectedSpace", "availableSpace", "find", "every", "__decorate", "static", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.ts"], "sourcesContent": ["import { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { TemplateService, SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { tap } from 'rxjs';\r\nimport {\r\n  SaveTemplateArgs,\r\n  GetTemplateDetailByIdArgs,\r\n  TemplateDetailItem,\r\n  GetSpaceListResponse\r\n} from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\nexport interface TemplateItem {\r\n  CTemplateId: number;\r\n  CTemplateName: string;\r\n  CTemplateType?: number;\r\n  CCreateDt: string;\r\n  CUpdateDt: string;\r\n  CCreator?: string | null;\r\n  CUpdator?: string | null;\r\n  CStatus?: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpacePickListItem {\r\n  CSpaceID: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n  selected?: boolean;\r\n}\r\n\r\n// 僅用於模板明細空間顯示\r\nexport interface TemplateDetailSpaceItem {\r\n  CReleateId: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-template',\r\n  templateUrl: './template.component.html',\r\n  styleUrls: ['./template.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent\r\n  ],\r\n})\r\nexport class TemplateComponent extends BaseComponent implements OnInit {\r\n  Math = Math; // 讓模板可以使用 Math 函數\r\n  EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\r\n  EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\r\n\r\n  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;\r\n  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;\r\n  @ViewChild('templateDetailModal', { static: false }) templateDetailModal!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _templateService: TemplateService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow);\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  // 模板相關屬性\r\n  templateList: TemplateItem[] = [];\r\n  templateDetail: SaveTemplateArgs = {};\r\n  searchKeyword: string = '';\r\n  searchStatus: number | null = null;\r\n\r\n  // 空間選擇相關屬性\r\n  availableSpaces: SpacePickListItem[] = [];\r\n  selectedSpacesForTemplate: SpacePickListItem[] = [];\r\n  spaceSearchKeyword: string = '';\r\n  spaceSearchLocation: string = '';\r\n  spacePageIndex = 1;\r\n  spacePageSize = 10;\r\n  spaceTotalRecords = 0;\r\n  allSpacesSelected = false;\r\n\r\n  // 模板明細相關屬性\r\n  selectedTemplateDetail: TemplateItem | null = null;\r\n  templateDetailSpaces: TemplateDetailSpaceItem[] = [];\r\n  isLoadingTemplateDetail = false;\r\n\r\n  override ngOnInit(): void {\r\n    this.loadTemplateList();\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 載入模板列表\r\n  loadTemplateList(): void {\r\n    const request = {\r\n      CTemplateName: this.searchKeyword || null,\r\n      CStatus: this.searchStatus,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.templateList = response.Entries?.map(item => ({\r\n            CTemplateId: item.CTemplateId!,\r\n            CTemplateName: item.CTemplateName!,\r\n            CTemplateType: item.CTemplateType, // 新增模板類型\r\n            CCreateDt: item.CCreateDt!,\r\n            CUpdateDt: item.CUpdateDt!,\r\n            CCreator: item.CCreator,\r\n            CUpdator: item.CUpdator,\r\n            CStatus: item.CStatus\r\n          })) || [];\r\n          this.totalRecords = response.TotalItems || 0;\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板列表失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 載入可用空間列表\r\n  loadAvailableSpaces(): void {\r\n    const request = {\r\n      CPart: this.spaceSearchKeyword || null,\r\n      CLocation: this.spaceSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.spacePageIndex,\r\n      PageSize: this.spacePageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableSpaces = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\r\n          })) || [];\r\n          this.spaceTotalRecords = response.TotalItems || 0;\r\n          this.updateAllSpacesSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 搜尋功能\r\n  onSearch(): void {\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  onReset(): void {\r\n    this.searchKeyword = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 空間搜尋功能\r\n  onSpaceSearch(): void {\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  onSpaceReset(): void {\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 分頁功能\r\n  pageChanged(page: number): void {\r\n    this.pageIndex = page;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  spacePageChanged(page: number): void {\r\n    this.spacePageIndex = page;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 模態框操作\r\n  openCreateModal(modal: TemplateRef<any>): void {\r\n    this.templateDetail = {\r\n      CStatus: 1,\r\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\r\n    };\r\n    this.selectedSpacesForTemplate = [];\r\n    this.loadAvailableSpaces();\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  openEditModal(modal: TemplateRef<any>, template: TemplateItem): void {\r\n    this.templateDetail = {\r\n      CTemplateId: template.CTemplateId,\r\n      CTemplateName: template.CTemplateName,\r\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\r\n      CStatus: template.CStatus || 1\r\n    };\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  onClose(ref: any): void {\r\n    ref.close();\r\n  }\r\n\r\n  onSubmit(ref: any): void {\r\n    if (!this.validateTemplateForm()) {\r\n      return;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateId) {\r\n      this.updateTemplate(ref);\r\n    } else {\r\n      this.createTemplate(ref);\r\n    }\r\n  }\r\n\r\n  // 驗證表單\r\n  validateTemplateForm(): boolean {\r\n    if (!this.templateDetail.CTemplateName?.trim()) {\r\n      this.message.showErrorMSG('請輸入模板名稱');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\r\n      this.message.showErrorMSG('請選擇模板類型');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\r\n      this.message.showErrorMSG('請選擇模板狀態');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\r\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 建立模板\r\n  createTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          const templateId = parseInt(response.Entries, 10);\r\n          this.saveTemplateDetails(templateId, ref);\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '建立模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 更新模板\r\n  updateTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateId: this.templateDetail.CTemplateId,\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.message.showSucessMSG('更新模板成功');\r\n          ref.close();\r\n          this.loadTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '更新模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 儲存模板詳細資料（關聯空間）\r\n  saveTemplateDetails(templateId: number, ref: any): void {\r\n    // 目前 API 可能不支援模板詳細資料的保存，暫時跳過這個步驟\r\n    this.message.showSucessMSG('建立模板成功');\r\n    ref.close();\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 刪除模板\r\n  deleteTemplate(template: TemplateItem): void {\r\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\r\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\r\n        body: { CTemplateId: template.CTemplateId }\r\n      }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('刪除模板成功');\r\n            this.loadTemplateList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '刪除模板失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    }\r\n  }\r\n\r\n  // 查看模板明細\r\n  viewTemplateDetail(template: TemplateItem, modal: TemplateRef<any>): void {\r\n    this.selectedTemplateDetail = template;\r\n    this.isLoadingTemplateDetail = true;\r\n    this.templateDetailSpaces = [];\r\n\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n\r\n    const request: GetTemplateDetailByIdArgs = {\r\n      templateId: template.CTemplateId\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        this.isLoadingTemplateDetail = false;\r\n        if (response.StatusCode === 0) {\r\n          this.templateDetailSpaces = response.Entries?.map(item => ({\r\n            CReleateId: item.CReleateId!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation\r\n          })) || [];\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板明細失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間選擇相關方法\r\n  toggleSpaceSelection(space: SpacePickListItem): void {\r\n    space.selected = !space.selected;\r\n\r\n    if (space.selected) {\r\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n        this.selectedSpacesForTemplate.push({ ...space });\r\n      }\r\n    } else {\r\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  toggleAllSpaces(): void {\r\n    this.allSpacesSelected = !this.allSpacesSelected;\r\n\r\n    this.availableSpaces.forEach(space => {\r\n      space.selected = this.allSpacesSelected;\r\n      if (this.allSpacesSelected) {\r\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n          this.selectedSpacesForTemplate.push({ ...space });\r\n        }\r\n      } else {\r\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedSpace(space: SpacePickListItem): void {\r\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n\r\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\r\n    if (availableSpace) {\r\n      availableSpace.selected = false;\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  updateAllSpacesSelectedState(): void {\r\n    this.allSpacesSelected = this.availableSpaces.length > 0 &&\r\n      this.availableSpaces.every(space => space.selected);\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,qCAAqC;AACnE,SAASC,SAAS,EAAUC,SAAS,QAAqB,eAAe;AACzE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gCAAgC;AAG7D,SAASC,mBAAmB,QAAQ,kDAAkD;AAItF,SAASC,GAAG,QAAQ,MAAM;AAO1B,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;AAuCxF,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAkB,SAAQT,aAAa;EASlDU,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,gBAAiC,EACjCC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAdf,KAAAC,IAAI,GAAGA,IAAI,CAAC,CAAC;IACb,KAAAV,gBAAgB,GAAGA,gBAAgB,CAAC,CAAC;IACrC,KAAAC,sBAAsB,GAAGA,sBAAsB,CAAC,CAAC;IAiBxC,KAAAU,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB;IACA,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAAC,cAAc,GAAqB,EAAE;IACrC,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAkB,IAAI;IAElC;IACA,KAAAC,eAAe,GAAwB,EAAE;IACzC,KAAAC,yBAAyB,GAAwB,EAAE;IACnD,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,mBAAmB,GAAW,EAAE;IAChC,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,iBAAiB,GAAG,KAAK;IAEzB;IACA,KAAAC,sBAAsB,GAAwB,IAAI;IAClD,KAAAC,oBAAoB,GAA8B,EAAE;IACpD,KAAAC,uBAAuB,GAAG,KAAK;EA1B/B;EA4BSC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAD,gBAAgBA,CAAA;IACd,MAAME,OAAO,GAAG;MACdC,aAAa,EAAE,IAAI,CAACjB,aAAa,IAAI,IAAI;MACzCkB,OAAO,EAAE,IAAI,CAACjB,YAAY;MAC1BkB,SAAS,EAAE,IAAI,CAACvB,SAAS;MACzBwB,QAAQ,EAAE,IAAI,CAACzB;KAChB;IAED,IAAI,CAACN,gBAAgB,CAACgC,mCAAmC,CAAC;MAAEC,IAAI,EAAEN;IAAO,CAAE,CAAC,CAACO,IAAI,CAC/EzC,GAAG,CAAC0C,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAAC3B,YAAY,GAAG0B,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACjDC,WAAW,EAAED,IAAI,CAACC,WAAY;UAC9BZ,aAAa,EAAEW,IAAI,CAACX,aAAc;UAClCa,aAAa,EAAEF,IAAI,CAACE,aAAa;UAAE;UACnCC,SAAS,EAAEH,IAAI,CAACG,SAAU;UAC1BC,SAAS,EAAEJ,IAAI,CAACI,SAAU;UAC1BC,QAAQ,EAAEL,IAAI,CAACK,QAAQ;UACvBC,QAAQ,EAAEN,IAAI,CAACM,QAAQ;UACvBhB,OAAO,EAAEU,IAAI,CAACV;SACf,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAACrB,YAAY,GAAG2B,QAAQ,CAACW,UAAU,IAAI,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAAC5C,OAAO,CAAC6C,YAAY,CAACZ,QAAQ,CAACa,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAvB,mBAAmBA,CAAA;IACjB,MAAMC,OAAO,GAAG;MACduB,KAAK,EAAE,IAAI,CAACnC,kBAAkB,IAAI,IAAI;MACtCoC,SAAS,EAAE,IAAI,CAACnC,mBAAmB,IAAI,IAAI;MAC3Ca,OAAO,EAAE,CAAC;MAAE;MACZC,SAAS,EAAE,IAAI,CAACb,cAAc;MAC9Bc,QAAQ,EAAE,IAAI,CAACb;KAChB;IAED,IAAI,CAACjB,aAAa,CAACmD,6BAA6B,CAAC;MAAEnB,IAAI,EAAEN;IAAO,CAAE,CAAC,CAACO,IAAI,CACtEzC,GAAG,CAAC0C,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACvB,eAAe,GAAGsB,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDc,QAAQ,EAAEd,IAAI,CAACc,QAAS;UACxBH,KAAK,EAAEX,IAAI,CAACW,KAAM;UAClBC,SAAS,EAAEZ,IAAI,CAACY,SAAS;UACzBG,QAAQ,EAAE,IAAI,CAACxC,yBAAyB,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKd,IAAI,CAACc,QAAQ;SAChF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAClC,iBAAiB,GAAGgB,QAAQ,CAACW,UAAU,IAAI,CAAC;QACjD,IAAI,CAACW,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,CACH,CAACR,SAAS,EAAE;EACf;EAEA;EACAS,QAAQA,CAAA;IACN,IAAI,CAACnD,SAAS,GAAG,CAAC;IAClB,IAAI,CAACkB,gBAAgB,EAAE;EACzB;EAEAkC,OAAOA,CAAA;IACL,IAAI,CAAChD,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACL,SAAS,GAAG,CAAC;IAClB,IAAI,CAACkB,gBAAgB,EAAE;EACzB;EAEA;EACAmC,aAAaA,CAAA;IACX,IAAI,CAAC3C,cAAc,GAAG,CAAC;IACvB,IAAI,CAACS,mBAAmB,EAAE;EAC5B;EAEAmC,YAAYA,CAAA;IACV,IAAI,CAAC9C,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACS,mBAAmB,EAAE;EAC5B;EAEA;EACAoC,WAAWA,CAACC,IAAY;IACtB,IAAI,CAACxD,SAAS,GAAGwD,IAAI;IACrB,IAAI,CAACtC,gBAAgB,EAAE;EACzB;EAEAuC,gBAAgBA,CAACD,IAAY;IAC3B,IAAI,CAAC9C,cAAc,GAAG8C,IAAI;IAC1B,IAAI,CAACrC,mBAAmB,EAAE;EAC5B;EAEA;EACAuC,eAAeA,CAACC,KAAuB;IACrC,IAAI,CAACxD,cAAc,GAAG;MACpBmB,OAAO,EAAE,CAAC;MACVY,aAAa,EAAE/C,gBAAgB,CAACyE,aAAa,CAAC;KAC/C;IACD,IAAI,CAACrD,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAACY,mBAAmB,EAAE;IAC1B,IAAI,CAAC3B,aAAa,CAACqE,IAAI,CAACF,KAAK,EAAE;MAC7BG,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAACL,KAAuB,EAAEM,QAAsB;IAC3D,IAAI,CAAC9D,cAAc,GAAG;MACpB8B,WAAW,EAAEgC,QAAQ,CAAChC,WAAW;MACjCZ,aAAa,EAAE4C,QAAQ,CAAC5C,aAAa;MACrCa,aAAa,EAAE+B,QAAQ,CAAC/B,aAAa,IAAI/C,gBAAgB,CAACyE,aAAa;MACvEtC,OAAO,EAAE2C,QAAQ,CAAC3C,OAAO,IAAI;KAC9B;IACD,IAAI,CAAC9B,aAAa,CAACqE,IAAI,CAACF,KAAK,EAAE;MAC7BG,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAG,OAAOA,CAACC,GAAQ;IACdA,GAAG,CAACC,KAAK,EAAE;EACb;EAEAC,QAAQA,CAACF,GAAQ;IACf,IAAI,CAAC,IAAI,CAACG,oBAAoB,EAAE,EAAE;MAChC;IACF;IAEA,IAAI,IAAI,CAACnE,cAAc,CAAC8B,WAAW,EAAE;MACnC,IAAI,CAACsC,cAAc,CAACJ,GAAG,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACK,cAAc,CAACL,GAAG,CAAC;IAC1B;EACF;EAEA;EACAG,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACnE,cAAc,CAACkB,aAAa,EAAEoD,IAAI,EAAE,EAAE;MAC9C,IAAI,CAAC9E,OAAO,CAAC6C,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACrC,cAAc,CAAC+B,aAAa,KAAKwC,SAAS,IAAI,IAAI,CAACvE,cAAc,CAAC+B,aAAa,KAAK,IAAI,EAAE;MACjG,IAAI,CAACvC,OAAO,CAAC6C,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACrC,cAAc,CAACmB,OAAO,KAAKoD,SAAS,IAAI,IAAI,CAACvE,cAAc,CAACmB,OAAO,KAAK,IAAI,EAAE;MACrF,IAAI,CAAC3B,OAAO,CAAC6C,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAACrC,cAAc,CAAC8B,WAAW,IAAI,IAAI,CAAC9B,cAAc,CAAC+B,aAAa,KAAK/C,gBAAgB,CAACyE,aAAa,IAAI,IAAI,CAACrD,yBAAyB,CAACoE,MAAM,KAAK,CAAC,EAAE;MAC3J,IAAI,CAAChF,OAAO,CAAC6C,YAAY,CAAC,eAAe,CAAC;MAC1C,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;EAEA;EACAgC,cAAcA,CAACL,GAAQ;IACrB,MAAMS,YAAY,GAAqB;MACrCvD,aAAa,EAAE,IAAI,CAAClB,cAAc,CAACkB,aAAa;MAChDa,aAAa,EAAE,IAAI,CAAC/B,cAAc,CAAC+B,aAAa;MAChDZ,OAAO,EAAE,IAAI,CAACnB,cAAc,CAACmB;KAC9B;IAED,IAAI,CAAC7B,gBAAgB,CAACoF,gCAAgC,CAAC;MAAEnD,IAAI,EAAEkD;IAAY,CAAE,CAAC,CAACjD,IAAI,CACjFzC,GAAG,CAAC0C,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;QACjD,MAAMgD,UAAU,GAAGC,QAAQ,CAACnD,QAAQ,CAACE,OAAO,EAAE,EAAE,CAAC;QACjD,IAAI,CAACkD,mBAAmB,CAACF,UAAU,EAAEX,GAAG,CAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAACxE,OAAO,CAAC6C,YAAY,CAACZ,QAAQ,CAACa,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACA6B,cAAcA,CAACJ,GAAQ;IACrB,MAAMS,YAAY,GAAqB;MACrC3C,WAAW,EAAE,IAAI,CAAC9B,cAAc,CAAC8B,WAAW;MAC5CZ,aAAa,EAAE,IAAI,CAAClB,cAAc,CAACkB,aAAa;MAChDa,aAAa,EAAE,IAAI,CAAC/B,cAAc,CAAC+B,aAAa;MAChDZ,OAAO,EAAE,IAAI,CAACnB,cAAc,CAACmB;KAC9B;IAED,IAAI,CAAC7B,gBAAgB,CAACoF,gCAAgC,CAAC;MAAEnD,IAAI,EAAEkD;IAAY,CAAE,CAAC,CAACjD,IAAI,CACjFzC,GAAG,CAAC0C,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAAClC,OAAO,CAACsF,aAAa,CAAC,QAAQ,CAAC;QACpCd,GAAG,CAACC,KAAK,EAAE;QACX,IAAI,CAAClD,gBAAgB,EAAE;MACzB,CAAC,MAAM;QACL,IAAI,CAACvB,OAAO,CAAC6C,YAAY,CAACZ,QAAQ,CAACa,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAsC,mBAAmBA,CAACF,UAAkB,EAAEX,GAAQ;IAC9C;IACA,IAAI,CAACxE,OAAO,CAACsF,aAAa,CAAC,QAAQ,CAAC;IACpCd,GAAG,CAACC,KAAK,EAAE;IACX,IAAI,CAAClD,gBAAgB,EAAE;EACzB;EAEA;EACAgE,cAAcA,CAACjB,QAAsB;IACnC,IAAIkB,OAAO,CAAC,WAAWlB,QAAQ,CAAC5C,aAAa,KAAK,CAAC,EAAE;MACnD,IAAI,CAAC5B,gBAAgB,CAAC2F,kCAAkC,CAAC;QACvD1D,IAAI,EAAE;UAAEO,WAAW,EAAEgC,QAAQ,CAAChC;QAAW;OAC1C,CAAC,CAACN,IAAI,CACLzC,GAAG,CAAC0C,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAAClC,OAAO,CAACsF,aAAa,CAAC,QAAQ,CAAC;UACpC,IAAI,CAAC/D,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAACvB,OAAO,CAAC6C,YAAY,CAACZ,QAAQ,CAACa,OAAO,IAAI,QAAQ,CAAC;QACzD;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf;EACF;EAEA;EACA2C,kBAAkBA,CAACpB,QAAsB,EAAEN,KAAuB;IAChE,IAAI,CAAC7C,sBAAsB,GAAGmD,QAAQ;IACtC,IAAI,CAACjD,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACD,oBAAoB,GAAG,EAAE;IAE9B,IAAI,CAACvB,aAAa,CAACqE,IAAI,CAACF,KAAK,EAAE;MAC7BG,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;IAEF,MAAM3C,OAAO,GAA8B;MACzC0D,UAAU,EAAEb,QAAQ,CAAChC;KACtB;IAED,IAAI,CAACxC,gBAAgB,CAAC6F,yCAAyC,CAAC;MAAE5D,IAAI,EAAEN;IAAO,CAAE,CAAC,CAACO,IAAI,CACrFzC,GAAG,CAAC0C,QAAQ,IAAG;MACb,IAAI,CAACZ,uBAAuB,GAAG,KAAK;MACpC,IAAIY,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACd,oBAAoB,GAAGa,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACzDuD,UAAU,EAAEvD,IAAI,CAACuD,UAAW;UAC5B5C,KAAK,EAAEX,IAAI,CAACW,KAAM;UAClBC,SAAS,EAAEZ,IAAI,CAACY;SACjB,CAAC,CAAC,IAAI,EAAE;MACX,CAAC,MAAM;QACL,IAAI,CAACjD,OAAO,CAAC6C,YAAY,CAACZ,QAAQ,CAACa,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACA8C,oBAAoBA,CAACC,KAAwB;IAC3CA,KAAK,CAAC1C,QAAQ,GAAG,CAAC0C,KAAK,CAAC1C,QAAQ;IAEhC,IAAI0C,KAAK,CAAC1C,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAACxC,yBAAyB,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAK2C,KAAK,CAAC3C,QAAQ,CAAC,EAAE;QAC5E,IAAI,CAACvC,yBAAyB,CAACmF,IAAI,CAAC;UAAE,GAAGD;QAAK,CAAE,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAAClF,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACoF,MAAM,CAAC1C,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAK2C,KAAK,CAAC3C,QAAQ,CAAC;IAC5G;IAEA,IAAI,CAACI,4BAA4B,EAAE;EACrC;EAEA0C,eAAeA,CAAA;IACb,IAAI,CAAC/E,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAEhD,IAAI,CAACP,eAAe,CAACuF,OAAO,CAACJ,KAAK,IAAG;MACnCA,KAAK,CAAC1C,QAAQ,GAAG,IAAI,CAAClC,iBAAiB;MACvC,IAAI,IAAI,CAACA,iBAAiB,EAAE;QAC1B,IAAI,CAAC,IAAI,CAACN,yBAAyB,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAK2C,KAAK,CAAC3C,QAAQ,CAAC,EAAE;UAC5E,IAAI,CAACvC,yBAAyB,CAACmF,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACnD;MACF,CAAC,MAAM;QACL,IAAI,CAAClF,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACoF,MAAM,CAAC1C,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAK2C,KAAK,CAAC3C,QAAQ,CAAC;MAC5G;IACF,CAAC,CAAC;EACJ;EAEAgD,mBAAmBA,CAACL,KAAwB;IAC1C,IAAI,CAAClF,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACoF,MAAM,CAAC1C,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAK2C,KAAK,CAAC3C,QAAQ,CAAC;IAE1G,MAAMiD,cAAc,GAAG,IAAI,CAACzF,eAAe,CAAC0F,IAAI,CAAC/C,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAK2C,KAAK,CAAC3C,QAAQ,CAAC;IACpF,IAAIiD,cAAc,EAAE;MAClBA,cAAc,CAAChD,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAACG,4BAA4B,EAAE;EACrC;EAEAA,4BAA4BA,CAAA;IAC1B,IAAI,CAACrC,iBAAiB,GAAG,IAAI,CAACP,eAAe,CAACqE,MAAM,GAAG,CAAC,IACtD,IAAI,CAACrE,eAAe,CAAC2F,KAAK,CAACR,KAAK,IAAIA,KAAK,CAAC1C,QAAQ,CAAC;EACvD;CACD;AA/V8CmD,UAAA,EAA5CpH,SAAS,CAAC,aAAa,EAAE;EAAEqH,MAAM,EAAE;AAAK,CAAE,CAAC,C,qDAAgC;AACjCD,UAAA,EAA1CpH,SAAS,CAAC,WAAW,EAAE;EAAEqH,MAAM,EAAE;AAAK,CAAE,CAAC,C,mDAA8B;AACnBD,UAAA,EAApDpH,SAAS,CAAC,qBAAqB,EAAE;EAAEqH,MAAM,EAAE;AAAK,CAAE,CAAC,C,6DAAwC;AAPjF9G,iBAAiB,GAAA6G,UAAA,EAX7BrH,SAAS,CAAC;EACTuH,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,2BAA2B;EACxCC,SAAS,EAAE,CAAC,2BAA2B,CAAC;EACxCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPzH,YAAY,EACZC,YAAY,EACZC,mBAAmB;CAEtB,CAAC,C,EACWI,iBAAiB,CAoW7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}