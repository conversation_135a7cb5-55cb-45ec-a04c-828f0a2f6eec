.household-binding-container {
  position: relative;
  width: 100%;

  // 載入動畫樣式
  .spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  .selected-households-area {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;

    .selected-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.75rem;

      .selected-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .selected-count {
          font-size: 0.875rem;
          font-weight: 500;
          color: #495057;
        }
      }
    }

    .selected-content {
      .building-group {
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
        margin-bottom: 0.5rem;

        &:last-child {
          margin-bottom: 0;
        }

        .building-label {
          min-width: 80px;
          font-weight: 500;
          color: #495057;
          font-size: 0.875rem;
        }

        .households-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 0.25rem;
          flex: 1;

          .household-tag {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            background-color: #e3f2fd;
            color: #1976d2;
            font-size: 0.75rem;
            border-radius: 0.25rem;
            border: 1px solid #bbdefb;
            transition: all 0.2s ease;

            &:hover {
              background-color: #bbdefb;
            }

            .household-info {
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              gap: 0.1rem;
              line-height: 1.2;
              min-width: 0;

              .household-code {
                font-weight: 600;
                color: #0d47a1;
              }

              .household-floor {
                font-size: 0.7rem;
                font-weight: 600;
                color: #ffffff;
                background-color: #28a745;
                padding: 0.15rem 0.4rem;
                border-radius: 0.25rem;
                min-width: fit-content;
                text-align: center;
                letter-spacing: 0.02em;
              }
            }

            .remove-btn {
              background: rgba(255, 255, 255, 0.9);
              border: 1px solid #90caf9;
              padding: 0.1rem;
              margin: 0;
              cursor: pointer;
              color: #0d47a1;
              border-radius: 0.25rem;
              width: 1.2rem;
              height: 1.2rem;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.2s ease;
              flex-shrink: 0;

              &:hover {
                background-color: #f44336;
                border-color: #f44336;
                color: white;
              }

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                  background-color: rgba(255, 255, 255, 0.9);
                  border-color: #90caf9;
                  color: #0d47a1;
                }
              }

              nb-icon {
                font-size: 0.75rem;
                line-height: 1;
              }
            }
          }
        }
      }
    }
  }

  .selector-container {
    position: relative;

    .selector-button {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.5rem 0.75rem;
      border: 1px solid #ced4da;
      border-radius: 0.375rem;
      background-color: #fff;
      cursor: pointer;
      transition: all 0.15s ease;

      &:hover:not(.disabled) {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }

      &:focus {
        outline: none;
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }

      &.disabled {
        opacity: 0.6;
        cursor: not-allowed !important;
        background-color: #e9ecef;
      }

      .selector-text {
        color: #495057;
        font-size: 0.875rem;
      }

      .chevron-icon {
        transition: transform 0.15s ease;
        color: #6c757d;

        &.rotated {
          transform: rotate(180deg);
        }
      }
    }
  }
}

// 扁平化樣式類別
.flat-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;

  &.building {
    background-color: #007bff;
    color: white;
  }

  &.floor {
    background-color: #28a745;
    color: white;
  }

  &.search {
    background-color: #e9ecef;
    color: #6c757d;
  }

  &.selected {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
  }
}

// 扁平化戶別標籤
.flat-household-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background-color: #e3f2fd;
  color: #1976d2;
  font-size: 0.75rem;
  border-radius: 4px;
  border: 1px solid #bbdefb;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #bbdefb;
  }

  .household-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.1rem;
    line-height: 1.2;
    min-width: 0;

    .household-code {
      font-weight: 600;
      color: #0d47a1;
    }

    .household-floor {
      font-size: 0.7rem;
      font-weight: 600;
      color: #ffffff;
      background-color: #28a745;
      padding: 0.15rem 0.4rem;
      border-radius: 4px;
      min-width: fit-content;
      text-align: center;
      letter-spacing: 0.02em;
    }
  }

  .remove-btn {
    background: #ffffff;
    border: 1px solid #90caf9;
    padding: 0.1rem;
    margin: 0;
    cursor: pointer;
    color: #0d47a1;
    border-radius: 4px;
    width: 1.2rem;
    height: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;

    &:hover {
      background-color: #f44336;
      border-color: #f44336;
      color: white;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: #ffffff;
        border-color: #90caf9;
        color: #0d47a1;
      }
    }

    nb-icon {
      font-size: 0.75rem;
      line-height: 1;
    }
  }
}

// 扁平化按鈕樣式
.flat-button {
  border: 1px solid;
  border-radius: 4px;
  transition: all 0.2s ease;
  cursor: pointer;

  &.primary {
    background-color: #007bff;
    color: white;
    border-color: #007bff;

    &:hover {
      background-color: #0056b3;
      border-color: #0056b3;
    }

    &.selected {
      background-color: #0056b3;
      border-color: #0056b3;
    }
  }

  &.success {
    background-color: #28a745;
    color: white;
    border-color: #28a745;

    &:hover {
      background-color: #1e7e34;
      border-color: #1e7e34;
    }

    &.selected {
      background-color: #1e7e34;
      border-color: #1e7e34;
    }
  }

  &.light {
    background-color: #f8f9fa;
    color: #495057;
    border-color: #ced4da;

    &:hover {
      background-color: #e9ecef;
      border-color: #adb5bd;
    }

    &.selected {
      background-color: #007bff;
      color: white;
      border-color: #007bff;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      opacity: 0.6;
    }
  }
}

// 響應式設計
@media (max-width: 768px) {
  .household-binding-container {
    .selected-households-area {
      .selected-content {
        .building-group {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.25rem;

          .building-label {
            min-width: auto;
          }

          .households-tags {
            width: 100%;

            .household-tag {
              .household-info {
                .household-floor {
                  font-size: 0.6rem;
                }
              }
            }
          }
        }
      }
    }

    .selector-container {
      .selector-button {
        padding: 0.75rem;

        .selector-text {
          font-size: 1rem;
        }
      }
    }
  }
}

// 深色主題支援
@media (prefers-color-scheme: dark) {
  .household-binding-container {
    .selected-households-area {
      background-color: #343a40;
      border-color: #495057;
      color: #f8f9fa;

      .selected-header {
        .selected-info {
          .selected-count {
            color: #f8f9fa;
          }
        }
      }

      .selected-content {
        .building-group {
          .building-label {
            color: #adb5bd;
          }

          .households-tags {
            .household-tag {
              background-color: #495057;
              color: #bbdefb;
              border-color: #6c757d;

              &:hover {
                background-color: #5a6268;
              }

              .household-info {
                .household-code {
                  color: #bbdefb;
                }

                .household-floor {
                  color: #90caf9;
                  background-color: rgba(0, 0, 0, 0.2);
                }
              }

              .remove-btn {
                background: rgba(0, 0, 0, 0.3);
                border-color: #6c757d;
                color: #bbdefb;

                &:hover {
                  background-color: #f44336;
                  border-color: #f44336;
                  color: white;
                }
              }
            }
          }
        }
      }
    }

    .selector-container {
      .selector-button {
        background-color: #495057;
        border-color: #6c757d;
        color: #f8f9fa;

        &:hover:not(.disabled) {
          background-color: #5a6268;
        }

        &.disabled {
          background-color: #6c757d;
        }

        .selector-text {
          color: #f8f9fa;
        }

        .chevron-icon {
          color: #adb5bd;
        }
      }
    }
  }
}

// [優化] 新增的摘要和 Popover 樣式
.selected-households-summary {
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.1);
  }

  .summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;

    .summary-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .summary-count {
        font-size: 0.875rem;
        font-weight: 500;
        color: #495057;
      }
    }
  }

  .summary-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background-color: #ffffff;
    border-radius: 0.25rem;

    .summary-text {
      color: #1976d2;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .info-icon {
      color: #6c757d;
    }
  }
}

.details-popover-content {
  max-height: 300px;
  overflow: hidden;
  padding: 0.5rem;

  .details-list {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 280px;
    overflow-y: auto;

    .details-list-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem;
      border-bottom: 1px solid #e9ecef;

      &:last-child {
        border-bottom: none;
      }

      .building-name {
        font-weight: 600;
        color: #495057;
        min-width: 50px;
      }

      .house-name {
        color: #0d47a1;
      }

      .floor-badge {
        font-size: 0.7rem;
        font-weight: 600;
        color: #ffffff;
        background-color: #28a745;
        padding: 0.15rem 0.4rem;
        border-radius: 0.25rem;
        margin-left: auto;
      }
    }
  }
}
