{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\n// Leap year occures every 4 years, except for years that are divisable by 100 and not divisable by 400.\n// 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\nvar daysInYear = 365.2425;\n\n/**\n * @name milliseconds\n * @category Millisecond Helpers\n * @summary\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * @description\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * One years equals 365.2425 days according to the formula:\n *\n * > Leap year occures every 4 years, except for years that are divisable by 100 and not divisable by 400.\n * > 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\n *\n * One month is a year divided by 12.\n *\n * @param {Duration} duration - the object with years, months, weeks, days, hours, minutes and seconds to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {number} the milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // 1 year in milliseconds\n * milliseconds({ years: 1 })\n * //=> 31556952000\n *\n * // 3 months in milliseconds\n * milliseconds({ months: 3 })\n * //=> 7889238000\n */\nexport default function milliseconds(_ref) {\n  var years = _ref.years,\n    months = _ref.months,\n    weeks = _ref.weeks,\n    days = _ref.days,\n    hours = _ref.hours,\n    minutes = _ref.minutes,\n    seconds = _ref.seconds;\n  requiredArgs(1, arguments);\n  var totalDays = 0;\n  if (years) totalDays += years * daysInYear;\n  if (months) totalDays += months * (daysInYear / 12);\n  if (weeks) totalDays += weeks * 7;\n  if (days) totalDays += days;\n  var totalSeconds = totalDays * 24 * 60 * 60;\n  if (hours) totalSeconds += hours * 60 * 60;\n  if (minutes) totalSeconds += minutes * 60;\n  if (seconds) totalSeconds += seconds;\n  return Math.round(totalSeconds * 1000);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}