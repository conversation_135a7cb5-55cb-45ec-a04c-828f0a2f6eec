{"ast": null, "code": "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { ListView } from './internal.js';\nimport { identity, createFormatter } from '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\nconst OPTION_REFINERS = {\n  listDayFormat: createFalsableFormatter,\n  listDaySideFormat: createFalsableFormatter,\n  noEventsClassNames: identity,\n  noEventsContent: identity,\n  noEventsDidMount: identity,\n  noEventsWillUnmount: identity\n  // noEventsText is defined in base options\n};\nfunction createFalsableFormatter(input) {\n  return input === false ? null : createFormatter(input);\n}\nvar index = createPlugin({\n  name: '@fullcalendar/list',\n  optionRefiners: OPTION_REFINERS,\n  views: {\n    list: {\n      component: ListView,\n      buttonTextKey: 'list',\n      listDayFormat: {\n        month: 'long',\n        day: 'numeric',\n        year: 'numeric'\n      } // like \"January 1, 2016\"\n    },\n    listDay: {\n      type: 'list',\n      duration: {\n        days: 1\n      },\n      listDayFormat: {\n        weekday: 'long'\n      } // day-of-week is all we need. full date is probably in headerToolbar\n    },\n    listWeek: {\n      type: 'list',\n      duration: {\n        weeks: 1\n      },\n      listDayFormat: {\n        weekday: 'long'\n      },\n      listDaySideFormat: {\n        month: 'long',\n        day: 'numeric',\n        year: 'numeric'\n      }\n    },\n    listMonth: {\n      type: 'list',\n      duration: {\n        month: 1\n      },\n      listDaySideFormat: {\n        weekday: 'long'\n      } // day-of-week is nice-to-have\n    },\n    listYear: {\n      type: 'list',\n      duration: {\n        year: 1\n      },\n      listDaySideFormat: {\n        weekday: 'long'\n      } // day-of-week is nice-to-have\n    }\n  }\n});\nexport { index as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}