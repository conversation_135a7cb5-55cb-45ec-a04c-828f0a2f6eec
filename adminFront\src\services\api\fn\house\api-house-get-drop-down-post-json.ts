/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { StringHouseDropDownItemListDictionaryResponseBase } from '../../models/string-house-drop-down-item-list-dictionary-response-base';

export interface ApiHouseGetDropDownPost$Json$Params {
  buildCaseId?: number;
}

export function apiHouseGetDropDownPost$Json(http: HttpClient, rootUrl: string, params?: ApiHouseGetDropDownPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringHouseDropDownItemListDictionaryResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiHouseGetDropDownPost$Json.PATH, 'post');
  if (params) {
    rb.query('buildCaseId', params.buildCaseId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<StringHouseDropDownItemListDictionaryResponseBase>;
    })
  );
}

apiHouseGetDropDownPost$Json.PATH = '/api/House/GetDropDown';
