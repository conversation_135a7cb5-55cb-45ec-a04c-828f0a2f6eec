/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { StringHouseDropDownItemListDictionaryResponseBase } from '../../models/string-house-drop-down-item-list-dictionary-response-base';

export interface ApiHouseGetDropDownPost$Plain$Params {
  buildCaseId?: number;
}

export function apiHouseGetDropDownPost$Plain(http: HttpClient, rootUrl: string, params?: ApiHouseGetDropDownPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringHouseDropDownItemListDictionaryResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiHouseGetDropDownPost$Plain.PATH, 'post');
  if (params) {
    rb.query('buildCaseId', params.buildCaseId, {});
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<StringHouseDropDownItemListDictionaryResponseBase>;
    })
  );
}

apiHouseGetDropDownPost$Plain.PATH = '/api/House/GetDropDown';
