{"ast": null, "code": "export var EnumTemplateType = /*#__PURE__*/function (EnumTemplateType) {\n  EnumTemplateType[EnumTemplateType[\"SpaceTemplate\"] = 1] = \"SpaceTemplate\";\n  EnumTemplateType[EnumTemplateType[\"ItemTemplate\"] = 2] = \"ItemTemplate\"; // 項目模板\n  return EnumTemplateType;\n}(EnumTemplateType || {});\nexport class EnumTemplateTypeHelper {\n  static getDisplayName(templateType) {\n    switch (templateType) {\n      case EnumTemplateType.SpaceTemplate:\n        return '空間模板';\n      case EnumTemplateType.ItemTemplate:\n        return '項目模板';\n      default:\n        return '未知';\n    }\n  }\n  static getTemplateTypeList() {\n    return [{\n      value: EnumTemplateType.SpaceTemplate,\n      label: '空間模板'\n    }, {\n      value: EnumTemplateType.ItemTemplate,\n      label: '項目模板'\n    }];\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}