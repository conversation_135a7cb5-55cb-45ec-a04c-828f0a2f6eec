{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"householdDialog\"];\nfunction HouseholdBindingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11);\n    i0.ɵɵelement(3, \"nb-icon\", 12);\n    i0.ɵɵelementStart(4, \"span\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_Template_button_click_6_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.onClearAll();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 15)(9, \"span\", 16);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"nb-icon\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const detailsPopover_r4 = i0.ɵɵreference(9);\n    i0.ɵɵproperty(\"nbPopover\", detailsPopover_r4);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.displayText.selectedPrefix, \" (\", ctx_r2.getSelectedCount(), \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.getGroupedSummary());\n  }\n}\nfunction HouseholdBindingComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nb-icon\", 18);\n    i0.ɵɵtext(2, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getSelectedCount() > 0 ? \"\\u5DF2\\u9078\\u64C7 \" + ctx_r2.getSelectedCount() + \" \" + ctx_r2.displayText.selectedCount : ctx_r2.placeholder || ctx_r2.displayText.placeholder, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_ng_container_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r5.floor);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 22)(2, \"span\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 24);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_8_ng_container_2_span_6_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r5.building);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.houseName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.useHouseNameMode && item_r5.floor);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"ul\", 20);\n    i0.ɵɵtemplate(2, HouseholdBindingComponent_ng_template_8_ng_container_2_Template, 7, 3, \"ng-container\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getAllSelectedItems());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52);\n    i0.ɵɵelement(2, \"nb-icon\", 53);\n    i0.ɵɵelementStart(3, \"p\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\\u8F09\\u5165\", ctx_r2.displayText.unitType, \"\\u8CC7\\u6599\\u4E2D...\");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_10_div_13_button_6_Template_button_click_0_listener() {\n      const building_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onBuildingSelect(building_r8));\n    });\n    i0.ɵɵelementStart(1, \"span\", 73);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 74);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.selectedBuilding === building_r8 ? \"#e3f2fd\" : \"transparent\")(\"border-left\", ctx_r2.selectedBuilding === building_r8 ? \"3px solid #007bff\" : \"3px solid transparent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r8);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getBuildingCount(building_r8), \"\\u6236 \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵelement(1, \"nb-icon\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedBuilding, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 77);\n    i0.ɵɵelement(1, \"nb-icon\", 78);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_15_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_10_div_13_div_15_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onUnselectAllBuilding());\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_10_div_13_div_15_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSelectAllFiltered());\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_10_div_13_div_15_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSelectAllBuilding());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_10_div_13_div_15_button_5_Template, 2, 0, \"button\", 82);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"opacity\", ctx_r2.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canSelectMore());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"opacity\", ctx_r2.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canSelectMore());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u9078\", ctx_r2.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSomeBuildingSelected());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.displayText.noResults, \" \\\"\", ctx_r2.searchTerm, \"\\\" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"div\", 85)(2, \"input\", 86);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingComponent_ng_template_10_div_13_div_16_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchTerm, $event) || (ctx_r2.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdBindingComponent_ng_template_10_div_13_div_16_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"nb-icon\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_ng_template_10_div_13_div_16_div_4_Template, 2, 2, \"div\", 88);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchTerm);\n    i0.ɵɵproperty(\"placeholder\", ctx_r2.displayText.searchPlaceholder);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm && ctx_r2.hasNoSearchResults());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_17_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 77);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_17_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_10_div_13_div_17_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onFloorSelect(\"\"));\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_17_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_10_div_13_div_17_button_8_Template_button_click_0_listener() {\n      const floor_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onFloorSelect(floor_r14));\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 99);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 100);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const floor_r14 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.selectedFloor === floor_r14 ? \"#007bff\" : \"#f8f9fa\")(\"color\", ctx_r2.selectedFloor === floor_r14 ? \"#fff\" : \"#495057\")(\"border\", ctx_r2.selectedFloor === floor_r14 ? \"2px solid #007bff\" : \"1px solid #dee2e6\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", floor_r14, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r2.getFloorCount(floor_r14), \")\");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"div\", 91);\n    i0.ɵɵelement(2, \"nb-icon\", 92);\n    i0.ɵɵelementStart(3, \"span\", 93);\n    i0.ɵɵtext(4, \"\\u6A13\\u5C64\\u7BE9\\u9078:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_10_div_13_div_17_span_5_Template, 2, 1, \"span\", 65)(6, HouseholdBindingComponent_ng_template_10_div_13_div_17_button_6_Template, 2, 0, \"button\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 95);\n    i0.ɵɵtemplate(8, HouseholdBindingComponent_ng_template_10_div_13_div_17_button_8_Template, 5, 8, \"button\", 96);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.floors);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵelement(1, \"nb-icon\", 102);\n    i0.ɵɵelementStart(2, \"p\", 54);\n    i0.ɵɵtext(3, \"\\u8ACB\\u5148\\u9078\\u64C7\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_20_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 108);\n    i0.ɵɵelement(1, \"nb-icon\", 109);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const household_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.isHouseholdSelected(household_r16.houseId) ? \"rgba(255,255,255,0.9)\" : \"#28a745\")(\"color\", ctx_r2.isHouseholdSelected(household_r16.houseId) ? \"#007bff\" : \"#fff\")(\"border\", ctx_r2.isHouseholdSelected(household_r16.houseId) ? \"1px solid rgba(0,123,255,0.3)\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", household_r16.floor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_20_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \" \\u2715 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_20_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_10_div_13_div_20_ng_container_1_Template_button_click_1_listener() {\n      const household_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onHouseholdToggle(household_r16.houseId));\n    });\n    i0.ɵɵelementStart(2, \"span\", 105);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_ng_template_10_div_13_div_20_ng_container_1_span_4_Template, 3, 7, \"span\", 106)(5, HouseholdBindingComponent_ng_template_10_div_13_div_20_ng_container_1_div_5_Template, 2, 0, \"div\", 107);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const household_r16 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.isHouseholdSelected(household_r16.houseId) ? \"#007bff\" : ctx_r2.isHouseholdExcluded(household_r16.houseId) ? \"#f8f9fa\" : \"#fff\")(\"color\", ctx_r2.isHouseholdSelected(household_r16.houseId) ? \"#fff\" : ctx_r2.isHouseholdExcluded(household_r16.houseId) ? \"#6c757d\" : \"#495057\")(\"border\", ctx_r2.isHouseholdSelected(household_r16.houseId) ? \"2px solid #007bff\" : ctx_r2.isHouseholdExcluded(household_r16.houseId) ? \"1px solid #dee2e6\" : \"1px solid #ced4da\")(\"opacity\", ctx_r2.isHouseholdDisabled(household_r16.houseId) ? \"0.6\" : \"1\")(\"cursor\", ctx_r2.isHouseholdDisabled(household_r16.houseId) ? \"not-allowed\" : \"pointer\");\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isHouseholdDisabled(household_r16.houseId));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"text-decoration\", ctx_r2.isHouseholdExcluded(household_r16.houseId) ? \"line-through\" : \"none\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", household_r16.houseName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.useHouseNameMode && household_r16.floor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isHouseholdExcluded(household_r16.houseId));\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_ng_template_10_div_13_div_20_ng_container_1_Template, 6, 16, \"ng-container\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getUniqueHouseholdsForDisplay());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵelement(1, \"nb-icon\", 111);\n    i0.ɵɵelementStart(2, \"p\", 54);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.displayText.noAvailable);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"div\", 57)(3, \"h6\", 58);\n    i0.ɵɵtext(4, \"\\u68DF\\u5225\\u5217\\u8868\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 59);\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_10_div_13_button_6_Template, 5, 6, \"button\", 60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 61)(8, \"div\", 57)(9, \"div\", 62)(10, \"div\", 29)(11, \"h6\", 63);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, HouseholdBindingComponent_ng_template_10_div_13_span_13_Template, 3, 1, \"span\", 64)(14, HouseholdBindingComponent_ng_template_10_div_13_span_14_Template, 3, 1, \"span\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, HouseholdBindingComponent_ng_template_10_div_13_div_15_Template, 6, 8, \"div\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, HouseholdBindingComponent_ng_template_10_div_13_div_16_Template, 5, 3, \"div\", 67)(17, HouseholdBindingComponent_ng_template_10_div_13_div_17_Template, 9, 3, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 69);\n    i0.ɵɵtemplate(19, HouseholdBindingComponent_ng_template_10_div_13_div_19_Template, 4, 0, \"div\", 70)(20, HouseholdBindingComponent_ng_template_10_div_13_div_20_Template, 2, 1, \"div\", 71)(21, HouseholdBindingComponent_ng_template_10_div_13_div_21_Template, 4, 1, \"div\", 70);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildings);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.displayText.unitSelection, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.useHouseNameMode && ctx_r2.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowBatchSelect && ctx_r2.selectedBuilding && ctx_r2.buildingData[ctx_r2.selectedBuilding] && ctx_r2.buildingData[ctx_r2.selectedBuilding].length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.useHouseNameMode && ctx_r2.selectedBuilding && ctx_r2.floors.length > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && ctx_r2.buildingData[ctx_r2.selectedBuilding] && ctx_r2.buildingData[ctx_r2.selectedBuilding].length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && (!ctx_r2.buildingData[ctx_r2.selectedBuilding] || ctx_r2.buildingData[ctx_r2.selectedBuilding].length === 0) && !ctx_r2.searchTerm);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"nb-icon\", 112);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u9650\\u5236: \\u6700\\u591A \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" \\u500B\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.maxSelections);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_25_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 116);\n    i0.ɵɵelement(1, \"nb-icon\", 78);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"nb-icon\", 113);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u7576\\u524D\\u68DF\\u5225: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 114);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_10_div_25_span_6_Template, 3, 1, \"span\", 115);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.useHouseNameMode && ctx_r2.selectedFloor);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u641C\\u5C0B: \\\"\", ctx_r2.searchTerm, \"\\\" (\", ctx_r2.getFilteredHouseholdsCount(), \" \\u500B\\u7D50\\u679C) \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_10_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onClearAll());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 119);\n    i0.ɵɵtext(2, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_10_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.resetSearch());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 121);\n    i0.ɵɵtext(2, \" \\u91CD\\u7F6E\\u641C\\u5C0B \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 27)(1, \"nb-card-header\")(2, \"div\", 28)(3, \"div\", 29);\n    i0.ɵɵelement(4, \"nb-icon\", 30);\n    i0.ɵɵelementStart(5, \"span\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 32);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 32);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 33);\n    i0.ɵɵtemplate(12, HouseholdBindingComponent_ng_template_10_div_12_Template, 5, 1, \"div\", 34)(13, HouseholdBindingComponent_ng_template_10_div_13_Template, 22, 10, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 36)(15, \"div\", 37)(16, \"div\", 38)(17, \"div\", 39);\n    i0.ɵɵelement(18, \"nb-icon\", 40);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"\\u5DF2\\u9078\\u64C7: \");\n    i0.ɵɵelementStart(21, \"strong\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, HouseholdBindingComponent_ng_template_10_div_24_Template, 7, 1, \"div\", 41)(25, HouseholdBindingComponent_ng_template_10_div_25_Template, 7, 2, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, HouseholdBindingComponent_ng_template_10_div_26_Template, 2, 2, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 44)(28, \"div\", 45);\n    i0.ɵɵtemplate(29, HouseholdBindingComponent_ng_template_10_button_29_Template, 3, 0, \"button\", 46)(30, HouseholdBindingComponent_ng_template_10_button_30_Template, 3, 0, \"button\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 45)(32, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_10_Template_button_click_32_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r6).dialogRef;\n      return i0.ɵɵresetView(ref_r19.close());\n    });\n    i0.ɵɵtext(33, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_10_Template_button_click_34_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r6).dialogRef;\n      return i0.ɵɵresetView(ref_r19.close());\n    });\n    i0.ɵɵelement(35, \"nb-icon\", 50);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.displayText.selectUnit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r2.buildings.length, \" \\u500B\\u68DF\\u5225)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx_r2.getSelectedCount(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.getSelectedCount());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.displayText.selectedCount, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.maxSelections);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedHouseIds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r2.getSelectedCount(), \") \");\n  }\n}\nexport class HouseholdBindingComponent {\n  constructor(cdr, dialogService) {\n    this.cdr = cdr;\n    this.dialogService = dialogService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 建案ID（用於識別）\n    this.buildingData = {};\n    this.allowBatchSelect = true;\n    this.excludedHouseIds = []; // 改為：排除的戶別ID（已被其他元件選擇）\n    this.useHouseNameMode = false; // 新增：使用戶別名稱模式\n    this.selectionChange = new EventEmitter();\n    this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n    this.houseNameChange = new EventEmitter(); // 新增：useHouseNameMode 時回傳戶別名稱陣列\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseIds = []; // 改為：使用 houseId 作為選擇的key\n    this.selectedHouseNames = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列\n    this.buildings = [];\n    this.floors = []; // 新增：當前棧別的樓層列表\n    this.filteredHouseholds = []; // 保持為字串陣列用於UI顯示\n    this.selectedByBuilding = {}; // 改為：儲存 houseId\n    this.isLoading = false; // 新增：載入狀態  // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  /**\n   * [優化] 產生已選戶別的分組摘要文字。\n   * 用於簡潔地顯示選擇結果。\n   * @returns string - 例如: \"棟 A (10戶), 棟 B (5戶)\"\n   */\n  getGroupedSummary() {\n    if (this.selectedHouseIds.length === 0) {\n      return '';\n    }\n    return this.buildings.map(building => {\n      const count = this.selectedByBuilding[building]?.length || 0;\n      return count > 0 ? `${building} (${count}戶)` : null;\n    }).filter(Boolean) // 過濾掉沒有選擇的棟別\n    .join(', ');\n  }\n  /**\n   * [優化] 獲取所有已選戶別的詳細資訊列表。\n   * 用於在 Popover 中顯示。\n   * @returns HouseholdItem[]\n   */\n  getAllSelectedItems() {\n    return this.selectedHouseIds.map(id => this.getHouseholdByHouseId(id)).filter(item => !!item);\n  }\n  writeValue(value) {\n    if (!value || value.length === 0) {\n      this.selectedHouseIds = [];\n      this.selectedHouseNames = [];\n    } else {\n      const firstItem = value[0];\n      if (this.useHouseNameMode) {\n        // useHouseNameMode: 期望接收戶別名稱陣列\n        if (typeof firstItem === 'string') {\n          this.selectedHouseNames = [...new Set(value)]; // 去除重複的戶別名稱\n          // 將戶別名稱轉換為 houseId（用於內部邏輯），會自動處理重複名稱\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\n          console.log('useHouseNameMode: 使用傳入的戶別名稱陣列（已去重）');\n        } else {\n          console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);\n          this.selectedHouseNames = [];\n          this.selectedHouseIds = [];\n        }\n      } else {\n        // 一般模式: 期望接收 houseId 陣列\n        if (typeof firstItem === 'number') {\n          this.selectedHouseIds = value;\n          // 將 houseId 轉換為戶別名稱（用於顯示）\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n          console.log('一般模式: 使用傳入的 houseId 陣列');\n        } else if (typeof firstItem === 'string') {\n          // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告\n          console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');\n          console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');\n          return;\n        } else {\n          console.error('writeValue 收到未知格式的資料:', value);\n          this.selectedHouseIds = [];\n          this.selectedHouseNames = [];\n        }\n      }\n    }\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseIds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n    }\n    if (changes['useHouseNameMode']) {\n      if (this.useHouseNameMode) {\n        this.selectedFloor = '';\n      }\n    }\n  }\n  initializeData() {\n    // 使用傳入的 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateSelectedByBuilding();\n    } else {\n      // 沒有 buildingData，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData provided');\n    }\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseIds.forEach(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseId);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    const filteredItems = households.filter(h => {\n      // 樓層篩選：在 useHouseNameMode 時跳過樓層篩選，否則按原邏輯篩選\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(houseId) {\n    console.log('onHouseholdToggle called with houseId:', houseId);\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\n    if (!houseId) {\n      console.log(`無效的 houseId: ${houseId}`);\n      return;\n    }\n    // 防止選擇已排除的戶別\n    if (this.isHouseIdExcluded(houseId)) {\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    // 取得被點擊戶別的名稱\n    const clickedHousehold = this.getHouseholdByHouseId(houseId);\n    if (!clickedHousehold) {\n      console.log(`找不到 houseId ${houseId} 對應的戶別資訊`);\n      return;\n    }\n    let newSelection;\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 處理同名戶別的邏輯\n      const houseName = clickedHousehold.houseName;\n      const allMatchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n      // 檢查是否有任何同名戶別已被選中\n      const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n      if (hasAnySelected) {\n        // 如果有同名戶別被選中，移除所有同名戶別\n        newSelection = this.selectedHouseIds.filter(id => !allMatchingHouseIds.includes(id));\n        console.log(`useHouseNameMode: 移除所有同名戶別 \"${houseName}\":`, allMatchingHouseIds);\n      } else {\n        // 如果沒有同名戶別被選中，只添加第一個（通常是當前點擊的）\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, allMatchingHouseIds[0]];\n        console.log(`useHouseNameMode: 添加戶別 \"${houseName}\" 的第一個項目:`, allMatchingHouseIds[0]);\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      const isSelected = this.isHouseIdSelected(houseId);\n      if (isSelected) {\n        newSelection = this.selectedHouseIds.filter(id => id !== houseId);\n      } else {\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, houseId];\n      }\n    }\n    this.selectedHouseIds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(houseId) {\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    console.log('onSelectAllFiltered called');\n    console.log('selectedBuilding:', this.selectedBuilding);\n    console.log('selectedFloor:', this.selectedFloor);\n    console.log('searchTerm:', this.searchTerm);\n    if (!this.selectedBuilding) {\n      console.log('No building selected');\n      return;\n    }\n    // 使用 getUniqueHouseholdsForDisplay 方法來獲取要處理的戶別列表\n    const filteredHouseholdItems = this.getUniqueHouseholdsForDisplay();\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n    if (filteredHouseholdItems.length === 0) {\n      console.log('No filtered households found');\n      return;\n    }\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的過濾戶別ID\n    const unselectedFilteredIds = [];\n    for (const household of filteredHouseholdItems) {\n      if (household.houseId) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 檢查是否有任何同名戶別已被選擇\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseIdExcluded(id));\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedFilteredIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        } else {\n          // 一般模式: 原有邏輯\n          if (!this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\n            unselectedFilteredIds.push(household.houseId);\n          }\n        }\n      }\n    }\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\n    } else {\n      console.log('No households to add');\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的棟別戶別 ID\n    const unselectedBuildingIds = [];\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 只選擇唯一的戶別名稱\n      const processedHouseNames = new Set();\n      for (const household of buildingHouseholds) {\n        if (household.houseId && household.houseName && !processedHouseNames.has(household.houseName)) {\n          processedHouseNames.add(household.houseName);\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseholdExcluded(id));\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedBuildingIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        }\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      for (const household of buildingHouseholds) {\n        if (household.houseId && !this.selectedHouseIds.includes(household.houseId) && !this.isHouseholdExcluded(household.houseId)) {\n          unselectedBuildingIds.push(household.houseId);\n        }\n      }\n    }\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined);\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseIds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds); // 根據模式決定要回傳的資料格式\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 回傳戶別名稱陣列（去重複）\n      this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      const uniqueHouseNames = [...new Set(this.selectedHouseNames)]; // 確保去重複\n      console.log('useHouseNameMode - 回傳戶別名稱陣列（已去重）:', uniqueHouseNames);\n      this.onChange([...uniqueHouseNames]);\n      this.houseNameChange.emit([...uniqueHouseNames]);\n    } else {\n      // 一般模式: 回傳 houseId 陣列\n      console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);\n      this.onChange([...this.selectedHouseIds]);\n      // 回傳 houseId 陣列\n      const houseIds = this.selectedHouseIds.filter(id => id !== undefined);\n      console.log('House IDs to emit:', houseIds);\n      this.houseIdChange.emit(houseIds);\n    }\n    this.onTouched();\n    // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）\n    const selectedItems = this.selectedHouseIds.map(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    console.log('Selected items to emit:', selectedItems);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false\n    });\n  }\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  }\n  isHouseholdSelected(houseId) {\n    if (!houseId) return false;\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 檢查是否有任何同名戶別被選中\n      const household = this.getHouseholdByHouseId(houseId);\n      if (household) {\n        const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n        return allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n      }\n      return false;\n    } else {\n      // 一般模式: 直接檢查 houseId\n      return this.selectedHouseIds.includes(houseId);\n    }\n  }\n  isHouseholdExcluded(houseId) {\n    if (!houseId) return false;\n    return this.excludedHouseIds.includes(houseId);\n  }\n  isHouseholdDisabled(houseId) {\n    if (!houseId) return true;\n    return this.isHouseholdExcluded(houseId) || !this.canSelectMore() && !this.isHouseholdSelected(houseId);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled && h.houseId !== undefined);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const households = this.buildingData[building] || [];\n      const uniqueHouseNames = new Set(households.map(h => h.houseName));\n      return uniqueHouseNames.size;\n    } else {\n      // 一般模式: 返回總戶別數量\n      return this.buildingData[building]?.length || 0;\n    }\n  }\n  getSelectedCount() {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const uniqueHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      return uniqueHouseNames.length;\n    } else {\n      // 一般模式: 返回實際選中的戶別數量\n      return this.selectedHouseIds.length;\n    }\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\n  getBuildingSelectedHouseIds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棧別的樓層計數\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棧別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: householdCode,\n      floor: ''\n    };\n  }\n  // 新增：根據 houseId 取得戶別的完整資訊\n  getHouseholdInfoById(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: `ID:${houseId}`,\n      floor: ''\n    };\n  }\n  // 新增：檢查搜尋是否有結果\n  hasNoSearchResults() {\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return false;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length === 0;\n  }\n  // 新增：取得過濾後的戶別數量\n  getFilteredHouseholdsCount() {\n    return this.getUniqueHouseholdsForDisplay().length;\n  }\n  // 新增：產生戶別的唯一識別符\n  getHouseholdUniqueId(household) {\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n  }\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\n  getHouseholdByHouseId(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) return household;\n    }\n    return null;\n  } // 新增：輔助方法 - 根據 houseName 查找 houseId\n  getHouseIdByHouseName(houseName) {\n    const matchingHouseholds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        matchingHouseholds.push({\n          building,\n          household\n        });\n      });\n    }\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\n    if (matchingHouseholds.length === 0) {\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n      return null;\n    }\n    if (matchingHouseholds.length > 1) {\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\n    }\n    const firstMatch = matchingHouseholds[0];\n    return firstMatch.household.houseId || null;\n  }\n  // 新增：輔助方法 - 根據 houseName 查找所有對應的 houseId（處理重複名稱）\n  getAllHouseIdsByHouseName(houseName) {\n    const houseIds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        if (household.houseId) {\n          houseIds.push(household.houseId);\n        }\n      });\n    }\n    return houseIds;\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\n  isHouseIdSelected(houseId) {\n    return this.selectedHouseIds.includes(houseId);\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\n  isHouseIdExcluded(houseId) {\n    return this.excludedHouseIds.includes(houseId);\n  }\n  // 新增：從唯一識別符獲取戶別物件\n  getHouseholdFromUniqueId(uniqueId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n      if (household) return household;\n    }\n    return null;\n  } // 新增：將戶別名稱陣列轉換為 houseId 陣列（在 useHouseNameMode 下只選擇第一個匹配項）\n  convertHouseNamesToIds(houseNames) {\n    const houseIds = [];\n    const uniqueHouseNames = [...new Set(houseNames)]; // 去除重複的戶別名稱\n    for (const houseName of uniqueHouseNames) {\n      const matchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n      if (matchingHouseIds.length > 0) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 只選擇第一個匹配的 houseId，避免多個同名戶別都被選中\n          houseIds.push(matchingHouseIds[0]);\n          if (matchingHouseIds.length > 1) {\n            console.log(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，在 useHouseNameMode 下只選擇第一個:`, matchingHouseIds[0]);\n          }\n        } else {\n          // 一般模式: 將所有對應的 houseId 都加入（保持原有邏輯）\n          houseIds.push(...matchingHouseIds);\n          if (matchingHouseIds.length > 1) {\n            console.warn(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，已全部加入選擇:`, matchingHouseIds);\n          }\n        }\n      } else {\n        console.warn(`無法找到戶別名稱 \"${houseName}\" 對應的 houseId`);\n      }\n    }\n    // 去除重複的 houseId\n    return [...new Set(houseIds)];\n  }\n  // 新增：將 houseId 陣列轉換為戶別名稱陣列（去重複）\n  convertIdsToHouseNames(houseIds) {\n    const houseNames = [];\n    const uniqueHouseIds = [...new Set(houseIds)]; // 去除重複的 houseId\n    for (const houseId of uniqueHouseIds) {\n      const householdInfo = this.getHouseholdInfoById(houseId);\n      if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {\n        houseNames.push(householdInfo.houseName);\n      } else {\n        console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);\n      }\n    }\n    // 去除重複的戶別名稱\n    return [...new Set(houseNames)];\n  }\n  // 新增：取得去重複的戶別列表（用於 useHouseNameMode 顯示）\n  getUniqueHouseholdsForDisplay() {\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return [];\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    if (!this.useHouseNameMode) {\n      // 一般模式：返回所有戶別\n      return households.filter(h => {\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n        const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n        return floorMatch && searchMatch;\n      });\n    }\n    // useHouseNameMode：只返回唯一的戶別名稱\n    const uniqueHouseNames = new Set();\n    const uniqueHouseholds = [];\n    for (const household of households) {\n      // 搜尋篩選\n      const searchMatch = !this.searchTerm || household.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      if (searchMatch && !uniqueHouseNames.has(household.houseName)) {\n        uniqueHouseNames.add(household.houseName);\n        uniqueHouseholds.push(household);\n      }\n    }\n    return uniqueHouseholds;\n  }\n  // 新增：動態獲取文案的getter方法\n  get displayText() {\n    return {\n      unitType: this.useHouseNameMode ? '戶型' : '戶別',\n      placeholder: this.useHouseNameMode ? '請選擇戶型' : '請選擇戶別',\n      selectedPrefix: this.useHouseNameMode ? '已選擇戶型' : '已選擇戶別',\n      selectUnit: this.useHouseNameMode ? '選擇戶型' : '選擇戶別',\n      unitSelection: this.useHouseNameMode ? '戶型選擇' : '戶別選擇',\n      selectedCount: this.useHouseNameMode ? '個戶型' : '個戶',\n      searchPlaceholder: this.useHouseNameMode ? '搜尋戶型...' : '搜尋戶別...',\n      noResults: this.useHouseNameMode ? '找不到符合的戶型' : '找不到符合的戶別',\n      noAvailable: this.useHouseNameMode ? '此棟別沒有可選擇的戶型' : '此棟別沒有可選擇的戶別'\n    };\n  }\n  static {\n    this.ɵfac = function HouseholdBindingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NbDialogService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingComponent,\n      selectors: [[\"app-household-binding\"]],\n      viewQuery: function HouseholdBindingComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.householdDialog = _t.first);\n        }\n      },\n      inputs: {\n        placeholder: \"placeholder\",\n        maxSelections: \"maxSelections\",\n        disabled: \"disabled\",\n        buildCaseId: \"buildCaseId\",\n        buildingData: \"buildingData\",\n        allowBatchSelect: \"allowBatchSelect\",\n        excludedHouseIds: \"excludedHouseIds\",\n        useHouseNameMode: \"useHouseNameMode\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        houseIdChange: \"houseIdChange\",\n        houseNameChange: \"houseNameChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => HouseholdBindingComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      decls: 12,\n      vars: 6,\n      consts: [[\"detailsPopover\", \"\"], [\"householdDialog\", \"\"], [1, \"household-binding-container\"], [\"class\", \"selected-households-summary\", \"nbPopoverTrigger\", \"hover\", \"nbPopoverPlacement\", \"bottom\", 3, \"nbPopover\", 4, \"ngIf\"], [1, \"selector-container\"], [\"type\", \"button\", 1, \"selector-button\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"padding\", \"0.5rem 0.75rem\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"0.375rem\", \"background-color\", \"#fff\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [1, \"selector-text\"], [4, \"ngIf\"], [\"icon\", \"home-outline\", 1, \"chevron-icon\"], [\"nbPopoverTrigger\", \"hover\", \"nbPopoverPlacement\", \"bottom\", 1, \"selected-households-summary\", 3, \"nbPopover\"], [1, \"summary-header\"], [1, \"summary-info\"], [\"icon\", \"people-outline\", 1, \"text-primary\"], [1, \"summary-count\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"summary-content\"], [1, \"summary-text\"], [\"icon\", \"info-outline\", 1, \"info-icon\"], [\"icon\", \"loader-outline\", 1, \"spin\"], [1, \"details-popover-content\"], [1, \"details-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"details-list-item\"], [1, \"building-name\"], [1, \"house-name\"], [\"class\", \"floor-badge\", 4, \"ngIf\"], [1, \"floor-badge\"], [2, \"width\", \"95vw\", \"max-width\", \"1200px\", \"max-height\", \"90vh\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\", \"font-size\", \"1.5rem\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\", \"font-size\", \"1.25rem\"], [2, \"font-size\", \"0.875rem\", \"color\", \"#6c757d\"], [2, \"padding\", \"0\", \"overflow\", \"hidden\"], [\"style\", \"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\", 4, \"ngIf\"], [\"style\", \"display: flex; height: 60vh; min-height: 400px;\", 4, \"ngIf\"], [2, \"padding\", \"16px\", \"border-top\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"font-size\", \"0.875rem\", \"color\", \"#495057\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\"], [\"icon\", \"checkmark-circle-outline\", 2, \"color\", \"#28a745\"], [\"style\", \"display: flex; align-items: center; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"display: flex; align-items: center; gap: 8px;\", 4, \"ngIf\"], [\"style\", \"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 4px;\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"gap\", \"8px\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"8px 16px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"8px 20px\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"checkmark-outline\"], [2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"40px\"], [2, \"text-align\", \"center\", \"color\", \"#6c757d\"], [\"icon\", \"loader-outline\", 1, \"spin\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\"], [2, \"display\", \"flex\", \"height\", \"60vh\", \"min-height\", \"400px\"], [2, \"width\", \"300px\", \"border-right\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\", 3, \"background-color\", \"border-left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"600\", \"color\", \"#495057\"], [\"style\", \"background-color: #007bff; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\", 4, \"ngIf\"], [\"style\", \"background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\", 4, \"ngIf\"], [\"style\", \"display: flex; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 8px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 12px;\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"padding\", \"16px\", \"overflow-y\", \"auto\"], [\"style\", \"text-align: center; padding: 40px 20px; color: #6c757d;\", 4, \"ngIf\"], [\"style\", \"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"width\", \"100%\", \"text-align\", \"left\", \"padding\", \"12px 16px\", \"border\", \"none\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", 3, \"click\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"2px 6px\", \"border-radius\", \"10px\"], [2, \"background-color\", \"#007bff\", \"color\", \"white\", \"padding\", \"3px 8px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"icon\", \"home-outline\", 2, \"margin-right\", \"4px\", \"font-size\", \"0.7rem\"], [2, \"background-color\", \"#28a745\", \"color\", \"white\", \"padding\", \"3px 8px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"4px\", \"font-size\", \"0.7rem\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#28a745\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\"], [2, \"margin-top\", \"8px\"], [2, \"position\", \"relative\"], [\"type\", \"text\", 2, \"width\", \"100%\", \"padding\", \"6px 32px 6px 12px\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"outline\", \"none\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"placeholder\"], [\"icon\", \"search-outline\", 2, \"position\", \"absolute\", \"right\", \"10px\", \"top\", \"50%\", \"transform\", \"translateY(-50%)\", \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [\"style\", \"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\", 4, \"ngIf\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#dc3545\", \"margin-top\", \"4px\"], [2, \"margin-top\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"margin-bottom\", \"8px\"], [\"icon\", \"layers-outline\", 2, \"color\", \"#6c757d\", \"font-size\", \"1rem\"], [2, \"font-size\", \"0.875rem\", \"font-weight\", \"600\", \"color\", \"#495057\"], [\"type\", \"button\", \"style\", \"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"flex-wrap\", \"wrap\", \"gap\", \"4px\", \"max-height\", \"100px\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"padding: 6px 10px; border-radius: 3px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\", 3, \"background-color\", \"color\", \"border\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"font-size\", \"0.75rem\", \"color\", \"#007bff\", \"background\", \"none\", \"border\", \"none\", \"text-decoration\", \"underline\", \"cursor\", \"pointer\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"6px 10px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"500\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"white-space\", \"nowrap\", 3, \"click\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"3px\", \"font-size\", \"0.7rem\"], [2, \"font-size\", \"0.7rem\", \"opacity\", \"0.7\"], [2, \"text-align\", \"center\", \"padding\", \"40px 20px\", \"color\", \"#6c757d\"], [\"icon\", \"home-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(auto-fill, minmax(90px, 1fr))\", \"gap\", \"8px\"], [\"type\", \"button\", 2, \"padding\", \"8px 6px\", \"border-radius\", \"4px\", \"transition\", \"all 0.15s ease\", \"font-size\", \"0.75rem\", \"text-align\", \"center\", \"min-height\", \"55px\", \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\", \"justify-content\", \"center\", \"align-items\", \"center\", \"gap\", \"3px\", 3, \"click\", \"disabled\"], [2, \"font-weight\", \"600\", \"line-height\", \"1.2\", \"font-size\", \"0.85rem\"], [\"style\", \"font-size: 0.7rem; font-weight: 600; padding: 2px 6px; border-radius: 3px; display: inline-flex; align-items: center; justify-content: center; min-width: 22px;\", 3, \"background-color\", \"color\", \"border\", 4, \"ngIf\"], [\"style\", \"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\", 4, \"ngIf\"], [2, \"font-size\", \"0.7rem\", \"font-weight\", \"600\", \"padding\", \"2px 6px\", \"border-radius\", \"3px\", \"display\", \"inline-flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"22px\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"2px\", \"font-size\", \"0.6rem\"], [2, \"position\", \"absolute\", \"top\", \"-8px\", \"right\", \"-8px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border-radius\", \"50%\", \"width\", \"16px\", \"height\", \"16px\", \"font-size\", \"10px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [\"icon\", \"alert-circle-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [\"icon\", \"alert-circle-outline\", 2, \"color\", \"#ffc107\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\"], [2, \"background-color\", \"#007bff\", \"color\", \"white\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"style\", \"background-color: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600; margin-left: 4px;\", 4, \"ngIf\"], [2, \"background-color\", \"#28a745\", \"color\", \"white\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\", \"margin-left\", \"4px\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"trash-2-outline\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"refresh-outline\"]],\n      template: function HouseholdBindingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_Template, 12, 5, \"div\", 3);\n          i0.ɵɵelementStart(2, \"div\", 4)(3, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_Template_button_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleDropdown());\n          });\n          i0.ɵɵelementStart(4, \"span\", 6);\n          i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_container_5_Template, 3, 0, \"ng-container\", 7)(6, HouseholdBindingComponent_ng_container_6_Template, 2, 1, \"ng-container\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"nb-icon\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, HouseholdBindingComponent_ng_template_8_Template, 3, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(10, HouseholdBindingComponent_ng_template_10_Template, 37, 13, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedHouseIds.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent, i1.NbIconComponent, i1.NbPopoverDirective],\n      styles: [\".household-binding-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  font-weight: 500;\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n  flex: 1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.375rem;\\n  padding: 0.375rem 0.75rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid #bbdefb;\\n  transition: all 0.2s ease;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 0.1rem;\\n  line-height: 1.2;\\n  min-width: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #0d47a1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  color: #ffffff;\\n  background-color: #28a745;\\n  padding: 0.15rem 0.4rem;\\n  border-radius: 0.25rem;\\n  min-width: fit-content;\\n  text-align: center;\\n  letter-spacing: 0.02em;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: 1px solid #90caf9;\\n  padding: 0.1rem;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #0d47a1;\\n  border-radius: 0.25rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f44336;\\n  border-color: #f44336;\\n  color: white;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled:hover {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  border-color: #90caf9;\\n  color: #0d47a1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  line-height: 1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem 0.75rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  cursor: not-allowed !important;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.15s ease;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon.rotated[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.flat-badge[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.flat-badge.building[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n}\\n.flat-badge.floor[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.flat-badge.search[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n  color: #6c757d;\\n}\\n.flat-badge.selected[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  border: 1px solid #bbdefb;\\n}\\n\\n.flat-household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.375rem;\\n  padding: 0.375rem 0.75rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 4px;\\n  border: 1px solid #bbdefb;\\n  transition: background-color 0.2s ease;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 0.1rem;\\n  line-height: 1.2;\\n  min-width: 0;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #0d47a1;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  color: #ffffff;\\n  background-color: #28a745;\\n  padding: 0.15rem 0.4rem;\\n  border-radius: 4px;\\n  min-width: fit-content;\\n  text-align: center;\\n  letter-spacing: 0.02em;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #90caf9;\\n  padding: 0.1rem;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #0d47a1;\\n  border-radius: 4px;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f44336;\\n  border-color: #f44336;\\n  color: white;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled:hover {\\n  background-color: #ffffff;\\n  border-color: #90caf9;\\n  color: #0d47a1;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  line-height: 1;\\n}\\n\\n.flat-button[_ngcontent-%COMP%] {\\n  border: 1px solid;\\n  border-radius: 4px;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n}\\n.flat-button.primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  border-color: #007bff;\\n}\\n.flat-button.primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n}\\n.flat-button.primary.selected[_ngcontent-%COMP%] {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n}\\n.flat-button.success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n  border-color: #28a745;\\n}\\n.flat-button.success[_ngcontent-%COMP%]:hover {\\n  background-color: #1e7e34;\\n  border-color: #1e7e34;\\n}\\n.flat-button.success.selected[_ngcontent-%COMP%] {\\n  background-color: #1e7e34;\\n  border-color: #1e7e34;\\n}\\n.flat-button.light[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #495057;\\n  border-color: #ced4da;\\n}\\n.flat-button.light[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n}\\n.flat-button.light.selected[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  border-color: #007bff;\\n}\\n.flat-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.flat-button[_ngcontent-%COMP%]:disabled:hover {\\n  opacity: 0.6;\\n}\\n\\n@media (max-width: 768px) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.25rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n    font-size: 0.6rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    color: #bbdefb;\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n    color: #bbdefb;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n    color: #90caf9;\\n    background-color: rgba(0, 0, 0, 0.2);\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n    background: rgba(0, 0, 0, 0.3);\\n    border-color: #6c757d;\\n    color: #bbdefb;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n    background-color: #f44336;\\n    border-color: #f44336;\\n    color: white;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n}\\n.selected-households-summary[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.selected-households-summary[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.1);\\n}\\n.selected-households-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.selected-households-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.selected-households-summary[_ngcontent-%COMP%]   .summary-header[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .summary-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.selected-households-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.5rem;\\n  background-color: #ffffff;\\n  border-radius: 0.25rem;\\n}\\n.selected-households-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.selected-households-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.details-popover-content[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow: hidden;\\n  padding: 0.5rem;\\n}\\n.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n  max-height: 280px;\\n  overflow-y: auto;\\n}\\n.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .details-list-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .details-list-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .details-list-item[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  min-width: 50px;\\n}\\n.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .details-list-item[_ngcontent-%COMP%]   .house-name[_ngcontent-%COMP%] {\\n  color: #0d47a1;\\n}\\n.details-popover-content[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .details-list-item[_ngcontent-%COMP%]   .floor-badge[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  color: #ffffff;\\n  background-color: #28a745;\\n  padding: 0.15rem 0.4rem;\\n  border-radius: 0.25rem;\\n  margin-left: auto;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "HouseholdBindingComponent_div_1_Template_button_click_6_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "onClearAll", "ɵɵresetView", "stopPropagation", "ɵɵproperty", "detailsPopover_r4", "ɵɵadvance", "ɵɵtextInterpolate2", "displayText", "selectedPrefix", "getSelectedCount", "disabled", "ɵɵtextInterpolate", "getGroupedSummary", "ɵɵelementContainerStart", "ɵɵtextInterpolate1", "selectedCount", "placeholder", "item_r5", "floor", "ɵɵtemplate", "HouseholdBindingComponent_ng_template_8_ng_container_2_span_6_Template", "building", "houseName", "useHouseNameMode", "HouseholdBindingComponent_ng_template_8_ng_container_2_Template", "getAllSelectedItems", "unitType", "HouseholdBindingComponent_ng_template_10_div_13_button_6_Template_button_click_0_listener", "building_r8", "_r7", "$implicit", "onBuildingSelect", "ɵɵstyleProp", "selectedBuilding", "getBuildingCount", "selectedF<PERSON>or", "HouseholdBindingComponent_ng_template_10_div_13_div_15_button_5_Template_button_click_0_listener", "_r10", "onUnselectAllBuilding", "HouseholdBindingComponent_ng_template_10_div_13_div_15_Template_button_click_1_listener", "_r9", "onSelectAllFiltered", "HouseholdBindingComponent_ng_template_10_div_13_div_15_Template_button_click_3_listener", "onSelectAllBuilding", "HouseholdBindingComponent_ng_template_10_div_13_div_15_button_5_Template", "canSelectMore", "isSomeBuildingSelected", "noResults", "searchTerm", "ɵɵtwoWayListener", "HouseholdBindingComponent_ng_template_10_div_13_div_16_Template_input_ngModelChange_2_listener", "_r11", "ɵɵtwoWayBindingSet", "HouseholdBindingComponent_ng_template_10_div_13_div_16_Template_input_input_2_listener", "onSearchChange", "HouseholdBindingComponent_ng_template_10_div_13_div_16_div_4_Template", "ɵɵtwoWayProperty", "searchPlaceholder", "hasNoSearchResults", "HouseholdBindingComponent_ng_template_10_div_13_div_17_button_6_Template_button_click_0_listener", "_r12", "onFloorSelect", "HouseholdBindingComponent_ng_template_10_div_13_div_17_button_8_Template_button_click_0_listener", "floor_r14", "_r13", "getFloorCount", "HouseholdBindingComponent_ng_template_10_div_13_div_17_span_5_Template", "HouseholdBindingComponent_ng_template_10_div_13_div_17_button_6_Template", "HouseholdBindingComponent_ng_template_10_div_13_div_17_button_8_Template", "floors", "isHouseholdSelected", "household_r16", "houseId", "HouseholdBindingComponent_ng_template_10_div_13_div_20_ng_container_1_Template_button_click_1_listener", "_r15", "onHouseholdToggle", "HouseholdBindingComponent_ng_template_10_div_13_div_20_ng_container_1_span_4_Template", "HouseholdBindingComponent_ng_template_10_div_13_div_20_ng_container_1_div_5_Template", "isHouseholdExcluded", "isHouseholdDisabled", "HouseholdBindingComponent_ng_template_10_div_13_div_20_ng_container_1_Template", "getUniqueHouseholdsForDisplay", "noAvailable", "HouseholdBindingComponent_ng_template_10_div_13_button_6_Template", "HouseholdBindingComponent_ng_template_10_div_13_span_13_Template", "HouseholdBindingComponent_ng_template_10_div_13_span_14_Template", "HouseholdBindingComponent_ng_template_10_div_13_div_15_Template", "HouseholdBindingComponent_ng_template_10_div_13_div_16_Template", "HouseholdBindingComponent_ng_template_10_div_13_div_17_Template", "HouseholdBindingComponent_ng_template_10_div_13_div_19_Template", "HouseholdBindingComponent_ng_template_10_div_13_div_20_Template", "HouseholdBindingComponent_ng_template_10_div_13_div_21_Template", "buildings", "unitSelection", "allowBatchSelect", "buildingData", "length", "maxSelections", "HouseholdBindingComponent_ng_template_10_div_25_span_6_Template", "getFilteredHouseholdsCount", "HouseholdBindingComponent_ng_template_10_button_29_Template_button_click_0_listener", "_r17", "HouseholdBindingComponent_ng_template_10_button_30_Template_button_click_0_listener", "_r18", "resetSearch", "HouseholdBindingComponent_ng_template_10_div_12_Template", "HouseholdBindingComponent_ng_template_10_div_13_Template", "HouseholdBindingComponent_ng_template_10_div_24_Template", "HouseholdBindingComponent_ng_template_10_div_25_Template", "HouseholdBindingComponent_ng_template_10_div_26_Template", "HouseholdBindingComponent_ng_template_10_button_29_Template", "HouseholdBindingComponent_ng_template_10_button_30_Template", "HouseholdBindingComponent_ng_template_10_Template_button_click_32_listener", "ref_r19", "_r6", "dialogRef", "close", "HouseholdBindingComponent_ng_template_10_Template_button_click_34_listener", "selectUnit", "isLoading", "selectedHouseIds", "HouseholdBindingComponent", "constructor", "cdr", "dialogService", "buildCaseId", "excludedHouseIds", "selectionChange", "houseIdChange", "houseNameChange", "isOpen", "selectedHouseNames", "filteredHouseholds", "selectedByBuilding", "onChange", "value", "onTouched", "map", "count", "filter", "Boolean", "join", "id", "getHouseholdByHouseId", "item", "writeValue", "firstItem", "Set", "convertHouseNamesToIds", "console", "log", "error", "convertIdsToHouseNames", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "grouped", "for<PERSON>ach", "find", "h", "push", "updateFloorsForBuilding", "detectChanges", "onBuildingClick", "households", "filteredItems", "floorMatch", "searchMatch", "toLowerCase", "includes", "event", "target", "isHouseIdExcluded", "clickedHousehold", "newSelection", "allMatchingHouseIds", "getAllHouseIdsByHouseName", "hasAnySelected", "some", "isSelected", "isHouseIdSelected", "emitChanges", "onRemoveHousehold", "filteredHouseholdItems", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFilteredIds", "household", "hasAnyExcluded", "toAdd", "slice", "buildingHouseholds", "unselectedBuildingIds", "processedHouseNames", "has", "add", "buildingHouseIds", "undefined", "uniqueHouseNames", "emit", "houseIds", "selectedItems", "toggleDropdown", "openDialog", "open", "householdDialog", "context", "closeOnBackdropClick", "closeOnEsc", "autoFocus", "closeDropdown", "isAllBuildingSelected", "every", "getSelectedByBuilding", "size", "getBuildingSelectedHouseIds", "hasBuildingSelected", "floorSet", "Array", "from", "sort", "a", "b", "numA", "parseInt", "replace", "numB", "getHouseholdFloor", "householdCode", "getHouseholdInfo", "getHouseholdInfoById", "filtered", "getHouseholdUniqueId", "toString", "getHouseIdByHouseName", "matchingHouseholds", "matches", "warn", "m", "firstMatch", "getHouseholdFromUniqueId", "uniqueId", "houseNames", "matchingHouseIds", "uniqueHouseIds", "householdInfo", "startsWith", "uniqueHouseholds", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "NbDialogService", "selectors", "viewQuery", "HouseholdBindingComponent_Query", "rf", "ctx", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "HouseholdBindingComponent_Template", "HouseholdBindingComponent_div_1_Template", "HouseholdBindingComponent_Template_button_click_3_listener", "_r1", "HouseholdBindingComponent_ng_container_5_Template", "HouseholdBindingComponent_ng_container_6_Template", "HouseholdBindingComponent_ng_template_8_Template", "ɵɵtemplateRefExtractor", "HouseholdBindingComponent_ng_template_10_Template", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { NbDialogService } from '@nebular/theme';\n\nexport interface HouseholdItem {\n  houseName: string;\n  building: string;\n  floor?: string;\n  houseId?: number;\n  houseType?: number; // 新增：戶別類型\n  isSelected?: boolean;\n  isDisabled?: boolean;\n}\n\nexport interface BuildingData {\n  [key: string]: HouseholdItem[];\n}\n\n@Component({\n  selector: 'app-household-binding',\n  templateUrl: './household-binding.component.html',\n  styleUrls: ['./household-binding.component.scss'],\n  providers: [\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => HouseholdBindingComponent),\n      multi: true,\n    },\n  ],\n})\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\n  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;\n  @Input() placeholder: string = '請選擇戶別';\n  @Input() maxSelections: number | null = null;\n  @Input() disabled: boolean = false;\n  @Input() buildCaseId: number | null = null; // 建案ID（用於識別）\n  @Input() buildingData: BuildingData = {};\n  @Input() allowBatchSelect: boolean = true;\n  @Input() excludedHouseIds: number[] = []; // 改為：排除的戶別ID（已被其他元件選擇）\n  @Input() useHouseNameMode: boolean = false; // 新增：使用戶別名稱模式\n\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\n  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列\n  @Output() houseNameChange = new EventEmitter<string[]>(); // 新增：useHouseNameMode 時回傳戶別名稱陣列\n  isOpen = false;\n  selectedBuilding = '';\n  searchTerm = ''; selectedFloor = ''; // 新增：選中的樓層\n  selectedHouseIds: number[] = []; // 改為：使用 houseId 作為選擇的key\n  selectedHouseNames: string[] = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列\n  buildings: string[] = [];\n  floors: string[] = []; // 新增：當前棧別的樓層列表\n  filteredHouseholds: string[] = [];  // 保持為字串陣列用於UI顯示\n  selectedByBuilding: { [building: string]: number[] } = {}; // 改為：儲存 houseId\n  isLoading: boolean = false; // 新增：載入狀態  // ControlValueAccessor implementation\n  private onChange = (value: number[] | string[]) => { };\n  private onTouched = () => { }; constructor(\n    private cdr: ChangeDetectorRef,\n    private dialogService: NbDialogService\n  ) { }\n\n  /**\n   * [優化] 產生已選戶別的分組摘要文字。\n   * 用於簡潔地顯示選擇結果。\n   * @returns string - 例如: \"棟 A (10戶), 棟 B (5戶)\"\n   */\n  getGroupedSummary(): string {\n    if (this.selectedHouseIds.length === 0) {\n      return '';\n    }\n\n    return this.buildings\n      .map(building => {\n        const count = this.selectedByBuilding[building]?.length || 0;\n        return count > 0 ? `${building} (${count}戶)` : null;\n      })\n      .filter(Boolean) // 過濾掉沒有選擇的棟別\n      .join(', ');\n  }\n\n  /**\n   * [優化] 獲取所有已選戶別的詳細資訊列表。\n   * 用於在 Popover 中顯示。\n   * @returns HouseholdItem[]\n   */\n  getAllSelectedItems(): HouseholdItem[] {\n    return this.selectedHouseIds\n      .map(id => this.getHouseholdByHouseId(id))\n      .filter((item): item is HouseholdItem => !!item);\n  }\n\n  writeValue(value: any[]): void {\n    if (!value || value.length === 0) {\n      this.selectedHouseIds = [];\n      this.selectedHouseNames = [];\n    } else {\n      const firstItem = value[0]; if (this.useHouseNameMode) {\n        // useHouseNameMode: 期望接收戶別名稱陣列\n        if (typeof firstItem === 'string') {\n          this.selectedHouseNames = [...new Set(value as string[])]; // 去除重複的戶別名稱\n          // 將戶別名稱轉換為 houseId（用於內部邏輯），會自動處理重複名稱\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\n          console.log('useHouseNameMode: 使用傳入的戶別名稱陣列（已去重）');\n        } else {\n          console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);\n          this.selectedHouseNames = [];\n          this.selectedHouseIds = [];\n        }\n      } else {\n        // 一般模式: 期望接收 houseId 陣列\n        if (typeof firstItem === 'number') {\n          this.selectedHouseIds = value as number[];\n          // 將 houseId 轉換為戶別名稱（用於顯示）\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n          console.log('一般模式: 使用傳入的 houseId 陣列');\n        } else if (typeof firstItem === 'string') {\n          // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告\n          console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');\n          console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');\n          return;\n        } else {\n          console.error('writeValue 收到未知格式的資料:', value);\n          this.selectedHouseIds = [];\n          this.selectedHouseNames = [];\n        }\n      }\n    }\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn: (value: number[] | string[]) => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n  } ngOnInit() {\n    this.initializeData();\n  } ngOnChanges(changes: SimpleChanges) {\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    } if (changes['excludedHouseIds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n    }\n    if (changes['useHouseNameMode']) {\n      if (this.useHouseNameMode) {\n        this.selectedFloor = '';\n      }\n    }\n  } private initializeData() {\n    // 使用傳入的 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateSelectedByBuilding();\n    } else {\n      // 沒有 buildingData，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData provided');\n    }\n  } private updateSelectedByBuilding() {\n    const grouped: { [building: string]: number[] } = {};\n\n    this.selectedHouseIds.forEach(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseId);\n          break;\n        }\n      }\n    });\n\n    this.selectedByBuilding = grouped;\n  } onBuildingSelect(building: string) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n\n  onBuildingClick(building: string) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    const filteredItems = households.filter(h => {\n      // 樓層篩選：在 useHouseNameMode 時跳過樓層篩選，否則按原邏輯篩選\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\n\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n  }\n\n  onSearchChange(event: any) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  } onHouseholdToggle(houseId: number | undefined) {\n    console.log('onHouseholdToggle called with houseId:', houseId);\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\n\n    if (!houseId) {\n      console.log(`無效的 houseId: ${houseId}`);\n      return;\n    }\n\n    // 防止選擇已排除的戶別\n    if (this.isHouseIdExcluded(houseId)) {\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n\n    // 取得被點擊戶別的名稱\n    const clickedHousehold = this.getHouseholdByHouseId(houseId);\n    if (!clickedHousehold) {\n      console.log(`找不到 houseId ${houseId} 對應的戶別資訊`);\n      return;\n    }\n\n    let newSelection: number[];\n\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 處理同名戶別的邏輯\n      const houseName = clickedHousehold.houseName;\n      const allMatchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n\n      // 檢查是否有任何同名戶別已被選中\n      const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n\n      if (hasAnySelected) {\n        // 如果有同名戶別被選中，移除所有同名戶別\n        newSelection = this.selectedHouseIds.filter(id => !allMatchingHouseIds.includes(id));\n        console.log(`useHouseNameMode: 移除所有同名戶別 \"${houseName}\":`, allMatchingHouseIds);\n      } else {\n        // 如果沒有同名戶別被選中，只添加第一個（通常是當前點擊的）\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, allMatchingHouseIds[0]];\n        console.log(`useHouseNameMode: 添加戶別 \"${houseName}\" 的第一個項目:`, allMatchingHouseIds[0]);\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      const isSelected = this.isHouseIdSelected(houseId);\n\n      if (isSelected) {\n        newSelection = this.selectedHouseIds.filter(id => id !== houseId);\n      } else {\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, houseId];\n      }\n    }\n\n    this.selectedHouseIds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(houseId: number) {\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\n    this.emitChanges();\n  } onSelectAllFiltered() {\n    console.log('onSelectAllFiltered called');\n    console.log('selectedBuilding:', this.selectedBuilding);\n    console.log('selectedFloor:', this.selectedFloor);\n    console.log('searchTerm:', this.searchTerm);\n\n    if (!this.selectedBuilding) {\n      console.log('No building selected');\n      return;\n    }\n\n    // 使用 getUniqueHouseholdsForDisplay 方法來獲取要處理的戶別列表\n    const filteredHouseholdItems = this.getUniqueHouseholdsForDisplay();\n\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n\n    if (filteredHouseholdItems.length === 0) {\n      console.log('No filtered households found');\n      return;\n    }\n\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n\n    // 取得尚未選擇且未被排除的過濾戶別ID\n    const unselectedFilteredIds: number[] = [];\n    for (const household of filteredHouseholdItems) {\n      if (household.houseId) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 檢查是否有任何同名戶別已被選擇\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseIdExcluded(id));\n\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedFilteredIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        } else {\n          // 一般模式: 原有邏輯\n          if (!this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\n            unselectedFilteredIds.push(household.houseId);\n          }\n        }\n      }\n    }\n\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\n\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\n\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\n    } else {\n      console.log('No households to add');\n    }\n  } onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n\n    // 取得尚未選擇且未被排除的棟別戶別 ID\n    const unselectedBuildingIds: number[] = [];\n\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 只選擇唯一的戶別名稱\n      const processedHouseNames = new Set<string>();\n\n      for (const household of buildingHouseholds) {\n        if (household.houseId && household.houseName && !processedHouseNames.has(household.houseName)) {\n          processedHouseNames.add(household.houseName);\n\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseholdExcluded(id));\n\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedBuildingIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        }\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      for (const household of buildingHouseholds) {\n        if (household.houseId &&\n          !this.selectedHouseIds.includes(household.houseId) &&\n          !this.isHouseholdExcluded(household.houseId)) {\n          unselectedBuildingIds.push(household.houseId);\n        }\n      }\n    }\n\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\n\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined) as number[];\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\n    this.emitChanges();\n  }\n\n  onClearAll() {\n    this.selectedHouseIds = [];\n    this.emitChanges();\n  } private emitChanges() {\n    this.updateSelectedByBuilding();\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds);    // 根據模式決定要回傳的資料格式\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 回傳戶別名稱陣列（去重複）\n      this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      const uniqueHouseNames = [...new Set(this.selectedHouseNames)]; // 確保去重複\n      console.log('useHouseNameMode - 回傳戶別名稱陣列（已去重）:', uniqueHouseNames);\n      this.onChange([...uniqueHouseNames]);\n      this.houseNameChange.emit([...uniqueHouseNames]);\n    } else {\n      // 一般模式: 回傳 houseId 陣列\n      console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);\n      this.onChange([...this.selectedHouseIds]);\n\n      // 回傳 houseId 陣列\n      const houseIds = this.selectedHouseIds.filter(id => id !== undefined);\n      console.log('House IDs to emit:', houseIds);\n      this.houseIdChange.emit(houseIds);\n    }\n\n    this.onTouched();\n\n    // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）\n    const selectedItems = this.selectedHouseIds.map(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null) as HouseholdItem[];\n\n    console.log('Selected items to emit:', selectedItems);\n    this.selectionChange.emit(selectedItems);\n  } toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false,\n    });\n  }\n\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  } isHouseholdSelected(houseId: number | undefined): boolean {\n    if (!houseId) return false;\n\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 檢查是否有任何同名戶別被選中\n      const household = this.getHouseholdByHouseId(houseId);\n      if (household) {\n        const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n        return allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n      }\n      return false;\n    } else {\n      // 一般模式: 直接檢查 houseId\n      return this.selectedHouseIds.includes(houseId);\n    }\n  }\n\n  isHouseholdExcluded(houseId: number | undefined): boolean {\n    if (!houseId) return false;\n    return this.excludedHouseIds.includes(houseId);\n  }\n\n  isHouseholdDisabled(houseId: number | undefined): boolean {\n    if (!houseId) return true;\n    return this.isHouseholdExcluded(houseId) ||\n      (!this.canSelectMore() && !this.isHouseholdSelected(houseId));\n  }\n  canSelectMore(): boolean {\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\n  } isAllBuildingSelected(): boolean {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\n      .filter(h => !h.isDisabled && h.houseId !== undefined);\n    return buildingHouseholds.length > 0 &&\n      buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n\n  isSomeBuildingSelected(): boolean {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  } getSelectedByBuilding(): { [building: string]: number[] } {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building: string): number {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const households = this.buildingData[building] || [];\n      const uniqueHouseNames = new Set(households.map(h => h.houseName));\n      return uniqueHouseNames.size;\n    } else {\n      // 一般模式: 返回總戶別數量\n      return this.buildingData[building]?.length || 0;\n    }\n  }\n  getSelectedCount(): number {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const uniqueHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      return uniqueHouseNames.length;\n    } else {\n      // 一般模式: 返回實際選中的戶別數量\n      return this.selectedHouseIds.length;\n    }\n  }\n\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\n  getBuildingSelectedHouseIds(building: string): number[] {\n    return this.selectedByBuilding[building] || [];\n  }\n\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building: string): boolean {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n\n  // 新增：更新當前棧別的樓層計數\n  private updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set<string>();\n\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n\n  // 新增：樓層選擇處理\n  onFloorSelect(floor: string) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n\n  // 新增：取得當前棧別的樓層計數\n  getFloorCount(floor: string): number {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode: string): string {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode: string): { houseName: string, floor: string } {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return { houseName: householdCode, floor: '' };\n  }\n\n  // 新增：根據 houseId 取得戶別的完整資訊\n  getHouseholdInfoById(houseId: number): { houseName: string, floor: string } {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return { houseName: `ID:${houseId}`, floor: '' };\n  }\n\n  // 新增：檢查搜尋是否有結果\n  hasNoSearchResults(): boolean {\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return false;\n    } const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n\n    return filtered.length === 0;\n  }\n  // 新增：取得過濾後的戶別數量\n  getFilteredHouseholdsCount(): number {\n    return this.getUniqueHouseholdsForDisplay().length;\n  }\n\n  // 新增：產生戶別的唯一識別符\n  getHouseholdUniqueId(household: HouseholdItem): string {\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n  }\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\n  private getHouseholdByHouseId(houseId: number): HouseholdItem | null {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) return household;\n    }\n    return null;\n  }  // 新增：輔助方法 - 根據 houseName 查找 houseId\n  private getHouseIdByHouseName(houseName: string): number | null {\n    const matchingHouseholds: { building: string, household: HouseholdItem }[] = [];\n\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        matchingHouseholds.push({ building, household });\n      });\n    }\n\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\n\n    if (matchingHouseholds.length === 0) {\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n      return null;\n    }\n\n    if (matchingHouseholds.length > 1) {\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\n    }\n\n    const firstMatch = matchingHouseholds[0];\n    return firstMatch.household.houseId || null;\n  }\n\n  // 新增：輔助方法 - 根據 houseName 查找所有對應的 houseId（處理重複名稱）\n  private getAllHouseIdsByHouseName(houseName: string): number[] {\n    const houseIds: number[] = [];\n\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        if (household.houseId) {\n          houseIds.push(household.houseId);\n        }\n      });\n    }\n\n    return houseIds;\n  }\n\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\n  private isHouseIdSelected(houseId: number): boolean {\n    return this.selectedHouseIds.includes(houseId);\n  }\n\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\n  private isHouseIdExcluded(houseId: number): boolean {\n    return this.excludedHouseIds.includes(houseId);\n  }\n  // 新增：從唯一識別符獲取戶別物件\n  getHouseholdFromUniqueId(uniqueId: string): HouseholdItem | null {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n      if (household) return household;\n    }\n    return null;\n  }  // 新增：將戶別名稱陣列轉換為 houseId 陣列（在 useHouseNameMode 下只選擇第一個匹配項）\n  private convertHouseNamesToIds(houseNames: string[]): number[] {\n    const houseIds: number[] = [];\n    const uniqueHouseNames = [...new Set(houseNames)]; // 去除重複的戶別名稱\n\n    for (const houseName of uniqueHouseNames) {\n      const matchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n      if (matchingHouseIds.length > 0) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 只選擇第一個匹配的 houseId，避免多個同名戶別都被選中\n          houseIds.push(matchingHouseIds[0]);\n          if (matchingHouseIds.length > 1) {\n            console.log(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，在 useHouseNameMode 下只選擇第一個:`, matchingHouseIds[0]);\n          }\n        } else {\n          // 一般模式: 將所有對應的 houseId 都加入（保持原有邏輯）\n          houseIds.push(...matchingHouseIds);\n          if (matchingHouseIds.length > 1) {\n            console.warn(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，已全部加入選擇:`, matchingHouseIds);\n          }\n        }\n      } else {\n        console.warn(`無法找到戶別名稱 \"${houseName}\" 對應的 houseId`);\n      }\n    }\n\n    // 去除重複的 houseId\n    return [...new Set(houseIds)];\n  }\n  // 新增：將 houseId 陣列轉換為戶別名稱陣列（去重複）\n  private convertIdsToHouseNames(houseIds: number[]): string[] {\n    const houseNames: string[] = [];\n    const uniqueHouseIds = [...new Set(houseIds)]; // 去除重複的 houseId\n\n    for (const houseId of uniqueHouseIds) {\n      const householdInfo = this.getHouseholdInfoById(houseId);\n      if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {\n        houseNames.push(householdInfo.houseName);\n      } else {\n        console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);\n      }\n    }\n\n    // 去除重複的戶別名稱\n    return [...new Set(houseNames)];\n  }\n\n  // 新增：取得去重複的戶別列表（用於 useHouseNameMode 顯示）\n  getUniqueHouseholdsForDisplay(): HouseholdItem[] {\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return [];\n    }\n\n    const households = this.buildingData[this.selectedBuilding] || [];\n\n    if (!this.useHouseNameMode) {\n      // 一般模式：返回所有戶別\n      return households.filter(h => {\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n        const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n        return floorMatch && searchMatch;\n      });\n    }\n\n    // useHouseNameMode：只返回唯一的戶別名稱\n    const uniqueHouseNames = new Set<string>();\n    const uniqueHouseholds: HouseholdItem[] = [];\n\n    for (const household of households) {\n      // 搜尋篩選\n      const searchMatch = !this.searchTerm || household.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n\n      if (searchMatch && !uniqueHouseNames.has(household.houseName)) {\n        uniqueHouseNames.add(household.houseName);\n        uniqueHouseholds.push(household);\n      }\n    }\n    return uniqueHouseholds;\n  }\n\n  // 新增：動態獲取文案的getter方法\n  get displayText() {\n    return {\n      unitType: this.useHouseNameMode ? '戶型' : '戶別',\n      placeholder: this.useHouseNameMode ? '請選擇戶型' : '請選擇戶別',\n      selectedPrefix: this.useHouseNameMode ? '已選擇戶型' : '已選擇戶別',\n      selectUnit: this.useHouseNameMode ? '選擇戶型' : '選擇戶別',\n      unitSelection: this.useHouseNameMode ? '戶型選擇' : '戶別選擇',\n      selectedCount: this.useHouseNameMode ? '個戶型' : '個戶',\n      searchPlaceholder: this.useHouseNameMode ? '搜尋戶型...' : '搜尋戶別...',\n      noResults: this.useHouseNameMode ? '找不到符合的戶型' : '找不到符合的戶別',\n      noAvailable: this.useHouseNameMode ? '此棟別沒有可選擇的戶型' : '此棟別沒有可選擇的戶別'\n    };\n  }\n}\n", "<div class=\"household-binding-container\">\n  <!-- [優化] 已選擇戶別摘要顯示區域 -->\n  <div *ngIf=\"selectedHouseIds.length > 0\" class=\"selected-households-summary\"\n    [nbPopover]=\"detailsPopover\" nbPopoverTrigger=\"hover\" nbPopoverPlacement=\"bottom\">\n    <div class=\"summary-header\">\n      <div class=\"summary-info\">\n        <nb-icon icon=\"people-outline\" class=\"text-primary\"></nb-icon>\n        <span class=\"summary-count\">{{displayText.selectedPrefix}} ({{getSelectedCount()}})</span>\n      </div>\n      <button type=\"button\" class=\"btn btn-outline-danger btn-sm\" [disabled]=\"disabled\" (click)=\"onClearAll(); $event.stopPropagation()\">\n        清空全部\n      </button>\n    </div>\n    <div class=\"summary-content\">\n      <span class=\"summary-text\">{{ getGroupedSummary() }}</span>\n      <nb-icon icon=\"info-outline\" class=\"info-icon\"></nb-icon>\n    </div>\n  </div>\n\n  <!-- 選擇器 -->\n  <div class=\"selector-container\">\n    <button type=\"button\" class=\"selector-button\" [class.disabled]=\"disabled || isLoading\"\n      [disabled]=\"disabled || isLoading\" (click)=\"toggleDropdown()\"\n      style=\"width: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.375rem; background-color: #fff; cursor: pointer;\">\n      <span class=\"selector-text\">\n        <ng-container *ngIf=\"isLoading\">\n          <nb-icon icon=\"loader-outline\" class=\"spin\"></nb-icon>\n          載入中...\n        </ng-container>\n        <ng-container *ngIf=\"!isLoading\">\n          {{getSelectedCount() > 0 ? '已選擇 ' + getSelectedCount() + ' ' + displayText.selectedCount : (placeholder ||\n          displayText.placeholder)}}\n        </ng-container>\n      </span>\n      <nb-icon icon=\"home-outline\" class=\"chevron-icon\"></nb-icon>\n    </button>\n  </div>\n</div>\n\n<!-- [優化] 已選項目詳情 Popover -->\n<ng-template #detailsPopover>\n  <div class=\"details-popover-content\">\n    <ul class=\"details-list\">\n      <ng-container *ngFor=\"let item of getAllSelectedItems()\">\n        <li class=\"details-list-item\">\n          <span class=\"building-name\">{{item.building}}</span>\n          <span class=\"house-name\">{{item.houseName}}</span>\n          <span *ngIf=\"!useHouseNameMode && item.floor\" class=\"floor-badge\">{{item.floor}}</span>\n        </li>\n      </ng-container>\n    </ul>\n  </div>\n</ng-template>\n\n<!-- 戶別/戶型選擇對話框 -->\n<ng-template #householdDialog let-dialog let-ref=\"dialogRef\">\n  <nb-card style=\"width: 95vw; max-width: 1200px; max-height: 90vh;\">\n    <nb-card-header>\n      <div style=\"display: flex; align-items: center; justify-content: space-between;\">\n        <div style=\"display: flex; align-items: center; gap: 8px;\">\n          <nb-icon icon=\"home-outline\" style=\"color: #007bff; font-size: 1.5rem;\"></nb-icon>\n          <span style=\"font-weight: 500; color: #495057; font-size: 1.25rem;\">{{displayText.selectUnit}}</span>\n          <span style=\"font-size: 0.875rem; color: #6c757d;\">({{buildings.length}} 個棟別)</span>\n        </div>\n        <span style=\"font-size: 0.875rem; color: #6c757d;\">已選擇: {{getSelectedCount()}}</span>\n      </div>\n    </nb-card-header>\n\n    <nb-card-body style=\"padding: 0; overflow: hidden;\">\n      <!-- 載入狀態 -->\n      <div *ngIf=\"isLoading\"\n        style=\"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\">\n        <div style=\"text-align: center; color: #6c757d;\">\n          <nb-icon icon=\"loader-outline\" class=\"spin\" style=\"font-size: 2rem; margin-bottom: 8px;\"></nb-icon>\n          <p style=\"margin: 0; font-size: 0.875rem;\">載入{{displayText.unitType}}資料中...</p>\n        </div>\n      </div>\n\n      <!-- 主要內容區域 -->\n      <div *ngIf=\"!isLoading\" style=\"display: flex; height: 60vh; min-height: 400px;\">\n        <!-- 棟別選擇側邊欄 -->\n        <div\n          style=\"width: 300px; border-right: 1px solid #e9ecef; background-color: #f8f9fa; display: flex; flex-direction: column;\">\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\n            <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">棟別列表</h6>\n          </div>\n          <div style=\"flex: 1; overflow-y: auto;\">\n            <button *ngFor=\"let building of buildings\" type=\"button\" (click)=\"onBuildingSelect(building)\"\n              [style.background-color]=\"selectedBuilding === building ? '#e3f2fd' : 'transparent'\"\n              [style.border-left]=\"selectedBuilding === building ? '3px solid #007bff' : '3px solid transparent'\"\n              style=\"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\">\n              <span style=\"font-weight: 500; color: #495057;\">{{building}}</span>\n              <span\n                style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 2px 6px; border-radius: 10px;\">\n                {{getBuildingCount(building)}}戶\n              </span>\n            </button>\n          </div>\n        </div>\n\n        <!-- 戶別/戶型選擇主區域 -->\n        <div style=\"flex: 1; display: flex; flex-direction: column;\">\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\n            <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;\">\n              <div style=\"display: flex; align-items: center; gap: 8px;\">\n                <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 600; color: #495057;\">\n                  {{displayText.unitSelection}}</h6> <span *ngIf=\"selectedBuilding\"\n                  style=\"background-color: #007bff; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\">\n                  <nb-icon icon=\"home-outline\" style=\"margin-right: 4px; font-size: 0.7rem;\"></nb-icon>\n                  {{selectedBuilding}}\n                </span>\n                <span *ngIf=\"!useHouseNameMode && selectedFloor\"\n                  style=\"background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\">\n                  <nb-icon icon=\"layers-outline\" style=\"margin-right: 4px; font-size: 0.7rem;\"></nb-icon>\n                  {{selectedFloor}}\n                </span>\n              </div>\n              <div\n                *ngIf=\"allowBatchSelect && selectedBuilding && buildingData[selectedBuilding] && buildingData[selectedBuilding].length > 0\"\n                style=\"display: flex; gap: 4px;\">\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllFiltered()\"\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;\">\n                  全選當前\n                </button>\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllBuilding()\"\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;\">\n                  全選{{selectedBuilding}}\n                </button>\n                <button type=\"button\" *ngIf=\"isSomeBuildingSelected()\" (click)=\"onUnselectAllBuilding()\"\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\">\n                  清除\n                </button>\n              </div>\n            </div>\n\n            <!-- 搜尋框 -->\n            <div *ngIf=\"selectedBuilding\" style=\"margin-top: 8px;\">\n              <div style=\"position: relative;\">\n                <input type=\"text\" [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange($event)\"\n                  [placeholder]=\"displayText.searchPlaceholder\"\n                  style=\"width: 100%; padding: 6px 32px 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.875rem; outline: none;\">\n                <nb-icon icon=\"search-outline\"\n                  style=\"position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; font-size: 0.875rem;\"></nb-icon>\n              </div>\n              <div *ngIf=\"searchTerm && hasNoSearchResults()\"\n                style=\"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\">\n                {{displayText.noResults}} \"{{searchTerm}}\"\n              </div>\n            </div> <!-- 樓層篩選器 -->\n            <div *ngIf=\"!useHouseNameMode && selectedBuilding && floors.length > 1\" style=\"margin-top: 12px;\">\n              <div style=\"display: flex; align-items: center; gap: 8px; margin-bottom: 8px;\">\n                <nb-icon icon=\"layers-outline\" style=\"color: #6c757d; font-size: 1rem;\"></nb-icon>\n                <span style=\"font-size: 0.875rem; font-weight: 600; color: #495057;\">樓層篩選:</span>\n                <span *ngIf=\"selectedFloor\"\n                  style=\"background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\">\n                  {{selectedFloor}}\n                </span>\n                <button type=\"button\" *ngIf=\"selectedFloor\" (click)=\"onFloorSelect('')\"\n                  style=\"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\">\n                  清除篩選\n                </button>\n              </div>\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px; max-height: 100px; overflow-y: auto;\">\n                <button type=\"button\" *ngFor=\"let floor of floors\" (click)=\"onFloorSelect(floor)\"\n                  [style.background-color]=\"selectedFloor === floor ? '#007bff' : '#f8f9fa'\"\n                  [style.color]=\"selectedFloor === floor ? '#fff' : '#495057'\"\n                  [style.border]=\"selectedFloor === floor ? '2px solid #007bff' : '1px solid #dee2e6'\"\n                  style=\"padding: 6px 10px; border-radius: 3px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\">\n                  <nb-icon icon=\"layers-outline\" style=\"margin-right: 3px; font-size: 0.7rem;\"></nb-icon>\n                  {{floor}} <span style=\"font-size: 0.7rem; opacity: 0.7;\">({{getFloorCount(floor)}})</span>\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 戶別/戶型網格或空狀態 -->\n          <div style=\"flex: 1; padding: 16px; overflow-y: auto;\">\n            <div *ngIf=\"!selectedBuilding\" style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\n              <nb-icon icon=\"home-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\n              <p style=\"margin: 0; font-size: 0.875rem;\">請先選擇棟別</p>\n            </div>\n            <div *ngIf=\"selectedBuilding && buildingData[selectedBuilding] && buildingData[selectedBuilding].length > 0\"\n              style=\"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\">\n              <ng-container *ngFor=\"let household of getUniqueHouseholdsForDisplay()\"> <button type=\"button\"\n                  (click)=\"onHouseholdToggle(household.houseId)\" [disabled]=\"isHouseholdDisabled(household.houseId)\"\n                  [style.background-color]=\"isHouseholdSelected(household.houseId) ? '#007bff' : (isHouseholdExcluded(household.houseId) ? '#f8f9fa' : '#fff')\"\n                  [style.color]=\"isHouseholdSelected(household.houseId) ? '#fff' : (isHouseholdExcluded(household.houseId) ? '#6c757d' : '#495057')\"\n                  [style.border]=\"isHouseholdSelected(household.houseId) ? '2px solid #007bff' : (isHouseholdExcluded(household.houseId) ? '1px solid #dee2e6' : '1px solid #ced4da')\"\n                  [style.opacity]=\"isHouseholdDisabled(household.houseId) ? '0.6' : '1'\"\n                  [style.cursor]=\"isHouseholdDisabled(household.houseId) ? 'not-allowed' : 'pointer'\"\n                  style=\"padding: 8px 6px; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 55px; position: relative; display: flex; flex-direction: column; justify-content: center; align-items: center; gap: 3px;\">\n                  <span [style.text-decoration]=\"isHouseholdExcluded(household.houseId) ? 'line-through' : 'none'\"\n                    style=\"font-weight: 600; line-height: 1.2; font-size: 0.85rem;\">\n                    {{household.houseName}}\n                  </span> <span *ngIf=\"!useHouseNameMode && household.floor\"\n                    [style.background-color]=\"isHouseholdSelected(household.houseId) ? 'rgba(255,255,255,0.9)' : '#28a745'\"\n                    [style.color]=\"isHouseholdSelected(household.houseId) ? '#007bff' : '#fff'\"\n                    [style.border]=\"isHouseholdSelected(household.houseId) ? '1px solid rgba(0,123,255,0.3)' : 'none'\"\n                    style=\"font-size: 0.7rem; font-weight: 600; padding: 2px 6px; border-radius: 3px; display: inline-flex; align-items: center; justify-content: center; min-width: 22px;\">\n                    <nb-icon icon=\"layers-outline\" style=\"margin-right: 2px; font-size: 0.6rem;\"></nb-icon>\n                    {{household.floor}}\n                  </span>\n                  <div *ngIf=\"isHouseholdExcluded(household.houseId)\"\n                    style=\"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\">\n                    ✕\n                  </div>\n                </button>\n              </ng-container>\n            </div>\n            <div\n              *ngIf=\"selectedBuilding && (!buildingData[selectedBuilding] || buildingData[selectedBuilding].length === 0) && !searchTerm\"\n              style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\n              <nb-icon icon=\"alert-circle-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\n              <p style=\"margin: 0; font-size: 0.875rem;\">{{displayText.noAvailable}}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nb-card-body>\n\n    <nb-card-footer style=\"padding: 16px; border-top: 1px solid #e9ecef; background-color: #f8f9fa;\">\n      <!-- 統計資訊行 -->\n      <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;\">\n        <div style=\"display: flex; align-items: center; gap: 16px; font-size: 0.875rem; color: #495057;\">\n          <div style=\"display: flex; align-items: center; gap: 4px;\">\n            <nb-icon icon=\"checkmark-circle-outline\" style=\"color: #28a745;\"></nb-icon>\n            <span>已選擇: <strong>{{getSelectedCount()}}</strong> {{displayText.selectedCount}}</span>\n          </div>\n          <div *ngIf=\"maxSelections\" style=\"display: flex; align-items: center; gap: 4px;\">\n            <nb-icon icon=\"alert-circle-outline\" style=\"color: #ffc107;\"></nb-icon>\n            <span>限制: 最多 <strong>{{maxSelections}}</strong> 個</span>\n          </div>\n          <div *ngIf=\"selectedBuilding\" style=\"display: flex; align-items: center; gap: 8px;\">\n            <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\n            <span>當前棟別: </span>\n            <span\n              style=\"background-color: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600;\">\n              {{selectedBuilding}}\n            </span> <span *ngIf=\"!useHouseNameMode && selectedFloor\"\n              style=\"background-color: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600; margin-left: 4px;\">\n              <nb-icon icon=\"layers-outline\" style=\"margin-right: 4px; font-size: 0.7rem;\"></nb-icon>\n              {{selectedFloor}}\n            </span>\n          </div>\n        </div>\n        <div *ngIf=\"searchTerm\"\n          style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 4px;\">\n          搜尋: \"{{searchTerm}}\" ({{getFilteredHouseholdsCount()}} 個結果)\n        </div>\n      </div>\n\n      <!-- 操作按鈕行 -->\n      <div style=\"display: flex; align-items: center; justify-content: space-between; gap: 8px;\">\n        <div style=\"display: flex; gap: 8px;\">\n          <button type=\"button\" *ngIf=\"selectedHouseIds.length > 0\" (click)=\"onClearAll()\"\n            style=\"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\n            <nb-icon icon=\"trash-2-outline\"></nb-icon>\n            清空全部\n          </button>\n          <button type=\"button\" *ngIf=\"searchTerm\" (click)=\"resetSearch()\"\n            style=\"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\n            <nb-icon icon=\"refresh-outline\"></nb-icon>\n            重置搜尋\n          </button>\n        </div>\n\n        <div style=\"display: flex; gap: 8px;\">\n          <button type=\"button\" (click)=\"ref.close()\"\n            style=\"padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem;\">\n            取消\n          </button>\n          <button type=\"button\" (click)=\"ref.close()\"\n            style=\"padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 4px;\">\n            <nb-icon icon=\"checkmark-outline\"></nb-icon>\n            確定選擇 ({{getSelectedCount()}})\n          </button>\n        </div>\n      </div>\n    </nb-card-footer>\n  </nb-card>\n</ng-template>"], "mappings": "AAAA,SAAmCA,YAAY,EAAoCC,UAAU,QAAmD,eAAe;AAC/J,SAA+BC,iBAAiB,QAAQ,gBAAgB;;;;;;;;;ICIlEC,EAHJ,CAAAC,cAAA,aACoF,cACtD,cACA;IACxBD,EAAA,CAAAE,SAAA,kBAA8D;IAC9DF,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAuD;IACrFH,EADqF,CAAAI,YAAA,EAAO,EACtF;IACNJ,EAAA,CAAAC,cAAA,iBAAmI;IAAjDD,EAAA,CAAAK,UAAA,mBAAAC,iEAAAC,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAASD,MAAA,CAAAE,UAAA,EAAY;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAEN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;IAChId,EAAA,CAAAG,MAAA,iCACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;IAEJJ,EADF,CAAAC,cAAA,cAA6B,eACA;IAAAD,EAAA,CAAAG,MAAA,IAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3DJ,EAAA,CAAAE,SAAA,mBAAyD;IAE7DF,EADE,CAAAI,YAAA,EAAM,EACF;;;;;IAdJJ,EAAA,CAAAe,UAAA,cAAAC,iBAAA,CAA4B;IAIIhB,EAAA,CAAAiB,SAAA,GAAuD;IAAvDjB,EAAA,CAAAkB,kBAAA,KAAAR,MAAA,CAAAS,WAAA,CAAAC,cAAA,QAAAV,MAAA,CAAAW,gBAAA,QAAuD;IAEzBrB,EAAA,CAAAiB,SAAA,EAAqB;IAArBjB,EAAA,CAAAe,UAAA,aAAAL,MAAA,CAAAY,QAAA,CAAqB;IAKtDtB,EAAA,CAAAiB,SAAA,GAAyB;IAAzBjB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAc,iBAAA,GAAyB;;;;;IAWlDxB,EAAA,CAAAyB,uBAAA,GAAgC;IAC9BzB,EAAA,CAAAE,SAAA,kBAAsD;IACtDF,EAAA,CAAAG,MAAA,8BACF;;;;;;IACAH,EAAA,CAAAyB,uBAAA,GAAiC;IAC/BzB,EAAA,CAAAG,MAAA,GAEF;;;;;IAFEH,EAAA,CAAAiB,SAAA,EAEF;IAFEjB,EAAA,CAAA0B,kBAAA,MAAAhB,MAAA,CAAAW,gBAAA,iCAAAX,MAAA,CAAAW,gBAAA,WAAAX,MAAA,CAAAS,WAAA,CAAAQ,aAAA,GAAAjB,MAAA,CAAAkB,WAAA,IAAAlB,MAAA,CAAAS,WAAA,CAAAS,WAAA,MAEF;;;;;IAeE5B,EAAA,CAAAC,cAAA,eAAkE;IAAAD,EAAA,CAAAG,MAAA,GAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAArBJ,EAAA,CAAAiB,SAAA,EAAc;IAAdjB,EAAA,CAAAuB,iBAAA,CAAAM,OAAA,CAAAC,KAAA,CAAc;;;;;IAJpF9B,EAAA,CAAAyB,uBAAA,GAAyD;IAErDzB,EADF,CAAAC,cAAA,aAA8B,eACA;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClDJ,EAAA,CAAA+B,UAAA,IAAAC,sEAAA,mBAAkE;IACpEhC,EAAA,CAAAI,YAAA,EAAK;;;;;;IAHyBJ,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAuB,iBAAA,CAAAM,OAAA,CAAAI,QAAA,CAAiB;IACpBjC,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAuB,iBAAA,CAAAM,OAAA,CAAAK,SAAA,CAAkB;IACpClC,EAAA,CAAAiB,SAAA,EAAqC;IAArCjB,EAAA,CAAAe,UAAA,UAAAL,MAAA,CAAAyB,gBAAA,IAAAN,OAAA,CAAAC,KAAA,CAAqC;;;;;IALlD9B,EADF,CAAAC,cAAA,cAAqC,aACV;IACvBD,EAAA,CAAA+B,UAAA,IAAAK,+DAAA,2BAAyD;IAQ7DpC,EADE,CAAAI,YAAA,EAAK,EACD;;;;IAR6BJ,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAA2B,mBAAA,GAAwB;;;;;IA6BrDrC,EAFF,CAAAC,cAAA,cACmG,cAChD;IAC/CD,EAAA,CAAAE,SAAA,kBAAmG;IACnGF,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAE/EH,EAF+E,CAAAI,YAAA,EAAI,EAC3E,EACF;;;;IAFyCJ,EAAA,CAAAiB,SAAA,GAAgC;IAAhCjB,EAAA,CAAA0B,kBAAA,iBAAAhB,MAAA,CAAAS,WAAA,CAAAmB,QAAA,0BAAgC;;;;;;IAazEtC,EAAA,CAAAC,cAAA,iBAG4L;IAHnID,EAAA,CAAAK,UAAA,mBAAAkC,0FAAA;MAAA,MAAAC,WAAA,GAAAxC,EAAA,CAAAQ,aAAA,CAAAiC,GAAA,EAAAC,SAAA;MAAA,MAAAhC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAiC,gBAAA,CAAAH,WAAA,CAA0B;IAAA,EAAC;IAI3FxC,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAG,MAAA,GAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnEJ,EAAA,CAAAC,cAAA,eACgH;IAC9GD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACA;;;;;IAPPJ,EADA,CAAA4C,WAAA,qBAAAlC,MAAA,CAAAmC,gBAAA,KAAAL,WAAA,6BAAoF,gBAAA9B,MAAA,CAAAmC,gBAAA,KAAAL,WAAA,iDACe;IAEnDxC,EAAA,CAAAiB,SAAA,GAAY;IAAZjB,EAAA,CAAAuB,iBAAA,CAAAiB,WAAA,CAAY;IAG1DxC,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA0B,kBAAA,MAAAhB,MAAA,CAAAoC,gBAAA,CAAAN,WAAA,aACF;;;;;IAWuCxC,EAAA,CAAAC,cAAA,eAC0F;IAC7HD,EAAA,CAAAE,SAAA,kBAAqF;IACrFF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA0B,kBAAA,MAAAhB,MAAA,CAAAmC,gBAAA,MACF;;;;;IACA7C,EAAA,CAAAC,cAAA,eAC+H;IAC7HD,EAAA,CAAAE,SAAA,kBAAuF;IACvFF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA0B,kBAAA,MAAAhB,MAAA,CAAAqC,aAAA,MACF;;;;;;IAeA/C,EAAA,CAAAC,cAAA,iBACsI;IAD/ED,EAAA,CAAAK,UAAA,mBAAA2C,iGAAA;MAAAhD,EAAA,CAAAQ,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAwC,qBAAA,EAAuB;IAAA,EAAC;IAEtFlD,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IAbTJ,EAHF,CAAAC,cAAA,cAEmC,iBAGqG;IAFlFD,EAAA,CAAAK,UAAA,mBAAA8C,wFAAA;MAAAnD,EAAA,CAAAQ,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAA2C,mBAAA,EAAqB;IAAA,EAAC;IAGjFrD,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,iBAEsI;IAFlFD,EAAA,CAAAK,UAAA,mBAAAiD,wFAAA;MAAAtD,EAAA,CAAAQ,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAA6C,mBAAA,EAAqB;IAAA,EAAC;IAGjFvD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAA+B,UAAA,IAAAyB,wEAAA,qBACsI;IAGxIxD,EAAA,CAAAI,YAAA,EAAM;;;;IAbFJ,EAAA,CAAAiB,SAAA,EAA+C;IAA/CjB,EAAA,CAAA4C,WAAA,YAAAlC,MAAA,CAAA+C,aAAA,iBAA+C;IAD3BzD,EAAA,CAAAe,UAAA,cAAAL,MAAA,CAAA+C,aAAA,GAA6B;IAMjDzD,EAAA,CAAAiB,SAAA,GAA+C;IAA/CjB,EAAA,CAAA4C,WAAA,YAAAlC,MAAA,CAAA+C,aAAA,iBAA+C;IAD3BzD,EAAA,CAAAe,UAAA,cAAAL,MAAA,CAAA+C,aAAA,GAA6B;IAGjDzD,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAA0B,kBAAA,kBAAAhB,MAAA,CAAAmC,gBAAA,MACF;IACuB7C,EAAA,CAAAiB,SAAA,EAA8B;IAA9BjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAgD,sBAAA,GAA8B;;;;;IAgBvD1D,EAAA,CAAAC,cAAA,cAC+D;IAC7DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAR,MAAA,CAAAS,WAAA,CAAAwC,SAAA,SAAAjD,MAAA,CAAAkD,UAAA,QACF;;;;;;IATE5D,EAFJ,CAAAC,cAAA,cAAuD,cACpB,gBAGuG;IAFnHD,EAAA,CAAA6D,gBAAA,2BAAAC,+FAAAvD,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAuD,IAAA;MAAA,MAAArD,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAAgE,kBAAA,CAAAtD,MAAA,CAAAkD,UAAA,EAAArD,MAAA,MAAAG,MAAA,CAAAkD,UAAA,GAAArD,MAAA;MAAA,OAAAP,EAAA,CAAAa,WAAA,CAAAN,MAAA;IAAA,EAAwB;IAACP,EAAA,CAAAK,UAAA,mBAAA4D,uFAAA1D,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAuD,IAAA;MAAA,MAAArD,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAwD,cAAA,CAAA3D,MAAA,CAAsB;IAAA,EAAC;IAA5EP,EAAA,CAAAI,YAAA,EAEsI;IACtIJ,EAAA,CAAAE,SAAA,kBACiI;IACnIF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAA+B,UAAA,IAAAoC,qEAAA,kBAC+D;IAGjEnE,EAAA,CAAAI,YAAA,EAAM;;;;IAViBJ,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAoE,gBAAA,YAAA1D,MAAA,CAAAkD,UAAA,CAAwB;IACzC5D,EAAA,CAAAe,UAAA,gBAAAL,MAAA,CAAAS,WAAA,CAAAkD,iBAAA,CAA6C;IAK3CrE,EAAA,CAAAiB,SAAA,GAAwC;IAAxCjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAkD,UAAA,IAAAlD,MAAA,CAAA4D,kBAAA,GAAwC;;;;;IAS5CtE,EAAA,CAAAC,cAAA,eAC+H;IAC7HD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAA0B,kBAAA,MAAAhB,MAAA,CAAAqC,aAAA,MACF;;;;;;IACA/C,EAAA,CAAAC,cAAA,iBAC2H;IAD/ED,EAAA,CAAAK,UAAA,mBAAAkE,iGAAA;MAAAvE,EAAA,CAAAQ,aAAA,CAAAgE,IAAA;MAAA,MAAA9D,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAA+D,aAAA,CAAc,EAAE,CAAC;IAAA,EAAC;IAErEzE,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IAGTJ,EAAA,CAAAC,cAAA,iBAIyJ;IAJtGD,EAAA,CAAAK,UAAA,mBAAAqE,iGAAA;MAAA,MAAAC,SAAA,GAAA3E,EAAA,CAAAQ,aAAA,CAAAoE,IAAA,EAAAlC,SAAA;MAAA,MAAAhC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAA+D,aAAA,CAAAE,SAAA,CAAoB;IAAA,EAAC;IAK/E3E,EAAA,CAAAE,SAAA,kBAAuF;IACvFF,EAAA,CAAAG,MAAA,GAAU;IAAAH,EAAA,CAAAC,cAAA,gBAA+C;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IACrFH,EADqF,CAAAI,YAAA,EAAO,EACnF;;;;;IAJPJ,EAFA,CAAA4C,WAAA,qBAAAlC,MAAA,CAAAqC,aAAA,KAAA4B,SAAA,yBAA0E,UAAAjE,MAAA,CAAAqC,aAAA,KAAA4B,SAAA,sBACd,WAAAjE,MAAA,CAAAqC,aAAA,KAAA4B,SAAA,6CACwB;IAGpF3E,EAAA,CAAAiB,SAAA,GAAU;IAAVjB,EAAA,CAAA0B,kBAAA,MAAAiD,SAAA,MAAU;IAA+C3E,EAAA,CAAAiB,SAAA,GAA0B;IAA1BjB,EAAA,CAAA0B,kBAAA,MAAAhB,MAAA,CAAAmE,aAAA,CAAAF,SAAA,OAA0B;;;;;IAnBvF3E,EADF,CAAAC,cAAA,cAAkG,cACjB;IAC7ED,EAAA,CAAAE,SAAA,kBAAkF;IAClFF,EAAA,CAAAC,cAAA,eAAqE;IAAAD,EAAA,CAAAG,MAAA,gCAAK;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAKjFJ,EAJA,CAAA+B,UAAA,IAAA+C,sEAAA,mBAC+H,IAAAC,wEAAA,qBAIJ;IAG7H/E,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAA+B,UAAA,IAAAiD,wEAAA,qBAIyJ;IAK7JhF,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAnBKJ,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAqC,aAAA,CAAmB;IAIH/C,EAAA,CAAAiB,SAAA,EAAmB;IAAnBjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAqC,aAAA,CAAmB;IAMF/C,EAAA,CAAAiB,SAAA,GAAS;IAATjB,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAAuE,MAAA,CAAS;;;;;IAcrDjF,EAAA,CAAAC,cAAA,eAA+F;IAC7FD,EAAA,CAAAE,SAAA,mBAAkG;IAClGF,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAG,MAAA,2CAAM;IACnDH,EADmD,CAAAI,YAAA,EAAI,EACjD;;;;;IAcQJ,EAAA,CAAAC,cAAA,gBAIkK;IACxKD,EAAA,CAAAE,SAAA,mBAAuF;IACvFF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAJLJ,EAFA,CAAA4C,WAAA,qBAAAlC,MAAA,CAAAwE,mBAAA,CAAAC,aAAA,CAAAC,OAAA,wCAAuG,UAAA1E,MAAA,CAAAwE,mBAAA,CAAAC,aAAA,CAAAC,OAAA,uBAC5B,WAAA1E,MAAA,CAAAwE,mBAAA,CAAAC,aAAA,CAAAC,OAAA,6CACuB;IAGlGpF,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA0B,kBAAA,MAAAyD,aAAA,CAAArD,KAAA,MACF;;;;;IACA9B,EAAA,CAAAC,cAAA,eACsN;IACpND,EAAA,CAAAG,MAAA,eACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;;IAtBVJ,EAAA,CAAAyB,uBAAA,GAAwE;IAACzB,EAAA,CAAAC,cAAA,kBAOkL;IANvPD,EAAA,CAAAK,UAAA,mBAAAgF,uGAAA;MAAA,MAAAF,aAAA,GAAAnF,EAAA,CAAAQ,aAAA,CAAA8E,IAAA,EAAA5C,SAAA;MAAA,MAAAhC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAA6E,iBAAA,CAAAJ,aAAA,CAAAC,OAAA,CAAoC;IAAA,EAAC;IAO9CpF,EAAA,CAAAC,cAAA,gBACkE;IAChED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAQPJ,EARQ,CAAA+B,UAAA,IAAAyD,qFAAA,oBAIkK,IAAAC,oFAAA,mBAK4C;IAGxNzF,EAAA,CAAAI,YAAA,EAAS;;;;;;IArBPJ,EAAA,CAAAiB,SAAA,EAA6I;IAI7IjB,EAJA,CAAA4C,WAAA,qBAAAlC,MAAA,CAAAwE,mBAAA,CAAAC,aAAA,CAAAC,OAAA,gBAAA1E,MAAA,CAAAgF,mBAAA,CAAAP,aAAA,CAAAC,OAAA,uBAA6I,UAAA1E,MAAA,CAAAwE,mBAAA,CAAAC,aAAA,CAAAC,OAAA,aAAA1E,MAAA,CAAAgF,mBAAA,CAAAP,aAAA,CAAAC,OAAA,0BACX,WAAA1E,MAAA,CAAAwE,mBAAA,CAAAC,aAAA,CAAAC,OAAA,0BAAA1E,MAAA,CAAAgF,mBAAA,CAAAP,aAAA,CAAAC,OAAA,8CACkC,YAAA1E,MAAA,CAAAiF,mBAAA,CAAAR,aAAA,CAAAC,OAAA,gBAC9F,WAAA1E,MAAA,CAAAiF,mBAAA,CAAAR,aAAA,CAAAC,OAAA,8BACa;IALpCpF,EAAA,CAAAe,UAAA,aAAAL,MAAA,CAAAiF,mBAAA,CAAAR,aAAA,CAAAC,OAAA,EAAmD;IAO5FpF,EAAA,CAAAiB,SAAA,EAA0F;IAA1FjB,EAAA,CAAA4C,WAAA,oBAAAlC,MAAA,CAAAgF,mBAAA,CAAAP,aAAA,CAAAC,OAAA,4BAA0F;IAE9FpF,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAA0B,kBAAA,MAAAyD,aAAA,CAAAjD,SAAA,MACF;IAAelC,EAAA,CAAAiB,SAAA,EAA0C;IAA1CjB,EAAA,CAAAe,UAAA,UAAAL,MAAA,CAAAyB,gBAAA,IAAAgD,aAAA,CAAArD,KAAA,CAA0C;IAQnD9B,EAAA,CAAAiB,SAAA,EAA4C;IAA5CjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAgF,mBAAA,CAAAP,aAAA,CAAAC,OAAA,EAA4C;;;;;IArBxDpF,EAAA,CAAAC,cAAA,eACgG;IAC9FD,EAAA,CAAA+B,UAAA,IAAA6D,8EAAA,4BAAwE;IAyB1E5F,EAAA,CAAAI,YAAA,EAAM;;;;IAzBgCJ,EAAA,CAAAiB,SAAA,EAAkC;IAAlCjB,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAAmF,6BAAA,GAAkC;;;;;IA0BxE7F,EAAA,CAAAC,cAAA,eAEkE;IAChED,EAAA,CAAAE,SAAA,mBAA0G;IAC1GF,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IACxEH,EADwE,CAAAI,YAAA,EAAI,EACtE;;;;IADuCJ,EAAA,CAAAiB,SAAA,GAA2B;IAA3BjB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAS,WAAA,CAAA2E,WAAA,CAA2B;;;;;IAnIxE9F,EALN,CAAAC,cAAA,cAAgF,cAG6C,cACtD,aACa;IAAAD,EAAA,CAAAG,MAAA,+BAAI;IACpFH,EADoF,CAAAI,YAAA,EAAK,EACnF;IACNJ,EAAA,CAAAC,cAAA,cAAwC;IACtCD,EAAA,CAAA+B,UAAA,IAAAgE,iEAAA,qBAG4L;IAQhM/F,EADE,CAAAI,YAAA,EAAM,EACF;IAOEJ,EAJR,CAAAC,cAAA,cAA6D,cACQ,cACoC,eACxC,cACqB;IAC5ED,EAAA,CAAAG,MAAA,IAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAKpCJ,EALqC,CAAA+B,UAAA,KAAAiE,gEAAA,mBAC0F,KAAAC,gEAAA,mBAKA;IAIjIjG,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAA+B,UAAA,KAAAmE,+DAAA,kBAEmC;IAgBrClG,EAAA,CAAAI,YAAA,EAAM;IAgBNJ,EAbA,CAAA+B,UAAA,KAAAoE,+DAAA,kBAAuD,KAAAC,+DAAA,kBAa2C;IAwBpGpG,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,eAAuD;IAiCrDD,EAhCA,CAAA+B,UAAA,KAAAsE,+DAAA,kBAA+F,KAAAC,+DAAA,kBAKC,KAAAC,+DAAA,kBA6B9B;IAMxEvG,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IApI6BJ,EAAA,CAAAiB,SAAA,GAAY;IAAZjB,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAA8F,SAAA,CAAY;IAmBnCxG,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAA0B,kBAAA,MAAAhB,MAAA,CAAAS,WAAA,CAAAsF,aAAA,KAA6B;IAAazG,EAAA,CAAAiB,SAAA,EAAsB;IAAtBjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAmC,gBAAA,CAAsB;IAK3D7C,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAe,UAAA,UAAAL,MAAA,CAAAyB,gBAAA,IAAAzB,MAAA,CAAAqC,aAAA,CAAwC;IAO9C/C,EAAA,CAAAiB,SAAA,EAAyH;IAAzHjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAgG,gBAAA,IAAAhG,MAAA,CAAAmC,gBAAA,IAAAnC,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAmC,gBAAA,KAAAnC,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAmC,gBAAA,EAAA+D,MAAA,KAAyH;IAoBxH5G,EAAA,CAAAiB,SAAA,EAAsB;IAAtBjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAmC,gBAAA,CAAsB;IAatB7C,EAAA,CAAAiB,SAAA,EAAgE;IAAhEjB,EAAA,CAAAe,UAAA,UAAAL,MAAA,CAAAyB,gBAAA,IAAAzB,MAAA,CAAAmC,gBAAA,IAAAnC,MAAA,CAAAuE,MAAA,CAAA2B,MAAA,KAAgE;IA4BhE5G,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAe,UAAA,UAAAL,MAAA,CAAAmC,gBAAA,CAAuB;IAIvB7C,EAAA,CAAAiB,SAAA,EAAqG;IAArGjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAmC,gBAAA,IAAAnC,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAmC,gBAAA,KAAAnC,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAmC,gBAAA,EAAA+D,MAAA,KAAqG;IA6BxG5G,EAAA,CAAAiB,SAAA,EAAyH;IAAzHjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAmC,gBAAA,MAAAnC,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAmC,gBAAA,KAAAnC,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAmC,gBAAA,EAAA+D,MAAA,YAAAlG,MAAA,CAAAkD,UAAA,CAAyH;;;;;IAkB9H5D,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAE,SAAA,mBAAuE;IACvEF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,kCAAO;IAAAH,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,cAAC;IACnDH,EADmD,CAAAI,YAAA,EAAO,EACpD;;;;IADiBJ,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAmG,aAAA,CAAiB;;;;;IAQ9B7G,EAAA,CAAAC,cAAA,gBACyI;IAC/ID,EAAA,CAAAE,SAAA,kBAAuF;IACvFF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA0B,kBAAA,MAAAhB,MAAA,CAAAqC,aAAA,MACF;;;;;IAVF/C,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAE,SAAA,mBAA+D;IAC/DF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,iCAAM;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnBJ,EAAA,CAAAC,cAAA,gBAC+H;IAC7HD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAACJ,EAAA,CAAA+B,UAAA,IAAA+E,+DAAA,oBACyI;IAInJ9G,EAAA,CAAAI,YAAA,EAAM;;;;IANFJ,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA0B,kBAAA,MAAAhB,MAAA,CAAAmC,gBAAA,MACF;IAAe7C,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAe,UAAA,UAAAL,MAAA,CAAAyB,gBAAA,IAAAzB,MAAA,CAAAqC,aAAA,CAAwC;;;;;IAO3D/C,EAAA,CAAAC,cAAA,eAC+G;IAC7GD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,sBAAAR,MAAA,CAAAkD,UAAA,UAAAlD,MAAA,CAAAqG,0BAAA,4BACF;;;;;;IAME/G,EAAA,CAAAC,cAAA,kBACsL;IAD5HD,EAAA,CAAAK,UAAA,mBAAA2G,oFAAA;MAAAhH,EAAA,CAAAQ,aAAA,CAAAyG,IAAA;MAAA,MAAAvG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAE,UAAA,EAAY;IAAA,EAAC;IAE9EZ,EAAA,CAAAE,SAAA,mBAA0C;IAC1CF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IACTJ,EAAA,CAAAC,cAAA,kBACsL;IAD7ID,EAAA,CAAAK,UAAA,mBAAA6G,oFAAA;MAAAlH,EAAA,CAAAQ,aAAA,CAAA2G,IAAA;MAAA,MAAAzG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAA0G,WAAA,EAAa;IAAA,EAAC;IAE9DpH,EAAA,CAAAE,SAAA,mBAA0C;IAC1CF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IA9MXJ,EAHN,CAAAC,cAAA,kBAAmE,qBACjD,cACmE,cACpB;IACzDD,EAAA,CAAAE,SAAA,kBAAkF;IAClFF,EAAA,CAAAC,cAAA,eAAoE;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrGJ,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAC/EH,EAD+E,CAAAI,YAAA,EAAO,EAChF;IACNJ,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAG,MAAA,IAA2B;IAElFH,EAFkF,CAAAI,YAAA,EAAO,EACjF,EACS;IAEjBJ,EAAA,CAAAC,cAAA,wBAAoD;IAWlDD,EATA,CAAA+B,UAAA,KAAAsF,wDAAA,kBACmG,KAAAC,wDAAA,oBAQnB;IA6IlFtH,EAAA,CAAAI,YAAA,EAAe;IAMTJ,EAJN,CAAAC,cAAA,0BAAiG,eAEO,eACH,eACpC;IACzDD,EAAA,CAAAE,SAAA,mBAA2E;IAC3EF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,4BAAK;IAAAH,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAG,MAAA,IAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,IAA6B;IAClFH,EADkF,CAAAI,YAAA,EAAO,EACnF;IAKNJ,EAJA,CAAA+B,UAAA,KAAAwF,wDAAA,kBAAiF,KAAAC,wDAAA,kBAIG;IAYtFxH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAA+B,UAAA,KAAA0F,wDAAA,kBAC+G;IAGjHzH,EAAA,CAAAI,YAAA,EAAM;IAIJJ,EADF,CAAAC,cAAA,eAA2F,eACnD;IAMpCD,EALA,CAAA+B,UAAA,KAAA2F,2DAAA,qBACsL,KAAAC,2DAAA,qBAKA;IAIxL3H,EAAA,CAAAI,YAAA,EAAM;IAGJJ,EADF,CAAAC,cAAA,eAAsC,kBAEoG;IADlHD,EAAA,CAAAK,UAAA,mBAAAuH,2EAAA;MAAA,MAAAC,OAAA,GAAA7H,EAAA,CAAAQ,aAAA,CAAAsH,GAAA,EAAAC,SAAA;MAAA,OAAA/H,EAAA,CAAAa,WAAA,CAASgH,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEzChI,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBACwM;IADlLD,EAAA,CAAAK,UAAA,mBAAA4H,2EAAA;MAAA,MAAAJ,OAAA,GAAA7H,EAAA,CAAAQ,aAAA,CAAAsH,GAAA,EAAAC,SAAA;MAAA,OAAA/H,EAAA,CAAAa,WAAA,CAASgH,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEzChI,EAAA,CAAAE,SAAA,mBAA4C;IAC5CF,EAAA,CAAAG,MAAA,IACF;IAIRH,EAJQ,CAAAI,YAAA,EAAS,EACL,EACF,EACS,EACT;;;;IA5NkEJ,EAAA,CAAAiB,SAAA,GAA0B;IAA1BjB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAS,WAAA,CAAA+G,UAAA,CAA0B;IAC3ClI,EAAA,CAAAiB,SAAA,GAA0B;IAA1BjB,EAAA,CAAA0B,kBAAA,MAAAhB,MAAA,CAAA8F,SAAA,CAAAI,MAAA,yBAA0B;IAE5B5G,EAAA,CAAAiB,SAAA,GAA2B;IAA3BjB,EAAA,CAAA0B,kBAAA,yBAAAhB,MAAA,CAAAW,gBAAA,OAA2B;IAM1ErB,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAyH,SAAA,CAAe;IASfnI,EAAA,CAAAiB,SAAA,EAAgB;IAAhBjB,EAAA,CAAAe,UAAA,UAAAL,MAAA,CAAAyH,SAAA,CAAgB;IAqJGnI,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAuB,iBAAA,CAAAb,MAAA,CAAAW,gBAAA,GAAsB;IAAUrB,EAAA,CAAAiB,SAAA,EAA6B;IAA7BjB,EAAA,CAAA0B,kBAAA,MAAAhB,MAAA,CAAAS,WAAA,CAAAQ,aAAA,KAA6B;IAE5E3B,EAAA,CAAAiB,SAAA,EAAmB;IAAnBjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAmG,aAAA,CAAmB;IAInB7G,EAAA,CAAAiB,SAAA,EAAsB;IAAtBjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAmC,gBAAA,CAAsB;IAaxB7C,EAAA,CAAAiB,SAAA,EAAgB;IAAhBjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAkD,UAAA,CAAgB;IASG5D,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAA0H,gBAAA,CAAAxB,MAAA,KAAiC;IAKjC5G,EAAA,CAAAiB,SAAA,EAAgB;IAAhBjB,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAkD,UAAA,CAAgB;IAerC5D,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA0B,kBAAA,gCAAAhB,MAAA,CAAAW,gBAAA,SACF;;;ADvPV,OAAM,MAAOgH,yBAAyB;EAyBLC,YACrBC,GAAsB,EACtBC,aAA8B;IAD9B,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,aAAa,GAAbA,aAAa;IAzBd,KAAA5G,WAAW,GAAW,OAAO;IAC7B,KAAAiF,aAAa,GAAkB,IAAI;IACnC,KAAAvF,QAAQ,GAAY,KAAK;IACzB,KAAAmH,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAA9B,YAAY,GAAiB,EAAE;IAC/B,KAAAD,gBAAgB,GAAY,IAAI;IAChC,KAAAgC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAvG,gBAAgB,GAAY,KAAK,CAAC,CAAC;IAElC,KAAAwG,eAAe,GAAG,IAAI9I,YAAY,EAAmB;IACrD,KAAA+I,aAAa,GAAG,IAAI/I,YAAY,EAAY,CAAC,CAAC;IAC9C,KAAAgJ,eAAe,GAAG,IAAIhJ,YAAY,EAAY,CAAC,CAAC;IAC1D,KAAAiJ,MAAM,GAAG,KAAK;IACd,KAAAjG,gBAAgB,GAAG,EAAE;IACrB,KAAAe,UAAU,GAAG,EAAE;IAAE,KAAAb,aAAa,GAAG,EAAE,CAAC,CAAC;IACrC,KAAAqF,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAW,kBAAkB,GAAa,EAAE,CAAC,CAAC;IACnC,KAAAvC,SAAS,GAAa,EAAE;IACxB,KAAAvB,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAA+D,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE,CAAC,CAAC;IAC3D,KAAAd,SAAS,GAAY,KAAK,CAAC,CAAC;IACpB,KAAAe,QAAQ,GAAIC,KAA0B,IAAI,CAAG,CAAC;IAC9C,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAGzB;EAEJ;;;;;EAKA5H,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC4G,gBAAgB,CAACxB,MAAM,KAAK,CAAC,EAAE;MACtC,OAAO,EAAE;IACX;IAEA,OAAO,IAAI,CAACJ,SAAS,CAClB6C,GAAG,CAACpH,QAAQ,IAAG;MACd,MAAMqH,KAAK,GAAG,IAAI,CAACL,kBAAkB,CAAChH,QAAQ,CAAC,EAAE2E,MAAM,IAAI,CAAC;MAC5D,OAAO0C,KAAK,GAAG,CAAC,GAAG,GAAGrH,QAAQ,KAAKqH,KAAK,IAAI,GAAG,IAAI;IACrD,CAAC,CAAC,CACDC,MAAM,CAACC,OAAO,CAAC,CAAC;IAAA,CAChBC,IAAI,CAAC,IAAI,CAAC;EACf;EAEA;;;;;EAKApH,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC+F,gBAAgB,CACzBiB,GAAG,CAACK,EAAE,IAAI,IAAI,CAACC,qBAAqB,CAACD,EAAE,CAAC,CAAC,CACzCH,MAAM,CAAEK,IAAI,IAA4B,CAAC,CAACA,IAAI,CAAC;EACpD;EAEAC,UAAUA,CAACV,KAAY;IACrB,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACvC,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACwB,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACW,kBAAkB,GAAG,EAAE;IAC9B,CAAC,MAAM;MACL,MAAMe,SAAS,GAAGX,KAAK,CAAC,CAAC,CAAC;MAAE,IAAI,IAAI,CAAChH,gBAAgB,EAAE;QACrD;QACA,IAAI,OAAO2H,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACf,kBAAkB,GAAG,CAAC,GAAG,IAAIgB,GAAG,CAACZ,KAAiB,CAAC,CAAC,CAAC,CAAC;UAC3D;UACA,IAAI,CAACf,gBAAgB,GAAG,IAAI,CAAC4B,sBAAsB,CAAC,IAAI,CAACjB,kBAAkB,CAAC;UAC5EkB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACnD,CAAC,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAE,OAAOL,SAAS,CAAC;UACtE,IAAI,CAACf,kBAAkB,GAAG,EAAE;UAC5B,IAAI,CAACX,gBAAgB,GAAG,EAAE;QAC5B;MACF,CAAC,MAAM;QACL;QACA,IAAI,OAAO0B,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAAC1B,gBAAgB,GAAGe,KAAiB;UACzC;UACA,IAAI,CAACJ,kBAAkB,GAAG,IAAI,CAACqB,sBAAsB,CAAC,IAAI,CAAChC,gBAAgB,CAAC;UAC5E6B,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACvC,CAAC,MAAM,IAAI,OAAOJ,SAAS,KAAK,QAAQ,EAAE;UACxC;UACAG,OAAO,CAACE,KAAK,CAAC,oCAAoC,CAAC;UACnDF,OAAO,CAACE,KAAK,CAAC,2CAA2C,CAAC;UAC1D;QACF,CAAC,MAAM;UACLF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEhB,KAAK,CAAC;UAC7C,IAAI,CAACf,gBAAgB,GAAG,EAAE;UAC1B,IAAI,CAACW,kBAAkB,GAAG,EAAE;QAC9B;MACF;IACF;IACA,IAAI,CAACsB,wBAAwB,EAAE;EACjC;EACAC,gBAAgBA,CAACC,EAAwC;IACvD,IAAI,CAACrB,QAAQ,GAAGqB,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACnB,SAAS,GAAGmB,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACpJ,QAAQ,GAAGoJ,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EAAEC,WAAWA,CAACC,OAAsB;IAClC,IAAIA,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B;MACA,IAAI,CAACtE,SAAS,GAAGuE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrE,YAAY,IAAI,EAAE,CAAC;MACrDsD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACvD,YAAY,CAAC;MACvD,IAAI,CAACsE,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IAAE,IAAIS,OAAO,CAAC,kBAAkB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;IACjC;IACA,IAAIH,OAAO,CAAC,kBAAkB,CAAC,EAAE;MAC/B,IAAI,IAAI,CAAC3I,gBAAgB,EAAE;QACzB,IAAI,CAACY,aAAa,GAAG,EAAE;MACzB;IACF;EACF;EAAU6H,cAAcA,CAAA;IACtB;IACA,IAAI,IAAI,CAACjE,YAAY,IAAIoE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrE,YAAY,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAClE,IAAI,CAACJ,SAAS,GAAGuE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrE,YAAY,CAAC;MAC/C,IAAI,CAAC0D,wBAAwB,EAAE;IACjC,CAAC,MAAM;MACL;MACA,IAAI,CAAC7D,SAAS,GAAG,EAAE;MACnByD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC;EACF;EAAUG,wBAAwBA,CAAA;IAChC,MAAMa,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAAC9C,gBAAgB,CAAC+C,OAAO,CAAC/F,OAAO,IAAG;MACtC,KAAK,MAAMnD,QAAQ,IAAI,IAAI,CAACuE,SAAS,EAAE;QACrC,MAAMoD,IAAI,GAAG,IAAI,CAACjD,YAAY,CAAC1E,QAAQ,CAAC,EAAEmJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjG,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAIwE,IAAI,EAAE;UACR,IAAI,CAACsB,OAAO,CAACjJ,QAAQ,CAAC,EAAEiJ,OAAO,CAACjJ,QAAQ,CAAC,GAAG,EAAE;UAC9CiJ,OAAO,CAACjJ,QAAQ,CAAC,CAACqJ,IAAI,CAAClG,OAAO,CAAC;UAC/B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAAC6D,kBAAkB,GAAGiC,OAAO;EACnC;EAAEvI,gBAAgBA,CAACV,QAAgB;IACjCgI,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEjI,QAAQ,CAAC;IAC3C,IAAI,CAACY,gBAAgB,GAAGZ,QAAQ;IAChC,IAAI,CAACc,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACa,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC2H,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAACN,wBAAwB,EAAE;IAC/BhB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAClB,kBAAkB,CAACpC,MAAM,CAAC;IACzE;IACA,IAAI,CAAC2B,GAAG,CAACiD,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAACxJ,QAAgB;IAC9BgI,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEjI,QAAQ,CAAC;EACxD;EACAgJ,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACpI,gBAAgB,EAAE;MAC1B,IAAI,CAACmG,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAM0C,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,IAAI,EAAE;IACjEoH,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEwB,UAAU,CAAC9E,MAAM,CAAC;IAEpE;IACA,MAAM+E,aAAa,GAAGD,UAAU,CAACnC,MAAM,CAAC8B,CAAC,IAAG;MAC1C;MACA,MAAMO,UAAU,GAAG,IAAI,CAACzJ,gBAAgB,IAAI,CAAC,IAAI,CAACY,aAAa,IAAIsI,CAAC,CAACvJ,KAAK,KAAK,IAAI,CAACiB,aAAa;MACjG;MACA,MAAM8I,WAAW,GAAG,CAAC,IAAI,CAACjI,UAAU,IAAIyH,CAAC,CAACnJ,SAAS,CAAC4J,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACnI,UAAU,CAACkI,WAAW,EAAE,CAAC;MACzG,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC7C,kBAAkB,GAAG2C,aAAa,CAACtC,GAAG,CAACgC,CAAC,IAAIA,CAAC,CAACnJ,SAAS,CAAC;IAE7D+H,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAClB,kBAAkB,CAACpC,MAAM,CAAC;IAC1EqD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEyB,aAAa,CAACtC,GAAG,CAACgC,CAAC,IAAI,GAAGA,CAAC,CAACnJ,SAAS,IAAImJ,CAAC,CAACvJ,KAAK,QAAQuJ,CAAC,CAACjG,OAAO,GAAG,CAAC,CAAC;EAC/G;EAEAlB,cAAcA,CAAC8H,KAAU;IACvB,IAAI,CAACpI,UAAU,GAAGoI,KAAK,CAACC,MAAM,CAAC9C,KAAK;IACpC,IAAI,CAAC8B,wBAAwB,EAAE;IAC/BhB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACtG,UAAU,CAAC;EACtD;EACAwD,WAAWA,CAAA;IACT,IAAI,CAACxD,UAAU,GAAG,EAAE;IACpB,IAAI,CAACqH,wBAAwB,EAAE;IAC/BhB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EAAE3E,iBAAiBA,CAACH,OAA2B;IAC7C6E,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE9E,OAAO,CAAC;IAC9D6E,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC9B,gBAAgB,CAAC;IAE/D,IAAI,CAAChD,OAAO,EAAE;MACZ6E,OAAO,CAACC,GAAG,CAAC,gBAAgB9E,OAAO,EAAE,CAAC;MACtC;IACF;IAEA;IACA,IAAI,IAAI,CAAC8G,iBAAiB,CAAC9G,OAAO,CAAC,EAAE;MACnC6E,OAAO,CAACC,GAAG,CAAC,SAAS9E,OAAO,kBAAkB,CAAC;MAC/C;IACF;IAEA;IACA,MAAM+G,gBAAgB,GAAG,IAAI,CAACxC,qBAAqB,CAACvE,OAAO,CAAC;IAC5D,IAAI,CAAC+G,gBAAgB,EAAE;MACrBlC,OAAO,CAACC,GAAG,CAAC,eAAe9E,OAAO,UAAU,CAAC;MAC7C;IACF;IAEA,IAAIgH,YAAsB;IAE1B,IAAI,IAAI,CAACjK,gBAAgB,EAAE;MACzB;MACA,MAAMD,SAAS,GAAGiK,gBAAgB,CAACjK,SAAS;MAC5C,MAAMmK,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACpK,SAAS,CAAC;MAErE;MACA,MAAMqK,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAAC9C,EAAE,IAAI,IAAI,CAACtB,gBAAgB,CAAC2D,QAAQ,CAACrC,EAAE,CAAC,CAAC;MAEzF,IAAI6C,cAAc,EAAE;QAClB;QACAH,YAAY,GAAG,IAAI,CAAChE,gBAAgB,CAACmB,MAAM,CAACG,EAAE,IAAI,CAAC2C,mBAAmB,CAACN,QAAQ,CAACrC,EAAE,CAAC,CAAC;QACpFO,OAAO,CAACC,GAAG,CAAC,+BAA+BhI,SAAS,IAAI,EAAEmK,mBAAmB,CAAC;MAChF,CAAC,MAAM;QACL;QACA,IAAI,IAAI,CAACxF,aAAa,IAAI,IAAI,CAACuB,gBAAgB,CAACxB,MAAM,IAAI,IAAI,CAACC,aAAa,EAAE;UAC5EoD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;UACxB;QACF;QACAkC,YAAY,GAAG,CAAC,GAAG,IAAI,CAAChE,gBAAgB,EAAEiE,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACjEpC,OAAO,CAACC,GAAG,CAAC,2BAA2BhI,SAAS,WAAW,EAAEmK,mBAAmB,CAAC,CAAC,CAAC,CAAC;MACtF;IACF,CAAC,MAAM;MACL;MACA,MAAMI,UAAU,GAAG,IAAI,CAACC,iBAAiB,CAACtH,OAAO,CAAC;MAElD,IAAIqH,UAAU,EAAE;QACdL,YAAY,GAAG,IAAI,CAAChE,gBAAgB,CAACmB,MAAM,CAACG,EAAE,IAAIA,EAAE,KAAKtE,OAAO,CAAC;MACnE,CAAC,MAAM;QACL,IAAI,IAAI,CAACyB,aAAa,IAAI,IAAI,CAACuB,gBAAgB,CAACxB,MAAM,IAAI,IAAI,CAACC,aAAa,EAAE;UAC5EoD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;UACxB;QACF;QACAkC,YAAY,GAAG,CAAC,GAAG,IAAI,CAAChE,gBAAgB,EAAEhD,OAAO,CAAC;MACpD;IACF;IAEA,IAAI,CAACgD,gBAAgB,GAAGgE,YAAY;IACpC,IAAI,CAACO,WAAW,EAAE;EACpB;EACAC,iBAAiBA,CAACxH,OAAe;IAC/B,IAAI,CAACgD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACmB,MAAM,CAACG,EAAE,IAAIA,EAAE,KAAKtE,OAAO,CAAC;IAC1E,IAAI,CAACuH,WAAW,EAAE;EACpB;EAAEtJ,mBAAmBA,CAAA;IACnB4G,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACrH,gBAAgB,CAAC;IACvDoH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACnH,aAAa,CAAC;IACjDkH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACtG,UAAU,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACf,gBAAgB,EAAE;MAC1BoH,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEA;IACA,MAAM2C,sBAAsB,GAAG,IAAI,CAAChH,6BAA6B,EAAE;IAEnEoE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE2C,sBAAsB,CAACxD,GAAG,CAACgC,CAAC,IAAI,GAAGA,CAAC,CAACnJ,SAAS,IAAImJ,CAAC,CAACvJ,KAAK,QAAQuJ,CAAC,CAACjG,OAAO,GAAG,CAAC,CAAC;IAExH,IAAIyH,sBAAsB,CAACjG,MAAM,KAAK,CAAC,EAAE;MACvCqD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;IACF;IAEA;IACA,MAAM4C,YAAY,GAAG,IAAI,CAAC1E,gBAAgB,CAACxB,MAAM;IACjD,MAAMmG,UAAU,GAAG,IAAI,CAAClG,aAAa,IAAImG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMI,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAMC,SAAS,IAAIN,sBAAsB,EAAE;MAC9C,IAAIM,SAAS,CAAC/H,OAAO,EAAE;QACrB,IAAI,IAAI,CAACjD,gBAAgB,EAAE;UACzB;UACA,MAAMkK,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACa,SAAS,CAACjL,SAAS,CAAC;UAC/E,MAAMqK,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAAC9C,EAAE,IAAI,IAAI,CAACtB,gBAAgB,CAAC2D,QAAQ,CAACrC,EAAE,CAAC,CAAC;UACzF,MAAM0D,cAAc,GAAGf,mBAAmB,CAACG,IAAI,CAAC9C,EAAE,IAAI,IAAI,CAACwC,iBAAiB,CAACxC,EAAE,CAAC,CAAC;UAEjF,IAAI,CAAC6C,cAAc,IAAI,CAACa,cAAc,EAAE;YACtCF,qBAAqB,CAAC5B,IAAI,CAACe,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD;QACF,CAAC,MAAM;UACL;UACA,IAAI,CAAC,IAAI,CAACK,iBAAiB,CAACS,SAAS,CAAC/H,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC8G,iBAAiB,CAACiB,SAAS,CAAC/H,OAAO,CAAC,EAAE;YAC5F8H,qBAAqB,CAAC5B,IAAI,CAAC6B,SAAS,CAAC/H,OAAO,CAAC;UAC/C;QACF;MACF;IACF;IAEA6E,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEgD,qBAAqB,CAAC;IAE9D;IACA,MAAMG,KAAK,GAAGH,qBAAqB,CAACI,KAAK,CAAC,CAAC,EAAEL,cAAc,CAAC;IAE5D,IAAII,KAAK,CAACzG,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACwB,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGiF,KAAK,CAAC;MAC5D,IAAI,CAACV,WAAW,EAAE;MAClB1C,OAAO,CAACC,GAAG,CAAC,aAAamD,KAAK,CAACzG,MAAM,WAAW,EAAEyG,KAAK,CAAC;IAC1D,CAAC,MAAM;MACLpD,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF;EAAE3G,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACV,gBAAgB,EAAE;IAE5B;IACA,MAAM0K,kBAAkB,GAAG,IAAI,CAAC5G,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,IAAI,EAAE;IAEzE;IACA,MAAMiK,YAAY,GAAG,IAAI,CAAC1E,gBAAgB,CAACxB,MAAM;IACjD,MAAMmG,UAAU,GAAG,IAAI,CAAClG,aAAa,IAAImG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMU,qBAAqB,GAAa,EAAE;IAE1C,IAAI,IAAI,CAACrL,gBAAgB,EAAE;MACzB;MACA,MAAMsL,mBAAmB,GAAG,IAAI1D,GAAG,EAAU;MAE7C,KAAK,MAAMoD,SAAS,IAAII,kBAAkB,EAAE;QAC1C,IAAIJ,SAAS,CAAC/H,OAAO,IAAI+H,SAAS,CAACjL,SAAS,IAAI,CAACuL,mBAAmB,CAACC,GAAG,CAACP,SAAS,CAACjL,SAAS,CAAC,EAAE;UAC7FuL,mBAAmB,CAACE,GAAG,CAACR,SAAS,CAACjL,SAAS,CAAC;UAE5C,MAAMmK,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACa,SAAS,CAACjL,SAAS,CAAC;UAC/E,MAAMqK,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAAC9C,EAAE,IAAI,IAAI,CAACtB,gBAAgB,CAAC2D,QAAQ,CAACrC,EAAE,CAAC,CAAC;UACzF,MAAM0D,cAAc,GAAGf,mBAAmB,CAACG,IAAI,CAAC9C,EAAE,IAAI,IAAI,CAAChE,mBAAmB,CAACgE,EAAE,CAAC,CAAC;UAEnF,IAAI,CAAC6C,cAAc,IAAI,CAACa,cAAc,EAAE;YACtCI,qBAAqB,CAAClC,IAAI,CAACe,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD;QACF;MACF;IACF,CAAC,MAAM;MACL;MACA,KAAK,MAAMc,SAAS,IAAII,kBAAkB,EAAE;QAC1C,IAAIJ,SAAS,CAAC/H,OAAO,IACnB,CAAC,IAAI,CAACgD,gBAAgB,CAAC2D,QAAQ,CAACoB,SAAS,CAAC/H,OAAO,CAAC,IAClD,CAAC,IAAI,CAACM,mBAAmB,CAACyH,SAAS,CAAC/H,OAAO,CAAC,EAAE;UAC9CoI,qBAAqB,CAAClC,IAAI,CAAC6B,SAAS,CAAC/H,OAAO,CAAC;QAC/C;MACF;IACF;IAEA;IACA,MAAMiI,KAAK,GAAGG,qBAAqB,CAACF,KAAK,CAAC,CAAC,EAAEL,cAAc,CAAC;IAE5D,IAAII,KAAK,CAACzG,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACwB,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGiF,KAAK,CAAC;MAC5D,IAAI,CAACV,WAAW,EAAE;MAClB1C,OAAO,CAACC,GAAG,CAAC,aAAamD,KAAK,CAACzG,MAAM,MAAM,CAAC;IAC9C;EACF;EAEA1D,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACL,gBAAgB,EAAE;IAE5B,MAAM0K,kBAAkB,GAAG,IAAI,CAAC5G,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,IAAI,EAAE;IACzE,MAAM+K,gBAAgB,GAAGL,kBAAkB,CAAClE,GAAG,CAACgC,CAAC,IAAIA,CAAC,CAACjG,OAAO,CAAC,CAACmE,MAAM,CAACG,EAAE,IAAIA,EAAE,KAAKmE,SAAS,CAAa;IAC1G,IAAI,CAACzF,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACmB,MAAM,CAACG,EAAE,IAAI,CAACkE,gBAAgB,CAAC7B,QAAQ,CAACrC,EAAE,CAAC,CAAC;IAC1F,IAAI,CAACiD,WAAW,EAAE;EACpB;EAEA/L,UAAUA,CAAA;IACR,IAAI,CAACwH,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACuE,WAAW,EAAE;EACpB;EAAUA,WAAWA,CAAA;IACnB,IAAI,CAACtC,wBAAwB,EAAE;IAC/BJ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC9B,gBAAgB,CAAC,CAAC,CAAI;IAC/E,IAAI,IAAI,CAACjG,gBAAgB,EAAE;MACzB;MACA,IAAI,CAAC4G,kBAAkB,GAAG,IAAI,CAACqB,sBAAsB,CAAC,IAAI,CAAChC,gBAAgB,CAAC;MAC5E,MAAM0F,gBAAgB,GAAG,CAAC,GAAG,IAAI/D,GAAG,CAAC,IAAI,CAAChB,kBAAkB,CAAC,CAAC,CAAC,CAAC;MAChEkB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE4D,gBAAgB,CAAC;MAClE,IAAI,CAAC5E,QAAQ,CAAC,CAAC,GAAG4E,gBAAgB,CAAC,CAAC;MACpC,IAAI,CAACjF,eAAe,CAACkF,IAAI,CAAC,CAAC,GAAGD,gBAAgB,CAAC,CAAC;IAClD,CAAC,MAAM;MACL;MACA7D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC9B,gBAAgB,CAAC;MAC3D,IAAI,CAACc,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACd,gBAAgB,CAAC,CAAC;MAEzC;MACA,MAAM4F,QAAQ,GAAG,IAAI,CAAC5F,gBAAgB,CAACmB,MAAM,CAACG,EAAE,IAAIA,EAAE,KAAKmE,SAAS,CAAC;MACrE5D,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8D,QAAQ,CAAC;MAC3C,IAAI,CAACpF,aAAa,CAACmF,IAAI,CAACC,QAAQ,CAAC;IACnC;IAEA,IAAI,CAAC5E,SAAS,EAAE;IAEhB;IACA,MAAM6E,aAAa,GAAG,IAAI,CAAC7F,gBAAgB,CAACiB,GAAG,CAACjE,OAAO,IAAG;MACxD,KAAK,MAAMnD,QAAQ,IAAI,IAAI,CAACuE,SAAS,EAAE;QACrC,MAAMoD,IAAI,GAAG,IAAI,CAACjD,YAAY,CAAC1E,QAAQ,CAAC,EAAEmJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjG,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAIwE,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACL,MAAM,CAACK,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnDK,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE+D,aAAa,CAAC;IACrD,IAAI,CAACtF,eAAe,CAACoF,IAAI,CAACE,aAAa,CAAC;EAC1C;EAAEC,cAAcA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC5M,QAAQ,EAAE;MAClB,IAAI,CAAC6M,UAAU,EAAE;MACjBlE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC1D,SAAS,CAAC;IACrD;EACF;EAEA2H,UAAUA,CAAA;IACR,IAAI,CAAC3F,aAAa,CAAC4F,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC5CC,OAAO,EAAE,EAAE;MACXC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA;EAAA;EACAxJ,mBAAmBA,CAACE,OAA2B;IAC/C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B,IAAI,IAAI,CAACjD,gBAAgB,EAAE;MACzB;MACA,MAAMgL,SAAS,GAAG,IAAI,CAACxD,qBAAqB,CAACvE,OAAO,CAAC;MACrD,IAAI+H,SAAS,EAAE;QACb,MAAMd,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACa,SAAS,CAACjL,SAAS,CAAC;QAC/E,OAAOmK,mBAAmB,CAACG,IAAI,CAAC9C,EAAE,IAAI,IAAI,CAACtB,gBAAgB,CAAC2D,QAAQ,CAACrC,EAAE,CAAC,CAAC;MAC3E;MACA,OAAO,KAAK;IACd,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAACtB,gBAAgB,CAAC2D,QAAQ,CAAC3G,OAAO,CAAC;IAChD;EACF;EAEAM,mBAAmBA,CAACN,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAACsD,gBAAgB,CAACqD,QAAQ,CAAC3G,OAAO,CAAC;EAChD;EAEAO,mBAAmBA,CAACP,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB,OAAO,IAAI,CAACM,mBAAmB,CAACN,OAAO,CAAC,IACrC,CAAC,IAAI,CAAC3B,aAAa,EAAE,IAAI,CAAC,IAAI,CAACyB,mBAAmB,CAACE,OAAO,CAAE;EACjE;EACA3B,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAACoD,aAAa,IAAI,IAAI,CAACuB,gBAAgB,CAACxB,MAAM,GAAG,IAAI,CAACC,aAAa;EACjF;EAAE8H,qBAAqBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC9L,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM0K,kBAAkB,GAAG,IAAI,CAAC5G,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,CAChE0G,MAAM,CAAC8B,CAAC,IAAI,CAACA,CAAC,CAACX,UAAU,IAAIW,CAAC,CAACjG,OAAO,KAAKyI,SAAS,CAAC;IACxD,OAAON,kBAAkB,CAAC3G,MAAM,GAAG,CAAC,IAClC2G,kBAAkB,CAACqB,KAAK,CAACzB,SAAS,IAAIA,SAAS,CAAC/H,OAAO,IAAI,IAAI,CAACgD,gBAAgB,CAAC2D,QAAQ,CAACoB,SAAS,CAAC/H,OAAO,CAAC,CAAC;EACjH;EAEA1B,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACb,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM0K,kBAAkB,GAAG,IAAI,CAAC5G,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,IAAI,EAAE;IACzE,OAAO0K,kBAAkB,CAACf,IAAI,CAACW,SAAS,IAAIA,SAAS,CAAC/H,OAAO,IAAI,IAAI,CAACgD,gBAAgB,CAAC2D,QAAQ,CAACoB,SAAS,CAAC/H,OAAO,CAAC,CAAC;EACrH;EAAEyJ,qBAAqBA,CAAA;IACrB,OAAO,IAAI,CAAC5F,kBAAkB;EAChC;EACAnG,gBAAgBA,CAACb,QAAgB;IAC/B,IAAI,IAAI,CAACE,gBAAgB,EAAE;MACzB;MACA,MAAMuJ,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC1E,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM6L,gBAAgB,GAAG,IAAI/D,GAAG,CAAC2B,UAAU,CAACrC,GAAG,CAACgC,CAAC,IAAIA,CAAC,CAACnJ,SAAS,CAAC,CAAC;MAClE,OAAO4L,gBAAgB,CAACgB,IAAI;IAC9B,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAACnI,YAAY,CAAC1E,QAAQ,CAAC,EAAE2E,MAAM,IAAI,CAAC;IACjD;EACF;EACAvF,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACc,gBAAgB,EAAE;MACzB;MACA,MAAM2L,gBAAgB,GAAG,IAAI,CAAC1D,sBAAsB,CAAC,IAAI,CAAChC,gBAAgB,CAAC;MAC3E,OAAO0F,gBAAgB,CAAClH,MAAM;IAChC,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAACwB,gBAAgB,CAACxB,MAAM;IACrC;EACF;EAEA;EACAmI,2BAA2BA,CAAC9M,QAAgB;IAC1C,OAAO,IAAI,CAACgH,kBAAkB,CAAChH,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACA+M,mBAAmBA,CAAC/M,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAACgH,kBAAkB,CAAChH,QAAQ,CAAC,IAAI,IAAI,CAACgH,kBAAkB,CAAChH,QAAQ,CAAC,CAAC2E,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQ2E,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC1I,gBAAgB,EAAE;MAC1B,IAAI,CAACoC,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAMyG,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMoM,QAAQ,GAAG,IAAIlF,GAAG,EAAU;IAElC2B,UAAU,CAACP,OAAO,CAACgC,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAACrL,KAAK,EAAE;QACnBmN,QAAQ,CAACtB,GAAG,CAACR,SAAS,CAACrL,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACmD,MAAM,GAAGiK,KAAK,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGC,QAAQ,CAACH,CAAC,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGF,QAAQ,CAACF,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOF,IAAI,GAAGG,IAAI;IACpB,CAAC,CAAC;IAEFzF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACrH,gBAAgB,EAAE,IAAI,CAACoC,MAAM,CAAC;EACjF;EAEA;EACAR,aAAaA,CAAC3C,KAAa;IACzBmI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEpI,KAAK,CAAC;IACrC,IAAI,CAACiB,aAAa,GAAG,IAAI,CAACA,aAAa,KAAKjB,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAACmJ,wBAAwB,EAAE;IAC/B,IAAI,CAAC1C,GAAG,CAACiD,aAAa,EAAE;EAC1B;EAEA;EACA3G,aAAaA,CAAC/C,KAAa;IACzB,IAAI,CAAC,IAAI,CAACe,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAM6I,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAO6I,UAAU,CAACnC,MAAM,CAAC8B,CAAC,IAAIA,CAAC,CAACvJ,KAAK,KAAKA,KAAK,CAAC,CAAC8E,MAAM;EACzD;EACA;EACA+I,iBAAiBA,CAACC,aAAqB;IACrC,IAAI,CAAC,IAAI,CAAC/M,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAM6I,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMsK,SAAS,GAAGzB,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnJ,SAAS,KAAK0N,aAAa,CAAC;IACrE,OAAOzC,SAAS,EAAErL,KAAK,IAAI,EAAE;EAC/B;EACA;EACA+N,gBAAgBA,CAACD,aAAqB;IACpC,KAAK,MAAM3N,QAAQ,IAAI,IAAI,CAACuE,SAAS,EAAE;MACrC,MAAMkF,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC1E,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMkL,SAAS,GAAGzB,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnJ,SAAS,KAAK0N,aAAa,CAAC;MACrE,IAAIzC,SAAS,EAAE;QACb,OAAO;UACLjL,SAAS,EAAEiL,SAAS,CAACjL,SAAS;UAC9BJ,KAAK,EAAEqL,SAAS,CAACrL,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEI,SAAS,EAAE0N,aAAa;MAAE9N,KAAK,EAAE;IAAE,CAAE;EAChD;EAEA;EACAgO,oBAAoBA,CAAC1K,OAAe;IAClC,KAAK,MAAMnD,QAAQ,IAAI,IAAI,CAACuE,SAAS,EAAE;MACrC,MAAMkF,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC1E,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMkL,SAAS,GAAGzB,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjG,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAI+H,SAAS,EAAE;QACb,OAAO;UACLjL,SAAS,EAAEiL,SAAS,CAACjL,SAAS;UAC9BJ,KAAK,EAAEqL,SAAS,CAACrL,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEI,SAAS,EAAE,MAAMkD,OAAO,EAAE;MAAEtD,KAAK,EAAE;IAAE,CAAE;EAClD;EAEA;EACAwC,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACV,UAAU,IAAI,CAAC,IAAI,CAACf,gBAAgB,IAAI,CAAC,IAAI,CAAC8D,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,EAAE;MAC3F,OAAO,KAAK;IACd;IAAE,MAAMkN,QAAQ,GAAG,IAAI,CAACpJ,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,CAAC0G,MAAM,CAAC8B,CAAC,IAAG;MACrE,MAAMO,UAAU,GAAG,IAAI,CAACzJ,gBAAgB,IAAI,CAAC,IAAI,CAACY,aAAa,IAAIsI,CAAC,CAACvJ,KAAK,KAAK,IAAI,CAACiB,aAAa;MACjG,MAAM8I,WAAW,GAAGR,CAAC,CAACnJ,SAAS,CAAC4J,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACnI,UAAU,CAACkI,WAAW,EAAE,CAAC;MACrF,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAOkE,QAAQ,CAACnJ,MAAM,KAAK,CAAC;EAC9B;EACA;EACAG,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAAClB,6BAA6B,EAAE,CAACe,MAAM;EACpD;EAEA;EACAoJ,oBAAoBA,CAAC7C,SAAwB;IAC3C,OAAOA,SAAS,CAAC/H,OAAO,GAAG+H,SAAS,CAAC/H,OAAO,CAAC6K,QAAQ,EAAE,GAAG,GAAG9C,SAAS,CAACjL,SAAS,IAAIiL,SAAS,CAACrL,KAAK,EAAE;EACvG;EACA;EACQ6H,qBAAqBA,CAACvE,OAAe;IAC3C,KAAK,MAAMnD,QAAQ,IAAI,IAAI,CAACuE,SAAS,EAAE;MACrC,MAAMkF,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC1E,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMkL,SAAS,GAAGzB,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjG,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAI+H,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb,CAAC,CAAE;EACK+C,qBAAqBA,CAAChO,SAAiB;IAC7C,MAAMiO,kBAAkB,GAAqD,EAAE;IAE/E;IACA,KAAK,MAAMlO,QAAQ,IAAI,IAAI,CAACuE,SAAS,EAAE;MACrC,MAAMkF,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC1E,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMmO,OAAO,GAAG1E,UAAU,CAACnC,MAAM,CAAC8B,CAAC,IAAIA,CAAC,CAACnJ,SAAS,KAAKA,SAAS,CAAC;MACjEkO,OAAO,CAACjF,OAAO,CAACgC,SAAS,IAAG;QAC1BgD,kBAAkB,CAAC7E,IAAI,CAAC;UAAErJ,QAAQ;UAAEkL;QAAS,CAAE,CAAC;MAClD,CAAC,CAAC;IACJ;IAEAlD,OAAO,CAACC,GAAG,CAAC,iBAAiBhI,SAAS,QAAQ,EAAEiO,kBAAkB,CAAC;IAEnE,IAAIA,kBAAkB,CAACvJ,MAAM,KAAK,CAAC,EAAE;MACnCqD,OAAO,CAACoG,IAAI,CAAC,kBAAkBnO,SAAS,SAAS,CAAC;MAClD,OAAO,IAAI;IACb;IAEA,IAAIiO,kBAAkB,CAACvJ,MAAM,GAAG,CAAC,EAAE;MACjCqD,OAAO,CAACoG,IAAI,CAAC,aAAanO,SAAS,IAAI,EAAEiO,kBAAkB,CAAC9G,GAAG,CAACiH,CAAC,IAAI,GAAGA,CAAC,CAACrO,QAAQ,IAAIqO,CAAC,CAACnD,SAAS,CAACrL,KAAK,EAAE,CAAC,CAAC;MAC3GmI,OAAO,CAACoG,IAAI,CAAC,cAAcF,kBAAkB,CAAC,CAAC,CAAC,CAAClO,QAAQ,IAAIkO,kBAAkB,CAAC,CAAC,CAAC,CAAChD,SAAS,CAACrL,KAAK,EAAE,CAAC;IACvG;IAEA,MAAMyO,UAAU,GAAGJ,kBAAkB,CAAC,CAAC,CAAC;IACxC,OAAOI,UAAU,CAACpD,SAAS,CAAC/H,OAAO,IAAI,IAAI;EAC7C;EAEA;EACQkH,yBAAyBA,CAACpK,SAAiB;IACjD,MAAM8L,QAAQ,GAAa,EAAE;IAE7B;IACA,KAAK,MAAM/L,QAAQ,IAAI,IAAI,CAACuE,SAAS,EAAE;MACrC,MAAMkF,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC1E,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMmO,OAAO,GAAG1E,UAAU,CAACnC,MAAM,CAAC8B,CAAC,IAAIA,CAAC,CAACnJ,SAAS,KAAKA,SAAS,CAAC;MACjEkO,OAAO,CAACjF,OAAO,CAACgC,SAAS,IAAG;QAC1B,IAAIA,SAAS,CAAC/H,OAAO,EAAE;UACrB4I,QAAQ,CAAC1C,IAAI,CAAC6B,SAAS,CAAC/H,OAAO,CAAC;QAClC;MACF,CAAC,CAAC;IACJ;IAEA,OAAO4I,QAAQ;EACjB;EAEA;EACQtB,iBAAiBA,CAACtH,OAAe;IACvC,OAAO,IAAI,CAACgD,gBAAgB,CAAC2D,QAAQ,CAAC3G,OAAO,CAAC;EAChD;EAEA;EACQ8G,iBAAiBA,CAAC9G,OAAe;IACvC,OAAO,IAAI,CAACsD,gBAAgB,CAACqD,QAAQ,CAAC3G,OAAO,CAAC;EAChD;EACA;EACAoL,wBAAwBA,CAACC,QAAgB;IACvC,KAAK,MAAMxO,QAAQ,IAAI,IAAI,CAACuE,SAAS,EAAE;MACrC,MAAMkF,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC1E,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMkL,SAAS,GAAGzB,UAAU,CAACN,IAAI,CAACC,CAAC,IAAI,IAAI,CAAC2E,oBAAoB,CAAC3E,CAAC,CAAC,KAAKoF,QAAQ,CAAC;MACjF,IAAItD,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb,CAAC,CAAE;EACKnD,sBAAsBA,CAAC0G,UAAoB;IACjD,MAAM1C,QAAQ,GAAa,EAAE;IAC7B,MAAMF,gBAAgB,GAAG,CAAC,GAAG,IAAI/D,GAAG,CAAC2G,UAAU,CAAC,CAAC,CAAC,CAAC;IAEnD,KAAK,MAAMxO,SAAS,IAAI4L,gBAAgB,EAAE;MACxC,MAAM6C,gBAAgB,GAAG,IAAI,CAACrE,yBAAyB,CAACpK,SAAS,CAAC;MAClE,IAAIyO,gBAAgB,CAAC/J,MAAM,GAAG,CAAC,EAAE;QAC/B,IAAI,IAAI,CAACzE,gBAAgB,EAAE;UACzB;UACA6L,QAAQ,CAAC1C,IAAI,CAACqF,gBAAgB,CAAC,CAAC,CAAC,CAAC;UAClC,IAAIA,gBAAgB,CAAC/J,MAAM,GAAG,CAAC,EAAE;YAC/BqD,OAAO,CAACC,GAAG,CAAC,SAAShI,SAAS,OAAOyO,gBAAgB,CAAC/J,MAAM,oCAAoC,EAAE+J,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACxH;QACF,CAAC,MAAM;UACL;UACA3C,QAAQ,CAAC1C,IAAI,CAAC,GAAGqF,gBAAgB,CAAC;UAClC,IAAIA,gBAAgB,CAAC/J,MAAM,GAAG,CAAC,EAAE;YAC/BqD,OAAO,CAACoG,IAAI,CAAC,SAASnO,SAAS,OAAOyO,gBAAgB,CAAC/J,MAAM,iBAAiB,EAAE+J,gBAAgB,CAAC;UACnG;QACF;MACF,CAAC,MAAM;QACL1G,OAAO,CAACoG,IAAI,CAAC,aAAanO,SAAS,eAAe,CAAC;MACrD;IACF;IAEA;IACA,OAAO,CAAC,GAAG,IAAI6H,GAAG,CAACiE,QAAQ,CAAC,CAAC;EAC/B;EACA;EACQ5D,sBAAsBA,CAAC4D,QAAkB;IAC/C,MAAM0C,UAAU,GAAa,EAAE;IAC/B,MAAME,cAAc,GAAG,CAAC,GAAG,IAAI7G,GAAG,CAACiE,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE/C,KAAK,MAAM5I,OAAO,IAAIwL,cAAc,EAAE;MACpC,MAAMC,aAAa,GAAG,IAAI,CAACf,oBAAoB,CAAC1K,OAAO,CAAC;MACxD,IAAIyL,aAAa,CAAC3O,SAAS,IAAI,CAAC2O,aAAa,CAAC3O,SAAS,CAAC4O,UAAU,CAAC,KAAK,CAAC,EAAE;QACzEJ,UAAU,CAACpF,IAAI,CAACuF,aAAa,CAAC3O,SAAS,CAAC;MAC1C,CAAC,MAAM;QACL+H,OAAO,CAACoG,IAAI,CAAC,gBAAgBjL,OAAO,UAAU,CAAC;MACjD;IACF;IAEA;IACA,OAAO,CAAC,GAAG,IAAI2E,GAAG,CAAC2G,UAAU,CAAC,CAAC;EACjC;EAEA;EACA7K,6BAA6BA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAAChD,gBAAgB,IAAI,CAAC,IAAI,CAAC8D,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,EAAE;MACvE,OAAO,EAAE;IACX;IAEA,MAAM6I,UAAU,GAAG,IAAI,CAAC/E,YAAY,CAAC,IAAI,CAAC9D,gBAAgB,CAAC,IAAI,EAAE;IAEjE,IAAI,CAAC,IAAI,CAACV,gBAAgB,EAAE;MAC1B;MACA,OAAOuJ,UAAU,CAACnC,MAAM,CAAC8B,CAAC,IAAG;QAC3B,MAAMO,UAAU,GAAG,CAAC,IAAI,CAAC7I,aAAa,IAAIsI,CAAC,CAACvJ,KAAK,KAAK,IAAI,CAACiB,aAAa;QACxE,MAAM8I,WAAW,GAAG,CAAC,IAAI,CAACjI,UAAU,IAAIyH,CAAC,CAACnJ,SAAS,CAAC4J,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACnI,UAAU,CAACkI,WAAW,EAAE,CAAC;QACzG,OAAOF,UAAU,IAAIC,WAAW;MAClC,CAAC,CAAC;IACJ;IAEA;IACA,MAAMiC,gBAAgB,GAAG,IAAI/D,GAAG,EAAU;IAC1C,MAAMgH,gBAAgB,GAAoB,EAAE;IAE5C,KAAK,MAAM5D,SAAS,IAAIzB,UAAU,EAAE;MAClC;MACA,MAAMG,WAAW,GAAG,CAAC,IAAI,CAACjI,UAAU,IAAIuJ,SAAS,CAACjL,SAAS,CAAC4J,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACnI,UAAU,CAACkI,WAAW,EAAE,CAAC;MAEjH,IAAID,WAAW,IAAI,CAACiC,gBAAgB,CAACJ,GAAG,CAACP,SAAS,CAACjL,SAAS,CAAC,EAAE;QAC7D4L,gBAAgB,CAACH,GAAG,CAACR,SAAS,CAACjL,SAAS,CAAC;QACzC6O,gBAAgB,CAACzF,IAAI,CAAC6B,SAAS,CAAC;MAClC;IACF;IACA,OAAO4D,gBAAgB;EACzB;EAEA;EACA,IAAI5P,WAAWA,CAAA;IACb,OAAO;MACLmB,QAAQ,EAAE,IAAI,CAACH,gBAAgB,GAAG,IAAI,GAAG,IAAI;MAC7CP,WAAW,EAAE,IAAI,CAACO,gBAAgB,GAAG,OAAO,GAAG,OAAO;MACtDf,cAAc,EAAE,IAAI,CAACe,gBAAgB,GAAG,OAAO,GAAG,OAAO;MACzD+F,UAAU,EAAE,IAAI,CAAC/F,gBAAgB,GAAG,MAAM,GAAG,MAAM;MACnDsE,aAAa,EAAE,IAAI,CAACtE,gBAAgB,GAAG,MAAM,GAAG,MAAM;MACtDR,aAAa,EAAE,IAAI,CAACQ,gBAAgB,GAAG,KAAK,GAAG,IAAI;MACnDkC,iBAAiB,EAAE,IAAI,CAAClC,gBAAgB,GAAG,SAAS,GAAG,SAAS;MAChEwB,SAAS,EAAE,IAAI,CAACxB,gBAAgB,GAAG,UAAU,GAAG,UAAU;MAC1D2D,WAAW,EAAE,IAAI,CAAC3D,gBAAgB,GAAG,aAAa,GAAG;KACtD;EACH;;;uCA9wBWkG,yBAAyB,EAAArI,EAAA,CAAAgR,iBAAA,CAAAhR,EAAA,CAAAiR,iBAAA,GAAAjR,EAAA,CAAAgR,iBAAA,CAAAE,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAzB9I,yBAAyB;MAAA+I,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;uCARzB,CACT;QACEE,OAAO,EAAE1R,iBAAiB;QAC1B2R,WAAW,EAAE5R,UAAU,CAAC,MAAMuI,yBAAyB,CAAC;QACxDsJ,KAAK,EAAE;OACR,CACF,GAAA3R,EAAA,CAAA4R,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC5BHvR,EAAA,CAAAC,cAAA,aAAyC;UAEvCD,EAAA,CAAA+B,UAAA,IAAAmQ,wCAAA,kBACoF;UAkBlFlS,EADF,CAAAC,cAAA,aAAgC,gBAGmL;UAD5KD,EAAA,CAAAK,UAAA,mBAAA8R,2DAAA;YAAAnS,EAAA,CAAAQ,aAAA,CAAA4R,GAAA;YAAA,OAAApS,EAAA,CAAAa,WAAA,CAAS2Q,GAAA,CAAAtD,cAAA,EAAgB;UAAA,EAAC;UAE7DlO,EAAA,CAAAC,cAAA,cAA4B;UAK1BD,EAJA,CAAA+B,UAAA,IAAAsQ,iDAAA,0BAAgC,IAAAC,iDAAA,0BAIC;UAInCtS,EAAA,CAAAI,YAAA,EAAO;UACPJ,EAAA,CAAAE,SAAA,iBAA4D;UAGlEF,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;UAkBNJ,EAfA,CAAA+B,UAAA,IAAAwQ,gDAAA,gCAAAvS,EAAA,CAAAwS,sBAAA,CAA6B,KAAAC,iDAAA,kCAAAzS,EAAA,CAAAwS,sBAAA,CAegC;;;UArDrDxS,EAAA,CAAAiB,SAAA,EAAiC;UAAjCjB,EAAA,CAAAe,UAAA,SAAAyQ,GAAA,CAAApJ,gBAAA,CAAAxB,MAAA,KAAiC;UAmBS5G,EAAA,CAAAiB,SAAA,GAAwC;UAAxCjB,EAAA,CAAA0S,WAAA,aAAAlB,GAAA,CAAAlQ,QAAA,IAAAkQ,GAAA,CAAArJ,SAAA,CAAwC;UACpFnI,EAAA,CAAAe,UAAA,aAAAyQ,GAAA,CAAAlQ,QAAA,IAAAkQ,GAAA,CAAArJ,SAAA,CAAkC;UAGjBnI,EAAA,CAAAiB,SAAA,GAAe;UAAfjB,EAAA,CAAAe,UAAA,SAAAyQ,GAAA,CAAArJ,SAAA,CAAe;UAIfnI,EAAA,CAAAiB,SAAA,EAAgB;UAAhBjB,EAAA,CAAAe,UAAA,UAAAyQ,GAAA,CAAArJ,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}