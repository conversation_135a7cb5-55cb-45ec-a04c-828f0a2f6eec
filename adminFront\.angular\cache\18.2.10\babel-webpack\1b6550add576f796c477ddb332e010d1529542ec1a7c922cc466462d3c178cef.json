{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner } from './model.js';\nexport var getECData = makeInner();\nexport var setCommonECData = function (seriesIndex, dataType, dataIdx, el) {\n  if (el) {\n    var ecData = getECData(el);\n    // Add data index and series index for indexing the data by element\n    // Useful in tooltip\n    ecData.dataIndex = dataIdx;\n    ecData.dataType = dataType;\n    ecData.seriesIndex = seriesIndex;\n    ecData.ssrType = 'chart';\n    // TODO: not store dataIndex on children.\n    if (el.type === 'group') {\n      el.traverse(function (child) {\n        var childECData = getECData(child);\n        childECData.seriesIndex = seriesIndex;\n        childECData.dataIndex = dataIdx;\n        childECData.dataType = dataType;\n        childECData.ssrType = 'chart';\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["makeInner", "getECData", "setCommonECData", "seriesIndex", "dataType", "dataIdx", "el", "ecData", "dataIndex", "ssrType", "type", "traverse", "child", "childECData"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/util/innerStore.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner } from './model.js';\nexport var getECData = makeInner();\nexport var setCommonECData = function (seriesIndex, dataType, dataIdx, el) {\n  if (el) {\n    var ecData = getECData(el);\n    // Add data index and series index for indexing the data by element\n    // Useful in tooltip\n    ecData.dataIndex = dataIdx;\n    ecData.dataType = dataType;\n    ecData.seriesIndex = seriesIndex;\n    ecData.ssrType = 'chart';\n    // TODO: not store dataIndex on children.\n    if (el.type === 'group') {\n      el.traverse(function (child) {\n        var childECData = getECData(child);\n        childECData.seriesIndex = seriesIndex;\n        childECData.dataIndex = dataIdx;\n        childECData.dataType = dataType;\n        childECData.ssrType = 'chart';\n      });\n    }\n  }\n};"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,YAAY;AACtC,OAAO,IAAIC,SAAS,GAAGD,SAAS,CAAC,CAAC;AAClC,OAAO,IAAIE,eAAe,GAAG,SAAAA,CAAUC,WAAW,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,EAAE,EAAE;EACzE,IAAIA,EAAE,EAAE;IACN,IAAIC,MAAM,GAAGN,SAAS,CAACK,EAAE,CAAC;IAC1B;IACA;IACAC,MAAM,CAACC,SAAS,GAAGH,OAAO;IAC1BE,MAAM,CAACH,QAAQ,GAAGA,QAAQ;IAC1BG,MAAM,CAACJ,WAAW,GAAGA,WAAW;IAChCI,MAAM,CAACE,OAAO,GAAG,OAAO;IACxB;IACA,IAAIH,EAAE,CAACI,IAAI,KAAK,OAAO,EAAE;MACvBJ,EAAE,CAACK,QAAQ,CAAC,UAAUC,KAAK,EAAE;QAC3B,IAAIC,WAAW,GAAGZ,SAAS,CAACW,KAAK,CAAC;QAClCC,WAAW,CAACV,WAAW,GAAGA,WAAW;QACrCU,WAAW,CAACL,SAAS,GAAGH,OAAO;QAC/BQ,WAAW,CAACT,QAAQ,GAAGA,QAAQ;QAC/BS,WAAW,CAACJ,OAAO,GAAG,OAAO;MAC/B,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}