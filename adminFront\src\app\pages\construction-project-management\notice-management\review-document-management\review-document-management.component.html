<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>
  <nb-card-body>
    <h1 class="font-bold text-[#818181]">可上傳要提供客戶標準圖說，文件內型分為標準圖及設備，並設定該檔案適用的戶別。</h1>
    <div class="d-flex flex-wrap">
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label col-3">建案</label>
          <nb-select placeholder="建案" [(ngModel)]="searchQuery.selectedBuildCase" class="col-9"
            (selectedChange)="onSelectionChangeBuildCase()">
            <nb-option *ngFor="let case of userBuildCaseOptions" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="cReviewType" class="label col-3">類型</label>
          <nb-select placeholder="類型" [(ngModel)]="searchQuery.selectedReviewType" class="col-9">
            <nb-option *ngFor="let case of reviewTypeOptionsQuery" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="cReviewName" class="label col-3"> 名稱 </label>
          <div class="col-9">
            <input type="text" id="cReviewName" nbInput class="w-full !max-w-[290px]"
              [(ngModel)]="searchQuery.CReviewName">
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="cStatus" class="label col-3">
            狀態
          </label>
          <nb-select placeholder="狀態" [(ngModel)]="searchQuery.seletedStatus" class="col-9">
            <nb-option *ngFor="let case of statusOptionsQuery" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="CExamineStatus" class="label col-3">
            簽回狀態
          </label>
          <nb-select placeholder="簽回狀態" [(ngModel)]="searchQuery.selectedExamineStatus" class="col-9">
            <nb-option *ngFor="let case of examineStatusOptionsQuery" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-end justify-end w-full">
          <button class="btn btn-secondary btn-sm" *ngIf="isRead" (click)="onSearch()">
            查詢 <i class="fas fa-search"></i>
          </button>
          <button class="btn btn-info mx-1 btn-sm" *ngIf="isCreate" (click)="openModel(dialog)">
            新增 <i class="fas fa-plus"></i>
          </button>
        </div>
      </div>
    </div>
    <div class="table-responsive mt-4">
      <table class="table table-striped border " style="background-color:#f3f3f3;">
        <thead>
          <tr style="background-color: #27ae60; color: white;">
            <th scope="col" class="col-1 ">類型</th>
            <th scope="col" class="col-1">名稱</th>
            <th scope="col" class="col-2">適用戶型</th>
            <th scope="col" class="col-1">狀態</th>
            <th scope="col" class="col-1">審核狀態</th>
            <th scope="col" class="col-2">審核日期</th>
            <th scope="col" class="col-1">動作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of reviewList ; let i = index">
            <td>
              {{item.CReviewType | getLabelInOptions : reviewTypeOptions}}
            </td>
            <td>
              {{ item.CReviewName}}
            </td>
            <td>
              {{ item.CHouse }}
            </td>
            <td>
              {{item.CStatus | getLabelInOptions : statusOptions}}
            </td>
            <td>
              {{item.CExamineStatus | getLabelInOptions : examineStatusOptionsQuery}}
            </td>
            <td>
              {{ item.CActionDate ? (item.CActionDate | dateFormat) : ''}}
            </td>
            <td class="w-32 px-0">
              <button *ngIf="isUpdate" (click)="openModel(dialog, item)"
                class="btn btn-outline-success btn-sm text-left m-[2px]">
                編輯
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">
    <ngb-pagination [(page)]="pageIndex" [pageSize]="pageSize" [collectionSize]="totalRecords"
      (pageChange)="pageChanged($event)" aria-label="Pagination">
    </ngb-pagination>
  </nb-card-footer>
</nb-card>


<ng-template #dialog let-dialog let-ref="dialogRef">
  <nb-card style="width:800px; max-height: 95vh">
    <nb-card-header> {{isNew? '新增審閱文件': '編輯審閱文件'}} </nb-card-header>
    <nb-card-body class="px-4">
      <div class="form-group d-flex align-items-center w-full">
        <label for="ReviewType" class="required-field label col-3">類型
        </label>
        <nb-select [disabled]="latestAction===1" placeholder="類型" [(ngModel)]="selectedReview.selectedReviewType"
          class="col-9 px-0">
          <nb-option *ngFor="let case of reviewTypeOptions" [value]="case">
            {{ case.label }}
          </nb-option>
        </nb-select>
      </div>
      <div class="form-group d-flex align-items-center">
        <!-- 使用共用文件上傳元件 -->
        <app-file-upload [config]="fileUploadConfigWithState" [currentFileName]="fileName"
          [currentFileUrl]="selectedReview.CFileUrl || null" (fileSelected)="onFileUploaded($event)"
          (fileCleared)="onFileCleared()" (nameAutoFilled)="onNameAutoFilled($event)">
        </app-file-upload>
      </div>
      <div class="form-group d-flex align-items-center">
        <div class="form-group d-flex align-items-center w-full">
          <label for="CExamineStatus" class="label col-3 required-field">
            狀態
          </label>
          <nb-select [disabled]="latestAction===1" placeholder="狀態" [(ngModel)]="selectedReview.seletedStatus"
            class="col-9 px-0">
            <nb-option *ngFor="let case of statusOptions" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>

      <div class="form-group d-flex align-items-center">
        <div class="form-group d-flex align-items-center w-full">
          <label for="cExamineNote" class="label col-3 required-field">
            審核說明
          </label>
          <textarea nbInput [(ngModel)]="selectedReview.CExamineNote" [rows]="4" [disabled]="latestAction===1"
            class="resize-none w-full col-9 !max-w-[320px]"></textarea>
        </div>
      </div>
      <div class="form-group d-flex mb-0">
        <label for="houseList2D" baseLabel class="required-field mr-3" style="min-width:75px">適用戶別</label>
      </div>

      <!-- 新的戶別選擇器 -->
      <div class="mt-1 mb-3">
        <app-household-binding [(ngModel)]="selectedHouseholds" [buildCaseId]="searchQuery.selectedBuildCase?.value"
          [buildingData]="buildingData" [maxSelections]="50" placeholder="請選擇適用戶別" [disabled]="latestAction === 1"
          [allowBatchSelect]="true" [useHouseNameMode]="false" (houseIdChange)="onHouseholdIdChange($event)"
          (selectionChange)="onHouseholdSelectionChange($event)">
        </app-household-binding>
      </div>


      <div class="d-flex justify-content-center">
        <button class="btn btn-danger btn-sm mr-4 min-w-[90px]" (click)="onClose(ref)">
          取消
        </button>
        <button class="btn btn-success btn-sm min-w-[90px]" (click)="onSaveReview(ref)" [disabled]="latestAction===1">
          {{isNew? '新增並送出審核': '送出審核'}}
        </button>
      </div>
      <div class="w-full" *ngIf="!isNew">
        <div class="form-group d-flex align-items-center">
          <label for="status" baseLabel class="mr-3" style="min-width:75px">審核歷程</label>
        </div>
        <table class="table table-bordered" style="background-color:#f3f3f3;">
          <thead>
            <tr>
              <th>時間</th>
              <th>使用者</th>
              <th>動作</th>
              <th>說明</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of selectedReview.tblExamineLogs">
              <td>{{row.CCreateDt | dateFormatHour}}</td>
              <td>{{row.CCreator}}</td>
              <td>{{getActionName(row.CAction)}}</td>
              <td>{{row.CExamineNote}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </nb-card-body>
  </nb-card>
</ng-template>