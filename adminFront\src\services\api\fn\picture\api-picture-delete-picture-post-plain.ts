/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BooleanResponseBase } from '../../models/boolean-response-base';

export interface ApiPictureDeletePicturePost$Plain$Params {
      body?: Array<number>
}

export function apiPictureDeletePicturePost$Plain(http: HttpClient, rootUrl: string, params?: ApiPictureDeletePicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BooleanResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiPictureDeletePicturePost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BooleanResponseBase>;
    })
  );
}

apiPictureDeletePicturePost$Plain.PATH = '/api/Picture/DeletePicture';
