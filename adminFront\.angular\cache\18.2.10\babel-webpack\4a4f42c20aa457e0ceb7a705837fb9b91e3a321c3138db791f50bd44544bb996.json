{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r3, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_47_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", floor_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", floor_r6, \"F \");\n  }\n}\nfunction SettingTimePeriodComponent_div_47_button_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_47_button_37_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearAllFilters());\n    });\n    i0.ɵɵelement(1, \"i\", 57);\n    i0.ɵɵtext(2, \"\\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"div\", 15)(3, \"nb-form-field\", 25);\n    i0.ɵɵelement(4, \"nb-icon\", 43);\n    i0.ɵɵelementStart(5, \"input\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_47_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.searchKeyword, $event) || (ctx_r4.filterOptions.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_47_Template_input_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 20)(7, \"nb-select\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.statusFilter, $event) || (ctx_r4.filterOptions.statusFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_selectedChange_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(8, \"nb-option\", 22);\n    i0.ɵɵtext(9, \"\\u5168\\u90E8\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-option\", 46);\n    i0.ɵɵtext(11, \"\\u9032\\u884C\\u4E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-option\", 47);\n    i0.ɵɵtext(13, \"\\u5F85\\u958B\\u653E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-option\", 48);\n    i0.ɵɵtext(15, \"\\u5DF2\\u904E\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-option\", 49);\n    i0.ɵɵtext(17, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-option\", 50);\n    i0.ɵɵtext(19, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 20)(21, \"nb-select\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.floorFilter, $event) || (ctx_r4.filterOptions.floorFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_selectedChange_21_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(22, \"nb-option\", 22);\n    i0.ɵɵtext(23, \"\\u5168\\u90E8\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SettingTimePeriodComponent_div_47_nb_option_24_Template, 2, 2, \"nb-option\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 52)(26, \"nb-select\", 53);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.pageSize, $event) || (ctx_r4.pageSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_selectedChange_26_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onPageSizeChange());\n    });\n    i0.ɵɵelementStart(27, \"nb-option\", 40);\n    i0.ɵɵtext(28, \"50\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-option\", 40);\n    i0.ɵɵtext(30, \"100\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"nb-option\", 40);\n    i0.ɵɵtext(32, \"200\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-option\", 40);\n    i0.ɵɵtext(34, \"500\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 31)(36, \"div\", 54);\n    i0.ɵɵtemplate(37, SettingTimePeriodComponent_div_47_button_37_Template, 3, 0, \"button\", 55);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.statusFilter);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.floorFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.availableFloors);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 50);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 200);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 500);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.hasActiveFilters());\n  }\n}\nfunction SettingTimePeriodComponent_div_48_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 97);\n    i0.ɵɵelement(1, \"i\", 98);\n    i0.ɵɵtext(2, \" \\u5DF2\\u9078 \");\n    i0.ɵɵelementStart(3, \"strong\", 99);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7B46 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedHouses.length);\n  }\n}\nfunction SettingTimePeriodComponent_div_48_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_32_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.openBatchSetting());\n    });\n    i0.ɵɵelement(2, \"i\", 102);\n    i0.ɵɵtext(3, \"\\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵelementStart(4, \"span\", 103);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_32_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearSelection());\n    });\n    i0.ɵɵelement(7, \"i\", 57);\n    i0.ɵɵtext(8, \"\\u6E05\\u9664\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedHouses.length);\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_small_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 120);\n    i0.ɵɵtext(1, \"\\u7121\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 121);\n    i0.ɵɵelement(1, \"i\", 122);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 1, house_r11.CChangeStartDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 123);\n    i0.ɵɵelement(1, \"i\", 71);\n    i0.ɵɵtext(2, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 121);\n    i0.ɵɵelement(1, \"i\", 124);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 1, house_r11.CChangeEndDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 123);\n    i0.ɵɵelement(1, \"i\", 71);\n    i0.ɵɵtext(2, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_tr_71_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const house_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r11.selected, $event) || (house_r11.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_tr_71_Template_nb_checkbox_ngModelChange_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 106)(5, \"span\", 107);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SettingTimePeriodComponent_div_48_tr_71_small_7_Template, 2, 0, \"small\", 108);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\")(9, \"span\", 109);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\")(12, \"span\", 110);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\")(15, \"div\", 111);\n    i0.ɵɵtemplate(16, SettingTimePeriodComponent_div_48_tr_71_span_16_Template, 4, 4, \"span\", 112)(17, SettingTimePeriodComponent_div_48_tr_71_span_17_Template, 3, 0, \"span\", 113);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"div\", 111);\n    i0.ɵɵtemplate(20, SettingTimePeriodComponent_div_48_tr_71_span_20_Template, 4, 4, \"span\", 112)(21, SettingTimePeriodComponent_div_48_tr_71_span_21_Template, 3, 0, \"span\", 113);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"td\")(23, \"div\", 114)(24, \"span\", 115);\n    i0.ɵɵelement(25, \"i\", 116);\n    i0.ɵɵelementStart(26, \"span\", 117);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"td\")(29, \"div\", 32)(30, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_tr_71_Template_button_click_30_listener() {\n      const house_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const dialog_r12 = i0.ɵɵreference(56);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r12, house_r11));\n    });\n    i0.ɵɵelement(31, \"i\", 119);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const house_r11 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"table-row-selected\", house_r11.selected)(\"table-row-disabled\", !house_r11.CHouseId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r11.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r11.CHouseId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(house_r11.CHouseHold);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r11.CHouseId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(house_r11.CBuildingName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", house_r11.CFloor, \"F\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", house_r11.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r11.CChangeStartDate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", house_r11.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r11.CChangeEndDate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r11));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r4.getStatusIcon(house_r11));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getStatusText(house_r11));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !house_r11.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_48_div_72_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 130)(1, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_li_21_Template_button_click_1_listener() {\n      const page_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(page_r15));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r15 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r15 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r15);\n  }\n}\nfunction SettingTimePeriodComponent_div_48_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 125)(1, \"div\", 60)(2, \"div\", 126)(3, \"span\", 81);\n    i0.ɵɵtext(4, \" \\u7B2C \");\n    i0.ɵɵelementStart(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" \\u9801\\uFF0C\\u5171 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" \\u9801 \");\n    i0.ɵɵelementStart(11, \"span\", 127);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"nav\", 128)(14, \"ul\", 129)(15, \"li\", 130)(16, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(1));\n    });\n    i0.ɵɵelement(17, \"i\", 132);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\", 130)(19, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage - 1));\n    });\n    i0.ɵɵelement(20, \"i\", 134);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, SettingTimePeriodComponent_div_48_div_72_li_21_Template, 3, 3, \"li\", 135);\n    i0.ɵɵelementStart(22, \"li\", 130)(23, \"button\", 136);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage + 1));\n    });\n    i0.ɵɵelement(24, \"i\", 137);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"li\", 130)(26, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.totalPages));\n    });\n    i0.ɵɵelement(27, \"i\", 139);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"div\", 140)(29, \"div\", 141)(30, \"input\", 142);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_div_72_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.jumpToPage, $event) || (ctx_r4.jumpToPage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function SettingTimePeriodComponent_div_48_div_72_Template_input_keyup_enter_30_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.jumpToPageAction());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.jumpToPageAction());\n    });\n    i0.ɵɵelement(32, \"i\", 144);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r4.currentPage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.totalPages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" (\\u986F\\u793A\\u7B2C \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" \\u7B46\\uFF0C \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46) \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.jumpToPage);\n    i0.ɵɵproperty(\"min\", 1)(\"max\", ctx_r4.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"div\", 60)(3, \"div\", 61)(4, \"span\", 62);\n    i0.ɵɵelement(5, \"i\", 63);\n    i0.ɵɵtext(6, \" \\u5171 \");\n    i0.ɵɵelementStart(7, \"strong\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, SettingTimePeriodComponent_div_48_span_10_Template, 6, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"div\", 66)(13, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"active\"));\n    });\n    i0.ɵɵelement(14, \"i\", 68);\n    i0.ɵɵtext(15, \"\\u9032\\u884C\\u4E2D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"pending\"));\n    });\n    i0.ɵɵelement(17, \"i\", 69);\n    i0.ɵɵtext(18, \"\\u5F85\\u958B\\u653E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"expired\"));\n    });\n    i0.ɵɵelement(20, \"i\", 70);\n    i0.ɵɵtext(21, \"\\u5DF2\\u904E\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"not-set\"));\n    });\n    i0.ɵɵelement(23, \"i\", 71);\n    i0.ɵɵtext(24, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(25, \"div\", 72)(26, \"div\", 60)(27, \"div\", 73)(28, \"div\", 74)(29, \"nb-checkbox\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_nb_checkbox_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_nb_checkbox_ngModelChange_29_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementStart(30, \"span\", 76);\n    i0.ɵɵtext(31, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, SettingTimePeriodComponent_div_48_div_32_Template, 9, 1, \"div\", 77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 78)(34, \"div\", 79)(35, \"div\", 80)(36, \"small\", 81);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(38, \"div\", 82)(39, \"table\", 83)(40, \"thead\", 84)(41, \"tr\")(42, \"th\", 85)(43, \"nb-checkbox\", 86);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_nb_checkbox_ngModelChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_nb_checkbox_ngModelChange_43_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"th\", 87);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_th_click_44_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CHouseHold\"));\n    });\n    i0.ɵɵelementStart(45, \"div\", 88);\n    i0.ɵɵtext(46, \" \\u6236\\u578B \");\n    i0.ɵɵelement(47, \"i\", 89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"th\", 90);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_th_click_48_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CBuildingName\"));\n    });\n    i0.ɵɵelementStart(49, \"div\", 88);\n    i0.ɵɵtext(50, \" \\u68DF\\u5225 \");\n    i0.ɵɵelement(51, \"i\", 89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"th\", 91);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_th_click_52_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CFloor\"));\n    });\n    i0.ɵɵelementStart(53, \"div\", 88);\n    i0.ɵɵtext(54, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelement(55, \"i\", 89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"th\", 92);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_th_click_56_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeStartDate\"));\n    });\n    i0.ɵɵelementStart(57, \"div\", 88);\n    i0.ɵɵtext(58, \" \\u958B\\u59CB\\u65E5\\u671F \");\n    i0.ɵɵelement(59, \"i\", 89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"th\", 92);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_th_click_60_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeEndDate\"));\n    });\n    i0.ɵɵelementStart(61, \"div\", 88);\n    i0.ɵɵtext(62, \" \\u7D50\\u675F\\u65E5\\u671F \");\n    i0.ɵɵelement(63, \"i\", 89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"th\", 93)(65, \"div\", 88);\n    i0.ɵɵtext(66, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"th\", 94)(68, \"div\", 88);\n    i0.ɵɵtext(69, \" \\u64CD\\u4F5C \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(70, \"tbody\");\n    i0.ɵɵtemplate(71, SettingTimePeriodComponent_div_48_tr_71_Template, 32, 20, \"tr\", 95);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(72, SettingTimePeriodComponent_div_48_div_72_Template, 33, 21, \"div\", 96);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.filteredHouses.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r4.filterOptions.statusFilter === \"active\")(\"btn-outline-primary\", ctx_r4.filterOptions.statusFilter !== \"active\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-warning\", ctx_r4.filterOptions.statusFilter === \"pending\")(\"btn-outline-warning\", ctx_r4.filterOptions.statusFilter !== \"pending\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-danger\", ctx_r4.filterOptions.statusFilter === \"expired\")(\"btn-outline-danger\", ctx_r4.filterOptions.statusFilter !== \"expired\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-secondary\", ctx_r4.filterOptions.statusFilter === \"not-set\")(\"btn-outline-secondary\", ctx_r4.filterOptions.statusFilter !== \"not-set\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" / \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46 \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.paginatedHouses)(\"ngForTrackBy\", ctx_r4.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 81);\n    i0.ɵɵelement(4, \"i\", 147);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\\u5F8C\\u67E5\\u8A62\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 81)(4, \"div\", 148)(5, \"span\", 149);\n    i0.ɵɵtext(6, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 150);\n    i0.ɵɵtext(8, \"\\u8CC7\\u6599\\u8F09\\u5165\\u4E2D\\uFF0C\\u8ACB\\u7A0D\\u5019...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \\u5DF2\\u9078\\u64C7 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225 \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r4.selectedBuildingForBatch.name, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 169);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", house_r17.CHouseHold, \" (\", house_r17.CBuildingName, \"-\", house_r17.CFloor, \"F) \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_28_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" ...\\u7B49 \", ctx_r4.selectedHouses.length - 10, \" \\u500B \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 165)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 166);\n    i0.ɵɵtemplate(4, SettingTimePeriodComponent_ng_template_51_div_28_span_4_Template, 2, 3, \"span\", 167)(5, SettingTimePeriodComponent_ng_template_51_div_28_span_5_Template, 2, 1, \"span\", 168);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5C07\\u5957\\u7528\\u5230\\u5DF2\\u9078\\u64C7\\u7684 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedHouses.slice(0, 10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 10);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      const house_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r22.selected, $event) || (house_r22.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r22 = ctx.$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r22.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r22.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r22.CHouseHold, \" (\", house_r22.CBuildingName, \") \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 174);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_nb_checkbox_1_Template, 2, 4, \"nb-checkbox\", 175);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", floor_r20.houses);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 172)(1, \"nb-checkbox\", 86);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const floor_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(floor_r20.selected, $event) || (floor_r20.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      const floor_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.onFloorSelectionChange(floor_r20));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_Template, 2, 1, \"div\", 173);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", floor_r20.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r20.floorNumber, \"F (\", floor_r20.houses.length, \" \\u6236) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", floor_r20.selected);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_29_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 150);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_Template, 4, 4, \"div\", 171);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedBuildingForBatch.floors);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"nb-checkbox\", 86);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_div_29_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.applyToAll, $event) || (ctx_r4.batchSettings.applyToAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_51_div_29_div_3_Template, 2, 1, \"div\", 170);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.applyToAll);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u90E8\\u6236\\u5225 (\", ctx_r4.flattenedHouses.length, \" \\u500B) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.batchSettings.applyToAll && ctx_r4.selectedBuildingForBatch);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225)\");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 151)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_51_span_3_Template, 2, 1, \"span\", 152)(4, SettingTimePeriodComponent_ng_template_51_span_4_Template, 2, 1, \"span\", 153);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"div\", 154)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n    i0.ɵɵelementStart(9, \"span\", 155);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 156)(12, \"nb-form-field\", 157);\n    i0.ɵɵelement(13, \"nb-icon\", 26);\n    i0.ɵɵelementStart(14, \"input\", 158);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.startDate, $event) || (ctx_r4.batchSettings.startDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 28, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 159);\n    i0.ɵɵtext(18, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\", 157);\n    i0.ɵɵelement(20, \"nb-icon\", 26);\n    i0.ɵɵelementStart(21, \"input\", 158);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.endDate, $event) || (ctx_r4.batchSettings.endDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 28, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 154)(25, \"label\");\n    i0.ɵɵtext(26, \"\\u9069\\u7528\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 160);\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_ng_template_51_div_28_Template, 6, 3, \"div\", 161)(29, SettingTimePeriodComponent_ng_template_51_div_29_Template, 4, 3, \"div\", 153);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-card-footer\", 162)(31, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_51_Template_button_click_31_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r23));\n    });\n    i0.ɵɵtext(32, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 164);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_51_Template_button_click_33_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onBatchSubmit(ref_r23));\n    });\n    i0.ɵɵtext(34, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(35, SettingTimePeriodComponent_ng_template_51_span_35_Template, 2, 1, \"span\", 153);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const batchStartDate_r24 = i0.ɵɵreference(16);\n    const batchEndDate_r25 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedBuildingForBatch && ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", batchStartDate_r24);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.startDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", batchEndDate_r25);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.endDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 176);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 177);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 176)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 178);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 179)(7, \"div\", 180)(8, \"label\", 181);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 155);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 182);\n    i0.ɵɵelement(13, \"nb-icon\", 26);\n    i0.ɵɵelementStart(14, \"input\", 183);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_55_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 28, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 184);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 26);\n    i0.ɵɵelementStart(21, \"input\", 185);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_55_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 28, 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 177)(25, \"button\", 186);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_55_Template_button_click_25_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r26).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r27));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 187);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_55_Template_button_click_27_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r26).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSubmit(ref_r27));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r28 = i0.ɵɵreference(16);\n    const changeEndDate_r29 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r4.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r28);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r29);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport let SettingTimePeriodComponent = /*#__PURE__*/(() => {\n  class SettingTimePeriodComponent extends BaseComponent {\n    constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._houseService = _houseService;\n      this._buildCaseService = _buildCaseService;\n      this.router = router;\n      this._eventService = _eventService;\n      this.tempBuildCaseId = -1;\n      this.buildCaseOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      // 新增的屬性\n      this.buildingGroups = [];\n      this.buildingOptions = [];\n      this.selectedBuilding = '';\n      this.availableFloors = [];\n      // 篩選和搜尋\n      this.filterOptions = {\n        searchKeyword: '',\n        statusFilter: '',\n        floorFilter: '',\n        buildingFilter: ''\n      };\n      // 批次設定\n      this.batchSettings = {\n        startDate: null,\n        endDate: null,\n        applyToAll: true,\n        selectedBuildings: [],\n        selectedFloors: [],\n        selectedHouses: []\n      };\n      this.selectedBuildingForBatch = null;\n      // 表格視圖相關屬性\n      this.flattenedHouses = [];\n      this.filteredHouses = [];\n      this.paginatedHouses = [];\n      this.selectedHouses = [];\n      this.selectAll = false;\n      this.loading = false;\n      // 分頁相關\n      this.currentPage = 1;\n      this.pageSize = 50;\n      this.totalPages = 1;\n      // 排序相關\n      this.sortField = '';\n      this.sortDirection = 'asc';\n      // 數學函數引用\n      this.Math = Math;\n      // 新增的UI控制屬性\n      this.jumpToPage = 1;\n      this.selectedHouseChangeDate = {\n        CChangeStartDate: '',\n        CChangeEndDate: '',\n        CFloor: undefined,\n        CHouseHold: '',\n        CHouseId: undefined\n      };\n      this._eventService.receive().pipe(tap(res => {\n        if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n          this.tempBuildCaseId = res.payload;\n        }\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        CBuildingNameSelected: this.buildCaseOptions[0],\n        CChangeStartDate: undefined,\n        CChangeEndDate: undefined\n      };\n      this.getUserBuildCase();\n    }\n    // 新增的UI控制方法\n    hasActiveFilters() {\n      return !!(this.filterOptions.searchKeyword || this.filterOptions.statusFilter || this.filterOptions.floorFilter);\n    }\n    getActiveFiltersCount() {\n      let count = 0;\n      if (this.filterOptions.searchKeyword) count++;\n      if (this.filterOptions.statusFilter) count++;\n      if (this.filterOptions.floorFilter) count++;\n      return count;\n    }\n    resetFilters() {\n      this.searchQuery = {\n        CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n        CBuildingNameSelected: this.buildCaseOptions[0],\n        CChangeStartDate: undefined,\n        CChangeEndDate: undefined\n      };\n      this.selectedBuilding = '';\n      this.clearAllFilters();\n    }\n    clearAllFilters() {\n      this.filterOptions = {\n        searchKeyword: '',\n        statusFilter: '',\n        floorFilter: '',\n        buildingFilter: ''\n      };\n      this.onSearch();\n    }\n    setQuickFilter(status) {\n      if (this.filterOptions.statusFilter === status) {\n        this.filterOptions.statusFilter = '';\n      } else {\n        this.filterOptions.statusFilter = status;\n      }\n      this.onSearch();\n    }\n    clearSelection() {\n      this.selectedHouses = [];\n      this.selectAll = false;\n      this.flattenedHouses.forEach(house => house.selected = false);\n    }\n    getStatusIcon(house) {\n      const status = this.getHouseStatus(house);\n      switch (status) {\n        case 'active':\n          return 'fas fa-play-circle';\n        case 'pending':\n          return 'fas fa-clock';\n        case 'expired':\n          return 'fas fa-times-circle';\n        case 'not-set':\n          return 'fas fa-exclamation-triangle';\n        case 'disabled':\n          return 'fas fa-ban';\n        default:\n          return 'fas fa-exclamation-triangle';\n      }\n    }\n    jumpToPageAction() {\n      if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\n        this.goToPage(this.jumpToPage);\n      }\n    }\n    openModel(ref, item) {\n      if (item.CHouseId) {\n        this.selectedHouseChangeDate = {\n          ...item,\n          CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n          CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n        };\n        this.dialogService.open(ref);\n      }\n    }\n    formatDate(CChangeDate) {\n      if (CChangeDate) {\n        return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n      }\n      return '';\n    }\n    onSubmit(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      const param = {\n        CHouseId: this.selectedHouseChangeDate.CHouseId,\n        CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n        CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n      };\n      this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n        body: [param]\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getHouseChangeDate();\n          ref.close();\n        }\n      });\n    }\n    getUserBuildCase() {\n      this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n        body: {}\n      }).pipe(tap(res => {\n        const entries = res.Entries ?? []; // entries not undefined and not null\n        if (entries.length && res.StatusCode === 0) {\n          this.userBuildCaseOptions = entries.map(entry => ({\n            CBuildCaseName: entry.CBuildCaseName,\n            cID: entry.cID\n          }));\n          if (this.tempBuildCaseId != -1) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n          }\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n          if (selectedCID) {\n            this.getHouseChangeDate();\n          }\n        }\n      })).subscribe();\n    }\n    convertHouseholdArrayOptimized(arr) {\n      const floorDict = {}; // Initialize dictionary to group elements by CFloor\n      arr.forEach(household => {\n        household.CHouses.forEach(house => {\n          const floor = house.CFloor;\n          if (!floorDict[floor]) {\n            // If CFloor is not in the dictionary, initialize an empty list\n            floorDict[floor] = [];\n          }\n          floorDict[floor].push({\n            CHouseHold: household.CHouseHold,\n            CBuildingName: house.CBuildingName || '未分類',\n            CHouseId: house.CHouseId,\n            CFloor: house.CFloor,\n            CChangeStartDate: house.CChangeStartDate,\n            CChangeEndDate: house.CChangeEndDate\n          });\n        });\n      });\n      // Arrange floors in ascending order\n      this.floors.sort((a, b) => b - a);\n      const result = this.floors.map(floor => {\n        return this.households.map(household => {\n          const house = floorDict[floor].find(h => h.CHouseHold === household);\n          return house || {\n            CHouseHold: household,\n            CBuildingName: '未分類',\n            CHouseId: null,\n            CFloor: floor,\n            CChangeStartDate: null,\n            CChangeEndDate: null\n          };\n        });\n      });\n      return result;\n    }\n    getFloorsAndHouseholds(arr) {\n      const floorsSet = new Set();\n      const householdsSet = new Set();\n      arr.forEach(household => {\n        householdsSet.add(household.CHouseHold);\n        household.CHouses.forEach(house => {\n          floorsSet.add(house.CFloor);\n        });\n      });\n      this.floors = Array.from(floorsSet);\n      this.households = Array.from(householdsSet);\n      return {\n        floors: Array.from(floorsSet),\n        households: Array.from(householdsSet)\n      };\n    }\n    validationDate() {\n      if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n        const startDate = new Date(this.searchQuery.CChangeStartDate);\n        const endDate = new Date(this.searchQuery.CChangeEndDate);\n        if (startDate && endDate && startDate > endDate) {\n          this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n        }\n      }\n    }\n    // 新增：建案變更處理\n    onBuildCaseChange() {\n      // 重置所有相關狀態\n      this.resetAllStates();\n      // 執行查詢\n      this.getHouseChangeDate();\n    }\n    // 新增：重置所有狀態\n    resetAllStates() {\n      // 重置數據\n      this.houseChangeDates = [];\n      this.convertedHouseArray = [];\n      this.buildingGroups = [];\n      this.flattenedHouses = [];\n      this.filteredHouses = [];\n      this.paginatedHouses = [];\n      this.selectedHouses = [];\n      // 重置篩選條件\n      this.filterOptions = {\n        searchKeyword: '',\n        statusFilter: '',\n        floorFilter: '',\n        buildingFilter: ''\n      };\n      // 重置選擇狀態\n      this.selectAll = false;\n      this.selectedBuilding = '';\n      // 重置分頁\n      this.currentPage = 1;\n      this.totalPages = 1;\n      // 重置可用選項\n      this.buildingOptions = [];\n      this.availableFloors = [];\n      // 重置排序\n      this.sortField = '';\n      this.sortDirection = 'asc';\n    }\n    getHouseChangeDate() {\n      // 如果沒有選擇建案，直接返回\n      if (!this.searchQuery.CBuildCaseSelected?.cID) {\n        this.loading = false;\n        return;\n      }\n      this.loading = true;\n      this.validationDate();\n      this._houseService.apiHouseGetHouseChangeDatePost$Json({\n        body: {\n          CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n          CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n          CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n        }\n      }).subscribe(res => {\n        this.loading = false;\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseChangeDates = res.Entries ? res.Entries : [];\n          if (res.Entries) {\n            this.houseChangeDates = [...res.Entries];\n            this.getFloorsAndHouseholds(res.Entries);\n            this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n            // 新增：建立棟別分組資料\n            this.buildBuildingGroups(res.Entries);\n            // 新增：建立扁平化資料\n            this.buildFlattenedHouses(res.Entries);\n          }\n        }\n      });\n    }\n    // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n    buildBuildingGroups(data) {\n      const buildingMap = new Map();\n      data.forEach(household => {\n        const houseType = household.CHouseHold || ''; // 戶型\n        household.CHouses?.forEach(house => {\n          const buildingName = house.CBuildingName || '未分類'; // 棟別\n          const floor = house.CFloor || 0;\n          if (!buildingMap.has(buildingName)) {\n            buildingMap.set(buildingName, new Map());\n          }\n          const floorMap = buildingMap.get(buildingName);\n          if (!floorMap.has(floor)) {\n            floorMap.set(floor, []);\n          }\n          floorMap.get(floor).push({\n            CHouseHold: houseType,\n            // 戶型\n            CBuildingName: buildingName,\n            // 棟別\n            CHouseId: house.CHouseId || 0,\n            CFloor: floor,\n            CChangeStartDate: house.CChangeStartDate || '',\n            CChangeEndDate: house.CChangeEndDate || '',\n            selected: false\n          });\n        });\n      });\n      // 轉換為BuildingGroup格式\n      this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n        const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n        .map(([floorNumber, houses]) => ({\n          floorNumber,\n          houses: houses.sort((a, b) => {\n            // 排序邏輯：先按戶型排序，再按樓層排序\n            if (a.CHouseHold !== b.CHouseHold) {\n              return a.CHouseHold.localeCompare(b.CHouseHold);\n            }\n            return a.CFloor - b.CFloor;\n          }),\n          selected: false\n        }));\n        return {\n          name: buildingName,\n          floors,\n          selected: false\n        };\n      }).sort((a, b) => a.name.localeCompare(b.name));\n      // 更新棟別選項和可用樓層\n      this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n      this.updateAvailableFloors();\n    }\n    // 新增：更新可用樓層\n    updateAvailableFloors() {\n      const floorsSet = new Set();\n      this.buildingGroups.forEach(building => {\n        if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n          building.floors.forEach(floor => {\n            floorsSet.add(floor.floorNumber);\n          });\n        }\n      });\n      this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n    }\n    // 修改：棟別選擇變更處理\n    onBuildingChange() {\n      // 重置選擇狀態\n      this.selectedHouses.forEach(house => house.selected = false);\n      this.selectedHouses = [];\n      this.selectAll = false;\n      // 重置分頁到第一頁\n      this.currentPage = 1;\n      // 重置樓層篩選\n      this.filterOptions.floorFilter = '';\n      // 更新可用樓層\n      this.updateAvailableFloors();\n      // 設定棟別篩選並執行搜尋\n      this.filterOptions.buildingFilter = this.selectedBuilding;\n      this.onSearch();\n    }\n    // 新增：取得過濾後的棟別資料\n    getFilteredBuildings() {\n      return this.buildingGroups.filter(building => {\n        // 棟別篩選\n        if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n          return false;\n        }\n        // 關鍵字搜尋 (搜尋戶型)\n        if (this.filterOptions.searchKeyword) {\n          const keyword = this.filterOptions.searchKeyword.toLowerCase();\n          const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n          if (!hasMatchingHouse) {\n            return false;\n          }\n        }\n        // 狀態篩選\n        if (this.filterOptions.statusFilter) {\n          const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n          if (!hasMatchingStatus) {\n            return false;\n          }\n        }\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n          if (!hasMatchingFloor) {\n            return false;\n          }\n        }\n        return true;\n      }).map(building => {\n        // 對每個棟別，也要篩選其樓層和戶別\n        const filteredBuilding = {\n          ...building\n        };\n        filteredBuilding.floors = building.floors.filter(floor => {\n          // 樓層篩選\n          if (this.filterOptions.floorFilter) {\n            const floorNumber = parseInt(this.filterOptions.floorFilter);\n            if (floor.floorNumber !== floorNumber) {\n              return false;\n            }\n          }\n          // 檢查該樓層是否有符合條件的戶別\n          const hasValidHouses = floor.houses.some(house => {\n            // 關鍵字篩選 (搜尋戶型或棟別)\n            if (this.filterOptions.searchKeyword) {\n              const keyword = this.filterOptions.searchKeyword.toLowerCase();\n              if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n                return false;\n              }\n            }\n            // 狀態篩選\n            if (this.filterOptions.statusFilter) {\n              if (!this.matchesStatusFilter(house)) {\n                return false;\n              }\n            }\n            return true;\n          });\n          return hasValidHouses;\n        }).map(floor => {\n          // 篩選戶別\n          const filteredFloor = {\n            ...floor\n          };\n          filteredFloor.houses = floor.houses.filter(house => {\n            // 關鍵字篩選 (搜尋戶型或棟別)\n            if (this.filterOptions.searchKeyword) {\n              const keyword = this.filterOptions.searchKeyword.toLowerCase();\n              if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n                return false;\n              }\n            }\n            // 狀態篩選\n            if (this.filterOptions.statusFilter) {\n              if (!this.matchesStatusFilter(house)) {\n                return false;\n              }\n            }\n            return true;\n          });\n          return filteredFloor;\n        });\n        return filteredBuilding;\n      });\n    }\n    // 修復：檢查戶別是否符合狀態篩選\n    matchesStatusFilter(house) {\n      const status = this.getHouseStatus(house);\n      switch (this.filterOptions.statusFilter) {\n        case 'active':\n          return status === 'active';\n        case 'pending':\n          return status === 'pending';\n        case 'expired':\n          return status === 'expired';\n        case 'not-set':\n          return status === 'not-set';\n        case 'disabled':\n          return status === 'disabled';\n        default:\n          return true;\n        // 全部狀態\n      }\n    }\n    // 修復：取得戶別狀態\n    getHouseStatus(house) {\n      if (!house.CHouseId) {\n        return 'disabled';\n      }\n      // 檢查是否有設定開放時段\n      if (!house.CChangeStartDate || !house.CChangeEndDate || house.CChangeStartDate === '' || house.CChangeEndDate === '') {\n        return 'not-set';\n      }\n      try {\n        // 處理日期字串，支援多種格式\n        const now = new Date();\n        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n        // 解析開始日期\n        let startDate;\n        if (house.CChangeStartDate.includes('T')) {\n          startDate = new Date(house.CChangeStartDate);\n        } else {\n          startDate = new Date(house.CChangeStartDate + 'T00:00:00');\n        }\n        // 解析結束日期\n        let endDate;\n        if (house.CChangeEndDate.includes('T')) {\n          endDate = new Date(house.CChangeEndDate);\n        } else {\n          endDate = new Date(house.CChangeEndDate + 'T23:59:59');\n        }\n        // 檢查日期有效性\n        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\n          console.warn('Invalid date format:', {\n            start: house.CChangeStartDate,\n            end: house.CChangeEndDate,\n            houseId: house.CHouseId\n          });\n          return 'not-set';\n        }\n        // 轉換為日期比較（不含時間）\n        const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n        const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n        // 判斷狀態\n        if (today < startDateOnly) {\n          return 'pending'; // 待開放\n        } else if (today >= startDateOnly && today <= endDateOnly) {\n          return 'active'; // 進行中\n        } else {\n          return 'expired'; // 已過期\n        }\n      } catch (error) {\n        console.error('Error parsing dates:', error, house);\n        return 'not-set';\n      }\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n      this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n      this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n    }\n    onNavigateWithId() {\n      this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n    }\n    // 修改：開啟批次設定對話框\n    openBatchSetting(building) {\n      this.selectedBuildingForBatch = building || null;\n      // 如果有選中的房屋，使用選中的房屋；否則使用全部\n      const hasSelectedHouses = this.selectedHouses.length > 0;\n      this.batchSettings = {\n        startDate: null,\n        endDate: null,\n        applyToAll: !hasSelectedHouses && !building,\n        selectedBuildings: building ? [building.name] : [],\n        selectedFloors: [],\n        selectedHouses: []\n      };\n      // 重置選擇狀態\n      if (building) {\n        building.floors.forEach(floor => {\n          floor.selected = false;\n          floor.houses.forEach(house => house.selected = false);\n        });\n      }\n      // 開啟對話框\n      this.dialogService.open(this.batchSettingDialog);\n    }\n    // 新增：樓層選擇變更處理\n    onFloorSelectionChange(floor) {\n      if (floor.selected) {\n        floor.houses.forEach(house => {\n          if (house.CHouseId) {\n            house.selected = true;\n          }\n        });\n      } else {\n        floor.houses.forEach(house => house.selected = false);\n      }\n    }\n    // 修改：批次提交\n    onBatchSubmit(ref) {\n      // 驗證批次設定\n      this.valid.clear();\n      this.valid.required('[開始日期]', this.batchSettings.startDate);\n      this.valid.required('[結束日期]', this.batchSettings.endDate);\n      this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      // 收集要更新的房屋\n      const housesToUpdate = [];\n      if (this.batchSettings.applyToAll) {\n        // 全部戶別\n        this.flattenedHouses.forEach(house => {\n          if (house.CHouseId) {\n            housesToUpdate.push({\n              CHouseId: house.CHouseId,\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n            });\n          }\n        });\n      } else {\n        // 使用已選擇的戶別\n        if (this.selectedHouses.length > 0) {\n          this.selectedHouses.forEach(house => {\n            if (house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        } else if (this.selectedBuildingForBatch) {\n          // 如果沒有選擇的戶別，使用舊的邏輯\n          this.selectedBuildingForBatch.floors.forEach(floor => {\n            floor.houses.forEach(house => {\n              if (house.selected && house.CHouseId) {\n                housesToUpdate.push({\n                  CHouseId: house.CHouseId,\n                  CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                  CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n                });\n              }\n            });\n          });\n        }\n      }\n      if (housesToUpdate.length === 0) {\n        this.message.showErrorMSGs(['請選擇要設定的戶別']);\n        return;\n      }\n      // 調用API進行批次更新\n      this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n        body: housesToUpdate\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n          // 清除選擇狀態\n          this.selectedHouses.forEach(house => house.selected = false);\n          this.selectedHouses = [];\n          this.selectAll = false;\n          this.getHouseChangeDate();\n          ref.close();\n        }\n      });\n    }\n    // 新增：取得狀態樣式類別\n    getStatusClass(house) {\n      const status = this.getHouseStatus(house);\n      return `status-${status}`;\n    }\n    // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n    openHouseDialog(house) {\n      if (house.CHouseId) {\n        // 使用現有的openModel方法\n        this.openModel(this.dialog, house);\n      }\n    }\n    // 新增：建立扁平化房屋資料\n    buildFlattenedHouses(data) {\n      this.flattenedHouses = [];\n      data.forEach(household => {\n        const houseType = household.CHouseHold || '';\n        household.CHouses?.forEach(house => {\n          this.flattenedHouses.push({\n            CHouseHold: houseType,\n            CBuildingName: house.CBuildingName || '未分類',\n            CHouseId: house.CHouseId || 0,\n            CFloor: house.CFloor || 0,\n            CChangeStartDate: house.CChangeStartDate || '',\n            CChangeEndDate: house.CChangeEndDate || '',\n            selected: false\n          });\n        });\n      });\n      // 初始化篩選和分頁\n      this.onSearch();\n      // 調試：輸出狀態統計\n      this.debugStatusCounts();\n    }\n    // 調試：輸出狀態統計\n    debugStatusCounts() {\n      const statusCounts = {\n        active: 0,\n        pending: 0,\n        expired: 0,\n        'not-set': 0,\n        disabled: 0\n      };\n      this.flattenedHouses.forEach(house => {\n        const status = this.getHouseStatus(house);\n        if (statusCounts.hasOwnProperty(status)) {\n          statusCounts[status]++;\n        }\n      });\n      console.log('狀態統計:', statusCounts);\n      console.log('今天日期:', new Date().toISOString().split('T')[0]);\n    }\n    // 修改：搜尋和篩選\n    onSearch() {\n      // 記錄篩選前的已選擇項目\n      const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\n      this.filteredHouses = this.flattenedHouses.filter(house => {\n        // 關鍵字搜尋\n        if (this.filterOptions.searchKeyword) {\n          const keyword = this.filterOptions.searchKeyword.toLowerCase();\n          if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n            return false;\n          }\n        }\n        // 棟別篩選\n        if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n          return false;\n        }\n        // 狀態篩選\n        if (this.filterOptions.statusFilter) {\n          if (!this.matchesStatusFilter(house)) {\n            return false;\n          }\n        }\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (house.CFloor !== floorNumber) {\n            return false;\n          }\n        }\n        return true;\n      });\n      // 更新選擇狀態：只保留仍在篩選結果中的已選項目\n      this.selectedHouses = this.filteredHouses.filter(house => previouslySelectedIds.includes(house.CHouseId));\n      // 更新扁平化資料中的選擇狀態\n      this.flattenedHouses.forEach(house => {\n        house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\n      });\n      // 更新全選狀態\n      this.updateSelectAllState();\n      // 重新計算分頁\n      this.currentPage = 1;\n      this.updatePagination();\n    }\n    // 新增：更新全選狀態\n    updateSelectAllState() {\n      if (this.paginatedHouses.length === 0) {\n        this.selectAll = false;\n      } else {\n        this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n      }\n    }\n    // 修改：更新分頁\n    updatePagination() {\n      this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n      const startIndex = (this.currentPage - 1) * this.pageSize;\n      const endIndex = startIndex + this.pageSize;\n      this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n      // 更新全選狀態\n      this.updateSelectAllState();\n    }\n    // 新增：頁面大小變更\n    onPageSizeChange() {\n      this.currentPage = 1;\n      this.updatePagination();\n    }\n    // 新增：跳轉頁面\n    goToPage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n        this.updatePagination();\n      }\n    }\n    // 新增：取得可見頁碼\n    getVisiblePages() {\n      const pages = [];\n      const maxVisible = 5;\n      let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n      let end = Math.min(this.totalPages, start + maxVisible - 1);\n      if (end - start + 1 < maxVisible) {\n        start = Math.max(1, end - maxVisible + 1);\n      }\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n    // 新增：全選/取消全選\n    onSelectAllChange() {\n      this.paginatedHouses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = this.selectAll;\n        }\n      });\n      this.updateSelectedHouses();\n    }\n    // 新增：單一選擇變更\n    onHouseSelectionChange() {\n      this.updateSelectedHouses();\n      this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n    }\n    // 新增：更新已選擇房屋列表\n    updateSelectedHouses() {\n      this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n    }\n    // 新增：排序\n    sort(field) {\n      if (this.sortField === field) {\n        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n      } else {\n        this.sortField = field;\n        this.sortDirection = 'asc';\n      }\n      this.filteredHouses.sort((a, b) => {\n        let aValue = a[field];\n        let bValue = b[field];\n        // 處理日期排序\n        if (field.includes('Date')) {\n          aValue = aValue ? new Date(aValue).getTime() : 0;\n          bValue = bValue ? new Date(bValue).getTime() : 0;\n        }\n        // 處理數字排序\n        if (field === 'CFloor') {\n          aValue = Number(aValue) || 0;\n          bValue = Number(bValue) || 0;\n        }\n        // 處理字串排序\n        if (typeof aValue === 'string') {\n          aValue = aValue.toLowerCase();\n          bValue = bValue.toLowerCase();\n        }\n        if (aValue < bValue) {\n          return this.sortDirection === 'asc' ? -1 : 1;\n        }\n        if (aValue > bValue) {\n          return this.sortDirection === 'asc' ? 1 : -1;\n        }\n        return 0;\n      });\n      this.updatePagination();\n    }\n    // 新增：TrackBy函數\n    trackByHouseId(_index, house) {\n      return house.CHouseId;\n    }\n    // 新增：取得狀態文字\n    getStatusText(house) {\n      const status = this.getHouseStatus(house);\n      switch (status) {\n        case 'active':\n          return '進行中';\n        case 'pending':\n          return '待開放';\n        case 'expired':\n          return '已過期';\n        case 'not-set':\n          return '未設定';\n        case 'disabled':\n          return '已停用';\n        default:\n          return '未知';\n      }\n    }\n    static {\n      this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SettingTimePeriodComponent,\n        selectors: [[\"ngx-setting-time-period\"]],\n        viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 57,\n        vars: 14,\n        consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"batchSettingDialog\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"batchStartDate\", \"\"], [\"batchEndDate\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"page-header-optimized\"], [1, \"page-description-section\"], [1, \"page-description\", \"text-muted\", \"mb-0\"], [1, \"compact-filters\"], [1, \"row\", \"g-3\", \"align-items-end\"], [1, \"col-lg-3\", \"col-md-4\"], [1, \"form-label\", \"small\", \"fw-medium\"], [1, \"text-danger\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-2\", \"col-md-3\"], [\"placeholder\", \"\\u5168\\u90E8\\u68DF\\u5225\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"\"], [1, \"col-lg-4\", \"col-md-5\"], [1, \"date-range-group\"], [\"size\", \"small\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u958B\\u59CB\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [1, \"date-separator\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u7D50\\u675F\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-lg-3\", \"col-md-12\"], [1, \"action-buttons\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-search\", \"me-1\"], [\"title\", \"\\u91CD\\u7F6E\\u7BE9\\u9078\\u689D\\u4EF6\", 1, \"btn\", \"btn-outline-secondary\", \"ms-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-undo\"], [\"class\", \"advanced-filters-panel\", 4, \"ngIf\"], [\"class\", \"table-view-enhanced mt-4\", 4, \"ngIf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [3, \"value\"], [1, \"advanced-filters-panel\"], [1, \"row\", \"g-3\", \"align-items-center\"], [\"nbPrefix\", \"\", \"icon\", \"search-outline\"], [\"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u578B\\u3001\\u68DF\\u5225...\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u72C0\\u614B\\u7BE9\\u9078\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"active\"], [\"value\", \"pending\"], [\"value\", \"expired\"], [\"value\", \"not-set\"], [\"value\", \"disabled\"], [\"placeholder\", \"\\u6A13\\u5C64\\u7BE9\\u9078\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-lg-2\", \"col-md-2\"], [\"placeholder\", \"\\u986F\\u793A\\u7B46\\u6578\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"filter-actions\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"table-view-enhanced\", \"mt-4\"], [1, \"data-summary-bar\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"summary-info\"], [1, \"total-count\"], [1, \"fas\", \"fa-database\", \"me-1\"], [\"class\", \"selected-count\", 4, \"ngIf\"], [1, \"quick-filters\"], [1, \"d-flex\", \"gap-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-play-circle\", \"me-1\"], [1, \"fas\", \"fa-clock\", \"me-1\"], [1, \"fas\", \"fa-times-circle\", \"me-1\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-1\"], [1, \"enhanced-toolbar\"], [1, \"batch-operations\"], [1, \"selection-controls\"], [1, \"select-all-checkbox\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fw-medium\"], [\"class\", \"batch-actions ms-3\", 4, \"ngIf\"], [1, \"table-controls\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\"], [1, \"pagination-summary\"], [1, \"text-muted\"], [1, \"enhanced-table-container\"], [1, \"table\", \"table-hover\", \"enhanced-table\"], [1, \"enhanced-table-header\"], [\"width\", \"50\"], [3, \"ngModelChange\", \"ngModel\"], [\"width\", \"120\", 1, \"sortable\", 3, \"click\"], [1, \"header-content\"], [1, \"fas\", \"fa-sort\", \"sort-icon\"], [\"width\", \"100\", 1, \"sortable\", 3, \"click\"], [\"width\", \"80\", 1, \"sortable\", 3, \"click\"], [\"width\", \"140\", 1, \"sortable\", 3, \"click\"], [\"width\", \"120\"], [\"width\", \"100\"], [3, \"table-row-selected\", \"table-row-disabled\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"enhanced-pagination-container\", 4, \"ngIf\"], [1, \"selected-count\"], [1, \"fas\", \"fa-check-square\", \"me-1\", \"text-primary\"], [1, \"text-primary\"], [1, \"batch-actions\", \"ms-3\"], [\"title\", \"\\u6279\\u6B21\\u8A2D\\u5B9A\\u9078\\u4E2D\\u7684\\u6236\\u5225\\u958B\\u653E\\u6642\\u6BB5\", 1, \"btn\", \"btn-warning\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-cogs\", \"me-1\"], [1, \"badge\", \"bg-light\", \"text-dark\", \"ms-1\"], [\"title\", \"\\u6E05\\u9664\\u9078\\u64C7\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"ms-2\", 3, \"click\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"house-info\"], [1, \"house-name\", \"fw-medium\"], [\"class\", \"text-muted d-block\", 4, \"ngIf\"], [1, \"building-name\"], [1, \"floor-badge\"], [1, \"date-info\"], [\"class\", \"date-display\", 4, \"ngIf\"], [\"class\", \"not-set-text\", 4, \"ngIf\"], [1, \"status-display\"], [1, \"enhanced-status-badge\"], [1, \"status-icon\"], [1, \"status-text\"], [\"title\", \"\\u7DE8\\u8F2F\\u6642\\u6BB5\\u8A2D\\u5B9A\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-edit\"], [1, \"text-muted\", \"d-block\"], [1, \"date-display\"], [1, \"fas\", \"fa-calendar\", \"me-1\", \"text-success\"], [1, \"not-set-text\"], [1, \"fas\", \"fa-calendar\", \"me-1\", \"text-danger\"], [1, \"enhanced-pagination-container\"], [1, \"pagination-info-detailed\"], [1, \"ms-2\"], [1, \"pagination-nav\"], [1, \"pagination\", \"pagination-sm\", \"mb-0\"], [1, \"page-item\"], [\"title\", \"\\u7B2C\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [\"title\", \"\\u4E0A\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"\\u4E0B\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-right\"], [\"title\", \"\\u6700\\u5F8C\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"quick-jump\"], [1, \"input-group\", \"input-group-sm\", 2, \"width\", \"120px\"], [\"type\", \"number\", \"placeholder\", \"\\u9801\\u78BC\", 1, \"form-control\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"min\", \"max\"], [\"type\", \"button\", \"title\", \"\\u8DF3\\u8F49\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"mb-3\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"mt-2\"], [2, \"width\", \"600px\", \"max-height\", \"90vh\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"form-group\"], [1, \"text-red-600\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-fill\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"mx-2\"], [1, \"selection-options\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"alert\", \"alert-info\"], [1, \"selected-houses-preview\"], [\"class\", \"badge badge-primary mr-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"badge\", \"badge-primary\", \"mr-1\", \"mb-1\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"floor-selection\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-selection\"], [\"class\", \"house-selection ml-4\", 4, \"ngIf\"], [1, \"house-selection\", \"ml-4\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n        template: function SettingTimePeriodComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 9)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 10)(5, \"div\", 11)(6, \"p\", 12);\n            i0.ɵɵtext(7, \"\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u9593\\u7BC4\\u570D\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 13)(9, \"div\", 14)(10, \"div\", 15)(11, \"label\", 16);\n            i0.ɵɵtext(12, \"\\u5EFA\\u6848 \");\n            i0.ɵɵelementStart(13, \"span\", 17);\n            i0.ɵɵtext(14, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"nb-select\", 18);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_15_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_15_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onBuildCaseChange());\n            });\n            i0.ɵɵtemplate(16, SettingTimePeriodComponent_nb_option_16_Template, 2, 2, \"nb-option\", 19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 20)(18, \"label\", 16);\n            i0.ɵɵtext(19, \"\\u68DF\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"nb-select\", 21);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_20_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_20_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onBuildingChange());\n            });\n            i0.ɵɵelementStart(21, \"nb-option\", 22);\n            i0.ɵɵtext(22, \"\\u5168\\u90E8\\u68DF\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(23, SettingTimePeriodComponent_nb_option_23_Template, 2, 2, \"nb-option\", 19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 23)(25, \"label\", 16);\n            i0.ɵɵtext(26, \"\\u958B\\u653E\\u65E5\\u671F\\u7BC4\\u570D\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"div\", 24)(28, \"nb-form-field\", 25);\n            i0.ɵɵelement(29, \"nb-icon\", 26);\n            i0.ɵɵelementStart(30, \"input\", 27);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_30_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(31, \"nb-datepicker\", 28, 0);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"span\", 29);\n            i0.ɵɵtext(34, \"~\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"nb-form-field\", 25);\n            i0.ɵɵelement(36, \"nb-icon\", 26);\n            i0.ɵɵelementStart(37, \"input\", 30);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_37_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(38, \"nb-datepicker\", 28, 1);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(40, \"div\", 31)(41, \"div\", 32)(42, \"button\", 33);\n            i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_42_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getHouseChangeDate());\n            });\n            i0.ɵɵelement(43, \"i\", 34);\n            i0.ɵɵtext(44, \"\\u67E5\\u8A62 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"button\", 35);\n            i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_45_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.resetFilters());\n            });\n            i0.ɵɵelement(46, \"i\", 36);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(47, SettingTimePeriodComponent_div_47_Template, 38, 10, \"div\", 37);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(48, SettingTimePeriodComponent_div_48_Template, 73, 47, \"div\", 38)(49, SettingTimePeriodComponent_div_49_Template, 7, 0, \"div\", 39)(50, SettingTimePeriodComponent_div_50_Template, 9, 0, \"div\", 39);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(51, SettingTimePeriodComponent_ng_template_51_Template, 36, 9, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(53, SettingTimePeriodComponent_ng_template_53_Template, 4, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(55, SettingTimePeriodComponent_ng_template_55_Template, 29, 6, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            const StartDate_r30 = i0.ɵɵreference(32);\n            const EndDate_r31 = i0.ɵɵreference(39);\n            i0.ɵɵadvance(15);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.buildingOptions);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"nbDatepicker\", StartDate_r30);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"nbDatepicker\", EndDate_r31);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.loading);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.loading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length === 0 && ctx.houseChangeDates.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          }\n        },\n        dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.MinValidator, i9.MaxValidator, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, NbDatepickerModule, NbDateFnsDateModule],\n        styles: [\"@charset \\\"UTF-8\\\";.page-header-optimized[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#f8f9fa);border-radius:.75rem;box-shadow:0 2px 8px #b8a6761a;margin-bottom:1.5rem;overflow:hidden;border:1px solid rgba(184,166,118,.15)}.page-header-optimized[_ngcontent-%COMP%]   .page-description-section[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-bottom:1px solid rgba(184,166,118,.1)}.page-header-optimized[_ngcontent-%COMP%]   .page-description-section[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%]{font-size:.9rem;color:#6c757d;line-height:1.4;text-align:center;font-weight:400}.compact-filters[_ngcontent-%COMP%]{padding:1.25rem 1.5rem;background:linear-gradient(to bottom,#fafafa,#f8f9fa);border-bottom:1px solid rgba(184,166,118,.15)}.compact-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-weight:500;color:#495057;margin-bottom:.375rem;font-size:.8rem;text-transform:uppercase;letter-spacing:.5px}.compact-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]{flex:1}.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%]{color:#6c757d;font-weight:500;padding:0 .25rem}.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;justify-content:flex-end}.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-weight:500;border-radius:.375rem;transition:all .3s ease;padding:.5rem 1rem}.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#a69660);border-color:#b8a676;color:#fff}.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background:linear-gradient(135deg,#a69660,#95854a);border-color:#a69660;transform:translateY(-1px);box-shadow:0 4px 8px #b8a6764d}.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 .2rem #b8a67640}.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]{border-color:#6c757d;color:#6c757d}.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#6c757d;border-color:#6c757d;color:#fff;transform:translateY(-1px)}.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem}.advanced-filters-panel[_ngcontent-%COMP%]{padding:1rem 1.5rem 1.25rem;background-color:#fff;border-top:1px solid rgba(184,166,118,.1)}.advanced-filters-panel[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;align-items:center}.advanced-filters-panel[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .btn-outline-danger[_ngcontent-%COMP%]{font-size:.8rem;padding:.375rem .75rem}.advanced-filters[_ngcontent-%COMP%]{padding:1rem 1.5rem 1.5rem;background-color:#fff;border-top:1px solid rgba(184,166,118,.1)}.advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]{font-size:.875rem}.advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]   .text-primary[_ngcontent-%COMP%]{font-weight:500;color:#b8a676}.advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%], .advanced-filters[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]{width:100%}.advanced-filters[_ngcontent-%COMP%]   nb-select.ng-touched.ng-valid[_ngcontent-%COMP%]{border-color:#b8a67680}.advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border-color:#b8a676;box-shadow:0 0 0 .2rem #b8a67640}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa,#e9ecef);padding:1rem 1.25rem;border-radius:.5rem .5rem 0 0;border:1px solid rgba(184,166,118,.15);border-bottom:none}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1.5rem}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]{font-size:.875rem;color:#495057}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#b8a676}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#2c3e50}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#007bff}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.8rem;padding:.375rem .75rem;border-radius:.25rem;font-weight:500;transition:all .3s ease}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]{color:#fff}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-warning[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-danger[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]{background-color:#fff}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffc107,#e0a800);border-color:#ffc107;color:#212529;font-weight:600;box-shadow:0 2px 4px #ffc1074d}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#856404}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]{background-color:#fff;border:2px solid #ffc107;color:#856404;font-weight:500}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffc107}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#fff3cd,#ffeaa7);border-color:#e0a800;color:#856404;transform:translateY(-1px);box-shadow:0 3px 6px #ffc10733}.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{color:#e0a800}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]{background:linear-gradient(to bottom,#fff,#f8f9fa);padding:1rem 1.25rem;border:1px solid rgba(184,166,118,.15);border-top:none;border-bottom:none}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]{display:flex;align-items:center}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]{font-weight:500;color:#495057}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-weight:500;transition:all .3s ease;font-size:.875rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 3px 6px #00000026}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,#d4b96a,#c4a85a);border-color:#d4b96a;color:#5a4a2a}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]:hover:not(:disabled){background:linear-gradient(135deg,#c4a85a,#b4984a);border-color:#c4a85a}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background-color:#fff;color:#b8a676;font-weight:600;font-size:.7rem;padding:.2rem .4rem;border-radius:.25rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500;transition:all .3s ease}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 3px 6px #28a7454d}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .pagination-summary[_ngcontent-%COMP%]{font-size:.8rem;color:#6c757d;margin-left:1rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]{border:1px solid rgba(184,166,118,.2);border-radius:0 0 .5rem .5rem;overflow:hidden;box-shadow:0 4px 8px #b8a6761a}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]{margin-bottom:0;font-size:.875rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f6f0,#f0ede5)}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-top:none;font-weight:600;color:#495057;padding:1rem .75rem;border-bottom:2px solid rgba(184,166,118,.2);font-size:.8rem;text-transform:uppercase;letter-spacing:.5px}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .sort-icon[_ngcontent-%COMP%]{opacity:.4;color:#6c757d;transition:opacity .3s ease;margin-left:.5rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none;transition:all .3s ease}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover{background-color:#b8a6761a}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover   .sort-icon[_ngcontent-%COMP%]{opacity:1;color:#b8a676}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:all .3s ease}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#b8a6760d;transform:translateY(-1px);box-shadow:0 2px 4px #b8a6761a}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-selected[_ngcontent-%COMP%]{background-color:#b8a67626;border-left:3px solid #B8A676}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-disabled[_ngcontent-%COMP%]{opacity:.6;background-color:#f8f9fa}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:1rem .75rem;vertical-align:middle;border-bottom:1px solid rgba(184,166,118,.1)}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .house-info[_ngcontent-%COMP%]   .house-name[_ngcontent-%COMP%]{color:#2c3e50;font-size:.9rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .house-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.75rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%]{color:#495057;font-weight:500}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .floor-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e9ecef,#dee2e6);color:#495057;padding:.25rem .5rem;border-radius:.25rem;font-weight:500;font-size:.8rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%]{font-family:Courier New,monospace;font-size:.85rem;color:#495057}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .not-set-text[_ngcontent-%COMP%]{color:#856404;font-weight:500;font-size:.85rem;background:linear-gradient(135deg,#fff3cd,#ffeaa7);padding:.25rem .5rem;border-radius:.375rem;border:1px solid #ffc107;display:inline-flex;align-items:center}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .not-set-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffc107;font-size:.75rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;padding:.375rem .75rem;border-radius:1rem;font-size:.75rem;font-weight:500;border:1px solid transparent;transition:all .3s ease}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{margin-right:.375rem;font-size:.7rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{white-space:nowrap}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 2px 4px #0000001a}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{transition:all .3s ease;border-radius:.375rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:scale(1.1);box-shadow:0 3px 6px #007bff4d}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e8f5e8,#d4edda);color:#2d5a2d;border-color:#2d5a2d4d}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-active[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{color:#28a745}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-pending[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff8e1,#fff3cd);color:#8b6914;border-color:#8b69144d}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-pending[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{color:#ffc107}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-expired[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffebee,#f8d7da);color:#8b2635;border-color:#8b26354d}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-expired[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{color:#dc3545}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-not-set[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff3cd,#ffeaa7);color:#856404;border-color:#ffc10766;font-weight:600}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-not-set[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{color:#ffc107}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-disabled[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fafafa,#f8f9fa);color:#8a8a8a;border-color:#8a8a8a4d}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-disabled[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{color:#adb5bd}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]{background:linear-gradient(to bottom,#fff,#f8f9fa);padding:1.25rem;border:1px solid rgba(184,166,118,.15);border-top:none;border-radius:0 0 .5rem .5rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-info-detailed[_ngcontent-%COMP%]{font-size:.875rem;color:#495057}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-info-detailed[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#2c3e50;font-weight:600}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]{margin:0 .125rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{color:#495057;border-color:#b8a6764d;transition:all .3s ease;border-radius:.375rem;padding:.5rem .75rem;font-weight:500}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover{background-color:#b8a6761a;border-color:#b8a67680;color:#b8a676;transform:translateY(-1px);box-shadow:0 2px 4px #b8a67633}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#a69660);border-color:#b8a676;color:#fff;box-shadow:0 3px 6px #b8a6764d}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{color:#adb5bd;background-color:#fff;border-color:#b8a67633;cursor:not-allowed}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-color:#b8a6764d;font-size:.8rem;text-align:center}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#b8a676;box-shadow:0 0 0 .2rem #b8a67640}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-color:#b8a6764d;color:#495057;transition:all .3s ease}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{background-color:#b8a676;border-color:#b8a676;color:#fff}.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.search-enhanced[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:1rem;border-radius:.375rem;border:1px solid #dee2e6}.search-enhanced[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], .search-enhanced[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], .search-enhanced[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]{margin-bottom:0}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{color:#5a5a5a;border-color:#b8a6764d;transition:all .2s ease}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover{background-color:#b8a6761a;border-color:#b8a67680;color:#b8a676}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#a69660);border-color:#b8a676;color:#fff}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{color:#aaa;background-color:#fff;border-color:#b8a67633}.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]{margin-bottom:.5rem;padding-left:1rem}.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(150px,1fr));gap:.25rem;margin-top:.5rem;padding:.5rem;background-color:#f8f9fa;border-radius:.25rem}.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]{margin-bottom:.25rem}.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%]{max-height:200px;overflow-y:auto}.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem}.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{padding:.75rem;margin-bottom:1rem;border:1px solid transparent;border-radius:.375rem}.selection-options[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%]{color:#0c5460;background-color:#d1ecf1;border-color:#bee5eb}.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin-bottom:.5rem;font-weight:600}@media (max-width: 992px){.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%], .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]{margin-bottom:1rem}.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]{margin-bottom:.75rem}.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%]{margin-top:.5rem;justify-content:center}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]{overflow-x:auto}.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%], .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]{justify-content:center}}@media (max-width: 768px){.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{padding:1rem;border-radius:.375rem .375rem 0 0}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start!important}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]{font-size:1.25rem}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%]{font-size:.8rem;margin-top:.25rem}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]{margin-top:.75rem;text-align:left!important;width:100%}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%]{font-size:.9rem}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%]{font-size:.75rem}.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]{padding:1rem}.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .flex-fill[_ngcontent-%COMP%]{width:100%}.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .mx-2[_ngcontent-%COMP%]{margin:.25rem 0!important;text-align:center}.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{margin-bottom:.5rem}.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]{margin-bottom:.25rem}.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]{padding:1rem}.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]{text-align:center;margin-bottom:.5rem}.search-enhanced[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{margin-bottom:.5rem}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]{font-size:.875rem}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:.5rem .25rem}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3), .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(3){display:none}}@media (max-width: 576px){.integrated-header[_ngcontent-%COMP%]{margin-bottom:1rem;border-radius:.25rem}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{padding:.75rem;border-radius:.25rem .25rem 0 0}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]{font-size:1.1rem}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem;margin-right:.25rem}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%]{font-size:.75rem;display:none}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]{margin-top:.5rem}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%]{font-size:.85rem}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem}.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%]{font-size:.7rem}.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]{padding:.75rem}.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%;margin-bottom:.5rem}.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{margin-bottom:0}.integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.75rem}.integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%;justify-content:center}.integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]{width:100%}.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]{padding:.75rem}.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.query-section[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%]{margin-bottom:1rem}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4), .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(4){display:none}}nb-card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#495057;font-weight:600}nb-card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]{font-size:.8rem;padding:.25rem .5rem}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:500;color:#5a5a5a;margin-bottom:.5rem}.btn-outline-primary[_ngcontent-%COMP%]{color:#b8a676;border-color:#b8a676;transition:all .3s ease}.btn-outline-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#b8a676,#a69660);border-color:#b8a676;color:#fff;transform:translateY(-1px);box-shadow:0 3px 6px #b8a6764d}.btn-outline-primary[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 .2rem #b8a67640}.btn-secondary[_ngcontent-%COMP%]{background-color:#f8f9fa;border-color:#dee2e6;color:#6c757d}.btn-secondary[_ngcontent-%COMP%]:hover{background-color:#e9ecef;border-color:#adb5bd;color:#5a5a5a}.status-indicator[_ngcontent-%COMP%]{display:inline-block;width:8px;height:8px;border-radius:50%;margin-right:.5rem;box-shadow:0 1px 2px #0000001a}.status-indicator.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4caf50,#45a049)}.status-indicator.pending[_ngcontent-%COMP%]{background:linear-gradient(135deg,#d4b96a,#c4a85a)}.status-indicator.expired[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f44336,#e53935)}.status-indicator.not-set[_ngcontent-%COMP%]{background:linear-gradient(135deg,#9e9e9e,#757575)}.status-indicator.disabled[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e0e0e0,#bdbdbd)}  nb-select.appearance-outline .select-button{border-color:#b8a6764d}  nb-select.appearance-outline .select-button:focus{border-color:#b8a676;box-shadow:0 0 0 .2rem #b8a67640}  nb-form-field.appearance-outline .form-control{border-color:#b8a6764d}  nb-form-field.appearance-outline .form-control:focus{border-color:#b8a676;box-shadow:0 0 0 .2rem #b8a67640}  nb-checkbox .customised-control-input:checked~.customised-control-indicator{background-color:#b8a676;border-color:#b8a676}  nb-calendar-day-cell.selected{background-color:#b8a676;border-color:#b8a676}\"]\n      });\n    }\n  }\n  return SettingTimePeriodComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}