import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { NbDialogService } from '@nebular/theme';

export interface HouseholdItem {
  houseName: string;
  building: string;
  floor?: string;
  houseId?: number;
  houseType?: number; // 新增：戶別類型
  isSelected?: boolean;
  isDisabled?: boolean;
}

export interface BuildingData {
  [key: string]: HouseholdItem[];
}

@Component({
  selector: 'app-household-binding',
  templateUrl: './household-binding.component.html',
  styleUrls: ['./household-binding.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => HouseholdBindingComponent),
      multi: true,
    },
  ],
})
export class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {
  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;
  @Input() placeholder: string = '請選擇戶別';
  @Input() maxSelections: number | null = null;
  @Input() disabled: boolean = false;
  @Input() buildCaseId: number | null = null; // 建案ID（用於識別）
  @Input() buildingData: BuildingData = {};
  @Input() allowBatchSelect: boolean = true;
  @Input() excludedHouseIds: number[] = []; // 改為：排除的戶別ID（已被其他元件選擇）
  @Input() useHouseNameMode: boolean = false; // 新增：使用戶別名稱模式
  @Input() preFilterHouseType: number | null = null; // 新增：預先篩選的戶別類型（1=地主戶，2=銷售戶）
  @Input() reminderText: string = ''; // 新增：提醒文案

  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();
  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列
  @Output() houseNameChange = new EventEmitter<string[]>(); // 新增：useHouseNameMode 時回傳戶別名稱陣列
  isOpen = false;
  selectedBuilding = '';
  searchTerm = ''; selectedFloor = ''; // 新增：選中的樓層
  selectedHouseIds: number[] = []; // 改為：使用 houseId 作為選擇的key
  selectedHouseNames: string[] = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列
  buildings: string[] = [];
  floors: string[] = []; // 新增：當前棧別的樓層列表
  filteredHouseholds: string[] = [];  // 保持為字串陣列用於UI顯示
  selectedByBuilding: { [building: string]: number[] } = {}; // 改為：儲存 houseId
  isLoading: boolean = false; // 新增：載入狀態  // ControlValueAccessor implementation
  private onChange = (value: number[] | string[]) => { };
  private onTouched = () => { }; constructor(
    private cdr: ChangeDetectorRef,
    private dialogService: NbDialogService
  ) { }

  /**
   * [優化] 產生已選戶別的分組摘要文字。
   * 用於簡潔地顯示選擇結果。
   * @returns string - 例如: "棟 A (10戶), 棟 B (5戶)"
   */
  getGroupedSummary(): string {
    if (this.selectedHouseIds.length === 0) {
      return '';
    }

    return this.buildings
      .map(building => {
        const count = this.selectedByBuilding[building]?.length || 0;
        return count > 0 ? `${building} (${count}戶)` : null;
      })
      .filter(Boolean) // 過濾掉沒有選擇的棟別
      .join(', ');
  }

  /**
   * [優化] 獲取所有已選戶別的詳細資訊列表。
   * 用於在 Popover 中顯示。
   * @returns HouseholdItem[]
   */
  getAllSelectedItems(): HouseholdItem[] {
    return this.selectedHouseIds
      .map(id => this.getHouseholdByHouseId(id))
      .filter((item): item is HouseholdItem => !!item);
  }

  writeValue(value: any[]): void {
    if (!value || value.length === 0) {
      this.selectedHouseIds = [];
      this.selectedHouseNames = [];
    } else {
      const firstItem = value[0]; if (this.useHouseNameMode) {
        // useHouseNameMode: 期望接收戶別名稱陣列
        if (typeof firstItem === 'string') {
          this.selectedHouseNames = [...new Set(value as string[])]; // 去除重複的戶別名稱
          // 將戶別名稱轉換為 houseId（用於內部邏輯），會自動處理重複名稱
          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);
          console.log('useHouseNameMode: 使用傳入的戶別名稱陣列（已去重）');
        } else {
          console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);
          this.selectedHouseNames = [];
          this.selectedHouseIds = [];
        }
      } else {
        // 一般模式: 期望接收 houseId 陣列
        if (typeof firstItem === 'number') {
          this.selectedHouseIds = value as number[];
          // 將 houseId 轉換為戶別名稱（用於顯示）
          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);
          console.log('一般模式: 使用傳入的 houseId 陣列');
        } else if (typeof firstItem === 'string') {
          // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告
          console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');
          console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');
          return;
        } else {
          console.error('writeValue 收到未知格式的資料:', value);
          this.selectedHouseIds = [];
          this.selectedHouseNames = [];
        }
      }
    }
    this.updateSelectedByBuilding();
  }
  registerOnChange(fn: (value: number[] | string[]) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }
  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  } ngOnInit() {
    this.initializeData();
  } ngOnChanges(changes: SimpleChanges) {
    if (changes['buildingData']) {
      // 當 buildingData 變更時，重新初始化
      const newBuildingData = changes['buildingData'].currentValue;

      // 如果 buildingData 為空物件或 undefined，可能正在載入
      if (!newBuildingData || Object.keys(newBuildingData).length === 0) {
        this.isLoading = false; // 假設空物件表示載入完成但無資料
      } else {
        this.isLoading = false; // 有資料表示載入完成
      }

      this.buildings = Object.keys(this.buildingData || {});
      console.log('buildingData updated:', this.buildingData);
      this.updateFilteredHouseholds();
      this.updateSelectedByBuilding();
    } if (changes['excludedHouseIds']) {
      // 當排除列表變化時，重新更新顯示
      this.updateFilteredHouseholds();
    }
    if (changes['useHouseNameMode']) {
      if (this.useHouseNameMode) {
        this.selectedFloor = '';
      }
    }
  } private initializeData() {
    // 使用傳入的 buildingData
    if (this.buildingData && Object.keys(this.buildingData).length > 0) {
      this.buildings = Object.keys(this.buildingData);
      this.updateSelectedByBuilding();
    } else {
      // 沒有 buildingData，保持空狀態
      this.buildings = [];
      console.log('No buildingData provided');
    }
  } private updateSelectedByBuilding() {
    const grouped: { [building: string]: number[] } = {};

    this.selectedHouseIds.forEach(houseId => {
      for (const building of this.buildings) {
        const item = this.buildingData[building]?.find(h => h.houseId === houseId);
        if (item) {
          if (!grouped[building]) grouped[building] = [];
          grouped[building].push(houseId);
          break;
        }
      }
    });

    this.selectedByBuilding = grouped;
  } onBuildingSelect(building: string) {
    console.log('Building selected:', building);
    this.selectedBuilding = building;
    this.selectedFloor = ''; // 重置樓層選擇
    this.searchTerm = '';
    this.updateFloorsForBuilding(); // 更新樓層列表
    this.updateFilteredHouseholds();
    console.log('Filtered households count:', this.filteredHouseholds.length);
    // 手動觸發變更偵測
    this.cdr.detectChanges();
  }

  onBuildingClick(building: string) {
    console.log('Building clicked (mousedown):', building);
  }
  updateFilteredHouseholds() {
    if (!this.selectedBuilding) {
      this.filteredHouseholds = [];
      return;
    }

    const households = this.buildingData[this.selectedBuilding] || [];
    console.log('Available households for building:', households.length);

    // 提取戶別代碼並進行搜尋、樓層和戶別類型過濾
    const filteredItems = households.filter(h => {
      // 樓層篩選：在 useHouseNameMode 時跳過樓層篩選，否則按原邏輯篩選
      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;
      // 搜尋篩選：戶別代碼包含搜尋詞
      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());
      // 戶別類型篩選：如果有指定預篩選類型，則只顯示符合的戶別
      const houseTypeMatch = this.preFilterHouseType === null || h.houseType === this.preFilterHouseType;
      return floorMatch && searchMatch && houseTypeMatch;
    });

    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）
    this.filteredHouseholds = filteredItems.map(h => h.houseName);

    console.log('Filtered households result:', this.filteredHouseholds.length);
    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId}, Type:${h.houseType})`));
    if (this.preFilterHouseType !== null) {
      console.log('Pre-filter house type:', this.preFilterHouseType);
    }
  }

  onSearchChange(event: any) {
    this.searchTerm = event.target.value;
    this.updateFilteredHouseholds();
    console.log('Search term changed:', this.searchTerm);
  }
  resetSearch() {
    this.searchTerm = '';
    this.updateFilteredHouseholds();
    console.log('Search reset');
  } onHouseholdToggle(houseId: number | undefined) {
    console.log('onHouseholdToggle called with houseId:', houseId);
    console.log('Current selectedHouseIds:', this.selectedHouseIds);

    if (!houseId) {
      console.log(`無效的 houseId: ${houseId}`);
      return;
    }

    // 防止選擇已排除的戶別
    if (this.isHouseIdExcluded(houseId)) {
      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);
      return;
    }

    // 取得被點擊戶別的名稱
    const clickedHousehold = this.getHouseholdByHouseId(houseId);
    if (!clickedHousehold) {
      console.log(`找不到 houseId ${houseId} 對應的戶別資訊`);
      return;
    }

    let newSelection: number[];

    if (this.useHouseNameMode) {
      // useHouseNameMode: 處理同名戶別的邏輯
      const houseName = clickedHousehold.houseName;
      const allMatchingHouseIds = this.getAllHouseIdsByHouseName(houseName);

      // 檢查是否有任何同名戶別已被選中
      const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));

      if (hasAnySelected) {
        // 如果有同名戶別被選中，移除所有同名戶別
        newSelection = this.selectedHouseIds.filter(id => !allMatchingHouseIds.includes(id));
        console.log(`useHouseNameMode: 移除所有同名戶別 "${houseName}":`, allMatchingHouseIds);
      } else {
        // 如果沒有同名戶別被選中，只添加第一個（通常是當前點擊的）
        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {
          console.log('已達到最大選擇數量');
          return;
        }
        newSelection = [...this.selectedHouseIds, allMatchingHouseIds[0]];
        console.log(`useHouseNameMode: 添加戶別 "${houseName}" 的第一個項目:`, allMatchingHouseIds[0]);
      }
    } else {
      // 一般模式: 原有邏輯
      const isSelected = this.isHouseIdSelected(houseId);

      if (isSelected) {
        newSelection = this.selectedHouseIds.filter(id => id !== houseId);
      } else {
        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {
          console.log('已達到最大選擇數量');
          return;
        }
        newSelection = [...this.selectedHouseIds, houseId];
      }
    }

    this.selectedHouseIds = newSelection;
    this.emitChanges();
  }
  onRemoveHousehold(houseId: number) {
    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);
    this.emitChanges();
  } onSelectAllFiltered() {
    console.log('onSelectAllFiltered called');
    console.log('selectedBuilding:', this.selectedBuilding);
    console.log('selectedFloor:', this.selectedFloor);
    console.log('searchTerm:', this.searchTerm);

    if (!this.selectedBuilding) {
      console.log('No building selected');
      return;
    }

    // 使用 getUniqueHouseholdsForDisplay 方法來獲取要處理的戶別列表
    const filteredHouseholdItems = this.getUniqueHouseholdsForDisplay();

    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));

    if (filteredHouseholdItems.length === 0) {
      console.log('No filtered households found');
      return;
    }

    // 計算可以新增的戶別數量
    const currentCount = this.selectedHouseIds.length;
    const maxAllowed = this.maxSelections || Infinity;
    const remainingSlots = maxAllowed - currentCount;

    // 取得尚未選擇且未被排除的過濾戶別ID
    const unselectedFilteredIds: number[] = [];
    for (const household of filteredHouseholdItems) {
      if (household.houseId) {
        if (this.useHouseNameMode) {
          // useHouseNameMode: 檢查是否有任何同名戶別已被選擇
          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);
          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));
          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseIdExcluded(id));

          if (!hasAnySelected && !hasAnyExcluded) {
            unselectedFilteredIds.push(allMatchingHouseIds[0]); // 只選擇第一個
          }
        } else {
          // 一般模式: 原有邏輯
          if (!this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {
            unselectedFilteredIds.push(household.houseId);
          }
        }
      }
    }

    console.log('Unselected filtered IDs:', unselectedFilteredIds);

    // 根據剩餘空間決定要新增的戶別
    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);

    if (toAdd.length > 0) {
      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];
      this.emitChanges();
      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);
    } else {
      console.log('No households to add');
    }
  } onSelectAllBuilding() {
    if (!this.selectedBuilding) return;

    // 取得當前棟別的所有戶別
    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];

    // 計算可以新增的戶別數量
    const currentCount = this.selectedHouseIds.length;
    const maxAllowed = this.maxSelections || Infinity;
    const remainingSlots = maxAllowed - currentCount;

    // 取得尚未選擇且未被排除的棟別戶別 ID
    const unselectedBuildingIds: number[] = [];

    if (this.useHouseNameMode) {
      // useHouseNameMode: 只選擇唯一的戶別名稱
      const processedHouseNames = new Set<string>();

      for (const household of buildingHouseholds) {
        if (household.houseId && household.houseName && !processedHouseNames.has(household.houseName)) {
          processedHouseNames.add(household.houseName);

          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);
          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));
          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseholdExcluded(id));

          if (!hasAnySelected && !hasAnyExcluded) {
            unselectedBuildingIds.push(allMatchingHouseIds[0]); // 只選擇第一個
          }
        }
      }
    } else {
      // 一般模式: 原有邏輯
      for (const household of buildingHouseholds) {
        if (household.houseId &&
          !this.selectedHouseIds.includes(household.houseId) &&
          !this.isHouseholdExcluded(household.houseId)) {
          unselectedBuildingIds.push(household.houseId);
        }
      }
    }

    // 根據剩餘空間決定要新增的戶別
    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);

    if (toAdd.length > 0) {
      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];
      this.emitChanges();
      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);
    }
  }

  onUnselectAllBuilding() {
    if (!this.selectedBuilding) return;

    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];
    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined) as number[];
    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));
    this.emitChanges();
  }

  onClearAll() {
    this.selectedHouseIds = [];
    this.emitChanges();
  } private emitChanges() {
    this.updateSelectedByBuilding();
    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds);    // 根據模式決定要回傳的資料格式
    if (this.useHouseNameMode) {
      // useHouseNameMode: 回傳戶別名稱陣列（去重複）
      this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);
      const uniqueHouseNames = [...new Set(this.selectedHouseNames)]; // 確保去重複
      console.log('useHouseNameMode - 回傳戶別名稱陣列（已去重）:', uniqueHouseNames);
      this.onChange([...uniqueHouseNames]);
      this.houseNameChange.emit([...uniqueHouseNames]);
    } else {
      // 一般模式: 回傳 houseId 陣列
      console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);
      this.onChange([...this.selectedHouseIds]);

      // 回傳 houseId 陣列
      const houseIds = this.selectedHouseIds.filter(id => id !== undefined);
      console.log('House IDs to emit:', houseIds);
      this.houseIdChange.emit(houseIds);
    }

    this.onTouched();

    // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）
    const selectedItems = this.selectedHouseIds.map(houseId => {
      for (const building of this.buildings) {
        const item = this.buildingData[building]?.find(h => h.houseId === houseId);
        if (item) return item;
      }
      return null;
    }).filter(item => item !== null) as HouseholdItem[];

    console.log('Selected items to emit:', selectedItems);
    this.selectionChange.emit(selectedItems);
  } toggleDropdown() {
    if (!this.disabled) {
      this.openDialog();
      console.log('Opening household selection dialog');
      console.log('Buildings available:', this.buildings);
    }
  }

  openDialog() {
    this.dialogService.open(this.householdDialog, {
      context: {},
      closeOnBackdropClick: false,
      closeOnEsc: true,
      autoFocus: false,
    });
  }

  closeDropdown() {
    // 這個方法現在用於關閉對話框
    // 對話框的關閉將由 NbDialogRef 處理
  } isHouseholdSelected(houseId: number | undefined): boolean {
    if (!houseId) return false;

    if (this.useHouseNameMode) {
      // useHouseNameMode: 檢查是否有任何同名戶別被選中
      const household = this.getHouseholdByHouseId(houseId);
      if (household) {
        const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);
        return allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));
      }
      return false;
    } else {
      // 一般模式: 直接檢查 houseId
      return this.selectedHouseIds.includes(houseId);
    }
  }

  isHouseholdExcluded(houseId: number | undefined): boolean {
    if (!houseId) return false;
    return this.excludedHouseIds.includes(houseId);
  }

  isHouseholdDisabled(houseId: number | undefined): boolean {
    if (!houseId) return true;
    return this.isHouseholdExcluded(houseId) ||
      (!this.canSelectMore() && !this.isHouseholdSelected(houseId));
  }
  canSelectMore(): boolean {
    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;
  } isAllBuildingSelected(): boolean {
    if (!this.selectedBuilding) return false;
    const buildingHouseholds = this.buildingData[this.selectedBuilding]
      .filter(h => !h.isDisabled && h.houseId !== undefined);
    return buildingHouseholds.length > 0 &&
      buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));
  }

  isSomeBuildingSelected(): boolean {
    if (!this.selectedBuilding) return false;
    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];
    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));
  } getSelectedByBuilding(): { [building: string]: number[] } {
    return this.selectedByBuilding;
  }
  getBuildingCount(building: string): number {
    if (this.useHouseNameMode) {
      // useHouseNameMode: 返回唯一戶別名稱的數量
      const households = this.buildingData[building] || [];
      const uniqueHouseNames = new Set(households.map(h => h.houseName));
      return uniqueHouseNames.size;
    } else {
      // 一般模式: 返回總戶別數量
      return this.buildingData[building]?.length || 0;
    }
  }
  getSelectedCount(): number {
    if (this.useHouseNameMode) {
      // useHouseNameMode: 返回唯一戶別名稱的數量
      const uniqueHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);
      return uniqueHouseNames.length;
    } else {
      // 一般模式: 返回實際選中的戶別數量
      return this.selectedHouseIds.length;
    }
  }

  // 輔助方法：安全地獲取建築物的已選戶別 ID
  getBuildingSelectedHouseIds(building: string): number[] {
    return this.selectedByBuilding[building] || [];
  }

  // 輔助方法：檢查建築物是否有已選戶別
  hasBuildingSelected(building: string): boolean {
    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);
  }

  // 新增：更新當前棧別的樓層計數
  private updateFloorsForBuilding() {
    if (!this.selectedBuilding) {
      this.floors = [];
      return;
    }

    const households = this.buildingData[this.selectedBuilding] || [];
    const floorSet = new Set<string>();

    households.forEach(household => {
      if (household.floor) {
        floorSet.add(household.floor);
      }
    });

    // 對樓層進行自然排序
    this.floors = Array.from(floorSet).sort((a, b) => {
      const numA = parseInt(a.replace(/[^0-9]/g, ''));
      const numB = parseInt(b.replace(/[^0-9]/g, ''));
      return numA - numB;
    });

    console.log('Updated floors for building:', this.selectedBuilding, this.floors);
  }

  // 新增：樓層選擇處理
  onFloorSelect(floor: string) {
    console.log('Floor selected:', floor);
    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇
    this.updateFilteredHouseholds();
    this.cdr.detectChanges();
  }

  // 新增：取得當前棧別的樓層計數
  getFloorCount(floor: string): number {
    if (!this.selectedBuilding) return 0;
    const households = this.buildingData[this.selectedBuilding] || [];
    return households.filter(h => h.floor === floor).length;
  }
  // 新增：取得戶別的樓層資訊
  getHouseholdFloor(householdCode: string): string {
    if (!this.selectedBuilding) return '';
    const households = this.buildingData[this.selectedBuilding] || [];
    const household = households.find(h => h.houseName === householdCode);
    return household?.floor || '';
  }
  // 新增：取得戶別的完整資訊（包含樓層）
  getHouseholdInfo(householdCode: string): { houseName: string, floor: string } {
    for (const building of this.buildings) {
      const households = this.buildingData[building] || [];
      const household = households.find(h => h.houseName === householdCode);
      if (household) {
        return {
          houseName: household.houseName,
          floor: household.floor || ''
        };
      }
    }
    return { houseName: householdCode, floor: '' };
  }

  // 新增：根據 houseId 取得戶別的完整資訊
  getHouseholdInfoById(houseId: number): { houseName: string, floor: string } {
    for (const building of this.buildings) {
      const households = this.buildingData[building] || [];
      const household = households.find(h => h.houseId === houseId);
      if (household) {
        return {
          houseName: household.houseName,
          floor: household.floor || ''
        };
      }
    }
    return { houseName: `ID:${houseId}`, floor: '' };
  }

  // 新增：檢查搜尋是否有結果
  hasNoSearchResults(): boolean {
    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {
      return false;
    } const filtered = this.buildingData[this.selectedBuilding].filter(h => {
      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;
      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());
      const houseTypeMatch = this.preFilterHouseType === null || h.houseType === this.preFilterHouseType;
      return floorMatch && searchMatch && houseTypeMatch;
    });

    return filtered.length === 0;
  }
  // 新增：取得過濾後的戶別數量
  getFilteredHouseholdsCount(): number {
    return this.getUniqueHouseholdsForDisplay().length;
  }

  // 新增：產生戶別的唯一識別符
  getHouseholdUniqueId(household: HouseholdItem): string {
    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;
  }
  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem
  private getHouseholdByHouseId(houseId: number): HouseholdItem | null {
    for (const building of this.buildings) {
      const households = this.buildingData[building] || [];
      const household = households.find(h => h.houseId === houseId);
      if (household) return household;
    }
    return null;
  }  // 新增：輔助方法 - 根據 houseName 查找 houseId
  private getHouseIdByHouseName(houseName: string): number | null {
    const matchingHouseholds: { building: string, household: HouseholdItem }[] = [];

    // 收集所有符合名稱的戶別
    for (const building of this.buildings) {
      const households = this.buildingData[building] || [];
      const matches = households.filter(h => h.houseName === houseName);
      matches.forEach(household => {
        matchingHouseholds.push({ building, household });
      });
    }

    console.log(`查找 houseName "${houseName}" 的結果:`, matchingHouseholds);

    if (matchingHouseholds.length === 0) {
      console.warn(`找不到 houseName "${houseName}" 對應的戶別`);
      return null;
    }

    if (matchingHouseholds.length > 1) {
      console.warn(`發現多個同名戶別 "${houseName}":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));
      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);
    }

    const firstMatch = matchingHouseholds[0];
    return firstMatch.household.houseId || null;
  }

  // 新增：輔助方法 - 根據 houseName 查找所有對應的 houseId（處理重複名稱）
  private getAllHouseIdsByHouseName(houseName: string): number[] {
    const houseIds: number[] = [];

    // 收集所有符合名稱的戶別
    for (const building of this.buildings) {
      const households = this.buildingData[building] || [];
      const matches = households.filter(h => h.houseName === houseName);
      matches.forEach(household => {
        if (household.houseId) {
          houseIds.push(household.houseId);
        }
      });
    }

    return houseIds;
  }

  // 新增：輔助方法 - 檢查 houseId 是否被選中
  private isHouseIdSelected(houseId: number): boolean {
    return this.selectedHouseIds.includes(houseId);
  }

  // 新增：輔助方法 - 檢查 houseId 是否被排除
  private isHouseIdExcluded(houseId: number): boolean {
    return this.excludedHouseIds.includes(houseId);
  }
  // 新增：從唯一識別符獲取戶別物件
  getHouseholdFromUniqueId(uniqueId: string): HouseholdItem | null {
    for (const building of this.buildings) {
      const households = this.buildingData[building] || [];
      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);
      if (household) return household;
    }
    return null;
  }  // 新增：將戶別名稱陣列轉換為 houseId 陣列（在 useHouseNameMode 下只選擇第一個匹配項）
  private convertHouseNamesToIds(houseNames: string[]): number[] {
    const houseIds: number[] = [];
    const uniqueHouseNames = [...new Set(houseNames)]; // 去除重複的戶別名稱

    for (const houseName of uniqueHouseNames) {
      const matchingHouseIds = this.getAllHouseIdsByHouseName(houseName);
      if (matchingHouseIds.length > 0) {
        if (this.useHouseNameMode) {
          // useHouseNameMode: 只選擇第一個匹配的 houseId，避免多個同名戶別都被選中
          houseIds.push(matchingHouseIds[0]);
          if (matchingHouseIds.length > 1) {
            console.log(`戶別名稱 "${houseName}" 有 ${matchingHouseIds.length} 個重複項目，在 useHouseNameMode 下只選擇第一個:`, matchingHouseIds[0]);
          }
        } else {
          // 一般模式: 將所有對應的 houseId 都加入（保持原有邏輯）
          houseIds.push(...matchingHouseIds);
          if (matchingHouseIds.length > 1) {
            console.warn(`戶別名稱 "${houseName}" 有 ${matchingHouseIds.length} 個重複項目，已全部加入選擇:`, matchingHouseIds);
          }
        }
      } else {
        console.warn(`無法找到戶別名稱 "${houseName}" 對應的 houseId`);
      }
    }

    // 去除重複的 houseId
    return [...new Set(houseIds)];
  }
  // 新增：將 houseId 陣列轉換為戶別名稱陣列（去重複）
  private convertIdsToHouseNames(houseIds: number[]): string[] {
    const houseNames: string[] = [];
    const uniqueHouseIds = [...new Set(houseIds)]; // 去除重複的 houseId

    for (const houseId of uniqueHouseIds) {
      const householdInfo = this.getHouseholdInfoById(houseId);
      if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {
        houseNames.push(householdInfo.houseName);
      } else {
        console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);
      }
    }

    // 去除重複的戶別名稱
    return [...new Set(houseNames)];
  }

  // 新增：取得去重複的戶別列表（用於 useHouseNameMode 顯示）
  getUniqueHouseholdsForDisplay(): HouseholdItem[] {
    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {
      return [];
    }

    const households = this.buildingData[this.selectedBuilding] || [];

    if (!this.useHouseNameMode) {
      // 一般模式：返回所有戶別
      return households.filter(h => {
        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;
        const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());
        const houseTypeMatch = this.preFilterHouseType === null || h.houseType === this.preFilterHouseType;
        return floorMatch && searchMatch && houseTypeMatch;
      });
    }

    // useHouseNameMode：只返回唯一的戶別名稱
    const uniqueHouseNames = new Set<string>();
    const uniqueHouseholds: HouseholdItem[] = [];

    for (const household of households) {
      // 搜尋篩選
      const searchMatch = !this.searchTerm || household.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());
      // 戶別類型篩選
      const houseTypeMatch = this.preFilterHouseType === null || household.houseType === this.preFilterHouseType;

      if (searchMatch && houseTypeMatch && !uniqueHouseNames.has(household.houseName)) {
        uniqueHouseNames.add(household.houseName);
        uniqueHouseholds.push(household);
      }
    }
    return uniqueHouseholds;
  }

  // 新增：動態獲取文案的getter方法
  get displayText() {
    return {
      unitType: this.useHouseNameMode ? '戶型' : '戶別',
      placeholder: this.useHouseNameMode ? '請選擇戶型' : '請選擇戶別',
      selectedPrefix: this.useHouseNameMode ? '已選擇戶型' : '已選擇戶別',
      selectUnit: this.useHouseNameMode ? '選擇戶型' : '選擇戶別',
      unitSelection: this.useHouseNameMode ? '戶型選擇' : '戶別選擇',
      selectedCount: this.useHouseNameMode ? '個戶型' : '個戶',
      searchPlaceholder: this.useHouseNameMode ? '搜尋戶型...' : '搜尋戶別...',
      noResults: this.useHouseNameMode ? '找不到符合的戶型' : '找不到符合的戶別',
      noAvailable: this.useHouseNameMode ? '此棟別沒有可選擇的戶型' : '此棟別沒有可選擇的戶別'
    };
  }
}
