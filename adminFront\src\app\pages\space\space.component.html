<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>
  <nb-card-body>
    <div class="alert alert-info mb-4"
      style="border-left: 4px solid #4a90e2; background-color: #f8f9ff; border-radius: 6px;">
      <div class="d-flex align-items-center">
        <i class="fas fa-info-circle text-primary me-3" style="font-size: 1.2rem;"></i>
        <div>
          <p class="mb-0 text-muted" style="font-size: 0.9rem;">
            在此頁面您可以管理系統中的各個空間資訊，包括新增、編輯、刪除空間，以及設定項目名稱、所屬區域和啟用狀態等。
          </p>
        </div>
      </div>
    </div>
    <div class="d-flex flex-wrap">
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="spaceName" class="label col-3">項目名稱</label>
          <nb-form-field class="col-9">
            <input type="text" id="spaceName" nbInput class="w-full" placeholder="搜尋項目名稱..." [(ngModel)]="searchKeyword"
              (keyup.enter)="onSearch()">
          </nb-form-field>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="location" class="label col-3">所屬區域</label>
          <nb-form-field class="col-9">
            <input type="text" id="location" nbInput class="w-full" placeholder="搜尋所屬區域..." [(ngModel)]="searchLocation"
              (keyup.enter)="onSearch()">
          </nb-form-field>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="status" class="label col-3">狀態</label>
          <nb-form-field class="col-9">
            <nb-select id="status" placeholder="選擇狀態..." [(ngModel)]="searchStatus" (selectedChange)="onSearch()">
              <nb-option [value]="null">全部</nb-option>
              <nb-option [value]="1">啟用</nb-option>
              <nb-option [value]="0">停用</nb-option>
            </nb-select>
          </nb-form-field>
        </div>
      </div>

      <div class="col-md-6">
        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->
      </div>

      <!-- 查詢和重置按鈕移到這裡，放在搜尋條件的右下角 -->
      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full mt-2 mb-3">
          <button class="btn btn-outline-secondary btn-sm me-2" (click)="onReset()">
            <i class="fas fa-undo me-1"></i>重置
          </button>
          <button class="btn btn-secondary btn-sm" (click)="onSearch()">
            <i class="fas fa-search me-1"></i>查詢
          </button>
        </div>
      </div>

      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full mt-3">
          <button class="btn btn-info mx-1 btn-sm" *ngIf="isCreate" (click)="openCreateModal(createModal)">
            <i class="fas fa-plus me-1"></i>新增空間
          </button>
          <button class="btn btn-warning mx-1 btn-sm" (click)="navigateToTemplate()">
            <i class="fas fa-layer-group me-1"></i>前往管理模板
          </button>
        </div>
      </div>
    </div>

    <div class="table-responsive mt-4">
      <table class="table" style="min-width: 800px;">
        <thead>
          <tr class="table-header">
            <th scope="col" class="col-2">所屬區域</th>
            <th scope="col" class="col-2">項目名稱</th>
            <th scope="col" class="col-2">狀態</th>
            <th scope="col" class="col-3">建立時間</th>
            <th scope="col" class="col-3">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of spaceList">
            <td>{{ item.CLocation || '-' }}</td>
            <td>{{ item.CPart }}</td>
            <td>
              {{ item.CStatus === 1 ? '啟用' : '停用' }}
            </td>
            <td>{{ item.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>
            <td class="table-actions">
              <button *ngIf="isUpdate" class="btn btn-outline-success btn-sm" (click)="openEditModal(editModal, item)">
                <i class="fas fa-edit"></i>編輯
              </button>
              <button *ngIf="isDelete" class="btn btn-outline-danger btn-sm" (click)="deleteSpace(item)">
                <i class="fas fa-trash"></i>刪除
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">
    <ngx-pagination [(Page)]="pageIndex" [PageSize]="pageSize" [CollectionSize]="totalRecords"
      (PageChange)="pageChanged($event)">
    </ngx-pagination>
  </nb-card-footer>
</nb-card>

<!-- 新增模態框 -->
<ng-template #createModal let-ref="dialogRef">
  <nb-card style="width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);">
    <nb-card-header class="d-flex justify-content-between align-items-center border-bottom py-3 px-4">
      <h5 class="mb-0 text-primary font-weight-bold">
        <i class="fas fa-plus-circle me-2 text-success"></i>新增空間
      </h5>
      <button type="button" class="btn btn-ghost-light btn-sm rounded-circle" (click)="onClose(ref)"
        style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
        <i class="fas fa-times"></i>
      </button>
    </nb-card-header>

    <nb-card-body class="px-4 py-4">
      <div class="row">
        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label for="spaceName" class="required-field mb-0"
                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                項目名稱
              </label>
              <div class="flex-grow-1 ml-3">
                <input type="text" id="spaceName" class="form-control" nbInput placeholder="請輸入項目名稱"
                  [(ngModel)]="spaceDetail.CPart" name="spaceName"
                  style="height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;" />
              </div>
            </div>
          </div>
        </div>

        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label for="location" class="required-field mb-0"
                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                所屬區域
              </label>
              <div class="flex-grow-1 ml-3">
                <input type="text" id="location" class="form-control" nbInput placeholder="請輸入所屬區域"
                  [(ngModel)]="spaceDetail.CLocation" name="location"
                  style="height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;" />
              </div>
            </div>
          </div>
        </div>

        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label for="status" class="required-field mb-0"
                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                狀態
              </label>
              <div class="flex-grow-1 ml-3">
                <nb-form-field class="w-full">
                  <nb-select id="status" [(ngModel)]="spaceDetail.CStatus" name="status" placeholder="選擇狀態"
                    style="height: 42px;">
                    <nb-option [value]="1">
                      <span class="d-flex align-items-center">
                        <i class="fas fa-check-circle text-success me-2"></i>啟用
                      </span>
                    </nb-option>
                    <nb-option [value]="0">
                      <span class="d-flex align-items-center">
                        <i class="fas fa-times-circle text-danger me-2"></i>停用
                      </span>
                    </nb-option>
                  </nb-select>
                </nb-form-field>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nb-card-body>

    <nb-card-footer class="d-flex justify-content-end border-top pt-3 px-4 pb-3" style="background-color: #f8f9fa;">
      <button class="btn btn-outline-secondary me-3 px-4" (click)="onClose(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px;">
        <i class="fas fa-times me-1"></i>取消
      </button>
      <button class="btn btn-primary px-4" (click)="onSubmit(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);">
        <i class="fas fa-check me-1"></i>確認
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<!-- 編輯模態框 -->
<ng-template #editModal let-ref="dialogRef">
  <nb-card style="width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);">
    <nb-card-header class="d-flex justify-content-between align-items-center border-bottom py-3 px-4">
      <h5 class="mb-0 text-primary font-weight-bold">
        <i class="fas fa-edit me-2 text-warning"></i>編輯空間
      </h5>
      <button type="button" class="btn btn-ghost-light btn-sm rounded-circle" (click)="onClose(ref)"
        style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
        <i class="fas fa-times"></i>
      </button>
    </nb-card-header>

    <nb-card-body class="px-4 py-4">
      <div class="row">
        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label for="locationEdit" class="required-field mb-0"
                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                所屬區域
              </label>
              <div class="flex-grow-1 ml-3">
                <input type="text" id="locationEdit" class="form-control" nbInput placeholder="請輸入所屬區域"
                  [(ngModel)]="spaceDetail.CLocation" name="location"
                  style="height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;" />
              </div>
            </div>
          </div>
        </div>

        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label for="spaceNameEdit" class="required-field mb-0"
                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                項目名稱
              </label>
              <div class="flex-grow-1 ml-3">
                <input type="text" id="spaceNameEdit" class="form-control" nbInput placeholder="請輸入項目名稱"
                  [(ngModel)]="spaceDetail.CPart" name="spaceName"
                  style="height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;" />
              </div>
            </div>
          </div>
        </div>

        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label for="statusEdit" class="required-field mb-0"
                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                狀態
              </label>
              <div class="flex-grow-1 ml-3">
                <nb-form-field class="w-full">
                  <nb-select id="statusEdit" [(ngModel)]="spaceDetail.CStatus" name="status" placeholder="選擇狀態"
                    style="height: 42px;">
                    <nb-option [value]="1">
                      <span class="d-flex align-items-center">
                        <i class="fas fa-check-circle text-success me-2"></i>啟用
                      </span>
                    </nb-option>
                    <nb-option [value]="0">
                      <span class="d-flex align-items-center">
                        <i class="fas fa-times-circle text-danger me-2"></i>停用
                      </span>
                    </nb-option>
                  </nb-select>
                </nb-form-field>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nb-card-body>

    <nb-card-footer class="d-flex justify-content-end border-top pt-3 px-4 pb-3" style="background-color: #f8f9fa;">
      <button class="btn btn-outline-secondary me-3 px-4" (click)="onClose(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px;">
        <i class="fas fa-times me-1"></i>取消
      </button>
      <button class="btn btn-primary px-4" (click)="onSubmit(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);">
        <i class="fas fa-save me-1"></i>確認
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>