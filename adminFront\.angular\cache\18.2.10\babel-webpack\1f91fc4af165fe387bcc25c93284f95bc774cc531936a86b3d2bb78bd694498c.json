{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_nb_option_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r3, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_28_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", floor_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", floor_r7, \"F \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_28_button_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_28_button_37_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.clearAllFilters());\n    });\n    i0.ɵɵelement(1, \"i\", 62);\n    i0.ɵɵtext(2, \"\\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"div\", 45)(3, \"nb-form-field\");\n    i0.ɵɵelement(4, \"nb-icon\", 46);\n    i0.ɵɵelementStart(5, \"input\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_28_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.searchKeyword, $event) || (ctx_r4.filterOptions.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_28_Template_input_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 48)(7, \"nb-select\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.statusFilter, $event) || (ctx_r4.filterOptions.statusFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_selectedChange_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(8, \"nb-option\", 21);\n    i0.ɵɵtext(9, \"\\u5168\\u90E8\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-option\", 50);\n    i0.ɵɵtext(11, \"\\u9032\\u884C\\u4E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-option\", 51);\n    i0.ɵɵtext(13, \"\\u5F85\\u958B\\u653E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-option\", 52);\n    i0.ɵɵtext(15, \"\\u5DF2\\u904E\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-option\", 53);\n    i0.ɵɵtext(17, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-option\", 54);\n    i0.ɵɵtext(19, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 48)(21, \"nb-select\", 55);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.floorFilter, $event) || (ctx_r4.filterOptions.floorFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_selectedChange_21_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(22, \"nb-option\", 21);\n    i0.ɵɵtext(23, \"\\u5168\\u90E8\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SettingTimePeriodComponent_nb_card_23_div_28_nb_option_24_Template, 2, 2, \"nb-option\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 56)(26, \"nb-select\", 57);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.pageSize, $event) || (ctx_r4.pageSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_selectedChange_26_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onPageSizeChange());\n    });\n    i0.ɵɵelementStart(27, \"nb-option\", 23);\n    i0.ɵɵtext(28, \"50\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-option\", 23);\n    i0.ɵɵtext(30, \"100\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"nb-option\", 23);\n    i0.ɵɵtext(32, \"200\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-option\", 23);\n    i0.ɵɵtext(34, \"500\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 58)(36, \"div\", 59);\n    i0.ɵɵtemplate(37, SettingTimePeriodComponent_nb_card_23_div_28_button_37_Template, 3, 0, \"button\", 60);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.statusFilter);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.floorFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.availableFloors);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 50);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 200);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 500);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.hasActiveFilters());\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_29_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 105);\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵtext(2, \" \\u5DF2\\u9078 \");\n    i0.ɵɵelementStart(3, \"strong\", 107);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7B46 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedHouses.length);\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_29_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_div_32_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.openBatchSetting());\n    });\n    i0.ɵɵelement(2, \"i\", 110);\n    i0.ɵɵtext(3, \"\\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵelementStart(4, \"span\", 111);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_div_32_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.clearSelection());\n    });\n    i0.ɵɵelement(7, \"i\", 62);\n    i0.ɵɵtext(8, \"\\u6E05\\u9664\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedHouses.length);\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_29_tr_74_small_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 130);\n    i0.ɵɵtext(1, \"\\u7121\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 131);\n    i0.ɵɵelement(1, \"i\", 132);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 1, house_r12.CChangeStartDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 133);\n    i0.ɵɵelement(1, \"i\", 77);\n    i0.ɵɵtext(2, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 131);\n    i0.ɵɵelement(1, \"i\", 134);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 1, house_r12.CChangeEndDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 133);\n    i0.ɵɵelement(1, \"i\", 77);\n    i0.ɵɵtext(2, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_29_tr_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 113)(2, \"nb-checkbox\", 114);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_29_tr_74_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const house_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r12.selected, $event) || (house_r12.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_29_tr_74_Template_nb_checkbox_ngModelChange_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 115)(5, \"span\", 116);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SettingTimePeriodComponent_nb_card_23_div_29_tr_74_small_7_Template, 2, 0, \"small\", 117);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\")(9, \"span\", 118);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\", 113)(12, \"span\", 119);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\")(15, \"div\", 120);\n    i0.ɵɵtemplate(16, SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_16_Template, 4, 4, \"span\", 121)(17, SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_17_Template, 3, 0, \"span\", 122);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"div\", 120);\n    i0.ɵɵtemplate(20, SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_20_Template, 4, 4, \"span\", 121)(21, SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_21_Template, 3, 0, \"span\", 122);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"td\", 113)(23, \"div\", 123)(24, \"span\", 124);\n    i0.ɵɵelement(25, \"i\", 125);\n    i0.ɵɵelementStart(26, \"span\", 126);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"td\", 113)(29, \"div\", 127)(30, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_tr_74_Template_button_click_30_listener() {\n      const house_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      const dialog_r13 = i0.ɵɵreference(29);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r13, house_r12));\n    });\n    i0.ɵɵelement(31, \"i\", 129);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const house_r12 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"table-row-selected\", house_r12.selected)(\"table-row-disabled\", !house_r12.CHouseId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r12.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r12.CHouseId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(house_r12.CHouseHold);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r12.CHouseId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(house_r12.CBuildingName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", house_r12.CFloor, \"F\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", house_r12.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r12.CChangeStartDate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", house_r12.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r12.CChangeEndDate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r12));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r4.getStatusIcon(house_r12));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getStatusText(house_r12));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !house_r12.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_29_div_75_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 140)(1, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_div_75_li_21_Template_button_click_1_listener() {\n      const page_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.goToPage(page_r16));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r16 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"active\", page_r16 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r16);\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"div\", 65)(2, \"div\", 136)(3, \"span\", 89);\n    i0.ɵɵtext(4, \" \\u7B2C \");\n    i0.ɵɵelementStart(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" \\u9801\\uFF0C\\u5171 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" \\u9801 \");\n    i0.ɵɵelementStart(11, \"span\", 137);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"nav\", 138)(14, \"ul\", 139)(15, \"li\", 140)(16, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(1));\n    });\n    i0.ɵɵelement(17, \"i\", 142);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\", 140)(19, \"button\", 143);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage - 1));\n    });\n    i0.ɵɵelement(20, \"i\", 144);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, SettingTimePeriodComponent_nb_card_23_div_29_div_75_li_21_Template, 3, 3, \"li\", 145);\n    i0.ɵɵelementStart(22, \"li\", 140)(23, \"button\", 146);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage + 1));\n    });\n    i0.ɵɵelement(24, \"i\", 147);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"li\", 140)(26, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.totalPages));\n    });\n    i0.ɵɵelement(27, \"i\", 149);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"div\", 150)(29, \"div\", 151)(30, \"input\", 152);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.jumpToPage, $event) || (ctx_r4.jumpToPage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_input_keyup_enter_30_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.jumpToPageAction());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 153);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.jumpToPageAction());\n    });\n    i0.ɵɵelement(32, \"i\", 154);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r4.currentPage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.totalPages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" (\\u986F\\u793A\\u7B2C \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" \\u7B46\\uFF0C \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46) \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.jumpToPage);\n    i0.ɵɵproperty(\"min\", 1)(\"max\", ctx_r4.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64)(2, \"div\", 65)(3, \"div\", 66)(4, \"span\", 67);\n    i0.ɵɵelement(5, \"i\", 68);\n    i0.ɵɵtext(6, \" \\u5171 \");\n    i0.ɵɵelementStart(7, \"strong\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, SettingTimePeriodComponent_nb_card_23_div_29_span_10_Template, 6, 1, \"span\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 70)(12, \"div\", 71)(13, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"active\"));\n    });\n    i0.ɵɵelement(14, \"i\", 73);\n    i0.ɵɵtext(15, \"\\u9032\\u884C\\u4E2D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"pending\"));\n    });\n    i0.ɵɵelement(17, \"i\", 74);\n    i0.ɵɵtext(18, \"\\u5F85\\u958B\\u653E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"expired\"));\n    });\n    i0.ɵɵelement(20, \"i\", 75);\n    i0.ɵɵtext(21, \"\\u5DF2\\u904E\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"not-set\"));\n    });\n    i0.ɵɵelement(23, \"i\", 77);\n    i0.ɵɵtext(24, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(25, \"div\", 78)(26, \"div\", 65)(27, \"div\", 79)(28, \"div\", 80)(29, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_nb_checkbox_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_nb_checkbox_ngModelChange_29_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementStart(30, \"span\", 82);\n    i0.ɵɵtext(31, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, SettingTimePeriodComponent_nb_card_23_div_29_div_32_Template, 9, 1, \"div\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 84)(34, \"div\", 85)(35, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.exportData());\n    });\n    i0.ɵɵelement(36, \"i\", 87);\n    i0.ɵɵtext(37, \"\\u532F\\u51FA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 88)(39, \"small\", 89);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(41, \"div\", 90)(42, \"table\", 91)(43, \"thead\", 92)(44, \"tr\")(45, \"th\", 93)(46, \"nb-checkbox\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_nb_checkbox_ngModelChange_46_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_nb_checkbox_ngModelChange_46_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"th\", 95);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_th_click_47_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.sort(\"CHouseHold\"));\n    });\n    i0.ɵɵelementStart(48, \"div\", 96);\n    i0.ɵɵtext(49, \" \\u6236\\u578B \");\n    i0.ɵɵelement(50, \"i\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"th\", 98);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_th_click_51_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.sort(\"CBuildingName\"));\n    });\n    i0.ɵɵelementStart(52, \"div\", 96);\n    i0.ɵɵtext(53, \" \\u68DF\\u5225 \");\n    i0.ɵɵelement(54, \"i\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"th\", 99);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_th_click_55_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.sort(\"CFloor\"));\n    });\n    i0.ɵɵelementStart(56, \"div\", 96);\n    i0.ɵɵtext(57, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelement(58, \"i\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"th\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_th_click_59_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeStartDate\"));\n    });\n    i0.ɵɵelementStart(60, \"div\", 96);\n    i0.ɵɵtext(61, \" \\u958B\\u59CB\\u65E5\\u671F \");\n    i0.ɵɵelement(62, \"i\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"th\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_div_29_Template_th_click_63_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeEndDate\"));\n    });\n    i0.ɵɵelementStart(64, \"div\", 96);\n    i0.ɵɵtext(65, \" \\u7D50\\u675F\\u65E5\\u671F \");\n    i0.ɵɵelement(66, \"i\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"th\", 101)(68, \"div\", 96);\n    i0.ɵɵtext(69, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"th\", 102)(71, \"div\", 96);\n    i0.ɵɵtext(72, \" \\u64CD\\u4F5C \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(73, \"tbody\");\n    i0.ɵɵtemplate(74, SettingTimePeriodComponent_nb_card_23_div_29_tr_74_Template, 32, 20, \"tr\", 103);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(75, SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template, 33, 21, \"div\", 104);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.filteredHouses.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r4.filterOptions.statusFilter === \"active\")(\"btn-outline-primary\", ctx_r4.filterOptions.statusFilter !== \"active\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-warning\", ctx_r4.filterOptions.statusFilter === \"pending\")(\"btn-outline-warning\", ctx_r4.filterOptions.statusFilter !== \"pending\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-danger\", ctx_r4.filterOptions.statusFilter === \"expired\")(\"btn-outline-danger\", ctx_r4.filterOptions.statusFilter !== \"expired\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-not-set-active\", ctx_r4.filterOptions.statusFilter === \"not-set\")(\"btn-not-set-outline\", ctx_r4.filterOptions.statusFilter !== \"not-set\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.filteredHouses.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" / \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46 \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.paginatedHouses)(\"ngForTrackBy\", ctx_r4.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 156)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 89);\n    i0.ɵɵelement(4, \"i\", 157);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\\u5F8C\\u67E5\\u8A62\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 156)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 89)(4, \"div\", 158)(5, \"span\", 159);\n    i0.ɵɵtext(6, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 160);\n    i0.ɵɵtext(8, \"\\u8CC7\\u6599\\u8F09\\u5165\\u4E2D\\uFF0C\\u8ACB\\u7A0D\\u5019...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_nb_card_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 24)(1, \"nb-card-body\", 25)(2, \"div\", 26)(3, \"div\", 27)(4, \"div\", 13)(5, \"div\", 28)(6, \"label\", 15);\n    i0.ɵɵtext(7, \"\\u958B\\u653E\\u65E5\\u671F\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 29)(9, \"nb-form-field\");\n    i0.ɵɵelement(10, \"nb-icon\", 30);\n    i0.ɵɵelementStart(11, \"input\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.searchQuery.CChangeStartDate, $event) || (ctx_r4.searchQuery.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"nb-datepicker\", 32, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 33);\n    i0.ɵɵtext(15, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-form-field\");\n    i0.ɵɵelement(17, \"nb-icon\", 30);\n    i0.ɵɵelementStart(18, \"input\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_nb_card_23_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.searchQuery.CChangeEndDate, $event) || (ctx_r4.searchQuery.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"nb-datepicker\", 32, 4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 19)(22, \"div\", 35)(23, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.getHouseChangeDate());\n    });\n    i0.ɵɵelement(24, \"i\", 37);\n    i0.ɵɵtext(25, \"\\u67E5\\u8A62 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_nb_card_23_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.resetFilters());\n    });\n    i0.ɵɵelement(27, \"i\", 39);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_nb_card_23_div_28_Template, 38, 10, \"div\", 40)(29, SettingTimePeriodComponent_nb_card_23_div_29_Template, 76, 48, \"div\", 41)(30, SettingTimePeriodComponent_nb_card_23_div_30_Template, 7, 0, \"div\", 42)(31, SettingTimePeriodComponent_nb_card_23_div_31_Template, 9, 0, \"div\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const StartDate_r17 = i0.ɵɵreference(13);\n    const EndDate_r18 = i0.ɵɵreference(20);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"nbDatepicker\", StartDate_r17);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.searchQuery.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", EndDate_r18);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.searchQuery.CChangeEndDate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.loading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.loading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.flattenedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.flattenedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.flattenedHouses.length === 0 && ctx_r4.houseChangeDates.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.loading);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \\u5DF2\\u9078\\u64C7 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225 \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r4.selectedBuildingForBatch.name, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_div_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 179);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", house_r20.CHouseHold, \" (\", house_r20.CBuildingName, \"-\", house_r20.CFloor, \"F) \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_div_28_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 89);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" ...\\u7B49 \", ctx_r4.selectedHouses.length - 10, \" \\u500B \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 175)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 176);\n    i0.ɵɵtemplate(4, SettingTimePeriodComponent_ng_template_24_div_28_span_4_Template, 2, 3, \"span\", 177)(5, SettingTimePeriodComponent_ng_template_24_div_28_span_5_Template, 2, 1, \"span\", 178);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5C07\\u5957\\u7528\\u5230\\u5DF2\\u9078\\u64C7\\u7684 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedHouses.slice(0, 10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 10);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_div_3_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 114);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      const house_r25 = i0.ɵɵrestoreView(_r24).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r25.selected, $event) || (house_r25.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r25 = ctx.$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r25.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r25.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r25.CHouseHold, \" (\", house_r25.CBuildingName, \") \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 184);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_div_3_nb_checkbox_1_Template, 2, 4, \"nb-checkbox\", 185);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", floor_r23.houses);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 182)(1, \"nb-checkbox\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const floor_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      i0.ɵɵtwoWayBindingSet(floor_r23.selected, $event) || (floor_r23.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      const floor_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.onFloorSelectionChange(floor_r23));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_div_3_Template, 2, 1, \"div\", 183);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r23 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", floor_r23.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r23.floorNumber, \"F (\", floor_r23.houses.length, \" \\u6236) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", floor_r23.selected);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_div_29_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 160);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_Template, 4, 4, \"div\", 181);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedBuildingForBatch.floors);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"nb-checkbox\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_24_div_29_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.applyToAll, $event) || (ctx_r4.batchSettings.applyToAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_24_div_29_div_3_Template, 2, 1, \"div\", 180);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.applyToAll);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u90E8\\u6236\\u5225 (\", ctx_r4.flattenedHouses.length, \" \\u500B) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.batchSettings.applyToAll && ctx_r4.selectedBuildingForBatch);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225)\");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 161)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_24_span_3_Template, 2, 1, \"span\", 162)(4, SettingTimePeriodComponent_ng_template_24_span_4_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"div\", 164)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n    i0.ɵɵelementStart(9, \"span\", 165);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 166)(12, \"nb-form-field\", 167);\n    i0.ɵɵelement(13, \"nb-icon\", 30);\n    i0.ɵɵelementStart(14, \"input\", 168);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_24_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.startDate, $event) || (ctx_r4.batchSettings.startDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 32, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 169);\n    i0.ɵɵtext(18, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\", 167);\n    i0.ɵɵelement(20, \"nb-icon\", 30);\n    i0.ɵɵelementStart(21, \"input\", 168);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_24_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.endDate, $event) || (ctx_r4.batchSettings.endDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 32, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 164)(25, \"label\");\n    i0.ɵɵtext(26, \"\\u9069\\u7528\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 170);\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_ng_template_24_div_28_Template, 6, 3, \"div\", 171)(29, SettingTimePeriodComponent_ng_template_24_div_29_Template, 4, 3, \"div\", 163);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-card-footer\", 172)(31, \"button\", 173);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_24_Template_button_click_31_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r26));\n    });\n    i0.ɵɵtext(32, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 174);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_24_Template_button_click_33_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onBatchSubmit(ref_r26));\n    });\n    i0.ɵɵtext(34, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(35, SettingTimePeriodComponent_ng_template_24_span_35_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const batchStartDate_r27 = i0.ɵɵreference(16);\n    const batchEndDate_r28 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedBuildingForBatch && ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", batchStartDate_r27);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.startDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", batchEndDate_r28);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.endDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 186);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 187);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 186)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 188);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 189)(7, \"div\", 190)(8, \"label\", 191);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 165);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 192);\n    i0.ɵɵelement(13, \"nb-icon\", 30);\n    i0.ɵɵelementStart(14, \"input\", 193);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_28_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 32, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 194);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 30);\n    i0.ɵɵelementStart(21, \"input\", 195);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_28_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 32, 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 187)(25, \"button\", 196);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_28_Template_button_click_25_listener() {\n      const ref_r30 = i0.ɵɵrestoreView(_r29).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r30));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 197);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_28_Template_button_click_27_listener() {\n      const ref_r30 = i0.ɵɵrestoreView(_r29).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSubmit(ref_r30));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r31 = i0.ɵɵreference(16);\n    const changeEndDate_r32 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r4.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r31);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r32);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    // 新增的屬性\n    this.buildingGroups = [];\n    this.buildingOptions = [];\n    this.selectedBuilding = '';\n    this.availableFloors = [];\n    // 篩選和搜尋\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 批次設定\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: true,\n      selectedBuildings: [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    this.selectedBuildingForBatch = null;\n    // 表格視圖相關屬性\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.loading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalPages = 1;\n    // 排序相關\n    this.sortField = '';\n    this.sortDirection = 'asc';\n    // 數學函數引用\n    this.Math = Math;\n    // 新增的UI控制屬性\n    this.jumpToPage = 1;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  // 新增的UI控制方法\n  hasActiveFilters() {\n    return !!(this.filterOptions.searchKeyword || this.filterOptions.statusFilter || this.filterOptions.floorFilter);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.filterOptions.searchKeyword) count++;\n    if (this.filterOptions.statusFilter) count++;\n    if (this.filterOptions.floorFilter) count++;\n    return count;\n  }\n  resetFilters() {\n    this.searchQuery = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.selectedBuilding = '';\n    this.clearAllFilters();\n  }\n  clearAllFilters() {\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    this.onSearch();\n  }\n  setQuickFilter(status) {\n    if (this.filterOptions.statusFilter === status) {\n      this.filterOptions.statusFilter = '';\n    } else {\n      this.filterOptions.statusFilter = status;\n    }\n    this.onSearch();\n  }\n  clearSelection() {\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.flattenedHouses.forEach(house => house.selected = false);\n  }\n  getStatusIcon(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return 'fas fa-play-circle';\n      case 'pending':\n        return 'fas fa-clock';\n      case 'expired':\n        return 'fas fa-times-circle';\n      case 'not-set':\n        return 'fas fa-exclamation-triangle';\n      case 'disabled':\n        return 'fas fa-ban';\n      default:\n        return 'fas fa-exclamation-triangle';\n    }\n  }\n  jumpToPageAction() {\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\n      this.goToPage(this.jumpToPage);\n    }\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CBuildingName: '未分類',\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  // 新增：建案變更處理\n  onBuildCaseChange() {\n    // 重置所有相關狀態\n    this.resetAllStates();\n    // 執行查詢\n    this.getHouseChangeDate();\n  }\n  // 新增：重置所有狀態\n  resetAllStates() {\n    // 重置數據\n    this.houseChangeDates = [];\n    this.convertedHouseArray = [];\n    this.buildingGroups = [];\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    // 重置篩選條件\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 重置選擇狀態\n    this.selectAll = false;\n    this.selectedBuilding = '';\n    // 重置分頁\n    this.currentPage = 1;\n    this.totalPages = 1;\n    // 重置可用選項\n    this.buildingOptions = [];\n    this.availableFloors = [];\n    // 重置排序\n    this.sortField = '';\n    this.sortDirection = 'asc';\n  }\n  getHouseChangeDate() {\n    // 如果沒有選擇建案，直接返回\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\n      this.loading = false;\n      return;\n    }\n    this.loading = true;\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      this.loading = false;\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          // 新增：建立棟別分組資料\n          this.buildBuildingGroups(res.Entries);\n          // 新增：建立扁平化資料\n          this.buildFlattenedHouses(res.Entries);\n        }\n      }\n    });\n  }\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n  buildBuildingGroups(data) {\n    const buildingMap = new Map();\n    data.forEach(household => {\n      const houseType = household.CHouseHold || ''; // 戶型\n      household.CHouses?.forEach(house => {\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\n        const floor = house.CFloor || 0;\n        if (!buildingMap.has(buildingName)) {\n          buildingMap.set(buildingName, new Map());\n        }\n        const floorMap = buildingMap.get(buildingName);\n        if (!floorMap.has(floor)) {\n          floorMap.set(floor, []);\n        }\n        floorMap.get(floor).push({\n          CHouseHold: houseType,\n          // 戶型\n          CBuildingName: buildingName,\n          // 棟別\n          CHouseId: house.CHouseId || 0,\n          CFloor: floor,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 轉換為BuildingGroup格式\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n      const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n      .map(([floorNumber, houses]) => ({\n        floorNumber,\n        houses: houses.sort((a, b) => {\n          // 排序邏輯：先按戶型排序，再按樓層排序\n          if (a.CHouseHold !== b.CHouseHold) {\n            return a.CHouseHold.localeCompare(b.CHouseHold);\n          }\n          return a.CFloor - b.CFloor;\n        }),\n        selected: false\n      }));\n      return {\n        name: buildingName,\n        floors,\n        selected: false\n      };\n    }).sort((a, b) => a.name.localeCompare(b.name));\n    // 更新棟別選項和可用樓層\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n    this.updateAvailableFloors();\n  }\n  // 新增：更新可用樓層\n  updateAvailableFloors() {\n    const floorsSet = new Set();\n    this.buildingGroups.forEach(building => {\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n        building.floors.forEach(floor => {\n          floorsSet.add(floor.floorNumber);\n        });\n      }\n    });\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n  }\n  // 修改：棟別選擇變更處理\n  onBuildingChange() {\n    // 重置選擇狀態\n    this.selectedHouses.forEach(house => house.selected = false);\n    this.selectedHouses = [];\n    this.selectAll = false;\n    // 重置分頁到第一頁\n    this.currentPage = 1;\n    // 重置樓層篩選\n    this.filterOptions.floorFilter = '';\n    // 更新可用樓層\n    this.updateAvailableFloors();\n    // 設定棟別篩選並執行搜尋\n    this.filterOptions.buildingFilter = this.selectedBuilding;\n    this.onSearch();\n  }\n  // 新增：取得過濾後的棟別資料\n  getFilteredBuildings() {\n    return this.buildingGroups.filter(building => {\n      // 棟別篩選\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n        return false;\n      }\n      // 關鍵字搜尋 (搜尋戶型)\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n        if (!hasMatchingHouse) {\n          return false;\n        }\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n        if (!hasMatchingStatus) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n        if (!hasMatchingFloor) {\n          return false;\n        }\n      }\n      return true;\n    }).map(building => {\n      // 對每個棟別，也要篩選其樓層和戶別\n      const filteredBuilding = {\n        ...building\n      };\n      filteredBuilding.floors = building.floors.filter(floor => {\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (floor.floorNumber !== floorNumber) {\n            return false;\n          }\n        }\n        // 檢查該樓層是否有符合條件的戶別\n        const hasValidHouses = floor.houses.some(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return hasValidHouses;\n      }).map(floor => {\n        // 篩選戶別\n        const filteredFloor = {\n          ...floor\n        };\n        filteredFloor.houses = floor.houses.filter(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return filteredFloor;\n      });\n      return filteredBuilding;\n    });\n  }\n  // 修復：檢查戶別是否符合狀態篩選\n  matchesStatusFilter(house) {\n    const status = this.getHouseStatus(house);\n    switch (this.filterOptions.statusFilter) {\n      case 'active':\n        return status === 'active';\n      case 'pending':\n        return status === 'pending';\n      case 'expired':\n        return status === 'expired';\n      case 'not-set':\n        return status === 'not-set';\n      case 'disabled':\n        return status === 'disabled';\n      default:\n        return true;\n      // 全部狀態\n    }\n  }\n  // 修復：取得戶別狀態\n  getHouseStatus(house) {\n    if (!house.CHouseId) {\n      return 'disabled';\n    }\n    // 檢查是否有設定開放時段\n    if (!house.CChangeStartDate || !house.CChangeEndDate || house.CChangeStartDate === '' || house.CChangeEndDate === '') {\n      return 'not-set';\n    }\n    try {\n      // 處理日期字串，支援多種格式\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      // 解析開始日期\n      let startDate;\n      if (house.CChangeStartDate.includes('T')) {\n        startDate = new Date(house.CChangeStartDate);\n      } else {\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\n      }\n      // 解析結束日期\n      let endDate;\n      if (house.CChangeEndDate.includes('T')) {\n        endDate = new Date(house.CChangeEndDate);\n      } else {\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\n      }\n      // 檢查日期有效性\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\n        console.warn('Invalid date format:', {\n          start: house.CChangeStartDate,\n          end: house.CChangeEndDate,\n          houseId: house.CHouseId\n        });\n        return 'not-set';\n      }\n      // 轉換為日期比較（不含時間）\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n      // 判斷狀態\n      if (today < startDateOnly) {\n        return 'pending'; // 待開放\n      } else if (today >= startDateOnly && today <= endDateOnly) {\n        return 'active'; // 進行中\n      } else {\n        return 'expired'; // 已過期\n      }\n    } catch (error) {\n      console.error('Error parsing dates:', error, house);\n      return 'not-set';\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  // 修改：開啟批次設定對話框\n  openBatchSetting(building) {\n    this.selectedBuildingForBatch = building || null;\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\n    const hasSelectedHouses = this.selectedHouses.length > 0;\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: !hasSelectedHouses && !building,\n      selectedBuildings: building ? [building.name] : [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    // 重置選擇狀態\n    if (building) {\n      building.floors.forEach(floor => {\n        floor.selected = false;\n        floor.houses.forEach(house => house.selected = false);\n      });\n    }\n    // 開啟對話框\n    this.dialogService.open(this.batchSettingDialog);\n  }\n  // 新增：樓層選擇變更處理\n  onFloorSelectionChange(floor) {\n    if (floor.selected) {\n      floor.houses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = true;\n        }\n      });\n    } else {\n      floor.houses.forEach(house => house.selected = false);\n    }\n  }\n  // 修改：批次提交\n  onBatchSubmit(ref) {\n    // 驗證批次設定\n    this.valid.clear();\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 收集要更新的房屋\n    const housesToUpdate = [];\n    if (this.batchSettings.applyToAll) {\n      // 全部戶別\n      this.flattenedHouses.forEach(house => {\n        if (house.CHouseId) {\n          housesToUpdate.push({\n            CHouseId: house.CHouseId,\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n          });\n        }\n      });\n    } else {\n      // 使用已選擇的戶別\n      if (this.selectedHouses.length > 0) {\n        this.selectedHouses.forEach(house => {\n          if (house.CHouseId) {\n            housesToUpdate.push({\n              CHouseId: house.CHouseId,\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n            });\n          }\n        });\n      } else if (this.selectedBuildingForBatch) {\n        // 如果沒有選擇的戶別，使用舊的邏輯\n        this.selectedBuildingForBatch.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.selected && house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      }\n    }\n    if (housesToUpdate.length === 0) {\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\n      return;\n    }\n    // 調用API進行批次更新\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: housesToUpdate\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n        // 清除選擇狀態\n        this.selectedHouses.forEach(house => house.selected = false);\n        this.selectedHouses = [];\n        this.selectAll = false;\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  // 新增：取得狀態樣式類別\n  getStatusClass(house) {\n    const status = this.getHouseStatus(house);\n    return `status-${status}`;\n  }\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n  openHouseDialog(house) {\n    if (house.CHouseId) {\n      // 使用現有的openModel方法\n      this.openModel(this.dialog, house);\n    }\n  }\n  // 新增：建立扁平化房屋資料\n  buildFlattenedHouses(data) {\n    this.flattenedHouses = [];\n    data.forEach(household => {\n      const houseType = household.CHouseHold || '';\n      household.CHouses?.forEach(house => {\n        this.flattenedHouses.push({\n          CHouseHold: houseType,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId || 0,\n          CFloor: house.CFloor || 0,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 初始化篩選和分頁\n    this.onSearch();\n    // 調試：輸出狀態統計\n    this.debugStatusCounts();\n  }\n  // 調試：輸出狀態統計\n  debugStatusCounts() {\n    const statusCounts = {\n      active: 0,\n      pending: 0,\n      expired: 0,\n      'not-set': 0,\n      disabled: 0\n    };\n    this.flattenedHouses.forEach(house => {\n      const status = this.getHouseStatus(house);\n      if (statusCounts.hasOwnProperty(status)) {\n        statusCounts[status]++;\n      }\n    });\n    console.log('狀態統計:', statusCounts);\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\n  }\n  // 修改：搜尋和篩選\n  onSearch() {\n    // 記錄篩選前的已選擇項目\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\n    this.filteredHouses = this.flattenedHouses.filter(house => {\n      // 關鍵字搜尋\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n          return false;\n        }\n      }\n      // 棟別篩選\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n        return false;\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        if (!this.matchesStatusFilter(house)) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        if (house.CFloor !== floorNumber) {\n          return false;\n        }\n      }\n      return true;\n    });\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\n    this.selectedHouses = this.filteredHouses.filter(house => previouslySelectedIds.includes(house.CHouseId));\n    // 更新扁平化資料中的選擇狀態\n    this.flattenedHouses.forEach(house => {\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\n    });\n    // 更新全選狀態\n    this.updateSelectAllState();\n    // 重新計算分頁\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：更新全選狀態\n  updateSelectAllState() {\n    if (this.paginatedHouses.length === 0) {\n      this.selectAll = false;\n    } else {\n      this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n    }\n  }\n  // 修改：更新分頁\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n    // 更新全選狀態\n    this.updateSelectAllState();\n  }\n  // 新增：頁面大小變更\n  onPageSizeChange() {\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：跳轉頁面\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.updatePagination();\n    }\n  }\n  // 新增：取得可見頁碼\n  getVisiblePages() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 新增：全選/取消全選\n  onSelectAllChange() {\n    this.paginatedHouses.forEach(house => {\n      if (house.CHouseId) {\n        house.selected = this.selectAll;\n      }\n    });\n    this.updateSelectedHouses();\n  }\n  // 新增：單一選擇變更\n  onHouseSelectionChange() {\n    this.updateSelectedHouses();\n    this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n  }\n  // 新增：更新已選擇房屋列表\n  updateSelectedHouses() {\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n  }\n  // 新增：排序\n  sort(field) {\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    this.filteredHouses.sort((a, b) => {\n      let aValue = a[field];\n      let bValue = b[field];\n      // 處理日期排序\n      if (field.includes('Date')) {\n        aValue = aValue ? new Date(aValue).getTime() : 0;\n        bValue = bValue ? new Date(bValue).getTime() : 0;\n      }\n      // 處理數字排序\n      if (field === 'CFloor') {\n        aValue = Number(aValue) || 0;\n        bValue = Number(bValue) || 0;\n      }\n      // 處理字串排序\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (aValue < bValue) {\n        return this.sortDirection === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return this.sortDirection === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    this.updatePagination();\n  }\n  // 新增：TrackBy函數\n  trackByHouseId(_index, house) {\n    return house.CHouseId;\n  }\n  // 新增：取得狀態文字\n  getStatusText(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return '進行中';\n      case 'pending':\n        return '待開放';\n      case 'expired':\n        return '已過期';\n      case 'not-set':\n        return '未設定';\n      case 'disabled':\n        return '已停用';\n      default:\n        return '未知';\n    }\n  }\n  // 新增：匯出資料\n  exportData() {\n    // 實現匯出功能\n    const csvContent = this.generateCSV();\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  // 新增：產生CSV內容\n  generateCSV() {\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\n    const rows = this.filteredHouses.map(house => [house.CHouseHold, house.CBuildingName, `${house.CFloor}F`, house.CChangeStartDate || '未設定', house.CChangeEndDate || '未設定', this.getStatusText(house)]);\n    const csvContent = [headers, ...rows].map(row => row.map(cell => `\"${cell}\"`).join(',')).join('\\n');\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\n  }\n  static {\n    this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingTimePeriodComponent,\n      selectors: [[\"ngx-setting-time-period\"]],\n      viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 30,\n      vars: 5,\n      consts: [[\"batchSettingDialog\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"batchStartDate\", \"\"], [\"batchEndDate\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\", 1, \"mb-3\"], [1, \"page-description-section\"], [1, \"page-description\", \"text-muted\", \"mb-0\"], [1, \"basic-selection-area\"], [1, \"row\", \"g-3\", \"align-items-end\"], [1, \"col-lg-4\", \"col-md-6\"], [1, \"form-label\", \"fw-medium\"], [1, \"text-danger\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-3\", \"col-md-6\"], [\"placeholder\", \"\\u5168\\u90E8\\u68DF\\u5225\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"\"], [\"accent\", \"success\", 4, \"ngIf\"], [3, \"value\"], [\"accent\", \"success\"], [1, \"p-0\"], [1, \"filters-and-table-section\"], [1, \"date-query-section\"], [1, \"col-lg-5\", \"col-md-6\"], [1, \"date-range-group\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u958B\\u59CB\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [1, \"date-separator\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u7D50\\u675F\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"query-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-search\", \"me-1\"], [\"title\", \"\\u91CD\\u7F6E\\u7BE9\\u9078\\u689D\\u4EF6\", 1, \"btn\", \"btn-outline-secondary\", \"ms-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-undo\"], [\"class\", \"advanced-filters-section\", 4, \"ngIf\"], [\"class\", \"table-view-enhanced mt-4\", 4, \"ngIf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [1, \"advanced-filters-section\"], [1, \"row\", \"g-3\", \"align-items-center\"], [1, \"col-lg-3\", \"col-md-4\"], [\"nbPrefix\", \"\", \"icon\", \"search-outline\"], [\"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u578B\\u3001\\u68DF\\u5225...\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-lg-2\", \"col-md-3\"], [\"placeholder\", \"\\u72C0\\u614B\\u7BE9\\u9078\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"active\"], [\"value\", \"pending\"], [\"value\", \"expired\"], [\"value\", \"not-set\"], [\"value\", \"disabled\"], [\"placeholder\", \"\\u6A13\\u5C64\\u7BE9\\u9078\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-lg-2\", \"col-md-2\"], [\"placeholder\", \"\\u986F\\u793A\\u7B46\\u6578\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-lg-3\", \"col-md-12\"], [1, \"filter-actions\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"table-view-enhanced\", \"mt-4\"], [1, \"data-summary-bar\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"summary-info\"], [1, \"total-count\"], [1, \"fas\", \"fa-database\", \"me-1\"], [\"class\", \"selected-count\", 4, \"ngIf\"], [1, \"quick-filters\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [1, \"fas\", \"fa-play-circle\", \"me-1\"], [1, \"fas\", \"fa-clock\", \"me-1\"], [1, \"fas\", \"fa-times-circle\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-not-set\", 3, \"click\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-1\"], [1, \"enhanced-toolbar\"], [1, \"batch-operations\"], [1, \"selection-controls\"], [1, \"select-all-checkbox\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fw-medium\"], [\"class\", \"batch-actions ms-3\", 4, \"ngIf\"], [1, \"table-controls\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\"], [\"title\", \"\\u532F\\u51FA\\u8CC7\\u6599\", 1, \"btn\", \"btn-outline-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\", \"me-1\"], [1, \"pagination-summary\"], [1, \"text-muted\"], [1, \"enhanced-table-container\"], [1, \"table\", \"table-hover\", \"enhanced-table\"], [1, \"enhanced-table-header\"], [\"width\", \"50\", 1, \"text-center\"], [3, \"ngModelChange\", \"ngModel\"], [\"width\", \"120\", 1, \"sortable\", 3, \"click\"], [1, \"header-content\"], [1, \"fas\", \"fa-sort\", \"sort-icon\"], [\"width\", \"100\", 1, \"sortable\", 3, \"click\"], [\"width\", \"80\", 1, \"sortable\", \"text-center\", 3, \"click\"], [\"width\", \"140\", 1, \"sortable\", 3, \"click\"], [\"width\", \"120\", 1, \"text-center\"], [\"width\", \"100\", 1, \"text-center\"], [3, \"table-row-selected\", \"table-row-disabled\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"enhanced-pagination-container\", 4, \"ngIf\"], [1, \"selected-count\"], [1, \"fas\", \"fa-check-square\", \"me-1\", \"text-primary\"], [1, \"text-primary\"], [1, \"batch-actions\", \"ms-3\"], [\"title\", \"\\u6279\\u6B21\\u8A2D\\u5B9A\\u9078\\u4E2D\\u7684\\u6236\\u5225\\u958B\\u653E\\u6642\\u6BB5\", 1, \"btn\", \"btn-warning\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-cogs\", \"me-1\"], [1, \"badge\", \"bg-light\", \"text-dark\", \"ms-1\"], [\"title\", \"\\u6E05\\u9664\\u9078\\u64C7\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"ms-2\", 3, \"click\"], [1, \"text-center\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"house-info\"], [1, \"house-name\", \"fw-medium\"], [\"class\", \"text-muted d-block\", 4, \"ngIf\"], [1, \"building-name\"], [1, \"floor-badge\"], [1, \"date-info\"], [\"class\", \"date-display\", 4, \"ngIf\"], [\"class\", \"not-set-text\", 4, \"ngIf\"], [1, \"status-display\"], [1, \"enhanced-status-badge\"], [1, \"status-icon\"], [1, \"status-text\"], [1, \"action-buttons\"], [\"title\", \"\\u7DE8\\u8F2F\\u6642\\u6BB5\\u8A2D\\u5B9A\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-edit\"], [1, \"text-muted\", \"d-block\"], [1, \"date-display\"], [1, \"fas\", \"fa-calendar\", \"me-1\", \"text-success\"], [1, \"not-set-text\"], [1, \"fas\", \"fa-calendar\", \"me-1\", \"text-danger\"], [1, \"enhanced-pagination-container\"], [1, \"pagination-info-detailed\"], [1, \"ms-2\"], [1, \"pagination-nav\"], [1, \"pagination\", \"pagination-sm\", \"mb-0\"], [1, \"page-item\"], [\"title\", \"\\u7B2C\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [\"title\", \"\\u4E0A\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"\\u4E0B\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-right\"], [\"title\", \"\\u6700\\u5F8C\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"quick-jump\"], [1, \"input-group\", \"input-group-sm\", 2, \"width\", \"120px\"], [\"type\", \"number\", \"placeholder\", \"\\u9801\\u78BC\", 1, \"form-control\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"min\", \"max\"], [\"type\", \"button\", \"title\", \"\\u8DF3\\u8F49\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"mb-3\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"mt-2\"], [2, \"width\", \"600px\", \"max-height\", \"90vh\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"form-group\"], [1, \"text-red-600\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-fill\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"mx-2\"], [1, \"selection-options\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"alert\", \"alert-info\"], [1, \"selected-houses-preview\"], [\"class\", \"badge badge-primary mr-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"badge\", \"badge-primary\", \"mr-1\", \"mb-1\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"floor-selection\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-selection\"], [\"class\", \"house-selection ml-4\", 4, \"ngIf\"], [1, \"house-selection\", \"ml-4\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n      template: function SettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 9)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 10)(5, \"p\", 11);\n          i0.ɵɵtext(6, \"\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u9593\\u7BC4\\u570D\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 12)(8, \"div\", 13)(9, \"div\", 14)(10, \"label\", 15);\n          i0.ɵɵtext(11, \"\\u5EFA\\u6848 \");\n          i0.ɵɵelementStart(12, \"span\", 16);\n          i0.ɵɵtext(13, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"nb-select\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildCaseChange());\n          });\n          i0.ɵɵtemplate(15, SettingTimePeriodComponent_nb_option_15_Template, 2, 2, \"nb-option\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 19)(17, \"label\", 15);\n          i0.ɵɵtext(18, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"nb-select\", 20);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_19_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildingChange());\n          });\n          i0.ɵɵelementStart(20, \"nb-option\", 21);\n          i0.ɵɵtext(21, \"\\u5168\\u90E8\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, SettingTimePeriodComponent_nb_option_22_Template, 2, 2, \"nb-option\", 18);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(23, SettingTimePeriodComponent_nb_card_23_Template, 32, 10, \"nb-card\", 22)(24, SettingTimePeriodComponent_ng_template_24_Template, 36, 9, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(26, SettingTimePeriodComponent_ng_template_26_Template, 4, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(28, SettingTimePeriodComponent_ng_template_28_Template, 29, 6, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingOptions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searchQuery.CBuildCaseSelected);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.MinValidator, i9.MaxValidator, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n.page-header-optimized[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n  border-radius: 0.75rem;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.1);\\n  margin-bottom: 1.5rem;\\n  overflow: hidden;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-description-section[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-description-section[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #6c757d;\\n  line-height: 1.4;\\n  text-align: center;\\n  font-weight: 400;\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .basic-selection-area[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 1.5rem;\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .basic-selection-area[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 0.875rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.compact-filters[_ngcontent-%COMP%] {\\n  padding: 1.25rem 1.5rem;\\n  background: linear-gradient(to bottom, #fafafa 0%, #f8f9fa 100%);\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.15);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin-bottom: 0.375rem;\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545 !important;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0 0.25rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  justify-content: flex-end;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.3s ease;\\n  padding: 0.5rem 1rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #A69660 0%, #95854A 100%);\\n  border-color: #A69660;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(184, 166, 118, 0.3);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #6c757d;\\n  color: #6c757d;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n  transform: translateY(-1px);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.filters-and-table-section[_ngcontent-%COMP%]   .date-query-section[_ngcontent-%COMP%] {\\n  padding: 1.5rem 1.5rem 1rem;\\n  background-color: #ffffff;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.filters-and-table-section[_ngcontent-%COMP%]   .date-query-section[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 0.875rem;\\n  margin-bottom: 0.5rem;\\n  font-weight: 500;\\n}\\n.filters-and-table-section[_ngcontent-%COMP%]   .date-query-section[_ngcontent-%COMP%]   .query-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: end;\\n  height: 100%;\\n  padding-bottom: 0.25rem;\\n}\\n.filters-and-table-section[_ngcontent-%COMP%]   .date-query-section[_ngcontent-%COMP%]   .query-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.5rem 1rem;\\n}\\n.filters-and-table-section[_ngcontent-%COMP%]   .advanced-filters-section[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 1.25rem;\\n  background-color: #ffffff;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.filters-and-table-section[_ngcontent-%COMP%]   .advanced-filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n}\\n.filters-and-table-section[_ngcontent-%COMP%]   .advanced-filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .btn-outline-danger[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.advanced-filters[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 1.5rem;\\n  background-color: #ffffff;\\n  border-top: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]   .text-primary[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #B8A676;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%], \\n.advanced-filters[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-select.ng-touched.ng-valid[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.5);\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.25rem;\\n  border-radius: 0.5rem 0.5rem 0 0;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-bottom: none;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-warning[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-danger[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);\\n  border-color: #ffc107;\\n  color: #212529;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #856404;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border: 2px solid #ffc107;\\n  color: #856404;\\n  font-weight: 500;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\\n  border-color: #e0a800;\\n  color: #856404;\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(255, 193, 7, 0.2);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  color: #e0a800;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%] {\\n  background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);\\n  padding: 1rem 1.25rem;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-top: none;\\n  border-bottom: none;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  font-size: 0.875rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #D4B96A 0%, #C4A85A 100%);\\n  border-color: #D4B96A;\\n  color: #5a4a2a;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #C4A85A 0%, #B4984A 100%);\\n  border-color: #C4A85A;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  color: #B8A676;\\n  font-weight: 600;\\n  font-size: 0.7rem;\\n  padding: 0.2rem 0.4rem;\\n  border-radius: 0.25rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .pagination-summary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d;\\n  margin-left: 1rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(184, 166, 118, 0.2);\\n  border-radius: 0 0 0.5rem 0.5rem;\\n  overflow: hidden;\\n  box-shadow: 0 4px 8px rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  font-size: 0.875rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F8F6F0 0%, #F0EDE5 100%);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  border-bottom: 2px solid rgba(184, 166, 118, 0.2);\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .sort-icon[_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n  color: #6c757d;\\n  transition: opacity 0.3s ease;\\n  margin-left: 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover   .sort-icon[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  color: #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-selected[_ngcontent-%COMP%] {\\n  background-color: rgba(184, 166, 118, 0.15);\\n  border-left: 3px solid #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  background-color: #f8f9fa;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  vertical-align: middle;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .house-info[_ngcontent-%COMP%]   .house-name[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .house-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 500;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .floor-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);\\n  color: #495057;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 0.85rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .not-set-text[_ngcontent-%COMP%] {\\n  color: #856404;\\n  font-weight: 500;\\n  font-size: 0.85rem;\\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #ffc107;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .not-set-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 1rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.375rem;\\n  font-size: 0.7rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border-radius: 0.375rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: scale(1.1);\\n  box-shadow: 0 3px 6px rgba(0, 123, 255, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #E8F5E8 0%, #D4EDDA 100%);\\n  color: #2D5A2D;\\n  border-color: rgba(45, 90, 45, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-active[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-pending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FFF8E1 0%, #FFF3CD 100%);\\n  color: #8B6914;\\n  border-color: rgba(139, 105, 20, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-pending[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-expired[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FFEBEE 0%, #F8D7DA 100%);\\n  color: #8B2635;\\n  border-color: rgba(139, 38, 53, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-expired[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-not-set[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\\n  color: #856404;\\n  border-color: rgba(255, 193, 7, 0.4);\\n  font-weight: 600;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-not-set[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-disabled[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FAFAFA 0%, #F8F9FA 100%);\\n  color: #8A8A8A;\\n  border-color: rgba(138, 138, 138, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-disabled[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);\\n  padding: 1.25rem;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-top: none;\\n  border-radius: 0 0 0.5rem 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-info-detailed[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-info-detailed[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%] {\\n  margin: 0 0.125rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #495057;\\n  border-color: rgba(184, 166, 118, 0.3);\\n  transition: all 0.3s ease;\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  font-weight: 500;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: rgba(184, 166, 118, 0.5);\\n  color: #B8A676;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(184, 166, 118, 0.2);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n  box-shadow: 0 3px 6px rgba(184, 166, 118, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n  background-color: #fff;\\n  border-color: rgba(184, 166, 118, 0.2);\\n  cursor: not-allowed;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.3);\\n  font-size: 0.8rem;\\n  text-align: center;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.3);\\n  color: #495057;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n.search-enhanced[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #5a5a5a;\\n  border-color: rgba(184, 166, 118, 0.3);\\n  transition: all 0.2s ease;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: rgba(184, 166, 118, 0.5);\\n  color: #B8A676;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #aaa;\\n  background-color: #fff;\\n  border-color: rgba(184, 166, 118, 0.2);\\n}\\n\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  padding-left: 1rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\\n  gap: 0.25rem;\\n  margin-top: 0.5rem;\\n  padding: 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  margin-bottom: 1rem;\\n  border: 1px solid transparent;\\n  border-radius: 0.375rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%] {\\n  color: #0c5460;\\n  background-color: #d1ecf1;\\n  border-color: #bee5eb;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  font-weight: 600;\\n}\\n\\n@media (max-width: 992px) {\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n    justify-content: center;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    border-radius: 0.375rem 0.375rem 0 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    margin-top: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.75rem;\\n    text-align: left !important;\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .flex-fill[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .mx-2[_ngcontent-%COMP%] {\\n    margin: 0.25rem 0 !important;\\n    text-align: center;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n    margin-bottom: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 0.5rem;\\n  }\\n  .search-enhanced[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0.25rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(3) {\\n    display: none;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .integrated-header[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n    border-radius: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    border-radius: 0.25rem 0.25rem 0 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    margin-right: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n    display: none;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n    margin-bottom: 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .query-section[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(4) {\\n    display: none;\\n  }\\n}\\nnb-card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #5a5a5a;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n  border-color: #B8A676;\\n  transition: all 0.3s ease;\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(184, 166, 118, 0.3);\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-color: #dee2e6;\\n  color: #6c757d;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  color: #5a5a5a;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  margin-right: 0.5rem;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n.status-indicator.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);\\n}\\n.status-indicator.pending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #D4B96A 0%, #C4A85A 100%);\\n}\\n.status-indicator.expired[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F44336 0%, #E53935 100%);\\n}\\n.status-indicator.not-set[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9E9E9E 0%, #757575 100%);\\n}\\n.status-indicator.disabled[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #E0E0E0 0%, #BDBDBD 100%);\\n}\\n\\n  nb-select.appearance-outline .select-button {\\n  border-color: rgba(184, 166, 118, 0.3);\\n}\\n  nb-select.appearance-outline .select-button:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n  nb-form-field.appearance-outline .form-control {\\n  border-color: rgba(184, 166, 118, 0.3);\\n}\\n  nb-form-field.appearance-outline .form-control:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n  nb-checkbox .customised-control-input:checked ~ .customised-control-indicator {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n}\\n  nb-calendar-day-cell.selected {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "building_r3", "floor_r7", "ɵɵlistener", "SettingTimePeriodComponent_nb_card_23_div_28_button_37_Template_button_click_0_listener", "ɵɵrestoreView", "_r8", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "clearAllFilters", "ɵɵelement", "ɵɵtwoWayListener", "SettingTimePeriodComponent_nb_card_23_div_28_Template_input_ngModelChange_5_listener", "$event", "_r6", "ɵɵtwoWayBindingSet", "filterOptions", "searchKeyword", "onSearch", "SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_ngModelChange_7_listener", "statusFilter", "SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_selectedChange_7_listener", "SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_ngModelChange_21_listener", "floorFilter", "SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_selectedChange_21_listener", "ɵɵtemplate", "SettingTimePeriodComponent_nb_card_23_div_28_nb_option_24_Template", "SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_ngModelChange_26_listener", "pageSize", "SettingTimePeriodComponent_nb_card_23_div_28_Template_nb_select_selectedChange_26_listener", "onPageSizeChange", "SettingTimePeriodComponent_nb_card_23_div_28_button_37_Template", "ɵɵtwoWayProperty", "availableFloors", "hasActiveFilters", "ɵɵtextInterpolate", "selectedHouses", "length", "SettingTimePeriodComponent_nb_card_23_div_29_div_32_Template_button_click_1_listener", "_r10", "openBatchSetting", "SettingTimePeriodComponent_nb_card_23_div_29_div_32_Template_button_click_6_listener", "clearSelection", "ɵɵpipeBind2", "house_r12", "CChangeStartDate", "CChangeEndDate", "SettingTimePeriodComponent_nb_card_23_div_29_tr_74_Template_nb_checkbox_ngModelChange_2_listener", "_r11", "$implicit", "selected", "onHouseSelectionChange", "SettingTimePeriodComponent_nb_card_23_div_29_tr_74_small_7_Template", "SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_16_Template", "SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_17_Template", "SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_20_Template", "SettingTimePeriodComponent_nb_card_23_div_29_tr_74_span_21_Template", "SettingTimePeriodComponent_nb_card_23_div_29_tr_74_Template_button_click_30_listener", "dialog_r13", "ɵɵreference", "openModel", "ɵɵclassProp", "CHouseId", "CHouseHold", "CBuildingName", "CFloor", "ɵɵclassMap", "getStatusClass", "getStatusIcon", "getStatusText", "SettingTimePeriodComponent_nb_card_23_div_29_div_75_li_21_Template_button_click_1_listener", "page_r16", "_r15", "goToPage", "currentPage", "SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_button_click_16_listener", "_r14", "SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_button_click_19_listener", "SettingTimePeriodComponent_nb_card_23_div_29_div_75_li_21_Template", "SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_button_click_23_listener", "SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_button_click_26_listener", "totalPages", "SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_input_ngModelChange_30_listener", "jumpToPage", "SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_input_keyup_enter_30_listener", "jumpToPageAction", "SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template_button_click_31_listener", "ɵɵtextInterpolate3", "Math", "min", "filteredHouses", "getVisiblePages", "SettingTimePeriodComponent_nb_card_23_div_29_span_10_Template", "SettingTimePeriodComponent_nb_card_23_div_29_Template_button_click_13_listener", "_r9", "setQuickFilter", "SettingTimePeriodComponent_nb_card_23_div_29_Template_button_click_16_listener", "SettingTimePeriodComponent_nb_card_23_div_29_Template_button_click_19_listener", "SettingTimePeriodComponent_nb_card_23_div_29_Template_button_click_22_listener", "SettingTimePeriodComponent_nb_card_23_div_29_Template_nb_checkbox_ngModelChange_29_listener", "selectAll", "onSelectAllChange", "SettingTimePeriodComponent_nb_card_23_div_29_div_32_Template", "SettingTimePeriodComponent_nb_card_23_div_29_Template_button_click_35_listener", "exportData", "SettingTimePeriodComponent_nb_card_23_div_29_Template_nb_checkbox_ngModelChange_46_listener", "SettingTimePeriodComponent_nb_card_23_div_29_Template_th_click_47_listener", "sort", "SettingTimePeriodComponent_nb_card_23_div_29_Template_th_click_51_listener", "SettingTimePeriodComponent_nb_card_23_div_29_Template_th_click_55_listener", "SettingTimePeriodComponent_nb_card_23_div_29_Template_th_click_59_listener", "SettingTimePeriodComponent_nb_card_23_div_29_Template_th_click_63_listener", "SettingTimePeriodComponent_nb_card_23_div_29_tr_74_Template", "SettingTimePeriodComponent_nb_card_23_div_29_div_75_Template", "sortField", "sortDirection", "paginatedHouses", "trackByHouseId", "SettingTimePeriodComponent_nb_card_23_Template_input_ngModelChange_11_listener", "_r4", "searchQuery", "SettingTimePeriodComponent_nb_card_23_Template_input_ngModelChange_18_listener", "SettingTimePeriodComponent_nb_card_23_Template_button_click_23_listener", "getHouseChangeDate", "SettingTimePeriodComponent_nb_card_23_Template_button_click_26_listener", "resetFilters", "SettingTimePeriodComponent_nb_card_23_div_28_Template", "SettingTimePeriodComponent_nb_card_23_div_29_Template", "SettingTimePeriodComponent_nb_card_23_div_30_Template", "SettingTimePeriodComponent_nb_card_23_div_31_Template", "StartDate_r17", "EndDate_r18", "loading", "flattenedHouses", "houseChangeDates", "selectedBuildingForBatch", "name", "house_r20", "SettingTimePeriodComponent_ng_template_24_div_28_span_4_Template", "SettingTimePeriodComponent_ng_template_24_div_28_span_5_Template", "slice", "SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener", "house_r25", "_r24", "ɵɵtextInterpolate2", "SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_div_3_nb_checkbox_1_Template", "floor_r23", "houses", "SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener", "_r22", "onFloorSelectionChange", "SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_div_3_Template", "floorNumber", "SettingTimePeriodComponent_ng_template_24_div_29_div_3_div_1_Template", "floors", "SettingTimePeriodComponent_ng_template_24_div_29_Template_nb_checkbox_ngModelChange_1_listener", "_r21", "batchSettings", "applyToAll", "SettingTimePeriodComponent_ng_template_24_div_29_div_3_Template", "SettingTimePeriodComponent_ng_template_24_span_3_Template", "SettingTimePeriodComponent_ng_template_24_span_4_Template", "SettingTimePeriodComponent_ng_template_24_Template_input_ngModelChange_14_listener", "_r19", "startDate", "SettingTimePeriodComponent_ng_template_24_Template_input_ngModelChange_21_listener", "endDate", "SettingTimePeriodComponent_ng_template_24_div_28_Template", "SettingTimePeriodComponent_ng_template_24_div_29_Template", "SettingTimePeriodComponent_ng_template_24_Template_button_click_31_listener", "ref_r26", "dialogRef", "onClose", "SettingTimePeriodComponent_ng_template_24_Template_button_click_33_listener", "onBatchSubmit", "SettingTimePeriodComponent_ng_template_24_span_35_Template", "batchStartDate_r27", "batchEndDate_r28", "SettingTimePeriodComponent_ng_template_28_Template_input_ngModelChange_14_listener", "_r29", "selectedHouseChangeDate", "SettingTimePeriodComponent_ng_template_28_Template_input_ngModelChange_21_listener", "SettingTimePeriodComponent_ng_template_28_Template_button_click_25_listener", "ref_r30", "SettingTimePeriodComponent_ng_template_28_Template_button_click_27_listener", "onSubmit", "changeStartDate_r31", "changeEndDate_r32", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "buildingGroups", "buildingOptions", "selectedBuilding", "buildingFilter", "selectedBuildings", "selectedFloors", "undefined", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "getActiveFiltersCount", "count", "status", "for<PERSON>ach", "house", "getHouseStatus", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "validation", "errorMessages", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "household", "CHouses", "floor", "push", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "onBuildCaseChange", "resetAllStates", "convertedHouseArray", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "buildBuildingGroups", "buildFlattenedHouses", "data", "buildingMap", "Map", "houseType", "buildingName", "has", "set", "floorMap", "get", "localeCompare", "bg", "updateAvailableFloors", "building", "onBuildingChange", "getFilteredBuildings", "filter", "keyword", "toLowerCase", "hasMatchingHouse", "some", "includes", "hasMatchingStatus", "matchesStatusFilter", "parseInt", "hasMatchingFloor", "filteredBuilding", "hasValidHouses", "filteredFloor", "now", "today", "getFullYear", "getMonth", "getDate", "isNaN", "getTime", "console", "warn", "start", "end", "houseId", "startDateOnly", "endDateOnly", "error", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "hasSelectedHouses", "batchSettingDialog", "housesToUpdate", "openHouseDialog", "dialog", "debugStatusCounts", "statusCounts", "active", "pending", "expired", "disabled", "hasOwnProperty", "log", "toISOString", "split", "previouslySelectedIds", "updateSelectAllState", "updatePagination", "every", "ceil", "startIndex", "endIndex", "page", "pages", "maxVisible", "max", "i", "updateSelectedHouses", "field", "aValue", "bValue", "Number", "_index", "csv<PERSON><PERSON>nt", "generateCSV", "blob", "Blob", "type", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "rows", "row", "cell", "join", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "Router", "i7", "EventService", "selectors", "viewQuery", "SettingTimePeriodComponent_Query", "rf", "ctx", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingTimePeriodComponent_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_14_listener", "_r1", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_14_listener", "SettingTimePeriodComponent_nb_option_15_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_19_listener", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON><PERSON>_19_listener", "SettingTimePeriodComponent_nb_option_22_Template", "SettingTimePeriodComponent_nb_card_23_Template", "SettingTimePeriodComponent_ng_template_24_Template", "ɵɵtemplateRefExtractor", "SettingTimePeriodComponent_ng_template_26_Template", "SettingTimePeriodComponent_ng_template_28_Template", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MinValidator", "MaxValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\n\r\n\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string; // 戶型\r\n  CBuildingName: string; // 棟別\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n\r\n  @ViewChild('batchSettingDialog') batchSettingDialog!: TemplateRef<any>;\r\n  @ViewChild('dialog') dialog!: TemplateRef<any>;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  // 新增的屬性\r\n  buildingGroups: BuildingGroup[] = [];\r\n  buildingOptions: string[] = [];\r\n  selectedBuilding: string = '';\r\n  availableFloors: number[] = [];\r\n\r\n  // 篩選和搜尋\r\n  filterOptions: FilterOptions = {\r\n    searchKeyword: '',\r\n    statusFilter: '',\r\n    floorFilter: '',\r\n    buildingFilter: ''\r\n  };\r\n\r\n  // 批次設定\r\n  batchSettings: BatchSettings = {\r\n    startDate: null,\r\n    endDate: null,\r\n    applyToAll: true,\r\n    selectedBuildings: [],\r\n    selectedFloors: [],\r\n    selectedHouses: []\r\n  };\r\n\r\n  selectedBuildingForBatch: BuildingGroup | null = null;\r\n\r\n  // 表格視圖相關屬性\r\n  flattenedHouses: ConvertedHouse[] = [];\r\n  filteredHouses: ConvertedHouse[] = [];\r\n  paginatedHouses: ConvertedHouse[] = [];\r\n  selectedHouses: ConvertedHouse[] = [];\r\n  selectAll: boolean = false;\r\n  loading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  override pageSize: number = 50;\r\n  totalPages: number = 1;\r\n\r\n  // 排序相關\r\n  sortField: string = '';\r\n  sortDirection: 'asc' | 'desc' = 'asc';\r\n\r\n  // 數學函數引用\r\n  Math = Math;\r\n\r\n  // 新增的UI控制屬性\r\n  jumpToPage: number = 1;\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  // 新增的UI控制方法\r\n  hasActiveFilters(): boolean {\r\n    return !!(this.filterOptions.searchKeyword ||\r\n      this.filterOptions.statusFilter ||\r\n      this.filterOptions.floorFilter);\r\n  }\r\n\r\n  getActiveFiltersCount(): number {\r\n    let count = 0;\r\n    if (this.filterOptions.searchKeyword) count++;\r\n    if (this.filterOptions.statusFilter) count++;\r\n    if (this.filterOptions.floorFilter) count++;\r\n    return count;\r\n  }\r\n\r\n  resetFilters(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    };\r\n    this.selectedBuilding = '';\r\n    this.clearAllFilters();\r\n  }\r\n\r\n  clearAllFilters(): void {\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n    this.onSearch();\r\n  }\r\n\r\n  setQuickFilter(status: string): void {\r\n    if (this.filterOptions.statusFilter === status) {\r\n      this.filterOptions.statusFilter = '';\r\n    } else {\r\n      this.filterOptions.statusFilter = status;\r\n    }\r\n    this.onSearch();\r\n  }\r\n\r\n  clearSelection(): void {\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n    this.flattenedHouses.forEach(house => house.selected = false);\r\n  }\r\n\r\n  getStatusIcon(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    switch (status) {\r\n      case 'active': return 'fas fa-play-circle';\r\n      case 'pending': return 'fas fa-clock';\r\n      case 'expired': return 'fas fa-times-circle';\r\n      case 'not-set': return 'fas fa-exclamation-triangle';\r\n      case 'disabled': return 'fas fa-ban';\r\n      default: return 'fas fa-exclamation-triangle';\r\n    }\r\n  }\r\n\r\n  jumpToPageAction(): void {\r\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\r\n      this.goToPage(this.jumpToPage);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CBuildingName: '未分類',\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：建案變更處理\r\n  onBuildCaseChange() {\r\n    // 重置所有相關狀態\r\n    this.resetAllStates();\r\n    // 執行查詢\r\n    this.getHouseChangeDate();\r\n  }\r\n\r\n  // 新增：重置所有狀態\r\n  resetAllStates() {\r\n    // 重置數據\r\n    this.houseChangeDates = [];\r\n    this.convertedHouseArray = [];\r\n    this.buildingGroups = [];\r\n    this.flattenedHouses = [];\r\n    this.filteredHouses = [];\r\n    this.paginatedHouses = [];\r\n    this.selectedHouses = [];\r\n\r\n    // 重置篩選條件\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    this.selectAll = false;\r\n    this.selectedBuilding = '';\r\n\r\n    // 重置分頁\r\n    this.currentPage = 1;\r\n    this.totalPages = 1;\r\n\r\n    // 重置可用選項\r\n    this.buildingOptions = [];\r\n    this.availableFloors = [];\r\n\r\n    // 重置排序\r\n    this.sortField = '';\r\n    this.sortDirection = 'asc';\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    // 如果沒有選擇建案，直接返回\r\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      this.loading = false;\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n          // 新增：建立棟別分組資料\r\n          this.buildBuildingGroups(res.Entries)\r\n          // 新增：建立扁平化資料\r\n          this.buildFlattenedHouses(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\r\n  buildBuildingGroups(data: GetHouseChangeDateRes[]) {\r\n    const buildingMap = new Map<string, Map<number, ConvertedHouse[]>>();\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || ''; // 戶型\r\n\r\n      household.CHouses?.forEach(house => {\r\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\r\n        const floor = house.CFloor || 0;\r\n\r\n        if (!buildingMap.has(buildingName)) {\r\n          buildingMap.set(buildingName, new Map<number, ConvertedHouse[]>());\r\n        }\r\n\r\n        const floorMap = buildingMap.get(buildingName)!;\r\n\r\n        if (!floorMap.has(floor)) {\r\n          floorMap.set(floor, []);\r\n        }\r\n\r\n        floorMap.get(floor)!.push({\r\n          CHouseHold: houseType, // 戶型\r\n          CBuildingName: buildingName, // 棟別\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: floor,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 轉換為BuildingGroup格式\r\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\r\n      const floors: FloorGroup[] = Array.from(floorMap.entries())\r\n        .sort(([a], [b]) => b - a) // 樓層由高到低排序\r\n        .map(([floorNumber, houses]) => ({\r\n          floorNumber,\r\n          houses: houses.sort((a, b) => {\r\n            // 排序邏輯：先按戶型排序，再按樓層排序\r\n            if (a.CHouseHold !== b.CHouseHold) {\r\n              return a.CHouseHold.localeCompare(b.CHouseHold);\r\n            }\r\n            return a.CFloor - b.CFloor;\r\n          }),\r\n          selected: false\r\n        }));\r\n\r\n      return {\r\n        name: buildingName,\r\n        floors,\r\n        selected: false\r\n      };\r\n    }).sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    // 更新棟別選項和可用樓層\r\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\r\n    this.updateAvailableFloors();\r\n  }\r\n\r\n  // 新增：更新可用樓層\r\n  updateAvailableFloors() {\r\n    const floorsSet = new Set<number>();\r\n\r\n    this.buildingGroups.forEach(building => {\r\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\r\n        building.floors.forEach(floor => {\r\n          floorsSet.add(floor.floorNumber);\r\n        });\r\n      }\r\n    });\r\n\r\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\r\n  }\r\n\r\n  // 修改：棟別選擇變更處理\r\n  onBuildingChange() {\r\n    // 重置選擇狀態\r\n    this.selectedHouses.forEach(house => house.selected = false);\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n\r\n    // 重置分頁到第一頁\r\n    this.currentPage = 1;\r\n\r\n    // 重置樓層篩選\r\n    this.filterOptions.floorFilter = '';\r\n\r\n    // 更新可用樓層\r\n    this.updateAvailableFloors();\r\n\r\n    // 設定棟別篩選並執行搜尋\r\n    this.filterOptions.buildingFilter = this.selectedBuilding;\r\n    this.onSearch();\r\n  }\r\n\r\n\r\n\r\n  // 新增：取得過濾後的棟別資料\r\n  getFilteredBuildings(): BuildingGroup[] {\r\n    return this.buildingGroups.filter(building => {\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 關鍵字搜尋 (搜尋戶型)\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        const hasMatchingHouse = building.floors.some(floor =>\r\n          floor.houses.some(house =>\r\n            house.CHouseHold.toLowerCase().includes(keyword) ||\r\n            house.CBuildingName.toLowerCase().includes(keyword)\r\n          )\r\n        );\r\n        if (!hasMatchingHouse) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        const hasMatchingStatus = building.floors.some(floor =>\r\n          floor.houses.some(house => this.matchesStatusFilter(house))\r\n        );\r\n        if (!hasMatchingStatus) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        const hasMatchingFloor = building.floors.some(floor =>\r\n          floor.floorNumber === floorNumber\r\n        );\r\n        if (!hasMatchingFloor) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }).map(building => {\r\n      // 對每個棟別，也要篩選其樓層和戶別\r\n      const filteredBuilding = { ...building };\r\n      filteredBuilding.floors = building.floors.filter(floor => {\r\n        // 樓層篩選\r\n        if (this.filterOptions.floorFilter) {\r\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n          if (floor.floorNumber !== floorNumber) {\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 檢查該樓層是否有符合條件的戶別\r\n        const hasValidHouses = floor.houses.some(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return hasValidHouses;\r\n      }).map(floor => {\r\n        // 篩選戶別\r\n        const filteredFloor = { ...floor };\r\n        filteredFloor.houses = floor.houses.filter(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return filteredFloor;\r\n      });\r\n\r\n      return filteredBuilding;\r\n    });\r\n  }\r\n\r\n  // 修復：檢查戶別是否符合狀態篩選\r\n  private matchesStatusFilter(house: ConvertedHouse): boolean {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (this.filterOptions.statusFilter) {\r\n      case 'active':\r\n        return status === 'active';\r\n      case 'pending':\r\n        return status === 'pending';\r\n      case 'expired':\r\n        return status === 'expired';\r\n      case 'not-set':\r\n        return status === 'not-set';\r\n      case 'disabled':\r\n        return status === 'disabled';\r\n      default:\r\n        return true; // 全部狀態\r\n    }\r\n  }\r\n\r\n  // 修復：取得戶別狀態\r\n  private getHouseStatus(house: ConvertedHouse): string {\r\n    if (!house.CHouseId) {\r\n      return 'disabled';\r\n    }\r\n\r\n    // 檢查是否有設定開放時段\r\n    if (!house.CChangeStartDate || !house.CChangeEndDate ||\r\n      house.CChangeStartDate === '' || house.CChangeEndDate === '') {\r\n      return 'not-set';\r\n    }\r\n\r\n    try {\r\n      // 處理日期字串，支援多種格式\r\n      const now = new Date();\r\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n\r\n      // 解析開始日期\r\n      let startDate: Date;\r\n      if (house.CChangeStartDate.includes('T')) {\r\n        startDate = new Date(house.CChangeStartDate);\r\n      } else {\r\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\r\n      }\r\n\r\n      // 解析結束日期\r\n      let endDate: Date;\r\n      if (house.CChangeEndDate.includes('T')) {\r\n        endDate = new Date(house.CChangeEndDate);\r\n      } else {\r\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\r\n      }\r\n\r\n      // 檢查日期有效性\r\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\r\n        console.warn('Invalid date format:', {\r\n          start: house.CChangeStartDate,\r\n          end: house.CChangeEndDate,\r\n          houseId: house.CHouseId\r\n        });\r\n        return 'not-set';\r\n      }\r\n\r\n      // 轉換為日期比較（不含時間）\r\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\r\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\r\n\r\n      // 判斷狀態\r\n      if (today < startDateOnly) {\r\n        return 'pending'; // 待開放\r\n      } else if (today >= startDateOnly && today <= endDateOnly) {\r\n        return 'active'; // 進行中\r\n      } else {\r\n        return 'expired'; // 已過期\r\n      }\r\n    } catch (error) {\r\n      console.error('Error parsing dates:', error, house);\r\n      return 'not-set';\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n\r\n  // 修改：開啟批次設定對話框\r\n  openBatchSetting(building?: BuildingGroup) {\r\n    this.selectedBuildingForBatch = building || null;\r\n\r\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\r\n    const hasSelectedHouses = this.selectedHouses.length > 0;\r\n\r\n    this.batchSettings = {\r\n      startDate: null,\r\n      endDate: null,\r\n      applyToAll: !hasSelectedHouses && !building,\r\n      selectedBuildings: building ? [building.name] : [],\r\n      selectedFloors: [],\r\n      selectedHouses: []\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    if (building) {\r\n      building.floors.forEach(floor => {\r\n        floor.selected = false;\r\n        floor.houses.forEach(house => house.selected = false);\r\n      });\r\n    }\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(this.batchSettingDialog);\r\n  }\r\n\r\n  // 新增：樓層選擇變更處理\r\n  onFloorSelectionChange(floor: FloorGroup) {\r\n    if (floor.selected) {\r\n      floor.houses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          house.selected = true;\r\n        }\r\n      });\r\n    } else {\r\n      floor.houses.forEach(house => house.selected = false);\r\n    }\r\n  }\r\n\r\n  // 修改：批次提交\r\n  onBatchSubmit(ref: any) {\r\n    // 驗證批次設定\r\n    this.valid.clear();\r\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\r\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 收集要更新的房屋\r\n    const housesToUpdate: any[] = [];\r\n\r\n    if (this.batchSettings.applyToAll) {\r\n      // 全部戶別\r\n      this.flattenedHouses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          housesToUpdate.push({\r\n            CHouseId: house.CHouseId,\r\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // 使用已選擇的戶別\r\n      if (this.selectedHouses.length > 0) {\r\n        this.selectedHouses.forEach(house => {\r\n          if (house.CHouseId) {\r\n            housesToUpdate.push({\r\n              CHouseId: house.CHouseId,\r\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n            });\r\n          }\r\n        });\r\n      } else if (this.selectedBuildingForBatch) {\r\n        // 如果沒有選擇的戶別，使用舊的邏輯\r\n        this.selectedBuildingForBatch.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.selected && house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n\r\n    if (housesToUpdate.length === 0) {\r\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\r\n      return;\r\n    }\r\n\r\n    // 調用API進行批次更新\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: housesToUpdate\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\r\n        // 清除選擇狀態\r\n        this.selectedHouses.forEach(house => house.selected = false);\r\n        this.selectedHouses = [];\r\n        this.selectAll = false;\r\n        this.getHouseChangeDate();\r\n        ref.close();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：取得狀態樣式類別\r\n  getStatusClass(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    return `status-${status}`;\r\n  }\r\n\r\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\r\n  openHouseDialog(house: ConvertedHouse) {\r\n    if (house.CHouseId) {\r\n      // 使用現有的openModel方法\r\n      this.openModel(this.dialog, house);\r\n    }\r\n  }\r\n\r\n  // 新增：建立扁平化房屋資料\r\n  buildFlattenedHouses(data: GetHouseChangeDateRes[]) {\r\n    this.flattenedHouses = [];\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || '';\r\n\r\n      household.CHouses?.forEach(house => {\r\n        this.flattenedHouses.push({\r\n          CHouseHold: houseType,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: house.CFloor || 0,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 初始化篩選和分頁\r\n    this.onSearch();\r\n\r\n    // 調試：輸出狀態統計\r\n    this.debugStatusCounts();\r\n  }\r\n\r\n  // 調試：輸出狀態統計\r\n  private debugStatusCounts() {\r\n    const statusCounts = {\r\n      active: 0,\r\n      pending: 0,\r\n      expired: 0,\r\n      'not-set': 0,\r\n      disabled: 0\r\n    };\r\n\r\n    this.flattenedHouses.forEach(house => {\r\n      const status = this.getHouseStatus(house);\r\n      if (statusCounts.hasOwnProperty(status)) {\r\n        statusCounts[status as keyof typeof statusCounts]++;\r\n      }\r\n    });\r\n\r\n    console.log('狀態統計:', statusCounts);\r\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\r\n  }\r\n\r\n\r\n\r\n  // 修改：搜尋和篩選\r\n  onSearch() {\r\n    // 記錄篩選前的已選擇項目\r\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\r\n\r\n    this.filteredHouses = this.flattenedHouses.filter(house => {\r\n      // 關鍵字搜尋\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n          !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        if (!this.matchesStatusFilter(house)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        if (house.CFloor !== floorNumber) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    });\r\n\r\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\r\n    this.selectedHouses = this.filteredHouses.filter(house =>\r\n      previouslySelectedIds.includes(house.CHouseId)\r\n    );\r\n\r\n    // 更新扁平化資料中的選擇狀態\r\n    this.flattenedHouses.forEach(house => {\r\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\r\n    });\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n\r\n    // 重新計算分頁\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：更新全選狀態\r\n  private updateSelectAllState() {\r\n    if (this.paginatedHouses.length === 0) {\r\n      this.selectAll = false;\r\n    } else {\r\n      this.selectAll = this.paginatedHouses.every(house =>\r\n        !house.CHouseId || house.selected\r\n      );\r\n    }\r\n  }\r\n\r\n  // 修改：更新分頁\r\n  updatePagination() {\r\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\r\n    const startIndex = (this.currentPage - 1) * this.pageSize;\r\n    const endIndex = startIndex + this.pageSize;\r\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 新增：頁面大小變更\r\n  onPageSizeChange() {\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：跳轉頁面\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  // 新增：取得可見頁碼\r\n  getVisiblePages(): number[] {\r\n    const pages: number[] = [];\r\n    const maxVisible = 5;\r\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\r\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\r\n\r\n    if (end - start + 1 < maxVisible) {\r\n      start = Math.max(1, end - maxVisible + 1);\r\n    }\r\n\r\n    for (let i = start; i <= end; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 新增：全選/取消全選\r\n  onSelectAllChange() {\r\n    this.paginatedHouses.forEach(house => {\r\n      if (house.CHouseId) {\r\n        house.selected = this.selectAll;\r\n      }\r\n    });\r\n    this.updateSelectedHouses();\r\n  }\r\n\r\n  // 新增：單一選擇變更\r\n  onHouseSelectionChange() {\r\n    this.updateSelectedHouses();\r\n    this.selectAll = this.paginatedHouses.every(house =>\r\n      !house.CHouseId || house.selected\r\n    );\r\n  }\r\n\r\n  // 新增：更新已選擇房屋列表\r\n  updateSelectedHouses() {\r\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\r\n  }\r\n\r\n  // 新增：排序\r\n  sort(field: string) {\r\n    if (this.sortField === field) {\r\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortDirection = 'asc';\r\n    }\r\n\r\n    this.filteredHouses.sort((a, b) => {\r\n      let aValue = (a as any)[field];\r\n      let bValue = (b as any)[field];\r\n\r\n      // 處理日期排序\r\n      if (field.includes('Date')) {\r\n        aValue = aValue ? new Date(aValue).getTime() : 0;\r\n        bValue = bValue ? new Date(bValue).getTime() : 0;\r\n      }\r\n\r\n      // 處理數字排序\r\n      if (field === 'CFloor') {\r\n        aValue = Number(aValue) || 0;\r\n        bValue = Number(bValue) || 0;\r\n      }\r\n\r\n      // 處理字串排序\r\n      if (typeof aValue === 'string') {\r\n        aValue = aValue.toLowerCase();\r\n        bValue = bValue.toLowerCase();\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return this.sortDirection === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return this.sortDirection === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：TrackBy函數\r\n  trackByHouseId(_index: number, house: ConvertedHouse): number {\r\n    return house.CHouseId;\r\n  }\r\n\r\n  // 新增：取得狀態文字\r\n  getStatusText(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (status) {\r\n      case 'active':\r\n        return '進行中';\r\n      case 'pending':\r\n        return '待開放';\r\n      case 'expired':\r\n        return '已過期';\r\n      case 'not-set':\r\n        return '未設定';\r\n      case 'disabled':\r\n        return '已停用';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  // 新增：匯出資料\r\n  exportData() {\r\n    // 實現匯出功能\r\n    const csvContent = this.generateCSV();\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  // 新增：產生CSV內容\r\n  private generateCSV(): string {\r\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\r\n    const rows = this.filteredHouses.map(house => [\r\n      house.CHouseHold,\r\n      house.CBuildingName,\r\n      `${house.CFloor}F`,\r\n      house.CChangeStartDate || '未設定',\r\n      house.CChangeEndDate || '未設定',\r\n      this.getStatusText(house)\r\n    ]);\r\n\r\n    const csvContent = [headers, ...rows]\r\n      .map(row => row.map(cell => `\"${cell}\"`).join(','))\r\n      .join('\\n');\r\n\r\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\r\n  }\r\n}\r\n", "<!-- 上方區塊：基本選擇 -->\r\n<nb-card accent=\"success\" class=\"mb-3\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <!-- 頁面說明 -->\r\n    <div class=\"page-description-section\">\r\n      <p class=\"page-description text-muted mb-0\">管理各戶別的選樣開放時間範圍</p>\r\n    </div>\r\n\r\n    <!-- 基本選擇區域 -->\r\n    <div class=\"basic-selection-area\">\r\n      <div class=\"row g-3 align-items-end\">\r\n        <div class=\"col-lg-4 col-md-6\">\r\n          <label class=\"form-label fw-medium\">建案 <span class=\"text-danger\">*</span></label>\r\n          <nb-select placeholder=\"請選擇建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\"\r\n            (selectedChange)=\"onBuildCaseChange()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n        <div class=\"col-lg-3 col-md-6\">\r\n          <label class=\"form-label fw-medium\">棟別</label>\r\n          <nb-select placeholder=\"全部棟別\" [(ngModel)]=\"selectedBuilding\" (selectedChange)=\"onBuildingChange()\">\r\n            <nb-option value=\"\">全部棟別</nb-option>\r\n            <nb-option *ngFor=\"let building of buildingOptions\" [value]=\"building\">\r\n              {{ building }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 下方區塊：篩選和表格 -->\r\n<nb-card accent=\"success\" *ngIf=\"searchQuery.CBuildCaseSelected\">\r\n  <nb-card-body class=\"p-0\">\r\n    <!-- 篩選和操作區域 -->\r\n    <div class=\"filters-and-table-section\">\r\n      <!-- 日期範圍和查詢 -->\r\n      <div class=\"date-query-section\">\r\n        <div class=\"row g-3 align-items-end\">\r\n          <div class=\"col-lg-5 col-md-6\">\r\n            <label class=\"form-label fw-medium\">開放日期範圍</label>\r\n            <div class=\"date-range-group\">\r\n              <nb-form-field>\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"開始日期\" [nbDatepicker]=\"StartDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n                <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n              <span class=\"date-separator\">~</span>\r\n              <nb-form-field>\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"結束日期\" [nbDatepicker]=\"EndDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n                <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-6\">\r\n            <div class=\"query-actions\">\r\n              <button class=\"btn btn-primary\" (click)=\"getHouseChangeDate()\" [disabled]=\"loading\">\r\n                <i class=\"fas fa-search me-1\"></i>查詢\r\n              </button>\r\n              <button class=\"btn btn-outline-secondary ms-2\" (click)=\"resetFilters()\" [disabled]=\"loading\"\r\n                title=\"重置篩選條件\">\r\n                <i class=\"fas fa-undo\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 進階篩選區域 -->\r\n      <div class=\"advanced-filters-section\" *ngIf=\"flattenedHouses.length > 0\">\r\n        <div class=\"row g-3 align-items-center\">\r\n          <div class=\"col-lg-3 col-md-4\">\r\n            <nb-form-field>\r\n              <nb-icon nbPrefix icon=\"search-outline\"></nb-icon>\r\n              <input nbInput placeholder=\"搜尋戶型、棟別...\" [(ngModel)]=\"filterOptions.searchKeyword\"\r\n                (ngModelChange)=\"onSearch()\">\r\n            </nb-form-field>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <nb-select placeholder=\"狀態篩選\" [(ngModel)]=\"filterOptions.statusFilter\" (selectedChange)=\"onSearch()\">\r\n              <nb-option value=\"\">全部狀態</nb-option>\r\n              <nb-option value=\"active\">進行中</nb-option>\r\n              <nb-option value=\"pending\">待開放</nb-option>\r\n              <nb-option value=\"expired\">已過期</nb-option>\r\n              <nb-option value=\"not-set\">未設定</nb-option>\r\n              <nb-option value=\"disabled\">已停用</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <nb-select placeholder=\"樓層篩選\" [(ngModel)]=\"filterOptions.floorFilter\" (selectedChange)=\"onSearch()\">\r\n              <nb-option value=\"\">全部樓層</nb-option>\r\n              <nb-option *ngFor=\"let floor of availableFloors\" [value]=\"floor\">\r\n                {{ floor }}F\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-2\">\r\n            <nb-select placeholder=\"顯示筆數\" [(ngModel)]=\"pageSize\" (selectedChange)=\"onPageSizeChange()\">\r\n              <nb-option [value]=\"50\">50筆/頁</nb-option>\r\n              <nb-option [value]=\"100\">100筆/頁</nb-option>\r\n              <nb-option [value]=\"200\">200筆/頁</nb-option>\r\n              <nb-option [value]=\"500\">500筆/頁</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12\">\r\n            <div class=\"filter-actions\">\r\n              <button class=\"btn btn-outline-danger btn-sm\" (click)=\"clearAllFilters()\" *ngIf=\"hasActiveFilters()\">\r\n                <i class=\"fas fa-times me-1\"></i>清除篩選\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- 優化的表格視圖 -->\r\n      <div class=\"table-view-enhanced mt-4\" *ngIf=\"flattenedHouses.length > 0\">\r\n        <!-- 資料統計和快速篩選 -->\r\n        <div class=\"data-summary-bar\">\r\n          <div class=\"d-flex justify-content-between align-items-center\">\r\n            <div class=\"summary-info\">\r\n              <span class=\"total-count\">\r\n                <i class=\"fas fa-database me-1\"></i>\r\n                共 <strong>{{ filteredHouses.length }}</strong> 筆資料\r\n              </span>\r\n              <span class=\"selected-count\" *ngIf=\"selectedHouses.length > 0\">\r\n                <i class=\"fas fa-check-square me-1 text-primary\"></i>\r\n                已選 <strong class=\"text-primary\">{{ selectedHouses.length }}</strong> 筆\r\n              </span>\r\n            </div>\r\n            <div class=\"quick-filters\">\r\n              <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n                <button type=\"button\" class=\"btn\" [class.btn-primary]=\"filterOptions.statusFilter === 'active'\"\r\n                  [class.btn-outline-primary]=\"filterOptions.statusFilter !== 'active'\"\r\n                  (click)=\"setQuickFilter('active')\">\r\n                  <i class=\"fas fa-play-circle me-1\"></i>進行中\r\n                </button>\r\n                <button type=\"button\" class=\"btn\" [class.btn-warning]=\"filterOptions.statusFilter === 'pending'\"\r\n                  [class.btn-outline-warning]=\"filterOptions.statusFilter !== 'pending'\"\r\n                  (click)=\"setQuickFilter('pending')\">\r\n                  <i class=\"fas fa-clock me-1\"></i>待開放\r\n                </button>\r\n                <button type=\"button\" class=\"btn\" [class.btn-danger]=\"filterOptions.statusFilter === 'expired'\"\r\n                  [class.btn-outline-danger]=\"filterOptions.statusFilter !== 'expired'\"\r\n                  (click)=\"setQuickFilter('expired')\">\r\n                  <i class=\"fas fa-times-circle me-1\"></i>已過期\r\n                </button>\r\n                <button type=\"button\" class=\"btn btn-not-set\"\r\n                  [class.btn-not-set-active]=\"filterOptions.statusFilter === 'not-set'\"\r\n                  [class.btn-not-set-outline]=\"filterOptions.statusFilter !== 'not-set'\"\r\n                  (click)=\"setQuickFilter('not-set')\">\r\n                  <i class=\"fas fa-exclamation-triangle me-1\"></i>未設定\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 優化的工具列 -->\r\n        <div class=\"enhanced-toolbar\">\r\n          <div class=\"d-flex justify-content-between align-items-center\">\r\n            <div class=\"batch-operations\">\r\n              <div class=\"selection-controls\">\r\n                <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\" class=\"select-all-checkbox\">\r\n                  <span class=\"fw-medium\">全選</span>\r\n                </nb-checkbox>\r\n                <div class=\"batch-actions ms-3\" *ngIf=\"selectedHouses.length > 0\">\r\n                  <button class=\"btn btn-warning btn-sm\" (click)=\"openBatchSetting()\" title=\"批次設定選中的戶別開放時段\">\r\n                    <i class=\"fas fa-cogs me-1\"></i>批次設定\r\n                    <span class=\"badge bg-light text-dark ms-1\">{{ selectedHouses.length }}</span>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-danger btn-sm ms-2\" (click)=\"clearSelection()\" title=\"清除選擇\">\r\n                    <i class=\"fas fa-times me-1\"></i>清除選擇\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"table-controls\">\r\n              <div class=\"d-flex align-items-center gap-2\">\r\n                <button class=\"btn btn-outline-success btn-sm\" [disabled]=\"filteredHouses.length === 0\"\r\n                  (click)=\"exportData()\" title=\"匯出資料\">\r\n                  <i class=\"fas fa-download me-1\"></i>匯出\r\n                </button>\r\n                <div class=\"pagination-summary\">\r\n                  <small class=\"text-muted\">\r\n                    顯示 {{ (currentPage - 1) * pageSize + 1 }} -\r\n                    {{ Math.min(currentPage * pageSize, filteredHouses.length) }} /\r\n                    共 {{ filteredHouses.length }} 筆\r\n                  </small>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 優化的表格 -->\r\n        <div class=\"enhanced-table-container\">\r\n          <table class=\"table table-hover enhanced-table\">\r\n            <thead class=\"enhanced-table-header\">\r\n              <tr>\r\n                <th width=\"50\" class=\"text-center\">\r\n                  <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\"></nb-checkbox>\r\n                </th>\r\n                <th width=\"120\" (click)=\"sort('CHouseHold')\" class=\"sortable\">\r\n                  <div class=\"header-content\">\r\n                    戶型\r\n                    <i class=\"fas fa-sort sort-icon\"\r\n                      [class.fa-sort-up]=\"sortField === 'CHouseHold' && sortDirection === 'asc'\"\r\n                      [class.fa-sort-down]=\"sortField === 'CHouseHold' && sortDirection === 'desc'\"></i>\r\n                  </div>\r\n                </th>\r\n                <th width=\"100\" (click)=\"sort('CBuildingName')\" class=\"sortable\">\r\n                  <div class=\"header-content\">\r\n                    棟別\r\n                    <i class=\"fas fa-sort sort-icon\"\r\n                      [class.fa-sort-up]=\"sortField === 'CBuildingName' && sortDirection === 'asc'\"\r\n                      [class.fa-sort-down]=\"sortField === 'CBuildingName' && sortDirection === 'desc'\"></i>\r\n                  </div>\r\n                </th>\r\n                <th width=\"80\" (click)=\"sort('CFloor')\" class=\"sortable text-center\">\r\n                  <div class=\"header-content\">\r\n                    樓層\r\n                    <i class=\"fas fa-sort sort-icon\"\r\n                      [class.fa-sort-up]=\"sortField === 'CFloor' && sortDirection === 'asc'\"\r\n                      [class.fa-sort-down]=\"sortField === 'CFloor' && sortDirection === 'desc'\"></i>\r\n                  </div>\r\n                </th>\r\n                <th width=\"140\" (click)=\"sort('CChangeStartDate')\" class=\"sortable\">\r\n                  <div class=\"header-content\">\r\n                    開始日期\r\n                    <i class=\"fas fa-sort sort-icon\"\r\n                      [class.fa-sort-up]=\"sortField === 'CChangeStartDate' && sortDirection === 'asc'\"\r\n                      [class.fa-sort-down]=\"sortField === 'CChangeStartDate' && sortDirection === 'desc'\"></i>\r\n                  </div>\r\n                </th>\r\n                <th width=\"140\" (click)=\"sort('CChangeEndDate')\" class=\"sortable\">\r\n                  <div class=\"header-content\">\r\n                    結束日期\r\n                    <i class=\"fas fa-sort sort-icon\"\r\n                      [class.fa-sort-up]=\"sortField === 'CChangeEndDate' && sortDirection === 'asc'\"\r\n                      [class.fa-sort-down]=\"sortField === 'CChangeEndDate' && sortDirection === 'desc'\"></i>\r\n                  </div>\r\n                </th>\r\n                <th width=\"120\" class=\"text-center\">\r\n                  <div class=\"header-content\">\r\n                    狀態\r\n                  </div>\r\n                </th>\r\n                <th width=\"100\" class=\"text-center\">\r\n                  <div class=\"header-content\">\r\n                    操作\r\n                  </div>\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let house of paginatedHouses; trackBy: trackByHouseId\"\r\n                [class.table-row-selected]=\"house.selected\" [class.table-row-disabled]=\"!house.CHouseId\">\r\n                <td class=\"text-center\">\r\n                  <nb-checkbox [(ngModel)]=\"house.selected\" [disabled]=\"!house.CHouseId\"\r\n                    (ngModelChange)=\"onHouseSelectionChange()\"></nb-checkbox>\r\n                </td>\r\n                <td>\r\n                  <div class=\"house-info\">\r\n                    <span class=\"house-name fw-medium\">{{ house.CHouseHold }}</span>\r\n                    <small class=\"text-muted d-block\" *ngIf=\"!house.CHouseId\">無資料</small>\r\n                  </div>\r\n                </td>\r\n                <td>\r\n                  <span class=\"building-name\">{{ house.CBuildingName }}</span>\r\n                </td>\r\n                <td class=\"text-center\">\r\n                  <span class=\"floor-badge\">{{ house.CFloor }}F</span>\r\n                </td>\r\n                <td>\r\n                  <div class=\"date-info\">\r\n                    <span *ngIf=\"house.CChangeStartDate\" class=\"date-display\">\r\n                      <i class=\"fas fa-calendar me-1 text-success\"></i>\r\n                      {{ house.CChangeStartDate | date:'yyyy-MM-dd' }}\r\n                    </span>\r\n                    <span *ngIf=\"!house.CChangeStartDate\" class=\"not-set-text\">\r\n                      <i class=\"fas fa-exclamation-triangle me-1\"></i>未設定\r\n                    </span>\r\n                  </div>\r\n                </td>\r\n                <td>\r\n                  <div class=\"date-info\">\r\n                    <span *ngIf=\"house.CChangeEndDate\" class=\"date-display\">\r\n                      <i class=\"fas fa-calendar me-1 text-danger\"></i>\r\n                      {{ house.CChangeEndDate | date:'yyyy-MM-dd' }}\r\n                    </span>\r\n                    <span *ngIf=\"!house.CChangeEndDate\" class=\"not-set-text\">\r\n                      <i class=\"fas fa-exclamation-triangle me-1\"></i>未設定\r\n                    </span>\r\n                  </div>\r\n                </td>\r\n                <td class=\"text-center\">\r\n                  <div class=\"status-display\">\r\n                    <span class=\"enhanced-status-badge\" [class]=\"getStatusClass(house)\">\r\n                      <i class=\"status-icon\" [class]=\"getStatusIcon(house)\"></i>\r\n                      <span class=\"status-text\">{{ getStatusText(house) }}</span>\r\n                    </span>\r\n                  </div>\r\n                </td>\r\n                <td class=\"text-center\">\r\n                  <div class=\"action-buttons\">\r\n                    <button class=\"btn btn-sm btn-outline-primary\" [disabled]=\"!house.CHouseId\"\r\n                      (click)=\"openModel(dialog, house)\" title=\"編輯時段設定\">\r\n                      <i class=\"fas fa-edit\"></i>\r\n                    </button>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n\r\n        <!-- 優化的分頁控制 -->\r\n        <div class=\"enhanced-pagination-container\" *ngIf=\"totalPages > 1\">\r\n          <div class=\"d-flex justify-content-between align-items-center\">\r\n            <div class=\"pagination-info-detailed\">\r\n              <span class=\"text-muted\">\r\n                第 <strong>{{ currentPage }}</strong> 頁，共 <strong>{{ totalPages }}</strong> 頁\r\n                <span class=\"ms-2\">\r\n                  (顯示第 {{ (currentPage - 1) * pageSize + 1 }} -\r\n                  {{ Math.min(currentPage * pageSize, filteredHouses.length) }} 筆，\r\n                  共 {{ filteredHouses.length }} 筆)\r\n                </span>\r\n              </span>\r\n            </div>\r\n\r\n            <nav class=\"pagination-nav\">\r\n              <ul class=\"pagination pagination-sm mb-0\">\r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\" title=\"第一頁\">\r\n                    <i class=\"fas fa-angle-double-left\"></i>\r\n                  </button>\r\n                </li>\r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\"\r\n                    title=\"上一頁\">\r\n                    <i class=\"fas fa-angle-left\"></i>\r\n                  </button>\r\n                </li>\r\n\r\n                <!-- 頁碼顯示 -->\r\n                <li class=\"page-item\" *ngFor=\"let page of getVisiblePages()\" [class.active]=\"page === currentPage\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n                </li>\r\n\r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\" [disabled]=\"currentPage === totalPages\"\r\n                    title=\"下一頁\">\r\n                    <i class=\"fas fa-angle-right\"></i>\r\n                  </button>\r\n                </li>\r\n                <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                  <button class=\"page-link\" (click)=\"goToPage(totalPages)\" [disabled]=\"currentPage === totalPages\"\r\n                    title=\"最後一頁\">\r\n                    <i class=\"fas fa-angle-double-right\"></i>\r\n                  </button>\r\n                </li>\r\n              </ul>\r\n            </nav>\r\n\r\n            <!-- 快速跳轉 -->\r\n            <div class=\"quick-jump\">\r\n              <div class=\"input-group input-group-sm\" style=\"width: 120px;\">\r\n                <input type=\"number\" class=\"form-control\" placeholder=\"頁碼\" [(ngModel)]=\"jumpToPage\"\r\n                  (keyup.enter)=\"jumpToPageAction()\" [min]=\"1\" [max]=\"totalPages\">\r\n                <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"jumpToPageAction()\" title=\"跳轉\">\r\n                  <i class=\"fas fa-arrow-right\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      <!-- 當沒有資料時顯示 -->\r\n      <div class=\"text-center mt-4\" *ngIf=\"flattenedHouses.length === 0 && houseChangeDates.length === 0\">\r\n        <nb-card>\r\n          <nb-card-body>\r\n            <div class=\"text-muted\">\r\n              <i class=\"fas fa-info-circle fa-2x mb-3\"></i>\r\n              <p>請選擇建案後查詢資料</p>\r\n            </div>\r\n          </nb-card-body>\r\n        </nb-card>\r\n      </div>\r\n\r\n      <!-- 載入中狀態 -->\r\n      <div class=\"text-center mt-4\" *ngIf=\"loading\">\r\n        <nb-card>\r\n          <nb-card-body>\r\n            <div class=\"text-muted\">\r\n              <div class=\"spinner-border\" role=\"status\">\r\n                <span class=\"sr-only\">載入中...</span>\r\n              </div>\r\n              <p class=\"mt-2\">資料載入中，請稍候...</p>\r\n            </div>\r\n          </nb-card-body>\r\n        </nb-card>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 批次設定對話框 -->\r\n<ng-template #batchSettingDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:600px; max-height: 90vh\">\r\n    <nb-card-header>\r\n      批次設定\r\n      <span *ngIf=\"selectedHouses.length > 0\" class=\"text-primary\">\r\n        - 已選擇 {{ selectedHouses.length }} 個戶別\r\n      </span>\r\n      <span *ngIf=\"selectedBuildingForBatch && selectedHouses.length === 0\">\r\n        - {{ selectedBuildingForBatch.name }}\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 時間設定 -->\r\n      <div class=\"form-group\">\r\n        <label>開放時間起訖 <span class=\"text-red-600\">*</span></label>\r\n        <div class=\"d-flex align-items-center\">\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchStartDate\"\r\n              [(ngModel)]=\"batchSettings.startDate\">\r\n            <nb-datepicker #batchStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <span class=\"mx-2\">~</span>\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchEndDate\"\r\n              [(ngModel)]=\"batchSettings.endDate\">\r\n            <nb-datepicker #batchEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 選擇範圍 -->\r\n      <div class=\"form-group\">\r\n        <label>適用範圍</label>\r\n        <div class=\"selection-options\">\r\n          <!-- 如果有已選擇的戶別，顯示已選擇的資訊 -->\r\n          <div *ngIf=\"selectedHouses.length > 0\" class=\"alert alert-info\">\r\n            <h6>將套用到已選擇的 {{ selectedHouses.length }} 個戶別：</h6>\r\n            <div class=\"selected-houses-preview\">\r\n              <span *ngFor=\"let house of selectedHouses.slice(0, 10); let i = index\"\r\n                class=\"badge badge-primary mr-1 mb-1\">\r\n                {{ house.CHouseHold }} ({{ house.CBuildingName }}-{{ house.CFloor }}F)\r\n              </span>\r\n              <span *ngIf=\"selectedHouses.length > 10\" class=\"text-muted\">\r\n                ...等 {{ selectedHouses.length - 10 }} 個\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 如果沒有已選擇的戶別，顯示選擇選項 -->\r\n          <div *ngIf=\"selectedHouses.length === 0\">\r\n            <nb-checkbox [(ngModel)]=\"batchSettings.applyToAll\">\r\n              全部戶別 ({{ flattenedHouses.length }} 個)\r\n            </nb-checkbox>\r\n            <div class=\"mt-2\" *ngIf=\"!batchSettings.applyToAll && selectedBuildingForBatch\">\r\n              <div class=\"floor-selection\" *ngFor=\"let floor of selectedBuildingForBatch.floors\">\r\n                <nb-checkbox [(ngModel)]=\"floor.selected\" (ngModelChange)=\"onFloorSelectionChange(floor)\">\r\n                  {{ floor.floorNumber }}F ({{ floor.houses.length }} 戶)\r\n                </nb-checkbox>\r\n                <div class=\"house-selection ml-4\" *ngIf=\"floor.selected\">\r\n                  <nb-checkbox *ngFor=\"let house of floor.houses\" [(ngModel)]=\"house.selected\"\r\n                    [disabled]=\"!house.CHouseId\">\r\n                    {{ house.CHouseHold }} ({{ house.CBuildingName }})\r\n                  </nb-checkbox>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-end\">\r\n      <button class=\"btn btn-secondary me-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onBatchSubmit(ref)\">\r\n        批次設定\r\n        <span *ngIf=\"selectedHouses.length > 0\">({{ selectedHouses.length }} 個戶別)</span>\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      您正在編輯 <span class=\"text-green-600\">{{selectedHouseChangeDate.CHouseHold}} -\r\n        {{selectedHouseChangeDate.CFloor}}F</span> 的選樣開放時段\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cFloorFrom\" class=\"label col-3\">開放時間起訖<span class=\"text-red-600\">*</span>\r\n        </label>\r\n        <nb-form-field class=\"ml-3\">\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeStartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeStartDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeStartDate\">\r\n          <nb-datepicker #changeStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n        <label for=\"CChangeStartDate\" class=\"label col-1\"> ~\r\n        </label>\r\n        <nb-form-field>\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeEndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeEndDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeEndDate\">\r\n          <nb-datepicker #changeEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AAUvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;;;ICCxEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IAQAR,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAK,WAAA,CAAkB;IACpET,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,WAAA,MACF;;;;;IA0EET,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAM,QAAA,CAAe;IAC9DV,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAG,QAAA,OACF;;;;;;IAeAV,EAAA,CAAAC,cAAA,iBAAqG;IAAvDD,EAAA,CAAAW,UAAA,mBAAAC,wFAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IACvElB,EAAA,CAAAmB,SAAA,YAAiC;IAAAnB,EAAA,CAAAE,MAAA,gCACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAxCXH,EAHN,CAAAC,cAAA,cAAyE,cAC/B,cACP,oBACd;IACbD,EAAA,CAAAmB,SAAA,kBAAkD;IAClDnB,EAAA,CAAAC,cAAA,gBAC+B;IADSD,EAAA,CAAAoB,gBAAA,2BAAAC,qFAAAC,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAU,aAAA,CAAAC,aAAA,EAAAJ,MAAA,MAAAP,MAAA,CAAAU,aAAA,CAAAC,aAAA,GAAAJ,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAyC;IAC/EtB,EAAA,CAAAW,UAAA,2BAAAU,qFAAA;MAAArB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAAY,QAAA,EAAU;IAAA,EAAC;IAElC3B,EAHI,CAAAG,YAAA,EAC+B,EACjB,EACZ;IAGJH,EADF,CAAAC,cAAA,cAA+B,oBACwE;IAAvED,EAAA,CAAAoB,gBAAA,2BAAAQ,yFAAAN,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAU,aAAA,CAAAI,YAAA,EAAAP,MAAA,MAAAP,MAAA,CAAAU,aAAA,CAAAI,YAAA,GAAAP,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAwC;IAACtB,EAAA,CAAAW,UAAA,4BAAAmB,0FAAA;MAAA9B,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAkBF,MAAA,CAAAY,QAAA,EAAU;IAAA,EAAC;IAClG3B,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,qBAA0B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA4B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAA+B,qBACuE;IAAtED,EAAA,CAAAoB,gBAAA,2BAAAW,0FAAAT,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAU,aAAA,CAAAO,WAAA,EAAAV,MAAA,MAAAP,MAAA,CAAAU,aAAA,CAAAO,WAAA,GAAAV,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAuC;IAACtB,EAAA,CAAAW,UAAA,4BAAAsB,2FAAA;MAAAjC,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAkBF,MAAA,CAAAY,QAAA,EAAU;IAAA,EAAC;IACjG3B,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAkC,UAAA,KAAAC,kEAAA,wBAAiE;IAIrEnC,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,eAA+B,qBAC8D;IAA7DD,EAAA,CAAAoB,gBAAA,2BAAAgB,0FAAAd,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAsB,QAAA,EAAAf,MAAA,MAAAP,MAAA,CAAAsB,QAAA,GAAAf,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAsB;IAACtB,EAAA,CAAAW,UAAA,4BAAA2B,2FAAA;MAAAtC,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAkBF,MAAA,CAAAwB,gBAAA,EAAkB;IAAA,EAAC;IACxFvC,EAAA,CAAAC,cAAA,qBAAwB;IAAAD,EAAA,CAAAE,MAAA,uBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAAgC,eACF;IAC1BD,EAAA,CAAAkC,UAAA,KAAAM,+DAAA,qBAAqG;IAM7GxC,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IA1C0CH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAU,aAAA,CAAAC,aAAA,CAAyC;IAMrD1B,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAU,aAAA,CAAAI,YAAA,CAAwC;IAWxC7B,EAAA,CAAAM,SAAA,IAAuC;IAAvCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAU,aAAA,CAAAO,WAAA,CAAuC;IAEtChC,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAA2B,eAAA,CAAkB;IAOnB1C,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAsB,QAAA,CAAsB;IACvCrC,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAI,UAAA,aAAY;IACZJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IAMmDJ,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA4B,gBAAA,GAAwB;;;;;IAiBnG3C,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAmB,SAAA,aAAqD;IACrDnB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,eACvE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAD2BH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA8B,cAAA,CAAAC,MAAA,CAA2B;;;;;;IAwCzD9C,EADF,CAAAC,cAAA,eAAkE,kBAC0B;IAAnDD,EAAA,CAAAW,UAAA,mBAAAoC,qFAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAAmC,IAAA;MAAA,MAAAjC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAkC,gBAAA,EAAkB;IAAA,EAAC;IACjEjD,EAAA,CAAAmB,SAAA,aAAgC;IAAAnB,EAAA,CAAAE,MAAA,gCAChC;IAAAF,EAAA,CAAAC,cAAA,gBAA4C;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IACzEF,EADyE,CAAAG,YAAA,EAAO,EACvE;IACTH,EAAA,CAAAC,cAAA,kBAA2F;IAAxCD,EAAA,CAAAW,UAAA,mBAAAuC,qFAAA;MAAAlD,EAAA,CAAAa,aAAA,CAAAmC,IAAA;MAAA,MAAAjC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAoC,cAAA,EAAgB;IAAA,EAAC;IAC3EnD,EAAA,CAAAmB,SAAA,YAAiC;IAAAnB,EAAA,CAAAE,MAAA,gCACnC;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAL0CH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA8B,cAAA,CAAAC,MAAA,CAA2B;;;;;IAiGvE9C,EAAA,CAAAC,cAAA,iBAA0D;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAWrEH,EAAA,CAAAC,cAAA,gBAA0D;IACxDD,EAAA,CAAAmB,SAAA,aAAiD;IACjDnB,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAoD,WAAA,OAAAC,SAAA,CAAAC,gBAAA,qBACF;;;;;IACAtD,EAAA,CAAAC,cAAA,gBAA2D;IACzDD,EAAA,CAAAmB,SAAA,YAAgD;IAAAnB,EAAA,CAAAE,MAAA,0BAClD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAKPH,EAAA,CAAAC,cAAA,gBAAwD;IACtDD,EAAA,CAAAmB,SAAA,aAAgD;IAChDnB,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAoD,WAAA,OAAAC,SAAA,CAAAE,cAAA,qBACF;;;;;IACAvD,EAAA,CAAAC,cAAA,gBAAyD;IACvDD,EAAA,CAAAmB,SAAA,YAAgD;IAAAnB,EAAA,CAAAE,MAAA,0BAClD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAlCTH,EAHJ,CAAAC,cAAA,SAC2F,cACjE,uBAEuB;IADhCD,EAAA,CAAAoB,gBAAA,2BAAAoC,iGAAAlC,MAAA;MAAA,MAAA+B,SAAA,GAAArD,EAAA,CAAAa,aAAA,CAAA4C,IAAA,EAAAC,SAAA;MAAA1D,EAAA,CAAAwB,kBAAA,CAAA6B,SAAA,CAAAM,QAAA,EAAArC,MAAA,MAAA+B,SAAA,CAAAM,QAAA,GAAArC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAA4B;IACvCtB,EAAA,CAAAW,UAAA,2BAAA6C,iGAAA;MAAAxD,EAAA,CAAAa,aAAA,CAAA4C,IAAA;MAAA,MAAA1C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAA6C,sBAAA,EAAwB;IAAA,EAAC;IAC9C5D,EAD+C,CAAAG,YAAA,EAAc,EACxD;IAGDH,EAFJ,CAAAC,cAAA,SAAI,eACsB,gBACa;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAAkC,UAAA,IAAA2B,mEAAA,qBAA0D;IAE9D7D,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,SAAI,gBAC0B;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACzD;IAEHH,EADF,CAAAC,cAAA,eAAwB,iBACI;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAC/CF,EAD+C,CAAAG,YAAA,EAAO,EACjD;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACqB;IAKrBD,EAJA,CAAAkC,UAAA,KAAA4B,mEAAA,oBAA0D,KAAAC,mEAAA,oBAIC;IAI/D/D,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACqB;IAKrBD,EAJA,CAAAkC,UAAA,KAAA8B,mEAAA,oBAAwD,KAAAC,mEAAA,oBAIC;IAI7DjE,EADE,CAAAG,YAAA,EAAM,EACH;IAGDH,EAFJ,CAAAC,cAAA,eAAwB,gBACM,iBAC0C;IAClED,EAAA,CAAAmB,SAAA,cAA0D;IAC1DnB,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAG1DF,EAH0D,CAAAG,YAAA,EAAO,EACtD,EACH,EACH;IAGDH,EAFJ,CAAAC,cAAA,eAAwB,gBACM,mBAE0B;IAAlDD,EAAA,CAAAW,UAAA,mBAAAuD,qFAAA;MAAA,MAAAb,SAAA,GAAArD,EAAA,CAAAa,aAAA,CAAA4C,IAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAmD,UAAA,GAAAnE,EAAA,CAAAoE,WAAA;MAAA,OAAApE,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsD,SAAA,CAAAF,UAAA,EAAAd,SAAA,CAAwB;IAAA,EAAC;IAClCrD,EAAA,CAAAmB,SAAA,cAA2B;IAInCnB,EAHM,CAAAG,YAAA,EAAS,EACL,EACH,EACF;;;;;IAvDyCH,EAA5C,CAAAsE,WAAA,uBAAAjB,SAAA,CAAAM,QAAA,CAA2C,wBAAAN,SAAA,CAAAkB,QAAA,CAA6C;IAEzEvE,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAyC,gBAAA,YAAAY,SAAA,CAAAM,QAAA,CAA4B;IAAC3D,EAAA,CAAAI,UAAA,cAAAiD,SAAA,CAAAkB,QAAA,CAA4B;IAKjCvE,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAA4C,iBAAA,CAAAS,SAAA,CAAAmB,UAAA,CAAsB;IACtBxE,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAI,UAAA,UAAAiD,SAAA,CAAAkB,QAAA,CAAqB;IAI9BvE,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA4C,iBAAA,CAAAS,SAAA,CAAAoB,aAAA,CAAyB;IAG3BzE,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,kBAAA,KAAA8C,SAAA,CAAAqB,MAAA,MAAmB;IAIpC1E,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,SAAAiD,SAAA,CAAAC,gBAAA,CAA4B;IAI5BtD,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAI,UAAA,UAAAiD,SAAA,CAAAC,gBAAA,CAA6B;IAO7BtD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,SAAAiD,SAAA,CAAAE,cAAA,CAA0B;IAI1BvD,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAAiD,SAAA,CAAAE,cAAA,CAA2B;IAOEvD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAA2E,UAAA,CAAA5D,MAAA,CAAA6D,cAAA,CAAAvB,SAAA,EAA+B;IAC1CrD,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAA2E,UAAA,CAAA5D,MAAA,CAAA8D,aAAA,CAAAxB,SAAA,EAA8B;IAC3BrD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA+D,aAAA,CAAAzB,SAAA,EAA0B;IAMPrD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,cAAAiD,SAAA,CAAAkB,QAAA,CAA4B;;;;;;IAyC7EvE,EADF,CAAAC,cAAA,cAAmG,kBAC9C;IAAzBD,EAAA,CAAAW,UAAA,mBAAAoE,2FAAA;MAAA,MAAAC,QAAA,GAAAhF,EAAA,CAAAa,aAAA,CAAAoE,IAAA,EAAAvB,SAAA;MAAA,MAAA3C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAF,QAAA,CAAc;IAAA,EAAC;IAAChF,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFwDH,EAAA,CAAAsE,WAAA,WAAAU,QAAA,KAAAjE,MAAA,CAAAoE,WAAA,CAAqC;IAC7CnF,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAA4C,iBAAA,CAAAoC,QAAA,CAAU;;;;;;IA1BjEhF,EAHN,CAAAC,cAAA,eAAkE,cACD,eACvB,eACX;IACvBD,EAAA,CAAAE,MAAA,eAAE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,gBAC3E;IAAAF,EAAA,CAAAC,cAAA,iBAAmB;IACjBD,EAAA,CAAAE,MAAA,IAGF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACF,EACH;IAKAH,EAHN,CAAAC,cAAA,gBAA4B,eACgB,eACmB,mBACkC;IAAjED,EAAA,CAAAW,UAAA,mBAAAyE,sFAAA;MAAApF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAC7ClF,EAAA,CAAAmB,SAAA,cAAwC;IAE5CnB,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,eAA2D,mBAE3C;IADYD,EAAA,CAAAW,UAAA,mBAAA2E,sFAAA;MAAAtF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAnE,MAAA,CAAAoE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAE3DnF,EAAA,CAAAmB,SAAA,cAAiC;IAErCnB,EADE,CAAAG,YAAA,EAAS,EACN;IAGLH,EAAA,CAAAkC,UAAA,KAAAqD,kEAAA,kBAAmG;IAKjGvF,EADF,CAAAC,cAAA,eAAoE,mBAEpD;IADYD,EAAA,CAAAW,UAAA,mBAAA6E,sFAAA;MAAAxF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAnE,MAAA,CAAAoE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAE3DnF,EAAA,CAAAmB,SAAA,cAAkC;IAEtCnB,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,eAAoE,mBAEnD;IADWD,EAAA,CAAAW,UAAA,mBAAA8E,sFAAA;MAAAzF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAnE,MAAA,CAAA2E,UAAA,CAAoB;IAAA,EAAC;IAEtD1F,EAAA,CAAAmB,SAAA,cAAyC;IAIjDnB,EAHM,CAAAG,YAAA,EAAS,EACN,EACF,EACD;IAKFH,EAFJ,CAAAC,cAAA,gBAAwB,gBACwC,kBAEM;IADPD,EAAA,CAAAoB,gBAAA,2BAAAuE,6FAAArE,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA6E,UAAA,EAAAtE,MAAA,MAAAP,MAAA,CAAA6E,UAAA,GAAAtE,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAwB;IACjFtB,EAAA,CAAAW,UAAA,yBAAAkF,2FAAA;MAAA7F,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAeF,MAAA,CAAA+E,gBAAA,EAAkB;IAAA,EAAC;IADpC9F,EAAA,CAAAG,YAAA,EACkE;IAClEH,EAAA,CAAAC,cAAA,mBAAgG;IAAxCD,EAAA,CAAAW,UAAA,mBAAAoF,sFAAA;MAAA/F,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA+E,gBAAA,EAAkB;IAAA,EAAC;IAClF9F,EAAA,CAAAmB,SAAA,cAAkC;IAK5CnB,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAtDYH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAAoE,WAAA,CAAiB;IAAsBnF,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA2E,UAAA,CAAgB;IAE/D1F,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAgG,kBAAA,2BAAAjF,MAAA,CAAAoE,WAAA,QAAApE,MAAA,CAAAsB,QAAA,aAAAtB,MAAA,CAAAkF,IAAA,CAAAC,GAAA,CAAAnF,MAAA,CAAAoE,WAAA,GAAApE,MAAA,CAAAsB,QAAA,EAAAtB,MAAA,CAAAoF,cAAA,CAAArD,MAAA,4BAAA/B,MAAA,CAAAoF,cAAA,CAAArD,MAAA,cAGF;IAMsB9C,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,OAAoC;IACRnF,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,OAA8B;IAI1DnF,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,OAAoC;IACMnF,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,OAA8B;IAOvDnF,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAqF,eAAA,GAAoB;IAIrCpG,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAA6C;IACH1F,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAAuC;IAKjF1F,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAA6C;IACR1F,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAAuC;IAWvC1F,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA6E,UAAA,CAAwB;IACpC5F,EAAV,CAAAI,UAAA,UAAS,QAAAW,MAAA,CAAA2E,UAAA,CAAmB;;;;;;IAzPnE1F,EALR,CAAAC,cAAA,cAAyE,cAEzC,cACmC,cACnC,eACE;IACxBD,EAAA,CAAAmB,SAAA,YAAoC;IACpCnB,EAAA,CAAAE,MAAA,eAAE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BACjD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAkC,UAAA,KAAAmE,6DAAA,mBAA+D;IAIjErG,EAAA,CAAAG,YAAA,EAAM;IAGFH,EAFJ,CAAAC,cAAA,eAA2B,eACwB,kBAGV;IAAnCD,EAAA,CAAAW,UAAA,mBAAA2F,+EAAA;MAAAtG,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,QAAQ,CAAC;IAAA,EAAC;IAClCxG,EAAA,CAAAmB,SAAA,aAAuC;IAAAnB,EAAA,CAAAE,MAAA,2BACzC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEsC;IAApCD,EAAA,CAAAW,UAAA,mBAAA8F,+EAAA;MAAAzG,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnCxG,EAAA,CAAAmB,SAAA,aAAiC;IAAAnB,EAAA,CAAAE,MAAA,2BACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEsC;IAApCD,EAAA,CAAAW,UAAA,mBAAA+F,+EAAA;MAAA1G,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnCxG,EAAA,CAAAmB,SAAA,aAAwC;IAAAnB,EAAA,CAAAE,MAAA,2BAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGsC;IAApCD,EAAA,CAAAW,UAAA,mBAAAgG,+EAAA;MAAA3G,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnCxG,EAAA,CAAAmB,SAAA,aAAgD;IAAAnB,EAAA,CAAAE,MAAA,2BAClD;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;IAOEH,EAJR,CAAAC,cAAA,eAA8B,eACmC,eAC/B,eACI,uBACyE;IAA1FD,EAAA,CAAAoB,gBAAA,2BAAAwF,4FAAAtF,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA8F,SAAA,EAAAvF,MAAA,MAAAP,MAAA,CAAA8F,SAAA,GAAAvF,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAuB;IAACtB,EAAA,CAAAW,UAAA,2BAAAiG,4FAAA;MAAA5G,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAA+F,iBAAA,EAAmB;IAAA,EAAC;IACxE9G,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EACrB;IACdH,EAAA,CAAAkC,UAAA,KAAA6E,4DAAA,kBAAkE;IAUtE/G,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA4B,eACmB,kBAEL;IAApCD,EAAA,CAAAW,UAAA,mBAAAqG,+EAAA;MAAAhH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAkG,UAAA,EAAY;IAAA,EAAC;IACtBjH,EAAA,CAAAmB,SAAA,aAAoC;IAAAnB,EAAA,CAAAE,MAAA,qBACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,eAAgC,iBACJ;IACxBD,EAAA,CAAAE,MAAA,IAGF;IAKVF,EALU,CAAAG,YAAA,EAAQ,EACJ,EACF,EACF,EACF,EACF;IAQIH,EALV,CAAAC,cAAA,eAAsC,iBACY,iBACT,UAC/B,cACiC,uBAC0C;IAA9DD,EAAA,CAAAoB,gBAAA,2BAAA8F,4FAAA5F,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA8F,SAAA,EAAAvF,MAAA,MAAAP,MAAA,CAAA8F,SAAA,GAAAvF,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAuB;IAACtB,EAAA,CAAAW,UAAA,2BAAAuG,4FAAA;MAAAlH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAA+F,iBAAA,EAAmB;IAAA,EAAC;IAC5E9G,EAD6E,CAAAG,YAAA,EAAc,EACtF;IACLH,EAAA,CAAAC,cAAA,cAA8D;IAA9CD,EAAA,CAAAW,UAAA,mBAAAwG,2EAAA;MAAAnH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqG,IAAA,CAAK,YAAY,CAAC;IAAA,EAAC;IAC1CpH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAmB,SAAA,aAEoF;IAExFnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,cAAiE;IAAjDD,EAAA,CAAAW,UAAA,mBAAA0G,2EAAA;MAAArH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqG,IAAA,CAAK,eAAe,CAAC;IAAA,EAAC;IAC7CpH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAmB,SAAA,aAEuF;IAE3FnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,cAAqE;IAAtDD,EAAA,CAAAW,UAAA,mBAAA2G,2EAAA;MAAAtH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqG,IAAA,CAAK,QAAQ,CAAC;IAAA,EAAC;IACrCpH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAmB,SAAA,aAEgF;IAEpFnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,eAAoE;IAApDD,EAAA,CAAAW,UAAA,mBAAA4G,2EAAA;MAAAvH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqG,IAAA,CAAK,kBAAkB,CAAC;IAAA,EAAC;IAChDpH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAmB,SAAA,aAE0F;IAE9FnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,eAAkE;IAAlDD,EAAA,CAAAW,UAAA,mBAAA6G,2EAAA;MAAAxH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqG,IAAA,CAAK,gBAAgB,CAAC;IAAA,EAAC;IAC9CpH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAmB,SAAA,aAEwF;IAE5FnB,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,eAAoC,eACN;IAC1BD,EAAA,CAAAE,MAAA,sBACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,eAAoC,eACN;IAC1BD,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAM,EACH,EACF,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAkC,UAAA,KAAAuF,2DAAA,oBAC2F;IA0DjGzH,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAGNH,EAAA,CAAAkC,UAAA,KAAAwF,4DAAA,qBAAkE;IA2DpE1H,EAAA,CAAAG,YAAA,EAAM;;;;IA/PcH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAAoF,cAAA,CAAArD,MAAA,CAA2B;IAET9C,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAOzB9C,EAAA,CAAAM,SAAA,GAA6D;IAC7FN,EADgC,CAAAsE,WAAA,gBAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,cAA6D,wBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,cACxB;IAIrC7B,EAAA,CAAAM,SAAA,GAA8D;IAC9FN,EADgC,CAAAsE,WAAA,gBAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,eAA8D,wBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,eACxB;IAItC7B,EAAA,CAAAM,SAAA,GAA6D;IAC7FN,EADgC,CAAAsE,WAAA,eAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,eAA6D,uBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,eACxB;IAKrE7B,EAAA,CAAAM,SAAA,GAAqE;IACrEN,EADA,CAAAsE,WAAA,uBAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,eAAqE,wBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,eACC;IAc3D7B,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA8F,SAAA,CAAuB;IAGH7G,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAcjB9C,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoF,cAAA,CAAArD,MAAA,OAAwC;IAMnF9C,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAgG,kBAAA,oBAAAjF,MAAA,CAAAoE,WAAA,QAAApE,MAAA,CAAAsB,QAAA,aAAAtB,MAAA,CAAAkF,IAAA,CAAAC,GAAA,CAAAnF,MAAA,CAAAoE,WAAA,GAAApE,MAAA,CAAAsB,QAAA,EAAAtB,MAAA,CAAAoF,cAAA,CAAArD,MAAA,iBAAA/B,MAAA,CAAAoF,cAAA,CAAArD,MAAA,aAGF;IAaa9C,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA8F,SAAA,CAAuB;IAMhC7G,EAAA,CAAAM,SAAA,GAA0E;IAC1EN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA4G,SAAA,qBAAA5G,MAAA,CAAA6G,aAAA,WAA0E,iBAAA7G,MAAA,CAAA4G,SAAA,qBAAA5G,MAAA,CAAA6G,aAAA,YACG;IAO7E5H,EAAA,CAAAM,SAAA,GAA6E;IAC7EN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA4G,SAAA,wBAAA5G,MAAA,CAAA6G,aAAA,WAA6E,iBAAA7G,MAAA,CAAA4G,SAAA,wBAAA5G,MAAA,CAAA6G,aAAA,YACG;IAOhF5H,EAAA,CAAAM,SAAA,GAAsE;IACtEN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA4G,SAAA,iBAAA5G,MAAA,CAAA6G,aAAA,WAAsE,iBAAA7G,MAAA,CAAA4G,SAAA,iBAAA5G,MAAA,CAAA6G,aAAA,YACG;IAOzE5H,EAAA,CAAAM,SAAA,GAAgF;IAChFN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA4G,SAAA,2BAAA5G,MAAA,CAAA6G,aAAA,WAAgF,iBAAA7G,MAAA,CAAA4G,SAAA,2BAAA5G,MAAA,CAAA6G,aAAA,YACG;IAOnF5H,EAAA,CAAAM,SAAA,GAA8E;IAC9EN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA4G,SAAA,yBAAA5G,MAAA,CAAA6G,aAAA,WAA8E,iBAAA7G,MAAA,CAAA4G,SAAA,yBAAA5G,MAAA,CAAA6G,aAAA,YACG;IAgBnE5H,EAAA,CAAAM,SAAA,GAAoB;IAAAN,EAApB,CAAAI,UAAA,YAAAW,MAAA,CAAA8G,eAAA,CAAoB,iBAAA9G,MAAA,CAAA+G,cAAA,CAAuB;IA8D3B9H,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA2E,UAAA,KAAoB;;;;;IAmE5D1F,EAHN,CAAAC,cAAA,eAAoG,cACzF,mBACO,cACY;IACtBD,EAAA,CAAAmB,SAAA,aAA6C;IAC7CnB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IAIrBF,EAJqB,CAAAG,YAAA,EAAI,EACb,EACO,EACP,EACN;;;;;IAQIH,EALV,CAAAC,cAAA,eAA8C,cACnC,mBACO,cACY,eACoB,gBAClB;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAE,MAAA,gEAAY;IAIpCF,EAJoC,CAAAG,YAAA,EAAI,EAC5B,EACO,EACP,EACN;;;;;;IApXAH,EARZ,CAAAC,cAAA,kBAAiE,uBACrC,cAEe,cAEL,cACO,cACJ,gBACO;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEhDH,EADF,CAAAC,cAAA,cAA8B,oBACb;IACbD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,iBAC6C;IAA3CD,EAAA,CAAAoB,gBAAA,2BAAA2G,+EAAAzG,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAmH,GAAA;MAAA,MAAAjH,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAkH,WAAA,CAAA3E,gBAAA,EAAAhC,MAAA,MAAAP,MAAA,CAAAkH,WAAA,CAAA3E,gBAAA,GAAAhC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAA0C;IAD5CtB,EAAA,CAAAG,YAAA,EAC6C;IAC7CH,EAAA,CAAAmB,SAAA,4BAA8D;IAChEnB,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,iBAC2C;IAAzCD,EAAA,CAAAoB,gBAAA,2BAAA8G,+EAAA5G,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAmH,GAAA;MAAA,MAAAjH,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAkH,WAAA,CAAA1E,cAAA,EAAAjC,MAAA,MAAAP,MAAA,CAAAkH,WAAA,CAAA1E,cAAA,GAAAjC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAwC;IAD1CtB,EAAA,CAAAG,YAAA,EAC2C;IAC3CH,EAAA,CAAAmB,SAAA,4BAA4D;IAGlEnB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA+B,eACF,kBAC2D;IAApDD,EAAA,CAAAW,UAAA,mBAAAwH,wEAAA;MAAAnI,EAAA,CAAAa,aAAA,CAAAmH,GAAA;MAAA,MAAAjH,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqH,kBAAA,EAAoB;IAAA,EAAC;IAC5DpI,EAAA,CAAAmB,SAAA,aAAkC;IAAAnB,EAAA,CAAAE,MAAA,qBACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACiB;IAD8BD,EAAA,CAAAW,UAAA,mBAAA0H,wEAAA;MAAArI,EAAA,CAAAa,aAAA,CAAAmH,GAAA;MAAA,MAAAjH,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAuH,YAAA,EAAc;IAAA,EAAC;IAErEtI,EAAA,CAAAmB,SAAA,aAA2B;IAKrCnB,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;IA2UNH,EAxUA,CAAAkC,UAAA,KAAAqG,qDAAA,oBAAyE,KAAAC,qDAAA,oBAiDA,KAAAC,qDAAA,kBA2Q2B,KAAAC,qDAAA,kBAYtD;IAcpD1I,EAFI,CAAAG,YAAA,EAAM,EACO,EACP;;;;;;IAnXoDH,EAAA,CAAAM,SAAA,IAA0B;IAA1BN,EAAA,CAAAI,UAAA,iBAAAuI,aAAA,CAA0B;IACtE3I,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAkH,WAAA,CAAA3E,gBAAA,CAA0C;IAMEtD,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,iBAAAwI,WAAA,CAAwB;IACpE5I,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAkH,WAAA,CAAA1E,cAAA,CAAwC;IAQmBvD,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAA8H,OAAA,CAAoB;IAGX7I,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAA8H,OAAA,CAAoB;IAU7D7I,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA+H,eAAA,CAAAhG,MAAA,KAAgC;IAiDhC9C,EAAA,CAAAM,SAAA,EAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA+H,eAAA,CAAAhG,MAAA,KAAgC;IA2QxC9C,EAAA,CAAAM,SAAA,EAAmE;IAAnEN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA+H,eAAA,CAAAhG,MAAA,UAAA/B,MAAA,CAAAgI,gBAAA,CAAAjG,MAAA,OAAmE;IAYnE9C,EAAA,CAAAM,SAAA,EAAa;IAAbN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8H,OAAA,CAAa;;;;;IAqB5C7I,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,2BAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,yBACF;;;;;IACA9C,EAAA,CAAAC,cAAA,WAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,QAAAQ,MAAA,CAAAiI,wBAAA,CAAAC,IAAA,MACF;;;;;IA+BQjJ,EAAA,CAAAC,cAAA,gBACwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAgG,kBAAA,MAAAkD,SAAA,CAAA1E,UAAA,QAAA0E,SAAA,CAAAzE,aAAA,OAAAyE,SAAA,CAAAxE,MAAA,QACF;;;;;IACA1E,EAAA,CAAAC,cAAA,eAA4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gBAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,kBACF;;;;;IARF9C,EADF,CAAAC,cAAA,eAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,eAAqC;IAKnCD,EAJA,CAAAkC,UAAA,IAAAiH,gEAAA,oBACwC,IAAAC,gEAAA,oBAGoB;IAIhEpJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAVAH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAO,kBAAA,sDAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,8BAAyC;IAEnB9C,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAA8B,cAAA,CAAAwG,KAAA,QAAgC;IAIjDrJ,EAAA,CAAAM,SAAA,EAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,MAAgC;;;;;;IAiBnC9C,EAAA,CAAAC,cAAA,uBAC+B;IADiBD,EAAA,CAAAoB,gBAAA,2BAAAkI,+HAAAhI,MAAA;MAAA,MAAAiI,SAAA,GAAAvJ,EAAA,CAAAa,aAAA,CAAA2I,IAAA,EAAA9F,SAAA;MAAA1D,EAAA,CAAAwB,kBAAA,CAAA+H,SAAA,CAAA5F,QAAA,EAAArC,MAAA,MAAAiI,SAAA,CAAA5F,QAAA,GAAArC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAA4B;IAE1EtB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;;;;IAHkCH,EAAA,CAAAyC,gBAAA,YAAA8G,SAAA,CAAA5F,QAAA,CAA4B;IAC1E3D,EAAA,CAAAI,UAAA,cAAAmJ,SAAA,CAAAhF,QAAA,CAA4B;IAC5BvE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAyJ,kBAAA,MAAAF,SAAA,CAAA/E,UAAA,QAAA+E,SAAA,CAAA9E,aAAA,OACF;;;;;IAJFzE,EAAA,CAAAC,cAAA,eAAyD;IACvDD,EAAA,CAAAkC,UAAA,IAAAwH,yFAAA,2BAC+B;IAGjC1J,EAAA,CAAAG,YAAA,EAAM;;;;IAJ2BH,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAI,UAAA,YAAAuJ,SAAA,CAAAC,MAAA,CAAe;;;;;;IAJhD5J,EADF,CAAAC,cAAA,eAAmF,sBACS;IAA7ED,EAAA,CAAAoB,gBAAA,2BAAAyI,2GAAAvI,MAAA;MAAA,MAAAqI,SAAA,GAAA3J,EAAA,CAAAa,aAAA,CAAAiJ,IAAA,EAAApG,SAAA;MAAA1D,EAAA,CAAAwB,kBAAA,CAAAmI,SAAA,CAAAhG,QAAA,EAAArC,MAAA,MAAAqI,SAAA,CAAAhG,QAAA,GAAArC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAA4B;IAACtB,EAAA,CAAAW,UAAA,2BAAAkJ,2GAAA;MAAA,MAAAF,SAAA,GAAA3J,EAAA,CAAAa,aAAA,CAAAiJ,IAAA,EAAApG,SAAA;MAAA,MAAA3C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAAgJ,sBAAA,CAAAJ,SAAA,CAA6B;IAAA,EAAC;IACvF3J,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAkC,UAAA,IAAA8H,2EAAA,mBAAyD;IAM3DhK,EAAA,CAAAG,YAAA,EAAM;;;;IATSH,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAAyC,gBAAA,YAAAkH,SAAA,CAAAhG,QAAA,CAA4B;IACvC3D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAyJ,kBAAA,MAAAE,SAAA,CAAAM,WAAA,SAAAN,SAAA,CAAAC,MAAA,CAAA9G,MAAA,cACF;IACmC9C,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAuJ,SAAA,CAAAhG,QAAA,CAAoB;;;;;IAL3D3D,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAkC,UAAA,IAAAgI,qEAAA,mBAAmF;IAWrFlK,EAAA,CAAAG,YAAA,EAAM;;;;IAX2CH,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAiI,wBAAA,CAAAmB,MAAA,CAAkC;;;;;;IAJnFnK,EADF,CAAAC,cAAA,UAAyC,sBACa;IAAvCD,EAAA,CAAAoB,gBAAA,2BAAAgJ,+FAAA9I,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAwJ,IAAA;MAAA,MAAAtJ,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAuJ,aAAA,CAAAC,UAAA,EAAAjJ,MAAA,MAAAP,MAAA,CAAAuJ,aAAA,CAAAC,UAAA,GAAAjJ,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAsC;IACjDtB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAkC,UAAA,IAAAsI,+DAAA,mBAAgF;IAalFxK,EAAA,CAAAG,YAAA,EAAM;;;;IAhBSH,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAuJ,aAAA,CAAAC,UAAA,CAAsC;IACjDvK,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gCAAAQ,MAAA,CAAA+H,eAAA,CAAAhG,MAAA,cACF;IACmB9C,EAAA,CAAAM,SAAA,EAA2D;IAA3DN,EAAA,CAAAI,UAAA,UAAAW,MAAA,CAAAuJ,aAAA,CAAAC,UAAA,IAAAxJ,MAAA,CAAAiI,wBAAA,CAA2D;;;;;IAqBlFhJ,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAxCH,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAO,kBAAA,MAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,yBAAiC;;;;;;IA1E7E9C,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,iCACA;IAGAF,EAHA,CAAAkC,UAAA,IAAAuI,yDAAA,oBAA6D,IAAAC,yDAAA,oBAGS;IAGxE1K,EAAA,CAAAG,YAAA,EAAiB;IAIbH,EAHJ,CAAAC,cAAA,mBAAc,eAEY,YACf;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAEvDH,EADF,CAAAC,cAAA,gBAAuC,0BACJ;IAC/BD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBACwC;IAAtCD,EAAA,CAAAoB,gBAAA,2BAAAuJ,mFAAArJ,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA+J,IAAA;MAAA,MAAA7J,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAuJ,aAAA,CAAAO,SAAA,EAAAvJ,MAAA,MAAAP,MAAA,CAAAuJ,aAAA,CAAAO,SAAA,GAAAvJ,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAqC;IADvCtB,EAAA,CAAAG,YAAA,EACwC;IACxCH,EAAA,CAAAmB,SAAA,4BAAmE;IACrEnB,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,iBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,0BAAiC;IAC/BD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBACsC;IAApCD,EAAA,CAAAoB,gBAAA,2BAAA0J,mFAAAxJ,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA+J,IAAA;MAAA,MAAA7J,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAuJ,aAAA,CAAAS,OAAA,EAAAzJ,MAAA,MAAAP,MAAA,CAAAuJ,aAAA,CAAAS,OAAA,GAAAzJ,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAmC;IADrCtB,EAAA,CAAAG,YAAA,EACsC;IACtCH,EAAA,CAAAmB,SAAA,4BAAiE;IAGvEnB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAwB,aACf;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnBH,EAAA,CAAAC,cAAA,gBAA+B;IAgB7BD,EAdA,CAAAkC,UAAA,KAAA8I,yDAAA,mBAAgE,KAAAC,yDAAA,mBAcvB;IAoB/CjL,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAmD,mBACa;IAAvBD,EAAA,CAAAW,UAAA,mBAAAuK,4EAAA;MAAA,MAAAC,OAAA,GAAAnL,EAAA,CAAAa,aAAA,CAAA+J,IAAA,EAAAQ,SAAA;MAAA,MAAArK,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsK,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAACnL,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzEH,EAAA,CAAAC,cAAA,mBAA6D;IAA7BD,EAAA,CAAAW,UAAA,mBAAA2K,4EAAA;MAAA,MAAAH,OAAA,GAAAnL,EAAA,CAAAa,aAAA,CAAA+J,IAAA,EAAAQ,SAAA;MAAA,MAAArK,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAwK,aAAA,CAAAJ,OAAA,CAAkB;IAAA,EAAC;IAC1DnL,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAkC,UAAA,KAAAsJ,0DAAA,oBAAwC;IAG9CxL,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;;;IA3ECH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAG/B9C,EAAA,CAAAM,SAAA,EAA6D;IAA7DN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAiI,wBAAA,IAAAjI,MAAA,CAAA8B,cAAA,CAAAC,MAAA,OAA6D;IAWf9C,EAAA,CAAAM,SAAA,IAA+B;IAA/BN,EAAA,CAAAI,UAAA,iBAAAqL,kBAAA,CAA+B;IAC5EzL,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAuJ,aAAA,CAAAO,SAAA,CAAqC;IAMQ7K,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,iBAAAsL,gBAAA,CAA6B;IAC1E1L,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAuJ,aAAA,CAAAS,OAAA,CAAmC;IAWjC/K,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAc/B9C,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,OAAiC;IAyBlC9C,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;;;;;IAO5C9C,EAAA,CAAAC,cAAA,mBAA+C;IAM7CD,EALA,CAAAmB,SAAA,qBACiB,mBAGF,0BAGE;IACnBnB,EAAA,CAAAG,YAAA,EAAU;;;;;;IAKRH,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GACE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mDAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,wBAA2B,eACyB,iBACJ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC7E;IACRH,EAAA,CAAAC,cAAA,0BAA4B;IAC1BD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBAC8E;IAAvDD,EAAA,CAAAoB,gBAAA,2BAAAuK,mFAAArK,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA+K,IAAA;MAAA,MAAA7K,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA8K,uBAAA,CAAAvI,gBAAA,EAAAhC,MAAA,MAAAP,MAAA,CAAA8K,uBAAA,CAAAvI,gBAAA,GAAAhC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAsD;IAD7EtB,EAAA,CAAAG,YAAA,EAC8E;IAC9EH,EAAA,CAAAmB,SAAA,4BAAoE;IACtEnB,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,kBAAkD;IAACD,EAAA,CAAAE,MAAA,WACnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBAC4E;IAArDD,EAAA,CAAAoB,gBAAA,2BAAA0K,mFAAAxK,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA+K,IAAA;MAAA,MAAA7K,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA8K,uBAAA,CAAAtI,cAAA,EAAAjC,MAAA,MAAAP,MAAA,CAAA8K,uBAAA,CAAAtI,cAAA,GAAAjC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAoD;IAD3EtB,EAAA,CAAAG,YAAA,EAC4E;IAC5EH,EAAA,CAAAmB,SAAA,4BAAkE;IAGxEnB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAsD,mBACiB;IAAvBD,EAAA,CAAAW,UAAA,mBAAAoL,4EAAA;MAAA,MAAAC,OAAA,GAAAhM,EAAA,CAAAa,aAAA,CAAA+K,IAAA,EAAAR,SAAA;MAAA,MAAArK,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsK,OAAA,CAAAW,OAAA,CAAY;IAAA,EAAC;IAAChM,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,mBAAoE;IAAxBD,EAAA,CAAAW,UAAA,mBAAAsL,4EAAA;MAAA,MAAAD,OAAA,GAAAhM,EAAA,CAAAa,aAAA,CAAA+K,IAAA,EAAAR,SAAA;MAAA,MAAArK,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmL,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IAAChM,EAAA,CAAAE,MAAA,oBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAChE,EACT;;;;;;IA3B6BH,EAAA,CAAAM,SAAA,GACE;IADFN,EAAA,CAAAyJ,kBAAA,KAAA1I,MAAA,CAAA8K,uBAAA,CAAArH,UAAA,SAAAzD,MAAA,CAAA8K,uBAAA,CAAAnH,MAAA,MACE;IAQoC1E,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,iBAAA+L,mBAAA,CAAgC;IAC9EnM,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA8K,uBAAA,CAAAvI,gBAAA,CAAsD;IAOVtD,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAI,UAAA,iBAAAgM,iBAAA,CAA8B;IAC1EpM,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA8K,uBAAA,CAAAtI,cAAA,CAAoD;;;AD9crF,OAAM,MAAO8I,0BAA2B,SAAQvM,aAAa;EAK3DwM,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA6C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAUtD;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAA3K,eAAe,GAAa,EAAE;IAE9B;IACA,KAAAjB,aAAa,GAAkB;MAC7BC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfsL,cAAc,EAAE;KACjB;IAED;IACA,KAAAhD,aAAa,GAAkB;MAC7BO,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbR,UAAU,EAAE,IAAI;MAChBgD,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClB3K,cAAc,EAAE;KACjB;IAED,KAAAmG,wBAAwB,GAAyB,IAAI;IAErD;IACA,KAAAF,eAAe,GAAqB,EAAE;IACtC,KAAA3C,cAAc,GAAqB,EAAE;IACrC,KAAA0B,eAAe,GAAqB,EAAE;IACtC,KAAAhF,cAAc,GAAqB,EAAE;IACrC,KAAAgE,SAAS,GAAY,KAAK;IAC1B,KAAAgC,OAAO,GAAY,KAAK;IAExB;IACA,KAAA1D,WAAW,GAAW,CAAC;IACd,KAAA9C,QAAQ,GAAW,EAAE;IAC9B,KAAAqD,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAiC,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAmB,KAAK;IAErC;IACA,KAAA3B,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAL,UAAU,GAAW,CAAC;IArFpB,IAAI,CAACiG,uBAAuB,GAAG;MAC7BvI,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBmB,MAAM,EAAE+I,SAAS;MACjBjJ,UAAU,EAAE,EAAE;MACdD,QAAQ,EAAEkJ;KACX;IAED,IAAI,CAACX,aAAa,CAACY,OAAO,EAAE,CAACC,IAAI,CAC/BlO,GAAG,CAAEmO,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAACd,eAAe,GAAGa,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAwESC,QAAQA,CAAA;IACf,IAAI,CAAC/F,WAAW,GAAG;MACjBgG,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAAClB,gBAAgB,CAAC,CAAC,CAAC;MAC/C1J,gBAAgB,EAAEmK,SAAS;MAC3BlK,cAAc,EAAEkK;KACjB;IACD,IAAI,CAACU,gBAAgB,EAAE;EACzB;EAEA;EACAxL,gBAAgBA,CAAA;IACd,OAAO,CAAC,EAAE,IAAI,CAAClB,aAAa,CAACC,aAAa,IACxC,IAAI,CAACD,aAAa,CAACI,YAAY,IAC/B,IAAI,CAACJ,aAAa,CAACO,WAAW,CAAC;EACnC;EAEAoM,qBAAqBA,CAAA;IACnB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAC5M,aAAa,CAACC,aAAa,EAAE2M,KAAK,EAAE;IAC7C,IAAI,IAAI,CAAC5M,aAAa,CAACI,YAAY,EAAEwM,KAAK,EAAE;IAC5C,IAAI,IAAI,CAAC5M,aAAa,CAACO,WAAW,EAAEqM,KAAK,EAAE;IAC3C,OAAOA,KAAK;EACd;EAEA/F,YAAYA,CAAA;IACV,IAAI,CAACL,WAAW,GAAG;MACjBgG,kBAAkB,EAAE,IAAI,CAAChG,WAAW,CAACgG,kBAAkB;MACvDC,qBAAqB,EAAE,IAAI,CAAClB,gBAAgB,CAAC,CAAC,CAAC;MAC/C1J,gBAAgB,EAAEmK,SAAS;MAC3BlK,cAAc,EAAEkK;KACjB;IACD,IAAI,CAACJ,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACnM,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACO,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfsL,cAAc,EAAE;KACjB;IACD,IAAI,CAAC3L,QAAQ,EAAE;EACjB;EAEA6E,cAAcA,CAAC8H,MAAc;IAC3B,IAAI,IAAI,CAAC7M,aAAa,CAACI,YAAY,KAAKyM,MAAM,EAAE;MAC9C,IAAI,CAAC7M,aAAa,CAACI,YAAY,GAAG,EAAE;IACtC,CAAC,MAAM;MACL,IAAI,CAACJ,aAAa,CAACI,YAAY,GAAGyM,MAAM;IAC1C;IACA,IAAI,CAAC3M,QAAQ,EAAE;EACjB;EAEAwB,cAAcA,CAAA;IACZ,IAAI,CAACN,cAAc,GAAG,EAAE;IACxB,IAAI,CAACgE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACiC,eAAe,CAACyF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC7K,QAAQ,GAAG,KAAK,CAAC;EAC/D;EAEAkB,aAAaA,CAAC2J,KAAqB;IACjC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IACzC,QAAQF,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,oBAAoB;MAC1C,KAAK,SAAS;QAAE,OAAO,cAAc;MACrC,KAAK,SAAS;QAAE,OAAO,qBAAqB;MAC5C,KAAK,SAAS;QAAE,OAAO,6BAA6B;MACpD,KAAK,UAAU;QAAE,OAAO,YAAY;MACpC;QAAS,OAAO,6BAA6B;IAC/C;EACF;EAEAxI,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACF,UAAU,IAAI,IAAI,CAACA,UAAU,IAAI,CAAC,IAAI,IAAI,CAACA,UAAU,IAAI,IAAI,CAACF,UAAU,EAAE;MACjF,IAAI,CAACR,QAAQ,CAAC,IAAI,CAACU,UAAU,CAAC;IAChC;EACF;EAIAvB,SAASA,CAACqK,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACpK,QAAQ,EAAE;MACjB,IAAI,CAACsH,uBAAuB,GAAG;QAC7B,GAAG8C,IAAI;QACPrL,gBAAgB,EAAEqL,IAAI,CAACrL,gBAAgB,GAAG,IAAIsL,IAAI,CAACD,IAAI,CAACrL,gBAAgB,CAAC,GAAGmK,SAAS;QACrFlK,cAAc,EAAEoL,IAAI,CAACpL,cAAc,GAAG,IAAIqL,IAAI,CAACD,IAAI,CAACpL,cAAc,CAAC,GAAGkK;OACvE;MACD,IAAI,CAACjB,aAAa,CAACqC,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOnP,MAAM,CAACmP,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEA9C,QAAQA,CAACwC,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAACvC,KAAK,CAACwC,aAAa,CAACpM,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC2J,OAAO,CAAC0C,aAAa,CAAC,IAAI,CAACzC,KAAK,CAACwC,aAAa,CAAC;MACpD;IACF;IACA,MAAME,KAAK,GAAG;MACZ7K,QAAQ,EAAE,IAAI,CAACsH,uBAAuB,CAACtH,QAAQ;MAC/CjB,gBAAgB,EAAE,IAAI,CAACwL,UAAU,CAAC,IAAI,CAACjD,uBAAuB,CAACvI,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAACuL,UAAU,CAAC,IAAI,CAACjD,uBAAuB,CAACtI,cAAc;KAC5E;IAED,IAAI,CAACoJ,aAAa,CAAC0C,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAACrB,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC2B,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC9C,OAAO,CAAC+C,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACpH,kBAAkB,EAAE;QACzBsG,GAAG,CAACe,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAtB,gBAAgBA,CAAA;IACd,IAAI,CAACvB,iBAAiB,CAAC8C,qCAAqC,CAAC;MAAEJ,IAAI,EAAE;IAAE,CAAE,CAAC,CAAC3B,IAAI,CAC7ElO,GAAG,CAACmO,GAAG,IAAG;MACR,MAAM+B,OAAO,GAAG/B,GAAG,CAACgC,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAAC7M,MAAM,IAAI8K,GAAG,CAAC2B,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACM,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChDvP,cAAc,EAAEuP,KAAK,CAACvP,cAAc;UACpCwP,GAAG,EAAED,KAAK,CAACC;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAACjD,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAIkD,KAAK,GAAG,IAAI,CAACJ,oBAAoB,CAACK,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAACjD,eAAe,CAAC;UAC1F,IAAI,CAAC9E,WAAW,CAACgG,kBAAkB,GAAG,IAAI,CAAC4B,oBAAoB,CAACI,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAAChI,WAAW,CAACgG,kBAAkB,GAAG,IAAI,CAAC4B,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMO,WAAW,GAAG,IAAI,CAACnI,WAAW,EAAEgG,kBAAkB,EAAE+B,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAAChI,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAAC2F,SAAS,EAAE;EACf;EAEAsC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAAC/B,OAAO,CAACiC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAAClC,OAAO,CAAEC,KAAU,IAAI;QACvC,MAAMkC,KAAK,GAAGlC,KAAK,CAAC9J,MAAM;QAC1B,IAAI,CAAC6L,SAAS,CAACG,KAAK,CAAC,EAAE;UAAE;UACvBH,SAAS,CAACG,KAAK,CAAC,GAAG,EAAE;QACvB;QACAH,SAAS,CAACG,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBnM,UAAU,EAAEgM,SAAS,CAAChM,UAAU;UAChCC,aAAa,EAAE+J,KAAK,CAAC/J,aAAa,IAAI,KAAK;UAC3CF,QAAQ,EAAEiK,KAAK,CAACjK,QAAQ;UACxBG,MAAM,EAAE8J,KAAK,CAAC9J,MAAM;UACpBpB,gBAAgB,EAAEkL,KAAK,CAAClL,gBAAgB;UACxCC,cAAc,EAAEiL,KAAK,CAACjL;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC4G,MAAM,CAAC/C,IAAI,CAAC,CAACwJ,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAAC3G,MAAM,CAAC2F,GAAG,CAAEY,KAAU,IAAI;MAChE,OAAO,IAAI,CAACK,UAAU,CAACjB,GAAG,CAAEU,SAAc,IAAI;QAC5C,MAAMhC,KAAK,GAAG+B,SAAS,CAACG,KAAK,CAAC,CAACM,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAACzM,UAAU,KAAKgM,SAAS,CAAC;QAC5F,OAAOhC,KAAK,IAAI;UACdhK,UAAU,EAAEgM,SAAS;UACrB/L,aAAa,EAAE,KAAK;UACpBF,QAAQ,EAAE,IAAI;UACdG,MAAM,EAAEgM,KAAK;UACbpN,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOuN,MAAM;EACf;EAEAI,sBAAsBA,CAACZ,GAAU;IAC/B,MAAMa,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5Cd,GAAG,CAAC/B,OAAO,CAACiC,SAAS,IAAG;MACtBa,aAAa,CAACC,GAAG,CAACd,SAAS,CAAChM,UAAU,CAAC;MACvCgM,SAAS,CAACC,OAAO,CAAClC,OAAO,CAAEC,KAAU,IAAI;QACvC2C,SAAS,CAACG,GAAG,CAAC9C,KAAK,CAAC9J,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACyF,MAAM,GAAGoH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLlH,MAAM,EAAEoH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACxJ,WAAW,CAAC3E,gBAAgB,IAAI,IAAI,CAAC2E,WAAW,CAAC1E,cAAc,EAAE;MACxE,MAAMsH,SAAS,GAAG,IAAI+D,IAAI,CAAC,IAAI,CAAC3G,WAAW,CAAC3E,gBAAgB,CAAC;MAC7D,MAAMyH,OAAO,GAAG,IAAI6D,IAAI,CAAC,IAAI,CAAC3G,WAAW,CAAC1E,cAAc,CAAC;MACzD,IAAIsH,SAAS,IAAIE,OAAO,IAAIF,SAAS,GAAGE,OAAO,EAAE;QAC/C,IAAI,CAAC0B,OAAO,CAAC0C,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEA;EACAuC,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACC,cAAc,EAAE;IACrB;IACA,IAAI,CAACvJ,kBAAkB,EAAE;EAC3B;EAEA;EACAuJ,cAAcA,CAAA;IACZ;IACA,IAAI,CAAC5I,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC6I,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACzE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACrE,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC3C,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC0B,eAAe,GAAG,EAAE;IACzB,IAAI,CAAChF,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAACpB,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfsL,cAAc,EAAE;KACjB;IAED;IACA,IAAI,CAACzG,SAAS,GAAG,KAAK;IACtB,IAAI,CAACwG,gBAAgB,GAAG,EAAE;IAE1B;IACA,IAAI,CAAClI,WAAW,GAAG,CAAC;IACpB,IAAI,CAACO,UAAU,GAAG,CAAC;IAEnB;IACA,IAAI,CAAC0H,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC1K,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,CAACiF,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC5B;EAEAQ,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC,IAAI,CAACH,WAAW,CAACgG,kBAAkB,EAAE+B,GAAG,EAAE;MAC7C,IAAI,CAACnH,OAAO,GAAG,KAAK;MACpB;IACF;IAEA,IAAI,CAACA,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC4I,cAAc,EAAE;IACrB,IAAI,CAAC9E,aAAa,CAACkF,mCAAmC,CAAC;MACrDvC,IAAI,EAAE;QACJwC,YAAY,EAAE,IAAI,CAAC7J,WAAW,CAACgG,kBAAkB,CAAC+B,GAAG;QACrD1M,gBAAgB,EAAE,IAAI,CAAC2E,WAAW,CAAC3E,gBAAgB,GAAG,IAAI,CAACwL,UAAU,CAAC,IAAI,CAAC7G,WAAW,CAAC3E,gBAAgB,CAAC,GAAGmK,SAAS;QACpHlK,cAAc,EAAE,IAAI,CAAC0E,WAAW,CAAC1E,cAAc,GAAG,IAAI,CAACuL,UAAU,CAAC,IAAI,CAAC7G,WAAW,CAAC1E,cAAc,CAAC,GAAGkK;;KAExG,CAAC,CAACM,SAAS,CAACH,GAAG,IAAG;MACjB,IAAI,CAAC/E,OAAO,GAAG,KAAK;MACpB,IAAI+E,GAAG,CAACgC,OAAO,IAAIhC,GAAG,CAAC2B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACxG,gBAAgB,GAAG6E,GAAG,CAACgC,OAAO,GAAGhC,GAAG,CAACgC,OAAO,GAAG,EAAE;QACtD,IAAIhC,GAAG,CAACgC,OAAO,EAAE;UACf,IAAI,CAAC7G,gBAAgB,GAAG,CAAC,GAAG6E,GAAG,CAACgC,OAAO,CAAC;UACxC,IAAI,CAACsB,sBAAsB,CAACtD,GAAG,CAACgC,OAAO,CAAC;UACxC,IAAI,CAACgC,mBAAmB,GAAG,IAAI,CAACvB,8BAA8B,CAACzC,GAAG,CAACgC,OAAO,CAAC;UAC3E;UACA,IAAI,CAACmC,mBAAmB,CAACnE,GAAG,CAACgC,OAAO,CAAC;UACrC;UACA,IAAI,CAACoC,oBAAoB,CAACpE,GAAG,CAACgC,OAAO,CAAC;QACxC;MACF;IACF,CAAC,CAAC;EACJ;EAEA;EACAmC,mBAAmBA,CAACE,IAA6B;IAC/C,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAyC;IAEpEF,IAAI,CAAC1D,OAAO,CAACiC,SAAS,IAAG;MACvB,MAAM4B,SAAS,GAAG5B,SAAS,CAAChM,UAAU,IAAI,EAAE,CAAC,CAAC;MAE9CgM,SAAS,CAACC,OAAO,EAAElC,OAAO,CAACC,KAAK,IAAG;QACjC,MAAM6D,YAAY,GAAG7D,KAAK,CAAC/J,aAAa,IAAI,KAAK,CAAC,CAAC;QACnD,MAAMiM,KAAK,GAAGlC,KAAK,CAAC9J,MAAM,IAAI,CAAC;QAE/B,IAAI,CAACwN,WAAW,CAACI,GAAG,CAACD,YAAY,CAAC,EAAE;UAClCH,WAAW,CAACK,GAAG,CAACF,YAAY,EAAE,IAAIF,GAAG,EAA4B,CAAC;QACpE;QAEA,MAAMK,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACJ,YAAY,CAAE;QAE/C,IAAI,CAACG,QAAQ,CAACF,GAAG,CAAC5B,KAAK,CAAC,EAAE;UACxB8B,QAAQ,CAACD,GAAG,CAAC7B,KAAK,EAAE,EAAE,CAAC;QACzB;QAEA8B,QAAQ,CAACC,GAAG,CAAC/B,KAAK,CAAE,CAACC,IAAI,CAAC;UACxBnM,UAAU,EAAE4N,SAAS;UAAE;UACvB3N,aAAa,EAAE4N,YAAY;UAAE;UAC7B9N,QAAQ,EAAEiK,KAAK,CAACjK,QAAQ,IAAI,CAAC;UAC7BG,MAAM,EAAEgM,KAAK;UACbpN,gBAAgB,EAAEkL,KAAK,CAAClL,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEiL,KAAK,CAACjL,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACwJ,cAAc,GAAGoE,KAAK,CAACC,IAAI,CAACU,WAAW,CAACvC,OAAO,EAAE,CAAC,CAACG,GAAG,CAAC,CAAC,CAACuC,YAAY,EAAEG,QAAQ,CAAC,KAAI;MACvF,MAAMrI,MAAM,GAAiBoH,KAAK,CAACC,IAAI,CAACgB,QAAQ,CAAC7C,OAAO,EAAE,CAAC,CACxDvI,IAAI,CAAC,CAAC,CAACwJ,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;MAAA,CAC1Bd,GAAG,CAAC,CAAC,CAAC7F,WAAW,EAAEL,MAAM,CAAC,MAAM;QAC/BK,WAAW;QACXL,MAAM,EAAEA,MAAM,CAACxC,IAAI,CAAC,CAACwJ,CAAC,EAAEC,CAAC,KAAI;UAC3B;UACA,IAAID,CAAC,CAACpM,UAAU,KAAKqM,CAAC,CAACrM,UAAU,EAAE;YACjC,OAAOoM,CAAC,CAACpM,UAAU,CAACkO,aAAa,CAAC7B,CAAC,CAACrM,UAAU,CAAC;UACjD;UACA,OAAOoM,CAAC,CAAClM,MAAM,GAAGmM,CAAC,CAACnM,MAAM;QAC5B,CAAC,CAAC;QACFf,QAAQ,EAAE;OACX,CAAC,CAAC;MAEL,OAAO;QACLsF,IAAI,EAAEoJ,YAAY;QAClBlI,MAAM;QACNxG,QAAQ,EAAE;OACX;IACH,CAAC,CAAC,CAACyD,IAAI,CAAC,CAACwJ,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3H,IAAI,CAACyJ,aAAa,CAAC7B,CAAC,CAAC5H,IAAI,CAAC,CAAC;IAE/C;IACA,IAAI,CAACmE,eAAe,GAAG,IAAI,CAACD,cAAc,CAAC2C,GAAG,CAAC6C,EAAE,IAAIA,EAAE,CAAC1J,IAAI,CAAC;IAC7D,IAAI,CAAC2J,qBAAqB,EAAE;EAC9B;EAEA;EACAA,qBAAqBA,CAAA;IACnB,MAAMzB,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,IAAI,CAACjE,cAAc,CAACoB,OAAO,CAACsE,QAAQ,IAAG;MACrC,IAAI,CAAC,IAAI,CAACxF,gBAAgB,IAAIwF,QAAQ,CAAC5J,IAAI,KAAK,IAAI,CAACoE,gBAAgB,EAAE;QACrEwF,QAAQ,CAAC1I,MAAM,CAACoE,OAAO,CAACmC,KAAK,IAAG;UAC9BS,SAAS,CAACG,GAAG,CAACZ,KAAK,CAACzG,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACvH,eAAe,GAAG6O,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC,CAAC/J,IAAI,CAAC,CAACwJ,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpE;EAEA;EACAkC,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACjQ,cAAc,CAAC0L,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC7K,QAAQ,GAAG,KAAK,CAAC;IAC5D,IAAI,CAACd,cAAc,GAAG,EAAE;IACxB,IAAI,CAACgE,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAAC1B,WAAW,GAAG,CAAC;IAEpB;IACA,IAAI,CAAC1D,aAAa,CAACO,WAAW,GAAG,EAAE;IAEnC;IACA,IAAI,CAAC4Q,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACnR,aAAa,CAAC6L,cAAc,GAAG,IAAI,CAACD,gBAAgB;IACzD,IAAI,CAAC1L,QAAQ,EAAE;EACjB;EAIA;EACAoR,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC5F,cAAc,CAAC6F,MAAM,CAACH,QAAQ,IAAG;MAC3C;MACA,IAAI,IAAI,CAACxF,gBAAgB,IAAIwF,QAAQ,CAAC5J,IAAI,KAAK,IAAI,CAACoE,gBAAgB,EAAE;QACpE,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAAC5L,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMuR,OAAO,GAAG,IAAI,CAACxR,aAAa,CAACC,aAAa,CAACwR,WAAW,EAAE;QAC9D,MAAMC,gBAAgB,GAAGN,QAAQ,CAAC1I,MAAM,CAACiJ,IAAI,CAAC1C,KAAK,IACjDA,KAAK,CAAC9G,MAAM,CAACwJ,IAAI,CAAC5E,KAAK,IACrBA,KAAK,CAAChK,UAAU,CAAC0O,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IAChDzE,KAAK,CAAC/J,aAAa,CAACyO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,CACpD,CACF;QACD,IAAI,CAACE,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC1R,aAAa,CAACI,YAAY,EAAE;QACnC,MAAMyR,iBAAiB,GAAGT,QAAQ,CAAC1I,MAAM,CAACiJ,IAAI,CAAC1C,KAAK,IAClDA,KAAK,CAAC9G,MAAM,CAACwJ,IAAI,CAAC5E,KAAK,IAAI,IAAI,CAAC+E,mBAAmB,CAAC/E,KAAK,CAAC,CAAC,CAC5D;QACD,IAAI,CAAC8E,iBAAiB,EAAE;UACtB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC7R,aAAa,CAACO,WAAW,EAAE;QAClC,MAAMiI,WAAW,GAAGuJ,QAAQ,CAAC,IAAI,CAAC/R,aAAa,CAACO,WAAW,CAAC;QAC5D,MAAMyR,gBAAgB,GAAGZ,QAAQ,CAAC1I,MAAM,CAACiJ,IAAI,CAAC1C,KAAK,IACjDA,KAAK,CAACzG,WAAW,KAAKA,WAAW,CAClC;QACD,IAAI,CAACwJ,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CAAC3D,GAAG,CAAC+C,QAAQ,IAAG;MAChB;MACA,MAAMa,gBAAgB,GAAG;QAAE,GAAGb;MAAQ,CAAE;MACxCa,gBAAgB,CAACvJ,MAAM,GAAG0I,QAAQ,CAAC1I,MAAM,CAAC6I,MAAM,CAACtC,KAAK,IAAG;QACvD;QACA,IAAI,IAAI,CAACjP,aAAa,CAACO,WAAW,EAAE;UAClC,MAAMiI,WAAW,GAAGuJ,QAAQ,CAAC,IAAI,CAAC/R,aAAa,CAACO,WAAW,CAAC;UAC5D,IAAI0O,KAAK,CAACzG,WAAW,KAAKA,WAAW,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QAEA;QACA,MAAM0J,cAAc,GAAGjD,KAAK,CAAC9G,MAAM,CAACwJ,IAAI,CAAC5E,KAAK,IAAG;UAC/C;UACA,IAAI,IAAI,CAAC/M,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMuR,OAAO,GAAG,IAAI,CAACxR,aAAa,CAACC,aAAa,CAACwR,WAAW,EAAE;YAC9D,IAAI,CAAC1E,KAAK,CAAChK,UAAU,CAAC0O,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzE,KAAK,CAAC/J,aAAa,CAACyO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAACxR,aAAa,CAACI,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC0R,mBAAmB,CAAC/E,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOmF,cAAc;MACvB,CAAC,CAAC,CAAC7D,GAAG,CAACY,KAAK,IAAG;QACb;QACA,MAAMkD,aAAa,GAAG;UAAE,GAAGlD;QAAK,CAAE;QAClCkD,aAAa,CAAChK,MAAM,GAAG8G,KAAK,CAAC9G,MAAM,CAACoJ,MAAM,CAACxE,KAAK,IAAG;UACjD;UACA,IAAI,IAAI,CAAC/M,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMuR,OAAO,GAAG,IAAI,CAACxR,aAAa,CAACC,aAAa,CAACwR,WAAW,EAAE;YAC9D,IAAI,CAAC1E,KAAK,CAAChK,UAAU,CAAC0O,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzE,KAAK,CAAC/J,aAAa,CAACyO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAACxR,aAAa,CAACI,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC0R,mBAAmB,CAAC/E,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOoF,aAAa;MACtB,CAAC,CAAC;MAEF,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ;EAEA;EACQH,mBAAmBA,CAAC/E,KAAqB;IAC/C,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IAEzC,QAAQ,IAAI,CAAC/M,aAAa,CAACI,YAAY;MACrC,KAAK,QAAQ;QACX,OAAOyM,MAAM,KAAK,QAAQ;MAC5B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,UAAU;MAC9B;QACE,OAAO,IAAI;MAAE;IACjB;EACF;EAEA;EACQG,cAAcA,CAACD,KAAqB;IAC1C,IAAI,CAACA,KAAK,CAACjK,QAAQ,EAAE;MACnB,OAAO,UAAU;IACnB;IAEA;IACA,IAAI,CAACiK,KAAK,CAAClL,gBAAgB,IAAI,CAACkL,KAAK,CAACjL,cAAc,IAClDiL,KAAK,CAAClL,gBAAgB,KAAK,EAAE,IAAIkL,KAAK,CAACjL,cAAc,KAAK,EAAE,EAAE;MAC9D,OAAO,SAAS;IAClB;IAEA,IAAI;MACF;MACA,MAAMsQ,GAAG,GAAG,IAAIjF,IAAI,EAAE;MACtB,MAAMkF,KAAK,GAAG,IAAIlF,IAAI,CAACiF,GAAG,CAACE,WAAW,EAAE,EAAEF,GAAG,CAACG,QAAQ,EAAE,EAAEH,GAAG,CAACI,OAAO,EAAE,CAAC;MAExE;MACA,IAAIpJ,SAAe;MACnB,IAAI2D,KAAK,CAAClL,gBAAgB,CAAC+P,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxCxI,SAAS,GAAG,IAAI+D,IAAI,CAACJ,KAAK,CAAClL,gBAAgB,CAAC;MAC9C,CAAC,MAAM;QACLuH,SAAS,GAAG,IAAI+D,IAAI,CAACJ,KAAK,CAAClL,gBAAgB,GAAG,WAAW,CAAC;MAC5D;MAEA;MACA,IAAIyH,OAAa;MACjB,IAAIyD,KAAK,CAACjL,cAAc,CAAC8P,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtCtI,OAAO,GAAG,IAAI6D,IAAI,CAACJ,KAAK,CAACjL,cAAc,CAAC;MAC1C,CAAC,MAAM;QACLwH,OAAO,GAAG,IAAI6D,IAAI,CAACJ,KAAK,CAACjL,cAAc,GAAG,WAAW,CAAC;MACxD;MAEA;MACA,IAAI2Q,KAAK,CAACrJ,SAAS,CAACsJ,OAAO,EAAE,CAAC,IAAID,KAAK,CAACnJ,OAAO,CAACoJ,OAAO,EAAE,CAAC,EAAE;QAC1DC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAE;UACnCC,KAAK,EAAE9F,KAAK,CAAClL,gBAAgB;UAC7BiR,GAAG,EAAE/F,KAAK,CAACjL,cAAc;UACzBiR,OAAO,EAAEhG,KAAK,CAACjK;SAChB,CAAC;QACF,OAAO,SAAS;MAClB;MAEA;MACA,MAAMkQ,aAAa,GAAG,IAAI7F,IAAI,CAAC/D,SAAS,CAACkJ,WAAW,EAAE,EAAElJ,SAAS,CAACmJ,QAAQ,EAAE,EAAEnJ,SAAS,CAACoJ,OAAO,EAAE,CAAC;MAClG,MAAMS,WAAW,GAAG,IAAI9F,IAAI,CAAC7D,OAAO,CAACgJ,WAAW,EAAE,EAAEhJ,OAAO,CAACiJ,QAAQ,EAAE,EAAEjJ,OAAO,CAACkJ,OAAO,EAAE,CAAC;MAE1F;MACA,IAAIH,KAAK,GAAGW,aAAa,EAAE;QACzB,OAAO,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIX,KAAK,IAAIW,aAAa,IAAIX,KAAK,IAAIY,WAAW,EAAE;QACzD,OAAO,QAAQ,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,OAAO,SAAS,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,EAAEnG,KAAK,CAAC;MACnD,OAAO,SAAS;IAClB;EACF;EAEAnD,OAAOA,CAACqD,GAAQ;IACdA,GAAG,CAACe,KAAK,EAAE;EACb;EAEAR,UAAUA,CAAA;IACR,IAAI,CAACvC,KAAK,CAACkI,KAAK,EAAE;IAClB,IAAI,CAAClI,KAAK,CAACmI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAChJ,uBAAuB,CAACvI,gBAAgB,CAAC;IAC9E,IAAI,CAACoJ,KAAK,CAACmI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAChJ,uBAAuB,CAACtI,cAAc,CAAC;IAC5E,IAAI,CAACmJ,KAAK,CAACoI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACjJ,uBAAuB,CAACvI,gBAAgB,EAAE,IAAI,CAACuI,uBAAuB,CAACtI,cAAc,CAAC;EACtI;EAEAwR,gBAAgBA,CAAA;IACd,IAAI,CAAClI,MAAM,CAACmI,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAAC/M,WAAW,EAAEgG,kBAAkB,EAAE+B,GAAG,EAAE,CAAC,CAAC;EACnG;EAEA;EACA/M,gBAAgBA,CAAC4P,QAAwB;IACvC,IAAI,CAAC7J,wBAAwB,GAAG6J,QAAQ,IAAI,IAAI;IAEhD;IACA,MAAMoC,iBAAiB,GAAG,IAAI,CAACpS,cAAc,CAACC,MAAM,GAAG,CAAC;IAExD,IAAI,CAACwH,aAAa,GAAG;MACnBO,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbR,UAAU,EAAE,CAAC0K,iBAAiB,IAAI,CAACpC,QAAQ;MAC3CtF,iBAAiB,EAAEsF,QAAQ,GAAG,CAACA,QAAQ,CAAC5J,IAAI,CAAC,GAAG,EAAE;MAClDuE,cAAc,EAAE,EAAE;MAClB3K,cAAc,EAAE;KACjB;IAED;IACA,IAAIgQ,QAAQ,EAAE;MACZA,QAAQ,CAAC1I,MAAM,CAACoE,OAAO,CAACmC,KAAK,IAAG;QAC9BA,KAAK,CAAC/M,QAAQ,GAAG,KAAK;QACtB+M,KAAK,CAAC9G,MAAM,CAAC2E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC7K,QAAQ,GAAG,KAAK,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAAC6I,aAAa,CAACqC,IAAI,CAAC,IAAI,CAACqG,kBAAkB,CAAC;EAClD;EAEA;EACAnL,sBAAsBA,CAAC2G,KAAiB;IACtC,IAAIA,KAAK,CAAC/M,QAAQ,EAAE;MAClB+M,KAAK,CAAC9G,MAAM,CAAC2E,OAAO,CAACC,KAAK,IAAG;QAC3B,IAAIA,KAAK,CAACjK,QAAQ,EAAE;UAClBiK,KAAK,CAAC7K,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL+M,KAAK,CAAC9G,MAAM,CAAC2E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC7K,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA;EACA4H,aAAaA,CAACmD,GAAQ;IACpB;IACA,IAAI,CAAChC,KAAK,CAACkI,KAAK,EAAE;IAClB,IAAI,CAAClI,KAAK,CAACmI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvK,aAAa,CAACO,SAAS,CAAC;IAC3D,IAAI,CAAC6B,KAAK,CAACmI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvK,aAAa,CAACS,OAAO,CAAC;IACzD,IAAI,CAAC2B,KAAK,CAACoI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACxK,aAAa,CAACO,SAAS,EAAE,IAAI,CAACP,aAAa,CAACS,OAAO,CAAC;IAElG,IAAI,IAAI,CAAC2B,KAAK,CAACwC,aAAa,CAACpM,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC2J,OAAO,CAAC0C,aAAa,CAAC,IAAI,CAACzC,KAAK,CAACwC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMiG,cAAc,GAAU,EAAE;IAEhC,IAAI,IAAI,CAAC7K,aAAa,CAACC,UAAU,EAAE;MACjC;MACA,IAAI,CAACzB,eAAe,CAACyF,OAAO,CAACC,KAAK,IAAG;QACnC,IAAIA,KAAK,CAACjK,QAAQ,EAAE;UAClB4Q,cAAc,CAACxE,IAAI,CAAC;YAClBpM,QAAQ,EAAEiK,KAAK,CAACjK,QAAQ;YACxBjB,gBAAgB,EAAE,IAAI,CAACwL,UAAU,CAAC,IAAI,CAACxE,aAAa,CAACO,SAAS,CAAC;YAC/DtH,cAAc,EAAE,IAAI,CAACuL,UAAU,CAAC,IAAI,CAACxE,aAAa,CAACS,OAAO;WAC3D,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAAClI,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACD,cAAc,CAAC0L,OAAO,CAACC,KAAK,IAAG;UAClC,IAAIA,KAAK,CAACjK,QAAQ,EAAE;YAClB4Q,cAAc,CAACxE,IAAI,CAAC;cAClBpM,QAAQ,EAAEiK,KAAK,CAACjK,QAAQ;cACxBjB,gBAAgB,EAAE,IAAI,CAACwL,UAAU,CAAC,IAAI,CAACxE,aAAa,CAACO,SAAS,CAAC;cAC/DtH,cAAc,EAAE,IAAI,CAACuL,UAAU,CAAC,IAAI,CAACxE,aAAa,CAACS,OAAO;aAC3D,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAAC/B,wBAAwB,EAAE;QACxC;QACA,IAAI,CAACA,wBAAwB,CAACmB,MAAM,CAACoE,OAAO,CAACmC,KAAK,IAAG;UACnDA,KAAK,CAAC9G,MAAM,CAAC2E,OAAO,CAACC,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAAC7K,QAAQ,IAAI6K,KAAK,CAACjK,QAAQ,EAAE;cACpC4Q,cAAc,CAACxE,IAAI,CAAC;gBAClBpM,QAAQ,EAAEiK,KAAK,CAACjK,QAAQ;gBACxBjB,gBAAgB,EAAE,IAAI,CAACwL,UAAU,CAAC,IAAI,CAACxE,aAAa,CAACO,SAAS,CAAC;gBAC/DtH,cAAc,EAAE,IAAI,CAACuL,UAAU,CAAC,IAAI,CAACxE,aAAa,CAACS,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIoK,cAAc,CAACrS,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC2J,OAAO,CAAC0C,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAACxC,aAAa,CAAC0C,oCAAoC,CAAC;MACtDC,IAAI,EAAE6F;KACP,CAAC,CAACpH,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC2B,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC9C,OAAO,CAAC+C,aAAa,CAAC,QAAQ2F,cAAc,CAACrS,MAAM,WAAW,CAAC;QACpE;QACA,IAAI,CAACD,cAAc,CAAC0L,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC7K,QAAQ,GAAG,KAAK,CAAC;QAC5D,IAAI,CAACd,cAAc,GAAG,EAAE;QACxB,IAAI,CAACgE,SAAS,GAAG,KAAK;QACtB,IAAI,CAACuB,kBAAkB,EAAE;QACzBsG,GAAG,CAACe,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA;EACA7K,cAAcA,CAAC4J,KAAqB;IAClC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IACzC,OAAO,UAAUF,MAAM,EAAE;EAC3B;EAEA;EACA8G,eAAeA,CAAC5G,KAAqB;IACnC,IAAIA,KAAK,CAACjK,QAAQ,EAAE;MAClB;MACA,IAAI,CAACF,SAAS,CAAC,IAAI,CAACgR,MAAM,EAAE7G,KAAK,CAAC;IACpC;EACF;EAEA;EACAwD,oBAAoBA,CAACC,IAA6B;IAChD,IAAI,CAACnJ,eAAe,GAAG,EAAE;IAEzBmJ,IAAI,CAAC1D,OAAO,CAACiC,SAAS,IAAG;MACvB,MAAM4B,SAAS,GAAG5B,SAAS,CAAChM,UAAU,IAAI,EAAE;MAE5CgM,SAAS,CAACC,OAAO,EAAElC,OAAO,CAACC,KAAK,IAAG;QACjC,IAAI,CAAC1F,eAAe,CAAC6H,IAAI,CAAC;UACxBnM,UAAU,EAAE4N,SAAS;UACrB3N,aAAa,EAAE+J,KAAK,CAAC/J,aAAa,IAAI,KAAK;UAC3CF,QAAQ,EAAEiK,KAAK,CAACjK,QAAQ,IAAI,CAAC;UAC7BG,MAAM,EAAE8J,KAAK,CAAC9J,MAAM,IAAI,CAAC;UACzBpB,gBAAgB,EAAEkL,KAAK,CAAClL,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEiL,KAAK,CAACjL,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAChC,QAAQ,EAAE;IAEf;IACA,IAAI,CAAC2T,iBAAiB,EAAE;EAC1B;EAEA;EACQA,iBAAiBA,CAAA;IACvB,MAAMC,YAAY,GAAG;MACnBC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACV,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAAC7M,eAAe,CAACyF,OAAO,CAACC,KAAK,IAAG;MACnC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;MACzC,IAAI+G,YAAY,CAACK,cAAc,CAACtH,MAAM,CAAC,EAAE;QACvCiH,YAAY,CAACjH,MAAmC,CAAC,EAAE;MACrD;IACF,CAAC,CAAC;IAEF8F,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAEN,YAAY,CAAC;IAClCnB,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAE,IAAIjH,IAAI,EAAE,CAACkH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;EAIA;EACApU,QAAQA,CAAA;IACN;IACA,MAAMqU,qBAAqB,GAAG,IAAI,CAACnT,cAAc,CAACiN,GAAG,CAACtB,KAAK,IAAIA,KAAK,CAACjK,QAAQ,CAAC;IAE9E,IAAI,CAAC4B,cAAc,GAAG,IAAI,CAAC2C,eAAe,CAACkK,MAAM,CAACxE,KAAK,IAAG;MACxD;MACA,IAAI,IAAI,CAAC/M,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMuR,OAAO,GAAG,IAAI,CAACxR,aAAa,CAACC,aAAa,CAACwR,WAAW,EAAE;QAC9D,IAAI,CAAC1E,KAAK,CAAChK,UAAU,CAAC0O,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzE,KAAK,CAAC/J,aAAa,CAACyO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;UACtD,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC5F,gBAAgB,IAAImB,KAAK,CAAC/J,aAAa,KAAK,IAAI,CAAC4I,gBAAgB,EAAE;QAC1E,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAAC5L,aAAa,CAACI,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAAC0R,mBAAmB,CAAC/E,KAAK,CAAC,EAAE;UACpC,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC/M,aAAa,CAACO,WAAW,EAAE;QAClC,MAAMiI,WAAW,GAAGuJ,QAAQ,CAAC,IAAI,CAAC/R,aAAa,CAACO,WAAW,CAAC;QAC5D,IAAIwM,KAAK,CAAC9J,MAAM,KAAKuF,WAAW,EAAE;UAChC,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF;IACA,IAAI,CAACpH,cAAc,GAAG,IAAI,CAACsD,cAAc,CAAC6M,MAAM,CAACxE,KAAK,IACpDwH,qBAAqB,CAAC3C,QAAQ,CAAC7E,KAAK,CAACjK,QAAQ,CAAC,CAC/C;IAED;IACA,IAAI,CAACuE,eAAe,CAACyF,OAAO,CAACC,KAAK,IAAG;MACnCA,KAAK,CAAC7K,QAAQ,GAAG,IAAI,CAACd,cAAc,CAACuQ,IAAI,CAACzP,QAAQ,IAAIA,QAAQ,CAACY,QAAQ,KAAKiK,KAAK,CAACjK,QAAQ,CAAC;IAC7F,CAAC,CAAC;IAEF;IACA,IAAI,CAAC0R,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAAC9Q,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC+Q,gBAAgB,EAAE;EACzB;EAEA;EACQD,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACpO,eAAe,CAAC/E,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC+D,SAAS,GAAG,KAAK;IACxB,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI,CAACgB,eAAe,CAACsO,KAAK,CAAC3H,KAAK,IAC/C,CAACA,KAAK,CAACjK,QAAQ,IAAIiK,KAAK,CAAC7K,QAAQ,CAClC;IACH;EACF;EAEA;EACAuS,gBAAgBA,CAAA;IACd,IAAI,CAACxQ,UAAU,GAAGO,IAAI,CAACmQ,IAAI,CAAC,IAAI,CAACjQ,cAAc,CAACrD,MAAM,GAAG,IAAI,CAACT,QAAQ,CAAC;IACvE,MAAMgU,UAAU,GAAG,CAAC,IAAI,CAAClR,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC9C,QAAQ;IACzD,MAAMiU,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAChU,QAAQ;IAC3C,IAAI,CAACwF,eAAe,GAAG,IAAI,CAAC1B,cAAc,CAACkD,KAAK,CAACgN,UAAU,EAAEC,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACL,oBAAoB,EAAE;EAC7B;EAEA;EACA1T,gBAAgBA,CAAA;IACd,IAAI,CAAC4C,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC+Q,gBAAgB,EAAE;EACzB;EAEA;EACAhR,QAAQA,CAACqR,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC7Q,UAAU,EAAE;MACxC,IAAI,CAACP,WAAW,GAAGoR,IAAI;MACvB,IAAI,CAACL,gBAAgB,EAAE;IACzB;EACF;EAEA;EACA9P,eAAeA,CAAA;IACb,MAAMoQ,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAInC,KAAK,GAAGrO,IAAI,CAACyQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAACvR,WAAW,GAAGc,IAAI,CAACyK,KAAK,CAAC+F,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAIlC,GAAG,GAAGtO,IAAI,CAACC,GAAG,CAAC,IAAI,CAACR,UAAU,EAAE4O,KAAK,GAAGmC,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAIlC,GAAG,GAAGD,KAAK,GAAG,CAAC,GAAGmC,UAAU,EAAE;MAChCnC,KAAK,GAAGrO,IAAI,CAACyQ,GAAG,CAAC,CAAC,EAAEnC,GAAG,GAAGkC,UAAU,GAAG,CAAC,CAAC;IAC3C;IAEA,KAAK,IAAIE,CAAC,GAAGrC,KAAK,EAAEqC,CAAC,IAAIpC,GAAG,EAAEoC,CAAC,EAAE,EAAE;MACjCH,KAAK,CAAC7F,IAAI,CAACgG,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACA1P,iBAAiBA,CAAA;IACf,IAAI,CAACe,eAAe,CAAC0G,OAAO,CAACC,KAAK,IAAG;MACnC,IAAIA,KAAK,CAACjK,QAAQ,EAAE;QAClBiK,KAAK,CAAC7K,QAAQ,GAAG,IAAI,CAACkD,SAAS;MACjC;IACF,CAAC,CAAC;IACF,IAAI,CAAC+P,oBAAoB,EAAE;EAC7B;EAEA;EACAhT,sBAAsBA,CAAA;IACpB,IAAI,CAACgT,oBAAoB,EAAE;IAC3B,IAAI,CAAC/P,SAAS,GAAG,IAAI,CAACgB,eAAe,CAACsO,KAAK,CAAC3H,KAAK,IAC/C,CAACA,KAAK,CAACjK,QAAQ,IAAIiK,KAAK,CAAC7K,QAAQ,CAClC;EACH;EAEA;EACAiT,oBAAoBA,CAAA;IAClB,IAAI,CAAC/T,cAAc,GAAG,IAAI,CAACiG,eAAe,CAACkK,MAAM,CAACxE,KAAK,IAAIA,KAAK,CAAC7K,QAAQ,CAAC;EAC5E;EAEA;EACAyD,IAAIA,CAACyP,KAAa;IAChB,IAAI,IAAI,CAAClP,SAAS,KAAKkP,KAAK,EAAE;MAC5B,IAAI,CAACjP,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IACpE,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAGkP,KAAK;MACtB,IAAI,CAACjP,aAAa,GAAG,KAAK;IAC5B;IAEA,IAAI,CAACzB,cAAc,CAACiB,IAAI,CAAC,CAACwJ,CAAC,EAAEC,CAAC,KAAI;MAChC,IAAIiG,MAAM,GAAIlG,CAAS,CAACiG,KAAK,CAAC;MAC9B,IAAIE,MAAM,GAAIlG,CAAS,CAACgG,KAAK,CAAC;MAE9B;MACA,IAAIA,KAAK,CAACxD,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC1ByD,MAAM,GAAGA,MAAM,GAAG,IAAIlI,IAAI,CAACkI,MAAM,CAAC,CAAC3C,OAAO,EAAE,GAAG,CAAC;QAChD4C,MAAM,GAAGA,MAAM,GAAG,IAAInI,IAAI,CAACmI,MAAM,CAAC,CAAC5C,OAAO,EAAE,GAAG,CAAC;MAClD;MAEA;MACA,IAAI0C,KAAK,KAAK,QAAQ,EAAE;QACtBC,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC,IAAI,CAAC;QAC5BC,MAAM,GAAGC,MAAM,CAACD,MAAM,CAAC,IAAI,CAAC;MAC9B;MAEA;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAAC5D,WAAW,EAAE;QAC7B6D,MAAM,GAAGA,MAAM,CAAC7D,WAAW,EAAE;MAC/B;MAEA,IAAI4D,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACnP,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC9C;MACA,IAAIkP,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACnP,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9C;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAACsO,gBAAgB,EAAE;EACzB;EAEA;EACApO,cAAcA,CAACmP,MAAc,EAAEzI,KAAqB;IAClD,OAAOA,KAAK,CAACjK,QAAQ;EACvB;EAEA;EACAO,aAAaA,CAAC0J,KAAqB;IACjC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IAEzC,QAAQF,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,UAAU;QACb,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACArH,UAAUA,CAAA;IACR;IACA,MAAMiQ,UAAU,GAAG,IAAI,CAACC,WAAW,EAAE;IACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACrCG,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,UAAU,IAAIjJ,IAAI,EAAE,CAACkH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACrFwB,IAAI,CAACO,KAAK,CAACC,UAAU,GAAG,QAAQ;IAChCP,QAAQ,CAAClI,IAAI,CAAC0I,WAAW,CAACT,IAAI,CAAC;IAC/BA,IAAI,CAACU,KAAK,EAAE;IACZT,QAAQ,CAAClI,IAAI,CAAC4I,WAAW,CAACX,IAAI,CAAC;EACjC;EAEA;EACQJ,WAAWA,CAAA;IACjB,MAAMgB,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;IACxD,MAAMC,IAAI,GAAG,IAAI,CAACjS,cAAc,CAAC2J,GAAG,CAACtB,KAAK,IAAI,CAC5CA,KAAK,CAAChK,UAAU,EAChBgK,KAAK,CAAC/J,aAAa,EACnB,GAAG+J,KAAK,CAAC9J,MAAM,GAAG,EAClB8J,KAAK,CAAClL,gBAAgB,IAAI,KAAK,EAC/BkL,KAAK,CAACjL,cAAc,IAAI,KAAK,EAC7B,IAAI,CAACuB,aAAa,CAAC0J,KAAK,CAAC,CAC1B,CAAC;IAEF,MAAM0I,UAAU,GAAG,CAACiB,OAAO,EAAE,GAAGC,IAAI,CAAC,CAClCtI,GAAG,CAACuI,GAAG,IAAIA,GAAG,CAACvI,GAAG,CAACwI,IAAI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAClDA,IAAI,CAAC,IAAI,CAAC;IAEb,OAAO,QAAQ,GAAGrB,UAAU,CAAC,CAAC;EAChC;;;uCAtlCW7K,0BAA0B,EAAArM,EAAA,CAAAwY,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1Y,EAAA,CAAAwY,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA5Y,EAAA,CAAAwY,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA9Y,EAAA,CAAAwY,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAhZ,EAAA,CAAAwY,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAlZ,EAAA,CAAAwY,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAAnZ,EAAA,CAAAwY,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAArZ,EAAA,CAAAwY,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA1BlN,0BAA0B;MAAAmN,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;uCAT1B,EAAE,GAAA3Z,EAAA,CAAA6Z,0BAAA,EAAA7Z,EAAA,CAAA8Z,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCpEb3Z,EADF,CAAAC,cAAA,iBAAuC,qBACrB;UACdD,EAAA,CAAAmB,SAAA,qBAAiC;UACnCnB,EAAA,CAAAG,YAAA,EAAiB;UAIbH,EAHJ,CAAAC,cAAA,mBAAc,cAE0B,YACQ;UAAAD,EAAA,CAAAE,MAAA,2FAAc;UAC5DF,EAD4D,CAAAG,YAAA,EAAI,EAC1D;UAMAH,EAHN,CAAAC,cAAA,cAAkC,cACK,cACJ,iBACO;UAAAD,EAAA,CAAAE,MAAA,qBAAG;UAAAF,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;UACjFH,EAAA,CAAAC,cAAA,qBACyC;UADVD,EAAA,CAAAoB,gBAAA,2BAAAgZ,wEAAA9Y,MAAA;YAAAtB,EAAA,CAAAa,aAAA,CAAAwZ,GAAA;YAAAra,EAAA,CAAAwB,kBAAA,CAAAoY,GAAA,CAAA3R,WAAA,CAAAgG,kBAAA,EAAA3M,MAAA,MAAAsY,GAAA,CAAA3R,WAAA,CAAAgG,kBAAA,GAAA3M,MAAA;YAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;UAAA,EAA4C;UACzEtB,EAAA,CAAAW,UAAA,4BAAA2Z,yEAAA;YAAAta,EAAA,CAAAa,aAAA,CAAAwZ,GAAA;YAAA,OAAAra,EAAA,CAAAiB,WAAA,CAAkB2Y,GAAA,CAAAlI,iBAAA,EAAmB;UAAA,EAAC;UACtC1R,EAAA,CAAAkC,UAAA,KAAAqY,gDAAA,wBAAoE;UAIxEva,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAA+B,iBACO;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9CH,EAAA,CAAAC,cAAA,qBAAmG;UAArED,EAAA,CAAAoB,gBAAA,2BAAAoZ,wEAAAlZ,MAAA;YAAAtB,EAAA,CAAAa,aAAA,CAAAwZ,GAAA;YAAAra,EAAA,CAAAwB,kBAAA,CAAAoY,GAAA,CAAAvM,gBAAA,EAAA/L,MAAA,MAAAsY,GAAA,CAAAvM,gBAAA,GAAA/L,MAAA;YAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;UAAA,EAA8B;UAACtB,EAAA,CAAAW,UAAA,4BAAA8Z,yEAAA;YAAAza,EAAA,CAAAa,aAAA,CAAAwZ,GAAA;YAAA,OAAAra,EAAA,CAAAiB,WAAA,CAAkB2Y,GAAA,CAAA9G,gBAAA,EAAkB;UAAA,EAAC;UAChG9S,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAkC,UAAA,KAAAwY,gDAAA,wBAAuE;UAQnF1a,EALU,CAAAG,YAAA,EAAY,EACR,EACF,EACF,EACO,EACP;UAoeVH,EAjeA,CAAAkC,UAAA,KAAAyY,8CAAA,wBAAiE,KAAAC,kDAAA,iCAAA5a,EAAA,CAAA6a,sBAAA,CAkYD,KAAAC,kDAAA,gCAAA9a,EAAA,CAAA6a,sBAAA,CAkFG,KAAAE,kDAAA,iCAAA/a,EAAA,CAAA6a,sBAAA,CAaf;;;UAxfX7a,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAyC,gBAAA,YAAAmX,GAAA,CAAA3R,WAAA,CAAAgG,kBAAA,CAA4C;UAE7CjO,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAwZ,GAAA,CAAA/J,oBAAA,CAAuB;UAQvB7P,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAyC,gBAAA,YAAAmX,GAAA,CAAAvM,gBAAA,CAA8B;UAE1BrN,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAAwZ,GAAA,CAAAxM,eAAA,CAAkB;UAWnCpN,EAAA,CAAAM,SAAA,EAAoC;UAApCN,EAAA,CAAAI,UAAA,SAAAwZ,GAAA,CAAA3R,WAAA,CAAAgG,kBAAA,CAAoC;;;qBDiC3DzO,YAAY,EAAAwb,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAEtb,YAAY,EAAAub,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,YAAA,EAAAL,EAAA,CAAAM,OAAA,EAAA/C,EAAA,CAAAgD,eAAA,EAAAhD,EAAA,CAAAiD,mBAAA,EAAAjD,EAAA,CAAAkD,qBAAA,EAAAlD,EAAA,CAAAmD,qBAAA,EAAAnD,EAAA,CAAAoD,mBAAA,EAAApD,EAAA,CAAAqD,gBAAA,EAAArD,EAAA,CAAAsD,iBAAA,EAAAtD,EAAA,CAAAuD,iBAAA,EAAAvD,EAAA,CAAAwD,oBAAA,EAAAxD,EAAA,CAAAyD,iBAAA,EAAAzD,EAAA,CAAA0D,eAAA,EAAA1D,EAAA,CAAA2D,qBAAA,EAAA3D,EAAA,CAAA4D,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAC1B/c,kBAAkB,EAAEC,mBAAmB;MAAA+c,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}