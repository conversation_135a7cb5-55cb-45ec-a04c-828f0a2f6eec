import { Injectable } from '@angular/core';
import { SpaceTemplateSelectorService, SpaceTemplateSelectorConfig } from './space-template-selector.service';
import { SpaceTemplateConfig } from './space-template-selector.component';
import { Observable } from 'rxjs';

/**
 * 空間模板選擇器輔助工具
 * 提供快速創建常用按鈕配置的方法
 */
@Injectable({
    providedIn: 'root'
})
export class SpaceTemplateSelectorHelper {

    constructor(private spaceTemplateSelectorService: SpaceTemplateSelectorService) { }

    /**
     * 創建標準的模板新增按鈕配置
     */
    createTemplateAddButton(buildCaseId: string): SpaceTemplateSelectorConfig {
        return {
            buildCaseId,
            buttonText: '模板新增',
            buttonIcon: 'fas fa-layer-group',
            buttonClass: 'btn btn-warning mr-2'
        };
    }

    /**
     * 創建自定義的模板選擇按鈕配置
     */
    createCustomButton(options: {
        buildCaseId: string;
        text?: string;
        icon?: string;
        buttonClass?: string;
    }): SpaceTemplateSelectorConfig {
        return {
            buildCaseId: options.buildCaseId,
            buttonText: options.text || '選擇模板',
            buttonIcon: options.icon || 'fas fa-layer-group',
            buttonClass: options.buttonClass || 'btn btn-primary'
        };
    }

    /**
     * 創建小尺寸按鈕配置
     */
    createSmallButton(buildCaseId: string, text: string = '模板'): SpaceTemplateSelectorConfig {
        return {
            buildCaseId,
            buttonText: text,
            buttonIcon: 'fas fa-layer-group',
            buttonClass: 'btn btn-outline-primary btn-sm'
        };
    }

    /**
     * 創建成功樣式按鈕配置
     */
    createSuccessButton(buildCaseId: string, text: string = '套用模板'): SpaceTemplateSelectorConfig {
        return {
            buildCaseId,
            buttonText: text,
            buttonIcon: 'fas fa-check',
            buttonClass: 'btn btn-success'
        };
    }

    /**
     * 快速開啟標準模板選擇器
     */
    openStandardSelector(buildCaseId: string): Observable<SpaceTemplateConfig | null> {
        const config = this.createTemplateAddButton(buildCaseId);
        return this.spaceTemplateSelectorService.openSelector(config);
    }

    /**
     * 快速開啟自定義模板選擇器
     */
    openCustomSelector(options: {
        buildCaseId: string;
        text?: string;
        icon?: string;
        buttonClass?: string;
    }): Observable<SpaceTemplateConfig | null> {
        const config = this.createCustomButton(options);
        return this.spaceTemplateSelectorService.openSelector(config);
    }
}
