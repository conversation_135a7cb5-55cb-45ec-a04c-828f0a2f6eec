{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { SharedModule } from '../../../components/shared.module';\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { LabelInOptionsPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { BaseComponent } from '../../../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { DateFormatHourPipe, DateFormatPipe } from 'src/app/@theme/pipes';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { FileUploadComponent } from '../../../components/file-upload/file-upload.component';\nlet ReviewDocumentManagementComponent = class ReviewDocumentManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, _reviewService, reviewService, utilityService, _houseCustomService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this._reviewService = _reviewService;\n    this.reviewService = reviewService;\n    this.utilityService = utilityService;\n    this._houseCustomService = _houseCustomService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.reviewTypeOptions = [{\n      value: 1,\n      label: '標準圖' //standard drawing\n    }, {\n      value: 2,\n      label: '設備圖' //equipment drawing\n    }];\n    this.reviewTypeOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 1,\n      label: '標準圖' //standard drawing\n    }, {\n      value: 2,\n      label: '設備圖' //equipment drawing\n    }];\n    this.examineStatusOptions = [{\n      value: -1,\n      label: '待審核' //Pending review\n    }, {\n      value: 1,\n      label: '已通過' //passed\n    }, {\n      value: 2,\n      label: '已駁回' //rejected\n    }];\n    this.examineStatusOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 0,\n      label: '待審核' //Pending review\n    }, {\n      value: 1,\n      label: '已通過' //passed\n    }, {\n      value: 2,\n      label: '已駁回' //rejected\n    }];\n    this.statusOptions = [{\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n    this.statusOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    // 新增：新戶別選擇器相關屬性\n    this.buildingData = {}; // 存放建築物戶別資料\n    this.selectedHouseholds = []; // 選中的戶別ID (使用 houseId)\n    this.fileName = null;\n    this.imageUrl = undefined;\n    this.fileUploadConfig = {\n      acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', '.dwg', '.dxf'],\n      acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\n      acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\n      label: '上傳檔案',\n      helpText: '*請上傳PDF格式或CAD檔案（.dwg, .dxf）',\n      required: true,\n      disabled: false,\n      autoFillName: true,\n      buttonText: '上傳',\n      buttonIcon: 'fa-solid fa-cloud-arrow-up',\n      maxFileSize: 10,\n      multiple: false,\n      showPreview: false\n    };\n    this.latestAction = 0;\n    this.isNew = true;\n  }\n  ngOnInit() {\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\n      this.searchQuery = {\n        selectedBuildCase: null,\n        selectedReviewType: this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value) ? this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value) : this.reviewTypeOptionsQuery[0],\n        selectedExamineStatus: this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value) ? this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value) : this.examineStatusOptionsQuery[0],\n        seletedStatus: this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value) ? this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value) : this.statusOptionsQuery[0],\n        CReviewName: previous_search.CReviewName\n      };\n    } else {\n      this.searchQuery = {\n        selectedBuildCase: null,\n        selectedReviewType: this.reviewTypeOptionsQuery[0],\n        selectedExamineStatus: this.examineStatusOptionsQuery[0],\n        seletedStatus: this.statusOptionsQuery[0],\n        CReviewName: ''\n      };\n    }\n    this.getUserBuildCase();\n  }\n  // 文件上傳配置\n  get fileUploadConfigWithState() {\n    return {\n      ...this.fileUploadConfig,\n      disabled: this.latestAction === 1\n    };\n  }\n  clearImage() {\n    if (this.imageUrl) {\n      this.imageUrl = null;\n      this.fileName = null;\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n      }\n    }\n  }\n  // 處理共用元件的文件選擇事件\n  onFileUploaded(result) {\n    this.fileName = result.fileName;\n    this.imageUrl = {\n      CName: result.CName,\n      CFile: result.CFile,\n      Cimg: result.Cimg,\n      CFileUpload: result.CFileUpload,\n      CFileType: result.CFileType\n    };\n  }\n  // 處理共用元件的文件清除事件\n  onFileCleared() {\n    this.fileName = null;\n    this.imageUrl = null;\n  }\n  // 處理共用元件的自動填入名稱事件\n  onNameAutoFilled(fileName) {\n    if (!this.selectedReview.CReviewName) {\n      this.selectedReview.CReviewName = fileName;\n    }\n  }\n  // 更新 disabled 狀態\n  get isFileUploadDisabled() {\n    return this.latestAction === 1;\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  getReviewById(item, ref) {\n    this._reviewService.apiReviewGetReviewByIdPost$Json({\n      body: item.CReviewId\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        const data = res.Entries;\n        this.selectedReview = {\n          CBuildCaseId: data.tblReview?.CBuildCaseId,\n          CReviewId: data.tblReview?.CReviewId,\n          CReviewType: data.tblReview?.CReviewType,\n          CReviewName: data.tblReview?.CReviewName ? data.tblReview?.CReviewName : '',\n          CSort: data.tblReview?.CSort,\n          CStatus: data.tblReview?.CStatus,\n          CFileUrl: data.tblReview?.CFileUrl,\n          CExamineNote: data?.CExamineNote ? data?.CExamineNote : '',\n          seletedStatus: data.tblReview?.CStatus ? this.getItemByValue(data.tblReview?.CStatus, this.statusOptions) : this.statusOptions[0],\n          selectedReviewType: data.tblReview?.CReviewType ? this.getItemByValue(data.tblReview?.CReviewType, this.reviewTypeOptions) : this.reviewTypeOptions[0],\n          tblExamineLogs: data.tblExamineLogs,\n          reviewHouseHolds: data?.reviewHouseHolds?.filter(i => i.CIsSelect),\n          tblReview: data.tblReview\n        };\n        if (data && data?.tblExamineLogs && data?.tblExamineLogs.length) {\n          if (data?.tblExamineLogs.length === 0) return undefined;\n          this.latestAction = data?.tblExamineLogs[0].CAction;\n          let latestDate = data?.tblExamineLogs[0].CCreateDt ? new Date(data?.tblExamineLogs[0].CCreateDt) : '';\n          for (let i = 1; i < data.tblExamineLogs.length; i++) {\n            if (data.tblExamineLogs[i].CCreateDt) {\n              const currentDate = new Date(data.tblExamineLogs[i].CCreateDt);\n              if (currentDate > latestDate) {\n                latestDate = currentDate;\n                this.latestAction = data?.tblExamineLogs[i].CAction;\n              }\n            }\n          }\n        }\n        // 先準備選中的戶別ID資料（API 已更新，ReviewHouseHold 現在包含 CHouseID 為 number 類型）\n        let selectedHouseholdIds = [];\n        if (data?.reviewHouseHolds) {\n          selectedHouseholdIds = data.reviewHouseHolds.filter(item => item.CIsSelect && item.CHouseID).map(item => item.CHouseID);\n        }\n        // 載入建築物資料，並在載入完成後設置選中的戶別\n        this.loadBuildingDataFromAPI(selectedHouseholdIds, ref);\n      }\n    });\n  }\n  onSaveReview(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 使用新戶別選擇器的結果\n    let houseReviews = [];\n    if (this.selectedHouseholds && this.selectedHouseholds.length > 0) {\n      houseReviews = this.convertSelectedHouseholdsToHouseReview();\n    }\n    this.saveReviewPostRes = {\n      CBuildCaseId: this.searchQuery.selectedBuildCase.value,\n      CReviewId: this.selectedReview.CReviewId,\n      CReviewType: this.selectedReview.selectedReviewType.value,\n      CReviewName: this.selectedReview.CReviewName,\n      CSort: this.selectedReview?.CSort,\n      CStatus: this.selectedReview.seletedStatus.value,\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\n      CExamineNote: this.selectedReview.CExamineNote,\n      HouseReviews: houseReviews\n    };\n    this.reviewService.SaveReview(this.saveReviewPostRes).subscribe(res => {\n      if (res && res.body && res.body.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.clearImage();\n        this.getReviewList();\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res && res.body && res.body.Message);\n      }\n    });\n  }\n  onSearch() {\n    let previous_search = {\n      CReviewName: this.searchQuery.CReviewName,\n      CSelectedBuildCase: this.searchQuery.selectedBuildCase,\n      CSeletedStatus: this.searchQuery.seletedStatus,\n      CReviewType: this.searchQuery.selectedReviewType,\n      CExamineStatus: this.searchQuery.selectedExamineStatus\n    };\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.REVIEW_SEARCH, JSON.stringify(previous_search));\n    this.getReviewList();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getReviewList();\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) {\n      this.utilityService.openFileNewTab(CFileUrl);\n    }\n  }\n  getReviewList() {\n    return this._reviewService.apiReviewGetReviewListPost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CReviewName: this.searchQuery.CReviewName,\n        CBuildCaseID: this.searchQuery.selectedBuildCase.value,\n        CStatus: this.searchQuery.seletedStatus.value,\n        CReviewType: this.searchQuery.selectedReviewType.value,\n        CExamineStatus: this.searchQuery.selectedExamineStatus.value\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.reviewList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    })).subscribe();\n  }\n  onSelectionChangeBuildCase() {\n    if (this.searchQuery.selectedBuildCase.value) {\n      this.getReviewList();\n      // 載入新戶別選擇器的建築物資料\n      this.loadBuildingDataFromAPI();\n    }\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            label: res.CBuildCaseName,\n            value: res.cID\n          };\n        });\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\n          if (previous_search.CSelectedBuildCase != null && previous_search.CSelectedBuildCase != undefined) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.value == previous_search.CSelectedBuildCase.value);\n            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0];\n        }\n        if (this.searchQuery.selectedBuildCase.value) {\n          this.getReviewList();\n          // 載入新戶別選擇器的建築物資料\n          this.loadBuildingDataFromAPI();\n        }\n      }\n    })).subscribe();\n  }\n  openModel(ref, item) {\n    this.latestAction = 0;\n    this.isNew = true;\n    this.clearImage();\n    // 初始化新戶別選擇器\n    this.selectedHouseholds = [];\n    this.selectedReview = {\n      selectedReviewType: this.reviewTypeOptions[0],\n      seletedStatus: this.statusOptions[0],\n      selectedExamineStatus: this.examineStatusOptions[0],\n      CReviewName: '',\n      CSort: 0,\n      CFileUrl: '',\n      CExamineNote: '',\n      CIsSelectAll: false\n    };\n    if (item) {\n      this.isNew = false;\n      this.getReviewById(item, ref);\n    } else {\n      this.isNew = true;\n      // 載入新戶別選擇器的建築物資料，並在載入完成後開啟對話框\n      this.loadBuildingDataFromAPI(undefined, ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {}\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    if (this.isNew && !this.imageUrl) {\n      this.valid.addErrorMessage(`前台圖片`);\n    }\n    // 驗證戶別選擇\n    if (!this.selectedHouseholds || this.selectedHouseholds.length === 0) {\n      this.valid.addErrorMessage('[適用戶別]請至少選擇一個戶別');\n    }\n    this.valid.required('[送審說明]', this.selectedReview.CExamineNote);\n  }\n  getActionName(actionID) {\n    let textR = \"\";\n    if (actionID != undefined) {\n      switch (actionID) {\n        case 1:\n          textR = \"傳送\";\n          break;\n        case 2:\n          textR = \"通過\";\n          break;\n        case 3:\n          textR = \"駁回\";\n          break;\n        default:\n          break;\n      }\n    }\n    return textR;\n  }\n  // 新增：載入建築物戶別資料 (使用 GetDropDown API)\n  loadBuildingDataFromAPI(selectedHouseholdIds, dialogRef) {\n    if (!this.searchQuery.selectedBuildCase?.value) {\n      console.warn('沒有選擇建案，無法載入建築物資料');\n      return;\n    }\n    console.log('開始載入建築物資料，建案ID:', this.searchQuery.selectedBuildCase.value);\n    this._houseCustomService.getDropDown(this.searchQuery.selectedBuildCase.value).subscribe({\n      next: response => {\n        console.log('API 回應:', response);\n        if (response && response.Entries) {\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n          console.log('轉換後的建築物資料:', this.buildingData);\n          // 在建築物資料載入完成後，設置選中的戶別ID\n          if (selectedHouseholdIds && selectedHouseholdIds.length > 0) {\n            console.log('設置選中的戶別ID:', selectedHouseholdIds);\n            this.selectedHouseholds = [...selectedHouseholdIds];\n            // 觸發 ngModel 更新以確保戶別選擇器顯示選中的戶別\n            setTimeout(() => {\n              this.selectedHouseholds = [...selectedHouseholdIds];\n            }, 50);\n          }\n          // 開啟對話框\n          if (dialogRef) {\n            setTimeout(() => {\n              this.dialogService.open(dialogRef);\n            }, 100);\n          }\n        } else {\n          console.warn('API 回應無資料');\n          this.buildingData = {};\n          if (dialogRef) {\n            this.dialogService.open(dialogRef);\n          }\n        }\n      },\n      error: error => {\n        console.error('載入建築物戶別資料失敗:', error);\n        this.buildingData = {};\n        if (dialogRef) {\n          this.dialogService.open(dialogRef);\n        }\n      }\n    });\n  }\n  // 新增：將 API 回應轉換為建築物資料格式\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      if (Array.isArray(houses) && houses.length > 0) {\n        buildingData[building] = houses.map(house => ({\n          houseName: house.HouseName || '',\n          building: building,\n          floor: house.Floor ? `${house.Floor}F` : '',\n          houseId: house.HouseId,\n          houseType: house.HouseType,\n          isSelected: false,\n          isDisabled: false\n        }));\n      }\n    });\n    console.log('轉換後的 buildingData:', buildingData);\n    return buildingData;\n  }\n  // 新增：處理戶別選擇變更\n  onHouseholdSelectionChange(selectedItems) {\n    // 更新 selectedHouseholds (使用 houseId)\n    this.selectedHouseholds = selectedItems.map(item => item.houseId).filter(id => id !== undefined);\n  }\n  // 新增：處理戶別ID變更事件\n  onHouseholdIdChange(selectedIds) {\n    this.selectedHouseholds = selectedIds;\n  }\n  // 新增：從新戶別選擇器的選擇結果轉換為原有格式\n  convertSelectedHouseholdsToHouseReview() {\n    const result = [];\n    // 從 buildingData 中找到對應的戶別資訊\n    if (this.buildingData && this.selectedHouseholds.length > 0) {\n      Object.values(this.buildingData).forEach(houses => {\n        houses.forEach(house => {\n          if (house.houseId && this.selectedHouseholds.includes(house.houseId)) {\n            result.push({\n              CHouseID: house.houseId,\n              CIsSelect: true,\n              CFloor: house.floor ? parseInt(house.floor.replace('F', '')) : undefined,\n              CHouseHold: house.houseName\n            });\n          }\n        });\n      });\n    }\n    return result;\n  }\n};\n__decorate([ViewChild('fileInput')], ReviewDocumentManagementComponent.prototype, \"fileInput\", void 0);\nReviewDocumentManagementComponent = __decorate([Component({\n  selector: 'ngx-review-document-management',\n  templateUrl: './review-document-management.component.html',\n  styleUrls: ['./review-document-management.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, AppSharedModule, NbDatepickerModule, NbDateFnsDateModule, DateFormatPipe, LabelInOptionsPipe, DateFormatHourPipe, FileUploadComponent]\n})], ReviewDocumentManagementComponent);\nexport { ReviewDocumentManagementComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "SharedModule", "AppSharedModule", "CommonModule", "NbDatepickerModule", "LabelInOptionsPipe", "BaseComponent", "tap", "NbDateFnsDateModule", "moment", "DateFormatHourPipe", "DateFormatPipe", "LocalStorageService", "STORAGE_KEY", "FileUploadComponent", "ReviewDocumentManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "_reviewService", "reviewService", "utilityService", "_houseCustomService", "pageFirst", "pageSize", "pageIndex", "totalRecords", "reviewTypeOptions", "value", "label", "reviewTypeOptionsQuery", "examineStatusOptions", "examineStatusOptionsQuery", "statusOptions", "statusOptionsQuery", "buildCaseOptions", "buildingData", "selectedHouseholds", "fileName", "imageUrl", "undefined", "fileUploadConfig", "acceptedTypes", "acceptedFileRegex", "acceptAttribute", "helpText", "required", "disabled", "autoFillName", "buttonText", "buttonIcon", "maxFileSize", "multiple", "showPreview", "latestAction", "isNew", "ngOnInit", "GetSessionStorage", "REVIEW_SEARCH", "previous_search", "JSON", "parse", "searchQuery", "selectedBuildCase", "selectedReviewType", "find", "x", "CReviewType", "selectedExamineStatus", "CExamineStatus", "se<PERSON><PERSON><PERSON><PERSON><PERSON>", "CSeletedStatus", "CReviewName", "getUserBuildCase", "fileUploadConfigWithState", "clearImage", "fileInput", "nativeElement", "onFileUploaded", "result", "CName", "CFile", "Cimg", "CFileUpload", "CFileType", "onFileCleared", "onNameAutoFilled", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isFileUploadDisabled", "getItemByValue", "options", "item", "getReviewById", "ref", "apiReviewGetReviewByIdPost$Json", "body", "CReviewId", "subscribe", "res", "Entries", "StatusCode", "data", "CBuildCaseId", "tblReview", "CSort", "CStatus", "CFileUrl", "CExamineNote", "tblExamineLogs", "reviewHouseHolds", "filter", "i", "CIsSelect", "length", "CAction", "latestDate", "CCreateDt", "Date", "currentDate", "selectedHouseholdIds", "CHouseID", "map", "loadBuildingDataFromAPI", "onSaveReview", "validation", "errorMessages", "showErrorMSGs", "houseReviews", "convertSelectedHouseholdsToHouseReview", "saveReviewPostRes", "HouseReviews", "SaveReview", "showSucessMSG", "getReviewList", "close", "showErrorMSG", "Message", "onSearch", "CSelectedBuildCase", "AddSessionStorage", "stringify", "pageChanged", "newPage", "openPdfInNewTab", "openFileNewTab", "apiReviewGetReviewListPost$Json", "PageIndex", "PageSize", "CBuildCaseID", "pipe", "reviewList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "userBuildCaseOptions", "CBuildCaseName", "cID", "index", "findIndex", "openModel", "CIsSelectAll", "formatDate", "CChangeDate", "format", "onSubmit", "onClose", "clear", "addErrorMessage", "getActionName", "actionID", "textR", "dialogRef", "console", "warn", "log", "getDropDown", "next", "response", "convertApiResponseToBuildingData", "setTimeout", "open", "error", "entries", "Object", "for<PERSON>ach", "building", "houses", "Array", "isArray", "house", "houseName", "HouseName", "floor", "Floor", "houseId", "HouseId", "houseType", "HouseType", "isSelected", "isDisabled", "onHouseholdSelectionChange", "selectedItems", "id", "onHouseholdIdChange", "selectedIds", "values", "includes", "push", "CFloor", "parseInt", "replace", "CHouseHold", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\review-document-management\\review-document-management.component.ts"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../../../components/shared.module';\r\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseService, ReviewService } from 'src/services/api/services';\r\nimport { LabelInOptionsPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { BaseComponent } from '../../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetReviewListRes, HouseReview, ReviewHouseHold, TblExamineLog, TblReview } from 'src/services/api/models';\r\nimport { tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { ReviewServiceCustom } from 'src/app/@core/service/review.service';\r\nimport { DateFormatHourPipe, DateFormatPipe } from 'src/app/@theme/pipes';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { co } from '@fullcalendar/core/internal-common';\r\nimport { FileUploadComponent, FileUploadConfig, FileUploadResult } from '../../../components/file-upload/file-upload.component';\r\nimport { HouseholdItem, BuildingData } from 'src/app/shared/components/household-binding/household-binding.component';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n}\r\n\r\nexport interface HouseList {\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseID?: number;\r\n  CID?: number;\r\n  CIsSelect?: boolean | null;\r\n  CHouseType?: number | null;\r\n  CIsEnable?: boolean | null;\r\n}\r\n\r\nexport interface SaveReviewPostParam {\r\n  CBuildCaseId?: number;\r\n  CReviewId?: number;\r\n  CReviewType?: number;\r\n  CReviewName?: string;\r\n  CSort?: number;\r\n  CStatus?: number;\r\n  CFile?: Blob;\r\n  CExamineNote?: string;\r\n  HouseReviews?: Array<HouseReview>;\r\n  CIsSelectAll?: boolean;\r\n  selectedCNoticeType?: any\r\n}\r\n\r\nexport interface ReviewType {\r\n  CBuildCaseId?: number;\r\n  CReviewId?: number;\r\n  CReviewType?: number | null;\r\n  CReviewName?: string;\r\n  CSort?: number;\r\n  CStatus?: number;\r\n  CFile?: Blob;\r\n  CExamineNote?: string;\r\n  HouseReviews?: Array<HouseReview>;\r\n  CIsSelectAll?: boolean;\r\n  selectedCNoticeType?: any,\r\n  CFileUrl?: string | null;\r\n  selectedBuildCase?: any | null;\r\n  selectedReviewType?: any | null;\r\n  selectedExamineStatus?: any | null;\r\n  seletedStatus?: any | null;\r\n  reviewHouseHolds?: Array<ReviewHouseHold>;\r\n  tblExamineLogs?: Array<TblExamineLog> | null;\r\n  tblReview?: TblReview;\r\n}\r\nexport interface SearchQuery {\r\n  selectedBuildCase?: any | null;\r\n  selectedReviewType?: any | null;\r\n  selectedExamineStatus?: any | null;\r\n  seletedStatus?: any | null;\r\n  CReviewName?: any | null;\r\n}\r\n@Component({\r\n  selector: 'ngx-review-document-management',\r\n  templateUrl: './review-document-management.component.html',\r\n  styleUrls: ['./review-document-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, AppSharedModule, NbDatepickerModule, NbDateFnsDateModule, DateFormatPipe, LabelInOptionsPipe, DateFormatHourPipe, FileUploadComponent],\r\n})\r\n\r\nexport class ReviewDocumentManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _reviewService: ReviewService,\r\n    private reviewService: ReviewServiceCustom,\r\n    private utilityService: UtilityService,\r\n    private _houseCustomService: HouseCustomService,\r\n  ) { super(_allow) }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  reviewTypeOptions = [{\r\n    value: 1,\r\n    label: '標準圖', //standard drawing\r\n  }, {\r\n    value: 2,\r\n    label: '設備圖', //equipment drawing\r\n  }\r\n  ]\r\n\r\n  reviewTypeOptionsQuery = [\r\n    {\r\n      value: -1,\r\n      label: '全部'\r\n    },\r\n    {\r\n      value: 1,\r\n      label: '標準圖', //standard drawing\r\n    }, {\r\n      value: 2,\r\n      label: '設備圖', //equipment drawing\r\n    }\r\n  ]\r\n\r\n  examineStatusOptions = [\r\n    {\r\n      value: -1,\r\n      label: '待審核' //Pending review\r\n\r\n    }, {\r\n      value: 1,\r\n      label: '已通過' //passed\r\n\r\n    },\r\n    {\r\n      value: 2,\r\n      label: '已駁回' //rejected\r\n\r\n    }\r\n  ]\r\n\r\n  examineStatusOptionsQuery = [{\r\n    value: -1,\r\n    label: '全部'\r\n  },\r\n  {\r\n    value: 0,\r\n    label: '待審核' //Pending review\r\n\r\n  }, {\r\n    value: 1,\r\n    label: '已通過' //passed\r\n\r\n  },\r\n  {\r\n    value: 2,\r\n    label: '已駁回' //rejected\r\n  }\r\n  ]\r\n\r\n  statusOptions = [{\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  },]\r\n\r\n  statusOptionsQuery = [{\r\n    value: -1,\r\n    label: '全部'\r\n  }, {\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  },]\r\n\r\n\r\n  searchQuery: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  // 新增：新戶別選擇器相關屬性\r\n  buildingData: BuildingData = {} // 存放建築物戶別資料\r\n  selectedHouseholds: number[] = [] // 選中的戶別ID (使用 houseId)\r\n\r\n\r\n  override ngOnInit(): void {\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\r\n      this.searchQuery = {\r\n        selectedBuildCase: null,\r\n        selectedReviewType: this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value)\r\n          ? this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value)\r\n          : this.reviewTypeOptionsQuery[0],\r\n        selectedExamineStatus: this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value)\r\n          ? this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value)\r\n          : this.examineStatusOptionsQuery[0],\r\n        seletedStatus: this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value)\r\n          ? this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value)\r\n          : this.statusOptionsQuery[0],\r\n        CReviewName: previous_search.CReviewName\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        selectedBuildCase: null,\r\n        selectedReviewType: this.reviewTypeOptionsQuery[0],\r\n        selectedExamineStatus: this.examineStatusOptionsQuery[0],\r\n        seletedStatus: this.statusOptionsQuery[0],\r\n        CReviewName: ''\r\n      }\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  fileName: string | null = null;\r\n  imageUrl: any = undefined;\r\n  // 文件上傳配置\r\n  get fileUploadConfigWithState(): FileUploadConfig {\r\n    return {\r\n      ...this.fileUploadConfig,\r\n      disabled: this.latestAction === 1\r\n    };\r\n  }\r\n  fileUploadConfig: FileUploadConfig = {\r\n    acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', '.dwg', '.dxf'],\r\n    acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\r\n    acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\r\n    label: '上傳檔案',\r\n    helpText: '*請上傳PDF格式或CAD檔案（.dwg, .dxf）',\r\n    required: true,\r\n    disabled: false,\r\n    autoFillName: true,\r\n    buttonText: '上傳',\r\n    buttonIcon: 'fa-solid fa-cloud-arrow-up',\r\n    maxFileSize: 10,\r\n    multiple: false,\r\n    showPreview: false\r\n  };\r\n  clearImage() {\r\n    if (this.imageUrl) {\r\n      this.imageUrl = null;\r\n      this.fileName = null;\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\r\n      }\r\n    }\r\n  }\r\n\r\n  // 處理共用元件的文件選擇事件\r\n  onFileUploaded(result: FileUploadResult) {\r\n    this.fileName = result.fileName;\r\n    this.imageUrl = {\r\n      CName: result.CName,\r\n      CFile: result.CFile,\r\n      Cimg: result.Cimg,\r\n      CFileUpload: result.CFileUpload,\r\n      CFileType: result.CFileType,\r\n    };\r\n  }\r\n\r\n  // 處理共用元件的文件清除事件\r\n  onFileCleared() {\r\n    this.fileName = null;\r\n    this.imageUrl = null;\r\n  }\r\n\r\n  // 處理共用元件的自動填入名稱事件\r\n  onNameAutoFilled(fileName: string) {\r\n    if (!this.selectedReview.CReviewName) {\r\n      this.selectedReview.CReviewName = fileName;\r\n    }\r\n  }\r\n\r\n  // 更新 disabled 狀態\r\n  get isFileUploadDisabled(): boolean {\r\n    return this.latestAction === 1;\r\n  }\r\n\r\n  // 舊的文件上傳方法 - 已被共用元件取代\r\n  /*\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;\r\n    if (!fileRegex.test(file.name)) {\r\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf或CAD檔案（dwg, dxf）');\r\n      return;\r\n    }\r\n    if (file) {\r\n      this.fileName = file.name;\r\n\r\n      // 如果名稱欄位為空，自動填入檔案名稱（去除副檔名）\r\n      if (!this.selectedReview.CReviewName) {\r\n        const fileName = file.name;\r\n        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\r\n        this.selectedReview.CReviewName = fileNameWithoutExtension;\r\n      }\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = (e: any) => {        // 判斷檔案類型\r\n        let fileType: number;\r\n        if (file.type.startsWith('image/')) {\r\n          fileType = EnumFileType.JPG; // 圖片\r\n        } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\r\n          fileType = 3; // CAD檔案\r\n        } else {\r\n          fileType = EnumFileType.PDF; // PDF\r\n        }\r\n\r\n        this.imageUrl = {\r\n          CName: file.name,\r\n          CFile: e.target?.result?.toString().split(',')[1],\r\n          Cimg: file.name.includes('pdf') ? file : file,\r\n          CFileUpload: file,\r\n          CFileType: fileType,\r\n        };\r\n        if (this.fileInput) {\r\n          this.fileInput.nativeElement.value = null;\r\n        }\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n  */\r\n\r\n\r\n  selectedReview: ReviewType\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n  latestAction: any = 0\r\n\r\n  getReviewById(item: any, ref: any) {\r\n\r\n    this._reviewService.apiReviewGetReviewByIdPost$Json({\r\n      body: item.CReviewId\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        const data = res.Entries\r\n        this.selectedReview = {\r\n          CBuildCaseId: data.tblReview?.CBuildCaseId,\r\n          CReviewId: data.tblReview?.CReviewId,\r\n          CReviewType: data.tblReview?.CReviewType,\r\n          CReviewName: data.tblReview?.CReviewName ? data.tblReview?.CReviewName : '',\r\n          CSort: data.tblReview?.CSort,\r\n          CStatus: data.tblReview?.CStatus,\r\n          CFileUrl: data.tblReview?.CFileUrl,\r\n          CExamineNote: data?.CExamineNote ? data?.CExamineNote : '',\r\n          seletedStatus: data.tblReview?.CStatus ? this.getItemByValue(data.tblReview?.CStatus, this.statusOptions) : this.statusOptions[0],\r\n          selectedReviewType: data.tblReview?.CReviewType ? this.getItemByValue(data.tblReview?.CReviewType, this.reviewTypeOptions) : this.reviewTypeOptions[0],\r\n          tblExamineLogs: data.tblExamineLogs,\r\n          reviewHouseHolds: data?.reviewHouseHolds?.filter((i: any) => i.CIsSelect),\r\n          tblReview: data.tblReview\r\n        }\r\n\r\n        if (data && data?.tblExamineLogs && data?.tblExamineLogs.length) {\r\n          if (data?.tblExamineLogs.length === 0) return undefined;\r\n          this.latestAction = data?.tblExamineLogs[0].CAction;\r\n          let latestDate = data?.tblExamineLogs[0].CCreateDt ? new Date(data?.tblExamineLogs[0].CCreateDt) : ''\r\n          for (let i = 1; i < data.tblExamineLogs.length; i++) {\r\n            if (data.tblExamineLogs[i].CCreateDt) {\r\n              const currentDate = new Date(data.tblExamineLogs[i].CCreateDt!)\r\n              if (currentDate > latestDate) {\r\n                latestDate = currentDate;\r\n                this.latestAction = data?.tblExamineLogs[i].CAction;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 先準備選中的戶別ID資料（API 已更新，ReviewHouseHold 現在包含 CHouseID 為 number 類型）\r\n        let selectedHouseholdIds: number[] = [];\r\n        if (data?.reviewHouseHolds) {\r\n          selectedHouseholdIds = data.reviewHouseHolds\r\n            .filter((item: any) => item.CIsSelect && item.CHouseID)\r\n            .map((item: any) => item.CHouseID);\r\n        }\r\n\r\n        // 載入建築物資料，並在載入完成後設置選中的戶別\r\n        this.loadBuildingDataFromAPI(selectedHouseholdIds, ref);\r\n      }\r\n    })\r\n  }\r\n\r\n  saveReviewPostRes: SaveReviewPostParam\r\n\r\n\r\n\r\n\r\n\r\n  onSaveReview(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    // 使用新戶別選擇器的結果\r\n    let houseReviews: HouseReview[] = [];\r\n\r\n    if (this.selectedHouseholds && this.selectedHouseholds.length > 0) {\r\n      houseReviews = this.convertSelectedHouseholdsToHouseReview();\r\n    }\r\n\r\n    this.saveReviewPostRes = {\r\n      CBuildCaseId: this.searchQuery.selectedBuildCase.value,\r\n      CReviewId: this.selectedReview.CReviewId,\r\n      CReviewType: this.selectedReview.selectedReviewType.value,\r\n      CReviewName: this.selectedReview.CReviewName,\r\n      CSort: this.selectedReview?.CSort,\r\n      CStatus: this.selectedReview.seletedStatus.value,\r\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\r\n      CExamineNote: this.selectedReview.CExamineNote,\r\n      HouseReviews: houseReviews,\r\n    }\r\n    this.reviewService.SaveReview(this.saveReviewPostRes).subscribe(res => {\r\n      if (res && res.body! && res.body.StatusCode! === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.clearImage()\r\n        this.getReviewList()\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res && res.body && res.body.Message!);\r\n      }\r\n    })\r\n  }\r\n\r\n  onSearch() {\r\n    let previous_search = {\r\n      CReviewName: this.searchQuery.CReviewName,\r\n      CSelectedBuildCase: this.searchQuery.selectedBuildCase,\r\n      CSeletedStatus: this.searchQuery.seletedStatus,\r\n      CReviewType: this.searchQuery.selectedReviewType,\r\n      CExamineStatus: this.searchQuery.selectedExamineStatus,\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.REVIEW_SEARCH, JSON.stringify(previous_search));\r\n    this.getReviewList()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getReviewList()\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  reviewList: GetReviewListRes[] | undefined\r\n\r\n  isNew = true\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) {\r\n      this.utilityService.openFileNewTab(CFileUrl)\r\n    }\r\n  }\r\n\r\n  getReviewList() {\r\n    return this._reviewService.apiReviewGetReviewListPost$Json({\r\n      body: {\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CReviewName: this.searchQuery.CReviewName,\r\n        CBuildCaseID: this.searchQuery.selectedBuildCase.value,\r\n        CStatus: this.searchQuery.seletedStatus.value,\r\n        CReviewType: this.searchQuery.selectedReviewType.value,\r\n        CExamineStatus: this.searchQuery.selectedExamineStatus.value,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.reviewList = res.Entries\r\n          this.totalRecords = res.TotalItems!\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n\r\n  onSelectionChangeBuildCase() {\r\n    if (this.searchQuery.selectedBuildCase.value) {\r\n      this.getReviewList()\r\n      // 載入新戶別選擇器的建築物資料\r\n      this.loadBuildingDataFromAPI()\r\n    }\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              label: res.CBuildCaseName,\r\n              value: res.cID\r\n            }\r\n          })\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\r\n            if (previous_search.CSelectedBuildCase != null && previous_search.CSelectedBuildCase != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.value == previous_search.CSelectedBuildCase.value)\r\n              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0]\r\n          }\r\n          if (this.searchQuery.selectedBuildCase.value) {\r\n            this.getReviewList()\r\n            // 載入新戶別選擇器的建築物資料\r\n            this.loadBuildingDataFromAPI()\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe()\r\n  }\r\n\r\n  openModel(ref: any, item?: any) {\r\n    this.latestAction = 0\r\n    this.isNew = true\r\n    this.clearImage()\r\n\r\n    // 初始化新戶別選擇器\r\n    this.selectedHouseholds = []\r\n\r\n    this.selectedReview = {\r\n      selectedReviewType: this.reviewTypeOptions[0],\r\n      seletedStatus: this.statusOptions[0],\r\n      selectedExamineStatus: this.examineStatusOptions[0],\r\n      CReviewName: '',\r\n      CSort: 0,\r\n      CFileUrl: '',\r\n      CExamineNote: '',\r\n      CIsSelectAll: false\r\n    }\r\n\r\n    if (item) {\r\n      this.isNew = false\r\n      this.getReviewById(item, ref)\r\n    } else {\r\n      this.isNew = true\r\n      // 載入新戶別選擇器的建築物資料，並在載入完成後開啟對話框\r\n      this.loadBuildingDataFromAPI(undefined, ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    if (this.isNew && !this.imageUrl) {\r\n      this.valid.addErrorMessage(`前台圖片`);\r\n    }\r\n\r\n    // 驗證戶別選擇\r\n    if (!this.selectedHouseholds || this.selectedHouseholds.length === 0) {\r\n      this.valid.addErrorMessage('[適用戶別]請至少選擇一個戶別');\r\n    }\r\n\r\n    this.valid.required('[送審說明]', this.selectedReview.CExamineNote)\r\n  }\r\n\r\n  getActionName(actionID: number | undefined) {\r\n    let textR = \"\";\r\n    if (actionID != undefined) {\r\n      switch (actionID) {\r\n        case 1:\r\n          textR = \"傳送\";\r\n          break;\r\n        case 2:\r\n          textR = \"通過\";\r\n          break;\r\n        case 3:\r\n          textR = \"駁回\";\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n    return textR;\r\n  }\r\n\r\n\r\n\r\n  // 新增：載入建築物戶別資料 (使用 GetDropDown API)\r\n  private loadBuildingDataFromAPI(selectedHouseholdIds?: number[], dialogRef?: any): void {\r\n    if (!this.searchQuery.selectedBuildCase?.value) {\r\n      console.warn('沒有選擇建案，無法載入建築物資料');\r\n      return;\r\n    }\r\n\r\n    console.log('開始載入建築物資料，建案ID:', this.searchQuery.selectedBuildCase.value);\r\n\r\n    this._houseCustomService.getDropDown(this.searchQuery.selectedBuildCase.value).subscribe({\r\n      next: (response) => {\r\n        console.log('API 回應:', response);\r\n        if (response && response.Entries) {\r\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n          console.log('轉換後的建築物資料:', this.buildingData);\r\n\r\n          // 在建築物資料載入完成後，設置選中的戶別ID\r\n          if (selectedHouseholdIds && selectedHouseholdIds.length > 0) {\r\n            console.log('設置選中的戶別ID:', selectedHouseholdIds);\r\n            this.selectedHouseholds = [...selectedHouseholdIds];\r\n            // 觸發 ngModel 更新以確保戶別選擇器顯示選中的戶別\r\n            setTimeout(() => {\r\n              this.selectedHouseholds = [...selectedHouseholdIds];\r\n            }, 50);\r\n          }\r\n\r\n          // 開啟對話框\r\n          if (dialogRef) {\r\n            setTimeout(() => {\r\n              this.dialogService.open(dialogRef);\r\n            }, 100);\r\n          }\r\n        } else {\r\n          console.warn('API 回應無資料');\r\n          this.buildingData = {};\r\n          if (dialogRef) {\r\n            this.dialogService.open(dialogRef);\r\n          }\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('載入建築物戶別資料失敗:', error);\r\n        this.buildingData = {};\r\n        if (dialogRef) {\r\n          this.dialogService.open(dialogRef);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：將 API 回應轉換為建築物資料格式\r\n  private convertApiResponseToBuildingData(entries: any): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]: [string, any]) => {\r\n      if (Array.isArray(houses) && houses.length > 0) {\r\n        buildingData[building] = houses.map((house: any) => ({\r\n          houseName: house.HouseName || '',\r\n          building: building,\r\n          floor: house.Floor ? `${house.Floor}F` : '',\r\n          houseId: house.HouseId,\r\n          houseType: house.HouseType,\r\n          isSelected: false,\r\n          isDisabled: false\r\n        }));\r\n      }\r\n    });\r\n\r\n    console.log('轉換後的 buildingData:', buildingData);\r\n    return buildingData;\r\n  }\r\n\r\n\r\n\r\n  // 新增：處理戶別選擇變更\r\n  onHouseholdSelectionChange(selectedItems: HouseholdItem[]) {\r\n    // 更新 selectedHouseholds (使用 houseId)\r\n    this.selectedHouseholds = selectedItems.map(item => item.houseId).filter(id => id !== undefined) as number[];\r\n  }\r\n\r\n  // 新增：處理戶別ID變更事件\r\n  onHouseholdIdChange(selectedIds: number[]) {\r\n    this.selectedHouseholds = selectedIds;\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n  // 新增：從新戶別選擇器的選擇結果轉換為原有格式\r\n  convertSelectedHouseholdsToHouseReview(): HouseReview[] {\r\n    const result: HouseReview[] = [];\r\n\r\n    // 從 buildingData 中找到對應的戶別資訊\r\n    if (this.buildingData && this.selectedHouseholds.length > 0) {\r\n      Object.values(this.buildingData).forEach(houses => {\r\n        houses.forEach(house => {\r\n          if (house.houseId && this.selectedHouseholds.includes(house.houseId)) {\r\n            result.push({\r\n              CHouseID: house.houseId,\r\n              CIsSelect: true,\r\n              CFloor: house.floor ? parseInt(house.floor.replace('F', '')) : undefined,\r\n              CHouseHold: house.houseName,\r\n            });\r\n          }\r\n        });\r\n      });\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAsBC,SAAS,QAAQ,eAAe;AACxE,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASA,YAAY,IAAIC,eAAe,QAAQ,8BAA8B;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAyB,gBAAgB;AAIpE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,aAAa,QAAQ,wCAAwC;AAGtE,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,sBAAsB;AAEzE,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAE9D,SAASC,mBAAmB,QAA4C,uDAAuD;AAoExH,IAAMC,iCAAiC,GAAvC,MAAMA,iCAAkC,SAAQT,aAAa;EAClEU,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,cAA6B,EAC7BC,aAAkC,EAClCC,cAA8B,EAC9BC,mBAAuC;IAC7C,KAAK,CAACT,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAGpB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,iBAAiB,GAAG,CAAC;MACnBC,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAE;KACf,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAE;KACf,CACA;IAED,KAAAC,sBAAsB,GAAG,CACvB;MACEF,KAAK,EAAE,CAAC,CAAC;MACTC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAE;KACf,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAE;KACf,CACF;IAED,KAAAE,oBAAoB,GAAG,CACrB;MACEH,KAAK,EAAE,CAAC,CAAC;MACTC,KAAK,EAAE,KAAK,CAAC;KAEd,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAC;KAEd,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAC;KAEd,CACF;IAED,KAAAG,yBAAyB,GAAG,CAAC;MAC3BJ,KAAK,EAAE,CAAC,CAAC;MACTC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAC;KAEd,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAC;KAEd,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAC;KACd,CACA;IAED,KAAAI,aAAa,GAAG,CAAC;MACfL,KAAK,EAAE,CAAC;MAAE;MACVC,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC;KACb,CAAE;IAEH,KAAAK,kBAAkB,GAAG,CAAC;MACpBN,KAAK,EAAE,CAAC,CAAC;MACTC,KAAK,EAAE;KACR,EAAE;MACDD,KAAK,EAAE,CAAC;MAAE;MACVC,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC;KACb,CAAE;IAKH,KAAAM,gBAAgB,GAAU,CAAC;MAAEN,KAAK,EAAE,IAAI;MAAED,KAAK,EAAE;IAAE,CAAE,CAAC;IAItD;IACA,KAAAQ,YAAY,GAAiB,EAAE,EAAC;IAChC,KAAAC,kBAAkB,GAAa,EAAE,EAAC;IAkClC,KAAAC,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,QAAQ,GAAQC,SAAS;IAQzB,KAAAC,gBAAgB,GAAqB;MACnCC,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,CAAC;MAC7EC,iBAAiB,EAAE,2BAA2B;MAC9CC,eAAe,EAAE,oDAAoD;MACrEf,KAAK,EAAE,MAAM;MACbgB,QAAQ,EAAE,6BAA6B;MACvCC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,4BAA4B;MACxCC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE;KACd;IAkGD,KAAAC,YAAY,GAAQ,CAAC;IAmIrB,KAAAC,KAAK,GAAG,IAAI;EA5XM;EAiGTC,QAAQA,CAAA;IACf,IAAIhD,mBAAmB,CAACiD,iBAAiB,CAAChD,WAAW,CAACiD,aAAa,CAAC,IAAI,IAAI,IACvElD,mBAAmB,CAACiD,iBAAiB,CAAChD,WAAW,CAACiD,aAAa,CAAC,IAAIlB,SAAS,IAC7EhC,mBAAmB,CAACiD,iBAAiB,CAAChD,WAAW,CAACiD,aAAa,CAAC,IAAI,EAAE,EAAE;MAC3E,IAAIC,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACrD,mBAAmB,CAACiD,iBAAiB,CAAChD,WAAW,CAACiD,aAAa,CAAC,CAAC;MAClG,IAAI,CAACI,WAAW,GAAG;QACjBC,iBAAiB,EAAE,IAAI;QACvBC,kBAAkB,EAAE,IAAI,CAAClC,sBAAsB,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,KAAK,IAAI+B,eAAe,CAACQ,WAAW,CAACvC,KAAK,CAAC,GACnG,IAAI,CAACE,sBAAsB,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,KAAK,IAAI+B,eAAe,CAACQ,WAAW,CAACvC,KAAK,CAAC,GACnF,IAAI,CAACE,sBAAsB,CAAC,CAAC,CAAC;QAClCsC,qBAAqB,EAAE,IAAI,CAACpC,yBAAyB,CAACiC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,KAAK,IAAI+B,eAAe,CAACU,cAAc,CAACzC,KAAK,CAAC,GAC5G,IAAI,CAACI,yBAAyB,CAACiC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,KAAK,IAAI+B,eAAe,CAACU,cAAc,CAACzC,KAAK,CAAC,GACzF,IAAI,CAACI,yBAAyB,CAAC,CAAC,CAAC;QACrCsC,aAAa,EAAE,IAAI,CAACpC,kBAAkB,CAAC+B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,KAAK,IAAI+B,eAAe,CAACY,cAAc,CAAC3C,KAAK,CAAC,GAC7F,IAAI,CAACM,kBAAkB,CAAC+B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,KAAK,IAAI+B,eAAe,CAACY,cAAc,CAAC3C,KAAK,CAAC,GAClF,IAAI,CAACM,kBAAkB,CAAC,CAAC,CAAC;QAC9BsC,WAAW,EAAEb,eAAe,CAACa;OAC9B;IACH,CAAC,MACI;MACH,IAAI,CAACV,WAAW,GAAG;QACjBC,iBAAiB,EAAE,IAAI;QACvBC,kBAAkB,EAAE,IAAI,CAAClC,sBAAsB,CAAC,CAAC,CAAC;QAClDsC,qBAAqB,EAAE,IAAI,CAACpC,yBAAyB,CAAC,CAAC,CAAC;QACxDsC,aAAa,EAAE,IAAI,CAACpC,kBAAkB,CAAC,CAAC,CAAC;QACzCsC,WAAW,EAAE;OACd;IACH;IACA,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAIA;EACA,IAAIC,yBAAyBA,CAAA;IAC3B,OAAO;MACL,GAAG,IAAI,CAACjC,gBAAgB;MACxBM,QAAQ,EAAE,IAAI,CAACO,YAAY,KAAK;KACjC;EACH;EAgBAqB,UAAUA,CAAA;IACR,IAAI,IAAI,CAACpC,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACD,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACsC,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACjD,KAAK,GAAG,IAAI,CAAC,CAAC;MAC7C;IACF;EACF;EAEA;EACAkD,cAAcA,CAACC,MAAwB;IACrC,IAAI,CAACzC,QAAQ,GAAGyC,MAAM,CAACzC,QAAQ;IAC/B,IAAI,CAACC,QAAQ,GAAG;MACdyC,KAAK,EAAED,MAAM,CAACC,KAAK;MACnBC,KAAK,EAAEF,MAAM,CAACE,KAAK;MACnBC,IAAI,EAAEH,MAAM,CAACG,IAAI;MACjBC,WAAW,EAAEJ,MAAM,CAACI,WAAW;MAC/BC,SAAS,EAAEL,MAAM,CAACK;KACnB;EACH;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAAC/C,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAG,IAAI;EACtB;EAEA;EACA+C,gBAAgBA,CAAChD,QAAgB;IAC/B,IAAI,CAAC,IAAI,CAACiD,cAAc,CAACf,WAAW,EAAE;MACpC,IAAI,CAACe,cAAc,CAACf,WAAW,GAAGlC,QAAQ;IAC5C;EACF;EAEA;EACA,IAAIkD,oBAAoBA,CAAA;IACtB,OAAO,IAAI,CAAClC,YAAY,KAAK,CAAC;EAChC;EAmDAmC,cAAcA,CAAC7D,KAAU,EAAE8D,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC/D,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO+D,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAGAC,aAAaA,CAACD,IAAS,EAAEE,GAAQ;IAE/B,IAAI,CAAC1E,cAAc,CAAC2E,+BAA+B,CAAC;MAClDC,IAAI,EAAEJ,IAAI,CAACK;KACZ,CAAC,CAACC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,MAAMC,IAAI,GAAGH,GAAG,CAACC,OAAO;QACxB,IAAI,CAACZ,cAAc,GAAG;UACpBe,YAAY,EAAED,IAAI,CAACE,SAAS,EAAED,YAAY;UAC1CN,SAAS,EAAEK,IAAI,CAACE,SAAS,EAAEP,SAAS;UACpC7B,WAAW,EAAEkC,IAAI,CAACE,SAAS,EAAEpC,WAAW;UACxCK,WAAW,EAAE6B,IAAI,CAACE,SAAS,EAAE/B,WAAW,GAAG6B,IAAI,CAACE,SAAS,EAAE/B,WAAW,GAAG,EAAE;UAC3EgC,KAAK,EAAEH,IAAI,CAACE,SAAS,EAAEC,KAAK;UAC5BC,OAAO,EAAEJ,IAAI,CAACE,SAAS,EAAEE,OAAO;UAChCC,QAAQ,EAAEL,IAAI,CAACE,SAAS,EAAEG,QAAQ;UAClCC,YAAY,EAAEN,IAAI,EAAEM,YAAY,GAAGN,IAAI,EAAEM,YAAY,GAAG,EAAE;UAC1DrC,aAAa,EAAE+B,IAAI,CAACE,SAAS,EAAEE,OAAO,GAAG,IAAI,CAAChB,cAAc,CAACY,IAAI,CAACE,SAAS,EAAEE,OAAO,EAAE,IAAI,CAACxE,aAAa,CAAC,GAAG,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC;UACjI+B,kBAAkB,EAAEqC,IAAI,CAACE,SAAS,EAAEpC,WAAW,GAAG,IAAI,CAACsB,cAAc,CAACY,IAAI,CAACE,SAAS,EAAEpC,WAAW,EAAE,IAAI,CAACxC,iBAAiB,CAAC,GAAG,IAAI,CAACA,iBAAiB,CAAC,CAAC,CAAC;UACtJiF,cAAc,EAAEP,IAAI,CAACO,cAAc;UACnCC,gBAAgB,EAAER,IAAI,EAAEQ,gBAAgB,EAAEC,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACC,SAAS,CAAC;UACzET,SAAS,EAAEF,IAAI,CAACE;SACjB;QAED,IAAIF,IAAI,IAAIA,IAAI,EAAEO,cAAc,IAAIP,IAAI,EAAEO,cAAc,CAACK,MAAM,EAAE;UAC/D,IAAIZ,IAAI,EAAEO,cAAc,CAACK,MAAM,KAAK,CAAC,EAAE,OAAOzE,SAAS;UACvD,IAAI,CAACc,YAAY,GAAG+C,IAAI,EAAEO,cAAc,CAAC,CAAC,CAAC,CAACM,OAAO;UACnD,IAAIC,UAAU,GAAGd,IAAI,EAAEO,cAAc,CAAC,CAAC,CAAC,CAACQ,SAAS,GAAG,IAAIC,IAAI,CAAChB,IAAI,EAAEO,cAAc,CAAC,CAAC,CAAC,CAACQ,SAAS,CAAC,GAAG,EAAE;UACrG,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,IAAI,CAACO,cAAc,CAACK,MAAM,EAAEF,CAAC,EAAE,EAAE;YACnD,IAAIV,IAAI,CAACO,cAAc,CAACG,CAAC,CAAC,CAACK,SAAS,EAAE;cACpC,MAAME,WAAW,GAAG,IAAID,IAAI,CAAChB,IAAI,CAACO,cAAc,CAACG,CAAC,CAAC,CAACK,SAAU,CAAC;cAC/D,IAAIE,WAAW,GAAGH,UAAU,EAAE;gBAC5BA,UAAU,GAAGG,WAAW;gBACxB,IAAI,CAAChE,YAAY,GAAG+C,IAAI,EAAEO,cAAc,CAACG,CAAC,CAAC,CAACG,OAAO;cACrD;YACF;UACF;QACF;QAEA;QACA,IAAIK,oBAAoB,GAAa,EAAE;QACvC,IAAIlB,IAAI,EAAEQ,gBAAgB,EAAE;UAC1BU,oBAAoB,GAAGlB,IAAI,CAACQ,gBAAgB,CACzCC,MAAM,CAAEnB,IAAS,IAAKA,IAAI,CAACqB,SAAS,IAAIrB,IAAI,CAAC6B,QAAQ,CAAC,CACtDC,GAAG,CAAE9B,IAAS,IAAKA,IAAI,CAAC6B,QAAQ,CAAC;QACtC;QAEA;QACA,IAAI,CAACE,uBAAuB,CAACH,oBAAoB,EAAE1B,GAAG,CAAC;MACzD;IACF,CAAC,CAAC;EACJ;EAQA8B,YAAYA,CAAC9B,GAAQ;IACnB,IAAI,CAAC+B,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5G,KAAK,CAAC6G,aAAa,CAACZ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClG,OAAO,CAAC+G,aAAa,CAAC,IAAI,CAAC9G,KAAK,CAAC6G,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAIE,YAAY,GAAkB,EAAE;IAEpC,IAAI,IAAI,CAAC1F,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAAC4E,MAAM,GAAG,CAAC,EAAE;MACjEc,YAAY,GAAG,IAAI,CAACC,sCAAsC,EAAE;IAC9D;IAEA,IAAI,CAACC,iBAAiB,GAAG;MACvB3B,YAAY,EAAE,IAAI,CAACxC,WAAW,CAACC,iBAAiB,CAACnC,KAAK;MACtDoE,SAAS,EAAE,IAAI,CAACT,cAAc,CAACS,SAAS;MACxC7B,WAAW,EAAE,IAAI,CAACoB,cAAc,CAACvB,kBAAkB,CAACpC,KAAK;MACzD4C,WAAW,EAAE,IAAI,CAACe,cAAc,CAACf,WAAW;MAC5CgC,KAAK,EAAE,IAAI,CAACjB,cAAc,EAAEiB,KAAK;MACjCC,OAAO,EAAE,IAAI,CAAClB,cAAc,CAACjB,aAAa,CAAC1C,KAAK;MAChDqD,KAAK,EAAE,IAAI,CAAC1C,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4C,WAAW,GAAG3C,SAAS;MAC5DmE,YAAY,EAAE,IAAI,CAACpB,cAAc,CAACoB,YAAY;MAC9CuB,YAAY,EAAEH;KACf;IACD,IAAI,CAAC3G,aAAa,CAAC+G,UAAU,CAAC,IAAI,CAACF,iBAAiB,CAAC,CAAChC,SAAS,CAACC,GAAG,IAAG;MACpE,IAAIA,GAAG,IAAIA,GAAG,CAACH,IAAK,IAAIG,GAAG,CAACH,IAAI,CAACK,UAAW,KAAK,CAAC,EAAE;QAClD,IAAI,CAACrF,OAAO,CAACqH,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACzD,UAAU,EAAE;QACjB,IAAI,CAAC0D,aAAa,EAAE;QACpBxC,GAAG,CAACyC,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACvH,OAAO,CAACwH,YAAY,CAACrC,GAAG,IAAIA,GAAG,CAACH,IAAI,IAAIG,GAAG,CAACH,IAAI,CAACyC,OAAQ,CAAC;MACjE;IACF,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI9E,eAAe,GAAG;MACpBa,WAAW,EAAE,IAAI,CAACV,WAAW,CAACU,WAAW;MACzCkE,kBAAkB,EAAE,IAAI,CAAC5E,WAAW,CAACC,iBAAiB;MACtDQ,cAAc,EAAE,IAAI,CAACT,WAAW,CAACQ,aAAa;MAC9CH,WAAW,EAAE,IAAI,CAACL,WAAW,CAACE,kBAAkB;MAChDK,cAAc,EAAE,IAAI,CAACP,WAAW,CAACM;KAClC;IACD5D,mBAAmB,CAACmI,iBAAiB,CAAClI,WAAW,CAACiD,aAAa,EAAEE,IAAI,CAACgF,SAAS,CAACjF,eAAe,CAAC,CAAC;IACjG,IAAI,CAAC0E,aAAa,EAAE;EACtB;EAEAQ,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACrH,SAAS,GAAGqH,OAAO;IACxB,IAAI,CAACT,aAAa,EAAE;EACtB;EAqBAU,eAAeA,CAACrC,QAAc;IAC5B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAACrF,cAAc,CAAC2H,cAAc,CAACtC,QAAQ,CAAC;IAC9C;EACF;EAEA2B,aAAaA,CAAA;IACX,OAAO,IAAI,CAAClH,cAAc,CAAC8H,+BAA+B,CAAC;MACzDlD,IAAI,EAAE;QACJmD,SAAS,EAAE,IAAI,CAACzH,SAAS;QACzB0H,QAAQ,EAAE,IAAI,CAAC3H,QAAQ;QACvBgD,WAAW,EAAE,IAAI,CAACV,WAAW,CAACU,WAAW;QACzC4E,YAAY,EAAE,IAAI,CAACtF,WAAW,CAACC,iBAAiB,CAACnC,KAAK;QACtD6E,OAAO,EAAE,IAAI,CAAC3C,WAAW,CAACQ,aAAa,CAAC1C,KAAK;QAC7CuC,WAAW,EAAE,IAAI,CAACL,WAAW,CAACE,kBAAkB,CAACpC,KAAK;QACtDyC,cAAc,EAAE,IAAI,CAACP,WAAW,CAACM,qBAAqB,CAACxC;;KAE1D,CAAC,CAACyH,IAAI,CACLlJ,GAAG,CAAC+F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACkD,UAAU,GAAGpD,GAAG,CAACC,OAAO;QAC7B,IAAI,CAACzE,YAAY,GAAGwE,GAAG,CAACqD,UAAW;MACrC;IACF,CAAC,CAAC,CACH,CAACtD,SAAS,EAAE;EACf;EAGAuD,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAAC1F,WAAW,CAACC,iBAAiB,CAACnC,KAAK,EAAE;MAC5C,IAAI,CAACyG,aAAa,EAAE;MACpB;MACA,IAAI,CAACX,uBAAuB,EAAE;IAChC;EACF;EAEAjD,gBAAgBA,CAAA;IACd,IAAI,CAACvD,iBAAiB,CAACuI,qCAAqC,CAAC;MAAE1D,IAAI,EAAE;IAAE,CAAE,CAAC,CAACsD,IAAI,CAC7ElJ,GAAG,CAAC+F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACsD,oBAAoB,GAAGxD,GAAG,CAACC,OAAO,CAACsB,GAAG,CAACvB,GAAG,IAAG;UAChD,OAAO;YACLrE,KAAK,EAAEqE,GAAG,CAACyD,cAAc;YACzB/H,KAAK,EAAEsE,GAAG,CAAC0D;WACZ;QACH,CAAC,CAAC;QACF,IAAIpJ,mBAAmB,CAACiD,iBAAiB,CAAChD,WAAW,CAACiD,aAAa,CAAC,IAAI,IAAI,IACvElD,mBAAmB,CAACiD,iBAAiB,CAAChD,WAAW,CAACiD,aAAa,CAAC,IAAIlB,SAAS,IAC7EhC,mBAAmB,CAACiD,iBAAiB,CAAChD,WAAW,CAACiD,aAAa,CAAC,IAAI,EAAE,EAAE;UAC3E,IAAIC,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACrD,mBAAmB,CAACiD,iBAAiB,CAAChD,WAAW,CAACiD,aAAa,CAAC,CAAC;UAClG,IAAIC,eAAe,CAAC+E,kBAAkB,IAAI,IAAI,IAAI/E,eAAe,CAAC+E,kBAAkB,IAAIlG,SAAS,EAAE;YACjG,IAAIqH,KAAK,GAAG,IAAI,CAACH,oBAAoB,CAACI,SAAS,CAAE5F,CAAM,IAAKA,CAAC,CAACtC,KAAK,IAAI+B,eAAe,CAAC+E,kBAAkB,CAAC9G,KAAK,CAAC;YAChH,IAAI,CAACkC,WAAW,CAACC,iBAAiB,GAAG,IAAI,CAAC2F,oBAAoB,CAACG,KAAK,CAAC;UACvE,CAAC,MAAM;YACL,IAAI,CAAC/F,WAAW,CAACC,iBAAiB,GAAG,IAAI,CAAC2F,oBAAoB,CAAC,CAAC,CAAC;UACnE;QACF,CAAC,MACI;UACH,IAAI,CAAC5F,WAAW,CAACC,iBAAiB,GAAG,IAAI,CAAC2F,oBAAoB,CAAC,CAAC,CAAC;QACnE;QACA,IAAI,IAAI,CAAC5F,WAAW,CAACC,iBAAiB,CAACnC,KAAK,EAAE;UAC5C,IAAI,CAACyG,aAAa,EAAE;UACpB;UACA,IAAI,CAACX,uBAAuB,EAAE;QAChC;MACF;IACF,CAAC,CAAC,CACH,CAACzB,SAAS,EAAE;EACf;EAEA8D,SAASA,CAAClE,GAAQ,EAAEF,IAAU;IAC5B,IAAI,CAACrC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACoB,UAAU,EAAE;IAEjB;IACA,IAAI,CAACtC,kBAAkB,GAAG,EAAE;IAE5B,IAAI,CAACkD,cAAc,GAAG;MACpBvB,kBAAkB,EAAE,IAAI,CAACrC,iBAAiB,CAAC,CAAC,CAAC;MAC7C2C,aAAa,EAAE,IAAI,CAACrC,aAAa,CAAC,CAAC,CAAC;MACpCmC,qBAAqB,EAAE,IAAI,CAACrC,oBAAoB,CAAC,CAAC,CAAC;MACnDyC,WAAW,EAAE,EAAE;MACfgC,KAAK,EAAE,CAAC;MACRE,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBqD,YAAY,EAAE;KACf;IAED,IAAIrE,IAAI,EAAE;MACR,IAAI,CAACpC,KAAK,GAAG,KAAK;MAClB,IAAI,CAACqC,aAAa,CAACD,IAAI,EAAEE,GAAG,CAAC;IAC/B,CAAC,MAAM;MACL,IAAI,CAACtC,KAAK,GAAG,IAAI;MACjB;MACA,IAAI,CAACmE,uBAAuB,CAAClF,SAAS,EAAEqD,GAAG,CAAC;IAC9C;EACF;EAEAoE,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO7J,MAAM,CAAC6J,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAC,QAAQA,CAACvE,GAAQ,GAEjB;EAEAwE,OAAOA,CAACxE,GAAQ;IACdA,GAAG,CAACyC,KAAK,EAAE;EACb;EAGAV,UAAUA,CAAA;IACR,IAAI,CAAC5G,KAAK,CAACsJ,KAAK,EAAE;IAClB,IAAI,IAAI,CAAC/G,KAAK,IAAI,CAAC,IAAI,CAAChB,QAAQ,EAAE;MAChC,IAAI,CAACvB,KAAK,CAACuJ,eAAe,CAAC,MAAM,CAAC;IACpC;IAEA;IACA,IAAI,CAAC,IAAI,CAAClI,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAAC4E,MAAM,KAAK,CAAC,EAAE;MACpE,IAAI,CAACjG,KAAK,CAACuJ,eAAe,CAAC,iBAAiB,CAAC;IAC/C;IAEA,IAAI,CAACvJ,KAAK,CAAC8B,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACyC,cAAc,CAACoB,YAAY,CAAC;EACjE;EAEA6D,aAAaA,CAACC,QAA4B;IACxC,IAAIC,KAAK,GAAG,EAAE;IACd,IAAID,QAAQ,IAAIjI,SAAS,EAAE;MACzB,QAAQiI,QAAQ;QACd,KAAK,CAAC;UACJC,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF;UACE;MACJ;IACF;IACA,OAAOA,KAAK;EACd;EAIA;EACQhD,uBAAuBA,CAACH,oBAA+B,EAAEoD,SAAe;IAC9E,IAAI,CAAC,IAAI,CAAC7G,WAAW,CAACC,iBAAiB,EAAEnC,KAAK,EAAE;MAC9CgJ,OAAO,CAACC,IAAI,CAAC,kBAAkB,CAAC;MAChC;IACF;IAEAD,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAChH,WAAW,CAACC,iBAAiB,CAACnC,KAAK,CAAC;IAExE,IAAI,CAACN,mBAAmB,CAACyJ,WAAW,CAAC,IAAI,CAACjH,WAAW,CAACC,iBAAiB,CAACnC,KAAK,CAAC,CAACqE,SAAS,CAAC;MACvF+E,IAAI,EAAGC,QAAQ,IAAI;QACjBL,OAAO,CAACE,GAAG,CAAC,SAAS,EAAEG,QAAQ,CAAC;QAChC,IAAIA,QAAQ,IAAIA,QAAQ,CAAC9E,OAAO,EAAE;UAChC,IAAI,CAAC/D,YAAY,GAAG,IAAI,CAAC8I,gCAAgC,CAACD,QAAQ,CAAC9E,OAAO,CAAC;UAC3EyE,OAAO,CAACE,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC1I,YAAY,CAAC;UAE5C;UACA,IAAImF,oBAAoB,IAAIA,oBAAoB,CAACN,MAAM,GAAG,CAAC,EAAE;YAC3D2D,OAAO,CAACE,GAAG,CAAC,YAAY,EAAEvD,oBAAoB,CAAC;YAC/C,IAAI,CAAClF,kBAAkB,GAAG,CAAC,GAAGkF,oBAAoB,CAAC;YACnD;YACA4D,UAAU,CAAC,MAAK;cACd,IAAI,CAAC9I,kBAAkB,GAAG,CAAC,GAAGkF,oBAAoB,CAAC;YACrD,CAAC,EAAE,EAAE,CAAC;UACR;UAEA;UACA,IAAIoD,SAAS,EAAE;YACbQ,UAAU,CAAC,MAAK;cACd,IAAI,CAACrK,aAAa,CAACsK,IAAI,CAACT,SAAS,CAAC;YACpC,CAAC,EAAE,GAAG,CAAC;UACT;QACF,CAAC,MAAM;UACLC,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;UACzB,IAAI,CAACzI,YAAY,GAAG,EAAE;UACtB,IAAIuI,SAAS,EAAE;YACb,IAAI,CAAC7J,aAAa,CAACsK,IAAI,CAACT,SAAS,CAAC;UACpC;QACF;MACF,CAAC;MACDU,KAAK,EAAGA,KAAK,IAAI;QACfT,OAAO,CAACS,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACpC,IAAI,CAACjJ,YAAY,GAAG,EAAE;QACtB,IAAIuI,SAAS,EAAE;UACb,IAAI,CAAC7J,aAAa,CAACsK,IAAI,CAACT,SAAS,CAAC;QACpC;MACF;KACD,CAAC;EACJ;EAEA;EACQO,gCAAgCA,CAACI,OAAY;IACnD,MAAMlJ,YAAY,GAAiB,EAAE;IAErCmJ,MAAM,CAACD,OAAO,CAACA,OAAO,CAAC,CAACE,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAgB,KAAI;MACpE,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAIA,MAAM,CAACzE,MAAM,GAAG,CAAC,EAAE;QAC9C7E,YAAY,CAACqJ,QAAQ,CAAC,GAAGC,MAAM,CAACjE,GAAG,CAAEoE,KAAU,KAAM;UACnDC,SAAS,EAAED,KAAK,CAACE,SAAS,IAAI,EAAE;UAChCN,QAAQ,EAAEA,QAAQ;UAClBO,KAAK,EAAEH,KAAK,CAACI,KAAK,GAAG,GAAGJ,KAAK,CAACI,KAAK,GAAG,GAAG,EAAE;UAC3CC,OAAO,EAAEL,KAAK,CAACM,OAAO;UACtBC,SAAS,EAAEP,KAAK,CAACQ,SAAS;UAC1BC,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE;SACb,CAAC,CAAC;MACL;IACF,CAAC,CAAC;IAEF3B,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAE1I,YAAY,CAAC;IAC/C,OAAOA,YAAY;EACrB;EAIA;EACAoK,0BAA0BA,CAACC,aAA8B;IACvD;IACA,IAAI,CAACpK,kBAAkB,GAAGoK,aAAa,CAAChF,GAAG,CAAC9B,IAAI,IAAIA,IAAI,CAACuG,OAAO,CAAC,CAACpF,MAAM,CAAC4F,EAAE,IAAIA,EAAE,KAAKlK,SAAS,CAAa;EAC9G;EAEA;EACAmK,mBAAmBA,CAACC,WAAqB;IACvC,IAAI,CAACvK,kBAAkB,GAAGuK,WAAW;EACvC;EAMA;EACA5E,sCAAsCA,CAAA;IACpC,MAAMjD,MAAM,GAAkB,EAAE;IAEhC;IACA,IAAI,IAAI,CAAC3C,YAAY,IAAI,IAAI,CAACC,kBAAkB,CAAC4E,MAAM,GAAG,CAAC,EAAE;MAC3DsE,MAAM,CAACsB,MAAM,CAAC,IAAI,CAACzK,YAAY,CAAC,CAACoJ,OAAO,CAACE,MAAM,IAAG;QAChDA,MAAM,CAACF,OAAO,CAACK,KAAK,IAAG;UACrB,IAAIA,KAAK,CAACK,OAAO,IAAI,IAAI,CAAC7J,kBAAkB,CAACyK,QAAQ,CAACjB,KAAK,CAACK,OAAO,CAAC,EAAE;YACpEnH,MAAM,CAACgI,IAAI,CAAC;cACVvF,QAAQ,EAAEqE,KAAK,CAACK,OAAO;cACvBlF,SAAS,EAAE,IAAI;cACfgG,MAAM,EAAEnB,KAAK,CAACG,KAAK,GAAGiB,QAAQ,CAACpB,KAAK,CAACG,KAAK,CAACkB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG1K,SAAS;cACxE2K,UAAU,EAAEtB,KAAK,CAACC;aACnB,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,OAAO/G,MAAM;EACf;CAED;AAtgByBqI,UAAA,EAAvBxN,SAAS,CAAC,WAAW,CAAC,C,mEAAwB;AA3IpCe,iCAAiC,GAAAyM,UAAA,EAR7CzN,SAAS,CAAC;EACT0N,QAAQ,EAAE,gCAAgC;EAC1CC,WAAW,EAAE,6CAA6C;EAC1DC,SAAS,EAAE,CAAC,6CAA6C,CAAC;EAC1DC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC1N,YAAY,EAAEF,YAAY,EAAEC,eAAe,EAAEE,kBAAkB,EAAEI,mBAAmB,EAAEG,cAAc,EAAEN,kBAAkB,EAAEK,kBAAkB,EAAEI,mBAAmB;CAC5K,CAAC,C,EAEWC,iCAAiC,CAipB7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}