{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isFunction, extend, createHashMap } from 'zrender/lib/core/util.js';\nimport makeStyleMapper from '../model/mixin/makeStyleMapper.js';\nimport { ITEM_STYLE_KEY_MAP } from '../model/mixin/itemStyle.js';\nimport { LINE_STYLE_KEY_MAP } from '../model/mixin/lineStyle.js';\nimport Model from '../model/Model.js';\nimport { makeInner } from '../util/model.js';\nvar inner = makeInner();\nvar defaultStyleMappers = {\n  itemStyle: makeStyleMapper(ITEM_STYLE_KEY_MAP, true),\n  lineStyle: makeStyleMapper(LINE_STYLE_KEY_MAP, true)\n};\nvar defaultColorKey = {\n  lineStyle: 'stroke',\n  itemStyle: 'fill'\n};\nfunction getStyleMapper(seriesModel, stylePath) {\n  var styleMapper = seriesModel.visualStyleMapper || defaultStyleMappers[stylePath];\n  if (!styleMapper) {\n    console.warn(\"Unknown style type '\" + stylePath + \"'.\");\n    return defaultStyleMappers.itemStyle;\n  }\n  return styleMapper;\n}\nfunction getDefaultColorKey(seriesModel, stylePath) {\n  // return defaultColorKey[stylePath] ||\n  var colorKey = seriesModel.visualDrawType || defaultColorKey[stylePath];\n  if (!colorKey) {\n    console.warn(\"Unknown style type '\" + stylePath + \"'.\");\n    return 'fill';\n  }\n  return colorKey;\n}\nvar seriesStyleTask = {\n  createOnAllSeries: true,\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    var stylePath = seriesModel.visualStyleAccessPath || 'itemStyle';\n    // Set in itemStyle\n    var styleModel = seriesModel.getModel(stylePath);\n    var getStyle = getStyleMapper(seriesModel, stylePath);\n    var globalStyle = getStyle(styleModel);\n    var decalOption = styleModel.getShallow('decal');\n    if (decalOption) {\n      data.setVisual('decal', decalOption);\n      decalOption.dirty = true;\n    }\n    // TODO\n    var colorKey = getDefaultColorKey(seriesModel, stylePath);\n    var color = globalStyle[colorKey];\n    // TODO style callback\n    var colorCallback = isFunction(color) ? color : null;\n    var hasAutoColor = globalStyle.fill === 'auto' || globalStyle.stroke === 'auto';\n    // Get from color palette by default.\n    if (!globalStyle[colorKey] || colorCallback || hasAutoColor) {\n      // Note: If some series has color specified (e.g., by itemStyle.color), we DO NOT\n      // make it effect palette. Because some scenarios users need to make some series\n      // transparent or as background, which should better not effect the palette.\n      var colorPalette = seriesModel.getColorFromPalette(\n      // TODO series count changed.\n      seriesModel.name, null, ecModel.getSeriesCount());\n      if (!globalStyle[colorKey]) {\n        globalStyle[colorKey] = colorPalette;\n        data.setVisual('colorFromPalette', true);\n      }\n      globalStyle.fill = globalStyle.fill === 'auto' || isFunction(globalStyle.fill) ? colorPalette : globalStyle.fill;\n      globalStyle.stroke = globalStyle.stroke === 'auto' || isFunction(globalStyle.stroke) ? colorPalette : globalStyle.stroke;\n    }\n    data.setVisual('style', globalStyle);\n    data.setVisual('drawType', colorKey);\n    // Only visible series has each data be visual encoded\n    if (!ecModel.isSeriesFiltered(seriesModel) && colorCallback) {\n      data.setVisual('colorFromPalette', false);\n      return {\n        dataEach: function (data, idx) {\n          var dataParams = seriesModel.getDataParams(idx);\n          var itemStyle = extend({}, globalStyle);\n          itemStyle[colorKey] = colorCallback(dataParams);\n          data.setItemVisual(idx, 'style', itemStyle);\n        }\n      };\n    }\n  }\n};\nvar sharedModel = new Model();\nvar dataStyleTask = {\n  createOnAllSeries: true,\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    if (seriesModel.ignoreStyleOnData || ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var data = seriesModel.getData();\n    var stylePath = seriesModel.visualStyleAccessPath || 'itemStyle';\n    // Set in itemStyle\n    var getStyle = getStyleMapper(seriesModel, stylePath);\n    var colorKey = data.getVisual('drawType');\n    return {\n      dataEach: data.hasItemOption ? function (data, idx) {\n        // Not use getItemModel for performance considuration\n        var rawItem = data.getRawDataItem(idx);\n        if (rawItem && rawItem[stylePath]) {\n          sharedModel.option = rawItem[stylePath];\n          var style = getStyle(sharedModel);\n          var existsStyle = data.ensureUniqueItemVisual(idx, 'style');\n          extend(existsStyle, style);\n          if (sharedModel.option.decal) {\n            data.setItemVisual(idx, 'decal', sharedModel.option.decal);\n            sharedModel.option.decal.dirty = true;\n          }\n          if (colorKey in style) {\n            data.setItemVisual(idx, 'colorFromPalette', false);\n          }\n        }\n      } : null\n    };\n  }\n};\n// Pick color from palette for the data which has not been set with color yet.\n// Note: do not support stream rendering. No such cases yet.\nvar dataColorPaletteTask = {\n  performRawSeries: true,\n  overallReset: function (ecModel) {\n    // Each type of series uses one scope.\n    // Pie and funnel are using different scopes.\n    var paletteScopeGroupByType = createHashMap();\n    ecModel.eachSeries(function (seriesModel) {\n      var colorBy = seriesModel.getColorBy();\n      if (seriesModel.isColorBySeries()) {\n        return;\n      }\n      var key = seriesModel.type + '-' + colorBy;\n      var colorScope = paletteScopeGroupByType.get(key);\n      if (!colorScope) {\n        colorScope = {};\n        paletteScopeGroupByType.set(key, colorScope);\n      }\n      inner(seriesModel).scope = colorScope;\n    });\n    ecModel.eachSeries(function (seriesModel) {\n      if (seriesModel.isColorBySeries() || ecModel.isSeriesFiltered(seriesModel)) {\n        return;\n      }\n      var dataAll = seriesModel.getRawData();\n      var idxMap = {};\n      var data = seriesModel.getData();\n      var colorScope = inner(seriesModel).scope;\n      var stylePath = seriesModel.visualStyleAccessPath || 'itemStyle';\n      var colorKey = getDefaultColorKey(seriesModel, stylePath);\n      data.each(function (idx) {\n        var rawIdx = data.getRawIndex(idx);\n        idxMap[rawIdx] = idx;\n      });\n      // Iterate on data before filtered. To make sure color from palette can be\n      // Consistent when toggling legend.\n      dataAll.each(function (rawIdx) {\n        var idx = idxMap[rawIdx];\n        var fromPalette = data.getItemVisual(idx, 'colorFromPalette');\n        // Get color from palette for each data only when the color is inherited from series color, which is\n        // also picked from color palette. So following situation is not in the case:\n        // 1. series.itemStyle.color is set\n        // 2. color is encoded by visualMap\n        if (fromPalette) {\n          var itemStyle = data.ensureUniqueItemVisual(idx, 'style');\n          var name_1 = dataAll.getName(rawIdx) || rawIdx + '';\n          var dataCount = dataAll.count();\n          itemStyle[colorKey] = seriesModel.getColorFromPalette(name_1, colorScope, dataCount);\n        }\n      });\n    });\n  }\n};\nexport { seriesStyleTask, dataStyleTask, dataColorPaletteTask };", "map": {"version": 3, "names": ["isFunction", "extend", "createHashMap", "makeStyleMapper", "ITEM_STYLE_KEY_MAP", "LINE_STYLE_KEY_MAP", "Model", "makeInner", "inner", "defaultStyleMappers", "itemStyle", "lineStyle", "defaultColorKey", "getStyleMapper", "seriesModel", "stylePath", "styleMapper", "visualStyleMapper", "console", "warn", "getDefaultColorKey", "colorKey", "visualDrawType", "seriesStyleTask", "createOnAllSeries", "performRawSeries", "reset", "ecModel", "data", "getData", "visualStyleAccessPath", "styleModel", "getModel", "getStyle", "globalStyle", "decalOption", "getShallow", "setVisual", "dirty", "color", "colorCallback", "hasAutoColor", "fill", "stroke", "colorPalette", "getColorFromPalette", "name", "getSeriesCount", "isSeriesFiltered", "dataEach", "idx", "dataParams", "getDataParams", "setItemVisual", "sharedModel", "dataStyleTask", "ignoreStyleOnData", "getVisual", "hasItemOption", "rawItem", "getRawDataItem", "option", "style", "existsStyle", "ensureUniqueItemVisual", "decal", "dataColorPaletteTask", "<PERSON><PERSON><PERSON><PERSON>", "paletteScopeGroupByType", "eachSeries", "colorBy", "getColorBy", "isColorBySeries", "key", "type", "colorScope", "get", "set", "scope", "dataAll", "getRawData", "idxMap", "each", "rawIdx", "getRawIndex", "fromPalette", "getItemVisual", "name_1", "getName", "dataCount", "count"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/visual/style.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isFunction, extend, createHashMap } from 'zrender/lib/core/util.js';\nimport makeStyleMapper from '../model/mixin/makeStyleMapper.js';\nimport { ITEM_STYLE_KEY_MAP } from '../model/mixin/itemStyle.js';\nimport { LINE_STYLE_KEY_MAP } from '../model/mixin/lineStyle.js';\nimport Model from '../model/Model.js';\nimport { makeInner } from '../util/model.js';\nvar inner = makeInner();\nvar defaultStyleMappers = {\n  itemStyle: makeStyleMapper(ITEM_STYLE_KEY_MAP, true),\n  lineStyle: makeStyleMapper(LINE_STYLE_KEY_MAP, true)\n};\nvar defaultColorKey = {\n  lineStyle: 'stroke',\n  itemStyle: 'fill'\n};\nfunction getStyleMapper(seriesModel, stylePath) {\n  var styleMapper = seriesModel.visualStyleMapper || defaultStyleMappers[stylePath];\n  if (!styleMapper) {\n    console.warn(\"Unknown style type '\" + stylePath + \"'.\");\n    return defaultStyleMappers.itemStyle;\n  }\n  return styleMapper;\n}\nfunction getDefaultColorKey(seriesModel, stylePath) {\n  // return defaultColorKey[stylePath] ||\n  var colorKey = seriesModel.visualDrawType || defaultColorKey[stylePath];\n  if (!colorKey) {\n    console.warn(\"Unknown style type '\" + stylePath + \"'.\");\n    return 'fill';\n  }\n  return colorKey;\n}\nvar seriesStyleTask = {\n  createOnAllSeries: true,\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    var stylePath = seriesModel.visualStyleAccessPath || 'itemStyle';\n    // Set in itemStyle\n    var styleModel = seriesModel.getModel(stylePath);\n    var getStyle = getStyleMapper(seriesModel, stylePath);\n    var globalStyle = getStyle(styleModel);\n    var decalOption = styleModel.getShallow('decal');\n    if (decalOption) {\n      data.setVisual('decal', decalOption);\n      decalOption.dirty = true;\n    }\n    // TODO\n    var colorKey = getDefaultColorKey(seriesModel, stylePath);\n    var color = globalStyle[colorKey];\n    // TODO style callback\n    var colorCallback = isFunction(color) ? color : null;\n    var hasAutoColor = globalStyle.fill === 'auto' || globalStyle.stroke === 'auto';\n    // Get from color palette by default.\n    if (!globalStyle[colorKey] || colorCallback || hasAutoColor) {\n      // Note: If some series has color specified (e.g., by itemStyle.color), we DO NOT\n      // make it effect palette. Because some scenarios users need to make some series\n      // transparent or as background, which should better not effect the palette.\n      var colorPalette = seriesModel.getColorFromPalette(\n      // TODO series count changed.\n      seriesModel.name, null, ecModel.getSeriesCount());\n      if (!globalStyle[colorKey]) {\n        globalStyle[colorKey] = colorPalette;\n        data.setVisual('colorFromPalette', true);\n      }\n      globalStyle.fill = globalStyle.fill === 'auto' || isFunction(globalStyle.fill) ? colorPalette : globalStyle.fill;\n      globalStyle.stroke = globalStyle.stroke === 'auto' || isFunction(globalStyle.stroke) ? colorPalette : globalStyle.stroke;\n    }\n    data.setVisual('style', globalStyle);\n    data.setVisual('drawType', colorKey);\n    // Only visible series has each data be visual encoded\n    if (!ecModel.isSeriesFiltered(seriesModel) && colorCallback) {\n      data.setVisual('colorFromPalette', false);\n      return {\n        dataEach: function (data, idx) {\n          var dataParams = seriesModel.getDataParams(idx);\n          var itemStyle = extend({}, globalStyle);\n          itemStyle[colorKey] = colorCallback(dataParams);\n          data.setItemVisual(idx, 'style', itemStyle);\n        }\n      };\n    }\n  }\n};\nvar sharedModel = new Model();\nvar dataStyleTask = {\n  createOnAllSeries: true,\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    if (seriesModel.ignoreStyleOnData || ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var data = seriesModel.getData();\n    var stylePath = seriesModel.visualStyleAccessPath || 'itemStyle';\n    // Set in itemStyle\n    var getStyle = getStyleMapper(seriesModel, stylePath);\n    var colorKey = data.getVisual('drawType');\n    return {\n      dataEach: data.hasItemOption ? function (data, idx) {\n        // Not use getItemModel for performance considuration\n        var rawItem = data.getRawDataItem(idx);\n        if (rawItem && rawItem[stylePath]) {\n          sharedModel.option = rawItem[stylePath];\n          var style = getStyle(sharedModel);\n          var existsStyle = data.ensureUniqueItemVisual(idx, 'style');\n          extend(existsStyle, style);\n          if (sharedModel.option.decal) {\n            data.setItemVisual(idx, 'decal', sharedModel.option.decal);\n            sharedModel.option.decal.dirty = true;\n          }\n          if (colorKey in style) {\n            data.setItemVisual(idx, 'colorFromPalette', false);\n          }\n        }\n      } : null\n    };\n  }\n};\n// Pick color from palette for the data which has not been set with color yet.\n// Note: do not support stream rendering. No such cases yet.\nvar dataColorPaletteTask = {\n  performRawSeries: true,\n  overallReset: function (ecModel) {\n    // Each type of series uses one scope.\n    // Pie and funnel are using different scopes.\n    var paletteScopeGroupByType = createHashMap();\n    ecModel.eachSeries(function (seriesModel) {\n      var colorBy = seriesModel.getColorBy();\n      if (seriesModel.isColorBySeries()) {\n        return;\n      }\n      var key = seriesModel.type + '-' + colorBy;\n      var colorScope = paletteScopeGroupByType.get(key);\n      if (!colorScope) {\n        colorScope = {};\n        paletteScopeGroupByType.set(key, colorScope);\n      }\n      inner(seriesModel).scope = colorScope;\n    });\n    ecModel.eachSeries(function (seriesModel) {\n      if (seriesModel.isColorBySeries() || ecModel.isSeriesFiltered(seriesModel)) {\n        return;\n      }\n      var dataAll = seriesModel.getRawData();\n      var idxMap = {};\n      var data = seriesModel.getData();\n      var colorScope = inner(seriesModel).scope;\n      var stylePath = seriesModel.visualStyleAccessPath || 'itemStyle';\n      var colorKey = getDefaultColorKey(seriesModel, stylePath);\n      data.each(function (idx) {\n        var rawIdx = data.getRawIndex(idx);\n        idxMap[rawIdx] = idx;\n      });\n      // Iterate on data before filtered. To make sure color from palette can be\n      // Consistent when toggling legend.\n      dataAll.each(function (rawIdx) {\n        var idx = idxMap[rawIdx];\n        var fromPalette = data.getItemVisual(idx, 'colorFromPalette');\n        // Get color from palette for each data only when the color is inherited from series color, which is\n        // also picked from color palette. So following situation is not in the case:\n        // 1. series.itemStyle.color is set\n        // 2. color is encoded by visualMap\n        if (fromPalette) {\n          var itemStyle = data.ensureUniqueItemVisual(idx, 'style');\n          var name_1 = dataAll.getName(rawIdx) || rawIdx + '';\n          var dataCount = dataAll.count();\n          itemStyle[colorKey] = seriesModel.getColorFromPalette(name_1, colorScope, dataCount);\n        }\n      });\n    });\n  }\n};\nexport { seriesStyleTask, dataStyleTask, dataColorPaletteTask };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,EAAEC,MAAM,EAAEC,aAAa,QAAQ,0BAA0B;AAC5E,OAAOC,eAAe,MAAM,mCAAmC;AAC/D,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,OAAOC,KAAK,MAAM,mBAAmB;AACrC,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,IAAIC,KAAK,GAAGD,SAAS,CAAC,CAAC;AACvB,IAAIE,mBAAmB,GAAG;EACxBC,SAAS,EAAEP,eAAe,CAACC,kBAAkB,EAAE,IAAI,CAAC;EACpDO,SAAS,EAAER,eAAe,CAACE,kBAAkB,EAAE,IAAI;AACrD,CAAC;AACD,IAAIO,eAAe,GAAG;EACpBD,SAAS,EAAE,QAAQ;EACnBD,SAAS,EAAE;AACb,CAAC;AACD,SAASG,cAAcA,CAACC,WAAW,EAAEC,SAAS,EAAE;EAC9C,IAAIC,WAAW,GAAGF,WAAW,CAACG,iBAAiB,IAAIR,mBAAmB,CAACM,SAAS,CAAC;EACjF,IAAI,CAACC,WAAW,EAAE;IAChBE,OAAO,CAACC,IAAI,CAAC,sBAAsB,GAAGJ,SAAS,GAAG,IAAI,CAAC;IACvD,OAAON,mBAAmB,CAACC,SAAS;EACtC;EACA,OAAOM,WAAW;AACpB;AACA,SAASI,kBAAkBA,CAACN,WAAW,EAAEC,SAAS,EAAE;EAClD;EACA,IAAIM,QAAQ,GAAGP,WAAW,CAACQ,cAAc,IAAIV,eAAe,CAACG,SAAS,CAAC;EACvE,IAAI,CAACM,QAAQ,EAAE;IACbH,OAAO,CAACC,IAAI,CAAC,sBAAsB,GAAGJ,SAAS,GAAG,IAAI,CAAC;IACvD,OAAO,MAAM;EACf;EACA,OAAOM,QAAQ;AACjB;AACA,IAAIE,eAAe,GAAG;EACpBC,iBAAiB,EAAE,IAAI;EACvBC,gBAAgB,EAAE,IAAI;EACtBC,KAAK,EAAE,SAAAA,CAAUZ,WAAW,EAAEa,OAAO,EAAE;IACrC,IAAIC,IAAI,GAAGd,WAAW,CAACe,OAAO,CAAC,CAAC;IAChC,IAAId,SAAS,GAAGD,WAAW,CAACgB,qBAAqB,IAAI,WAAW;IAChE;IACA,IAAIC,UAAU,GAAGjB,WAAW,CAACkB,QAAQ,CAACjB,SAAS,CAAC;IAChD,IAAIkB,QAAQ,GAAGpB,cAAc,CAACC,WAAW,EAAEC,SAAS,CAAC;IACrD,IAAImB,WAAW,GAAGD,QAAQ,CAACF,UAAU,CAAC;IACtC,IAAII,WAAW,GAAGJ,UAAU,CAACK,UAAU,CAAC,OAAO,CAAC;IAChD,IAAID,WAAW,EAAE;MACfP,IAAI,CAACS,SAAS,CAAC,OAAO,EAAEF,WAAW,CAAC;MACpCA,WAAW,CAACG,KAAK,GAAG,IAAI;IAC1B;IACA;IACA,IAAIjB,QAAQ,GAAGD,kBAAkB,CAACN,WAAW,EAAEC,SAAS,CAAC;IACzD,IAAIwB,KAAK,GAAGL,WAAW,CAACb,QAAQ,CAAC;IACjC;IACA,IAAImB,aAAa,GAAGxC,UAAU,CAACuC,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAI;IACpD,IAAIE,YAAY,GAAGP,WAAW,CAACQ,IAAI,KAAK,MAAM,IAAIR,WAAW,CAACS,MAAM,KAAK,MAAM;IAC/E;IACA,IAAI,CAACT,WAAW,CAACb,QAAQ,CAAC,IAAImB,aAAa,IAAIC,YAAY,EAAE;MAC3D;MACA;MACA;MACA,IAAIG,YAAY,GAAG9B,WAAW,CAAC+B,mBAAmB;MAClD;MACA/B,WAAW,CAACgC,IAAI,EAAE,IAAI,EAAEnB,OAAO,CAACoB,cAAc,CAAC,CAAC,CAAC;MACjD,IAAI,CAACb,WAAW,CAACb,QAAQ,CAAC,EAAE;QAC1Ba,WAAW,CAACb,QAAQ,CAAC,GAAGuB,YAAY;QACpChB,IAAI,CAACS,SAAS,CAAC,kBAAkB,EAAE,IAAI,CAAC;MAC1C;MACAH,WAAW,CAACQ,IAAI,GAAGR,WAAW,CAACQ,IAAI,KAAK,MAAM,IAAI1C,UAAU,CAACkC,WAAW,CAACQ,IAAI,CAAC,GAAGE,YAAY,GAAGV,WAAW,CAACQ,IAAI;MAChHR,WAAW,CAACS,MAAM,GAAGT,WAAW,CAACS,MAAM,KAAK,MAAM,IAAI3C,UAAU,CAACkC,WAAW,CAACS,MAAM,CAAC,GAAGC,YAAY,GAAGV,WAAW,CAACS,MAAM;IAC1H;IACAf,IAAI,CAACS,SAAS,CAAC,OAAO,EAAEH,WAAW,CAAC;IACpCN,IAAI,CAACS,SAAS,CAAC,UAAU,EAAEhB,QAAQ,CAAC;IACpC;IACA,IAAI,CAACM,OAAO,CAACqB,gBAAgB,CAAClC,WAAW,CAAC,IAAI0B,aAAa,EAAE;MAC3DZ,IAAI,CAACS,SAAS,CAAC,kBAAkB,EAAE,KAAK,CAAC;MACzC,OAAO;QACLY,QAAQ,EAAE,SAAAA,CAAUrB,IAAI,EAAEsB,GAAG,EAAE;UAC7B,IAAIC,UAAU,GAAGrC,WAAW,CAACsC,aAAa,CAACF,GAAG,CAAC;UAC/C,IAAIxC,SAAS,GAAGT,MAAM,CAAC,CAAC,CAAC,EAAEiC,WAAW,CAAC;UACvCxB,SAAS,CAACW,QAAQ,CAAC,GAAGmB,aAAa,CAACW,UAAU,CAAC;UAC/CvB,IAAI,CAACyB,aAAa,CAACH,GAAG,EAAE,OAAO,EAAExC,SAAS,CAAC;QAC7C;MACF,CAAC;IACH;EACF;AACF,CAAC;AACD,IAAI4C,WAAW,GAAG,IAAIhD,KAAK,CAAC,CAAC;AAC7B,IAAIiD,aAAa,GAAG;EAClB/B,iBAAiB,EAAE,IAAI;EACvBC,gBAAgB,EAAE,IAAI;EACtBC,KAAK,EAAE,SAAAA,CAAUZ,WAAW,EAAEa,OAAO,EAAE;IACrC,IAAIb,WAAW,CAAC0C,iBAAiB,IAAI7B,OAAO,CAACqB,gBAAgB,CAAClC,WAAW,CAAC,EAAE;MAC1E;IACF;IACA,IAAIc,IAAI,GAAGd,WAAW,CAACe,OAAO,CAAC,CAAC;IAChC,IAAId,SAAS,GAAGD,WAAW,CAACgB,qBAAqB,IAAI,WAAW;IAChE;IACA,IAAIG,QAAQ,GAAGpB,cAAc,CAACC,WAAW,EAAEC,SAAS,CAAC;IACrD,IAAIM,QAAQ,GAAGO,IAAI,CAAC6B,SAAS,CAAC,UAAU,CAAC;IACzC,OAAO;MACLR,QAAQ,EAAErB,IAAI,CAAC8B,aAAa,GAAG,UAAU9B,IAAI,EAAEsB,GAAG,EAAE;QAClD;QACA,IAAIS,OAAO,GAAG/B,IAAI,CAACgC,cAAc,CAACV,GAAG,CAAC;QACtC,IAAIS,OAAO,IAAIA,OAAO,CAAC5C,SAAS,CAAC,EAAE;UACjCuC,WAAW,CAACO,MAAM,GAAGF,OAAO,CAAC5C,SAAS,CAAC;UACvC,IAAI+C,KAAK,GAAG7B,QAAQ,CAACqB,WAAW,CAAC;UACjC,IAAIS,WAAW,GAAGnC,IAAI,CAACoC,sBAAsB,CAACd,GAAG,EAAE,OAAO,CAAC;UAC3DjD,MAAM,CAAC8D,WAAW,EAAED,KAAK,CAAC;UAC1B,IAAIR,WAAW,CAACO,MAAM,CAACI,KAAK,EAAE;YAC5BrC,IAAI,CAACyB,aAAa,CAACH,GAAG,EAAE,OAAO,EAAEI,WAAW,CAACO,MAAM,CAACI,KAAK,CAAC;YAC1DX,WAAW,CAACO,MAAM,CAACI,KAAK,CAAC3B,KAAK,GAAG,IAAI;UACvC;UACA,IAAIjB,QAAQ,IAAIyC,KAAK,EAAE;YACrBlC,IAAI,CAACyB,aAAa,CAACH,GAAG,EAAE,kBAAkB,EAAE,KAAK,CAAC;UACpD;QACF;MACF,CAAC,GAAG;IACN,CAAC;EACH;AACF,CAAC;AACD;AACA;AACA,IAAIgB,oBAAoB,GAAG;EACzBzC,gBAAgB,EAAE,IAAI;EACtB0C,YAAY,EAAE,SAAAA,CAAUxC,OAAO,EAAE;IAC/B;IACA;IACA,IAAIyC,uBAAuB,GAAGlE,aAAa,CAAC,CAAC;IAC7CyB,OAAO,CAAC0C,UAAU,CAAC,UAAUvD,WAAW,EAAE;MACxC,IAAIwD,OAAO,GAAGxD,WAAW,CAACyD,UAAU,CAAC,CAAC;MACtC,IAAIzD,WAAW,CAAC0D,eAAe,CAAC,CAAC,EAAE;QACjC;MACF;MACA,IAAIC,GAAG,GAAG3D,WAAW,CAAC4D,IAAI,GAAG,GAAG,GAAGJ,OAAO;MAC1C,IAAIK,UAAU,GAAGP,uBAAuB,CAACQ,GAAG,CAACH,GAAG,CAAC;MACjD,IAAI,CAACE,UAAU,EAAE;QACfA,UAAU,GAAG,CAAC,CAAC;QACfP,uBAAuB,CAACS,GAAG,CAACJ,GAAG,EAAEE,UAAU,CAAC;MAC9C;MACAnE,KAAK,CAACM,WAAW,CAAC,CAACgE,KAAK,GAAGH,UAAU;IACvC,CAAC,CAAC;IACFhD,OAAO,CAAC0C,UAAU,CAAC,UAAUvD,WAAW,EAAE;MACxC,IAAIA,WAAW,CAAC0D,eAAe,CAAC,CAAC,IAAI7C,OAAO,CAACqB,gBAAgB,CAAClC,WAAW,CAAC,EAAE;QAC1E;MACF;MACA,IAAIiE,OAAO,GAAGjE,WAAW,CAACkE,UAAU,CAAC,CAAC;MACtC,IAAIC,MAAM,GAAG,CAAC,CAAC;MACf,IAAIrD,IAAI,GAAGd,WAAW,CAACe,OAAO,CAAC,CAAC;MAChC,IAAI8C,UAAU,GAAGnE,KAAK,CAACM,WAAW,CAAC,CAACgE,KAAK;MACzC,IAAI/D,SAAS,GAAGD,WAAW,CAACgB,qBAAqB,IAAI,WAAW;MAChE,IAAIT,QAAQ,GAAGD,kBAAkB,CAACN,WAAW,EAAEC,SAAS,CAAC;MACzDa,IAAI,CAACsD,IAAI,CAAC,UAAUhC,GAAG,EAAE;QACvB,IAAIiC,MAAM,GAAGvD,IAAI,CAACwD,WAAW,CAAClC,GAAG,CAAC;QAClC+B,MAAM,CAACE,MAAM,CAAC,GAAGjC,GAAG;MACtB,CAAC,CAAC;MACF;MACA;MACA6B,OAAO,CAACG,IAAI,CAAC,UAAUC,MAAM,EAAE;QAC7B,IAAIjC,GAAG,GAAG+B,MAAM,CAACE,MAAM,CAAC;QACxB,IAAIE,WAAW,GAAGzD,IAAI,CAAC0D,aAAa,CAACpC,GAAG,EAAE,kBAAkB,CAAC;QAC7D;QACA;QACA;QACA;QACA,IAAImC,WAAW,EAAE;UACf,IAAI3E,SAAS,GAAGkB,IAAI,CAACoC,sBAAsB,CAACd,GAAG,EAAE,OAAO,CAAC;UACzD,IAAIqC,MAAM,GAAGR,OAAO,CAACS,OAAO,CAACL,MAAM,CAAC,IAAIA,MAAM,GAAG,EAAE;UACnD,IAAIM,SAAS,GAAGV,OAAO,CAACW,KAAK,CAAC,CAAC;UAC/BhF,SAAS,CAACW,QAAQ,CAAC,GAAGP,WAAW,CAAC+B,mBAAmB,CAAC0C,MAAM,EAAEZ,UAAU,EAAEc,SAAS,CAAC;QACtF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC;AACD,SAASlE,eAAe,EAAEgC,aAAa,EAAEW,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}