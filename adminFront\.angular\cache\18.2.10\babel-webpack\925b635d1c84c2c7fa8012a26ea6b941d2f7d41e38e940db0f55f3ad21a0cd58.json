{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Scottish Gaelic [gd]\n//! author : <PERSON> : https://github.com/jonashdown\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var months = ['Am Faoilleach', 'An Gearran', 'Am <PERSON>', 'An Giblean', 'An <PERSON>', 'An t-Ògmhios', 'An t-Iuchar', 'An Lùnastal', 'An t-Sultain', 'An Dàmhair', 'An t-Samhain', 'An Dùbhlachd'],\n    monthsShort = ['Faoi', 'Gear', 'Màrt', 'Gibl', 'Cèit', 'Ògmh', 'Iuch', 'Lùn', 'Sult', '<PERSON>àmh', 'Samh', 'Dùbh'],\n    weekdays = ['<PERSON><PERSON><PERSON><PERSON><PERSON>h', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>aoin', '<PERSON><PERSON><PERSON>', 'Disathairne'],\n    weekdaysShort = ['Did', 'Dil', 'Dim', 'Dic', 'Dia', 'Dih', 'Dis'],\n    weekdaysMin = ['Dò', 'Lu', 'Mà', 'Ci', 'Ar', 'Ha', 'Sa'];\n  var gd = moment.defineLocale('gd', {\n    months: months,\n    monthsShort: monthsShort,\n    monthsParseExact: true,\n    weekdays: weekdays,\n    weekdaysShort: weekdaysShort,\n    weekdaysMin: weekdaysMin,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[An-diugh aig] LT',\n      nextDay: '[A-màireach aig] LT',\n      nextWeek: 'dddd [aig] LT',\n      lastDay: '[An-dè aig] LT',\n      lastWeek: 'dddd [seo chaidh] [aig] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'ann an %s',\n      past: 'bho chionn %s',\n      s: 'beagan diogan',\n      ss: '%d diogan',\n      m: 'mionaid',\n      mm: '%d mionaidean',\n      h: 'uair',\n      hh: '%d uairean',\n      d: 'latha',\n      dd: '%d latha',\n      M: 'mìos',\n      MM: '%d mìosan',\n      y: 'bliadhna',\n      yy: '%d bliadhna'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(d|na|mh)/,\n    ordinal: function (number) {\n      var output = number === 1 ? 'd' : number % 10 === 2 ? 'na' : 'mh';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return gd;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}