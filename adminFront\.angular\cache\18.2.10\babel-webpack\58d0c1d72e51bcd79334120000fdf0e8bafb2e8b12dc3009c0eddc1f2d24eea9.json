{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport createSeriesDataSimply from './createSeriesDataSimply.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport { makeSeriesEncodeForAxisCoordSys } from '../../data/helper/sourceHelper.js';\nvar WhiskerBoxCommonMixin = /** @class */function () {\n  function WhiskerBoxCommonMixin() {}\n  /**\r\n   * @private\r\n   */\n  WhiskerBoxCommonMixin.prototype._hasEncodeRule = function (key) {\n    var encodeRules = this.getEncode();\n    return encodeRules && encodeRules.get(key) != null;\n  };\n  /**\r\n   * @override\r\n   */\n  WhiskerBoxCommonMixin.prototype.getInitialData = function (option, ecModel) {\n    // When both types of xAxis and yAxis are 'value', layout is\n    // needed to be specified by user. Otherwise, layout can be\n    // judged by which axis is category.\n    var ordinalMeta;\n    var xAxisModel = ecModel.getComponent('xAxis', this.get('xAxisIndex'));\n    var yAxisModel = ecModel.getComponent('yAxis', this.get('yAxisIndex'));\n    var xAxisType = xAxisModel.get('type');\n    var yAxisType = yAxisModel.get('type');\n    var addOrdinal;\n    // FIXME\n    // Consider time axis.\n    if (xAxisType === 'category') {\n      option.layout = 'horizontal';\n      ordinalMeta = xAxisModel.getOrdinalMeta();\n      addOrdinal = !this._hasEncodeRule('x');\n    } else if (yAxisType === 'category') {\n      option.layout = 'vertical';\n      ordinalMeta = yAxisModel.getOrdinalMeta();\n      addOrdinal = !this._hasEncodeRule('y');\n    } else {\n      option.layout = option.layout || 'horizontal';\n    }\n    var coordDims = ['x', 'y'];\n    var baseAxisDimIndex = option.layout === 'horizontal' ? 0 : 1;\n    var baseAxisDim = this._baseAxisDim = coordDims[baseAxisDimIndex];\n    var otherAxisDim = coordDims[1 - baseAxisDimIndex];\n    var axisModels = [xAxisModel, yAxisModel];\n    var baseAxisType = axisModels[baseAxisDimIndex].get('type');\n    var otherAxisType = axisModels[1 - baseAxisDimIndex].get('type');\n    var data = option.data;\n    // Clone a new data for next setOption({}) usage.\n    // Avoid modifying current data will affect further update.\n    if (data && addOrdinal) {\n      var newOptionData_1 = [];\n      zrUtil.each(data, function (item, index) {\n        var newItem;\n        if (zrUtil.isArray(item)) {\n          newItem = item.slice();\n          // Modify current using data.\n          item.unshift(index);\n        } else if (zrUtil.isArray(item.value)) {\n          newItem = zrUtil.extend({}, item);\n          newItem.value = newItem.value.slice();\n          // Modify current using data.\n          item.value.unshift(index);\n        } else {\n          newItem = item;\n        }\n        newOptionData_1.push(newItem);\n      });\n      option.data = newOptionData_1;\n    }\n    var defaultValueDimensions = this.defaultValueDimensions;\n    var coordDimensions = [{\n      name: baseAxisDim,\n      type: getDimensionTypeByAxis(baseAxisType),\n      ordinalMeta: ordinalMeta,\n      otherDims: {\n        tooltip: false,\n        itemName: 0\n      },\n      dimsDef: ['base']\n    }, {\n      name: otherAxisDim,\n      type: getDimensionTypeByAxis(otherAxisType),\n      dimsDef: defaultValueDimensions.slice()\n    }];\n    return createSeriesDataSimply(this, {\n      coordDimensions: coordDimensions,\n      dimensionsCount: defaultValueDimensions.length + 1,\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForAxisCoordSys, coordDimensions, this)\n    });\n  };\n  /**\r\n   * If horizontal, base axis is x, otherwise y.\r\n   * @override\r\n   */\n  WhiskerBoxCommonMixin.prototype.getBaseAxis = function () {\n    var dim = this._baseAxisDim;\n    return this.ecModel.getComponent(dim + 'Axis', this.get(dim + 'AxisIndex')).axis;\n  };\n  return WhiskerBoxCommonMixin;\n}();\n;\nexport { WhiskerBoxCommonMixin };", "map": {"version": 3, "names": ["createSeriesDataSimply", "zrUtil", "getDimensionTypeByAxis", "makeSeriesEncodeForAxisCoordSys", "WhiskerBoxCommonMixin", "prototype", "_hasEncodeRule", "key", "encodeRules", "getEncode", "get", "getInitialData", "option", "ecModel", "ordinalMeta", "xAxisModel", "getComponent", "yAxisModel", "xAxisType", "yAxisType", "addOrdinal", "layout", "getOrdinalMeta", "coordDims", "baseAxisDimIndex", "baseAxisDim", "_baseAxisDim", "otherAxisDim", "axisModels", "baseAxisType", "otherAxisType", "data", "newOptionData_1", "each", "item", "index", "newItem", "isArray", "slice", "unshift", "value", "extend", "push", "defaultValueDimensions", "coordDimensions", "name", "type", "otherDims", "tooltip", "itemName", "dimsDef", "dimensionsCount", "length", "encodeDefaulter", "curry", "getBaseAxis", "dim", "axis"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/helper/whiskerBoxCommon.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport createSeriesDataSimply from './createSeriesDataSimply.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport { makeSeriesEncodeForAxisCoordSys } from '../../data/helper/sourceHelper.js';\nvar WhiskerBoxCommonMixin = /** @class */function () {\n  function WhiskerBoxCommonMixin() {}\n  /**\r\n   * @private\r\n   */\n  WhiskerBoxCommonMixin.prototype._hasEncodeRule = function (key) {\n    var encodeRules = this.getEncode();\n    return encodeRules && encodeRules.get(key) != null;\n  };\n  /**\r\n   * @override\r\n   */\n  WhiskerBoxCommonMixin.prototype.getInitialData = function (option, ecModel) {\n    // When both types of xAxis and yAxis are 'value', layout is\n    // needed to be specified by user. Otherwise, layout can be\n    // judged by which axis is category.\n    var ordinalMeta;\n    var xAxisModel = ecModel.getComponent('xAxis', this.get('xAxisIndex'));\n    var yAxisModel = ecModel.getComponent('yAxis', this.get('yAxisIndex'));\n    var xAxisType = xAxisModel.get('type');\n    var yAxisType = yAxisModel.get('type');\n    var addOrdinal;\n    // FIXME\n    // Consider time axis.\n    if (xAxisType === 'category') {\n      option.layout = 'horizontal';\n      ordinalMeta = xAxisModel.getOrdinalMeta();\n      addOrdinal = !this._hasEncodeRule('x');\n    } else if (yAxisType === 'category') {\n      option.layout = 'vertical';\n      ordinalMeta = yAxisModel.getOrdinalMeta();\n      addOrdinal = !this._hasEncodeRule('y');\n    } else {\n      option.layout = option.layout || 'horizontal';\n    }\n    var coordDims = ['x', 'y'];\n    var baseAxisDimIndex = option.layout === 'horizontal' ? 0 : 1;\n    var baseAxisDim = this._baseAxisDim = coordDims[baseAxisDimIndex];\n    var otherAxisDim = coordDims[1 - baseAxisDimIndex];\n    var axisModels = [xAxisModel, yAxisModel];\n    var baseAxisType = axisModels[baseAxisDimIndex].get('type');\n    var otherAxisType = axisModels[1 - baseAxisDimIndex].get('type');\n    var data = option.data;\n    // Clone a new data for next setOption({}) usage.\n    // Avoid modifying current data will affect further update.\n    if (data && addOrdinal) {\n      var newOptionData_1 = [];\n      zrUtil.each(data, function (item, index) {\n        var newItem;\n        if (zrUtil.isArray(item)) {\n          newItem = item.slice();\n          // Modify current using data.\n          item.unshift(index);\n        } else if (zrUtil.isArray(item.value)) {\n          newItem = zrUtil.extend({}, item);\n          newItem.value = newItem.value.slice();\n          // Modify current using data.\n          item.value.unshift(index);\n        } else {\n          newItem = item;\n        }\n        newOptionData_1.push(newItem);\n      });\n      option.data = newOptionData_1;\n    }\n    var defaultValueDimensions = this.defaultValueDimensions;\n    var coordDimensions = [{\n      name: baseAxisDim,\n      type: getDimensionTypeByAxis(baseAxisType),\n      ordinalMeta: ordinalMeta,\n      otherDims: {\n        tooltip: false,\n        itemName: 0\n      },\n      dimsDef: ['base']\n    }, {\n      name: otherAxisDim,\n      type: getDimensionTypeByAxis(otherAxisType),\n      dimsDef: defaultValueDimensions.slice()\n    }];\n    return createSeriesDataSimply(this, {\n      coordDimensions: coordDimensions,\n      dimensionsCount: defaultValueDimensions.length + 1,\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForAxisCoordSys, coordDimensions, this)\n    });\n  };\n  /**\r\n   * If horizontal, base axis is x, otherwise y.\r\n   * @override\r\n   */\n  WhiskerBoxCommonMixin.prototype.getBaseAxis = function () {\n    var dim = this._baseAxisDim;\n    return this.ecModel.getComponent(dim + 'Axis', this.get(dim + 'AxisIndex')).axis;\n  };\n  return WhiskerBoxCommonMixin;\n}();\n;\nexport { WhiskerBoxCommonMixin };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,sBAAsB,MAAM,6BAA6B;AAChE,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,sBAAsB,QAAQ,sCAAsC;AAC7E,SAASC,+BAA+B,QAAQ,mCAAmC;AACnF,IAAIC,qBAAqB,GAAG,aAAa,YAAY;EACnD,SAASA,qBAAqBA,CAAA,EAAG,CAAC;EAClC;AACF;AACA;EACEA,qBAAqB,CAACC,SAAS,CAACC,cAAc,GAAG,UAAUC,GAAG,EAAE;IAC9D,IAAIC,WAAW,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAClC,OAAOD,WAAW,IAAIA,WAAW,CAACE,GAAG,CAACH,GAAG,CAAC,IAAI,IAAI;EACpD,CAAC;EACD;AACF;AACA;EACEH,qBAAqB,CAACC,SAAS,CAACM,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IAC1E;IACA;IACA;IACA,IAAIC,WAAW;IACf,IAAIC,UAAU,GAAGF,OAAO,CAACG,YAAY,CAAC,OAAO,EAAE,IAAI,CAACN,GAAG,CAAC,YAAY,CAAC,CAAC;IACtE,IAAIO,UAAU,GAAGJ,OAAO,CAACG,YAAY,CAAC,OAAO,EAAE,IAAI,CAACN,GAAG,CAAC,YAAY,CAAC,CAAC;IACtE,IAAIQ,SAAS,GAAGH,UAAU,CAACL,GAAG,CAAC,MAAM,CAAC;IACtC,IAAIS,SAAS,GAAGF,UAAU,CAACP,GAAG,CAAC,MAAM,CAAC;IACtC,IAAIU,UAAU;IACd;IACA;IACA,IAAIF,SAAS,KAAK,UAAU,EAAE;MAC5BN,MAAM,CAACS,MAAM,GAAG,YAAY;MAC5BP,WAAW,GAAGC,UAAU,CAACO,cAAc,CAAC,CAAC;MACzCF,UAAU,GAAG,CAAC,IAAI,CAACd,cAAc,CAAC,GAAG,CAAC;IACxC,CAAC,MAAM,IAAIa,SAAS,KAAK,UAAU,EAAE;MACnCP,MAAM,CAACS,MAAM,GAAG,UAAU;MAC1BP,WAAW,GAAGG,UAAU,CAACK,cAAc,CAAC,CAAC;MACzCF,UAAU,GAAG,CAAC,IAAI,CAACd,cAAc,CAAC,GAAG,CAAC;IACxC,CAAC,MAAM;MACLM,MAAM,CAACS,MAAM,GAAGT,MAAM,CAACS,MAAM,IAAI,YAAY;IAC/C;IACA,IAAIE,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAC1B,IAAIC,gBAAgB,GAAGZ,MAAM,CAACS,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;IAC7D,IAAII,WAAW,GAAG,IAAI,CAACC,YAAY,GAAGH,SAAS,CAACC,gBAAgB,CAAC;IACjE,IAAIG,YAAY,GAAGJ,SAAS,CAAC,CAAC,GAAGC,gBAAgB,CAAC;IAClD,IAAII,UAAU,GAAG,CAACb,UAAU,EAAEE,UAAU,CAAC;IACzC,IAAIY,YAAY,GAAGD,UAAU,CAACJ,gBAAgB,CAAC,CAACd,GAAG,CAAC,MAAM,CAAC;IAC3D,IAAIoB,aAAa,GAAGF,UAAU,CAAC,CAAC,GAAGJ,gBAAgB,CAAC,CAACd,GAAG,CAAC,MAAM,CAAC;IAChE,IAAIqB,IAAI,GAAGnB,MAAM,CAACmB,IAAI;IACtB;IACA;IACA,IAAIA,IAAI,IAAIX,UAAU,EAAE;MACtB,IAAIY,eAAe,GAAG,EAAE;MACxB/B,MAAM,CAACgC,IAAI,CAACF,IAAI,EAAE,UAAUG,IAAI,EAAEC,KAAK,EAAE;QACvC,IAAIC,OAAO;QACX,IAAInC,MAAM,CAACoC,OAAO,CAACH,IAAI,CAAC,EAAE;UACxBE,OAAO,GAAGF,IAAI,CAACI,KAAK,CAAC,CAAC;UACtB;UACAJ,IAAI,CAACK,OAAO,CAACJ,KAAK,CAAC;QACrB,CAAC,MAAM,IAAIlC,MAAM,CAACoC,OAAO,CAACH,IAAI,CAACM,KAAK,CAAC,EAAE;UACrCJ,OAAO,GAAGnC,MAAM,CAACwC,MAAM,CAAC,CAAC,CAAC,EAAEP,IAAI,CAAC;UACjCE,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACI,KAAK,CAACF,KAAK,CAAC,CAAC;UACrC;UACAJ,IAAI,CAACM,KAAK,CAACD,OAAO,CAACJ,KAAK,CAAC;QAC3B,CAAC,MAAM;UACLC,OAAO,GAAGF,IAAI;QAChB;QACAF,eAAe,CAACU,IAAI,CAACN,OAAO,CAAC;MAC/B,CAAC,CAAC;MACFxB,MAAM,CAACmB,IAAI,GAAGC,eAAe;IAC/B;IACA,IAAIW,sBAAsB,GAAG,IAAI,CAACA,sBAAsB;IACxD,IAAIC,eAAe,GAAG,CAAC;MACrBC,IAAI,EAAEpB,WAAW;MACjBqB,IAAI,EAAE5C,sBAAsB,CAAC2B,YAAY,CAAC;MAC1Cf,WAAW,EAAEA,WAAW;MACxBiC,SAAS,EAAE;QACTC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE;MACZ,CAAC;MACDC,OAAO,EAAE,CAAC,MAAM;IAClB,CAAC,EAAE;MACDL,IAAI,EAAElB,YAAY;MAClBmB,IAAI,EAAE5C,sBAAsB,CAAC4B,aAAa,CAAC;MAC3CoB,OAAO,EAAEP,sBAAsB,CAACL,KAAK,CAAC;IACxC,CAAC,CAAC;IACF,OAAOtC,sBAAsB,CAAC,IAAI,EAAE;MAClC4C,eAAe,EAAEA,eAAe;MAChCO,eAAe,EAAER,sBAAsB,CAACS,MAAM,GAAG,CAAC;MAClDC,eAAe,EAAEpD,MAAM,CAACqD,KAAK,CAACnD,+BAA+B,EAAEyC,eAAe,EAAE,IAAI;IACtF,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;EACExC,qBAAqB,CAACC,SAAS,CAACkD,WAAW,GAAG,YAAY;IACxD,IAAIC,GAAG,GAAG,IAAI,CAAC9B,YAAY;IAC3B,OAAO,IAAI,CAACb,OAAO,CAACG,YAAY,CAACwC,GAAG,GAAG,MAAM,EAAE,IAAI,CAAC9C,GAAG,CAAC8C,GAAG,GAAG,WAAW,CAAC,CAAC,CAACC,IAAI;EAClF,CAAC;EACD,OAAOrD,qBAAqB;AAC9B,CAAC,CAAC,CAAC;AACH;AACA,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}