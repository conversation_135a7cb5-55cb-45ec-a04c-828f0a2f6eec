{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbButtonModule, NbIconModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./space-template-selector.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@nebular/theme\";\nfunction SpaceTemplateSelectorButtonComponent_nb_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"icon\", ctx_r0.icon);\n  }\n}\nexport class SpaceTemplateSelectorButtonComponent {\n  constructor(spaceTemplateSelectorService) {\n    this.spaceTemplateSelectorService = spaceTemplateSelectorService;\n    this.buildCaseId = '';\n    this.text = '模板新增';\n    this.icon = 'fas fa-layer-group';\n    this.buttonClass = 'btn btn-warning mr-2';\n    this.disabled = false;\n    this.config = {};\n    this.templateApplied = new EventEmitter();\n    this.beforeOpen = new EventEmitter();\n    this.error = new EventEmitter();\n  }\n  openSelector() {\n    this.beforeOpen.emit();\n    // 建立完整的配置\n    const fullConfig = {\n      buildCaseId: this.buildCaseId,\n      buttonText: this.text,\n      buttonIcon: this.icon,\n      buttonClass: this.buttonClass,\n      ...this.config\n    };\n    this.spaceTemplateSelectorService.openSelector(fullConfig).subscribe({\n      next: result => {\n        if (result) {\n          this.templateApplied.emit(result);\n        }\n      },\n      error: error => {\n        this.error.emit(error.message || '開啟模板選擇器時發生錯誤');\n      }\n    });\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorButtonComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorButtonComponent)(i0.ɵɵdirectiveInject(i1.SpaceTemplateSelectorService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceTemplateSelectorButtonComponent,\n      selectors: [[\"app-space-template-selector-button\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        text: \"text\",\n        icon: \"icon\",\n        buttonClass: \"buttonClass\",\n        disabled: \"disabled\",\n        config: \"config\"\n      },\n      outputs: {\n        templateApplied: \"templateApplied\",\n        beforeOpen: \"beforeOpen\",\n        error: \"error\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 5,\n      consts: [[\"type\", \"button\", 3, \"click\", \"disabled\"], [\"class\", \"mr-1\", 3, \"icon\", 4, \"ngIf\"], [1, \"mr-1\", 3, \"icon\"]],\n      template: function SpaceTemplateSelectorButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorButtonComponent_Template_button_click_0_listener() {\n            return ctx.openSelector();\n          });\n          i0.ɵɵtemplate(1, SpaceTemplateSelectorButtonComponent_nb_icon_1_Template, 1, 1, \"nb-icon\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.buttonClass);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.icon);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.text, \" \");\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, NbButtonModule, NbIconModule, i3.NbIconComponent],\n      styles: [\".mr-1[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInNwYWNlLXRlbXBsYXRlLXNlbGVjdG9yLWJ1dHRvbi5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0k7RUFDRSxxQkFBQTtBQUFOIiwiZmlsZSI6InNwYWNlLXRlbXBsYXRlLXNlbGVjdG9yLWJ1dHRvbi5jb21wb25lbnQudHMiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAubXItMSB7XG4gICAgICBtYXJnaW4tcmlnaHQ6IDAuMjVyZW07XG4gICAgfVxuICAiXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvc3BhY2UtdGVtcGxhdGUtc2VsZWN0b3Ivc3BhY2UtdGVtcGxhdGUtc2VsZWN0b3ItYnV0dG9uLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNFLHFCQUFBO0FBQU47QUFDQSw0WEFBNFgiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAubXItMSB7XG4gICAgICBtYXJnaW4tcmlnaHQ6IDAuMjVyZW07XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "NbButtonModule", "NbIconModule", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "icon", "SpaceTemplateSelectorButtonComponent", "constructor", "spaceTemplateSelectorService", "buildCaseId", "text", "buttonClass", "disabled", "config", "templateApplied", "beforeOpen", "error", "openSelector", "emit", "fullConfig", "buttonText", "buttonIcon", "subscribe", "next", "result", "message", "ɵɵdirectiveInject", "i1", "SpaceTemplateSelectorService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SpaceTemplateSelectorButtonComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "SpaceTemplateSelectorButtonComponent_Template_button_click_0_listener", "ɵɵtemplate", "SpaceTemplateSelectorButtonComponent_nb_icon_1_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassMap", "ɵɵadvance", "ɵɵtextInterpolate1", "i2", "NgIf", "i3", "NbIconComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector-button.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbButtonModule, NbIconModule } from '@nebular/theme';\r\nimport { SpaceTemplateSelectorService, SpaceTemplateSelectorConfig } from './space-template-selector.service';\r\nimport { SpaceTemplateConfig } from './space-template-selector.component';\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector-button',\r\n  standalone: true,\r\n  imports: [CommonModule, NbButtonModule, NbIconModule],\r\n  template: `\r\n    <button \r\n      type=\"button\" \r\n      [class]=\"buttonClass\" \r\n      (click)=\"openSelector()\"\r\n      [disabled]=\"disabled\">\r\n      <nb-icon *ngIf=\"icon\" [icon]=\"icon\" class=\"mr-1\"></nb-icon>\r\n      {{ text }}\r\n    </button>\r\n  `,\r\n  styles: [`\r\n    .mr-1 {\r\n      margin-right: 0.25rem;\r\n    }\r\n  `]\r\n})\r\nexport class SpaceTemplateSelectorButtonComponent {\r\n  @Input() buildCaseId: string = '';\r\n  @Input() text: string = '模板新增';\r\n  @Input() icon: string = 'fas fa-layer-group';\r\n  @Input() buttonClass: string = 'btn btn-warning mr-2';\r\n  @Input() disabled: boolean = false;\r\n  @Input() config: Partial<SpaceTemplateSelectorConfig> = {};\r\n  \r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n  @Output() beforeOpen = new EventEmitter<void>();\r\n  @Output() error = new EventEmitter<string>();\r\n\r\n  constructor(private spaceTemplateSelectorService: SpaceTemplateSelectorService) {}\r\n\r\n  openSelector() {\r\n    this.beforeOpen.emit();\r\n    \r\n    // 建立完整的配置\r\n    const fullConfig: SpaceTemplateSelectorConfig = {\r\n      buildCaseId: this.buildCaseId,\r\n      buttonText: this.text,\r\n      buttonIcon: this.icon,\r\n      buttonClass: this.buttonClass,\r\n      ...this.config\r\n    };\r\n\r\n    this.spaceTemplateSelectorService.openSelector(fullConfig)\r\n      .subscribe({\r\n        next: (result) => {\r\n          if (result) {\r\n            this.templateApplied.emit(result);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.error.emit(error.message || '開啟模板選擇器時發生錯誤');\r\n        }\r\n      });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAQ,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;;;;;;;IAcvDC,EAAA,CAAAC,SAAA,iBAA2D;;;;IAArCD,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAa;;;AAUzC,OAAM,MAAOC,oCAAoC;EAY/CC,YAAoBC,4BAA0D;IAA1D,KAAAA,4BAA4B,GAA5BA,4BAA4B;IAXvC,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,IAAI,GAAW,MAAM;IACrB,KAAAL,IAAI,GAAW,oBAAoB;IACnC,KAAAM,WAAW,GAAW,sBAAsB;IAC5C,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,MAAM,GAAyC,EAAE;IAEhD,KAAAC,eAAe,GAAG,IAAIjB,YAAY,EAAuB;IACzD,KAAAkB,UAAU,GAAG,IAAIlB,YAAY,EAAQ;IACrC,KAAAmB,KAAK,GAAG,IAAInB,YAAY,EAAU;EAEqC;EAEjFoB,YAAYA,CAAA;IACV,IAAI,CAACF,UAAU,CAACG,IAAI,EAAE;IAEtB;IACA,MAAMC,UAAU,GAAgC;MAC9CV,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BW,UAAU,EAAE,IAAI,CAACV,IAAI;MACrBW,UAAU,EAAE,IAAI,CAAChB,IAAI;MACrBM,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B,GAAG,IAAI,CAACE;KACT;IAED,IAAI,CAACL,4BAA4B,CAACS,YAAY,CAACE,UAAU,CAAC,CACvDG,SAAS,CAAC;MACTC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAIA,MAAM,EAAE;UACV,IAAI,CAACV,eAAe,CAACI,IAAI,CAACM,MAAM,CAAC;QACnC;MACF,CAAC;MACDR,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,CAACE,IAAI,CAACF,KAAK,CAACS,OAAO,IAAI,cAAc,CAAC;MAClD;KACD,CAAC;EACN;;;uCArCWnB,oCAAoC,EAAAL,EAAA,CAAAyB,iBAAA,CAAAC,EAAA,CAAAC,4BAAA;IAAA;EAAA;;;YAApCtB,oCAAoC;MAAAuB,SAAA;MAAAC,MAAA;QAAArB,WAAA;QAAAC,IAAA;QAAAL,IAAA;QAAAM,WAAA;QAAAC,QAAA;QAAAC,MAAA;MAAA;MAAAkB,OAAA;QAAAjB,eAAA;QAAAC,UAAA;QAAAC,KAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GAAAhC,EAAA,CAAAiC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAf7CvC,EAAA,CAAAyC,cAAA,gBAIwB;UADtBzC,EAAA,CAAA0C,UAAA,mBAAAC,sEAAA;YAAA,OAASH,GAAA,CAAAxB,YAAA,EAAc;UAAA,EAAC;UAExBhB,EAAA,CAAA4C,UAAA,IAAAC,uDAAA,qBAAiD;UACjD7C,EAAA,CAAA8C,MAAA,GACF;UAAA9C,EAAA,CAAA+C,YAAA,EAAS;;;UALP/C,EAAA,CAAAgD,UAAA,CAAAR,GAAA,CAAA9B,WAAA,CAAqB;UAErBV,EAAA,CAAAE,UAAA,aAAAsC,GAAA,CAAA7B,QAAA,CAAqB;UACXX,EAAA,CAAAiD,SAAA,EAAU;UAAVjD,EAAA,CAAAE,UAAA,SAAAsC,GAAA,CAAApC,IAAA,CAAU;UACpBJ,EAAA,CAAAiD,SAAA,EACF;UADEjD,EAAA,CAAAkD,kBAAA,MAAAV,GAAA,CAAA/B,IAAA,MACF;;;qBATQZ,YAAY,EAAAsD,EAAA,CAAAC,IAAA,EAAEtD,cAAc,EAAEC,YAAY,EAAAsD,EAAA,CAAAC,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}