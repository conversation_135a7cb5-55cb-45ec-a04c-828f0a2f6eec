# Nebular 主題色彩系統優化指南

## 概述
此次優化統一了整個應用的色彩系統，以金色漸變作為主要品牌色彩，提升了視覺一致性和用戶體驗。

## 主要改進

### 1. 色彩變數系統化
- 創建了統一的色彩變數檔案 `_colors.scss`
- 所有色彩都使用語義化命名
- 支援不同狀態的色彩變化（hover、active、disabled）

### 2. 品牌色彩系統
```scss
// 主要金色漸變
$primary-gold-light: #B8A676;      // 主要金色 - 淺色
$primary-gold-dark: #AE9B66;       // 主要金色 - 深色
$primary-gold-darker: #9B8A5A;     // 主要金色 - 更深
$primary-gold-hover: #C4B382;      // 懸停狀態
```

### 3. Nebular 主題整合
- 更新了所有四個主題（default、cosmic、corporate、dark）
- 統一使用金色作為主要色彩
- 保持各主題的特色同時融入品牌色彩

### 4. 組件樣式覆蓋
- 創建了 `_nebular-overrides.scss` 統一所有 Nebular 組件樣式
- 包含按鈕、表單、卡片、表格等所有常用組件
- 支援所有互動狀態（hover、focus、active、disabled）

## 文件結構

```
src/app/@theme/styles/
├── _colors.scss              # 統一色彩變數系統
├── _nebular-overrides.scss   # Nebular 組件樣式覆蓋
├── _overrides.scss           # 全域樣式覆蓋（已更新）
├── themes.scss               # Nebular 主題配置（已更新）
├── pace.theme.scss           # 載入進度條樣式（已更新）
├── theme.default.ts          # 預設主題 JS 配置（已更新）
├── theme.cosmic.ts           # Cosmic 主題配置
├── theme.corporate.ts        # Corporate 主題配置
├── theme.dark.ts             # Dark 主題配置
└── README.md                 # 本使用指南
```

## 使用指南

### 在組件中使用色彩變數
```scss
@import '../../@theme/styles/colors';

.my-component {
  background: $gradient-primary;
  color: $text-light;
  border: 1px solid $border-primary;
  
  &:hover {
    background: $gradient-primary-hover;
    box-shadow: $shadow-md;
  }
}
```

### 按鈕樣式
```scss
// 主要按鈕
.primary-button {
  background: $gradient-primary;
  color: $text-light;
  box-shadow: $shadow-md;
  
  &:hover {
    background: $gradient-primary-hover;
    transform: translateY(-1px);
    box-shadow: $shadow-lg;
  }
}

// 次要按鈕
.secondary-button {
  border: 1px solid $border-medium;
  color: $text-tertiary;
  
  &:hover {
    border-color: $primary-gold-light;
    color: $primary-gold-dark;
    background-color: alpha-gold(0.05);
  }
}
```

### 文字色彩
- `$text-primary`: 主要標題和重要文字
- `$text-secondary`: 次要文字和說明文字
- `$text-tertiary`: 輔助文字
- `$text-disabled`: 禁用狀態文字

### 背景色彩
- `$bg-primary`: 主要背景（白色）
- `$bg-secondary`: 次要背景（淺奶油色）
- `$gradient-background`: 漸變背景

### 陰影系統
- `$shadow-sm`: 輕微陰影
- `$shadow-md`: 中等陰影
- `$shadow-lg`: 較強陰影
- `$shadow-xl`: 最強陰影

## 已更新的組件

### 1. Nebular 組件
- **nb-button**: 統一了所有狀態的按鈕樣式
- **nb-form-field**: 優化了輸入框和焦點狀態
- **nb-select**: 統一了下拉選單樣式
- **nb-checkbox/nb-radio**: 使用金色主題
- **nb-card**: 增強了卡片陰影和邊框
- **nb-tabset**: 統一了標籤頁樣式
- **nb-calendar**: 日期選擇器使用金色主題

### 2. Bootstrap 組件
- **按鈕**: .btn-primary, .btn-outline-primary 等
- **表單**: .form-control, .form-select 焦點樣式
- **表格**: .table 樣式增強
- **卡片**: .card 陰影和邊框優化
- **分頁**: .pagination 使用金色主題
- **徽章**: .badge 樣式統一

### 3. 載入進度條
- 使用金色漸變
- 增強動畫效果
- 統一視覺風格

## 開發建議

### 1. 使用色彩變數
總是使用預定義的色彩變數，避免硬編碼色值：
```scss
// ✅ 正確
background: $primary-gold-light;

// ❌ 錯誤
background: #B8A676;
```

### 2. 遵循陰影系統
使用預定義的陰影等級：
```scss
// ✅ 正確
box-shadow: $shadow-md;

// ❌ 錯誤
box-shadow: 0 2px 8px rgba(0,0,0,0.15);
```

### 3. 保持動畫一致性
使用預定義的過渡時間：
```scss
// ✅ 正確
transition: $transition-normal;

// ❌ 錯誤
transition: all 0.3s ease;
```

### 4. 測試無障礙性
確保色彩對比度符合 WCAG 標準，特別是文字和背景的對比度。

## 向後兼容性
為確保現有代碼不受影響，保留了以下變數：
- `$mainColorB`, `$mainColorG`, `$mainColorGray`
- `$textColor`, `$mainColorY`, `$color-drop`

這些變數會自動映射到新的色彩系統。

## 主題切換
應用支援四種主題，都已整合金色品牌色彩：
- **default**: 標準淺色主題
- **cosmic**: 深色科技主題
- **corporate**: 企業級主題
- **dark**: 純深色主題

## 輔助函數
```scss
// 透明度變化
@function alpha-gold($alpha) {
  @return rgba(184, 166, 118, $alpha);
}

// 亮度調整
@function lighten-gold($amount) {
  @return lighten($primary-gold-light, $amount);
}

@function darken-gold($amount) {
  @return darken($primary-gold-light, $amount);
}
```

## 重要更新：完全統一色彩系統

### 🎯 問題解決
針對 `nb-card accent="success"` 等組件顯示綠色而非金色的問題，我們實施了以下解決方案：

#### 1. 主題層級覆蓋
在 `themes.scss` 中將所有狀態色彩（success、warning、danger、info）統一映射到金色系統：
```scss
// 成功色彩 - 統一使用金色系統
color-success-default: $primary-gold-light,
color-warning-default: $primary-gold-light,
color-danger-default: $primary-gold-light,
color-info-default: $primary-gold-light,
```

#### 2. 組件層級強制覆蓋
在 `_nebular-overrides.scss` 中添加強制覆蓋樣式：
```scss
// 強制覆蓋所有狀態色彩
[class*="status-success"],
[class*="accent-success"],
.status-success,
.accent-success {
  border-color: $primary-gold-light !important;
}

// 特別針對 nb-card accent 屬性
nb-card {
  &[accent="success"],
  &.accent-success {
    border-top: 3px solid $primary-gold-light !important;
  }
}
```

#### 3. 全域樣式覆蓋
在 `_overrides.scss` 中添加 Bootstrap 和其他框架的狀態色彩覆蓋：
```scss
.btn-success,
.btn-warning,
.btn-danger,
.btn-info {
  background: $gradient-primary !important;
  border-color: $primary-gold-light !important;
}
```

### ✅ 覆蓋範圍
現在所有以下組件都統一使用金色主題：
- **nb-card** 的所有 accent 屬性
- **nb-button** 的所有 status 屬性
- **nb-form-field** 的所有狀態
- **nb-select** 的所有狀態
- **nb-checkbox/nb-radio** 的選中狀態
- **nb-tag** 的所有狀態
- **Bootstrap 按鈕** 的所有變體
- **Bootstrap 警告框** 的所有類型
- **所有狀態相關的 CSS 類別**

## 故障排除

### 1. 色彩變數未生效
確保在組件 SCSS 文件中正確導入：
```scss
@import '../../@theme/styles/colors';
```

### 2. Nebular 組件樣式未更新
檢查是否正確導入了覆蓋樣式：
```scss
@import '../../@theme/styles/nebular-overrides';
```

### 3. 主題切換問題
確保在 `theme.module.ts` 中正確註冊了所有主題。

### 4. 狀態色彩仍顯示原色
如果某些組件仍顯示綠色/紅色/黃色/藍色：
1. 檢查是否有內聯樣式覆蓋
2. 確認組件載入順序
3. 使用瀏覽器開發工具檢查 CSS 優先級
4. 可能需要添加更具體的選擇器

## 未來擴展

### 1. 深色模式支援
已預留深色主題變數，可進一步擴展：
```scss
$dark-bg-primary: #1A1A1A;
$dark-bg-secondary: #2D2D2D;
$dark-text-primary: #FFFFFF;
```

### 2. 響應式色彩
可根據螢幕尺寸調整色彩：
```scss
@media (max-width: 768px) {
  $mobile-primary: lighten($primary-gold-light, 5%);
}
```

### 3. 自定義主題
可基於現有色彩系統創建新的主題變體。
