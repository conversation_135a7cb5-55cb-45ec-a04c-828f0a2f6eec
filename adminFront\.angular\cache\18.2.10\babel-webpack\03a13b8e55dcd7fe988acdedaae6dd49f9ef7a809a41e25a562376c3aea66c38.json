{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_container_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i_r3 + 1, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_td_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 43);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_tr_55_td_3_span_3_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const itm_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      const dialog_r7 = i0.ɵɵreference(59);\n      return i0.ɵɵresetView(ctx_r5.openModel(dialog_r7, itm_r5));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"getSettingTimeStatus\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itm_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(itm_r5.CHouseId ? \"cursor-pointer text-blue-800 block min-w-[100px]\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 3, itm_r5.CChangeStartDate, itm_r5.CChangeEndDate, ctx_r5.isStatus), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_td_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_td_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_tr_55_td_3_span_3_Template, 3, 7, \"span\", 41)(4, SettingTimePeriodComponent_tr_55_td_3_span_4_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itm_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", itm_r5.CHouseHold, \"-\", itm_r5.CFloor, \"F\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", itm_r5.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !itm_r5.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_tr_55_td_3_Template, 5, 4, \"td\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", item_r8[0].CFloor, \"F\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", item_r8);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 44);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 45);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 44)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 46);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 47)(7, \"div\", 14)(8, \"label\", 15);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 48);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 16);\n    i0.ɵɵelement(13, \"nb-icon\", 17);\n    i0.ɵɵelementStart(14, \"input\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_58_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r5.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 19, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 50);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 17);\n    i0.ɵɵelementStart(21, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_58_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r5.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 19, 5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 45)(25, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_58_Template_button_click_25_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onClose(ref_r10));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_58_Template_button_click_27_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onSubmit(ref_r10));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r11 = i0.ɵɵreference(16);\n    const changeEndDate_r12 = i0.ɵɵreference(23);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r5.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r5.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r12);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    // 新增的屬性\n    this.buildingGroups = [];\n    this.buildingOptions = [];\n    this.selectedBuilding = '';\n    this.availableFloors = [];\n    // 篩選和搜尋\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 批次設定\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: true,\n      selectedBuildings: [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    this.selectedBuildingForBatch = null;\n    // 表格視圖相關屬性\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.loading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalPages = 1;\n    // 排序相關\n    this.sortField = '';\n    this.sortDirection = 'asc';\n    // 數學函數引用\n    this.Math = Math;\n    // 新增的UI控制屬性\n    this.jumpToPage = 1;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  // 新增的UI控制方法\n  hasActiveFilters() {\n    return !!(this.filterOptions.searchKeyword || this.filterOptions.statusFilter || this.filterOptions.floorFilter);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.filterOptions.searchKeyword) count++;\n    if (this.filterOptions.statusFilter) count++;\n    if (this.filterOptions.floorFilter) count++;\n    return count;\n  }\n  resetFilters() {\n    this.searchQuery = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.selectedBuilding = '';\n    this.clearAllFilters();\n  }\n  clearAllFilters() {\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    this.onSearch();\n  }\n  setQuickFilter(status) {\n    if (this.filterOptions.statusFilter === status) {\n      this.filterOptions.statusFilter = '';\n    } else {\n      this.filterOptions.statusFilter = status;\n    }\n    this.onSearch();\n  }\n  clearSelection() {\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.flattenedHouses.forEach(house => house.selected = false);\n  }\n  getStatusIcon(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return 'fas fa-play-circle';\n      case 'pending':\n        return 'fas fa-clock';\n      case 'expired':\n        return 'fas fa-times-circle';\n      case 'not-set':\n        return 'fas fa-exclamation-triangle';\n      case 'disabled':\n        return 'fas fa-ban';\n      default:\n        return 'fas fa-exclamation-triangle';\n    }\n  }\n  jumpToPageAction() {\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\n      this.goToPage(this.jumpToPage);\n    }\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CBuildingName: '未分類',\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  // 新增：建案變更處理\n  onBuildCaseChange() {\n    // 重置所有相關狀態\n    this.resetAllStates();\n    // 執行查詢\n    this.getHouseChangeDate();\n  }\n  // 新增：重置所有狀態\n  resetAllStates() {\n    // 重置數據\n    this.houseChangeDates = [];\n    this.convertedHouseArray = [];\n    this.buildingGroups = [];\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    // 重置篩選條件\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 重置選擇狀態\n    this.selectAll = false;\n    this.selectedBuilding = '';\n    // 重置分頁\n    this.currentPage = 1;\n    this.totalPages = 1;\n    // 重置可用選項\n    this.buildingOptions = [];\n    this.availableFloors = [];\n    // 重置排序\n    this.sortField = '';\n    this.sortDirection = 'asc';\n  }\n  getHouseChangeDate() {\n    // 如果沒有選擇建案，直接返回\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\n      this.loading = false;\n      return;\n    }\n    this.loading = true;\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      this.loading = false;\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          // 新增：建立棟別分組資料\n          this.buildBuildingGroups(res.Entries);\n          // 新增：建立扁平化資料\n          this.buildFlattenedHouses(res.Entries);\n        }\n      }\n    });\n  }\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n  buildBuildingGroups(data) {\n    const buildingMap = new Map();\n    data.forEach(household => {\n      const houseType = household.CHouseHold || ''; // 戶型\n      household.CHouses?.forEach(house => {\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\n        const floor = house.CFloor || 0;\n        if (!buildingMap.has(buildingName)) {\n          buildingMap.set(buildingName, new Map());\n        }\n        const floorMap = buildingMap.get(buildingName);\n        if (!floorMap.has(floor)) {\n          floorMap.set(floor, []);\n        }\n        floorMap.get(floor).push({\n          CHouseHold: houseType,\n          // 戶型\n          CBuildingName: buildingName,\n          // 棟別\n          CHouseId: house.CHouseId || 0,\n          CFloor: floor,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 轉換為BuildingGroup格式\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n      const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n      .map(([floorNumber, houses]) => ({\n        floorNumber,\n        houses: houses.sort((a, b) => {\n          // 排序邏輯：先按戶型排序，再按樓層排序\n          if (a.CHouseHold !== b.CHouseHold) {\n            return a.CHouseHold.localeCompare(b.CHouseHold);\n          }\n          return a.CFloor - b.CFloor;\n        }),\n        selected: false\n      }));\n      return {\n        name: buildingName,\n        floors,\n        selected: false\n      };\n    }).sort((a, b) => a.name.localeCompare(b.name));\n    // 更新棟別選項和可用樓層\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n    this.updateAvailableFloors();\n  }\n  // 新增：更新可用樓層\n  updateAvailableFloors() {\n    const floorsSet = new Set();\n    this.buildingGroups.forEach(building => {\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n        building.floors.forEach(floor => {\n          floorsSet.add(floor.floorNumber);\n        });\n      }\n    });\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n  }\n  // 修改：棟別選擇變更處理\n  onBuildingChange() {\n    // 重置選擇狀態\n    this.selectedHouses.forEach(house => house.selected = false);\n    this.selectedHouses = [];\n    this.selectAll = false;\n    // 重置分頁到第一頁\n    this.currentPage = 1;\n    // 重置樓層篩選\n    this.filterOptions.floorFilter = '';\n    // 更新可用樓層\n    this.updateAvailableFloors();\n    // 設定棟別篩選並執行搜尋\n    this.filterOptions.buildingFilter = this.selectedBuilding;\n    this.onSearch();\n  }\n  // 新增：取得過濾後的棟別資料\n  getFilteredBuildings() {\n    return this.buildingGroups.filter(building => {\n      // 棟別篩選\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n        return false;\n      }\n      // 關鍵字搜尋 (搜尋戶型)\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n        if (!hasMatchingHouse) {\n          return false;\n        }\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n        if (!hasMatchingStatus) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n        if (!hasMatchingFloor) {\n          return false;\n        }\n      }\n      return true;\n    }).map(building => {\n      // 對每個棟別，也要篩選其樓層和戶別\n      const filteredBuilding = {\n        ...building\n      };\n      filteredBuilding.floors = building.floors.filter(floor => {\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (floor.floorNumber !== floorNumber) {\n            return false;\n          }\n        }\n        // 檢查該樓層是否有符合條件的戶別\n        const hasValidHouses = floor.houses.some(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return hasValidHouses;\n      }).map(floor => {\n        // 篩選戶別\n        const filteredFloor = {\n          ...floor\n        };\n        filteredFloor.houses = floor.houses.filter(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return filteredFloor;\n      });\n      return filteredBuilding;\n    });\n  }\n  // 修復：檢查戶別是否符合狀態篩選\n  matchesStatusFilter(house) {\n    const status = this.getHouseStatus(house);\n    switch (this.filterOptions.statusFilter) {\n      case 'active':\n        return status === 'active';\n      case 'pending':\n        return status === 'pending';\n      case 'expired':\n        return status === 'expired';\n      case 'not-set':\n        return status === 'not-set';\n      case 'disabled':\n        return status === 'disabled';\n      default:\n        return true;\n      // 全部狀態\n    }\n  }\n  // 修復：取得戶別狀態\n  getHouseStatus(house) {\n    if (!house.CHouseId) {\n      return 'disabled';\n    }\n    // 檢查是否有設定開放時段\n    if (!house.CChangeStartDate || !house.CChangeEndDate || house.CChangeStartDate === '' || house.CChangeEndDate === '') {\n      return 'not-set';\n    }\n    try {\n      // 處理日期字串，支援多種格式\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      // 解析開始日期\n      let startDate;\n      if (house.CChangeStartDate.includes('T')) {\n        startDate = new Date(house.CChangeStartDate);\n      } else {\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\n      }\n      // 解析結束日期\n      let endDate;\n      if (house.CChangeEndDate.includes('T')) {\n        endDate = new Date(house.CChangeEndDate);\n      } else {\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\n      }\n      // 檢查日期有效性\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\n        console.warn('Invalid date format:', {\n          start: house.CChangeStartDate,\n          end: house.CChangeEndDate,\n          houseId: house.CHouseId\n        });\n        return 'not-set';\n      }\n      // 轉換為日期比較（不含時間）\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n      // 判斷狀態\n      if (today < startDateOnly) {\n        return 'pending'; // 待開放\n      } else if (today >= startDateOnly && today <= endDateOnly) {\n        return 'active'; // 進行中\n      } else {\n        return 'expired'; // 已過期\n      }\n    } catch (error) {\n      console.error('Error parsing dates:', error, house);\n      return 'not-set';\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  // 修改：開啟批次設定對話框\n  openBatchSetting(building) {\n    this.selectedBuildingForBatch = building || null;\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\n    const hasSelectedHouses = this.selectedHouses.length > 0;\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: !hasSelectedHouses && !building,\n      selectedBuildings: building ? [building.name] : [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    // 重置選擇狀態\n    if (building) {\n      building.floors.forEach(floor => {\n        floor.selected = false;\n        floor.houses.forEach(house => house.selected = false);\n      });\n    }\n    // 開啟對話框\n    this.dialogService.open(this.batchSettingDialog);\n  }\n  // 新增：樓層選擇變更處理\n  onFloorSelectionChange(floor) {\n    if (floor.selected) {\n      floor.houses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = true;\n        }\n      });\n    } else {\n      floor.houses.forEach(house => house.selected = false);\n    }\n  }\n  // 修改：批次提交\n  onBatchSubmit(ref) {\n    // 驗證批次設定\n    this.valid.clear();\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 收集要更新的房屋\n    const housesToUpdate = [];\n    if (this.batchSettings.applyToAll) {\n      // 全部戶別\n      this.flattenedHouses.forEach(house => {\n        if (house.CHouseId) {\n          housesToUpdate.push({\n            CHouseId: house.CHouseId,\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n          });\n        }\n      });\n    } else {\n      // 使用已選擇的戶別\n      if (this.selectedHouses.length > 0) {\n        this.selectedHouses.forEach(house => {\n          if (house.CHouseId) {\n            housesToUpdate.push({\n              CHouseId: house.CHouseId,\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n            });\n          }\n        });\n      } else if (this.selectedBuildingForBatch) {\n        // 如果沒有選擇的戶別，使用舊的邏輯\n        this.selectedBuildingForBatch.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.selected && house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      }\n    }\n    if (housesToUpdate.length === 0) {\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\n      return;\n    }\n    // 調用API進行批次更新\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: housesToUpdate\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n        // 清除選擇狀態\n        this.selectedHouses.forEach(house => house.selected = false);\n        this.selectedHouses = [];\n        this.selectAll = false;\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  // 新增：取得狀態樣式類別\n  getStatusClass(house) {\n    const status = this.getHouseStatus(house);\n    return `status-${status}`;\n  }\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n  openHouseDialog(house) {\n    if (house.CHouseId) {\n      // 使用現有的openModel方法\n      this.openModel(this.dialog, house);\n    }\n  }\n  // 新增：建立扁平化房屋資料\n  buildFlattenedHouses(data) {\n    this.flattenedHouses = [];\n    data.forEach(household => {\n      const houseType = household.CHouseHold || '';\n      household.CHouses?.forEach(house => {\n        this.flattenedHouses.push({\n          CHouseHold: houseType,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId || 0,\n          CFloor: house.CFloor || 0,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 初始化篩選和分頁\n    this.onSearch();\n    // 調試：輸出狀態統計\n    this.debugStatusCounts();\n  }\n  // 調試：輸出狀態統計\n  debugStatusCounts() {\n    const statusCounts = {\n      active: 0,\n      pending: 0,\n      expired: 0,\n      'not-set': 0,\n      disabled: 0\n    };\n    this.flattenedHouses.forEach(house => {\n      const status = this.getHouseStatus(house);\n      if (statusCounts.hasOwnProperty(status)) {\n        statusCounts[status]++;\n      }\n    });\n    console.log('狀態統計:', statusCounts);\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\n  }\n  // 修改：搜尋和篩選\n  onSearch() {\n    // 記錄篩選前的已選擇項目\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\n    this.filteredHouses = this.flattenedHouses.filter(house => {\n      // 關鍵字搜尋\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n          return false;\n        }\n      }\n      // 棟別篩選\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n        return false;\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        if (!this.matchesStatusFilter(house)) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        if (house.CFloor !== floorNumber) {\n          return false;\n        }\n      }\n      return true;\n    });\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\n    this.selectedHouses = this.filteredHouses.filter(house => previouslySelectedIds.includes(house.CHouseId));\n    // 更新扁平化資料中的選擇狀態\n    this.flattenedHouses.forEach(house => {\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\n    });\n    // 更新全選狀態\n    this.updateSelectAllState();\n    // 重新計算分頁\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：更新全選狀態\n  updateSelectAllState() {\n    if (this.paginatedHouses.length === 0) {\n      this.selectAll = false;\n    } else {\n      this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n    }\n  }\n  // 修改：更新分頁\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n    // 更新全選狀態\n    this.updateSelectAllState();\n  }\n  // 新增：頁面大小變更\n  onPageSizeChange() {\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：跳轉頁面\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.updatePagination();\n    }\n  }\n  // 新增：取得可見頁碼\n  getVisiblePages() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 新增：全選/取消全選\n  onSelectAllChange() {\n    this.paginatedHouses.forEach(house => {\n      if (house.CHouseId) {\n        house.selected = this.selectAll;\n      }\n    });\n    this.updateSelectedHouses();\n  }\n  // 新增：單一選擇變更\n  onHouseSelectionChange() {\n    this.updateSelectedHouses();\n    this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n  }\n  // 新增：更新已選擇房屋列表\n  updateSelectedHouses() {\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n  }\n  // 新增：排序\n  sort(field) {\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    this.filteredHouses.sort((a, b) => {\n      let aValue = a[field];\n      let bValue = b[field];\n      // 處理日期排序\n      if (field.includes('Date')) {\n        aValue = aValue ? new Date(aValue).getTime() : 0;\n        bValue = bValue ? new Date(bValue).getTime() : 0;\n      }\n      // 處理數字排序\n      if (field === 'CFloor') {\n        aValue = Number(aValue) || 0;\n        bValue = Number(bValue) || 0;\n      }\n      // 處理字串排序\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (aValue < bValue) {\n        return this.sortDirection === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return this.sortDirection === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    this.updatePagination();\n  }\n  // 新增：TrackBy函數\n  trackByHouseId(_index, house) {\n    return house.CHouseId;\n  }\n  // 新增：取得狀態文字\n  getStatusText(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return '進行中';\n      case 'pending':\n        return '待開放';\n      case 'expired':\n        return '已過期';\n      case 'not-set':\n        return '未設定';\n      case 'disabled':\n        return '已停用';\n      default:\n        return '未知';\n    }\n  }\n  static {\n    this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingTimePeriodComponent,\n      selectors: [[\"ngx-setting-time-period\"]],\n      viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 60,\n      vars: 12,\n      consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-md-7\", \"max-sm:col-md-12\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [1, \"form-check\", \"mx-2\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault1\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault1\", 1, \"form-check-label\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault2\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault2\", 1, \"form-check-label\"], [1, \"col-md-5\", \"max-sm:col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"text-center\"], [2, \"display\", \"block\", \"min-width\", \"100px\"], [\"style\", \"display: block; min-width: 100px\", 3, \"class\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"display\", \"block\", \"min-width\", \"100px\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"text-red-600\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n      template: function SettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 6)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 7);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 8)(7, \"div\", 9)(8, \"div\", 10)(9, \"label\", 11);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵtemplate(12, SettingTimePeriodComponent_nb_option_12_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 14)(15, \"label\", 15);\n          i0.ɵɵtext(16, \"\\u958B\\u653E\\u65E5\\u671F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-form-field\", 16);\n          i0.ɵɵelement(18, \"nb-icon\", 17);\n          i0.ɵɵelementStart(19, \"input\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"nb-datepicker\", 19, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"label\", 20);\n          i0.ɵɵtext(23, \" ~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"nb-form-field\");\n          i0.ɵɵelement(25, \"nb-icon\", 17);\n          i0.ɵɵelementStart(26, \"input\", 21);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_26_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"nb-datepicker\", 19, 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 22)(30, \"div\", 10)(31, \"label\", 23);\n          i0.ɵɵtext(32, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 24)(34, \"input\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"label\", 26);\n          i0.ɵɵtext(36, \" \\u4F9D\\u958B\\u653E\\u6642\\u6BB5\\u986F\\u793A \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 24)(38, \"input\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"label\", 28);\n          i0.ɵɵtext(40, \" \\u4F9D\\u958B\\u653E\\u72C0\\u614B\\u986F\\u793A \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 29)(42, \"div\", 30)(43, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_43_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵtext(44, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(45, \"i\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_46_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNavigateWithId());\n          });\n          i0.ɵɵtext(47, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(48, \"div\", 34)(49, \"table\", 35)(50, \"thead\")(51, \"tr\", 36);\n          i0.ɵɵelement(52, \"th\");\n          i0.ɵɵtemplate(53, SettingTimePeriodComponent_ng_container_53_Template, 3, 1, \"ng-container\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"tbody\");\n          i0.ɵɵtemplate(55, SettingTimePeriodComponent_tr_55_Template, 4, 2, \"tr\", 37);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(56, SettingTimePeriodComponent_ng_template_56_Template, 4, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(58, SettingTimePeriodComponent_ng_template_58_Template, 29, 6, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const StartDate_r13 = i0.ɵɵreference(21);\n          const EndDate_r14 = i0.ɵɵreference(28);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", StartDate_r13);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", EndDate_r14);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseChangeDates);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.convertedHouseArray);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, SharedModule, i9.DefaultValueAccessor, i9.RadioControlValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, NbDatepickerModule, NbDateFnsDateModule, SettingTimeStatusPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJzZXR0aW5nLXRpbWUtcGVyaW9kLmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvc2V0dGluZy10aW1lLXBlcmlvZC9zZXR0aW5nLXRpbWUtcGVyaW9kLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxvTEFBb0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "SettingTimeStatusPipe", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵelementContainerStart", "i_r3", "ɵɵlistener", "SettingTimePeriodComponent_tr_55_td_3_span_3_Template_span_click_0_listener", "ɵɵrestoreView", "_r4", "itm_r5", "ɵɵnextContext", "$implicit", "ctx_r5", "dialog_r7", "ɵɵreference", "ɵɵresetView", "openModel", "ɵɵclassMap", "CHouseId", "ɵɵpipeBind3", "CChangeStartDate", "CChangeEndDate", "isStatus", "ɵɵtemplate", "SettingTimePeriodComponent_tr_55_td_3_span_3_Template", "SettingTimePeriodComponent_tr_55_td_3_span_4_Template", "ɵɵtextInterpolate2", "CHouseHold", "CFloor", "SettingTimePeriodComponent_tr_55_td_3_Template", "item_r8", "ɵɵelement", "ɵɵtwoWayListener", "SettingTimePeriodComponent_ng_template_58_Template_input_ngModelChange_14_listener", "$event", "_r9", "ɵɵtwoWayBindingSet", "selectedHouseChangeDate", "SettingTimePeriodComponent_ng_template_58_Template_input_ngModelChange_21_listener", "SettingTimePeriodComponent_ng_template_58_Template_button_click_25_listener", "ref_r10", "dialogRef", "onClose", "SettingTimePeriodComponent_ng_template_58_Template_button_click_27_listener", "onSubmit", "changeStartDate_r11", "ɵɵtwoWayProperty", "changeEndDate_r12", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "buildingGroups", "buildingOptions", "selectedBuilding", "availableFloors", "filterOptions", "searchKeyword", "statusFilter", "floorFilter", "buildingFilter", "batchSettings", "startDate", "endDate", "applyToAll", "selectedBuildings", "selectedFloors", "selectedHouses", "selectedBuildingForBatch", "flattenedHouses", "filteredHouses", "paginatedHouses", "selectAll", "loading", "currentPage", "pageSize", "totalPages", "sortField", "sortDirection", "Math", "jumpToPage", "undefined", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "searchQuery", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "hasActiveFilters", "getActiveFiltersCount", "count", "resetFilters", "clearAllFilters", "onSearch", "setQuickFilter", "status", "clearSelection", "for<PERSON>ach", "house", "selected", "getStatusIcon", "getHouseStatus", "jumpToPageAction", "goToPage", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "validation", "errorMessages", "length", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "household", "CHouses", "floor", "push", "CBuildingName", "floors", "sort", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "onBuildCaseChange", "resetAllStates", "houseChangeDates", "convertedHouseArray", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "buildBuildingGroups", "buildFlattenedHouses", "data", "buildingMap", "Map", "houseType", "buildingName", "has", "set", "floorMap", "get", "floorNumber", "houses", "localeCompare", "name", "bg", "updateAvailableFloors", "building", "onBuildingChange", "getFilteredBuildings", "filter", "keyword", "toLowerCase", "hasMatchingHouse", "some", "includes", "hasMatchingStatus", "matchesStatusFilter", "parseInt", "hasMatchingFloor", "filteredBuilding", "hasValidHouses", "filteredFloor", "now", "today", "getFullYear", "getMonth", "getDate", "isNaN", "getTime", "console", "warn", "start", "end", "houseId", "startDateOnly", "endDateOnly", "error", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "openBatchSetting", "hasSelectedHouses", "batchSettingDialog", "onFloorSelectionChange", "onBatchSubmit", "housesToUpdate", "getStatusClass", "openHouseDialog", "dialog", "debugStatusCounts", "statusCounts", "active", "pending", "expired", "disabled", "hasOwnProperty", "log", "toISOString", "split", "previouslySelectedIds", "updateSelectAllState", "updatePagination", "every", "ceil", "startIndex", "endIndex", "slice", "onPageSizeChange", "page", "getVisiblePages", "pages", "maxVisible", "max", "min", "i", "onSelectAllChange", "updateSelectedHouses", "onHouseSelectionChange", "field", "aValue", "bValue", "Number", "trackByHouseId", "_index", "getStatusText", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "Router", "i7", "EventService", "selectors", "viewQuery", "SettingTimePeriodComponent_Query", "rf", "ctx", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingTimePeriodComponent_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "SettingTimePeriodComponent_nb_option_12_Template", "SettingTimePeriodComponent_Template_input_ngModelChange_19_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_26_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_34_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_38_listener", "SettingTimePeriodComponent_Template_button_click_43_listener", "SettingTimePeriodComponent_Template_button_click_46_listener", "SettingTimePeriodComponent_ng_container_53_Template", "SettingTimePeriodComponent_tr_55_Template", "SettingTimePeriodComponent_ng_template_56_Template", "ɵɵtemplateRefExtractor", "SettingTimePeriodComponent_ng_template_58_Template", "StartDate_r13", "EndDate_r14", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "RadioControlValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\n\r\n\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string; // 戶型\r\n  CBuildingName: string; // 棟別\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n\r\n  @ViewChild('batchSettingDialog') batchSettingDialog!: TemplateRef<any>;\r\n  @ViewChild('dialog') dialog!: TemplateRef<any>;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  // 新增的屬性\r\n  buildingGroups: BuildingGroup[] = [];\r\n  buildingOptions: string[] = [];\r\n  selectedBuilding: string = '';\r\n  availableFloors: number[] = [];\r\n\r\n  // 篩選和搜尋\r\n  filterOptions: FilterOptions = {\r\n    searchKeyword: '',\r\n    statusFilter: '',\r\n    floorFilter: '',\r\n    buildingFilter: ''\r\n  };\r\n\r\n  // 批次設定\r\n  batchSettings: BatchSettings = {\r\n    startDate: null,\r\n    endDate: null,\r\n    applyToAll: true,\r\n    selectedBuildings: [],\r\n    selectedFloors: [],\r\n    selectedHouses: []\r\n  };\r\n\r\n  selectedBuildingForBatch: BuildingGroup | null = null;\r\n\r\n  // 表格視圖相關屬性\r\n  flattenedHouses: ConvertedHouse[] = [];\r\n  filteredHouses: ConvertedHouse[] = [];\r\n  paginatedHouses: ConvertedHouse[] = [];\r\n  selectedHouses: ConvertedHouse[] = [];\r\n  selectAll: boolean = false;\r\n  loading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  override pageSize: number = 50;\r\n  totalPages: number = 1;\r\n\r\n  // 排序相關\r\n  sortField: string = '';\r\n  sortDirection: 'asc' | 'desc' = 'asc';\r\n\r\n  // 數學函數引用\r\n  Math = Math;\r\n\r\n  // 新增的UI控制屬性\r\n  jumpToPage: number = 1;\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  // 新增的UI控制方法\r\n  hasActiveFilters(): boolean {\r\n    return !!(this.filterOptions.searchKeyword ||\r\n      this.filterOptions.statusFilter ||\r\n      this.filterOptions.floorFilter);\r\n  }\r\n\r\n  getActiveFiltersCount(): number {\r\n    let count = 0;\r\n    if (this.filterOptions.searchKeyword) count++;\r\n    if (this.filterOptions.statusFilter) count++;\r\n    if (this.filterOptions.floorFilter) count++;\r\n    return count;\r\n  }\r\n\r\n  resetFilters(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    };\r\n    this.selectedBuilding = '';\r\n    this.clearAllFilters();\r\n  }\r\n\r\n  clearAllFilters(): void {\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n    this.onSearch();\r\n  }\r\n\r\n  setQuickFilter(status: string): void {\r\n    if (this.filterOptions.statusFilter === status) {\r\n      this.filterOptions.statusFilter = '';\r\n    } else {\r\n      this.filterOptions.statusFilter = status;\r\n    }\r\n    this.onSearch();\r\n  }\r\n\r\n  clearSelection(): void {\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n    this.flattenedHouses.forEach(house => house.selected = false);\r\n  }\r\n\r\n  getStatusIcon(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    switch (status) {\r\n      case 'active': return 'fas fa-play-circle';\r\n      case 'pending': return 'fas fa-clock';\r\n      case 'expired': return 'fas fa-times-circle';\r\n      case 'not-set': return 'fas fa-exclamation-triangle';\r\n      case 'disabled': return 'fas fa-ban';\r\n      default: return 'fas fa-exclamation-triangle';\r\n    }\r\n  }\r\n\r\n  jumpToPageAction(): void {\r\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\r\n      this.goToPage(this.jumpToPage);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CBuildingName: '未分類',\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：建案變更處理\r\n  onBuildCaseChange() {\r\n    // 重置所有相關狀態\r\n    this.resetAllStates();\r\n    // 執行查詢\r\n    this.getHouseChangeDate();\r\n  }\r\n\r\n  // 新增：重置所有狀態\r\n  resetAllStates() {\r\n    // 重置數據\r\n    this.houseChangeDates = [];\r\n    this.convertedHouseArray = [];\r\n    this.buildingGroups = [];\r\n    this.flattenedHouses = [];\r\n    this.filteredHouses = [];\r\n    this.paginatedHouses = [];\r\n    this.selectedHouses = [];\r\n\r\n    // 重置篩選條件\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    this.selectAll = false;\r\n    this.selectedBuilding = '';\r\n\r\n    // 重置分頁\r\n    this.currentPage = 1;\r\n    this.totalPages = 1;\r\n\r\n    // 重置可用選項\r\n    this.buildingOptions = [];\r\n    this.availableFloors = [];\r\n\r\n    // 重置排序\r\n    this.sortField = '';\r\n    this.sortDirection = 'asc';\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    // 如果沒有選擇建案，直接返回\r\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      this.loading = false;\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n          // 新增：建立棟別分組資料\r\n          this.buildBuildingGroups(res.Entries)\r\n          // 新增：建立扁平化資料\r\n          this.buildFlattenedHouses(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\r\n  buildBuildingGroups(data: GetHouseChangeDateRes[]) {\r\n    const buildingMap = new Map<string, Map<number, ConvertedHouse[]>>();\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || ''; // 戶型\r\n\r\n      household.CHouses?.forEach(house => {\r\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\r\n        const floor = house.CFloor || 0;\r\n\r\n        if (!buildingMap.has(buildingName)) {\r\n          buildingMap.set(buildingName, new Map<number, ConvertedHouse[]>());\r\n        }\r\n\r\n        const floorMap = buildingMap.get(buildingName)!;\r\n\r\n        if (!floorMap.has(floor)) {\r\n          floorMap.set(floor, []);\r\n        }\r\n\r\n        floorMap.get(floor)!.push({\r\n          CHouseHold: houseType, // 戶型\r\n          CBuildingName: buildingName, // 棟別\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: floor,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 轉換為BuildingGroup格式\r\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\r\n      const floors: FloorGroup[] = Array.from(floorMap.entries())\r\n        .sort(([a], [b]) => b - a) // 樓層由高到低排序\r\n        .map(([floorNumber, houses]) => ({\r\n          floorNumber,\r\n          houses: houses.sort((a, b) => {\r\n            // 排序邏輯：先按戶型排序，再按樓層排序\r\n            if (a.CHouseHold !== b.CHouseHold) {\r\n              return a.CHouseHold.localeCompare(b.CHouseHold);\r\n            }\r\n            return a.CFloor - b.CFloor;\r\n          }),\r\n          selected: false\r\n        }));\r\n\r\n      return {\r\n        name: buildingName,\r\n        floors,\r\n        selected: false\r\n      };\r\n    }).sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    // 更新棟別選項和可用樓層\r\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\r\n    this.updateAvailableFloors();\r\n  }\r\n\r\n  // 新增：更新可用樓層\r\n  updateAvailableFloors() {\r\n    const floorsSet = new Set<number>();\r\n\r\n    this.buildingGroups.forEach(building => {\r\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\r\n        building.floors.forEach(floor => {\r\n          floorsSet.add(floor.floorNumber);\r\n        });\r\n      }\r\n    });\r\n\r\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\r\n  }\r\n\r\n  // 修改：棟別選擇變更處理\r\n  onBuildingChange() {\r\n    // 重置選擇狀態\r\n    this.selectedHouses.forEach(house => house.selected = false);\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n\r\n    // 重置分頁到第一頁\r\n    this.currentPage = 1;\r\n\r\n    // 重置樓層篩選\r\n    this.filterOptions.floorFilter = '';\r\n\r\n    // 更新可用樓層\r\n    this.updateAvailableFloors();\r\n\r\n    // 設定棟別篩選並執行搜尋\r\n    this.filterOptions.buildingFilter = this.selectedBuilding;\r\n    this.onSearch();\r\n  }\r\n\r\n\r\n\r\n  // 新增：取得過濾後的棟別資料\r\n  getFilteredBuildings(): BuildingGroup[] {\r\n    return this.buildingGroups.filter(building => {\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 關鍵字搜尋 (搜尋戶型)\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        const hasMatchingHouse = building.floors.some(floor =>\r\n          floor.houses.some(house =>\r\n            house.CHouseHold.toLowerCase().includes(keyword) ||\r\n            house.CBuildingName.toLowerCase().includes(keyword)\r\n          )\r\n        );\r\n        if (!hasMatchingHouse) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        const hasMatchingStatus = building.floors.some(floor =>\r\n          floor.houses.some(house => this.matchesStatusFilter(house))\r\n        );\r\n        if (!hasMatchingStatus) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        const hasMatchingFloor = building.floors.some(floor =>\r\n          floor.floorNumber === floorNumber\r\n        );\r\n        if (!hasMatchingFloor) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }).map(building => {\r\n      // 對每個棟別，也要篩選其樓層和戶別\r\n      const filteredBuilding = { ...building };\r\n      filteredBuilding.floors = building.floors.filter(floor => {\r\n        // 樓層篩選\r\n        if (this.filterOptions.floorFilter) {\r\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n          if (floor.floorNumber !== floorNumber) {\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 檢查該樓層是否有符合條件的戶別\r\n        const hasValidHouses = floor.houses.some(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return hasValidHouses;\r\n      }).map(floor => {\r\n        // 篩選戶別\r\n        const filteredFloor = { ...floor };\r\n        filteredFloor.houses = floor.houses.filter(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return filteredFloor;\r\n      });\r\n\r\n      return filteredBuilding;\r\n    });\r\n  }\r\n\r\n  // 修復：檢查戶別是否符合狀態篩選\r\n  private matchesStatusFilter(house: ConvertedHouse): boolean {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (this.filterOptions.statusFilter) {\r\n      case 'active':\r\n        return status === 'active';\r\n      case 'pending':\r\n        return status === 'pending';\r\n      case 'expired':\r\n        return status === 'expired';\r\n      case 'not-set':\r\n        return status === 'not-set';\r\n      case 'disabled':\r\n        return status === 'disabled';\r\n      default:\r\n        return true; // 全部狀態\r\n    }\r\n  }\r\n\r\n  // 修復：取得戶別狀態\r\n  private getHouseStatus(house: ConvertedHouse): string {\r\n    if (!house.CHouseId) {\r\n      return 'disabled';\r\n    }\r\n\r\n    // 檢查是否有設定開放時段\r\n    if (!house.CChangeStartDate || !house.CChangeEndDate ||\r\n      house.CChangeStartDate === '' || house.CChangeEndDate === '') {\r\n      return 'not-set';\r\n    }\r\n\r\n    try {\r\n      // 處理日期字串，支援多種格式\r\n      const now = new Date();\r\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n\r\n      // 解析開始日期\r\n      let startDate: Date;\r\n      if (house.CChangeStartDate.includes('T')) {\r\n        startDate = new Date(house.CChangeStartDate);\r\n      } else {\r\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\r\n      }\r\n\r\n      // 解析結束日期\r\n      let endDate: Date;\r\n      if (house.CChangeEndDate.includes('T')) {\r\n        endDate = new Date(house.CChangeEndDate);\r\n      } else {\r\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\r\n      }\r\n\r\n      // 檢查日期有效性\r\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\r\n        console.warn('Invalid date format:', {\r\n          start: house.CChangeStartDate,\r\n          end: house.CChangeEndDate,\r\n          houseId: house.CHouseId\r\n        });\r\n        return 'not-set';\r\n      }\r\n\r\n      // 轉換為日期比較（不含時間）\r\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\r\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\r\n\r\n      // 判斷狀態\r\n      if (today < startDateOnly) {\r\n        return 'pending'; // 待開放\r\n      } else if (today >= startDateOnly && today <= endDateOnly) {\r\n        return 'active'; // 進行中\r\n      } else {\r\n        return 'expired'; // 已過期\r\n      }\r\n    } catch (error) {\r\n      console.error('Error parsing dates:', error, house);\r\n      return 'not-set';\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n\r\n  // 修改：開啟批次設定對話框\r\n  openBatchSetting(building?: BuildingGroup) {\r\n    this.selectedBuildingForBatch = building || null;\r\n\r\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\r\n    const hasSelectedHouses = this.selectedHouses.length > 0;\r\n\r\n    this.batchSettings = {\r\n      startDate: null,\r\n      endDate: null,\r\n      applyToAll: !hasSelectedHouses && !building,\r\n      selectedBuildings: building ? [building.name] : [],\r\n      selectedFloors: [],\r\n      selectedHouses: []\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    if (building) {\r\n      building.floors.forEach(floor => {\r\n        floor.selected = false;\r\n        floor.houses.forEach(house => house.selected = false);\r\n      });\r\n    }\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(this.batchSettingDialog);\r\n  }\r\n\r\n  // 新增：樓層選擇變更處理\r\n  onFloorSelectionChange(floor: FloorGroup) {\r\n    if (floor.selected) {\r\n      floor.houses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          house.selected = true;\r\n        }\r\n      });\r\n    } else {\r\n      floor.houses.forEach(house => house.selected = false);\r\n    }\r\n  }\r\n\r\n  // 修改：批次提交\r\n  onBatchSubmit(ref: any) {\r\n    // 驗證批次設定\r\n    this.valid.clear();\r\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\r\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 收集要更新的房屋\r\n    const housesToUpdate: any[] = [];\r\n\r\n    if (this.batchSettings.applyToAll) {\r\n      // 全部戶別\r\n      this.flattenedHouses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          housesToUpdate.push({\r\n            CHouseId: house.CHouseId,\r\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // 使用已選擇的戶別\r\n      if (this.selectedHouses.length > 0) {\r\n        this.selectedHouses.forEach(house => {\r\n          if (house.CHouseId) {\r\n            housesToUpdate.push({\r\n              CHouseId: house.CHouseId,\r\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n            });\r\n          }\r\n        });\r\n      } else if (this.selectedBuildingForBatch) {\r\n        // 如果沒有選擇的戶別，使用舊的邏輯\r\n        this.selectedBuildingForBatch.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.selected && house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n\r\n    if (housesToUpdate.length === 0) {\r\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\r\n      return;\r\n    }\r\n\r\n    // 調用API進行批次更新\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: housesToUpdate\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\r\n        // 清除選擇狀態\r\n        this.selectedHouses.forEach(house => house.selected = false);\r\n        this.selectedHouses = [];\r\n        this.selectAll = false;\r\n        this.getHouseChangeDate();\r\n        ref.close();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：取得狀態樣式類別\r\n  getStatusClass(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    return `status-${status}`;\r\n  }\r\n\r\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\r\n  openHouseDialog(house: ConvertedHouse) {\r\n    if (house.CHouseId) {\r\n      // 使用現有的openModel方法\r\n      this.openModel(this.dialog, house);\r\n    }\r\n  }\r\n\r\n  // 新增：建立扁平化房屋資料\r\n  buildFlattenedHouses(data: GetHouseChangeDateRes[]) {\r\n    this.flattenedHouses = [];\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || '';\r\n\r\n      household.CHouses?.forEach(house => {\r\n        this.flattenedHouses.push({\r\n          CHouseHold: houseType,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: house.CFloor || 0,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 初始化篩選和分頁\r\n    this.onSearch();\r\n\r\n    // 調試：輸出狀態統計\r\n    this.debugStatusCounts();\r\n  }\r\n\r\n  // 調試：輸出狀態統計\r\n  private debugStatusCounts() {\r\n    const statusCounts = {\r\n      active: 0,\r\n      pending: 0,\r\n      expired: 0,\r\n      'not-set': 0,\r\n      disabled: 0\r\n    };\r\n\r\n    this.flattenedHouses.forEach(house => {\r\n      const status = this.getHouseStatus(house);\r\n      if (statusCounts.hasOwnProperty(status)) {\r\n        statusCounts[status as keyof typeof statusCounts]++;\r\n      }\r\n    });\r\n\r\n    console.log('狀態統計:', statusCounts);\r\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\r\n  }\r\n\r\n\r\n\r\n  // 修改：搜尋和篩選\r\n  onSearch() {\r\n    // 記錄篩選前的已選擇項目\r\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\r\n\r\n    this.filteredHouses = this.flattenedHouses.filter(house => {\r\n      // 關鍵字搜尋\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n          !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        if (!this.matchesStatusFilter(house)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        if (house.CFloor !== floorNumber) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    });\r\n\r\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\r\n    this.selectedHouses = this.filteredHouses.filter(house =>\r\n      previouslySelectedIds.includes(house.CHouseId)\r\n    );\r\n\r\n    // 更新扁平化資料中的選擇狀態\r\n    this.flattenedHouses.forEach(house => {\r\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\r\n    });\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n\r\n    // 重新計算分頁\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：更新全選狀態\r\n  private updateSelectAllState() {\r\n    if (this.paginatedHouses.length === 0) {\r\n      this.selectAll = false;\r\n    } else {\r\n      this.selectAll = this.paginatedHouses.every(house =>\r\n        !house.CHouseId || house.selected\r\n      );\r\n    }\r\n  }\r\n\r\n  // 修改：更新分頁\r\n  updatePagination() {\r\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\r\n    const startIndex = (this.currentPage - 1) * this.pageSize;\r\n    const endIndex = startIndex + this.pageSize;\r\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 新增：頁面大小變更\r\n  onPageSizeChange() {\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：跳轉頁面\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  // 新增：取得可見頁碼\r\n  getVisiblePages(): number[] {\r\n    const pages: number[] = [];\r\n    const maxVisible = 5;\r\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\r\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\r\n\r\n    if (end - start + 1 < maxVisible) {\r\n      start = Math.max(1, end - maxVisible + 1);\r\n    }\r\n\r\n    for (let i = start; i <= end; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 新增：全選/取消全選\r\n  onSelectAllChange() {\r\n    this.paginatedHouses.forEach(house => {\r\n      if (house.CHouseId) {\r\n        house.selected = this.selectAll;\r\n      }\r\n    });\r\n    this.updateSelectedHouses();\r\n  }\r\n\r\n  // 新增：單一選擇變更\r\n  onHouseSelectionChange() {\r\n    this.updateSelectedHouses();\r\n    this.selectAll = this.paginatedHouses.every(house =>\r\n      !house.CHouseId || house.selected\r\n    );\r\n  }\r\n\r\n  // 新增：更新已選擇房屋列表\r\n  updateSelectedHouses() {\r\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\r\n  }\r\n\r\n  // 新增：排序\r\n  sort(field: string) {\r\n    if (this.sortField === field) {\r\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortDirection = 'asc';\r\n    }\r\n\r\n    this.filteredHouses.sort((a, b) => {\r\n      let aValue = (a as any)[field];\r\n      let bValue = (b as any)[field];\r\n\r\n      // 處理日期排序\r\n      if (field.includes('Date')) {\r\n        aValue = aValue ? new Date(aValue).getTime() : 0;\r\n        bValue = bValue ? new Date(bValue).getTime() : 0;\r\n      }\r\n\r\n      // 處理數字排序\r\n      if (field === 'CFloor') {\r\n        aValue = Number(aValue) || 0;\r\n        bValue = Number(bValue) || 0;\r\n      }\r\n\r\n      // 處理字串排序\r\n      if (typeof aValue === 'string') {\r\n        aValue = aValue.toLowerCase();\r\n        bValue = bValue.toLowerCase();\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return this.sortDirection === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return this.sortDirection === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：TrackBy函數\r\n  trackByHouseId(_index: number, house: ConvertedHouse): number {\r\n    return house.CHouseId;\r\n  }\r\n\r\n  // 新增：取得狀態文字\r\n  getStatusText(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (status) {\r\n      case 'active':\r\n        return '進行中';\r\n      case 'pending':\r\n        return '待開放';\r\n      case 'expired':\r\n        return '已過期';\r\n      case 'not-set':\r\n        return '未設定';\r\n      case 'disabled':\r\n        return '已停用';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"getHouseChangeDate()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">開放日期\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"StartDate\"\r\n              class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n            <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\"> ~\r\n          </label>\r\n          <nb-form-field>\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"EndDate\" class=\"w-full col-4\"\r\n              [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n            <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-7 max-sm:col-md-12\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cSignStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <div class=\"form-check mx-2\">\r\n            <input class=\"form-check-input\" type=\"radio\" name=\"flexRadioDefault\" id=\"flexRadioDefault1\" [value]=\"true\"\r\n              [(ngModel)]=\"isStatus\">\r\n            <label class=\"form-check-label\" for=\"flexRadioDefault1\">\r\n              依開放時段顯示\r\n            </label>\r\n          </div>\r\n          <div class=\"form-check mx-2\">\r\n            <input class=\"form-check-input\" type=\"radio\" name=\"flexRadioDefault\" id=\"flexRadioDefault2\" [value]=\"false\"\r\n              [(ngModel)]=\"isStatus\">\r\n            <label class=\"form-check-label\" for=\"flexRadioDefault2\">\r\n              依開放狀態顯示\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-5 max-sm:col-md-12 \">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"getHouseChangeDate()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-info btn-sm\" (click)=\"onNavigateWithId()\">\r\n            批次設定\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th></th>\r\n            <ng-container *ngFor=\"let item of houseChangeDates ; let i = index\">\r\n              <th> {{i+1}} </th>\r\n            </ng-container>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of convertedHouseArray ; let i = index\">\r\n            <td class=\"text-center\">{{ item[0].CFloor}}F</td>\r\n            <td *ngFor=\"let itm of item ; let i = index\">\r\n              <span style=\"display: block; min-width: 100px\">{{itm.CHouseHold}}-{{itm.CFloor}}F</span>\r\n              <span (click)=\"openModel(dialog, itm)\" *ngIf=\"itm.CHouseId\"\r\n                [class]=\"itm.CHouseId ? 'cursor-pointer text-blue-800 block min-w-[100px]' : ''\"\r\n                style=\"display: block; min-width: 100px\">\r\n                {{ itm.CChangeStartDate | getSettingTimeStatus:itm.CChangeEndDate: isStatus }}\r\n              </span>\r\n              <span *ngIf=\"!itm.CHouseId\">已停用</span>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      您正在編輯 <span class=\"text-green-600\">{{selectedHouseChangeDate.CHouseHold}} -\r\n        {{selectedHouseChangeDate.CFloor}}F</span> 的選樣開放時段\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cFloorFrom\" class=\"label col-3\">開放時間起訖<span class=\"text-red-600\">*</span>\r\n        </label>\r\n        <nb-form-field class=\"ml-3\">\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeStartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeStartDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeStartDate\">\r\n          <nb-datepicker #changeStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n        <label for=\"CChangeStartDate\" class=\"label col-1\"> ~\r\n        </label>\r\n        <nb-form-field>\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeEndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeEndDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeEndDate\">\r\n          <nb-datepicker #changeEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AAQvD,SAASC,qBAAqB,QAAQ,mCAAmC;AAEzE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;;;ICLxEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IA+DAR,EAAA,CAAAS,uBAAA,GAAoE;IAClET,EAAA,CAAAC,cAAA,SAAI;IAACD,EAAA,CAAAE,MAAA,GAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAAbH,EAAA,CAAAM,SAAA,GAAQ;IAARN,EAAA,CAAAO,kBAAA,MAAAG,IAAA,UAAQ;;;;;;IASbV,EAAA,CAAAC,cAAA,eAE2C;IAFrCD,EAAA,CAAAW,UAAA,mBAAAC,4EAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,MAAAG,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASH,MAAA,CAAAI,SAAA,CAAAH,SAAA,EAAAJ,MAAA,CAAsB;IAAA,EAAC;IAGpCf,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHLH,EAAA,CAAAuB,UAAA,CAAAR,MAAA,CAAAS,QAAA,2DAAgF;IAEhFxB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAyB,WAAA,OAAAV,MAAA,CAAAW,gBAAA,EAAAX,MAAA,CAAAY,cAAA,EAAAT,MAAA,CAAAU,QAAA,OACF;;;;;IACA5B,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IANtCH,EADF,CAAAC,cAAA,SAA6C,eACI;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAMxFH,EALA,CAAA6B,UAAA,IAAAC,qDAAA,mBAE2C,IAAAC,qDAAA,mBAGf;IAC9B/B,EAAA,CAAAG,YAAA,EAAK;;;;IAP4CH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAgC,kBAAA,KAAAjB,MAAA,CAAAkB,UAAA,OAAAlB,MAAA,CAAAmB,MAAA,MAAkC;IACzClC,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAS,QAAA,CAAkB;IAKnDxB,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAI,UAAA,UAAAW,MAAA,CAAAS,QAAA,CAAmB;;;;;IAR5BxB,EADF,CAAAC,cAAA,SAA6D,aACnC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAA6B,UAAA,IAAAM,8CAAA,iBAA6C;IAS/CnC,EAAA,CAAAG,YAAA,EAAK;;;;IAVqBH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAO,kBAAA,KAAA6B,OAAA,IAAAF,MAAA,MAAoB;IACxBlC,EAAA,CAAAM,SAAA,EAAU;IAAVN,EAAA,CAAAI,UAAA,YAAAgC,OAAA,CAAU;;;;;IAiBxCpC,EAAA,CAAAC,cAAA,kBAA+C;IAM7CD,EALA,CAAAqC,SAAA,qBACiB,mBAGF,yBAGE;IACnBrC,EAAA,CAAAG,YAAA,EAAU;;;;;;IAKRH,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GACE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mDAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,uBAA2B,cACyB,gBACJ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC7E;IACRH,EAAA,CAAAC,cAAA,yBAA4B;IAC1BD,EAAA,CAAAqC,SAAA,mBAAoD;IACpDrC,EAAA,CAAAC,cAAA,iBAC8E;IAAvDD,EAAA,CAAAsC,gBAAA,2BAAAC,mFAAAC,MAAA;MAAAxC,EAAA,CAAAa,aAAA,CAAA4B,GAAA;MAAA,MAAAvB,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAA0C,kBAAA,CAAAxB,MAAA,CAAAyB,uBAAA,CAAAjB,gBAAA,EAAAc,MAAA,MAAAtB,MAAA,CAAAyB,uBAAA,CAAAjB,gBAAA,GAAAc,MAAA;MAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;IAAA,EAAsD;IAD7ExC,EAAA,CAAAG,YAAA,EAC8E;IAC9EH,EAAA,CAAAqC,SAAA,4BAAoE;IACtErC,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,iBAAkD;IAACD,EAAA,CAAAE,MAAA,WACnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAqC,SAAA,mBAAoD;IACpDrC,EAAA,CAAAC,cAAA,iBAC4E;IAArDD,EAAA,CAAAsC,gBAAA,2BAAAM,mFAAAJ,MAAA;MAAAxC,EAAA,CAAAa,aAAA,CAAA4B,GAAA;MAAA,MAAAvB,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAA0C,kBAAA,CAAAxB,MAAA,CAAAyB,uBAAA,CAAAhB,cAAA,EAAAa,MAAA,MAAAtB,MAAA,CAAAyB,uBAAA,CAAAhB,cAAA,GAAAa,MAAA;MAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;IAAA,EAAoD;IAD3ExC,EAAA,CAAAG,YAAA,EAC4E;IAC5EH,EAAA,CAAAqC,SAAA,4BAAkE;IAGxErC,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACiB;IAAvBD,EAAA,CAAAW,UAAA,mBAAAkC,4EAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAAa,aAAA,CAAA4B,GAAA,EAAAM,SAAA;MAAA,MAAA7B,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAqB,WAAA,CAASH,MAAA,CAAA8B,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAAC9C,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,kBAAoE;IAAxBD,EAAA,CAAAW,UAAA,mBAAAsC,4EAAA;MAAA,MAAAH,OAAA,GAAA9C,EAAA,CAAAa,aAAA,CAAA4B,GAAA,EAAAM,SAAA;MAAA,MAAA7B,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAqB,WAAA,CAASH,MAAA,CAAAgC,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAAC9C,EAAA,CAAAE,MAAA,oBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAChE,EACT;;;;;;IA3B6BH,EAAA,CAAAM,SAAA,GACE;IADFN,EAAA,CAAAgC,kBAAA,KAAAd,MAAA,CAAAyB,uBAAA,CAAAV,UAAA,SAAAf,MAAA,CAAAyB,uBAAA,CAAAT,MAAA,MACE;IAQoClC,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,iBAAA+C,mBAAA,CAAgC;IAC9EnD,EAAA,CAAAoD,gBAAA,YAAAlC,MAAA,CAAAyB,uBAAA,CAAAjB,gBAAA,CAAsD;IAOV1B,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAI,UAAA,iBAAAiD,iBAAA,CAA8B;IAC1ErD,EAAA,CAAAoD,gBAAA,YAAAlC,MAAA,CAAAyB,uBAAA,CAAAhB,cAAA,CAAoD;;;ADxDrF,OAAM,MAAO2B,0BAA2B,SAAQxD,aAAa;EAK3DyD,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA6C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAUtD;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,eAAe,GAAa,EAAE;IAE9B;IACA,KAAAC,aAAa,GAAkB;MAC7BC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE;KACjB;IAED;IACA,KAAAC,aAAa,GAAkB;MAC7BC,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;KACjB;IAED,KAAAC,wBAAwB,GAAyB,IAAI;IAErD;IACA,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAC,cAAc,GAAqB,EAAE;IACrC,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAJ,cAAc,GAAqB,EAAE;IACrC,KAAAK,SAAS,GAAY,KAAK;IAC1B,KAAAC,OAAO,GAAY,KAAK;IAExB;IACA,KAAAC,WAAW,GAAW,CAAC;IACd,KAAAC,QAAQ,GAAW,EAAE;IAC9B,KAAAC,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAmB,KAAK;IAErC;IACA,KAAAC,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAC,UAAU,GAAW,CAAC;IArFpB,IAAI,CAACrD,uBAAuB,GAAG;MAC7BjB,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBO,MAAM,EAAE+D,SAAS;MACjBhE,UAAU,EAAE,EAAE;MACdT,QAAQ,EAAEyE;KACX;IAED,IAAI,CAAClC,aAAa,CAACmC,OAAO,EAAE,CAACC,IAAI,CAC/B3G,GAAG,CAAE4G,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAACrC,eAAe,GAAGoC,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAwESC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAAC1C,gBAAgB,CAAC,CAAC,CAAC;MAC/CvC,gBAAgB,EAAEuE,SAAS;MAC3BtE,cAAc,EAAEsE;KACjB;IACD,IAAI,CAACW,gBAAgB,EAAE;EACzB;EAEA;EACAC,gBAAgBA,CAAA;IACd,OAAO,CAAC,EAAE,IAAI,CAACrC,aAAa,CAACC,aAAa,IACxC,IAAI,CAACD,aAAa,CAACE,YAAY,IAC/B,IAAI,CAACF,aAAa,CAACG,WAAW,CAAC;EACnC;EAEAmC,qBAAqBA,CAAA;IACnB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAACvC,aAAa,CAACC,aAAa,EAAEsC,KAAK,EAAE;IAC7C,IAAI,IAAI,CAACvC,aAAa,CAACE,YAAY,EAAEqC,KAAK,EAAE;IAC5C,IAAI,IAAI,CAACvC,aAAa,CAACG,WAAW,EAAEoC,KAAK,EAAE;IAC3C,OAAOA,KAAK;EACd;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACP,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvDC,qBAAqB,EAAE,IAAI,CAAC1C,gBAAgB,CAAC,CAAC,CAAC;MAC/CvC,gBAAgB,EAAEuE,SAAS;MAC3BtE,cAAc,EAAEsE;KACjB;IACD,IAAI,CAAC3B,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC2C,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACzC,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE;KACjB;IACD,IAAI,CAACsC,QAAQ,EAAE;EACjB;EAEAC,cAAcA,CAACC,MAAc;IAC3B,IAAI,IAAI,CAAC5C,aAAa,CAACE,YAAY,KAAK0C,MAAM,EAAE;MAC9C,IAAI,CAAC5C,aAAa,CAACE,YAAY,GAAG,EAAE;IACtC,CAAC,MAAM;MACL,IAAI,CAACF,aAAa,CAACE,YAAY,GAAG0C,MAAM;IAC1C;IACA,IAAI,CAACF,QAAQ,EAAE;EACjB;EAEAG,cAAcA,CAAA;IACZ,IAAI,CAAClC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACK,SAAS,GAAG,KAAK;IACtB,IAAI,CAACH,eAAe,CAACiC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,KAAK,CAAC;EAC/D;EAEAC,aAAaA,CAACF,KAAqB;IACjC,MAAMH,MAAM,GAAG,IAAI,CAACM,cAAc,CAACH,KAAK,CAAC;IACzC,QAAQH,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,oBAAoB;MAC1C,KAAK,SAAS;QAAE,OAAO,cAAc;MACrC,KAAK,SAAS;QAAE,OAAO,qBAAqB;MAC5C,KAAK,SAAS;QAAE,OAAO,6BAA6B;MACpD,KAAK,UAAU;QAAE,OAAO,YAAY;MACpC;QAAS,OAAO,6BAA6B;IAC/C;EACF;EAEAO,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC3B,UAAU,IAAI,IAAI,CAACA,UAAU,IAAI,CAAC,IAAI,IAAI,CAACA,UAAU,IAAI,IAAI,CAACJ,UAAU,EAAE;MACjF,IAAI,CAACgC,QAAQ,CAAC,IAAI,CAAC5B,UAAU,CAAC;IAChC;EACF;EAIA1E,SAASA,CAACuG,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACtG,QAAQ,EAAE;MACjB,IAAI,CAACmB,uBAAuB,GAAG;QAC7B,GAAGmF,IAAI;QACPpG,gBAAgB,EAAEoG,IAAI,CAACpG,gBAAgB,GAAG,IAAIqG,IAAI,CAACD,IAAI,CAACpG,gBAAgB,CAAC,GAAGuE,SAAS;QACrFtE,cAAc,EAAEmG,IAAI,CAACnG,cAAc,GAAG,IAAIoG,IAAI,CAACD,IAAI,CAACnG,cAAc,CAAC,GAAGsE;OACvE;MACD,IAAI,CAACxC,aAAa,CAACuE,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOtI,MAAM,CAACsI,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAjF,QAAQA,CAAC2E,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAACzE,KAAK,CAAC0E,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC5E,OAAO,CAAC6E,aAAa,CAAC,IAAI,CAAC5E,KAAK,CAAC0E,aAAa,CAAC;MACpD;IACF;IACA,MAAMG,KAAK,GAAG;MACZhH,QAAQ,EAAE,IAAI,CAACmB,uBAAuB,CAACnB,QAAQ;MAC/CE,gBAAgB,EAAE,IAAI,CAACuG,UAAU,CAAC,IAAI,CAACtF,uBAAuB,CAACjB,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAACsG,UAAU,CAAC,IAAI,CAACtF,uBAAuB,CAAChB,cAAc;KAC5E;IAED,IAAI,CAACiC,aAAa,CAAC6E,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAACjC,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACuC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACjF,OAAO,CAACkF,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBhB,GAAG,CAACiB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAlC,gBAAgBA,CAAA;IACd,IAAI,CAAC/C,iBAAiB,CAACkF,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAACvC,IAAI,CAC7E3G,GAAG,CAAC4G,GAAG,IAAG;MACR,MAAM4C,OAAO,GAAG5C,GAAG,CAAC6C,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAACV,MAAM,IAAIlC,GAAG,CAACuC,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChD5I,cAAc,EAAE4I,KAAK,CAAC5I,cAAc;UACpC6I,GAAG,EAAED,KAAK,CAACC;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAACrF,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAIsF,KAAK,GAAG,IAAI,CAACJ,oBAAoB,CAACK,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAACrF,eAAe,CAAC;UAC1F,IAAI,CAACyC,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACwC,oBAAoB,CAACI,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAAC7C,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACwC,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMO,WAAW,GAAG,IAAI,CAAChD,WAAW,EAAEC,kBAAkB,EAAE2C,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACZ,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAACtC,SAAS,EAAE;EACf;EAEAmD,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAACrC,OAAO,CAACuC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACxC,OAAO,CAAEC,KAAU,IAAI;QACvC,MAAMwC,KAAK,GAAGxC,KAAK,CAACrF,MAAM;QAC1B,IAAI,CAAC0H,SAAS,CAACG,KAAK,CAAC,EAAE;UAAE;UACvBH,SAAS,CAACG,KAAK,CAAC,GAAG,EAAE;QACvB;QACAH,SAAS,CAACG,KAAK,CAAC,CAACC,IAAI,CAAC;UACpB/H,UAAU,EAAE4H,SAAS,CAAC5H,UAAU;UAChCgI,aAAa,EAAE1C,KAAK,CAAC0C,aAAa,IAAI,KAAK;UAC3CzI,QAAQ,EAAE+F,KAAK,CAAC/F,QAAQ;UACxBU,MAAM,EAAEqF,KAAK,CAACrF,MAAM;UACpBR,gBAAgB,EAAE6F,KAAK,CAAC7F,gBAAgB;UACxCC,cAAc,EAAE4F,KAAK,CAAC5F;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACuI,MAAM,CAACC,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAACJ,MAAM,CAACf,GAAG,CAAEY,KAAU,IAAI;MAChE,OAAO,IAAI,CAACQ,UAAU,CAACpB,GAAG,CAAEU,SAAc,IAAI;QAC5C,MAAMtC,KAAK,GAAGqC,SAAS,CAACG,KAAK,CAAC,CAACS,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAACxI,UAAU,KAAK4H,SAAS,CAAC;QAC5F,OAAOtC,KAAK,IAAI;UACdtF,UAAU,EAAE4H,SAAS;UACrBI,aAAa,EAAE,KAAK;UACpBzI,QAAQ,EAAE,IAAI;UACdU,MAAM,EAAE6H,KAAK;UACbrI,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO2I,MAAM;EACf;EAEAI,sBAAsBA,CAACf,GAAU;IAC/B,MAAMgB,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5CjB,GAAG,CAACrC,OAAO,CAACuC,SAAS,IAAG;MACtBgB,aAAa,CAACC,GAAG,CAACjB,SAAS,CAAC5H,UAAU,CAAC;MACvC4H,SAAS,CAACC,OAAO,CAACxC,OAAO,CAAEC,KAAU,IAAI;QACvCoD,SAAS,CAACG,GAAG,CAACvD,KAAK,CAACrF,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACgI,MAAM,GAAGa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLX,MAAM,EAAEa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACxE,WAAW,CAAC/E,gBAAgB,IAAI,IAAI,CAAC+E,WAAW,CAAC9E,cAAc,EAAE;MACxE,MAAMmD,SAAS,GAAG,IAAIiD,IAAI,CAAC,IAAI,CAACtB,WAAW,CAAC/E,gBAAgB,CAAC;MAC7D,MAAMqD,OAAO,GAAG,IAAIgD,IAAI,CAAC,IAAI,CAACtB,WAAW,CAAC9E,cAAc,CAAC;MACzD,IAAImD,SAAS,IAAIC,OAAO,IAAID,SAAS,GAAGC,OAAO,EAAE;QAC/C,IAAI,CAACrB,OAAO,CAAC6E,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEA;EACA2C,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACC,cAAc,EAAE;IACrB;IACA,IAAI,CAACtC,kBAAkB,EAAE;EAC3B;EAEA;EACAsC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACjH,cAAc,GAAG,EAAE;IACxB,IAAI,CAACiB,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACJ,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAACX,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE;KACjB;IAED;IACA,IAAI,CAACY,SAAS,GAAG,KAAK;IACtB,IAAI,CAAClB,gBAAgB,GAAG,EAAE;IAE1B;IACA,IAAI,CAACoB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACE,UAAU,GAAG,CAAC;IAEnB;IACA,IAAI,CAACvB,eAAe,GAAG,EAAE;IACzB,IAAI,CAACE,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,CAACsB,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC5B;EAEA+C,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC,IAAI,CAACpC,WAAW,CAACC,kBAAkB,EAAE2C,GAAG,EAAE;MAC7C,IAAI,CAAC5D,OAAO,GAAG,KAAK;MACpB;IACF;IAEA,IAAI,CAACA,OAAO,GAAG,IAAI;IACnB,IAAI,CAACwF,cAAc,EAAE;IACrB,IAAI,CAACrH,aAAa,CAAC0H,mCAAmC,CAAC;MACrD5C,IAAI,EAAE;QACJ6C,YAAY,EAAE,IAAI,CAAC9E,WAAW,CAACC,kBAAkB,CAAC2C,GAAG;QACrD3H,gBAAgB,EAAE,IAAI,CAAC+E,WAAW,CAAC/E,gBAAgB,GAAG,IAAI,CAACuG,UAAU,CAAC,IAAI,CAACxB,WAAW,CAAC/E,gBAAgB,CAAC,GAAGuE,SAAS;QACpHtE,cAAc,EAAE,IAAI,CAAC8E,WAAW,CAAC9E,cAAc,GAAG,IAAI,CAACsG,UAAU,CAAC,IAAI,CAACxB,WAAW,CAAC9E,cAAc,CAAC,GAAGsE;;KAExG,CAAC,CAACM,SAAS,CAACH,GAAG,IAAG;MACjB,IAAI,CAACX,OAAO,GAAG,KAAK;MACpB,IAAIW,GAAG,CAAC6C,OAAO,IAAI7C,GAAG,CAACuC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACyC,gBAAgB,GAAGhF,GAAG,CAAC6C,OAAO,GAAG7C,GAAG,CAAC6C,OAAO,GAAG,EAAE;QACtD,IAAI7C,GAAG,CAAC6C,OAAO,EAAE;UACf,IAAI,CAACmC,gBAAgB,GAAG,CAAC,GAAGhF,GAAG,CAAC6C,OAAO,CAAC;UACxC,IAAI,CAACyB,sBAAsB,CAACtE,GAAG,CAAC6C,OAAO,CAAC;UACxC,IAAI,CAACoC,mBAAmB,GAAG,IAAI,CAAC3B,8BAA8B,CAACtD,GAAG,CAAC6C,OAAO,CAAC;UAC3E;UACA,IAAI,CAACuC,mBAAmB,CAACpF,GAAG,CAAC6C,OAAO,CAAC;UACrC;UACA,IAAI,CAACwC,oBAAoB,CAACrF,GAAG,CAAC6C,OAAO,CAAC;QACxC;MACF;IACF,CAAC,CAAC;EACJ;EAEA;EACAuC,mBAAmBA,CAACE,IAA6B;IAC/C,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAyC;IAEpEF,IAAI,CAACpE,OAAO,CAACuC,SAAS,IAAG;MACvB,MAAMgC,SAAS,GAAGhC,SAAS,CAAC5H,UAAU,IAAI,EAAE,CAAC,CAAC;MAE9C4H,SAAS,CAACC,OAAO,EAAExC,OAAO,CAACC,KAAK,IAAG;QACjC,MAAMuE,YAAY,GAAGvE,KAAK,CAAC0C,aAAa,IAAI,KAAK,CAAC,CAAC;QACnD,MAAMF,KAAK,GAAGxC,KAAK,CAACrF,MAAM,IAAI,CAAC;QAE/B,IAAI,CAACyJ,WAAW,CAACI,GAAG,CAACD,YAAY,CAAC,EAAE;UAClCH,WAAW,CAACK,GAAG,CAACF,YAAY,EAAE,IAAIF,GAAG,EAA4B,CAAC;QACpE;QAEA,MAAMK,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACJ,YAAY,CAAE;QAE/C,IAAI,CAACG,QAAQ,CAACF,GAAG,CAAChC,KAAK,CAAC,EAAE;UACxBkC,QAAQ,CAACD,GAAG,CAACjC,KAAK,EAAE,EAAE,CAAC;QACzB;QAEAkC,QAAQ,CAACC,GAAG,CAACnC,KAAK,CAAE,CAACC,IAAI,CAAC;UACxB/H,UAAU,EAAE4J,SAAS;UAAE;UACvB5B,aAAa,EAAE6B,YAAY;UAAE;UAC7BtK,QAAQ,EAAE+F,KAAK,CAAC/F,QAAQ,IAAI,CAAC;UAC7BU,MAAM,EAAE6H,KAAK;UACbrI,gBAAgB,EAAE6F,KAAK,CAAC7F,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAE4F,KAAK,CAAC5F,cAAc,IAAI,EAAE;UAC1C6F,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACpD,cAAc,GAAG2G,KAAK,CAACC,IAAI,CAACW,WAAW,CAAC3C,OAAO,EAAE,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC2C,YAAY,EAAEG,QAAQ,CAAC,KAAI;MACvF,MAAM/B,MAAM,GAAiBa,KAAK,CAACC,IAAI,CAACiB,QAAQ,CAACjD,OAAO,EAAE,CAAC,CACxDmB,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;MAAA,CAC1BjB,GAAG,CAAC,CAAC,CAACgD,WAAW,EAAEC,MAAM,CAAC,MAAM;QAC/BD,WAAW;QACXC,MAAM,EAAEA,MAAM,CAACjC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAC3B;UACA,IAAID,CAAC,CAACnI,UAAU,KAAKoI,CAAC,CAACpI,UAAU,EAAE;YACjC,OAAOmI,CAAC,CAACnI,UAAU,CAACoK,aAAa,CAAChC,CAAC,CAACpI,UAAU,CAAC;UACjD;UACA,OAAOmI,CAAC,CAAClI,MAAM,GAAGmI,CAAC,CAACnI,MAAM;QAC5B,CAAC,CAAC;QACFsF,QAAQ,EAAE;OACX,CAAC,CAAC;MAEL,OAAO;QACL8E,IAAI,EAAER,YAAY;QAClB5B,MAAM;QACN1C,QAAQ,EAAE;OACX;IACH,CAAC,CAAC,CAAC2C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACkC,IAAI,CAACD,aAAa,CAAChC,CAAC,CAACiC,IAAI,CAAC,CAAC;IAE/C;IACA,IAAI,CAACjI,eAAe,GAAG,IAAI,CAACD,cAAc,CAAC+E,GAAG,CAACoD,EAAE,IAAIA,EAAE,CAACD,IAAI,CAAC;IAC7D,IAAI,CAACE,qBAAqB,EAAE;EAC9B;EAEA;EACAA,qBAAqBA,CAAA;IACnB,MAAM7B,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,IAAI,CAACxG,cAAc,CAACkD,OAAO,CAACmF,QAAQ,IAAG;MACrC,IAAI,CAAC,IAAI,CAACnI,gBAAgB,IAAImI,QAAQ,CAACH,IAAI,KAAK,IAAI,CAAChI,gBAAgB,EAAE;QACrEmI,QAAQ,CAACvC,MAAM,CAAC5C,OAAO,CAACyC,KAAK,IAAG;UAC9BY,SAAS,CAACG,GAAG,CAACf,KAAK,CAACoC,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAAC5H,eAAe,GAAGwG,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC,CAACR,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpE;EAEA;EACAsC,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACvH,cAAc,CAACmC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,KAAK,CAAC;IAC5D,IAAI,CAACrC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACK,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAACE,WAAW,GAAG,CAAC;IAEpB;IACA,IAAI,CAAClB,aAAa,CAACG,WAAW,GAAG,EAAE;IAEnC;IACA,IAAI,CAAC6H,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAAChI,aAAa,CAACI,cAAc,GAAG,IAAI,CAACN,gBAAgB;IACzD,IAAI,CAAC4C,QAAQ,EAAE;EACjB;EAIA;EACAyF,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACvI,cAAc,CAACwI,MAAM,CAACH,QAAQ,IAAG;MAC3C;MACA,IAAI,IAAI,CAACnI,gBAAgB,IAAImI,QAAQ,CAACH,IAAI,KAAK,IAAI,CAAChI,gBAAgB,EAAE;QACpE,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAACE,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMoI,OAAO,GAAG,IAAI,CAACrI,aAAa,CAACC,aAAa,CAACqI,WAAW,EAAE;QAC9D,MAAMC,gBAAgB,GAAGN,QAAQ,CAACvC,MAAM,CAAC8C,IAAI,CAACjD,KAAK,IACjDA,KAAK,CAACqC,MAAM,CAACY,IAAI,CAACzF,KAAK,IACrBA,KAAK,CAACtF,UAAU,CAAC6K,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IAChDtF,KAAK,CAAC0C,aAAa,CAAC6C,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,CACpD,CACF;QACD,IAAI,CAACE,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACvI,aAAa,CAACE,YAAY,EAAE;QACnC,MAAMwI,iBAAiB,GAAGT,QAAQ,CAACvC,MAAM,CAAC8C,IAAI,CAACjD,KAAK,IAClDA,KAAK,CAACqC,MAAM,CAACY,IAAI,CAACzF,KAAK,IAAI,IAAI,CAAC4F,mBAAmB,CAAC5F,KAAK,CAAC,CAAC,CAC5D;QACD,IAAI,CAAC2F,iBAAiB,EAAE;UACtB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC1I,aAAa,CAACG,WAAW,EAAE;QAClC,MAAMwH,WAAW,GAAGiB,QAAQ,CAAC,IAAI,CAAC5I,aAAa,CAACG,WAAW,CAAC;QAC5D,MAAM0I,gBAAgB,GAAGZ,QAAQ,CAACvC,MAAM,CAAC8C,IAAI,CAACjD,KAAK,IACjDA,KAAK,CAACoC,WAAW,KAAKA,WAAW,CAClC;QACD,IAAI,CAACkB,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CAAClE,GAAG,CAACsD,QAAQ,IAAG;MAChB;MACA,MAAMa,gBAAgB,GAAG;QAAE,GAAGb;MAAQ,CAAE;MACxCa,gBAAgB,CAACpD,MAAM,GAAGuC,QAAQ,CAACvC,MAAM,CAAC0C,MAAM,CAAC7C,KAAK,IAAG;QACvD;QACA,IAAI,IAAI,CAACvF,aAAa,CAACG,WAAW,EAAE;UAClC,MAAMwH,WAAW,GAAGiB,QAAQ,CAAC,IAAI,CAAC5I,aAAa,CAACG,WAAW,CAAC;UAC5D,IAAIoF,KAAK,CAACoC,WAAW,KAAKA,WAAW,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QAEA;QACA,MAAMoB,cAAc,GAAGxD,KAAK,CAACqC,MAAM,CAACY,IAAI,CAACzF,KAAK,IAAG;UAC/C;UACA,IAAI,IAAI,CAAC/C,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMoI,OAAO,GAAG,IAAI,CAACrI,aAAa,CAACC,aAAa,CAACqI,WAAW,EAAE;YAC9D,IAAI,CAACvF,KAAK,CAACtF,UAAU,CAAC6K,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACtF,KAAK,CAAC0C,aAAa,CAAC6C,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAACrI,aAAa,CAACE,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACyI,mBAAmB,CAAC5F,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOgG,cAAc;MACvB,CAAC,CAAC,CAACpE,GAAG,CAACY,KAAK,IAAG;QACb;QACA,MAAMyD,aAAa,GAAG;UAAE,GAAGzD;QAAK,CAAE;QAClCyD,aAAa,CAACpB,MAAM,GAAGrC,KAAK,CAACqC,MAAM,CAACQ,MAAM,CAACrF,KAAK,IAAG;UACjD;UACA,IAAI,IAAI,CAAC/C,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMoI,OAAO,GAAG,IAAI,CAACrI,aAAa,CAACC,aAAa,CAACqI,WAAW,EAAE;YAC9D,IAAI,CAACvF,KAAK,CAACtF,UAAU,CAAC6K,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACtF,KAAK,CAAC0C,aAAa,CAAC6C,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAACrI,aAAa,CAACE,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACyI,mBAAmB,CAAC5F,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOiG,aAAa;MACtB,CAAC,CAAC;MAEF,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ;EAEA;EACQH,mBAAmBA,CAAC5F,KAAqB;IAC/C,MAAMH,MAAM,GAAG,IAAI,CAACM,cAAc,CAACH,KAAK,CAAC;IAEzC,QAAQ,IAAI,CAAC/C,aAAa,CAACE,YAAY;MACrC,KAAK,QAAQ;QACX,OAAO0C,MAAM,KAAK,QAAQ;MAC5B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,UAAU;MAC9B;QACE,OAAO,IAAI;MAAE;IACjB;EACF;EAEA;EACQM,cAAcA,CAACH,KAAqB;IAC1C,IAAI,CAACA,KAAK,CAAC/F,QAAQ,EAAE;MACnB,OAAO,UAAU;IACnB;IAEA;IACA,IAAI,CAAC+F,KAAK,CAAC7F,gBAAgB,IAAI,CAAC6F,KAAK,CAAC5F,cAAc,IAClD4F,KAAK,CAAC7F,gBAAgB,KAAK,EAAE,IAAI6F,KAAK,CAAC5F,cAAc,KAAK,EAAE,EAAE;MAC9D,OAAO,SAAS;IAClB;IAEA,IAAI;MACF;MACA,MAAM8L,GAAG,GAAG,IAAI1F,IAAI,EAAE;MACtB,MAAM2F,KAAK,GAAG,IAAI3F,IAAI,CAAC0F,GAAG,CAACE,WAAW,EAAE,EAAEF,GAAG,CAACG,QAAQ,EAAE,EAAEH,GAAG,CAACI,OAAO,EAAE,CAAC;MAExE;MACA,IAAI/I,SAAe;MACnB,IAAIyC,KAAK,CAAC7F,gBAAgB,CAACuL,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxCnI,SAAS,GAAG,IAAIiD,IAAI,CAACR,KAAK,CAAC7F,gBAAgB,CAAC;MAC9C,CAAC,MAAM;QACLoD,SAAS,GAAG,IAAIiD,IAAI,CAACR,KAAK,CAAC7F,gBAAgB,GAAG,WAAW,CAAC;MAC5D;MAEA;MACA,IAAIqD,OAAa;MACjB,IAAIwC,KAAK,CAAC5F,cAAc,CAACsL,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtClI,OAAO,GAAG,IAAIgD,IAAI,CAACR,KAAK,CAAC5F,cAAc,CAAC;MAC1C,CAAC,MAAM;QACLoD,OAAO,GAAG,IAAIgD,IAAI,CAACR,KAAK,CAAC5F,cAAc,GAAG,WAAW,CAAC;MACxD;MAEA;MACA,IAAImM,KAAK,CAAChJ,SAAS,CAACiJ,OAAO,EAAE,CAAC,IAAID,KAAK,CAAC/I,OAAO,CAACgJ,OAAO,EAAE,CAAC,EAAE;QAC1DC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAE;UACnCC,KAAK,EAAE3G,KAAK,CAAC7F,gBAAgB;UAC7ByM,GAAG,EAAE5G,KAAK,CAAC5F,cAAc;UACzByM,OAAO,EAAE7G,KAAK,CAAC/F;SAChB,CAAC;QACF,OAAO,SAAS;MAClB;MAEA;MACA,MAAM6M,aAAa,GAAG,IAAItG,IAAI,CAACjD,SAAS,CAAC6I,WAAW,EAAE,EAAE7I,SAAS,CAAC8I,QAAQ,EAAE,EAAE9I,SAAS,CAAC+I,OAAO,EAAE,CAAC;MAClG,MAAMS,WAAW,GAAG,IAAIvG,IAAI,CAAChD,OAAO,CAAC4I,WAAW,EAAE,EAAE5I,OAAO,CAAC6I,QAAQ,EAAE,EAAE7I,OAAO,CAAC8I,OAAO,EAAE,CAAC;MAE1F;MACA,IAAIH,KAAK,GAAGW,aAAa,EAAE;QACzB,OAAO,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIX,KAAK,IAAIW,aAAa,IAAIX,KAAK,IAAIY,WAAW,EAAE;QACzD,OAAO,QAAQ,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,OAAO,SAAS,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,EAAEhH,KAAK,CAAC;MACnD,OAAO,SAAS;IAClB;EACF;EAEAvE,OAAOA,CAAC6E,GAAQ;IACdA,GAAG,CAACiB,KAAK,EAAE;EACb;EAEAV,UAAUA,CAAA;IACR,IAAI,CAACzE,KAAK,CAAC6K,KAAK,EAAE;IAClB,IAAI,CAAC7K,KAAK,CAAC8K,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC9L,uBAAuB,CAACjB,gBAAgB,CAAC;IAC9E,IAAI,CAACiC,KAAK,CAAC8K,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC9L,uBAAuB,CAAChB,cAAc,CAAC;IAC5E,IAAI,CAACgC,KAAK,CAAC+K,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC/L,uBAAuB,CAACjB,gBAAgB,EAAE,IAAI,CAACiB,uBAAuB,CAAChB,cAAc,CAAC;EACtI;EAEAgN,gBAAgBA,CAAA;IACd,IAAI,CAAC7K,MAAM,CAAC8K,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAACnI,WAAW,EAAEC,kBAAkB,EAAE2C,GAAG,EAAE,CAAC,CAAC;EACnG;EAEA;EACAwF,gBAAgBA,CAACpC,QAAwB;IACvC,IAAI,CAACrH,wBAAwB,GAAGqH,QAAQ,IAAI,IAAI;IAEhD;IACA,MAAMqC,iBAAiB,GAAG,IAAI,CAAC3J,cAAc,CAACmD,MAAM,GAAG,CAAC;IAExD,IAAI,CAACzD,aAAa,GAAG;MACnBC,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CAAC8J,iBAAiB,IAAI,CAACrC,QAAQ;MAC3CxH,iBAAiB,EAAEwH,QAAQ,GAAG,CAACA,QAAQ,CAACH,IAAI,CAAC,GAAG,EAAE;MAClDpH,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;KACjB;IAED;IACA,IAAIsH,QAAQ,EAAE;MACZA,QAAQ,CAACvC,MAAM,CAAC5C,OAAO,CAACyC,KAAK,IAAG;QAC9BA,KAAK,CAACvC,QAAQ,GAAG,KAAK;QACtBuC,KAAK,CAACqC,MAAM,CAAC9E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,KAAK,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAAC/D,aAAa,CAACuE,IAAI,CAAC,IAAI,CAAC+G,kBAAkB,CAAC;EAClD;EAEA;EACAC,sBAAsBA,CAACjF,KAAiB;IACtC,IAAIA,KAAK,CAACvC,QAAQ,EAAE;MAClBuC,KAAK,CAACqC,MAAM,CAAC9E,OAAO,CAACC,KAAK,IAAG;QAC3B,IAAIA,KAAK,CAAC/F,QAAQ,EAAE;UAClB+F,KAAK,CAACC,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLuC,KAAK,CAACqC,MAAM,CAAC9E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA;EACAyH,aAAaA,CAACpH,GAAQ;IACpB;IACA,IAAI,CAAClE,KAAK,CAAC6K,KAAK,EAAE;IAClB,IAAI,CAAC7K,KAAK,CAAC8K,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC5J,aAAa,CAACC,SAAS,CAAC;IAC3D,IAAI,CAACnB,KAAK,CAAC8K,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC5J,aAAa,CAACE,OAAO,CAAC;IACzD,IAAI,CAACpB,KAAK,CAAC+K,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC7J,aAAa,CAACC,SAAS,EAAE,IAAI,CAACD,aAAa,CAACE,OAAO,CAAC;IAElG,IAAI,IAAI,CAACpB,KAAK,CAAC0E,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC5E,OAAO,CAAC6E,aAAa,CAAC,IAAI,CAAC5E,KAAK,CAAC0E,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAM6G,cAAc,GAAU,EAAE;IAEhC,IAAI,IAAI,CAACrK,aAAa,CAACG,UAAU,EAAE;MACjC;MACA,IAAI,CAACK,eAAe,CAACiC,OAAO,CAACC,KAAK,IAAG;QACnC,IAAIA,KAAK,CAAC/F,QAAQ,EAAE;UAClB0N,cAAc,CAAClF,IAAI,CAAC;YAClBxI,QAAQ,EAAE+F,KAAK,CAAC/F,QAAQ;YACxBE,gBAAgB,EAAE,IAAI,CAACuG,UAAU,CAAC,IAAI,CAACpD,aAAa,CAACC,SAAS,CAAC;YAC/DnD,cAAc,EAAE,IAAI,CAACsG,UAAU,CAAC,IAAI,CAACpD,aAAa,CAACE,OAAO;WAC3D,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACI,cAAc,CAACmD,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACnD,cAAc,CAACmC,OAAO,CAACC,KAAK,IAAG;UAClC,IAAIA,KAAK,CAAC/F,QAAQ,EAAE;YAClB0N,cAAc,CAAClF,IAAI,CAAC;cAClBxI,QAAQ,EAAE+F,KAAK,CAAC/F,QAAQ;cACxBE,gBAAgB,EAAE,IAAI,CAACuG,UAAU,CAAC,IAAI,CAACpD,aAAa,CAACC,SAAS,CAAC;cAC/DnD,cAAc,EAAE,IAAI,CAACsG,UAAU,CAAC,IAAI,CAACpD,aAAa,CAACE,OAAO;aAC3D,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAACK,wBAAwB,EAAE;QACxC;QACA,IAAI,CAACA,wBAAwB,CAAC8E,MAAM,CAAC5C,OAAO,CAACyC,KAAK,IAAG;UACnDA,KAAK,CAACqC,MAAM,CAAC9E,OAAO,CAACC,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAAC/F,QAAQ,EAAE;cACpC0N,cAAc,CAAClF,IAAI,CAAC;gBAClBxI,QAAQ,EAAE+F,KAAK,CAAC/F,QAAQ;gBACxBE,gBAAgB,EAAE,IAAI,CAACuG,UAAU,CAAC,IAAI,CAACpD,aAAa,CAACC,SAAS,CAAC;gBAC/DnD,cAAc,EAAE,IAAI,CAACsG,UAAU,CAAC,IAAI,CAACpD,aAAa,CAACE,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,IAAImK,cAAc,CAAC5G,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC5E,OAAO,CAAC6E,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAAC3E,aAAa,CAAC6E,oCAAoC,CAAC;MACtDC,IAAI,EAAEwG;KACP,CAAC,CAAC3I,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACuC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACjF,OAAO,CAACkF,aAAa,CAAC,QAAQsG,cAAc,CAAC5G,MAAM,WAAW,CAAC;QACpE;QACA,IAAI,CAACnD,cAAc,CAACmC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,KAAK,CAAC;QAC5D,IAAI,CAACrC,cAAc,GAAG,EAAE;QACxB,IAAI,CAACK,SAAS,GAAG,KAAK;QACtB,IAAI,CAACqD,kBAAkB,EAAE;QACzBhB,GAAG,CAACiB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA;EACAqG,cAAcA,CAAC5H,KAAqB;IAClC,MAAMH,MAAM,GAAG,IAAI,CAACM,cAAc,CAACH,KAAK,CAAC;IACzC,OAAO,UAAUH,MAAM,EAAE;EAC3B;EAEA;EACAgI,eAAeA,CAAC7H,KAAqB;IACnC,IAAIA,KAAK,CAAC/F,QAAQ,EAAE;MAClB;MACA,IAAI,CAACF,SAAS,CAAC,IAAI,CAAC+N,MAAM,EAAE9H,KAAK,CAAC;IACpC;EACF;EAEA;EACAkE,oBAAoBA,CAACC,IAA6B;IAChD,IAAI,CAACrG,eAAe,GAAG,EAAE;IAEzBqG,IAAI,CAACpE,OAAO,CAACuC,SAAS,IAAG;MACvB,MAAMgC,SAAS,GAAGhC,SAAS,CAAC5H,UAAU,IAAI,EAAE;MAE5C4H,SAAS,CAACC,OAAO,EAAExC,OAAO,CAACC,KAAK,IAAG;QACjC,IAAI,CAAClC,eAAe,CAAC2E,IAAI,CAAC;UACxB/H,UAAU,EAAE4J,SAAS;UACrB5B,aAAa,EAAE1C,KAAK,CAAC0C,aAAa,IAAI,KAAK;UAC3CzI,QAAQ,EAAE+F,KAAK,CAAC/F,QAAQ,IAAI,CAAC;UAC7BU,MAAM,EAAEqF,KAAK,CAACrF,MAAM,IAAI,CAAC;UACzBR,gBAAgB,EAAE6F,KAAK,CAAC7F,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAE4F,KAAK,CAAC5F,cAAc,IAAI,EAAE;UAC1C6F,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACN,QAAQ,EAAE;IAEf;IACA,IAAI,CAACoI,iBAAiB,EAAE;EAC1B;EAEA;EACQA,iBAAiBA,CAAA;IACvB,MAAMC,YAAY,GAAG;MACnBC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACV,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAACtK,eAAe,CAACiC,OAAO,CAACC,KAAK,IAAG;MACnC,MAAMH,MAAM,GAAG,IAAI,CAACM,cAAc,CAACH,KAAK,CAAC;MACzC,IAAIgI,YAAY,CAACK,cAAc,CAACxI,MAAM,CAAC,EAAE;QACvCmI,YAAY,CAACnI,MAAmC,CAAC,EAAE;MACrD;IACF,CAAC,CAAC;IAEF4G,OAAO,CAAC6B,GAAG,CAAC,OAAO,EAAEN,YAAY,CAAC;IAClCvB,OAAO,CAAC6B,GAAG,CAAC,OAAO,EAAE,IAAI9H,IAAI,EAAE,CAAC+H,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;EAIA;EACA7I,QAAQA,CAAA;IACN;IACA,MAAM8I,qBAAqB,GAAG,IAAI,CAAC7K,cAAc,CAACgE,GAAG,CAAC5B,KAAK,IAAIA,KAAK,CAAC/F,QAAQ,CAAC;IAE9E,IAAI,CAAC8D,cAAc,GAAG,IAAI,CAACD,eAAe,CAACuH,MAAM,CAACrF,KAAK,IAAG;MACxD;MACA,IAAI,IAAI,CAAC/C,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMoI,OAAO,GAAG,IAAI,CAACrI,aAAa,CAACC,aAAa,CAACqI,WAAW,EAAE;QAC9D,IAAI,CAACvF,KAAK,CAACtF,UAAU,CAAC6K,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACtF,KAAK,CAAC0C,aAAa,CAAC6C,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;UACtD,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACvI,gBAAgB,IAAIiD,KAAK,CAAC0C,aAAa,KAAK,IAAI,CAAC3F,gBAAgB,EAAE;QAC1E,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAACE,aAAa,CAACE,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAACyI,mBAAmB,CAAC5F,KAAK,CAAC,EAAE;UACpC,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC/C,aAAa,CAACG,WAAW,EAAE;QAClC,MAAMwH,WAAW,GAAGiB,QAAQ,CAAC,IAAI,CAAC5I,aAAa,CAACG,WAAW,CAAC;QAC5D,IAAI4C,KAAK,CAACrF,MAAM,KAAKiK,WAAW,EAAE;UAChC,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF;IACA,IAAI,CAAChH,cAAc,GAAG,IAAI,CAACG,cAAc,CAACsH,MAAM,CAACrF,KAAK,IACpDyI,qBAAqB,CAAC/C,QAAQ,CAAC1F,KAAK,CAAC/F,QAAQ,CAAC,CAC/C;IAED;IACA,IAAI,CAAC6D,eAAe,CAACiC,OAAO,CAACC,KAAK,IAAG;MACnCA,KAAK,CAACC,QAAQ,GAAG,IAAI,CAACrC,cAAc,CAAC6H,IAAI,CAACxF,QAAQ,IAAIA,QAAQ,CAAChG,QAAQ,KAAK+F,KAAK,CAAC/F,QAAQ,CAAC;IAC7F,CAAC,CAAC;IAEF;IACA,IAAI,CAACyO,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACvK,WAAW,GAAG,CAAC;IACpB,IAAI,CAACwK,gBAAgB,EAAE;EACzB;EAEA;EACQD,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAAC1K,eAAe,CAAC+C,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC9C,SAAS,GAAG,KAAK;IACxB,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI,CAACD,eAAe,CAAC4K,KAAK,CAAC5I,KAAK,IAC/C,CAACA,KAAK,CAAC/F,QAAQ,IAAI+F,KAAK,CAACC,QAAQ,CAClC;IACH;EACF;EAEA;EACA0I,gBAAgBA,CAAA;IACd,IAAI,CAACtK,UAAU,GAAGG,IAAI,CAACqK,IAAI,CAAC,IAAI,CAAC9K,cAAc,CAACgD,MAAM,GAAG,IAAI,CAAC3C,QAAQ,CAAC;IACvE,MAAM0K,UAAU,GAAG,CAAC,IAAI,CAAC3K,WAAW,GAAG,CAAC,IAAI,IAAI,CAACC,QAAQ;IACzD,MAAM2K,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC1K,QAAQ;IAC3C,IAAI,CAACJ,eAAe,GAAG,IAAI,CAACD,cAAc,CAACiL,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACL,oBAAoB,EAAE;EAC7B;EAEA;EACAO,gBAAgBA,CAAA;IACd,IAAI,CAAC9K,WAAW,GAAG,CAAC;IACpB,IAAI,CAACwK,gBAAgB,EAAE;EACzB;EAEA;EACAtI,QAAQA,CAAC6I,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC7K,UAAU,EAAE;MACxC,IAAI,CAACF,WAAW,GAAG+K,IAAI;MACvB,IAAI,CAACP,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAQ,eAAeA,CAAA;IACb,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAI1C,KAAK,GAAGnI,IAAI,CAAC8K,GAAG,CAAC,CAAC,EAAE,IAAI,CAACnL,WAAW,GAAGK,IAAI,CAACgE,KAAK,CAAC6G,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAIzC,GAAG,GAAGpI,IAAI,CAAC+K,GAAG,CAAC,IAAI,CAAClL,UAAU,EAAEsI,KAAK,GAAG0C,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAIzC,GAAG,GAAGD,KAAK,GAAG,CAAC,GAAG0C,UAAU,EAAE;MAChC1C,KAAK,GAAGnI,IAAI,CAAC8K,GAAG,CAAC,CAAC,EAAE1C,GAAG,GAAGyC,UAAU,GAAG,CAAC,CAAC;IAC3C;IAEA,KAAK,IAAIG,CAAC,GAAG7C,KAAK,EAAE6C,CAAC,IAAI5C,GAAG,EAAE4C,CAAC,EAAE,EAAE;MACjCJ,KAAK,CAAC3G,IAAI,CAAC+G,CAAC,CAAC;IACf;IAEA,OAAOJ,KAAK;EACd;EAEA;EACAK,iBAAiBA,CAAA;IACf,IAAI,CAACzL,eAAe,CAAC+B,OAAO,CAACC,KAAK,IAAG;MACnC,IAAIA,KAAK,CAAC/F,QAAQ,EAAE;QAClB+F,KAAK,CAACC,QAAQ,GAAG,IAAI,CAAChC,SAAS;MACjC;IACF,CAAC,CAAC;IACF,IAAI,CAACyL,oBAAoB,EAAE;EAC7B;EAEA;EACAC,sBAAsBA,CAAA;IACpB,IAAI,CAACD,oBAAoB,EAAE;IAC3B,IAAI,CAACzL,SAAS,GAAG,IAAI,CAACD,eAAe,CAAC4K,KAAK,CAAC5I,KAAK,IAC/C,CAACA,KAAK,CAAC/F,QAAQ,IAAI+F,KAAK,CAACC,QAAQ,CAClC;EACH;EAEA;EACAyJ,oBAAoBA,CAAA;IAClB,IAAI,CAAC9L,cAAc,GAAG,IAAI,CAACE,eAAe,CAACuH,MAAM,CAACrF,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EAC5E;EAEA;EACA2C,IAAIA,CAACgH,KAAa;IAChB,IAAI,IAAI,CAACtL,SAAS,KAAKsL,KAAK,EAAE;MAC5B,IAAI,CAACrL,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IACpE,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAGsL,KAAK;MACtB,IAAI,CAACrL,aAAa,GAAG,KAAK;IAC5B;IAEA,IAAI,CAACR,cAAc,CAAC6E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAChC,IAAI+G,MAAM,GAAIhH,CAAS,CAAC+G,KAAK,CAAC;MAC9B,IAAIE,MAAM,GAAIhH,CAAS,CAAC8G,KAAK,CAAC;MAE9B;MACA,IAAIA,KAAK,CAAClE,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC1BmE,MAAM,GAAGA,MAAM,GAAG,IAAIrJ,IAAI,CAACqJ,MAAM,CAAC,CAACrD,OAAO,EAAE,GAAG,CAAC;QAChDsD,MAAM,GAAGA,MAAM,GAAG,IAAItJ,IAAI,CAACsJ,MAAM,CAAC,CAACtD,OAAO,EAAE,GAAG,CAAC;MAClD;MAEA;MACA,IAAIoD,KAAK,KAAK,QAAQ,EAAE;QACtBC,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC,IAAI,CAAC;QAC5BC,MAAM,GAAGC,MAAM,CAACD,MAAM,CAAC,IAAI,CAAC;MAC9B;MAEA;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAACtE,WAAW,EAAE;QAC7BuE,MAAM,GAAGA,MAAM,CAACvE,WAAW,EAAE;MAC/B;MAEA,IAAIsE,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACvL,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC9C;MACA,IAAIsL,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACvL,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9C;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAACoK,gBAAgB,EAAE;EACzB;EAEA;EACAqB,cAAcA,CAACC,MAAc,EAAEjK,KAAqB;IAClD,OAAOA,KAAK,CAAC/F,QAAQ;EACvB;EAEA;EACAiQ,aAAaA,CAAClK,KAAqB;IACjC,MAAMH,MAAM,GAAG,IAAI,CAACM,cAAc,CAACH,KAAK,CAAC;IAEzC,QAAQH,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,UAAU;QACb,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;;;uCApjCW9D,0BAA0B,EAAAtD,EAAA,CAAA0R,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5R,EAAA,CAAA0R,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA9R,EAAA,CAAA0R,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAhS,EAAA,CAAA0R,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAlS,EAAA,CAAA0R,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAApS,EAAA,CAAA0R,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAArS,EAAA,CAAA0R,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAAvS,EAAA,CAAA0R,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA1BnP,0BAA0B;MAAAoP,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;uCAT1B,EAAE,GAAA7S,EAAA,CAAA+S,0BAAA,EAAA/S,EAAA,CAAAgT,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCrEb7S,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAqC,SAAA,qBAAiC;UACnCrC,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,aAA8B,aACN,cACqC,gBACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,qBAC0C;UADdD,EAAA,CAAAsC,gBAAA,2BAAAgR,wEAAA9Q,MAAA;YAAAxC,EAAA,CAAAa,aAAA,CAAA0S,GAAA;YAAAvT,EAAA,CAAA0C,kBAAA,CAAAoQ,GAAA,CAAArM,WAAA,CAAAC,kBAAA,EAAAlE,MAAA,MAAAsQ,GAAA,CAAArM,WAAA,CAAAC,kBAAA,GAAAlE,MAAA;YAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;UAAA,EAA4C;UACtExC,EAAA,CAAAW,UAAA,4BAAA6S,yEAAA;YAAAxT,EAAA,CAAAa,aAAA,CAAA0S,GAAA;YAAA,OAAAvT,EAAA,CAAAqB,WAAA,CAAkByR,GAAA,CAAAjK,kBAAA,EAAoB;UAAA,EAAC;UACvC7I,EAAA,CAAA6B,UAAA,KAAA4R,gDAAA,wBAAoE;UAK1EzT,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,iCAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,yBAA4B;UAC1BD,EAAA,CAAAqC,SAAA,mBAAoD;UACpDrC,EAAA,CAAAC,cAAA,iBACkE;UAA3CD,EAAA,CAAAsC,gBAAA,2BAAAoR,oEAAAlR,MAAA;YAAAxC,EAAA,CAAAa,aAAA,CAAA0S,GAAA;YAAAvT,EAAA,CAAA0C,kBAAA,CAAAoQ,GAAA,CAAArM,WAAA,CAAA/E,gBAAA,EAAAc,MAAA,MAAAsQ,GAAA,CAAArM,WAAA,CAAA/E,gBAAA,GAAAc,MAAA;YAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;UAAA,EAA0C;UADjExC,EAAA,CAAAG,YAAA,EACkE;UAClEH,EAAA,CAAAqC,SAAA,4BAA8D;UAChErC,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAAC,cAAA,iBAA0C;UAACD,EAAA,CAAAE,MAAA,WAC3C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAe;UACbD,EAAA,CAAAqC,SAAA,mBAAoD;UACpDrC,EAAA,CAAAC,cAAA,iBAC2C;UAAzCD,EAAA,CAAAsC,gBAAA,2BAAAqR,oEAAAnR,MAAA;YAAAxC,EAAA,CAAAa,aAAA,CAAA0S,GAAA;YAAAvT,EAAA,CAAA0C,kBAAA,CAAAoQ,GAAA,CAAArM,WAAA,CAAA9E,cAAA,EAAAa,MAAA,MAAAsQ,GAAA,CAAArM,WAAA,CAAA9E,cAAA,GAAAa,MAAA;YAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;UAAA,EAAwC;UAD1CxC,EAAA,CAAAG,YAAA,EAC2C;UAC3CH,EAAA,CAAAqC,SAAA,4BAA4D;UAGlErC,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAuC,eACoB,iBACV;UAC3CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,eAA6B,iBAEF;UAAvBD,EAAA,CAAAsC,gBAAA,2BAAAsR,oEAAApR,MAAA;YAAAxC,EAAA,CAAAa,aAAA,CAAA0S,GAAA;YAAAvT,EAAA,CAAA0C,kBAAA,CAAAoQ,GAAA,CAAAlR,QAAA,EAAAY,MAAA,MAAAsQ,GAAA,CAAAlR,QAAA,GAAAY,MAAA;YAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;UAAA,EAAsB;UADxBxC,EAAA,CAAAG,YAAA,EACyB;UACzBH,EAAA,CAAAC,cAAA,iBAAwD;UACtDD,EAAA,CAAAE,MAAA,oDACF;UACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;UAEJH,EADF,CAAAC,cAAA,eAA6B,iBAEF;UAAvBD,EAAA,CAAAsC,gBAAA,2BAAAuR,oEAAArR,MAAA;YAAAxC,EAAA,CAAAa,aAAA,CAAA0S,GAAA;YAAAvT,EAAA,CAAA0C,kBAAA,CAAAoQ,GAAA,CAAAlR,QAAA,EAAAY,MAAA,MAAAsQ,GAAA,CAAAlR,QAAA,GAAAY,MAAA;YAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;UAAA,EAAsB;UADxBxC,EAAA,CAAAG,YAAA,EACyB;UACzBH,EAAA,CAAAC,cAAA,iBAAwD;UACtDD,EAAA,CAAAE,MAAA,oDACF;UAGNF,EAHM,CAAAG,YAAA,EAAQ,EACJ,EACF,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAwC,eACS,kBACgC;UAA/BD,EAAA,CAAAW,UAAA,mBAAAmT,6DAAA;YAAA9T,EAAA,CAAAa,aAAA,CAAA0S,GAAA;YAAA,OAAAvT,EAAA,CAAAqB,WAAA,CAASyR,GAAA,CAAAjK,kBAAA,EAAoB;UAAA,EAAC;UAC1E7I,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAqC,SAAA,aAA6B;UAClCrC,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAiE;UAA7BD,EAAA,CAAAW,UAAA,mBAAAoT,6DAAA;YAAA/T,EAAA,CAAAa,aAAA,CAAA0S,GAAA;YAAA,OAAAvT,EAAA,CAAAqB,WAAA,CAASyR,GAAA,CAAAnE,gBAAA,EAAkB;UAAA,EAAC;UAC9D3O,EAAA,CAAAE,MAAA,kCACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAKAH,EAHN,CAAAC,cAAA,eAAmC,iBAC2C,aACnE,cACgD;UACnDD,EAAA,CAAAqC,SAAA,UAAS;UACTrC,EAAA,CAAA6B,UAAA,KAAAmS,mDAAA,2BAAoE;UAIxEhU,EADE,CAAAG,YAAA,EAAK,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA6B,UAAA,KAAAoS,yCAAA,iBAA6D;UAgBvEjU,EAJQ,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO,EACP;UAeVH,EAbA,CAAA6B,UAAA,KAAAqS,kDAAA,gCAAAlU,EAAA,CAAAmU,sBAAA,CAAmE,KAAAC,kDAAA,iCAAApU,EAAA,CAAAmU,sBAAA,CAaf;;;;;UAxGdnU,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAoD,gBAAA,YAAA0P,GAAA,CAAArM,WAAA,CAAAC,kBAAA,CAA4C;UAE1C1G,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA0S,GAAA,CAAA5J,oBAAA,CAAuB;UAYWlJ,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,iBAAAiU,aAAA,CAA0B;UACjErU,EAAA,CAAAoD,gBAAA,YAAA0P,GAAA,CAAArM,WAAA,CAAA/E,gBAAA,CAA0C;UAOL1B,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,iBAAAkU,WAAA,CAAwB;UAClFtU,EAAA,CAAAoD,gBAAA,YAAA0P,GAAA,CAAArM,WAAA,CAAA9E,cAAA,CAAwC;UAWkD3B,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,eAAc;UACxGJ,EAAA,CAAAoD,gBAAA,YAAA0P,GAAA,CAAAlR,QAAA,CAAsB;UAMoE5B,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,gBAAe;UACzGJ,EAAA,CAAAoD,gBAAA,YAAA0P,GAAA,CAAAlR,QAAA,CAAsB;UAyBO5B,EAAA,CAAAM,SAAA,IAAsB;UAAtBN,EAAA,CAAAI,UAAA,YAAA0S,GAAA,CAAA1H,gBAAA,CAAsB;UAMlCpL,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAA0S,GAAA,CAAAzH,mBAAA,CAAyB;;;qBDXpD9L,YAAY,EAAAgV,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE5U,YAAY,EAAA6U,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,yBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAjD,EAAA,CAAAkD,eAAA,EAAAlD,EAAA,CAAAmD,mBAAA,EAAAnD,EAAA,CAAAoD,qBAAA,EAAApD,EAAA,CAAAqD,qBAAA,EAAArD,EAAA,CAAAsD,gBAAA,EAAAtD,EAAA,CAAAuD,iBAAA,EAAAvD,EAAA,CAAAwD,iBAAA,EAAAxD,EAAA,CAAAyD,oBAAA,EAAAzD,EAAA,CAAA0D,iBAAA,EAAA1D,EAAA,CAAA2D,eAAA,EAAA3D,EAAA,CAAA4D,qBAAA,EAAA5D,EAAA,CAAA6D,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAC1BnW,kBAAkB,EAAEC,mBAAmB,EACvCC,qBAAqB;MAAAkW,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}