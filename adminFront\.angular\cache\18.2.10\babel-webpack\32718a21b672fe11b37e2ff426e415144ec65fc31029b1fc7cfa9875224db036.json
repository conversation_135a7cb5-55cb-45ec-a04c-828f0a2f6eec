{"ast": null, "code": "import { SpaceTemplateSelectorComponent } from './space-template-selector.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nexport let SpaceTemplateSelectorService = /*#__PURE__*/(() => {\n  class SpaceTemplateSelectorService {\n    constructor(dialogService) {\n      this.dialogService = dialogService;\n    }\n    /**\n     * 開啟空間模板選擇器對話框\n     * @param buildCaseId 建案ID\n     * @returns Observable 當用戶確認套用模板時回傳配置，取消時回傳 null\n     */\n    openSelector(buildCaseId = '') {\n      const dialogRef = this.dialogService.open(SpaceTemplateSelectorComponent, {\n        context: {\n          buildCaseId: buildCaseId\n        },\n        closeOnBackdropClick: true,\n        closeOnEsc: true,\n        hasScroll: true,\n        autoFocus: false\n      });\n      // 監聽模板套用事件\n      const templateApplied$ = dialogRef.componentRef.instance.templateApplied.asObservable();\n      // 當有模板套用時，關閉對話框並回傳結果\n      templateApplied$.subscribe(config => {\n        dialogRef.close(config);\n      });\n      return dialogRef.onClose;\n    }\n    static {\n      this.ɵfac = function SpaceTemplateSelectorService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SpaceTemplateSelectorService)(i0.ɵɵinject(i1.NbDialogService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SpaceTemplateSelectorService,\n        factory: SpaceTemplateSelectorService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SpaceTemplateSelectorService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}