{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// FIXME step not support polar\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport SymbolClz from '../helper/Symbol.js';\nimport lineAnimationDiff from './lineAnimationDiff.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as modelUtil from '../../util/model.js';\nimport { ECPolyline, ECPolygon } from './poly.js';\nimport ChartView from '../../view/Chart.js';\nimport { prepareDataCoordInfo, getStackedOnPoint } from './helper.js';\nimport { createGridClipPath, createPolarClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { setStatesStylesFromModel, setStatesFlag, toggleHoverEmphasis, SPECIAL_STATES } from '../../util/states.js';\nimport { setLabelStyle, getLabelStatesModels, labelInner } from '../../label/labelStyle.js';\nimport { getDefaultLabel, getDefaultInterpolatedLabel } from '../helper/labelHelper.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createFloat32Array } from '../../util/vendor.js';\nimport { convertToColorString } from '../../util/format.js';\nimport { lerp } from 'zrender/lib/tool/color.js';\nfunction isPointsSame(points1, points2) {\n  if (points1.length !== points2.length) {\n    return;\n  }\n  for (var i = 0; i < points1.length; i++) {\n    if (points1[i] !== points2[i]) {\n      return;\n    }\n  }\n  return true;\n}\nfunction bboxFromPoints(points) {\n  var minX = Infinity;\n  var minY = Infinity;\n  var maxX = -Infinity;\n  var maxY = -Infinity;\n  for (var i = 0; i < points.length;) {\n    var x = points[i++];\n    var y = points[i++];\n    if (!isNaN(x)) {\n      minX = Math.min(x, minX);\n      maxX = Math.max(x, maxX);\n    }\n    if (!isNaN(y)) {\n      minY = Math.min(y, minY);\n      maxY = Math.max(y, maxY);\n    }\n  }\n  return [[minX, minY], [maxX, maxY]];\n}\nfunction getBoundingDiff(points1, points2) {\n  var _a = bboxFromPoints(points1),\n    min1 = _a[0],\n    max1 = _a[1];\n  var _b = bboxFromPoints(points2),\n    min2 = _b[0],\n    max2 = _b[1];\n  // Get a max value from each corner of two boundings.\n  return Math.max(Math.abs(min1[0] - min2[0]), Math.abs(min1[1] - min2[1]), Math.abs(max1[0] - max2[0]), Math.abs(max1[1] - max2[1]));\n}\nfunction getSmooth(smooth) {\n  return zrUtil.isNumber(smooth) ? smooth : smooth ? 0.5 : 0;\n}\nfunction getStackedOnPoints(coordSys, data, dataCoordInfo) {\n  if (!dataCoordInfo.valueDim) {\n    return [];\n  }\n  var len = data.count();\n  var points = createFloat32Array(len * 2);\n  for (var idx = 0; idx < len; idx++) {\n    var pt = getStackedOnPoint(dataCoordInfo, coordSys, data, idx);\n    points[idx * 2] = pt[0];\n    points[idx * 2 + 1] = pt[1];\n  }\n  return points;\n}\n/**\r\n * Filter the null data and extend data for step considering `stepTurnAt`\r\n *\r\n * @param points data to convert, that may containing null\r\n * @param basePoints base data to reference, used only for areaStyle points\r\n * @param coordSys coordinate system\r\n * @param stepTurnAt 'start' | 'end' | 'middle' | true\r\n * @param connectNulls whether to connect nulls\r\n * @returns converted point positions\r\n */\nfunction turnPointsIntoStep(points, basePoints, coordSys, stepTurnAt, connectNulls) {\n  var baseAxis = coordSys.getBaseAxis();\n  var baseIndex = baseAxis.dim === 'x' || baseAxis.dim === 'radius' ? 0 : 1;\n  var stepPoints = [];\n  var i = 0;\n  var stepPt = [];\n  var pt = [];\n  var nextPt = [];\n  var filteredPoints = [];\n  if (connectNulls) {\n    for (i = 0; i < points.length; i += 2) {\n      /**\r\n       * For areaStyle of stepped lines, `stackedOnPoints` should be\r\n       * filtered the same as `points` so that the base axis values\r\n       * should stay the same as the lines above. See #20021\r\n       */\n      var reference = basePoints || points;\n      if (!isNaN(reference[i]) && !isNaN(reference[i + 1])) {\n        filteredPoints.push(points[i], points[i + 1]);\n      }\n    }\n    points = filteredPoints;\n  }\n  for (i = 0; i < points.length - 2; i += 2) {\n    nextPt[0] = points[i + 2];\n    nextPt[1] = points[i + 3];\n    pt[0] = points[i];\n    pt[1] = points[i + 1];\n    stepPoints.push(pt[0], pt[1]);\n    switch (stepTurnAt) {\n      case 'end':\n        stepPt[baseIndex] = nextPt[baseIndex];\n        stepPt[1 - baseIndex] = pt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n        break;\n      case 'middle':\n        var middle = (pt[baseIndex] + nextPt[baseIndex]) / 2;\n        var stepPt2 = [];\n        stepPt[baseIndex] = stepPt2[baseIndex] = middle;\n        stepPt[1 - baseIndex] = pt[1 - baseIndex];\n        stepPt2[1 - baseIndex] = nextPt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n        stepPoints.push(stepPt2[0], stepPt2[1]);\n        break;\n      default:\n        // default is start\n        stepPt[baseIndex] = pt[baseIndex];\n        stepPt[1 - baseIndex] = nextPt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n    }\n  }\n  // Last points\n  stepPoints.push(points[i++], points[i++]);\n  return stepPoints;\n}\n/**\r\n * Clip color stops to edge. Avoid creating too large gradients.\r\n * Which may lead to blurry when GPU acceleration is enabled. See #15680\r\n *\r\n * The stops has been sorted from small to large.\r\n */\nfunction clipColorStops(colorStops, maxSize) {\n  var newColorStops = [];\n  var len = colorStops.length;\n  // coord will always < 0 in prevOutOfRangeColorStop.\n  var prevOutOfRangeColorStop;\n  var prevInRangeColorStop;\n  function lerpStop(stop0, stop1, clippedCoord) {\n    var coord0 = stop0.coord;\n    var p = (clippedCoord - coord0) / (stop1.coord - coord0);\n    var color = lerp(p, [stop0.color, stop1.color]);\n    return {\n      coord: clippedCoord,\n      color: color\n    };\n  }\n  for (var i = 0; i < len; i++) {\n    var stop_1 = colorStops[i];\n    var coord = stop_1.coord;\n    if (coord < 0) {\n      prevOutOfRangeColorStop = stop_1;\n    } else if (coord > maxSize) {\n      if (prevInRangeColorStop) {\n        newColorStops.push(lerpStop(prevInRangeColorStop, stop_1, maxSize));\n      } else if (prevOutOfRangeColorStop) {\n        // If there are two stops and coord range is between these two stops\n        newColorStops.push(lerpStop(prevOutOfRangeColorStop, stop_1, 0), lerpStop(prevOutOfRangeColorStop, stop_1, maxSize));\n      }\n      // All following stop will be out of range. So just ignore them.\n      break;\n    } else {\n      if (prevOutOfRangeColorStop) {\n        newColorStops.push(lerpStop(prevOutOfRangeColorStop, stop_1, 0));\n        // Reset\n        prevOutOfRangeColorStop = null;\n      }\n      newColorStops.push(stop_1);\n      prevInRangeColorStop = stop_1;\n    }\n  }\n  return newColorStops;\n}\nfunction getVisualGradient(data, coordSys, api) {\n  var visualMetaList = data.getVisual('visualMeta');\n  if (!visualMetaList || !visualMetaList.length || !data.count()) {\n    // When data.count() is 0, gradient range can not be calculated.\n    return;\n  }\n  if (coordSys.type !== 'cartesian2d') {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Visual map on line style is only supported on cartesian2d.');\n    }\n    return;\n  }\n  var coordDim;\n  var visualMeta;\n  for (var i = visualMetaList.length - 1; i >= 0; i--) {\n    var dimInfo = data.getDimensionInfo(visualMetaList[i].dimension);\n    coordDim = dimInfo && dimInfo.coordDim;\n    // Can only be x or y\n    if (coordDim === 'x' || coordDim === 'y') {\n      visualMeta = visualMetaList[i];\n      break;\n    }\n  }\n  if (!visualMeta) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Visual map on line style only support x or y dimension.');\n    }\n    return;\n  }\n  // If the area to be rendered is bigger than area defined by LinearGradient,\n  // the canvas spec prescribes that the color of the first stop and the last\n  // stop should be used. But if two stops are added at offset 0, in effect\n  // browsers use the color of the second stop to render area outside\n  // LinearGradient. So we can only infinitesimally extend area defined in\n  // LinearGradient to render `outerColors`.\n  var axis = coordSys.getAxis(coordDim);\n  // dataToCoord mapping may not be linear, but must be monotonic.\n  var colorStops = zrUtil.map(visualMeta.stops, function (stop) {\n    // offset will be calculated later.\n    return {\n      coord: axis.toGlobalCoord(axis.dataToCoord(stop.value)),\n      color: stop.color\n    };\n  });\n  var stopLen = colorStops.length;\n  var outerColors = visualMeta.outerColors.slice();\n  if (stopLen && colorStops[0].coord > colorStops[stopLen - 1].coord) {\n    colorStops.reverse();\n    outerColors.reverse();\n  }\n  var colorStopsInRange = clipColorStops(colorStops, coordDim === 'x' ? api.getWidth() : api.getHeight());\n  var inRangeStopLen = colorStopsInRange.length;\n  if (!inRangeStopLen && stopLen) {\n    // All stops are out of range. All will be the same color.\n    return colorStops[0].coord < 0 ? outerColors[1] ? outerColors[1] : colorStops[stopLen - 1].color : outerColors[0] ? outerColors[0] : colorStops[0].color;\n  }\n  var tinyExtent = 10; // Arbitrary value: 10px\n  var minCoord = colorStopsInRange[0].coord - tinyExtent;\n  var maxCoord = colorStopsInRange[inRangeStopLen - 1].coord + tinyExtent;\n  var coordSpan = maxCoord - minCoord;\n  if (coordSpan < 1e-3) {\n    return 'transparent';\n  }\n  zrUtil.each(colorStopsInRange, function (stop) {\n    stop.offset = (stop.coord - minCoord) / coordSpan;\n  });\n  colorStopsInRange.push({\n    // NOTE: inRangeStopLen may still be 0 if stoplen is zero.\n    offset: inRangeStopLen ? colorStopsInRange[inRangeStopLen - 1].offset : 0.5,\n    color: outerColors[1] || 'transparent'\n  });\n  colorStopsInRange.unshift({\n    offset: inRangeStopLen ? colorStopsInRange[0].offset : 0.5,\n    color: outerColors[0] || 'transparent'\n  });\n  var gradient = new graphic.LinearGradient(0, 0, 0, 0, colorStopsInRange, true);\n  gradient[coordDim] = minCoord;\n  gradient[coordDim + '2'] = maxCoord;\n  return gradient;\n}\nfunction getIsIgnoreFunc(seriesModel, data, coordSys) {\n  var showAllSymbol = seriesModel.get('showAllSymbol');\n  var isAuto = showAllSymbol === 'auto';\n  if (showAllSymbol && !isAuto) {\n    return;\n  }\n  var categoryAxis = coordSys.getAxesByScale('ordinal')[0];\n  if (!categoryAxis) {\n    return;\n  }\n  // Note that category label interval strategy might bring some weird effect\n  // in some scenario: users may wonder why some of the symbols are not\n  // displayed. So we show all symbols as possible as we can.\n  if (isAuto\n  // Simplify the logic, do not determine label overlap here.\n  && canShowAllSymbolForCategory(categoryAxis, data)) {\n    return;\n  }\n  // Otherwise follow the label interval strategy on category axis.\n  var categoryDataDim = data.mapDimension(categoryAxis.dim);\n  var labelMap = {};\n  zrUtil.each(categoryAxis.getViewLabels(), function (labelItem) {\n    var ordinalNumber = categoryAxis.scale.getRawOrdinalNumber(labelItem.tickValue);\n    labelMap[ordinalNumber] = 1;\n  });\n  return function (dataIndex) {\n    return !labelMap.hasOwnProperty(data.get(categoryDataDim, dataIndex));\n  };\n}\nfunction canShowAllSymbolForCategory(categoryAxis, data) {\n  // In most cases, line is monotonous on category axis, and the label size\n  // is close with each other. So we check the symbol size and some of the\n  // label size alone with the category axis to estimate whether all symbol\n  // can be shown without overlap.\n  var axisExtent = categoryAxis.getExtent();\n  var availSize = Math.abs(axisExtent[1] - axisExtent[0]) / categoryAxis.scale.count();\n  isNaN(availSize) && (availSize = 0); // 0/0 is NaN.\n  // Sampling some points, max 5.\n  var dataLen = data.count();\n  var step = Math.max(1, Math.round(dataLen / 5));\n  for (var dataIndex = 0; dataIndex < dataLen; dataIndex += step) {\n    if (SymbolClz.getSymbolSize(data, dataIndex\n    // Only for cartesian, where `isHorizontal` exists.\n    )[categoryAxis.isHorizontal() ? 1 : 0]\n    // Empirical number\n    * 1.5 > availSize) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPointNull(x, y) {\n  return isNaN(x) || isNaN(y);\n}\nfunction getLastIndexNotNull(points) {\n  var len = points.length / 2;\n  for (; len > 0; len--) {\n    if (!isPointNull(points[len * 2 - 2], points[len * 2 - 1])) {\n      break;\n    }\n  }\n  return len - 1;\n}\nfunction getPointAtIndex(points, idx) {\n  return [points[idx * 2], points[idx * 2 + 1]];\n}\nfunction getIndexRange(points, xOrY, dim) {\n  var len = points.length / 2;\n  var dimIdx = dim === 'x' ? 0 : 1;\n  var a;\n  var b;\n  var prevIndex = 0;\n  var nextIndex = -1;\n  for (var i = 0; i < len; i++) {\n    b = points[i * 2 + dimIdx];\n    if (isNaN(b) || isNaN(points[i * 2 + 1 - dimIdx])) {\n      continue;\n    }\n    if (i === 0) {\n      a = b;\n      continue;\n    }\n    if (a <= xOrY && b >= xOrY || a >= xOrY && b <= xOrY) {\n      nextIndex = i;\n      break;\n    }\n    prevIndex = i;\n    a = b;\n  }\n  return {\n    range: [prevIndex, nextIndex],\n    t: (xOrY - a) / (b - a)\n  };\n}\nfunction anyStateShowEndLabel(seriesModel) {\n  if (seriesModel.get(['endLabel', 'show'])) {\n    return true;\n  }\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    if (seriesModel.get([SPECIAL_STATES[i], 'endLabel', 'show'])) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction createLineClipPath(lineView, coordSys, hasAnimation, seriesModel) {\n  if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n    var endLabelModel_1 = seriesModel.getModel('endLabel');\n    var valueAnimation_1 = endLabelModel_1.get('valueAnimation');\n    var data_1 = seriesModel.getData();\n    var labelAnimationRecord_1 = {\n      lastFrameIndex: 0\n    };\n    var during = anyStateShowEndLabel(seriesModel) ? function (percent, clipRect) {\n      lineView._endLabelOnDuring(percent, clipRect, data_1, labelAnimationRecord_1, valueAnimation_1, endLabelModel_1, coordSys);\n    } : null;\n    var isHorizontal = coordSys.getBaseAxis().isHorizontal();\n    var clipPath = createGridClipPath(coordSys, hasAnimation, seriesModel, function () {\n      var endLabel = lineView._endLabel;\n      if (endLabel && hasAnimation) {\n        if (labelAnimationRecord_1.originalX != null) {\n          endLabel.attr({\n            x: labelAnimationRecord_1.originalX,\n            y: labelAnimationRecord_1.originalY\n          });\n        }\n      }\n    }, during);\n    // Expand clip shape to avoid clipping when line value exceeds axis\n    if (!seriesModel.get('clip', true)) {\n      var rectShape = clipPath.shape;\n      var expandSize = Math.max(rectShape.width, rectShape.height);\n      if (isHorizontal) {\n        rectShape.y -= expandSize;\n        rectShape.height += expandSize * 2;\n      } else {\n        rectShape.x -= expandSize;\n        rectShape.width += expandSize * 2;\n      }\n    }\n    // Set to the final frame. To make sure label layout is right.\n    if (during) {\n      during(1, clipPath);\n    }\n    return clipPath;\n  } else {\n    if (process.env.NODE_ENV !== 'production') {\n      if (seriesModel.get(['endLabel', 'show'])) {\n        console.warn('endLabel is not supported for lines in polar systems.');\n      }\n    }\n    return createPolarClipPath(coordSys, hasAnimation, seriesModel);\n  }\n}\nfunction getEndLabelStateSpecified(endLabelModel, coordSys) {\n  var baseAxis = coordSys.getBaseAxis();\n  var isHorizontal = baseAxis.isHorizontal();\n  var isBaseInversed = baseAxis.inverse;\n  var align = isHorizontal ? isBaseInversed ? 'right' : 'left' : 'center';\n  var verticalAlign = isHorizontal ? 'middle' : isBaseInversed ? 'top' : 'bottom';\n  return {\n    normal: {\n      align: endLabelModel.get('align') || align,\n      verticalAlign: endLabelModel.get('verticalAlign') || verticalAlign\n    }\n  };\n}\nvar LineView = /** @class */function (_super) {\n  __extends(LineView, _super);\n  function LineView() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  LineView.prototype.init = function () {\n    var lineGroup = new graphic.Group();\n    var symbolDraw = new SymbolDraw();\n    this.group.add(symbolDraw.group);\n    this._symbolDraw = symbolDraw;\n    this._lineGroup = lineGroup;\n    this._changePolyState = zrUtil.bind(this._changePolyState, this);\n  };\n  LineView.prototype.render = function (seriesModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var group = this.group;\n    var data = seriesModel.getData();\n    var lineStyleModel = seriesModel.getModel('lineStyle');\n    var areaStyleModel = seriesModel.getModel('areaStyle');\n    var points = data.getLayout('points') || [];\n    var isCoordSysPolar = coordSys.type === 'polar';\n    var prevCoordSys = this._coordSys;\n    var symbolDraw = this._symbolDraw;\n    var polyline = this._polyline;\n    var polygon = this._polygon;\n    var lineGroup = this._lineGroup;\n    var hasAnimation = !ecModel.ssr && seriesModel.get('animation');\n    var isAreaChart = !areaStyleModel.isEmpty();\n    var valueOrigin = areaStyleModel.get('origin');\n    var dataCoordInfo = prepareDataCoordInfo(coordSys, data, valueOrigin);\n    var stackedOnPoints = isAreaChart && getStackedOnPoints(coordSys, data, dataCoordInfo);\n    var showSymbol = seriesModel.get('showSymbol');\n    var connectNulls = seriesModel.get('connectNulls');\n    var isIgnoreFunc = showSymbol && !isCoordSysPolar && getIsIgnoreFunc(seriesModel, data, coordSys);\n    // Remove temporary symbols\n    var oldData = this._data;\n    oldData && oldData.eachItemGraphicEl(function (el, idx) {\n      if (el.__temp) {\n        group.remove(el);\n        oldData.setItemGraphicEl(idx, null);\n      }\n    });\n    // Remove previous created symbols if showSymbol changed to false\n    if (!showSymbol) {\n      symbolDraw.remove();\n    }\n    group.add(lineGroup);\n    // FIXME step not support polar\n    var step = !isCoordSysPolar ? seriesModel.get('step') : false;\n    var clipShapeForSymbol;\n    if (coordSys && coordSys.getArea && seriesModel.get('clip', true)) {\n      clipShapeForSymbol = coordSys.getArea();\n      // Avoid float number rounding error for symbol on the edge of axis extent.\n      // See #7913 and `test/dataZoom-clip.html`.\n      if (clipShapeForSymbol.width != null) {\n        clipShapeForSymbol.x -= 0.1;\n        clipShapeForSymbol.y -= 0.1;\n        clipShapeForSymbol.width += 0.2;\n        clipShapeForSymbol.height += 0.2;\n      } else if (clipShapeForSymbol.r0) {\n        clipShapeForSymbol.r0 -= 0.5;\n        clipShapeForSymbol.r += 0.5;\n      }\n    }\n    this._clipShapeForSymbol = clipShapeForSymbol;\n    var visualColor = getVisualGradient(data, coordSys, api) || data.getVisual('style')[data.getVisual('drawType')];\n    // Initialization animation or coordinate system changed\n    if (!(polyline && prevCoordSys.type === coordSys.type && step === this._step)) {\n      showSymbol && symbolDraw.updateData(data, {\n        isIgnore: isIgnoreFunc,\n        clipShape: clipShapeForSymbol,\n        disableAnimation: true,\n        getSymbolPoint: function (idx) {\n          return [points[idx * 2], points[idx * 2 + 1]];\n        }\n      });\n      hasAnimation && this._initSymbolLabelAnimation(data, coordSys, clipShapeForSymbol);\n      if (step) {\n        if (stackedOnPoints) {\n          stackedOnPoints = turnPointsIntoStep(stackedOnPoints, points, coordSys, step, connectNulls);\n        }\n        // TODO If stacked series is not step\n        points = turnPointsIntoStep(points, null, coordSys, step, connectNulls);\n      }\n      polyline = this._newPolyline(points);\n      if (isAreaChart) {\n        polygon = this._newPolygon(points, stackedOnPoints);\n      } // If areaStyle is removed\n      else if (polygon) {\n        lineGroup.remove(polygon);\n        polygon = this._polygon = null;\n      }\n      // NOTE: Must update _endLabel before setClipPath.\n      if (!isCoordSysPolar) {\n        this._initOrUpdateEndLabel(seriesModel, coordSys, convertToColorString(visualColor));\n      }\n      lineGroup.setClipPath(createLineClipPath(this, coordSys, true, seriesModel));\n    } else {\n      if (isAreaChart && !polygon) {\n        // If areaStyle is added\n        polygon = this._newPolygon(points, stackedOnPoints);\n      } else if (polygon && !isAreaChart) {\n        // If areaStyle is removed\n        lineGroup.remove(polygon);\n        polygon = this._polygon = null;\n      }\n      // NOTE: Must update _endLabel before setClipPath.\n      if (!isCoordSysPolar) {\n        this._initOrUpdateEndLabel(seriesModel, coordSys, convertToColorString(visualColor));\n      }\n      // Update clipPath\n      var oldClipPath = lineGroup.getClipPath();\n      if (oldClipPath) {\n        var newClipPath = createLineClipPath(this, coordSys, false, seriesModel);\n        graphic.initProps(oldClipPath, {\n          shape: newClipPath.shape\n        }, seriesModel);\n      } else {\n        lineGroup.setClipPath(createLineClipPath(this, coordSys, true, seriesModel));\n      }\n      // Always update, or it is wrong in the case turning on legend\n      // because points are not changed.\n      showSymbol && symbolDraw.updateData(data, {\n        isIgnore: isIgnoreFunc,\n        clipShape: clipShapeForSymbol,\n        disableAnimation: true,\n        getSymbolPoint: function (idx) {\n          return [points[idx * 2], points[idx * 2 + 1]];\n        }\n      });\n      // In the case data zoom triggered refreshing frequently\n      // Data may not change if line has a category axis. So it should animate nothing.\n      if (!isPointsSame(this._stackedOnPoints, stackedOnPoints) || !isPointsSame(this._points, points)) {\n        if (hasAnimation) {\n          this._doUpdateAnimation(data, stackedOnPoints, coordSys, api, step, valueOrigin, connectNulls);\n        } else {\n          // Not do it in update with animation\n          if (step) {\n            if (stackedOnPoints) {\n              stackedOnPoints = turnPointsIntoStep(stackedOnPoints, points, coordSys, step, connectNulls);\n            }\n            // TODO If stacked series is not step\n            points = turnPointsIntoStep(points, null, coordSys, step, connectNulls);\n          }\n          polyline.setShape({\n            points: points\n          });\n          polygon && polygon.setShape({\n            points: points,\n            stackedOnPoints: stackedOnPoints\n          });\n        }\n      }\n    }\n    var emphasisModel = seriesModel.getModel('emphasis');\n    var focus = emphasisModel.get('focus');\n    var blurScope = emphasisModel.get('blurScope');\n    var emphasisDisabled = emphasisModel.get('disabled');\n    polyline.useStyle(zrUtil.defaults(\n    // Use color in lineStyle first\n    lineStyleModel.getLineStyle(), {\n      fill: 'none',\n      stroke: visualColor,\n      lineJoin: 'bevel'\n    }));\n    setStatesStylesFromModel(polyline, seriesModel, 'lineStyle');\n    if (polyline.style.lineWidth > 0 && seriesModel.get(['emphasis', 'lineStyle', 'width']) === 'bolder') {\n      var emphasisLineStyle = polyline.getState('emphasis').style;\n      emphasisLineStyle.lineWidth = +polyline.style.lineWidth + 1;\n    }\n    // Needs seriesIndex for focus\n    getECData(polyline).seriesIndex = seriesModel.seriesIndex;\n    toggleHoverEmphasis(polyline, focus, blurScope, emphasisDisabled);\n    var smooth = getSmooth(seriesModel.get('smooth'));\n    var smoothMonotone = seriesModel.get('smoothMonotone');\n    polyline.setShape({\n      smooth: smooth,\n      smoothMonotone: smoothMonotone,\n      connectNulls: connectNulls\n    });\n    if (polygon) {\n      var stackedOnSeries = data.getCalculationInfo('stackedOnSeries');\n      var stackedOnSmooth = 0;\n      polygon.useStyle(zrUtil.defaults(areaStyleModel.getAreaStyle(), {\n        fill: visualColor,\n        opacity: 0.7,\n        lineJoin: 'bevel',\n        decal: data.getVisual('style').decal\n      }));\n      if (stackedOnSeries) {\n        stackedOnSmooth = getSmooth(stackedOnSeries.get('smooth'));\n      }\n      polygon.setShape({\n        smooth: smooth,\n        stackedOnSmooth: stackedOnSmooth,\n        smoothMonotone: smoothMonotone,\n        connectNulls: connectNulls\n      });\n      setStatesStylesFromModel(polygon, seriesModel, 'areaStyle');\n      // Needs seriesIndex for focus\n      getECData(polygon).seriesIndex = seriesModel.seriesIndex;\n      toggleHoverEmphasis(polygon, focus, blurScope, emphasisDisabled);\n    }\n    var changePolyState = this._changePolyState;\n    data.eachItemGraphicEl(function (el) {\n      // Switch polyline / polygon state if element changed its state.\n      el && (el.onHoverStateChange = changePolyState);\n    });\n    this._polyline.onHoverStateChange = changePolyState;\n    this._data = data;\n    // Save the coordinate system for transition animation when data changed\n    this._coordSys = coordSys;\n    this._stackedOnPoints = stackedOnPoints;\n    this._points = points;\n    this._step = step;\n    this._valueOrigin = valueOrigin;\n    if (seriesModel.get('triggerLineEvent')) {\n      this.packEventData(seriesModel, polyline);\n      polygon && this.packEventData(seriesModel, polygon);\n    }\n  };\n  LineView.prototype.packEventData = function (seriesModel, el) {\n    getECData(el).eventData = {\n      componentType: 'series',\n      componentSubType: 'line',\n      componentIndex: seriesModel.componentIndex,\n      seriesIndex: seriesModel.seriesIndex,\n      seriesName: seriesModel.name,\n      seriesType: 'line'\n    };\n  };\n  LineView.prototype.highlight = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var dataIndex = modelUtil.queryDataIndex(data, payload);\n    this._changePolyState('emphasis');\n    if (!(dataIndex instanceof Array) && dataIndex != null && dataIndex >= 0) {\n      var points = data.getLayout('points');\n      var symbol = data.getItemGraphicEl(dataIndex);\n      if (!symbol) {\n        // Create a temporary symbol if it is not exists\n        var x = points[dataIndex * 2];\n        var y = points[dataIndex * 2 + 1];\n        if (isNaN(x) || isNaN(y)) {\n          // Null data\n          return;\n        }\n        // fix #11360: shouldn't draw symbol outside clipShapeForSymbol\n        if (this._clipShapeForSymbol && !this._clipShapeForSymbol.contain(x, y)) {\n          return;\n        }\n        var zlevel = seriesModel.get('zlevel') || 0;\n        var z = seriesModel.get('z') || 0;\n        symbol = new SymbolClz(data, dataIndex);\n        symbol.x = x;\n        symbol.y = y;\n        symbol.setZ(zlevel, z);\n        // ensure label text of the temporary symbol is in front of line and area polygon\n        var symbolLabel = symbol.getSymbolPath().getTextContent();\n        if (symbolLabel) {\n          symbolLabel.zlevel = zlevel;\n          symbolLabel.z = z;\n          symbolLabel.z2 = this._polyline.z2 + 1;\n        }\n        symbol.__temp = true;\n        data.setItemGraphicEl(dataIndex, symbol);\n        // Stop scale animation\n        symbol.stopSymbolAnimation(true);\n        this.group.add(symbol);\n      }\n      symbol.highlight();\n    } else {\n      // Highlight whole series\n      ChartView.prototype.highlight.call(this, seriesModel, ecModel, api, payload);\n    }\n  };\n  LineView.prototype.downplay = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var dataIndex = modelUtil.queryDataIndex(data, payload);\n    this._changePolyState('normal');\n    if (dataIndex != null && dataIndex >= 0) {\n      var symbol = data.getItemGraphicEl(dataIndex);\n      if (symbol) {\n        if (symbol.__temp) {\n          data.setItemGraphicEl(dataIndex, null);\n          this.group.remove(symbol);\n        } else {\n          symbol.downplay();\n        }\n      }\n    } else {\n      // FIXME\n      // can not downplay completely.\n      // Downplay whole series\n      ChartView.prototype.downplay.call(this, seriesModel, ecModel, api, payload);\n    }\n  };\n  LineView.prototype._changePolyState = function (toState) {\n    var polygon = this._polygon;\n    setStatesFlag(this._polyline, toState);\n    polygon && setStatesFlag(polygon, toState);\n  };\n  LineView.prototype._newPolyline = function (points) {\n    var polyline = this._polyline;\n    // Remove previous created polyline\n    if (polyline) {\n      this._lineGroup.remove(polyline);\n    }\n    polyline = new ECPolyline({\n      shape: {\n        points: points\n      },\n      segmentIgnoreThreshold: 2,\n      z2: 10\n    });\n    this._lineGroup.add(polyline);\n    this._polyline = polyline;\n    return polyline;\n  };\n  LineView.prototype._newPolygon = function (points, stackedOnPoints) {\n    var polygon = this._polygon;\n    // Remove previous created polygon\n    if (polygon) {\n      this._lineGroup.remove(polygon);\n    }\n    polygon = new ECPolygon({\n      shape: {\n        points: points,\n        stackedOnPoints: stackedOnPoints\n      },\n      segmentIgnoreThreshold: 2\n    });\n    this._lineGroup.add(polygon);\n    this._polygon = polygon;\n    return polygon;\n  };\n  LineView.prototype._initSymbolLabelAnimation = function (data, coordSys, clipShape) {\n    var isHorizontalOrRadial;\n    var isCoordSysPolar;\n    var baseAxis = coordSys.getBaseAxis();\n    var isAxisInverse = baseAxis.inverse;\n    if (coordSys.type === 'cartesian2d') {\n      isHorizontalOrRadial = baseAxis.isHorizontal();\n      isCoordSysPolar = false;\n    } else if (coordSys.type === 'polar') {\n      isHorizontalOrRadial = baseAxis.dim === 'angle';\n      isCoordSysPolar = true;\n    }\n    var seriesModel = data.hostModel;\n    var seriesDuration = seriesModel.get('animationDuration');\n    if (zrUtil.isFunction(seriesDuration)) {\n      seriesDuration = seriesDuration(null);\n    }\n    var seriesDelay = seriesModel.get('animationDelay') || 0;\n    var seriesDelayValue = zrUtil.isFunction(seriesDelay) ? seriesDelay(null) : seriesDelay;\n    data.eachItemGraphicEl(function (symbol, idx) {\n      var el = symbol;\n      if (el) {\n        var point = [symbol.x, symbol.y];\n        var start = void 0;\n        var end = void 0;\n        var current = void 0;\n        if (clipShape) {\n          if (isCoordSysPolar) {\n            var polarClip = clipShape;\n            var coord = coordSys.pointToCoord(point);\n            if (isHorizontalOrRadial) {\n              start = polarClip.startAngle;\n              end = polarClip.endAngle;\n              current = -coord[1] / 180 * Math.PI;\n            } else {\n              start = polarClip.r0;\n              end = polarClip.r;\n              current = coord[0];\n            }\n          } else {\n            var gridClip = clipShape;\n            if (isHorizontalOrRadial) {\n              start = gridClip.x;\n              end = gridClip.x + gridClip.width;\n              current = symbol.x;\n            } else {\n              start = gridClip.y + gridClip.height;\n              end = gridClip.y;\n              current = symbol.y;\n            }\n          }\n        }\n        var ratio = end === start ? 0 : (current - start) / (end - start);\n        if (isAxisInverse) {\n          ratio = 1 - ratio;\n        }\n        var delay = zrUtil.isFunction(seriesDelay) ? seriesDelay(idx) : seriesDuration * ratio + seriesDelayValue;\n        var symbolPath = el.getSymbolPath();\n        var text = symbolPath.getTextContent();\n        el.attr({\n          scaleX: 0,\n          scaleY: 0\n        });\n        el.animateTo({\n          scaleX: 1,\n          scaleY: 1\n        }, {\n          duration: 200,\n          setToFinal: true,\n          delay: delay\n        });\n        if (text) {\n          text.animateFrom({\n            style: {\n              opacity: 0\n            }\n          }, {\n            duration: 300,\n            delay: delay\n          });\n        }\n        symbolPath.disableLabelAnimation = true;\n      }\n    });\n  };\n  LineView.prototype._initOrUpdateEndLabel = function (seriesModel, coordSys, inheritColor) {\n    var endLabelModel = seriesModel.getModel('endLabel');\n    if (anyStateShowEndLabel(seriesModel)) {\n      var data_2 = seriesModel.getData();\n      var polyline = this._polyline;\n      // series may be filtered.\n      var points = data_2.getLayout('points');\n      if (!points) {\n        polyline.removeTextContent();\n        this._endLabel = null;\n        return;\n      }\n      var endLabel = this._endLabel;\n      if (!endLabel) {\n        endLabel = this._endLabel = new graphic.Text({\n          z2: 200 // should be higher than item symbol\n        });\n        endLabel.ignoreClip = true;\n        polyline.setTextContent(this._endLabel);\n        polyline.disableLabelAnimation = true;\n      }\n      // Find last non-NaN data to display data\n      var dataIndex = getLastIndexNotNull(points);\n      if (dataIndex >= 0) {\n        setLabelStyle(polyline, getLabelStatesModels(seriesModel, 'endLabel'), {\n          inheritColor: inheritColor,\n          labelFetcher: seriesModel,\n          labelDataIndex: dataIndex,\n          defaultText: function (dataIndex, opt, interpolatedValue) {\n            return interpolatedValue != null ? getDefaultInterpolatedLabel(data_2, interpolatedValue) : getDefaultLabel(data_2, dataIndex);\n          },\n          enableTextSetter: true\n        }, getEndLabelStateSpecified(endLabelModel, coordSys));\n        polyline.textConfig.position = null;\n      }\n    } else if (this._endLabel) {\n      this._polyline.removeTextContent();\n      this._endLabel = null;\n    }\n  };\n  LineView.prototype._endLabelOnDuring = function (percent, clipRect, data, animationRecord, valueAnimation, endLabelModel, coordSys) {\n    var endLabel = this._endLabel;\n    var polyline = this._polyline;\n    if (endLabel) {\n      // NOTE: Don't remove percent < 1. percent === 1 means the first frame during render.\n      // The label is not prepared at this time.\n      if (percent < 1 && animationRecord.originalX == null) {\n        animationRecord.originalX = endLabel.x;\n        animationRecord.originalY = endLabel.y;\n      }\n      var points = data.getLayout('points');\n      var seriesModel = data.hostModel;\n      var connectNulls = seriesModel.get('connectNulls');\n      var precision = endLabelModel.get('precision');\n      var distance = endLabelModel.get('distance') || 0;\n      var baseAxis = coordSys.getBaseAxis();\n      var isHorizontal = baseAxis.isHorizontal();\n      var isBaseInversed = baseAxis.inverse;\n      var clipShape = clipRect.shape;\n      var xOrY = isBaseInversed ? isHorizontal ? clipShape.x : clipShape.y + clipShape.height : isHorizontal ? clipShape.x + clipShape.width : clipShape.y;\n      var distanceX = (isHorizontal ? distance : 0) * (isBaseInversed ? -1 : 1);\n      var distanceY = (isHorizontal ? 0 : -distance) * (isBaseInversed ? -1 : 1);\n      var dim = isHorizontal ? 'x' : 'y';\n      var dataIndexRange = getIndexRange(points, xOrY, dim);\n      var indices = dataIndexRange.range;\n      var diff = indices[1] - indices[0];\n      var value = void 0;\n      if (diff >= 1) {\n        // diff > 1 && connectNulls, which is on the null data.\n        if (diff > 1 && !connectNulls) {\n          var pt = getPointAtIndex(points, indices[0]);\n          endLabel.attr({\n            x: pt[0] + distanceX,\n            y: pt[1] + distanceY\n          });\n          valueAnimation && (value = seriesModel.getRawValue(indices[0]));\n        } else {\n          var pt = polyline.getPointOn(xOrY, dim);\n          pt && endLabel.attr({\n            x: pt[0] + distanceX,\n            y: pt[1] + distanceY\n          });\n          var startValue = seriesModel.getRawValue(indices[0]);\n          var endValue = seriesModel.getRawValue(indices[1]);\n          valueAnimation && (value = modelUtil.interpolateRawValues(data, precision, startValue, endValue, dataIndexRange.t));\n        }\n        animationRecord.lastFrameIndex = indices[0];\n      } else {\n        // If diff <= 0, which is the range is not found(Include NaN)\n        // Choose the first point or last point.\n        var idx = percent === 1 || animationRecord.lastFrameIndex > 0 ? indices[0] : 0;\n        var pt = getPointAtIndex(points, idx);\n        valueAnimation && (value = seriesModel.getRawValue(idx));\n        endLabel.attr({\n          x: pt[0] + distanceX,\n          y: pt[1] + distanceY\n        });\n      }\n      if (valueAnimation) {\n        var inner = labelInner(endLabel);\n        if (typeof inner.setLabelText === 'function') {\n          inner.setLabelText(value);\n        }\n      }\n    }\n  };\n  /**\r\n   * @private\r\n   */\n  // FIXME Two value axis\n  LineView.prototype._doUpdateAnimation = function (data, stackedOnPoints, coordSys, api, step, valueOrigin, connectNulls) {\n    var polyline = this._polyline;\n    var polygon = this._polygon;\n    var seriesModel = data.hostModel;\n    var diff = lineAnimationDiff(this._data, data, this._stackedOnPoints, stackedOnPoints, this._coordSys, coordSys, this._valueOrigin, valueOrigin);\n    var current = diff.current;\n    var stackedOnCurrent = diff.stackedOnCurrent;\n    var next = diff.next;\n    var stackedOnNext = diff.stackedOnNext;\n    if (step) {\n      // TODO If stacked series is not step\n      stackedOnCurrent = turnPointsIntoStep(diff.stackedOnCurrent, diff.current, coordSys, step, connectNulls);\n      current = turnPointsIntoStep(diff.current, null, coordSys, step, connectNulls);\n      stackedOnNext = turnPointsIntoStep(diff.stackedOnNext, diff.next, coordSys, step, connectNulls);\n      next = turnPointsIntoStep(diff.next, null, coordSys, step, connectNulls);\n    }\n    // Don't apply animation if diff is large.\n    // For better result and avoid memory explosion problems like\n    // https://github.com/apache/incubator-echarts/issues/12229\n    if (getBoundingDiff(current, next) > 3000 || polygon && getBoundingDiff(stackedOnCurrent, stackedOnNext) > 3000) {\n      polyline.stopAnimation();\n      polyline.setShape({\n        points: next\n      });\n      if (polygon) {\n        polygon.stopAnimation();\n        polygon.setShape({\n          points: next,\n          stackedOnPoints: stackedOnNext\n        });\n      }\n      return;\n    }\n    polyline.shape.__points = diff.current;\n    polyline.shape.points = current;\n    var target = {\n      shape: {\n        points: next\n      }\n    };\n    // Also animate the original points.\n    // If points reference is changed when turning into step line.\n    if (diff.current !== current) {\n      target.shape.__points = diff.next;\n    }\n    // Stop previous animation.\n    polyline.stopAnimation();\n    graphic.updateProps(polyline, target, seriesModel);\n    if (polygon) {\n      polygon.setShape({\n        // Reuse the points with polyline.\n        points: current,\n        stackedOnPoints: stackedOnCurrent\n      });\n      polygon.stopAnimation();\n      graphic.updateProps(polygon, {\n        shape: {\n          stackedOnPoints: stackedOnNext\n        }\n      }, seriesModel);\n      // If use attr directly in updateProps.\n      if (polyline.shape.points !== polygon.shape.points) {\n        polygon.shape.points = polyline.shape.points;\n      }\n    }\n    var updatedDataInfo = [];\n    var diffStatus = diff.status;\n    for (var i = 0; i < diffStatus.length; i++) {\n      var cmd = diffStatus[i].cmd;\n      if (cmd === '=') {\n        var el = data.getItemGraphicEl(diffStatus[i].idx1);\n        if (el) {\n          updatedDataInfo.push({\n            el: el,\n            ptIdx: i // Index of points\n          });\n        }\n      }\n    }\n    if (polyline.animators && polyline.animators.length) {\n      polyline.animators[0].during(function () {\n        polygon && polygon.dirtyShape();\n        var points = polyline.shape.__points;\n        for (var i = 0; i < updatedDataInfo.length; i++) {\n          var el = updatedDataInfo[i].el;\n          var offset = updatedDataInfo[i].ptIdx * 2;\n          el.x = points[offset];\n          el.y = points[offset + 1];\n          el.markRedraw();\n        }\n      });\n    }\n  };\n  LineView.prototype.remove = function (ecModel) {\n    var group = this.group;\n    var oldData = this._data;\n    this._lineGroup.removeAll();\n    this._symbolDraw.remove(true);\n    // Remove temporary created elements when highlighting\n    oldData && oldData.eachItemGraphicEl(function (el, idx) {\n      if (el.__temp) {\n        group.remove(el);\n        oldData.setItemGraphicEl(idx, null);\n      }\n    });\n    this._polyline = this._polygon = this._coordSys = this._points = this._stackedOnPoints = this._endLabel = this._data = null;\n  };\n  LineView.type = 'line';\n  return LineView;\n}(ChartView);\nexport default LineView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "SymbolDraw", "SymbolClz", "lineAnimationDiff", "graphic", "modelUtil", "ECPolyline", "ECPolygon", "ChartView", "prepareDataCoordInfo", "getStackedOnPoint", "createGridClipPath", "createPolarClipPath", "isCoordinateSystemType", "setStatesStylesFromModel", "setStatesFlag", "toggleHoverEmphasis", "SPECIAL_STATES", "setLabelStyle", "getLabelStatesModels", "labelInner", "getDefaultLabel", "getDefaultInterpolatedLabel", "getECData", "createFloat32Array", "convertToColorString", "lerp", "isPointsSame", "points1", "points2", "length", "i", "bboxFromPoints", "points", "minX", "Infinity", "minY", "maxX", "maxY", "x", "y", "isNaN", "Math", "min", "max", "getBoundingDiff", "_a", "min1", "max1", "_b", "min2", "max2", "abs", "getSmooth", "smooth", "isNumber", "getStackedOnPoints", "coordSys", "data", "dataCoordInfo", "valueDim", "len", "count", "idx", "pt", "turnPointsIntoStep", "basePoints", "stepTurnAt", "connectNulls", "baseAxis", "getBaseAxis", "baseIndex", "dim", "stepPoints", "stepPt", "nextPt", "filteredPoints", "reference", "push", "middle", "stepPt2", "clipColorStops", "colorStops", "maxSize", "newColorStops", "prevOutOfRangeColorStop", "prevInRangeColorStop", "lerpStop", "stop0", "stop1", "clippedCoord", "coord0", "coord", "p", "color", "stop_1", "getVisualGradient", "api", "visualMetaList", "getVisual", "type", "process", "env", "NODE_ENV", "console", "warn", "coordDim", "visualMeta", "dimInfo", "getDimensionInfo", "dimension", "axis", "getAxis", "map", "stops", "stop", "toGlobalCoord", "dataToCoord", "value", "stopLen", "outerColors", "slice", "reverse", "colorStopsInRange", "getWidth", "getHeight", "inRangeStopLen", "tinyExtent", "minCoord", "maxCoord", "coordSpan", "each", "offset", "unshift", "gradient", "LinearGradient", "getIsIgnoreFunc", "seriesModel", "showAllSymbol", "get", "isAuto", "categoryAxis", "getAxesByScale", "canShowAllSymbolForCategory", "categoryDataDim", "mapDimension", "labelMap", "getViewLabels", "labelItem", "ordinalNumber", "scale", "getRawOrdinalNumber", "tickValue", "dataIndex", "hasOwnProperty", "axisExtent", "getExtent", "availSize", "dataLen", "step", "round", "getSymbolSize", "isHorizontal", "isPointNull", "getLastIndexNotNull", "getPointAtIndex", "getIndexRange", "xOrY", "dimIdx", "a", "b", "prevIndex", "nextIndex", "range", "t", "anyStateShowEndLabel", "createLineClipPath", "lineView", "hasAnimation", "endLabelModel_1", "getModel", "valueAnimation_1", "data_1", "getData", "labelAnimationRecord_1", "lastFrameIndex", "during", "percent", "clipRect", "_endLabelOnDuring", "clipPath", "endLabel", "_end<PERSON>abel", "originalX", "attr", "originalY", "rectShape", "shape", "expandSize", "width", "height", "getEndLabelStateSpecified", "endLabelModel", "isBaseInversed", "inverse", "align", "verticalAlign", "normal", "LineView", "_super", "apply", "arguments", "prototype", "init", "lineGroup", "Group", "symbolDraw", "group", "add", "_symbolDraw", "_lineGroup", "_changePolyState", "bind", "render", "ecModel", "coordinateSystem", "lineStyleModel", "areaStyleModel", "getLayout", "isCoordSysPolar", "prevCoordSys", "_coordSys", "polyline", "_polyline", "polygon", "_polygon", "ssr", "is<PERSON><PERSON><PERSON><PERSON>", "isEmpty", "valueOrigin", "stackedOnPoints", "showSymbol", "isIgnoreFunc", "oldData", "_data", "eachItemGraphicEl", "el", "__temp", "remove", "setItemGraphicEl", "clipShapeForSymbol", "getArea", "r0", "r", "_clipShapeForSymbol", "visualColor", "_step", "updateData", "isIgnore", "clipShape", "disableAnimation", "getSymbolPoint", "_initSymbolLabelAnimation", "_newPolyline", "_newPolygon", "_initOrUpdateEndLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "old<PERSON><PERSON><PERSON><PERSON>", "getClipPath", "newClipPath", "initProps", "_stackedOnPoints", "_points", "_doUpdateAnimation", "setShape", "emphasisModel", "focus", "blurScope", "emphasisDisabled", "useStyle", "defaults", "getLineStyle", "fill", "stroke", "lineJoin", "style", "lineWidth", "emphasisLineStyle", "getState", "seriesIndex", "smoothMonotone", "stackedOnSeries", "getCalculationInfo", "stackedOnSmooth", "getAreaStyle", "opacity", "decal", "changePolyState", "onHoverStateChange", "_valueO<PERSON>in", "packEventData", "eventData", "componentType", "componentSubType", "componentIndex", "seriesName", "name", "seriesType", "highlight", "payload", "queryDataIndex", "Array", "symbol", "getItemGraphicEl", "contain", "zlevel", "z", "setZ", "symbol<PERSON><PERSON><PERSON>", "getSymbolPath", "getTextContent", "z2", "stopSymbolAnimation", "call", "downplay", "toState", "segmentIgnoreThreshold", "isHorizontalOrRadial", "isAxisInverse", "hostModel", "seriesDuration", "isFunction", "seriesDelay", "seriesDelayValue", "point", "start", "end", "current", "polarClip", "pointToCoord", "startAngle", "endAngle", "PI", "gridClip", "ratio", "delay", "symbolPath", "text", "scaleX", "scaleY", "animateTo", "duration", "setToFinal", "animateFrom", "disableLabelAnimation", "inheritColor", "data_2", "removeTextContent", "Text", "ignoreClip", "setTextContent", "labelFetcher", "labelDataIndex", "defaultText", "opt", "interpolatedV<PERSON>ue", "enableTextSetter", "textConfig", "position", "animationRecord", "valueAnimation", "precision", "distance", "distanceX", "distanceY", "dataIndexRange", "indices", "diff", "getRawValue", "getPointOn", "startValue", "endValue", "interpolateRawValues", "inner", "setLabelText", "stackedOnCurrent", "next", "stackedOnNext", "stopAnimation", "__points", "target", "updateProps", "updatedDataInfo", "diffStatus", "status", "cmd", "idx1", "ptIdx", "animators", "dirtyShape", "mark<PERSON><PERSON><PERSON>", "removeAll"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/line/LineView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// FIXME step not support polar\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport SymbolClz from '../helper/Symbol.js';\nimport lineAnimationDiff from './lineAnimationDiff.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as modelUtil from '../../util/model.js';\nimport { ECPolyline, ECPolygon } from './poly.js';\nimport ChartView from '../../view/Chart.js';\nimport { prepareDataCoordInfo, getStackedOnPoint } from './helper.js';\nimport { createGridClipPath, createPolarClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { setStatesStylesFromModel, setStatesFlag, toggleHoverEmphasis, SPECIAL_STATES } from '../../util/states.js';\nimport { setLabelStyle, getLabelStatesModels, labelInner } from '../../label/labelStyle.js';\nimport { getDefaultLabel, getDefaultInterpolatedLabel } from '../helper/labelHelper.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createFloat32Array } from '../../util/vendor.js';\nimport { convertToColorString } from '../../util/format.js';\nimport { lerp } from 'zrender/lib/tool/color.js';\nfunction isPointsSame(points1, points2) {\n  if (points1.length !== points2.length) {\n    return;\n  }\n  for (var i = 0; i < points1.length; i++) {\n    if (points1[i] !== points2[i]) {\n      return;\n    }\n  }\n  return true;\n}\nfunction bboxFromPoints(points) {\n  var minX = Infinity;\n  var minY = Infinity;\n  var maxX = -Infinity;\n  var maxY = -Infinity;\n  for (var i = 0; i < points.length;) {\n    var x = points[i++];\n    var y = points[i++];\n    if (!isNaN(x)) {\n      minX = Math.min(x, minX);\n      maxX = Math.max(x, maxX);\n    }\n    if (!isNaN(y)) {\n      minY = Math.min(y, minY);\n      maxY = Math.max(y, maxY);\n    }\n  }\n  return [[minX, minY], [maxX, maxY]];\n}\nfunction getBoundingDiff(points1, points2) {\n  var _a = bboxFromPoints(points1),\n    min1 = _a[0],\n    max1 = _a[1];\n  var _b = bboxFromPoints(points2),\n    min2 = _b[0],\n    max2 = _b[1];\n  // Get a max value from each corner of two boundings.\n  return Math.max(Math.abs(min1[0] - min2[0]), Math.abs(min1[1] - min2[1]), Math.abs(max1[0] - max2[0]), Math.abs(max1[1] - max2[1]));\n}\nfunction getSmooth(smooth) {\n  return zrUtil.isNumber(smooth) ? smooth : smooth ? 0.5 : 0;\n}\nfunction getStackedOnPoints(coordSys, data, dataCoordInfo) {\n  if (!dataCoordInfo.valueDim) {\n    return [];\n  }\n  var len = data.count();\n  var points = createFloat32Array(len * 2);\n  for (var idx = 0; idx < len; idx++) {\n    var pt = getStackedOnPoint(dataCoordInfo, coordSys, data, idx);\n    points[idx * 2] = pt[0];\n    points[idx * 2 + 1] = pt[1];\n  }\n  return points;\n}\n/**\r\n * Filter the null data and extend data for step considering `stepTurnAt`\r\n *\r\n * @param points data to convert, that may containing null\r\n * @param basePoints base data to reference, used only for areaStyle points\r\n * @param coordSys coordinate system\r\n * @param stepTurnAt 'start' | 'end' | 'middle' | true\r\n * @param connectNulls whether to connect nulls\r\n * @returns converted point positions\r\n */\nfunction turnPointsIntoStep(points, basePoints, coordSys, stepTurnAt, connectNulls) {\n  var baseAxis = coordSys.getBaseAxis();\n  var baseIndex = baseAxis.dim === 'x' || baseAxis.dim === 'radius' ? 0 : 1;\n  var stepPoints = [];\n  var i = 0;\n  var stepPt = [];\n  var pt = [];\n  var nextPt = [];\n  var filteredPoints = [];\n  if (connectNulls) {\n    for (i = 0; i < points.length; i += 2) {\n      /**\r\n       * For areaStyle of stepped lines, `stackedOnPoints` should be\r\n       * filtered the same as `points` so that the base axis values\r\n       * should stay the same as the lines above. See #20021\r\n       */\n      var reference = basePoints || points;\n      if (!isNaN(reference[i]) && !isNaN(reference[i + 1])) {\n        filteredPoints.push(points[i], points[i + 1]);\n      }\n    }\n    points = filteredPoints;\n  }\n  for (i = 0; i < points.length - 2; i += 2) {\n    nextPt[0] = points[i + 2];\n    nextPt[1] = points[i + 3];\n    pt[0] = points[i];\n    pt[1] = points[i + 1];\n    stepPoints.push(pt[0], pt[1]);\n    switch (stepTurnAt) {\n      case 'end':\n        stepPt[baseIndex] = nextPt[baseIndex];\n        stepPt[1 - baseIndex] = pt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n        break;\n      case 'middle':\n        var middle = (pt[baseIndex] + nextPt[baseIndex]) / 2;\n        var stepPt2 = [];\n        stepPt[baseIndex] = stepPt2[baseIndex] = middle;\n        stepPt[1 - baseIndex] = pt[1 - baseIndex];\n        stepPt2[1 - baseIndex] = nextPt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n        stepPoints.push(stepPt2[0], stepPt2[1]);\n        break;\n      default:\n        // default is start\n        stepPt[baseIndex] = pt[baseIndex];\n        stepPt[1 - baseIndex] = nextPt[1 - baseIndex];\n        stepPoints.push(stepPt[0], stepPt[1]);\n    }\n  }\n  // Last points\n  stepPoints.push(points[i++], points[i++]);\n  return stepPoints;\n}\n/**\r\n * Clip color stops to edge. Avoid creating too large gradients.\r\n * Which may lead to blurry when GPU acceleration is enabled. See #15680\r\n *\r\n * The stops has been sorted from small to large.\r\n */\nfunction clipColorStops(colorStops, maxSize) {\n  var newColorStops = [];\n  var len = colorStops.length;\n  // coord will always < 0 in prevOutOfRangeColorStop.\n  var prevOutOfRangeColorStop;\n  var prevInRangeColorStop;\n  function lerpStop(stop0, stop1, clippedCoord) {\n    var coord0 = stop0.coord;\n    var p = (clippedCoord - coord0) / (stop1.coord - coord0);\n    var color = lerp(p, [stop0.color, stop1.color]);\n    return {\n      coord: clippedCoord,\n      color: color\n    };\n  }\n  for (var i = 0; i < len; i++) {\n    var stop_1 = colorStops[i];\n    var coord = stop_1.coord;\n    if (coord < 0) {\n      prevOutOfRangeColorStop = stop_1;\n    } else if (coord > maxSize) {\n      if (prevInRangeColorStop) {\n        newColorStops.push(lerpStop(prevInRangeColorStop, stop_1, maxSize));\n      } else if (prevOutOfRangeColorStop) {\n        // If there are two stops and coord range is between these two stops\n        newColorStops.push(lerpStop(prevOutOfRangeColorStop, stop_1, 0), lerpStop(prevOutOfRangeColorStop, stop_1, maxSize));\n      }\n      // All following stop will be out of range. So just ignore them.\n      break;\n    } else {\n      if (prevOutOfRangeColorStop) {\n        newColorStops.push(lerpStop(prevOutOfRangeColorStop, stop_1, 0));\n        // Reset\n        prevOutOfRangeColorStop = null;\n      }\n      newColorStops.push(stop_1);\n      prevInRangeColorStop = stop_1;\n    }\n  }\n  return newColorStops;\n}\nfunction getVisualGradient(data, coordSys, api) {\n  var visualMetaList = data.getVisual('visualMeta');\n  if (!visualMetaList || !visualMetaList.length || !data.count()) {\n    // When data.count() is 0, gradient range can not be calculated.\n    return;\n  }\n  if (coordSys.type !== 'cartesian2d') {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Visual map on line style is only supported on cartesian2d.');\n    }\n    return;\n  }\n  var coordDim;\n  var visualMeta;\n  for (var i = visualMetaList.length - 1; i >= 0; i--) {\n    var dimInfo = data.getDimensionInfo(visualMetaList[i].dimension);\n    coordDim = dimInfo && dimInfo.coordDim;\n    // Can only be x or y\n    if (coordDim === 'x' || coordDim === 'y') {\n      visualMeta = visualMetaList[i];\n      break;\n    }\n  }\n  if (!visualMeta) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Visual map on line style only support x or y dimension.');\n    }\n    return;\n  }\n  // If the area to be rendered is bigger than area defined by LinearGradient,\n  // the canvas spec prescribes that the color of the first stop and the last\n  // stop should be used. But if two stops are added at offset 0, in effect\n  // browsers use the color of the second stop to render area outside\n  // LinearGradient. So we can only infinitesimally extend area defined in\n  // LinearGradient to render `outerColors`.\n  var axis = coordSys.getAxis(coordDim);\n  // dataToCoord mapping may not be linear, but must be monotonic.\n  var colorStops = zrUtil.map(visualMeta.stops, function (stop) {\n    // offset will be calculated later.\n    return {\n      coord: axis.toGlobalCoord(axis.dataToCoord(stop.value)),\n      color: stop.color\n    };\n  });\n  var stopLen = colorStops.length;\n  var outerColors = visualMeta.outerColors.slice();\n  if (stopLen && colorStops[0].coord > colorStops[stopLen - 1].coord) {\n    colorStops.reverse();\n    outerColors.reverse();\n  }\n  var colorStopsInRange = clipColorStops(colorStops, coordDim === 'x' ? api.getWidth() : api.getHeight());\n  var inRangeStopLen = colorStopsInRange.length;\n  if (!inRangeStopLen && stopLen) {\n    // All stops are out of range. All will be the same color.\n    return colorStops[0].coord < 0 ? outerColors[1] ? outerColors[1] : colorStops[stopLen - 1].color : outerColors[0] ? outerColors[0] : colorStops[0].color;\n  }\n  var tinyExtent = 10; // Arbitrary value: 10px\n  var minCoord = colorStopsInRange[0].coord - tinyExtent;\n  var maxCoord = colorStopsInRange[inRangeStopLen - 1].coord + tinyExtent;\n  var coordSpan = maxCoord - minCoord;\n  if (coordSpan < 1e-3) {\n    return 'transparent';\n  }\n  zrUtil.each(colorStopsInRange, function (stop) {\n    stop.offset = (stop.coord - minCoord) / coordSpan;\n  });\n  colorStopsInRange.push({\n    // NOTE: inRangeStopLen may still be 0 if stoplen is zero.\n    offset: inRangeStopLen ? colorStopsInRange[inRangeStopLen - 1].offset : 0.5,\n    color: outerColors[1] || 'transparent'\n  });\n  colorStopsInRange.unshift({\n    offset: inRangeStopLen ? colorStopsInRange[0].offset : 0.5,\n    color: outerColors[0] || 'transparent'\n  });\n  var gradient = new graphic.LinearGradient(0, 0, 0, 0, colorStopsInRange, true);\n  gradient[coordDim] = minCoord;\n  gradient[coordDim + '2'] = maxCoord;\n  return gradient;\n}\nfunction getIsIgnoreFunc(seriesModel, data, coordSys) {\n  var showAllSymbol = seriesModel.get('showAllSymbol');\n  var isAuto = showAllSymbol === 'auto';\n  if (showAllSymbol && !isAuto) {\n    return;\n  }\n  var categoryAxis = coordSys.getAxesByScale('ordinal')[0];\n  if (!categoryAxis) {\n    return;\n  }\n  // Note that category label interval strategy might bring some weird effect\n  // in some scenario: users may wonder why some of the symbols are not\n  // displayed. So we show all symbols as possible as we can.\n  if (isAuto\n  // Simplify the logic, do not determine label overlap here.\n  && canShowAllSymbolForCategory(categoryAxis, data)) {\n    return;\n  }\n  // Otherwise follow the label interval strategy on category axis.\n  var categoryDataDim = data.mapDimension(categoryAxis.dim);\n  var labelMap = {};\n  zrUtil.each(categoryAxis.getViewLabels(), function (labelItem) {\n    var ordinalNumber = categoryAxis.scale.getRawOrdinalNumber(labelItem.tickValue);\n    labelMap[ordinalNumber] = 1;\n  });\n  return function (dataIndex) {\n    return !labelMap.hasOwnProperty(data.get(categoryDataDim, dataIndex));\n  };\n}\nfunction canShowAllSymbolForCategory(categoryAxis, data) {\n  // In most cases, line is monotonous on category axis, and the label size\n  // is close with each other. So we check the symbol size and some of the\n  // label size alone with the category axis to estimate whether all symbol\n  // can be shown without overlap.\n  var axisExtent = categoryAxis.getExtent();\n  var availSize = Math.abs(axisExtent[1] - axisExtent[0]) / categoryAxis.scale.count();\n  isNaN(availSize) && (availSize = 0); // 0/0 is NaN.\n  // Sampling some points, max 5.\n  var dataLen = data.count();\n  var step = Math.max(1, Math.round(dataLen / 5));\n  for (var dataIndex = 0; dataIndex < dataLen; dataIndex += step) {\n    if (SymbolClz.getSymbolSize(data, dataIndex\n    // Only for cartesian, where `isHorizontal` exists.\n    )[categoryAxis.isHorizontal() ? 1 : 0]\n    // Empirical number\n    * 1.5 > availSize) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPointNull(x, y) {\n  return isNaN(x) || isNaN(y);\n}\nfunction getLastIndexNotNull(points) {\n  var len = points.length / 2;\n  for (; len > 0; len--) {\n    if (!isPointNull(points[len * 2 - 2], points[len * 2 - 1])) {\n      break;\n    }\n  }\n  return len - 1;\n}\nfunction getPointAtIndex(points, idx) {\n  return [points[idx * 2], points[idx * 2 + 1]];\n}\nfunction getIndexRange(points, xOrY, dim) {\n  var len = points.length / 2;\n  var dimIdx = dim === 'x' ? 0 : 1;\n  var a;\n  var b;\n  var prevIndex = 0;\n  var nextIndex = -1;\n  for (var i = 0; i < len; i++) {\n    b = points[i * 2 + dimIdx];\n    if (isNaN(b) || isNaN(points[i * 2 + 1 - dimIdx])) {\n      continue;\n    }\n    if (i === 0) {\n      a = b;\n      continue;\n    }\n    if (a <= xOrY && b >= xOrY || a >= xOrY && b <= xOrY) {\n      nextIndex = i;\n      break;\n    }\n    prevIndex = i;\n    a = b;\n  }\n  return {\n    range: [prevIndex, nextIndex],\n    t: (xOrY - a) / (b - a)\n  };\n}\nfunction anyStateShowEndLabel(seriesModel) {\n  if (seriesModel.get(['endLabel', 'show'])) {\n    return true;\n  }\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    if (seriesModel.get([SPECIAL_STATES[i], 'endLabel', 'show'])) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction createLineClipPath(lineView, coordSys, hasAnimation, seriesModel) {\n  if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n    var endLabelModel_1 = seriesModel.getModel('endLabel');\n    var valueAnimation_1 = endLabelModel_1.get('valueAnimation');\n    var data_1 = seriesModel.getData();\n    var labelAnimationRecord_1 = {\n      lastFrameIndex: 0\n    };\n    var during = anyStateShowEndLabel(seriesModel) ? function (percent, clipRect) {\n      lineView._endLabelOnDuring(percent, clipRect, data_1, labelAnimationRecord_1, valueAnimation_1, endLabelModel_1, coordSys);\n    } : null;\n    var isHorizontal = coordSys.getBaseAxis().isHorizontal();\n    var clipPath = createGridClipPath(coordSys, hasAnimation, seriesModel, function () {\n      var endLabel = lineView._endLabel;\n      if (endLabel && hasAnimation) {\n        if (labelAnimationRecord_1.originalX != null) {\n          endLabel.attr({\n            x: labelAnimationRecord_1.originalX,\n            y: labelAnimationRecord_1.originalY\n          });\n        }\n      }\n    }, during);\n    // Expand clip shape to avoid clipping when line value exceeds axis\n    if (!seriesModel.get('clip', true)) {\n      var rectShape = clipPath.shape;\n      var expandSize = Math.max(rectShape.width, rectShape.height);\n      if (isHorizontal) {\n        rectShape.y -= expandSize;\n        rectShape.height += expandSize * 2;\n      } else {\n        rectShape.x -= expandSize;\n        rectShape.width += expandSize * 2;\n      }\n    }\n    // Set to the final frame. To make sure label layout is right.\n    if (during) {\n      during(1, clipPath);\n    }\n    return clipPath;\n  } else {\n    if (process.env.NODE_ENV !== 'production') {\n      if (seriesModel.get(['endLabel', 'show'])) {\n        console.warn('endLabel is not supported for lines in polar systems.');\n      }\n    }\n    return createPolarClipPath(coordSys, hasAnimation, seriesModel);\n  }\n}\nfunction getEndLabelStateSpecified(endLabelModel, coordSys) {\n  var baseAxis = coordSys.getBaseAxis();\n  var isHorizontal = baseAxis.isHorizontal();\n  var isBaseInversed = baseAxis.inverse;\n  var align = isHorizontal ? isBaseInversed ? 'right' : 'left' : 'center';\n  var verticalAlign = isHorizontal ? 'middle' : isBaseInversed ? 'top' : 'bottom';\n  return {\n    normal: {\n      align: endLabelModel.get('align') || align,\n      verticalAlign: endLabelModel.get('verticalAlign') || verticalAlign\n    }\n  };\n}\nvar LineView = /** @class */function (_super) {\n  __extends(LineView, _super);\n  function LineView() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  LineView.prototype.init = function () {\n    var lineGroup = new graphic.Group();\n    var symbolDraw = new SymbolDraw();\n    this.group.add(symbolDraw.group);\n    this._symbolDraw = symbolDraw;\n    this._lineGroup = lineGroup;\n    this._changePolyState = zrUtil.bind(this._changePolyState, this);\n  };\n  LineView.prototype.render = function (seriesModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var group = this.group;\n    var data = seriesModel.getData();\n    var lineStyleModel = seriesModel.getModel('lineStyle');\n    var areaStyleModel = seriesModel.getModel('areaStyle');\n    var points = data.getLayout('points') || [];\n    var isCoordSysPolar = coordSys.type === 'polar';\n    var prevCoordSys = this._coordSys;\n    var symbolDraw = this._symbolDraw;\n    var polyline = this._polyline;\n    var polygon = this._polygon;\n    var lineGroup = this._lineGroup;\n    var hasAnimation = !ecModel.ssr && seriesModel.get('animation');\n    var isAreaChart = !areaStyleModel.isEmpty();\n    var valueOrigin = areaStyleModel.get('origin');\n    var dataCoordInfo = prepareDataCoordInfo(coordSys, data, valueOrigin);\n    var stackedOnPoints = isAreaChart && getStackedOnPoints(coordSys, data, dataCoordInfo);\n    var showSymbol = seriesModel.get('showSymbol');\n    var connectNulls = seriesModel.get('connectNulls');\n    var isIgnoreFunc = showSymbol && !isCoordSysPolar && getIsIgnoreFunc(seriesModel, data, coordSys);\n    // Remove temporary symbols\n    var oldData = this._data;\n    oldData && oldData.eachItemGraphicEl(function (el, idx) {\n      if (el.__temp) {\n        group.remove(el);\n        oldData.setItemGraphicEl(idx, null);\n      }\n    });\n    // Remove previous created symbols if showSymbol changed to false\n    if (!showSymbol) {\n      symbolDraw.remove();\n    }\n    group.add(lineGroup);\n    // FIXME step not support polar\n    var step = !isCoordSysPolar ? seriesModel.get('step') : false;\n    var clipShapeForSymbol;\n    if (coordSys && coordSys.getArea && seriesModel.get('clip', true)) {\n      clipShapeForSymbol = coordSys.getArea();\n      // Avoid float number rounding error for symbol on the edge of axis extent.\n      // See #7913 and `test/dataZoom-clip.html`.\n      if (clipShapeForSymbol.width != null) {\n        clipShapeForSymbol.x -= 0.1;\n        clipShapeForSymbol.y -= 0.1;\n        clipShapeForSymbol.width += 0.2;\n        clipShapeForSymbol.height += 0.2;\n      } else if (clipShapeForSymbol.r0) {\n        clipShapeForSymbol.r0 -= 0.5;\n        clipShapeForSymbol.r += 0.5;\n      }\n    }\n    this._clipShapeForSymbol = clipShapeForSymbol;\n    var visualColor = getVisualGradient(data, coordSys, api) || data.getVisual('style')[data.getVisual('drawType')];\n    // Initialization animation or coordinate system changed\n    if (!(polyline && prevCoordSys.type === coordSys.type && step === this._step)) {\n      showSymbol && symbolDraw.updateData(data, {\n        isIgnore: isIgnoreFunc,\n        clipShape: clipShapeForSymbol,\n        disableAnimation: true,\n        getSymbolPoint: function (idx) {\n          return [points[idx * 2], points[idx * 2 + 1]];\n        }\n      });\n      hasAnimation && this._initSymbolLabelAnimation(data, coordSys, clipShapeForSymbol);\n      if (step) {\n        if (stackedOnPoints) {\n          stackedOnPoints = turnPointsIntoStep(stackedOnPoints, points, coordSys, step, connectNulls);\n        }\n        // TODO If stacked series is not step\n        points = turnPointsIntoStep(points, null, coordSys, step, connectNulls);\n      }\n      polyline = this._newPolyline(points);\n      if (isAreaChart) {\n        polygon = this._newPolygon(points, stackedOnPoints);\n      } // If areaStyle is removed\n      else if (polygon) {\n        lineGroup.remove(polygon);\n        polygon = this._polygon = null;\n      }\n      // NOTE: Must update _endLabel before setClipPath.\n      if (!isCoordSysPolar) {\n        this._initOrUpdateEndLabel(seriesModel, coordSys, convertToColorString(visualColor));\n      }\n      lineGroup.setClipPath(createLineClipPath(this, coordSys, true, seriesModel));\n    } else {\n      if (isAreaChart && !polygon) {\n        // If areaStyle is added\n        polygon = this._newPolygon(points, stackedOnPoints);\n      } else if (polygon && !isAreaChart) {\n        // If areaStyle is removed\n        lineGroup.remove(polygon);\n        polygon = this._polygon = null;\n      }\n      // NOTE: Must update _endLabel before setClipPath.\n      if (!isCoordSysPolar) {\n        this._initOrUpdateEndLabel(seriesModel, coordSys, convertToColorString(visualColor));\n      }\n      // Update clipPath\n      var oldClipPath = lineGroup.getClipPath();\n      if (oldClipPath) {\n        var newClipPath = createLineClipPath(this, coordSys, false, seriesModel);\n        graphic.initProps(oldClipPath, {\n          shape: newClipPath.shape\n        }, seriesModel);\n      } else {\n        lineGroup.setClipPath(createLineClipPath(this, coordSys, true, seriesModel));\n      }\n      // Always update, or it is wrong in the case turning on legend\n      // because points are not changed.\n      showSymbol && symbolDraw.updateData(data, {\n        isIgnore: isIgnoreFunc,\n        clipShape: clipShapeForSymbol,\n        disableAnimation: true,\n        getSymbolPoint: function (idx) {\n          return [points[idx * 2], points[idx * 2 + 1]];\n        }\n      });\n      // In the case data zoom triggered refreshing frequently\n      // Data may not change if line has a category axis. So it should animate nothing.\n      if (!isPointsSame(this._stackedOnPoints, stackedOnPoints) || !isPointsSame(this._points, points)) {\n        if (hasAnimation) {\n          this._doUpdateAnimation(data, stackedOnPoints, coordSys, api, step, valueOrigin, connectNulls);\n        } else {\n          // Not do it in update with animation\n          if (step) {\n            if (stackedOnPoints) {\n              stackedOnPoints = turnPointsIntoStep(stackedOnPoints, points, coordSys, step, connectNulls);\n            }\n            // TODO If stacked series is not step\n            points = turnPointsIntoStep(points, null, coordSys, step, connectNulls);\n          }\n          polyline.setShape({\n            points: points\n          });\n          polygon && polygon.setShape({\n            points: points,\n            stackedOnPoints: stackedOnPoints\n          });\n        }\n      }\n    }\n    var emphasisModel = seriesModel.getModel('emphasis');\n    var focus = emphasisModel.get('focus');\n    var blurScope = emphasisModel.get('blurScope');\n    var emphasisDisabled = emphasisModel.get('disabled');\n    polyline.useStyle(zrUtil.defaults(\n    // Use color in lineStyle first\n    lineStyleModel.getLineStyle(), {\n      fill: 'none',\n      stroke: visualColor,\n      lineJoin: 'bevel'\n    }));\n    setStatesStylesFromModel(polyline, seriesModel, 'lineStyle');\n    if (polyline.style.lineWidth > 0 && seriesModel.get(['emphasis', 'lineStyle', 'width']) === 'bolder') {\n      var emphasisLineStyle = polyline.getState('emphasis').style;\n      emphasisLineStyle.lineWidth = +polyline.style.lineWidth + 1;\n    }\n    // Needs seriesIndex for focus\n    getECData(polyline).seriesIndex = seriesModel.seriesIndex;\n    toggleHoverEmphasis(polyline, focus, blurScope, emphasisDisabled);\n    var smooth = getSmooth(seriesModel.get('smooth'));\n    var smoothMonotone = seriesModel.get('smoothMonotone');\n    polyline.setShape({\n      smooth: smooth,\n      smoothMonotone: smoothMonotone,\n      connectNulls: connectNulls\n    });\n    if (polygon) {\n      var stackedOnSeries = data.getCalculationInfo('stackedOnSeries');\n      var stackedOnSmooth = 0;\n      polygon.useStyle(zrUtil.defaults(areaStyleModel.getAreaStyle(), {\n        fill: visualColor,\n        opacity: 0.7,\n        lineJoin: 'bevel',\n        decal: data.getVisual('style').decal\n      }));\n      if (stackedOnSeries) {\n        stackedOnSmooth = getSmooth(stackedOnSeries.get('smooth'));\n      }\n      polygon.setShape({\n        smooth: smooth,\n        stackedOnSmooth: stackedOnSmooth,\n        smoothMonotone: smoothMonotone,\n        connectNulls: connectNulls\n      });\n      setStatesStylesFromModel(polygon, seriesModel, 'areaStyle');\n      // Needs seriesIndex for focus\n      getECData(polygon).seriesIndex = seriesModel.seriesIndex;\n      toggleHoverEmphasis(polygon, focus, blurScope, emphasisDisabled);\n    }\n    var changePolyState = this._changePolyState;\n    data.eachItemGraphicEl(function (el) {\n      // Switch polyline / polygon state if element changed its state.\n      el && (el.onHoverStateChange = changePolyState);\n    });\n    this._polyline.onHoverStateChange = changePolyState;\n    this._data = data;\n    // Save the coordinate system for transition animation when data changed\n    this._coordSys = coordSys;\n    this._stackedOnPoints = stackedOnPoints;\n    this._points = points;\n    this._step = step;\n    this._valueOrigin = valueOrigin;\n    if (seriesModel.get('triggerLineEvent')) {\n      this.packEventData(seriesModel, polyline);\n      polygon && this.packEventData(seriesModel, polygon);\n    }\n  };\n  LineView.prototype.packEventData = function (seriesModel, el) {\n    getECData(el).eventData = {\n      componentType: 'series',\n      componentSubType: 'line',\n      componentIndex: seriesModel.componentIndex,\n      seriesIndex: seriesModel.seriesIndex,\n      seriesName: seriesModel.name,\n      seriesType: 'line'\n    };\n  };\n  LineView.prototype.highlight = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var dataIndex = modelUtil.queryDataIndex(data, payload);\n    this._changePolyState('emphasis');\n    if (!(dataIndex instanceof Array) && dataIndex != null && dataIndex >= 0) {\n      var points = data.getLayout('points');\n      var symbol = data.getItemGraphicEl(dataIndex);\n      if (!symbol) {\n        // Create a temporary symbol if it is not exists\n        var x = points[dataIndex * 2];\n        var y = points[dataIndex * 2 + 1];\n        if (isNaN(x) || isNaN(y)) {\n          // Null data\n          return;\n        }\n        // fix #11360: shouldn't draw symbol outside clipShapeForSymbol\n        if (this._clipShapeForSymbol && !this._clipShapeForSymbol.contain(x, y)) {\n          return;\n        }\n        var zlevel = seriesModel.get('zlevel') || 0;\n        var z = seriesModel.get('z') || 0;\n        symbol = new SymbolClz(data, dataIndex);\n        symbol.x = x;\n        symbol.y = y;\n        symbol.setZ(zlevel, z);\n        // ensure label text of the temporary symbol is in front of line and area polygon\n        var symbolLabel = symbol.getSymbolPath().getTextContent();\n        if (symbolLabel) {\n          symbolLabel.zlevel = zlevel;\n          symbolLabel.z = z;\n          symbolLabel.z2 = this._polyline.z2 + 1;\n        }\n        symbol.__temp = true;\n        data.setItemGraphicEl(dataIndex, symbol);\n        // Stop scale animation\n        symbol.stopSymbolAnimation(true);\n        this.group.add(symbol);\n      }\n      symbol.highlight();\n    } else {\n      // Highlight whole series\n      ChartView.prototype.highlight.call(this, seriesModel, ecModel, api, payload);\n    }\n  };\n  LineView.prototype.downplay = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData();\n    var dataIndex = modelUtil.queryDataIndex(data, payload);\n    this._changePolyState('normal');\n    if (dataIndex != null && dataIndex >= 0) {\n      var symbol = data.getItemGraphicEl(dataIndex);\n      if (symbol) {\n        if (symbol.__temp) {\n          data.setItemGraphicEl(dataIndex, null);\n          this.group.remove(symbol);\n        } else {\n          symbol.downplay();\n        }\n      }\n    } else {\n      // FIXME\n      // can not downplay completely.\n      // Downplay whole series\n      ChartView.prototype.downplay.call(this, seriesModel, ecModel, api, payload);\n    }\n  };\n  LineView.prototype._changePolyState = function (toState) {\n    var polygon = this._polygon;\n    setStatesFlag(this._polyline, toState);\n    polygon && setStatesFlag(polygon, toState);\n  };\n  LineView.prototype._newPolyline = function (points) {\n    var polyline = this._polyline;\n    // Remove previous created polyline\n    if (polyline) {\n      this._lineGroup.remove(polyline);\n    }\n    polyline = new ECPolyline({\n      shape: {\n        points: points\n      },\n      segmentIgnoreThreshold: 2,\n      z2: 10\n    });\n    this._lineGroup.add(polyline);\n    this._polyline = polyline;\n    return polyline;\n  };\n  LineView.prototype._newPolygon = function (points, stackedOnPoints) {\n    var polygon = this._polygon;\n    // Remove previous created polygon\n    if (polygon) {\n      this._lineGroup.remove(polygon);\n    }\n    polygon = new ECPolygon({\n      shape: {\n        points: points,\n        stackedOnPoints: stackedOnPoints\n      },\n      segmentIgnoreThreshold: 2\n    });\n    this._lineGroup.add(polygon);\n    this._polygon = polygon;\n    return polygon;\n  };\n  LineView.prototype._initSymbolLabelAnimation = function (data, coordSys, clipShape) {\n    var isHorizontalOrRadial;\n    var isCoordSysPolar;\n    var baseAxis = coordSys.getBaseAxis();\n    var isAxisInverse = baseAxis.inverse;\n    if (coordSys.type === 'cartesian2d') {\n      isHorizontalOrRadial = baseAxis.isHorizontal();\n      isCoordSysPolar = false;\n    } else if (coordSys.type === 'polar') {\n      isHorizontalOrRadial = baseAxis.dim === 'angle';\n      isCoordSysPolar = true;\n    }\n    var seriesModel = data.hostModel;\n    var seriesDuration = seriesModel.get('animationDuration');\n    if (zrUtil.isFunction(seriesDuration)) {\n      seriesDuration = seriesDuration(null);\n    }\n    var seriesDelay = seriesModel.get('animationDelay') || 0;\n    var seriesDelayValue = zrUtil.isFunction(seriesDelay) ? seriesDelay(null) : seriesDelay;\n    data.eachItemGraphicEl(function (symbol, idx) {\n      var el = symbol;\n      if (el) {\n        var point = [symbol.x, symbol.y];\n        var start = void 0;\n        var end = void 0;\n        var current = void 0;\n        if (clipShape) {\n          if (isCoordSysPolar) {\n            var polarClip = clipShape;\n            var coord = coordSys.pointToCoord(point);\n            if (isHorizontalOrRadial) {\n              start = polarClip.startAngle;\n              end = polarClip.endAngle;\n              current = -coord[1] / 180 * Math.PI;\n            } else {\n              start = polarClip.r0;\n              end = polarClip.r;\n              current = coord[0];\n            }\n          } else {\n            var gridClip = clipShape;\n            if (isHorizontalOrRadial) {\n              start = gridClip.x;\n              end = gridClip.x + gridClip.width;\n              current = symbol.x;\n            } else {\n              start = gridClip.y + gridClip.height;\n              end = gridClip.y;\n              current = symbol.y;\n            }\n          }\n        }\n        var ratio = end === start ? 0 : (current - start) / (end - start);\n        if (isAxisInverse) {\n          ratio = 1 - ratio;\n        }\n        var delay = zrUtil.isFunction(seriesDelay) ? seriesDelay(idx) : seriesDuration * ratio + seriesDelayValue;\n        var symbolPath = el.getSymbolPath();\n        var text = symbolPath.getTextContent();\n        el.attr({\n          scaleX: 0,\n          scaleY: 0\n        });\n        el.animateTo({\n          scaleX: 1,\n          scaleY: 1\n        }, {\n          duration: 200,\n          setToFinal: true,\n          delay: delay\n        });\n        if (text) {\n          text.animateFrom({\n            style: {\n              opacity: 0\n            }\n          }, {\n            duration: 300,\n            delay: delay\n          });\n        }\n        symbolPath.disableLabelAnimation = true;\n      }\n    });\n  };\n  LineView.prototype._initOrUpdateEndLabel = function (seriesModel, coordSys, inheritColor) {\n    var endLabelModel = seriesModel.getModel('endLabel');\n    if (anyStateShowEndLabel(seriesModel)) {\n      var data_2 = seriesModel.getData();\n      var polyline = this._polyline;\n      // series may be filtered.\n      var points = data_2.getLayout('points');\n      if (!points) {\n        polyline.removeTextContent();\n        this._endLabel = null;\n        return;\n      }\n      var endLabel = this._endLabel;\n      if (!endLabel) {\n        endLabel = this._endLabel = new graphic.Text({\n          z2: 200 // should be higher than item symbol\n        });\n        endLabel.ignoreClip = true;\n        polyline.setTextContent(this._endLabel);\n        polyline.disableLabelAnimation = true;\n      }\n      // Find last non-NaN data to display data\n      var dataIndex = getLastIndexNotNull(points);\n      if (dataIndex >= 0) {\n        setLabelStyle(polyline, getLabelStatesModels(seriesModel, 'endLabel'), {\n          inheritColor: inheritColor,\n          labelFetcher: seriesModel,\n          labelDataIndex: dataIndex,\n          defaultText: function (dataIndex, opt, interpolatedValue) {\n            return interpolatedValue != null ? getDefaultInterpolatedLabel(data_2, interpolatedValue) : getDefaultLabel(data_2, dataIndex);\n          },\n          enableTextSetter: true\n        }, getEndLabelStateSpecified(endLabelModel, coordSys));\n        polyline.textConfig.position = null;\n      }\n    } else if (this._endLabel) {\n      this._polyline.removeTextContent();\n      this._endLabel = null;\n    }\n  };\n  LineView.prototype._endLabelOnDuring = function (percent, clipRect, data, animationRecord, valueAnimation, endLabelModel, coordSys) {\n    var endLabel = this._endLabel;\n    var polyline = this._polyline;\n    if (endLabel) {\n      // NOTE: Don't remove percent < 1. percent === 1 means the first frame during render.\n      // The label is not prepared at this time.\n      if (percent < 1 && animationRecord.originalX == null) {\n        animationRecord.originalX = endLabel.x;\n        animationRecord.originalY = endLabel.y;\n      }\n      var points = data.getLayout('points');\n      var seriesModel = data.hostModel;\n      var connectNulls = seriesModel.get('connectNulls');\n      var precision = endLabelModel.get('precision');\n      var distance = endLabelModel.get('distance') || 0;\n      var baseAxis = coordSys.getBaseAxis();\n      var isHorizontal = baseAxis.isHorizontal();\n      var isBaseInversed = baseAxis.inverse;\n      var clipShape = clipRect.shape;\n      var xOrY = isBaseInversed ? isHorizontal ? clipShape.x : clipShape.y + clipShape.height : isHorizontal ? clipShape.x + clipShape.width : clipShape.y;\n      var distanceX = (isHorizontal ? distance : 0) * (isBaseInversed ? -1 : 1);\n      var distanceY = (isHorizontal ? 0 : -distance) * (isBaseInversed ? -1 : 1);\n      var dim = isHorizontal ? 'x' : 'y';\n      var dataIndexRange = getIndexRange(points, xOrY, dim);\n      var indices = dataIndexRange.range;\n      var diff = indices[1] - indices[0];\n      var value = void 0;\n      if (diff >= 1) {\n        // diff > 1 && connectNulls, which is on the null data.\n        if (diff > 1 && !connectNulls) {\n          var pt = getPointAtIndex(points, indices[0]);\n          endLabel.attr({\n            x: pt[0] + distanceX,\n            y: pt[1] + distanceY\n          });\n          valueAnimation && (value = seriesModel.getRawValue(indices[0]));\n        } else {\n          var pt = polyline.getPointOn(xOrY, dim);\n          pt && endLabel.attr({\n            x: pt[0] + distanceX,\n            y: pt[1] + distanceY\n          });\n          var startValue = seriesModel.getRawValue(indices[0]);\n          var endValue = seriesModel.getRawValue(indices[1]);\n          valueAnimation && (value = modelUtil.interpolateRawValues(data, precision, startValue, endValue, dataIndexRange.t));\n        }\n        animationRecord.lastFrameIndex = indices[0];\n      } else {\n        // If diff <= 0, which is the range is not found(Include NaN)\n        // Choose the first point or last point.\n        var idx = percent === 1 || animationRecord.lastFrameIndex > 0 ? indices[0] : 0;\n        var pt = getPointAtIndex(points, idx);\n        valueAnimation && (value = seriesModel.getRawValue(idx));\n        endLabel.attr({\n          x: pt[0] + distanceX,\n          y: pt[1] + distanceY\n        });\n      }\n      if (valueAnimation) {\n        var inner = labelInner(endLabel);\n        if (typeof inner.setLabelText === 'function') {\n          inner.setLabelText(value);\n        }\n      }\n    }\n  };\n  /**\r\n   * @private\r\n   */\n  // FIXME Two value axis\n  LineView.prototype._doUpdateAnimation = function (data, stackedOnPoints, coordSys, api, step, valueOrigin, connectNulls) {\n    var polyline = this._polyline;\n    var polygon = this._polygon;\n    var seriesModel = data.hostModel;\n    var diff = lineAnimationDiff(this._data, data, this._stackedOnPoints, stackedOnPoints, this._coordSys, coordSys, this._valueOrigin, valueOrigin);\n    var current = diff.current;\n    var stackedOnCurrent = diff.stackedOnCurrent;\n    var next = diff.next;\n    var stackedOnNext = diff.stackedOnNext;\n    if (step) {\n      // TODO If stacked series is not step\n      stackedOnCurrent = turnPointsIntoStep(diff.stackedOnCurrent, diff.current, coordSys, step, connectNulls);\n      current = turnPointsIntoStep(diff.current, null, coordSys, step, connectNulls);\n      stackedOnNext = turnPointsIntoStep(diff.stackedOnNext, diff.next, coordSys, step, connectNulls);\n      next = turnPointsIntoStep(diff.next, null, coordSys, step, connectNulls);\n    }\n    // Don't apply animation if diff is large.\n    // For better result and avoid memory explosion problems like\n    // https://github.com/apache/incubator-echarts/issues/12229\n    if (getBoundingDiff(current, next) > 3000 || polygon && getBoundingDiff(stackedOnCurrent, stackedOnNext) > 3000) {\n      polyline.stopAnimation();\n      polyline.setShape({\n        points: next\n      });\n      if (polygon) {\n        polygon.stopAnimation();\n        polygon.setShape({\n          points: next,\n          stackedOnPoints: stackedOnNext\n        });\n      }\n      return;\n    }\n    polyline.shape.__points = diff.current;\n    polyline.shape.points = current;\n    var target = {\n      shape: {\n        points: next\n      }\n    };\n    // Also animate the original points.\n    // If points reference is changed when turning into step line.\n    if (diff.current !== current) {\n      target.shape.__points = diff.next;\n    }\n    // Stop previous animation.\n    polyline.stopAnimation();\n    graphic.updateProps(polyline, target, seriesModel);\n    if (polygon) {\n      polygon.setShape({\n        // Reuse the points with polyline.\n        points: current,\n        stackedOnPoints: stackedOnCurrent\n      });\n      polygon.stopAnimation();\n      graphic.updateProps(polygon, {\n        shape: {\n          stackedOnPoints: stackedOnNext\n        }\n      }, seriesModel);\n      // If use attr directly in updateProps.\n      if (polyline.shape.points !== polygon.shape.points) {\n        polygon.shape.points = polyline.shape.points;\n      }\n    }\n    var updatedDataInfo = [];\n    var diffStatus = diff.status;\n    for (var i = 0; i < diffStatus.length; i++) {\n      var cmd = diffStatus[i].cmd;\n      if (cmd === '=') {\n        var el = data.getItemGraphicEl(diffStatus[i].idx1);\n        if (el) {\n          updatedDataInfo.push({\n            el: el,\n            ptIdx: i // Index of points\n          });\n        }\n      }\n    }\n    if (polyline.animators && polyline.animators.length) {\n      polyline.animators[0].during(function () {\n        polygon && polygon.dirtyShape();\n        var points = polyline.shape.__points;\n        for (var i = 0; i < updatedDataInfo.length; i++) {\n          var el = updatedDataInfo[i].el;\n          var offset = updatedDataInfo[i].ptIdx * 2;\n          el.x = points[offset];\n          el.y = points[offset + 1];\n          el.markRedraw();\n        }\n      });\n    }\n  };\n  LineView.prototype.remove = function (ecModel) {\n    var group = this.group;\n    var oldData = this._data;\n    this._lineGroup.removeAll();\n    this._symbolDraw.remove(true);\n    // Remove temporary created elements when highlighting\n    oldData && oldData.eachItemGraphicEl(function (el, idx) {\n      if (el.__temp) {\n        group.remove(el);\n        oldData.setItemGraphicEl(idx, null);\n      }\n    });\n    this._polyline = this._polygon = this._coordSys = this._points = this._stackedOnPoints = this._endLabel = this._data = null;\n  };\n  LineView.type = 'line';\n  return LineView;\n}(ChartView);\nexport default LineView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,SAASC,UAAU,EAAEC,SAAS,QAAQ,WAAW;AACjD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,oBAAoB,EAAEC,iBAAiB,QAAQ,aAAa;AACrE,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,yCAAyC;AACjG,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,wBAAwB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,cAAc,QAAQ,sBAAsB;AACnH,SAASC,aAAa,EAAEC,oBAAoB,EAAEC,UAAU,QAAQ,2BAA2B;AAC3F,SAASC,eAAe,EAAEC,2BAA2B,QAAQ,0BAA0B;AACvF,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,IAAI,QAAQ,2BAA2B;AAChD,SAASC,YAAYA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACtC,IAAID,OAAO,CAACE,MAAM,KAAKD,OAAO,CAACC,MAAM,EAAE;IACrC;EACF;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IACvC,IAAIH,OAAO,CAACG,CAAC,CAAC,KAAKF,OAAO,CAACE,CAAC,CAAC,EAAE;MAC7B;IACF;EACF;EACA,OAAO,IAAI;AACb;AACA,SAASC,cAAcA,CAACC,MAAM,EAAE;EAC9B,IAAIC,IAAI,GAAGC,QAAQ;EACnB,IAAIC,IAAI,GAAGD,QAAQ;EACnB,IAAIE,IAAI,GAAG,CAACF,QAAQ;EACpB,IAAIG,IAAI,GAAG,CAACH,QAAQ;EACpB,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,CAACH,MAAM,GAAG;IAClC,IAAIS,CAAC,GAAGN,MAAM,CAACF,CAAC,EAAE,CAAC;IACnB,IAAIS,CAAC,GAAGP,MAAM,CAACF,CAAC,EAAE,CAAC;IACnB,IAAI,CAACU,KAAK,CAACF,CAAC,CAAC,EAAE;MACbL,IAAI,GAAGQ,IAAI,CAACC,GAAG,CAACJ,CAAC,EAAEL,IAAI,CAAC;MACxBG,IAAI,GAAGK,IAAI,CAACE,GAAG,CAACL,CAAC,EAAEF,IAAI,CAAC;IAC1B;IACA,IAAI,CAACI,KAAK,CAACD,CAAC,CAAC,EAAE;MACbJ,IAAI,GAAGM,IAAI,CAACC,GAAG,CAACH,CAAC,EAAEJ,IAAI,CAAC;MACxBE,IAAI,GAAGI,IAAI,CAACE,GAAG,CAACJ,CAAC,EAAEF,IAAI,CAAC;IAC1B;EACF;EACA,OAAO,CAAC,CAACJ,IAAI,EAAEE,IAAI,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,CAAC,CAAC;AACrC;AACA,SAASO,eAAeA,CAACjB,OAAO,EAAEC,OAAO,EAAE;EACzC,IAAIiB,EAAE,GAAGd,cAAc,CAACJ,OAAO,CAAC;IAC9BmB,IAAI,GAAGD,EAAE,CAAC,CAAC,CAAC;IACZE,IAAI,GAAGF,EAAE,CAAC,CAAC,CAAC;EACd,IAAIG,EAAE,GAAGjB,cAAc,CAACH,OAAO,CAAC;IAC9BqB,IAAI,GAAGD,EAAE,CAAC,CAAC,CAAC;IACZE,IAAI,GAAGF,EAAE,CAAC,CAAC,CAAC;EACd;EACA,OAAOP,IAAI,CAACE,GAAG,CAACF,IAAI,CAACU,GAAG,CAACL,IAAI,CAAC,CAAC,CAAC,GAAGG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAER,IAAI,CAACU,GAAG,CAACL,IAAI,CAAC,CAAC,CAAC,GAAGG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAER,IAAI,CAACU,GAAG,CAACJ,IAAI,CAAC,CAAC,CAAC,GAAGG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAET,IAAI,CAACU,GAAG,CAACJ,IAAI,CAAC,CAAC,CAAC,GAAGG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrI;AACA,SAASE,SAASA,CAACC,MAAM,EAAE;EACzB,OAAOtD,MAAM,CAACuD,QAAQ,CAACD,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,GAAG,GAAG,GAAG,CAAC;AAC5D;AACA,SAASE,kBAAkBA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,aAAa,EAAE;EACzD,IAAI,CAACA,aAAa,CAACC,QAAQ,EAAE;IAC3B,OAAO,EAAE;EACX;EACA,IAAIC,GAAG,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC;EACtB,IAAI7B,MAAM,GAAGT,kBAAkB,CAACqC,GAAG,GAAG,CAAC,CAAC;EACxC,KAAK,IAAIE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,GAAG,EAAEE,GAAG,EAAE,EAAE;IAClC,IAAIC,EAAE,GAAGtD,iBAAiB,CAACiD,aAAa,EAAEF,QAAQ,EAAEC,IAAI,EAAEK,GAAG,CAAC;IAC9D9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;IACvB/B,MAAM,CAAC8B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EAC7B;EACA,OAAO/B,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgC,kBAAkBA,CAAChC,MAAM,EAAEiC,UAAU,EAAET,QAAQ,EAAEU,UAAU,EAAEC,YAAY,EAAE;EAClF,IAAIC,QAAQ,GAAGZ,QAAQ,CAACa,WAAW,CAAC,CAAC;EACrC,IAAIC,SAAS,GAAGF,QAAQ,CAACG,GAAG,KAAK,GAAG,IAAIH,QAAQ,CAACG,GAAG,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC;EACzE,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAI1C,CAAC,GAAG,CAAC;EACT,IAAI2C,MAAM,GAAG,EAAE;EACf,IAAIV,EAAE,GAAG,EAAE;EACX,IAAIW,MAAM,GAAG,EAAE;EACf,IAAIC,cAAc,GAAG,EAAE;EACvB,IAAIR,YAAY,EAAE;IAChB,KAAKrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,CAACH,MAAM,EAAEC,CAAC,IAAI,CAAC,EAAE;MACrC;AACN;AACA;AACA;AACA;MACM,IAAI8C,SAAS,GAAGX,UAAU,IAAIjC,MAAM;MACpC,IAAI,CAACQ,KAAK,CAACoC,SAAS,CAAC9C,CAAC,CAAC,CAAC,IAAI,CAACU,KAAK,CAACoC,SAAS,CAAC9C,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QACpD6C,cAAc,CAACE,IAAI,CAAC7C,MAAM,CAACF,CAAC,CAAC,EAAEE,MAAM,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/C;IACF;IACAE,MAAM,GAAG2C,cAAc;EACzB;EACA,KAAK7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,CAACH,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE;IACzC4C,MAAM,CAAC,CAAC,CAAC,GAAG1C,MAAM,CAACF,CAAC,GAAG,CAAC,CAAC;IACzB4C,MAAM,CAAC,CAAC,CAAC,GAAG1C,MAAM,CAACF,CAAC,GAAG,CAAC,CAAC;IACzBiC,EAAE,CAAC,CAAC,CAAC,GAAG/B,MAAM,CAACF,CAAC,CAAC;IACjBiC,EAAE,CAAC,CAAC,CAAC,GAAG/B,MAAM,CAACF,CAAC,GAAG,CAAC,CAAC;IACrB0C,UAAU,CAACK,IAAI,CAACd,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7B,QAAQG,UAAU;MAChB,KAAK,KAAK;QACRO,MAAM,CAACH,SAAS,CAAC,GAAGI,MAAM,CAACJ,SAAS,CAAC;QACrCG,MAAM,CAAC,CAAC,GAAGH,SAAS,CAAC,GAAGP,EAAE,CAAC,CAAC,GAAGO,SAAS,CAAC;QACzCE,UAAU,CAACK,IAAI,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;QACrC;MACF,KAAK,QAAQ;QACX,IAAIK,MAAM,GAAG,CAACf,EAAE,CAACO,SAAS,CAAC,GAAGI,MAAM,CAACJ,SAAS,CAAC,IAAI,CAAC;QACpD,IAAIS,OAAO,GAAG,EAAE;QAChBN,MAAM,CAACH,SAAS,CAAC,GAAGS,OAAO,CAACT,SAAS,CAAC,GAAGQ,MAAM;QAC/CL,MAAM,CAAC,CAAC,GAAGH,SAAS,CAAC,GAAGP,EAAE,CAAC,CAAC,GAAGO,SAAS,CAAC;QACzCS,OAAO,CAAC,CAAC,GAAGT,SAAS,CAAC,GAAGI,MAAM,CAAC,CAAC,GAAGJ,SAAS,CAAC;QAC9CE,UAAU,CAACK,IAAI,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;QACrCD,UAAU,CAACK,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;QACvC;MACF;QACE;QACAN,MAAM,CAACH,SAAS,CAAC,GAAGP,EAAE,CAACO,SAAS,CAAC;QACjCG,MAAM,CAAC,CAAC,GAAGH,SAAS,CAAC,GAAGI,MAAM,CAAC,CAAC,GAAGJ,SAAS,CAAC;QAC7CE,UAAU,CAACK,IAAI,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACzC;EACF;EACA;EACAD,UAAU,CAACK,IAAI,CAAC7C,MAAM,CAACF,CAAC,EAAE,CAAC,EAAEE,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;EACzC,OAAO0C,UAAU;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,cAAcA,CAACC,UAAU,EAAEC,OAAO,EAAE;EAC3C,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIvB,GAAG,GAAGqB,UAAU,CAACpD,MAAM;EAC3B;EACA,IAAIuD,uBAAuB;EAC3B,IAAIC,oBAAoB;EACxB,SAASC,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAEC,YAAY,EAAE;IAC5C,IAAIC,MAAM,GAAGH,KAAK,CAACI,KAAK;IACxB,IAAIC,CAAC,GAAG,CAACH,YAAY,GAAGC,MAAM,KAAKF,KAAK,CAACG,KAAK,GAAGD,MAAM,CAAC;IACxD,IAAIG,KAAK,GAAGpE,IAAI,CAACmE,CAAC,EAAE,CAACL,KAAK,CAACM,KAAK,EAAEL,KAAK,CAACK,KAAK,CAAC,CAAC;IAC/C,OAAO;MACLF,KAAK,EAAEF,YAAY;MACnBI,KAAK,EAAEA;IACT,CAAC;EACH;EACA,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,GAAG,EAAE9B,CAAC,EAAE,EAAE;IAC5B,IAAIgE,MAAM,GAAGb,UAAU,CAACnD,CAAC,CAAC;IAC1B,IAAI6D,KAAK,GAAGG,MAAM,CAACH,KAAK;IACxB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACbP,uBAAuB,GAAGU,MAAM;IAClC,CAAC,MAAM,IAAIH,KAAK,GAAGT,OAAO,EAAE;MAC1B,IAAIG,oBAAoB,EAAE;QACxBF,aAAa,CAACN,IAAI,CAACS,QAAQ,CAACD,oBAAoB,EAAES,MAAM,EAAEZ,OAAO,CAAC,CAAC;MACrE,CAAC,MAAM,IAAIE,uBAAuB,EAAE;QAClC;QACAD,aAAa,CAACN,IAAI,CAACS,QAAQ,CAACF,uBAAuB,EAAEU,MAAM,EAAE,CAAC,CAAC,EAAER,QAAQ,CAACF,uBAAuB,EAAEU,MAAM,EAAEZ,OAAO,CAAC,CAAC;MACtH;MACA;MACA;IACF,CAAC,MAAM;MACL,IAAIE,uBAAuB,EAAE;QAC3BD,aAAa,CAACN,IAAI,CAACS,QAAQ,CAACF,uBAAuB,EAAEU,MAAM,EAAE,CAAC,CAAC,CAAC;QAChE;QACAV,uBAAuB,GAAG,IAAI;MAChC;MACAD,aAAa,CAACN,IAAI,CAACiB,MAAM,CAAC;MAC1BT,oBAAoB,GAAGS,MAAM;IAC/B;EACF;EACA,OAAOX,aAAa;AACtB;AACA,SAASY,iBAAiBA,CAACtC,IAAI,EAAED,QAAQ,EAAEwC,GAAG,EAAE;EAC9C,IAAIC,cAAc,GAAGxC,IAAI,CAACyC,SAAS,CAAC,YAAY,CAAC;EACjD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAACpE,MAAM,IAAI,CAAC4B,IAAI,CAACI,KAAK,CAAC,CAAC,EAAE;IAC9D;IACA;EACF;EACA,IAAIL,QAAQ,CAAC2C,IAAI,KAAK,aAAa,EAAE;IACnC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCC,OAAO,CAACC,IAAI,CAAC,4DAA4D,CAAC;IAC5E;IACA;EACF;EACA,IAAIC,QAAQ;EACZ,IAAIC,UAAU;EACd,KAAK,IAAI5E,CAAC,GAAGmE,cAAc,CAACpE,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACnD,IAAI6E,OAAO,GAAGlD,IAAI,CAACmD,gBAAgB,CAACX,cAAc,CAACnE,CAAC,CAAC,CAAC+E,SAAS,CAAC;IAChEJ,QAAQ,GAAGE,OAAO,IAAIA,OAAO,CAACF,QAAQ;IACtC;IACA,IAAIA,QAAQ,KAAK,GAAG,IAAIA,QAAQ,KAAK,GAAG,EAAE;MACxCC,UAAU,GAAGT,cAAc,CAACnE,CAAC,CAAC;MAC9B;IACF;EACF;EACA,IAAI,CAAC4E,UAAU,EAAE;IACf,IAAIN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCC,OAAO,CAACC,IAAI,CAAC,yDAAyD,CAAC;IACzE;IACA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIM,IAAI,GAAGtD,QAAQ,CAACuD,OAAO,CAACN,QAAQ,CAAC;EACrC;EACA,IAAIxB,UAAU,GAAGlF,MAAM,CAACiH,GAAG,CAACN,UAAU,CAACO,KAAK,EAAE,UAAUC,IAAI,EAAE;IAC5D;IACA,OAAO;MACLvB,KAAK,EAAEmB,IAAI,CAACK,aAAa,CAACL,IAAI,CAACM,WAAW,CAACF,IAAI,CAACG,KAAK,CAAC,CAAC;MACvDxB,KAAK,EAAEqB,IAAI,CAACrB;IACd,CAAC;EACH,CAAC,CAAC;EACF,IAAIyB,OAAO,GAAGrC,UAAU,CAACpD,MAAM;EAC/B,IAAI0F,WAAW,GAAGb,UAAU,CAACa,WAAW,CAACC,KAAK,CAAC,CAAC;EAChD,IAAIF,OAAO,IAAIrC,UAAU,CAAC,CAAC,CAAC,CAACU,KAAK,GAAGV,UAAU,CAACqC,OAAO,GAAG,CAAC,CAAC,CAAC3B,KAAK,EAAE;IAClEV,UAAU,CAACwC,OAAO,CAAC,CAAC;IACpBF,WAAW,CAACE,OAAO,CAAC,CAAC;EACvB;EACA,IAAIC,iBAAiB,GAAG1C,cAAc,CAACC,UAAU,EAAEwB,QAAQ,KAAK,GAAG,GAAGT,GAAG,CAAC2B,QAAQ,CAAC,CAAC,GAAG3B,GAAG,CAAC4B,SAAS,CAAC,CAAC,CAAC;EACvG,IAAIC,cAAc,GAAGH,iBAAiB,CAAC7F,MAAM;EAC7C,IAAI,CAACgG,cAAc,IAAIP,OAAO,EAAE;IAC9B;IACA,OAAOrC,UAAU,CAAC,CAAC,CAAC,CAACU,KAAK,GAAG,CAAC,GAAG4B,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGtC,UAAU,CAACqC,OAAO,GAAG,CAAC,CAAC,CAACzB,KAAK,GAAG0B,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGtC,UAAU,CAAC,CAAC,CAAC,CAACY,KAAK;EAC1J;EACA,IAAIiC,UAAU,GAAG,EAAE,CAAC,CAAC;EACrB,IAAIC,QAAQ,GAAGL,iBAAiB,CAAC,CAAC,CAAC,CAAC/B,KAAK,GAAGmC,UAAU;EACtD,IAAIE,QAAQ,GAAGN,iBAAiB,CAACG,cAAc,GAAG,CAAC,CAAC,CAAClC,KAAK,GAAGmC,UAAU;EACvE,IAAIG,SAAS,GAAGD,QAAQ,GAAGD,QAAQ;EACnC,IAAIE,SAAS,GAAG,IAAI,EAAE;IACpB,OAAO,aAAa;EACtB;EACAlI,MAAM,CAACmI,IAAI,CAACR,iBAAiB,EAAE,UAAUR,IAAI,EAAE;IAC7CA,IAAI,CAACiB,MAAM,GAAG,CAACjB,IAAI,CAACvB,KAAK,GAAGoC,QAAQ,IAAIE,SAAS;EACnD,CAAC,CAAC;EACFP,iBAAiB,CAAC7C,IAAI,CAAC;IACrB;IACAsD,MAAM,EAAEN,cAAc,GAAGH,iBAAiB,CAACG,cAAc,GAAG,CAAC,CAAC,CAACM,MAAM,GAAG,GAAG;IAC3EtC,KAAK,EAAE0B,WAAW,CAAC,CAAC,CAAC,IAAI;EAC3B,CAAC,CAAC;EACFG,iBAAiB,CAACU,OAAO,CAAC;IACxBD,MAAM,EAAEN,cAAc,GAAGH,iBAAiB,CAAC,CAAC,CAAC,CAACS,MAAM,GAAG,GAAG;IAC1DtC,KAAK,EAAE0B,WAAW,CAAC,CAAC,CAAC,IAAI;EAC3B,CAAC,CAAC;EACF,IAAIc,QAAQ,GAAG,IAAIlI,OAAO,CAACmI,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEZ,iBAAiB,EAAE,IAAI,CAAC;EAC9EW,QAAQ,CAAC5B,QAAQ,CAAC,GAAGsB,QAAQ;EAC7BM,QAAQ,CAAC5B,QAAQ,GAAG,GAAG,CAAC,GAAGuB,QAAQ;EACnC,OAAOK,QAAQ;AACjB;AACA,SAASE,eAAeA,CAACC,WAAW,EAAE/E,IAAI,EAAED,QAAQ,EAAE;EACpD,IAAIiF,aAAa,GAAGD,WAAW,CAACE,GAAG,CAAC,eAAe,CAAC;EACpD,IAAIC,MAAM,GAAGF,aAAa,KAAK,MAAM;EACrC,IAAIA,aAAa,IAAI,CAACE,MAAM,EAAE;IAC5B;EACF;EACA,IAAIC,YAAY,GAAGpF,QAAQ,CAACqF,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;EACxD,IAAI,CAACD,YAAY,EAAE;IACjB;EACF;EACA;EACA;EACA;EACA,IAAID;EACJ;EAAA,GACGG,2BAA2B,CAACF,YAAY,EAAEnF,IAAI,CAAC,EAAE;IAClD;EACF;EACA;EACA,IAAIsF,eAAe,GAAGtF,IAAI,CAACuF,YAAY,CAACJ,YAAY,CAACrE,GAAG,CAAC;EACzD,IAAI0E,QAAQ,GAAG,CAAC,CAAC;EACjBlJ,MAAM,CAACmI,IAAI,CAACU,YAAY,CAACM,aAAa,CAAC,CAAC,EAAE,UAAUC,SAAS,EAAE;IAC7D,IAAIC,aAAa,GAAGR,YAAY,CAACS,KAAK,CAACC,mBAAmB,CAACH,SAAS,CAACI,SAAS,CAAC;IAC/EN,QAAQ,CAACG,aAAa,CAAC,GAAG,CAAC;EAC7B,CAAC,CAAC;EACF,OAAO,UAAUI,SAAS,EAAE;IAC1B,OAAO,CAACP,QAAQ,CAACQ,cAAc,CAAChG,IAAI,CAACiF,GAAG,CAACK,eAAe,EAAES,SAAS,CAAC,CAAC;EACvE,CAAC;AACH;AACA,SAASV,2BAA2BA,CAACF,YAAY,EAAEnF,IAAI,EAAE;EACvD;EACA;EACA;EACA;EACA,IAAIiG,UAAU,GAAGd,YAAY,CAACe,SAAS,CAAC,CAAC;EACzC,IAAIC,SAAS,GAAGnH,IAAI,CAACU,GAAG,CAACuG,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGd,YAAY,CAACS,KAAK,CAACxF,KAAK,CAAC,CAAC;EACpFrB,KAAK,CAACoH,SAAS,CAAC,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;EACrC;EACA,IAAIC,OAAO,GAAGpG,IAAI,CAACI,KAAK,CAAC,CAAC;EAC1B,IAAIiG,IAAI,GAAGrH,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACsH,KAAK,CAACF,OAAO,GAAG,CAAC,CAAC,CAAC;EAC/C,KAAK,IAAIL,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGK,OAAO,EAAEL,SAAS,IAAIM,IAAI,EAAE;IAC9D,IAAI7J,SAAS,CAAC+J,aAAa,CAACvG,IAAI,EAAE+F;IAClC;IACA,CAAC,CAACZ,YAAY,CAACqB,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IACrC;IAAA,EACE,GAAG,GAAGL,SAAS,EAAE;MACjB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AACA,SAASM,WAAWA,CAAC5H,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOC,KAAK,CAACF,CAAC,CAAC,IAAIE,KAAK,CAACD,CAAC,CAAC;AAC7B;AACA,SAAS4H,mBAAmBA,CAACnI,MAAM,EAAE;EACnC,IAAI4B,GAAG,GAAG5B,MAAM,CAACH,MAAM,GAAG,CAAC;EAC3B,OAAO+B,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;IACrB,IAAI,CAACsG,WAAW,CAAClI,MAAM,CAAC4B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE5B,MAAM,CAAC4B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC1D;IACF;EACF;EACA,OAAOA,GAAG,GAAG,CAAC;AAChB;AACA,SAASwG,eAAeA,CAACpI,MAAM,EAAE8B,GAAG,EAAE;EACpC,OAAO,CAAC9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/C;AACA,SAASuG,aAAaA,CAACrI,MAAM,EAAEsI,IAAI,EAAE/F,GAAG,EAAE;EACxC,IAAIX,GAAG,GAAG5B,MAAM,CAACH,MAAM,GAAG,CAAC;EAC3B,IAAI0I,MAAM,GAAGhG,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;EAChC,IAAIiG,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,KAAK,IAAI7I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,GAAG,EAAE9B,CAAC,EAAE,EAAE;IAC5B2I,CAAC,GAAGzI,MAAM,CAACF,CAAC,GAAG,CAAC,GAAGyI,MAAM,CAAC;IAC1B,IAAI/H,KAAK,CAACiI,CAAC,CAAC,IAAIjI,KAAK,CAACR,MAAM,CAACF,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGyI,MAAM,CAAC,CAAC,EAAE;MACjD;IACF;IACA,IAAIzI,CAAC,KAAK,CAAC,EAAE;MACX0I,CAAC,GAAGC,CAAC;MACL;IACF;IACA,IAAID,CAAC,IAAIF,IAAI,IAAIG,CAAC,IAAIH,IAAI,IAAIE,CAAC,IAAIF,IAAI,IAAIG,CAAC,IAAIH,IAAI,EAAE;MACpDK,SAAS,GAAG7I,CAAC;MACb;IACF;IACA4I,SAAS,GAAG5I,CAAC;IACb0I,CAAC,GAAGC,CAAC;EACP;EACA,OAAO;IACLG,KAAK,EAAE,CAACF,SAAS,EAAEC,SAAS,CAAC;IAC7BE,CAAC,EAAE,CAACP,IAAI,GAAGE,CAAC,KAAKC,CAAC,GAAGD,CAAC;EACxB,CAAC;AACH;AACA,SAASM,oBAAoBA,CAACtC,WAAW,EAAE;EACzC,IAAIA,WAAW,CAACE,GAAG,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,EAAE;IACzC,OAAO,IAAI;EACb;EACA,KAAK,IAAI5G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,cAAc,CAACa,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC9C,IAAI0G,WAAW,CAACE,GAAG,CAAC,CAAC1H,cAAc,CAACc,CAAC,CAAC,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,EAAE;MAC5D,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AACA,SAASiJ,kBAAkBA,CAACC,QAAQ,EAAExH,QAAQ,EAAEyH,YAAY,EAAEzC,WAAW,EAAE;EACzE,IAAI5H,sBAAsB,CAAC4C,QAAQ,EAAE,aAAa,CAAC,EAAE;IACnD,IAAI0H,eAAe,GAAG1C,WAAW,CAAC2C,QAAQ,CAAC,UAAU,CAAC;IACtD,IAAIC,gBAAgB,GAAGF,eAAe,CAACxC,GAAG,CAAC,gBAAgB,CAAC;IAC5D,IAAI2C,MAAM,GAAG7C,WAAW,CAAC8C,OAAO,CAAC,CAAC;IAClC,IAAIC,sBAAsB,GAAG;MAC3BC,cAAc,EAAE;IAClB,CAAC;IACD,IAAIC,MAAM,GAAGX,oBAAoB,CAACtC,WAAW,CAAC,GAAG,UAAUkD,OAAO,EAAEC,QAAQ,EAAE;MAC5EX,QAAQ,CAACY,iBAAiB,CAACF,OAAO,EAAEC,QAAQ,EAAEN,MAAM,EAAEE,sBAAsB,EAAEH,gBAAgB,EAAEF,eAAe,EAAE1H,QAAQ,CAAC;IAC5H,CAAC,GAAG,IAAI;IACR,IAAIyG,YAAY,GAAGzG,QAAQ,CAACa,WAAW,CAAC,CAAC,CAAC4F,YAAY,CAAC,CAAC;IACxD,IAAI4B,QAAQ,GAAGnL,kBAAkB,CAAC8C,QAAQ,EAAEyH,YAAY,EAAEzC,WAAW,EAAE,YAAY;MACjF,IAAIsD,QAAQ,GAAGd,QAAQ,CAACe,SAAS;MACjC,IAAID,QAAQ,IAAIb,YAAY,EAAE;QAC5B,IAAIM,sBAAsB,CAACS,SAAS,IAAI,IAAI,EAAE;UAC5CF,QAAQ,CAACG,IAAI,CAAC;YACZ3J,CAAC,EAAEiJ,sBAAsB,CAACS,SAAS;YACnCzJ,CAAC,EAAEgJ,sBAAsB,CAACW;UAC5B,CAAC,CAAC;QACJ;MACF;IACF,CAAC,EAAET,MAAM,CAAC;IACV;IACA,IAAI,CAACjD,WAAW,CAACE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;MAClC,IAAIyD,SAAS,GAAGN,QAAQ,CAACO,KAAK;MAC9B,IAAIC,UAAU,GAAG5J,IAAI,CAACE,GAAG,CAACwJ,SAAS,CAACG,KAAK,EAAEH,SAAS,CAACI,MAAM,CAAC;MAC5D,IAAItC,YAAY,EAAE;QAChBkC,SAAS,CAAC5J,CAAC,IAAI8J,UAAU;QACzBF,SAAS,CAACI,MAAM,IAAIF,UAAU,GAAG,CAAC;MACpC,CAAC,MAAM;QACLF,SAAS,CAAC7J,CAAC,IAAI+J,UAAU;QACzBF,SAAS,CAACG,KAAK,IAAID,UAAU,GAAG,CAAC;MACnC;IACF;IACA;IACA,IAAIZ,MAAM,EAAE;MACVA,MAAM,CAAC,CAAC,EAAEI,QAAQ,CAAC;IACrB;IACA,OAAOA,QAAQ;EACjB,CAAC,MAAM;IACL,IAAIzF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIkC,WAAW,CAACE,GAAG,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,EAAE;QACzCnC,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;MACvE;IACF;IACA,OAAO7F,mBAAmB,CAAC6C,QAAQ,EAAEyH,YAAY,EAAEzC,WAAW,CAAC;EACjE;AACF;AACA,SAASgE,yBAAyBA,CAACC,aAAa,EAAEjJ,QAAQ,EAAE;EAC1D,IAAIY,QAAQ,GAAGZ,QAAQ,CAACa,WAAW,CAAC,CAAC;EACrC,IAAI4F,YAAY,GAAG7F,QAAQ,CAAC6F,YAAY,CAAC,CAAC;EAC1C,IAAIyC,cAAc,GAAGtI,QAAQ,CAACuI,OAAO;EACrC,IAAIC,KAAK,GAAG3C,YAAY,GAAGyC,cAAc,GAAG,OAAO,GAAG,MAAM,GAAG,QAAQ;EACvE,IAAIG,aAAa,GAAG5C,YAAY,GAAG,QAAQ,GAAGyC,cAAc,GAAG,KAAK,GAAG,QAAQ;EAC/E,OAAO;IACLI,MAAM,EAAE;MACNF,KAAK,EAAEH,aAAa,CAAC/D,GAAG,CAAC,OAAO,CAAC,IAAIkE,KAAK;MAC1CC,aAAa,EAAEJ,aAAa,CAAC/D,GAAG,CAAC,eAAe,CAAC,IAAImE;IACvD;EACF,CAAC;AACH;AACA,IAAIE,QAAQ,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC5ClN,SAAS,CAACiN,QAAQ,EAAEC,MAAM,CAAC;EAC3B,SAASD,QAAQA,CAAA,EAAG;IAClB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACAH,QAAQ,CAACI,SAAS,CAACC,IAAI,GAAG,YAAY;IACpC,IAAIC,SAAS,GAAG,IAAIlN,OAAO,CAACmN,KAAK,CAAC,CAAC;IACnC,IAAIC,UAAU,GAAG,IAAIvN,UAAU,CAAC,CAAC;IACjC,IAAI,CAACwN,KAAK,CAACC,GAAG,CAACF,UAAU,CAACC,KAAK,CAAC;IAChC,IAAI,CAACE,WAAW,GAAGH,UAAU;IAC7B,IAAI,CAACI,UAAU,GAAGN,SAAS;IAC3B,IAAI,CAACO,gBAAgB,GAAG7N,MAAM,CAAC8N,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE,IAAI,CAAC;EAClE,CAAC;EACDb,QAAQ,CAACI,SAAS,CAACW,MAAM,GAAG,UAAUtF,WAAW,EAAEuF,OAAO,EAAE/H,GAAG,EAAE;IAC/D,IAAIxC,QAAQ,GAAGgF,WAAW,CAACwF,gBAAgB;IAC3C,IAAIR,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI/J,IAAI,GAAG+E,WAAW,CAAC8C,OAAO,CAAC,CAAC;IAChC,IAAI2C,cAAc,GAAGzF,WAAW,CAAC2C,QAAQ,CAAC,WAAW,CAAC;IACtD,IAAI+C,cAAc,GAAG1F,WAAW,CAAC2C,QAAQ,CAAC,WAAW,CAAC;IACtD,IAAInJ,MAAM,GAAGyB,IAAI,CAAC0K,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE;IAC3C,IAAIC,eAAe,GAAG5K,QAAQ,CAAC2C,IAAI,KAAK,OAAO;IAC/C,IAAIkI,YAAY,GAAG,IAAI,CAACC,SAAS;IACjC,IAAIf,UAAU,GAAG,IAAI,CAACG,WAAW;IACjC,IAAIa,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC7B,IAAIC,OAAO,GAAG,IAAI,CAACC,QAAQ;IAC3B,IAAIrB,SAAS,GAAG,IAAI,CAACM,UAAU;IAC/B,IAAI1C,YAAY,GAAG,CAAC8C,OAAO,CAACY,GAAG,IAAInG,WAAW,CAACE,GAAG,CAAC,WAAW,CAAC;IAC/D,IAAIkG,WAAW,GAAG,CAACV,cAAc,CAACW,OAAO,CAAC,CAAC;IAC3C,IAAIC,WAAW,GAAGZ,cAAc,CAACxF,GAAG,CAAC,QAAQ,CAAC;IAC9C,IAAIhF,aAAa,GAAGlD,oBAAoB,CAACgD,QAAQ,EAAEC,IAAI,EAAEqL,WAAW,CAAC;IACrE,IAAIC,eAAe,GAAGH,WAAW,IAAIrL,kBAAkB,CAACC,QAAQ,EAAEC,IAAI,EAAEC,aAAa,CAAC;IACtF,IAAIsL,UAAU,GAAGxG,WAAW,CAACE,GAAG,CAAC,YAAY,CAAC;IAC9C,IAAIvE,YAAY,GAAGqE,WAAW,CAACE,GAAG,CAAC,cAAc,CAAC;IAClD,IAAIuG,YAAY,GAAGD,UAAU,IAAI,CAACZ,eAAe,IAAI7F,eAAe,CAACC,WAAW,EAAE/E,IAAI,EAAED,QAAQ,CAAC;IACjG;IACA,IAAI0L,OAAO,GAAG,IAAI,CAACC,KAAK;IACxBD,OAAO,IAAIA,OAAO,CAACE,iBAAiB,CAAC,UAAUC,EAAE,EAAEvL,GAAG,EAAE;MACtD,IAAIuL,EAAE,CAACC,MAAM,EAAE;QACb9B,KAAK,CAAC+B,MAAM,CAACF,EAAE,CAAC;QAChBH,OAAO,CAACM,gBAAgB,CAAC1L,GAAG,EAAE,IAAI,CAAC;MACrC;IACF,CAAC,CAAC;IACF;IACA,IAAI,CAACkL,UAAU,EAAE;MACfzB,UAAU,CAACgC,MAAM,CAAC,CAAC;IACrB;IACA/B,KAAK,CAACC,GAAG,CAACJ,SAAS,CAAC;IACpB;IACA,IAAIvD,IAAI,GAAG,CAACsE,eAAe,GAAG5F,WAAW,CAACE,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK;IAC7D,IAAI+G,kBAAkB;IACtB,IAAIjM,QAAQ,IAAIA,QAAQ,CAACkM,OAAO,IAAIlH,WAAW,CAACE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;MACjE+G,kBAAkB,GAAGjM,QAAQ,CAACkM,OAAO,CAAC,CAAC;MACvC;MACA;MACA,IAAID,kBAAkB,CAACnD,KAAK,IAAI,IAAI,EAAE;QACpCmD,kBAAkB,CAACnN,CAAC,IAAI,GAAG;QAC3BmN,kBAAkB,CAAClN,CAAC,IAAI,GAAG;QAC3BkN,kBAAkB,CAACnD,KAAK,IAAI,GAAG;QAC/BmD,kBAAkB,CAAClD,MAAM,IAAI,GAAG;MAClC,CAAC,MAAM,IAAIkD,kBAAkB,CAACE,EAAE,EAAE;QAChCF,kBAAkB,CAACE,EAAE,IAAI,GAAG;QAC5BF,kBAAkB,CAACG,CAAC,IAAI,GAAG;MAC7B;IACF;IACA,IAAI,CAACC,mBAAmB,GAAGJ,kBAAkB;IAC7C,IAAIK,WAAW,GAAG/J,iBAAiB,CAACtC,IAAI,EAAED,QAAQ,EAAEwC,GAAG,CAAC,IAAIvC,IAAI,CAACyC,SAAS,CAAC,OAAO,CAAC,CAACzC,IAAI,CAACyC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC/G;IACA,IAAI,EAAEqI,QAAQ,IAAIF,YAAY,CAAClI,IAAI,KAAK3C,QAAQ,CAAC2C,IAAI,IAAI2D,IAAI,KAAK,IAAI,CAACiG,KAAK,CAAC,EAAE;MAC7Ef,UAAU,IAAIzB,UAAU,CAACyC,UAAU,CAACvM,IAAI,EAAE;QACxCwM,QAAQ,EAAEhB,YAAY;QACtBiB,SAAS,EAAET,kBAAkB;QAC7BU,gBAAgB,EAAE,IAAI;QACtBC,cAAc,EAAE,SAAAA,CAAUtM,GAAG,EAAE;UAC7B,OAAO,CAAC9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/C;MACF,CAAC,CAAC;MACFmH,YAAY,IAAI,IAAI,CAACoF,yBAAyB,CAAC5M,IAAI,EAAED,QAAQ,EAAEiM,kBAAkB,CAAC;MAClF,IAAI3F,IAAI,EAAE;QACR,IAAIiF,eAAe,EAAE;UACnBA,eAAe,GAAG/K,kBAAkB,CAAC+K,eAAe,EAAE/M,MAAM,EAAEwB,QAAQ,EAAEsG,IAAI,EAAE3F,YAAY,CAAC;QAC7F;QACA;QACAnC,MAAM,GAAGgC,kBAAkB,CAAChC,MAAM,EAAE,IAAI,EAAEwB,QAAQ,EAAEsG,IAAI,EAAE3F,YAAY,CAAC;MACzE;MACAoK,QAAQ,GAAG,IAAI,CAAC+B,YAAY,CAACtO,MAAM,CAAC;MACpC,IAAI4M,WAAW,EAAE;QACfH,OAAO,GAAG,IAAI,CAAC8B,WAAW,CAACvO,MAAM,EAAE+M,eAAe,CAAC;MACrD,CAAC,CAAC;MAAA,KACG,IAAIN,OAAO,EAAE;QAChBpB,SAAS,CAACkC,MAAM,CAACd,OAAO,CAAC;QACzBA,OAAO,GAAG,IAAI,CAACC,QAAQ,GAAG,IAAI;MAChC;MACA;MACA,IAAI,CAACN,eAAe,EAAE;QACpB,IAAI,CAACoC,qBAAqB,CAAChI,WAAW,EAAEhF,QAAQ,EAAEhC,oBAAoB,CAACsO,WAAW,CAAC,CAAC;MACtF;MACAzC,SAAS,CAACoD,WAAW,CAAC1F,kBAAkB,CAAC,IAAI,EAAEvH,QAAQ,EAAE,IAAI,EAAEgF,WAAW,CAAC,CAAC;IAC9E,CAAC,MAAM;MACL,IAAIoG,WAAW,IAAI,CAACH,OAAO,EAAE;QAC3B;QACAA,OAAO,GAAG,IAAI,CAAC8B,WAAW,CAACvO,MAAM,EAAE+M,eAAe,CAAC;MACrD,CAAC,MAAM,IAAIN,OAAO,IAAI,CAACG,WAAW,EAAE;QAClC;QACAvB,SAAS,CAACkC,MAAM,CAACd,OAAO,CAAC;QACzBA,OAAO,GAAG,IAAI,CAACC,QAAQ,GAAG,IAAI;MAChC;MACA;MACA,IAAI,CAACN,eAAe,EAAE;QACpB,IAAI,CAACoC,qBAAqB,CAAChI,WAAW,EAAEhF,QAAQ,EAAEhC,oBAAoB,CAACsO,WAAW,CAAC,CAAC;MACtF;MACA;MACA,IAAIY,WAAW,GAAGrD,SAAS,CAACsD,WAAW,CAAC,CAAC;MACzC,IAAID,WAAW,EAAE;QACf,IAAIE,WAAW,GAAG7F,kBAAkB,CAAC,IAAI,EAAEvH,QAAQ,EAAE,KAAK,EAAEgF,WAAW,CAAC;QACxErI,OAAO,CAAC0Q,SAAS,CAACH,WAAW,EAAE;UAC7BtE,KAAK,EAAEwE,WAAW,CAACxE;QACrB,CAAC,EAAE5D,WAAW,CAAC;MACjB,CAAC,MAAM;QACL6E,SAAS,CAACoD,WAAW,CAAC1F,kBAAkB,CAAC,IAAI,EAAEvH,QAAQ,EAAE,IAAI,EAAEgF,WAAW,CAAC,CAAC;MAC9E;MACA;MACA;MACAwG,UAAU,IAAIzB,UAAU,CAACyC,UAAU,CAACvM,IAAI,EAAE;QACxCwM,QAAQ,EAAEhB,YAAY;QACtBiB,SAAS,EAAET,kBAAkB;QAC7BU,gBAAgB,EAAE,IAAI;QACtBC,cAAc,EAAE,SAAAA,CAAUtM,GAAG,EAAE;UAC7B,OAAO,CAAC9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAAC8B,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/C;MACF,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAACpC,YAAY,CAAC,IAAI,CAACoP,gBAAgB,EAAE/B,eAAe,CAAC,IAAI,CAACrN,YAAY,CAAC,IAAI,CAACqP,OAAO,EAAE/O,MAAM,CAAC,EAAE;QAChG,IAAIiJ,YAAY,EAAE;UAChB,IAAI,CAAC+F,kBAAkB,CAACvN,IAAI,EAAEsL,eAAe,EAAEvL,QAAQ,EAAEwC,GAAG,EAAE8D,IAAI,EAAEgF,WAAW,EAAE3K,YAAY,CAAC;QAChG,CAAC,MAAM;UACL;UACA,IAAI2F,IAAI,EAAE;YACR,IAAIiF,eAAe,EAAE;cACnBA,eAAe,GAAG/K,kBAAkB,CAAC+K,eAAe,EAAE/M,MAAM,EAAEwB,QAAQ,EAAEsG,IAAI,EAAE3F,YAAY,CAAC;YAC7F;YACA;YACAnC,MAAM,GAAGgC,kBAAkB,CAAChC,MAAM,EAAE,IAAI,EAAEwB,QAAQ,EAAEsG,IAAI,EAAE3F,YAAY,CAAC;UACzE;UACAoK,QAAQ,CAAC0C,QAAQ,CAAC;YAChBjP,MAAM,EAAEA;UACV,CAAC,CAAC;UACFyM,OAAO,IAAIA,OAAO,CAACwC,QAAQ,CAAC;YAC1BjP,MAAM,EAAEA,MAAM;YACd+M,eAAe,EAAEA;UACnB,CAAC,CAAC;QACJ;MACF;IACF;IACA,IAAImC,aAAa,GAAG1I,WAAW,CAAC2C,QAAQ,CAAC,UAAU,CAAC;IACpD,IAAIgG,KAAK,GAAGD,aAAa,CAACxI,GAAG,CAAC,OAAO,CAAC;IACtC,IAAI0I,SAAS,GAAGF,aAAa,CAACxI,GAAG,CAAC,WAAW,CAAC;IAC9C,IAAI2I,gBAAgB,GAAGH,aAAa,CAACxI,GAAG,CAAC,UAAU,CAAC;IACpD6F,QAAQ,CAAC+C,QAAQ,CAACvR,MAAM,CAACwR,QAAQ;IACjC;IACAtD,cAAc,CAACuD,YAAY,CAAC,CAAC,EAAE;MAC7BC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE5B,WAAW;MACnB6B,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;IACH9Q,wBAAwB,CAAC0N,QAAQ,EAAE/F,WAAW,EAAE,WAAW,CAAC;IAC5D,IAAI+F,QAAQ,CAACqD,KAAK,CAACC,SAAS,GAAG,CAAC,IAAIrJ,WAAW,CAACE,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpG,IAAIoJ,iBAAiB,GAAGvD,QAAQ,CAACwD,QAAQ,CAAC,UAAU,CAAC,CAACH,KAAK;MAC3DE,iBAAiB,CAACD,SAAS,GAAG,CAACtD,QAAQ,CAACqD,KAAK,CAACC,SAAS,GAAG,CAAC;IAC7D;IACA;IACAvQ,SAAS,CAACiN,QAAQ,CAAC,CAACyD,WAAW,GAAGxJ,WAAW,CAACwJ,WAAW;IACzDjR,mBAAmB,CAACwN,QAAQ,EAAE4C,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,CAAC;IACjE,IAAIhO,MAAM,GAAGD,SAAS,CAACoF,WAAW,CAACE,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjD,IAAIuJ,cAAc,GAAGzJ,WAAW,CAACE,GAAG,CAAC,gBAAgB,CAAC;IACtD6F,QAAQ,CAAC0C,QAAQ,CAAC;MAChB5N,MAAM,EAAEA,MAAM;MACd4O,cAAc,EAAEA,cAAc;MAC9B9N,YAAY,EAAEA;IAChB,CAAC,CAAC;IACF,IAAIsK,OAAO,EAAE;MACX,IAAIyD,eAAe,GAAGzO,IAAI,CAAC0O,kBAAkB,CAAC,iBAAiB,CAAC;MAChE,IAAIC,eAAe,GAAG,CAAC;MACvB3D,OAAO,CAAC6C,QAAQ,CAACvR,MAAM,CAACwR,QAAQ,CAACrD,cAAc,CAACmE,YAAY,CAAC,CAAC,EAAE;QAC9DZ,IAAI,EAAE3B,WAAW;QACjBwC,OAAO,EAAE,GAAG;QACZX,QAAQ,EAAE,OAAO;QACjBY,KAAK,EAAE9O,IAAI,CAACyC,SAAS,CAAC,OAAO,CAAC,CAACqM;MACjC,CAAC,CAAC,CAAC;MACH,IAAIL,eAAe,EAAE;QACnBE,eAAe,GAAGhP,SAAS,CAAC8O,eAAe,CAACxJ,GAAG,CAAC,QAAQ,CAAC,CAAC;MAC5D;MACA+F,OAAO,CAACwC,QAAQ,CAAC;QACf5N,MAAM,EAAEA,MAAM;QACd+O,eAAe,EAAEA,eAAe;QAChCH,cAAc,EAAEA,cAAc;QAC9B9N,YAAY,EAAEA;MAChB,CAAC,CAAC;MACFtD,wBAAwB,CAAC4N,OAAO,EAAEjG,WAAW,EAAE,WAAW,CAAC;MAC3D;MACAlH,SAAS,CAACmN,OAAO,CAAC,CAACuD,WAAW,GAAGxJ,WAAW,CAACwJ,WAAW;MACxDjR,mBAAmB,CAAC0N,OAAO,EAAE0C,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,CAAC;IAClE;IACA,IAAImB,eAAe,GAAG,IAAI,CAAC5E,gBAAgB;IAC3CnK,IAAI,CAAC2L,iBAAiB,CAAC,UAAUC,EAAE,EAAE;MACnC;MACAA,EAAE,KAAKA,EAAE,CAACoD,kBAAkB,GAAGD,eAAe,CAAC;IACjD,CAAC,CAAC;IACF,IAAI,CAAChE,SAAS,CAACiE,kBAAkB,GAAGD,eAAe;IACnD,IAAI,CAACrD,KAAK,GAAG1L,IAAI;IACjB;IACA,IAAI,CAAC6K,SAAS,GAAG9K,QAAQ;IACzB,IAAI,CAACsN,gBAAgB,GAAG/B,eAAe;IACvC,IAAI,CAACgC,OAAO,GAAG/O,MAAM;IACrB,IAAI,CAAC+N,KAAK,GAAGjG,IAAI;IACjB,IAAI,CAAC4I,YAAY,GAAG5D,WAAW;IAC/B,IAAItG,WAAW,CAACE,GAAG,CAAC,kBAAkB,CAAC,EAAE;MACvC,IAAI,CAACiK,aAAa,CAACnK,WAAW,EAAE+F,QAAQ,CAAC;MACzCE,OAAO,IAAI,IAAI,CAACkE,aAAa,CAACnK,WAAW,EAAEiG,OAAO,CAAC;IACrD;EACF,CAAC;EACD1B,QAAQ,CAACI,SAAS,CAACwF,aAAa,GAAG,UAAUnK,WAAW,EAAE6G,EAAE,EAAE;IAC5D/N,SAAS,CAAC+N,EAAE,CAAC,CAACuD,SAAS,GAAG;MACxBC,aAAa,EAAE,QAAQ;MACvBC,gBAAgB,EAAE,MAAM;MACxBC,cAAc,EAAEvK,WAAW,CAACuK,cAAc;MAC1Cf,WAAW,EAAExJ,WAAW,CAACwJ,WAAW;MACpCgB,UAAU,EAAExK,WAAW,CAACyK,IAAI;MAC5BC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EACDnG,QAAQ,CAACI,SAAS,CAACgG,SAAS,GAAG,UAAU3K,WAAW,EAAEuF,OAAO,EAAE/H,GAAG,EAAEoN,OAAO,EAAE;IAC3E,IAAI3P,IAAI,GAAG+E,WAAW,CAAC8C,OAAO,CAAC,CAAC;IAChC,IAAI9B,SAAS,GAAGpJ,SAAS,CAACiT,cAAc,CAAC5P,IAAI,EAAE2P,OAAO,CAAC;IACvD,IAAI,CAACxF,gBAAgB,CAAC,UAAU,CAAC;IACjC,IAAI,EAAEpE,SAAS,YAAY8J,KAAK,CAAC,IAAI9J,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAI,CAAC,EAAE;MACxE,IAAIxH,MAAM,GAAGyB,IAAI,CAAC0K,SAAS,CAAC,QAAQ,CAAC;MACrC,IAAIoF,MAAM,GAAG9P,IAAI,CAAC+P,gBAAgB,CAAChK,SAAS,CAAC;MAC7C,IAAI,CAAC+J,MAAM,EAAE;QACX;QACA,IAAIjR,CAAC,GAAGN,MAAM,CAACwH,SAAS,GAAG,CAAC,CAAC;QAC7B,IAAIjH,CAAC,GAAGP,MAAM,CAACwH,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC,IAAIhH,KAAK,CAACF,CAAC,CAAC,IAAIE,KAAK,CAACD,CAAC,CAAC,EAAE;UACxB;UACA;QACF;QACA;QACA,IAAI,IAAI,CAACsN,mBAAmB,IAAI,CAAC,IAAI,CAACA,mBAAmB,CAAC4D,OAAO,CAACnR,CAAC,EAAEC,CAAC,CAAC,EAAE;UACvE;QACF;QACA,IAAImR,MAAM,GAAGlL,WAAW,CAACE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC3C,IAAIiL,CAAC,GAAGnL,WAAW,CAACE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;QACjC6K,MAAM,GAAG,IAAItT,SAAS,CAACwD,IAAI,EAAE+F,SAAS,CAAC;QACvC+J,MAAM,CAACjR,CAAC,GAAGA,CAAC;QACZiR,MAAM,CAAChR,CAAC,GAAGA,CAAC;QACZgR,MAAM,CAACK,IAAI,CAACF,MAAM,EAAEC,CAAC,CAAC;QACtB;QACA,IAAIE,WAAW,GAAGN,MAAM,CAACO,aAAa,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;QACzD,IAAIF,WAAW,EAAE;UACfA,WAAW,CAACH,MAAM,GAAGA,MAAM;UAC3BG,WAAW,CAACF,CAAC,GAAGA,CAAC;UACjBE,WAAW,CAACG,EAAE,GAAG,IAAI,CAACxF,SAAS,CAACwF,EAAE,GAAG,CAAC;QACxC;QACAT,MAAM,CAACjE,MAAM,GAAG,IAAI;QACpB7L,IAAI,CAAC+L,gBAAgB,CAAChG,SAAS,EAAE+J,MAAM,CAAC;QACxC;QACAA,MAAM,CAACU,mBAAmB,CAAC,IAAI,CAAC;QAChC,IAAI,CAACzG,KAAK,CAACC,GAAG,CAAC8F,MAAM,CAAC;MACxB;MACAA,MAAM,CAACJ,SAAS,CAAC,CAAC;IACpB,CAAC,MAAM;MACL;MACA5S,SAAS,CAAC4M,SAAS,CAACgG,SAAS,CAACe,IAAI,CAAC,IAAI,EAAE1L,WAAW,EAAEuF,OAAO,EAAE/H,GAAG,EAAEoN,OAAO,CAAC;IAC9E;EACF,CAAC;EACDrG,QAAQ,CAACI,SAAS,CAACgH,QAAQ,GAAG,UAAU3L,WAAW,EAAEuF,OAAO,EAAE/H,GAAG,EAAEoN,OAAO,EAAE;IAC1E,IAAI3P,IAAI,GAAG+E,WAAW,CAAC8C,OAAO,CAAC,CAAC;IAChC,IAAI9B,SAAS,GAAGpJ,SAAS,CAACiT,cAAc,CAAC5P,IAAI,EAAE2P,OAAO,CAAC;IACvD,IAAI,CAACxF,gBAAgB,CAAC,QAAQ,CAAC;IAC/B,IAAIpE,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAI,CAAC,EAAE;MACvC,IAAI+J,MAAM,GAAG9P,IAAI,CAAC+P,gBAAgB,CAAChK,SAAS,CAAC;MAC7C,IAAI+J,MAAM,EAAE;QACV,IAAIA,MAAM,CAACjE,MAAM,EAAE;UACjB7L,IAAI,CAAC+L,gBAAgB,CAAChG,SAAS,EAAE,IAAI,CAAC;UACtC,IAAI,CAACgE,KAAK,CAAC+B,MAAM,CAACgE,MAAM,CAAC;QAC3B,CAAC,MAAM;UACLA,MAAM,CAACY,QAAQ,CAAC,CAAC;QACnB;MACF;IACF,CAAC,MAAM;MACL;MACA;MACA;MACA5T,SAAS,CAAC4M,SAAS,CAACgH,QAAQ,CAACD,IAAI,CAAC,IAAI,EAAE1L,WAAW,EAAEuF,OAAO,EAAE/H,GAAG,EAAEoN,OAAO,CAAC;IAC7E;EACF,CAAC;EACDrG,QAAQ,CAACI,SAAS,CAACS,gBAAgB,GAAG,UAAUwG,OAAO,EAAE;IACvD,IAAI3F,OAAO,GAAG,IAAI,CAACC,QAAQ;IAC3B5N,aAAa,CAAC,IAAI,CAAC0N,SAAS,EAAE4F,OAAO,CAAC;IACtC3F,OAAO,IAAI3N,aAAa,CAAC2N,OAAO,EAAE2F,OAAO,CAAC;EAC5C,CAAC;EACDrH,QAAQ,CAACI,SAAS,CAACmD,YAAY,GAAG,UAAUtO,MAAM,EAAE;IAClD,IAAIuM,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC7B;IACA,IAAID,QAAQ,EAAE;MACZ,IAAI,CAACZ,UAAU,CAAC4B,MAAM,CAAChB,QAAQ,CAAC;IAClC;IACAA,QAAQ,GAAG,IAAIlO,UAAU,CAAC;MACxB+L,KAAK,EAAE;QACLpK,MAAM,EAAEA;MACV,CAAC;MACDqS,sBAAsB,EAAE,CAAC;MACzBL,EAAE,EAAE;IACN,CAAC,CAAC;IACF,IAAI,CAACrG,UAAU,CAACF,GAAG,CAACc,QAAQ,CAAC;IAC7B,IAAI,CAACC,SAAS,GAAGD,QAAQ;IACzB,OAAOA,QAAQ;EACjB,CAAC;EACDxB,QAAQ,CAACI,SAAS,CAACoD,WAAW,GAAG,UAAUvO,MAAM,EAAE+M,eAAe,EAAE;IAClE,IAAIN,OAAO,GAAG,IAAI,CAACC,QAAQ;IAC3B;IACA,IAAID,OAAO,EAAE;MACX,IAAI,CAACd,UAAU,CAAC4B,MAAM,CAACd,OAAO,CAAC;IACjC;IACAA,OAAO,GAAG,IAAInO,SAAS,CAAC;MACtB8L,KAAK,EAAE;QACLpK,MAAM,EAAEA,MAAM;QACd+M,eAAe,EAAEA;MACnB,CAAC;MACDsF,sBAAsB,EAAE;IAC1B,CAAC,CAAC;IACF,IAAI,CAAC1G,UAAU,CAACF,GAAG,CAACgB,OAAO,CAAC;IAC5B,IAAI,CAACC,QAAQ,GAAGD,OAAO;IACvB,OAAOA,OAAO;EAChB,CAAC;EACD1B,QAAQ,CAACI,SAAS,CAACkD,yBAAyB,GAAG,UAAU5M,IAAI,EAAED,QAAQ,EAAE0M,SAAS,EAAE;IAClF,IAAIoE,oBAAoB;IACxB,IAAIlG,eAAe;IACnB,IAAIhK,QAAQ,GAAGZ,QAAQ,CAACa,WAAW,CAAC,CAAC;IACrC,IAAIkQ,aAAa,GAAGnQ,QAAQ,CAACuI,OAAO;IACpC,IAAInJ,QAAQ,CAAC2C,IAAI,KAAK,aAAa,EAAE;MACnCmO,oBAAoB,GAAGlQ,QAAQ,CAAC6F,YAAY,CAAC,CAAC;MAC9CmE,eAAe,GAAG,KAAK;IACzB,CAAC,MAAM,IAAI5K,QAAQ,CAAC2C,IAAI,KAAK,OAAO,EAAE;MACpCmO,oBAAoB,GAAGlQ,QAAQ,CAACG,GAAG,KAAK,OAAO;MAC/C6J,eAAe,GAAG,IAAI;IACxB;IACA,IAAI5F,WAAW,GAAG/E,IAAI,CAAC+Q,SAAS;IAChC,IAAIC,cAAc,GAAGjM,WAAW,CAACE,GAAG,CAAC,mBAAmB,CAAC;IACzD,IAAI3I,MAAM,CAAC2U,UAAU,CAACD,cAAc,CAAC,EAAE;MACrCA,cAAc,GAAGA,cAAc,CAAC,IAAI,CAAC;IACvC;IACA,IAAIE,WAAW,GAAGnM,WAAW,CAACE,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACxD,IAAIkM,gBAAgB,GAAG7U,MAAM,CAAC2U,UAAU,CAACC,WAAW,CAAC,GAAGA,WAAW,CAAC,IAAI,CAAC,GAAGA,WAAW;IACvFlR,IAAI,CAAC2L,iBAAiB,CAAC,UAAUmE,MAAM,EAAEzP,GAAG,EAAE;MAC5C,IAAIuL,EAAE,GAAGkE,MAAM;MACf,IAAIlE,EAAE,EAAE;QACN,IAAIwF,KAAK,GAAG,CAACtB,MAAM,CAACjR,CAAC,EAAEiR,MAAM,CAAChR,CAAC,CAAC;QAChC,IAAIuS,KAAK,GAAG,KAAK,CAAC;QAClB,IAAIC,GAAG,GAAG,KAAK,CAAC;QAChB,IAAIC,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI9E,SAAS,EAAE;UACb,IAAI9B,eAAe,EAAE;YACnB,IAAI6G,SAAS,GAAG/E,SAAS;YACzB,IAAIvK,KAAK,GAAGnC,QAAQ,CAAC0R,YAAY,CAACL,KAAK,CAAC;YACxC,IAAIP,oBAAoB,EAAE;cACxBQ,KAAK,GAAGG,SAAS,CAACE,UAAU;cAC5BJ,GAAG,GAAGE,SAAS,CAACG,QAAQ;cACxBJ,OAAO,GAAG,CAACrP,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGlD,IAAI,CAAC4S,EAAE;YACrC,CAAC,MAAM;cACLP,KAAK,GAAGG,SAAS,CAACtF,EAAE;cACpBoF,GAAG,GAAGE,SAAS,CAACrF,CAAC;cACjBoF,OAAO,GAAGrP,KAAK,CAAC,CAAC,CAAC;YACpB;UACF,CAAC,MAAM;YACL,IAAI2P,QAAQ,GAAGpF,SAAS;YACxB,IAAIoE,oBAAoB,EAAE;cACxBQ,KAAK,GAAGQ,QAAQ,CAAChT,CAAC;cAClByS,GAAG,GAAGO,QAAQ,CAAChT,CAAC,GAAGgT,QAAQ,CAAChJ,KAAK;cACjC0I,OAAO,GAAGzB,MAAM,CAACjR,CAAC;YACpB,CAAC,MAAM;cACLwS,KAAK,GAAGQ,QAAQ,CAAC/S,CAAC,GAAG+S,QAAQ,CAAC/I,MAAM;cACpCwI,GAAG,GAAGO,QAAQ,CAAC/S,CAAC;cAChByS,OAAO,GAAGzB,MAAM,CAAChR,CAAC;YACpB;UACF;QACF;QACA,IAAIgT,KAAK,GAAGR,GAAG,KAAKD,KAAK,GAAG,CAAC,GAAG,CAACE,OAAO,GAAGF,KAAK,KAAKC,GAAG,GAAGD,KAAK,CAAC;QACjE,IAAIP,aAAa,EAAE;UACjBgB,KAAK,GAAG,CAAC,GAAGA,KAAK;QACnB;QACA,IAAIC,KAAK,GAAGzV,MAAM,CAAC2U,UAAU,CAACC,WAAW,CAAC,GAAGA,WAAW,CAAC7Q,GAAG,CAAC,GAAG2Q,cAAc,GAAGc,KAAK,GAAGX,gBAAgB;QACzG,IAAIa,UAAU,GAAGpG,EAAE,CAACyE,aAAa,CAAC,CAAC;QACnC,IAAI4B,IAAI,GAAGD,UAAU,CAAC1B,cAAc,CAAC,CAAC;QACtC1E,EAAE,CAACpD,IAAI,CAAC;UACN0J,MAAM,EAAE,CAAC;UACTC,MAAM,EAAE;QACV,CAAC,CAAC;QACFvG,EAAE,CAACwG,SAAS,CAAC;UACXF,MAAM,EAAE,CAAC;UACTC,MAAM,EAAE;QACV,CAAC,EAAE;UACDE,QAAQ,EAAE,GAAG;UACbC,UAAU,EAAE,IAAI;UAChBP,KAAK,EAAEA;QACT,CAAC,CAAC;QACF,IAAIE,IAAI,EAAE;UACRA,IAAI,CAACM,WAAW,CAAC;YACfpE,KAAK,EAAE;cACLU,OAAO,EAAE;YACX;UACF,CAAC,EAAE;YACDwD,QAAQ,EAAE,GAAG;YACbN,KAAK,EAAEA;UACT,CAAC,CAAC;QACJ;QACAC,UAAU,CAACQ,qBAAqB,GAAG,IAAI;MACzC;IACF,CAAC,CAAC;EACJ,CAAC;EACDlJ,QAAQ,CAACI,SAAS,CAACqD,qBAAqB,GAAG,UAAUhI,WAAW,EAAEhF,QAAQ,EAAE0S,YAAY,EAAE;IACxF,IAAIzJ,aAAa,GAAGjE,WAAW,CAAC2C,QAAQ,CAAC,UAAU,CAAC;IACpD,IAAIL,oBAAoB,CAACtC,WAAW,CAAC,EAAE;MACrC,IAAI2N,MAAM,GAAG3N,WAAW,CAAC8C,OAAO,CAAC,CAAC;MAClC,IAAIiD,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC7B;MACA,IAAIxM,MAAM,GAAGmU,MAAM,CAAChI,SAAS,CAAC,QAAQ,CAAC;MACvC,IAAI,CAACnM,MAAM,EAAE;QACXuM,QAAQ,CAAC6H,iBAAiB,CAAC,CAAC;QAC5B,IAAI,CAACrK,SAAS,GAAG,IAAI;QACrB;MACF;MACA,IAAID,QAAQ,GAAG,IAAI,CAACC,SAAS;MAC7B,IAAI,CAACD,QAAQ,EAAE;QACbA,QAAQ,GAAG,IAAI,CAACC,SAAS,GAAG,IAAI5L,OAAO,CAACkW,IAAI,CAAC;UAC3CrC,EAAE,EAAE,GAAG,CAAC;QACV,CAAC,CAAC;QACFlI,QAAQ,CAACwK,UAAU,GAAG,IAAI;QAC1B/H,QAAQ,CAACgI,cAAc,CAAC,IAAI,CAACxK,SAAS,CAAC;QACvCwC,QAAQ,CAAC0H,qBAAqB,GAAG,IAAI;MACvC;MACA;MACA,IAAIzM,SAAS,GAAGW,mBAAmB,CAACnI,MAAM,CAAC;MAC3C,IAAIwH,SAAS,IAAI,CAAC,EAAE;QAClBvI,aAAa,CAACsN,QAAQ,EAAErN,oBAAoB,CAACsH,WAAW,EAAE,UAAU,CAAC,EAAE;UACrE0N,YAAY,EAAEA,YAAY;UAC1BM,YAAY,EAAEhO,WAAW;UACzBiO,cAAc,EAAEjN,SAAS;UACzBkN,WAAW,EAAE,SAAAA,CAAUlN,SAAS,EAAEmN,GAAG,EAAEC,iBAAiB,EAAE;YACxD,OAAOA,iBAAiB,IAAI,IAAI,GAAGvV,2BAA2B,CAAC8U,MAAM,EAAES,iBAAiB,CAAC,GAAGxV,eAAe,CAAC+U,MAAM,EAAE3M,SAAS,CAAC;UAChI,CAAC;UACDqN,gBAAgB,EAAE;QACpB,CAAC,EAAErK,yBAAyB,CAACC,aAAa,EAAEjJ,QAAQ,CAAC,CAAC;QACtD+K,QAAQ,CAACuI,UAAU,CAACC,QAAQ,GAAG,IAAI;MACrC;IACF,CAAC,MAAM,IAAI,IAAI,CAAChL,SAAS,EAAE;MACzB,IAAI,CAACyC,SAAS,CAAC4H,iBAAiB,CAAC,CAAC;MAClC,IAAI,CAACrK,SAAS,GAAG,IAAI;IACvB;EACF,CAAC;EACDgB,QAAQ,CAACI,SAAS,CAACvB,iBAAiB,GAAG,UAAUF,OAAO,EAAEC,QAAQ,EAAElI,IAAI,EAAEuT,eAAe,EAAEC,cAAc,EAAExK,aAAa,EAAEjJ,QAAQ,EAAE;IAClI,IAAIsI,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC7B,IAAIwC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC7B,IAAI1C,QAAQ,EAAE;MACZ;MACA;MACA,IAAIJ,OAAO,GAAG,CAAC,IAAIsL,eAAe,CAAChL,SAAS,IAAI,IAAI,EAAE;QACpDgL,eAAe,CAAChL,SAAS,GAAGF,QAAQ,CAACxJ,CAAC;QACtC0U,eAAe,CAAC9K,SAAS,GAAGJ,QAAQ,CAACvJ,CAAC;MACxC;MACA,IAAIP,MAAM,GAAGyB,IAAI,CAAC0K,SAAS,CAAC,QAAQ,CAAC;MACrC,IAAI3F,WAAW,GAAG/E,IAAI,CAAC+Q,SAAS;MAChC,IAAIrQ,YAAY,GAAGqE,WAAW,CAACE,GAAG,CAAC,cAAc,CAAC;MAClD,IAAIwO,SAAS,GAAGzK,aAAa,CAAC/D,GAAG,CAAC,WAAW,CAAC;MAC9C,IAAIyO,QAAQ,GAAG1K,aAAa,CAAC/D,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;MACjD,IAAItE,QAAQ,GAAGZ,QAAQ,CAACa,WAAW,CAAC,CAAC;MACrC,IAAI4F,YAAY,GAAG7F,QAAQ,CAAC6F,YAAY,CAAC,CAAC;MAC1C,IAAIyC,cAAc,GAAGtI,QAAQ,CAACuI,OAAO;MACrC,IAAIuD,SAAS,GAAGvE,QAAQ,CAACS,KAAK;MAC9B,IAAI9B,IAAI,GAAGoC,cAAc,GAAGzC,YAAY,GAAGiG,SAAS,CAAC5N,CAAC,GAAG4N,SAAS,CAAC3N,CAAC,GAAG2N,SAAS,CAAC3D,MAAM,GAAGtC,YAAY,GAAGiG,SAAS,CAAC5N,CAAC,GAAG4N,SAAS,CAAC5D,KAAK,GAAG4D,SAAS,CAAC3N,CAAC;MACpJ,IAAI6U,SAAS,GAAG,CAACnN,YAAY,GAAGkN,QAAQ,GAAG,CAAC,KAAKzK,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACzE,IAAI2K,SAAS,GAAG,CAACpN,YAAY,GAAG,CAAC,GAAG,CAACkN,QAAQ,KAAKzK,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1E,IAAInI,GAAG,GAAG0F,YAAY,GAAG,GAAG,GAAG,GAAG;MAClC,IAAIqN,cAAc,GAAGjN,aAAa,CAACrI,MAAM,EAAEsI,IAAI,EAAE/F,GAAG,CAAC;MACrD,IAAIgT,OAAO,GAAGD,cAAc,CAAC1M,KAAK;MAClC,IAAI4M,IAAI,GAAGD,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;MAClC,IAAIlQ,KAAK,GAAG,KAAK,CAAC;MAClB,IAAImQ,IAAI,IAAI,CAAC,EAAE;QACb;QACA,IAAIA,IAAI,GAAG,CAAC,IAAI,CAACrT,YAAY,EAAE;UAC7B,IAAIJ,EAAE,GAAGqG,eAAe,CAACpI,MAAM,EAAEuV,OAAO,CAAC,CAAC,CAAC,CAAC;UAC5CzL,QAAQ,CAACG,IAAI,CAAC;YACZ3J,CAAC,EAAEyB,EAAE,CAAC,CAAC,CAAC,GAAGqT,SAAS;YACpB7U,CAAC,EAAEwB,EAAE,CAAC,CAAC,CAAC,GAAGsT;UACb,CAAC,CAAC;UACFJ,cAAc,KAAK5P,KAAK,GAAGmB,WAAW,CAACiP,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,MAAM;UACL,IAAIxT,EAAE,GAAGwK,QAAQ,CAACmJ,UAAU,CAACpN,IAAI,EAAE/F,GAAG,CAAC;UACvCR,EAAE,IAAI+H,QAAQ,CAACG,IAAI,CAAC;YAClB3J,CAAC,EAAEyB,EAAE,CAAC,CAAC,CAAC,GAAGqT,SAAS;YACpB7U,CAAC,EAAEwB,EAAE,CAAC,CAAC,CAAC,GAAGsT;UACb,CAAC,CAAC;UACF,IAAIM,UAAU,GAAGnP,WAAW,CAACiP,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;UACpD,IAAIK,QAAQ,GAAGpP,WAAW,CAACiP,WAAW,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;UAClDN,cAAc,KAAK5P,KAAK,GAAGjH,SAAS,CAACyX,oBAAoB,CAACpU,IAAI,EAAEyT,SAAS,EAAES,UAAU,EAAEC,QAAQ,EAAEN,cAAc,CAACzM,CAAC,CAAC,CAAC;QACrH;QACAmM,eAAe,CAACxL,cAAc,GAAG+L,OAAO,CAAC,CAAC,CAAC;MAC7C,CAAC,MAAM;QACL;QACA;QACA,IAAIzT,GAAG,GAAG4H,OAAO,KAAK,CAAC,IAAIsL,eAAe,CAACxL,cAAc,GAAG,CAAC,GAAG+L,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QAC9E,IAAIxT,EAAE,GAAGqG,eAAe,CAACpI,MAAM,EAAE8B,GAAG,CAAC;QACrCmT,cAAc,KAAK5P,KAAK,GAAGmB,WAAW,CAACiP,WAAW,CAAC3T,GAAG,CAAC,CAAC;QACxDgI,QAAQ,CAACG,IAAI,CAAC;UACZ3J,CAAC,EAAEyB,EAAE,CAAC,CAAC,CAAC,GAAGqT,SAAS;UACpB7U,CAAC,EAAEwB,EAAE,CAAC,CAAC,CAAC,GAAGsT;QACb,CAAC,CAAC;MACJ;MACA,IAAIJ,cAAc,EAAE;QAClB,IAAIa,KAAK,GAAG3W,UAAU,CAAC2K,QAAQ,CAAC;QAChC,IAAI,OAAOgM,KAAK,CAACC,YAAY,KAAK,UAAU,EAAE;UAC5CD,KAAK,CAACC,YAAY,CAAC1Q,KAAK,CAAC;QAC3B;MACF;IACF;EACF,CAAC;EACD;AACF;AACA;EACE;EACA0F,QAAQ,CAACI,SAAS,CAAC6D,kBAAkB,GAAG,UAAUvN,IAAI,EAAEsL,eAAe,EAAEvL,QAAQ,EAAEwC,GAAG,EAAE8D,IAAI,EAAEgF,WAAW,EAAE3K,YAAY,EAAE;IACvH,IAAIoK,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC7B,IAAIC,OAAO,GAAG,IAAI,CAACC,QAAQ;IAC3B,IAAIlG,WAAW,GAAG/E,IAAI,CAAC+Q,SAAS;IAChC,IAAIgD,IAAI,GAAGtX,iBAAiB,CAAC,IAAI,CAACiP,KAAK,EAAE1L,IAAI,EAAE,IAAI,CAACqN,gBAAgB,EAAE/B,eAAe,EAAE,IAAI,CAACT,SAAS,EAAE9K,QAAQ,EAAE,IAAI,CAACkP,YAAY,EAAE5D,WAAW,CAAC;IAChJ,IAAIkG,OAAO,GAAGwC,IAAI,CAACxC,OAAO;IAC1B,IAAIgD,gBAAgB,GAAGR,IAAI,CAACQ,gBAAgB;IAC5C,IAAIC,IAAI,GAAGT,IAAI,CAACS,IAAI;IACpB,IAAIC,aAAa,GAAGV,IAAI,CAACU,aAAa;IACtC,IAAIpO,IAAI,EAAE;MACR;MACAkO,gBAAgB,GAAGhU,kBAAkB,CAACwT,IAAI,CAACQ,gBAAgB,EAAER,IAAI,CAACxC,OAAO,EAAExR,QAAQ,EAAEsG,IAAI,EAAE3F,YAAY,CAAC;MACxG6Q,OAAO,GAAGhR,kBAAkB,CAACwT,IAAI,CAACxC,OAAO,EAAE,IAAI,EAAExR,QAAQ,EAAEsG,IAAI,EAAE3F,YAAY,CAAC;MAC9E+T,aAAa,GAAGlU,kBAAkB,CAACwT,IAAI,CAACU,aAAa,EAAEV,IAAI,CAACS,IAAI,EAAEzU,QAAQ,EAAEsG,IAAI,EAAE3F,YAAY,CAAC;MAC/F8T,IAAI,GAAGjU,kBAAkB,CAACwT,IAAI,CAACS,IAAI,EAAE,IAAI,EAAEzU,QAAQ,EAAEsG,IAAI,EAAE3F,YAAY,CAAC;IAC1E;IACA;IACA;IACA;IACA,IAAIvB,eAAe,CAACoS,OAAO,EAAEiD,IAAI,CAAC,GAAG,IAAI,IAAIxJ,OAAO,IAAI7L,eAAe,CAACoV,gBAAgB,EAAEE,aAAa,CAAC,GAAG,IAAI,EAAE;MAC/G3J,QAAQ,CAAC4J,aAAa,CAAC,CAAC;MACxB5J,QAAQ,CAAC0C,QAAQ,CAAC;QAChBjP,MAAM,EAAEiW;MACV,CAAC,CAAC;MACF,IAAIxJ,OAAO,EAAE;QACXA,OAAO,CAAC0J,aAAa,CAAC,CAAC;QACvB1J,OAAO,CAACwC,QAAQ,CAAC;UACfjP,MAAM,EAAEiW,IAAI;UACZlJ,eAAe,EAAEmJ;QACnB,CAAC,CAAC;MACJ;MACA;IACF;IACA3J,QAAQ,CAACnC,KAAK,CAACgM,QAAQ,GAAGZ,IAAI,CAACxC,OAAO;IACtCzG,QAAQ,CAACnC,KAAK,CAACpK,MAAM,GAAGgT,OAAO;IAC/B,IAAIqD,MAAM,GAAG;MACXjM,KAAK,EAAE;QACLpK,MAAM,EAAEiW;MACV;IACF,CAAC;IACD;IACA;IACA,IAAIT,IAAI,CAACxC,OAAO,KAAKA,OAAO,EAAE;MAC5BqD,MAAM,CAACjM,KAAK,CAACgM,QAAQ,GAAGZ,IAAI,CAACS,IAAI;IACnC;IACA;IACA1J,QAAQ,CAAC4J,aAAa,CAAC,CAAC;IACxBhY,OAAO,CAACmY,WAAW,CAAC/J,QAAQ,EAAE8J,MAAM,EAAE7P,WAAW,CAAC;IAClD,IAAIiG,OAAO,EAAE;MACXA,OAAO,CAACwC,QAAQ,CAAC;QACf;QACAjP,MAAM,EAAEgT,OAAO;QACfjG,eAAe,EAAEiJ;MACnB,CAAC,CAAC;MACFvJ,OAAO,CAAC0J,aAAa,CAAC,CAAC;MACvBhY,OAAO,CAACmY,WAAW,CAAC7J,OAAO,EAAE;QAC3BrC,KAAK,EAAE;UACL2C,eAAe,EAAEmJ;QACnB;MACF,CAAC,EAAE1P,WAAW,CAAC;MACf;MACA,IAAI+F,QAAQ,CAACnC,KAAK,CAACpK,MAAM,KAAKyM,OAAO,CAACrC,KAAK,CAACpK,MAAM,EAAE;QAClDyM,OAAO,CAACrC,KAAK,CAACpK,MAAM,GAAGuM,QAAQ,CAACnC,KAAK,CAACpK,MAAM;MAC9C;IACF;IACA,IAAIuW,eAAe,GAAG,EAAE;IACxB,IAAIC,UAAU,GAAGhB,IAAI,CAACiB,MAAM;IAC5B,KAAK,IAAI3W,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0W,UAAU,CAAC3W,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC1C,IAAI4W,GAAG,GAAGF,UAAU,CAAC1W,CAAC,CAAC,CAAC4W,GAAG;MAC3B,IAAIA,GAAG,KAAK,GAAG,EAAE;QACf,IAAIrJ,EAAE,GAAG5L,IAAI,CAAC+P,gBAAgB,CAACgF,UAAU,CAAC1W,CAAC,CAAC,CAAC6W,IAAI,CAAC;QAClD,IAAItJ,EAAE,EAAE;UACNkJ,eAAe,CAAC1T,IAAI,CAAC;YACnBwK,EAAE,EAAEA,EAAE;YACNuJ,KAAK,EAAE9W,CAAC,CAAC;UACX,CAAC,CAAC;QACJ;MACF;IACF;IACA,IAAIyM,QAAQ,CAACsK,SAAS,IAAItK,QAAQ,CAACsK,SAAS,CAAChX,MAAM,EAAE;MACnD0M,QAAQ,CAACsK,SAAS,CAAC,CAAC,CAAC,CAACpN,MAAM,CAAC,YAAY;QACvCgD,OAAO,IAAIA,OAAO,CAACqK,UAAU,CAAC,CAAC;QAC/B,IAAI9W,MAAM,GAAGuM,QAAQ,CAACnC,KAAK,CAACgM,QAAQ;QACpC,KAAK,IAAItW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyW,eAAe,CAAC1W,MAAM,EAAEC,CAAC,EAAE,EAAE;UAC/C,IAAIuN,EAAE,GAAGkJ,eAAe,CAACzW,CAAC,CAAC,CAACuN,EAAE;UAC9B,IAAIlH,MAAM,GAAGoQ,eAAe,CAACzW,CAAC,CAAC,CAAC8W,KAAK,GAAG,CAAC;UACzCvJ,EAAE,CAAC/M,CAAC,GAAGN,MAAM,CAACmG,MAAM,CAAC;UACrBkH,EAAE,CAAC9M,CAAC,GAAGP,MAAM,CAACmG,MAAM,GAAG,CAAC,CAAC;UACzBkH,EAAE,CAAC0J,UAAU,CAAC,CAAC;QACjB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACDhM,QAAQ,CAACI,SAAS,CAACoC,MAAM,GAAG,UAAUxB,OAAO,EAAE;IAC7C,IAAIP,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI0B,OAAO,GAAG,IAAI,CAACC,KAAK;IACxB,IAAI,CAACxB,UAAU,CAACqL,SAAS,CAAC,CAAC;IAC3B,IAAI,CAACtL,WAAW,CAAC6B,MAAM,CAAC,IAAI,CAAC;IAC7B;IACAL,OAAO,IAAIA,OAAO,CAACE,iBAAiB,CAAC,UAAUC,EAAE,EAAEvL,GAAG,EAAE;MACtD,IAAIuL,EAAE,CAACC,MAAM,EAAE;QACb9B,KAAK,CAAC+B,MAAM,CAACF,EAAE,CAAC;QAChBH,OAAO,CAACM,gBAAgB,CAAC1L,GAAG,EAAE,IAAI,CAAC;MACrC;IACF,CAAC,CAAC;IACF,IAAI,CAAC0K,SAAS,GAAG,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACJ,SAAS,GAAG,IAAI,CAACyC,OAAO,GAAG,IAAI,CAACD,gBAAgB,GAAG,IAAI,CAAC/E,SAAS,GAAG,IAAI,CAACoD,KAAK,GAAG,IAAI;EAC7H,CAAC;EACDpC,QAAQ,CAAC5G,IAAI,GAAG,MAAM;EACtB,OAAO4G,QAAQ;AACjB,CAAC,CAACxM,SAAS,CAAC;AACZ,eAAewM,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}