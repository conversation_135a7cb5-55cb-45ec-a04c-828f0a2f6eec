{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/components/space-template-selector/space-template-selector.service\";\nimport * as i10 from \"@angular/forms\";\nconst _c0 = [\"batchEditDialog\"];\nconst _c1 = () => [];\nfunction RequirementManagementComponent_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_67_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const batchEditDialog_r6 = i0.ɵɵreference(103);\n      return i0.ɵɵresetView(ctx_r4.openBatchEdit(batchEditDialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u6279\\u6B21\\u7DE8\\u8F2F (\", ctx_r4.selectedItems.length, \")\");\n  }\n}\nfunction RequirementManagementComponent_button_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_68_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r8 = i0.ɵɵreference(101);\n      return i0.ɵɵresetView(ctx_r4.add(dialog_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 44);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_98_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_98_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const data_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r8 = i0.ɵɵreference(101);\n      return i0.ɵɵresetView(ctx_r4.onEdit(data_r10, dialog_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_98_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_98_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const data_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onDelete(data_r10));\n    });\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_98_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 45)(1, \"td\", 46)(2, \"nb-checkbox\", 35);\n    i0.ɵɵlistener(\"ngModelChange\", function RequirementManagementComponent_tr_98_Template_nb_checkbox_ngModelChange_2_listener() {\n      const data_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleItemSelection(data_r10));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 47);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 48);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 48);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 48);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 48);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 48);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 48);\n    i0.ɵɵtemplate(22, RequirementManagementComponent_tr_98_button_22_Template, 3, 0, \"button\", 49)(23, RequirementManagementComponent_tr_98_button_23_Template, 3, 0, \"button\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r10 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.isItemSelected(data_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getHouseType(data_r10.CHouseType || i0.ɵɵpureFunction0(15, _c1)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 11, data_r10.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsShowText(data_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsSimpleText(data_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 13, data_r10.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_100_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_100_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_100_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r14.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r14.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_100_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r15.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r15.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_100_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 54)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_100_span_2_Template, 2, 0, \"span\", 55)(3, RequirementManagementComponent_ng_template_100_span_3_Template, 2, 0, \"span\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 56)(5, \"div\", 5)(6, \"div\", 57)(7, \"div\", 5)(8, \"app-form-group\", 58)(9, \"input\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_100_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CLocation, $event) || (ctx_r4.saveRequirement.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"app-form-group\", 58)(11, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_100_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRequirement, $event) || (ctx_r4.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"app-form-group\", 58)(13, \"input\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_100_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CSort, $event) || (ctx_r4.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"app-form-group\", 58)(15, \"nb-select\", 62);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_100_Template_nb_select_selectedChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CHouseType, $event) || (ctx_r4.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(16, RequirementManagementComponent_ng_template_100_nb_option_16_Template, 2, 2, \"nb-option\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 58)(18, \"nb-select\", 64);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_100_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CStatus, $event) || (ctx_r4.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_100_nb_option_19_Template, 2, 2, \"nb-option\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 58)(21, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_100_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnitPrice, $event) || (ctx_r4.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"app-form-group\", 58)(23, \"input\", 66);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_100_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnit, $event) || (ctx_r4.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"app-form-group\", 58)(25, \"nb-checkbox\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_100_Template_nb_checkbox_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsShow, $event) || (ctx_r4.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(26, \" \\u986F\\u793A\\u5728\\u9810\\u7D04\\u9700\\u6C42\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 58)(28, \"nb-checkbox\", 68);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_100_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsSimple, $event) || (ctx_r4.saveRequirement.CIsSimple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u8A2D\\u5B9A\\u70BA\\u7C21\\u6613\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 58)(31, \"textarea\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_100_Template_textarea_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRemark, $event) || (ctx_r4.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\")(33, \"div\", 5)(34, \"div\", 70)(35, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_100_Template_button_click_35_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r13).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.save(ref_r16));\n    });\n    i0.ɵɵtext(36, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_100_Template_button_click_37_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r13).dialogRef;\n      return i0.ɵɵresetView(ref_r16.close());\n    });\n    i0.ɵɵtext(38, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5340\\u57DF\")(\"labelFor\", \"CLocation\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CLocation);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u9810\\u7D04\\u9700\\u6C42\")(\"labelFor\", \"CIsShow\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u7C21\\u6613\\u5BA2\\u8B8A\")(\"labelFor\", \"CIsSimple\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsSimple);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_102_div_21_nb_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r21.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r21.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_ng_template_102_div_21_nb_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r22.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r22.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_ng_template_102_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"nb-card\")(2, \"nb-card-header\", 82)(3, \"h6\", 83);\n    i0.ɵɵelement(4, \"i\", 84);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_102_div_21_Template_button_click_6_listener() {\n      const i_r19 = i0.ɵɵrestoreView(_r18).index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.resetBatchEditItem(i_r19));\n    });\n    i0.ɵɵelement(7, \"i\", 24);\n    i0.ɵɵtext(8, \"\\u91CD\\u7F6E \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"nb-card-body\", 86)(10, \"div\", 5)(11, \"div\", 21)(12, \"app-form-group\", 58)(13, \"input\", 87);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_102_div_21_Template_input_ngModelChange_13_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r18).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r20.CLocation, $event) || (item_r20.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 21)(15, \"app-form-group\", 58)(16, \"input\", 88);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_102_div_21_Template_input_ngModelChange_16_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r18).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r20.CRequirement, $event) || (item_r20.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 21)(18, \"app-form-group\", 58)(19, \"nb-select\", 89);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_102_div_21_Template_nb_select_selectedChange_19_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r18).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r20.CHouseType, $event) || (item_r20.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(20, RequirementManagementComponent_ng_template_102_div_21_nb_option_20_Template, 2, 2, \"nb-option\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 21)(22, \"app-form-group\", 58)(23, \"input\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_102_div_21_Template_input_ngModelChange_23_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r18).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r20.CSort, $event) || (item_r20.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 21)(25, \"app-form-group\", 58)(26, \"nb-select\", 91);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_102_div_21_Template_nb_select_selectedChange_26_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r18).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r20.CStatus, $event) || (item_r20.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(27, RequirementManagementComponent_ng_template_102_div_21_nb_option_27_Template, 2, 2, \"nb-option\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 21)(29, \"app-form-group\", 58)(30, \"input\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_102_div_21_Template_input_ngModelChange_30_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r18).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r20.CUnitPrice, $event) || (item_r20.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 21)(32, \"app-form-group\", 58)(33, \"input\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_102_div_21_Template_input_ngModelChange_33_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r18).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r20.CUnit, $event) || (item_r20.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 21)(35, \"app-form-group\", 58)(36, \"nb-checkbox\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_102_div_21_Template_nb_checkbox_ngModelChange_36_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r18).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r20.CIsShow, $event) || (item_r20.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(37, \" \\u986F\\u793A\\u5728\\u9810\\u7D04\\u9700\\u6C42\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 21)(39, \"app-form-group\", 58)(40, \"nb-checkbox\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_102_div_21_Template_nb_checkbox_ngModelChange_40_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r18).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r20.CIsSimple, $event) || (item_r20.CIsSimple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(41, \" \\u8A2D\\u5B9A\\u70BA\\u7C21\\u6613\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"div\", 4)(43, \"app-form-group\", 58)(44, \"textarea\", 95);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_102_div_21_Template_textarea_ngModelChange_44_listener($event) {\n      const item_r20 = i0.ɵɵrestoreView(_r18).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r20.CRemark, $event) || (item_r20.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(45, \"                      \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const item_r20 = ctx.$implicit;\n    const i_r19 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \\u9805\\u76EE \", i_r19 + 1, \": \", item_r20.CRequirement, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"label\", \"\\u5340\\u57DF\")(\"labelFor\", \"location_\" + i_r19)(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"location_\" + i_r19);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r20.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"requirement_\" + i_r19)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"requirement_\" + i_r19);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r20.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"houseType_\" + i_r19)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"houseType_\" + i_r19);\n    i0.ɵɵtwoWayProperty(\"selected\", item_r20.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"sort_\" + i_r19)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"sort_\" + i_r19);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r20.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"status_\" + i_r19)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"status_\" + i_r19);\n    i0.ɵɵtwoWayProperty(\"selected\", item_r20.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"unitPrice_\" + i_r19)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"unitPrice_\" + i_r19);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r20.CUnitPrice);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"unit_\" + i_r19)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"unit_\" + i_r19);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r20.CUnit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u9810\\u7D04\\u9700\\u6C42\")(\"labelFor\", \"isShow_\" + i_r19)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"isShow_\" + i_r19);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r20.CIsShow);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", \"\\u7C21\\u6613\\u5BA2\\u8B8A\")(\"labelFor\", \"isSimple_\" + i_r19)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"isSimple_\" + i_r19);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r20.CIsSimple);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"remark_\" + i_r19)(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"remark_\" + i_r19);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r20.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 73)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 74)(5, \"div\", 5)(6, \"div\", 4)(7, \"div\", 75);\n    i0.ɵɵelement(8, \"i\", 76);\n    i0.ɵɵtext(9, \" \\u60A8\\u53EF\\u4EE5\\u500B\\u5225\\u4FEE\\u6539\\u6BCF\\u500B\\u9805\\u76EE\\u7684\\u6B04\\u4F4D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 77);\n    i0.ɵɵelement(11, \"i\", 78);\n    i0.ɵɵelementStart(12, \"strong\");\n    i0.ɵɵtext(13, \"\\u6CE8\\u610F\\u4E8B\\u9805\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ul\", 79)(15, \"li\");\n    i0.ɵɵtext(16, \"\\u5DE5\\u7A0B\\u9805\\u76EE\\u3001\\u985E\\u578B\\u3001\\u6392\\u5E8F\\u3001\\u72C0\\u614B\\u3001\\u55AE\\u50F9\\u3001\\u55AE\\u4F4D\\u70BA\\u5FC5\\u586B\\u6B04\\u4F4D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"li\");\n    i0.ɵɵtext(18, \"\\u6392\\u5E8F\\u548C\\u55AE\\u50F9\\u4E0D\\u80FD\\u70BA\\u8CA0\\u6578\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"li\");\n    i0.ɵɵtext(20, \"\\u5340\\u57DF\\u6700\\u591A20\\u500B\\u5B57\\uFF0C\\u5DE5\\u7A0B\\u9805\\u76EE\\u6700\\u591A50\\u500B\\u5B57\\uFF0C\\u5099\\u8A3B\\u8AAA\\u660E\\u6700\\u591A100\\u500B\\u5B57\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, RequirementManagementComponent_ng_template_102_div_21_Template, 46, 54, \"div\", 80);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"nb-card-footer\")(23, \"div\", 5)(24, \"div\", 70)(25, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_102_Template_button_click_25_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.batchSave(ref_r23));\n    });\n    i0.ɵɵtext(26, \"\\u78BA\\u5B9A\\u6279\\u6B21\\u66F4\\u65B0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_102_Template_button_click_27_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.cancelBatchEdit(ref_r23));\n    });\n    i0.ɵɵtext(28, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u6279\\u6B21\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42 (\\u5DF2\\u9078\\u64C7 \", ctx_r4.selectedItems.length, \" \\u500B\\u9805\\u76EE)\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.batchEditItems);\n  }\n}\nexport class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, templateService, pettern, router, destroyref, spaceTemplateSelectorService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.templateService = templateService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    this.spaceTemplateSelectorService = spaceTemplateSelectorService;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    // 批次編輯相關屬性\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.isBatchEditMode = false;\n    // 批次編輯時的項目資料副本\n    this.batchEditItems = [];\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CIsSimple = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CLocation = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 建案切換事件處理\n  onBuildCaseChange(newBuildCaseId) {\n    // 如果在批次編輯模式下切換建案，給予警告\n    if (this.isBatchEditMode || this.selectedItems.length > 0) {\n      const confirmMessage = this.isBatchEditMode ? '切換建案將會關閉批次編輯對話框並清除所有選擇的項目，是否繼續？' : `切換建案將會清除已選擇的 ${this.selectedItems.length} 個項目，是否繼續？`;\n      if (!confirm(confirmMessage)) {\n        // 使用者取消，恢復原來的建案選擇\n        setTimeout(() => {\n          this.getListRequirementRequest.CBuildCaseID = this.currentBuildCase;\n        }, 0);\n        return;\n      }\n    }\n    // 重置批次選擇的項目\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.batchEditItems = [];\n    this.isBatchEditMode = false;\n    // 更新當前建案並重新載入資料\n    this.currentBuildCase = newBuildCaseId;\n    this.getList();\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 清除選擇狀態和批次編輯相關狀態\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.batchEditItems = [];\n    this.isBatchEditMode = false;\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 建案頁面需要驗證建案名稱\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[排序]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 數值範圍驗證\n    if (this.saveRequirement.CSort !== null && this.saveRequirement.CSort !== undefined && this.saveRequirement.CSort < 0) {\n      this.valid.errorMessages.push('[排序] 不能為負數');\n    }\n    if (this.saveRequirement.CUnitPrice !== null && this.saveRequirement.CUnitPrice !== undefined && this.saveRequirement.CUnitPrice < 0) {\n      this.valid.errorMessages.push('[單價] 不能為負數');\n    }\n    // 長度驗證\n    if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    if (this.saveRequirement.CRequirement && this.saveRequirement.CRequirement.length > 50) {\n      this.valid.errorMessages.push('[工程項目] 不能超過50個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false,\n      CIsSimple: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 建案頁面 - 使用當前選擇的建案或第一個建案\n    if (this.currentBuildCase != 0) {\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n    } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n      this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 如果有建案時才查詢\n      if (this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 建案頁面的邏輯\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.requirementList = res.Entries;\n          this.totalRecords = res.TotalItems;\n          // 清理已選擇的項目，移除不存在於新列表中的項目\n          const originalSelectedCount = this.selectedItems.length;\n          this.selectedItems = this.selectedItems.filter(selectedItem => this.requirementList.some(listItem => listItem.CRequirementID === selectedItem.CRequirementID));\n          // 如果選擇的項目數量有變化，清理批次編輯狀態\n          if (originalSelectedCount !== this.selectedItems.length || this.selectedItems.length === 0) {\n            this.batchEditItems = [];\n            this.isBatchEditMode = false;\n          }\n          // 更新選擇狀態\n          this.updateSelectAllState();\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false,\n            CIsSimple: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CLocation = res.Entries.CLocation;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          this.saveRequirement.CSpaceId = res.Entries.CSpaceId || null;\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n          this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  getCIsSimpleText(data) {\n    return data.CIsSimple ? '是' : '否';\n  }\n  // 空間模板相關方法\n  onSpaceTemplateApplied(config) {\n    console.log('套用空間模板配置:', config);\n    // 直接將模板項目轉換為批次編輯項目，使用從模板選擇器傳來的明細數據\n    this.convertTemplatesToBatchEdit(config.selectedItems, config.templateDetails);\n  }\n  onTemplateError(errorMessage) {\n    this.message.showErrorMSG(errorMessage);\n  }\n  // 將模板項目轉換為批次編輯項目\n  convertTemplatesToBatchEdit(selectedTemplates, templateDetails) {\n    if (!this.getListRequirementRequest.CBuildCaseID) {\n      this.message.showErrorMSG('建案 ID 不存在');\n      return;\n    }\n    // 清除當前選擇的項目\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    // 取得預設排序值 - 從列表最大排序值開始\n    const maxSort = this.requirementList.length > 0 ? Math.max(...this.requirementList.map(item => item.CSort || 0)) : 0;\n    let currentSortIndex = 0;\n    const allBatchEditItems = [];\n    // 處理每個模板的明細項目\n    selectedTemplates.forEach(template => {\n      const details = templateDetails.get(template.CTemplateId) || [];\n      if (details && details.length > 0) {\n        // 將每個明細項目轉換為批次編輯項目\n        details.forEach((detail, detailIndex) => {\n          const batchEditItem = {\n            CRequirementID: undefined,\n            // 新項目沒有 ID\n            CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n            CLocation: detail.CLocation || template.CLocation || '',\n            // 優先使用明細的位置，其次使用模板位置\n            CRequirement: detail.CPart || `${template.CTemplateName} - 項目 ${detailIndex + 1}`,\n            // 使用明細名稱作為工程項目\n            CHouseType: template.CHouseType || [],\n            // 從模板獲取房屋類型，預設為空陣列\n            CSort: maxSort + currentSortIndex + 1,\n            // 從最大排序值往上遞增\n            CStatus: 1,\n            // 預設啟用\n            CUnitPrice: detail.CUnitPrice || template.CUnitPrice || 0,\n            // 優先使用明細單價，其次使用模板單價\n            CUnit: detail.CUnit || template.CUnit || '式',\n            // 優先使用明細單位，其次使用模板單位\n            CSpaceId: detail.CReleateId || template.cRelateID || null,\n            // 從明細獲取關聯空間 ID\n            CIsShow: template.CIsShow !== undefined ? template.CIsShow : false,\n            // 預設不顯示在預約需求\n            CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false,\n            // 預設不是簡易客變\n            CRemark: detail.CRemark || `從模板「${template.CTemplateName}」的明細項目「${detail.CPart}」產生` // 記錄來源模板和明細\n          };\n          allBatchEditItems.push(batchEditItem);\n          currentSortIndex++;\n        });\n      } else {\n        // 如果模板沒有明細，則將模板本身作為一個項目\n        const batchEditItem = {\n          CRequirementID: undefined,\n          // 新項目沒有 ID\n          CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n          CLocation: template.CLocation || '',\n          // 從模板獲取區域資訊\n          CRequirement: template.CTemplateName || `模板項目 ${currentSortIndex + 1}`,\n          // 使用模板名稱作為工程項目\n          CHouseType: template.CHouseType || [],\n          // 從模板獲取房屋類型，預設為空陣列\n          CSort: maxSort + currentSortIndex + 1,\n          // 從最大排序值往上遞增\n          CStatus: 1,\n          // 預設啟用\n          CUnitPrice: template.CUnitPrice || 0,\n          // 從模板獲取單價，預設為 0\n          CUnit: template.CUnit || '式',\n          // 從模板獲取單位，預設為 '式'\n          CSpaceId: template.cRelateID || null,\n          // 從模板獲取關聯空間 ID\n          CIsShow: template.CIsShow !== undefined ? template.CIsShow : false,\n          // 預設不顯示在預約需求\n          CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false,\n          // 預設不是簡易客變\n          CRemark: template.CRemark || `從模板「${template.CTemplateName}」產生` // 記錄來源模板\n        };\n        allBatchEditItems.push(batchEditItem);\n        currentSortIndex++;\n      }\n    });\n    // 設定批次編輯項目\n    this.batchEditItems = allBatchEditItems;\n    // 設定為批次編輯模式\n    this.isBatchEditMode = true;\n    // 計算總明細數量\n    const totalDetailCount = selectedTemplates.reduce((sum, template) => {\n      const details = templateDetails.get(template.CTemplateId) || [];\n      return sum + (details.length || 1);\n    }, 0);\n    // 顯示提示\n    const message = `已載入 ${selectedTemplates.length} 個模板，共 ${totalDetailCount} 個明細項目到批次編輯模式。\\n` + `請檢查並調整各項目的設定，包括：\\n` + `• 區域、工程項目名稱\\n` + `• 房屋類型、排序\\n` + `• 單價、單位\\n` + `• 預約需求、簡易客變設定\\n\\n` + `設定完成後點擊「確定批次更新」以儲存所有項目。`;\n    alert(message);\n    // 直接開啟批次編輯對話框\n    setTimeout(() => {\n      this.dialogService.open(this.batchEditDialog);\n    }, 100);\n  } // 批次編輯相關方法\n  // 切換單一項目選擇狀態\n  toggleItemSelection(item) {\n    const index = this.selectedItems.findIndex(selected => selected.CRequirementID === item.CRequirementID);\n    if (index > -1) {\n      this.selectedItems.splice(index, 1);\n    } else {\n      this.selectedItems.push(item);\n    }\n    this.updateSelectAllState();\n  }\n  // 切換全選狀態\n  toggleSelectAll(newValue) {\n    if (this.requirementList.length === 0) {\n      this.selectedItems = [];\n      this.isAllSelected = false;\n      return;\n    }\n    // 更新 isAllSelected 狀態\n    this.isAllSelected = newValue;\n    // 根據新值更新 selectedItems\n    if (this.isAllSelected) {\n      this.selectedItems = [...this.requirementList];\n    } else {\n      this.selectedItems = [];\n    }\n  }\n  // 更新全選狀態\n  updateSelectAllState() {\n    if (this.requirementList.length === 0) {\n      this.isAllSelected = false;\n    } else {\n      this.isAllSelected = this.selectedItems.length === this.requirementList.length;\n    }\n  }\n  // 檢查項目是否被選中\n  isItemSelected(item) {\n    return this.selectedItems.some(selected => selected.CRequirementID === item.CRequirementID);\n  }\n  // 開啟批次編輯對話框\n  openBatchEdit(dialog) {\n    if (this.selectedItems.length === 0) {\n      this.message.showErrorMSG('請先選擇要編輯的項目');\n      return;\n    }\n    this.isBatchEditMode = true;\n    // 初始化批次編輯項目資料\n    this.batchEditItems = this.selectedItems.map(item => ({\n      CRequirementID: item.CRequirementID,\n      CBuildCaseID: item.CBuildCaseID,\n      CLocation: item.CLocation,\n      CRequirement: item.CRequirement,\n      CHouseType: item.CHouseType ? [...item.CHouseType] : [],\n      CSort: item.CSort,\n      CStatus: item.CStatus,\n      CUnitPrice: item.CUnitPrice || 0,\n      CUnit: item.CUnit,\n      CSpaceId: item.CSpaceId || null,\n      CIsShow: item.CIsShow || false,\n      CIsSimple: item.CIsSimple || false,\n      CRemark: item.CRemark\n    }));\n    this.dialogService.open(dialog);\n  }\n  // 批次驗證方法\n  batchValidation() {\n    const errorMessages = [];\n    this.batchEditItems.forEach((item, index) => {\n      const itemNum = index + 1;\n      // 必填欄位檢核\n      if (!item.CBuildCaseID) {\n        errorMessages.push(`[項目 ${itemNum}] 建案名稱為必填欄位`);\n      }\n      if (!item.CRequirement || item.CRequirement.trim() === '') {\n        errorMessages.push(`[項目 ${itemNum}] 工程項目為必填欄位`);\n      }\n      if (!item.CHouseType || item.CHouseType.length === 0) {\n        errorMessages.push(`[項目 ${itemNum}] 類型為必填欄位`);\n      }\n      if (item.CSort === null || item.CSort === undefined || item.CSort < 0) {\n        errorMessages.push(`[項目 ${itemNum}] 排序為必填欄位且不能為負數`);\n      }\n      if (item.CStatus === null || item.CStatus === undefined) {\n        errorMessages.push(`[項目 ${itemNum}] 狀態為必填欄位`);\n      }\n      if (item.CUnitPrice === null || item.CUnitPrice === undefined || item.CUnitPrice < 0) {\n        errorMessages.push(`[項目 ${itemNum}] 單價為必填欄位且不能為負數`);\n      }\n      if (!item.CUnit || item.CUnit.trim() === '') {\n        errorMessages.push(`[項目 ${itemNum}] 單位為必填欄位`);\n      }\n      // 長度驗證\n      if (item.CLocation && item.CLocation.length > 20) {\n        errorMessages.push(`[項目 ${itemNum}] 區域不能超過20個字`);\n      }\n      if (item.CRequirement && item.CRequirement.length > 50) {\n        errorMessages.push(`[項目 ${itemNum}] 工程項目不能超過50個字`);\n      }\n      if (item.CRemark && item.CRemark.length > 100) {\n        errorMessages.push(`[項目 ${itemNum}] 備註說明不能超過100個字`);\n      }\n    });\n    return errorMessages;\n  }\n  // 批次保存\n  batchSave(ref) {\n    if (this.batchEditItems.length === 0) {\n      this.message.showErrorMSG('沒有要更新的項目');\n      return;\n    }\n    // 執行批次驗證\n    const validationErrors = this.batchValidation();\n    if (validationErrors.length > 0) {\n      this.message.showErrorMSGs(validationErrors);\n      return;\n    }\n    // 分離新增項目和更新項目\n    const newItems = this.batchEditItems.filter(item => !item.CRequirementID);\n    const updateItems = this.batchEditItems.filter(item => item.CRequirementID);\n    // 建立請求陣列\n    const requests = [];\n    // 新增項目的請求\n    newItems.forEach(item => {\n      const newItemData = {\n        CBuildCaseID: item.CBuildCaseID,\n        CLocation: item.CLocation,\n        CRequirement: item.CRequirement,\n        CHouseType: item.CHouseType,\n        CSort: item.CSort,\n        CStatus: item.CStatus,\n        CUnitPrice: item.CUnitPrice,\n        CUnit: item.CUnit,\n        CSpaceId: item.CSpaceId,\n        CIsShow: item.CIsShow,\n        CIsSimple: item.CIsSimple,\n        CRemark: item.CRemark\n      };\n      requests.push(this.requirementService.apiRequirementSaveDataPost$Json({\n        body: newItemData\n      }).toPromise());\n    });\n    // 更新項目的請求（如果有的話）\n    if (updateItems.length > 0) {\n      const updateData = updateItems.map(item => ({\n        CRequirementID: item.CRequirementID,\n        CBuildCaseID: item.CBuildCaseID,\n        CLocation: item.CLocation,\n        CRequirement: item.CRequirement,\n        CHouseType: item.CHouseType,\n        CSort: item.CSort,\n        CStatus: item.CStatus,\n        CUnitPrice: item.CUnitPrice,\n        CUnit: item.CUnit,\n        CSpaceId: item.CSpaceId,\n        CIsShow: item.CIsShow,\n        CIsSimple: item.CIsSimple,\n        CRemark: item.CRemark\n      }));\n      requests.push(this.requirementService.apiRequirementBatchSaveDataPost$Json({\n        body: updateData\n      }).toPromise());\n    }\n    // 執行所有請求\n    Promise.all(requests).then(responses => {\n      const successCount = responses.filter(res => res?.StatusCode === 0).length;\n      const totalItems = this.batchEditItems.length;\n      if (successCount === responses.length) {\n        this.message.showSucessMSG(`成功處理 ${totalItems} 個項目 (新增: ${newItems.length}, 更新: ${updateItems.length})`);\n      } else {\n        this.message.showSucessMSG(`成功處理 ${successCount} 個項目，${responses.length - successCount} 個失敗`);\n      }\n      // 清理狀態並重新載入資料\n      this.selectedItems = [];\n      this.batchEditItems = [];\n      this.isBatchEditMode = false;\n      this.updateSelectAllState();\n      this.getList();\n      ref.close();\n    }).catch(error => {\n      console.error('批次保存失敗:', error);\n      this.message.showErrorMSG('批次保存時發生錯誤');\n    });\n  }\n  // 重置批次編輯中的單一項目到原始狀態\n  resetBatchEditItem(index) {\n    const originalItem = this.selectedItems[index];\n    if (originalItem) {\n      this.batchEditItems[index] = {\n        CRequirementID: originalItem.CRequirementID,\n        CBuildCaseID: originalItem.CBuildCaseID,\n        CLocation: originalItem.CLocation,\n        CRequirement: originalItem.CRequirement,\n        CHouseType: originalItem.CHouseType ? [...originalItem.CHouseType] : [],\n        CSort: originalItem.CSort,\n        CStatus: originalItem.CStatus,\n        CUnitPrice: originalItem.CUnitPrice || 0,\n        CUnit: originalItem.CUnit,\n        CSpaceId: originalItem.CSpaceId || null,\n        CIsShow: originalItem.CIsShow || false,\n        CIsSimple: originalItem.CIsSimple || false,\n        CRemark: originalItem.CRemark\n      };\n    }\n  }\n  // 取消批次編輯\n  cancelBatchEdit(ref) {\n    this.isBatchEditMode = false;\n    this.batchEditItems = [];\n    ref.close();\n  }\n  // 備用：如果需要手動建立需求項目的方法\n  batchCreateRequirements(requirements) {\n    const batchRequests = requirements.map(requirement => this.requirementService.apiRequirementSaveDataPost$Json({\n      body: requirement\n    }));\n    Promise.all(batchRequests.map(req => req.toPromise())).then(responses => {\n      const successCount = responses.filter(res => res?.StatusCode === 0).length;\n      this.message.showSucessMSG(`成功建立 ${successCount} 個需求項目`);\n      this.getList();\n    }).catch(error => {\n      console.error('批次建立需求失敗:', error);\n      this.message.showErrorMSG('批次建立需求時發生錯誤');\n    });\n  }\n  static {\n    this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i6.TemplateService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef), i0.ɵɵdirectiveInject(i9.SpaceTemplateSelectorService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementManagementComponent,\n      selectors: [[\"app-requirement-management\"]],\n      viewQuery: function RequirementManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchEditDialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 104,\n      vars: 30,\n      consts: [[\"dialog\", \"\"], [\"batchEditDialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\", \"pb-0\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u5340\\u57DF\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [3, \"value\"], [\"for\", \"isShow\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5168\\u90E8\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"isSimple\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [3, \"templateApplied\", \"error\", \"buildCaseId\", \"text\", \"icon\", \"buttonClass\", \"disabled\"], [\"class\", \"btn btn-primary mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"col-12\", \"mt-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\", \"d-flex\", \"flex-column\", \"align-items-center\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"text-white\", \"mt-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"btn\", \"btn-primary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-1\", \"text-center\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CLocation\", \"name\", \"CLocation\", \"placeholder\", \"\\u5340\\u57DF\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CSort\", \"name\", \"CSort\", \"placeholder\", \"\\u6392\\u5E8F\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", \"step\", \"0.01\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CUnit\", \"name\", \"CUnit\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsShow\", \"name\", \"CIsShow\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsSimple\", \"name\", \"CIsSimple\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"langg\", \"\", 3, \"value\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"900px\"], [2, \"padding\", \"1rem 2rem\", \"max-height\", \"70vh\", \"overflow-y\", \"auto\"], [1, \"alert\", \"alert-info\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [1, \"alert\", \"alert-warning\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-2\"], [1, \"mb-0\", \"mt-2\"], [\"class\", \"mb-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"mb-4\"], [1, \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [\"type\", \"button\", \"title\", \"\\u91CD\\u7F6E\\u70BA\\u539F\\u59CB\\u503C\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"py-3\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5340\\u57DF\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"id\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6392\\u5E8F\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"id\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u55AE\\u50F9\", \"step\", \"0.01\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"nbInput\", \"\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"2\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"]],\n      template: function RequirementManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"label\", 7);\n          i0.ɵɵtext(8, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"nb-select\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CBuildCaseID, $event) || (ctx.getListRequirementRequest.CBuildCaseID = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildCaseChange($event));\n          });\n          i0.ɵɵtemplate(10, RequirementManagementComponent_nb_option_10_Template, 2, 2, \"nb-option\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"label\", 10);\n          i0.ɵɵtext(13, \"\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CLocation, $event) || (ctx.getListRequirementRequest.CLocation = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 6)(16, \"label\", 12);\n          i0.ɵɵtext(17, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 5)(20, \"div\", 6)(21, \"label\", 14);\n          i0.ɵɵtext(22, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(24, RequirementManagementComponent_nb_option_24_Template, 2, 2, \"nb-option\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 6)(26, \"label\", 16);\n          i0.ɵɵtext(27, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"nb-select\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(29, \"nb-option\", 17);\n          i0.ɵɵtext(30, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"nb-option\", 17);\n          i0.ɵɵtext(32, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-option\", 17);\n          i0.ɵɵtext(34, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 6)(36, \"label\", 18);\n          i0.ɵɵtext(37, \"\\u9810\\u7D04\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"nb-select\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsShow, $event) || (ctx.getListRequirementRequest.CIsShow = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(39, \"nb-option\", 17);\n          i0.ɵɵtext(40, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"nb-option\", 17);\n          i0.ɵɵtext(42, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"nb-option\", 17);\n          i0.ɵɵtext(44, \"\\u5426\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(45, \"div\", 5)(46, \"div\", 6)(47, \"label\", 20);\n          i0.ɵɵtext(48, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"nb-select\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_49_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsSimple, $event) || (ctx.getListRequirementRequest.CIsSimple = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(50, \"nb-option\", 17);\n          i0.ɵɵtext(51, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"nb-option\", 17);\n          i0.ɵɵtext(53, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"nb-option\", 17);\n          i0.ɵɵtext(55, \"\\u5426\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(56, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 5);\n          i0.ɵɵelement(58, \"div\", 21);\n          i0.ɵɵelementStart(59, \"div\", 22)(60, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_60_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetSearch());\n          });\n          i0.ɵɵelement(61, \"i\", 24);\n          i0.ɵɵtext(62, \"\\u91CD\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_63_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(64, \"i\", 26);\n          i0.ɵɵtext(65, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"app-space-template-selector-button\", 27);\n          i0.ɵɵlistener(\"templateApplied\", function RequirementManagementComponent_Template_app_space_template_selector_button_templateApplied_66_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSpaceTemplateApplied($event));\n          })(\"error\", function RequirementManagementComponent_Template_app_space_template_selector_button_error_66_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTemplateError($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(67, RequirementManagementComponent_button_67_Template, 3, 1, \"button\", 28)(68, RequirementManagementComponent_button_68_Template, 3, 0, \"button\", 29);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(69, \"nb-card-body\", 3)(70, \"div\", 30)(71, \"div\", 31)(72, \"table\", 32)(73, \"thead\")(74, \"tr\", 33)(75, \"th\", 34)(76, \"nb-checkbox\", 35);\n          i0.ɵɵlistener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_checkbox_ngModelChange_76_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleSelectAll($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"small\", 36);\n          i0.ɵɵtext(78, \"\\u5168\\u9078\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"th\", 37);\n          i0.ɵɵtext(80, \"\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"th\", 37);\n          i0.ɵɵtext(82, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"th\", 38);\n          i0.ɵɵtext(84, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"th\", 38);\n          i0.ɵɵtext(86, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"th\", 38);\n          i0.ɵɵtext(88, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"th\", 38);\n          i0.ɵɵtext(90, \"\\u9810\\u7D04\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"th\", 38);\n          i0.ɵɵtext(92, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"th\", 38);\n          i0.ɵɵtext(94, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"th\", 38);\n          i0.ɵɵtext(96, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(97, \"tbody\");\n          i0.ɵɵtemplate(98, RequirementManagementComponent_tr_98_Template, 24, 16, \"tr\", 39);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(99, \"ngx-pagination\", 40);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_99_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_99_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(100, RequirementManagementComponent_ng_template_100_Template, 39, 44, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(102, RequirementManagementComponent_ng_template_102_Template, 29, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CBuildCaseID);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CLocation);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsShow);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsSimple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"buildCaseId\", (ctx.getListRequirementRequest.CBuildCaseID == null ? null : ctx.getListRequirementRequest.CBuildCaseID.toString()) || \"\")(\"text\", \"\\u6A21\\u677F\\u65B0\\u589E\")(\"icon\", \"fas fa-layer-group\")(\"buttonClass\", \"btn btn-warning mr-2\")(\"disabled\", !ctx.getListRequirementRequest.CBuildCaseID);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItems.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.isAllSelected);\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i10.DefaultValueAccessor, i10.NumberValueAccessor, i10.NgControlStatus, i10.MaxLengthValidator, i10.MinValidator, i10.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent, NumberWithCommasPipe, SpaceTemplateSelectorButtonComponent],\n      styles: [\".table-active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd !important;\\n  border-left: 3px solid #2196f3;\\n}\\n\\n.page-description-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1.25rem;\\n  border-radius: 10px;\\n  border: 1px solid #dee2e6;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n  margin-bottom: 1rem;\\n}\\n.page-description-card[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #007bff;\\n}\\n.page-description-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  line-height: 1.5;\\n  margin-bottom: 0.75rem;\\n}\\n.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  color: #6c757d;\\n  border: 1px solid #dee2e6;\\n  font-size: 0.8rem;\\n  padding: 0.4rem 0.8rem;\\n  font-weight: 500;\\n}\\n.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.template-creation-controls[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n  border: 1px solid #e9ecef;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .template-action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .template-status-info[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  border: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:disabled {\\n  background: #6c757d;\\n  cursor: not-allowed;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  font-size: 0.75rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #6c757d;\\n  color: #6c757d;\\n  transition: all 0.2s ease;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-style: italic;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n  margin: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n  margin: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-active[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_selectRow 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_selectRow {\\n  0% {\\n    background-color: transparent;\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.01);\\n  }\\n  100% {\\n    background-color: #e3f2fd;\\n    transform: scale(1);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 0.25rem;\\n  }\\n  .template-creation-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.375rem 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "SpaceTemplateSelectorButtonComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "type_r3", "value", "label", "ɵɵlistener", "RequirementManagementComponent_button_67_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r4", "ɵɵnextContext", "batchEditDialog_r6", "ɵɵreference", "ɵɵresetView", "openBatchEdit", "ɵɵelement", "selectedItems", "length", "RequirementManagementComponent_button_68_Template_button_click_0_listener", "_r7", "dialog_r8", "add", "RequirementManagementComponent_tr_98_button_22_Template_button_click_0_listener", "_r11", "data_r10", "$implicit", "onEdit", "RequirementManagementComponent_tr_98_button_23_Template_button_click_0_listener", "_r12", "onDelete", "RequirementManagementComponent_tr_98_Template_nb_checkbox_ngModelChange_2_listener", "_r9", "toggleItemSelection", "ɵɵtemplate", "RequirementManagementComponent_tr_98_button_22_Template", "RequirementManagementComponent_tr_98_button_23_Template", "isItemSelected", "ɵɵtextInterpolate", "CLocation", "CRequirement", "getHouseType", "CHouseType", "ɵɵpureFunction0", "_c1", "CSort", "ɵɵpipeBind1", "CStatus", "getCIsShowText", "getCIsSimpleText", "CUnitPrice", "isUpdate", "isDelete", "type_r14", "status_r15", "RequirementManagementComponent_ng_template_100_span_2_Template", "RequirementManagementComponent_ng_template_100_span_3_Template", "ɵɵtwoWayListener", "RequirementManagementComponent_ng_template_100_Template_input_ngModelChange_9_listener", "$event", "_r13", "ɵɵtwoWayBindingSet", "saveRequirement", "RequirementManagementComponent_ng_template_100_Template_input_ngModelChange_11_listener", "RequirementManagementComponent_ng_template_100_Template_input_ngModelChange_13_listener", "RequirementManagementComponent_ng_template_100_Template_nb_select_selectedChange_15_listener", "RequirementManagementComponent_ng_template_100_nb_option_16_Template", "RequirementManagementComponent_ng_template_100_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_100_nb_option_19_Template", "RequirementManagementComponent_ng_template_100_Template_input_ngModelChange_21_listener", "RequirementManagementComponent_ng_template_100_Template_input_ngModelChange_23_listener", "CUnit", "RequirementManagementComponent_ng_template_100_Template_nb_checkbox_ngModelChange_25_listener", "CIsShow", "RequirementManagementComponent_ng_template_100_Template_nb_checkbox_ngModelChange_28_listener", "CIsSimple", "RequirementManagementComponent_ng_template_100_Template_textarea_ngModelChange_31_listener", "CRemark", "RequirementManagementComponent_ng_template_100_Template_button_click_35_listener", "ref_r16", "dialogRef", "save", "RequirementManagementComponent_ng_template_100_Template_button_click_37_listener", "close", "isNew", "ɵɵtwoWayProperty", "houseType", "statusOptions", "type_r21", "status_r22", "RequirementManagementComponent_ng_template_102_div_21_Template_button_click_6_listener", "i_r19", "_r18", "index", "resetBatchEditItem", "RequirementManagementComponent_ng_template_102_div_21_Template_input_ngModelChange_13_listener", "item_r20", "RequirementManagementComponent_ng_template_102_div_21_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_ng_template_102_div_21_Template_nb_select_selectedChange_19_listener", "RequirementManagementComponent_ng_template_102_div_21_nb_option_20_Template", "RequirementManagementComponent_ng_template_102_div_21_Template_input_ngModelChange_23_listener", "RequirementManagementComponent_ng_template_102_div_21_Template_nb_select_selectedChange_26_listener", "RequirementManagementComponent_ng_template_102_div_21_nb_option_27_Template", "RequirementManagementComponent_ng_template_102_div_21_Template_input_ngModelChange_30_listener", "RequirementManagementComponent_ng_template_102_div_21_Template_input_ngModelChange_33_listener", "RequirementManagementComponent_ng_template_102_div_21_Template_nb_checkbox_ngModelChange_36_listener", "RequirementManagementComponent_ng_template_102_div_21_Template_nb_checkbox_ngModelChange_40_listener", "RequirementManagementComponent_ng_template_102_div_21_Template_textarea_ngModelChange_44_listener", "ɵɵtextInterpolate2", "RequirementManagementComponent_ng_template_102_div_21_Template", "RequirementManagementComponent_ng_template_102_Template_button_click_25_listener", "ref_r23", "_r17", "batchSave", "RequirementManagementComponent_ng_template_102_Template_button_click_27_listener", "cancelBatchEdit", "batchEditItems", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "templateService", "pettern", "router", "destroyref", "spaceTemplateSelectorService", "getListRequirementRequest", "getRequirementRequest", "buildCaseList", "requirementList", "getEnumOptions", "currentBuildCase", "isAllSelected", "isBatchEditMode", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "map", "type", "onBuildCaseChange", "newBuildCaseId", "confirmMessage", "confirm", "setTimeout", "CBuildCaseID", "getList", "resetSearch", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "undefined", "errorMessages", "dialog", "open", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "window", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "TotalItems", "originalSelectedCount", "filter", "selectedItem", "some", "listItem", "updateSelectAllState", "apiRequirementGetDataPost$Json", "CSpaceId", "onHouseTypeChange", "checked", "includes", "v", "onSpaceTemplateApplied", "config", "convertTemplatesToBatchEdit", "templateDetails", "onTemplateError", "errorMessage", "selectedTemplates", "maxSort", "Math", "max", "item", "currentSortIndex", "allBatchEditItems", "template", "details", "get", "CTemplateId", "detail", "detailIndex", "batchEditItem", "<PERSON>art", "CTemplateName", "CReleateId", "cRelateID", "totalDetailCount", "reduce", "sum", "alert", "batchEditDialog", "findIndex", "selected", "splice", "toggleSelectAll", "newValue", "batchValidation", "itemNum", "trim", "validationErrors", "newItems", "updateItems", "requests", "newItemData", "to<PERSON>romise", "updateData", "apiRequirementBatchSaveDataPost$Json", "Promise", "all", "then", "responses", "successCount", "totalItems", "catch", "originalItem", "batchCreateRequirements", "requirements", "batchRequests", "requirement", "req", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "BuildCaseService", "RequirementService", "TemplateService", "i7", "PetternHelper", "i8", "Router", "DestroyRef", "i9", "SpaceTemplateSelectorService", "selectors", "viewQuery", "RequirementManagementComponent_Query", "rf", "ctx", "RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener", "_r1", "RequirementManagementComponent_nb_option_10_Template", "RequirementManagementComponent_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_Template_input_ngModelChange_18_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_23_listener", "RequirementManagementComponent_nb_option_24_Template", "RequirementManagementComponent_Template_nb_select_ngModelChange_28_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_38_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_49_listener", "RequirementManagementComponent_Template_button_click_60_listener", "RequirementManagementComponent_Template_button_click_63_listener", "RequirementManagementComponent_Template_app_space_template_selector_button_templateApplied_66_listener", "RequirementManagementComponent_Template_app_space_template_selector_button_error_66_listener", "RequirementManagementComponent_button_67_Template", "RequirementManagementComponent_button_68_Template", "RequirementManagementComponent_Template_nb_checkbox_ngModelChange_76_listener", "RequirementManagementComponent_tr_98_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_99_listener", "RequirementManagementComponent_ng_template_100_Template", "ɵɵtemplateRefExtractor", "RequirementManagementComponent_ng_template_102_Template", "toString", "isCreate", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i10", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "MinValidator", "NgModel", "NbSelectComponent", "NbOptionComponent", "NbCheckboxComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService, TemplateService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { SpaceTemplateSelectorService } from 'src/app/shared/components/space-template-selector/space-template-selector.service';\r\nimport { SpaceTemplateConfig } from 'src/app/shared/components/space-template-selector/space-template-selector.component';\r\nimport { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    SpaceTemplateSelectorButtonComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('batchEditDialog', { static: false }) batchEditDialog!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private templateService: TemplateService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef,\r\n    private spaceTemplateSelectorService: SpaceTemplateSelectorService\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n\r\n  // 批次編輯相關屬性\r\n  selectedItems: GetRequirement[] = [];\r\n  isAllSelected = false;\r\n  isBatchEditMode = false;\r\n  // 批次編輯時的項目資料副本\r\n  batchEditItems: (SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean })[] = [];\r\n\r\n  override ngOnInit(): void { }\r\n\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CIsSimple = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CLocation = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 建案切換事件處理\r\n  onBuildCaseChange(newBuildCaseId: any) {\r\n    // 如果在批次編輯模式下切換建案，給予警告\r\n    if (this.isBatchEditMode || this.selectedItems.length > 0) {\r\n      const confirmMessage = this.isBatchEditMode\r\n        ? '切換建案將會關閉批次編輯對話框並清除所有選擇的項目，是否繼續？'\r\n        : `切換建案將會清除已選擇的 ${this.selectedItems.length} 個項目，是否繼續？`;\r\n\r\n      if (!confirm(confirmMessage)) {\r\n        // 使用者取消，恢復原來的建案選擇\r\n        setTimeout(() => {\r\n          this.getListRequirementRequest.CBuildCaseID = this.currentBuildCase;\r\n        }, 0);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // 重置批次選擇的項目\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n    this.batchEditItems = [];\r\n    this.isBatchEditMode = false;\r\n\r\n    // 更新當前建案並重新載入資料\r\n    this.currentBuildCase = newBuildCaseId;\r\n    this.getList();\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 清除選擇狀態和批次編輯相關狀態\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n    this.batchEditItems = [];\r\n    this.isBatchEditMode = false;\r\n\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n\r\n    // 建案頁面需要驗證建案名稱\r\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[排序]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 數值範圍驗證\r\n    if (this.saveRequirement.CSort !== null && this.saveRequirement.CSort !== undefined && this.saveRequirement.CSort < 0) {\r\n      this.valid.errorMessages.push('[排序] 不能為負數');\r\n    }\r\n\r\n    if (this.saveRequirement.CUnitPrice !== null && this.saveRequirement.CUnitPrice !== undefined && this.saveRequirement.CUnitPrice < 0) {\r\n      this.valid.errorMessages.push('[單價] 不能為負數');\r\n    }\r\n\r\n    // 長度驗證\r\n    if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    if (this.saveRequirement.CRequirement && this.saveRequirement.CRequirement.length > 50) {\r\n      this.valid.errorMessages.push('[工程項目] 不能超過50個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n    if (this.currentBuildCase != 0) {\r\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n    } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n    }\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 如果有建案時才查詢\r\n        if (this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n\r\n    // 建案頁面的邏輯\r\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.requirementList = res.Entries;\r\n            this.totalRecords = res.TotalItems!;\r\n\r\n            // 清理已選擇的項目，移除不存在於新列表中的項目\r\n            const originalSelectedCount = this.selectedItems.length;\r\n            this.selectedItems = this.selectedItems.filter(selectedItem =>\r\n              this.requirementList.some(listItem => listItem.CRequirementID === selectedItem.CRequirementID)\r\n            );\r\n\r\n            // 如果選擇的項目數量有變化，清理批次編輯狀態\r\n            if (originalSelectedCount !== this.selectedItems.length || this.selectedItems.length === 0) {\r\n              this.batchEditItems = [];\r\n              this.isBatchEditMode = false;\r\n            }\r\n\r\n            // 更新選擇狀態\r\n            this.updateSelectAllState();\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CLocation = res.Entries.CLocation;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            this.saveRequirement.CSpaceId = res.Entries.CSpaceId || null;\r\n            this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\r\n            this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  getCIsSimpleText(data: any): string {\r\n    return data.CIsSimple ? '是' : '否';\r\n  }\r\n\r\n  // 空間模板相關方法\r\n  onSpaceTemplateApplied(config: SpaceTemplateConfig) {\r\n    console.log('套用空間模板配置:', config);\r\n\r\n    // 直接將模板項目轉換為批次編輯項目，使用從模板選擇器傳來的明細數據\r\n    this.convertTemplatesToBatchEdit(config.selectedItems, config.templateDetails);\r\n  }\r\n\r\n  onTemplateError(errorMessage: string) {\r\n    this.message.showErrorMSG(errorMessage);\r\n  }\r\n\r\n  // 將模板項目轉換為批次編輯項目\r\n  private convertTemplatesToBatchEdit(selectedTemplates: any[], templateDetails: Map<number, any[]>) {\r\n    if (!this.getListRequirementRequest.CBuildCaseID) {\r\n      this.message.showErrorMSG('建案 ID 不存在');\r\n      return;\r\n    }\r\n\r\n    // 清除當前選擇的項目\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n\r\n    // 取得預設排序值 - 從列表最大排序值開始\r\n    const maxSort = this.requirementList.length > 0\r\n      ? Math.max(...this.requirementList.map(item => item.CSort || 0))\r\n      : 0;\r\n\r\n    let currentSortIndex = 0;\r\n    const allBatchEditItems: any[] = [];\r\n\r\n    // 處理每個模板的明細項目\r\n    selectedTemplates.forEach(template => {\r\n      const details = templateDetails.get(template.CTemplateId) || [];\r\n\r\n      if (details && details.length > 0) {\r\n        // 將每個明細項目轉換為批次編輯項目\r\n        details.forEach((detail, detailIndex) => {\r\n          const batchEditItem = {\r\n            CRequirementID: undefined, // 新項目沒有 ID\r\n            CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\r\n            CLocation: detail.CLocation || template.CLocation || '', // 優先使用明細的位置，其次使用模板位置\r\n            CRequirement: detail.CPart || `${template.CTemplateName} - 項目 ${detailIndex + 1}`, // 使用明細名稱作為工程項目\r\n            CHouseType: template.CHouseType || [], // 從模板獲取房屋類型，預設為空陣列\r\n            CSort: maxSort + currentSortIndex + 1, // 從最大排序值往上遞增\r\n            CStatus: 1, // 預設啟用\r\n            CUnitPrice: (detail as any).CUnitPrice || (template as any).CUnitPrice || 0, // 優先使用明細單價，其次使用模板單價\r\n            CUnit: (detail as any).CUnit || (template as any).CUnit || '式', // 優先使用明細單位，其次使用模板單位\r\n            CSpaceId: detail.CReleateId || (template as any).cRelateID || null, // 從明細獲取關聯空間 ID\r\n            CIsShow: template.CIsShow !== undefined ? template.CIsShow : false, // 預設不顯示在預約需求\r\n            CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false, // 預設不是簡易客變\r\n            CRemark: (detail as any).CRemark || `從模板「${template.CTemplateName}」的明細項目「${detail.CPart}」產生` // 記錄來源模板和明細\r\n          };\r\n\r\n          allBatchEditItems.push(batchEditItem);\r\n          currentSortIndex++;\r\n        });\r\n      } else {\r\n        // 如果模板沒有明細，則將模板本身作為一個項目\r\n        const batchEditItem = {\r\n          CRequirementID: undefined, // 新項目沒有 ID\r\n          CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\r\n          CLocation: template.CLocation || '', // 從模板獲取區域資訊\r\n          CRequirement: template.CTemplateName || `模板項目 ${currentSortIndex + 1}`, // 使用模板名稱作為工程項目\r\n          CHouseType: template.CHouseType || [], // 從模板獲取房屋類型，預設為空陣列\r\n          CSort: maxSort + currentSortIndex + 1, // 從最大排序值往上遞增\r\n          CStatus: 1, // 預設啟用\r\n          CUnitPrice: (template as any).CUnitPrice || 0, // 從模板獲取單價，預設為 0\r\n          CUnit: (template as any).CUnit || '式', // 從模板獲取單位，預設為 '式'\r\n          CSpaceId: (template as any).cRelateID || null, // 從模板獲取關聯空間 ID\r\n          CIsShow: template.CIsShow !== undefined ? template.CIsShow : false, // 預設不顯示在預約需求\r\n          CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false, // 預設不是簡易客變\r\n          CRemark: (template as any).CRemark || `從模板「${template.CTemplateName}」產生` // 記錄來源模板\r\n        };\r\n\r\n        allBatchEditItems.push(batchEditItem);\r\n        currentSortIndex++;\r\n      }\r\n    });\r\n\r\n    // 設定批次編輯項目\r\n    this.batchEditItems = allBatchEditItems;\r\n\r\n    // 設定為批次編輯模式\r\n    this.isBatchEditMode = true;\r\n\r\n    // 計算總明細數量\r\n    const totalDetailCount = selectedTemplates.reduce((sum, template) => {\r\n      const details = templateDetails.get(template.CTemplateId) || [];\r\n      return sum + (details.length || 1);\r\n    }, 0);\r\n\r\n    // 顯示提示\r\n    const message = `已載入 ${selectedTemplates.length} 個模板，共 ${totalDetailCount} 個明細項目到批次編輯模式。\\n` +\r\n      `請檢查並調整各項目的設定，包括：\\n` +\r\n      `• 區域、工程項目名稱\\n` +\r\n      `• 房屋類型、排序\\n` +\r\n      `• 單價、單位\\n` +\r\n      `• 預約需求、簡易客變設定\\n\\n` +\r\n      `設定完成後點擊「確定批次更新」以儲存所有項目。`;\r\n\r\n    alert(message);\r\n\r\n    // 直接開啟批次編輯對話框\r\n    setTimeout(() => {\r\n      this.dialogService.open(this.batchEditDialog);\r\n    }, 100);\r\n  }  // 批次編輯相關方法\r\n\r\n  // 切換單一項目選擇狀態\r\n  toggleItemSelection(item: GetRequirement) {\r\n    const index = this.selectedItems.findIndex(selected => selected.CRequirementID === item.CRequirementID);\r\n    if (index > -1) {\r\n      this.selectedItems.splice(index, 1);\r\n    } else {\r\n      this.selectedItems.push(item);\r\n    }\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 切換全選狀態\r\n  toggleSelectAll(newValue: boolean) {\r\n    if (this.requirementList.length === 0) {\r\n      this.selectedItems = [];\r\n      this.isAllSelected = false;\r\n      return;\r\n    }\r\n\r\n    // 更新 isAllSelected 狀態\r\n    this.isAllSelected = newValue;\r\n\r\n    // 根據新值更新 selectedItems\r\n    if (this.isAllSelected) {\r\n      this.selectedItems = [...this.requirementList];\r\n    } else {\r\n      this.selectedItems = [];\r\n    }\r\n  }\r\n\r\n  // 更新全選狀態\r\n  updateSelectAllState() {\r\n    if (this.requirementList.length === 0) {\r\n      this.isAllSelected = false;\r\n    } else {\r\n      this.isAllSelected = this.selectedItems.length === this.requirementList.length;\r\n    }\r\n  }\r\n\r\n  // 檢查項目是否被選中\r\n  isItemSelected(item: GetRequirement): boolean {\r\n    return this.selectedItems.some(selected => selected.CRequirementID === item.CRequirementID);\r\n  }\r\n\r\n  // 開啟批次編輯對話框\r\n  openBatchEdit(dialog: TemplateRef<any>) {\r\n    if (this.selectedItems.length === 0) {\r\n      this.message.showErrorMSG('請先選擇要編輯的項目');\r\n      return;\r\n    }\r\n\r\n    this.isBatchEditMode = true;\r\n\r\n    // 初始化批次編輯項目資料\r\n    this.batchEditItems = this.selectedItems.map(item => ({\r\n      CRequirementID: item.CRequirementID,\r\n      CBuildCaseID: item.CBuildCaseID,\r\n      CLocation: item.CLocation,\r\n      CRequirement: item.CRequirement,\r\n      CHouseType: item.CHouseType ? [...item.CHouseType] : [],\r\n      CSort: item.CSort,\r\n      CStatus: item.CStatus,\r\n      CUnitPrice: item.CUnitPrice || 0,\r\n      CUnit: item.CUnit,\r\n      CSpaceId: item.CSpaceId || null,\r\n      CIsShow: item.CIsShow || false,\r\n      CIsSimple: item.CIsSimple || false,\r\n      CRemark: item.CRemark\r\n    }));\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 批次驗證方法\r\n  batchValidation(): string[] {\r\n    const errorMessages: string[] = [];\r\n\r\n    this.batchEditItems.forEach((item, index) => {\r\n      const itemNum = index + 1;\r\n\r\n      // 必填欄位檢核\r\n      if (!item.CBuildCaseID) {\r\n        errorMessages.push(`[項目 ${itemNum}] 建案名稱為必填欄位`);\r\n      }\r\n\r\n      if (!item.CRequirement || item.CRequirement.trim() === '') {\r\n        errorMessages.push(`[項目 ${itemNum}] 工程項目為必填欄位`);\r\n      }\r\n\r\n      if (!item.CHouseType || item.CHouseType.length === 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 類型為必填欄位`);\r\n      }\r\n\r\n      if (item.CSort === null || item.CSort === undefined || item.CSort < 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 排序為必填欄位且不能為負數`);\r\n      }\r\n\r\n      if (item.CStatus === null || item.CStatus === undefined) {\r\n        errorMessages.push(`[項目 ${itemNum}] 狀態為必填欄位`);\r\n      }\r\n\r\n      if (item.CUnitPrice === null || item.CUnitPrice === undefined || item.CUnitPrice < 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 單價為必填欄位且不能為負數`);\r\n      }\r\n\r\n      if (!item.CUnit || item.CUnit.trim() === '') {\r\n        errorMessages.push(`[項目 ${itemNum}] 單位為必填欄位`);\r\n      }\r\n\r\n      // 長度驗證\r\n      if (item.CLocation && item.CLocation.length > 20) {\r\n        errorMessages.push(`[項目 ${itemNum}] 區域不能超過20個字`);\r\n      }\r\n\r\n      if (item.CRequirement && item.CRequirement.length > 50) {\r\n        errorMessages.push(`[項目 ${itemNum}] 工程項目不能超過50個字`);\r\n      }\r\n\r\n      if (item.CRemark && item.CRemark.length > 100) {\r\n        errorMessages.push(`[項目 ${itemNum}] 備註說明不能超過100個字`);\r\n      }\r\n    });\r\n\r\n    return errorMessages;\r\n  }\r\n\r\n  // 批次保存\r\n  batchSave(ref: any) {\r\n    if (this.batchEditItems.length === 0) {\r\n      this.message.showErrorMSG('沒有要更新的項目');\r\n      return;\r\n    }\r\n\r\n    // 執行批次驗證\r\n    const validationErrors = this.batchValidation();\r\n    if (validationErrors.length > 0) {\r\n      this.message.showErrorMSGs(validationErrors);\r\n      return;\r\n    }\r\n\r\n    // 分離新增項目和更新項目\r\n    const newItems = this.batchEditItems.filter(item => !item.CRequirementID);\r\n    const updateItems = this.batchEditItems.filter(item => item.CRequirementID);\r\n\r\n    // 建立請求陣列\r\n    const requests: Promise<any>[] = [];\r\n\r\n    // 新增項目的請求\r\n    newItems.forEach(item => {\r\n      const newItemData: SaveDataRequirement = {\r\n        CBuildCaseID: item.CBuildCaseID,\r\n        CLocation: item.CLocation,\r\n        CRequirement: item.CRequirement,\r\n        CHouseType: item.CHouseType,\r\n        CSort: item.CSort,\r\n        CStatus: item.CStatus,\r\n        CUnitPrice: item.CUnitPrice,\r\n        CUnit: item.CUnit,\r\n        CSpaceId: item.CSpaceId,\r\n        CIsShow: item.CIsShow,\r\n        CIsSimple: item.CIsSimple,\r\n        CRemark: item.CRemark\r\n      };\r\n\r\n      requests.push(\r\n        this.requirementService.apiRequirementSaveDataPost$Json({\r\n          body: newItemData\r\n        }).toPromise()\r\n      );\r\n    });\r\n\r\n    // 更新項目的請求（如果有的話）\r\n    if (updateItems.length > 0) {\r\n      const updateData: SaveDataRequirement[] = updateItems.map(item => ({\r\n        CRequirementID: item.CRequirementID,\r\n        CBuildCaseID: item.CBuildCaseID,\r\n        CLocation: item.CLocation,\r\n        CRequirement: item.CRequirement,\r\n        CHouseType: item.CHouseType,\r\n        CSort: item.CSort,\r\n        CStatus: item.CStatus,\r\n        CUnitPrice: item.CUnitPrice,\r\n        CUnit: item.CUnit,\r\n        CSpaceId: item.CSpaceId,\r\n        CIsShow: item.CIsShow,\r\n        CIsSimple: item.CIsSimple,\r\n        CRemark: item.CRemark\r\n      }));\r\n\r\n      requests.push(\r\n        this.requirementService.apiRequirementBatchSaveDataPost$Json({\r\n          body: updateData\r\n        }).toPromise()\r\n      );\r\n    }\r\n\r\n    // 執行所有請求\r\n    Promise.all(requests)\r\n      .then(responses => {\r\n        const successCount = responses.filter(res => res?.StatusCode === 0).length;\r\n        const totalItems = this.batchEditItems.length;\r\n\r\n        if (successCount === responses.length) {\r\n          this.message.showSucessMSG(`成功處理 ${totalItems} 個項目 (新增: ${newItems.length}, 更新: ${updateItems.length})`);\r\n        } else {\r\n          this.message.showSucessMSG(`成功處理 ${successCount} 個項目，${responses.length - successCount} 個失敗`);\r\n        }\r\n\r\n        // 清理狀態並重新載入資料\r\n        this.selectedItems = [];\r\n        this.batchEditItems = [];\r\n        this.isBatchEditMode = false;\r\n        this.updateSelectAllState();\r\n        this.getList();\r\n        ref.close();\r\n      })\r\n      .catch(error => {\r\n        console.error('批次保存失敗:', error);\r\n        this.message.showErrorMSG('批次保存時發生錯誤');\r\n      });\r\n  }\r\n\r\n  // 重置批次編輯中的單一項目到原始狀態\r\n  resetBatchEditItem(index: number) {\r\n    const originalItem = this.selectedItems[index];\r\n    if (originalItem) {\r\n      this.batchEditItems[index] = {\r\n        CRequirementID: originalItem.CRequirementID,\r\n        CBuildCaseID: originalItem.CBuildCaseID,\r\n        CLocation: originalItem.CLocation,\r\n        CRequirement: originalItem.CRequirement,\r\n        CHouseType: originalItem.CHouseType ? [...originalItem.CHouseType] : [],\r\n        CSort: originalItem.CSort,\r\n        CStatus: originalItem.CStatus,\r\n        CUnitPrice: originalItem.CUnitPrice || 0,\r\n        CUnit: originalItem.CUnit,\r\n        CSpaceId: originalItem.CSpaceId || null,\r\n        CIsShow: originalItem.CIsShow || false,\r\n        CIsSimple: originalItem.CIsSimple || false,\r\n        CRemark: originalItem.CRemark\r\n      };\r\n    }\r\n  }\r\n\r\n  // 取消批次編輯\r\n  cancelBatchEdit(ref: any) {\r\n    this.isBatchEditMode = false;\r\n    this.batchEditItems = [];\r\n    ref.close();\r\n  }\r\n\r\n  // 備用：如果需要手動建立需求項目的方法\r\n  private batchCreateRequirements(requirements: SaveDataRequirement[]) {\r\n    const batchRequests = requirements.map(requirement =>\r\n      this.requirementService.apiRequirementSaveDataPost$Json({\r\n        body: requirement\r\n      })\r\n    );\r\n\r\n    Promise.all(batchRequests.map(req => req.toPromise()))\r\n      .then(responses => {\r\n        const successCount = responses.filter(res => res?.StatusCode === 0).length;\r\n        this.message.showSucessMSG(`成功建立 ${successCount} 個需求項目`);\r\n        this.getList();\r\n      })\r\n      .catch(error => {\r\n        console.error('批次建立需求失敗:', error);\r\n        this.message.showErrorMSG('批次建立需求時發生錯誤');\r\n      });\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n\r\n  <!-- 搜尋區域 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"buildCase\" class=\"label mr-2\">建案</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CBuildCaseID\" class=\"col-9\"\r\n            (ngModelChange)=\"onBuildCaseChange($event)\">\r\n            <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"groupName\" class=\"label mr-2\">區域</label>\r\n          <input type=\"text\" nbInput id=\"groupName\" name=\"groupName\" placeholder=\"區域\"\r\n            [(ngModel)]=\"getListRequirementRequest.CLocation\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"requirement\" class=\"label mr-2\">工程項目</label>\r\n          <input type=\"text\" nbInput id=\"requirement\" name=\"requirement\" placeholder=\"工程項目\"\r\n            [(ngModel)]=\"getListRequirementRequest.CRequirement\">\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"houseType\" class=\"label mr-2\">類型</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CHouseType\" class=\"col-9\" multiple>\r\n            <nb-option *ngFor=\"let type of houseType\" [value]=\"type.value\">\r\n              {{ type.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CStatus\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isShow\" class=\"label mr-2\">預約需求</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsShow\" class=\"col-9\" placeholder=\"全部\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isSimple\" class=\"label mr-2\">簡易客變</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsSimple\" class=\"col-9\" placeholder=\"全部\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\"></div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\"></div>\r\n        <div class=\"form-group col-12 col-md-6 text-right\">\r\n          <button class=\"btn btn-secondary mr-2\" (click)=\"resetSearch()\"><i class=\"fas fa-undo mr-1\"></i>重置</button>\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <app-space-template-selector-button [buildCaseId]=\"getListRequirementRequest.CBuildCaseID?.toString() || ''\"\r\n            [text]=\"'模板新增'\" [icon]=\"'fas fa-layer-group'\" [buttonClass]=\"'btn btn-warning mr-2'\"\r\n            [disabled]=\"!getListRequirementRequest.CBuildCaseID\" (templateApplied)=\"onSpaceTemplateApplied($event)\"\r\n            (error)=\"onTemplateError($event)\">\r\n          </app-space-template-selector-button>\r\n          <button class=\"btn btn-primary mr-2\" (click)=\"openBatchEdit(batchEditDialog)\"\r\n            *ngIf=\"selectedItems.length > 0\"><i class=\"fas fa-edit mr-1\"></i>批次編輯 ({{selectedItems.length}})</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <!-- 建案需求列表 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <div class=\"col-12 mt-3\">\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n              <th scope=\"col\" class=\"col-1 text-center d-flex flex-column align-items-center\">\r\n                <nb-checkbox [ngModel]=\"isAllSelected\" (ngModelChange)=\"toggleSelectAll($event)\">\r\n                </nb-checkbox>\r\n                <small class=\"text-white mt-1\">全選</small>\r\n              </th>\r\n              <th scope=\"col\" class=\"col-2\">區域</th>\r\n              <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n              <th scope=\"col\" class=\"col-1\">類型</th>\r\n              <th scope=\"col\" class=\"col-1\">排序</th>\r\n              <th scope=\"col\" class=\"col-1\">狀態</th>\r\n              <th scope=\"col\" class=\"col-1\">預約需求</th>\r\n              <th scope=\"col\" class=\"col-1\">簡易客變</th>\r\n              <th scope=\"col\" class=\"col-1\">單價</th>\r\n              <th scope=\"col\" class=\"col-1\">操作功能</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n              <td class=\"col-1 text-center\">\r\n                <nb-checkbox [ngModel]=\"isItemSelected(data)\" (ngModelChange)=\"toggleItemSelection(data)\">\r\n                </nb-checkbox>\r\n              </td>\r\n              <td class=\"col-2\">{{ data.CLocation }}</td>\r\n              <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n              <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n              <td class=\"col-1\">{{ data.CSort }}</td>\r\n              <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n              <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n              <td class=\"col-1\">{{ getCIsSimpleText(data) }}</td>\r\n              <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n              <td class=\"col-1\">\r\n                <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                  (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                  (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n        (PageChange)=\"getList()\">\r\n      </ngx-pagination>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 建案對話框 -->\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增建案需求</span>\r\n      <span *ngIf=\"isNew===false\">編輯建案需求</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n\r\n            <app-form-group [label]=\"'區域'\" [labelFor]=\"'CLocation'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CLocation\" name=\"CLocation\" placeholder=\"區域\"\r\n                [(ngModel)]=\"saveRequirement.CLocation\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\" min=\"0\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple>\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\" step=\"0.01\" min=\"0\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'預約需求'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"true\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在預約需求清單\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'簡易客變'\" [labelFor]=\"'CIsSimple'\" [isRequired]=\"true\">\r\n              <nb-checkbox id=\"CIsSimple\" name=\"CIsSimple\" [(ngModel)]=\"saveRequirement.CIsSimple\" class=\"flex-grow-1\">\r\n                設定為簡易客變\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 批次編輯對話框 -->\r\n<ng-template #batchEditDialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 900px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span>批次編輯建案需求 (已選擇 {{selectedItems.length}} 個項目)</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem; max-height: 70vh; overflow-y: auto;\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"alert alert-info\">\r\n            <i class=\"fas fa-info-circle mr-2\"></i>\r\n            您可以個別修改每個項目的欄位\r\n          </div>\r\n\r\n          <div class=\"alert alert-warning\">\r\n            <i class=\"fas fa-exclamation-triangle mr-2\"></i>\r\n            <strong>注意事項：</strong>\r\n            <ul class=\"mb-0 mt-2\">\r\n              <li>工程項目、類型、排序、狀態、單價、單位為必填欄位</li>\r\n              <li>排序和單價不能為負數</li>\r\n              <li>區域最多20個字，工程項目最多50個字，備註說明最多100個字</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <!-- 批次編輯項目列表 -->\r\n          <div *ngFor=\"let item of batchEditItems; let i = index\" class=\"mb-4\">\r\n            <nb-card>\r\n              <nb-card-header class=\"py-2 d-flex justify-content-between align-items-center\">\r\n                <h6 class=\"mb-0\">\r\n                  <i class=\"fas fa-edit mr-2\"></i>\r\n                  項目 {{i + 1}}: {{item.CRequirement}}\r\n                </h6>\r\n                <button type=\"button\" class=\"btn btn-outline-secondary btn-sm\" (click)=\"resetBatchEditItem(i)\"\r\n                  title=\"重置為原始值\">\r\n                  <i class=\"fas fa-undo mr-1\"></i>重置\r\n                </button>\r\n              </nb-card-header>\r\n              <nb-card-body class=\"py-3\">\r\n                <div class=\"row\">\r\n                  <!-- 區域 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'區域'\" [labelFor]=\"'location_' + i\" [isRequired]=\"false\">\r\n                      <input type=\"text\" nbInput class=\"flex-grow-1\" [id]=\"'location_' + i\" [(ngModel)]=\"item.CLocation\"\r\n                        placeholder=\"區域\" maxlength=\"20\">\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 工程項目 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'requirement_' + i\" [isRequired]=\"true\">\r\n                      <input type=\"text\" nbInput class=\"flex-grow-1\" [id]=\"'requirement_' + i\"\r\n                        [(ngModel)]=\"item.CRequirement\" placeholder=\"工程項目\" maxlength=\"50\">\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 類型 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'類型'\" [labelFor]=\"'houseType_' + i\" [isRequired]=\"true\">\r\n                      <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" [id]=\"'houseType_' + i\"\r\n                        [(selected)]=\"item.CHouseType\" multiple>\r\n                        <nb-option *ngFor=\"let type of houseType\" [value]=\"type.value\">\r\n                          {{type.label}}\r\n                        </nb-option>\r\n                      </nb-select>\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 排序 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'排序'\" [labelFor]=\"'sort_' + i\" [isRequired]=\"true\">\r\n                      <input type=\"number\" nbInput class=\"flex-grow-1\" [id]=\"'sort_' + i\" [(ngModel)]=\"item.CSort\"\r\n                        placeholder=\"排序\" min=\"0\">\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 狀態 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'狀態'\" [labelFor]=\"'status_' + i\" [isRequired]=\"true\">\r\n                      <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" [id]=\"'status_' + i\" [(selected)]=\"item.CStatus\">\r\n                        <nb-option *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                          {{status.label}}\r\n                        </nb-option>\r\n                      </nb-select>\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 單價 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'單價'\" [labelFor]=\"'unitPrice_' + i\" [isRequired]=\"true\">\r\n                      <input type=\"number\" nbInput class=\"flex-grow-1\" [id]=\"'unitPrice_' + i\"\r\n                        [(ngModel)]=\"item.CUnitPrice\" placeholder=\"單價\" step=\"0.01\" min=\"0\">\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 單位 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'單位'\" [labelFor]=\"'unit_' + i\" [isRequired]=\"true\">\r\n                      <input type=\"text\" nbInput class=\"flex-grow-1\" [id]=\"'unit_' + i\" [(ngModel)]=\"item.CUnit\"\r\n                        placeholder=\"單位\">\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 預約需求 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'預約需求'\" [labelFor]=\"'isShow_' + i\" [isRequired]=\"true\">\r\n                      <nb-checkbox [id]=\"'isShow_' + i\" [(ngModel)]=\"item.CIsShow\" class=\"flex-grow-1\">\r\n                        顯示在預約需求清單\r\n                      </nb-checkbox>\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 簡易客變 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'簡易客變'\" [labelFor]=\"'isSimple_' + i\" [isRequired]=\"true\">\r\n                      <nb-checkbox [id]=\"'isSimple_' + i\" [(ngModel)]=\"item.CIsSimple\" class=\"flex-grow-1\">\r\n                        設定為簡易客變\r\n                      </nb-checkbox>\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 備註說明 -->\r\n                  <div class=\"col-12\">\r\n                    <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'remark_' + i\" [isRequired]=\"false\">\r\n                      <textarea nbInput class=\"flex-grow-1\" [id]=\"'remark_' + i\" [(ngModel)]=\"item.CRemark\"\r\n                        placeholder=\"備註說明\" maxlength=\"100\" rows=\"2\">\r\n                      </textarea>\r\n                    </app-form-group>\r\n                  </div>\r\n                </div>\r\n              </nb-card-body>\r\n            </nb-card>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"batchSave(ref)\">確定批次更新</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"cancelBatchEdit(ref)\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AAEA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/H,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;AAGjE,SAASC,oCAAoC,QAAQ,4FAA4F;;;;;;;;;;;;;;;;ICTrIC,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;IAkBAT,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAC,KAAA,CAAoB;IAC5DX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAE,KAAA,MACF;;;;;;IAyCFZ,EAAA,CAAAC,cAAA,iBACmC;IADED,EAAA,CAAAa,UAAA,mBAAAC,0EAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,kBAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAK,aAAA,CAAAH,kBAAA,CAA8B;IAAA,EAAC;IAC1CnB,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAxCH,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAQ,kBAAA,+BAAAS,MAAA,CAAAO,aAAA,CAAAC,MAAA,MAA+B;;;;;;IAClGzB,EAAA,CAAAC,cAAA,iBAA4E;IAAvCD,EAAA,CAAAa,UAAA,mBAAAa,0EAAA;MAAA1B,EAAA,CAAAe,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAU,SAAA,GAAA5B,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAY,GAAA,CAAAD,SAAA,CAAW;IAAA,EAAC;IAAkB5B,EAAA,CAAAuB,SAAA,YAC3C;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA4CtCH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAa,UAAA,mBAAAiB,gFAAA;MAAA9B,EAAA,CAAAe,aAAA,CAAAgB,IAAA;MAAA,MAAAC,QAAA,GAAAhC,EAAA,CAAAkB,aAAA,GAAAe,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAU,SAAA,GAAA5B,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAiB,MAAA,CAAAF,QAAA,EAAAJ,SAAA,CAAmB;IAAA,EAAC;IAAC5B,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAa,UAAA,mBAAAsB,gFAAA;MAAAnC,EAAA,CAAAe,aAAA,CAAAqB,IAAA;MAAA,MAAAJ,QAAA,GAAAhC,EAAA,CAAAkB,aAAA,GAAAe,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAoB,QAAA,CAAAL,QAAA,CAAc;IAAA,EAAC;IAAChC,EAAA,CAAAuB,SAAA,YAAqC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAf3EH,EAFJ,CAAAC,cAAA,aAAuE,aACvC,sBAC8D;IAA5CD,EAAA,CAAAa,UAAA,2BAAAyB,mFAAA;MAAA,MAAAN,QAAA,GAAAhC,EAAA,CAAAe,aAAA,CAAAwB,GAAA,EAAAN,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAAiBJ,MAAA,CAAAuB,mBAAA,CAAAR,QAAA,CAAyB;IAAA,EAAC;IAE3FhC,EADE,CAAAG,YAAA,EAAc,EACX;IACLH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAyC,UAAA,KAAAC,uDAAA,qBACgC,KAAAC,uDAAA,qBAEL;IAE/B3C,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAjBYH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA2B,cAAA,CAAAZ,QAAA,EAAgC;IAG7BhC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAA6C,iBAAA,CAAAb,QAAA,CAAAc,SAAA,CAAoB;IACpB9C,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAA6C,iBAAA,CAAAb,QAAA,CAAAe,YAAA,CAAuB;IACvB/C,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAA6C,iBAAA,CAAA5B,MAAA,CAAA+B,YAAA,CAAAhB,QAAA,CAAAiB,UAAA,IAAAjD,EAAA,CAAAkD,eAAA,KAAAC,GAAA,GAAyC;IACzCnD,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAA6C,iBAAA,CAAAb,QAAA,CAAAoB,KAAA,CAAgB;IAChBpD,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,SAAArB,QAAA,CAAAsB,OAAA,EAAkC;IAClCtD,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAA6C,iBAAA,CAAA5B,MAAA,CAAAsC,cAAA,CAAAvB,QAAA,EAA0B;IAC1BhC,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAA6C,iBAAA,CAAA5B,MAAA,CAAAuC,gBAAA,CAAAxB,QAAA,EAA4B;IAC5BhC,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,SAAArB,QAAA,CAAAyB,UAAA,OAAkD;IAEzDzD,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAyC,QAAA,CAAc;IAEd1D,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA0C,QAAA,CAAc;;;;;IAkBjC3D,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxCH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAsB/BH,EAAA,CAAAC,cAAA,oBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAAwD,QAAA,CAAAjD,KAAA,CAAoB;IAAEX,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAAoD,QAAA,CAAAhD,KAAA,KAAc;;;;;IAMpFZ,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAAyD,UAAA,CAAAlD,KAAA,CAAsB;IAC1EX,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAAqD,UAAA,CAAAjD,KAAA,KAAgB;;;;;;IA/B9BZ,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAAyC,UAAA,IAAAqB,8DAAA,mBAA2B,IAAAC,8DAAA,mBACC;IAC9B/D,EAAA,CAAAG,YAAA,EAAiB;IAOPH,EANV,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX,yBAE8D,gBAElB;IAAvDD,EAAA,CAAAgE,gBAAA,2BAAAC,uFAAAC,MAAA;MAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAoE,kBAAA,CAAAnD,MAAA,CAAAoD,eAAA,CAAAvB,SAAA,EAAAoB,MAAA,MAAAjD,MAAA,CAAAoD,eAAA,CAAAvB,SAAA,GAAAoB,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAAuC;IAC3ClE,EAFE,CAAAG,YAAA,EACyD,EAC1C;IAEfH,EADF,CAAAC,cAAA,0BAAiF,iBAEnB;IAA1DD,EAAA,CAAAgE,gBAAA,2BAAAM,wFAAAJ,MAAA;MAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAoE,kBAAA,CAAAnD,MAAA,CAAAoD,eAAA,CAAAtB,YAAA,EAAAmB,MAAA,MAAAjD,MAAA,CAAAoD,eAAA,CAAAtB,YAAA,GAAAmB,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAA0C;IAC9ClE,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAExB;IAA5CD,EAAA,CAAAgE,gBAAA,2BAAAO,wFAAAL,MAAA;MAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAoE,kBAAA,CAAAnD,MAAA,CAAAoD,eAAA,CAAAjB,KAAA,EAAAc,MAAA,MAAAjD,MAAA,CAAAoD,eAAA,CAAAjB,KAAA,GAAAc,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAAmC;IACvClE,EAFE,CAAAG,YAAA,EAC8C,EAC/B;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEtB;IAAnDD,EAAA,CAAAgE,gBAAA,4BAAAQ,6FAAAN,MAAA;MAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAoE,kBAAA,CAAAnD,MAAA,CAAAoD,eAAA,CAAApB,UAAA,EAAAiB,MAAA,MAAAjD,MAAA,CAAAoD,eAAA,CAAApB,UAAA,GAAAiB,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAAyC;IACzClE,EAAA,CAAAyC,UAAA,KAAAgC,oEAAA,wBAAqE;IAEzEzE,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAgE,gBAAA,4BAAAU,6FAAAR,MAAA;MAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAoE,kBAAA,CAAAnD,MAAA,CAAAoD,eAAA,CAAAf,OAAA,EAAAY,MAAA,MAAAjD,MAAA,CAAAoD,eAAA,CAAAf,OAAA,GAAAY,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAAsC;IACtClE,EAAA,CAAAyC,UAAA,KAAAkC,oEAAA,wBAA6E;IAGjF3E,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAEZ;IAA7DD,EAAA,CAAAgE,gBAAA,2BAAAY,wFAAAV,MAAA;MAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAoE,kBAAA,CAAAnD,MAAA,CAAAoD,eAAA,CAAAZ,UAAA,EAAAS,MAAA,MAAAjD,MAAA,CAAAoD,eAAA,CAAAZ,UAAA,GAAAS,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAAwC;IAC5ClE,EAFE,CAAAG,YAAA,EAC+D,EAChD;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAgE,gBAAA,2BAAAa,wFAAAX,MAAA;MAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAoE,kBAAA,CAAAnD,MAAA,CAAAoD,eAAA,CAAAS,KAAA,EAAAZ,MAAA,MAAAjD,MAAA,CAAAoD,eAAA,CAAAS,KAAA,GAAAZ,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAAmC;IACvClE,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA4E,uBACyB;IAA1DD,EAAA,CAAAgE,gBAAA,2BAAAe,8FAAAb,MAAA;MAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAoE,kBAAA,CAAAnD,MAAA,CAAAoD,eAAA,CAAAW,OAAA,EAAAd,MAAA,MAAAjD,MAAA,CAAAoD,eAAA,CAAAW,OAAA,GAAAd,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAAqC;IAC5ElE,EAAA,CAAAE,MAAA,gEACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA8E,uBAC6B;IAA5DD,EAAA,CAAAgE,gBAAA,2BAAAiB,8FAAAf,MAAA;MAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAoE,kBAAA,CAAAnD,MAAA,CAAAoD,eAAA,CAAAa,SAAA,EAAAhB,MAAA,MAAAjD,MAAA,CAAAoD,eAAA,CAAAa,SAAA,GAAAhB,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAAuC;IAClFlE,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,oBAEV;IAA/DD,EAAA,CAAAgE,gBAAA,2BAAAmB,2FAAAjB,MAAA;MAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAoE,kBAAA,CAAAnD,MAAA,CAAAoD,eAAA,CAAAe,OAAA,EAAAlB,MAAA,MAAAjD,MAAA,CAAAoD,eAAA,CAAAe,OAAA,GAAAlB,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAAqC;IAKjDlE,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBAC2B;IAApBD,EAAA,CAAAa,UAAA,mBAAAwE,iFAAA;MAAA,MAAAC,OAAA,GAAAtF,EAAA,CAAAe,aAAA,CAAAoD,IAAA,EAAAoB,SAAA;MAAA,MAAAtE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAuE,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAACtF,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAAa,UAAA,mBAAA4E,iFAAA;MAAA,MAAAH,OAAA,GAAAtF,EAAA,CAAAe,aAAA,CAAAoD,IAAA,EAAAoB,SAAA;MAAA,OAAAvF,EAAA,CAAAqB,WAAA,CAASiE,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAAC1F,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IAnECH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA0E,KAAA,UAAkB;IAClB3F,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA0E,KAAA,WAAmB;IAOJ3F,EAAA,CAAAO,SAAA,GAAc;IAA0BP,EAAxC,CAAAI,UAAA,yBAAc,yBAAyB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAA4F,gBAAA,YAAA3E,MAAA,CAAAoD,eAAA,CAAAvB,SAAA,CAAuC;IAE3B9C,EAAA,CAAAO,SAAA,EAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAA4F,gBAAA,YAAA3E,MAAA,CAAAoD,eAAA,CAAAtB,YAAA,CAA0C;IAE9B/C,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA4F,gBAAA,YAAA3E,MAAA,CAAAoD,eAAA,CAAAjB,KAAA,CAAmC;IAEvBpD,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAA4F,gBAAA,aAAA3E,MAAA,CAAAoD,eAAA,CAAApB,UAAA,CAAyC;IACPjD,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA4E,SAAA,CAAY;IAGlC7F,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAA4F,gBAAA,aAAA3E,MAAA,CAAAoD,eAAA,CAAAf,OAAA,CAAsC;IACFtD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA6E,aAAA,CAAgB;IAIxC9F,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAA4F,gBAAA,YAAA3E,MAAA,CAAAoD,eAAA,CAAAZ,UAAA,CAAwC;IAE5BzD,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA4F,gBAAA,YAAA3E,MAAA,CAAAoD,eAAA,CAAAS,KAAA,CAAmC;IAEvB9E,EAAA,CAAAO,SAAA,EAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,oBAAoB;IAChCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA4F,gBAAA,YAAA3E,MAAA,CAAAoD,eAAA,CAAAW,OAAA,CAAqC;IAIhEhF,EAAA,CAAAO,SAAA,GAAgB;IAA0BP,EAA1C,CAAAI,UAAA,qCAAgB,yBAAyB,oBAAoB;IAC9BJ,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAA4F,gBAAA,YAAA3E,MAAA,CAAAoD,eAAA,CAAAa,SAAA,CAAuC;IAItElF,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA4F,gBAAA,YAAA3E,MAAA,CAAAoD,eAAA,CAAAe,OAAA,CAAqC;;;;;IA6E7BpF,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAA2F,QAAA,CAAApF,KAAA,CAAoB;IAC5DX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAuF,QAAA,CAAAnF,KAAA,MACF;;;;;IAiBAZ,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAA4F,UAAA,CAAArF,KAAA,CAAsB;IACpEX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAwF,UAAA,CAAApF,KAAA,MACF;;;;;;IArDRZ,EAHN,CAAAC,cAAA,cAAqE,cAC1D,yBACwE,aAC5D;IACfD,EAAA,CAAAuB,SAAA,YAAgC;IAChCvB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,iBACiB;IAD8CD,EAAA,CAAAa,UAAA,mBAAAoF,uFAAA;MAAA,MAAAC,KAAA,GAAAlG,EAAA,CAAAe,aAAA,CAAAoF,IAAA,EAAAC,KAAA;MAAA,MAAAnF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAoF,kBAAA,CAAAH,KAAA,CAAqB;IAAA,EAAC;IAE5FlG,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,oBAClC;IACFF,EADE,CAAAG,YAAA,EAAS,EACM;IAMTH,EALR,CAAAC,cAAA,uBAA2B,cACR,eAEO,0BAC6D,iBAE7C;IADoCD,EAAA,CAAAgE,gBAAA,2BAAAsC,+FAAApC,MAAA;MAAA,MAAAqC,QAAA,GAAAvG,EAAA,CAAAe,aAAA,CAAAoF,IAAA,EAAAlE,SAAA;MAAAjC,EAAA,CAAAoE,kBAAA,CAAAmC,QAAA,CAAAzD,SAAA,EAAAoB,MAAA,MAAAqC,QAAA,CAAAzD,SAAA,GAAAoB,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAA4B;IAGtGlE,EAHI,CAAAG,YAAA,EACkC,EACnB,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BACiE,iBAEf;IAAlED,EAAA,CAAAgE,gBAAA,2BAAAwC,+FAAAtC,MAAA;MAAA,MAAAqC,QAAA,GAAAvG,EAAA,CAAAe,aAAA,CAAAoF,IAAA,EAAAlE,SAAA;MAAAjC,EAAA,CAAAoE,kBAAA,CAAAmC,QAAA,CAAAxD,YAAA,EAAAmB,MAAA,MAAAqC,QAAA,CAAAxD,YAAA,GAAAmB,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAA+B;IAErClE,EAHI,CAAAG,YAAA,EACoE,EACrD,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BAC6D,qBAErC;IAAxCD,EAAA,CAAAgE,gBAAA,4BAAAyC,oGAAAvC,MAAA;MAAA,MAAAqC,QAAA,GAAAvG,EAAA,CAAAe,aAAA,CAAAoF,IAAA,EAAAlE,SAAA;MAAAjC,EAAA,CAAAoE,kBAAA,CAAAmC,QAAA,CAAAtD,UAAA,EAAAiB,MAAA,MAAAqC,QAAA,CAAAtD,UAAA,GAAAiB,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAA8B;IAC9BlE,EAAA,CAAAyC,UAAA,KAAAiE,2EAAA,uBAA+D;IAKrE1G,EAFI,CAAAG,YAAA,EAAY,EACG,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BACwD,iBAE/C;IADyCD,EAAA,CAAAgE,gBAAA,2BAAA2C,+FAAAzC,MAAA;MAAA,MAAAqC,QAAA,GAAAvG,EAAA,CAAAe,aAAA,CAAAoF,IAAA,EAAAlE,SAAA;MAAAjC,EAAA,CAAAoE,kBAAA,CAAAmC,QAAA,CAAAnD,KAAA,EAAAc,MAAA,MAAAqC,QAAA,CAAAnD,KAAA,GAAAc,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAAwB;IAGhGlE,EAHI,CAAAG,YAAA,EAC2B,EACZ,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BAC0D,qBACsB;IAA5BD,EAAA,CAAAgE,gBAAA,4BAAA4C,oGAAA1C,MAAA;MAAA,MAAAqC,QAAA,GAAAvG,EAAA,CAAAe,aAAA,CAAAoF,IAAA,EAAAlE,SAAA;MAAAjC,EAAA,CAAAoE,kBAAA,CAAAmC,QAAA,CAAAjD,OAAA,EAAAY,MAAA,MAAAqC,QAAA,CAAAjD,OAAA,GAAAY,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAA2B;IAC/FlE,EAAA,CAAAyC,UAAA,KAAAoE,2EAAA,uBAAuE;IAK7E7G,EAFI,CAAAG,YAAA,EAAY,EACG,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BAC6D,iBAEV;IAAnED,EAAA,CAAAgE,gBAAA,2BAAA8C,+FAAA5C,MAAA;MAAA,MAAAqC,QAAA,GAAAvG,EAAA,CAAAe,aAAA,CAAAoF,IAAA,EAAAlE,SAAA;MAAAjC,EAAA,CAAAoE,kBAAA,CAAAmC,QAAA,CAAA9C,UAAA,EAAAS,MAAA,MAAAqC,QAAA,CAAA9C,UAAA,GAAAS,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAA6B;IAEnClE,EAHI,CAAAG,YAAA,EACqE,EACtD,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BACwD,iBAEvD;IAD+CD,EAAA,CAAAgE,gBAAA,2BAAA+C,+FAAA7C,MAAA;MAAA,MAAAqC,QAAA,GAAAvG,EAAA,CAAAe,aAAA,CAAAoF,IAAA,EAAAlE,SAAA;MAAAjC,EAAA,CAAAoE,kBAAA,CAAAmC,QAAA,CAAAzB,KAAA,EAAAZ,MAAA,MAAAqC,QAAA,CAAAzB,KAAA,GAAAZ,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAAwB;IAG9FlE,EAHI,CAAAG,YAAA,EACmB,EACJ,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BAC4D,uBACG;IAA/CD,EAAA,CAAAgE,gBAAA,2BAAAgD,qGAAA9C,MAAA;MAAA,MAAAqC,QAAA,GAAAvG,EAAA,CAAAe,aAAA,CAAAoF,IAAA,EAAAlE,SAAA;MAAAjC,EAAA,CAAAoE,kBAAA,CAAAmC,QAAA,CAAAvB,OAAA,EAAAd,MAAA,MAAAqC,QAAA,CAAAvB,OAAA,GAAAd,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAA0B;IAC1DlE,EAAA,CAAAE,MAAA,gEACF;IAEJF,EAFI,CAAAG,YAAA,EAAc,EACC,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BAC8D,uBACK;IAAjDD,EAAA,CAAAgE,gBAAA,2BAAAiD,qGAAA/C,MAAA;MAAA,MAAAqC,QAAA,GAAAvG,EAAA,CAAAe,aAAA,CAAAoF,IAAA,EAAAlE,SAAA;MAAAjC,EAAA,CAAAoE,kBAAA,CAAAmC,QAAA,CAAArB,SAAA,EAAAhB,MAAA,MAAAqC,QAAA,CAAArB,SAAA,GAAAhB,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAA4B;IAC9DlE,EAAA,CAAAE,MAAA,oDACF;IAEJF,EAFI,CAAAG,YAAA,EAAc,EACC,EACb;IAKFH,EAFJ,CAAAC,cAAA,cAAoB,0BAC+D,oBAEjC;IADaD,EAAA,CAAAgE,gBAAA,2BAAAkD,kGAAAhD,MAAA;MAAA,MAAAqC,QAAA,GAAAvG,EAAA,CAAAe,aAAA,CAAAoF,IAAA,EAAAlE,SAAA;MAAAjC,EAAA,CAAAoE,kBAAA,CAAAmC,QAAA,CAAAnB,OAAA,EAAAlB,MAAA,MAAAqC,QAAA,CAAAnB,OAAA,GAAAlB,MAAA;MAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;IAAA,EAA0B;IAErFlE,EAAA,CAAAE,MAAA;IAMZF,EANY,CAAAG,YAAA,EAAW,EACI,EACb,EACF,EACO,EACP,EACN;;;;;;IArGEH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAmH,kBAAA,mBAAAjB,KAAA,YAAAK,QAAA,CAAAxD,YAAA,MACF;IAUoB/C,EAAA,CAAAO,SAAA,GAAc;IAA8BP,EAA5C,CAAAI,UAAA,yBAAc,2BAAA8F,KAAA,CAA6B,qBAAqB;IAC/BlG,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,qBAAA8F,KAAA,CAAsB;IAAClG,EAAA,CAAA4F,gBAAA,YAAAW,QAAA,CAAAzD,SAAA,CAA4B;IAOpF9C,EAAA,CAAAO,SAAA,GAAgB;IAAiCP,EAAjD,CAAAI,UAAA,qCAAgB,8BAAA8F,KAAA,CAAgC,oBAAoB;IACnClG,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,wBAAA8F,KAAA,CAAyB;IACtElG,EAAA,CAAA4F,gBAAA,YAAAW,QAAA,CAAAxD,YAAA,CAA+B;IAMnB/C,EAAA,CAAAO,SAAA,GAAc;IAA+BP,EAA7C,CAAAI,UAAA,yBAAc,4BAAA8F,KAAA,CAA8B,oBAAoB;IAC7BlG,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAI,UAAA,sBAAA8F,KAAA,CAAuB;IACtElG,EAAA,CAAA4F,gBAAA,aAAAW,QAAA,CAAAtD,UAAA,CAA8B;IACFjD,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA4E,SAAA,CAAY;IAS5B7F,EAAA,CAAAO,SAAA,GAAc;IAA0BP,EAAxC,CAAAI,UAAA,yBAAc,uBAAA8F,KAAA,CAAyB,oBAAoB;IACxBlG,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,iBAAA8F,KAAA,CAAkB;IAAClG,EAAA,CAAA4F,gBAAA,YAAAW,QAAA,CAAAnD,KAAA,CAAwB;IAO9EpD,EAAA,CAAAO,SAAA,GAAc;IAA4BP,EAA1C,CAAAI,UAAA,yBAAc,yBAAA8F,KAAA,CAA2B,oBAAoB;IAC1BlG,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,mBAAA8F,KAAA,CAAoB;IAAClG,EAAA,CAAA4F,gBAAA,aAAAW,QAAA,CAAAjD,OAAA,CAA2B;IACjEtD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA6E,aAAA,CAAgB;IASlC9F,EAAA,CAAAO,SAAA,GAAc;IAA+BP,EAA7C,CAAAI,UAAA,yBAAc,4BAAA8F,KAAA,CAA8B,oBAAoB;IAC7BlG,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAI,UAAA,sBAAA8F,KAAA,CAAuB;IACtElG,EAAA,CAAA4F,gBAAA,YAAAW,QAAA,CAAA9C,UAAA,CAA6B;IAMjBzD,EAAA,CAAAO,SAAA,GAAc;IAA0BP,EAAxC,CAAAI,UAAA,yBAAc,uBAAA8F,KAAA,CAAyB,oBAAoB;IAC1BlG,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,iBAAA8F,KAAA,CAAkB;IAAClG,EAAA,CAAA4F,gBAAA,YAAAW,QAAA,CAAAzB,KAAA,CAAwB;IAO5E9E,EAAA,CAAAO,SAAA,GAAgB;IAA4BP,EAA5C,CAAAI,UAAA,qCAAgB,yBAAA8F,KAAA,CAA2B,oBAAoB;IAChElG,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,mBAAA8F,KAAA,CAAoB;IAAClG,EAAA,CAAA4F,gBAAA,YAAAW,QAAA,CAAAvB,OAAA,CAA0B;IAQ9ChF,EAAA,CAAAO,SAAA,GAAgB;IAA8BP,EAA9C,CAAAI,UAAA,qCAAgB,2BAAA8F,KAAA,CAA6B,oBAAoB;IAClElG,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,qBAAA8F,KAAA,CAAsB;IAAClG,EAAA,CAAA4F,gBAAA,YAAAW,QAAA,CAAArB,SAAA,CAA4B;IAQlDlF,EAAA,CAAAO,SAAA,GAAgB;IAA4BP,EAA5C,CAAAI,UAAA,qCAAgB,yBAAA8F,KAAA,CAA2B,qBAAqB;IACxClG,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,mBAAA8F,KAAA,CAAoB;IAAClG,EAAA,CAAA4F,gBAAA,YAAAW,QAAA,CAAAnB,OAAA,CAA0B;;;;;;IAvHrGpF,EAFJ,CAAAC,cAAA,kBAAyF,qBACvE,WACR;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACzC;IAIXH,EAHN,CAAAC,cAAA,uBAA6E,aAC1D,aACK,cACY;IAC5BD,EAAA,CAAAuB,SAAA,YAAuC;IACvCvB,EAAA,CAAAE,MAAA,6FACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAuB,SAAA,aAAgD;IAChDvB,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEpBH,EADF,CAAAC,cAAA,cAAsB,UAChB;IAAAD,EAAA,CAAAE,MAAA,wJAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oEAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,+JAA+B;IAEvCF,EAFuC,CAAAG,YAAA,EAAK,EACrC,EACD;IAGNH,EAAA,CAAAyC,UAAA,KAAA2E,8DAAA,oBAAqE;IA6G3EpH,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBACgC;IAAzBD,EAAA,CAAAa,UAAA,mBAAAwG,iFAAA;MAAA,MAAAC,OAAA,GAAAtH,EAAA,CAAAe,aAAA,CAAAwG,IAAA,EAAAhC,SAAA;MAAA,MAAAtE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAuG,SAAA,CAAAF,OAAA,CAAc;IAAA,EAAC;IAACtH,EAAA,CAAAE,MAAA,4CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7EH,EAAA,CAAAC,cAAA,kBAAmE;IAA/BD,EAAA,CAAAa,UAAA,mBAAA4G,iFAAA;MAAA,MAAAH,OAAA,GAAAtH,EAAA,CAAAe,aAAA,CAAAwG,IAAA,EAAAhC,SAAA;MAAA,MAAAtE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAyG,eAAA,CAAAJ,OAAA,CAAoB;IAAA,EAAC;IAACtH,EAAA,CAAAE,MAAA,oBAAE;IAI7EF,EAJ6E,CAAAG,YAAA,EAAS,EAC1E,EACF,EACS,EACT;;;;IA3IAH,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAQ,kBAAA,0EAAAS,MAAA,CAAAO,aAAA,CAAAC,MAAA,yBAA2C;IAqBvBzB,EAAA,CAAAO,SAAA,IAAmB;IAAnBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA0G,cAAA,CAAmB;;;ADhMnD,OAAM,MAAOC,8BAA+B,SAAQxI,aAAa;EAG/DyI,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,eAAgC,EAChCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB,EACtBC,4BAA0D;IAElE,KAAK,CAACX,MAAM,CAAC;IAbL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,4BAA4B,GAA5BA,4BAA4B;IAOtC;IACA,KAAAC,yBAAyB,GAAG,EAA0F;IACtH,KAAAC,qBAAqB,GAA8B,EAAE;IAErD;IACA,KAAAC,aAAa,GAA8B,EAAE;IAC7C,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAxE,eAAe,GAAqE;MAAEpB,UAAU,EAAE;IAAE,CAAE;IAEtG,KAAA6C,aAAa,GAAG,CACd;MAAEnF,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAiF,SAAS,GAAG,IAAI,CAACkC,UAAU,CAACe,cAAc,CAAChJ,aAAa,CAAC;IACzD,KAAA6F,KAAK,GAAG,KAAK;IACb,KAAAoD,gBAAgB,GAAG,CAAC;IAEpB;IACA,KAAAvH,aAAa,GAAqB,EAAE;IACpC,KAAAwH,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,KAAK;IACvB;IACA,KAAAtB,cAAc,GAAyE,EAAE;IA1BvF,IAAI,CAACuB,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EA0BSC,QAAQA,CAAA,GAAW;EAE5B;EACAF,oBAAoBA,CAAA;IAClB,IAAI,CAACR,yBAAyB,CAACpF,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACoF,yBAAyB,CAAC1D,OAAO,GAAG,IAAI;IAC7C,IAAI,CAAC0D,yBAAyB,CAACxD,SAAS,GAAG,IAAI;IAC/C,IAAI,CAACwD,yBAAyB,CAAC3F,YAAY,GAAG,EAAE;IAChD,IAAI,CAAC2F,yBAAyB,CAAC5F,SAAS,GAAG,EAAE;IAC7C;IACA,IAAI,CAAC4F,yBAAyB,CAACzF,UAAU,GAAG,IAAI,CAAC4C,SAAS,CAACwD,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC3I,KAAK,CAAC;EACpF;EAEA;EACA4I,iBAAiBA,CAACC,cAAmB;IACnC;IACA,IAAI,IAAI,CAACP,eAAe,IAAI,IAAI,CAACzH,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMgI,cAAc,GAAG,IAAI,CAACR,eAAe,GACvC,iCAAiC,GACjC,gBAAgB,IAAI,CAACzH,aAAa,CAACC,MAAM,YAAY;MAEzD,IAAI,CAACiI,OAAO,CAACD,cAAc,CAAC,EAAE;QAC5B;QACAE,UAAU,CAAC,MAAK;UACd,IAAI,CAACjB,yBAAyB,CAACkB,YAAY,GAAG,IAAI,CAACb,gBAAgB;QACrE,CAAC,EAAE,CAAC,CAAC;QACL;MACF;IACF;IAEA;IACA,IAAI,CAACvH,aAAa,GAAG,EAAE;IACvB,IAAI,CAACwH,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACrB,cAAc,GAAG,EAAE;IACxB,IAAI,CAACsB,eAAe,GAAG,KAAK;IAE5B;IACA,IAAI,CAACF,gBAAgB,GAAGS,cAAc;IACtC,IAAI,CAACK,OAAO,EAAE;EAChB;EAEA;EACAC,WAAWA,CAAA;IACT,IAAI,CAACZ,oBAAoB,EAAE;IAC3B;IACA,IAAI,CAAC1H,aAAa,GAAG,EAAE;IACvB,IAAI,CAACwH,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACrB,cAAc,GAAG,EAAE;IACxB,IAAI,CAACsB,eAAe,GAAG,KAAK;IAE5B;IACA,IAAI,IAAI,CAACL,aAAa,IAAI,IAAI,CAACA,aAAa,CAACnH,MAAM,GAAG,CAAC,EAAE;MACvDkI,UAAU,CAAC,MAAK;QACd,IAAI,CAACjB,yBAAyB,CAACkB,YAAY,GAAG,IAAI,CAAChB,aAAa,CAAC,CAAC,CAAC,CAACtI,GAAG;QACvE,IAAI,CAACuJ,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEA7G,YAAYA,CAAC+G,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACyE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5J,KAAK,IAAIyJ,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAACzJ,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOsJ,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACxC,KAAK,CAACyC,KAAK,EAAE;IAElB;IACA,IAAI,CAACzC,KAAK,CAAC0C,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvG,eAAe,CAACuF,YAAY,CAAC;IAChE,IAAI,CAAC1B,KAAK,CAAC0C,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvG,eAAe,CAACtB,YAAY,CAAC;IAC9D,IAAI,CAACmF,KAAK,CAAC0C,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACvG,eAAe,CAACpB,UAAU,CAAC;IAC7D,IAAI,CAACiF,KAAK,CAAC0C,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvG,eAAe,CAACjB,KAAK,CAAC;IACvD,IAAI,CAAC8E,KAAK,CAAC0C,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvG,eAAe,CAACf,OAAO,CAAC;IACzD,IAAI,CAAC4E,KAAK,CAAC0C,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvG,eAAe,CAACZ,UAAU,CAAC;IAC5D,IAAI,CAACyE,KAAK,CAAC0C,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvG,eAAe,CAACS,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACT,eAAe,CAACjB,KAAK,KAAK,IAAI,IAAI,IAAI,CAACiB,eAAe,CAACjB,KAAK,KAAKyH,SAAS,IAAI,IAAI,CAACxG,eAAe,CAACjB,KAAK,GAAG,CAAC,EAAE;MACrH,IAAI,CAAC8E,KAAK,CAAC4C,aAAa,CAACN,IAAI,CAAC,YAAY,CAAC;IAC7C;IAEA,IAAI,IAAI,CAACnG,eAAe,CAACZ,UAAU,KAAK,IAAI,IAAI,IAAI,CAACY,eAAe,CAACZ,UAAU,KAAKoH,SAAS,IAAI,IAAI,CAACxG,eAAe,CAACZ,UAAU,GAAG,CAAC,EAAE;MACpI,IAAI,CAACyE,KAAK,CAAC4C,aAAa,CAACN,IAAI,CAAC,YAAY,CAAC;IAC7C;IAEA;IACA,IAAI,IAAI,CAACnG,eAAe,CAACvB,SAAS,IAAI,IAAI,CAACuB,eAAe,CAACvB,SAAS,CAACrB,MAAM,GAAG,EAAE,EAAE;MAChF,IAAI,CAACyG,KAAK,CAAC4C,aAAa,CAACN,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA,IAAI,IAAI,CAACnG,eAAe,CAACtB,YAAY,IAAI,IAAI,CAACsB,eAAe,CAACtB,YAAY,CAACtB,MAAM,GAAG,EAAE,EAAE;MACtF,IAAI,CAACyG,KAAK,CAAC4C,aAAa,CAACN,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACnG,eAAe,CAACe,OAAO,IAAI,IAAI,CAACf,eAAe,CAACe,OAAO,CAAC3D,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACyG,KAAK,CAAC4C,aAAa,CAACN,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEA3I,GAAGA,CAACkJ,MAAwB;IAC1B,IAAI,CAACpF,KAAK,GAAG,IAAI;IACjB,IAAI,CAACtB,eAAe,GAAG;MAAEpB,UAAU,EAAE,EAAE;MAAE+B,OAAO,EAAE,KAAK;MAAEE,SAAS,EAAE;IAAK,CAAE;IAC3E,IAAI,CAACb,eAAe,CAACf,OAAO,GAAG,CAAC;IAChC,IAAI,CAACe,eAAe,CAACZ,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAACsF,gBAAgB,IAAI,CAAC,EAAE;MAC9B,IAAI,CAAC1E,eAAe,CAACuF,YAAY,GAAG,IAAI,CAACb,gBAAgB;IAC3D,CAAC,MAAM,IAAI,IAAI,CAACH,aAAa,IAAI,IAAI,CAACA,aAAa,CAACnH,MAAM,GAAG,CAAC,EAAE;MAC9D,IAAI,CAAC4C,eAAe,CAACuF,YAAY,GAAG,IAAI,CAAChB,aAAa,CAAC,CAAC,CAAC,CAACtI,GAAG;IAC/D;IAEA,IAAI,CAAC0H,aAAa,CAACgD,IAAI,CAACD,MAAM,CAAC;EACjC;EAEM7I,MAAMA,CAAC+I,IAAoB,EAAEF,MAAwB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAACvC,qBAAqB,CAACyC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAACvF,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMuF,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAAClD,aAAa,CAACgD,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA9F,IAAIA,CAACiG,GAAQ;IACX,IAAI,CAACf,UAAU,EAAE;IACjB,IAAI,IAAI,CAACxC,KAAK,CAAC4C,aAAa,CAACrJ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACwG,OAAO,CAACyD,aAAa,CAAC,IAAI,CAACxD,KAAK,CAAC4C,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC1C,kBAAkB,CAACuD,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAACvH;KACZ,CAAC,CAACwH,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC9D,OAAO,CAAC+D,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACnC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAC5B,OAAO,CAACgE,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAAC/F,KAAK,EAAE;EACb;EAEArD,QAAQA,CAAC4I,IAAoB;IAC3B,IAAI,CAAC5G,eAAe,CAAC+G,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAACzF,KAAK,GAAG,KAAK;IAClB,IAAIwG,MAAM,CAACzC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAAC0C,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAAChE,kBAAkB,CAACiE,iCAAiC,CAAC;MACxDT,IAAI,EAAE;QACJR,cAAc,EAAE,IAAI,CAAC/G,eAAe,CAAC+G;;KAExC,CAAC,CAACS,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAAC7D,OAAO,CAAC+D,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACnC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAV,gBAAgBA,CAAA;IACd,IAAI,CAAChB,gBAAgB,CAACmE,qCAAqC,CAAC;MAAEV,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEW,IAAI,CAAClN,kBAAkB,CAAC,IAAI,CAACmJ,UAAU,CAAC,CAAC,CAACqD,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAClD,aAAa,GAAGkD,GAAG,CAACU,OAAQ;MACjC;MACA,IAAI,IAAI,CAAC5D,aAAa,CAACnH,MAAM,GAAG,CAAC,EAAE;QACjC,IAAI,CAACiH,yBAAyB,CAACkB,YAAY,GAAG,IAAI,CAAChB,aAAa,CAAC,CAAC,CAAC,CAACtI,GAAG;QACvE,IAAI,CAACuJ,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAACnB,yBAAyB,CAAC+D,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAAChE,yBAAyB,CAACiE,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAAC/D,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAACgE,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,IAAI,CAACnE,yBAAyB,CAACkB,YAAY,IAAI,IAAI,CAAClB,yBAAyB,CAACkB,YAAY,IAAI,CAAC,EAAE;MACnG,IAAI,CAACb,gBAAgB,GAAG,IAAI,CAACL,yBAAyB,CAACkB,YAAY;IACrE;IAEA,IAAI,CAACxB,kBAAkB,CAAC0E,8BAA8B,CAAC;MAAElB,IAAI,EAAE,IAAI,CAAClD;IAAyB,CAAE,CAAC,CAC7F6D,IAAI,EAAE,CACNV,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACU,OAAO,EAAE;UACf,IAAI,CAAC3D,eAAe,GAAGiD,GAAG,CAACU,OAAO;UAClC,IAAI,CAACK,YAAY,GAAGf,GAAG,CAACiB,UAAW;UAEnC;UACA,MAAMC,qBAAqB,GAAG,IAAI,CAACxL,aAAa,CAACC,MAAM;UACvD,IAAI,CAACD,aAAa,GAAG,IAAI,CAACA,aAAa,CAACyL,MAAM,CAACC,YAAY,IACzD,IAAI,CAACrE,eAAe,CAACsE,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAAChC,cAAc,KAAK8B,YAAY,CAAC9B,cAAc,CAAC,CAC/F;UAED;UACA,IAAI4B,qBAAqB,KAAK,IAAI,CAACxL,aAAa,CAACC,MAAM,IAAI,IAAI,CAACD,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;YAC1F,IAAI,CAACkG,cAAc,GAAG,EAAE;YACxB,IAAI,CAACsB,eAAe,GAAG,KAAK;UAC9B;UAEA;UACA,IAAI,CAACoE,oBAAoB,EAAE;QAC7B;MACF;IACF,CAAC,CAAC;EACN;EAEAhC,OAAOA,CAAA;IACL,IAAI,CAACjD,kBAAkB,CAACkF,8BAA8B,CAAC;MAAE1B,IAAI,EAAE,IAAI,CAACjD;IAAqB,CAAE,CAAC,CACzF4D,IAAI,EAAE,CACNV,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACU,OAAO,EAAE;UACf,IAAI,CAACnI,eAAe,GAAG;YAAEpB,UAAU,EAAE,EAAE;YAAE+B,OAAO,EAAE,KAAK;YAAEE,SAAS,EAAE;UAAK,CAAE;UAC3E,IAAI,CAACb,eAAe,CAACuF,YAAY,GAAGkC,GAAG,CAACU,OAAO,CAAC5C,YAAY;UAC5D,IAAI,CAACvF,eAAe,CAACvB,SAAS,GAAGgJ,GAAG,CAACU,OAAO,CAAC1J,SAAS;UACtD,IAAI,CAACuB,eAAe,CAACpB,UAAU,GAAG6I,GAAG,CAACU,OAAO,CAACvJ,UAAU,GAAI+G,KAAK,CAACC,OAAO,CAAC6B,GAAG,CAACU,OAAO,CAACvJ,UAAU,CAAC,GAAG6I,GAAG,CAACU,OAAO,CAACvJ,UAAU,GAAG,CAAC6I,GAAG,CAACU,OAAO,CAACvJ,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACoB,eAAe,CAACe,OAAO,GAAG0G,GAAG,CAACU,OAAO,CAACpH,OAAO;UAClD,IAAI,CAACf,eAAe,CAACtB,YAAY,GAAG+I,GAAG,CAACU,OAAO,CAACzJ,YAAY;UAC5D,IAAI,CAACsB,eAAe,CAAC+G,cAAc,GAAGU,GAAG,CAACU,OAAO,CAACpB,cAAc;UAChE,IAAI,CAAC/G,eAAe,CAACjB,KAAK,GAAG0I,GAAG,CAACU,OAAO,CAACpJ,KAAK;UAC9C,IAAI,CAACiB,eAAe,CAACf,OAAO,GAAGwI,GAAG,CAACU,OAAO,CAAClJ,OAAO;UAClD,IAAI,CAACe,eAAe,CAACZ,UAAU,GAAGqI,GAAG,CAACU,OAAO,CAAC/I,UAAU,IAAI,CAAC;UAC7D,IAAI,CAACY,eAAe,CAACS,KAAK,GAAGgH,GAAG,CAACU,OAAO,CAAC1H,KAAK;UAC9C,IAAI,CAACT,eAAe,CAACkJ,QAAQ,GAAGzB,GAAG,CAACU,OAAO,CAACe,QAAQ,IAAI,IAAI;UAC5D,IAAI,CAAClJ,eAAe,CAACW,OAAO,GAAG8G,GAAG,CAACU,OAAO,CAACxH,OAAO,IAAI,KAAK;UAC3D,IAAI,CAACX,eAAe,CAACa,SAAS,GAAG4G,GAAG,CAACU,OAAO,CAACtH,SAAS,IAAI,KAAK;QACjE;MACF;IACF,CAAC,CAAC;EACN;EAEAsI,iBAAiBA,CAAC7M,KAAa,EAAE8M,OAAY;IAC3ClC,OAAO,CAACC,GAAG,CAACiC,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAACpJ,eAAe,CAACpB,UAAU,EAAEyK,QAAQ,CAAC/M,KAAK,CAAC,EAAE;QACrD,IAAI,CAAC0D,eAAe,CAACpB,UAAU,EAAEuH,IAAI,CAAC7J,KAAK,CAAC;MAC9C;MACA4K,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnH,eAAe,CAACpB,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACoB,eAAe,CAACpB,UAAU,GAAG,IAAI,CAACoB,eAAe,CAACpB,UAAU,EAAEgK,MAAM,CAACU,CAAC,IAAIA,CAAC,KAAKhN,KAAK,CAAC;IAC7F;EACF;EAEA4C,cAAcA,CAAC0H,IAAS;IACtB,OAAOA,IAAI,CAACjG,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAEAxB,gBAAgBA,CAACyH,IAAS;IACxB,OAAOA,IAAI,CAAC/F,SAAS,GAAG,GAAG,GAAG,GAAG;EACnC;EAEA;EACA0I,sBAAsBA,CAACC,MAA2B;IAChDtC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEqC,MAAM,CAAC;IAEhC;IACA,IAAI,CAACC,2BAA2B,CAACD,MAAM,CAACrM,aAAa,EAAEqM,MAAM,CAACE,eAAe,CAAC;EAChF;EAEAC,eAAeA,CAACC,YAAoB;IAClC,IAAI,CAAChG,OAAO,CAACgE,YAAY,CAACgC,YAAY,CAAC;EACzC;EAEA;EACQH,2BAA2BA,CAACI,iBAAwB,EAAEH,eAAmC;IAC/F,IAAI,CAAC,IAAI,CAACrF,yBAAyB,CAACkB,YAAY,EAAE;MAChD,IAAI,CAAC3B,OAAO,CAACgE,YAAY,CAAC,WAAW,CAAC;MACtC;IACF;IAEA;IACA,IAAI,CAACzK,aAAa,GAAG,EAAE;IACvB,IAAI,CAACwH,aAAa,GAAG,KAAK;IAE1B;IACA,MAAMmF,OAAO,GAAG,IAAI,CAACtF,eAAe,CAACpH,MAAM,GAAG,CAAC,GAC3C2M,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAACxF,eAAe,CAACQ,GAAG,CAACiF,IAAI,IAAIA,IAAI,CAAClL,KAAK,IAAI,CAAC,CAAC,CAAC,GAC9D,CAAC;IAEL,IAAImL,gBAAgB,GAAG,CAAC;IACxB,MAAMC,iBAAiB,GAAU,EAAE;IAEnC;IACAN,iBAAiB,CAAC/D,OAAO,CAACsE,QAAQ,IAAG;MACnC,MAAMC,OAAO,GAAGX,eAAe,CAACY,GAAG,CAACF,QAAQ,CAACG,WAAW,CAAC,IAAI,EAAE;MAE/D,IAAIF,OAAO,IAAIA,OAAO,CAACjN,MAAM,GAAG,CAAC,EAAE;QACjC;QACAiN,OAAO,CAACvE,OAAO,CAAC,CAAC0E,MAAM,EAAEC,WAAW,KAAI;UACtC,MAAMC,aAAa,GAAG;YACpB3D,cAAc,EAAEP,SAAS;YAAE;YAC3BjB,YAAY,EAAE,IAAI,CAAClB,yBAAyB,CAACkB,YAAY;YACzD9G,SAAS,EAAE+L,MAAM,CAAC/L,SAAS,IAAI2L,QAAQ,CAAC3L,SAAS,IAAI,EAAE;YAAE;YACzDC,YAAY,EAAE8L,MAAM,CAACG,KAAK,IAAI,GAAGP,QAAQ,CAACQ,aAAa,SAASH,WAAW,GAAG,CAAC,EAAE;YAAE;YACnF7L,UAAU,EAAEwL,QAAQ,CAACxL,UAAU,IAAI,EAAE;YAAE;YACvCG,KAAK,EAAE+K,OAAO,GAAGI,gBAAgB,GAAG,CAAC;YAAE;YACvCjL,OAAO,EAAE,CAAC;YAAE;YACZG,UAAU,EAAGoL,MAAc,CAACpL,UAAU,IAAKgL,QAAgB,CAAChL,UAAU,IAAI,CAAC;YAAE;YAC7EqB,KAAK,EAAG+J,MAAc,CAAC/J,KAAK,IAAK2J,QAAgB,CAAC3J,KAAK,IAAI,GAAG;YAAE;YAChEyI,QAAQ,EAAEsB,MAAM,CAACK,UAAU,IAAKT,QAAgB,CAACU,SAAS,IAAI,IAAI;YAAE;YACpEnK,OAAO,EAAEyJ,QAAQ,CAACzJ,OAAO,KAAK6F,SAAS,GAAG4D,QAAQ,CAACzJ,OAAO,GAAG,KAAK;YAAE;YACpEE,SAAS,EAAEuJ,QAAQ,CAACvJ,SAAS,KAAK2F,SAAS,GAAG4D,QAAQ,CAACvJ,SAAS,GAAG,KAAK;YAAE;YAC1EE,OAAO,EAAGyJ,MAAc,CAACzJ,OAAO,IAAI,OAAOqJ,QAAQ,CAACQ,aAAa,UAAUJ,MAAM,CAACG,KAAK,KAAK,CAAC;WAC9F;UAEDR,iBAAiB,CAAChE,IAAI,CAACuE,aAAa,CAAC;UACrCR,gBAAgB,EAAE;QACpB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMQ,aAAa,GAAG;UACpB3D,cAAc,EAAEP,SAAS;UAAE;UAC3BjB,YAAY,EAAE,IAAI,CAAClB,yBAAyB,CAACkB,YAAY;UACzD9G,SAAS,EAAE2L,QAAQ,CAAC3L,SAAS,IAAI,EAAE;UAAE;UACrCC,YAAY,EAAE0L,QAAQ,CAACQ,aAAa,IAAI,QAAQV,gBAAgB,GAAG,CAAC,EAAE;UAAE;UACxEtL,UAAU,EAAEwL,QAAQ,CAACxL,UAAU,IAAI,EAAE;UAAE;UACvCG,KAAK,EAAE+K,OAAO,GAAGI,gBAAgB,GAAG,CAAC;UAAE;UACvCjL,OAAO,EAAE,CAAC;UAAE;UACZG,UAAU,EAAGgL,QAAgB,CAAChL,UAAU,IAAI,CAAC;UAAE;UAC/CqB,KAAK,EAAG2J,QAAgB,CAAC3J,KAAK,IAAI,GAAG;UAAE;UACvCyI,QAAQ,EAAGkB,QAAgB,CAACU,SAAS,IAAI,IAAI;UAAE;UAC/CnK,OAAO,EAAEyJ,QAAQ,CAACzJ,OAAO,KAAK6F,SAAS,GAAG4D,QAAQ,CAACzJ,OAAO,GAAG,KAAK;UAAE;UACpEE,SAAS,EAAEuJ,QAAQ,CAACvJ,SAAS,KAAK2F,SAAS,GAAG4D,QAAQ,CAACvJ,SAAS,GAAG,KAAK;UAAE;UAC1EE,OAAO,EAAGqJ,QAAgB,CAACrJ,OAAO,IAAI,OAAOqJ,QAAQ,CAACQ,aAAa,KAAK,CAAC;SAC1E;QAEDT,iBAAiB,CAAChE,IAAI,CAACuE,aAAa,CAAC;QACrCR,gBAAgB,EAAE;MACpB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAC5G,cAAc,GAAG6G,iBAAiB;IAEvC;IACA,IAAI,CAACvF,eAAe,GAAG,IAAI;IAE3B;IACA,MAAMmG,gBAAgB,GAAGlB,iBAAiB,CAACmB,MAAM,CAAC,CAACC,GAAG,EAAEb,QAAQ,KAAI;MAClE,MAAMC,OAAO,GAAGX,eAAe,CAACY,GAAG,CAACF,QAAQ,CAACG,WAAW,CAAC,IAAI,EAAE;MAC/D,OAAOU,GAAG,IAAIZ,OAAO,CAACjN,MAAM,IAAI,CAAC,CAAC;IACpC,CAAC,EAAE,CAAC,CAAC;IAEL;IACA,MAAMwG,OAAO,GAAG,OAAOiG,iBAAiB,CAACzM,MAAM,UAAU2N,gBAAgB,kBAAkB,GACzF,oBAAoB,GACpB,eAAe,GACf,aAAa,GACb,WAAW,GACX,mBAAmB,GACnB,yBAAyB;IAE3BG,KAAK,CAACtH,OAAO,CAAC;IAEd;IACA0B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC3B,aAAa,CAACgD,IAAI,CAAC,IAAI,CAACwE,eAAe,CAAC;IAC/C,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAE;EAEH;EACAhN,mBAAmBA,CAAC8L,IAAoB;IACtC,MAAMlI,KAAK,GAAG,IAAI,CAAC5E,aAAa,CAACiO,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACtE,cAAc,KAAKkD,IAAI,CAAClD,cAAc,CAAC;IACvG,IAAIhF,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC5E,aAAa,CAACmO,MAAM,CAACvJ,KAAK,EAAE,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,IAAI,CAAC5E,aAAa,CAACgJ,IAAI,CAAC8D,IAAI,CAAC;IAC/B;IACA,IAAI,CAACjB,oBAAoB,EAAE;EAC7B;EAEA;EACAuC,eAAeA,CAACC,QAAiB;IAC/B,IAAI,IAAI,CAAChH,eAAe,CAACpH,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACD,aAAa,GAAG,EAAE;MACvB,IAAI,CAACwH,aAAa,GAAG,KAAK;MAC1B;IACF;IAEA;IACA,IAAI,CAACA,aAAa,GAAG6G,QAAQ;IAE7B;IACA,IAAI,IAAI,CAAC7G,aAAa,EAAE;MACtB,IAAI,CAACxH,aAAa,GAAG,CAAC,GAAG,IAAI,CAACqH,eAAe,CAAC;IAChD,CAAC,MAAM;MACL,IAAI,CAACrH,aAAa,GAAG,EAAE;IACzB;EACF;EAEA;EACA6L,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACxE,eAAe,CAACpH,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACuH,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACA,aAAa,GAAG,IAAI,CAACxH,aAAa,CAACC,MAAM,KAAK,IAAI,CAACoH,eAAe,CAACpH,MAAM;IAChF;EACF;EAEA;EACAmB,cAAcA,CAAC0L,IAAoB;IACjC,OAAO,IAAI,CAAC9M,aAAa,CAAC2L,IAAI,CAACuC,QAAQ,IAAIA,QAAQ,CAACtE,cAAc,KAAKkD,IAAI,CAAClD,cAAc,CAAC;EAC7F;EAEA;EACA9J,aAAaA,CAACyJ,MAAwB;IACpC,IAAI,IAAI,CAACvJ,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAACwG,OAAO,CAACgE,YAAY,CAAC,YAAY,CAAC;MACvC;IACF;IAEA,IAAI,CAAChD,eAAe,GAAG,IAAI;IAE3B;IACA,IAAI,CAACtB,cAAc,GAAG,IAAI,CAACnG,aAAa,CAAC6H,GAAG,CAACiF,IAAI,KAAK;MACpDlD,cAAc,EAAEkD,IAAI,CAAClD,cAAc;MACnCxB,YAAY,EAAE0E,IAAI,CAAC1E,YAAY;MAC/B9G,SAAS,EAAEwL,IAAI,CAACxL,SAAS;MACzBC,YAAY,EAAEuL,IAAI,CAACvL,YAAY;MAC/BE,UAAU,EAAEqL,IAAI,CAACrL,UAAU,GAAG,CAAC,GAAGqL,IAAI,CAACrL,UAAU,CAAC,GAAG,EAAE;MACvDG,KAAK,EAAEkL,IAAI,CAAClL,KAAK;MACjBE,OAAO,EAAEgL,IAAI,CAAChL,OAAO;MACrBG,UAAU,EAAE6K,IAAI,CAAC7K,UAAU,IAAI,CAAC;MAChCqB,KAAK,EAAEwJ,IAAI,CAACxJ,KAAK;MACjByI,QAAQ,EAAEe,IAAI,CAACf,QAAQ,IAAI,IAAI;MAC/BvI,OAAO,EAAEsJ,IAAI,CAACtJ,OAAO,IAAI,KAAK;MAC9BE,SAAS,EAAEoJ,IAAI,CAACpJ,SAAS,IAAI,KAAK;MAClCE,OAAO,EAAEkJ,IAAI,CAAClJ;KACf,CAAC,CAAC;IAEH,IAAI,CAAC4C,aAAa,CAACgD,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACA+E,eAAeA,CAAA;IACb,MAAMhF,aAAa,GAAa,EAAE;IAElC,IAAI,CAACnD,cAAc,CAACwC,OAAO,CAAC,CAACmE,IAAI,EAAElI,KAAK,KAAI;MAC1C,MAAM2J,OAAO,GAAG3J,KAAK,GAAG,CAAC;MAEzB;MACA,IAAI,CAACkI,IAAI,CAAC1E,YAAY,EAAE;QACtBkB,aAAa,CAACN,IAAI,CAAC,OAAOuF,OAAO,aAAa,CAAC;MACjD;MAEA,IAAI,CAACzB,IAAI,CAACvL,YAAY,IAAIuL,IAAI,CAACvL,YAAY,CAACiN,IAAI,EAAE,KAAK,EAAE,EAAE;QACzDlF,aAAa,CAACN,IAAI,CAAC,OAAOuF,OAAO,aAAa,CAAC;MACjD;MAEA,IAAI,CAACzB,IAAI,CAACrL,UAAU,IAAIqL,IAAI,CAACrL,UAAU,CAACxB,MAAM,KAAK,CAAC,EAAE;QACpDqJ,aAAa,CAACN,IAAI,CAAC,OAAOuF,OAAO,WAAW,CAAC;MAC/C;MAEA,IAAIzB,IAAI,CAAClL,KAAK,KAAK,IAAI,IAAIkL,IAAI,CAAClL,KAAK,KAAKyH,SAAS,IAAIyD,IAAI,CAAClL,KAAK,GAAG,CAAC,EAAE;QACrE0H,aAAa,CAACN,IAAI,CAAC,OAAOuF,OAAO,iBAAiB,CAAC;MACrD;MAEA,IAAIzB,IAAI,CAAChL,OAAO,KAAK,IAAI,IAAIgL,IAAI,CAAChL,OAAO,KAAKuH,SAAS,EAAE;QACvDC,aAAa,CAACN,IAAI,CAAC,OAAOuF,OAAO,WAAW,CAAC;MAC/C;MAEA,IAAIzB,IAAI,CAAC7K,UAAU,KAAK,IAAI,IAAI6K,IAAI,CAAC7K,UAAU,KAAKoH,SAAS,IAAIyD,IAAI,CAAC7K,UAAU,GAAG,CAAC,EAAE;QACpFqH,aAAa,CAACN,IAAI,CAAC,OAAOuF,OAAO,iBAAiB,CAAC;MACrD;MAEA,IAAI,CAACzB,IAAI,CAACxJ,KAAK,IAAIwJ,IAAI,CAACxJ,KAAK,CAACkL,IAAI,EAAE,KAAK,EAAE,EAAE;QAC3ClF,aAAa,CAACN,IAAI,CAAC,OAAOuF,OAAO,WAAW,CAAC;MAC/C;MAEA;MACA,IAAIzB,IAAI,CAACxL,SAAS,IAAIwL,IAAI,CAACxL,SAAS,CAACrB,MAAM,GAAG,EAAE,EAAE;QAChDqJ,aAAa,CAACN,IAAI,CAAC,OAAOuF,OAAO,cAAc,CAAC;MAClD;MAEA,IAAIzB,IAAI,CAACvL,YAAY,IAAIuL,IAAI,CAACvL,YAAY,CAACtB,MAAM,GAAG,EAAE,EAAE;QACtDqJ,aAAa,CAACN,IAAI,CAAC,OAAOuF,OAAO,gBAAgB,CAAC;MACpD;MAEA,IAAIzB,IAAI,CAAClJ,OAAO,IAAIkJ,IAAI,CAAClJ,OAAO,CAAC3D,MAAM,GAAG,GAAG,EAAE;QAC7CqJ,aAAa,CAACN,IAAI,CAAC,OAAOuF,OAAO,iBAAiB,CAAC;MACrD;IACF,CAAC,CAAC;IAEF,OAAOjF,aAAa;EACtB;EAEA;EACAtD,SAASA,CAACiE,GAAQ;IAChB,IAAI,IAAI,CAAC9D,cAAc,CAAClG,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAACwG,OAAO,CAACgE,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;IAEA;IACA,MAAMgE,gBAAgB,GAAG,IAAI,CAACH,eAAe,EAAE;IAC/C,IAAIG,gBAAgB,CAACxO,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACwG,OAAO,CAACyD,aAAa,CAACuE,gBAAgB,CAAC;MAC5C;IACF;IAEA;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACvI,cAAc,CAACsF,MAAM,CAACqB,IAAI,IAAI,CAACA,IAAI,CAAClD,cAAc,CAAC;IACzE,MAAM+E,WAAW,GAAG,IAAI,CAACxI,cAAc,CAACsF,MAAM,CAACqB,IAAI,IAAIA,IAAI,CAAClD,cAAc,CAAC;IAE3E;IACA,MAAMgF,QAAQ,GAAmB,EAAE;IAEnC;IACAF,QAAQ,CAAC/F,OAAO,CAACmE,IAAI,IAAG;MACtB,MAAM+B,WAAW,GAAwB;QACvCzG,YAAY,EAAE0E,IAAI,CAAC1E,YAAY;QAC/B9G,SAAS,EAAEwL,IAAI,CAACxL,SAAS;QACzBC,YAAY,EAAEuL,IAAI,CAACvL,YAAY;QAC/BE,UAAU,EAAEqL,IAAI,CAACrL,UAAU;QAC3BG,KAAK,EAAEkL,IAAI,CAAClL,KAAK;QACjBE,OAAO,EAAEgL,IAAI,CAAChL,OAAO;QACrBG,UAAU,EAAE6K,IAAI,CAAC7K,UAAU;QAC3BqB,KAAK,EAAEwJ,IAAI,CAACxJ,KAAK;QACjByI,QAAQ,EAAEe,IAAI,CAACf,QAAQ;QACvBvI,OAAO,EAAEsJ,IAAI,CAACtJ,OAAO;QACrBE,SAAS,EAAEoJ,IAAI,CAACpJ,SAAS;QACzBE,OAAO,EAAEkJ,IAAI,CAAClJ;OACf;MAEDgL,QAAQ,CAAC5F,IAAI,CACX,IAAI,CAACpC,kBAAkB,CAACuD,+BAA+B,CAAC;QACtDC,IAAI,EAAEyE;OACP,CAAC,CAACC,SAAS,EAAE,CACf;IACH,CAAC,CAAC;IAEF;IACA,IAAIH,WAAW,CAAC1O,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAM8O,UAAU,GAA0BJ,WAAW,CAAC9G,GAAG,CAACiF,IAAI,KAAK;QACjElD,cAAc,EAAEkD,IAAI,CAAClD,cAAc;QACnCxB,YAAY,EAAE0E,IAAI,CAAC1E,YAAY;QAC/B9G,SAAS,EAAEwL,IAAI,CAACxL,SAAS;QACzBC,YAAY,EAAEuL,IAAI,CAACvL,YAAY;QAC/BE,UAAU,EAAEqL,IAAI,CAACrL,UAAU;QAC3BG,KAAK,EAAEkL,IAAI,CAAClL,KAAK;QACjBE,OAAO,EAAEgL,IAAI,CAAChL,OAAO;QACrBG,UAAU,EAAE6K,IAAI,CAAC7K,UAAU;QAC3BqB,KAAK,EAAEwJ,IAAI,CAACxJ,KAAK;QACjByI,QAAQ,EAAEe,IAAI,CAACf,QAAQ;QACvBvI,OAAO,EAAEsJ,IAAI,CAACtJ,OAAO;QACrBE,SAAS,EAAEoJ,IAAI,CAACpJ,SAAS;QACzBE,OAAO,EAAEkJ,IAAI,CAAClJ;OACf,CAAC,CAAC;MAEHgL,QAAQ,CAAC5F,IAAI,CACX,IAAI,CAACpC,kBAAkB,CAACoI,oCAAoC,CAAC;QAC3D5E,IAAI,EAAE2E;OACP,CAAC,CAACD,SAAS,EAAE,CACf;IACH;IAEA;IACAG,OAAO,CAACC,GAAG,CAACN,QAAQ,CAAC,CAClBO,IAAI,CAACC,SAAS,IAAG;MAChB,MAAMC,YAAY,GAAGD,SAAS,CAAC3D,MAAM,CAACnB,GAAG,IAAIA,GAAG,EAAEC,UAAU,KAAK,CAAC,CAAC,CAACtK,MAAM;MAC1E,MAAMqP,UAAU,GAAG,IAAI,CAACnJ,cAAc,CAAClG,MAAM;MAE7C,IAAIoP,YAAY,KAAKD,SAAS,CAACnP,MAAM,EAAE;QACrC,IAAI,CAACwG,OAAO,CAAC+D,aAAa,CAAC,QAAQ8E,UAAU,aAAaZ,QAAQ,CAACzO,MAAM,SAAS0O,WAAW,CAAC1O,MAAM,GAAG,CAAC;MAC1G,CAAC,MAAM;QACL,IAAI,CAACwG,OAAO,CAAC+D,aAAa,CAAC,QAAQ6E,YAAY,QAAQD,SAAS,CAACnP,MAAM,GAAGoP,YAAY,MAAM,CAAC;MAC/F;MAEA;MACA,IAAI,CAACrP,aAAa,GAAG,EAAE;MACvB,IAAI,CAACmG,cAAc,GAAG,EAAE;MACxB,IAAI,CAACsB,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACoE,oBAAoB,EAAE;MAC3B,IAAI,CAACxD,OAAO,EAAE;MACd4B,GAAG,CAAC/F,KAAK,EAAE;IACb,CAAC,CAAC,CACDqL,KAAK,CAACzF,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,IAAI,CAACrD,OAAO,CAACgE,YAAY,CAAC,WAAW,CAAC;IACxC,CAAC,CAAC;EACN;EAEA;EACA5F,kBAAkBA,CAACD,KAAa;IAC9B,MAAM4K,YAAY,GAAG,IAAI,CAACxP,aAAa,CAAC4E,KAAK,CAAC;IAC9C,IAAI4K,YAAY,EAAE;MAChB,IAAI,CAACrJ,cAAc,CAACvB,KAAK,CAAC,GAAG;QAC3BgF,cAAc,EAAE4F,YAAY,CAAC5F,cAAc;QAC3CxB,YAAY,EAAEoH,YAAY,CAACpH,YAAY;QACvC9G,SAAS,EAAEkO,YAAY,CAAClO,SAAS;QACjCC,YAAY,EAAEiO,YAAY,CAACjO,YAAY;QACvCE,UAAU,EAAE+N,YAAY,CAAC/N,UAAU,GAAG,CAAC,GAAG+N,YAAY,CAAC/N,UAAU,CAAC,GAAG,EAAE;QACvEG,KAAK,EAAE4N,YAAY,CAAC5N,KAAK;QACzBE,OAAO,EAAE0N,YAAY,CAAC1N,OAAO;QAC7BG,UAAU,EAAEuN,YAAY,CAACvN,UAAU,IAAI,CAAC;QACxCqB,KAAK,EAAEkM,YAAY,CAAClM,KAAK;QACzByI,QAAQ,EAAEyD,YAAY,CAACzD,QAAQ,IAAI,IAAI;QACvCvI,OAAO,EAAEgM,YAAY,CAAChM,OAAO,IAAI,KAAK;QACtCE,SAAS,EAAE8L,YAAY,CAAC9L,SAAS,IAAI,KAAK;QAC1CE,OAAO,EAAE4L,YAAY,CAAC5L;OACvB;IACH;EACF;EAEA;EACAsC,eAAeA,CAAC+D,GAAQ;IACtB,IAAI,CAACxC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACtB,cAAc,GAAG,EAAE;IACxB8D,GAAG,CAAC/F,KAAK,EAAE;EACb;EAEA;EACQuL,uBAAuBA,CAACC,YAAmC;IACjE,MAAMC,aAAa,GAAGD,YAAY,CAAC7H,GAAG,CAAC+H,WAAW,IAChD,IAAI,CAAChJ,kBAAkB,CAACuD,+BAA+B,CAAC;MACtDC,IAAI,EAAEwF;KACP,CAAC,CACH;IAEDX,OAAO,CAACC,GAAG,CAACS,aAAa,CAAC9H,GAAG,CAACgI,GAAG,IAAIA,GAAG,CAACf,SAAS,EAAE,CAAC,CAAC,CACnDK,IAAI,CAACC,SAAS,IAAG;MAChB,MAAMC,YAAY,GAAGD,SAAS,CAAC3D,MAAM,CAACnB,GAAG,IAAIA,GAAG,EAAEC,UAAU,KAAK,CAAC,CAAC,CAACtK,MAAM;MAC1E,IAAI,CAACwG,OAAO,CAAC+D,aAAa,CAAC,QAAQ6E,YAAY,QAAQ,CAAC;MACxD,IAAI,CAAChH,OAAO,EAAE;IAChB,CAAC,CAAC,CACDkH,KAAK,CAACzF,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAI,CAACrD,OAAO,CAACgE,YAAY,CAAC,aAAa,CAAC;IAC1C,CAAC,CAAC;EACN;;;uCAhsBWrE,8BAA8B,EAAA5H,EAAA,CAAAsR,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxR,EAAA,CAAAsR,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA1R,EAAA,CAAAsR,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA5R,EAAA,CAAAsR,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA9R,EAAA,CAAAsR,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAhS,EAAA,CAAAsR,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAAlS,EAAA,CAAAsR,iBAAA,CAAAW,EAAA,CAAAE,kBAAA,GAAAnS,EAAA,CAAAsR,iBAAA,CAAAW,EAAA,CAAAG,eAAA,GAAApS,EAAA,CAAAsR,iBAAA,CAAAe,EAAA,CAAAC,aAAA,GAAAtS,EAAA,CAAAsR,iBAAA,CAAAiB,EAAA,CAAAC,MAAA,GAAAxS,EAAA,CAAAsR,iBAAA,CAAAtR,EAAA,CAAAyS,UAAA,GAAAzS,EAAA,CAAAsR,iBAAA,CAAAoB,EAAA,CAAAC,4BAAA;IAAA;EAAA;;;YAA9B/K,8BAA8B;MAAAgL,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC7CzC/S,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAuB,SAAA,qBAAiC;UACnCvB,EAAA,CAAAG,YAAA,EAAiB;UAOTH,EAJR,CAAAC,cAAA,sBAAoC,aACd,aACD,aACyB,eACI;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,mBAC8C;UADnCD,EAAA,CAAAgE,gBAAA,2BAAAiP,2EAAA/O,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAAlT,EAAA,CAAAoE,kBAAA,CAAA4O,GAAA,CAAAtK,yBAAA,CAAAkB,YAAA,EAAA1F,MAAA,MAAA8O,GAAA,CAAAtK,yBAAA,CAAAkB,YAAA,GAAA1F,MAAA;YAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;UAAA,EAAoD;UAC7DlE,EAAA,CAAAa,UAAA,2BAAAoS,2EAAA/O,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAA,OAAAlT,EAAA,CAAAqB,WAAA,CAAiB2R,GAAA,CAAAzJ,iBAAA,CAAArF,MAAA,CAAyB;UAAA,EAAC;UAC3ClE,EAAA,CAAAyC,UAAA,KAAA0Q,oDAAA,uBAAiE;UAIrEnT,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,iBACoD;UAAlDD,EAAA,CAAAgE,gBAAA,2BAAAoP,wEAAAlP,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAAlT,EAAA,CAAAoE,kBAAA,CAAA4O,GAAA,CAAAtK,yBAAA,CAAA5F,SAAA,EAAAoB,MAAA,MAAA8O,GAAA,CAAAtK,yBAAA,CAAA5F,SAAA,GAAAoB,MAAA;YAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;UAAA,EAAiD;UACrDlE,EAFE,CAAAG,YAAA,EACoD,EAChD;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACM;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,iBACuD;UAArDD,EAAA,CAAAgE,gBAAA,2BAAAqP,wEAAAnP,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAAlT,EAAA,CAAAoE,kBAAA,CAAA4O,GAAA,CAAAtK,yBAAA,CAAA3F,YAAA,EAAAmB,MAAA,MAAA8O,GAAA,CAAAtK,yBAAA,CAAA3F,YAAA,GAAAmB,MAAA;YAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;UAAA,EAAoD;UAE1DlE,EAHI,CAAAG,YAAA,EACuD,EACnD,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBAAqF;UAA1ED,EAAA,CAAAgE,gBAAA,2BAAAsP,4EAAApP,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAAlT,EAAA,CAAAoE,kBAAA,CAAA4O,GAAA,CAAAtK,yBAAA,CAAAzF,UAAA,EAAAiB,MAAA,MAAA8O,GAAA,CAAAtK,yBAAA,CAAAzF,UAAA,GAAAiB,MAAA;YAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;UAAA,EAAkD;UAC3DlE,EAAA,CAAAyC,UAAA,KAAA8Q,oDAAA,uBAA+D;UAInEvT,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,oBAAyE;UAA9DD,EAAA,CAAAgE,gBAAA,2BAAAwP,4EAAAtP,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAAlT,EAAA,CAAAoE,kBAAA,CAAA4O,GAAA,CAAAtK,yBAAA,CAAApF,OAAA,EAAAY,MAAA,MAAA8O,GAAA,CAAAtK,yBAAA,CAAApF,OAAA,GAAAY,MAAA;YAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;UAAA,EAA+C;UACxDlE,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAE7BF,EAF6B,CAAAG,YAAA,EAAY,EAC3B,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,qBAA0F;UAA/ED,EAAA,CAAAgE,gBAAA,2BAAAyP,4EAAAvP,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAAlT,EAAA,CAAAoE,kBAAA,CAAA4O,GAAA,CAAAtK,yBAAA,CAAA1D,OAAA,EAAAd,MAAA,MAAA8O,GAAA,CAAAtK,yBAAA,CAAA1D,OAAA,GAAAd,MAAA;YAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;UAAA,EAA+C;UACxDlE,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAGlCF,EAHkC,CAAAG,YAAA,EAAY,EAC9B,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACG;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,qBAA4F;UAAjFD,EAAA,CAAAgE,gBAAA,2BAAA0P,4EAAAxP,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAAlT,EAAA,CAAAoE,kBAAA,CAAA4O,GAAA,CAAAtK,yBAAA,CAAAxD,SAAA,EAAAhB,MAAA,MAAA8O,GAAA,CAAAtK,yBAAA,CAAAxD,SAAA,GAAAhB,MAAA;YAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;UAAA,EAAiD;UAC1DlE,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAEhCF,EAFgC,CAAAG,YAAA,EAAY,EAC9B,EACR;UACNH,EAAA,CAAAuB,SAAA,cAA8C;UAChDvB,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACfD,EAAA,CAAAuB,SAAA,eAA4B;UAE1BvB,EADF,CAAAC,cAAA,eAAmD,kBACc;UAAxBD,EAAA,CAAAa,UAAA,mBAAA8S,iEAAA;YAAA3T,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAA,OAAAlT,EAAA,CAAAqB,WAAA,CAAS2R,GAAA,CAAAlJ,WAAA,EAAa;UAAA,EAAC;UAAC9J,EAAA,CAAAuB,SAAA,aAAgC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1GH,EAAA,CAAAC,cAAA,kBAAsD;UAApBD,EAAA,CAAAa,UAAA,mBAAA+S,iEAAA;YAAA5T,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAA,OAAAlT,EAAA,CAAAqB,WAAA,CAAS2R,GAAA,CAAAnJ,OAAA,EAAS;UAAA,EAAC;UAAC7J,EAAA,CAAAuB,SAAA,aAAkC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnGH,EAAA,CAAAC,cAAA,8CAGoC;UAAlCD,EADqD,CAAAa,UAAA,6BAAAgT,uGAAA3P,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAA,OAAAlT,EAAA,CAAAqB,WAAA,CAAmB2R,GAAA,CAAApF,sBAAA,CAAA1J,MAAA,CAA8B;UAAA,EAAC,mBAAA4P,6FAAA5P,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAA,OAAAlT,EAAA,CAAAqB,WAAA,CAC9F2R,GAAA,CAAAhF,eAAA,CAAA9J,MAAA,CAAuB;UAAA,EAAC;UACnClE,EAAA,CAAAG,YAAA,EAAqC;UAGrCH,EAFA,CAAAyC,UAAA,KAAAsR,iDAAA,qBACmC,KAAAC,iDAAA,qBACyC;UAKpFhU,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;UAUDH,EAPd,CAAAC,cAAA,uBAAoC,eACT,eACO,iBACmE,aACtF,cAC4D,cACiB,uBACG;UAA1CD,EAAA,CAAAa,UAAA,2BAAAoT,8EAAA/P,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAA,OAAAlT,EAAA,CAAAqB,WAAA,CAAiB2R,GAAA,CAAApD,eAAA,CAAA1L,MAAA,CAAuB;UAAA,EAAC;UAChFlE,EAAA,CAAAG,YAAA,EAAc;UACdH,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UACnCF,EADmC,CAAAG,YAAA,EAAQ,EACtC;UACLH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAyC,UAAA,KAAAyR,6CAAA,mBAAuE;UAsB7ElU,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,0BAC2B;UADqBD,EAAA,CAAAgE,gBAAA,wBAAAmQ,8EAAAjQ,MAAA;YAAAlE,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAAlT,EAAA,CAAAoE,kBAAA,CAAA4O,GAAA,CAAApG,SAAA,EAAA1I,MAAA,MAAA8O,GAAA,CAAApG,SAAA,GAAA1I,MAAA;YAAA,OAAAlE,EAAA,CAAAqB,WAAA,CAAA6C,MAAA;UAAA,EAAoB;UAClElE,EAAA,CAAAa,UAAA,wBAAAsT,8EAAA;YAAAnU,EAAA,CAAAe,aAAA,CAAAmS,GAAA;YAAA,OAAAlT,EAAA,CAAAqB,WAAA,CAAc2R,GAAA,CAAAnJ,OAAA,EAAS;UAAA,EAAC;UAIhC7J,EAHM,CAAAG,YAAA,EAAiB,EACb,EACO,EACP;UA6EVH,EA1EA,CAAAyC,UAAA,MAAA2R,uDAAA,kCAAApU,EAAA,CAAAqU,sBAAA,CAAkD,MAAAC,uDAAA,iCAAAtU,EAAA,CAAAqU,sBAAA,CA0ES;;;UA3MtCrU,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAA4F,gBAAA,YAAAoN,GAAA,CAAAtK,yBAAA,CAAAkB,YAAA,CAAoD;UAEjC5J,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAA4S,GAAA,CAAApK,aAAA,CAAgB;UAQ5C5I,EAAA,CAAAO,SAAA,GAAiD;UAAjDP,EAAA,CAAA4F,gBAAA,YAAAoN,GAAA,CAAAtK,yBAAA,CAAA5F,SAAA,CAAiD;UAKjD9C,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAA4F,gBAAA,YAAAoN,GAAA,CAAAtK,yBAAA,CAAA3F,YAAA,CAAoD;UAM3C/C,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAA4F,gBAAA,YAAAoN,GAAA,CAAAtK,yBAAA,CAAAzF,UAAA,CAAkD;UAC/BjD,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,YAAA4S,GAAA,CAAAnN,SAAA,CAAY;UAO/B7F,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAA4F,gBAAA,YAAAoN,GAAA,CAAAtK,yBAAA,CAAApF,OAAA,CAA+C;UAC7CtD,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACZJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UACXJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UAKbJ,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAA4F,gBAAA,YAAAoN,GAAA,CAAAtK,yBAAA,CAAA1D,OAAA,CAA+C;UAC7ChF,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UAOjBJ,EAAA,CAAAO,SAAA,GAAiD;UAAjDP,EAAA,CAAA4F,gBAAA,YAAAoN,GAAA,CAAAtK,yBAAA,CAAAxD,SAAA,CAAiD;UAC/ClF,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UAUQJ,EAAA,CAAAO,SAAA,IAAwE;UAE1GP,EAFkC,CAAAI,UAAA,iBAAA4S,GAAA,CAAAtK,yBAAA,CAAAkB,YAAA,kBAAAoJ,GAAA,CAAAtK,yBAAA,CAAAkB,YAAA,CAAA2K,QAAA,UAAwE,oCAC3F,8BAA8B,uCAAuC,cAAAvB,GAAA,CAAAtK,yBAAA,CAAAkB,YAAA,CAChC;UAInD5J,EAAA,CAAAO,SAAA,EAA8B;UAA9BP,EAAA,CAAAI,UAAA,SAAA4S,GAAA,CAAAxR,aAAA,CAAAC,MAAA,KAA8B;UAC2BzB,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,SAAA4S,GAAA,CAAAwB,QAAA,CAAc;UAevDxU,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAI,UAAA,YAAA4S,GAAA,CAAAhK,aAAA,CAAyB;UAgBrBhJ,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAA4S,GAAA,CAAAnK,eAAA,CAAoB;UAuB/B7I,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAA4S,GAAA,CAAAnG,YAAA,CAA+B;UAAC7M,EAAA,CAAA4F,gBAAA,SAAAoN,GAAA,CAAApG,SAAA,CAAoB;UAAC5M,EAAA,CAAAI,UAAA,aAAA4S,GAAA,CAAAtG,QAAA,CAAqB;;;qBDxG5F3N,YAAY,EAAA4S,EAAA,CAAA8C,eAAA,EAAA9C,EAAA,CAAA+C,mBAAA,EAAA/C,EAAA,CAAAgD,qBAAA,EAAAhD,EAAA,CAAAiD,qBAAA,EACZtV,mBAAmB,EACnBL,aAAa,EAAA0S,EAAA,CAAAkD,gBAAA,EACbtV,WAAW,EAAAuV,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,kBAAA,EAAAJ,GAAA,CAAAK,YAAA,EAAAL,GAAA,CAAAM,OAAA,EACXjW,cAAc,EAAAwS,EAAA,CAAA0D,iBAAA,EAAA1D,EAAA,CAAA2D,iBAAA,EACdpW,cAAc,EACdO,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVX,gBAAgB,EAAA2S,EAAA,CAAA4D,mBAAA,EAChB3V,kBAAkB,EAClBC,oBAAoB,EACpBE,oCAAoC;MAAAyV,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}