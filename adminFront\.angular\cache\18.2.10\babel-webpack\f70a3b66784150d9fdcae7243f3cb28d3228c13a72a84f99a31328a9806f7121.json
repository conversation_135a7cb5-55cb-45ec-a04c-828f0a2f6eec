{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport * as echarts from '../../core/echarts.js';\nimport { noop } from 'zrender/lib/core/util.js';\nvar ATTR = '\\0_ec_interaction_mutex';\nexport function take(zr, resourceKey, userKey) {\n  var store = getStore(zr);\n  store[resourceKey] = userKey;\n}\nexport function release(zr, resourceKey, userKey) {\n  var store = getStore(zr);\n  var uKey = store[resourceKey];\n  if (uKey === userKey) {\n    store[resourceKey] = null;\n  }\n}\nexport function isTaken(zr, resourceKey) {\n  return !!getStore(zr)[resourceKey];\n}\nfunction getStore(zr) {\n  return zr[ATTR] || (zr[ATTR] = {});\n}\n/**\r\n * payload: {\r\n *     type: 'takeGlobalCursor',\r\n *     key: 'dataZoomSelect', or 'brush', or ...,\r\n *         If no userKey, release global cursor.\r\n * }\r\n */\n// TODO: SELF REGISTERED.\necharts.registerAction({\n  type: 'takeGlobalCursor',\n  event: 'globalCursorTaken',\n  update: 'update'\n}, noop);", "map": {"version": 3, "names": ["echarts", "noop", "ATTR", "take", "zr", "resourceKey", "<PERSON><PERSON><PERSON>", "store", "getStore", "release", "u<PERSON>ey", "isTaken", "registerAction", "type", "event", "update"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/component/helper/interactionMutex.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport * as echarts from '../../core/echarts.js';\nimport { noop } from 'zrender/lib/core/util.js';\nvar ATTR = '\\0_ec_interaction_mutex';\nexport function take(zr, resourceKey, userKey) {\n  var store = getStore(zr);\n  store[resourceKey] = userKey;\n}\nexport function release(zr, resourceKey, userKey) {\n  var store = getStore(zr);\n  var uKey = store[resourceKey];\n  if (uKey === userKey) {\n    store[resourceKey] = null;\n  }\n}\nexport function isTaken(zr, resourceKey) {\n  return !!getStore(zr)[resourceKey];\n}\nfunction getStore(zr) {\n  return zr[ATTR] || (zr[ATTR] = {});\n}\n/**\r\n * payload: {\r\n *     type: 'takeGlobalCursor',\r\n *     key: 'dataZoomSelect', or 'brush', or ...,\r\n *         If no userKey, release global cursor.\r\n * }\r\n */\n// TODO: SELF REGISTERED.\necharts.registerAction({\n  type: 'takeGlobalCursor',\n  event: 'globalCursorTaken',\n  update: 'update'\n}, noop);"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,uBAAuB;AAChD,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,IAAIC,IAAI,GAAG,yBAAyB;AACpC,OAAO,SAASC,IAAIA,CAACC,EAAE,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC7C,IAAIC,KAAK,GAAGC,QAAQ,CAACJ,EAAE,CAAC;EACxBG,KAAK,CAACF,WAAW,CAAC,GAAGC,OAAO;AAC9B;AACA,OAAO,SAASG,OAAOA,CAACL,EAAE,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAChD,IAAIC,KAAK,GAAGC,QAAQ,CAACJ,EAAE,CAAC;EACxB,IAAIM,IAAI,GAAGH,KAAK,CAACF,WAAW,CAAC;EAC7B,IAAIK,IAAI,KAAKJ,OAAO,EAAE;IACpBC,KAAK,CAACF,WAAW,CAAC,GAAG,IAAI;EAC3B;AACF;AACA,OAAO,SAASM,OAAOA,CAACP,EAAE,EAAEC,WAAW,EAAE;EACvC,OAAO,CAAC,CAACG,QAAQ,CAACJ,EAAE,CAAC,CAACC,WAAW,CAAC;AACpC;AACA,SAASG,QAAQA,CAACJ,EAAE,EAAE;EACpB,OAAOA,EAAE,CAACF,IAAI,CAAC,KAAKE,EAAE,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,OAAO,CAACY,cAAc,CAAC;EACrBC,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE,mBAAmB;EAC1BC,MAAM,EAAE;AACV,CAAC,EAAEd,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}