{"ast": null, "code": "export function create(x, y) {\n  if (x == null) {\n    x = 0;\n  }\n  if (y == null) {\n    y = 0;\n  }\n  return [x, y];\n}\nexport function copy(out, v) {\n  out[0] = v[0];\n  out[1] = v[1];\n  return out;\n}\nexport function clone(v) {\n  return [v[0], v[1]];\n}\nexport function set(out, a, b) {\n  out[0] = a;\n  out[1] = b;\n  return out;\n}\nexport function add(out, v1, v2) {\n  out[0] = v1[0] + v2[0];\n  out[1] = v1[1] + v2[1];\n  return out;\n}\nexport function scaleAndAdd(out, v1, v2, a) {\n  out[0] = v1[0] + v2[0] * a;\n  out[1] = v1[1] + v2[1] * a;\n  return out;\n}\nexport function sub(out, v1, v2) {\n  out[0] = v1[0] - v2[0];\n  out[1] = v1[1] - v2[1];\n  return out;\n}\nexport function len(v) {\n  return Math.sqrt(lenSquare(v));\n}\nexport var length = len;\nexport function lenSquare(v) {\n  return v[0] * v[0] + v[1] * v[1];\n}\nexport var lengthSquare = lenSquare;\nexport function mul(out, v1, v2) {\n  out[0] = v1[0] * v2[0];\n  out[1] = v1[1] * v2[1];\n  return out;\n}\nexport function div(out, v1, v2) {\n  out[0] = v1[0] / v2[0];\n  out[1] = v1[1] / v2[1];\n  return out;\n}\nexport function dot(v1, v2) {\n  return v1[0] * v2[0] + v1[1] * v2[1];\n}\nexport function scale(out, v, s) {\n  out[0] = v[0] * s;\n  out[1] = v[1] * s;\n  return out;\n}\nexport function normalize(out, v) {\n  var d = len(v);\n  if (d === 0) {\n    out[0] = 0;\n    out[1] = 0;\n  } else {\n    out[0] = v[0] / d;\n    out[1] = v[1] / d;\n  }\n  return out;\n}\nexport function distance(v1, v2) {\n  return Math.sqrt((v1[0] - v2[0]) * (v1[0] - v2[0]) + (v1[1] - v2[1]) * (v1[1] - v2[1]));\n}\nexport var dist = distance;\nexport function distanceSquare(v1, v2) {\n  return (v1[0] - v2[0]) * (v1[0] - v2[0]) + (v1[1] - v2[1]) * (v1[1] - v2[1]);\n}\nexport var distSquare = distanceSquare;\nexport function negate(out, v) {\n  out[0] = -v[0];\n  out[1] = -v[1];\n  return out;\n}\nexport function lerp(out, v1, v2, t) {\n  out[0] = v1[0] + t * (v2[0] - v1[0]);\n  out[1] = v1[1] + t * (v2[1] - v1[1]);\n  return out;\n}\nexport function applyTransform(out, v, m) {\n  var x = v[0];\n  var y = v[1];\n  out[0] = m[0] * x + m[2] * y + m[4];\n  out[1] = m[1] * x + m[3] * y + m[5];\n  return out;\n}\nexport function min(out, v1, v2) {\n  out[0] = Math.min(v1[0], v2[0]);\n  out[1] = Math.min(v1[1], v2[1]);\n  return out;\n}\nexport function max(out, v1, v2) {\n  out[0] = Math.max(v1[0], v2[0]);\n  out[1] = Math.max(v1[1], v2[1]);\n  return out;\n}", "map": {"version": 3, "names": ["create", "x", "y", "copy", "out", "v", "clone", "set", "a", "b", "add", "v1", "v2", "scaleAndAdd", "sub", "len", "Math", "sqrt", "lenSquare", "length", "lengthSquare", "mul", "div", "dot", "scale", "s", "normalize", "d", "distance", "dist", "distanceSquare", "distSquare", "negate", "lerp", "t", "applyTransform", "m", "min", "max"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/zrender/lib/core/vector.js"], "sourcesContent": ["export function create(x, y) {\n    if (x == null) {\n        x = 0;\n    }\n    if (y == null) {\n        y = 0;\n    }\n    return [x, y];\n}\nexport function copy(out, v) {\n    out[0] = v[0];\n    out[1] = v[1];\n    return out;\n}\nexport function clone(v) {\n    return [v[0], v[1]];\n}\nexport function set(out, a, b) {\n    out[0] = a;\n    out[1] = b;\n    return out;\n}\nexport function add(out, v1, v2) {\n    out[0] = v1[0] + v2[0];\n    out[1] = v1[1] + v2[1];\n    return out;\n}\nexport function scaleAndAdd(out, v1, v2, a) {\n    out[0] = v1[0] + v2[0] * a;\n    out[1] = v1[1] + v2[1] * a;\n    return out;\n}\nexport function sub(out, v1, v2) {\n    out[0] = v1[0] - v2[0];\n    out[1] = v1[1] - v2[1];\n    return out;\n}\nexport function len(v) {\n    return Math.sqrt(lenSquare(v));\n}\nexport var length = len;\nexport function lenSquare(v) {\n    return v[0] * v[0] + v[1] * v[1];\n}\nexport var lengthSquare = lenSquare;\nexport function mul(out, v1, v2) {\n    out[0] = v1[0] * v2[0];\n    out[1] = v1[1] * v2[1];\n    return out;\n}\nexport function div(out, v1, v2) {\n    out[0] = v1[0] / v2[0];\n    out[1] = v1[1] / v2[1];\n    return out;\n}\nexport function dot(v1, v2) {\n    return v1[0] * v2[0] + v1[1] * v2[1];\n}\nexport function scale(out, v, s) {\n    out[0] = v[0] * s;\n    out[1] = v[1] * s;\n    return out;\n}\nexport function normalize(out, v) {\n    var d = len(v);\n    if (d === 0) {\n        out[0] = 0;\n        out[1] = 0;\n    }\n    else {\n        out[0] = v[0] / d;\n        out[1] = v[1] / d;\n    }\n    return out;\n}\nexport function distance(v1, v2) {\n    return Math.sqrt((v1[0] - v2[0]) * (v1[0] - v2[0])\n        + (v1[1] - v2[1]) * (v1[1] - v2[1]));\n}\nexport var dist = distance;\nexport function distanceSquare(v1, v2) {\n    return (v1[0] - v2[0]) * (v1[0] - v2[0])\n        + (v1[1] - v2[1]) * (v1[1] - v2[1]);\n}\nexport var distSquare = distanceSquare;\nexport function negate(out, v) {\n    out[0] = -v[0];\n    out[1] = -v[1];\n    return out;\n}\nexport function lerp(out, v1, v2, t) {\n    out[0] = v1[0] + t * (v2[0] - v1[0]);\n    out[1] = v1[1] + t * (v2[1] - v1[1]);\n    return out;\n}\nexport function applyTransform(out, v, m) {\n    var x = v[0];\n    var y = v[1];\n    out[0] = m[0] * x + m[2] * y + m[4];\n    out[1] = m[1] * x + m[3] * y + m[5];\n    return out;\n}\nexport function min(out, v1, v2) {\n    out[0] = Math.min(v1[0], v2[0]);\n    out[1] = Math.min(v1[1], v2[1]);\n    return out;\n}\nexport function max(out, v1, v2) {\n    out[0] = Math.max(v1[0], v2[0]);\n    out[1] = Math.max(v1[1], v2[1]);\n    return out;\n}\n"], "mappings": "AAAA,OAAO,SAASA,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAID,CAAC,IAAI,IAAI,EAAE;IACXA,CAAC,GAAG,CAAC;EACT;EACA,IAAIC,CAAC,IAAI,IAAI,EAAE;IACXA,CAAC,GAAG,CAAC;EACT;EACA,OAAO,CAACD,CAAC,EAAEC,CAAC,CAAC;AACjB;AACA,OAAO,SAASC,IAAIA,CAACC,GAAG,EAAEC,CAAC,EAAE;EACzBD,GAAG,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;EACbD,GAAG,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;EACb,OAAOD,GAAG;AACd;AACA,OAAO,SAASE,KAAKA,CAACD,CAAC,EAAE;EACrB,OAAO,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB;AACA,OAAO,SAASE,GAAGA,CAACH,GAAG,EAAEI,CAAC,EAAEC,CAAC,EAAE;EAC3BL,GAAG,CAAC,CAAC,CAAC,GAAGI,CAAC;EACVJ,GAAG,CAAC,CAAC,CAAC,GAAGK,CAAC;EACV,OAAOL,GAAG;AACd;AACA,OAAO,SAASM,GAAGA,CAACN,GAAG,EAAEO,EAAE,EAAEC,EAAE,EAAE;EAC7BR,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACtBR,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACtB,OAAOR,GAAG;AACd;AACA,OAAO,SAASS,WAAWA,CAACT,GAAG,EAAEO,EAAE,EAAEC,EAAE,EAAEJ,CAAC,EAAE;EACxCJ,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGJ,CAAC;EAC1BJ,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGJ,CAAC;EAC1B,OAAOJ,GAAG;AACd;AACA,OAAO,SAASU,GAAGA,CAACV,GAAG,EAAEO,EAAE,EAAEC,EAAE,EAAE;EAC7BR,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACtBR,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACtB,OAAOR,GAAG;AACd;AACA,OAAO,SAASW,GAAGA,CAACV,CAAC,EAAE;EACnB,OAAOW,IAAI,CAACC,IAAI,CAACC,SAAS,CAACb,CAAC,CAAC,CAAC;AAClC;AACA,OAAO,IAAIc,MAAM,GAAGJ,GAAG;AACvB,OAAO,SAASG,SAASA,CAACb,CAAC,EAAE;EACzB,OAAOA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;AACpC;AACA,OAAO,IAAIe,YAAY,GAAGF,SAAS;AACnC,OAAO,SAASG,GAAGA,CAACjB,GAAG,EAAEO,EAAE,EAAEC,EAAE,EAAE;EAC7BR,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACtBR,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACtB,OAAOR,GAAG;AACd;AACA,OAAO,SAASkB,GAAGA,CAAClB,GAAG,EAAEO,EAAE,EAAEC,EAAE,EAAE;EAC7BR,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACtBR,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACtB,OAAOR,GAAG;AACd;AACA,OAAO,SAASmB,GAAGA,CAACZ,EAAE,EAAEC,EAAE,EAAE;EACxB,OAAOD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;AACxC;AACA,OAAO,SAASY,KAAKA,CAACpB,GAAG,EAAEC,CAAC,EAAEoB,CAAC,EAAE;EAC7BrB,GAAG,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGoB,CAAC;EACjBrB,GAAG,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGoB,CAAC;EACjB,OAAOrB,GAAG;AACd;AACA,OAAO,SAASsB,SAASA,CAACtB,GAAG,EAAEC,CAAC,EAAE;EAC9B,IAAIsB,CAAC,GAAGZ,GAAG,CAACV,CAAC,CAAC;EACd,IAAIsB,CAAC,KAAK,CAAC,EAAE;IACTvB,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACVA,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;EACd,CAAC,MACI;IACDA,GAAG,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGsB,CAAC;IACjBvB,GAAG,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGsB,CAAC;EACrB;EACA,OAAOvB,GAAG;AACd;AACA,OAAO,SAASwB,QAAQA,CAACjB,EAAE,EAAEC,EAAE,EAAE;EAC7B,OAAOI,IAAI,CAACC,IAAI,CAAC,CAACN,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,KAAKD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,CAAC,GAC5C,CAACD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,KAAKD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C;AACA,OAAO,IAAIiB,IAAI,GAAGD,QAAQ;AAC1B,OAAO,SAASE,cAAcA,CAACnB,EAAE,EAAEC,EAAE,EAAE;EACnC,OAAO,CAACD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,KAAKD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,CAAC,GAClC,CAACD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,KAAKD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA,OAAO,IAAImB,UAAU,GAAGD,cAAc;AACtC,OAAO,SAASE,MAAMA,CAAC5B,GAAG,EAAEC,CAAC,EAAE;EAC3BD,GAAG,CAAC,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,CAAC,CAAC;EACdD,GAAG,CAAC,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,CAAC,CAAC;EACd,OAAOD,GAAG;AACd;AACA,OAAO,SAAS6B,IAAIA,CAAC7B,GAAG,EAAEO,EAAE,EAAEC,EAAE,EAAEsB,CAAC,EAAE;EACjC9B,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGuB,CAAC,IAAItB,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,CAAC;EACpCP,GAAG,CAAC,CAAC,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGuB,CAAC,IAAItB,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,CAAC;EACpC,OAAOP,GAAG;AACd;AACA,OAAO,SAAS+B,cAAcA,CAAC/B,GAAG,EAAEC,CAAC,EAAE+B,CAAC,EAAE;EACtC,IAAInC,CAAC,GAAGI,CAAC,CAAC,CAAC,CAAC;EACZ,IAAIH,CAAC,GAAGG,CAAC,CAAC,CAAC,CAAC;EACZD,GAAG,CAAC,CAAC,CAAC,GAAGgC,CAAC,CAAC,CAAC,CAAC,GAAGnC,CAAC,GAAGmC,CAAC,CAAC,CAAC,CAAC,GAAGlC,CAAC,GAAGkC,CAAC,CAAC,CAAC,CAAC;EACnChC,GAAG,CAAC,CAAC,CAAC,GAAGgC,CAAC,CAAC,CAAC,CAAC,GAAGnC,CAAC,GAAGmC,CAAC,CAAC,CAAC,CAAC,GAAGlC,CAAC,GAAGkC,CAAC,CAAC,CAAC,CAAC;EACnC,OAAOhC,GAAG;AACd;AACA,OAAO,SAASiC,GAAGA,CAACjC,GAAG,EAAEO,EAAE,EAAEC,EAAE,EAAE;EAC7BR,GAAG,CAAC,CAAC,CAAC,GAAGY,IAAI,CAACqB,GAAG,CAAC1B,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/BR,GAAG,CAAC,CAAC,CAAC,GAAGY,IAAI,CAACqB,GAAG,CAAC1B,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/B,OAAOR,GAAG;AACd;AACA,OAAO,SAASkC,GAAGA,CAAClC,GAAG,EAAEO,EAAE,EAAEC,EAAE,EAAE;EAC7BR,GAAG,CAAC,CAAC,CAAC,GAAGY,IAAI,CAACsB,GAAG,CAAC3B,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/BR,GAAG,CAAC,CAAC,CAAC,GAAGY,IAAI,CAACsB,GAAG,CAAC3B,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/B,OAAOR,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}