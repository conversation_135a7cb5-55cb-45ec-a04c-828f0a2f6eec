{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRegularNoticeFileSaveRegularNoticeFilePost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRegularNoticeFileSaveRegularNoticeFilePost$Json.PATH, 'post');\n  if (params) {\n    rb.query('BuId', params.BuId, {});\n    rb.query('UserId', params.UserId, {});\n    rb.query('UserCode', params.UserCode, {});\n    rb.query('UserName', params.UserName, {});\n    rb.query('CUserType', params.CUserType, {});\n    rb.query('CreateTime', params.CreateTime, {});\n    rb.query('ExpTime', params.ExpTime, {});\n    rb.query('LoginId', params.LoginId, {});\n    rb.query('IsLimit', params.IsLimit, {});\n    rb.body(params.body, 'multipart/form-data');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRegularNoticeFileSaveRegularNoticeFilePost$Json.PATH = '/api/RegularNoticeFile/SaveRegularNoticeFile';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}