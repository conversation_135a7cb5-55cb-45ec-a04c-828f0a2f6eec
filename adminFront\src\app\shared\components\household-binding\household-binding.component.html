<div class="household-binding-container">
  <!-- 提醒文案顯示區域 -->
  <div *ngIf="reminderText" class="reminder-text-container"
    style="margin-bottom: 8px; padding: 8px 12px; background-color: #e3f2fd; border: 1px solid #bbdefb; border-radius: 4px; font-size: 0.875rem; color: #1976d2;">
    <nb-icon icon="info-outline" style="margin-right: 6px; color: #1976d2;"></nb-icon>
    <span>{{ reminderText }}</span>
  </div>

  <!-- [優化] 已選擇戶別摘要顯示區域 -->
  <div *ngIf="selectedHouseIds.length > 0" class="selected-households-summary" [nbPopover]="detailsPopover"
    nbPopoverTrigger="hover" nbPopoverPlacement="bottom">
    <div class="summary-header">
      <div class="summary-info">
        <nb-icon icon="people-outline" class="text-primary"></nb-icon>
        <span class="summary-count">{{displayText.selectedPrefix}} ({{getSelectedCount()}})</span>
      </div>
      <button type="button" class="btn btn-outline-danger btn-sm" [disabled]="disabled"
        (click)="onClearAll(); $event.stopPropagation()">
        清空全部
      </button>
    </div>
    <div class="summary-content">
      <span class="summary-text">{{ getGroupedSummary() }}</span>
      <nb-icon icon="info-outline" class="info-icon"></nb-icon>
    </div>
  </div>

  <!-- 選擇器 -->
  <div class="selector-container">
    <button type="button" class="selector-button" [class.disabled]="disabled || isLoading"
      [disabled]="disabled || isLoading" (click)="toggleDropdown()"
      style="width: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.375rem; background-color: #fff; cursor: pointer;">
      <span class="selector-text">
        <ng-container *ngIf="isLoading">
          <nb-icon icon="loader-outline" class="spin"></nb-icon>
          載入中...
        </ng-container>
        <ng-container *ngIf="!isLoading">
          {{getSelectedCount() > 0 ? '已選擇 ' + getSelectedCount() + ' ' + displayText.selectedCount : (placeholder ||
          displayText.placeholder)}}
        </ng-container>
      </span>
      <nb-icon icon="home-outline" class="chevron-icon"></nb-icon>
    </button>
  </div>
</div>

<!-- [優化] 已選項目詳情 Popover -->
<ng-template #detailsPopover>
  <div class="details-popover-content">
    <ul class="details-list">
      <ng-container *ngFor="let item of getAllSelectedItems()">
        <li class="details-list-item">
          <span class="building-name">{{item.building}}</span>
          <span class="house-name">{{item.houseName}}</span>
          <span *ngIf="!useHouseNameMode && item.floor" class="floor-badge">{{item.floor}}</span>
        </li>
      </ng-container>
    </ul>
  </div>
</ng-template>

<!-- 戶別/戶型選擇對話框 -->
<ng-template #householdDialog let-dialog let-ref="dialogRef">
  <nb-card style="width: 95vw; max-width: 1200px; max-height: 90vh;">
    <nb-card-header>
      <div style="display: flex; align-items: center; justify-content: space-between;">
        <div style="display: flex; align-items: center; gap: 8px;">
          <nb-icon icon="home-outline" style="color: #007bff; font-size: 1.5rem;"></nb-icon>
          <span style="font-weight: 500; color: #495057; font-size: 1.25rem;">{{displayText.selectUnit}}</span>
          <span style="font-size: 0.875rem; color: #6c757d;">({{buildings.length}} 個棟別)</span>
        </div>
        <span style="font-size: 0.875rem; color: #6c757d;">已選擇: {{getSelectedCount()}}</span>
      </div>
      <!-- 對話框中的提醒文案 -->
      <div *ngIf="reminderText"
        style="margin-top: 12px; padding: 8px 12px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; font-size: 0.875rem; color: #856404;">
        <nb-icon icon="alert-triangle-outline" style="margin-right: 6px; color: #856404;"></nb-icon>
        <span>{{ reminderText }}</span>
      </div>
    </nb-card-header>

    <nb-card-body style="padding: 0; overflow: hidden;">
      <!-- 載入狀態 -->
      <div *ngIf="isLoading"
        style="width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;">
        <div style="text-align: center; color: #6c757d;">
          <nb-icon icon="loader-outline" class="spin" style="font-size: 2rem; margin-bottom: 8px;"></nb-icon>
          <p style="margin: 0; font-size: 0.875rem;">載入{{displayText.unitType}}資料中...</p>
        </div>
      </div>

      <!-- 主要內容區域 -->
      <div *ngIf="!isLoading" style="display: flex; height: 60vh; min-height: 400px;">
        <!-- 棟別選擇側邊欄 -->
        <div
          style="width: 300px; border-right: 1px solid #e9ecef; background-color: #f8f9fa; display: flex; flex-direction: column;">
          <div style="padding: 12px 16px; border-bottom: 1px solid #e9ecef;">
            <h6 style="margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;">棟別列表</h6>
          </div>
          <div style="flex: 1; overflow-y: auto;">
            <button *ngFor="let building of buildings" type="button" (click)="onBuildingSelect(building)"
              [style.background-color]="selectedBuilding === building ? '#e3f2fd' : 'transparent'"
              [style.border-left]="selectedBuilding === building ? '3px solid #007bff' : '3px solid transparent'"
              style="width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;">
              <span style="font-weight: 500; color: #495057;">{{building}}</span>
              <span
                style="font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 2px 6px; border-radius: 10px;">
                {{getBuildingCount(building)}}戶
              </span>
            </button>
          </div>
        </div>

        <!-- 戶別/戶型選擇主區域 -->
        <div style="flex: 1; display: flex; flex-direction: column;">
          <div style="padding: 12px 16px; border-bottom: 1px solid #e9ecef;">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
              <div style="display: flex; align-items: center; gap: 8px;">
                <h6 style="margin: 0; font-size: 0.875rem; font-weight: 600; color: #495057;">
                  {{displayText.unitSelection}}</h6> <span *ngIf="selectedBuilding"
                  style="background-color: #007bff; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;">
                  <nb-icon icon="home-outline" style="margin-right: 4px; font-size: 0.7rem;"></nb-icon>
                  {{selectedBuilding}}
                </span>
                <span *ngIf="!useHouseNameMode && selectedFloor"
                  style="background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;">
                  <nb-icon icon="layers-outline" style="margin-right: 4px; font-size: 0.7rem;"></nb-icon>
                  {{selectedFloor}}
                </span>
              </div>
              <div
                *ngIf="allowBatchSelect && selectedBuilding && buildingData[selectedBuilding] && buildingData[selectedBuilding].length > 0"
                style="display: flex; gap: 4px;">
                <button type="button" [disabled]="!canSelectMore()" (click)="onSelectAllFiltered()"
                  [style.opacity]="canSelectMore() ? '1' : '0.5'"
                  style="padding: 4px 8px; font-size: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                  全選當前
                </button>
                <button type="button" [disabled]="!canSelectMore()" (click)="onSelectAllBuilding()"
                  [style.opacity]="canSelectMore() ? '1' : '0.5'"
                  style="padding: 4px 8px; font-size: 0.75rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                  全選{{selectedBuilding}}
                </button>
                <button type="button" *ngIf="isSomeBuildingSelected()" (click)="onUnselectAllBuilding()"
                  style="padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                  清除
                </button>
              </div>
            </div>

            <!-- 搜尋框 -->
            <div *ngIf="selectedBuilding" style="margin-top: 8px;">
              <div style="position: relative;">
                <input type="text" [(ngModel)]="searchTerm" (input)="onSearchChange($event)"
                  [placeholder]="displayText.searchPlaceholder"
                  style="width: 100%; padding: 6px 32px 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.875rem; outline: none;">
                <nb-icon icon="search-outline"
                  style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; font-size: 0.875rem;"></nb-icon>
              </div>
              <div *ngIf="searchTerm && hasNoSearchResults()"
                style="font-size: 0.75rem; color: #dc3545; margin-top: 4px;">
                {{displayText.noResults}} "{{searchTerm}}"
              </div>
            </div> <!-- 樓層篩選器 -->
            <div *ngIf="!useHouseNameMode && selectedBuilding && floors.length > 1" style="margin-top: 12px;">
              <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                <nb-icon icon="layers-outline" style="color: #6c757d; font-size: 1rem;"></nb-icon>
                <span style="font-size: 0.875rem; font-weight: 600; color: #495057;">樓層篩選:</span>
                <span *ngIf="selectedFloor"
                  style="background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;">
                  {{selectedFloor}}
                </span>
                <button type="button" *ngIf="selectedFloor" (click)="onFloorSelect('')"
                  style="font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;">
                  清除篩選
                </button>
              </div>
              <div style="display: flex; flex-wrap: wrap; gap: 4px; max-height: 100px; overflow-y: auto;">
                <button type="button" *ngFor="let floor of floors" (click)="onFloorSelect(floor)"
                  [style.background-color]="selectedFloor === floor ? '#007bff' : '#f8f9fa'"
                  [style.color]="selectedFloor === floor ? '#fff' : '#495057'"
                  [style.border]="selectedFloor === floor ? '2px solid #007bff' : '1px solid #dee2e6'"
                  style="padding: 6px 10px; border-radius: 3px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;">
                  <nb-icon icon="layers-outline" style="margin-right: 3px; font-size: 0.7rem;"></nb-icon>
                  {{floor}} <span style="font-size: 0.7rem; opacity: 0.7;">({{getFloorCount(floor)}})</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 戶別/戶型網格或空狀態 -->
          <div style="flex: 1; padding: 16px; overflow-y: auto;">
            <div *ngIf="!selectedBuilding" style="text-align: center; padding: 40px 20px; color: #6c757d;">
              <nb-icon icon="home-outline" style="font-size: 2rem; margin-bottom: 8px; opacity: 0.5;"></nb-icon>
              <p style="margin: 0; font-size: 0.875rem;">請先選擇棟別</p>
            </div>
            <div *ngIf="selectedBuilding && buildingData[selectedBuilding] && buildingData[selectedBuilding].length > 0"
              style="display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;">
              <ng-container *ngFor="let household of getUniqueHouseholdsForDisplay()"> <button type="button"
                  (click)="onHouseholdToggle(household.houseId)" [disabled]="isHouseholdDisabled(household.houseId)"
                  [style.background-color]="isHouseholdSelected(household.houseId) ? '#007bff' : (isHouseholdExcluded(household.houseId) ? '#f8f9fa' : '#fff')"
                  [style.color]="isHouseholdSelected(household.houseId) ? '#fff' : (isHouseholdExcluded(household.houseId) ? '#6c757d' : '#495057')"
                  [style.border]="isHouseholdSelected(household.houseId) ? '2px solid #007bff' : (isHouseholdExcluded(household.houseId) ? '1px solid #dee2e6' : '1px solid #ced4da')"
                  [style.opacity]="isHouseholdDisabled(household.houseId) ? '0.6' : '1'"
                  [style.cursor]="isHouseholdDisabled(household.houseId) ? 'not-allowed' : 'pointer'"
                  style="padding: 8px 6px; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 55px; position: relative; display: flex; flex-direction: column; justify-content: center; align-items: center; gap: 3px;">
                  <span [style.text-decoration]="isHouseholdExcluded(household.houseId) ? 'line-through' : 'none'"
                    style="font-weight: 600; line-height: 1.2; font-size: 0.85rem;">
                    {{household.houseName}}
                  </span> <span *ngIf="!useHouseNameMode && household.floor"
                    [style.background-color]="isHouseholdSelected(household.houseId) ? 'rgba(255,255,255,0.9)' : '#28a745'"
                    [style.color]="isHouseholdSelected(household.houseId) ? '#007bff' : '#fff'"
                    [style.border]="isHouseholdSelected(household.houseId) ? '1px solid rgba(0,123,255,0.3)' : 'none'"
                    style="font-size: 0.7rem; font-weight: 600; padding: 2px 6px; border-radius: 3px; display: inline-flex; align-items: center; justify-content: center; min-width: 22px;">
                    <nb-icon icon="layers-outline" style="margin-right: 2px; font-size: 0.6rem;"></nb-icon>
                    {{household.floor}}
                  </span>
                  <div *ngIf="isHouseholdExcluded(household.houseId)"
                    style="position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;">
                    ✕
                  </div>
                </button>
              </ng-container>
            </div>
            <div
              *ngIf="!isLoading && selectedBuilding && buildings.length > 0 && (!buildingData[selectedBuilding] || buildingData[selectedBuilding].length === 0) && !searchTerm"
              style="text-align: center; padding: 40px 20px; color: #6c757d;">
              <nb-icon icon="alert-circle-outline" style="font-size: 2rem; margin-bottom: 8px; opacity: 0.5;"></nb-icon>
              <p style="margin: 0; font-size: 0.875rem;">{{displayText.noAvailable}}</p>
            </div>
          </div>
        </div>
      </div>
    </nb-card-body>

    <nb-card-footer style="padding: 16px; border-top: 1px solid #e9ecef; background-color: #f8f9fa;">
      <!-- 統計資訊行 -->
      <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
        <div style="display: flex; align-items: center; gap: 16px; font-size: 0.875rem; color: #495057;">
          <div style="display: flex; align-items: center; gap: 4px;">
            <nb-icon icon="checkmark-circle-outline" style="color: #28a745;"></nb-icon>
            <span>已選擇: <strong>{{getSelectedCount()}}</strong> {{displayText.selectedCount}}</span>
          </div>
          <div *ngIf="maxSelections" style="display: flex; align-items: center; gap: 4px;">
            <nb-icon icon="alert-circle-outline" style="color: #ffc107;"></nb-icon>
            <span>限制: 最多 <strong>{{maxSelections}}</strong> 個</span>
          </div>
          <div *ngIf="selectedBuilding" style="display: flex; align-items: center; gap: 8px;">
            <nb-icon icon="home-outline" style="color: #007bff;"></nb-icon>
            <span>當前棟別: </span>
            <span
              style="background-color: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600;">
              {{selectedBuilding}}
            </span> <span *ngIf="!useHouseNameMode && selectedFloor"
              style="background-color: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600; margin-left: 4px;">
              <nb-icon icon="layers-outline" style="margin-right: 4px; font-size: 0.7rem;"></nb-icon>
              {{selectedFloor}}
            </span>
          </div>
        </div>
        <div *ngIf="searchTerm"
          style="font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 4px;">
          搜尋: "{{searchTerm}}" ({{getFilteredHouseholdsCount()}} 個結果)
        </div>
      </div>

      <!-- 操作按鈕行 -->
      <div style="display: flex; align-items: center; justify-content: space-between; gap: 8px;">
        <div style="display: flex; gap: 8px;">
          <button type="button" *ngIf="selectedHouseIds.length > 0" (click)="onClearAll()"
            style="padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;">
            <nb-icon icon="trash-2-outline"></nb-icon>
            清空全部
          </button>
          <button type="button" *ngIf="searchTerm" (click)="resetSearch()"
            style="padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;">
            <nb-icon icon="refresh-outline"></nb-icon>
            重置搜尋
          </button>
        </div>

        <div style="display: flex; gap: 8px;">
          <button type="button" (click)="ref.close()"
            style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem;">
            取消
          </button>
          <button type="button" (click)="ref.close()"
            style="padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 4px;">
            <nb-icon icon="checkmark-outline"></nb-icon>
            確定選擇 ({{getSelectedCount()}})
          </button>
        </div>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>
