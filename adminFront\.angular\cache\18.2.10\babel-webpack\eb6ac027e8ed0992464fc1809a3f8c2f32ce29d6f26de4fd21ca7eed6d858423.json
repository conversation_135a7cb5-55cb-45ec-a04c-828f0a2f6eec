{"ast": null, "code": "import isSameISOWeek from \"../isSameISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisISOWeek\n * @category ISO Week Helpers\n * @summary Is the given date in the same ISO week as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same ISO week as the current date?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 25 September 2014, is 22 September 2014 in this ISO week?\n * const result = isThisISOWeek(new Date(2014, 8, 22))\n * //=> true\n */\nexport default function isThisISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameISOWeek(dirtyDate, Date.now());\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}