{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { environment } from 'src/environments/environment';\nlet FileUploadComponent = class FileUploadComponent extends BaseComponent {\n  constructor(_allow, message, fileService) {\n    super(_allow);\n    this._allow = _allow;\n    this.message = message;\n    this.fileService = fileService;\n    this.config = {\n      acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\n      acceptedFileRegex: /pdf|jpg|jpeg|png/i,\n      acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\n      label: '上傳檔案',\n      helpText: '*請上傳PDF格式',\n      required: false,\n      disabled: false,\n      autoFillName: false,\n      buttonText: '上傳',\n      buttonIcon: 'fa-solid fa-cloud-arrow-up',\n      maxFileSize: 10,\n      multiple: false,\n      showPreview: false\n    };\n    this.currentFileName = null;\n    this.currentFileUrl = null;\n    this.labelMinWidth = '172px';\n    this.fileList = []; // 用於多檔案模式的檔案列表\n    this.existingFiles = []; // 已存在的檔案列表（編輯模式用）\n    this.fileSelected = new EventEmitter();\n    this.fileCleared = new EventEmitter();\n    this.nameAutoFilled = new EventEmitter();\n    this.multiFileSelected = new EventEmitter(); // 多檔案選擇事件\n    this.fileRemoved = new EventEmitter(); // 檔案移除事件\n    this.fileName = null;\n  }\n  ngOnInit() {\n    this.fileName = this.currentFileName; // 設置預設配置\n    this.config = {\n      ...{\n        acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\n        acceptedFileRegex: /pdf|jpg|jpeg|png/i,\n        acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\n        label: '上傳檔案',\n        helpText: '*請上傳PDF格式',\n        required: false,\n        disabled: false,\n        autoFillName: false,\n        buttonText: '上傳',\n        buttonIcon: 'fa-solid fa-cloud-arrow-up',\n        maxFileSize: 10,\n        multiple: false,\n        showPreview: false\n      },\n      ...this.config\n    };\n  }\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (!files || files.length === 0) {\n      return;\n    }\n    if (this.config.multiple) {\n      this.handleMultipleFiles(files);\n    } else {\n      this.handleSingleFile(files[0]);\n    }\n  }\n  handleSingleFile(file) {\n    // 檔案格式檢查\n    if (!this.config.acceptedFileRegex.test(file.name)) {\n      const acceptedFormats = this.getAcceptedFormatsText();\n      this.message.showErrorMSG(`檔案格式錯誤，${acceptedFormats}`);\n      return;\n    }\n    // 檔案大小檢查\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\n    if (file.size > maxSizeInBytes) {\n      this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);\n      return;\n    }\n    this.fileName = file.name;\n    // 自動填入名稱功能\n    if (this.config.autoFillName) {\n      const fileNameWithoutExtension = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\n    }\n    const reader = new FileReader();\n    reader.onload = e => {\n      // 判斷檔案類型\n      let fileType;\n      if (file.type.startsWith('image/')) {\n        fileType = EnumFileType.JPG; // 圖片\n      } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\n        fileType = 3; // CAD檔案\n      } else {\n        fileType = EnumFileType.PDF; // PDF\n      }\n      const result = {\n        CName: file.name,\n        CFile: e.target?.result?.toString().split(',')[1],\n        Cimg: file.name.includes('pdf') ? file : file,\n        CFileUpload: file,\n        CFileType: fileType,\n        fileName: file.name\n      };\n      this.fileSelected.emit(result);\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null;\n      }\n    };\n    reader.readAsDataURL(file);\n  }\n  handleMultipleFiles(files) {\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\n    const fileRegex = this.config.acceptedFileRegex || /pdf|jpg|jpeg|png|dwg|dxf/i;\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\n    // 如果是第一次選擇檔案且支援自動填入名稱\n    if (this.config.autoFillName && files.length > 0) {\n      const firstFile = files[0];\n      const fileName = firstFile.name;\n      const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\n    }\n    const newFiles = [];\n    let processedCount = 0;\n    for (let i = 0; i < files.length; i++) {\n      const file = files[i];\n      // 檔案格式檢查\n      if (!fileRegex.test(file.name)) {\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\n        processedCount++;\n        continue;\n      }\n      // 檔案大小檢查\n      if (file.size > maxSizeInBytes) {\n        this.message.showErrorMSG(`檔案 ${file.name} 大小超過限制（${this.config.maxFileSize}MB）`);\n        processedCount++;\n        continue;\n      }\n      if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\n        const reader = new FileReader();\n        reader.onload = e => {\n          // 判斷檔案類型\n          let fileType = 1; // 預設為其他\n          const fileName = file.name.toLowerCase();\n          if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\n            fileType = 2; // 圖片\n          } else if (fileName.endsWith('.pdf')) {\n            fileType = 1; // PDF\n          } else if (fileName.match(/\\.(dwg|dxf)$/)) {\n            fileType = 3; // CAD\n          }\n          newFiles.push({\n            data: e.target.result,\n            CFileBlood: this.removeBase64Prefix(e.target.result),\n            CFileName: file.name,\n            CFileType: fileType\n          });\n          processedCount++;\n          if (processedCount === files.length) {\n            this.fileList = [...this.fileList, ...newFiles];\n            this.multiFileSelected.emit(this.fileList);\n            if (this.fileInput) {\n              this.fileInput.nativeElement.value = null;\n            }\n          }\n        };\n        reader.readAsDataURL(file);\n      } else {\n        processedCount++;\n      }\n    }\n  }\n  clearFile() {\n    this.fileName = null;\n    this.fileCleared.emit();\n    if (this.fileInput) {\n      this.fileInput.nativeElement.value = null;\n    }\n  }\n  openFile(url) {\n    if (url) {\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\n      window.open(fullUrl, '_blank');\n    }\n  }\n  removeFile(index) {\n    this.fileList.splice(index, 1);\n    this.fileRemoved.emit(index);\n    this.multiFileSelected.emit(this.fileList);\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  isImage(fileType) {\n    return fileType === 2;\n  }\n  isCad(fileType) {\n    return fileType === 3;\n  }\n  isPdf(fileType) {\n    return fileType === 1;\n  }\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  } // 處理檔案點擊事件\n  handleFileClick(file) {\n    console.log('檔案點擊事件觸發:', file);\n    const fileName = file.CFileName || file.fileName || '';\n    const displayName = file.CFileName || fileName;\n    // 判斷檔案類型\n    const isImageByName = this.isImageFile(displayName);\n    const isPdfByName = this.isPDFString(displayName);\n    const isCadByName = this.isCadString(displayName);\n    console.log('檔案類型判斷:', {\n      isImageByName,\n      isPdfByName,\n      isCadByName,\n      displayName\n    });\n    // 統一使用 GetFile API 取得檔案 blob\n    const relativePath = file.relativePath || file.CFile;\n    const serverFileName = file.fileName || file.CFileName;\n    console.log('檔案路徑資訊:', {\n      relativePath,\n      serverFileName\n    });\n    if (relativePath && serverFileName) {\n      console.log('使用 GetFile API 取得檔案 blob');\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\n    } else {\n      // 如果沒有路徑資訊，顯示錯誤訊息\n      console.error('檔案缺少路徑資訊，無法使用 getFile API:', file);\n      this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\n    }\n  }\n  // 處理本地檔案的後備方法 - 已廢棄，統一使用 getFile API\n  handleLocalFile(file, isImage, isPdf, isCad) {\n    const fileName = file.CFileName || file.fileName || '';\n    console.error('檔案缺少必要的路徑資訊，無法使用 getFile API:', file);\n    this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\n  }\n  // 處理本地檔案資料（新上傳的檔案）- 已廢棄，統一使用 getFile API\n  handleLocalFileData(data, fileName, isImage, isPdf, isCad) {\n    console.warn('handleLocalFileData 方法已廢棄，統一使用 getFile API');\n    this.message.showErrorMSG('檔案處理功能已更新，請重新操作');\n    /* 原有邏輯已移除，統一使用 getFile API\n    if (isImage) {\n      // 圖片預覽\n      this.openImagePreview(data, fileName);\n    } else if (isPdf) {\n      // PDF 檔案另開視窗顯示\n      this.openPdfInNewWindow(data, fileName);\n    } else {\n      // 其他檔案（如CAD）轉換為blob並下載\n      this.downloadBase64File(data, fileName);\n    }\n    */\n  }\n  // 下載base64檔案\n  downloadBase64File(base64Data, fileName) {\n    try {\n      // 移除data URL前綴（如果存在）\n      const base64String = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\n      // 轉換為blob\n      const byteCharacters = atob(base64String);\n      const byteNumbers = new Array(byteCharacters.length);\n      for (let i = 0; i < byteCharacters.length; i++) {\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      const blob = new Blob([byteArray]);\n      // 創建下載連結\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      // 清理URL\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\n    } catch (error) {\n      console.error('下載檔案失敗:', error);\n      this.message.showErrorMSG('檔案下載失敗');\n    }\n  }\n  // 從後端取得檔案 blob\n  getFileFromServer(relativePath, fileName, displayName, isImage, isPdf, isCad) {\n    this.fileService.getFile(relativePath, fileName).subscribe({\n      next: blob => {\n        const url = URL.createObjectURL(blob);\n        if (isImage) {\n          // 圖片預覽\n          this.openImagePreview(url, displayName);\n        } else if (isPdf) {\n          // PDF 檔案另開視窗顯示\n          this.openPdfInNewWindow(url, displayName);\n        } else {\n          // 其他檔案直接下載\n          this.downloadBlobFile(blob, displayName);\n        }\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\n      },\n      error: error => {\n        console.error('取得檔案失敗:', error);\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\n      }\n    });\n  }\n  // 在新視窗中開啟 PDF\n  openPdfInNewWindow(blobUrl, fileName) {\n    try {\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { margin: 0; padding: 0; }\n                iframe { width: 100vw; height: 100vh; border: none; }\n              </style>\n            </head>\n            <body>\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      } else {\n        // 如果彈出視窗被阻擋，直接開啟 URL\n        window.location.href = blobUrl;\n      }\n    } catch (error) {\n      console.error('開啟 PDF 視窗失敗:', error);\n      // 後備方案：直接開啟 URL\n      window.open(blobUrl, '_blank');\n    }\n  }\n  // 下載 blob 檔案\n  downloadBlobFile(blob, fileName) {\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = fileName;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    // 清理 URL\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\n  }\n  // 判斷檔案是否為圖片（根據檔名）\n  isImageFile(fileName) {\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    return imageExtensions.includes(extension || '');\n  }\n  // 取得正確的圖片 src\n  getImageSrc(file) {\n    const fileName = file.CFileName || file.fileName || '';\n    if (!fileName) {\n      return '';\n    }\n    // 如果檔案有 data 屬性（base64格式），直接使用\n    if (file.data) {\n      return file.data;\n    }\n    // 如果有相對路徑和檔案名，構造 API URL\n    if (file.relativePath && file.fileName) {\n      return `${environment.BASE_WITHOUT_FILEROOT}/api/File/GetFile?relativePath=${encodeURIComponent(file.relativePath)}&fileName=${encodeURIComponent(file.fileName)}`;\n    }\n    // 如果是現有檔案且有 CFile 路徑\n    if (file.CFile && !file.CFile.startsWith('data:')) {\n      return `${environment.BASE_WITHOUT_FILEROOT}${file.CFile}`;\n    }\n    // 後備方案：空字串\n    return '';\n  }\n  // HTML 模板中使用的方法\n  isImageByName(fileName) {\n    return this.isImageFile(fileName);\n  }\n  isPDFByName(fileName) {\n    return this.isPDFString(fileName);\n  }\n  isCadByName(fileName) {\n    return this.isCadString(fileName);\n  } // 圖片載入錯誤處理\n  onImageError(event, file) {\n    const fileName = file.CFileName || file.fileName || '檔案';\n    console.warn('圖片載入失敗:', fileName, file);\n    // 可以設置預設圖片或其他處理\n    event.target.style.display = 'none';\n    // 在圖片載入失敗時，顯示檔案類型圖示，但保留點擊事件\n    const container = event.target.parentElement;\n    if (container) {\n      const iconInfo = this.getFileTypeIcon(file);\n      // 創建新的內容並綁定點擊事件\n      const newContent = document.createElement('div');\n      newContent.className = `w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg ${iconInfo.bgColor}`;\n      newContent.innerHTML = `\n        <i class=\"${iconInfo.icon} text-4xl mb-2 ${iconInfo.color}\"></i>\n        <span class=\"text-xs font-medium ${iconInfo.color}\">${iconInfo.label}</span>\n      `;\n      // 綁定點擊事件\n      newContent.addEventListener('click', () => {\n        this.handleFileClick(file);\n      });\n      // 替換內容\n      container.innerHTML = '';\n      container.appendChild(newContent);\n    }\n  }\n  getAcceptedFormatsText() {\n    const extensions = this.config.acceptAttribute?.split(',').map(type => {\n      if (type.includes('pdf')) return 'PDF';\n      if (type.includes('jpeg') || type.includes('jpg')) return 'JPG';\n      if (type.includes('png')) return 'PNG';\n      if (type.includes('.dwg')) return 'DWG';\n      if (type.includes('.dxf')) return 'DXF';\n      return type;\n    }).join('、');\n    return `僅限${extensions}格式`;\n  }\n  // 在瀏覽器中打開 PDF（已廢棄，統一使用 getFile API）\n  openPdfInBrowser(fileUrl) {\n    console.warn('openPdfInBrowser 方法已廢棄，請使用 getFileFromServer');\n    this.message.showErrorMSG('PDF 檢視功能已更新，請重新操作');\n  }\n  // 直接下載檔案（已廢棄，統一使用 getFile API）\n  downloadFileDirectly(fileUrl, fileName) {\n    console.warn('downloadFileDirectly 方法已廢棄，請使用 getFileFromServer');\n    this.message.showErrorMSG('檔案下載功能已更新，請重新操作');\n  }\n  // 開啟圖片預覽\n  openImagePreview(imageUrl, fileName) {\n    try {\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body {\n                  margin: 0;\n                  padding: 20px;\n                  background-color: #f5f5f5;\n                  display: flex;\n                  justify-content: center;\n                  align-items: center;\n                  min-height: 100vh;\n                }\n                img {\n                  max-width: 100%;\n                  max-height: 100vh;\n                  object-fit: contain;\n                  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n                  background-color: white;\n                }\n                .title {\n                  position: fixed;\n                  top: 10px;\n                  left: 20px;\n                  background: rgba(0, 0, 0, 0.7);\n                  color: white;\n                  padding: 10px;\n                  border-radius: 4px;\n                  font-family: Arial, sans-serif;\n                }\n              </style>\n            </head>\n            <body>\n              <div class=\"title\">${fileName}</div>\n              <img src=\"${imageUrl}\" alt=\"${fileName}\" />\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      }\n    } catch (error) {\n      console.error('開啟圖片預覽失敗:', error);\n      window.open(imageUrl, '_blank');\n    }\n  }\n  openNewTab(url) {\n    if (url) {\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\n      window.open(fullUrl, '_blank');\n    }\n  }\n  // 取得檔案類型圖示資訊\n  getFileTypeIcon(file) {\n    const fileName = file.CFileName || file.fileName || '';\n    const fileType = file.CFileType;\n    const extension = fileName.split('.').pop()?.toLowerCase() || '';\n    // 圖片類型\n    if (fileType === 2 || this.isImageFile(fileName)) {\n      if (extension === 'jpg' || extension === 'jpeg') {\n        return {\n          icon: 'fa fa-file-image-o',\n          color: 'text-blue-600',\n          bgColor: 'bg-blue-50',\n          label: 'JPG'\n        };\n      } else if (extension === 'png') {\n        return {\n          icon: 'fa fa-file-image-o',\n          color: 'text-purple-600',\n          bgColor: 'bg-purple-50',\n          label: 'PNG'\n        };\n      } else {\n        return {\n          icon: 'fa fa-file-image-o',\n          color: 'text-blue-600',\n          bgColor: 'bg-blue-50',\n          label: '圖片'\n        };\n      }\n    }\n    // PDF類型\n    if (fileType === 1 || this.isPDFString(fileName)) {\n      return {\n        icon: 'fa fa-file-pdf-o',\n        color: 'text-red-600',\n        bgColor: 'bg-red-50',\n        label: 'PDF'\n      };\n    }\n    // CAD類型\n    if (fileType === 3 || this.isCadString(fileName)) {\n      if (extension === 'dwg') {\n        return {\n          icon: 'fa fa-cube',\n          color: 'text-green-600',\n          bgColor: 'bg-green-50',\n          label: 'DWG'\n        };\n      } else if (extension === 'dxf') {\n        return {\n          icon: 'fa fa-cube',\n          color: 'text-emerald-600',\n          bgColor: 'bg-emerald-50',\n          label: 'DXF'\n        };\n      } else {\n        return {\n          icon: 'fa fa-cube',\n          color: 'text-green-600',\n          bgColor: 'bg-green-50',\n          label: 'CAD'\n        };\n      }\n    }\n    // Office文件類型\n    if (extension === 'doc' || extension === 'docx') {\n      return {\n        icon: 'fa fa-file-word-o',\n        color: 'text-blue-700',\n        bgColor: 'bg-blue-50',\n        label: 'Word'\n      };\n    }\n    if (extension === 'xls' || extension === 'xlsx') {\n      return {\n        icon: 'fa fa-file-excel-o',\n        color: 'text-green-700',\n        bgColor: 'bg-green-50',\n        label: 'Excel'\n      };\n    }\n    if (extension === 'ppt' || extension === 'pptx') {\n      return {\n        icon: 'fa fa-file-powerpoint-o',\n        color: 'text-orange-600',\n        bgColor: 'bg-orange-50',\n        label: 'PPT'\n      };\n    }\n    // 壓縮檔案\n    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {\n      return {\n        icon: 'fa fa-file-archive-o',\n        color: 'text-yellow-600',\n        bgColor: 'bg-yellow-50',\n        label: extension.toUpperCase()\n      };\n    }\n    // 文字檔案\n    if (['txt', 'log', 'md'].includes(extension)) {\n      return {\n        icon: 'fa fa-file-text-o',\n        color: 'text-gray-600',\n        bgColor: 'bg-gray-50',\n        label: extension.toUpperCase()\n      };\n    }\n    // 預設檔案類型\n    return {\n      icon: 'fa fa-file-o',\n      color: 'text-gray-500',\n      bgColor: 'bg-gray-50',\n      label: extension ? extension.toUpperCase() : '檔案'\n    };\n  }\n  // 取得檔案大小格式化字串\n  getFileSize(file) {\n    let size = 0;\n    if (file.size) {\n      size = file.size;\n    } else if (file.CFileBlood) {\n      // base64 字串大小估算 (約為實際檔案大小的 4/3)\n      size = Math.floor(file.CFileBlood.length * 0.75);\n    }\n    if (size === 0) return '';\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let unitIndex = 0;\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;\n  }\n};\n__decorate([ViewChild('fileInput')], FileUploadComponent.prototype, \"fileInput\", void 0);\n__decorate([Input()], FileUploadComponent.prototype, \"config\", void 0);\n__decorate([Input()], FileUploadComponent.prototype, \"currentFileName\", void 0);\n__decorate([Input()], FileUploadComponent.prototype, \"currentFileUrl\", void 0);\n__decorate([Input()], FileUploadComponent.prototype, \"labelMinWidth\", void 0);\n__decorate([Input()], FileUploadComponent.prototype, \"fileList\", void 0);\n__decorate([Input()], FileUploadComponent.prototype, \"existingFiles\", void 0);\n__decorate([Output()], FileUploadComponent.prototype, \"fileSelected\", void 0);\n__decorate([Output()], FileUploadComponent.prototype, \"fileCleared\", void 0);\n__decorate([Output()], FileUploadComponent.prototype, \"nameAutoFilled\", void 0);\n__decorate([Output()], FileUploadComponent.prototype, \"multiFileSelected\", void 0);\n__decorate([Output()], FileUploadComponent.prototype, \"fileRemoved\", void 0);\nFileUploadComponent = __decorate([Component({\n  selector: 'app-file-upload',\n  templateUrl: './file-upload.component.html',\n  styleUrls: ['./file-upload.component.css'],\n  standalone: true,\n  imports: [CommonModule]\n})], FileUploadComponent);\nexport { FileUploadComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "ViewChild", "CommonModule", "BaseComponent", "EnumFileType", "environment", "FileUploadComponent", "constructor", "_allow", "message", "fileService", "config", "acceptedTypes", "acceptedFileRegex", "acceptAttribute", "label", "helpText", "required", "disabled", "autoFillName", "buttonText", "buttonIcon", "maxFileSize", "multiple", "showPreview", "currentFileName", "currentFileUrl", "labelMinWidth", "fileList", "existingFiles", "fileSelected", "fileCleared", "nameAutoFilled", "multiFileSelected", "fileRemoved", "fileName", "ngOnInit", "onFileSelected", "event", "files", "target", "length", "handleMultipleFiles", "handleSingleFile", "file", "test", "name", "acceptedFormats", "getAcceptedFormatsText", "showErrorMSG", "maxSizeInBytes", "size", "fileNameWithoutExtension", "substring", "lastIndexOf", "emit", "reader", "FileReader", "onload", "e", "fileType", "type", "startsWith", "JPG", "toLowerCase", "includes", "PDF", "result", "CName", "CFile", "toString", "split", "Cimg", "CFileUpload", "CFileType", "fileInput", "nativeElement", "value", "readAsDataURL", "allowedTypes", "fileRegex", "firstFile", "newFiles", "processedCount", "i", "match", "endsWith", "push", "data", "CFileBlood", "removeBase64Prefix", "CFileName", "clearFile", "openFile", "url", "fullUrl", "BASE_WITHOUT_FILEROOT", "window", "open", "removeFile", "index", "splice", "base64String", "prefixIndex", "indexOf", "isImage", "isCad", "isPdf", "isPDFString", "str", "isCadString", "lowerStr", "handleFileClick", "console", "log", "displayName", "isImageByName", "isImageFile", "isPdfByName", "isCadByName", "relativePath", "serverFileName", "getFileFromServer", "error", "handleLocalFile", "handleLocalFileData", "warn", "downloadBase64File", "base64Data", "byteCharacters", "atob", "byteNumbers", "Array", "charCodeAt", "byteArray", "Uint8Array", "blob", "Blob", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "revokeObjectURL", "getFile", "subscribe", "next", "openImagePreview", "openPdfInNewWindow", "downloadBlobFile", "blobUrl", "newWindow", "write", "close", "location", "imageExtensions", "extension", "pop", "getImageSrc", "encodeURIComponent", "isPDFByName", "onImageError", "style", "display", "container", "parentElement", "iconInfo", "getFileTypeIcon", "newContent", "className", "bgColor", "innerHTML", "icon", "color", "addEventListener", "extensions", "map", "join", "openPdfInBrowser", "fileUrl", "downloadFileDirectly", "imageUrl", "openNewTab", "toUpperCase", "getFileSize", "Math", "floor", "units", "unitIndex", "toFixed", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\file-upload\\file-upload.component.ts"], "sourcesContent": ["import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { environment } from 'src/environments/environment';\r\nimport { FileService } from 'src/services/File.service';\r\n\r\nexport interface FileUploadResult {\r\n  CName: string;\r\n  CFile: string;\r\n  Cimg: File;\r\n  CFileUpload: File;\r\n  CFileType: number;\r\n  fileName: string;\r\n}\r\n\r\nexport interface MultiFileUploadResult {\r\n  data: string;\r\n  CFileBlood: string;\r\n  CFileName: string;\r\n  CFileType: number;\r\n}\r\n\r\nexport interface FileUploadConfig {\r\n  acceptedTypes?: string[];\r\n  acceptedFileRegex?: RegExp;\r\n  acceptAttribute?: string;\r\n  label?: string;\r\n  helpText?: string;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  autoFillName?: boolean;\r\n  buttonText?: string;\r\n  buttonIcon?: string;\r\n  maxFileSize?: number; // in MB\r\n  multiple?: boolean; // 是否支援多檔案上傳\r\n  showPreview?: boolean; // 是否顯示檔案預覽\r\n}\r\n\r\n@Component({\r\n  selector: 'app-file-upload',\r\n  templateUrl: './file-upload.component.html',\r\n  styleUrls: ['./file-upload.component.css'],\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n})\r\nexport class FileUploadComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  @Input() config: FileUploadConfig = {\r\n    acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\r\n    acceptedFileRegex: /pdf|jpg|jpeg|png/i,\r\n    acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\r\n    label: '上傳檔案',\r\n    helpText: '*請上傳PDF格式',\r\n    required: false,\r\n    disabled: false,\r\n    autoFillName: false,\r\n    buttonText: '上傳',\r\n    buttonIcon: 'fa-solid fa-cloud-arrow-up',\r\n    maxFileSize: 10,\r\n    multiple: false,\r\n    showPreview: false\r\n  };\r\n  @Input() currentFileName: string | null = null;\r\n  @Input() currentFileUrl: string | null = null;\r\n  @Input() labelMinWidth: string = '172px';\r\n  @Input() fileList: MultiFileUploadResult[] = []; // 用於多檔案模式的檔案列表\r\n  @Input() existingFiles: any[] = []; // 已存在的檔案列表（編輯模式用）\r\n\r\n  @Output() fileSelected = new EventEmitter<FileUploadResult>();\r\n  @Output() fileCleared = new EventEmitter<void>();\r\n  @Output() nameAutoFilled = new EventEmitter<string>();\r\n  @Output() multiFileSelected = new EventEmitter<MultiFileUploadResult[]>(); // 多檔案選擇事件\r\n  @Output() fileRemoved = new EventEmitter<number>(); // 檔案移除事件\r\n\r\n  fileName: string | null = null;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private message: MessageService,\r\n    private fileService: FileService\r\n  ) {\r\n    super(_allow);\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.fileName = this.currentFileName;    // 設置預設配置\r\n    this.config = {\r\n      ...{\r\n        acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\r\n        acceptedFileRegex: /pdf|jpg|jpeg|png/i,\r\n        acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\r\n        label: '上傳檔案',\r\n        helpText: '*請上傳PDF格式',\r\n        required: false,\r\n        disabled: false,\r\n        autoFillName: false,\r\n        buttonText: '上傳',\r\n        buttonIcon: 'fa-solid fa-cloud-arrow-up',\r\n        maxFileSize: 10,\r\n        multiple: false,\r\n        showPreview: false\r\n      },\r\n      ...this.config\r\n    };\r\n  }\r\n  onFileSelected(event: any) {\r\n    const files: FileList = event.target.files;\r\n\r\n    if (!files || files.length === 0) {\r\n      return;\r\n    }\r\n\r\n    if (this.config.multiple) {\r\n      this.handleMultipleFiles(files);\r\n    } else {\r\n      this.handleSingleFile(files[0]);\r\n    }\r\n  }\r\n\r\n  private handleSingleFile(file: File) {\r\n    // 檔案格式檢查\r\n    if (!this.config.acceptedFileRegex!.test(file.name)) {\r\n      const acceptedFormats = this.getAcceptedFormatsText();\r\n      this.message.showErrorMSG(`檔案格式錯誤，${acceptedFormats}`);\r\n      return;\r\n    }\r\n\r\n    // 檔案大小檢查\r\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\r\n    if (file.size > maxSizeInBytes) {\r\n      this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);\r\n      return;\r\n    }\r\n\r\n    this.fileName = file.name;\r\n\r\n    // 自動填入名稱功能\r\n    if (this.config.autoFillName) {\r\n      const fileNameWithoutExtension = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;\r\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\r\n    }\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = (e: any) => {\r\n      // 判斷檔案類型\r\n      let fileType: number;\r\n      if (file.type.startsWith('image/')) {\r\n        fileType = EnumFileType.JPG; // 圖片\r\n      } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\r\n        fileType = 3; // CAD檔案\r\n      } else {\r\n        fileType = EnumFileType.PDF; // PDF\r\n      }\r\n\r\n      const result: FileUploadResult = {\r\n        CName: file.name,\r\n        CFile: e.target?.result?.toString().split(',')[1],\r\n        Cimg: file.name.includes('pdf') ? file : file,\r\n        CFileUpload: file,\r\n        CFileType: fileType,\r\n        fileName: file.name\r\n      };\r\n\r\n      this.fileSelected.emit(result);\r\n\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null;\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  private handleMultipleFiles(files: FileList) {\r\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\r\n    const fileRegex = this.config.acceptedFileRegex || /pdf|jpg|jpeg|png|dwg|dxf/i;\r\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\r\n\r\n    // 如果是第一次選擇檔案且支援自動填入名稱\r\n    if (this.config.autoFillName && files.length > 0) {\r\n      const firstFile = files[0];\r\n      const fileName = firstFile.name;\r\n      const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\r\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\r\n    }\r\n\r\n    const newFiles: MultiFileUploadResult[] = [];\r\n    let processedCount = 0;\r\n\r\n    for (let i = 0; i < files.length; i++) {\r\n      const file = files[i];\r\n\r\n      // 檔案格式檢查\r\n      if (!fileRegex.test(file.name)) {\r\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\r\n        processedCount++;\r\n        continue;\r\n      }\r\n\r\n      // 檔案大小檢查\r\n      if (file.size > maxSizeInBytes) {\r\n        this.message.showErrorMSG(`檔案 ${file.name} 大小超過限制（${this.config.maxFileSize}MB）`);\r\n        processedCount++;\r\n        continue;\r\n      }\r\n\r\n      if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\r\n        const reader = new FileReader();\r\n        reader.onload = (e: any) => {\r\n          // 判斷檔案類型\r\n          let fileType = 1; // 預設為其他\r\n          const fileName = file.name.toLowerCase();\r\n\r\n          if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\r\n            fileType = 2; // 圖片\r\n          } else if (fileName.endsWith('.pdf')) {\r\n            fileType = 1; // PDF\r\n          } else if (fileName.match(/\\.(dwg|dxf)$/)) {\r\n            fileType = 3; // CAD\r\n          }\r\n\r\n          newFiles.push({\r\n            data: e.target.result,\r\n            CFileBlood: this.removeBase64Prefix(e.target.result),\r\n            CFileName: file.name,\r\n            CFileType: fileType\r\n          });\r\n\r\n          processedCount++;\r\n          if (processedCount === files.length) {\r\n            this.fileList = [...this.fileList, ...newFiles];\r\n            this.multiFileSelected.emit(this.fileList);\r\n\r\n            if (this.fileInput) {\r\n              this.fileInput.nativeElement.value = null;\r\n            }\r\n          }\r\n        };\r\n        reader.readAsDataURL(file);\r\n      } else {\r\n        processedCount++;\r\n      }\r\n    }\r\n  }\r\n  clearFile() {\r\n    this.fileName = null;\r\n    this.fileCleared.emit();\r\n    if (this.fileInput) {\r\n      this.fileInput.nativeElement.value = null;\r\n    }\r\n  }\r\n  openFile(url: string) {\r\n    if (url) {\r\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\r\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\r\n      window.open(fullUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  removeFile(index: number) {\r\n    this.fileList.splice(index, 1);\r\n    this.fileRemoved.emit(index);\r\n    this.multiFileSelected.emit(this.fileList);\r\n  }\r\n\r\n  private removeBase64Prefix(base64String: string): string {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  isImage(fileType: number): boolean {\r\n    return fileType === 2;\r\n  }\r\n\r\n  isCad(fileType: number): boolean {\r\n    return fileType === 3;\r\n  }\r\n\r\n  isPdf(fileType: number): boolean {\r\n    return fileType === 1;\r\n  }\r\n\r\n  isPDFString(str: string): boolean {\r\n    if (str) {\r\n      return str.toLowerCase().endsWith(\".pdf\");\r\n    }\r\n    return false;\r\n  }\r\n\r\n  isCadString(str: string): boolean {\r\n    if (str) {\r\n      const lowerStr = str.toLowerCase();\r\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n    }\r\n    return false;\r\n  }  // 處理檔案點擊事件\r\n  handleFileClick(file: any) {\r\n    console.log('檔案點擊事件觸發:', file);\r\n    const fileName = file.CFileName || file.fileName || '';\r\n    const displayName = file.CFileName || fileName;\r\n\r\n    // 判斷檔案類型\r\n    const isImageByName = this.isImageFile(displayName);\r\n    const isPdfByName = this.isPDFString(displayName);\r\n    const isCadByName = this.isCadString(displayName);\r\n\r\n    console.log('檔案類型判斷:', { isImageByName, isPdfByName, isCadByName, displayName });\r\n\r\n    // 統一使用 GetFile API 取得檔案 blob\r\n    const relativePath = file.relativePath || file.CFile;\r\n    const serverFileName = file.fileName || file.CFileName;\r\n\r\n    console.log('檔案路徑資訊:', { relativePath, serverFileName });\r\n\r\n    if (relativePath && serverFileName) {\r\n      console.log('使用 GetFile API 取得檔案 blob');\r\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\r\n    } else {\r\n      // 如果沒有路徑資訊，顯示錯誤訊息\r\n      console.error('檔案缺少路徑資訊，無法使用 getFile API:', file);\r\n      this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\r\n    }\r\n  }\r\n\r\n  // 處理本地檔案的後備方法 - 已廢棄，統一使用 getFile API\r\n  private handleLocalFile(file: any, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    const fileName = file.CFileName || file.fileName || '';\r\n    console.error('檔案缺少必要的路徑資訊，無法使用 getFile API:', file);\r\n    this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\r\n  }\r\n  // 處理本地檔案資料（新上傳的檔案）- 已廢棄，統一使用 getFile API\r\n  private handleLocalFileData(data: string, fileName: string, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    console.warn('handleLocalFileData 方法已廢棄，統一使用 getFile API');\r\n    this.message.showErrorMSG('檔案處理功能已更新，請重新操作');\r\n\r\n    /* 原有邏輯已移除，統一使用 getFile API\r\n    if (isImage) {\r\n      // 圖片預覽\r\n      this.openImagePreview(data, fileName);\r\n    } else if (isPdf) {\r\n      // PDF 檔案另開視窗顯示\r\n      this.openPdfInNewWindow(data, fileName);\r\n    } else {\r\n      // 其他檔案（如CAD）轉換為blob並下載\r\n      this.downloadBase64File(data, fileName);\r\n    }\r\n    */\r\n  }\r\n\r\n  // 下載base64檔案\r\n  private downloadBase64File(base64Data: string, fileName: string) {\r\n    try {\r\n      // 移除data URL前綴（如果存在）\r\n      const base64String = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\r\n\r\n      // 轉換為blob\r\n      const byteCharacters = atob(base64String);\r\n      const byteNumbers = new Array(byteCharacters.length);\r\n      for (let i = 0; i < byteCharacters.length; i++) {\r\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      const blob = new Blob([byteArray]);\r\n\r\n      // 創建下載連結\r\n      const url = URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n\r\n      // 清理URL\r\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n    } catch (error) {\r\n      console.error('下載檔案失敗:', error);\r\n      this.message.showErrorMSG('檔案下載失敗');\r\n    }\r\n  }\r\n\r\n  // 從後端取得檔案 blob\r\n  private getFileFromServer(relativePath: string, fileName: string, displayName: string, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    this.fileService.getFile(relativePath, fileName).subscribe({\r\n      next: (blob: Blob) => {\r\n        const url = URL.createObjectURL(blob);\r\n\r\n        if (isImage) {\r\n          // 圖片預覽\r\n          this.openImagePreview(url, displayName);\r\n        } else if (isPdf) {\r\n          // PDF 檔案另開視窗顯示\r\n          this.openPdfInNewWindow(url, displayName);\r\n        } else {\r\n          // 其他檔案直接下載\r\n          this.downloadBlobFile(blob, displayName);\r\n        }\r\n\r\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\r\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\r\n      },\r\n      error: (error) => {\r\n        console.error('取得檔案失敗:', error);\r\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\r\n      }\r\n    });\r\n  }\r\n\r\n  // 在新視窗中開啟 PDF\r\n  private openPdfInNewWindow(blobUrl: string, fileName: string) {\r\n    try {\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body { margin: 0; padding: 0; }\r\n                iframe { width: 100vw; height: 100vh; border: none; }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        // 如果彈出視窗被阻擋，直接開啟 URL\r\n        window.location.href = blobUrl;\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟 PDF 視窗失敗:', error);\r\n      // 後備方案：直接開啟 URL\r\n      window.open(blobUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  // 下載 blob 檔案\r\n  private downloadBlobFile(blob: Blob, fileName: string) {\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = fileName;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n\r\n    // 清理 URL\r\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n  }\r\n  // 判斷檔案是否為圖片（根據檔名）\r\n  isImageFile(fileName: string): boolean {\r\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    return imageExtensions.includes(extension || '');\r\n  }\r\n  // 取得正確的圖片 src\r\n  getImageSrc(file: any): string {\r\n    const fileName = file.CFileName || file.fileName || '';\r\n\r\n    if (!fileName) {\r\n      return '';\r\n    }\r\n\r\n    // 如果檔案有 data 屬性（base64格式），直接使用\r\n    if (file.data) {\r\n      return file.data;\r\n    }\r\n\r\n    // 如果有相對路徑和檔案名，構造 API URL\r\n    if (file.relativePath && file.fileName) {\r\n      return `${environment.BASE_WITHOUT_FILEROOT}/api/File/GetFile?relativePath=${encodeURIComponent(file.relativePath)}&fileName=${encodeURIComponent(file.fileName)}`;\r\n    }\r\n\r\n    // 如果是現有檔案且有 CFile 路徑\r\n    if (file.CFile && !file.CFile.startsWith('data:')) {\r\n      return `${environment.BASE_WITHOUT_FILEROOT}${file.CFile}`;\r\n    }\r\n\r\n    // 後備方案：空字串\r\n    return '';\r\n  }\r\n\r\n  // HTML 模板中使用的方法\r\n  isImageByName(fileName: string): boolean {\r\n    return this.isImageFile(fileName);\r\n  }\r\n\r\n  isPDFByName(fileName: string): boolean {\r\n    return this.isPDFString(fileName);\r\n  }\r\n\r\n  isCadByName(fileName: string): boolean {\r\n    return this.isCadString(fileName);\r\n  }  // 圖片載入錯誤處理\r\n  onImageError(event: any, file: any) {\r\n    const fileName = file.CFileName || file.fileName || '檔案';\r\n    console.warn('圖片載入失敗:', fileName, file);\r\n    // 可以設置預設圖片或其他處理\r\n    event.target.style.display = 'none';\r\n\r\n    // 在圖片載入失敗時，顯示檔案類型圖示，但保留點擊事件\r\n    const container = event.target.parentElement;\r\n    if (container) {\r\n      const iconInfo = this.getFileTypeIcon(file);\r\n\r\n      // 創建新的內容並綁定點擊事件\r\n      const newContent = document.createElement('div');\r\n      newContent.className = `w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg ${iconInfo.bgColor}`;\r\n      newContent.innerHTML = `\r\n        <i class=\"${iconInfo.icon} text-4xl mb-2 ${iconInfo.color}\"></i>\r\n        <span class=\"text-xs font-medium ${iconInfo.color}\">${iconInfo.label}</span>\r\n      `;\r\n\r\n      // 綁定點擊事件\r\n      newContent.addEventListener('click', () => {\r\n        this.handleFileClick(file);\r\n      });\r\n\r\n      // 替換內容\r\n      container.innerHTML = '';\r\n      container.appendChild(newContent);\r\n    }\r\n  }\r\n\r\n  private getAcceptedFormatsText(): string {\r\n    const extensions = this.config.acceptAttribute?.split(',').map(type => {\r\n      if (type.includes('pdf')) return 'PDF';\r\n      if (type.includes('jpeg') || type.includes('jpg')) return 'JPG';\r\n      if (type.includes('png')) return 'PNG';\r\n      if (type.includes('.dwg')) return 'DWG';\r\n      if (type.includes('.dxf')) return 'DXF';\r\n      return type;\r\n    }).join('、');\r\n\r\n    return `僅限${extensions}格式`;\r\n  }\r\n  // 在瀏覽器中打開 PDF（已廢棄，統一使用 getFile API）\r\n  openPdfInBrowser(fileUrl: string) {\r\n    console.warn('openPdfInBrowser 方法已廢棄，請使用 getFileFromServer');\r\n    this.message.showErrorMSG('PDF 檢視功能已更新，請重新操作');\r\n  }\r\n  // 直接下載檔案（已廢棄，統一使用 getFile API）\r\n  downloadFileDirectly(fileUrl: string, fileName: string) {\r\n    console.warn('downloadFileDirectly 方法已廢棄，請使用 getFileFromServer');\r\n    this.message.showErrorMSG('檔案下載功能已更新，請重新操作');\r\n  }\r\n\r\n  // 開啟圖片預覽\r\n  private openImagePreview(imageUrl: string, fileName: string) {\r\n    try {\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body {\r\n                  margin: 0;\r\n                  padding: 20px;\r\n                  background-color: #f5f5f5;\r\n                  display: flex;\r\n                  justify-content: center;\r\n                  align-items: center;\r\n                  min-height: 100vh;\r\n                }\r\n                img {\r\n                  max-width: 100%;\r\n                  max-height: 100vh;\r\n                  object-fit: contain;\r\n                  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n                  background-color: white;\r\n                }\r\n                .title {\r\n                  position: fixed;\r\n                  top: 10px;\r\n                  left: 20px;\r\n                  background: rgba(0, 0, 0, 0.7);\r\n                  color: white;\r\n                  padding: 10px;\r\n                  border-radius: 4px;\r\n                  font-family: Arial, sans-serif;\r\n                }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <div class=\"title\">${fileName}</div>\r\n              <img src=\"${imageUrl}\" alt=\"${fileName}\" />\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟圖片預覽失敗:', error);\r\n      window.open(imageUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  openNewTab(url: string) {\r\n    if (url) {\r\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\r\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\r\n      window.open(fullUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  // 取得檔案類型圖示資訊\r\n  getFileTypeIcon(file: any): { icon: string, color: string, bgColor: string, label: string } {\r\n    const fileName = file.CFileName || file.fileName || '';\r\n    const fileType = file.CFileType;\r\n    const extension = fileName.split('.').pop()?.toLowerCase() || '';\r\n\r\n    // 圖片類型\r\n    if (fileType === 2 || this.isImageFile(fileName)) {\r\n      if (extension === 'jpg' || extension === 'jpeg') {\r\n        return {\r\n          icon: 'fa fa-file-image-o',\r\n          color: 'text-blue-600',\r\n          bgColor: 'bg-blue-50',\r\n          label: 'JPG'\r\n        };\r\n      } else if (extension === 'png') {\r\n        return {\r\n          icon: 'fa fa-file-image-o',\r\n          color: 'text-purple-600',\r\n          bgColor: 'bg-purple-50',\r\n          label: 'PNG'\r\n        };\r\n      } else {\r\n        return {\r\n          icon: 'fa fa-file-image-o',\r\n          color: 'text-blue-600',\r\n          bgColor: 'bg-blue-50',\r\n          label: '圖片'\r\n        };\r\n      }\r\n    }\r\n\r\n    // PDF類型\r\n    if (fileType === 1 || this.isPDFString(fileName)) {\r\n      return {\r\n        icon: 'fa fa-file-pdf-o',\r\n        color: 'text-red-600',\r\n        bgColor: 'bg-red-50',\r\n        label: 'PDF'\r\n      };\r\n    }\r\n\r\n    // CAD類型\r\n    if (fileType === 3 || this.isCadString(fileName)) {\r\n      if (extension === 'dwg') {\r\n        return {\r\n          icon: 'fa fa-cube',\r\n          color: 'text-green-600',\r\n          bgColor: 'bg-green-50',\r\n          label: 'DWG'\r\n        };\r\n      } else if (extension === 'dxf') {\r\n        return {\r\n          icon: 'fa fa-cube',\r\n          color: 'text-emerald-600',\r\n          bgColor: 'bg-emerald-50',\r\n          label: 'DXF'\r\n        };\r\n      } else {\r\n        return {\r\n          icon: 'fa fa-cube',\r\n          color: 'text-green-600',\r\n          bgColor: 'bg-green-50',\r\n          label: 'CAD'\r\n        };\r\n      }\r\n    }\r\n\r\n    // Office文件類型\r\n    if (extension === 'doc' || extension === 'docx') {\r\n      return {\r\n        icon: 'fa fa-file-word-o',\r\n        color: 'text-blue-700',\r\n        bgColor: 'bg-blue-50',\r\n        label: 'Word'\r\n      };\r\n    }\r\n\r\n    if (extension === 'xls' || extension === 'xlsx') {\r\n      return {\r\n        icon: 'fa fa-file-excel-o',\r\n        color: 'text-green-700',\r\n        bgColor: 'bg-green-50',\r\n        label: 'Excel'\r\n      };\r\n    }\r\n\r\n    if (extension === 'ppt' || extension === 'pptx') {\r\n      return {\r\n        icon: 'fa fa-file-powerpoint-o',\r\n        color: 'text-orange-600',\r\n        bgColor: 'bg-orange-50',\r\n        label: 'PPT'\r\n      };\r\n    }\r\n\r\n    // 壓縮檔案\r\n    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {\r\n      return {\r\n        icon: 'fa fa-file-archive-o',\r\n        color: 'text-yellow-600',\r\n        bgColor: 'bg-yellow-50',\r\n        label: extension.toUpperCase()\r\n      };\r\n    }\r\n\r\n    // 文字檔案\r\n    if (['txt', 'log', 'md'].includes(extension)) {\r\n      return {\r\n        icon: 'fa fa-file-text-o',\r\n        color: 'text-gray-600',\r\n        bgColor: 'bg-gray-50',\r\n        label: extension.toUpperCase()\r\n      };\r\n    }\r\n\r\n    // 預設檔案類型\r\n    return {\r\n      icon: 'fa fa-file-o',\r\n      color: 'text-gray-500',\r\n      bgColor: 'bg-gray-50',\r\n      label: extension ? extension.toUpperCase() : '檔案'\r\n    };\r\n  }\r\n\r\n  // 取得檔案大小格式化字串\r\n  getFileSize(file: any): string {\r\n    let size = 0;\r\n\r\n    if (file.size) {\r\n      size = file.size;\r\n    } else if (file.CFileBlood) {\r\n      // base64 字串大小估算 (約為實際檔案大小的 4/3)\r\n      size = Math.floor(file.CFileBlood.length * 0.75);\r\n    }\r\n\r\n    if (size === 0) return '';\r\n\r\n    const units = ['B', 'KB', 'MB', 'GB'];\r\n    let unitIndex = 0;\r\n\r\n    while (size >= 1024 && unitIndex < units.length - 1) {\r\n      size /= 1024;\r\n      unitIndex++;\r\n    }\r\n\r\n    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAcC,YAAY,EAAEC,KAAK,EAAUC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACrG,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,qCAAqC;AAEnE,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,WAAW,QAAQ,8BAA8B;AA0CnD,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAoB,SAAQH,aAAa;EA8BpDI,YACUC,MAAmB,EACnBC,OAAuB,EACvBC,WAAwB;IAEhC,KAAK,CAACF,MAAM,CAAC;IAJL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,WAAW,GAAXA,WAAW;IA/BZ,KAAAC,MAAM,GAAqB;MAClCC,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;MAC7DC,iBAAiB,EAAE,mBAAmB;MACtCC,eAAe,EAAE,wCAAwC;MACzDC,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE,WAAW;MACrBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,4BAA4B;MACxCC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE;KACd;IACQ,KAAAC,eAAe,GAAkB,IAAI;IACrC,KAAAC,cAAc,GAAkB,IAAI;IACpC,KAAAC,aAAa,GAAW,OAAO;IAC/B,KAAAC,QAAQ,GAA4B,EAAE,CAAC,CAAC;IACxC,KAAAC,aAAa,GAAU,EAAE,CAAC,CAAC;IAE1B,KAAAC,YAAY,GAAG,IAAIhC,YAAY,EAAoB;IACnD,KAAAiC,WAAW,GAAG,IAAIjC,YAAY,EAAQ;IACtC,KAAAkC,cAAc,GAAG,IAAIlC,YAAY,EAAU;IAC3C,KAAAmC,iBAAiB,GAAG,IAAInC,YAAY,EAA2B,CAAC,CAAC;IACjE,KAAAoC,WAAW,GAAG,IAAIpC,YAAY,EAAU,CAAC,CAAC;IAEpD,KAAAqC,QAAQ,GAAkB,IAAI;EAO9B;EAESC,QAAQA,CAAA;IACf,IAAI,CAACD,QAAQ,GAAG,IAAI,CAACV,eAAe,CAAC,CAAI;IACzC,IAAI,CAACd,MAAM,GAAG;MACZ,GAAG;QACDC,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;QAC7DC,iBAAiB,EAAE,mBAAmB;QACtCC,eAAe,EAAE,wCAAwC;QACzDC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,WAAW;QACrBC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,KAAK;QACfC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,4BAA4B;QACxCC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE;OACd;MACD,GAAG,IAAI,CAACb;KACT;EACH;EACA0B,cAAcA,CAACC,KAAU;IACvB,MAAMC,KAAK,GAAaD,KAAK,CAACE,MAAM,CAACD,KAAK;IAE1C,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;MAChC;IACF;IAEA,IAAI,IAAI,CAAC9B,MAAM,CAACY,QAAQ,EAAE;MACxB,IAAI,CAACmB,mBAAmB,CAACH,KAAK,CAAC;IACjC,CAAC,MAAM;MACL,IAAI,CAACI,gBAAgB,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC;IACjC;EACF;EAEQI,gBAAgBA,CAACC,IAAU;IACjC;IACA,IAAI,CAAC,IAAI,CAACjC,MAAM,CAACE,iBAAkB,CAACgC,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;MACnD,MAAMC,eAAe,GAAG,IAAI,CAACC,sBAAsB,EAAE;MACrD,IAAI,CAACvC,OAAO,CAACwC,YAAY,CAAC,UAAUF,eAAe,EAAE,CAAC;MACtD;IACF;IAEA;IACA,MAAMG,cAAc,GAAG,CAAC,IAAI,CAACvC,MAAM,CAACW,WAAW,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI;IACpE,IAAIsB,IAAI,CAACO,IAAI,GAAGD,cAAc,EAAE;MAC9B,IAAI,CAACzC,OAAO,CAACwC,YAAY,CAAC,YAAY,IAAI,CAACtC,MAAM,CAACW,WAAW,KAAK,CAAC;MACnE;IACF;IAEA,IAAI,CAACa,QAAQ,GAAGS,IAAI,CAACE,IAAI;IAEzB;IACA,IAAI,IAAI,CAACnC,MAAM,CAACQ,YAAY,EAAE;MAC5B,MAAMiC,wBAAwB,GAAGR,IAAI,CAACE,IAAI,CAACO,SAAS,CAAC,CAAC,EAAET,IAAI,CAACE,IAAI,CAACQ,WAAW,CAAC,GAAG,CAAC,CAAC,IAAIV,IAAI,CAACE,IAAI;MAChG,IAAI,CAACd,cAAc,CAACuB,IAAI,CAACH,wBAAwB,CAAC;IACpD;IAEA,MAAMI,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;MACzB;MACA,IAAIC,QAAgB;MACpB,IAAIhB,IAAI,CAACiB,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QAClCF,QAAQ,GAAGxD,YAAY,CAAC2D,GAAG,CAAC,CAAC;MAC/B,CAAC,MAAM,IAAInB,IAAI,CAACE,IAAI,CAACkB,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAIrB,IAAI,CAACE,IAAI,CAACkB,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC/FL,QAAQ,GAAG,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACLA,QAAQ,GAAGxD,YAAY,CAAC8D,GAAG,CAAC,CAAC;MAC/B;MAEA,MAAMC,MAAM,GAAqB;QAC/BC,KAAK,EAAExB,IAAI,CAACE,IAAI;QAChBuB,KAAK,EAAEV,CAAC,CAACnB,MAAM,EAAE2B,MAAM,EAAEG,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjDC,IAAI,EAAE5B,IAAI,CAACE,IAAI,CAACmB,QAAQ,CAAC,KAAK,CAAC,GAAGrB,IAAI,GAAGA,IAAI;QAC7C6B,WAAW,EAAE7B,IAAI;QACjB8B,SAAS,EAAEd,QAAQ;QACnBzB,QAAQ,EAAES,IAAI,CAACE;OAChB;MAED,IAAI,CAAChB,YAAY,CAACyB,IAAI,CAACY,MAAM,CAAC;MAE9B,IAAI,IAAI,CAACQ,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC3C;IACF,CAAC;IACDrB,MAAM,CAACsB,aAAa,CAAClC,IAAI,CAAC;EAC5B;EAEQF,mBAAmBA,CAACH,KAAe;IACzC,MAAMwC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,eAAe,EAAE,aAAa,CAAC;IAChJ,MAAMC,SAAS,GAAG,IAAI,CAACrE,MAAM,CAACE,iBAAiB,IAAI,2BAA2B;IAC9E,MAAMqC,cAAc,GAAG,CAAC,IAAI,CAACvC,MAAM,CAACW,WAAW,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI;IAEpE;IACA,IAAI,IAAI,CAACX,MAAM,CAACQ,YAAY,IAAIoB,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MAChD,MAAMwC,SAAS,GAAG1C,KAAK,CAAC,CAAC,CAAC;MAC1B,MAAMJ,QAAQ,GAAG8C,SAAS,CAACnC,IAAI;MAC/B,MAAMM,wBAAwB,GAAGjB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAElB,QAAQ,CAACmB,WAAW,CAAC,GAAG,CAAC,CAAC,IAAInB,QAAQ;MAC7F,IAAI,CAACH,cAAc,CAACuB,IAAI,CAACH,wBAAwB,CAAC;IACpD;IAEA,MAAM8B,QAAQ,GAA4B,EAAE;IAC5C,IAAIC,cAAc,GAAG,CAAC;IAEtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7C,KAAK,CAACE,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACrC,MAAMxC,IAAI,GAAGL,KAAK,CAAC6C,CAAC,CAAC;MAErB;MACA,IAAI,CAACJ,SAAS,CAACnC,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACrC,OAAO,CAACwC,YAAY,CAAC,kCAAkC,CAAC;QAC7DkC,cAAc,EAAE;QAChB;MACF;MAEA;MACA,IAAIvC,IAAI,CAACO,IAAI,GAAGD,cAAc,EAAE;QAC9B,IAAI,CAACzC,OAAO,CAACwC,YAAY,CAAC,MAAML,IAAI,CAACE,IAAI,WAAW,IAAI,CAACnC,MAAM,CAACW,WAAW,KAAK,CAAC;QACjF6D,cAAc,EAAE;QAChB;MACF;MAEA,IAAIJ,YAAY,CAACd,QAAQ,CAACrB,IAAI,CAACiB,IAAI,CAAC,IAAImB,SAAS,CAACnC,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;QACjE,MAAMU,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB;UACA,IAAIC,QAAQ,GAAG,CAAC,CAAC,CAAC;UAClB,MAAMzB,QAAQ,GAAGS,IAAI,CAACE,IAAI,CAACkB,WAAW,EAAE;UAExC,IAAI7B,QAAQ,CAACkD,KAAK,CAAC,mBAAmB,CAAC,EAAE;YACvCzB,QAAQ,GAAG,CAAC,CAAC,CAAC;UAChB,CAAC,MAAM,IAAIzB,QAAQ,CAACmD,QAAQ,CAAC,MAAM,CAAC,EAAE;YACpC1B,QAAQ,GAAG,CAAC,CAAC,CAAC;UAChB,CAAC,MAAM,IAAIzB,QAAQ,CAACkD,KAAK,CAAC,cAAc,CAAC,EAAE;YACzCzB,QAAQ,GAAG,CAAC,CAAC,CAAC;UAChB;UAEAsB,QAAQ,CAACK,IAAI,CAAC;YACZC,IAAI,EAAE7B,CAAC,CAACnB,MAAM,CAAC2B,MAAM;YACrBsB,UAAU,EAAE,IAAI,CAACC,kBAAkB,CAAC/B,CAAC,CAACnB,MAAM,CAAC2B,MAAM,CAAC;YACpDwB,SAAS,EAAE/C,IAAI,CAACE,IAAI;YACpB4B,SAAS,EAAEd;WACZ,CAAC;UAEFuB,cAAc,EAAE;UAChB,IAAIA,cAAc,KAAK5C,KAAK,CAACE,MAAM,EAAE;YACnC,IAAI,CAACb,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE,GAAGsD,QAAQ,CAAC;YAC/C,IAAI,CAACjD,iBAAiB,CAACsB,IAAI,CAAC,IAAI,CAAC3B,QAAQ,CAAC;YAE1C,IAAI,IAAI,CAAC+C,SAAS,EAAE;cAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;YAC3C;UACF;QACF,CAAC;QACDrB,MAAM,CAACsB,aAAa,CAAClC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACLuC,cAAc,EAAE;MAClB;IACF;EACF;EACAS,SAASA,CAAA;IACP,IAAI,CAACzD,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACJ,WAAW,CAACwB,IAAI,EAAE;IACvB,IAAI,IAAI,CAACoB,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;IAC3C;EACF;EACAgB,QAAQA,CAACC,GAAW;IAClB,IAAIA,GAAG,EAAE;MACP;MACA,MAAMC,OAAO,GAAGD,GAAG,CAAChC,UAAU,CAAC,MAAM,CAAC,GAAGgC,GAAG,GAAG,GAAGzF,WAAW,CAAC2F,qBAAqB,GAAGF,GAAG,EAAE;MAC3FG,MAAM,CAACC,IAAI,CAACH,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;EAEAI,UAAUA,CAACC,KAAa;IACtB,IAAI,CAACxE,QAAQ,CAACyE,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAC9B,IAAI,CAAClE,WAAW,CAACqB,IAAI,CAAC6C,KAAK,CAAC;IAC5B,IAAI,CAACnE,iBAAiB,CAACsB,IAAI,CAAC,IAAI,CAAC3B,QAAQ,CAAC;EAC5C;EAEQ8D,kBAAkBA,CAACY,YAAoB;IAC7C,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACjD,SAAS,CAACkD,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEAG,OAAOA,CAAC7C,QAAgB;IACtB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEA8C,KAAKA,CAAC9C,QAAgB;IACpB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEA+C,KAAKA,CAAC/C,QAAgB;IACpB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEAgD,WAAWA,CAACC,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,OAAOA,GAAG,CAAC7C,WAAW,EAAE,CAACsB,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEAwB,WAAWA,CAACD,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,MAAME,QAAQ,GAAGF,GAAG,CAAC7C,WAAW,EAAE;MAClC,OAAO+C,QAAQ,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAAIyB,QAAQ,CAACzB,QAAQ,CAAC,MAAM,CAAC;IAC/D;IACA,OAAO,KAAK;EACd,CAAC,CAAE;EACH0B,eAAeA,CAACpE,IAAS;IACvBqE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEtE,IAAI,CAAC;IAC9B,MAAMT,QAAQ,GAAGS,IAAI,CAAC+C,SAAS,IAAI/C,IAAI,CAACT,QAAQ,IAAI,EAAE;IACtD,MAAMgF,WAAW,GAAGvE,IAAI,CAAC+C,SAAS,IAAIxD,QAAQ;IAE9C;IACA,MAAMiF,aAAa,GAAG,IAAI,CAACC,WAAW,CAACF,WAAW,CAAC;IACnD,MAAMG,WAAW,GAAG,IAAI,CAACV,WAAW,CAACO,WAAW,CAAC;IACjD,MAAMI,WAAW,GAAG,IAAI,CAACT,WAAW,CAACK,WAAW,CAAC;IAEjDF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MAAEE,aAAa;MAAEE,WAAW;MAAEC,WAAW;MAAEJ;IAAW,CAAE,CAAC;IAEhF;IACA,MAAMK,YAAY,GAAG5E,IAAI,CAAC4E,YAAY,IAAI5E,IAAI,CAACyB,KAAK;IACpD,MAAMoD,cAAc,GAAG7E,IAAI,CAACT,QAAQ,IAAIS,IAAI,CAAC+C,SAAS;IAEtDsB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MAAEM,YAAY;MAAEC;IAAc,CAAE,CAAC;IAExD,IAAID,YAAY,IAAIC,cAAc,EAAE;MAClCR,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,IAAI,CAACQ,iBAAiB,CAACF,YAAY,EAAEC,cAAc,EAAEN,WAAW,EAAEC,aAAa,EAAEE,WAAW,EAAEC,WAAW,CAAC;IAC5G,CAAC,MAAM;MACL;MACAN,OAAO,CAACU,KAAK,CAAC,4BAA4B,EAAE/E,IAAI,CAAC;MACjD,IAAI,CAACnC,OAAO,CAACwC,YAAY,CAAC,kBAAkB,CAAC;IAC/C;EACF;EAEA;EACQ2E,eAAeA,CAAChF,IAAS,EAAE6D,OAAgB,EAAEE,KAAc,EAAED,KAAc;IACjF,MAAMvE,QAAQ,GAAGS,IAAI,CAAC+C,SAAS,IAAI/C,IAAI,CAACT,QAAQ,IAAI,EAAE;IACtD8E,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAE/E,IAAI,CAAC;IACpD,IAAI,CAACnC,OAAO,CAACwC,YAAY,CAAC,kBAAkB,CAAC;EAC/C;EACA;EACQ4E,mBAAmBA,CAACrC,IAAY,EAAErD,QAAgB,EAAEsE,OAAgB,EAAEE,KAAc,EAAED,KAAc;IAC1GO,OAAO,CAACa,IAAI,CAAC,4CAA4C,CAAC;IAC1D,IAAI,CAACrH,OAAO,CAACwC,YAAY,CAAC,iBAAiB,CAAC;IAE5C;;;;;;;;;;;;EAYF;EAEA;EACQ8E,kBAAkBA,CAACC,UAAkB,EAAE7F,QAAgB;IAC7D,IAAI;MACF;MACA,MAAMmE,YAAY,GAAG0B,UAAU,CAAC/D,QAAQ,CAAC,GAAG,CAAC,GAAG+D,UAAU,CAACzD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGyD,UAAU;MAErF;MACA,MAAMC,cAAc,GAAGC,IAAI,CAAC5B,YAAY,CAAC;MACzC,MAAM6B,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAACxF,MAAM,CAAC;MACpD,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,cAAc,CAACxF,MAAM,EAAE2C,CAAC,EAAE,EAAE;QAC9C+C,WAAW,CAAC/C,CAAC,CAAC,GAAG6C,cAAc,CAACI,UAAU,CAACjD,CAAC,CAAC;MAC/C;MACA,MAAMkD,SAAS,GAAG,IAAIC,UAAU,CAACJ,WAAW,CAAC;MAC7C,MAAMK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,SAAS,CAAC,CAAC;MAElC;MACA,MAAMxC,GAAG,GAAG4C,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;MACrC,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGjD,GAAG;MACf8C,IAAI,CAACI,QAAQ,GAAG7G,QAAQ;MACxB0G,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,EAAE;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAE/B;MACAS,UAAU,CAAC,MAAMX,GAAG,CAACY,eAAe,CAACxD,GAAG,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,IAAI,CAAClH,OAAO,CAACwC,YAAY,CAAC,QAAQ,CAAC;IACrC;EACF;EAEA;EACQyE,iBAAiBA,CAACF,YAAoB,EAAErF,QAAgB,EAAEgF,WAAmB,EAAEV,OAAgB,EAAEE,KAAc,EAAED,KAAc;IACrI,IAAI,CAAChG,WAAW,CAAC6I,OAAO,CAAC/B,YAAY,EAAErF,QAAQ,CAAC,CAACqH,SAAS,CAAC;MACzDC,IAAI,EAAGjB,IAAU,IAAI;QACnB,MAAM1C,GAAG,GAAG4C,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAErC,IAAI/B,OAAO,EAAE;UACX;UACA,IAAI,CAACiD,gBAAgB,CAAC5D,GAAG,EAAEqB,WAAW,CAAC;QACzC,CAAC,MAAM,IAAIR,KAAK,EAAE;UAChB;UACA,IAAI,CAACgD,kBAAkB,CAAC7D,GAAG,EAAEqB,WAAW,CAAC;QAC3C,CAAC,MAAM;UACL;UACA,IAAI,CAACyC,gBAAgB,CAACpB,IAAI,EAAErB,WAAW,CAAC;QAC1C;QAEA;QACAkC,UAAU,CAAC,MAAMX,GAAG,CAACY,eAAe,CAACxD,GAAG,CAAC,EAAE,KAAK,CAAC;MACnD,CAAC;MACD6B,KAAK,EAAGA,KAAK,IAAI;QACfV,OAAO,CAACU,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAAClH,OAAO,CAACwC,YAAY,CAAC,cAAc,CAAC;MAC3C;KACD,CAAC;EACJ;EAEA;EACQ0G,kBAAkBA,CAACE,OAAe,EAAE1H,QAAgB;IAC1D,IAAI;MACF,MAAM2H,SAAS,GAAG7D,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAI4D,SAAS,EAAE;QACbA,SAAS,CAACjB,QAAQ,CAACkB,KAAK,CAAC;;;uBAGV5H,QAAQ;;;;;;;6BAOF0H,OAAO;;;SAG3B,CAAC;QACFC,SAAS,CAACjB,QAAQ,CAACmB,KAAK,EAAE;MAC5B,CAAC,MAAM;QACL;QACA/D,MAAM,CAACgE,QAAQ,CAAClB,IAAI,GAAGc,OAAO;MAChC;IACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACA1B,MAAM,CAACC,IAAI,CAAC2D,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;EAEA;EACQD,gBAAgBA,CAACpB,IAAU,EAAErG,QAAgB;IACnD,MAAM2D,GAAG,GAAG4C,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;IACrC,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGjD,GAAG;IACf8C,IAAI,CAACI,QAAQ,GAAG7G,QAAQ;IACxB0G,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,EAAE;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IAE/B;IACAS,UAAU,CAAC,MAAMX,GAAG,CAACY,eAAe,CAACxD,GAAG,CAAC,EAAE,IAAI,CAAC;EAClD;EACA;EACAuB,WAAWA,CAAClF,QAAgB;IAC1B,MAAM+H,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IACpE,MAAMC,SAAS,GAAGhI,QAAQ,CAACoC,KAAK,CAAC,GAAG,CAAC,CAAC6F,GAAG,EAAE,EAAEpG,WAAW,EAAE;IAC1D,OAAOkG,eAAe,CAACjG,QAAQ,CAACkG,SAAS,IAAI,EAAE,CAAC;EAClD;EACA;EACAE,WAAWA,CAACzH,IAAS;IACnB,MAAMT,QAAQ,GAAGS,IAAI,CAAC+C,SAAS,IAAI/C,IAAI,CAACT,QAAQ,IAAI,EAAE;IAEtD,IAAI,CAACA,QAAQ,EAAE;MACb,OAAO,EAAE;IACX;IAEA;IACA,IAAIS,IAAI,CAAC4C,IAAI,EAAE;MACb,OAAO5C,IAAI,CAAC4C,IAAI;IAClB;IAEA;IACA,IAAI5C,IAAI,CAAC4E,YAAY,IAAI5E,IAAI,CAACT,QAAQ,EAAE;MACtC,OAAO,GAAG9B,WAAW,CAAC2F,qBAAqB,kCAAkCsE,kBAAkB,CAAC1H,IAAI,CAAC4E,YAAY,CAAC,aAAa8C,kBAAkB,CAAC1H,IAAI,CAACT,QAAQ,CAAC,EAAE;IACpK;IAEA;IACA,IAAIS,IAAI,CAACyB,KAAK,IAAI,CAACzB,IAAI,CAACyB,KAAK,CAACP,UAAU,CAAC,OAAO,CAAC,EAAE;MACjD,OAAO,GAAGzD,WAAW,CAAC2F,qBAAqB,GAAGpD,IAAI,CAACyB,KAAK,EAAE;IAC5D;IAEA;IACA,OAAO,EAAE;EACX;EAEA;EACA+C,aAAaA,CAACjF,QAAgB;IAC5B,OAAO,IAAI,CAACkF,WAAW,CAAClF,QAAQ,CAAC;EACnC;EAEAoI,WAAWA,CAACpI,QAAgB;IAC1B,OAAO,IAAI,CAACyE,WAAW,CAACzE,QAAQ,CAAC;EACnC;EAEAoF,WAAWA,CAACpF,QAAgB;IAC1B,OAAO,IAAI,CAAC2E,WAAW,CAAC3E,QAAQ,CAAC;EACnC,CAAC,CAAE;EACHqI,YAAYA,CAAClI,KAAU,EAAEM,IAAS;IAChC,MAAMT,QAAQ,GAAGS,IAAI,CAAC+C,SAAS,IAAI/C,IAAI,CAACT,QAAQ,IAAI,IAAI;IACxD8E,OAAO,CAACa,IAAI,CAAC,SAAS,EAAE3F,QAAQ,EAAES,IAAI,CAAC;IACvC;IACAN,KAAK,CAACE,MAAM,CAACiI,KAAK,CAACC,OAAO,GAAG,MAAM;IAEnC;IACA,MAAMC,SAAS,GAAGrI,KAAK,CAACE,MAAM,CAACoI,aAAa;IAC5C,IAAID,SAAS,EAAE;MACb,MAAME,QAAQ,GAAG,IAAI,CAACC,eAAe,CAAClI,IAAI,CAAC;MAE3C;MACA,MAAMmI,UAAU,GAAGlC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAChDiC,UAAU,CAACC,SAAS,GAAG,qFAAqFH,QAAQ,CAACI,OAAO,EAAE;MAC9HF,UAAU,CAACG,SAAS,GAAG;oBACTL,QAAQ,CAACM,IAAI,kBAAkBN,QAAQ,CAACO,KAAK;2CACtBP,QAAQ,CAACO,KAAK,KAAKP,QAAQ,CAAC9J,KAAK;OACrE;MAED;MACAgK,UAAU,CAACM,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACxC,IAAI,CAACrE,eAAe,CAACpE,IAAI,CAAC;MAC5B,CAAC,CAAC;MAEF;MACA+H,SAAS,CAACO,SAAS,GAAG,EAAE;MACxBP,SAAS,CAACzB,WAAW,CAAC6B,UAAU,CAAC;IACnC;EACF;EAEQ/H,sBAAsBA,CAAA;IAC5B,MAAMsI,UAAU,GAAG,IAAI,CAAC3K,MAAM,CAACG,eAAe,EAAEyD,KAAK,CAAC,GAAG,CAAC,CAACgH,GAAG,CAAC1H,IAAI,IAAG;MACpE,IAAIA,IAAI,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MACtC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,MAAM,CAAC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC/D,IAAIJ,IAAI,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MACtC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,KAAK;MACvC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,KAAK;MACvC,OAAOJ,IAAI;IACb,CAAC,CAAC,CAAC2H,IAAI,CAAC,GAAG,CAAC;IAEZ,OAAO,KAAKF,UAAU,IAAI;EAC5B;EACA;EACAG,gBAAgBA,CAACC,OAAe;IAC9BzE,OAAO,CAACa,IAAI,CAAC,8CAA8C,CAAC;IAC5D,IAAI,CAACrH,OAAO,CAACwC,YAAY,CAAC,mBAAmB,CAAC;EAChD;EACA;EACA0I,oBAAoBA,CAACD,OAAe,EAAEvJ,QAAgB;IACpD8E,OAAO,CAACa,IAAI,CAAC,kDAAkD,CAAC;IAChE,IAAI,CAACrH,OAAO,CAACwC,YAAY,CAAC,iBAAiB,CAAC;EAC9C;EAEA;EACQyG,gBAAgBA,CAACkC,QAAgB,EAAEzJ,QAAgB;IACzD,IAAI;MACF,MAAM2H,SAAS,GAAG7D,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAI4D,SAAS,EAAE;QACbA,SAAS,CAACjB,QAAQ,CAACkB,KAAK,CAAC;;;uBAGV5H,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA+BIA,QAAQ;0BACjByJ,QAAQ,UAAUzJ,QAAQ;;;SAG3C,CAAC;QACF2H,SAAS,CAACjB,QAAQ,CAACmB,KAAK,EAAE;MAC5B;IACF,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC1B,MAAM,CAACC,IAAI,CAAC0F,QAAQ,EAAE,QAAQ,CAAC;IACjC;EACF;EAEAC,UAAUA,CAAC/F,GAAW;IACpB,IAAIA,GAAG,EAAE;MACP;MACA,MAAMC,OAAO,GAAGD,GAAG,CAAChC,UAAU,CAAC,MAAM,CAAC,GAAGgC,GAAG,GAAG,GAAGzF,WAAW,CAAC2F,qBAAqB,GAAGF,GAAG,EAAE;MAC3FG,MAAM,CAACC,IAAI,CAACH,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;EAEA;EACA+E,eAAeA,CAAClI,IAAS;IACvB,MAAMT,QAAQ,GAAGS,IAAI,CAAC+C,SAAS,IAAI/C,IAAI,CAACT,QAAQ,IAAI,EAAE;IACtD,MAAMyB,QAAQ,GAAGhB,IAAI,CAAC8B,SAAS;IAC/B,MAAMyF,SAAS,GAAGhI,QAAQ,CAACoC,KAAK,CAAC,GAAG,CAAC,CAAC6F,GAAG,EAAE,EAAEpG,WAAW,EAAE,IAAI,EAAE;IAEhE;IACA,IAAIJ,QAAQ,KAAK,CAAC,IAAI,IAAI,CAACyD,WAAW,CAAClF,QAAQ,CAAC,EAAE;MAChD,IAAIgI,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;QAC/C,OAAO;UACLgB,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,eAAe;UACtBH,OAAO,EAAE,YAAY;UACrBlK,KAAK,EAAE;SACR;MACH,CAAC,MAAM,IAAIoJ,SAAS,KAAK,KAAK,EAAE;QAC9B,OAAO;UACLgB,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,iBAAiB;UACxBH,OAAO,EAAE,cAAc;UACvBlK,KAAK,EAAE;SACR;MACH,CAAC,MAAM;QACL,OAAO;UACLoK,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,eAAe;UACtBH,OAAO,EAAE,YAAY;UACrBlK,KAAK,EAAE;SACR;MACH;IACF;IAEA;IACA,IAAI6C,QAAQ,KAAK,CAAC,IAAI,IAAI,CAACgD,WAAW,CAACzE,QAAQ,CAAC,EAAE;MAChD,OAAO;QACLgJ,IAAI,EAAE,kBAAkB;QACxBC,KAAK,EAAE,cAAc;QACrBH,OAAO,EAAE,WAAW;QACpBlK,KAAK,EAAE;OACR;IACH;IAEA;IACA,IAAI6C,QAAQ,KAAK,CAAC,IAAI,IAAI,CAACkD,WAAW,CAAC3E,QAAQ,CAAC,EAAE;MAChD,IAAIgI,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO;UACLgB,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,gBAAgB;UACvBH,OAAO,EAAE,aAAa;UACtBlK,KAAK,EAAE;SACR;MACH,CAAC,MAAM,IAAIoJ,SAAS,KAAK,KAAK,EAAE;QAC9B,OAAO;UACLgB,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,kBAAkB;UACzBH,OAAO,EAAE,eAAe;UACxBlK,KAAK,EAAE;SACR;MACH,CAAC,MAAM;QACL,OAAO;UACLoK,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,gBAAgB;UACvBH,OAAO,EAAE,aAAa;UACtBlK,KAAK,EAAE;SACR;MACH;IACF;IAEA;IACA,IAAIoJ,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;MAC/C,OAAO;QACLgB,IAAI,EAAE,mBAAmB;QACzBC,KAAK,EAAE,eAAe;QACtBH,OAAO,EAAE,YAAY;QACrBlK,KAAK,EAAE;OACR;IACH;IAEA,IAAIoJ,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;MAC/C,OAAO;QACLgB,IAAI,EAAE,oBAAoB;QAC1BC,KAAK,EAAE,gBAAgB;QACvBH,OAAO,EAAE,aAAa;QACtBlK,KAAK,EAAE;OACR;IACH;IAEA,IAAIoJ,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;MAC/C,OAAO;QACLgB,IAAI,EAAE,yBAAyB;QAC/BC,KAAK,EAAE,iBAAiB;QACxBH,OAAO,EAAE,cAAc;QACvBlK,KAAK,EAAE;OACR;IACH;IAEA;IACA,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAACkD,QAAQ,CAACkG,SAAS,CAAC,EAAE;MACzD,OAAO;QACLgB,IAAI,EAAE,sBAAsB;QAC5BC,KAAK,EAAE,iBAAiB;QACxBH,OAAO,EAAE,cAAc;QACvBlK,KAAK,EAAEoJ,SAAS,CAAC2B,WAAW;OAC7B;IACH;IAEA;IACA,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC7H,QAAQ,CAACkG,SAAS,CAAC,EAAE;MAC5C,OAAO;QACLgB,IAAI,EAAE,mBAAmB;QACzBC,KAAK,EAAE,eAAe;QACtBH,OAAO,EAAE,YAAY;QACrBlK,KAAK,EAAEoJ,SAAS,CAAC2B,WAAW;OAC7B;IACH;IAEA;IACA,OAAO;MACLX,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,eAAe;MACtBH,OAAO,EAAE,YAAY;MACrBlK,KAAK,EAAEoJ,SAAS,GAAGA,SAAS,CAAC2B,WAAW,EAAE,GAAG;KAC9C;EACH;EAEA;EACAC,WAAWA,CAACnJ,IAAS;IACnB,IAAIO,IAAI,GAAG,CAAC;IAEZ,IAAIP,IAAI,CAACO,IAAI,EAAE;MACbA,IAAI,GAAGP,IAAI,CAACO,IAAI;IAClB,CAAC,MAAM,IAAIP,IAAI,CAAC6C,UAAU,EAAE;MAC1B;MACAtC,IAAI,GAAG6I,IAAI,CAACC,KAAK,CAACrJ,IAAI,CAAC6C,UAAU,CAAChD,MAAM,GAAG,IAAI,CAAC;IAClD;IAEA,IAAIU,IAAI,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzB,MAAM+I,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,IAAIC,SAAS,GAAG,CAAC;IAEjB,OAAOhJ,IAAI,IAAI,IAAI,IAAIgJ,SAAS,GAAGD,KAAK,CAACzJ,MAAM,GAAG,CAAC,EAAE;MACnDU,IAAI,IAAI,IAAI;MACZgJ,SAAS,EAAE;IACb;IAEA,OAAO,GAAGhJ,IAAI,CAACiJ,OAAO,CAACD,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAID,KAAK,CAACC,SAAS,CAAC,EAAE;EACvE;CACD;AAzsByBE,UAAA,EAAvBpM,SAAS,CAAC,WAAW,CAAC,C,qDAAwB;AACtCoM,UAAA,EAARtM,KAAK,EAAE,C,kDAcN;AACOsM,UAAA,EAARtM,KAAK,EAAE,C,2DAAuC;AACtCsM,UAAA,EAARtM,KAAK,EAAE,C,0DAAsC;AACrCsM,UAAA,EAARtM,KAAK,EAAE,C,yDAAiC;AAChCsM,UAAA,EAARtM,KAAK,EAAE,C,oDAAwC;AACvCsM,UAAA,EAARtM,KAAK,EAAE,C,yDAA2B;AAEzBsM,UAAA,EAATrM,MAAM,EAAE,C,wDAAqD;AACpDqM,UAAA,EAATrM,MAAM,EAAE,C,uDAAwC;AACvCqM,UAAA,EAATrM,MAAM,EAAE,C,0DAA6C;AAC5CqM,UAAA,EAATrM,MAAM,EAAE,C,6DAAiE;AAChEqM,UAAA,EAATrM,MAAM,EAAE,C,uDAA0C;AA3BxCM,mBAAmB,GAAA+L,UAAA,EAP/BxM,SAAS,CAAC;EACTyM,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,CAAC,6BAA6B,CAAC;EAC1CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACxM,YAAY;CACvB,CAAC,C,EACWI,mBAAmB,CA0sB/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}