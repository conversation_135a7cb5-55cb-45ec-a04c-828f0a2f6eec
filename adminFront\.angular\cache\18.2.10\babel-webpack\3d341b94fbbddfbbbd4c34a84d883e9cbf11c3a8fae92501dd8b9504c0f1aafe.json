{"ast": null, "code": "import { last, map } from 'rxjs';\nimport { FormBodyBuilder } from 'src/app/shared/constant/constant';\nimport { environment } from 'src/environments/environment';\nimport { RequestBuilder } from 'src/services/api/request-builder';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ReviewServiceCustom = /*#__PURE__*/(() => {\n  class ReviewServiceCustom {\n    constructor(http) {\n      this.http = http;\n    }\n    SaveReview(body) {\n      const endPoint = '/api/Review/SaveReview';\n      // const body = {\n      //     ...body\n      // CReviewType,\n      // CBuildCaseId,\n      // CFile,\n      // HouseReviews,\n      // CReviewId,\n      // CSort,\n      // CStatus,\n      // CReviewName,\n      // CIsSelectAll,\n      // CExamineNote\n      // };\n      return this._request(body, endPoint, 'post').pipe(last(), map(res => res));\n    }\n    _request(body, endPoint, method, context) {\n      const rb = new RequestBuilder(environment.BASE_URL_API, endPoint, method);\n      rb._bodyContent = FormBodyBuilder.BuildBodyContent(body);\n      return this.http.request(rb.build({\n        responseType: 'json',\n        accept: 'text/json',\n        reportProgress: false\n      }));\n    }\n    static {\n      this.ɵfac = function ReviewServiceCustom_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ReviewServiceCustom)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ReviewServiceCustom,\n        factory: ReviewServiceCustom.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ReviewServiceCustom;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}