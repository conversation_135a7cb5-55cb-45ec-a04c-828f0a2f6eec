{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/components/space-template-selector/space-template-selector.service\";\nimport * as i10 from \"@angular/forms\";\nconst _c0 = () => [];\nfunction RequirementManagementComponent_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_69_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const batchEditDialog_r6 = i0.ɵɵreference(107);\n      return i0.ɵɵresetView(ctx_r4.openBatchEdit(batchEditDialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u6279\\u6B21\\u7DE8\\u8F2F (\", ctx_r4.selectedItems.length, \")\");\n  }\n}\nfunction RequirementManagementComponent_button_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_70_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r8 = i0.ɵɵreference(105);\n      return i0.ɵɵresetView(ctx_r4.add(dialog_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_102_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_102_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const data_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r8 = i0.ɵɵreference(105);\n      return i0.ɵɵresetView(ctx_r4.onEdit(data_r10, dialog_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_102_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_102_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const data_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onDelete(data_r10));\n    });\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 46)(1, \"td\", 47)(2, \"nb-checkbox\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function RequirementManagementComponent_tr_102_Template_nb_checkbox_ngModelChange_2_listener() {\n      const data_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleItemSelection(data_r10));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 48);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 49);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 49);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 49);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 49);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 49);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 49);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 49);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 49);\n    i0.ɵɵtemplate(24, RequirementManagementComponent_tr_102_button_24_Template, 3, 0, \"button\", 50)(25, RequirementManagementComponent_tr_102_button_25_Template, 3, 0, \"button\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r10 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.isItemSelected(data_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getHouseType(data_r10.CHouseType || i0.ɵɵpureFunction0(16, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(15, 12, data_r10.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsShowText(data_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsSimpleText(data_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 14, data_r10.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_104_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_104_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_104_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const b_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", b_r14.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", b_r14.CBuildCaseName, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_104_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r15.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r15.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_104_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r16.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r16.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_104_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 55)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_104_span_2_Template, 2, 0, \"span\", 56)(3, RequirementManagementComponent_ng_template_104_span_3_Template, 2, 0, \"span\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 57)(5, \"div\", 5)(6, \"div\", 58)(7, \"div\", 5)(8, \"app-form-group\", 59)(9, \"nb-select\", 60);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_104_Template_nb_select_selectedChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CBuildCaseID, $event) || (ctx_r4.saveRequirement.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(10, RequirementManagementComponent_ng_template_104_nb_option_10_Template, 2, 2, \"nb-option\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"app-form-group\", 59)(12, \"input\", 62);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_104_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CLocation, $event) || (ctx_r4.saveRequirement.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"app-form-group\", 59)(14, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_104_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRequirement, $event) || (ctx_r4.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"app-form-group\", 59)(16, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_104_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CSort, $event) || (ctx_r4.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 59)(18, \"nb-select\", 65);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_104_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CHouseType, $event) || (ctx_r4.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_104_nb_option_19_Template, 2, 2, \"nb-option\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 59)(21, \"nb-select\", 66);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_104_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CStatus, $event) || (ctx_r4.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, RequirementManagementComponent_ng_template_104_nb_option_22_Template, 2, 2, \"nb-option\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"app-form-group\", 59)(24, \"input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_104_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnitPrice, $event) || (ctx_r4.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"app-form-group\", 59)(26, \"input\", 68);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_104_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnit, $event) || (ctx_r4.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 59)(28, \"nb-checkbox\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_104_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsShow, $event) || (ctx_r4.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u986F\\u793A\\u5728\\u9810\\u7D04\\u9700\\u6C42\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 59)(31, \"nb-checkbox\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_104_Template_nb_checkbox_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsSimple, $event) || (ctx_r4.saveRequirement.CIsSimple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(32, \" \\u8A2D\\u5B9A\\u70BA\\u7C21\\u6613\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"app-form-group\", 59)(34, \"textarea\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_104_Template_textarea_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRemark, $event) || (ctx_r4.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(35, \"nb-card-footer\")(36, \"div\", 5)(37, \"div\", 72)(38, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_104_Template_button_click_38_listener() {\n      const ref_r17 = i0.ɵɵrestoreView(_r13).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.save(ref_r17));\n    });\n    i0.ɵɵtext(39, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_104_Template_button_click_40_listener() {\n      const ref_r17 = i0.ɵɵrestoreView(_r13).dialogRef;\n      return i0.ɵɵresetView(ref_r17.close());\n    });\n    i0.ɵɵtext(41, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\\u540D\\u7A31\")(\"labelFor\", \"CBuildCaseID\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.buildCaseList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5340\\u57DF\")(\"labelFor\", \"CLocation\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CLocation);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u9810\\u7D04\\u9700\\u6C42\")(\"labelFor\", \"CIsShow\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u7C21\\u6613\\u5BA2\\u8B8A\")(\"labelFor\", \"CIsSimple\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsSimple);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_106_div_21_nb_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r22.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r22.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_ng_template_106_div_21_nb_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r23.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_ng_template_106_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"nb-card\")(2, \"nb-card-header\", 84)(3, \"h6\", 85);\n    i0.ɵɵelement(4, \"i\", 86);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_106_div_21_Template_button_click_6_listener() {\n      const i_r20 = i0.ɵɵrestoreView(_r19).index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.resetBatchEditItem(i_r20));\n    });\n    i0.ɵɵelement(7, \"i\", 24);\n    i0.ɵɵtext(8, \"\\u91CD\\u7F6E \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"nb-card-body\", 88)(10, \"div\", 5)(11, \"div\", 21)(12, \"app-form-group\", 59)(13, \"input\", 89);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_106_div_21_Template_input_ngModelChange_13_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CLocation, $event) || (item_r21.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 21)(15, \"app-form-group\", 59)(16, \"input\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_106_div_21_Template_input_ngModelChange_16_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CRequirement, $event) || (item_r21.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 21)(18, \"app-form-group\", 59)(19, \"nb-select\", 91);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_106_div_21_Template_nb_select_selectedChange_19_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CHouseType, $event) || (item_r21.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(20, RequirementManagementComponent_ng_template_106_div_21_nb_option_20_Template, 2, 2, \"nb-option\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 21)(22, \"app-form-group\", 59)(23, \"input\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_106_div_21_Template_input_ngModelChange_23_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CSort, $event) || (item_r21.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 21)(25, \"app-form-group\", 59)(26, \"nb-select\", 93);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_106_div_21_Template_nb_select_selectedChange_26_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CStatus, $event) || (item_r21.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(27, RequirementManagementComponent_ng_template_106_div_21_nb_option_27_Template, 2, 2, \"nb-option\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 21)(29, \"app-form-group\", 59)(30, \"input\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_106_div_21_Template_input_ngModelChange_30_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CUnitPrice, $event) || (item_r21.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 21)(32, \"app-form-group\", 59)(33, \"input\", 95);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_106_div_21_Template_input_ngModelChange_33_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CUnit, $event) || (item_r21.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 21)(35, \"app-form-group\", 59)(36, \"nb-checkbox\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_106_div_21_Template_nb_checkbox_ngModelChange_36_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CIsShow, $event) || (item_r21.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(37, \" \\u986F\\u793A\\u5728\\u9810\\u7D04\\u9700\\u6C42\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 21)(39, \"app-form-group\", 59)(40, \"nb-checkbox\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_106_div_21_Template_nb_checkbox_ngModelChange_40_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CIsSimple, $event) || (item_r21.CIsSimple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(41, \" \\u8A2D\\u5B9A\\u70BA\\u7C21\\u6613\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"div\", 4)(43, \"app-form-group\", 59)(44, \"textarea\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_106_div_21_Template_textarea_ngModelChange_44_listener($event) {\n      const item_r21 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r21.CRemark, $event) || (item_r21.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(45, \"                      \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const item_r21 = ctx.$implicit;\n    const i_r20 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \\u9805\\u76EE \", i_r20 + 1, \": \", ctx_r4.selectedItems[i_r20].CBuildCaseName, \" - \", item_r21.CRequirement, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"label\", \"\\u5340\\u57DF\")(\"labelFor\", \"location_\" + i_r20)(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"location_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"requirement_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"requirement_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"houseType_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"houseType_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"selected\", item_r21.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"sort_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"sort_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"status_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"status_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"selected\", item_r21.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"unitPrice_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"unitPrice_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CUnitPrice);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"unit_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"unit_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CUnit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u9810\\u7D04\\u9700\\u6C42\")(\"labelFor\", \"isShow_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"isShow_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CIsShow);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", \"\\u7C21\\u6613\\u5BA2\\u8B8A\")(\"labelFor\", \"isSimple_\" + i_r20)(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"isSimple_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CIsSimple);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"remark_\" + i_r20)(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"remark_\" + i_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r21.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_106_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 75)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 76)(5, \"div\", 5)(6, \"div\", 4)(7, \"div\", 77);\n    i0.ɵɵelement(8, \"i\", 78);\n    i0.ɵɵtext(9, \" \\u60A8\\u53EF\\u4EE5\\u500B\\u5225\\u4FEE\\u6539\\u6BCF\\u500B\\u9805\\u76EE\\u7684\\u6B04\\u4F4D\\uFF0C\\u9664\\u4E86\\u5EFA\\u6848\\u540D\\u7A31\\u4E4B\\u5916\\u7684\\u6240\\u6709\\u6B04\\u4F4D\\u90FD\\u53EF\\u4EE5\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 79);\n    i0.ɵɵelement(11, \"i\", 80);\n    i0.ɵɵelementStart(12, \"strong\");\n    i0.ɵɵtext(13, \"\\u6CE8\\u610F\\u4E8B\\u9805\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ul\", 81)(15, \"li\");\n    i0.ɵɵtext(16, \"\\u5DE5\\u7A0B\\u9805\\u76EE\\u3001\\u985E\\u578B\\u3001\\u6392\\u5E8F\\u3001\\u72C0\\u614B\\u3001\\u55AE\\u50F9\\u3001\\u55AE\\u4F4D\\u70BA\\u5FC5\\u586B\\u6B04\\u4F4D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"li\");\n    i0.ɵɵtext(18, \"\\u6392\\u5E8F\\u548C\\u55AE\\u50F9\\u4E0D\\u80FD\\u70BA\\u8CA0\\u6578\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"li\");\n    i0.ɵɵtext(20, \"\\u5340\\u57DF\\u6700\\u591A20\\u500B\\u5B57\\uFF0C\\u5DE5\\u7A0B\\u9805\\u76EE\\u6700\\u591A50\\u500B\\u5B57\\uFF0C\\u5099\\u8A3B\\u8AAA\\u660E\\u6700\\u591A100\\u500B\\u5B57\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, RequirementManagementComponent_ng_template_106_div_21_Template, 46, 55, \"div\", 82);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"nb-card-footer\")(23, \"div\", 5)(24, \"div\", 72)(25, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_106_Template_button_click_25_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.batchSave(ref_r24));\n    });\n    i0.ɵɵtext(26, \"\\u78BA\\u5B9A\\u6279\\u6B21\\u66F4\\u65B0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_106_Template_button_click_27_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.cancelBatchEdit(ref_r24));\n    });\n    i0.ɵɵtext(28, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u6279\\u6B21\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42 (\\u5DF2\\u9078\\u64C7 \", ctx_r4.selectedItems.length, \" \\u500B\\u9805\\u76EE)\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.batchEditItems);\n  }\n}\nexport class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref, spaceTemplateSelectorService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    this.spaceTemplateSelectorService = spaceTemplateSelectorService;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    // 批次編輯相關屬性\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.isBatchEditMode = false;\n    // 批次編輯時的項目資料副本\n    this.batchEditItems = [];\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CIsSimple = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CLocation = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 建案切換事件處理\n  onBuildCaseChange(newBuildCaseId) {\n    // 重置批次選擇的項目\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.batchEditItems = [];\n    this.isBatchEditMode = false;\n    // 更新當前建案並重新載入資料\n    this.currentBuildCase = newBuildCaseId;\n    this.getList();\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 清除選擇狀態和批次編輯相關狀態\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.batchEditItems = [];\n    this.isBatchEditMode = false;\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 建案頁面需要驗證建案名稱\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[排序]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 數值範圍驗證\n    if (this.saveRequirement.CSort !== null && this.saveRequirement.CSort !== undefined && this.saveRequirement.CSort < 0) {\n      this.valid.errorMessages.push('[排序] 不能為負數');\n    }\n    if (this.saveRequirement.CUnitPrice !== null && this.saveRequirement.CUnitPrice !== undefined && this.saveRequirement.CUnitPrice < 0) {\n      this.valid.errorMessages.push('[單價] 不能為負數');\n    }\n    // 長度驗證\n    if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    if (this.saveRequirement.CRequirement && this.saveRequirement.CRequirement.length > 50) {\n      this.valid.errorMessages.push('[工程項目] 不能超過50個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false,\n      CIsSimple: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 建案頁面 - 使用當前選擇的建案或第一個建案\n    if (this.currentBuildCase != 0) {\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n    } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n      this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 如果有建案時才查詢\n      if (this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 建案頁面的邏輯\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.requirementList = res.Entries;\n          this.totalRecords = res.TotalItems;\n          // 清理已選擇的項目，移除不存在於新列表中的項目\n          this.selectedItems = this.selectedItems.filter(selectedItem => this.requirementList.some(listItem => listItem.CRequirementID === selectedItem.CRequirementID));\n          // 更新選擇狀態\n          this.updateSelectAllState();\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false,\n            CIsSimple: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CLocation = res.Entries.CLocation;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n          this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  getCIsSimpleText(data) {\n    return data.CIsSimple ? '是' : '否';\n  }\n  // 空間模板相關方法\n  openSpaceTemplateSelector() {\n    if (!this.getListRequirementRequest.CBuildCaseID) {\n      this.message.showErrorMSG('請先選擇建案');\n      return;\n    }\n    // 使用新的服務開啟對話框\n    this.spaceTemplateSelectorService.openSelector(this.getListRequirementRequest.CBuildCaseID?.toString() || '').subscribe(result => {\n      if (result) {\n        this.onSpaceTemplateApplied(result);\n      }\n    });\n  }\n  onSpaceTemplateApplied(config) {\n    console.log('套用空間模板配置:', config);\n    // 使用 SaveTemplayeData API 批次套用模板\n    this.batchApplyTemplates(config.selectedItems);\n  }\n  // 批次套用模板方法\n  batchApplyTemplates(selectedTemplates) {\n    if (!this.getListRequirementRequest.CBuildCaseID) {\n      this.message.showErrorMSG('建案 ID 不存在');\n      return;\n    }\n    // 準備批次請求\n    const batchRequests = selectedTemplates.map(template => {\n      const saveTemplateDataReq = {\n        CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n        CTemplateID: template.CTemplateId\n      };\n      return this.requirementService.apiRequirementSaveTemplayeDataPost$Json({\n        body: saveTemplateDataReq\n      });\n    });\n    // 同時執行所有請求\n    Promise.all(batchRequests.map(req => req.toPromise())).then(responses => {\n      const successCount = responses.filter(res => res?.StatusCode === 0).length;\n      const failCount = responses.length - successCount;\n      if (failCount === 0) {\n        this.message.showSucessMSG(`成功套用 ${successCount} 個模板項目`);\n      } else if (successCount > 0) {\n        this.message.showSucessMSG(`成功套用 ${successCount} 個模板項目，${failCount} 個失敗`);\n      } else {\n        this.message.showErrorMSG('模板套用失敗');\n      }\n      // 重新載入資料\n      this.getList();\n    }).catch(error => {\n      console.error('批次套用模板失敗:', error);\n      this.message.showErrorMSG('批次套用模板時發生錯誤');\n    });\n  }\n  // 批次編輯相關方法\n  // 切換單一項目選擇狀態\n  toggleItemSelection(item) {\n    const index = this.selectedItems.findIndex(selected => selected.CRequirementID === item.CRequirementID);\n    if (index > -1) {\n      this.selectedItems.splice(index, 1);\n    } else {\n      this.selectedItems.push(item);\n    }\n    this.updateSelectAllState();\n  }\n  // 切換全選狀態\n  toggleSelectAll(newValue) {\n    if (this.requirementList.length === 0) {\n      this.selectedItems = [];\n      this.isAllSelected = false;\n      return;\n    }\n    // 更新 isAllSelected 狀態\n    this.isAllSelected = newValue;\n    // 根據新值更新 selectedItems\n    if (this.isAllSelected) {\n      this.selectedItems = [...this.requirementList];\n    } else {\n      this.selectedItems = [];\n    }\n  }\n  // 更新全選狀態\n  updateSelectAllState() {\n    if (this.requirementList.length === 0) {\n      this.isAllSelected = false;\n    } else {\n      this.isAllSelected = this.selectedItems.length === this.requirementList.length;\n    }\n  }\n  // 檢查項目是否被選中\n  isItemSelected(item) {\n    return this.selectedItems.some(selected => selected.CRequirementID === item.CRequirementID);\n  }\n  // 開啟批次編輯對話框\n  openBatchEdit(dialog) {\n    if (this.selectedItems.length === 0) {\n      this.message.showErrorMSG('請先選擇要編輯的項目');\n      return;\n    }\n    this.isBatchEditMode = true;\n    // 初始化批次編輯項目資料\n    this.batchEditItems = this.selectedItems.map(item => ({\n      CRequirementID: item.CRequirementID,\n      CBuildCaseID: item.CBuildCaseID,\n      CLocation: item.CLocation,\n      CRequirement: item.CRequirement,\n      CHouseType: item.CHouseType ? [...item.CHouseType] : [],\n      CSort: item.CSort,\n      CStatus: item.CStatus,\n      CUnitPrice: item.CUnitPrice || 0,\n      CUnit: item.CUnit,\n      CIsShow: item.CIsShow || false,\n      CIsSimple: item.CIsSimple || false,\n      CRemark: item.CRemark\n    }));\n    this.dialogService.open(dialog);\n  }\n  // 批次驗證方法\n  batchValidation() {\n    const errorMessages = [];\n    this.batchEditItems.forEach((item, index) => {\n      const itemNum = index + 1;\n      // 必填欄位檢核\n      if (!item.CBuildCaseID) {\n        errorMessages.push(`[項目 ${itemNum}] 建案名稱為必填欄位`);\n      }\n      if (!item.CRequirement || item.CRequirement.trim() === '') {\n        errorMessages.push(`[項目 ${itemNum}] 工程項目為必填欄位`);\n      }\n      if (!item.CHouseType || item.CHouseType.length === 0) {\n        errorMessages.push(`[項目 ${itemNum}] 類型為必填欄位`);\n      }\n      if (item.CSort === null || item.CSort === undefined || item.CSort < 0) {\n        errorMessages.push(`[項目 ${itemNum}] 排序為必填欄位且不能為負數`);\n      }\n      if (item.CStatus === null || item.CStatus === undefined) {\n        errorMessages.push(`[項目 ${itemNum}] 狀態為必填欄位`);\n      }\n      if (item.CUnitPrice === null || item.CUnitPrice === undefined || item.CUnitPrice < 0) {\n        errorMessages.push(`[項目 ${itemNum}] 單價為必填欄位且不能為負數`);\n      }\n      if (!item.CUnit || item.CUnit.trim() === '') {\n        errorMessages.push(`[項目 ${itemNum}] 單位為必填欄位`);\n      }\n      // 長度驗證\n      if (item.CLocation && item.CLocation.length > 20) {\n        errorMessages.push(`[項目 ${itemNum}] 區域不能超過20個字`);\n      }\n      if (item.CRequirement && item.CRequirement.length > 50) {\n        errorMessages.push(`[項目 ${itemNum}] 工程項目不能超過50個字`);\n      }\n      if (item.CRemark && item.CRemark.length > 100) {\n        errorMessages.push(`[項目 ${itemNum}] 備註說明不能超過100個字`);\n      }\n    });\n    return errorMessages;\n  }\n  // 批次保存\n  batchSave(ref) {\n    if (this.batchEditItems.length === 0) {\n      this.message.showErrorMSG('沒有要更新的項目');\n      return;\n    }\n    // 執行批次驗證\n    const validationErrors = this.batchValidation();\n    if (validationErrors.length > 0) {\n      this.message.showErrorMSGs(validationErrors);\n      return;\n    }\n    // 直接使用編輯後的資料\n    const batchData = this.batchEditItems.map(item => ({\n      CRequirementID: item.CRequirementID,\n      CBuildCaseID: item.CBuildCaseID,\n      CLocation: item.CLocation,\n      CRequirement: item.CRequirement,\n      CHouseType: item.CHouseType,\n      CSort: item.CSort,\n      CStatus: item.CStatus,\n      CUnitPrice: item.CUnitPrice,\n      CUnit: item.CUnit,\n      CIsShow: item.CIsShow,\n      CIsSimple: item.CIsSimple,\n      CRemark: item.CRemark\n    }));\n    // 調用批次保存 API\n    this.requirementService.apiRequirementBatchSaveDataPost$Json({\n      body: batchData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(`成功批次更新 ${this.batchEditItems.length} 個項目`);\n        this.selectedItems = [];\n        this.batchEditItems = [];\n        this.updateSelectAllState();\n        this.getList();\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message || '批次更新失敗');\n      }\n    });\n  }\n  // 重置批次編輯中的單一項目到原始狀態\n  resetBatchEditItem(index) {\n    const originalItem = this.selectedItems[index];\n    if (originalItem) {\n      this.batchEditItems[index] = {\n        CRequirementID: originalItem.CRequirementID,\n        CBuildCaseID: originalItem.CBuildCaseID,\n        CLocation: originalItem.CLocation,\n        CRequirement: originalItem.CRequirement,\n        CHouseType: originalItem.CHouseType ? [...originalItem.CHouseType] : [],\n        CSort: originalItem.CSort,\n        CStatus: originalItem.CStatus,\n        CUnitPrice: originalItem.CUnitPrice || 0,\n        CUnit: originalItem.CUnit,\n        CIsShow: originalItem.CIsShow || false,\n        CIsSimple: originalItem.CIsSimple || false,\n        CRemark: originalItem.CRemark\n      };\n    }\n  }\n  // 取消批次編輯\n  cancelBatchEdit(ref) {\n    this.isBatchEditMode = false;\n    this.batchEditItems = [];\n    ref.close();\n  }\n  // 備用：如果需要手動建立需求項目的方法\n  batchCreateRequirements(requirements) {\n    const batchRequests = requirements.map(requirement => this.requirementService.apiRequirementSaveDataPost$Json({\n      body: requirement\n    }));\n    Promise.all(batchRequests.map(req => req.toPromise())).then(responses => {\n      const successCount = responses.filter(res => res?.StatusCode === 0).length;\n      this.message.showSucessMSG(`成功建立 ${successCount} 個需求項目`);\n      this.getList();\n    }).catch(error => {\n      console.error('批次建立需求失敗:', error);\n      this.message.showErrorMSG('批次建立需求時發生錯誤');\n    });\n  }\n  static {\n    this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef), i0.ɵɵdirectiveInject(i9.SpaceTemplateSelectorService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementManagementComponent,\n      selectors: [[\"app-requirement-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 108,\n      vars: 25,\n      consts: [[\"dialog\", \"\"], [\"batchEditDialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\", \"pb-0\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u5340\\u57DF\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [3, \"value\"], [\"for\", \"isShow\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5168\\u90E8\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"isSimple\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"btn\", \"btn-warning\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-layer-group\", \"mr-1\"], [\"class\", \"btn btn-primary mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"col-12\", \"mt-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\", \"d-flex\", \"flex-column\", \"align-items-center\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"text-white\", \"mt-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"btn\", \"btn-primary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-1\", \"text-center\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CBuildCaseID\", \"name\", \"CBuildCaseID\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CLocation\", \"name\", \"CLocation\", \"placeholder\", \"\\u5340\\u57DF\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CSort\", \"name\", \"CSort\", \"placeholder\", \"\\u6392\\u5E8F\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", \"step\", \"0.01\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CUnit\", \"name\", \"CUnit\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsShow\", \"name\", \"CIsShow\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsSimple\", \"name\", \"CIsSimple\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"langg\", \"\", 3, \"value\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"900px\"], [2, \"padding\", \"1rem 2rem\", \"max-height\", \"70vh\", \"overflow-y\", \"auto\"], [1, \"alert\", \"alert-info\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [1, \"alert\", \"alert-warning\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-2\"], [1, \"mb-0\", \"mt-2\"], [\"class\", \"mb-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"mb-4\"], [1, \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [\"type\", \"button\", \"title\", \"\\u91CD\\u7F6E\\u70BA\\u539F\\u59CB\\u503C\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"py-3\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5340\\u57DF\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"id\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6392\\u5E8F\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"id\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u55AE\\u50F9\", \"step\", \"0.01\", \"min\", \"0\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [\"nbInput\", \"\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"2\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"id\", \"ngModel\"]],\n      template: function RequirementManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"label\", 7);\n          i0.ɵɵtext(8, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"nb-select\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CBuildCaseID, $event) || (ctx.getListRequirementRequest.CBuildCaseID = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildCaseChange($event));\n          });\n          i0.ɵɵtemplate(10, RequirementManagementComponent_nb_option_10_Template, 2, 2, \"nb-option\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"label\", 10);\n          i0.ɵɵtext(13, \"\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CLocation, $event) || (ctx.getListRequirementRequest.CLocation = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 6)(16, \"label\", 12);\n          i0.ɵɵtext(17, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 5)(20, \"div\", 6)(21, \"label\", 14);\n          i0.ɵɵtext(22, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(24, RequirementManagementComponent_nb_option_24_Template, 2, 2, \"nb-option\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 6)(26, \"label\", 16);\n          i0.ɵɵtext(27, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"nb-select\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(29, \"nb-option\", 17);\n          i0.ɵɵtext(30, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"nb-option\", 17);\n          i0.ɵɵtext(32, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-option\", 17);\n          i0.ɵɵtext(34, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 6)(36, \"label\", 18);\n          i0.ɵɵtext(37, \"\\u9810\\u7D04\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"nb-select\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsShow, $event) || (ctx.getListRequirementRequest.CIsShow = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(39, \"nb-option\", 17);\n          i0.ɵɵtext(40, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"nb-option\", 17);\n          i0.ɵɵtext(42, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"nb-option\", 17);\n          i0.ɵɵtext(44, \"\\u5426\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(45, \"div\", 5)(46, \"div\", 6)(47, \"label\", 20);\n          i0.ɵɵtext(48, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"nb-select\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_49_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsSimple, $event) || (ctx.getListRequirementRequest.CIsSimple = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(50, \"nb-option\", 17);\n          i0.ɵɵtext(51, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"nb-option\", 17);\n          i0.ɵɵtext(53, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"nb-option\", 17);\n          i0.ɵɵtext(55, \"\\u5426\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(56, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 5);\n          i0.ɵɵelement(58, \"div\", 21);\n          i0.ɵɵelementStart(59, \"div\", 22)(60, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_60_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetSearch());\n          });\n          i0.ɵɵelement(61, \"i\", 24);\n          i0.ɵɵtext(62, \"\\u91CD\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_63_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(64, \"i\", 26);\n          i0.ɵɵtext(65, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_66_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openSpaceTemplateSelector());\n          });\n          i0.ɵɵelement(67, \"i\", 28);\n          i0.ɵɵtext(68, \"\\u6A21\\u677F\\u65B0\\u589E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(69, RequirementManagementComponent_button_69_Template, 3, 1, \"button\", 29)(70, RequirementManagementComponent_button_70_Template, 3, 0, \"button\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(71, \"nb-card-body\", 3)(72, \"div\", 31)(73, \"div\", 32)(74, \"table\", 33)(75, \"thead\")(76, \"tr\", 34)(77, \"th\", 35)(78, \"nb-checkbox\", 36);\n          i0.ɵɵlistener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_checkbox_ngModelChange_78_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleSelectAll($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"small\", 37);\n          i0.ɵɵtext(80, \"\\u5168\\u9078\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"th\", 38);\n          i0.ɵɵtext(82, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"th\", 39);\n          i0.ɵɵtext(84, \"\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"th\", 38);\n          i0.ɵɵtext(86, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"th\", 39);\n          i0.ɵɵtext(88, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"th\", 39);\n          i0.ɵɵtext(90, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"th\", 39);\n          i0.ɵɵtext(92, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"th\", 39);\n          i0.ɵɵtext(94, \"\\u9810\\u7D04\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"th\", 39);\n          i0.ɵɵtext(96, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"th\", 39);\n          i0.ɵɵtext(98, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"th\", 39);\n          i0.ɵɵtext(100, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(101, \"tbody\");\n          i0.ɵɵtemplate(102, RequirementManagementComponent_tr_102_Template, 26, 17, \"tr\", 40);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(103, \"ngx-pagination\", 41);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_103_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_103_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(104, RequirementManagementComponent_ng_template_104_Template, 42, 49, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(106, RequirementManagementComponent_ng_template_106_Template, 29, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CBuildCaseID);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CLocation);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsShow);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsSimple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItems.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.isAllSelected);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i10.DefaultValueAccessor, i10.NumberValueAccessor, i10.NgControlStatus, i10.MaxLengthValidator, i10.MinValidator, i10.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent, NumberWithCommasPipe],\n      styles: [\".table-active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd !important;\\n  border-left: 3px solid #2196f3;\\n}\\n\\n.page-description-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1.25rem;\\n  border-radius: 10px;\\n  border: 1px solid #dee2e6;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n  margin-bottom: 1rem;\\n}\\n.page-description-card[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #007bff;\\n}\\n.page-description-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  line-height: 1.5;\\n  margin-bottom: 0.75rem;\\n}\\n.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  color: #6c757d;\\n  border: 1px solid #dee2e6;\\n  font-size: 0.8rem;\\n  padding: 0.4rem 0.8rem;\\n  font-weight: 500;\\n}\\n.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.template-creation-controls[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n  border: 1px solid #e9ecef;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .template-action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .template-status-info[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  border: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:disabled {\\n  background: #6c757d;\\n  cursor: not-allowed;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  font-size: 0.75rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #6c757d;\\n  color: #6c757d;\\n  transition: all 0.2s ease;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-style: italic;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n  margin: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n  margin: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-active[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_selectRow 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_selectRow {\\n  0% {\\n    background-color: transparent;\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.01);\\n  }\\n  100% {\\n    background-color: #e3f2fd;\\n    transform: scale(1);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 0.25rem;\\n  }\\n  .template-creation-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.375rem 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "type_r3", "value", "label", "ɵɵlistener", "RequirementManagementComponent_button_69_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r4", "ɵɵnextContext", "batchEditDialog_r6", "ɵɵreference", "ɵɵresetView", "openBatchEdit", "ɵɵelement", "selectedItems", "length", "RequirementManagementComponent_button_70_Template_button_click_0_listener", "_r7", "dialog_r8", "add", "RequirementManagementComponent_tr_102_button_24_Template_button_click_0_listener", "_r11", "data_r10", "$implicit", "onEdit", "RequirementManagementComponent_tr_102_button_25_Template_button_click_0_listener", "_r12", "onDelete", "RequirementManagementComponent_tr_102_Template_nb_checkbox_ngModelChange_2_listener", "_r9", "toggleItemSelection", "ɵɵtemplate", "RequirementManagementComponent_tr_102_button_24_Template", "RequirementManagementComponent_tr_102_button_25_Template", "isItemSelected", "ɵɵtextInterpolate", "CLocation", "CRequirement", "getHouseType", "CHouseType", "ɵɵpureFunction0", "_c0", "CSort", "ɵɵpipeBind1", "CStatus", "getCIsShowText", "getCIsSimpleText", "CUnitPrice", "isUpdate", "isDelete", "b_r14", "type_r15", "status_r16", "RequirementManagementComponent_ng_template_104_span_2_Template", "RequirementManagementComponent_ng_template_104_span_3_Template", "ɵɵtwoWayListener", "RequirementManagementComponent_ng_template_104_Template_nb_select_selectedChange_9_listener", "$event", "_r13", "ɵɵtwoWayBindingSet", "saveRequirement", "CBuildCaseID", "RequirementManagementComponent_ng_template_104_nb_option_10_Template", "RequirementManagementComponent_ng_template_104_Template_input_ngModelChange_12_listener", "RequirementManagementComponent_ng_template_104_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_ng_template_104_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_ng_template_104_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_104_nb_option_19_Template", "RequirementManagementComponent_ng_template_104_Template_nb_select_selectedChange_21_listener", "RequirementManagementComponent_ng_template_104_nb_option_22_Template", "RequirementManagementComponent_ng_template_104_Template_input_ngModelChange_24_listener", "RequirementManagementComponent_ng_template_104_Template_input_ngModelChange_26_listener", "CUnit", "RequirementManagementComponent_ng_template_104_Template_nb_checkbox_ngModelChange_28_listener", "CIsShow", "RequirementManagementComponent_ng_template_104_Template_nb_checkbox_ngModelChange_31_listener", "CIsSimple", "RequirementManagementComponent_ng_template_104_Template_textarea_ngModelChange_34_listener", "CRemark", "RequirementManagementComponent_ng_template_104_Template_button_click_38_listener", "ref_r17", "dialogRef", "save", "RequirementManagementComponent_ng_template_104_Template_button_click_40_listener", "close", "isNew", "ɵɵtwoWayProperty", "buildCaseList", "houseType", "statusOptions", "type_r22", "status_r23", "RequirementManagementComponent_ng_template_106_div_21_Template_button_click_6_listener", "i_r20", "_r19", "index", "resetBatchEditItem", "RequirementManagementComponent_ng_template_106_div_21_Template_input_ngModelChange_13_listener", "item_r21", "RequirementManagementComponent_ng_template_106_div_21_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_ng_template_106_div_21_Template_nb_select_selectedChange_19_listener", "RequirementManagementComponent_ng_template_106_div_21_nb_option_20_Template", "RequirementManagementComponent_ng_template_106_div_21_Template_input_ngModelChange_23_listener", "RequirementManagementComponent_ng_template_106_div_21_Template_nb_select_selectedChange_26_listener", "RequirementManagementComponent_ng_template_106_div_21_nb_option_27_Template", "RequirementManagementComponent_ng_template_106_div_21_Template_input_ngModelChange_30_listener", "RequirementManagementComponent_ng_template_106_div_21_Template_input_ngModelChange_33_listener", "RequirementManagementComponent_ng_template_106_div_21_Template_nb_checkbox_ngModelChange_36_listener", "RequirementManagementComponent_ng_template_106_div_21_Template_nb_checkbox_ngModelChange_40_listener", "RequirementManagementComponent_ng_template_106_div_21_Template_textarea_ngModelChange_44_listener", "ɵɵtextInterpolate3", "RequirementManagementComponent_ng_template_106_div_21_Template", "RequirementManagementComponent_ng_template_106_Template_button_click_25_listener", "ref_r24", "_r18", "batchSave", "RequirementManagementComponent_ng_template_106_Template_button_click_27_listener", "cancelBatchEdit", "batchEditItems", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "spaceTemplateSelectorService", "getListRequirementRequest", "getRequirementRequest", "requirementList", "getEnumOptions", "currentBuildCase", "isAllSelected", "isBatchEditMode", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "map", "type", "onBuildCaseChange", "newBuildCaseId", "getList", "resetSearch", "setTimeout", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "undefined", "errorMessages", "dialog", "open", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "TotalItems", "filter", "selectedItem", "some", "listItem", "updateSelectAllState", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "v", "openSpaceTemplateSelector", "openSelector", "toString", "result", "onSpaceTemplateApplied", "config", "batchApplyTemplates", "selectedTemplates", "batchRequests", "template", "saveTemplateDataReq", "CTemplateID", "CTemplateId", "apiRequirementSaveTemplayeDataPost$Json", "Promise", "all", "req", "to<PERSON>romise", "then", "responses", "successCount", "failCount", "catch", "item", "findIndex", "selected", "splice", "toggleSelectAll", "newValue", "batchValidation", "itemNum", "trim", "validationErrors", "batchData", "apiRequirementBatchSaveDataPost$Json", "originalItem", "batchCreateRequirements", "requirements", "requirement", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "BuildCaseService", "RequirementService", "i7", "PetternHelper", "i8", "Router", "DestroyRef", "i9", "SpaceTemplateSelectorService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "RequirementManagementComponent_Template", "rf", "ctx", "RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener", "_r1", "RequirementManagementComponent_nb_option_10_Template", "RequirementManagementComponent_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_Template_input_ngModelChange_18_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_23_listener", "RequirementManagementComponent_nb_option_24_Template", "RequirementManagementComponent_Template_nb_select_ngModelChange_28_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_38_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_49_listener", "RequirementManagementComponent_Template_button_click_60_listener", "RequirementManagementComponent_Template_button_click_63_listener", "RequirementManagementComponent_Template_button_click_66_listener", "RequirementManagementComponent_button_69_Template", "RequirementManagementComponent_button_70_Template", "RequirementManagementComponent_Template_nb_checkbox_ngModelChange_78_listener", "RequirementManagementComponent_tr_102_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_103_listener", "RequirementManagementComponent_ng_template_104_Template", "ɵɵtemplateRefExtractor", "RequirementManagementComponent_ng_template_106_Template", "isCreate", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i10", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "MinValidator", "NgModel", "NbSelectComponent", "NbOptionComponent", "NbCheckboxComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement, SaveTemplayeDataReq } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { SpaceTemplateSelectorService } from 'src/app/shared/components/space-template-selector/space-template-selector.service';\r\nimport { SpaceTemplateConfig } from 'src/app/shared/components/space-template-selector/space-template-selector.component';\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef,\r\n    private spaceTemplateSelectorService: SpaceTemplateSelectorService\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n\r\n  // 批次編輯相關屬性\r\n  selectedItems: GetRequirement[] = [];\r\n  isAllSelected = false;\r\n  isBatchEditMode = false;\r\n  // 批次編輯時的項目資料副本\r\n  batchEditItems: (SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean })[] = [];\r\n\r\n  override ngOnInit(): void { }\r\n\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CIsSimple = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CLocation = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 建案切換事件處理\r\n  onBuildCaseChange(newBuildCaseId: any) {\r\n    // 重置批次選擇的項目\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n    this.batchEditItems = [];\r\n    this.isBatchEditMode = false;\r\n    \r\n    // 更新當前建案並重新載入資料\r\n    this.currentBuildCase = newBuildCaseId;\r\n    this.getList();\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 清除選擇狀態和批次編輯相關狀態\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n    this.batchEditItems = [];\r\n    this.isBatchEditMode = false;\r\n    \r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n\r\n    // 建案頁面需要驗證建案名稱\r\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[排序]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 數值範圍驗證\r\n    if (this.saveRequirement.CSort !== null && this.saveRequirement.CSort !== undefined && this.saveRequirement.CSort < 0) {\r\n      this.valid.errorMessages.push('[排序] 不能為負數');\r\n    }\r\n    \r\n    if (this.saveRequirement.CUnitPrice !== null && this.saveRequirement.CUnitPrice !== undefined && this.saveRequirement.CUnitPrice < 0) {\r\n      this.valid.errorMessages.push('[單價] 不能為負數');\r\n    }\r\n\r\n    // 長度驗證\r\n    if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n    \r\n    if (this.saveRequirement.CRequirement && this.saveRequirement.CRequirement.length > 50) {\r\n      this.valid.errorMessages.push('[工程項目] 不能超過50個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n    if (this.currentBuildCase != 0) {\r\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n    } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n    }\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 如果有建案時才查詢\r\n        if (this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n\r\n    // 建案頁面的邏輯\r\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.requirementList = res.Entries;\r\n            this.totalRecords = res.TotalItems!;\r\n\r\n            // 清理已選擇的項目，移除不存在於新列表中的項目\r\n            this.selectedItems = this.selectedItems.filter(selectedItem =>\r\n              this.requirementList.some(listItem => listItem.CRequirementID === selectedItem.CRequirementID)\r\n            );\r\n\r\n            // 更新選擇狀態\r\n            this.updateSelectAllState();\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CLocation = res.Entries.CLocation;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\r\n            this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  getCIsSimpleText(data: any): string {\r\n    return data.CIsSimple ? '是' : '否';\r\n  }\r\n\r\n  // 空間模板相關方法\r\n  openSpaceTemplateSelector() {\r\n    if (!this.getListRequirementRequest.CBuildCaseID) {\r\n      this.message.showErrorMSG('請先選擇建案');\r\n      return;\r\n    }\r\n\r\n    // 使用新的服務開啟對話框\r\n    this.spaceTemplateSelectorService.openSelector(this.getListRequirementRequest.CBuildCaseID?.toString() || '')\r\n      .subscribe(result => {\r\n        if (result) {\r\n          this.onSpaceTemplateApplied(result);\r\n        }\r\n      });\r\n  }\r\n\r\n  onSpaceTemplateApplied(config: SpaceTemplateConfig) {\r\n    console.log('套用空間模板配置:', config);\r\n\r\n    // 使用 SaveTemplayeData API 批次套用模板\r\n    this.batchApplyTemplates(config.selectedItems);\r\n  }\r\n\r\n  // 批次套用模板方法\r\n  private batchApplyTemplates(selectedTemplates: any[]) {\r\n    if (!this.getListRequirementRequest.CBuildCaseID) {\r\n      this.message.showErrorMSG('建案 ID 不存在');\r\n      return;\r\n    }\r\n\r\n    // 準備批次請求\r\n    const batchRequests = selectedTemplates.map(template => {\r\n      const saveTemplateDataReq = {\r\n        CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\r\n        CTemplateID: template.CTemplateId\r\n      };\r\n\r\n      return this.requirementService.apiRequirementSaveTemplayeDataPost$Json({\r\n        body: saveTemplateDataReq\r\n      });\r\n    });\r\n\r\n    // 同時執行所有請求\r\n    Promise.all(batchRequests.map(req => req.toPromise()))\r\n      .then(responses => {\r\n        const successCount = responses.filter(res => res?.StatusCode === 0).length;\r\n        const failCount = responses.length - successCount;\r\n\r\n        if (failCount === 0) {\r\n          this.message.showSucessMSG(`成功套用 ${successCount} 個模板項目`);\r\n        } else if (successCount > 0) {\r\n          this.message.showSucessMSG(`成功套用 ${successCount} 個模板項目，${failCount} 個失敗`);\r\n        } else {\r\n          this.message.showErrorMSG('模板套用失敗');\r\n        }\r\n\r\n        // 重新載入資料\r\n        this.getList();\r\n      })\r\n      .catch(error => {\r\n        console.error('批次套用模板失敗:', error);\r\n        this.message.showErrorMSG('批次套用模板時發生錯誤');\r\n      });\r\n  }\r\n\r\n  // 批次編輯相關方法\r\n\r\n  // 切換單一項目選擇狀態\r\n  toggleItemSelection(item: GetRequirement) {\r\n    const index = this.selectedItems.findIndex(selected => selected.CRequirementID === item.CRequirementID);\r\n    if (index > -1) {\r\n      this.selectedItems.splice(index, 1);\r\n    } else {\r\n      this.selectedItems.push(item);\r\n    }\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 切換全選狀態\r\n  toggleSelectAll(newValue: boolean) {\r\n    if (this.requirementList.length === 0) {\r\n      this.selectedItems = [];\r\n      this.isAllSelected = false;\r\n      return;\r\n    }\r\n\r\n    // 更新 isAllSelected 狀態\r\n    this.isAllSelected = newValue;\r\n\r\n    // 根據新值更新 selectedItems\r\n    if (this.isAllSelected) {\r\n      this.selectedItems = [...this.requirementList];\r\n    } else {\r\n      this.selectedItems = [];\r\n    }\r\n  }\r\n\r\n  // 更新全選狀態\r\n  updateSelectAllState() {\r\n    if (this.requirementList.length === 0) {\r\n      this.isAllSelected = false;\r\n    } else {\r\n      this.isAllSelected = this.selectedItems.length === this.requirementList.length;\r\n    }\r\n  }\r\n\r\n  // 檢查項目是否被選中\r\n  isItemSelected(item: GetRequirement): boolean {\r\n    return this.selectedItems.some(selected => selected.CRequirementID === item.CRequirementID);\r\n  }\r\n\r\n  // 開啟批次編輯對話框\r\n  openBatchEdit(dialog: TemplateRef<any>) {\r\n    if (this.selectedItems.length === 0) {\r\n      this.message.showErrorMSG('請先選擇要編輯的項目');\r\n      return;\r\n    }\r\n\r\n    this.isBatchEditMode = true;\r\n\r\n    // 初始化批次編輯項目資料\r\n    this.batchEditItems = this.selectedItems.map(item => ({\r\n      CRequirementID: item.CRequirementID,\r\n      CBuildCaseID: item.CBuildCaseID,\r\n      CLocation: item.CLocation,\r\n      CRequirement: item.CRequirement,\r\n      CHouseType: item.CHouseType ? [...item.CHouseType] : [],\r\n      CSort: item.CSort,\r\n      CStatus: item.CStatus,\r\n      CUnitPrice: item.CUnitPrice || 0,\r\n      CUnit: item.CUnit,\r\n      CIsShow: item.CIsShow || false,\r\n      CIsSimple: item.CIsSimple || false,\r\n      CRemark: item.CRemark\r\n    }));\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 批次驗證方法\r\n  batchValidation(): string[] {\r\n    const errorMessages: string[] = [];\r\n    \r\n    this.batchEditItems.forEach((item, index) => {\r\n      const itemNum = index + 1;\r\n      \r\n      // 必填欄位檢核\r\n      if (!item.CBuildCaseID) {\r\n        errorMessages.push(`[項目 ${itemNum}] 建案名稱為必填欄位`);\r\n      }\r\n      \r\n      if (!item.CRequirement || item.CRequirement.trim() === '') {\r\n        errorMessages.push(`[項目 ${itemNum}] 工程項目為必填欄位`);\r\n      }\r\n      \r\n      if (!item.CHouseType || item.CHouseType.length === 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 類型為必填欄位`);\r\n      }\r\n      \r\n      if (item.CSort === null || item.CSort === undefined || item.CSort < 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 排序為必填欄位且不能為負數`);\r\n      }\r\n      \r\n      if (item.CStatus === null || item.CStatus === undefined) {\r\n        errorMessages.push(`[項目 ${itemNum}] 狀態為必填欄位`);\r\n      }\r\n      \r\n      if (item.CUnitPrice === null || item.CUnitPrice === undefined || item.CUnitPrice < 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 單價為必填欄位且不能為負數`);\r\n      }\r\n      \r\n      if (!item.CUnit || item.CUnit.trim() === '') {\r\n        errorMessages.push(`[項目 ${itemNum}] 單位為必填欄位`);\r\n      }\r\n      \r\n      // 長度驗證\r\n      if (item.CLocation && item.CLocation.length > 20) {\r\n        errorMessages.push(`[項目 ${itemNum}] 區域不能超過20個字`);\r\n      }\r\n      \r\n      if (item.CRequirement && item.CRequirement.length > 50) {\r\n        errorMessages.push(`[項目 ${itemNum}] 工程項目不能超過50個字`);\r\n      }\r\n      \r\n      if (item.CRemark && item.CRemark.length > 100) {\r\n        errorMessages.push(`[項目 ${itemNum}] 備註說明不能超過100個字`);\r\n      }\r\n    });\r\n    \r\n    return errorMessages;\r\n  }\r\n\r\n  // 批次保存\r\n  batchSave(ref: any) {\r\n    if (this.batchEditItems.length === 0) {\r\n      this.message.showErrorMSG('沒有要更新的項目');\r\n      return;\r\n    }\r\n\r\n    // 執行批次驗證\r\n    const validationErrors = this.batchValidation();\r\n    if (validationErrors.length > 0) {\r\n      this.message.showErrorMSGs(validationErrors);\r\n      return;\r\n    }\r\n\r\n    // 直接使用編輯後的資料\r\n    const batchData: SaveDataRequirement[] = this.batchEditItems.map(item => ({\r\n      CRequirementID: item.CRequirementID,\r\n      CBuildCaseID: item.CBuildCaseID,\r\n      CLocation: item.CLocation,\r\n      CRequirement: item.CRequirement,\r\n      CHouseType: item.CHouseType,\r\n      CSort: item.CSort,\r\n      CStatus: item.CStatus,\r\n      CUnitPrice: item.CUnitPrice,\r\n      CUnit: item.CUnit,\r\n      CIsShow: item.CIsShow,\r\n      CIsSimple: item.CIsSimple,\r\n      CRemark: item.CRemark\r\n    }));\r\n\r\n    // 調用批次保存 API\r\n    this.requirementService.apiRequirementBatchSaveDataPost$Json({\r\n      body: batchData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(`成功批次更新 ${this.batchEditItems.length} 個項目`);\r\n        this.selectedItems = [];\r\n        this.batchEditItems = [];\r\n        this.updateSelectAllState();\r\n        this.getList();\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '批次更新失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  // 重置批次編輯中的單一項目到原始狀態\r\n  resetBatchEditItem(index: number) {\r\n    const originalItem = this.selectedItems[index];\r\n    if (originalItem) {\r\n      this.batchEditItems[index] = {\r\n        CRequirementID: originalItem.CRequirementID,\r\n        CBuildCaseID: originalItem.CBuildCaseID,\r\n        CLocation: originalItem.CLocation,\r\n        CRequirement: originalItem.CRequirement,\r\n        CHouseType: originalItem.CHouseType ? [...originalItem.CHouseType] : [],\r\n        CSort: originalItem.CSort,\r\n        CStatus: originalItem.CStatus,\r\n        CUnitPrice: originalItem.CUnitPrice || 0,\r\n        CUnit: originalItem.CUnit,\r\n        CIsShow: originalItem.CIsShow || false,\r\n        CIsSimple: originalItem.CIsSimple || false,\r\n        CRemark: originalItem.CRemark\r\n      };\r\n    }\r\n  }\r\n\r\n  // 取消批次編輯\r\n  cancelBatchEdit(ref: any) {\r\n    this.isBatchEditMode = false;\r\n    this.batchEditItems = [];\r\n    ref.close();\r\n  }\r\n\r\n  // 備用：如果需要手動建立需求項目的方法\r\n  private batchCreateRequirements(requirements: SaveDataRequirement[]) {\r\n    const batchRequests = requirements.map(requirement =>\r\n      this.requirementService.apiRequirementSaveDataPost$Json({\r\n        body: requirement\r\n      })\r\n    );\r\n\r\n    Promise.all(batchRequests.map(req => req.toPromise()))\r\n      .then(responses => {\r\n        const successCount = responses.filter(res => res?.StatusCode === 0).length;\r\n        this.message.showSucessMSG(`成功建立 ${successCount} 個需求項目`);\r\n        this.getList();\r\n      })\r\n      .catch(error => {\r\n        console.error('批次建立需求失敗:', error);\r\n        this.message.showErrorMSG('批次建立需求時發生錯誤');\r\n      });\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n\r\n  <!-- 搜尋區域 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"buildCase\" class=\"label mr-2\">建案</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CBuildCaseID\" class=\"col-9\" (ngModelChange)=\"onBuildCaseChange($event)\">\r\n            <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"groupName\" class=\"label mr-2\">區域</label>\r\n          <input type=\"text\" nbInput id=\"groupName\" name=\"groupName\" placeholder=\"區域\"\r\n            [(ngModel)]=\"getListRequirementRequest.CLocation\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"requirement\" class=\"label mr-2\">工程項目</label>\r\n          <input type=\"text\" nbInput id=\"requirement\" name=\"requirement\" placeholder=\"工程項目\"\r\n            [(ngModel)]=\"getListRequirementRequest.CRequirement\">\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"houseType\" class=\"label mr-2\">類型</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CHouseType\" class=\"col-9\" multiple>\r\n            <nb-option *ngFor=\"let type of houseType\" [value]=\"type.value\">\r\n              {{ type.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CStatus\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isShow\" class=\"label mr-2\">預約需求</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsShow\" class=\"col-9\" placeholder=\"全部\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isSimple\" class=\"label mr-2\">簡易客變</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsSimple\" class=\"col-9\" placeholder=\"全部\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\"></div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\"></div>\r\n        <div class=\"form-group col-12 col-md-6 text-right\">\r\n          <button class=\"btn btn-secondary mr-2\" (click)=\"resetSearch()\"><i class=\"fas fa-undo mr-1\"></i>重置</button>\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <button class=\"btn btn-warning mr-2\" (click)=\"openSpaceTemplateSelector()\"><i\r\n              class=\"fas fa-layer-group mr-1\"></i>模板新增</button>\r\n          <button class=\"btn btn-primary mr-2\" (click)=\"openBatchEdit(batchEditDialog)\"\r\n            *ngIf=\"selectedItems.length > 0\"><i class=\"fas fa-edit mr-1\"></i>批次編輯 ({{selectedItems.length}})</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <!-- 建案需求列表 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <div class=\"col-12 mt-3\">\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n              <th scope=\"col\" class=\"col-1 text-center d-flex flex-column align-items-center\">\r\n                <nb-checkbox [ngModel]=\"isAllSelected\" (ngModelChange)=\"toggleSelectAll($event)\">\r\n                </nb-checkbox>\r\n                <small class=\"text-white mt-1\">全選</small>\r\n              </th>\r\n              <th scope=\"col\" class=\"col-2\">建案名稱</th>\r\n              <th scope=\"col\" class=\"col-1\">區域</th>\r\n              <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n              <th scope=\"col\" class=\"col-1\">類型</th>\r\n              <th scope=\"col\" class=\"col-1\">排序</th>\r\n              <th scope=\"col\" class=\"col-1\">狀態</th>\r\n              <th scope=\"col\" class=\"col-1\">預約需求</th>\r\n              <th scope=\"col\" class=\"col-1\">簡易客變</th>\r\n              <th scope=\"col\" class=\"col-1\">單價</th>\r\n              <th scope=\"col\" class=\"col-1\">操作功能</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n              <td class=\"col-1 text-center\">\r\n                <nb-checkbox [ngModel]=\"isItemSelected(data)\" (ngModelChange)=\"toggleItemSelection(data)\">\r\n                </nb-checkbox>\r\n              </td>\r\n              <td class=\"col-2\">{{ data.CBuildCaseName }}</td>\r\n              <td class=\"col-1\">{{ data.CLocation }}</td>\r\n              <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n              <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n              <td class=\"col-1\">{{ data.CSort }}</td>\r\n              <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n              <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n              <td class=\"col-1\">{{ getCIsSimpleText(data) }}</td>\r\n              <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n              <td class=\"col-1\">\r\n                <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                  (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                  (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n        (PageChange)=\"getList()\">\r\n      </ngx-pagination>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 建案對話框 -->\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增建案需求</span>\r\n      <span *ngIf=\"isNew===false\">編輯建案需求</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'建案名稱'\" [labelFor]=\"'CBuildCaseID'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CBuildCaseID\" name=\"CBuildCaseID\"\r\n                [(selected)]=\"saveRequirement.CBuildCaseID\">\r\n                <nb-option langg *ngFor=\"let b of buildCaseList\" [value]=\"b.cID\"> {{b.CBuildCaseName}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'區域'\" [labelFor]=\"'CLocation'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CLocation\" name=\"CLocation\" placeholder=\"區域\"\r\n                [(ngModel)]=\"saveRequirement.CLocation\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\" min=\"0\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple>\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\" step=\"0.01\" min=\"0\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'預約需求'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"true\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在預約需求清單\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'簡易客變'\" [labelFor]=\"'CIsSimple'\" [isRequired]=\"true\">\r\n              <nb-checkbox id=\"CIsSimple\" name=\"CIsSimple\" [(ngModel)]=\"saveRequirement.CIsSimple\" class=\"flex-grow-1\">\r\n                設定為簡易客變\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 批次編輯對話框 -->\r\n<ng-template #batchEditDialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 900px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span>批次編輯建案需求 (已選擇 {{selectedItems.length}} 個項目)</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem; max-height: 70vh; overflow-y: auto;\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"alert alert-info\">\r\n            <i class=\"fas fa-info-circle mr-2\"></i>\r\n            您可以個別修改每個項目的欄位，除了建案名稱之外的所有欄位都可以編輯\r\n          </div>\r\n          \r\n          <div class=\"alert alert-warning\">\r\n            <i class=\"fas fa-exclamation-triangle mr-2\"></i>\r\n            <strong>注意事項：</strong>\r\n            <ul class=\"mb-0 mt-2\">\r\n              <li>工程項目、類型、排序、狀態、單價、單位為必填欄位</li>\r\n              <li>排序和單價不能為負數</li>\r\n              <li>區域最多20個字，工程項目最多50個字，備註說明最多100個字</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <!-- 批次編輯項目列表 -->\r\n          <div *ngFor=\"let item of batchEditItems; let i = index\" class=\"mb-4\">\r\n            <nb-card>\r\n              <nb-card-header class=\"py-2 d-flex justify-content-between align-items-center\">\r\n                <h6 class=\"mb-0\">\r\n                  <i class=\"fas fa-edit mr-2\"></i>\r\n                  項目 {{i + 1}}: {{selectedItems[i].CBuildCaseName}} - {{item.CRequirement}}\r\n                </h6>\r\n                <button type=\"button\" class=\"btn btn-outline-secondary btn-sm\" \r\n                        (click)=\"resetBatchEditItem(i)\" \r\n                        title=\"重置為原始值\">\r\n                  <i class=\"fas fa-undo mr-1\"></i>重置\r\n                </button>\r\n              </nb-card-header>\r\n              <nb-card-body class=\"py-3\">\r\n                <div class=\"row\">\r\n                  <!-- 區域 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'區域'\" [labelFor]=\"'location_' + i\" [isRequired]=\"false\">\r\n                      <input type=\"text\" nbInput class=\"flex-grow-1\" [id]=\"'location_' + i\" [(ngModel)]=\"item.CLocation\"\r\n                        placeholder=\"區域\" maxlength=\"20\">\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 工程項目 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'requirement_' + i\" [isRequired]=\"true\">\r\n                      <input type=\"text\" nbInput class=\"flex-grow-1\" [id]=\"'requirement_' + i\"\r\n                        [(ngModel)]=\"item.CRequirement\" placeholder=\"工程項目\" maxlength=\"50\">\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 類型 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'類型'\" [labelFor]=\"'houseType_' + i\" [isRequired]=\"true\">\r\n                      <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" [id]=\"'houseType_' + i\"\r\n                        [(selected)]=\"item.CHouseType\" multiple>\r\n                        <nb-option *ngFor=\"let type of houseType\" [value]=\"type.value\">\r\n                          {{type.label}}\r\n                        </nb-option>\r\n                      </nb-select>\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 排序 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'排序'\" [labelFor]=\"'sort_' + i\" [isRequired]=\"true\">\r\n                      <input type=\"number\" nbInput class=\"flex-grow-1\" [id]=\"'sort_' + i\" [(ngModel)]=\"item.CSort\"\r\n                        placeholder=\"排序\" min=\"0\">\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 狀態 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'狀態'\" [labelFor]=\"'status_' + i\" [isRequired]=\"true\">\r\n                      <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" [id]=\"'status_' + i\" [(selected)]=\"item.CStatus\">\r\n                        <nb-option *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                          {{status.label}}\r\n                        </nb-option>\r\n                      </nb-select>\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 單價 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'單價'\" [labelFor]=\"'unitPrice_' + i\" [isRequired]=\"true\">\r\n                      <input type=\"number\" nbInput class=\"flex-grow-1\" [id]=\"'unitPrice_' + i\"\r\n                        [(ngModel)]=\"item.CUnitPrice\" placeholder=\"單價\" step=\"0.01\" min=\"0\">\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 單位 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'單位'\" [labelFor]=\"'unit_' + i\" [isRequired]=\"true\">\r\n                      <input type=\"text\" nbInput class=\"flex-grow-1\" [id]=\"'unit_' + i\" [(ngModel)]=\"item.CUnit\"\r\n                        placeholder=\"單位\">\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 預約需求 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'預約需求'\" [labelFor]=\"'isShow_' + i\" [isRequired]=\"true\">\r\n                      <nb-checkbox [id]=\"'isShow_' + i\" [(ngModel)]=\"item.CIsShow\" class=\"flex-grow-1\">\r\n                        顯示在預約需求清單\r\n                      </nb-checkbox>\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 簡易客變 -->\r\n                  <div class=\"col-md-6\">\r\n                    <app-form-group [label]=\"'簡易客變'\" [labelFor]=\"'isSimple_' + i\" [isRequired]=\"true\">\r\n                      <nb-checkbox [id]=\"'isSimple_' + i\" [(ngModel)]=\"item.CIsSimple\" class=\"flex-grow-1\">\r\n                        設定為簡易客變\r\n                      </nb-checkbox>\r\n                    </app-form-group>\r\n                  </div>\r\n\r\n                  <!-- 備註說明 -->\r\n                  <div class=\"col-12\">\r\n                    <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'remark_' + i\" [isRequired]=\"false\">\r\n                      <textarea nbInput class=\"flex-grow-1\" [id]=\"'remark_' + i\" [(ngModel)]=\"item.CRemark\"\r\n                        placeholder=\"備註說明\" maxlength=\"100\" rows=\"2\">\r\n                      </textarea>\r\n                    </app-form-group>\r\n                  </div>\r\n                </div>\r\n              </nb-card-body>\r\n            </nb-card>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"batchSave(ref)\">確定批次更新</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"cancelBatchEdit(ref)\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AAEA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/H,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;;ICPrDC,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;IAkBAT,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAC,KAAA,CAAoB;IAC5DX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAE,KAAA,MACF;;;;;;IAsCFZ,EAAA,CAAAC,cAAA,iBACmC;IADED,EAAA,CAAAa,UAAA,mBAAAC,0EAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,kBAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAK,aAAA,CAAAH,kBAAA,CAA8B;IAAA,EAAC;IAC1CnB,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAxCH,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAQ,kBAAA,+BAAAS,MAAA,CAAAO,aAAA,CAAAC,MAAA,MAA+B;;;;;;IAClGzB,EAAA,CAAAC,cAAA,iBAA4E;IAAvCD,EAAA,CAAAa,UAAA,mBAAAa,0EAAA;MAAA1B,EAAA,CAAAe,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAU,SAAA,GAAA5B,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAY,GAAA,CAAAD,SAAA,CAAW;IAAA,EAAC;IAAkB5B,EAAA,CAAAuB,SAAA,YAC3C;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA8CtCH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAa,UAAA,mBAAAiB,iFAAA;MAAA9B,EAAA,CAAAe,aAAA,CAAAgB,IAAA;MAAA,MAAAC,QAAA,GAAAhC,EAAA,CAAAkB,aAAA,GAAAe,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAU,SAAA,GAAA5B,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAiB,MAAA,CAAAF,QAAA,EAAAJ,SAAA,CAAmB;IAAA,EAAC;IAAC5B,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAa,UAAA,mBAAAsB,iFAAA;MAAAnC,EAAA,CAAAe,aAAA,CAAAqB,IAAA;MAAA,MAAAJ,QAAA,GAAAhC,EAAA,CAAAkB,aAAA,GAAAe,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAoB,QAAA,CAAAL,QAAA,CAAc;IAAA,EAAC;IAAChC,EAAA,CAAAuB,SAAA,YAAqC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAhB3EH,EAFJ,CAAAC,cAAA,aAAuE,aACvC,sBAC8D;IAA5CD,EAAA,CAAAa,UAAA,2BAAAyB,oFAAA;MAAA,MAAAN,QAAA,GAAAhC,EAAA,CAAAe,aAAA,CAAAwB,GAAA,EAAAN,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAAiBJ,MAAA,CAAAuB,mBAAA,CAAAR,QAAA,CAAyB;IAAA,EAAC;IAE3FhC,EADE,CAAAG,YAAA,EAAc,EACX;IACLH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAyC,UAAA,KAAAC,wDAAA,qBACgC,KAAAC,wDAAA,qBAEL;IAE/B3C,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAlBYH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA2B,cAAA,CAAAZ,QAAA,EAAgC;IAG7BhC,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAA6C,iBAAA,CAAAb,QAAA,CAAAvB,cAAA,CAAyB;IACzBT,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAA6C,iBAAA,CAAAb,QAAA,CAAAc,SAAA,CAAoB;IACpB9C,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAA6C,iBAAA,CAAAb,QAAA,CAAAe,YAAA,CAAuB;IACvB/C,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAA6C,iBAAA,CAAA5B,MAAA,CAAA+B,YAAA,CAAAhB,QAAA,CAAAiB,UAAA,IAAAjD,EAAA,CAAAkD,eAAA,KAAAC,GAAA,GAAyC;IACzCnD,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAA6C,iBAAA,CAAAb,QAAA,CAAAoB,KAAA,CAAgB;IAChBpD,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,SAAArB,QAAA,CAAAsB,OAAA,EAAkC;IAClCtD,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAA6C,iBAAA,CAAA5B,MAAA,CAAAsC,cAAA,CAAAvB,QAAA,EAA0B;IAC1BhC,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAA6C,iBAAA,CAAA5B,MAAA,CAAAuC,gBAAA,CAAAxB,QAAA,EAA4B;IAC5BhC,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,SAAArB,QAAA,CAAAyB,UAAA,OAAkD;IAEzDzD,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAyC,QAAA,CAAc;IAEd1D,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA0C,QAAA,CAAc;;;;;IAkBjC3D,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxCH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS/BH,EAAA,CAAAC,cAAA,oBAAiE;IAACD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAjDH,EAAA,CAAAI,UAAA,UAAAwD,KAAA,CAAAtD,GAAA,CAAe;IAAEN,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAQ,kBAAA,MAAAoD,KAAA,CAAAnD,cAAA,KAAoB;;;;;IAkBtFT,EAAA,CAAAC,cAAA,oBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAAyD,QAAA,CAAAlD,KAAA,CAAoB;IAAEX,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAAqD,QAAA,CAAAjD,KAAA,KAAc;;;;;IAMpFZ,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAA0D,UAAA,CAAAnD,KAAA,CAAsB;IAC1EX,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAAsD,UAAA,CAAAlD,KAAA,KAAgB;;;;;;IApC9BZ,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAAyC,UAAA,IAAAsB,8DAAA,mBAA2B,IAAAC,8DAAA,mBACC;IAC9BhE,EAAA,CAAAG,YAAA,EAAiB;IAMPH,EALV,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX,yBACkE,oBAEjC;IAA5CD,EAAA,CAAAiE,gBAAA,4BAAAC,4FAAAC,MAAA;MAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAqE,kBAAA,CAAApD,MAAA,CAAAqD,eAAA,CAAAC,YAAA,EAAAJ,MAAA,MAAAlD,MAAA,CAAAqD,eAAA,CAAAC,YAAA,GAAAJ,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAA2C;IAC3CnE,EAAA,CAAAyC,UAAA,KAAA+B,oEAAA,wBAAiE;IAErExE,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAElB;IAAvDD,EAAA,CAAAiE,gBAAA,2BAAAQ,wFAAAN,MAAA;MAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAqE,kBAAA,CAAApD,MAAA,CAAAqD,eAAA,CAAAxB,SAAA,EAAAqB,MAAA,MAAAlD,MAAA,CAAAqD,eAAA,CAAAxB,SAAA,GAAAqB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAAuC;IAC3CnE,EAFE,CAAAG,YAAA,EACyD,EAC1C;IAEfH,EADF,CAAAC,cAAA,0BAAiF,iBAEnB;IAA1DD,EAAA,CAAAiE,gBAAA,2BAAAS,wFAAAP,MAAA;MAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAqE,kBAAA,CAAApD,MAAA,CAAAqD,eAAA,CAAAvB,YAAA,EAAAoB,MAAA,MAAAlD,MAAA,CAAAqD,eAAA,CAAAvB,YAAA,GAAAoB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAA0C;IAC9CnE,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAExB;IAA5CD,EAAA,CAAAiE,gBAAA,2BAAAU,wFAAAR,MAAA;MAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAqE,kBAAA,CAAApD,MAAA,CAAAqD,eAAA,CAAAlB,KAAA,EAAAe,MAAA,MAAAlD,MAAA,CAAAqD,eAAA,CAAAlB,KAAA,GAAAe,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAAmC;IACvCnE,EAFE,CAAAG,YAAA,EAC8C,EAC/B;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEtB;IAAnDD,EAAA,CAAAiE,gBAAA,4BAAAW,6FAAAT,MAAA;MAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAqE,kBAAA,CAAApD,MAAA,CAAAqD,eAAA,CAAArB,UAAA,EAAAkB,MAAA,MAAAlD,MAAA,CAAAqD,eAAA,CAAArB,UAAA,GAAAkB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAAyC;IACzCnE,EAAA,CAAAyC,UAAA,KAAAoC,oEAAA,wBAAqE;IAEzE7E,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAiE,gBAAA,4BAAAa,6FAAAX,MAAA;MAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAqE,kBAAA,CAAApD,MAAA,CAAAqD,eAAA,CAAAhB,OAAA,EAAAa,MAAA,MAAAlD,MAAA,CAAAqD,eAAA,CAAAhB,OAAA,GAAAa,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAAsC;IACtCnE,EAAA,CAAAyC,UAAA,KAAAsC,oEAAA,wBAA6E;IAGjF/E,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAEZ;IAA7DD,EAAA,CAAAiE,gBAAA,2BAAAe,wFAAAb,MAAA;MAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAqE,kBAAA,CAAApD,MAAA,CAAAqD,eAAA,CAAAb,UAAA,EAAAU,MAAA,MAAAlD,MAAA,CAAAqD,eAAA,CAAAb,UAAA,GAAAU,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAAwC;IAC5CnE,EAFE,CAAAG,YAAA,EAC+D,EAChD;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAiE,gBAAA,2BAAAgB,wFAAAd,MAAA;MAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAqE,kBAAA,CAAApD,MAAA,CAAAqD,eAAA,CAAAY,KAAA,EAAAf,MAAA,MAAAlD,MAAA,CAAAqD,eAAA,CAAAY,KAAA,GAAAf,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAAmC;IACvCnE,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA4E,uBACyB;IAA1DD,EAAA,CAAAiE,gBAAA,2BAAAkB,8FAAAhB,MAAA;MAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAqE,kBAAA,CAAApD,MAAA,CAAAqD,eAAA,CAAAc,OAAA,EAAAjB,MAAA,MAAAlD,MAAA,CAAAqD,eAAA,CAAAc,OAAA,GAAAjB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAAqC;IAC5EnE,EAAA,CAAAE,MAAA,gEACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA8E,uBAC6B;IAA5DD,EAAA,CAAAiE,gBAAA,2BAAAoB,8FAAAlB,MAAA;MAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAqE,kBAAA,CAAApD,MAAA,CAAAqD,eAAA,CAAAgB,SAAA,EAAAnB,MAAA,MAAAlD,MAAA,CAAAqD,eAAA,CAAAgB,SAAA,GAAAnB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAAuC;IAClFnE,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,oBAEV;IAA/DD,EAAA,CAAAiE,gBAAA,2BAAAsB,2FAAApB,MAAA;MAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAqE,kBAAA,CAAApD,MAAA,CAAAqD,eAAA,CAAAkB,OAAA,EAAArB,MAAA,MAAAlD,MAAA,CAAAqD,eAAA,CAAAkB,OAAA,GAAArB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAAqC;IAKjDnE,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBAC2B;IAApBD,EAAA,CAAAa,UAAA,mBAAA4E,iFAAA;MAAA,MAAAC,OAAA,GAAA1F,EAAA,CAAAe,aAAA,CAAAqD,IAAA,EAAAuB,SAAA;MAAA,MAAA1E,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAA2E,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAAC1F,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAAa,UAAA,mBAAAgF,iFAAA;MAAA,MAAAH,OAAA,GAAA1F,EAAA,CAAAe,aAAA,CAAAqD,IAAA,EAAAuB,SAAA;MAAA,OAAA3F,EAAA,CAAAqB,WAAA,CAASqE,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAAC9F,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IAxECH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA8E,KAAA,UAAkB;IAClB/F,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA8E,KAAA,WAAmB;IAMJ/F,EAAA,CAAAO,SAAA,GAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAAgG,gBAAA,aAAA/E,MAAA,CAAAqD,eAAA,CAAAC,YAAA,CAA2C;IACZvE,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAgF,aAAA,CAAgB;IAGnCjG,EAAA,CAAAO,SAAA,EAAc;IAA0BP,EAAxC,CAAAI,UAAA,yBAAc,yBAAyB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAgG,gBAAA,YAAA/E,MAAA,CAAAqD,eAAA,CAAAxB,SAAA,CAAuC;IAE3B9C,EAAA,CAAAO,SAAA,EAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAgG,gBAAA,YAAA/E,MAAA,CAAAqD,eAAA,CAAAvB,YAAA,CAA0C;IAE9B/C,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAgG,gBAAA,YAAA/E,MAAA,CAAAqD,eAAA,CAAAlB,KAAA,CAAmC;IAEvBpD,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAgG,gBAAA,aAAA/E,MAAA,CAAAqD,eAAA,CAAArB,UAAA,CAAyC;IACPjD,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAiF,SAAA,CAAY;IAGlClG,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAgG,gBAAA,aAAA/E,MAAA,CAAAqD,eAAA,CAAAhB,OAAA,CAAsC;IACFtD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAkF,aAAA,CAAgB;IAIxCnG,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAgG,gBAAA,YAAA/E,MAAA,CAAAqD,eAAA,CAAAb,UAAA,CAAwC;IAE5BzD,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAgG,gBAAA,YAAA/E,MAAA,CAAAqD,eAAA,CAAAY,KAAA,CAAmC;IAEvBlF,EAAA,CAAAO,SAAA,EAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,oBAAoB;IAChCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAgG,gBAAA,YAAA/E,MAAA,CAAAqD,eAAA,CAAAc,OAAA,CAAqC;IAIhEpF,EAAA,CAAAO,SAAA,GAAgB;IAA0BP,EAA1C,CAAAI,UAAA,qCAAgB,yBAAyB,oBAAoB;IAC9BJ,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAgG,gBAAA,YAAA/E,MAAA,CAAAqD,eAAA,CAAAgB,SAAA,CAAuC;IAItEtF,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAgG,gBAAA,YAAA/E,MAAA,CAAAqD,eAAA,CAAAkB,OAAA,CAAqC;;;;;IA8E7BxF,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAgG,QAAA,CAAAzF,KAAA,CAAoB;IAC5DX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA4F,QAAA,CAAAxF,KAAA,MACF;;;;;IAiBAZ,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAiG,UAAA,CAAA1F,KAAA,CAAsB;IACpEX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA6F,UAAA,CAAAzF,KAAA,MACF;;;;;;IAtDRZ,EAHN,CAAAC,cAAA,cAAqE,cAC1D,yBACwE,aAC5D;IACfD,EAAA,CAAAuB,SAAA,YAAgC;IAChCvB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,iBAEuB;IADfD,EAAA,CAAAa,UAAA,mBAAAyF,uFAAA;MAAA,MAAAC,KAAA,GAAAvG,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAC,KAAA;MAAA,MAAAxF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAyF,kBAAA,CAAAH,KAAA,CAAqB;IAAA,EAAC;IAErCvG,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,oBAClC;IACFF,EADE,CAAAG,YAAA,EAAS,EACM;IAMTH,EALR,CAAAC,cAAA,uBAA2B,cACR,eAEO,0BAC6D,iBAE7C;IADoCD,EAAA,CAAAiE,gBAAA,2BAAA0C,+FAAAxC,MAAA;MAAA,MAAAyC,QAAA,GAAA5G,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAvE,SAAA;MAAAjC,EAAA,CAAAqE,kBAAA,CAAAuC,QAAA,CAAA9D,SAAA,EAAAqB,MAAA,MAAAyC,QAAA,CAAA9D,SAAA,GAAAqB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAA4B;IAGtGnE,EAHI,CAAAG,YAAA,EACkC,EACnB,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BACiE,iBAEf;IAAlED,EAAA,CAAAiE,gBAAA,2BAAA4C,+FAAA1C,MAAA;MAAA,MAAAyC,QAAA,GAAA5G,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAvE,SAAA;MAAAjC,EAAA,CAAAqE,kBAAA,CAAAuC,QAAA,CAAA7D,YAAA,EAAAoB,MAAA,MAAAyC,QAAA,CAAA7D,YAAA,GAAAoB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAA+B;IAErCnE,EAHI,CAAAG,YAAA,EACoE,EACrD,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BAC6D,qBAErC;IAAxCD,EAAA,CAAAiE,gBAAA,4BAAA6C,oGAAA3C,MAAA;MAAA,MAAAyC,QAAA,GAAA5G,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAvE,SAAA;MAAAjC,EAAA,CAAAqE,kBAAA,CAAAuC,QAAA,CAAA3D,UAAA,EAAAkB,MAAA,MAAAyC,QAAA,CAAA3D,UAAA,GAAAkB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAA8B;IAC9BnE,EAAA,CAAAyC,UAAA,KAAAsE,2EAAA,uBAA+D;IAKrE/G,EAFI,CAAAG,YAAA,EAAY,EACG,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BACwD,iBAE/C;IADyCD,EAAA,CAAAiE,gBAAA,2BAAA+C,+FAAA7C,MAAA;MAAA,MAAAyC,QAAA,GAAA5G,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAvE,SAAA;MAAAjC,EAAA,CAAAqE,kBAAA,CAAAuC,QAAA,CAAAxD,KAAA,EAAAe,MAAA,MAAAyC,QAAA,CAAAxD,KAAA,GAAAe,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAAwB;IAGhGnE,EAHI,CAAAG,YAAA,EAC2B,EACZ,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BAC0D,qBACsB;IAA5BD,EAAA,CAAAiE,gBAAA,4BAAAgD,oGAAA9C,MAAA;MAAA,MAAAyC,QAAA,GAAA5G,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAvE,SAAA;MAAAjC,EAAA,CAAAqE,kBAAA,CAAAuC,QAAA,CAAAtD,OAAA,EAAAa,MAAA,MAAAyC,QAAA,CAAAtD,OAAA,GAAAa,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAA2B;IAC/FnE,EAAA,CAAAyC,UAAA,KAAAyE,2EAAA,uBAAuE;IAK7ElH,EAFI,CAAAG,YAAA,EAAY,EACG,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BAC6D,iBAEV;IAAnED,EAAA,CAAAiE,gBAAA,2BAAAkD,+FAAAhD,MAAA;MAAA,MAAAyC,QAAA,GAAA5G,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAvE,SAAA;MAAAjC,EAAA,CAAAqE,kBAAA,CAAAuC,QAAA,CAAAnD,UAAA,EAAAU,MAAA,MAAAyC,QAAA,CAAAnD,UAAA,GAAAU,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAA6B;IAEnCnE,EAHI,CAAAG,YAAA,EACqE,EACtD,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BACwD,iBAEvD;IAD+CD,EAAA,CAAAiE,gBAAA,2BAAAmD,+FAAAjD,MAAA;MAAA,MAAAyC,QAAA,GAAA5G,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAvE,SAAA;MAAAjC,EAAA,CAAAqE,kBAAA,CAAAuC,QAAA,CAAA1B,KAAA,EAAAf,MAAA,MAAAyC,QAAA,CAAA1B,KAAA,GAAAf,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAAwB;IAG9FnE,EAHI,CAAAG,YAAA,EACmB,EACJ,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BAC4D,uBACG;IAA/CD,EAAA,CAAAiE,gBAAA,2BAAAoD,qGAAAlD,MAAA;MAAA,MAAAyC,QAAA,GAAA5G,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAvE,SAAA;MAAAjC,EAAA,CAAAqE,kBAAA,CAAAuC,QAAA,CAAAxB,OAAA,EAAAjB,MAAA,MAAAyC,QAAA,CAAAxB,OAAA,GAAAjB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAA0B;IAC1DnE,EAAA,CAAAE,MAAA,gEACF;IAEJF,EAFI,CAAAG,YAAA,EAAc,EACC,EACb;IAKFH,EAFJ,CAAAC,cAAA,eAAsB,0BAC8D,uBACK;IAAjDD,EAAA,CAAAiE,gBAAA,2BAAAqD,qGAAAnD,MAAA;MAAA,MAAAyC,QAAA,GAAA5G,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAvE,SAAA;MAAAjC,EAAA,CAAAqE,kBAAA,CAAAuC,QAAA,CAAAtB,SAAA,EAAAnB,MAAA,MAAAyC,QAAA,CAAAtB,SAAA,GAAAnB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAA4B;IAC9DnE,EAAA,CAAAE,MAAA,oDACF;IAEJF,EAFI,CAAAG,YAAA,EAAc,EACC,EACb;IAKFH,EAFJ,CAAAC,cAAA,cAAoB,0BAC+D,oBAEjC;IADaD,EAAA,CAAAiE,gBAAA,2BAAAsD,kGAAApD,MAAA;MAAA,MAAAyC,QAAA,GAAA5G,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAvE,SAAA;MAAAjC,EAAA,CAAAqE,kBAAA,CAAAuC,QAAA,CAAApB,OAAA,EAAArB,MAAA,MAAAyC,QAAA,CAAApB,OAAA,GAAArB,MAAA;MAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;IAAA,EAA0B;IAErFnE,EAAA,CAAAE,MAAA;IAMZF,EANY,CAAAG,YAAA,EAAW,EACI,EACb,EACF,EACO,EACP,EACN;;;;;;IAtGEH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAwH,kBAAA,mBAAAjB,KAAA,YAAAtF,MAAA,CAAAO,aAAA,CAAA+E,KAAA,EAAA9F,cAAA,SAAAmG,QAAA,CAAA7D,YAAA,MACF;IAWoB/C,EAAA,CAAAO,SAAA,GAAc;IAA8BP,EAA5C,CAAAI,UAAA,yBAAc,2BAAAmG,KAAA,CAA6B,qBAAqB;IAC/BvG,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,qBAAAmG,KAAA,CAAsB;IAACvG,EAAA,CAAAgG,gBAAA,YAAAY,QAAA,CAAA9D,SAAA,CAA4B;IAOpF9C,EAAA,CAAAO,SAAA,GAAgB;IAAiCP,EAAjD,CAAAI,UAAA,qCAAgB,8BAAAmG,KAAA,CAAgC,oBAAoB;IACnCvG,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,wBAAAmG,KAAA,CAAyB;IACtEvG,EAAA,CAAAgG,gBAAA,YAAAY,QAAA,CAAA7D,YAAA,CAA+B;IAMnB/C,EAAA,CAAAO,SAAA,GAAc;IAA+BP,EAA7C,CAAAI,UAAA,yBAAc,4BAAAmG,KAAA,CAA8B,oBAAoB;IAC7BvG,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAI,UAAA,sBAAAmG,KAAA,CAAuB;IACtEvG,EAAA,CAAAgG,gBAAA,aAAAY,QAAA,CAAA3D,UAAA,CAA8B;IACFjD,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAiF,SAAA,CAAY;IAS5BlG,EAAA,CAAAO,SAAA,GAAc;IAA0BP,EAAxC,CAAAI,UAAA,yBAAc,uBAAAmG,KAAA,CAAyB,oBAAoB;IACxBvG,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,iBAAAmG,KAAA,CAAkB;IAACvG,EAAA,CAAAgG,gBAAA,YAAAY,QAAA,CAAAxD,KAAA,CAAwB;IAO9EpD,EAAA,CAAAO,SAAA,GAAc;IAA4BP,EAA1C,CAAAI,UAAA,yBAAc,yBAAAmG,KAAA,CAA2B,oBAAoB;IAC1BvG,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,mBAAAmG,KAAA,CAAoB;IAACvG,EAAA,CAAAgG,gBAAA,aAAAY,QAAA,CAAAtD,OAAA,CAA2B;IACjEtD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAkF,aAAA,CAAgB;IASlCnG,EAAA,CAAAO,SAAA,GAAc;IAA+BP,EAA7C,CAAAI,UAAA,yBAAc,4BAAAmG,KAAA,CAA8B,oBAAoB;IAC7BvG,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAI,UAAA,sBAAAmG,KAAA,CAAuB;IACtEvG,EAAA,CAAAgG,gBAAA,YAAAY,QAAA,CAAAnD,UAAA,CAA6B;IAMjBzD,EAAA,CAAAO,SAAA,GAAc;IAA0BP,EAAxC,CAAAI,UAAA,yBAAc,uBAAAmG,KAAA,CAAyB,oBAAoB;IAC1BvG,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,iBAAAmG,KAAA,CAAkB;IAACvG,EAAA,CAAAgG,gBAAA,YAAAY,QAAA,CAAA1B,KAAA,CAAwB;IAO5ElF,EAAA,CAAAO,SAAA,GAAgB;IAA4BP,EAA5C,CAAAI,UAAA,qCAAgB,yBAAAmG,KAAA,CAA2B,oBAAoB;IAChEvG,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,mBAAAmG,KAAA,CAAoB;IAACvG,EAAA,CAAAgG,gBAAA,YAAAY,QAAA,CAAAxB,OAAA,CAA0B;IAQ9CpF,EAAA,CAAAO,SAAA,GAAgB;IAA8BP,EAA9C,CAAAI,UAAA,qCAAgB,2BAAAmG,KAAA,CAA6B,oBAAoB;IAClEvG,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAI,UAAA,qBAAAmG,KAAA,CAAsB;IAACvG,EAAA,CAAAgG,gBAAA,YAAAY,QAAA,CAAAtB,SAAA,CAA4B;IAQlDtF,EAAA,CAAAO,SAAA,GAAgB;IAA4BP,EAA5C,CAAAI,UAAA,qCAAgB,yBAAAmG,KAAA,CAA2B,qBAAqB;IACxCvG,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAI,UAAA,mBAAAmG,KAAA,CAAoB;IAACvG,EAAA,CAAAgG,gBAAA,YAAAY,QAAA,CAAApB,OAAA,CAA0B;;;;;;IAxHrGxF,EAFJ,CAAAC,cAAA,kBAAyF,qBACvE,WACR;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACzC;IAIXH,EAHN,CAAAC,cAAA,uBAA6E,aAC1D,aACK,cACY;IAC5BD,EAAA,CAAAuB,SAAA,YAAuC;IACvCvB,EAAA,CAAAE,MAAA,+MACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAuB,SAAA,aAAgD;IAChDvB,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEpBH,EADF,CAAAC,cAAA,cAAsB,UAChB;IAAAD,EAAA,CAAAE,MAAA,wJAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oEAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,+JAA+B;IAEvCF,EAFuC,CAAAG,YAAA,EAAK,EACrC,EACD;IAGNH,EAAA,CAAAyC,UAAA,KAAAgF,8DAAA,oBAAqE;IA8G3EzH,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBACgC;IAAzBD,EAAA,CAAAa,UAAA,mBAAA6G,iFAAA;MAAA,MAAAC,OAAA,GAAA3H,EAAA,CAAAe,aAAA,CAAA6G,IAAA,EAAAjC,SAAA;MAAA,MAAA1E,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAA4G,SAAA,CAAAF,OAAA,CAAc;IAAA,EAAC;IAAC3H,EAAA,CAAAE,MAAA,4CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7EH,EAAA,CAAAC,cAAA,kBAAmE;IAA/BD,EAAA,CAAAa,UAAA,mBAAAiH,iFAAA;MAAA,MAAAH,OAAA,GAAA3H,EAAA,CAAAe,aAAA,CAAA6G,IAAA,EAAAjC,SAAA;MAAA,MAAA1E,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAA8G,eAAA,CAAAJ,OAAA,CAAoB;IAAA,EAAC;IAAC3H,EAAA,CAAAE,MAAA,oBAAE;IAI7EF,EAJ6E,CAAAG,YAAA,EAAS,EAC1E,EACF,EACS,EACT;;;;IA5IAH,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAQ,kBAAA,0EAAAS,MAAA,CAAAO,aAAA,CAAAC,MAAA,yBAA2C;IAqBvBzB,EAAA,CAAAO,SAAA,IAAmB;IAAnBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA+G,cAAA,CAAmB;;;ADrMnD,OAAM,MAAOC,8BAA+B,SAAQ5I,aAAa;EAC/D6I,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB,EACtBC,4BAA0D;IAElE,KAAK,CAACV,MAAM,CAAC;IAZL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,4BAA4B,GAA5BA,4BAA4B;IAOtC;IACA,KAAAC,yBAAyB,GAAG,EAA0F;IACtH,KAAAC,qBAAqB,GAA8B,EAAE;IAErD;IACA,KAAA9C,aAAa,GAA8B,EAAE;IAC7C,KAAA+C,eAAe,GAAqB,EAAE;IACtC,KAAA1E,eAAe,GAAqE;MAAErB,UAAU,EAAE;IAAE,CAAE;IAEtG,KAAAkD,aAAa,GAAG,CACd;MAAExF,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAsF,SAAS,GAAG,IAAI,CAACkC,UAAU,CAACa,cAAc,CAAClJ,aAAa,CAAC;IACzD,KAAAgG,KAAK,GAAG,KAAK;IACb,KAAAmD,gBAAgB,GAAG,CAAC;IAEpB;IACA,KAAA1H,aAAa,GAAqB,EAAE;IACpC,KAAA2H,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,KAAK;IACvB;IACA,KAAApB,cAAc,GAAyE,EAAE;IA1BvF,IAAI,CAACqB,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EA0BSC,QAAQA,CAAA,GAAW;EAE5B;EACAF,oBAAoBA,CAAA;IAClB,IAAI,CAACP,yBAAyB,CAACxF,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACwF,yBAAyB,CAAC1D,OAAO,GAAG,IAAI;IAC7C,IAAI,CAAC0D,yBAAyB,CAACxD,SAAS,GAAG,IAAI;IAC/C,IAAI,CAACwD,yBAAyB,CAAC/F,YAAY,GAAG,EAAE;IAChD,IAAI,CAAC+F,yBAAyB,CAAChG,SAAS,GAAG,EAAE;IAC7C;IACA,IAAI,CAACgG,yBAAyB,CAAC7F,UAAU,GAAG,IAAI,CAACiD,SAAS,CAACsD,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC9I,KAAK,CAAC;EACpF;EAEA;EACA+I,iBAAiBA,CAACC,cAAmB;IACnC;IACA,IAAI,CAACnI,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC2H,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACnB,cAAc,GAAG,EAAE;IACxB,IAAI,CAACoB,eAAe,GAAG,KAAK;IAE5B;IACA,IAAI,CAACF,gBAAgB,GAAGS,cAAc;IACtC,IAAI,CAACC,OAAO,EAAE;EAChB;EAEA;EACAC,WAAWA,CAAA;IACT,IAAI,CAACR,oBAAoB,EAAE;IAC3B;IACA,IAAI,CAAC7H,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC2H,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACnB,cAAc,GAAG,EAAE;IACxB,IAAI,CAACoB,eAAe,GAAG,KAAK;IAE5B;IACA,IAAI,IAAI,CAACnD,aAAa,IAAI,IAAI,CAACA,aAAa,CAACxE,MAAM,GAAG,CAAC,EAAE;MACvDqI,UAAU,CAAC,MAAK;QACd,IAAI,CAAChB,yBAAyB,CAACvE,YAAY,GAAG,IAAI,CAAC0B,aAAa,CAAC,CAAC,CAAC,CAAC3F,GAAG;QACvE,IAAI,CAACsJ,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEA5G,YAAYA,CAAC+G,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAACnE,SAAS,CAACoE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5J,KAAK,IAAIyJ,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAACzJ,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOsJ,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACnC,KAAK,CAACoC,KAAK,EAAE;IAElB;IACA,IAAI,CAACpC,KAAK,CAACqC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtG,eAAe,CAACC,YAAY,CAAC;IAChE,IAAI,CAACgE,KAAK,CAACqC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtG,eAAe,CAACvB,YAAY,CAAC;IAC9D,IAAI,CAACwF,KAAK,CAACqC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACtG,eAAe,CAACrB,UAAU,CAAC;IAC7D,IAAI,CAACsF,KAAK,CAACqC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtG,eAAe,CAAClB,KAAK,CAAC;IACvD,IAAI,CAACmF,KAAK,CAACqC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtG,eAAe,CAAChB,OAAO,CAAC;IACzD,IAAI,CAACiF,KAAK,CAACqC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtG,eAAe,CAACb,UAAU,CAAC;IAC5D,IAAI,CAAC8E,KAAK,CAACqC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtG,eAAe,CAACY,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACZ,eAAe,CAAClB,KAAK,KAAK,IAAI,IAAI,IAAI,CAACkB,eAAe,CAAClB,KAAK,KAAKyH,SAAS,IAAI,IAAI,CAACvG,eAAe,CAAClB,KAAK,GAAG,CAAC,EAAE;MACrH,IAAI,CAACmF,KAAK,CAACuC,aAAa,CAACN,IAAI,CAAC,YAAY,CAAC;IAC7C;IAEA,IAAI,IAAI,CAAClG,eAAe,CAACb,UAAU,KAAK,IAAI,IAAI,IAAI,CAACa,eAAe,CAACb,UAAU,KAAKoH,SAAS,IAAI,IAAI,CAACvG,eAAe,CAACb,UAAU,GAAG,CAAC,EAAE;MACpI,IAAI,CAAC8E,KAAK,CAACuC,aAAa,CAACN,IAAI,CAAC,YAAY,CAAC;IAC7C;IAEA;IACA,IAAI,IAAI,CAAClG,eAAe,CAACxB,SAAS,IAAI,IAAI,CAACwB,eAAe,CAACxB,SAAS,CAACrB,MAAM,GAAG,EAAE,EAAE;MAChF,IAAI,CAAC8G,KAAK,CAACuC,aAAa,CAACN,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA,IAAI,IAAI,CAAClG,eAAe,CAACvB,YAAY,IAAI,IAAI,CAACuB,eAAe,CAACvB,YAAY,CAACtB,MAAM,GAAG,EAAE,EAAE;MACtF,IAAI,CAAC8G,KAAK,CAACuC,aAAa,CAACN,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAClG,eAAe,CAACkB,OAAO,IAAI,IAAI,CAAClB,eAAe,CAACkB,OAAO,CAAC/D,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAAC8G,KAAK,CAACuC,aAAa,CAACN,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEA3I,GAAGA,CAACkJ,MAAwB;IAC1B,IAAI,CAAChF,KAAK,GAAG,IAAI;IACjB,IAAI,CAACzB,eAAe,GAAG;MAAErB,UAAU,EAAE,EAAE;MAAEmC,OAAO,EAAE,KAAK;MAAEE,SAAS,EAAE;IAAK,CAAE;IAC3E,IAAI,CAAChB,eAAe,CAAChB,OAAO,GAAG,CAAC;IAChC,IAAI,CAACgB,eAAe,CAACb,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAACyF,gBAAgB,IAAI,CAAC,EAAE;MAC9B,IAAI,CAAC5E,eAAe,CAACC,YAAY,GAAG,IAAI,CAAC2E,gBAAgB;IAC3D,CAAC,MAAM,IAAI,IAAI,CAACjD,aAAa,IAAI,IAAI,CAACA,aAAa,CAACxE,MAAM,GAAG,CAAC,EAAE;MAC9D,IAAI,CAAC6C,eAAe,CAACC,YAAY,GAAG,IAAI,CAAC0B,aAAa,CAAC,CAAC,CAAC,CAAC3F,GAAG;IAC/D;IAEA,IAAI,CAAC+H,aAAa,CAAC2C,IAAI,CAACD,MAAM,CAAC;EACjC;EAEM7I,MAAMA,CAAC+I,IAAoB,EAAEF,MAAwB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAACnC,qBAAqB,CAACqC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAACnF,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMmF,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAAC7C,aAAa,CAAC2C,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA1F,IAAIA,CAAC6F,GAAQ;IACX,IAAI,CAACf,UAAU,EAAE;IACjB,IAAI,IAAI,CAACnC,KAAK,CAACuC,aAAa,CAACrJ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC6G,OAAO,CAACoD,aAAa,CAAC,IAAI,CAACnD,KAAK,CAACuC,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACrC,kBAAkB,CAACkD,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAACtH;KACZ,CAAC,CAACuH,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACzD,OAAO,CAAC0D,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACpC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACtB,OAAO,CAAC2D,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAAC3F,KAAK,EAAE;EACb;EAEAzD,QAAQA,CAAC4I,IAAoB;IAC3B,IAAI,CAAC3G,eAAe,CAAC8G,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAACrF,KAAK,GAAG,KAAK;IAClB,IAAIoG,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAAC5D,kBAAkB,CAAC6D,iCAAiC,CAAC;MACxDV,IAAI,EAAE;QACJR,cAAc,EAAE,IAAI,CAAC9G,eAAe,CAAC8G;;KAExC,CAAC,CAACS,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACxD,OAAO,CAAC0D,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACpC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAN,gBAAgBA,CAAA;IACd,IAAI,CAACd,gBAAgB,CAAC+D,qCAAqC,CAAC;MAAEX,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEY,IAAI,CAAClN,kBAAkB,CAAC,IAAI,CAACsJ,UAAU,CAAC,CAAC,CAACiD,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAC7F,aAAa,GAAG6F,GAAG,CAACW,OAAQ;MACjC;MACA,IAAI,IAAI,CAACxG,aAAa,CAACxE,MAAM,GAAG,CAAC,EAAE;QACjC,IAAI,CAACqH,yBAAyB,CAACvE,YAAY,GAAG,IAAI,CAAC0B,aAAa,CAAC,CAAC,CAAC,CAAC3F,GAAG;QACvE,IAAI,CAACsJ,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAACd,yBAAyB,CAAC4D,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAAC7D,yBAAyB,CAAC8D,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAAC7D,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAAC8D,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,IAAI,CAAChE,yBAAyB,CAACvE,YAAY,IAAI,IAAI,CAACuE,yBAAyB,CAACvE,YAAY,IAAI,CAAC,EAAE;MACnG,IAAI,CAAC2E,gBAAgB,GAAG,IAAI,CAACJ,yBAAyB,CAACvE,YAAY;IACrE;IAEA,IAAI,CAACkE,kBAAkB,CAACsE,8BAA8B,CAAC;MAAEnB,IAAI,EAAE,IAAI,CAAC9C;IAAyB,CAAE,CAAC,CAC7F0D,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACzD,eAAe,GAAG8C,GAAG,CAACW,OAAO;UAClC,IAAI,CAACK,YAAY,GAAGhB,GAAG,CAACkB,UAAW;UAEnC;UACA,IAAI,CAACxL,aAAa,GAAG,IAAI,CAACA,aAAa,CAACyL,MAAM,CAACC,YAAY,IACzD,IAAI,CAAClE,eAAe,CAACmE,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAAChC,cAAc,KAAK8B,YAAY,CAAC9B,cAAc,CAAC,CAC/F;UAED;UACA,IAAI,CAACiC,oBAAoB,EAAE;QAC7B;MACF;IACF,CAAC,CAAC;EACN;EAEAhC,OAAOA,CAAA;IACL,IAAI,CAAC5C,kBAAkB,CAAC6E,8BAA8B,CAAC;MAAE1B,IAAI,EAAE,IAAI,CAAC7C;IAAqB,CAAE,CAAC,CACzFyD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACnI,eAAe,GAAG;YAAErB,UAAU,EAAE,EAAE;YAAEmC,OAAO,EAAE,KAAK;YAAEE,SAAS,EAAE;UAAK,CAAE;UAC3E,IAAI,CAAChB,eAAe,CAACC,YAAY,GAAGuH,GAAG,CAACW,OAAO,CAAClI,YAAY;UAC5D,IAAI,CAACD,eAAe,CAACxB,SAAS,GAAGgJ,GAAG,CAACW,OAAO,CAAC3J,SAAS;UACtD,IAAI,CAACwB,eAAe,CAACrB,UAAU,GAAG6I,GAAG,CAACW,OAAO,CAACxJ,UAAU,GAAI+G,KAAK,CAACC,OAAO,CAAC6B,GAAG,CAACW,OAAO,CAACxJ,UAAU,CAAC,GAAG6I,GAAG,CAACW,OAAO,CAACxJ,UAAU,GAAG,CAAC6I,GAAG,CAACW,OAAO,CAACxJ,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACqB,eAAe,CAACkB,OAAO,GAAGsG,GAAG,CAACW,OAAO,CAACjH,OAAO;UAClD,IAAI,CAAClB,eAAe,CAACvB,YAAY,GAAG+I,GAAG,CAACW,OAAO,CAAC1J,YAAY;UAC5D,IAAI,CAACuB,eAAe,CAAC8G,cAAc,GAAGU,GAAG,CAACW,OAAO,CAACrB,cAAc;UAChE,IAAI,CAAC9G,eAAe,CAAClB,KAAK,GAAG0I,GAAG,CAACW,OAAO,CAACrJ,KAAK;UAC9C,IAAI,CAACkB,eAAe,CAAChB,OAAO,GAAGwI,GAAG,CAACW,OAAO,CAACnJ,OAAO;UAClD,IAAI,CAACgB,eAAe,CAACb,UAAU,GAAGqI,GAAG,CAACW,OAAO,CAAChJ,UAAU,IAAI,CAAC;UAC7D,IAAI,CAACa,eAAe,CAACY,KAAK,GAAG4G,GAAG,CAACW,OAAO,CAACvH,KAAK;UAC9C,IAAI,CAACZ,eAAe,CAACc,OAAO,GAAG0G,GAAG,CAACW,OAAO,CAACrH,OAAO,IAAI,KAAK;UAC3D,IAAI,CAACd,eAAe,CAACgB,SAAS,GAAGwG,GAAG,CAACW,OAAO,CAACnH,SAAS,IAAI,KAAK;QACjE;MACF;IACF,CAAC,CAAC;EACN;EAEAiI,iBAAiBA,CAAC5M,KAAa,EAAE6M,OAAY;IAC3CjC,OAAO,CAACC,GAAG,CAACgC,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAAClJ,eAAe,CAACrB,UAAU,EAAEwK,QAAQ,CAAC9M,KAAK,CAAC,EAAE;QACrD,IAAI,CAAC2D,eAAe,CAACrB,UAAU,EAAEuH,IAAI,CAAC7J,KAAK,CAAC;MAC9C;MACA4K,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClH,eAAe,CAACrB,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACqB,eAAe,CAACrB,UAAU,GAAG,IAAI,CAACqB,eAAe,CAACrB,UAAU,EAAEgK,MAAM,CAACS,CAAC,IAAIA,CAAC,KAAK/M,KAAK,CAAC;IAC7F;EACF;EAEA4C,cAAcA,CAAC0H,IAAS;IACtB,OAAOA,IAAI,CAAC7F,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAEA5B,gBAAgBA,CAACyH,IAAS;IACxB,OAAOA,IAAI,CAAC3F,SAAS,GAAG,GAAG,GAAG,GAAG;EACnC;EAEA;EACAqI,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAAC7E,yBAAyB,CAACvE,YAAY,EAAE;MAChD,IAAI,CAAC+D,OAAO,CAAC2D,YAAY,CAAC,QAAQ,CAAC;MACnC;IACF;IAEA;IACA,IAAI,CAACpD,4BAA4B,CAAC+E,YAAY,CAAC,IAAI,CAAC9E,yBAAyB,CAACvE,YAAY,EAAEsJ,QAAQ,EAAE,IAAI,EAAE,CAAC,CAC1GhC,SAAS,CAACiC,MAAM,IAAG;MAClB,IAAIA,MAAM,EAAE;QACV,IAAI,CAACC,sBAAsB,CAACD,MAAM,CAAC;MACrC;IACF,CAAC,CAAC;EACN;EAEAC,sBAAsBA,CAACC,MAA2B;IAChDzC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEwC,MAAM,CAAC;IAEhC;IACA,IAAI,CAACC,mBAAmB,CAACD,MAAM,CAACxM,aAAa,CAAC;EAChD;EAEA;EACQyM,mBAAmBA,CAACC,iBAAwB;IAClD,IAAI,CAAC,IAAI,CAACpF,yBAAyB,CAACvE,YAAY,EAAE;MAChD,IAAI,CAAC+D,OAAO,CAAC2D,YAAY,CAAC,WAAW,CAAC;MACtC;IACF;IAEA;IACA,MAAMkC,aAAa,GAAGD,iBAAiB,CAAC1E,GAAG,CAAC4E,QAAQ,IAAG;MACrD,MAAMC,mBAAmB,GAAG;QAC1B9J,YAAY,EAAE,IAAI,CAACuE,yBAAyB,CAACvE,YAAY;QACzD+J,WAAW,EAAEF,QAAQ,CAACG;OACvB;MAED,OAAO,IAAI,CAAC9F,kBAAkB,CAAC+F,uCAAuC,CAAC;QACrE5C,IAAI,EAAEyC;OACP,CAAC;IACJ,CAAC,CAAC;IAEF;IACAI,OAAO,CAACC,GAAG,CAACP,aAAa,CAAC3E,GAAG,CAACmF,GAAG,IAAIA,GAAG,CAACC,SAAS,EAAE,CAAC,CAAC,CACnDC,IAAI,CAACC,SAAS,IAAG;MAChB,MAAMC,YAAY,GAAGD,SAAS,CAAC7B,MAAM,CAACnB,GAAG,IAAIA,GAAG,EAAEC,UAAU,KAAK,CAAC,CAAC,CAACtK,MAAM;MAC1E,MAAMuN,SAAS,GAAGF,SAAS,CAACrN,MAAM,GAAGsN,YAAY;MAEjD,IAAIC,SAAS,KAAK,CAAC,EAAE;QACnB,IAAI,CAAC1G,OAAO,CAAC0D,aAAa,CAAC,QAAQ+C,YAAY,QAAQ,CAAC;MAC1D,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;QAC3B,IAAI,CAACzG,OAAO,CAAC0D,aAAa,CAAC,QAAQ+C,YAAY,UAAUC,SAAS,MAAM,CAAC;MAC3E,CAAC,MAAM;QACL,IAAI,CAAC1G,OAAO,CAAC2D,YAAY,CAAC,QAAQ,CAAC;MACrC;MAEA;MACA,IAAI,CAACrC,OAAO,EAAE;IAChB,CAAC,CAAC,CACDqF,KAAK,CAAC3D,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAI,CAAChD,OAAO,CAAC2D,YAAY,CAAC,aAAa,CAAC;IAC1C,CAAC,CAAC;EACN;EAEA;EAEA;EACAzJ,mBAAmBA,CAAC0M,IAAoB;IACtC,MAAMzI,KAAK,GAAG,IAAI,CAACjF,aAAa,CAAC2N,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAAChE,cAAc,KAAK8D,IAAI,CAAC9D,cAAc,CAAC;IACvG,IAAI3E,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACjF,aAAa,CAAC6N,MAAM,CAAC5I,KAAK,EAAE,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,IAAI,CAACjF,aAAa,CAACgJ,IAAI,CAAC0E,IAAI,CAAC;IAC/B;IACA,IAAI,CAAC7B,oBAAoB,EAAE;EAC7B;EAEA;EACAiC,eAAeA,CAACC,QAAiB;IAC/B,IAAI,IAAI,CAACvG,eAAe,CAACvH,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACD,aAAa,GAAG,EAAE;MACvB,IAAI,CAAC2H,aAAa,GAAG,KAAK;MAC1B;IACF;IAEA;IACA,IAAI,CAACA,aAAa,GAAGoG,QAAQ;IAE7B;IACA,IAAI,IAAI,CAACpG,aAAa,EAAE;MACtB,IAAI,CAAC3H,aAAa,GAAG,CAAC,GAAG,IAAI,CAACwH,eAAe,CAAC;IAChD,CAAC,MAAM;MACL,IAAI,CAACxH,aAAa,GAAG,EAAE;IACzB;EACF;EAEA;EACA6L,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACrE,eAAe,CAACvH,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC0H,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC3H,aAAa,CAACC,MAAM,KAAK,IAAI,CAACuH,eAAe,CAACvH,MAAM;IAChF;EACF;EAEA;EACAmB,cAAcA,CAACsM,IAAoB;IACjC,OAAO,IAAI,CAAC1N,aAAa,CAAC2L,IAAI,CAACiC,QAAQ,IAAIA,QAAQ,CAAChE,cAAc,KAAK8D,IAAI,CAAC9D,cAAc,CAAC;EAC7F;EAEA;EACA9J,aAAaA,CAACyJ,MAAwB;IACpC,IAAI,IAAI,CAACvJ,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAAC6G,OAAO,CAAC2D,YAAY,CAAC,YAAY,CAAC;MACvC;IACF;IAEA,IAAI,CAAC7C,eAAe,GAAG,IAAI;IAE3B;IACA,IAAI,CAACpB,cAAc,GAAG,IAAI,CAACxG,aAAa,CAACgI,GAAG,CAAC0F,IAAI,KAAK;MACpD9D,cAAc,EAAE8D,IAAI,CAAC9D,cAAc;MACnC7G,YAAY,EAAE2K,IAAI,CAAC3K,YAAY;MAC/BzB,SAAS,EAAEoM,IAAI,CAACpM,SAAS;MACzBC,YAAY,EAAEmM,IAAI,CAACnM,YAAY;MAC/BE,UAAU,EAAEiM,IAAI,CAACjM,UAAU,GAAG,CAAC,GAAGiM,IAAI,CAACjM,UAAU,CAAC,GAAG,EAAE;MACvDG,KAAK,EAAE8L,IAAI,CAAC9L,KAAK;MACjBE,OAAO,EAAE4L,IAAI,CAAC5L,OAAO;MACrBG,UAAU,EAAEyL,IAAI,CAACzL,UAAU,IAAI,CAAC;MAChCyB,KAAK,EAAEgK,IAAI,CAAChK,KAAK;MACjBE,OAAO,EAAE8J,IAAI,CAAC9J,OAAO,IAAI,KAAK;MAC9BE,SAAS,EAAE4J,IAAI,CAAC5J,SAAS,IAAI,KAAK;MAClCE,OAAO,EAAE0J,IAAI,CAAC1J;KACf,CAAC,CAAC;IAEH,IAAI,CAAC6C,aAAa,CAAC2C,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACAyE,eAAeA,CAAA;IACb,MAAM1E,aAAa,GAAa,EAAE;IAElC,IAAI,CAAC9C,cAAc,CAACmC,OAAO,CAAC,CAAC+E,IAAI,EAAEzI,KAAK,KAAI;MAC1C,MAAMgJ,OAAO,GAAGhJ,KAAK,GAAG,CAAC;MAEzB;MACA,IAAI,CAACyI,IAAI,CAAC3K,YAAY,EAAE;QACtBuG,aAAa,CAACN,IAAI,CAAC,OAAOiF,OAAO,aAAa,CAAC;MACjD;MAEA,IAAI,CAACP,IAAI,CAACnM,YAAY,IAAImM,IAAI,CAACnM,YAAY,CAAC2M,IAAI,EAAE,KAAK,EAAE,EAAE;QACzD5E,aAAa,CAACN,IAAI,CAAC,OAAOiF,OAAO,aAAa,CAAC;MACjD;MAEA,IAAI,CAACP,IAAI,CAACjM,UAAU,IAAIiM,IAAI,CAACjM,UAAU,CAACxB,MAAM,KAAK,CAAC,EAAE;QACpDqJ,aAAa,CAACN,IAAI,CAAC,OAAOiF,OAAO,WAAW,CAAC;MAC/C;MAEA,IAAIP,IAAI,CAAC9L,KAAK,KAAK,IAAI,IAAI8L,IAAI,CAAC9L,KAAK,KAAKyH,SAAS,IAAIqE,IAAI,CAAC9L,KAAK,GAAG,CAAC,EAAE;QACrE0H,aAAa,CAACN,IAAI,CAAC,OAAOiF,OAAO,iBAAiB,CAAC;MACrD;MAEA,IAAIP,IAAI,CAAC5L,OAAO,KAAK,IAAI,IAAI4L,IAAI,CAAC5L,OAAO,KAAKuH,SAAS,EAAE;QACvDC,aAAa,CAACN,IAAI,CAAC,OAAOiF,OAAO,WAAW,CAAC;MAC/C;MAEA,IAAIP,IAAI,CAACzL,UAAU,KAAK,IAAI,IAAIyL,IAAI,CAACzL,UAAU,KAAKoH,SAAS,IAAIqE,IAAI,CAACzL,UAAU,GAAG,CAAC,EAAE;QACpFqH,aAAa,CAACN,IAAI,CAAC,OAAOiF,OAAO,iBAAiB,CAAC;MACrD;MAEA,IAAI,CAACP,IAAI,CAAChK,KAAK,IAAIgK,IAAI,CAAChK,KAAK,CAACwK,IAAI,EAAE,KAAK,EAAE,EAAE;QAC3C5E,aAAa,CAACN,IAAI,CAAC,OAAOiF,OAAO,WAAW,CAAC;MAC/C;MAEA;MACA,IAAIP,IAAI,CAACpM,SAAS,IAAIoM,IAAI,CAACpM,SAAS,CAACrB,MAAM,GAAG,EAAE,EAAE;QAChDqJ,aAAa,CAACN,IAAI,CAAC,OAAOiF,OAAO,cAAc,CAAC;MAClD;MAEA,IAAIP,IAAI,CAACnM,YAAY,IAAImM,IAAI,CAACnM,YAAY,CAACtB,MAAM,GAAG,EAAE,EAAE;QACtDqJ,aAAa,CAACN,IAAI,CAAC,OAAOiF,OAAO,gBAAgB,CAAC;MACpD;MAEA,IAAIP,IAAI,CAAC1J,OAAO,IAAI0J,IAAI,CAAC1J,OAAO,CAAC/D,MAAM,GAAG,GAAG,EAAE;QAC7CqJ,aAAa,CAACN,IAAI,CAAC,OAAOiF,OAAO,iBAAiB,CAAC;MACrD;IACF,CAAC,CAAC;IAEF,OAAO3E,aAAa;EACtB;EAEA;EACAjD,SAASA,CAAC4D,GAAQ;IAChB,IAAI,IAAI,CAACzD,cAAc,CAACvG,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAAC6G,OAAO,CAAC2D,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;IAEA;IACA,MAAM0D,gBAAgB,GAAG,IAAI,CAACH,eAAe,EAAE;IAC/C,IAAIG,gBAAgB,CAAClO,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAAC6G,OAAO,CAACoD,aAAa,CAACiE,gBAAgB,CAAC;MAC5C;IACF;IAEA;IACA,MAAMC,SAAS,GAA0B,IAAI,CAAC5H,cAAc,CAACwB,GAAG,CAAC0F,IAAI,KAAK;MACxE9D,cAAc,EAAE8D,IAAI,CAAC9D,cAAc;MACnC7G,YAAY,EAAE2K,IAAI,CAAC3K,YAAY;MAC/BzB,SAAS,EAAEoM,IAAI,CAACpM,SAAS;MACzBC,YAAY,EAAEmM,IAAI,CAACnM,YAAY;MAC/BE,UAAU,EAAEiM,IAAI,CAACjM,UAAU;MAC3BG,KAAK,EAAE8L,IAAI,CAAC9L,KAAK;MACjBE,OAAO,EAAE4L,IAAI,CAAC5L,OAAO;MACrBG,UAAU,EAAEyL,IAAI,CAACzL,UAAU;MAC3ByB,KAAK,EAAEgK,IAAI,CAAChK,KAAK;MACjBE,OAAO,EAAE8J,IAAI,CAAC9J,OAAO;MACrBE,SAAS,EAAE4J,IAAI,CAAC5J,SAAS;MACzBE,OAAO,EAAE0J,IAAI,CAAC1J;KACf,CAAC,CAAC;IAEH;IACA,IAAI,CAACiD,kBAAkB,CAACoH,oCAAoC,CAAC;MAC3DjE,IAAI,EAAEgE;KACP,CAAC,CAAC/D,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACzD,OAAO,CAAC0D,aAAa,CAAC,UAAU,IAAI,CAAChE,cAAc,CAACvG,MAAM,MAAM,CAAC;QACtE,IAAI,CAACD,aAAa,GAAG,EAAE;QACvB,IAAI,CAACwG,cAAc,GAAG,EAAE;QACxB,IAAI,CAACqF,oBAAoB,EAAE;QAC3B,IAAI,CAACzD,OAAO,EAAE;QACd6B,GAAG,CAAC3F,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACwC,OAAO,CAAC2D,YAAY,CAACH,GAAG,CAACI,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEA;EACAxF,kBAAkBA,CAACD,KAAa;IAC9B,MAAMqJ,YAAY,GAAG,IAAI,CAACtO,aAAa,CAACiF,KAAK,CAAC;IAC9C,IAAIqJ,YAAY,EAAE;MAChB,IAAI,CAAC9H,cAAc,CAACvB,KAAK,CAAC,GAAG;QAC3B2E,cAAc,EAAE0E,YAAY,CAAC1E,cAAc;QAC3C7G,YAAY,EAAEuL,YAAY,CAACvL,YAAY;QACvCzB,SAAS,EAAEgN,YAAY,CAAChN,SAAS;QACjCC,YAAY,EAAE+M,YAAY,CAAC/M,YAAY;QACvCE,UAAU,EAAE6M,YAAY,CAAC7M,UAAU,GAAG,CAAC,GAAG6M,YAAY,CAAC7M,UAAU,CAAC,GAAG,EAAE;QACvEG,KAAK,EAAE0M,YAAY,CAAC1M,KAAK;QACzBE,OAAO,EAAEwM,YAAY,CAACxM,OAAO;QAC7BG,UAAU,EAAEqM,YAAY,CAACrM,UAAU,IAAI,CAAC;QACxCyB,KAAK,EAAE4K,YAAY,CAAC5K,KAAK;QACzBE,OAAO,EAAE0K,YAAY,CAAC1K,OAAO,IAAI,KAAK;QACtCE,SAAS,EAAEwK,YAAY,CAACxK,SAAS,IAAI,KAAK;QAC1CE,OAAO,EAAEsK,YAAY,CAACtK;OACvB;IACH;EACF;EAEA;EACAuC,eAAeA,CAAC0D,GAAQ;IACtB,IAAI,CAACrC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACpB,cAAc,GAAG,EAAE;IACxByD,GAAG,CAAC3F,KAAK,EAAE;EACb;EAEA;EACQiK,uBAAuBA,CAACC,YAAmC;IACjE,MAAM7B,aAAa,GAAG6B,YAAY,CAACxG,GAAG,CAACyG,WAAW,IAChD,IAAI,CAACxH,kBAAkB,CAACkD,+BAA+B,CAAC;MACtDC,IAAI,EAAEqE;KACP,CAAC,CACH;IAEDxB,OAAO,CAACC,GAAG,CAACP,aAAa,CAAC3E,GAAG,CAACmF,GAAG,IAAIA,GAAG,CAACC,SAAS,EAAE,CAAC,CAAC,CACnDC,IAAI,CAACC,SAAS,IAAG;MAChB,MAAMC,YAAY,GAAGD,SAAS,CAAC7B,MAAM,CAACnB,GAAG,IAAIA,GAAG,EAAEC,UAAU,KAAK,CAAC,CAAC,CAACtK,MAAM;MAC1E,IAAI,CAAC6G,OAAO,CAAC0D,aAAa,CAAC,QAAQ+C,YAAY,QAAQ,CAAC;MACxD,IAAI,CAACnF,OAAO,EAAE;IAChB,CAAC,CAAC,CACDqF,KAAK,CAAC3D,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAI,CAAChD,OAAO,CAAC2D,YAAY,CAAC,aAAa,CAAC;IAC1C,CAAC,CAAC;EACN;;;uCAzkBWhE,8BAA8B,EAAAjI,EAAA,CAAAkQ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApQ,EAAA,CAAAkQ,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAtQ,EAAA,CAAAkQ,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAxQ,EAAA,CAAAkQ,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA1Q,EAAA,CAAAkQ,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA5Q,EAAA,CAAAkQ,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA9Q,EAAA,CAAAkQ,iBAAA,CAAAW,EAAA,CAAAE,kBAAA,GAAA/Q,EAAA,CAAAkQ,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAAjR,EAAA,CAAAkQ,iBAAA,CAAAgB,EAAA,CAAAC,MAAA,GAAAnR,EAAA,CAAAkQ,iBAAA,CAAAlQ,EAAA,CAAAoR,UAAA,GAAApR,EAAA,CAAAkQ,iBAAA,CAAAmB,EAAA,CAAAC,4BAAA;IAAA;EAAA;;;YAA9BrJ,8BAA8B;MAAAsJ,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzR,EAAA,CAAA0R,0BAAA,EAAA1R,EAAA,CAAA2R,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA1D,QAAA,WAAA2D,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC3CzChS,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAuB,SAAA,qBAAiC;UACnCvB,EAAA,CAAAG,YAAA,EAAiB;UAOTH,EAJR,CAAAC,cAAA,sBAAoC,aACd,aACD,aACyB,eACI;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,mBAA0H;UAA/GD,EAAA,CAAAiE,gBAAA,2BAAAiO,2EAAA/N,MAAA;YAAAnE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAqE,kBAAA,CAAA4N,GAAA,CAAAnJ,yBAAA,CAAAvE,YAAA,EAAAJ,MAAA,MAAA8N,GAAA,CAAAnJ,yBAAA,CAAAvE,YAAA,GAAAJ,MAAA;YAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;UAAA,EAAoD;UAAenE,EAAA,CAAAa,UAAA,2BAAAqR,2EAAA/N,MAAA;YAAAnE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAiB4Q,GAAA,CAAAvI,iBAAA,CAAAvF,MAAA,CAAyB;UAAA,EAAC;UACvHnE,EAAA,CAAAyC,UAAA,KAAA2P,oDAAA,uBAAiE;UAIrEpS,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,iBACoD;UAAlDD,EAAA,CAAAiE,gBAAA,2BAAAoO,wEAAAlO,MAAA;YAAAnE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAqE,kBAAA,CAAA4N,GAAA,CAAAnJ,yBAAA,CAAAhG,SAAA,EAAAqB,MAAA,MAAA8N,GAAA,CAAAnJ,yBAAA,CAAAhG,SAAA,GAAAqB,MAAA;YAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;UAAA,EAAiD;UACrDnE,EAFE,CAAAG,YAAA,EACoD,EAChD;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACM;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,iBACuD;UAArDD,EAAA,CAAAiE,gBAAA,2BAAAqO,wEAAAnO,MAAA;YAAAnE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAqE,kBAAA,CAAA4N,GAAA,CAAAnJ,yBAAA,CAAA/F,YAAA,EAAAoB,MAAA,MAAA8N,GAAA,CAAAnJ,yBAAA,CAAA/F,YAAA,GAAAoB,MAAA;YAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;UAAA,EAAoD;UAE1DnE,EAHI,CAAAG,YAAA,EACuD,EACnD,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBAAqF;UAA1ED,EAAA,CAAAiE,gBAAA,2BAAAsO,4EAAApO,MAAA;YAAAnE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAqE,kBAAA,CAAA4N,GAAA,CAAAnJ,yBAAA,CAAA7F,UAAA,EAAAkB,MAAA,MAAA8N,GAAA,CAAAnJ,yBAAA,CAAA7F,UAAA,GAAAkB,MAAA;YAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;UAAA,EAAkD;UAC3DnE,EAAA,CAAAyC,UAAA,KAAA+P,oDAAA,uBAA+D;UAInExS,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,oBAAyE;UAA9DD,EAAA,CAAAiE,gBAAA,2BAAAwO,4EAAAtO,MAAA;YAAAnE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAqE,kBAAA,CAAA4N,GAAA,CAAAnJ,yBAAA,CAAAxF,OAAA,EAAAa,MAAA,MAAA8N,GAAA,CAAAnJ,yBAAA,CAAAxF,OAAA,GAAAa,MAAA;YAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;UAAA,EAA+C;UACxDnE,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAE7BF,EAF6B,CAAAG,YAAA,EAAY,EAC3B,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,qBAA0F;UAA/ED,EAAA,CAAAiE,gBAAA,2BAAAyO,4EAAAvO,MAAA;YAAAnE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAqE,kBAAA,CAAA4N,GAAA,CAAAnJ,yBAAA,CAAA1D,OAAA,EAAAjB,MAAA,MAAA8N,GAAA,CAAAnJ,yBAAA,CAAA1D,OAAA,GAAAjB,MAAA;YAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;UAAA,EAA+C;UACxDnE,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAGlCF,EAHkC,CAAAG,YAAA,EAAY,EAC9B,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACG;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,qBAA4F;UAAjFD,EAAA,CAAAiE,gBAAA,2BAAA0O,4EAAAxO,MAAA;YAAAnE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAqE,kBAAA,CAAA4N,GAAA,CAAAnJ,yBAAA,CAAAxD,SAAA,EAAAnB,MAAA,MAAA8N,GAAA,CAAAnJ,yBAAA,CAAAxD,SAAA,GAAAnB,MAAA;YAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;UAAA,EAAiD;UAC1DnE,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAEhCF,EAFgC,CAAAG,YAAA,EAAY,EAC9B,EACR;UACNH,EAAA,CAAAuB,SAAA,cAA8C;UAChDvB,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACfD,EAAA,CAAAuB,SAAA,eAA4B;UAE1BvB,EADF,CAAAC,cAAA,eAAmD,kBACc;UAAxBD,EAAA,CAAAa,UAAA,mBAAA+R,iEAAA;YAAA5S,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAS4Q,GAAA,CAAApI,WAAA,EAAa;UAAA,EAAC;UAAC7J,EAAA,CAAAuB,SAAA,aAAgC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1GH,EAAA,CAAAC,cAAA,kBAAsD;UAApBD,EAAA,CAAAa,UAAA,mBAAAgS,iEAAA;YAAA7S,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAS4Q,GAAA,CAAArI,OAAA,EAAS;UAAA,EAAC;UAAC5J,EAAA,CAAAuB,SAAA,aAAkC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnGH,EAAA,CAAAC,cAAA,kBAA2E;UAAtCD,EAAA,CAAAa,UAAA,mBAAAiS,iEAAA;YAAA9S,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAS4Q,GAAA,CAAAtE,yBAAA,EAA2B;UAAA,EAAC;UAAC3N,EAAA,CAAAuB,SAAA,aACnC;UAAAvB,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGrDH,EAFA,CAAAyC,UAAA,KAAAsQ,iDAAA,qBACmC,KAAAC,iDAAA,qBACyC;UAKpFhT,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;UAUDH,EAPd,CAAAC,cAAA,uBAAoC,eACT,eACO,iBACmE,aACtF,cAC4D,cACiB,uBACG;UAA1CD,EAAA,CAAAa,UAAA,2BAAAoS,8EAAA9O,MAAA;YAAAnE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAiB4Q,GAAA,CAAA3C,eAAA,CAAAnL,MAAA,CAAuB;UAAA,EAAC;UAChFnE,EAAA,CAAAG,YAAA,EAAc;UACdH,EAAA,CAAAC,cAAA,iBAA+B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UACnCF,EADmC,CAAAG,YAAA,EAAQ,EACtC;UACLH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAyC,UAAA,MAAAyQ,8CAAA,mBAAuE;UAuB7ElT,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,2BAC2B;UADqBD,EAAA,CAAAiE,gBAAA,wBAAAkP,+EAAAhP,MAAA;YAAAnE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAqE,kBAAA,CAAA4N,GAAA,CAAApF,SAAA,EAAA1I,MAAA,MAAA8N,GAAA,CAAApF,SAAA,GAAA1I,MAAA;YAAA,OAAAnE,EAAA,CAAAqB,WAAA,CAAA8C,MAAA;UAAA,EAAoB;UAClEnE,EAAA,CAAAa,UAAA,wBAAAsS,+EAAA;YAAAnT,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAc4Q,GAAA,CAAArI,OAAA,EAAS;UAAA,EAAC;UAIhC5J,EAHM,CAAAG,YAAA,EAAiB,EACb,EACO,EACP;UAkFVH,EA/EA,CAAAyC,UAAA,MAAA2Q,uDAAA,kCAAApT,EAAA,CAAAqT,sBAAA,CAAkD,MAAAC,uDAAA,iCAAAtT,EAAA,CAAAqT,sBAAA,CA+ES;;;UA9MtCrT,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAAgG,gBAAA,YAAAiM,GAAA,CAAAnJ,yBAAA,CAAAvE,YAAA,CAAoD;UACjCvE,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAA6R,GAAA,CAAAhM,aAAA,CAAgB;UAQ5CjG,EAAA,CAAAO,SAAA,GAAiD;UAAjDP,EAAA,CAAAgG,gBAAA,YAAAiM,GAAA,CAAAnJ,yBAAA,CAAAhG,SAAA,CAAiD;UAKjD9C,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAAgG,gBAAA,YAAAiM,GAAA,CAAAnJ,yBAAA,CAAA/F,YAAA,CAAoD;UAM3C/C,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAgG,gBAAA,YAAAiM,GAAA,CAAAnJ,yBAAA,CAAA7F,UAAA,CAAkD;UAC/BjD,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,YAAA6R,GAAA,CAAA/L,SAAA,CAAY;UAO/BlG,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAgG,gBAAA,YAAAiM,GAAA,CAAAnJ,yBAAA,CAAAxF,OAAA,CAA+C;UAC7CtD,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACZJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UACXJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UAKbJ,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAgG,gBAAA,YAAAiM,GAAA,CAAAnJ,yBAAA,CAAA1D,OAAA,CAA+C;UAC7CpF,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UAOjBJ,EAAA,CAAAO,SAAA,GAAiD;UAAjDP,EAAA,CAAAgG,gBAAA,YAAAiM,GAAA,CAAAnJ,yBAAA,CAAAxD,SAAA,CAAiD;UAC/CtF,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UAazBJ,EAAA,CAAAO,SAAA,IAA8B;UAA9BP,EAAA,CAAAI,UAAA,SAAA6R,GAAA,CAAAzQ,aAAA,CAAAC,MAAA,KAA8B;UAC2BzB,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,SAAA6R,GAAA,CAAAsB,QAAA,CAAc;UAevDvT,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAI,UAAA,YAAA6R,GAAA,CAAA9I,aAAA,CAAyB;UAiBrBnJ,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAA6R,GAAA,CAAAjJ,eAAA,CAAoB;UAwB/BhJ,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAA6R,GAAA,CAAAnF,YAAA,CAA+B;UAAC9M,EAAA,CAAAgG,gBAAA,SAAAiM,GAAA,CAAApF,SAAA,CAAoB;UAAC7M,EAAA,CAAAI,UAAA,aAAA6R,GAAA,CAAAtF,QAAA,CAAqB;;;qBDvG5F3N,YAAY,EAAAuR,EAAA,CAAAiD,eAAA,EAAAjD,EAAA,CAAAkD,mBAAA,EAAAlD,EAAA,CAAAmD,qBAAA,EAAAnD,EAAA,CAAAoD,qBAAA,EACZpU,mBAAmB,EACnBL,aAAa,EAAAqR,EAAA,CAAAqD,gBAAA,EACbpU,WAAW,EAAAqU,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,kBAAA,EAAAJ,GAAA,CAAAK,YAAA,EAAAL,GAAA,CAAAM,OAAA,EACX/U,cAAc,EAAAmR,EAAA,CAAA6D,iBAAA,EAAA7D,EAAA,CAAA8D,iBAAA,EACdlV,cAAc,EACdO,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVX,gBAAgB,EAAAsR,EAAA,CAAA+D,mBAAA,EAChBzU,kBAAkB,EAClBC,oBAAoB;MAAAyU,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}