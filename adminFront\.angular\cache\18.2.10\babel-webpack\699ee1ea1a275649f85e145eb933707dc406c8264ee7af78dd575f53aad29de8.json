{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/components/space-template-selector/space-template-selector.service\";\nimport * as i10 from \"@angular/forms\";\nconst _c0 = () => [];\nfunction RequirementManagementComponent_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_69_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(100);\n      return i0.ɵɵresetView(ctx_r4.add(dialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_97_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_97_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const data_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(100);\n      return i0.ɵɵresetView(ctx_r4.onEdit(data_r8, dialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_97_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_97_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const data_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onDelete(data_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 39)(1, \"td\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 41);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 40);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 41);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 41);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 41);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 41);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 41);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 41);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 41);\n    i0.ɵɵtemplate(22, RequirementManagementComponent_tr_97_button_22_Template, 3, 0, \"button\", 42)(23, RequirementManagementComponent_tr_97_button_23_Template, 3, 0, \"button\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getHouseType(data_r8.CHouseType || i0.ɵɵpureFunction0(15, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 11, data_r8.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsShowText(data_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsSimpleText(data_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 13, data_r8.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_99_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_99_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_99_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const b_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", b_r11.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", b_r11.CBuildCaseName, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_99_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r12.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r12.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_99_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r13.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r13.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_99_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 48)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_99_span_2_Template, 2, 0, \"span\", 49)(3, RequirementManagementComponent_ng_template_99_span_3_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 50)(5, \"div\", 4)(6, \"div\", 51)(7, \"div\", 4)(8, \"app-form-group\", 52)(9, \"nb-select\", 53);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_99_Template_nb_select_selectedChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CBuildCaseID, $event) || (ctx_r4.saveRequirement.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(10, RequirementManagementComponent_ng_template_99_nb_option_10_Template, 2, 2, \"nb-option\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"app-form-group\", 52)(12, \"input\", 55);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_99_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CLocation, $event) || (ctx_r4.saveRequirement.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"app-form-group\", 52)(14, \"input\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_99_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRequirement, $event) || (ctx_r4.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"app-form-group\", 52)(16, \"input\", 57);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_99_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CSort, $event) || (ctx_r4.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 52)(18, \"nb-select\", 58);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_99_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CHouseType, $event) || (ctx_r4.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_99_nb_option_19_Template, 2, 2, \"nb-option\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 52)(21, \"nb-select\", 59);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_99_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CStatus, $event) || (ctx_r4.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, RequirementManagementComponent_ng_template_99_nb_option_22_Template, 2, 2, \"nb-option\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"app-form-group\", 52)(24, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_99_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnitPrice, $event) || (ctx_r4.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"app-form-group\", 52)(26, \"input\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_99_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnit, $event) || (ctx_r4.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 52)(28, \"nb-checkbox\", 62);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_99_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsShow, $event) || (ctx_r4.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u986F\\u793A\\u5728\\u9810\\u7D04\\u9700\\u6C42\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 52)(31, \"nb-checkbox\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_99_Template_nb_checkbox_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsSimple, $event) || (ctx_r4.saveRequirement.CIsSimple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(32, \" \\u8A2D\\u5B9A\\u70BA\\u7C21\\u6613\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"app-form-group\", 52)(34, \"textarea\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_99_Template_textarea_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRemark, $event) || (ctx_r4.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(35, \"nb-card-footer\")(36, \"div\", 4)(37, \"div\", 65)(38, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_99_Template_button_click_38_listener() {\n      const ref_r14 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.save(ref_r14));\n    });\n    i0.ɵɵtext(39, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_99_Template_button_click_40_listener() {\n      const ref_r14 = i0.ɵɵrestoreView(_r10).dialogRef;\n      return i0.ɵɵresetView(ref_r14.close());\n    });\n    i0.ɵɵtext(41, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\\u540D\\u7A31\")(\"labelFor\", \"CBuildCaseID\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.buildCaseList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5340\\u57DF\")(\"labelFor\", \"CLocation\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CLocation);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u9810\\u7D04\\u9700\\u6C42\")(\"labelFor\", \"CIsShow\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u7C21\\u6613\\u5BA2\\u8B8A\")(\"labelFor\", \"CIsSimple\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsSimple);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRemark);\n  }\n}\nexport let RequirementManagementComponent = /*#__PURE__*/(() => {\n  class RequirementManagementComponent extends BaseComponent {\n    constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref, spaceTemplateSelectorService) {\n      super(_allow);\n      this._allow = _allow;\n      this.enumHelper = enumHelper;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this.buildCaseService = buildCaseService;\n      this.requirementService = requirementService;\n      this.pettern = pettern;\n      this.router = router;\n      this.destroyref = destroyref;\n      this.spaceTemplateSelectorService = spaceTemplateSelectorService;\n      // request\n      this.getListRequirementRequest = {};\n      this.getRequirementRequest = {};\n      // response\n      this.buildCaseList = [];\n      this.requirementList = [];\n      this.saveRequirement = {\n        CHouseType: []\n      };\n      this.statusOptions = [{\n        value: 0,\n        label: '停用'\n      }, {\n        value: 1,\n        label: '啟用'\n      }];\n      this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n      this.isNew = false;\n      this.currentBuildCase = 0;\n      this.initializeSearchForm();\n      this.getBuildCaseList();\n    }\n    ngOnInit() {}\n    // 初始化搜尋表單\n    initializeSearchForm() {\n      this.getListRequirementRequest.CStatus = -1;\n      this.getListRequirementRequest.CIsShow = null;\n      this.getListRequirementRequest.CIsSimple = null;\n      this.getListRequirementRequest.CRequirement = '';\n      this.getListRequirementRequest.CLocation = '';\n      // 預設全選所有房屋類型\n      this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n    }\n    // 重置搜尋\n    resetSearch() {\n      this.initializeSearchForm();\n      // 重置後如果有建案資料，重新設定預設選擇第一個建案\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\n        setTimeout(() => {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n          this.getList();\n        }, 0);\n      } else {\n        this.getList();\n      }\n    }\n    getHouseType(hTypes) {\n      if (!hTypes) {\n        return '';\n      }\n      if (!Array.isArray(hTypes)) {\n        hTypes = [hTypes];\n      }\n      let labels = [];\n      hTypes.forEach(htype => {\n        let findH = this.houseType.find(x => x.value == htype);\n        if (findH) {\n          labels.push(findH.label);\n        }\n      });\n      return labels.join(', ');\n    }\n    validation() {\n      this.valid.clear();\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n      this.valid.required('[需求]', this.saveRequirement.CRequirement);\n      this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n      this.valid.required('[排序]', this.saveRequirement.CSort);\n      this.valid.required('[狀態]', this.saveRequirement.CStatus);\n      this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n      this.valid.required('[單位]', this.saveRequirement.CUnit);\n      // 群組名稱長度驗證\n      if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {\n        this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n      }\n      // 備註說明長度驗證\n      if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n        this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n      }\n    }\n    add(dialog) {\n      this.isNew = true;\n      this.saveRequirement = {\n        CHouseType: [],\n        CIsShow: false,\n        CIsSimple: false\n      };\n      this.saveRequirement.CStatus = 1;\n      this.saveRequirement.CUnitPrice = 0;\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n      this.dialogService.open(dialog);\n    }\n    onEdit(data, dialog) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n        _this.isNew = false;\n        try {\n          yield _this.getData();\n          _this.dialogService.open(dialog);\n        } catch (error) {\n          console.log(\"Failed to get function data\", error);\n        }\n      })();\n    }\n    save(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this.requirementService.apiRequirementSaveDataPost$Json({\n        body: this.saveRequirement\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getList();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n      ref.close();\n    }\n    onDelete(data) {\n      this.saveRequirement.CRequirementID = data.CRequirementID;\n      this.isNew = false;\n      if (window.confirm('是否確定刪除?')) {\n        this.remove();\n      } else {\n        return;\n      }\n    }\n    remove() {\n      this.requirementService.apiRequirementDeleteDataPost$Json({\n        body: {\n          CRequirementID: this.saveRequirement.CRequirementID\n        }\n      }).subscribe(res => {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      });\n    }\n    getBuildCaseList() {\n      this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n        body: {}\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n        this.buildCaseList = res.Entries;\n        // 如果有建案時才查詢\n        if (this.buildCaseList.length > 0) {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n          this.getList();\n        }\n      });\n    }\n    getList() {\n      this.getListRequirementRequest.PageSize = this.pageSize;\n      this.getListRequirementRequest.PageIndex = this.pageIndex;\n      this.requirementList = [];\n      this.totalRecords = 0;\n      // 建案頁面的邏輯\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n      this.requirementService.apiRequirementGetListPost$Json({\n        body: this.getListRequirementRequest\n      }).pipe().subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries) {\n            this.requirementList = res.Entries;\n            this.totalRecords = res.TotalItems;\n          }\n        }\n      });\n    }\n    getData() {\n      this.requirementService.apiRequirementGetDataPost$Json({\n        body: this.getRequirementRequest\n      }).pipe().subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries) {\n            this.saveRequirement = {\n              CHouseType: [],\n              CIsShow: false,\n              CIsSimple: false\n            };\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n            this.saveRequirement.CLocation = res.Entries.CLocation;\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n            this.saveRequirement.CRemark = res.Entries.CRemark;\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n            this.saveRequirement.CSort = res.Entries.CSort;\n            this.saveRequirement.CStatus = res.Entries.CStatus;\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\n            this.saveRequirement.CUnit = res.Entries.CUnit;\n            this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n            this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\n          }\n        }\n      });\n    }\n    onHouseTypeChange(value, checked) {\n      console.log(checked);\n      if (checked) {\n        if (!this.saveRequirement.CHouseType?.includes(value)) {\n          this.saveRequirement.CHouseType?.push(value);\n        }\n        console.log(this.saveRequirement.CHouseType);\n      } else {\n        this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n      }\n    }\n    getCIsShowText(data) {\n      return data.CIsShow ? '是' : '否';\n    }\n    getCIsSimpleText(data) {\n      return data.CIsSimple ? '是' : '否';\n    }\n    // 空間模板相關方法\n    openSpaceTemplateSelector() {\n      if (!this.getListRequirementRequest.CBuildCaseID) {\n        this.message.showErrorMSG('請先選擇建案');\n        return;\n      }\n      // 使用新的服務開啟對話框\n      this.spaceTemplateSelectorService.openSelector(this.getListRequirementRequest.CBuildCaseID?.toString() || '').subscribe(result => {\n        if (result) {\n          this.onSpaceTemplateApplied(result);\n        }\n      });\n    }\n    onSpaceTemplateApplied(config) {\n      console.log('套用空間模板配置:', config);\n      // 這裡可以實作將模板配置轉換為需求項目並批次新增的邏輯\n      // 例如：\n      const newRequirements = config.selectedItems.map(item => ({\n        CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n        CRequirement: `${config.spaceName}-${item.CTemplateName}`,\n        CLocation: config.spaceName,\n        CUnitPrice: 0,\n        // API 沒有價格資訊，預設為 0\n        CUnit: '個',\n        // 預設單位\n        CStatus: 1,\n        CIsShow: true,\n        CIsSimple: false,\n        CHouseType: this.houseType.map(type => type.value),\n        // 預設所有房型\n        CRemark: `由空間模板批次新增 - ID: ${item.CTemplateId}`\n      }));\n      // TODO: 調用批次新增 API\n      // this.batchCreateRequirements(newRequirements);\n      this.message.showSucessMSG(`成功套用 ${config.selectedItems.length} 個${config.spaceName}模板項目`);\n      this.getList(); // 重新載入資料\n    }\n    // 未來可以擴展的批次新增方法\n    batchCreateRequirements(requirements) {\n      // 實作批次新增需求的 API 調用\n      // 可以參考現有的 save 方法進行批次處理\n    }\n    static {\n      this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef), i0.ɵɵdirectiveInject(i9.SpaceTemplateSelectorService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RequirementManagementComponent,\n        selectors: [[\"app-requirement-management\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 101,\n        vars: 23,\n        consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\", \"pb-0\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u5340\\u57DF\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [3, \"value\"], [\"for\", \"isShow\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5168\\u90E8\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"isSimple\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"btn\", \"btn-warning\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-layer-group\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"col-12\", \"mt-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CBuildCaseID\", \"name\", \"CBuildCaseID\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CLocation\", \"name\", \"CLocation\", \"placeholder\", \"\\u5340\\u57DF\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CSort\", \"name\", \"CSort\", \"placeholder\", \"\\u6392\\u5E8F\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CUnit\", \"name\", \"CUnit\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsShow\", \"name\", \"CIsShow\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsSimple\", \"name\", \"CIsSimple\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"langg\", \"\", 3, \"value\"]],\n        template: function RequirementManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"label\", 6);\n            i0.ɵɵtext(8, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"nb-select\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CBuildCaseID, $event) || (ctx.getListRequirementRequest.CBuildCaseID = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(10, RequirementManagementComponent_nb_option_10_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 5)(12, \"label\", 9);\n            i0.ɵɵtext(13, \"\\u5340\\u57DF\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"input\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CLocation, $event) || (ctx.getListRequirementRequest.CLocation = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 5)(16, \"label\", 11);\n            i0.ɵɵtext(17, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_18_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"div\", 4)(20, \"div\", 5)(21, \"label\", 13);\n            i0.ɵɵtext(22, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"nb-select\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_23_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(24, RequirementManagementComponent_nb_option_24_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"div\", 5)(26, \"label\", 15);\n            i0.ɵɵtext(27, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"nb-select\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_28_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(29, \"nb-option\", 16);\n            i0.ɵɵtext(30, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"nb-option\", 16);\n            i0.ɵɵtext(32, \"\\u555F\\u7528\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"nb-option\", 16);\n            i0.ɵɵtext(34, \"\\u505C\\u7528\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(35, \"div\", 5)(36, \"label\", 17);\n            i0.ɵɵtext(37, \"\\u9810\\u7D04\\u9700\\u6C42\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"nb-select\", 18);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_38_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsShow, $event) || (ctx.getListRequirementRequest.CIsShow = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(39, \"nb-option\", 16);\n            i0.ɵɵtext(40, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"nb-option\", 16);\n            i0.ɵɵtext(42, \"\\u662F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"nb-option\", 16);\n            i0.ɵɵtext(44, \"\\u5426\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(45, \"div\", 4)(46, \"div\", 5)(47, \"label\", 19);\n            i0.ɵɵtext(48, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"nb-select\", 18);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_49_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsSimple, $event) || (ctx.getListRequirementRequest.CIsSimple = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(50, \"nb-option\", 16);\n            i0.ɵɵtext(51, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"nb-option\", 16);\n            i0.ɵɵtext(53, \"\\u662F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"nb-option\", 16);\n            i0.ɵɵtext(55, \"\\u5426\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelement(56, \"div\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"div\", 4);\n            i0.ɵɵelement(58, \"div\", 20);\n            i0.ɵɵelementStart(59, \"div\", 21)(60, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_60_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.resetSearch());\n            });\n            i0.ɵɵelement(61, \"i\", 23);\n            i0.ɵɵtext(62, \"\\u91CD\\u7F6E\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_63_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getList());\n            });\n            i0.ɵɵelement(64, \"i\", 25);\n            i0.ɵɵtext(65, \"\\u67E5\\u8A62\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_66_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.openSpaceTemplateSelector());\n            });\n            i0.ɵɵelement(67, \"i\", 27);\n            i0.ɵɵtext(68, \"\\u6A21\\u677F\\u65B0\\u589E\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(69, RequirementManagementComponent_button_69_Template, 3, 0, \"button\", 28);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(70, \"nb-card-body\", 2)(71, \"div\", 29)(72, \"div\", 30)(73, \"table\", 31)(74, \"thead\")(75, \"tr\", 32)(76, \"th\", 33);\n            i0.ɵɵtext(77, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"th\", 34);\n            i0.ɵɵtext(79, \"\\u5340\\u57DF\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"th\", 33);\n            i0.ɵɵtext(81, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(82, \"th\", 34);\n            i0.ɵɵtext(83, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(84, \"th\", 34);\n            i0.ɵɵtext(85, \"\\u6392\\u5E8F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"th\", 34);\n            i0.ɵɵtext(87, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(88, \"th\", 34);\n            i0.ɵɵtext(89, \"\\u9810\\u7D04\\u9700\\u6C42\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(90, \"th\", 34);\n            i0.ɵɵtext(91, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(92, \"th\", 34);\n            i0.ɵɵtext(93, \"\\u55AE\\u50F9\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(94, \"th\", 34);\n            i0.ɵɵtext(95, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(96, \"tbody\");\n            i0.ɵɵtemplate(97, RequirementManagementComponent_tr_97_Template, 24, 16, \"tr\", 35);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(98, \"ngx-pagination\", 36);\n            i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_98_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_98_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getList());\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(99, RequirementManagementComponent_ng_template_99_Template, 42, 49, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CBuildCaseID);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CLocation);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", -1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsShow);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", false);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsSimple);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", false);\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(28);\n            i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          }\n        },\n        dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i10.DefaultValueAccessor, i10.NumberValueAccessor, i10.NgControlStatus, i10.MaxLengthValidator, i10.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent, NumberWithCommasPipe],\n        styles: [\".table-active[_ngcontent-%COMP%]{background-color:#e3f2fd!important;border-left:3px solid #2196f3}.page-description-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa,#e9ecef);padding:1.25rem;border-radius:10px;border:1px solid #dee2e6;box-shadow:0 2px 4px #0000000d;margin-bottom:1rem}.page-description-card[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-weight:600;color:#007bff}.page-description-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem;line-height:1.5;margin-bottom:.75rem}.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background-color:#fff;color:#6c757d;border:1px solid #dee2e6;font-size:.8rem;padding:.4rem .8rem;font-weight:500}.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#007bff}.template-creation-controls[_ngcontent-%COMP%]{background:#f8f9fa;padding:1rem;border-radius:8px;margin-bottom:1rem;border:1px solid #e9ecef}.template-creation-controls[_ngcontent-%COMP%]   .template-action-buttons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.template-creation-controls[_ngcontent-%COMP%]   .template-status-info[_ngcontent-%COMP%]{margin-left:1rem}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff,#0056b3);border:none;font-weight:500;transition:all .2s ease}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 4px 12px #007bff4d}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:disabled{background:#6c757d;cursor:not-allowed}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background:#fff3;color:#fff;font-size:.75rem}.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]{border-color:#6c757d;color:#6c757d;transition:all .2s ease}.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#6c757d;border-color:#6c757d;color:#fff}.template-creation-controls[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{font-size:.875rem;font-style:italic}.template-creation-controls[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]{transform:scale(1.1);margin:0}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:all .2s ease}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-active[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_selectRow .3s ease-out}@keyframes _ngcontent-%COMP%_selectRow{0%{background-color:transparent;transform:scale(1)}50%{transform:scale(1.01)}to{background-color:#e3f2fd;transform:scale(1)}}@media (max-width: 768px){.template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}.template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:row;gap:.25rem}.template-creation-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.875rem;padding:.375rem .75rem}}\"]\n      });\n    }\n  }\n  return RequirementManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}