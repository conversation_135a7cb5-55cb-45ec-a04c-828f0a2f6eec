{"ast": null, "code": "import { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i9 from \"../components/breadcrumb/breadcrumb.component\";\nfunction SpaceComponent_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const createModal_r4 = i0.ɵɵreference(49);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(createModal_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 28);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_45_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_tr_45_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const editModal_r7 = i0.ɵɵreference(51);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(editModal_r7, item_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_45_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_tr_45_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteSpace(item_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵelement(8, \"nb-badge\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 30);\n    i0.ɵɵtemplate(13, SpaceComponent_tr_45_button_13_Template, 3, 0, \"button\", 31)(14, SpaceComponent_tr_45_button_14_Template, 3, 0, \"button\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CSpaceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CLocation || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CDescription || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", item_r6.CIsEnable ? \"success\" : \"danger\")(\"text\", item_r6.CIsEnable ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 8, item_r6.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction SpaceComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-header\")(2, \"h5\");\n    i0.ɵɵtext(3, \"\\u65B0\\u589E\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\")(5, \"form\")(6, \"div\", 37)(7, \"label\", 38);\n    i0.ɵɵtext(8, \"\\u7A7A\\u9593\\u540D\\u7A31 \");\n    i0.ɵɵelementStart(9, \"span\", 39);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"input\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_48_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CSpaceName, $event) || (ctx_r2.spaceDetail.CSpaceName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 37)(13, \"label\", 41);\n    i0.ɵɵtext(14, \"\\u4F4D\\u7F6E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_48_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CLocation, $event) || (ctx_r2.spaceDetail.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 37)(17, \"label\", 43);\n    i0.ɵɵtext(18, \"\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"textarea\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_48_Template_textarea_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CDescription, $event) || (ctx_r2.spaceDetail.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(20, \"          \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 37)(22, \"nb-checkbox\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_48_Template_nb_checkbox_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CIsEnable, $event) || (ctx_r2.spaceDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(23, \" \\u555F\\u7528 \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\")(25, \"div\", 46)(26, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_48_Template_button_click_26_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r10));\n    });\n    i0.ɵɵtext(27, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_48_Template_button_click_28_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r10));\n    });\n    i0.ɵɵtext(29, \" \\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CSpaceName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CDescription);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CIsEnable);\n  }\n}\nfunction SpaceComponent_ng_template_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-header\")(2, \"h5\");\n    i0.ɵɵtext(3, \"\\u7DE8\\u8F2F\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\")(5, \"form\")(6, \"div\", 37)(7, \"label\", 49);\n    i0.ɵɵtext(8, \"\\u7A7A\\u9593\\u540D\\u7A31 \");\n    i0.ɵɵelementStart(9, \"span\", 39);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"input\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_50_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CSpaceName, $event) || (ctx_r2.spaceDetail.CSpaceName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 37)(13, \"label\", 51);\n    i0.ɵɵtext(14, \"\\u4F4D\\u7F6E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 52);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_50_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CLocation, $event) || (ctx_r2.spaceDetail.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 37)(17, \"label\", 53);\n    i0.ɵɵtext(18, \"\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"textarea\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_50_Template_textarea_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CDescription, $event) || (ctx_r2.spaceDetail.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(20, \"          \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 37)(22, \"nb-checkbox\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_50_Template_nb_checkbox_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CIsEnable, $event) || (ctx_r2.spaceDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(23, \" \\u555F\\u7528 \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\")(25, \"div\", 46)(26, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_50_Template_button_click_26_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵtext(27, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_50_Template_button_click_28_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵtext(29, \" \\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CSpaceName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CDescription);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CIsEnable);\n  }\n}\nexport class SpaceComponent extends BaseComponent {\n  constructor(allow, dialogService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.spaceList = [];\n    this.spaceDetail = {};\n    this.searchKeyword = '';\n    this.searchLocation = '';\n  }\n  ngOnInit() {\n    this.getSpaceList();\n  }\n  getSpaceList() {\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CSpaceName: this.searchKeyword || null,\n        CLocation: this.searchLocation || null\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    })).subscribe();\n  }\n  onSearch() {\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getSpaceList();\n  }\n  openCreateModal(ref) {\n    this.spaceDetail = {\n      CSpaceName: '',\n      CLocation: '',\n      CDescription: '',\n      CIsEnable: true\n    };\n    this.dialogService.open(ref);\n  }\n  openEditModal(ref, item) {\n    this.getSpaceById(item.CSpaceId, ref);\n  }\n  getSpaceById(spaceId, ref) {\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\n      body: {\n        CSpaceID: spaceId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceDetail = {\n          ...res.Entries\n        };\n        this.dialogService.open(ref);\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    });\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._spaceService.apiSpaceSaveSpacePost$Json({\n      body: this.spaceDetail\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n        this.getSpaceList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    })).subscribe();\n  }\n  deleteSpace(item) {\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\n        body: {\n          CSpaceID: item.CSpaceId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"刪除成功\");\n          this.getSpaceList();\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除失敗');\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[空間名稱]', this.spaceDetail.CSpaceName);\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CSpaceName, 50);\n    this.valid.isStringMaxLength('[描述]', this.spaceDetail.CDescription, 200);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  static {\n    this.ɵfac = function SpaceComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceComponent,\n      selectors: [[\"ngx-space\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 52,\n      vars: 7,\n      consts: [[\"createModal\", \"\"], [\"editModal\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"spaceName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"spaceName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u7A7A\\u9593\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"location\", 1, \"label\", \"col-3\"], [\"type\", \"text\", \"id\", \"location\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u4F4D\\u7F6E\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [3, \"status\", \"text\"], [1, \"text-center\", \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"me-1\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"me-1\"], [1, \"form-group\"], [\"for\", \"spaceName\"], [1, \"text-danger\"], [\"type\", \"text\", \"id\", \"spaceName\", \"name\", \"spaceName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u7A7A\\u9593\\u540D\\u7A31\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"location\"], [\"type\", \"text\", \"id\", \"location\", \"name\", \"location\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u4F4D\\u7F6E\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"description\"], [\"id\", \"description\", \"name\", \"description\", \"rows\", \"3\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u7A7A\\u9593\\u63CF\\u8FF0\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"isEnable\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\"], [\"nbButton\", \"\", \"status\", \"basic\", 1, \"mr-2\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\"], [\"for\", \"spaceNameEdit\"], [\"type\", \"text\", \"id\", \"spaceNameEdit\", \"name\", \"spaceName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u7A7A\\u9593\\u540D\\u7A31\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"locationEdit\"], [\"type\", \"text\", \"id\", \"locationEdit\", \"name\", \"location\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u4F4D\\u7F6E\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"descriptionEdit\"], [\"id\", \"descriptionEdit\", \"name\", \"description\", \"rows\", \"3\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u7A7A\\u9593\\u63CF\\u8FF0\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"]],\n      template: function SpaceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 3);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u7A7A\\u9593\\u7684\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u7A7A\\u9593\\u540D\\u7A31\\u3001\\u4F4D\\u7F6E\\u3001\\u63CF\\u8FF0\\u53CA\\u72C0\\u614B\\u7B49\\u8A2D\\u5B9A\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 5)(8, \"div\", 6)(9, \"label\", 7);\n          i0.ɵɵtext(10, \"\\u7A7A\\u9593\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-form-field\", 8)(12, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_input_ngModelChange_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpaceComponent_Template_input_keyup_enter_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 5)(14, \"div\", 6)(15, \"label\", 10);\n          i0.ɵɵtext(16, \"\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-form-field\", 8)(18, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_input_ngModelChange_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchLocation, $event) || (ctx.searchLocation = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpaceComponent_Template_input_keyup_enter_18_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(19, \"div\", 5);\n          i0.ɵɵelementStart(20, \"div\", 12)(21, \"div\", 13)(22, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function SpaceComponent_Template_button_click_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelement(23, \"i\", 15);\n          i0.ɵɵtext(24, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 12)(26, \"div\", 16);\n          i0.ɵɵtemplate(27, SpaceComponent_button_27_Template, 3, 0, \"button\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 18)(29, \"table\", 19)(30, \"thead\")(31, \"tr\", 20)(32, \"th\", 21);\n          i0.ɵɵtext(33, \"\\u7A7A\\u9593\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"th\", 21);\n          i0.ɵɵtext(35, \"\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"th\", 22);\n          i0.ɵɵtext(37, \"\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"th\", 23);\n          i0.ɵɵtext(39, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"th\", 21);\n          i0.ɵɵtext(41, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"th\", 21);\n          i0.ɵɵtext(43, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"tbody\");\n          i0.ɵɵtemplate(45, SpaceComponent_tr_45_Template, 15, 11, \"tr\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(46, \"nb-card-footer\", 25)(47, \"ngb-pagination\", 26);\n          i0.ɵɵtwoWayListener(\"pageChange\", function SpaceComponent_Template_ngb_pagination_pageChange_47_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function SpaceComponent_Template_ngb_pagination_pageChange_47_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(48, SpaceComponent_ng_template_48_Template, 30, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(50, SpaceComponent_ng_template_50_Template, 30, 4, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchLocation);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.spaceList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, i6.DatePipe, SharedModule, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.NgModel, i7.NgForm, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbButtonComponent, i2.NbFormFieldComponent, i8.NgbPagination, i2.NbBadgeComponent, i9.BreadcrumbComponent],\n      styles: [\".btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: var(--color-bg-2);\\n  font-weight: 600;\\n  border-bottom: 2px solid var(--color-bg-3);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: var(--color-bg-1);\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0;\\n  border-bottom-left-radius: 0;\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--color-fg-heading);\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: var(--color-danger) !important;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.mt-3[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInNwYWNlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNFO0VBQ0UscUJBQUE7QUFBSjtBQUVJO0VBQ0UsZUFBQTtBQUFOOztBQU1FO0VBQ0UsbUNBQUE7RUFDQSxnQkFBQTtFQUNBLDBDQUFBO0FBSEo7QUFPSTtFQUNFLG1DQUFBO0FBTE47O0FBWUk7RUFDRSx5QkFBQTtFQUNBLDRCQUFBO0FBVE47O0FBZUU7RUFDRSw4QkFBQTtBQVpKOztBQWdCQTtFQUNFLHFDQUFBO0FBYkY7O0FBZ0JBO0VBQ0Usb0JBQUE7QUFiRjs7QUFnQkE7RUFDRSxtQkFBQTtBQWJGOztBQWdCQTtFQUNFLGdCQUFBO0FBYkYiLCJmaWxlIjoic3BhY2UuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuYnRuLWdyb3VwIHtcclxuICBidXR0b24ge1xyXG4gICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtO1xyXG5cclxuICAgICY6bGFzdC1jaGlsZCB7XHJcbiAgICAgIG1hcmdpbi1yaWdodDogMDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi50YWJsZSB7XHJcbiAgdGgge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tY29sb3ItYmctMik7XHJcbiAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkIHZhcigtLWNvbG9yLWJnLTMpO1xyXG4gIH1cclxuXHJcbiAgdGJvZHkgdHIge1xyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWNvbG9yLWJnLTEpO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLmlucHV0LWdyb3VwIHtcclxuICAuaW5wdXQtZ3JvdXAtYXBwZW5kIHtcclxuICAgIC5idG4ge1xyXG4gICAgICBib3JkZXItdG9wLWxlZnQtcmFkaXVzOiAwO1xyXG4gICAgICBib3JkZXItYm90dG9tLWxlZnQtcmFkaXVzOiAwO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxubmItY2FyZC1oZWFkZXIge1xyXG4gIGg1IHtcclxuICAgIGNvbG9yOiB2YXIoLS1jb2xvci1mZy1oZWFkaW5nKTtcclxuICB9XHJcbn1cclxuXHJcbi50ZXh0LWRhbmdlciB7XHJcbiAgY29sb3I6IHZhcigtLWNvbG9yLWRhbmdlcikgIWltcG9ydGFudDtcclxufVxyXG5cclxuLm1yLTIge1xyXG4gIG1hcmdpbi1yaWdodDogMC41cmVtO1xyXG59XHJcblxyXG4ubWItMyB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxufVxyXG5cclxuLm10LTMge1xyXG4gIG1hcmdpbi10b3A6IDFyZW07XHJcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc3BhY2Uvc3BhY2UuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0U7RUFDRSxxQkFBQTtBQUFKO0FBRUk7RUFDRSxlQUFBO0FBQU47O0FBTUU7RUFDRSxtQ0FBQTtFQUNBLGdCQUFBO0VBQ0EsMENBQUE7QUFISjtBQU9JO0VBQ0UsbUNBQUE7QUFMTjs7QUFZSTtFQUNFLHlCQUFBO0VBQ0EsNEJBQUE7QUFUTjs7QUFlRTtFQUNFLDhCQUFBO0FBWko7O0FBZ0JBO0VBQ0UscUNBQUE7QUFiRjs7QUFnQkE7RUFDRSxvQkFBQTtBQWJGOztBQWdCQTtFQUNFLG1CQUFBO0FBYkY7O0FBZ0JBO0VBQ0UsZ0JBQUE7QUFiRjtBQUNBLHdvREFBd29EIiwic291cmNlc0NvbnRlbnQiOlsiLmJ0bi1ncm91cCB7XHJcbiAgYnV0dG9uIHtcclxuICAgIG1hcmdpbi1yaWdodDogMC4yNXJlbTtcclxuXHJcbiAgICAmOmxhc3QtY2hpbGQge1xyXG4gICAgICBtYXJnaW4tcmlnaHQ6IDA7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4udGFibGUge1xyXG4gIHRoIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWNvbG9yLWJnLTIpO1xyXG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCB2YXIoLS1jb2xvci1iZy0zKTtcclxuICB9XHJcblxyXG4gIHRib2R5IHRyIHtcclxuICAgICY6aG92ZXIge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1jb2xvci1iZy0xKTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi5pbnB1dC1ncm91cCB7XHJcbiAgLmlucHV0LWdyb3VwLWFwcGVuZCB7XHJcbiAgICAuYnRuIHtcclxuICAgICAgYm9yZGVyLXRvcC1sZWZ0LXJhZGl1czogMDtcclxuICAgICAgYm9yZGVyLWJvdHRvbS1sZWZ0LXJhZGl1czogMDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbm5iLWNhcmQtaGVhZGVyIHtcclxuICBoNSB7XHJcbiAgICBjb2xvcjogdmFyKC0tY29sb3ItZmctaGVhZGluZyk7XHJcbiAgfVxyXG59XHJcblxyXG4udGV4dC1kYW5nZXIge1xyXG4gIGNvbG9yOiB2YXIoLS1jb2xvci1kYW5nZXIpICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5tci0yIHtcclxuICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcclxufVxyXG5cclxuLm1iLTMge1xyXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbn1cclxuXHJcbi5tdC0zIHtcclxuICBtYXJnaW4tdG9wOiAxcmVtO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "BaseComponent", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "SpaceComponent_button_27_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "createModal_r4", "ɵɵreference", "ɵɵresetView", "openCreateModal", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "SpaceComponent_tr_45_button_13_Template_button_click_0_listener", "_r5", "item_r6", "$implicit", "editModal_r7", "openEditModal", "SpaceComponent_tr_45_button_14_Template_button_click_0_listener", "_r8", "deleteSpace", "ɵɵtemplate", "SpaceComponent_tr_45_button_13_Template", "SpaceComponent_tr_45_button_14_Template", "ɵɵadvance", "ɵɵtextInterpolate", "CSpaceName", "CLocation", "CDescription", "ɵɵproperty", "CIsEnable", "ɵɵpipeBind2", "CCreateDt", "isUpdate", "isDelete", "ɵɵtwoWayListener", "SpaceComponent_ng_template_48_Template_input_ngModelChange_11_listener", "$event", "_r9", "ɵɵtwoWayBindingSet", "spaceDetail", "SpaceComponent_ng_template_48_Template_input_ngModelChange_15_listener", "SpaceComponent_ng_template_48_Template_textarea_ngModelChange_19_listener", "SpaceComponent_ng_template_48_Template_nb_checkbox_ngModelChange_22_listener", "SpaceComponent_ng_template_48_Template_button_click_26_listener", "ref_r10", "dialogRef", "onClose", "SpaceComponent_ng_template_48_Template_button_click_28_listener", "onSubmit", "ɵɵtwoWayProperty", "SpaceComponent_ng_template_50_Template_input_ngModelChange_11_listener", "_r11", "SpaceComponent_ng_template_50_Template_input_ngModelChange_15_listener", "SpaceComponent_ng_template_50_Template_textarea_ngModelChange_19_listener", "SpaceComponent_ng_template_50_Template_nb_checkbox_ngModelChange_22_listener", "SpaceComponent_ng_template_50_Template_button_click_26_listener", "ref_r12", "SpaceComponent_ng_template_50_Template_button_click_28_listener", "SpaceComponent", "constructor", "allow", "dialogService", "_spaceService", "message", "valid", "pageFirst", "pageSize", "pageIndex", "totalRecords", "spaceList", "searchKeyword", "searchLocation", "ngOnInit", "getSpaceList", "apiSpaceGetSpaceListPost$Json", "body", "PageIndex", "PageSize", "pipe", "res", "Entries", "StatusCode", "TotalItems", "showErrorMSG", "Message", "subscribe", "onSearch", "pageChanged", "newPage", "ref", "open", "item", "getSpaceById", "CSpaceId", "spaceId", "apiSpaceGetSpaceByIdPost$Json", "CSpaceID", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpaceSaveSpacePost$Json", "showSucessMSG", "close", "confirm", "apiSpaceDeleteSpacePost$Json", "clear", "required", "isStringMaxLength", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "SpaceService", "i4", "MessageService", "i5", "ValidationHelper", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SpaceComponent_Template", "rf", "ctx", "SpaceComponent_Template_input_ngModelChange_12_listener", "_r1", "SpaceComponent_Template_input_keyup_enter_12_listener", "SpaceComponent_Template_input_ngModelChange_18_listener", "SpaceComponent_Template_input_keyup_enter_18_listener", "SpaceComponent_Template_button_click_22_listener", "SpaceComponent_button_27_Template", "SpaceComponent_tr_45_Template", "SpaceComponent_Template_ngb_pagination_pageChange_47_listener", "SpaceComponent_ng_template_48_Template", "ɵɵtemplateRefExtractor", "SpaceComponent_ng_template_50_Template", "isCreate", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i7", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "NgModel", "NgForm", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbButtonComponent", "NbFormFieldComponent", "i8", "NgbPagination", "NbBadgeComponent", "i9", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\space\\space.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\space\\space.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { tap } from 'rxjs';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-space',\r\n  templateUrl: './space.component.html',\r\n  styleUrls: ['./space.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n  ],\r\n})\r\n\r\nexport class SpaceComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  spaceList: any[] = [];\r\n  spaceDetail: any = {};\r\n  searchKeyword: string = '';\r\n  searchLocation: string = '';\r\n\r\n  override ngOnInit(): void {\r\n    this.getSpaceList();\r\n  }\r\n\r\n  getSpaceList() {\r\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\r\n      body: {\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CSpaceName: this.searchKeyword || null,\r\n        CLocation: this.searchLocation || null\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.spaceList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSearch() {\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  openCreateModal(ref: any) {\r\n    this.spaceDetail = {\r\n      CSpaceName: '',\r\n      CLocation: '',\r\n      CDescription: '',\r\n      CIsEnable: true\r\n    };\r\n    this.dialogService.open(ref);\r\n  }\r\n\r\n  openEditModal(ref: any, item: any) {\r\n    this.getSpaceById(item.CSpaceId, ref);\r\n  }\r\n\r\n  getSpaceById(spaceId: number, ref: any) {\r\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\r\n      body: { CSpaceID: spaceId }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.spaceDetail = { ...res.Entries };\r\n        this.dialogService.open(ref);\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this._spaceService.apiSpaceSaveSpacePost$Json({\r\n      body: this.spaceDetail\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  deleteSpace(item: any) {\r\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\r\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\r\n        body: { CSpaceID: item.CSpaceId }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"刪除成功\");\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '刪除失敗');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[空間名稱]', this.spaceDetail.CSpaceName);\r\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CSpaceName, 50);\r\n    this.valid.isStringMaxLength('[描述]', this.spaceDetail.CDescription, 200);\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各空間的相關資訊，包含空間名稱、位置、描述及狀態等設定。</h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"spaceName\" class=\"label col-3\">空間名稱</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"spaceName\" nbInput class=\"w-full\" \r\n                   placeholder=\"搜尋空間名稱...\" \r\n                   [(ngModel)]=\"searchKeyword\"\r\n                   (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"location\" class=\"label col-3\">位置</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"location\" nbInput class=\"w-full\" \r\n                   placeholder=\"搜尋位置名稱...\" \r\n                   [(ngModel)]=\"searchLocation\"\r\n                   (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢按鈕移到這裡，放在搜尋條件的右下角 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search me-1\"></i>查詢\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openCreateModal(createModal)\">\r\n            <i class=\"fas fa-plus me-1\"></i>新增空間\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border\" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-2\">空間名稱</th>\r\n            <th scope=\"col\" class=\"col-2\">位置</th>\r\n            <th scope=\"col\" class=\"col-3\">描述</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-2\">建立時間</th>\r\n            <th scope=\"col\" class=\"col-2\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of spaceList\">\r\n            <td>{{ item.CSpaceName }}</td>\r\n            <td>{{ item.CLocation || '-' }}</td>\r\n            <td>{{ item.CDescription || '-' }}</td>\r\n            <td>\r\n              <nb-badge [status]=\"item.CIsEnable ? 'success' : 'danger'\" \r\n                        [text]=\"item.CIsEnable ? '啟用' : '停用'\">\r\n              </nb-badge>\r\n            </td>\r\n            <td>{{ item.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>\r\n            <td class=\"text-center w-32 px-0\">\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-success btn-sm text-left m-[2px]\"\r\n                      (click)=\"openEditModal(editModal, item)\">\r\n                <i class=\"fas fa-edit me-1\"></i>編輯\r\n              </button>\r\n              <button *ngIf=\"isDelete\" class=\"btn btn-outline-danger btn-sm m-[2px]\"\r\n                      (click)=\"deleteSpace(item)\">\r\n                <i class=\"fas fa-trash me-1\"></i>刪除\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n                    (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<!-- 新增模態框 -->\r\n<ng-template #createModal let-ref=\"dialogRef\">\r\n  <nb-card>\r\n    <nb-card-header>\r\n      <h5>新增空間</h5>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <form>\r\n        <div class=\"form-group\">\r\n          <label for=\"spaceName\">空間名稱 <span class=\"text-danger\">*</span></label>\r\n          <input type=\"text\" id=\"spaceName\" class=\"form-control\" [(ngModel)]=\"spaceDetail.CSpaceName\" name=\"spaceName\"\r\n            placeholder=\"請輸入空間名稱\">\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label for=\"location\">位置</label>\r\n          <input type=\"text\" id=\"location\" class=\"form-control\" [(ngModel)]=\"spaceDetail.CLocation\" name=\"location\"\r\n            placeholder=\"請輸入位置\">\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label for=\"description\">描述</label>\r\n          <textarea id=\"description\" class=\"form-control\" [(ngModel)]=\"spaceDetail.CDescription\" name=\"description\"\r\n            rows=\"3\" placeholder=\"請輸入空間描述\">\r\n          </textarea>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <nb-checkbox [(ngModel)]=\"spaceDetail.CIsEnable\" name=\"isEnable\">\r\n            啟用\r\n          </nb-checkbox>\r\n        </div>\r\n      </form>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"d-flex justify-content-end\">\r\n        <button nbButton status=\"basic\" class=\"mr-2\" (click)=\"onClose(ref)\">\r\n          取消\r\n        </button>\r\n        <button nbButton status=\"primary\" (click)=\"onSubmit(ref)\">\r\n          確認\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 編輯模態框 -->\r\n<ng-template #editModal let-ref=\"dialogRef\">\r\n  <nb-card>\r\n    <nb-card-header>\r\n      <h5>編輯空間</h5>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <form>\r\n        <div class=\"form-group\">\r\n          <label for=\"spaceNameEdit\">空間名稱 <span class=\"text-danger\">*</span></label>\r\n          <input type=\"text\" id=\"spaceNameEdit\" class=\"form-control\" [(ngModel)]=\"spaceDetail.CSpaceName\"\r\n            name=\"spaceName\" placeholder=\"請輸入空間名稱\">\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label for=\"locationEdit\">位置</label>\r\n          <input type=\"text\" id=\"locationEdit\" class=\"form-control\" [(ngModel)]=\"spaceDetail.CLocation\" name=\"location\"\r\n            placeholder=\"請輸入位置\">\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label for=\"descriptionEdit\">描述</label>\r\n          <textarea id=\"descriptionEdit\" class=\"form-control\" [(ngModel)]=\"spaceDetail.CDescription\" name=\"description\"\r\n            rows=\"3\" placeholder=\"請輸入空間描述\">\r\n          </textarea>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <nb-checkbox [(ngModel)]=\"spaceDetail.CIsEnable\" name=\"isEnable\">\r\n            啟用\r\n          </nb-checkbox>\r\n        </div>\r\n      </form>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"d-flex justify-content-end\">\r\n        <button nbButton status=\"basic\" class=\"mr-2\" (click)=\"onClose(ref)\">\r\n          取消\r\n        </button>\r\n        <button nbButton status=\"primary\" (click)=\"onSubmit(ref)\">\r\n          確認\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,kCAAkC;AAKhE,SAASC,GAAG,QAAQ,MAAM;;;;;;;;;;;;;;ICqChBC,EAAA,CAAAC,cAAA,iBAAiG;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,0DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,cAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,eAAA,CAAAH,cAAA,CAA4B;IAAA,EAAC;IAC9FR,EAAA,CAAAY,SAAA,YAAgC;IAAAZ,EAAA,CAAAa,MAAA,gCAClC;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IA6BLd,EAAA,CAAAC,cAAA,iBACiD;IAAzCD,EAAA,CAAAE,UAAA,mBAAAa,gEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAY,YAAA,GAAAnB,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAc,aAAA,CAAAD,YAAA,EAAAF,OAAA,CAA8B;IAAA,EAAC;IAC9CjB,EAAA,CAAAY,SAAA,YAAgC;IAAAZ,EAAA,CAAAa,MAAA,oBAClC;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IACTd,EAAA,CAAAC,cAAA,iBACoC;IAA5BD,EAAA,CAAAE,UAAA,mBAAAmB,gEAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA;MAAA,MAAAL,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiB,WAAA,CAAAN,OAAA,CAAiB;IAAA,EAAC;IACjCjB,EAAA,CAAAY,SAAA,YAAiC;IAAAZ,EAAA,CAAAa,MAAA,oBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;IAjBXd,EADF,CAAAC,cAAA,SAAmC,SAC7B;IAAAD,EAAA,CAAAa,MAAA,GAAqB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC9Bd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpCd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,mBAEW;IACbZ,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,IAA+C;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACxDd,EAAA,CAAAC,cAAA,cAAkC;IAKhCD,EAJA,CAAAwB,UAAA,KAAAC,uCAAA,qBACiD,KAAAC,uCAAA,qBAIb;IAIxC1B,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAnBCd,EAAA,CAAA2B,SAAA,GAAqB;IAArB3B,EAAA,CAAA4B,iBAAA,CAAAX,OAAA,CAAAY,UAAA,CAAqB;IACrB7B,EAAA,CAAA2B,SAAA,GAA2B;IAA3B3B,EAAA,CAAA4B,iBAAA,CAAAX,OAAA,CAAAa,SAAA,QAA2B;IAC3B9B,EAAA,CAAA2B,SAAA,GAA8B;IAA9B3B,EAAA,CAAA4B,iBAAA,CAAAX,OAAA,CAAAc,YAAA,QAA8B;IAEtB/B,EAAA,CAAA2B,SAAA,GAAgD;IAChD3B,EADA,CAAAgC,UAAA,WAAAf,OAAA,CAAAgB,SAAA,wBAAgD,SAAAhB,OAAA,CAAAgB,SAAA,mCACX;IAG7CjC,EAAA,CAAA2B,SAAA,GAA+C;IAA/C3B,EAAA,CAAA4B,iBAAA,CAAA5B,EAAA,CAAAkC,WAAA,QAAAjB,OAAA,CAAAkB,SAAA,sBAA+C;IAExCnC,EAAA,CAAA2B,SAAA,GAAc;IAAd3B,EAAA,CAAAgC,UAAA,SAAA1B,MAAA,CAAA8B,QAAA,CAAc;IAIdpC,EAAA,CAAA2B,SAAA,EAAc;IAAd3B,EAAA,CAAAgC,UAAA,SAAA1B,MAAA,CAAA+B,QAAA,CAAc;;;;;;IAqB/BrC,EAFJ,CAAAC,cAAA,cAAS,qBACS,SACV;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IACVb,EADU,CAAAc,YAAA,EAAK,EACE;IAIXd,EAHN,CAAAC,cAAA,mBAAc,WACN,cACoB,gBACC;IAAAD,EAAA,CAAAa,MAAA,gCAAK;IAAAb,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAa,MAAA,SAAC;IAAOb,EAAP,CAAAc,YAAA,EAAO,EAAQ;IACtEd,EAAA,CAAAC,cAAA,iBACwB;IAD+BD,EAAA,CAAAsC,gBAAA,2BAAAC,uEAAAC,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,WAAA,CAAAd,UAAA,EAAAW,MAAA,MAAAlC,MAAA,CAAAqC,WAAA,CAAAd,UAAA,GAAAW,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAAoC;IAE7FxC,EAFE,CAAAc,YAAA,EACwB,EACpB;IAGJd,EADF,CAAAC,cAAA,eAAwB,iBACA;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAChCd,EAAA,CAAAC,cAAA,iBACsB;IADgCD,EAAA,CAAAsC,gBAAA,2BAAAM,uEAAAJ,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,WAAA,CAAAb,SAAA,EAAAU,MAAA,MAAAlC,MAAA,CAAAqC,WAAA,CAAAb,SAAA,GAAAU,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAAmC;IAE3FxC,EAFE,CAAAc,YAAA,EACsB,EAClB;IAGJd,EADF,CAAAC,cAAA,eAAwB,iBACG;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACnCd,EAAA,CAAAC,cAAA,oBACiC;IADeD,EAAA,CAAAsC,gBAAA,2BAAAO,0EAAAL,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,WAAA,CAAAZ,YAAA,EAAAS,MAAA,MAAAlC,MAAA,CAAAqC,WAAA,CAAAZ,YAAA,GAAAS,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAAsC;IAEtFxC,EAAA,CAAAa,MAAA;IACFb,EADE,CAAAc,YAAA,EAAW,EACP;IAGJd,EADF,CAAAC,cAAA,eAAwB,uBAC2C;IAApDD,EAAA,CAAAsC,gBAAA,2BAAAQ,6EAAAN,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,WAAA,CAAAV,SAAA,EAAAO,MAAA,MAAAlC,MAAA,CAAAqC,WAAA,CAAAV,SAAA,GAAAO,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAAmC;IAC9CxC,EAAA,CAAAa,MAAA,sBACF;IAGNb,EAHM,CAAAc,YAAA,EAAc,EACV,EACD,EACM;IAGXd,EAFJ,CAAAC,cAAA,sBAAgB,eAC0B,kBAC8B;IAAvBD,EAAA,CAAAE,UAAA,mBAAA6C,gEAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAAI,aAAA,CAAAqC,GAAA,EAAAQ,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA4C,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IACjEhD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAA0D;IAAxBD,EAAA,CAAAE,UAAA,mBAAAiD,gEAAA;MAAA,MAAAH,OAAA,GAAAhD,EAAA,CAAAI,aAAA,CAAAqC,GAAA,EAAAQ,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA8C,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IACvDhD,EAAA,CAAAa,MAAA,sBACF;IAGNb,EAHM,CAAAc,YAAA,EAAS,EACL,EACS,EACT;;;;IAlCqDd,EAAA,CAAA2B,SAAA,IAAoC;IAApC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAqC,WAAA,CAAAd,UAAA,CAAoC;IAMrC7B,EAAA,CAAA2B,SAAA,GAAmC;IAAnC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAqC,WAAA,CAAAb,SAAA,CAAmC;IAMzC9B,EAAA,CAAA2B,SAAA,GAAsC;IAAtC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAqC,WAAA,CAAAZ,YAAA,CAAsC;IAMzE/B,EAAA,CAAA2B,SAAA,GAAmC;IAAnC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAqC,WAAA,CAAAV,SAAA,CAAmC;;;;;;IAuBpDjC,EAFJ,CAAAC,cAAA,cAAS,qBACS,SACV;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IACVb,EADU,CAAAc,YAAA,EAAK,EACE;IAIXd,EAHN,CAAAC,cAAA,mBAAc,WACN,cACoB,gBACK;IAAAD,EAAA,CAAAa,MAAA,gCAAK;IAAAb,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAa,MAAA,SAAC;IAAOb,EAAP,CAAAc,YAAA,EAAO,EAAQ;IAC1Ed,EAAA,CAAAC,cAAA,iBACyC;IADkBD,EAAA,CAAAsC,gBAAA,2BAAAgB,uEAAAd,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,WAAA,CAAAd,UAAA,EAAAW,MAAA,MAAAlC,MAAA,CAAAqC,WAAA,CAAAd,UAAA,GAAAW,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAAoC;IAEjGxC,EAFE,CAAAc,YAAA,EACyC,EACrC;IAGJd,EADF,CAAAC,cAAA,eAAwB,iBACI;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACpCd,EAAA,CAAAC,cAAA,iBACsB;IADoCD,EAAA,CAAAsC,gBAAA,2BAAAkB,uEAAAhB,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,WAAA,CAAAb,SAAA,EAAAU,MAAA,MAAAlC,MAAA,CAAAqC,WAAA,CAAAb,SAAA,GAAAU,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAAmC;IAE/FxC,EAFE,CAAAc,YAAA,EACsB,EAClB;IAGJd,EADF,CAAAC,cAAA,eAAwB,iBACO;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACvCd,EAAA,CAAAC,cAAA,oBACiC;IADmBD,EAAA,CAAAsC,gBAAA,2BAAAmB,0EAAAjB,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,WAAA,CAAAZ,YAAA,EAAAS,MAAA,MAAAlC,MAAA,CAAAqC,WAAA,CAAAZ,YAAA,GAAAS,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAAsC;IAE1FxC,EAAA,CAAAa,MAAA;IACFb,EADE,CAAAc,YAAA,EAAW,EACP;IAGJd,EADF,CAAAC,cAAA,eAAwB,uBAC2C;IAApDD,EAAA,CAAAsC,gBAAA,2BAAAoB,6EAAAlB,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,WAAA,CAAAV,SAAA,EAAAO,MAAA,MAAAlC,MAAA,CAAAqC,WAAA,CAAAV,SAAA,GAAAO,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAAmC;IAC9CxC,EAAA,CAAAa,MAAA,sBACF;IAGNb,EAHM,CAAAc,YAAA,EAAc,EACV,EACD,EACM;IAGXd,EAFJ,CAAAC,cAAA,sBAAgB,eAC0B,kBAC8B;IAAvBD,EAAA,CAAAE,UAAA,mBAAAyD,gEAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAAI,aAAA,CAAAmD,IAAA,EAAAN,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA4C,OAAA,CAAAU,OAAA,CAAY;IAAA,EAAC;IACjE5D,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAA0D;IAAxBD,EAAA,CAAAE,UAAA,mBAAA2D,gEAAA;MAAA,MAAAD,OAAA,GAAA5D,EAAA,CAAAI,aAAA,CAAAmD,IAAA,EAAAN,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA8C,QAAA,CAAAQ,OAAA,CAAa;IAAA,EAAC;IACvD5D,EAAA,CAAAa,MAAA,sBACF;IAGNb,EAHM,CAAAc,YAAA,EAAS,EACL,EACS,EACT;;;;IAlCyDd,EAAA,CAAA2B,SAAA,IAAoC;IAApC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAqC,WAAA,CAAAd,UAAA,CAAoC;IAMrC7B,EAAA,CAAA2B,SAAA,GAAmC;IAAnC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAqC,WAAA,CAAAb,SAAA,CAAmC;IAMzC9B,EAAA,CAAA2B,SAAA,GAAsC;IAAtC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAqC,WAAA,CAAAZ,YAAA,CAAsC;IAM7E/B,EAAA,CAAA2B,SAAA,GAAmC;IAAnC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAqC,WAAA,CAAAV,SAAA,CAAmC;;;ADjJ1D,OAAM,MAAO6B,cAAe,SAAQhE,aAAa;EAC/CiE,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACJ,KAAK,CAAC;IANO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAKN,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAA9B,WAAW,GAAQ,EAAE;IACrB,KAAA+B,aAAa,GAAW,EAAE;IAC1B,KAAAC,cAAc,GAAW,EAAE;EAV3B;EAYSC,QAAQA,CAAA;IACf,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,OAAO,IAAI,CAACX,aAAa,CAACY,6BAA6B,CAAC;MACtDC,IAAI,EAAE;QACJC,SAAS,EAAE,IAAI,CAACT,SAAS;QACzBU,QAAQ,EAAE,IAAI,CAACX,QAAQ;QACvBzC,UAAU,EAAE,IAAI,CAAC6C,aAAa,IAAI,IAAI;QACtC5C,SAAS,EAAE,IAAI,CAAC6C,cAAc,IAAI;;KAErC,CAAC,CAACO,IAAI,CACLnF,GAAG,CAACoF,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACZ,SAAS,GAAGU,GAAG,CAACC,OAAO;QAC5B,IAAI,CAACZ,YAAY,GAAGW,GAAG,CAACG,UAAW;MACrC,CAAC,MAAM;QACL,IAAI,CAACnB,OAAO,CAACoB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACnB,SAAS,GAAG,CAAC;IAClB,IAAI,CAACM,YAAY,EAAE;EACrB;EAEAc,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACrB,SAAS,GAAGqB,OAAO;IACxB,IAAI,CAACf,YAAY,EAAE;EACrB;EAEAlE,eAAeA,CAACkF,GAAQ;IACtB,IAAI,CAAClD,WAAW,GAAG;MACjBd,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBE,SAAS,EAAE;KACZ;IACD,IAAI,CAACgC,aAAa,CAAC6B,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEAzE,aAAaA,CAACyE,GAAQ,EAAEE,IAAS;IAC/B,IAAI,CAACC,YAAY,CAACD,IAAI,CAACE,QAAQ,EAAEJ,GAAG,CAAC;EACvC;EAEAG,YAAYA,CAACE,OAAe,EAAEL,GAAQ;IACpC,IAAI,CAAC3B,aAAa,CAACiC,6BAA6B,CAAC;MAC/CpB,IAAI,EAAE;QAAEqB,QAAQ,EAAEF;MAAO;KAC1B,CAAC,CAACT,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC1C,WAAW,GAAG;UAAE,GAAGwC,GAAG,CAACC;QAAO,CAAE;QACrC,IAAI,CAACnB,aAAa,CAAC6B,IAAI,CAACD,GAAG,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAAC1B,OAAO,CAACoB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEApC,QAAQA,CAACyC,GAAQ;IACf,IAAI,CAACQ,UAAU,EAAE;IACjB,IAAI,IAAI,CAACjC,KAAK,CAACkC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACpC,OAAO,CAACqC,aAAa,CAAC,IAAI,CAACpC,KAAK,CAACkC,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACpC,aAAa,CAACuC,0BAA0B,CAAC;MAC5C1B,IAAI,EAAE,IAAI,CAACpC;KACZ,CAAC,CAACuC,IAAI,CACLnF,GAAG,CAACoF,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAClB,OAAO,CAACuC,aAAa,CAAC,MAAM,CAAC;QAClCb,GAAG,CAACc,KAAK,EAAE;QACX,IAAI,CAAC9B,YAAY,EAAE;MACrB,CAAC,MAAM;QACL,IAAI,CAACV,OAAO,CAACoB,YAAY,CAACJ,GAAG,CAACK,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAlE,WAAWA,CAACwE,IAAS;IACnB,IAAIa,OAAO,CAAC,eAAe,CAAC,EAAE;MAC5B,IAAI,CAAC1C,aAAa,CAAC2C,4BAA4B,CAAC;QAC9C9B,IAAI,EAAE;UAAEqB,QAAQ,EAAEL,IAAI,CAACE;QAAQ;OAChC,CAAC,CAACR,SAAS,CAACN,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAAClB,OAAO,CAACuC,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAAC7B,YAAY,EAAE;QACrB,CAAC,MAAM;UACL,IAAI,CAACV,OAAO,CAACoB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,MAAM,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EACF;EAEAa,UAAUA,CAAA;IACR,IAAI,CAACjC,KAAK,CAAC0C,KAAK,EAAE;IAClB,IAAI,CAAC1C,KAAK,CAAC2C,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACpE,WAAW,CAACd,UAAU,CAAC;IAC1D,IAAI,CAACuC,KAAK,CAAC4C,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACrE,WAAW,CAACd,UAAU,EAAE,EAAE,CAAC;IACvE,IAAI,CAACuC,KAAK,CAAC4C,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACrE,WAAW,CAACZ,YAAY,EAAE,GAAG,CAAC;EAC1E;EAEAmB,OAAOA,CAAC2C,GAAQ;IACdA,GAAG,CAACc,KAAK,EAAE;EACb;;;uCAhIW7C,cAAc,EAAA9D,EAAA,CAAAiH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnH,EAAA,CAAAiH,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAArH,EAAA,CAAAiH,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAvH,EAAA,CAAAiH,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAzH,EAAA,CAAAiH,iBAAA,CAAAS,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAd7D,cAAc;MAAA8D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA9H,EAAA,CAAA+H,0BAAA,EAAA/H,EAAA,CAAAgI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC3BzBtI,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,SAAA,qBAAiC;UACnCZ,EAAA,CAAAc,YAAA,EAAiB;UAEfd,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAa,MAAA,mNAAkC;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAItEd,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACZ;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAErDd,EADF,CAAAC,cAAA,wBAA6B,gBAIO;UAD3BD,EAAA,CAAAsC,gBAAA,2BAAAkG,wDAAAhG,MAAA;YAAAxC,EAAA,CAAAI,aAAA,CAAAqI,GAAA;YAAAzI,EAAA,CAAA0C,kBAAA,CAAA6F,GAAA,CAAA7D,aAAA,EAAAlC,MAAA,MAAA+F,GAAA,CAAA7D,aAAA,GAAAlC,MAAA;YAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;UAAA,EAA2B;UAC3BxC,EAAA,CAAAE,UAAA,yBAAAwI,sDAAA;YAAA1I,EAAA,CAAAI,aAAA,CAAAqI,GAAA;YAAA,OAAAzI,EAAA,CAAAU,WAAA,CAAe6H,GAAA,CAAA7C,QAAA,EAAU;UAAA,EAAC;UAGvC1F,EANM,CAAAc,YAAA,EAGkC,EACpB,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACb;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAElDd,EADF,CAAAC,cAAA,wBAA6B,iBAIO;UAD3BD,EAAA,CAAAsC,gBAAA,2BAAAqG,wDAAAnG,MAAA;YAAAxC,EAAA,CAAAI,aAAA,CAAAqI,GAAA;YAAAzI,EAAA,CAAA0C,kBAAA,CAAA6F,GAAA,CAAA5D,cAAA,EAAAnC,MAAA,MAAA+F,GAAA,CAAA5D,cAAA,GAAAnC,MAAA;YAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;UAAA,EAA4B;UAC5BxC,EAAA,CAAAE,UAAA,yBAAA0I,sDAAA;YAAA5I,EAAA,CAAAI,aAAA,CAAAqI,GAAA;YAAA,OAAAzI,EAAA,CAAAU,WAAA,CAAe6H,GAAA,CAAA7C,QAAA,EAAU;UAAA,EAAC;UAGvC1F,EANM,CAAAc,YAAA,EAGkC,EACpB,EACZ,EACF;UAENd,EAAA,CAAAY,SAAA,cAEM;UAKFZ,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACO;UAArBD,EAAA,CAAAE,UAAA,mBAAA2I,iDAAA;YAAA7I,EAAA,CAAAI,aAAA,CAAAqI,GAAA;YAAA,OAAAzI,EAAA,CAAAU,WAAA,CAAS6H,GAAA,CAAA7C,QAAA,EAAU;UAAA,EAAC;UAC3D1F,EAAA,CAAAY,SAAA,aAAkC;UAAAZ,EAAA,CAAAa,MAAA,qBACpC;UAEJb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAGJd,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAAwB,UAAA,KAAAsH,iCAAA,qBAAiG;UAKvG9I,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;UAMEd,EAJR,CAAAC,cAAA,eAAmC,iBAC6D,aACrF,cACgD,cACrB;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAEpCb,EAFoC,CAAAc,YAAA,EAAK,EAClC,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAwB,UAAA,KAAAuH,6BAAA,mBAAmC;UAwB3C/I,EAHM,CAAAc,YAAA,EAAQ,EACF,EACJ,EACO;UAEbd,EADF,CAAAC,cAAA,0BAAsD,0BAEuB;UAD3DD,EAAA,CAAAsC,gBAAA,wBAAA0G,8DAAAxG,MAAA;YAAAxC,EAAA,CAAAI,aAAA,CAAAqI,GAAA;YAAAzI,EAAA,CAAA0C,kBAAA,CAAA6F,GAAA,CAAAhE,SAAA,EAAA/B,MAAA,MAAA+F,GAAA,CAAAhE,SAAA,GAAA/B,MAAA;YAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;UAAA,EAAoB;UACpBxC,EAAA,CAAAE,UAAA,wBAAA8I,8DAAAxG,MAAA;YAAAxC,EAAA,CAAAI,aAAA,CAAAqI,GAAA;YAAA,OAAAzI,EAAA,CAAAU,WAAA,CAAc6H,GAAA,CAAA5C,WAAA,CAAAnD,MAAA,CAAmB;UAAA,EAAC;UAGtDxC,EAFI,CAAAc,YAAA,EAAiB,EACF,EACT;UAkDVd,EA/CA,CAAAwB,UAAA,KAAAyH,sCAAA,iCAAAjJ,EAAA,CAAAkJ,sBAAA,CAA8C,KAAAC,sCAAA,iCAAAnJ,EAAA,CAAAkJ,sBAAA,CA+CF;;;UArIzBlJ,EAAA,CAAA2B,SAAA,IAA2B;UAA3B3B,EAAA,CAAAqD,gBAAA,YAAAkF,GAAA,CAAA7D,aAAA,CAA2B;UAY3B1E,EAAA,CAAA2B,SAAA,GAA4B;UAA5B3B,EAAA,CAAAqD,gBAAA,YAAAkF,GAAA,CAAA5D,cAAA,CAA4B;UAqBK3E,EAAA,CAAA2B,SAAA,GAAc;UAAd3B,EAAA,CAAAgC,UAAA,SAAAuG,GAAA,CAAAa,QAAA,CAAc;UAoBnCpJ,EAAA,CAAA2B,SAAA,IAAY;UAAZ3B,EAAA,CAAAgC,UAAA,YAAAuG,GAAA,CAAA9D,SAAA,CAAY;UA0BvBzE,EAAA,CAAA2B,SAAA,GAAoB;UAApB3B,EAAA,CAAAqD,gBAAA,SAAAkF,GAAA,CAAAhE,SAAA,CAAoB;UAAuBvE,EAAtB,CAAAgC,UAAA,aAAAuG,GAAA,CAAAjE,QAAA,CAAqB,mBAAAiE,GAAA,CAAA/D,YAAA,CAAgC;;;qBDrE1F3E,YAAY,EAAAwJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EACZ5J,YAAY,EAAA6J,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAAL,EAAA,CAAAM,MAAA,EAAA3C,EAAA,CAAA4C,eAAA,EAAA5C,EAAA,CAAA6C,mBAAA,EAAA7C,EAAA,CAAA8C,qBAAA,EAAA9C,EAAA,CAAA+C,qBAAA,EAAA/C,EAAA,CAAAgD,mBAAA,EAAAhD,EAAA,CAAAiD,gBAAA,EAAAjD,EAAA,CAAAkD,iBAAA,EAAAlD,EAAA,CAAAmD,oBAAA,EAAAC,EAAA,CAAAC,aAAA,EAAArD,EAAA,CAAAsD,gBAAA,EAAAC,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}