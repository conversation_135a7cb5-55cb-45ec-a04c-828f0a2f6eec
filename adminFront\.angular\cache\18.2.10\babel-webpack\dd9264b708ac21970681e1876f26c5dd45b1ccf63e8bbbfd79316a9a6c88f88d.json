{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Konkani Latin script [gom-latn]\n//! author : The Discoverer : https://github.com/WikiDiscoverer\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var format = {\n      s: ['thoddea sekondamni', 'thodde sekond'],\n      ss: [number + ' sekondamni', number + ' sekond'],\n      m: ['eka mintan', 'ek minut'],\n      mm: [number + ' mintamni', number + ' mintam'],\n      h: ['eka voran', 'ek vor'],\n      hh: [number + ' voramni', number + ' voram'],\n      d: ['eka disan', 'ek dis'],\n      dd: [number + ' disamni', number + ' dis'],\n      M: ['eka mhoinean', 'ek mhoino'],\n      MM: [number + ' mhoineamni', number + ' mhoine'],\n      y: ['eka vorsan', 'ek voros'],\n      yy: [number + ' vorsamni', number + ' vorsam']\n    };\n    return isFuture ? format[key][0] : format[key][1];\n  }\n  var gomLatn = moment.defineLocale('gom-latn', {\n    months: {\n      standalone: 'Janer_Febrer_Mars_Abril_Mai_Jun_Julai_Agost_Setembr_Otubr_Novembr_Dezembr'.split('_'),\n      format: 'Janerachea_Febrerachea_Marsachea_Abrilachea_Maiachea_Junachea_Julaiachea_Agostachea_Setembrachea_Otubrachea_Novembrachea_Dezembrachea'.split('_'),\n      isFormat: /MMMM(\\s)+D[oD]?/\n    },\n    monthsShort: 'Jan._Feb._Mars_Abr._Mai_Jun_Jul._Ago._Set._Otu._Nov._Dez.'.split('_'),\n    monthsParseExact: true,\n    weekdays: \"Aitar_Somar_Mongllar_Budhvar_Birestar_Sukrar_Son'var\".split('_'),\n    weekdaysShort: 'Ait._Som._Mon._Bud._Bre._Suk._Son.'.split('_'),\n    weekdaysMin: 'Ai_Sm_Mo_Bu_Br_Su_Sn'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'A h:mm [vazta]',\n      LTS: 'A h:mm:ss [vazta]',\n      L: 'DD-MM-YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY A h:mm [vazta]',\n      LLLL: 'dddd, MMMM Do, YYYY, A h:mm [vazta]',\n      llll: 'ddd, D MMM YYYY, A h:mm [vazta]'\n    },\n    calendar: {\n      sameDay: '[Aiz] LT',\n      nextDay: '[Faleam] LT',\n      nextWeek: '[Fuddlo] dddd[,] LT',\n      lastDay: '[Kal] LT',\n      lastWeek: '[Fattlo] dddd[,] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s',\n      past: '%s adim',\n      s: processRelativeTime,\n      ss: processRelativeTime,\n      m: processRelativeTime,\n      mm: processRelativeTime,\n      h: processRelativeTime,\n      hh: processRelativeTime,\n      d: processRelativeTime,\n      dd: processRelativeTime,\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(er)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        // the ordinal 'er' only applies to day of the month\n        case 'D':\n          return number + 'er';\n        default:\n        case 'M':\n        case 'Q':\n        case 'DDD':\n        case 'd':\n        case 'w':\n        case 'W':\n          return number;\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week\n      doy: 3 // The week that contains Jan 4th is the first week of the year (7 + 0 - 4)\n    },\n    meridiemParse: /rati|sokallim|donparam|sanje/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'rati') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'sokallim') {\n        return hour;\n      } else if (meridiem === 'donparam') {\n        return hour > 12 ? hour : hour + 12;\n      } else if (meridiem === 'sanje') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'rati';\n      } else if (hour < 12) {\n        return 'sokallim';\n      } else if (hour < 16) {\n        return 'donparam';\n      } else if (hour < 20) {\n        return 'sanje';\n      } else {\n        return 'rati';\n      }\n    }\n  });\n  return gomLatn;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}