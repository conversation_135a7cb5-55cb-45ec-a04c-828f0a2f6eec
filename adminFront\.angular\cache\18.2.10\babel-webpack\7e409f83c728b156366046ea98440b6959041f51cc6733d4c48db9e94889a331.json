{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Grid is a region which contains at most 4 cartesian systems\r\n *\r\n * TODO Default cartesian\r\n */\nimport { isObject, each, indexOf, retrieve3, keys } from 'zrender/lib/core/util.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport { createScaleByModel, ifAxisCrossZero, niceScaleExtent, estimateLabelUnionRect, getDataDimensionsOnAxis } from '../../coord/axisHelper.js';\nimport Cartesian2D, { cartesian2DDimensions } from './Cartesian2D.js';\nimport Axis2D from './Axis2D.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nimport { isCartesian2DSeries, findAxisModels } from './cartesianAxisHelper.js';\nimport { isIntervalOrLogScale } from '../../scale/helper.js';\nimport { alignScaleTicks } from '../axisAlignTicks.js';\nvar Grid = /** @class */function () {\n  function Grid(gridModel, ecModel, api) {\n    // FIXME:TS where used (different from registered type 'cartesian2d')?\n    this.type = 'grid';\n    this._coordsMap = {};\n    this._coordsList = [];\n    this._axesMap = {};\n    this._axesList = [];\n    this.axisPointerEnabled = true;\n    this.dimensions = cartesian2DDimensions;\n    this._initCartesian(gridModel, ecModel, api);\n    this.model = gridModel;\n  }\n  Grid.prototype.getRect = function () {\n    return this._rect;\n  };\n  Grid.prototype.update = function (ecModel, api) {\n    var axesMap = this._axesMap;\n    this._updateScale(ecModel, this.model);\n    function updateAxisTicks(axes) {\n      var alignTo;\n      // Axis is added in order of axisIndex.\n      var axesIndices = keys(axes);\n      var len = axesIndices.length;\n      if (!len) {\n        return;\n      }\n      var axisNeedsAlign = [];\n      // Process once and calculate the ticks for those don't use alignTicks.\n      for (var i = len - 1; i >= 0; i--) {\n        var idx = +axesIndices[i]; // Convert to number.\n        var axis = axes[idx];\n        var model = axis.model;\n        var scale = axis.scale;\n        if (\n        // Only value and log axis without interval support alignTicks.\n        isIntervalOrLogScale(scale) && model.get('alignTicks') && model.get('interval') == null) {\n          axisNeedsAlign.push(axis);\n        } else {\n          niceScaleExtent(scale, model);\n          if (isIntervalOrLogScale(scale)) {\n            // Can only align to interval or log axis.\n            alignTo = axis;\n          }\n        }\n      }\n      ;\n      // All axes has set alignTicks. Pick the first one.\n      // PENDING. Should we find the axis that both set interval, min, max and align to this one?\n      if (axisNeedsAlign.length) {\n        if (!alignTo) {\n          alignTo = axisNeedsAlign.pop();\n          niceScaleExtent(alignTo.scale, alignTo.model);\n        }\n        each(axisNeedsAlign, function (axis) {\n          alignScaleTicks(axis.scale, axis.model, alignTo.scale);\n        });\n      }\n    }\n    updateAxisTicks(axesMap.x);\n    updateAxisTicks(axesMap.y);\n    // Key: axisDim_axisIndex, value: boolean, whether onZero target.\n    var onZeroRecords = {};\n    each(axesMap.x, function (xAxis) {\n      fixAxisOnZero(axesMap, 'y', xAxis, onZeroRecords);\n    });\n    each(axesMap.y, function (yAxis) {\n      fixAxisOnZero(axesMap, 'x', yAxis, onZeroRecords);\n    });\n    // Resize again if containLabel is enabled\n    // FIXME It may cause getting wrong grid size in data processing stage\n    this.resize(this.model, api);\n  };\n  /**\r\n   * Resize the grid\r\n   */\n  Grid.prototype.resize = function (gridModel, api, ignoreContainLabel) {\n    var boxLayoutParams = gridModel.getBoxLayoutParams();\n    var isContainLabel = !ignoreContainLabel && gridModel.get('containLabel');\n    var gridRect = getLayoutRect(boxLayoutParams, {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n    this._rect = gridRect;\n    var axesList = this._axesList;\n    adjustAxes();\n    // Minus label size\n    if (isContainLabel) {\n      each(axesList, function (axis) {\n        if (!axis.model.get(['axisLabel', 'inside'])) {\n          var labelUnionRect = estimateLabelUnionRect(axis);\n          if (labelUnionRect) {\n            var dim = axis.isHorizontal() ? 'height' : 'width';\n            var margin = axis.model.get(['axisLabel', 'margin']);\n            gridRect[dim] -= labelUnionRect[dim] + margin;\n            if (axis.position === 'top') {\n              gridRect.y += labelUnionRect.height + margin;\n            } else if (axis.position === 'left') {\n              gridRect.x += labelUnionRect.width + margin;\n            }\n          }\n        }\n      });\n      adjustAxes();\n    }\n    each(this._coordsList, function (coord) {\n      // Calculate affine matrix to accelerate the data to point transform.\n      // If all the axes scales are time or value.\n      coord.calcAffineTransform();\n    });\n    function adjustAxes() {\n      each(axesList, function (axis) {\n        var isHorizontal = axis.isHorizontal();\n        var extent = isHorizontal ? [0, gridRect.width] : [0, gridRect.height];\n        var idx = axis.inverse ? 1 : 0;\n        axis.setExtent(extent[idx], extent[1 - idx]);\n        updateAxisTransform(axis, isHorizontal ? gridRect.x : gridRect.y);\n      });\n    }\n  };\n  Grid.prototype.getAxis = function (dim, axisIndex) {\n    var axesMapOnDim = this._axesMap[dim];\n    if (axesMapOnDim != null) {\n      return axesMapOnDim[axisIndex || 0];\n    }\n  };\n  Grid.prototype.getAxes = function () {\n    return this._axesList.slice();\n  };\n  Grid.prototype.getCartesian = function (xAxisIndex, yAxisIndex) {\n    if (xAxisIndex != null && yAxisIndex != null) {\n      var key = 'x' + xAxisIndex + 'y' + yAxisIndex;\n      return this._coordsMap[key];\n    }\n    if (isObject(xAxisIndex)) {\n      yAxisIndex = xAxisIndex.yAxisIndex;\n      xAxisIndex = xAxisIndex.xAxisIndex;\n    }\n    for (var i = 0, coordList = this._coordsList; i < coordList.length; i++) {\n      if (coordList[i].getAxis('x').index === xAxisIndex || coordList[i].getAxis('y').index === yAxisIndex) {\n        return coordList[i];\n      }\n    }\n  };\n  Grid.prototype.getCartesians = function () {\n    return this._coordsList.slice();\n  };\n  /**\r\n   * @implements\r\n   */\n  Grid.prototype.convertToPixel = function (ecModel, finder, value) {\n    var target = this._findConvertTarget(finder);\n    return target.cartesian ? target.cartesian.dataToPoint(value) : target.axis ? target.axis.toGlobalCoord(target.axis.dataToCoord(value)) : null;\n  };\n  /**\r\n   * @implements\r\n   */\n  Grid.prototype.convertFromPixel = function (ecModel, finder, value) {\n    var target = this._findConvertTarget(finder);\n    return target.cartesian ? target.cartesian.pointToData(value) : target.axis ? target.axis.coordToData(target.axis.toLocalCoord(value)) : null;\n  };\n  Grid.prototype._findConvertTarget = function (finder) {\n    var seriesModel = finder.seriesModel;\n    var xAxisModel = finder.xAxisModel || seriesModel && seriesModel.getReferringComponents('xAxis', SINGLE_REFERRING).models[0];\n    var yAxisModel = finder.yAxisModel || seriesModel && seriesModel.getReferringComponents('yAxis', SINGLE_REFERRING).models[0];\n    var gridModel = finder.gridModel;\n    var coordsList = this._coordsList;\n    var cartesian;\n    var axis;\n    if (seriesModel) {\n      cartesian = seriesModel.coordinateSystem;\n      indexOf(coordsList, cartesian) < 0 && (cartesian = null);\n    } else if (xAxisModel && yAxisModel) {\n      cartesian = this.getCartesian(xAxisModel.componentIndex, yAxisModel.componentIndex);\n    } else if (xAxisModel) {\n      axis = this.getAxis('x', xAxisModel.componentIndex);\n    } else if (yAxisModel) {\n      axis = this.getAxis('y', yAxisModel.componentIndex);\n    }\n    // Lowest priority.\n    else if (gridModel) {\n      var grid = gridModel.coordinateSystem;\n      if (grid === this) {\n        cartesian = this._coordsList[0];\n      }\n    }\n    return {\n      cartesian: cartesian,\n      axis: axis\n    };\n  };\n  /**\r\n   * @implements\r\n   */\n  Grid.prototype.containPoint = function (point) {\n    var coord = this._coordsList[0];\n    if (coord) {\n      return coord.containPoint(point);\n    }\n  };\n  /**\r\n   * Initialize cartesian coordinate systems\r\n   */\n  Grid.prototype._initCartesian = function (gridModel, ecModel, api) {\n    var _this = this;\n    var grid = this;\n    var axisPositionUsed = {\n      left: false,\n      right: false,\n      top: false,\n      bottom: false\n    };\n    var axesMap = {\n      x: {},\n      y: {}\n    };\n    var axesCount = {\n      x: 0,\n      y: 0\n    };\n    // Create axis\n    ecModel.eachComponent('xAxis', createAxisCreator('x'), this);\n    ecModel.eachComponent('yAxis', createAxisCreator('y'), this);\n    if (!axesCount.x || !axesCount.y) {\n      // Roll back when there no either x or y axis\n      this._axesMap = {};\n      this._axesList = [];\n      return;\n    }\n    this._axesMap = axesMap;\n    // Create cartesian2d\n    each(axesMap.x, function (xAxis, xAxisIndex) {\n      each(axesMap.y, function (yAxis, yAxisIndex) {\n        var key = 'x' + xAxisIndex + 'y' + yAxisIndex;\n        var cartesian = new Cartesian2D(key);\n        cartesian.master = _this;\n        cartesian.model = gridModel;\n        _this._coordsMap[key] = cartesian;\n        _this._coordsList.push(cartesian);\n        cartesian.addAxis(xAxis);\n        cartesian.addAxis(yAxis);\n      });\n    });\n    function createAxisCreator(dimName) {\n      return function (axisModel, idx) {\n        if (!isAxisUsedInTheGrid(axisModel, gridModel)) {\n          return;\n        }\n        var axisPosition = axisModel.get('position');\n        if (dimName === 'x') {\n          // Fix position\n          if (axisPosition !== 'top' && axisPosition !== 'bottom') {\n            // Default bottom of X\n            axisPosition = axisPositionUsed.bottom ? 'top' : 'bottom';\n          }\n        } else {\n          // Fix position\n          if (axisPosition !== 'left' && axisPosition !== 'right') {\n            // Default left of Y\n            axisPosition = axisPositionUsed.left ? 'right' : 'left';\n          }\n        }\n        axisPositionUsed[axisPosition] = true;\n        var axis = new Axis2D(dimName, createScaleByModel(axisModel), [0, 0], axisModel.get('type'), axisPosition);\n        var isCategory = axis.type === 'category';\n        axis.onBand = isCategory && axisModel.get('boundaryGap');\n        axis.inverse = axisModel.get('inverse');\n        // Inject axis into axisModel\n        axisModel.axis = axis;\n        // Inject axisModel into axis\n        axis.model = axisModel;\n        // Inject grid info axis\n        axis.grid = grid;\n        // Index of axis, can be used as key\n        axis.index = idx;\n        grid._axesList.push(axis);\n        axesMap[dimName][idx] = axis;\n        axesCount[dimName]++;\n      };\n    }\n  };\n  /**\r\n   * Update cartesian properties from series.\r\n   */\n  Grid.prototype._updateScale = function (ecModel, gridModel) {\n    // Reset scale\n    each(this._axesList, function (axis) {\n      axis.scale.setExtent(Infinity, -Infinity);\n      if (axis.type === 'category') {\n        var categorySortInfo = axis.model.get('categorySortInfo');\n        axis.scale.setSortInfo(categorySortInfo);\n      }\n    });\n    ecModel.eachSeries(function (seriesModel) {\n      if (isCartesian2DSeries(seriesModel)) {\n        var axesModelMap = findAxisModels(seriesModel);\n        var xAxisModel = axesModelMap.xAxisModel;\n        var yAxisModel = axesModelMap.yAxisModel;\n        if (!isAxisUsedInTheGrid(xAxisModel, gridModel) || !isAxisUsedInTheGrid(yAxisModel, gridModel)) {\n          return;\n        }\n        var cartesian = this.getCartesian(xAxisModel.componentIndex, yAxisModel.componentIndex);\n        var data = seriesModel.getData();\n        var xAxis = cartesian.getAxis('x');\n        var yAxis = cartesian.getAxis('y');\n        unionExtent(data, xAxis);\n        unionExtent(data, yAxis);\n      }\n    }, this);\n    function unionExtent(data, axis) {\n      each(getDataDimensionsOnAxis(data, axis.dim), function (dim) {\n        axis.scale.unionExtentFromData(data, dim);\n      });\n    }\n  };\n  /**\r\n   * @param dim 'x' or 'y' or 'auto' or null/undefined\r\n   */\n  Grid.prototype.getTooltipAxes = function (dim) {\n    var baseAxes = [];\n    var otherAxes = [];\n    each(this.getCartesians(), function (cartesian) {\n      var baseAxis = dim != null && dim !== 'auto' ? cartesian.getAxis(dim) : cartesian.getBaseAxis();\n      var otherAxis = cartesian.getOtherAxis(baseAxis);\n      indexOf(baseAxes, baseAxis) < 0 && baseAxes.push(baseAxis);\n      indexOf(otherAxes, otherAxis) < 0 && otherAxes.push(otherAxis);\n    });\n    return {\n      baseAxes: baseAxes,\n      otherAxes: otherAxes\n    };\n  };\n  Grid.create = function (ecModel, api) {\n    var grids = [];\n    ecModel.eachComponent('grid', function (gridModel, idx) {\n      var grid = new Grid(gridModel, ecModel, api);\n      grid.name = 'grid_' + idx;\n      // dataSampling requires axis extent, so resize\n      // should be performed in create stage.\n      grid.resize(gridModel, api, true);\n      gridModel.coordinateSystem = grid;\n      grids.push(grid);\n    });\n    // Inject the coordinateSystems into seriesModel\n    ecModel.eachSeries(function (seriesModel) {\n      if (!isCartesian2DSeries(seriesModel)) {\n        return;\n      }\n      var axesModelMap = findAxisModels(seriesModel);\n      var xAxisModel = axesModelMap.xAxisModel;\n      var yAxisModel = axesModelMap.yAxisModel;\n      var gridModel = xAxisModel.getCoordSysModel();\n      if (process.env.NODE_ENV !== 'production') {\n        if (!gridModel) {\n          throw new Error('Grid \"' + retrieve3(xAxisModel.get('gridIndex'), xAxisModel.get('gridId'), 0) + '\" not found');\n        }\n        if (xAxisModel.getCoordSysModel() !== yAxisModel.getCoordSysModel()) {\n          throw new Error('xAxis and yAxis must use the same grid');\n        }\n      }\n      var grid = gridModel.coordinateSystem;\n      seriesModel.coordinateSystem = grid.getCartesian(xAxisModel.componentIndex, yAxisModel.componentIndex);\n    });\n    return grids;\n  };\n  // For deciding which dimensions to use when creating list data\n  Grid.dimensions = cartesian2DDimensions;\n  return Grid;\n}();\n/**\r\n * Check if the axis is used in the specified grid.\r\n */\nfunction isAxisUsedInTheGrid(axisModel, gridModel) {\n  return axisModel.getCoordSysModel() === gridModel;\n}\nfunction fixAxisOnZero(axesMap, otherAxisDim, axis,\n// Key: see `getOnZeroRecordKey`\nonZeroRecords) {\n  axis.getAxesOnZeroOf = function () {\n    // TODO: onZero of multiple axes.\n    return otherAxisOnZeroOf ? [otherAxisOnZeroOf] : [];\n  };\n  // onZero can not be enabled in these two situations:\n  // 1. When any other axis is a category axis.\n  // 2. When no axis is cross 0 point.\n  var otherAxes = axesMap[otherAxisDim];\n  var otherAxisOnZeroOf;\n  var axisModel = axis.model;\n  var onZero = axisModel.get(['axisLine', 'onZero']);\n  var onZeroAxisIndex = axisModel.get(['axisLine', 'onZeroAxisIndex']);\n  if (!onZero) {\n    return;\n  }\n  // If target axis is specified.\n  if (onZeroAxisIndex != null) {\n    if (canOnZeroToAxis(otherAxes[onZeroAxisIndex])) {\n      otherAxisOnZeroOf = otherAxes[onZeroAxisIndex];\n    }\n  } else {\n    // Find the first available other axis.\n    for (var idx in otherAxes) {\n      if (otherAxes.hasOwnProperty(idx) && canOnZeroToAxis(otherAxes[idx])\n      // Consider that two Y axes on one value axis,\n      // if both onZero, the two Y axes overlap.\n      && !onZeroRecords[getOnZeroRecordKey(otherAxes[idx])]) {\n        otherAxisOnZeroOf = otherAxes[idx];\n        break;\n      }\n    }\n  }\n  if (otherAxisOnZeroOf) {\n    onZeroRecords[getOnZeroRecordKey(otherAxisOnZeroOf)] = true;\n  }\n  function getOnZeroRecordKey(axis) {\n    return axis.dim + '_' + axis.index;\n  }\n}\nfunction canOnZeroToAxis(axis) {\n  return axis && axis.type !== 'category' && axis.type !== 'time' && ifAxisCrossZero(axis);\n}\nfunction updateAxisTransform(axis, coordBase) {\n  var axisExtent = axis.getExtent();\n  var axisExtentSum = axisExtent[0] + axisExtent[1];\n  // Fast transform\n  axis.toGlobalCoord = axis.dim === 'x' ? function (coord) {\n    return coord + coordBase;\n  } : function (coord) {\n    return axisExtentSum - coord + coordBase;\n  };\n  axis.toLocalCoord = axis.dim === 'x' ? function (coord) {\n    return coord - coordBase;\n  } : function (coord) {\n    return axisExtentSum - coord + coordBase;\n  };\n}\nexport default Grid;", "map": {"version": 3, "names": ["isObject", "each", "indexOf", "retrieve3", "keys", "getLayoutRect", "createScaleByModel", "ifAxisCrossZero", "niceScaleExtent", "estimateLabelUnionRect", "getDataDimensionsOnAxis", "Cartesian2D", "cartesian2DDimensions", "Axis2D", "SINGLE_REFERRING", "isCartesian2DSeries", "findAxisModels", "isIntervalOrLogScale", "alignScaleTicks", "Grid", "gridModel", "ecModel", "api", "type", "_coordsMap", "_coordsList", "_axesMap", "_axesList", "axisPointerEnabled", "dimensions", "_initCartesian", "model", "prototype", "getRect", "_rect", "update", "axesMap", "_updateScale", "updateAxisTicks", "axes", "alignTo", "axesIndices", "len", "length", "axisNeedsAlign", "i", "idx", "axis", "scale", "get", "push", "pop", "x", "y", "onZeroRecords", "xAxis", "fixAxisOnZero", "yAxis", "resize", "ignoreContainLabel", "boxLayoutParams", "getBoxLayoutParams", "isContainLabel", "gridRect", "width", "getWidth", "height", "getHeight", "axesList", "adjustAxes", "labelUnionRect", "dim", "isHorizontal", "margin", "position", "coord", "calcAffineTransform", "extent", "inverse", "setExtent", "updateAxisTransform", "getAxis", "axisIndex", "axesMapOnDim", "getAxes", "slice", "getCartesian", "xAxisIndex", "yAxisIndex", "key", "coordList", "index", "getCartesians", "convertToPixel", "finder", "value", "target", "_findConvertTarget", "cartesian", "dataToPoint", "toGlobalCoord", "dataToCoord", "convertFromPixel", "pointToData", "coordToData", "toLocalCoord", "seriesModel", "xAxisModel", "getReferringComponents", "models", "yAxisModel", "coordsList", "coordinateSystem", "componentIndex", "grid", "containPoint", "point", "_this", "axisPositionUsed", "left", "right", "top", "bottom", "axesCount", "eachComponent", "createAxisCreator", "master", "addAxis", "dimName", "axisModel", "isAxisUsedInTheGrid", "axisPosition", "isCategory", "onBand", "Infinity", "categorySortInfo", "setSortInfo", "eachSeries", "axesModelMap", "data", "getData", "unionExtent", "unionExtentFromData", "getTooltipAxes", "baseAxes", "otherAxes", "baseAxis", "getBaseAxis", "otherAxis", "getOtherAxis", "create", "grids", "name", "getCoordSysModel", "process", "env", "NODE_ENV", "Error", "otherAxisDim", "getAxesOnZeroOf", "otherAxisOnZeroOf", "onZero", "onZeroAxisIndex", "canOnZeroToAxis", "hasOwnProperty", "getOnZeroRecordKey", "coordBase", "axisExtent", "getExtent", "axisExtentSum"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/coord/cartesian/Grid.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Grid is a region which contains at most 4 cartesian systems\r\n *\r\n * TODO Default cartesian\r\n */\nimport { isObject, each, indexOf, retrieve3, keys } from 'zrender/lib/core/util.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport { createScaleByModel, ifAxisCrossZero, niceScaleExtent, estimateLabelUnionRect, getDataDimensionsOnAxis } from '../../coord/axisHelper.js';\nimport Cartesian2D, { cartesian2DDimensions } from './Cartesian2D.js';\nimport Axis2D from './Axis2D.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nimport { isCartesian2DSeries, findAxisModels } from './cartesianAxisHelper.js';\nimport { isIntervalOrLogScale } from '../../scale/helper.js';\nimport { alignScaleTicks } from '../axisAlignTicks.js';\nvar Grid = /** @class */function () {\n  function Grid(gridModel, ecModel, api) {\n    // FIXME:TS where used (different from registered type 'cartesian2d')?\n    this.type = 'grid';\n    this._coordsMap = {};\n    this._coordsList = [];\n    this._axesMap = {};\n    this._axesList = [];\n    this.axisPointerEnabled = true;\n    this.dimensions = cartesian2DDimensions;\n    this._initCartesian(gridModel, ecModel, api);\n    this.model = gridModel;\n  }\n  Grid.prototype.getRect = function () {\n    return this._rect;\n  };\n  Grid.prototype.update = function (ecModel, api) {\n    var axesMap = this._axesMap;\n    this._updateScale(ecModel, this.model);\n    function updateAxisTicks(axes) {\n      var alignTo;\n      // Axis is added in order of axisIndex.\n      var axesIndices = keys(axes);\n      var len = axesIndices.length;\n      if (!len) {\n        return;\n      }\n      var axisNeedsAlign = [];\n      // Process once and calculate the ticks for those don't use alignTicks.\n      for (var i = len - 1; i >= 0; i--) {\n        var idx = +axesIndices[i]; // Convert to number.\n        var axis = axes[idx];\n        var model = axis.model;\n        var scale = axis.scale;\n        if (\n        // Only value and log axis without interval support alignTicks.\n        isIntervalOrLogScale(scale) && model.get('alignTicks') && model.get('interval') == null) {\n          axisNeedsAlign.push(axis);\n        } else {\n          niceScaleExtent(scale, model);\n          if (isIntervalOrLogScale(scale)) {\n            // Can only align to interval or log axis.\n            alignTo = axis;\n          }\n        }\n      }\n      ;\n      // All axes has set alignTicks. Pick the first one.\n      // PENDING. Should we find the axis that both set interval, min, max and align to this one?\n      if (axisNeedsAlign.length) {\n        if (!alignTo) {\n          alignTo = axisNeedsAlign.pop();\n          niceScaleExtent(alignTo.scale, alignTo.model);\n        }\n        each(axisNeedsAlign, function (axis) {\n          alignScaleTicks(axis.scale, axis.model, alignTo.scale);\n        });\n      }\n    }\n    updateAxisTicks(axesMap.x);\n    updateAxisTicks(axesMap.y);\n    // Key: axisDim_axisIndex, value: boolean, whether onZero target.\n    var onZeroRecords = {};\n    each(axesMap.x, function (xAxis) {\n      fixAxisOnZero(axesMap, 'y', xAxis, onZeroRecords);\n    });\n    each(axesMap.y, function (yAxis) {\n      fixAxisOnZero(axesMap, 'x', yAxis, onZeroRecords);\n    });\n    // Resize again if containLabel is enabled\n    // FIXME It may cause getting wrong grid size in data processing stage\n    this.resize(this.model, api);\n  };\n  /**\r\n   * Resize the grid\r\n   */\n  Grid.prototype.resize = function (gridModel, api, ignoreContainLabel) {\n    var boxLayoutParams = gridModel.getBoxLayoutParams();\n    var isContainLabel = !ignoreContainLabel && gridModel.get('containLabel');\n    var gridRect = getLayoutRect(boxLayoutParams, {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n    this._rect = gridRect;\n    var axesList = this._axesList;\n    adjustAxes();\n    // Minus label size\n    if (isContainLabel) {\n      each(axesList, function (axis) {\n        if (!axis.model.get(['axisLabel', 'inside'])) {\n          var labelUnionRect = estimateLabelUnionRect(axis);\n          if (labelUnionRect) {\n            var dim = axis.isHorizontal() ? 'height' : 'width';\n            var margin = axis.model.get(['axisLabel', 'margin']);\n            gridRect[dim] -= labelUnionRect[dim] + margin;\n            if (axis.position === 'top') {\n              gridRect.y += labelUnionRect.height + margin;\n            } else if (axis.position === 'left') {\n              gridRect.x += labelUnionRect.width + margin;\n            }\n          }\n        }\n      });\n      adjustAxes();\n    }\n    each(this._coordsList, function (coord) {\n      // Calculate affine matrix to accelerate the data to point transform.\n      // If all the axes scales are time or value.\n      coord.calcAffineTransform();\n    });\n    function adjustAxes() {\n      each(axesList, function (axis) {\n        var isHorizontal = axis.isHorizontal();\n        var extent = isHorizontal ? [0, gridRect.width] : [0, gridRect.height];\n        var idx = axis.inverse ? 1 : 0;\n        axis.setExtent(extent[idx], extent[1 - idx]);\n        updateAxisTransform(axis, isHorizontal ? gridRect.x : gridRect.y);\n      });\n    }\n  };\n  Grid.prototype.getAxis = function (dim, axisIndex) {\n    var axesMapOnDim = this._axesMap[dim];\n    if (axesMapOnDim != null) {\n      return axesMapOnDim[axisIndex || 0];\n    }\n  };\n  Grid.prototype.getAxes = function () {\n    return this._axesList.slice();\n  };\n  Grid.prototype.getCartesian = function (xAxisIndex, yAxisIndex) {\n    if (xAxisIndex != null && yAxisIndex != null) {\n      var key = 'x' + xAxisIndex + 'y' + yAxisIndex;\n      return this._coordsMap[key];\n    }\n    if (isObject(xAxisIndex)) {\n      yAxisIndex = xAxisIndex.yAxisIndex;\n      xAxisIndex = xAxisIndex.xAxisIndex;\n    }\n    for (var i = 0, coordList = this._coordsList; i < coordList.length; i++) {\n      if (coordList[i].getAxis('x').index === xAxisIndex || coordList[i].getAxis('y').index === yAxisIndex) {\n        return coordList[i];\n      }\n    }\n  };\n  Grid.prototype.getCartesians = function () {\n    return this._coordsList.slice();\n  };\n  /**\r\n   * @implements\r\n   */\n  Grid.prototype.convertToPixel = function (ecModel, finder, value) {\n    var target = this._findConvertTarget(finder);\n    return target.cartesian ? target.cartesian.dataToPoint(value) : target.axis ? target.axis.toGlobalCoord(target.axis.dataToCoord(value)) : null;\n  };\n  /**\r\n   * @implements\r\n   */\n  Grid.prototype.convertFromPixel = function (ecModel, finder, value) {\n    var target = this._findConvertTarget(finder);\n    return target.cartesian ? target.cartesian.pointToData(value) : target.axis ? target.axis.coordToData(target.axis.toLocalCoord(value)) : null;\n  };\n  Grid.prototype._findConvertTarget = function (finder) {\n    var seriesModel = finder.seriesModel;\n    var xAxisModel = finder.xAxisModel || seriesModel && seriesModel.getReferringComponents('xAxis', SINGLE_REFERRING).models[0];\n    var yAxisModel = finder.yAxisModel || seriesModel && seriesModel.getReferringComponents('yAxis', SINGLE_REFERRING).models[0];\n    var gridModel = finder.gridModel;\n    var coordsList = this._coordsList;\n    var cartesian;\n    var axis;\n    if (seriesModel) {\n      cartesian = seriesModel.coordinateSystem;\n      indexOf(coordsList, cartesian) < 0 && (cartesian = null);\n    } else if (xAxisModel && yAxisModel) {\n      cartesian = this.getCartesian(xAxisModel.componentIndex, yAxisModel.componentIndex);\n    } else if (xAxisModel) {\n      axis = this.getAxis('x', xAxisModel.componentIndex);\n    } else if (yAxisModel) {\n      axis = this.getAxis('y', yAxisModel.componentIndex);\n    }\n    // Lowest priority.\n    else if (gridModel) {\n      var grid = gridModel.coordinateSystem;\n      if (grid === this) {\n        cartesian = this._coordsList[0];\n      }\n    }\n    return {\n      cartesian: cartesian,\n      axis: axis\n    };\n  };\n  /**\r\n   * @implements\r\n   */\n  Grid.prototype.containPoint = function (point) {\n    var coord = this._coordsList[0];\n    if (coord) {\n      return coord.containPoint(point);\n    }\n  };\n  /**\r\n   * Initialize cartesian coordinate systems\r\n   */\n  Grid.prototype._initCartesian = function (gridModel, ecModel, api) {\n    var _this = this;\n    var grid = this;\n    var axisPositionUsed = {\n      left: false,\n      right: false,\n      top: false,\n      bottom: false\n    };\n    var axesMap = {\n      x: {},\n      y: {}\n    };\n    var axesCount = {\n      x: 0,\n      y: 0\n    };\n    // Create axis\n    ecModel.eachComponent('xAxis', createAxisCreator('x'), this);\n    ecModel.eachComponent('yAxis', createAxisCreator('y'), this);\n    if (!axesCount.x || !axesCount.y) {\n      // Roll back when there no either x or y axis\n      this._axesMap = {};\n      this._axesList = [];\n      return;\n    }\n    this._axesMap = axesMap;\n    // Create cartesian2d\n    each(axesMap.x, function (xAxis, xAxisIndex) {\n      each(axesMap.y, function (yAxis, yAxisIndex) {\n        var key = 'x' + xAxisIndex + 'y' + yAxisIndex;\n        var cartesian = new Cartesian2D(key);\n        cartesian.master = _this;\n        cartesian.model = gridModel;\n        _this._coordsMap[key] = cartesian;\n        _this._coordsList.push(cartesian);\n        cartesian.addAxis(xAxis);\n        cartesian.addAxis(yAxis);\n      });\n    });\n    function createAxisCreator(dimName) {\n      return function (axisModel, idx) {\n        if (!isAxisUsedInTheGrid(axisModel, gridModel)) {\n          return;\n        }\n        var axisPosition = axisModel.get('position');\n        if (dimName === 'x') {\n          // Fix position\n          if (axisPosition !== 'top' && axisPosition !== 'bottom') {\n            // Default bottom of X\n            axisPosition = axisPositionUsed.bottom ? 'top' : 'bottom';\n          }\n        } else {\n          // Fix position\n          if (axisPosition !== 'left' && axisPosition !== 'right') {\n            // Default left of Y\n            axisPosition = axisPositionUsed.left ? 'right' : 'left';\n          }\n        }\n        axisPositionUsed[axisPosition] = true;\n        var axis = new Axis2D(dimName, createScaleByModel(axisModel), [0, 0], axisModel.get('type'), axisPosition);\n        var isCategory = axis.type === 'category';\n        axis.onBand = isCategory && axisModel.get('boundaryGap');\n        axis.inverse = axisModel.get('inverse');\n        // Inject axis into axisModel\n        axisModel.axis = axis;\n        // Inject axisModel into axis\n        axis.model = axisModel;\n        // Inject grid info axis\n        axis.grid = grid;\n        // Index of axis, can be used as key\n        axis.index = idx;\n        grid._axesList.push(axis);\n        axesMap[dimName][idx] = axis;\n        axesCount[dimName]++;\n      };\n    }\n  };\n  /**\r\n   * Update cartesian properties from series.\r\n   */\n  Grid.prototype._updateScale = function (ecModel, gridModel) {\n    // Reset scale\n    each(this._axesList, function (axis) {\n      axis.scale.setExtent(Infinity, -Infinity);\n      if (axis.type === 'category') {\n        var categorySortInfo = axis.model.get('categorySortInfo');\n        axis.scale.setSortInfo(categorySortInfo);\n      }\n    });\n    ecModel.eachSeries(function (seriesModel) {\n      if (isCartesian2DSeries(seriesModel)) {\n        var axesModelMap = findAxisModels(seriesModel);\n        var xAxisModel = axesModelMap.xAxisModel;\n        var yAxisModel = axesModelMap.yAxisModel;\n        if (!isAxisUsedInTheGrid(xAxisModel, gridModel) || !isAxisUsedInTheGrid(yAxisModel, gridModel)) {\n          return;\n        }\n        var cartesian = this.getCartesian(xAxisModel.componentIndex, yAxisModel.componentIndex);\n        var data = seriesModel.getData();\n        var xAxis = cartesian.getAxis('x');\n        var yAxis = cartesian.getAxis('y');\n        unionExtent(data, xAxis);\n        unionExtent(data, yAxis);\n      }\n    }, this);\n    function unionExtent(data, axis) {\n      each(getDataDimensionsOnAxis(data, axis.dim), function (dim) {\n        axis.scale.unionExtentFromData(data, dim);\n      });\n    }\n  };\n  /**\r\n   * @param dim 'x' or 'y' or 'auto' or null/undefined\r\n   */\n  Grid.prototype.getTooltipAxes = function (dim) {\n    var baseAxes = [];\n    var otherAxes = [];\n    each(this.getCartesians(), function (cartesian) {\n      var baseAxis = dim != null && dim !== 'auto' ? cartesian.getAxis(dim) : cartesian.getBaseAxis();\n      var otherAxis = cartesian.getOtherAxis(baseAxis);\n      indexOf(baseAxes, baseAxis) < 0 && baseAxes.push(baseAxis);\n      indexOf(otherAxes, otherAxis) < 0 && otherAxes.push(otherAxis);\n    });\n    return {\n      baseAxes: baseAxes,\n      otherAxes: otherAxes\n    };\n  };\n  Grid.create = function (ecModel, api) {\n    var grids = [];\n    ecModel.eachComponent('grid', function (gridModel, idx) {\n      var grid = new Grid(gridModel, ecModel, api);\n      grid.name = 'grid_' + idx;\n      // dataSampling requires axis extent, so resize\n      // should be performed in create stage.\n      grid.resize(gridModel, api, true);\n      gridModel.coordinateSystem = grid;\n      grids.push(grid);\n    });\n    // Inject the coordinateSystems into seriesModel\n    ecModel.eachSeries(function (seriesModel) {\n      if (!isCartesian2DSeries(seriesModel)) {\n        return;\n      }\n      var axesModelMap = findAxisModels(seriesModel);\n      var xAxisModel = axesModelMap.xAxisModel;\n      var yAxisModel = axesModelMap.yAxisModel;\n      var gridModel = xAxisModel.getCoordSysModel();\n      if (process.env.NODE_ENV !== 'production') {\n        if (!gridModel) {\n          throw new Error('Grid \"' + retrieve3(xAxisModel.get('gridIndex'), xAxisModel.get('gridId'), 0) + '\" not found');\n        }\n        if (xAxisModel.getCoordSysModel() !== yAxisModel.getCoordSysModel()) {\n          throw new Error('xAxis and yAxis must use the same grid');\n        }\n      }\n      var grid = gridModel.coordinateSystem;\n      seriesModel.coordinateSystem = grid.getCartesian(xAxisModel.componentIndex, yAxisModel.componentIndex);\n    });\n    return grids;\n  };\n  // For deciding which dimensions to use when creating list data\n  Grid.dimensions = cartesian2DDimensions;\n  return Grid;\n}();\n/**\r\n * Check if the axis is used in the specified grid.\r\n */\nfunction isAxisUsedInTheGrid(axisModel, gridModel) {\n  return axisModel.getCoordSysModel() === gridModel;\n}\nfunction fixAxisOnZero(axesMap, otherAxisDim, axis,\n// Key: see `getOnZeroRecordKey`\nonZeroRecords) {\n  axis.getAxesOnZeroOf = function () {\n    // TODO: onZero of multiple axes.\n    return otherAxisOnZeroOf ? [otherAxisOnZeroOf] : [];\n  };\n  // onZero can not be enabled in these two situations:\n  // 1. When any other axis is a category axis.\n  // 2. When no axis is cross 0 point.\n  var otherAxes = axesMap[otherAxisDim];\n  var otherAxisOnZeroOf;\n  var axisModel = axis.model;\n  var onZero = axisModel.get(['axisLine', 'onZero']);\n  var onZeroAxisIndex = axisModel.get(['axisLine', 'onZeroAxisIndex']);\n  if (!onZero) {\n    return;\n  }\n  // If target axis is specified.\n  if (onZeroAxisIndex != null) {\n    if (canOnZeroToAxis(otherAxes[onZeroAxisIndex])) {\n      otherAxisOnZeroOf = otherAxes[onZeroAxisIndex];\n    }\n  } else {\n    // Find the first available other axis.\n    for (var idx in otherAxes) {\n      if (otherAxes.hasOwnProperty(idx) && canOnZeroToAxis(otherAxes[idx])\n      // Consider that two Y axes on one value axis,\n      // if both onZero, the two Y axes overlap.\n      && !onZeroRecords[getOnZeroRecordKey(otherAxes[idx])]) {\n        otherAxisOnZeroOf = otherAxes[idx];\n        break;\n      }\n    }\n  }\n  if (otherAxisOnZeroOf) {\n    onZeroRecords[getOnZeroRecordKey(otherAxisOnZeroOf)] = true;\n  }\n  function getOnZeroRecordKey(axis) {\n    return axis.dim + '_' + axis.index;\n  }\n}\nfunction canOnZeroToAxis(axis) {\n  return axis && axis.type !== 'category' && axis.type !== 'time' && ifAxisCrossZero(axis);\n}\nfunction updateAxisTransform(axis, coordBase) {\n  var axisExtent = axis.getExtent();\n  var axisExtentSum = axisExtent[0] + axisExtent[1];\n  // Fast transform\n  axis.toGlobalCoord = axis.dim === 'x' ? function (coord) {\n    return coord + coordBase;\n  } : function (coord) {\n    return axisExtentSum - coord + coordBase;\n  };\n  axis.toLocalCoord = axis.dim === 'x' ? function (coord) {\n    return coord - coordBase;\n  } : function (coord) {\n    return axisExtentSum - coord + coordBase;\n  };\n}\nexport default Grid;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,EAAEC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,IAAI,QAAQ,0BAA0B;AACnF,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,kBAAkB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,sBAAsB,EAAEC,uBAAuB,QAAQ,2BAA2B;AACjJ,OAAOC,WAAW,IAAIC,qBAAqB,QAAQ,kBAAkB;AACrE,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,0BAA0B;AAC9E,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,eAAe,QAAQ,sBAAsB;AACtD,IAAIC,IAAI,GAAG,aAAa,YAAY;EAClC,SAASA,IAAIA,CAACC,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACrC;IACA,IAAI,CAACC,IAAI,GAAG,MAAM;IAClB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,UAAU,GAAGjB,qBAAqB;IACvC,IAAI,CAACkB,cAAc,CAACV,SAAS,EAAEC,OAAO,EAAEC,GAAG,CAAC;IAC5C,IAAI,CAACS,KAAK,GAAGX,SAAS;EACxB;EACAD,IAAI,CAACa,SAAS,CAACC,OAAO,GAAG,YAAY;IACnC,OAAO,IAAI,CAACC,KAAK;EACnB,CAAC;EACDf,IAAI,CAACa,SAAS,CAACG,MAAM,GAAG,UAAUd,OAAO,EAAEC,GAAG,EAAE;IAC9C,IAAIc,OAAO,GAAG,IAAI,CAACV,QAAQ;IAC3B,IAAI,CAACW,YAAY,CAAChB,OAAO,EAAE,IAAI,CAACU,KAAK,CAAC;IACtC,SAASO,eAAeA,CAACC,IAAI,EAAE;MAC7B,IAAIC,OAAO;MACX;MACA,IAAIC,WAAW,GAAGrC,IAAI,CAACmC,IAAI,CAAC;MAC5B,IAAIG,GAAG,GAAGD,WAAW,CAACE,MAAM;MAC5B,IAAI,CAACD,GAAG,EAAE;QACR;MACF;MACA,IAAIE,cAAc,GAAG,EAAE;MACvB;MACA,KAAK,IAAIC,CAAC,GAAGH,GAAG,GAAG,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACjC,IAAIC,GAAG,GAAG,CAACL,WAAW,CAACI,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAIE,IAAI,GAAGR,IAAI,CAACO,GAAG,CAAC;QACpB,IAAIf,KAAK,GAAGgB,IAAI,CAAChB,KAAK;QACtB,IAAIiB,KAAK,GAAGD,IAAI,CAACC,KAAK;QACtB;QACA;QACA/B,oBAAoB,CAAC+B,KAAK,CAAC,IAAIjB,KAAK,CAACkB,GAAG,CAAC,YAAY,CAAC,IAAIlB,KAAK,CAACkB,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE;UACvFL,cAAc,CAACM,IAAI,CAACH,IAAI,CAAC;QAC3B,CAAC,MAAM;UACLvC,eAAe,CAACwC,KAAK,EAAEjB,KAAK,CAAC;UAC7B,IAAId,oBAAoB,CAAC+B,KAAK,CAAC,EAAE;YAC/B;YACAR,OAAO,GAAGO,IAAI;UAChB;QACF;MACF;MACA;MACA;MACA;MACA,IAAIH,cAAc,CAACD,MAAM,EAAE;QACzB,IAAI,CAACH,OAAO,EAAE;UACZA,OAAO,GAAGI,cAAc,CAACO,GAAG,CAAC,CAAC;UAC9B3C,eAAe,CAACgC,OAAO,CAACQ,KAAK,EAAER,OAAO,CAACT,KAAK,CAAC;QAC/C;QACA9B,IAAI,CAAC2C,cAAc,EAAE,UAAUG,IAAI,EAAE;UACnC7B,eAAe,CAAC6B,IAAI,CAACC,KAAK,EAAED,IAAI,CAAChB,KAAK,EAAES,OAAO,CAACQ,KAAK,CAAC;QACxD,CAAC,CAAC;MACJ;IACF;IACAV,eAAe,CAACF,OAAO,CAACgB,CAAC,CAAC;IAC1Bd,eAAe,CAACF,OAAO,CAACiB,CAAC,CAAC;IAC1B;IACA,IAAIC,aAAa,GAAG,CAAC,CAAC;IACtBrD,IAAI,CAACmC,OAAO,CAACgB,CAAC,EAAE,UAAUG,KAAK,EAAE;MAC/BC,aAAa,CAACpB,OAAO,EAAE,GAAG,EAAEmB,KAAK,EAAED,aAAa,CAAC;IACnD,CAAC,CAAC;IACFrD,IAAI,CAACmC,OAAO,CAACiB,CAAC,EAAE,UAAUI,KAAK,EAAE;MAC/BD,aAAa,CAACpB,OAAO,EAAE,GAAG,EAAEqB,KAAK,EAAEH,aAAa,CAAC;IACnD,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAACI,MAAM,CAAC,IAAI,CAAC3B,KAAK,EAAET,GAAG,CAAC;EAC9B,CAAC;EACD;AACF;AACA;EACEH,IAAI,CAACa,SAAS,CAAC0B,MAAM,GAAG,UAAUtC,SAAS,EAAEE,GAAG,EAAEqC,kBAAkB,EAAE;IACpE,IAAIC,eAAe,GAAGxC,SAAS,CAACyC,kBAAkB,CAAC,CAAC;IACpD,IAAIC,cAAc,GAAG,CAACH,kBAAkB,IAAIvC,SAAS,CAAC6B,GAAG,CAAC,cAAc,CAAC;IACzE,IAAIc,QAAQ,GAAG1D,aAAa,CAACuD,eAAe,EAAE;MAC5CI,KAAK,EAAE1C,GAAG,CAAC2C,QAAQ,CAAC,CAAC;MACrBC,MAAM,EAAE5C,GAAG,CAAC6C,SAAS,CAAC;IACxB,CAAC,CAAC;IACF,IAAI,CAACjC,KAAK,GAAG6B,QAAQ;IACrB,IAAIK,QAAQ,GAAG,IAAI,CAACzC,SAAS;IAC7B0C,UAAU,CAAC,CAAC;IACZ;IACA,IAAIP,cAAc,EAAE;MAClB7D,IAAI,CAACmE,QAAQ,EAAE,UAAUrB,IAAI,EAAE;QAC7B,IAAI,CAACA,IAAI,CAAChB,KAAK,CAACkB,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,EAAE;UAC5C,IAAIqB,cAAc,GAAG7D,sBAAsB,CAACsC,IAAI,CAAC;UACjD,IAAIuB,cAAc,EAAE;YAClB,IAAIC,GAAG,GAAGxB,IAAI,CAACyB,YAAY,CAAC,CAAC,GAAG,QAAQ,GAAG,OAAO;YAClD,IAAIC,MAAM,GAAG1B,IAAI,CAAChB,KAAK,CAACkB,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YACpDc,QAAQ,CAACQ,GAAG,CAAC,IAAID,cAAc,CAACC,GAAG,CAAC,GAAGE,MAAM;YAC7C,IAAI1B,IAAI,CAAC2B,QAAQ,KAAK,KAAK,EAAE;cAC3BX,QAAQ,CAACV,CAAC,IAAIiB,cAAc,CAACJ,MAAM,GAAGO,MAAM;YAC9C,CAAC,MAAM,IAAI1B,IAAI,CAAC2B,QAAQ,KAAK,MAAM,EAAE;cACnCX,QAAQ,CAACX,CAAC,IAAIkB,cAAc,CAACN,KAAK,GAAGS,MAAM;YAC7C;UACF;QACF;MACF,CAAC,CAAC;MACFJ,UAAU,CAAC,CAAC;IACd;IACApE,IAAI,CAAC,IAAI,CAACwB,WAAW,EAAE,UAAUkD,KAAK,EAAE;MACtC;MACA;MACAA,KAAK,CAACC,mBAAmB,CAAC,CAAC;IAC7B,CAAC,CAAC;IACF,SAASP,UAAUA,CAAA,EAAG;MACpBpE,IAAI,CAACmE,QAAQ,EAAE,UAAUrB,IAAI,EAAE;QAC7B,IAAIyB,YAAY,GAAGzB,IAAI,CAACyB,YAAY,CAAC,CAAC;QACtC,IAAIK,MAAM,GAAGL,YAAY,GAAG,CAAC,CAAC,EAAET,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAED,QAAQ,CAACG,MAAM,CAAC;QACtE,IAAIpB,GAAG,GAAGC,IAAI,CAAC+B,OAAO,GAAG,CAAC,GAAG,CAAC;QAC9B/B,IAAI,CAACgC,SAAS,CAACF,MAAM,CAAC/B,GAAG,CAAC,EAAE+B,MAAM,CAAC,CAAC,GAAG/B,GAAG,CAAC,CAAC;QAC5CkC,mBAAmB,CAACjC,IAAI,EAAEyB,YAAY,GAAGT,QAAQ,CAACX,CAAC,GAAGW,QAAQ,CAACV,CAAC,CAAC;MACnE,CAAC,CAAC;IACJ;EACF,CAAC;EACDlC,IAAI,CAACa,SAAS,CAACiD,OAAO,GAAG,UAAUV,GAAG,EAAEW,SAAS,EAAE;IACjD,IAAIC,YAAY,GAAG,IAAI,CAACzD,QAAQ,CAAC6C,GAAG,CAAC;IACrC,IAAIY,YAAY,IAAI,IAAI,EAAE;MACxB,OAAOA,YAAY,CAACD,SAAS,IAAI,CAAC,CAAC;IACrC;EACF,CAAC;EACD/D,IAAI,CAACa,SAAS,CAACoD,OAAO,GAAG,YAAY;IACnC,OAAO,IAAI,CAACzD,SAAS,CAAC0D,KAAK,CAAC,CAAC;EAC/B,CAAC;EACDlE,IAAI,CAACa,SAAS,CAACsD,YAAY,GAAG,UAAUC,UAAU,EAAEC,UAAU,EAAE;IAC9D,IAAID,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;MAC5C,IAAIC,GAAG,GAAG,GAAG,GAAGF,UAAU,GAAG,GAAG,GAAGC,UAAU;MAC7C,OAAO,IAAI,CAAChE,UAAU,CAACiE,GAAG,CAAC;IAC7B;IACA,IAAIzF,QAAQ,CAACuF,UAAU,CAAC,EAAE;MACxBC,UAAU,GAAGD,UAAU,CAACC,UAAU;MAClCD,UAAU,GAAGA,UAAU,CAACA,UAAU;IACpC;IACA,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAE6C,SAAS,GAAG,IAAI,CAACjE,WAAW,EAAEoB,CAAC,GAAG6C,SAAS,CAAC/C,MAAM,EAAEE,CAAC,EAAE,EAAE;MACvE,IAAI6C,SAAS,CAAC7C,CAAC,CAAC,CAACoC,OAAO,CAAC,GAAG,CAAC,CAACU,KAAK,KAAKJ,UAAU,IAAIG,SAAS,CAAC7C,CAAC,CAAC,CAACoC,OAAO,CAAC,GAAG,CAAC,CAACU,KAAK,KAAKH,UAAU,EAAE;QACpG,OAAOE,SAAS,CAAC7C,CAAC,CAAC;MACrB;IACF;EACF,CAAC;EACD1B,IAAI,CAACa,SAAS,CAAC4D,aAAa,GAAG,YAAY;IACzC,OAAO,IAAI,CAACnE,WAAW,CAAC4D,KAAK,CAAC,CAAC;EACjC,CAAC;EACD;AACF;AACA;EACElE,IAAI,CAACa,SAAS,CAAC6D,cAAc,GAAG,UAAUxE,OAAO,EAAEyE,MAAM,EAAEC,KAAK,EAAE;IAChE,IAAIC,MAAM,GAAG,IAAI,CAACC,kBAAkB,CAACH,MAAM,CAAC;IAC5C,OAAOE,MAAM,CAACE,SAAS,GAAGF,MAAM,CAACE,SAAS,CAACC,WAAW,CAACJ,KAAK,CAAC,GAAGC,MAAM,CAACjD,IAAI,GAAGiD,MAAM,CAACjD,IAAI,CAACqD,aAAa,CAACJ,MAAM,CAACjD,IAAI,CAACsD,WAAW,CAACN,KAAK,CAAC,CAAC,GAAG,IAAI;EAChJ,CAAC;EACD;AACF;AACA;EACE5E,IAAI,CAACa,SAAS,CAACsE,gBAAgB,GAAG,UAAUjF,OAAO,EAAEyE,MAAM,EAAEC,KAAK,EAAE;IAClE,IAAIC,MAAM,GAAG,IAAI,CAACC,kBAAkB,CAACH,MAAM,CAAC;IAC5C,OAAOE,MAAM,CAACE,SAAS,GAAGF,MAAM,CAACE,SAAS,CAACK,WAAW,CAACR,KAAK,CAAC,GAAGC,MAAM,CAACjD,IAAI,GAAGiD,MAAM,CAACjD,IAAI,CAACyD,WAAW,CAACR,MAAM,CAACjD,IAAI,CAAC0D,YAAY,CAACV,KAAK,CAAC,CAAC,GAAG,IAAI;EAC/I,CAAC;EACD5E,IAAI,CAACa,SAAS,CAACiE,kBAAkB,GAAG,UAAUH,MAAM,EAAE;IACpD,IAAIY,WAAW,GAAGZ,MAAM,CAACY,WAAW;IACpC,IAAIC,UAAU,GAAGb,MAAM,CAACa,UAAU,IAAID,WAAW,IAAIA,WAAW,CAACE,sBAAsB,CAAC,OAAO,EAAE9F,gBAAgB,CAAC,CAAC+F,MAAM,CAAC,CAAC,CAAC;IAC5H,IAAIC,UAAU,GAAGhB,MAAM,CAACgB,UAAU,IAAIJ,WAAW,IAAIA,WAAW,CAACE,sBAAsB,CAAC,OAAO,EAAE9F,gBAAgB,CAAC,CAAC+F,MAAM,CAAC,CAAC,CAAC;IAC5H,IAAIzF,SAAS,GAAG0E,MAAM,CAAC1E,SAAS;IAChC,IAAI2F,UAAU,GAAG,IAAI,CAACtF,WAAW;IACjC,IAAIyE,SAAS;IACb,IAAInD,IAAI;IACR,IAAI2D,WAAW,EAAE;MACfR,SAAS,GAAGQ,WAAW,CAACM,gBAAgB;MACxC9G,OAAO,CAAC6G,UAAU,EAAEb,SAAS,CAAC,GAAG,CAAC,KAAKA,SAAS,GAAG,IAAI,CAAC;IAC1D,CAAC,MAAM,IAAIS,UAAU,IAAIG,UAAU,EAAE;MACnCZ,SAAS,GAAG,IAAI,CAACZ,YAAY,CAACqB,UAAU,CAACM,cAAc,EAAEH,UAAU,CAACG,cAAc,CAAC;IACrF,CAAC,MAAM,IAAIN,UAAU,EAAE;MACrB5D,IAAI,GAAG,IAAI,CAACkC,OAAO,CAAC,GAAG,EAAE0B,UAAU,CAACM,cAAc,CAAC;IACrD,CAAC,MAAM,IAAIH,UAAU,EAAE;MACrB/D,IAAI,GAAG,IAAI,CAACkC,OAAO,CAAC,GAAG,EAAE6B,UAAU,CAACG,cAAc,CAAC;IACrD;IACA;IAAA,KACK,IAAI7F,SAAS,EAAE;MAClB,IAAI8F,IAAI,GAAG9F,SAAS,CAAC4F,gBAAgB;MACrC,IAAIE,IAAI,KAAK,IAAI,EAAE;QACjBhB,SAAS,GAAG,IAAI,CAACzE,WAAW,CAAC,CAAC,CAAC;MACjC;IACF;IACA,OAAO;MACLyE,SAAS,EAAEA,SAAS;MACpBnD,IAAI,EAAEA;IACR,CAAC;EACH,CAAC;EACD;AACF;AACA;EACE5B,IAAI,CAACa,SAAS,CAACmF,YAAY,GAAG,UAAUC,KAAK,EAAE;IAC7C,IAAIzC,KAAK,GAAG,IAAI,CAAClD,WAAW,CAAC,CAAC,CAAC;IAC/B,IAAIkD,KAAK,EAAE;MACT,OAAOA,KAAK,CAACwC,YAAY,CAACC,KAAK,CAAC;IAClC;EACF,CAAC;EACD;AACF;AACA;EACEjG,IAAI,CAACa,SAAS,CAACF,cAAc,GAAG,UAAUV,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACjE,IAAI+F,KAAK,GAAG,IAAI;IAChB,IAAIH,IAAI,GAAG,IAAI;IACf,IAAII,gBAAgB,GAAG;MACrBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,KAAK;MACVC,MAAM,EAAE;IACV,CAAC;IACD,IAAItF,OAAO,GAAG;MACZgB,CAAC,EAAE,CAAC,CAAC;MACLC,CAAC,EAAE,CAAC;IACN,CAAC;IACD,IAAIsE,SAAS,GAAG;MACdvE,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;IACD;IACAhC,OAAO,CAACuG,aAAa,CAAC,OAAO,EAAEC,iBAAiB,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;IAC5DxG,OAAO,CAACuG,aAAa,CAAC,OAAO,EAAEC,iBAAiB,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;IAC5D,IAAI,CAACF,SAAS,CAACvE,CAAC,IAAI,CAACuE,SAAS,CAACtE,CAAC,EAAE;MAChC;MACA,IAAI,CAAC3B,QAAQ,GAAG,CAAC,CAAC;MAClB,IAAI,CAACC,SAAS,GAAG,EAAE;MACnB;IACF;IACA,IAAI,CAACD,QAAQ,GAAGU,OAAO;IACvB;IACAnC,IAAI,CAACmC,OAAO,CAACgB,CAAC,EAAE,UAAUG,KAAK,EAAEgC,UAAU,EAAE;MAC3CtF,IAAI,CAACmC,OAAO,CAACiB,CAAC,EAAE,UAAUI,KAAK,EAAE+B,UAAU,EAAE;QAC3C,IAAIC,GAAG,GAAG,GAAG,GAAGF,UAAU,GAAG,GAAG,GAAGC,UAAU;QAC7C,IAAIU,SAAS,GAAG,IAAIvF,WAAW,CAAC8E,GAAG,CAAC;QACpCS,SAAS,CAAC4B,MAAM,GAAGT,KAAK;QACxBnB,SAAS,CAACnE,KAAK,GAAGX,SAAS;QAC3BiG,KAAK,CAAC7F,UAAU,CAACiE,GAAG,CAAC,GAAGS,SAAS;QACjCmB,KAAK,CAAC5F,WAAW,CAACyB,IAAI,CAACgD,SAAS,CAAC;QACjCA,SAAS,CAAC6B,OAAO,CAACxE,KAAK,CAAC;QACxB2C,SAAS,CAAC6B,OAAO,CAACtE,KAAK,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,SAASoE,iBAAiBA,CAACG,OAAO,EAAE;MAClC,OAAO,UAAUC,SAAS,EAAEnF,GAAG,EAAE;QAC/B,IAAI,CAACoF,mBAAmB,CAACD,SAAS,EAAE7G,SAAS,CAAC,EAAE;UAC9C;QACF;QACA,IAAI+G,YAAY,GAAGF,SAAS,CAAChF,GAAG,CAAC,UAAU,CAAC;QAC5C,IAAI+E,OAAO,KAAK,GAAG,EAAE;UACnB;UACA,IAAIG,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,QAAQ,EAAE;YACvD;YACAA,YAAY,GAAGb,gBAAgB,CAACI,MAAM,GAAG,KAAK,GAAG,QAAQ;UAC3D;QACF,CAAC,MAAM;UACL;UACA,IAAIS,YAAY,KAAK,MAAM,IAAIA,YAAY,KAAK,OAAO,EAAE;YACvD;YACAA,YAAY,GAAGb,gBAAgB,CAACC,IAAI,GAAG,OAAO,GAAG,MAAM;UACzD;QACF;QACAD,gBAAgB,CAACa,YAAY,CAAC,GAAG,IAAI;QACrC,IAAIpF,IAAI,GAAG,IAAIlC,MAAM,CAACmH,OAAO,EAAE1H,kBAAkB,CAAC2H,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEA,SAAS,CAAChF,GAAG,CAAC,MAAM,CAAC,EAAEkF,YAAY,CAAC;QAC1G,IAAIC,UAAU,GAAGrF,IAAI,CAACxB,IAAI,KAAK,UAAU;QACzCwB,IAAI,CAACsF,MAAM,GAAGD,UAAU,IAAIH,SAAS,CAAChF,GAAG,CAAC,aAAa,CAAC;QACxDF,IAAI,CAAC+B,OAAO,GAAGmD,SAAS,CAAChF,GAAG,CAAC,SAAS,CAAC;QACvC;QACAgF,SAAS,CAAClF,IAAI,GAAGA,IAAI;QACrB;QACAA,IAAI,CAAChB,KAAK,GAAGkG,SAAS;QACtB;QACAlF,IAAI,CAACmE,IAAI,GAAGA,IAAI;QAChB;QACAnE,IAAI,CAAC4C,KAAK,GAAG7C,GAAG;QAChBoE,IAAI,CAACvF,SAAS,CAACuB,IAAI,CAACH,IAAI,CAAC;QACzBX,OAAO,CAAC4F,OAAO,CAAC,CAAClF,GAAG,CAAC,GAAGC,IAAI;QAC5B4E,SAAS,CAACK,OAAO,CAAC,EAAE;MACtB,CAAC;IACH;EACF,CAAC;EACD;AACF;AACA;EACE7G,IAAI,CAACa,SAAS,CAACK,YAAY,GAAG,UAAUhB,OAAO,EAAED,SAAS,EAAE;IAC1D;IACAnB,IAAI,CAAC,IAAI,CAAC0B,SAAS,EAAE,UAAUoB,IAAI,EAAE;MACnCA,IAAI,CAACC,KAAK,CAAC+B,SAAS,CAACuD,QAAQ,EAAE,CAACA,QAAQ,CAAC;MACzC,IAAIvF,IAAI,CAACxB,IAAI,KAAK,UAAU,EAAE;QAC5B,IAAIgH,gBAAgB,GAAGxF,IAAI,CAAChB,KAAK,CAACkB,GAAG,CAAC,kBAAkB,CAAC;QACzDF,IAAI,CAACC,KAAK,CAACwF,WAAW,CAACD,gBAAgB,CAAC;MAC1C;IACF,CAAC,CAAC;IACFlH,OAAO,CAACoH,UAAU,CAAC,UAAU/B,WAAW,EAAE;MACxC,IAAI3F,mBAAmB,CAAC2F,WAAW,CAAC,EAAE;QACpC,IAAIgC,YAAY,GAAG1H,cAAc,CAAC0F,WAAW,CAAC;QAC9C,IAAIC,UAAU,GAAG+B,YAAY,CAAC/B,UAAU;QACxC,IAAIG,UAAU,GAAG4B,YAAY,CAAC5B,UAAU;QACxC,IAAI,CAACoB,mBAAmB,CAACvB,UAAU,EAAEvF,SAAS,CAAC,IAAI,CAAC8G,mBAAmB,CAACpB,UAAU,EAAE1F,SAAS,CAAC,EAAE;UAC9F;QACF;QACA,IAAI8E,SAAS,GAAG,IAAI,CAACZ,YAAY,CAACqB,UAAU,CAACM,cAAc,EAAEH,UAAU,CAACG,cAAc,CAAC;QACvF,IAAI0B,IAAI,GAAGjC,WAAW,CAACkC,OAAO,CAAC,CAAC;QAChC,IAAIrF,KAAK,GAAG2C,SAAS,CAACjB,OAAO,CAAC,GAAG,CAAC;QAClC,IAAIxB,KAAK,GAAGyC,SAAS,CAACjB,OAAO,CAAC,GAAG,CAAC;QAClC4D,WAAW,CAACF,IAAI,EAAEpF,KAAK,CAAC;QACxBsF,WAAW,CAACF,IAAI,EAAElF,KAAK,CAAC;MAC1B;IACF,CAAC,EAAE,IAAI,CAAC;IACR,SAASoF,WAAWA,CAACF,IAAI,EAAE5F,IAAI,EAAE;MAC/B9C,IAAI,CAACS,uBAAuB,CAACiI,IAAI,EAAE5F,IAAI,CAACwB,GAAG,CAAC,EAAE,UAAUA,GAAG,EAAE;QAC3DxB,IAAI,CAACC,KAAK,CAAC8F,mBAAmB,CAACH,IAAI,EAAEpE,GAAG,CAAC;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;EACD;AACF;AACA;EACEpD,IAAI,CAACa,SAAS,CAAC+G,cAAc,GAAG,UAAUxE,GAAG,EAAE;IAC7C,IAAIyE,QAAQ,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,EAAE;IAClBhJ,IAAI,CAAC,IAAI,CAAC2F,aAAa,CAAC,CAAC,EAAE,UAAUM,SAAS,EAAE;MAC9C,IAAIgD,QAAQ,GAAG3E,GAAG,IAAI,IAAI,IAAIA,GAAG,KAAK,MAAM,GAAG2B,SAAS,CAACjB,OAAO,CAACV,GAAG,CAAC,GAAG2B,SAAS,CAACiD,WAAW,CAAC,CAAC;MAC/F,IAAIC,SAAS,GAAGlD,SAAS,CAACmD,YAAY,CAACH,QAAQ,CAAC;MAChDhJ,OAAO,CAAC8I,QAAQ,EAAEE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAAC9F,IAAI,CAACgG,QAAQ,CAAC;MAC1DhJ,OAAO,CAAC+I,SAAS,EAAEG,SAAS,CAAC,GAAG,CAAC,IAAIH,SAAS,CAAC/F,IAAI,CAACkG,SAAS,CAAC;IAChE,CAAC,CAAC;IACF,OAAO;MACLJ,QAAQ,EAAEA,QAAQ;MAClBC,SAAS,EAAEA;IACb,CAAC;EACH,CAAC;EACD9H,IAAI,CAACmI,MAAM,GAAG,UAAUjI,OAAO,EAAEC,GAAG,EAAE;IACpC,IAAIiI,KAAK,GAAG,EAAE;IACdlI,OAAO,CAACuG,aAAa,CAAC,MAAM,EAAE,UAAUxG,SAAS,EAAE0B,GAAG,EAAE;MACtD,IAAIoE,IAAI,GAAG,IAAI/F,IAAI,CAACC,SAAS,EAAEC,OAAO,EAAEC,GAAG,CAAC;MAC5C4F,IAAI,CAACsC,IAAI,GAAG,OAAO,GAAG1G,GAAG;MACzB;MACA;MACAoE,IAAI,CAACxD,MAAM,CAACtC,SAAS,EAAEE,GAAG,EAAE,IAAI,CAAC;MACjCF,SAAS,CAAC4F,gBAAgB,GAAGE,IAAI;MACjCqC,KAAK,CAACrG,IAAI,CAACgE,IAAI,CAAC;IAClB,CAAC,CAAC;IACF;IACA7F,OAAO,CAACoH,UAAU,CAAC,UAAU/B,WAAW,EAAE;MACxC,IAAI,CAAC3F,mBAAmB,CAAC2F,WAAW,CAAC,EAAE;QACrC;MACF;MACA,IAAIgC,YAAY,GAAG1H,cAAc,CAAC0F,WAAW,CAAC;MAC9C,IAAIC,UAAU,GAAG+B,YAAY,CAAC/B,UAAU;MACxC,IAAIG,UAAU,GAAG4B,YAAY,CAAC5B,UAAU;MACxC,IAAI1F,SAAS,GAAGuF,UAAU,CAAC8C,gBAAgB,CAAC,CAAC;MAC7C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI,CAACxI,SAAS,EAAE;UACd,MAAM,IAAIyI,KAAK,CAAC,QAAQ,GAAG1J,SAAS,CAACwG,UAAU,CAAC1D,GAAG,CAAC,WAAW,CAAC,EAAE0D,UAAU,CAAC1D,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC;QACjH;QACA,IAAI0D,UAAU,CAAC8C,gBAAgB,CAAC,CAAC,KAAK3C,UAAU,CAAC2C,gBAAgB,CAAC,CAAC,EAAE;UACnE,MAAM,IAAII,KAAK,CAAC,wCAAwC,CAAC;QAC3D;MACF;MACA,IAAI3C,IAAI,GAAG9F,SAAS,CAAC4F,gBAAgB;MACrCN,WAAW,CAACM,gBAAgB,GAAGE,IAAI,CAAC5B,YAAY,CAACqB,UAAU,CAACM,cAAc,EAAEH,UAAU,CAACG,cAAc,CAAC;IACxG,CAAC,CAAC;IACF,OAAOsC,KAAK;EACd,CAAC;EACD;EACApI,IAAI,CAACU,UAAU,GAAGjB,qBAAqB;EACvC,OAAOO,IAAI;AACb,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA,SAAS+G,mBAAmBA,CAACD,SAAS,EAAE7G,SAAS,EAAE;EACjD,OAAO6G,SAAS,CAACwB,gBAAgB,CAAC,CAAC,KAAKrI,SAAS;AACnD;AACA,SAASoC,aAAaA,CAACpB,OAAO,EAAE0H,YAAY,EAAE/G,IAAI;AAClD;AACAO,aAAa,EAAE;EACbP,IAAI,CAACgH,eAAe,GAAG,YAAY;IACjC;IACA,OAAOC,iBAAiB,GAAG,CAACA,iBAAiB,CAAC,GAAG,EAAE;EACrD,CAAC;EACD;EACA;EACA;EACA,IAAIf,SAAS,GAAG7G,OAAO,CAAC0H,YAAY,CAAC;EACrC,IAAIE,iBAAiB;EACrB,IAAI/B,SAAS,GAAGlF,IAAI,CAAChB,KAAK;EAC1B,IAAIkI,MAAM,GAAGhC,SAAS,CAAChF,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;EAClD,IAAIiH,eAAe,GAAGjC,SAAS,CAAChF,GAAG,CAAC,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;EACpE,IAAI,CAACgH,MAAM,EAAE;IACX;EACF;EACA;EACA,IAAIC,eAAe,IAAI,IAAI,EAAE;IAC3B,IAAIC,eAAe,CAAClB,SAAS,CAACiB,eAAe,CAAC,CAAC,EAAE;MAC/CF,iBAAiB,GAAGf,SAAS,CAACiB,eAAe,CAAC;IAChD;EACF,CAAC,MAAM;IACL;IACA,KAAK,IAAIpH,GAAG,IAAImG,SAAS,EAAE;MACzB,IAAIA,SAAS,CAACmB,cAAc,CAACtH,GAAG,CAAC,IAAIqH,eAAe,CAAClB,SAAS,CAACnG,GAAG,CAAC;MACnE;MACA;MAAA,GACG,CAACQ,aAAa,CAAC+G,kBAAkB,CAACpB,SAAS,CAACnG,GAAG,CAAC,CAAC,CAAC,EAAE;QACrDkH,iBAAiB,GAAGf,SAAS,CAACnG,GAAG,CAAC;QAClC;MACF;IACF;EACF;EACA,IAAIkH,iBAAiB,EAAE;IACrB1G,aAAa,CAAC+G,kBAAkB,CAACL,iBAAiB,CAAC,CAAC,GAAG,IAAI;EAC7D;EACA,SAASK,kBAAkBA,CAACtH,IAAI,EAAE;IAChC,OAAOA,IAAI,CAACwB,GAAG,GAAG,GAAG,GAAGxB,IAAI,CAAC4C,KAAK;EACpC;AACF;AACA,SAASwE,eAAeA,CAACpH,IAAI,EAAE;EAC7B,OAAOA,IAAI,IAAIA,IAAI,CAACxB,IAAI,KAAK,UAAU,IAAIwB,IAAI,CAACxB,IAAI,KAAK,MAAM,IAAIhB,eAAe,CAACwC,IAAI,CAAC;AAC1F;AACA,SAASiC,mBAAmBA,CAACjC,IAAI,EAAEuH,SAAS,EAAE;EAC5C,IAAIC,UAAU,GAAGxH,IAAI,CAACyH,SAAS,CAAC,CAAC;EACjC,IAAIC,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;EACjD;EACAxH,IAAI,CAACqD,aAAa,GAAGrD,IAAI,CAACwB,GAAG,KAAK,GAAG,GAAG,UAAUI,KAAK,EAAE;IACvD,OAAOA,KAAK,GAAG2F,SAAS;EAC1B,CAAC,GAAG,UAAU3F,KAAK,EAAE;IACnB,OAAO8F,aAAa,GAAG9F,KAAK,GAAG2F,SAAS;EAC1C,CAAC;EACDvH,IAAI,CAAC0D,YAAY,GAAG1D,IAAI,CAACwB,GAAG,KAAK,GAAG,GAAG,UAAUI,KAAK,EAAE;IACtD,OAAOA,KAAK,GAAG2F,SAAS;EAC1B,CAAC,GAAG,UAAU3F,KAAK,EAAE;IACnB,OAAO8F,aAAa,GAAG9F,KAAK,GAAG2F,SAAS;EAC1C,CAAC;AACH;AACA,eAAenJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}