{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { NbCardModule } from '@nebular/theme';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nconst _c0 = () => [\"\\u7CFB\\u7D71\\u7BA1\\u7406\", \"\\u6A21\\u677F\\u7BA1\\u7406\"];\nexport class TemplateComponent extends BaseComponent {\n  constructor(allow) {\n    super(allow);\n    this.allow = allow;\n  }\n  ngOnInit() {\n    // Initialize component\n  }\n  static {\n    this.ɵfac = function TemplateComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateComponent,\n      selectors: [[\"ngx-template\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 2,\n      consts: [[3, \"menuList\"]],\n      template: function TemplateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\", 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h4\");\n          i0.ɵɵtext(5, \"\\u6A21\\u677F\\u7BA1\\u7406\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\");\n          i0.ɵɵtext(7, \"\\u9019\\u662F\\u6A21\\u677F\\u7BA1\\u7406\\u9801\\u9762\\u7684\\u5167\\u5BB9\\u3002\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"menuList\", i0.ɵɵpureFunction0(1, _c0));\n        }\n      },\n      dependencies: [NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardHeaderComponent, BreadcrumbComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJ0ZW1wbGF0ZS5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc3lzdGVtLW1hbmFnZW1lbnQvdGVtcGxhdGUvdGVtcGxhdGUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLG9LQUFvSyIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "NbCardModule", "BreadcrumbComponent", "TemplateComponent", "constructor", "allow", "ngOnInit", "i0", "ɵɵdirectiveInject", "i1", "AllowHelper", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TemplateComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "i2", "NbCardComponent", "NbCardBodyComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.html"], "sourcesContent": ["import { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { NbCardModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\n\r\n@Component({\r\n  selector: 'ngx-template',\r\n  templateUrl: './template.component.html',\r\n  styleUrls: ['./template.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent\r\n  ],\r\n})\r\nexport class TemplateComponent extends BaseComponent implements OnInit {\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper\r\n  ) {\r\n    super(allow);\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    // Initialize component\r\n  }\r\n}", "<nb-card>\r\n  <nb-card-header>\r\n    <ngx-breadcrumb [menuList]=\"['系統管理', '模板管理']\"></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h4>模板管理</h4>\r\n    <p>這是模板管理頁面的內容。</p>\r\n  </nb-card-body>\r\n</nb-card>"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qCAAqC;AAEnE,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,SAASC,mBAAmB,QAAQ,kDAAkD;;;;;AAYtF,OAAM,MAAOC,iBAAkB,SAAQH,aAAa;EAElDI,YACqBC,KAAkB;IAErC,KAAK,CAACA,KAAK,CAAC;IAFO,KAAAA,KAAK,GAALA,KAAK;EAG1B;EAESC,QAAQA,CAAA;IACf;EAAA;;;uCATSH,iBAAiB,EAAAI,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjBP,iBAAiB;MAAAQ,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,0BAAA,EAAAP,EAAA,CAAAQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCf5Bd,EADF,CAAAgB,cAAA,cAAS,qBACS;UACdhB,EAAA,CAAAiB,SAAA,wBAA+D;UACjEjB,EAAA,CAAAkB,YAAA,EAAiB;UAEflB,EADF,CAAAgB,cAAA,mBAAc,SACR;UAAAhB,EAAA,CAAAmB,MAAA,+BAAI;UAAAnB,EAAA,CAAAkB,YAAA,EAAK;UACblB,EAAA,CAAAgB,cAAA,QAAG;UAAAhB,EAAA,CAAAmB,MAAA,+EAAY;UAEnBnB,EAFmB,CAAAkB,YAAA,EAAI,EACN,EACP;;;UANUlB,EAAA,CAAAoB,SAAA,GAA6B;UAA7BpB,EAAA,CAAAqB,UAAA,aAAArB,EAAA,CAAAsB,eAAA,IAAAC,GAAA,EAA6B;;;qBDU7C7B,YAAY,EAAA8B,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EACZhC,mBAAmB;MAAAiC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}