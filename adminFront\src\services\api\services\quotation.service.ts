/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { apiQuotationGetDataPost$Json } from '../fn/quotation/api-quotation-get-data-post-json';
import { ApiQuotationGetDataPost$Json$Params } from '../fn/quotation/api-quotation-get-data-post-json';
import { apiQuotationGetDataPost$Plain } from '../fn/quotation/api-quotation-get-data-post-plain';
import { ApiQuotationGetDataPost$Plain$Params } from '../fn/quotation/api-quotation-get-data-post-plain';
import { apiQuotationGetListByHouseIdPost$Json } from '../fn/quotation/api-quotation-get-list-by-house-id-post-json';
import { ApiQuotationGetListByHouseIdPost$Json$Params } from '../fn/quotation/api-quotation-get-list-by-house-id-post-json';
import { apiQuotationGetListByHouseIdPost$Plain } from '../fn/quotation/api-quotation-get-list-by-house-id-post-plain';
import { ApiQuotationGetListByHouseIdPost$Plain$Params } from '../fn/quotation/api-quotation-get-list-by-house-id-post-plain';
import { apiQuotationGetListPost$Json } from '../fn/quotation/api-quotation-get-list-post-json';
import { ApiQuotationGetListPost$Json$Params } from '../fn/quotation/api-quotation-get-list-post-json';
import { apiQuotationGetListPost$Plain } from '../fn/quotation/api-quotation-get-list-post-plain';
import { ApiQuotationGetListPost$Plain$Params } from '../fn/quotation/api-quotation-get-list-post-plain';
import { apiQuotationGetQuotationHistoryPost$Json } from '../fn/quotation/api-quotation-get-quotation-history-post-json';
import { ApiQuotationGetQuotationHistoryPost$Json$Params } from '../fn/quotation/api-quotation-get-quotation-history-post-json';
import { apiQuotationGetQuotationHistoryPost$Plain } from '../fn/quotation/api-quotation-get-quotation-history-post-plain';
import { ApiQuotationGetQuotationHistoryPost$Plain$Params } from '../fn/quotation/api-quotation-get-quotation-history-post-plain';
import { apiQuotationLoadDefaultItemsPost$Json } from '../fn/quotation/api-quotation-load-default-items-post-json';
import { ApiQuotationLoadDefaultItemsPost$Json$Params } from '../fn/quotation/api-quotation-load-default-items-post-json';
import { apiQuotationLoadDefaultItemsPost$Plain } from '../fn/quotation/api-quotation-load-default-items-post-plain';
import { ApiQuotationLoadDefaultItemsPost$Plain$Params } from '../fn/quotation/api-quotation-load-default-items-post-plain';
import { apiQuotationLoadRegularItemsPost$Json } from '../fn/quotation/api-quotation-load-regular-items-post-json';
import { ApiQuotationLoadRegularItemsPost$Json$Params } from '../fn/quotation/api-quotation-load-regular-items-post-json';
import { apiQuotationLoadRegularItemsPost$Plain } from '../fn/quotation/api-quotation-load-regular-items-post-plain';
import { ApiQuotationLoadRegularItemsPost$Plain$Params } from '../fn/quotation/api-quotation-load-regular-items-post-plain';
import { apiQuotationLockQuotationPost$Json } from '../fn/quotation/api-quotation-lock-quotation-post-json';
import { ApiQuotationLockQuotationPost$Json$Params } from '../fn/quotation/api-quotation-lock-quotation-post-json';
import { apiQuotationLockQuotationPost$Plain } from '../fn/quotation/api-quotation-lock-quotation-post-plain';
import { ApiQuotationLockQuotationPost$Plain$Params } from '../fn/quotation/api-quotation-lock-quotation-post-plain';
import { apiQuotationSaveDataPost$Json } from '../fn/quotation/api-quotation-save-data-post-json';
import { ApiQuotationSaveDataPost$Json$Params } from '../fn/quotation/api-quotation-save-data-post-json';
import { apiQuotationSaveDataPost$Plain } from '../fn/quotation/api-quotation-save-data-post-plain';
import { ApiQuotationSaveDataPost$Plain$Params } from '../fn/quotation/api-quotation-save-data-post-plain';
import { apiQuotationSignQuotationPost$Json } from '../fn/quotation/api-quotation-sign-quotation-post-json';
import { ApiQuotationSignQuotationPost$Json$Params } from '../fn/quotation/api-quotation-sign-quotation-post-json';
import { apiQuotationSignQuotationPost$Plain } from '../fn/quotation/api-quotation-sign-quotation-post-plain';
import { ApiQuotationSignQuotationPost$Plain$Params } from '../fn/quotation/api-quotation-sign-quotation-post-plain';
import { GetQuotationListResponseBase } from '../models/get-quotation-list-response-base';
import { GetQuotationResponseBase } from '../models/get-quotation-response-base';
import { GetQuotationVersionsListResponseBase } from '../models/get-quotation-versions-list-response-base';
import { SaveDataQuotationResponseBase } from '../models/save-data-quotation-response-base';
import { StringResponseBase } from '../models/string-response-base';

@Injectable({ providedIn: 'root' })
export class QuotationService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `apiQuotationGetListPost()` */
  static readonly ApiQuotationGetListPostPath = '/api/Quotation/GetList';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationGetListPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListPost$Plain$Response(params?: ApiQuotationGetListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {
    return apiQuotationGetListPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationGetListPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListPost$Plain(params?: ApiQuotationGetListPost$Plain$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {
    return this.apiQuotationGetListPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationGetListPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListPost$Json$Response(params?: ApiQuotationGetListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {
    return apiQuotationGetListPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationGetListPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListPost$Json(params?: ApiQuotationGetListPost$Json$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {
    return this.apiQuotationGetListPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationGetDataPost()` */
  static readonly ApiQuotationGetDataPostPath = '/api/Quotation/GetData';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationGetDataPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetDataPost$Plain$Response(params?: ApiQuotationGetDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationResponseBase>> {
    return apiQuotationGetDataPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationGetDataPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetDataPost$Plain(params?: ApiQuotationGetDataPost$Plain$Params, context?: HttpContext): Observable<GetQuotationResponseBase> {
    return this.apiQuotationGetDataPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationResponseBase>): GetQuotationResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationGetDataPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetDataPost$Json$Response(params?: ApiQuotationGetDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationResponseBase>> {
    return apiQuotationGetDataPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationGetDataPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetDataPost$Json(params?: ApiQuotationGetDataPost$Json$Params, context?: HttpContext): Observable<GetQuotationResponseBase> {
    return this.apiQuotationGetDataPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationResponseBase>): GetQuotationResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationSaveDataPost()` */
  static readonly ApiQuotationSaveDataPostPath = '/api/Quotation/SaveData';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationSaveDataPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationSaveDataPost$Plain$Response(params?: ApiQuotationSaveDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiQuotationSaveDataPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationSaveDataPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationSaveDataPost$Plain(params?: ApiQuotationSaveDataPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiQuotationSaveDataPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationSaveDataPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationSaveDataPost$Json$Response(params?: ApiQuotationSaveDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiQuotationSaveDataPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationSaveDataPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationSaveDataPost$Json(params?: ApiQuotationSaveDataPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiQuotationSaveDataPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationGetListByHouseIdPost()` */
  static readonly ApiQuotationGetListByHouseIdPostPath = '/api/Quotation/GetListByHouseID';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationGetListByHouseIdPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListByHouseIdPost$Plain$Response(params?: ApiQuotationGetListByHouseIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<SaveDataQuotationResponseBase>> {
    return apiQuotationGetListByHouseIdPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationGetListByHouseIdPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListByHouseIdPost$Plain(params?: ApiQuotationGetListByHouseIdPost$Plain$Params, context?: HttpContext): Observable<SaveDataQuotationResponseBase> {
    return this.apiQuotationGetListByHouseIdPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<SaveDataQuotationResponseBase>): SaveDataQuotationResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationGetListByHouseIdPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListByHouseIdPost$Json$Response(params?: ApiQuotationGetListByHouseIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<SaveDataQuotationResponseBase>> {
    return apiQuotationGetListByHouseIdPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationGetListByHouseIdPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationGetListByHouseIdPost$Json(params?: ApiQuotationGetListByHouseIdPost$Json$Params, context?: HttpContext): Observable<SaveDataQuotationResponseBase> {
    return this.apiQuotationGetListByHouseIdPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<SaveDataQuotationResponseBase>): SaveDataQuotationResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationLoadDefaultItemsPost()` */
  static readonly ApiQuotationLoadDefaultItemsPostPath = '/api/Quotation/LoadDefaultItems';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationLoadDefaultItemsPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLoadDefaultItemsPost$Plain$Response(params?: ApiQuotationLoadDefaultItemsPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {
    return apiQuotationLoadDefaultItemsPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationLoadDefaultItemsPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLoadDefaultItemsPost$Plain(params?: ApiQuotationLoadDefaultItemsPost$Plain$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {
    return this.apiQuotationLoadDefaultItemsPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationLoadDefaultItemsPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLoadDefaultItemsPost$Json$Response(params?: ApiQuotationLoadDefaultItemsPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {
    return apiQuotationLoadDefaultItemsPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationLoadDefaultItemsPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLoadDefaultItemsPost$Json(params?: ApiQuotationLoadDefaultItemsPost$Json$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {
    return this.apiQuotationLoadDefaultItemsPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationLoadRegularItemsPost()` */
  static readonly ApiQuotationLoadRegularItemsPostPath = '/api/Quotation/LoadRegularItems';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationLoadRegularItemsPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLoadRegularItemsPost$Plain$Response(params?: ApiQuotationLoadRegularItemsPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {
    return apiQuotationLoadRegularItemsPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationLoadRegularItemsPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLoadRegularItemsPost$Plain(params?: ApiQuotationLoadRegularItemsPost$Plain$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {
    return this.apiQuotationLoadRegularItemsPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationLoadRegularItemsPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLoadRegularItemsPost$Json$Response(params?: ApiQuotationLoadRegularItemsPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {
    return apiQuotationLoadRegularItemsPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationLoadRegularItemsPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLoadRegularItemsPost$Json(params?: ApiQuotationLoadRegularItemsPost$Json$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {
    return this.apiQuotationLoadRegularItemsPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationLockQuotationPost()` */
  static readonly ApiQuotationLockQuotationPostPath = '/api/Quotation/LockQuotation';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationLockQuotationPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLockQuotationPost$Plain$Response(params?: ApiQuotationLockQuotationPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiQuotationLockQuotationPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationLockQuotationPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLockQuotationPost$Plain(params?: ApiQuotationLockQuotationPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiQuotationLockQuotationPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationLockQuotationPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLockQuotationPost$Json$Response(params?: ApiQuotationLockQuotationPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiQuotationLockQuotationPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationLockQuotationPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationLockQuotationPost$Json(params?: ApiQuotationLockQuotationPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiQuotationLockQuotationPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationGetQuotationHistoryPost()` */
  static readonly ApiQuotationGetQuotationHistoryPostPath = '/api/Quotation/GetQuotationHistory';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationGetQuotationHistoryPost$Plain()` instead.
   *
   * This method doesn't expect any request body.
   */
  apiQuotationGetQuotationHistoryPost$Plain$Response(params?: ApiQuotationGetQuotationHistoryPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationVersionsListResponseBase>> {
    return apiQuotationGetQuotationHistoryPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationGetQuotationHistoryPost$Plain$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  apiQuotationGetQuotationHistoryPost$Plain(params?: ApiQuotationGetQuotationHistoryPost$Plain$Params, context?: HttpContext): Observable<GetQuotationVersionsListResponseBase> {
    return this.apiQuotationGetQuotationHistoryPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationVersionsListResponseBase>): GetQuotationVersionsListResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationGetQuotationHistoryPost$Json()` instead.
   *
   * This method doesn't expect any request body.
   */
  apiQuotationGetQuotationHistoryPost$Json$Response(params?: ApiQuotationGetQuotationHistoryPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationVersionsListResponseBase>> {
    return apiQuotationGetQuotationHistoryPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationGetQuotationHistoryPost$Json$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  apiQuotationGetQuotationHistoryPost$Json(params?: ApiQuotationGetQuotationHistoryPost$Json$Params, context?: HttpContext): Observable<GetQuotationVersionsListResponseBase> {
    return this.apiQuotationGetQuotationHistoryPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<GetQuotationVersionsListResponseBase>): GetQuotationVersionsListResponseBase => r.body)
    );
  }

  /** Path part for operation `apiQuotationSignQuotationPost()` */
  static readonly ApiQuotationSignQuotationPostPath = '/api/Quotation/SignQuotation';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationSignQuotationPost$Plain()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationSignQuotationPost$Plain$Response(params?: ApiQuotationSignQuotationPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiQuotationSignQuotationPost$Plain(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationSignQuotationPost$Plain$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationSignQuotationPost$Plain(params?: ApiQuotationSignQuotationPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiQuotationSignQuotationPost$Plain$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiQuotationSignQuotationPost$Json()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationSignQuotationPost$Json$Response(params?: ApiQuotationSignQuotationPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {
    return apiQuotationSignQuotationPost$Json(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiQuotationSignQuotationPost$Json$Response()` instead.
   *
   * This method sends `application/*+json` and handles request body of type `application/*+json`.
   */
  apiQuotationSignQuotationPost$Json(params?: ApiQuotationSignQuotationPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {
    return this.apiQuotationSignQuotationPost$Json$Response(params, context).pipe(
      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)
    );
  }

}
