{"ast": null, "code": "import * as i0 from \"@angular/core\";\nfunction TestDropdownComponent_div_7_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r2);\n  }\n}\nfunction TestDropdownComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"h4\");\n    i0.ɵɵtext(2, \"\\u4E0B\\u62C9\\u9078\\u55AE\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"\\u5982\\u679C\\u4F60\\u770B\\u5230\\u9019\\u500B\\u9EC3\\u8272\\u5340\\u57DF\\uFF0C\\u8868\\u793A *ngIf \\u6B63\\u5E38\\u5DE5\\u4F5C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ul\");\n    i0.ɵɵtemplate(6, TestDropdownComponent_div_7_li_6_Template, 2, 1, \"li\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function TestDropdownComponent_div_7_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeDropdown());\n    });\n    i0.ɵɵtext(8, \" \\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.testItems);\n  }\n}\nexport class TestDropdownComponent {\n  constructor() {\n    this.isOpen = false;\n    this.testItems = ['項目1', '項目2', '項目3', '項目4', '項目5'];\n  }\n  toggleDropdown() {\n    this.isOpen = !this.isOpen;\n    console.log('Test dropdown toggled:', this.isOpen);\n  }\n  closeDropdown() {\n    this.isOpen = false;\n    console.log('Test dropdown closed');\n  }\n  static {\n    this.ɵfac = function TestDropdownComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TestDropdownComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TestDropdownComponent,\n      selectors: [[\"app-test-dropdown\"]],\n      decls: 13,\n      vars: 4,\n      consts: [[2, \"padding\", \"20px\", \"border\", \"2px solid red\"], [2, \"padding\", \"10px\", \"background\", \"blue\", \"color\", \"white\", \"border\", \"none\", \"cursor\", \"pointer\", 3, \"click\"], [\"style\", \"position: relative; margin-top: 10px; padding: 20px; background: yellow; border: 2px solid green; z-index: 9999;\", 4, \"ngIf\"], [2, \"margin-top\", \"20px\", \"padding\", \"10px\", \"background\", \"lightblue\", \"border\", \"1px solid blue\"], [2, \"position\", \"relative\", \"margin-top\", \"10px\", \"padding\", \"20px\", \"background\", \"yellow\", \"border\", \"2px solid green\", \"z-index\", \"9999\"], [4, \"ngFor\", \"ngForOf\"], [2, \"padding\", \"5px\", \"background\", \"red\", \"color\", \"white\", \"border\", \"none\", \"cursor\", \"pointer\", 3, \"click\"]],\n      template: function TestDropdownComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\");\n          i0.ɵɵtext(2, \"\\u6E2C\\u8A66\\u4E0B\\u62C9\\u9078\\u55AE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 1);\n          i0.ɵɵlistener(\"click\", function TestDropdownComponent_Template_button_click_5_listener() {\n            return ctx.toggleDropdown();\n          });\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, TestDropdownComponent_div_7_Template, 9, 1, \"div\", 2);\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"h4\");\n          i0.ɵɵtext(10, \"\\u9019\\u500B\\u5340\\u57DF\\u61C9\\u8A72\\u6C38\\u9060\\u53EF\\u898B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\");\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"isOpen \\u72C0\\u614B: \", ctx.isOpen, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \\u9EDE\\u64CA\\u5207\\u63DB\\u4E0B\\u62C9\\u9078\\u55AE (\\u7576\\u524D: \", ctx.isOpen ? \"\\u958B\\u555F\" : \"\\u95DC\\u9589\", \") \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\\u6E2C\\u8A66\\u9805\\u76EE\\u6578\\u91CF: \", ctx.testItems.length, \"\");\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "item_r2", "ɵɵtemplate", "TestDropdownComponent_div_7_li_6_Template", "ɵɵlistener", "TestDropdownComponent_div_7_Template_button_click_7_listener", "ɵɵrestoreView", "_r1", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "closeDropdown", "ɵɵproperty", "testItems", "TestDropdownComponent", "constructor", "isOpen", "toggleDropdown", "console", "log", "selectors", "decls", "vars", "consts", "template", "TestDropdownComponent_Template", "rf", "ctx", "TestDropdownComponent_Template_button_click_5_listener", "TestDropdownComponent_div_7_Template", "ɵɵtextInterpolate1", "length"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\test-dropdown.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-test-dropdown',\r\n  template: `\r\n    <div style=\"padding: 20px; border: 2px solid red;\">\r\n      <h3>測試下拉選單</h3>\r\n      <p>isOpen 狀態: {{isOpen}}</p>\r\n\r\n      <button (click)=\"toggleDropdown()\" style=\"padding: 10px; background: blue; color: white; border: none; cursor: pointer;\">\r\n        點擊切換下拉選單 (當前: {{isOpen ? '開啟' : '關閉'}})\r\n      </button>\r\n\r\n      <!-- 簡單的下拉選單測試 -->\r\n      <div *ngIf=\"isOpen\" style=\"position: relative; margin-top: 10px; padding: 20px; background: yellow; border: 2px solid green; z-index: 9999;\">\r\n        <h4>下拉選單內容</h4>\r\n        <p>如果你看到這個黃色區域，表示 *ngIf 正常工作</p>\r\n        <ul>\r\n          <li *ngFor=\"let item of testItems\">{{item}}</li>\r\n        </ul>\r\n        <button (click)=\"closeDropdown()\" style=\"padding: 5px; background: red; color: white; border: none; cursor: pointer;\">\r\n          關閉\r\n        </button>\r\n      </div>\r\n\r\n      <!-- 無條件顯示的區域 -->\r\n      <div style=\"margin-top: 20px; padding: 10px; background: lightblue; border: 1px solid blue;\">\r\n        <h4>這個區域應該永遠可見</h4>\r\n        <p>測試項目數量: {{testItems.length}}</p>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: []\r\n})\r\nexport class TestDropdownComponent {\r\n  isOpen = false;\r\n  testItems = ['項目1', '項目2', '項目3', '項目4', '項目5'];\r\n\r\n  toggleDropdown() {\r\n    this.isOpen = !this.isOpen;\r\n    console.log('Test dropdown toggled:', this.isOpen);\r\n  }\r\n\r\n  closeDropdown() {\r\n    this.isOpen = false;\r\n    console.log('Test dropdown closed');\r\n  }\r\n}\r\n"], "mappings": ";;;IAkBUA,EAAA,CAAAC,cAAA,SAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAbH,EAAA,CAAAI,SAAA,EAAQ;IAARJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAQ;;;;;;IAH7CN,EADF,CAAAC,cAAA,aAA6I,SACvI;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0HAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAChCH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAO,UAAA,IAAAC,yCAAA,gBAAmC;IACrCR,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,gBAAsH;IAA9GD,EAAA,CAAAS,UAAA,mBAAAC,6DAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IAC/BhB,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALmBH,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAiB,UAAA,YAAAJ,MAAA,CAAAK,SAAA,CAAY;;;AAgB3C,OAAM,MAAOC,qBAAqB;EAhClCC,YAAA;IAiCE,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAH,SAAS,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;;EAE/CI,cAAcA,CAAA;IACZ,IAAI,CAACD,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAC1BE,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACH,MAAM,CAAC;EACpD;EAEAL,aAAaA,CAAA;IACX,IAAI,CAACK,MAAM,GAAG,KAAK;IACnBE,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;EACrC;;;uCAZWL,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAM,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5B5B/B,EADF,CAAAC,cAAA,aAAmD,SAC7C;UAAAD,EAAA,CAAAE,MAAA,2CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACfH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,GAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE5BH,EAAA,CAAAC,cAAA,gBAAyH;UAAjHD,EAAA,CAAAS,UAAA,mBAAAwB,uDAAA;YAAA,OAASD,GAAA,CAAAV,cAAA,EAAgB;UAAA,EAAC;UAChCtB,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAO,UAAA,IAAA2B,oCAAA,iBAA6I;UAa3IlC,EADF,CAAAC,cAAA,aAA6F,SACvF;UAAAD,EAAA,CAAAE,MAAA,oEAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,IAA4B;UAEnCF,EAFmC,CAAAG,YAAA,EAAI,EAC/B,EACF;;;UAvBDH,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAmC,kBAAA,0BAAAH,GAAA,CAAAX,MAAA,KAAqB;UAGtBrB,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAmC,kBAAA,sEAAAH,GAAA,CAAAX,MAAA,yCACF;UAGMrB,EAAA,CAAAI,SAAA,EAAY;UAAZJ,EAAA,CAAAiB,UAAA,SAAAe,GAAA,CAAAX,MAAA,CAAY;UAcbrB,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAmC,kBAAA,2CAAAH,GAAA,CAAAd,SAAA,CAAAkB,MAAA,KAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}