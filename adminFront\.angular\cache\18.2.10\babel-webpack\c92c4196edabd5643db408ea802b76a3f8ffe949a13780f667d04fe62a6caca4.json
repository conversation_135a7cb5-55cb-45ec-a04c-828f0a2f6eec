{"ast": null, "code": "import { DEFAULT_COMMON_STYLE } from '../graphic/Displayable.js';\nimport PathProxy from '../core/PathProxy.js';\nimport { createOrUpdateImage, isImageReady } from '../graphic/helper/image.js';\nimport { getCanvasGradient, isClipPathChanged } from './helper.js';\nimport Path from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport TSpan from '../graphic/TSpan.js';\nimport { RADIAN_TO_DEGREE } from '../core/util.js';\nimport { getLineDash } from './dashStyle.js';\nimport { REDRAW_BIT, SHAPE_CHANGED_BIT } from '../graphic/constants.js';\nimport { DEFAULT_FONT } from '../core/platform.js';\nvar pathProxyForDraw = new PathProxy(true);\nfunction styleHasStroke(style) {\n  var stroke = style.stroke;\n  return !(stroke == null || stroke === 'none' || !(style.lineWidth > 0));\n}\nfunction isValidStrokeFillStyle(strokeOrFill) {\n  return typeof strokeOrFill === 'string' && strokeOrFill !== 'none';\n}\nfunction styleHasFill(style) {\n  var fill = style.fill;\n  return fill != null && fill !== 'none';\n}\nfunction doFillPath(ctx, style) {\n  if (style.fillOpacity != null && style.fillOpacity !== 1) {\n    var originalGlobalAlpha = ctx.globalAlpha;\n    ctx.globalAlpha = style.fillOpacity * style.opacity;\n    ctx.fill();\n    ctx.globalAlpha = originalGlobalAlpha;\n  } else {\n    ctx.fill();\n  }\n}\nfunction doStrokePath(ctx, style) {\n  if (style.strokeOpacity != null && style.strokeOpacity !== 1) {\n    var originalGlobalAlpha = ctx.globalAlpha;\n    ctx.globalAlpha = style.strokeOpacity * style.opacity;\n    ctx.stroke();\n    ctx.globalAlpha = originalGlobalAlpha;\n  } else {\n    ctx.stroke();\n  }\n}\nexport function createCanvasPattern(ctx, pattern, el) {\n  var image = createOrUpdateImage(pattern.image, pattern.__image, el);\n  if (isImageReady(image)) {\n    var canvasPattern = ctx.createPattern(image, pattern.repeat || 'repeat');\n    if (typeof DOMMatrix === 'function' && canvasPattern && canvasPattern.setTransform) {\n      var matrix = new DOMMatrix();\n      matrix.translateSelf(pattern.x || 0, pattern.y || 0);\n      matrix.rotateSelf(0, 0, (pattern.rotation || 0) * RADIAN_TO_DEGREE);\n      matrix.scaleSelf(pattern.scaleX || 1, pattern.scaleY || 1);\n      canvasPattern.setTransform(matrix);\n    }\n    return canvasPattern;\n  }\n}\nfunction brushPath(ctx, el, style, inBatch) {\n  var _a;\n  var hasStroke = styleHasStroke(style);\n  var hasFill = styleHasFill(style);\n  var strokePercent = style.strokePercent;\n  var strokePart = strokePercent < 1;\n  var firstDraw = !el.path;\n  if ((!el.silent || strokePart) && firstDraw) {\n    el.createPathProxy();\n  }\n  var path = el.path || pathProxyForDraw;\n  var dirtyFlag = el.__dirty;\n  if (!inBatch) {\n    var fill = style.fill;\n    var stroke = style.stroke;\n    var hasFillGradient = hasFill && !!fill.colorStops;\n    var hasStrokeGradient = hasStroke && !!stroke.colorStops;\n    var hasFillPattern = hasFill && !!fill.image;\n    var hasStrokePattern = hasStroke && !!stroke.image;\n    var fillGradient = void 0;\n    var strokeGradient = void 0;\n    var fillPattern = void 0;\n    var strokePattern = void 0;\n    var rect = void 0;\n    if (hasFillGradient || hasStrokeGradient) {\n      rect = el.getBoundingRect();\n    }\n    if (hasFillGradient) {\n      fillGradient = dirtyFlag ? getCanvasGradient(ctx, fill, rect) : el.__canvasFillGradient;\n      el.__canvasFillGradient = fillGradient;\n    }\n    if (hasStrokeGradient) {\n      strokeGradient = dirtyFlag ? getCanvasGradient(ctx, stroke, rect) : el.__canvasStrokeGradient;\n      el.__canvasStrokeGradient = strokeGradient;\n    }\n    if (hasFillPattern) {\n      fillPattern = dirtyFlag || !el.__canvasFillPattern ? createCanvasPattern(ctx, fill, el) : el.__canvasFillPattern;\n      el.__canvasFillPattern = fillPattern;\n    }\n    if (hasStrokePattern) {\n      strokePattern = dirtyFlag || !el.__canvasStrokePattern ? createCanvasPattern(ctx, stroke, el) : el.__canvasStrokePattern;\n      el.__canvasStrokePattern = fillPattern;\n    }\n    if (hasFillGradient) {\n      ctx.fillStyle = fillGradient;\n    } else if (hasFillPattern) {\n      if (fillPattern) {\n        ctx.fillStyle = fillPattern;\n      } else {\n        hasFill = false;\n      }\n    }\n    if (hasStrokeGradient) {\n      ctx.strokeStyle = strokeGradient;\n    } else if (hasStrokePattern) {\n      if (strokePattern) {\n        ctx.strokeStyle = strokePattern;\n      } else {\n        hasStroke = false;\n      }\n    }\n  }\n  var scale = el.getGlobalScale();\n  path.setScale(scale[0], scale[1], el.segmentIgnoreThreshold);\n  var lineDash;\n  var lineDashOffset;\n  if (ctx.setLineDash && style.lineDash) {\n    _a = getLineDash(el), lineDash = _a[0], lineDashOffset = _a[1];\n  }\n  var needsRebuild = true;\n  if (firstDraw || dirtyFlag & SHAPE_CHANGED_BIT) {\n    path.setDPR(ctx.dpr);\n    if (strokePart) {\n      path.setContext(null);\n    } else {\n      path.setContext(ctx);\n      needsRebuild = false;\n    }\n    path.reset();\n    el.buildPath(path, el.shape, inBatch);\n    path.toStatic();\n    el.pathUpdated();\n  }\n  if (needsRebuild) {\n    path.rebuildPath(ctx, strokePart ? strokePercent : 1);\n  }\n  if (lineDash) {\n    ctx.setLineDash(lineDash);\n    ctx.lineDashOffset = lineDashOffset;\n  }\n  if (!inBatch) {\n    if (style.strokeFirst) {\n      if (hasStroke) {\n        doStrokePath(ctx, style);\n      }\n      if (hasFill) {\n        doFillPath(ctx, style);\n      }\n    } else {\n      if (hasFill) {\n        doFillPath(ctx, style);\n      }\n      if (hasStroke) {\n        doStrokePath(ctx, style);\n      }\n    }\n  }\n  if (lineDash) {\n    ctx.setLineDash([]);\n  }\n}\nfunction brushImage(ctx, el, style) {\n  var image = el.__image = createOrUpdateImage(style.image, el.__image, el, el.onload);\n  if (!image || !isImageReady(image)) {\n    return;\n  }\n  var x = style.x || 0;\n  var y = style.y || 0;\n  var width = el.getWidth();\n  var height = el.getHeight();\n  var aspect = image.width / image.height;\n  if (width == null && height != null) {\n    width = height * aspect;\n  } else if (height == null && width != null) {\n    height = width / aspect;\n  } else if (width == null && height == null) {\n    width = image.width;\n    height = image.height;\n  }\n  if (style.sWidth && style.sHeight) {\n    var sx = style.sx || 0;\n    var sy = style.sy || 0;\n    ctx.drawImage(image, sx, sy, style.sWidth, style.sHeight, x, y, width, height);\n  } else if (style.sx && style.sy) {\n    var sx = style.sx;\n    var sy = style.sy;\n    var sWidth = width - sx;\n    var sHeight = height - sy;\n    ctx.drawImage(image, sx, sy, sWidth, sHeight, x, y, width, height);\n  } else {\n    ctx.drawImage(image, x, y, width, height);\n  }\n}\nfunction brushText(ctx, el, style) {\n  var _a;\n  var text = style.text;\n  text != null && (text += '');\n  if (text) {\n    ctx.font = style.font || DEFAULT_FONT;\n    ctx.textAlign = style.textAlign;\n    ctx.textBaseline = style.textBaseline;\n    var lineDash = void 0;\n    var lineDashOffset = void 0;\n    if (ctx.setLineDash && style.lineDash) {\n      _a = getLineDash(el), lineDash = _a[0], lineDashOffset = _a[1];\n    }\n    if (lineDash) {\n      ctx.setLineDash(lineDash);\n      ctx.lineDashOffset = lineDashOffset;\n    }\n    if (style.strokeFirst) {\n      if (styleHasStroke(style)) {\n        ctx.strokeText(text, style.x, style.y);\n      }\n      if (styleHasFill(style)) {\n        ctx.fillText(text, style.x, style.y);\n      }\n    } else {\n      if (styleHasFill(style)) {\n        ctx.fillText(text, style.x, style.y);\n      }\n      if (styleHasStroke(style)) {\n        ctx.strokeText(text, style.x, style.y);\n      }\n    }\n    if (lineDash) {\n      ctx.setLineDash([]);\n    }\n  }\n}\nvar SHADOW_NUMBER_PROPS = ['shadowBlur', 'shadowOffsetX', 'shadowOffsetY'];\nvar STROKE_PROPS = [['lineCap', 'butt'], ['lineJoin', 'miter'], ['miterLimit', 10]];\nfunction bindCommonProps(ctx, style, prevStyle, forceSetAll, scope) {\n  var styleChanged = false;\n  if (!forceSetAll) {\n    prevStyle = prevStyle || {};\n    if (style === prevStyle) {\n      return false;\n    }\n  }\n  if (forceSetAll || style.opacity !== prevStyle.opacity) {\n    flushPathDrawn(ctx, scope);\n    styleChanged = true;\n    var opacity = Math.max(Math.min(style.opacity, 1), 0);\n    ctx.globalAlpha = isNaN(opacity) ? DEFAULT_COMMON_STYLE.opacity : opacity;\n  }\n  if (forceSetAll || style.blend !== prevStyle.blend) {\n    if (!styleChanged) {\n      flushPathDrawn(ctx, scope);\n      styleChanged = true;\n    }\n    ctx.globalCompositeOperation = style.blend || DEFAULT_COMMON_STYLE.blend;\n  }\n  for (var i = 0; i < SHADOW_NUMBER_PROPS.length; i++) {\n    var propName = SHADOW_NUMBER_PROPS[i];\n    if (forceSetAll || style[propName] !== prevStyle[propName]) {\n      if (!styleChanged) {\n        flushPathDrawn(ctx, scope);\n        styleChanged = true;\n      }\n      ctx[propName] = ctx.dpr * (style[propName] || 0);\n    }\n  }\n  if (forceSetAll || style.shadowColor !== prevStyle.shadowColor) {\n    if (!styleChanged) {\n      flushPathDrawn(ctx, scope);\n      styleChanged = true;\n    }\n    ctx.shadowColor = style.shadowColor || DEFAULT_COMMON_STYLE.shadowColor;\n  }\n  return styleChanged;\n}\nfunction bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetAll, scope) {\n  var style = getStyle(el, scope.inHover);\n  var prevStyle = forceSetAll ? null : prevEl && getStyle(prevEl, scope.inHover) || {};\n  if (style === prevStyle) {\n    return false;\n  }\n  var styleChanged = bindCommonProps(ctx, style, prevStyle, forceSetAll, scope);\n  if (forceSetAll || style.fill !== prevStyle.fill) {\n    if (!styleChanged) {\n      flushPathDrawn(ctx, scope);\n      styleChanged = true;\n    }\n    isValidStrokeFillStyle(style.fill) && (ctx.fillStyle = style.fill);\n  }\n  if (forceSetAll || style.stroke !== prevStyle.stroke) {\n    if (!styleChanged) {\n      flushPathDrawn(ctx, scope);\n      styleChanged = true;\n    }\n    isValidStrokeFillStyle(style.stroke) && (ctx.strokeStyle = style.stroke);\n  }\n  if (forceSetAll || style.opacity !== prevStyle.opacity) {\n    if (!styleChanged) {\n      flushPathDrawn(ctx, scope);\n      styleChanged = true;\n    }\n    ctx.globalAlpha = style.opacity == null ? 1 : style.opacity;\n  }\n  if (el.hasStroke()) {\n    var lineWidth = style.lineWidth;\n    var newLineWidth = lineWidth / (style.strokeNoScale && el.getLineScale ? el.getLineScale() : 1);\n    if (ctx.lineWidth !== newLineWidth) {\n      if (!styleChanged) {\n        flushPathDrawn(ctx, scope);\n        styleChanged = true;\n      }\n      ctx.lineWidth = newLineWidth;\n    }\n  }\n  for (var i = 0; i < STROKE_PROPS.length; i++) {\n    var prop = STROKE_PROPS[i];\n    var propName = prop[0];\n    if (forceSetAll || style[propName] !== prevStyle[propName]) {\n      if (!styleChanged) {\n        flushPathDrawn(ctx, scope);\n        styleChanged = true;\n      }\n      ctx[propName] = style[propName] || prop[1];\n    }\n  }\n  return styleChanged;\n}\nfunction bindImageStyle(ctx, el, prevEl, forceSetAll, scope) {\n  return bindCommonProps(ctx, getStyle(el, scope.inHover), prevEl && getStyle(prevEl, scope.inHover), forceSetAll, scope);\n}\nfunction setContextTransform(ctx, el) {\n  var m = el.transform;\n  var dpr = ctx.dpr || 1;\n  if (m) {\n    ctx.setTransform(dpr * m[0], dpr * m[1], dpr * m[2], dpr * m[3], dpr * m[4], dpr * m[5]);\n  } else {\n    ctx.setTransform(dpr, 0, 0, dpr, 0, 0);\n  }\n}\nfunction updateClipStatus(clipPaths, ctx, scope) {\n  var allClipped = false;\n  for (var i = 0; i < clipPaths.length; i++) {\n    var clipPath = clipPaths[i];\n    allClipped = allClipped || clipPath.isZeroArea();\n    setContextTransform(ctx, clipPath);\n    ctx.beginPath();\n    clipPath.buildPath(ctx, clipPath.shape);\n    ctx.clip();\n  }\n  scope.allClipped = allClipped;\n}\nfunction isTransformChanged(m0, m1) {\n  if (m0 && m1) {\n    return m0[0] !== m1[0] || m0[1] !== m1[1] || m0[2] !== m1[2] || m0[3] !== m1[3] || m0[4] !== m1[4] || m0[5] !== m1[5];\n  } else if (!m0 && !m1) {\n    return false;\n  }\n  return true;\n}\nvar DRAW_TYPE_PATH = 1;\nvar DRAW_TYPE_IMAGE = 2;\nvar DRAW_TYPE_TEXT = 3;\nvar DRAW_TYPE_INCREMENTAL = 4;\nfunction canPathBatch(style) {\n  var hasFill = styleHasFill(style);\n  var hasStroke = styleHasStroke(style);\n  return !(style.lineDash || !(+hasFill ^ +hasStroke) || hasFill && typeof style.fill !== 'string' || hasStroke && typeof style.stroke !== 'string' || style.strokePercent < 1 || style.strokeOpacity < 1 || style.fillOpacity < 1);\n}\nfunction flushPathDrawn(ctx, scope) {\n  scope.batchFill && ctx.fill();\n  scope.batchStroke && ctx.stroke();\n  scope.batchFill = '';\n  scope.batchStroke = '';\n}\nfunction getStyle(el, inHover) {\n  return inHover ? el.__hoverStyle || el.style : el.style;\n}\nexport function brushSingle(ctx, el) {\n  brush(ctx, el, {\n    inHover: false,\n    viewWidth: 0,\n    viewHeight: 0\n  }, true);\n}\nexport function brush(ctx, el, scope, isLast) {\n  var m = el.transform;\n  if (!el.shouldBePainted(scope.viewWidth, scope.viewHeight, false, false)) {\n    el.__dirty &= ~REDRAW_BIT;\n    el.__isRendered = false;\n    return;\n  }\n  var clipPaths = el.__clipPaths;\n  var prevElClipPaths = scope.prevElClipPaths;\n  var forceSetTransform = false;\n  var forceSetStyle = false;\n  if (!prevElClipPaths || isClipPathChanged(clipPaths, prevElClipPaths)) {\n    if (prevElClipPaths && prevElClipPaths.length) {\n      flushPathDrawn(ctx, scope);\n      ctx.restore();\n      forceSetStyle = forceSetTransform = true;\n      scope.prevElClipPaths = null;\n      scope.allClipped = false;\n      scope.prevEl = null;\n    }\n    if (clipPaths && clipPaths.length) {\n      flushPathDrawn(ctx, scope);\n      ctx.save();\n      updateClipStatus(clipPaths, ctx, scope);\n      forceSetTransform = true;\n    }\n    scope.prevElClipPaths = clipPaths;\n  }\n  if (scope.allClipped) {\n    el.__isRendered = false;\n    return;\n  }\n  el.beforeBrush && el.beforeBrush();\n  el.innerBeforeBrush();\n  var prevEl = scope.prevEl;\n  if (!prevEl) {\n    forceSetStyle = forceSetTransform = true;\n  }\n  var canBatchPath = el instanceof Path && el.autoBatch && canPathBatch(el.style);\n  if (forceSetTransform || isTransformChanged(m, prevEl.transform)) {\n    flushPathDrawn(ctx, scope);\n    setContextTransform(ctx, el);\n  } else if (!canBatchPath) {\n    flushPathDrawn(ctx, scope);\n  }\n  var style = getStyle(el, scope.inHover);\n  if (el instanceof Path) {\n    if (scope.lastDrawType !== DRAW_TYPE_PATH) {\n      forceSetStyle = true;\n      scope.lastDrawType = DRAW_TYPE_PATH;\n    }\n    bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetStyle, scope);\n    if (!canBatchPath || !scope.batchFill && !scope.batchStroke) {\n      ctx.beginPath();\n    }\n    brushPath(ctx, el, style, canBatchPath);\n    if (canBatchPath) {\n      scope.batchFill = style.fill || '';\n      scope.batchStroke = style.stroke || '';\n    }\n  } else {\n    if (el instanceof TSpan) {\n      if (scope.lastDrawType !== DRAW_TYPE_TEXT) {\n        forceSetStyle = true;\n        scope.lastDrawType = DRAW_TYPE_TEXT;\n      }\n      bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetStyle, scope);\n      brushText(ctx, el, style);\n    } else if (el instanceof ZRImage) {\n      if (scope.lastDrawType !== DRAW_TYPE_IMAGE) {\n        forceSetStyle = true;\n        scope.lastDrawType = DRAW_TYPE_IMAGE;\n      }\n      bindImageStyle(ctx, el, prevEl, forceSetStyle, scope);\n      brushImage(ctx, el, style);\n    } else if (el.getTemporalDisplayables) {\n      if (scope.lastDrawType !== DRAW_TYPE_INCREMENTAL) {\n        forceSetStyle = true;\n        scope.lastDrawType = DRAW_TYPE_INCREMENTAL;\n      }\n      brushIncremental(ctx, el, scope);\n    }\n  }\n  if (canBatchPath && isLast) {\n    flushPathDrawn(ctx, scope);\n  }\n  el.innerAfterBrush();\n  el.afterBrush && el.afterBrush();\n  scope.prevEl = el;\n  el.__dirty = 0;\n  el.__isRendered = true;\n}\nfunction brushIncremental(ctx, el, scope) {\n  var displayables = el.getDisplayables();\n  var temporalDisplayables = el.getTemporalDisplayables();\n  ctx.save();\n  var innerScope = {\n    prevElClipPaths: null,\n    prevEl: null,\n    allClipped: false,\n    viewWidth: scope.viewWidth,\n    viewHeight: scope.viewHeight,\n    inHover: scope.inHover\n  };\n  var i;\n  var len;\n  for (i = el.getCursor(), len = displayables.length; i < len; i++) {\n    var displayable = displayables[i];\n    displayable.beforeBrush && displayable.beforeBrush();\n    displayable.innerBeforeBrush();\n    brush(ctx, displayable, innerScope, i === len - 1);\n    displayable.innerAfterBrush();\n    displayable.afterBrush && displayable.afterBrush();\n    innerScope.prevEl = displayable;\n  }\n  for (var i_1 = 0, len_1 = temporalDisplayables.length; i_1 < len_1; i_1++) {\n    var displayable = temporalDisplayables[i_1];\n    displayable.beforeBrush && displayable.beforeBrush();\n    displayable.innerBeforeBrush();\n    brush(ctx, displayable, innerScope, i_1 === len_1 - 1);\n    displayable.innerAfterBrush();\n    displayable.afterBrush && displayable.afterBrush();\n    innerScope.prevEl = displayable;\n  }\n  el.clearTemporalDisplayables();\n  el.notClear = true;\n  ctx.restore();\n}", "map": {"version": 3, "names": ["DEFAULT_COMMON_STYLE", "PathProxy", "createOrUpdateImage", "isImageReady", "getCanvasGradient", "isClipPath<PERSON><PERSON>ed", "Path", "ZRImage", "TSpan", "RADIAN_TO_DEGREE", "getLineDash", "REDRAW_BIT", "SHAPE_CHANGED_BIT", "DEFAULT_FONT", "pathProxyForDraw", "styleHasStroke", "style", "stroke", "lineWidth", "isValidStrokeFillStyle", "strokeOrFill", "styleHasFill", "fill", "do<PERSON>ill<PERSON><PERSON>", "ctx", "fillOpacity", "originalGlobalAlpha", "globalAlpha", "opacity", "doStrokePath", "strokeOpacity", "createCanvasPattern", "pattern", "el", "image", "__image", "canvasPattern", "createPattern", "repeat", "DOMMatrix", "setTransform", "matrix", "translateSelf", "x", "y", "rotateSelf", "rotation", "scaleSelf", "scaleX", "scaleY", "brushPath", "inBatch", "_a", "hasStroke", "hasFill", "strokePercent", "strokePart", "firstDraw", "path", "silent", "createPathProxy", "dirtyFlag", "__dirty", "hasFillGradient", "colorStops", "hasStrokeGradient", "hasFillPattern", "hasStrokePattern", "fillGradient", "strokeGradient", "fillPattern", "strokePattern", "rect", "getBoundingRect", "__canvasFillGradient", "__canvasStrokeGradient", "__canvasFillPattern", "__canvasStrokePattern", "fillStyle", "strokeStyle", "scale", "getGlobalScale", "setScale", "segmentIgnoreThreshold", "lineDash", "lineDashOffset", "setLineDash", "needsRebuild", "setDPR", "dpr", "setContext", "reset", "buildPath", "shape", "to<PERSON><PERSON><PERSON>", "pathUpdated", "rebuildPath", "<PERSON><PERSON><PERSON><PERSON>", "brushImage", "onload", "width", "getWidth", "height", "getHeight", "aspect", "sWidth", "sHeight", "sx", "sy", "drawImage", "brushText", "text", "font", "textAlign", "textBaseline", "strokeText", "fillText", "SHADOW_NUMBER_PROPS", "STROKE_PROPS", "bindCommonProps", "prevStyle", "forceSetAll", "scope", "styleChanged", "flushPathDrawn", "Math", "max", "min", "isNaN", "blend", "globalCompositeOperation", "i", "length", "propName", "shadowColor", "bindPathAndTextCommonStyle", "prevEl", "getStyle", "inHover", "newLineWidth", "strokeNoScale", "getLineScale", "prop", "bindImageStyle", "setContextTransform", "m", "transform", "updateClipStatus", "clipPaths", "allClipped", "clipPath", "isZeroArea", "beginPath", "clip", "isTransformChanged", "m0", "m1", "DRAW_TYPE_PATH", "DRAW_TYPE_IMAGE", "DRAW_TYPE_TEXT", "DRAW_TYPE_INCREMENTAL", "canPathBatch", "batchFill", "batchStroke", "__hoverStyle", "brushSingle", "brush", "viewWidth", "viewHeight", "isLast", "shouldBePainted", "__isRendered", "__clipPaths", "prevElClipPaths", "forceSetTransform", "forceSetStyle", "restore", "save", "beforeBrush", "innerBeforeBrush", "canBatchPath", "autoBatch", "lastDrawType", "getTemporalDisplayables", "brushIncremental", "innerAfterBrush", "afterBrush", "displayables", "getDisplayables", "temporalDisplayables", "innerScope", "len", "getCursor", "displayable", "i_1", "len_1", "clearTemporalDisplayables", "notClear"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/zrender/lib/canvas/graphic.js"], "sourcesContent": ["import { DEFAULT_COMMON_STYLE } from '../graphic/Displayable.js';\nimport PathProxy from '../core/PathProxy.js';\nimport { createOrUpdateImage, isImageReady } from '../graphic/helper/image.js';\nimport { getCanvasGradient, isClipPathChanged } from './helper.js';\nimport Path from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport TSpan from '../graphic/TSpan.js';\nimport { RADIAN_TO_DEGREE } from '../core/util.js';\nimport { getLineDash } from './dashStyle.js';\nimport { REDRAW_BIT, SHAPE_CHANGED_BIT } from '../graphic/constants.js';\nimport { DEFAULT_FONT } from '../core/platform.js';\nvar pathProxyForDraw = new PathProxy(true);\nfunction styleHasStroke(style) {\n    var stroke = style.stroke;\n    return !(stroke == null || stroke === 'none' || !(style.lineWidth > 0));\n}\nfunction isValidStrokeFillStyle(strokeOrFill) {\n    return typeof strokeOrFill === 'string' && strokeOrFill !== 'none';\n}\nfunction styleHasFill(style) {\n    var fill = style.fill;\n    return fill != null && fill !== 'none';\n}\nfunction doFillPath(ctx, style) {\n    if (style.fillOpacity != null && style.fillOpacity !== 1) {\n        var originalGlobalAlpha = ctx.globalAlpha;\n        ctx.globalAlpha = style.fillOpacity * style.opacity;\n        ctx.fill();\n        ctx.globalAlpha = originalGlobalAlpha;\n    }\n    else {\n        ctx.fill();\n    }\n}\nfunction doStrokePath(ctx, style) {\n    if (style.strokeOpacity != null && style.strokeOpacity !== 1) {\n        var originalGlobalAlpha = ctx.globalAlpha;\n        ctx.globalAlpha = style.strokeOpacity * style.opacity;\n        ctx.stroke();\n        ctx.globalAlpha = originalGlobalAlpha;\n    }\n    else {\n        ctx.stroke();\n    }\n}\nexport function createCanvasPattern(ctx, pattern, el) {\n    var image = createOrUpdateImage(pattern.image, pattern.__image, el);\n    if (isImageReady(image)) {\n        var canvasPattern = ctx.createPattern(image, pattern.repeat || 'repeat');\n        if (typeof DOMMatrix === 'function'\n            && canvasPattern\n            && canvasPattern.setTransform) {\n            var matrix = new DOMMatrix();\n            matrix.translateSelf((pattern.x || 0), (pattern.y || 0));\n            matrix.rotateSelf(0, 0, (pattern.rotation || 0) * RADIAN_TO_DEGREE);\n            matrix.scaleSelf((pattern.scaleX || 1), (pattern.scaleY || 1));\n            canvasPattern.setTransform(matrix);\n        }\n        return canvasPattern;\n    }\n}\nfunction brushPath(ctx, el, style, inBatch) {\n    var _a;\n    var hasStroke = styleHasStroke(style);\n    var hasFill = styleHasFill(style);\n    var strokePercent = style.strokePercent;\n    var strokePart = strokePercent < 1;\n    var firstDraw = !el.path;\n    if ((!el.silent || strokePart) && firstDraw) {\n        el.createPathProxy();\n    }\n    var path = el.path || pathProxyForDraw;\n    var dirtyFlag = el.__dirty;\n    if (!inBatch) {\n        var fill = style.fill;\n        var stroke = style.stroke;\n        var hasFillGradient = hasFill && !!fill.colorStops;\n        var hasStrokeGradient = hasStroke && !!stroke.colorStops;\n        var hasFillPattern = hasFill && !!fill.image;\n        var hasStrokePattern = hasStroke && !!stroke.image;\n        var fillGradient = void 0;\n        var strokeGradient = void 0;\n        var fillPattern = void 0;\n        var strokePattern = void 0;\n        var rect = void 0;\n        if (hasFillGradient || hasStrokeGradient) {\n            rect = el.getBoundingRect();\n        }\n        if (hasFillGradient) {\n            fillGradient = dirtyFlag\n                ? getCanvasGradient(ctx, fill, rect)\n                : el.__canvasFillGradient;\n            el.__canvasFillGradient = fillGradient;\n        }\n        if (hasStrokeGradient) {\n            strokeGradient = dirtyFlag\n                ? getCanvasGradient(ctx, stroke, rect)\n                : el.__canvasStrokeGradient;\n            el.__canvasStrokeGradient = strokeGradient;\n        }\n        if (hasFillPattern) {\n            fillPattern = (dirtyFlag || !el.__canvasFillPattern)\n                ? createCanvasPattern(ctx, fill, el)\n                : el.__canvasFillPattern;\n            el.__canvasFillPattern = fillPattern;\n        }\n        if (hasStrokePattern) {\n            strokePattern = (dirtyFlag || !el.__canvasStrokePattern)\n                ? createCanvasPattern(ctx, stroke, el)\n                : el.__canvasStrokePattern;\n            el.__canvasStrokePattern = fillPattern;\n        }\n        if (hasFillGradient) {\n            ctx.fillStyle = fillGradient;\n        }\n        else if (hasFillPattern) {\n            if (fillPattern) {\n                ctx.fillStyle = fillPattern;\n            }\n            else {\n                hasFill = false;\n            }\n        }\n        if (hasStrokeGradient) {\n            ctx.strokeStyle = strokeGradient;\n        }\n        else if (hasStrokePattern) {\n            if (strokePattern) {\n                ctx.strokeStyle = strokePattern;\n            }\n            else {\n                hasStroke = false;\n            }\n        }\n    }\n    var scale = el.getGlobalScale();\n    path.setScale(scale[0], scale[1], el.segmentIgnoreThreshold);\n    var lineDash;\n    var lineDashOffset;\n    if (ctx.setLineDash && style.lineDash) {\n        _a = getLineDash(el), lineDash = _a[0], lineDashOffset = _a[1];\n    }\n    var needsRebuild = true;\n    if (firstDraw || (dirtyFlag & SHAPE_CHANGED_BIT)) {\n        path.setDPR(ctx.dpr);\n        if (strokePart) {\n            path.setContext(null);\n        }\n        else {\n            path.setContext(ctx);\n            needsRebuild = false;\n        }\n        path.reset();\n        el.buildPath(path, el.shape, inBatch);\n        path.toStatic();\n        el.pathUpdated();\n    }\n    if (needsRebuild) {\n        path.rebuildPath(ctx, strokePart ? strokePercent : 1);\n    }\n    if (lineDash) {\n        ctx.setLineDash(lineDash);\n        ctx.lineDashOffset = lineDashOffset;\n    }\n    if (!inBatch) {\n        if (style.strokeFirst) {\n            if (hasStroke) {\n                doStrokePath(ctx, style);\n            }\n            if (hasFill) {\n                doFillPath(ctx, style);\n            }\n        }\n        else {\n            if (hasFill) {\n                doFillPath(ctx, style);\n            }\n            if (hasStroke) {\n                doStrokePath(ctx, style);\n            }\n        }\n    }\n    if (lineDash) {\n        ctx.setLineDash([]);\n    }\n}\nfunction brushImage(ctx, el, style) {\n    var image = el.__image = createOrUpdateImage(style.image, el.__image, el, el.onload);\n    if (!image || !isImageReady(image)) {\n        return;\n    }\n    var x = style.x || 0;\n    var y = style.y || 0;\n    var width = el.getWidth();\n    var height = el.getHeight();\n    var aspect = image.width / image.height;\n    if (width == null && height != null) {\n        width = height * aspect;\n    }\n    else if (height == null && width != null) {\n        height = width / aspect;\n    }\n    else if (width == null && height == null) {\n        width = image.width;\n        height = image.height;\n    }\n    if (style.sWidth && style.sHeight) {\n        var sx = style.sx || 0;\n        var sy = style.sy || 0;\n        ctx.drawImage(image, sx, sy, style.sWidth, style.sHeight, x, y, width, height);\n    }\n    else if (style.sx && style.sy) {\n        var sx = style.sx;\n        var sy = style.sy;\n        var sWidth = width - sx;\n        var sHeight = height - sy;\n        ctx.drawImage(image, sx, sy, sWidth, sHeight, x, y, width, height);\n    }\n    else {\n        ctx.drawImage(image, x, y, width, height);\n    }\n}\nfunction brushText(ctx, el, style) {\n    var _a;\n    var text = style.text;\n    text != null && (text += '');\n    if (text) {\n        ctx.font = style.font || DEFAULT_FONT;\n        ctx.textAlign = style.textAlign;\n        ctx.textBaseline = style.textBaseline;\n        var lineDash = void 0;\n        var lineDashOffset = void 0;\n        if (ctx.setLineDash && style.lineDash) {\n            _a = getLineDash(el), lineDash = _a[0], lineDashOffset = _a[1];\n        }\n        if (lineDash) {\n            ctx.setLineDash(lineDash);\n            ctx.lineDashOffset = lineDashOffset;\n        }\n        if (style.strokeFirst) {\n            if (styleHasStroke(style)) {\n                ctx.strokeText(text, style.x, style.y);\n            }\n            if (styleHasFill(style)) {\n                ctx.fillText(text, style.x, style.y);\n            }\n        }\n        else {\n            if (styleHasFill(style)) {\n                ctx.fillText(text, style.x, style.y);\n            }\n            if (styleHasStroke(style)) {\n                ctx.strokeText(text, style.x, style.y);\n            }\n        }\n        if (lineDash) {\n            ctx.setLineDash([]);\n        }\n    }\n}\nvar SHADOW_NUMBER_PROPS = ['shadowBlur', 'shadowOffsetX', 'shadowOffsetY'];\nvar STROKE_PROPS = [\n    ['lineCap', 'butt'], ['lineJoin', 'miter'], ['miterLimit', 10]\n];\nfunction bindCommonProps(ctx, style, prevStyle, forceSetAll, scope) {\n    var styleChanged = false;\n    if (!forceSetAll) {\n        prevStyle = prevStyle || {};\n        if (style === prevStyle) {\n            return false;\n        }\n    }\n    if (forceSetAll || style.opacity !== prevStyle.opacity) {\n        flushPathDrawn(ctx, scope);\n        styleChanged = true;\n        var opacity = Math.max(Math.min(style.opacity, 1), 0);\n        ctx.globalAlpha = isNaN(opacity) ? DEFAULT_COMMON_STYLE.opacity : opacity;\n    }\n    if (forceSetAll || style.blend !== prevStyle.blend) {\n        if (!styleChanged) {\n            flushPathDrawn(ctx, scope);\n            styleChanged = true;\n        }\n        ctx.globalCompositeOperation = style.blend || DEFAULT_COMMON_STYLE.blend;\n    }\n    for (var i = 0; i < SHADOW_NUMBER_PROPS.length; i++) {\n        var propName = SHADOW_NUMBER_PROPS[i];\n        if (forceSetAll || style[propName] !== prevStyle[propName]) {\n            if (!styleChanged) {\n                flushPathDrawn(ctx, scope);\n                styleChanged = true;\n            }\n            ctx[propName] = ctx.dpr * (style[propName] || 0);\n        }\n    }\n    if (forceSetAll || style.shadowColor !== prevStyle.shadowColor) {\n        if (!styleChanged) {\n            flushPathDrawn(ctx, scope);\n            styleChanged = true;\n        }\n        ctx.shadowColor = style.shadowColor || DEFAULT_COMMON_STYLE.shadowColor;\n    }\n    return styleChanged;\n}\nfunction bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetAll, scope) {\n    var style = getStyle(el, scope.inHover);\n    var prevStyle = forceSetAll\n        ? null\n        : (prevEl && getStyle(prevEl, scope.inHover) || {});\n    if (style === prevStyle) {\n        return false;\n    }\n    var styleChanged = bindCommonProps(ctx, style, prevStyle, forceSetAll, scope);\n    if (forceSetAll || style.fill !== prevStyle.fill) {\n        if (!styleChanged) {\n            flushPathDrawn(ctx, scope);\n            styleChanged = true;\n        }\n        isValidStrokeFillStyle(style.fill) && (ctx.fillStyle = style.fill);\n    }\n    if (forceSetAll || style.stroke !== prevStyle.stroke) {\n        if (!styleChanged) {\n            flushPathDrawn(ctx, scope);\n            styleChanged = true;\n        }\n        isValidStrokeFillStyle(style.stroke) && (ctx.strokeStyle = style.stroke);\n    }\n    if (forceSetAll || style.opacity !== prevStyle.opacity) {\n        if (!styleChanged) {\n            flushPathDrawn(ctx, scope);\n            styleChanged = true;\n        }\n        ctx.globalAlpha = style.opacity == null ? 1 : style.opacity;\n    }\n    if (el.hasStroke()) {\n        var lineWidth = style.lineWidth;\n        var newLineWidth = lineWidth / ((style.strokeNoScale && el.getLineScale) ? el.getLineScale() : 1);\n        if (ctx.lineWidth !== newLineWidth) {\n            if (!styleChanged) {\n                flushPathDrawn(ctx, scope);\n                styleChanged = true;\n            }\n            ctx.lineWidth = newLineWidth;\n        }\n    }\n    for (var i = 0; i < STROKE_PROPS.length; i++) {\n        var prop = STROKE_PROPS[i];\n        var propName = prop[0];\n        if (forceSetAll || style[propName] !== prevStyle[propName]) {\n            if (!styleChanged) {\n                flushPathDrawn(ctx, scope);\n                styleChanged = true;\n            }\n            ctx[propName] = style[propName] || prop[1];\n        }\n    }\n    return styleChanged;\n}\nfunction bindImageStyle(ctx, el, prevEl, forceSetAll, scope) {\n    return bindCommonProps(ctx, getStyle(el, scope.inHover), prevEl && getStyle(prevEl, scope.inHover), forceSetAll, scope);\n}\nfunction setContextTransform(ctx, el) {\n    var m = el.transform;\n    var dpr = ctx.dpr || 1;\n    if (m) {\n        ctx.setTransform(dpr * m[0], dpr * m[1], dpr * m[2], dpr * m[3], dpr * m[4], dpr * m[5]);\n    }\n    else {\n        ctx.setTransform(dpr, 0, 0, dpr, 0, 0);\n    }\n}\nfunction updateClipStatus(clipPaths, ctx, scope) {\n    var allClipped = false;\n    for (var i = 0; i < clipPaths.length; i++) {\n        var clipPath = clipPaths[i];\n        allClipped = allClipped || clipPath.isZeroArea();\n        setContextTransform(ctx, clipPath);\n        ctx.beginPath();\n        clipPath.buildPath(ctx, clipPath.shape);\n        ctx.clip();\n    }\n    scope.allClipped = allClipped;\n}\nfunction isTransformChanged(m0, m1) {\n    if (m0 && m1) {\n        return m0[0] !== m1[0]\n            || m0[1] !== m1[1]\n            || m0[2] !== m1[2]\n            || m0[3] !== m1[3]\n            || m0[4] !== m1[4]\n            || m0[5] !== m1[5];\n    }\n    else if (!m0 && !m1) {\n        return false;\n    }\n    return true;\n}\nvar DRAW_TYPE_PATH = 1;\nvar DRAW_TYPE_IMAGE = 2;\nvar DRAW_TYPE_TEXT = 3;\nvar DRAW_TYPE_INCREMENTAL = 4;\nfunction canPathBatch(style) {\n    var hasFill = styleHasFill(style);\n    var hasStroke = styleHasStroke(style);\n    return !(style.lineDash\n        || !(+hasFill ^ +hasStroke)\n        || (hasFill && typeof style.fill !== 'string')\n        || (hasStroke && typeof style.stroke !== 'string')\n        || style.strokePercent < 1\n        || style.strokeOpacity < 1\n        || style.fillOpacity < 1);\n}\nfunction flushPathDrawn(ctx, scope) {\n    scope.batchFill && ctx.fill();\n    scope.batchStroke && ctx.stroke();\n    scope.batchFill = '';\n    scope.batchStroke = '';\n}\nfunction getStyle(el, inHover) {\n    return inHover ? (el.__hoverStyle || el.style) : el.style;\n}\nexport function brushSingle(ctx, el) {\n    brush(ctx, el, { inHover: false, viewWidth: 0, viewHeight: 0 }, true);\n}\nexport function brush(ctx, el, scope, isLast) {\n    var m = el.transform;\n    if (!el.shouldBePainted(scope.viewWidth, scope.viewHeight, false, false)) {\n        el.__dirty &= ~REDRAW_BIT;\n        el.__isRendered = false;\n        return;\n    }\n    var clipPaths = el.__clipPaths;\n    var prevElClipPaths = scope.prevElClipPaths;\n    var forceSetTransform = false;\n    var forceSetStyle = false;\n    if (!prevElClipPaths || isClipPathChanged(clipPaths, prevElClipPaths)) {\n        if (prevElClipPaths && prevElClipPaths.length) {\n            flushPathDrawn(ctx, scope);\n            ctx.restore();\n            forceSetStyle = forceSetTransform = true;\n            scope.prevElClipPaths = null;\n            scope.allClipped = false;\n            scope.prevEl = null;\n        }\n        if (clipPaths && clipPaths.length) {\n            flushPathDrawn(ctx, scope);\n            ctx.save();\n            updateClipStatus(clipPaths, ctx, scope);\n            forceSetTransform = true;\n        }\n        scope.prevElClipPaths = clipPaths;\n    }\n    if (scope.allClipped) {\n        el.__isRendered = false;\n        return;\n    }\n    el.beforeBrush && el.beforeBrush();\n    el.innerBeforeBrush();\n    var prevEl = scope.prevEl;\n    if (!prevEl) {\n        forceSetStyle = forceSetTransform = true;\n    }\n    var canBatchPath = el instanceof Path\n        && el.autoBatch\n        && canPathBatch(el.style);\n    if (forceSetTransform || isTransformChanged(m, prevEl.transform)) {\n        flushPathDrawn(ctx, scope);\n        setContextTransform(ctx, el);\n    }\n    else if (!canBatchPath) {\n        flushPathDrawn(ctx, scope);\n    }\n    var style = getStyle(el, scope.inHover);\n    if (el instanceof Path) {\n        if (scope.lastDrawType !== DRAW_TYPE_PATH) {\n            forceSetStyle = true;\n            scope.lastDrawType = DRAW_TYPE_PATH;\n        }\n        bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetStyle, scope);\n        if (!canBatchPath || (!scope.batchFill && !scope.batchStroke)) {\n            ctx.beginPath();\n        }\n        brushPath(ctx, el, style, canBatchPath);\n        if (canBatchPath) {\n            scope.batchFill = style.fill || '';\n            scope.batchStroke = style.stroke || '';\n        }\n    }\n    else {\n        if (el instanceof TSpan) {\n            if (scope.lastDrawType !== DRAW_TYPE_TEXT) {\n                forceSetStyle = true;\n                scope.lastDrawType = DRAW_TYPE_TEXT;\n            }\n            bindPathAndTextCommonStyle(ctx, el, prevEl, forceSetStyle, scope);\n            brushText(ctx, el, style);\n        }\n        else if (el instanceof ZRImage) {\n            if (scope.lastDrawType !== DRAW_TYPE_IMAGE) {\n                forceSetStyle = true;\n                scope.lastDrawType = DRAW_TYPE_IMAGE;\n            }\n            bindImageStyle(ctx, el, prevEl, forceSetStyle, scope);\n            brushImage(ctx, el, style);\n        }\n        else if (el.getTemporalDisplayables) {\n            if (scope.lastDrawType !== DRAW_TYPE_INCREMENTAL) {\n                forceSetStyle = true;\n                scope.lastDrawType = DRAW_TYPE_INCREMENTAL;\n            }\n            brushIncremental(ctx, el, scope);\n        }\n    }\n    if (canBatchPath && isLast) {\n        flushPathDrawn(ctx, scope);\n    }\n    el.innerAfterBrush();\n    el.afterBrush && el.afterBrush();\n    scope.prevEl = el;\n    el.__dirty = 0;\n    el.__isRendered = true;\n}\nfunction brushIncremental(ctx, el, scope) {\n    var displayables = el.getDisplayables();\n    var temporalDisplayables = el.getTemporalDisplayables();\n    ctx.save();\n    var innerScope = {\n        prevElClipPaths: null,\n        prevEl: null,\n        allClipped: false,\n        viewWidth: scope.viewWidth,\n        viewHeight: scope.viewHeight,\n        inHover: scope.inHover\n    };\n    var i;\n    var len;\n    for (i = el.getCursor(), len = displayables.length; i < len; i++) {\n        var displayable = displayables[i];\n        displayable.beforeBrush && displayable.beforeBrush();\n        displayable.innerBeforeBrush();\n        brush(ctx, displayable, innerScope, i === len - 1);\n        displayable.innerAfterBrush();\n        displayable.afterBrush && displayable.afterBrush();\n        innerScope.prevEl = displayable;\n    }\n    for (var i_1 = 0, len_1 = temporalDisplayables.length; i_1 < len_1; i_1++) {\n        var displayable = temporalDisplayables[i_1];\n        displayable.beforeBrush && displayable.beforeBrush();\n        displayable.innerBeforeBrush();\n        brush(ctx, displayable, innerScope, i_1 === len_1 - 1);\n        displayable.innerAfterBrush();\n        displayable.afterBrush && displayable.afterBrush();\n        innerScope.prevEl = displayable;\n    }\n    el.clearTemporalDisplayables();\n    el.notClear = true;\n    ctx.restore();\n}\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,mBAAmB,EAAEC,YAAY,QAAQ,4BAA4B;AAC9E,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,aAAa;AAClE,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,UAAU,EAAEC,iBAAiB,QAAQ,yBAAyB;AACvE,SAASC,YAAY,QAAQ,qBAAqB;AAClD,IAAIC,gBAAgB,GAAG,IAAIb,SAAS,CAAC,IAAI,CAAC;AAC1C,SAASc,cAAcA,CAACC,KAAK,EAAE;EAC3B,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;EACzB,OAAO,EAAEA,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAK,MAAM,IAAI,EAAED,KAAK,CAACE,SAAS,GAAG,CAAC,CAAC,CAAC;AAC3E;AACA,SAASC,sBAAsBA,CAACC,YAAY,EAAE;EAC1C,OAAO,OAAOA,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,MAAM;AACtE;AACA,SAASC,YAAYA,CAACL,KAAK,EAAE;EACzB,IAAIM,IAAI,GAAGN,KAAK,CAACM,IAAI;EACrB,OAAOA,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,MAAM;AAC1C;AACA,SAASC,UAAUA,CAACC,GAAG,EAAER,KAAK,EAAE;EAC5B,IAAIA,KAAK,CAACS,WAAW,IAAI,IAAI,IAAIT,KAAK,CAACS,WAAW,KAAK,CAAC,EAAE;IACtD,IAAIC,mBAAmB,GAAGF,GAAG,CAACG,WAAW;IACzCH,GAAG,CAACG,WAAW,GAAGX,KAAK,CAACS,WAAW,GAAGT,KAAK,CAACY,OAAO;IACnDJ,GAAG,CAACF,IAAI,CAAC,CAAC;IACVE,GAAG,CAACG,WAAW,GAAGD,mBAAmB;EACzC,CAAC,MACI;IACDF,GAAG,CAACF,IAAI,CAAC,CAAC;EACd;AACJ;AACA,SAASO,YAAYA,CAACL,GAAG,EAAER,KAAK,EAAE;EAC9B,IAAIA,KAAK,CAACc,aAAa,IAAI,IAAI,IAAId,KAAK,CAACc,aAAa,KAAK,CAAC,EAAE;IAC1D,IAAIJ,mBAAmB,GAAGF,GAAG,CAACG,WAAW;IACzCH,GAAG,CAACG,WAAW,GAAGX,KAAK,CAACc,aAAa,GAAGd,KAAK,CAACY,OAAO;IACrDJ,GAAG,CAACP,MAAM,CAAC,CAAC;IACZO,GAAG,CAACG,WAAW,GAAGD,mBAAmB;EACzC,CAAC,MACI;IACDF,GAAG,CAACP,MAAM,CAAC,CAAC;EAChB;AACJ;AACA,OAAO,SAASc,mBAAmBA,CAACP,GAAG,EAAEQ,OAAO,EAAEC,EAAE,EAAE;EAClD,IAAIC,KAAK,GAAGhC,mBAAmB,CAAC8B,OAAO,CAACE,KAAK,EAAEF,OAAO,CAACG,OAAO,EAAEF,EAAE,CAAC;EACnE,IAAI9B,YAAY,CAAC+B,KAAK,CAAC,EAAE;IACrB,IAAIE,aAAa,GAAGZ,GAAG,CAACa,aAAa,CAACH,KAAK,EAAEF,OAAO,CAACM,MAAM,IAAI,QAAQ,CAAC;IACxE,IAAI,OAAOC,SAAS,KAAK,UAAU,IAC5BH,aAAa,IACbA,aAAa,CAACI,YAAY,EAAE;MAC/B,IAAIC,MAAM,GAAG,IAAIF,SAAS,CAAC,CAAC;MAC5BE,MAAM,CAACC,aAAa,CAAEV,OAAO,CAACW,CAAC,IAAI,CAAC,EAAIX,OAAO,CAACY,CAAC,IAAI,CAAE,CAAC;MACxDH,MAAM,CAACI,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAACb,OAAO,CAACc,QAAQ,IAAI,CAAC,IAAIrC,gBAAgB,CAAC;MACnEgC,MAAM,CAACM,SAAS,CAAEf,OAAO,CAACgB,MAAM,IAAI,CAAC,EAAIhB,OAAO,CAACiB,MAAM,IAAI,CAAE,CAAC;MAC9Db,aAAa,CAACI,YAAY,CAACC,MAAM,CAAC;IACtC;IACA,OAAOL,aAAa;EACxB;AACJ;AACA,SAASc,SAASA,CAAC1B,GAAG,EAAES,EAAE,EAAEjB,KAAK,EAAEmC,OAAO,EAAE;EACxC,IAAIC,EAAE;EACN,IAAIC,SAAS,GAAGtC,cAAc,CAACC,KAAK,CAAC;EACrC,IAAIsC,OAAO,GAAGjC,YAAY,CAACL,KAAK,CAAC;EACjC,IAAIuC,aAAa,GAAGvC,KAAK,CAACuC,aAAa;EACvC,IAAIC,UAAU,GAAGD,aAAa,GAAG,CAAC;EAClC,IAAIE,SAAS,GAAG,CAACxB,EAAE,CAACyB,IAAI;EACxB,IAAI,CAAC,CAACzB,EAAE,CAAC0B,MAAM,IAAIH,UAAU,KAAKC,SAAS,EAAE;IACzCxB,EAAE,CAAC2B,eAAe,CAAC,CAAC;EACxB;EACA,IAAIF,IAAI,GAAGzB,EAAE,CAACyB,IAAI,IAAI5C,gBAAgB;EACtC,IAAI+C,SAAS,GAAG5B,EAAE,CAAC6B,OAAO;EAC1B,IAAI,CAACX,OAAO,EAAE;IACV,IAAI7B,IAAI,GAAGN,KAAK,CAACM,IAAI;IACrB,IAAIL,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzB,IAAI8C,eAAe,GAAGT,OAAO,IAAI,CAAC,CAAChC,IAAI,CAAC0C,UAAU;IAClD,IAAIC,iBAAiB,GAAGZ,SAAS,IAAI,CAAC,CAACpC,MAAM,CAAC+C,UAAU;IACxD,IAAIE,cAAc,GAAGZ,OAAO,IAAI,CAAC,CAAChC,IAAI,CAACY,KAAK;IAC5C,IAAIiC,gBAAgB,GAAGd,SAAS,IAAI,CAAC,CAACpC,MAAM,CAACiB,KAAK;IAClD,IAAIkC,YAAY,GAAG,KAAK,CAAC;IACzB,IAAIC,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAIC,WAAW,GAAG,KAAK,CAAC;IACxB,IAAIC,aAAa,GAAG,KAAK,CAAC;IAC1B,IAAIC,IAAI,GAAG,KAAK,CAAC;IACjB,IAAIT,eAAe,IAAIE,iBAAiB,EAAE;MACtCO,IAAI,GAAGvC,EAAE,CAACwC,eAAe,CAAC,CAAC;IAC/B;IACA,IAAIV,eAAe,EAAE;MACjBK,YAAY,GAAGP,SAAS,GAClBzD,iBAAiB,CAACoB,GAAG,EAAEF,IAAI,EAAEkD,IAAI,CAAC,GAClCvC,EAAE,CAACyC,oBAAoB;MAC7BzC,EAAE,CAACyC,oBAAoB,GAAGN,YAAY;IAC1C;IACA,IAAIH,iBAAiB,EAAE;MACnBI,cAAc,GAAGR,SAAS,GACpBzD,iBAAiB,CAACoB,GAAG,EAAEP,MAAM,EAAEuD,IAAI,CAAC,GACpCvC,EAAE,CAAC0C,sBAAsB;MAC/B1C,EAAE,CAAC0C,sBAAsB,GAAGN,cAAc;IAC9C;IACA,IAAIH,cAAc,EAAE;MAChBI,WAAW,GAAIT,SAAS,IAAI,CAAC5B,EAAE,CAAC2C,mBAAmB,GAC7C7C,mBAAmB,CAACP,GAAG,EAAEF,IAAI,EAAEW,EAAE,CAAC,GAClCA,EAAE,CAAC2C,mBAAmB;MAC5B3C,EAAE,CAAC2C,mBAAmB,GAAGN,WAAW;IACxC;IACA,IAAIH,gBAAgB,EAAE;MAClBI,aAAa,GAAIV,SAAS,IAAI,CAAC5B,EAAE,CAAC4C,qBAAqB,GACjD9C,mBAAmB,CAACP,GAAG,EAAEP,MAAM,EAAEgB,EAAE,CAAC,GACpCA,EAAE,CAAC4C,qBAAqB;MAC9B5C,EAAE,CAAC4C,qBAAqB,GAAGP,WAAW;IAC1C;IACA,IAAIP,eAAe,EAAE;MACjBvC,GAAG,CAACsD,SAAS,GAAGV,YAAY;IAChC,CAAC,MACI,IAAIF,cAAc,EAAE;MACrB,IAAII,WAAW,EAAE;QACb9C,GAAG,CAACsD,SAAS,GAAGR,WAAW;MAC/B,CAAC,MACI;QACDhB,OAAO,GAAG,KAAK;MACnB;IACJ;IACA,IAAIW,iBAAiB,EAAE;MACnBzC,GAAG,CAACuD,WAAW,GAAGV,cAAc;IACpC,CAAC,MACI,IAAIF,gBAAgB,EAAE;MACvB,IAAII,aAAa,EAAE;QACf/C,GAAG,CAACuD,WAAW,GAAGR,aAAa;MACnC,CAAC,MACI;QACDlB,SAAS,GAAG,KAAK;MACrB;IACJ;EACJ;EACA,IAAI2B,KAAK,GAAG/C,EAAE,CAACgD,cAAc,CAAC,CAAC;EAC/BvB,IAAI,CAACwB,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAE/C,EAAE,CAACkD,sBAAsB,CAAC;EAC5D,IAAIC,QAAQ;EACZ,IAAIC,cAAc;EAClB,IAAI7D,GAAG,CAAC8D,WAAW,IAAItE,KAAK,CAACoE,QAAQ,EAAE;IACnChC,EAAE,GAAG1C,WAAW,CAACuB,EAAE,CAAC,EAAEmD,QAAQ,GAAGhC,EAAE,CAAC,CAAC,CAAC,EAAEiC,cAAc,GAAGjC,EAAE,CAAC,CAAC,CAAC;EAClE;EACA,IAAImC,YAAY,GAAG,IAAI;EACvB,IAAI9B,SAAS,IAAKI,SAAS,GAAGjD,iBAAkB,EAAE;IAC9C8C,IAAI,CAAC8B,MAAM,CAAChE,GAAG,CAACiE,GAAG,CAAC;IACpB,IAAIjC,UAAU,EAAE;MACZE,IAAI,CAACgC,UAAU,CAAC,IAAI,CAAC;IACzB,CAAC,MACI;MACDhC,IAAI,CAACgC,UAAU,CAAClE,GAAG,CAAC;MACpB+D,YAAY,GAAG,KAAK;IACxB;IACA7B,IAAI,CAACiC,KAAK,CAAC,CAAC;IACZ1D,EAAE,CAAC2D,SAAS,CAAClC,IAAI,EAAEzB,EAAE,CAAC4D,KAAK,EAAE1C,OAAO,CAAC;IACrCO,IAAI,CAACoC,QAAQ,CAAC,CAAC;IACf7D,EAAE,CAAC8D,WAAW,CAAC,CAAC;EACpB;EACA,IAAIR,YAAY,EAAE;IACd7B,IAAI,CAACsC,WAAW,CAACxE,GAAG,EAAEgC,UAAU,GAAGD,aAAa,GAAG,CAAC,CAAC;EACzD;EACA,IAAI6B,QAAQ,EAAE;IACV5D,GAAG,CAAC8D,WAAW,CAACF,QAAQ,CAAC;IACzB5D,GAAG,CAAC6D,cAAc,GAAGA,cAAc;EACvC;EACA,IAAI,CAAClC,OAAO,EAAE;IACV,IAAInC,KAAK,CAACiF,WAAW,EAAE;MACnB,IAAI5C,SAAS,EAAE;QACXxB,YAAY,CAACL,GAAG,EAAER,KAAK,CAAC;MAC5B;MACA,IAAIsC,OAAO,EAAE;QACT/B,UAAU,CAACC,GAAG,EAAER,KAAK,CAAC;MAC1B;IACJ,CAAC,MACI;MACD,IAAIsC,OAAO,EAAE;QACT/B,UAAU,CAACC,GAAG,EAAER,KAAK,CAAC;MAC1B;MACA,IAAIqC,SAAS,EAAE;QACXxB,YAAY,CAACL,GAAG,EAAER,KAAK,CAAC;MAC5B;IACJ;EACJ;EACA,IAAIoE,QAAQ,EAAE;IACV5D,GAAG,CAAC8D,WAAW,CAAC,EAAE,CAAC;EACvB;AACJ;AACA,SAASY,UAAUA,CAAC1E,GAAG,EAAES,EAAE,EAAEjB,KAAK,EAAE;EAChC,IAAIkB,KAAK,GAAGD,EAAE,CAACE,OAAO,GAAGjC,mBAAmB,CAACc,KAAK,CAACkB,KAAK,EAAED,EAAE,CAACE,OAAO,EAAEF,EAAE,EAAEA,EAAE,CAACkE,MAAM,CAAC;EACpF,IAAI,CAACjE,KAAK,IAAI,CAAC/B,YAAY,CAAC+B,KAAK,CAAC,EAAE;IAChC;EACJ;EACA,IAAIS,CAAC,GAAG3B,KAAK,CAAC2B,CAAC,IAAI,CAAC;EACpB,IAAIC,CAAC,GAAG5B,KAAK,CAAC4B,CAAC,IAAI,CAAC;EACpB,IAAIwD,KAAK,GAAGnE,EAAE,CAACoE,QAAQ,CAAC,CAAC;EACzB,IAAIC,MAAM,GAAGrE,EAAE,CAACsE,SAAS,CAAC,CAAC;EAC3B,IAAIC,MAAM,GAAGtE,KAAK,CAACkE,KAAK,GAAGlE,KAAK,CAACoE,MAAM;EACvC,IAAIF,KAAK,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAE;IACjCF,KAAK,GAAGE,MAAM,GAAGE,MAAM;EAC3B,CAAC,MACI,IAAIF,MAAM,IAAI,IAAI,IAAIF,KAAK,IAAI,IAAI,EAAE;IACtCE,MAAM,GAAGF,KAAK,GAAGI,MAAM;EAC3B,CAAC,MACI,IAAIJ,KAAK,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAE;IACtCF,KAAK,GAAGlE,KAAK,CAACkE,KAAK;IACnBE,MAAM,GAAGpE,KAAK,CAACoE,MAAM;EACzB;EACA,IAAItF,KAAK,CAACyF,MAAM,IAAIzF,KAAK,CAAC0F,OAAO,EAAE;IAC/B,IAAIC,EAAE,GAAG3F,KAAK,CAAC2F,EAAE,IAAI,CAAC;IACtB,IAAIC,EAAE,GAAG5F,KAAK,CAAC4F,EAAE,IAAI,CAAC;IACtBpF,GAAG,CAACqF,SAAS,CAAC3E,KAAK,EAAEyE,EAAE,EAAEC,EAAE,EAAE5F,KAAK,CAACyF,MAAM,EAAEzF,KAAK,CAAC0F,OAAO,EAAE/D,CAAC,EAAEC,CAAC,EAAEwD,KAAK,EAAEE,MAAM,CAAC;EAClF,CAAC,MACI,IAAItF,KAAK,CAAC2F,EAAE,IAAI3F,KAAK,CAAC4F,EAAE,EAAE;IAC3B,IAAID,EAAE,GAAG3F,KAAK,CAAC2F,EAAE;IACjB,IAAIC,EAAE,GAAG5F,KAAK,CAAC4F,EAAE;IACjB,IAAIH,MAAM,GAAGL,KAAK,GAAGO,EAAE;IACvB,IAAID,OAAO,GAAGJ,MAAM,GAAGM,EAAE;IACzBpF,GAAG,CAACqF,SAAS,CAAC3E,KAAK,EAAEyE,EAAE,EAAEC,EAAE,EAAEH,MAAM,EAAEC,OAAO,EAAE/D,CAAC,EAAEC,CAAC,EAAEwD,KAAK,EAAEE,MAAM,CAAC;EACtE,CAAC,MACI;IACD9E,GAAG,CAACqF,SAAS,CAAC3E,KAAK,EAAES,CAAC,EAAEC,CAAC,EAAEwD,KAAK,EAAEE,MAAM,CAAC;EAC7C;AACJ;AACA,SAASQ,SAASA,CAACtF,GAAG,EAAES,EAAE,EAAEjB,KAAK,EAAE;EAC/B,IAAIoC,EAAE;EACN,IAAI2D,IAAI,GAAG/F,KAAK,CAAC+F,IAAI;EACrBA,IAAI,IAAI,IAAI,KAAKA,IAAI,IAAI,EAAE,CAAC;EAC5B,IAAIA,IAAI,EAAE;IACNvF,GAAG,CAACwF,IAAI,GAAGhG,KAAK,CAACgG,IAAI,IAAInG,YAAY;IACrCW,GAAG,CAACyF,SAAS,GAAGjG,KAAK,CAACiG,SAAS;IAC/BzF,GAAG,CAAC0F,YAAY,GAAGlG,KAAK,CAACkG,YAAY;IACrC,IAAI9B,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAIC,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI7D,GAAG,CAAC8D,WAAW,IAAItE,KAAK,CAACoE,QAAQ,EAAE;MACnChC,EAAE,GAAG1C,WAAW,CAACuB,EAAE,CAAC,EAAEmD,QAAQ,GAAGhC,EAAE,CAAC,CAAC,CAAC,EAAEiC,cAAc,GAAGjC,EAAE,CAAC,CAAC,CAAC;IAClE;IACA,IAAIgC,QAAQ,EAAE;MACV5D,GAAG,CAAC8D,WAAW,CAACF,QAAQ,CAAC;MACzB5D,GAAG,CAAC6D,cAAc,GAAGA,cAAc;IACvC;IACA,IAAIrE,KAAK,CAACiF,WAAW,EAAE;MACnB,IAAIlF,cAAc,CAACC,KAAK,CAAC,EAAE;QACvBQ,GAAG,CAAC2F,UAAU,CAACJ,IAAI,EAAE/F,KAAK,CAAC2B,CAAC,EAAE3B,KAAK,CAAC4B,CAAC,CAAC;MAC1C;MACA,IAAIvB,YAAY,CAACL,KAAK,CAAC,EAAE;QACrBQ,GAAG,CAAC4F,QAAQ,CAACL,IAAI,EAAE/F,KAAK,CAAC2B,CAAC,EAAE3B,KAAK,CAAC4B,CAAC,CAAC;MACxC;IACJ,CAAC,MACI;MACD,IAAIvB,YAAY,CAACL,KAAK,CAAC,EAAE;QACrBQ,GAAG,CAAC4F,QAAQ,CAACL,IAAI,EAAE/F,KAAK,CAAC2B,CAAC,EAAE3B,KAAK,CAAC4B,CAAC,CAAC;MACxC;MACA,IAAI7B,cAAc,CAACC,KAAK,CAAC,EAAE;QACvBQ,GAAG,CAAC2F,UAAU,CAACJ,IAAI,EAAE/F,KAAK,CAAC2B,CAAC,EAAE3B,KAAK,CAAC4B,CAAC,CAAC;MAC1C;IACJ;IACA,IAAIwC,QAAQ,EAAE;MACV5D,GAAG,CAAC8D,WAAW,CAAC,EAAE,CAAC;IACvB;EACJ;AACJ;AACA,IAAI+B,mBAAmB,GAAG,CAAC,YAAY,EAAE,eAAe,EAAE,eAAe,CAAC;AAC1E,IAAIC,YAAY,GAAG,CACf,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,EAAE,CAAC,CACjE;AACD,SAASC,eAAeA,CAAC/F,GAAG,EAAER,KAAK,EAAEwG,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAE;EAChE,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAI,CAACF,WAAW,EAAE;IACdD,SAAS,GAAGA,SAAS,IAAI,CAAC,CAAC;IAC3B,IAAIxG,KAAK,KAAKwG,SAAS,EAAE;MACrB,OAAO,KAAK;IAChB;EACJ;EACA,IAAIC,WAAW,IAAIzG,KAAK,CAACY,OAAO,KAAK4F,SAAS,CAAC5F,OAAO,EAAE;IACpDgG,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;IAC1BC,YAAY,GAAG,IAAI;IACnB,IAAI/F,OAAO,GAAGiG,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC/G,KAAK,CAACY,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACrDJ,GAAG,CAACG,WAAW,GAAGqG,KAAK,CAACpG,OAAO,CAAC,GAAG5B,oBAAoB,CAAC4B,OAAO,GAAGA,OAAO;EAC7E;EACA,IAAI6F,WAAW,IAAIzG,KAAK,CAACiH,KAAK,KAAKT,SAAS,CAACS,KAAK,EAAE;IAChD,IAAI,CAACN,YAAY,EAAE;MACfC,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;MAC1BC,YAAY,GAAG,IAAI;IACvB;IACAnG,GAAG,CAAC0G,wBAAwB,GAAGlH,KAAK,CAACiH,KAAK,IAAIjI,oBAAoB,CAACiI,KAAK;EAC5E;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,mBAAmB,CAACe,MAAM,EAAED,CAAC,EAAE,EAAE;IACjD,IAAIE,QAAQ,GAAGhB,mBAAmB,CAACc,CAAC,CAAC;IACrC,IAAIV,WAAW,IAAIzG,KAAK,CAACqH,QAAQ,CAAC,KAAKb,SAAS,CAACa,QAAQ,CAAC,EAAE;MACxD,IAAI,CAACV,YAAY,EAAE;QACfC,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;QAC1BC,YAAY,GAAG,IAAI;MACvB;MACAnG,GAAG,CAAC6G,QAAQ,CAAC,GAAG7G,GAAG,CAACiE,GAAG,IAAIzE,KAAK,CAACqH,QAAQ,CAAC,IAAI,CAAC,CAAC;IACpD;EACJ;EACA,IAAIZ,WAAW,IAAIzG,KAAK,CAACsH,WAAW,KAAKd,SAAS,CAACc,WAAW,EAAE;IAC5D,IAAI,CAACX,YAAY,EAAE;MACfC,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;MAC1BC,YAAY,GAAG,IAAI;IACvB;IACAnG,GAAG,CAAC8G,WAAW,GAAGtH,KAAK,CAACsH,WAAW,IAAItI,oBAAoB,CAACsI,WAAW;EAC3E;EACA,OAAOX,YAAY;AACvB;AACA,SAASY,0BAA0BA,CAAC/G,GAAG,EAAES,EAAE,EAAEuG,MAAM,EAAEf,WAAW,EAAEC,KAAK,EAAE;EACrE,IAAI1G,KAAK,GAAGyH,QAAQ,CAACxG,EAAE,EAAEyF,KAAK,CAACgB,OAAO,CAAC;EACvC,IAAIlB,SAAS,GAAGC,WAAW,GACrB,IAAI,GACHe,MAAM,IAAIC,QAAQ,CAACD,MAAM,EAAEd,KAAK,CAACgB,OAAO,CAAC,IAAI,CAAC,CAAE;EACvD,IAAI1H,KAAK,KAAKwG,SAAS,EAAE;IACrB,OAAO,KAAK;EAChB;EACA,IAAIG,YAAY,GAAGJ,eAAe,CAAC/F,GAAG,EAAER,KAAK,EAAEwG,SAAS,EAAEC,WAAW,EAAEC,KAAK,CAAC;EAC7E,IAAID,WAAW,IAAIzG,KAAK,CAACM,IAAI,KAAKkG,SAAS,CAAClG,IAAI,EAAE;IAC9C,IAAI,CAACqG,YAAY,EAAE;MACfC,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;MAC1BC,YAAY,GAAG,IAAI;IACvB;IACAxG,sBAAsB,CAACH,KAAK,CAACM,IAAI,CAAC,KAAKE,GAAG,CAACsD,SAAS,GAAG9D,KAAK,CAACM,IAAI,CAAC;EACtE;EACA,IAAImG,WAAW,IAAIzG,KAAK,CAACC,MAAM,KAAKuG,SAAS,CAACvG,MAAM,EAAE;IAClD,IAAI,CAAC0G,YAAY,EAAE;MACfC,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;MAC1BC,YAAY,GAAG,IAAI;IACvB;IACAxG,sBAAsB,CAACH,KAAK,CAACC,MAAM,CAAC,KAAKO,GAAG,CAACuD,WAAW,GAAG/D,KAAK,CAACC,MAAM,CAAC;EAC5E;EACA,IAAIwG,WAAW,IAAIzG,KAAK,CAACY,OAAO,KAAK4F,SAAS,CAAC5F,OAAO,EAAE;IACpD,IAAI,CAAC+F,YAAY,EAAE;MACfC,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;MAC1BC,YAAY,GAAG,IAAI;IACvB;IACAnG,GAAG,CAACG,WAAW,GAAGX,KAAK,CAACY,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGZ,KAAK,CAACY,OAAO;EAC/D;EACA,IAAIK,EAAE,CAACoB,SAAS,CAAC,CAAC,EAAE;IAChB,IAAInC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC/B,IAAIyH,YAAY,GAAGzH,SAAS,IAAKF,KAAK,CAAC4H,aAAa,IAAI3G,EAAE,CAAC4G,YAAY,GAAI5G,EAAE,CAAC4G,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;IACjG,IAAIrH,GAAG,CAACN,SAAS,KAAKyH,YAAY,EAAE;MAChC,IAAI,CAAChB,YAAY,EAAE;QACfC,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;QAC1BC,YAAY,GAAG,IAAI;MACvB;MACAnG,GAAG,CAACN,SAAS,GAAGyH,YAAY;IAChC;EACJ;EACA,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,IAAIW,IAAI,GAAGxB,YAAY,CAACa,CAAC,CAAC;IAC1B,IAAIE,QAAQ,GAAGS,IAAI,CAAC,CAAC,CAAC;IACtB,IAAIrB,WAAW,IAAIzG,KAAK,CAACqH,QAAQ,CAAC,KAAKb,SAAS,CAACa,QAAQ,CAAC,EAAE;MACxD,IAAI,CAACV,YAAY,EAAE;QACfC,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;QAC1BC,YAAY,GAAG,IAAI;MACvB;MACAnG,GAAG,CAAC6G,QAAQ,CAAC,GAAGrH,KAAK,CAACqH,QAAQ,CAAC,IAAIS,IAAI,CAAC,CAAC,CAAC;IAC9C;EACJ;EACA,OAAOnB,YAAY;AACvB;AACA,SAASoB,cAAcA,CAACvH,GAAG,EAAES,EAAE,EAAEuG,MAAM,EAAEf,WAAW,EAAEC,KAAK,EAAE;EACzD,OAAOH,eAAe,CAAC/F,GAAG,EAAEiH,QAAQ,CAACxG,EAAE,EAAEyF,KAAK,CAACgB,OAAO,CAAC,EAAEF,MAAM,IAAIC,QAAQ,CAACD,MAAM,EAAEd,KAAK,CAACgB,OAAO,CAAC,EAAEjB,WAAW,EAAEC,KAAK,CAAC;AAC3H;AACA,SAASsB,mBAAmBA,CAACxH,GAAG,EAAES,EAAE,EAAE;EAClC,IAAIgH,CAAC,GAAGhH,EAAE,CAACiH,SAAS;EACpB,IAAIzD,GAAG,GAAGjE,GAAG,CAACiE,GAAG,IAAI,CAAC;EACtB,IAAIwD,CAAC,EAAE;IACHzH,GAAG,CAACgB,YAAY,CAACiD,GAAG,GAAGwD,CAAC,CAAC,CAAC,CAAC,EAAExD,GAAG,GAAGwD,CAAC,CAAC,CAAC,CAAC,EAAExD,GAAG,GAAGwD,CAAC,CAAC,CAAC,CAAC,EAAExD,GAAG,GAAGwD,CAAC,CAAC,CAAC,CAAC,EAAExD,GAAG,GAAGwD,CAAC,CAAC,CAAC,CAAC,EAAExD,GAAG,GAAGwD,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5F,CAAC,MACI;IACDzH,GAAG,CAACgB,YAAY,CAACiD,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEA,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1C;AACJ;AACA,SAAS0D,gBAAgBA,CAACC,SAAS,EAAE5H,GAAG,EAAEkG,KAAK,EAAE;EAC7C,IAAI2B,UAAU,GAAG,KAAK;EACtB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,SAAS,CAAChB,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAImB,QAAQ,GAAGF,SAAS,CAACjB,CAAC,CAAC;IAC3BkB,UAAU,GAAGA,UAAU,IAAIC,QAAQ,CAACC,UAAU,CAAC,CAAC;IAChDP,mBAAmB,CAACxH,GAAG,EAAE8H,QAAQ,CAAC;IAClC9H,GAAG,CAACgI,SAAS,CAAC,CAAC;IACfF,QAAQ,CAAC1D,SAAS,CAACpE,GAAG,EAAE8H,QAAQ,CAACzD,KAAK,CAAC;IACvCrE,GAAG,CAACiI,IAAI,CAAC,CAAC;EACd;EACA/B,KAAK,CAAC2B,UAAU,GAAGA,UAAU;AACjC;AACA,SAASK,kBAAkBA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAChC,IAAID,EAAE,IAAIC,EAAE,EAAE;IACV,OAAOD,EAAE,CAAC,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,CAAC,IACfD,EAAE,CAAC,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,CAAC,IACfD,EAAE,CAAC,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,CAAC,IACfD,EAAE,CAAC,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,CAAC,IACfD,EAAE,CAAC,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,CAAC,IACfD,EAAE,CAAC,CAAC,CAAC,KAAKC,EAAE,CAAC,CAAC,CAAC;EAC1B,CAAC,MACI,IAAI,CAACD,EAAE,IAAI,CAACC,EAAE,EAAE;IACjB,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,IAAIC,cAAc,GAAG,CAAC;AACtB,IAAIC,eAAe,GAAG,CAAC;AACvB,IAAIC,cAAc,GAAG,CAAC;AACtB,IAAIC,qBAAqB,GAAG,CAAC;AAC7B,SAASC,YAAYA,CAACjJ,KAAK,EAAE;EACzB,IAAIsC,OAAO,GAAGjC,YAAY,CAACL,KAAK,CAAC;EACjC,IAAIqC,SAAS,GAAGtC,cAAc,CAACC,KAAK,CAAC;EACrC,OAAO,EAAEA,KAAK,CAACoE,QAAQ,IAChB,EAAE,CAAC9B,OAAO,GAAG,CAACD,SAAS,CAAC,IACvBC,OAAO,IAAI,OAAOtC,KAAK,CAACM,IAAI,KAAK,QAAS,IAC1C+B,SAAS,IAAI,OAAOrC,KAAK,CAACC,MAAM,KAAK,QAAS,IAC/CD,KAAK,CAACuC,aAAa,GAAG,CAAC,IACvBvC,KAAK,CAACc,aAAa,GAAG,CAAC,IACvBd,KAAK,CAACS,WAAW,GAAG,CAAC,CAAC;AACjC;AACA,SAASmG,cAAcA,CAACpG,GAAG,EAAEkG,KAAK,EAAE;EAChCA,KAAK,CAACwC,SAAS,IAAI1I,GAAG,CAACF,IAAI,CAAC,CAAC;EAC7BoG,KAAK,CAACyC,WAAW,IAAI3I,GAAG,CAACP,MAAM,CAAC,CAAC;EACjCyG,KAAK,CAACwC,SAAS,GAAG,EAAE;EACpBxC,KAAK,CAACyC,WAAW,GAAG,EAAE;AAC1B;AACA,SAAS1B,QAAQA,CAACxG,EAAE,EAAEyG,OAAO,EAAE;EAC3B,OAAOA,OAAO,GAAIzG,EAAE,CAACmI,YAAY,IAAInI,EAAE,CAACjB,KAAK,GAAIiB,EAAE,CAACjB,KAAK;AAC7D;AACA,OAAO,SAASqJ,WAAWA,CAAC7I,GAAG,EAAES,EAAE,EAAE;EACjCqI,KAAK,CAAC9I,GAAG,EAAES,EAAE,EAAE;IAAEyG,OAAO,EAAE,KAAK;IAAE6B,SAAS,EAAE,CAAC;IAAEC,UAAU,EAAE;EAAE,CAAC,EAAE,IAAI,CAAC;AACzE;AACA,OAAO,SAASF,KAAKA,CAAC9I,GAAG,EAAES,EAAE,EAAEyF,KAAK,EAAE+C,MAAM,EAAE;EAC1C,IAAIxB,CAAC,GAAGhH,EAAE,CAACiH,SAAS;EACpB,IAAI,CAACjH,EAAE,CAACyI,eAAe,CAAChD,KAAK,CAAC6C,SAAS,EAAE7C,KAAK,CAAC8C,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE;IACtEvI,EAAE,CAAC6B,OAAO,IAAI,CAACnD,UAAU;IACzBsB,EAAE,CAAC0I,YAAY,GAAG,KAAK;IACvB;EACJ;EACA,IAAIvB,SAAS,GAAGnH,EAAE,CAAC2I,WAAW;EAC9B,IAAIC,eAAe,GAAGnD,KAAK,CAACmD,eAAe;EAC3C,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAI,CAACF,eAAe,IAAIxK,iBAAiB,CAAC+I,SAAS,EAAEyB,eAAe,CAAC,EAAE;IACnE,IAAIA,eAAe,IAAIA,eAAe,CAACzC,MAAM,EAAE;MAC3CR,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;MAC1BlG,GAAG,CAACwJ,OAAO,CAAC,CAAC;MACbD,aAAa,GAAGD,iBAAiB,GAAG,IAAI;MACxCpD,KAAK,CAACmD,eAAe,GAAG,IAAI;MAC5BnD,KAAK,CAAC2B,UAAU,GAAG,KAAK;MACxB3B,KAAK,CAACc,MAAM,GAAG,IAAI;IACvB;IACA,IAAIY,SAAS,IAAIA,SAAS,CAAChB,MAAM,EAAE;MAC/BR,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;MAC1BlG,GAAG,CAACyJ,IAAI,CAAC,CAAC;MACV9B,gBAAgB,CAACC,SAAS,EAAE5H,GAAG,EAAEkG,KAAK,CAAC;MACvCoD,iBAAiB,GAAG,IAAI;IAC5B;IACApD,KAAK,CAACmD,eAAe,GAAGzB,SAAS;EACrC;EACA,IAAI1B,KAAK,CAAC2B,UAAU,EAAE;IAClBpH,EAAE,CAAC0I,YAAY,GAAG,KAAK;IACvB;EACJ;EACA1I,EAAE,CAACiJ,WAAW,IAAIjJ,EAAE,CAACiJ,WAAW,CAAC,CAAC;EAClCjJ,EAAE,CAACkJ,gBAAgB,CAAC,CAAC;EACrB,IAAI3C,MAAM,GAAGd,KAAK,CAACc,MAAM;EACzB,IAAI,CAACA,MAAM,EAAE;IACTuC,aAAa,GAAGD,iBAAiB,GAAG,IAAI;EAC5C;EACA,IAAIM,YAAY,GAAGnJ,EAAE,YAAY3B,IAAI,IAC9B2B,EAAE,CAACoJ,SAAS,IACZpB,YAAY,CAAChI,EAAE,CAACjB,KAAK,CAAC;EAC7B,IAAI8J,iBAAiB,IAAIpB,kBAAkB,CAACT,CAAC,EAAET,MAAM,CAACU,SAAS,CAAC,EAAE;IAC9DtB,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;IAC1BsB,mBAAmB,CAACxH,GAAG,EAAES,EAAE,CAAC;EAChC,CAAC,MACI,IAAI,CAACmJ,YAAY,EAAE;IACpBxD,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;EAC9B;EACA,IAAI1G,KAAK,GAAGyH,QAAQ,CAACxG,EAAE,EAAEyF,KAAK,CAACgB,OAAO,CAAC;EACvC,IAAIzG,EAAE,YAAY3B,IAAI,EAAE;IACpB,IAAIoH,KAAK,CAAC4D,YAAY,KAAKzB,cAAc,EAAE;MACvCkB,aAAa,GAAG,IAAI;MACpBrD,KAAK,CAAC4D,YAAY,GAAGzB,cAAc;IACvC;IACAtB,0BAA0B,CAAC/G,GAAG,EAAES,EAAE,EAAEuG,MAAM,EAAEuC,aAAa,EAAErD,KAAK,CAAC;IACjE,IAAI,CAAC0D,YAAY,IAAK,CAAC1D,KAAK,CAACwC,SAAS,IAAI,CAACxC,KAAK,CAACyC,WAAY,EAAE;MAC3D3I,GAAG,CAACgI,SAAS,CAAC,CAAC;IACnB;IACAtG,SAAS,CAAC1B,GAAG,EAAES,EAAE,EAAEjB,KAAK,EAAEoK,YAAY,CAAC;IACvC,IAAIA,YAAY,EAAE;MACd1D,KAAK,CAACwC,SAAS,GAAGlJ,KAAK,CAACM,IAAI,IAAI,EAAE;MAClCoG,KAAK,CAACyC,WAAW,GAAGnJ,KAAK,CAACC,MAAM,IAAI,EAAE;IAC1C;EACJ,CAAC,MACI;IACD,IAAIgB,EAAE,YAAYzB,KAAK,EAAE;MACrB,IAAIkH,KAAK,CAAC4D,YAAY,KAAKvB,cAAc,EAAE;QACvCgB,aAAa,GAAG,IAAI;QACpBrD,KAAK,CAAC4D,YAAY,GAAGvB,cAAc;MACvC;MACAxB,0BAA0B,CAAC/G,GAAG,EAAES,EAAE,EAAEuG,MAAM,EAAEuC,aAAa,EAAErD,KAAK,CAAC;MACjEZ,SAAS,CAACtF,GAAG,EAAES,EAAE,EAAEjB,KAAK,CAAC;IAC7B,CAAC,MACI,IAAIiB,EAAE,YAAY1B,OAAO,EAAE;MAC5B,IAAImH,KAAK,CAAC4D,YAAY,KAAKxB,eAAe,EAAE;QACxCiB,aAAa,GAAG,IAAI;QACpBrD,KAAK,CAAC4D,YAAY,GAAGxB,eAAe;MACxC;MACAf,cAAc,CAACvH,GAAG,EAAES,EAAE,EAAEuG,MAAM,EAAEuC,aAAa,EAAErD,KAAK,CAAC;MACrDxB,UAAU,CAAC1E,GAAG,EAAES,EAAE,EAAEjB,KAAK,CAAC;IAC9B,CAAC,MACI,IAAIiB,EAAE,CAACsJ,uBAAuB,EAAE;MACjC,IAAI7D,KAAK,CAAC4D,YAAY,KAAKtB,qBAAqB,EAAE;QAC9Ce,aAAa,GAAG,IAAI;QACpBrD,KAAK,CAAC4D,YAAY,GAAGtB,qBAAqB;MAC9C;MACAwB,gBAAgB,CAAChK,GAAG,EAAES,EAAE,EAAEyF,KAAK,CAAC;IACpC;EACJ;EACA,IAAI0D,YAAY,IAAIX,MAAM,EAAE;IACxB7C,cAAc,CAACpG,GAAG,EAAEkG,KAAK,CAAC;EAC9B;EACAzF,EAAE,CAACwJ,eAAe,CAAC,CAAC;EACpBxJ,EAAE,CAACyJ,UAAU,IAAIzJ,EAAE,CAACyJ,UAAU,CAAC,CAAC;EAChChE,KAAK,CAACc,MAAM,GAAGvG,EAAE;EACjBA,EAAE,CAAC6B,OAAO,GAAG,CAAC;EACd7B,EAAE,CAAC0I,YAAY,GAAG,IAAI;AAC1B;AACA,SAASa,gBAAgBA,CAAChK,GAAG,EAAES,EAAE,EAAEyF,KAAK,EAAE;EACtC,IAAIiE,YAAY,GAAG1J,EAAE,CAAC2J,eAAe,CAAC,CAAC;EACvC,IAAIC,oBAAoB,GAAG5J,EAAE,CAACsJ,uBAAuB,CAAC,CAAC;EACvD/J,GAAG,CAACyJ,IAAI,CAAC,CAAC;EACV,IAAIa,UAAU,GAAG;IACbjB,eAAe,EAAE,IAAI;IACrBrC,MAAM,EAAE,IAAI;IACZa,UAAU,EAAE,KAAK;IACjBkB,SAAS,EAAE7C,KAAK,CAAC6C,SAAS;IAC1BC,UAAU,EAAE9C,KAAK,CAAC8C,UAAU;IAC5B9B,OAAO,EAAEhB,KAAK,CAACgB;EACnB,CAAC;EACD,IAAIP,CAAC;EACL,IAAI4D,GAAG;EACP,KAAK5D,CAAC,GAAGlG,EAAE,CAAC+J,SAAS,CAAC,CAAC,EAAED,GAAG,GAAGJ,YAAY,CAACvD,MAAM,EAAED,CAAC,GAAG4D,GAAG,EAAE5D,CAAC,EAAE,EAAE;IAC9D,IAAI8D,WAAW,GAAGN,YAAY,CAACxD,CAAC,CAAC;IACjC8D,WAAW,CAACf,WAAW,IAAIe,WAAW,CAACf,WAAW,CAAC,CAAC;IACpDe,WAAW,CAACd,gBAAgB,CAAC,CAAC;IAC9Bb,KAAK,CAAC9I,GAAG,EAAEyK,WAAW,EAAEH,UAAU,EAAE3D,CAAC,KAAK4D,GAAG,GAAG,CAAC,CAAC;IAClDE,WAAW,CAACR,eAAe,CAAC,CAAC;IAC7BQ,WAAW,CAACP,UAAU,IAAIO,WAAW,CAACP,UAAU,CAAC,CAAC;IAClDI,UAAU,CAACtD,MAAM,GAAGyD,WAAW;EACnC;EACA,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEC,KAAK,GAAGN,oBAAoB,CAACzD,MAAM,EAAE8D,GAAG,GAAGC,KAAK,EAAED,GAAG,EAAE,EAAE;IACvE,IAAID,WAAW,GAAGJ,oBAAoB,CAACK,GAAG,CAAC;IAC3CD,WAAW,CAACf,WAAW,IAAIe,WAAW,CAACf,WAAW,CAAC,CAAC;IACpDe,WAAW,CAACd,gBAAgB,CAAC,CAAC;IAC9Bb,KAAK,CAAC9I,GAAG,EAAEyK,WAAW,EAAEH,UAAU,EAAEI,GAAG,KAAKC,KAAK,GAAG,CAAC,CAAC;IACtDF,WAAW,CAACR,eAAe,CAAC,CAAC;IAC7BQ,WAAW,CAACP,UAAU,IAAIO,WAAW,CAACP,UAAU,CAAC,CAAC;IAClDI,UAAU,CAACtD,MAAM,GAAGyD,WAAW;EACnC;EACAhK,EAAE,CAACmK,yBAAyB,CAAC,CAAC;EAC9BnK,EAAE,CAACoK,QAAQ,GAAG,IAAI;EAClB7K,GAAG,CAACwJ,OAAO,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}