{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, assert } from 'zrender/lib/core/util.js';\nimport { isComponentIdInternal } from '../util/model.js';\nvar internalOptionCreatorMap = createHashMap();\nexport function registerInternalOptionCreator(mainType, creator) {\n  assert(internalOptionCreatorMap.get(mainType) == null && creator);\n  internalOptionCreatorMap.set(mainType, creator);\n}\nexport function concatInternalOptions(ecModel, mainType, newCmptOptionList) {\n  var internalOptionCreator = internalOptionCreatorMap.get(mainType);\n  if (!internalOptionCreator) {\n    return newCmptOptionList;\n  }\n  var internalOptions = internalOptionCreator(ecModel);\n  if (!internalOptions) {\n    return newCmptOptionList;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    for (var i = 0; i < internalOptions.length; i++) {\n      assert(isComponentIdInternal(internalOptions[i]));\n    }\n  }\n  return newCmptOptionList.concat(internalOptions);\n}", "map": {"version": 3, "names": ["createHashMap", "assert", "isComponentIdInternal", "internalOptionCreatorMap", "registerInternalOptionCreator", "mainType", "creator", "get", "set", "concatInternalOptions", "ecModel", "newCmptOptionList", "internalOptionCreator", "internalOptions", "process", "env", "NODE_ENV", "i", "length", "concat"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/model/internalComponentCreator.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, assert } from 'zrender/lib/core/util.js';\nimport { isComponentIdInternal } from '../util/model.js';\nvar internalOptionCreatorMap = createHashMap();\nexport function registerInternalOptionCreator(mainType, creator) {\n  assert(internalOptionCreatorMap.get(mainType) == null && creator);\n  internalOptionCreatorMap.set(mainType, creator);\n}\nexport function concatInternalOptions(ecModel, mainType, newCmptOptionList) {\n  var internalOptionCreator = internalOptionCreatorMap.get(mainType);\n  if (!internalOptionCreator) {\n    return newCmptOptionList;\n  }\n  var internalOptions = internalOptionCreator(ecModel);\n  if (!internalOptions) {\n    return newCmptOptionList;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    for (var i = 0; i < internalOptions.length; i++) {\n      assert(isComponentIdInternal(internalOptions[i]));\n    }\n  }\n  return newCmptOptionList.concat(internalOptions);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,MAAM,QAAQ,0BAA0B;AAChE,SAASC,qBAAqB,QAAQ,kBAAkB;AACxD,IAAIC,wBAAwB,GAAGH,aAAa,CAAC,CAAC;AAC9C,OAAO,SAASI,6BAA6BA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC/DL,MAAM,CAACE,wBAAwB,CAACI,GAAG,CAACF,QAAQ,CAAC,IAAI,IAAI,IAAIC,OAAO,CAAC;EACjEH,wBAAwB,CAACK,GAAG,CAACH,QAAQ,EAAEC,OAAO,CAAC;AACjD;AACA,OAAO,SAASG,qBAAqBA,CAACC,OAAO,EAAEL,QAAQ,EAAEM,iBAAiB,EAAE;EAC1E,IAAIC,qBAAqB,GAAGT,wBAAwB,CAACI,GAAG,CAACF,QAAQ,CAAC;EAClE,IAAI,CAACO,qBAAqB,EAAE;IAC1B,OAAOD,iBAAiB;EAC1B;EACA,IAAIE,eAAe,GAAGD,qBAAqB,CAACF,OAAO,CAAC;EACpD,IAAI,CAACG,eAAe,EAAE;IACpB,OAAOF,iBAAiB;EAC1B;EACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,eAAe,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/ChB,MAAM,CAACC,qBAAqB,CAACW,eAAe,CAACI,CAAC,CAAC,CAAC,CAAC;IACnD;EACF;EACA,OAAON,iBAAiB,CAACQ,MAAM,CAACN,eAAe,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}