{"ast": null, "code": "import addDays from \"../addDays/index.js\";\nimport isSameDay from \"../isSameDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isTomorrow\n * @category Day Helpers\n * @summary Is the given date tomorrow?\n * @pure false\n *\n * @description\n * Is the given date tomorrow?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is tomorrow\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 7 October 14:00:00 tomorrow?\n * const result = isTomorrow(new Date(2014, 9, 7, 14, 0))\n * //=> true\n */\nexport default function isTomorrow(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameDay(dirtyDate, addDays(Date.now(), 1));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}