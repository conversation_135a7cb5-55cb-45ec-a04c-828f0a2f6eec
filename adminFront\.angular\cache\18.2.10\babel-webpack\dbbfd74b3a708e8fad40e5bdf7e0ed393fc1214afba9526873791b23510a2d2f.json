{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nfunction SettingTimePeriodComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_container_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i_r3 + 1, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_td_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 43);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_tr_55_td_3_span_3_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const itm_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      const dialog_r7 = i0.ɵɵreference(59);\n      return i0.ɵɵresetView(ctx_r5.openModel(dialog_r7, itm_r5));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"getSettingTimeStatus\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itm_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(itm_r5.CHouseId ? \"cursor-pointer text-blue-800 block min-w-[100px]\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 3, itm_r5.CChangeStartDate, itm_r5.CChangeEndDate, ctx_r5.isStatus), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_td_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_td_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_tr_55_td_3_span_3_Template, 3, 7, \"span\", 41)(4, SettingTimePeriodComponent_tr_55_td_3_span_4_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itm_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", itm_r5.CHouseHold, \"-\", itm_r5.CFloor, \"F\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", itm_r5.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !itm_r5.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_tr_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_tr_55_td_3_Template, 5, 4, \"td\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", item_r8[0].CFloor, \"F\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", item_r8);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 44);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 45);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 44)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 46);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 47)(7, \"div\", 14)(8, \"label\", 15);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 48);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 16);\n    i0.ɵɵelement(13, \"nb-icon\", 17);\n    i0.ɵɵelementStart(14, \"input\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_58_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r5.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 19, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 50);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 17);\n    i0.ɵɵelementStart(21, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_58_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r5.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 19, 5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 45)(25, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_58_Template_button_click_25_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onClose(ref_r10));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_58_Template_button_click_27_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onSubmit(ref_r10));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r11 = i0.ɵɵreference(16);\n    const changeEndDate_r12 = i0.ɵɵreference(23);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r5.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r5.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r12);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.isStatus = true;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  getHouseChangeDate() {\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n        }\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  static {\n    this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingTimePeriodComponent,\n      selectors: [[\"ngx-setting-time-period\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 60,\n      vars: 12,\n      consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-md-7\", \"max-sm:col-md-12\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [1, \"form-check\", \"mx-2\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault1\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault1\", 1, \"form-check-label\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault2\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault2\", 1, \"form-check-label\"], [1, \"col-md-5\", \"max-sm:col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"text-center\"], [2, \"display\", \"block\", \"min-width\", \"100px\"], [\"style\", \"display: block; min-width: 100px\", 3, \"class\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [2, \"display\", \"block\", \"min-width\", \"100px\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"text-red-600\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n      template: function SettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 6)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 7);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 8)(7, \"div\", 9)(8, \"div\", 10)(9, \"label\", 11);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵtemplate(12, SettingTimePeriodComponent_nb_option_12_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 14)(15, \"label\", 15);\n          i0.ɵɵtext(16, \"\\u958B\\u653E\\u65E5\\u671F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-form-field\", 16);\n          i0.ɵɵelement(18, \"nb-icon\", 17);\n          i0.ɵɵelementStart(19, \"input\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"nb-datepicker\", 19, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"label\", 20);\n          i0.ɵɵtext(23, \" ~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"nb-form-field\");\n          i0.ɵɵelement(25, \"nb-icon\", 17);\n          i0.ɵɵelementStart(26, \"input\", 21);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_26_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"nb-datepicker\", 19, 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 22)(30, \"div\", 10)(31, \"label\", 23);\n          i0.ɵɵtext(32, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 24)(34, \"input\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"label\", 26);\n          i0.ɵɵtext(36, \" \\u4F9D\\u958B\\u653E\\u6642\\u6BB5\\u986F\\u793A \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 24)(38, \"input\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"label\", 28);\n          i0.ɵɵtext(40, \" \\u4F9D\\u958B\\u653E\\u72C0\\u614B\\u986F\\u793A \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 29)(42, \"div\", 30)(43, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_43_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵtext(44, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(45, \"i\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_46_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNavigateWithId());\n          });\n          i0.ɵɵtext(47, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(48, \"div\", 34)(49, \"table\", 35)(50, \"thead\")(51, \"tr\", 36);\n          i0.ɵɵelement(52, \"th\");\n          i0.ɵɵtemplate(53, SettingTimePeriodComponent_ng_container_53_Template, 3, 1, \"ng-container\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"tbody\");\n          i0.ɵɵtemplate(55, SettingTimePeriodComponent_tr_55_Template, 4, 2, \"tr\", 37);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(56, SettingTimePeriodComponent_ng_template_56_Template, 4, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(58, SettingTimePeriodComponent_ng_template_58_Template, 29, 6, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const StartDate_r13 = i0.ɵɵreference(21);\n          const EndDate_r14 = i0.ɵɵreference(28);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", StartDate_r13);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", EndDate_r14);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseChangeDates);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.convertedHouseArray);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, SharedModule, i9.DefaultValueAccessor, i9.RadioControlValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, RadioButtonModule, NbDatepickerModule, NbDateFnsDateModule, SettingTimeStatusPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJzZXR0aW5nLXRpbWUtcGVyaW9kLmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvc2V0dGluZy10aW1lLXBlcmlvZC9zZXR0aW5nLXRpbWUtcGVyaW9kLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxvTEFBb0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "RadioButtonModule", "SettingTimeStatusPipe", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵelementContainerStart", "i_r3", "ɵɵlistener", "SettingTimePeriodComponent_tr_55_td_3_span_3_Template_span_click_0_listener", "ɵɵrestoreView", "_r4", "itm_r5", "ɵɵnextContext", "$implicit", "ctx_r5", "dialog_r7", "ɵɵreference", "ɵɵresetView", "openModel", "ɵɵclassMap", "CHouseId", "ɵɵpipeBind3", "CChangeStartDate", "CChangeEndDate", "isStatus", "ɵɵtemplate", "SettingTimePeriodComponent_tr_55_td_3_span_3_Template", "SettingTimePeriodComponent_tr_55_td_3_span_4_Template", "ɵɵtextInterpolate2", "CHouseHold", "CFloor", "SettingTimePeriodComponent_tr_55_td_3_Template", "item_r8", "ɵɵelement", "ɵɵtwoWayListener", "SettingTimePeriodComponent_ng_template_58_Template_input_ngModelChange_14_listener", "$event", "_r9", "ɵɵtwoWayBindingSet", "selectedHouseChangeDate", "SettingTimePeriodComponent_ng_template_58_Template_input_ngModelChange_21_listener", "SettingTimePeriodComponent_ng_template_58_Template_button_click_25_listener", "ref_r10", "dialogRef", "onClose", "SettingTimePeriodComponent_ng_template_58_Template_button_click_27_listener", "onSubmit", "changeStartDate_r11", "ɵɵtwoWayProperty", "changeEndDate_r12", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "undefined", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "searchQuery", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "validation", "errorMessages", "length", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "for<PERSON>ach", "household", "CHouses", "house", "floor", "push", "floors", "sort", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "startDate", "endDate", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "houseChangeDates", "convertedHouseArray", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "Router", "i7", "EventService", "selectors", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingTimePeriodComponent_Template", "rf", "ctx", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "SettingTimePeriodComponent_nb_option_12_Template", "SettingTimePeriodComponent_Template_input_ngModelChange_19_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_26_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_34_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_38_listener", "SettingTimePeriodComponent_Template_button_click_43_listener", "SettingTimePeriodComponent_Template_button_click_46_listener", "SettingTimePeriodComponent_ng_container_53_Template", "SettingTimePeriodComponent_tr_55_Template", "SettingTimePeriodComponent_ng_template_56_Template", "ɵɵtemplateRefExtractor", "SettingTimePeriodComponent_ng_template_58_Template", "StartDate_r13", "EndDate_r14", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "RadioControlValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string;\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule, RadioButtonModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  isStatus: boolean = true;\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"getHouseChangeDate()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">開放日期\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"StartDate\"\r\n              class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n            <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\"> ~\r\n          </label>\r\n          <nb-form-field>\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"EndDate\" class=\"w-full col-4\"\r\n              [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n            <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-7 max-sm:col-md-12\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cSignStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <div class=\"form-check mx-2\">\r\n            <input class=\"form-check-input\" type=\"radio\" name=\"flexRadioDefault\" id=\"flexRadioDefault1\" [value]=\"true\"\r\n              [(ngModel)]=\"isStatus\">\r\n            <label class=\"form-check-label\" for=\"flexRadioDefault1\">\r\n              依開放時段顯示\r\n            </label>\r\n          </div>\r\n          <div class=\"form-check mx-2\">\r\n            <input class=\"form-check-input\" type=\"radio\" name=\"flexRadioDefault\" id=\"flexRadioDefault2\" [value]=\"false\"\r\n              [(ngModel)]=\"isStatus\">\r\n            <label class=\"form-check-label\" for=\"flexRadioDefault2\">\r\n              依開放狀態顯示\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-5 max-sm:col-md-12 \">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"getHouseChangeDate()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-info btn-sm\" (click)=\"onNavigateWithId()\">\r\n            批次設定\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th></th>\r\n            <ng-container *ngFor=\"let item of houseChangeDates ; let i = index\">\r\n              <th> {{i+1}} </th>\r\n            </ng-container>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of convertedHouseArray ; let i = index\">\r\n            <td class=\"text-center\">{{ item[0].CFloor}}F</td>\r\n            <td *ngFor=\"let itm of item ; let i = index\">\r\n              <span style=\"display: block; min-width: 100px\">{{itm.CHouseHold}}-{{itm.CFloor}}F</span>\r\n              <span (click)=\"openModel(dialog, itm)\" *ngIf=\"itm.CHouseId\"\r\n                [class]=\"itm.CHouseId ? 'cursor-pointer text-blue-800 block min-w-[100px]' : ''\"\r\n                style=\"display: block; min-width: 100px\">\r\n                {{ itm.CChangeStartDate | getSettingTimeStatus:itm.CChangeEndDate: isStatus }}\r\n              </span>\r\n              <span *ngIf=\"!itm.CHouseId\">已停用</span>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      您正在編輯 <span class=\"text-green-600\">{{selectedHouseChangeDate.CHouseHold}} -\r\n        {{selectedHouseChangeDate.CFloor}}F</span> 的選樣開放時段\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cFloorFrom\" class=\"label col-3\">開放時間起訖<span class=\"text-red-600\">*</span>\r\n        </label>\r\n        <nb-form-field class=\"ml-3\">\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeStartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeStartDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeStartDate\">\r\n          <nb-datepicker #changeStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n        <label for=\"CChangeStartDate\" class=\"label col-1\"> ~\r\n        </label>\r\n        <nb-form-field>\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeEndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeEndDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeEndDate\">\r\n          <nb-datepicker #changeEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AAMvD,SAASC,qBAAqB,QAAQ,mCAAmC;AAEzE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;ICJxEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IA+DAR,EAAA,CAAAS,uBAAA,GAAoE;IAClET,EAAA,CAAAC,cAAA,SAAI;IAACD,EAAA,CAAAE,MAAA,GAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAAbH,EAAA,CAAAM,SAAA,GAAQ;IAARN,EAAA,CAAAO,kBAAA,MAAAG,IAAA,UAAQ;;;;;;IASbV,EAAA,CAAAC,cAAA,eAE2C;IAFrCD,EAAA,CAAAW,UAAA,mBAAAC,4EAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,MAAAG,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASH,MAAA,CAAAI,SAAA,CAAAH,SAAA,EAAAJ,MAAA,CAAsB;IAAA,EAAC;IAGpCf,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHLH,EAAA,CAAAuB,UAAA,CAAAR,MAAA,CAAAS,QAAA,2DAAgF;IAEhFxB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAyB,WAAA,OAAAV,MAAA,CAAAW,gBAAA,EAAAX,MAAA,CAAAY,cAAA,EAAAT,MAAA,CAAAU,QAAA,OACF;;;;;IACA5B,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IANtCH,EADF,CAAAC,cAAA,SAA6C,eACI;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAMxFH,EALA,CAAA6B,UAAA,IAAAC,qDAAA,mBAE2C,IAAAC,qDAAA,mBAGf;IAC9B/B,EAAA,CAAAG,YAAA,EAAK;;;;IAP4CH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAgC,kBAAA,KAAAjB,MAAA,CAAAkB,UAAA,OAAAlB,MAAA,CAAAmB,MAAA,MAAkC;IACzClC,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAS,QAAA,CAAkB;IAKnDxB,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAI,UAAA,UAAAW,MAAA,CAAAS,QAAA,CAAmB;;;;;IAR5BxB,EADF,CAAAC,cAAA,SAA6D,aACnC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAA6B,UAAA,IAAAM,8CAAA,iBAA6C;IAS/CnC,EAAA,CAAAG,YAAA,EAAK;;;;IAVqBH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAO,kBAAA,KAAA6B,OAAA,IAAAF,MAAA,MAAoB;IACxBlC,EAAA,CAAAM,SAAA,EAAU;IAAVN,EAAA,CAAAI,UAAA,YAAAgC,OAAA,CAAU;;;;;IAiBxCpC,EAAA,CAAAC,cAAA,kBAA+C;IAM7CD,EALA,CAAAqC,SAAA,qBACiB,mBAGF,yBAGE;IACnBrC,EAAA,CAAAG,YAAA,EAAU;;;;;;IAKRH,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GACE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mDAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,uBAA2B,cACyB,gBACJ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC7E;IACRH,EAAA,CAAAC,cAAA,yBAA4B;IAC1BD,EAAA,CAAAqC,SAAA,mBAAoD;IACpDrC,EAAA,CAAAC,cAAA,iBAC8E;IAAvDD,EAAA,CAAAsC,gBAAA,2BAAAC,mFAAAC,MAAA;MAAAxC,EAAA,CAAAa,aAAA,CAAA4B,GAAA;MAAA,MAAAvB,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAA0C,kBAAA,CAAAxB,MAAA,CAAAyB,uBAAA,CAAAjB,gBAAA,EAAAc,MAAA,MAAAtB,MAAA,CAAAyB,uBAAA,CAAAjB,gBAAA,GAAAc,MAAA;MAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;IAAA,EAAsD;IAD7ExC,EAAA,CAAAG,YAAA,EAC8E;IAC9EH,EAAA,CAAAqC,SAAA,4BAAoE;IACtErC,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,iBAAkD;IAACD,EAAA,CAAAE,MAAA,WACnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAqC,SAAA,mBAAoD;IACpDrC,EAAA,CAAAC,cAAA,iBAC4E;IAArDD,EAAA,CAAAsC,gBAAA,2BAAAM,mFAAAJ,MAAA;MAAAxC,EAAA,CAAAa,aAAA,CAAA4B,GAAA;MAAA,MAAAvB,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAA0C,kBAAA,CAAAxB,MAAA,CAAAyB,uBAAA,CAAAhB,cAAA,EAAAa,MAAA,MAAAtB,MAAA,CAAAyB,uBAAA,CAAAhB,cAAA,GAAAa,MAAA;MAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;IAAA,EAAoD;IAD3ExC,EAAA,CAAAG,YAAA,EAC4E;IAC5EH,EAAA,CAAAqC,SAAA,4BAAkE;IAGxErC,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACiB;IAAvBD,EAAA,CAAAW,UAAA,mBAAAkC,4EAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAAa,aAAA,CAAA4B,GAAA,EAAAM,SAAA;MAAA,MAAA7B,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAqB,WAAA,CAASH,MAAA,CAAA8B,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAAC9C,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,kBAAoE;IAAxBD,EAAA,CAAAW,UAAA,mBAAAsC,4EAAA;MAAA,MAAAH,OAAA,GAAA9C,EAAA,CAAAa,aAAA,CAAA4B,GAAA,EAAAM,SAAA;MAAA,MAAA7B,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAqB,WAAA,CAASH,MAAA,CAAAgC,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAAC9C,EAAA,CAAAE,MAAA,oBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAChE,EACT;;;;;;IA3B6BH,EAAA,CAAAM,SAAA,GACE;IADFN,EAAA,CAAAgC,kBAAA,KAAAd,MAAA,CAAAyB,uBAAA,CAAAV,UAAA,SAAAf,MAAA,CAAAyB,uBAAA,CAAAT,MAAA,MACE;IAQoClC,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,iBAAA+C,mBAAA,CAAgC;IAC9EnD,EAAA,CAAAoD,gBAAA,YAAAlC,MAAA,CAAAyB,uBAAA,CAAAjB,gBAAA,CAAsD;IAOV1B,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAI,UAAA,iBAAAiD,iBAAA,CAA8B;IAC1ErD,EAAA,CAAAoD,gBAAA,YAAAlC,MAAA,CAAAyB,uBAAA,CAAAhB,cAAA,CAAoD;;;AD3DrF,OAAM,MAAO2B,0BAA2B,SAAQxD,aAAa;EAE3DyD,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IATvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA0C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAItD,KAAAvC,QAAQ,GAAY,IAAI;IAhCtB,IAAI,CAACe,uBAAuB,GAAG;MAC7BjB,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBO,MAAM,EAAEkC,SAAS;MACjBnC,UAAU,EAAE,EAAE;MACdT,QAAQ,EAAE4C;KACX;IAED,IAAI,CAACL,aAAa,CAACM,OAAO,EAAE,CAACC,IAAI,CAC/B/E,GAAG,CAAEgF,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAACR,eAAe,GAAGO,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAwBSC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAACb,gBAAgB,CAAC,CAAC,CAAC;MAC/CvC,gBAAgB,EAAE0C,SAAS;MAC3BzC,cAAc,EAAEyC;KACjB;IACD,IAAI,CAACW,gBAAgB,EAAE;EACzB;EAEAzD,SAASA,CAAC0D,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACzD,QAAQ,EAAE;MACjB,IAAI,CAACmB,uBAAuB,GAAG;QAC7B,GAAGsC,IAAI;QACPvD,gBAAgB,EAAEuD,IAAI,CAACvD,gBAAgB,GAAG,IAAIwD,IAAI,CAACD,IAAI,CAACvD,gBAAgB,CAAC,GAAG0C,SAAS;QACrFzC,cAAc,EAAEsD,IAAI,CAACtD,cAAc,GAAG,IAAIuD,IAAI,CAACD,IAAI,CAACtD,cAAc,CAAC,GAAGyC;OACvE;MACD,IAAI,CAACX,aAAa,CAAC0B,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOzF,MAAM,CAACyF,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEApC,QAAQA,CAAC8B,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5B,KAAK,CAAC6B,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC/B,OAAO,CAACgC,aAAa,CAAC,IAAI,CAAC/B,KAAK,CAAC6B,aAAa,CAAC;MACpD;IACF;IACA,MAAMG,KAAK,GAAG;MACZnE,QAAQ,EAAE,IAAI,CAACmB,uBAAuB,CAACnB,QAAQ;MAC/CE,gBAAgB,EAAE,IAAI,CAAC0D,UAAU,CAAC,IAAI,CAACzC,uBAAuB,CAACjB,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACzC,uBAAuB,CAAChB,cAAc;KAC5E;IAED,IAAI,CAACiC,aAAa,CAACgC,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAACjB,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACuB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACpC,OAAO,CAACqC,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBhB,GAAG,CAACiB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAlB,gBAAgBA,CAAA;IACd,IAAI,CAAClB,iBAAiB,CAACqC,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAACvB,IAAI,CAC7E/E,GAAG,CAACgF,GAAG,IAAG;MACR,MAAM4B,OAAO,GAAG5B,GAAG,CAAC6B,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAACV,MAAM,IAAIlB,GAAG,CAACuB,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChD/F,cAAc,EAAE+F,KAAK,CAAC/F,cAAc;UACpCgG,GAAG,EAAED,KAAK,CAACC;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAACxC,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAIyC,KAAK,GAAG,IAAI,CAACJ,oBAAoB,CAACK,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAACxC,eAAe,CAAC;UAC1F,IAAI,CAACY,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACwB,oBAAoB,CAACI,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAAC7B,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACwB,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMO,WAAW,GAAG,IAAI,CAAChC,WAAW,EAAEC,kBAAkB,EAAE2B,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACZ,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAACtB,SAAS,EAAE;EACf;EAEAmC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvC,MAAMC,KAAK,GAAGD,KAAK,CAACjF,MAAM;QAC1B,IAAI,CAAC6E,SAAS,CAACK,KAAK,CAAC,EAAE;UAAE;UACvBL,SAAS,CAACK,KAAK,CAAC,GAAG,EAAE;QACvB;QACAL,SAAS,CAACK,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBpF,UAAU,EAAEgF,SAAS,CAAChF,UAAU;UAChCT,QAAQ,EAAE2F,KAAK,CAAC3F,QAAQ;UACxBU,MAAM,EAAEiF,KAAK,CAACjF,MAAM;UACpBR,gBAAgB,EAAEyF,KAAK,CAACzF,gBAAgB;UACxCC,cAAc,EAAEwF,KAAK,CAACxF;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC2F,MAAM,CAACC,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAACJ,MAAM,CAAChB,GAAG,CAAEc,KAAU,IAAI;MAChE,OAAO,IAAI,CAACO,UAAU,CAACrB,GAAG,CAAEW,SAAc,IAAI;QAC5C,MAAME,KAAK,GAAGJ,SAAS,CAACK,KAAK,CAAC,CAACQ,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAAC5F,UAAU,KAAKgF,SAAS,CAAC;QAC5F,OAAOE,KAAK,IAAI;UACdlF,UAAU,EAAEgF,SAAS;UACrBzF,QAAQ,EAAE,IAAI;UACdU,MAAM,EAAEkF,KAAK;UACb1F,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO+F,MAAM;EACf;EAEAI,sBAAsBA,CAAChB,GAAU;IAC/B,MAAMiB,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5ClB,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBgB,aAAa,CAACC,GAAG,CAACjB,SAAS,CAAChF,UAAU,CAAC;MACvCgF,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvCY,SAAS,CAACG,GAAG,CAACf,KAAK,CAACjF,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACoF,MAAM,GAAGa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLX,MAAM,EAAEa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACzD,WAAW,CAAClD,gBAAgB,IAAI,IAAI,CAACkD,WAAW,CAACjD,cAAc,EAAE;MACxE,MAAM2G,SAAS,GAAG,IAAIpD,IAAI,CAAC,IAAI,CAACN,WAAW,CAAClD,gBAAgB,CAAC;MAC7D,MAAM6G,OAAO,GAAG,IAAIrD,IAAI,CAAC,IAAI,CAACN,WAAW,CAACjD,cAAc,CAAC;MACzD,IAAI2G,SAAS,IAAIC,OAAO,IAAID,SAAS,GAAGC,OAAO,EAAE;QAC/C,IAAI,CAAC7E,OAAO,CAACgC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEAM,kBAAkBA,CAAA;IAChB,IAAI,CAACqC,cAAc,EAAE;IACrB,IAAI,CAACzE,aAAa,CAAC4E,mCAAmC,CAAC;MACrD3C,IAAI,EAAE;QACJ4C,YAAY,EAAE,IAAI,CAAC7D,WAAW,CAACC,kBAAkB,CAAC2B,GAAG;QACrD9E,gBAAgB,EAAE,IAAI,CAACkD,WAAW,CAAClD,gBAAgB,GAAG,IAAI,CAAC0D,UAAU,CAAC,IAAI,CAACR,WAAW,CAAClD,gBAAgB,CAAC,GAAG0C,SAAS;QACpHzC,cAAc,EAAE,IAAI,CAACiD,WAAW,CAACjD,cAAc,GAAG,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACR,WAAW,CAACjD,cAAc,CAAC,GAAGyC;;KAExG,CAAC,CAACM,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC6B,OAAO,IAAI7B,GAAG,CAACuB,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC4C,gBAAgB,GAAGnE,GAAG,CAAC6B,OAAO,GAAG7B,GAAG,CAAC6B,OAAO,GAAG,EAAE;QACtD,IAAI7B,GAAG,CAAC6B,OAAO,EAAE;UACf,IAAI,CAACsC,gBAAgB,GAAG,CAAC,GAAGnE,GAAG,CAAC6B,OAAO,CAAC;UACxC,IAAI,CAAC0B,sBAAsB,CAACvD,GAAG,CAAC6B,OAAO,CAAC;UACxC,IAAI,CAACuC,mBAAmB,GAAG,IAAI,CAAC9B,8BAA8B,CAACtC,GAAG,CAAC6B,OAAO,CAAC;QAC7E;MACF;IACF,CAAC,CAAC;EACJ;EAEApD,OAAOA,CAACgC,GAAQ;IACdA,GAAG,CAACiB,KAAK,EAAE;EACb;EAEAV,UAAUA,CAAA;IACR,IAAI,CAAC5B,KAAK,CAACiF,KAAK,EAAE;IAClB,IAAI,CAACjF,KAAK,CAACkF,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAClG,uBAAuB,CAACjB,gBAAgB,CAAC;IAC9E,IAAI,CAACiC,KAAK,CAACkF,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAClG,uBAAuB,CAAChB,cAAc,CAAC;IAC5E,IAAI,CAACgC,KAAK,CAACmF,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACnG,uBAAuB,CAACjB,gBAAgB,EAAE,IAAI,CAACiB,uBAAuB,CAAChB,cAAc,CAAC;EACtI;EAEAoH,gBAAgBA,CAAA;IACd,IAAI,CAACjF,MAAM,CAACkF,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAACpE,WAAW,EAAEC,kBAAkB,EAAE2B,GAAG,EAAE,CAAC,CAAC;EACnG;;;uCAlOWlD,0BAA0B,EAAAtD,EAAA,CAAAiJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnJ,EAAA,CAAAiJ,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAArJ,EAAA,CAAAiJ,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAvJ,EAAA,CAAAiJ,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAzJ,EAAA,CAAAiJ,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA3J,EAAA,CAAAiJ,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAA5J,EAAA,CAAAiJ,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAA9J,EAAA,CAAAiJ,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA1B1G,0BAA0B;MAAA2G,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnK,EAAA,CAAAoK,kBAAA,CAR1B,EAAE,GAAApK,EAAA,CAAAqK,0BAAA,EAAArK,EAAA,CAAAsK,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnEb5K,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAqC,SAAA,qBAAiC;UACnCrC,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,aAA8B,aACN,cACqC,gBACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,qBAC0C;UADdD,EAAA,CAAAsC,gBAAA,2BAAAwI,wEAAAtI,MAAA;YAAAxC,EAAA,CAAAa,aAAA,CAAAkK,GAAA;YAAA/K,EAAA,CAAA0C,kBAAA,CAAAmI,GAAA,CAAAjG,WAAA,CAAAC,kBAAA,EAAArC,MAAA,MAAAqI,GAAA,CAAAjG,WAAA,CAAAC,kBAAA,GAAArC,MAAA;YAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;UAAA,EAA4C;UACtExC,EAAA,CAAAW,UAAA,4BAAAqK,yEAAA;YAAAhL,EAAA,CAAAa,aAAA,CAAAkK,GAAA;YAAA,OAAA/K,EAAA,CAAAqB,WAAA,CAAkBwJ,GAAA,CAAA7E,kBAAA,EAAoB;UAAA,EAAC;UACvChG,EAAA,CAAA6B,UAAA,KAAAoJ,gDAAA,wBAAoE;UAK1EjL,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,iCAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,yBAA4B;UAC1BD,EAAA,CAAAqC,SAAA,mBAAoD;UACpDrC,EAAA,CAAAC,cAAA,iBACkE;UAA3CD,EAAA,CAAAsC,gBAAA,2BAAA4I,oEAAA1I,MAAA;YAAAxC,EAAA,CAAAa,aAAA,CAAAkK,GAAA;YAAA/K,EAAA,CAAA0C,kBAAA,CAAAmI,GAAA,CAAAjG,WAAA,CAAAlD,gBAAA,EAAAc,MAAA,MAAAqI,GAAA,CAAAjG,WAAA,CAAAlD,gBAAA,GAAAc,MAAA;YAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;UAAA,EAA0C;UADjExC,EAAA,CAAAG,YAAA,EACkE;UAClEH,EAAA,CAAAqC,SAAA,4BAA8D;UAChErC,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAAC,cAAA,iBAA0C;UAACD,EAAA,CAAAE,MAAA,WAC3C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAe;UACbD,EAAA,CAAAqC,SAAA,mBAAoD;UACpDrC,EAAA,CAAAC,cAAA,iBAC2C;UAAzCD,EAAA,CAAAsC,gBAAA,2BAAA6I,oEAAA3I,MAAA;YAAAxC,EAAA,CAAAa,aAAA,CAAAkK,GAAA;YAAA/K,EAAA,CAAA0C,kBAAA,CAAAmI,GAAA,CAAAjG,WAAA,CAAAjD,cAAA,EAAAa,MAAA,MAAAqI,GAAA,CAAAjG,WAAA,CAAAjD,cAAA,GAAAa,MAAA;YAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;UAAA,EAAwC;UAD1CxC,EAAA,CAAAG,YAAA,EAC2C;UAC3CH,EAAA,CAAAqC,SAAA,4BAA4D;UAGlErC,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAuC,eACoB,iBACV;UAC3CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,eAA6B,iBAEF;UAAvBD,EAAA,CAAAsC,gBAAA,2BAAA8I,oEAAA5I,MAAA;YAAAxC,EAAA,CAAAa,aAAA,CAAAkK,GAAA;YAAA/K,EAAA,CAAA0C,kBAAA,CAAAmI,GAAA,CAAAjJ,QAAA,EAAAY,MAAA,MAAAqI,GAAA,CAAAjJ,QAAA,GAAAY,MAAA;YAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;UAAA,EAAsB;UADxBxC,EAAA,CAAAG,YAAA,EACyB;UACzBH,EAAA,CAAAC,cAAA,iBAAwD;UACtDD,EAAA,CAAAE,MAAA,oDACF;UACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;UAEJH,EADF,CAAAC,cAAA,eAA6B,iBAEF;UAAvBD,EAAA,CAAAsC,gBAAA,2BAAA+I,oEAAA7I,MAAA;YAAAxC,EAAA,CAAAa,aAAA,CAAAkK,GAAA;YAAA/K,EAAA,CAAA0C,kBAAA,CAAAmI,GAAA,CAAAjJ,QAAA,EAAAY,MAAA,MAAAqI,GAAA,CAAAjJ,QAAA,GAAAY,MAAA;YAAA,OAAAxC,EAAA,CAAAqB,WAAA,CAAAmB,MAAA;UAAA,EAAsB;UADxBxC,EAAA,CAAAG,YAAA,EACyB;UACzBH,EAAA,CAAAC,cAAA,iBAAwD;UACtDD,EAAA,CAAAE,MAAA,oDACF;UAGNF,EAHM,CAAAG,YAAA,EAAQ,EACJ,EACF,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAwC,eACS,kBACgC;UAA/BD,EAAA,CAAAW,UAAA,mBAAA2K,6DAAA;YAAAtL,EAAA,CAAAa,aAAA,CAAAkK,GAAA;YAAA,OAAA/K,EAAA,CAAAqB,WAAA,CAASwJ,GAAA,CAAA7E,kBAAA,EAAoB;UAAA,EAAC;UAC1EhG,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAqC,SAAA,aAA6B;UAClCrC,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAiE;UAA7BD,EAAA,CAAAW,UAAA,mBAAA4K,6DAAA;YAAAvL,EAAA,CAAAa,aAAA,CAAAkK,GAAA;YAAA,OAAA/K,EAAA,CAAAqB,WAAA,CAASwJ,GAAA,CAAA9B,gBAAA,EAAkB;UAAA,EAAC;UAC9D/I,EAAA,CAAAE,MAAA,kCACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAKAH,EAHN,CAAAC,cAAA,eAAmC,iBAC2C,aACnE,cACgD;UACnDD,EAAA,CAAAqC,SAAA,UAAS;UACTrC,EAAA,CAAA6B,UAAA,KAAA2J,mDAAA,2BAAoE;UAIxExL,EADE,CAAAG,YAAA,EAAK,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA6B,UAAA,KAAA4J,yCAAA,iBAA6D;UAgBvEzL,EAJQ,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO,EACP;UAeVH,EAbA,CAAA6B,UAAA,KAAA6J,kDAAA,gCAAA1L,EAAA,CAAA2L,sBAAA,CAAmE,KAAAC,kDAAA,iCAAA5L,EAAA,CAAA2L,sBAAA,CAaf;;;;;UAxGd3L,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAoD,gBAAA,YAAAyH,GAAA,CAAAjG,WAAA,CAAAC,kBAAA,CAA4C;UAE1C7E,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAyK,GAAA,CAAAxE,oBAAA,CAAuB;UAYWrG,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,iBAAAyL,aAAA,CAA0B;UACjE7L,EAAA,CAAAoD,gBAAA,YAAAyH,GAAA,CAAAjG,WAAA,CAAAlD,gBAAA,CAA0C;UAOL1B,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,iBAAA0L,WAAA,CAAwB;UAClF9L,EAAA,CAAAoD,gBAAA,YAAAyH,GAAA,CAAAjG,WAAA,CAAAjD,cAAA,CAAwC;UAWkD3B,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,eAAc;UACxGJ,EAAA,CAAAoD,gBAAA,YAAAyH,GAAA,CAAAjJ,QAAA,CAAsB;UAMoE5B,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,gBAAe;UACzGJ,EAAA,CAAAoD,gBAAA,YAAAyH,GAAA,CAAAjJ,QAAA,CAAsB;UAyBO5B,EAAA,CAAAM,SAAA,IAAsB;UAAtBN,EAAA,CAAAI,UAAA,YAAAyK,GAAA,CAAAnC,gBAAA,CAAsB;UAMlC1I,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAAyK,GAAA,CAAAlC,mBAAA,CAAyB;;;qBDbpDrJ,YAAY,EAAAyM,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEpM,YAAY,EAAAqM,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,yBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAlD,EAAA,CAAAmD,eAAA,EAAAnD,EAAA,CAAAoD,mBAAA,EAAApD,EAAA,CAAAqD,qBAAA,EAAArD,EAAA,CAAAsD,qBAAA,EAAAtD,EAAA,CAAAuD,gBAAA,EAAAvD,EAAA,CAAAwD,iBAAA,EAAAxD,EAAA,CAAAyD,iBAAA,EAAAzD,EAAA,CAAA0D,oBAAA,EAAA1D,EAAA,CAAA2D,iBAAA,EAAA3D,EAAA,CAAA4D,eAAA,EAAA5D,EAAA,CAAA6D,qBAAA,EAAA7D,EAAA,CAAA8D,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAE1N,iBAAiB,EAC7CF,kBAAkB,EAAEC,mBAAmB,EACvCE,qBAAqB;MAAA0N,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}