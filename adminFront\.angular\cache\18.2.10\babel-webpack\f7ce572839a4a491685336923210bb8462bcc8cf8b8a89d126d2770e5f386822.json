{"ast": null, "code": "export var numericPatterns = {\n  month: /^(1[0-2]|0?\\d)/,\n  // 0 to 12\n  date: /^(3[0-1]|[0-2]?\\d)/,\n  // 0 to 31\n  dayOfYear: /^(36[0-6]|3[0-5]\\d|[0-2]?\\d?\\d)/,\n  // 0 to 366\n  week: /^(5[0-3]|[0-4]?\\d)/,\n  // 0 to 53\n  hour23h: /^(2[0-3]|[0-1]?\\d)/,\n  // 0 to 23\n  hour24h: /^(2[0-4]|[0-1]?\\d)/,\n  // 0 to 24\n  hour11h: /^(1[0-1]|0?\\d)/,\n  // 0 to 11\n  hour12h: /^(1[0-2]|0?\\d)/,\n  // 0 to 12\n  minute: /^[0-5]?\\d/,\n  // 0 to 59\n  second: /^[0-5]?\\d/,\n  // 0 to 59\n\n  singleDigit: /^\\d/,\n  // 0 to 9\n  twoDigits: /^\\d{1,2}/,\n  // 0 to 99\n  threeDigits: /^\\d{1,3}/,\n  // 0 to 999\n  fourDigits: /^\\d{1,4}/,\n  // 0 to 9999\n\n  anyDigitsSigned: /^-?\\d+/,\n  singleDigitSigned: /^-?\\d/,\n  // 0 to 9, -0 to -9\n  twoDigitsSigned: /^-?\\d{1,2}/,\n  // 0 to 99, -0 to -99\n  threeDigitsSigned: /^-?\\d{1,3}/,\n  // 0 to 999, -0 to -999\n  fourDigitsSigned: /^-?\\d{1,4}/ // 0 to 9999, -0 to -9999\n};\nexport var timezonePatterns = {\n  basicOptionalMinutes: /^([+-])(\\d{2})(\\d{2})?|Z/,\n  basic: /^([+-])(\\d{2})(\\d{2})|Z/,\n  basicOptionalSeconds: /^([+-])(\\d{2})(\\d{2})((\\d{2}))?|Z/,\n  extended: /^([+-])(\\d{2}):(\\d{2})|Z/,\n  extendedOptionalSeconds: /^([+-])(\\d{2}):(\\d{2})(:(\\d{2}))?|Z/\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}