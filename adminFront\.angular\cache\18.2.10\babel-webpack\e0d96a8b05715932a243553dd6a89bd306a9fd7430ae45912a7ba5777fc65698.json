{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Belarusian [be]\n//! author : <PERSON> : https://github.com/demidov91\n//! author: Praleska: http://praleska.pro/\n//! Author : <PERSON><PERSON><PERSON> : https://github.com/Oire\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function plural(word, num) {\n    var forms = word.split('_');\n    return num % 10 === 1 && num % 100 !== 11 ? forms[0] : num % 10 >= 2 && num % 10 <= 4 && (num % 100 < 10 || num % 100 >= 20) ? forms[1] : forms[2];\n  }\n  function relativeTimeWithPlural(number, withoutSuffix, key) {\n    var format = {\n      ss: withoutSuffix ? 'секунда_секунды_секунд' : 'секунду_секунды_секунд',\n      mm: withoutSuffix ? 'хвіліна_хвіліны_хвілін' : 'хвіліну_хвіліны_хвілін',\n      hh: withoutSuffix ? 'гадзіна_гадзіны_гадзін' : 'гадзіну_гадзіны_гадзін',\n      dd: 'дзень_дні_дзён',\n      MM: 'месяц_месяцы_месяцаў',\n      yy: 'год_гады_гадоў'\n    };\n    if (key === 'm') {\n      return withoutSuffix ? 'хвіліна' : 'хвіліну';\n    } else if (key === 'h') {\n      return withoutSuffix ? 'гадзіна' : 'гадзіну';\n    } else {\n      return number + ' ' + plural(format[key], +number);\n    }\n  }\n  var be = moment.defineLocale('be', {\n    months: {\n      format: 'студзеня_лютага_сакавіка_красавіка_траўня_чэрвеня_ліпеня_жніўня_верасня_кастрычніка_лістапада_снежня'.split('_'),\n      standalone: 'студзень_люты_сакавік_красавік_травень_чэрвень_ліпень_жнівень_верасень_кастрычнік_лістапад_снежань'.split('_')\n    },\n    monthsShort: 'студ_лют_сак_крас_трав_чэрв_ліп_жнів_вер_каст_ліст_снеж'.split('_'),\n    weekdays: {\n      format: 'нядзелю_панядзелак_аўторак_сераду_чацвер_пятніцу_суботу'.split('_'),\n      standalone: 'нядзеля_панядзелак_аўторак_серада_чацвер_пятніца_субота'.split('_'),\n      isFormat: /\\[ ?[Ууў] ?(?:мінулую|наступную)? ?\\] ?dddd/\n    },\n    weekdaysShort: 'нд_пн_ат_ср_чц_пт_сб'.split('_'),\n    weekdaysMin: 'нд_пн_ат_ср_чц_пт_сб'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY г.',\n      LLL: 'D MMMM YYYY г., HH:mm',\n      LLLL: 'dddd, D MMMM YYYY г., HH:mm'\n    },\n    calendar: {\n      sameDay: '[Сёння ў] LT',\n      nextDay: '[Заўтра ў] LT',\n      lastDay: '[Учора ў] LT',\n      nextWeek: function () {\n        return '[У] dddd [ў] LT';\n      },\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n          case 3:\n          case 5:\n          case 6:\n            return '[У мінулую] dddd [ў] LT';\n          case 1:\n          case 2:\n          case 4:\n            return '[У мінулы] dddd [ў] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'праз %s',\n      past: '%s таму',\n      s: 'некалькі секунд',\n      m: relativeTimeWithPlural,\n      mm: relativeTimeWithPlural,\n      h: relativeTimeWithPlural,\n      hh: relativeTimeWithPlural,\n      d: 'дзень',\n      dd: relativeTimeWithPlural,\n      M: 'месяц',\n      MM: relativeTimeWithPlural,\n      y: 'год',\n      yy: relativeTimeWithPlural\n    },\n    meridiemParse: /ночы|раніцы|дня|вечара/,\n    isPM: function (input) {\n      return /^(дня|вечара)$/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'ночы';\n      } else if (hour < 12) {\n        return 'раніцы';\n      } else if (hour < 17) {\n        return 'дня';\n      } else {\n        return 'вечара';\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(і|ы|га)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'M':\n        case 'd':\n        case 'DDD':\n        case 'w':\n        case 'W':\n          return (number % 10 === 2 || number % 10 === 3) && number % 100 !== 12 && number % 100 !== 13 ? number + '-і' : number + '-ы';\n        case 'D':\n          return number + '-га';\n        default:\n          return number;\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return be;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}