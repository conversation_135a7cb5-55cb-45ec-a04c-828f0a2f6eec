{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nlet SpaceTemplateSelectorComponent = class SpaceTemplateSelectorComponent {\n  constructor() {\n    this.isVisible = false;\n    this.buildCaseId = '';\n    this.availableSpaces = [];\n    this.templateApplied = new EventEmitter();\n    this.closed = new EventEmitter();\n    this.currentStep = 1;\n    this.selectedSpace = null;\n    this.templateGroups = [];\n    // 預設空間選項\n    this.defaultSpaces = [{\n      id: 'kitchen',\n      name: '廚房',\n      icon: '🍳',\n      templateCount: 8\n    }, {\n      id: 'living',\n      name: '客廳',\n      icon: '🛋️',\n      templateCount: 5\n    }, {\n      id: 'bedroom',\n      name: '主臥室',\n      icon: '🛏️',\n      templateCount: 6\n    }, {\n      id: 'bathroom',\n      name: '主衛浴',\n      icon: '🚿',\n      templateCount: 4\n    }, {\n      id: 'balcony',\n      name: '陽台',\n      icon: '🏢',\n      templateCount: 3\n    }, {\n      id: 'entrance',\n      name: '玄關',\n      icon: '🚪',\n      templateCount: 2\n    }];\n  }\n  ngOnInit() {\n    if (this.availableSpaces.length === 0) {\n      this.availableSpaces = this.defaultSpaces;\n    }\n  }\n  selectSpace(space) {\n    this.selectedSpace = space;\n    this.loadTemplatesForSpace(space.id);\n  }\n  loadTemplatesForSpace(spaceId) {\n    // 根據空間ID載入對應的模板群組\n    // 這裡可以調用API或使用預設資料\n    this.templateGroups = this.getDefaultTemplatesForSpace(spaceId);\n  }\n  getDefaultTemplatesForSpace(spaceId) {\n    const templates = {\n      kitchen: [{\n        id: 'kitchen-standard',\n        name: '廚房標準配備',\n        icon: '🔧',\n        expanded: false,\n        items: [{\n          id: 'kt001',\n          name: '洗碗機',\n          code: 'KT001',\n          price: 38000,\n          unit: '台',\n          selected: false\n        }, {\n          id: 'kt002',\n          name: '烤箱',\n          code: 'KT002',\n          price: 25000,\n          unit: '台',\n          selected: false\n        }, {\n          id: 'kt003',\n          name: '抽油煙機升級',\n          code: 'KT003',\n          price: 15000,\n          unit: '台',\n          selected: false\n        }]\n      }, {\n        id: 'kitchen-premium',\n        name: '廚房高級配備',\n        icon: '⭐',\n        expanded: false,\n        items: [{\n          id: 'kt004',\n          name: '中島檯面',\n          code: 'KT004',\n          price: 80000,\n          unit: '組',\n          selected: false\n        }, {\n          id: 'kt005',\n          name: '智能電磁爐',\n          code: 'KT005',\n          price: 45000,\n          unit: '台',\n          selected: false\n        }]\n      }],\n      living: [{\n        id: 'living-basic',\n        name: '客廳基本配備',\n        icon: '🏠',\n        expanded: false,\n        items: [{\n          id: 'lv001',\n          name: '投影設備',\n          code: 'LV001',\n          price: 50000,\n          unit: '組',\n          selected: false\n        }, {\n          id: 'lv002',\n          name: '音響系統',\n          code: 'LV002',\n          price: 35000,\n          unit: '組',\n          selected: false\n        }]\n      }]\n    };\n    return templates[spaceId] || [];\n  }\n  toggleTemplateGroup(group) {\n    // 收合其他群組\n    this.templateGroups.forEach(g => {\n      if (g.id !== group.id) {\n        g.expanded = false;\n      }\n    });\n    // 切換當前群組\n    group.expanded = !group.expanded;\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  getTotalPrice(items) {\n    return items.reduce((total, item) => total + (item.selected ? item.price : 0), 0);\n  }\n  getSelectedItems() {\n    const selected = [];\n    this.templateGroups.forEach(group => {\n      group.items.forEach(item => {\n        if (item.selected) {\n          selected.push(item);\n        }\n      });\n    });\n    return selected;\n  }\n  getSelectedTotalPrice() {\n    return this.getSelectedItems().reduce((total, item) => total + item.price, 0);\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.selectedSpace !== null;\n      case 2:\n        return this.getSelectedItems().length > 0;\n      case 3:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 3) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇目標空間',\n      2: '請選擇要套用的模板項目',\n      3: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    if (!this.selectedSpace) return;\n    const config = {\n      spaceId: this.selectedSpace.id,\n      spaceName: this.selectedSpace.name,\n      selectedItems: this.getSelectedItems(),\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.isVisible = false;\n    this.reset();\n    this.closed.emit();\n  }\n  onBackdropClick(event) {\n    if (event.target === event.currentTarget) {\n      this.close();\n    }\n  }\n  reset() {\n    this.currentStep = 1;\n    this.selectedSpace = null;\n    this.templateGroups = [];\n  }\n  // 公共API方法\n  open(preSelectedSpaceId) {\n    this.isVisible = true;\n    this.reset();\n    if (preSelectedSpaceId) {\n      const space = this.availableSpaces.find(s => s.id === preSelectedSpaceId);\n      if (space) {\n        this.selectSpace(space);\n      }\n    }\n  }\n};\n__decorate([Input()], SpaceTemplateSelectorComponent.prototype, \"isVisible\", void 0);\n__decorate([Input()], SpaceTemplateSelectorComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], SpaceTemplateSelectorComponent.prototype, \"availableSpaces\", void 0);\n__decorate([Output()], SpaceTemplateSelectorComponent.prototype, \"templateApplied\", void 0);\n__decorate([Output()], SpaceTemplateSelectorComponent.prototype, \"closed\", void 0);\nSpaceTemplateSelectorComponent = __decorate([Component({\n  selector: 'app-space-template-selector',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './space-template-selector.component.html',\n  styleUrls: ['./space-template-selector.component.scss']\n})], SpaceTemplateSelectorComponent);\nexport { SpaceTemplateSelectorComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "SpaceTemplateSelectorComponent", "constructor", "isVisible", "buildCaseId", "availableSpaces", "templateApplied", "closed", "currentStep", "selectedSpace", "templateGroups", "defaultSpaces", "id", "name", "icon", "templateCount", "ngOnInit", "length", "selectSpace", "space", "loadTemplatesForSpace", "spaceId", "getDefaultTemplatesForSpace", "templates", "kitchen", "expanded", "items", "code", "price", "unit", "selected", "living", "toggleTemplateGroup", "group", "for<PERSON>ach", "g", "onTemplateItemChange", "getTotalPrice", "reduce", "total", "item", "getSelectedItems", "push", "getSelectedTotalPrice", "canProceed", "nextStep", "previousStep", "getProgressText", "progressTexts", "hasConflicts", "getConflictCount", "applyTemplate", "config", "spaceName", "selectedItems", "totalPrice", "emit", "close", "reset", "onBackdropClick", "event", "target", "currentTarget", "open", "preSelectedSpaceId", "find", "s", "__decorate", "selector", "standalone", "imports", "CommonModule", "FormsModule", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\n\r\nexport interface SpaceOption {\r\n  id: string;\r\n  name: string;\r\n  icon: string;\r\n  templateCount: number;\r\n}\r\n\r\nexport interface TemplateItem {\r\n  id: string;\r\n  name: string;\r\n  code: string;\r\n  price: number;\r\n  unit: string;\r\n  selected: boolean;\r\n}\r\n\r\nexport interface TemplateGroup {\r\n  id: string;\r\n  name: string;\r\n  icon: string;\r\n  items: TemplateItem[];\r\n  expanded: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: TemplateItem[];\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule\r\n  ],\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() buildCaseId: string = '';\r\n  @Input() availableSpaces: SpaceOption[] = [];\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n  @Output() closed = new EventEmitter<void>();\r\n\r\n  currentStep: number = 1;\r\n  selectedSpace: SpaceOption | null = null;\r\n  templateGroups: TemplateGroup[] = [];\r\n\r\n  // 預設空間選項\r\n  defaultSpaces: SpaceOption[] = [\r\n    { id: 'kitchen', name: '廚房', icon: '🍳', templateCount: 8 },\r\n    { id: 'living', name: '客廳', icon: '🛋️', templateCount: 5 },\r\n    { id: 'bedroom', name: '主臥室', icon: '🛏️', templateCount: 6 },\r\n    { id: 'bathroom', name: '主衛浴', icon: '🚿', templateCount: 4 },\r\n    { id: 'balcony', name: '陽台', icon: '🏢', templateCount: 3 },\r\n    { id: 'entrance', name: '玄關', icon: '🚪', templateCount: 2 }\r\n  ];\r\n\r\n  ngOnInit() {\r\n    if (this.availableSpaces.length === 0) {\r\n      this.availableSpaces = this.defaultSpaces;\r\n    }\r\n  }\r\n\r\n  selectSpace(space: SpaceOption) {\r\n    this.selectedSpace = space;\r\n    this.loadTemplatesForSpace(space.id);\r\n  }\r\n\r\n  loadTemplatesForSpace(spaceId: string) {\r\n    // 根據空間ID載入對應的模板群組\r\n    // 這裡可以調用API或使用預設資料\r\n    this.templateGroups = this.getDefaultTemplatesForSpace(spaceId);\r\n  }\r\n\r\n  getDefaultTemplatesForSpace(spaceId: string): TemplateGroup[] {\r\n    const templates: Record<string, TemplateGroup[]> = {\r\n      kitchen: [\r\n        {\r\n          id: 'kitchen-standard',\r\n          name: '廚房標準配備',\r\n          icon: '🔧',\r\n          expanded: false,\r\n          items: [\r\n            { id: 'kt001', name: '洗碗機', code: 'KT001', price: 38000, unit: '台', selected: false },\r\n            { id: 'kt002', name: '烤箱', code: 'KT002', price: 25000, unit: '台', selected: false },\r\n            { id: 'kt003', name: '抽油煙機升級', code: 'KT003', price: 15000, unit: '台', selected: false }\r\n          ]\r\n        },\r\n        {\r\n          id: 'kitchen-premium',\r\n          name: '廚房高級配備',\r\n          icon: '⭐',\r\n          expanded: false,\r\n          items: [\r\n            { id: 'kt004', name: '中島檯面', code: 'KT004', price: 80000, unit: '組', selected: false },\r\n            { id: 'kt005', name: '智能電磁爐', code: 'KT005', price: 45000, unit: '台', selected: false }\r\n          ]\r\n        }\r\n      ],\r\n      living: [\r\n        {\r\n          id: 'living-basic',\r\n          name: '客廳基本配備',\r\n          icon: '🏠',\r\n          expanded: false,\r\n          items: [\r\n            { id: 'lv001', name: '投影設備', code: 'LV001', price: 50000, unit: '組', selected: false },\r\n            { id: 'lv002', name: '音響系統', code: 'LV002', price: 35000, unit: '組', selected: false }\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n\r\n    return templates[spaceId] || [];\r\n  }\r\n\r\n  toggleTemplateGroup(group: TemplateGroup) {\r\n    // 收合其他群組\r\n    this.templateGroups.forEach(g => {\r\n      if (g.id !== group.id) {\r\n        g.expanded = false;\r\n      }\r\n    });\r\n    \r\n    // 切換當前群組\r\n    group.expanded = !group.expanded;\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  getTotalPrice(items: TemplateItem[]): number {\r\n    return items.reduce((total, item) => total + (item.selected ? item.price : 0), 0);\r\n  }\r\n\r\n  getSelectedItems(): TemplateItem[] {\r\n    const selected: TemplateItem[] = [];\r\n    this.templateGroups.forEach(group => {\r\n      group.items.forEach(item => {\r\n        if (item.selected) {\r\n          selected.push(item);\r\n        }\r\n      });\r\n    });\r\n    return selected;\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    return this.getSelectedItems().reduce((total, item) => total + item.price, 0);\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.selectedSpace !== null;\r\n      case 2:\r\n        return this.getSelectedItems().length > 0;\r\n      case 3:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 3) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇目標空間',\r\n      2: '請選擇要套用的模板項目',\r\n      3: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    if (!this.selectedSpace) return;\r\n\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: this.selectedSpace.id,\r\n      spaceName: this.selectedSpace.name,\r\n      selectedItems: this.getSelectedItems(),\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.isVisible = false;\r\n    this.reset();\r\n    this.closed.emit();\r\n  }\r\n\r\n  onBackdropClick(event: Event) {\r\n    if (event.target === event.currentTarget) {\r\n      this.close();\r\n    }\r\n  }\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.selectedSpace = null;\r\n    this.templateGroups = [];\r\n  }\r\n\r\n  // 公共API方法\r\n  open(preSelectedSpaceId?: string) {\r\n    this.isVisible = true;\r\n    this.reset();\r\n    \r\n    if (preSelectedSpaceId) {\r\n      const space = this.availableSpaces.find(s => s.id === preSelectedSpaceId);\r\n      if (space) {\r\n        this.selectSpace(space);\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAgB,eAAe;AA2CvE,IAAMC,8BAA8B,GAApC,MAAMA,8BAA8B;EAApCC,YAAA;IACI,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,eAAe,GAAkB,EAAE;IAClC,KAAAC,eAAe,GAAG,IAAIR,YAAY,EAAuB;IACzD,KAAAS,MAAM,GAAG,IAAIT,YAAY,EAAQ;IAE3C,KAAAU,WAAW,GAAW,CAAC;IACvB,KAAAC,aAAa,GAAuB,IAAI;IACxC,KAAAC,cAAc,GAAoB,EAAE;IAEpC;IACA,KAAAC,aAAa,GAAkB,CAC7B;MAAEC,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAC,CAAE,EAC3D;MAAEH,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,KAAK;MAAEC,aAAa,EAAE;IAAC,CAAE,EAC3D;MAAEH,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,KAAK;MAAEC,aAAa,EAAE;IAAC,CAAE,EAC7D;MAAEH,EAAE,EAAE,UAAU;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAC,CAAE,EAC7D;MAAEH,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAC,CAAE,EAC3D;MAAEH,EAAE,EAAE,UAAU;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAC,CAAE,CAC7D;EAyLH;EAvLEC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACX,eAAe,CAACY,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACZ,eAAe,GAAG,IAAI,CAACM,aAAa;IAC3C;EACF;EAEAO,WAAWA,CAACC,KAAkB;IAC5B,IAAI,CAACV,aAAa,GAAGU,KAAK;IAC1B,IAAI,CAACC,qBAAqB,CAACD,KAAK,CAACP,EAAE,CAAC;EACtC;EAEAQ,qBAAqBA,CAACC,OAAe;IACnC;IACA;IACA,IAAI,CAACX,cAAc,GAAG,IAAI,CAACY,2BAA2B,CAACD,OAAO,CAAC;EACjE;EAEAC,2BAA2BA,CAACD,OAAe;IACzC,MAAME,SAAS,GAAoC;MACjDC,OAAO,EAAE,CACP;QACEZ,EAAE,EAAE,kBAAkB;QACtBC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,IAAI;QACVW,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,CACL;UAAEd,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,KAAK;UAAEc,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACrF;UAAElB,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,IAAI;UAAEc,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACpF;UAAElB,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,QAAQ;UAAEc,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE;OAE3F,EACD;QACElB,EAAE,EAAE,iBAAiB;QACrBC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,GAAG;QACTW,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,CACL;UAAEd,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,MAAM;UAAEc,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACtF;UAAElB,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,OAAO;UAAEc,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE;OAE1F,CACF;MACDC,MAAM,EAAE,CACN;QACEnB,EAAE,EAAE,cAAc;QAClBC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,IAAI;QACVW,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,CACL;UAAEd,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,MAAM;UAAEc,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACtF;UAAElB,EAAE,EAAE,OAAO;UAAEC,IAAI,EAAE,MAAM;UAAEc,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE;OAEzF;KAEJ;IAED,OAAOP,SAAS,CAACF,OAAO,CAAC,IAAI,EAAE;EACjC;EAEAW,mBAAmBA,CAACC,KAAoB;IACtC;IACA,IAAI,CAACvB,cAAc,CAACwB,OAAO,CAACC,CAAC,IAAG;MAC9B,IAAIA,CAAC,CAACvB,EAAE,KAAKqB,KAAK,CAACrB,EAAE,EAAE;QACrBuB,CAAC,CAACV,QAAQ,GAAG,KAAK;MACpB;IACF,CAAC,CAAC;IAEF;IACAQ,KAAK,CAACR,QAAQ,GAAG,CAACQ,KAAK,CAACR,QAAQ;EAClC;EAEAW,oBAAoBA,CAAA;IAClB;EAAA;EAGFC,aAAaA,CAACX,KAAqB;IACjC,OAAOA,KAAK,CAACY,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,IAAIC,IAAI,CAACV,QAAQ,GAAGU,IAAI,CAACZ,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACnF;EAEAa,gBAAgBA,CAAA;IACd,MAAMX,QAAQ,GAAmB,EAAE;IACnC,IAAI,CAACpB,cAAc,CAACwB,OAAO,CAACD,KAAK,IAAG;MAClCA,KAAK,CAACP,KAAK,CAACQ,OAAO,CAACM,IAAI,IAAG;QACzB,IAAIA,IAAI,CAACV,QAAQ,EAAE;UACjBA,QAAQ,CAACY,IAAI,CAACF,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOV,QAAQ;EACjB;EAEAa,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACF,gBAAgB,EAAE,CAACH,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACZ,KAAK,EAAE,CAAC,CAAC;EAC/E;EAEAgB,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACpC,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACC,aAAa,KAAK,IAAI;MACpC,KAAK,CAAC;QACJ,OAAO,IAAI,CAACgC,gBAAgB,EAAE,CAACxB,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEA4B,QAAQA,CAAA;IACN,IAAI,IAAI,CAACD,UAAU,EAAE,IAAI,IAAI,CAACpC,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAsC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACtC,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAuC,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAACxC,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAyC,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACR,gBAAgB,EAAE,CAACxB,MAAM,GAAG,CAAC;EAC3C;EAEAiC,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACD,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAE,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAAC1C,aAAa,EAAE;IAEzB,MAAM2C,MAAM,GAAwB;MAClC/B,OAAO,EAAE,IAAI,CAACZ,aAAa,CAACG,EAAE;MAC9ByC,SAAS,EAAE,IAAI,CAAC5C,aAAa,CAACI,IAAI;MAClCyC,aAAa,EAAE,IAAI,CAACb,gBAAgB,EAAE;MACtCc,UAAU,EAAE,IAAI,CAACZ,qBAAqB;KACvC;IAED,IAAI,CAACrC,eAAe,CAACkD,IAAI,CAACJ,MAAM,CAAC;IACjC,IAAI,CAACK,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACtD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACuD,KAAK,EAAE;IACZ,IAAI,CAACnD,MAAM,CAACiD,IAAI,EAAE;EACpB;EAEAG,eAAeA,CAACC,KAAY;IAC1B,IAAIA,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,aAAa,EAAE;MACxC,IAAI,CAACL,KAAK,EAAE;IACd;EACF;EAEQC,KAAKA,CAAA;IACX,IAAI,CAAClD,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,cAAc,GAAG,EAAE;EAC1B;EAEA;EACAqD,IAAIA,CAACC,kBAA2B;IAC9B,IAAI,CAAC7D,SAAS,GAAG,IAAI;IACrB,IAAI,CAACuD,KAAK,EAAE;IAEZ,IAAIM,kBAAkB,EAAE;MACtB,MAAM7C,KAAK,GAAG,IAAI,CAACd,eAAe,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,EAAE,KAAKoD,kBAAkB,CAAC;MACzE,IAAI7C,KAAK,EAAE;QACT,IAAI,CAACD,WAAW,CAACC,KAAK,CAAC;MACzB;IACF;EACF;CACD;AA3MUgD,UAAA,EAARpE,KAAK,EAAE,C,gEAA4B;AAC3BoE,UAAA,EAARpE,KAAK,EAAE,C,kEAA0B;AACzBoE,UAAA,EAARpE,KAAK,EAAE,C,sEAAqC;AACnCoE,UAAA,EAATnE,MAAM,EAAE,C,sEAA2D;AAC1DmE,UAAA,EAATnE,MAAM,EAAE,C,6DAAmC;AALjCC,8BAA8B,GAAAkE,UAAA,EAV1CtE,SAAS,CAAC;EACTuE,QAAQ,EAAE,6BAA6B;EACvCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPC,YAAY,EACZC,WAAW,CACZ;EACDC,WAAW,EAAE,0CAA0C;EACvDC,SAAS,EAAE,CAAC,0CAA0C;CACvD,CAAC,C,EACWzE,8BAA8B,CA4M1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}