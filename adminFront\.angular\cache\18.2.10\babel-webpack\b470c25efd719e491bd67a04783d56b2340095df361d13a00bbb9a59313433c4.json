{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r3, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_48_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", floor_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", floor_r6, \"F \");\n  }\n}\nfunction SettingTimePeriodComponent_div_48_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\\u5DF2\\u9078 \", ctx_r4.selectedHouses.length, \" \\u7B46) \");\n  }\n}\nfunction SettingTimePeriodComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"div\", 18)(3, \"nb-form-field\");\n    i0.ɵɵelement(4, \"nb-icon\", 43);\n    i0.ɵɵelementStart(5, \"input\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.searchKeyword, $event) || (ctx_r4.filterOptions.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_input_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 22)(7, \"nb-select\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.statusFilter, $event) || (ctx_r4.filterOptions.statusFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_48_Template_nb_select_selectedChange_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(8, \"nb-option\", 24);\n    i0.ɵɵtext(9, \"\\u5168\\u90E8\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-option\", 46);\n    i0.ɵɵtext(11, \"\\u9032\\u884C\\u4E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-option\", 47);\n    i0.ɵɵtext(13, \"\\u5F85\\u958B\\u653E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-option\", 48);\n    i0.ɵɵtext(15, \"\\u5DF2\\u904E\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-option\", 49);\n    i0.ɵɵtext(17, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-option\", 50);\n    i0.ɵɵtext(19, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 22)(21, \"nb-select\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.floorFilter, $event) || (ctx_r4.filterOptions.floorFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_48_Template_nb_select_selectedChange_21_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(22, \"nb-option\", 24);\n    i0.ɵɵtext(23, \"\\u5168\\u90E8\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SettingTimePeriodComponent_div_48_nb_option_24_Template, 2, 2, \"nb-option\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 52)(26, \"nb-select\", 53);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.pageSize, $event) || (ctx_r4.pageSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_48_Template_nb_select_selectedChange_26_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onPageSizeChange());\n    });\n    i0.ɵɵelementStart(27, \"nb-option\", 40);\n    i0.ɵɵtext(28, \"50\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-option\", 40);\n    i0.ɵɵtext(30, \"100\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"nb-option\", 40);\n    i0.ɵɵtext(32, \"200\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-option\", 40);\n    i0.ɵɵtext(34, \"500\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 33)(36, \"div\", 12)(37, \"div\", 54)(38, \"span\", 55);\n    i0.ɵɵtext(39);\n    i0.ɵɵtemplate(40, SettingTimePeriodComponent_div_48_span_40_Template, 2, 1, \"span\", 56);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.statusFilter);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.floorFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.availableFloors);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 50);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 200);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 500);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_div_49_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.selectedHouses.length, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_49_tr_41_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 87);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r9.CChangeStartDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_49_tr_41_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 88);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_49_tr_41_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 87);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r9.CChangeEndDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_49_tr_41_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 88);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_49_tr_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_49_tr_41_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const house_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r9.selected, $event) || (house_r9.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_49_tr_41_Template_nb_checkbox_ngModelChange_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtemplate(10, SettingTimePeriodComponent_div_49_tr_41_span_10_Template, 3, 4, \"span\", 82)(11, SettingTimePeriodComponent_div_49_tr_41_span_11_Template, 2, 0, \"span\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtemplate(13, SettingTimePeriodComponent_div_49_tr_41_span_13_Template, 3, 4, \"span\", 82)(14, SettingTimePeriodComponent_div_49_tr_41_span_14_Template, 2, 0, \"span\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\")(16, \"span\", 84);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_tr_41_Template_button_click_19_listener() {\n      const house_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const dialog_r10 = i0.ɵɵreference(57);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r10, house_r9));\n    });\n    i0.ɵɵelement(20, \"i\", 86);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const house_r9 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"table-row-selected\", house_r9.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r9.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r9.CHouseId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r9.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r9.CBuildingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", house_r9.CFloor, \"F\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", house_r9.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r9.CChangeStartDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", house_r9.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r9.CChangeEndDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r9));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getStatusText(house_r9), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !house_r9.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_49_div_42_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 91)(1, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_div_42_li_9_Template_button_click_1_listener() {\n      const page_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(page_r13));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r13 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r13 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r13);\n  }\n}\nfunction SettingTimePeriodComponent_div_49_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"nav\")(2, \"ul\", 90)(3, \"li\", 91)(4, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_div_42_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(1));\n    });\n    i0.ɵɵtext(5, \"\\u9996\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 91)(7, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_div_42_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage - 1));\n    });\n    i0.ɵɵtext(8, \"\\u4E0A\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, SettingTimePeriodComponent_div_49_div_42_li_9_Template, 3, 3, \"li\", 93);\n    i0.ɵɵelementStart(10, \"li\", 91)(11, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_div_42_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage + 1));\n    });\n    i0.ɵɵtext(12, \"\\u4E0B\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\", 91)(14, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_div_42_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.totalPages));\n    });\n    i0.ɵɵtext(15, \"\\u672B\\u9801\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"div\", 12)(3, \"div\", 60)(4, \"nb-checkbox\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_49_Template_nb_checkbox_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_49_Template_nb_checkbox_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵtext(5, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openBatchSetting());\n    });\n    i0.ɵɵelement(7, \"i\", 63);\n    i0.ɵɵtext(8, \"\\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(9, SettingTimePeriodComponent_div_49_span_9_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.exportData());\n    });\n    i0.ɵɵelement(11, \"i\", 66);\n    i0.ɵɵtext(12, \" \\u532F\\u51FA \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 67);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 68)(16, \"table\", 69)(17, \"thead\", 70)(18, \"tr\")(19, \"th\", 71)(20, \"nb-checkbox\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_49_Template_nb_checkbox_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_49_Template_nb_checkbox_ngModelChange_20_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"th\", 72);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_Template_th_click_21_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CHouseHold\"));\n    });\n    i0.ɵɵtext(22, \" \\u6236\\u578B \");\n    i0.ɵɵelement(23, \"i\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 72);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_Template_th_click_24_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CBuildingName\"));\n    });\n    i0.ɵɵtext(25, \" \\u68DF\\u5225 \");\n    i0.ɵɵelement(26, \"i\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"th\", 74);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_Template_th_click_27_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CFloor\"));\n    });\n    i0.ɵɵtext(28, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelement(29, \"i\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\", 75);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_Template_th_click_30_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeStartDate\"));\n    });\n    i0.ɵɵtext(31, \" \\u958B\\u59CB\\u65E5\\u671F \");\n    i0.ɵɵelement(32, \"i\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\", 75);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_49_Template_th_click_33_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeEndDate\"));\n    });\n    i0.ɵɵtext(34, \" \\u7D50\\u675F\\u65E5\\u671F \");\n    i0.ɵɵelement(35, \"i\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"th\", 76);\n    i0.ɵɵtext(37, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"th\", 77);\n    i0.ɵɵtext(39, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"tbody\");\n    i0.ɵɵtemplate(41, SettingTimePeriodComponent_div_49_tr_41_Template, 21, 15, \"tr\", 78);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(42, SettingTimePeriodComponent_div_49_div_42_Template, 16, 13, \"div\", 79);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.filteredHouses.length === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" / \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46 \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.paginatedHouses)(\"ngForTrackBy\", ctx_r4.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 88);\n    i0.ɵɵelement(4, \"i\", 96);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\\u5F8C\\u67E5\\u8A62\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 88)(4, \"div\", 97)(5, \"span\", 98);\n    i0.ɵɵtext(6, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 99);\n    i0.ɵɵtext(8, \"\\u8CC7\\u6599\\u8F09\\u5165\\u4E2D\\uFF0C\\u8ACB\\u7A0D\\u5019...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 111);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \\u5DF2\\u9078\\u64C7 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225 \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r4.selectedBuildingForBatch.name, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_div_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 115);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r15 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", house_r15.CHouseHold, \" (\", house_r15.CBuildingName, \"-\", house_r15.CFloor, \"F) \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_div_28_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" ...\\u7B49 \", ctx_r4.selectedHouses.length - 10, \" \\u500B \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 113);\n    i0.ɵɵtemplate(4, SettingTimePeriodComponent_ng_template_52_div_28_span_4_Template, 2, 3, \"span\", 114)(5, SettingTimePeriodComponent_ng_template_52_div_28_span_5_Template, 2, 1, \"span\", 83);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5C07\\u5957\\u7528\\u5230\\u5DF2\\u9078\\u64C7\\u7684 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedHouses.slice(0, 10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 10);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_div_3_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      const house_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r20.selected, $event) || (house_r20.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r20 = ctx.$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r20.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r20.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r20.CHouseHold, \" (\", house_r20.CBuildingName, \") \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_div_3_nb_checkbox_1_Template, 2, 4, \"nb-checkbox\", 121);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", floor_r18.houses);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"nb-checkbox\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const floor_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      i0.ɵɵtwoWayBindingSet(floor_r18.selected, $event) || (floor_r18.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      const floor_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.onFloorSelectionChange(floor_r18));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_div_3_Template, 2, 1, \"div\", 119);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r18 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", floor_r18.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r18.floorNumber, \"F (\", floor_r18.houses.length, \" \\u6236) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", floor_r18.selected);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_div_29_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_Template, 4, 4, \"div\", 117);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedBuildingForBatch.floors);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"nb-checkbox\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_52_div_29_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.applyToAll, $event) || (ctx_r4.batchSettings.applyToAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_52_div_29_div_3_Template, 2, 1, \"div\", 116);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.applyToAll);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u90E8\\u6236\\u5225 (\", ctx_r4.flattenedHouses.length, \" \\u500B) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.batchSettings.applyToAll && ctx_r4.selectedBuildingForBatch);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225)\");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 100)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_52_span_3_Template, 2, 1, \"span\", 101)(4, SettingTimePeriodComponent_ng_template_52_span_4_Template, 2, 1, \"span\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"div\", 103)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n    i0.ɵɵelementStart(9, \"span\", 104);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 26)(12, \"nb-form-field\", 27);\n    i0.ɵɵelement(13, \"nb-icon\", 28);\n    i0.ɵɵelementStart(14, \"input\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_52_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.startDate, $event) || (ctx_r4.batchSettings.startDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 30, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 31);\n    i0.ɵɵtext(18, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\", 27);\n    i0.ɵɵelement(20, \"nb-icon\", 28);\n    i0.ɵɵelementStart(21, \"input\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_52_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.endDate, $event) || (ctx_r4.batchSettings.endDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 30, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 103)(25, \"label\");\n    i0.ɵɵtext(26, \"\\u9069\\u7528\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 106);\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_ng_template_52_div_28_Template, 6, 3, \"div\", 107)(29, SettingTimePeriodComponent_ng_template_52_div_29_Template, 4, 3, \"div\", 102);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-card-footer\", 108)(31, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_52_Template_button_click_31_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r21));\n    });\n    i0.ɵɵtext(32, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_52_Template_button_click_33_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onBatchSubmit(ref_r21));\n    });\n    i0.ɵɵtext(34, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(35, SettingTimePeriodComponent_ng_template_52_span_35_Template, 2, 1, \"span\", 102);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const batchStartDate_r22 = i0.ɵɵreference(16);\n    const batchEndDate_r23 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedBuildingForBatch && ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", batchStartDate_r22);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.startDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", batchEndDate_r23);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.endDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 122);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 123);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 122)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 124);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 125)(7, \"div\", 126)(8, \"label\", 127);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 104);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 128);\n    i0.ɵɵelement(13, \"nb-icon\", 28);\n    i0.ɵɵelementStart(14, \"input\", 129);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_56_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 30, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 130);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 28);\n    i0.ɵɵelementStart(21, \"input\", 131);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_56_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 30, 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 123)(25, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_56_Template_button_click_25_listener() {\n      const ref_r25 = i0.ɵɵrestoreView(_r24).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r25));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_56_Template_button_click_27_listener() {\n      const ref_r25 = i0.ɵɵrestoreView(_r24).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSubmit(ref_r25));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r26 = i0.ɵɵreference(16);\n    const changeEndDate_r27 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r4.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r27);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    // 新增的屬性\n    this.buildingGroups = [];\n    this.buildingOptions = [];\n    this.selectedBuilding = '';\n    this.availableFloors = [];\n    // 篩選和搜尋\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 批次設定\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: true,\n      selectedBuildings: [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    this.selectedBuildingForBatch = null;\n    // 表格視圖相關屬性\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.loading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalPages = 1;\n    // 排序相關\n    this.sortField = '';\n    this.sortDirection = 'asc';\n    // 數學函數引用\n    this.Math = Math;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CBuildingName: '未分類',\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  // 新增：建案變更處理\n  onBuildCaseChange() {\n    // 重置所有相關狀態\n    this.resetAllStates();\n    // 執行查詢\n    this.getHouseChangeDate();\n  }\n  // 新增：重置所有狀態\n  resetAllStates() {\n    // 重置數據\n    this.houseChangeDates = [];\n    this.convertedHouseArray = [];\n    this.buildingGroups = [];\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    // 重置篩選條件\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 重置選擇狀態\n    this.selectAll = false;\n    this.selectedBuilding = '';\n    // 重置分頁\n    this.currentPage = 1;\n    this.totalPages = 1;\n    // 重置可用選項\n    this.buildingOptions = [];\n    this.availableFloors = [];\n    // 重置排序\n    this.sortField = '';\n    this.sortDirection = 'asc';\n  }\n  getHouseChangeDate() {\n    // 如果沒有選擇建案，直接返回\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\n      this.loading = false;\n      return;\n    }\n    this.loading = true;\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      this.loading = false;\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          // 新增：建立棟別分組資料\n          this.buildBuildingGroups(res.Entries);\n          // 新增：建立扁平化資料\n          this.buildFlattenedHouses(res.Entries);\n        }\n      }\n    });\n  }\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n  buildBuildingGroups(data) {\n    const buildingMap = new Map();\n    data.forEach(household => {\n      const houseType = household.CHouseHold || ''; // 戶型\n      household.CHouses?.forEach(house => {\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\n        const floor = house.CFloor || 0;\n        if (!buildingMap.has(buildingName)) {\n          buildingMap.set(buildingName, new Map());\n        }\n        const floorMap = buildingMap.get(buildingName);\n        if (!floorMap.has(floor)) {\n          floorMap.set(floor, []);\n        }\n        floorMap.get(floor).push({\n          CHouseHold: houseType,\n          // 戶型\n          CBuildingName: buildingName,\n          // 棟別\n          CHouseId: house.CHouseId || 0,\n          CFloor: floor,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 轉換為BuildingGroup格式\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n      const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n      .map(([floorNumber, houses]) => ({\n        floorNumber,\n        houses: houses.sort((a, b) => {\n          // 排序邏輯：先按戶型排序，再按樓層排序\n          if (a.CHouseHold !== b.CHouseHold) {\n            return a.CHouseHold.localeCompare(b.CHouseHold);\n          }\n          return a.CFloor - b.CFloor;\n        }),\n        selected: false\n      }));\n      return {\n        name: buildingName,\n        floors,\n        selected: false\n      };\n    }).sort((a, b) => a.name.localeCompare(b.name));\n    // 更新棟別選項和可用樓層\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n    this.updateAvailableFloors();\n  }\n  // 新增：更新可用樓層\n  updateAvailableFloors() {\n    const floorsSet = new Set();\n    this.buildingGroups.forEach(building => {\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n        building.floors.forEach(floor => {\n          floorsSet.add(floor.floorNumber);\n        });\n      }\n    });\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n  }\n  // 修改：棟別選擇變更處理\n  onBuildingChange() {\n    // 重置選擇狀態\n    this.selectedHouses.forEach(house => house.selected = false);\n    this.selectedHouses = [];\n    this.selectAll = false;\n    // 重置分頁到第一頁\n    this.currentPage = 1;\n    // 重置樓層篩選\n    this.filterOptions.floorFilter = '';\n    // 更新可用樓層\n    this.updateAvailableFloors();\n    // 設定棟別篩選並執行搜尋\n    this.filterOptions.buildingFilter = this.selectedBuilding;\n    this.onSearch();\n  }\n  // 新增：取得過濾後的棟別資料\n  getFilteredBuildings() {\n    return this.buildingGroups.filter(building => {\n      // 棟別篩選\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n        return false;\n      }\n      // 關鍵字搜尋 (搜尋戶型)\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n        if (!hasMatchingHouse) {\n          return false;\n        }\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n        if (!hasMatchingStatus) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n        if (!hasMatchingFloor) {\n          return false;\n        }\n      }\n      return true;\n    }).map(building => {\n      // 對每個棟別，也要篩選其樓層和戶別\n      const filteredBuilding = {\n        ...building\n      };\n      filteredBuilding.floors = building.floors.filter(floor => {\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (floor.floorNumber !== floorNumber) {\n            return false;\n          }\n        }\n        // 檢查該樓層是否有符合條件的戶別\n        const hasValidHouses = floor.houses.some(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return hasValidHouses;\n      }).map(floor => {\n        // 篩選戶別\n        const filteredFloor = {\n          ...floor\n        };\n        filteredFloor.houses = floor.houses.filter(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return filteredFloor;\n      });\n      return filteredBuilding;\n    });\n  }\n  // 修復：檢查戶別是否符合狀態篩選\n  matchesStatusFilter(house) {\n    const status = this.getHouseStatus(house);\n    switch (this.filterOptions.statusFilter) {\n      case 'active':\n        return status === 'active';\n      case 'pending':\n        return status === 'pending';\n      case 'expired':\n        return status === 'expired';\n      case 'not-set':\n        return status === 'not-set';\n      case 'disabled':\n        return status === 'disabled';\n      default:\n        return true;\n      // 全部狀態\n    }\n  }\n  // 修復：取得戶別狀態\n  getHouseStatus(house) {\n    if (!house.CHouseId) {\n      return 'disabled';\n    }\n    // 檢查是否有設定開放時段\n    if (!house.CChangeStartDate || !house.CChangeEndDate || house.CChangeStartDate === '' || house.CChangeEndDate === '') {\n      return 'not-set';\n    }\n    try {\n      // 處理日期字串，支援多種格式\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      // 解析開始日期\n      let startDate;\n      if (house.CChangeStartDate.includes('T')) {\n        startDate = new Date(house.CChangeStartDate);\n      } else {\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\n      }\n      // 解析結束日期\n      let endDate;\n      if (house.CChangeEndDate.includes('T')) {\n        endDate = new Date(house.CChangeEndDate);\n      } else {\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\n      }\n      // 檢查日期有效性\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\n        console.warn('Invalid date format:', {\n          start: house.CChangeStartDate,\n          end: house.CChangeEndDate,\n          houseId: house.CHouseId\n        });\n        return 'not-set';\n      }\n      // 轉換為日期比較（不含時間）\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n      // 判斷狀態\n      if (today < startDateOnly) {\n        return 'pending'; // 待開放\n      } else if (today >= startDateOnly && today <= endDateOnly) {\n        return 'active'; // 進行中\n      } else {\n        return 'expired'; // 已過期\n      }\n    } catch (error) {\n      console.error('Error parsing dates:', error, house);\n      return 'not-set';\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  // 修改：開啟批次設定對話框\n  openBatchSetting(building) {\n    this.selectedBuildingForBatch = building || null;\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\n    const hasSelectedHouses = this.selectedHouses.length > 0;\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: !hasSelectedHouses && !building,\n      selectedBuildings: building ? [building.name] : [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    // 重置選擇狀態\n    if (building) {\n      building.floors.forEach(floor => {\n        floor.selected = false;\n        floor.houses.forEach(house => house.selected = false);\n      });\n    }\n    // 開啟對話框\n    this.dialogService.open(this.batchSettingDialog);\n  }\n  // 新增：樓層選擇變更處理\n  onFloorSelectionChange(floor) {\n    if (floor.selected) {\n      floor.houses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = true;\n        }\n      });\n    } else {\n      floor.houses.forEach(house => house.selected = false);\n    }\n  }\n  // 修改：批次提交\n  onBatchSubmit(ref) {\n    // 驗證批次設定\n    this.valid.clear();\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 收集要更新的房屋\n    const housesToUpdate = [];\n    if (this.batchSettings.applyToAll) {\n      // 全部戶別\n      this.flattenedHouses.forEach(house => {\n        if (house.CHouseId) {\n          housesToUpdate.push({\n            CHouseId: house.CHouseId,\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n          });\n        }\n      });\n    } else {\n      // 使用已選擇的戶別\n      if (this.selectedHouses.length > 0) {\n        this.selectedHouses.forEach(house => {\n          if (house.CHouseId) {\n            housesToUpdate.push({\n              CHouseId: house.CHouseId,\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n            });\n          }\n        });\n      } else if (this.selectedBuildingForBatch) {\n        // 如果沒有選擇的戶別，使用舊的邏輯\n        this.selectedBuildingForBatch.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.selected && house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      }\n    }\n    if (housesToUpdate.length === 0) {\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\n      return;\n    }\n    // 調用API進行批次更新\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: housesToUpdate\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n        // 清除選擇狀態\n        this.selectedHouses.forEach(house => house.selected = false);\n        this.selectedHouses = [];\n        this.selectAll = false;\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  // 新增：取得狀態樣式類別\n  getStatusClass(house) {\n    const status = this.getHouseStatus(house);\n    return `status-${status}`;\n  }\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n  openHouseDialog(house) {\n    if (house.CHouseId) {\n      // 使用現有的openModel方法\n      this.openModel(this.dialog, house);\n    }\n  }\n  // 新增：建立扁平化房屋資料\n  buildFlattenedHouses(data) {\n    this.flattenedHouses = [];\n    data.forEach(household => {\n      const houseType = household.CHouseHold || '';\n      household.CHouses?.forEach(house => {\n        this.flattenedHouses.push({\n          CHouseHold: houseType,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId || 0,\n          CFloor: house.CFloor || 0,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 初始化篩選和分頁\n    this.onSearch();\n    // 調試：輸出狀態統計\n    this.debugStatusCounts();\n  }\n  // 調試：輸出狀態統計\n  debugStatusCounts() {\n    const statusCounts = {\n      active: 0,\n      pending: 0,\n      expired: 0,\n      'not-set': 0,\n      disabled: 0\n    };\n    this.flattenedHouses.forEach(house => {\n      const status = this.getHouseStatus(house);\n      if (statusCounts.hasOwnProperty(status)) {\n        statusCounts[status]++;\n      }\n    });\n    console.log('狀態統計:', statusCounts);\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\n  }\n  // 修改：搜尋和篩選\n  onSearch() {\n    // 記錄篩選前的已選擇項目\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\n    this.filteredHouses = this.flattenedHouses.filter(house => {\n      // 關鍵字搜尋\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n          return false;\n        }\n      }\n      // 棟別篩選\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n        return false;\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        if (!this.matchesStatusFilter(house)) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        if (house.CFloor !== floorNumber) {\n          return false;\n        }\n      }\n      return true;\n    });\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\n    this.selectedHouses = this.filteredHouses.filter(house => previouslySelectedIds.includes(house.CHouseId));\n    // 更新扁平化資料中的選擇狀態\n    this.flattenedHouses.forEach(house => {\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\n    });\n    // 更新全選狀態\n    this.updateSelectAllState();\n    // 重新計算分頁\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：更新全選狀態\n  updateSelectAllState() {\n    if (this.paginatedHouses.length === 0) {\n      this.selectAll = false;\n    } else {\n      this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n    }\n  }\n  // 修改：更新分頁\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n    // 更新全選狀態\n    this.updateSelectAllState();\n  }\n  // 新增：頁面大小變更\n  onPageSizeChange() {\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：跳轉頁面\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.updatePagination();\n    }\n  }\n  // 新增：取得可見頁碼\n  getVisiblePages() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 新增：全選/取消全選\n  onSelectAllChange() {\n    this.paginatedHouses.forEach(house => {\n      if (house.CHouseId) {\n        house.selected = this.selectAll;\n      }\n    });\n    this.updateSelectedHouses();\n  }\n  // 新增：單一選擇變更\n  onHouseSelectionChange() {\n    this.updateSelectedHouses();\n    this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n  }\n  // 新增：更新已選擇房屋列表\n  updateSelectedHouses() {\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n  }\n  // 新增：排序\n  sort(field) {\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    this.filteredHouses.sort((a, b) => {\n      let aValue = a[field];\n      let bValue = b[field];\n      // 處理日期排序\n      if (field.includes('Date')) {\n        aValue = aValue ? new Date(aValue).getTime() : 0;\n        bValue = bValue ? new Date(bValue).getTime() : 0;\n      }\n      // 處理數字排序\n      if (field === 'CFloor') {\n        aValue = Number(aValue) || 0;\n        bValue = Number(bValue) || 0;\n      }\n      // 處理字串排序\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (aValue < bValue) {\n        return this.sortDirection === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return this.sortDirection === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    this.updatePagination();\n  }\n  // 新增：TrackBy函數\n  trackByHouseId(_index, house) {\n    return house.CHouseId;\n  }\n  // 新增：取得狀態文字\n  getStatusText(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return '進行中';\n      case 'pending':\n        return '待開放';\n      case 'expired':\n        return '已過期';\n      case 'not-set':\n        return '未設定';\n      case 'disabled':\n        return '已停用';\n      default:\n        return '未知';\n    }\n  }\n  // 新增：匯出資料\n  exportData() {\n    // 實現匯出功能\n    const csvContent = this.generateCSV();\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  // 新增：產生CSV內容\n  generateCSV() {\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\n    const rows = this.filteredHouses.map(house => [house.CHouseHold, house.CBuildingName, `${house.CFloor}F`, house.CChangeStartDate || '未設定', house.CChangeEndDate || '未設定', this.getStatusText(house)]);\n    const csvContent = [headers, ...rows].map(row => row.map(cell => `\"${cell}\"`).join(',')).join('\\n');\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\n  }\n  static {\n    this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingTimePeriodComponent,\n      selectors: [[\"ngx-setting-time-period\"]],\n      viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 58,\n      vars: 13,\n      consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"batchSettingDialog\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"batchStartDate\", \"\"], [\"batchEndDate\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"integrated-header\"], [1, \"page-header\", \"mb-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"page-title\"], [1, \"fas\", \"fa-calendar-alt\", \"me-2\"], [1, \"page-description\", \"mb-0\"], [1, \"primary-filters\", \"mb-3\"], [1, \"row\", \"align-items-end\"], [1, \"col-lg-3\", \"col-md-4\", \"mb-2\"], [1, \"form-label\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-2\", \"col-md-3\", \"mb-2\"], [\"placeholder\", \"\\u5168\\u90E8\\u68DF\\u5225\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"\"], [1, \"col-lg-4\", \"col-md-5\", \"mb-2\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-fill\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u958B\\u59CB\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [1, \"mx-2\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u7D50\\u675F\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-lg-3\", \"col-md-12\", \"mb-2\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-end\", \"h-100\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-search\", \"me-1\"], [\"class\", \"advanced-filters\", 4, \"ngIf\"], [\"class\", \"table-view mt-4\", 4, \"ngIf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [3, \"value\"], [1, \"advanced-filters\"], [1, \"row\", \"align-items-center\"], [\"nbPrefix\", \"\", \"icon\", \"search-outline\"], [\"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u578B\\u3001\\u68DF\\u5225...\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u72C0\\u614B\\u7BE9\\u9078\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"active\"], [\"value\", \"pending\"], [\"value\", \"expired\"], [\"value\", \"not-set\"], [\"value\", \"disabled\"], [\"placeholder\", \"\\u6A13\\u5C64\\u7BE9\\u9078\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-lg-2\", \"col-md-2\", \"mb-2\"], [\"placeholder\", \"\\u986F\\u793A\\u7B46\\u6578\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"data-summary\"], [1, \"text-muted\", \"small\"], [\"class\", \"text-primary ms-1\", 4, \"ngIf\"], [1, \"text-primary\", \"ms-1\"], [1, \"table-view\", \"mt-4\"], [1, \"table-toolbar\", \"mb-3\"], [1, \"batch-actions\"], [3, \"ngModelChange\", \"ngModel\"], [\"title\", \"\\u6279\\u6B21\\u8A2D\\u5B9A\\u9078\\u4E2D\\u7684\\u6236\\u5225\\u958B\\u653E\\u6642\\u6BB5\", 1, \"btn\", \"btn-sm\", \"btn-warning\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-cogs\", \"me-1\"], [\"class\", \"badge badge-light ml-1\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-success\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\"], [1, \"pagination-info\"], [1, \"table-container\"], [1, \"table\", \"table-hover\"], [1, \"table-header\"], [\"width\", \"50\"], [\"width\", \"100\", 1, \"sortable\", 3, \"click\"], [1, \"fas\", \"fa-sort\"], [\"width\", \"80\", 1, \"sortable\", 3, \"click\"], [\"width\", \"120\", 1, \"sortable\", 3, \"click\"], [\"width\", \"100\"], [\"width\", \"80\"], [3, \"table-row-selected\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"pagination-container mt-3\", 4, \"ngIf\"], [1, \"badge\", \"badge-light\", \"ml-1\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"date-display\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"status-badge\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-edit\"], [1, \"date-display\"], [1, \"text-muted\"], [1, \"pagination-container\", \"mt-3\"], [1, \"pagination\", \"justify-content-center\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"mb-3\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"mt-2\"], [2, \"width\", \"600px\", \"max-height\", \"90vh\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"form-group\"], [1, \"text-red-600\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"selection-options\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"text-primary\"], [1, \"alert\", \"alert-info\"], [1, \"selected-houses-preview\"], [\"class\", \"badge badge-primary mr-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"mr-1\", \"mb-1\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"floor-selection\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-selection\"], [\"class\", \"house-selection ml-4\", 4, \"ngIf\"], [1, \"house-selection\", \"ml-4\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n      template: function SettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 9)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 10)(5, \"div\", 11)(6, \"div\", 12)(7, \"div\")(8, \"h1\", 13);\n          i0.ɵɵelement(9, \"i\", 14);\n          i0.ɵɵtext(10, \" \\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5\\u8A2D\\u5B9A \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 15);\n          i0.ɵɵtext(12, \" \\u7BA1\\u7406\\u5404\\u6236\\u5225\\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5\\uFF0C\\u8A2D\\u5B9A\\u958B\\u59CB\\u8207\\u7D50\\u675F\\u65E5\\u671F\\uFF0C\\u63A7\\u5236\\u5BA2\\u6236\\u9078\\u6A23\\u6B0A\\u9650 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 16)(14, \"div\", 17)(15, \"div\", 18)(16, \"label\", 19);\n          i0.ɵɵtext(17, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"nb-select\", 20);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_18_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildCaseChange());\n          });\n          i0.ɵɵtemplate(19, SettingTimePeriodComponent_nb_option_19_Template, 2, 2, \"nb-option\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 22)(21, \"label\", 19);\n          i0.ɵɵtext(22, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-select\", 23);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildingChange());\n          });\n          i0.ɵɵelementStart(24, \"nb-option\", 24);\n          i0.ɵɵtext(25, \"\\u5168\\u90E8\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(26, SettingTimePeriodComponent_nb_option_26_Template, 2, 2, \"nb-option\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 25)(28, \"label\", 19);\n          i0.ɵɵtext(29, \"\\u958B\\u653E\\u65E5\\u671F\\u7BC4\\u570D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 26)(31, \"nb-form-field\", 27);\n          i0.ɵɵelement(32, \"nb-icon\", 28);\n          i0.ɵɵelementStart(33, \"input\", 29);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_33_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"nb-datepicker\", 30, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"span\", 31);\n          i0.ɵɵtext(37, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"nb-form-field\", 27);\n          i0.ɵɵelement(39, \"nb-icon\", 28);\n          i0.ɵɵelementStart(40, \"input\", 32);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(41, \"nb-datepicker\", 30, 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"div\", 33)(44, \"div\", 34)(45, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_45_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵelement(46, \"i\", 36);\n          i0.ɵɵtext(47, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(48, SettingTimePeriodComponent_div_48_Template, 41, 11, \"div\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(49, SettingTimePeriodComponent_div_49_Template, 43, 31, \"div\", 38)(50, SettingTimePeriodComponent_div_50_Template, 7, 0, \"div\", 39)(51, SettingTimePeriodComponent_div_51_Template, 9, 0, \"div\", 39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(52, SettingTimePeriodComponent_ng_template_52_Template, 36, 9, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(54, SettingTimePeriodComponent_ng_template_54_Template, 4, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(56, SettingTimePeriodComponent_ng_template_56_Template, 29, 6, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const StartDate_r28 = i0.ɵɵreference(35);\n          const EndDate_r29 = i0.ɵɵreference(42);\n          i0.ɵɵadvance(18);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingOptions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", StartDate_r28);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", EndDate_r29);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length === 0 && ctx.houseChangeDates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\".integrated-header[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 0.5rem;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n  margin-bottom: 1.5rem;\\n  overflow: hidden;\\n  border: 1px solid rgba(184, 166, 118, 0.2);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: white;\\n  padding: 1.5rem;\\n  margin-bottom: 0;\\n  border-radius: 0.5rem 0.5rem 0 0;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  width: 100px;\\n  height: 100px;\\n  background: rgba(255, 255, 255, 0.15);\\n  border-radius: 50%;\\n  transform: translate(30px, -30px);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 80px;\\n  height: 80px;\\n  background: rgba(255, 255, 255, 0.08);\\n  border-radius: 50%;\\n  transform: translate(-20px, 20px);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  color: white;\\n  position: relative;\\n  z-index: 1;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #F5E6A3;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  opacity: 0.95;\\n  font-size: 0.9rem;\\n  color: rgba(255, 255, 255, 0.95);\\n  position: relative;\\n  z-index: 1;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n  color: #F5E6A3;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 0.9;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: rgba(255, 255, 255, 0.85);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  color: #F5E6A3;\\n  font-weight: 600;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  background: linear-gradient(to bottom, #fafafa 0%, #f5f5f5 100%);\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.2);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #5a5a5a;\\n  margin-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding-top: 0.5rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  margin-right: 1rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.3s ease;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #A69660 0%, #95854A 100%);\\n  border-color: #A69660;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(184, 166, 118, 0.3);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 1.5rem;\\n  background-color: #ffffff;\\n  border-top: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]   .text-primary[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #B8A676;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%], \\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   nb-select.ng-touched.ng-valid[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.5);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%] {\\n  background: linear-gradient(to bottom, #fafafa 0%, #f5f5f5 100%);\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid rgba(184, 166, 118, 0.2);\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  font-weight: 500;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #D4B96A 0%, #C4A85A 100%);\\n  border-color: #D4B96A;\\n  color: #5a4a2a;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #C4A85A 0%, #B4984A 100%);\\n  border-color: #C4A85A;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  color: #B8A676;\\n  font-weight: 600;\\n  font-size: 0.7rem;\\n  padding: 0.2rem 0.4rem;\\n  border-radius: 0.25rem;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #A69660 0%, #95854A 100%);\\n  border-color: #A69660;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #5a5a5a;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(184, 166, 118, 0.2);\\n  border-radius: 0.375rem;\\n  overflow: hidden;\\n  box-shadow: 0 2px 4px rgba(184, 166, 118, 0.1);\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(to bottom, #F8F6F0 0%, #F0EDE5 100%);\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n  color: #5a5a5a;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.2);\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: background-color 0.2s ease;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  opacity: 0.5;\\n  color: #B8A676;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: background-color 0.2s ease;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.05);\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-selected[_ngcontent-%COMP%] {\\n  background-color: rgba(184, 166, 118, 0.15);\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  vertical-align: middle;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #E8F5E8 0%, #D4EDDA 100%);\\n  color: #2D5A2D;\\n  border-color: rgba(45, 90, 45, 0.2);\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-pending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FFF8E1 0%, #FFF3CD 100%);\\n  color: #8B6914;\\n  border-color: rgba(139, 105, 20, 0.2);\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-expired[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FFEBEE 0%, #F8D7DA 100%);\\n  color: #8B2635;\\n  border-color: rgba(139, 38, 53, 0.2);\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-not-set[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F5F5F5 0%, #E2E3E5 100%);\\n  color: #5A5A5A;\\n  border-color: rgba(90, 90, 90, 0.2);\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-disabled[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FAFAFA 0%, #F8F9FA 100%);\\n  color: #8A8A8A;\\n  border-color: rgba(138, 138, 138, 0.2);\\n}\\n.table-view[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%] {\\n  font-family: monospace;\\n  font-size: 0.875rem;\\n}\\n\\n.search-enhanced[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #5a5a5a;\\n  border-color: rgba(184, 166, 118, 0.3);\\n  transition: all 0.2s ease;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: rgba(184, 166, 118, 0.5);\\n  color: #B8A676;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #aaa;\\n  background-color: #fff;\\n  border-color: rgba(184, 166, 118, 0.2);\\n}\\n\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  padding-left: 1rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\\n  gap: 0.25rem;\\n  margin-top: 0.5rem;\\n  padding: 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  margin-bottom: 1rem;\\n  border: 1px solid transparent;\\n  border-radius: 0.375rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%] {\\n  color: #0c5460;\\n  background-color: #d1ecf1;\\n  border-color: #bee5eb;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  font-weight: 600;\\n}\\n\\n@media (max-width: 992px) {\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n    justify-content: center;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    border-radius: 0.375rem 0.375rem 0 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    margin-top: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.75rem;\\n    text-align: left !important;\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .flex-fill[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .mx-2[_ngcontent-%COMP%] {\\n    margin: 0.25rem 0 !important;\\n    text-align: center;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n    margin-bottom: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 0.5rem;\\n  }\\n  .search-enhanced[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0.25rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(3) {\\n    display: none;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .integrated-header[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n    border-radius: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    border-radius: 0.25rem 0.25rem 0 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    margin-right: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n    display: none;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n    margin-bottom: 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .query-section[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(4) {\\n    display: none;\\n  }\\n}\\nnb-card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #5a5a5a;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n  border-color: #B8A676;\\n  transition: all 0.3s ease;\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(184, 166, 118, 0.3);\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-color: #dee2e6;\\n  color: #6c757d;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  color: #5a5a5a;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  margin-right: 0.5rem;\\n}\\n.status-indicator.active[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n}\\n.status-indicator.pending[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n}\\n.status-indicator.expired[_ngcontent-%COMP%] {\\n  background-color: #dc3545;\\n}\\n.status-indicator.not-set[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n}\\n.status-indicator.disabled[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "building_r3", "floor_r6", "ctx_r4", "selectedHouses", "length", "ɵɵelement", "ɵɵtwoWayListener", "SettingTimePeriodComponent_div_48_Template_input_ngModelChange_5_listener", "$event", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "filterOptions", "searchKeyword", "ɵɵresetView", "ɵɵlistener", "onSearch", "SettingTimePeriodComponent_div_48_Template_nb_select_ngModelChange_7_listener", "statusFilter", "SettingTimePeriodComponent_div_48_Template_nb_select_selectedChange_7_listener", "SettingTimePeriodComponent_div_48_Template_nb_select_ngModelChange_21_listener", "floorFilter", "SettingTimePeriodComponent_div_48_Template_nb_select_selectedChange_21_listener", "ɵɵtemplate", "SettingTimePeriodComponent_div_48_nb_option_24_Template", "SettingTimePeriodComponent_div_48_Template_nb_select_ngModelChange_26_listener", "pageSize", "SettingTimePeriodComponent_div_48_Template_nb_select_selectedChange_26_listener", "onPageSizeChange", "SettingTimePeriodComponent_div_48_span_40_Template", "ɵɵtwoWayProperty", "availableFloors", "filteredHouses", "ɵɵpipeBind2", "house_r9", "CChangeStartDate", "CChangeEndDate", "SettingTimePeriodComponent_div_49_tr_41_Template_nb_checkbox_ngModelChange_2_listener", "_r8", "$implicit", "selected", "onHouseSelectionChange", "SettingTimePeriodComponent_div_49_tr_41_span_10_Template", "SettingTimePeriodComponent_div_49_tr_41_span_11_Template", "SettingTimePeriodComponent_div_49_tr_41_span_13_Template", "SettingTimePeriodComponent_div_49_tr_41_span_14_Template", "SettingTimePeriodComponent_div_49_tr_41_Template_button_click_19_listener", "dialog_r10", "ɵɵreference", "openModel", "ɵɵclassProp", "CHouseId", "ɵɵtextInterpolate", "CHouseHold", "CBuildingName", "CFloor", "ɵɵclassMap", "getStatusClass", "getStatusText", "SettingTimePeriodComponent_div_49_div_42_li_9_Template_button_click_1_listener", "page_r13", "_r12", "goToPage", "currentPage", "SettingTimePeriodComponent_div_49_div_42_Template_button_click_4_listener", "_r11", "SettingTimePeriodComponent_div_49_div_42_Template_button_click_7_listener", "SettingTimePeriodComponent_div_49_div_42_li_9_Template", "SettingTimePeriodComponent_div_49_div_42_Template_button_click_11_listener", "SettingTimePeriodComponent_div_49_div_42_Template_button_click_14_listener", "totalPages", "getVisiblePages", "SettingTimePeriodComponent_div_49_Template_nb_checkbox_ngModelChange_4_listener", "_r7", "selectAll", "onSelectAllChange", "SettingTimePeriodComponent_div_49_Template_button_click_6_listener", "openBatchSetting", "SettingTimePeriodComponent_div_49_span_9_Template", "SettingTimePeriodComponent_div_49_Template_button_click_10_listener", "exportData", "SettingTimePeriodComponent_div_49_Template_nb_checkbox_ngModelChange_20_listener", "SettingTimePeriodComponent_div_49_Template_th_click_21_listener", "sort", "SettingTimePeriodComponent_div_49_Template_th_click_24_listener", "SettingTimePeriodComponent_div_49_Template_th_click_27_listener", "SettingTimePeriodComponent_div_49_Template_th_click_30_listener", "SettingTimePeriodComponent_div_49_Template_th_click_33_listener", "SettingTimePeriodComponent_div_49_tr_41_Template", "SettingTimePeriodComponent_div_49_div_42_Template", "ɵɵtextInterpolate3", "Math", "min", "sortField", "sortDirection", "paginatedHouses", "trackByHouseId", "selectedBuildingForBatch", "name", "house_r15", "SettingTimePeriodComponent_ng_template_52_div_28_span_4_Template", "SettingTimePeriodComponent_ng_template_52_div_28_span_5_Template", "slice", "SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener", "house_r20", "_r19", "ɵɵtextInterpolate2", "SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_div_3_nb_checkbox_1_Template", "floor_r18", "houses", "SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener", "_r17", "onFloorSelectionChange", "SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_div_3_Template", "floorNumber", "SettingTimePeriodComponent_ng_template_52_div_29_div_3_div_1_Template", "floors", "SettingTimePeriodComponent_ng_template_52_div_29_Template_nb_checkbox_ngModelChange_1_listener", "_r16", "batchSettings", "applyToAll", "SettingTimePeriodComponent_ng_template_52_div_29_div_3_Template", "flattenedHouses", "SettingTimePeriodComponent_ng_template_52_span_3_Template", "SettingTimePeriodComponent_ng_template_52_span_4_Template", "SettingTimePeriodComponent_ng_template_52_Template_input_ngModelChange_14_listener", "_r14", "startDate", "SettingTimePeriodComponent_ng_template_52_Template_input_ngModelChange_21_listener", "endDate", "SettingTimePeriodComponent_ng_template_52_div_28_Template", "SettingTimePeriodComponent_ng_template_52_div_29_Template", "SettingTimePeriodComponent_ng_template_52_Template_button_click_31_listener", "ref_r21", "dialogRef", "onClose", "SettingTimePeriodComponent_ng_template_52_Template_button_click_33_listener", "onBatchSubmit", "SettingTimePeriodComponent_ng_template_52_span_35_Template", "batchStartDate_r22", "batchEndDate_r23", "SettingTimePeriodComponent_ng_template_56_Template_input_ngModelChange_14_listener", "_r24", "selectedHouseChangeDate", "SettingTimePeriodComponent_ng_template_56_Template_input_ngModelChange_21_listener", "SettingTimePeriodComponent_ng_template_56_Template_button_click_25_listener", "ref_r25", "SettingTimePeriodComponent_ng_template_56_Template_button_click_27_listener", "onSubmit", "changeStartDate_r26", "changeEndDate_r27", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "buildingGroups", "buildingOptions", "selectedBuilding", "buildingFilter", "selectedBuildings", "selectedFloors", "loading", "undefined", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "searchQuery", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "validation", "errorMessages", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "for<PERSON>ach", "household", "CHouses", "house", "floor", "push", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "onBuildCaseChange", "resetAllStates", "houseChangeDates", "convertedHouseArray", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "buildBuildingGroups", "buildFlattenedHouses", "data", "buildingMap", "Map", "houseType", "buildingName", "has", "set", "floorMap", "get", "localeCompare", "bg", "updateAvailableFloors", "building", "onBuildingChange", "getFilteredBuildings", "filter", "keyword", "toLowerCase", "hasMatchingHouse", "some", "includes", "hasMatchingStatus", "matchesStatusFilter", "parseInt", "hasMatchingFloor", "filteredBuilding", "hasValidHouses", "filteredFloor", "status", "getHouseStatus", "now", "today", "getFullYear", "getMonth", "getDate", "isNaN", "getTime", "console", "warn", "start", "end", "houseId", "startDateOnly", "endDateOnly", "error", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "hasSelectedHouses", "batchSettingDialog", "housesToUpdate", "openHouseDialog", "dialog", "debugStatusCounts", "statusCounts", "active", "pending", "expired", "disabled", "hasOwnProperty", "log", "toISOString", "split", "previouslySelectedIds", "updateSelectAllState", "updatePagination", "every", "ceil", "startIndex", "endIndex", "page", "pages", "maxVisible", "max", "i", "updateSelectedHouses", "field", "aValue", "bValue", "Number", "_index", "csv<PERSON><PERSON>nt", "generateCSV", "blob", "Blob", "type", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "rows", "row", "cell", "join", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "Router", "i7", "EventService", "selectors", "viewQuery", "SettingTimePeriodComponent_Query", "rf", "ctx", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingTimePeriodComponent_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_18_listener", "_r1", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_18_listener", "SettingTimePeriodComponent_nb_option_19_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_23_listener", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON><PERSON>_23_listener", "SettingTimePeriodComponent_nb_option_26_Template", "SettingTimePeriodComponent_Template_input_ngModelChange_33_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_40_listener", "SettingTimePeriodComponent_Template_button_click_45_listener", "SettingTimePeriodComponent_div_48_Template", "SettingTimePeriodComponent_div_49_Template", "SettingTimePeriodComponent_div_50_Template", "SettingTimePeriodComponent_div_51_Template", "SettingTimePeriodComponent_ng_template_52_Template", "ɵɵtemplateRefExtractor", "SettingTimePeriodComponent_ng_template_54_Template", "SettingTimePeriodComponent_ng_template_56_Template", "StartDate_r28", "EndDate_r29", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\n\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string; // 戶型\r\n  CBuildingName: string; // 棟別\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n\r\n  @ViewChild('batchSettingDialog') batchSettingDialog!: TemplateRef<any>;\r\n  @ViewChild('dialog') dialog!: TemplateRef<any>;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  // 新增的屬性\r\n  buildingGroups: BuildingGroup[] = [];\r\n  buildingOptions: string[] = [];\r\n  selectedBuilding: string = '';\r\n  availableFloors: number[] = [];\r\n\r\n  // 篩選和搜尋\r\n  filterOptions: FilterOptions = {\r\n    searchKeyword: '',\r\n    statusFilter: '',\r\n    floorFilter: '',\r\n    buildingFilter: ''\r\n  };\r\n\r\n  // 批次設定\r\n  batchSettings: BatchSettings = {\r\n    startDate: null,\r\n    endDate: null,\r\n    applyToAll: true,\r\n    selectedBuildings: [],\r\n    selectedFloors: [],\r\n    selectedHouses: []\r\n  };\r\n\r\n  selectedBuildingForBatch: BuildingGroup | null = null;\r\n\r\n  // 表格視圖相關屬性\r\n  flattenedHouses: ConvertedHouse[] = [];\r\n  filteredHouses: ConvertedHouse[] = [];\r\n  paginatedHouses: ConvertedHouse[] = [];\r\n  selectedHouses: ConvertedHouse[] = [];\r\n  selectAll: boolean = false;\r\n  loading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  override pageSize: number = 50;\r\n  totalPages: number = 1;\r\n\r\n  // 排序相關\r\n  sortField: string = '';\r\n  sortDirection: 'asc' | 'desc' = 'asc';\r\n\r\n  // 數學函數引用\r\n  Math = Math;\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CBuildingName: '未分類',\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：建案變更處理\r\n  onBuildCaseChange() {\r\n    // 重置所有相關狀態\r\n    this.resetAllStates();\r\n    // 執行查詢\r\n    this.getHouseChangeDate();\r\n  }\r\n\r\n  // 新增：重置所有狀態\r\n  resetAllStates() {\r\n    // 重置數據\r\n    this.houseChangeDates = [];\r\n    this.convertedHouseArray = [];\r\n    this.buildingGroups = [];\r\n    this.flattenedHouses = [];\r\n    this.filteredHouses = [];\r\n    this.paginatedHouses = [];\r\n    this.selectedHouses = [];\r\n\r\n    // 重置篩選條件\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    this.selectAll = false;\r\n    this.selectedBuilding = '';\r\n\r\n    // 重置分頁\r\n    this.currentPage = 1;\r\n    this.totalPages = 1;\r\n\r\n    // 重置可用選項\r\n    this.buildingOptions = [];\r\n    this.availableFloors = [];\r\n\r\n    // 重置排序\r\n    this.sortField = '';\r\n    this.sortDirection = 'asc';\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    // 如果沒有選擇建案，直接返回\r\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      this.loading = false;\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n          // 新增：建立棟別分組資料\r\n          this.buildBuildingGroups(res.Entries)\r\n          // 新增：建立扁平化資料\r\n          this.buildFlattenedHouses(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\r\n  buildBuildingGroups(data: GetHouseChangeDateRes[]) {\r\n    const buildingMap = new Map<string, Map<number, ConvertedHouse[]>>();\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || ''; // 戶型\r\n\r\n      household.CHouses?.forEach(house => {\r\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\r\n        const floor = house.CFloor || 0;\r\n\r\n        if (!buildingMap.has(buildingName)) {\r\n          buildingMap.set(buildingName, new Map<number, ConvertedHouse[]>());\r\n        }\r\n\r\n        const floorMap = buildingMap.get(buildingName)!;\r\n\r\n        if (!floorMap.has(floor)) {\r\n          floorMap.set(floor, []);\r\n        }\r\n\r\n        floorMap.get(floor)!.push({\r\n          CHouseHold: houseType, // 戶型\r\n          CBuildingName: buildingName, // 棟別\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: floor,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 轉換為BuildingGroup格式\r\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\r\n      const floors: FloorGroup[] = Array.from(floorMap.entries())\r\n        .sort(([a], [b]) => b - a) // 樓層由高到低排序\r\n        .map(([floorNumber, houses]) => ({\r\n          floorNumber,\r\n          houses: houses.sort((a, b) => {\r\n            // 排序邏輯：先按戶型排序，再按樓層排序\r\n            if (a.CHouseHold !== b.CHouseHold) {\r\n              return a.CHouseHold.localeCompare(b.CHouseHold);\r\n            }\r\n            return a.CFloor - b.CFloor;\r\n          }),\r\n          selected: false\r\n        }));\r\n\r\n      return {\r\n        name: buildingName,\r\n        floors,\r\n        selected: false\r\n      };\r\n    }).sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    // 更新棟別選項和可用樓層\r\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\r\n    this.updateAvailableFloors();\r\n  }\r\n\r\n  // 新增：更新可用樓層\r\n  updateAvailableFloors() {\r\n    const floorsSet = new Set<number>();\r\n\r\n    this.buildingGroups.forEach(building => {\r\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\r\n        building.floors.forEach(floor => {\r\n          floorsSet.add(floor.floorNumber);\r\n        });\r\n      }\r\n    });\r\n\r\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\r\n  }\r\n\r\n  // 修改：棟別選擇變更處理\r\n  onBuildingChange() {\r\n    // 重置選擇狀態\r\n    this.selectedHouses.forEach(house => house.selected = false);\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n\r\n    // 重置分頁到第一頁\r\n    this.currentPage = 1;\r\n\r\n    // 重置樓層篩選\r\n    this.filterOptions.floorFilter = '';\r\n\r\n    // 更新可用樓層\r\n    this.updateAvailableFloors();\r\n\r\n    // 設定棟別篩選並執行搜尋\r\n    this.filterOptions.buildingFilter = this.selectedBuilding;\r\n    this.onSearch();\r\n  }\r\n\r\n\r\n\r\n  // 新增：取得過濾後的棟別資料\r\n  getFilteredBuildings(): BuildingGroup[] {\r\n    return this.buildingGroups.filter(building => {\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 關鍵字搜尋 (搜尋戶型)\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        const hasMatchingHouse = building.floors.some(floor =>\r\n          floor.houses.some(house =>\r\n            house.CHouseHold.toLowerCase().includes(keyword) ||\r\n            house.CBuildingName.toLowerCase().includes(keyword)\r\n          )\r\n        );\r\n        if (!hasMatchingHouse) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        const hasMatchingStatus = building.floors.some(floor =>\r\n          floor.houses.some(house => this.matchesStatusFilter(house))\r\n        );\r\n        if (!hasMatchingStatus) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        const hasMatchingFloor = building.floors.some(floor =>\r\n          floor.floorNumber === floorNumber\r\n        );\r\n        if (!hasMatchingFloor) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }).map(building => {\r\n      // 對每個棟別，也要篩選其樓層和戶別\r\n      const filteredBuilding = { ...building };\r\n      filteredBuilding.floors = building.floors.filter(floor => {\r\n        // 樓層篩選\r\n        if (this.filterOptions.floorFilter) {\r\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n          if (floor.floorNumber !== floorNumber) {\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 檢查該樓層是否有符合條件的戶別\r\n        const hasValidHouses = floor.houses.some(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return hasValidHouses;\r\n      }).map(floor => {\r\n        // 篩選戶別\r\n        const filteredFloor = { ...floor };\r\n        filteredFloor.houses = floor.houses.filter(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return filteredFloor;\r\n      });\r\n\r\n      return filteredBuilding;\r\n    });\r\n  }\r\n\r\n  // 修復：檢查戶別是否符合狀態篩選\r\n  private matchesStatusFilter(house: ConvertedHouse): boolean {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (this.filterOptions.statusFilter) {\r\n      case 'active':\r\n        return status === 'active';\r\n      case 'pending':\r\n        return status === 'pending';\r\n      case 'expired':\r\n        return status === 'expired';\r\n      case 'not-set':\r\n        return status === 'not-set';\r\n      case 'disabled':\r\n        return status === 'disabled';\r\n      default:\r\n        return true; // 全部狀態\r\n    }\r\n  }\r\n\r\n  // 修復：取得戶別狀態\r\n  private getHouseStatus(house: ConvertedHouse): string {\r\n    if (!house.CHouseId) {\r\n      return 'disabled';\r\n    }\r\n\r\n    // 檢查是否有設定開放時段\r\n    if (!house.CChangeStartDate || !house.CChangeEndDate ||\r\n      house.CChangeStartDate === '' || house.CChangeEndDate === '') {\r\n      return 'not-set';\r\n    }\r\n\r\n    try {\r\n      // 處理日期字串，支援多種格式\r\n      const now = new Date();\r\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n\r\n      // 解析開始日期\r\n      let startDate: Date;\r\n      if (house.CChangeStartDate.includes('T')) {\r\n        startDate = new Date(house.CChangeStartDate);\r\n      } else {\r\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\r\n      }\r\n\r\n      // 解析結束日期\r\n      let endDate: Date;\r\n      if (house.CChangeEndDate.includes('T')) {\r\n        endDate = new Date(house.CChangeEndDate);\r\n      } else {\r\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\r\n      }\r\n\r\n      // 檢查日期有效性\r\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\r\n        console.warn('Invalid date format:', {\r\n          start: house.CChangeStartDate,\r\n          end: house.CChangeEndDate,\r\n          houseId: house.CHouseId\r\n        });\r\n        return 'not-set';\r\n      }\r\n\r\n      // 轉換為日期比較（不含時間）\r\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\r\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\r\n\r\n      // 判斷狀態\r\n      if (today < startDateOnly) {\r\n        return 'pending'; // 待開放\r\n      } else if (today >= startDateOnly && today <= endDateOnly) {\r\n        return 'active'; // 進行中\r\n      } else {\r\n        return 'expired'; // 已過期\r\n      }\r\n    } catch (error) {\r\n      console.error('Error parsing dates:', error, house);\r\n      return 'not-set';\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n\r\n  // 修改：開啟批次設定對話框\r\n  openBatchSetting(building?: BuildingGroup) {\r\n    this.selectedBuildingForBatch = building || null;\r\n\r\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\r\n    const hasSelectedHouses = this.selectedHouses.length > 0;\r\n\r\n    this.batchSettings = {\r\n      startDate: null,\r\n      endDate: null,\r\n      applyToAll: !hasSelectedHouses && !building,\r\n      selectedBuildings: building ? [building.name] : [],\r\n      selectedFloors: [],\r\n      selectedHouses: []\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    if (building) {\r\n      building.floors.forEach(floor => {\r\n        floor.selected = false;\r\n        floor.houses.forEach(house => house.selected = false);\r\n      });\r\n    }\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(this.batchSettingDialog);\r\n  }\r\n\r\n  // 新增：樓層選擇變更處理\r\n  onFloorSelectionChange(floor: FloorGroup) {\r\n    if (floor.selected) {\r\n      floor.houses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          house.selected = true;\r\n        }\r\n      });\r\n    } else {\r\n      floor.houses.forEach(house => house.selected = false);\r\n    }\r\n  }\r\n\r\n  // 修改：批次提交\r\n  onBatchSubmit(ref: any) {\r\n    // 驗證批次設定\r\n    this.valid.clear();\r\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\r\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 收集要更新的房屋\r\n    const housesToUpdate: any[] = [];\r\n\r\n    if (this.batchSettings.applyToAll) {\r\n      // 全部戶別\r\n      this.flattenedHouses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          housesToUpdate.push({\r\n            CHouseId: house.CHouseId,\r\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // 使用已選擇的戶別\r\n      if (this.selectedHouses.length > 0) {\r\n        this.selectedHouses.forEach(house => {\r\n          if (house.CHouseId) {\r\n            housesToUpdate.push({\r\n              CHouseId: house.CHouseId,\r\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n            });\r\n          }\r\n        });\r\n      } else if (this.selectedBuildingForBatch) {\r\n        // 如果沒有選擇的戶別，使用舊的邏輯\r\n        this.selectedBuildingForBatch.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.selected && house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n\r\n    if (housesToUpdate.length === 0) {\r\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\r\n      return;\r\n    }\r\n\r\n    // 調用API進行批次更新\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: housesToUpdate\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\r\n        // 清除選擇狀態\r\n        this.selectedHouses.forEach(house => house.selected = false);\r\n        this.selectedHouses = [];\r\n        this.selectAll = false;\r\n        this.getHouseChangeDate();\r\n        ref.close();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：取得狀態樣式類別\r\n  getStatusClass(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    return `status-${status}`;\r\n  }\r\n\r\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\r\n  openHouseDialog(house: ConvertedHouse) {\r\n    if (house.CHouseId) {\r\n      // 使用現有的openModel方法\r\n      this.openModel(this.dialog, house);\r\n    }\r\n  }\r\n\r\n  // 新增：建立扁平化房屋資料\r\n  buildFlattenedHouses(data: GetHouseChangeDateRes[]) {\r\n    this.flattenedHouses = [];\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || '';\r\n\r\n      household.CHouses?.forEach(house => {\r\n        this.flattenedHouses.push({\r\n          CHouseHold: houseType,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: house.CFloor || 0,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 初始化篩選和分頁\r\n    this.onSearch();\r\n\r\n    // 調試：輸出狀態統計\r\n    this.debugStatusCounts();\r\n  }\r\n\r\n  // 調試：輸出狀態統計\r\n  private debugStatusCounts() {\r\n    const statusCounts = {\r\n      active: 0,\r\n      pending: 0,\r\n      expired: 0,\r\n      'not-set': 0,\r\n      disabled: 0\r\n    };\r\n\r\n    this.flattenedHouses.forEach(house => {\r\n      const status = this.getHouseStatus(house);\r\n      if (statusCounts.hasOwnProperty(status)) {\r\n        statusCounts[status as keyof typeof statusCounts]++;\r\n      }\r\n    });\r\n\r\n    console.log('狀態統計:', statusCounts);\r\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\r\n  }\r\n\r\n\r\n\r\n  // 修改：搜尋和篩選\r\n  onSearch() {\r\n    // 記錄篩選前的已選擇項目\r\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\r\n\r\n    this.filteredHouses = this.flattenedHouses.filter(house => {\r\n      // 關鍵字搜尋\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n          !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        if (!this.matchesStatusFilter(house)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        if (house.CFloor !== floorNumber) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    });\r\n\r\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\r\n    this.selectedHouses = this.filteredHouses.filter(house =>\r\n      previouslySelectedIds.includes(house.CHouseId)\r\n    );\r\n\r\n    // 更新扁平化資料中的選擇狀態\r\n    this.flattenedHouses.forEach(house => {\r\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\r\n    });\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n\r\n    // 重新計算分頁\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：更新全選狀態\r\n  private updateSelectAllState() {\r\n    if (this.paginatedHouses.length === 0) {\r\n      this.selectAll = false;\r\n    } else {\r\n      this.selectAll = this.paginatedHouses.every(house =>\r\n        !house.CHouseId || house.selected\r\n      );\r\n    }\r\n  }\r\n\r\n  // 修改：更新分頁\r\n  updatePagination() {\r\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\r\n    const startIndex = (this.currentPage - 1) * this.pageSize;\r\n    const endIndex = startIndex + this.pageSize;\r\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 新增：頁面大小變更\r\n  onPageSizeChange() {\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：跳轉頁面\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  // 新增：取得可見頁碼\r\n  getVisiblePages(): number[] {\r\n    const pages: number[] = [];\r\n    const maxVisible = 5;\r\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\r\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\r\n\r\n    if (end - start + 1 < maxVisible) {\r\n      start = Math.max(1, end - maxVisible + 1);\r\n    }\r\n\r\n    for (let i = start; i <= end; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 新增：全選/取消全選\r\n  onSelectAllChange() {\r\n    this.paginatedHouses.forEach(house => {\r\n      if (house.CHouseId) {\r\n        house.selected = this.selectAll;\r\n      }\r\n    });\r\n    this.updateSelectedHouses();\r\n  }\r\n\r\n  // 新增：單一選擇變更\r\n  onHouseSelectionChange() {\r\n    this.updateSelectedHouses();\r\n    this.selectAll = this.paginatedHouses.every(house =>\r\n      !house.CHouseId || house.selected\r\n    );\r\n  }\r\n\r\n  // 新增：更新已選擇房屋列表\r\n  updateSelectedHouses() {\r\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\r\n  }\r\n\r\n  // 新增：排序\r\n  sort(field: string) {\r\n    if (this.sortField === field) {\r\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortDirection = 'asc';\r\n    }\r\n\r\n    this.filteredHouses.sort((a, b) => {\r\n      let aValue = (a as any)[field];\r\n      let bValue = (b as any)[field];\r\n\r\n      // 處理日期排序\r\n      if (field.includes('Date')) {\r\n        aValue = aValue ? new Date(aValue).getTime() : 0;\r\n        bValue = bValue ? new Date(bValue).getTime() : 0;\r\n      }\r\n\r\n      // 處理數字排序\r\n      if (field === 'CFloor') {\r\n        aValue = Number(aValue) || 0;\r\n        bValue = Number(bValue) || 0;\r\n      }\r\n\r\n      // 處理字串排序\r\n      if (typeof aValue === 'string') {\r\n        aValue = aValue.toLowerCase();\r\n        bValue = bValue.toLowerCase();\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return this.sortDirection === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return this.sortDirection === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：TrackBy函數\r\n  trackByHouseId(_index: number, house: ConvertedHouse): number {\r\n    return house.CHouseId;\r\n  }\r\n\r\n  // 新增：取得狀態文字\r\n  getStatusText(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (status) {\r\n      case 'active':\r\n        return '進行中';\r\n      case 'pending':\r\n        return '待開放';\r\n      case 'expired':\r\n        return '已過期';\r\n      case 'not-set':\r\n        return '未設定';\r\n      case 'disabled':\r\n        return '已停用';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  // 新增：匯出資料\r\n  exportData() {\r\n    // 實現匯出功能\r\n    const csvContent = this.generateCSV();\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  // 新增：產生CSV內容\r\n  private generateCSV(): string {\r\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\r\n    const rows = this.filteredHouses.map(house => [\r\n      house.CHouseHold,\r\n      house.CBuildingName,\r\n      `${house.CFloor}F`,\r\n      house.CChangeStartDate || '未設定',\r\n      house.CChangeEndDate || '未設定',\r\n      this.getStatusText(house)\r\n    ]);\r\n\r\n    const csvContent = [headers, ...rows]\r\n      .map(row => row.map(cell => `\"${cell}\"`).join(','))\r\n      .join('\\n');\r\n\r\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <!-- 整合的Header區域 -->\r\n    <div class=\"integrated-header\">\r\n      <!-- 頁面標題和描述 -->\r\n      <div class=\"page-header mb-4\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div>\r\n            <h1 class=\"page-title\">\r\n              <i class=\"fas fa-calendar-alt me-2\"></i>\r\n              選樣開放時段設定\r\n            </h1>\r\n            <p class=\"page-description mb-0\">\r\n              管理各戶別的選樣開放時段，設定開始與結束日期，控制客戶選樣權限\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要查詢條件 -->\r\n      <div class=\"primary-filters mb-3\">\r\n        <div class=\"row align-items-end\">\r\n          <div class=\"col-lg-3 col-md-4 mb-2\">\r\n            <label class=\"form-label\">建案</label>\r\n            <nb-select placeholder=\"請選擇建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\"\r\n              (selectedChange)=\"onBuildCaseChange()\">\r\n              <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n                {{ case.CBuildCaseName }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3 mb-2\">\r\n            <label class=\"form-label\">棟別</label>\r\n            <nb-select placeholder=\"全部棟別\" [(ngModel)]=\"selectedBuilding\" (selectedChange)=\"onBuildingChange()\">\r\n              <nb-option value=\"\">全部棟別</nb-option>\r\n              <nb-option *ngFor=\"let building of buildingOptions\" [value]=\"building\">\r\n                {{ building }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-4 col-md-5 mb-2\">\r\n            <label class=\"form-label\">開放日期範圍</label>\r\n            <div class=\"d-flex align-items-center\">\r\n              <nb-form-field class=\"flex-fill\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"開始日期\" [nbDatepicker]=\"StartDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n                <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n              <span class=\"mx-2\">~</span>\r\n              <nb-form-field class=\"flex-fill\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"結束日期\" [nbDatepicker]=\"EndDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n                <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12 mb-2\">\r\n            <div class=\"d-flex align-items-center justify-content-end h-100\">\r\n              <button class=\"btn btn-primary btn-sm\" (click)=\"getHouseChangeDate()\" [disabled]=\"loading\">\r\n                <i class=\"fas fa-search me-1\"></i>查詢\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 進階篩選和操作區域 -->\r\n      <div class=\"advanced-filters\" *ngIf=\"flattenedHouses.length > 0\">\r\n        <div class=\"row align-items-center\">\r\n          <div class=\"col-lg-3 col-md-4 mb-2\">\r\n            <nb-form-field>\r\n              <nb-icon nbPrefix icon=\"search-outline\"></nb-icon>\r\n              <input nbInput placeholder=\"搜尋戶型、棟別...\" [(ngModel)]=\"filterOptions.searchKeyword\"\r\n                (ngModelChange)=\"onSearch()\">\r\n            </nb-form-field>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3 mb-2\">\r\n            <nb-select placeholder=\"狀態篩選\" [(ngModel)]=\"filterOptions.statusFilter\" (selectedChange)=\"onSearch()\">\r\n              <nb-option value=\"\">全部狀態</nb-option>\r\n              <nb-option value=\"active\">進行中</nb-option>\r\n              <nb-option value=\"pending\">待開放</nb-option>\r\n              <nb-option value=\"expired\">已過期</nb-option>\r\n              <nb-option value=\"not-set\">未設定</nb-option>\r\n              <nb-option value=\"disabled\">已停用</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3 mb-2\">\r\n            <nb-select placeholder=\"樓層篩選\" [(ngModel)]=\"filterOptions.floorFilter\" (selectedChange)=\"onSearch()\">\r\n              <nb-option value=\"\">全部樓層</nb-option>\r\n              <nb-option *ngFor=\"let floor of availableFloors\" [value]=\"floor\">\r\n                {{ floor }}F\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-2 mb-2\">\r\n            <nb-select placeholder=\"顯示筆數\" [(ngModel)]=\"pageSize\" (selectedChange)=\"onPageSizeChange()\">\r\n              <nb-option [value]=\"50\">50筆/頁</nb-option>\r\n              <nb-option [value]=\"100\">100筆/頁</nb-option>\r\n              <nb-option [value]=\"200\">200筆/頁</nb-option>\r\n              <nb-option [value]=\"500\">500筆/頁</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12 mb-2\">\r\n            <div class=\"d-flex justify-content-between align-items-center\">\r\n              <div class=\"data-summary\">\r\n                <span class=\"text-muted small\">\r\n                  共 {{ filteredHouses.length }} 筆資料\r\n                  <span *ngIf=\"selectedHouses.length > 0\" class=\"text-primary ms-1\">\r\n                    (已選 {{ selectedHouses.length }} 筆)\r\n                  </span>\r\n                </span>\r\n              </div>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 表格視圖 -->\r\n    <div class=\"table-view mt-4\" *ngIf=\"flattenedHouses.length > 0\">\r\n      <!-- 工具列 -->\r\n      <div class=\"table-toolbar mb-3\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"batch-actions\">\r\n            <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\">\r\n              全選\r\n            </nb-checkbox>\r\n            <button class=\"btn btn-sm btn-warning ml-2\" [disabled]=\"selectedHouses.length === 0\"\r\n              (click)=\"openBatchSetting()\" title=\"批次設定選中的戶別開放時段\">\r\n              <i class=\"fas fa-cogs me-1\"></i>批次設定\r\n              <span *ngIf=\"selectedHouses.length > 0\" class=\"badge badge-light ml-1\">\r\n                {{ selectedHouses.length }}\r\n              </span>\r\n            </button>\r\n            <button class=\"btn btn-sm btn-success ml-2\" [disabled]=\"filteredHouses.length === 0\" (click)=\"exportData()\">\r\n              <i class=\"fas fa-download\"></i> 匯出\r\n            </button>\r\n          </div>\r\n          <div class=\"pagination-info\">\r\n            顯示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredHouses.length) }}\r\n            / 共 {{ filteredHouses.length }} 筆\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格 -->\r\n      <div class=\"table-container\">\r\n        <table class=\"table table-hover\">\r\n          <thead class=\"table-header\">\r\n            <tr>\r\n              <th width=\"50\">\r\n                <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\"></nb-checkbox>\r\n              </th>\r\n              <th width=\"100\" (click)=\"sort('CHouseHold')\" class=\"sortable\">\r\n                戶型\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CHouseHold' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CHouseHold' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"100\" (click)=\"sort('CBuildingName')\" class=\"sortable\">\r\n                棟別\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CBuildingName' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CBuildingName' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"80\" (click)=\"sort('CFloor')\" class=\"sortable\">\r\n                樓層\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CFloor' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CFloor' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"120\" (click)=\"sort('CChangeStartDate')\" class=\"sortable\">\r\n                開始日期\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CChangeStartDate' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CChangeStartDate' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"120\" (click)=\"sort('CChangeEndDate')\" class=\"sortable\">\r\n                結束日期\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CChangeEndDate' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CChangeEndDate' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"100\">狀態</th>\r\n              <th width=\"80\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let house of paginatedHouses; trackBy: trackByHouseId\"\r\n              [class.table-row-selected]=\"house.selected\">\r\n              <td>\r\n                <nb-checkbox [(ngModel)]=\"house.selected\" [disabled]=\"!house.CHouseId\"\r\n                  (ngModelChange)=\"onHouseSelectionChange()\"></nb-checkbox>\r\n              </td>\r\n              <td>{{ house.CHouseHold }}</td>\r\n              <td>{{ house.CBuildingName }}</td>\r\n              <td>{{ house.CFloor }}F</td>\r\n              <td>\r\n                <span *ngIf=\"house.CChangeStartDate\" class=\"date-display\">\r\n                  {{ house.CChangeStartDate | date:'yyyy-MM-dd' }}\r\n                </span>\r\n                <span *ngIf=\"!house.CChangeStartDate\" class=\"text-muted\">未設定</span>\r\n              </td>\r\n              <td>\r\n                <span *ngIf=\"house.CChangeEndDate\" class=\"date-display\">\r\n                  {{ house.CChangeEndDate | date:'yyyy-MM-dd' }}\r\n                </span>\r\n                <span *ngIf=\"!house.CChangeEndDate\" class=\"text-muted\">未設定</span>\r\n              </td>\r\n              <td>\r\n                <span class=\"status-badge\" [class]=\"getStatusClass(house)\">\r\n                  {{ getStatusText(house) }}\r\n                </span>\r\n              </td>\r\n              <td>\r\n                <button class=\"btn btn-sm btn-outline-primary\" [disabled]=\"!house.CHouseId\"\r\n                  (click)=\"openModel(dialog, house)\">\r\n                  <i class=\"fas fa-edit\"></i>\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 分頁控制 -->\r\n      <div class=\"pagination-container mt-3\" *ngIf=\"totalPages > 1\">\r\n        <nav>\r\n          <ul class=\"pagination justify-content-center\">\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\">首頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\">上一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" *ngFor=\"let page of getVisiblePages()\" [class.active]=\"page === currentPage\">\r\n              <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\"\r\n                [disabled]=\"currentPage === totalPages\">下一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(totalPages)\"\r\n                [disabled]=\"currentPage === totalPages\">末頁</button>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 當沒有資料時顯示 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"flattenedHouses.length === 0 && houseChangeDates.length === 0\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <i class=\"fas fa-info-circle fa-2x mb-3\"></i>\r\n            <p>請選擇建案後查詢資料</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n\r\n    <!-- 載入中狀態 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"loading\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <div class=\"spinner-border\" role=\"status\">\r\n              <span class=\"sr-only\">載入中...</span>\r\n            </div>\r\n            <p class=\"mt-2\">資料載入中，請稍候...</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 批次設定對話框 -->\r\n<ng-template #batchSettingDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:600px; max-height: 90vh\">\r\n    <nb-card-header>\r\n      批次設定\r\n      <span *ngIf=\"selectedHouses.length > 0\" class=\"text-primary\">\r\n        - 已選擇 {{ selectedHouses.length }} 個戶別\r\n      </span>\r\n      <span *ngIf=\"selectedBuildingForBatch && selectedHouses.length === 0\">\r\n        - {{ selectedBuildingForBatch.name }}\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 時間設定 -->\r\n      <div class=\"form-group\">\r\n        <label>開放時間起訖 <span class=\"text-red-600\">*</span></label>\r\n        <div class=\"d-flex align-items-center\">\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchStartDate\"\r\n              [(ngModel)]=\"batchSettings.startDate\">\r\n            <nb-datepicker #batchStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <span class=\"mx-2\">~</span>\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchEndDate\"\r\n              [(ngModel)]=\"batchSettings.endDate\">\r\n            <nb-datepicker #batchEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 選擇範圍 -->\r\n      <div class=\"form-group\">\r\n        <label>適用範圍</label>\r\n        <div class=\"selection-options\">\r\n          <!-- 如果有已選擇的戶別，顯示已選擇的資訊 -->\r\n          <div *ngIf=\"selectedHouses.length > 0\" class=\"alert alert-info\">\r\n            <h6>將套用到已選擇的 {{ selectedHouses.length }} 個戶別：</h6>\r\n            <div class=\"selected-houses-preview\">\r\n              <span *ngFor=\"let house of selectedHouses.slice(0, 10); let i = index\"\r\n                class=\"badge badge-primary mr-1 mb-1\">\r\n                {{ house.CHouseHold }} ({{ house.CBuildingName }}-{{ house.CFloor }}F)\r\n              </span>\r\n              <span *ngIf=\"selectedHouses.length > 10\" class=\"text-muted\">\r\n                ...等 {{ selectedHouses.length - 10 }} 個\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 如果沒有已選擇的戶別，顯示選擇選項 -->\r\n          <div *ngIf=\"selectedHouses.length === 0\">\r\n            <nb-checkbox [(ngModel)]=\"batchSettings.applyToAll\">\r\n              全部戶別 ({{ flattenedHouses.length }} 個)\r\n            </nb-checkbox>\r\n            <div class=\"mt-2\" *ngIf=\"!batchSettings.applyToAll && selectedBuildingForBatch\">\r\n              <div class=\"floor-selection\" *ngFor=\"let floor of selectedBuildingForBatch.floors\">\r\n                <nb-checkbox [(ngModel)]=\"floor.selected\" (ngModelChange)=\"onFloorSelectionChange(floor)\">\r\n                  {{ floor.floorNumber }}F ({{ floor.houses.length }} 戶)\r\n                </nb-checkbox>\r\n                <div class=\"house-selection ml-4\" *ngIf=\"floor.selected\">\r\n                  <nb-checkbox *ngFor=\"let house of floor.houses\" [(ngModel)]=\"house.selected\"\r\n                    [disabled]=\"!house.CHouseId\">\r\n                    {{ house.CHouseHold }} ({{ house.CBuildingName }})\r\n                  </nb-checkbox>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-end\">\r\n      <button class=\"btn btn-secondary me-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onBatchSubmit(ref)\">\r\n        批次設定\r\n        <span *ngIf=\"selectedHouses.length > 0\">({{ selectedHouses.length }} 個戶別)</span>\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      您正在編輯 <span class=\"text-green-600\">{{selectedHouseChangeDate.CHouseHold}} -\r\n        {{selectedHouseChangeDate.CFloor}}F</span> 的選樣開放時段\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cFloorFrom\" class=\"label col-3\">開放時間起訖<span class=\"text-red-600\">*</span>\r\n        </label>\r\n        <nb-form-field class=\"ml-3\">\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeStartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeStartDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeStartDate\">\r\n          <nb-datepicker #changeStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n        <label for=\"CChangeStartDate\" class=\"label col-1\"> ~\r\n        </label>\r\n        <nb-form-field>\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeEndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeEndDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeEndDate\">\r\n          <nb-datepicker #changeEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AASvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;;;ICatEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IAQAR,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAK,WAAA,CAAkB;IACpET,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,WAAA,MACF;;;;;IA0DAT,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAM,QAAA,CAAe;IAC9DV,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAG,QAAA,OACF;;;;;IAkBIV,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,oBAAAI,MAAA,CAAAC,cAAA,CAAAC,MAAA,cACF;;;;;;IA3CNb,EAHN,CAAAC,cAAA,cAAiE,cAC3B,cACE,oBACnB;IACbD,EAAA,CAAAc,SAAA,kBAAkD;IAClDd,EAAA,CAAAC,cAAA,gBAC+B;IADSD,EAAA,CAAAe,gBAAA,2BAAAC,0EAAAC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAW,aAAA,CAAAC,aAAA,EAAAN,MAAA,MAAAN,MAAA,CAAAW,aAAA,CAAAC,aAAA,GAAAN,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAyC;IAC/EjB,EAAA,CAAAyB,UAAA,2BAAAT,0EAAA;MAAAhB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAiBb,MAAA,CAAAe,QAAA,EAAU;IAAA,EAAC;IAElC1B,EAHI,CAAAG,YAAA,EAC+B,EACjB,EACZ;IAGJH,EADF,CAAAC,cAAA,cAAoC,oBACmE;IAAvED,EAAA,CAAAe,gBAAA,2BAAAY,8EAAAV,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAW,aAAA,CAAAM,YAAA,EAAAX,MAAA,MAAAN,MAAA,CAAAW,aAAA,CAAAM,YAAA,GAAAX,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAwC;IAACjB,EAAA,CAAAyB,UAAA,4BAAAI,+EAAA;MAAA7B,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAkBb,MAAA,CAAAe,QAAA,EAAU;IAAA,EAAC;IAClG1B,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,qBAA0B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA4B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAAoC,qBACkE;IAAtED,EAAA,CAAAe,gBAAA,2BAAAe,+EAAAb,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAW,aAAA,CAAAS,WAAA,EAAAd,MAAA,MAAAN,MAAA,CAAAW,aAAA,CAAAS,WAAA,GAAAd,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAuC;IAACjB,EAAA,CAAAyB,UAAA,4BAAAO,gFAAA;MAAAhC,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAkBb,MAAA,CAAAe,QAAA,EAAU;IAAA,EAAC;IACjG1B,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAiC,UAAA,KAAAC,uDAAA,wBAAiE;IAIrElC,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,eAAoC,qBACyD;IAA7DD,EAAA,CAAAe,gBAAA,2BAAAoB,+EAAAlB,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAyB,QAAA,EAAAnB,MAAA,MAAAN,MAAA,CAAAyB,QAAA,GAAAnB,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAsB;IAACjB,EAAA,CAAAyB,UAAA,4BAAAY,gFAAA;MAAArC,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAkBb,MAAA,CAAA2B,gBAAA,EAAkB;IAAA,EAAC;IACxFtC,EAAA,CAAAC,cAAA,qBAAwB;IAAAD,EAAA,CAAAE,MAAA,uBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAKAH,EAHN,CAAAC,cAAA,eAAqC,eAC4B,eACnC,gBACO;IAC7BD,EAAA,CAAAE,MAAA,IACA;IAAAF,EAAA,CAAAiC,UAAA,KAAAM,kDAAA,mBAAkE;IAS9EvC,EANU,CAAAG,YAAA,EAAO,EACH,EAEF,EACF,EACF,EACF;;;;IAhD0CH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAwC,gBAAA,YAAA7B,MAAA,CAAAW,aAAA,CAAAC,aAAA,CAAyC;IAMrDvB,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAwC,gBAAA,YAAA7B,MAAA,CAAAW,aAAA,CAAAM,YAAA,CAAwC;IAWxC5B,EAAA,CAAAM,SAAA,IAAuC;IAAvCN,EAAA,CAAAwC,gBAAA,YAAA7B,MAAA,CAAAW,aAAA,CAAAS,WAAA,CAAuC;IAEtC/B,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA8B,eAAA,CAAkB;IAOnBzC,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAwC,gBAAA,YAAA7B,MAAA,CAAAyB,QAAA,CAAsB;IACvCpC,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAI,UAAA,aAAY;IACZJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IAQpBJ,EAAA,CAAAM,SAAA,GACA;IADAN,EAAA,CAAAO,kBAAA,aAAAI,MAAA,CAAA+B,cAAA,CAAA7B,MAAA,yBACA;IAAOb,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAA+B;;;;;IAwB1Cb,EAAA,CAAAC,cAAA,eAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,MAAA,CAAAC,cAAA,CAAAC,MAAA,MACF;;;;;IA6DEb,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAA2C,WAAA,OAAAC,QAAA,CAAAC,gBAAA,qBACF;;;;;IACA7C,EAAA,CAAAC,cAAA,eAAyD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGnEH,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAA2C,WAAA,OAAAC,QAAA,CAAAE,cAAA,qBACF;;;;;IACA9C,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAhBjEH,EAHJ,CAAAC,cAAA,SAC8C,SACxC,sBAE2C;IADhCD,EAAA,CAAAe,gBAAA,2BAAAgC,sFAAA9B,MAAA;MAAA,MAAA2B,QAAA,GAAA5C,EAAA,CAAAkB,aAAA,CAAA8B,GAAA,EAAAC,SAAA;MAAAjD,EAAA,CAAAqB,kBAAA,CAAAuB,QAAA,CAAAM,QAAA,EAAAjC,MAAA,MAAA2B,QAAA,CAAAM,QAAA,GAAAjC,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAA4B;IACvCjB,EAAA,CAAAyB,UAAA,2BAAAsB,sFAAA;MAAA/C,EAAA,CAAAkB,aAAA,CAAA8B,GAAA;MAAA,MAAArC,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAiBb,MAAA,CAAAwC,sBAAA,EAAwB;IAAA,EAAC;IAC9CnD,EAD+C,CAAAG,YAAA,EAAc,EACxD;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IAIFD,EAHA,CAAAiC,UAAA,KAAAmB,wDAAA,mBAA0D,KAAAC,wDAAA,mBAGD;IAC3DrD,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAIFD,EAHA,CAAAiC,UAAA,KAAAqB,wDAAA,mBAAwD,KAAAC,wDAAA,mBAGD;IACzDvD,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACyD;IACzDD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IAEHH,EADF,CAAAC,cAAA,UAAI,kBAEmC;IAAnCD,EAAA,CAAAyB,UAAA,mBAAA+B,0EAAA;MAAA,MAAAZ,QAAA,GAAA5C,EAAA,CAAAkB,aAAA,CAAA8B,GAAA,EAAAC,SAAA;MAAA,MAAAtC,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,MAAAqC,UAAA,GAAAzD,EAAA,CAAA0D,WAAA;MAAA,OAAA1D,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAgD,SAAA,CAAAF,UAAA,EAAAb,QAAA,CAAwB;IAAA,EAAC;IAClC5C,EAAA,CAAAc,SAAA,aAA2B;IAGjCd,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;IA/BHH,EAAA,CAAA4D,WAAA,uBAAAhB,QAAA,CAAAM,QAAA,CAA2C;IAE5BlD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAwC,gBAAA,YAAAI,QAAA,CAAAM,QAAA,CAA4B;IAAClD,EAAA,CAAAI,UAAA,cAAAwC,QAAA,CAAAiB,QAAA,CAA4B;IAGpE7D,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAA8D,iBAAA,CAAAlB,QAAA,CAAAmB,UAAA,CAAsB;IACtB/D,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA8D,iBAAA,CAAAlB,QAAA,CAAAoB,aAAA,CAAyB;IACzBhE,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,kBAAA,KAAAqC,QAAA,CAAAqB,MAAA,MAAmB;IAEdjE,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,SAAAwC,QAAA,CAAAC,gBAAA,CAA4B;IAG5B7C,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAI,UAAA,UAAAwC,QAAA,CAAAC,gBAAA,CAA6B;IAG7B7C,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,SAAAwC,QAAA,CAAAE,cAAA,CAA0B;IAG1B9C,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAAwC,QAAA,CAAAE,cAAA,CAA2B;IAGP9C,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAkE,UAAA,CAAAvD,MAAA,CAAAwD,cAAA,CAAAvB,QAAA,EAA+B;IACxD5C,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,MAAA,CAAAyD,aAAA,CAAAxB,QAAA,OACF;IAG+C5C,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,cAAAwC,QAAA,CAAAiB,QAAA,CAA4B;;;;;;IAqB7E7D,EADF,CAAAC,cAAA,aAAmG,iBAC9C;IAAzBD,EAAA,CAAAyB,UAAA,mBAAA4C,+EAAA;MAAA,MAAAC,QAAA,GAAAtE,EAAA,CAAAkB,aAAA,CAAAqD,IAAA,EAAAtB,SAAA;MAAA,MAAAtC,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAA6D,QAAA,CAAAF,QAAA,CAAc;IAAA,EAAC;IAACtE,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFwDH,EAAA,CAAA4D,WAAA,WAAAU,QAAA,KAAA3D,MAAA,CAAA8D,WAAA,CAAqC;IAC7CzE,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAA8D,iBAAA,CAAAQ,QAAA,CAAU;;;;;;IAN7DtE,EAJR,CAAAC,cAAA,cAA8D,UACvD,aAC2C,aACe,iBACsB;IAArDD,EAAA,CAAAyB,UAAA,mBAAAiD,0EAAA;MAAA1E,EAAA,CAAAkB,aAAA,CAAAyD,IAAA;MAAA,MAAAhE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAA6D,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAAgCxE,EAAA,CAAAE,MAAA,mBAAE;IACnFF,EADmF,CAAAG,YAAA,EAAS,EACvF;IAEHH,EADF,CAAAC,cAAA,aAA2D,iBACoC;IAAnED,EAAA,CAAAyB,UAAA,mBAAAmD,0EAAA;MAAA5E,EAAA,CAAAkB,aAAA,CAAAyD,IAAA;MAAA,MAAAhE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAA6D,QAAA,CAAA7D,MAAA,CAAA8D,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAAgCzE,EAAA,CAAAE,MAAA,yBAAG;IAClGF,EADkG,CAAAG,YAAA,EAAS,EACtG;IACLH,EAAA,CAAAiC,UAAA,IAAA4C,sDAAA,iBAAmG;IAIjG7E,EADF,CAAAC,cAAA,cAAoE,kBAExB;IADhBD,EAAA,CAAAyB,UAAA,mBAAAqD,2EAAA;MAAA9E,EAAA,CAAAkB,aAAA,CAAAyD,IAAA;MAAA,MAAAhE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAA6D,QAAA,CAAA7D,MAAA,CAAA8D,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IACnBzE,EAAA,CAAAE,MAAA,0BAAG;IAC/CF,EAD+C,CAAAG,YAAA,EAAS,EACnD;IAEHH,EADF,CAAAC,cAAA,cAAoE,kBAExB;IADhBD,EAAA,CAAAyB,UAAA,mBAAAsD,2EAAA;MAAA/E,EAAA,CAAAkB,aAAA,CAAAyD,IAAA;MAAA,MAAAhE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAA6D,QAAA,CAAA7D,MAAA,CAAAqE,UAAA,CAAoB;IAAA,EAAC;IACdhF,EAAA,CAAAE,MAAA,oBAAE;IAIpDF,EAJoD,CAAAG,YAAA,EAAS,EAClD,EACF,EACD,EACF;;;;IAnBsBH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAA4D,WAAA,aAAAjD,MAAA,CAAA8D,WAAA,OAAoC;IACRzE,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA8D,WAAA,OAA8B;IAE1DzE,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAA4D,WAAA,aAAAjD,MAAA,CAAA8D,WAAA,OAAoC;IACMzE,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA8D,WAAA,OAA8B;IAEvDzE,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAsE,eAAA,GAAoB;IAGrCjF,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAA4D,WAAA,aAAAjD,MAAA,CAAA8D,WAAA,KAAA9D,MAAA,CAAAqE,UAAA,CAA6C;IAE/DhF,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA8D,WAAA,KAAA9D,MAAA,CAAAqE,UAAA,CAAuC;IAErBhF,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAA4D,WAAA,aAAAjD,MAAA,CAAA8D,WAAA,KAAA9D,MAAA,CAAAqE,UAAA,CAA6C;IAE/DhF,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA8D,WAAA,KAAA9D,MAAA,CAAAqE,UAAA,CAAuC;;;;;;IAnH3ChF,EALR,CAAAC,cAAA,cAAgE,cAE9B,cACiC,cAClC,sBACkD;IAA9DD,EAAA,CAAAe,gBAAA,2BAAAmE,gFAAAjE,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAiE,GAAA;MAAA,MAAAxE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAyE,SAAA,EAAAnE,MAAA,MAAAN,MAAA,CAAAyE,SAAA,GAAAnE,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAuB;IAACjB,EAAA,CAAAyB,UAAA,2BAAAyD,gFAAA;MAAAlF,EAAA,CAAAkB,aAAA,CAAAiE,GAAA;MAAA,MAAAxE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAiBb,MAAA,CAAA0E,iBAAA,EAAmB;IAAA,EAAC;IACxErF,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAC,cAAA,iBACqD;IAAnDD,EAAA,CAAAyB,UAAA,mBAAA6D,mEAAA;MAAAtF,EAAA,CAAAkB,aAAA,CAAAiE,GAAA;MAAA,MAAAxE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAA4E,gBAAA,EAAkB;IAAA,EAAC;IAC5BvF,EAAA,CAAAc,SAAA,YAAgC;IAAAd,EAAA,CAAAE,MAAA,gCAChC;IAAAF,EAAA,CAAAiC,UAAA,IAAAuD,iDAAA,mBAAuE;IAGzExF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4G;IAAvBD,EAAA,CAAAyB,UAAA,mBAAAgE,oEAAA;MAAAzF,EAAA,CAAAkB,aAAA,CAAAiE,GAAA;MAAA,MAAAxE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAA+E,UAAA,EAAY;IAAA,EAAC;IACzG1F,EAAA,CAAAc,SAAA,aAA+B;IAACd,EAAA,CAAAE,MAAA,sBAClC;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,MAAA,IAEF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAQIH,EALV,CAAAC,cAAA,eAA6B,iBACM,iBACH,UACtB,cACa,uBAC8D;IAA9DD,EAAA,CAAAe,gBAAA,2BAAA4E,iFAAA1E,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAiE,GAAA;MAAA,MAAAxE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAyE,SAAA,EAAAnE,MAAA,MAAAN,MAAA,CAAAyE,SAAA,GAAAnE,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAuB;IAACjB,EAAA,CAAAyB,UAAA,2BAAAkE,iFAAA;MAAA3F,EAAA,CAAAkB,aAAA,CAAAiE,GAAA;MAAA,MAAAxE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAiBb,MAAA,CAAA0E,iBAAA,EAAmB;IAAA,EAAC;IAC5ErF,EAD6E,CAAAG,YAAA,EAAc,EACtF;IACLH,EAAA,CAAAC,cAAA,cAA8D;IAA9CD,EAAA,CAAAyB,UAAA,mBAAAmE,gEAAA;MAAA5F,EAAA,CAAAkB,aAAA,CAAAiE,GAAA;MAAA,MAAAxE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAkF,IAAA,CAAK,YAAY,CAAC;IAAA,EAAC;IAC1C7F,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAc,SAAA,aACoF;IACtFd,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAiE;IAAjDD,EAAA,CAAAyB,UAAA,mBAAAqE,gEAAA;MAAA9F,EAAA,CAAAkB,aAAA,CAAAiE,GAAA;MAAA,MAAAxE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAkF,IAAA,CAAK,eAAe,CAAC;IAAA,EAAC;IAC7C7F,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAc,SAAA,aACuF;IACzFd,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAyD;IAA1CD,EAAA,CAAAyB,UAAA,mBAAAsE,gEAAA;MAAA/F,EAAA,CAAAkB,aAAA,CAAAiE,GAAA;MAAA,MAAAxE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAkF,IAAA,CAAK,QAAQ,CAAC;IAAA,EAAC;IACrC7F,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAc,SAAA,aACgF;IAClFd,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAoE;IAApDD,EAAA,CAAAyB,UAAA,mBAAAuE,gEAAA;MAAAhG,EAAA,CAAAkB,aAAA,CAAAiE,GAAA;MAAA,MAAAxE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAkF,IAAA,CAAK,kBAAkB,CAAC;IAAA,EAAC;IAChD7F,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAc,SAAA,aAC0F;IAC5Fd,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAkE;IAAlDD,EAAA,CAAAyB,UAAA,mBAAAwE,gEAAA;MAAAjG,EAAA,CAAAkB,aAAA,CAAAiE,GAAA;MAAA,MAAAxE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAkF,IAAA,CAAK,gBAAgB,CAAC;IAAA,EAAC;IAC9C7F,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAc,SAAA,aACwF;IAC1Fd,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,cAAe;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAErBF,EAFqB,CAAAG,YAAA,EAAK,EACnB,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAiC,UAAA,KAAAiE,gDAAA,mBAC8C;IAkCpDlG,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAGNH,EAAA,CAAAiC,UAAA,KAAAkE,iDAAA,oBAA8D;IAuBhEnG,EAAA,CAAAG,YAAA,EAAM;;;;IAxHeH,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAwC,gBAAA,YAAA7B,MAAA,CAAAyE,SAAA,CAAuB;IAGQpF,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAC,cAAA,CAAAC,MAAA,OAAwC;IAG3Eb,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAA+B;IAIIb,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA+B,cAAA,CAAA7B,MAAA,OAAwC;IAKpFb,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAAoG,kBAAA,oBAAAzF,MAAA,CAAA8D,WAAA,QAAA9D,MAAA,CAAAyB,QAAA,aAAAzB,MAAA,CAAA0F,IAAA,CAAAC,GAAA,CAAA3F,MAAA,CAAA8D,WAAA,GAAA9D,MAAA,CAAAyB,QAAA,EAAAzB,MAAA,CAAA+B,cAAA,CAAA7B,MAAA,iBAAAF,MAAA,CAAA+B,cAAA,CAAA7B,MAAA,aAEF;IAUmBb,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAwC,gBAAA,YAAA7B,MAAA,CAAAyE,SAAA,CAAuB;IAIbpF,EAAA,CAAAM,SAAA,GAA0E;IAC/FN,EADqB,CAAA4D,WAAA,eAAAjD,MAAA,CAAA4F,SAAA,qBAAA5F,MAAA,CAAA6F,aAAA,WAA0E,iBAAA7F,MAAA,CAAA4F,SAAA,qBAAA5F,MAAA,CAAA6F,aAAA,YAClB;IAIxDxG,EAAA,CAAAM,SAAA,GAA6E;IAClGN,EADqB,CAAA4D,WAAA,eAAAjD,MAAA,CAAA4F,SAAA,wBAAA5F,MAAA,CAAA6F,aAAA,WAA6E,iBAAA7F,MAAA,CAAA4F,SAAA,wBAAA5F,MAAA,CAAA6F,aAAA,YAClB;IAI3DxG,EAAA,CAAAM,SAAA,GAAsE;IAC3FN,EADqB,CAAA4D,WAAA,eAAAjD,MAAA,CAAA4F,SAAA,iBAAA5F,MAAA,CAAA6F,aAAA,WAAsE,iBAAA7F,MAAA,CAAA4F,SAAA,iBAAA5F,MAAA,CAAA6F,aAAA,YAClB;IAIpDxG,EAAA,CAAAM,SAAA,GAAgF;IACrGN,EADqB,CAAA4D,WAAA,eAAAjD,MAAA,CAAA4F,SAAA,2BAAA5F,MAAA,CAAA6F,aAAA,WAAgF,iBAAA7F,MAAA,CAAA4F,SAAA,2BAAA5F,MAAA,CAAA6F,aAAA,YAClB;IAI9DxG,EAAA,CAAAM,SAAA,GAA8E;IACnGN,EADqB,CAAA4D,WAAA,eAAAjD,MAAA,CAAA4F,SAAA,yBAAA5F,MAAA,CAAA6F,aAAA,WAA8E,iBAAA7F,MAAA,CAAA4F,SAAA,yBAAA5F,MAAA,CAAA6F,aAAA,YAClB;IAOjExG,EAAA,CAAAM,SAAA,GAAoB;IAAAN,EAApB,CAAAI,UAAA,YAAAO,MAAA,CAAA8F,eAAA,CAAoB,iBAAA9F,MAAA,CAAA+F,cAAA,CAAuB;IAsC/B1G,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAqE,UAAA,KAAoB;;;;;IA+BxDhF,EAHN,CAAAC,cAAA,cAAoG,cACzF,mBACO,cACY;IACtBD,EAAA,CAAAc,SAAA,YAA6C;IAC7Cd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IAIrBF,EAJqB,CAAAG,YAAA,EAAI,EACb,EACO,EACP,EACN;;;;;IAQIH,EALV,CAAAC,cAAA,cAA8C,cACnC,mBACO,cACY,cACoB,eAClB;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,gEAAY;IAIpCF,EAJoC,CAAAG,YAAA,EAAI,EAC5B,EACO,EACP,EACN;;;;;IASJH,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,2BAAAI,MAAA,CAAAC,cAAA,CAAAC,MAAA,yBACF;;;;;IACAb,EAAA,CAAAC,cAAA,WAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,QAAAI,MAAA,CAAAgG,wBAAA,CAAAC,IAAA,MACF;;;;;IA+BQ5G,EAAA,CAAAC,cAAA,gBACwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAoG,kBAAA,MAAAS,SAAA,CAAA9C,UAAA,QAAA8C,SAAA,CAAA7C,aAAA,OAAA6C,SAAA,CAAA5C,MAAA,QACF;;;;;IACAjE,EAAA,CAAAC,cAAA,eAA4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gBAAAI,MAAA,CAAAC,cAAA,CAAAC,MAAA,kBACF;;;;;IARFb,EADF,CAAAC,cAAA,eAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,eAAqC;IAKnCD,EAJA,CAAAiC,UAAA,IAAA6E,gEAAA,oBACwC,IAAAC,gEAAA,mBAGoB;IAIhE/G,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAVAH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAO,kBAAA,sDAAAI,MAAA,CAAAC,cAAA,CAAAC,MAAA,8BAAyC;IAEnBb,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAC,cAAA,CAAAoG,KAAA,QAAgC;IAIjDhH,EAAA,CAAAM,SAAA,EAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,cAAA,CAAAC,MAAA,MAAgC;;;;;;IAiBnCb,EAAA,CAAAC,cAAA,sBAC+B;IADiBD,EAAA,CAAAe,gBAAA,2BAAAkG,+HAAAhG,MAAA;MAAA,MAAAiG,SAAA,GAAAlH,EAAA,CAAAkB,aAAA,CAAAiG,IAAA,EAAAlE,SAAA;MAAAjD,EAAA,CAAAqB,kBAAA,CAAA6F,SAAA,CAAAhE,QAAA,EAAAjC,MAAA,MAAAiG,SAAA,CAAAhE,QAAA,GAAAjC,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAA4B;IAE1EjB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;;;;IAHkCH,EAAA,CAAAwC,gBAAA,YAAA0E,SAAA,CAAAhE,QAAA,CAA4B;IAC1ElD,EAAA,CAAAI,UAAA,cAAA8G,SAAA,CAAArD,QAAA,CAA4B;IAC5B7D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAoH,kBAAA,MAAAF,SAAA,CAAAnD,UAAA,QAAAmD,SAAA,CAAAlD,aAAA,OACF;;;;;IAJFhE,EAAA,CAAAC,cAAA,eAAyD;IACvDD,EAAA,CAAAiC,UAAA,IAAAoF,yFAAA,2BAC+B;IAGjCrH,EAAA,CAAAG,YAAA,EAAM;;;;IAJ2BH,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAI,UAAA,YAAAkH,SAAA,CAAAC,MAAA,CAAe;;;;;;IAJhDvH,EADF,CAAAC,cAAA,eAAmF,sBACS;IAA7ED,EAAA,CAAAe,gBAAA,2BAAAyG,2GAAAvG,MAAA;MAAA,MAAAqG,SAAA,GAAAtH,EAAA,CAAAkB,aAAA,CAAAuG,IAAA,EAAAxE,SAAA;MAAAjD,EAAA,CAAAqB,kBAAA,CAAAiG,SAAA,CAAApE,QAAA,EAAAjC,MAAA,MAAAqG,SAAA,CAAApE,QAAA,GAAAjC,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAA4B;IAACjB,EAAA,CAAAyB,UAAA,2BAAA+F,2GAAA;MAAA,MAAAF,SAAA,GAAAtH,EAAA,CAAAkB,aAAA,CAAAuG,IAAA,EAAAxE,SAAA;MAAA,MAAAtC,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAiBb,MAAA,CAAA+G,sBAAA,CAAAJ,SAAA,CAA6B;IAAA,EAAC;IACvFtH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAiC,UAAA,IAAA0F,2EAAA,mBAAyD;IAM3D3H,EAAA,CAAAG,YAAA,EAAM;;;;IATSH,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAAwC,gBAAA,YAAA8E,SAAA,CAAApE,QAAA,CAA4B;IACvClD,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAoH,kBAAA,MAAAE,SAAA,CAAAM,WAAA,SAAAN,SAAA,CAAAC,MAAA,CAAA1G,MAAA,cACF;IACmCb,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAkH,SAAA,CAAApE,QAAA,CAAoB;;;;;IAL3DlD,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAiC,UAAA,IAAA4F,qEAAA,mBAAmF;IAWrF7H,EAAA,CAAAG,YAAA,EAAM;;;;IAX2CH,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAgG,wBAAA,CAAAmB,MAAA,CAAkC;;;;;;IAJnF9H,EADF,CAAAC,cAAA,UAAyC,sBACa;IAAvCD,EAAA,CAAAe,gBAAA,2BAAAgH,+FAAA9G,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA8G,IAAA;MAAA,MAAArH,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAsH,aAAA,CAAAC,UAAA,EAAAjH,MAAA,MAAAN,MAAA,CAAAsH,aAAA,CAAAC,UAAA,GAAAjH,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAsC;IACjDjB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAiC,UAAA,IAAAkG,+DAAA,mBAAgF;IAalFnI,EAAA,CAAAG,YAAA,EAAM;;;;IAhBSH,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAwC,gBAAA,YAAA7B,MAAA,CAAAsH,aAAA,CAAAC,UAAA,CAAsC;IACjDlI,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gCAAAI,MAAA,CAAAyH,eAAA,CAAAvH,MAAA,cACF;IACmBb,EAAA,CAAAM,SAAA,EAA2D;IAA3DN,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAsH,aAAA,CAAAC,UAAA,IAAAvH,MAAA,CAAAgG,wBAAA,CAA2D;;;;;IAqBlF3G,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAxCH,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAO,kBAAA,MAAAI,MAAA,CAAAC,cAAA,CAAAC,MAAA,yBAAiC;;;;;;IA1E7Eb,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,iCACA;IAGAF,EAHA,CAAAiC,UAAA,IAAAoG,yDAAA,oBAA6D,IAAAC,yDAAA,oBAGS;IAGxEtI,EAAA,CAAAG,YAAA,EAAiB;IAIbH,EAHJ,CAAAC,cAAA,mBAAc,eAEY,YACf;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAEvDH,EADF,CAAAC,cAAA,eAAuC,yBACJ;IAC/BD,EAAA,CAAAc,SAAA,mBAAoD;IACpDd,EAAA,CAAAC,cAAA,kBACwC;IAAtCD,EAAA,CAAAe,gBAAA,2BAAAwH,mFAAAtH,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAsH,IAAA;MAAA,MAAA7H,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAsH,aAAA,CAAAQ,SAAA,EAAAxH,MAAA,MAAAN,MAAA,CAAAsH,aAAA,CAAAQ,SAAA,GAAAxH,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAqC;IADvCjB,EAAA,CAAAG,YAAA,EACwC;IACxCH,EAAA,CAAAc,SAAA,4BAAmE;IACrEd,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,yBAAiC;IAC/BD,EAAA,CAAAc,SAAA,mBAAoD;IACpDd,EAAA,CAAAC,cAAA,kBACsC;IAApCD,EAAA,CAAAe,gBAAA,2BAAA2H,mFAAAzH,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAsH,IAAA;MAAA,MAAA7H,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAsH,aAAA,CAAAU,OAAA,EAAA1H,MAAA,MAAAN,MAAA,CAAAsH,aAAA,CAAAU,OAAA,GAAA1H,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAmC;IADrCjB,EAAA,CAAAG,YAAA,EACsC;IACtCH,EAAA,CAAAc,SAAA,4BAAiE;IAGvEd,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAwB,aACf;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnBH,EAAA,CAAAC,cAAA,gBAA+B;IAgB7BD,EAdA,CAAAiC,UAAA,KAAA2G,yDAAA,mBAAgE,KAAAC,yDAAA,mBAcvB;IAoB/C7I,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAmD,mBACa;IAAvBD,EAAA,CAAAyB,UAAA,mBAAAqH,4EAAA;MAAA,MAAAC,OAAA,GAAA/I,EAAA,CAAAkB,aAAA,CAAAsH,IAAA,EAAAQ,SAAA;MAAA,MAAArI,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAsI,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAAC/I,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzEH,EAAA,CAAAC,cAAA,mBAA6D;IAA7BD,EAAA,CAAAyB,UAAA,mBAAAyH,4EAAA;MAAA,MAAAH,OAAA,GAAA/I,EAAA,CAAAkB,aAAA,CAAAsH,IAAA,EAAAQ,SAAA;MAAA,MAAArI,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAwI,aAAA,CAAAJ,OAAA,CAAkB;IAAA,EAAC;IAC1D/I,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAiC,UAAA,KAAAmH,0DAAA,oBAAwC;IAG9CpJ,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;;;IA3ECH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAA+B;IAG/Bb,EAAA,CAAAM,SAAA,EAA6D;IAA7DN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAgG,wBAAA,IAAAhG,MAAA,CAAAC,cAAA,CAAAC,MAAA,OAA6D;IAWfb,EAAA,CAAAM,SAAA,IAA+B;IAA/BN,EAAA,CAAAI,UAAA,iBAAAiJ,kBAAA,CAA+B;IAC5ErJ,EAAA,CAAAwC,gBAAA,YAAA7B,MAAA,CAAAsH,aAAA,CAAAQ,SAAA,CAAqC;IAMQzI,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,iBAAAkJ,gBAAA,CAA6B;IAC1EtJ,EAAA,CAAAwC,gBAAA,YAAA7B,MAAA,CAAAsH,aAAA,CAAAU,OAAA,CAAmC;IAWjC3I,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAA+B;IAc/Bb,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,cAAA,CAAAC,MAAA,OAAiC;IAyBlCb,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAA+B;;;;;IAO5Cb,EAAA,CAAAC,cAAA,mBAA+C;IAM7CD,EALA,CAAAc,SAAA,qBACiB,mBAGF,0BAGE;IACnBd,EAAA,CAAAG,YAAA,EAAU;;;;;;IAKRH,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GACE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mDAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,wBAA2B,eACyB,iBACJ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC7E;IACRH,EAAA,CAAAC,cAAA,0BAA4B;IAC1BD,EAAA,CAAAc,SAAA,mBAAoD;IACpDd,EAAA,CAAAC,cAAA,kBAC8E;IAAvDD,EAAA,CAAAe,gBAAA,2BAAAwI,mFAAAtI,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAsI,IAAA;MAAA,MAAA7I,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAA8I,uBAAA,CAAA5G,gBAAA,EAAA5B,MAAA,MAAAN,MAAA,CAAA8I,uBAAA,CAAA5G,gBAAA,GAAA5B,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAsD;IAD7EjB,EAAA,CAAAG,YAAA,EAC8E;IAC9EH,EAAA,CAAAc,SAAA,4BAAoE;IACtEd,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,kBAAkD;IAACD,EAAA,CAAAE,MAAA,WACnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAc,SAAA,mBAAoD;IACpDd,EAAA,CAAAC,cAAA,kBAC4E;IAArDD,EAAA,CAAAe,gBAAA,2BAAA2I,mFAAAzI,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAsI,IAAA;MAAA,MAAA7I,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAA8I,uBAAA,CAAA3G,cAAA,EAAA7B,MAAA,MAAAN,MAAA,CAAA8I,uBAAA,CAAA3G,cAAA,GAAA7B,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAoD;IAD3EjB,EAAA,CAAAG,YAAA,EAC4E;IAC5EH,EAAA,CAAAc,SAAA,4BAAkE;IAGxEd,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAsD,mBACiB;IAAvBD,EAAA,CAAAyB,UAAA,mBAAAkI,4EAAA;MAAA,MAAAC,OAAA,GAAA5J,EAAA,CAAAkB,aAAA,CAAAsI,IAAA,EAAAR,SAAA;MAAA,MAAArI,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAsI,OAAA,CAAAW,OAAA,CAAY;IAAA,EAAC;IAAC5J,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,mBAAoE;IAAxBD,EAAA,CAAAyB,UAAA,mBAAAoI,4EAAA;MAAA,MAAAD,OAAA,GAAA5J,EAAA,CAAAkB,aAAA,CAAAsI,IAAA,EAAAR,SAAA;MAAA,MAAArI,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAmJ,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IAAC5J,EAAA,CAAAE,MAAA,oBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAChE,EACT;;;;;;IA3B6BH,EAAA,CAAAM,SAAA,GACE;IADFN,EAAA,CAAAoH,kBAAA,KAAAzG,MAAA,CAAA8I,uBAAA,CAAA1F,UAAA,SAAApD,MAAA,CAAA8I,uBAAA,CAAAxF,MAAA,MACE;IAQoCjE,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,iBAAA2J,mBAAA,CAAgC;IAC9E/J,EAAA,CAAAwC,gBAAA,YAAA7B,MAAA,CAAA8I,uBAAA,CAAA5G,gBAAA,CAAsD;IAOV7C,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAI,UAAA,iBAAA4J,iBAAA,CAA8B;IAC1EhK,EAAA,CAAAwC,gBAAA,YAAA7B,MAAA,CAAA8I,uBAAA,CAAA3G,cAAA,CAAoD;;;ADzUrF,OAAM,MAAOmH,0BAA2B,SAAQnK,aAAa;EAK3DoK,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA6C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAUtD;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAxI,eAAe,GAAa,EAAE;IAE9B;IACA,KAAAnB,aAAa,GAAkB;MAC7BC,aAAa,EAAE,EAAE;MACjBK,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfmJ,cAAc,EAAE;KACjB;IAED;IACA,KAAAjD,aAAa,GAAkB;MAC7BQ,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbT,UAAU,EAAE,IAAI;MAChBiD,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBxK,cAAc,EAAE;KACjB;IAED,KAAA+F,wBAAwB,GAAyB,IAAI;IAErD;IACA,KAAAyB,eAAe,GAAqB,EAAE;IACtC,KAAA1F,cAAc,GAAqB,EAAE;IACrC,KAAA+D,eAAe,GAAqB,EAAE;IACtC,KAAA7F,cAAc,GAAqB,EAAE;IACrC,KAAAwE,SAAS,GAAY,KAAK;IAC1B,KAAAiG,OAAO,GAAY,KAAK;IAExB;IACA,KAAA5G,WAAW,GAAW,CAAC;IACd,KAAArC,QAAQ,GAAW,EAAE;IAC9B,KAAA4C,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAuB,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAmB,KAAK;IAErC;IACA,KAAAH,IAAI,GAAGA,IAAI;IAlFT,IAAI,CAACoD,uBAAuB,GAAG;MAC7B5G,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBmB,MAAM,EAAEqH,SAAS;MACjBvH,UAAU,EAAE,EAAE;MACdF,QAAQ,EAAEyH;KACX;IAED,IAAI,CAACZ,aAAa,CAACa,OAAO,EAAE,CAACC,IAAI,CAC/B/L,GAAG,CAAEgM,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAACf,eAAe,GAAGc,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAqESC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAACpB,gBAAgB,CAAC,CAAC,CAAC;MAC/C/H,gBAAgB,EAAEyI,SAAS;MAC3BxI,cAAc,EAAEwI;KACjB;IACD,IAAI,CAACW,gBAAgB,EAAE;EACzB;EAEAtI,SAASA,CAACuI,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACtI,QAAQ,EAAE;MACjB,IAAI,CAAC4F,uBAAuB,GAAG;QAC7B,GAAG0C,IAAI;QACPtJ,gBAAgB,EAAEsJ,IAAI,CAACtJ,gBAAgB,GAAG,IAAIuJ,IAAI,CAACD,IAAI,CAACtJ,gBAAgB,CAAC,GAAGyI,SAAS;QACrFxI,cAAc,EAAEqJ,IAAI,CAACrJ,cAAc,GAAG,IAAIsJ,IAAI,CAACD,IAAI,CAACrJ,cAAc,CAAC,GAAGwI;OACvE;MACD,IAAI,CAAClB,aAAa,CAACiC,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO3M,MAAM,CAAC2M,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEA1C,QAAQA,CAACoC,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAACnC,KAAK,CAACoC,aAAa,CAAC7L,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACwJ,OAAO,CAACsC,aAAa,CAAC,IAAI,CAACrC,KAAK,CAACoC,aAAa,CAAC;MACpD;IACF;IACA,MAAME,KAAK,GAAG;MACZ/I,QAAQ,EAAE,IAAI,CAAC4F,uBAAuB,CAAC5F,QAAQ;MAC/ChB,gBAAgB,EAAE,IAAI,CAACyJ,UAAU,CAAC,IAAI,CAAC7C,uBAAuB,CAAC5G,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAACwJ,UAAU,CAAC,IAAI,CAAC7C,uBAAuB,CAAC3G,cAAc;KAC5E;IAED,IAAI,CAACyH,aAAa,CAACsC,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAAChB,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC1C,OAAO,CAAC2C,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAjB,gBAAgBA,CAAA;IACd,IAAI,CAACzB,iBAAiB,CAAC2C,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAACtB,IAAI,CAC7E/L,GAAG,CAACgM,GAAG,IAAG;MACR,MAAM2B,OAAO,GAAG3B,GAAG,CAAC4B,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAACvM,MAAM,IAAI4K,GAAG,CAACsB,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChDhN,cAAc,EAAEgN,KAAK,CAAChN,cAAc;UACpCiN,GAAG,EAAED,KAAK,CAACC;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAAC9C,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAI+C,KAAK,GAAG,IAAI,CAACJ,oBAAoB,CAACK,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAAC9C,eAAe,CAAC;UAC1F,IAAI,CAACmB,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuB,oBAAoB,CAACI,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAAC5B,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuB,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMO,WAAW,GAAG,IAAI,CAAC/B,WAAW,EAAEC,kBAAkB,EAAE0B,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACZ,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAACrB,SAAS,EAAE;EACf;EAEAkC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvC,MAAMC,KAAK,GAAGD,KAAK,CAACnK,MAAM;QAC1B,IAAI,CAAC+J,SAAS,CAACK,KAAK,CAAC,EAAE;UAAE;UACvBL,SAAS,CAACK,KAAK,CAAC,GAAG,EAAE;QACvB;QACAL,SAAS,CAACK,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBvK,UAAU,EAAEmK,SAAS,CAACnK,UAAU;UAChCC,aAAa,EAAEoK,KAAK,CAACpK,aAAa,IAAI,KAAK;UAC3CH,QAAQ,EAAEuK,KAAK,CAACvK,QAAQ;UACxBI,MAAM,EAAEmK,KAAK,CAACnK,MAAM;UACpBpB,gBAAgB,EAAEuL,KAAK,CAACvL,gBAAgB;UACxCC,cAAc,EAAEsL,KAAK,CAACtL;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACgF,MAAM,CAACjC,IAAI,CAAC,CAAC0I,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAAC3G,MAAM,CAACyF,GAAG,CAAEc,KAAU,IAAI;MAChE,OAAO,IAAI,CAACK,UAAU,CAACnB,GAAG,CAAEW,SAAc,IAAI;QAC5C,MAAME,KAAK,GAAGJ,SAAS,CAACK,KAAK,CAAC,CAACM,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAAC7K,UAAU,KAAKmK,SAAS,CAAC;QAC5F,OAAOE,KAAK,IAAI;UACdrK,UAAU,EAAEmK,SAAS;UACrBlK,aAAa,EAAE,KAAK;UACpBH,QAAQ,EAAE,IAAI;UACdI,MAAM,EAAEoK,KAAK;UACbxL,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO2L,MAAM;EACf;EAEAI,sBAAsBA,CAACd,GAAU;IAC/B,MAAMe,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5ChB,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBc,aAAa,CAACC,GAAG,CAACf,SAAS,CAACnK,UAAU,CAAC;MACvCmK,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvCU,SAAS,CAACG,GAAG,CAACb,KAAK,CAACnK,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAAC6D,MAAM,GAAGoH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLlH,MAAM,EAAEoH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACtD,WAAW,CAACjJ,gBAAgB,IAAI,IAAI,CAACiJ,WAAW,CAAChJ,cAAc,EAAE;MACxE,MAAM2F,SAAS,GAAG,IAAI2D,IAAI,CAAC,IAAI,CAACN,WAAW,CAACjJ,gBAAgB,CAAC;MAC7D,MAAM8F,OAAO,GAAG,IAAIyD,IAAI,CAAC,IAAI,CAACN,WAAW,CAAChJ,cAAc,CAAC;MACzD,IAAI2F,SAAS,IAAIE,OAAO,IAAIF,SAAS,GAAGE,OAAO,EAAE;QAC/C,IAAI,CAAC0B,OAAO,CAACsC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEA;EACA0C,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACC,cAAc,EAAE;IACrB;IACA,IAAI,CAACrC,kBAAkB,EAAE;EAC3B;EAEA;EACAqC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACzE,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC3C,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC1F,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC+D,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC7F,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAACU,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBK,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfmJ,cAAc,EAAE;KACjB;IAED;IACA,IAAI,CAAC9F,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC6F,gBAAgB,GAAG,EAAE;IAE1B;IACA,IAAI,CAACxG,WAAW,GAAG,CAAC;IACpB,IAAI,CAACO,UAAU,GAAG,CAAC;IAEnB;IACA,IAAI,CAACgG,eAAe,GAAG,EAAE;IACzB,IAAI,CAACvI,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,CAAC8D,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC5B;EAEAyG,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC,IAAI,CAACnB,WAAW,CAACC,kBAAkB,EAAE0B,GAAG,EAAE;MAC7C,IAAI,CAACpC,OAAO,GAAG,KAAK;MACpB;IACF;IAEA,IAAI,CAACA,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC+D,cAAc,EAAE;IACrB,IAAI,CAAC7E,aAAa,CAACkF,mCAAmC,CAAC;MACrD3C,IAAI,EAAE;QACJ4C,YAAY,EAAE,IAAI,CAAC5D,WAAW,CAACC,kBAAkB,CAAC0B,GAAG;QACrD5K,gBAAgB,EAAE,IAAI,CAACiJ,WAAW,CAACjJ,gBAAgB,GAAG,IAAI,CAACyJ,UAAU,CAAC,IAAI,CAACR,WAAW,CAACjJ,gBAAgB,CAAC,GAAGyI,SAAS;QACpHxI,cAAc,EAAE,IAAI,CAACgJ,WAAW,CAAChJ,cAAc,GAAG,IAAI,CAACwJ,UAAU,CAAC,IAAI,CAACR,WAAW,CAAChJ,cAAc,CAAC,GAAGwI;;KAExG,CAAC,CAACM,SAAS,CAACH,GAAG,IAAG;MACjB,IAAI,CAACJ,OAAO,GAAG,KAAK;MACpB,IAAII,GAAG,CAAC4B,OAAO,IAAI5B,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACwC,gBAAgB,GAAG9D,GAAG,CAAC4B,OAAO,GAAG5B,GAAG,CAAC4B,OAAO,GAAG,EAAE;QACtD,IAAI5B,GAAG,CAAC4B,OAAO,EAAE;UACf,IAAI,CAACkC,gBAAgB,GAAG,CAAC,GAAG9D,GAAG,CAAC4B,OAAO,CAAC;UACxC,IAAI,CAACwB,sBAAsB,CAACpD,GAAG,CAAC4B,OAAO,CAAC;UACxC,IAAI,CAACmC,mBAAmB,GAAG,IAAI,CAAC1B,8BAA8B,CAACrC,GAAG,CAAC4B,OAAO,CAAC;UAC3E;UACA,IAAI,CAACsC,mBAAmB,CAAClE,GAAG,CAAC4B,OAAO,CAAC;UACrC;UACA,IAAI,CAACuC,oBAAoB,CAACnE,GAAG,CAAC4B,OAAO,CAAC;QACxC;MACF;IACF,CAAC,CAAC;EACJ;EAEA;EACAsC,mBAAmBA,CAACE,IAA6B;IAC/C,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAyC;IAEpEF,IAAI,CAAC5B,OAAO,CAACC,SAAS,IAAG;MACvB,MAAM8B,SAAS,GAAG9B,SAAS,CAACnK,UAAU,IAAI,EAAE,CAAC,CAAC;MAE9CmK,SAAS,CAACC,OAAO,EAAEF,OAAO,CAACG,KAAK,IAAG;QACjC,MAAM6B,YAAY,GAAG7B,KAAK,CAACpK,aAAa,IAAI,KAAK,CAAC,CAAC;QACnD,MAAMqK,KAAK,GAAGD,KAAK,CAACnK,MAAM,IAAI,CAAC;QAE/B,IAAI,CAAC6L,WAAW,CAACI,GAAG,CAACD,YAAY,CAAC,EAAE;UAClCH,WAAW,CAACK,GAAG,CAACF,YAAY,EAAE,IAAIF,GAAG,EAA4B,CAAC;QACpE;QAEA,MAAMK,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACJ,YAAY,CAAE;QAE/C,IAAI,CAACG,QAAQ,CAACF,GAAG,CAAC7B,KAAK,CAAC,EAAE;UACxB+B,QAAQ,CAACD,GAAG,CAAC9B,KAAK,EAAE,EAAE,CAAC;QACzB;QAEA+B,QAAQ,CAACC,GAAG,CAAChC,KAAK,CAAE,CAACC,IAAI,CAAC;UACxBvK,UAAU,EAAEiM,SAAS;UAAE;UACvBhM,aAAa,EAAEiM,YAAY;UAAE;UAC7BpM,QAAQ,EAAEuK,KAAK,CAACvK,QAAQ,IAAI,CAAC;UAC7BI,MAAM,EAAEoK,KAAK;UACbxL,gBAAgB,EAAEuL,KAAK,CAACvL,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEsL,KAAK,CAACtL,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC6H,cAAc,GAAGmE,KAAK,CAACC,IAAI,CAACW,WAAW,CAAC1C,OAAO,EAAE,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC0C,YAAY,EAAEG,QAAQ,CAAC,KAAI;MACvF,MAAMtI,MAAM,GAAiBoH,KAAK,CAACC,IAAI,CAACiB,QAAQ,CAAChD,OAAO,EAAE,CAAC,CACxDvH,IAAI,CAAC,CAAC,CAAC0I,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;MAAA,CAC1BhB,GAAG,CAAC,CAAC,CAAC3F,WAAW,EAAEL,MAAM,CAAC,MAAM;QAC/BK,WAAW;QACXL,MAAM,EAAEA,MAAM,CAAC1B,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAI;UAC3B;UACA,IAAID,CAAC,CAACxK,UAAU,KAAKyK,CAAC,CAACzK,UAAU,EAAE;YACjC,OAAOwK,CAAC,CAACxK,UAAU,CAACuM,aAAa,CAAC9B,CAAC,CAACzK,UAAU,CAAC;UACjD;UACA,OAAOwK,CAAC,CAACtK,MAAM,GAAGuK,CAAC,CAACvK,MAAM;QAC5B,CAAC,CAAC;QACFf,QAAQ,EAAE;OACX,CAAC,CAAC;MAEL,OAAO;QACL0D,IAAI,EAAEqJ,YAAY;QAClBnI,MAAM;QACN5E,QAAQ,EAAE;OACX;IACH,CAAC,CAAC,CAAC2C,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3H,IAAI,CAAC0J,aAAa,CAAC9B,CAAC,CAAC5H,IAAI,CAAC,CAAC;IAE/C;IACA,IAAI,CAACoE,eAAe,GAAG,IAAI,CAACD,cAAc,CAACwC,GAAG,CAACgD,EAAE,IAAIA,EAAE,CAAC3J,IAAI,CAAC;IAC7D,IAAI,CAAC4J,qBAAqB,EAAE;EAC9B;EAEA;EACAA,qBAAqBA,CAAA;IACnB,MAAM1B,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,IAAI,CAAChE,cAAc,CAACkD,OAAO,CAACwC,QAAQ,IAAG;MACrC,IAAI,CAAC,IAAI,CAACxF,gBAAgB,IAAIwF,QAAQ,CAAC7J,IAAI,KAAK,IAAI,CAACqE,gBAAgB,EAAE;QACrEwF,QAAQ,CAAC3I,MAAM,CAACmG,OAAO,CAACI,KAAK,IAAG;UAC9BS,SAAS,CAACG,GAAG,CAACZ,KAAK,CAACzG,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACnF,eAAe,GAAGyM,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC,CAACjJ,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpE;EAEA;EACAmC,gBAAgBA,CAAA;IACd;IACA,IAAI,CAAC9P,cAAc,CAACqN,OAAO,CAACG,KAAK,IAAIA,KAAK,CAAClL,QAAQ,GAAG,KAAK,CAAC;IAC5D,IAAI,CAACtC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACwE,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAACX,WAAW,GAAG,CAAC;IAEpB;IACA,IAAI,CAACnD,aAAa,CAACS,WAAW,GAAG,EAAE;IAEnC;IACA,IAAI,CAACyO,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAAClP,aAAa,CAAC4J,cAAc,GAAG,IAAI,CAACD,gBAAgB;IACzD,IAAI,CAACvJ,QAAQ,EAAE;EACjB;EAIA;EACAiP,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC5F,cAAc,CAAC6F,MAAM,CAACH,QAAQ,IAAG;MAC3C;MACA,IAAI,IAAI,CAACxF,gBAAgB,IAAIwF,QAAQ,CAAC7J,IAAI,KAAK,IAAI,CAACqE,gBAAgB,EAAE;QACpE,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAAC3J,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMsP,OAAO,GAAG,IAAI,CAACvP,aAAa,CAACC,aAAa,CAACuP,WAAW,EAAE;QAC9D,MAAMC,gBAAgB,GAAGN,QAAQ,CAAC3I,MAAM,CAACkJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAAC9G,MAAM,CAACyJ,IAAI,CAAC5C,KAAK,IACrBA,KAAK,CAACrK,UAAU,CAAC+M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IAChDzC,KAAK,CAACpK,aAAa,CAAC8M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,CACpD,CACF;QACD,IAAI,CAACE,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACzP,aAAa,CAACM,YAAY,EAAE;QACnC,MAAMsP,iBAAiB,GAAGT,QAAQ,CAAC3I,MAAM,CAACkJ,IAAI,CAAC3C,KAAK,IAClDA,KAAK,CAAC9G,MAAM,CAACyJ,IAAI,CAAC5C,KAAK,IAAI,IAAI,CAAC+C,mBAAmB,CAAC/C,KAAK,CAAC,CAAC,CAC5D;QACD,IAAI,CAAC8C,iBAAiB,EAAE;UACtB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC5P,aAAa,CAACS,WAAW,EAAE;QAClC,MAAM6F,WAAW,GAAGwJ,QAAQ,CAAC,IAAI,CAAC9P,aAAa,CAACS,WAAW,CAAC;QAC5D,MAAMsP,gBAAgB,GAAGZ,QAAQ,CAAC3I,MAAM,CAACkJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAACzG,WAAW,KAAKA,WAAW,CAClC;QACD,IAAI,CAACyJ,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CAAC9D,GAAG,CAACkD,QAAQ,IAAG;MAChB;MACA,MAAMa,gBAAgB,GAAG;QAAE,GAAGb;MAAQ,CAAE;MACxCa,gBAAgB,CAACxJ,MAAM,GAAG2I,QAAQ,CAAC3I,MAAM,CAAC8I,MAAM,CAACvC,KAAK,IAAG;QACvD;QACA,IAAI,IAAI,CAAC/M,aAAa,CAACS,WAAW,EAAE;UAClC,MAAM6F,WAAW,GAAGwJ,QAAQ,CAAC,IAAI,CAAC9P,aAAa,CAACS,WAAW,CAAC;UAC5D,IAAIsM,KAAK,CAACzG,WAAW,KAAKA,WAAW,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QAEA;QACA,MAAM2J,cAAc,GAAGlD,KAAK,CAAC9G,MAAM,CAACyJ,IAAI,CAAC5C,KAAK,IAAG;UAC/C;UACA,IAAI,IAAI,CAAC9M,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMsP,OAAO,GAAG,IAAI,CAACvP,aAAa,CAACC,aAAa,CAACuP,WAAW,EAAE;YAC9D,IAAI,CAAC1C,KAAK,CAACrK,UAAU,CAAC+M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzC,KAAK,CAACpK,aAAa,CAAC8M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAACvP,aAAa,CAACM,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACuP,mBAAmB,CAAC/C,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOmD,cAAc;MACvB,CAAC,CAAC,CAAChE,GAAG,CAACc,KAAK,IAAG;QACb;QACA,MAAMmD,aAAa,GAAG;UAAE,GAAGnD;QAAK,CAAE;QAClCmD,aAAa,CAACjK,MAAM,GAAG8G,KAAK,CAAC9G,MAAM,CAACqJ,MAAM,CAACxC,KAAK,IAAG;UACjD;UACA,IAAI,IAAI,CAAC9M,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMsP,OAAO,GAAG,IAAI,CAACvP,aAAa,CAACC,aAAa,CAACuP,WAAW,EAAE;YAC9D,IAAI,CAAC1C,KAAK,CAACrK,UAAU,CAAC+M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzC,KAAK,CAACpK,aAAa,CAAC8M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAACvP,aAAa,CAACM,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACuP,mBAAmB,CAAC/C,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOoD,aAAa;MACtB,CAAC,CAAC;MAEF,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ;EAEA;EACQH,mBAAmBA,CAAC/C,KAAqB;IAC/C,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;IAEzC,QAAQ,IAAI,CAAC9M,aAAa,CAACM,YAAY;MACrC,KAAK,QAAQ;QACX,OAAO6P,MAAM,KAAK,QAAQ;MAC5B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,UAAU;MAC9B;QACE,OAAO,IAAI;MAAE;IACjB;EACF;EAEA;EACQC,cAAcA,CAACtD,KAAqB;IAC1C,IAAI,CAACA,KAAK,CAACvK,QAAQ,EAAE;MACnB,OAAO,UAAU;IACnB;IAEA;IACA,IAAI,CAACuK,KAAK,CAACvL,gBAAgB,IAAI,CAACuL,KAAK,CAACtL,cAAc,IAClDsL,KAAK,CAACvL,gBAAgB,KAAK,EAAE,IAAIuL,KAAK,CAACtL,cAAc,KAAK,EAAE,EAAE;MAC9D,OAAO,SAAS;IAClB;IAEA,IAAI;MACF;MACA,MAAM6O,GAAG,GAAG,IAAIvF,IAAI,EAAE;MACtB,MAAMwF,KAAK,GAAG,IAAIxF,IAAI,CAACuF,GAAG,CAACE,WAAW,EAAE,EAAEF,GAAG,CAACG,QAAQ,EAAE,EAAEH,GAAG,CAACI,OAAO,EAAE,CAAC;MAExE;MACA,IAAItJ,SAAe;MACnB,IAAI2F,KAAK,CAACvL,gBAAgB,CAACoO,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxCxI,SAAS,GAAG,IAAI2D,IAAI,CAACgC,KAAK,CAACvL,gBAAgB,CAAC;MAC9C,CAAC,MAAM;QACL4F,SAAS,GAAG,IAAI2D,IAAI,CAACgC,KAAK,CAACvL,gBAAgB,GAAG,WAAW,CAAC;MAC5D;MAEA;MACA,IAAI8F,OAAa;MACjB,IAAIyF,KAAK,CAACtL,cAAc,CAACmO,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtCtI,OAAO,GAAG,IAAIyD,IAAI,CAACgC,KAAK,CAACtL,cAAc,CAAC;MAC1C,CAAC,MAAM;QACL6F,OAAO,GAAG,IAAIyD,IAAI,CAACgC,KAAK,CAACtL,cAAc,GAAG,WAAW,CAAC;MACxD;MAEA;MACA,IAAIkP,KAAK,CAACvJ,SAAS,CAACwJ,OAAO,EAAE,CAAC,IAAID,KAAK,CAACrJ,OAAO,CAACsJ,OAAO,EAAE,CAAC,EAAE;QAC1DC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAE;UACnCC,KAAK,EAAEhE,KAAK,CAACvL,gBAAgB;UAC7BwP,GAAG,EAAEjE,KAAK,CAACtL,cAAc;UACzBwP,OAAO,EAAElE,KAAK,CAACvK;SAChB,CAAC;QACF,OAAO,SAAS;MAClB;MAEA;MACA,MAAM0O,aAAa,GAAG,IAAInG,IAAI,CAAC3D,SAAS,CAACoJ,WAAW,EAAE,EAAEpJ,SAAS,CAACqJ,QAAQ,EAAE,EAAErJ,SAAS,CAACsJ,OAAO,EAAE,CAAC;MAClG,MAAMS,WAAW,GAAG,IAAIpG,IAAI,CAACzD,OAAO,CAACkJ,WAAW,EAAE,EAAElJ,OAAO,CAACmJ,QAAQ,EAAE,EAAEnJ,OAAO,CAACoJ,OAAO,EAAE,CAAC;MAE1F;MACA,IAAIH,KAAK,GAAGW,aAAa,EAAE;QACzB,OAAO,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIX,KAAK,IAAIW,aAAa,IAAIX,KAAK,IAAIY,WAAW,EAAE;QACzD,OAAO,QAAQ,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,OAAO,SAAS,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,EAAErE,KAAK,CAAC;MACnD,OAAO,SAAS;IAClB;EACF;EAEAnF,OAAOA,CAACiD,GAAQ;IACdA,GAAG,CAACgB,KAAK,EAAE;EACb;EAEAT,UAAUA,CAAA;IACR,IAAI,CAACnC,KAAK,CAACoI,KAAK,EAAE;IAClB,IAAI,CAACpI,KAAK,CAACqI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAClJ,uBAAuB,CAAC5G,gBAAgB,CAAC;IAC9E,IAAI,CAACyH,KAAK,CAACqI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAClJ,uBAAuB,CAAC3G,cAAc,CAAC;IAC5E,IAAI,CAACwH,KAAK,CAACsI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACnJ,uBAAuB,CAAC5G,gBAAgB,EAAE,IAAI,CAAC4G,uBAAuB,CAAC3G,cAAc,CAAC;EACtI;EAEA+P,gBAAgBA,CAAA;IACd,IAAI,CAACpI,MAAM,CAACqI,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAAChH,WAAW,EAAEC,kBAAkB,EAAE0B,GAAG,EAAE,CAAC,CAAC;EACnG;EAEA;EACAlI,gBAAgBA,CAACkL,QAAwB;IACvC,IAAI,CAAC9J,wBAAwB,GAAG8J,QAAQ,IAAI,IAAI;IAEhD;IACA,MAAMsC,iBAAiB,GAAG,IAAI,CAACnS,cAAc,CAACC,MAAM,GAAG,CAAC;IAExD,IAAI,CAACoH,aAAa,GAAG;MACnBQ,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbT,UAAU,EAAE,CAAC6K,iBAAiB,IAAI,CAACtC,QAAQ;MAC3CtF,iBAAiB,EAAEsF,QAAQ,GAAG,CAACA,QAAQ,CAAC7J,IAAI,CAAC,GAAG,EAAE;MAClDwE,cAAc,EAAE,EAAE;MAClBxK,cAAc,EAAE;KACjB;IAED;IACA,IAAI6P,QAAQ,EAAE;MACZA,QAAQ,CAAC3I,MAAM,CAACmG,OAAO,CAACI,KAAK,IAAG;QAC9BA,KAAK,CAACnL,QAAQ,GAAG,KAAK;QACtBmL,KAAK,CAAC9G,MAAM,CAAC0G,OAAO,CAACG,KAAK,IAAIA,KAAK,CAAClL,QAAQ,GAAG,KAAK,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAACkH,aAAa,CAACiC,IAAI,CAAC,IAAI,CAAC2G,kBAAkB,CAAC;EAClD;EAEA;EACAtL,sBAAsBA,CAAC2G,KAAiB;IACtC,IAAIA,KAAK,CAACnL,QAAQ,EAAE;MAClBmL,KAAK,CAAC9G,MAAM,CAAC0G,OAAO,CAACG,KAAK,IAAG;QAC3B,IAAIA,KAAK,CAACvK,QAAQ,EAAE;UAClBuK,KAAK,CAAClL,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLmL,KAAK,CAAC9G,MAAM,CAAC0G,OAAO,CAACG,KAAK,IAAIA,KAAK,CAAClL,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA;EACAiG,aAAaA,CAAC+C,GAAQ;IACpB;IACA,IAAI,CAAC5B,KAAK,CAACoI,KAAK,EAAE;IAClB,IAAI,CAACpI,KAAK,CAACqI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC1K,aAAa,CAACQ,SAAS,CAAC;IAC3D,IAAI,CAAC6B,KAAK,CAACqI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC1K,aAAa,CAACU,OAAO,CAAC;IACzD,IAAI,CAAC2B,KAAK,CAACsI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC3K,aAAa,CAACQ,SAAS,EAAE,IAAI,CAACR,aAAa,CAACU,OAAO,CAAC;IAElG,IAAI,IAAI,CAAC2B,KAAK,CAACoC,aAAa,CAAC7L,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACwJ,OAAO,CAACsC,aAAa,CAAC,IAAI,CAACrC,KAAK,CAACoC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMuG,cAAc,GAAU,EAAE;IAEhC,IAAI,IAAI,CAAChL,aAAa,CAACC,UAAU,EAAE;MACjC;MACA,IAAI,CAACE,eAAe,CAAC6F,OAAO,CAACG,KAAK,IAAG;QACnC,IAAIA,KAAK,CAACvK,QAAQ,EAAE;UAClBoP,cAAc,CAAC3E,IAAI,CAAC;YAClBzK,QAAQ,EAAEuK,KAAK,CAACvK,QAAQ;YACxBhB,gBAAgB,EAAE,IAAI,CAACyJ,UAAU,CAAC,IAAI,CAACrE,aAAa,CAACQ,SAAS,CAAC;YAC/D3F,cAAc,EAAE,IAAI,CAACwJ,UAAU,CAAC,IAAI,CAACrE,aAAa,CAACU,OAAO;WAC3D,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAAC/H,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACD,cAAc,CAACqN,OAAO,CAACG,KAAK,IAAG;UAClC,IAAIA,KAAK,CAACvK,QAAQ,EAAE;YAClBoP,cAAc,CAAC3E,IAAI,CAAC;cAClBzK,QAAQ,EAAEuK,KAAK,CAACvK,QAAQ;cACxBhB,gBAAgB,EAAE,IAAI,CAACyJ,UAAU,CAAC,IAAI,CAACrE,aAAa,CAACQ,SAAS,CAAC;cAC/D3F,cAAc,EAAE,IAAI,CAACwJ,UAAU,CAAC,IAAI,CAACrE,aAAa,CAACU,OAAO;aAC3D,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAAChC,wBAAwB,EAAE;QACxC;QACA,IAAI,CAACA,wBAAwB,CAACmB,MAAM,CAACmG,OAAO,CAACI,KAAK,IAAG;UACnDA,KAAK,CAAC9G,MAAM,CAAC0G,OAAO,CAACG,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAAClL,QAAQ,IAAIkL,KAAK,CAACvK,QAAQ,EAAE;cACpCoP,cAAc,CAAC3E,IAAI,CAAC;gBAClBzK,QAAQ,EAAEuK,KAAK,CAACvK,QAAQ;gBACxBhB,gBAAgB,EAAE,IAAI,CAACyJ,UAAU,CAAC,IAAI,CAACrE,aAAa,CAACQ,SAAS,CAAC;gBAC/D3F,cAAc,EAAE,IAAI,CAACwJ,UAAU,CAAC,IAAI,CAACrE,aAAa,CAACU,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIsK,cAAc,CAACpS,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAACwJ,OAAO,CAACsC,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAACpC,aAAa,CAACsC,oCAAoC,CAAC;MACtDC,IAAI,EAAEmG;KACP,CAAC,CAACrH,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC1C,OAAO,CAAC2C,aAAa,CAAC,QAAQiG,cAAc,CAACpS,MAAM,WAAW,CAAC;QACpE;QACA,IAAI,CAACD,cAAc,CAACqN,OAAO,CAACG,KAAK,IAAIA,KAAK,CAAClL,QAAQ,GAAG,KAAK,CAAC;QAC5D,IAAI,CAACtC,cAAc,GAAG,EAAE;QACxB,IAAI,CAACwE,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC6H,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA;EACA/I,cAAcA,CAACiK,KAAqB;IAClC,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;IACzC,OAAO,UAAUqD,MAAM,EAAE;EAC3B;EAEA;EACAyB,eAAeA,CAAC9E,KAAqB;IACnC,IAAIA,KAAK,CAACvK,QAAQ,EAAE;MAClB;MACA,IAAI,CAACF,SAAS,CAAC,IAAI,CAACwP,MAAM,EAAE/E,KAAK,CAAC;IACpC;EACF;EAEA;EACAwB,oBAAoBA,CAACC,IAA6B;IAChD,IAAI,CAACzH,eAAe,GAAG,EAAE;IAEzByH,IAAI,CAAC5B,OAAO,CAACC,SAAS,IAAG;MACvB,MAAM8B,SAAS,GAAG9B,SAAS,CAACnK,UAAU,IAAI,EAAE;MAE5CmK,SAAS,CAACC,OAAO,EAAEF,OAAO,CAACG,KAAK,IAAG;QACjC,IAAI,CAAChG,eAAe,CAACkG,IAAI,CAAC;UACxBvK,UAAU,EAAEiM,SAAS;UACrBhM,aAAa,EAAEoK,KAAK,CAACpK,aAAa,IAAI,KAAK;UAC3CH,QAAQ,EAAEuK,KAAK,CAACvK,QAAQ,IAAI,CAAC;UAC7BI,MAAM,EAAEmK,KAAK,CAACnK,MAAM,IAAI,CAAC;UACzBpB,gBAAgB,EAAEuL,KAAK,CAACvL,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEsL,KAAK,CAACtL,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACxB,QAAQ,EAAE;IAEf;IACA,IAAI,CAAC0R,iBAAiB,EAAE;EAC1B;EAEA;EACQA,iBAAiBA,CAAA;IACvB,MAAMC,YAAY,GAAG;MACnBC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACV,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAACrL,eAAe,CAAC6F,OAAO,CAACG,KAAK,IAAG;MACnC,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;MACzC,IAAIiF,YAAY,CAACK,cAAc,CAACjC,MAAM,CAAC,EAAE;QACvC4B,YAAY,CAAC5B,MAAmC,CAAC,EAAE;MACrD;IACF,CAAC,CAAC;IAEFS,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAEN,YAAY,CAAC;IAClCnB,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAE,IAAIvH,IAAI,EAAE,CAACwH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;EAIA;EACAnS,QAAQA,CAAA;IACN;IACA,MAAMoS,qBAAqB,GAAG,IAAI,CAAClT,cAAc,CAAC2M,GAAG,CAACa,KAAK,IAAIA,KAAK,CAACvK,QAAQ,CAAC;IAE9E,IAAI,CAACnB,cAAc,GAAG,IAAI,CAAC0F,eAAe,CAACwI,MAAM,CAACxC,KAAK,IAAG;MACxD;MACA,IAAI,IAAI,CAAC9M,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMsP,OAAO,GAAG,IAAI,CAACvP,aAAa,CAACC,aAAa,CAACuP,WAAW,EAAE;QAC9D,IAAI,CAAC1C,KAAK,CAACrK,UAAU,CAAC+M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzC,KAAK,CAACpK,aAAa,CAAC8M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;UACtD,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC5F,gBAAgB,IAAImD,KAAK,CAACpK,aAAa,KAAK,IAAI,CAACiH,gBAAgB,EAAE;QAC1E,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAAC3J,aAAa,CAACM,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAACuP,mBAAmB,CAAC/C,KAAK,CAAC,EAAE;UACpC,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC9M,aAAa,CAACS,WAAW,EAAE;QAClC,MAAM6F,WAAW,GAAGwJ,QAAQ,CAAC,IAAI,CAAC9P,aAAa,CAACS,WAAW,CAAC;QAC5D,IAAIqM,KAAK,CAACnK,MAAM,KAAK2D,WAAW,EAAE;UAChC,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF;IACA,IAAI,CAAChH,cAAc,GAAG,IAAI,CAAC8B,cAAc,CAACkO,MAAM,CAACxC,KAAK,IACpD0F,qBAAqB,CAAC7C,QAAQ,CAAC7C,KAAK,CAACvK,QAAQ,CAAC,CAC/C;IAED;IACA,IAAI,CAACuE,eAAe,CAAC6F,OAAO,CAACG,KAAK,IAAG;MACnCA,KAAK,CAAClL,QAAQ,GAAG,IAAI,CAACtC,cAAc,CAACoQ,IAAI,CAAC9N,QAAQ,IAAIA,QAAQ,CAACW,QAAQ,KAAKuK,KAAK,CAACvK,QAAQ,CAAC;IAC7F,CAAC,CAAC;IAEF;IACA,IAAI,CAACkQ,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACtP,WAAW,GAAG,CAAC;IACpB,IAAI,CAACuP,gBAAgB,EAAE;EACzB;EAEA;EACQD,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACtN,eAAe,CAAC5F,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACuE,SAAS,GAAG,KAAK;IACxB,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI,CAACqB,eAAe,CAACwN,KAAK,CAAC7F,KAAK,IAC/C,CAACA,KAAK,CAACvK,QAAQ,IAAIuK,KAAK,CAAClL,QAAQ,CAClC;IACH;EACF;EAEA;EACA8Q,gBAAgBA,CAAA;IACd,IAAI,CAAChP,UAAU,GAAGqB,IAAI,CAAC6N,IAAI,CAAC,IAAI,CAACxR,cAAc,CAAC7B,MAAM,GAAG,IAAI,CAACuB,QAAQ,CAAC;IACvE,MAAM+R,UAAU,GAAG,CAAC,IAAI,CAAC1P,WAAW,GAAG,CAAC,IAAI,IAAI,CAACrC,QAAQ;IACzD,MAAMgS,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC/R,QAAQ;IAC3C,IAAI,CAACqE,eAAe,GAAG,IAAI,CAAC/D,cAAc,CAACsE,KAAK,CAACmN,UAAU,EAAEC,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACL,oBAAoB,EAAE;EAC7B;EAEA;EACAzR,gBAAgBA,CAAA;IACd,IAAI,CAACmC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACuP,gBAAgB,EAAE;EACzB;EAEA;EACAxP,QAAQA,CAAC6P,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACrP,UAAU,EAAE;MACxC,IAAI,CAACP,WAAW,GAAG4P,IAAI;MACvB,IAAI,CAACL,gBAAgB,EAAE;IACzB;EACF;EAEA;EACA/O,eAAeA,CAAA;IACb,MAAMqP,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAInC,KAAK,GAAG/L,IAAI,CAACmO,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC/P,WAAW,GAAG4B,IAAI,CAACgI,KAAK,CAACkG,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAIlC,GAAG,GAAGhM,IAAI,CAACC,GAAG,CAAC,IAAI,CAACtB,UAAU,EAAEoN,KAAK,GAAGmC,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAIlC,GAAG,GAAGD,KAAK,GAAG,CAAC,GAAGmC,UAAU,EAAE;MAChCnC,KAAK,GAAG/L,IAAI,CAACmO,GAAG,CAAC,CAAC,EAAEnC,GAAG,GAAGkC,UAAU,GAAG,CAAC,CAAC;IAC3C;IAEA,KAAK,IAAIE,CAAC,GAAGrC,KAAK,EAAEqC,CAAC,IAAIpC,GAAG,EAAEoC,CAAC,EAAE,EAAE;MACjCH,KAAK,CAAChG,IAAI,CAACmG,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACAjP,iBAAiBA,CAAA;IACf,IAAI,CAACoB,eAAe,CAACwH,OAAO,CAACG,KAAK,IAAG;MACnC,IAAIA,KAAK,CAACvK,QAAQ,EAAE;QAClBuK,KAAK,CAAClL,QAAQ,GAAG,IAAI,CAACkC,SAAS;MACjC;IACF,CAAC,CAAC;IACF,IAAI,CAACsP,oBAAoB,EAAE;EAC7B;EAEA;EACAvR,sBAAsBA,CAAA;IACpB,IAAI,CAACuR,oBAAoB,EAAE;IAC3B,IAAI,CAACtP,SAAS,GAAG,IAAI,CAACqB,eAAe,CAACwN,KAAK,CAAC7F,KAAK,IAC/C,CAACA,KAAK,CAACvK,QAAQ,IAAIuK,KAAK,CAAClL,QAAQ,CAClC;EACH;EAEA;EACAwR,oBAAoBA,CAAA;IAClB,IAAI,CAAC9T,cAAc,GAAG,IAAI,CAACwH,eAAe,CAACwI,MAAM,CAACxC,KAAK,IAAIA,KAAK,CAAClL,QAAQ,CAAC;EAC5E;EAEA;EACA2C,IAAIA,CAAC8O,KAAa;IAChB,IAAI,IAAI,CAACpO,SAAS,KAAKoO,KAAK,EAAE;MAC5B,IAAI,CAACnO,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IACpE,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAGoO,KAAK;MACtB,IAAI,CAACnO,aAAa,GAAG,KAAK;IAC5B;IAEA,IAAI,CAAC9D,cAAc,CAACmD,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAI;MAChC,IAAIoG,MAAM,GAAIrG,CAAS,CAACoG,KAAK,CAAC;MAC9B,IAAIE,MAAM,GAAIrG,CAAS,CAACmG,KAAK,CAAC;MAE9B;MACA,IAAIA,KAAK,CAAC1D,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC1B2D,MAAM,GAAGA,MAAM,GAAG,IAAIxI,IAAI,CAACwI,MAAM,CAAC,CAAC3C,OAAO,EAAE,GAAG,CAAC;QAChD4C,MAAM,GAAGA,MAAM,GAAG,IAAIzI,IAAI,CAACyI,MAAM,CAAC,CAAC5C,OAAO,EAAE,GAAG,CAAC;MAClD;MAEA;MACA,IAAI0C,KAAK,KAAK,QAAQ,EAAE;QACtBC,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC,IAAI,CAAC;QAC5BC,MAAM,GAAGC,MAAM,CAACD,MAAM,CAAC,IAAI,CAAC;MAC9B;MAEA;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAAC9D,WAAW,EAAE;QAC7B+D,MAAM,GAAGA,MAAM,CAAC/D,WAAW,EAAE;MAC/B;MAEA,IAAI8D,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACrO,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC9C;MACA,IAAIoO,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACrO,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9C;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAACwN,gBAAgB,EAAE;EACzB;EAEA;EACAtN,cAAcA,CAACqO,MAAc,EAAE3G,KAAqB;IAClD,OAAOA,KAAK,CAACvK,QAAQ;EACvB;EAEA;EACAO,aAAaA,CAACgK,KAAqB;IACjC,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;IAEzC,QAAQqD,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,UAAU;QACb,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACA/L,UAAUA,CAAA;IACR;IACA,MAAMsP,UAAU,GAAG,IAAI,CAACC,WAAW,EAAE;IACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACrCG,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,UAAU,IAAIvJ,IAAI,EAAE,CAACwH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACrFwB,IAAI,CAACO,KAAK,CAACC,UAAU,GAAG,QAAQ;IAChCP,QAAQ,CAACxI,IAAI,CAACgJ,WAAW,CAACT,IAAI,CAAC;IAC/BA,IAAI,CAACU,KAAK,EAAE;IACZT,QAAQ,CAACxI,IAAI,CAACkJ,WAAW,CAACX,IAAI,CAAC;EACjC;EAEA;EACQJ,WAAWA,CAAA;IACjB,MAAMgB,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;IACxD,MAAMC,IAAI,GAAG,IAAI,CAACxT,cAAc,CAAC6K,GAAG,CAACa,KAAK,IAAI,CAC5CA,KAAK,CAACrK,UAAU,EAChBqK,KAAK,CAACpK,aAAa,EACnB,GAAGoK,KAAK,CAACnK,MAAM,GAAG,EAClBmK,KAAK,CAACvL,gBAAgB,IAAI,KAAK,EAC/BuL,KAAK,CAACtL,cAAc,IAAI,KAAK,EAC7B,IAAI,CAACsB,aAAa,CAACgK,KAAK,CAAC,CAC1B,CAAC;IAEF,MAAM4G,UAAU,GAAG,CAACiB,OAAO,EAAE,GAAGC,IAAI,CAAC,CAClC3I,GAAG,CAAC4I,GAAG,IAAIA,GAAG,CAAC5I,GAAG,CAAC6I,IAAI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAClDA,IAAI,CAAC,IAAI,CAAC;IAEb,OAAO,QAAQ,GAAGrB,UAAU,CAAC,CAAC;EAChC;;;uCA5gCW/K,0BAA0B,EAAAjK,EAAA,CAAAsW,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxW,EAAA,CAAAsW,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA1W,EAAA,CAAAsW,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA5W,EAAA,CAAAsW,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA9W,EAAA,CAAAsW,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAhX,EAAA,CAAAsW,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAAjX,EAAA,CAAAsW,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAAnX,EAAA,CAAAsW,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA1BpN,0BAA0B;MAAAqN,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;uCAR1B,EAAE,GAAAzX,EAAA,CAAA2X,0BAAA,EAAA3X,EAAA,CAAA4X,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCpEbzX,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAc,SAAA,qBAAiC;UACnCd,EAAA,CAAAG,YAAA,EAAiB;UAQPH,EAPV,CAAAC,cAAA,mBAAc,cAEmB,cAEC,cACmC,UACxD,aACoB;UACrBD,EAAA,CAAAc,SAAA,YAAwC;UACxCd,EAAA,CAAAE,MAAA,0DACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAiC;UAC/BD,EAAA,CAAAE,MAAA,oMACF;UAGNF,EAHM,CAAAG,YAAA,EAAI,EACA,EACF,EACF;UAMAH,EAHN,CAAAC,cAAA,eAAkC,eACC,eACK,iBACR;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpCH,EAAA,CAAAC,cAAA,qBACyC;UADVD,EAAA,CAAAe,gBAAA,2BAAAmX,wEAAAjX,MAAA;YAAAjB,EAAA,CAAAkB,aAAA,CAAAiX,GAAA;YAAAnY,EAAA,CAAAqB,kBAAA,CAAAqW,GAAA,CAAA5L,WAAA,CAAAC,kBAAA,EAAA9K,MAAA,MAAAyW,GAAA,CAAA5L,WAAA,CAAAC,kBAAA,GAAA9K,MAAA;YAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;UAAA,EAA4C;UACzEjB,EAAA,CAAAyB,UAAA,4BAAA2W,yEAAA;YAAApY,EAAA,CAAAkB,aAAA,CAAAiX,GAAA;YAAA,OAAAnY,EAAA,CAAAwB,WAAA,CAAkBkW,GAAA,CAAArI,iBAAA,EAAmB;UAAA,EAAC;UACtCrP,EAAA,CAAAiC,UAAA,KAAAoW,gDAAA,wBAAoE;UAIxErY,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAAoC,iBACR;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpCH,EAAA,CAAAC,cAAA,qBAAmG;UAArED,EAAA,CAAAe,gBAAA,2BAAAuX,wEAAArX,MAAA;YAAAjB,EAAA,CAAAkB,aAAA,CAAAiX,GAAA;YAAAnY,EAAA,CAAAqB,kBAAA,CAAAqW,GAAA,CAAAzM,gBAAA,EAAAhK,MAAA,MAAAyW,GAAA,CAAAzM,gBAAA,GAAAhK,MAAA;YAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;UAAA,EAA8B;UAACjB,EAAA,CAAAyB,UAAA,4BAAA8W,yEAAA;YAAAvY,EAAA,CAAAkB,aAAA,CAAAiX,GAAA;YAAA,OAAAnY,EAAA,CAAAwB,WAAA,CAAkBkW,GAAA,CAAAhH,gBAAA,EAAkB;UAAA,EAAC;UAChG1Q,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAiC,UAAA,KAAAuW,gDAAA,wBAAuE;UAI3ExY,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAAoC,iBACR;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEtCH,EADF,CAAAC,cAAA,eAAuC,yBACJ;UAC/BD,EAAA,CAAAc,SAAA,mBAAoD;UACpDd,EAAA,CAAAC,cAAA,iBAC6C;UAA3CD,EAAA,CAAAe,gBAAA,2BAAA0X,oEAAAxX,MAAA;YAAAjB,EAAA,CAAAkB,aAAA,CAAAiX,GAAA;YAAAnY,EAAA,CAAAqB,kBAAA,CAAAqW,GAAA,CAAA5L,WAAA,CAAAjJ,gBAAA,EAAA5B,MAAA,MAAAyW,GAAA,CAAA5L,WAAA,CAAAjJ,gBAAA,GAAA5B,MAAA;YAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;UAAA,EAA0C;UAD5CjB,EAAA,CAAAG,YAAA,EAC6C;UAC7CH,EAAA,CAAAc,SAAA,4BAA8D;UAChEd,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,yBAAiC;UAC/BD,EAAA,CAAAc,SAAA,mBAAoD;UACpDd,EAAA,CAAAC,cAAA,iBAC2C;UAAzCD,EAAA,CAAAe,gBAAA,2BAAA2X,oEAAAzX,MAAA;YAAAjB,EAAA,CAAAkB,aAAA,CAAAiX,GAAA;YAAAnY,EAAA,CAAAqB,kBAAA,CAAAqW,GAAA,CAAA5L,WAAA,CAAAhJ,cAAA,EAAA7B,MAAA,MAAAyW,GAAA,CAAA5L,WAAA,CAAAhJ,cAAA,GAAA7B,MAAA;YAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;UAAA,EAAwC;UAD1CjB,EAAA,CAAAG,YAAA,EAC2C;UAC3CH,EAAA,CAAAc,SAAA,4BAA4D;UAGlEd,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAqC,eAC8B,kBAC4B;UAApDD,EAAA,CAAAyB,UAAA,mBAAAkX,6DAAA;YAAA3Y,EAAA,CAAAkB,aAAA,CAAAiX,GAAA;YAAA,OAAAnY,EAAA,CAAAwB,WAAA,CAASkW,GAAA,CAAAzK,kBAAA,EAAoB;UAAA,EAAC;UACnEjN,EAAA,CAAAc,SAAA,aAAkC;UAAAd,EAAA,CAAAE,MAAA,qBACpC;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;UAGNH,EAAA,CAAAiC,UAAA,KAAA2W,0CAAA,oBAAiE;UAsDnE5Y,EAAA,CAAAG,YAAA,EAAM;UAiJNH,EA9IA,CAAAiC,UAAA,KAAA4W,0CAAA,oBAAgE,KAAAC,0CAAA,kBAkIoC,KAAAC,0CAAA,kBAYtD;UAalD/Y,EADE,CAAAG,YAAA,EAAe,EACP;UAkGVH,EA/FA,CAAAiC,UAAA,KAAA+W,kDAAA,iCAAAhZ,EAAA,CAAAiZ,sBAAA,CAAgE,KAAAC,kDAAA,gCAAAlZ,EAAA,CAAAiZ,sBAAA,CAkFG,KAAAE,kDAAA,iCAAAnZ,EAAA,CAAAiZ,sBAAA,CAaf;;;;;UAtWTjZ,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAwC,gBAAA,YAAAkV,GAAA,CAAA5L,WAAA,CAAAC,kBAAA,CAA4C;UAE7C/L,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAsX,GAAA,CAAApK,oBAAA,CAAuB;UAQvBtN,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAwC,gBAAA,YAAAkV,GAAA,CAAAzM,gBAAA,CAA8B;UAE1BjL,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAAsX,GAAA,CAAA1M,eAAA,CAAkB;UAWFhL,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,iBAAAgZ,aAAA,CAA0B;UACtEpZ,EAAA,CAAAwC,gBAAA,YAAAkV,GAAA,CAAA5L,WAAA,CAAAjJ,gBAAA,CAA0C;UAME7C,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,iBAAAiZ,WAAA,CAAwB;UACpErZ,EAAA,CAAAwC,gBAAA,YAAAkV,GAAA,CAAA5L,WAAA,CAAAhJ,cAAA,CAAwC;UAQ0B9C,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,aAAAsX,GAAA,CAAArM,OAAA,CAAoB;UASnErL,EAAA,CAAAM,SAAA,GAAgC;UAAhCN,EAAA,CAAAI,UAAA,SAAAsX,GAAA,CAAAtP,eAAA,CAAAvH,MAAA,KAAgC;UAyDnCb,EAAA,CAAAM,SAAA,EAAgC;UAAhCN,EAAA,CAAAI,UAAA,SAAAsX,GAAA,CAAAtP,eAAA,CAAAvH,MAAA,KAAgC;UAkI/Bb,EAAA,CAAAM,SAAA,EAAmE;UAAnEN,EAAA,CAAAI,UAAA,SAAAsX,GAAA,CAAAtP,eAAA,CAAAvH,MAAA,UAAA6W,GAAA,CAAAnI,gBAAA,CAAA1O,MAAA,OAAmE;UAYnEb,EAAA,CAAAM,SAAA,EAAa;UAAbN,EAAA,CAAAI,UAAA,SAAAsX,GAAA,CAAArM,OAAA,CAAa;;;qBD3M5C7L,YAAY,EAAA8Z,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAE5Z,YAAY,EAAA6Z,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAApD,EAAA,CAAAqD,eAAA,EAAArD,EAAA,CAAAsD,mBAAA,EAAAtD,EAAA,CAAAuD,qBAAA,EAAAvD,EAAA,CAAAwD,qBAAA,EAAAxD,EAAA,CAAAyD,mBAAA,EAAAzD,EAAA,CAAA0D,gBAAA,EAAA1D,EAAA,CAAA2D,iBAAA,EAAA3D,EAAA,CAAA4D,iBAAA,EAAA5D,EAAA,CAAA6D,oBAAA,EAAA7D,EAAA,CAAA8D,iBAAA,EAAA9D,EAAA,CAAA+D,eAAA,EAAA/D,EAAA,CAAAgE,qBAAA,EAAAhE,EAAA,CAAAiE,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAC1Blb,kBAAkB,EAAEC,mBAAmB;MAAAkb,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}