{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r3, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_56_nb_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", floor_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", floor_r6, \"F \");\n  }\n}\nfunction SettingTimePeriodComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"div\", 12)(3, \"nb-form-field\");\n    i0.ɵɵelement(4, \"nb-icon\", 46);\n    i0.ɵɵelementStart(5, \"input\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.searchKeyword, $event) || (ctx_r4.filterOptions.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_input_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 48)(7, \"nb-select\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.statusFilter, $event) || (ctx_r4.filterOptions.statusFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(8, \"nb-option\", 19);\n    i0.ɵɵtext(9, \"\\u5168\\u90E8\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-option\", 50);\n    i0.ɵɵtext(11, \"\\u5DF2\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-option\", 51);\n    i0.ɵɵtext(13, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-option\", 52);\n    i0.ɵɵtext(15, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 48)(17, \"nb-select\", 53);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.floorFilter, $event) || (ctx_r4.filterOptions.floorFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(18, \"nb-option\", 19);\n    i0.ɵɵtext(19, \"\\u5168\\u90E8\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, SettingTimePeriodComponent_div_56_nb_option_20_Template, 2, 2, \"nb-option\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 54)(22, \"div\", 55);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.statusFilter);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.floorFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.availableFloors);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r4.buildingGroups.length, \" \\u500B\\u68DF\\u5225 \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_div_1_div_8_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_1_div_8_div_7_Template_div_click_0_listener() {\n      const house_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      const dialog_r11 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r11, house_r10));\n    });\n    i0.ɵɵelementStart(1, \"div\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 72);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"getSettingTimeStatus\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const house_r10 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"disabled\", !house_r10.CHouseId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r10.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r10.CBuildingName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r10));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", house_r10.CHouseId ? i0.ɵɵpipeBind3(7, 7, house_r10.CChangeStartDate, house_r10.CChangeEndDate, ctx_r4.isStatus) : \"\\u5DF2\\u505C\\u7528\", \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64)(2, \"span\", 65);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 66);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 67);\n    i0.ɵɵtemplate(7, SettingTimePeriodComponent_div_57_div_1_div_8_div_7_Template, 8, 11, \"div\", 68);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const floor_r12 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", floor_r12.floorNumber, \"F\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", floor_r12.houses.length, \" \\u6236\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", floor_r12.houses);\n  }\n}\nfunction SettingTimePeriodComponent_div_57_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"nb-card\")(2, \"nb-card-header\", 59)(3, \"h6\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_1_Template_button_click_5_listener() {\n      const building_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.openBatchSetting(building_r8));\n    });\n    i0.ɵɵtext(6, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\");\n    i0.ɵɵtemplate(8, SettingTimePeriodComponent_div_57_div_1_div_8_Template, 8, 3, \"div\", 62);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const building_r8 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(building_r8.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", building_r8.floors);\n  }\n}\nfunction SettingTimePeriodComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_div_57_div_1_Template, 9, 2, \"div\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getFilteredBuildings());\n  }\n}\nfunction SettingTimePeriodComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 66);\n    i0.ɵɵelement(4, \"i\", 74);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\\u5F8C\\u67E5\\u8A62\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_28_div_1_div_3_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_div_28_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      const house_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r17.selected, $event) || (house_r17.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r17 = ctx.$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r17.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r17.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", house_r17.CHouseHold, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_28_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_59_div_28_div_1_div_3_nb_checkbox_1_Template, 2, 3, \"nb-checkbox\", 93);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", floor_r15.houses);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"nb-checkbox\", 83);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_div_28_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const floor_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      i0.ɵɵtwoWayBindingSet(floor_r15.selected, $event) || (floor_r15.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_div_28_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      const floor_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.onFloorSelectionChange(floor_r15));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_59_div_28_div_1_div_3_Template, 2, 1, \"div\", 91);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r15 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", floor_r15.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r15.floorNumber, \"F (\", floor_r15.houses.length, \" \\u6236) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", floor_r15.selected);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_59_div_28_div_1_Template, 4, 4, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedBuildingForBatch.floors);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 75)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 76)(5, \"label\");\n    i0.ɵɵtext(6, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n    i0.ɵɵelementStart(7, \"span\", 77);\n    i0.ɵɵtext(8, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 78)(10, \"nb-form-field\", 79);\n    i0.ɵɵelement(11, \"nb-icon\", 23);\n    i0.ɵɵelementStart(12, \"input\", 80);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.startDate, $event) || (ctx_r4.batchSettings.startDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"nb-datepicker\", 25, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 81);\n    i0.ɵɵtext(16, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"nb-form-field\", 79);\n    i0.ɵɵelement(18, \"nb-icon\", 23);\n    i0.ɵɵelementStart(19, \"input\", 80);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_Template_input_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.endDate, $event) || (ctx_r4.batchSettings.endDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"nb-datepicker\", 25, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 76)(23, \"label\");\n    i0.ɵɵtext(24, \"\\u9069\\u7528\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 82)(26, \"nb-checkbox\", 83);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_Template_nb_checkbox_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.applyToAll, $event) || (ctx_r4.batchSettings.applyToAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(27, \" \\u5168\\u90E8\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_ng_template_59_div_28_Template, 2, 1, \"div\", 84);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"nb-card-footer\", 85)(30, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_59_Template_button_click_30_listener() {\n      const ref_r18 = i0.ɵɵrestoreView(_r13).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r18));\n    });\n    i0.ɵɵtext(31, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_59_Template_button_click_32_listener() {\n      const ref_r18 = i0.ɵɵrestoreView(_r13).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onBatchSubmit(ref_r18));\n    });\n    i0.ɵɵtext(33, \"\\u6279\\u6B21\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const batchStartDate_r19 = i0.ɵɵreference(14);\n    const batchEndDate_r20 = i0.ɵɵreference(21);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u6279\\u6B21\\u8A2D\\u5B9A - \", (ctx_r4.selectedBuildingForBatch == null ? null : ctx_r4.selectedBuildingForBatch.name) || \"\\u5168\\u90E8\", \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", batchStartDate_r19);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.startDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", batchEndDate_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.endDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.applyToAll);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.batchSettings.applyToAll && ctx_r4.selectedBuildingForBatch);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 95);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 96);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 95)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 97);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 98)(7, \"div\", 20)(8, \"label\", 21);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 77);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 99);\n    i0.ɵɵelement(13, \"nb-icon\", 23);\n    i0.ɵɵelementStart(14, \"input\", 100);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_63_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 25, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 101);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 23);\n    i0.ɵɵelementStart(21, \"input\", 102);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_63_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 25, 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 96)(25, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_63_Template_button_click_25_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r22));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_63_Template_button_click_27_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSubmit(ref_r22));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r23 = i0.ɵɵreference(16);\n    const changeEndDate_r24 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r4.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r23);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r24);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.isStatus = true;\n    // 新增的屬性\n    this.buildingGroups = [];\n    this.buildingOptions = [];\n    this.selectedBuilding = '';\n    this.availableFloors = [];\n    // 篩選和搜尋\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 批次設定\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: true,\n      selectedBuildings: [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    this.selectedBuildingForBatch = null;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CBuildingName: '未分類',\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  getHouseChangeDate() {\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          // 新增：建立棟別分組資料\n          this.buildBuildingGroups(res.Entries);\n        }\n      }\n    });\n  }\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n  buildBuildingGroups(data) {\n    const buildingMap = new Map();\n    data.forEach(household => {\n      const houseType = household.CHouseHold || ''; // 戶型\n      household.CHouses?.forEach(house => {\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\n        const floor = house.CFloor || 0;\n        if (!buildingMap.has(buildingName)) {\n          buildingMap.set(buildingName, new Map());\n        }\n        const floorMap = buildingMap.get(buildingName);\n        if (!floorMap.has(floor)) {\n          floorMap.set(floor, []);\n        }\n        floorMap.get(floor).push({\n          CHouseHold: houseType,\n          // 戶型\n          CBuildingName: buildingName,\n          // 棟別\n          CHouseId: house.CHouseId || 0,\n          CFloor: floor,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 轉換為BuildingGroup格式\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n      const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n      .map(([floorNumber, houses]) => ({\n        floorNumber,\n        houses: houses.sort((a, b) => {\n          // 排序邏輯：先按戶型排序，再按樓層排序\n          if (a.CHouseHold !== b.CHouseHold) {\n            return a.CHouseHold.localeCompare(b.CHouseHold);\n          }\n          return a.CFloor - b.CFloor;\n        }),\n        selected: false\n      }));\n      return {\n        name: buildingName,\n        floors,\n        selected: false\n      };\n    }).sort((a, b) => a.name.localeCompare(b.name));\n    // 更新棟別選項和可用樓層\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n    this.updateAvailableFloors();\n  }\n  // 新增：更新可用樓層\n  updateAvailableFloors() {\n    const floorsSet = new Set();\n    this.buildingGroups.forEach(building => {\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n        building.floors.forEach(floor => {\n          floorsSet.add(floor.floorNumber);\n        });\n      }\n    });\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n  }\n  // 新增：棟別選擇變更處理\n  onBuildingChange() {\n    this.updateAvailableFloors();\n    this.filterOptions.buildingFilter = this.selectedBuilding;\n  }\n  // 新增：搜尋處理\n  onSearch() {\n    // 搜尋功能將在HTML模板中使用管道或過濾器實現\n  }\n  // 新增：取得過濾後的棟別資料\n  getFilteredBuildings() {\n    return this.buildingGroups.filter(building => {\n      // 棟別篩選\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n        return false;\n      }\n      // 關鍵字搜尋 (搜尋戶型)\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n        if (!hasMatchingHouse) {\n          return false;\n        }\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n        if (!hasMatchingStatus) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n        if (!hasMatchingFloor) {\n          return false;\n        }\n      }\n      return true;\n    }).map(building => {\n      // 對每個棟別，也要篩選其樓層和戶別\n      const filteredBuilding = {\n        ...building\n      };\n      filteredBuilding.floors = building.floors.filter(floor => {\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (floor.floorNumber !== floorNumber) {\n            return false;\n          }\n        }\n        // 檢查該樓層是否有符合條件的戶別\n        const hasValidHouses = floor.houses.some(house => {\n          // 關鍵字篩選\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return hasValidHouses;\n      }).map(floor => {\n        // 篩選戶別\n        const filteredFloor = {\n          ...floor\n        };\n        filteredFloor.houses = floor.houses.filter(house => {\n          // 關鍵字篩選\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return filteredFloor;\n      });\n      return filteredBuilding;\n    });\n  }\n  // 新增：檢查戶別是否符合狀態篩選\n  matchesStatusFilter(house) {\n    const status = this.getHouseStatus(house);\n    switch (this.filterOptions.statusFilter) {\n      case 'active':\n        return status === 'active';\n      case 'inactive':\n        return status === 'not-set';\n      case 'disabled':\n        return status === 'disabled';\n      default:\n        return true;\n    }\n  }\n  // 新增：取得戶別狀態\n  getHouseStatus(house) {\n    if (!house.CHouseId) {\n      return 'disabled';\n    }\n    if (house.CChangeStartDate && house.CChangeEndDate) {\n      const now = new Date();\n      const startDate = new Date(house.CChangeStartDate);\n      const endDate = new Date(house.CChangeEndDate);\n      if (now < startDate) {\n        return 'pending';\n      } else if (now >= startDate && now <= endDate) {\n        return 'active';\n      } else {\n        return 'expired';\n      }\n    }\n    return 'not-set';\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  // 新增：開啟批次設定對話框\n  openBatchSetting(building) {\n    this.selectedBuildingForBatch = building || null;\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: !building,\n      selectedBuildings: building ? [building.name] : [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    // 重置選擇狀態\n    if (building) {\n      building.floors.forEach(floor => {\n        floor.selected = false;\n        floor.houses.forEach(house => house.selected = false);\n      });\n    }\n    // 開啟對話框\n    this.dialogService.open(this.batchSettingDialog);\n  }\n  // 新增：樓層選擇變更處理\n  onFloorSelectionChange(floor) {\n    if (floor.selected) {\n      floor.houses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = true;\n        }\n      });\n    } else {\n      floor.houses.forEach(house => house.selected = false);\n    }\n  }\n  // 新增：批次提交\n  onBatchSubmit(ref) {\n    // 驗證批次設定\n    this.valid.clear();\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 收集要更新的房屋\n    const housesToUpdate = [];\n    if (this.batchSettings.applyToAll) {\n      // 全部戶別\n      this.buildingGroups.forEach(building => {\n        building.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      });\n    } else {\n      // 選擇的戶別\n      if (this.selectedBuildingForBatch) {\n        this.selectedBuildingForBatch.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.selected && house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      }\n    }\n    if (housesToUpdate.length === 0) {\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\n      return;\n    }\n    // 調用API進行批次更新\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: housesToUpdate\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  // 新增：取得狀態樣式類別\n  getStatusClass(house) {\n    const status = this.getHouseStatus(house);\n    return `status-${status}`;\n  }\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n  openHouseDialog(house) {\n    if (house.CHouseId) {\n      // 使用現有的openModel方法\n      this.openModel(this.dialog, house);\n    }\n  }\n  static {\n    this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingTimePeriodComponent,\n      selectors: [[\"ngx-setting-time-period\"]],\n      viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 65,\n      vars: 15,\n      consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"batchSettingDialog\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"batchStartDate\", \"\"], [\"batchEndDate\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"buildingSelect\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u68DF\\u5225\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-1\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [1, \"mx-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-md-7\", \"max-sm:col-md-12\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [1, \"form-check\", \"mx-2\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault1\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault1\", 1, \"form-check-label\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault2\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault2\", 1, \"form-check-label\"], [1, \"col-md-5\", \"max-sm:col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [\"class\", \"search-enhanced mt-3\", 4, \"ngIf\"], [\"class\", \"row mt-4\", 4, \"ngIf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [3, \"value\"], [1, \"search-enhanced\", \"mt-3\"], [1, \"row\"], [\"nbPrefix\", \"\", \"icon\", \"search-outline\"], [\"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u5225...\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-3\"], [\"placeholder\", \"\\u72C0\\u614B\\u7BE9\\u9078\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"active\"], [\"value\", \"inactive\"], [\"value\", \"disabled\"], [\"placeholder\", \"\\u6A13\\u5C64\\u7BE9\\u9078\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-2\"], [1, \"text-muted\", \"small\"], [1, \"row\", \"mt-4\"], [\"class\", \"col-md-4 mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"mb-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [\"class\", \"floor-container\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-container\"], [1, \"floor-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"font-weight-bold\"], [1, \"text-muted\"], [1, \"house-grid\"], [\"class\", \"house-item\", 3, \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"house-item\", 3, \"click\"], [1, \"house-number\"], [1, \"house-building\"], [1, \"house-status\"], [1, \"text-center\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"mb-3\"], [2, \"width\", \"600px\", \"max-height\", \"90vh\"], [1, \"form-group\"], [1, \"text-red-600\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-fill\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"mx-2\"], [1, \"selection-options\"], [3, \"ngModelChange\", \"ngModel\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"mt-2\"], [\"class\", \"floor-selection\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-selection\"], [\"class\", \"house-selection ml-4\", 4, \"ngIf\"], [1, \"house-selection\", \"ml-4\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"ml-3\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n      template: function SettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 9)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 10);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 11)(7, \"div\", 12)(8, \"div\", 13)(9, \"label\", 14);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵtemplate(12, SettingTimePeriodComponent_nb_option_12_Template, 2, 2, \"nb-option\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13)(15, \"label\", 17);\n          i0.ɵɵtext(16, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_17_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildingChange());\n          });\n          i0.ɵɵelementStart(18, \"nb-option\", 19);\n          i0.ɵɵtext(19, \"\\u5168\\u90E8\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, SettingTimePeriodComponent_nb_option_20_Template, 2, 2, \"nb-option\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 12)(22, \"div\", 20)(23, \"label\", 21);\n          i0.ɵɵtext(24, \"\\u958B\\u653E\\u65E5\\u671F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nb-form-field\", 22);\n          i0.ɵɵelement(26, \"nb-icon\", 23);\n          i0.ɵɵelementStart(27, \"input\", 24);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"nb-datepicker\", 25, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"span\", 26);\n          i0.ɵɵtext(31, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"nb-form-field\");\n          i0.ɵɵelement(33, \"nb-icon\", 23);\n          i0.ɵɵelementStart(34, \"input\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"nb-datepicker\", 25, 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 28)(38, \"div\", 13)(39, \"label\", 29);\n          i0.ɵɵtext(40, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 30)(42, \"input\", 31);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_42_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"label\", 32);\n          i0.ɵɵtext(44, \" \\u4F9D\\u958B\\u653E\\u6642\\u6BB5\\u986F\\u793A \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 30)(46, \"input\", 33);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_46_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"label\", 34);\n          i0.ɵɵtext(48, \" \\u4F9D\\u958B\\u653E\\u72C0\\u614B\\u986F\\u793A \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(49, \"div\", 35)(50, \"div\", 36)(51, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_51_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵtext(52, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(53, \"i\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_54_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openBatchSetting());\n          });\n          i0.ɵɵtext(55, \" \\u5168\\u90E8\\u6279\\u6B21\\u8A2D\\u5B9A \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(56, SettingTimePeriodComponent_div_56_Template, 24, 5, \"div\", 40)(57, SettingTimePeriodComponent_div_57_Template, 2, 1, \"div\", 41)(58, SettingTimePeriodComponent_div_58_Template, 7, 0, \"div\", 42);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(59, SettingTimePeriodComponent_ng_template_59_Template, 34, 7, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(61, SettingTimePeriodComponent_ng_template_61_Template, 4, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(63, SettingTimePeriodComponent_ng_template_63_Template, 29, 6, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const StartDate_r25 = i0.ɵɵreference(29);\n          const EndDate_r26 = i0.ɵɵreference(36);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingOptions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", StartDate_r25);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", EndDate_r26);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.buildingGroups.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.buildingGroups.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.buildingGroups.length === 0 && ctx.houseChangeDates.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, SharedModule, i9.DefaultValueAccessor, i9.RadioControlValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, RadioButtonModule, NbDatepickerModule, NbDateFnsDateModule, SettingTimeStatusPipe],\n      styles: [\".floor-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.floor-container[_ngcontent-%COMP%]   .floor-header[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-bottom: 1px solid #e0e0e0;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.house-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\\n  gap: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.house-item[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  padding: 0.5rem;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  background: white;\\n}\\n.house-item[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  border-color: #007bff;\\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);\\n  transform: translateY(-1px);\\n}\\n.house-item.disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n  cursor: not-allowed;\\n  opacity: 0.6;\\n}\\n.house-item[_ngcontent-%COMP%]   .house-number[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  margin-bottom: 0.1rem;\\n  font-size: 0.9rem;\\n}\\n.house-item[_ngcontent-%COMP%]   .house-building[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  margin-bottom: 0.25rem;\\n}\\n.house-item[_ngcontent-%COMP%]   .house-status[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 2px 4px;\\n  border-radius: 3px;\\n}\\n.house-item[_ngcontent-%COMP%]   .house-status.status-active[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #155724;\\n}\\n.house-item[_ngcontent-%COMP%]   .house-status.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  color: #856404;\\n}\\n.house-item[_ngcontent-%COMP%]   .house-status.status-expired[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n}\\n.house-item[_ngcontent-%COMP%]   .house-status.status-not-set[_ngcontent-%COMP%] {\\n  background-color: #e2e3e5;\\n  color: #383d41;\\n}\\n.house-item[_ngcontent-%COMP%]   .house-status.status-disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n}\\n\\n.search-enhanced[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  padding-left: 1rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\\n  gap: 0.25rem;\\n  margin-top: 0.5rem;\\n  padding: 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .house-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));\\n    gap: 0.25rem;\\n  }\\n  .house-item[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n  }\\n  .house-item[_ngcontent-%COMP%]   .house-number[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .house-item[_ngcontent-%COMP%]   .house-status[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n  .search-enhanced[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .house-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));\\n  }\\n  .col-md-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n}\\nnb-card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  margin-right: 0.5rem;\\n}\\n.status-indicator.active[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n}\\n.status-indicator.pending[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n}\\n.status-indicator.expired[_ngcontent-%COMP%] {\\n  background-color: #dc3545;\\n}\\n.status-indicator.not-set[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n}\\n.status-indicator.disabled[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "RadioButtonModule", "SettingTimeStatusPipe", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "building_r3", "floor_r6", "ɵɵelement", "ɵɵtwoWayListener", "SettingTimePeriodComponent_div_56_Template_input_ngModelChange_5_listener", "$event", "ɵɵrestoreView", "_r4", "ctx_r4", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "filterOptions", "searchKeyword", "ɵɵresetView", "ɵɵlistener", "onSearch", "SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_7_listener", "statusFilter", "SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_17_listener", "floorFilter", "ɵɵtemplate", "SettingTimePeriodComponent_div_56_nb_option_20_Template", "ɵɵtwoWayProperty", "availableFloors", "buildingGroups", "length", "SettingTimePeriodComponent_div_57_div_1_div_8_div_7_Template_div_click_0_listener", "house_r10", "_r9", "$implicit", "dialog_r11", "ɵɵreference", "openModel", "ɵɵclassProp", "CHouseId", "ɵɵtextInterpolate", "CHouseHold", "CBuildingName", "ɵɵclassMap", "getStatusClass", "ɵɵpipeBind3", "CChangeStartDate", "CChangeEndDate", "isStatus", "SettingTimePeriodComponent_div_57_div_1_div_8_div_7_Template", "floor_r12", "floorNumber", "houses", "SettingTimePeriodComponent_div_57_div_1_Template_button_click_5_listener", "building_r8", "_r7", "openBatchSetting", "SettingTimePeriodComponent_div_57_div_1_div_8_Template", "name", "floors", "SettingTimePeriodComponent_div_57_div_1_Template", "getFilteredBuildings", "SettingTimePeriodComponent_ng_template_59_div_28_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener", "house_r17", "_r16", "selected", "SettingTimePeriodComponent_ng_template_59_div_28_div_1_div_3_nb_checkbox_1_Template", "floor_r15", "SettingTimePeriodComponent_ng_template_59_div_28_div_1_Template_nb_checkbox_ngModelChange_1_listener", "_r14", "onFloorSelectionChange", "SettingTimePeriodComponent_ng_template_59_div_28_div_1_div_3_Template", "ɵɵtextInterpolate2", "SettingTimePeriodComponent_ng_template_59_div_28_div_1_Template", "selectedBuildingForBatch", "SettingTimePeriodComponent_ng_template_59_Template_input_ngModelChange_12_listener", "_r13", "batchSettings", "startDate", "SettingTimePeriodComponent_ng_template_59_Template_input_ngModelChange_19_listener", "endDate", "SettingTimePeriodComponent_ng_template_59_Template_nb_checkbox_ngModelChange_26_listener", "applyToAll", "SettingTimePeriodComponent_ng_template_59_div_28_Template", "SettingTimePeriodComponent_ng_template_59_Template_button_click_30_listener", "ref_r18", "dialogRef", "onClose", "SettingTimePeriodComponent_ng_template_59_Template_button_click_32_listener", "onBatchSubmit", "batchStartDate_r19", "batchEndDate_r20", "SettingTimePeriodComponent_ng_template_63_Template_input_ngModelChange_14_listener", "_r21", "selectedHouseChangeDate", "SettingTimePeriodComponent_ng_template_63_Template_input_ngModelChange_21_listener", "SettingTimePeriodComponent_ng_template_63_Template_button_click_25_listener", "ref_r22", "SettingTimePeriodComponent_ng_template_63_Template_button_click_27_listener", "onSubmit", "CFloor", "changeStartDate_r23", "changeEndDate_r24", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "buildingOptions", "selectedBuilding", "buildingFilter", "selectedBuildings", "selectedFloors", "selectedHouses", "undefined", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "searchQuery", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "validation", "errorMessages", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "for<PERSON>ach", "household", "CHouses", "house", "floor", "push", "sort", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "houseChangeDates", "convertedHouseArray", "buildBuildingGroups", "data", "buildingMap", "Map", "houseType", "buildingName", "has", "set", "floorMap", "get", "localeCompare", "bg", "updateAvailableFloors", "building", "onBuildingChange", "filter", "keyword", "toLowerCase", "hasMatchingHouse", "some", "includes", "hasMatchingStatus", "matchesStatusFilter", "parseInt", "hasMatchingFloor", "filteredBuilding", "hasValidHouses", "filteredFloor", "status", "getHouseStatus", "now", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "batchSettingDialog", "housesToUpdate", "openHouseDialog", "dialog", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "Router", "i7", "EventService", "selectors", "viewQuery", "SettingTimePeriodComponent_Query", "rf", "ctx", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingTimePeriodComponent_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "SettingTimePeriodComponent_nb_option_12_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_17_listener", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_17_listener", "SettingTimePeriodComponent_nb_option_20_Template", "SettingTimePeriodComponent_Template_input_ngModelChange_27_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_34_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_42_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_46_listener", "SettingTimePeriodComponent_Template_button_click_51_listener", "SettingTimePeriodComponent_Template_button_click_54_listener", "SettingTimePeriodComponent_div_56_Template", "SettingTimePeriodComponent_div_57_Template", "SettingTimePeriodComponent_div_58_Template", "SettingTimePeriodComponent_ng_template_59_Template", "ɵɵtemplateRefExtractor", "SettingTimePeriodComponent_ng_template_61_Template", "SettingTimePeriodComponent_ng_template_63_Template", "StartDate_r25", "EndDate_r26", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "RadioControlValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string; // 戶型\r\n  CBuildingName: string; // 棟別\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule, RadioButtonModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n\r\n  @ViewChild('batchSettingDialog') batchSettingDialog!: TemplateRef<any>;\r\n  @ViewChild('dialog') dialog!: TemplateRef<any>;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  isStatus: boolean = true;\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  // 新增的屬性\r\n  buildingGroups: BuildingGroup[] = [];\r\n  buildingOptions: string[] = [];\r\n  selectedBuilding: string = '';\r\n  availableFloors: number[] = [];\r\n\r\n  // 篩選和搜尋\r\n  filterOptions: FilterOptions = {\r\n    searchKeyword: '',\r\n    statusFilter: '',\r\n    floorFilter: '',\r\n    buildingFilter: ''\r\n  };\r\n\r\n  // 批次設定\r\n  batchSettings: BatchSettings = {\r\n    startDate: null,\r\n    endDate: null,\r\n    applyToAll: true,\r\n    selectedBuildings: [],\r\n    selectedFloors: [],\r\n    selectedHouses: []\r\n  };\r\n\r\n  selectedBuildingForBatch: BuildingGroup | null = null;\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CBuildingName: '未分類',\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n          // 新增：建立棟別分組資料\r\n          this.buildBuildingGroups(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\r\n  buildBuildingGroups(data: GetHouseChangeDateRes[]) {\r\n    const buildingMap = new Map<string, Map<number, ConvertedHouse[]>>();\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || ''; // 戶型\r\n\r\n      household.CHouses?.forEach(house => {\r\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\r\n        const floor = house.CFloor || 0;\r\n\r\n        if (!buildingMap.has(buildingName)) {\r\n          buildingMap.set(buildingName, new Map<number, ConvertedHouse[]>());\r\n        }\r\n\r\n        const floorMap = buildingMap.get(buildingName)!;\r\n\r\n        if (!floorMap.has(floor)) {\r\n          floorMap.set(floor, []);\r\n        }\r\n\r\n        floorMap.get(floor)!.push({\r\n          CHouseHold: houseType, // 戶型\r\n          CBuildingName: buildingName, // 棟別\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: floor,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 轉換為BuildingGroup格式\r\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\r\n      const floors: FloorGroup[] = Array.from(floorMap.entries())\r\n        .sort(([a], [b]) => b - a) // 樓層由高到低排序\r\n        .map(([floorNumber, houses]) => ({\r\n          floorNumber,\r\n          houses: houses.sort((a, b) => {\r\n            // 排序邏輯：先按戶型排序，再按樓層排序\r\n            if (a.CHouseHold !== b.CHouseHold) {\r\n              return a.CHouseHold.localeCompare(b.CHouseHold);\r\n            }\r\n            return a.CFloor - b.CFloor;\r\n          }),\r\n          selected: false\r\n        }));\r\n\r\n      return {\r\n        name: buildingName,\r\n        floors,\r\n        selected: false\r\n      };\r\n    }).sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    // 更新棟別選項和可用樓層\r\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\r\n    this.updateAvailableFloors();\r\n  }\r\n\r\n  // 新增：更新可用樓層\r\n  updateAvailableFloors() {\r\n    const floorsSet = new Set<number>();\r\n\r\n    this.buildingGroups.forEach(building => {\r\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\r\n        building.floors.forEach(floor => {\r\n          floorsSet.add(floor.floorNumber);\r\n        });\r\n      }\r\n    });\r\n\r\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\r\n  }\r\n\r\n  // 新增：棟別選擇變更處理\r\n  onBuildingChange() {\r\n    this.updateAvailableFloors();\r\n    this.filterOptions.buildingFilter = this.selectedBuilding;\r\n  }\r\n\r\n  // 新增：搜尋處理\r\n  onSearch() {\r\n    // 搜尋功能將在HTML模板中使用管道或過濾器實現\r\n  }\r\n\r\n  // 新增：取得過濾後的棟別資料\r\n  getFilteredBuildings(): BuildingGroup[] {\r\n    return this.buildingGroups.filter(building => {\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 關鍵字搜尋 (搜尋戶型)\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        const hasMatchingHouse = building.floors.some(floor =>\r\n          floor.houses.some(house =>\r\n            house.CHouseHold.toLowerCase().includes(keyword) ||\r\n            house.CBuildingName.toLowerCase().includes(keyword)\r\n          )\r\n        );\r\n        if (!hasMatchingHouse) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        const hasMatchingStatus = building.floors.some(floor =>\r\n          floor.houses.some(house => this.matchesStatusFilter(house))\r\n        );\r\n        if (!hasMatchingStatus) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        const hasMatchingFloor = building.floors.some(floor =>\r\n          floor.floorNumber === floorNumber\r\n        );\r\n        if (!hasMatchingFloor) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }).map(building => {\r\n      // 對每個棟別，也要篩選其樓層和戶別\r\n      const filteredBuilding = { ...building };\r\n      filteredBuilding.floors = building.floors.filter(floor => {\r\n        // 樓層篩選\r\n        if (this.filterOptions.floorFilter) {\r\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n          if (floor.floorNumber !== floorNumber) {\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 檢查該樓層是否有符合條件的戶別\r\n        const hasValidHouses = floor.houses.some(house => {\r\n          // 關鍵字篩選\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return hasValidHouses;\r\n      }).map(floor => {\r\n        // 篩選戶別\r\n        const filteredFloor = { ...floor };\r\n        filteredFloor.houses = floor.houses.filter(house => {\r\n          // 關鍵字篩選\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return filteredFloor;\r\n      });\r\n\r\n      return filteredBuilding;\r\n    });\r\n  }\r\n\r\n  // 新增：檢查戶別是否符合狀態篩選\r\n  private matchesStatusFilter(house: ConvertedHouse): boolean {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (this.filterOptions.statusFilter) {\r\n      case 'active':\r\n        return status === 'active';\r\n      case 'inactive':\r\n        return status === 'not-set';\r\n      case 'disabled':\r\n        return status === 'disabled';\r\n      default:\r\n        return true;\r\n    }\r\n  }\r\n\r\n  // 新增：取得戶別狀態\r\n  private getHouseStatus(house: ConvertedHouse): string {\r\n    if (!house.CHouseId) {\r\n      return 'disabled';\r\n    }\r\n\r\n    if (house.CChangeStartDate && house.CChangeEndDate) {\r\n      const now = new Date();\r\n      const startDate = new Date(house.CChangeStartDate);\r\n      const endDate = new Date(house.CChangeEndDate);\r\n\r\n      if (now < startDate) {\r\n        return 'pending';\r\n      } else if (now >= startDate && now <= endDate) {\r\n        return 'active';\r\n      } else {\r\n        return 'expired';\r\n      }\r\n    }\r\n\r\n    return 'not-set';\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n\r\n  // 新增：開啟批次設定對話框\r\n  openBatchSetting(building?: BuildingGroup) {\r\n    this.selectedBuildingForBatch = building || null;\r\n    this.batchSettings = {\r\n      startDate: null,\r\n      endDate: null,\r\n      applyToAll: !building,\r\n      selectedBuildings: building ? [building.name] : [],\r\n      selectedFloors: [],\r\n      selectedHouses: []\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    if (building) {\r\n      building.floors.forEach(floor => {\r\n        floor.selected = false;\r\n        floor.houses.forEach(house => house.selected = false);\r\n      });\r\n    }\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(this.batchSettingDialog);\r\n  }\r\n\r\n  // 新增：樓層選擇變更處理\r\n  onFloorSelectionChange(floor: FloorGroup) {\r\n    if (floor.selected) {\r\n      floor.houses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          house.selected = true;\r\n        }\r\n      });\r\n    } else {\r\n      floor.houses.forEach(house => house.selected = false);\r\n    }\r\n  }\r\n\r\n  // 新增：批次提交\r\n  onBatchSubmit(ref: any) {\r\n    // 驗證批次設定\r\n    this.valid.clear();\r\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\r\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 收集要更新的房屋\r\n    const housesToUpdate: any[] = [];\r\n\r\n    if (this.batchSettings.applyToAll) {\r\n      // 全部戶別\r\n      this.buildingGroups.forEach(building => {\r\n        building.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      });\r\n    } else {\r\n      // 選擇的戶別\r\n      if (this.selectedBuildingForBatch) {\r\n        this.selectedBuildingForBatch.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.selected && house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n\r\n    if (housesToUpdate.length === 0) {\r\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\r\n      return;\r\n    }\r\n\r\n    // 調用API進行批次更新\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: housesToUpdate\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\r\n        this.getHouseChangeDate();\r\n        ref.close();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：取得狀態樣式類別\r\n  getStatusClass(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    return `status-${status}`;\r\n  }\r\n\r\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\r\n  openHouseDialog(house: ConvertedHouse) {\r\n    if (house.CHouseId) {\r\n      // 使用現有的openModel方法\r\n      this.openModel(this.dialog, house);\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"getHouseChangeDate()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingSelect\" class=\"label col-3\">棟別</label>\r\n          <nb-select placeholder=\"請選擇棟別\" [(ngModel)]=\"selectedBuilding\" class=\"col-9\"\r\n            (selectedChange)=\"onBuildingChange()\">\r\n            <nb-option value=\"\">全部棟別</nb-option>\r\n            <nb-option *ngFor=\"let building of buildingOptions\" [value]=\"building\">\r\n              {{ building }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">開放日期\r\n          </label>\r\n          <nb-form-field class=\"ml-1\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"StartDate\" class=\"w-full\"\r\n              [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n            <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <span class=\"mx-1\">~</span>\r\n          <nb-form-field>\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"EndDate\" class=\"w-full\"\r\n              [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n            <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-7 max-sm:col-md-12\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cSignStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <div class=\"form-check mx-2\">\r\n            <input class=\"form-check-input\" type=\"radio\" name=\"flexRadioDefault\" id=\"flexRadioDefault1\" [value]=\"true\"\r\n              [(ngModel)]=\"isStatus\">\r\n            <label class=\"form-check-label\" for=\"flexRadioDefault1\">\r\n              依開放時段顯示\r\n            </label>\r\n          </div>\r\n          <div class=\"form-check mx-2\">\r\n            <input class=\"form-check-input\" type=\"radio\" name=\"flexRadioDefault\" id=\"flexRadioDefault2\" [value]=\"false\"\r\n              [(ngModel)]=\"isStatus\">\r\n            <label class=\"form-check-label\" for=\"flexRadioDefault2\">\r\n              依開放狀態顯示\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-5 max-sm:col-md-12 \">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"getHouseChangeDate()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-info btn-sm\" (click)=\"openBatchSetting()\">\r\n            全部批次設定\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 增強的搜尋和篩選區域 -->\r\n    <div class=\"search-enhanced mt-3\" *ngIf=\"buildingGroups.length > 0\">\r\n      <div class=\"row\">\r\n        <div class=\"col-md-4\">\r\n          <nb-form-field>\r\n            <nb-icon nbPrefix icon=\"search-outline\"></nb-icon>\r\n            <input nbInput placeholder=\"搜尋戶別...\" [(ngModel)]=\"filterOptions.searchKeyword\" (ngModelChange)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n          <nb-select placeholder=\"狀態篩選\" [(ngModel)]=\"filterOptions.statusFilter\">\r\n            <nb-option value=\"\">全部狀態</nb-option>\r\n            <nb-option value=\"active\">已設定</nb-option>\r\n            <nb-option value=\"inactive\">未設定</nb-option>\r\n            <nb-option value=\"disabled\">已停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n          <nb-select placeholder=\"樓層篩選\" [(ngModel)]=\"filterOptions.floorFilter\">\r\n            <nb-option value=\"\">全部樓層</nb-option>\r\n            <nb-option *ngFor=\"let floor of availableFloors\" [value]=\"floor\">\r\n              {{ floor }}F\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"col-md-2\">\r\n          <div class=\"text-muted small\">\r\n            共 {{ buildingGroups.length }} 個棟別\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 卡片式顯示區域 -->\r\n    <div class=\"row mt-4\" *ngIf=\"buildingGroups.length > 0\">\r\n      <div class=\"col-md-4 mb-3\" *ngFor=\"let building of getFilteredBuildings()\">\r\n        <nb-card>\r\n          <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n            <h6 class=\"mb-0\">{{ building.name }}</h6>\r\n            <button class=\"btn btn-sm btn-outline-primary\" (click)=\"openBatchSetting(building)\">\r\n              批次設定\r\n            </button>\r\n          </nb-card-header>\r\n          <nb-card-body>\r\n            <div class=\"floor-container\" *ngFor=\"let floor of building.floors\">\r\n              <div class=\"floor-header d-flex justify-content-between align-items-center mb-2\">\r\n                <span class=\"font-weight-bold\">{{ floor.floorNumber }}F</span>\r\n                <span class=\"text-muted\">{{ floor.houses.length }} 戶</span>\r\n              </div>\r\n              <div class=\"house-grid\">\r\n                <div class=\"house-item\" *ngFor=\"let house of floor.houses\" [class.disabled]=\"!house.CHouseId\"\r\n                  (click)=\"openModel(dialog, house)\">\r\n                  <div class=\"house-number\">{{ house.CHouseHold }}</div>\r\n                  <div class=\"house-building\">{{ house.CBuildingName }}</div>\r\n                  <div class=\"house-status\" [class]=\"getStatusClass(house)\">\r\n                    {{ house.CHouseId ? (house.CChangeStartDate | getSettingTimeStatus:house.CChangeEndDate:isStatus) :\r\n                    '已停用' }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </nb-card-body>\r\n        </nb-card>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 當沒有資料時顯示 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"buildingGroups.length === 0 && houseChangeDates.length === 0\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <i class=\"fas fa-info-circle fa-2x mb-3\"></i>\r\n            <p>請選擇建案後查詢資料</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 批次設定對話框 -->\r\n<ng-template #batchSettingDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:600px; max-height: 90vh\">\r\n    <nb-card-header>\r\n      批次設定 - {{ selectedBuildingForBatch?.name || '全部' }}\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 時間設定 -->\r\n      <div class=\"form-group\">\r\n        <label>開放時間起訖 <span class=\"text-red-600\">*</span></label>\r\n        <div class=\"d-flex align-items-center\">\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchStartDate\"\r\n              [(ngModel)]=\"batchSettings.startDate\">\r\n            <nb-datepicker #batchStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <span class=\"mx-2\">~</span>\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchEndDate\"\r\n              [(ngModel)]=\"batchSettings.endDate\">\r\n            <nb-datepicker #batchEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 選擇範圍 -->\r\n      <div class=\"form-group\">\r\n        <label>適用範圍</label>\r\n        <div class=\"selection-options\">\r\n          <nb-checkbox [(ngModel)]=\"batchSettings.applyToAll\">\r\n            全部戶別\r\n          </nb-checkbox>\r\n          <div class=\"mt-2\" *ngIf=\"!batchSettings.applyToAll && selectedBuildingForBatch\">\r\n            <div class=\"floor-selection\" *ngFor=\"let floor of selectedBuildingForBatch.floors\">\r\n              <nb-checkbox [(ngModel)]=\"floor.selected\" (ngModelChange)=\"onFloorSelectionChange(floor)\">\r\n                {{ floor.floorNumber }}F ({{ floor.houses.length }} 戶)\r\n              </nb-checkbox>\r\n              <div class=\"house-selection ml-4\" *ngIf=\"floor.selected\">\r\n                <nb-checkbox *ngFor=\"let house of floor.houses\" [(ngModel)]=\"house.selected\"\r\n                  [disabled]=\"!house.CHouseId\">\r\n                  {{ house.CHouseHold }}\r\n                </nb-checkbox>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-end\">\r\n      <button class=\"btn btn-secondary me-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onBatchSubmit(ref)\">批次設定</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      您正在編輯 <span class=\"text-green-600\">{{selectedHouseChangeDate.CHouseHold}} -\r\n        {{selectedHouseChangeDate.CFloor}}F</span> 的選樣開放時段\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cFloorFrom\" class=\"label col-3\">開放時間起訖<span class=\"text-red-600\">*</span>\r\n        </label>\r\n        <nb-form-field class=\"ml-3\">\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeStartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeStartDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeStartDate\">\r\n          <nb-datepicker #changeStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n        <label for=\"CChangeStartDate\" class=\"label col-1\"> ~\r\n        </label>\r\n        <nb-form-field>\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeEndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeEndDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeEndDate\">\r\n          <nb-datepicker #changeEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AAMvD,SAASC,qBAAqB,QAAQ,mCAAmC;AAEzE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;;;ICJxEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IAUAR,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAK,WAAA,CAAkB;IACpET,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,WAAA,MACF;;;;;IA6EAT,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAM,QAAA,CAAe;IAC9DV,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAG,QAAA,OACF;;;;;;IAlBFV,EAHN,CAAAC,cAAA,cAAoE,cACjD,cACO,oBACL;IACbD,EAAA,CAAAW,SAAA,kBAAkD;IAClDX,EAAA,CAAAC,cAAA,gBAA4G;IAAvED,EAAA,CAAAY,gBAAA,2BAAAC,0EAAAC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAC,aAAA,EAAAP,MAAA,MAAAG,MAAA,CAAAG,aAAA,CAAAC,aAAA,GAAAP,MAAA;MAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;IAAA,EAAyC;IAACd,EAAA,CAAAuB,UAAA,2BAAAV,0EAAA;MAAAb,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAsB,WAAA,CAAiBL,MAAA,CAAAO,QAAA,EAAU;IAAA,EAAC;IAE/GxB,EAFI,CAAAG,YAAA,EAA4G,EAC9F,EACZ;IAEJH,EADF,CAAAC,cAAA,cAAsB,oBACmD;IAAzCD,EAAA,CAAAY,gBAAA,2BAAAa,8EAAAX,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAM,YAAA,EAAAZ,MAAA,MAAAG,MAAA,CAAAG,aAAA,CAAAM,YAAA,GAAAZ,MAAA;MAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IACpEd,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,qBAA0B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAA4B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAA4B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAEJH,EADF,CAAAC,cAAA,eAAsB,qBACkD;IAAxCD,EAAA,CAAAY,gBAAA,2BAAAe,+EAAAb,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAQ,WAAA,EAAAd,MAAA,MAAAG,MAAA,CAAAG,aAAA,CAAAQ,WAAA,GAAAd,MAAA;MAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;IAAA,EAAuC;IACnEd,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAA6B,UAAA,KAAAC,uDAAA,wBAAiE;IAIrE9B,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,eAAsB,eACU;IAC5BD,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IAzBuCH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAA+B,gBAAA,YAAAd,MAAA,CAAAG,aAAA,CAAAC,aAAA,CAAyC;IAIlDrB,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAA+B,gBAAA,YAAAd,MAAA,CAAAG,aAAA,CAAAM,YAAA,CAAwC;IAQxC1B,EAAA,CAAAM,SAAA,IAAuC;IAAvCN,EAAA,CAAA+B,gBAAA,YAAAd,MAAA,CAAAG,aAAA,CAAAQ,WAAA,CAAuC;IAEtC5B,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAe,eAAA,CAAkB;IAO/ChC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,aAAAU,MAAA,CAAAgB,cAAA,CAAAC,MAAA,yBACF;;;;;;IAsBMlC,EAAA,CAAAC,cAAA,cACqC;IAAnCD,EAAA,CAAAuB,UAAA,mBAAAY,kFAAA;MAAA,MAAAC,SAAA,GAAApC,EAAA,CAAAe,aAAA,CAAAsB,GAAA,EAAAC,SAAA;MAAA,MAAArB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAqB,UAAA,GAAAvC,EAAA,CAAAwC,WAAA;MAAA,OAAAxC,EAAA,CAAAsB,WAAA,CAASL,MAAA,CAAAwB,SAAA,CAAAF,UAAA,EAAAH,SAAA,CAAwB;IAAA,EAAC;IAClCpC,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtDH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3DH,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,MAAA,GAEF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IARqDH,EAAA,CAAA0C,WAAA,cAAAN,SAAA,CAAAO,QAAA,CAAkC;IAEjE3C,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAA4C,iBAAA,CAAAR,SAAA,CAAAS,UAAA,CAAsB;IACpB7C,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA4C,iBAAA,CAAAR,SAAA,CAAAU,aAAA,CAAyB;IAC3B9C,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAA+C,UAAA,CAAA9B,MAAA,CAAA+B,cAAA,CAAAZ,SAAA,EAA+B;IACvDpC,EAAA,CAAAM,SAAA,EAEF;IAFEN,EAAA,CAAAO,kBAAA,MAAA6B,SAAA,CAAAO,QAAA,GAAA3C,EAAA,CAAAiD,WAAA,OAAAb,SAAA,CAAAc,gBAAA,EAAAd,SAAA,CAAAe,cAAA,EAAAlC,MAAA,CAAAmC,QAAA,8BAEF;;;;;IAXFpD,EAFJ,CAAAC,cAAA,cAAmE,cACgB,eAChD;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IACtDF,EADsD,CAAAG,YAAA,EAAO,EACvD;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA6B,UAAA,IAAAwB,4DAAA,mBACqC;IASzCrD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAd6BH,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAO,kBAAA,KAAA+C,SAAA,CAAAC,WAAA,MAAwB;IAC9BvD,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAO,kBAAA,KAAA+C,SAAA,CAAAE,MAAA,CAAAtB,MAAA,YAA2B;IAGVlC,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAI,UAAA,YAAAkD,SAAA,CAAAE,MAAA,CAAe;;;;;;IAZ7DxD,EAHN,CAAAC,cAAA,cAA2E,cAChE,yBACmE,aACvD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,iBAAoF;IAArCD,EAAA,CAAAuB,UAAA,mBAAAkC,yEAAA;MAAA,MAAAC,WAAA,GAAA1D,EAAA,CAAAe,aAAA,CAAA4C,GAAA,EAAArB,SAAA;MAAA,MAAArB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAsB,WAAA,CAASL,MAAA,CAAA2C,gBAAA,CAAAF,WAAA,CAA0B;IAAA,EAAC;IACjF1D,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACM;IACjBH,EAAA,CAAAC,cAAA,mBAAc;IACZD,EAAA,CAAA6B,UAAA,IAAAgC,sDAAA,kBAAmE;IAmBzE7D,EAFI,CAAAG,YAAA,EAAe,EACP,EACN;;;;IAzBiBH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAA4C,iBAAA,CAAAc,WAAA,CAAAI,IAAA,CAAmB;IAMW9D,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,YAAAsD,WAAA,CAAAK,MAAA,CAAkB;;;;;IAVzE/D,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAA6B,UAAA,IAAAmC,gDAAA,kBAA2E;IA6B7EhE,EAAA,CAAAG,YAAA,EAAM;;;;IA7B4CH,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAgD,oBAAA,GAAyB;;;;;IAmCrEjE,EAHN,CAAAC,cAAA,cAAmG,cACxF,mBACO,cACY;IACtBD,EAAA,CAAAW,SAAA,YAA6C;IAC7CX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IAIrBF,EAJqB,CAAAG,YAAA,EAAI,EACb,EACO,EACP,EACN;;;;;;IA4CMH,EAAA,CAAAC,cAAA,sBAC+B;IADiBD,EAAA,CAAAY,gBAAA,2BAAAsD,yHAAApD,MAAA;MAAA,MAAAqD,SAAA,GAAAnE,EAAA,CAAAe,aAAA,CAAAqD,IAAA,EAAA9B,SAAA;MAAAtC,EAAA,CAAAmB,kBAAA,CAAAgD,SAAA,CAAAE,QAAA,EAAAvD,MAAA,MAAAqD,SAAA,CAAAE,QAAA,GAAAvD,MAAA;MAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;IAAA,EAA4B;IAE1Ed,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;;;;IAHkCH,EAAA,CAAA+B,gBAAA,YAAAoC,SAAA,CAAAE,QAAA,CAA4B;IAC1ErE,EAAA,CAAAI,UAAA,cAAA+D,SAAA,CAAAxB,QAAA,CAA4B;IAC5B3C,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAA4D,SAAA,CAAAtB,UAAA,MACF;;;;;IAJF7C,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAA6B,UAAA,IAAAyC,mFAAA,0BAC+B;IAGjCtE,EAAA,CAAAG,YAAA,EAAM;;;;IAJ2BH,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAI,UAAA,YAAAmE,SAAA,CAAAf,MAAA,CAAe;;;;;;IAJhDxD,EADF,CAAAC,cAAA,cAAmF,sBACS;IAA7ED,EAAA,CAAAY,gBAAA,2BAAA4D,qGAAA1D,MAAA;MAAA,MAAAyD,SAAA,GAAAvE,EAAA,CAAAe,aAAA,CAAA0D,IAAA,EAAAnC,SAAA;MAAAtC,EAAA,CAAAmB,kBAAA,CAAAoD,SAAA,CAAAF,QAAA,EAAAvD,MAAA,MAAAyD,SAAA,CAAAF,QAAA,GAAAvD,MAAA;MAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;IAAA,EAA4B;IAACd,EAAA,CAAAuB,UAAA,2BAAAiD,qGAAA;MAAA,MAAAD,SAAA,GAAAvE,EAAA,CAAAe,aAAA,CAAA0D,IAAA,EAAAnC,SAAA;MAAA,MAAArB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAsB,WAAA,CAAiBL,MAAA,CAAAyD,sBAAA,CAAAH,SAAA,CAA6B;IAAA,EAAC;IACvFvE,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAA6B,UAAA,IAAA8C,qEAAA,kBAAyD;IAM3D3E,EAAA,CAAAG,YAAA,EAAM;;;;IATSH,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAA+B,gBAAA,YAAAwC,SAAA,CAAAF,QAAA,CAA4B;IACvCrE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAA4E,kBAAA,MAAAL,SAAA,CAAAhB,WAAA,SAAAgB,SAAA,CAAAf,MAAA,CAAAtB,MAAA,cACF;IACmClC,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAmE,SAAA,CAAAF,QAAA,CAAoB;;;;;IAL3DrE,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAA6B,UAAA,IAAAgD,+DAAA,kBAAmF;IAWrF7E,EAAA,CAAAG,YAAA,EAAM;;;;IAX2CH,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA6D,wBAAA,CAAAf,MAAA,CAAkC;;;;;;IAhCzF/D,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAIbH,EAHJ,CAAAC,cAAA,mBAAc,cAEY,YACf;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAEvDH,EADF,CAAAC,cAAA,cAAuC,yBACJ;IAC/BD,EAAA,CAAAW,SAAA,mBAAoD;IACpDX,EAAA,CAAAC,cAAA,iBACwC;IAAtCD,EAAA,CAAAY,gBAAA,2BAAAmE,mFAAAjE,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAF,MAAA,CAAAgE,aAAA,CAAAC,SAAA,EAAApE,MAAA,MAAAG,MAAA,CAAAgE,aAAA,CAAAC,SAAA,GAAApE,MAAA;MAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IADvCd,EAAA,CAAAG,YAAA,EACwC;IACxCH,EAAA,CAAAW,SAAA,4BAAmE;IACrEX,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,yBAAiC;IAC/BD,EAAA,CAAAW,SAAA,mBAAoD;IACpDX,EAAA,CAAAC,cAAA,iBACsC;IAApCD,EAAA,CAAAY,gBAAA,2BAAAuE,mFAAArE,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAF,MAAA,CAAAgE,aAAA,CAAAG,OAAA,EAAAtE,MAAA,MAAAG,MAAA,CAAAgE,aAAA,CAAAG,OAAA,GAAAtE,MAAA;MAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IADrCd,EAAA,CAAAG,YAAA,EACsC;IACtCH,EAAA,CAAAW,SAAA,4BAAiE;IAGvEX,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;IAIJH,EADF,CAAAC,cAAA,eAAwB,aACf;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEjBH,EADF,CAAAC,cAAA,eAA+B,uBACuB;IAAvCD,EAAA,CAAAY,gBAAA,2BAAAyE,yFAAAvE,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAF,MAAA,CAAAgE,aAAA,CAAAK,UAAA,EAAAxE,MAAA,MAAAG,MAAA,CAAAgE,aAAA,CAAAK,UAAA,GAAAxE,MAAA;MAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;IAAA,EAAsC;IACjDd,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAA6B,UAAA,KAAA0D,yDAAA,kBAAgF;IAetFvF,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAmD,kBACa;IAAvBD,EAAA,CAAAuB,UAAA,mBAAAiE,4EAAA;MAAA,MAAAC,OAAA,GAAAzF,EAAA,CAAAe,aAAA,CAAAiE,IAAA,EAAAU,SAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAsB,WAAA,CAASL,MAAA,CAAA0E,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAACzF,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzEH,EAAA,CAAAC,cAAA,kBAA6D;IAA7BD,EAAA,CAAAuB,UAAA,mBAAAqE,4EAAA;MAAA,MAAAH,OAAA,GAAAzF,EAAA,CAAAe,aAAA,CAAAiE,IAAA,EAAAU,SAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAsB,WAAA,CAASL,MAAA,CAAA4E,aAAA,CAAAJ,OAAA,CAAkB;IAAA,EAAC;IAACzF,EAAA,CAAAE,MAAA,gCAAI;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EAC3D,EACT;;;;;;IAlDNH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,kCAAAU,MAAA,CAAA6D,wBAAA,kBAAA7D,MAAA,CAAA6D,wBAAA,CAAAhB,IAAA,yBACF;IAQuD9D,EAAA,CAAAM,SAAA,IAA+B;IAA/BN,EAAA,CAAAI,UAAA,iBAAA0F,kBAAA,CAA+B;IAC5E9F,EAAA,CAAA+B,gBAAA,YAAAd,MAAA,CAAAgE,aAAA,CAAAC,SAAA,CAAqC;IAMQlF,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,iBAAA2F,gBAAA,CAA6B;IAC1E/F,EAAA,CAAA+B,gBAAA,YAAAd,MAAA,CAAAgE,aAAA,CAAAG,OAAA,CAAmC;IAU1BpF,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAA+B,gBAAA,YAAAd,MAAA,CAAAgE,aAAA,CAAAK,UAAA,CAAsC;IAGhCtF,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAI,UAAA,UAAAa,MAAA,CAAAgE,aAAA,CAAAK,UAAA,IAAArE,MAAA,CAAA6D,wBAAA,CAA2D;;;;;IAwBtF9E,EAAA,CAAAC,cAAA,kBAA+C;IAM7CD,EALA,CAAAW,SAAA,qBACiB,mBAGF,yBAGE;IACnBX,EAAA,CAAAG,YAAA,EAAU;;;;;;IAKRH,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GACE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mDAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,uBAA2B,cACyB,gBACJ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC7E;IACRH,EAAA,CAAAC,cAAA,yBAA4B;IAC1BD,EAAA,CAAAW,SAAA,mBAAoD;IACpDX,EAAA,CAAAC,cAAA,kBAC8E;IAAvDD,EAAA,CAAAY,gBAAA,2BAAAoF,mFAAAlF,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAF,MAAA,CAAAiF,uBAAA,CAAAhD,gBAAA,EAAApC,MAAA,MAAAG,MAAA,CAAAiF,uBAAA,CAAAhD,gBAAA,GAAApC,MAAA;MAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;IAAA,EAAsD;IAD7Ed,EAAA,CAAAG,YAAA,EAC8E;IAC9EH,EAAA,CAAAW,SAAA,4BAAoE;IACtEX,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,kBAAkD;IAACD,EAAA,CAAAE,MAAA,WACnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAW,SAAA,mBAAoD;IACpDX,EAAA,CAAAC,cAAA,kBAC4E;IAArDD,EAAA,CAAAY,gBAAA,2BAAAuF,mFAAArF,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAF,MAAA,CAAAiF,uBAAA,CAAA/C,cAAA,EAAArC,MAAA,MAAAG,MAAA,CAAAiF,uBAAA,CAAA/C,cAAA,GAAArC,MAAA;MAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;IAAA,EAAoD;IAD3Ed,EAAA,CAAAG,YAAA,EAC4E;IAC5EH,EAAA,CAAAW,SAAA,4BAAkE;IAGxEX,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACiB;IAAvBD,EAAA,CAAAuB,UAAA,mBAAA6E,4EAAA;MAAA,MAAAC,OAAA,GAAArG,EAAA,CAAAe,aAAA,CAAAkF,IAAA,EAAAP,SAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAsB,WAAA,CAASL,MAAA,CAAA0E,OAAA,CAAAU,OAAA,CAAY;IAAA,EAAC;IAACrG,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,mBAAoE;IAAxBD,EAAA,CAAAuB,UAAA,mBAAA+E,4EAAA;MAAA,MAAAD,OAAA,GAAArG,EAAA,CAAAe,aAAA,CAAAkF,IAAA,EAAAP,SAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAsB,WAAA,CAASL,MAAA,CAAAsF,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IAACrG,EAAA,CAAAE,MAAA,oBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAChE,EACT;;;;;;IA3B6BH,EAAA,CAAAM,SAAA,GACE;IADFN,EAAA,CAAA4E,kBAAA,KAAA3D,MAAA,CAAAiF,uBAAA,CAAArD,UAAA,SAAA5B,MAAA,CAAAiF,uBAAA,CAAAM,MAAA,MACE;IAQoCxG,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,iBAAAqG,mBAAA,CAAgC;IAC9EzG,EAAA,CAAA+B,gBAAA,YAAAd,MAAA,CAAAiF,uBAAA,CAAAhD,gBAAA,CAAsD;IAOVlD,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAI,UAAA,iBAAAsG,iBAAA,CAA8B;IAC1E1G,EAAA,CAAA+B,gBAAA,YAAAd,MAAA,CAAAiF,uBAAA,CAAA/C,cAAA,CAAoD;;;ADjLrF,OAAM,MAAOwD,0BAA2B,SAAQ7G,aAAa;EAK3D8G,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA6C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAItD,KAAApE,QAAQ,GAAY,IAAI;IAOxB;IACA,KAAAnB,cAAc,GAAoB,EAAE;IACpC,KAAAwF,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAA1F,eAAe,GAAa,EAAE;IAE9B;IACA,KAAAZ,aAAa,GAAkB;MAC7BC,aAAa,EAAE,EAAE;MACjBK,YAAY,EAAE,EAAE;MAChBE,WAAW,EAAE,EAAE;MACf+F,cAAc,EAAE;KACjB;IAED;IACA,KAAA1C,aAAa,GAAkB;MAC7BC,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbE,UAAU,EAAE,IAAI;MAChBsC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;KACjB;IAED,KAAAhD,wBAAwB,GAAyB,IAAI;IA/DnD,IAAI,CAACoB,uBAAuB,GAAG;MAC7BhD,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBqD,MAAM,EAAEuB,SAAS;MACjBlF,UAAU,EAAE,EAAE;MACdF,QAAQ,EAAEoF;KACX;IAED,IAAI,CAACX,aAAa,CAACY,OAAO,EAAE,CAACC,IAAI,CAC/B1I,GAAG,CAAE2I,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAACd,eAAe,GAAGa,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAkDSC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAACnB,gBAAgB,CAAC,CAAC,CAAC;MAC/CpE,gBAAgB,EAAE6E,SAAS;MAC3B5E,cAAc,EAAE4E;KACjB;IACD,IAAI,CAACW,gBAAgB,EAAE;EACzB;EAEAjG,SAASA,CAACkG,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACjG,QAAQ,EAAE;MACjB,IAAI,CAACuD,uBAAuB,GAAG;QAC7B,GAAG0C,IAAI;QACP1F,gBAAgB,EAAE0F,IAAI,CAAC1F,gBAAgB,GAAG,IAAI2F,IAAI,CAACD,IAAI,CAAC1F,gBAAgB,CAAC,GAAG6E,SAAS;QACrF5E,cAAc,EAAEyF,IAAI,CAACzF,cAAc,GAAG,IAAI0F,IAAI,CAACD,IAAI,CAACzF,cAAc,CAAC,GAAG4E;OACvE;MACD,IAAI,CAACjB,aAAa,CAACgC,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOpJ,MAAM,CAACoJ,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEA1C,QAAQA,CAACoC,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAAClC,KAAK,CAACmC,aAAa,CAACjH,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC6E,OAAO,CAACqC,aAAa,CAAC,IAAI,CAACpC,KAAK,CAACmC,aAAa,CAAC;MACpD;IACF;IACA,MAAME,KAAK,GAAG;MACZ1G,QAAQ,EAAE,IAAI,CAACuD,uBAAuB,CAACvD,QAAQ;MAC/CO,gBAAgB,EAAE,IAAI,CAAC6F,UAAU,CAAC,IAAI,CAAC7C,uBAAuB,CAAChD,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAAC4F,UAAU,CAAC,IAAI,CAAC7C,uBAAuB,CAAC/C,cAAc;KAC5E;IAED,IAAI,CAAC8D,aAAa,CAACqC,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAAChB,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACzC,OAAO,CAAC0C,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAjB,gBAAgBA,CAAA;IACd,IAAI,CAACxB,iBAAiB,CAAC0C,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAACtB,IAAI,CAC7E1I,GAAG,CAAC2I,GAAG,IAAG;MACR,MAAM2B,OAAO,GAAG3B,GAAG,CAAC4B,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAAC3H,MAAM,IAAIgG,GAAG,CAACsB,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChDzJ,cAAc,EAAEyJ,KAAK,CAACzJ,cAAc;UACpC0J,GAAG,EAAED,KAAK,CAACC;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAAC7C,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAI8C,KAAK,GAAG,IAAI,CAACJ,oBAAoB,CAACK,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAAC7C,eAAe,CAAC;UAC1F,IAAI,CAACkB,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuB,oBAAoB,CAACI,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAAC5B,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuB,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMO,WAAW,GAAG,IAAI,CAAC/B,WAAW,EAAEC,kBAAkB,EAAE0B,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACZ,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAACrB,SAAS,EAAE;EACf;EAEAkC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvC,MAAMC,KAAK,GAAGD,KAAK,CAACrE,MAAM;QAC1B,IAAI,CAACiE,SAAS,CAACK,KAAK,CAAC,EAAE;UAAE;UACvBL,SAAS,CAACK,KAAK,CAAC,GAAG,EAAE;QACvB;QACAL,SAAS,CAACK,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBlI,UAAU,EAAE8H,SAAS,CAAC9H,UAAU;UAChCC,aAAa,EAAE+H,KAAK,CAAC/H,aAAa,IAAI,KAAK;UAC3CH,QAAQ,EAAEkI,KAAK,CAAClI,QAAQ;UACxB6D,MAAM,EAAEqE,KAAK,CAACrE,MAAM;UACpBtD,gBAAgB,EAAE2H,KAAK,CAAC3H,gBAAgB;UACxCC,cAAc,EAAE0H,KAAK,CAAC1H;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACY,MAAM,CAACiH,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAACpH,MAAM,CAACiG,GAAG,CAAEc,KAAU,IAAI;MAChE,OAAO,IAAI,CAACM,UAAU,CAACpB,GAAG,CAAEW,SAAc,IAAI;QAC5C,MAAME,KAAK,GAAGJ,SAAS,CAACK,KAAK,CAAC,CAACO,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAACzI,UAAU,KAAK8H,SAAS,CAAC;QAC5F,OAAOE,KAAK,IAAI;UACdhI,UAAU,EAAE8H,SAAS;UACrB7H,aAAa,EAAE,KAAK;UACpBH,QAAQ,EAAE,IAAI;UACd6D,MAAM,EAAEsE,KAAK;UACb5H,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOgI,MAAM;EACf;EAEAI,sBAAsBA,CAACf,GAAU;IAC/B,MAAMgB,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5CjB,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBe,aAAa,CAACC,GAAG,CAAChB,SAAS,CAAC9H,UAAU,CAAC;MACvC8H,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvCW,SAAS,CAACG,GAAG,CAACd,KAAK,CAACrE,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACzC,MAAM,GAAG6H,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACL3H,MAAM,EAAE6H,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACvD,WAAW,CAACrF,gBAAgB,IAAI,IAAI,CAACqF,WAAW,CAACpF,cAAc,EAAE;MACxE,MAAM+B,SAAS,GAAG,IAAI2D,IAAI,CAAC,IAAI,CAACN,WAAW,CAACrF,gBAAgB,CAAC;MAC7D,MAAMkC,OAAO,GAAG,IAAIyD,IAAI,CAAC,IAAI,CAACN,WAAW,CAACpF,cAAc,CAAC;MACzD,IAAI+B,SAAS,IAAIE,OAAO,IAAIF,SAAS,GAAGE,OAAO,EAAE;QAC/C,IAAI,CAAC2B,OAAO,CAACqC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEAM,kBAAkBA,CAAA;IAChB,IAAI,CAACoC,cAAc,EAAE;IACrB,IAAI,CAAC7E,aAAa,CAAC8E,mCAAmC,CAAC;MACrDxC,IAAI,EAAE;QACJyC,YAAY,EAAE,IAAI,CAACzD,WAAW,CAACC,kBAAkB,CAAC0B,GAAG;QACrDhH,gBAAgB,EAAE,IAAI,CAACqF,WAAW,CAACrF,gBAAgB,GAAG,IAAI,CAAC6F,UAAU,CAAC,IAAI,CAACR,WAAW,CAACrF,gBAAgB,CAAC,GAAG6E,SAAS;QACpH5E,cAAc,EAAE,IAAI,CAACoF,WAAW,CAACpF,cAAc,GAAG,IAAI,CAAC4F,UAAU,CAAC,IAAI,CAACR,WAAW,CAACpF,cAAc,CAAC,GAAG4E;;KAExG,CAAC,CAACM,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC4B,OAAO,IAAI5B,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACyC,gBAAgB,GAAG/D,GAAG,CAAC4B,OAAO,GAAG5B,GAAG,CAAC4B,OAAO,GAAG,EAAE;QACtD,IAAI5B,GAAG,CAAC4B,OAAO,EAAE;UACf,IAAI,CAACmC,gBAAgB,GAAG,CAAC,GAAG/D,GAAG,CAAC4B,OAAO,CAAC;UACxC,IAAI,CAACyB,sBAAsB,CAACrD,GAAG,CAAC4B,OAAO,CAAC;UACxC,IAAI,CAACoC,mBAAmB,GAAG,IAAI,CAAC3B,8BAA8B,CAACrC,GAAG,CAAC4B,OAAO,CAAC;UAC3E;UACA,IAAI,CAACqC,mBAAmB,CAACjE,GAAG,CAAC4B,OAAO,CAAC;QACvC;MACF;IACF,CAAC,CAAC;EACJ;EAEA;EACAqC,mBAAmBA,CAACC,IAA6B;IAC/C,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAyC;IAEpEF,IAAI,CAAC1B,OAAO,CAACC,SAAS,IAAG;MACvB,MAAM4B,SAAS,GAAG5B,SAAS,CAAC9H,UAAU,IAAI,EAAE,CAAC,CAAC;MAE9C8H,SAAS,CAACC,OAAO,EAAEF,OAAO,CAACG,KAAK,IAAG;QACjC,MAAM2B,YAAY,GAAG3B,KAAK,CAAC/H,aAAa,IAAI,KAAK,CAAC,CAAC;QACnD,MAAMgI,KAAK,GAAGD,KAAK,CAACrE,MAAM,IAAI,CAAC;QAE/B,IAAI,CAAC6F,WAAW,CAACI,GAAG,CAACD,YAAY,CAAC,EAAE;UAClCH,WAAW,CAACK,GAAG,CAACF,YAAY,EAAE,IAAIF,GAAG,EAA4B,CAAC;QACpE;QAEA,MAAMK,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACJ,YAAY,CAAE;QAE/C,IAAI,CAACG,QAAQ,CAACF,GAAG,CAAC3B,KAAK,CAAC,EAAE;UACxB6B,QAAQ,CAACD,GAAG,CAAC5B,KAAK,EAAE,EAAE,CAAC;QACzB;QAEA6B,QAAQ,CAACC,GAAG,CAAC9B,KAAK,CAAE,CAACC,IAAI,CAAC;UACxBlI,UAAU,EAAE0J,SAAS;UAAE;UACvBzJ,aAAa,EAAE0J,YAAY;UAAE;UAC7B7J,QAAQ,EAAEkI,KAAK,CAAClI,QAAQ,IAAI,CAAC;UAC7B6D,MAAM,EAAEsE,KAAK;UACb5H,gBAAgB,EAAE2H,KAAK,CAAC3H,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAE0H,KAAK,CAAC1H,cAAc,IAAI,EAAE;UAC1CkB,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACpC,cAAc,GAAG2J,KAAK,CAACC,IAAI,CAACQ,WAAW,CAACxC,OAAO,EAAE,CAAC,CAACG,GAAG,CAAC,CAAC,CAACwC,YAAY,EAAEG,QAAQ,CAAC,KAAI;MACvF,MAAM5I,MAAM,GAAiB6H,KAAK,CAACC,IAAI,CAACc,QAAQ,CAAC9C,OAAO,EAAE,CAAC,CACxDmB,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;MAAA,CAC1BjB,GAAG,CAAC,CAAC,CAACzG,WAAW,EAAEC,MAAM,CAAC,MAAM;QAC/BD,WAAW;QACXC,MAAM,EAAEA,MAAM,CAACwH,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAC3B;UACA,IAAID,CAAC,CAACpI,UAAU,KAAKqI,CAAC,CAACrI,UAAU,EAAE;YACjC,OAAOoI,CAAC,CAACpI,UAAU,CAACgK,aAAa,CAAC3B,CAAC,CAACrI,UAAU,CAAC;UACjD;UACA,OAAOoI,CAAC,CAACzE,MAAM,GAAG0E,CAAC,CAAC1E,MAAM;QAC5B,CAAC,CAAC;QACFnC,QAAQ,EAAE;OACX,CAAC,CAAC;MAEL,OAAO;QACLP,IAAI,EAAE0I,YAAY;QAClBzI,MAAM;QACNM,QAAQ,EAAE;OACX;IACH,CAAC,CAAC,CAAC2G,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnH,IAAI,CAAC+I,aAAa,CAAC3B,CAAC,CAACpH,IAAI,CAAC,CAAC;IAE/C;IACA,IAAI,CAAC2D,eAAe,GAAG,IAAI,CAACxF,cAAc,CAAC+H,GAAG,CAAC8C,EAAE,IAAIA,EAAE,CAAChJ,IAAI,CAAC;IAC7D,IAAI,CAACiJ,qBAAqB,EAAE;EAC9B;EAEA;EACAA,qBAAqBA,CAAA;IACnB,MAAMvB,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,IAAI,CAACxJ,cAAc,CAACyI,OAAO,CAACsC,QAAQ,IAAG;MACrC,IAAI,CAAC,IAAI,CAACtF,gBAAgB,IAAIsF,QAAQ,CAAClJ,IAAI,KAAK,IAAI,CAAC4D,gBAAgB,EAAE;QACrEsF,QAAQ,CAACjJ,MAAM,CAAC2G,OAAO,CAACI,KAAK,IAAG;UAC9BU,SAAS,CAACG,GAAG,CAACb,KAAK,CAACvH,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACvB,eAAe,GAAG4J,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC,CAACR,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpE;EAEA;EACAgC,gBAAgBA,CAAA;IACd,IAAI,CAACF,qBAAqB,EAAE;IAC5B,IAAI,CAAC3L,aAAa,CAACuG,cAAc,GAAG,IAAI,CAACD,gBAAgB;EAC3D;EAEA;EACAlG,QAAQA,CAAA;IACN;EAAA;EAGF;EACAyC,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAChC,cAAc,CAACiL,MAAM,CAACF,QAAQ,IAAG;MAC3C;MACA,IAAI,IAAI,CAACtF,gBAAgB,IAAIsF,QAAQ,CAAClJ,IAAI,KAAK,IAAI,CAAC4D,gBAAgB,EAAE;QACpE,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAACtG,aAAa,CAACC,aAAa,EAAE;QACpC,MAAM8L,OAAO,GAAG,IAAI,CAAC/L,aAAa,CAACC,aAAa,CAAC+L,WAAW,EAAE;QAC9D,MAAMC,gBAAgB,GAAGL,QAAQ,CAACjJ,MAAM,CAACuJ,IAAI,CAACxC,KAAK,IACjDA,KAAK,CAACtH,MAAM,CAAC8J,IAAI,CAACzC,KAAK,IACrBA,KAAK,CAAChI,UAAU,CAACuK,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IAChDtC,KAAK,CAAC/H,aAAa,CAACsK,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,CACpD,CACF;QACD,IAAI,CAACE,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACjM,aAAa,CAACM,YAAY,EAAE;QACnC,MAAM8L,iBAAiB,GAAGR,QAAQ,CAACjJ,MAAM,CAACuJ,IAAI,CAACxC,KAAK,IAClDA,KAAK,CAACtH,MAAM,CAAC8J,IAAI,CAACzC,KAAK,IAAI,IAAI,CAAC4C,mBAAmB,CAAC5C,KAAK,CAAC,CAAC,CAC5D;QACD,IAAI,CAAC2C,iBAAiB,EAAE;UACtB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACpM,aAAa,CAACQ,WAAW,EAAE;QAClC,MAAM2B,WAAW,GAAGmK,QAAQ,CAAC,IAAI,CAACtM,aAAa,CAACQ,WAAW,CAAC;QAC5D,MAAM+L,gBAAgB,GAAGX,QAAQ,CAACjJ,MAAM,CAACuJ,IAAI,CAACxC,KAAK,IACjDA,KAAK,CAACvH,WAAW,KAAKA,WAAW,CAClC;QACD,IAAI,CAACoK,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CAAC3D,GAAG,CAACgD,QAAQ,IAAG;MAChB;MACA,MAAMY,gBAAgB,GAAG;QAAE,GAAGZ;MAAQ,CAAE;MACxCY,gBAAgB,CAAC7J,MAAM,GAAGiJ,QAAQ,CAACjJ,MAAM,CAACmJ,MAAM,CAACpC,KAAK,IAAG;QACvD;QACA,IAAI,IAAI,CAAC1J,aAAa,CAACQ,WAAW,EAAE;UAClC,MAAM2B,WAAW,GAAGmK,QAAQ,CAAC,IAAI,CAACtM,aAAa,CAACQ,WAAW,CAAC;UAC5D,IAAIkJ,KAAK,CAACvH,WAAW,KAAKA,WAAW,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QAEA;QACA,MAAMsK,cAAc,GAAG/C,KAAK,CAACtH,MAAM,CAAC8J,IAAI,CAACzC,KAAK,IAAG;UAC/C;UACA,IAAI,IAAI,CAACzJ,aAAa,CAACC,aAAa,EAAE;YACpC,MAAM8L,OAAO,GAAG,IAAI,CAAC/L,aAAa,CAACC,aAAa,CAAC+L,WAAW,EAAE;YAC9D,IAAI,CAACvC,KAAK,CAAChI,UAAU,CAACuK,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACrD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAC/L,aAAa,CAACM,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC+L,mBAAmB,CAAC5C,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOgD,cAAc;MACvB,CAAC,CAAC,CAAC7D,GAAG,CAACc,KAAK,IAAG;QACb;QACA,MAAMgD,aAAa,GAAG;UAAE,GAAGhD;QAAK,CAAE;QAClCgD,aAAa,CAACtK,MAAM,GAAGsH,KAAK,CAACtH,MAAM,CAAC0J,MAAM,CAACrC,KAAK,IAAG;UACjD;UACA,IAAI,IAAI,CAACzJ,aAAa,CAACC,aAAa,EAAE;YACpC,MAAM8L,OAAO,GAAG,IAAI,CAAC/L,aAAa,CAACC,aAAa,CAAC+L,WAAW,EAAE;YAC9D,IAAI,CAACvC,KAAK,CAAChI,UAAU,CAACuK,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACrD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAC/L,aAAa,CAACM,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC+L,mBAAmB,CAAC5C,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOiD,aAAa;MACtB,CAAC,CAAC;MAEF,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ;EAEA;EACQH,mBAAmBA,CAAC5C,KAAqB;IAC/C,MAAMkD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACnD,KAAK,CAAC;IAEzC,QAAQ,IAAI,CAACzJ,aAAa,CAACM,YAAY;MACrC,KAAK,QAAQ;QACX,OAAOqM,MAAM,KAAK,QAAQ;MAC5B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,UAAU;MAC9B;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACQC,cAAcA,CAACnD,KAAqB;IAC1C,IAAI,CAACA,KAAK,CAAClI,QAAQ,EAAE;MACnB,OAAO,UAAU;IACnB;IAEA,IAAIkI,KAAK,CAAC3H,gBAAgB,IAAI2H,KAAK,CAAC1H,cAAc,EAAE;MAClD,MAAM8K,GAAG,GAAG,IAAIpF,IAAI,EAAE;MACtB,MAAM3D,SAAS,GAAG,IAAI2D,IAAI,CAACgC,KAAK,CAAC3H,gBAAgB,CAAC;MAClD,MAAMkC,OAAO,GAAG,IAAIyD,IAAI,CAACgC,KAAK,CAAC1H,cAAc,CAAC;MAE9C,IAAI8K,GAAG,GAAG/I,SAAS,EAAE;QACnB,OAAO,SAAS;MAClB,CAAC,MAAM,IAAI+I,GAAG,IAAI/I,SAAS,IAAI+I,GAAG,IAAI7I,OAAO,EAAE;QAC7C,OAAO,QAAQ;MACjB,CAAC,MAAM;QACL,OAAO,SAAS;MAClB;IACF;IAEA,OAAO,SAAS;EAClB;EAEAO,OAAOA,CAACgD,GAAQ;IACdA,GAAG,CAACgB,KAAK,EAAE;EACb;EAEAT,UAAUA,CAAA;IACR,IAAI,CAAClC,KAAK,CAACkH,KAAK,EAAE;IAClB,IAAI,CAAClH,KAAK,CAACmH,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACjI,uBAAuB,CAAChD,gBAAgB,CAAC;IAC9E,IAAI,CAAC8D,KAAK,CAACmH,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACjI,uBAAuB,CAAC/C,cAAc,CAAC;IAC5E,IAAI,CAAC6D,KAAK,CAACoH,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAClI,uBAAuB,CAAChD,gBAAgB,EAAE,IAAI,CAACgD,uBAAuB,CAAC/C,cAAc,CAAC;EACtI;EAEAkL,gBAAgBA,CAAA;IACd,IAAI,CAAClH,MAAM,CAACmH,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAAC/F,WAAW,EAAEC,kBAAkB,EAAE0B,GAAG,EAAE,CAAC,CAAC;EACnG;EAEA;EACAtG,gBAAgBA,CAACoJ,QAAwB;IACvC,IAAI,CAAClI,wBAAwB,GAAGkI,QAAQ,IAAI,IAAI;IAChD,IAAI,CAAC/H,aAAa,GAAG;MACnBC,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbE,UAAU,EAAE,CAAC0H,QAAQ;MACrBpF,iBAAiB,EAAEoF,QAAQ,GAAG,CAACA,QAAQ,CAAClJ,IAAI,CAAC,GAAG,EAAE;MAClD+D,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;KACjB;IAED;IACA,IAAIkF,QAAQ,EAAE;MACZA,QAAQ,CAACjJ,MAAM,CAAC2G,OAAO,CAACI,KAAK,IAAG;QAC9BA,KAAK,CAACzG,QAAQ,GAAG,KAAK;QACtByG,KAAK,CAACtH,MAAM,CAACkH,OAAO,CAACG,KAAK,IAAIA,KAAK,CAACxG,QAAQ,GAAG,KAAK,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAACyC,aAAa,CAACgC,IAAI,CAAC,IAAI,CAACyF,kBAAkB,CAAC;EAClD;EAEA;EACA7J,sBAAsBA,CAACoG,KAAiB;IACtC,IAAIA,KAAK,CAACzG,QAAQ,EAAE;MAClByG,KAAK,CAACtH,MAAM,CAACkH,OAAO,CAACG,KAAK,IAAG;QAC3B,IAAIA,KAAK,CAAClI,QAAQ,EAAE;UAClBkI,KAAK,CAACxG,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLyG,KAAK,CAACtH,MAAM,CAACkH,OAAO,CAACG,KAAK,IAAIA,KAAK,CAACxG,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA;EACAwB,aAAaA,CAAC8C,GAAQ;IACpB;IACA,IAAI,CAAC3B,KAAK,CAACkH,KAAK,EAAE;IAClB,IAAI,CAAClH,KAAK,CAACmH,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClJ,aAAa,CAACC,SAAS,CAAC;IAC3D,IAAI,CAAC8B,KAAK,CAACmH,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClJ,aAAa,CAACG,OAAO,CAAC;IACzD,IAAI,CAAC4B,KAAK,CAACoH,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACnJ,aAAa,CAACC,SAAS,EAAE,IAAI,CAACD,aAAa,CAACG,OAAO,CAAC;IAElG,IAAI,IAAI,CAAC4B,KAAK,CAACmC,aAAa,CAACjH,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC6E,OAAO,CAACqC,aAAa,CAAC,IAAI,CAACpC,KAAK,CAACmC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMqF,cAAc,GAAU,EAAE;IAEhC,IAAI,IAAI,CAACvJ,aAAa,CAACK,UAAU,EAAE;MACjC;MACA,IAAI,CAACrD,cAAc,CAACyI,OAAO,CAACsC,QAAQ,IAAG;QACrCA,QAAQ,CAACjJ,MAAM,CAAC2G,OAAO,CAACI,KAAK,IAAG;UAC9BA,KAAK,CAACtH,MAAM,CAACkH,OAAO,CAACG,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAAClI,QAAQ,EAAE;cAClB6L,cAAc,CAACzD,IAAI,CAAC;gBAClBpI,QAAQ,EAAEkI,KAAK,CAAClI,QAAQ;gBACxBO,gBAAgB,EAAE,IAAI,CAAC6F,UAAU,CAAC,IAAI,CAAC9D,aAAa,CAACC,SAAS,CAAC;gBAC/D/B,cAAc,EAAE,IAAI,CAAC4F,UAAU,CAAC,IAAI,CAAC9D,aAAa,CAACG,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACN,wBAAwB,EAAE;QACjC,IAAI,CAACA,wBAAwB,CAACf,MAAM,CAAC2G,OAAO,CAACI,KAAK,IAAG;UACnDA,KAAK,CAACtH,MAAM,CAACkH,OAAO,CAACG,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAACxG,QAAQ,IAAIwG,KAAK,CAAClI,QAAQ,EAAE;cACpC6L,cAAc,CAACzD,IAAI,CAAC;gBAClBpI,QAAQ,EAAEkI,KAAK,CAAClI,QAAQ;gBACxBO,gBAAgB,EAAE,IAAI,CAAC6F,UAAU,CAAC,IAAI,CAAC9D,aAAa,CAACC,SAAS,CAAC;gBAC/D/B,cAAc,EAAE,IAAI,CAAC4F,UAAU,CAAC,IAAI,CAAC9D,aAAa,CAACG,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIoJ,cAAc,CAACtM,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC6E,OAAO,CAACqC,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAACnC,aAAa,CAACqC,oCAAoC,CAAC;MACtDC,IAAI,EAAEiF;KACP,CAAC,CAACnG,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACzC,OAAO,CAAC0C,aAAa,CAAC,QAAQ+E,cAAc,CAACtM,MAAM,WAAW,CAAC;QACpE,IAAI,CAACwH,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA;EACA3G,cAAcA,CAAC6H,KAAqB;IAClC,MAAMkD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACnD,KAAK,CAAC;IACzC,OAAO,UAAUkD,MAAM,EAAE;EAC3B;EAEA;EACAU,eAAeA,CAAC5D,KAAqB;IACnC,IAAIA,KAAK,CAAClI,QAAQ,EAAE;MAClB;MACA,IAAI,CAACF,SAAS,CAAC,IAAI,CAACiM,MAAM,EAAE7D,KAAK,CAAC;IACpC;EACF;;;uCA/lBWlE,0BAA0B,EAAA3G,EAAA,CAAA2O,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7O,EAAA,CAAA2O,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA/O,EAAA,CAAA2O,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAjP,EAAA,CAAA2O,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAnP,EAAA,CAAA2O,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAArP,EAAA,CAAA2O,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAAtP,EAAA,CAAA2O,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAAxP,EAAA,CAAA2O,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA1B/I,0BAA0B;MAAAgJ,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;uCAR1B,EAAE,GAAA9P,EAAA,CAAAgQ,0BAAA,EAAAhQ,EAAA,CAAAiQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCpEb9P,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAW,SAAA,qBAAiC;UACnCX,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,aACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,cAA8B,cACN,cACqC,gBACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,qBAC0C;UADdD,EAAA,CAAAY,gBAAA,2BAAA2P,wEAAAzP,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAyP,GAAA;YAAAxQ,EAAA,CAAAmB,kBAAA,CAAA4O,GAAA,CAAAxH,WAAA,CAAAC,kBAAA,EAAA1H,MAAA,MAAAiP,GAAA,CAAAxH,WAAA,CAAAC,kBAAA,GAAA1H,MAAA;YAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;UAAA,EAA4C;UACtEd,EAAA,CAAAuB,UAAA,4BAAAkP,yEAAA;YAAAzQ,EAAA,CAAAe,aAAA,CAAAyP,GAAA;YAAA,OAAAxQ,EAAA,CAAAsB,WAAA,CAAkByO,GAAA,CAAArG,kBAAA,EAAoB;UAAA,EAAC;UACvC1J,EAAA,CAAA6B,UAAA,KAAA6O,gDAAA,wBAAoE;UAK1E1Q,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACP;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1DH,EAAA,CAAAC,cAAA,qBACwC;UADTD,EAAA,CAAAY,gBAAA,2BAAA+P,wEAAA7P,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAyP,GAAA;YAAAxQ,EAAA,CAAAmB,kBAAA,CAAA4O,GAAA,CAAArI,gBAAA,EAAA5G,MAAA,MAAAiP,GAAA,CAAArI,gBAAA,GAAA5G,MAAA;YAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;UAAA,EAA8B;UAC3Dd,EAAA,CAAAuB,UAAA,4BAAAqP,yEAAA;YAAA5Q,EAAA,CAAAe,aAAA,CAAAyP,GAAA;YAAA,OAAAxQ,EAAA,CAAAsB,WAAA,CAAkByO,GAAA,CAAA9C,gBAAA,EAAkB;UAAA,EAAC;UACrCjN,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAA6B,UAAA,KAAAgP,gDAAA,wBAAuE;UAK7E7Q,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAsB,eAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,iCAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,yBAA4B;UAC1BD,EAAA,CAAAW,SAAA,mBAAoD;UACpDX,EAAA,CAAAC,cAAA,iBAC6C;UAA3CD,EAAA,CAAAY,gBAAA,2BAAAkQ,oEAAAhQ,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAyP,GAAA;YAAAxQ,EAAA,CAAAmB,kBAAA,CAAA4O,GAAA,CAAAxH,WAAA,CAAArF,gBAAA,EAAApC,MAAA,MAAAiP,GAAA,CAAAxH,WAAA,CAAArF,gBAAA,GAAApC,MAAA;YAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;UAAA,EAA0C;UAD5Cd,EAAA,CAAAG,YAAA,EAC6C;UAC7CH,EAAA,CAAAW,SAAA,4BAA8D;UAChEX,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,qBAAe;UACbD,EAAA,CAAAW,SAAA,mBAAoD;UACpDX,EAAA,CAAAC,cAAA,iBAC2C;UAAzCD,EAAA,CAAAY,gBAAA,2BAAAmQ,oEAAAjQ,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAyP,GAAA;YAAAxQ,EAAA,CAAAmB,kBAAA,CAAA4O,GAAA,CAAAxH,WAAA,CAAApF,cAAA,EAAArC,MAAA,MAAAiP,GAAA,CAAAxH,WAAA,CAAApF,cAAA,GAAArC,MAAA;YAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;UAAA,EAAwC;UAD1Cd,EAAA,CAAAG,YAAA,EAC2C;UAC3CH,EAAA,CAAAW,SAAA,4BAA4D;UAGlEX,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAuC,eACoB,iBACV;UAC3CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,eAA6B,iBAEF;UAAvBD,EAAA,CAAAY,gBAAA,2BAAAoQ,oEAAAlQ,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAyP,GAAA;YAAAxQ,EAAA,CAAAmB,kBAAA,CAAA4O,GAAA,CAAA3M,QAAA,EAAAtC,MAAA,MAAAiP,GAAA,CAAA3M,QAAA,GAAAtC,MAAA;YAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;UAAA,EAAsB;UADxBd,EAAA,CAAAG,YAAA,EACyB;UACzBH,EAAA,CAAAC,cAAA,iBAAwD;UACtDD,EAAA,CAAAE,MAAA,oDACF;UACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;UAEJH,EADF,CAAAC,cAAA,eAA6B,iBAEF;UAAvBD,EAAA,CAAAY,gBAAA,2BAAAqQ,oEAAAnQ,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAyP,GAAA;YAAAxQ,EAAA,CAAAmB,kBAAA,CAAA4O,GAAA,CAAA3M,QAAA,EAAAtC,MAAA,MAAAiP,GAAA,CAAA3M,QAAA,GAAAtC,MAAA;YAAA,OAAAd,EAAA,CAAAsB,WAAA,CAAAR,MAAA;UAAA,EAAsB;UADxBd,EAAA,CAAAG,YAAA,EACyB;UACzBH,EAAA,CAAAC,cAAA,iBAAwD;UACtDD,EAAA,CAAAE,MAAA,oDACF;UAGNF,EAHM,CAAAG,YAAA,EAAQ,EACJ,EACF,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAwC,eACS,kBACgC;UAA/BD,EAAA,CAAAuB,UAAA,mBAAA2P,6DAAA;YAAAlR,EAAA,CAAAe,aAAA,CAAAyP,GAAA;YAAA,OAAAxQ,EAAA,CAAAsB,WAAA,CAASyO,GAAA,CAAArG,kBAAA,EAAoB;UAAA,EAAC;UAC1E1J,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAW,SAAA,aAA6B;UAClCX,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAiE;UAA7BD,EAAA,CAAAuB,UAAA,mBAAA4P,6DAAA;YAAAnR,EAAA,CAAAe,aAAA,CAAAyP,GAAA;YAAA,OAAAxQ,EAAA,CAAAsB,WAAA,CAASyO,GAAA,CAAAnM,gBAAA,EAAkB;UAAA,EAAC;UAC9D5D,EAAA,CAAAE,MAAA,8CACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAqENH,EAlEA,CAAA6B,UAAA,KAAAuP,0CAAA,mBAAoE,KAAAC,0CAAA,kBAiCZ,KAAAC,0CAAA,kBAiC2C;UAWvGtR,EADE,CAAAG,YAAA,EAAe,EACP;UAwEVH,EArEA,CAAA6B,UAAA,KAAA0P,kDAAA,iCAAAvR,EAAA,CAAAwR,sBAAA,CAAgE,KAAAC,kDAAA,gCAAAzR,EAAA,CAAAwR,sBAAA,CAwDG,KAAAE,kDAAA,iCAAA1R,EAAA,CAAAwR,sBAAA,CAaf;;;;;UA/NdxR,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAA+B,gBAAA,YAAAgO,GAAA,CAAAxH,WAAA,CAAAC,kBAAA,CAA4C;UAE1CxI,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA2P,GAAA,CAAAhG,oBAAA,CAAuB;UAStB/J,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAA+B,gBAAA,YAAAgO,GAAA,CAAArI,gBAAA,CAA8B;UAG3B1H,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAA2P,GAAA,CAAAtI,eAAA,CAAkB;UAYYzH,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,iBAAAuR,aAAA,CAA0B;UACtF3R,EAAA,CAAA+B,gBAAA,YAAAgO,GAAA,CAAAxH,WAAA,CAAArF,gBAAA,CAA0C;UAMgBlD,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,iBAAAwR,WAAA,CAAwB;UAClF5R,EAAA,CAAA+B,gBAAA,YAAAgO,GAAA,CAAAxH,WAAA,CAAApF,cAAA,CAAwC;UAWkDnD,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,eAAc;UACxGJ,EAAA,CAAA+B,gBAAA,YAAAgO,GAAA,CAAA3M,QAAA,CAAsB;UAMoEpD,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,gBAAe;UACzGJ,EAAA,CAAA+B,gBAAA,YAAAgO,GAAA,CAAA3M,QAAA,CAAsB;UAqBGpD,EAAA,CAAAM,SAAA,IAA+B;UAA/BN,EAAA,CAAAI,UAAA,SAAA2P,GAAA,CAAA9N,cAAA,CAAAC,MAAA,KAA+B;UAiC3ClC,EAAA,CAAAM,SAAA,EAA+B;UAA/BN,EAAA,CAAAI,UAAA,SAAA2P,GAAA,CAAA9N,cAAA,CAAAC,MAAA,KAA+B;UAiCvBlC,EAAA,CAAAM,SAAA,EAAkE;UAAlEN,EAAA,CAAAI,UAAA,SAAA2P,GAAA,CAAA9N,cAAA,CAAAC,MAAA,UAAA6N,GAAA,CAAA9D,gBAAA,CAAA/J,MAAA,OAAkE;;;qBD/EjG5C,YAAY,EAAAuS,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAElS,YAAY,EAAAmS,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,yBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAtD,EAAA,CAAAuD,eAAA,EAAAvD,EAAA,CAAAwD,mBAAA,EAAAxD,EAAA,CAAAyD,qBAAA,EAAAzD,EAAA,CAAA0D,qBAAA,EAAA1D,EAAA,CAAA2D,mBAAA,EAAA3D,EAAA,CAAA4D,gBAAA,EAAA5D,EAAA,CAAA6D,iBAAA,EAAA7D,EAAA,CAAA8D,iBAAA,EAAA9D,EAAA,CAAA+D,oBAAA,EAAA/D,EAAA,CAAAgE,iBAAA,EAAAhE,EAAA,CAAAiE,eAAA,EAAAjE,EAAA,CAAAkE,qBAAA,EAAAlE,EAAA,CAAAmE,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAEzT,iBAAiB,EAC7CF,kBAAkB,EAAEC,mBAAmB,EACvCE,qBAAqB;MAAAyT,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}