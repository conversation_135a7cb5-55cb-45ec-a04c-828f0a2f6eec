{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimpo;\nvalidation();\n{\n  this.valid.clear();\n  // 建案頁面需要驗證建案名稱\n  this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n  this.valid.required('[需求]', this.saveRequirement.CRequirement);\n  dModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule;\n}\nfrom;\n'@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\nimport { TemplateCreatorComponent } from 'src/app/shared/components/template-creator/template-creator.component';\nimport { SpaceTemplateSelectorComponent } from 'src/app/shared/components/space-template-selector/space-template-selector.component';\nlet RequirementManagementComponent = class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.showSpaceTemplateSelector = false;\n    // Tab 切換事件處理\n    this.isFirstTabChange = true;\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CIsSimple = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CGroupName = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 根據當前 tab 決定是否需要驗證建案名稱\n    if (this.currentTab === 0) {\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    }\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[排序]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false,\n      CIsSimple: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 根據當前 tab 決定是否需要建案ID\n    if (this.currentTab === 0) {\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    } else {\n      // 模板頁面 - 設定建案ID為0，CHouseType預設[1,2]\n      this.saveRequirement.CBuildCaseID = 0;\n      this.saveRequirement.CHouseType = [1, 2];\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        // 編輯時如果是共用tab，CHouseType強制為[1,2]\n        if (_this.currentTab === 1) {\n          _this.saveRequirement.CHouseType = [1, 2];\n        }\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\n    if (this.currentTab === 1) {\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 只在建案 tab 下且有建案時才查詢\n      if (this.currentTab === 0 && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      } else if (this.currentTab === 1) {\n        this.getListRequirementRequest.CBuildCaseID = 0;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動，CHouseType預設為[1,2]\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n      this.getListRequirementRequest.CHouseType = [1, 2];\n    } else {\n      // 建案頁面的邏輯保持不變\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          // 將 API 返回的數據轉換為 SelectableRequirement 並初始化 selected 屬性\n          this.requirementList = res.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false,\n            CIsSimple: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          // 共用tab時CHouseType強制為[1,2]\n          this.saveRequirement.CHouseType = this.currentTab === 1 ? [1, 2] : res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n          this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  getCIsSimpleText(data) {\n    return data.CIsSimple ? '是' : '否';\n  }\n  onTabChange(event) {\n    // 避免頁面初始化時自動觸發重複查詢\n    if (this.isFirstTabChange) {\n      this.isFirstTabChange = false;\n      return;\n    }\n    // 根據 tabTitle 來判斷當前頁面\n    if (event.tabTitle === '共用') {\n      this.currentTab = 1;\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      this.currentTab = 0;\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    }\n    this.getList();\n  }\n  // 新增模板\n  addTemplate(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false,\n      CIsSimple: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 模板設定建案ID為0，CHouseType預設[1,2]\n    this.saveRequirement.CBuildCaseID = 0;\n    this.saveRequirement.CHouseType = [1, 2];\n    this.dialogService.open(dialog);\n  }\n  // 編輯模板\n  onEditTemplate(data, dialog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this2.isNew = false;\n      try {\n        yield _this2.getData();\n        // 編輯模板時CHouseType強制為[1,2]\n        _this2.saveRequirement.CHouseType = [1, 2];\n        _this2.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get template data\", error);\n      }\n    })();\n  }\n  // 保存模板\n  saveTemplate(ref) {\n    // 模板驗證（不包含建案名稱）\n    this.valid.clear();\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    // CHouseType強制為[1,2]\n    if (this.currentTab === 1) {\n      this.saveRequirement.CHouseType = [1, 2];\n    }\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 確保模板建案ID為0\n    const templateData = {\n      ...this.saveRequirement\n    };\n    templateData.CBuildCaseID = 0;\n    // CHouseType強制為[1,2]\n    if (this.currentTab === 1) {\n      templateData.CHouseType = [1, 2];\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: templateData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  openTemplateViewer(templateViewerDialog) {\n    this.dialogService.open(templateViewerDialog);\n  }\n  onSelectTemplate(tpl) {\n    // 查看模板邏輯\n  }\n  // 獲取選中的需求項目\n  getSelectedRequirements() {\n    return this.requirementList.filter(req => req.selected);\n  }\n  // 選中狀態變更處理\n  onRequirementSelectionChange() {\n    // 可以在這裡添加額外的邏輯，比如更新選中計數等\n  }\n  // 全選功能\n  selectAllRequirements() {\n    this.requirementList.forEach(req => req.selected = true);\n  }\n  // 清除所有選擇\n  clearAllSelections() {\n    this.requirementList.forEach(req => req.selected = false);\n  }\n  // 檢查是否全選\n  isAllSelected() {\n    return this.requirementList.length > 0 && this.requirementList.every(req => req.selected);\n  }\n  // 檢查是否部分選中（用於 indeterminate 狀態）\n  isIndeterminate() {\n    const selectedCount = this.requirementList.filter(req => req.selected).length;\n    return selectedCount > 0 && selectedCount < this.requirementList.length;\n  }\n  // 切換全選狀態\n  toggleSelectAll(event) {\n    const isChecked = event.target.checked;\n    this.requirementList.forEach(req => req.selected = isChecked);\n  }\n  // 打開模板創建器\n  openTemplateCreator(templateCreatorDialog) {\n    const selectedRequirements = this.getSelectedRequirements();\n    if (selectedRequirements.length === 0) {\n      this.message.showErrorMSG('請先選擇要加入模板的項目');\n      return;\n    }\n    // 將選中的項目存儲在屬性中，確保在模板中保持引用一致性\n    this.selectedRequirementsForTemplate = [...selectedRequirements];\n    this.isCreatingTemplate = true;\n    const dialogRef = this.dialogService.open(templateCreatorDialog);\n    // 當對話框關閉時重置狀態\n    dialogRef.onClose.subscribe(() => {\n      this.isCreatingTemplate = false;\n      this.selectedRequirementsForTemplate = [];\n    });\n  }\n  // 模板創建成功回調\n  onTemplateCreated() {\n    this.message.showSucessMSG('模板創建成功');\n    // 清除選中狀態\n    this.clearAllSelections();\n  }\n  // 空間模板相關方法\n  openSpaceTemplateSelector() {\n    if (!this.getListRequirementRequest.CBuildCaseID) {\n      this.message.showErrorMSG('請先選擇建案');\n      return;\n    }\n    this.showSpaceTemplateSelector = true;\n    // 使用 setTimeout 確保組件已渲染後再調用 open 方法\n    setTimeout(() => {\n      if (this.spaceTemplateSelectorComponent) {\n        this.spaceTemplateSelectorComponent.open();\n      }\n    }, 0);\n  }\n  onSpaceTemplateApplied(config) {\n    console.log('套用空間模板配置:', config);\n    // 這裡可以實作將模板配置轉換為需求項目並批次新增的邏輯\n    // 例如：\n    const newRequirements = config.selectedItems.map(item => ({\n      CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n      CRequirement: `${config.spaceName}-${item.CTemplateName}`,\n      CGroupName: config.spaceName,\n      CUnitPrice: 0,\n      // API 沒有價格資訊，預設為 0\n      CUnit: '個',\n      // 預設單位\n      CStatus: 1,\n      CIsShow: true,\n      CIsSimple: false,\n      CHouseType: this.houseType.map(type => type.value),\n      // 預設所有房型\n      CRemark: `由空間模板批次新增 - ID: ${item.CTemplateId}`\n    }));\n    // TODO: 調用批次新增 API\n    // this.batchCreateRequirements(newRequirements);\n    this.message.showSucessMSG(`成功套用 ${config.selectedItems.length} 個${config.spaceName}模板項目`);\n    this.getList(); // 重新載入資料\n  }\n  onSpaceTemplateClosed() {\n    this.showSpaceTemplateSelector = false;\n  }\n  // 未來可以擴展的批次新增方法\n  batchCreateRequirements(requirements) {\n    // 實作批次新增需求的 API 調用\n    // 可以參考現有的 save 方法進行批次處理\n  }\n};\n__decorate([ViewChild('spaceTemplateSelector')], RequirementManagementComponent.prototype, \"spaceTemplateSelectorComponent\", void 0);\nRequirementManagementComponent = __decorate([Component({\n  selector: 'app-requirement-management',\n  standalone: true,\n  imports: [NbCardModule, BreadcrumbComponent, NbInputModule, FormsModule, NbSelectModule, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, FormGroupComponent, NumberWithCommasPipe, TemplateViewerComponent, TemplateCreatorComponent, SpaceTemplateSelectorComponent],\n  templateUrl: './requirement-management.component.html',\n  styleUrl: './requirement-management.component.scss'\n})], RequirementManagementComponent);\nexport { RequirementManagementComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "impo", "validation", "valid", "clear", "required", "saveRequirement", "CBuildCaseID", "CRequirement", "dModule", "NbCheckboxModule", "NbDialogService", "NbInputModule", "NbOptionModule", "NbSelectModule", "NbTabsetModule", "from", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "TemplateViewerComponent", "TemplateCreatorComponent", "SpaceTemplateSelectorComponent", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getListRequirementRequest", "getRequirementRequest", "buildCaseList", "requirementList", "CHouseType", "statusOptions", "value", "label", "houseType", "getEnumOptions", "isNew", "currentBuildCase", "showSpaceTemplateSelector", "isFirstTabChange", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "CStatus", "CIsShow", "CIsSimple", "CGroupName", "map", "type", "resetSearch", "length", "setTimeout", "cID", "getList", "getHouseType", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "currentTab", "CSort", "CUnitPrice", "CUnit", "errorMessages", "CRemark", "add", "dialog", "open", "onEdit", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "save", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "close", "onDelete", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "item", "selected", "TotalItems", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "filter", "v", "getCIsShowText", "getCIsSimpleText", "onTabChange", "event", "tabTitle", "addTemplate", "onEditTemplate", "_this2", "saveTemplate", "templateData", "openTemplateViewer", "templateViewerDialog", "onSelectTemplate", "tpl", "getSelectedRequirements", "req", "onRequirementSelectionChange", "selectAllRequirements", "clearAllSelections", "isAllSelected", "every", "isIndeterminate", "selectedCount", "toggleSelectAll", "isChecked", "target", "openTemplateCreator", "templateCreatorDialog", "selectedRequirements", "selectedRequirementsForTemplate", "isCreatingTemplate", "dialogRef", "onClose", "onTemplateCreated", "openSpaceTemplateSelector", "spaceTemplateSelectorComponent", "onSpaceTemplateApplied", "config", "newRequirements", "selectedItems", "spaceName", "CTemplateName", "CTemplateId", "onSpaceTemplateClosed", "batchCreateRequirements", "requirements", "__decorate", "selector", "standalone", "imports", "NbCardModule", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimpo  validation() {\r\n    this.valid.clear();\r\n\r\n    // 建案頁面需要驗證建案名稱\r\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);dModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { Template, TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\r\nimport { TemplateCreatorComponent } from 'src/app/shared/components/template-creator/template-creator.component';\r\nimport { SpaceTemplateSelectorComponent, SpaceTemplateConfig } from 'src/app/shared/components/space-template-selector/space-template-selector.component';\r\n\r\n// 擴展 GetRequirement 接口以支持選中狀態\r\ninterface SelectableRequirement extends GetRequirement {\r\n  selected?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    TemplateViewerComponent,\r\n    TemplateCreatorComponent,\r\n    SpaceTemplateSelectorComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: SelectableRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n\r\n  // 空間模板選擇器相關屬性\r\n  @ViewChild('spaceTemplateSelector') spaceTemplateSelectorComponent!: SpaceTemplateSelectorComponent;\r\n  showSpaceTemplateSelector = false;\r\n\r\n\r\n\r\n  override ngOnInit(): void { }\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CIsSimple = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CGroupName = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    // 根據當前 tab 決定是否需要驗證建案名稱\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面需要驗證建案名稱\r\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    }\r\n\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[排序]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 根據當前 tab 決定是否需要建案ID\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n      if (this.currentBuildCase != 0) {\r\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    } else {\r\n      // 模板頁面 - 設定建案ID為0，CHouseType預設[1,2]\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n    }\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: SelectableRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      // 編輯時如果是共用tab，CHouseType強制為[1,2]\r\n      if (this.currentTab === 1) {\r\n        this.saveRequirement.CHouseType = [1, 2];\r\n      }\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: SelectableRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 只在建案 tab 下且有建案時才查詢\r\n        if (this.currentTab === 0 && this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        } else if (this.currentTab === 1) {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動，CHouseType預設為[1,2]\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n      this.getListRequirementRequest.CHouseType = [1, 2];\r\n    } else {\r\n      // 建案頁面的邏輯保持不變\r\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n      }\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            // 將 API 返回的數據轉換為 SelectableRequirement 並初始化 selected 屬性\r\n            this.requirementList = res.Entries.map(item => ({\r\n              ...item,\r\n              selected: false\r\n            }));\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  } getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            // 共用tab時CHouseType強制為[1,2]\r\n            this.saveRequirement.CHouseType = this.currentTab === 1 ? [1, 2] : (res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : []);\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\r\n            this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  getCIsSimpleText(data: any): string {\r\n    return data.CIsSimple ? '是' : '否';\r\n  }\r\n\r\n  // Tab 切換事件處理\r\n  private isFirstTabChange = true;\r\n  onTabChange(event: any) {\r\n    // 避免頁面初始化時自動觸發重複查詢\r\n    if (this.isFirstTabChange) {\r\n      this.isFirstTabChange = false;\r\n      return;\r\n    }\r\n    // 根據 tabTitle 來判斷當前頁面\r\n    if (event.tabTitle === '共用') {\r\n      this.currentTab = 1;\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      this.currentTab = 0;\r\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\r\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    }\r\n    this.getList();\r\n  }\r\n\r\n  // 新增模板\r\n  addTemplate(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    // 模板設定建案ID為0，CHouseType預設[1,2]\r\n    this.saveRequirement.CBuildCaseID = 0;\r\n    this.saveRequirement.CHouseType = [1, 2];\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 編輯模板\r\n  async onEditTemplate(data: SelectableRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      // 編輯模板時CHouseType強制為[1,2]\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get template data\", error);\r\n    }\r\n  }\r\n\r\n  // 保存模板\r\n  saveTemplate(ref: any) {\r\n    // 模板驗證（不包含建案名稱）\r\n    this.valid.clear();\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    // CHouseType強制為[1,2]\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n    }\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 確保模板建案ID為0\r\n    const templateData = { ...this.saveRequirement };\r\n    templateData.CBuildCaseID = 0;\r\n    // CHouseType強制為[1,2]\r\n    if (this.currentTab === 1) {\r\n      templateData.CHouseType = [1, 2];\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: templateData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  openTemplateViewer(templateViewerDialog: TemplateRef<any>) {\r\n    this.dialogService.open(templateViewerDialog);\r\n  }\r\n\r\n  onSelectTemplate(tpl: Template) {\r\n    // 查看模板邏輯\r\n  }\r\n\r\n  // 獲取選中的需求項目\r\n  getSelectedRequirements(): SelectableRequirement[] {\r\n    return this.requirementList.filter(req => req.selected);\r\n  }\r\n\r\n  // 選中狀態變更處理\r\n  onRequirementSelectionChange() {\r\n    // 可以在這裡添加額外的邏輯，比如更新選中計數等\r\n  }\r\n\r\n  // 全選功能\r\n  selectAllRequirements() {\r\n    this.requirementList.forEach(req => req.selected = true);\r\n  }\r\n\r\n  // 清除所有選擇\r\n  clearAllSelections() {\r\n    this.requirementList.forEach(req => req.selected = false);\r\n  }\r\n\r\n  // 檢查是否全選\r\n  isAllSelected(): boolean {\r\n    return this.requirementList.length > 0 && this.requirementList.every(req => req.selected);\r\n  }\r\n\r\n  // 檢查是否部分選中（用於 indeterminate 狀態）\r\n  isIndeterminate(): boolean {\r\n    const selectedCount = this.requirementList.filter(req => req.selected).length;\r\n    return selectedCount > 0 && selectedCount < this.requirementList.length;\r\n  }\r\n\r\n  // 切換全選狀態\r\n  toggleSelectAll(event: any) {\r\n    const isChecked = event.target.checked;\r\n    this.requirementList.forEach(req => req.selected = isChecked);\r\n  }\r\n\r\n  // 打開模板創建器\r\n  openTemplateCreator(templateCreatorDialog: TemplateRef<any>) {\r\n    const selectedRequirements = this.getSelectedRequirements();\r\n    if (selectedRequirements.length === 0) {\r\n      this.message.showErrorMSG('請先選擇要加入模板的項目');\r\n      return;\r\n    }\r\n\r\n    // 將選中的項目存儲在屬性中，確保在模板中保持引用一致性\r\n    this.selectedRequirementsForTemplate = [...selectedRequirements];\r\n    this.isCreatingTemplate = true;\r\n    const dialogRef = this.dialogService.open(templateCreatorDialog);\r\n\r\n    // 當對話框關閉時重置狀態\r\n    dialogRef.onClose.subscribe(() => {\r\n      this.isCreatingTemplate = false;\r\n      this.selectedRequirementsForTemplate = [];\r\n    });\r\n  }\r\n\r\n  // 模板創建成功回調\r\n  onTemplateCreated() {\r\n    this.message.showSucessMSG('模板創建成功');\r\n    // 清除選中狀態\r\n    this.clearAllSelections();\r\n  }\r\n\r\n  // 空間模板相關方法\r\n  openSpaceTemplateSelector() {\r\n    if (!this.getListRequirementRequest.CBuildCaseID) {\r\n      this.message.showErrorMSG('請先選擇建案');\r\n      return;\r\n    }\r\n    this.showSpaceTemplateSelector = true;\r\n\r\n    // 使用 setTimeout 確保組件已渲染後再調用 open 方法\r\n    setTimeout(() => {\r\n      if (this.spaceTemplateSelectorComponent) {\r\n        this.spaceTemplateSelectorComponent.open();\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  onSpaceTemplateApplied(config: SpaceTemplateConfig) {\r\n    console.log('套用空間模板配置:', config);\r\n\r\n    // 這裡可以實作將模板配置轉換為需求項目並批次新增的邏輯\r\n    // 例如：\r\n    const newRequirements = config.selectedItems.map(item => ({\r\n      CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\r\n      CRequirement: `${config.spaceName}-${item.CTemplateName}`,\r\n      CGroupName: config.spaceName,\r\n      CUnitPrice: 0, // API 沒有價格資訊，預設為 0\r\n      CUnit: '個', // 預設單位\r\n      CStatus: 1,\r\n      CIsShow: true,\r\n      CIsSimple: false,\r\n      CHouseType: this.houseType.map(type => type.value), // 預設所有房型\r\n      CRemark: `由空間模板批次新增 - ID: ${item.CTemplateId}`\r\n    }));\r\n\r\n    // TODO: 調用批次新增 API\r\n    // this.batchCreateRequirements(newRequirements);\r\n\r\n    this.message.showSucessMSG(`成功套用 ${config.selectedItems.length} 個${config.spaceName}模板項目`);\r\n    this.getList(); // 重新載入資料\r\n  }\r\n\r\n  onSpaceTemplateClosed() {\r\n    this.showSpaceTemplateSelector = false;\r\n  }\r\n\r\n  // 未來可以擴展的批次新增方法\r\n  private batchCreateRequirements(requirements: any[]) {\r\n    // 實作批次新增需求的 API 調用\r\n    // 可以參考現有的 save 方法進行批次處理\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAmCC,SAAS,QAAQ,eAAe;AAErFC,IAAI;AAAEC,UAAU,EAAE;AAAC;EACf,IAAI,CAACC,KAAK,CAACC,KAAK,EAAE;EAElB;EACA,IAAI,CAACD,KAAK,CAACE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACC,eAAe,CAACC,YAAY,CAAC;EAEhE,IAAI,CAACJ,KAAK,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACC,eAAe,CAACE,YAAY,CAAC;EAACC,OAAO,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc;AAAC;AAAEC,IAAI;AAAC,gBAAgB;AAOpM,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAAmBC,uBAAuB,QAAQ,qEAAqE;AACvH,SAASC,wBAAwB,QAAQ,uEAAuE;AAChH,SAASC,8BAA8B,QAA6B,qFAAqF;AA8BlJ,IAAMC,8BAA8B,GAApC,MAAMA,8BAA+B,SAAQd,aAAa;EAC/De,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBjC,KAAuB,EACvBkC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACR,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAjC,KAAK,GAALA,KAAK;IACL,KAAAkC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAMpB;IACA,KAAAC,yBAAyB,GAAG,EAA0F;IACtH,KAAAC,qBAAqB,GAA8B,EAAE;IACrD;IACA,KAAAC,aAAa,GAA8B,EAAE;IAC7C,KAAAC,eAAe,GAA4B,EAAE;IAC7C,KAAAvC,eAAe,GAAqE;MAAEwC,UAAU,EAAE;IAAE,CAAE;IAEtG,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAC,SAAS,GAAG,IAAI,CAAChB,UAAU,CAACiB,cAAc,CAACxB,aAAa,CAAC;IACzD,KAAAyB,KAAK,GAAG,KAAK;IACb,KAAAC,gBAAgB,GAAG,CAAC;IAIpB,KAAAC,yBAAyB,GAAG,KAAK;IAyPjC;IACQ,KAAAC,gBAAgB,GAAG,IAAI;IA/Q7B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAuBSC,QAAQA,CAAA,GAAW;EAC5B;EACAF,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACd,yBAAyB,CAACiB,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACjB,yBAAyB,CAACkB,OAAO,GAAG,IAAI;IAC7C,IAAI,CAAClB,yBAAyB,CAACmB,SAAS,GAAG,IAAI;IAC/C,IAAI,CAACnB,yBAAyB,CAAClC,YAAY,GAAG,EAAE;IAChD,IAAI,CAACkC,yBAAyB,CAACoB,UAAU,GAAG,EAAE;IAC9C;IACA,IAAI,CAACpB,yBAAyB,CAACI,UAAU,GAAG,IAAI,CAACI,SAAS,CAACa,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAChB,KAAK,CAAC;EACpF;EAEA;EACAiB,WAAWA,CAAA;IACT,IAAI,CAACT,oBAAoB,EAAE;IAC3B;IACA,IAAI,IAAI,CAACZ,aAAa,IAAI,IAAI,CAACA,aAAa,CAACsB,MAAM,GAAG,CAAC,EAAE;MACvDC,UAAU,CAAC,MAAK;QACd,IAAI,CAACzB,yBAAyB,CAACnC,YAAY,GAAG,IAAI,CAACqC,aAAa,CAAC,CAAC,CAAC,CAACwB,GAAG;QACvE,IAAI,CAACC,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEAC,YAAYA,CAACC,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAAC3B,SAAS,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/B,KAAK,IAAI4B,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC5B,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOyB,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAE/E,UAAUA,CAAA;IACV,IAAI,CAACC,KAAK,CAACC,KAAK,EAAE;IAElB;IACA,IAAI,IAAI,CAAC8E,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,CAAC/E,KAAK,CAACE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACC,eAAe,CAACC,YAAY,CAAC;IAClE;IAEA,IAAI,CAACJ,KAAK,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACC,eAAe,CAACE,YAAY,CAAC;IAC9D,IAAI,CAACL,KAAK,CAACE,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACC,eAAe,CAACwC,UAAU,CAAC;IAC7D,IAAI,CAAC3C,KAAK,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACC,eAAe,CAAC6E,KAAK,CAAC;IACvD,IAAI,CAAChF,KAAK,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACC,eAAe,CAACqD,OAAO,CAAC;IACzD,IAAI,CAACxD,KAAK,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACC,eAAe,CAAC8E,UAAU,CAAC;IAC5D,IAAI,CAACjF,KAAK,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACC,eAAe,CAAC+E,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAAC/E,eAAe,CAACwD,UAAU,IAAI,IAAI,CAACxD,eAAe,CAACwD,UAAU,CAACI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAAC/D,KAAK,CAACmF,aAAa,CAACN,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAC1E,eAAe,CAACiF,OAAO,IAAI,IAAI,CAACjF,eAAe,CAACiF,OAAO,CAACrB,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAAC/D,KAAK,CAACmF,aAAa,CAACN,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEAQ,GAAGA,CAACC,MAAwB;IAC1B,IAAI,CAACrC,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC9C,eAAe,GAAG;MAAEwC,UAAU,EAAE,EAAE;MAAEc,OAAO,EAAE,KAAK;MAAEC,SAAS,EAAE;IAAK,CAAE;IAC3E,IAAI,CAACvD,eAAe,CAACqD,OAAO,GAAG,CAAC;IAChC,IAAI,CAACrD,eAAe,CAAC8E,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAACF,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,IAAI,CAAC7B,gBAAgB,IAAI,CAAC,EAAE;QAC9B,IAAI,CAAC/C,eAAe,CAACC,YAAY,GAAG,IAAI,CAAC8C,gBAAgB;MAC3D,CAAC,MAAM,IAAI,IAAI,CAACT,aAAa,IAAI,IAAI,CAACA,aAAa,CAACsB,MAAM,GAAG,CAAC,EAAE;QAC9D,IAAI,CAAC5D,eAAe,CAACC,YAAY,GAAG,IAAI,CAACqC,aAAa,CAAC,CAAC,CAAC,CAACwB,GAAG;MAC/D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAC9D,eAAe,CAACC,YAAY,GAAG,CAAC;MACrC,IAAI,CAACD,eAAe,CAACwC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,CAACX,aAAa,CAACuD,IAAI,CAACD,MAAM,CAAC;EACjC;EAEME,MAAMA,CAACC,IAA2B,EAAEH,MAAwB;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MAChED,KAAI,CAAClD,qBAAqB,CAACoD,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAACzC,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMyC,KAAI,CAACG,OAAO,EAAE;QACpB;QACA,IAAIH,KAAI,CAACX,UAAU,KAAK,CAAC,EAAE;UACzBW,KAAI,CAACvF,eAAe,CAACwC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1C;QACA+C,KAAI,CAAC1D,aAAa,CAACuD,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAG,IAAIA,CAACC,GAAQ;IACX,IAAI,CAACnG,UAAU,EAAE;IACjB,IAAI,IAAI,CAACC,KAAK,CAACmF,aAAa,CAACpB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC9B,OAAO,CAACkE,aAAa,CAAC,IAAI,CAACnG,KAAK,CAACmF,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAI,IAAI,CAACJ,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC5E,eAAe,CAACC,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAAC+B,kBAAkB,CAACiE,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAAClG;KACZ,CAAC,CAACmG,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACvE,OAAO,CAACwE,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACvC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACjC,OAAO,CAACyE,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEAC,QAAQA,CAACpB,IAA2B;IAClC,IAAI,CAACtF,eAAe,CAACyF,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAAC3C,KAAK,GAAG,KAAK;IAClB,IAAI6D,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAAC7E,kBAAkB,CAAC8E,iCAAiC,CAAC;MACxDZ,IAAI,EAAE;QACJT,cAAc,EAAE,IAAI,CAACzF,eAAe,CAACyF;;KAExC,CAAC,CAACU,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACtE,OAAO,CAACwE,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACvC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAZ,gBAAgBA,CAAA;IACd,IAAI,CAACpB,gBAAgB,CAACgF,qCAAqC,CAAC;MAAEb,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEc,IAAI,CAACpG,kBAAkB,CAAC,IAAI,CAACuB,UAAU,CAAC,CAAC,CAACgE,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAC9D,aAAa,GAAG8D,GAAG,CAACa,OAAQ;MACjC;MACA,IAAI,IAAI,CAACrC,UAAU,KAAK,CAAC,IAAI,IAAI,CAACtC,aAAa,CAACsB,MAAM,GAAG,CAAC,EAAE;QAC1D,IAAI,CAACxB,yBAAyB,CAACnC,YAAY,GAAG,IAAI,CAACqC,aAAa,CAAC,CAAC,CAAC,CAACwB,GAAG;QACvE,IAAI,CAACC,OAAO,EAAE;MAChB,CAAC,MAAM,IAAI,IAAI,CAACa,UAAU,KAAK,CAAC,EAAE;QAChC,IAAI,CAACxC,yBAAyB,CAACnC,YAAY,GAAG,CAAC;QAC/C,IAAI,CAAC8D,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAAC3B,yBAAyB,CAAC8E,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAAC/E,yBAAyB,CAACgF,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAAC9E,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAAC+E,YAAY,GAAG,CAAC;IACrB;IACA,IAAI,IAAI,CAAC1C,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACxC,yBAAyB,CAACnC,YAAY,GAAG,CAAC;MAC/C,IAAI,CAACmC,yBAAyB,CAACI,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACpD,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACJ,yBAAyB,CAACnC,YAAY,IAAI,IAAI,CAACmC,yBAAyB,CAACnC,YAAY,IAAI,CAAC,EAAE;QACnG,IAAI,CAAC8C,gBAAgB,GAAG,IAAI,CAACX,yBAAyB,CAACnC,YAAY;MACrE;IACF;IAEA,IAAI,CAAC+B,kBAAkB,CAACuF,8BAA8B,CAAC;MAAErB,IAAI,EAAE,IAAI,CAAC9D;IAAyB,CAAE,CAAC,CAC7F4E,IAAI,EAAE,CACNb,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACa,OAAO,EAAE;UACf;UACA,IAAI,CAAC1E,eAAe,GAAG6D,GAAG,CAACa,OAAO,CAACxD,GAAG,CAAC+D,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACPC,QAAQ,EAAE;WACX,CAAC,CAAC;UACH,IAAI,CAACH,YAAY,GAAGlB,GAAG,CAACsB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAAEhC,OAAOA,CAAA;IACP,IAAI,CAAC1D,kBAAkB,CAAC2F,8BAA8B,CAAC;MAAEzB,IAAI,EAAE,IAAI,CAAC7D;IAAqB,CAAE,CAAC,CACzF2E,IAAI,EAAE,CACNb,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACa,OAAO,EAAE;UACf,IAAI,CAACjH,eAAe,GAAG;YAAEwC,UAAU,EAAE,EAAE;YAAEc,OAAO,EAAE,KAAK;YAAEC,SAAS,EAAE;UAAK,CAAE;UAC3E,IAAI,CAACvD,eAAe,CAACC,YAAY,GAAGmG,GAAG,CAACa,OAAO,CAAChH,YAAY;UAC5D,IAAI,CAACD,eAAe,CAACwD,UAAU,GAAG4C,GAAG,CAACa,OAAO,CAACzD,UAAU;UACxD;UACA,IAAI,CAACxD,eAAe,CAACwC,UAAU,GAAG,IAAI,CAACoC,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAIwB,GAAG,CAACa,OAAO,CAACzE,UAAU,GAAI0B,KAAK,CAACC,OAAO,CAACiC,GAAG,CAACa,OAAO,CAACzE,UAAU,CAAC,GAAG4D,GAAG,CAACa,OAAO,CAACzE,UAAU,GAAG,CAAC4D,GAAG,CAACa,OAAO,CAACzE,UAAU,CAAC,GAAI,EAAG;UAC9L,IAAI,CAACxC,eAAe,CAACiF,OAAO,GAAGmB,GAAG,CAACa,OAAO,CAAChC,OAAO;UAClD,IAAI,CAACjF,eAAe,CAACE,YAAY,GAAGkG,GAAG,CAACa,OAAO,CAAC/G,YAAY;UAC5D,IAAI,CAACF,eAAe,CAACyF,cAAc,GAAGW,GAAG,CAACa,OAAO,CAACxB,cAAc;UAChE,IAAI,CAACzF,eAAe,CAAC6E,KAAK,GAAGuB,GAAG,CAACa,OAAO,CAACpC,KAAK;UAC9C,IAAI,CAAC7E,eAAe,CAACqD,OAAO,GAAG+C,GAAG,CAACa,OAAO,CAAC5D,OAAO;UAClD,IAAI,CAACrD,eAAe,CAAC8E,UAAU,GAAGsB,GAAG,CAACa,OAAO,CAACnC,UAAU,IAAI,CAAC;UAC7D,IAAI,CAAC9E,eAAe,CAAC+E,KAAK,GAAGqB,GAAG,CAACa,OAAO,CAAClC,KAAK;UAC9C,IAAI,CAAC/E,eAAe,CAACsD,OAAO,GAAG8C,GAAG,CAACa,OAAO,CAAC3D,OAAO,IAAI,KAAK;UAC3D,IAAI,CAACtD,eAAe,CAACuD,SAAS,GAAG6C,GAAG,CAACa,OAAO,CAAC1D,SAAS,IAAI,KAAK;QACjE;MACF;IACF,CAAC,CAAC;EACN;EAEAqE,iBAAiBA,CAAClF,KAAa,EAAEmF,OAAY;IAC3CjC,OAAO,CAACC,GAAG,CAACgC,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAAC7H,eAAe,CAACwC,UAAU,EAAEsF,QAAQ,CAACpF,KAAK,CAAC,EAAE;QACrD,IAAI,CAAC1C,eAAe,CAACwC,UAAU,EAAEkC,IAAI,CAAChC,KAAK,CAAC;MAC9C;MACAkD,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC7F,eAAe,CAACwC,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACxC,eAAe,CAACwC,UAAU,GAAG,IAAI,CAACxC,eAAe,CAACwC,UAAU,EAAEuF,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKtF,KAAK,CAAC;IAC7F;EACF;EAEAuF,cAAcA,CAAC3C,IAAS;IACtB,OAAOA,IAAI,CAAChC,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAEA4E,gBAAgBA,CAAC5C,IAAS;IACxB,OAAOA,IAAI,CAAC/B,SAAS,GAAG,GAAG,GAAG,GAAG;EACnC;EAIA4E,WAAWA,CAACC,KAAU;IACpB;IACA,IAAI,IAAI,CAACnF,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B;IACF;IACA;IACA,IAAImF,KAAK,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACzD,UAAU,GAAG,CAAC;MACnB,IAAI,CAACxC,yBAAyB,CAACnC,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,IAAI,CAAC2E,UAAU,GAAG,CAAC;MACnB;MACA,IAAI,IAAI,CAACtC,aAAa,IAAI,IAAI,CAACA,aAAa,CAACsB,MAAM,GAAG,CAAC,EAAE;QACvD,IAAI,CAACxB,yBAAyB,CAACnC,YAAY,GAAG,IAAI,CAACqC,aAAa,CAAC,CAAC,CAAC,CAACwB,GAAG;MACzE;IACF;IACA,IAAI,CAACC,OAAO,EAAE;EAChB;EAEA;EACAuE,WAAWA,CAACnD,MAAwB;IAClC,IAAI,CAACrC,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC9C,eAAe,GAAG;MAAEwC,UAAU,EAAE,EAAE;MAAEc,OAAO,EAAE,KAAK;MAAEC,SAAS,EAAE;IAAK,CAAE;IAC3E,IAAI,CAACvD,eAAe,CAACqD,OAAO,GAAG,CAAC;IAChC,IAAI,CAACrD,eAAe,CAAC8E,UAAU,GAAG,CAAC;IACnC;IACA,IAAI,CAAC9E,eAAe,CAACC,YAAY,GAAG,CAAC;IACrC,IAAI,CAACD,eAAe,CAACwC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACxC,IAAI,CAACX,aAAa,CAACuD,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACMoD,cAAcA,CAACjD,IAA2B,EAAEH,MAAwB;IAAA,IAAAqD,MAAA;IAAA,OAAAhD,iBAAA;MACxEgD,MAAI,CAACnG,qBAAqB,CAACoD,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChE+C,MAAI,CAAC1F,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM0F,MAAI,CAAC9C,OAAO,EAAE;QACpB;QACA8C,MAAI,CAACxI,eAAe,CAACwC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACxCgG,MAAI,CAAC3G,aAAa,CAACuD,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA;EACA8C,YAAYA,CAAC1C,GAAQ;IACnB;IACA,IAAI,CAAClG,KAAK,CAACC,KAAK,EAAE;IAClB,IAAI,CAACD,KAAK,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACC,eAAe,CAACE,YAAY,CAAC;IAC9D;IACA,IAAI,IAAI,CAAC0E,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC5E,eAAe,CAACwC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC3C,KAAK,CAACE,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACC,eAAe,CAACwC,UAAU,CAAC;IAC7D,IAAI,CAAC3C,KAAK,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACC,eAAe,CAAC6E,KAAK,CAAC;IACvD,IAAI,CAAChF,KAAK,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACC,eAAe,CAACqD,OAAO,CAAC;IACzD,IAAI,CAACxD,KAAK,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACC,eAAe,CAAC8E,UAAU,CAAC;IAC5D,IAAI,CAACjF,KAAK,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACC,eAAe,CAAC+E,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAAC/E,eAAe,CAACwD,UAAU,IAAI,IAAI,CAACxD,eAAe,CAACwD,UAAU,CAACI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAAC/D,KAAK,CAACmF,aAAa,CAACN,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAC1E,eAAe,CAACiF,OAAO,IAAI,IAAI,CAACjF,eAAe,CAACiF,OAAO,CAACrB,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAAC/D,KAAK,CAACmF,aAAa,CAACN,IAAI,CAAC,kBAAkB,CAAC;IACnD;IAEA,IAAI,IAAI,CAAC7E,KAAK,CAACmF,aAAa,CAACpB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC9B,OAAO,CAACkE,aAAa,CAAC,IAAI,CAACnG,KAAK,CAACmF,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAM0D,YAAY,GAAG;MAAE,GAAG,IAAI,CAAC1I;IAAe,CAAE;IAChD0I,YAAY,CAACzI,YAAY,GAAG,CAAC;IAC7B;IACA,IAAI,IAAI,CAAC2E,UAAU,KAAK,CAAC,EAAE;MACzB8D,YAAY,CAAClG,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClC;IAEA,IAAI,CAACR,kBAAkB,CAACiE,+BAA+B,CAAC;MACtDC,IAAI,EAAEwC;KACP,CAAC,CAACvC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACvE,OAAO,CAACwE,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACvC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACjC,OAAO,CAACyE,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEAkC,kBAAkBA,CAACC,oBAAsC;IACvD,IAAI,CAAC/G,aAAa,CAACuD,IAAI,CAACwD,oBAAoB,CAAC;EAC/C;EAEAC,gBAAgBA,CAACC,GAAa;IAC5B;EAAA;EAGF;EACAC,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACxG,eAAe,CAACwF,MAAM,CAACiB,GAAG,IAAIA,GAAG,CAACvB,QAAQ,CAAC;EACzD;EAEA;EACAwB,4BAA4BA,CAAA;IAC1B;EAAA;EAGF;EACAC,qBAAqBA,CAAA;IACnB,IAAI,CAAC3G,eAAe,CAAC8B,OAAO,CAAC2E,GAAG,IAAIA,GAAG,CAACvB,QAAQ,GAAG,IAAI,CAAC;EAC1D;EAEA;EACA0B,kBAAkBA,CAAA;IAChB,IAAI,CAAC5G,eAAe,CAAC8B,OAAO,CAAC2E,GAAG,IAAIA,GAAG,CAACvB,QAAQ,GAAG,KAAK,CAAC;EAC3D;EAEA;EACA2B,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC7G,eAAe,CAACqB,MAAM,GAAG,CAAC,IAAI,IAAI,CAACrB,eAAe,CAAC8G,KAAK,CAACL,GAAG,IAAIA,GAAG,CAACvB,QAAQ,CAAC;EAC3F;EAEA;EACA6B,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG,IAAI,CAAChH,eAAe,CAACwF,MAAM,CAACiB,GAAG,IAAIA,GAAG,CAACvB,QAAQ,CAAC,CAAC7D,MAAM;IAC7E,OAAO2F,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,IAAI,CAAChH,eAAe,CAACqB,MAAM;EACzE;EAEA;EACA4F,eAAeA,CAACpB,KAAU;IACxB,MAAMqB,SAAS,GAAGrB,KAAK,CAACsB,MAAM,CAAC7B,OAAO;IACtC,IAAI,CAACtF,eAAe,CAAC8B,OAAO,CAAC2E,GAAG,IAAIA,GAAG,CAACvB,QAAQ,GAAGgC,SAAS,CAAC;EAC/D;EAEA;EACAE,mBAAmBA,CAACC,qBAAuC;IACzD,MAAMC,oBAAoB,GAAG,IAAI,CAACd,uBAAuB,EAAE;IAC3D,IAAIc,oBAAoB,CAACjG,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC9B,OAAO,CAACyE,YAAY,CAAC,cAAc,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAACuD,+BAA+B,GAAG,CAAC,GAAGD,oBAAoB,CAAC;IAChE,IAAI,CAACE,kBAAkB,GAAG,IAAI;IAC9B,MAAMC,SAAS,GAAG,IAAI,CAACnI,aAAa,CAACuD,IAAI,CAACwE,qBAAqB,CAAC;IAEhE;IACAI,SAAS,CAACC,OAAO,CAAC9D,SAAS,CAAC,MAAK;MAC/B,IAAI,CAAC4D,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACD,+BAA+B,GAAG,EAAE;IAC3C,CAAC,CAAC;EACJ;EAEA;EACAI,iBAAiBA,CAAA;IACf,IAAI,CAACpI,OAAO,CAACwE,aAAa,CAAC,QAAQ,CAAC;IACpC;IACA,IAAI,CAAC6C,kBAAkB,EAAE;EAC3B;EAEA;EACAgB,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAAC/H,yBAAyB,CAACnC,YAAY,EAAE;MAChD,IAAI,CAAC6B,OAAO,CAACyE,YAAY,CAAC,QAAQ,CAAC;MACnC;IACF;IACA,IAAI,CAACvD,yBAAyB,GAAG,IAAI;IAErC;IACAa,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACuG,8BAA8B,EAAE;QACvC,IAAI,CAACA,8BAA8B,CAAChF,IAAI,EAAE;MAC5C;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAiF,sBAAsBA,CAACC,MAA2B;IAChD1E,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEyE,MAAM,CAAC;IAEhC;IACA;IACA,MAAMC,eAAe,GAAGD,MAAM,CAACE,aAAa,CAAC/G,GAAG,CAAC+D,IAAI,KAAK;MACxDvH,YAAY,EAAE,IAAI,CAACmC,yBAAyB,CAACnC,YAAY;MACzDC,YAAY,EAAE,GAAGoK,MAAM,CAACG,SAAS,IAAIjD,IAAI,CAACkD,aAAa,EAAE;MACzDlH,UAAU,EAAE8G,MAAM,CAACG,SAAS;MAC5B3F,UAAU,EAAE,CAAC;MAAE;MACfC,KAAK,EAAE,GAAG;MAAE;MACZ1B,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,KAAK;MAChBf,UAAU,EAAE,IAAI,CAACI,SAAS,CAACa,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAChB,KAAK,CAAC;MAAE;MACpDuC,OAAO,EAAE,mBAAmBuC,IAAI,CAACmD,WAAW;KAC7C,CAAC,CAAC;IAEH;IACA;IAEA,IAAI,CAAC7I,OAAO,CAACwE,aAAa,CAAC,QAAQgE,MAAM,CAACE,aAAa,CAAC5G,MAAM,KAAK0G,MAAM,CAACG,SAAS,MAAM,CAAC;IAC1F,IAAI,CAAC1G,OAAO,EAAE,CAAC,CAAC;EAClB;EAEA6G,qBAAqBA,CAAA;IACnB,IAAI,CAAC5H,yBAAyB,GAAG,KAAK;EACxC;EAEA;EACQ6H,uBAAuBA,CAACC,YAAmB;IACjD;IACA;EAAA;CAEH;AAvdqCC,UAAA,EAAnCrL,SAAS,CAAC,uBAAuB,CAAC,C,qFAAiE;AAlCzF+B,8BAA8B,GAAAsJ,UAAA,EAvB1CtL,SAAS,CAAC;EACTuL,QAAQ,EAAE,4BAA4B;EACtCC,UAAU,EAAE,IAAI;EAAEC,OAAO,EAAE,CACzBC,YAAY,EACZtK,mBAAmB,EACnBP,aAAa,EACbQ,WAAW,EACXN,cAAc,EACdD,cAAc,EACdS,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVd,gBAAgB,EAChBe,kBAAkB,EAClBC,oBAAoB,EACpBE,uBAAuB,EACvBC,wBAAwB,EACxBC,8BAA8B,CAC/B;EACD4J,WAAW,EAAE,yCAAyC;EACtDC,QAAQ,EAAE;CACX,CAAC,C,EACW5J,8BAA8B,CAyf1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}