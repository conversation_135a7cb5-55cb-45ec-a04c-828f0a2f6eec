{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport function getItemVisualFromData(data, dataIndex, key) {\n  switch (key) {\n    case 'color':\n      var style = data.getItemVisual(dataIndex, 'style');\n      return style[data.getVisual('drawType')];\n    case 'opacity':\n      return data.getItemVisual(dataIndex, 'style').opacity;\n    case 'symbol':\n    case 'symbolSize':\n    case 'liftZ':\n      return data.getItemVisual(dataIndex, key);\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\"Unknown visual type \" + key);\n      }\n  }\n}\nexport function getVisualFromData(data, key) {\n  switch (key) {\n    case 'color':\n      var style = data.getVisual('style');\n      return style[data.getVisual('drawType')];\n    case 'opacity':\n      return data.getVisual('style').opacity;\n    case 'symbol':\n    case 'symbolSize':\n    case 'liftZ':\n      return data.getVisual(key);\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\"Unknown visual type \" + key);\n      }\n  }\n}\nexport function setItemVisualFromData(data, dataIndex, key, value) {\n  switch (key) {\n    case 'color':\n      // Make sure not sharing style object.\n      var style = data.ensureUniqueItemVisual(dataIndex, 'style');\n      style[data.getVisual('drawType')] = value;\n      // Mark the color has been changed, not from palette anymore\n      data.setItemVisual(dataIndex, 'colorFromPalette', false);\n      break;\n    case 'opacity':\n      data.ensureUniqueItemVisual(dataIndex, 'style').opacity = value;\n      break;\n    case 'symbol':\n    case 'symbolSize':\n    case 'liftZ':\n      data.setItemVisual(dataIndex, key, value);\n      break;\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\"Unknown visual type \" + key);\n      }\n  }\n}", "map": {"version": 3, "names": ["getItemVisualFromData", "data", "dataIndex", "key", "style", "getItemVisual", "getVisual", "opacity", "process", "env", "NODE_ENV", "console", "warn", "getVisualFromData", "setItemVisualFromData", "value", "ensureUniqueItemVisual", "setItemVisual"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/visual/helper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport function getItemVisualFromData(data, dataIndex, key) {\n  switch (key) {\n    case 'color':\n      var style = data.getItemVisual(dataIndex, 'style');\n      return style[data.getVisual('drawType')];\n    case 'opacity':\n      return data.getItemVisual(dataIndex, 'style').opacity;\n    case 'symbol':\n    case 'symbolSize':\n    case 'liftZ':\n      return data.getItemVisual(dataIndex, key);\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\"Unknown visual type \" + key);\n      }\n  }\n}\nexport function getVisualFromData(data, key) {\n  switch (key) {\n    case 'color':\n      var style = data.getVisual('style');\n      return style[data.getVisual('drawType')];\n    case 'opacity':\n      return data.getVisual('style').opacity;\n    case 'symbol':\n    case 'symbolSize':\n    case 'liftZ':\n      return data.getVisual(key);\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\"Unknown visual type \" + key);\n      }\n  }\n}\nexport function setItemVisualFromData(data, dataIndex, key, value) {\n  switch (key) {\n    case 'color':\n      // Make sure not sharing style object.\n      var style = data.ensureUniqueItemVisual(dataIndex, 'style');\n      style[data.getVisual('drawType')] = value;\n      // Mark the color has been changed, not from palette anymore\n      data.setItemVisual(dataIndex, 'colorFromPalette', false);\n      break;\n    case 'opacity':\n      data.ensureUniqueItemVisual(dataIndex, 'style').opacity = value;\n      break;\n    case 'symbol':\n    case 'symbolSize':\n    case 'liftZ':\n      data.setItemVisual(dataIndex, key, value);\n      break;\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\"Unknown visual type \" + key);\n      }\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,qBAAqBA,CAACC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAE;EAC1D,QAAQA,GAAG;IACT,KAAK,OAAO;MACV,IAAIC,KAAK,GAAGH,IAAI,CAACI,aAAa,CAACH,SAAS,EAAE,OAAO,CAAC;MAClD,OAAOE,KAAK,CAACH,IAAI,CAACK,SAAS,CAAC,UAAU,CAAC,CAAC;IAC1C,KAAK,SAAS;MACZ,OAAOL,IAAI,CAACI,aAAa,CAACH,SAAS,EAAE,OAAO,CAAC,CAACK,OAAO;IACvD,KAAK,QAAQ;IACb,KAAK,YAAY;IACjB,KAAK,OAAO;MACV,OAAON,IAAI,CAACI,aAAa,CAACH,SAAS,EAAEC,GAAG,CAAC;IAC3C;MACE,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,IAAI,CAAC,sBAAsB,GAAGT,GAAG,CAAC;MAC5C;EACJ;AACF;AACA,OAAO,SAASU,iBAAiBA,CAACZ,IAAI,EAAEE,GAAG,EAAE;EAC3C,QAAQA,GAAG;IACT,KAAK,OAAO;MACV,IAAIC,KAAK,GAAGH,IAAI,CAACK,SAAS,CAAC,OAAO,CAAC;MACnC,OAAOF,KAAK,CAACH,IAAI,CAACK,SAAS,CAAC,UAAU,CAAC,CAAC;IAC1C,KAAK,SAAS;MACZ,OAAOL,IAAI,CAACK,SAAS,CAAC,OAAO,CAAC,CAACC,OAAO;IACxC,KAAK,QAAQ;IACb,KAAK,YAAY;IACjB,KAAK,OAAO;MACV,OAAON,IAAI,CAACK,SAAS,CAACH,GAAG,CAAC;IAC5B;MACE,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,IAAI,CAAC,sBAAsB,GAAGT,GAAG,CAAC;MAC5C;EACJ;AACF;AACA,OAAO,SAASW,qBAAqBA,CAACb,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAEY,KAAK,EAAE;EACjE,QAAQZ,GAAG;IACT,KAAK,OAAO;MACV;MACA,IAAIC,KAAK,GAAGH,IAAI,CAACe,sBAAsB,CAACd,SAAS,EAAE,OAAO,CAAC;MAC3DE,KAAK,CAACH,IAAI,CAACK,SAAS,CAAC,UAAU,CAAC,CAAC,GAAGS,KAAK;MACzC;MACAd,IAAI,CAACgB,aAAa,CAACf,SAAS,EAAE,kBAAkB,EAAE,KAAK,CAAC;MACxD;IACF,KAAK,SAAS;MACZD,IAAI,CAACe,sBAAsB,CAACd,SAAS,EAAE,OAAO,CAAC,CAACK,OAAO,GAAGQ,KAAK;MAC/D;IACF,KAAK,QAAQ;IACb,KAAK,YAAY;IACjB,KAAK,OAAO;MACVd,IAAI,CAACgB,aAAa,CAACf,SAAS,EAAEC,GAAG,EAAEY,KAAK,CAAC;MACzC;IACF;MACE,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,IAAI,CAAC,sBAAsB,GAAGT,GAAG,CAAC;MAC5C;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}