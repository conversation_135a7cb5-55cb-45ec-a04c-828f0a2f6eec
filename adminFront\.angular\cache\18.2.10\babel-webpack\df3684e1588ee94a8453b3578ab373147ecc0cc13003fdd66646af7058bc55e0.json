{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule } from '@nebular/theme';\nimport { TemplateGetListResponse } from 'src/services/api/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"nb-checkbox\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const template_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(template_r2.selected, $event) || (template_r2.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onTemplateItemChange());\n    });\n    i0.ɵɵelementStart(2, \"div\", 27)(3, \"div\")(4, \"div\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 30);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 31);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const template_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", template_r2.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(template_r2.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", template_r2.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u72C0\\u614B: \", template_r2.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u985E\\u578B: \", template_r2.CTemplateType, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template, 12, 5, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templates);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"nb-icon\", 33);\n    i0.ɵɵtext(2, \" \\u66AB\\u7121\\u53EF\\u7528\\u7684\\u6A21\\u677F\\u9805\\u76EE\\uFF0C\\u8ACB\\u7A0D\\u5F8C\\u518D\\u8A66\\u6216\\u806F\\u7E6B\\u7BA1\\u7406\\u54E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"nb-icon\", 21);\n    i0.ɵɵtext(4, \"\\u9078\\u64C7\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 22);\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_12_div_6_Template, 2, 1, \"div\", 23)(7, SpaceTemplateSelectorComponent_div_12_ng_template_7_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const noTemplates_r4 = i0.ɵɵreference(8);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templates.length > 0)(\"ngIfElse\", noTemplates_r4);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u4F4D\\u7F6E: \", detail_r5.CLocation, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"div\", 57);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template, 2, 1, \"div\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r6 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r5.CPart);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r5.CLocation);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"span\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 52);\n    i0.ɵɵtemplate(5, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template, 7, 3, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5305\\u542B \", ctx_r2.getTemplateDetails(item_r7.CTemplateId).length, \" \\u500B\\u660E\\u7D30\\u9805\\u76EE\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getTemplateDetails(item_r7.CTemplateId));\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"nb-icon\", 33);\n    i0.ɵɵtext(2, \" \\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u660E\\u7D30\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"h5\", 43);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 44)(5, \"span\", 45);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 46);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 47);\n    i0.ɵɵtemplate(10, SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template, 6, 2, \"div\", 48)(11, SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template, 3, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const noDetails_r8 = i0.ɵɵreference(12);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r7.CTemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", item_r7.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTemplateDetails(item_r7.CTemplateId).length > 0)(\"ngIfElse\", noDetails_r8);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"strong\");\n    i0.ɵɵelement(3, \"nb-icon\", 63);\n    i0.ɵɵtext(4, \"\\u885D\\u7A81\\u6AA2\\u6E2C\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6AA2\\u6E2C\\u5230 \", ctx_r2.getConflictCount(), \" \\u500B\\u9805\\u76EE\\u53EF\\u80FD\\u8207\\u73FE\\u6709\\u9700\\u6C42\\u91CD\\u8907\\uFF0C\\u7CFB\\u7D71\\u5C07\\u81EA\\u52D5\\u8655\\u7406\\u885D\\u7A81\\u9805\\u76EE\\u3002 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 34)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"nb-icon\", 35);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u5957\\u7528\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36)(6, \"div\", 37);\n    i0.ɵɵtext(7, \" \\u5C07\\u5957\\u7528 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9, \"\\u901A\\u7528\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_13_div_12_Template, 13, 5, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SpaceTemplateSelectorComponent_div_13_div_13_Template, 6, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\\uFF1A\", ctx_r2.getSelectedItems().length, \"\\u500B\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSelectedItems());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasConflicts());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.previousStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0B\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canProceed());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyTemplate());\n    });\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u5957\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.getSelectedItems().length === 0);\n  }\n}\nexport class SpaceTemplateSelectorComponent {\n  constructor(templateService, dialogRef) {\n    this.templateService = templateService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = '';\n    this.templateApplied = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templates = []; // 直接使用 API 資料\n    this.selectedTemplateDetails = new Map(); // 存儲已載入的模板詳情\n  }\n  ngOnInit() {\n    // 移除自動載入，改為在開啟時才載入\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: 1,\n      // 1=客變需求\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 直接使用 API 資料，只添加 selected 屬性\n          this.templates = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        this.templates = [];\n      }\n    });\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  getSelectedItems() {\n    return this.templates.filter(item => item.selected);\n  }\n  getSelectedTotalPrice() {\n    // 由於 API 沒有價格資訊，返回 0\n    return 0;\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      if (this.currentStep === 1) {\n        // 進入步驟2前，載入選中模板的詳情\n        this.loadSelectedTemplateDetails();\n      }\n      this.currentStep++;\n    }\n  }\n  loadSelectedTemplateDetails() {\n    const selectedItems = this.getSelectedItems();\n    selectedItems.forEach(item => {\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\n        // 只載入尚未載入過的模板詳情\n        this.loadTemplateDetailById(item.CTemplateId);\n      }\n    });\n  }\n  loadTemplateDetailById(templateId) {\n    const args = {\n      templateId: templateId\n    };\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          this.selectedTemplateDetails.set(templateId, response.Entries);\n        }\n      },\n      error: () => {\n        // 錯誤處理：設置空陣列\n        this.selectedTemplateDetails.set(templateId, []);\n      }\n    });\n  }\n  getTemplateDetails(templateId) {\n    return this.selectedTemplateDetails.get(templateId) || [];\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要套用的模板項目',\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: '通用模板',\n      selectedItems: this.getSelectedItems(),\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.isVisible = false;\n    this.resetSelections();\n    this.closed.emit();\n  }\n  onBackdropClick(event) {\n    if (event.target === event.currentTarget) {\n      this.close();\n    }\n  }\n  reset() {\n    this.currentStep = 1;\n    this.templates = [];\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    // 保留 templates 資料，只重置選擇狀態\n    this.templates.forEach(template => {\n      template.selected = false;\n    });\n    // 清空詳情快取\n    this.selectedTemplateDetails.clear();\n  }\n  // 公共API方法\n  open() {\n    this.isVisible = true;\n    if (this.templates.length === 0) {\n      // 只有在沒有資料時才載入\n      this.loadTemplatesFromAPI();\n    } else {\n      // 如果已有資料，只重置選擇狀態和步驟\n      this.currentStep = 1;\n      this.templates.forEach(template => {\n        template.selected = false;\n      });\n      this.selectedTemplateDetails.clear();\n    }\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService), i0.ɵɵdirectiveInject(i2.NbDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceTemplateSelectorComponent,\n      selectors: [[\"app-space-template-selector\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\"\n      },\n      outputs: {\n        templateApplied: \"templateApplied\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 24,\n      vars: 16,\n      consts: [[\"noTemplates\", \"\"], [\"noDetails\", \"\"], [1, \"space-template-dialog\"], [1, \"space-template-header\"], [1, \"space-template-title\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"close-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"space-template-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"space-template-footer\"], [1, \"progress-info\"], [1, \"step-buttons\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"template-selection\"], [1, \"section-title\"], [\"icon\", \"layers-outline\", 1, \"mr-2\"], [1, \"template-list\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-item\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"template-info\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"item-status\"], [1, \"item-type\"], [1, \"no-templates\"], [\"icon\", \"info-outline\", 1, \"mr-2\"], [1, \"confirmation-area\"], [\"icon\", \"checkmark-circle-outline\", 1, \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"selected-templates-details\"], [\"class\", \"template-detail-section\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"conflict-warning\", 4, \"ngIf\"], [1, \"template-detail-section\"], [1, \"template-detail-header\"], [1, \"template-name\"], [1, \"template-meta\"], [1, \"template-id\"], [1, \"template-status\"], [1, \"template-detail-content\"], [\"class\", \"detail-items\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-items\"], [1, \"detail-items-header\"], [1, \"detail-count\"], [1, \"detail-items-list\"], [\"class\", \"detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\"], [1, \"detail-index\"], [1, \"detail-info\"], [1, \"detail-part\"], [\"class\", \"detail-location\", 4, \"ngIf\"], [1, \"detail-location\"], [1, \"no-details\"], [1, \"conflict-warning\"], [1, \"warning-text\"], [\"icon\", \"alert-triangle-outline\", 1, \"mr-1\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\", \"disabled\"]],\n      template: function SpaceTemplateSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\", 3)(2, \"div\", 4);\n          i0.ɵɵtext(3, \"\\u7A7A\\u9593\\u6A21\\u677F\\u9078\\u64C7\\u5668\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_4_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(5, \"nb-icon\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 7)(7, \"div\", 8)(8, \"div\", 9);\n          i0.ɵɵtext(9, \"1. \\u9078\\u64C7\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 9);\n          i0.ɵɵtext(11, \"2. \\u78BA\\u8A8D\\u5957\\u7528\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_12_Template, 9, 2, \"div\", 10)(13, SpaceTemplateSelectorComponent_div_13_Template, 14, 3, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"nb-card-footer\", 11)(15, \"div\", 12)(16, \"span\");\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_19_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(20, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(21, SpaceTemplateSelectorComponent_button_21_Template, 2, 0, \"button\", 15)(22, SpaceTemplateSelectorComponent_button_22_Template, 2, 1, \"button\", 16)(23, SpaceTemplateSelectorComponent_button_23_Template, 2, 1, \"button\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(8, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(12, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.getProgressText());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, FormsModule, i4.NgControlStatus, i4.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbIconModule, i2.NbIconComponent, NbCheckboxModule, i2.NbCheckboxComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.space-template-modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.space-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--color-basic-800);\\n  margin-bottom: 4px;\\n}\\n\\n.space-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-basic-600);\\n}\\n\\n.space-template-modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.space-template-content[_ngcontent-%COMP%] {\\n  background: var(--color-basic-100);\\n  border-radius: 12px;\\n  width: 90%;\\n  max-width: 1000px;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n\\n.space-template-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);\\n  color: var(--color-basic-100);\\n  padding: 20px 30px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.space-template-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  color: var(--color-basic-100);\\n  font-size: 24px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  border: none;\\n  background: none;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: background 0.3s ease;\\n}\\n\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n.space-template-body[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n\\n\\n\\n.step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n  gap: 10px;\\n}\\n\\n.step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n\\n.step-item.active[_ngcontent-%COMP%] {\\n  background: var(--color-primary-600);\\n  color: var(--color-basic-100);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.3);\\n}\\n\\n.step-item.completed[_ngcontent-%COMP%] {\\n  background: var(--color-success-600);\\n  color: var(--color-basic-100);\\n}\\n\\n.step-item.pending[_ngcontent-%COMP%] {\\n  background: var(--color-basic-200);\\n  color: var(--color-basic-600);\\n}\\n\\n\\n\\n.space-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-basic-800);\\n  margin-bottom: 15px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.space-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n\\n.space-card[_ngcontent-%COMP%] {\\n  border: 2px solid var(--color-basic-300);\\n  border-radius: 8px;\\n  padding: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-align: center;\\n  background: var(--color-basic-100);\\n}\\n\\n.space-card[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-primary-500);\\n  background: var(--color-primary-100);\\n}\\n\\n.space-card.selected[_ngcontent-%COMP%] {\\n  border-color: var(--color-primary-600);\\n  background: var(--color-primary-100);\\n}\\n\\n.space-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 8px;\\n}\\n\\n.space-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 4px;\\n}\\n\\n.space-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n\\n\\n.template-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.template-list[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 15px;\\n}\\n\\n.template-groups[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 15px;\\n}\\n\\n.template-group[_ngcontent-%COMP%] {\\n  border: 1px solid var(--color-basic-300);\\n  border-radius: 8px;\\n  overflow: hidden;\\n  background: var(--color-basic-100);\\n}\\n\\n.template-group-header[_ngcontent-%COMP%] {\\n  background: var(--color-basic-200);\\n  padding: 16px 20px;\\n  cursor: pointer;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  transition: background 0.3s ease;\\n}\\n\\n.template-group-header[_ngcontent-%COMP%]:hover {\\n  background: var(--color-basic-300);\\n}\\n\\n.template-group-header.active[_ngcontent-%COMP%] {\\n  background: var(--color-primary-100);\\n  border-color: var(--color-primary-400);\\n  border-left: 4px solid var(--color-primary-600);\\n}\\n\\n.group-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.group-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.group-stats[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n.group-toggle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  transition: transform 0.3s ease;\\n}\\n\\n.group-toggle.expanded[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.template-items[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n\\n.template-items.expanded[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n}\\n\\n.template-item[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  border: 1px solid var(--color-basic-300);\\n  border-radius: 8px;\\n  background: var(--color-basic-100);\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  transition: all 0.2s ease;\\n}\\n\\n.template-item[_ngcontent-%COMP%]:hover {\\n  background: var(--color-basic-200);\\n  border-color: var(--color-primary-300);\\n}\\n\\n.template-checkbox[_ngcontent-%COMP%] {\\n  transform: scale(1.2);\\n}\\n\\n.template-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr;\\n  gap: 20px;\\n  align-items: center;\\n}\\n\\n.item-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.item-code[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 12px;\\n}\\n\\n.item-status[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  text-align: center;\\n}\\n\\n.item-type[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-basic-600);\\n  text-align: center;\\n}\\n\\n.item-price[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n\\n.price-input[_ngcontent-%COMP%] {\\n  width: 90px;\\n  padding: 4px 8px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  text-align: right;\\n  font-size: 12px;\\n}\\n\\n\\n\\n.confirmation-area[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.selected-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.summary-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.summary-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n\\n.selected-items-list[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 15px;\\n  border-radius: 6px;\\n  margin-bottom: 15px;\\n}\\n\\n.items-list-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 10px;\\n}\\n\\n.items-list[_ngcontent-%COMP%] {\\n  margin-left: 20px;\\n  line-height: 1.6;\\n}\\n\\n.conflict-warning[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n\\n.warning-text[_ngcontent-%COMP%] {\\n  color: #856404;\\n  font-size: 14px;\\n}\\n\\n\\n\\n.space-template-footer[_ngcontent-%COMP%] {\\n  padding: 20px 30px;\\n  border-top: 1px solid #e9ecef;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background: #f8f9fa;\\n  border-radius: 0 0 12px 12px;\\n}\\n\\n.step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.selected-templates-details[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n.template-detail-section[_ngcontent-%COMP%] {\\n  border: 1px solid var(--color-basic-300);\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n  overflow: hidden;\\n  background: var(--color-basic-100);\\n}\\n\\n.template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--color-primary-100) 0%, var(--color-primary-200) 100%);\\n  padding: 16px 20px;\\n  border-bottom: 1px solid var(--color-basic-300);\\n}\\n\\n.template-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-primary-700);\\n  margin: 0 0 8px 0;\\n}\\n\\n.template-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  font-size: 14px;\\n}\\n\\n.template-id[_ngcontent-%COMP%] {\\n  color: var(--color-basic-600);\\n}\\n\\n.template-status[_ngcontent-%COMP%] {\\n  color: var(--color-success-600);\\n  font-weight: 500;\\n}\\n\\n.template-detail-content[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n}\\n\\n.detail-items-header[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n\\n.detail-count[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-basic-700);\\n}\\n\\n.detail-items-list[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 8px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px;\\n  background: var(--color-basic-200);\\n  border-radius: 6px;\\n  gap: 12px;\\n}\\n\\n.detail-index[_ngcontent-%COMP%] {\\n  background: var(--color-primary-600);\\n  color: white;\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 600;\\n  flex-shrink: 0;\\n}\\n\\n.detail-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.detail-part[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--color-basic-800);\\n  margin-bottom: 4px;\\n}\\n\\n.detail-location[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-basic-600);\\n}\\n\\n.no-details[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 20px;\\n  color: var(--color-basic-500);\\n  font-style: italic;\\n}\\n\\n.no-templates[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: var(--color-basic-500);\\n  background: var(--color-basic-200);\\n  border-radius: 8px;\\n  margin: 20px 0;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    transform: translateY(-50px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  .template-info[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n  .space-template-content[_ngcontent-%COMP%] {\\n    width: 95%;\\n    margin: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "TemplateGetListResponse", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener", "$event", "template_r2", "ɵɵrestoreView", "_r1", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "ctx_r2", "ɵɵnextContext", "onTemplateItemChange", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CTemplateName", "ɵɵtextInterpolate1", "CTemplateId", "CStatus", "CTemplateType", "ɵɵtemplate", "SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template", "ɵɵproperty", "templates", "ɵɵelement", "SpaceTemplateSelectorComponent_div_12_div_6_Template", "SpaceTemplateSelectorComponent_div_12_ng_template_7_Template", "ɵɵtemplateRefExtractor", "length", "noTemplates_r4", "detail_r5", "CLocation", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template", "i_r6", "<PERSON>art", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template", "getTemplateDetails", "item_r7", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template", "SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template", "noDetails_r8", "getConflictCount", "SpaceTemplateSelectorComponent_div_13_div_12_Template", "SpaceTemplateSelectorComponent_div_13_div_13_Template", "getSelectedItems", "hasConflicts", "SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener", "_r9", "previousStep", "SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener", "_r10", "nextStep", "canProceed", "SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener", "_r11", "applyTemplate", "SpaceTemplateSelectorComponent", "constructor", "templateService", "dialogRef", "buildCaseId", "templateApplied", "currentStep", "selectedTemplateDetails", "Map", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "PageIndex", "PageSize", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "error", "filter", "getSelectedTotalPrice", "loadSelectedTemplateDetails", "selectedItems", "for<PERSON>ach", "has", "loadTemplateDetailById", "templateId", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "set", "get", "getProgressText", "progressTexts", "config", "spaceId", "spaceName", "totalPrice", "emit", "close", "isVisible", "resetSelections", "closed", "onBackdropClick", "event", "target", "currentTarget", "reset", "template", "clear", "open", "ɵɵdirectiveInject", "i1", "TemplateService", "i2", "NbDialogRef", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "SpaceTemplateSelectorComponent_Template", "rf", "ctx", "SpaceTemplateSelectorComponent_Template_button_click_4_listener", "SpaceTemplateSelectorComponent_div_12_Template", "SpaceTemplateSelectorComponent_div_13_Template", "SpaceTemplateSelectorComponent_Template_button_click_19_listener", "SpaceTemplateSelectorComponent_button_21_Template", "SpaceTemplateSelectorComponent_button_22_Template", "SpaceTemplateSelectorComponent_button_23_Template", "ɵɵpureFunction3", "_c0", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbIconComponent", "NbCheckboxComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { \r\n  NbCardModule, \r\n  NbButtonModule, \r\n  NbIconModule, \r\n  NbCheckboxModule,\r\n  NbDialogRef\r\n} from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, TemplateGetListResponse, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';\r\n\r\n// 擴展 API 模型以支援前端選擇功能\r\nexport interface ExtendedTemplateItem extends TemplateGetListResponse {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: ExtendedTemplateItem[];\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule\r\n  ],\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() buildCaseId: string = '';\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n\r\n  currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n  templates: ExtendedTemplateItem[] = []; // 直接使用 API 資料\r\n  selectedTemplateDetails: Map<number, TemplateDetailItem[]> = new Map(); // 存儲已載入的模板詳情\r\n\r\n  constructor(\r\n    private templateService: TemplateService,\r\n    private dialogRef: NbDialogRef<SpaceTemplateSelectorComponent>\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    // 移除自動載入，改為在開啟時才載入\r\n  }\r\n\r\n  loadTemplatesFromAPI() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: 1, // 1=客變需求\r\n      PageIndex: 1,\r\n      PageSize: 100, // 載入足夠的資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateListForCommon API\r\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // 直接使用 API 資料，只添加 selected 屬性\r\n          this.templates = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  getSelectedItems(): ExtendedTemplateItem[] {\r\n    return this.templates.filter(item => item.selected);\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    // 由於 API 沒有價格資訊，返回 0\r\n    return 0;\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      if (this.currentStep === 1) {\r\n        // 進入步驟2前，載入選中模板的詳情\r\n        this.loadSelectedTemplateDetails();\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  loadSelectedTemplateDetails() {\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    selectedItems.forEach(item => {\r\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\r\n        // 只載入尚未載入過的模板詳情\r\n        this.loadTemplateDetailById(item.CTemplateId);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTemplateDetailById(templateId: number) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.selectedTemplateDetails.set(templateId, response.Entries);\r\n        }\r\n      },\r\n      error: () => {\r\n        // 錯誤處理：設置空陣列\r\n        this.selectedTemplateDetails.set(templateId, []);\r\n      }\r\n    });\r\n  }\r\n\r\n  getTemplateDetails(templateId: number): TemplateDetailItem[] {\r\n    return this.selectedTemplateDetails.get(templateId) || [];\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要套用的模板項目',\r\n      2: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: 'common', // 通用模板，不特定空間\r\n      spaceName: '通用模板',\r\n      selectedItems: this.getSelectedItems(),\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.isVisible = false;\r\n    this.resetSelections();\r\n    this.closed.emit();\r\n  }\r\n\r\n  onBackdropClick(event: Event) {\r\n    if (event.target === event.currentTarget) {\r\n      this.close();\r\n    }\r\n  }\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.templates = [];\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    // 保留 templates 資料，只重置選擇狀態\r\n    this.templates.forEach(template => {\r\n      template.selected = false;\r\n    });\r\n    // 清空詳情快取\r\n    this.selectedTemplateDetails.clear();\r\n  }\r\n\r\n  // 公共API方法\r\n  open() {\r\n    this.isVisible = true;\r\n    if (this.templates.length === 0) {\r\n      // 只有在沒有資料時才載入\r\n      this.loadTemplatesFromAPI();\r\n    } else {\r\n      // 如果已有資料，只重置選擇狀態和步驟\r\n      this.currentStep = 1;\r\n      this.templates.forEach(template => {\r\n        template.selected = false;\r\n      });\r\n      this.selectedTemplateDetails.clear();\r\n    }\r\n  }\r\n}\r\n", "<!-- 空間模板選擇器共用元件 - 使用 nb-dialog -->\r\n<nb-card class=\"space-template-dialog\">\r\n  <nb-card-header class=\"space-template-header\">\r\n    <div class=\"space-template-title\">空間模板選擇器</div>\r\n    <button class=\"close-btn\" nbButton ghost (click)=\"close()\">\r\n      <nb-icon icon=\"close-outline\"></nb-icon>\r\n    </button>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body class=\"space-template-body\">\r\n    <!-- 步驟導航 -->\r\n    <div class=\"step-nav\">\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 1,\r\n        'completed': currentStep > 1,\r\n        'pending': currentStep < 1\r\n      }\">1. 選擇模板</div>\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 2,\r\n        'completed': currentStep > 2,\r\n        'pending': currentStep < 2\r\n      }\">2. 確認套用</div>\r\n    </div>\r\n\r\n    <!-- 步驟1: 選擇模板 -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <div class=\"template-selection\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"layers-outline\" class=\"mr-2\"></nb-icon>選擇模板項目\r\n        </div>\r\n        <div class=\"template-list\">\r\n          <div *ngIf=\"templates.length > 0; else noTemplates\">\r\n            <div *ngFor=\"let template of templates\" class=\"template-item\">\r\n              <nb-checkbox [(ngModel)]=\"template.selected\" (ngModelChange)=\"onTemplateItemChange()\">\r\n                <div class=\"template-info\">\r\n                  <div>\r\n                    <div class=\"item-name\">{{ template.CTemplateName }}</div>\r\n                    <div class=\"item-code\">ID: {{ template.CTemplateId }}</div>\r\n                  </div>\r\n                  <div class=\"item-status\">\r\n                    狀態: {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n                  </div>\r\n                  <div class=\"item-type\">\r\n                    類型: {{ template.CTemplateType }}\r\n                  </div>\r\n                </div>\r\n              </nb-checkbox>\r\n            </div>\r\n          </div>\r\n          <ng-template #noTemplates>\r\n            <div class=\"no-templates\">\r\n              <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n              暫無可用的模板項目，請稍後再試或聯繫管理員\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 步驟2: 確認套用 -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <div class=\"confirmation-area\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"checkmark-circle-outline\" class=\"mr-2\"></nb-icon>確認套用詳情\r\n        </div>\r\n\r\n        <div class=\"selected-summary\">\r\n          <div class=\"summary-text\">\r\n            將套用 <strong>通用模板</strong>：{{ getSelectedItems().length }}個模板項目\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 選中的模板詳情展開 -->\r\n        <div class=\"selected-templates-details\">\r\n          <div *ngFor=\"let item of getSelectedItems()\" class=\"template-detail-section\">\r\n            <div class=\"template-detail-header\">\r\n              <h5 class=\"template-name\">{{ item.CTemplateName }}</h5>\r\n              <div class=\"template-meta\">\r\n                <span class=\"template-id\">ID: {{ item.CTemplateId }}</span>\r\n                <span class=\"template-status\">{{ item.CStatus === 1 ? '啟用' : '停用' }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"template-detail-content\">\r\n              <div *ngIf=\"getTemplateDetails(item.CTemplateId!).length > 0; else noDetails\" class=\"detail-items\">\r\n                <div class=\"detail-items-header\">\r\n                  <span class=\"detail-count\">包含 {{ getTemplateDetails(item.CTemplateId!).length }} 個明細項目：</span>\r\n                </div>\r\n                <div class=\"detail-items-list\">\r\n                  <div *ngFor=\"let detail of getTemplateDetails(item.CTemplateId!); let i = index\"\r\n                    class=\"detail-item\">\r\n                    <div class=\"detail-index\">{{ i + 1 }}</div>\r\n                    <div class=\"detail-info\">\r\n                      <div class=\"detail-part\">{{ detail.CPart }}</div>\r\n                      <div class=\"detail-location\" *ngIf=\"detail.CLocation\">\r\n                        位置: {{ detail.CLocation }}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <ng-template #noDetails>\r\n                <div class=\"no-details\">\r\n                  <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n                  此模板暫無明細項目\r\n                </div>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"hasConflicts()\" class=\"conflict-warning\">\r\n          <div class=\"warning-text\">\r\n            <strong><nb-icon icon=\"alert-triangle-outline\" class=\"mr-1\"></nb-icon>衝突檢測：</strong>\r\n            檢測到 {{ getConflictCount() }} 個項目可能與現有需求重複，系統將自動處理衝突項目。\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-footer class=\"space-template-footer\">\r\n    <div class=\"progress-info\">\r\n      <span>{{ getProgressText() }}</span>\r\n    </div>\r\n    <div class=\"step-buttons\">\r\n      <button nbButton status=\"basic\" (click)=\"close()\">取消</button>\r\n      <button *ngIf=\"currentStep > 1\" nbButton status=\"basic\" (click)=\"previousStep()\">上一步</button>\r\n      <button *ngIf=\"currentStep < 2\" nbButton status=\"primary\" [disabled]=\"!canProceed()\"\r\n        (click)=\"nextStep()\">下一步</button>\r\n      <button *ngIf=\"currentStep === 2\" nbButton status=\"success\" [disabled]=\"getSelectedItems().length === 0\"\r\n        (click)=\"applyTemplate()\">確認套用</button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,QAEX,gBAAgB;AAEvB,SAA8BC,uBAAuB,QAAuD,yBAAyB;;;;;;;;;;;;;;ICsBvHC,EADF,CAAAC,cAAA,cAA8D,sBAC0B;IAAzED,EAAA,CAAAE,gBAAA,2BAAAC,gGAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAJ,WAAA,CAAAK,QAAA,EAAAN,MAAA,MAAAC,WAAA,CAAAK,QAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAACJ,EAAA,CAAAY,UAAA,2BAAAT,gGAAA;MAAAH,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAAiBE,MAAA,CAAAE,oBAAA,EAAsB;IAAA,EAAC;IAG/Ef,EAFJ,CAAAC,cAAA,cAA2B,UACpB,cACoB;IAAAD,EAAA,CAAAgB,MAAA,GAA4B;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACzDjB,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAgB,MAAA,GAA8B;IACvDhB,EADuD,CAAAiB,YAAA,EAAM,EACvD;IACNjB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAgB,MAAA,IACF;IAGNhB,EAHM,CAAAiB,YAAA,EAAM,EACF,EACM,EACV;;;;IAdSjB,EAAA,CAAAkB,SAAA,EAA+B;IAA/BlB,EAAA,CAAAmB,gBAAA,YAAAd,WAAA,CAAAK,QAAA,CAA+B;IAGfV,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAAoB,iBAAA,CAAAf,WAAA,CAAAgB,aAAA,CAA4B;IAC5BrB,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAAsB,kBAAA,SAAAjB,WAAA,CAAAkB,WAAA,KAA8B;IAGrDvB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAjB,WAAA,CAAAmB,OAAA,8CACF;IAEExB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAjB,WAAA,CAAAoB,aAAA,MACF;;;;;IAbRzB,EAAA,CAAAC,cAAA,UAAoD;IAClDD,EAAA,CAAA0B,UAAA,IAAAC,0DAAA,mBAA8D;IAgBhE3B,EAAA,CAAAiB,YAAA,EAAM;;;;IAhBsBjB,EAAA,CAAAkB,SAAA,EAAY;IAAZlB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAAgB,SAAA,CAAY;;;;;IAkBtC7B,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA8B,SAAA,kBAAoD;IACpD9B,EAAA,CAAAgB,MAAA,uIACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;;IA1BVjB,EAFJ,CAAAC,cAAA,cAAoD,cAClB,cACH;IACzBD,EAAA,CAAA8B,SAAA,kBAAsD;IAAA9B,EAAA,CAAAgB,MAAA,4CACxD;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,cAA2B;IAmBzBD,EAlBA,CAAA0B,UAAA,IAAAK,oDAAA,kBAAoD,IAAAC,4DAAA,gCAAAhC,EAAA,CAAAiC,sBAAA,CAkB1B;IAQhCjC,EAFI,CAAAiB,YAAA,EAAM,EACF,EACF;;;;;IA1BMjB,EAAA,CAAAkB,SAAA,GAA4B;IAAAlB,EAA5B,CAAA4B,UAAA,SAAAf,MAAA,CAAAgB,SAAA,CAAAK,MAAA,KAA4B,aAAAC,cAAA,CAAgB;;;;;IA+DtCnC,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;IADJjB,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAc,SAAA,CAAAC,SAAA,MACF;;;;;IALFrC,EAFF,CAAAC,cAAA,cACsB,cACM;IAAAD,EAAA,CAAAgB,MAAA,GAAW;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAEzCjB,EADF,CAAAC,cAAA,cAAyB,cACE;IAAAD,EAAA,CAAAgB,MAAA,GAAkB;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACjDjB,EAAA,CAAA0B,UAAA,IAAAY,wEAAA,kBAAsD;IAI1DtC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IAPsBjB,EAAA,CAAAkB,SAAA,GAAW;IAAXlB,EAAA,CAAAoB,iBAAA,CAAAmB,IAAA,KAAW;IAEVvC,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAAoB,iBAAA,CAAAgB,SAAA,CAAAI,KAAA,CAAkB;IACbxC,EAAA,CAAAkB,SAAA,EAAsB;IAAtBlB,EAAA,CAAA4B,UAAA,SAAAQ,SAAA,CAAAC,SAAA,CAAsB;;;;;IARxDrC,EAFJ,CAAAC,cAAA,cAAmG,cAChE,eACJ;IAAAD,EAAA,CAAAgB,MAAA,GAA4D;IACzFhB,EADyF,CAAAiB,YAAA,EAAO,EAC1F;IACNjB,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA0B,UAAA,IAAAe,kEAAA,kBACsB;IAU1BzC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IAdyBjB,EAAA,CAAAkB,SAAA,GAA4D;IAA5DlB,EAAA,CAAAsB,kBAAA,kBAAAT,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAApB,WAAA,EAAAW,MAAA,0CAA4D;IAG/DlC,EAAA,CAAAkB,SAAA,GAA0C;IAA1ClB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAApB,WAAA,EAA0C;;;;;IAcpEvB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA8B,SAAA,kBAAoD;IACpD9B,EAAA,CAAAgB,MAAA,+DACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;;IA9BRjB,EAFJ,CAAAC,cAAA,cAA6E,cACvC,aACR;IAAAD,EAAA,CAAAgB,MAAA,GAAwB;IAAAhB,EAAA,CAAAiB,YAAA,EAAK;IAErDjB,EADF,CAAAC,cAAA,cAA2B,eACC;IAAAD,EAAA,CAAAgB,MAAA,GAA0B;IAAAhB,EAAA,CAAAiB,YAAA,EAAO;IAC3DjB,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAgB,MAAA,GAAsC;IAExEhB,EAFwE,CAAAiB,YAAA,EAAO,EACvE,EACF;IAENjB,EAAA,CAAAC,cAAA,cAAqC;IAmBnCD,EAlBA,CAAA0B,UAAA,KAAAkB,4DAAA,kBAAmG,KAAAC,oEAAA,gCAAA7C,EAAA,CAAAiC,sBAAA,CAkB3E;IAO5BjC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;;IAjCwBjB,EAAA,CAAAkB,SAAA,GAAwB;IAAxBlB,EAAA,CAAAoB,iBAAA,CAAAuB,OAAA,CAAAtB,aAAA,CAAwB;IAEtBrB,EAAA,CAAAkB,SAAA,GAA0B;IAA1BlB,EAAA,CAAAsB,kBAAA,SAAAqB,OAAA,CAAApB,WAAA,KAA0B;IACtBvB,EAAA,CAAAkB,SAAA,GAAsC;IAAtClB,EAAA,CAAAoB,iBAAA,CAAAuB,OAAA,CAAAnB,OAAA,yCAAsC;IAKhExB,EAAA,CAAAkB,SAAA,GAAwD;IAAAlB,EAAxD,CAAA4B,UAAA,SAAAf,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAApB,WAAA,EAAAW,MAAA,KAAwD,aAAAY,YAAA,CAAc;;;;;IA8B9E9C,EAFJ,CAAAC,cAAA,cAAqD,cACzB,aAChB;IAAAD,EAAA,CAAA8B,SAAA,kBAA8D;IAAA9B,EAAA,CAAAgB,MAAA,qCAAK;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IACpFjB,EAAA,CAAAgB,MAAA,GACF;IACFhB,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IAFFjB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,yBAAAT,MAAA,CAAAkC,gBAAA,+JACF;;;;;IAtDF/C,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAA8B,SAAA,kBAAgE;IAAA9B,EAAA,CAAAgB,MAAA,4CAClE;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAGJjB,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAgB,MAAA,2BAAI;IAAAhB,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IAAAjB,EAAA,CAAAgB,MAAA,IAC3B;IACFhB,EADE,CAAAiB,YAAA,EAAM,EACF;IAGNjB,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAA0B,UAAA,KAAAsB,qDAAA,mBAA6E;IAoC/EhD,EAAA,CAAAiB,YAAA,EAAM;IAENjB,EAAA,CAAA0B,UAAA,KAAAuB,qDAAA,kBAAqD;IAOzDjD,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IAnD2BjB,EAAA,CAAAkB,SAAA,IAC3B;IAD2BlB,EAAA,CAAAsB,kBAAA,WAAAT,MAAA,CAAAqC,gBAAA,GAAAhB,MAAA,oCAC3B;IAKsBlC,EAAA,CAAAkB,SAAA,GAAqB;IAArBlB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAAqC,gBAAA,GAAqB;IAsCvClD,EAAA,CAAAkB,SAAA,EAAoB;IAApBlB,EAAA,CAAA4B,UAAA,SAAAf,MAAA,CAAAsC,YAAA,GAAoB;;;;;;IAgB5BnD,EAAA,CAAAC,cAAA,iBAAiF;IAAzBD,EAAA,CAAAY,UAAA,mBAAAwC,0EAAA;MAAApD,EAAA,CAAAM,aAAA,CAAA+C,GAAA;MAAA,MAAAxC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAAyC,YAAA,EAAc;IAAA,EAAC;IAACtD,EAAA,CAAAgB,MAAA,yBAAG;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;;;IAC7FjB,EAAA,CAAAC,cAAA,iBACuB;IAArBD,EAAA,CAAAY,UAAA,mBAAA2C,0EAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAAkD,IAAA;MAAA,MAAA3C,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAA4C,QAAA,EAAU;IAAA,EAAC;IAACzD,EAAA,CAAAgB,MAAA,yBAAG;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;IADuBjB,EAAA,CAAA4B,UAAA,cAAAf,MAAA,CAAA6C,UAAA,GAA0B;;;;;;IAEpF1D,EAAA,CAAAC,cAAA,iBAC4B;IAA1BD,EAAA,CAAAY,UAAA,mBAAA+C,0EAAA;MAAA3D,EAAA,CAAAM,aAAA,CAAAsD,IAAA;MAAA,MAAA/C,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAAgD,aAAA,EAAe;IAAA,EAAC;IAAC7D,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;IADmBjB,EAAA,CAAA4B,UAAA,aAAAf,MAAA,CAAAqC,gBAAA,GAAAhB,MAAA,OAA4C;;;AD5F9G,OAAM,MAAO4B,8BAA8B;EAQzCC,YACUC,eAAgC,EAChCC,SAAsD;IADtD,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IATV,KAAAC,WAAW,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAG,IAAI3E,YAAY,EAAuB;IAEnE,KAAA4E,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAvC,SAAS,GAA2B,EAAE,CAAC,CAAC;IACxC,KAAAwC,uBAAuB,GAAsC,IAAIC,GAAG,EAAE,CAAC,CAAC;EAKpE;EAEJC,QAAQA,CAAA;IACN;EAAA;EAGFC,oBAAoBA,CAAA;IAClB;IACA,MAAMC,mBAAmB,GAAwB;MAC/ChD,aAAa,EAAE,CAAC;MAAE;MAClBiD,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACftD,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAAC2C,eAAe,CAACY,4CAA4C,CAAC;MAChEC,IAAI,EAAEJ;KACP,CAAC,CAACK,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACA,IAAI,CAACrD,SAAS,GAAGmD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C,GAAGA,IAAI;YACP1E,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL;UACA,IAAI,CAACmB,SAAS,GAAG,EAAE;QACrB;MACF,CAAC;MACDwD,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAACxD,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEAd,oBAAoBA,CAAA;IAClB;EAAA;EAGFmC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACrB,SAAS,CAACyD,MAAM,CAACF,IAAI,IAAIA,IAAI,CAAC1E,QAAQ,CAAC;EACrD;EAEA6E,qBAAqBA,CAAA;IACnB;IACA,OAAO,CAAC;EACV;EAEA7B,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACU,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAAClB,gBAAgB,EAAE,CAAChB,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAuB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACU,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B;QACA,IAAI,CAACoB,2BAA2B,EAAE;MACpC;MACA,IAAI,CAACpB,WAAW,EAAE;IACpB;EACF;EAEAoB,2BAA2BA,CAAA;IACzB,MAAMC,aAAa,GAAG,IAAI,CAACvC,gBAAgB,EAAE;IAE7CuC,aAAa,CAACC,OAAO,CAACN,IAAI,IAAG;MAC3B,IAAIA,IAAI,CAAC7D,WAAW,IAAI,CAAC,IAAI,CAAC8C,uBAAuB,CAACsB,GAAG,CAACP,IAAI,CAAC7D,WAAW,CAAC,EAAE;QAC3E;QACA,IAAI,CAACqE,sBAAsB,CAACR,IAAI,CAAC7D,WAAW,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAqE,sBAAsBA,CAACC,UAAkB;IACvC,MAAMC,IAAI,GAA8B;MACtCD,UAAU,EAAEA;KACb;IAED,IAAI,CAAC7B,eAAe,CAAC+B,yCAAyC,CAAC;MAC7DlB,IAAI,EAAEiB;KACP,CAAC,CAAChB,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAACb,uBAAuB,CAAC2B,GAAG,CAACH,UAAU,EAAEb,QAAQ,CAACE,OAAO,CAAC;QAChE;MACF,CAAC;MACDG,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAAChB,uBAAuB,CAAC2B,GAAG,CAACH,UAAU,EAAE,EAAE,CAAC;MAClD;KACD,CAAC;EACJ;EAEAnD,kBAAkBA,CAACmD,UAAkB;IACnC,OAAO,IAAI,CAACxB,uBAAuB,CAAC4B,GAAG,CAACJ,UAAU,CAAC,IAAI,EAAE;EAC3D;EAEAvC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACc,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEA8B,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAAC/B,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAjB,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACD,gBAAgB,EAAE,CAAChB,MAAM,GAAG,CAAC;EAC3C;EAEAa,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACI,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAU,aAAaA,CAAA;IACX,MAAMuC,MAAM,GAAwB;MAClCC,OAAO,EAAE,QAAQ;MAAE;MACnBC,SAAS,EAAE,MAAM;MACjBb,aAAa,EAAE,IAAI,CAACvC,gBAAgB,EAAE;MACtCqD,UAAU,EAAE,IAAI,CAAChB,qBAAqB;KACvC;IAED,IAAI,CAACpB,eAAe,CAACqC,IAAI,CAACJ,MAAM,CAAC;IACjC,IAAI,CAACK,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,MAAM,CAACJ,IAAI,EAAE;EACpB;EAEAK,eAAeA,CAACC,KAAY;IAC1B,IAAIA,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,aAAa,EAAE;MACxC,IAAI,CAACP,KAAK,EAAE;IACd;EACF;EAEQQ,KAAKA,CAAA;IACX,IAAI,CAAC7C,WAAW,GAAG,CAAC;IACpB,IAAI,CAACvC,SAAS,GAAG,EAAE;EACrB;EAEQ8E,eAAeA,CAAA;IACrB,IAAI,CAACvC,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACvC,SAAS,CAAC6D,OAAO,CAACwB,QAAQ,IAAG;MAChCA,QAAQ,CAACxG,QAAQ,GAAG,KAAK;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAAC2D,uBAAuB,CAAC8C,KAAK,EAAE;EACtC;EAEA;EACAC,IAAIA,CAAA;IACF,IAAI,CAACV,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAAC7E,SAAS,CAACK,MAAM,KAAK,CAAC,EAAE;MAC/B;MACA,IAAI,CAACsC,oBAAoB,EAAE;IAC7B,CAAC,MAAM;MACL;MACA,IAAI,CAACJ,WAAW,GAAG,CAAC;MACpB,IAAI,CAACvC,SAAS,CAAC6D,OAAO,CAACwB,QAAQ,IAAG;QAChCA,QAAQ,CAACxG,QAAQ,GAAG,KAAK;MAC3B,CAAC,CAAC;MACF,IAAI,CAAC2D,uBAAuB,CAAC8C,KAAK,EAAE;IACtC;EACF;;;uCAnMWrD,8BAA8B,EAAA9D,EAAA,CAAAqH,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAvH,EAAA,CAAAqH,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA9B3D,8BAA8B;MAAA4D,SAAA;MAAAC,MAAA;QAAAzD,WAAA;MAAA;MAAA0D,OAAA;QAAAzD,eAAA;MAAA;MAAA0D,UAAA;MAAAC,QAAA,GAAA9H,EAAA,CAAA+H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAhB,QAAA,WAAAiB,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpCvCpI,EAFJ,CAAAC,cAAA,iBAAuC,wBACS,aACV;UAAAD,EAAA,CAAAgB,MAAA,iDAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAC/CjB,EAAA,CAAAC,cAAA,gBAA2D;UAAlBD,EAAA,CAAAY,UAAA,mBAAA0H,gEAAA;YAAA,OAASD,GAAA,CAAA5B,KAAA,EAAO;UAAA,EAAC;UACxDzG,EAAA,CAAA8B,SAAA,iBAAwC;UAE5C9B,EADE,CAAAiB,YAAA,EAAS,EACM;UAKbjB,EAHJ,CAAAC,cAAA,sBAA0C,aAElB,aAKjB;UAAAD,EAAA,CAAAgB,MAAA,kCAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAChBjB,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAgB,MAAA,mCAAO;UACZhB,EADY,CAAAiB,YAAA,EAAM,EACZ;UAsCNjB,EAnCA,CAAA0B,UAAA,KAAA6G,8CAAA,kBAAoD,KAAAC,8CAAA,mBAmCA;UA4DtDxI,EAAA,CAAAiB,YAAA,EAAe;UAIXjB,EAFJ,CAAAC,cAAA,0BAA8C,eACjB,YACnB;UAAAD,EAAA,CAAAgB,MAAA,IAAuB;UAC/BhB,EAD+B,CAAAiB,YAAA,EAAO,EAChC;UAEJjB,EADF,CAAAC,cAAA,eAA0B,kBAC0B;UAAlBD,EAAA,CAAAY,UAAA,mBAAA6H,iEAAA;YAAA,OAASJ,GAAA,CAAA5B,KAAA,EAAO;UAAA,EAAC;UAACzG,EAAA,CAAAgB,MAAA,oBAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAI7DjB,EAHA,CAAA0B,UAAA,KAAAgH,iDAAA,qBAAiF,KAAAC,iDAAA,qBAE1D,KAAAC,iDAAA,qBAEK;UAGlC5I,EAFI,CAAAiB,YAAA,EAAM,EACS,EACT;;;UA3HmBjB,EAAA,CAAAkB,SAAA,GAIrB;UAJqBlB,EAAA,CAAA4B,UAAA,YAAA5B,EAAA,CAAA6I,eAAA,IAAAC,GAAA,EAAAT,GAAA,CAAAjE,WAAA,QAAAiE,GAAA,CAAAjE,WAAA,MAAAiE,GAAA,CAAAjE,WAAA,MAIrB;UACqBpE,EAAA,CAAAkB,SAAA,GAIrB;UAJqBlB,EAAA,CAAA4B,UAAA,YAAA5B,EAAA,CAAA6I,eAAA,KAAAC,GAAA,EAAAT,GAAA,CAAAjE,WAAA,QAAAiE,GAAA,CAAAjE,WAAA,MAAAiE,GAAA,CAAAjE,WAAA,MAIrB;UAIEpE,EAAA,CAAAkB,SAAA,GAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAAyG,GAAA,CAAAjE,WAAA,OAAuB;UAmCvBpE,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAAyG,GAAA,CAAAjE,WAAA,OAAuB;UAgErBpE,EAAA,CAAAkB,SAAA,GAAuB;UAAvBlB,EAAA,CAAAoB,iBAAA,CAAAiH,GAAA,CAAAnC,eAAA,GAAuB;UAIpBlG,EAAA,CAAAkB,SAAA,GAAqB;UAArBlB,EAAA,CAAA4B,UAAA,SAAAyG,GAAA,CAAAjE,WAAA,KAAqB;UACrBpE,EAAA,CAAAkB,SAAA,EAAqB;UAArBlB,EAAA,CAAA4B,UAAA,SAAAyG,GAAA,CAAAjE,WAAA,KAAqB;UAErBpE,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAAyG,GAAA,CAAAjE,WAAA,OAAuB;;;qBDtGlC3E,YAAY,EAAAsJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZxJ,WAAW,EAAAyJ,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EACX1J,YAAY,EAAA6H,EAAA,CAAA8B,eAAA,EAAA9B,EAAA,CAAA+B,mBAAA,EAAA/B,EAAA,CAAAgC,qBAAA,EAAAhC,EAAA,CAAAiC,qBAAA,EACZ7J,cAAc,EAAA4H,EAAA,CAAAkC,iBAAA,EACd7J,YAAY,EAAA2H,EAAA,CAAAmC,eAAA,EACZ7J,gBAAgB,EAAA0H,EAAA,CAAAoC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}