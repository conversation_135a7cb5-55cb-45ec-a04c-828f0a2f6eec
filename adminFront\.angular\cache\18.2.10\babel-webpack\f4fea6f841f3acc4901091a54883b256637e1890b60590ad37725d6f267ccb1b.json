{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// FIXME 公用？\nfunction dataStatistics(datas, statisticType) {\n  var dataNameMap = {};\n  zrUtil.each(datas, function (data) {\n    data.each(data.mapDimension('value'), function (value, idx) {\n      // Add prefix to avoid conflict with Object.prototype.\n      var mapKey = 'ec-' + data.getName(idx);\n      dataNameMap[mapKey] = dataNameMap[mapKey] || [];\n      if (!isNaN(value)) {\n        dataNameMap[mapKey].push(value);\n      }\n    });\n  });\n  return datas[0].map(datas[0].mapDimension('value'), function (value, idx) {\n    var mapKey = 'ec-' + datas[0].getName(idx);\n    var sum = 0;\n    var min = Infinity;\n    var max = -Infinity;\n    var len = dataNameMap[mapKey].length;\n    for (var i = 0; i < len; i++) {\n      min = Math.min(min, dataNameMap[mapKey][i]);\n      max = Math.max(max, dataNameMap[mapKey][i]);\n      sum += dataNameMap[mapKey][i];\n    }\n    var result;\n    if (statisticType === 'min') {\n      result = min;\n    } else if (statisticType === 'max') {\n      result = max;\n    } else if (statisticType === 'average') {\n      result = sum / len;\n    } else {\n      result = sum;\n    }\n    return len === 0 ? NaN : result;\n  });\n}\nexport default function mapDataStatistic(ecModel) {\n  var seriesGroups = {};\n  ecModel.eachSeriesByType('map', function (seriesModel) {\n    var hostGeoModel = seriesModel.getHostGeoModel();\n    var key = hostGeoModel ? 'o' + hostGeoModel.id : 'i' + seriesModel.getMapType();\n    (seriesGroups[key] = seriesGroups[key] || []).push(seriesModel);\n  });\n  zrUtil.each(seriesGroups, function (seriesList, key) {\n    var data = dataStatistics(zrUtil.map(seriesList, function (seriesModel) {\n      return seriesModel.getData();\n    }), seriesList[0].get('mapValueCalculation'));\n    for (var i = 0; i < seriesList.length; i++) {\n      seriesList[i].originalData = seriesList[i].getData();\n    }\n    // FIXME Put where?\n    for (var i = 0; i < seriesList.length; i++) {\n      seriesList[i].seriesGroup = seriesList;\n      seriesList[i].needsDrawMap = i === 0 && !seriesList[i].getHostGeoModel();\n      seriesList[i].setData(data.cloneShallow());\n      seriesList[i].mainSeries = seriesList[0];\n    }\n  });\n}", "map": {"version": 3, "names": ["zrUtil", "dataStatistics", "datas", "statisticType", "dataNameMap", "each", "data", "mapDimension", "value", "idx", "mapKey", "getName", "isNaN", "push", "map", "sum", "min", "Infinity", "max", "len", "length", "i", "Math", "result", "NaN", "mapDataStatistic", "ecModel", "seriesGroups", "eachSeriesByType", "seriesModel", "hostGeoModel", "getHostGeoModel", "key", "id", "getMapType", "seriesList", "getData", "get", "originalData", "seriesGroup", "needsDrawMap", "setData", "cloneShallow", "mainSeries"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/map/mapDataStatistic.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// FIXME 公用？\nfunction dataStatistics(datas, statisticType) {\n  var dataNameMap = {};\n  zrUtil.each(datas, function (data) {\n    data.each(data.mapDimension('value'), function (value, idx) {\n      // Add prefix to avoid conflict with Object.prototype.\n      var mapKey = 'ec-' + data.getName(idx);\n      dataNameMap[mapKey] = dataNameMap[mapKey] || [];\n      if (!isNaN(value)) {\n        dataNameMap[mapKey].push(value);\n      }\n    });\n  });\n  return datas[0].map(datas[0].mapDimension('value'), function (value, idx) {\n    var mapKey = 'ec-' + datas[0].getName(idx);\n    var sum = 0;\n    var min = Infinity;\n    var max = -Infinity;\n    var len = dataNameMap[mapKey].length;\n    for (var i = 0; i < len; i++) {\n      min = Math.min(min, dataNameMap[mapKey][i]);\n      max = Math.max(max, dataNameMap[mapKey][i]);\n      sum += dataNameMap[mapKey][i];\n    }\n    var result;\n    if (statisticType === 'min') {\n      result = min;\n    } else if (statisticType === 'max') {\n      result = max;\n    } else if (statisticType === 'average') {\n      result = sum / len;\n    } else {\n      result = sum;\n    }\n    return len === 0 ? NaN : result;\n  });\n}\nexport default function mapDataStatistic(ecModel) {\n  var seriesGroups = {};\n  ecModel.eachSeriesByType('map', function (seriesModel) {\n    var hostGeoModel = seriesModel.getHostGeoModel();\n    var key = hostGeoModel ? 'o' + hostGeoModel.id : 'i' + seriesModel.getMapType();\n    (seriesGroups[key] = seriesGroups[key] || []).push(seriesModel);\n  });\n  zrUtil.each(seriesGroups, function (seriesList, key) {\n    var data = dataStatistics(zrUtil.map(seriesList, function (seriesModel) {\n      return seriesModel.getData();\n    }), seriesList[0].get('mapValueCalculation'));\n    for (var i = 0; i < seriesList.length; i++) {\n      seriesList[i].originalData = seriesList[i].getData();\n    }\n    // FIXME Put where?\n    for (var i = 0; i < seriesList.length; i++) {\n      seriesList[i].seriesGroup = seriesList;\n      seriesList[i].needsDrawMap = i === 0 && !seriesList[i].getHostGeoModel();\n      seriesList[i].setData(data.cloneShallow());\n      seriesList[i].mainSeries = seriesList[0];\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,aAAa,EAAE;EAC5C,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpBJ,MAAM,CAACK,IAAI,CAACH,KAAK,EAAE,UAAUI,IAAI,EAAE;IACjCA,IAAI,CAACD,IAAI,CAACC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;MAC1D;MACA,IAAIC,MAAM,GAAG,KAAK,GAAGJ,IAAI,CAACK,OAAO,CAACF,GAAG,CAAC;MACtCL,WAAW,CAACM,MAAM,CAAC,GAAGN,WAAW,CAACM,MAAM,CAAC,IAAI,EAAE;MAC/C,IAAI,CAACE,KAAK,CAACJ,KAAK,CAAC,EAAE;QACjBJ,WAAW,CAACM,MAAM,CAAC,CAACG,IAAI,CAACL,KAAK,CAAC;MACjC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAON,KAAK,CAAC,CAAC,CAAC,CAACY,GAAG,CAACZ,KAAK,CAAC,CAAC,CAAC,CAACK,YAAY,CAAC,OAAO,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;IACxE,IAAIC,MAAM,GAAG,KAAK,GAAGR,KAAK,CAAC,CAAC,CAAC,CAACS,OAAO,CAACF,GAAG,CAAC;IAC1C,IAAIM,GAAG,GAAG,CAAC;IACX,IAAIC,GAAG,GAAGC,QAAQ;IAClB,IAAIC,GAAG,GAAG,CAACD,QAAQ;IACnB,IAAIE,GAAG,GAAGf,WAAW,CAACM,MAAM,CAAC,CAACU,MAAM;IACpC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC5BL,GAAG,GAAGM,IAAI,CAACN,GAAG,CAACA,GAAG,EAAEZ,WAAW,CAACM,MAAM,CAAC,CAACW,CAAC,CAAC,CAAC;MAC3CH,GAAG,GAAGI,IAAI,CAACJ,GAAG,CAACA,GAAG,EAAEd,WAAW,CAACM,MAAM,CAAC,CAACW,CAAC,CAAC,CAAC;MAC3CN,GAAG,IAAIX,WAAW,CAACM,MAAM,CAAC,CAACW,CAAC,CAAC;IAC/B;IACA,IAAIE,MAAM;IACV,IAAIpB,aAAa,KAAK,KAAK,EAAE;MAC3BoB,MAAM,GAAGP,GAAG;IACd,CAAC,MAAM,IAAIb,aAAa,KAAK,KAAK,EAAE;MAClCoB,MAAM,GAAGL,GAAG;IACd,CAAC,MAAM,IAAIf,aAAa,KAAK,SAAS,EAAE;MACtCoB,MAAM,GAAGR,GAAG,GAAGI,GAAG;IACpB,CAAC,MAAM;MACLI,MAAM,GAAGR,GAAG;IACd;IACA,OAAOI,GAAG,KAAK,CAAC,GAAGK,GAAG,GAAGD,MAAM;EACjC,CAAC,CAAC;AACJ;AACA,eAAe,SAASE,gBAAgBA,CAACC,OAAO,EAAE;EAChD,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrBD,OAAO,CAACE,gBAAgB,CAAC,KAAK,EAAE,UAAUC,WAAW,EAAE;IACrD,IAAIC,YAAY,GAAGD,WAAW,CAACE,eAAe,CAAC,CAAC;IAChD,IAAIC,GAAG,GAAGF,YAAY,GAAG,GAAG,GAAGA,YAAY,CAACG,EAAE,GAAG,GAAG,GAAGJ,WAAW,CAACK,UAAU,CAAC,CAAC;IAC/E,CAACP,YAAY,CAACK,GAAG,CAAC,GAAGL,YAAY,CAACK,GAAG,CAAC,IAAI,EAAE,EAAEnB,IAAI,CAACgB,WAAW,CAAC;EACjE,CAAC,CAAC;EACF7B,MAAM,CAACK,IAAI,CAACsB,YAAY,EAAE,UAAUQ,UAAU,EAAEH,GAAG,EAAE;IACnD,IAAI1B,IAAI,GAAGL,cAAc,CAACD,MAAM,CAACc,GAAG,CAACqB,UAAU,EAAE,UAAUN,WAAW,EAAE;MACtE,OAAOA,WAAW,CAACO,OAAO,CAAC,CAAC;IAC9B,CAAC,CAAC,EAAED,UAAU,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAC7C,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,UAAU,CAACf,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC1Cc,UAAU,CAACd,CAAC,CAAC,CAACiB,YAAY,GAAGH,UAAU,CAACd,CAAC,CAAC,CAACe,OAAO,CAAC,CAAC;IACtD;IACA;IACA,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,UAAU,CAACf,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC1Cc,UAAU,CAACd,CAAC,CAAC,CAACkB,WAAW,GAAGJ,UAAU;MACtCA,UAAU,CAACd,CAAC,CAAC,CAACmB,YAAY,GAAGnB,CAAC,KAAK,CAAC,IAAI,CAACc,UAAU,CAACd,CAAC,CAAC,CAACU,eAAe,CAAC,CAAC;MACxEI,UAAU,CAACd,CAAC,CAAC,CAACoB,OAAO,CAACnC,IAAI,CAACoC,YAAY,CAAC,CAAC,CAAC;MAC1CP,UAAU,CAACd,CAAC,CAAC,CAACsB,UAAU,GAAGR,UAAU,CAAC,CAAC,CAAC;IAC1C;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}