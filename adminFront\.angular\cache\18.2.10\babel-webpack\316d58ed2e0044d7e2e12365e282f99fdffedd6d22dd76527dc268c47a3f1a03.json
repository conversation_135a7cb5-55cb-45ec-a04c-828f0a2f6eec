{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r3, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_47_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", floor_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", floor_r6, \"F \");\n  }\n}\nfunction SettingTimePeriodComponent_div_47_button_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_47_button_37_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearAllFilters());\n    });\n    i0.ɵɵelement(1, \"i\", 57);\n    i0.ɵɵtext(2, \"\\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"div\", 15)(3, \"nb-form-field\", 25);\n    i0.ɵɵelement(4, \"nb-icon\", 43);\n    i0.ɵɵelementStart(5, \"input\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_47_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.searchKeyword, $event) || (ctx_r4.filterOptions.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_47_Template_input_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 20)(7, \"nb-select\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.statusFilter, $event) || (ctx_r4.filterOptions.statusFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_selectedChange_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(8, \"nb-option\", 22);\n    i0.ɵɵtext(9, \"\\u5168\\u90E8\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-option\", 46);\n    i0.ɵɵtext(11, \"\\u9032\\u884C\\u4E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-option\", 47);\n    i0.ɵɵtext(13, \"\\u5F85\\u958B\\u653E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-option\", 48);\n    i0.ɵɵtext(15, \"\\u5DF2\\u904E\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-option\", 49);\n    i0.ɵɵtext(17, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-option\", 50);\n    i0.ɵɵtext(19, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 20)(21, \"nb-select\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.floorFilter, $event) || (ctx_r4.filterOptions.floorFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_selectedChange_21_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(22, \"nb-option\", 22);\n    i0.ɵɵtext(23, \"\\u5168\\u90E8\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SettingTimePeriodComponent_div_47_nb_option_24_Template, 2, 2, \"nb-option\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 52)(26, \"nb-select\", 53);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.pageSize, $event) || (ctx_r4.pageSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_47_Template_nb_select_selectedChange_26_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onPageSizeChange());\n    });\n    i0.ɵɵelementStart(27, \"nb-option\", 40);\n    i0.ɵɵtext(28, \"50\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-option\", 40);\n    i0.ɵɵtext(30, \"100\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"nb-option\", 40);\n    i0.ɵɵtext(32, \"200\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-option\", 40);\n    i0.ɵɵtext(34, \"500\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 31)(36, \"div\", 54);\n    i0.ɵɵtemplate(37, SettingTimePeriodComponent_div_47_button_37_Template, 3, 0, \"button\", 55);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.statusFilter);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.floorFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.availableFloors);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 50);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 200);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 500);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.hasActiveFilters());\n  }\n}\nfunction SettingTimePeriodComponent_div_48_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 98);\n    i0.ɵɵelement(1, \"i\", 99);\n    i0.ɵɵtext(2, \" \\u5DF2\\u9078 \");\n    i0.ɵɵelementStart(3, \"strong\", 100);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7B46 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedHouses.length);\n  }\n}\nfunction SettingTimePeriodComponent_div_48_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_32_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.openBatchSetting());\n    });\n    i0.ɵɵelement(2, \"i\", 103);\n    i0.ɵɵtext(3, \"\\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵelementStart(4, \"span\", 104);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_32_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearSelection());\n    });\n    i0.ɵɵelement(7, \"i\", 57);\n    i0.ɵɵtext(8, \"\\u6E05\\u9664\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedHouses.length);\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_small_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 122);\n    i0.ɵɵtext(1, \"\\u7121\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 123);\n    i0.ɵɵelement(1, \"i\", 124);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 1, house_r11.CChangeStartDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 125);\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵtext(2, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 123);\n    i0.ɵɵelement(1, \"i\", 126);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 1, house_r11.CChangeEndDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 125);\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵtext(2, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_48_tr_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 106)(2, \"nb-checkbox\", 107);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_tr_71_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const house_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r11.selected, $event) || (house_r11.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_tr_71_Template_nb_checkbox_ngModelChange_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 108)(5, \"span\", 109);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SettingTimePeriodComponent_div_48_tr_71_small_7_Template, 2, 0, \"small\", 110);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\")(9, \"span\", 111);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\")(12, \"span\", 112);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\")(15, \"div\", 113);\n    i0.ɵɵtemplate(16, SettingTimePeriodComponent_div_48_tr_71_span_16_Template, 4, 4, \"span\", 114)(17, SettingTimePeriodComponent_div_48_tr_71_span_17_Template, 3, 0, \"span\", 115);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"div\", 113);\n    i0.ɵɵtemplate(20, SettingTimePeriodComponent_div_48_tr_71_span_20_Template, 4, 4, \"span\", 114)(21, SettingTimePeriodComponent_div_48_tr_71_span_21_Template, 3, 0, \"span\", 115);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"td\", 106)(23, \"div\", 116)(24, \"span\", 117);\n    i0.ɵɵelement(25, \"i\", 118);\n    i0.ɵɵelementStart(26, \"span\", 119);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"td\", 106)(29, \"div\", 32)(30, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_tr_71_Template_button_click_30_listener() {\n      const house_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const dialog_r12 = i0.ɵɵreference(56);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r12, house_r11));\n    });\n    i0.ɵɵelement(31, \"i\", 121);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const house_r11 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"table-row-selected\", house_r11.selected)(\"table-row-disabled\", !house_r11.CHouseId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r11.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r11.CHouseId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(house_r11.CHouseHold);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r11.CHouseId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(house_r11.CBuildingName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", house_r11.CFloor, \"F\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", house_r11.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r11.CChangeStartDate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", house_r11.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r11.CChangeEndDate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r11));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r4.getStatusIcon(house_r11));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getStatusText(house_r11));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !house_r11.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_48_div_72_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 132)(1, \"button\", 147);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_li_21_Template_button_click_1_listener() {\n      const page_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(page_r15));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r15 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r15 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r15);\n  }\n}\nfunction SettingTimePeriodComponent_div_48_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 127)(1, \"div\", 60)(2, \"div\", 128)(3, \"span\", 82);\n    i0.ɵɵtext(4, \" \\u7B2C \");\n    i0.ɵɵelementStart(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" \\u9801\\uFF0C\\u5171 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" \\u9801 \");\n    i0.ɵɵelementStart(11, \"span\", 129);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"nav\", 130)(14, \"ul\", 131)(15, \"li\", 132)(16, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(1));\n    });\n    i0.ɵɵelement(17, \"i\", 134);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\", 132)(19, \"button\", 135);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage - 1));\n    });\n    i0.ɵɵelement(20, \"i\", 136);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, SettingTimePeriodComponent_div_48_div_72_li_21_Template, 3, 3, \"li\", 137);\n    i0.ɵɵelementStart(22, \"li\", 132)(23, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage + 1));\n    });\n    i0.ɵɵelement(24, \"i\", 139);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"li\", 132)(26, \"button\", 140);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.totalPages));\n    });\n    i0.ɵɵelement(27, \"i\", 141);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"div\", 142)(29, \"div\", 143)(30, \"input\", 144);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_div_72_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.jumpToPage, $event) || (ctx_r4.jumpToPage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function SettingTimePeriodComponent_div_48_div_72_Template_input_keyup_enter_30_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.jumpToPageAction());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_div_72_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.jumpToPageAction());\n    });\n    i0.ɵɵelement(32, \"i\", 146);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r4.currentPage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.totalPages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" (\\u986F\\u793A\\u7B2C \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" \\u7B46\\uFF0C \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46) \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.jumpToPage);\n    i0.ɵɵproperty(\"min\", 1)(\"max\", ctx_r4.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"div\", 60)(3, \"div\", 61)(4, \"span\", 62);\n    i0.ɵɵelement(5, \"i\", 63);\n    i0.ɵɵtext(6, \" \\u5171 \");\n    i0.ɵɵelementStart(7, \"strong\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, SettingTimePeriodComponent_div_48_span_10_Template, 6, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"div\", 66)(13, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"active\"));\n    });\n    i0.ɵɵelement(14, \"i\", 68);\n    i0.ɵɵtext(15, \"\\u9032\\u884C\\u4E2D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"pending\"));\n    });\n    i0.ɵɵelement(17, \"i\", 69);\n    i0.ɵɵtext(18, \"\\u5F85\\u958B\\u653E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"expired\"));\n    });\n    i0.ɵɵelement(20, \"i\", 70);\n    i0.ɵɵtext(21, \"\\u5DF2\\u904E\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"not-set\"));\n    });\n    i0.ɵɵelement(23, \"i\", 72);\n    i0.ɵɵtext(24, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(25, \"div\", 73)(26, \"div\", 60)(27, \"div\", 74)(28, \"div\", 75)(29, \"nb-checkbox\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_nb_checkbox_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_nb_checkbox_ngModelChange_29_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementStart(30, \"span\", 77);\n    i0.ɵɵtext(31, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, SettingTimePeriodComponent_div_48_div_32_Template, 9, 1, \"div\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 79)(34, \"div\", 80)(35, \"div\", 81)(36, \"small\", 82);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(38, \"div\", 83)(39, \"table\", 84)(40, \"thead\", 85)(41, \"tr\")(42, \"th\", 86)(43, \"nb-checkbox\", 87);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_nb_checkbox_ngModelChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_48_Template_nb_checkbox_ngModelChange_43_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"th\", 88);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_th_click_44_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CHouseHold\"));\n    });\n    i0.ɵɵelementStart(45, \"div\", 89);\n    i0.ɵɵtext(46, \" \\u6236\\u578B \");\n    i0.ɵɵelement(47, \"i\", 90);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"th\", 91);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_th_click_48_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CBuildingName\"));\n    });\n    i0.ɵɵelementStart(49, \"div\", 89);\n    i0.ɵɵtext(50, \" \\u68DF\\u5225 \");\n    i0.ɵɵelement(51, \"i\", 90);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"th\", 92);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_th_click_52_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CFloor\"));\n    });\n    i0.ɵɵelementStart(53, \"div\", 89);\n    i0.ɵɵtext(54, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelement(55, \"i\", 90);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"th\", 93);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_th_click_56_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeStartDate\"));\n    });\n    i0.ɵɵelementStart(57, \"div\", 89);\n    i0.ɵɵtext(58, \" \\u958B\\u59CB\\u65E5\\u671F \");\n    i0.ɵɵelement(59, \"i\", 90);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"th\", 93);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_48_Template_th_click_60_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeEndDate\"));\n    });\n    i0.ɵɵelementStart(61, \"div\", 89);\n    i0.ɵɵtext(62, \" \\u7D50\\u675F\\u65E5\\u671F \");\n    i0.ɵɵelement(63, \"i\", 90);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"th\", 94)(65, \"div\", 89);\n    i0.ɵɵtext(66, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"th\", 95)(68, \"div\", 89);\n    i0.ɵɵtext(69, \" \\u64CD\\u4F5C \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(70, \"tbody\");\n    i0.ɵɵtemplate(71, SettingTimePeriodComponent_div_48_tr_71_Template, 32, 20, \"tr\", 96);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(72, SettingTimePeriodComponent_div_48_div_72_Template, 33, 21, \"div\", 97);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.filteredHouses.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r4.filterOptions.statusFilter === \"active\")(\"btn-outline-primary\", ctx_r4.filterOptions.statusFilter !== \"active\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-warning\", ctx_r4.filterOptions.statusFilter === \"pending\")(\"btn-outline-warning\", ctx_r4.filterOptions.statusFilter !== \"pending\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-danger\", ctx_r4.filterOptions.statusFilter === \"expired\")(\"btn-outline-danger\", ctx_r4.filterOptions.statusFilter !== \"expired\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-not-set-active\", ctx_r4.filterOptions.statusFilter === \"not-set\")(\"btn-not-set-outline\", ctx_r4.filterOptions.statusFilter !== \"not-set\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" / \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46 \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.paginatedHouses)(\"ngForTrackBy\", ctx_r4.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 82);\n    i0.ɵɵelement(4, \"i\", 149);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\\u5F8C\\u67E5\\u8A62\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 82)(4, \"div\", 150)(5, \"span\", 151);\n    i0.ɵɵtext(6, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 152);\n    i0.ɵɵtext(8, \"\\u8CC7\\u6599\\u8F09\\u5165\\u4E2D\\uFF0C\\u8ACB\\u7A0D\\u5019...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \\u5DF2\\u9078\\u64C7 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225 \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r4.selectedBuildingForBatch.name, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 171);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", house_r17.CHouseHold, \" (\", house_r17.CBuildingName, \"-\", house_r17.CFloor, \"F) \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_28_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" ...\\u7B49 \", ctx_r4.selectedHouses.length - 10, \" \\u500B \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 167)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 168);\n    i0.ɵɵtemplate(4, SettingTimePeriodComponent_ng_template_51_div_28_span_4_Template, 2, 3, \"span\", 169)(5, SettingTimePeriodComponent_ng_template_51_div_28_span_5_Template, 2, 1, \"span\", 170);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5C07\\u5957\\u7528\\u5230\\u5DF2\\u9078\\u64C7\\u7684 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedHouses.slice(0, 10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 10);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 107);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      const house_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r22.selected, $event) || (house_r22.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r22 = ctx.$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r22.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r22.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r22.CHouseHold, \" (\", house_r22.CBuildingName, \") \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 176);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_nb_checkbox_1_Template, 2, 4, \"nb-checkbox\", 177);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", floor_r20.houses);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 174)(1, \"nb-checkbox\", 87);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const floor_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(floor_r20.selected, $event) || (floor_r20.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      const floor_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.onFloorSelectionChange(floor_r20));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_Template, 2, 1, \"div\", 175);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", floor_r20.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r20.floorNumber, \"F (\", floor_r20.houses.length, \" \\u6236) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", floor_r20.selected);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_29_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 152);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_Template, 4, 4, \"div\", 173);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedBuildingForBatch.floors);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"nb-checkbox\", 87);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_div_29_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.applyToAll, $event) || (ctx_r4.batchSettings.applyToAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_51_div_29_div_3_Template, 2, 1, \"div\", 172);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.applyToAll);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u90E8\\u6236\\u5225 (\", ctx_r4.flattenedHouses.length, \" \\u500B) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.batchSettings.applyToAll && ctx_r4.selectedBuildingForBatch);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225)\");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 153)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_51_span_3_Template, 2, 1, \"span\", 154)(4, SettingTimePeriodComponent_ng_template_51_span_4_Template, 2, 1, \"span\", 155);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"div\", 156)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n    i0.ɵɵelementStart(9, \"span\", 157);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 158)(12, \"nb-form-field\", 159);\n    i0.ɵɵelement(13, \"nb-icon\", 26);\n    i0.ɵɵelementStart(14, \"input\", 160);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.startDate, $event) || (ctx_r4.batchSettings.startDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 28, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 161);\n    i0.ɵɵtext(18, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\", 159);\n    i0.ɵɵelement(20, \"nb-icon\", 26);\n    i0.ɵɵelementStart(21, \"input\", 160);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_51_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.endDate, $event) || (ctx_r4.batchSettings.endDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 28, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 156)(25, \"label\");\n    i0.ɵɵtext(26, \"\\u9069\\u7528\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 162);\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_ng_template_51_div_28_Template, 6, 3, \"div\", 163)(29, SettingTimePeriodComponent_ng_template_51_div_29_Template, 4, 3, \"div\", 155);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-card-footer\", 164)(31, \"button\", 165);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_51_Template_button_click_31_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r23));\n    });\n    i0.ɵɵtext(32, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 166);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_51_Template_button_click_33_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onBatchSubmit(ref_r23));\n    });\n    i0.ɵɵtext(34, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(35, SettingTimePeriodComponent_ng_template_51_span_35_Template, 2, 1, \"span\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const batchStartDate_r24 = i0.ɵɵreference(16);\n    const batchEndDate_r25 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedBuildingForBatch && ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", batchStartDate_r24);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.startDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", batchEndDate_r25);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.endDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 178);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 179);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 178)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 180);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 181)(7, \"div\", 182)(8, \"label\", 183);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 157);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 184);\n    i0.ɵɵelement(13, \"nb-icon\", 26);\n    i0.ɵɵelementStart(14, \"input\", 185);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_55_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 28, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 186);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 26);\n    i0.ɵɵelementStart(21, \"input\", 187);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_55_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 28, 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 179)(25, \"button\", 188);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_55_Template_button_click_25_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r26).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r27));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 189);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_55_Template_button_click_27_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r26).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSubmit(ref_r27));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r28 = i0.ɵɵreference(16);\n    const changeEndDate_r29 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r4.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r28);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r29);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    // 新增的屬性\n    this.buildingGroups = [];\n    this.buildingOptions = [];\n    this.selectedBuilding = '';\n    this.availableFloors = [];\n    // 篩選和搜尋\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 批次設定\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: true,\n      selectedBuildings: [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    this.selectedBuildingForBatch = null;\n    // 表格視圖相關屬性\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.loading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalPages = 1;\n    // 排序相關\n    this.sortField = '';\n    this.sortDirection = 'asc';\n    // 數學函數引用\n    this.Math = Math;\n    // 新增的UI控制屬性\n    this.jumpToPage = 1;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  // 新增的UI控制方法\n  hasActiveFilters() {\n    return !!(this.filterOptions.searchKeyword || this.filterOptions.statusFilter || this.filterOptions.floorFilter);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.filterOptions.searchKeyword) count++;\n    if (this.filterOptions.statusFilter) count++;\n    if (this.filterOptions.floorFilter) count++;\n    return count;\n  }\n  resetFilters() {\n    this.searchQuery = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.selectedBuilding = '';\n    this.clearAllFilters();\n  }\n  clearAllFilters() {\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    this.onSearch();\n  }\n  setQuickFilter(status) {\n    if (this.filterOptions.statusFilter === status) {\n      this.filterOptions.statusFilter = '';\n    } else {\n      this.filterOptions.statusFilter = status;\n    }\n    this.onSearch();\n  }\n  clearSelection() {\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.flattenedHouses.forEach(house => house.selected = false);\n  }\n  getStatusIcon(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return 'fas fa-play-circle';\n      case 'pending':\n        return 'fas fa-clock';\n      case 'expired':\n        return 'fas fa-times-circle';\n      case 'not-set':\n        return 'fas fa-exclamation-triangle';\n      case 'disabled':\n        return 'fas fa-ban';\n      default:\n        return 'fas fa-exclamation-triangle';\n    }\n  }\n  jumpToPageAction() {\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\n      this.goToPage(this.jumpToPage);\n    }\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CBuildingName: '未分類',\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  // 新增：建案變更處理\n  onBuildCaseChange() {\n    // 重置所有相關狀態\n    this.resetAllStates();\n    // 執行查詢\n    this.getHouseChangeDate();\n  }\n  // 新增：重置所有狀態\n  resetAllStates() {\n    // 重置數據\n    this.houseChangeDates = [];\n    this.convertedHouseArray = [];\n    this.buildingGroups = [];\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    // 重置篩選條件\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 重置選擇狀態\n    this.selectAll = false;\n    this.selectedBuilding = '';\n    // 重置分頁\n    this.currentPage = 1;\n    this.totalPages = 1;\n    // 重置可用選項\n    this.buildingOptions = [];\n    this.availableFloors = [];\n    // 重置排序\n    this.sortField = '';\n    this.sortDirection = 'asc';\n  }\n  getHouseChangeDate() {\n    // 如果沒有選擇建案，直接返回\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\n      this.loading = false;\n      return;\n    }\n    this.loading = true;\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      this.loading = false;\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          // 新增：建立棟別分組資料\n          this.buildBuildingGroups(res.Entries);\n          // 新增：建立扁平化資料\n          this.buildFlattenedHouses(res.Entries);\n        }\n      }\n    });\n  }\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n  buildBuildingGroups(data) {\n    const buildingMap = new Map();\n    data.forEach(household => {\n      const houseType = household.CHouseHold || ''; // 戶型\n      household.CHouses?.forEach(house => {\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\n        const floor = house.CFloor || 0;\n        if (!buildingMap.has(buildingName)) {\n          buildingMap.set(buildingName, new Map());\n        }\n        const floorMap = buildingMap.get(buildingName);\n        if (!floorMap.has(floor)) {\n          floorMap.set(floor, []);\n        }\n        floorMap.get(floor).push({\n          CHouseHold: houseType,\n          // 戶型\n          CBuildingName: buildingName,\n          // 棟別\n          CHouseId: house.CHouseId || 0,\n          CFloor: floor,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 轉換為BuildingGroup格式\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n      const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n      .map(([floorNumber, houses]) => ({\n        floorNumber,\n        houses: houses.sort((a, b) => {\n          // 排序邏輯：先按戶型排序，再按樓層排序\n          if (a.CHouseHold !== b.CHouseHold) {\n            return a.CHouseHold.localeCompare(b.CHouseHold);\n          }\n          return a.CFloor - b.CFloor;\n        }),\n        selected: false\n      }));\n      return {\n        name: buildingName,\n        floors,\n        selected: false\n      };\n    }).sort((a, b) => a.name.localeCompare(b.name));\n    // 更新棟別選項和可用樓層\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n    this.updateAvailableFloors();\n  }\n  // 新增：更新可用樓層\n  updateAvailableFloors() {\n    const floorsSet = new Set();\n    this.buildingGroups.forEach(building => {\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n        building.floors.forEach(floor => {\n          floorsSet.add(floor.floorNumber);\n        });\n      }\n    });\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n  }\n  // 修改：棟別選擇變更處理\n  onBuildingChange() {\n    // 重置選擇狀態\n    this.selectedHouses.forEach(house => house.selected = false);\n    this.selectedHouses = [];\n    this.selectAll = false;\n    // 重置分頁到第一頁\n    this.currentPage = 1;\n    // 重置樓層篩選\n    this.filterOptions.floorFilter = '';\n    // 更新可用樓層\n    this.updateAvailableFloors();\n    // 設定棟別篩選並執行搜尋\n    this.filterOptions.buildingFilter = this.selectedBuilding;\n    this.onSearch();\n  }\n  // 新增：取得過濾後的棟別資料\n  getFilteredBuildings() {\n    return this.buildingGroups.filter(building => {\n      // 棟別篩選\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n        return false;\n      }\n      // 關鍵字搜尋 (搜尋戶型)\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n        if (!hasMatchingHouse) {\n          return false;\n        }\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n        if (!hasMatchingStatus) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n        if (!hasMatchingFloor) {\n          return false;\n        }\n      }\n      return true;\n    }).map(building => {\n      // 對每個棟別，也要篩選其樓層和戶別\n      const filteredBuilding = {\n        ...building\n      };\n      filteredBuilding.floors = building.floors.filter(floor => {\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (floor.floorNumber !== floorNumber) {\n            return false;\n          }\n        }\n        // 檢查該樓層是否有符合條件的戶別\n        const hasValidHouses = floor.houses.some(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return hasValidHouses;\n      }).map(floor => {\n        // 篩選戶別\n        const filteredFloor = {\n          ...floor\n        };\n        filteredFloor.houses = floor.houses.filter(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return filteredFloor;\n      });\n      return filteredBuilding;\n    });\n  }\n  // 修復：檢查戶別是否符合狀態篩選\n  matchesStatusFilter(house) {\n    const status = this.getHouseStatus(house);\n    switch (this.filterOptions.statusFilter) {\n      case 'active':\n        return status === 'active';\n      case 'pending':\n        return status === 'pending';\n      case 'expired':\n        return status === 'expired';\n      case 'not-set':\n        return status === 'not-set';\n      case 'disabled':\n        return status === 'disabled';\n      default:\n        return true;\n      // 全部狀態\n    }\n  }\n  // 修復：取得戶別狀態\n  getHouseStatus(house) {\n    if (!house.CHouseId) {\n      return 'disabled';\n    }\n    // 檢查是否有設定開放時段\n    if (!house.CChangeStartDate || !house.CChangeEndDate || house.CChangeStartDate === '' || house.CChangeEndDate === '') {\n      return 'not-set';\n    }\n    try {\n      // 處理日期字串，支援多種格式\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      // 解析開始日期\n      let startDate;\n      if (house.CChangeStartDate.includes('T')) {\n        startDate = new Date(house.CChangeStartDate);\n      } else {\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\n      }\n      // 解析結束日期\n      let endDate;\n      if (house.CChangeEndDate.includes('T')) {\n        endDate = new Date(house.CChangeEndDate);\n      } else {\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\n      }\n      // 檢查日期有效性\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\n        console.warn('Invalid date format:', {\n          start: house.CChangeStartDate,\n          end: house.CChangeEndDate,\n          houseId: house.CHouseId\n        });\n        return 'not-set';\n      }\n      // 轉換為日期比較（不含時間）\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n      // 判斷狀態\n      if (today < startDateOnly) {\n        return 'pending'; // 待開放\n      } else if (today >= startDateOnly && today <= endDateOnly) {\n        return 'active'; // 進行中\n      } else {\n        return 'expired'; // 已過期\n      }\n    } catch (error) {\n      console.error('Error parsing dates:', error, house);\n      return 'not-set';\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  // 修改：開啟批次設定對話框\n  openBatchSetting(building) {\n    this.selectedBuildingForBatch = building || null;\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\n    const hasSelectedHouses = this.selectedHouses.length > 0;\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: !hasSelectedHouses && !building,\n      selectedBuildings: building ? [building.name] : [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    // 重置選擇狀態\n    if (building) {\n      building.floors.forEach(floor => {\n        floor.selected = false;\n        floor.houses.forEach(house => house.selected = false);\n      });\n    }\n    // 開啟對話框\n    this.dialogService.open(this.batchSettingDialog);\n  }\n  // 新增：樓層選擇變更處理\n  onFloorSelectionChange(floor) {\n    if (floor.selected) {\n      floor.houses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = true;\n        }\n      });\n    } else {\n      floor.houses.forEach(house => house.selected = false);\n    }\n  }\n  // 修改：批次提交\n  onBatchSubmit(ref) {\n    // 驗證批次設定\n    this.valid.clear();\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 收集要更新的房屋\n    const housesToUpdate = [];\n    if (this.batchSettings.applyToAll) {\n      // 全部戶別\n      this.flattenedHouses.forEach(house => {\n        if (house.CHouseId) {\n          housesToUpdate.push({\n            CHouseId: house.CHouseId,\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n          });\n        }\n      });\n    } else {\n      // 使用已選擇的戶別\n      if (this.selectedHouses.length > 0) {\n        this.selectedHouses.forEach(house => {\n          if (house.CHouseId) {\n            housesToUpdate.push({\n              CHouseId: house.CHouseId,\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n            });\n          }\n        });\n      } else if (this.selectedBuildingForBatch) {\n        // 如果沒有選擇的戶別，使用舊的邏輯\n        this.selectedBuildingForBatch.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.selected && house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      }\n    }\n    if (housesToUpdate.length === 0) {\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\n      return;\n    }\n    // 調用API進行批次更新\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: housesToUpdate\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n        // 清除選擇狀態\n        this.selectedHouses.forEach(house => house.selected = false);\n        this.selectedHouses = [];\n        this.selectAll = false;\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  // 新增：取得狀態樣式類別\n  getStatusClass(house) {\n    const status = this.getHouseStatus(house);\n    return `status-${status}`;\n  }\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n  openHouseDialog(house) {\n    if (house.CHouseId) {\n      // 使用現有的openModel方法\n      this.openModel(this.dialog, house);\n    }\n  }\n  // 新增：建立扁平化房屋資料\n  buildFlattenedHouses(data) {\n    this.flattenedHouses = [];\n    data.forEach(household => {\n      const houseType = household.CHouseHold || '';\n      household.CHouses?.forEach(house => {\n        this.flattenedHouses.push({\n          CHouseHold: houseType,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId || 0,\n          CFloor: house.CFloor || 0,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 初始化篩選和分頁\n    this.onSearch();\n    // 調試：輸出狀態統計\n    this.debugStatusCounts();\n  }\n  // 調試：輸出狀態統計\n  debugStatusCounts() {\n    const statusCounts = {\n      active: 0,\n      pending: 0,\n      expired: 0,\n      'not-set': 0,\n      disabled: 0\n    };\n    this.flattenedHouses.forEach(house => {\n      const status = this.getHouseStatus(house);\n      if (statusCounts.hasOwnProperty(status)) {\n        statusCounts[status]++;\n      }\n    });\n    console.log('狀態統計:', statusCounts);\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\n  }\n  // 修改：搜尋和篩選\n  onSearch() {\n    // 記錄篩選前的已選擇項目\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\n    this.filteredHouses = this.flattenedHouses.filter(house => {\n      // 關鍵字搜尋\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n          return false;\n        }\n      }\n      // 棟別篩選\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n        return false;\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        if (!this.matchesStatusFilter(house)) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        if (house.CFloor !== floorNumber) {\n          return false;\n        }\n      }\n      return true;\n    });\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\n    this.selectedHouses = this.filteredHouses.filter(house => previouslySelectedIds.includes(house.CHouseId));\n    // 更新扁平化資料中的選擇狀態\n    this.flattenedHouses.forEach(house => {\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\n    });\n    // 更新全選狀態\n    this.updateSelectAllState();\n    // 重新計算分頁\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：更新全選狀態\n  updateSelectAllState() {\n    if (this.paginatedHouses.length === 0) {\n      this.selectAll = false;\n    } else {\n      this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n    }\n  }\n  // 修改：更新分頁\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n    // 更新全選狀態\n    this.updateSelectAllState();\n  }\n  // 新增：頁面大小變更\n  onPageSizeChange() {\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：跳轉頁面\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.updatePagination();\n    }\n  }\n  // 新增：取得可見頁碼\n  getVisiblePages() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 新增：全選/取消全選\n  onSelectAllChange() {\n    this.paginatedHouses.forEach(house => {\n      if (house.CHouseId) {\n        house.selected = this.selectAll;\n      }\n    });\n    this.updateSelectedHouses();\n  }\n  // 新增：單一選擇變更\n  onHouseSelectionChange() {\n    this.updateSelectedHouses();\n    this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n  }\n  // 新增：更新已選擇房屋列表\n  updateSelectedHouses() {\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n  }\n  // 新增：排序\n  sort(field) {\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    this.filteredHouses.sort((a, b) => {\n      let aValue = a[field];\n      let bValue = b[field];\n      // 處理日期排序\n      if (field.includes('Date')) {\n        aValue = aValue ? new Date(aValue).getTime() : 0;\n        bValue = bValue ? new Date(bValue).getTime() : 0;\n      }\n      // 處理數字排序\n      if (field === 'CFloor') {\n        aValue = Number(aValue) || 0;\n        bValue = Number(bValue) || 0;\n      }\n      // 處理字串排序\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (aValue < bValue) {\n        return this.sortDirection === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return this.sortDirection === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    this.updatePagination();\n  }\n  // 新增：TrackBy函數\n  trackByHouseId(_index, house) {\n    return house.CHouseId;\n  }\n  // 新增：取得狀態文字\n  getStatusText(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return '進行中';\n      case 'pending':\n        return '待開放';\n      case 'expired':\n        return '已過期';\n      case 'not-set':\n        return '未設定';\n      case 'disabled':\n        return '已停用';\n      default:\n        return '未知';\n    }\n  }\n  static {\n    this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingTimePeriodComponent,\n      selectors: [[\"ngx-setting-time-period\"]],\n      viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 57,\n      vars: 14,\n      consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"batchSettingDialog\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"batchStartDate\", \"\"], [\"batchEndDate\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"page-header-optimized\"], [1, \"page-description-section\"], [1, \"page-description\", \"text-muted\", \"mb-0\"], [1, \"compact-filters\"], [1, \"row\", \"g-3\", \"align-items-end\"], [1, \"col-lg-3\", \"col-md-4\"], [1, \"form-label\", \"small\", \"fw-medium\"], [1, \"text-danger\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-2\", \"col-md-3\"], [\"placeholder\", \"\\u5168\\u90E8\\u68DF\\u5225\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"\"], [1, \"col-lg-4\", \"col-md-5\"], [1, \"date-range-group\"], [\"size\", \"small\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u958B\\u59CB\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [1, \"date-separator\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u7D50\\u675F\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-lg-3\", \"col-md-12\"], [1, \"action-buttons\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-search\", \"me-1\"], [\"title\", \"\\u91CD\\u7F6E\\u7BE9\\u9078\\u689D\\u4EF6\", 1, \"btn\", \"btn-outline-secondary\", \"ms-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-undo\"], [\"class\", \"advanced-filters-panel\", 4, \"ngIf\"], [\"class\", \"table-view-enhanced mt-4\", 4, \"ngIf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [3, \"value\"], [1, \"advanced-filters-panel\"], [1, \"row\", \"g-3\", \"align-items-center\"], [\"nbPrefix\", \"\", \"icon\", \"search-outline\"], [\"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u578B\\u3001\\u68DF\\u5225...\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u72C0\\u614B\\u7BE9\\u9078\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"active\"], [\"value\", \"pending\"], [\"value\", \"expired\"], [\"value\", \"not-set\"], [\"value\", \"disabled\"], [\"placeholder\", \"\\u6A13\\u5C64\\u7BE9\\u9078\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-lg-2\", \"col-md-2\"], [\"placeholder\", \"\\u986F\\u793A\\u7B46\\u6578\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"filter-actions\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"table-view-enhanced\", \"mt-4\"], [1, \"data-summary-bar\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"summary-info\"], [1, \"total-count\"], [1, \"fas\", \"fa-database\", \"me-1\"], [\"class\", \"selected-count\", 4, \"ngIf\"], [1, \"quick-filters\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [1, \"fas\", \"fa-play-circle\", \"me-1\"], [1, \"fas\", \"fa-clock\", \"me-1\"], [1, \"fas\", \"fa-times-circle\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-not-set\", 3, \"click\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-1\"], [1, \"enhanced-toolbar\"], [1, \"batch-operations\"], [1, \"selection-controls\"], [1, \"select-all-checkbox\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fw-medium\"], [\"class\", \"batch-actions ms-3\", 4, \"ngIf\"], [1, \"table-controls\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\"], [1, \"pagination-summary\"], [1, \"text-muted\"], [1, \"enhanced-table-container\"], [1, \"table\", \"table-hover\", \"enhanced-table\"], [1, \"enhanced-table-header\"], [\"width\", \"50\", 1, \"text-center\"], [3, \"ngModelChange\", \"ngModel\"], [\"width\", \"120\", 1, \"sortable\", 3, \"click\"], [1, \"header-content\"], [1, \"fas\", \"fa-sort\", \"sort-icon\"], [\"width\", \"100\", 1, \"sortable\", 3, \"click\"], [\"width\", \"80\", 1, \"sortable\", 3, \"click\"], [\"width\", \"140\", 1, \"sortable\", 3, \"click\"], [\"width\", \"120\"], [\"width\", \"100\"], [3, \"table-row-selected\", \"table-row-disabled\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"enhanced-pagination-container\", 4, \"ngIf\"], [1, \"selected-count\"], [1, \"fas\", \"fa-check-square\", \"me-1\", \"text-primary\"], [1, \"text-primary\"], [1, \"batch-actions\", \"ms-3\"], [\"title\", \"\\u6279\\u6B21\\u8A2D\\u5B9A\\u9078\\u4E2D\\u7684\\u6236\\u5225\\u958B\\u653E\\u6642\\u6BB5\", 1, \"btn\", \"btn-warning\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-cogs\", \"me-1\"], [1, \"badge\", \"bg-light\", \"text-dark\", \"ms-1\"], [\"title\", \"\\u6E05\\u9664\\u9078\\u64C7\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"ms-2\", 3, \"click\"], [1, \"text-center\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"house-info\"], [1, \"house-name\", \"fw-medium\"], [\"class\", \"text-muted d-block\", 4, \"ngIf\"], [1, \"building-name\"], [1, \"floor-badge\"], [1, \"date-info\"], [\"class\", \"date-display\", 4, \"ngIf\"], [\"class\", \"not-set-text\", 4, \"ngIf\"], [1, \"status-display\"], [1, \"enhanced-status-badge\"], [1, \"status-icon\"], [1, \"status-text\"], [\"title\", \"\\u7DE8\\u8F2F\\u6642\\u6BB5\\u8A2D\\u5B9A\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-edit\"], [1, \"text-muted\", \"d-block\"], [1, \"date-display\"], [1, \"fas\", \"fa-calendar\", \"me-1\", \"text-success\"], [1, \"not-set-text\"], [1, \"fas\", \"fa-calendar\", \"me-1\", \"text-danger\"], [1, \"enhanced-pagination-container\"], [1, \"pagination-info-detailed\"], [1, \"ms-2\"], [1, \"pagination-nav\"], [1, \"pagination\", \"pagination-sm\", \"mb-0\"], [1, \"page-item\"], [\"title\", \"\\u7B2C\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [\"title\", \"\\u4E0A\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"\\u4E0B\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-right\"], [\"title\", \"\\u6700\\u5F8C\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"quick-jump\"], [1, \"input-group\", \"input-group-sm\", 2, \"width\", \"120px\"], [\"type\", \"number\", \"placeholder\", \"\\u9801\\u78BC\", 1, \"form-control\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"min\", \"max\"], [\"type\", \"button\", \"title\", \"\\u8DF3\\u8F49\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"mb-3\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"mt-2\"], [2, \"width\", \"600px\", \"max-height\", \"90vh\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"form-group\"], [1, \"text-red-600\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-fill\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"mx-2\"], [1, \"selection-options\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"alert\", \"alert-info\"], [1, \"selected-houses-preview\"], [\"class\", \"badge badge-primary mr-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"badge\", \"badge-primary\", \"mr-1\", \"mb-1\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"floor-selection\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-selection\"], [\"class\", \"house-selection ml-4\", 4, \"ngIf\"], [1, \"house-selection\", \"ml-4\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n      template: function SettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 9)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 10)(5, \"div\", 11)(6, \"p\", 12);\n          i0.ɵɵtext(7, \"\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u9593\\u7BC4\\u570D\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 13)(9, \"div\", 14)(10, \"div\", 15)(11, \"label\", 16);\n          i0.ɵɵtext(12, \"\\u5EFA\\u6848 \");\n          i0.ɵɵelementStart(13, \"span\", 17);\n          i0.ɵɵtext(14, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"nb-select\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_15_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_15_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildCaseChange());\n          });\n          i0.ɵɵtemplate(16, SettingTimePeriodComponent_nb_option_16_Template, 2, 2, \"nb-option\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 20)(18, \"label\", 16);\n          i0.ɵɵtext(19, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"nb-select\", 21);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_20_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_20_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildingChange());\n          });\n          i0.ɵɵelementStart(21, \"nb-option\", 22);\n          i0.ɵɵtext(22, \"\\u5168\\u90E8\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(23, SettingTimePeriodComponent_nb_option_23_Template, 2, 2, \"nb-option\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 23)(25, \"label\", 16);\n          i0.ɵɵtext(26, \"\\u958B\\u653E\\u65E5\\u671F\\u7BC4\\u570D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 24)(28, \"nb-form-field\", 25);\n          i0.ɵɵelement(29, \"nb-icon\", 26);\n          i0.ɵɵelementStart(30, \"input\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(31, \"nb-datepicker\", 28, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"span\", 29);\n          i0.ɵɵtext(34, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"nb-form-field\", 25);\n          i0.ɵɵelement(36, \"nb-icon\", 26);\n          i0.ɵɵelementStart(37, \"input\", 30);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_37_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"nb-datepicker\", 28, 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"div\", 31)(41, \"div\", 32)(42, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_42_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵelement(43, \"i\", 34);\n          i0.ɵɵtext(44, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_45_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetFilters());\n          });\n          i0.ɵɵelement(46, \"i\", 36);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(47, SettingTimePeriodComponent_div_47_Template, 38, 10, \"div\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(48, SettingTimePeriodComponent_div_48_Template, 73, 47, \"div\", 38)(49, SettingTimePeriodComponent_div_49_Template, 7, 0, \"div\", 39)(50, SettingTimePeriodComponent_div_50_Template, 9, 0, \"div\", 39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(51, SettingTimePeriodComponent_ng_template_51_Template, 36, 9, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(53, SettingTimePeriodComponent_ng_template_53_Template, 4, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(55, SettingTimePeriodComponent_ng_template_55_Template, 29, 6, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const StartDate_r30 = i0.ɵɵreference(32);\n          const EndDate_r31 = i0.ɵɵreference(39);\n          i0.ɵɵadvance(15);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingOptions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", StartDate_r30);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", EndDate_r31);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length === 0 && ctx.houseChangeDates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.MinValidator, i9.MaxValidator, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n.page-header-optimized[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n  border-radius: 0.75rem;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.1);\\n  margin-bottom: 1.5rem;\\n  overflow: hidden;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-description-section[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-description-section[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #6c757d;\\n  line-height: 1.4;\\n  text-align: center;\\n  font-weight: 400;\\n}\\n\\n.compact-filters[_ngcontent-%COMP%] {\\n  padding: 1.25rem 1.5rem;\\n  background: linear-gradient(to bottom, #fafafa 0%, #f8f9fa 100%);\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.15);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin-bottom: 0.375rem;\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545 !important;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0 0.25rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  justify-content: flex-end;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.3s ease;\\n  padding: 0.5rem 1rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #A69660 0%, #95854A 100%);\\n  border-color: #A69660;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(184, 166, 118, 0.3);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #6c757d;\\n  color: #6c757d;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n  transform: translateY(-1px);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 1.25rem;\\n  background-color: #ffffff;\\n  border-top: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .btn-outline-danger[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.advanced-filters[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 1.5rem;\\n  background-color: #ffffff;\\n  border-top: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]   .text-primary[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #B8A676;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%], \\n.advanced-filters[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-select.ng-touched.ng-valid[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.5);\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.25rem;\\n  border-radius: 0.5rem 0.5rem 0 0;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-bottom: none;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-warning[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-danger[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);\\n  border-color: #ffc107;\\n  color: #212529;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #856404;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border: 2px solid #ffc107;\\n  color: #856404;\\n  font-weight: 500;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\\n  border-color: #e0a800;\\n  color: #856404;\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(255, 193, 7, 0.2);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  color: #e0a800;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%] {\\n  background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);\\n  padding: 1rem 1.25rem;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-top: none;\\n  border-bottom: none;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  font-size: 0.875rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #D4B96A 0%, #C4A85A 100%);\\n  border-color: #D4B96A;\\n  color: #5a4a2a;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #C4A85A 0%, #B4984A 100%);\\n  border-color: #C4A85A;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  color: #B8A676;\\n  font-weight: 600;\\n  font-size: 0.7rem;\\n  padding: 0.2rem 0.4rem;\\n  border-radius: 0.25rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .pagination-summary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d;\\n  margin-left: 1rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(184, 166, 118, 0.2);\\n  border-radius: 0 0 0.5rem 0.5rem;\\n  overflow: hidden;\\n  box-shadow: 0 4px 8px rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  font-size: 0.875rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F8F6F0 0%, #F0EDE5 100%);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  border-bottom: 2px solid rgba(184, 166, 118, 0.2);\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .sort-icon[_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n  color: #6c757d;\\n  transition: opacity 0.3s ease;\\n  margin-left: 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover   .sort-icon[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  color: #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-selected[_ngcontent-%COMP%] {\\n  background-color: rgba(184, 166, 118, 0.15);\\n  border-left: 3px solid #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  background-color: #f8f9fa;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  vertical-align: middle;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .house-info[_ngcontent-%COMP%]   .house-name[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .house-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 500;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .floor-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);\\n  color: #495057;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 0.85rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .not-set-text[_ngcontent-%COMP%] {\\n  color: #856404;\\n  font-weight: 500;\\n  font-size: 0.85rem;\\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #ffc107;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .not-set-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 1rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.375rem;\\n  font-size: 0.7rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border-radius: 0.375rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: scale(1.1);\\n  box-shadow: 0 3px 6px rgba(0, 123, 255, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #E8F5E8 0%, #D4EDDA 100%);\\n  color: #2D5A2D;\\n  border-color: rgba(45, 90, 45, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-active[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-pending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FFF8E1 0%, #FFF3CD 100%);\\n  color: #8B6914;\\n  border-color: rgba(139, 105, 20, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-pending[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-expired[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FFEBEE 0%, #F8D7DA 100%);\\n  color: #8B2635;\\n  border-color: rgba(139, 38, 53, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-expired[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-not-set[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\\n  color: #856404;\\n  border-color: rgba(255, 193, 7, 0.4);\\n  font-weight: 600;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-not-set[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-disabled[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FAFAFA 0%, #F8F9FA 100%);\\n  color: #8A8A8A;\\n  border-color: rgba(138, 138, 138, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-disabled[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);\\n  padding: 1.25rem;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-top: none;\\n  border-radius: 0 0 0.5rem 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-info-detailed[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-info-detailed[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%] {\\n  margin: 0 0.125rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #495057;\\n  border-color: rgba(184, 166, 118, 0.3);\\n  transition: all 0.3s ease;\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  font-weight: 500;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: rgba(184, 166, 118, 0.5);\\n  color: #B8A676;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(184, 166, 118, 0.2);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n  box-shadow: 0 3px 6px rgba(184, 166, 118, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n  background-color: #fff;\\n  border-color: rgba(184, 166, 118, 0.2);\\n  cursor: not-allowed;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.3);\\n  font-size: 0.8rem;\\n  text-align: center;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.3);\\n  color: #495057;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n.search-enhanced[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #5a5a5a;\\n  border-color: rgba(184, 166, 118, 0.3);\\n  transition: all 0.2s ease;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: rgba(184, 166, 118, 0.5);\\n  color: #B8A676;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #aaa;\\n  background-color: #fff;\\n  border-color: rgba(184, 166, 118, 0.2);\\n}\\n\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  padding-left: 1rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\\n  gap: 0.25rem;\\n  margin-top: 0.5rem;\\n  padding: 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  margin-bottom: 1rem;\\n  border: 1px solid transparent;\\n  border-radius: 0.375rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%] {\\n  color: #0c5460;\\n  background-color: #d1ecf1;\\n  border-color: #bee5eb;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  font-weight: 600;\\n}\\n\\n@media (max-width: 992px) {\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n    justify-content: center;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    border-radius: 0.375rem 0.375rem 0 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    margin-top: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.75rem;\\n    text-align: left !important;\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .flex-fill[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .mx-2[_ngcontent-%COMP%] {\\n    margin: 0.25rem 0 !important;\\n    text-align: center;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n    margin-bottom: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 0.5rem;\\n  }\\n  .search-enhanced[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0.25rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(3) {\\n    display: none;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .integrated-header[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n    border-radius: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    border-radius: 0.25rem 0.25rem 0 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    margin-right: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n    display: none;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n    margin-bottom: 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .query-section[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(4) {\\n    display: none;\\n  }\\n}\\nnb-card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #5a5a5a;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n  border-color: #B8A676;\\n  transition: all 0.3s ease;\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(184, 166, 118, 0.3);\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-color: #dee2e6;\\n  color: #6c757d;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  color: #5a5a5a;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  margin-right: 0.5rem;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n.status-indicator.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);\\n}\\n.status-indicator.pending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #D4B96A 0%, #C4A85A 100%);\\n}\\n.status-indicator.expired[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F44336 0%, #E53935 100%);\\n}\\n.status-indicator.not-set[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9E9E9E 0%, #757575 100%);\\n}\\n.status-indicator.disabled[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #E0E0E0 0%, #BDBDBD 100%);\\n}\\n\\n  nb-select.appearance-outline .select-button {\\n  border-color: rgba(184, 166, 118, 0.3);\\n}\\n  nb-select.appearance-outline .select-button:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n  nb-form-field.appearance-outline .form-control {\\n  border-color: rgba(184, 166, 118, 0.3);\\n}\\n  nb-form-field.appearance-outline .form-control:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n  nb-checkbox .customised-control-input:checked ~ .customised-control-indicator {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n}\\n  nb-calendar-day-cell.selected {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "building_r3", "floor_r6", "ɵɵlistener", "SettingTimePeriodComponent_div_47_button_37_Template_button_click_0_listener", "ɵɵrestoreView", "_r7", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "clearAllFilters", "ɵɵelement", "ɵɵtwoWayListener", "SettingTimePeriodComponent_div_47_Template_input_ngModelChange_5_listener", "$event", "_r4", "ɵɵtwoWayBindingSet", "filterOptions", "searchKeyword", "onSearch", "SettingTimePeriodComponent_div_47_Template_nb_select_ngModelChange_7_listener", "statusFilter", "SettingTimePeriodComponent_div_47_Template_nb_select_selectedChange_7_listener", "SettingTimePeriodComponent_div_47_Template_nb_select_ngModelChange_21_listener", "floorFilter", "SettingTimePeriodComponent_div_47_Template_nb_select_selectedChange_21_listener", "ɵɵtemplate", "SettingTimePeriodComponent_div_47_nb_option_24_Template", "SettingTimePeriodComponent_div_47_Template_nb_select_ngModelChange_26_listener", "pageSize", "SettingTimePeriodComponent_div_47_Template_nb_select_selectedChange_26_listener", "onPageSizeChange", "SettingTimePeriodComponent_div_47_button_37_Template", "ɵɵtwoWayProperty", "availableFloors", "hasActiveFilters", "ɵɵtextInterpolate", "selectedHouses", "length", "SettingTimePeriodComponent_div_48_div_32_Template_button_click_1_listener", "_r9", "openBatchSetting", "SettingTimePeriodComponent_div_48_div_32_Template_button_click_6_listener", "clearSelection", "ɵɵpipeBind2", "house_r11", "CChangeStartDate", "CChangeEndDate", "SettingTimePeriodComponent_div_48_tr_71_Template_nb_checkbox_ngModelChange_2_listener", "_r10", "$implicit", "selected", "onHouseSelectionChange", "SettingTimePeriodComponent_div_48_tr_71_small_7_Template", "SettingTimePeriodComponent_div_48_tr_71_span_16_Template", "SettingTimePeriodComponent_div_48_tr_71_span_17_Template", "SettingTimePeriodComponent_div_48_tr_71_span_20_Template", "SettingTimePeriodComponent_div_48_tr_71_span_21_Template", "SettingTimePeriodComponent_div_48_tr_71_Template_button_click_30_listener", "dialog_r12", "ɵɵreference", "openModel", "ɵɵclassProp", "CHouseId", "CHouseHold", "CBuildingName", "CFloor", "ɵɵclassMap", "getStatusClass", "getStatusIcon", "getStatusText", "SettingTimePeriodComponent_div_48_div_72_li_21_Template_button_click_1_listener", "page_r15", "_r14", "goToPage", "currentPage", "SettingTimePeriodComponent_div_48_div_72_Template_button_click_16_listener", "_r13", "SettingTimePeriodComponent_div_48_div_72_Template_button_click_19_listener", "SettingTimePeriodComponent_div_48_div_72_li_21_Template", "SettingTimePeriodComponent_div_48_div_72_Template_button_click_23_listener", "SettingTimePeriodComponent_div_48_div_72_Template_button_click_26_listener", "totalPages", "SettingTimePeriodComponent_div_48_div_72_Template_input_ngModelChange_30_listener", "jumpToPage", "SettingTimePeriodComponent_div_48_div_72_Template_input_keyup_enter_30_listener", "jumpToPageAction", "SettingTimePeriodComponent_div_48_div_72_Template_button_click_31_listener", "ɵɵtextInterpolate3", "Math", "min", "filteredHouses", "getVisiblePages", "SettingTimePeriodComponent_div_48_span_10_Template", "SettingTimePeriodComponent_div_48_Template_button_click_13_listener", "_r8", "setQuickFilter", "SettingTimePeriodComponent_div_48_Template_button_click_16_listener", "SettingTimePeriodComponent_div_48_Template_button_click_19_listener", "SettingTimePeriodComponent_div_48_Template_button_click_22_listener", "SettingTimePeriodComponent_div_48_Template_nb_checkbox_ngModelChange_29_listener", "selectAll", "onSelectAllChange", "SettingTimePeriodComponent_div_48_div_32_Template", "SettingTimePeriodComponent_div_48_Template_nb_checkbox_ngModelChange_43_listener", "SettingTimePeriodComponent_div_48_Template_th_click_44_listener", "sort", "SettingTimePeriodComponent_div_48_Template_th_click_48_listener", "SettingTimePeriodComponent_div_48_Template_th_click_52_listener", "SettingTimePeriodComponent_div_48_Template_th_click_56_listener", "SettingTimePeriodComponent_div_48_Template_th_click_60_listener", "SettingTimePeriodComponent_div_48_tr_71_Template", "SettingTimePeriodComponent_div_48_div_72_Template", "sortField", "sortDirection", "paginatedHouses", "trackByHouseId", "selectedBuildingForBatch", "name", "house_r17", "SettingTimePeriodComponent_ng_template_51_div_28_span_4_Template", "SettingTimePeriodComponent_ng_template_51_div_28_span_5_Template", "slice", "SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener", "house_r22", "_r21", "ɵɵtextInterpolate2", "SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_nb_checkbox_1_Template", "floor_r20", "houses", "SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener", "_r19", "onFloorSelectionChange", "SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_div_3_Template", "floorNumber", "SettingTimePeriodComponent_ng_template_51_div_29_div_3_div_1_Template", "floors", "SettingTimePeriodComponent_ng_template_51_div_29_Template_nb_checkbox_ngModelChange_1_listener", "_r18", "batchSettings", "applyToAll", "SettingTimePeriodComponent_ng_template_51_div_29_div_3_Template", "flattenedHouses", "SettingTimePeriodComponent_ng_template_51_span_3_Template", "SettingTimePeriodComponent_ng_template_51_span_4_Template", "SettingTimePeriodComponent_ng_template_51_Template_input_ngModelChange_14_listener", "_r16", "startDate", "SettingTimePeriodComponent_ng_template_51_Template_input_ngModelChange_21_listener", "endDate", "SettingTimePeriodComponent_ng_template_51_div_28_Template", "SettingTimePeriodComponent_ng_template_51_div_29_Template", "SettingTimePeriodComponent_ng_template_51_Template_button_click_31_listener", "ref_r23", "dialogRef", "onClose", "SettingTimePeriodComponent_ng_template_51_Template_button_click_33_listener", "onBatchSubmit", "SettingTimePeriodComponent_ng_template_51_span_35_Template", "batchStartDate_r24", "batchEndDate_r25", "SettingTimePeriodComponent_ng_template_55_Template_input_ngModelChange_14_listener", "_r26", "selectedHouseChangeDate", "SettingTimePeriodComponent_ng_template_55_Template_input_ngModelChange_21_listener", "SettingTimePeriodComponent_ng_template_55_Template_button_click_25_listener", "ref_r27", "SettingTimePeriodComponent_ng_template_55_Template_button_click_27_listener", "onSubmit", "changeStartDate_r28", "changeEndDate_r29", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "buildingGroups", "buildingOptions", "selectedBuilding", "buildingFilter", "selectedBuildings", "selectedFloors", "loading", "undefined", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "searchQuery", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "getActiveFiltersCount", "count", "resetFilters", "status", "for<PERSON>ach", "house", "getHouseStatus", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "validation", "errorMessages", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "household", "CHouses", "floor", "push", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "onBuildCaseChange", "resetAllStates", "houseChangeDates", "convertedHouseArray", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "buildBuildingGroups", "buildFlattenedHouses", "data", "buildingMap", "Map", "houseType", "buildingName", "has", "set", "floorMap", "get", "localeCompare", "bg", "updateAvailableFloors", "building", "onBuildingChange", "getFilteredBuildings", "filter", "keyword", "toLowerCase", "hasMatchingHouse", "some", "includes", "hasMatchingStatus", "matchesStatusFilter", "parseInt", "hasMatchingFloor", "filteredBuilding", "hasValidHouses", "filteredFloor", "now", "today", "getFullYear", "getMonth", "getDate", "isNaN", "getTime", "console", "warn", "start", "end", "houseId", "startDateOnly", "endDateOnly", "error", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "hasSelectedHouses", "batchSettingDialog", "housesToUpdate", "openHouseDialog", "dialog", "debugStatusCounts", "statusCounts", "active", "pending", "expired", "disabled", "hasOwnProperty", "log", "toISOString", "split", "previouslySelectedIds", "updateSelectAllState", "updatePagination", "every", "ceil", "startIndex", "endIndex", "page", "pages", "maxVisible", "max", "i", "updateSelectedHouses", "field", "aValue", "bValue", "Number", "_index", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "Router", "i7", "EventService", "selectors", "viewQuery", "SettingTimePeriodComponent_Query", "rf", "ctx", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingTimePeriodComponent_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_15_listener", "_r1", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON>hange_15_listener", "SettingTimePeriodComponent_nb_option_16_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_20_listener", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_20_listener", "SettingTimePeriodComponent_nb_option_23_Template", "SettingTimePeriodComponent_Template_input_ngModelChange_30_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_37_listener", "SettingTimePeriodComponent_Template_button_click_42_listener", "SettingTimePeriodComponent_Template_button_click_45_listener", "SettingTimePeriodComponent_div_47_Template", "SettingTimePeriodComponent_div_48_Template", "SettingTimePeriodComponent_div_49_Template", "SettingTimePeriodComponent_div_50_Template", "SettingTimePeriodComponent_ng_template_51_Template", "ɵɵtemplateRefExtractor", "SettingTimePeriodComponent_ng_template_53_Template", "SettingTimePeriodComponent_ng_template_55_Template", "StartDate_r30", "EndDate_r31", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MinValidator", "MaxValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\n\r\n\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string; // 戶型\r\n  CBuildingName: string; // 棟別\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n\r\n  @ViewChild('batchSettingDialog') batchSettingDialog!: TemplateRef<any>;\r\n  @ViewChild('dialog') dialog!: TemplateRef<any>;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  // 新增的屬性\r\n  buildingGroups: BuildingGroup[] = [];\r\n  buildingOptions: string[] = [];\r\n  selectedBuilding: string = '';\r\n  availableFloors: number[] = [];\r\n\r\n  // 篩選和搜尋\r\n  filterOptions: FilterOptions = {\r\n    searchKeyword: '',\r\n    statusFilter: '',\r\n    floorFilter: '',\r\n    buildingFilter: ''\r\n  };\r\n\r\n  // 批次設定\r\n  batchSettings: BatchSettings = {\r\n    startDate: null,\r\n    endDate: null,\r\n    applyToAll: true,\r\n    selectedBuildings: [],\r\n    selectedFloors: [],\r\n    selectedHouses: []\r\n  };\r\n\r\n  selectedBuildingForBatch: BuildingGroup | null = null;\r\n\r\n  // 表格視圖相關屬性\r\n  flattenedHouses: ConvertedHouse[] = [];\r\n  filteredHouses: ConvertedHouse[] = [];\r\n  paginatedHouses: ConvertedHouse[] = [];\r\n  selectedHouses: ConvertedHouse[] = [];\r\n  selectAll: boolean = false;\r\n  loading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  override pageSize: number = 50;\r\n  totalPages: number = 1;\r\n\r\n  // 排序相關\r\n  sortField: string = '';\r\n  sortDirection: 'asc' | 'desc' = 'asc';\r\n\r\n  // 數學函數引用\r\n  Math = Math;\r\n\r\n  // 新增的UI控制屬性\r\n  jumpToPage: number = 1;\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  // 新增的UI控制方法\r\n  hasActiveFilters(): boolean {\r\n    return !!(this.filterOptions.searchKeyword ||\r\n      this.filterOptions.statusFilter ||\r\n      this.filterOptions.floorFilter);\r\n  }\r\n\r\n  getActiveFiltersCount(): number {\r\n    let count = 0;\r\n    if (this.filterOptions.searchKeyword) count++;\r\n    if (this.filterOptions.statusFilter) count++;\r\n    if (this.filterOptions.floorFilter) count++;\r\n    return count;\r\n  }\r\n\r\n  resetFilters(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    };\r\n    this.selectedBuilding = '';\r\n    this.clearAllFilters();\r\n  }\r\n\r\n  clearAllFilters(): void {\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n    this.onSearch();\r\n  }\r\n\r\n  setQuickFilter(status: string): void {\r\n    if (this.filterOptions.statusFilter === status) {\r\n      this.filterOptions.statusFilter = '';\r\n    } else {\r\n      this.filterOptions.statusFilter = status;\r\n    }\r\n    this.onSearch();\r\n  }\r\n\r\n  clearSelection(): void {\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n    this.flattenedHouses.forEach(house => house.selected = false);\r\n  }\r\n\r\n  getStatusIcon(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    switch (status) {\r\n      case 'active': return 'fas fa-play-circle';\r\n      case 'pending': return 'fas fa-clock';\r\n      case 'expired': return 'fas fa-times-circle';\r\n      case 'not-set': return 'fas fa-exclamation-triangle';\r\n      case 'disabled': return 'fas fa-ban';\r\n      default: return 'fas fa-exclamation-triangle';\r\n    }\r\n  }\r\n\r\n  jumpToPageAction(): void {\r\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\r\n      this.goToPage(this.jumpToPage);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CBuildingName: '未分類',\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：建案變更處理\r\n  onBuildCaseChange() {\r\n    // 重置所有相關狀態\r\n    this.resetAllStates();\r\n    // 執行查詢\r\n    this.getHouseChangeDate();\r\n  }\r\n\r\n  // 新增：重置所有狀態\r\n  resetAllStates() {\r\n    // 重置數據\r\n    this.houseChangeDates = [];\r\n    this.convertedHouseArray = [];\r\n    this.buildingGroups = [];\r\n    this.flattenedHouses = [];\r\n    this.filteredHouses = [];\r\n    this.paginatedHouses = [];\r\n    this.selectedHouses = [];\r\n\r\n    // 重置篩選條件\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    this.selectAll = false;\r\n    this.selectedBuilding = '';\r\n\r\n    // 重置分頁\r\n    this.currentPage = 1;\r\n    this.totalPages = 1;\r\n\r\n    // 重置可用選項\r\n    this.buildingOptions = [];\r\n    this.availableFloors = [];\r\n\r\n    // 重置排序\r\n    this.sortField = '';\r\n    this.sortDirection = 'asc';\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    // 如果沒有選擇建案，直接返回\r\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      this.loading = false;\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n          // 新增：建立棟別分組資料\r\n          this.buildBuildingGroups(res.Entries)\r\n          // 新增：建立扁平化資料\r\n          this.buildFlattenedHouses(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\r\n  buildBuildingGroups(data: GetHouseChangeDateRes[]) {\r\n    const buildingMap = new Map<string, Map<number, ConvertedHouse[]>>();\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || ''; // 戶型\r\n\r\n      household.CHouses?.forEach(house => {\r\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\r\n        const floor = house.CFloor || 0;\r\n\r\n        if (!buildingMap.has(buildingName)) {\r\n          buildingMap.set(buildingName, new Map<number, ConvertedHouse[]>());\r\n        }\r\n\r\n        const floorMap = buildingMap.get(buildingName)!;\r\n\r\n        if (!floorMap.has(floor)) {\r\n          floorMap.set(floor, []);\r\n        }\r\n\r\n        floorMap.get(floor)!.push({\r\n          CHouseHold: houseType, // 戶型\r\n          CBuildingName: buildingName, // 棟別\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: floor,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 轉換為BuildingGroup格式\r\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\r\n      const floors: FloorGroup[] = Array.from(floorMap.entries())\r\n        .sort(([a], [b]) => b - a) // 樓層由高到低排序\r\n        .map(([floorNumber, houses]) => ({\r\n          floorNumber,\r\n          houses: houses.sort((a, b) => {\r\n            // 排序邏輯：先按戶型排序，再按樓層排序\r\n            if (a.CHouseHold !== b.CHouseHold) {\r\n              return a.CHouseHold.localeCompare(b.CHouseHold);\r\n            }\r\n            return a.CFloor - b.CFloor;\r\n          }),\r\n          selected: false\r\n        }));\r\n\r\n      return {\r\n        name: buildingName,\r\n        floors,\r\n        selected: false\r\n      };\r\n    }).sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    // 更新棟別選項和可用樓層\r\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\r\n    this.updateAvailableFloors();\r\n  }\r\n\r\n  // 新增：更新可用樓層\r\n  updateAvailableFloors() {\r\n    const floorsSet = new Set<number>();\r\n\r\n    this.buildingGroups.forEach(building => {\r\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\r\n        building.floors.forEach(floor => {\r\n          floorsSet.add(floor.floorNumber);\r\n        });\r\n      }\r\n    });\r\n\r\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\r\n  }\r\n\r\n  // 修改：棟別選擇變更處理\r\n  onBuildingChange() {\r\n    // 重置選擇狀態\r\n    this.selectedHouses.forEach(house => house.selected = false);\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n\r\n    // 重置分頁到第一頁\r\n    this.currentPage = 1;\r\n\r\n    // 重置樓層篩選\r\n    this.filterOptions.floorFilter = '';\r\n\r\n    // 更新可用樓層\r\n    this.updateAvailableFloors();\r\n\r\n    // 設定棟別篩選並執行搜尋\r\n    this.filterOptions.buildingFilter = this.selectedBuilding;\r\n    this.onSearch();\r\n  }\r\n\r\n\r\n\r\n  // 新增：取得過濾後的棟別資料\r\n  getFilteredBuildings(): BuildingGroup[] {\r\n    return this.buildingGroups.filter(building => {\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 關鍵字搜尋 (搜尋戶型)\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        const hasMatchingHouse = building.floors.some(floor =>\r\n          floor.houses.some(house =>\r\n            house.CHouseHold.toLowerCase().includes(keyword) ||\r\n            house.CBuildingName.toLowerCase().includes(keyword)\r\n          )\r\n        );\r\n        if (!hasMatchingHouse) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        const hasMatchingStatus = building.floors.some(floor =>\r\n          floor.houses.some(house => this.matchesStatusFilter(house))\r\n        );\r\n        if (!hasMatchingStatus) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        const hasMatchingFloor = building.floors.some(floor =>\r\n          floor.floorNumber === floorNumber\r\n        );\r\n        if (!hasMatchingFloor) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }).map(building => {\r\n      // 對每個棟別，也要篩選其樓層和戶別\r\n      const filteredBuilding = { ...building };\r\n      filteredBuilding.floors = building.floors.filter(floor => {\r\n        // 樓層篩選\r\n        if (this.filterOptions.floorFilter) {\r\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n          if (floor.floorNumber !== floorNumber) {\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 檢查該樓層是否有符合條件的戶別\r\n        const hasValidHouses = floor.houses.some(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return hasValidHouses;\r\n      }).map(floor => {\r\n        // 篩選戶別\r\n        const filteredFloor = { ...floor };\r\n        filteredFloor.houses = floor.houses.filter(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return filteredFloor;\r\n      });\r\n\r\n      return filteredBuilding;\r\n    });\r\n  }\r\n\r\n  // 修復：檢查戶別是否符合狀態篩選\r\n  private matchesStatusFilter(house: ConvertedHouse): boolean {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (this.filterOptions.statusFilter) {\r\n      case 'active':\r\n        return status === 'active';\r\n      case 'pending':\r\n        return status === 'pending';\r\n      case 'expired':\r\n        return status === 'expired';\r\n      case 'not-set':\r\n        return status === 'not-set';\r\n      case 'disabled':\r\n        return status === 'disabled';\r\n      default:\r\n        return true; // 全部狀態\r\n    }\r\n  }\r\n\r\n  // 修復：取得戶別狀態\r\n  private getHouseStatus(house: ConvertedHouse): string {\r\n    if (!house.CHouseId) {\r\n      return 'disabled';\r\n    }\r\n\r\n    // 檢查是否有設定開放時段\r\n    if (!house.CChangeStartDate || !house.CChangeEndDate ||\r\n      house.CChangeStartDate === '' || house.CChangeEndDate === '') {\r\n      return 'not-set';\r\n    }\r\n\r\n    try {\r\n      // 處理日期字串，支援多種格式\r\n      const now = new Date();\r\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n\r\n      // 解析開始日期\r\n      let startDate: Date;\r\n      if (house.CChangeStartDate.includes('T')) {\r\n        startDate = new Date(house.CChangeStartDate);\r\n      } else {\r\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\r\n      }\r\n\r\n      // 解析結束日期\r\n      let endDate: Date;\r\n      if (house.CChangeEndDate.includes('T')) {\r\n        endDate = new Date(house.CChangeEndDate);\r\n      } else {\r\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\r\n      }\r\n\r\n      // 檢查日期有效性\r\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\r\n        console.warn('Invalid date format:', {\r\n          start: house.CChangeStartDate,\r\n          end: house.CChangeEndDate,\r\n          houseId: house.CHouseId\r\n        });\r\n        return 'not-set';\r\n      }\r\n\r\n      // 轉換為日期比較（不含時間）\r\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\r\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\r\n\r\n      // 判斷狀態\r\n      if (today < startDateOnly) {\r\n        return 'pending'; // 待開放\r\n      } else if (today >= startDateOnly && today <= endDateOnly) {\r\n        return 'active'; // 進行中\r\n      } else {\r\n        return 'expired'; // 已過期\r\n      }\r\n    } catch (error) {\r\n      console.error('Error parsing dates:', error, house);\r\n      return 'not-set';\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n\r\n  // 修改：開啟批次設定對話框\r\n  openBatchSetting(building?: BuildingGroup) {\r\n    this.selectedBuildingForBatch = building || null;\r\n\r\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\r\n    const hasSelectedHouses = this.selectedHouses.length > 0;\r\n\r\n    this.batchSettings = {\r\n      startDate: null,\r\n      endDate: null,\r\n      applyToAll: !hasSelectedHouses && !building,\r\n      selectedBuildings: building ? [building.name] : [],\r\n      selectedFloors: [],\r\n      selectedHouses: []\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    if (building) {\r\n      building.floors.forEach(floor => {\r\n        floor.selected = false;\r\n        floor.houses.forEach(house => house.selected = false);\r\n      });\r\n    }\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(this.batchSettingDialog);\r\n  }\r\n\r\n  // 新增：樓層選擇變更處理\r\n  onFloorSelectionChange(floor: FloorGroup) {\r\n    if (floor.selected) {\r\n      floor.houses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          house.selected = true;\r\n        }\r\n      });\r\n    } else {\r\n      floor.houses.forEach(house => house.selected = false);\r\n    }\r\n  }\r\n\r\n  // 修改：批次提交\r\n  onBatchSubmit(ref: any) {\r\n    // 驗證批次設定\r\n    this.valid.clear();\r\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\r\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 收集要更新的房屋\r\n    const housesToUpdate: any[] = [];\r\n\r\n    if (this.batchSettings.applyToAll) {\r\n      // 全部戶別\r\n      this.flattenedHouses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          housesToUpdate.push({\r\n            CHouseId: house.CHouseId,\r\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // 使用已選擇的戶別\r\n      if (this.selectedHouses.length > 0) {\r\n        this.selectedHouses.forEach(house => {\r\n          if (house.CHouseId) {\r\n            housesToUpdate.push({\r\n              CHouseId: house.CHouseId,\r\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n            });\r\n          }\r\n        });\r\n      } else if (this.selectedBuildingForBatch) {\r\n        // 如果沒有選擇的戶別，使用舊的邏輯\r\n        this.selectedBuildingForBatch.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.selected && house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n\r\n    if (housesToUpdate.length === 0) {\r\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\r\n      return;\r\n    }\r\n\r\n    // 調用API進行批次更新\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: housesToUpdate\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\r\n        // 清除選擇狀態\r\n        this.selectedHouses.forEach(house => house.selected = false);\r\n        this.selectedHouses = [];\r\n        this.selectAll = false;\r\n        this.getHouseChangeDate();\r\n        ref.close();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：取得狀態樣式類別\r\n  getStatusClass(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    return `status-${status}`;\r\n  }\r\n\r\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\r\n  openHouseDialog(house: ConvertedHouse) {\r\n    if (house.CHouseId) {\r\n      // 使用現有的openModel方法\r\n      this.openModel(this.dialog, house);\r\n    }\r\n  }\r\n\r\n  // 新增：建立扁平化房屋資料\r\n  buildFlattenedHouses(data: GetHouseChangeDateRes[]) {\r\n    this.flattenedHouses = [];\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || '';\r\n\r\n      household.CHouses?.forEach(house => {\r\n        this.flattenedHouses.push({\r\n          CHouseHold: houseType,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: house.CFloor || 0,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 初始化篩選和分頁\r\n    this.onSearch();\r\n\r\n    // 調試：輸出狀態統計\r\n    this.debugStatusCounts();\r\n  }\r\n\r\n  // 調試：輸出狀態統計\r\n  private debugStatusCounts() {\r\n    const statusCounts = {\r\n      active: 0,\r\n      pending: 0,\r\n      expired: 0,\r\n      'not-set': 0,\r\n      disabled: 0\r\n    };\r\n\r\n    this.flattenedHouses.forEach(house => {\r\n      const status = this.getHouseStatus(house);\r\n      if (statusCounts.hasOwnProperty(status)) {\r\n        statusCounts[status as keyof typeof statusCounts]++;\r\n      }\r\n    });\r\n\r\n    console.log('狀態統計:', statusCounts);\r\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\r\n  }\r\n\r\n\r\n\r\n  // 修改：搜尋和篩選\r\n  onSearch() {\r\n    // 記錄篩選前的已選擇項目\r\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\r\n\r\n    this.filteredHouses = this.flattenedHouses.filter(house => {\r\n      // 關鍵字搜尋\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n          !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        if (!this.matchesStatusFilter(house)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        if (house.CFloor !== floorNumber) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    });\r\n\r\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\r\n    this.selectedHouses = this.filteredHouses.filter(house =>\r\n      previouslySelectedIds.includes(house.CHouseId)\r\n    );\r\n\r\n    // 更新扁平化資料中的選擇狀態\r\n    this.flattenedHouses.forEach(house => {\r\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\r\n    });\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n\r\n    // 重新計算分頁\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：更新全選狀態\r\n  private updateSelectAllState() {\r\n    if (this.paginatedHouses.length === 0) {\r\n      this.selectAll = false;\r\n    } else {\r\n      this.selectAll = this.paginatedHouses.every(house =>\r\n        !house.CHouseId || house.selected\r\n      );\r\n    }\r\n  }\r\n\r\n  // 修改：更新分頁\r\n  updatePagination() {\r\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\r\n    const startIndex = (this.currentPage - 1) * this.pageSize;\r\n    const endIndex = startIndex + this.pageSize;\r\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 新增：頁面大小變更\r\n  onPageSizeChange() {\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：跳轉頁面\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  // 新增：取得可見頁碼\r\n  getVisiblePages(): number[] {\r\n    const pages: number[] = [];\r\n    const maxVisible = 5;\r\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\r\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\r\n\r\n    if (end - start + 1 < maxVisible) {\r\n      start = Math.max(1, end - maxVisible + 1);\r\n    }\r\n\r\n    for (let i = start; i <= end; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 新增：全選/取消全選\r\n  onSelectAllChange() {\r\n    this.paginatedHouses.forEach(house => {\r\n      if (house.CHouseId) {\r\n        house.selected = this.selectAll;\r\n      }\r\n    });\r\n    this.updateSelectedHouses();\r\n  }\r\n\r\n  // 新增：單一選擇變更\r\n  onHouseSelectionChange() {\r\n    this.updateSelectedHouses();\r\n    this.selectAll = this.paginatedHouses.every(house =>\r\n      !house.CHouseId || house.selected\r\n    );\r\n  }\r\n\r\n  // 新增：更新已選擇房屋列表\r\n  updateSelectedHouses() {\r\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\r\n  }\r\n\r\n  // 新增：排序\r\n  sort(field: string) {\r\n    if (this.sortField === field) {\r\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortDirection = 'asc';\r\n    }\r\n\r\n    this.filteredHouses.sort((a, b) => {\r\n      let aValue = (a as any)[field];\r\n      let bValue = (b as any)[field];\r\n\r\n      // 處理日期排序\r\n      if (field.includes('Date')) {\r\n        aValue = aValue ? new Date(aValue).getTime() : 0;\r\n        bValue = bValue ? new Date(bValue).getTime() : 0;\r\n      }\r\n\r\n      // 處理數字排序\r\n      if (field === 'CFloor') {\r\n        aValue = Number(aValue) || 0;\r\n        bValue = Number(bValue) || 0;\r\n      }\r\n\r\n      // 處理字串排序\r\n      if (typeof aValue === 'string') {\r\n        aValue = aValue.toLowerCase();\r\n        bValue = bValue.toLowerCase();\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return this.sortDirection === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return this.sortDirection === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：TrackBy函數\r\n  trackByHouseId(_index: number, house: ConvertedHouse): number {\r\n    return house.CHouseId;\r\n  }\r\n\r\n  // 新增：取得狀態文字\r\n  getStatusText(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (status) {\r\n      case 'active':\r\n        return '進行中';\r\n      case 'pending':\r\n        return '待開放';\r\n      case 'expired':\r\n        return '已過期';\r\n      case 'not-set':\r\n        return '未設定';\r\n      case 'disabled':\r\n        return '已停用';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <!-- 優化的Header區域 -->\r\n    <div class=\"page-header-optimized\">\r\n      <!-- 頁面說明 -->\r\n      <div class=\"page-description-section\">\r\n        <p class=\"page-description text-muted mb-0\">管理各戶別的選樣開放時間範圍</p>\r\n      </div>\r\n\r\n      <!-- 緊湊的主要篩選區域 -->\r\n      <div class=\"compact-filters\">\r\n        <div class=\"row g-3 align-items-end\">\r\n          <div class=\"col-lg-3 col-md-4\">\r\n            <label class=\"form-label small fw-medium\">建案 <span class=\"text-danger\">*</span></label>\r\n            <nb-select placeholder=\"請選擇建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\"\r\n              (selectedChange)=\"onBuildCaseChange()\" size=\"small\">\r\n              <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n                {{ case.CBuildCaseName }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <label class=\"form-label small fw-medium\">棟別</label>\r\n            <nb-select placeholder=\"全部棟別\" [(ngModel)]=\"selectedBuilding\" (selectedChange)=\"onBuildingChange()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部棟別</nb-option>\r\n              <nb-option *ngFor=\"let building of buildingOptions\" [value]=\"building\">\r\n                {{ building }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-4 col-md-5\">\r\n            <label class=\"form-label small fw-medium\">開放日期範圍</label>\r\n            <div class=\"date-range-group\">\r\n              <nb-form-field size=\"small\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"開始日期\" [nbDatepicker]=\"StartDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n                <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n              <span class=\"date-separator\">~</span>\r\n              <nb-form-field size=\"small\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"結束日期\" [nbDatepicker]=\"EndDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n                <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12\">\r\n            <div class=\"action-buttons\">\r\n              <button class=\"btn btn-primary\" (click)=\"getHouseChangeDate()\" [disabled]=\"loading\">\r\n                <i class=\"fas fa-search me-1\"></i>查詢\r\n              </button>\r\n              <button class=\"btn btn-outline-secondary ms-2\" (click)=\"resetFilters()\" [disabled]=\"loading\"\r\n                title=\"重置篩選條件\">\r\n                <i class=\"fas fa-undo\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      <!-- 進階篩選區域 -->\r\n      <div class=\"advanced-filters-panel\" *ngIf=\"flattenedHouses.length > 0\">\r\n        <div class=\"row g-3 align-items-center\">\r\n          <div class=\"col-lg-3 col-md-4\">\r\n            <nb-form-field size=\"small\">\r\n              <nb-icon nbPrefix icon=\"search-outline\"></nb-icon>\r\n              <input nbInput placeholder=\"搜尋戶型、棟別...\" [(ngModel)]=\"filterOptions.searchKeyword\"\r\n                (ngModelChange)=\"onSearch()\">\r\n            </nb-form-field>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <nb-select placeholder=\"狀態篩選\" [(ngModel)]=\"filterOptions.statusFilter\" (selectedChange)=\"onSearch()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部狀態</nb-option>\r\n              <nb-option value=\"active\">進行中</nb-option>\r\n              <nb-option value=\"pending\">待開放</nb-option>\r\n              <nb-option value=\"expired\">已過期</nb-option>\r\n              <nb-option value=\"not-set\">未設定</nb-option>\r\n              <nb-option value=\"disabled\">已停用</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <nb-select placeholder=\"樓層篩選\" [(ngModel)]=\"filterOptions.floorFilter\" (selectedChange)=\"onSearch()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部樓層</nb-option>\r\n              <nb-option *ngFor=\"let floor of availableFloors\" [value]=\"floor\">\r\n                {{ floor }}F\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-2\">\r\n            <nb-select placeholder=\"顯示筆數\" [(ngModel)]=\"pageSize\" (selectedChange)=\"onPageSizeChange()\" size=\"small\">\r\n              <nb-option [value]=\"50\">50筆/頁</nb-option>\r\n              <nb-option [value]=\"100\">100筆/頁</nb-option>\r\n              <nb-option [value]=\"200\">200筆/頁</nb-option>\r\n              <nb-option [value]=\"500\">500筆/頁</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12\">\r\n            <div class=\"filter-actions\">\r\n              <button class=\"btn btn-outline-danger btn-sm\" (click)=\"clearAllFilters()\" *ngIf=\"hasActiveFilters()\">\r\n                <i class=\"fas fa-times me-1\"></i>清除篩選\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 優化的表格視圖 -->\r\n    <div class=\"table-view-enhanced mt-4\" *ngIf=\"flattenedHouses.length > 0\">\r\n      <!-- 資料統計和快速篩選 -->\r\n      <div class=\"data-summary-bar\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"summary-info\">\r\n            <span class=\"total-count\">\r\n              <i class=\"fas fa-database me-1\"></i>\r\n              共 <strong>{{ filteredHouses.length }}</strong> 筆資料\r\n            </span>\r\n            <span class=\"selected-count\" *ngIf=\"selectedHouses.length > 0\">\r\n              <i class=\"fas fa-check-square me-1 text-primary\"></i>\r\n              已選 <strong class=\"text-primary\">{{ selectedHouses.length }}</strong> 筆\r\n            </span>\r\n          </div>\r\n          <div class=\"quick-filters\">\r\n            <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n              <button type=\"button\" class=\"btn\" [class.btn-primary]=\"filterOptions.statusFilter === 'active'\"\r\n                [class.btn-outline-primary]=\"filterOptions.statusFilter !== 'active'\"\r\n                (click)=\"setQuickFilter('active')\">\r\n                <i class=\"fas fa-play-circle me-1\"></i>進行中\r\n              </button>\r\n              <button type=\"button\" class=\"btn\" [class.btn-warning]=\"filterOptions.statusFilter === 'pending'\"\r\n                [class.btn-outline-warning]=\"filterOptions.statusFilter !== 'pending'\"\r\n                (click)=\"setQuickFilter('pending')\">\r\n                <i class=\"fas fa-clock me-1\"></i>待開放\r\n              </button>\r\n              <button type=\"button\" class=\"btn\" [class.btn-danger]=\"filterOptions.statusFilter === 'expired'\"\r\n                [class.btn-outline-danger]=\"filterOptions.statusFilter !== 'expired'\"\r\n                (click)=\"setQuickFilter('expired')\">\r\n                <i class=\"fas fa-times-circle me-1\"></i>已過期\r\n              </button>\r\n              <button type=\"button\" class=\"btn btn-not-set\"\r\n                [class.btn-not-set-active]=\"filterOptions.statusFilter === 'not-set'\"\r\n                [class.btn-not-set-outline]=\"filterOptions.statusFilter !== 'not-set'\"\r\n                (click)=\"setQuickFilter('not-set')\">\r\n                <i class=\"fas fa-exclamation-triangle me-1\"></i>未設定\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 優化的工具列 -->\r\n      <div class=\"enhanced-toolbar\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"batch-operations\">\r\n            <div class=\"selection-controls\">\r\n              <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\" class=\"select-all-checkbox\">\r\n                <span class=\"fw-medium\">全選</span>\r\n              </nb-checkbox>\r\n              <div class=\"batch-actions ms-3\" *ngIf=\"selectedHouses.length > 0\">\r\n                <button class=\"btn btn-warning btn-sm\" (click)=\"openBatchSetting()\" title=\"批次設定選中的戶別開放時段\">\r\n                  <i class=\"fas fa-cogs me-1\"></i>批次設定\r\n                  <span class=\"badge bg-light text-dark ms-1\">{{ selectedHouses.length }}</span>\r\n                </button>\r\n                <button class=\"btn btn-outline-danger btn-sm ms-2\" (click)=\"clearSelection()\" title=\"清除選擇\">\r\n                  <i class=\"fas fa-times me-1\"></i>清除選擇\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"table-controls\">\r\n            <div class=\"d-flex align-items-center gap-2\">\r\n              <div class=\"pagination-summary\">\r\n                <small class=\"text-muted\">\r\n                  顯示 {{ (currentPage - 1) * pageSize + 1 }} -\r\n                  {{ Math.min(currentPage * pageSize, filteredHouses.length) }} /\r\n                  共 {{ filteredHouses.length }} 筆\r\n                </small>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 優化的表格 -->\r\n      <div class=\"enhanced-table-container\">\r\n        <table class=\"table table-hover enhanced-table\">\r\n          <thead class=\"enhanced-table-header\">\r\n            <tr>\r\n              <th width=\"50\" class=\"text-center\">\r\n                <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\"></nb-checkbox>\r\n              </th>\r\n              <th width=\"120\" (click)=\"sort('CHouseHold')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  戶型\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CHouseHold' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CHouseHold' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"100\" (click)=\"sort('CBuildingName')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  棟別\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CBuildingName' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CBuildingName' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"80\" (click)=\"sort('CFloor')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  樓層\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CFloor' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CFloor' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"140\" (click)=\"sort('CChangeStartDate')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  開始日期\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CChangeStartDate' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CChangeStartDate' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"140\" (click)=\"sort('CChangeEndDate')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  結束日期\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CChangeEndDate' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CChangeEndDate' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"120\">\r\n                <div class=\"header-content\">\r\n                  狀態\r\n                </div>\r\n              </th>\r\n              <th width=\"100\">\r\n                <div class=\"header-content\">\r\n                  操作\r\n                </div>\r\n              </th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let house of paginatedHouses; trackBy: trackByHouseId\"\r\n              [class.table-row-selected]=\"house.selected\" [class.table-row-disabled]=\"!house.CHouseId\">\r\n              <td class=\"text-center\">\r\n                <nb-checkbox [(ngModel)]=\"house.selected\" [disabled]=\"!house.CHouseId\"\r\n                  (ngModelChange)=\"onHouseSelectionChange()\"></nb-checkbox>\r\n              </td>\r\n              <td>\r\n                <div class=\"house-info\">\r\n                  <span class=\"house-name fw-medium\">{{ house.CHouseHold }}</span>\r\n                  <small class=\"text-muted d-block\" *ngIf=\"!house.CHouseId\">無資料</small>\r\n                </div>\r\n              </td>\r\n              <td>\r\n                <span class=\"building-name\">{{ house.CBuildingName }}</span>\r\n              </td>\r\n              <td>\r\n                <span class=\"floor-badge\">{{ house.CFloor }}F</span>\r\n              </td>\r\n              <td>\r\n                <div class=\"date-info\">\r\n                  <span *ngIf=\"house.CChangeStartDate\" class=\"date-display\">\r\n                    <i class=\"fas fa-calendar me-1 text-success\"></i>\r\n                    {{ house.CChangeStartDate | date:'yyyy-MM-dd' }}\r\n                  </span>\r\n                  <span *ngIf=\"!house.CChangeStartDate\" class=\"not-set-text\">\r\n                    <i class=\"fas fa-exclamation-triangle me-1\"></i>未設定\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td>\r\n                <div class=\"date-info\">\r\n                  <span *ngIf=\"house.CChangeEndDate\" class=\"date-display\">\r\n                    <i class=\"fas fa-calendar me-1 text-danger\"></i>\r\n                    {{ house.CChangeEndDate | date:'yyyy-MM-dd' }}\r\n                  </span>\r\n                  <span *ngIf=\"!house.CChangeEndDate\" class=\"not-set-text\">\r\n                    <i class=\"fas fa-exclamation-triangle me-1\"></i>未設定\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"text-center\">\r\n                <div class=\"status-display\">\r\n                  <span class=\"enhanced-status-badge\" [class]=\"getStatusClass(house)\">\r\n                    <i class=\"status-icon\" [class]=\"getStatusIcon(house)\"></i>\r\n                    <span class=\"status-text\">{{ getStatusText(house) }}</span>\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"text-center\">\r\n                <div class=\"action-buttons\">\r\n                  <button class=\"btn btn-sm btn-outline-primary\" [disabled]=\"!house.CHouseId\"\r\n                    (click)=\"openModel(dialog, house)\" title=\"編輯時段設定\">\r\n                    <i class=\"fas fa-edit\"></i>\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 優化的分頁控制 -->\r\n      <div class=\"enhanced-pagination-container\" *ngIf=\"totalPages > 1\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"pagination-info-detailed\">\r\n            <span class=\"text-muted\">\r\n              第 <strong>{{ currentPage }}</strong> 頁，共 <strong>{{ totalPages }}</strong> 頁\r\n              <span class=\"ms-2\">\r\n                (顯示第 {{ (currentPage - 1) * pageSize + 1 }} -\r\n                {{ Math.min(currentPage * pageSize, filteredHouses.length) }} 筆，\r\n                共 {{ filteredHouses.length }} 筆)\r\n              </span>\r\n            </span>\r\n          </div>\r\n\r\n          <nav class=\"pagination-nav\">\r\n            <ul class=\"pagination pagination-sm mb-0\">\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\" title=\"第一頁\">\r\n                  <i class=\"fas fa-angle-double-left\"></i>\r\n                </button>\r\n              </li>\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\"\r\n                  title=\"上一頁\">\r\n                  <i class=\"fas fa-angle-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 頁碼顯示 -->\r\n              <li class=\"page-item\" *ngFor=\"let page of getVisiblePages()\" [class.active]=\"page === currentPage\">\r\n                <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n              </li>\r\n\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\" [disabled]=\"currentPage === totalPages\"\r\n                  title=\"下一頁\">\r\n                  <i class=\"fas fa-angle-right\"></i>\r\n                </button>\r\n              </li>\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                <button class=\"page-link\" (click)=\"goToPage(totalPages)\" [disabled]=\"currentPage === totalPages\"\r\n                  title=\"最後一頁\">\r\n                  <i class=\"fas fa-angle-double-right\"></i>\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n\r\n          <!-- 快速跳轉 -->\r\n          <div class=\"quick-jump\">\r\n            <div class=\"input-group input-group-sm\" style=\"width: 120px;\">\r\n              <input type=\"number\" class=\"form-control\" placeholder=\"頁碼\" [(ngModel)]=\"jumpToPage\"\r\n                (keyup.enter)=\"jumpToPageAction()\" [min]=\"1\" [max]=\"totalPages\">\r\n              <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"jumpToPageAction()\" title=\"跳轉\">\r\n                <i class=\"fas fa-arrow-right\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 當沒有資料時顯示 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"flattenedHouses.length === 0 && houseChangeDates.length === 0\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <i class=\"fas fa-info-circle fa-2x mb-3\"></i>\r\n            <p>請選擇建案後查詢資料</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n\r\n    <!-- 載入中狀態 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"loading\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <div class=\"spinner-border\" role=\"status\">\r\n              <span class=\"sr-only\">載入中...</span>\r\n            </div>\r\n            <p class=\"mt-2\">資料載入中，請稍候...</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 批次設定對話框 -->\r\n<ng-template #batchSettingDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:600px; max-height: 90vh\">\r\n    <nb-card-header>\r\n      批次設定\r\n      <span *ngIf=\"selectedHouses.length > 0\" class=\"text-primary\">\r\n        - 已選擇 {{ selectedHouses.length }} 個戶別\r\n      </span>\r\n      <span *ngIf=\"selectedBuildingForBatch && selectedHouses.length === 0\">\r\n        - {{ selectedBuildingForBatch.name }}\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 時間設定 -->\r\n      <div class=\"form-group\">\r\n        <label>開放時間起訖 <span class=\"text-red-600\">*</span></label>\r\n        <div class=\"d-flex align-items-center\">\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchStartDate\"\r\n              [(ngModel)]=\"batchSettings.startDate\">\r\n            <nb-datepicker #batchStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <span class=\"mx-2\">~</span>\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchEndDate\"\r\n              [(ngModel)]=\"batchSettings.endDate\">\r\n            <nb-datepicker #batchEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 選擇範圍 -->\r\n      <div class=\"form-group\">\r\n        <label>適用範圍</label>\r\n        <div class=\"selection-options\">\r\n          <!-- 如果有已選擇的戶別，顯示已選擇的資訊 -->\r\n          <div *ngIf=\"selectedHouses.length > 0\" class=\"alert alert-info\">\r\n            <h6>將套用到已選擇的 {{ selectedHouses.length }} 個戶別：</h6>\r\n            <div class=\"selected-houses-preview\">\r\n              <span *ngFor=\"let house of selectedHouses.slice(0, 10); let i = index\"\r\n                class=\"badge badge-primary mr-1 mb-1\">\r\n                {{ house.CHouseHold }} ({{ house.CBuildingName }}-{{ house.CFloor }}F)\r\n              </span>\r\n              <span *ngIf=\"selectedHouses.length > 10\" class=\"text-muted\">\r\n                ...等 {{ selectedHouses.length - 10 }} 個\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 如果沒有已選擇的戶別，顯示選擇選項 -->\r\n          <div *ngIf=\"selectedHouses.length === 0\">\r\n            <nb-checkbox [(ngModel)]=\"batchSettings.applyToAll\">\r\n              全部戶別 ({{ flattenedHouses.length }} 個)\r\n            </nb-checkbox>\r\n            <div class=\"mt-2\" *ngIf=\"!batchSettings.applyToAll && selectedBuildingForBatch\">\r\n              <div class=\"floor-selection\" *ngFor=\"let floor of selectedBuildingForBatch.floors\">\r\n                <nb-checkbox [(ngModel)]=\"floor.selected\" (ngModelChange)=\"onFloorSelectionChange(floor)\">\r\n                  {{ floor.floorNumber }}F ({{ floor.houses.length }} 戶)\r\n                </nb-checkbox>\r\n                <div class=\"house-selection ml-4\" *ngIf=\"floor.selected\">\r\n                  <nb-checkbox *ngFor=\"let house of floor.houses\" [(ngModel)]=\"house.selected\"\r\n                    [disabled]=\"!house.CHouseId\">\r\n                    {{ house.CHouseHold }} ({{ house.CBuildingName }})\r\n                  </nb-checkbox>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-end\">\r\n      <button class=\"btn btn-secondary me-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onBatchSubmit(ref)\">\r\n        批次設定\r\n        <span *ngIf=\"selectedHouses.length > 0\">({{ selectedHouses.length }} 個戶別)</span>\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      您正在編輯 <span class=\"text-green-600\">{{selectedHouseChangeDate.CHouseHold}} -\r\n        {{selectedHouseChangeDate.CFloor}}F</span> 的選樣開放時段\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cFloorFrom\" class=\"label col-3\">開放時間起訖<span class=\"text-red-600\">*</span>\r\n        </label>\r\n        <nb-form-field class=\"ml-3\">\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeStartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeStartDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeStartDate\">\r\n          <nb-datepicker #changeStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n        <label for=\"CChangeStartDate\" class=\"label col-1\"> ~\r\n        </label>\r\n        <nb-form-field>\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeEndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeEndDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeEndDate\">\r\n          <nb-datepicker #changeEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AAUvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;;;ICEtEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IASAR,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAK,WAAA,CAAkB;IACpET,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,WAAA,MACF;;;;;IAkEAT,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAM,QAAA,CAAe;IAC9DV,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAG,QAAA,OACF;;;;;;IAeAV,EAAA,CAAAC,cAAA,iBAAqG;IAAvDD,EAAA,CAAAW,UAAA,mBAAAC,6EAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IACvElB,EAAA,CAAAmB,SAAA,YAAiC;IAAAnB,EAAA,CAAAE,MAAA,gCACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA1CXH,EAHN,CAAAC,cAAA,cAAuE,cAC7B,cACP,wBACD;IAC1BD,EAAA,CAAAmB,SAAA,kBAAkD;IAClDnB,EAAA,CAAAC,cAAA,gBAC+B;IADSD,EAAA,CAAAoB,gBAAA,2BAAAC,0EAAAC,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAU,aAAA,CAAAC,aAAA,EAAAJ,MAAA,MAAAP,MAAA,CAAAU,aAAA,CAAAC,aAAA,GAAAJ,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAyC;IAC/EtB,EAAA,CAAAW,UAAA,2BAAAU,0EAAA;MAAArB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAAY,QAAA,EAAU;IAAA,EAAC;IAElC3B,EAHI,CAAAG,YAAA,EAC+B,EACjB,EACZ;IAGJH,EADF,CAAAC,cAAA,cAA+B,oBAEd;IADeD,EAAA,CAAAoB,gBAAA,2BAAAQ,8EAAAN,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAU,aAAA,CAAAI,YAAA,EAAAP,MAAA,MAAAP,MAAA,CAAAU,aAAA,CAAAI,YAAA,GAAAP,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAwC;IAACtB,EAAA,CAAAW,UAAA,4BAAAmB,+EAAA;MAAA9B,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAkBF,MAAA,CAAAY,QAAA,EAAU;IAAA,EAAC;IAElG3B,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,qBAA0B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA4B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAA+B,qBAEd;IADeD,EAAA,CAAAoB,gBAAA,2BAAAW,+EAAAT,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAU,aAAA,CAAAO,WAAA,EAAAV,MAAA,MAAAP,MAAA,CAAAU,aAAA,CAAAO,WAAA,GAAAV,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAuC;IAACtB,EAAA,CAAAW,UAAA,4BAAAsB,gFAAA;MAAAjC,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAkBF,MAAA,CAAAY,QAAA,EAAU;IAAA,EAAC;IAEjG3B,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAkC,UAAA,KAAAC,uDAAA,wBAAiE;IAIrEnC,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,eAA+B,qBAC2E;IAA1ED,EAAA,CAAAoB,gBAAA,2BAAAgB,+EAAAd,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAsB,QAAA,EAAAf,MAAA,MAAAP,MAAA,CAAAsB,QAAA,GAAAf,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAsB;IAACtB,EAAA,CAAAW,UAAA,4BAAA2B,gFAAA;MAAAtC,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAkBF,MAAA,CAAAwB,gBAAA,EAAkB;IAAA,EAAC;IACxFvC,EAAA,CAAAC,cAAA,qBAAwB;IAAAD,EAAA,CAAAE,MAAA,uBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAAgC,eACF;IAC1BD,EAAA,CAAAkC,UAAA,KAAAM,oDAAA,qBAAqG;IAM7GxC,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IA5C0CH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAU,aAAA,CAAAC,aAAA,CAAyC;IAMrD1B,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAU,aAAA,CAAAI,YAAA,CAAwC;IAYxC7B,EAAA,CAAAM,SAAA,IAAuC;IAAvCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAU,aAAA,CAAAO,WAAA,CAAuC;IAGtChC,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAA2B,eAAA,CAAkB;IAOnB1C,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAsB,QAAA,CAAsB;IACvCrC,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAI,UAAA,aAAY;IACZJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IAMmDJ,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA4B,gBAAA,GAAwB;;;;;IAmBrG3C,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAmB,SAAA,YAAqD;IACrDnB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,eACvE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAD2BH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA8B,cAAA,CAAAC,MAAA,CAA2B;;;;;;IAwCzD9C,EADF,CAAAC,cAAA,eAAkE,kBAC0B;IAAnDD,EAAA,CAAAW,UAAA,mBAAAoC,0EAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAAmC,GAAA;MAAA,MAAAjC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAkC,gBAAA,EAAkB;IAAA,EAAC;IACjEjD,EAAA,CAAAmB,SAAA,aAAgC;IAAAnB,EAAA,CAAAE,MAAA,gCAChC;IAAAF,EAAA,CAAAC,cAAA,gBAA4C;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IACzEF,EADyE,CAAAG,YAAA,EAAO,EACvE;IACTH,EAAA,CAAAC,cAAA,kBAA2F;IAAxCD,EAAA,CAAAW,UAAA,mBAAAuC,0EAAA;MAAAlD,EAAA,CAAAa,aAAA,CAAAmC,GAAA;MAAA,MAAAjC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAoC,cAAA,EAAgB;IAAA,EAAC;IAC3EnD,EAAA,CAAAmB,SAAA,YAAiC;IAAAnB,EAAA,CAAAE,MAAA,gCACnC;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAL0CH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA8B,cAAA,CAAAC,MAAA,CAA2B;;;;;IA6FvE9C,EAAA,CAAAC,cAAA,iBAA0D;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAWrEH,EAAA,CAAAC,cAAA,gBAA0D;IACxDD,EAAA,CAAAmB,SAAA,aAAiD;IACjDnB,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAoD,WAAA,OAAAC,SAAA,CAAAC,gBAAA,qBACF;;;;;IACAtD,EAAA,CAAAC,cAAA,gBAA2D;IACzDD,EAAA,CAAAmB,SAAA,YAAgD;IAAAnB,EAAA,CAAAE,MAAA,0BAClD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAKPH,EAAA,CAAAC,cAAA,gBAAwD;IACtDD,EAAA,CAAAmB,SAAA,aAAgD;IAChDnB,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAoD,WAAA,OAAAC,SAAA,CAAAE,cAAA,qBACF;;;;;IACAvD,EAAA,CAAAC,cAAA,gBAAyD;IACvDD,EAAA,CAAAmB,SAAA,YAAgD;IAAAnB,EAAA,CAAAE,MAAA,0BAClD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAlCTH,EAHJ,CAAAC,cAAA,SAC2F,cACjE,uBAEuB;IADhCD,EAAA,CAAAoB,gBAAA,2BAAAoC,sFAAAlC,MAAA;MAAA,MAAA+B,SAAA,GAAArD,EAAA,CAAAa,aAAA,CAAA4C,IAAA,EAAAC,SAAA;MAAA1D,EAAA,CAAAwB,kBAAA,CAAA6B,SAAA,CAAAM,QAAA,EAAArC,MAAA,MAAA+B,SAAA,CAAAM,QAAA,GAAArC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAA4B;IACvCtB,EAAA,CAAAW,UAAA,2BAAA6C,sFAAA;MAAAxD,EAAA,CAAAa,aAAA,CAAA4C,IAAA;MAAA,MAAA1C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAA6C,sBAAA,EAAwB;IAAA,EAAC;IAC9C5D,EAD+C,CAAAG,YAAA,EAAc,EACxD;IAGDH,EAFJ,CAAAC,cAAA,SAAI,eACsB,gBACa;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAAkC,UAAA,IAAA2B,wDAAA,qBAA0D;IAE9D7D,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,SAAI,gBAC0B;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACzD;IAEHH,EADF,CAAAC,cAAA,UAAI,iBACwB;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAC/CF,EAD+C,CAAAG,YAAA,EAAO,EACjD;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACqB;IAKrBD,EAJA,CAAAkC,UAAA,KAAA4B,wDAAA,oBAA0D,KAAAC,wDAAA,oBAIC;IAI/D/D,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACqB;IAKrBD,EAJA,CAAAkC,UAAA,KAAA8B,wDAAA,oBAAwD,KAAAC,wDAAA,oBAIC;IAI7DjE,EADE,CAAAG,YAAA,EAAM,EACH;IAGDH,EAFJ,CAAAC,cAAA,eAAwB,gBACM,iBAC0C;IAClED,EAAA,CAAAmB,SAAA,cAA0D;IAC1DnB,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAG1DF,EAH0D,CAAAG,YAAA,EAAO,EACtD,EACH,EACH;IAGDH,EAFJ,CAAAC,cAAA,eAAwB,eACM,mBAE0B;IAAlDD,EAAA,CAAAW,UAAA,mBAAAuD,0EAAA;MAAA,MAAAb,SAAA,GAAArD,EAAA,CAAAa,aAAA,CAAA4C,IAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAmD,UAAA,GAAAnE,EAAA,CAAAoE,WAAA;MAAA,OAAApE,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsD,SAAA,CAAAF,UAAA,EAAAd,SAAA,CAAwB;IAAA,EAAC;IAClCrD,EAAA,CAAAmB,SAAA,cAA2B;IAInCnB,EAHM,CAAAG,YAAA,EAAS,EACL,EACH,EACF;;;;;IAvDyCH,EAA5C,CAAAsE,WAAA,uBAAAjB,SAAA,CAAAM,QAAA,CAA2C,wBAAAN,SAAA,CAAAkB,QAAA,CAA6C;IAEzEvE,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAyC,gBAAA,YAAAY,SAAA,CAAAM,QAAA,CAA4B;IAAC3D,EAAA,CAAAI,UAAA,cAAAiD,SAAA,CAAAkB,QAAA,CAA4B;IAKjCvE,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAA4C,iBAAA,CAAAS,SAAA,CAAAmB,UAAA,CAAsB;IACtBxE,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAI,UAAA,UAAAiD,SAAA,CAAAkB,QAAA,CAAqB;IAI9BvE,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA4C,iBAAA,CAAAS,SAAA,CAAAoB,aAAA,CAAyB;IAG3BzE,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,kBAAA,KAAA8C,SAAA,CAAAqB,MAAA,MAAmB;IAIpC1E,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,SAAAiD,SAAA,CAAAC,gBAAA,CAA4B;IAI5BtD,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAI,UAAA,UAAAiD,SAAA,CAAAC,gBAAA,CAA6B;IAO7BtD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,SAAAiD,SAAA,CAAAE,cAAA,CAA0B;IAI1BvD,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAAiD,SAAA,CAAAE,cAAA,CAA2B;IAOEvD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAA2E,UAAA,CAAA5D,MAAA,CAAA6D,cAAA,CAAAvB,SAAA,EAA+B;IAC1CrD,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAA2E,UAAA,CAAA5D,MAAA,CAAA8D,aAAA,CAAAxB,SAAA,EAA8B;IAC3BrD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA+D,aAAA,CAAAzB,SAAA,EAA0B;IAMPrD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,cAAAiD,SAAA,CAAAkB,QAAA,CAA4B;;;;;;IAyC7EvE,EADF,CAAAC,cAAA,cAAmG,kBAC9C;IAAzBD,EAAA,CAAAW,UAAA,mBAAAoE,gFAAA;MAAA,MAAAC,QAAA,GAAAhF,EAAA,CAAAa,aAAA,CAAAoE,IAAA,EAAAvB,SAAA;MAAA,MAAA3C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAF,QAAA,CAAc;IAAA,EAAC;IAAChF,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFwDH,EAAA,CAAAsE,WAAA,WAAAU,QAAA,KAAAjE,MAAA,CAAAoE,WAAA,CAAqC;IAC7CnF,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAA4C,iBAAA,CAAAoC,QAAA,CAAU;;;;;;IA1BjEhF,EAHN,CAAAC,cAAA,eAAkE,cACD,eACvB,eACX;IACvBD,EAAA,CAAAE,MAAA,eAAE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,gBAC3E;IAAAF,EAAA,CAAAC,cAAA,iBAAmB;IACjBD,EAAA,CAAAE,MAAA,IAGF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACF,EACH;IAKAH,EAHN,CAAAC,cAAA,gBAA4B,eACgB,eACmB,mBACkC;IAAjED,EAAA,CAAAW,UAAA,mBAAAyE,2EAAA;MAAApF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAC7ClF,EAAA,CAAAmB,SAAA,cAAwC;IAE5CnB,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,eAA2D,mBAE3C;IADYD,EAAA,CAAAW,UAAA,mBAAA2E,2EAAA;MAAAtF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAnE,MAAA,CAAAoE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAE3DnF,EAAA,CAAAmB,SAAA,cAAiC;IAErCnB,EADE,CAAAG,YAAA,EAAS,EACN;IAGLH,EAAA,CAAAkC,UAAA,KAAAqD,uDAAA,kBAAmG;IAKjGvF,EADF,CAAAC,cAAA,eAAoE,mBAEpD;IADYD,EAAA,CAAAW,UAAA,mBAAA6E,2EAAA;MAAAxF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAnE,MAAA,CAAAoE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAE3DnF,EAAA,CAAAmB,SAAA,cAAkC;IAEtCnB,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,eAAoE,mBAEnD;IADWD,EAAA,CAAAW,UAAA,mBAAA8E,2EAAA;MAAAzF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAnE,MAAA,CAAA2E,UAAA,CAAoB;IAAA,EAAC;IAEtD1F,EAAA,CAAAmB,SAAA,cAAyC;IAIjDnB,EAHM,CAAAG,YAAA,EAAS,EACN,EACF,EACD;IAKFH,EAFJ,CAAAC,cAAA,gBAAwB,gBACwC,kBAEM;IADPD,EAAA,CAAAoB,gBAAA,2BAAAuE,kFAAArE,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA6E,UAAA,EAAAtE,MAAA,MAAAP,MAAA,CAAA6E,UAAA,GAAAtE,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAwB;IACjFtB,EAAA,CAAAW,UAAA,yBAAAkF,gFAAA;MAAA7F,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAeF,MAAA,CAAA+E,gBAAA,EAAkB;IAAA,EAAC;IADpC9F,EAAA,CAAAG,YAAA,EACkE;IAClEH,EAAA,CAAAC,cAAA,mBAAgG;IAAxCD,EAAA,CAAAW,UAAA,mBAAAoF,2EAAA;MAAA/F,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA+E,gBAAA,EAAkB;IAAA,EAAC;IAClF9F,EAAA,CAAAmB,SAAA,cAAkC;IAK5CnB,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAtDYH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAAoE,WAAA,CAAiB;IAAsBnF,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA2E,UAAA,CAAgB;IAE/D1F,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAgG,kBAAA,2BAAAjF,MAAA,CAAAoE,WAAA,QAAApE,MAAA,CAAAsB,QAAA,aAAAtB,MAAA,CAAAkF,IAAA,CAAAC,GAAA,CAAAnF,MAAA,CAAAoE,WAAA,GAAApE,MAAA,CAAAsB,QAAA,EAAAtB,MAAA,CAAAoF,cAAA,CAAArD,MAAA,4BAAA/B,MAAA,CAAAoF,cAAA,CAAArD,MAAA,cAGF;IAMsB9C,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,OAAoC;IACRnF,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,OAA8B;IAI1DnF,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,OAAoC;IACMnF,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,OAA8B;IAOvDnF,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAqF,eAAA,GAAoB;IAIrCpG,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAA6C;IACH1F,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAAuC;IAKjF1F,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAA6C;IACR1F,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAAuC;IAWvC1F,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA6E,UAAA,CAAwB;IACpC5F,EAAV,CAAAI,UAAA,UAAS,QAAAW,MAAA,CAAA2E,UAAA,CAAmB;;;;;;IArPnE1F,EALR,CAAAC,cAAA,cAAyE,cAEzC,cACmC,cACnC,eACE;IACxBD,EAAA,CAAAmB,SAAA,YAAoC;IACpCnB,EAAA,CAAAE,MAAA,eAAE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BACjD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAkC,UAAA,KAAAmE,kDAAA,mBAA+D;IAIjErG,EAAA,CAAAG,YAAA,EAAM;IAGFH,EAFJ,CAAAC,cAAA,eAA2B,eACwB,kBAGV;IAAnCD,EAAA,CAAAW,UAAA,mBAAA2F,oEAAA;MAAAtG,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,QAAQ,CAAC;IAAA,EAAC;IAClCxG,EAAA,CAAAmB,SAAA,aAAuC;IAAAnB,EAAA,CAAAE,MAAA,2BACzC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEsC;IAApCD,EAAA,CAAAW,UAAA,mBAAA8F,oEAAA;MAAAzG,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnCxG,EAAA,CAAAmB,SAAA,aAAiC;IAAAnB,EAAA,CAAAE,MAAA,2BACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEsC;IAApCD,EAAA,CAAAW,UAAA,mBAAA+F,oEAAA;MAAA1G,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnCxG,EAAA,CAAAmB,SAAA,aAAwC;IAAAnB,EAAA,CAAAE,MAAA,2BAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGsC;IAApCD,EAAA,CAAAW,UAAA,mBAAAgG,oEAAA;MAAA3G,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnCxG,EAAA,CAAAmB,SAAA,aAAgD;IAAAnB,EAAA,CAAAE,MAAA,2BAClD;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;IAOEH,EAJR,CAAAC,cAAA,eAA8B,eACmC,eAC/B,eACI,uBACyE;IAA1FD,EAAA,CAAAoB,gBAAA,2BAAAwF,iFAAAtF,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA8F,SAAA,EAAAvF,MAAA,MAAAP,MAAA,CAAA8F,SAAA,GAAAvF,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAuB;IAACtB,EAAA,CAAAW,UAAA,2BAAAiG,iFAAA;MAAA5G,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAA+F,iBAAA,EAAmB;IAAA,EAAC;IACxE9G,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EACrB;IACdH,EAAA,CAAAkC,UAAA,KAAA6E,iDAAA,kBAAkE;IAUtE/G,EADE,CAAAG,YAAA,EAAM,EACF;IAKAH,EAHN,CAAAC,cAAA,eAA4B,eACmB,eACX,iBACJ;IACxBD,EAAA,CAAAE,MAAA,IAGF;IAKVF,EALU,CAAAG,YAAA,EAAQ,EACJ,EACF,EACF,EACF,EACF;IAQIH,EALV,CAAAC,cAAA,eAAsC,iBACY,iBACT,UAC/B,cACiC,uBAC0C;IAA9DD,EAAA,CAAAoB,gBAAA,2BAAA4F,iFAAA1F,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA8F,SAAA,EAAAvF,MAAA,MAAAP,MAAA,CAAA8F,SAAA,GAAAvF,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAuB;IAACtB,EAAA,CAAAW,UAAA,2BAAAqG,iFAAA;MAAAhH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAA+F,iBAAA,EAAmB;IAAA,EAAC;IAC5E9G,EAD6E,CAAAG,YAAA,EAAc,EACtF;IACLH,EAAA,CAAAC,cAAA,cAA8D;IAA9CD,EAAA,CAAAW,UAAA,mBAAAsG,gEAAA;MAAAjH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmG,IAAA,CAAK,YAAY,CAAC;IAAA,EAAC;IAC1ClH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAmB,SAAA,aAEoF;IAExFnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,cAAiE;IAAjDD,EAAA,CAAAW,UAAA,mBAAAwG,gEAAA;MAAAnH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmG,IAAA,CAAK,eAAe,CAAC;IAAA,EAAC;IAC7ClH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAmB,SAAA,aAEuF;IAE3FnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,cAAyD;IAA1CD,EAAA,CAAAW,UAAA,mBAAAyG,gEAAA;MAAApH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmG,IAAA,CAAK,QAAQ,CAAC;IAAA,EAAC;IACrClH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAmB,SAAA,aAEgF;IAEpFnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,cAAoE;IAApDD,EAAA,CAAAW,UAAA,mBAAA0G,gEAAA;MAAArH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmG,IAAA,CAAK,kBAAkB,CAAC;IAAA,EAAC;IAChDlH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAmB,SAAA,aAE0F;IAE9FnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,cAAkE;IAAlDD,EAAA,CAAAW,UAAA,mBAAA2G,gEAAA;MAAAtH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmG,IAAA,CAAK,gBAAgB,CAAC;IAAA,EAAC;IAC9ClH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAmB,SAAA,aAEwF;IAE5FnB,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,cAAgB,eACc;IAC1BD,EAAA,CAAAE,MAAA,sBACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,cAAgB,eACc;IAC1BD,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAM,EACH,EACF,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAkC,UAAA,KAAAqF,gDAAA,mBAC2F;IA0DjGvH,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAGNH,EAAA,CAAAkC,UAAA,KAAAsF,iDAAA,oBAAkE;IA2DpExH,EAAA,CAAAG,YAAA,EAAM;;;;IA3PcH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAAoF,cAAA,CAAArD,MAAA,CAA2B;IAET9C,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAOzB9C,EAAA,CAAAM,SAAA,GAA6D;IAC7FN,EADgC,CAAAsE,WAAA,gBAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,cAA6D,wBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,cACxB;IAIrC7B,EAAA,CAAAM,SAAA,GAA8D;IAC9FN,EADgC,CAAAsE,WAAA,gBAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,eAA8D,wBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,eACxB;IAItC7B,EAAA,CAAAM,SAAA,GAA6D;IAC7FN,EADgC,CAAAsE,WAAA,eAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,eAA6D,uBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,eACxB;IAKrE7B,EAAA,CAAAM,SAAA,GAAqE;IACrEN,EADA,CAAAsE,WAAA,uBAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,eAAqE,wBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,eACC;IAc3D7B,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA8F,SAAA,CAAuB;IAGH7G,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAgB5D9C,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAgG,kBAAA,oBAAAjF,MAAA,CAAAoE,WAAA,QAAApE,MAAA,CAAAsB,QAAA,aAAAtB,MAAA,CAAAkF,IAAA,CAAAC,GAAA,CAAAnF,MAAA,CAAAoE,WAAA,GAAApE,MAAA,CAAAsB,QAAA,EAAAtB,MAAA,CAAAoF,cAAA,CAAArD,MAAA,iBAAA/B,MAAA,CAAAoF,cAAA,CAAArD,MAAA,aAGF;IAaa9C,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA8F,SAAA,CAAuB;IAMhC7G,EAAA,CAAAM,SAAA,GAA0E;IAC1EN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA0G,SAAA,qBAAA1G,MAAA,CAAA2G,aAAA,WAA0E,iBAAA3G,MAAA,CAAA0G,SAAA,qBAAA1G,MAAA,CAAA2G,aAAA,YACG;IAO7E1H,EAAA,CAAAM,SAAA,GAA6E;IAC7EN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA0G,SAAA,wBAAA1G,MAAA,CAAA2G,aAAA,WAA6E,iBAAA3G,MAAA,CAAA0G,SAAA,wBAAA1G,MAAA,CAAA2G,aAAA,YACG;IAOhF1H,EAAA,CAAAM,SAAA,GAAsE;IACtEN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA0G,SAAA,iBAAA1G,MAAA,CAAA2G,aAAA,WAAsE,iBAAA3G,MAAA,CAAA0G,SAAA,iBAAA1G,MAAA,CAAA2G,aAAA,YACG;IAOzE1H,EAAA,CAAAM,SAAA,GAAgF;IAChFN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA0G,SAAA,2BAAA1G,MAAA,CAAA2G,aAAA,WAAgF,iBAAA3G,MAAA,CAAA0G,SAAA,2BAAA1G,MAAA,CAAA2G,aAAA,YACG;IAOnF1H,EAAA,CAAAM,SAAA,GAA8E;IAC9EN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA0G,SAAA,yBAAA1G,MAAA,CAAA2G,aAAA,WAA8E,iBAAA3G,MAAA,CAAA0G,SAAA,yBAAA1G,MAAA,CAAA2G,aAAA,YACG;IAgBnE1H,EAAA,CAAAM,SAAA,GAAoB;IAAAN,EAApB,CAAAI,UAAA,YAAAW,MAAA,CAAA4G,eAAA,CAAoB,iBAAA5G,MAAA,CAAA6G,cAAA,CAAuB;IA8D3B5H,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA2E,UAAA,KAAoB;;;;;IAmE5D1F,EAHN,CAAAC,cAAA,eAAoG,cACzF,mBACO,cACY;IACtBD,EAAA,CAAAmB,SAAA,aAA6C;IAC7CnB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IAIrBF,EAJqB,CAAAG,YAAA,EAAI,EACb,EACO,EACP,EACN;;;;;IAQIH,EALV,CAAAC,cAAA,eAA8C,cACnC,mBACO,cACY,eACoB,gBAClB;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAE,MAAA,gEAAY;IAIpCF,EAJoC,CAAAG,YAAA,EAAI,EAC5B,EACO,EACP,EACN;;;;;IASJH,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,2BAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,yBACF;;;;;IACA9C,EAAA,CAAAC,cAAA,WAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,QAAAQ,MAAA,CAAA8G,wBAAA,CAAAC,IAAA,MACF;;;;;IA+BQ9H,EAAA,CAAAC,cAAA,gBACwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAgG,kBAAA,MAAA+B,SAAA,CAAAvD,UAAA,QAAAuD,SAAA,CAAAtD,aAAA,OAAAsD,SAAA,CAAArD,MAAA,QACF;;;;;IACA1E,EAAA,CAAAC,cAAA,eAA4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gBAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,kBACF;;;;;IARF9C,EADF,CAAAC,cAAA,eAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,eAAqC;IAKnCD,EAJA,CAAAkC,UAAA,IAAA8F,gEAAA,oBACwC,IAAAC,gEAAA,oBAGoB;IAIhEjI,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAVAH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAO,kBAAA,sDAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,8BAAyC;IAEnB9C,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAA8B,cAAA,CAAAqF,KAAA,QAAgC;IAIjDlI,EAAA,CAAAM,SAAA,EAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,MAAgC;;;;;;IAiBnC9C,EAAA,CAAAC,cAAA,uBAC+B;IADiBD,EAAA,CAAAoB,gBAAA,2BAAA+G,+HAAA7G,MAAA;MAAA,MAAA8G,SAAA,GAAApI,EAAA,CAAAa,aAAA,CAAAwH,IAAA,EAAA3E,SAAA;MAAA1D,EAAA,CAAAwB,kBAAA,CAAA4G,SAAA,CAAAzE,QAAA,EAAArC,MAAA,MAAA8G,SAAA,CAAAzE,QAAA,GAAArC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAA4B;IAE1EtB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;;;;IAHkCH,EAAA,CAAAyC,gBAAA,YAAA2F,SAAA,CAAAzE,QAAA,CAA4B;IAC1E3D,EAAA,CAAAI,UAAA,cAAAgI,SAAA,CAAA7D,QAAA,CAA4B;IAC5BvE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAsI,kBAAA,MAAAF,SAAA,CAAA5D,UAAA,QAAA4D,SAAA,CAAA3D,aAAA,OACF;;;;;IAJFzE,EAAA,CAAAC,cAAA,eAAyD;IACvDD,EAAA,CAAAkC,UAAA,IAAAqG,yFAAA,2BAC+B;IAGjCvI,EAAA,CAAAG,YAAA,EAAM;;;;IAJ2BH,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAI,UAAA,YAAAoI,SAAA,CAAAC,MAAA,CAAe;;;;;;IAJhDzI,EADF,CAAAC,cAAA,eAAmF,sBACS;IAA7ED,EAAA,CAAAoB,gBAAA,2BAAAsH,2GAAApH,MAAA;MAAA,MAAAkH,SAAA,GAAAxI,EAAA,CAAAa,aAAA,CAAA8H,IAAA,EAAAjF,SAAA;MAAA1D,EAAA,CAAAwB,kBAAA,CAAAgH,SAAA,CAAA7E,QAAA,EAAArC,MAAA,MAAAkH,SAAA,CAAA7E,QAAA,GAAArC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAA4B;IAACtB,EAAA,CAAAW,UAAA,2BAAA+H,2GAAA;MAAA,MAAAF,SAAA,GAAAxI,EAAA,CAAAa,aAAA,CAAA8H,IAAA,EAAAjF,SAAA;MAAA,MAAA3C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAA6H,sBAAA,CAAAJ,SAAA,CAA6B;IAAA,EAAC;IACvFxI,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAkC,UAAA,IAAA2G,2EAAA,mBAAyD;IAM3D7I,EAAA,CAAAG,YAAA,EAAM;;;;IATSH,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAAyC,gBAAA,YAAA+F,SAAA,CAAA7E,QAAA,CAA4B;IACvC3D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAsI,kBAAA,MAAAE,SAAA,CAAAM,WAAA,SAAAN,SAAA,CAAAC,MAAA,CAAA3F,MAAA,cACF;IACmC9C,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAoI,SAAA,CAAA7E,QAAA,CAAoB;;;;;IAL3D3D,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAkC,UAAA,IAAA6G,qEAAA,mBAAmF;IAWrF/I,EAAA,CAAAG,YAAA,EAAM;;;;IAX2CH,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAA8G,wBAAA,CAAAmB,MAAA,CAAkC;;;;;;IAJnFhJ,EADF,CAAAC,cAAA,UAAyC,sBACa;IAAvCD,EAAA,CAAAoB,gBAAA,2BAAA6H,+FAAA3H,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAqI,IAAA;MAAA,MAAAnI,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAoI,aAAA,CAAAC,UAAA,EAAA9H,MAAA,MAAAP,MAAA,CAAAoI,aAAA,CAAAC,UAAA,GAAA9H,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAsC;IACjDtB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAkC,UAAA,IAAAmH,+DAAA,mBAAgF;IAalFrJ,EAAA,CAAAG,YAAA,EAAM;;;;IAhBSH,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAoI,aAAA,CAAAC,UAAA,CAAsC;IACjDpJ,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gCAAAQ,MAAA,CAAAuI,eAAA,CAAAxG,MAAA,cACF;IACmB9C,EAAA,CAAAM,SAAA,EAA2D;IAA3DN,EAAA,CAAAI,UAAA,UAAAW,MAAA,CAAAoI,aAAA,CAAAC,UAAA,IAAArI,MAAA,CAAA8G,wBAAA,CAA2D;;;;;IAqBlF7H,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAxCH,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAO,kBAAA,MAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,yBAAiC;;;;;;IA1E7E9C,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,iCACA;IAGAF,EAHA,CAAAkC,UAAA,IAAAqH,yDAAA,oBAA6D,IAAAC,yDAAA,oBAGS;IAGxExJ,EAAA,CAAAG,YAAA,EAAiB;IAIbH,EAHJ,CAAAC,cAAA,mBAAc,eAEY,YACf;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAEvDH,EADF,CAAAC,cAAA,gBAAuC,0BACJ;IAC/BD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBACwC;IAAtCD,EAAA,CAAAoB,gBAAA,2BAAAqI,mFAAAnI,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA6I,IAAA;MAAA,MAAA3I,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAoI,aAAA,CAAAQ,SAAA,EAAArI,MAAA,MAAAP,MAAA,CAAAoI,aAAA,CAAAQ,SAAA,GAAArI,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAqC;IADvCtB,EAAA,CAAAG,YAAA,EACwC;IACxCH,EAAA,CAAAmB,SAAA,4BAAmE;IACrEnB,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,iBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,0BAAiC;IAC/BD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBACsC;IAApCD,EAAA,CAAAoB,gBAAA,2BAAAwI,mFAAAtI,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA6I,IAAA;MAAA,MAAA3I,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAoI,aAAA,CAAAU,OAAA,EAAAvI,MAAA,MAAAP,MAAA,CAAAoI,aAAA,CAAAU,OAAA,GAAAvI,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAmC;IADrCtB,EAAA,CAAAG,YAAA,EACsC;IACtCH,EAAA,CAAAmB,SAAA,4BAAiE;IAGvEnB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAwB,aACf;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnBH,EAAA,CAAAC,cAAA,gBAA+B;IAgB7BD,EAdA,CAAAkC,UAAA,KAAA4H,yDAAA,mBAAgE,KAAAC,yDAAA,mBAcvB;IAoB/C/J,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAmD,mBACa;IAAvBD,EAAA,CAAAW,UAAA,mBAAAqJ,4EAAA;MAAA,MAAAC,OAAA,GAAAjK,EAAA,CAAAa,aAAA,CAAA6I,IAAA,EAAAQ,SAAA;MAAA,MAAAnJ,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAoJ,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAACjK,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzEH,EAAA,CAAAC,cAAA,mBAA6D;IAA7BD,EAAA,CAAAW,UAAA,mBAAAyJ,4EAAA;MAAA,MAAAH,OAAA,GAAAjK,EAAA,CAAAa,aAAA,CAAA6I,IAAA,EAAAQ,SAAA;MAAA,MAAAnJ,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsJ,aAAA,CAAAJ,OAAA,CAAkB;IAAA,EAAC;IAC1DjK,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAkC,UAAA,KAAAoI,0DAAA,oBAAwC;IAG9CtK,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;;;IA3ECH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAG/B9C,EAAA,CAAAM,SAAA,EAA6D;IAA7DN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8G,wBAAA,IAAA9G,MAAA,CAAA8B,cAAA,CAAAC,MAAA,OAA6D;IAWf9C,EAAA,CAAAM,SAAA,IAA+B;IAA/BN,EAAA,CAAAI,UAAA,iBAAAmK,kBAAA,CAA+B;IAC5EvK,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAoI,aAAA,CAAAQ,SAAA,CAAqC;IAMQ3J,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,iBAAAoK,gBAAA,CAA6B;IAC1ExK,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAoI,aAAA,CAAAU,OAAA,CAAmC;IAWjC7J,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAc/B9C,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,OAAiC;IAyBlC9C,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;;;;;IAO5C9C,EAAA,CAAAC,cAAA,mBAA+C;IAM7CD,EALA,CAAAmB,SAAA,qBACiB,mBAGF,0BAGE;IACnBnB,EAAA,CAAAG,YAAA,EAAU;;;;;;IAKRH,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GACE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mDAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,wBAA2B,eACyB,iBACJ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC7E;IACRH,EAAA,CAAAC,cAAA,0BAA4B;IAC1BD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBAC8E;IAAvDD,EAAA,CAAAoB,gBAAA,2BAAAqJ,mFAAAnJ,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA6J,IAAA;MAAA,MAAA3J,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA4J,uBAAA,CAAArH,gBAAA,EAAAhC,MAAA,MAAAP,MAAA,CAAA4J,uBAAA,CAAArH,gBAAA,GAAAhC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAsD;IAD7EtB,EAAA,CAAAG,YAAA,EAC8E;IAC9EH,EAAA,CAAAmB,SAAA,4BAAoE;IACtEnB,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,kBAAkD;IAACD,EAAA,CAAAE,MAAA,WACnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBAC4E;IAArDD,EAAA,CAAAoB,gBAAA,2BAAAwJ,mFAAAtJ,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA6J,IAAA;MAAA,MAAA3J,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA4J,uBAAA,CAAApH,cAAA,EAAAjC,MAAA,MAAAP,MAAA,CAAA4J,uBAAA,CAAApH,cAAA,GAAAjC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAoD;IAD3EtB,EAAA,CAAAG,YAAA,EAC4E;IAC5EH,EAAA,CAAAmB,SAAA,4BAAkE;IAGxEnB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAsD,mBACiB;IAAvBD,EAAA,CAAAW,UAAA,mBAAAkK,4EAAA;MAAA,MAAAC,OAAA,GAAA9K,EAAA,CAAAa,aAAA,CAAA6J,IAAA,EAAAR,SAAA;MAAA,MAAAnJ,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAoJ,OAAA,CAAAW,OAAA,CAAY;IAAA,EAAC;IAAC9K,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,mBAAoE;IAAxBD,EAAA,CAAAW,UAAA,mBAAAoK,4EAAA;MAAA,MAAAD,OAAA,GAAA9K,EAAA,CAAAa,aAAA,CAAA6J,IAAA,EAAAR,SAAA;MAAA,MAAAnJ,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAiK,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IAAC9K,EAAA,CAAAE,MAAA,oBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAChE,EACT;;;;;;IA3B6BH,EAAA,CAAAM,SAAA,GACE;IADFN,EAAA,CAAAsI,kBAAA,KAAAvH,MAAA,CAAA4J,uBAAA,CAAAnG,UAAA,SAAAzD,MAAA,CAAA4J,uBAAA,CAAAjG,MAAA,MACE;IAQoC1E,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,iBAAA6K,mBAAA,CAAgC;IAC9EjL,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA4J,uBAAA,CAAArH,gBAAA,CAAsD;IAOVtD,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAI,UAAA,iBAAA8K,iBAAA,CAA8B;IAC1ElL,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA4J,uBAAA,CAAApH,cAAA,CAAoD;;;ADrcrF,OAAM,MAAO4H,0BAA2B,SAAQrL,aAAa;EAK3DsL,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA6C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAUtD;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAzJ,eAAe,GAAa,EAAE;IAE9B;IACA,KAAAjB,aAAa,GAAkB;MAC7BC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfoK,cAAc,EAAE;KACjB;IAED;IACA,KAAAjD,aAAa,GAAkB;MAC7BQ,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbT,UAAU,EAAE,IAAI;MAChBiD,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBzJ,cAAc,EAAE;KACjB;IAED,KAAAgF,wBAAwB,GAAyB,IAAI;IAErD;IACA,KAAAyB,eAAe,GAAqB,EAAE;IACtC,KAAAnD,cAAc,GAAqB,EAAE;IACrC,KAAAwB,eAAe,GAAqB,EAAE;IACtC,KAAA9E,cAAc,GAAqB,EAAE;IACrC,KAAAgE,SAAS,GAAY,KAAK;IAC1B,KAAA0F,OAAO,GAAY,KAAK;IAExB;IACA,KAAApH,WAAW,GAAW,CAAC;IACd,KAAA9C,QAAQ,GAAW,EAAE;IAC9B,KAAAqD,UAAU,GAAW,CAAC;IAEtB;IACA,KAAA+B,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAmB,KAAK;IAErC;IACA,KAAAzB,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAL,UAAU,GAAW,CAAC;IArFpB,IAAI,CAAC+E,uBAAuB,GAAG;MAC7BrH,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBmB,MAAM,EAAE8H,SAAS;MACjBhI,UAAU,EAAE,EAAE;MACdD,QAAQ,EAAEiI;KACX;IAED,IAAI,CAACZ,aAAa,CAACa,OAAO,EAAE,CAACC,IAAI,CAC/BjN,GAAG,CAAEkN,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAACf,eAAe,GAAGc,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAwESC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAACpB,gBAAgB,CAAC,CAAC,CAAC;MAC/CxI,gBAAgB,EAAEkJ,SAAS;MAC3BjJ,cAAc,EAAEiJ;KACjB;IACD,IAAI,CAACW,gBAAgB,EAAE;EACzB;EAEA;EACAxK,gBAAgBA,CAAA;IACd,OAAO,CAAC,EAAE,IAAI,CAAClB,aAAa,CAACC,aAAa,IACxC,IAAI,CAACD,aAAa,CAACI,YAAY,IAC/B,IAAI,CAACJ,aAAa,CAACO,WAAW,CAAC;EACnC;EAEAoL,qBAAqBA,CAAA;IACnB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAC5L,aAAa,CAACC,aAAa,EAAE2L,KAAK,EAAE;IAC7C,IAAI,IAAI,CAAC5L,aAAa,CAACI,YAAY,EAAEwL,KAAK,EAAE;IAC5C,IAAI,IAAI,CAAC5L,aAAa,CAACO,WAAW,EAAEqL,KAAK,EAAE;IAC3C,OAAOA,KAAK;EACd;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACN,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvDC,qBAAqB,EAAE,IAAI,CAACpB,gBAAgB,CAAC,CAAC,CAAC;MAC/CxI,gBAAgB,EAAEkJ,SAAS;MAC3BjJ,cAAc,EAAEiJ;KACjB;IACD,IAAI,CAACL,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACjL,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACO,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfoK,cAAc,EAAE;KACjB;IACD,IAAI,CAACzK,QAAQ,EAAE;EACjB;EAEA6E,cAAcA,CAAC+G,MAAc;IAC3B,IAAI,IAAI,CAAC9L,aAAa,CAACI,YAAY,KAAK0L,MAAM,EAAE;MAC9C,IAAI,CAAC9L,aAAa,CAACI,YAAY,GAAG,EAAE;IACtC,CAAC,MAAM;MACL,IAAI,CAACJ,aAAa,CAACI,YAAY,GAAG0L,MAAM;IAC1C;IACA,IAAI,CAAC5L,QAAQ,EAAE;EACjB;EAEAwB,cAAcA,CAAA;IACZ,IAAI,CAACN,cAAc,GAAG,EAAE;IACxB,IAAI,CAACgE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACyC,eAAe,CAACkE,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC9J,QAAQ,GAAG,KAAK,CAAC;EAC/D;EAEAkB,aAAaA,CAAC4I,KAAqB;IACjC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IACzC,QAAQF,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,oBAAoB;MAC1C,KAAK,SAAS;QAAE,OAAO,cAAc;MACrC,KAAK,SAAS;QAAE,OAAO,qBAAqB;MAC5C,KAAK,SAAS;QAAE,OAAO,6BAA6B;MACpD,KAAK,UAAU;QAAE,OAAO,YAAY;MACpC;QAAS,OAAO,6BAA6B;IAC/C;EACF;EAEAzH,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACF,UAAU,IAAI,IAAI,CAACA,UAAU,IAAI,CAAC,IAAI,IAAI,CAACA,UAAU,IAAI,IAAI,CAACF,UAAU,EAAE;MACjF,IAAI,CAACR,QAAQ,CAAC,IAAI,CAACU,UAAU,CAAC;IAChC;EACF;EAIAvB,SAASA,CAACsJ,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACrJ,QAAQ,EAAE;MACjB,IAAI,CAACoG,uBAAuB,GAAG;QAC7B,GAAGiD,IAAI;QACPtK,gBAAgB,EAAEsK,IAAI,CAACtK,gBAAgB,GAAG,IAAIuK,IAAI,CAACD,IAAI,CAACtK,gBAAgB,CAAC,GAAGkJ,SAAS;QACrFjJ,cAAc,EAAEqK,IAAI,CAACrK,cAAc,GAAG,IAAIsK,IAAI,CAACD,IAAI,CAACrK,cAAc,CAAC,GAAGiJ;OACvE;MACD,IAAI,CAAClB,aAAa,CAACwC,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOpO,MAAM,CAACoO,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAjD,QAAQA,CAAC2C,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC1C,KAAK,CAAC2C,aAAa,CAACrL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACyI,OAAO,CAAC6C,aAAa,CAAC,IAAI,CAAC5C,KAAK,CAAC2C,aAAa,CAAC;MACpD;IACF;IACA,MAAME,KAAK,GAAG;MACZ9J,QAAQ,EAAE,IAAI,CAACoG,uBAAuB,CAACpG,QAAQ;MAC/CjB,gBAAgB,EAAE,IAAI,CAACyK,UAAU,CAAC,IAAI,CAACpD,uBAAuB,CAACrH,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAACpD,uBAAuB,CAACpH,cAAc;KAC5E;IAED,IAAI,CAACkI,aAAa,CAAC6C,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAACvB,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC6B,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACjD,OAAO,CAACkD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAxB,gBAAgBA,CAAA;IACd,IAAI,CAACzB,iBAAiB,CAACkD,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAAC7B,IAAI,CAC7EjN,GAAG,CAACkN,GAAG,IAAG;MACR,MAAMkC,OAAO,GAAGlC,GAAG,CAACmC,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAAC/L,MAAM,IAAI6J,GAAG,CAAC6B,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChDzO,cAAc,EAAEyO,KAAK,CAACzO,cAAc;UACpC0O,GAAG,EAAED,KAAK,CAACC;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAACrD,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAIsD,KAAK,GAAG,IAAI,CAACJ,oBAAoB,CAACK,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAACrD,eAAe,CAAC;UAC1F,IAAI,CAACmB,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC8B,oBAAoB,CAACI,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAACnC,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC8B,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMO,WAAW,GAAG,IAAI,CAACtC,WAAW,EAAEC,kBAAkB,EAAEiC,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACZ,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAAC5B,SAAS,EAAE;EACf;EAEAyC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAAChC,OAAO,CAACkC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACnC,OAAO,CAAEC,KAAU,IAAI;QACvC,MAAMmC,KAAK,GAAGnC,KAAK,CAAC/I,MAAM;QAC1B,IAAI,CAAC+K,SAAS,CAACG,KAAK,CAAC,EAAE;UAAE;UACvBH,SAAS,CAACG,KAAK,CAAC,GAAG,EAAE;QACvB;QACAH,SAAS,CAACG,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBrL,UAAU,EAAEkL,SAAS,CAAClL,UAAU;UAChCC,aAAa,EAAEgJ,KAAK,CAAChJ,aAAa,IAAI,KAAK;UAC3CF,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ;UACxBG,MAAM,EAAE+I,KAAK,CAAC/I,MAAM;UACpBpB,gBAAgB,EAAEmK,KAAK,CAACnK,gBAAgB;UACxCC,cAAc,EAAEkK,KAAK,CAAClK;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACyF,MAAM,CAAC9B,IAAI,CAAC,CAAC4I,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAAChH,MAAM,CAACgG,GAAG,CAAEY,KAAU,IAAI;MAChE,OAAO,IAAI,CAACK,UAAU,CAACjB,GAAG,CAAEU,SAAc,IAAI;QAC5C,MAAMjC,KAAK,GAAGgC,SAAS,CAACG,KAAK,CAAC,CAACM,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAAC3L,UAAU,KAAKkL,SAAS,CAAC;QAC5F,OAAOjC,KAAK,IAAI;UACdjJ,UAAU,EAAEkL,SAAS;UACrBjL,aAAa,EAAE,KAAK;UACpBF,QAAQ,EAAE,IAAI;UACdG,MAAM,EAAEkL,KAAK;UACbtM,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOyM,MAAM;EACf;EAEAI,sBAAsBA,CAACZ,GAAU;IAC/B,MAAMa,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5Cd,GAAG,CAAChC,OAAO,CAACkC,SAAS,IAAG;MACtBa,aAAa,CAACC,GAAG,CAACd,SAAS,CAAClL,UAAU,CAAC;MACvCkL,SAAS,CAACC,OAAO,CAACnC,OAAO,CAAEC,KAAU,IAAI;QACvC4C,SAAS,CAACG,GAAG,CAAC/C,KAAK,CAAC/I,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACsE,MAAM,GAAGyH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLvH,MAAM,EAAEyH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC3D,WAAW,CAAC1J,gBAAgB,IAAI,IAAI,CAAC0J,WAAW,CAACzJ,cAAc,EAAE;MACxE,MAAMoG,SAAS,GAAG,IAAIkE,IAAI,CAAC,IAAI,CAACb,WAAW,CAAC1J,gBAAgB,CAAC;MAC7D,MAAMuG,OAAO,GAAG,IAAIgE,IAAI,CAAC,IAAI,CAACb,WAAW,CAACzJ,cAAc,CAAC;MACzD,IAAIoG,SAAS,IAAIE,OAAO,IAAIF,SAAS,GAAGE,OAAO,EAAE;QAC/C,IAAI,CAAC0B,OAAO,CAAC6C,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEA;EACAwC,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACC,cAAc,EAAE;IACrB;IACA,IAAI,CAACnC,kBAAkB,EAAE;EAC3B;EAEA;EACAmC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC9E,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC3C,eAAe,GAAG,EAAE;IACzB,IAAI,CAACnD,cAAc,GAAG,EAAE;IACxB,IAAI,CAACwB,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC9E,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAACpB,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfoK,cAAc,EAAE;KACjB;IAED;IACA,IAAI,CAACvF,SAAS,GAAG,KAAK;IACtB,IAAI,CAACsF,gBAAgB,GAAG,EAAE;IAE1B;IACA,IAAI,CAAChH,WAAW,GAAG,CAAC;IACpB,IAAI,CAACO,UAAU,GAAG,CAAC;IAEnB;IACA,IAAI,CAACwG,eAAe,GAAG,EAAE;IACzB,IAAI,CAACxJ,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,CAAC+E,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC5B;EAEAgH,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC,IAAI,CAAC1B,WAAW,CAACC,kBAAkB,EAAEiC,GAAG,EAAE;MAC7C,IAAI,CAAC3C,OAAO,GAAG,KAAK;MACpB;IACF;IAEA,IAAI,CAACA,OAAO,GAAG,IAAI;IACnB,IAAI,CAACoE,cAAc,EAAE;IACrB,IAAI,CAAClF,aAAa,CAACuF,mCAAmC,CAAC;MACrDzC,IAAI,EAAE;QACJ0C,YAAY,EAAE,IAAI,CAACjE,WAAW,CAACC,kBAAkB,CAACiC,GAAG;QACrD5L,gBAAgB,EAAE,IAAI,CAAC0J,WAAW,CAAC1J,gBAAgB,GAAG,IAAI,CAACyK,UAAU,CAAC,IAAI,CAACf,WAAW,CAAC1J,gBAAgB,CAAC,GAAGkJ,SAAS;QACpHjJ,cAAc,EAAE,IAAI,CAACyJ,WAAW,CAACzJ,cAAc,GAAG,IAAI,CAACwK,UAAU,CAAC,IAAI,CAACf,WAAW,CAACzJ,cAAc,CAAC,GAAGiJ;;KAExG,CAAC,CAACM,SAAS,CAACH,GAAG,IAAG;MACjB,IAAI,CAACJ,OAAO,GAAG,KAAK;MACpB,IAAII,GAAG,CAACmC,OAAO,IAAInC,GAAG,CAAC6B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACsC,gBAAgB,GAAGnE,GAAG,CAACmC,OAAO,GAAGnC,GAAG,CAACmC,OAAO,GAAG,EAAE;QACtD,IAAInC,GAAG,CAACmC,OAAO,EAAE;UACf,IAAI,CAACgC,gBAAgB,GAAG,CAAC,GAAGnE,GAAG,CAACmC,OAAO,CAAC;UACxC,IAAI,CAACsB,sBAAsB,CAACzD,GAAG,CAACmC,OAAO,CAAC;UACxC,IAAI,CAACiC,mBAAmB,GAAG,IAAI,CAACxB,8BAA8B,CAAC5C,GAAG,CAACmC,OAAO,CAAC;UAC3E;UACA,IAAI,CAACoC,mBAAmB,CAACvE,GAAG,CAACmC,OAAO,CAAC;UACrC;UACA,IAAI,CAACqC,oBAAoB,CAACxE,GAAG,CAACmC,OAAO,CAAC;QACxC;MACF;IACF,CAAC,CAAC;EACJ;EAEA;EACAoC,mBAAmBA,CAACE,IAA6B;IAC/C,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAyC;IAEpEF,IAAI,CAAC5D,OAAO,CAACkC,SAAS,IAAG;MACvB,MAAM6B,SAAS,GAAG7B,SAAS,CAAClL,UAAU,IAAI,EAAE,CAAC,CAAC;MAE9CkL,SAAS,CAACC,OAAO,EAAEnC,OAAO,CAACC,KAAK,IAAG;QACjC,MAAM+D,YAAY,GAAG/D,KAAK,CAAChJ,aAAa,IAAI,KAAK,CAAC,CAAC;QACnD,MAAMmL,KAAK,GAAGnC,KAAK,CAAC/I,MAAM,IAAI,CAAC;QAE/B,IAAI,CAAC2M,WAAW,CAACI,GAAG,CAACD,YAAY,CAAC,EAAE;UAClCH,WAAW,CAACK,GAAG,CAACF,YAAY,EAAE,IAAIF,GAAG,EAA4B,CAAC;QACpE;QAEA,MAAMK,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACJ,YAAY,CAAE;QAE/C,IAAI,CAACG,QAAQ,CAACF,GAAG,CAAC7B,KAAK,CAAC,EAAE;UACxB+B,QAAQ,CAACD,GAAG,CAAC9B,KAAK,EAAE,EAAE,CAAC;QACzB;QAEA+B,QAAQ,CAACC,GAAG,CAAChC,KAAK,CAAE,CAACC,IAAI,CAAC;UACxBrL,UAAU,EAAE+M,SAAS;UAAE;UACvB9M,aAAa,EAAE+M,YAAY;UAAE;UAC7BjN,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ,IAAI,CAAC;UAC7BG,MAAM,EAAEkL,KAAK;UACbtM,gBAAgB,EAAEmK,KAAK,CAACnK,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEkK,KAAK,CAAClK,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACsI,cAAc,GAAGwE,KAAK,CAACC,IAAI,CAACW,WAAW,CAACxC,OAAO,EAAE,CAAC,CAACG,GAAG,CAAC,CAAC,CAACwC,YAAY,EAAEG,QAAQ,CAAC,KAAI;MACvF,MAAM3I,MAAM,GAAiByH,KAAK,CAACC,IAAI,CAACiB,QAAQ,CAAC9C,OAAO,EAAE,CAAC,CACxD3H,IAAI,CAAC,CAAC,CAAC4I,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;MAAA,CAC1Bd,GAAG,CAAC,CAAC,CAAClG,WAAW,EAAEL,MAAM,CAAC,MAAM;QAC/BK,WAAW;QACXL,MAAM,EAAEA,MAAM,CAACvB,IAAI,CAAC,CAAC4I,CAAC,EAAEC,CAAC,KAAI;UAC3B;UACA,IAAID,CAAC,CAACtL,UAAU,KAAKuL,CAAC,CAACvL,UAAU,EAAE;YACjC,OAAOsL,CAAC,CAACtL,UAAU,CAACqN,aAAa,CAAC9B,CAAC,CAACvL,UAAU,CAAC;UACjD;UACA,OAAOsL,CAAC,CAACpL,MAAM,GAAGqL,CAAC,CAACrL,MAAM;QAC5B,CAAC,CAAC;QACFf,QAAQ,EAAE;OACX,CAAC,CAAC;MAEL,OAAO;QACLmE,IAAI,EAAE0J,YAAY;QAClBxI,MAAM;QACNrF,QAAQ,EAAE;OACX;IACH,CAAC,CAAC,CAACuD,IAAI,CAAC,CAAC4I,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChI,IAAI,CAAC+J,aAAa,CAAC9B,CAAC,CAACjI,IAAI,CAAC,CAAC;IAE/C;IACA,IAAI,CAACoE,eAAe,GAAG,IAAI,CAACD,cAAc,CAAC+C,GAAG,CAAC8C,EAAE,IAAIA,EAAE,CAAChK,IAAI,CAAC;IAC7D,IAAI,CAACiK,qBAAqB,EAAE;EAC9B;EAEA;EACAA,qBAAqBA,CAAA;IACnB,MAAM1B,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,IAAI,CAACrE,cAAc,CAACuB,OAAO,CAACwE,QAAQ,IAAG;MACrC,IAAI,CAAC,IAAI,CAAC7F,gBAAgB,IAAI6F,QAAQ,CAAClK,IAAI,KAAK,IAAI,CAACqE,gBAAgB,EAAE;QACrE6F,QAAQ,CAAChJ,MAAM,CAACwE,OAAO,CAACoC,KAAK,IAAG;UAC9BS,SAAS,CAACG,GAAG,CAACZ,KAAK,CAAC9G,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACpG,eAAe,GAAG+N,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC,CAACnJ,IAAI,CAAC,CAAC4I,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpE;EAEA;EACAmC,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACpP,cAAc,CAAC2K,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC9J,QAAQ,GAAG,KAAK,CAAC;IAC5D,IAAI,CAACd,cAAc,GAAG,EAAE;IACxB,IAAI,CAACgE,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAAC1B,WAAW,GAAG,CAAC;IAEpB;IACA,IAAI,CAAC1D,aAAa,CAACO,WAAW,GAAG,EAAE;IAEnC;IACA,IAAI,CAAC+P,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACtQ,aAAa,CAAC2K,cAAc,GAAG,IAAI,CAACD,gBAAgB;IACzD,IAAI,CAACxK,QAAQ,EAAE;EACjB;EAIA;EACAuQ,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACjG,cAAc,CAACkG,MAAM,CAACH,QAAQ,IAAG;MAC3C;MACA,IAAI,IAAI,CAAC7F,gBAAgB,IAAI6F,QAAQ,CAAClK,IAAI,KAAK,IAAI,CAACqE,gBAAgB,EAAE;QACpE,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAAC1K,aAAa,CAACC,aAAa,EAAE;QACpC,MAAM0Q,OAAO,GAAG,IAAI,CAAC3Q,aAAa,CAACC,aAAa,CAAC2Q,WAAW,EAAE;QAC9D,MAAMC,gBAAgB,GAAGN,QAAQ,CAAChJ,MAAM,CAACuJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAACnH,MAAM,CAAC8J,IAAI,CAAC9E,KAAK,IACrBA,KAAK,CAACjJ,UAAU,CAAC6N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IAChD3E,KAAK,CAAChJ,aAAa,CAAC4N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,CACpD,CACF;QACD,IAAI,CAACE,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC7Q,aAAa,CAACI,YAAY,EAAE;QACnC,MAAM4Q,iBAAiB,GAAGT,QAAQ,CAAChJ,MAAM,CAACuJ,IAAI,CAAC3C,KAAK,IAClDA,KAAK,CAACnH,MAAM,CAAC8J,IAAI,CAAC9E,KAAK,IAAI,IAAI,CAACiF,mBAAmB,CAACjF,KAAK,CAAC,CAAC,CAC5D;QACD,IAAI,CAACgF,iBAAiB,EAAE;UACtB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAChR,aAAa,CAACO,WAAW,EAAE;QAClC,MAAM8G,WAAW,GAAG6J,QAAQ,CAAC,IAAI,CAAClR,aAAa,CAACO,WAAW,CAAC;QAC5D,MAAM4Q,gBAAgB,GAAGZ,QAAQ,CAAChJ,MAAM,CAACuJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAAC9G,WAAW,KAAKA,WAAW,CAClC;QACD,IAAI,CAAC8J,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CAAC5D,GAAG,CAACgD,QAAQ,IAAG;MAChB;MACA,MAAMa,gBAAgB,GAAG;QAAE,GAAGb;MAAQ,CAAE;MACxCa,gBAAgB,CAAC7J,MAAM,GAAGgJ,QAAQ,CAAChJ,MAAM,CAACmJ,MAAM,CAACvC,KAAK,IAAG;QACvD;QACA,IAAI,IAAI,CAACnO,aAAa,CAACO,WAAW,EAAE;UAClC,MAAM8G,WAAW,GAAG6J,QAAQ,CAAC,IAAI,CAAClR,aAAa,CAACO,WAAW,CAAC;UAC5D,IAAI4N,KAAK,CAAC9G,WAAW,KAAKA,WAAW,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QAEA;QACA,MAAMgK,cAAc,GAAGlD,KAAK,CAACnH,MAAM,CAAC8J,IAAI,CAAC9E,KAAK,IAAG;UAC/C;UACA,IAAI,IAAI,CAAChM,aAAa,CAACC,aAAa,EAAE;YACpC,MAAM0Q,OAAO,GAAG,IAAI,CAAC3Q,aAAa,CAACC,aAAa,CAAC2Q,WAAW,EAAE;YAC9D,IAAI,CAAC5E,KAAK,CAACjJ,UAAU,CAAC6N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAAC3E,KAAK,CAAChJ,aAAa,CAAC4N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAC3Q,aAAa,CAACI,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC6Q,mBAAmB,CAACjF,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOqF,cAAc;MACvB,CAAC,CAAC,CAAC9D,GAAG,CAACY,KAAK,IAAG;QACb;QACA,MAAMmD,aAAa,GAAG;UAAE,GAAGnD;QAAK,CAAE;QAClCmD,aAAa,CAACtK,MAAM,GAAGmH,KAAK,CAACnH,MAAM,CAAC0J,MAAM,CAAC1E,KAAK,IAAG;UACjD;UACA,IAAI,IAAI,CAAChM,aAAa,CAACC,aAAa,EAAE;YACpC,MAAM0Q,OAAO,GAAG,IAAI,CAAC3Q,aAAa,CAACC,aAAa,CAAC2Q,WAAW,EAAE;YAC9D,IAAI,CAAC5E,KAAK,CAACjJ,UAAU,CAAC6N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAAC3E,KAAK,CAAChJ,aAAa,CAAC4N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAC3Q,aAAa,CAACI,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC6Q,mBAAmB,CAACjF,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOsF,aAAa;MACtB,CAAC,CAAC;MAEF,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ;EAEA;EACQH,mBAAmBA,CAACjF,KAAqB;IAC/C,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IAEzC,QAAQ,IAAI,CAAChM,aAAa,CAACI,YAAY;MACrC,KAAK,QAAQ;QACX,OAAO0L,MAAM,KAAK,QAAQ;MAC5B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,UAAU;MAC9B;QACE,OAAO,IAAI;MAAE;IACjB;EACF;EAEA;EACQG,cAAcA,CAACD,KAAqB;IAC1C,IAAI,CAACA,KAAK,CAAClJ,QAAQ,EAAE;MACnB,OAAO,UAAU;IACnB;IAEA;IACA,IAAI,CAACkJ,KAAK,CAACnK,gBAAgB,IAAI,CAACmK,KAAK,CAAClK,cAAc,IAClDkK,KAAK,CAACnK,gBAAgB,KAAK,EAAE,IAAImK,KAAK,CAAClK,cAAc,KAAK,EAAE,EAAE;MAC9D,OAAO,SAAS;IAClB;IAEA,IAAI;MACF;MACA,MAAMyP,GAAG,GAAG,IAAInF,IAAI,EAAE;MACtB,MAAMoF,KAAK,GAAG,IAAIpF,IAAI,CAACmF,GAAG,CAACE,WAAW,EAAE,EAAEF,GAAG,CAACG,QAAQ,EAAE,EAAEH,GAAG,CAACI,OAAO,EAAE,CAAC;MAExE;MACA,IAAIzJ,SAAe;MACnB,IAAI8D,KAAK,CAACnK,gBAAgB,CAACkP,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxC7I,SAAS,GAAG,IAAIkE,IAAI,CAACJ,KAAK,CAACnK,gBAAgB,CAAC;MAC9C,CAAC,MAAM;QACLqG,SAAS,GAAG,IAAIkE,IAAI,CAACJ,KAAK,CAACnK,gBAAgB,GAAG,WAAW,CAAC;MAC5D;MAEA;MACA,IAAIuG,OAAa;MACjB,IAAI4D,KAAK,CAAClK,cAAc,CAACiP,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtC3I,OAAO,GAAG,IAAIgE,IAAI,CAACJ,KAAK,CAAClK,cAAc,CAAC;MAC1C,CAAC,MAAM;QACLsG,OAAO,GAAG,IAAIgE,IAAI,CAACJ,KAAK,CAAClK,cAAc,GAAG,WAAW,CAAC;MACxD;MAEA;MACA,IAAI8P,KAAK,CAAC1J,SAAS,CAAC2J,OAAO,EAAE,CAAC,IAAID,KAAK,CAACxJ,OAAO,CAACyJ,OAAO,EAAE,CAAC,EAAE;QAC1DC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAE;UACnCC,KAAK,EAAEhG,KAAK,CAACnK,gBAAgB;UAC7BoQ,GAAG,EAAEjG,KAAK,CAAClK,cAAc;UACzBoQ,OAAO,EAAElG,KAAK,CAAClJ;SAChB,CAAC;QACF,OAAO,SAAS;MAClB;MAEA;MACA,MAAMqP,aAAa,GAAG,IAAI/F,IAAI,CAAClE,SAAS,CAACuJ,WAAW,EAAE,EAAEvJ,SAAS,CAACwJ,QAAQ,EAAE,EAAExJ,SAAS,CAACyJ,OAAO,EAAE,CAAC;MAClG,MAAMS,WAAW,GAAG,IAAIhG,IAAI,CAAChE,OAAO,CAACqJ,WAAW,EAAE,EAAErJ,OAAO,CAACsJ,QAAQ,EAAE,EAAEtJ,OAAO,CAACuJ,OAAO,EAAE,CAAC;MAE1F;MACA,IAAIH,KAAK,GAAGW,aAAa,EAAE;QACzB,OAAO,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIX,KAAK,IAAIW,aAAa,IAAIX,KAAK,IAAIY,WAAW,EAAE;QACzD,OAAO,QAAQ,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,OAAO,SAAS,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,EAAErG,KAAK,CAAC;MACnD,OAAO,SAAS;IAClB;EACF;EAEAtD,OAAOA,CAACwD,GAAQ;IACdA,GAAG,CAACgB,KAAK,EAAE;EACb;EAEAT,UAAUA,CAAA;IACR,IAAI,CAAC1C,KAAK,CAACuI,KAAK,EAAE;IAClB,IAAI,CAACvI,KAAK,CAACwI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACrJ,uBAAuB,CAACrH,gBAAgB,CAAC;IAC9E,IAAI,CAACkI,KAAK,CAACwI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACrJ,uBAAuB,CAACpH,cAAc,CAAC;IAC5E,IAAI,CAACiI,KAAK,CAACyI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACtJ,uBAAuB,CAACrH,gBAAgB,EAAE,IAAI,CAACqH,uBAAuB,CAACpH,cAAc,CAAC;EACtI;EAEA2Q,gBAAgBA,CAAA;IACd,IAAI,CAACvI,MAAM,CAACwI,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAACnH,WAAW,EAAEC,kBAAkB,EAAEiC,GAAG,EAAE,CAAC,CAAC;EACnG;EAEA;EACAjM,gBAAgBA,CAAC+O,QAAwB;IACvC,IAAI,CAACnK,wBAAwB,GAAGmK,QAAQ,IAAI,IAAI;IAEhD;IACA,MAAMoC,iBAAiB,GAAG,IAAI,CAACvR,cAAc,CAACC,MAAM,GAAG,CAAC;IAExD,IAAI,CAACqG,aAAa,GAAG;MACnBQ,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbT,UAAU,EAAE,CAACgL,iBAAiB,IAAI,CAACpC,QAAQ;MAC3C3F,iBAAiB,EAAE2F,QAAQ,GAAG,CAACA,QAAQ,CAAClK,IAAI,CAAC,GAAG,EAAE;MAClDwE,cAAc,EAAE,EAAE;MAClBzJ,cAAc,EAAE;KACjB;IAED;IACA,IAAImP,QAAQ,EAAE;MACZA,QAAQ,CAAChJ,MAAM,CAACwE,OAAO,CAACoC,KAAK,IAAG;QAC9BA,KAAK,CAACjM,QAAQ,GAAG,KAAK;QACtBiM,KAAK,CAACnH,MAAM,CAAC+E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC9J,QAAQ,GAAG,KAAK,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAAC2H,aAAa,CAACwC,IAAI,CAAC,IAAI,CAACuG,kBAAkB,CAAC;EAClD;EAEA;EACAzL,sBAAsBA,CAACgH,KAAiB;IACtC,IAAIA,KAAK,CAACjM,QAAQ,EAAE;MAClBiM,KAAK,CAACnH,MAAM,CAAC+E,OAAO,CAACC,KAAK,IAAG;QAC3B,IAAIA,KAAK,CAAClJ,QAAQ,EAAE;UAClBkJ,KAAK,CAAC9J,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLiM,KAAK,CAACnH,MAAM,CAAC+E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC9J,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA;EACA0G,aAAaA,CAACsD,GAAQ;IACpB;IACA,IAAI,CAACnC,KAAK,CAACuI,KAAK,EAAE;IAClB,IAAI,CAACvI,KAAK,CAACwI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC7K,aAAa,CAACQ,SAAS,CAAC;IAC3D,IAAI,CAAC6B,KAAK,CAACwI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC7K,aAAa,CAACU,OAAO,CAAC;IACzD,IAAI,CAAC2B,KAAK,CAACyI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC9K,aAAa,CAACQ,SAAS,EAAE,IAAI,CAACR,aAAa,CAACU,OAAO,CAAC;IAElG,IAAI,IAAI,CAAC2B,KAAK,CAAC2C,aAAa,CAACrL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACyI,OAAO,CAAC6C,aAAa,CAAC,IAAI,CAAC5C,KAAK,CAAC2C,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMmG,cAAc,GAAU,EAAE;IAEhC,IAAI,IAAI,CAACnL,aAAa,CAACC,UAAU,EAAE;MACjC;MACA,IAAI,CAACE,eAAe,CAACkE,OAAO,CAACC,KAAK,IAAG;QACnC,IAAIA,KAAK,CAAClJ,QAAQ,EAAE;UAClB+P,cAAc,CAACzE,IAAI,CAAC;YAClBtL,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ;YACxBjB,gBAAgB,EAAE,IAAI,CAACyK,UAAU,CAAC,IAAI,CAAC5E,aAAa,CAACQ,SAAS,CAAC;YAC/DpG,cAAc,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAAC5E,aAAa,CAACU,OAAO;WAC3D,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAAChH,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACD,cAAc,CAAC2K,OAAO,CAACC,KAAK,IAAG;UAClC,IAAIA,KAAK,CAAClJ,QAAQ,EAAE;YAClB+P,cAAc,CAACzE,IAAI,CAAC;cAClBtL,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ;cACxBjB,gBAAgB,EAAE,IAAI,CAACyK,UAAU,CAAC,IAAI,CAAC5E,aAAa,CAACQ,SAAS,CAAC;cAC/DpG,cAAc,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAAC5E,aAAa,CAACU,OAAO;aAC3D,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAAChC,wBAAwB,EAAE;QACxC;QACA,IAAI,CAACA,wBAAwB,CAACmB,MAAM,CAACwE,OAAO,CAACoC,KAAK,IAAG;UACnDA,KAAK,CAACnH,MAAM,CAAC+E,OAAO,CAACC,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAAC9J,QAAQ,IAAI8J,KAAK,CAAClJ,QAAQ,EAAE;cACpC+P,cAAc,CAACzE,IAAI,CAAC;gBAClBtL,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ;gBACxBjB,gBAAgB,EAAE,IAAI,CAACyK,UAAU,CAAC,IAAI,CAAC5E,aAAa,CAACQ,SAAS,CAAC;gBAC/DpG,cAAc,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAAC5E,aAAa,CAACU,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIyK,cAAc,CAACxR,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAACyI,OAAO,CAAC6C,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAAC3C,aAAa,CAAC6C,oCAAoC,CAAC;MACtDC,IAAI,EAAE+F;KACP,CAAC,CAACxH,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC6B,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACjD,OAAO,CAACkD,aAAa,CAAC,QAAQ6F,cAAc,CAACxR,MAAM,WAAW,CAAC;QACpE;QACA,IAAI,CAACD,cAAc,CAAC2K,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC9J,QAAQ,GAAG,KAAK,CAAC;QAC5D,IAAI,CAACd,cAAc,GAAG,EAAE;QACxB,IAAI,CAACgE,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC6H,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA;EACA/J,cAAcA,CAAC6I,KAAqB;IAClC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IACzC,OAAO,UAAUF,MAAM,EAAE;EAC3B;EAEA;EACAgH,eAAeA,CAAC9G,KAAqB;IACnC,IAAIA,KAAK,CAAClJ,QAAQ,EAAE;MAClB;MACA,IAAI,CAACF,SAAS,CAAC,IAAI,CAACmQ,MAAM,EAAE/G,KAAK,CAAC;IACpC;EACF;EAEA;EACA0D,oBAAoBA,CAACC,IAA6B;IAChD,IAAI,CAAC9H,eAAe,GAAG,EAAE;IAEzB8H,IAAI,CAAC5D,OAAO,CAACkC,SAAS,IAAG;MACvB,MAAM6B,SAAS,GAAG7B,SAAS,CAAClL,UAAU,IAAI,EAAE;MAE5CkL,SAAS,CAACC,OAAO,EAAEnC,OAAO,CAACC,KAAK,IAAG;QACjC,IAAI,CAACnE,eAAe,CAACuG,IAAI,CAAC;UACxBrL,UAAU,EAAE+M,SAAS;UACrB9M,aAAa,EAAEgJ,KAAK,CAAChJ,aAAa,IAAI,KAAK;UAC3CF,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ,IAAI,CAAC;UAC7BG,MAAM,EAAE+I,KAAK,CAAC/I,MAAM,IAAI,CAAC;UACzBpB,gBAAgB,EAAEmK,KAAK,CAACnK,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEkK,KAAK,CAAClK,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAChC,QAAQ,EAAE;IAEf;IACA,IAAI,CAAC8S,iBAAiB,EAAE;EAC1B;EAEA;EACQA,iBAAiBA,CAAA;IACvB,MAAMC,YAAY,GAAG;MACnBC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACV,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAACxL,eAAe,CAACkE,OAAO,CAACC,KAAK,IAAG;MACnC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;MACzC,IAAIiH,YAAY,CAACK,cAAc,CAACxH,MAAM,CAAC,EAAE;QACvCmH,YAAY,CAACnH,MAAmC,CAAC,EAAE;MACrD;IACF,CAAC,CAAC;IAEFgG,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAEN,YAAY,CAAC;IAClCnB,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAE,IAAInH,IAAI,EAAE,CAACoH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;EAIA;EACAvT,QAAQA,CAAA;IACN;IACA,MAAMwT,qBAAqB,GAAG,IAAI,CAACtS,cAAc,CAACmM,GAAG,CAACvB,KAAK,IAAIA,KAAK,CAAClJ,QAAQ,CAAC;IAE9E,IAAI,CAAC4B,cAAc,GAAG,IAAI,CAACmD,eAAe,CAAC6I,MAAM,CAAC1E,KAAK,IAAG;MACxD;MACA,IAAI,IAAI,CAAChM,aAAa,CAACC,aAAa,EAAE;QACpC,MAAM0Q,OAAO,GAAG,IAAI,CAAC3Q,aAAa,CAACC,aAAa,CAAC2Q,WAAW,EAAE;QAC9D,IAAI,CAAC5E,KAAK,CAACjJ,UAAU,CAAC6N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAAC3E,KAAK,CAAChJ,aAAa,CAAC4N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;UACtD,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACjG,gBAAgB,IAAIsB,KAAK,CAAChJ,aAAa,KAAK,IAAI,CAAC0H,gBAAgB,EAAE;QAC1E,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAAC1K,aAAa,CAACI,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAAC6Q,mBAAmB,CAACjF,KAAK,CAAC,EAAE;UACpC,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAChM,aAAa,CAACO,WAAW,EAAE;QAClC,MAAM8G,WAAW,GAAG6J,QAAQ,CAAC,IAAI,CAAClR,aAAa,CAACO,WAAW,CAAC;QAC5D,IAAIyL,KAAK,CAAC/I,MAAM,KAAKoE,WAAW,EAAE;UAChC,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF;IACA,IAAI,CAACjG,cAAc,GAAG,IAAI,CAACsD,cAAc,CAACgM,MAAM,CAAC1E,KAAK,IACpD0H,qBAAqB,CAAC3C,QAAQ,CAAC/E,KAAK,CAAClJ,QAAQ,CAAC,CAC/C;IAED;IACA,IAAI,CAAC+E,eAAe,CAACkE,OAAO,CAACC,KAAK,IAAG;MACnCA,KAAK,CAAC9J,QAAQ,GAAG,IAAI,CAACd,cAAc,CAAC0P,IAAI,CAAC5O,QAAQ,IAAIA,QAAQ,CAACY,QAAQ,KAAKkJ,KAAK,CAAClJ,QAAQ,CAAC;IAC7F,CAAC,CAAC;IAEF;IACA,IAAI,CAAC6Q,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACjQ,WAAW,GAAG,CAAC;IACpB,IAAI,CAACkQ,gBAAgB,EAAE;EACzB;EAEA;EACQD,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACzN,eAAe,CAAC7E,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC+D,SAAS,GAAG,KAAK;IACxB,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI,CAACc,eAAe,CAAC2N,KAAK,CAAC7H,KAAK,IAC/C,CAACA,KAAK,CAAClJ,QAAQ,IAAIkJ,KAAK,CAAC9J,QAAQ,CAClC;IACH;EACF;EAEA;EACA0R,gBAAgBA,CAAA;IACd,IAAI,CAAC3P,UAAU,GAAGO,IAAI,CAACsP,IAAI,CAAC,IAAI,CAACpP,cAAc,CAACrD,MAAM,GAAG,IAAI,CAACT,QAAQ,CAAC;IACvE,MAAMmT,UAAU,GAAG,CAAC,IAAI,CAACrQ,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC9C,QAAQ;IACzD,MAAMoT,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACnT,QAAQ;IAC3C,IAAI,CAACsF,eAAe,GAAG,IAAI,CAACxB,cAAc,CAAC+B,KAAK,CAACsN,UAAU,EAAEC,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACL,oBAAoB,EAAE;EAC7B;EAEA;EACA7S,gBAAgBA,CAAA;IACd,IAAI,CAAC4C,WAAW,GAAG,CAAC;IACpB,IAAI,CAACkQ,gBAAgB,EAAE;EACzB;EAEA;EACAnQ,QAAQA,CAACwQ,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAChQ,UAAU,EAAE;MACxC,IAAI,CAACP,WAAW,GAAGuQ,IAAI;MACvB,IAAI,CAACL,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAjP,eAAeA,CAAA;IACb,MAAMuP,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAInC,KAAK,GAAGxN,IAAI,CAAC4P,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC1Q,WAAW,GAAGc,IAAI,CAAC2J,KAAK,CAACgG,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAIlC,GAAG,GAAGzN,IAAI,CAACC,GAAG,CAAC,IAAI,CAACR,UAAU,EAAE+N,KAAK,GAAGmC,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAIlC,GAAG,GAAGD,KAAK,GAAG,CAAC,GAAGmC,UAAU,EAAE;MAChCnC,KAAK,GAAGxN,IAAI,CAAC4P,GAAG,CAAC,CAAC,EAAEnC,GAAG,GAAGkC,UAAU,GAAG,CAAC,CAAC;IAC3C;IAEA,KAAK,IAAIE,CAAC,GAAGrC,KAAK,EAAEqC,CAAC,IAAIpC,GAAG,EAAEoC,CAAC,EAAE,EAAE;MACjCH,KAAK,CAAC9F,IAAI,CAACiG,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACA7O,iBAAiBA,CAAA;IACf,IAAI,CAACa,eAAe,CAAC6F,OAAO,CAACC,KAAK,IAAG;MACnC,IAAIA,KAAK,CAAClJ,QAAQ,EAAE;QAClBkJ,KAAK,CAAC9J,QAAQ,GAAG,IAAI,CAACkD,SAAS;MACjC;IACF,CAAC,CAAC;IACF,IAAI,CAACkP,oBAAoB,EAAE;EAC7B;EAEA;EACAnS,sBAAsBA,CAAA;IACpB,IAAI,CAACmS,oBAAoB,EAAE;IAC3B,IAAI,CAAClP,SAAS,GAAG,IAAI,CAACc,eAAe,CAAC2N,KAAK,CAAC7H,KAAK,IAC/C,CAACA,KAAK,CAAClJ,QAAQ,IAAIkJ,KAAK,CAAC9J,QAAQ,CAClC;EACH;EAEA;EACAoS,oBAAoBA,CAAA;IAClB,IAAI,CAAClT,cAAc,GAAG,IAAI,CAACyG,eAAe,CAAC6I,MAAM,CAAC1E,KAAK,IAAIA,KAAK,CAAC9J,QAAQ,CAAC;EAC5E;EAEA;EACAuD,IAAIA,CAAC8O,KAAa;IAChB,IAAI,IAAI,CAACvO,SAAS,KAAKuO,KAAK,EAAE;MAC5B,IAAI,CAACtO,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IACpE,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAGuO,KAAK;MACtB,IAAI,CAACtO,aAAa,GAAG,KAAK;IAC5B;IAEA,IAAI,CAACvB,cAAc,CAACe,IAAI,CAAC,CAAC4I,CAAC,EAAEC,CAAC,KAAI;MAChC,IAAIkG,MAAM,GAAInG,CAAS,CAACkG,KAAK,CAAC;MAC9B,IAAIE,MAAM,GAAInG,CAAS,CAACiG,KAAK,CAAC;MAE9B;MACA,IAAIA,KAAK,CAACxD,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC1ByD,MAAM,GAAGA,MAAM,GAAG,IAAIpI,IAAI,CAACoI,MAAM,CAAC,CAAC3C,OAAO,EAAE,GAAG,CAAC;QAChD4C,MAAM,GAAGA,MAAM,GAAG,IAAIrI,IAAI,CAACqI,MAAM,CAAC,CAAC5C,OAAO,EAAE,GAAG,CAAC;MAClD;MAEA;MACA,IAAI0C,KAAK,KAAK,QAAQ,EAAE;QACtBC,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC,IAAI,CAAC;QAC5BC,MAAM,GAAGC,MAAM,CAACD,MAAM,CAAC,IAAI,CAAC;MAC9B;MAEA;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAAC5D,WAAW,EAAE;QAC7B6D,MAAM,GAAGA,MAAM,CAAC7D,WAAW,EAAE;MAC/B;MAEA,IAAI4D,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACxO,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC9C;MACA,IAAIuO,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACxO,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9C;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAAC2N,gBAAgB,EAAE;EACzB;EAEA;EACAzN,cAAcA,CAACwO,MAAc,EAAE3I,KAAqB;IAClD,OAAOA,KAAK,CAAClJ,QAAQ;EACvB;EAEA;EACAO,aAAaA,CAAC2I,KAAqB;IACjC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IAEzC,QAAQF,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,UAAU;QACb,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;;;uCApjCWpC,0BAA0B,EAAAnL,EAAA,CAAAqW,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvW,EAAA,CAAAqW,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAzW,EAAA,CAAAqW,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3W,EAAA,CAAAqW,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA7W,EAAA,CAAAqW,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA/W,EAAA,CAAAqW,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAAhX,EAAA,CAAAqW,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAAlX,EAAA,CAAAqW,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA1BjM,0BAA0B;MAAAkM,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;uCAT1B,EAAE,GAAAxX,EAAA,CAAA0X,0BAAA,EAAA1X,EAAA,CAAA2X,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCrEbxX,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAmB,SAAA,qBAAiC;UACnCnB,EAAA,CAAAG,YAAA,EAAiB;UAMXH,EALN,CAAAC,cAAA,mBAAc,cAEuB,cAEK,YACQ;UAAAD,EAAA,CAAAE,MAAA,2FAAc;UAC5DF,EAD4D,CAAAG,YAAA,EAAI,EAC1D;UAMAH,EAHN,CAAAC,cAAA,cAA6B,cACU,eACJ,iBACa;UAAAD,EAAA,CAAAE,MAAA,qBAAG;UAAAF,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;UACvFH,EAAA,CAAAC,cAAA,qBACsD;UADvBD,EAAA,CAAAoB,gBAAA,2BAAA6W,wEAAA3W,MAAA;YAAAtB,EAAA,CAAAa,aAAA,CAAAqX,GAAA;YAAAlY,EAAA,CAAAwB,kBAAA,CAAAiW,GAAA,CAAAzK,WAAA,CAAAC,kBAAA,EAAA3L,MAAA,MAAAmW,GAAA,CAAAzK,WAAA,CAAAC,kBAAA,GAAA3L,MAAA;YAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;UAAA,EAA4C;UACzEtB,EAAA,CAAAW,UAAA,4BAAAwX,yEAAA;YAAAnY,EAAA,CAAAa,aAAA,CAAAqX,GAAA;YAAA,OAAAlY,EAAA,CAAAiB,WAAA,CAAkBwW,GAAA,CAAA7G,iBAAA,EAAmB;UAAA,EAAC;UACtC5Q,EAAA,CAAAkC,UAAA,KAAAkW,gDAAA,wBAAoE;UAIxEpY,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAA+B,iBACa;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBACe;UADeD,EAAA,CAAAoB,gBAAA,2BAAAiX,wEAAA/W,MAAA;YAAAtB,EAAA,CAAAa,aAAA,CAAAqX,GAAA;YAAAlY,EAAA,CAAAwB,kBAAA,CAAAiW,GAAA,CAAAtL,gBAAA,EAAA7K,MAAA,MAAAmW,GAAA,CAAAtL,gBAAA,GAAA7K,MAAA;YAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;UAAA,EAA8B;UAACtB,EAAA,CAAAW,UAAA,4BAAA2X,yEAAA;YAAAtY,EAAA,CAAAa,aAAA,CAAAqX,GAAA;YAAA,OAAAlY,EAAA,CAAAiB,WAAA,CAAkBwW,GAAA,CAAAxF,gBAAA,EAAkB;UAAA,EAAC;UAEhGjS,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAkC,UAAA,KAAAqW,gDAAA,wBAAuE;UAI3EvY,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAA+B,iBACa;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEtDH,EADF,CAAAC,cAAA,eAA8B,yBACA;UAC1BD,EAAA,CAAAmB,SAAA,mBAAoD;UACpDnB,EAAA,CAAAC,cAAA,iBAC6C;UAA3CD,EAAA,CAAAoB,gBAAA,2BAAAoX,oEAAAlX,MAAA;YAAAtB,EAAA,CAAAa,aAAA,CAAAqX,GAAA;YAAAlY,EAAA,CAAAwB,kBAAA,CAAAiW,GAAA,CAAAzK,WAAA,CAAA1J,gBAAA,EAAAhC,MAAA,MAAAmW,GAAA,CAAAzK,WAAA,CAAA1J,gBAAA,GAAAhC,MAAA;YAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;UAAA,EAA0C;UAD5CtB,EAAA,CAAAG,YAAA,EAC6C;UAC7CH,EAAA,CAAAmB,SAAA,4BAA8D;UAChEnB,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,yBAA4B;UAC1BD,EAAA,CAAAmB,SAAA,mBAAoD;UACpDnB,EAAA,CAAAC,cAAA,iBAC2C;UAAzCD,EAAA,CAAAoB,gBAAA,2BAAAqX,oEAAAnX,MAAA;YAAAtB,EAAA,CAAAa,aAAA,CAAAqX,GAAA;YAAAlY,EAAA,CAAAwB,kBAAA,CAAAiW,GAAA,CAAAzK,WAAA,CAAAzJ,cAAA,EAAAjC,MAAA,MAAAmW,GAAA,CAAAzK,WAAA,CAAAzJ,cAAA,GAAAjC,MAAA;YAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;UAAA,EAAwC;UAD1CtB,EAAA,CAAAG,YAAA,EAC2C;UAC3CH,EAAA,CAAAmB,SAAA,4BAA4D;UAGlEnB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAgC,eACF,kBAC0D;UAApDD,EAAA,CAAAW,UAAA,mBAAA+X,6DAAA;YAAA1Y,EAAA,CAAAa,aAAA,CAAAqX,GAAA;YAAA,OAAAlY,EAAA,CAAAiB,WAAA,CAASwW,GAAA,CAAA/I,kBAAA,EAAoB;UAAA,EAAC;UAC5D1O,EAAA,CAAAmB,SAAA,aAAkC;UAAAnB,EAAA,CAAAE,MAAA,qBACpC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBACiB;UAD8BD,EAAA,CAAAW,UAAA,mBAAAgY,6DAAA;YAAA3Y,EAAA,CAAAa,aAAA,CAAAqX,GAAA;YAAA,OAAAlY,EAAA,CAAAiB,WAAA,CAASwW,GAAA,CAAAnK,YAAA,EAAc;UAAA,EAAC;UAErEtN,EAAA,CAAAmB,SAAA,aAA2B;UAKrCnB,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;UAKNH,EAAA,CAAAkC,UAAA,KAAA0W,0CAAA,oBAAuE;UAkDzE5Y,EAAA,CAAAG,YAAA,EAAM;UAsRNH,EAnRA,CAAAkC,UAAA,KAAA2W,0CAAA,oBAAyE,KAAAC,0CAAA,kBAuQ2B,KAAAC,0CAAA,kBAYtD;UAalD/Y,EADE,CAAAG,YAAA,EAAe,EACP;UAkGVH,EA/FA,CAAAkC,UAAA,KAAA8W,kDAAA,iCAAAhZ,EAAA,CAAAiZ,sBAAA,CAAgE,KAAAC,kDAAA,gCAAAlZ,EAAA,CAAAiZ,sBAAA,CAkFG,KAAAE,kDAAA,iCAAAnZ,EAAA,CAAAiZ,sBAAA,CAaf;;;;;UA9eTjZ,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAyC,gBAAA,YAAAgV,GAAA,CAAAzK,WAAA,CAAAC,kBAAA,CAA4C;UAE7CjN,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAqX,GAAA,CAAA1I,oBAAA,CAAuB;UAQvB/O,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAyC,gBAAA,YAAAgV,GAAA,CAAAtL,gBAAA,CAA8B;UAG1BnM,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAAqX,GAAA,CAAAvL,eAAA,CAAkB;UAWFlM,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,iBAAAgZ,aAAA,CAA0B;UACtEpZ,EAAA,CAAAyC,gBAAA,YAAAgV,GAAA,CAAAzK,WAAA,CAAA1J,gBAAA,CAA0C;UAMEtD,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,iBAAAiZ,WAAA,CAAwB;UACpErZ,EAAA,CAAAyC,gBAAA,YAAAgV,GAAA,CAAAzK,WAAA,CAAAzJ,cAAA,CAAwC;UAQmBvD,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,aAAAqX,GAAA,CAAAlL,OAAA,CAAoB;UAGXvM,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,aAAAqX,GAAA,CAAAlL,OAAA,CAAoB;UAY/DvM,EAAA,CAAAM,SAAA,GAAgC;UAAhCN,EAAA,CAAAI,UAAA,SAAAqX,GAAA,CAAAnO,eAAA,CAAAxG,MAAA,KAAgC;UAqDhC9C,EAAA,CAAAM,SAAA,EAAgC;UAAhCN,EAAA,CAAAI,UAAA,SAAAqX,GAAA,CAAAnO,eAAA,CAAAxG,MAAA,KAAgC;UAuQxC9C,EAAA,CAAAM,SAAA,EAAmE;UAAnEN,EAAA,CAAAI,UAAA,SAAAqX,GAAA,CAAAnO,eAAA,CAAAxG,MAAA,UAAA2U,GAAA,CAAA3G,gBAAA,CAAAhO,MAAA,OAAmE;UAYnE9C,EAAA,CAAAM,SAAA,EAAa;UAAbN,EAAA,CAAAI,UAAA,SAAAqX,GAAA,CAAAlL,OAAA,CAAa;;;qBDxU5C/M,YAAY,EAAA8Z,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAE5Z,YAAY,EAAA6Z,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,YAAA,EAAAL,EAAA,CAAAM,OAAA,EAAAxD,EAAA,CAAAyD,eAAA,EAAAzD,EAAA,CAAA0D,mBAAA,EAAA1D,EAAA,CAAA2D,qBAAA,EAAA3D,EAAA,CAAA4D,qBAAA,EAAA5D,EAAA,CAAA6D,mBAAA,EAAA7D,EAAA,CAAA8D,gBAAA,EAAA9D,EAAA,CAAA+D,iBAAA,EAAA/D,EAAA,CAAAgE,iBAAA,EAAAhE,EAAA,CAAAiE,oBAAA,EAAAjE,EAAA,CAAAkE,iBAAA,EAAAlE,EAAA,CAAAmE,eAAA,EAAAnE,EAAA,CAAAoE,qBAAA,EAAApE,EAAA,CAAAqE,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAC1Brb,kBAAkB,EAAEC,mBAAmB;MAAAqb,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}