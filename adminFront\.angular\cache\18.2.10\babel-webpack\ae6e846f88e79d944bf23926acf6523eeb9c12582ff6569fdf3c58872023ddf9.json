{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Counter block mode.\n   */\n  CryptoJS.mode.CTR = function () {\n    var CTR = CryptoJS.lib.BlockCipherMode.extend();\n    var Encryptor = CTR.Encryptor = CTR.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n        var iv = this._iv;\n        var counter = this._counter;\n\n        // Generate keystream\n        if (iv) {\n          counter = this._counter = iv.slice(0);\n\n          // Remove IV for subsequent blocks\n          this._iv = undefined;\n        }\n        var keystream = counter.slice(0);\n        cipher.encryptBlock(keystream, 0);\n\n        // Increment counter\n        counter[blockSize - 1] = counter[blockSize - 1] + 1 | 0;\n\n        // Encrypt\n        for (var i = 0; i < blockSize; i++) {\n          words[offset + i] ^= keystream[i];\n        }\n      }\n    });\n    CTR.Decryptor = Encryptor;\n    return CTR;\n  }();\n  return CryptoJS.mode.CTR;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}