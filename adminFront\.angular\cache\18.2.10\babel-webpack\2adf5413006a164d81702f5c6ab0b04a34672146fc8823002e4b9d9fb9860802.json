{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInQuarter } from \"../constants/index.js\";\n/**\n * @name quartersToMonths\n * @category Conversion Helpers\n * @summary Convert number of quarters to months.\n *\n * @description\n * Convert a number of quarters to a full number of months.\n *\n * @param {number} quarters - number of quarters to be converted\n *\n * @returns {number} the number of quarters converted in months\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 quarters to months\n * const result = quartersToMonths(2)\n * //=> 6\n */\nexport default function quartersToMonths(quarters) {\n  requiredArgs(1, arguments);\n  return Math.floor(quarters * monthsInQuarter);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}