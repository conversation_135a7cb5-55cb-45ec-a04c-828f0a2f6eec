{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { parseNumericPattern, parseNDigits } from \"../utils.js\";\nexport var Hour1To24Parser = /*#__PURE__*/function (_Parser) {\n  _inherits(Hour1To24Parser, _Parser);\n  var _super = _createSuper(Hour1To24Parser);\n  function Hour1To24Parser() {\n    var _this;\n    _classCallCheck(this, Hour1To24Parser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 70);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['a', 'b', 'h', 'H', 'K', 't', 'T']);\n    return _this;\n  }\n  _createClass(Hour1To24Parser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      switch (token) {\n        case 'k':\n          return parseNumericPattern(numericPatterns.hour24h, dateString);\n        case 'ko':\n          return match.ordinalNumber(dateString, {\n            unit: 'hour'\n          });\n        default:\n          return parseNDigits(token.length, dateString);\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value >= 1 && value <= 24;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      var hours = value <= 24 ? value % 24 : value;\n      date.setUTCHours(hours, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return Hour1To24Parser;\n}(Parser);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}