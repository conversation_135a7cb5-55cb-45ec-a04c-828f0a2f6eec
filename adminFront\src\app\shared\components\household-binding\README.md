# 戶別綁定元件實作指南

## 概述
我們已經創建了一個功能完整的戶別綁定共用元件，支援從 API 載入資料或使用 Mock 資料，可以替代現有的複雜表格選擇方式。

## 新功能更新 (2025-06-24)
✅ **API 整合** - 支援透過 `buildCaseId` 從 `HouseCustomService.getDropDown` 載入戶別資料  
✅ **載入狀態** - 增加載入狀態顯示和錯誤處理  
✅ **資料轉換** - 自動轉換 API 回應為元件所需格式  
✅ **備援機制** - API 載入失敗時自動使用 Mock 資料  

## 文件結構
```
src/app/shared/
├── components/
│   └── household-binding/
│       ├── household-binding.component.ts          (主要元件)
│       ├── household-binding.component.html        (模板)
│       ├── household-binding.component.scss        (樣式)
│       ├── household-binding-demo.component.ts     (演示元件)
│       └── README.md                               (說明文件)
└── shared.module.ts

src/services/api/
├── services/
│   └── HouseCustom.service.ts                     (API 服務)
└── models/
    └── house.model.ts                              (資料模型)
```

## 元件特色

### 1. 階層式選擇
- 左側棟別選擇器
- 右側戶別網格顯示
- 支援按棟別分組管理

### 2. 智能搜尋
- 即時搜尋戶別編號
- 支援模糊匹配

### 3. 批次操作
- 全選當前顯示的戶別
- 全選整棟戶別
- 一鍵清空所有選擇

### 4. 視覺回饋
- 已選戶別按棟別分組顯示
- 可獨立移除已選項目
- 清晰的選擇狀態指示

### 5. 靈活配置
- 支援最大選擇數量限制
- 可啟用/禁用搜尋功能
- 可控制批次選擇功能
- 支援自定義戶別資料

## 使用方式

### 1. 在模組中導入
```typescript
// 在需要使用的模組中導入 SharedModule
import { SharedModule } from '../../shared/shared.module';

@NgModule({
  imports: [
    // ...其他模組
    SharedModule
  ],
  // ...
})
export class YourModule { }
```

### 2. 在元件中使用

#### 方式一：使用 API 資料 (建議)
```typescript
import { Component } from '@angular/core';
import { HouseholdItem } from '../../shared/components/household-binding/household-binding.component';

export class YourComponent {
  selectedHouseholds: string[] = [];
  buildCaseId: number = 123; // 建案ID

  onHouseholdSelectionChange(selectedItems: HouseholdItem[]) {
    console.log('選中的戶別:', selectedItems);
    // 處理選擇變更邏輯
  }
}
```

#### HTML 模板 (API 方式)
```html
<app-household-binding
  [(ngModel)]="selectedHouseholds"
  [buildCaseId]="buildCaseId"
  [maxSelections]="20"
  placeholder="請選擇戶別"
  (selectionChange)="onHouseholdSelectionChange($event)">
</app-household-binding>
```

#### 方式二：使用自定義資料
```typescript
import { Component } from '@angular/core';
import { HouseholdItem, BuildingData } from '../../shared/components/household-binding/household-binding.component';

export class YourComponent {
  selectedHouseholds: string[] = [];
  
  // 自定義戶別資料
  customBuildingData: BuildingData = {
    'A棟': [
      { code: 'A001', building: 'A棟', floor: '1F', isSelected: false, isDisabled: false },
      { code: 'A002', building: 'A棟', floor: '1F', isSelected: false, isDisabled: false },
      // ...更多戶別
    ],
    'B棟': [
      { code: 'B001', building: 'B棟', floor: '1F', isSelected: false, isDisabled: false },
      // ...更多戶別
    ]
  };

  onHouseholdSelectionChange(selectedItems: HouseholdItem[]) {
    console.log('選中的戶別:', selectedItems);
    // 處理選擇變更邏輯
  }
}
```

#### HTML 模板
```html
<app-household-binding
  [(ngModel)]="selectedHouseholds"
  placeholder="請選擇戶別"
  [maxSelections]="20"
  [disabled]="false"
  [buildingData]="customBuildingData"
  [allowBatchSelect]="true"
  (selectionChange)="onHouseholdSelectionChange($event)">
</app-household-binding>
```

## API 參考

### 輸入屬性 (Inputs)
| 屬性名 | 類型 | 預設值 | 說明 |
|--------|------|--------|------|
| placeholder | string | '請選擇戶別' | 選擇器占位符文字 |
| maxSelections | number \| null | null | 最大選擇數量限制 |
| disabled | boolean | false | 是否禁用元件 |
| buildingData | BuildingData | 模擬資料 | 戶別資料源 |
| allowBatchSelect | boolean | true | 是否允許批次選擇 |
| preFilterHouseType | number \| null | null | 預先篩選的戶別類型（1=地主戶，2=銷售戶） |
| reminderText | string | '' | 提醒文案 |
| buildCaseId | number \| null | null | 建案ID（用於從API載入資料） |
| excludedHouseIds | number[] | [] | 排除的戶別ID列表 |
| useHouseNameMode | boolean | false | 使用戶別名稱模式 |

### 輸出事件 (Outputs)
| 事件名 | 參數類型 | 說明 |
|--------|----------|------|
| selectionChange | HouseholdItem[] | 選擇變更時觸發 |

### 資料介面
```typescript
interface HouseholdItem {
  code: string;        // 戶別編號
  building: string;    // 棟別名稱
  floor?: string;      // 樓層資訊
  isSelected?: boolean; // 是否已選中
  isDisabled?: boolean; // 是否禁用
}

interface BuildingData {
  [key: string]: HouseholdItem[];
}
```

## 整合到現有專案

### 在 notice-management 中的整合步驟

1. **導入 SharedModule**
```typescript
// notice-management.component.ts
@Component({
  // ...
  imports: [CommonModule, SharedModule, /* ...其他imports */],
})
```

2. **添加必要的屬性**
```typescript
export class NoticeManagementComponent {
  selectedHouseholds: string[] = [];
  
  // 轉換現有資料格式
  convertToHouseholdData(): BuildingData {
    const buildingData: BuildingData = {};
    
    if (!this.houseList2D || this.houseList2D.length === 0) {
      return buildingData;
    }

    this.houseList2D.forEach(row => {
      row.forEach(house => {
        if (!house.CHouseHold) return;
        
        const building = house.CHouseHold.charAt(0) + '棟';
        
        if (!buildingData[building]) {
          buildingData[building] = [];
        }
        
        buildingData[building].push({
          code: house.CHouseHold,
          building: building,
          floor: house.CFloor ? `${house.CFloor}F` : undefined,
          houseId: house.CHouseID,
          houseType: house.CHouseType, // 保留戶別類型資訊供子元件篩選使用
          isSelected: house.CIsSelect || false,
          isDisabled: !house.CIsEnable || this.saveSpecialNoticeFile?.CExamineStauts === 0
          // 注意：不要在父元件中根據 selectedCNoticeType 預先篩選
          // 篩選邏輯應該由戶別綁定元件根據 preFilterHouseType 參數處理
        });
      });
    });
    
    return buildingData;
  }

  onHouseholdSelectionChange(selectedItems: HouseholdItem[]) {
    // 同步更新原有的 houseList2D
    this.updateHouseListSelection(selectedItems.map(item => item.code));
  }

  private updateHouseListSelection(selectedCodes: string[]) {
    if (!this.houseList2D) return;
    
    this.houseList2D.forEach(row => {
      row.forEach(house => {
        if (house.CHouseHold) {
          house.CIsSelect = selectedCodes.includes(house.CHouseHold);
        }
      });
    });
  }
}
```

3. **在模板中使用**
```html
<!-- 替換原有的表格 -->
<div class="form-group d-flex mb-0">
  <label for="houseList2D" baseLabel class="required-field mr-3" style="min-width:75px">適用戶別</label>
</div>

<div class="household-binding-wrapper mt-2" *ngIf="isHouseList">
  <app-household-binding
    [(ngModel)]="selectedHouseholds"
    placeholder="請選擇適用戶別"
    [maxSelections]="20"
    [disabled]="saveSpecialNoticeFile?.CExamineStauts == 0"
    [buildingData]="convertToHouseholdData()"
    [allowBatchSelect]="true"
    [preFilterHouseType]="saveSpecialNoticeFile?.selectedCNoticeType?.value"
    [reminderText]="getHouseholdReminderText()"
    (selectionChange)="onHouseholdSelectionChange($event)">
  </app-household-binding>
</div>
```

## 樣式自定義

元件使用了響應式設計和深色主題支援。您可以通過覆蓋 CSS 類來自定義樣式：

```scss
.household-binding-container {
  // 自定義容器樣式
}

.selector-button {
  // 自定義選擇器按鈕樣式
}

.dropdown-menu {
  // 自定義下拉選單樣式
}
```

## 測試說明

### 使用演示元件測試
```bash
# 如果演示元件已經整合到路由中，可以直接訪問
# 或者在任何頁面中使用：
<app-household-binding-demo></app-household-binding-demo>
```

### 測試項目

1. **API 整合測試**
   - 輸入有效的建案ID (例如：1, 2, 3...)
   - 點擊「套用」按鈕載入API資料
   - 觀察載入狀態和結果
   - 測試API錯誤時的備援機制

2. **功能測試**
   - 測試棟別選擇
   - 測試戶別選擇和取消選擇
   - 測試搜尋功能
   - 測試批次選擇功能
   - 測試最大選擇數量限制

3. **整合測試**
   - 測試與現有表單的整合
   - 測試資料保存和載入
   - 測試禁用狀態下的行為
   - 測試多個元件間的排除功能

4. **使用者體驗測試**
   - 測試響應式設計
   - 測試鍵盤導航
   - 測試觸控設備的使用體驗
   - 測試載入狀態的顯示

## 注意事項

1. **效能考量**
   - 大量戶別資料時建議實作虛擬滾動
   - 考慮在資料變更時使用 OnPush 變更檢測策略

2. **無障礙設計**
   - 元件已包含基本的無障礙支援
   - 建議根據具體需求進一步優化

3. **瀏覽器支援**
   - 支援現代瀏覽器
   - IE11 需要額外的 polyfill

## 後續擴展

1. **虛擬滾動**
   - 支援大量戶別資料的高效顯示

2. **多選模式**
   - 支援不同的選擇模式（單選、多選、範圍選擇）

3. **資料懶載入**
   - 支援按需載入戶別資料

4. **自定義渲染**
   - 支援自定義戶別項目的顯示方式
