{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport { tap } from 'rxjs';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"../../components/pagination/pagination.component\";\nconst _c0 = [\"createModal\"];\nconst _c1 = [\"editModal\"];\nconst _c2 = [\"templateDetailModal\"];\nfunction TemplateComponent_button_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_button_52_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const createModal_r4 = i0.ɵɵreference(75);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(createModal_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 39);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const editModal_r9 = i0.ɵɵreference(77);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(editModal_r9, template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteTemplate(template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 40);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 41)(14, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_Template_button_click_14_listener() {\n      const template_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateDetailModal_r7 = i0.ɵɵreference(79);\n      return i0.ɵɵresetView(ctx_r2.viewTemplateDetail(template_r6, templateDetailModal_r7));\n    });\n    i0.ɵɵelement(15, \"i\", 43);\n    i0.ɵɵtext(16, \"\\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, TemplateComponent_tr_70_button_17_Template, 3, 0, \"button\", 44)(18, TemplateComponent_tr_70_button_18_Template, 3, 0, \"button\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const template_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CTemplateType === 1 ? \"\\u7A7A\\u9593\\u6A21\\u677F\" : template_r6.CTemplateType === 2 ? \"\\u9805\\u76EE\\u6A21\\u677F\" : \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(template_r6.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", template_r6.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 8, template_r6.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r6.CCreator || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction TemplateComponent_tr_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 50);\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵtext(3, \"\\u76EE\\u524D\\u6C92\\u6709\\u4EFB\\u4F55\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_44_Template_div_click_0_listener() {\n      const space_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleSpaceSelection(space_r14));\n    });\n    i0.ɵɵelementStart(1, \"div\", 106)(2, \"div\", 107);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 108);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const space_r14 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", space_r14.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(space_r14.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r14.CLocation || \"-\");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \"\\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"div\", 111);\n    i0.ɵɵtext(2, \" \\u5171 \");\n    i0.ɵɵelementStart(3, \"span\", 112);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ngx-pagination\", 37);\n    i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_ng_template_74_div_46_Template_ngx_pagination_PageChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spacePageIndex, $event) || (ctx_r2.spacePageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function TemplateComponent_ng_template_74_div_46_Template_ngx_pagination_PageChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.spacePageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.spaceTotalRecords);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r2.spacePageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r2.spacePageSize)(\"CollectionSize\", ctx_r2.spaceTotalRecords);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_47_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 116);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 117);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_47_span_4_Template_button_click_2_listener() {\n      const space_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeSelectedSpace(space_r17));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r17.CPart, \" \");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"label\", 113);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 114);\n    i0.ɵɵtemplate(4, TemplateComponent_ng_template_74_div_47_span_4_Template, 3, 1, \"span\", 115);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u7684\\u7A7A\\u9593 (\", ctx_r2.selectedSpacesForTemplate.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedSpacesForTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 52)(1, \"nb-card-header\", 53)(2, \"h5\", 54);\n    i0.ɵɵelement(3, \"i\", 55);\n    i0.ɵɵtext(4, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_5_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 58)(8, \"div\", 59)(9, \"div\", 60)(10, \"div\", 61)(11, \"div\", 62)(12, \"label\", 63);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 64)(15, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown.control.enter\", function TemplateComponent_ng_template_74_Template_input_keydown_control_enter_15_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 60)(17, \"div\", 61)(18, \"div\", 62)(19, \"label\", 66);\n    i0.ɵɵtext(20, \" \\u6A21\\u677F\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 64)(22, \"nb-tabset\", 67)(23, \"nb-tab\", 68);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_nb_tab_click_23_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.SpaceTemplate);\n    });\n    i0.ɵɵelementStart(24, \"div\", 69)(25, \"div\", 70)(26, \"div\", 71)(27, \"input\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchKeyword, $event) || (ctx_r2.spaceSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_74_Template_input_keyup_enter_27_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 71)(29, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchLocation, $event) || (ctx_r2.spaceSearchLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_74_Template_input_keyup_enter_29_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 74)(31, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceReset());\n    });\n    i0.ɵɵelement(32, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelement(34, \"i\", 78);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 79)(36, \"div\", 80)(37, \"div\", 5)(38, \"input\", 81);\n    i0.ɵɵlistener(\"change\", function TemplateComponent_ng_template_74_Template_input_change_38_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"label\", 82);\n    i0.ɵɵtext(40, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"small\", 83);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 84);\n    i0.ɵɵtemplate(44, TemplateComponent_ng_template_74_div_44_Template, 6, 4, \"div\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, TemplateComponent_ng_template_74_div_45_Template, 3, 0, \"div\", 86)(46, TemplateComponent_ng_template_74_div_46_Template, 7, 4, \"div\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, TemplateComponent_ng_template_74_div_47_Template, 5, 2, \"div\", 88);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"nb-tab\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_nb_tab_click_48_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.ItemTemplate);\n    });\n    i0.ɵɵelementStart(49, \"span\", 90);\n    i0.ɵɵtext(50, \"\\u9805\\u76EE\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 69)(52, \"div\", 59)(53, \"div\", 9)(54, \"div\", 61)(55, \"div\", 62)(56, \"label\", 91);\n    i0.ɵɵtext(57, \" \\u55AE\\u50F9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 64);\n    i0.ɵɵelement(59, \"input\", 92);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(60, \"div\", 9)(61, \"div\", 61)(62, \"div\", 62)(63, \"label\", 93);\n    i0.ɵɵtext(64, \" \\u55AE\\u4F4D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"div\", 64);\n    i0.ɵɵelement(66, \"input\", 94);\n    i0.ɵɵelementEnd()()()()()()()()()()()();\n    i0.ɵɵelementStart(67, \"div\", 60)(68, \"div\", 61)(69, \"div\", 62)(70, \"label\", 95);\n    i0.ɵɵtext(71, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"div\", 64)(73, \"nb-form-field\", 96)(74, \"nb-select\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_nb_select_ngModelChange_74_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(75, \"nb-option\", 16)(76, \"span\", 5);\n    i0.ɵɵelement(77, \"i\", 98);\n    i0.ɵɵtext(78, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(79, \"nb-option\", 16)(80, \"span\", 5);\n    i0.ɵɵelement(81, \"i\", 99);\n    i0.ɵɵtext(82, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(83, \"nb-card-footer\", 100)(84, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_84_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(85, \"i\", 102);\n    i0.ɵɵtext(86, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(87, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_87_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelement(88, \"i\", 104);\n    i0.ɵɵtext(89, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchLocation);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"checked\", ctx_r2.allSpacesSelected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx_r2.spaceTotalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx_r2.spacePageIndex, \" / \", ctx_r2.Math.ceil(ctx_r2.spaceTotalRecords / ctx_r2.spacePageSize), \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableSpaces);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableSpaces.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.spaceTotalRecords > ctx_r2.spacePageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedSpacesForTemplate.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate);\n    i0.ɵɵadvance(26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction TemplateComponent_ng_template_76_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 118)(1, \"nb-card-header\", 53)(2, \"h5\", 54);\n    i0.ɵɵelement(3, \"i\", 119);\n    i0.ɵɵtext(4, \"\\u7DE8\\u8F2F\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_5_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r19));\n    });\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 58)(8, \"div\", 59)(9, \"div\", 60)(10, \"div\", 61)(11, \"div\", 62)(12, \"label\", 120);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 64)(15, \"input\", 121);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_76_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 60)(17, \"div\", 61)(18, \"div\", 62)(19, \"label\", 66);\n    i0.ɵɵtext(20, \" \\u6A21\\u677F\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 64)(22, \"nb-tabset\", 67)(23, \"nb-tab\", 68);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_nb_tab_click_23_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.SpaceTemplate);\n    });\n    i0.ɵɵelementStart(24, \"span\", 90);\n    i0.ɵɵtext(25, \"\\u7A7A\\u9593\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 69)(27, \"div\", 122);\n    i0.ɵɵelement(28, \"i\", 51);\n    i0.ɵɵtext(29, \" \\u7DE8\\u8F2F\\u6A21\\u5F0F\\u4E0B\\uFF0C\\u7A7A\\u9593\\u914D\\u7F6E\\u8ACB\\u5728\\u6A21\\u677F\\u8A73\\u60C5\\u4E2D\\u9032\\u884C\\u7BA1\\u7406\\u3002 \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-tab\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_nb_tab_click_30_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.ItemTemplate);\n    });\n    i0.ɵɵelementStart(31, \"span\", 90);\n    i0.ɵɵtext(32, \"\\u9805\\u76EE\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 69)(34, \"div\", 59)(35, \"div\", 9)(36, \"div\", 61)(37, \"div\", 62)(38, \"label\", 123);\n    i0.ɵɵtext(39, \" \\u55AE\\u50F9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 64);\n    i0.ɵɵelement(41, \"input\", 124);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(42, \"div\", 9)(43, \"div\", 61)(44, \"div\", 62)(45, \"label\", 125);\n    i0.ɵɵtext(46, \" \\u55AE\\u4F4D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 64);\n    i0.ɵɵelement(48, \"input\", 126);\n    i0.ɵɵelementEnd()()()()()()()()()()()();\n    i0.ɵɵelementStart(49, \"div\", 60)(50, \"div\", 61)(51, \"div\", 62)(52, \"label\", 127);\n    i0.ɵɵtext(53, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 64)(55, \"nb-form-field\", 96)(56, \"nb-select\", 128);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_76_Template_nb_select_ngModelChange_56_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(57, \"nb-option\", 16)(58, \"span\", 5);\n    i0.ɵɵelement(59, \"i\", 98);\n    i0.ɵɵtext(60, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"nb-option\", 16)(62, \"span\", 5);\n    i0.ɵɵelement(63, \"i\", 99);\n    i0.ɵɵtext(64, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(65, \"nb-card-footer\", 100)(66, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_66_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r19));\n    });\n    i0.ɵɵelement(67, \"i\", 102);\n    i0.ɵɵtext(68, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_69_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r19));\n    });\n    i0.ɵɵelement(70, \"i\", 129);\n    i0.ɵɵtext(71, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate);\n    i0.ɵɵadvance(26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction TemplateComponent_ng_template_78_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 152);\n    i0.ɵɵelement(1, \"i\", 153);\n    i0.ɵɵelementStart(2, \"span\", 83);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_div_1_tr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"i\", 160);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵelement(7, \"i\", 161);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r22 = ctx.$implicit;\n    const i_r23 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r23 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r22.CPart, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r22.CLocation || \"-\", \" \");\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 155)(1, \"table\", 156)(2, \"thead\")(3, \"tr\")(4, \"th\", 157);\n    i0.ɵɵtext(5, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 158);\n    i0.ɵɵtext(7, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 159);\n    i0.ɵɵtext(9, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"tbody\");\n    i0.ɵɵtemplate(11, TemplateComponent_ng_template_78_div_70_div_1_tr_11_Template, 9, 3, \"tr\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateDetailSpaces);\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \"\\u6B64\\u6A21\\u677F\\u5C1A\\u672A\\u5305\\u542B\\u4EFB\\u4F55\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TemplateComponent_ng_template_78_div_70_div_1_Template, 12, 1, \"div\", 154)(2, TemplateComponent_ng_template_78_div_70_div_2_Template, 3, 0, \"div\", 86);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length === 0);\n  }\n}\nfunction TemplateComponent_ng_template_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 52)(1, \"nb-card-header\", 53)(2, \"h5\", 54);\n    i0.ɵɵelement(3, \"i\", 130);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u660E\\u7D30 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_78_Template_button_click_5_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r20).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r21));\n    });\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 58)(8, \"div\", 131)(9, \"div\", 132)(10, \"h6\", 133);\n    i0.ɵɵelement(11, \"i\", 134);\n    i0.ɵɵtext(12, \"\\u57FA\\u672C\\u8CC7\\u8A0A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 135)(14, \"div\", 59)(15, \"div\", 9)(16, \"div\", 136)(17, \"label\", 137);\n    i0.ɵɵelement(18, \"i\", 138);\n    i0.ɵɵtext(19, \"\\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 139);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 9)(23, \"div\", 136)(24, \"label\", 137);\n    i0.ɵɵelement(25, \"i\", 140);\n    i0.ɵɵtext(26, \"\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 139)(28, \"span\", 40);\n    i0.ɵɵelement(29, \"i\", 141);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 9)(32, \"div\", 136)(33, \"label\", 137);\n    i0.ɵɵelement(34, \"i\", 142);\n    i0.ɵɵtext(35, \"\\u5EFA\\u7ACB\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"p\", 139);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 9)(40, \"div\", 136)(41, \"label\", 137);\n    i0.ɵɵelement(42, \"i\", 143);\n    i0.ɵɵtext(43, \"\\u5EFA\\u7ACB\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 139);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 9)(47, \"div\", 136)(48, \"label\", 137);\n    i0.ɵɵelement(49, \"i\", 144);\n    i0.ɵɵtext(50, \"\\u66F4\\u65B0\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 139);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 136)(56, \"label\", 137);\n    i0.ɵɵelement(57, \"i\", 145);\n    i0.ɵɵtext(58, \"\\u66F4\\u65B0\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\", 139);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(61, \"div\", 146)(62, \"div\", 147)(63, \"h6\", 133);\n    i0.ɵɵelement(64, \"i\", 148);\n    i0.ɵɵtext(65, \"\\u5305\\u542B\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 149);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 135);\n    i0.ɵɵtemplate(69, TemplateComponent_ng_template_78_div_69_Template, 4, 0, \"div\", 150)(70, TemplateComponent_ng_template_78_div_70_Template, 3, 2, \"div\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"nb-card-footer\", 100)(72, \"button\", 151);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_78_Template_button_click_72_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r20).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r21));\n    });\n    i0.ɵɵelement(73, \"i\", 102);\n    i0.ɵɵtext(74, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CTemplateName) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"fas fa-check-circle\" : \"fas fa-times-circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(38, 12, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(53, 15, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r2.templateDetailSpaces.length, \" \\u500B\\u7A7A\\u9593\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingTemplateDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoadingTemplateDetail);\n  }\n}\nexport class TemplateComponent extends BaseComponent {\n  constructor(allow, dialogService, _templateService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._templateService = _templateService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.Math = Math; // 讓模板可以使用 Math 函數\n    this.EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\n    this.EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    // 模板相關屬性\n    this.templateList = [];\n    this.templateDetail = {};\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.searchTemplateType = null;\n    // 空間選擇相關屬性\n    this.availableSpaces = [];\n    this.selectedSpacesForTemplate = [];\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.spacePageSize = 10;\n    this.spaceTotalRecords = 0;\n    this.allSpacesSelected = false;\n    // 項目選擇相關屬性\n    this.availableItems = [];\n    this.selectedItemsForTemplate = [];\n    this.itemSearchKeyword = '';\n    this.itemSearchLocation = '';\n    this.itemPageIndex = 1;\n    this.itemPageSize = 10;\n    this.itemTotalRecords = 0;\n    this.allItemsSelected = false;\n    // 模板明細相關屬性\n    this.selectedTemplateDetail = null;\n    this.templateDetailSpaces = [];\n    this.isLoadingTemplateDetail = false;\n  }\n  ngOnInit() {\n    this.loadTemplateList();\n    this.loadAvailableSpaces();\n  }\n  // 載入模板列表\n  loadTemplateList() {\n    const request = {\n      CTemplateName: this.searchKeyword || null,\n      CStatus: this.searchStatus,\n      CTemplateType: this.searchTemplateType,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this._templateService.apiTemplateGetTemplateListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.templateList = response.Entries?.map(item => ({\n          CTemplateId: item.CTemplateId,\n          CTemplateName: item.CTemplateName,\n          CTemplateType: item.CTemplateType,\n          // 新增模板類型\n          CCreateDt: item.CCreateDt,\n          CUpdateDt: item.CUpdateDt,\n          CCreator: item.CCreator,\n          CUpdator: item.CUpdator,\n          CStatus: item.CStatus\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板列表失敗');\n      }\n    })).subscribe();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    const request = {\n      CPart: this.spaceSearchKeyword || null,\n      CLocation: this.spaceSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.spacePageIndex,\n      PageSize: this.spacePageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.spaceTotalRecords = response.TotalItems || 0;\n        this.updateAllSpacesSelectedState();\n      }\n    })).subscribe();\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  // 空間搜尋功能\n  onSpaceSearch() {\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  onSpaceReset() {\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 分頁功能\n  pageChanged(page) {\n    this.pageIndex = page;\n    this.loadTemplateList();\n  }\n  spacePageChanged(page) {\n    this.spacePageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  // 模態框操作\n  openCreateModal(modal) {\n    this.templateDetail = {\n      CStatus: 1,\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\n    };\n    this.selectedSpacesForTemplate = [];\n    this.loadAvailableSpaces();\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  openEditModal(modal, template) {\n    this.templateDetail = {\n      CTemplateId: template.CTemplateId,\n      CTemplateName: template.CTemplateName,\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\n      CStatus: template.CStatus || 1\n    };\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onSubmit(ref) {\n    if (!this.validateTemplateForm()) {\n      return;\n    }\n    if (this.templateDetail.CTemplateId) {\n      this.updateTemplate(ref);\n    } else {\n      this.createTemplate(ref);\n    }\n  }\n  // 驗證表單\n  validateTemplateForm() {\n    if (!this.templateDetail.CTemplateName?.trim()) {\n      this.message.showErrorMSG('請輸入模板名稱');\n      return false;\n    }\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\n      this.message.showErrorMSG('請選擇模板類型');\n      return false;\n    }\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\n      this.message.showErrorMSG('請選擇模板狀態');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\n      return false;\n    }\n    return true;\n  }\n  // 建立模板\n  createTemplate(ref) {\n    const templateData = {\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0 && response.Entries) {\n        const templateId = parseInt(response.Entries, 10);\n        this.saveTemplateDetails(templateId, ref);\n      } else {\n        this.message.showErrorMSG(response.Message || '建立模板失敗');\n      }\n    })).subscribe();\n  }\n  // 更新模板\n  updateTemplate(ref) {\n    const templateData = {\n      CTemplateId: this.templateDetail.CTemplateId,\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.message.showSucessMSG('更新模板成功');\n        ref.close();\n        this.loadTemplateList();\n      } else {\n        this.message.showErrorMSG(response.Message || '更新模板失敗');\n      }\n    })).subscribe();\n  }\n  // 儲存模板詳細資料（關聯空間）\n  saveTemplateDetails(templateId, ref) {\n    // 目前 API 可能不支援模板詳細資料的保存，暫時跳過這個步驟\n    this.message.showSucessMSG('建立模板成功');\n    ref.close();\n    this.loadTemplateList();\n  }\n  // 刪除模板\n  deleteTemplate(template) {\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\n        body: {\n          CTemplateId: template.CTemplateId\n        }\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('刪除模板成功');\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '刪除模板失敗');\n        }\n      })).subscribe();\n    }\n  }\n  // 查看模板明細\n  viewTemplateDetail(template, modal) {\n    this.selectedTemplateDetail = template;\n    this.isLoadingTemplateDetail = true;\n    this.templateDetailSpaces = [];\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n    const request = {\n      templateId: template.CTemplateId\n    };\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      this.isLoadingTemplateDetail = false;\n      if (response.StatusCode === 0) {\n        this.templateDetailSpaces = response.Entries?.map(item => ({\n          CReleateId: item.CReleateId,\n          CPart: item.CPart,\n          CLocation: item.CLocation\n        })) || [];\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板明細失敗');\n      }\n    })).subscribe();\n  }\n  // 空間選擇相關方法\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n        this.selectedSpacesForTemplate.push({\n          ...space\n        });\n      }\n    } else {\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  toggleAllSpaces() {\n    this.allSpacesSelected = !this.allSpacesSelected;\n    this.availableSpaces.forEach(space => {\n      space.selected = this.allSpacesSelected;\n      if (this.allSpacesSelected) {\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else {\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n  }\n  removeSelectedSpace(space) {\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  updateAllSpacesSelectedState() {\n    this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  static {\n    this.ɵfac = function TemplateComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateComponent,\n      selectors: [[\"ngx-template\"]],\n      viewQuery: function TemplateComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.createModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateDetailModal = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 80,\n      vars: 15,\n      consts: [[\"createModal\", \"\"], [\"editModal\", \"\"], [\"templateDetailModal\", \"\"], [\"accent\", \"success\"], [1, \"alert\", \"alert-info\", \"mb-4\", 2, \"border-left\", \"4px solid #4a90e2\", \"background-color\", \"#f8f9ff\", \"border-radius\", \"6px\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-3\", 2, \"font-size\", \"1.2rem\"], [1, \"mb-0\", \"text-muted\", 2, \"font-size\", \"0.9rem\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"templateName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"col-3\"], [\"id\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\"], [\"for\", \"templateType\", 1, \"label\", \"col-3\"], [\"id\", \"templateType\", \"placeholder\", \"\\u9078\\u64C7\\u6A21\\u677F\\u985E\\u578B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"800px\"], [\"scope\", \"col\", 2, \"width\", \"120px\"], [\"scope\", \"col\", 2, \"width\", \"200px\"], [\"scope\", \"col\", 2, \"width\", \"100px\"], [\"scope\", \"col\", 2, \"width\", \"180px\"], [\"scope\", \"col\", 2, \"width\", \"140px\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"badge\", 3, \"ngClass\"], [1, \"table-actions\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-outline-warning btn-sm me-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-warning\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"5\", 1, \"text-muted\", \"py-4\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"border-bottom\", \"py-3\", \"px-4\"], [1, \"mb-0\", \"text-primary\", \"font-weight-bold\"], [1, \"fas\", \"fa-plus-circle\", \"me-2\", \"text-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-light\", \"btn-sm\", \"rounded-circle\", 2, \"width\", \"32px\", \"height\", \"32px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"px-4\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"form-group\", \"mb-4\"], [1, \"d-flex\", \"align-items-start\"], [\"for\", \"templateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"flex-grow-1\", \"ml-3\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"templateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"keydown.control.enter\", \"ngModel\"], [1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"template-type-tabs\"], [\"tabTitle\", \"\\u7A7A\\u9593\\u6A21\\u677F\", 3, \"click\", \"active\"], [1, \"mt-3\"], [1, \"row\", \"mb-3\"], [1, \"col-md-5\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"col-md-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"border\", \"rounded\", \"p-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [\"type\", \"checkbox\", \"id\", \"selectAll\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAll\", 1, \"mb-0\", \"font-weight-bold\"], [1, \"text-muted\"], [1, \"space-grid\"], [\"class\", \"space-item\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [\"class\", \"w-100 d-flex flex-column align-items-center mt-4\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [\"tabTitle\", \"\\u9805\\u76EE\\u6A21\\u677F\", 3, \"click\", \"active\"], [\"slot\", \"tabTitle\"], [\"for\", \"unitPrice\", 1, \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"number\", \"id\", \"unitPrice\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u50F9\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\"], [\"for\", \"unit\", 1, \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"unit\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u4F4D\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\"], [\"for\", \"templateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"w-full\"], [\"id\", \"templateStatus\", \"name\", \"templateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\", \"me-2\"], [1, \"d-flex\", \"justify-content-end\", \"border-top\", \"pt-3\", \"px-4\", \"pb-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"btn\", \"btn-outline-secondary\", \"me-3\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", \"background\", \"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [1, \"space-item\", 3, \"click\"], [1, \"space-card\"], [1, \"space-name\"], [1, \"space-location\"], [1, \"text-center\", \"text-muted\", \"py-4\"], [1, \"w-100\", \"d-flex\", \"flex-column\", \"align-items-center\", \"mt-4\"], [1, \"mb-2\", \"text-secondary\", 2, \"font-size\", \"0.95rem\"], [1, \"fw-bold\", \"text-primary\"], [1, \"mb-2\", \"font-weight-bold\"], [1, \"border\", \"rounded\", \"p-2\", 2, \"max-height\", \"150px\", \"overflow-y\", \"auto\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", 1, \"btn-close\", \"btn-close-white\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"fas\", \"fa-edit\", \"me-2\", \"text-warning\"], [\"for\", \"editTemplateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"editTemplateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"editTemplateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [1, \"alert\", \"alert-info\"], [\"for\", \"editUnitPrice\", 1, \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"number\", \"id\", \"editUnitPrice\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u50F9\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\"], [\"for\", \"editUnit\", 1, \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"editUnit\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u4F4D\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\"], [\"for\", \"editTemplateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"id\", \"editTemplateStatus\", \"name\", \"editTemplateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-save\", \"me-1\"], [1, \"fas\", \"fa-eye\", \"me-2\", \"text-info\"], [1, \"card\", \"mb-4\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"mb-0\", \"text-dark\", \"font-weight-bold\"], [1, \"fas\", \"fa-info-circle\", \"me-2\", \"text-primary\"], [1, \"card-body\"], [1, \"form-group\", \"mb-3\"], [1, \"font-weight-bold\", \"text-muted\"], [1, \"fas\", \"fa-tag\", \"me-2\", \"text-primary\"], [1, \"mb-0\"], [1, \"fas\", \"fa-toggle-on\", \"me-2\", \"text-success\"], [1, \"me-1\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-plus\", \"me-2\", \"text-warning\"], [1, \"fas\", \"fa-calendar-edit\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-edit\", \"me-2\", \"text-warning\"], [1, \"card\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-success\"], [1, \"badge\", \"badge-info\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"text-center\", \"py-4\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"me-2\", \"text-primary\", 2, \"font-size\", \"1.2rem\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-7\"], [\"scope\", \"col\", 1, \"col-4\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\", \"text-muted\"]],\n      template: function TemplateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementStart(7, \"div\")(8, \"p\", 7);\n          i0.ɵɵtext(9, \" \\u5728\\u6B64\\u9801\\u9762\\u60A8\\u53EF\\u4EE5\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u7684\\u5404\\u500B\\u6A21\\u677F\\u8CC7\\u8A0A\\uFF0C\\u5305\\u62EC\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u6A21\\u677F\\uFF0C\\u4EE5\\u53CA\\u8A2D\\u5B9A\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u72C0\\u614B\\u548C\\u5305\\u542B\\u7684\\u7A7A\\u9593\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"label\", 11);\n          i0.ɵɵtext(14, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"nb-form-field\", 12)(16, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_Template_input_keyup_enter_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10)(19, \"label\", 14);\n          i0.ɵɵtext(20, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"nb-form-field\", 12)(22, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchStatus, $event) || (ctx.searchStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(23, \"nb-option\", 16);\n          i0.ɵɵtext(24, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nb-option\", 16);\n          i0.ɵɵtext(26, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-option\", 16);\n          i0.ɵɵtext(28, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(29, \"div\", 9)(30, \"div\", 10)(31, \"label\", 17);\n          i0.ɵɵtext(32, \"\\u6A21\\u677F\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-form-field\", 12)(34, \"nb-select\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchTemplateType, $event) || (ctx.searchTemplateType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_34_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(35, \"nb-option\", 16);\n          i0.ɵɵtext(36, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"nb-option\", 16);\n          i0.ɵɵtext(38, \"\\u7A7A\\u9593\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"nb-option\", 16);\n          i0.ɵɵtext(40, \"\\u9805\\u76EE\\u6A21\\u677F\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(41, \"div\", 9);\n          i0.ɵɵelementStart(42, \"div\", 19)(43, \"div\", 20)(44, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_44_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReset());\n          });\n          i0.ɵɵelement(45, \"i\", 22);\n          i0.ɵɵtext(46, \"\\u91CD\\u7F6E \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_47_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelement(48, \"i\", 24);\n          i0.ɵɵtext(49, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(50, \"div\", 19)(51, \"div\", 25);\n          i0.ɵɵtemplate(52, TemplateComponent_button_52_Template, 3, 0, \"button\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"div\", 27)(54, \"table\", 28)(55, \"thead\")(56, \"tr\")(57, \"th\", 29);\n          i0.ɵɵtext(58, \"\\u6A21\\u677F\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\", 30);\n          i0.ɵɵtext(60, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"th\", 31);\n          i0.ɵɵtext(62, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\", 32);\n          i0.ɵɵtext(64, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\", 29);\n          i0.ɵɵtext(66, \"\\u5EFA\\u7ACB\\u8005\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\", 33);\n          i0.ɵɵtext(68, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(69, \"tbody\");\n          i0.ɵɵtemplate(70, TemplateComponent_tr_70_Template, 19, 11, \"tr\", 34)(71, TemplateComponent_tr_71_Template, 4, 0, \"tr\", 35);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(72, \"nb-card-footer\", 36)(73, \"ngx-pagination\", 37);\n          i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_73_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_73_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(74, TemplateComponent_ng_template_74_Template, 90, 16, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(76, TemplateComponent_ng_template_76_Template, 72, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(78, TemplateComponent_ng_template_78_Template, 75, 18, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTemplateType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.templateList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.templateList.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, i6.DatePipe, SharedModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbTabsetComponent, i2.NbTabComponent, i2.NbFormFieldComponent, i8.BreadcrumbComponent, i9.PaginationComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0;\\n  border-bottom-left-radius: 0;\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--color-fg-heading);\\n}\\n\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.badge.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n.badge.badge-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.required-field[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: #dc3545;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n\\n.btn-close-white[_ngcontent-%COMP%] {\\n  filter: invert(1) grayscale(100%) brightness(200%);\\n}\\n\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset {\\n  border-bottom: 2px solid #f1f3f4;\\n  margin-bottom: 0;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab {\\n  padding: 0;\\n  margin-right: 8px;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link {\\n  padding: 14px 24px;\\n  border: none;\\n  border-radius: 8px 8px 0 0;\\n  background-color: transparent;\\n  color: #6c757d;\\n  font-weight: 500;\\n  font-size: 0.95rem;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link:hover {\\n  background-color: #f8f9fa;\\n  color: #495057;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link.active {\\n  background-color: #007bff;\\n  color: #fff;\\n  font-weight: 600;\\n  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link.active::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background-color: #007bff;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab-content {\\n  padding: 0;\\n  border: none;\\n  background: transparent;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab-content .tab-pane.active {\\n  display: block;\\n}\\n\\n.space-grid[_ngcontent-%COMP%] {\\n  display: grid !important;\\n  grid-template-columns: repeat(5, 1fr);\\n  gap: 16px;\\n  margin-bottom: 20px;\\n  width: 100%;\\n  border: 1px dashed #ccc;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: block;\\n  width: 100%;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border: 2px solid #e9ecef !important;\\n  border-radius: 12px;\\n  background-color: #fff !important;\\n  transition: all 0.3s ease;\\n  min-height: 80px;\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50 !important;\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n  margin-bottom: 4px;\\n  text-align: center;\\n  display: block;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d !important;\\n  line-height: 1.3;\\n  font-weight: 400;\\n  text-align: center;\\n  display: block;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]:hover   .space-card[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background-color: #f8f9ff;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  color: #0056b3;\\n  font-weight: 700;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]::after {\\n  content: \\\"\\u2713\\\";\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  width: 20px;\\n  height: 20px;\\n  background-color: #007bff;\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: bold;\\n}\\n\\n@media (max-width: 1200px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(4, 1fr);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 8px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "CommonModule", "SharedModule", "tap", "EnumTemplateType", "EnumTemplateTypeHelper", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateComponent_button_52_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "createModal_r4", "ɵɵreference", "ɵɵresetView", "openCreateModal", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "TemplateComponent_tr_70_button_17_Template_button_click_0_listener", "_r8", "template_r6", "$implicit", "editModal_r9", "openEditModal", "TemplateComponent_tr_70_button_18_Template_button_click_0_listener", "_r10", "deleteTemplate", "TemplateComponent_tr_70_Template_button_click_14_listener", "_r5", "templateDetailModal_r7", "viewTemplateDetail", "ɵɵtemplate", "TemplateComponent_tr_70_button_17_Template", "TemplateComponent_tr_70_button_18_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "CTemplateType", "ɵɵtextInterpolate", "CTemplateName", "ɵɵproperty", "CStatus", "ɵɵpipeBind2", "CCreateDt", "CCreator", "isUpdate", "isDelete", "TemplateComponent_ng_template_74_div_44_Template_div_click_0_listener", "space_r14", "_r13", "toggleSpaceSelection", "ɵɵclassProp", "selected", "<PERSON>art", "CLocation", "ɵɵtwoWayListener", "TemplateComponent_ng_template_74_div_46_Template_ngx_pagination_PageChange_6_listener", "$event", "_r15", "ɵɵtwoWayBindingSet", "spacePageIndex", "spacePageChanged", "spaceTotalRecords", "ɵɵtwoWayProperty", "spacePageSize", "TemplateComponent_ng_template_74_div_47_span_4_Template_button_click_2_listener", "space_r17", "_r16", "removeSelectedSpace", "TemplateComponent_ng_template_74_div_47_span_4_Template", "selectedSpacesForTemplate", "length", "TemplateComponent_ng_template_74_Template_button_click_5_listener", "ref_r12", "_r11", "dialogRef", "onClose", "TemplateComponent_ng_template_74_Template_input_ngModelChange_15_listener", "templateDetail", "TemplateComponent_ng_template_74_Template_input_keydown_control_enter_15_listener", "onSubmit", "TemplateComponent_ng_template_74_Template_nb_tab_click_23_listener", "SpaceTemplate", "TemplateComponent_ng_template_74_Template_input_ngModelChange_27_listener", "spaceSearchKeyword", "TemplateComponent_ng_template_74_Template_input_keyup_enter_27_listener", "onSpaceSearch", "TemplateComponent_ng_template_74_Template_input_ngModelChange_29_listener", "spaceSearchLocation", "TemplateComponent_ng_template_74_Template_input_keyup_enter_29_listener", "TemplateComponent_ng_template_74_Template_button_click_31_listener", "onSpaceReset", "TemplateComponent_ng_template_74_Template_button_click_33_listener", "TemplateComponent_ng_template_74_Template_input_change_38_listener", "toggleAllSpaces", "TemplateComponent_ng_template_74_div_44_Template", "TemplateComponent_ng_template_74_div_45_Template", "TemplateComponent_ng_template_74_div_46_Template", "TemplateComponent_ng_template_74_div_47_Template", "TemplateComponent_ng_template_74_Template_nb_tab_click_48_listener", "ItemTemplate", "TemplateComponent_ng_template_74_Template_nb_select_ngModelChange_74_listener", "TemplateComponent_ng_template_74_Template_button_click_84_listener", "TemplateComponent_ng_template_74_Template_button_click_87_listener", "allSpacesSelected", "ɵɵtextInterpolate3", "Math", "ceil", "availableSpaces", "TemplateComponent_ng_template_76_Template_button_click_5_listener", "ref_r19", "_r18", "TemplateComponent_ng_template_76_Template_input_ngModelChange_15_listener", "TemplateComponent_ng_template_76_Template_nb_tab_click_23_listener", "TemplateComponent_ng_template_76_Template_nb_tab_click_30_listener", "TemplateComponent_ng_template_76_Template_nb_select_ngModelChange_56_listener", "TemplateComponent_ng_template_76_Template_button_click_66_listener", "TemplateComponent_ng_template_76_Template_button_click_69_listener", "i_r23", "space_r22", "TemplateComponent_ng_template_78_div_70_div_1_tr_11_Template", "templateDetailSpaces", "TemplateComponent_ng_template_78_div_70_div_1_Template", "TemplateComponent_ng_template_78_div_70_div_2_Template", "TemplateComponent_ng_template_78_Template_button_click_5_listener", "ref_r21", "_r20", "TemplateComponent_ng_template_78_div_69_Template", "TemplateComponent_ng_template_78_div_70_Template", "TemplateComponent_ng_template_78_Template_button_click_72_listener", "selectedTemplateDetail", "ɵɵclassMap", "CUpdateDt", "CUpdator", "isLoadingTemplateDetail", "TemplateComponent", "constructor", "allow", "dialogService", "_templateService", "_spaceService", "message", "valid", "pageFirst", "pageSize", "pageIndex", "totalRecords", "templateList", "searchKeyword", "searchStatus", "searchTemplateType", "availableItems", "selectedItemsForTemplate", "itemSearchKeyword", "itemSearchLocation", "itemPageIndex", "itemPageSize", "itemTotalRecords", "allItemsSelected", "ngOnInit", "loadTemplateList", "loadAvailableSpaces", "request", "PageIndex", "PageSize", "apiTemplateGetTemplateListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "TotalItems", "showErrorMSG", "Message", "subscribe", "apiSpaceGetSpaceListPost$Json", "CSpaceID", "some", "s", "updateAllSpacesSelectedState", "onSearch", "onReset", "pageChanged", "page", "modal", "open", "context", "autoFocus", "template", "ref", "close", "validateTemplateForm", "updateTemplate", "createTemplate", "trim", "undefined", "templateData", "apiTemplateSaveTemplatePost$Json", "templateId", "parseInt", "saveTemplateDetails", "showSucessMSG", "confirm", "apiTemplateDeleteTemplatePost$Json", "apiTemplateGetTemplateDetailByIdPost$Json", "CReleateId", "space", "push", "filter", "for<PERSON>ach", "availableSpace", "find", "every", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "TemplateService", "SpaceService", "i4", "MessageService", "i5", "ValidationHelper", "selectors", "viewQuery", "TemplateComponent_Query", "rf", "ctx", "TemplateComponent_Template_input_ngModelChange_16_listener", "_r1", "TemplateComponent_Template_input_keyup_enter_16_listener", "TemplateComponent_Template_nb_select_ngModelChange_22_listener", "TemplateComponent_Template_nb_select_selectedChange_22_listener", "TemplateComponent_Template_nb_select_ngModelChange_34_listener", "TemplateComponent_Template_nb_select_selected<PERSON><PERSON>e_34_listener", "TemplateComponent_Template_button_click_44_listener", "TemplateComponent_Template_button_click_47_listener", "TemplateComponent_button_52_Template", "TemplateComponent_tr_70_Template", "TemplateComponent_tr_71_Template", "TemplateComponent_Template_ngx_pagination_PageChange_73_listener", "TemplateComponent_ng_template_74_Template", "ɵɵtemplateRefExtractor", "TemplateComponent_ng_template_76_Template", "TemplateComponent_ng_template_78_Template", "isCreate", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbTabsetComponent", "NbTabComponent", "NbFormFieldComponent", "i8", "BreadcrumbComponent", "i9", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.html"], "sourcesContent": ["import { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { TemplateService, SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { tap } from 'rxjs';\r\nimport {\r\n  SaveTemplateArgs,\r\n  GetTemplateDetailByIdArgs,\r\n  TemplateDetailItem,\r\n  GetSpaceListResponse\r\n} from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\nexport interface TemplateItem {\r\n  CTemplateId: number;\r\n  CTemplateName: string;\r\n  CTemplateType?: number;\r\n  CCreateDt: string;\r\n  CUpdateDt: string;\r\n  CCreator?: string | null;\r\n  CUpdator?: string | null;\r\n  CStatus?: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpacePickListItem {\r\n  CSpaceID: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n  selected?: boolean;\r\n}\r\n\r\n// 僅用於模板明細空間顯示\r\nexport interface TemplateDetailSpaceItem {\r\n  CReleateId: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n}\r\n\r\n// 項目模板選擇項目介面\r\nexport interface ItemPickListItem {\r\n  CRequirementID: number;\r\n  CRequirement: string;\r\n  CLocation?: string | null;\r\n  CUnitPrice?: number;\r\n  CUnit?: string;\r\n  selected?: boolean;\r\n}\r\n\r\n// 項目模板詳細項目介面\r\nexport interface TemplateDetailItemItem {\r\n  CReleateId: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n  CUnitPrice?: number;\r\n  CUnit?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-template',\r\n  templateUrl: './template.component.html',\r\n  styleUrls: ['./template.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent\r\n  ],\r\n})\r\nexport class TemplateComponent extends BaseComponent implements OnInit {\r\n  Math = Math; // 讓模板可以使用 Math 函數\r\n  EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\r\n  EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\r\n\r\n  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;\r\n  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;\r\n  @ViewChild('templateDetailModal', { static: false }) templateDetailModal!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _templateService: TemplateService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow);\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  // 模板相關屬性\r\n  templateList: TemplateItem[] = [];\r\n  templateDetail: SaveTemplateArgs = {};\r\n  searchKeyword: string = '';\r\n  searchStatus: number | null = null;\r\n  searchTemplateType: number | null = null;\r\n\r\n  // 空間選擇相關屬性\r\n  availableSpaces: SpacePickListItem[] = [];\r\n  selectedSpacesForTemplate: SpacePickListItem[] = [];\r\n  spaceSearchKeyword: string = '';\r\n  spaceSearchLocation: string = '';\r\n  spacePageIndex = 1;\r\n  spacePageSize = 10;\r\n  spaceTotalRecords = 0;\r\n  allSpacesSelected = false;\r\n\r\n  // 項目選擇相關屬性\r\n  availableItems: ItemPickListItem[] = [];\r\n  selectedItemsForTemplate: ItemPickListItem[] = [];\r\n  itemSearchKeyword: string = '';\r\n  itemSearchLocation: string = '';\r\n  itemPageIndex = 1;\r\n  itemPageSize = 10;\r\n  itemTotalRecords = 0;\r\n  allItemsSelected = false;\r\n\r\n  // 模板明細相關屬性\r\n  selectedTemplateDetail: TemplateItem | null = null;\r\n  templateDetailSpaces: TemplateDetailSpaceItem[] = [];\r\n  isLoadingTemplateDetail = false;\r\n\r\n  override ngOnInit(): void {\r\n    this.loadTemplateList();\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 載入模板列表\r\n  loadTemplateList(): void {\r\n    const request = {\r\n      CTemplateName: this.searchKeyword || null,\r\n      CStatus: this.searchStatus,\r\n      CTemplateType: this.searchTemplateType,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.templateList = response.Entries?.map(item => ({\r\n            CTemplateId: item.CTemplateId!,\r\n            CTemplateName: item.CTemplateName!,\r\n            CTemplateType: item.CTemplateType, // 新增模板類型\r\n            CCreateDt: item.CCreateDt!,\r\n            CUpdateDt: item.CUpdateDt!,\r\n            CCreator: item.CCreator,\r\n            CUpdator: item.CUpdator,\r\n            CStatus: item.CStatus\r\n          })) || [];\r\n          this.totalRecords = response.TotalItems || 0;\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板列表失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 載入可用空間列表\r\n  loadAvailableSpaces(): void {\r\n    const request = {\r\n      CPart: this.spaceSearchKeyword || null,\r\n      CLocation: this.spaceSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.spacePageIndex,\r\n      PageSize: this.spacePageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableSpaces = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\r\n          })) || [];\r\n          this.spaceTotalRecords = response.TotalItems || 0;\r\n          this.updateAllSpacesSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 搜尋功能\r\n  onSearch(): void {\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  onReset(): void {\r\n    this.searchKeyword = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 空間搜尋功能\r\n  onSpaceSearch(): void {\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  onSpaceReset(): void {\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 分頁功能\r\n  pageChanged(page: number): void {\r\n    this.pageIndex = page;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  spacePageChanged(page: number): void {\r\n    this.spacePageIndex = page;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 模態框操作\r\n  openCreateModal(modal: TemplateRef<any>): void {\r\n    this.templateDetail = {\r\n      CStatus: 1,\r\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\r\n    };\r\n    this.selectedSpacesForTemplate = [];\r\n    this.loadAvailableSpaces();\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  openEditModal(modal: TemplateRef<any>, template: TemplateItem): void {\r\n    this.templateDetail = {\r\n      CTemplateId: template.CTemplateId,\r\n      CTemplateName: template.CTemplateName,\r\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\r\n      CStatus: template.CStatus || 1\r\n    };\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  onClose(ref: any): void {\r\n    ref.close();\r\n  }\r\n\r\n  onSubmit(ref: any): void {\r\n    if (!this.validateTemplateForm()) {\r\n      return;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateId) {\r\n      this.updateTemplate(ref);\r\n    } else {\r\n      this.createTemplate(ref);\r\n    }\r\n  }\r\n\r\n  // 驗證表單\r\n  validateTemplateForm(): boolean {\r\n    if (!this.templateDetail.CTemplateName?.trim()) {\r\n      this.message.showErrorMSG('請輸入模板名稱');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\r\n      this.message.showErrorMSG('請選擇模板類型');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\r\n      this.message.showErrorMSG('請選擇模板狀態');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\r\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 建立模板\r\n  createTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          const templateId = parseInt(response.Entries, 10);\r\n          this.saveTemplateDetails(templateId, ref);\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '建立模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 更新模板\r\n  updateTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateId: this.templateDetail.CTemplateId,\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.message.showSucessMSG('更新模板成功');\r\n          ref.close();\r\n          this.loadTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '更新模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 儲存模板詳細資料（關聯空間）\r\n  saveTemplateDetails(templateId: number, ref: any): void {\r\n    // 目前 API 可能不支援模板詳細資料的保存，暫時跳過這個步驟\r\n    this.message.showSucessMSG('建立模板成功');\r\n    ref.close();\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 刪除模板\r\n  deleteTemplate(template: TemplateItem): void {\r\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\r\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\r\n        body: { CTemplateId: template.CTemplateId }\r\n      }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('刪除模板成功');\r\n            this.loadTemplateList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '刪除模板失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    }\r\n  }\r\n\r\n  // 查看模板明細\r\n  viewTemplateDetail(template: TemplateItem, modal: TemplateRef<any>): void {\r\n    this.selectedTemplateDetail = template;\r\n    this.isLoadingTemplateDetail = true;\r\n    this.templateDetailSpaces = [];\r\n\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n\r\n    const request: GetTemplateDetailByIdArgs = {\r\n      templateId: template.CTemplateId\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        this.isLoadingTemplateDetail = false;\r\n        if (response.StatusCode === 0) {\r\n          this.templateDetailSpaces = response.Entries?.map(item => ({\r\n            CReleateId: item.CReleateId!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation\r\n          })) || [];\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板明細失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間選擇相關方法\r\n  toggleSpaceSelection(space: SpacePickListItem): void {\r\n    space.selected = !space.selected;\r\n\r\n    if (space.selected) {\r\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n        this.selectedSpacesForTemplate.push({ ...space });\r\n      }\r\n    } else {\r\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  toggleAllSpaces(): void {\r\n    this.allSpacesSelected = !this.allSpacesSelected;\r\n\r\n    this.availableSpaces.forEach(space => {\r\n      space.selected = this.allSpacesSelected;\r\n      if (this.allSpacesSelected) {\r\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n          this.selectedSpacesForTemplate.push({ ...space });\r\n        }\r\n      } else {\r\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedSpace(space: SpacePickListItem): void {\r\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n\r\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\r\n    if (availableSpace) {\r\n      availableSpace.selected = false;\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  updateAllSpacesSelectedState(): void {\r\n    this.allSpacesSelected = this.availableSpaces.length > 0 &&\r\n      this.availableSpaces.every(space => space.selected);\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"alert alert-info mb-4\"\r\n      style=\"border-left: 4px solid #4a90e2; background-color: #f8f9ff; border-radius: 6px;\">\r\n      <div class=\"d-flex align-items-center\">\r\n        <i class=\"fas fa-info-circle text-primary me-3\" style=\"font-size: 1.2rem;\"></i>\r\n        <div>\r\n          <p class=\"mb-0 text-muted\" style=\"font-size: 0.9rem;\">\r\n            在此頁面您可以管理系統中的各個模板資訊，包括新增、編輯、刪除模板，以及設定模板名稱、狀態和包含的空間等。\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜尋條件區域 -->\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"templateName\" class=\"label col-3\">模板名稱</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"templateName\" nbInput class=\"w-full\" placeholder=\"搜尋模板名稱...\"\r\n              [(ngModel)]=\"searchKeyword\" (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"status\" class=\"label col-3\">狀態</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"status\" placeholder=\"選擇狀態...\" [(ngModel)]=\"searchStatus\" (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">啟用</nb-option>\r\n              <nb-option [value]=\"0\">停用</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"templateType\" class=\"label col-3\">模板類型</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"templateType\" placeholder=\"選擇模板類型...\" [(ngModel)]=\"searchTemplateType\"\r\n              (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">空間模板</nb-option>\r\n              <nb-option [value]=\"2\">項目模板</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢和重置按鈕 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-outline-secondary btn-sm me-2\" (click)=\"onReset()\">\r\n            <i class=\"fas fa-undo me-1\"></i>重置\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search me-1\"></i>查詢\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openCreateModal(createModal)\">\r\n            <i class=\"fas fa-plus me-1\"></i>新增模板\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模板列表表格 -->\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table\" style=\"min-width: 800px;\">\r\n        <thead>\r\n          <tr>\r\n            <th scope=\"col\" style=\"width: 120px;\">模板類型</th>\r\n            <th scope=\"col\" style=\"width: 200px;\">模板名稱</th>\r\n            <th scope=\"col\" style=\"width: 100px;\">狀態</th>\r\n            <th scope=\"col\" style=\"width: 180px;\">建立時間</th>\r\n            <th scope=\"col\" style=\"width: 120px;\">建立者</th>\r\n            <th scope=\"col\" style=\"width: 140px;\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let template of templateList\">\r\n            <td>\r\n              {{ template.CTemplateType === 1 ? '空間模板' : (template.CTemplateType === 2 ? '項目模板' : '-') }}\r\n            </td>\r\n            <td>{{ template.CTemplateName }}</td>\r\n            <td>\r\n              <span class=\"badge\" [ngClass]=\"template.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n              </span>\r\n            </td>\r\n            <td>{{ template.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>\r\n            <td>{{ template.CCreator || '-' }}</td>\r\n            <td class=\"table-actions\">\r\n              <button class=\"btn btn-outline-info btn-sm me-1\"\r\n                (click)=\"viewTemplateDetail(template, templateDetailModal)\">\r\n                <i class=\"fas fa-eye\"></i>查看\r\n              </button>\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-warning btn-sm me-1\"\r\n                (click)=\"openEditModal(editModal, template)\">\r\n                <i class=\"fas fa-edit\"></i>編輯\r\n              </button>\r\n              <button *ngIf=\"isDelete\" class=\"btn btn-outline-danger btn-sm\" (click)=\"deleteTemplate(template)\">\r\n                <i class=\"fas fa-trash\"></i>刪除\r\n              </button>\r\n            </td>\r\n          </tr>\r\n          <tr *ngIf=\"templateList.length === 0\">\r\n            <td colspan=\"5\" class=\"text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>目前沒有任何模板\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\" [CollectionSize]=\"totalRecords\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<!-- 新增模板模態框 -->\r\n<ng-template #createModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-plus-circle me-2 text-success\"></i>新增模板\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"templateName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"templateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                  [(ngModel)]=\"templateDetail.CTemplateName\" name=\"templateName\" (keydown.control.enter)=\"onSubmit(ref)\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板類型頁簽 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板類型\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-tabset class=\"template-type-tabs\">\r\n                  <nb-tab tabTitle=\"空間模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.SpaceTemplate\">\r\n                    <!-- 空間選擇區域 -->\r\n                    <div class=\"mt-3\">\r\n                      <!-- 搜尋區域 -->\r\n                      <div class=\"row mb-3\">\r\n                        <div class=\"col-md-5\">\r\n                          <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋項目名稱...\"\r\n                            [(ngModel)]=\"spaceSearchKeyword\" (keyup.enter)=\"onSpaceSearch()\"\r\n                            style=\"height: 32px; border-radius: 4px;\" />\r\n                        </div>\r\n                        <div class=\"col-md-5\">\r\n                          <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋所屬區域...\"\r\n                            [(ngModel)]=\"spaceSearchLocation\" (keyup.enter)=\"onSpaceSearch()\"\r\n                            style=\"height: 32px; border-radius: 4px;\" />\r\n                        </div>\r\n                        <div class=\"col-md-2\">\r\n                          <button class=\"btn btn-sm btn-outline-secondary me-1\" (click)=\"onSpaceReset()\">\r\n                            <i class=\"fas fa-undo\"></i>\r\n                          </button>\r\n                          <button class=\"btn btn-sm btn-secondary\" (click)=\"onSpaceSearch()\">\r\n                            <i class=\"fas fa-search\"></i>\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 可選空間列表 -->\r\n                      <div class=\"border rounded p-3\" style=\"background-color: #f8f9fa;\">\r\n                        <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n                          <div class=\"d-flex align-items-center\">\r\n                            <input type=\"checkbox\" id=\"selectAll\" [checked]=\"allSpacesSelected\"\r\n                              (change)=\"toggleAllSpaces()\" class=\"me-2\">\r\n                            <label for=\"selectAll\" class=\"mb-0 font-weight-bold\">全選當頁空間</label>\r\n                          </div>\r\n                          <small class=\"text-muted\">\r\n                            共 {{ spaceTotalRecords }} 筆，第 {{ spacePageIndex }} / {{ Math.ceil(spaceTotalRecords /\r\n                            spacePageSize) }} 頁\r\n                          </small>\r\n                        </div>\r\n\r\n                        <!-- 空間項目網格 -->\r\n                        <div class=\"space-grid\">\r\n                          <div class=\"space-item\" *ngFor=\"let space of availableSpaces\"\r\n                            [class.selected]=\"space.selected\" (click)=\"toggleSpaceSelection(space)\">\r\n                            <div class=\"space-card\">\r\n                              <div class=\"space-name\">{{ space.CPart }}</div>\r\n                              <div class=\"space-location\">{{ space.CLocation || '-' }}</div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- 空間列表為空時的提示 -->\r\n                        <div *ngIf=\"availableSpaces.length === 0\" class=\"text-center text-muted py-4\">\r\n                          <i class=\"fas fa-info-circle me-2\"></i>沒有符合條件的空間\r\n                        </div>\r\n\r\n                        <!-- 分頁 -->\r\n                        <div class=\"w-100 d-flex flex-column align-items-center mt-4\"\r\n                          *ngIf=\"spaceTotalRecords > spacePageSize\">\r\n                          <div class=\"mb-2 text-secondary\" style=\"font-size: 0.95rem;\">\r\n                            共 <span class=\"fw-bold text-primary\">{{ spaceTotalRecords }}</span> 筆資料\r\n                          </div>\r\n                          <ngx-pagination [(Page)]=\"spacePageIndex\" [PageSize]=\"spacePageSize\"\r\n                            [CollectionSize]=\"spaceTotalRecords\" (PageChange)=\"spacePageChanged($event)\">\r\n                          </ngx-pagination>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 已選空間 -->\r\n                      <div class=\"mt-3\" *ngIf=\"selectedSpacesForTemplate.length > 0\">\r\n                        <label class=\"mb-2 font-weight-bold\">已選擇的空間 ({{ selectedSpacesForTemplate.length }})</label>\r\n                        <div class=\"border rounded p-2\" style=\"max-height: 150px; overflow-y: auto;\">\r\n                          <span class=\"badge badge-primary me-1 mb-1\" *ngFor=\"let space of selectedSpacesForTemplate\">\r\n                            {{ space.CPart }}\r\n                            <button type=\"button\" class=\"btn-close btn-close-white ms-1\"\r\n                              (click)=\"removeSelectedSpace(space)\" style=\"font-size: 0.7rem;\"></button>\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                  <nb-tab tabTitle=\"項目模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.ItemTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.ItemTemplate\">\r\n                    <span slot=\"tabTitle\">項目模板</span>\r\n\r\n                    <!-- 項目模板專用欄位 -->\r\n                    <div class=\"mt-3\">\r\n                      <div class=\"row\">\r\n                        <!-- 單價 -->\r\n                        <div class=\"col-md-6\">\r\n                          <div class=\"form-group mb-4\">\r\n                            <div class=\"d-flex align-items-start\">\r\n                              <label for=\"unitPrice\" class=\"mb-0\"\r\n                                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                                單價\r\n                              </label>\r\n                              <div class=\"flex-grow-1 ml-3\">\r\n                                <input type=\"number\" id=\"unitPrice\" class=\"form-control\" nbInput placeholder=\"請輸入單價\"\r\n                                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- 單位 -->\r\n                        <div class=\"col-md-6\">\r\n                          <div class=\"form-group mb-4\">\r\n                            <div class=\"d-flex align-items-start\">\r\n                              <label for=\"unit\" class=\"mb-0\"\r\n                                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                                單位\r\n                              </label>\r\n                              <div class=\"flex-grow-1 ml-3\">\r\n                                <input type=\"text\" id=\"unit\" class=\"form-control\" nbInput placeholder=\"請輸入單位\"\r\n                                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                </nb-tabset>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板狀態 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"templateStatus\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"templateStatus\" [(ngModel)]=\"templateDetail.CStatus\" name=\"templateStatus\"\r\n                    placeholder=\"選擇狀態\" style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-check me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 編輯模板模態框 -->\r\n<ng-template #editModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-edit me-2 text-warning\"></i>編輯模板\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"editTemplateName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"editTemplateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                  [(ngModel)]=\"templateDetail.CTemplateName\" name=\"editTemplateName\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板類型頁簽 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板類型\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-tabset class=\"template-type-tabs\">\r\n                  <nb-tab tabTitle=\"空間模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.SpaceTemplate\">\r\n                    <span slot=\"tabTitle\">空間模板</span>\r\n\r\n                    <!-- 編輯模式下空間模板說明 -->\r\n                    <div class=\"mt-3\">\r\n                      <div class=\"alert alert-info\">\r\n                        <i class=\"fas fa-info-circle me-2\"></i>\r\n                        編輯模式下，空間配置請在模板詳情中進行管理。\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                  <nb-tab tabTitle=\"項目模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.ItemTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.ItemTemplate\">\r\n                    <span slot=\"tabTitle\">項目模板</span>\r\n\r\n                    <!-- 項目模板專用欄位 -->\r\n                    <div class=\"mt-3\">\r\n                      <div class=\"row\">\r\n                        <!-- 單價 -->\r\n                        <div class=\"col-md-6\">\r\n                          <div class=\"form-group mb-4\">\r\n                            <div class=\"d-flex align-items-start\">\r\n                              <label for=\"editUnitPrice\" class=\"mb-0\"\r\n                                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                                單價\r\n                              </label>\r\n                              <div class=\"flex-grow-1 ml-3\">\r\n                                <input type=\"number\" id=\"editUnitPrice\" class=\"form-control\" nbInput placeholder=\"請輸入單價\"\r\n                                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- 單位 -->\r\n                        <div class=\"col-md-6\">\r\n                          <div class=\"form-group mb-4\">\r\n                            <div class=\"d-flex align-items-start\">\r\n                              <label for=\"editUnit\" class=\"mb-0\"\r\n                                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                                單位\r\n                              </label>\r\n                              <div class=\"flex-grow-1 ml-3\">\r\n                                <input type=\"text\" id=\"editUnit\" class=\"form-control\" nbInput placeholder=\"請輸入單位\"\r\n                                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                </nb-tabset>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板狀態 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"editTemplateStatus\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"editTemplateStatus\" [(ngModel)]=\"templateDetail.CStatus\" name=\"editTemplateStatus\"\r\n                    placeholder=\"選擇狀態\" style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-save me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 查看模板明細模態框 -->\r\n<ng-template #templateDetailModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-eye me-2 text-info\"></i>模板明細\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <!-- 模板基本資訊 -->\r\n      <div class=\"card mb-4\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header\" style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-info-circle me-2 text-primary\"></i>基本資訊\r\n          </h6>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div class=\"row\">\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-tag me-2 text-primary\"></i>模板名稱\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CTemplateName || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-toggle-on me-2 text-success\"></i>狀態\r\n                </label>\r\n                <p class=\"mb-0\">\r\n                  <span class=\"badge\"\r\n                    [ngClass]=\"selectedTemplateDetail?.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                    <i [class]=\"selectedTemplateDetail?.CStatus === 1 ? 'fas fa-check-circle' : 'fas fa-times-circle'\"\r\n                      class=\"me-1\"></i>\r\n                    {{ selectedTemplateDetail?.CStatus === 1 ? '啟用' : '停用' }}\r\n                  </span>\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-plus me-2 text-info\"></i>建立時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CCreateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-plus me-2 text-warning\"></i>建立者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CCreator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-edit me-2 text-info\"></i>更新時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CUpdateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-edit me-2 text-warning\"></i>更新者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CUpdator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 包含的空間列表 -->\r\n      <div class=\"card\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header d-flex justify-content-between align-items-center\"\r\n          style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-home me-2 text-success\"></i>包含的空間\r\n          </h6>\r\n          <span class=\"badge badge-info\">共 {{ templateDetailSpaces.length }} 個空間</span>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <!-- Loading 狀態 -->\r\n          <div *ngIf=\"isLoadingTemplateDetail\" class=\"text-center py-4\">\r\n            <i class=\"fas fa-spinner fa-spin me-2 text-primary\" style=\"font-size: 1.2rem;\"></i>\r\n            <span class=\"text-muted\">載入中...</span>\r\n          </div>\r\n\r\n          <!-- 空間列表 -->\r\n          <div *ngIf=\"!isLoadingTemplateDetail\">\r\n            <div class=\"table-responsive\" *ngIf=\"templateDetailSpaces.length > 0\">\r\n              <table class=\"table table-sm\">\r\n                <thead>\r\n                  <tr>\r\n                    <th scope=\"col\" class=\"col-1\">#</th>\r\n                    <th scope=\"col\" class=\"col-7\">項目名稱</th>\r\n                    <th scope=\"col\" class=\"col-4\">所屬區域</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let space of templateDetailSpaces; let i = index\">\r\n                    <td>{{ i + 1 }}</td>\r\n                    <td>\r\n                      <i class=\"fas fa-home me-2 text-muted\"></i>{{ space.CPart }}\r\n                    </td>\r\n                    <td>\r\n                      <i class=\"fas fa-map-marker-alt me-2 text-muted\"></i>{{ space.CLocation || '-' }}\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n\r\n            <!-- 沒有空間時的提示 -->\r\n            <div *ngIf=\"templateDetailSpaces.length === 0\" class=\"text-center text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>此模板尚未包含任何空間\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-secondary px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>關閉\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qCAAqC;AAEnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gCAAgC;AAO7D,SAASC,GAAG,QAAQ,MAAM;AAO1B,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;;;;;;;;;;;;;;;;;ICyDrFC,EAAA,CAAAC,cAAA,iBAAiG;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,cAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,eAAA,CAAAH,cAAA,CAA4B;IAAA,EAAC;IAC9FR,EAAA,CAAAY,SAAA,YAAgC;IAAAZ,EAAA,CAAAa,MAAA,gCAClC;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAoCLd,EAAA,CAAAC,cAAA,iBAC+C;IAA7CD,EAAA,CAAAE,UAAA,mBAAAa,mEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAY,YAAA,GAAAnB,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAc,aAAA,CAAAD,YAAA,EAAAF,WAAA,CAAkC;IAAA,EAAC;IAC5CjB,EAAA,CAAAY,SAAA,YAA2B;IAAAZ,EAAA,CAAAa,MAAA,oBAC7B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IACTd,EAAA,CAAAC,cAAA,iBAAkG;IAAnCD,EAAA,CAAAE,UAAA,mBAAAmB,mEAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,IAAA;MAAA,MAAAL,WAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiB,cAAA,CAAAN,WAAA,CAAwB;IAAA,EAAC;IAC/FjB,EAAA,CAAAY,SAAA,YAA4B;IAAAZ,EAAA,CAAAa,MAAA,oBAC9B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAtBXd,EADF,CAAAC,cAAA,SAA0C,SACpC;IACFD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEnCd,EADF,CAAAC,cAAA,SAAI,eAC2F;IAC3FD,EAAA,CAAAa,MAAA,GACF;IACFb,EADE,CAAAc,YAAA,EAAO,EACJ;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAmD;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC5Dd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,IAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAErCd,EADF,CAAAC,cAAA,cAA0B,kBAEsC;IAA5DD,EAAA,CAAAE,UAAA,mBAAAsB,0DAAA;MAAA,MAAAP,WAAA,GAAAjB,EAAA,CAAAI,aAAA,CAAAqB,GAAA,EAAAP,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAmB,sBAAA,GAAA1B,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAqB,kBAAA,CAAAV,WAAA,EAAAS,sBAAA,CAAiD;IAAA,EAAC;IAC3D1B,EAAA,CAAAY,SAAA,aAA0B;IAAAZ,EAAA,CAAAa,MAAA,qBAC5B;IAAAb,EAAA,CAAAc,YAAA,EAAS;IAKTd,EAJA,CAAA4B,UAAA,KAAAC,0CAAA,qBAC+C,KAAAC,0CAAA,qBAGmD;IAItG9B,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAvBDd,EAAA,CAAA+B,SAAA,GACF;IADE/B,EAAA,CAAAgC,kBAAA,MAAAf,WAAA,CAAAgB,aAAA,sCAAAhB,WAAA,CAAAgB,aAAA,+CACF;IACIjC,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAkC,iBAAA,CAAAjB,WAAA,CAAAkB,aAAA,CAA4B;IAEVnC,EAAA,CAAA+B,SAAA,GAAwE;IAAxE/B,EAAA,CAAAoC,UAAA,YAAAnB,WAAA,CAAAoB,OAAA,6CAAwE;IAC1FrC,EAAA,CAAA+B,SAAA,EACF;IADE/B,EAAA,CAAAgC,kBAAA,MAAAf,WAAA,CAAAoB,OAAA,8CACF;IAEErC,EAAA,CAAA+B,SAAA,GAAmD;IAAnD/B,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAsC,WAAA,QAAArB,WAAA,CAAAsB,SAAA,sBAAmD;IACnDvC,EAAA,CAAA+B,SAAA,GAA8B;IAA9B/B,EAAA,CAAAkC,iBAAA,CAAAjB,WAAA,CAAAuB,QAAA,QAA8B;IAMvBxC,EAAA,CAAA+B,SAAA,GAAc;IAAd/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAmC,QAAA,CAAc;IAIdzC,EAAA,CAAA+B,SAAA,EAAc;IAAd/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAoC,QAAA,CAAc;;;;;IAMzB1C,EADF,CAAAC,cAAA,SAAsC,aACI;IACtCD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,wDACzC;IACFb,EADE,CAAAc,YAAA,EAAK,EACF;;;;;;IA+FWd,EAAA,CAAAC,cAAA,eAC0E;IAAtCD,EAAA,CAAAE,UAAA,mBAAAyC,sEAAA;MAAA,MAAAC,SAAA,GAAA5C,EAAA,CAAAI,aAAA,CAAAyC,IAAA,EAAA3B,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAwC,oBAAA,CAAAF,SAAA,CAA2B;IAAA,EAAC;IAErE5C,EADF,CAAAC,cAAA,eAAwB,eACE;IAAAD,EAAA,CAAAa,MAAA,GAAiB;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAC/Cd,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IAE5Db,EAF4D,CAAAc,YAAA,EAAM,EAC1D,EACF;;;;IALJd,EAAA,CAAA+C,WAAA,aAAAH,SAAA,CAAAI,QAAA,CAAiC;IAEPhD,EAAA,CAAA+B,SAAA,GAAiB;IAAjB/B,EAAA,CAAAkC,iBAAA,CAAAU,SAAA,CAAAK,KAAA,CAAiB;IACbjD,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAkC,iBAAA,CAAAU,SAAA,CAAAM,SAAA,QAA4B;;;;;IAM9DlD,EAAA,CAAAC,cAAA,eAA8E;IAC5ED,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,8DACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;;IAKJd,EAFF,CAAAC,cAAA,eAC4C,eACmB;IAC3DD,EAAA,CAAAa,MAAA,eAAE;IAAAb,EAAA,CAAAC,cAAA,gBAAmC;IAAAD,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAACd,EAAA,CAAAa,MAAA,2BACtE;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,yBAC+E;IAD/DD,EAAA,CAAAmD,gBAAA,wBAAAC,sFAAAC,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAkD,cAAA,EAAAH,MAAA,MAAA/C,MAAA,CAAAkD,cAAA,GAAAH,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAyB;IACFrD,EAAA,CAAAE,UAAA,wBAAAkD,sFAAAC,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAcJ,MAAA,CAAAmD,gBAAA,CAAAJ,MAAA,CAAwB;IAAA,EAAC;IAEhFrD,EADE,CAAAc,YAAA,EAAiB,EACb;;;;IALmCd,EAAA,CAAA+B,SAAA,GAAuB;IAAvB/B,EAAA,CAAAkC,iBAAA,CAAA5B,MAAA,CAAAoD,iBAAA,CAAuB;IAE9C1D,EAAA,CAAA+B,SAAA,GAAyB;IAAzB/B,EAAA,CAAA2D,gBAAA,SAAArD,MAAA,CAAAkD,cAAA,CAAyB;IACvCxD,EADwC,CAAAoC,UAAA,aAAA9B,MAAA,CAAAsD,aAAA,CAA0B,mBAAAtD,MAAA,CAAAoD,iBAAA,CAC9B;;;;;;IAStC1D,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAa,MAAA,GACA;IAAAb,EAAA,CAAAC,cAAA,kBACkE;IAAhED,EAAA,CAAAE,UAAA,mBAAA2D,gFAAA;MAAA,MAAAC,SAAA,GAAA9D,EAAA,CAAAI,aAAA,CAAA2D,IAAA,EAAA7C,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA0D,mBAAA,CAAAF,SAAA,CAA0B;IAAA,EAAC;IACxC9D,EADoE,CAAAc,YAAA,EAAS,EACtE;;;;IAHLd,EAAA,CAAA+B,SAAA,EACA;IADA/B,EAAA,CAAAgC,kBAAA,MAAA8B,SAAA,CAAAb,KAAA,MACA;;;;;IAJJjD,EADF,CAAAC,cAAA,cAA+D,iBACxB;IAAAD,EAAA,CAAAa,MAAA,GAA+C;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC5Fd,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAA4B,UAAA,IAAAqC,uDAAA,oBAA4F;IAMhGjE,EADE,CAAAc,YAAA,EAAM,EACF;;;;IARiCd,EAAA,CAAA+B,SAAA,GAA+C;IAA/C/B,EAAA,CAAAgC,kBAAA,2CAAA1B,MAAA,CAAA4D,yBAAA,CAAAC,MAAA,MAA+C;IAEpBnE,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAA4D,yBAAA,CAA4B;;;;;;IA7G9GlE,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,YAAoD;IAAAZ,EAAA,CAAAa,MAAA,gCACtD;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAkE,kEAAA;MAAA,MAAAC,OAAA,GAAArE,EAAA,CAAAI,aAAA,CAAAkE,IAAA,EAAAC,SAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAkE,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAE5FrE,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAQPd,EANV,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAAmD,gBAAA,2BAAAsB,0EAAApB,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAoE,cAAA,CAAAvC,aAAA,EAAAkB,MAAA,MAAA/C,MAAA,CAAAoE,cAAA,CAAAvC,aAAA,GAAAkB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAA0C;IAAqBrD,EAAA,CAAAE,UAAA,mCAAAyE,kFAAA;MAAA,MAAAN,OAAA,GAAArE,EAAA,CAAAI,aAAA,CAAAkE,IAAA,EAAAC,SAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAyBJ,MAAA,CAAAsE,QAAA,CAAAP,OAAA,CAAa;IAAA,EAAC;IAKhHrE,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAC4D;IAC9FD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,qBACU,kBAEsC;IAAxED,EAAA,CAAAE,UAAA,mBAAA2E,mEAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAAoE,cAAA,CAAAzC,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAAgF,aAAA;IAAA,EAAuE;IAMjE9E,EAJN,CAAAC,cAAA,eAAkB,eAEM,eACE,iBAG0B;IAD5CD,EAAA,CAAAmD,gBAAA,2BAAA4B,0EAAA1B,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAA0E,kBAAA,EAAA3B,MAAA,MAAA/C,MAAA,CAAA0E,kBAAA,GAAA3B,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAgC;IAACrD,EAAA,CAAAE,UAAA,yBAAA+E,wEAAA;MAAAjF,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAA4E,aAAA,EAAe;IAAA,EAAC;IAEpElF,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,eAAsB,iBAG0B;IAD5CD,EAAA,CAAAmD,gBAAA,2BAAAgC,0EAAA9B,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAA8E,mBAAA,EAAA/B,MAAA,MAAA/C,MAAA,CAAA8E,mBAAA,GAAA/B,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAiC;IAACrD,EAAA,CAAAE,UAAA,yBAAAmF,wEAAA;MAAArF,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAA4E,aAAA,EAAe;IAAA,EAAC;IAErElF,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,eAAsB,kBAC2D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAoF,mEAAA;MAAAtF,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiF,YAAA,EAAc;IAAA,EAAC;IAC5EvF,EAAA,CAAAY,SAAA,aAA2B;IAC7BZ,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAAmE;IAA1BD,EAAA,CAAAE,UAAA,mBAAAsF,mEAAA;MAAAxF,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA4E,aAAA,EAAe;IAAA,EAAC;IAChElF,EAAA,CAAAY,SAAA,aAA6B;IAGnCZ,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAmE,eACG,cAC3B,iBAEO;IAA1CD,EAAA,CAAAE,UAAA,oBAAAuF,mEAAA;MAAAzF,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAAoF,eAAA,EAAiB;IAAA,EAAC;IAD9B1F,EAAA,CAAAc,YAAA,EAC4C;IAC5Cd,EAAA,CAAAC,cAAA,iBAAqD;IAAAD,EAAA,CAAAa,MAAA,4CAAM;IAC7Db,EAD6D,CAAAc,YAAA,EAAQ,EAC/D;IACNd,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAa,MAAA,IAEF;IACFb,EADE,CAAAc,YAAA,EAAQ,EACJ;IAGNd,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAA4B,UAAA,KAAA+D,gDAAA,kBAC0E;IAM5E3F,EAAA,CAAAc,YAAA,EAAM;IAQNd,EALA,CAAA4B,UAAA,KAAAgE,gDAAA,kBAA8E,KAAAC,gDAAA,kBAMlC;IAQ9C7F,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAA4B,UAAA,KAAAkE,gDAAA,kBAA+D;IAWnE9F,EADE,CAAAc,YAAA,EAAM,EACC;IACTd,EAAA,CAAAC,cAAA,kBACyE;IAAvED,EAAA,CAAAE,UAAA,mBAAA6F,mEAAA;MAAA/F,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAAoE,cAAA,CAAAzC,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAAkG,YAAA;IAAA,EAAsE;IACtEhG,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAO;IASvBd,EANV,CAAAC,cAAA,eAAkB,eACC,cAEO,eACS,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAY,SAAA,iBACyE;IAIjFZ,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,cAAsB,eACS,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAY,SAAA,iBACyE;IAYjGZ,EAXsB,CAAAc,YAAA,EAAM,EACF,EACF,EACF,EACF,EACF,EACC,EACC,EACR,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,qBAEe;IADZD,EAAA,CAAAmD,gBAAA,2BAAA8C,8EAAA5C,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAoE,cAAA,CAAArC,OAAA,EAAAgB,MAAA,MAAA/C,MAAA,CAAAoE,cAAA,CAAArC,OAAA,GAAAgB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAoC;IAG/DrD,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IAWlBb,EAXkB,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF,EAGF,EACO;IAGbd,EADF,CAAAC,cAAA,2BAAgH,mBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAAgG,mEAAA;MAAA,MAAA7B,OAAA,GAAArE,EAAA,CAAAI,aAAA,CAAAkE,IAAA,EAAAC,SAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAkE,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAExErE,EAAA,CAAAY,SAAA,cAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,mBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAAiG,mEAAA;MAAA,MAAA9B,OAAA,GAAArE,EAAA,CAAAI,aAAA,CAAAkE,IAAA,EAAAC,SAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAsE,QAAA,CAAAP,OAAA,CAAa;IAAA,EAAC;IAE1DrE,EAAA,CAAAY,SAAA,cAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA3LMd,EAAA,CAAA+B,SAAA,IAA0C;IAA1C/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAAoE,cAAA,CAAAvC,aAAA,CAA0C;IAgBlBnC,EAAA,CAAA+B,SAAA,GAA0E;IAA1E/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAAoE,cAAA,CAAAzC,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAgF,aAAA,CAA0E;IAQxF9E,EAAA,CAAA+B,SAAA,GAAgC;IAAhC/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAA0E,kBAAA,CAAgC;IAKhChF,EAAA,CAAA+B,SAAA,GAAiC;IAAjC/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAA8E,mBAAA,CAAiC;IAiBKpF,EAAA,CAAA+B,SAAA,GAA6B;IAA7B/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAA8F,iBAAA,CAA6B;IAKnEpG,EAAA,CAAA+B,SAAA,GAEF;IAFE/B,EAAA,CAAAqG,kBAAA,aAAA/F,MAAA,CAAAoD,iBAAA,0BAAApD,MAAA,CAAAkD,cAAA,SAAAlD,MAAA,CAAAgG,IAAA,CAAAC,IAAA,CAAAjG,MAAA,CAAAoD,iBAAA,GAAApD,MAAA,CAAAsD,aAAA,cAEF;IAK0C5D,EAAA,CAAA+B,SAAA,GAAkB;IAAlB/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAAkG,eAAA,CAAkB;IAUxDxG,EAAA,CAAA+B,SAAA,EAAkC;IAAlC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAkG,eAAA,CAAArC,MAAA,OAAkC;IAMrCnE,EAAA,CAAA+B,SAAA,EAAuC;IAAvC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAoD,iBAAA,GAAApD,MAAA,CAAAsD,aAAA,CAAuC;IAWzB5D,EAAA,CAAA+B,SAAA,EAA0C;IAA1C/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA4D,yBAAA,CAAAC,MAAA,KAA0C;IAYzCnE,EAAA,CAAA+B,SAAA,EAAyE;IAAzE/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAAoE,cAAA,CAAAzC,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAkG,YAAA,CAAyE;IAyDlEhG,EAAA,CAAA+B,SAAA,IAAoC;IAApC/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAAoE,cAAA,CAAArC,OAAA,CAAoC;IAEtDrC,EAAA,CAAA+B,SAAA,EAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;IAKXpC,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;;;;;;IAiCpCpC,EAFJ,CAAAC,cAAA,mBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,aAA6C;IAAAZ,EAAA,CAAAa,MAAA,gCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAuG,kEAAA;MAAA,MAAAC,OAAA,GAAA1G,EAAA,CAAAI,aAAA,CAAAuG,IAAA,EAAApC,SAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAkE,OAAA,CAAAkC,OAAA,CAAY;IAAA,EAAC;IAE5F1G,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAQPd,EANV,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACW,kBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,kBAG6C;IADvED,EAAA,CAAAmD,gBAAA,2BAAAyD,0EAAAvD,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAuG,IAAA;MAAA,MAAArG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAoE,cAAA,CAAAvC,aAAA,EAAAkB,MAAA,MAAA/C,MAAA,CAAAoE,cAAA,CAAAvC,aAAA,GAAAkB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAA0C;IAKpDrD,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAC4D;IAC9FD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,qBACU,kBAEsC;IAAxED,EAAA,CAAAE,UAAA,mBAAA2G,mEAAA;MAAA7G,EAAA,CAAAI,aAAA,CAAAuG,IAAA;MAAA,MAAArG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAAoE,cAAA,CAAAzC,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAAgF,aAAA;IAAA,EAAuE;IACvE9E,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAI/Bd,EADF,CAAAC,cAAA,eAAkB,gBACc;IAC5BD,EAAA,CAAAY,SAAA,aAAuC;IACvCZ,EAAA,CAAAa,MAAA,8IACF;IAEJb,EAFI,CAAAc,YAAA,EAAM,EACF,EACC;IACTd,EAAA,CAAAC,cAAA,kBACyE;IAAvED,EAAA,CAAAE,UAAA,mBAAA4G,mEAAA;MAAA9G,EAAA,CAAAI,aAAA,CAAAuG,IAAA;MAAA,MAAArG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAAoE,cAAA,CAAAzC,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAAkG,YAAA;IAAA,EAAsE;IACtEhG,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAO;IASvBd,EANV,CAAAC,cAAA,eAAkB,eACC,cAEO,eACS,eACW,kBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAY,SAAA,kBACyE;IAIjFZ,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,cAAsB,eACS,eACW,kBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAY,SAAA,kBACyE;IAYjGZ,EAXsB,CAAAc,YAAA,EAAM,EACF,EACF,EACF,EACF,EACF,EACC,EACC,EACR,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,kBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,sBAEe;IADRD,EAAA,CAAAmD,gBAAA,2BAAA4D,8EAAA1D,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAuG,IAAA;MAAA,MAAArG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAoE,cAAA,CAAArC,OAAA,EAAAgB,MAAA,MAAA/C,MAAA,CAAAoE,cAAA,CAAArC,OAAA,GAAAgB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAoC;IAGnErD,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IAWlBb,EAXkB,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF,EAGF,EACO;IAGbd,EADF,CAAAC,cAAA,2BAAgH,mBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAA8G,mEAAA;MAAA,MAAAN,OAAA,GAAA1G,EAAA,CAAAI,aAAA,CAAAuG,IAAA,EAAApC,SAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAkE,OAAA,CAAAkC,OAAA,CAAY;IAAA,EAAC;IAExE1G,EAAA,CAAAY,SAAA,cAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,mBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAA+G,mEAAA;MAAA,MAAAP,OAAA,GAAA1G,EAAA,CAAAI,aAAA,CAAAuG,IAAA,EAAApC,SAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAsE,QAAA,CAAA8B,OAAA,CAAa;IAAA,EAAC;IAE1D1G,EAAA,CAAAY,SAAA,cAAgC;IAAAZ,EAAA,CAAAa,MAAA,qBAClC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IAtHMd,EAAA,CAAA+B,SAAA,IAA0C;IAA1C/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAAoE,cAAA,CAAAvC,aAAA,CAA0C;IAgBlBnC,EAAA,CAAA+B,SAAA,GAA0E;IAA1E/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAAoE,cAAA,CAAAzC,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAgF,aAAA,CAA0E;IAY1E9E,EAAA,CAAA+B,SAAA,GAAyE;IAAzE/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAAoE,cAAA,CAAAzC,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAkG,YAAA,CAAyE;IAyD9DhG,EAAA,CAAA+B,SAAA,IAAoC;IAApC/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAAoE,cAAA,CAAArC,OAAA,CAAoC;IAE1DrC,EAAA,CAAA+B,SAAA,EAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;IAKXpC,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;;;;;IA0HhCpC,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAY,SAAA,aAAmF;IACnFZ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAa,MAAA,4BAAM;IACjCb,EADiC,CAAAc,YAAA,EAAO,EAClC;;;;;IAeId,EADF,CAAAC,cAAA,SAA8D,SACxD;IAAAD,EAAA,CAAAa,MAAA,GAAW;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpBd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAA2C;IAAAZ,EAAA,CAAAa,MAAA,GAC7C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,GACvD;IACFb,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAPCd,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAkC,iBAAA,CAAAgF,KAAA,KAAW;IAE8BlH,EAAA,CAAA+B,SAAA,GAC7C;IAD6C/B,EAAA,CAAAgC,kBAAA,KAAAmF,SAAA,CAAAlE,KAAA,MAC7C;IAEuDjD,EAAA,CAAA+B,SAAA,GACvD;IADuD/B,EAAA,CAAAgC,kBAAA,KAAAmF,SAAA,CAAAjE,SAAA,aACvD;;;;;IAbAlD,EAJR,CAAAC,cAAA,eAAsE,iBACtC,YACrB,SACD,cAC4B;IAAAD,EAAA,CAAAa,MAAA,QAAC;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAEtCb,EAFsC,CAAAc,YAAA,EAAK,EACpC,EACC;IACRd,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA4B,UAAA,KAAAwF,4DAAA,iBAA8D;IAWpEpH,EAFI,CAAAc,YAAA,EAAQ,EACF,EACJ;;;;IAXsBd,EAAA,CAAA+B,SAAA,IAAyB;IAAzB/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAA+G,oBAAA,CAAyB;;;;;IAcrDrH,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,0EACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IA3BRd,EAAA,CAAAC,cAAA,UAAsC;IAyBpCD,EAxBA,CAAA4B,UAAA,IAAA0F,sDAAA,oBAAsE,IAAAC,sDAAA,kBAwBa;IAGrFvH,EAAA,CAAAc,YAAA,EAAM;;;;IA3B2Bd,EAAA,CAAA+B,SAAA,EAAqC;IAArC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+G,oBAAA,CAAAlD,MAAA,KAAqC;IAwB9DnE,EAAA,CAAA+B,SAAA,EAAuC;IAAvC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+G,oBAAA,CAAAlD,MAAA,OAAuC;;;;;;IAxHnDnE,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,aAAyC;IAAAZ,EAAA,CAAAa,MAAA,gCAC3C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAsH,kEAAA;MAAA,MAAAC,OAAA,GAAAzH,EAAA,CAAAI,aAAA,CAAAsH,IAAA,EAAAnD,SAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAkE,OAAA,CAAAiD,OAAA,CAAY;IAAA,EAAC;IAE5FzH,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAMXd,EAJN,CAAAC,cAAA,uBAAgC,eAEgD,eACkB,eAChD;IAC1CD,EAAA,CAAAY,SAAA,cAAoD;IAAAZ,EAAA,CAAAa,MAAA,iCACtD;IACFb,EADE,CAAAc,YAAA,EAAK,EACD;IAKEd,EAJR,CAAAC,cAAA,gBAAuB,eACJ,cACO,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAA4C;IAAAZ,EAAA,CAAAa,MAAA,iCAC9C;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAAkD;IAEtEb,EAFsE,CAAAc,YAAA,EAAI,EAClE,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,qBACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,cAAgB,gBAE4E;IACxFD,EAAA,CAAAY,SAAA,cACmB;IACnBZ,EAAA,CAAAa,MAAA,IACF;IAGNb,EAHM,CAAAc,YAAA,EAAO,EACL,EACA,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAmD;IAAAZ,EAAA,CAAAa,MAAA,iCACrD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,2BACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAEjEb,EAFiE,CAAAc,YAAA,EAAI,EAC7D,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAmD;IAAAZ,EAAA,CAAAa,MAAA,iCACrD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,2BACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAKvEb,EALuE,CAAAc,YAAA,EAAI,EAC7D,EACF,EACF,EACF,EACF;IAMFd,EAHJ,CAAAC,cAAA,gBAAyE,gBAEA,eACzB;IAC1CD,EAAA,CAAAY,SAAA,cAA6C;IAAAZ,EAAA,CAAAa,MAAA,uCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAa,MAAA,IAAuC;IACxEb,EADwE,CAAAc,YAAA,EAAO,EACzE;IACNd,EAAA,CAAAC,cAAA,gBAAuB;IAQrBD,EANA,CAAA4B,UAAA,KAAA+F,gDAAA,mBAA8D,KAAAC,gDAAA,kBAMxB;IA+B5C5H,EAFI,CAAAc,YAAA,EAAM,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,2BAAgH,mBAEjD;IADtBD,EAAA,CAAAE,UAAA,mBAAA2H,mEAAA;MAAA,MAAAJ,OAAA,GAAAzH,EAAA,CAAAI,aAAA,CAAAsH,IAAA,EAAAnD,SAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAkE,OAAA,CAAAiD,OAAA,CAAY;IAAA,EAAC;IAE3DzH,EAAA,CAAAY,SAAA,cAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA9GoBd,EAAA,CAAA+B,SAAA,IAAkD;IAAlD/B,EAAA,CAAAkC,iBAAA,EAAA5B,MAAA,CAAAwH,sBAAA,kBAAAxH,MAAA,CAAAwH,sBAAA,CAAA3F,aAAA,SAAkD;IAU9DnC,EAAA,CAAA+B,SAAA,GAAuF;IAAvF/B,EAAA,CAAAoC,UAAA,aAAA9B,MAAA,CAAAwH,sBAAA,kBAAAxH,MAAA,CAAAwH,sBAAA,CAAAzF,OAAA,8CAAuF;IACpFrC,EAAA,CAAA+B,SAAA,EAA+F;IAA/F/B,EAAA,CAAA+H,UAAA,EAAAzH,MAAA,CAAAwH,sBAAA,kBAAAxH,MAAA,CAAAwH,sBAAA,CAAAzF,OAAA,wDAA+F;IAElGrC,EAAA,CAAA+B,SAAA,EACF;IADE/B,EAAA,CAAAgC,kBAAA,OAAA1B,MAAA,CAAAwH,sBAAA,kBAAAxH,MAAA,CAAAwH,sBAAA,CAAAzF,OAAA,+CACF;IAScrC,EAAA,CAAA+B,SAAA,GAA2E;IAA3E/B,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAsC,WAAA,SAAAhC,MAAA,CAAAwH,sBAAA,kBAAAxH,MAAA,CAAAwH,sBAAA,CAAAvF,SAAA,6BAA2E;IAQ3EvC,EAAA,CAAA+B,SAAA,GAA6C;IAA7C/B,EAAA,CAAAkC,iBAAA,EAAA5B,MAAA,CAAAwH,sBAAA,kBAAAxH,MAAA,CAAAwH,sBAAA,CAAAtF,QAAA,SAA6C;IAQ7CxC,EAAA,CAAA+B,SAAA,GAA2E;IAA3E/B,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAsC,WAAA,SAAAhC,MAAA,CAAAwH,sBAAA,kBAAAxH,MAAA,CAAAwH,sBAAA,CAAAE,SAAA,6BAA2E;IAQ3EhI,EAAA,CAAA+B,SAAA,GAA6C;IAA7C/B,EAAA,CAAAkC,iBAAA,EAAA5B,MAAA,CAAAwH,sBAAA,kBAAAxH,MAAA,CAAAwH,sBAAA,CAAAG,QAAA,SAA6C;IAcpCjI,EAAA,CAAA+B,SAAA,GAAuC;IAAvC/B,EAAA,CAAAgC,kBAAA,YAAA1B,MAAA,CAAA+G,oBAAA,CAAAlD,MAAA,wBAAuC;IAIhEnE,EAAA,CAAA+B,SAAA,GAA6B;IAA7B/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA4H,uBAAA,CAA6B;IAM7BlI,EAAA,CAAA+B,SAAA,EAA8B;IAA9B/B,EAAA,CAAAoC,UAAA,UAAA9B,MAAA,CAAA4H,uBAAA,CAA8B;;;AD1gB9C,OAAM,MAAOC,iBAAkB,SAAQzI,aAAa;EASlD0I,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,gBAAiC,EACjCC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAdf,KAAApC,IAAI,GAAGA,IAAI,CAAC,CAAC;IACb,KAAAxG,gBAAgB,GAAGA,gBAAgB,CAAC,CAAC;IACrC,KAAAC,sBAAsB,GAAGA,sBAAsB,CAAC,CAAC;IAiBxC,KAAA4I,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB;IACA,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAArE,cAAc,GAAqB,EAAE;IACrC,KAAAsE,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAkB,IAAI;IAClC,KAAAC,kBAAkB,GAAkB,IAAI;IAExC;IACA,KAAA1C,eAAe,GAAwB,EAAE;IACzC,KAAAtC,yBAAyB,GAAwB,EAAE;IACnD,KAAAc,kBAAkB,GAAW,EAAE;IAC/B,KAAAI,mBAAmB,GAAW,EAAE;IAChC,KAAA5B,cAAc,GAAG,CAAC;IAClB,KAAAI,aAAa,GAAG,EAAE;IAClB,KAAAF,iBAAiB,GAAG,CAAC;IACrB,KAAA0C,iBAAiB,GAAG,KAAK;IAEzB;IACA,KAAA+C,cAAc,GAAuB,EAAE;IACvC,KAAAC,wBAAwB,GAAuB,EAAE;IACjD,KAAAC,iBAAiB,GAAW,EAAE;IAC9B,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAA5B,sBAAsB,GAAwB,IAAI;IAClD,KAAAT,oBAAoB,GAA8B,EAAE;IACpD,KAAAa,uBAAuB,GAAG,KAAK;EArC/B;EAuCSyB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAD,gBAAgBA,CAAA;IACd,MAAME,OAAO,GAAG;MACd3H,aAAa,EAAE,IAAI,CAAC6G,aAAa,IAAI,IAAI;MACzC3G,OAAO,EAAE,IAAI,CAAC4G,YAAY;MAC1BhH,aAAa,EAAE,IAAI,CAACiH,kBAAkB;MACtCa,SAAS,EAAE,IAAI,CAAClB,SAAS;MACzBmB,QAAQ,EAAE,IAAI,CAACpB;KAChB;IAED,IAAI,CAACL,gBAAgB,CAAC0B,mCAAmC,CAAC;MAAEC,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CAC/EtK,GAAG,CAACuK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACtB,YAAY,GAAGqB,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACjDC,WAAW,EAAED,IAAI,CAACC,WAAY;UAC9BtI,aAAa,EAAEqI,IAAI,CAACrI,aAAc;UAClCF,aAAa,EAAEuI,IAAI,CAACvI,aAAa;UAAE;UACnCM,SAAS,EAAEiI,IAAI,CAACjI,SAAU;UAC1ByF,SAAS,EAAEwC,IAAI,CAACxC,SAAU;UAC1BxF,QAAQ,EAAEgI,IAAI,CAAChI,QAAQ;UACvByF,QAAQ,EAAEuC,IAAI,CAACvC,QAAQ;UACvB5F,OAAO,EAAEmI,IAAI,CAACnI;SACf,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAACyG,YAAY,GAAGsB,QAAQ,CAACM,UAAU,IAAI,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAACjC,OAAO,CAACkC,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAhB,mBAAmBA,CAAA;IACjB,MAAMC,OAAO,GAAG;MACd7G,KAAK,EAAE,IAAI,CAAC+B,kBAAkB,IAAI,IAAI;MACtC9B,SAAS,EAAE,IAAI,CAACkC,mBAAmB,IAAI,IAAI;MAC3C/C,OAAO,EAAE,CAAC;MAAE;MACZ0H,SAAS,EAAE,IAAI,CAACvG,cAAc;MAC9BwG,QAAQ,EAAE,IAAI,CAACpG;KAChB;IAED,IAAI,CAAC4E,aAAa,CAACsC,6BAA6B,CAAC;MAAEZ,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACtEtK,GAAG,CAACuK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAAC7D,eAAe,GAAG4D,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDO,QAAQ,EAAEP,IAAI,CAACO,QAAS;UACxB9H,KAAK,EAAEuH,IAAI,CAACvH,KAAM;UAClBC,SAAS,EAAEsH,IAAI,CAACtH,SAAS;UACzBF,QAAQ,EAAE,IAAI,CAACkB,yBAAyB,CAAC8G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ;SAChF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAACrH,iBAAiB,GAAG0G,QAAQ,CAACM,UAAU,IAAI,CAAC;QACjD,IAAI,CAACQ,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,CACH,CAACL,SAAS,EAAE;EACf;EAEA;EACAM,QAAQA,CAAA;IACN,IAAI,CAACtC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACe,gBAAgB,EAAE;EACzB;EAEAwB,OAAOA,CAAA;IACL,IAAI,CAACpC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACJ,SAAS,GAAG,CAAC;IAClB,IAAI,CAACe,gBAAgB,EAAE;EACzB;EAEA;EACA1E,aAAaA,CAAA;IACX,IAAI,CAAC1B,cAAc,GAAG,CAAC;IACvB,IAAI,CAACqG,mBAAmB,EAAE;EAC5B;EAEAtE,YAAYA,CAAA;IACV,IAAI,CAACP,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACI,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC5B,cAAc,GAAG,CAAC;IACvB,IAAI,CAACqG,mBAAmB,EAAE;EAC5B;EAEA;EACAwB,WAAWA,CAACC,IAAY;IACtB,IAAI,CAACzC,SAAS,GAAGyC,IAAI;IACrB,IAAI,CAAC1B,gBAAgB,EAAE;EACzB;EAEAnG,gBAAgBA,CAAC6H,IAAY;IAC3B,IAAI,CAAC9H,cAAc,GAAG8H,IAAI;IAC1B,IAAI,CAACzB,mBAAmB,EAAE;EAC5B;EAEA;EACAlJ,eAAeA,CAAC4K,KAAuB;IACrC,IAAI,CAAC7G,cAAc,GAAG;MACpBrC,OAAO,EAAE,CAAC;MACVJ,aAAa,EAAEnC,gBAAgB,CAACgF,aAAa,CAAC;KAC/C;IACD,IAAI,CAACZ,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAAC2F,mBAAmB,EAAE;IAC1B,IAAI,CAACvB,aAAa,CAACkD,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAtK,aAAaA,CAACmK,KAAuB,EAAEI,QAAsB;IAC3D,IAAI,CAACjH,cAAc,GAAG;MACpB+F,WAAW,EAAEkB,QAAQ,CAAClB,WAAW;MACjCtI,aAAa,EAAEwJ,QAAQ,CAACxJ,aAAa;MACrCF,aAAa,EAAE0J,QAAQ,CAAC1J,aAAa,IAAInC,gBAAgB,CAACgF,aAAa;MACvEzC,OAAO,EAAEsJ,QAAQ,CAACtJ,OAAO,IAAI;KAC9B;IACD,IAAI,CAACiG,aAAa,CAACkD,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAlH,OAAOA,CAACoH,GAAQ;IACdA,GAAG,CAACC,KAAK,EAAE;EACb;EAEAjH,QAAQA,CAACgH,GAAQ;IACf,IAAI,CAAC,IAAI,CAACE,oBAAoB,EAAE,EAAE;MAChC;IACF;IAEA,IAAI,IAAI,CAACpH,cAAc,CAAC+F,WAAW,EAAE;MACnC,IAAI,CAACsB,cAAc,CAACH,GAAG,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACI,cAAc,CAACJ,GAAG,CAAC;IAC1B;EACF;EAEA;EACAE,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACpH,cAAc,CAACvC,aAAa,EAAE8J,IAAI,EAAE,EAAE;MAC9C,IAAI,CAACxD,OAAO,CAACkC,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACjG,cAAc,CAACzC,aAAa,KAAKiK,SAAS,IAAI,IAAI,CAACxH,cAAc,CAACzC,aAAa,KAAK,IAAI,EAAE;MACjG,IAAI,CAACwG,OAAO,CAACkC,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACjG,cAAc,CAACrC,OAAO,KAAK6J,SAAS,IAAI,IAAI,CAACxH,cAAc,CAACrC,OAAO,KAAK,IAAI,EAAE;MACrF,IAAI,CAACoG,OAAO,CAACkC,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAACjG,cAAc,CAAC+F,WAAW,IAAI,IAAI,CAAC/F,cAAc,CAACzC,aAAa,KAAKnC,gBAAgB,CAACgF,aAAa,IAAI,IAAI,CAACZ,yBAAyB,CAACC,MAAM,KAAK,CAAC,EAAE;MAC3J,IAAI,CAACsE,OAAO,CAACkC,YAAY,CAAC,eAAe,CAAC;MAC1C,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;EAEA;EACAqB,cAAcA,CAACJ,GAAQ;IACrB,MAAMO,YAAY,GAAqB;MACrChK,aAAa,EAAE,IAAI,CAACuC,cAAc,CAACvC,aAAa;MAChDF,aAAa,EAAE,IAAI,CAACyC,cAAc,CAACzC,aAAa;MAChDI,OAAO,EAAE,IAAI,CAACqC,cAAc,CAACrC;KAC9B;IAED,IAAI,CAACkG,gBAAgB,CAAC6D,gCAAgC,CAAC;MAAElC,IAAI,EAAEiC;IAAY,CAAE,CAAC,CAAChC,IAAI,CACjFtK,GAAG,CAACuK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;QACjD,MAAM+B,UAAU,GAAGC,QAAQ,CAAClC,QAAQ,CAACE,OAAO,EAAE,EAAE,CAAC;QACjD,IAAI,CAACiC,mBAAmB,CAACF,UAAU,EAAET,GAAG,CAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAACnD,OAAO,CAACkC,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAkB,cAAcA,CAACH,GAAQ;IACrB,MAAMO,YAAY,GAAqB;MACrC1B,WAAW,EAAE,IAAI,CAAC/F,cAAc,CAAC+F,WAAW;MAC5CtI,aAAa,EAAE,IAAI,CAACuC,cAAc,CAACvC,aAAa;MAChDF,aAAa,EAAE,IAAI,CAACyC,cAAc,CAACzC,aAAa;MAChDI,OAAO,EAAE,IAAI,CAACqC,cAAc,CAACrC;KAC9B;IAED,IAAI,CAACkG,gBAAgB,CAAC6D,gCAAgC,CAAC;MAAElC,IAAI,EAAEiC;IAAY,CAAE,CAAC,CAAChC,IAAI,CACjFtK,GAAG,CAACuK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAAC5B,OAAO,CAAC+D,aAAa,CAAC,QAAQ,CAAC;QACpCZ,GAAG,CAACC,KAAK,EAAE;QACX,IAAI,CAACjC,gBAAgB,EAAE;MACzB,CAAC,MAAM;QACL,IAAI,CAACnB,OAAO,CAACkC,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACA0B,mBAAmBA,CAACF,UAAkB,EAAET,GAAQ;IAC9C;IACA,IAAI,CAACnD,OAAO,CAAC+D,aAAa,CAAC,QAAQ,CAAC;IACpCZ,GAAG,CAACC,KAAK,EAAE;IACX,IAAI,CAACjC,gBAAgB,EAAE;EACzB;EAEA;EACArI,cAAcA,CAACoK,QAAsB;IACnC,IAAIc,OAAO,CAAC,WAAWd,QAAQ,CAACxJ,aAAa,KAAK,CAAC,EAAE;MACnD,IAAI,CAACoG,gBAAgB,CAACmE,kCAAkC,CAAC;QACvDxC,IAAI,EAAE;UAAEO,WAAW,EAAEkB,QAAQ,CAAClB;QAAW;OAC1C,CAAC,CAACN,IAAI,CACLtK,GAAG,CAACuK,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAAC5B,OAAO,CAAC+D,aAAa,CAAC,QAAQ,CAAC;UACpC,IAAI,CAAC5C,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAACnB,OAAO,CAACkC,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;QACzD;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf;EACF;EAEA;EACAlJ,kBAAkBA,CAACgK,QAAsB,EAAEJ,KAAuB;IAChE,IAAI,CAACzD,sBAAsB,GAAG6D,QAAQ;IACtC,IAAI,CAACzD,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACb,oBAAoB,GAAG,EAAE;IAE9B,IAAI,CAACiB,aAAa,CAACkD,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;IAEF,MAAM5B,OAAO,GAA8B;MACzCuC,UAAU,EAAEV,QAAQ,CAAClB;KACtB;IAED,IAAI,CAAClC,gBAAgB,CAACoE,yCAAyC,CAAC;MAAEzC,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACrFtK,GAAG,CAACuK,QAAQ,IAAG;MACb,IAAI,CAAClC,uBAAuB,GAAG,KAAK;MACpC,IAAIkC,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAAChD,oBAAoB,GAAG+C,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACzDoC,UAAU,EAAEpC,IAAI,CAACoC,UAAW;UAC5B3J,KAAK,EAAEuH,IAAI,CAACvH,KAAM;UAClBC,SAAS,EAAEsH,IAAI,CAACtH;SACjB,CAAC,CAAC,IAAI,EAAE;MACX,CAAC,MAAM;QACL,IAAI,CAACuF,OAAO,CAACkC,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACA/H,oBAAoBA,CAAC+J,KAAwB;IAC3CA,KAAK,CAAC7J,QAAQ,GAAG,CAAC6J,KAAK,CAAC7J,QAAQ;IAEhC,IAAI6J,KAAK,CAAC7J,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAACkB,yBAAyB,CAAC8G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC,EAAE;QAC5E,IAAI,CAAC7G,yBAAyB,CAAC4I,IAAI,CAAC;UAAE,GAAGD;QAAK,CAAE,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAAC3I,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC6I,MAAM,CAAC9B,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC;IAC5G;IAEA,IAAI,CAACG,4BAA4B,EAAE;EACrC;EAEAxF,eAAeA,CAAA;IACb,IAAI,CAACU,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAEhD,IAAI,CAACI,eAAe,CAACwG,OAAO,CAACH,KAAK,IAAG;MACnCA,KAAK,CAAC7J,QAAQ,GAAG,IAAI,CAACoD,iBAAiB;MACvC,IAAI,IAAI,CAACA,iBAAiB,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAClC,yBAAyB,CAAC8G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC,EAAE;UAC5E,IAAI,CAAC7G,yBAAyB,CAAC4I,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACnD;MACF,CAAC,MAAM;QACL,IAAI,CAAC3I,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC6I,MAAM,CAAC9B,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC;MAC5G;IACF,CAAC,CAAC;EACJ;EAEA/G,mBAAmBA,CAAC6I,KAAwB;IAC1C,IAAI,CAAC3I,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC6I,MAAM,CAAC9B,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC;IAE1G,MAAMkC,cAAc,GAAG,IAAI,CAACzG,eAAe,CAAC0G,IAAI,CAACjC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC;IACpF,IAAIkC,cAAc,EAAE;MAClBA,cAAc,CAACjK,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAACkI,4BAA4B,EAAE;EACrC;EAEAA,4BAA4BA,CAAA;IAC1B,IAAI,CAAC9E,iBAAiB,GAAG,IAAI,CAACI,eAAe,CAACrC,MAAM,GAAG,CAAC,IACtD,IAAI,CAACqC,eAAe,CAAC2G,KAAK,CAACN,KAAK,IAAIA,KAAK,CAAC7J,QAAQ,CAAC;EACvD;;;uCA/WWmF,iBAAiB,EAAAnI,EAAA,CAAAoN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtN,EAAA,CAAAoN,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAxN,EAAA,CAAAoN,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA1N,EAAA,CAAAoN,iBAAA,CAAAK,EAAA,CAAAE,YAAA,GAAA3N,EAAA,CAAAoN,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA7N,EAAA,CAAAoN,iBAAA,CAAAU,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAjB5F,iBAAiB;MAAA6F,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;UC1E5BnO,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,SAAA,qBAAiC;UACnCZ,EAAA,CAAAc,YAAA,EAAiB;UAIbd,EAHJ,CAAAC,cAAA,mBAAc,aAE6E,aAChD;UACrCD,EAAA,CAAAY,SAAA,WAA+E;UAE7EZ,EADF,CAAAC,cAAA,UAAK,WACmD;UACpDD,EAAA,CAAAa,MAAA,iUACF;UAGNb,EAHM,CAAAc,YAAA,EAAI,EACA,EACF,EACF;UAMAd,EAHN,CAAAC,cAAA,cAA8B,cACN,eACqC,iBACT;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAExDd,EADF,CAAAC,cAAA,yBAA6B,iBAE8B;UAAvDD,EAAA,CAAAmD,gBAAA,2BAAAkL,2DAAAhL,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAAkO,GAAA;YAAAtO,EAAA,CAAAuD,kBAAA,CAAA6K,GAAA,CAAApF,aAAA,EAAA3F,MAAA,MAAA+K,GAAA,CAAApF,aAAA,GAAA3F,MAAA;YAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;UAAA,EAA2B;UAACrD,EAAA,CAAAE,UAAA,yBAAAqO,yDAAA;YAAAvO,EAAA,CAAAI,aAAA,CAAAkO,GAAA;YAAA,OAAAtO,EAAA,CAAAU,WAAA,CAAe0N,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAG9DnL,EAJM,CAAAc,YAAA,EACyD,EAC3C,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACf;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAEhDd,EADF,CAAAC,cAAA,yBAA6B,qBAC2E;UAAzDD,EAAA,CAAAmD,gBAAA,2BAAAqL,+DAAAnL,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAAkO,GAAA;YAAAtO,EAAA,CAAAuD,kBAAA,CAAA6K,GAAA,CAAAnF,YAAA,EAAA5F,MAAA,MAAA+K,GAAA,CAAAnF,YAAA,GAAA5F,MAAA;YAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;UAAA,EAA0B;UAACrD,EAAA,CAAAE,UAAA,4BAAAuO,gEAAA;YAAAzO,EAAA,CAAAI,aAAA,CAAAkO,GAAA;YAAA,OAAAtO,EAAA,CAAAU,WAAA,CAAkB0N,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UACnGnL,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACxCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACrCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAIjCb,EAJiC,CAAAc,YAAA,EAAY,EAC3B,EACE,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACT;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAExDd,EADF,CAAAC,cAAA,yBAA6B,qBAEK;UADqBD,EAAA,CAAAmD,gBAAA,2BAAAuL,+DAAArL,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAAkO,GAAA;YAAAtO,EAAA,CAAAuD,kBAAA,CAAA6K,GAAA,CAAAlF,kBAAA,EAAA7F,MAAA,MAAA+K,GAAA,CAAAlF,kBAAA,GAAA7F,MAAA;YAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;UAAA,EAAgC;UACnFrD,EAAA,CAAAE,UAAA,4BAAAyO,gEAAA;YAAA3O,EAAA,CAAAI,aAAA,CAAAkO,GAAA;YAAA,OAAAtO,EAAA,CAAAU,WAAA,CAAkB0N,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAC7BnL,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACxCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACvCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAInCb,EAJmC,CAAAc,YAAA,EAAY,EAC7B,EACE,EACZ,EACF;UAENd,EAAA,CAAAY,SAAA,cAEM;UAKFZ,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACmB;UAApBD,EAAA,CAAAE,UAAA,mBAAA0O,oDAAA;YAAA5O,EAAA,CAAAI,aAAA,CAAAkO,GAAA;YAAA,OAAAtO,EAAA,CAAAU,WAAA,CAAS0N,GAAA,CAAAhD,OAAA,EAAS;UAAA,EAAC;UACvEpL,EAAA,CAAAY,SAAA,aAAgC;UAAAZ,EAAA,CAAAa,MAAA,qBAClC;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAC,cAAA,kBAA8D;UAArBD,EAAA,CAAAE,UAAA,mBAAA2O,oDAAA;YAAA7O,EAAA,CAAAI,aAAA,CAAAkO,GAAA;YAAA,OAAAtO,EAAA,CAAAU,WAAA,CAAS0N,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAC3DnL,EAAA,CAAAY,SAAA,aAAkC;UAAAZ,EAAA,CAAAa,MAAA,qBACpC;UAEJb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAGJd,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAA4B,UAAA,KAAAkN,oCAAA,qBAAiG;UAKvG9O,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;UAOEd,EAJR,CAAAC,cAAA,eAAmC,iBACc,aACtC,UACD,cACoC;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC7Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,0BAAG;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC9Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAE5Cb,EAF4C,CAAAc,YAAA,EAAK,EAC1C,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UA2BLD,EA1BA,CAAA4B,UAAA,KAAAmN,gCAAA,mBAA0C,KAAAC,gCAAA,iBA0BJ;UAQ9ChP,EAHM,CAAAc,YAAA,EAAQ,EACF,EACJ,EACO;UAEbd,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADrBD,EAAA,CAAAmD,gBAAA,wBAAA8L,iEAAA5L,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAAkO,GAAA;YAAAtO,EAAA,CAAAuD,kBAAA,CAAA6K,GAAA,CAAAvF,SAAA,EAAAxF,MAAA,MAAA+K,GAAA,CAAAvF,SAAA,GAAAxF,MAAA;YAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;UAAA,EAAoB;UAClCrD,EAAA,CAAAE,UAAA,wBAAA+O,iEAAA5L,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAAkO,GAAA;YAAA,OAAAtO,EAAA,CAAAU,WAAA,CAAc0N,GAAA,CAAA/C,WAAA,CAAAhI,MAAA,CAAmB;UAAA,EAAC;UAGxCrD,EAFI,CAAAc,YAAA,EAAiB,EACF,EACT;UA4WVd,EAzWA,CAAA4B,UAAA,KAAAsN,yCAAA,kCAAAlP,EAAA,CAAAmP,sBAAA,CAA8C,KAAAC,yCAAA,iCAAApP,EAAA,CAAAmP,sBAAA,CAuNF,KAAAE,yCAAA,kCAAArP,EAAA,CAAAmP,sBAAA,CAkJU;;;UA3dxCnP,EAAA,CAAA+B,SAAA,IAA2B;UAA3B/B,EAAA,CAAA2D,gBAAA,YAAAyK,GAAA,CAAApF,aAAA,CAA2B;UASgBhJ,EAAA,CAAA+B,SAAA,GAA0B;UAA1B/B,EAAA,CAAA2D,gBAAA,YAAAyK,GAAA,CAAAnF,YAAA,CAA0B;UAC1DjJ,EAAA,CAAA+B,SAAA,EAAc;UAAd/B,EAAA,CAAAoC,UAAA,eAAc;UACdpC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAoC,UAAA,YAAW;UACXpC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAoC,UAAA,YAAW;UAU6BpC,EAAA,CAAA+B,SAAA,GAAgC;UAAhC/B,EAAA,CAAA2D,gBAAA,YAAAyK,GAAA,CAAAlF,kBAAA,CAAgC;UAExElJ,EAAA,CAAA+B,SAAA,EAAc;UAAd/B,EAAA,CAAAoC,UAAA,eAAc;UACdpC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAoC,UAAA,YAAW;UACXpC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAoC,UAAA,YAAW;UAwBgBpC,EAAA,CAAA+B,SAAA,IAAc;UAAd/B,EAAA,CAAAoC,UAAA,SAAAgM,GAAA,CAAAkB,QAAA,CAAc;UAqB/BtP,EAAA,CAAA+B,SAAA,IAAe;UAAf/B,EAAA,CAAAoC,UAAA,YAAAgM,GAAA,CAAArF,YAAA,CAAe;UA0BnC/I,EAAA,CAAA+B,SAAA,EAA+B;UAA/B/B,EAAA,CAAAoC,UAAA,SAAAgM,GAAA,CAAArF,YAAA,CAAA5E,MAAA,OAA+B;UAU1BnE,EAAA,CAAA+B,SAAA,GAAoB;UAApB/B,EAAA,CAAA2D,gBAAA,SAAAyK,GAAA,CAAAvF,SAAA,CAAoB;UAAuB7I,EAAtB,CAAAoC,UAAA,aAAAgM,GAAA,CAAAxF,QAAA,CAAqB,mBAAAwF,GAAA,CAAAtF,YAAA,CAAgC;;;qBD7D1FnJ,YAAY,EAAA4P,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZ/P,YAAY,EAAAgQ,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAxC,EAAA,CAAAyC,eAAA,EAAAzC,EAAA,CAAA0C,mBAAA,EAAA1C,EAAA,CAAA2C,qBAAA,EAAA3C,EAAA,CAAA4C,qBAAA,EAAA5C,EAAA,CAAA6C,gBAAA,EAAA7C,EAAA,CAAA8C,iBAAA,EAAA9C,EAAA,CAAA+C,iBAAA,EAAA/C,EAAA,CAAAgD,iBAAA,EAAAhD,EAAA,CAAAiD,cAAA,EAAAjD,EAAA,CAAAkD,oBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}