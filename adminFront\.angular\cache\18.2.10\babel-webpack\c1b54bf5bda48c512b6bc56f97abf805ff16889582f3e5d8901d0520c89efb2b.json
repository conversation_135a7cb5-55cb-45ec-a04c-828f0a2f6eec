{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { GetRequirement } from 'src/services/api/models';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\nimport { TemplateCreatorComponent } from 'src/app/shared/components/template-creator/template-creator.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = () => [];\nfunction RequirementManagementComponent_div_6_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"label\", 67);\n    i0.ɵɵtext(2, \"\\u5EFA\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_div_6_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.getListRequirementRequest.CBuildCaseID, $event) || (ctx_r2.getListRequirementRequest.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, RequirementManagementComponent_div_6_nb_option_4_Template, 2, 2, \"nb-option\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.getListRequirementRequest.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildCaseList);\n  }\n}\nfunction RequirementManagementComponent_nb_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r5.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r5.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_50_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(146);\n      return i0.ɵɵresetView(ctx_r2.add(dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_79_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_79_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const data_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(146);\n      return i0.ɵɵresetView(ctx_r2.onEdit(data_r9, dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 75);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_79_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_79_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const data_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDelete(data_r9));\n    });\n    i0.ɵɵelement(1, \"i\", 77);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 69)(1, \"td\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 70);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 71);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 71);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 71);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 71);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 71);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 71);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 70);\n    i0.ɵɵtemplate(20, RequirementManagementComponent_tr_79_button_20_Template, 3, 0, \"button\", 72)(21, RequirementManagementComponent_tr_79_button_21_Template, 3, 0, \"button\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseType(data_r9.CHouseType || i0.ɵɵpureFunction0(14, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 10, data_r9.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getCIsShowText(data_r9));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 12, data_r9.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction RequirementManagementComponent_span_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getSelectedRequirements().length, \" \");\n  }\n}\nfunction RequirementManagementComponent_small_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 79);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5148\\u52FE\\u9078\\u8981\\u52A0\\u5165\\u6A21\\u677F\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_small_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u64C7 \", ctx_r2.getSelectedRequirements().length, \" \\u500B\\u9805\\u76EE \");\n  }\n}\nfunction RequirementManagementComponent_button_117_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_117_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectAllRequirements());\n    });\n    i0.ɵɵelement(1, \"i\", 82);\n    i0.ɵɵtext(2, \"\\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_button_118_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_118_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearAllSelections());\n    });\n    i0.ɵɵelement(1, \"i\", 84);\n    i0.ɵɵtext(2, \"\\u6E05\\u9664\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_143_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_143_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const data_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(146);\n      return i0.ɵɵresetView(ctx_r2.onEdit(data_r16, dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 75);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_143_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_143_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const data_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDelete(data_r16));\n    });\n    i0.ɵɵelement(1, \"i\", 77);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_143_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 69)(1, \"td\", 71)(2, \"input\", 85);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_tr_143_Template_input_ngModelChange_2_listener($event) {\n      const data_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      i0.ɵɵtwoWayBindingSet(data_r16.selected, $event) || (data_r16.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function RequirementManagementComponent_tr_143_Template_input_change_2_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRequirementSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 70);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 70);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 71);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 71);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 71);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 71);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 71);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 70);\n    i0.ɵɵtemplate(20, RequirementManagementComponent_tr_143_button_20_Template, 3, 0, \"button\", 72)(21, RequirementManagementComponent_tr_143_button_21_Template, 3, 0, \"button\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r16 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"table-active\", data_r16.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", data_r16.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r16.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r16.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseType(data_r16.CHouseType || i0.ɵɵpureFunction0(16, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r16.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 12, data_r16.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getCIsShowText(data_r16));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 14, data_r16.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_145_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_145_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_145_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_145_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_145_app_form_group_10_nb_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const b_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", b_r21.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", b_r21.CBuildCaseName, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_145_app_form_group_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-form-group\", 91)(1, \"nb-select\", 104);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_145_app_form_group_10_Template_nb_select_selectedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CBuildCaseID, $event) || (ctx_r2.saveRequirement.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_145_app_form_group_10_nb_option_2_Template, 2, 2, \"nb-option\", 96);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\\u540D\\u7A31\")(\"labelFor\", \"CBuildCaseID\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildCaseList);\n  }\n}\nfunction RequirementManagementComponent_ng_template_145_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r22.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r22.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_145_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r23.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_145_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 86)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_145_span_2_Template, 2, 0, \"span\", 87)(3, RequirementManagementComponent_ng_template_145_span_3_Template, 2, 0, \"span\", 87)(4, RequirementManagementComponent_ng_template_145_span_4_Template, 2, 0, \"span\", 87)(5, RequirementManagementComponent_ng_template_145_span_5_Template, 2, 0, \"span\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 88)(7, \"div\", 7)(8, \"div\", 89)(9, \"div\", 7);\n    i0.ɵɵtemplate(10, RequirementManagementComponent_ng_template_145_app_form_group_10_Template, 3, 5, \"app-form-group\", 90);\n    i0.ɵɵelementStart(11, \"app-form-group\", 91)(12, \"input\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_145_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRequirement, $event) || (ctx_r2.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"app-form-group\", 91)(14, \"input\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_145_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CGroupName, $event) || (ctx_r2.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"app-form-group\", 91)(16, \"input\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_145_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CSort, $event) || (ctx_r2.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 91)(18, \"nb-select\", 95);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_145_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CHouseType, $event) || (ctx_r2.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_145_nb_option_19_Template, 2, 2, \"nb-option\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 91)(21, \"nb-select\", 97);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_145_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CStatus, $event) || (ctx_r2.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, RequirementManagementComponent_ng_template_145_nb_option_22_Template, 2, 2, \"nb-option\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"app-form-group\", 91)(24, \"input\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_145_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnitPrice, $event) || (ctx_r2.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"app-form-group\", 91)(26, \"input\", 99);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_145_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnit, $event) || (ctx_r2.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 91)(28, \"nb-checkbox\", 100);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_145_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CIsShow, $event) || (ctx_r2.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u986F\\u793A\\u5728\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 91)(31, \"textarea\", 101);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_145_Template_textarea_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRemark, $event) || (ctx_r2.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\")(33, \"div\", 7)(34, \"div\", 102)(35, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_145_Template_button_click_35_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save(ref_r24));\n    });\n    i0.ɵɵtext(36, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_145_Template_button_click_37_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r19).dialogRef;\n      return i0.ɵɵresetView(ref_r24.close());\n    });\n    i0.ɵɵtext(38, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true && ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false && ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true && ctx_r2.currentTab === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false && ctx_r2.currentTab === 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CHouseType);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.currentTab === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u521D\\u6B65\\u9700\\u6C42\")(\"labelFor\", \"CIsShow\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_147_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_147_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_147_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r26 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r26.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r26.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_147_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r27 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r27.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r27.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_147_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 86)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_147_span_2_Template, 2, 0, \"span\", 87)(3, RequirementManagementComponent_ng_template_147_span_3_Template, 2, 0, \"span\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 88)(5, \"div\", 7)(6, \"div\", 89)(7, \"div\", 7)(8, \"app-form-group\", 91)(9, \"input\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_147_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRequirement, $event) || (ctx_r2.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"app-form-group\", 91)(11, \"input\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_147_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CGroupName, $event) || (ctx_r2.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"app-form-group\", 91)(13, \"input\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_147_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CSort, $event) || (ctx_r2.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"app-form-group\", 91)(15, \"nb-select\", 95);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_147_Template_nb_select_selectedChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CHouseType, $event) || (ctx_r2.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(16, RequirementManagementComponent_ng_template_147_nb_option_16_Template, 2, 2, \"nb-option\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 91)(18, \"nb-select\", 97);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_147_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CStatus, $event) || (ctx_r2.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_147_nb_option_19_Template, 2, 2, \"nb-option\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 91)(21, \"input\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_147_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnitPrice, $event) || (ctx_r2.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"app-form-group\", 91)(23, \"input\", 99);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_147_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnit, $event) || (ctx_r2.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"app-form-group\", 91)(25, \"nb-checkbox\", 100);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_147_Template_nb_checkbox_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CIsShow, $event) || (ctx_r2.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(26, \" \\u986F\\u793A\\u5728\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 91)(28, \"textarea\", 101);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_147_Template_textarea_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRemark, $event) || (ctx_r2.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(29, \"nb-card-footer\")(30, \"div\", 7)(31, \"div\", 102)(32, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_147_Template_button_click_32_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r25).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveTemplate(ref_r28));\n    });\n    i0.ɵɵtext(33, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_147_Template_button_click_34_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r25).dialogRef;\n      return i0.ɵɵresetView(ref_r28.close());\n    });\n    i0.ɵɵtext(35, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CHouseType);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.currentTab === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u521D\\u6B65\\u9700\\u6C42\")(\"labelFor\", \"CIsShow\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_149_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-template-viewer\", 106);\n    i0.ɵɵlistener(\"selectTemplate\", function RequirementManagementComponent_ng_template_149_Template_app_template_viewer_selectTemplate_0_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSelectTemplate($event));\n    })(\"close\", function RequirementManagementComponent_ng_template_149_Template_app_template_viewer_close_0_listener() {\n      const ref_r30 = i0.ɵɵrestoreView(_r29).dialogRef;\n      return i0.ɵɵresetView(ref_r30.close());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"templateType\", 1);\n  }\n}\nfunction RequirementManagementComponent_ng_template_151_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-template-creator\", 107);\n    i0.ɵɵlistener(\"templateCreated\", function RequirementManagementComponent_ng_template_151_Template_app_template_creator_templateCreated_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTemplateCreated());\n    })(\"close\", function RequirementManagementComponent_ng_template_151_Template_app_template_creator_close_0_listener() {\n      const ref_r32 = i0.ɵɵrestoreView(_r31).dialogRef;\n      return i0.ɵɵresetView(ref_r32.close());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"availableData\", ctx_r2.selectedRequirementsForTemplate)(\"templateType\", 1);\n  }\n}\nexport let RequirementManagementComponent = /*#__PURE__*/(() => {\n  class RequirementManagementComponent extends BaseComponent {\n    constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n      super(_allow);\n      this._allow = _allow;\n      this.enumHelper = enumHelper;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this.buildCaseService = buildCaseService;\n      this.requirementService = requirementService;\n      this.pettern = pettern;\n      this.router = router;\n      this.destroyref = destroyref;\n      // request\n      this.getListRequirementRequest = {};\n      this.getRequirementRequest = {};\n      // response\n      this.buildCaseList = [];\n      this.requirementList = [];\n      this.saveRequirement = {\n        CHouseType: []\n      };\n      this.statusOptions = [{\n        value: 0,\n        label: '停用'\n      }, {\n        value: 1,\n        label: '啟用'\n      }];\n      this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n      this.isNew = false;\n      this.currentBuildCase = 0;\n      this.currentTab = 0; // 追蹤當前選中的 tab\n      this.isCreatingTemplate = false; // 控制是否正在創建模板\n      this.selectedRequirementsForTemplate = []; // 用於模板創建的選中項目\n      // Tab 切換事件處理\n      this.isFirstTabChange = true;\n      this.initializeSearchForm();\n      this.getBuildCaseList();\n    }\n    ngOnInit() {}\n    // 初始化搜尋表單\n    initializeSearchForm() {\n      // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\n      this.getListRequirementRequest.CStatus = -1;\n      this.getListRequirementRequest.CIsShow = null;\n      this.getListRequirementRequest.CRequirement = '';\n      this.getListRequirementRequest.CGroupName = '';\n      // 預設全選所有房屋類型\n      this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n    }\n    // 重置搜尋\n    resetSearch() {\n      this.initializeSearchForm();\n      // 重置後如果有建案資料，重新設定預設選擇第一個建案\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\n        setTimeout(() => {\n          if (this.currentTab === 0) {\n            this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n          } else {\n            this.getListRequirementRequest.CBuildCaseID = 0;\n          }\n          this.getList();\n        }, 0);\n      } else {\n        this.getList();\n      }\n    }\n    getHouseType(hTypes) {\n      if (!hTypes) {\n        return '';\n      }\n      if (!Array.isArray(hTypes)) {\n        hTypes = [hTypes];\n      }\n      let labels = [];\n      hTypes.forEach(htype => {\n        let findH = this.houseType.find(x => x.value == htype);\n        if (findH) {\n          labels.push(findH.label);\n        }\n      });\n      return labels.join(', ');\n    }\n    validation() {\n      this.valid.clear();\n      // 根據當前 tab 決定是否需要驗證建案名稱\n      if (this.currentTab === 0) {\n        // 建案頁面需要驗證建案名稱\n        this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n      }\n      this.valid.required('[需求]', this.saveRequirement.CRequirement);\n      this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n      this.valid.required('[安排]', this.saveRequirement.CSort);\n      this.valid.required('[狀態]', this.saveRequirement.CStatus);\n      this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n      this.valid.required('[單位]', this.saveRequirement.CUnit);\n      // 群組名稱長度驗證\n      if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n        this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n      }\n      // 備註說明長度驗證\n      if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n        this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n      }\n    }\n    add(dialog) {\n      this.isNew = true;\n      this.saveRequirement = {\n        CHouseType: [],\n        CIsShow: false\n      };\n      this.saveRequirement.CStatus = 1;\n      this.saveRequirement.CUnitPrice = 0;\n      // 根據當前 tab 決定是否需要建案ID\n      if (this.currentTab === 0) {\n        // 建案頁面 - 使用當前選擇的建案或第一個建案\n        if (this.currentBuildCase != 0) {\n          this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n        } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n          this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n        }\n      } else {\n        // 模板頁面 - 設定建案ID為0，CHouseType預設[1,2]\n        this.saveRequirement.CBuildCaseID = 0;\n        this.saveRequirement.CHouseType = [1, 2];\n      }\n      this.dialogService.open(dialog);\n    }\n    onEdit(data, dialog) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n        _this.isNew = false;\n        try {\n          yield _this.getData();\n          // 編輯時如果是共用tab，CHouseType強制為[1,2]\n          if (_this.currentTab === 1) {\n            _this.saveRequirement.CHouseType = [1, 2];\n          }\n          _this.dialogService.open(dialog);\n        } catch (error) {\n          console.log(\"Failed to get function data\", error);\n        }\n      })();\n    }\n    save(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      // 如果是模板頁面，確保 CBuildCaseID 設定為 0\n      if (this.currentTab === 1) {\n        this.saveRequirement.CBuildCaseID = 0;\n      }\n      this.requirementService.apiRequirementSaveDataPost$Json({\n        body: this.saveRequirement\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getList();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n      ref.close();\n    }\n    onDelete(data) {\n      this.saveRequirement.CRequirementID = data.CRequirementID;\n      this.isNew = false;\n      if (window.confirm('是否確定刪除?')) {\n        this.remove();\n      } else {\n        return;\n      }\n    }\n    remove() {\n      this.requirementService.apiRequirementDeleteDataPost$Json({\n        body: {\n          CRequirementID: this.saveRequirement.CRequirementID\n        }\n      }).subscribe(res => {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      });\n    }\n    getBuildCaseList() {\n      this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n        body: {}\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n        this.buildCaseList = res.Entries;\n        // 只在建案 tab 下且有建案時才查詢\n        if (this.currentTab === 0 && this.buildCaseList.length > 0) {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n          this.getList();\n        } else if (this.currentTab === 1) {\n          this.getListRequirementRequest.CBuildCaseID = 0;\n          this.getList();\n        }\n      });\n    }\n    getList() {\n      this.getListRequirementRequest.PageSize = this.pageSize;\n      this.getListRequirementRequest.PageIndex = this.pageIndex;\n      this.requirementList = [];\n      this.totalRecords = 0;\n      // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動，CHouseType預設為[1,2]\n      if (this.currentTab === 1) {\n        this.getListRequirementRequest.CBuildCaseID = 0;\n        this.getListRequirementRequest.CHouseType = [1, 2];\n      } else {\n        // 建案頁面的邏輯保持不變\n        if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n          this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n        }\n      }\n      this.requirementService.apiRequirementGetListPost$Json({\n        body: this.getListRequirementRequest\n      }).pipe().subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries) {\n            // 將 API 返回的數據轉換為 SelectableRequirement 並初始化 selected 屬性\n            this.requirementList = res.Entries.map(item => ({\n              ...item,\n              selected: false\n            }));\n            this.totalRecords = res.TotalItems;\n          }\n        }\n      });\n    }\n    getData() {\n      this.requirementService.apiRequirementGetDataPost$Json({\n        body: this.getRequirementRequest\n      }).pipe().subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries) {\n            this.saveRequirement = {\n              CHouseType: [],\n              CIsShow: false\n            };\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\n            // 共用tab時CHouseType強制為[1,2]\n            this.saveRequirement.CHouseType = this.currentTab === 1 ? [1, 2] : res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n            this.saveRequirement.CRemark = res.Entries.CRemark;\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n            this.saveRequirement.CSort = res.Entries.CSort;\n            this.saveRequirement.CStatus = res.Entries.CStatus;\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\n            this.saveRequirement.CUnit = res.Entries.CUnit;\n            // TODO: 等後端API更新後啟用這行\n            this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n          }\n        }\n      });\n    }\n    onHouseTypeChange(value, checked) {\n      console.log(checked);\n      if (checked) {\n        if (!this.saveRequirement.CHouseType?.includes(value)) {\n          this.saveRequirement.CHouseType?.push(value);\n        }\n        console.log(this.saveRequirement.CHouseType);\n      } else {\n        this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n      }\n    }\n    getCIsShowText(data) {\n      return data.CIsShow ? '是' : '否';\n    }\n    onTabChange(event) {\n      // 避免頁面初始化時自動觸發重複查詢\n      if (this.isFirstTabChange) {\n        this.isFirstTabChange = false;\n        return;\n      }\n      // 根據 tabTitle 來判斷當前頁面\n      if (event.tabTitle === '共用') {\n        this.currentTab = 1;\n        this.getListRequirementRequest.CBuildCaseID = 0;\n      } else {\n        this.currentTab = 0;\n        // 切換回建案頁面時，如果有建案資料，預設選擇第一個\n        if (this.buildCaseList && this.buildCaseList.length > 0) {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        }\n      }\n      this.getList();\n    }\n    // 新增模板\n    addTemplate(dialog) {\n      this.isNew = true;\n      this.saveRequirement = {\n        CHouseType: [],\n        CIsShow: false\n      };\n      this.saveRequirement.CStatus = 1;\n      this.saveRequirement.CUnitPrice = 0;\n      // 模板設定建案ID為0，CHouseType預設[1,2]\n      this.saveRequirement.CBuildCaseID = 0;\n      this.saveRequirement.CHouseType = [1, 2];\n      this.dialogService.open(dialog);\n    }\n    // 編輯模板\n    onEditTemplate(data, dialog) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n        _this2.isNew = false;\n        try {\n          yield _this2.getData();\n          // 編輯模板時CHouseType強制為[1,2]\n          _this2.saveRequirement.CHouseType = [1, 2];\n          _this2.dialogService.open(dialog);\n        } catch (error) {\n          console.log(\"Failed to get template data\", error);\n        }\n      })();\n    }\n    // 保存模板\n    saveTemplate(ref) {\n      // 模板驗證（不包含建案名稱）\n      this.valid.clear();\n      this.valid.required('[需求]', this.saveRequirement.CRequirement);\n      // CHouseType強制為[1,2]\n      if (this.currentTab === 1) {\n        this.saveRequirement.CHouseType = [1, 2];\n      }\n      this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n      this.valid.required('[安排]', this.saveRequirement.CSort);\n      this.valid.required('[狀態]', this.saveRequirement.CStatus);\n      this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n      this.valid.required('[單位]', this.saveRequirement.CUnit);\n      // 群組名稱長度驗證\n      if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n        this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n      }\n      // 備註說明長度驗證\n      if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n        this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n      }\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      // 確保模板建案ID為0\n      const templateData = {\n        ...this.saveRequirement\n      };\n      templateData.CBuildCaseID = 0;\n      // CHouseType強制為[1,2]\n      if (this.currentTab === 1) {\n        templateData.CHouseType = [1, 2];\n      }\n      this.requirementService.apiRequirementSaveDataPost$Json({\n        body: templateData\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getList();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n      ref.close();\n    }\n    openTemplateViewer(templateViewerDialog) {\n      this.dialogService.open(templateViewerDialog);\n    }\n    onSelectTemplate(tpl) {\n      // 查看模板邏輯\n    }\n    // 獲取選中的需求項目\n    getSelectedRequirements() {\n      return this.requirementList.filter(req => req.selected);\n    }\n    // 選中狀態變更處理\n    onRequirementSelectionChange() {\n      // 可以在這裡添加額外的邏輯，比如更新選中計數等\n    }\n    // 全選功能\n    selectAllRequirements() {\n      this.requirementList.forEach(req => req.selected = true);\n    }\n    // 清除所有選擇\n    clearAllSelections() {\n      this.requirementList.forEach(req => req.selected = false);\n    }\n    // 檢查是否全選\n    isAllSelected() {\n      return this.requirementList.length > 0 && this.requirementList.every(req => req.selected);\n    }\n    // 檢查是否部分選中（用於 indeterminate 狀態）\n    isIndeterminate() {\n      const selectedCount = this.requirementList.filter(req => req.selected).length;\n      return selectedCount > 0 && selectedCount < this.requirementList.length;\n    }\n    // 切換全選狀態\n    toggleSelectAll(event) {\n      const isChecked = event.target.checked;\n      this.requirementList.forEach(req => req.selected = isChecked);\n    }\n    // 打開模板創建器\n    openTemplateCreator(templateCreatorDialog) {\n      const selectedRequirements = this.getSelectedRequirements();\n      if (selectedRequirements.length === 0) {\n        this.message.showErrorMSG('請先選擇要加入模板的項目');\n        return;\n      }\n      // 將選中的項目存儲在屬性中，確保在模板中保持引用一致性\n      this.selectedRequirementsForTemplate = [...selectedRequirements];\n      this.isCreatingTemplate = true;\n      const dialogRef = this.dialogService.open(templateCreatorDialog);\n      // 當對話框關閉時重置狀態\n      dialogRef.onClose.subscribe(() => {\n        this.isCreatingTemplate = false;\n        this.selectedRequirementsForTemplate = [];\n      });\n    }\n    // 模板創建成功回調\n    onTemplateCreated() {\n      this.message.showSucessMSG('模板創建成功');\n      // 清除選中狀態\n      this.clearAllSelections();\n    }\n    static {\n      this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RequirementManagementComponent,\n        selectors: [[\"app-requirement-management\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 153,\n        vars: 30,\n        consts: [[\"dialog\", \"\"], [\"templateDialog\", \"\"], [\"templateViewerDialog\", \"\"], [\"templateCreatorDialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\", \"pb-0\"], [1, \"col-12\"], [1, \"row\"], [\"class\", \"form-group col-12 col-md-4\", 4, \"ngIf\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [\"for\", \"isShow\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [3, \"changeTab\"], [\"tabTitle\", \"\\u55AE\\u5EFA\\u6848\"], [1, \"pt-3\"], [1, \"col-12\", \"mt-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [\"tabTitle\", \"\\u8DE8\\u5EFA\\u6848\"], [1, \"col-12\", \"mb-3\"], [1, \"page-description-card\"], [1, \"d-flex\", \"align-items-center\", \"mb-2\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"mr-2\"], [1, \"mb-0\", \"text-primary\"], [1, \"mb-2\", \"text-muted\"], [1, \"feature-highlights\"], [1, \"badge\", \"badge-light\", \"mr-2\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"fas\", \"fa-layer-group\", \"mr-1\"], [1, \"badge\", \"badge-light\"], [1, \"fas\", \"fa-share-alt\", \"mr-1\"], [1, \"template-creation-controls\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"d-flex\", \"align-items-center\"], [1, \"template-action-buttons\", \"mr-3\"], [1, \"btn\", \"btn-primary\", \"mr-2\", 3, \"click\", \"disabled\"], [\"class\", \"badge badge-light ml-1\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"mr-1\"], [1, \"template-status-info\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"class\", \"text-success\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-secondary btn-sm mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"checked\", \"indeterminate\"], [\"class\", \"d-flex\", 3, \"table-active\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"d-flex\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"badge\", \"badge-light\", \"ml-1\"], [1, \"text-muted\"], [1, \"text-success\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-check-square\", \"mr-1\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\", 4, \"ngIf\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CGroupName\", \"name\", \"CGroupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CSort\", \"name\", \"CSort\", \"placeholder\", \"\\u6392\\u5E8F\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\", \"disabled\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CUnit\", \"name\", \"CUnit\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsShow\", \"name\", \"CIsShow\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CBuildCaseID\", \"name\", \"CBuildCaseID\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\"], [3, \"selectTemplate\", \"close\", \"templateType\"], [3, \"templateCreated\", \"close\", \"availableData\", \"templateType\"]],\n        template: function RequirementManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 4)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\", 5)(4, \"div\", 6)(5, \"div\", 7);\n            i0.ɵɵtemplate(6, RequirementManagementComponent_div_6_Template, 5, 2, \"div\", 8);\n            i0.ɵɵelementStart(7, \"div\", 9)(8, \"label\", 10);\n            i0.ɵɵtext(9, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 9)(12, \"label\", 12);\n            i0.ɵɵtext(13, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CGroupName, $event) || (ctx.getListRequirementRequest.CGroupName = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(15, \"div\", 7)(16, \"div\", 9)(17, \"label\", 14);\n            i0.ɵɵtext(18, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"nb-select\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_19_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(20, RequirementManagementComponent_nb_option_20_Template, 2, 2, \"nb-option\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 9)(22, \"label\", 17);\n            i0.ɵɵtext(23, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"nb-select\", 18);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_24_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(25, \"nb-option\", 19);\n            i0.ɵɵtext(26, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"nb-option\", 19);\n            i0.ɵɵtext(28, \"\\u555F\\u7528\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"nb-option\", 19);\n            i0.ɵɵtext(30, \"\\u505C\\u7528\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"div\", 9)(32, \"label\", 20);\n            i0.ɵɵtext(33, \"\\u521D\\u6B65\\u9700\\u6C42\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"nb-select\", 18);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsShow, $event) || (ctx.getListRequirementRequest.CIsShow = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(35, \"nb-option\", 19);\n            i0.ɵɵtext(36, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"nb-option\", 19);\n            i0.ɵɵtext(38, \"\\u662F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"nb-option\", 19);\n            i0.ɵɵtext(40, \"\\u5426\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(41, \"div\", 7);\n            i0.ɵɵelement(42, \"div\", 21);\n            i0.ɵɵelementStart(43, \"div\", 22)(44, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_44_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.resetSearch());\n            });\n            i0.ɵɵelement(45, \"i\", 24);\n            i0.ɵɵtext(46, \"\\u91CD\\u7F6E\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_47_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getList());\n            });\n            i0.ɵɵelement(48, \"i\", 26);\n            i0.ɵɵtext(49, \"\\u67E5\\u8A62\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(50, RequirementManagementComponent_button_50_Template, 3, 0, \"button\", 27);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(51, \"nb-card-body\", 5)(52, \"nb-tabset\", 28);\n            i0.ɵɵlistener(\"changeTab\", function RequirementManagementComponent_Template_nb_tabset_changeTab_52_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onTabChange($event));\n            });\n            i0.ɵɵelementStart(53, \"nb-tab\", 29)(54, \"div\", 30)(55, \"div\", 31)(56, \"div\", 32)(57, \"table\", 33)(58, \"thead\")(59, \"tr\", 34)(60, \"th\", 35);\n            i0.ɵɵtext(61, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"th\", 35);\n            i0.ɵɵtext(63, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"th\", 36);\n            i0.ɵɵtext(65, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"th\", 36);\n            i0.ɵɵtext(67, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(68, \"th\", 36);\n            i0.ɵɵtext(69, \"\\u6392\\u5E8F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(70, \"th\", 36);\n            i0.ɵɵtext(71, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"th\", 36);\n            i0.ɵɵtext(73, \"\\u521D\\u6B65\\u9700\\u6C42\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"th\", 36);\n            i0.ɵɵtext(75, \"\\u55AE\\u50F9\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(76, \"th\", 35);\n            i0.ɵɵtext(77, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(78, \"tbody\");\n            i0.ɵɵtemplate(79, RequirementManagementComponent_tr_79_Template, 22, 15, \"tr\", 37);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(80, \"ngx-pagination\", 38);\n            i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_80_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_80_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getList());\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(81, \"nb-tab\", 39)(82, \"div\", 30)(83, \"div\", 40)(84, \"div\", 41)(85, \"div\", 42);\n            i0.ɵɵelement(86, \"i\", 43);\n            i0.ɵɵelementStart(87, \"h6\", 44);\n            i0.ɵɵtext(88, \"\\u5171\\u7528\\u9700\\u6C42\\u7BA1\\u7406\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(89, \"p\", 45);\n            i0.ɵɵtext(90, \" \\u6B64\\u9801\\u9762\\u7528\\u65BC\\u7BA1\\u7406\\u5171\\u7528\\u7684\\u5BA2\\u8B8A\\u9700\\u6C42\\u9805\\u76EE\\uFF0C\\u9019\\u4E9B\\u9805\\u76EE\\u53EF\\u4EE5\\u88AB\\u591A\\u500B\\u5EFA\\u6848\\u91CD\\u8907\\u4F7F\\u7528\\u3002 \\u60A8\\u53EF\\u4EE5\\u5728\\u6B64\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u6216\\u522A\\u9664\\u5171\\u7528\\u9700\\u6C42\\uFF0C\\u4E5F\\u53EF\\u4EE5\\u5C07\\u9078\\u4E2D\\u7684\\u9700\\u6C42\\u9805\\u76EE\\u5EFA\\u7ACB\\u70BA\\u6A21\\u677F\\u3002 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"div\", 46)(92, \"span\", 47);\n            i0.ɵɵelement(93, \"i\", 48);\n            i0.ɵɵtext(94, \"\\u65B0\\u589E\\u5171\\u7528\\u9700\\u6C42 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(95, \"span\", 47);\n            i0.ɵɵelement(96, \"i\", 49);\n            i0.ɵɵtext(97, \"\\u5EFA\\u7ACB\\u6A21\\u677F \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"span\", 50);\n            i0.ɵɵelement(99, \"i\", 51);\n            i0.ɵɵtext(100, \"\\u8DE8\\u5EFA\\u6848\\u5171\\u7528 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(101, \"div\", 40)(102, \"div\", 52)(103, \"div\", 53)(104, \"div\", 54)(105, \"div\", 55)(106, \"button\", 56);\n            i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_106_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const templateCreatorDialog_r11 = i0.ɵɵreference(152);\n              return i0.ɵɵresetView(ctx.openTemplateCreator(templateCreatorDialog_r11));\n            });\n            i0.ɵɵelement(107, \"i\", 48);\n            i0.ɵɵtext(108, \"\\u65B0\\u589E\\u6A21\\u677F \");\n            i0.ɵɵtemplate(109, RequirementManagementComponent_span_109_Template, 2, 1, \"span\", 57);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(110, \"button\", 58);\n            i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_110_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const templateViewerDialog_r12 = i0.ɵɵreference(150);\n              return i0.ɵɵresetView(ctx.openTemplateViewer(templateViewerDialog_r12));\n            });\n            i0.ɵɵelement(111, \"i\", 59);\n            i0.ɵɵtext(112, \"\\u67E5\\u770B\\u6A21\\u677F \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(113, \"div\", 60);\n            i0.ɵɵtemplate(114, RequirementManagementComponent_small_114_Template, 2, 0, \"small\", 61)(115, RequirementManagementComponent_small_115_Template, 2, 1, \"small\", 62);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(116, \"div\", 54);\n            i0.ɵɵtemplate(117, RequirementManagementComponent_button_117_Template, 3, 0, \"button\", 63)(118, RequirementManagementComponent_button_118_Template, 3, 0, \"button\", 64);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(119, \"div\", 31)(120, \"div\", 32)(121, \"table\", 33)(122, \"thead\")(123, \"tr\", 34)(124, \"th\", 36)(125, \"input\", 65);\n            i0.ɵɵlistener(\"change\", function RequirementManagementComponent_Template_input_change_125_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.toggleSelectAll($event));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(126, \"th\", 35);\n            i0.ɵɵtext(127, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(128, \"th\", 35);\n            i0.ɵɵtext(129, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(130, \"th\", 36);\n            i0.ɵɵtext(131, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(132, \"th\", 36);\n            i0.ɵɵtext(133, \"\\u6392\\u5E8F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(134, \"th\", 36);\n            i0.ɵɵtext(135, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(136, \"th\", 36);\n            i0.ɵɵtext(137, \"\\u521D\\u6B65\\u9700\\u6C42\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(138, \"th\", 36);\n            i0.ɵɵtext(139, \"\\u55AE\\u50F9\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(140, \"th\", 35);\n            i0.ɵɵtext(141, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(142, \"tbody\");\n            i0.ɵɵtemplate(143, RequirementManagementComponent_tr_143_Template, 22, 17, \"tr\", 66);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(144, \"ngx-pagination\", 38);\n            i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_144_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_144_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getList());\n            });\n            i0.ɵɵelementEnd()()()()()()()();\n            i0.ɵɵtemplate(145, RequirementManagementComponent_ng_template_145_Template, 39, 44, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(147, RequirementManagementComponent_ng_template_147_Template, 36, 41, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(149, RequirementManagementComponent_ng_template_149_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(151, RequirementManagementComponent_ng_template_151_Template, 1, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentTab === 0);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CGroupName);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", -1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsShow);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", false);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(29);\n            i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n            i0.ɵɵadvance(26);\n            i0.ɵɵproperty(\"disabled\", ctx.getSelectedRequirements().length === 0);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length > 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length > 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.requirementList.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length > 0);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"checked\", ctx.isAllSelected())(\"indeterminate\", ctx.isIndeterminate());\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          }\n        },\n        dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.CheckboxControlValueAccessor, i9.NgControlStatus, i9.MaxLengthValidator, i9.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent, NumberWithCommasPipe, NbTabsetModule, i3.NbTabsetComponent, i3.NbTabComponent, TemplateViewerComponent, TemplateCreatorComponent],\n        styles: [\".table-active[_ngcontent-%COMP%]{background-color:#e3f2fd!important;border-left:3px solid #2196f3}.page-description-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa,#e9ecef);padding:1.25rem;border-radius:10px;border:1px solid #dee2e6;box-shadow:0 2px 4px #0000000d;margin-bottom:1rem}.page-description-card[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-weight:600;color:#007bff}.page-description-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem;line-height:1.5;margin-bottom:.75rem}.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background-color:#fff;color:#6c757d;border:1px solid #dee2e6;font-size:.8rem;padding:.4rem .8rem;font-weight:500}.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#007bff}.template-creation-controls[_ngcontent-%COMP%]{background:#f8f9fa;padding:1rem;border-radius:8px;margin-bottom:1rem;border:1px solid #e9ecef}.template-creation-controls[_ngcontent-%COMP%]   .template-action-buttons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.template-creation-controls[_ngcontent-%COMP%]   .template-status-info[_ngcontent-%COMP%]{margin-left:1rem}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff,#0056b3);border:none;font-weight:500;transition:all .2s ease}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 4px 12px #007bff4d}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:disabled{background:#6c757d;cursor:not-allowed}.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background:#fff3;color:#fff;font-size:.75rem}.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]{border-color:#6c757d;color:#6c757d;transition:all .2s ease}.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#6c757d;border-color:#6c757d;color:#fff}.template-creation-controls[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{font-size:.875rem;font-style:italic}.template-creation-controls[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]{transform:scale(1.1);margin:0}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:all .2s ease}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-active[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_selectRow .3s ease-out}@keyframes _ngcontent-%COMP%_selectRow{0%{background-color:transparent;transform:scale(1)}50%{transform:scale(1.01)}to{background-color:#e3f2fd;transform:scale(1)}}@media (max-width: 768px){.template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}.template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:row;gap:.25rem}.template-creation-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.875rem;padding:.375rem .75rem}}\"]\n      });\n    }\n  }\n  return RequirementManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}