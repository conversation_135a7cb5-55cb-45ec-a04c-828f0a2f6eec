{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesData from '../helper/createSeriesData.js';\nimport SeriesModel from '../../model/Series.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { Group } from '../../util/graphic.js';\nvar LineSeriesModel = /** @class */function (_super) {\n  __extends(LineSeriesModel, _super);\n  function LineSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LineSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  LineSeriesModel.prototype.getInitialData = function (option) {\n    if (process.env.NODE_ENV !== 'production') {\n      var coordSys = option.coordinateSystem;\n      if (coordSys !== 'polar' && coordSys !== 'cartesian2d') {\n        throw new Error('Line not support coordinateSystem besides cartesian and polar');\n      }\n    }\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  LineSeriesModel.prototype.getLegendIcon = function (opt) {\n    var group = new Group();\n    var line = createSymbol('line', 0, opt.itemHeight / 2, opt.itemWidth, 0, opt.lineStyle.stroke, false);\n    group.add(line);\n    line.setStyle(opt.lineStyle);\n    var visualType = this.getData().getVisual('symbol');\n    var visualRotate = this.getData().getVisual('symbolRotate');\n    var symbolType = visualType === 'none' ? 'circle' : visualType;\n    // Symbol size is 80% when there is a line\n    var size = opt.itemHeight * 0.8;\n    var symbol = createSymbol(symbolType, (opt.itemWidth - size) / 2, (opt.itemHeight - size) / 2, size, size, opt.itemStyle.fill);\n    group.add(symbol);\n    symbol.setStyle(opt.itemStyle);\n    var symbolRotate = opt.iconRotate === 'inherit' ? visualRotate : opt.iconRotate || 0;\n    symbol.rotation = symbolRotate * Math.PI / 180;\n    symbol.setOrigin([opt.itemWidth / 2, opt.itemHeight / 2]);\n    if (symbolType.indexOf('empty') > -1) {\n      symbol.style.stroke = symbol.style.fill;\n      symbol.style.fill = '#fff';\n      symbol.style.lineWidth = 2;\n    }\n    return group;\n  };\n  LineSeriesModel.type = 'series.line';\n  LineSeriesModel.dependencies = ['grid', 'polar'];\n  LineSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 3,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    clip: true,\n    label: {\n      position: 'top'\n    },\n    // itemStyle: {\n    // },\n    endLabel: {\n      show: false,\n      valueAnimation: true,\n      distance: 8\n    },\n    lineStyle: {\n      width: 2,\n      type: 'solid'\n    },\n    emphasis: {\n      scale: true\n    },\n    // areaStyle: {\n    // origin of areaStyle. Valid values:\n    // `'auto'/null/undefined`: from axisLine to data\n    // `'start'`: from min to data\n    // `'end'`: from data to max\n    // origin: 'auto'\n    // },\n    // false, 'start', 'end', 'middle'\n    step: false,\n    // Disabled if step is true\n    smooth: false,\n    smoothMonotone: null,\n    symbol: 'emptyCircle',\n    symbolSize: 4,\n    symbolRotate: null,\n    showSymbol: true,\n    // `false`: follow the label interval strategy.\n    // `true`: show all symbols.\n    // `'auto'`: If possible, show all symbols, otherwise\n    //           follow the label interval strategy.\n    showAllSymbol: 'auto',\n    // Whether to connect break point.\n    connectNulls: false,\n    // Sampling for large data. Can be: 'average', 'max', 'min', 'sum', 'lttb'.\n    sampling: 'none',\n    animationEasing: 'linear',\n    // Disable progressive\n    progressive: 0,\n    hoverLayerThreshold: Infinity,\n    universalTransition: {\n      divideShape: 'clone'\n    },\n    triggerLineEvent: false\n  };\n  return LineSeriesModel;\n}(SeriesModel);\nexport default LineSeriesModel;", "map": {"version": 3, "names": ["__extends", "createSeriesData", "SeriesModel", "createSymbol", "Group", "LineSeriesModel", "_super", "_this", "apply", "arguments", "type", "hasSymbolVisual", "prototype", "getInitialData", "option", "process", "env", "NODE_ENV", "coordSys", "coordinateSystem", "Error", "useEncodeDefaulter", "getLegendIcon", "opt", "group", "line", "itemHeight", "itemWidth", "lineStyle", "stroke", "add", "setStyle", "visualType", "getData", "getVisual", "visualRotate", "symbolType", "size", "symbol", "itemStyle", "fill", "symbolRotate", "iconRotate", "rotation", "Math", "PI", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "style", "lineWidth", "dependencies", "defaultOption", "z", "legendHoverLink", "clip", "label", "position", "endLabel", "show", "valueAnimation", "distance", "width", "emphasis", "scale", "step", "smooth", "smoothMonotone", "symbolSize", "showSymbol", "showAllSymbol", "connectNulls", "sampling", "animationEasing", "progressive", "hoverLayerThreshold", "Infinity", "universalTransition", "divideShape", "triggerLineEvent"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/line/LineSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesData from '../helper/createSeriesData.js';\nimport SeriesModel from '../../model/Series.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport { Group } from '../../util/graphic.js';\nvar LineSeriesModel = /** @class */function (_super) {\n  __extends(LineSeriesModel, _super);\n  function LineSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LineSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  LineSeriesModel.prototype.getInitialData = function (option) {\n    if (process.env.NODE_ENV !== 'production') {\n      var coordSys = option.coordinateSystem;\n      if (coordSys !== 'polar' && coordSys !== 'cartesian2d') {\n        throw new Error('Line not support coordinateSystem besides cartesian and polar');\n      }\n    }\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  LineSeriesModel.prototype.getLegendIcon = function (opt) {\n    var group = new Group();\n    var line = createSymbol('line', 0, opt.itemHeight / 2, opt.itemWidth, 0, opt.lineStyle.stroke, false);\n    group.add(line);\n    line.setStyle(opt.lineStyle);\n    var visualType = this.getData().getVisual('symbol');\n    var visualRotate = this.getData().getVisual('symbolRotate');\n    var symbolType = visualType === 'none' ? 'circle' : visualType;\n    // Symbol size is 80% when there is a line\n    var size = opt.itemHeight * 0.8;\n    var symbol = createSymbol(symbolType, (opt.itemWidth - size) / 2, (opt.itemHeight - size) / 2, size, size, opt.itemStyle.fill);\n    group.add(symbol);\n    symbol.setStyle(opt.itemStyle);\n    var symbolRotate = opt.iconRotate === 'inherit' ? visualRotate : opt.iconRotate || 0;\n    symbol.rotation = symbolRotate * Math.PI / 180;\n    symbol.setOrigin([opt.itemWidth / 2, opt.itemHeight / 2]);\n    if (symbolType.indexOf('empty') > -1) {\n      symbol.style.stroke = symbol.style.fill;\n      symbol.style.fill = '#fff';\n      symbol.style.lineWidth = 2;\n    }\n    return group;\n  };\n  LineSeriesModel.type = 'series.line';\n  LineSeriesModel.dependencies = ['grid', 'polar'];\n  LineSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 3,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    clip: true,\n    label: {\n      position: 'top'\n    },\n    // itemStyle: {\n    // },\n    endLabel: {\n      show: false,\n      valueAnimation: true,\n      distance: 8\n    },\n    lineStyle: {\n      width: 2,\n      type: 'solid'\n    },\n    emphasis: {\n      scale: true\n    },\n    // areaStyle: {\n    // origin of areaStyle. Valid values:\n    // `'auto'/null/undefined`: from axisLine to data\n    // `'start'`: from min to data\n    // `'end'`: from data to max\n    // origin: 'auto'\n    // },\n    // false, 'start', 'end', 'middle'\n    step: false,\n    // Disabled if step is true\n    smooth: false,\n    smoothMonotone: null,\n    symbol: 'emptyCircle',\n    symbolSize: 4,\n    symbolRotate: null,\n    showSymbol: true,\n    // `false`: follow the label interval strategy.\n    // `true`: show all symbols.\n    // `'auto'`: If possible, show all symbols, otherwise\n    //           follow the label interval strategy.\n    showAllSymbol: 'auto',\n    // Whether to connect break point.\n    connectNulls: false,\n    // Sampling for large data. Can be: 'average', 'max', 'min', 'sum', 'lttb'.\n    sampling: 'none',\n    animationEasing: 'linear',\n    // Disable progressive\n    progressive: 0,\n    hoverLayerThreshold: Infinity,\n    universalTransition: {\n      divideShape: 'clone'\n    },\n    triggerLineEvent: false\n  };\n  return LineSeriesModel;\n}(SeriesModel);\nexport default LineSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,IAAIC,eAAe,GAAG,aAAa,UAAUC,MAAM,EAAE;EACnDN,SAAS,CAACK,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAAA,EAAG;IACzB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,eAAe,CAACK,IAAI;IACjCH,KAAK,CAACI,eAAe,GAAG,IAAI;IAC5B,OAAOJ,KAAK;EACd;EACAF,eAAe,CAACO,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAE;IAC3D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIC,QAAQ,GAAGJ,MAAM,CAACK,gBAAgB;MACtC,IAAID,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,aAAa,EAAE;QACtD,MAAM,IAAIE,KAAK,CAAC,+DAA+D,CAAC;MAClF;IACF;IACA,OAAOnB,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE;MAClCoB,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EACDhB,eAAe,CAACO,SAAS,CAACU,aAAa,GAAG,UAAUC,GAAG,EAAE;IACvD,IAAIC,KAAK,GAAG,IAAIpB,KAAK,CAAC,CAAC;IACvB,IAAIqB,IAAI,GAAGtB,YAAY,CAAC,MAAM,EAAE,CAAC,EAAEoB,GAAG,CAACG,UAAU,GAAG,CAAC,EAAEH,GAAG,CAACI,SAAS,EAAE,CAAC,EAAEJ,GAAG,CAACK,SAAS,CAACC,MAAM,EAAE,KAAK,CAAC;IACrGL,KAAK,CAACM,GAAG,CAACL,IAAI,CAAC;IACfA,IAAI,CAACM,QAAQ,CAACR,GAAG,CAACK,SAAS,CAAC;IAC5B,IAAII,UAAU,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC,CAACC,SAAS,CAAC,QAAQ,CAAC;IACnD,IAAIC,YAAY,GAAG,IAAI,CAACF,OAAO,CAAC,CAAC,CAACC,SAAS,CAAC,cAAc,CAAC;IAC3D,IAAIE,UAAU,GAAGJ,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAGA,UAAU;IAC9D;IACA,IAAIK,IAAI,GAAGd,GAAG,CAACG,UAAU,GAAG,GAAG;IAC/B,IAAIY,MAAM,GAAGnC,YAAY,CAACiC,UAAU,EAAE,CAACb,GAAG,CAACI,SAAS,GAAGU,IAAI,IAAI,CAAC,EAAE,CAACd,GAAG,CAACG,UAAU,GAAGW,IAAI,IAAI,CAAC,EAAEA,IAAI,EAAEA,IAAI,EAAEd,GAAG,CAACgB,SAAS,CAACC,IAAI,CAAC;IAC9HhB,KAAK,CAACM,GAAG,CAACQ,MAAM,CAAC;IACjBA,MAAM,CAACP,QAAQ,CAACR,GAAG,CAACgB,SAAS,CAAC;IAC9B,IAAIE,YAAY,GAAGlB,GAAG,CAACmB,UAAU,KAAK,SAAS,GAAGP,YAAY,GAAGZ,GAAG,CAACmB,UAAU,IAAI,CAAC;IACpFJ,MAAM,CAACK,QAAQ,GAAGF,YAAY,GAAGG,IAAI,CAACC,EAAE,GAAG,GAAG;IAC9CP,MAAM,CAACQ,SAAS,CAAC,CAACvB,GAAG,CAACI,SAAS,GAAG,CAAC,EAAEJ,GAAG,CAACG,UAAU,GAAG,CAAC,CAAC,CAAC;IACzD,IAAIU,UAAU,CAACW,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MACpCT,MAAM,CAACU,KAAK,CAACnB,MAAM,GAAGS,MAAM,CAACU,KAAK,CAACR,IAAI;MACvCF,MAAM,CAACU,KAAK,CAACR,IAAI,GAAG,MAAM;MAC1BF,MAAM,CAACU,KAAK,CAACC,SAAS,GAAG,CAAC;IAC5B;IACA,OAAOzB,KAAK;EACd,CAAC;EACDnB,eAAe,CAACK,IAAI,GAAG,aAAa;EACpCL,eAAe,CAAC6C,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;EAChD7C,eAAe,CAAC8C,aAAa,GAAG;IAC9B;IACAC,CAAC,EAAE,CAAC;IACJjC,gBAAgB,EAAE,aAAa;IAC/BkC,eAAe,EAAE,IAAI;IACrBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACLC,QAAQ,EAAE;IACZ,CAAC;IACD;IACA;IACAC,QAAQ,EAAE;MACRC,IAAI,EAAE,KAAK;MACXC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE;IACZ,CAAC;IACDhC,SAAS,EAAE;MACTiC,KAAK,EAAE,CAAC;MACRnD,IAAI,EAAE;IACR,CAAC;IACDoD,QAAQ,EAAE;MACRC,KAAK,EAAE;IACT,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,IAAI,EAAE,KAAK;IACX;IACAC,MAAM,EAAE,KAAK;IACbC,cAAc,EAAE,IAAI;IACpB5B,MAAM,EAAE,aAAa;IACrB6B,UAAU,EAAE,CAAC;IACb1B,YAAY,EAAE,IAAI;IAClB2B,UAAU,EAAE,IAAI;IAChB;IACA;IACA;IACA;IACAC,aAAa,EAAE,MAAM;IACrB;IACAC,YAAY,EAAE,KAAK;IACnB;IACAC,QAAQ,EAAE,MAAM;IAChBC,eAAe,EAAE,QAAQ;IACzB;IACAC,WAAW,EAAE,CAAC;IACdC,mBAAmB,EAAEC,QAAQ;IAC7BC,mBAAmB,EAAE;MACnBC,WAAW,EAAE;IACf,CAAC;IACDC,gBAAgB,EAAE;EACpB,CAAC;EACD,OAAOzE,eAAe;AACxB,CAAC,CAACH,WAAW,CAAC;AACd,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}