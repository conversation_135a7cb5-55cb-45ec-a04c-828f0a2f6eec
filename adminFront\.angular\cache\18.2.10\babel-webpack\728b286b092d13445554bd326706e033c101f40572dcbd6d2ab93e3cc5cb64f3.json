{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nfunction StandardHousePlanComponent_nb_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r2.label, \" \");\n  }\n}\nfunction StandardHousePlanComponent_tr_29_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i_r4.CHouseHold, \" \\u3001 \");\n  }\n}\nfunction StandardHousePlanComponent_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtemplate(4, StandardHousePlanComponent_tr_29_span_4_Template, 2, 1, \"span\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CFileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r5.CHouse);\n  }\n}\nfunction StandardHousePlanComponent_ng_template_32_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_ng_template_32_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ref_r8 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.submitEditHouseRegularPic(ref_r8));\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u50B3\\u6A94\\u6848\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StandardHousePlanComponent_ng_template_32_div_6_label_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 32)(1, \"nb-checkbox\", 34);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function StandardHousePlanComponent_ng_template_32_div_6_label_13_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      i0.ɵɵtwoWayBindingSet(i_r13.CIsSelect, $event) || (i_r13.CIsSelect = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function StandardHousePlanComponent_ng_template_32_div_6_label_13_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const idx_r14 = i0.ɵɵnextContext().index;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.checkItem($event, idx_r14, i_r13));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 39);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", i_r13.CIsSelect);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r13.CHouseHold || \"null\");\n  }\n}\nfunction StandardHousePlanComponent_ng_template_32_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"label\", 30);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\")(5, \"label\", 31);\n    i0.ɵɵtext(6, \" \\u9069\\u7528\\u6236\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"label\", 32)(8, \"span\", 33);\n    i0.ɵɵtext(9, \"\\u9069\\u7528\\u6236\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-checkbox\", 34);\n    i0.ɵɵlistener(\"checkedChange\", function StandardHousePlanComponent_ng_template_32_div_6_Template_nb_checkbox_checkedChange_10_listener($event) {\n      const houseRegularPic_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.checkAll($event, houseRegularPic_r11));\n    });\n    i0.ɵɵtext(11, \"\\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 35);\n    i0.ɵɵtemplate(13, StandardHousePlanComponent_ng_template_32_div_6_label_13_Template, 4, 2, \"label\", 36);\n    i0.ɵɵelementStart(14, \"div\", 37)(15, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_ng_template_32_div_6_Template_button_click_15_listener() {\n      const houseRegularPic_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.onDeleteHouseRegularPic(houseRegularPic_r11));\n    });\n    i0.ɵɵtext(16, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const houseRegularPic_r11 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", houseRegularPic_r11.CFileName, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"checked\", ctx_r8.isAllChecked(houseRegularPic_r11));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", houseRegularPic_r11.CHouse);\n  }\n}\nfunction StandardHousePlanComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 21)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6236\\u5225\\u7BA1\\u7406\\u300B\\u8A2D\\u5B9A\\u6236\\u578B\\u6A19\\u6E96\\u5716\\u300B\\u4FEE\\u6539 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 22)(4, \"div\", 23);\n    i0.ɵɵtemplate(5, StandardHousePlanComponent_ng_template_32_button_5_Template, 2, 0, \"button\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, StandardHousePlanComponent_ng_template_32_div_6_Template, 17, 3, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"nb-card-footer\", 18)(8, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_ng_template_32_Template_button_click_8_listener() {\n      const ref_r8 = i0.ɵɵrestoreView(_r6).dialogRef;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onClose(ref_r8));\n    });\n    i0.ɵɵtext(9, \"\\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_ng_template_32_Template_button_click_10_listener() {\n      const ref_r8 = i0.ɵɵrestoreView(_r6).dialogRef;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onClose(ref_r8));\n    });\n    i0.ɵɵtext(11, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.listHouseRegularPic.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.listHouseRegularPic);\n  }\n}\nexport let StandardHousePlanComponent = /*#__PURE__*/(() => {\n  class StandardHousePlanComponent extends BaseComponent {\n    constructor(_allow, dialogService, _houseService, route, message) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this._houseService = _houseService;\n      this.route = route;\n      this.message = message;\n      this.buildingSelectedOptions = [{\n        value: '',\n        label: '全部'\n      }];\n    }\n    getListBuilding() {\n      this._houseService.apiHouseGetListBuildingPost$Json({\n        body: {\n          CBuildCaseID: this.buildCaseId\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.buildingSelectedOptions = [{\n            value: '',\n            label: '全部'\n          }, ...res.Entries.map(e => {\n            return {\n              value: e,\n              label: e\n            };\n          })];\n          this.selectedBuilding = this.buildingSelectedOptions[0];\n          this.getListHouseRegularPic();\n        }\n      });\n    }\n    getListHouseRegularPic() {\n      let param = {\n        CBuildCaseID: this.buildCaseId,\n        CBuildingName: this.selectedBuilding.value,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      };\n      if (!this.selectedBuilding.value) {\n        delete param.CBuildingName;\n      }\n      this._houseService.apiHouseGetListHouseRegularPicPost$Json({\n        body: param\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.listHouseRegularPic = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n        }\n      });\n    }\n    checkAll(checked, houseRegularPic) {\n      if (houseRegularPic.CHouse && houseRegularPic.CHouse.length > 0) {\n        houseRegularPic.CHouse.forEach(item => item.CIsSelect = checked);\n      }\n      if (checked) {\n        this.listHouseRegularPic.forEach((element, index) => {\n          if (element.CRegularPictureID !== houseRegularPic.CRegularPictureID) {\n            if (element.CHouse && Array.isArray(element.CHouse)) {\n              element.CHouse.forEach((item, o) => {\n                item.CIsSelect = false;\n              });\n            }\n          }\n        });\n      }\n    }\n    checkItem(checked, idx, i) {\n      if (checked) {\n        this.listHouseRegularPic.forEach((element, index) => {\n          if (index !== idx) {\n            if (element.CHouse && Array.isArray(element.CHouse)) {\n              element.CHouse.forEach((item, o) => {\n                if (item.CHouseID === i.CHouseID) {\n                  item.CIsSelect = false;\n                }\n              });\n            }\n          }\n        });\n      }\n    }\n    isAllChecked(houseRegularPic) {\n      return houseRegularPic.CHouse.every(item => item.CIsSelect);\n    }\n    extractSelectedHouses(data) {\n      const result = [];\n      for (const item of data) {\n        for (const house of item.CHouse) {\n          if (house.CIsSelect) {\n            result.push({\n              CHouseID: house.CHouseID,\n              CRegularPictureID: item.CRegularPictureID\n            });\n          }\n        }\n      }\n      return result;\n    }\n    submitEditHouseRegularPic(ref) {\n      let bodyHouseRegularPic = this.extractSelectedHouses(this.listHouseRegularPic);\n      this._houseService.apiHouseEditHouseRegularPicPost$Json({\n        body: {\n          CHousePic: bodyHouseRegularPic\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        }\n      });\n    }\n    onDeleteHouseRegularPic(houseRegularPic) {\n      if (window.confirm(`確定要刪除【項目${houseRegularPic.CFileName}】?`)) {\n        this._houseService.apiHouseDeleteRegularPicturePost$Json({\n          body: {\n            CRegularPictureID: houseRegularPic.CRegularPictureID\n          }\n        }).subscribe(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG(\"執行成功\");\n            this.getListHouseRegularPic();\n          }\n        });\n      }\n    }\n    clear() {\n      this.selectedBuilding = this.buildingSelectedOptions[0];\n    }\n    ngOnInit() {\n      this.route.paramMap.subscribe(params => {\n        if (params) {\n          const idParam = params.get('id');\n          const id = idParam ? +idParam : 0;\n          this.buildCaseId = id;\n          this.getListBuilding();\n        }\n      });\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getListHouseRegularPic();\n    }\n    onOpen(ref) {\n      this.dialogService.open(ref);\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    static {\n      this.ɵfac = function StandardHousePlanComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || StandardHousePlanComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.HouseService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.MessageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: StandardHousePlanComponent,\n        selectors: [[\"ngx-standard-house-plan\"]],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 34,\n        vars: 6,\n        consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"1000px\"], [1, \"table-header\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [2, \"width\", \"700px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"d-flex\", \"justify-end\"], [\"class\", \"btn btn-primary mx-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"bg-white p-4 rounded shadow m-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"mx-2\", 3, \"click\"], [1, \"bg-white\", \"p-4\", \"rounded\", \"shadow\", \"m-2\"], [1, \"mb-2\"], [\"for\", \"standard-drawing\", 1, \"block\", \"text-gray-700\", \"font-bold\", \"mb-2\"], [\"for\", \"applicable-models\", 1, \"block\", \"text-gray-700\", \"font-bold\", \"mb-2\"], [1, \"inline-flex\", \"items-center\", \"mr-4\"], [1, \"mr-2\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [1, \"flex\", \"flex-wrap\"], [\"class\", \"inline-flex items-center mr-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"text-right\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-dark\", \"py-2\", \"px-4\", \"btn-sm\", \"ml-6\", 3, \"click\"], [1, \"ml-2\"]],\n        template: function StandardHousePlanComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\");\n            i0.ɵɵelement(4, \"h1\", 2);\n            i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"div\", 5)(8, \"label\", 6);\n            i0.ɵɵtext(9, \"\\u68DF\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"nb-select\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function StandardHousePlanComponent_Template_nb_select_ngModelChange_10_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(11, StandardHousePlanComponent_nb_option_11_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(12, \"div\", 4)(13, \"div\", 9)(14, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_Template_button_click_14_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.clear());\n            });\n            i0.ɵɵtext(15, \" \\u6E05\\u9664 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_Template_button_click_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getListHouseRegularPic());\n            });\n            i0.ɵɵtext(17, \" \\u67E5\\u8A62 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_Template_button_click_18_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const dialog_r3 = i0.ɵɵreference(33);\n              return i0.ɵɵresetView(ctx.onOpen(dialog_r3));\n            });\n            i0.ɵɵtext(19, \" \\u68DF\\u5225 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(20, \"div\", 13)(21, \"table\", 14)(22, \"thead\", 15)(23, \"tr\")(24, \"th\", 16);\n            i0.ɵɵtext(25, \"\\u6A94\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"th\", 16);\n            i0.ɵɵtext(27, \"\\u9069\\u7528\\u6236\\u578B \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(28, \"tbody\");\n            i0.ɵɵtemplate(29, StandardHousePlanComponent_tr_29_Template, 5, 2, \"tr\", 17);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(30, \"nb-card-footer\", 18)(31, \"ngb-pagination\", 19);\n            i0.ɵɵtwoWayListener(\"pageChange\", function StandardHousePlanComponent_Template_ngb_pagination_pageChange_31_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"pageChange\", function StandardHousePlanComponent_Template_ngb_pagination_pageChange_31_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(32, StandardHousePlanComponent_ng_template_32_Template, 12, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.buildingSelectedOptions);\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngForOf\", ctx.listHouseRegularPic);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i7.NgControlStatus, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbSelectComponent, i2.NbOptionComponent, i8.NgbPagination, i9.BreadcrumbComponent]\n      });\n    }\n  }\n  return StandardHousePlanComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}