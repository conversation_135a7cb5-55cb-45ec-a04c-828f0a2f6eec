{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbActionsModule, NbLayoutModule, NbMenuModule, NbSearchModule, NbSidebarModule, NbUserModule, NbContextMenuModule, NbButtonModule, NbSelectModule, NbIconModule, NbThemeModule, NbBadgeModule } from '@nebular/theme';\nimport { NbEvaIconsModule } from '@nebular/eva-icons';\nimport { NbSecurityModule } from '@nebular/security';\nimport { FooterComponent, HeaderComponent, SearchInputComponent, TinyMCEComponent } from './components';\nimport { CapitalizePipe, PluralPipe, RoundPipe, TimingPipe, NumberWithCommasPipe, MomentPipe, BooleanStringPipe, FormatHourPipe, BaseFilePipe, DateFormatPipe, FileNamePipe, HtmlPipe, FilterListItemsPipe } from './pipes';\nimport { OneColumnLayoutComponent, ThreeColumnsLayoutComponent, TwoColumnsLayoutComponent } from './layouts';\nimport { DEFAULT_THEME } from './styles/theme.default';\nimport { COSMIC_THEME } from './styles/theme.cosmic';\nimport { CORPORATE_THEME } from './styles/theme.corporate';\nimport { DARK_THEME } from './styles/theme.dark';\nimport { ApprovalWaitingPipe, DefaultKeyPipe, DocumentStatusPipe, PlanUsePipe, StatusMailPipe, StatusPipe, TaskLogStatusPipe, TaskStatusPipe, TypeMailPipe } from './pipes/mapping.pipe';\nimport { ApproveStatusPipe } from './pipes/approveStatus.pipe';\nimport * as i0 from \"@angular/core\";\nconst NB_MODULES = [NbLayoutModule, NbMenuModule, NbUserModule, NbActionsModule, NbSearchModule, NbSidebarModule, NbContextMenuModule, NbSecurityModule, NbButtonModule, NbSelectModule, NbIconModule, NbEvaIconsModule, NbBadgeModule];\nconst COMPONENTS = [HeaderComponent, FooterComponent, SearchInputComponent, TinyMCEComponent, OneColumnLayoutComponent, ThreeColumnsLayoutComponent, TwoColumnsLayoutComponent];\nconst PIPES = [CapitalizePipe, PluralPipe, RoundPipe, TimingPipe, NumberWithCommasPipe, MomentPipe, StatusPipe, StatusMailPipe, TypeMailPipe, DefaultKeyPipe, TaskStatusPipe, TaskLogStatusPipe, BooleanStringPipe, FormatHourPipe, ApproveStatusPipe, ApprovalWaitingPipe, BaseFilePipe, DateFormatPipe, FileNamePipe, HtmlPipe, PlanUsePipe, FilterListItemsPipe, DocumentStatusPipe];\nexport class ThemeModule {\n  static forRoot() {\n    return {\n      ngModule: ThemeModule,\n      providers: [...NbThemeModule.forRoot({\n        name: 'default'\n      }, [DEFAULT_THEME, COSMIC_THEME, CORPORATE_THEME, DARK_THEME]).providers]\n    };\n  }\n  static {\n    this.ɵfac = function ThemeModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ThemeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ThemeModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, NB_MODULES, HeaderComponent, OneColumnLayoutComponent, ThreeColumnsLayoutComponent, TwoColumnsLayoutComponent, CommonModule, NbLayoutModule, NbMenuModule, NbUserModule, NbActionsModule, NbSearchModule, NbSidebarModule, NbContextMenuModule, NbSecurityModule, NbButtonModule, NbSelectModule, NbIconModule, NbEvaIconsModule, NbBadgeModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ThemeModule, {\n    imports: [CommonModule, NbLayoutModule, NbMenuModule, NbUserModule, NbActionsModule, NbSearchModule, NbSidebarModule, NbContextMenuModule, NbSecurityModule, NbButtonModule, NbSelectModule, NbIconModule, NbEvaIconsModule, NbBadgeModule, HeaderComponent, FooterComponent, SearchInputComponent, TinyMCEComponent, OneColumnLayoutComponent, ThreeColumnsLayoutComponent, TwoColumnsLayoutComponent, CapitalizePipe, PluralPipe, RoundPipe, TimingPipe, NumberWithCommasPipe, MomentPipe, StatusPipe, StatusMailPipe, TypeMailPipe, DefaultKeyPipe, TaskStatusPipe, TaskLogStatusPipe, BooleanStringPipe, FormatHourPipe, ApproveStatusPipe, ApprovalWaitingPipe, BaseFilePipe, DateFormatPipe, FileNamePipe, HtmlPipe, PlanUsePipe, FilterListItemsPipe, DocumentStatusPipe],\n    exports: [CommonModule, CapitalizePipe, PluralPipe, RoundPipe, TimingPipe, NumberWithCommasPipe, MomentPipe, StatusPipe, StatusMailPipe, TypeMailPipe, DefaultKeyPipe, TaskStatusPipe, TaskLogStatusPipe, BooleanStringPipe, FormatHourPipe, ApproveStatusPipe, ApprovalWaitingPipe, BaseFilePipe, DateFormatPipe, FileNamePipe, HtmlPipe, PlanUsePipe, FilterListItemsPipe, DocumentStatusPipe, HeaderComponent, FooterComponent, SearchInputComponent, TinyMCEComponent, OneColumnLayoutComponent, ThreeColumnsLayoutComponent, TwoColumnsLayoutComponent, NbLayoutModule, NbMenuModule, NbUserModule, NbActionsModule, NbSearchModule, NbSidebarModule, NbContextMenuModule, NbSecurityModule, NbButtonModule, NbSelectModule, NbIconModule, NbEvaIconsModule, NbBadgeModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "NbActionsModule", "NbLayoutModule", "NbMenuModule", "NbSearchModule", "NbSidebarModule", "NbUserModule", "NbContextMenuModule", "NbButtonModule", "NbSelectModule", "NbIconModule", "NbThemeModule", "NbBadgeModule", "NbEvaIconsModule", "NbSecurityModule", "FooterComponent", "HeaderComponent", "SearchInputComponent", "TinyMCEComponent", "CapitalizePipe", "PluralPipe", "RoundPipe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NumberWithCommasPipe", "MomentPipe", "BooleanStringPipe", "FormatHourPipe", "BaseFilePipe", "DateFormatPipe", "FileNamePipe", "HtmlPipe", "FilterListItemsPipe", "OneColumnLayoutComponent", "ThreeColumnsLayoutComponent", "TwoColumnsLayoutComponent", "DEFAULT_THEME", "COSMIC_THEME", "CORPORATE_THEME", "DARK_THEME", "ApprovalWaitingPipe", "DefaultKeyPipe", "DocumentStatusPipe", "PlanUsePipe", "StatusMailPipe", "StatusPipe", "TaskLogStatusPipe", "TaskStatusPipe", "TypeMailPipe", "ApproveStatusPipe", "NB_MODULES", "COMPONENTS", "PIPES", "ThemeModule", "forRoot", "ngModule", "providers", "name", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\theme.module.ts"], "sourcesContent": ["import { ModuleWithProviders, NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport {\r\n  NbActionsModule,\r\n  NbLayoutModule,\r\n  NbMenuModule,\r\n  NbSearchModule,\r\n  NbSidebarModule,\r\n  NbUserModule,\r\n  NbContextMenuModule,\r\n  NbButtonModule,\r\n  NbSelectModule,\r\n  NbIconModule,\r\n  NbThemeModule,\r\n  NbBadgeModule,\r\n} from '@nebular/theme';\r\nimport { NbEvaIconsModule } from '@nebular/eva-icons';\r\nimport { NbSecurityModule } from '@nebular/security';\r\n\r\nimport {\r\n  FooterComponent,\r\n  HeaderComponent,\r\n  SearchInputComponent,\r\n  TinyMCEComponent,\r\n} from './components';\r\nimport {\r\n  CapitalizePipe,\r\n  PluralPipe,\r\n  RoundPipe,\r\n  TimingPipe,\r\n  NumberWithCommasPipe,\r\n  MomentPipe,\r\n  BooleanStringPipe,\r\n  FormatHourPipe,\r\n  BaseFilePipe,\r\n  DateFormatPipe,\r\n  FileNamePipe,\r\n  HtmlPipe,\r\n  FilterListItemsPipe\r\n} from './pipes';\r\nimport {\r\n  OneColumnLayoutComponent,\r\n  ThreeColumnsLayoutComponent,\r\n  TwoColumnsLayoutComponent,\r\n} from './layouts';\r\nimport { DEFAULT_THEME } from './styles/theme.default';\r\nimport { COSMIC_THEME } from './styles/theme.cosmic';\r\nimport { CORPORATE_THEME } from './styles/theme.corporate';\r\nimport { DARK_THEME } from './styles/theme.dark';\r\nimport { ApprovalWaitingPipe, DefaultKeyPipe, DocumentStatusPipe, PlanUsePipe, StatusMailPipe, StatusPipe, TaskLogStatusPipe, TaskStatusPipe, TypeMailPipe } from './pipes/mapping.pipe';\r\nimport { ApproveStatusPipe } from './pipes/approveStatus.pipe';\r\n\r\nconst NB_MODULES = [\r\n  NbLayoutModule,\r\n  NbMenuModule,\r\n  NbUserModule,\r\n  NbActionsModule,\r\n  NbSearchModule,\r\n  NbSidebarModule,\r\n  NbContextMenuModule,\r\n  NbSecurityModule,\r\n  NbButtonModule,\r\n  NbSelectModule,\r\n  NbIconModule,\r\n  NbEvaIconsModule,\r\n  NbBadgeModule,\r\n];\r\nconst COMPONENTS = [\r\n  HeaderComponent,\r\n  FooterComponent,\r\n  SearchInputComponent,\r\n  TinyMCEComponent,\r\n  OneColumnLayoutComponent,\r\n  ThreeColumnsLayoutComponent,\r\n  TwoColumnsLayoutComponent,\r\n];\r\nconst PIPES = [\r\n  CapitalizePipe,\r\n  PluralPipe,\r\n  RoundPipe,\r\n  TimingPipe,\r\n  NumberWithCommasPipe,\r\n  MomentPipe,\r\n  StatusPipe,\r\n  StatusMailPipe,\r\n  TypeMailPipe,\r\n  DefaultKeyPipe,\r\n  TaskStatusPipe,\r\n  TaskLogStatusPipe,\r\n  BooleanStringPipe,\r\n  FormatHourPipe,\r\n  ApproveStatusPipe,\r\n  ApprovalWaitingPipe,\r\n  BaseFilePipe,\r\n  DateFormatPipe,\r\n  FileNamePipe,\r\n  HtmlPipe,\r\n  PlanUsePipe,\r\n  FilterListItemsPipe,\r\n  DocumentStatusPipe\r\n];\r\n\r\n@NgModule({\r\n  imports: [CommonModule, ...NB_MODULES, ...COMPONENTS, ...PIPES],\r\n  exports: [CommonModule, ...PIPES, ...COMPONENTS, ...NB_MODULES],\r\n})\r\nexport class ThemeModule {\r\n  static forRoot(): ModuleWithProviders<ThemeModule> {\r\n    return {\r\n      ngModule: ThemeModule,\r\n      providers: [\r\n        ...NbThemeModule.forRoot(\r\n          {\r\n            name: 'default',\r\n          },\r\n          [DEFAULT_THEME, COSMIC_THEME, CORPORATE_THEME, DARK_THEME],\r\n        ).providers!,\r\n      ],\r\n    };\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SACEC,eAAe,EACfC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,YAAY,EACZC,mBAAmB,EACnBC,cAAc,EACdC,cAAc,EACdC,YAAY,EACZC,aAAa,EACbC,aAAa,QACR,gBAAgB;AACvB,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,gBAAgB,QAAQ,mBAAmB;AAEpD,SACEC,eAAe,EACfC,eAAe,EACfC,oBAAoB,EACpBC,gBAAgB,QACX,cAAc;AACrB,SACEC,cAAc,EACdC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,oBAAoB,EACpBC,UAAU,EACVC,iBAAiB,EACjBC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,QAAQ,EACRC,mBAAmB,QACd,SAAS;AAChB,SACEC,wBAAwB,EACxBC,2BAA2B,EAC3BC,yBAAyB,QACpB,WAAW;AAClB,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,mBAAmB,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,cAAc,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,sBAAsB;AACxL,SAASC,iBAAiB,QAAQ,4BAA4B;;AAE9D,MAAMC,UAAU,GAAG,CACjB/C,cAAc,EACdC,YAAY,EACZG,YAAY,EACZL,eAAe,EACfG,cAAc,EACdC,eAAe,EACfE,mBAAmB,EACnBO,gBAAgB,EAChBN,cAAc,EACdC,cAAc,EACdC,YAAY,EACZG,gBAAgB,EAChBD,aAAa,CACd;AACD,MAAMsC,UAAU,GAAG,CACjBlC,eAAe,EACfD,eAAe,EACfE,oBAAoB,EACpBC,gBAAgB,EAChBc,wBAAwB,EACxBC,2BAA2B,EAC3BC,yBAAyB,CAC1B;AACD,MAAMiB,KAAK,GAAG,CACZhC,cAAc,EACdC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,oBAAoB,EACpBC,UAAU,EACVoB,UAAU,EACVD,cAAc,EACdI,YAAY,EACZP,cAAc,EACdM,cAAc,EACdD,iBAAiB,EACjBpB,iBAAiB,EACjBC,cAAc,EACdsB,iBAAiB,EACjBT,mBAAmB,EACnBZ,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,QAAQ,EACRY,WAAW,EACXX,mBAAmB,EACnBU,kBAAkB,CACnB;AAMD,OAAM,MAAOW,WAAW;EACtB,OAAOC,OAAOA,CAAA;IACZ,OAAO;MACLC,QAAQ,EAAEF,WAAW;MACrBG,SAAS,EAAE,CACT,GAAG5C,aAAa,CAAC0C,OAAO,CACtB;QACEG,IAAI,EAAE;OACP,EACD,CAACrB,aAAa,EAAEC,YAAY,EAAEC,eAAe,EAAEC,UAAU,CAAC,CAC3D,CAACiB,SAAU;KAEf;EACH;;;uCAbWH,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAHZpD,YAAY,EAAKiD,UAAU,EAnCrCjC,eAAe,EAIfgB,wBAAwB,EACxBC,2BAA2B,EAC3BC,yBAAyB,EA8BflC,YAAY,EAnDtBE,cAAc,EACdC,YAAY,EACZG,YAAY,EACZL,eAAe,EACfG,cAAc,EACdC,eAAe,EACfE,mBAAmB,EACnBO,gBAAgB,EAChBN,cAAc,EACdC,cAAc,EACdC,YAAY,EACZG,gBAAgB,EAChBD,aAAa;IAAA;EAAA;;;2EAyCFwC,WAAW;IAAAK,OAAA,GAHZzD,YAAY,EAlDtBE,cAAc,EACdC,YAAY,EACZG,YAAY,EACZL,eAAe,EACfG,cAAc,EACdC,eAAe,EACfE,mBAAmB,EACnBO,gBAAgB,EAChBN,cAAc,EACdC,cAAc,EACdC,YAAY,EACZG,gBAAgB,EAChBD,aAAa,EAGbI,eAAe,EACfD,eAAe,EACfE,oBAAoB,EACpBC,gBAAgB,EAChBc,wBAAwB,EACxBC,2BAA2B,EAC3BC,yBAAyB,EAGzBf,cAAc,EACdC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,oBAAoB,EACpBC,UAAU,EACVoB,UAAU,EACVD,cAAc,EACdI,YAAY,EACZP,cAAc,EACdM,cAAc,EACdD,iBAAiB,EACjBpB,iBAAiB,EACjBC,cAAc,EACdsB,iBAAiB,EACjBT,mBAAmB,EACnBZ,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,QAAQ,EACRY,WAAW,EACXX,mBAAmB,EACnBU,kBAAkB;IAAAiB,OAAA,GAKR1D,YAAY,EA3BtBmB,cAAc,EACdC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,oBAAoB,EACpBC,UAAU,EACVoB,UAAU,EACVD,cAAc,EACdI,YAAY,EACZP,cAAc,EACdM,cAAc,EACdD,iBAAiB,EACjBpB,iBAAiB,EACjBC,cAAc,EACdsB,iBAAiB,EACjBT,mBAAmB,EACnBZ,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,QAAQ,EACRY,WAAW,EACXX,mBAAmB,EACnBU,kBAAkB,EA/BlBzB,eAAe,EACfD,eAAe,EACfE,oBAAoB,EACpBC,gBAAgB,EAChBc,wBAAwB,EACxBC,2BAA2B,EAC3BC,yBAAyB,EArBzBhC,cAAc,EACdC,YAAY,EACZG,YAAY,EACZL,eAAe,EACfG,cAAc,EACdC,eAAe,EACfE,mBAAmB,EACnBO,gBAAgB,EAChBN,cAAc,EACdC,cAAc,EACdC,YAAY,EACZG,gBAAgB,EAChBD,aAAa;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}