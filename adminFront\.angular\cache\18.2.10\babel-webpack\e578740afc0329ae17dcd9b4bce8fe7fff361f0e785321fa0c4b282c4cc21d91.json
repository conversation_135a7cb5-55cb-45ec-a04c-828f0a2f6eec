{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { parseNumericPattern, parseNDigits } from \"../utils.js\";\nexport var Hour1to12Parser = /*#__PURE__*/function (_Parser) {\n  _inherits(Hour1to12Parser, _Parser);\n  var _super = _createSuper(Hour1to12Parser);\n  function Hour1to12Parser() {\n    var _this;\n    _classCallCheck(this, Hour1to12Parser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 70);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['H', 'K', 'k', 't', 'T']);\n    return _this;\n  }\n  _createClass(Hour1to12Parser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      switch (token) {\n        case 'h':\n          return parseNumericPattern(numericPatterns.hour12h, dateString);\n        case 'ho':\n          return match.ordinalNumber(dateString, {\n            unit: 'hour'\n          });\n        default:\n          return parseNDigits(token.length, dateString);\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value >= 1 && value <= 12;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      var isPM = date.getUTCHours() >= 12;\n      if (isPM && value < 12) {\n        date.setUTCHours(value + 12, 0, 0, 0);\n      } else if (!isPM && value === 12) {\n        date.setUTCHours(0, 0, 0, 0);\n      } else {\n        date.setUTCHours(value, 0, 0, 0);\n      }\n      return date;\n    }\n  }]);\n  return Hour1to12Parser;\n}(Parser);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}