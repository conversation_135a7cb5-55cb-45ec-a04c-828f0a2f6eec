{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport { defaultEmphasis } from '../../util/model.js';\nimport { makeSeriesEncodeForNameBased } from '../../data/helper/sourceHelper.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport SeriesModel from '../../model/Series.js';\nvar FunnelSeriesModel = /** @class */function (_super) {\n  __extends(FunnelSeriesModel, _super);\n  function FunnelSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = FunnelSeriesModel.type;\n    return _this;\n  }\n  FunnelSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    // Enable legend selection for each data item\n    // Use a function instead of direct access because data reference may changed\n    this.legendVisualProvider = new LegendVisualProvider(zrUtil.bind(this.getData, this), zrUtil.bind(this.getRawData, this));\n    // Extend labelLine emphasis\n    this._defaultLabelLine(option);\n  };\n  FunnelSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesDataSimply(this, {\n      coordDimensions: ['value'],\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForNameBased, this)\n    });\n  };\n  FunnelSeriesModel.prototype._defaultLabelLine = function (option) {\n    // Extend labelLine emphasis\n    defaultEmphasis(option, 'labelLine', ['show']);\n    var labelLineNormalOpt = option.labelLine;\n    var labelLineEmphasisOpt = option.emphasis.labelLine;\n    // Not show label line if `label.normal.show = false`\n    labelLineNormalOpt.show = labelLineNormalOpt.show && option.label.show;\n    labelLineEmphasisOpt.show = labelLineEmphasisOpt.show && option.emphasis.label.show;\n  };\n  // Overwrite\n  FunnelSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var data = this.getData();\n    var params = _super.prototype.getDataParams.call(this, dataIndex);\n    var valueDim = data.mapDimension('value');\n    var sum = data.getSum(valueDim);\n    // Percent is 0 if sum is 0\n    params.percent = !sum ? 0 : +(data.get(valueDim, dataIndex) / sum * 100).toFixed(2);\n    params.$vars.push('percent');\n    return params;\n  };\n  FunnelSeriesModel.type = 'series.funnel';\n  FunnelSeriesModel.defaultOption = {\n    // zlevel: 0,                  // 一级层叠\n    z: 2,\n    legendHoverLink: true,\n    colorBy: 'data',\n    left: 80,\n    top: 60,\n    right: 80,\n    bottom: 60,\n    // width: {totalWidth} - left - right,\n    // height: {totalHeight} - top - bottom,\n    // 默认取数据最小最大值\n    // min: 0,\n    // max: 100,\n    minSize: '0%',\n    maxSize: '100%',\n    sort: 'descending',\n    orient: 'vertical',\n    gap: 0,\n    funnelAlign: 'center',\n    label: {\n      show: true,\n      position: 'outer'\n      // formatter: 标签文本格式器，同Tooltip.formatter，不支持异步回调\n    },\n    labelLine: {\n      show: true,\n      length: 20,\n      lineStyle: {\n        // color: 各异,\n        width: 1\n      }\n    },\n    itemStyle: {\n      // color: 各异,\n      borderColor: '#fff',\n      borderWidth: 1\n    },\n    emphasis: {\n      label: {\n        show: true\n      }\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    }\n  };\n  return FunnelSeriesModel;\n}(SeriesModel);\nexport default FunnelSeriesModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "createSeriesDataSimply", "defaultEmphasis", "makeSeriesEncodeForNameBased", "LegendVisualProvider", "SeriesModel", "FunnelSeriesModel", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "option", "legend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bind", "getData", "getRawData", "_defaultLabelLine", "getInitialData", "ecModel", "coordDimensions", "encodeDefaulter", "curry", "labelLineNormalOpt", "labelLine", "labelLineEmphasisOpt", "emphasis", "show", "label", "getDataParams", "dataIndex", "data", "params", "call", "valueDim", "mapDimension", "sum", "getSum", "percent", "get", "toFixed", "$vars", "push", "defaultOption", "z", "legendHoverLink", "colorBy", "left", "top", "right", "bottom", "minSize", "maxSize", "sort", "orient", "gap", "funnelAlign", "position", "length", "lineStyle", "width", "itemStyle", "borderColor", "borderWidth", "select"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/funnel/FunnelSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport { defaultEmphasis } from '../../util/model.js';\nimport { makeSeriesEncodeForNameBased } from '../../data/helper/sourceHelper.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport SeriesModel from '../../model/Series.js';\nvar FunnelSeriesModel = /** @class */function (_super) {\n  __extends(FunnelSeriesModel, _super);\n  function FunnelSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = FunnelSeriesModel.type;\n    return _this;\n  }\n  FunnelSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    // Enable legend selection for each data item\n    // Use a function instead of direct access because data reference may changed\n    this.legendVisualProvider = new LegendVisualProvider(zrUtil.bind(this.getData, this), zrUtil.bind(this.getRawData, this));\n    // Extend labelLine emphasis\n    this._defaultLabelLine(option);\n  };\n  FunnelSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesDataSimply(this, {\n      coordDimensions: ['value'],\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForNameBased, this)\n    });\n  };\n  FunnelSeriesModel.prototype._defaultLabelLine = function (option) {\n    // Extend labelLine emphasis\n    defaultEmphasis(option, 'labelLine', ['show']);\n    var labelLineNormalOpt = option.labelLine;\n    var labelLineEmphasisOpt = option.emphasis.labelLine;\n    // Not show label line if `label.normal.show = false`\n    labelLineNormalOpt.show = labelLineNormalOpt.show && option.label.show;\n    labelLineEmphasisOpt.show = labelLineEmphasisOpt.show && option.emphasis.label.show;\n  };\n  // Overwrite\n  FunnelSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var data = this.getData();\n    var params = _super.prototype.getDataParams.call(this, dataIndex);\n    var valueDim = data.mapDimension('value');\n    var sum = data.getSum(valueDim);\n    // Percent is 0 if sum is 0\n    params.percent = !sum ? 0 : +(data.get(valueDim, dataIndex) / sum * 100).toFixed(2);\n    params.$vars.push('percent');\n    return params;\n  };\n  FunnelSeriesModel.type = 'series.funnel';\n  FunnelSeriesModel.defaultOption = {\n    // zlevel: 0,                  // 一级层叠\n    z: 2,\n    legendHoverLink: true,\n    colorBy: 'data',\n    left: 80,\n    top: 60,\n    right: 80,\n    bottom: 60,\n    // width: {totalWidth} - left - right,\n    // height: {totalHeight} - top - bottom,\n    // 默认取数据最小最大值\n    // min: 0,\n    // max: 100,\n    minSize: '0%',\n    maxSize: '100%',\n    sort: 'descending',\n    orient: 'vertical',\n    gap: 0,\n    funnelAlign: 'center',\n    label: {\n      show: true,\n      position: 'outer'\n      // formatter: 标签文本格式器，同Tooltip.formatter，不支持异步回调\n    },\n    labelLine: {\n      show: true,\n      length: 20,\n      lineStyle: {\n        // color: 各异,\n        width: 1\n      }\n    },\n    itemStyle: {\n      // color: 各异,\n      borderColor: '#fff',\n      borderWidth: 1\n    },\n    emphasis: {\n      label: {\n        show: true\n      }\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    }\n  };\n  return FunnelSeriesModel;\n}(SeriesModel);\nexport default FunnelSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,4BAA4B,QAAQ,mCAAmC;AAChF,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,IAAIC,iBAAiB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACrDR,SAAS,CAACO,iBAAiB,EAAEC,MAAM,CAAC;EACpC,SAASD,iBAAiBA,CAAA,EAAG;IAC3B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,iBAAiB,CAACK,IAAI;IACnC,OAAOH,KAAK;EACd;EACAF,iBAAiB,CAACM,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAE;IACnDP,MAAM,CAACK,SAAS,CAACC,IAAI,CAACJ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC5C;IACA;IACA,IAAI,CAACK,oBAAoB,GAAG,IAAIX,oBAAoB,CAACJ,MAAM,CAACgB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC,EAAEjB,MAAM,CAACgB,IAAI,CAAC,IAAI,CAACE,UAAU,EAAE,IAAI,CAAC,CAAC;IACzH;IACA,IAAI,CAACC,iBAAiB,CAACL,MAAM,CAAC;EAChC,CAAC;EACDR,iBAAiB,CAACM,SAAS,CAACQ,cAAc,GAAG,UAAUN,MAAM,EAAEO,OAAO,EAAE;IACtE,OAAOpB,sBAAsB,CAAC,IAAI,EAAE;MAClCqB,eAAe,EAAE,CAAC,OAAO,CAAC;MAC1BC,eAAe,EAAEvB,MAAM,CAACwB,KAAK,CAACrB,4BAA4B,EAAE,IAAI;IAClE,CAAC,CAAC;EACJ,CAAC;EACDG,iBAAiB,CAACM,SAAS,CAACO,iBAAiB,GAAG,UAAUL,MAAM,EAAE;IAChE;IACAZ,eAAe,CAACY,MAAM,EAAE,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC;IAC9C,IAAIW,kBAAkB,GAAGX,MAAM,CAACY,SAAS;IACzC,IAAIC,oBAAoB,GAAGb,MAAM,CAACc,QAAQ,CAACF,SAAS;IACpD;IACAD,kBAAkB,CAACI,IAAI,GAAGJ,kBAAkB,CAACI,IAAI,IAAIf,MAAM,CAACgB,KAAK,CAACD,IAAI;IACtEF,oBAAoB,CAACE,IAAI,GAAGF,oBAAoB,CAACE,IAAI,IAAIf,MAAM,CAACc,QAAQ,CAACE,KAAK,CAACD,IAAI;EACrF,CAAC;EACD;EACAvB,iBAAiB,CAACM,SAAS,CAACmB,aAAa,GAAG,UAAUC,SAAS,EAAE;IAC/D,IAAIC,IAAI,GAAG,IAAI,CAAChB,OAAO,CAAC,CAAC;IACzB,IAAIiB,MAAM,GAAG3B,MAAM,CAACK,SAAS,CAACmB,aAAa,CAACI,IAAI,CAAC,IAAI,EAAEH,SAAS,CAAC;IACjE,IAAII,QAAQ,GAAGH,IAAI,CAACI,YAAY,CAAC,OAAO,CAAC;IACzC,IAAIC,GAAG,GAAGL,IAAI,CAACM,MAAM,CAACH,QAAQ,CAAC;IAC/B;IACAF,MAAM,CAACM,OAAO,GAAG,CAACF,GAAG,GAAG,CAAC,GAAG,CAAC,CAACL,IAAI,CAACQ,GAAG,CAACL,QAAQ,EAAEJ,SAAS,CAAC,GAAGM,GAAG,GAAG,GAAG,EAAEI,OAAO,CAAC,CAAC,CAAC;IACnFR,MAAM,CAACS,KAAK,CAACC,IAAI,CAAC,SAAS,CAAC;IAC5B,OAAOV,MAAM;EACf,CAAC;EACD5B,iBAAiB,CAACK,IAAI,GAAG,eAAe;EACxCL,iBAAiB,CAACuC,aAAa,GAAG;IAChC;IACAC,CAAC,EAAE,CAAC;IACJC,eAAe,EAAE,IAAI;IACrBC,OAAO,EAAE,MAAM;IACfC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACV;IACA;IACA;IACA;IACA;IACAC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,MAAM;IACfC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,UAAU;IAClBC,GAAG,EAAE,CAAC;IACNC,WAAW,EAAE,QAAQ;IACrB5B,KAAK,EAAE;MACLD,IAAI,EAAE,IAAI;MACV8B,QAAQ,EAAE;MACV;IACF,CAAC;IACDjC,SAAS,EAAE;MACTG,IAAI,EAAE,IAAI;MACV+B,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE;QACT;QACAC,KAAK,EAAE;MACT;IACF,CAAC;IACDC,SAAS,EAAE;MACT;MACAC,WAAW,EAAE,MAAM;MACnBC,WAAW,EAAE;IACf,CAAC;IACDrC,QAAQ,EAAE;MACRE,KAAK,EAAE;QACLD,IAAI,EAAE;MACR;IACF,CAAC;IACDqC,MAAM,EAAE;MACNH,SAAS,EAAE;QACTC,WAAW,EAAE;MACf;IACF;EACF,CAAC;EACD,OAAO1D,iBAAiB;AAC1B,CAAC,CAACD,WAAW,CAAC;AACd,eAAeC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}