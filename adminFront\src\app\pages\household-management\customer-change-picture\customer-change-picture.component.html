<nb-card accent="success">
  <nb-card-header>
    戶別管理 > 洽談紀錄上傳 > {{houseTitle}}
  </nb-card-header>
  <nb-card-body>
    <h1 class="font-bold text-[#818181]">您可與此上傳與該戶別客戶討論的客戶圖面，審核通過後客戶就可以在前台檢視該圖面。</h1>

    <div class="d-flex flex-wrap">

      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full">
          <button class="btn btn-info" (click)="addNew(dialogUploadDrawing)" *ngIf="isCreate">
            上傳圖面</button>
        </div>
      </div>
    </div>

    <div class="table-responsive mt-4">
      <table class="table" style="min-width: 1000px;">
        <thead class="table-header">
          <tr class="text-center">
            <th scope="col" class="col-1">來源</th>
            <th scope="col" class="col-1">討論日期</th>
            <th scope="col" class="col-1">圖面名稱</th>
            <th scope="col" class="col-1">上傳日期</th>
            <th scope="col" class="col-1">審核狀態</th>
            <th scope="col" class="col-1">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of listSpecialChange ; let i = index" class="text-center">
            <td>{{ item.CSource| specialChangeSource }}</td>
            <td>{{ formatDate(item.CChangeDate)}}</td>
            <td>{{ item.CDrawingName}}</td>
            <td>{{formatDate(item.CCreateDT)}}</td>
            <td>{{ item.CIsApprove == null ? '待審核' : ( item.CIsApprove ? "通過" : "駁回")}}</td>
            <td class="w-32">
              <button class="btn btn-outline-primary btn-sm m-1" *ngIf="isUpdate"
                (click)="onEdit(dialogUploadDrawing, item)">檢視</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">
    <ngb-pagination [(page)]="pageIndex" [pageSize]="pageSize" [collectionSize]="totalRecords"
      (pageChange)="pageChanged($event)" aria-label="Pagination">
    </ngb-pagination>
  </nb-card-footer>
  <nb-card-footer>
    <div class="d-flex justify-content-center">
      <button class="btn btn-secondary btn-sm" (click)="goBack()">
        返回上一頁
      </button>
    </div>
  </nb-card-footer>
</nb-card>


<ng-template #dialogUploadDrawing let-dialog let-ref="dialogRef">
  <nb-card style="min-width:600px; max-height: 95vh">
    <nb-card-header>
      戶別管理 > 洽談紀錄上傳 > {{house.CHousehold}} &nbsp; {{house.CFloor}}F
    </nb-card-header>
    <nb-card-body class="px-4">

      <div class="form-group">
        <label for="CChangeDate" #CChangeDate class="required-field mr-4" style="min-width:75px" baseLabel>
          討論日期
        </label>
        <p-calendar [appendTo]="'CChangeDate'" placeholder="年/月/日" [iconDisplay]="'input'" [showIcon]="true"
          inputId="icondisplay" dateFormat="yy/mm/dd" [(ngModel)]="formSpecialChange.CChangeDate" [disabled]="isEdit"
          [showButtonBar]="true" class="!w-[400px]"></p-calendar>
      </div>
      <div class="form-group">
        <label for="cDrawingName" class="required-field mr-4" style="min-width:75px" baseLabel>
          圖面名稱
        </label>
        <input type="text" class="w-full" nbInput placeholder="圖面名稱" [(ngModel)]="formSpecialChange.CDrawingName"
          [disabled]="isEdit" />
      </div>
      <div class="form-group">
        <label for="cIsEnable" class=" mr-4" style="min-width:75px" baseLabel>
          選樣結果
        </label>
        <app-file-upload [config]="fileUploadConfig" [fileList]="imageUrlList"
          [existingFiles]="isEdit && SpecialChange && SpecialChange.CFileRes ? SpecialChange.CFileRes : []"
          (multiFileSelected)="onMultiFileSelected($event)" (nameAutoFilled)="onNameAutoFilled($event)"
          labelMinWidth="0px">
        </app-file-upload>
      </div>
      <div class="form-group d-flex align-items-center">
        <label for="cApproveRemark" baseLabel class="required-field align-self-start mr-4"
          style="min-width:75px">審核說明</label>
        <textarea name="remark" id="cApproveRemark" rows="5" nbInput style="resize: none;" class="w-full"
          [disabled]="isEdit" class="w-full" [(ngModel)]="formSpecialChange.CApproveRemark"></textarea>
      </div>

      <div class="d-flex justify-content-center">
        <button class="btn btn-outline-secondary m-2" (click)="onClose(ref)">取消</button>
        <button class="btn btn-success m-2" *ngIf="!isEdit" (click)="onSaveSpecialChange(ref)">送出審核</button>
      </div>
    </nb-card-body>
  </nb-card>
</ng-template>