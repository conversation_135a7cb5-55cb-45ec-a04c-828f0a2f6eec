{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule } from '@nebular/theme';\nimport { TemplateGetListResponse } from 'src/services/api/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"nb-checkbox\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const template_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(template_r2.selected, $event) || (template_r2.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onTemplateItemChange());\n    });\n    i0.ɵɵelementStart(2, \"div\", 27)(3, \"div\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 30);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 31);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const template_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", template_r2.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r2.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", template_r2.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u72C0\\u614B: \", template_r2.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u985E\\u578B: \", template_r2.CTemplateType, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template, 11, 5, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templates);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"nb-icon\", 33);\n    i0.ɵɵtext(2, \" \\u66AB\\u7121\\u53EF\\u7528\\u7684\\u6A21\\u677F\\u9805\\u76EE\\uFF0C\\u8ACB\\u7A0D\\u5F8C\\u518D\\u8A66\\u6216\\u806F\\u7E6B\\u7BA1\\u7406\\u54E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"nb-icon\", 21);\n    i0.ɵɵtext(4, \"\\u9078\\u64C7\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 22);\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_12_div_6_Template, 2, 1, \"div\", 23)(7, SpaceTemplateSelectorComponent_div_12_ng_template_7_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const noTemplates_r4 = i0.ɵɵreference(8);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templates.length > 0)(\"ngIfElse\", noTemplates_r4);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u4F4D\\u7F6E: \", detail_r5.CLocation, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"div\", 57);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template, 2, 1, \"div\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r6 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r5.CPart);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r5.CLocation);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"span\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 52);\n    i0.ɵɵtemplate(5, SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template, 7, 3, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5305\\u542B \", ctx_r2.getTemplateDetails(item_r7.CTemplateId).length, \" \\u500B\\u660E\\u7D30\\u9805\\u76EE\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getTemplateDetails(item_r7.CTemplateId));\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"nb-icon\", 33);\n    i0.ɵɵtext(2, \" \\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u660E\\u7D30\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"h5\", 43);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 44)(5, \"span\", 45);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 46);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 47);\n    i0.ɵɵtemplate(10, SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template, 6, 2, \"div\", 48)(11, SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template, 3, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const noDetails_r8 = i0.ɵɵreference(12);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r7.CTemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", item_r7.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTemplateDetails(item_r7.CTemplateId).length > 0)(\"ngIfElse\", noDetails_r8);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"strong\");\n    i0.ɵɵelement(3, \"nb-icon\", 63);\n    i0.ɵɵtext(4, \"\\u885D\\u7A81\\u6AA2\\u6E2C\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6AA2\\u6E2C\\u5230 \", ctx_r2.getConflictCount(), \" \\u500B\\u9805\\u76EE\\u53EF\\u80FD\\u8207\\u73FE\\u6709\\u9700\\u6C42\\u91CD\\u8907\\uFF0C\\u7CFB\\u7D71\\u5C07\\u81EA\\u52D5\\u8655\\u7406\\u885D\\u7A81\\u9805\\u76EE\\u3002 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 34)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"nb-icon\", 35);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u5957\\u7528\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36)(6, \"div\", 37);\n    i0.ɵɵtext(7, \" \\u5C07\\u5957\\u7528 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9, \"\\u901A\\u7528\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_13_div_12_Template, 13, 5, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, SpaceTemplateSelectorComponent_div_13_div_13_Template, 6, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\\uFF1A\", ctx_r2.getSelectedItems().length, \"\\u500B\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSelectedItems());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasConflicts());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.previousStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0B\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canProceed());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyTemplate());\n    });\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u5957\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.getSelectedItems().length === 0);\n  }\n}\nexport class SpaceTemplateSelectorComponent {\n  constructor(templateService, dialogRef) {\n    this.templateService = templateService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = '';\n    this.templateApplied = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templates = []; // 直接使用 API 資料\n    this.selectedTemplateDetails = new Map(); // 存儲已載入的模板詳情\n  }\n  ngOnInit() {\n    // 組件初始化時載入模板\n    this.loadTemplatesFromAPI();\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: 1,\n      // 1=客變需求\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 直接使用 API 資料，只添加 selected 屬性\n          this.templates = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          // API 返回錯誤\n          this.templates = [];\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        this.templates = [];\n      }\n    });\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  getSelectedItems() {\n    return this.templates.filter(item => item.selected);\n  }\n  getSelectedTotalPrice() {\n    // 由於 API 沒有價格資訊，返回 0\n    return 0;\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      if (this.currentStep === 1) {\n        // 進入步驟2前，載入選中模板的詳情\n        this.loadSelectedTemplateDetails();\n      }\n      this.currentStep++;\n    }\n  }\n  loadSelectedTemplateDetails() {\n    const selectedItems = this.getSelectedItems();\n    selectedItems.forEach(item => {\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\n        // 只載入尚未載入過的模板詳情\n        this.loadTemplateDetailById(item.CTemplateId);\n      }\n    });\n  }\n  loadTemplateDetailById(templateId) {\n    const args = {\n      templateId: templateId\n    };\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          this.selectedTemplateDetails.set(templateId, response.Entries);\n        }\n      },\n      error: () => {\n        // 錯誤處理：設置空陣列\n        this.selectedTemplateDetails.set(templateId, []);\n      }\n    });\n  }\n  getTemplateDetails(templateId) {\n    return this.selectedTemplateDetails.get(templateId) || [];\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要套用的模板項目',\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: '通用模板',\n      selectedItems: this.getSelectedItems(),\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  // 移除不需要的方法\n  // onBackdropClick 由 NbDialog 自動處理\n  reset() {\n    this.currentStep = 1;\n    this.templates = [];\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    // 保留 templates 資料，只重置選擇狀態\n    this.templates.forEach(template => {\n      template.selected = false;\n    });\n    // 清空詳情快取\n    this.selectedTemplateDetails.clear();\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService), i0.ɵɵdirectiveInject(i2.NbDialogRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceTemplateSelectorComponent,\n      selectors: [[\"app-space-template-selector\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\"\n      },\n      outputs: {\n        templateApplied: \"templateApplied\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 24,\n      vars: 16,\n      consts: [[\"noTemplates\", \"\"], [\"noDetails\", \"\"], [1, \"space-template-dialog\"], [1, \"space-template-header\"], [1, \"space-template-title\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"close-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"space-template-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"space-template-footer\"], [1, \"progress-info\"], [1, \"step-buttons\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"template-selection\"], [1, \"section-title\"], [\"icon\", \"layers-outline\", 1, \"mr-2\"], [1, \"template-list\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-item\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"template-info\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"item-status\"], [1, \"item-type\"], [1, \"no-templates\"], [\"icon\", \"info-outline\", 1, \"mr-2\"], [1, \"confirmation-area\"], [\"icon\", \"checkmark-circle-outline\", 1, \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"selected-templates-details\"], [\"class\", \"template-detail-section\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"conflict-warning\", 4, \"ngIf\"], [1, \"template-detail-section\"], [1, \"template-detail-header\"], [1, \"template-name\"], [1, \"template-meta\"], [1, \"template-id\"], [1, \"template-status\"], [1, \"template-detail-content\"], [\"class\", \"detail-items\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-items\"], [1, \"detail-items-header\"], [1, \"detail-count\"], [1, \"detail-items-list\"], [\"class\", \"detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\"], [1, \"detail-index\"], [1, \"detail-info\"], [1, \"detail-part\"], [\"class\", \"detail-location\", 4, \"ngIf\"], [1, \"detail-location\"], [1, \"no-details\"], [1, \"conflict-warning\"], [1, \"warning-text\"], [\"icon\", \"alert-triangle-outline\", 1, \"mr-1\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\", \"disabled\"]],\n      template: function SpaceTemplateSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\", 3)(2, \"div\", 4);\n          i0.ɵɵtext(3, \"\\u7A7A\\u9593\\u6A21\\u677F\\u9078\\u64C7\\u5668\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_4_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(5, \"nb-icon\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 7)(7, \"div\", 8)(8, \"div\", 9);\n          i0.ɵɵtext(9, \"1. \\u9078\\u64C7\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 9);\n          i0.ɵɵtext(11, \"2. \\u78BA\\u8A8D\\u5957\\u7528\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, SpaceTemplateSelectorComponent_div_12_Template, 9, 2, \"div\", 10)(13, SpaceTemplateSelectorComponent_div_13_Template, 14, 3, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"nb-card-footer\", 11)(15, \"div\", 12)(16, \"span\");\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_19_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(20, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(21, SpaceTemplateSelectorComponent_button_21_Template, 2, 0, \"button\", 15)(22, SpaceTemplateSelectorComponent_button_22_Template, 2, 1, \"button\", 16)(23, SpaceTemplateSelectorComponent_button_23_Template, 2, 1, \"button\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(8, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(12, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.getProgressText());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, FormsModule, i4.NgControlStatus, i4.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbIconModule, i2.NbIconComponent, NbCheckboxModule, i2.NbCheckboxComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.space-template-dialog[_ngcontent-%COMP%] {\\n  min-width: 600px;\\n  max-width: 800px;\\n  min-height: 500px;\\n  max-height: 80vh;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.25rem 1.5rem;\\n  border-bottom: 1px solid var(--nb-card-divider-color, #e5e7eb);\\n  background-color: var(--nb-card-background-color, #ffffff);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .space-template-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--nb-text-primary-color, #2c2c2c);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  padding: 0.25rem;\\n  min-width: auto;\\n  border: none;\\n  background: transparent;\\n  color: var(--nb-text-hint-color, #737373);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--nb-text-primary-color, #2c2c2c);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  overflow-y: auto;\\n  max-height: 60vh;\\n  \\n\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 2rem;\\n  border-bottom: 1px solid var(--nb-card-divider-color);\\n  padding-bottom: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  margin: 0 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background-color: var(--nb-primary-color);\\n  color: var(--nb-text-primary-on-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background-color: var(--nb-success-color);\\n  color: var(--nb-text-primary-on-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: var(--nb-card-background-color);\\n  color: var(--nb-text-hint-color);\\n  border: 1px solid var(--nb-card-divider-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  \\n\\n  \\n\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: var(--nb-text-primary-color);\\n  margin-bottom: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: var(--nb-primary-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  border: 1px solid var(--nb-card-divider-color);\\n  border-radius: 0.375rem;\\n  transition: all 0.3s ease;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  border-color: var(--nb-primary-color);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  flex: 1;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--nb-text-primary-color);\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-text-hint-color);\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-text-basic-color);\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-text-basic-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: var(--nb-text-hint-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .no-templates[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%] {\\n  background-color: var(--nb-card-background-color);\\n  border: 1px solid var(--nb-primary-color);\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%] {\\n  color: var(--nb-text-primary-color);\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--nb-primary-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%] {\\n  border: 1px solid var(--nb-card-divider-color);\\n  border-radius: 0.375rem;\\n  margin-bottom: 1rem;\\n  overflow: hidden;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%] {\\n  background-color: var(--nb-card-background-color);\\n  padding: 1rem;\\n  border-bottom: 1px solid var(--nb-card-divider-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: var(--nb-text-primary-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .template-id[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-text-hint-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-header[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .template-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-success-color);\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-header[_ngcontent-%COMP%]   .detail-count[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--nb-text-basic-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  padding: 0.5rem 0;\\n  border-bottom: 1px solid var(--nb-card-divider-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%] {\\n  background-color: var(--nb-primary-color);\\n  color: var(--nb-text-primary-on-color);\\n  border-radius: 50%;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  margin-right: 0.75rem;\\n  flex-shrink: 0;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-part[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--nb-text-primary-color);\\n  margin-bottom: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .detail-items-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .detail-location[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-text-hint-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .no-details[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 1rem;\\n  color: var(--nb-text-hint-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-templates-details[_ngcontent-%COMP%]   .template-detail-section[_ngcontent-%COMP%]   .template-detail-content[_ngcontent-%COMP%]   .no-details[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%] {\\n  background-color: var(--nb-warning-light-color);\\n  border: 1px solid var(--nb-warning-color);\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-top: 1rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%] {\\n  color: var(--nb-warning-dark-color);\\n  font-weight: 500;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .conflict-warning[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid var(--nb-card-divider-color);\\n  background-color: var(--nb-card-background-color);\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  color: var(--nb-text-hint-color);\\n  font-size: 0.875rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.75rem;\\n}\\n.space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .space-template-dialog[_ngcontent-%COMP%] {\\n    min-width: 95vw;\\n    max-width: 95vw;\\n    margin: 0.5rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.4rem 0.8rem;\\n    margin: 0 0.25rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n   .space-template-dialog[_ngcontent-%COMP%]   .space-template-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n    align-items: stretch;\\n  }\\n  .space-template-dialog[_ngcontent-%COMP%]   .space-template-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n\\n\\nnb-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  width: 100%;\\n}\\nnb-checkbox[_ngcontent-%COMP%]   .customised-control-input[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  margin-top: 0.125rem;\\n}\\n\\n\\n\\n.mr-1[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n\\n\\n.nb-theme-dark[_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .space-template-dialog[_ngcontent-%COMP%]   .template-selection[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "TemplateGetListResponse", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template_nb_checkbox_ngModelChange_1_listener", "$event", "template_r2", "ɵɵrestoreView", "_r1", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "ctx_r2", "ɵɵnextContext", "onTemplateItemChange", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CTemplateName", "ɵɵtextInterpolate1", "CTemplateId", "CStatus", "CTemplateType", "ɵɵtemplate", "SpaceTemplateSelectorComponent_div_12_div_6_div_1_Template", "ɵɵproperty", "templates", "ɵɵelement", "SpaceTemplateSelectorComponent_div_12_div_6_Template", "SpaceTemplateSelectorComponent_div_12_ng_template_7_Template", "ɵɵtemplateRefExtractor", "length", "noTemplates_r4", "detail_r5", "CLocation", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_div_6_Template", "i_r6", "<PERSON>art", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_div_5_Template", "getTemplateDetails", "item_r7", "SpaceTemplateSelectorComponent_div_13_div_12_div_10_Template", "SpaceTemplateSelectorComponent_div_13_div_12_ng_template_11_Template", "noDetails_r8", "getConflictCount", "SpaceTemplateSelectorComponent_div_13_div_12_Template", "SpaceTemplateSelectorComponent_div_13_div_13_Template", "getSelectedItems", "hasConflicts", "SpaceTemplateSelectorComponent_button_21_Template_button_click_0_listener", "_r9", "previousStep", "SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener", "_r10", "nextStep", "canProceed", "SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener", "_r11", "applyTemplate", "SpaceTemplateSelectorComponent", "constructor", "templateService", "dialogRef", "buildCaseId", "templateApplied", "currentStep", "selectedTemplateDetails", "Map", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "PageIndex", "PageSize", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "error", "filter", "getSelectedTotalPrice", "loadSelectedTemplateDetails", "selectedItems", "for<PERSON>ach", "has", "loadTemplateDetailById", "templateId", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "set", "get", "getProgressText", "progressTexts", "config", "spaceId", "spaceName", "totalPrice", "emit", "close", "resetSelections", "reset", "template", "clear", "ɵɵdirectiveInject", "i1", "TemplateService", "i2", "NbDialogRef", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "SpaceTemplateSelectorComponent_Template", "rf", "ctx", "SpaceTemplateSelectorComponent_Template_button_click_4_listener", "SpaceTemplateSelectorComponent_div_12_Template", "SpaceTemplateSelectorComponent_div_13_Template", "SpaceTemplateSelectorComponent_Template_button_click_19_listener", "SpaceTemplateSelectorComponent_button_21_Template", "SpaceTemplateSelectorComponent_button_22_Template", "SpaceTemplateSelectorComponent_button_23_Template", "ɵɵpureFunction3", "_c0", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbIconComponent", "NbCheckboxComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbIconModule,\r\n  NbCheckboxModule,\r\n  NbDialogRef\r\n} from '@nebular/theme';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, TemplateGetListResponse, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';\r\n\r\n// 擴展 API 模型以支援前端選擇功能\r\nexport interface ExtendedTemplateItem extends TemplateGetListResponse {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: ExtendedTemplateItem[];\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule\r\n  ],\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() buildCaseId: string = '';\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n\r\n  currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n  templates: ExtendedTemplateItem[] = []; // 直接使用 API 資料\r\n  selectedTemplateDetails: Map<number, TemplateDetailItem[]> = new Map(); // 存儲已載入的模板詳情\r\n\r\n  constructor(\r\n    private templateService: TemplateService,\r\n    private dialogRef: NbDialogRef<SpaceTemplateSelectorComponent>\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    // 組件初始化時載入模板\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  loadTemplatesFromAPI() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: 1, // 1=客變需求\r\n      PageIndex: 1,\r\n      PageSize: 100, // 載入足夠的資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateListForCommon API\r\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // 直接使用 API 資料，只添加 selected 屬性\r\n          this.templates = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templates = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        this.templates = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  getSelectedItems(): ExtendedTemplateItem[] {\r\n    return this.templates.filter(item => item.selected);\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    // 由於 API 沒有價格資訊，返回 0\r\n    return 0;\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      if (this.currentStep === 1) {\r\n        // 進入步驟2前，載入選中模板的詳情\r\n        this.loadSelectedTemplateDetails();\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  loadSelectedTemplateDetails() {\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    selectedItems.forEach(item => {\r\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\r\n        // 只載入尚未載入過的模板詳情\r\n        this.loadTemplateDetailById(item.CTemplateId);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTemplateDetailById(templateId: number) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.selectedTemplateDetails.set(templateId, response.Entries);\r\n        }\r\n      },\r\n      error: () => {\r\n        // 錯誤處理：設置空陣列\r\n        this.selectedTemplateDetails.set(templateId, []);\r\n      }\r\n    });\r\n  }\r\n\r\n  getTemplateDetails(templateId: number): TemplateDetailItem[] {\r\n    return this.selectedTemplateDetails.get(templateId) || [];\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要套用的模板項目',\r\n      2: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: 'common', // 通用模板，不特定空間\r\n      spaceName: '通用模板',\r\n      selectedItems: this.getSelectedItems(),\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  // 移除不需要的方法\r\n  // onBackdropClick 由 NbDialog 自動處理\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.templates = [];\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    // 保留 templates 資料，只重置選擇狀態\r\n    this.templates.forEach(template => {\r\n      template.selected = false;\r\n    });\r\n    // 清空詳情快取\r\n    this.selectedTemplateDetails.clear();\r\n  }\r\n}\r\n", "<!-- 空間模板選擇器共用元件 - 使用 nb-dialog -->\r\n<nb-card class=\"space-template-dialog\">\r\n  <nb-card-header class=\"space-template-header\">\r\n    <div class=\"space-template-title\">空間模板選擇器</div>\r\n    <button class=\"close-btn\" nbButton ghost (click)=\"close()\">\r\n      <nb-icon icon=\"close-outline\"></nb-icon>\r\n    </button>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body class=\"space-template-body\">\r\n    <!-- 步驟導航 -->\r\n    <div class=\"step-nav\">\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 1,\r\n        'completed': currentStep > 1,\r\n        'pending': currentStep < 1\r\n      }\">1. 選擇模板</div>\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 2,\r\n        'completed': currentStep > 2,\r\n        'pending': currentStep < 2\r\n      }\">2. 確認套用</div>\r\n    </div>\r\n\r\n    <!-- 步驟1: 選擇模板 -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <div class=\"template-selection\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"layers-outline\" class=\"mr-2\"></nb-icon>選擇模板項目\r\n        </div>\r\n        <div class=\"template-list\">\r\n          <div *ngIf=\"templates.length > 0; else noTemplates\">\r\n            <div *ngFor=\"let template of templates\" class=\"template-item\">\r\n              <nb-checkbox [(ngModel)]=\"template.selected\" (ngModelChange)=\"onTemplateItemChange()\">\r\n                <div class=\"template-info\">\r\n                  <div class=\"item-name\">{{ template.CTemplateName }}</div>\r\n                  <div class=\"item-code\">ID: {{ template.CTemplateId }}</div>\r\n                  <div class=\"item-status\">\r\n                    狀態: {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n                  </div>\r\n                  <div class=\"item-type\">\r\n                    類型: {{ template.CTemplateType }}\r\n                  </div>\r\n                </div>\r\n              </nb-checkbox>\r\n            </div>\r\n          </div>\r\n          <ng-template #noTemplates>\r\n            <div class=\"no-templates\">\r\n              <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n              暫無可用的模板項目，請稍後再試或聯繫管理員\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 步驟2: 確認套用 -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <div class=\"confirmation-area\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"checkmark-circle-outline\" class=\"mr-2\"></nb-icon>確認套用詳情\r\n        </div>\r\n\r\n        <div class=\"selected-summary\">\r\n          <div class=\"summary-text\">\r\n            將套用 <strong>通用模板</strong>：{{ getSelectedItems().length }}個模板項目\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 選中的模板詳情展開 -->\r\n        <div class=\"selected-templates-details\">\r\n          <div *ngFor=\"let item of getSelectedItems()\" class=\"template-detail-section\">\r\n            <div class=\"template-detail-header\">\r\n              <h5 class=\"template-name\">{{ item.CTemplateName }}</h5>\r\n              <div class=\"template-meta\">\r\n                <span class=\"template-id\">ID: {{ item.CTemplateId }}</span>\r\n                <span class=\"template-status\">{{ item.CStatus === 1 ? '啟用' : '停用' }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"template-detail-content\">\r\n              <div *ngIf=\"getTemplateDetails(item.CTemplateId!).length > 0; else noDetails\" class=\"detail-items\">\r\n                <div class=\"detail-items-header\">\r\n                  <span class=\"detail-count\">包含 {{ getTemplateDetails(item.CTemplateId!).length }} 個明細項目：</span>\r\n                </div>\r\n                <div class=\"detail-items-list\">\r\n                  <div *ngFor=\"let detail of getTemplateDetails(item.CTemplateId!); let i = index\" class=\"detail-item\">\r\n                    <div class=\"detail-index\">{{ i + 1 }}</div>\r\n                    <div class=\"detail-info\">\r\n                      <div class=\"detail-part\">{{ detail.CPart }}</div>\r\n                      <div class=\"detail-location\" *ngIf=\"detail.CLocation\">\r\n                        位置: {{ detail.CLocation }}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <ng-template #noDetails>\r\n                <div class=\"no-details\">\r\n                  <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n                  此模板暫無明細項目\r\n                </div>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"hasConflicts()\" class=\"conflict-warning\">\r\n          <div class=\"warning-text\">\r\n            <strong><nb-icon icon=\"alert-triangle-outline\" class=\"mr-1\"></nb-icon>衝突檢測：</strong>\r\n            檢測到 {{ getConflictCount() }} 個項目可能與現有需求重複，系統將自動處理衝突項目。\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-footer class=\"space-template-footer\">\r\n    <div class=\"progress-info\">\r\n      <span>{{ getProgressText() }}</span>\r\n    </div>\r\n    <div class=\"step-buttons\">\r\n      <button nbButton status=\"basic\" (click)=\"close()\">取消</button>\r\n      <button *ngIf=\"currentStep > 1\" nbButton status=\"basic\" (click)=\"previousStep()\">上一步</button>\r\n      <button *ngIf=\"currentStep < 2\" nbButton status=\"primary\" [disabled]=\"!canProceed()\"\r\n        (click)=\"nextStep()\">下一步</button>\r\n      <button *ngIf=\"currentStep === 2\" nbButton status=\"success\" [disabled]=\"getSelectedItems().length === 0\"\r\n        (click)=\"applyTemplate()\">確認套用</button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,QAEX,gBAAgB;AAEvB,SAA8BC,uBAAuB,QAAuD,yBAAyB;;;;;;;;;;;;;;ICsBvHC,EADF,CAAAC,cAAA,cAA8D,sBAC0B;IAAzED,EAAA,CAAAE,gBAAA,2BAAAC,gGAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAJ,WAAA,CAAAK,QAAA,EAAAN,MAAA,MAAAC,WAAA,CAAAK,QAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAACJ,EAAA,CAAAY,UAAA,2BAAAT,gGAAA;MAAAH,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAAiBE,MAAA,CAAAE,oBAAA,EAAsB;IAAA,EAAC;IAEjFf,EADF,CAAAC,cAAA,cAA2B,cACF;IAAAD,EAAA,CAAAgB,MAAA,GAA4B;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACzDjB,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAgB,MAAA,GAA8B;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAC3DjB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAgB,MAAA,IACF;IAGNhB,EAHM,CAAAiB,YAAA,EAAM,EACF,EACM,EACV;;;;IAZSjB,EAAA,CAAAkB,SAAA,EAA+B;IAA/BlB,EAAA,CAAAmB,gBAAA,YAAAd,WAAA,CAAAK,QAAA,CAA+B;IAEjBV,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAAoB,iBAAA,CAAAf,WAAA,CAAAgB,aAAA,CAA4B;IAC5BrB,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAAsB,kBAAA,SAAAjB,WAAA,CAAAkB,WAAA,KAA8B;IAEnDvB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAjB,WAAA,CAAAmB,OAAA,8CACF;IAEExB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAjB,WAAA,CAAAoB,aAAA,MACF;;;;;IAXRzB,EAAA,CAAAC,cAAA,UAAoD;IAClDD,EAAA,CAAA0B,UAAA,IAAAC,0DAAA,mBAA8D;IAchE3B,EAAA,CAAAiB,YAAA,EAAM;;;;IAdsBjB,EAAA,CAAAkB,SAAA,EAAY;IAAZlB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAAgB,SAAA,CAAY;;;;;IAgBtC7B,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA8B,SAAA,kBAAoD;IACpD9B,EAAA,CAAAgB,MAAA,uIACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;;IAxBVjB,EAFJ,CAAAC,cAAA,cAAoD,cAClB,cACH;IACzBD,EAAA,CAAA8B,SAAA,kBAAsD;IAAA9B,EAAA,CAAAgB,MAAA,4CACxD;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,cAA2B;IAiBzBD,EAhBA,CAAA0B,UAAA,IAAAK,oDAAA,kBAAoD,IAAAC,4DAAA,gCAAAhC,EAAA,CAAAiC,sBAAA,CAgB1B;IAQhCjC,EAFI,CAAAiB,YAAA,EAAM,EACF,EACF;;;;;IAxBMjB,EAAA,CAAAkB,SAAA,GAA4B;IAAAlB,EAA5B,CAAA4B,UAAA,SAAAf,MAAA,CAAAgB,SAAA,CAAAK,MAAA,KAA4B,aAAAC,cAAA,CAAgB;;;;;IA4DtCnC,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;IADJjB,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAsB,kBAAA,oBAAAc,SAAA,CAAAC,SAAA,MACF;;;;;IALFrC,EADF,CAAAC,cAAA,cAAqG,cACzE;IAAAD,EAAA,CAAAgB,MAAA,GAAW;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAEzCjB,EADF,CAAAC,cAAA,cAAyB,cACE;IAAAD,EAAA,CAAAgB,MAAA,GAAkB;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IACjDjB,EAAA,CAAA0B,UAAA,IAAAY,wEAAA,kBAAsD;IAI1DtC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IAPsBjB,EAAA,CAAAkB,SAAA,GAAW;IAAXlB,EAAA,CAAAoB,iBAAA,CAAAmB,IAAA,KAAW;IAEVvC,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAAoB,iBAAA,CAAAgB,SAAA,CAAAI,KAAA,CAAkB;IACbxC,EAAA,CAAAkB,SAAA,EAAsB;IAAtBlB,EAAA,CAAA4B,UAAA,SAAAQ,SAAA,CAAAC,SAAA,CAAsB;;;;;IAPxDrC,EAFJ,CAAAC,cAAA,cAAmG,cAChE,eACJ;IAAAD,EAAA,CAAAgB,MAAA,GAA4D;IACzFhB,EADyF,CAAAiB,YAAA,EAAO,EAC1F;IACNjB,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA0B,UAAA,IAAAe,kEAAA,kBAAqG;IAUzGzC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IAbyBjB,EAAA,CAAAkB,SAAA,GAA4D;IAA5DlB,EAAA,CAAAsB,kBAAA,kBAAAT,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAApB,WAAA,EAAAW,MAAA,0CAA4D;IAG/DlC,EAAA,CAAAkB,SAAA,GAA0C;IAA1ClB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAApB,WAAA,EAA0C;;;;;IAapEvB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA8B,SAAA,kBAAoD;IACpD9B,EAAA,CAAAgB,MAAA,+DACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;;;IA7BRjB,EAFJ,CAAAC,cAAA,cAA6E,cACvC,aACR;IAAAD,EAAA,CAAAgB,MAAA,GAAwB;IAAAhB,EAAA,CAAAiB,YAAA,EAAK;IAErDjB,EADF,CAAAC,cAAA,cAA2B,eACC;IAAAD,EAAA,CAAAgB,MAAA,GAA0B;IAAAhB,EAAA,CAAAiB,YAAA,EAAO;IAC3DjB,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAgB,MAAA,GAAsC;IAExEhB,EAFwE,CAAAiB,YAAA,EAAO,EACvE,EACF;IAENjB,EAAA,CAAAC,cAAA,cAAqC;IAkBnCD,EAjBA,CAAA0B,UAAA,KAAAkB,4DAAA,kBAAmG,KAAAC,oEAAA,gCAAA7C,EAAA,CAAAiC,sBAAA,CAiB3E;IAO5BjC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;;IAhCwBjB,EAAA,CAAAkB,SAAA,GAAwB;IAAxBlB,EAAA,CAAAoB,iBAAA,CAAAuB,OAAA,CAAAtB,aAAA,CAAwB;IAEtBrB,EAAA,CAAAkB,SAAA,GAA0B;IAA1BlB,EAAA,CAAAsB,kBAAA,SAAAqB,OAAA,CAAApB,WAAA,KAA0B;IACtBvB,EAAA,CAAAkB,SAAA,GAAsC;IAAtClB,EAAA,CAAAoB,iBAAA,CAAAuB,OAAA,CAAAnB,OAAA,yCAAsC;IAKhExB,EAAA,CAAAkB,SAAA,GAAwD;IAAAlB,EAAxD,CAAA4B,UAAA,SAAAf,MAAA,CAAA6B,kBAAA,CAAAC,OAAA,CAAApB,WAAA,EAAAW,MAAA,KAAwD,aAAAY,YAAA,CAAc;;;;;IA6B9E9C,EAFJ,CAAAC,cAAA,cAAqD,cACzB,aAChB;IAAAD,EAAA,CAAA8B,SAAA,kBAA8D;IAAA9B,EAAA,CAAAgB,MAAA,qCAAK;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IACpFjB,EAAA,CAAAgB,MAAA,GACF;IACFhB,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IAFFjB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAsB,kBAAA,yBAAAT,MAAA,CAAAkC,gBAAA,+JACF;;;;;IArDF/C,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAA8B,SAAA,kBAAgE;IAAA9B,EAAA,CAAAgB,MAAA,4CAClE;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAGJjB,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAgB,MAAA,2BAAI;IAAAhB,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IAAAjB,EAAA,CAAAgB,MAAA,IAC3B;IACFhB,EADE,CAAAiB,YAAA,EAAM,EACF;IAGNjB,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAA0B,UAAA,KAAAsB,qDAAA,mBAA6E;IAmC/EhD,EAAA,CAAAiB,YAAA,EAAM;IAENjB,EAAA,CAAA0B,UAAA,KAAAuB,qDAAA,kBAAqD;IAOzDjD,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IAlD2BjB,EAAA,CAAAkB,SAAA,IAC3B;IAD2BlB,EAAA,CAAAsB,kBAAA,WAAAT,MAAA,CAAAqC,gBAAA,GAAAhB,MAAA,oCAC3B;IAKsBlC,EAAA,CAAAkB,SAAA,GAAqB;IAArBlB,EAAA,CAAA4B,UAAA,YAAAf,MAAA,CAAAqC,gBAAA,GAAqB;IAqCvClD,EAAA,CAAAkB,SAAA,EAAoB;IAApBlB,EAAA,CAAA4B,UAAA,SAAAf,MAAA,CAAAsC,YAAA,GAAoB;;;;;;IAgB5BnD,EAAA,CAAAC,cAAA,iBAAiF;IAAzBD,EAAA,CAAAY,UAAA,mBAAAwC,0EAAA;MAAApD,EAAA,CAAAM,aAAA,CAAA+C,GAAA;MAAA,MAAAxC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAAyC,YAAA,EAAc;IAAA,EAAC;IAACtD,EAAA,CAAAgB,MAAA,yBAAG;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;;;IAC7FjB,EAAA,CAAAC,cAAA,iBACuB;IAArBD,EAAA,CAAAY,UAAA,mBAAA2C,0EAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAAkD,IAAA;MAAA,MAAA3C,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAA4C,QAAA,EAAU;IAAA,EAAC;IAACzD,EAAA,CAAAgB,MAAA,yBAAG;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;IADuBjB,EAAA,CAAA4B,UAAA,cAAAf,MAAA,CAAA6C,UAAA,GAA0B;;;;;;IAEpF1D,EAAA,CAAAC,cAAA,iBAC4B;IAA1BD,EAAA,CAAAY,UAAA,mBAAA+C,0EAAA;MAAA3D,EAAA,CAAAM,aAAA,CAAAsD,IAAA;MAAA,MAAA/C,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAW,WAAA,CAASE,MAAA,CAAAgD,aAAA,EAAe;IAAA,EAAC;IAAC7D,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;IADmBjB,EAAA,CAAA4B,UAAA,aAAAf,MAAA,CAAAqC,gBAAA,GAAAhB,MAAA,OAA4C;;;ADzF9G,OAAM,MAAO4B,8BAA8B;EAQzCC,YACUC,eAAgC,EAChCC,SAAsD;IADtD,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IATV,KAAAC,WAAW,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAG,IAAI3E,YAAY,EAAuB;IAEnE,KAAA4E,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAvC,SAAS,GAA2B,EAAE,CAAC,CAAC;IACxC,KAAAwC,uBAAuB,GAAsC,IAAIC,GAAG,EAAE,CAAC,CAAC;EAKpE;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB;IACA,MAAMC,mBAAmB,GAAwB;MAC/ChD,aAAa,EAAE,CAAC;MAAE;MAClBiD,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACftD,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAAC2C,eAAe,CAACY,4CAA4C,CAAC;MAChEC,IAAI,EAAEJ;KACP,CAAC,CAACK,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACA,IAAI,CAACrD,SAAS,GAAGmD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C,GAAGA,IAAI;YACP1E,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL;UACA,IAAI,CAACmB,SAAS,GAAG,EAAE;QACrB;MACF,CAAC;MACDwD,KAAK,EAAGA,KAAK,IAAI;QACf;QACA,IAAI,CAACxD,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEAd,oBAAoBA,CAAA;IAClB;EAAA;EAGFmC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACrB,SAAS,CAACyD,MAAM,CAACF,IAAI,IAAIA,IAAI,CAAC1E,QAAQ,CAAC;EACrD;EAEA6E,qBAAqBA,CAAA;IACnB;IACA,OAAO,CAAC;EACV;EAEA7B,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACU,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAAClB,gBAAgB,EAAE,CAAChB,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAuB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACU,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B;QACA,IAAI,CAACoB,2BAA2B,EAAE;MACpC;MACA,IAAI,CAACpB,WAAW,EAAE;IACpB;EACF;EAEAoB,2BAA2BA,CAAA;IACzB,MAAMC,aAAa,GAAG,IAAI,CAACvC,gBAAgB,EAAE;IAE7CuC,aAAa,CAACC,OAAO,CAACN,IAAI,IAAG;MAC3B,IAAIA,IAAI,CAAC7D,WAAW,IAAI,CAAC,IAAI,CAAC8C,uBAAuB,CAACsB,GAAG,CAACP,IAAI,CAAC7D,WAAW,CAAC,EAAE;QAC3E;QACA,IAAI,CAACqE,sBAAsB,CAACR,IAAI,CAAC7D,WAAW,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAqE,sBAAsBA,CAACC,UAAkB;IACvC,MAAMC,IAAI,GAA8B;MACtCD,UAAU,EAAEA;KACb;IAED,IAAI,CAAC7B,eAAe,CAAC+B,yCAAyC,CAAC;MAC7DlB,IAAI,EAAEiB;KACP,CAAC,CAAChB,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAACb,uBAAuB,CAAC2B,GAAG,CAACH,UAAU,EAAEb,QAAQ,CAACE,OAAO,CAAC;QAChE;MACF,CAAC;MACDG,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAAChB,uBAAuB,CAAC2B,GAAG,CAACH,UAAU,EAAE,EAAE,CAAC;MAClD;KACD,CAAC;EACJ;EAEAnD,kBAAkBA,CAACmD,UAAkB;IACnC,OAAO,IAAI,CAACxB,uBAAuB,CAAC4B,GAAG,CAACJ,UAAU,CAAC,IAAI,EAAE;EAC3D;EAEAvC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACc,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEA8B,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAAC/B,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAjB,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACD,gBAAgB,EAAE,CAAChB,MAAM,GAAG,CAAC;EAC3C;EAEAa,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACI,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAU,aAAaA,CAAA;IACX,MAAMuC,MAAM,GAAwB;MAClCC,OAAO,EAAE,QAAQ;MAAE;MACnBC,SAAS,EAAE,MAAM;MACjBb,aAAa,EAAE,IAAI,CAACvC,gBAAgB,EAAE;MACtCqD,UAAU,EAAE,IAAI,CAAChB,qBAAqB;KACvC;IAED,IAAI,CAACpB,eAAe,CAACqC,IAAI,CAACJ,MAAM,CAAC;IACjC,IAAI,CAACK,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACzC,SAAS,CAACwC,KAAK,EAAE;EACxB;EAEA;EACA;EAEQE,KAAKA,CAAA;IACX,IAAI,CAACvC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACvC,SAAS,GAAG,EAAE;EACrB;EAEQ6E,eAAeA,CAAA;IACrB,IAAI,CAACtC,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACvC,SAAS,CAAC6D,OAAO,CAACkB,QAAQ,IAAG;MAChCA,QAAQ,CAAClG,QAAQ,GAAG,KAAK;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAAC2D,uBAAuB,CAACwC,KAAK,EAAE;EACtC;;;uCAhLW/C,8BAA8B,EAAA9D,EAAA,CAAA8G,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAhH,EAAA,CAAA8G,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA9BpD,8BAA8B;MAAAqD,SAAA;MAAAC,MAAA;QAAAlD,WAAA;MAAA;MAAAmD,OAAA;QAAAlD,eAAA;MAAA;MAAAmD,UAAA;MAAAC,QAAA,GAAAvH,EAAA,CAAAwH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAf,QAAA,WAAAgB,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpCvC7H,EAFJ,CAAAC,cAAA,iBAAuC,wBACS,aACV;UAAAD,EAAA,CAAAgB,MAAA,iDAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAC/CjB,EAAA,CAAAC,cAAA,gBAA2D;UAAlBD,EAAA,CAAAY,UAAA,mBAAAmH,gEAAA;YAAA,OAASD,GAAA,CAAArB,KAAA,EAAO;UAAA,EAAC;UACxDzG,EAAA,CAAA8B,SAAA,iBAAwC;UAE5C9B,EADE,CAAAiB,YAAA,EAAS,EACM;UAKbjB,EAHJ,CAAAC,cAAA,sBAA0C,aAElB,aAKjB;UAAAD,EAAA,CAAAgB,MAAA,kCAAO;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UAChBjB,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAgB,MAAA,mCAAO;UACZhB,EADY,CAAAiB,YAAA,EAAM,EACZ;UAoCNjB,EAjCA,CAAA0B,UAAA,KAAAsG,8CAAA,kBAAoD,KAAAC,8CAAA,mBAiCA;UA2DtDjI,EAAA,CAAAiB,YAAA,EAAe;UAIXjB,EAFJ,CAAAC,cAAA,0BAA8C,eACjB,YACnB;UAAAD,EAAA,CAAAgB,MAAA,IAAuB;UAC/BhB,EAD+B,CAAAiB,YAAA,EAAO,EAChC;UAEJjB,EADF,CAAAC,cAAA,eAA0B,kBAC0B;UAAlBD,EAAA,CAAAY,UAAA,mBAAAsH,iEAAA;YAAA,OAASJ,GAAA,CAAArB,KAAA,EAAO;UAAA,EAAC;UAACzG,EAAA,CAAAgB,MAAA,oBAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAI7DjB,EAHA,CAAA0B,UAAA,KAAAyG,iDAAA,qBAAiF,KAAAC,iDAAA,qBAE1D,KAAAC,iDAAA,qBAEK;UAGlCrI,EAFI,CAAAiB,YAAA,EAAM,EACS,EACT;;;UAxHmBjB,EAAA,CAAAkB,SAAA,GAIrB;UAJqBlB,EAAA,CAAA4B,UAAA,YAAA5B,EAAA,CAAAsI,eAAA,IAAAC,GAAA,EAAAT,GAAA,CAAA1D,WAAA,QAAA0D,GAAA,CAAA1D,WAAA,MAAA0D,GAAA,CAAA1D,WAAA,MAIrB;UACqBpE,EAAA,CAAAkB,SAAA,GAIrB;UAJqBlB,EAAA,CAAA4B,UAAA,YAAA5B,EAAA,CAAAsI,eAAA,KAAAC,GAAA,EAAAT,GAAA,CAAA1D,WAAA,QAAA0D,GAAA,CAAA1D,WAAA,MAAA0D,GAAA,CAAA1D,WAAA,MAIrB;UAIEpE,EAAA,CAAAkB,SAAA,GAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAAkG,GAAA,CAAA1D,WAAA,OAAuB;UAiCvBpE,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAAkG,GAAA,CAAA1D,WAAA,OAAuB;UA+DrBpE,EAAA,CAAAkB,SAAA,GAAuB;UAAvBlB,EAAA,CAAAoB,iBAAA,CAAA0G,GAAA,CAAA5B,eAAA,GAAuB;UAIpBlG,EAAA,CAAAkB,SAAA,GAAqB;UAArBlB,EAAA,CAAA4B,UAAA,SAAAkG,GAAA,CAAA1D,WAAA,KAAqB;UACrBpE,EAAA,CAAAkB,SAAA,EAAqB;UAArBlB,EAAA,CAAA4B,UAAA,SAAAkG,GAAA,CAAA1D,WAAA,KAAqB;UAErBpE,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAA4B,UAAA,SAAAkG,GAAA,CAAA1D,WAAA,OAAuB;;;qBDnGlC3E,YAAY,EAAA+I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZjJ,WAAW,EAAAkJ,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EACXnJ,YAAY,EAAAsH,EAAA,CAAA8B,eAAA,EAAA9B,EAAA,CAAA+B,mBAAA,EAAA/B,EAAA,CAAAgC,qBAAA,EAAAhC,EAAA,CAAAiC,qBAAA,EACZtJ,cAAc,EAAAqH,EAAA,CAAAkC,iBAAA,EACdtJ,YAAY,EAAAoH,EAAA,CAAAmC,eAAA,EACZtJ,gBAAgB,EAAAmH,EAAA,CAAAoC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}