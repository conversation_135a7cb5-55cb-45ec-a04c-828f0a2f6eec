{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as visualSolution from '../../visual/visualSolution.js';\nimport { makeBrushCommonSelectorForSeries } from './selector.js';\nimport * as throttleUtil from '../../util/throttle.js';\nimport BrushTargetManager from '../helper/BrushTargetManager.js';\nvar STATE_LIST = ['inBrush', 'outOfBrush'];\nvar DISPATCH_METHOD = '__ecBrushSelect';\nvar DISPATCH_FLAG = '__ecInBrushSelectEvent';\n;\nexport function layoutCovers(ecModel) {\n  ecModel.eachComponent({\n    mainType: 'brush'\n  }, function (brushModel) {\n    var brushTargetManager = brushModel.brushTargetManager = new BrushTargetManager(brushModel.option, ecModel);\n    brushTargetManager.setInputRanges(brushModel.areas, ecModel);\n  });\n}\n/**\r\n * Register the visual encoding if this modules required.\r\n */\nexport default function brushVisual(ecModel, api, payload) {\n  var brushSelected = [];\n  var throttleType;\n  var throttleDelay;\n  ecModel.eachComponent({\n    mainType: 'brush'\n  }, function (brushModel) {\n    payload && payload.type === 'takeGlobalCursor' && brushModel.setBrushOption(payload.key === 'brush' ? payload.brushOption : {\n      brushType: false\n    });\n  });\n  layoutCovers(ecModel);\n  ecModel.eachComponent({\n    mainType: 'brush'\n  }, function (brushModel, brushIndex) {\n    var thisBrushSelected = {\n      brushId: brushModel.id,\n      brushIndex: brushIndex,\n      brushName: brushModel.name,\n      areas: zrUtil.clone(brushModel.areas),\n      selected: []\n    };\n    // Every brush component exists in event params, convenient\n    // for user to find by index.\n    brushSelected.push(thisBrushSelected);\n    var brushOption = brushModel.option;\n    var brushLink = brushOption.brushLink;\n    var linkedSeriesMap = [];\n    var selectedDataIndexForLink = [];\n    var rangeInfoBySeries = [];\n    var hasBrushExists = false;\n    if (!brushIndex) {\n      // Only the first throttle setting works.\n      throttleType = brushOption.throttleType;\n      throttleDelay = brushOption.throttleDelay;\n    }\n    // Add boundingRect and selectors to range.\n    var areas = zrUtil.map(brushModel.areas, function (area) {\n      var builder = boundingRectBuilders[area.brushType];\n      var selectableArea = zrUtil.defaults({\n        boundingRect: builder ? builder(area) : void 0\n      }, area);\n      selectableArea.selectors = makeBrushCommonSelectorForSeries(selectableArea);\n      return selectableArea;\n    });\n    var visualMappings = visualSolution.createVisualMappings(brushModel.option, STATE_LIST, function (mappingOption) {\n      mappingOption.mappingMethod = 'fixed';\n    });\n    zrUtil.isArray(brushLink) && zrUtil.each(brushLink, function (seriesIndex) {\n      linkedSeriesMap[seriesIndex] = 1;\n    });\n    function linkOthers(seriesIndex) {\n      return brushLink === 'all' || !!linkedSeriesMap[seriesIndex];\n    }\n    // If no supported brush or no brush on the series,\n    // all visuals should be in original state.\n    function brushed(rangeInfoList) {\n      return !!rangeInfoList.length;\n    }\n    /**\r\n     * Logic for each series: (If the logic has to be modified one day, do it carefully!)\r\n     *\r\n     * ( brushed ┬ && ┬hasBrushExist ┬ && linkOthers  ) => StepA: ┬record, ┬ StepB: ┬visualByRecord.\r\n     *   !brushed┘    ├hasBrushExist ┤                            └nothing,┘        ├visualByRecord.\r\n     *                └!hasBrushExist┘                                              └nothing.\r\n     * ( !brushed  && ┬hasBrushExist ┬ && linkOthers  ) => StepA:  nothing,  StepB: ┬visualByRecord.\r\n     *                └!hasBrushExist┘                                              └nothing.\r\n     * ( brushed ┬ &&                     !linkOthers ) => StepA:  nothing,  StepB: ┬visualByCheck.\r\n     *   !brushed┘                                                                  └nothing.\r\n     * ( !brushed  &&                     !linkOthers ) => StepA:  nothing,  StepB:  nothing.\r\n     */\n    // Step A\n    ecModel.eachSeries(function (seriesModel, seriesIndex) {\n      var rangeInfoList = rangeInfoBySeries[seriesIndex] = [];\n      seriesModel.subType === 'parallel' ? stepAParallel(seriesModel, seriesIndex) : stepAOthers(seriesModel, seriesIndex, rangeInfoList);\n    });\n    function stepAParallel(seriesModel, seriesIndex) {\n      var coordSys = seriesModel.coordinateSystem;\n      hasBrushExists = hasBrushExists || coordSys.hasAxisBrushed();\n      linkOthers(seriesIndex) && coordSys.eachActiveState(seriesModel.getData(), function (activeState, dataIndex) {\n        activeState === 'active' && (selectedDataIndexForLink[dataIndex] = 1);\n      });\n    }\n    function stepAOthers(seriesModel, seriesIndex, rangeInfoList) {\n      if (!seriesModel.brushSelector || brushModelNotControll(brushModel, seriesIndex)) {\n        return;\n      }\n      zrUtil.each(areas, function (area) {\n        if (brushModel.brushTargetManager.controlSeries(area, seriesModel, ecModel)) {\n          rangeInfoList.push(area);\n        }\n        hasBrushExists = hasBrushExists || brushed(rangeInfoList);\n      });\n      if (linkOthers(seriesIndex) && brushed(rangeInfoList)) {\n        var data_1 = seriesModel.getData();\n        data_1.each(function (dataIndex) {\n          if (checkInRange(seriesModel, rangeInfoList, data_1, dataIndex)) {\n            selectedDataIndexForLink[dataIndex] = 1;\n          }\n        });\n      }\n    }\n    // Step B\n    ecModel.eachSeries(function (seriesModel, seriesIndex) {\n      var seriesBrushSelected = {\n        seriesId: seriesModel.id,\n        seriesIndex: seriesIndex,\n        seriesName: seriesModel.name,\n        dataIndex: []\n      };\n      // Every series exists in event params, convenient\n      // for user to find series by seriesIndex.\n      thisBrushSelected.selected.push(seriesBrushSelected);\n      var rangeInfoList = rangeInfoBySeries[seriesIndex];\n      var data = seriesModel.getData();\n      var getValueState = linkOthers(seriesIndex) ? function (dataIndex) {\n        return selectedDataIndexForLink[dataIndex] ? (seriesBrushSelected.dataIndex.push(data.getRawIndex(dataIndex)), 'inBrush') : 'outOfBrush';\n      } : function (dataIndex) {\n        return checkInRange(seriesModel, rangeInfoList, data, dataIndex) ? (seriesBrushSelected.dataIndex.push(data.getRawIndex(dataIndex)), 'inBrush') : 'outOfBrush';\n      };\n      // If no supported brush or no brush, all visuals are in original state.\n      (linkOthers(seriesIndex) ? hasBrushExists : brushed(rangeInfoList)) && visualSolution.applyVisual(STATE_LIST, visualMappings, data, getValueState);\n    });\n  });\n  dispatchAction(api, throttleType, throttleDelay, brushSelected, payload);\n}\n;\nfunction dispatchAction(api, throttleType, throttleDelay, brushSelected, payload) {\n  // This event will not be triggered when `setOpion`, otherwise dead lock may\n  // triggered when do `setOption` in event listener, which we do not find\n  // satisfactory way to solve yet. Some considered resolutions:\n  // (a) Diff with prevoius selected data ant only trigger event when changed.\n  // But store previous data and diff precisely (i.e., not only by dataIndex, but\n  // also detect value changes in selected data) might bring complexity or fragility.\n  // (b) Use spectial param like `silent` to suppress event triggering.\n  // But such kind of volatile param may be weird in `setOption`.\n  if (!payload) {\n    return;\n  }\n  var zr = api.getZr();\n  if (zr[DISPATCH_FLAG]) {\n    return;\n  }\n  if (!zr[DISPATCH_METHOD]) {\n    zr[DISPATCH_METHOD] = doDispatch;\n  }\n  var fn = throttleUtil.createOrUpdate(zr, DISPATCH_METHOD, throttleDelay, throttleType);\n  fn(api, brushSelected);\n}\nfunction doDispatch(api, brushSelected) {\n  if (!api.isDisposed()) {\n    var zr = api.getZr();\n    zr[DISPATCH_FLAG] = true;\n    api.dispatchAction({\n      type: 'brushSelect',\n      batch: brushSelected\n    });\n    zr[DISPATCH_FLAG] = false;\n  }\n}\nfunction checkInRange(seriesModel, rangeInfoList, data, dataIndex) {\n  for (var i = 0, len = rangeInfoList.length; i < len; i++) {\n    var area = rangeInfoList[i];\n    if (seriesModel.brushSelector(dataIndex, data, area.selectors, area)) {\n      return true;\n    }\n  }\n}\nfunction brushModelNotControll(brushModel, seriesIndex) {\n  var seriesIndices = brushModel.option.seriesIndex;\n  return seriesIndices != null && seriesIndices !== 'all' && (zrUtil.isArray(seriesIndices) ? zrUtil.indexOf(seriesIndices, seriesIndex) < 0 : seriesIndex !== seriesIndices);\n}\nvar boundingRectBuilders = {\n  rect: function (area) {\n    return getBoundingRectFromMinMax(area.range);\n  },\n  polygon: function (area) {\n    var minMax;\n    var range = area.range;\n    for (var i = 0, len = range.length; i < len; i++) {\n      minMax = minMax || [[Infinity, -Infinity], [Infinity, -Infinity]];\n      var rg = range[i];\n      rg[0] < minMax[0][0] && (minMax[0][0] = rg[0]);\n      rg[0] > minMax[0][1] && (minMax[0][1] = rg[0]);\n      rg[1] < minMax[1][0] && (minMax[1][0] = rg[1]);\n      rg[1] > minMax[1][1] && (minMax[1][1] = rg[1]);\n    }\n    return minMax && getBoundingRectFromMinMax(minMax);\n  }\n};\nfunction getBoundingRectFromMinMax(minMax) {\n  return new BoundingRect(minMax[0][0], minMax[1][0], minMax[0][1] - minMax[0][0], minMax[1][1] - minMax[1][0]);\n}", "map": {"version": 3, "names": ["zrUtil", "BoundingRect", "visualSolution", "makeBrushCommonSelectorForSeries", "throttleUtil", "BrushTargetManager", "STATE_LIST", "DISPATCH_METHOD", "DISPATCH_FLAG", "layoutCovers", "ecModel", "eachComponent", "mainType", "brushModel", "brushTargetManager", "option", "setInputRanges", "areas", "brushVisual", "api", "payload", "brushSelected", "throttleType", "throttle<PERSON><PERSON><PERSON>", "type", "setBrushOption", "key", "brushOption", "brushType", "brushIndex", "thisBrushSelected", "brushId", "id", "brushName", "name", "clone", "selected", "push", "brushLink", "linkedSeriesMap", "selectedDataIndexForLink", "rangeInfoBySeries", "hasBrushExists", "map", "area", "builder", "boundingRectBuilders", "selectableArea", "defaults", "boundingRect", "selectors", "visualMappings", "createVisualMappings", "mappingOption", "mappingMethod", "isArray", "each", "seriesIndex", "linkOthers", "brushed", "rangeInfoList", "length", "eachSeries", "seriesModel", "subType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coordSys", "coordinateSystem", "hasAxisBrushed", "eachActiveState", "getData", "activeState", "dataIndex", "brushSelector", "brushModelNotControll", "controlSeries", "data_1", "checkInRange", "seriesBrushSelected", "seriesId", "seriesName", "data", "getValueState", "getRawIndex", "applyVisual", "dispatchAction", "zr", "getZr", "doDispatch", "fn", "createOrUpdate", "isDisposed", "batch", "i", "len", "seriesIndices", "indexOf", "rect", "getBoundingRectFromMinMax", "range", "polygon", "minMax", "Infinity", "rg"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/component/brush/visualEncoding.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as visualSolution from '../../visual/visualSolution.js';\nimport { makeBrushCommonSelectorForSeries } from './selector.js';\nimport * as throttleUtil from '../../util/throttle.js';\nimport BrushTargetManager from '../helper/BrushTargetManager.js';\nvar STATE_LIST = ['inBrush', 'outOfBrush'];\nvar DISPATCH_METHOD = '__ecBrushSelect';\nvar DISPATCH_FLAG = '__ecInBrushSelectEvent';\n;\nexport function layoutCovers(ecModel) {\n  ecModel.eachComponent({\n    mainType: 'brush'\n  }, function (brushModel) {\n    var brushTargetManager = brushModel.brushTargetManager = new BrushTargetManager(brushModel.option, ecModel);\n    brushTargetManager.setInputRanges(brushModel.areas, ecModel);\n  });\n}\n/**\r\n * Register the visual encoding if this modules required.\r\n */\nexport default function brushVisual(ecModel, api, payload) {\n  var brushSelected = [];\n  var throttleType;\n  var throttleDelay;\n  ecModel.eachComponent({\n    mainType: 'brush'\n  }, function (brushModel) {\n    payload && payload.type === 'takeGlobalCursor' && brushModel.setBrushOption(payload.key === 'brush' ? payload.brushOption : {\n      brushType: false\n    });\n  });\n  layoutCovers(ecModel);\n  ecModel.eachComponent({\n    mainType: 'brush'\n  }, function (brushModel, brushIndex) {\n    var thisBrushSelected = {\n      brushId: brushModel.id,\n      brushIndex: brushIndex,\n      brushName: brushModel.name,\n      areas: zrUtil.clone(brushModel.areas),\n      selected: []\n    };\n    // Every brush component exists in event params, convenient\n    // for user to find by index.\n    brushSelected.push(thisBrushSelected);\n    var brushOption = brushModel.option;\n    var brushLink = brushOption.brushLink;\n    var linkedSeriesMap = [];\n    var selectedDataIndexForLink = [];\n    var rangeInfoBySeries = [];\n    var hasBrushExists = false;\n    if (!brushIndex) {\n      // Only the first throttle setting works.\n      throttleType = brushOption.throttleType;\n      throttleDelay = brushOption.throttleDelay;\n    }\n    // Add boundingRect and selectors to range.\n    var areas = zrUtil.map(brushModel.areas, function (area) {\n      var builder = boundingRectBuilders[area.brushType];\n      var selectableArea = zrUtil.defaults({\n        boundingRect: builder ? builder(area) : void 0\n      }, area);\n      selectableArea.selectors = makeBrushCommonSelectorForSeries(selectableArea);\n      return selectableArea;\n    });\n    var visualMappings = visualSolution.createVisualMappings(brushModel.option, STATE_LIST, function (mappingOption) {\n      mappingOption.mappingMethod = 'fixed';\n    });\n    zrUtil.isArray(brushLink) && zrUtil.each(brushLink, function (seriesIndex) {\n      linkedSeriesMap[seriesIndex] = 1;\n    });\n    function linkOthers(seriesIndex) {\n      return brushLink === 'all' || !!linkedSeriesMap[seriesIndex];\n    }\n    // If no supported brush or no brush on the series,\n    // all visuals should be in original state.\n    function brushed(rangeInfoList) {\n      return !!rangeInfoList.length;\n    }\n    /**\r\n     * Logic for each series: (If the logic has to be modified one day, do it carefully!)\r\n     *\r\n     * ( brushed ┬ && ┬hasBrushExist ┬ && linkOthers  ) => StepA: ┬record, ┬ StepB: ┬visualByRecord.\r\n     *   !brushed┘    ├hasBrushExist ┤                            └nothing,┘        ├visualByRecord.\r\n     *                └!hasBrushExist┘                                              └nothing.\r\n     * ( !brushed  && ┬hasBrushExist ┬ && linkOthers  ) => StepA:  nothing,  StepB: ┬visualByRecord.\r\n     *                └!hasBrushExist┘                                              └nothing.\r\n     * ( brushed ┬ &&                     !linkOthers ) => StepA:  nothing,  StepB: ┬visualByCheck.\r\n     *   !brushed┘                                                                  └nothing.\r\n     * ( !brushed  &&                     !linkOthers ) => StepA:  nothing,  StepB:  nothing.\r\n     */\n    // Step A\n    ecModel.eachSeries(function (seriesModel, seriesIndex) {\n      var rangeInfoList = rangeInfoBySeries[seriesIndex] = [];\n      seriesModel.subType === 'parallel' ? stepAParallel(seriesModel, seriesIndex) : stepAOthers(seriesModel, seriesIndex, rangeInfoList);\n    });\n    function stepAParallel(seriesModel, seriesIndex) {\n      var coordSys = seriesModel.coordinateSystem;\n      hasBrushExists = hasBrushExists || coordSys.hasAxisBrushed();\n      linkOthers(seriesIndex) && coordSys.eachActiveState(seriesModel.getData(), function (activeState, dataIndex) {\n        activeState === 'active' && (selectedDataIndexForLink[dataIndex] = 1);\n      });\n    }\n    function stepAOthers(seriesModel, seriesIndex, rangeInfoList) {\n      if (!seriesModel.brushSelector || brushModelNotControll(brushModel, seriesIndex)) {\n        return;\n      }\n      zrUtil.each(areas, function (area) {\n        if (brushModel.brushTargetManager.controlSeries(area, seriesModel, ecModel)) {\n          rangeInfoList.push(area);\n        }\n        hasBrushExists = hasBrushExists || brushed(rangeInfoList);\n      });\n      if (linkOthers(seriesIndex) && brushed(rangeInfoList)) {\n        var data_1 = seriesModel.getData();\n        data_1.each(function (dataIndex) {\n          if (checkInRange(seriesModel, rangeInfoList, data_1, dataIndex)) {\n            selectedDataIndexForLink[dataIndex] = 1;\n          }\n        });\n      }\n    }\n    // Step B\n    ecModel.eachSeries(function (seriesModel, seriesIndex) {\n      var seriesBrushSelected = {\n        seriesId: seriesModel.id,\n        seriesIndex: seriesIndex,\n        seriesName: seriesModel.name,\n        dataIndex: []\n      };\n      // Every series exists in event params, convenient\n      // for user to find series by seriesIndex.\n      thisBrushSelected.selected.push(seriesBrushSelected);\n      var rangeInfoList = rangeInfoBySeries[seriesIndex];\n      var data = seriesModel.getData();\n      var getValueState = linkOthers(seriesIndex) ? function (dataIndex) {\n        return selectedDataIndexForLink[dataIndex] ? (seriesBrushSelected.dataIndex.push(data.getRawIndex(dataIndex)), 'inBrush') : 'outOfBrush';\n      } : function (dataIndex) {\n        return checkInRange(seriesModel, rangeInfoList, data, dataIndex) ? (seriesBrushSelected.dataIndex.push(data.getRawIndex(dataIndex)), 'inBrush') : 'outOfBrush';\n      };\n      // If no supported brush or no brush, all visuals are in original state.\n      (linkOthers(seriesIndex) ? hasBrushExists : brushed(rangeInfoList)) && visualSolution.applyVisual(STATE_LIST, visualMappings, data, getValueState);\n    });\n  });\n  dispatchAction(api, throttleType, throttleDelay, brushSelected, payload);\n}\n;\nfunction dispatchAction(api, throttleType, throttleDelay, brushSelected, payload) {\n  // This event will not be triggered when `setOpion`, otherwise dead lock may\n  // triggered when do `setOption` in event listener, which we do not find\n  // satisfactory way to solve yet. Some considered resolutions:\n  // (a) Diff with prevoius selected data ant only trigger event when changed.\n  // But store previous data and diff precisely (i.e., not only by dataIndex, but\n  // also detect value changes in selected data) might bring complexity or fragility.\n  // (b) Use spectial param like `silent` to suppress event triggering.\n  // But such kind of volatile param may be weird in `setOption`.\n  if (!payload) {\n    return;\n  }\n  var zr = api.getZr();\n  if (zr[DISPATCH_FLAG]) {\n    return;\n  }\n  if (!zr[DISPATCH_METHOD]) {\n    zr[DISPATCH_METHOD] = doDispatch;\n  }\n  var fn = throttleUtil.createOrUpdate(zr, DISPATCH_METHOD, throttleDelay, throttleType);\n  fn(api, brushSelected);\n}\nfunction doDispatch(api, brushSelected) {\n  if (!api.isDisposed()) {\n    var zr = api.getZr();\n    zr[DISPATCH_FLAG] = true;\n    api.dispatchAction({\n      type: 'brushSelect',\n      batch: brushSelected\n    });\n    zr[DISPATCH_FLAG] = false;\n  }\n}\nfunction checkInRange(seriesModel, rangeInfoList, data, dataIndex) {\n  for (var i = 0, len = rangeInfoList.length; i < len; i++) {\n    var area = rangeInfoList[i];\n    if (seriesModel.brushSelector(dataIndex, data, area.selectors, area)) {\n      return true;\n    }\n  }\n}\nfunction brushModelNotControll(brushModel, seriesIndex) {\n  var seriesIndices = brushModel.option.seriesIndex;\n  return seriesIndices != null && seriesIndices !== 'all' && (zrUtil.isArray(seriesIndices) ? zrUtil.indexOf(seriesIndices, seriesIndex) < 0 : seriesIndex !== seriesIndices);\n}\nvar boundingRectBuilders = {\n  rect: function (area) {\n    return getBoundingRectFromMinMax(area.range);\n  },\n  polygon: function (area) {\n    var minMax;\n    var range = area.range;\n    for (var i = 0, len = range.length; i < len; i++) {\n      minMax = minMax || [[Infinity, -Infinity], [Infinity, -Infinity]];\n      var rg = range[i];\n      rg[0] < minMax[0][0] && (minMax[0][0] = rg[0]);\n      rg[0] > minMax[0][1] && (minMax[0][1] = rg[0]);\n      rg[1] < minMax[1][0] && (minMax[1][0] = rg[1]);\n      rg[1] > minMax[1][1] && (minMax[1][1] = rg[1]);\n    }\n    return minMax && getBoundingRectFromMinMax(minMax);\n  }\n};\nfunction getBoundingRectFromMinMax(minMax) {\n  return new BoundingRect(minMax[0][0], minMax[1][0], minMax[0][1] - minMax[0][0], minMax[1][1] - minMax[1][0]);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAO,KAAKC,cAAc,MAAM,gCAAgC;AAChE,SAASC,gCAAgC,QAAQ,eAAe;AAChE,OAAO,KAAKC,YAAY,MAAM,wBAAwB;AACtD,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,IAAIC,UAAU,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC;AAC1C,IAAIC,eAAe,GAAG,iBAAiB;AACvC,IAAIC,aAAa,GAAG,wBAAwB;AAC5C;AACA,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAE;EACpCA,OAAO,CAACC,aAAa,CAAC;IACpBC,QAAQ,EAAE;EACZ,CAAC,EAAE,UAAUC,UAAU,EAAE;IACvB,IAAIC,kBAAkB,GAAGD,UAAU,CAACC,kBAAkB,GAAG,IAAIT,kBAAkB,CAACQ,UAAU,CAACE,MAAM,EAAEL,OAAO,CAAC;IAC3GI,kBAAkB,CAACE,cAAc,CAACH,UAAU,CAACI,KAAK,EAAEP,OAAO,CAAC;EAC9D,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA,eAAe,SAASQ,WAAWA,CAACR,OAAO,EAAES,GAAG,EAAEC,OAAO,EAAE;EACzD,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIC,YAAY;EAChB,IAAIC,aAAa;EACjBb,OAAO,CAACC,aAAa,CAAC;IACpBC,QAAQ,EAAE;EACZ,CAAC,EAAE,UAAUC,UAAU,EAAE;IACvBO,OAAO,IAAIA,OAAO,CAACI,IAAI,KAAK,kBAAkB,IAAIX,UAAU,CAACY,cAAc,CAACL,OAAO,CAACM,GAAG,KAAK,OAAO,GAAGN,OAAO,CAACO,WAAW,GAAG;MAC1HC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC,CAAC;EACFnB,YAAY,CAACC,OAAO,CAAC;EACrBA,OAAO,CAACC,aAAa,CAAC;IACpBC,QAAQ,EAAE;EACZ,CAAC,EAAE,UAAUC,UAAU,EAAEgB,UAAU,EAAE;IACnC,IAAIC,iBAAiB,GAAG;MACtBC,OAAO,EAAElB,UAAU,CAACmB,EAAE;MACtBH,UAAU,EAAEA,UAAU;MACtBI,SAAS,EAAEpB,UAAU,CAACqB,IAAI;MAC1BjB,KAAK,EAAEjB,MAAM,CAACmC,KAAK,CAACtB,UAAU,CAACI,KAAK,CAAC;MACrCmB,QAAQ,EAAE;IACZ,CAAC;IACD;IACA;IACAf,aAAa,CAACgB,IAAI,CAACP,iBAAiB,CAAC;IACrC,IAAIH,WAAW,GAAGd,UAAU,CAACE,MAAM;IACnC,IAAIuB,SAAS,GAAGX,WAAW,CAACW,SAAS;IACrC,IAAIC,eAAe,GAAG,EAAE;IACxB,IAAIC,wBAAwB,GAAG,EAAE;IACjC,IAAIC,iBAAiB,GAAG,EAAE;IAC1B,IAAIC,cAAc,GAAG,KAAK;IAC1B,IAAI,CAACb,UAAU,EAAE;MACf;MACAP,YAAY,GAAGK,WAAW,CAACL,YAAY;MACvCC,aAAa,GAAGI,WAAW,CAACJ,aAAa;IAC3C;IACA;IACA,IAAIN,KAAK,GAAGjB,MAAM,CAAC2C,GAAG,CAAC9B,UAAU,CAACI,KAAK,EAAE,UAAU2B,IAAI,EAAE;MACvD,IAAIC,OAAO,GAAGC,oBAAoB,CAACF,IAAI,CAAChB,SAAS,CAAC;MAClD,IAAImB,cAAc,GAAG/C,MAAM,CAACgD,QAAQ,CAAC;QACnCC,YAAY,EAAEJ,OAAO,GAAGA,OAAO,CAACD,IAAI,CAAC,GAAG,KAAK;MAC/C,CAAC,EAAEA,IAAI,CAAC;MACRG,cAAc,CAACG,SAAS,GAAG/C,gCAAgC,CAAC4C,cAAc,CAAC;MAC3E,OAAOA,cAAc;IACvB,CAAC,CAAC;IACF,IAAII,cAAc,GAAGjD,cAAc,CAACkD,oBAAoB,CAACvC,UAAU,CAACE,MAAM,EAAET,UAAU,EAAE,UAAU+C,aAAa,EAAE;MAC/GA,aAAa,CAACC,aAAa,GAAG,OAAO;IACvC,CAAC,CAAC;IACFtD,MAAM,CAACuD,OAAO,CAACjB,SAAS,CAAC,IAAItC,MAAM,CAACwD,IAAI,CAAClB,SAAS,EAAE,UAAUmB,WAAW,EAAE;MACzElB,eAAe,CAACkB,WAAW,CAAC,GAAG,CAAC;IAClC,CAAC,CAAC;IACF,SAASC,UAAUA,CAACD,WAAW,EAAE;MAC/B,OAAOnB,SAAS,KAAK,KAAK,IAAI,CAAC,CAACC,eAAe,CAACkB,WAAW,CAAC;IAC9D;IACA;IACA;IACA,SAASE,OAAOA,CAACC,aAAa,EAAE;MAC9B,OAAO,CAAC,CAACA,aAAa,CAACC,MAAM;IAC/B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI;IACAnD,OAAO,CAACoD,UAAU,CAAC,UAAUC,WAAW,EAAEN,WAAW,EAAE;MACrD,IAAIG,aAAa,GAAGnB,iBAAiB,CAACgB,WAAW,CAAC,GAAG,EAAE;MACvDM,WAAW,CAACC,OAAO,KAAK,UAAU,GAAGC,aAAa,CAACF,WAAW,EAAEN,WAAW,CAAC,GAAGS,WAAW,CAACH,WAAW,EAAEN,WAAW,EAAEG,aAAa,CAAC;IACrI,CAAC,CAAC;IACF,SAASK,aAAaA,CAACF,WAAW,EAAEN,WAAW,EAAE;MAC/C,IAAIU,QAAQ,GAAGJ,WAAW,CAACK,gBAAgB;MAC3C1B,cAAc,GAAGA,cAAc,IAAIyB,QAAQ,CAACE,cAAc,CAAC,CAAC;MAC5DX,UAAU,CAACD,WAAW,CAAC,IAAIU,QAAQ,CAACG,eAAe,CAACP,WAAW,CAACQ,OAAO,CAAC,CAAC,EAAE,UAAUC,WAAW,EAAEC,SAAS,EAAE;QAC3GD,WAAW,KAAK,QAAQ,KAAKhC,wBAAwB,CAACiC,SAAS,CAAC,GAAG,CAAC,CAAC;MACvE,CAAC,CAAC;IACJ;IACA,SAASP,WAAWA,CAACH,WAAW,EAAEN,WAAW,EAAEG,aAAa,EAAE;MAC5D,IAAI,CAACG,WAAW,CAACW,aAAa,IAAIC,qBAAqB,CAAC9D,UAAU,EAAE4C,WAAW,CAAC,EAAE;QAChF;MACF;MACAzD,MAAM,CAACwD,IAAI,CAACvC,KAAK,EAAE,UAAU2B,IAAI,EAAE;QACjC,IAAI/B,UAAU,CAACC,kBAAkB,CAAC8D,aAAa,CAAChC,IAAI,EAAEmB,WAAW,EAAErD,OAAO,CAAC,EAAE;UAC3EkD,aAAa,CAACvB,IAAI,CAACO,IAAI,CAAC;QAC1B;QACAF,cAAc,GAAGA,cAAc,IAAIiB,OAAO,CAACC,aAAa,CAAC;MAC3D,CAAC,CAAC;MACF,IAAIF,UAAU,CAACD,WAAW,CAAC,IAAIE,OAAO,CAACC,aAAa,CAAC,EAAE;QACrD,IAAIiB,MAAM,GAAGd,WAAW,CAACQ,OAAO,CAAC,CAAC;QAClCM,MAAM,CAACrB,IAAI,CAAC,UAAUiB,SAAS,EAAE;UAC/B,IAAIK,YAAY,CAACf,WAAW,EAAEH,aAAa,EAAEiB,MAAM,EAAEJ,SAAS,CAAC,EAAE;YAC/DjC,wBAAwB,CAACiC,SAAS,CAAC,GAAG,CAAC;UACzC;QACF,CAAC,CAAC;MACJ;IACF;IACA;IACA/D,OAAO,CAACoD,UAAU,CAAC,UAAUC,WAAW,EAAEN,WAAW,EAAE;MACrD,IAAIsB,mBAAmB,GAAG;QACxBC,QAAQ,EAAEjB,WAAW,CAAC/B,EAAE;QACxByB,WAAW,EAAEA,WAAW;QACxBwB,UAAU,EAAElB,WAAW,CAAC7B,IAAI;QAC5BuC,SAAS,EAAE;MACb,CAAC;MACD;MACA;MACA3C,iBAAiB,CAACM,QAAQ,CAACC,IAAI,CAAC0C,mBAAmB,CAAC;MACpD,IAAInB,aAAa,GAAGnB,iBAAiB,CAACgB,WAAW,CAAC;MAClD,IAAIyB,IAAI,GAAGnB,WAAW,CAACQ,OAAO,CAAC,CAAC;MAChC,IAAIY,aAAa,GAAGzB,UAAU,CAACD,WAAW,CAAC,GAAG,UAAUgB,SAAS,EAAE;QACjE,OAAOjC,wBAAwB,CAACiC,SAAS,CAAC,IAAIM,mBAAmB,CAACN,SAAS,CAACpC,IAAI,CAAC6C,IAAI,CAACE,WAAW,CAACX,SAAS,CAAC,CAAC,EAAE,SAAS,IAAI,YAAY;MAC1I,CAAC,GAAG,UAAUA,SAAS,EAAE;QACvB,OAAOK,YAAY,CAACf,WAAW,EAAEH,aAAa,EAAEsB,IAAI,EAAET,SAAS,CAAC,IAAIM,mBAAmB,CAACN,SAAS,CAACpC,IAAI,CAAC6C,IAAI,CAACE,WAAW,CAACX,SAAS,CAAC,CAAC,EAAE,SAAS,IAAI,YAAY;MAChK,CAAC;MACD;MACA,CAACf,UAAU,CAACD,WAAW,CAAC,GAAGf,cAAc,GAAGiB,OAAO,CAACC,aAAa,CAAC,KAAK1D,cAAc,CAACmF,WAAW,CAAC/E,UAAU,EAAE6C,cAAc,EAAE+B,IAAI,EAAEC,aAAa,CAAC;IACpJ,CAAC,CAAC;EACJ,CAAC,CAAC;EACFG,cAAc,CAACnE,GAAG,EAAEG,YAAY,EAAEC,aAAa,EAAEF,aAAa,EAAED,OAAO,CAAC;AAC1E;AACA;AACA,SAASkE,cAAcA,CAACnE,GAAG,EAAEG,YAAY,EAAEC,aAAa,EAAEF,aAAa,EAAED,OAAO,EAAE;EAChF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,CAACA,OAAO,EAAE;IACZ;EACF;EACA,IAAImE,EAAE,GAAGpE,GAAG,CAACqE,KAAK,CAAC,CAAC;EACpB,IAAID,EAAE,CAAC/E,aAAa,CAAC,EAAE;IACrB;EACF;EACA,IAAI,CAAC+E,EAAE,CAAChF,eAAe,CAAC,EAAE;IACxBgF,EAAE,CAAChF,eAAe,CAAC,GAAGkF,UAAU;EAClC;EACA,IAAIC,EAAE,GAAGtF,YAAY,CAACuF,cAAc,CAACJ,EAAE,EAAEhF,eAAe,EAAEgB,aAAa,EAAED,YAAY,CAAC;EACtFoE,EAAE,CAACvE,GAAG,EAAEE,aAAa,CAAC;AACxB;AACA,SAASoE,UAAUA,CAACtE,GAAG,EAAEE,aAAa,EAAE;EACtC,IAAI,CAACF,GAAG,CAACyE,UAAU,CAAC,CAAC,EAAE;IACrB,IAAIL,EAAE,GAAGpE,GAAG,CAACqE,KAAK,CAAC,CAAC;IACpBD,EAAE,CAAC/E,aAAa,CAAC,GAAG,IAAI;IACxBW,GAAG,CAACmE,cAAc,CAAC;MACjB9D,IAAI,EAAE,aAAa;MACnBqE,KAAK,EAAExE;IACT,CAAC,CAAC;IACFkE,EAAE,CAAC/E,aAAa,CAAC,GAAG,KAAK;EAC3B;AACF;AACA,SAASsE,YAAYA,CAACf,WAAW,EAAEH,aAAa,EAAEsB,IAAI,EAAET,SAAS,EAAE;EACjE,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGnC,aAAa,CAACC,MAAM,EAAEiC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IACxD,IAAIlD,IAAI,GAAGgB,aAAa,CAACkC,CAAC,CAAC;IAC3B,IAAI/B,WAAW,CAACW,aAAa,CAACD,SAAS,EAAES,IAAI,EAAEtC,IAAI,CAACM,SAAS,EAAEN,IAAI,CAAC,EAAE;MACpE,OAAO,IAAI;IACb;EACF;AACF;AACA,SAAS+B,qBAAqBA,CAAC9D,UAAU,EAAE4C,WAAW,EAAE;EACtD,IAAIuC,aAAa,GAAGnF,UAAU,CAACE,MAAM,CAAC0C,WAAW;EACjD,OAAOuC,aAAa,IAAI,IAAI,IAAIA,aAAa,KAAK,KAAK,KAAKhG,MAAM,CAACuD,OAAO,CAACyC,aAAa,CAAC,GAAGhG,MAAM,CAACiG,OAAO,CAACD,aAAa,EAAEvC,WAAW,CAAC,GAAG,CAAC,GAAGA,WAAW,KAAKuC,aAAa,CAAC;AAC7K;AACA,IAAIlD,oBAAoB,GAAG;EACzBoD,IAAI,EAAE,SAAAA,CAAUtD,IAAI,EAAE;IACpB,OAAOuD,yBAAyB,CAACvD,IAAI,CAACwD,KAAK,CAAC;EAC9C,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAUzD,IAAI,EAAE;IACvB,IAAI0D,MAAM;IACV,IAAIF,KAAK,GAAGxD,IAAI,CAACwD,KAAK;IACtB,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGK,KAAK,CAACvC,MAAM,EAAEiC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAChDQ,MAAM,GAAGA,MAAM,IAAI,CAAC,CAACC,QAAQ,EAAE,CAACA,QAAQ,CAAC,EAAE,CAACA,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;MACjE,IAAIC,EAAE,GAAGJ,KAAK,CAACN,CAAC,CAAC;MACjBU,EAAE,CAAC,CAAC,CAAC,GAAGF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9CA,EAAE,CAAC,CAAC,CAAC,GAAGF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9CA,EAAE,CAAC,CAAC,CAAC,GAAGF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9CA,EAAE,CAAC,CAAC,CAAC,GAAGF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,CAAC;IAChD;IACA,OAAOF,MAAM,IAAIH,yBAAyB,CAACG,MAAM,CAAC;EACpD;AACF,CAAC;AACD,SAASH,yBAAyBA,CAACG,MAAM,EAAE;EACzC,OAAO,IAAIrG,YAAY,CAACqG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}