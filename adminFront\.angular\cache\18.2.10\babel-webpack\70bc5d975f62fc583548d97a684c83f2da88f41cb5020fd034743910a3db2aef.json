{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiTemplateDeleteTemplatePost$Json } from '../fn/template/api-template-delete-template-post-json';\nimport { apiTemplateDeleteTemplatePost$Plain } from '../fn/template/api-template-delete-template-post-plain';\nimport { apiTemplateGetTemplateByIdPost$Json } from '../fn/template/api-template-get-template-by-id-post-json';\nimport { apiTemplateGetTemplateByIdPost$Plain } from '../fn/template/api-template-get-template-by-id-post-plain';\nimport { apiTemplateGetTemplateDetailByIdPost$Json } from '../fn/template/api-template-get-template-detail-by-id-post-json';\nimport { apiTemplateGetTemplateDetailByIdPost$Plain } from '../fn/template/api-template-get-template-detail-by-id-post-plain';\nimport { apiTemplateGetTemplateListForCommonPost$Json } from '../fn/template/api-template-get-template-list-for-common-post-json';\nimport { apiTemplateGetTemplateListForCommonPost$Plain } from '../fn/template/api-template-get-template-list-for-common-post-plain';\nimport { apiTemplateGetTemplateListPost$Json } from '../fn/template/api-template-get-template-list-post-json';\nimport { apiTemplateGetTemplateListPost$Plain } from '../fn/template/api-template-get-template-list-post-plain';\nimport { apiTemplateSaveTemplatePost$Json } from '../fn/template/api-template-save-template-post-json';\nimport { apiTemplateSaveTemplatePost$Plain } from '../fn/template/api-template-save-template-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class TemplateService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiTemplateGetTemplateListPost()` */\n  static {\n    this.ApiTemplateGetTemplateListPostPath = '/api/Template/GetTemplateList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateGetTemplateListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateListPost$Plain$Response(params, context) {\n    return apiTemplateGetTemplateListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateListPost$Plain(params, context) {\n    return this.apiTemplateGetTemplateListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateGetTemplateListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateListPost$Json$Response(params, context) {\n    return apiTemplateGetTemplateListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateListPost$Json(params, context) {\n    return this.apiTemplateGetTemplateListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiTemplateGetTemplateByIdPost()` */\n  static {\n    this.ApiTemplateGetTemplateByIdPostPath = '/api/Template/GetTemplateById';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateGetTemplateByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateByIdPost$Plain$Response(params, context) {\n    return apiTemplateGetTemplateByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateByIdPost$Plain(params, context) {\n    return this.apiTemplateGetTemplateByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateGetTemplateByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateByIdPost$Json$Response(params, context) {\n    return apiTemplateGetTemplateByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateByIdPost$Json(params, context) {\n    return this.apiTemplateGetTemplateByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiTemplateDeleteTemplatePost()` */\n  static {\n    this.ApiTemplateDeleteTemplatePostPath = '/api/Template/DeleteTemplate';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateDeleteTemplatePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateDeleteTemplatePost$Plain$Response(params, context) {\n    return apiTemplateDeleteTemplatePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateDeleteTemplatePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateDeleteTemplatePost$Plain(params, context) {\n    return this.apiTemplateDeleteTemplatePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateDeleteTemplatePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateDeleteTemplatePost$Json$Response(params, context) {\n    return apiTemplateDeleteTemplatePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateDeleteTemplatePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateDeleteTemplatePost$Json(params, context) {\n    return this.apiTemplateDeleteTemplatePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiTemplateSaveTemplatePost()` */\n  static {\n    this.ApiTemplateSaveTemplatePostPath = '/api/Template/SaveTemplate';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateSaveTemplatePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateSaveTemplatePost$Plain$Response(params, context) {\n    return apiTemplateSaveTemplatePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateSaveTemplatePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateSaveTemplatePost$Plain(params, context) {\n    return this.apiTemplateSaveTemplatePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateSaveTemplatePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateSaveTemplatePost$Json$Response(params, context) {\n    return apiTemplateSaveTemplatePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateSaveTemplatePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateSaveTemplatePost$Json(params, context) {\n    return this.apiTemplateSaveTemplatePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiTemplateGetTemplateDetailByIdPost()` */\n  static {\n    this.ApiTemplateGetTemplateDetailByIdPostPath = '/api/Template/GetTemplateDetailById';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateGetTemplateDetailByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateDetailByIdPost$Plain$Response(params, context) {\n    return apiTemplateGetTemplateDetailByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateDetailByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateDetailByIdPost$Plain(params, context) {\n    return this.apiTemplateGetTemplateDetailByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateGetTemplateDetailByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateDetailByIdPost$Json$Response(params, context) {\n    return apiTemplateGetTemplateDetailByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateDetailByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateDetailByIdPost$Json(params, context) {\n    return this.apiTemplateGetTemplateDetailByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiTemplateGetTemplateListForCommonPost()` */\n  static {\n    this.ApiTemplateGetTemplateListForCommonPostPath = '/api/Template/GetTemplateListForCommon';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateGetTemplateListForCommonPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateListForCommonPost$Plain$Response(params, context) {\n    return apiTemplateGetTemplateListForCommonPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateListForCommonPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateListForCommonPost$Plain(params, context) {\n    return this.apiTemplateGetTemplateListForCommonPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiTemplateGetTemplateListForCommonPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateListForCommonPost$Json$Response(params, context) {\n    return apiTemplateGetTemplateListForCommonPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateListForCommonPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiTemplateGetTemplateListForCommonPost$Json(params, context) {\n    return this.apiTemplateGetTemplateListForCommonPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function TemplateService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TemplateService,\n      factory: TemplateService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiTemplateDeleteTemplatePost$Json", "apiTemplateDeleteTemplatePost$Plain", "apiTemplateGetTemplateByIdPost$Json", "apiTemplateGetTemplateByIdPost$Plain", "apiTemplateGetTemplateDetailByIdPost$Json", "apiTemplateGetTemplateDetailByIdPost$Plain", "apiTemplateGetTemplateListForCommonPost$Json", "apiTemplateGetTemplateListForCommonPost$Plain", "apiTemplateGetTemplateListPost$Json", "apiTemplateGetTemplateListPost$Plain", "apiTemplateSaveTemplatePost$Json", "apiTemplateSaveTemplatePost$Plain", "TemplateService", "constructor", "config", "http", "ApiTemplateGetTemplateListPostPath", "apiTemplateGetTemplateListPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiTemplateGetTemplateListPost$Json$Response", "ApiTemplateGetTemplateByIdPostPath", "apiTemplateGetTemplateByIdPost$Plain$Response", "apiTemplateGetTemplateByIdPost$Json$Response", "ApiTemplateDeleteTemplatePostPath", "apiTemplateDeleteTemplatePost$Plain$Response", "apiTemplateDeleteTemplatePost$Json$Response", "ApiTemplateSaveTemplatePostPath", "apiTemplateSaveTemplatePost$Plain$Response", "apiTemplateSaveTemplatePost$Json$Response", "ApiTemplateGetTemplateDetailByIdPostPath", "apiTemplateGetTemplateDetailByIdPost$Plain$Response", "apiTemplateGetTemplateDetailByIdPost$Json$Response", "ApiTemplateGetTemplateListForCommonPostPath", "apiTemplateGetTemplateListForCommonPost$Plain$Response", "apiTemplateGetTemplateListForCommonPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\template.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiTemplateDeleteTemplatePost$Json } from '../fn/template/api-template-delete-template-post-json';\r\nimport { ApiTemplateDeleteTemplatePost$Json$Params } from '../fn/template/api-template-delete-template-post-json';\r\nimport { apiTemplateDeleteTemplatePost$Plain } from '../fn/template/api-template-delete-template-post-plain';\r\nimport { ApiTemplateDeleteTemplatePost$Plain$Params } from '../fn/template/api-template-delete-template-post-plain';\r\nimport { apiTemplateGetTemplateByIdPost$Json } from '../fn/template/api-template-get-template-by-id-post-json';\r\nimport { ApiTemplateGetTemplateByIdPost$Json$Params } from '../fn/template/api-template-get-template-by-id-post-json';\r\nimport { apiTemplateGetTemplateByIdPost$Plain } from '../fn/template/api-template-get-template-by-id-post-plain';\r\nimport { ApiTemplateGetTemplateByIdPost$Plain$Params } from '../fn/template/api-template-get-template-by-id-post-plain';\r\nimport { apiTemplateGetTemplateDetailByIdPost$Json } from '../fn/template/api-template-get-template-detail-by-id-post-json';\r\nimport { ApiTemplateGetTemplateDetailByIdPost$Json$Params } from '../fn/template/api-template-get-template-detail-by-id-post-json';\r\nimport { apiTemplateGetTemplateDetailByIdPost$Plain } from '../fn/template/api-template-get-template-detail-by-id-post-plain';\r\nimport { ApiTemplateGetTemplateDetailByIdPost$Plain$Params } from '../fn/template/api-template-get-template-detail-by-id-post-plain';\r\nimport { apiTemplateGetTemplateListForCommonPost$Json } from '../fn/template/api-template-get-template-list-for-common-post-json';\r\nimport { ApiTemplateGetTemplateListForCommonPost$Json$Params } from '../fn/template/api-template-get-template-list-for-common-post-json';\r\nimport { apiTemplateGetTemplateListForCommonPost$Plain } from '../fn/template/api-template-get-template-list-for-common-post-plain';\r\nimport { ApiTemplateGetTemplateListForCommonPost$Plain$Params } from '../fn/template/api-template-get-template-list-for-common-post-plain';\r\nimport { apiTemplateGetTemplateListPost$Json } from '../fn/template/api-template-get-template-list-post-json';\r\nimport { ApiTemplateGetTemplateListPost$Json$Params } from '../fn/template/api-template-get-template-list-post-json';\r\nimport { apiTemplateGetTemplateListPost$Plain } from '../fn/template/api-template-get-template-list-post-plain';\r\nimport { ApiTemplateGetTemplateListPost$Plain$Params } from '../fn/template/api-template-get-template-list-post-plain';\r\nimport { apiTemplateSaveTemplatePost$Json } from '../fn/template/api-template-save-template-post-json';\r\nimport { ApiTemplateSaveTemplatePost$Json$Params } from '../fn/template/api-template-save-template-post-json';\r\nimport { apiTemplateSaveTemplatePost$Plain } from '../fn/template/api-template-save-template-post-plain';\r\nimport { ApiTemplateSaveTemplatePost$Plain$Params } from '../fn/template/api-template-save-template-post-plain';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\nimport { TemplateDetailItemListResponseBase } from '../models/template-detail-item-list-response-base';\r\nimport { TemplateGetListResponseListResponseBase } from '../models/template-get-list-response-list-response-base';\r\nimport { TemplateGetListResponseResponseBase } from '../models/template-get-list-response-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class TemplateService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiTemplateGetTemplateListPost()` */\r\n  static readonly ApiTemplateGetTemplateListPostPath = '/api/Template/GetTemplateList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateGetTemplateListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateListPost$Plain$Response(params?: ApiTemplateGetTemplateListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseListResponseBase>> {\r\n    return apiTemplateGetTemplateListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateListPost$Plain(params?: ApiTemplateGetTemplateListPost$Plain$Params, context?: HttpContext): Observable<TemplateGetListResponseListResponseBase> {\r\n    return this.apiTemplateGetTemplateListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TemplateGetListResponseListResponseBase>): TemplateGetListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateGetTemplateListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateListPost$Json$Response(params?: ApiTemplateGetTemplateListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseListResponseBase>> {\r\n    return apiTemplateGetTemplateListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateListPost$Json(params?: ApiTemplateGetTemplateListPost$Json$Params, context?: HttpContext): Observable<TemplateGetListResponseListResponseBase> {\r\n    return this.apiTemplateGetTemplateListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TemplateGetListResponseListResponseBase>): TemplateGetListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiTemplateGetTemplateByIdPost()` */\r\n  static readonly ApiTemplateGetTemplateByIdPostPath = '/api/Template/GetTemplateById';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateGetTemplateByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateByIdPost$Plain$Response(params?: ApiTemplateGetTemplateByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseResponseBase>> {\r\n    return apiTemplateGetTemplateByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateByIdPost$Plain(params?: ApiTemplateGetTemplateByIdPost$Plain$Params, context?: HttpContext): Observable<TemplateGetListResponseResponseBase> {\r\n    return this.apiTemplateGetTemplateByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TemplateGetListResponseResponseBase>): TemplateGetListResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateGetTemplateByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateByIdPost$Json$Response(params?: ApiTemplateGetTemplateByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseResponseBase>> {\r\n    return apiTemplateGetTemplateByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateByIdPost$Json(params?: ApiTemplateGetTemplateByIdPost$Json$Params, context?: HttpContext): Observable<TemplateGetListResponseResponseBase> {\r\n    return this.apiTemplateGetTemplateByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TemplateGetListResponseResponseBase>): TemplateGetListResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiTemplateDeleteTemplatePost()` */\r\n  static readonly ApiTemplateDeleteTemplatePostPath = '/api/Template/DeleteTemplate';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateDeleteTemplatePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateDeleteTemplatePost$Plain$Response(params?: ApiTemplateDeleteTemplatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiTemplateDeleteTemplatePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateDeleteTemplatePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateDeleteTemplatePost$Plain(params?: ApiTemplateDeleteTemplatePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiTemplateDeleteTemplatePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateDeleteTemplatePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateDeleteTemplatePost$Json$Response(params?: ApiTemplateDeleteTemplatePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiTemplateDeleteTemplatePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateDeleteTemplatePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateDeleteTemplatePost$Json(params?: ApiTemplateDeleteTemplatePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiTemplateDeleteTemplatePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiTemplateSaveTemplatePost()` */\r\n  static readonly ApiTemplateSaveTemplatePostPath = '/api/Template/SaveTemplate';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateSaveTemplatePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateSaveTemplatePost$Plain$Response(params?: ApiTemplateSaveTemplatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiTemplateSaveTemplatePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateSaveTemplatePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateSaveTemplatePost$Plain(params?: ApiTemplateSaveTemplatePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiTemplateSaveTemplatePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateSaveTemplatePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateSaveTemplatePost$Json$Response(params?: ApiTemplateSaveTemplatePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiTemplateSaveTemplatePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateSaveTemplatePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateSaveTemplatePost$Json(params?: ApiTemplateSaveTemplatePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiTemplateSaveTemplatePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiTemplateGetTemplateDetailByIdPost()` */\r\n  static readonly ApiTemplateGetTemplateDetailByIdPostPath = '/api/Template/GetTemplateDetailById';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateGetTemplateDetailByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateDetailByIdPost$Plain$Response(params?: ApiTemplateGetTemplateDetailByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateDetailItemListResponseBase>> {\r\n    return apiTemplateGetTemplateDetailByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateDetailByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateDetailByIdPost$Plain(params?: ApiTemplateGetTemplateDetailByIdPost$Plain$Params, context?: HttpContext): Observable<TemplateDetailItemListResponseBase> {\r\n    return this.apiTemplateGetTemplateDetailByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TemplateDetailItemListResponseBase>): TemplateDetailItemListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateGetTemplateDetailByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateDetailByIdPost$Json$Response(params?: ApiTemplateGetTemplateDetailByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateDetailItemListResponseBase>> {\r\n    return apiTemplateGetTemplateDetailByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateDetailByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateDetailByIdPost$Json(params?: ApiTemplateGetTemplateDetailByIdPost$Json$Params, context?: HttpContext): Observable<TemplateDetailItemListResponseBase> {\r\n    return this.apiTemplateGetTemplateDetailByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TemplateDetailItemListResponseBase>): TemplateDetailItemListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiTemplateGetTemplateListForCommonPost()` */\r\n  static readonly ApiTemplateGetTemplateListForCommonPostPath = '/api/Template/GetTemplateListForCommon';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateGetTemplateListForCommonPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateListForCommonPost$Plain$Response(params?: ApiTemplateGetTemplateListForCommonPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseListResponseBase>> {\r\n    return apiTemplateGetTemplateListForCommonPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateListForCommonPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateListForCommonPost$Plain(params?: ApiTemplateGetTemplateListForCommonPost$Plain$Params, context?: HttpContext): Observable<TemplateGetListResponseListResponseBase> {\r\n    return this.apiTemplateGetTemplateListForCommonPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TemplateGetListResponseListResponseBase>): TemplateGetListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiTemplateGetTemplateListForCommonPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateListForCommonPost$Json$Response(params?: ApiTemplateGetTemplateListForCommonPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TemplateGetListResponseListResponseBase>> {\r\n    return apiTemplateGetTemplateListForCommonPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiTemplateGetTemplateListForCommonPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiTemplateGetTemplateListForCommonPost$Json(params?: ApiTemplateGetTemplateListForCommonPost$Json$Params, context?: HttpContext): Observable<TemplateGetListResponseListResponseBase> {\r\n    return this.apiTemplateGetTemplateListForCommonPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TemplateGetListResponseListResponseBase>): TemplateGetListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAOA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,kCAAkC,QAAQ,uDAAuD;AAE1G,SAASC,mCAAmC,QAAQ,wDAAwD;AAE5G,SAASC,mCAAmC,QAAQ,0DAA0D;AAE9G,SAASC,oCAAoC,QAAQ,2DAA2D;AAEhH,SAASC,yCAAyC,QAAQ,iEAAiE;AAE3H,SAASC,0CAA0C,QAAQ,kEAAkE;AAE7H,SAASC,4CAA4C,QAAQ,oEAAoE;AAEjI,SAASC,6CAA6C,QAAQ,qEAAqE;AAEnI,SAASC,mCAAmC,QAAQ,yDAAyD;AAE7G,SAASC,oCAAoC,QAAQ,0DAA0D;AAE/G,SAASC,gCAAgC,QAAQ,qDAAqD;AAEtG,SAASC,iCAAiC,QAAQ,sDAAsD;;;;AAQxG,OAAM,MAAOC,eAAgB,SAAQb,WAAW;EAC9Cc,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,kCAAkC,GAAG,+BAA+B;EAAC;EAErF;;;;;;EAMAC,6CAA6CA,CAACC,MAAoD,EAAEC,OAAqB;IACvH,OAAOV,oCAAoC,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAV,oCAAoCA,CAACS,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACF,6CAA6C,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7EvB,GAAG,CAAEwB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAC,4CAA4CA,CAACN,MAAmD,EAAEC,OAAqB;IACrH,OAAOX,mCAAmC,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAX,mCAAmCA,CAACU,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACK,4CAA4C,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5EvB,GAAG,CAAEwB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;IACgB,KAAAE,kCAAkC,GAAG,+BAA+B;EAAC;EAErF;;;;;;EAMAC,6CAA6CA,CAACR,MAAoD,EAAEC,OAAqB;IACvH,OAAOhB,oCAAoC,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAhB,oCAAoCA,CAACe,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACO,6CAA6C,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7EvB,GAAG,CAAEwB,CAA0D,IAA0CA,CAAC,CAACC,IAAI,CAAC,CACjH;EACH;EAEA;;;;;;EAMAI,4CAA4CA,CAACT,MAAmD,EAAEC,OAAqB;IACrH,OAAOjB,mCAAmC,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAjB,mCAAmCA,CAACgB,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACQ,4CAA4C,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5EvB,GAAG,CAAEwB,CAA0D,IAA0CA,CAAC,CAACC,IAAI,CAAC,CACjH;EACH;EAEA;;IACgB,KAAAK,iCAAiC,GAAG,8BAA8B;EAAC;EAEnF;;;;;;EAMAC,4CAA4CA,CAACX,MAAmD,EAAEC,OAAqB;IACrH,OAAOlB,mCAAmC,CAAC,IAAI,CAACc,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAlB,mCAAmCA,CAACiB,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACU,4CAA4C,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5EvB,GAAG,CAAEwB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAO,2CAA2CA,CAACZ,MAAkD,EAAEC,OAAqB;IACnH,OAAOnB,kCAAkC,CAAC,IAAI,CAACe,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAnB,kCAAkCA,CAACkB,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAACW,2CAA2C,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3EvB,GAAG,CAAEwB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAQ,+BAA+B,GAAG,4BAA4B;EAAC;EAE/E;;;;;;EAMAC,0CAA0CA,CAACd,MAAiD,EAAEC,OAAqB;IACjH,OAAOR,iCAAiC,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMAR,iCAAiCA,CAACO,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACa,0CAA0C,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1EvB,GAAG,CAAEwB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAU,yCAAyCA,CAACf,MAAgD,EAAEC,OAAqB;IAC/G,OAAOT,gCAAgC,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMAT,gCAAgCA,CAACQ,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACc,yCAAyC,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzEvB,GAAG,CAAEwB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAW,wCAAwC,GAAG,qCAAqC;EAAC;EAEjG;;;;;;EAMAC,mDAAmDA,CAACjB,MAA0D,EAAEC,OAAqB;IACnI,OAAOd,0CAA0C,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7F;EAEA;;;;;;EAMAd,0CAA0CA,CAACa,MAA0D,EAAEC,OAAqB;IAC1H,OAAO,IAAI,CAACgB,mDAAmD,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnFvB,GAAG,CAAEwB,CAAyD,IAAyCA,CAAC,CAACC,IAAI,CAAC,CAC/G;EACH;EAEA;;;;;;EAMAa,kDAAkDA,CAAClB,MAAyD,EAAEC,OAAqB;IACjI,OAAOf,yCAAyC,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC5F;EAEA;;;;;;EAMAf,yCAAyCA,CAACc,MAAyD,EAAEC,OAAqB;IACxH,OAAO,IAAI,CAACiB,kDAAkD,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAClFvB,GAAG,CAAEwB,CAAyD,IAAyCA,CAAC,CAACC,IAAI,CAAC,CAC/G;EACH;EAEA;;IACgB,KAAAc,2CAA2C,GAAG,wCAAwC;EAAC;EAEvG;;;;;;EAMAC,sDAAsDA,CAACpB,MAA6D,EAAEC,OAAqB;IACzI,OAAOZ,6CAA6C,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChG;EAEA;;;;;;EAMAZ,6CAA6CA,CAACW,MAA6D,EAAEC,OAAqB;IAChI,OAAO,IAAI,CAACmB,sDAAsD,CAACpB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtFvB,GAAG,CAAEwB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAgB,qDAAqDA,CAACrB,MAA4D,EAAEC,OAAqB;IACvI,OAAOb,4CAA4C,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/F;EAEA;;;;;;EAMAb,4CAA4CA,CAACY,MAA4D,EAAEC,OAAqB;IAC9H,OAAO,IAAI,CAACoB,qDAAqD,CAACrB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrFvB,GAAG,CAAEwB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;;;uCA7RWX,eAAe,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAfjC,eAAe;MAAAkC,OAAA,EAAflC,eAAe,CAAAmC,IAAA;MAAAC,UAAA,EADF;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}