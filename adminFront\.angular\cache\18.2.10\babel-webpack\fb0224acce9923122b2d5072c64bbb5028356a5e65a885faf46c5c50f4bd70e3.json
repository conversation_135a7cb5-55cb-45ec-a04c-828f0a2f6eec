{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport { subPixelOptimizeLine } from '../helper/subPixelOptimize.js';\nvar subPixelOptimizeOutputShape = {};\nvar LineShape = function () {\n  function LineShape() {\n    this.x1 = 0;\n    this.y1 = 0;\n    this.x2 = 0;\n    this.y2 = 0;\n    this.percent = 1;\n  }\n  return LineShape;\n}();\nexport { LineShape };\nvar Line = function (_super) {\n  __extends(Line, _super);\n  function Line(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Line.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  Line.prototype.getDefaultShape = function () {\n    return new LineShape();\n  };\n  Line.prototype.buildPath = function (ctx, shape) {\n    var x1;\n    var y1;\n    var x2;\n    var y2;\n    if (this.subPixelOptimize) {\n      var optimizedShape = subPixelOptimizeLine(subPixelOptimizeOutputShape, shape, this.style);\n      x1 = optimizedShape.x1;\n      y1 = optimizedShape.y1;\n      x2 = optimizedShape.x2;\n      y2 = optimizedShape.y2;\n    } else {\n      x1 = shape.x1;\n      y1 = shape.y1;\n      x2 = shape.x2;\n      y2 = shape.y2;\n    }\n    var percent = shape.percent;\n    if (percent === 0) {\n      return;\n    }\n    ctx.moveTo(x1, y1);\n    if (percent < 1) {\n      x2 = x1 * (1 - percent) + x2 * percent;\n      y2 = y1 * (1 - percent) + y2 * percent;\n    }\n    ctx.lineTo(x2, y2);\n  };\n  Line.prototype.pointAt = function (p) {\n    var shape = this.shape;\n    return [shape.x1 * (1 - p) + shape.x2 * p, shape.y1 * (1 - p) + shape.y2 * p];\n  };\n  return Line;\n}(Path);\nLine.prototype.type = 'line';\nexport default Line;", "map": {"version": 3, "names": ["__extends", "Path", "subPixelOptimizeLine", "subPixelOptimizeOutputShape", "LineShape", "x1", "y1", "x2", "y2", "percent", "Line", "_super", "opts", "call", "prototype", "getDefaultStyle", "stroke", "fill", "getDefaultShape", "buildPath", "ctx", "shape", "subPixelOptimize", "optimizedShape", "style", "moveTo", "lineTo", "pointAt", "p", "type"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/zrender/lib/graphic/shape/Line.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport { subPixelOptimizeLine } from '../helper/subPixelOptimize.js';\nvar subPixelOptimizeOutputShape = {};\nvar LineShape = (function () {\n    function LineShape() {\n        this.x1 = 0;\n        this.y1 = 0;\n        this.x2 = 0;\n        this.y2 = 0;\n        this.percent = 1;\n    }\n    return LineShape;\n}());\nexport { LineShape };\nvar Line = (function (_super) {\n    __extends(Line, _super);\n    function Line(opts) {\n        return _super.call(this, opts) || this;\n    }\n    Line.prototype.getDefaultStyle = function () {\n        return {\n            stroke: '#000',\n            fill: null\n        };\n    };\n    Line.prototype.getDefaultShape = function () {\n        return new LineShape();\n    };\n    Line.prototype.buildPath = function (ctx, shape) {\n        var x1;\n        var y1;\n        var x2;\n        var y2;\n        if (this.subPixelOptimize) {\n            var optimizedShape = subPixelOptimizeLine(subPixelOptimizeOutputShape, shape, this.style);\n            x1 = optimizedShape.x1;\n            y1 = optimizedShape.y1;\n            x2 = optimizedShape.x2;\n            y2 = optimizedShape.y2;\n        }\n        else {\n            x1 = shape.x1;\n            y1 = shape.y1;\n            x2 = shape.x2;\n            y2 = shape.y2;\n        }\n        var percent = shape.percent;\n        if (percent === 0) {\n            return;\n        }\n        ctx.moveTo(x1, y1);\n        if (percent < 1) {\n            x2 = x1 * (1 - percent) + x2 * percent;\n            y2 = y1 * (1 - percent) + y2 * percent;\n        }\n        ctx.lineTo(x2, y2);\n    };\n    Line.prototype.pointAt = function (p) {\n        var shape = this.shape;\n        return [\n            shape.x1 * (1 - p) + shape.x2 * p,\n            shape.y1 * (1 - p) + shape.y2 * p\n        ];\n    };\n    return Line;\n}(Path));\nLine.prototype.type = 'line';\nexport default Line;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,IAAIC,2BAA2B,GAAG,CAAC,CAAC;AACpC,IAAIC,SAAS,GAAI,YAAY;EACzB,SAASA,SAASA,CAAA,EAAG;IACjB,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,OAAO,GAAG,CAAC;EACpB;EACA,OAAOL,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,SAASA,SAAS;AAClB,IAAIM,IAAI,GAAI,UAAUC,MAAM,EAAE;EAC1BX,SAAS,CAACU,IAAI,EAAEC,MAAM,CAAC;EACvB,SAASD,IAAIA,CAACE,IAAI,EAAE;IAChB,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC,IAAI,IAAI;EAC1C;EACAF,IAAI,CAACI,SAAS,CAACC,eAAe,GAAG,YAAY;IACzC,OAAO;MACHC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE;IACV,CAAC;EACL,CAAC;EACDP,IAAI,CAACI,SAAS,CAACI,eAAe,GAAG,YAAY;IACzC,OAAO,IAAId,SAAS,CAAC,CAAC;EAC1B,CAAC;EACDM,IAAI,CAACI,SAAS,CAACK,SAAS,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAC7C,IAAIhB,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAI,IAAI,CAACc,gBAAgB,EAAE;MACvB,IAAIC,cAAc,GAAGrB,oBAAoB,CAACC,2BAA2B,EAAEkB,KAAK,EAAE,IAAI,CAACG,KAAK,CAAC;MACzFnB,EAAE,GAAGkB,cAAc,CAAClB,EAAE;MACtBC,EAAE,GAAGiB,cAAc,CAACjB,EAAE;MACtBC,EAAE,GAAGgB,cAAc,CAAChB,EAAE;MACtBC,EAAE,GAAGe,cAAc,CAACf,EAAE;IAC1B,CAAC,MACI;MACDH,EAAE,GAAGgB,KAAK,CAAChB,EAAE;MACbC,EAAE,GAAGe,KAAK,CAACf,EAAE;MACbC,EAAE,GAAGc,KAAK,CAACd,EAAE;MACbC,EAAE,GAAGa,KAAK,CAACb,EAAE;IACjB;IACA,IAAIC,OAAO,GAAGY,KAAK,CAACZ,OAAO;IAC3B,IAAIA,OAAO,KAAK,CAAC,EAAE;MACf;IACJ;IACAW,GAAG,CAACK,MAAM,CAACpB,EAAE,EAAEC,EAAE,CAAC;IAClB,IAAIG,OAAO,GAAG,CAAC,EAAE;MACbF,EAAE,GAAGF,EAAE,IAAI,CAAC,GAAGI,OAAO,CAAC,GAAGF,EAAE,GAAGE,OAAO;MACtCD,EAAE,GAAGF,EAAE,IAAI,CAAC,GAAGG,OAAO,CAAC,GAAGD,EAAE,GAAGC,OAAO;IAC1C;IACAW,GAAG,CAACM,MAAM,CAACnB,EAAE,EAAEC,EAAE,CAAC;EACtB,CAAC;EACDE,IAAI,CAACI,SAAS,CAACa,OAAO,GAAG,UAAUC,CAAC,EAAE;IAClC,IAAIP,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,OAAO,CACHA,KAAK,CAAChB,EAAE,IAAI,CAAC,GAAGuB,CAAC,CAAC,GAAGP,KAAK,CAACd,EAAE,GAAGqB,CAAC,EACjCP,KAAK,CAACf,EAAE,IAAI,CAAC,GAAGsB,CAAC,CAAC,GAAGP,KAAK,CAACb,EAAE,GAAGoB,CAAC,CACpC;EACL,CAAC;EACD,OAAOlB,IAAI;AACf,CAAC,CAACT,IAAI,CAAE;AACRS,IAAI,CAACI,SAAS,CAACe,IAAI,GAAG,MAAM;AAC5B,eAAenB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}