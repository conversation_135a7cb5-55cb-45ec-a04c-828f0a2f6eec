/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetSpaceByIdRequest } from '../../models/get-space-by-id-request';
import { GetSpaceListResponseResponseBase } from '../../models/get-space-list-response-response-base';

export interface ApiSpaceGetSpaceByIdPost$Plain$Params {
      body?: GetSpaceByIdRequest
}

export function apiSpaceGetSpaceByIdPost$Plain(http: HttpClient, rootUrl: string, params?: ApiSpaceGetSpaceByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiSpaceGetSpaceByIdPost$Plain.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetSpaceListResponseResponseBase>;
    })
  );
}

apiSpaceGetSpaceByIdPost$Plain.PATH = '/api/Space/GetSpaceById';
