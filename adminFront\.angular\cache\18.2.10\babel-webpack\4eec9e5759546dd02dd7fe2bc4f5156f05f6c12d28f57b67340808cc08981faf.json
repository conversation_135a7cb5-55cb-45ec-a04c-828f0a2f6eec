{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Maldivian [dv]\n//! author : Jawish Hameed : https://github.com/jawish\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var months = ['ޖެނުއަރީ', 'ފެބްރުއަރީ', 'މާރިޗު', 'އޭޕްރީލު', 'މޭ', 'ޖޫން', 'ޖުލައި', 'އޯގަސްޓު', 'ސެޕްޓެމްބަރު', 'އޮކްޓޯބަރު', 'ނޮވެމްބަރު', 'ޑިސެމްބަރު'],\n    weekdays = ['އާދިއްތަ', 'ހޯމަ', 'އަންގާރަ', 'ބުދަ', 'ބުރާސްފަތި', 'ހުކުރު', 'ހޮނިހިރު'];\n  var dv = moment.defineLocale('dv', {\n    months: months,\n    monthsShort: months,\n    weekdays: weekdays,\n    weekdaysShort: weekdays,\n    weekdaysMin: 'އާދި_ހޯމަ_އަން_ބުދަ_ބުރާ_ހުކު_ހޮނި'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'D/M/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /މކ|މފ/,\n    isPM: function (input) {\n      return 'މފ' === input;\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'މކ';\n      } else {\n        return 'މފ';\n      }\n    },\n    calendar: {\n      sameDay: '[މިއަދު] LT',\n      nextDay: '[މާދަމާ] LT',\n      nextWeek: 'dddd LT',\n      lastDay: '[އިއްޔެ] LT',\n      lastWeek: '[ފާއިތުވި] dddd LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'ތެރޭގައި %s',\n      past: 'ކުރިން %s',\n      s: 'ސިކުންތުކޮޅެއް',\n      ss: 'd% ސިކުންތު',\n      m: 'މިނިޓެއް',\n      mm: 'މިނިޓު %d',\n      h: 'ގަޑިއިރެއް',\n      hh: 'ގަޑިއިރު %d',\n      d: 'ދުވަހެއް',\n      dd: 'ދުވަސް %d',\n      M: 'މަހެއް',\n      MM: 'މަސް %d',\n      y: 'އަހަރެއް',\n      yy: 'އަހަރު %d'\n    },\n    preparse: function (string) {\n      return string.replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/,/g, '،');\n    },\n    week: {\n      dow: 7,\n      // Sunday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n    }\n  });\n  return dv;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}