.btn-group {
  button {
    margin-right: 0.25rem;

    &:last-child {
      margin-right: 0;
    }
  }
}

// ===== 組件特定樣式 =====
.btn-group {
  button {
    margin-right: 0.25rem;

    &:last-child {
      margin-right: 0;
    }
  }
}

// 保留原有的通用表格樣式作為後備
.table {
  th {
    background-color: var(--color-bg-2);
    font-weight: 600;
    border-bottom: 2px solid var(--color-bg-3);
  }

  tbody tr {
    &:hover {
      background-color: var(--color-bg-1);
    }
  }
}

.input-group {
  .input-group-append {
    .btn {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

nb-card-header {
  h5 {
    color: var(--color-fg-heading);
  }
}

.text-danger {
  color: var(--color-danger) !important;
}

// ===== 資訊提示框樣式 =====
.alert-info {
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);
    transform: translateY(-1px);
  }

  h6 {
    font-size: 1rem;
    letter-spacing: 0.3px;
  }

  p {
    line-height: 1.6;
    color: #6c757d !important;
  }

  i {
    flex-shrink: 0;
  }
}

// 通用間距類
.mr-2 {
  margin-right: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mt-3 {
  margin-top: 1rem;
}

// ===== 搜尋區域按鈕樣式 =====
.btn-secondary,
.btn-outline-secondary {
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  i {
    font-size: 0.875rem;
  }
}

.btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;

  &:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
  }
}

// ===== 模態框優化樣式 =====
// 新增和編輯模態框的通用樣式
nb-card {

  // 模態框陰影效果
  &[style*="box-shadow"] {
    transition: all 0.3s ease-in-out;

    nb-card-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

      h5 {
        font-size: 1.1rem;
        letter-spacing: 0.5px;

        i {
          font-size: 1rem;
        }
      }

      button {
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
          transform: scale(1.05);
        }
      }
    }

    nb-card-body {
      .form-group {
        position: relative;

        label {
          color: #495057;
          font-size: 0.875rem;
          letter-spacing: 0.3px;

          &.required-field::after {
            content: " *";
            color: #dc3545;
            font-weight: bold;
          }
        }

        input[nbInput] {
          transition: all 0.2s ease;
          font-size: 0.875rem;

          &:focus {
            border-color: #4a90e2;
            box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
            transform: translateY(-1px);
          }

          &::placeholder {
            color: #adb5bd;
            font-style: italic;
          }
        }

        nb-select {
          nb-option {
            padding: 8px 12px;

            span {
              font-size: 0.875rem;

              i {
                font-size: 0.75rem;
              }
            }
          }
        }
      }
    }

    nb-card-footer {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

      button {
        font-size: 0.875rem;
        font-weight: 500;
        letter-spacing: 0.3px;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        &.btn-primary {
          &:hover {
            background: linear-gradient(135deg, #357abd 0%, #2c5282 100%);
          }
        }

        &.btn-outline-secondary {
          &:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: #fff;
          }
        }

        i {
          font-size: 0.75rem;
        }
      }
    }
  }
}

// 輸入框和選擇框的統一樣式
.form-control,
nb-select {
  &:focus-within {
    border-color: #4a90e2;
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
  }
}

// 響應式調整
@media (max-width: 576px) {
  nb-card[style*="width: 550px"] {
    width: 95vw !important;
    margin: 0 auto;
  }
}