{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { Rect } from '../../util/graphic.js';\nimport * as formatUtil from '../../util/format.js';\nimport * as layout from '../../util/layout.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport ComponentView from '../../view/Component.js';\nvar VisualMapView = /** @class */function (_super) {\n  __extends(VisualMapView, _super);\n  function VisualMapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = VisualMapView.type;\n    _this.autoPositionValues = {\n      left: 1,\n      right: 1,\n      top: 1,\n      bottom: 1\n    };\n    return _this;\n  }\n  VisualMapView.prototype.init = function (ecModel, api) {\n    this.ecModel = ecModel;\n    this.api = api;\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapView.prototype.render = function (visualMapModel, ecModel, api, payload // TODO: TYPE\n  ) {\n    this.visualMapModel = visualMapModel;\n    if (visualMapModel.get('show') === false) {\n      this.group.removeAll();\n      return;\n    }\n    this.doRender(visualMapModel, ecModel, api, payload);\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapView.prototype.renderBackground = function (group) {\n    var visualMapModel = this.visualMapModel;\n    var padding = formatUtil.normalizeCssArray(visualMapModel.get('padding') || 0);\n    var rect = group.getBoundingRect();\n    group.add(new Rect({\n      z2: -1,\n      silent: true,\n      shape: {\n        x: rect.x - padding[3],\n        y: rect.y - padding[0],\n        width: rect.width + padding[3] + padding[1],\n        height: rect.height + padding[0] + padding[2]\n      },\n      style: {\n        fill: visualMapModel.get('backgroundColor'),\n        stroke: visualMapModel.get('borderColor'),\n        lineWidth: visualMapModel.get('borderWidth')\n      }\n    }));\n  };\n  /**\r\n   * @protected\r\n   * @param targetValue can be Infinity or -Infinity\r\n   * @param visualCluster Only can be 'color' 'opacity' 'symbol' 'symbolSize'\r\n   * @param opts\r\n   * @param opts.forceState Specify state, instead of using getValueState method.\r\n   * @param opts.convertOpacityToAlpha For color gradient in controller widget.\r\n   * @return {*} Visual value.\r\n   */\n  VisualMapView.prototype.getControllerVisual = function (targetValue, visualCluster, opts) {\n    opts = opts || {};\n    var forceState = opts.forceState;\n    var visualMapModel = this.visualMapModel;\n    var visualObj = {};\n    // Default values.\n    if (visualCluster === 'color') {\n      var defaultColor = visualMapModel.get('contentColor');\n      visualObj.color = defaultColor;\n    }\n    function getter(key) {\n      return visualObj[key];\n    }\n    function setter(key, value) {\n      visualObj[key] = value;\n    }\n    var mappings = visualMapModel.controllerVisuals[forceState || visualMapModel.getValueState(targetValue)];\n    var visualTypes = VisualMapping.prepareVisualTypes(mappings);\n    zrUtil.each(visualTypes, function (type) {\n      var visualMapping = mappings[type];\n      if (opts.convertOpacityToAlpha && type === 'opacity') {\n        type = 'colorAlpha';\n        visualMapping = mappings.__alphaForOpacity;\n      }\n      if (VisualMapping.dependsOn(type, visualCluster)) {\n        visualMapping && visualMapping.applyVisual(targetValue, getter, setter);\n      }\n    });\n    return visualObj[visualCluster];\n  };\n  VisualMapView.prototype.positionGroup = function (group) {\n    var model = this.visualMapModel;\n    var api = this.api;\n    layout.positionElement(group, model.getBoxLayoutParams(), {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n  };\n  VisualMapView.prototype.doRender = function (visualMapModel, ecModel, api, payload) {};\n  VisualMapView.type = 'visualMap';\n  return VisualMapView;\n}(ComponentView);\nexport default VisualMapView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "Rect", "formatUtil", "layout", "VisualMapping", "ComponentView", "VisualMapView", "_super", "_this", "apply", "arguments", "type", "autoPositionValues", "left", "right", "top", "bottom", "prototype", "init", "ecModel", "api", "render", "visualMapModel", "payload", "get", "group", "removeAll", "doR<PERSON>", "renderBackground", "padding", "normalizeCssArray", "rect", "getBoundingRect", "add", "z2", "silent", "shape", "x", "y", "width", "height", "style", "fill", "stroke", "lineWidth", "getControllerVisual", "targetValue", "visualCluster", "opts", "forceState", "visualObj", "defaultColor", "color", "getter", "key", "setter", "value", "mappings", "controllerVisuals", "getValueState", "visualTypes", "prepareVisualTypes", "each", "visualMapping", "convertOpacityToAlpha", "__alphaForOpacity", "dependsOn", "applyVisual", "positionGroup", "model", "positionElement", "getBoxLayoutParams", "getWidth", "getHeight"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/component/visualMap/VisualMapView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { Rect } from '../../util/graphic.js';\nimport * as formatUtil from '../../util/format.js';\nimport * as layout from '../../util/layout.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport ComponentView from '../../view/Component.js';\nvar VisualMapView = /** @class */function (_super) {\n  __extends(VisualMapView, _super);\n  function VisualMapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = VisualMapView.type;\n    _this.autoPositionValues = {\n      left: 1,\n      right: 1,\n      top: 1,\n      bottom: 1\n    };\n    return _this;\n  }\n  VisualMapView.prototype.init = function (ecModel, api) {\n    this.ecModel = ecModel;\n    this.api = api;\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapView.prototype.render = function (visualMapModel, ecModel, api, payload // TODO: TYPE\n  ) {\n    this.visualMapModel = visualMapModel;\n    if (visualMapModel.get('show') === false) {\n      this.group.removeAll();\n      return;\n    }\n    this.doRender(visualMapModel, ecModel, api, payload);\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapView.prototype.renderBackground = function (group) {\n    var visualMapModel = this.visualMapModel;\n    var padding = formatUtil.normalizeCssArray(visualMapModel.get('padding') || 0);\n    var rect = group.getBoundingRect();\n    group.add(new Rect({\n      z2: -1,\n      silent: true,\n      shape: {\n        x: rect.x - padding[3],\n        y: rect.y - padding[0],\n        width: rect.width + padding[3] + padding[1],\n        height: rect.height + padding[0] + padding[2]\n      },\n      style: {\n        fill: visualMapModel.get('backgroundColor'),\n        stroke: visualMapModel.get('borderColor'),\n        lineWidth: visualMapModel.get('borderWidth')\n      }\n    }));\n  };\n  /**\r\n   * @protected\r\n   * @param targetValue can be Infinity or -Infinity\r\n   * @param visualCluster Only can be 'color' 'opacity' 'symbol' 'symbolSize'\r\n   * @param opts\r\n   * @param opts.forceState Specify state, instead of using getValueState method.\r\n   * @param opts.convertOpacityToAlpha For color gradient in controller widget.\r\n   * @return {*} Visual value.\r\n   */\n  VisualMapView.prototype.getControllerVisual = function (targetValue, visualCluster, opts) {\n    opts = opts || {};\n    var forceState = opts.forceState;\n    var visualMapModel = this.visualMapModel;\n    var visualObj = {};\n    // Default values.\n    if (visualCluster === 'color') {\n      var defaultColor = visualMapModel.get('contentColor');\n      visualObj.color = defaultColor;\n    }\n    function getter(key) {\n      return visualObj[key];\n    }\n    function setter(key, value) {\n      visualObj[key] = value;\n    }\n    var mappings = visualMapModel.controllerVisuals[forceState || visualMapModel.getValueState(targetValue)];\n    var visualTypes = VisualMapping.prepareVisualTypes(mappings);\n    zrUtil.each(visualTypes, function (type) {\n      var visualMapping = mappings[type];\n      if (opts.convertOpacityToAlpha && type === 'opacity') {\n        type = 'colorAlpha';\n        visualMapping = mappings.__alphaForOpacity;\n      }\n      if (VisualMapping.dependsOn(type, visualCluster)) {\n        visualMapping && visualMapping.applyVisual(targetValue, getter, setter);\n      }\n    });\n    return visualObj[visualCluster];\n  };\n  VisualMapView.prototype.positionGroup = function (group) {\n    var model = this.visualMapModel;\n    var api = this.api;\n    layout.positionElement(group, model.getBoxLayoutParams(), {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n  };\n  VisualMapView.prototype.doRender = function (visualMapModel, ecModel, api, payload) {};\n  VisualMapView.type = 'visualMap';\n  return VisualMapView;\n}(ComponentView);\nexport default VisualMapView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAO,KAAKC,MAAM,MAAM,sBAAsB;AAC9C,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,IAAIC,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjDR,SAAS,CAACO,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,aAAa,CAACK,IAAI;IAC/BH,KAAK,CAACI,kBAAkB,GAAG;MACzBC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE;IACV,CAAC;IACD,OAAOR,KAAK;EACd;EACAF,aAAa,CAACW,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IACrD,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,GAAG,GAAGA,GAAG;EAChB,CAAC;EACD;AACF;AACA;EACEd,aAAa,CAACW,SAAS,CAACI,MAAM,GAAG,UAAUC,cAAc,EAAEH,OAAO,EAAEC,GAAG,EAAEG,OAAO,CAAC;EAAA,EAC/E;IACA,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAIA,cAAc,CAACE,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,EAAE;MACxC,IAAI,CAACC,KAAK,CAACC,SAAS,CAAC,CAAC;MACtB;IACF;IACA,IAAI,CAACC,QAAQ,CAACL,cAAc,EAAEH,OAAO,EAAEC,GAAG,EAAEG,OAAO,CAAC;EACtD,CAAC;EACD;AACF;AACA;EACEjB,aAAa,CAACW,SAAS,CAACW,gBAAgB,GAAG,UAAUH,KAAK,EAAE;IAC1D,IAAIH,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIO,OAAO,GAAG3B,UAAU,CAAC4B,iBAAiB,CAACR,cAAc,CAACE,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9E,IAAIO,IAAI,GAAGN,KAAK,CAACO,eAAe,CAAC,CAAC;IAClCP,KAAK,CAACQ,GAAG,CAAC,IAAIhC,IAAI,CAAC;MACjBiC,EAAE,EAAE,CAAC,CAAC;MACNC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;QACLC,CAAC,EAAEN,IAAI,CAACM,CAAC,GAAGR,OAAO,CAAC,CAAC,CAAC;QACtBS,CAAC,EAAEP,IAAI,CAACO,CAAC,GAAGT,OAAO,CAAC,CAAC,CAAC;QACtBU,KAAK,EAAER,IAAI,CAACQ,KAAK,GAAGV,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;QAC3CW,MAAM,EAAET,IAAI,CAACS,MAAM,GAAGX,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC;MAC9C,CAAC;MACDY,KAAK,EAAE;QACLC,IAAI,EAAEpB,cAAc,CAACE,GAAG,CAAC,iBAAiB,CAAC;QAC3CmB,MAAM,EAAErB,cAAc,CAACE,GAAG,CAAC,aAAa,CAAC;QACzCoB,SAAS,EAAEtB,cAAc,CAACE,GAAG,CAAC,aAAa;MAC7C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACElB,aAAa,CAACW,SAAS,CAAC4B,mBAAmB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,IAAI,EAAE;IACxFA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,IAAIC,UAAU,GAAGD,IAAI,CAACC,UAAU;IAChC,IAAI3B,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAI4B,SAAS,GAAG,CAAC,CAAC;IAClB;IACA,IAAIH,aAAa,KAAK,OAAO,EAAE;MAC7B,IAAII,YAAY,GAAG7B,cAAc,CAACE,GAAG,CAAC,cAAc,CAAC;MACrD0B,SAAS,CAACE,KAAK,GAAGD,YAAY;IAChC;IACA,SAASE,MAAMA,CAACC,GAAG,EAAE;MACnB,OAAOJ,SAAS,CAACI,GAAG,CAAC;IACvB;IACA,SAASC,MAAMA,CAACD,GAAG,EAAEE,KAAK,EAAE;MAC1BN,SAAS,CAACI,GAAG,CAAC,GAAGE,KAAK;IACxB;IACA,IAAIC,QAAQ,GAAGnC,cAAc,CAACoC,iBAAiB,CAACT,UAAU,IAAI3B,cAAc,CAACqC,aAAa,CAACb,WAAW,CAAC,CAAC;IACxG,IAAIc,WAAW,GAAGxD,aAAa,CAACyD,kBAAkB,CAACJ,QAAQ,CAAC;IAC5DzD,MAAM,CAAC8D,IAAI,CAACF,WAAW,EAAE,UAAUjD,IAAI,EAAE;MACvC,IAAIoD,aAAa,GAAGN,QAAQ,CAAC9C,IAAI,CAAC;MAClC,IAAIqC,IAAI,CAACgB,qBAAqB,IAAIrD,IAAI,KAAK,SAAS,EAAE;QACpDA,IAAI,GAAG,YAAY;QACnBoD,aAAa,GAAGN,QAAQ,CAACQ,iBAAiB;MAC5C;MACA,IAAI7D,aAAa,CAAC8D,SAAS,CAACvD,IAAI,EAAEoC,aAAa,CAAC,EAAE;QAChDgB,aAAa,IAAIA,aAAa,CAACI,WAAW,CAACrB,WAAW,EAAEO,MAAM,EAAEE,MAAM,CAAC;MACzE;IACF,CAAC,CAAC;IACF,OAAOL,SAAS,CAACH,aAAa,CAAC;EACjC,CAAC;EACDzC,aAAa,CAACW,SAAS,CAACmD,aAAa,GAAG,UAAU3C,KAAK,EAAE;IACvD,IAAI4C,KAAK,GAAG,IAAI,CAAC/C,cAAc;IAC/B,IAAIF,GAAG,GAAG,IAAI,CAACA,GAAG;IAClBjB,MAAM,CAACmE,eAAe,CAAC7C,KAAK,EAAE4C,KAAK,CAACE,kBAAkB,CAAC,CAAC,EAAE;MACxDhC,KAAK,EAAEnB,GAAG,CAACoD,QAAQ,CAAC,CAAC;MACrBhC,MAAM,EAAEpB,GAAG,CAACqD,SAAS,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EACDnE,aAAa,CAACW,SAAS,CAACU,QAAQ,GAAG,UAAUL,cAAc,EAAEH,OAAO,EAAEC,GAAG,EAAEG,OAAO,EAAE,CAAC,CAAC;EACtFjB,aAAa,CAACK,IAAI,GAAG,WAAW;EAChC,OAAOL,aAAa;AACtB,CAAC,CAACD,aAAa,CAAC;AAChB,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}