{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { eachAfter, eachBefore } from './traversalHelper.js';\nimport { init, firstWalk, secondWalk, separation as sep, radialCoordinate, getViewRect } from './layoutHelper.js';\nexport default function treeLayout(ecModel, api) {\n  ecModel.eachSeriesByType('tree', function (seriesModel) {\n    commonLayout(seriesModel, api);\n  });\n}\nfunction commonLayout(seriesModel, api) {\n  var layoutInfo = getViewRect(seriesModel, api);\n  seriesModel.layoutInfo = layoutInfo;\n  var layout = seriesModel.get('layout');\n  var width = 0;\n  var height = 0;\n  var separation = null;\n  if (layout === 'radial') {\n    width = 2 * Math.PI;\n    height = Math.min(layoutInfo.height, layoutInfo.width) / 2;\n    separation = sep(function (node1, node2) {\n      return (node1.parentNode === node2.parentNode ? 1 : 2) / node1.depth;\n    });\n  } else {\n    width = layoutInfo.width;\n    height = layoutInfo.height;\n    separation = sep();\n  }\n  var virtualRoot = seriesModel.getData().tree.root;\n  var realRoot = virtualRoot.children[0];\n  if (realRoot) {\n    init(virtualRoot);\n    eachAfter(realRoot, firstWalk, separation);\n    virtualRoot.hierNode.modifier = -realRoot.hierNode.prelim;\n    eachBefore(realRoot, secondWalk);\n    var left_1 = realRoot;\n    var right_1 = realRoot;\n    var bottom_1 = realRoot;\n    eachBefore(realRoot, function (node) {\n      var x = node.getLayout().x;\n      if (x < left_1.getLayout().x) {\n        left_1 = node;\n      }\n      if (x > right_1.getLayout().x) {\n        right_1 = node;\n      }\n      if (node.depth > bottom_1.depth) {\n        bottom_1 = node;\n      }\n    });\n    var delta = left_1 === right_1 ? 1 : separation(left_1, right_1) / 2;\n    var tx_1 = delta - left_1.getLayout().x;\n    var kx_1 = 0;\n    var ky_1 = 0;\n    var coorX_1 = 0;\n    var coorY_1 = 0;\n    if (layout === 'radial') {\n      kx_1 = width / (right_1.getLayout().x + delta + tx_1);\n      // here we use (node.depth - 1), bucause the real root's depth is 1\n      ky_1 = height / (bottom_1.depth - 1 || 1);\n      eachBefore(realRoot, function (node) {\n        coorX_1 = (node.getLayout().x + tx_1) * kx_1;\n        coorY_1 = (node.depth - 1) * ky_1;\n        var finalCoor = radialCoordinate(coorX_1, coorY_1);\n        node.setLayout({\n          x: finalCoor.x,\n          y: finalCoor.y,\n          rawX: coorX_1,\n          rawY: coorY_1\n        }, true);\n      });\n    } else {\n      var orient_1 = seriesModel.getOrient();\n      if (orient_1 === 'RL' || orient_1 === 'LR') {\n        ky_1 = height / (right_1.getLayout().x + delta + tx_1);\n        kx_1 = width / (bottom_1.depth - 1 || 1);\n        eachBefore(realRoot, function (node) {\n          coorY_1 = (node.getLayout().x + tx_1) * ky_1;\n          coorX_1 = orient_1 === 'LR' ? (node.depth - 1) * kx_1 : width - (node.depth - 1) * kx_1;\n          node.setLayout({\n            x: coorX_1,\n            y: coorY_1\n          }, true);\n        });\n      } else if (orient_1 === 'TB' || orient_1 === 'BT') {\n        kx_1 = width / (right_1.getLayout().x + delta + tx_1);\n        ky_1 = height / (bottom_1.depth - 1 || 1);\n        eachBefore(realRoot, function (node) {\n          coorX_1 = (node.getLayout().x + tx_1) * kx_1;\n          coorY_1 = orient_1 === 'TB' ? (node.depth - 1) * ky_1 : height - (node.depth - 1) * ky_1;\n          node.setLayout({\n            x: coorX_1,\n            y: coorY_1\n          }, true);\n        });\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["eachAfter", "eachBefore", "init", "firstWalk", "secondWalk", "separation", "sep", "radialCoordinate", "getViewRect", "treeLayout", "ecModel", "api", "eachSeriesByType", "seriesModel", "commonLayout", "layoutInfo", "layout", "get", "width", "height", "Math", "PI", "min", "node1", "node2", "parentNode", "depth", "virtualRoot", "getData", "tree", "root", "realRoot", "children", "hier<PERSON><PERSON>", "modifier", "prelim", "left_1", "right_1", "bottom_1", "node", "x", "getLayout", "delta", "tx_1", "kx_1", "ky_1", "coorX_1", "coorY_1", "finalCoor", "setLayout", "y", "rawX", "rawY", "orient_1", "getOrient"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/tree/treeLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { eachAfter, eachBefore } from './traversalHelper.js';\nimport { init, firstWalk, secondWalk, separation as sep, radialCoordinate, getViewRect } from './layoutHelper.js';\nexport default function treeLayout(ecModel, api) {\n  ecModel.eachSeriesByType('tree', function (seriesModel) {\n    commonLayout(seriesModel, api);\n  });\n}\nfunction commonLayout(seriesModel, api) {\n  var layoutInfo = getViewRect(seriesModel, api);\n  seriesModel.layoutInfo = layoutInfo;\n  var layout = seriesModel.get('layout');\n  var width = 0;\n  var height = 0;\n  var separation = null;\n  if (layout === 'radial') {\n    width = 2 * Math.PI;\n    height = Math.min(layoutInfo.height, layoutInfo.width) / 2;\n    separation = sep(function (node1, node2) {\n      return (node1.parentNode === node2.parentNode ? 1 : 2) / node1.depth;\n    });\n  } else {\n    width = layoutInfo.width;\n    height = layoutInfo.height;\n    separation = sep();\n  }\n  var virtualRoot = seriesModel.getData().tree.root;\n  var realRoot = virtualRoot.children[0];\n  if (realRoot) {\n    init(virtualRoot);\n    eachAfter(realRoot, firstWalk, separation);\n    virtualRoot.hierNode.modifier = -realRoot.hierNode.prelim;\n    eachBefore(realRoot, secondWalk);\n    var left_1 = realRoot;\n    var right_1 = realRoot;\n    var bottom_1 = realRoot;\n    eachBefore(realRoot, function (node) {\n      var x = node.getLayout().x;\n      if (x < left_1.getLayout().x) {\n        left_1 = node;\n      }\n      if (x > right_1.getLayout().x) {\n        right_1 = node;\n      }\n      if (node.depth > bottom_1.depth) {\n        bottom_1 = node;\n      }\n    });\n    var delta = left_1 === right_1 ? 1 : separation(left_1, right_1) / 2;\n    var tx_1 = delta - left_1.getLayout().x;\n    var kx_1 = 0;\n    var ky_1 = 0;\n    var coorX_1 = 0;\n    var coorY_1 = 0;\n    if (layout === 'radial') {\n      kx_1 = width / (right_1.getLayout().x + delta + tx_1);\n      // here we use (node.depth - 1), bucause the real root's depth is 1\n      ky_1 = height / (bottom_1.depth - 1 || 1);\n      eachBefore(realRoot, function (node) {\n        coorX_1 = (node.getLayout().x + tx_1) * kx_1;\n        coorY_1 = (node.depth - 1) * ky_1;\n        var finalCoor = radialCoordinate(coorX_1, coorY_1);\n        node.setLayout({\n          x: finalCoor.x,\n          y: finalCoor.y,\n          rawX: coorX_1,\n          rawY: coorY_1\n        }, true);\n      });\n    } else {\n      var orient_1 = seriesModel.getOrient();\n      if (orient_1 === 'RL' || orient_1 === 'LR') {\n        ky_1 = height / (right_1.getLayout().x + delta + tx_1);\n        kx_1 = width / (bottom_1.depth - 1 || 1);\n        eachBefore(realRoot, function (node) {\n          coorY_1 = (node.getLayout().x + tx_1) * ky_1;\n          coorX_1 = orient_1 === 'LR' ? (node.depth - 1) * kx_1 : width - (node.depth - 1) * kx_1;\n          node.setLayout({\n            x: coorX_1,\n            y: coorY_1\n          }, true);\n        });\n      } else if (orient_1 === 'TB' || orient_1 === 'BT') {\n        kx_1 = width / (right_1.getLayout().x + delta + tx_1);\n        ky_1 = height / (bottom_1.depth - 1 || 1);\n        eachBefore(realRoot, function (node) {\n          coorX_1 = (node.getLayout().x + tx_1) * kx_1;\n          coorY_1 = orient_1 === 'TB' ? (node.depth - 1) * ky_1 : height - (node.depth - 1) * ky_1;\n          node.setLayout({\n            x: coorX_1,\n            y: coorY_1\n          }, true);\n        });\n      }\n    }\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,UAAU,QAAQ,sBAAsB;AAC5D,SAASC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAEC,UAAU,IAAIC,GAAG,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,mBAAmB;AACjH,eAAe,SAASC,UAAUA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAC/CD,OAAO,CAACE,gBAAgB,CAAC,MAAM,EAAE,UAAUC,WAAW,EAAE;IACtDC,YAAY,CAACD,WAAW,EAAEF,GAAG,CAAC;EAChC,CAAC,CAAC;AACJ;AACA,SAASG,YAAYA,CAACD,WAAW,EAAEF,GAAG,EAAE;EACtC,IAAII,UAAU,GAAGP,WAAW,CAACK,WAAW,EAAEF,GAAG,CAAC;EAC9CE,WAAW,CAACE,UAAU,GAAGA,UAAU;EACnC,IAAIC,MAAM,GAAGH,WAAW,CAACI,GAAG,CAAC,QAAQ,CAAC;EACtC,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,MAAM,GAAG,CAAC;EACd,IAAId,UAAU,GAAG,IAAI;EACrB,IAAIW,MAAM,KAAK,QAAQ,EAAE;IACvBE,KAAK,GAAG,CAAC,GAAGE,IAAI,CAACC,EAAE;IACnBF,MAAM,GAAGC,IAAI,CAACE,GAAG,CAACP,UAAU,CAACI,MAAM,EAAEJ,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC;IAC1Db,UAAU,GAAGC,GAAG,CAAC,UAAUiB,KAAK,EAAEC,KAAK,EAAE;MACvC,OAAO,CAACD,KAAK,CAACE,UAAU,KAAKD,KAAK,CAACC,UAAU,GAAG,CAAC,GAAG,CAAC,IAAIF,KAAK,CAACG,KAAK;IACtE,CAAC,CAAC;EACJ,CAAC,MAAM;IACLR,KAAK,GAAGH,UAAU,CAACG,KAAK;IACxBC,MAAM,GAAGJ,UAAU,CAACI,MAAM;IAC1Bd,UAAU,GAAGC,GAAG,CAAC,CAAC;EACpB;EACA,IAAIqB,WAAW,GAAGd,WAAW,CAACe,OAAO,CAAC,CAAC,CAACC,IAAI,CAACC,IAAI;EACjD,IAAIC,QAAQ,GAAGJ,WAAW,CAACK,QAAQ,CAAC,CAAC,CAAC;EACtC,IAAID,QAAQ,EAAE;IACZ7B,IAAI,CAACyB,WAAW,CAAC;IACjB3B,SAAS,CAAC+B,QAAQ,EAAE5B,SAAS,EAAEE,UAAU,CAAC;IAC1CsB,WAAW,CAACM,QAAQ,CAACC,QAAQ,GAAG,CAACH,QAAQ,CAACE,QAAQ,CAACE,MAAM;IACzDlC,UAAU,CAAC8B,QAAQ,EAAE3B,UAAU,CAAC;IAChC,IAAIgC,MAAM,GAAGL,QAAQ;IACrB,IAAIM,OAAO,GAAGN,QAAQ;IACtB,IAAIO,QAAQ,GAAGP,QAAQ;IACvB9B,UAAU,CAAC8B,QAAQ,EAAE,UAAUQ,IAAI,EAAE;MACnC,IAAIC,CAAC,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC,CAACD,CAAC;MAC1B,IAAIA,CAAC,GAAGJ,MAAM,CAACK,SAAS,CAAC,CAAC,CAACD,CAAC,EAAE;QAC5BJ,MAAM,GAAGG,IAAI;MACf;MACA,IAAIC,CAAC,GAAGH,OAAO,CAACI,SAAS,CAAC,CAAC,CAACD,CAAC,EAAE;QAC7BH,OAAO,GAAGE,IAAI;MAChB;MACA,IAAIA,IAAI,CAACb,KAAK,GAAGY,QAAQ,CAACZ,KAAK,EAAE;QAC/BY,QAAQ,GAAGC,IAAI;MACjB;IACF,CAAC,CAAC;IACF,IAAIG,KAAK,GAAGN,MAAM,KAAKC,OAAO,GAAG,CAAC,GAAGhC,UAAU,CAAC+B,MAAM,EAAEC,OAAO,CAAC,GAAG,CAAC;IACpE,IAAIM,IAAI,GAAGD,KAAK,GAAGN,MAAM,CAACK,SAAS,CAAC,CAAC,CAACD,CAAC;IACvC,IAAII,IAAI,GAAG,CAAC;IACZ,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,OAAO,GAAG,CAAC;IACf,IAAI/B,MAAM,KAAK,QAAQ,EAAE;MACvB4B,IAAI,GAAG1B,KAAK,IAAImB,OAAO,CAACI,SAAS,CAAC,CAAC,CAACD,CAAC,GAAGE,KAAK,GAAGC,IAAI,CAAC;MACrD;MACAE,IAAI,GAAG1B,MAAM,IAAImB,QAAQ,CAACZ,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;MACzCzB,UAAU,CAAC8B,QAAQ,EAAE,UAAUQ,IAAI,EAAE;QACnCO,OAAO,GAAG,CAACP,IAAI,CAACE,SAAS,CAAC,CAAC,CAACD,CAAC,GAAGG,IAAI,IAAIC,IAAI;QAC5CG,OAAO,GAAG,CAACR,IAAI,CAACb,KAAK,GAAG,CAAC,IAAImB,IAAI;QACjC,IAAIG,SAAS,GAAGzC,gBAAgB,CAACuC,OAAO,EAAEC,OAAO,CAAC;QAClDR,IAAI,CAACU,SAAS,CAAC;UACbT,CAAC,EAAEQ,SAAS,CAACR,CAAC;UACdU,CAAC,EAAEF,SAAS,CAACE,CAAC;UACdC,IAAI,EAAEL,OAAO;UACbM,IAAI,EAAEL;QACR,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIM,QAAQ,GAAGxC,WAAW,CAACyC,SAAS,CAAC,CAAC;MACtC,IAAID,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,IAAI,EAAE;QAC1CR,IAAI,GAAG1B,MAAM,IAAIkB,OAAO,CAACI,SAAS,CAAC,CAAC,CAACD,CAAC,GAAGE,KAAK,GAAGC,IAAI,CAAC;QACtDC,IAAI,GAAG1B,KAAK,IAAIoB,QAAQ,CAACZ,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;QACxCzB,UAAU,CAAC8B,QAAQ,EAAE,UAAUQ,IAAI,EAAE;UACnCQ,OAAO,GAAG,CAACR,IAAI,CAACE,SAAS,CAAC,CAAC,CAACD,CAAC,GAAGG,IAAI,IAAIE,IAAI;UAC5CC,OAAO,GAAGO,QAAQ,KAAK,IAAI,GAAG,CAACd,IAAI,CAACb,KAAK,GAAG,CAAC,IAAIkB,IAAI,GAAG1B,KAAK,GAAG,CAACqB,IAAI,CAACb,KAAK,GAAG,CAAC,IAAIkB,IAAI;UACvFL,IAAI,CAACU,SAAS,CAAC;YACbT,CAAC,EAAEM,OAAO;YACVI,CAAC,EAAEH;UACL,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIM,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,IAAI,EAAE;QACjDT,IAAI,GAAG1B,KAAK,IAAImB,OAAO,CAACI,SAAS,CAAC,CAAC,CAACD,CAAC,GAAGE,KAAK,GAAGC,IAAI,CAAC;QACrDE,IAAI,GAAG1B,MAAM,IAAImB,QAAQ,CAACZ,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;QACzCzB,UAAU,CAAC8B,QAAQ,EAAE,UAAUQ,IAAI,EAAE;UACnCO,OAAO,GAAG,CAACP,IAAI,CAACE,SAAS,CAAC,CAAC,CAACD,CAAC,GAAGG,IAAI,IAAIC,IAAI;UAC5CG,OAAO,GAAGM,QAAQ,KAAK,IAAI,GAAG,CAACd,IAAI,CAACb,KAAK,GAAG,CAAC,IAAImB,IAAI,GAAG1B,MAAM,GAAG,CAACoB,IAAI,CAACb,KAAK,GAAG,CAAC,IAAImB,IAAI;UACxFN,IAAI,CAACU,SAAS,CAAC;YACbT,CAAC,EAAEM,OAAO;YACVI,CAAC,EAAEH;UACL,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,CAAC;MACJ;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}