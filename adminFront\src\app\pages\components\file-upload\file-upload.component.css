.file-upload-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.file-upload-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.file-upload-label-container {
  display: flex;
  flex-direction: column;
  margin-right: 12px;
}

.file-upload-label {
  min-width: 172px;
  color: #333;
  font-weight: 500;
}

.required-field::after {
  content: " *";
  color: red;
}

.file-upload-help-text {
  color: red;
  font-size: 14px;
  margin-top: 4px;
}

.file-upload-input-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0;
  align-items: flex-start;
}

.file-input-hidden {
  display: none;
}

.file-upload-button {
  background-color: #3b82f6;
  color: white;
  font-weight: bold;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  display: inline-flex;
  align-items: center;
}

.file-upload-button:hover:not(.disabled) {
  background-color: #1d4ed8;
}

.file-upload-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.file-display-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.file-name {
  color: #6b7280;
  font-size: 14px;
}

.file-delete-button {
  color: #ef4444;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  transition: color 0.2s;
}

.file-delete-button:hover {
  color: #dc2626;
}

.current-file-link {
  color: #3b82f6;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
}

.current-file-link:hover {
  color: #1d4ed8;
}

.hidden {
  display: none;
}

.upload-icon {
  margin-right: 8px;
}

.delete-icon {
  font-size: 14px;
}