{"ast": null, "code": "import { DEFAULT_THEME as baseTheme } from '@nebular/theme';\nconst baseThemeVariables = baseTheme.variables;\n// 金色主題色彩常數\nconst GOLD_PRIMARY = '#B8A676';\nconst GOLD_LIGHT = '#C4B382';\nconst GOLD_DARK = '#A69660';\nexport const DEFAULT_THEME = {\n  name: 'default',\n  base: 'default',\n  variables: {\n    // 覆蓋主要色彩為金色系統\n    primary: GOLD_PRIMARY,\n    primaryLight: GOLD_LIGHT,\n    primaryDark: GOLD_DARK,\n    temperature: {\n      arcFill: [GOLD_PRIMARY, GOLD_PRIMARY, GOLD_PRIMARY, GOLD_PRIMARY, GOLD_PRIMARY],\n      arcEmpty: baseThemeVariables['bg2'],\n      thumbBg: baseThemeVariables['bg2'],\n      thumbBorder: GOLD_PRIMARY\n    },\n    solar: {\n      gradientLeft: baseThemeVariables['primary'],\n      gradientRight: baseThemeVariables['primary'],\n      shadowColor: 'rgba(0, 0, 0, 0)',\n      secondSeriesFill: baseThemeVariables['bg2'],\n      radius: ['80%', '90%']\n    },\n    traffic: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      yAxisSplitLine: baseThemeVariables['separator'],\n      lineBg: baseThemeVariables['border4'],\n      lineShadowBlur: '1',\n      itemColor: baseThemeVariables['border4'],\n      itemBorderColor: baseThemeVariables['border4'],\n      itemEmphasisBorderColor: baseThemeVariables['primary'],\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      shadowLineShadow: 'rgba(0, 0, 0, 0)',\n      gradFrom: baseThemeVariables['bg2'],\n      gradTo: baseThemeVariables['bg2']\n    },\n    electricity: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipLineColor: baseThemeVariables['fgText'],\n      tooltipLineWidth: '0',\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      axisLineColor: baseThemeVariables['border3'],\n      xAxisTextColor: baseThemeVariables['fg'],\n      yAxisSplitLine: baseThemeVariables['separator'],\n      itemBorderColor: baseThemeVariables['primary'],\n      lineStyle: 'solid',\n      lineWidth: '4',\n      lineGradFrom: baseThemeVariables['primary'],\n      lineGradTo: baseThemeVariables['primary'],\n      lineShadow: 'rgba(0, 0, 0, 0)',\n      areaGradFrom: baseThemeVariables['bg2'],\n      areaGradTo: baseThemeVariables['bg2'],\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)'\n    },\n    bubbleMap: {\n      titleColor: baseThemeVariables['fgText'],\n      areaColor: baseThemeVariables['bg4'],\n      areaHoverColor: baseThemeVariables['fgHighlight'],\n      areaBorderColor: baseThemeVariables['border5']\n    },\n    profitBarAnimationEchart: {\n      textColor: baseThemeVariables['fgText'],\n      firstAnimationBarColor: baseThemeVariables['primary'],\n      secondAnimationBarColor: baseThemeVariables['success'],\n      splitLineStyleOpacity: '1',\n      splitLineStyleWidth: '1',\n      splitLineStyleColor: baseThemeVariables['separator'],\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '16',\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipBorderWidth: '1',\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;'\n    },\n    trafficBarEchart: {\n      gradientFrom: baseThemeVariables['warningLight'],\n      gradientTo: baseThemeVariables['warning'],\n      shadow: baseThemeVariables['warningLight'],\n      shadowBlur: '0',\n      axisTextColor: baseThemeVariables['fgText'],\n      axisFontSize: '12',\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal'\n    },\n    countryOrders: {\n      countryBorderColor: baseThemeVariables['border4'],\n      countryFillColor: baseThemeVariables['bg3'],\n      countryBorderWidth: '1',\n      hoveredCountryBorderColor: baseThemeVariables['primary'],\n      hoveredCountryFillColor: baseThemeVariables['primaryLight'],\n      hoveredCountryBorderWidth: '1',\n      chartAxisLineColor: baseThemeVariables['border4'],\n      chartAxisTextColor: baseThemeVariables['fg'],\n      chartAxisFontSize: '16',\n      chartGradientTo: baseThemeVariables['primary'],\n      chartGradientFrom: baseThemeVariables['primaryLight'],\n      chartAxisSplitLine: baseThemeVariables['separator'],\n      chartShadowLineColor: baseThemeVariables['primaryLight'],\n      chartLineBottomShadowColor: baseThemeVariables['primary'],\n      chartInnerLineColor: baseThemeVariables['bg2']\n    },\n    echarts: {\n      bg: baseThemeVariables['bg'],\n      textColor: baseThemeVariables['fgText'],\n      axisLineColor: baseThemeVariables['fgText'],\n      splitLineColor: baseThemeVariables['separator'],\n      itemHoverShadowColor: 'rgba(0, 0, 0, 0.5)',\n      tooltipBackgroundColor: baseThemeVariables['primary'],\n      areaOpacity: '0.7'\n    },\n    chartjs: {\n      axisLineColor: baseThemeVariables['separator'],\n      textColor: baseThemeVariables['fgText']\n    },\n    orders: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\n      tooltipLineWidth: '0',\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '20',\n      axisLineColor: baseThemeVariables['border4'],\n      axisFontSize: '16',\n      axisTextColor: baseThemeVariables['fg'],\n      yAxisSplitLine: baseThemeVariables['separator'],\n      itemBorderColor: baseThemeVariables['primary'],\n      lineStyle: 'solid',\n      lineWidth: '4',\n      // first line\n      firstAreaGradFrom: baseThemeVariables['bg3'],\n      firstAreaGradTo: baseThemeVariables['bg3'],\n      firstShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      // second line\n      secondLineGradFrom: baseThemeVariables['primary'],\n      secondLineGradTo: baseThemeVariables['primary'],\n      secondAreaGradFrom: 'rgba(51, 102, 255, 0.2)',\n      secondAreaGradTo: 'rgba(51, 102, 255, 0)',\n      secondShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      // third line\n      thirdLineGradFrom: baseThemeVariables['success'],\n      thirdLineGradTo: baseThemeVariables['successLight'],\n      thirdAreaGradFrom: 'rgba(0, 214, 143, 0.2)',\n      thirdAreaGradTo: 'rgba(0, 214, 143, 0)',\n      thirdShadowLineDarkBg: 'rgba(0, 0, 0, 0)'\n    },\n    profit: {\n      bg: baseThemeVariables['bg'],\n      textColor: baseThemeVariables['fgText'],\n      axisLineColor: baseThemeVariables['border4'],\n      splitLineColor: baseThemeVariables['separator'],\n      areaOpacity: '1',\n      axisFontSize: '16',\n      axisTextColor: baseThemeVariables['fg'],\n      // first bar\n      firstLineGradFrom: baseThemeVariables['bg3'],\n      firstLineGradTo: baseThemeVariables['bg3'],\n      firstLineShadow: 'rgba(0, 0, 0, 0)',\n      // second bar\n      secondLineGradFrom: baseThemeVariables['primary'],\n      secondLineGradTo: baseThemeVariables['primary'],\n      secondLineShadow: 'rgba(0, 0, 0, 0)',\n      // third bar\n      thirdLineGradFrom: baseThemeVariables['success'],\n      thirdLineGradTo: baseThemeVariables['successLight'],\n      thirdLineShadow: 'rgba(0, 0, 0, 0)'\n    },\n    orderProfitLegend: {\n      firstItem: baseThemeVariables['success'],\n      secondItem: baseThemeVariables['primary'],\n      thirdItem: baseThemeVariables['bg3']\n    },\n    visitors: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\n      tooltipLineWidth: '1',\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '20',\n      axisLineColor: baseThemeVariables['border4'],\n      axisFontSize: '16',\n      axisTextColor: baseThemeVariables['fg'],\n      yAxisSplitLine: baseThemeVariables['separator'],\n      itemBorderColor: baseThemeVariables['primary'],\n      lineStyle: 'dotted',\n      lineWidth: '6',\n      lineGradFrom: '#ffffff',\n      lineGradTo: '#ffffff',\n      lineShadow: 'rgba(0, 0, 0, 0)',\n      areaGradFrom: baseThemeVariables['primary'],\n      areaGradTo: baseThemeVariables['primaryLight'],\n      innerLineStyle: 'solid',\n      innerLineWidth: '1',\n      innerAreaGradFrom: baseThemeVariables['success'],\n      innerAreaGradTo: baseThemeVariables['success']\n    },\n    visitorsLegend: {\n      firstIcon: baseThemeVariables['success'],\n      secondIcon: baseThemeVariables['primary']\n    },\n    visitorsPie: {\n      firstPieGradientLeft: baseThemeVariables['success'],\n      firstPieGradientRight: baseThemeVariables['success'],\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\n      firstPieRadius: ['70%', '90%'],\n      secondPieGradientLeft: baseThemeVariables['warning'],\n      secondPieGradientRight: baseThemeVariables['warningLight'],\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\n      secondPieRadius: ['60%', '97%'],\n      shadowOffsetX: '0',\n      shadowOffsetY: '0'\n    },\n    visitorsPieLegend: {\n      firstSection: baseThemeVariables['warning'],\n      secondSection: baseThemeVariables['success']\n    },\n    earningPie: {\n      radius: ['65%', '100%'],\n      center: ['50%', '50%'],\n      fontSize: '22',\n      firstPieGradientLeft: baseThemeVariables['success'],\n      firstPieGradientRight: baseThemeVariables['success'],\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\n      secondPieGradientLeft: baseThemeVariables['primary'],\n      secondPieGradientRight: baseThemeVariables['primary'],\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\n      thirdPieGradientLeft: baseThemeVariables['warning'],\n      thirdPieGradientRight: baseThemeVariables['warning'],\n      thirdPieShadowColor: 'rgba(0, 0, 0, 0)'\n    },\n    earningLine: {\n      gradFrom: baseThemeVariables['primary'],\n      gradTo: baseThemeVariables['primary'],\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '16',\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipBorderWidth: '1',\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;'\n    }\n  }\n};", "map": {"version": 3, "names": ["DEFAULT_THEME", "baseTheme", "baseThemeVariables", "variables", "GOLD_PRIMARY", "GOLD_LIGHT", "GOLD_DARK", "name", "base", "primary", "primaryLight", "primaryDark", "temperature", "arcFill", "arcEmpty", "thumbBg", "thumbBorder", "solar", "gradientLeft", "gradientRight", "shadowColor", "secondSeriesFill", "radius", "traffic", "tooltipBg", "tooltipBorderColor", "tooltipExtraCss", "tooltipTextColor", "tooltipFontWeight", "yAxisSplitLine", "lineBg", "lineShadowBlur", "itemColor", "itemBorderColor", "itemEmphasisBorderColor", "shadowLineDarkBg", "shadowLineShadow", "gradFrom", "gradTo", "electricity", "tooltipLineColor", "tooltipLineWidth", "axisLineColor", "xAxisTextColor", "lineStyle", "lineWidth", "lineGradFrom", "lineGradTo", "lineShadow", "areaGradFrom", "areaGradTo", "bubbleMap", "titleColor", "areaColor", "areaHoverColor", "areaBorderColor", "profitBarAnimationEchart", "textColor", "firstAnimationBarColor", "secondAnimationBarColor", "splitLineStyleOpacity", "splitLineStyleWidth", "splitLineStyleColor", "tooltipFontSize", "tooltipBorderWidth", "trafficBarEchart", "gradientFrom", "gradientTo", "shadow", "<PERSON><PERSON><PERSON><PERSON>", "axisTextColor", "axisFontSize", "countryOrders", "countryBorderColor", "countryFillColor", "countryBorderWidth", "hoveredCountryBorderColor", "hoveredCountryFillColor", "hoveredCountryBorderWidth", "chartAxisLineColor", "chartAxisTextColor", "chartAxisFontSize", "chartGradientTo", "chartGradientFrom", "chartAxisSplitLine", "chartShadowLineColor", "chartLineBottomShadowColor", "chartInnerLineColor", "echarts", "bg", "splitLineColor", "itemHoverShadowColor", "tooltipBackgroundColor", "areaOpacity", "chartjs", "orders", "firstAreaGradFrom", "firstAreaGradTo", "firstShadowLineDarkBg", "secondLineGradFrom", "secondLineGradTo", "secondAreaGradFrom", "secondAreaGradTo", "secondShadowLineDarkBg", "thirdLineGradFrom", "thirdLineGradTo", "thirdAreaGradFrom", "thirdAreaGradTo", "thirdShadowLineDarkBg", "profit", "firstLineGradFrom", "firstLineGradTo", "firstLineShadow", "secondLineShadow", "thirdLineShadow", "orderProfitLegend", "firstItem", "secondItem", "thirdItem", "visitors", "innerLineStyle", "innerLineWidth", "innerAreaGradFrom", "innerAreaGradTo", "visitorsLegend", "firstIcon", "secondIcon", "visitors<PERSON>ie", "firstPieGradientLeft", "firstPieGradientRight", "firstPieShadowColor", "firstPieRadius", "secondPieGradientLeft", "secondPieGradientRight", "secondPieShadowColor", "secondPieRadius", "shadowOffsetX", "shadowOffsetY", "visitorsPieLegend", "firstSection", "secondSection", "earning<PERSON>ie", "center", "fontSize", "thirdPieGradientLeft", "thirdPieGradientRight", "thirdPieShadowColor", "earningLine"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\styles\\theme.default.ts"], "sourcesContent": ["import { NbJSThemeOptions, DEFAULT_THEME as baseTheme } from '@nebular/theme';\r\n\r\nconst baseThemeVariables = baseTheme.variables!;\r\n\r\n// 金色主題色彩常數\r\nconst GOLD_PRIMARY = '#B8A676';\r\nconst GOLD_LIGHT = '#C4B382';\r\nconst GOLD_DARK = '#A69660';\r\n\r\nexport const DEFAULT_THEME = {\r\n  name: 'default',\r\n  base: 'default',\r\n  variables: {\r\n    // 覆蓋主要色彩為金色系統\r\n    primary: GOLD_PRIMARY,\r\n    primaryLight: GOLD_LIGHT,\r\n    primaryDark: GOLD_DARK,\r\n\r\n    temperature: {\r\n      arcFill: [\r\n        GOLD_PRIMARY,\r\n        GOLD_PRIMARY,\r\n        GOLD_PRIMARY,\r\n        GOLD_PRIMARY,\r\n        GOLD_PRIMARY,\r\n      ],\r\n      arcEmpty: baseThemeVariables['bg2'],\r\n      thumbBg: baseThemeVariables['bg2'],\r\n      thumbBorder: GOLD_PRIMARY,\r\n    },\r\n\r\n    solar: {\r\n      gradientLeft: baseThemeVariables['primary'],\r\n      gradientRight: baseThemeVariables['primary'],\r\n      shadowColor: 'rgba(0, 0, 0, 0)',\r\n      secondSeriesFill: baseThemeVariables['bg2'],\r\n      radius: ['80%', '90%'],\r\n    },\r\n\r\n    traffic: {\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n\r\n      yAxisSplitLine: baseThemeVariables['separator'],\r\n\r\n      lineBg: baseThemeVariables['border4'],\r\n      lineShadowBlur: '1',\r\n      itemColor: baseThemeVariables['border4'],\r\n      itemBorderColor: baseThemeVariables['border4'],\r\n      itemEmphasisBorderColor: baseThemeVariables['primary'],\r\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n      shadowLineShadow: 'rgba(0, 0, 0, 0)',\r\n      gradFrom: baseThemeVariables['bg2'],\r\n      gradTo: baseThemeVariables['bg2'],\r\n    },\r\n\r\n    electricity: {\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipLineColor: baseThemeVariables['fgText'],\r\n      tooltipLineWidth: '0',\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n\r\n      axisLineColor: baseThemeVariables['border3'],\r\n      xAxisTextColor: baseThemeVariables['fg'],\r\n      yAxisSplitLine: baseThemeVariables['separator'],\r\n\r\n      itemBorderColor: baseThemeVariables['primary'],\r\n      lineStyle: 'solid',\r\n      lineWidth: '4',\r\n      lineGradFrom: baseThemeVariables['primary'],\r\n      lineGradTo: baseThemeVariables['primary'],\r\n      lineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n      areaGradFrom: baseThemeVariables['bg2'],\r\n      areaGradTo: baseThemeVariables['bg2'],\r\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n    },\r\n\r\n    bubbleMap: {\r\n      titleColor: baseThemeVariables['fgText'],\r\n      areaColor: baseThemeVariables['bg4'],\r\n      areaHoverColor: baseThemeVariables['fgHighlight'],\r\n      areaBorderColor: baseThemeVariables['border5'],\r\n    },\r\n\r\n    profitBarAnimationEchart: {\r\n      textColor: baseThemeVariables['fgText'],\r\n\r\n      firstAnimationBarColor: baseThemeVariables['primary'],\r\n      secondAnimationBarColor: baseThemeVariables['success'],\r\n\r\n      splitLineStyleOpacity: '1',\r\n      splitLineStyleWidth: '1',\r\n      splitLineStyleColor: baseThemeVariables['separator'],\r\n\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n      tooltipFontSize: '16',\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipBorderWidth: '1',\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\r\n    },\r\n\r\n    trafficBarEchart: {\r\n      gradientFrom: baseThemeVariables['warningLight'],\r\n      gradientTo: baseThemeVariables['warning'],\r\n      shadow: baseThemeVariables['warningLight'],\r\n      shadowBlur: '0',\r\n\r\n      axisTextColor: baseThemeVariables['fgText'],\r\n      axisFontSize: '12',\r\n\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n    },\r\n\r\n    countryOrders: {\r\n      countryBorderColor: baseThemeVariables['border4'],\r\n      countryFillColor: baseThemeVariables['bg3'],\r\n      countryBorderWidth: '1',\r\n      hoveredCountryBorderColor: baseThemeVariables['primary'],\r\n      hoveredCountryFillColor: baseThemeVariables['primaryLight'],\r\n      hoveredCountryBorderWidth: '1',\r\n\r\n      chartAxisLineColor: baseThemeVariables['border4'],\r\n      chartAxisTextColor: baseThemeVariables['fg'],\r\n      chartAxisFontSize: '16',\r\n      chartGradientTo: baseThemeVariables['primary'],\r\n      chartGradientFrom: baseThemeVariables['primaryLight'],\r\n      chartAxisSplitLine: baseThemeVariables['separator'],\r\n      chartShadowLineColor: baseThemeVariables['primaryLight'],\r\n\r\n      chartLineBottomShadowColor: baseThemeVariables['primary'],\r\n\r\n      chartInnerLineColor: baseThemeVariables['bg2'],\r\n    },\r\n\r\n    echarts: {\r\n      bg: baseThemeVariables['bg'],\r\n      textColor: baseThemeVariables['fgText'],\r\n      axisLineColor: baseThemeVariables['fgText'],\r\n      splitLineColor: baseThemeVariables['separator'],\r\n      itemHoverShadowColor: 'rgba(0, 0, 0, 0.5)',\r\n      tooltipBackgroundColor: baseThemeVariables['primary'],\r\n      areaOpacity: '0.7',\r\n    },\r\n\r\n    chartjs: {\r\n      axisLineColor: baseThemeVariables['separator'],\r\n      textColor: baseThemeVariables['fgText'],\r\n    },\r\n\r\n    orders: {\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\r\n      tooltipLineWidth: '0',\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n      tooltipFontSize: '20',\r\n\r\n      axisLineColor: baseThemeVariables['border4'],\r\n      axisFontSize: '16',\r\n      axisTextColor: baseThemeVariables['fg'],\r\n      yAxisSplitLine: baseThemeVariables['separator'],\r\n\r\n      itemBorderColor: baseThemeVariables['primary'],\r\n      lineStyle: 'solid',\r\n      lineWidth: '4',\r\n\r\n      // first line\r\n      firstAreaGradFrom: baseThemeVariables['bg3'],\r\n      firstAreaGradTo: baseThemeVariables['bg3'],\r\n      firstShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n\r\n      // second line\r\n      secondLineGradFrom: baseThemeVariables['primary'],\r\n      secondLineGradTo: baseThemeVariables['primary'],\r\n\r\n      secondAreaGradFrom: 'rgba(51, 102, 255, 0.2)',\r\n      secondAreaGradTo: 'rgba(51, 102, 255, 0)',\r\n      secondShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n\r\n      // third line\r\n      thirdLineGradFrom: baseThemeVariables['success'],\r\n      thirdLineGradTo: baseThemeVariables['successLight'],\r\n\r\n      thirdAreaGradFrom: 'rgba(0, 214, 143, 0.2)',\r\n      thirdAreaGradTo: 'rgba(0, 214, 143, 0)',\r\n      thirdShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n    },\r\n\r\n    profit: {\r\n      bg: baseThemeVariables['bg'],\r\n      textColor: baseThemeVariables['fgText'],\r\n      axisLineColor: baseThemeVariables['border4'],\r\n      splitLineColor: baseThemeVariables['separator'],\r\n      areaOpacity: '1',\r\n\r\n      axisFontSize: '16',\r\n      axisTextColor: baseThemeVariables['fg'],\r\n\r\n      // first bar\r\n      firstLineGradFrom: baseThemeVariables['bg3'],\r\n      firstLineGradTo: baseThemeVariables['bg3'],\r\n      firstLineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n      // second bar\r\n      secondLineGradFrom: baseThemeVariables['primary'],\r\n      secondLineGradTo: baseThemeVariables['primary'],\r\n      secondLineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n      // third bar\r\n      thirdLineGradFrom: baseThemeVariables['success'],\r\n      thirdLineGradTo: baseThemeVariables['successLight'],\r\n      thirdLineShadow: 'rgba(0, 0, 0, 0)',\r\n    },\r\n\r\n    orderProfitLegend: {\r\n      firstItem: baseThemeVariables['success'],\r\n      secondItem: baseThemeVariables['primary'],\r\n      thirdItem: baseThemeVariables['bg3'],\r\n    },\r\n\r\n    visitors: {\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\r\n      tooltipLineWidth: '1',\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n      tooltipFontSize: '20',\r\n\r\n      axisLineColor: baseThemeVariables['border4'],\r\n      axisFontSize: '16',\r\n      axisTextColor: baseThemeVariables['fg'],\r\n      yAxisSplitLine: baseThemeVariables['separator'],\r\n\r\n      itemBorderColor: baseThemeVariables['primary'],\r\n      lineStyle: 'dotted',\r\n      lineWidth: '6',\r\n      lineGradFrom: '#ffffff',\r\n      lineGradTo: '#ffffff',\r\n      lineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n      areaGradFrom: baseThemeVariables['primary'],\r\n      areaGradTo: baseThemeVariables['primaryLight'],\r\n\r\n      innerLineStyle: 'solid',\r\n      innerLineWidth: '1',\r\n\r\n      innerAreaGradFrom: baseThemeVariables['success'],\r\n      innerAreaGradTo: baseThemeVariables['success'],\r\n    },\r\n\r\n    visitorsLegend: {\r\n      firstIcon: baseThemeVariables['success'],\r\n      secondIcon: baseThemeVariables['primary'],\r\n    },\r\n\r\n    visitorsPie: {\r\n      firstPieGradientLeft: baseThemeVariables['success'],\r\n      firstPieGradientRight: baseThemeVariables['success'],\r\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n      firstPieRadius: ['70%', '90%'],\r\n\r\n      secondPieGradientLeft: baseThemeVariables['warning'],\r\n      secondPieGradientRight: baseThemeVariables['warningLight'],\r\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n      secondPieRadius: ['60%', '97%'],\r\n      shadowOffsetX: '0',\r\n      shadowOffsetY: '0',\r\n    },\r\n\r\n    visitorsPieLegend: {\r\n      firstSection: baseThemeVariables['warning'],\r\n      secondSection: baseThemeVariables['success'],\r\n    },\r\n\r\n    earningPie: {\r\n      radius: ['65%', '100%'],\r\n      center: ['50%', '50%'],\r\n\r\n      fontSize: '22',\r\n\r\n      firstPieGradientLeft: baseThemeVariables['success'],\r\n      firstPieGradientRight: baseThemeVariables['success'],\r\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n\r\n      secondPieGradientLeft: baseThemeVariables['primary'],\r\n      secondPieGradientRight: baseThemeVariables['primary'],\r\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n\r\n      thirdPieGradientLeft: baseThemeVariables['warning'],\r\n      thirdPieGradientRight: baseThemeVariables['warning'],\r\n      thirdPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n    },\r\n\r\n    earningLine: {\r\n      gradFrom: baseThemeVariables['primary'],\r\n      gradTo: baseThemeVariables['primary'],\r\n\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n      tooltipFontSize: '16',\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipBorderWidth: '1',\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\r\n    },\r\n  },\r\n} as NbJSThemeOptions;\r\n"], "mappings": "AAAA,SAA2BA,aAAa,IAAIC,SAAS,QAAQ,gBAAgB;AAE7E,MAAMC,kBAAkB,GAAGD,SAAS,CAACE,SAAU;AAE/C;AACA,MAAMC,YAAY,GAAG,SAAS;AAC9B,MAAMC,UAAU,GAAG,SAAS;AAC5B,MAAMC,SAAS,GAAG,SAAS;AAE3B,OAAO,MAAMN,aAAa,GAAG;EAC3BO,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfL,SAAS,EAAE;IACT;IACAM,OAAO,EAAEL,YAAY;IACrBM,YAAY,EAAEL,UAAU;IACxBM,WAAW,EAAEL,SAAS;IAEtBM,WAAW,EAAE;MACXC,OAAO,EAAE,CACPT,YAAY,EACZA,YAAY,EACZA,YAAY,EACZA,YAAY,EACZA,YAAY,CACb;MACDU,QAAQ,EAAEZ,kBAAkB,CAAC,KAAK,CAAC;MACnCa,OAAO,EAAEb,kBAAkB,CAAC,KAAK,CAAC;MAClCc,WAAW,EAAEZ;KACd;IAEDa,KAAK,EAAE;MACLC,YAAY,EAAEhB,kBAAkB,CAAC,SAAS,CAAC;MAC3CiB,aAAa,EAAEjB,kBAAkB,CAAC,SAAS,CAAC;MAC5CkB,WAAW,EAAE,kBAAkB;MAC/BC,gBAAgB,EAAEnB,kBAAkB,CAAC,KAAK,CAAC;MAC3CoB,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK;KACtB;IAEDC,OAAO,EAAE;MACPC,SAAS,EAAEtB,kBAAkB,CAAC,IAAI,CAAC;MACnCuB,kBAAkB,EAAEvB,kBAAkB,CAAC,SAAS,CAAC;MACjDwB,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAEzB,kBAAkB,CAAC,QAAQ,CAAC;MAC9C0B,iBAAiB,EAAE,QAAQ;MAE3BC,cAAc,EAAE3B,kBAAkB,CAAC,WAAW,CAAC;MAE/C4B,MAAM,EAAE5B,kBAAkB,CAAC,SAAS,CAAC;MACrC6B,cAAc,EAAE,GAAG;MACnBC,SAAS,EAAE9B,kBAAkB,CAAC,SAAS,CAAC;MACxC+B,eAAe,EAAE/B,kBAAkB,CAAC,SAAS,CAAC;MAC9CgC,uBAAuB,EAAEhC,kBAAkB,CAAC,SAAS,CAAC;MACtDiC,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,kBAAkB;MACpCC,QAAQ,EAAEnC,kBAAkB,CAAC,KAAK,CAAC;MACnCoC,MAAM,EAAEpC,kBAAkB,CAAC,KAAK;KACjC;IAEDqC,WAAW,EAAE;MACXf,SAAS,EAAEtB,kBAAkB,CAAC,IAAI,CAAC;MACnCsC,gBAAgB,EAAEtC,kBAAkB,CAAC,QAAQ,CAAC;MAC9CuC,gBAAgB,EAAE,GAAG;MACrBhB,kBAAkB,EAAEvB,kBAAkB,CAAC,SAAS,CAAC;MACjDwB,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAEzB,kBAAkB,CAAC,QAAQ,CAAC;MAC9C0B,iBAAiB,EAAE,QAAQ;MAE3Bc,aAAa,EAAExC,kBAAkB,CAAC,SAAS,CAAC;MAC5CyC,cAAc,EAAEzC,kBAAkB,CAAC,IAAI,CAAC;MACxC2B,cAAc,EAAE3B,kBAAkB,CAAC,WAAW,CAAC;MAE/C+B,eAAe,EAAE/B,kBAAkB,CAAC,SAAS,CAAC;MAC9C0C,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,GAAG;MACdC,YAAY,EAAE5C,kBAAkB,CAAC,SAAS,CAAC;MAC3C6C,UAAU,EAAE7C,kBAAkB,CAAC,SAAS,CAAC;MACzC8C,UAAU,EAAE,kBAAkB;MAE9BC,YAAY,EAAE/C,kBAAkB,CAAC,KAAK,CAAC;MACvCgD,UAAU,EAAEhD,kBAAkB,CAAC,KAAK,CAAC;MACrCiC,gBAAgB,EAAE;KACnB;IAEDgB,SAAS,EAAE;MACTC,UAAU,EAAElD,kBAAkB,CAAC,QAAQ,CAAC;MACxCmD,SAAS,EAAEnD,kBAAkB,CAAC,KAAK,CAAC;MACpCoD,cAAc,EAAEpD,kBAAkB,CAAC,aAAa,CAAC;MACjDqD,eAAe,EAAErD,kBAAkB,CAAC,SAAS;KAC9C;IAEDsD,wBAAwB,EAAE;MACxBC,SAAS,EAAEvD,kBAAkB,CAAC,QAAQ,CAAC;MAEvCwD,sBAAsB,EAAExD,kBAAkB,CAAC,SAAS,CAAC;MACrDyD,uBAAuB,EAAEzD,kBAAkB,CAAC,SAAS,CAAC;MAEtD0D,qBAAqB,EAAE,GAAG;MAC1BC,mBAAmB,EAAE,GAAG;MACxBC,mBAAmB,EAAE5D,kBAAkB,CAAC,WAAW,CAAC;MAEpDyB,gBAAgB,EAAEzB,kBAAkB,CAAC,QAAQ,CAAC;MAC9C0B,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MACrBvC,SAAS,EAAEtB,kBAAkB,CAAC,IAAI,CAAC;MACnCuB,kBAAkB,EAAEvB,kBAAkB,CAAC,SAAS,CAAC;MACjD8D,kBAAkB,EAAE,GAAG;MACvBtC,eAAe,EAAE;KAClB;IAEDuC,gBAAgB,EAAE;MAChBC,YAAY,EAAEhE,kBAAkB,CAAC,cAAc,CAAC;MAChDiE,UAAU,EAAEjE,kBAAkB,CAAC,SAAS,CAAC;MACzCkE,MAAM,EAAElE,kBAAkB,CAAC,cAAc,CAAC;MAC1CmE,UAAU,EAAE,GAAG;MAEfC,aAAa,EAAEpE,kBAAkB,CAAC,QAAQ,CAAC;MAC3CqE,YAAY,EAAE,IAAI;MAElB/C,SAAS,EAAEtB,kBAAkB,CAAC,IAAI,CAAC;MACnCuB,kBAAkB,EAAEvB,kBAAkB,CAAC,SAAS,CAAC;MACjDwB,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAEzB,kBAAkB,CAAC,QAAQ,CAAC;MAC9C0B,iBAAiB,EAAE;KACpB;IAED4C,aAAa,EAAE;MACbC,kBAAkB,EAAEvE,kBAAkB,CAAC,SAAS,CAAC;MACjDwE,gBAAgB,EAAExE,kBAAkB,CAAC,KAAK,CAAC;MAC3CyE,kBAAkB,EAAE,GAAG;MACvBC,yBAAyB,EAAE1E,kBAAkB,CAAC,SAAS,CAAC;MACxD2E,uBAAuB,EAAE3E,kBAAkB,CAAC,cAAc,CAAC;MAC3D4E,yBAAyB,EAAE,GAAG;MAE9BC,kBAAkB,EAAE7E,kBAAkB,CAAC,SAAS,CAAC;MACjD8E,kBAAkB,EAAE9E,kBAAkB,CAAC,IAAI,CAAC;MAC5C+E,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAEhF,kBAAkB,CAAC,SAAS,CAAC;MAC9CiF,iBAAiB,EAAEjF,kBAAkB,CAAC,cAAc,CAAC;MACrDkF,kBAAkB,EAAElF,kBAAkB,CAAC,WAAW,CAAC;MACnDmF,oBAAoB,EAAEnF,kBAAkB,CAAC,cAAc,CAAC;MAExDoF,0BAA0B,EAAEpF,kBAAkB,CAAC,SAAS,CAAC;MAEzDqF,mBAAmB,EAAErF,kBAAkB,CAAC,KAAK;KAC9C;IAEDsF,OAAO,EAAE;MACPC,EAAE,EAAEvF,kBAAkB,CAAC,IAAI,CAAC;MAC5BuD,SAAS,EAAEvD,kBAAkB,CAAC,QAAQ,CAAC;MACvCwC,aAAa,EAAExC,kBAAkB,CAAC,QAAQ,CAAC;MAC3CwF,cAAc,EAAExF,kBAAkB,CAAC,WAAW,CAAC;MAC/CyF,oBAAoB,EAAE,oBAAoB;MAC1CC,sBAAsB,EAAE1F,kBAAkB,CAAC,SAAS,CAAC;MACrD2F,WAAW,EAAE;KACd;IAEDC,OAAO,EAAE;MACPpD,aAAa,EAAExC,kBAAkB,CAAC,WAAW,CAAC;MAC9CuD,SAAS,EAAEvD,kBAAkB,CAAC,QAAQ;KACvC;IAED6F,MAAM,EAAE;MACNvE,SAAS,EAAEtB,kBAAkB,CAAC,IAAI,CAAC;MACnCsC,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,GAAG;MACrBhB,kBAAkB,EAAEvB,kBAAkB,CAAC,SAAS,CAAC;MACjDwB,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAEzB,kBAAkB,CAAC,QAAQ,CAAC;MAC9C0B,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MAErBrB,aAAa,EAAExC,kBAAkB,CAAC,SAAS,CAAC;MAC5CqE,YAAY,EAAE,IAAI;MAClBD,aAAa,EAAEpE,kBAAkB,CAAC,IAAI,CAAC;MACvC2B,cAAc,EAAE3B,kBAAkB,CAAC,WAAW,CAAC;MAE/C+B,eAAe,EAAE/B,kBAAkB,CAAC,SAAS,CAAC;MAC9C0C,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,GAAG;MAEd;MACAmD,iBAAiB,EAAE9F,kBAAkB,CAAC,KAAK,CAAC;MAC5C+F,eAAe,EAAE/F,kBAAkB,CAAC,KAAK,CAAC;MAC1CgG,qBAAqB,EAAE,kBAAkB;MAEzC;MACAC,kBAAkB,EAAEjG,kBAAkB,CAAC,SAAS,CAAC;MACjDkG,gBAAgB,EAAElG,kBAAkB,CAAC,SAAS,CAAC;MAE/CmG,kBAAkB,EAAE,yBAAyB;MAC7CC,gBAAgB,EAAE,uBAAuB;MACzCC,sBAAsB,EAAE,kBAAkB;MAE1C;MACAC,iBAAiB,EAAEtG,kBAAkB,CAAC,SAAS,CAAC;MAChDuG,eAAe,EAAEvG,kBAAkB,CAAC,cAAc,CAAC;MAEnDwG,iBAAiB,EAAE,wBAAwB;MAC3CC,eAAe,EAAE,sBAAsB;MACvCC,qBAAqB,EAAE;KACxB;IAEDC,MAAM,EAAE;MACNpB,EAAE,EAAEvF,kBAAkB,CAAC,IAAI,CAAC;MAC5BuD,SAAS,EAAEvD,kBAAkB,CAAC,QAAQ,CAAC;MACvCwC,aAAa,EAAExC,kBAAkB,CAAC,SAAS,CAAC;MAC5CwF,cAAc,EAAExF,kBAAkB,CAAC,WAAW,CAAC;MAC/C2F,WAAW,EAAE,GAAG;MAEhBtB,YAAY,EAAE,IAAI;MAClBD,aAAa,EAAEpE,kBAAkB,CAAC,IAAI,CAAC;MAEvC;MACA4G,iBAAiB,EAAE5G,kBAAkB,CAAC,KAAK,CAAC;MAC5C6G,eAAe,EAAE7G,kBAAkB,CAAC,KAAK,CAAC;MAC1C8G,eAAe,EAAE,kBAAkB;MAEnC;MACAb,kBAAkB,EAAEjG,kBAAkB,CAAC,SAAS,CAAC;MACjDkG,gBAAgB,EAAElG,kBAAkB,CAAC,SAAS,CAAC;MAC/C+G,gBAAgB,EAAE,kBAAkB;MAEpC;MACAT,iBAAiB,EAAEtG,kBAAkB,CAAC,SAAS,CAAC;MAChDuG,eAAe,EAAEvG,kBAAkB,CAAC,cAAc,CAAC;MACnDgH,eAAe,EAAE;KAClB;IAEDC,iBAAiB,EAAE;MACjBC,SAAS,EAAElH,kBAAkB,CAAC,SAAS,CAAC;MACxCmH,UAAU,EAAEnH,kBAAkB,CAAC,SAAS,CAAC;MACzCoH,SAAS,EAAEpH,kBAAkB,CAAC,KAAK;KACpC;IAEDqH,QAAQ,EAAE;MACR/F,SAAS,EAAEtB,kBAAkB,CAAC,IAAI,CAAC;MACnCsC,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,GAAG;MACrBhB,kBAAkB,EAAEvB,kBAAkB,CAAC,SAAS,CAAC;MACjDwB,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAEzB,kBAAkB,CAAC,QAAQ,CAAC;MAC9C0B,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MAErBrB,aAAa,EAAExC,kBAAkB,CAAC,SAAS,CAAC;MAC5CqE,YAAY,EAAE,IAAI;MAClBD,aAAa,EAAEpE,kBAAkB,CAAC,IAAI,CAAC;MACvC2B,cAAc,EAAE3B,kBAAkB,CAAC,WAAW,CAAC;MAE/C+B,eAAe,EAAE/B,kBAAkB,CAAC,SAAS,CAAC;MAC9C0C,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,GAAG;MACdC,YAAY,EAAE,SAAS;MACvBC,UAAU,EAAE,SAAS;MACrBC,UAAU,EAAE,kBAAkB;MAE9BC,YAAY,EAAE/C,kBAAkB,CAAC,SAAS,CAAC;MAC3CgD,UAAU,EAAEhD,kBAAkB,CAAC,cAAc,CAAC;MAE9CsH,cAAc,EAAE,OAAO;MACvBC,cAAc,EAAE,GAAG;MAEnBC,iBAAiB,EAAExH,kBAAkB,CAAC,SAAS,CAAC;MAChDyH,eAAe,EAAEzH,kBAAkB,CAAC,SAAS;KAC9C;IAED0H,cAAc,EAAE;MACdC,SAAS,EAAE3H,kBAAkB,CAAC,SAAS,CAAC;MACxC4H,UAAU,EAAE5H,kBAAkB,CAAC,SAAS;KACzC;IAED6H,WAAW,EAAE;MACXC,oBAAoB,EAAE9H,kBAAkB,CAAC,SAAS,CAAC;MACnD+H,qBAAqB,EAAE/H,kBAAkB,CAAC,SAAS,CAAC;MACpDgI,mBAAmB,EAAE,kBAAkB;MACvCC,cAAc,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;MAE9BC,qBAAqB,EAAElI,kBAAkB,CAAC,SAAS,CAAC;MACpDmI,sBAAsB,EAAEnI,kBAAkB,CAAC,cAAc,CAAC;MAC1DoI,oBAAoB,EAAE,kBAAkB;MACxCC,eAAe,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;MAC/BC,aAAa,EAAE,GAAG;MAClBC,aAAa,EAAE;KAChB;IAEDC,iBAAiB,EAAE;MACjBC,YAAY,EAAEzI,kBAAkB,CAAC,SAAS,CAAC;MAC3C0I,aAAa,EAAE1I,kBAAkB,CAAC,SAAS;KAC5C;IAED2I,UAAU,EAAE;MACVvH,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;MACvBwH,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;MAEtBC,QAAQ,EAAE,IAAI;MAEdf,oBAAoB,EAAE9H,kBAAkB,CAAC,SAAS,CAAC;MACnD+H,qBAAqB,EAAE/H,kBAAkB,CAAC,SAAS,CAAC;MACpDgI,mBAAmB,EAAE,kBAAkB;MAEvCE,qBAAqB,EAAElI,kBAAkB,CAAC,SAAS,CAAC;MACpDmI,sBAAsB,EAAEnI,kBAAkB,CAAC,SAAS,CAAC;MACrDoI,oBAAoB,EAAE,kBAAkB;MAExCU,oBAAoB,EAAE9I,kBAAkB,CAAC,SAAS,CAAC;MACnD+I,qBAAqB,EAAE/I,kBAAkB,CAAC,SAAS,CAAC;MACpDgJ,mBAAmB,EAAE;KACtB;IAEDC,WAAW,EAAE;MACX9G,QAAQ,EAAEnC,kBAAkB,CAAC,SAAS,CAAC;MACvCoC,MAAM,EAAEpC,kBAAkB,CAAC,SAAS,CAAC;MAErCyB,gBAAgB,EAAEzB,kBAAkB,CAAC,QAAQ,CAAC;MAC9C0B,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MACrBvC,SAAS,EAAEtB,kBAAkB,CAAC,IAAI,CAAC;MACnCuB,kBAAkB,EAAEvB,kBAAkB,CAAC,SAAS,CAAC;MACjD8D,kBAAkB,EAAE,GAAG;MACvBtC,eAAe,EAAE;;;CAGF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}