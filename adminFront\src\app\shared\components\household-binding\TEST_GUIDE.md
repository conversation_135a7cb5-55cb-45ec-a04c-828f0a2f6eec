# 🧪 HouseholdBindingComponent 測試指南

## 快速開始測試

### 1. 啟動開發服務器
```bash
ng serve
```

### 2. 訪問演示頁面
在瀏覽器中導航到包含 `<app-household-binding-demo></app-household-binding-demo>` 的頁面。

### 3. API 測試步驟

#### 步驟 A：測試 API 載入
1. 在「使用 API 資料」區域找到建案ID輸入框
2. 輸入一個測試建案ID (例如：1)
3. 點擊「套用」按鈕
4. 觀察：
   - 載入狀態顯示
   - 下拉選單中的載入動畫
   - 資料載入完成後的棟別列表

#### 步驟 B：測試 API 錯誤處理
1. 輸入一個無效的建案ID (例如：999999)
2. 點擊「套用」按鈕
3. 觀察：
   - 錯誤會在 Console 中顯示
   - 元件自動回退到 Mock 資料
   - 功能仍然正常運作

#### 步驟 C：測試功能
1. 開啟下拉選單
2. 選擇一個棟別 (例如：A棟)
3. 測試以下功能：
   - 單個戶別選擇
   - 搜尋戶別
   - 批次選擇
   - 取消選擇

### 4. 檢查 Console 輸出
打開瀏覽器開發者工具 (F12)，在 Console 標籤中查看：
- API 請求日誌
- 選擇變更事件
- 錯誤訊息 (如果有)

### 5. 測試多元件協調
1. 在「基本使用」區域選擇一些戶別
2. 在「不顯示已選擇區域」區域開啟下拉選單
3. 觀察已選擇的戶別被標記為「已排除」(灰色 + 紅色 ✕)

## 預期結果

### ✅ 成功指標
- [ ] API 載入時顯示載入狀態
- [ ] API 資料正確轉換並顯示
- [ ] API 錯誤時回退到 Mock 資料
- [ ] 所有基本功能正常運作
- [ ] 選擇結果正確顯示在下方區域
- [ ] Console 中有詳細的日誌資訊

### ❌ 需要注意的問題
- 如果 API 服務不可用，只會看到 Mock 資料
- 如果建案ID無效，會在 Console 看到錯誤訊息
- 網路問題可能導致載入失敗

## 故障排除

### 問題：API 資料無法載入
**可能原因：**
- API 服務未啟動
- 建案ID不存在
- 網路連線問題

**解決方案：**
1. 檢查 API 服務是否運行
2. 確認建案ID的有效性
3. 查看 Console 中的錯誤訊息

### 問題：載入狀態一直顯示
**可能原因：**
- API 請求卡住
- 回應格式不正確

**解決方案：**
1. 重新整理頁面
2. 檢查網路連線
3. 查看 Network 標籤中的 API 請求

### 問題：選擇功能異常
**可能原因：**
- 資料格式轉換問題
- JavaScript 錯誤

**解決方案：**
1. 查看 Console 錯誤訊息
2. 確認資料結構正確
3. 嘗試使用 Mock 資料測試

## 進階測試

### 測試不同的建案ID
嘗試輸入不同的建案ID來測試各種情境：
- 有效ID：1, 2, 3...
- 無效ID：0, -1, 999999
- 特殊值：null, undefined, 空字串

### 測試邊界條件
- 大量戶別資料
- 空的戶別資料
- 特殊字元戶別代碼

### 性能測試
- 大建案的載入時間
- 搜尋功能的響應速度
- 記憶體使用情況

---

💡 **提示：** 如果遇到任何問題，請檢查 Console 輸出並參考上方的故障排除指南。
