{"ast": null, "code": "//! moment.js locale configuration\n//! locale : French [fr]\n//! author : <PERSON> : https://github.com/jfroffice\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var monthsStrictRegex = /^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,\n    monthsShortStrictRegex = /(janv\\.?|févr\\.?|mars|avr\\.?|mai|juin|juil\\.?|août|sept\\.?|oct\\.?|nov\\.?|déc\\.?)/i,\n    monthsRegex = /(janv\\.?|févr\\.?|mars|avr\\.?|mai|juin|juil\\.?|août|sept\\.?|oct\\.?|nov\\.?|déc\\.?|janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,\n    monthsParse = [/^janv/i, /^févr/i, /^mars/i, /^avr/i, /^mai/i, /^juin/i, /^juil/i, /^août/i, /^sept/i, /^oct/i, /^nov/i, /^déc/i];\n  var fr = moment.defineLocale('fr', {\n    months: 'janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre'.split('_'),\n    monthsShort: 'janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.'.split('_'),\n    monthsRegex: monthsRegex,\n    monthsShortRegex: monthsRegex,\n    monthsStrictRegex: monthsStrictRegex,\n    monthsShortStrictRegex: monthsShortStrictRegex,\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: monthsParse,\n    weekdays: 'dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi'.split('_'),\n    weekdaysShort: 'dim._lun._mar._mer._jeu._ven._sam.'.split('_'),\n    weekdaysMin: 'di_lu_ma_me_je_ve_sa'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Aujourd’hui à] LT',\n      nextDay: '[Demain à] LT',\n      nextWeek: 'dddd [à] LT',\n      lastDay: '[Hier à] LT',\n      lastWeek: 'dddd [dernier à] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'dans %s',\n      past: 'il y a %s',\n      s: 'quelques secondes',\n      ss: '%d secondes',\n      m: 'une minute',\n      mm: '%d minutes',\n      h: 'une heure',\n      hh: '%d heures',\n      d: 'un jour',\n      dd: '%d jours',\n      w: 'une semaine',\n      ww: '%d semaines',\n      M: 'un mois',\n      MM: '%d mois',\n      y: 'un an',\n      yy: '%d ans'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(er|)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        // TODO: Return 'e' when day of month > 1. Move this case inside\n        // block for masculine words below.\n        // See https://github.com/moment/moment/issues/3375\n        case 'D':\n          return number + (number === 1 ? 'er' : '');\n\n        // Words with masculine grammatical gender: mois, trimestre, jour\n        default:\n        case 'M':\n        case 'Q':\n        case 'DDD':\n        case 'd':\n          return number + (number === 1 ? 'er' : 'e');\n\n        // Words with feminine grammatical gender: semaine\n        case 'w':\n        case 'W':\n          return number + (number === 1 ? 're' : 'e');\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return fr;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}