import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { BaseComponent } from '../../components/base/baseComponent';
import { MessageService } from 'src/app/shared/services/message.service';
import { EnumFileType } from 'src/app/shared/enum/enumFileType';
import { environment } from 'src/environments/environment';
import { FileService } from 'src/services/api/services';

export interface FileUploadResult {
  CName: string;
  CFile: string;
  Cimg: File;
  CFileUpload: File;
  CFileType: number;
  fileName: string;
}

export interface MultiFileUploadResult {
  data: string;
  CFileBlood: string;
  CFileName: string;
  CFileType: number;
}

export interface FileUploadConfig {
  acceptedTypes?: string[];
  acceptedFileRegex?: RegExp;
  acceptAttribute?: string;
  label?: string;
  helpText?: string;
  required?: boolean;
  disabled?: boolean;
  autoFillName?: boolean;
  buttonText?: string;
  buttonIcon?: string;
  maxFileSize?: number; // in MB
  multiple?: boolean; // 是否支援多檔案上傳
  showPreview?: boolean; // 是否顯示檔案預覽
  hideSelectButton?: boolean; // 是否隱藏選擇檔案按鈕
}

@Component({
  selector: 'app-file-upload',
  templateUrl: './file-upload.component.html',
  styleUrls: ['./file-upload.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class FileUploadComponent extends BaseComponent implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef;
  @Input() config: FileUploadConfig = {
    acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],
    acceptedFileRegex: /pdf|jpg|jpeg|png/i,
    acceptAttribute: 'image/jpeg, image/jpg, application/pdf',
    label: '上傳檔案',
    helpText: '*請上傳PDF格式',
    required: false,
    disabled: false,
    autoFillName: false,
    buttonText: '上傳',
    buttonIcon: 'fa-solid fa-cloud-arrow-up',
    maxFileSize: 10,
    multiple: false,
    showPreview: false,
    hideSelectButton: false
  };
  @Input() currentFileName: string | null = null;
  @Input() currentFileUrl: string | null = null;
  @Input() labelMinWidth: string = '172px';
  @Input() fileList: MultiFileUploadResult[] = []; // 用於多檔案模式的檔案列表
  @Input() existingFiles: any[] = []; // 已存在的檔案列表（編輯模式用）

  @Output() fileSelected = new EventEmitter<FileUploadResult>();
  @Output() fileCleared = new EventEmitter<void>();
  @Output() nameAutoFilled = new EventEmitter<string>();
  @Output() multiFileSelected = new EventEmitter<MultiFileUploadResult[]>(); // 多檔案選擇事件
  @Output() fileRemoved = new EventEmitter<number>(); // 檔案移除事件

  fileName: string | null = null;
  constructor(
    private _allow: AllowHelper,
    private message: MessageService,
    private fileService: FileService
  ) {
    super(_allow);
  }

  override ngOnInit(): void {
    this.fileName = this.currentFileName;    // 設置預設配置
    this.config = {
      ...{
        acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],
        acceptedFileRegex: /pdf|jpg|jpeg|png/i,
        acceptAttribute: 'image/jpeg, image/jpg, application/pdf',
        label: '上傳檔案',
        helpText: '*請上傳PDF格式',
        required: false,
        disabled: false,
        autoFillName: false,
        buttonText: '上傳',
        buttonIcon: 'fa-solid fa-cloud-arrow-up',
        maxFileSize: 10,
        multiple: false,
        showPreview: false,
        hideSelectButton: false
      },
      ...this.config
    };
  }
  onFileSelected(event: any) {
    const files: FileList = event.target.files;

    if (!files || files.length === 0) {
      return;
    }

    if (this.config.multiple) {
      this.handleMultipleFiles(files);
    } else {
      this.handleSingleFile(files[0]);
    }
  }

  private handleSingleFile(file: File) {
    // 檔案格式檢查
    if (!this.config.acceptedFileRegex!.test(file.name)) {
      const acceptedFormats = this.getAcceptedFormatsText();
      this.message.showErrorMSG(`檔案格式錯誤，${acceptedFormats}`);
      return;
    }

    // 檔案大小檢查
    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);
      return;
    }

    this.fileName = file.name;

    // 自動填入名稱功能
    if (this.config.autoFillName) {
      const fileNameWithoutExtension = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;
      this.nameAutoFilled.emit(fileNameWithoutExtension);
    }

    const reader = new FileReader();
    reader.onload = (e: any) => {
      // 判斷檔案類型
      let fileType: number;
      if (file.type.startsWith('image/')) {
        fileType = EnumFileType.JPG; // 圖片
      } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {
        fileType = 3; // CAD檔案
      } else {
        fileType = EnumFileType.PDF; // PDF
      }

      const result: FileUploadResult = {
        CName: file.name,
        CFile: e.target?.result?.toString().split(',')[1],
        Cimg: file.name.includes('pdf') ? file : file,
        CFileUpload: file,
        CFileType: fileType,
        fileName: file.name
      };

      this.fileSelected.emit(result);

      if (this.fileInput) {
        this.fileInput.nativeElement.value = null;
      }
    };
    reader.readAsDataURL(file);
  }

  private handleMultipleFiles(files: FileList) {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];
    const fileRegex = this.config.acceptedFileRegex || /pdf|jpg|jpeg|png|dwg|dxf/i;
    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;

    // 如果是第一次選擇檔案且支援自動填入名稱
    if (this.config.autoFillName && files.length > 0) {
      const firstFile = files[0];
      const fileName = firstFile.name;
      const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
      this.nameAutoFilled.emit(fileNameWithoutExtension);
    }

    const newFiles: MultiFileUploadResult[] = [];
    let processedCount = 0;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // 檔案格式檢查
      if (!fileRegex.test(file.name)) {
        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');
        processedCount++;
        continue;
      }

      // 檔案大小檢查
      if (file.size > maxSizeInBytes) {
        this.message.showErrorMSG(`檔案 ${file.name} 大小超過限制（${this.config.maxFileSize}MB）`);
        processedCount++;
        continue;
      }

      if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {
        const reader = new FileReader();
        reader.onload = (e: any) => {
          // 判斷檔案類型
          let fileType = 1; // 預設為其他
          const fileName = file.name.toLowerCase();

          if (fileName.match(/\.(jpg|jpeg|png)$/)) {
            fileType = 2; // 圖片
          } else if (fileName.endsWith('.pdf')) {
            fileType = 1; // PDF
          } else if (fileName.match(/\.(dwg|dxf)$/)) {
            fileType = 3; // CAD
          }

          newFiles.push({
            data: e.target.result,
            CFileBlood: this.removeBase64Prefix(e.target.result),
            CFileName: file.name,
            CFileType: fileType
          });

          processedCount++;
          if (processedCount === files.length) {
            this.fileList = [...this.fileList, ...newFiles];
            this.multiFileSelected.emit(this.fileList);

            if (this.fileInput) {
              this.fileInput.nativeElement.value = null;
            }
          }
        };
        reader.readAsDataURL(file);
      } else {
        processedCount++;
      }
    }
  }
  clearFile() {
    this.fileName = null;
    this.fileCleared.emit();
    if (this.fileInput) {
      this.fileInput.nativeElement.value = null;
    }
  }
  openFile(url: string) {
    if (url) {
      // 如果 URL 不是完整的 http/https 連結，則添加 base URL
      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;
      window.open(fullUrl, '_blank');
    }
  }

  removeFile(index: number) {
    this.fileList.splice(index, 1);
    this.fileRemoved.emit(index);
    this.multiFileSelected.emit(this.fileList);
  }

  private removeBase64Prefix(base64String: string): string {
    const prefixIndex = base64String.indexOf(",");
    if (prefixIndex !== -1) {
      return base64String.substring(prefixIndex + 1);
    }
    return base64String;
  }

  isImage(fileType: number): boolean {
    return fileType === 2;
  }

  isCad(fileType: number): boolean {
    return fileType === 3;
  }

  isPdf(fileType: number): boolean {
    return fileType === 1;
  }

  isPDFString(str: string): boolean {
    if (str) {
      return str.toLowerCase().endsWith(".pdf");
    }
    return false;
  }

  isCadString(str: string): boolean {
    if (str) {
      const lowerStr = str.toLowerCase();
      return lowerStr.endsWith(".dwg") || lowerStr.endsWith(".dxf");
    }
    return false;
  }  // 處理檔案點擊事件
  handleFileClick(file: any) {
    console.log('檔案點擊事件觸發:', file);
    const fileName = file.CFileName || file.fileName || '';
    const displayName = file.CFileName || fileName;

    // 判斷檔案類型
    const isImageByName = this.isImageFile(displayName);
    const isPdfByName = this.isPDFString(displayName);
    const isCadByName = this.isCadString(displayName);

    console.log('檔案類型判斷:', { isImageByName, isPdfByName, isCadByName, displayName });

    // 統一使用 GetFile API 取得檔案 blob
    const relativePath = file.relativePath || file.CFile;
    const serverFileName = file.fileName || file.CFileName;

    console.log('檔案路徑資訊:', { relativePath, serverFileName });

    if (relativePath && serverFileName) {
      console.log('使用 GetFile API 取得檔案 blob');
      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);
    } else {
      // 如果沒有路徑資訊，顯示錯誤訊息
      console.error('檔案缺少路徑資訊，無法使用 getFile API:', file);
      this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');
    }
  }

  // 處理本地檔案的後備方法 - 已廢棄，統一使用 getFile API
  private handleLocalFile(file: any, isImage: boolean, isPdf: boolean, isCad: boolean) {
    const fileName = file.CFileName || file.fileName || '';
    console.error('檔案缺少必要的路徑資訊，無法使用 getFile API:', file);
    this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');
  }
  // 處理本地檔案資料（新上傳的檔案）- 已廢棄，統一使用 getFile API
  private handleLocalFileData(data: string, fileName: string, isImage: boolean, isPdf: boolean, isCad: boolean) {
    console.warn('handleLocalFileData 方法已廢棄，統一使用 getFile API');
    this.message.showErrorMSG('檔案處理功能已更新，請重新操作');

    /* 原有邏輯已移除，統一使用 getFile API
    if (isImage) {
      // 圖片預覽
      this.openImagePreview(data, fileName);
    } else if (isPdf) {
      // PDF 檔案另開視窗顯示
      this.openPdfInNewWindow(data, fileName);
    } else {
      // 其他檔案（如CAD）轉換為blob並下載
      this.downloadBase64File(data, fileName);
    }
    */
  }

  // 下載base64檔案
  private downloadBase64File(base64Data: string, fileName: string) {
    try {
      // 移除data URL前綴（如果存在）
      const base64String = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;

      // 轉換為blob
      const byteCharacters = atob(base64String);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray]);

      // 創建下載連結
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL
      setTimeout(() => URL.revokeObjectURL(url), 1000);
    } catch (error) {
      console.error('下載檔案失敗:', error);
      this.message.showErrorMSG('檔案下載失敗');
    }
  }

  // 從後端取得檔案 blob
  private getFileFromServer(relativePath: string, fileName: string, displayName: string, isImage: boolean, isPdf: boolean, isCad: boolean) {
    this.fileService.getFile(relativePath, fileName).subscribe({
      next: (blob: Blob) => {
        const url = URL.createObjectURL(blob);

        if (isImage) {
          // 圖片預覽
          this.openImagePreview(url, displayName);
        } else if (isPdf) {
          // PDF 檔案另開視窗顯示
          this.openPdfInNewWindow(url, displayName);
        } else {
          // 其他檔案直接下載
          this.downloadBlobFile(blob, displayName);
        }

        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)
        setTimeout(() => URL.revokeObjectURL(url), 10000);
      },
      error: (error) => {
        console.error('取得檔案失敗:', error);
        this.message.showErrorMSG('取得檔案失敗，請稍後再試');
      }
    });
  }

  // 在新視窗中開啟 PDF
  private openPdfInNewWindow(blobUrl: string, fileName: string) {
    try {
      const newWindow = window.open('', '_blank');
      if (newWindow) {
        newWindow.document.write(`
          <html>
            <head>
              <title>${fileName}</title>
              <style>
                body { margin: 0; padding: 0; }
                iframe { width: 100vw; height: 100vh; border: none; }
              </style>
            </head>
            <body>
              <iframe src="${blobUrl}" type="application/pdf"></iframe>
            </body>
          </html>
        `);
        newWindow.document.close();
      } else {
        // 如果彈出視窗被阻擋，直接開啟 URL
        window.location.href = blobUrl;
      }
    } catch (error) {
      console.error('開啟 PDF 視窗失敗:', error);
      // 後備方案：直接開啟 URL
      window.open(blobUrl, '_blank');
    }
  }

  // 下載 blob 檔案
  private downloadBlobFile(blob: Blob, fileName: string) {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理 URL
    setTimeout(() => URL.revokeObjectURL(url), 1000);
  }
  // 判斷檔案是否為圖片（根據檔名）
  isImageFile(fileName: string): boolean {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    const extension = fileName.split('.').pop()?.toLowerCase();
    return imageExtensions.includes(extension || '');
  }
  // 取得正確的圖片 src
  getImageSrc(file: any): string {
    const fileName = file.CFileName || file.fileName || '';

    if (!fileName) {
      return '';
    }

    // 如果檔案有 data 屬性（base64格式），直接使用
    if (file.data) {
      return file.data;
    }

    // 如果有相對路徑和檔案名，構造 API URL
    if (file.relativePath && file.fileName) {
      return `${environment.BASE_WITHOUT_FILEROOT}/api/File/GetFile?relativePath=${encodeURIComponent(file.relativePath)}&fileName=${encodeURIComponent(file.fileName)}`;
    }

    // 如果是現有檔案且有 CFile 路徑
    if (file.CFile && !file.CFile.startsWith('data:')) {
      return `${environment.BASE_WITHOUT_FILEROOT}${file.CFile}`;
    }

    // 後備方案：空字串
    return '';
  }

  // HTML 模板中使用的方法
  isImageByName(fileName: string): boolean {
    return this.isImageFile(fileName);
  }

  isPDFByName(fileName: string): boolean {
    return this.isPDFString(fileName);
  }

  isCadByName(fileName: string): boolean {
    return this.isCadString(fileName);
  }  // 圖片載入錯誤處理
  onImageError(event: any, file: any) {
    const fileName = file.CFileName || file.fileName || '檔案';
    console.warn('圖片載入失敗:', fileName, file);
    // 可以設置預設圖片或其他處理
    event.target.style.display = 'none';

    // 在圖片載入失敗時，顯示檔案類型圖示，但保留點擊事件
    const container = event.target.parentElement;
    if (container) {
      const iconInfo = this.getFileTypeIcon(file);

      // 創建新的內容並綁定點擊事件
      const newContent = document.createElement('div');
      newContent.className = `w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg ${iconInfo.bgColor}`;
      newContent.innerHTML = `
        <i class="${iconInfo.icon} text-4xl mb-2 ${iconInfo.color}"></i>
        <span class="text-xs font-medium ${iconInfo.color}">${iconInfo.label}</span>
      `;

      // 綁定點擊事件
      newContent.addEventListener('click', () => {
        this.handleFileClick(file);
      });

      // 替換內容
      container.innerHTML = '';
      container.appendChild(newContent);
    }
  }

  private getAcceptedFormatsText(): string {
    const extensions = this.config.acceptAttribute?.split(',').map(type => {
      if (type.includes('pdf')) return 'PDF';
      if (type.includes('jpeg') || type.includes('jpg')) return 'JPG';
      if (type.includes('png')) return 'PNG';
      if (type.includes('.dwg')) return 'DWG';
      if (type.includes('.dxf')) return 'DXF';
      return type;
    }).join('、');

    return `僅限${extensions}格式`;
  }
  // 在瀏覽器中打開 PDF（已廢棄，統一使用 getFile API）
  openPdfInBrowser(fileUrl: string) {
    console.warn('openPdfInBrowser 方法已廢棄，請使用 getFileFromServer');
    this.message.showErrorMSG('PDF 檢視功能已更新，請重新操作');
  }
  // 直接下載檔案（已廢棄，統一使用 getFile API）
  downloadFileDirectly(fileUrl: string, fileName: string) {
    console.warn('downloadFileDirectly 方法已廢棄，請使用 getFileFromServer');
    this.message.showErrorMSG('檔案下載功能已更新，請重新操作');
  }

  // 開啟圖片預覽
  private openImagePreview(imageUrl: string, fileName: string) {
    try {
      const newWindow = window.open('', '_blank');
      if (newWindow) {
        newWindow.document.write(`
          <html>
            <head>
              <title>${fileName}</title>
              <style>
                body {
                  margin: 0;
                  padding: 20px;
                  background-color: #f5f5f5;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  min-height: 100vh;
                }
                img {
                  max-width: 100%;
                  max-height: 100vh;
                  object-fit: contain;
                  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                  background-color: white;
                }
                .title {
                  position: fixed;
                  top: 10px;
                  left: 20px;
                  background: rgba(0, 0, 0, 0.7);
                  color: white;
                  padding: 10px;
                  border-radius: 4px;
                  font-family: Arial, sans-serif;
                }
              </style>
            </head>
            <body>
              <div class="title">${fileName}</div>
              <img src="${imageUrl}" alt="${fileName}" />
            </body>
          </html>
        `);
        newWindow.document.close();
      }
    } catch (error) {
      console.error('開啟圖片預覽失敗:', error);
      window.open(imageUrl, '_blank');
    }
  }

  openNewTab(url: string) {
    if (url) {
      // 如果 URL 不是完整的 http/https 連結，則添加 base URL
      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;
      window.open(fullUrl, '_blank');
    }
  }

  // 取得檔案類型圖示資訊
  getFileTypeIcon(file: any): { icon: string, color: string, bgColor: string, label: string } {
    const fileName = file.CFileName || file.fileName || '';
    const fileType = file.CFileType;
    const extension = fileName.split('.').pop()?.toLowerCase() || '';

    // 圖片類型
    if (fileType === 2 || this.isImageFile(fileName)) {
      if (extension === 'jpg' || extension === 'jpeg') {
        return {
          icon: 'fa fa-file-image-o',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          label: 'JPG'
        };
      } else if (extension === 'png') {
        return {
          icon: 'fa fa-file-image-o',
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          label: 'PNG'
        };
      } else {
        return {
          icon: 'fa fa-file-image-o',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          label: '圖片'
        };
      }
    }

    // PDF類型
    if (fileType === 1 || this.isPDFString(fileName)) {
      return {
        icon: 'fa fa-file-pdf-o',
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        label: 'PDF'
      };
    }

    // CAD類型
    if (fileType === 3 || this.isCadString(fileName)) {
      if (extension === 'dwg') {
        return {
          icon: 'fa fa-cube',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          label: 'DWG'
        };
      } else if (extension === 'dxf') {
        return {
          icon: 'fa fa-cube',
          color: 'text-emerald-600',
          bgColor: 'bg-emerald-50',
          label: 'DXF'
        };
      } else {
        return {
          icon: 'fa fa-cube',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          label: 'CAD'
        };
      }
    }

    // Office文件類型
    if (extension === 'doc' || extension === 'docx') {
      return {
        icon: 'fa fa-file-word-o',
        color: 'text-blue-700',
        bgColor: 'bg-blue-50',
        label: 'Word'
      };
    }

    if (extension === 'xls' || extension === 'xlsx') {
      return {
        icon: 'fa fa-file-excel-o',
        color: 'text-green-700',
        bgColor: 'bg-green-50',
        label: 'Excel'
      };
    }

    if (extension === 'ppt' || extension === 'pptx') {
      return {
        icon: 'fa fa-file-powerpoint-o',
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        label: 'PPT'
      };
    }

    // 壓縮檔案
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
      return {
        icon: 'fa fa-file-archive-o',
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        label: extension.toUpperCase()
      };
    }

    // 文字檔案
    if (['txt', 'log', 'md'].includes(extension)) {
      return {
        icon: 'fa fa-file-text-o',
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        label: extension.toUpperCase()
      };
    }

    // 預設檔案類型
    return {
      icon: 'fa fa-file-o',
      color: 'text-gray-500',
      bgColor: 'bg-gray-50',
      label: extension ? extension.toUpperCase() : '檔案'
    };
  }

  // 取得檔案大小格式化字串
  getFileSize(file: any): string {
    let size = 0;

    if (file.size) {
      size = file.size;
    } else if (file.CFileBlood) {
      // base64 字串大小估算 (約為實際檔案大小的 4/3)
      size = Math.floor(file.CFileBlood.length * 0.75);
    }

    if (size === 0) return '';

    const units = ['B', 'KB', 'MB', 'GB'];
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
  }
}
