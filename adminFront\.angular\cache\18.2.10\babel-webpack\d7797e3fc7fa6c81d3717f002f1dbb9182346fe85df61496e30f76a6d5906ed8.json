{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';\nlet RequirementManagementComponent = class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, templateService, pettern, router, destroyref, spaceTemplateSelectorService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.templateService = templateService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    this.spaceTemplateSelectorService = spaceTemplateSelectorService;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    // 批次編輯相關屬性\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.isBatchEditMode = false;\n    // 批次編輯時的項目資料副本\n    this.batchEditItems = [];\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CIsSimple = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CLocation = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 建案切換事件處理\n  onBuildCaseChange(newBuildCaseId) {\n    // 如果在批次編輯模式下切換建案，給予警告\n    if (this.isBatchEditMode || this.selectedItems.length > 0) {\n      const confirmMessage = this.isBatchEditMode ? '切換建案將會關閉批次編輯對話框並清除所有選擇的項目，是否繼續？' : `切換建案將會清除已選擇的 ${this.selectedItems.length} 個項目，是否繼續？`;\n      if (!confirm(confirmMessage)) {\n        // 使用者取消，恢復原來的建案選擇\n        setTimeout(() => {\n          this.getListRequirementRequest.CBuildCaseID = this.currentBuildCase;\n        }, 0);\n        return;\n      }\n    }\n    // 重置批次選擇的項目\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.batchEditItems = [];\n    this.isBatchEditMode = false;\n    // 更新當前建案並重新載入資料\n    this.currentBuildCase = newBuildCaseId;\n    this.getList();\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 清除選擇狀態和批次編輯相關狀態\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    this.batchEditItems = [];\n    this.isBatchEditMode = false;\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 建案頁面需要驗證建案名稱\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[排序]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 數值範圍驗證\n    if (this.saveRequirement.CSort !== null && this.saveRequirement.CSort !== undefined && this.saveRequirement.CSort < 0) {\n      this.valid.errorMessages.push('[排序] 不能為負數');\n    }\n    if (this.saveRequirement.CUnitPrice !== null && this.saveRequirement.CUnitPrice !== undefined && this.saveRequirement.CUnitPrice < 0) {\n      this.valid.errorMessages.push('[單價] 不能為負數');\n    }\n    // 長度驗證\n    if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    if (this.saveRequirement.CRequirement && this.saveRequirement.CRequirement.length > 50) {\n      this.valid.errorMessages.push('[工程項目] 不能超過50個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false,\n      CIsSimple: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 建案頁面 - 使用當前選擇的建案或第一個建案\n    if (this.currentBuildCase != 0) {\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n    } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n      this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 如果有建案時才查詢\n      if (this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 建案頁面的邏輯\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.requirementList = res.Entries;\n          this.totalRecords = res.TotalItems;\n          // 清理已選擇的項目，移除不存在於新列表中的項目\n          const originalSelectedCount = this.selectedItems.length;\n          this.selectedItems = this.selectedItems.filter(selectedItem => this.requirementList.some(listItem => listItem.CRequirementID === selectedItem.CRequirementID));\n          // 如果選擇的項目數量有變化，清理批次編輯狀態\n          if (originalSelectedCount !== this.selectedItems.length || this.selectedItems.length === 0) {\n            this.batchEditItems = [];\n            this.isBatchEditMode = false;\n          }\n          // 更新選擇狀態\n          this.updateSelectAllState();\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false,\n            CIsSimple: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CLocation = res.Entries.CLocation;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          this.saveRequirement.CSpaceId = res.Entries.CSpaceId || null;\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n          this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  getCIsSimpleText(data) {\n    return data.CIsSimple ? '是' : '否';\n  }\n  // 空間模板相關方法\n  onSpaceTemplateApplied(config) {\n    console.log('套用空間模板配置:', config);\n    // 直接將模板項目轉換為批次編輯項目，使用從模板選擇器傳來的明細數據\n    this.convertTemplatesToBatchEdit(config.selectedItems, config.templateDetails);\n  }\n  onTemplateError(errorMessage) {\n    this.message.showErrorMSG(errorMessage);\n  }\n  // 將模板項目轉換為批次編輯項目\n  convertTemplatesToBatchEdit(selectedTemplates, templateDetails) {\n    if (!this.getListRequirementRequest.CBuildCaseID) {\n      this.message.showErrorMSG('建案 ID 不存在');\n      return;\n    }\n    // 清除當前選擇的項目\n    this.selectedItems = [];\n    this.isAllSelected = false;\n    // 取得預設排序值 - 從列表最大排序值開始\n    const maxSort = this.requirementList.length > 0 ? Math.max(...this.requirementList.map(item => item.CSort || 0)) : 0;\n    let currentSortIndex = 0;\n    const allBatchEditItems = [];\n    // 處理每個模板的明細項目\n    selectedTemplates.forEach(template => {\n      const details = templateDetails.get(template.CTemplateId) || [];\n      if (details && details.length > 0) {\n        // 將每個明細項目轉換為批次編輯項目\n        details.forEach((detail, detailIndex) => {\n          const batchEditItem = {\n            CRequirementID: undefined,\n            // 新項目沒有 ID\n            CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n            CLocation: detail.CLocation || template.CLocation || '',\n            // 優先使用明細的位置，其次使用模板位置\n            CRequirement: detail.CPart || `${template.CTemplateName} - 項目 ${detailIndex + 1}`,\n            // 使用明細名稱作為工程項目\n            CHouseType: template.CHouseType || [],\n            // 從模板獲取房屋類型，預設為空陣列\n            CSort: maxSort + currentSortIndex + 1,\n            // 從最大排序值往上遞增\n            CStatus: 1,\n            // 預設啟用\n            CUnitPrice: detail.CUnitPrice || template.CUnitPrice || 0,\n            // 優先使用明細單價，其次使用模板單價\n            CUnit: detail.CUnit || template.CUnit || '式',\n            // 優先使用明細單位，其次使用模板單位\n            CSpaceId: detail.CReleateId || template.cRelateID || null,\n            // 從明細獲取關聯空間 ID\n            CIsShow: template.CIsShow !== undefined ? template.CIsShow : false,\n            // 預設不顯示在預約需求\n            CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false,\n            // 預設不是簡易客變\n            CRemark: detail.CRemark || `從模板「${template.CTemplateName}」的明細項目「${detail.CPart}」產生` // 記錄來源模板和明細\n          };\n          allBatchEditItems.push(batchEditItem);\n          currentSortIndex++;\n        });\n      } else {\n        // 如果模板沒有明細，則將模板本身作為一個項目\n        const batchEditItem = {\n          CRequirementID: undefined,\n          // 新項目沒有 ID\n          CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n          CLocation: template.CLocation || '',\n          // 從模板獲取區域資訊\n          CRequirement: template.CTemplateName || `模板項目 ${currentSortIndex + 1}`,\n          // 使用模板名稱作為工程項目\n          CHouseType: template.CHouseType || [],\n          // 從模板獲取房屋類型，預設為空陣列\n          CSort: maxSort + currentSortIndex + 1,\n          // 從最大排序值往上遞增\n          CStatus: 1,\n          // 預設啟用\n          CUnitPrice: template.CUnitPrice || 0,\n          // 從模板獲取單價，預設為 0\n          CUnit: template.CUnit || '式',\n          // 從模板獲取單位，預設為 '式'\n          CSpaceId: template.cRelateID || null,\n          // 從模板獲取關聯空間 ID\n          CIsShow: template.CIsShow !== undefined ? template.CIsShow : false,\n          // 預設不顯示在預約需求\n          CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false,\n          // 預設不是簡易客變\n          CRemark: template.CRemark || `從模板「${template.CTemplateName}」產生` // 記錄來源模板\n        };\n        allBatchEditItems.push(batchEditItem);\n        currentSortIndex++;\n      }\n    });\n    // 設定批次編輯項目\n    this.batchEditItems = allBatchEditItems;\n    // 設定為批次編輯模式\n    this.isBatchEditMode = true;\n    // 計算總明細數量\n    const totalDetailCount = selectedTemplates.reduce((sum, template) => {\n      const details = templateDetails.get(template.CTemplateId) || [];\n      return sum + (details.length || 1);\n    }, 0);\n    // 顯示提示\n    const message = `已載入 ${selectedTemplates.length} 個模板，共 ${totalDetailCount} 個明細項目到批次編輯模式。\\n` + `請檢查並調整各項目的設定，包括：\\n` + `• 區域、工程項目名稱\\n` + `• 房屋類型、排序\\n` + `• 單價、單位\\n` + `• 預約需求、簡易客變設定\\n\\n` + `設定完成後點擊「確定批次更新」以儲存所有項目。`;\n    alert(message);\n    // 直接開啟批次編輯對話框\n    setTimeout(() => {\n      this.dialogService.open(this.batchEditDialog);\n    }, 100);\n  } // 批次編輯相關方法\n  // 切換單一項目選擇狀態\n  toggleItemSelection(item) {\n    const index = this.selectedItems.findIndex(selected => selected.CRequirementID === item.CRequirementID);\n    if (index > -1) {\n      this.selectedItems.splice(index, 1);\n    } else {\n      this.selectedItems.push(item);\n    }\n    this.updateSelectAllState();\n  }\n  // 切換全選狀態\n  toggleSelectAll(newValue) {\n    if (this.requirementList.length === 0) {\n      this.selectedItems = [];\n      this.isAllSelected = false;\n      return;\n    }\n    // 更新 isAllSelected 狀態\n    this.isAllSelected = newValue;\n    // 根據新值更新 selectedItems\n    if (this.isAllSelected) {\n      this.selectedItems = [...this.requirementList];\n    } else {\n      this.selectedItems = [];\n    }\n  }\n  // 更新全選狀態\n  updateSelectAllState() {\n    if (this.requirementList.length === 0) {\n      this.isAllSelected = false;\n    } else {\n      this.isAllSelected = this.selectedItems.length === this.requirementList.length;\n    }\n  }\n  // 檢查項目是否被選中\n  isItemSelected(item) {\n    return this.selectedItems.some(selected => selected.CRequirementID === item.CRequirementID);\n  }\n  // 開啟批次編輯對話框\n  openBatchEdit(dialog) {\n    if (this.selectedItems.length === 0) {\n      this.message.showErrorMSG('請先選擇要編輯的項目');\n      return;\n    }\n    this.isBatchEditMode = true;\n    // 初始化批次編輯項目資料\n    this.batchEditItems = this.selectedItems.map(item => ({\n      CRequirementID: item.CRequirementID,\n      CBuildCaseID: item.CBuildCaseID,\n      CLocation: item.CLocation,\n      CRequirement: item.CRequirement,\n      CHouseType: item.CHouseType ? [...item.CHouseType] : [],\n      CSort: item.CSort,\n      CStatus: item.CStatus,\n      CUnitPrice: item.CUnitPrice || 0,\n      CUnit: item.CUnit,\n      CSpaceId: item.CSpaceId || null,\n      CIsShow: item.CIsShow || false,\n      CIsSimple: item.CIsSimple || false,\n      CRemark: item.CRemark\n    }));\n    this.dialogService.open(dialog);\n  }\n  // 批次驗證方法\n  batchValidation() {\n    const errorMessages = [];\n    this.batchEditItems.forEach((item, index) => {\n      const itemNum = index + 1;\n      // 必填欄位檢核\n      if (!item.CBuildCaseID) {\n        errorMessages.push(`[項目 ${itemNum}] 建案名稱為必填欄位`);\n      }\n      if (!item.CRequirement || item.CRequirement.trim() === '') {\n        errorMessages.push(`[項目 ${itemNum}] 工程項目為必填欄位`);\n      }\n      if (!item.CHouseType || item.CHouseType.length === 0) {\n        errorMessages.push(`[項目 ${itemNum}] 類型為必填欄位`);\n      }\n      if (item.CSort === null || item.CSort === undefined || item.CSort < 0) {\n        errorMessages.push(`[項目 ${itemNum}] 排序為必填欄位且不能為負數`);\n      }\n      if (item.CStatus === null || item.CStatus === undefined) {\n        errorMessages.push(`[項目 ${itemNum}] 狀態為必填欄位`);\n      }\n      if (item.CUnitPrice === null || item.CUnitPrice === undefined || item.CUnitPrice < 0) {\n        errorMessages.push(`[項目 ${itemNum}] 單價為必填欄位且不能為負數`);\n      }\n      if (!item.CUnit || item.CUnit.trim() === '') {\n        errorMessages.push(`[項目 ${itemNum}] 單位為必填欄位`);\n      }\n      // 長度驗證\n      if (item.CLocation && item.CLocation.length > 20) {\n        errorMessages.push(`[項目 ${itemNum}] 區域不能超過20個字`);\n      }\n      if (item.CRequirement && item.CRequirement.length > 50) {\n        errorMessages.push(`[項目 ${itemNum}] 工程項目不能超過50個字`);\n      }\n      if (item.CRemark && item.CRemark.length > 100) {\n        errorMessages.push(`[項目 ${itemNum}] 備註說明不能超過100個字`);\n      }\n    });\n    return errorMessages;\n  }\n  // 批次保存\n  batchSave(ref) {\n    if (this.batchEditItems.length === 0) {\n      this.message.showErrorMSG('沒有要更新的項目');\n      return;\n    }\n    // 執行批次驗證\n    const validationErrors = this.batchValidation();\n    if (validationErrors.length > 0) {\n      this.message.showErrorMSGs(validationErrors);\n      return;\n    }\n    // 分離新增項目和更新項目\n    const newItems = this.batchEditItems.filter(item => !item.CRequirementID);\n    const updateItems = this.batchEditItems.filter(item => item.CRequirementID);\n    // 建立請求陣列\n    const requests = [];\n    // 新增項目的請求\n    newItems.forEach(item => {\n      const newItemData = {\n        CBuildCaseID: item.CBuildCaseID,\n        CLocation: item.CLocation,\n        CRequirement: item.CRequirement,\n        CHouseType: item.CHouseType,\n        CSort: item.CSort,\n        CStatus: item.CStatus,\n        CUnitPrice: item.CUnitPrice,\n        CUnit: item.CUnit,\n        CSpaceId: item.CSpaceId,\n        CIsShow: item.CIsShow,\n        CIsSimple: item.CIsSimple,\n        CRemark: item.CRemark\n      };\n      requests.push(this.requirementService.apiRequirementSaveDataPost$Json({\n        body: newItemData\n      }).toPromise());\n    });\n    // 更新項目的請求（如果有的話）\n    if (updateItems.length > 0) {\n      const updateData = updateItems.map(item => ({\n        CRequirementID: item.CRequirementID,\n        CBuildCaseID: item.CBuildCaseID,\n        CLocation: item.CLocation,\n        CRequirement: item.CRequirement,\n        CHouseType: item.CHouseType,\n        CSort: item.CSort,\n        CStatus: item.CStatus,\n        CUnitPrice: item.CUnitPrice,\n        CUnit: item.CUnit,\n        CSpaceId: item.CSpaceId,\n        CIsShow: item.CIsShow,\n        CIsSimple: item.CIsSimple,\n        CRemark: item.CRemark\n      }));\n      requests.push(this.requirementService.apiRequirementBatchSaveDataPost$Json({\n        body: updateData\n      }).toPromise());\n    }\n    // 執行所有請求\n    Promise.all(requests).then(responses => {\n      const successCount = responses.filter(res => res?.StatusCode === 0).length;\n      const totalItems = this.batchEditItems.length;\n      if (successCount === responses.length) {\n        this.message.showSucessMSG(`成功處理 ${totalItems} 個項目 (新增: ${newItems.length}, 更新: ${updateItems.length})`);\n      } else {\n        this.message.showSucessMSG(`成功處理 ${successCount} 個項目，${responses.length - successCount} 個失敗`);\n      }\n      // 清理狀態並重新載入資料\n      this.selectedItems = [];\n      this.batchEditItems = [];\n      this.isBatchEditMode = false;\n      this.updateSelectAllState();\n      this.getList();\n      ref.close();\n    }).catch(error => {\n      console.error('批次保存失敗:', error);\n      this.message.showErrorMSG('批次保存時發生錯誤');\n    });\n  }\n  // 重置批次編輯中的單一項目到原始狀態\n  resetBatchEditItem(index) {\n    const originalItem = this.selectedItems[index];\n    if (originalItem) {\n      this.batchEditItems[index] = {\n        CRequirementID: originalItem.CRequirementID,\n        CBuildCaseID: originalItem.CBuildCaseID,\n        CLocation: originalItem.CLocation,\n        CRequirement: originalItem.CRequirement,\n        CHouseType: originalItem.CHouseType ? [...originalItem.CHouseType] : [],\n        CSort: originalItem.CSort,\n        CStatus: originalItem.CStatus,\n        CUnitPrice: originalItem.CUnitPrice || 0,\n        CUnit: originalItem.CUnit,\n        CSpaceId: originalItem.CSpaceId || null,\n        CIsShow: originalItem.CIsShow || false,\n        CIsSimple: originalItem.CIsSimple || false,\n        CRemark: originalItem.CRemark\n      };\n    }\n  }\n  // 取消批次編輯\n  cancelBatchEdit(ref) {\n    this.isBatchEditMode = false;\n    this.batchEditItems = [];\n    ref.close();\n  }\n  // 備用：如果需要手動建立需求項目的方法\n  batchCreateRequirements(requirements) {\n    const batchRequests = requirements.map(requirement => this.requirementService.apiRequirementSaveDataPost$Json({\n      body: requirement\n    }));\n    Promise.all(batchRequests.map(req => req.toPromise())).then(responses => {\n      const successCount = responses.filter(res => res?.StatusCode === 0).length;\n      this.message.showSucessMSG(`成功建立 ${successCount} 個需求項目`);\n      this.getList();\n    }).catch(error => {\n      console.error('批次建立需求失敗:', error);\n      this.message.showErrorMSG('批次建立需求時發生錯誤');\n    });\n  }\n};\n__decorate([ViewChild('batchEditDialog', {\n  static: false\n})], RequirementManagementComponent.prototype, \"batchEditDialog\", void 0);\nRequirementManagementComponent = __decorate([Component({\n  selector: 'app-requirement-management',\n  standalone: true,\n  imports: [NbCardModule, BreadcrumbComponent, NbInputModule, FormsModule, NbSelectModule, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, FormGroupComponent, NumberWithCommasPipe, SpaceTemplateSelectorButtonComponent],\n  templateUrl: './requirement-management.component.html',\n  styleUrl: './requirement-management.component.scss'\n})], RequirementManagementComponent);\nexport { RequirementManagementComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "SpaceTemplateSelectorButtonComponent", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "templateService", "pettern", "router", "destroyref", "spaceTemplateSelectorService", "getListRequirementRequest", "getRequirementRequest", "buildCaseList", "requirementList", "saveRequirement", "CHouseType", "statusOptions", "value", "label", "houseType", "getEnumOptions", "isNew", "currentBuildCase", "selectedItems", "isAllSelected", "isBatchEditMode", "batchEditItems", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "CStatus", "CIsShow", "CIsSimple", "CRequirement", "CLocation", "map", "type", "onBuildCaseChange", "newBuildCaseId", "length", "confirmMessage", "confirm", "setTimeout", "CBuildCaseID", "getList", "resetSearch", "cID", "getHouseType", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "CSort", "CUnitPrice", "CUnit", "undefined", "errorMessages", "CRemark", "add", "dialog", "open", "onEdit", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "save", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "close", "onDelete", "window", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "TotalItems", "originalSelectedCount", "filter", "selectedItem", "some", "listItem", "updateSelectAllState", "apiRequirementGetDataPost$Json", "CSpaceId", "onHouseTypeChange", "checked", "includes", "v", "getCIsShowText", "getCIsSimpleText", "onSpaceTemplateApplied", "config", "convertTemplatesToBatchEdit", "templateDetails", "onTemplateError", "errorMessage", "selectedTemplates", "maxSort", "Math", "max", "item", "currentSortIndex", "allBatchEditItems", "template", "details", "get", "CTemplateId", "detail", "detailIndex", "batchEditItem", "<PERSON>art", "CTemplateName", "CReleateId", "cRelateID", "totalDetailCount", "reduce", "sum", "alert", "batchEditDialog", "toggleItemSelection", "index", "findIndex", "selected", "splice", "toggleSelectAll", "newValue", "isItemSelected", "openBatchEdit", "batchValidation", "itemNum", "trim", "batchSave", "validationErrors", "newItems", "updateItems", "requests", "newItemData", "to<PERSON>romise", "updateData", "apiRequirementBatchSaveDataPost$Json", "Promise", "all", "then", "responses", "successCount", "totalItems", "catch", "resetBatchEditItem", "originalItem", "cancelBatchEdit", "batchCreateRequirements", "requirements", "batchRequests", "requirement", "req", "__decorate", "static", "selector", "standalone", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { SpaceTemplateSelectorService } from 'src/app/shared/components/space-template-selector/space-template-selector.service';\r\nimport { SpaceTemplateConfig } from 'src/app/shared/components/space-template-selector/space-template-selector.component';\r\nimport { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    SpaceTemplateSelectorButtonComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('batchEditDialog', { static: false }) batchEditDialog!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private templateService: TemplateService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef,\r\n    private spaceTemplateSelectorService: SpaceTemplateSelectorService\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n\r\n  // 批次編輯相關屬性\r\n  selectedItems: GetRequirement[] = [];\r\n  isAllSelected = false;\r\n  isBatchEditMode = false;\r\n  // 批次編輯時的項目資料副本\r\n  batchEditItems: (SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean })[] = [];\r\n\r\n  override ngOnInit(): void { }\r\n\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CIsSimple = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CLocation = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 建案切換事件處理\r\n  onBuildCaseChange(newBuildCaseId: any) {\r\n    // 如果在批次編輯模式下切換建案，給予警告\r\n    if (this.isBatchEditMode || this.selectedItems.length > 0) {\r\n      const confirmMessage = this.isBatchEditMode\r\n        ? '切換建案將會關閉批次編輯對話框並清除所有選擇的項目，是否繼續？'\r\n        : `切換建案將會清除已選擇的 ${this.selectedItems.length} 個項目，是否繼續？`;\r\n\r\n      if (!confirm(confirmMessage)) {\r\n        // 使用者取消，恢復原來的建案選擇\r\n        setTimeout(() => {\r\n          this.getListRequirementRequest.CBuildCaseID = this.currentBuildCase;\r\n        }, 0);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // 重置批次選擇的項目\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n    this.batchEditItems = [];\r\n    this.isBatchEditMode = false;\r\n\r\n    // 更新當前建案並重新載入資料\r\n    this.currentBuildCase = newBuildCaseId;\r\n    this.getList();\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 清除選擇狀態和批次編輯相關狀態\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n    this.batchEditItems = [];\r\n    this.isBatchEditMode = false;\r\n\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n\r\n    // 建案頁面需要驗證建案名稱\r\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[排序]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 數值範圍驗證\r\n    if (this.saveRequirement.CSort !== null && this.saveRequirement.CSort !== undefined && this.saveRequirement.CSort < 0) {\r\n      this.valid.errorMessages.push('[排序] 不能為負數');\r\n    }\r\n\r\n    if (this.saveRequirement.CUnitPrice !== null && this.saveRequirement.CUnitPrice !== undefined && this.saveRequirement.CUnitPrice < 0) {\r\n      this.valid.errorMessages.push('[單價] 不能為負數');\r\n    }\r\n\r\n    // 長度驗證\r\n    if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    if (this.saveRequirement.CRequirement && this.saveRequirement.CRequirement.length > 50) {\r\n      this.valid.errorMessages.push('[工程項目] 不能超過50個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n    if (this.currentBuildCase != 0) {\r\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n    } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n    }\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 如果有建案時才查詢\r\n        if (this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n\r\n    // 建案頁面的邏輯\r\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.requirementList = res.Entries;\r\n            this.totalRecords = res.TotalItems!;\r\n\r\n            // 清理已選擇的項目，移除不存在於新列表中的項目\r\n            const originalSelectedCount = this.selectedItems.length;\r\n            this.selectedItems = this.selectedItems.filter(selectedItem =>\r\n              this.requirementList.some(listItem => listItem.CRequirementID === selectedItem.CRequirementID)\r\n            );\r\n\r\n            // 如果選擇的項目數量有變化，清理批次編輯狀態\r\n            if (originalSelectedCount !== this.selectedItems.length || this.selectedItems.length === 0) {\r\n              this.batchEditItems = [];\r\n              this.isBatchEditMode = false;\r\n            }\r\n\r\n            // 更新選擇狀態\r\n            this.updateSelectAllState();\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CLocation = res.Entries.CLocation;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            this.saveRequirement.CSpaceId = res.Entries.CSpaceId || null;\r\n            this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\r\n            this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  getCIsSimpleText(data: any): string {\r\n    return data.CIsSimple ? '是' : '否';\r\n  }\r\n\r\n  // 空間模板相關方法\r\n  onSpaceTemplateApplied(config: SpaceTemplateConfig) {\r\n    console.log('套用空間模板配置:', config);\r\n\r\n    // 直接將模板項目轉換為批次編輯項目，使用從模板選擇器傳來的明細數據\r\n    this.convertTemplatesToBatchEdit(config.selectedItems, config.templateDetails);\r\n  }\r\n\r\n  onTemplateError(errorMessage: string) {\r\n    this.message.showErrorMSG(errorMessage);\r\n  }\r\n\r\n  // 將模板項目轉換為批次編輯項目\r\n  private convertTemplatesToBatchEdit(selectedTemplates: any[], templateDetails: Map<number, any[]>) {\r\n    if (!this.getListRequirementRequest.CBuildCaseID) {\r\n      this.message.showErrorMSG('建案 ID 不存在');\r\n      return;\r\n    }\r\n\r\n    // 清除當前選擇的項目\r\n    this.selectedItems = [];\r\n    this.isAllSelected = false;\r\n\r\n    // 取得預設排序值 - 從列表最大排序值開始\r\n    const maxSort = this.requirementList.length > 0\r\n      ? Math.max(...this.requirementList.map(item => item.CSort || 0))\r\n      : 0;\r\n\r\n    let currentSortIndex = 0;\r\n    const allBatchEditItems: any[] = [];\r\n\r\n    // 處理每個模板的明細項目\r\n    selectedTemplates.forEach(template => {\r\n      const details = templateDetails.get(template.CTemplateId) || [];\r\n\r\n      if (details && details.length > 0) {\r\n        // 將每個明細項目轉換為批次編輯項目\r\n        details.forEach((detail, detailIndex) => {\r\n          const batchEditItem = {\r\n            CRequirementID: undefined, // 新項目沒有 ID\r\n            CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\r\n            CLocation: detail.CLocation || template.CLocation || '', // 優先使用明細的位置，其次使用模板位置\r\n            CRequirement: detail.CPart || `${template.CTemplateName} - 項目 ${detailIndex + 1}`, // 使用明細名稱作為工程項目\r\n            CHouseType: template.CHouseType || [], // 從模板獲取房屋類型，預設為空陣列\r\n            CSort: maxSort + currentSortIndex + 1, // 從最大排序值往上遞增\r\n            CStatus: 1, // 預設啟用\r\n            CUnitPrice: (detail as any).CUnitPrice || (template as any).CUnitPrice || 0, // 優先使用明細單價，其次使用模板單價\r\n            CUnit: (detail as any).CUnit || (template as any).CUnit || '式', // 優先使用明細單位，其次使用模板單位\r\n            CSpaceId: detail.CReleateId || (template as any).cRelateID || null, // 從明細獲取關聯空間 ID\r\n            CIsShow: template.CIsShow !== undefined ? template.CIsShow : false, // 預設不顯示在預約需求\r\n            CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false, // 預設不是簡易客變\r\n            CRemark: (detail as any).CRemark || `從模板「${template.CTemplateName}」的明細項目「${detail.CPart}」產生` // 記錄來源模板和明細\r\n          };\r\n\r\n          allBatchEditItems.push(batchEditItem);\r\n          currentSortIndex++;\r\n        });\r\n      } else {\r\n        // 如果模板沒有明細，則將模板本身作為一個項目\r\n        const batchEditItem = {\r\n          CRequirementID: undefined, // 新項目沒有 ID\r\n          CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\r\n          CLocation: template.CLocation || '', // 從模板獲取區域資訊\r\n          CRequirement: template.CTemplateName || `模板項目 ${currentSortIndex + 1}`, // 使用模板名稱作為工程項目\r\n          CHouseType: template.CHouseType || [], // 從模板獲取房屋類型，預設為空陣列\r\n          CSort: maxSort + currentSortIndex + 1, // 從最大排序值往上遞增\r\n          CStatus: 1, // 預設啟用\r\n          CUnitPrice: (template as any).CUnitPrice || 0, // 從模板獲取單價，預設為 0\r\n          CUnit: (template as any).CUnit || '式', // 從模板獲取單位，預設為 '式'\r\n          CSpaceId: (template as any).cRelateID || null, // 從模板獲取關聯空間 ID\r\n          CIsShow: template.CIsShow !== undefined ? template.CIsShow : false, // 預設不顯示在預約需求\r\n          CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false, // 預設不是簡易客變\r\n          CRemark: (template as any).CRemark || `從模板「${template.CTemplateName}」產生` // 記錄來源模板\r\n        };\r\n\r\n        allBatchEditItems.push(batchEditItem);\r\n        currentSortIndex++;\r\n      }\r\n    });\r\n\r\n    // 設定批次編輯項目\r\n    this.batchEditItems = allBatchEditItems;\r\n\r\n    // 設定為批次編輯模式\r\n    this.isBatchEditMode = true;\r\n\r\n    // 計算總明細數量\r\n    const totalDetailCount = selectedTemplates.reduce((sum, template) => {\r\n      const details = templateDetails.get(template.CTemplateId) || [];\r\n      return sum + (details.length || 1);\r\n    }, 0);\r\n\r\n    // 顯示提示\r\n    const message = `已載入 ${selectedTemplates.length} 個模板，共 ${totalDetailCount} 個明細項目到批次編輯模式。\\n` +\r\n      `請檢查並調整各項目的設定，包括：\\n` +\r\n      `• 區域、工程項目名稱\\n` +\r\n      `• 房屋類型、排序\\n` +\r\n      `• 單價、單位\\n` +\r\n      `• 預約需求、簡易客變設定\\n\\n` +\r\n      `設定完成後點擊「確定批次更新」以儲存所有項目。`;\r\n\r\n    alert(message);\r\n\r\n    // 直接開啟批次編輯對話框\r\n    setTimeout(() => {\r\n      this.dialogService.open(this.batchEditDialog);\r\n    }, 100);\r\n  }  // 批次編輯相關方法\r\n\r\n  // 切換單一項目選擇狀態\r\n  toggleItemSelection(item: GetRequirement) {\r\n    const index = this.selectedItems.findIndex(selected => selected.CRequirementID === item.CRequirementID);\r\n    if (index > -1) {\r\n      this.selectedItems.splice(index, 1);\r\n    } else {\r\n      this.selectedItems.push(item);\r\n    }\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 切換全選狀態\r\n  toggleSelectAll(newValue: boolean) {\r\n    if (this.requirementList.length === 0) {\r\n      this.selectedItems = [];\r\n      this.isAllSelected = false;\r\n      return;\r\n    }\r\n\r\n    // 更新 isAllSelected 狀態\r\n    this.isAllSelected = newValue;\r\n\r\n    // 根據新值更新 selectedItems\r\n    if (this.isAllSelected) {\r\n      this.selectedItems = [...this.requirementList];\r\n    } else {\r\n      this.selectedItems = [];\r\n    }\r\n  }\r\n\r\n  // 更新全選狀態\r\n  updateSelectAllState() {\r\n    if (this.requirementList.length === 0) {\r\n      this.isAllSelected = false;\r\n    } else {\r\n      this.isAllSelected = this.selectedItems.length === this.requirementList.length;\r\n    }\r\n  }\r\n\r\n  // 檢查項目是否被選中\r\n  isItemSelected(item: GetRequirement): boolean {\r\n    return this.selectedItems.some(selected => selected.CRequirementID === item.CRequirementID);\r\n  }\r\n\r\n  // 開啟批次編輯對話框\r\n  openBatchEdit(dialog: TemplateRef<any>) {\r\n    if (this.selectedItems.length === 0) {\r\n      this.message.showErrorMSG('請先選擇要編輯的項目');\r\n      return;\r\n    }\r\n\r\n    this.isBatchEditMode = true;\r\n\r\n    // 初始化批次編輯項目資料\r\n    this.batchEditItems = this.selectedItems.map(item => ({\r\n      CRequirementID: item.CRequirementID,\r\n      CBuildCaseID: item.CBuildCaseID,\r\n      CLocation: item.CLocation,\r\n      CRequirement: item.CRequirement,\r\n      CHouseType: item.CHouseType ? [...item.CHouseType] : [],\r\n      CSort: item.CSort,\r\n      CStatus: item.CStatus,\r\n      CUnitPrice: item.CUnitPrice || 0,\r\n      CUnit: item.CUnit,\r\n      CSpaceId: item.CSpaceId || null,\r\n      CIsShow: item.CIsShow || false,\r\n      CIsSimple: item.CIsSimple || false,\r\n      CRemark: item.CRemark\r\n    }));\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 批次驗證方法\r\n  batchValidation(): string[] {\r\n    const errorMessages: string[] = [];\r\n\r\n    this.batchEditItems.forEach((item, index) => {\r\n      const itemNum = index + 1;\r\n\r\n      // 必填欄位檢核\r\n      if (!item.CBuildCaseID) {\r\n        errorMessages.push(`[項目 ${itemNum}] 建案名稱為必填欄位`);\r\n      }\r\n\r\n      if (!item.CRequirement || item.CRequirement.trim() === '') {\r\n        errorMessages.push(`[項目 ${itemNum}] 工程項目為必填欄位`);\r\n      }\r\n\r\n      if (!item.CHouseType || item.CHouseType.length === 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 類型為必填欄位`);\r\n      }\r\n\r\n      if (item.CSort === null || item.CSort === undefined || item.CSort < 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 排序為必填欄位且不能為負數`);\r\n      }\r\n\r\n      if (item.CStatus === null || item.CStatus === undefined) {\r\n        errorMessages.push(`[項目 ${itemNum}] 狀態為必填欄位`);\r\n      }\r\n\r\n      if (item.CUnitPrice === null || item.CUnitPrice === undefined || item.CUnitPrice < 0) {\r\n        errorMessages.push(`[項目 ${itemNum}] 單價為必填欄位且不能為負數`);\r\n      }\r\n\r\n      if (!item.CUnit || item.CUnit.trim() === '') {\r\n        errorMessages.push(`[項目 ${itemNum}] 單位為必填欄位`);\r\n      }\r\n\r\n      // 長度驗證\r\n      if (item.CLocation && item.CLocation.length > 20) {\r\n        errorMessages.push(`[項目 ${itemNum}] 區域不能超過20個字`);\r\n      }\r\n\r\n      if (item.CRequirement && item.CRequirement.length > 50) {\r\n        errorMessages.push(`[項目 ${itemNum}] 工程項目不能超過50個字`);\r\n      }\r\n\r\n      if (item.CRemark && item.CRemark.length > 100) {\r\n        errorMessages.push(`[項目 ${itemNum}] 備註說明不能超過100個字`);\r\n      }\r\n    });\r\n\r\n    return errorMessages;\r\n  }\r\n\r\n  // 批次保存\r\n  batchSave(ref: any) {\r\n    if (this.batchEditItems.length === 0) {\r\n      this.message.showErrorMSG('沒有要更新的項目');\r\n      return;\r\n    }\r\n\r\n    // 執行批次驗證\r\n    const validationErrors = this.batchValidation();\r\n    if (validationErrors.length > 0) {\r\n      this.message.showErrorMSGs(validationErrors);\r\n      return;\r\n    }\r\n\r\n    // 分離新增項目和更新項目\r\n    const newItems = this.batchEditItems.filter(item => !item.CRequirementID);\r\n    const updateItems = this.batchEditItems.filter(item => item.CRequirementID);\r\n\r\n    // 建立請求陣列\r\n    const requests: Promise<any>[] = [];\r\n\r\n    // 新增項目的請求\r\n    newItems.forEach(item => {\r\n      const newItemData: SaveDataRequirement = {\r\n        CBuildCaseID: item.CBuildCaseID,\r\n        CLocation: item.CLocation,\r\n        CRequirement: item.CRequirement,\r\n        CHouseType: item.CHouseType,\r\n        CSort: item.CSort,\r\n        CStatus: item.CStatus,\r\n        CUnitPrice: item.CUnitPrice,\r\n        CUnit: item.CUnit,\r\n        CSpaceId: item.CSpaceId,\r\n        CIsShow: item.CIsShow,\r\n        CIsSimple: item.CIsSimple,\r\n        CRemark: item.CRemark\r\n      };\r\n\r\n      requests.push(\r\n        this.requirementService.apiRequirementSaveDataPost$Json({\r\n          body: newItemData\r\n        }).toPromise()\r\n      );\r\n    });\r\n\r\n    // 更新項目的請求（如果有的話）\r\n    if (updateItems.length > 0) {\r\n      const updateData: SaveDataRequirement[] = updateItems.map(item => ({\r\n        CRequirementID: item.CRequirementID,\r\n        CBuildCaseID: item.CBuildCaseID,\r\n        CLocation: item.CLocation,\r\n        CRequirement: item.CRequirement,\r\n        CHouseType: item.CHouseType,\r\n        CSort: item.CSort,\r\n        CStatus: item.CStatus,\r\n        CUnitPrice: item.CUnitPrice,\r\n        CUnit: item.CUnit,\r\n        CSpaceId: item.CSpaceId,\r\n        CIsShow: item.CIsShow,\r\n        CIsSimple: item.CIsSimple,\r\n        CRemark: item.CRemark\r\n      }));\r\n\r\n      requests.push(\r\n        this.requirementService.apiRequirementBatchSaveDataPost$Json({\r\n          body: updateData\r\n        }).toPromise()\r\n      );\r\n    }\r\n\r\n    // 執行所有請求\r\n    Promise.all(requests)\r\n      .then(responses => {\r\n        const successCount = responses.filter(res => res?.StatusCode === 0).length;\r\n        const totalItems = this.batchEditItems.length;\r\n\r\n        if (successCount === responses.length) {\r\n          this.message.showSucessMSG(`成功處理 ${totalItems} 個項目 (新增: ${newItems.length}, 更新: ${updateItems.length})`);\r\n        } else {\r\n          this.message.showSucessMSG(`成功處理 ${successCount} 個項目，${responses.length - successCount} 個失敗`);\r\n        }\r\n\r\n        // 清理狀態並重新載入資料\r\n        this.selectedItems = [];\r\n        this.batchEditItems = [];\r\n        this.isBatchEditMode = false;\r\n        this.updateSelectAllState();\r\n        this.getList();\r\n        ref.close();\r\n      })\r\n      .catch(error => {\r\n        console.error('批次保存失敗:', error);\r\n        this.message.showErrorMSG('批次保存時發生錯誤');\r\n      });\r\n  }\r\n\r\n  // 重置批次編輯中的單一項目到原始狀態\r\n  resetBatchEditItem(index: number) {\r\n    const originalItem = this.selectedItems[index];\r\n    if (originalItem) {\r\n      this.batchEditItems[index] = {\r\n        CRequirementID: originalItem.CRequirementID,\r\n        CBuildCaseID: originalItem.CBuildCaseID,\r\n        CLocation: originalItem.CLocation,\r\n        CRequirement: originalItem.CRequirement,\r\n        CHouseType: originalItem.CHouseType ? [...originalItem.CHouseType] : [],\r\n        CSort: originalItem.CSort,\r\n        CStatus: originalItem.CStatus,\r\n        CUnitPrice: originalItem.CUnitPrice || 0,\r\n        CUnit: originalItem.CUnit,\r\n        CSpaceId: originalItem.CSpaceId || null,\r\n        CIsShow: originalItem.CIsShow || false,\r\n        CIsSimple: originalItem.CIsSimple || false,\r\n        CRemark: originalItem.CRemark\r\n      };\r\n    }\r\n  }\r\n\r\n  // 取消批次編輯\r\n  cancelBatchEdit(ref: any) {\r\n    this.isBatchEditMode = false;\r\n    this.batchEditItems = [];\r\n    ref.close();\r\n  }\r\n\r\n  // 備用：如果需要手動建立需求項目的方法\r\n  private batchCreateRequirements(requirements: SaveDataRequirement[]) {\r\n    const batchRequests = requirements.map(requirement =>\r\n      this.requirementService.apiRequirementSaveDataPost$Json({\r\n        body: requirement\r\n      })\r\n    );\r\n\r\n    Promise.all(batchRequests.map(req => req.toPromise()))\r\n      .then(responses => {\r\n        const successCount = responses.filter(res => res?.StatusCode === 0).length;\r\n        this.message.showSucessMSG(`成功建立 ${successCount} 個需求項目`);\r\n        this.getList();\r\n      })\r\n      .catch(error => {\r\n        console.error('批次建立需求失敗:', error);\r\n        this.message.showErrorMSG('批次建立需求時發生錯誤');\r\n      });\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAmCC,SAAS,QAAQ,eAAe;AAErF,SAASC,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/H,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;AAGjE,SAASC,oCAAoC,QAAQ,4FAA4F;AAwB1I,IAAMC,8BAA8B,GAApC,MAAMA,8BAA+B,SAAQZ,aAAa;EAG/Da,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,eAAgC,EAChCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB,EACtBC,4BAA0D;IAElE,KAAK,CAACX,MAAM,CAAC;IAbL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,4BAA4B,GAA5BA,4BAA4B;IAOtC;IACA,KAAAC,yBAAyB,GAAG,EAA0F;IACtH,KAAAC,qBAAqB,GAA8B,EAAE;IAErD;IACA,KAAAC,aAAa,GAA8B,EAAE;IAC7C,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAC,eAAe,GAAqE;MAAEC,UAAU,EAAE;IAAE,CAAE;IAEtG,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAC,SAAS,GAAG,IAAI,CAACpB,UAAU,CAACqB,cAAc,CAAC1B,aAAa,CAAC;IACzD,KAAA2B,KAAK,GAAG,KAAK;IACb,KAAAC,gBAAgB,GAAG,CAAC;IAEpB;IACA,KAAAC,aAAa,GAAqB,EAAE;IACpC,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,KAAK;IACvB;IACA,KAAAC,cAAc,GAAyE,EAAE;IA1BvF,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EA0BSC,QAAQA,CAAA,GAAW;EAE5B;EACAF,oBAAoBA,CAAA;IAClB,IAAI,CAACjB,yBAAyB,CAACoB,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACpB,yBAAyB,CAACqB,OAAO,GAAG,IAAI;IAC7C,IAAI,CAACrB,yBAAyB,CAACsB,SAAS,GAAG,IAAI;IAC/C,IAAI,CAACtB,yBAAyB,CAACuB,YAAY,GAAG,EAAE;IAChD,IAAI,CAACvB,yBAAyB,CAACwB,SAAS,GAAG,EAAE;IAC7C;IACA,IAAI,CAACxB,yBAAyB,CAACK,UAAU,GAAG,IAAI,CAACI,SAAS,CAACgB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACnB,KAAK,CAAC;EACpF;EAEA;EACAoB,iBAAiBA,CAACC,cAAmB;IACnC;IACA,IAAI,IAAI,CAACb,eAAe,IAAI,IAAI,CAACF,aAAa,CAACgB,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMC,cAAc,GAAG,IAAI,CAACf,eAAe,GACvC,iCAAiC,GACjC,gBAAgB,IAAI,CAACF,aAAa,CAACgB,MAAM,YAAY;MAEzD,IAAI,CAACE,OAAO,CAACD,cAAc,CAAC,EAAE;QAC5B;QACAE,UAAU,CAAC,MAAK;UACd,IAAI,CAAChC,yBAAyB,CAACiC,YAAY,GAAG,IAAI,CAACrB,gBAAgB;QACrE,CAAC,EAAE,CAAC,CAAC;QACL;MACF;IACF;IAEA;IACA,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACD,eAAe,GAAG,KAAK;IAE5B;IACA,IAAI,CAACH,gBAAgB,GAAGgB,cAAc;IACtC,IAAI,CAACM,OAAO,EAAE;EAChB;EAEA;EACAC,WAAWA,CAAA;IACT,IAAI,CAAClB,oBAAoB,EAAE;IAC3B;IACA,IAAI,CAACJ,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACD,eAAe,GAAG,KAAK;IAE5B;IACA,IAAI,IAAI,CAACb,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2B,MAAM,GAAG,CAAC,EAAE;MACvDG,UAAU,CAAC,MAAK;QACd,IAAI,CAAChC,yBAAyB,CAACiC,YAAY,GAAG,IAAI,CAAC/B,aAAa,CAAC,CAAC,CAAC,CAACkC,GAAG;QACvE,IAAI,CAACF,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEAG,YAAYA,CAACC,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAACnC,SAAS,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvC,KAAK,IAAIoC,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAACpC,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOiC,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACzD,KAAK,CAAC0D,KAAK,EAAE;IAElB;IACA,IAAI,CAAC1D,KAAK,CAAC2D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC/C,eAAe,CAAC6B,YAAY,CAAC;IAChE,IAAI,CAACzC,KAAK,CAAC2D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/C,eAAe,CAACmB,YAAY,CAAC;IAC9D,IAAI,CAAC/B,KAAK,CAAC2D,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC/C,eAAe,CAACC,UAAU,CAAC;IAC7D,IAAI,CAACb,KAAK,CAAC2D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/C,eAAe,CAACgD,KAAK,CAAC;IACvD,IAAI,CAAC5D,KAAK,CAAC2D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/C,eAAe,CAACgB,OAAO,CAAC;IACzD,IAAI,CAAC5B,KAAK,CAAC2D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/C,eAAe,CAACiD,UAAU,CAAC;IAC5D,IAAI,CAAC7D,KAAK,CAAC2D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/C,eAAe,CAACkD,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAAClD,eAAe,CAACgD,KAAK,KAAK,IAAI,IAAI,IAAI,CAAChD,eAAe,CAACgD,KAAK,KAAKG,SAAS,IAAI,IAAI,CAACnD,eAAe,CAACgD,KAAK,GAAG,CAAC,EAAE;MACrH,IAAI,CAAC5D,KAAK,CAACgE,aAAa,CAACT,IAAI,CAAC,YAAY,CAAC;IAC7C;IAEA,IAAI,IAAI,CAAC3C,eAAe,CAACiD,UAAU,KAAK,IAAI,IAAI,IAAI,CAACjD,eAAe,CAACiD,UAAU,KAAKE,SAAS,IAAI,IAAI,CAACnD,eAAe,CAACiD,UAAU,GAAG,CAAC,EAAE;MACpI,IAAI,CAAC7D,KAAK,CAACgE,aAAa,CAACT,IAAI,CAAC,YAAY,CAAC;IAC7C;IAEA;IACA,IAAI,IAAI,CAAC3C,eAAe,CAACoB,SAAS,IAAI,IAAI,CAACpB,eAAe,CAACoB,SAAS,CAACK,MAAM,GAAG,EAAE,EAAE;MAChF,IAAI,CAACrC,KAAK,CAACgE,aAAa,CAACT,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA,IAAI,IAAI,CAAC3C,eAAe,CAACmB,YAAY,IAAI,IAAI,CAACnB,eAAe,CAACmB,YAAY,CAACM,MAAM,GAAG,EAAE,EAAE;MACtF,IAAI,CAACrC,KAAK,CAACgE,aAAa,CAACT,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAC3C,eAAe,CAACqD,OAAO,IAAI,IAAI,CAACrD,eAAe,CAACqD,OAAO,CAAC5B,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACrC,KAAK,CAACgE,aAAa,CAACT,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEAW,GAAGA,CAACC,MAAwB;IAC1B,IAAI,CAAChD,KAAK,GAAG,IAAI;IACjB,IAAI,CAACP,eAAe,GAAG;MAAEC,UAAU,EAAE,EAAE;MAAEgB,OAAO,EAAE,KAAK;MAAEC,SAAS,EAAE;IAAK,CAAE;IAC3E,IAAI,CAAClB,eAAe,CAACgB,OAAO,GAAG,CAAC;IAChC,IAAI,CAAChB,eAAe,CAACiD,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAACzC,gBAAgB,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACR,eAAe,CAAC6B,YAAY,GAAG,IAAI,CAACrB,gBAAgB;IAC3D,CAAC,MAAM,IAAI,IAAI,CAACV,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2B,MAAM,GAAG,CAAC,EAAE;MAC9D,IAAI,CAACzB,eAAe,CAAC6B,YAAY,GAAG,IAAI,CAAC/B,aAAa,CAAC,CAAC,CAAC,CAACkC,GAAG;IAC/D;IAEA,IAAI,CAAC9C,aAAa,CAACsE,IAAI,CAACD,MAAM,CAAC;EACjC;EAEME,MAAMA,CAACC,IAAoB,EAAEH,MAAwB;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAAC9D,qBAAqB,CAACgE,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAACpD,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMoD,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAACzE,aAAa,CAACsE,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAG,IAAIA,CAACC,GAAQ;IACX,IAAI,CAACtB,UAAU,EAAE;IACjB,IAAI,IAAI,CAACzD,KAAK,CAACgE,aAAa,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACtC,OAAO,CAACiF,aAAa,CAAC,IAAI,CAAChF,KAAK,CAACgE,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC9D,kBAAkB,CAAC+E,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAACtE;KACZ,CAAC,CAACuE,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtF,OAAO,CAACuF,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC5C,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAC3C,OAAO,CAACwF,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEAC,QAAQA,CAACpB,IAAoB;IAC3B,IAAI,CAAC1D,eAAe,CAAC6D,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAACtD,KAAK,GAAG,KAAK;IAClB,IAAIwE,MAAM,CAACpD,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACqD,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAAC1F,kBAAkB,CAAC2F,iCAAiC,CAAC;MACxDX,IAAI,EAAE;QACJT,cAAc,EAAE,IAAI,CAAC7D,eAAe,CAAC6D;;KAExC,CAAC,CAACU,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACrF,OAAO,CAACuF,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAAC5C,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAhB,gBAAgBA,CAAA;IACd,IAAI,CAACzB,gBAAgB,CAAC6F,qCAAqC,CAAC;MAAEZ,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEa,IAAI,CAAChH,kBAAkB,CAAC,IAAI,CAACuB,UAAU,CAAC,CAAC,CAAC6E,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAC1E,aAAa,GAAG0E,GAAG,CAACY,OAAQ;MACjC;MACA,IAAI,IAAI,CAACtF,aAAa,CAAC2B,MAAM,GAAG,CAAC,EAAE;QACjC,IAAI,CAAC7B,yBAAyB,CAACiC,YAAY,GAAG,IAAI,CAAC/B,aAAa,CAAC,CAAC,CAAC,CAACkC,GAAG;QACvE,IAAI,CAACF,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAAClC,yBAAyB,CAACyF,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAAC1F,yBAAyB,CAAC2F,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAACzF,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAAC0F,YAAY,GAAG,CAAC;IAErB;IACA,IAAI,IAAI,CAAC7F,yBAAyB,CAACiC,YAAY,IAAI,IAAI,CAACjC,yBAAyB,CAACiC,YAAY,IAAI,CAAC,EAAE;MACnG,IAAI,CAACrB,gBAAgB,GAAG,IAAI,CAACZ,yBAAyB,CAACiC,YAAY;IACrE;IAEA,IAAI,CAACvC,kBAAkB,CAACoG,8BAA8B,CAAC;MAAEpB,IAAI,EAAE,IAAI,CAAC1E;IAAyB,CAAE,CAAC,CAC7FuF,IAAI,EAAE,CACNZ,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACY,OAAO,EAAE;UACf,IAAI,CAACrF,eAAe,GAAGyE,GAAG,CAACY,OAAO;UAClC,IAAI,CAACK,YAAY,GAAGjB,GAAG,CAACmB,UAAW;UAEnC;UACA,MAAMC,qBAAqB,GAAG,IAAI,CAACnF,aAAa,CAACgB,MAAM;UACvD,IAAI,CAAChB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACoF,MAAM,CAACC,YAAY,IACzD,IAAI,CAAC/F,eAAe,CAACgG,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACnC,cAAc,KAAKiC,YAAY,CAACjC,cAAc,CAAC,CAC/F;UAED;UACA,IAAI+B,qBAAqB,KAAK,IAAI,CAACnF,aAAa,CAACgB,MAAM,IAAI,IAAI,CAAChB,aAAa,CAACgB,MAAM,KAAK,CAAC,EAAE;YAC1F,IAAI,CAACb,cAAc,GAAG,EAAE;YACxB,IAAI,CAACD,eAAe,GAAG,KAAK;UAC9B;UAEA;UACA,IAAI,CAACsF,oBAAoB,EAAE;QAC7B;MACF;IACF,CAAC,CAAC;EACN;EAEAnC,OAAOA,CAAA;IACL,IAAI,CAACxE,kBAAkB,CAAC4G,8BAA8B,CAAC;MAAE5B,IAAI,EAAE,IAAI,CAACzE;IAAqB,CAAE,CAAC,CACzFsF,IAAI,EAAE,CACNZ,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACY,OAAO,EAAE;UACf,IAAI,CAACpF,eAAe,GAAG;YAAEC,UAAU,EAAE,EAAE;YAAEgB,OAAO,EAAE,KAAK;YAAEC,SAAS,EAAE;UAAK,CAAE;UAC3E,IAAI,CAAClB,eAAe,CAAC6B,YAAY,GAAG2C,GAAG,CAACY,OAAO,CAACvD,YAAY;UAC5D,IAAI,CAAC7B,eAAe,CAACoB,SAAS,GAAGoD,GAAG,CAACY,OAAO,CAAChE,SAAS;UACtD,IAAI,CAACpB,eAAe,CAACC,UAAU,GAAGuE,GAAG,CAACY,OAAO,CAACnF,UAAU,GAAIkC,KAAK,CAACC,OAAO,CAACoC,GAAG,CAACY,OAAO,CAACnF,UAAU,CAAC,GAAGuE,GAAG,CAACY,OAAO,CAACnF,UAAU,GAAG,CAACuE,GAAG,CAACY,OAAO,CAACnF,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACD,eAAe,CAACqD,OAAO,GAAGmB,GAAG,CAACY,OAAO,CAAC/B,OAAO;UAClD,IAAI,CAACrD,eAAe,CAACmB,YAAY,GAAGqD,GAAG,CAACY,OAAO,CAACjE,YAAY;UAC5D,IAAI,CAACnB,eAAe,CAAC6D,cAAc,GAAGW,GAAG,CAACY,OAAO,CAACvB,cAAc;UAChE,IAAI,CAAC7D,eAAe,CAACgD,KAAK,GAAGwB,GAAG,CAACY,OAAO,CAACpC,KAAK;UAC9C,IAAI,CAAChD,eAAe,CAACgB,OAAO,GAAGwD,GAAG,CAACY,OAAO,CAACpE,OAAO;UAClD,IAAI,CAAChB,eAAe,CAACiD,UAAU,GAAGuB,GAAG,CAACY,OAAO,CAACnC,UAAU,IAAI,CAAC;UAC7D,IAAI,CAACjD,eAAe,CAACkD,KAAK,GAAGsB,GAAG,CAACY,OAAO,CAAClC,KAAK;UAC9C,IAAI,CAAClD,eAAe,CAACmG,QAAQ,GAAG3B,GAAG,CAACY,OAAO,CAACe,QAAQ,IAAI,IAAI;UAC5D,IAAI,CAACnG,eAAe,CAACiB,OAAO,GAAGuD,GAAG,CAACY,OAAO,CAACnE,OAAO,IAAI,KAAK;UAC3D,IAAI,CAACjB,eAAe,CAACkB,SAAS,GAAGsD,GAAG,CAACY,OAAO,CAAClE,SAAS,IAAI,KAAK;QACjE;MACF;IACF,CAAC,CAAC;EACN;EAEAkF,iBAAiBA,CAACjG,KAAa,EAAEkG,OAAY;IAC3CrC,OAAO,CAACC,GAAG,CAACoC,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAACrG,eAAe,CAACC,UAAU,EAAEqG,QAAQ,CAACnG,KAAK,CAAC,EAAE;QACrD,IAAI,CAACH,eAAe,CAACC,UAAU,EAAE0C,IAAI,CAACxC,KAAK,CAAC;MAC9C;MACA6D,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjE,eAAe,CAACC,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACD,eAAe,CAACC,UAAU,GAAG,IAAI,CAACD,eAAe,CAACC,UAAU,EAAE4F,MAAM,CAACU,CAAC,IAAIA,CAAC,KAAKpG,KAAK,CAAC;IAC7F;EACF;EAEAqG,cAAcA,CAAC9C,IAAS;IACtB,OAAOA,IAAI,CAACzC,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAEAwF,gBAAgBA,CAAC/C,IAAS;IACxB,OAAOA,IAAI,CAACxC,SAAS,GAAG,GAAG,GAAG,GAAG;EACnC;EAEA;EACAwF,sBAAsBA,CAACC,MAA2B;IAChD3C,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE0C,MAAM,CAAC;IAEhC;IACA,IAAI,CAACC,2BAA2B,CAACD,MAAM,CAAClG,aAAa,EAAEkG,MAAM,CAACE,eAAe,CAAC;EAChF;EAEAC,eAAeA,CAACC,YAAoB;IAClC,IAAI,CAAC5H,OAAO,CAACwF,YAAY,CAACoC,YAAY,CAAC;EACzC;EAEA;EACQH,2BAA2BA,CAACI,iBAAwB,EAAEH,eAAmC;IAC/F,IAAI,CAAC,IAAI,CAACjH,yBAAyB,CAACiC,YAAY,EAAE;MAChD,IAAI,CAAC1C,OAAO,CAACwF,YAAY,CAAC,WAAW,CAAC;MACtC;IACF;IAEA;IACA,IAAI,CAAClE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,aAAa,GAAG,KAAK;IAE1B;IACA,MAAMuG,OAAO,GAAG,IAAI,CAAClH,eAAe,CAAC0B,MAAM,GAAG,CAAC,GAC3CyF,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAACpH,eAAe,CAACsB,GAAG,CAAC+F,IAAI,IAAIA,IAAI,CAACpE,KAAK,IAAI,CAAC,CAAC,CAAC,GAC9D,CAAC;IAEL,IAAIqE,gBAAgB,GAAG,CAAC;IACxB,MAAMC,iBAAiB,GAAU,EAAE;IAEnC;IACAN,iBAAiB,CAAC1E,OAAO,CAACiF,QAAQ,IAAG;MACnC,MAAMC,OAAO,GAAGX,eAAe,CAACY,GAAG,CAACF,QAAQ,CAACG,WAAW,CAAC,IAAI,EAAE;MAE/D,IAAIF,OAAO,IAAIA,OAAO,CAAC/F,MAAM,GAAG,CAAC,EAAE;QACjC;QACA+F,OAAO,CAAClF,OAAO,CAAC,CAACqF,MAAM,EAAEC,WAAW,KAAI;UACtC,MAAMC,aAAa,GAAG;YACpBhE,cAAc,EAAEV,SAAS;YAAE;YAC3BtB,YAAY,EAAE,IAAI,CAACjC,yBAAyB,CAACiC,YAAY;YACzDT,SAAS,EAAEuG,MAAM,CAACvG,SAAS,IAAImG,QAAQ,CAACnG,SAAS,IAAI,EAAE;YAAE;YACzDD,YAAY,EAAEwG,MAAM,CAACG,KAAK,IAAI,GAAGP,QAAQ,CAACQ,aAAa,SAASH,WAAW,GAAG,CAAC,EAAE;YAAE;YACnF3H,UAAU,EAAEsH,QAAQ,CAACtH,UAAU,IAAI,EAAE;YAAE;YACvC+C,KAAK,EAAEiE,OAAO,GAAGI,gBAAgB,GAAG,CAAC;YAAE;YACvCrG,OAAO,EAAE,CAAC;YAAE;YACZiC,UAAU,EAAG0E,MAAc,CAAC1E,UAAU,IAAKsE,QAAgB,CAACtE,UAAU,IAAI,CAAC;YAAE;YAC7EC,KAAK,EAAGyE,MAAc,CAACzE,KAAK,IAAKqE,QAAgB,CAACrE,KAAK,IAAI,GAAG;YAAE;YAChEiD,QAAQ,EAAEwB,MAAM,CAACK,UAAU,IAAKT,QAAgB,CAACU,SAAS,IAAI,IAAI;YAAE;YACpEhH,OAAO,EAAEsG,QAAQ,CAACtG,OAAO,KAAKkC,SAAS,GAAGoE,QAAQ,CAACtG,OAAO,GAAG,KAAK;YAAE;YACpEC,SAAS,EAAEqG,QAAQ,CAACrG,SAAS,KAAKiC,SAAS,GAAGoE,QAAQ,CAACrG,SAAS,GAAG,KAAK;YAAE;YAC1EmC,OAAO,EAAGsE,MAAc,CAACtE,OAAO,IAAI,OAAOkE,QAAQ,CAACQ,aAAa,UAAUJ,MAAM,CAACG,KAAK,KAAK,CAAC;WAC9F;UAEDR,iBAAiB,CAAC3E,IAAI,CAACkF,aAAa,CAAC;UACrCR,gBAAgB,EAAE;QACpB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMQ,aAAa,GAAG;UACpBhE,cAAc,EAAEV,SAAS;UAAE;UAC3BtB,YAAY,EAAE,IAAI,CAACjC,yBAAyB,CAACiC,YAAY;UACzDT,SAAS,EAAEmG,QAAQ,CAACnG,SAAS,IAAI,EAAE;UAAE;UACrCD,YAAY,EAAEoG,QAAQ,CAACQ,aAAa,IAAI,QAAQV,gBAAgB,GAAG,CAAC,EAAE;UAAE;UACxEpH,UAAU,EAAEsH,QAAQ,CAACtH,UAAU,IAAI,EAAE;UAAE;UACvC+C,KAAK,EAAEiE,OAAO,GAAGI,gBAAgB,GAAG,CAAC;UAAE;UACvCrG,OAAO,EAAE,CAAC;UAAE;UACZiC,UAAU,EAAGsE,QAAgB,CAACtE,UAAU,IAAI,CAAC;UAAE;UAC/CC,KAAK,EAAGqE,QAAgB,CAACrE,KAAK,IAAI,GAAG;UAAE;UACvCiD,QAAQ,EAAGoB,QAAgB,CAACU,SAAS,IAAI,IAAI;UAAE;UAC/ChH,OAAO,EAAEsG,QAAQ,CAACtG,OAAO,KAAKkC,SAAS,GAAGoE,QAAQ,CAACtG,OAAO,GAAG,KAAK;UAAE;UACpEC,SAAS,EAAEqG,QAAQ,CAACrG,SAAS,KAAKiC,SAAS,GAAGoE,QAAQ,CAACrG,SAAS,GAAG,KAAK;UAAE;UAC1EmC,OAAO,EAAGkE,QAAgB,CAAClE,OAAO,IAAI,OAAOkE,QAAQ,CAACQ,aAAa,KAAK,CAAC;SAC1E;QAEDT,iBAAiB,CAAC3E,IAAI,CAACkF,aAAa,CAAC;QACrCR,gBAAgB,EAAE;MACpB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACzG,cAAc,GAAG0G,iBAAiB;IAEvC;IACA,IAAI,CAAC3G,eAAe,GAAG,IAAI;IAE3B;IACA,MAAMuH,gBAAgB,GAAGlB,iBAAiB,CAACmB,MAAM,CAAC,CAACC,GAAG,EAAEb,QAAQ,KAAI;MAClE,MAAMC,OAAO,GAAGX,eAAe,CAACY,GAAG,CAACF,QAAQ,CAACG,WAAW,CAAC,IAAI,EAAE;MAC/D,OAAOU,GAAG,IAAIZ,OAAO,CAAC/F,MAAM,IAAI,CAAC,CAAC;IACpC,CAAC,EAAE,CAAC,CAAC;IAEL;IACA,MAAMtC,OAAO,GAAG,OAAO6H,iBAAiB,CAACvF,MAAM,UAAUyG,gBAAgB,kBAAkB,GACzF,oBAAoB,GACpB,eAAe,GACf,aAAa,GACb,WAAW,GACX,mBAAmB,GACnB,yBAAyB;IAE3BG,KAAK,CAAClJ,OAAO,CAAC;IAEd;IACAyC,UAAU,CAAC,MAAK;MACd,IAAI,CAAC1C,aAAa,CAACsE,IAAI,CAAC,IAAI,CAAC8E,eAAe,CAAC;IAC/C,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAE;EAEH;EACAC,mBAAmBA,CAACnB,IAAoB;IACtC,MAAMoB,KAAK,GAAG,IAAI,CAAC/H,aAAa,CAACgI,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAAC7E,cAAc,KAAKuD,IAAI,CAACvD,cAAc,CAAC;IACvG,IAAI2E,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC/H,aAAa,CAACkI,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,IAAI,CAAC/H,aAAa,CAACkC,IAAI,CAACyE,IAAI,CAAC;IAC/B;IACA,IAAI,CAACnB,oBAAoB,EAAE;EAC7B;EAEA;EACA2C,eAAeA,CAACC,QAAiB;IAC/B,IAAI,IAAI,CAAC9I,eAAe,CAAC0B,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAChB,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B;IACF;IAEA;IACA,IAAI,CAACA,aAAa,GAAGmI,QAAQ;IAE7B;IACA,IAAI,IAAI,CAACnI,aAAa,EAAE;MACtB,IAAI,CAACD,aAAa,GAAG,CAAC,GAAG,IAAI,CAACV,eAAe,CAAC;IAChD,CAAC,MAAM;MACL,IAAI,CAACU,aAAa,GAAG,EAAE;IACzB;EACF;EAEA;EACAwF,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAClG,eAAe,CAAC0B,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACf,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACA,aAAa,GAAG,IAAI,CAACD,aAAa,CAACgB,MAAM,KAAK,IAAI,CAAC1B,eAAe,CAAC0B,MAAM;IAChF;EACF;EAEA;EACAqH,cAAcA,CAAC1B,IAAoB;IACjC,OAAO,IAAI,CAAC3G,aAAa,CAACsF,IAAI,CAAC2C,QAAQ,IAAIA,QAAQ,CAAC7E,cAAc,KAAKuD,IAAI,CAACvD,cAAc,CAAC;EAC7F;EAEA;EACAkF,aAAaA,CAACxF,MAAwB;IACpC,IAAI,IAAI,CAAC9C,aAAa,CAACgB,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAACtC,OAAO,CAACwF,YAAY,CAAC,YAAY,CAAC;MACvC;IACF;IAEA,IAAI,CAAChE,eAAe,GAAG,IAAI;IAE3B;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACH,aAAa,CAACY,GAAG,CAAC+F,IAAI,KAAK;MACpDvD,cAAc,EAAEuD,IAAI,CAACvD,cAAc;MACnChC,YAAY,EAAEuF,IAAI,CAACvF,YAAY;MAC/BT,SAAS,EAAEgG,IAAI,CAAChG,SAAS;MACzBD,YAAY,EAAEiG,IAAI,CAACjG,YAAY;MAC/BlB,UAAU,EAAEmH,IAAI,CAACnH,UAAU,GAAG,CAAC,GAAGmH,IAAI,CAACnH,UAAU,CAAC,GAAG,EAAE;MACvD+C,KAAK,EAAEoE,IAAI,CAACpE,KAAK;MACjBhC,OAAO,EAAEoG,IAAI,CAACpG,OAAO;MACrBiC,UAAU,EAAEmE,IAAI,CAACnE,UAAU,IAAI,CAAC;MAChCC,KAAK,EAAEkE,IAAI,CAAClE,KAAK;MACjBiD,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ,IAAI,IAAI;MAC/BlF,OAAO,EAAEmG,IAAI,CAACnG,OAAO,IAAI,KAAK;MAC9BC,SAAS,EAAEkG,IAAI,CAAClG,SAAS,IAAI,KAAK;MAClCmC,OAAO,EAAE+D,IAAI,CAAC/D;KACf,CAAC,CAAC;IAEH,IAAI,CAACnE,aAAa,CAACsE,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACAyF,eAAeA,CAAA;IACb,MAAM5F,aAAa,GAAa,EAAE;IAElC,IAAI,CAACxC,cAAc,CAAC0B,OAAO,CAAC,CAAC8E,IAAI,EAAEoB,KAAK,KAAI;MAC1C,MAAMS,OAAO,GAAGT,KAAK,GAAG,CAAC;MAEzB;MACA,IAAI,CAACpB,IAAI,CAACvF,YAAY,EAAE;QACtBuB,aAAa,CAACT,IAAI,CAAC,OAAOsG,OAAO,aAAa,CAAC;MACjD;MAEA,IAAI,CAAC7B,IAAI,CAACjG,YAAY,IAAIiG,IAAI,CAACjG,YAAY,CAAC+H,IAAI,EAAE,KAAK,EAAE,EAAE;QACzD9F,aAAa,CAACT,IAAI,CAAC,OAAOsG,OAAO,aAAa,CAAC;MACjD;MAEA,IAAI,CAAC7B,IAAI,CAACnH,UAAU,IAAImH,IAAI,CAACnH,UAAU,CAACwB,MAAM,KAAK,CAAC,EAAE;QACpD2B,aAAa,CAACT,IAAI,CAAC,OAAOsG,OAAO,WAAW,CAAC;MAC/C;MAEA,IAAI7B,IAAI,CAACpE,KAAK,KAAK,IAAI,IAAIoE,IAAI,CAACpE,KAAK,KAAKG,SAAS,IAAIiE,IAAI,CAACpE,KAAK,GAAG,CAAC,EAAE;QACrEI,aAAa,CAACT,IAAI,CAAC,OAAOsG,OAAO,iBAAiB,CAAC;MACrD;MAEA,IAAI7B,IAAI,CAACpG,OAAO,KAAK,IAAI,IAAIoG,IAAI,CAACpG,OAAO,KAAKmC,SAAS,EAAE;QACvDC,aAAa,CAACT,IAAI,CAAC,OAAOsG,OAAO,WAAW,CAAC;MAC/C;MAEA,IAAI7B,IAAI,CAACnE,UAAU,KAAK,IAAI,IAAImE,IAAI,CAACnE,UAAU,KAAKE,SAAS,IAAIiE,IAAI,CAACnE,UAAU,GAAG,CAAC,EAAE;QACpFG,aAAa,CAACT,IAAI,CAAC,OAAOsG,OAAO,iBAAiB,CAAC;MACrD;MAEA,IAAI,CAAC7B,IAAI,CAAClE,KAAK,IAAIkE,IAAI,CAAClE,KAAK,CAACgG,IAAI,EAAE,KAAK,EAAE,EAAE;QAC3C9F,aAAa,CAACT,IAAI,CAAC,OAAOsG,OAAO,WAAW,CAAC;MAC/C;MAEA;MACA,IAAI7B,IAAI,CAAChG,SAAS,IAAIgG,IAAI,CAAChG,SAAS,CAACK,MAAM,GAAG,EAAE,EAAE;QAChD2B,aAAa,CAACT,IAAI,CAAC,OAAOsG,OAAO,cAAc,CAAC;MAClD;MAEA,IAAI7B,IAAI,CAACjG,YAAY,IAAIiG,IAAI,CAACjG,YAAY,CAACM,MAAM,GAAG,EAAE,EAAE;QACtD2B,aAAa,CAACT,IAAI,CAAC,OAAOsG,OAAO,gBAAgB,CAAC;MACpD;MAEA,IAAI7B,IAAI,CAAC/D,OAAO,IAAI+D,IAAI,CAAC/D,OAAO,CAAC5B,MAAM,GAAG,GAAG,EAAE;QAC7C2B,aAAa,CAACT,IAAI,CAAC,OAAOsG,OAAO,iBAAiB,CAAC;MACrD;IACF,CAAC,CAAC;IAEF,OAAO7F,aAAa;EACtB;EAEA;EACA+F,SAASA,CAAChF,GAAQ;IAChB,IAAI,IAAI,CAACvD,cAAc,CAACa,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAACtC,OAAO,CAACwF,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;IAEA;IACA,MAAMyE,gBAAgB,GAAG,IAAI,CAACJ,eAAe,EAAE;IAC/C,IAAII,gBAAgB,CAAC3H,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACtC,OAAO,CAACiF,aAAa,CAACgF,gBAAgB,CAAC;MAC5C;IACF;IAEA;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACzI,cAAc,CAACiF,MAAM,CAACuB,IAAI,IAAI,CAACA,IAAI,CAACvD,cAAc,CAAC;IACzE,MAAMyF,WAAW,GAAG,IAAI,CAAC1I,cAAc,CAACiF,MAAM,CAACuB,IAAI,IAAIA,IAAI,CAACvD,cAAc,CAAC;IAE3E;IACA,MAAM0F,QAAQ,GAAmB,EAAE;IAEnC;IACAF,QAAQ,CAAC/G,OAAO,CAAC8E,IAAI,IAAG;MACtB,MAAMoC,WAAW,GAAwB;QACvC3H,YAAY,EAAEuF,IAAI,CAACvF,YAAY;QAC/BT,SAAS,EAAEgG,IAAI,CAAChG,SAAS;QACzBD,YAAY,EAAEiG,IAAI,CAACjG,YAAY;QAC/BlB,UAAU,EAAEmH,IAAI,CAACnH,UAAU;QAC3B+C,KAAK,EAAEoE,IAAI,CAACpE,KAAK;QACjBhC,OAAO,EAAEoG,IAAI,CAACpG,OAAO;QACrBiC,UAAU,EAAEmE,IAAI,CAACnE,UAAU;QAC3BC,KAAK,EAAEkE,IAAI,CAAClE,KAAK;QACjBiD,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ;QACvBlF,OAAO,EAAEmG,IAAI,CAACnG,OAAO;QACrBC,SAAS,EAAEkG,IAAI,CAAClG,SAAS;QACzBmC,OAAO,EAAE+D,IAAI,CAAC/D;OACf;MAEDkG,QAAQ,CAAC5G,IAAI,CACX,IAAI,CAACrD,kBAAkB,CAAC+E,+BAA+B,CAAC;QACtDC,IAAI,EAAEkF;OACP,CAAC,CAACC,SAAS,EAAE,CACf;IACH,CAAC,CAAC;IAEF;IACA,IAAIH,WAAW,CAAC7H,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAMiI,UAAU,GAA0BJ,WAAW,CAACjI,GAAG,CAAC+F,IAAI,KAAK;QACjEvD,cAAc,EAAEuD,IAAI,CAACvD,cAAc;QACnChC,YAAY,EAAEuF,IAAI,CAACvF,YAAY;QAC/BT,SAAS,EAAEgG,IAAI,CAAChG,SAAS;QACzBD,YAAY,EAAEiG,IAAI,CAACjG,YAAY;QAC/BlB,UAAU,EAAEmH,IAAI,CAACnH,UAAU;QAC3B+C,KAAK,EAAEoE,IAAI,CAACpE,KAAK;QACjBhC,OAAO,EAAEoG,IAAI,CAACpG,OAAO;QACrBiC,UAAU,EAAEmE,IAAI,CAACnE,UAAU;QAC3BC,KAAK,EAAEkE,IAAI,CAAClE,KAAK;QACjBiD,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ;QACvBlF,OAAO,EAAEmG,IAAI,CAACnG,OAAO;QACrBC,SAAS,EAAEkG,IAAI,CAAClG,SAAS;QACzBmC,OAAO,EAAE+D,IAAI,CAAC/D;OACf,CAAC,CAAC;MAEHkG,QAAQ,CAAC5G,IAAI,CACX,IAAI,CAACrD,kBAAkB,CAACqK,oCAAoC,CAAC;QAC3DrF,IAAI,EAAEoF;OACP,CAAC,CAACD,SAAS,EAAE,CACf;IACH;IAEA;IACAG,OAAO,CAACC,GAAG,CAACN,QAAQ,CAAC,CAClBO,IAAI,CAACC,SAAS,IAAG;MAChB,MAAMC,YAAY,GAAGD,SAAS,CAAClE,MAAM,CAACrB,GAAG,IAAIA,GAAG,EAAEC,UAAU,KAAK,CAAC,CAAC,CAAChD,MAAM;MAC1E,MAAMwI,UAAU,GAAG,IAAI,CAACrJ,cAAc,CAACa,MAAM;MAE7C,IAAIuI,YAAY,KAAKD,SAAS,CAACtI,MAAM,EAAE;QACrC,IAAI,CAACtC,OAAO,CAACuF,aAAa,CAAC,QAAQuF,UAAU,aAAaZ,QAAQ,CAAC5H,MAAM,SAAS6H,WAAW,CAAC7H,MAAM,GAAG,CAAC;MAC1G,CAAC,MAAM;QACL,IAAI,CAACtC,OAAO,CAACuF,aAAa,CAAC,QAAQsF,YAAY,QAAQD,SAAS,CAACtI,MAAM,GAAGuI,YAAY,MAAM,CAAC;MAC/F;MAEA;MACA,IAAI,CAACvJ,aAAa,GAAG,EAAE;MACvB,IAAI,CAACG,cAAc,GAAG,EAAE;MACxB,IAAI,CAACD,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACsF,oBAAoB,EAAE;MAC3B,IAAI,CAACnE,OAAO,EAAE;MACdqC,GAAG,CAACU,KAAK,EAAE;IACb,CAAC,CAAC,CACDqF,KAAK,CAACnG,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,IAAI,CAAC5E,OAAO,CAACwF,YAAY,CAAC,WAAW,CAAC;IACxC,CAAC,CAAC;EACN;EAEA;EACAwF,kBAAkBA,CAAC3B,KAAa;IAC9B,MAAM4B,YAAY,GAAG,IAAI,CAAC3J,aAAa,CAAC+H,KAAK,CAAC;IAC9C,IAAI4B,YAAY,EAAE;MAChB,IAAI,CAACxJ,cAAc,CAAC4H,KAAK,CAAC,GAAG;QAC3B3E,cAAc,EAAEuG,YAAY,CAACvG,cAAc;QAC3ChC,YAAY,EAAEuI,YAAY,CAACvI,YAAY;QACvCT,SAAS,EAAEgJ,YAAY,CAAChJ,SAAS;QACjCD,YAAY,EAAEiJ,YAAY,CAACjJ,YAAY;QACvClB,UAAU,EAAEmK,YAAY,CAACnK,UAAU,GAAG,CAAC,GAAGmK,YAAY,CAACnK,UAAU,CAAC,GAAG,EAAE;QACvE+C,KAAK,EAAEoH,YAAY,CAACpH,KAAK;QACzBhC,OAAO,EAAEoJ,YAAY,CAACpJ,OAAO;QAC7BiC,UAAU,EAAEmH,YAAY,CAACnH,UAAU,IAAI,CAAC;QACxCC,KAAK,EAAEkH,YAAY,CAAClH,KAAK;QACzBiD,QAAQ,EAAEiE,YAAY,CAACjE,QAAQ,IAAI,IAAI;QACvClF,OAAO,EAAEmJ,YAAY,CAACnJ,OAAO,IAAI,KAAK;QACtCC,SAAS,EAAEkJ,YAAY,CAAClJ,SAAS,IAAI,KAAK;QAC1CmC,OAAO,EAAE+G,YAAY,CAAC/G;OACvB;IACH;EACF;EAEA;EACAgH,eAAeA,CAAClG,GAAQ;IACtB,IAAI,CAACxD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,cAAc,GAAG,EAAE;IACxBuD,GAAG,CAACU,KAAK,EAAE;EACb;EAEA;EACQyF,uBAAuBA,CAACC,YAAmC;IACjE,MAAMC,aAAa,GAAGD,YAAY,CAAClJ,GAAG,CAACoJ,WAAW,IAChD,IAAI,CAACnL,kBAAkB,CAAC+E,+BAA+B,CAAC;MACtDC,IAAI,EAAEmG;KACP,CAAC,CACH;IAEDb,OAAO,CAACC,GAAG,CAACW,aAAa,CAACnJ,GAAG,CAACqJ,GAAG,IAAIA,GAAG,CAACjB,SAAS,EAAE,CAAC,CAAC,CACnDK,IAAI,CAACC,SAAS,IAAG;MAChB,MAAMC,YAAY,GAAGD,SAAS,CAAClE,MAAM,CAACrB,GAAG,IAAIA,GAAG,EAAEC,UAAU,KAAK,CAAC,CAAC,CAAChD,MAAM;MAC1E,IAAI,CAACtC,OAAO,CAACuF,aAAa,CAAC,QAAQsF,YAAY,QAAQ,CAAC;MACxD,IAAI,CAAClI,OAAO,EAAE;IAChB,CAAC,CAAC,CACDoI,KAAK,CAACnG,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAI,CAAC5E,OAAO,CAACwF,YAAY,CAAC,aAAa,CAAC;IAC1C,CAAC,CAAC;EACN;CACD;AAhsBkDgG,UAAA,EAAhD/M,SAAS,CAAC,iBAAiB,EAAE;EAAEgN,MAAM,EAAE;AAAK,CAAE,CAAC,C,sEAAoC;AADzE9L,8BAA8B,GAAA6L,UAAA,EAtB1ChN,SAAS,CAAC;EACTkN,QAAQ,EAAE,4BAA4B;EACtCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPlN,YAAY,EACZO,mBAAmB,EACnBL,aAAa,EACbM,WAAW,EACXJ,cAAc,EACdD,cAAc,EACdO,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVX,gBAAgB,EAChBY,kBAAkB,EAClBC,oBAAoB,EACpBE,oCAAoC,CACrC;EACDmM,WAAW,EAAE,yCAAyC;EACtDC,QAAQ,EAAE;CACX,CAAC,C,EACWnM,8BAA8B,CAisB1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}