{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { KpiCardsComponent } from './components/kpi-cards/kpi-cards.component';\nimport { ProgressChartComponent } from './components/progress-chart/progress-chart.component';\nimport { PaymentChartComponent } from './components/payment-chart/payment-chart.component';\nimport { QuotationTrendsComponent } from './components/quotation-trends/quotation-trends.component';\nimport { HeatmapChartComponent } from './components/heatmap-chart/heatmap-chart.component';\nimport { BuildCaseSelectorComponent } from './components/build-case-selector/build-case-selector.component';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport * as i0 from \"@angular/core\";\nexport class HomeComponent {\n  constructor() {\n    this.selectedBuildCase = 'all';\n  }\n  ngOnInit() {}\n  onBuildCaseChange(buildCaseId) {\n    this.selectedBuildCase = buildCaseId;\n    // 通知所有圖表組件更新資料\n    if (this.kpiCardsComponent) {\n      this.kpiCardsComponent.loadKpiData(buildCaseId);\n    }\n    if (this.progressChartComponent) {\n      this.progressChartComponent.loadProgressData(buildCaseId);\n    }\n    if (this.paymentChartComponent) {\n      this.paymentChartComponent.loadPaymentData(buildCaseId);\n    }\n    if (this.quotationTrendsComponent) {\n      this.quotationTrendsComponent.loadQuotationData(buildCaseId);\n    }\n    if (this.heatmapChartComponent) {\n      this.heatmapChartComponent.loadHeatmapData(buildCaseId);\n    }\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"ngx-home\"]],\n      viewQuery: function HomeComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(KpiCardsComponent, 5);\n          i0.ɵɵviewQuery(ProgressChartComponent, 5);\n          i0.ɵɵviewQuery(PaymentChartComponent, 5);\n          i0.ɵɵviewQuery(QuotationTrendsComponent, 5);\n          i0.ɵɵviewQuery(HeatmapChartComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.kpiCardsComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.progressChartComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paymentChartComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.quotationTrendsComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.heatmapChartComponent = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 22,\n      vars: 0,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"dashboard-subtitle\"], [1, \"dashboard-controls\"], [3, \"buildCaseChange\"], [1, \"dashboard-section\"], [1, \"chart-grid\"], [1, \"chart-item\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"\\u71DF\\u904B\\u5100\\u8868\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 2);\n          i0.ɵɵtext(5, \"\\u5EFA\\u6848\\u9032\\u5EA6\\u8207\\u71DF\\u904B\\u72C0\\u614B\\u7E3D\\u89BD\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"section\", 3)(7, \"app-build-case-selector\", 4);\n          i0.ɵɵlistener(\"buildCaseChange\", function HomeComponent_Template_app_build_case_selector_buildCaseChange_7_listener($event) {\n            return ctx.onBuildCaseChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"section\", 5);\n          i0.ɵɵelement(9, \"app-kpi-cards\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"section\", 5)(11, \"div\", 6)(12, \"div\", 7);\n          i0.ɵɵelement(13, \"app-progress-chart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 7);\n          i0.ɵɵelement(15, \"app-payment-chart\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"section\", 5)(17, \"div\", 6)(18, \"div\", 7);\n          i0.ɵɵelement(19, \"app-quotation-trends\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 7);\n          i0.ɵɵelement(21, \"app-heatmap-chart\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      dependencies: [RouterModule, CommonModule, NgxEchartsModule, KpiCardsComponent, ProgressChartComponent, PaymentChartComponent, QuotationTrendsComponent, HeatmapChartComponent, BuildCaseSelectorComponent],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  text-align: center;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin: 0 0 8px 0;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .dashboard-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #7f8c8d;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n\\n.dashboard-controls[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  padding: 20px;\\n  background: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n  border: 1px solid #f0f0f0;\\n}\\n\\n.dashboard-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.dashboard-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.chart-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 24px;\\n}\\n@media (max-width: 1200px) {\\n  .chart-grid[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .chart-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n}\\n\\n.chart-item[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n[_nghost-%COMP%]     .dashboard-card {\\n  background: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #f0f0f0;\\n  transition: all 0.3s ease;\\n}\\n[_nghost-%COMP%]     .dashboard-card:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n\\n@media (max-width: 1600px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%] {\\n    margin-bottom: 24px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 28px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%]   .dashboard-subtitle[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .dashboard-section[_ngcontent-%COMP%] {\\n    margin-bottom: 24px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterModule", "CommonModule", "KpiCardsComponent", "ProgressChartComponent", "PaymentChartComponent", "QuotationTrendsComponent", "HeatmapChartComponent", "BuildCaseSelectorComponent", "NgxEchartsModule", "HomeComponent", "constructor", "selectedBuildCase", "ngOnInit", "onBuildCaseChange", "buildCaseId", "kpiCardsComponent", "loadKpiData", "progressChartComponent", "loadProgressData", "paymentChartComponent", "loadPaymentData", "quotationTrendsComponent", "loadQuotationData", "heatmapChartComponent", "loadHeatmapData", "selectors", "viewQuery", "HomeComponent_Query", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "HomeComponent_Template_app_build_case_selector_buildCaseChange_7_listener", "$event", "ɵɵelement", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { KpiCardsComponent } from './components/kpi-cards/kpi-cards.component';\r\nimport { ProgressChartComponent } from './components/progress-chart/progress-chart.component';\r\nimport { PaymentChartComponent } from './components/payment-chart/payment-chart.component';\r\nimport { QuotationTrendsComponent } from './components/quotation-trends/quotation-trends.component';\r\nimport { HeatmapChartComponent } from './components/heatmap-chart/heatmap-chart.component';\r\nimport { BuildCaseSelectorComponent } from './components/build-case-selector/build-case-selector.component';\r\nimport { NgxEchartsModule } from 'ngx-echarts';\r\n\r\n@Component({\r\n    selector: 'ngx-home',\r\n    templateUrl: './home.component.html',\r\n    styleUrls: ['./home.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        RouterModule,\r\n        CommonModule,\r\n        NgxEchartsModule,\r\n        KpiCardsComponent,\r\n        ProgressChartComponent,\r\n        PaymentChartComponent,\r\n        QuotationTrendsComponent,\r\n        HeatmapChartComponent,\r\n        BuildCaseSelectorComponent\r\n    ]\r\n})\r\nexport class HomeComponent implements OnInit {\r\n  @ViewChild(KpiCardsComponent) kpiCardsComponent!: KpiCardsComponent;\r\n  @ViewChild(ProgressChartComponent) progressChartComponent!: ProgressChartComponent;\r\n  @ViewChild(PaymentChartComponent) paymentChartComponent!: PaymentChartComponent;\r\n  @ViewChild(QuotationTrendsComponent) quotationTrendsComponent!: QuotationTrendsComponent;\r\n  @ViewChild(HeatmapChartComponent) heatmapChartComponent!: HeatmapChartComponent;\r\n\r\n  selectedBuildCase: string = 'all';\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  onBuildCaseChange(buildCaseId: string): void {\r\n    this.selectedBuildCase = buildCaseId;\r\n    \r\n    // 通知所有圖表組件更新資料\r\n    if (this.kpiCardsComponent) {\r\n      this.kpiCardsComponent.loadKpiData(buildCaseId);\r\n    }\r\n    if (this.progressChartComponent) {\r\n      this.progressChartComponent.loadProgressData(buildCaseId);\r\n    }\r\n    if (this.paymentChartComponent) {\r\n      this.paymentChartComponent.loadPaymentData(buildCaseId);\r\n    }\r\n    if (this.quotationTrendsComponent) {\r\n      this.quotationTrendsComponent.loadQuotationData(buildCaseId);\r\n    }\r\n    if (this.heatmapChartComponent) {\r\n      this.heatmapChartComponent.loadHeatmapData(buildCaseId);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"dashboard-container\">\n  <!-- 頁面標題 -->\n  <div class=\"dashboard-header\">\n    <h1>營運儀表板</h1>\n    <p class=\"dashboard-subtitle\">建案進度與營運狀態總覽</p>\n  </div>\n\n  <!-- 建案選擇器 -->\n  <section class=\"dashboard-controls\">\n    <app-build-case-selector (buildCaseChange)=\"onBuildCaseChange($event)\"></app-build-case-selector>\n  </section>\n\n  <!-- 第一排：KPI 關鍵指標卡片 -->\n  <section class=\"dashboard-section\">\n    <app-kpi-cards></app-kpi-cards>\n  </section>\n\n  <!-- 第二排：業務狀態監控 -->\n  <section class=\"dashboard-section\">\n    <div class=\"chart-grid\">\n      <div class=\"chart-item\">\n        <app-progress-chart></app-progress-chart>\n      </div>\n      <div class=\"chart-item\">\n        <app-payment-chart></app-payment-chart>\n      </div>\n    </div>\n  </section>\n\n  <!-- 第三排：詳細分析 -->\n  <section class=\"dashboard-section\">\n    <div class=\"chart-grid\">\n      <div class=\"chart-item\">\n        <app-quotation-trends></app-quotation-trends>\n      </div>\n      <div class=\"chart-item\">\n        <app-heatmap-chart></app-heatmap-chart>\n      </div>\n    </div>\n  </section>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,wBAAwB,QAAQ,0DAA0D;AACnG,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,0BAA0B,QAAQ,gEAAgE;AAC3G,SAASC,gBAAgB,QAAQ,aAAa;;AAmB9C,OAAM,MAAOC,aAAa;EASxBC,YAAA;IAFA,KAAAC,iBAAiB,GAAW,KAAK;EAEjB;EAEhBC,QAAQA,CAAA,GACR;EAEAC,iBAAiBA,CAACC,WAAmB;IACnC,IAAI,CAACH,iBAAiB,GAAGG,WAAW;IAEpC;IACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACC,WAAW,CAACF,WAAW,CAAC;IACjD;IACA,IAAI,IAAI,CAACG,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACC,gBAAgB,CAACJ,WAAW,CAAC;IAC3D;IACA,IAAI,IAAI,CAACK,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACC,eAAe,CAACN,WAAW,CAAC;IACzD;IACA,IAAI,IAAI,CAACO,wBAAwB,EAAE;MACjC,IAAI,CAACA,wBAAwB,CAACC,iBAAiB,CAACR,WAAW,CAAC;IAC9D;IACA,IAAI,IAAI,CAACS,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACC,eAAe,CAACV,WAAW,CAAC;IACzD;EACF;;;uCAjCWL,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAgB,SAAA;MAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBACb1B,iBAAiB;yBACjBC,sBAAsB;yBACtBC,qBAAqB;yBACrBC,wBAAwB;yBACxBC,qBAAqB;;;;;;;;;;;;;;;;;;UC9B9BwB,EAHJ,CAAAC,cAAA,aAAiC,aAED,SACxB;UAAAD,EAAA,CAAAE,MAAA,qCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,WAA8B;UAAAD,EAAA,CAAAE,MAAA,yEAAW;UAC3CF,EAD2C,CAAAG,YAAA,EAAI,EACzC;UAIJH,EADF,CAAAC,cAAA,iBAAoC,iCACqC;UAA9CD,EAAA,CAAAI,UAAA,6BAAAC,0EAAAC,MAAA;YAAA,OAAmBP,GAAA,CAAAhB,iBAAA,CAAAuB,MAAA,CAAyB;UAAA,EAAC;UACxEN,EADyE,CAAAG,YAAA,EAA0B,EACzF;UAGVH,EAAA,CAAAC,cAAA,iBAAmC;UACjCD,EAAA,CAAAO,SAAA,oBAA+B;UACjCP,EAAA,CAAAG,YAAA,EAAU;UAKNH,EAFJ,CAAAC,cAAA,kBAAmC,cACT,cACE;UACtBD,EAAA,CAAAO,SAAA,0BAAyC;UAC3CP,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAwB;UACtBD,EAAA,CAAAO,SAAA,yBAAuC;UAG7CP,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,kBAAmC,cACT,cACE;UACtBD,EAAA,CAAAO,SAAA,4BAA6C;UAC/CP,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAwB;UACtBD,EAAA,CAAAO,SAAA,yBAAuC;UAI/CP,EAHM,CAAAG,YAAA,EAAM,EACF,EACE,EACN;;;qBDvBEjC,YAAY,EACZC,YAAY,EACZO,gBAAgB,EAChBN,iBAAiB,EACjBC,sBAAsB,EACtBC,qBAAqB,EACrBC,wBAAwB,EACxBC,qBAAqB,EACrBC,0BAA0B;MAAA+B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}