{"ast": null, "code": "import { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"../components/pagination/pagination.component\";\nfunction SpaceComponent_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const createModal_r4 = i0.ɵɵreference(69);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(createModal_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_65_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_tr_65_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const editModal_r8 = i0.ɵɵreference(71);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(editModal_r8, item_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_65_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_tr_65_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteSpace(item_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 41);\n    i0.ɵɵtemplate(11, SpaceComponent_tr_65_button_11_Template, 3, 0, \"button\", 42)(12, SpaceComponent_tr_65_button_12_Template, 3, 0, \"button\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CLocation || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r7.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 6, item_r7.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction SpaceComponent_ng_template_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 48)(1, \"nb-card-header\", 49)(2, \"h5\", 50);\n    i0.ɵɵelement(3, \"i\", 51);\n    i0.ɵɵtext(4, \"\\u65B0\\u589E\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_68_Template_button_click_5_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r11));\n    });\n    i0.ɵɵelement(6, \"i\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 54)(8, \"div\", 55)(9, \"div\", 56)(10, \"div\", 57)(11, \"div\", 58)(12, \"label\", 59);\n    i0.ɵɵtext(13, \" \\u9805\\u76EE\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 60)(15, \"input\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_68_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CName, $event) || (ctx_r2.spaceDetail.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 56)(17, \"div\", 57)(18, \"div\", 58)(19, \"label\", 62);\n    i0.ɵɵtext(20, \" \\u6240\\u5C6C\\u5340\\u57DF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 60)(22, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_68_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CLocation, $event) || (ctx_r2.spaceDetail.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(23, \"div\", 56)(24, \"div\", 57)(25, \"div\", 58)(26, \"label\", 64);\n    i0.ɵɵtext(27, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 60)(29, \"nb-form-field\", 65)(30, \"nb-select\", 66);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_68_Template_nb_select_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CStatus, $event) || (ctx_r2.spaceDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(31, \"nb-option\", 20)(32, \"span\", 7);\n    i0.ɵɵelement(33, \"i\", 67);\n    i0.ɵɵtext(34, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"nb-option\", 20)(36, \"span\", 7);\n    i0.ɵɵelement(37, \"i\", 68);\n    i0.ɵɵtext(38, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(39, \"nb-card-footer\", 69)(40, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_68_Template_button_click_40_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r11));\n    });\n    i0.ɵɵelement(41, \"i\", 71);\n    i0.ɵɵtext(42, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_68_Template_button_click_43_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r11));\n    });\n    i0.ɵɵelement(44, \"i\", 73);\n    i0.ɵɵtext(45, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CName);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CLocation);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction SpaceComponent_ng_template_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 48)(1, \"nb-card-header\", 49)(2, \"h5\", 50);\n    i0.ɵɵelement(3, \"i\", 74);\n    i0.ɵɵtext(4, \"\\u7DE8\\u8F2F\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_70_Template_button_click_5_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r13));\n    });\n    i0.ɵɵelement(6, \"i\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 54)(8, \"div\", 55)(9, \"div\", 56)(10, \"div\", 57)(11, \"div\", 58)(12, \"label\", 75);\n    i0.ɵɵtext(13, \" \\u6240\\u5C6C\\u5340\\u57DF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 60)(15, \"input\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_70_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CLocation, $event) || (ctx_r2.spaceDetail.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 56)(17, \"div\", 57)(18, \"div\", 58)(19, \"label\", 77);\n    i0.ɵɵtext(20, \" \\u9805\\u76EE\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 60)(22, \"input\", 78);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_70_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CName, $event) || (ctx_r2.spaceDetail.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(23, \"div\", 56)(24, \"div\", 57)(25, \"div\", 58)(26, \"label\", 79);\n    i0.ɵɵtext(27, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 60)(29, \"nb-form-field\", 65)(30, \"nb-select\", 80);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_70_Template_nb_select_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CStatus, $event) || (ctx_r2.spaceDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(31, \"nb-option\", 20)(32, \"span\", 7);\n    i0.ɵɵelement(33, \"i\", 67);\n    i0.ɵɵtext(34, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"nb-option\", 20)(36, \"span\", 7);\n    i0.ɵɵelement(37, \"i\", 68);\n    i0.ɵɵtext(38, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(39, \"nb-card-footer\", 69)(40, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_70_Template_button_click_40_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r13));\n    });\n    i0.ɵɵelement(41, \"i\", 71);\n    i0.ɵɵtext(42, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_70_Template_button_click_43_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r13));\n    });\n    i0.ɵɵelement(44, \"i\", 81);\n    i0.ɵɵtext(45, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CLocation);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction SpaceComponent_ng_template_72_tr_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 92);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 41)(12, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_72_tr_31_Template_button_click_12_listener() {\n      const template_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const templateDetailModal_r19 = i0.ɵɵreference(77);\n      return i0.ɵɵresetView(ctx_r2.viewTemplateDetail(template_r18, templateDetailModal_r19));\n    });\n    i0.ɵɵelement(13, \"i\", 94);\n    i0.ɵɵtext(14, \"\\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_72_tr_31_Template_button_click_15_listener() {\n      const template_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.deleteTemplate(template_r18));\n    });\n    i0.ɵɵelement(16, \"i\", 47);\n    i0.ɵɵtext(17, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const template_r18 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(template_r18.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", template_r18.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", template_r18.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 5, template_r18.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r18.CCreator || \"-\");\n  }\n}\nfunction SpaceComponent_ng_template_72_tr_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 95);\n    i0.ɵɵelement(2, \"i\", 96);\n    i0.ɵɵtext(3, \"\\u76EE\\u524D\\u6C92\\u6709\\u4EFB\\u4F55\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SpaceComponent_ng_template_72_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"ngx-pagination\", 38);\n    i0.ɵɵtwoWayListener(\"PageChange\", function SpaceComponent_ng_template_72_div_33_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templatePageIndex, $event) || (ctx_r2.templatePageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function SpaceComponent_ng_template_72_div_33_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.templatePageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r2.templatePageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r2.templatePageSize)(\"CollectionSize\", ctx_r2.templateTotalRecords);\n  }\n}\nfunction SpaceComponent_ng_template_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 82)(1, \"nb-card-header\", 49)(2, \"h5\", 50);\n    i0.ɵɵelement(3, \"i\", 83);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u7BA1\\u7406 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_72_Template_button_click_5_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r15));\n    });\n    i0.ɵɵelement(6, \"i\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 54)(8, \"div\", 84)(9, \"div\")(10, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_72_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const createTemplateModal_r16 = i0.ɵɵreference(75);\n      return i0.ɵɵresetView(ctx_r2.openCreateTemplateModal(createTemplateModal_r16));\n    });\n    i0.ɵɵelement(11, \"i\", 40);\n    i0.ɵɵtext(12, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 86)(14, \"small\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 87)(17, \"table\", 88)(18, \"thead\")(19, \"tr\", 33)(20, \"th\", 35);\n    i0.ɵɵtext(21, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 34);\n    i0.ɵɵtext(23, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 35);\n    i0.ɵɵtext(25, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\", 34);\n    i0.ɵɵtext(27, \"\\u5EFA\\u7ACB\\u8005\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"th\", 34);\n    i0.ɵɵtext(29, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"tbody\");\n    i0.ɵɵtemplate(31, SpaceComponent_ng_template_72_tr_31_Template, 18, 8, \"tr\", 36)(32, SpaceComponent_ng_template_72_tr_32_Template, 4, 0, \"tr\", 89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(33, SpaceComponent_ng_template_72_div_33_Template, 2, 3, \"div\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"nb-card-footer\", 69)(35, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_72_Template_button_click_35_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r15));\n    });\n    i0.ɵɵelement(36, \"i\", 71);\n    i0.ɵɵtext(37, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r2.templateTotalRecords, \" \\u7B46\\u6A21\\u677F\");\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateList.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateTotalRecords > ctx_r2.templatePageSize);\n  }\n}\nfunction SpaceComponent_ng_template_74_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 118)(2, \"input\", 119);\n    i0.ɵɵlistener(\"change\", function SpaceComponent_ng_template_74_div_41_Template_input_change_2_listener() {\n      const space_r24 = i0.ɵɵrestoreView(_r23).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleSpaceSelection(space_r24));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 120);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementStart(5, \"small\", 121);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const space_r24 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"space-\" + space_r24.CSpaceID)(\"checked\", space_r24.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"space-\" + space_r24.CSpaceID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r24.CName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r24.CLocation || \"-\");\n  }\n}\nfunction SpaceComponent_ng_template_74_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵelement(1, \"i\", 96);\n    i0.ɵɵtext(2, \"\\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_ng_template_74_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"ngx-pagination\", 38);\n    i0.ɵɵtwoWayListener(\"PageChange\", function SpaceComponent_ng_template_74_div_43_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spacePageIndex, $event) || (ctx_r2.spacePageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function SpaceComponent_ng_template_74_div_43_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.spacePageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r2.spacePageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r2.spacePageSize)(\"CollectionSize\", ctx_r2.spaceTotalRecords);\n  }\n}\nfunction SpaceComponent_ng_template_74_div_44_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 127);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_74_div_44_span_4_Template_button_click_2_listener() {\n      const space_r27 = i0.ɵɵrestoreView(_r26).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeSelectedSpace(space_r27));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r27 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r27.CName, \" \");\n  }\n}\nfunction SpaceComponent_ng_template_74_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"label\", 124);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 125);\n    i0.ɵɵtemplate(4, SpaceComponent_ng_template_74_div_44_span_4_Template, 3, 1, \"span\", 126);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u7684\\u7A7A\\u9593 (\", ctx_r2.selectedSpacesForTemplate.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedSpacesForTemplate);\n  }\n}\nfunction SpaceComponent_ng_template_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 98)(1, \"nb-card-header\", 49)(2, \"h5\", 50);\n    i0.ɵɵelement(3, \"i\", 51);\n    i0.ɵɵtext(4, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_74_Template_button_click_5_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r22));\n    });\n    i0.ɵɵelement(6, \"i\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 54)(8, \"div\", 55)(9, \"div\", 56)(10, \"div\", 57)(11, \"div\", 58)(12, \"label\", 99);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 60)(15, \"input\", 100);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_74_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown.control.enter\", function SpaceComponent_ng_template_74_Template_input_keydown_control_enter_15_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmitTemplate(ref_r22));\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 56)(17, \"div\", 57)(18, \"div\", 58)(19, \"label\", 101);\n    i0.ɵɵtext(20, \" \\u9078\\u64C7\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 60)(22, \"div\", 102)(23, \"div\", 103)(24, \"input\", 104);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_74_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchKeyword, $event) || (ctx_r2.spaceSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function SpaceComponent_ng_template_74_Template_input_keyup_enter_24_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 103)(26, \"input\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_74_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchLocation, $event) || (ctx_r2.spaceSearchLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function SpaceComponent_ng_template_74_Template_input_keyup_enter_26_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 106)(28, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_74_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceReset());\n    });\n    i0.ɵɵelement(29, \"i\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_74_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelement(31, \"i\", 110);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 111)(33, \"div\", 112)(34, \"div\", 7)(35, \"input\", 113);\n    i0.ɵɵlistener(\"change\", function SpaceComponent_ng_template_74_Template_input_change_35_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"label\", 114);\n    i0.ɵɵtext(37, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"small\", 86);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 55);\n    i0.ɵɵtemplate(41, SpaceComponent_ng_template_74_div_41_Template, 7, 5, \"div\", 115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, SpaceComponent_ng_template_74_div_42_Template, 3, 0, \"div\", 116)(43, SpaceComponent_ng_template_74_div_43_Template, 2, 3, \"div\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(44, SpaceComponent_ng_template_74_div_44_Template, 5, 2, \"div\", 117);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(45, \"nb-card-footer\", 69)(46, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_74_Template_button_click_46_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r22));\n    });\n    i0.ɵɵelement(47, \"i\", 71);\n    i0.ɵɵtext(48, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_74_Template_button_click_49_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmitTemplate(ref_r22));\n    });\n    i0.ɵɵelement(50, \"i\", 73);\n    i0.ɵɵtext(51, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchLocation);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"checked\", ctx_r2.allSpacesSelected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx_r2.spaceTotalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx_r2.spacePageIndex, \" / \", ctx_r2.Math.ceil(ctx_r2.spaceTotalRecords / ctx_r2.spacePageSize), \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableSpaces);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableSpaces.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.spaceTotalRecords > ctx_r2.spacePageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedSpacesForTemplate.length > 0);\n  }\n}\nfunction SpaceComponent_ng_template_76_div_62_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"i\", 148);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵelement(7, \"i\", 149);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 150);\n    i0.ɵɵelement(11, \"i\", 151);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const space_r30 = ctx.$implicit;\n    const i_r31 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r31 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r30.CName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r30.CLocation || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", space_r30.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(space_r30.CStatus === 1 ? \"fas fa-check-circle\" : \"fas fa-times-circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r30.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n  }\n}\nfunction SpaceComponent_ng_template_76_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"table\", 144)(2, \"thead\")(3, \"tr\", 145)(4, \"th\", 146);\n    i0.ɵɵtext(5, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 147);\n    i0.ɵɵtext(7, \"\\u7A7A\\u9593\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 147);\n    i0.ɵɵtext(9, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 35);\n    i0.ɵɵtext(11, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"tbody\");\n    i0.ɵɵtemplate(13, SpaceComponent_ng_template_76_div_62_tr_13_Template, 13, 7, \"tr\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateDetailSpaces);\n  }\n}\nfunction SpaceComponent_ng_template_76_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 152);\n    i0.ɵɵelement(1, \"i\", 96);\n    i0.ɵɵtext(2, \"\\u6B64\\u6A21\\u677F\\u5C1A\\u672A\\u5305\\u542B\\u4EFB\\u4F55\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_ng_template_76_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 98)(1, \"nb-card-header\", 49)(2, \"h5\", 50);\n    i0.ɵɵelement(3, \"i\", 129);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u660E\\u7D30 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_76_Template_button_click_5_listener() {\n      const ref_r29 = i0.ɵɵrestoreView(_r28).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r29));\n    });\n    i0.ɵɵelement(6, \"i\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 54)(8, \"div\", 130)(9, \"div\", 131)(10, \"h6\", 132);\n    i0.ɵɵelement(11, \"i\", 133);\n    i0.ɵɵtext(12, \"\\u57FA\\u672C\\u8CC7\\u8A0A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 134)(14, \"div\", 55)(15, \"div\", 11)(16, \"div\", 135)(17, \"label\", 136);\n    i0.ɵɵtext(18, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\", 137);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 135)(23, \"label\", 136);\n    i0.ɵɵtext(24, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\", 137)(26, \"span\", 92);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"div\", 11)(29, \"div\", 135)(30, \"label\", 136);\n    i0.ɵɵtext(31, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\", 137);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 11)(36, \"div\", 135)(37, \"label\", 136);\n    i0.ɵɵtext(38, \"\\u5EFA\\u7ACB\\u8005\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\", 137);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 11)(42, \"div\", 135)(43, \"label\", 136);\n    i0.ɵɵtext(44, \"\\u66F4\\u65B0\\u6642\\u9593\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"p\", 137);\n    i0.ɵɵtext(46);\n    i0.ɵɵpipe(47, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"div\", 11)(49, \"div\", 135)(50, \"label\", 136);\n    i0.ɵɵtext(51, \"\\u66F4\\u65B0\\u8005\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"p\", 137);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(54, \"div\", 138)(55, \"div\", 139)(56, \"h6\", 132);\n    i0.ɵɵelement(57, \"i\", 140);\n    i0.ɵɵtext(58, \"\\u5305\\u542B\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"span\", 141);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 134);\n    i0.ɵɵtemplate(62, SpaceComponent_ng_template_76_div_62_Template, 14, 1, \"div\", 142)(63, SpaceComponent_ng_template_76_div_63_Template, 3, 0, \"div\", 143);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(64, \"nb-card-footer\", 69)(65, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_76_Template_button_click_65_listener() {\n      const ref_r29 = i0.ɵɵrestoreView(_r28).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r29));\n    });\n    i0.ɵɵelement(66, \"i\", 71);\n    i0.ɵɵtext(67, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(20);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CTemplateName) || \"-\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(34, 10, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreator) || \"-\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(47, 13, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r2.templateDetailSpaces.length, \" \\u500B\\u7A7A\\u9593\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length === 0);\n  }\n}\nexport class SpaceComponent extends BaseComponent {\n  constructor(allow, dialogService, _spaceService, _templateService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._spaceService = _spaceService;\n    this._templateService = _templateService;\n    this.message = message;\n    this.valid = valid;\n    this.Math = Math; // 讓模板可以使用 Math 函數\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.spaceList = [];\n    this.spaceDetail = {};\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.searchStatus = null;\n    // 模板相關屬性\n    this.templateList = [];\n    this.templateDetail = {};\n    this.templatePageIndex = 1;\n    this.templatePageSize = 10;\n    this.templateTotalRecords = 0;\n    // 查看模板明細相關屬性\n    this.selectedTemplateDetail = null;\n    this.templateDetailSpaces = [];\n    // 新增模板時的空間選擇器相關屬性\n    this.availableSpaces = [];\n    this.selectedSpacesForTemplate = [];\n    this.spacePageIndex = 1;\n    this.spacePageSize = 10;\n    this.spaceTotalRecords = 0;\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    // 控制UI顯示\n    this.showSpacePickList = false;\n    this.allSpacesSelected = false;\n  }\n  ngOnInit() {\n    this.getSpaceList();\n  }\n  getSpaceList() {\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CName: this.searchKeyword || null,\n        CLocation: this.searchLocation || null,\n        CStatus: this.searchStatus\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    })).subscribe();\n  }\n  onSearch() {\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getSpaceList();\n  }\n  openCreateModal(ref) {\n    this.spaceDetail = {\n      CName: '',\n      CLocation: '',\n      CStatus: 1 // 1 = 啟用, 0 = 停用\n    };\n    this.dialogService.open(ref);\n  }\n  openEditModal(ref, item) {\n    this.getSpaceById(item.CSpaceID, ref);\n  }\n  getSpaceById(spaceId, ref) {\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\n      body: {\n        CSpaceID: spaceId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceDetail = {\n          ...res.Entries\n        };\n        this.dialogService.open(ref);\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    });\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._spaceService.apiSpaceSaveSpacePost$Json({\n      body: this.spaceDetail\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n        this.getSpaceList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    })).subscribe();\n  }\n  deleteSpace(item) {\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\n        body: {\n          CSpaceID: item.CSpaceId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"刪除成功\");\n          this.getSpaceList();\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除失敗');\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[空間名稱]', this.spaceDetail.CName);\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CName, 50);\n    this.valid.required('[所屬區域]', this.spaceDetail.CLocation);\n    this.valid.isStringMaxLength('[所屬區域]', this.spaceDetail.CLocation, 50);\n  }\n  // 模板管理方法\n  openTemplateManagementModal(ref) {\n    this.templatePageIndex = 1; // 重置到第一頁\n    this.getTemplateList();\n    this.dialogService.open(ref, {\n      hasScroll: true\n    });\n  }\n  getTemplateList() {\n    this._templateService.apiTemplateGetTemplateListPost$Json({\n      body: {\n        PageIndex: this.templatePageIndex,\n        PageSize: this.templatePageSize\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode === 0) {\n        this.templateList = res.Entries.map(item => ({\n          CTemplateId: item.CTemplateId,\n          CTemplateName: item.CTemplateName,\n          CCreateDt: item.CCreateDt,\n          CUpdateDt: item.CUpdateDt,\n          CCreator: item.CCreator,\n          CUpdator: item.CUpdator,\n          CStatus: item.CStatus\n        }));\n        this.templateTotalRecords = res.TotalItems || 0;\n      } else {\n        this.message.showErrorMSG(res.Message || '載入模板資料失敗');\n      }\n    });\n  }\n  openCreateTemplateModal(ref) {\n    this.templateDetail = {\n      CTemplateName: '',\n      CTemplateType: 1,\n      CStatus: 1\n    };\n    this.selectedSpacesForTemplate = [];\n    this.spacePageIndex = 1; // 重置到第一頁\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.getSpacesForPickList();\n    this.dialogService.open(ref, {\n      hasScroll: true\n    });\n  }\n  getSpacesForPickList() {\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: {\n        PageIndex: this.spacePageIndex,\n        PageSize: this.spacePageSize,\n        CName: this.spaceSearchKeyword || null,\n        CLocation: this.spaceSearchLocation || null,\n        CStatus: 1 // 只顯示啟用的空間\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode === 0) {\n        this.availableSpaces = res.Entries.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CName: item.CName,\n          CLocation: item.CLocation,\n          selected: this.selectedSpacesForTemplate.some(selected => selected.CSpaceID === item.CSpaceID)\n        }));\n        this.spaceTotalRecords = res.TotalItems;\n        this.updateAllSpacesSelectedState();\n      } else {\n        this.message.showErrorMSG(res.Message || '載入空間資料失敗');\n      }\n    });\n  }\n  onSpaceSearch() {\n    this.spacePageIndex = 1;\n    this.getSpacesForPickList();\n  }\n  onSpaceReset() {\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.getSpacesForPickList();\n  }\n  spacePageChanged(newPage) {\n    this.spacePageIndex = newPage;\n    this.getSpacesForPickList();\n  }\n  templatePageChanged(newPage) {\n    this.templatePageIndex = newPage;\n    this.getTemplateList();\n  }\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      this.selectedSpacesForTemplate.push({\n        ...space\n      });\n    } else {\n      const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\n      if (index > -1) {\n        this.selectedSpacesForTemplate.splice(index, 1);\n      }\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  toggleAllSpaces() {\n    this.allSpacesSelected = !this.allSpacesSelected;\n    this.availableSpaces.forEach(space => {\n      const wasSelected = space.selected;\n      space.selected = this.allSpacesSelected;\n      if (this.allSpacesSelected && !wasSelected) {\n        // 新選中的空間，添加到已選列表\n        const exists = this.selectedSpacesForTemplate.find(s => s.CSpaceID === space.CSpaceID);\n        if (!exists) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else if (!this.allSpacesSelected && wasSelected) {\n        // 取消選中的空間，從已選列表移除\n        const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\n        if (index > -1) {\n          this.selectedSpacesForTemplate.splice(index, 1);\n        }\n      }\n    });\n  }\n  updateAllSpacesSelectedState() {\n    this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  removeSelectedSpace(space) {\n    const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\n    if (index > -1) {\n      this.selectedSpacesForTemplate.splice(index, 1);\n    }\n    // 更新可用空間列表中的選擇狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  onSubmitTemplate(ref) {\n    this.templateValidation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.selectedSpacesForTemplate.length === 0) {\n      this.message.showErrorMSG('請至少選擇一個空間');\n      return;\n    }\n    const templateDetails = this.selectedSpacesForTemplate.map(space => ({\n      CReleateId: space.CSpaceID,\n      CReleateName: space.CName\n    }));\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: {\n        ...this.templateDetail,\n        Details: templateDetails\n      }\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"模板建立成功\");\n        ref.close();\n        this.getTemplateList(); // 刷新模板列表\n      } else {\n        this.message.showErrorMSG(res.Message || '建立模板失敗');\n      }\n    });\n  }\n  deleteTemplate(template) {\n    if (confirm(`您確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\n        body: {\n          CTemplateId: template.CTemplateId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"模板刪除成功\");\n          // 檢查刪除後當前頁是否還有資料，如果沒有則跳轉到上一頁\n          const currentPageStartIndex = (this.templatePageIndex - 1) * this.templatePageSize;\n          const remainingItems = this.templateTotalRecords - 1; // 刪除一筆後的總數\n          if (remainingItems > 0 && currentPageStartIndex >= remainingItems) {\n            this.templatePageIndex = Math.max(1, this.templatePageIndex - 1);\n          }\n          this.getTemplateList();\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除模板失敗');\n        }\n      });\n    }\n  }\n  viewTemplateDetail(template, ref) {\n    this.selectedTemplateDetail = template;\n    this.getTemplateDetailSpaces(template.CTemplateId);\n    this.dialogService.open(ref, {\n      hasScroll: true\n    });\n  }\n  getTemplateDetailSpaces(templateId) {\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: {\n        templateId: templateId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode === 0) {\n        // 根據模板明細獲取空間資訊\n        const spaceIds = res.Entries.map(item => item.CReleateId).filter(id => id !== undefined);\n        if (spaceIds.length > 0) {\n          this.getSpaceDetailsByIds(spaceIds);\n        } else {\n          this.templateDetailSpaces = [];\n        }\n      } else {\n        this.message.showErrorMSG(res.Message || '載入模板明細失敗');\n        this.templateDetailSpaces = [];\n      }\n    });\n  }\n  getSpaceDetailsByIds(spaceIds) {\n    // 由於沒有批次查詢空間的API，我們需要查詢所有空間然後篩選\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: {\n        PageIndex: 1,\n        PageSize: 1000,\n        // 設定大一點確保能載入所有空間\n        CName: null,\n        CLocation: null,\n        CStatus: null\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode === 0) {\n        this.templateDetailSpaces = res.Entries.filter(space => spaceIds.includes(space.CSpaceID)).map(space => ({\n          CSpaceID: space.CSpaceID,\n          CName: space.CName,\n          CLocation: space.CLocation,\n          CStatus: space.CStatus\n        }));\n      } else {\n        this.message.showErrorMSG(res.Message || '載入空間資料失敗');\n        this.templateDetailSpaces = [];\n      }\n    });\n  }\n  templateValidation() {\n    this.valid.clear();\n    this.valid.required('[模板名稱]', this.templateDetail.CTemplateName);\n    this.valid.isStringMaxLength('[模板名稱]', this.templateDetail.CTemplateName, 100);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  static {\n    this.ɵfac = function SpaceComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceComponent,\n      selectors: [[\"ngx-space\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 78,\n      vars: 11,\n      consts: [[\"createModal\", \"\"], [\"editModal\", \"\"], [\"templateManagementModal\", \"\"], [\"createTemplateModal\", \"\"], [\"templateDetailModal\", \"\"], [\"accent\", \"success\"], [1, \"alert\", \"alert-info\", \"mb-4\", 2, \"border-left\", \"4px solid #4a90e2\", \"background-color\", \"#f8f9ff\", \"border-radius\", \"6px\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-3\", 2, \"font-size\", \"1.2rem\"], [1, \"mb-0\", \"text-muted\", 2, \"font-size\", \"0.9rem\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"spaceName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"spaceName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"location\", 1, \"label\", \"col-3\"], [\"type\", \"text\", \"id\", \"location\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"col-3\"], [\"id\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-warning\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-layer-group\", \"me-1\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"custom-table\", 2, \"min-width\", \"800px\"], [1, \"table-header\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"table-actions\"], [\"class\", \"btn btn-outline-success btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"border-bottom\", \"py-3\", \"px-4\"], [1, \"mb-0\", \"text-primary\", \"font-weight-bold\"], [1, \"fas\", \"fa-plus-circle\", \"me-2\", \"text-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-light\", \"btn-sm\", \"rounded-circle\", 2, \"width\", \"32px\", \"height\", \"32px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"px-4\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"form-group\", \"mb-4\"], [1, \"d-flex\", \"align-items-start\"], [\"for\", \"spaceName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"flex-grow-1\", \"ml-3\"], [\"type\", \"text\", \"id\", \"spaceName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u9805\\u76EE\\u540D\\u7A31\", \"name\", \"spaceName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"location\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"location\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6240\\u5C6C\\u5340\\u57DF\", \"name\", \"location\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"status\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"w-full\"], [\"id\", \"status\", \"name\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\", \"me-2\"], [1, \"d-flex\", \"justify-content-end\", \"border-top\", \"pt-3\", \"px-4\", \"pb-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"btn\", \"btn-outline-secondary\", \"me-3\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", \"background\", \"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [1, \"fas\", \"fa-edit\", \"me-2\", \"text-warning\"], [\"for\", \"locationEdit\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"locationEdit\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6240\\u5C6C\\u5340\\u57DF\", \"name\", \"location\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"spaceNameEdit\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"spaceNameEdit\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u9805\\u76EE\\u540D\\u7A31\", \"name\", \"spaceName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"statusEdit\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"id\", \"statusEdit\", \"name\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-save\", \"me-1\"], [2, \"width\", \"900px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"fas\", \"fa-layer-group\", \"me-2\", \"text-warning\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"text-muted\"], [1, \"table-responsive\"], [1, \"table\", \"custom-table\"], [4, \"ngIf\"], [\"class\", \"d-flex justify-content-center mt-3\", 4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"badge\", 3, \"ngClass\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"colspan\", \"5\", 1, \"text-center\", \"text-muted\", \"py-4\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [1, \"d-flex\", \"justify-content-center\", \"mt-3\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [\"for\", \"templateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"templateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"keydown.control.enter\", \"ngModel\"], [1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"row\", \"mb-3\"], [1, \"col-md-5\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"col-md-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"border\", \"rounded\", \"p-3\", 2, \"max-height\", \"300px\", \"overflow-y\", \"auto\", \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"selectAll\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAll\", 1, \"mb-0\", \"font-weight-bold\"], [\"class\", \"col-md-6\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-3\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"id\", \"checked\"], [1, \"form-check-label\", 3, \"for\"], [1, \"text-muted\", \"d-block\"], [1, \"text-center\", \"text-muted\", \"py-3\"], [1, \"mt-3\"], [1, \"mb-2\", \"font-weight-bold\"], [1, \"border\", \"rounded\", \"p-2\", 2, \"max-height\", \"150px\", \"overflow-y\", \"auto\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", 1, \"btn-close\", \"btn-close-white\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"me-2\", \"text-info\"], [1, \"card\", \"mb-4\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"mb-0\", \"text-dark\", \"font-weight-bold\"], [1, \"fas\", \"fa-info-circle\", \"me-2\", \"text-primary\"], [1, \"card-body\"], [1, \"form-group\", \"mb-3\"], [1, \"font-weight-bold\", \"text-muted\"], [1, \"mb-0\"], [1, \"card\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-success\"], [1, \"badge\", \"badge-info\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [1, \"table\", \"table-sm\", \"table-striped\"], [2, \"background-color\", \"#f8f9fa\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-4\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\", \"text-muted\"], [1, \"badge\", \"badge-sm\", 3, \"ngClass\"], [1, \"me-1\"], [1, \"text-center\", \"text-muted\", \"py-4\"]],\n      template: function SpaceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 5)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 6)(5, \"div\", 7);\n          i0.ɵɵelement(6, \"i\", 8);\n          i0.ɵɵelementStart(7, \"div\")(8, \"p\", 9);\n          i0.ɵɵtext(9, \" \\u5728\\u6B64\\u9801\\u9762\\u60A8\\u53EF\\u4EE5\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u7684\\u5404\\u500B\\u7A7A\\u9593\\u8CC7\\u8A0A\\uFF0C\\u5305\\u62EC\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u7A7A\\u9593\\uFF0C\\u4EE5\\u53CA\\u8A2D\\u5B9A\\u9805\\u76EE\\u540D\\u7A31\\u3001\\u6240\\u5C6C\\u5340\\u57DF\\u548C\\u555F\\u7528\\u72C0\\u614B\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"label\", 13);\n          i0.ɵɵtext(14, \"\\u9805\\u76EE\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"nb-form-field\", 14)(16, \"input\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpaceComponent_Template_input_keyup_enter_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"div\", 12)(19, \"label\", 16);\n          i0.ɵɵtext(20, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"nb-form-field\", 14)(22, \"input\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_input_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchLocation, $event) || (ctx.searchLocation = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpaceComponent_Template_input_keyup_enter_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 11)(24, \"div\", 12)(25, \"label\", 18);\n          i0.ɵɵtext(26, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-form-field\", 14)(28, \"nb-select\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_nb_select_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchStatus, $event) || (ctx.searchStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SpaceComponent_Template_nb_select_selectedChange_28_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(29, \"nb-option\", 20);\n          i0.ɵɵtext(30, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"nb-option\", 20);\n          i0.ɵɵtext(32, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-option\", 20);\n          i0.ɵɵtext(34, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(35, \"div\", 11);\n          i0.ɵɵelementStart(36, \"div\", 21)(37, \"div\", 22)(38, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SpaceComponent_Template_button_click_38_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReset());\n          });\n          i0.ɵɵelement(39, \"i\", 24);\n          i0.ɵɵtext(40, \"\\u91CD\\u7F6E \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function SpaceComponent_Template_button_click_41_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelement(42, \"i\", 26);\n          i0.ɵɵtext(43, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 21)(45, \"div\", 27);\n          i0.ɵɵtemplate(46, SpaceComponent_button_46_Template, 3, 0, \"button\", 28);\n          i0.ɵɵelementStart(47, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function SpaceComponent_Template_button_click_47_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const templateManagementModal_r5 = i0.ɵɵreference(73);\n            return i0.ɵɵresetView(ctx.openTemplateManagementModal(templateManagementModal_r5));\n          });\n          i0.ɵɵelement(48, \"i\", 30);\n          i0.ɵɵtext(49, \"\\u7BA1\\u7406\\u6A21\\u677F \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(50, \"div\", 31)(51, \"table\", 32)(52, \"thead\")(53, \"tr\", 33)(54, \"th\", 34);\n          i0.ɵɵtext(55, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"th\", 34);\n          i0.ɵɵtext(57, \"\\u9805\\u76EE\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"th\", 34);\n          i0.ɵɵtext(59, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"th\", 35);\n          i0.ɵɵtext(61, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"th\", 35);\n          i0.ɵɵtext(63, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(64, \"tbody\");\n          i0.ɵɵtemplate(65, SpaceComponent_tr_65_Template, 13, 9, \"tr\", 36);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(66, \"nb-card-footer\", 37)(67, \"ngx-pagination\", 38);\n          i0.ɵɵtwoWayListener(\"PageChange\", function SpaceComponent_Template_ngx_pagination_PageChange_67_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function SpaceComponent_Template_ngx_pagination_PageChange_67_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(68, SpaceComponent_ng_template_68_Template, 46, 5, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(70, SpaceComponent_ng_template_70_Template, 46, 5, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(72, SpaceComponent_ng_template_72_Template, 38, 4, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(74, SpaceComponent_ng_template_74_Template, 52, 11, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(76, SpaceComponent_ng_template_76_Template, 68, 16, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchLocation);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngForOf\", ctx.spaceList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, i6.DatePipe, SharedModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i8.BreadcrumbComponent, i9.PaginationComponent],\n      styles: [\".btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: var(--color-bg-2);\\n  font-weight: 600;\\n  border-bottom: 2px solid var(--color-bg-3);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: var(--color-bg-1);\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0;\\n  border-bottom-left-radius: 0;\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--color-fg-heading);\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: var(--color-danger) !important;\\n}\\n\\n.alert-info[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.alert-info[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);\\n  transform: translateY(-1px);\\n}\\n.alert-info[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  letter-spacing: 0.3px;\\n}\\n.alert-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  line-height: 1.6;\\n  color: #6c757d !important;\\n}\\n.alert-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.mt-3[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%], \\n.btn-outline-secondary[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:hover, \\n.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.btn-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.btn-outline-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #6c757d;\\n  color: #6c757d;\\n}\\n.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: #fff;\\n}\\n\\nnb-card[style*=box-shadow][_ngcontent-%COMP%] {\\n  transition: all 0.3s ease-in-out;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  letter-spacing: 0.5px;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  transform: scale(1.05);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n  letter-spacing: 0.3px;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label.required-field[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: #dc3545;\\n  font-weight: bold;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[nbInput][_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  font-size: 0.875rem;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[nbInput][_ngcontent-%COMP%]:focus {\\n  border-color: #4a90e2;\\n  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);\\n  transform: translateY(-1px);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[nbInput][_ngcontent-%COMP%]::placeholder {\\n  color: #adb5bd;\\n  font-style: italic;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]   nb-option[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]   nb-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]   nb-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  letter-spacing: 0.3px;\\n  transition: all 0.2s ease;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #357abd 0%, #2c5282 100%);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: #fff;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus-within, \\nnb-select[_ngcontent-%COMP%]:focus-within {\\n  border-color: #4a90e2;\\n  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);\\n}\\n\\n@media (max-width: 576px) {\\n  nb-card[style*=\\\"width: 550px\\\"][_ngcontent-%COMP%] {\\n    width: 95vw !important;\\n    margin: 0 auto;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "BaseComponent", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "SpaceComponent_button_46_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "createModal_r4", "ɵɵreference", "ɵɵresetView", "openCreateModal", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "SpaceComponent_tr_65_button_11_Template_button_click_0_listener", "_r6", "item_r7", "$implicit", "editModal_r8", "openEditModal", "SpaceComponent_tr_65_button_12_Template_button_click_0_listener", "_r9", "deleteSpace", "ɵɵtemplate", "SpaceComponent_tr_65_button_11_Template", "SpaceComponent_tr_65_button_12_Template", "ɵɵadvance", "ɵɵtextInterpolate", "CLocation", "CName", "ɵɵtextInterpolate1", "CStatus", "ɵɵpipeBind2", "CCreateDt", "ɵɵproperty", "isUpdate", "isDelete", "SpaceComponent_ng_template_68_Template_button_click_5_listener", "ref_r11", "_r10", "dialogRef", "onClose", "ɵɵtwoWayListener", "SpaceComponent_ng_template_68_Template_input_ngModelChange_15_listener", "$event", "ɵɵtwoWayBindingSet", "spaceDetail", "SpaceComponent_ng_template_68_Template_input_ngModelChange_22_listener", "SpaceComponent_ng_template_68_Template_nb_select_ngModelChange_30_listener", "SpaceComponent_ng_template_68_Template_button_click_40_listener", "SpaceComponent_ng_template_68_Template_button_click_43_listener", "onSubmit", "ɵɵtwoWayProperty", "SpaceComponent_ng_template_70_Template_button_click_5_listener", "ref_r13", "_r12", "SpaceComponent_ng_template_70_Template_input_ngModelChange_15_listener", "SpaceComponent_ng_template_70_Template_input_ngModelChange_22_listener", "SpaceComponent_ng_template_70_Template_nb_select_ngModelChange_30_listener", "SpaceComponent_ng_template_70_Template_button_click_40_listener", "SpaceComponent_ng_template_70_Template_button_click_43_listener", "SpaceComponent_ng_template_72_tr_31_Template_button_click_12_listener", "template_r18", "_r17", "templateDetailModal_r19", "viewTemplateDetail", "SpaceComponent_ng_template_72_tr_31_Template_button_click_15_listener", "deleteTemplate", "CTemplateName", "CCreator", "SpaceComponent_ng_template_72_div_33_Template_ngx_pagination_PageChange_1_listener", "_r20", "templatePageIndex", "templatePageChanged", "templatePageSize", "templateTotalRecords", "SpaceComponent_ng_template_72_Template_button_click_5_listener", "ref_r15", "_r14", "SpaceComponent_ng_template_72_Template_button_click_10_listener", "createTemplateModal_r16", "openCreateTemplateModal", "SpaceComponent_ng_template_72_tr_31_Template", "SpaceComponent_ng_template_72_tr_32_Template", "SpaceComponent_ng_template_72_div_33_Template", "SpaceComponent_ng_template_72_Template_button_click_35_listener", "templateList", "length", "SpaceComponent_ng_template_74_div_41_Template_input_change_2_listener", "space_r24", "_r23", "toggleSpaceSelection", "CSpaceID", "selected", "SpaceComponent_ng_template_74_div_43_Template_ngx_pagination_PageChange_1_listener", "_r25", "spacePageIndex", "spacePageChanged", "spacePageSize", "spaceTotalRecords", "SpaceComponent_ng_template_74_div_44_span_4_Template_button_click_2_listener", "space_r27", "_r26", "removeSelectedSpace", "SpaceComponent_ng_template_74_div_44_span_4_Template", "selectedSpacesForTemplate", "SpaceComponent_ng_template_74_Template_button_click_5_listener", "ref_r22", "_r21", "SpaceComponent_ng_template_74_Template_input_ngModelChange_15_listener", "templateDetail", "SpaceComponent_ng_template_74_Template_input_keydown_control_enter_15_listener", "onSubmitTemplate", "SpaceComponent_ng_template_74_Template_input_ngModelChange_24_listener", "spaceSearchKeyword", "SpaceComponent_ng_template_74_Template_input_keyup_enter_24_listener", "onSpaceSearch", "SpaceComponent_ng_template_74_Template_input_ngModelChange_26_listener", "spaceSearchLocation", "SpaceComponent_ng_template_74_Template_input_keyup_enter_26_listener", "SpaceComponent_ng_template_74_Template_button_click_28_listener", "onSpaceReset", "SpaceComponent_ng_template_74_Template_button_click_30_listener", "SpaceComponent_ng_template_74_Template_input_change_35_listener", "toggleAllSpaces", "SpaceComponent_ng_template_74_div_41_Template", "SpaceComponent_ng_template_74_div_42_Template", "SpaceComponent_ng_template_74_div_43_Template", "SpaceComponent_ng_template_74_div_44_Template", "SpaceComponent_ng_template_74_Template_button_click_46_listener", "SpaceComponent_ng_template_74_Template_button_click_49_listener", "allSpacesSelected", "ɵɵtextInterpolate3", "Math", "ceil", "availableSpaces", "i_r31", "space_r30", "ɵɵclassMap", "SpaceComponent_ng_template_76_div_62_tr_13_Template", "templateDetailSpaces", "SpaceComponent_ng_template_76_Template_button_click_5_listener", "ref_r29", "_r28", "SpaceComponent_ng_template_76_div_62_Template", "SpaceComponent_ng_template_76_div_63_Template", "SpaceComponent_ng_template_76_Template_button_click_65_listener", "selectedTemplateDetail", "CUpdateDt", "CUpdator", "SpaceComponent", "constructor", "allow", "dialogService", "_spaceService", "_templateService", "message", "valid", "pageFirst", "pageSize", "pageIndex", "totalRecords", "spaceList", "searchKeyword", "searchLocation", "searchStatus", "showSpacePickList", "ngOnInit", "getSpaceList", "apiSpaceGetSpaceListPost$Json", "body", "PageIndex", "PageSize", "pipe", "res", "Entries", "StatusCode", "TotalItems", "showErrorMSG", "Message", "subscribe", "onSearch", "onReset", "pageChanged", "newPage", "ref", "open", "item", "getSpaceById", "spaceId", "apiSpaceGetSpaceByIdPost$Json", "validation", "errorMessages", "showErrorMSGs", "apiSpaceSaveSpacePost$Json", "showSucessMSG", "close", "confirm", "apiSpaceDeleteSpacePost$Json", "CSpaceId", "clear", "required", "isStringMaxLength", "openTemplateManagementModal", "getTemplateList", "hasScroll", "apiTemplateGetTemplateListPost$Json", "map", "CTemplateId", "CTemplateType", "getSpacesForPickList", "some", "updateAllSpacesSelectedState", "space", "push", "index", "findIndex", "s", "splice", "for<PERSON>ach", "wasSelected", "exists", "find", "every", "availableSpace", "templateValidation", "templateDetails", "CReleateId", "CReleateName", "apiTemplateSaveTemplatePost$Json", "Details", "template", "apiTemplateDeleteTemplatePost$Json", "currentPageStartIndex", "remainingItems", "max", "getTemplateDetailSpaces", "templateId", "apiTemplateGetTemplateDetailByIdPost$Json", "spaceIds", "filter", "id", "undefined", "getSpaceDetailsByIds", "includes", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "SpaceService", "TemplateService", "i4", "MessageService", "i5", "ValidationHelper", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "SpaceComponent_Template", "rf", "ctx", "SpaceComponent_Template_input_ngModelChange_16_listener", "_r1", "SpaceComponent_Template_input_keyup_enter_16_listener", "SpaceComponent_Template_input_ngModelChange_22_listener", "SpaceComponent_Template_input_keyup_enter_22_listener", "SpaceComponent_Template_nb_select_ngModelChange_28_listener", "SpaceComponent_Template_nb_select_selectedChange_28_listener", "SpaceComponent_Template_button_click_38_listener", "SpaceComponent_Template_button_click_41_listener", "SpaceComponent_button_46_Template", "SpaceComponent_Template_button_click_47_listener", "templateManagementModal_r5", "SpaceComponent_tr_65_Template", "SpaceComponent_Template_ngx_pagination_PageChange_67_listener", "SpaceComponent_ng_template_68_Template", "ɵɵtemplateRefExtractor", "SpaceComponent_ng_template_70_Template", "SpaceComponent_ng_template_72_Template", "SpaceComponent_ng_template_74_Template", "SpaceComponent_ng_template_76_Template", "isCreate", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "i8", "BreadcrumbComponent", "i9", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\space\\space.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\space\\space.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { SpaceService, TemplateService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { tap } from 'rxjs';\r\nimport { GetSpaceListResponse, SaveSpaceRequest, TemplateGetListResponse, SaveTemplateArgs, SaveTemplateDetailArgs, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\nexport interface TemplateItem {\r\n  CTemplateId: number;\r\n  CTemplateName: string;\r\n  CCreateDt: string;\r\n  CUpdateDt: string;\r\n  CCreator?: string | null;\r\n  CUpdator?: string | null;\r\n  CStatus?: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpacePickListItem {\r\n  CSpaceID: number;\r\n  CName: string;\r\n  CLocation?: string | null;\r\n  CStatus?: number;\r\n  selected?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-space',\r\n  templateUrl: './space.component.html',\r\n  styleUrls: ['./space.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent,\r\n  ],\r\n})\r\n\r\nexport class SpaceComponent extends BaseComponent implements OnInit {\r\n  Math = Math; // 讓模板可以使用 Math 函數\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _spaceService: SpaceService,\r\n    private _templateService: TemplateService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  spaceList: GetSpaceListResponse[] = [];\r\n  spaceDetail: SaveSpaceRequest = {};\r\n  searchKeyword: string = '';\r\n  searchLocation: string = '';\r\n  searchStatus: number | null = null;\r\n\r\n  // 模板相關屬性\r\n  templateList: TemplateItem[] = [];\r\n  templateDetail: SaveTemplateArgs = {};\r\n  templatePageIndex = 1;\r\n  templatePageSize = 10;\r\n  templateTotalRecords = 0;\r\n  \r\n  // 查看模板明細相關屬性\r\n  selectedTemplateDetail: TemplateItem | null = null;\r\n  templateDetailSpaces: SpacePickListItem[] = [];\r\n  \r\n  // 新增模板時的空間選擇器相關屬性\r\n  availableSpaces: SpacePickListItem[] = [];\r\n  selectedSpacesForTemplate: SpacePickListItem[] = [];\r\n  spacePageIndex = 1;\r\n  spacePageSize = 10;\r\n  spaceTotalRecords = 0;\r\n  spaceSearchKeyword = '';\r\n  spaceSearchLocation = '';\r\n  \r\n  // 控制UI顯示\r\n  showSpacePickList = false;\r\n  allSpacesSelected = false;\r\n\r\n  override ngOnInit(): void {\r\n    this.getSpaceList();\r\n  }\r\n\r\n  getSpaceList() {\r\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\r\n      body: {\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CName: this.searchKeyword || null,\r\n        CLocation: this.searchLocation || null,\r\n        CStatus: this.searchStatus\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.spaceList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSearch() {\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  onReset() {\r\n    this.searchKeyword = '';\r\n    this.searchLocation = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  openCreateModal(ref: any) {\r\n    this.spaceDetail = {\r\n      CName: '',\r\n      CLocation: '',\r\n      CStatus: 1 // 1 = 啟用, 0 = 停用\r\n    };\r\n    this.dialogService.open(ref);\r\n  }\r\n\r\n  openEditModal(ref: any, item: any) {\r\n    this.getSpaceById(item.CSpaceID, ref);\r\n  }\r\n\r\n  getSpaceById(spaceId: number, ref: any) {\r\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\r\n      body: { CSpaceID: spaceId }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.spaceDetail = { ...res.Entries };\r\n        this.dialogService.open(ref);\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this._spaceService.apiSpaceSaveSpacePost$Json({\r\n      body: this.spaceDetail\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  deleteSpace(item: any) {\r\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\r\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\r\n        body: { CSpaceID: item.CSpaceId }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"刪除成功\");\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '刪除失敗');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[空間名稱]', this.spaceDetail.CName);\r\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CName, 50);\r\n    this.valid.required('[所屬區域]', this.spaceDetail.CLocation);\r\n    this.valid.isStringMaxLength('[所屬區域]', this.spaceDetail.CLocation, 50);\r\n  }\r\n\r\n  // 模板管理方法\r\n  openTemplateManagementModal(ref: any) {\r\n    this.templatePageIndex = 1; // 重置到第一頁\r\n    this.getTemplateList();\r\n    this.dialogService.open(ref, { hasScroll: true });\r\n  }\r\n\r\n  getTemplateList() {\r\n    this._templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: {\r\n        PageIndex: this.templatePageIndex,\r\n        PageSize: this.templatePageSize\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode === 0) {\r\n        this.templateList = res.Entries.map(item => ({\r\n          CTemplateId: item.CTemplateId!,\r\n          CTemplateName: item.CTemplateName!,\r\n          CCreateDt: item.CCreateDt!,\r\n          CUpdateDt: item.CUpdateDt!,\r\n          CCreator: item.CCreator,\r\n          CUpdator: item.CUpdator,\r\n          CStatus: item.CStatus\r\n        }));\r\n        this.templateTotalRecords = res.TotalItems || 0;\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入模板資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  openCreateTemplateModal(ref: any) {\r\n    this.templateDetail = {\r\n      CTemplateName: '',\r\n      CTemplateType: 1,\r\n      CStatus: 1\r\n    };\r\n    this.selectedSpacesForTemplate = [];\r\n    this.spacePageIndex = 1; // 重置到第一頁\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.getSpacesForPickList();\r\n    this.dialogService.open(ref, { hasScroll: true });\r\n  }\r\n\r\n  getSpacesForPickList() {\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\r\n      body: {\r\n        PageIndex: this.spacePageIndex,\r\n        PageSize: this.spacePageSize,\r\n        CName: this.spaceSearchKeyword || null,\r\n        CLocation: this.spaceSearchLocation || null,\r\n        CStatus: 1 // 只顯示啟用的空間\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode === 0) {\r\n        this.availableSpaces = res.Entries.map(item => ({\r\n          CSpaceID: item.CSpaceID!,\r\n          CName: item.CName!,\r\n          CLocation: item.CLocation,\r\n          selected: this.selectedSpacesForTemplate.some(selected => selected.CSpaceID === item.CSpaceID)\r\n        }));\r\n        this.spaceTotalRecords = res.TotalItems!;\r\n        this.updateAllSpacesSelectedState();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入空間資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  onSpaceSearch() {\r\n    this.spacePageIndex = 1;\r\n    this.getSpacesForPickList();\r\n  }\r\n\r\n  onSpaceReset() {\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.spacePageIndex = 1;\r\n    this.getSpacesForPickList();\r\n  }\r\n\r\n  spacePageChanged(newPage: number) {\r\n    this.spacePageIndex = newPage;\r\n    this.getSpacesForPickList();\r\n  }\r\n\r\n  templatePageChanged(newPage: number) {\r\n    this.templatePageIndex = newPage;\r\n    this.getTemplateList();\r\n  }\r\n\r\n  toggleSpaceSelection(space: SpacePickListItem) {\r\n    space.selected = !space.selected;\r\n    if (space.selected) {\r\n      this.selectedSpacesForTemplate.push({ ...space });\r\n    } else {\r\n      const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\r\n      if (index > -1) {\r\n        this.selectedSpacesForTemplate.splice(index, 1);\r\n      }\r\n    }\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  toggleAllSpaces() {\r\n    this.allSpacesSelected = !this.allSpacesSelected;\r\n    this.availableSpaces.forEach(space => {\r\n      const wasSelected = space.selected;\r\n      space.selected = this.allSpacesSelected;\r\n      \r\n      if (this.allSpacesSelected && !wasSelected) {\r\n        // 新選中的空間，添加到已選列表\r\n        const exists = this.selectedSpacesForTemplate.find(s => s.CSpaceID === space.CSpaceID);\r\n        if (!exists) {\r\n          this.selectedSpacesForTemplate.push({ ...space });\r\n        }\r\n      } else if (!this.allSpacesSelected && wasSelected) {\r\n        // 取消選中的空間，從已選列表移除\r\n        const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\r\n        if (index > -1) {\r\n          this.selectedSpacesForTemplate.splice(index, 1);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  updateAllSpacesSelectedState() {\r\n    this.allSpacesSelected = this.availableSpaces.length > 0 && \r\n      this.availableSpaces.every(space => space.selected);\r\n  }\r\n\r\n  removeSelectedSpace(space: SpacePickListItem) {\r\n    const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\r\n    if (index > -1) {\r\n      this.selectedSpacesForTemplate.splice(index, 1);\r\n    }\r\n    \r\n    // 更新可用空間列表中的選擇狀態\r\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\r\n    if (availableSpace) {\r\n      availableSpace.selected = false;\r\n    }\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  onSubmitTemplate(ref: any) {\r\n    this.templateValidation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    if (this.selectedSpacesForTemplate.length === 0) {\r\n      this.message.showErrorMSG('請至少選擇一個空間');\r\n      return;\r\n    }\r\n\r\n    const templateDetails: SaveTemplateDetailArgs[] = this.selectedSpacesForTemplate.map(space => ({\r\n      CReleateId: space.CSpaceID,\r\n      CReleateName: space.CName\r\n    }));\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({\r\n      body: {\r\n        ...this.templateDetail,\r\n        Details: templateDetails\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"模板建立成功\");\r\n        ref.close();\r\n        this.getTemplateList(); // 刷新模板列表\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '建立模板失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  deleteTemplate(template: TemplateItem) {\r\n    if (confirm(`您確定要刪除模板「${template.CTemplateName}」嗎？`)) {\r\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\r\n        body: { CTemplateId: template.CTemplateId }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"模板刪除成功\");\r\n          \r\n          // 檢查刪除後當前頁是否還有資料，如果沒有則跳轉到上一頁\r\n          const currentPageStartIndex = (this.templatePageIndex - 1) * this.templatePageSize;\r\n          const remainingItems = this.templateTotalRecords - 1; // 刪除一筆後的總數\r\n          \r\n          if (remainingItems > 0 && currentPageStartIndex >= remainingItems) {\r\n            this.templatePageIndex = Math.max(1, this.templatePageIndex - 1);\r\n          }\r\n          \r\n          this.getTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '刪除模板失敗');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  viewTemplateDetail(template: TemplateItem, ref: any) {\r\n    this.selectedTemplateDetail = template;\r\n    this.getTemplateDetailSpaces(template.CTemplateId);\r\n    this.dialogService.open(ref, { hasScroll: true });\r\n  }\r\n\r\n  getTemplateDetailSpaces(templateId: number) {\r\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: { templateId: templateId }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode === 0) {\r\n        // 根據模板明細獲取空間資訊\r\n        const spaceIds = res.Entries.map(item => item.CReleateId).filter(id => id !== undefined) as number[];\r\n        if (spaceIds.length > 0) {\r\n          this.getSpaceDetailsByIds(spaceIds);\r\n        } else {\r\n          this.templateDetailSpaces = [];\r\n        }\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入模板明細失敗');\r\n        this.templateDetailSpaces = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  getSpaceDetailsByIds(spaceIds: number[]) {\r\n    // 由於沒有批次查詢空間的API，我們需要查詢所有空間然後篩選\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\r\n      body: {\r\n        PageIndex: 1,\r\n        PageSize: 1000, // 設定大一點確保能載入所有空間\r\n        CName: null,\r\n        CLocation: null,\r\n        CStatus: null\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode === 0) {\r\n        this.templateDetailSpaces = res.Entries\r\n          .filter(space => spaceIds.includes(space.CSpaceID!))\r\n          .map(space => ({\r\n            CSpaceID: space.CSpaceID!,\r\n            CName: space.CName!,\r\n            CLocation: space.CLocation,\r\n            CStatus: space.CStatus\r\n          }));\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入空間資料失敗');\r\n        this.templateDetailSpaces = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  templateValidation() {\r\n    this.valid.clear();\r\n    this.valid.required('[模板名稱]', this.templateDetail.CTemplateName);\r\n    this.valid.isStringMaxLength('[模板名稱]', this.templateDetail.CTemplateName, 100);\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"alert alert-info mb-4\"\r\n      style=\"border-left: 4px solid #4a90e2; background-color: #f8f9ff; border-radius: 6px;\">\r\n      <div class=\"d-flex align-items-center\">\r\n        <i class=\"fas fa-info-circle text-primary me-3\" style=\"font-size: 1.2rem;\"></i>\r\n        <div>\r\n          <p class=\"mb-0 text-muted\" style=\"font-size: 0.9rem;\">\r\n            在此頁面您可以管理系統中的各個空間資訊，包括新增、編輯、刪除空間，以及設定項目名稱、所屬區域和啟用狀態等。\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"spaceName\" class=\"label col-3\">項目名稱</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"spaceName\" nbInput class=\"w-full\" placeholder=\"搜尋項目名稱...\" [(ngModel)]=\"searchKeyword\"\r\n              (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"location\" class=\"label col-3\">所屬區域</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"location\" nbInput class=\"w-full\" placeholder=\"搜尋所屬區域...\" [(ngModel)]=\"searchLocation\"\r\n              (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"status\" class=\"label col-3\">狀態</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"status\" placeholder=\"選擇狀態...\" [(ngModel)]=\"searchStatus\" (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">啟用</nb-option>\r\n              <nb-option [value]=\"0\">停用</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢和重置按鈕移到這裡，放在搜尋條件的右下角 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-outline-secondary btn-sm me-2\" (click)=\"onReset()\">\r\n            <i class=\"fas fa-undo me-1\"></i>重置\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search me-1\"></i>查詢\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openCreateModal(createModal)\">\r\n            <i class=\"fas fa-plus me-1\"></i>新增空間\r\n          </button>\r\n          <button class=\"btn btn-warning mx-1 btn-sm\" (click)=\"openTemplateManagementModal(templateManagementModal)\">\r\n            <i class=\"fas fa-layer-group me-1\"></i>管理模板\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table custom-table\" style=\"min-width: 800px;\">\r\n        <thead>\r\n          <tr class=\"table-header\">\r\n            <th scope=\"col\" class=\"col-2\">所屬區域</th>\r\n            <th scope=\"col\" class=\"col-2\">項目名稱</th>\r\n            <th scope=\"col\" class=\"col-2\">狀態</th>\r\n            <th scope=\"col\" class=\"col-3\">建立時間</th>\r\n            <th scope=\"col\" class=\"col-3\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of spaceList\">\r\n            <td>{{ item.CLocation || '-' }}</td>\r\n            <td>{{ item.CName }}</td>\r\n            <td>\r\n              {{ item.CStatus === 1 ? '啟用' : '停用' }}\r\n            </td>\r\n            <td>{{ item.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>\r\n            <td class=\"table-actions\">\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-success btn-sm\" (click)=\"openEditModal(editModal, item)\">\r\n                <i class=\"fas fa-edit\"></i>編輯\r\n              </button>\r\n              <button *ngIf=\"isDelete\" class=\"btn btn-outline-danger btn-sm\" (click)=\"deleteSpace(item)\">\r\n                <i class=\"fas fa-trash\"></i>刪除\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\" [CollectionSize]=\"totalRecords\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<!-- 新增模態框 -->\r\n<ng-template #createModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-plus-circle me-2 text-success\"></i>新增空間\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"spaceName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                項目名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"spaceName\" class=\"form-control\" nbInput placeholder=\"請輸入項目名稱\"\r\n                  [(ngModel)]=\"spaceDetail.CName\" name=\"spaceName\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"location\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                所屬區域\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"location\" class=\"form-control\" nbInput placeholder=\"請輸入所屬區域\"\r\n                  [(ngModel)]=\"spaceDetail.CLocation\" name=\"location\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"status\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"status\" [(ngModel)]=\"spaceDetail.CStatus\" name=\"status\" placeholder=\"選擇狀態\"\r\n                    style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-check me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 編輯模態框 -->\r\n<ng-template #editModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-edit me-2 text-warning\"></i>編輯空間\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"locationEdit\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                所屬區域\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"locationEdit\" class=\"form-control\" nbInput placeholder=\"請輸入所屬區域\"\r\n                  [(ngModel)]=\"spaceDetail.CLocation\" name=\"location\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"spaceNameEdit\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                項目名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"spaceNameEdit\" class=\"form-control\" nbInput placeholder=\"請輸入項目名稱\"\r\n                  [(ngModel)]=\"spaceDetail.CName\" name=\"spaceName\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"statusEdit\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"statusEdit\" [(ngModel)]=\"spaceDetail.CStatus\" name=\"status\" placeholder=\"選擇狀態\"\r\n                    style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-save me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 模板管理模態框 -->\r\n<ng-template #templateManagementModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 900px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-layer-group me-2 text-warning\"></i>模板管理\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n        <div>\r\n          <button class=\"btn btn-primary btn-sm\" (click)=\"openCreateTemplateModal(createTemplateModal)\">\r\n            <i class=\"fas fa-plus me-1\"></i>新增模板\r\n          </button>\r\n        </div>\r\n        <div class=\"text-muted\">\r\n          <small>共 {{ templateTotalRecords }} 筆模板</small>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table custom-table\">\r\n          <thead>\r\n            <tr class=\"table-header\">\r\n              <th scope=\"col\" class=\"col-3\">模板名稱</th>\r\n              <th scope=\"col\" class=\"col-2\">狀態</th>\r\n              <th scope=\"col\" class=\"col-3\">建立時間</th>\r\n              <th scope=\"col\" class=\"col-2\">建立者</th>\r\n              <th scope=\"col\" class=\"col-2\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let template of templateList\">\r\n              <td>{{ template.CTemplateName }}</td>\r\n              <td>\r\n                <span class=\"badge\" [ngClass]=\"template.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                  {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n                </span>\r\n              </td>\r\n              <td>{{ template.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>\r\n              <td>{{ template.CCreator || '-' }}</td>\r\n              <td class=\"table-actions\">\r\n                <button class=\"btn btn-outline-info btn-sm me-1\" (click)=\"viewTemplateDetail(template, templateDetailModal)\">\r\n                  <i class=\"fas fa-eye\"></i>查看\r\n                </button>\r\n                <button class=\"btn btn-outline-danger btn-sm\" (click)=\"deleteTemplate(template)\">\r\n                  <i class=\"fas fa-trash\"></i>刪除\r\n                </button>\r\n              </td>\r\n            </tr>\r\n            <tr *ngIf=\"templateList.length === 0\">\r\n              <td colspan=\"5\" class=\"text-center text-muted py-4\">\r\n                <i class=\"fas fa-info-circle me-2\"></i>目前沒有任何模板\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 模板列表分頁 -->\r\n      <div class=\"d-flex justify-content-center mt-3\" *ngIf=\"templateTotalRecords > templatePageSize\">\r\n        <ngx-pagination \r\n          [(Page)]=\"templatePageIndex\" \r\n          [PageSize]=\"templatePageSize\" \r\n          [CollectionSize]=\"templateTotalRecords\"\r\n          (PageChange)=\"templatePageChanged($event)\">\r\n        </ngx-pagination>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-secondary px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>關閉\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 新增模板模態框 -->\r\n<ng-template #createTemplateModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-plus-circle me-2 text-success\"></i>新增模板\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"templateName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"templateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                  [(ngModel)]=\"templateDetail.CTemplateName\" name=\"templateName\"\r\n                  (keydown.control.enter)=\"onSubmitTemplate(ref)\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 選擇空間區域 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                選擇空間\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <!-- 搜尋區域 -->\r\n                <div class=\"row mb-3\">\r\n                  <div class=\"col-md-5\">\r\n                    <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋項目名稱...\"\r\n                      [(ngModel)]=\"spaceSearchKeyword\" (keyup.enter)=\"onSpaceSearch()\"\r\n                      style=\"height: 32px; border-radius: 4px;\" />\r\n                  </div>\r\n                  <div class=\"col-md-5\">\r\n                    <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋所屬區域...\"\r\n                      [(ngModel)]=\"spaceSearchLocation\" (keyup.enter)=\"onSpaceSearch()\"\r\n                      style=\"height: 32px; border-radius: 4px;\" />\r\n                  </div>\r\n                  <div class=\"col-md-2\">\r\n                    <button class=\"btn btn-sm btn-outline-secondary me-1\" (click)=\"onSpaceReset()\">\r\n                      <i class=\"fas fa-undo\"></i>\r\n                    </button>\r\n                    <button class=\"btn btn-sm btn-secondary\" (click)=\"onSpaceSearch()\">\r\n                      <i class=\"fas fa-search\"></i>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 可選空間列表 -->\r\n                <div class=\"border rounded p-3\" style=\"max-height: 300px; overflow-y: auto; background-color: #f8f9fa;\">\r\n                  <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <div class=\"d-flex align-items-center\">\r\n                      <input type=\"checkbox\" id=\"selectAll\" [checked]=\"allSpacesSelected\" \r\n                        (change)=\"toggleAllSpaces()\" class=\"me-2\">\r\n                      <label for=\"selectAll\" class=\"mb-0 font-weight-bold\">全選當頁空間</label>\r\n                    </div>\r\n                    <small class=\"text-muted\">\r\n                      共 {{ spaceTotalRecords }} 筆，第 {{ spacePageIndex }} / {{ Math.ceil(spaceTotalRecords / spacePageSize) }} 頁\r\n                    </small>\r\n                  </div>\r\n                  \r\n                  <div class=\"row\">\r\n                    <div class=\"col-md-6\" *ngFor=\"let space of availableSpaces\">\r\n                      <div class=\"form-check mb-2\">\r\n                        <input type=\"checkbox\" class=\"form-check-input\" [id]=\"'space-' + space.CSpaceID\"\r\n                          [checked]=\"space.selected\" (change)=\"toggleSpaceSelection(space)\">\r\n                        <label class=\"form-check-label\" [for]=\"'space-' + space.CSpaceID\">\r\n                          {{ space.CName }}\r\n                          <small class=\"text-muted d-block\">{{ space.CLocation || '-' }}</small>\r\n                        </label>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- 空間列表為空時的提示 -->\r\n                  <div *ngIf=\"availableSpaces.length === 0\" class=\"text-center text-muted py-3\">\r\n                    <i class=\"fas fa-info-circle me-2\"></i>沒有符合條件的空間\r\n                  </div>\r\n\r\n                  <!-- 分頁 -->\r\n                  <div class=\"d-flex justify-content-center mt-3\" *ngIf=\"spaceTotalRecords > spacePageSize\">\r\n                    <ngx-pagination [(Page)]=\"spacePageIndex\" [PageSize]=\"spacePageSize\" \r\n                      [CollectionSize]=\"spaceTotalRecords\" (PageChange)=\"spacePageChanged($event)\">\r\n                    </ngx-pagination>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 已選空間 -->\r\n                <div class=\"mt-3\" *ngIf=\"selectedSpacesForTemplate.length > 0\">\r\n                  <label class=\"mb-2 font-weight-bold\">已選擇的空間 ({{ selectedSpacesForTemplate.length }})</label>\r\n                  <div class=\"border rounded p-2\" style=\"max-height: 150px; overflow-y: auto;\">\r\n                    <span class=\"badge badge-primary me-1 mb-1\" *ngFor=\"let space of selectedSpacesForTemplate\">\r\n                      {{ space.CName }}\r\n                      <button type=\"button\" class=\"btn-close btn-close-white ms-1\" \r\n                        (click)=\"removeSelectedSpace(space)\" style=\"font-size: 0.7rem;\"></button>\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmitTemplate(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-check me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 查看模板明細模態框 -->\r\n<ng-template #templateDetailModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-eye me-2 text-info\"></i>模板明細\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <!-- 模板基本資訊 -->\r\n      <div class=\"card mb-4\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header\" style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-info-circle me-2 text-primary\"></i>基本資訊\r\n          </h6>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div class=\"row\">\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">模板名稱</label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CTemplateName || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">狀態</label>\r\n                <p class=\"mb-0\">\r\n                  <span class=\"badge\" [ngClass]=\"selectedTemplateDetail?.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                    {{ selectedTemplateDetail?.CStatus === 1 ? '啟用' : '停用' }}\r\n                  </span>\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">建立時間</label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CCreateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">建立者</label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CCreator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">更新時間</label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CUpdateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">更新者</label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CUpdator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 包含的空間列表 -->\r\n      <div class=\"card\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header d-flex justify-content-between align-items-center\" style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-home me-2 text-success\"></i>包含的空間\r\n          </h6>\r\n          <span class=\"badge badge-info\">共 {{ templateDetailSpaces.length }} 個空間</span>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div class=\"table-responsive\" *ngIf=\"templateDetailSpaces.length > 0\">\r\n            <table class=\"table table-sm table-striped\">\r\n              <thead>\r\n                <tr style=\"background-color: #f8f9fa;\">\r\n                  <th scope=\"col\" class=\"col-1\">#</th>\r\n                  <th scope=\"col\" class=\"col-4\">空間名稱</th>\r\n                  <th scope=\"col\" class=\"col-4\">所屬區域</th>\r\n                  <th scope=\"col\" class=\"col-3\">狀態</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                <tr *ngFor=\"let space of templateDetailSpaces; let i = index\">\r\n                  <td>{{ i + 1 }}</td>\r\n                  <td>\r\n                    <i class=\"fas fa-home me-2 text-muted\"></i>{{ space.CName }}\r\n                  </td>\r\n                  <td>\r\n                    <i class=\"fas fa-map-marker-alt me-2 text-muted\"></i>{{ space.CLocation || '-' }}\r\n                  </td>\r\n                  <td>\r\n                    <span class=\"badge badge-sm\" [ngClass]=\"space.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                      <i [class]=\"space.CStatus === 1 ? 'fas fa-check-circle' : 'fas fa-times-circle'\" class=\"me-1\"></i>\r\n                      {{ space.CStatus === 1 ? '啟用' : '停用' }}\r\n                    </span>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n          \r\n          <!-- 沒有空間時的提示 -->\r\n          <div *ngIf=\"templateDetailSpaces.length === 0\" class=\"text-center text-muted py-4\">\r\n            <i class=\"fas fa-info-circle me-2\"></i>此模板尚未包含任何空間\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-secondary px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>關閉\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,kCAAkC;AAMhE,SAASC,GAAG,QAAQ,MAAM;;;;;;;;;;;;;;IC0DhBC,EAAA,CAAAC,cAAA,iBAAiG;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,0DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,cAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,eAAA,CAAAH,cAAA,CAA4B;IAAA,EAAC;IAC9FR,EAAA,CAAAY,SAAA,YAAgC;IAAAZ,EAAA,CAAAa,MAAA,gCAClC;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IA4BLd,EAAA,CAAAC,cAAA,iBAAyG;IAAzCD,EAAA,CAAAE,UAAA,mBAAAa,gEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAY,YAAA,GAAAnB,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAc,aAAA,CAAAD,YAAA,EAAAF,OAAA,CAA8B;IAAA,EAAC;IACtGjB,EAAA,CAAAY,SAAA,YAA2B;IAAAZ,EAAA,CAAAa,MAAA,oBAC7B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IACTd,EAAA,CAAAC,cAAA,iBAA2F;IAA5BD,EAAA,CAAAE,UAAA,mBAAAmB,gEAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA;MAAA,MAAAL,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiB,WAAA,CAAAN,OAAA,CAAiB;IAAA,EAAC;IACxFjB,EAAA,CAAAY,SAAA,YAA4B;IAAAZ,EAAA,CAAAa,MAAA,oBAC9B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;IAZXd,EADF,CAAAC,cAAA,SAAmC,SAC7B;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpCd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACzBd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAA+C;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACxDd,EAAA,CAAAC,cAAA,cAA0B;IAIxBD,EAHA,CAAAwB,UAAA,KAAAC,uCAAA,qBAAyG,KAAAC,uCAAA,qBAGd;IAI/F1B,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAdCd,EAAA,CAAA2B,SAAA,GAA2B;IAA3B3B,EAAA,CAAA4B,iBAAA,CAAAX,OAAA,CAAAY,SAAA,QAA2B;IAC3B7B,EAAA,CAAA2B,SAAA,GAAgB;IAAhB3B,EAAA,CAAA4B,iBAAA,CAAAX,OAAA,CAAAa,KAAA,CAAgB;IAElB9B,EAAA,CAAA2B,SAAA,GACF;IADE3B,EAAA,CAAA+B,kBAAA,MAAAd,OAAA,CAAAe,OAAA,8CACF;IACIhC,EAAA,CAAA2B,SAAA,GAA+C;IAA/C3B,EAAA,CAAA4B,iBAAA,CAAA5B,EAAA,CAAAiC,WAAA,OAAAhB,OAAA,CAAAiB,SAAA,sBAA+C;IAExClC,EAAA,CAAA2B,SAAA,GAAc;IAAd3B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,QAAA,CAAc;IAGdpC,EAAA,CAAA2B,SAAA,EAAc;IAAd3B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA+B,QAAA,CAAc;;;;;;IAoB/BrC,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,YAAoD;IAAAZ,EAAA,CAAAa,MAAA,gCACtD;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAoC,+DAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,IAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAE5FvC,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAOPd,EALV,CAAAC,cAAA,uBAAgC,cACb,cACK,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAA2C,gBAAA,2BAAAC,uEAAAC,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoC,IAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,EAAAe,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,GAAAe,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAA+B;IAKzC7C,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAKAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAA2C,gBAAA,2BAAAK,uEAAAH,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoC,IAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,EAAAgB,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,GAAAgB,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAAmC;IAK7C7C,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAKAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,qBAEJ;IADDD,EAAA,CAAA2C,gBAAA,2BAAAM,2EAAAJ,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoC,IAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAf,OAAA,EAAAa,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAf,OAAA,GAAAa,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAAiC;IAGpD7C,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IASlBb,EATkB,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAAgD,gEAAA;MAAA,MAAAX,OAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,IAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAExEvC,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAAiD,gEAAA;MAAA,MAAAZ,OAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,IAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA8C,QAAA,CAAAb,OAAA,CAAa;IAAA,EAAC;IAE1DvC,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA/DMd,EAAA,CAAA2B,SAAA,IAA+B;IAA/B3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,CAA+B;IAgB/B9B,EAAA,CAAA2B,SAAA,GAAmC;IAAnC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,CAAmC;IAgBZ7B,EAAA,CAAA2B,SAAA,GAAiC;IAAjC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAf,OAAA,CAAiC;IAE3ChC,EAAA,CAAA2B,SAAA,EAAW;IAAX3B,EAAA,CAAAmC,UAAA,YAAW;IAKXnC,EAAA,CAAA2B,SAAA,GAAW;IAAX3B,EAAA,CAAAmC,UAAA,YAAW;;;;;;IA+BpCnC,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,YAA6C;IAAAZ,EAAA,CAAAa,MAAA,gCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAoD,+DAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAI,aAAA,CAAAoD,IAAA,EAAAf,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAa,OAAA,CAAY;IAAA,EAAC;IAE5FvD,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAOPd,EALV,CAAAC,cAAA,uBAAgC,cACb,cACK,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAA2C,gBAAA,2BAAAc,uEAAAZ,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,EAAAgB,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,GAAAgB,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAAmC;IAK7C7C,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAKAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAA2C,gBAAA,2BAAAe,uEAAAb,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,EAAAe,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,GAAAe,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAA+B;IAKzC7C,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAKAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,qBAEJ;IADGD,EAAA,CAAA2C,gBAAA,2BAAAgB,2EAAAd,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAf,OAAA,EAAAa,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAf,OAAA,GAAAa,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAAiC;IAGxD7C,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IASlBb,EATkB,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAA0D,gEAAA;MAAA,MAAAL,OAAA,GAAAvD,EAAA,CAAAI,aAAA,CAAAoD,IAAA,EAAAf,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAa,OAAA,CAAY;IAAA,EAAC;IAExEvD,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAA2D,gEAAA;MAAA,MAAAN,OAAA,GAAAvD,EAAA,CAAAI,aAAA,CAAAoD,IAAA,EAAAf,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA8C,QAAA,CAAAG,OAAA,CAAa;IAAA,EAAC;IAE1DvD,EAAA,CAAAY,SAAA,aAAgC;IAAAZ,EAAA,CAAAa,MAAA,qBAClC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA/DMd,EAAA,CAAA2B,SAAA,IAAmC;IAAnC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,CAAmC;IAgBnC7B,EAAA,CAAA2B,SAAA,GAA+B;IAA/B3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,CAA+B;IAgBJ9B,EAAA,CAAA2B,SAAA,GAAiC;IAAjC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAf,OAAA,CAAiC;IAE/ChC,EAAA,CAAA2B,SAAA,EAAW;IAAX3B,EAAA,CAAAmC,UAAA,YAAW;IAKXnC,EAAA,CAAA2B,SAAA,GAAW;IAAX3B,EAAA,CAAAmC,UAAA,YAAW;;;;;;IAiE5BnC,EADF,CAAAC,cAAA,SAA0C,SACpC;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEnCd,EADF,CAAAC,cAAA,SAAI,eAC2F;IAC3FD,EAAA,CAAAa,MAAA,GACF;IACFb,EADE,CAAAc,YAAA,EAAO,EACJ;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAmD;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC5Dd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,IAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAErCd,EADF,CAAAC,cAAA,cAA0B,kBACqF;IAA5DD,EAAA,CAAAE,UAAA,mBAAA4D,sEAAA;MAAA,MAAAC,YAAA,GAAA/D,EAAA,CAAAI,aAAA,CAAA4D,IAAA,EAAA9C,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAA0D,uBAAA,GAAAjE,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA4D,kBAAA,CAAAH,YAAA,EAAAE,uBAAA,CAAiD;IAAA,EAAC;IAC1GjE,EAAA,CAAAY,SAAA,aAA0B;IAAAZ,EAAA,CAAAa,MAAA,qBAC5B;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAAiF;IAAnCD,EAAA,CAAAE,UAAA,mBAAAiE,sEAAA;MAAA,MAAAJ,YAAA,GAAA/D,EAAA,CAAAI,aAAA,CAAA4D,IAAA,EAAA9C,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA8D,cAAA,CAAAL,YAAA,CAAwB;IAAA,EAAC;IAC9E/D,EAAA,CAAAY,SAAA,aAA4B;IAAAZ,EAAA,CAAAa,MAAA,qBAC9B;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACN,EACF;;;;IAhBCd,EAAA,CAAA2B,SAAA,GAA4B;IAA5B3B,EAAA,CAAA4B,iBAAA,CAAAmC,YAAA,CAAAM,aAAA,CAA4B;IAEVrE,EAAA,CAAA2B,SAAA,GAAwE;IAAxE3B,EAAA,CAAAmC,UAAA,YAAA4B,YAAA,CAAA/B,OAAA,6CAAwE;IAC1FhC,EAAA,CAAA2B,SAAA,EACF;IADE3B,EAAA,CAAA+B,kBAAA,MAAAgC,YAAA,CAAA/B,OAAA,8CACF;IAEEhC,EAAA,CAAA2B,SAAA,GAAmD;IAAnD3B,EAAA,CAAA4B,iBAAA,CAAA5B,EAAA,CAAAiC,WAAA,OAAA8B,YAAA,CAAA7B,SAAA,sBAAmD;IACnDlC,EAAA,CAAA2B,SAAA,GAA8B;IAA9B3B,EAAA,CAAA4B,iBAAA,CAAAmC,YAAA,CAAAO,QAAA,QAA8B;;;;;IAWlCtE,EADF,CAAAC,cAAA,SAAsC,aACgB;IAClDD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,wDACzC;IACFb,EADE,CAAAc,YAAA,EAAK,EACF;;;;;;IAOTd,EADF,CAAAC,cAAA,cAAgG,yBAKjD;IAH3CD,EAAA,CAAA2C,gBAAA,wBAAA4B,mFAAA1B,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAAlE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAmE,iBAAA,EAAA5B,MAAA,MAAAvC,MAAA,CAAAmE,iBAAA,GAAA5B,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAA4B;IAG5B7C,EAAA,CAAAE,UAAA,wBAAAqE,mFAAA1B,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAAlE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAcJ,MAAA,CAAAoE,mBAAA,CAAA7B,MAAA,CAA2B;IAAA,EAAC;IAE9C7C,EADE,CAAAc,YAAA,EAAiB,EACb;;;;IALFd,EAAA,CAAA2B,SAAA,EAA4B;IAA5B3B,EAAA,CAAAqD,gBAAA,SAAA/C,MAAA,CAAAmE,iBAAA,CAA4B;IAE5BzE,EADA,CAAAmC,UAAA,aAAA7B,MAAA,CAAAqE,gBAAA,CAA6B,mBAAArE,MAAA,CAAAsE,oBAAA,CACU;;;;;;IAjE3C5E,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,YAAoD;IAAAZ,EAAA,CAAAa,MAAA,gCACtD;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAA2E,+DAAA;MAAA,MAAAC,OAAA,GAAA9E,EAAA,CAAAI,aAAA,CAAA2E,IAAA,EAAAtC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAoC,OAAA,CAAY;IAAA,EAAC;IAE5F9E,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAKXd,EAHN,CAAAC,cAAA,uBAAgC,cACsC,UAC7D,kBAC2F;IAAvDD,EAAA,CAAAE,UAAA,mBAAA8E,gEAAA;MAAAhF,EAAA,CAAAI,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAA0E,uBAAA,GAAAjF,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA4E,uBAAA,CAAAD,uBAAA,CAA4C;IAAA,EAAC;IAC3FjF,EAAA,CAAAY,SAAA,aAAgC;IAAAZ,EAAA,CAAAa,MAAA,iCAClC;IACFb,EADE,CAAAc,YAAA,EAAS,EACL;IAEJd,EADF,CAAAC,cAAA,eAAwB,aACf;IAAAD,EAAA,CAAAa,MAAA,IAAgC;IAE3Cb,EAF2C,CAAAc,YAAA,EAAQ,EAC3C,EACF;IAMEd,EAJR,CAAAC,cAAA,eAA8B,iBACM,aACzB,cACoB,cACO;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACrCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,0BAAG;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACtCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAEpCb,EAFoC,CAAAc,YAAA,EAAK,EAClC,EACC;IACRd,EAAA,CAAAC,cAAA,aAAO;IAmBLD,EAlBA,CAAAwB,UAAA,KAAA2D,4CAAA,kBAA0C,KAAAC,4CAAA,iBAkBJ;IAO5CpF,EAFI,CAAAc,YAAA,EAAQ,EACF,EACJ;IAGNd,EAAA,CAAAwB,UAAA,KAAA6D,6CAAA,kBAAgG;IAQlGrF,EAAA,CAAAc,YAAA,EAAe;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADtBD,EAAA,CAAAE,UAAA,mBAAAoF,gEAAA;MAAA,MAAAR,OAAA,GAAA9E,EAAA,CAAAI,aAAA,CAAA2E,IAAA,EAAAtC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAoC,OAAA,CAAY;IAAA,EAAC;IAE3D9E,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA5DKd,EAAA,CAAA2B,SAAA,IAAgC;IAAhC3B,EAAA,CAAA+B,kBAAA,YAAAzB,MAAA,CAAAsE,oBAAA,wBAAgC;IAgBZ5E,EAAA,CAAA2B,SAAA,IAAe;IAAf3B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAiF,YAAA,CAAe;IAkBnCvF,EAAA,CAAA2B,SAAA,EAA+B;IAA/B3B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAiF,YAAA,CAAAC,MAAA,OAA+B;IAUOxF,EAAA,CAAA2B,SAAA,EAA6C;IAA7C3B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAsE,oBAAA,GAAAtE,MAAA,CAAAqE,gBAAA,CAA6C;;;;;;IAmG5E3E,EAFJ,CAAAC,cAAA,cAA4D,eAC7B,iBAEyC;IAAvCD,EAAA,CAAAE,UAAA,oBAAAuF,sEAAA;MAAA,MAAAC,SAAA,GAAA1F,EAAA,CAAAI,aAAA,CAAAuF,IAAA,EAAAzE,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAAsF,oBAAA,CAAAF,SAAA,CAA2B;IAAA,EAAC;IADnE1F,EAAA,CAAAc,YAAA,EACoE;IACpEd,EAAA,CAAAC,cAAA,iBAAkE;IAChED,EAAA,CAAAa,MAAA,GACA;IAAAb,EAAA,CAAAC,cAAA,iBAAkC;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IAGpEb,EAHoE,CAAAc,YAAA,EAAQ,EAChE,EACJ,EACF;;;;IAP8Cd,EAAA,CAAA2B,SAAA,GAAgC;IAC9E3B,EAD8C,CAAAmC,UAAA,kBAAAuD,SAAA,CAAAG,QAAA,CAAgC,YAAAH,SAAA,CAAAI,QAAA,CACpD;IACI9F,EAAA,CAAA2B,SAAA,EAAiC;IAAjC3B,EAAA,CAAAmC,UAAA,mBAAAuD,SAAA,CAAAG,QAAA,CAAiC;IAC/D7F,EAAA,CAAA2B,SAAA,EACA;IADA3B,EAAA,CAAA+B,kBAAA,MAAA2D,SAAA,CAAA5D,KAAA,MACA;IAAkC9B,EAAA,CAAA2B,SAAA,GAA4B;IAA5B3B,EAAA,CAAA4B,iBAAA,CAAA8D,SAAA,CAAA7D,SAAA,QAA4B;;;;;IAOtE7B,EAAA,CAAAC,cAAA,eAA8E;IAC5ED,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,8DACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;;IAIJd,EADF,CAAAC,cAAA,cAA0F,yBAET;IAD/DD,EAAA,CAAA2C,gBAAA,wBAAAoD,mFAAAlD,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAA4F,IAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAA2F,cAAA,EAAApD,MAAA,MAAAvC,MAAA,CAAA2F,cAAA,GAAApD,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAAyB;IACF7C,EAAA,CAAAE,UAAA,wBAAA6F,mFAAAlD,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAA4F,IAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAcJ,MAAA,CAAA4F,gBAAA,CAAArD,MAAA,CAAwB;IAAA,EAAC;IAEhF7C,EADE,CAAAc,YAAA,EAAiB,EACb;;;;IAHYd,EAAA,CAAA2B,SAAA,EAAyB;IAAzB3B,EAAA,CAAAqD,gBAAA,SAAA/C,MAAA,CAAA2F,cAAA,CAAyB;IACvCjG,EADwC,CAAAmC,UAAA,aAAA7B,MAAA,CAAA6F,aAAA,CAA0B,mBAAA7F,MAAA,CAAA8F,iBAAA,CAC9B;;;;;;IAStCpG,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAa,MAAA,GACA;IAAAb,EAAA,CAAAC,cAAA,kBACkE;IAAhED,EAAA,CAAAE,UAAA,mBAAAmG,6EAAA;MAAA,MAAAC,SAAA,GAAAtG,EAAA,CAAAI,aAAA,CAAAmG,IAAA,EAAArF,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAkG,mBAAA,CAAAF,SAAA,CAA0B;IAAA,EAAC;IACxCtG,EADoE,CAAAc,YAAA,EAAS,EACtE;;;;IAHLd,EAAA,CAAA2B,SAAA,EACA;IADA3B,EAAA,CAAA+B,kBAAA,MAAAuE,SAAA,CAAAxE,KAAA,MACA;;;;;IAJJ9B,EADF,CAAAC,cAAA,eAA+D,iBACxB;IAAAD,EAAA,CAAAa,MAAA,GAA+C;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC5Fd,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAAwB,UAAA,IAAAiF,oDAAA,oBAA4F;IAMhGzG,EADE,CAAAc,YAAA,EAAM,EACF;;;;IARiCd,EAAA,CAAA2B,SAAA,GAA+C;IAA/C3B,EAAA,CAAA+B,kBAAA,2CAAAzB,MAAA,CAAAoG,yBAAA,CAAAlB,MAAA,MAA+C;IAEpBxF,EAAA,CAAA2B,SAAA,GAA4B;IAA5B3B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAoG,yBAAA,CAA4B;;;;;;IAvGxG1G,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,YAAoD;IAAAZ,EAAA,CAAAa,MAAA,gCACtD;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAyG,+DAAA;MAAA,MAAAC,OAAA,GAAA5G,EAAA,CAAAI,aAAA,CAAAyG,IAAA,EAAApE,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAkE,OAAA,CAAY;IAAA,EAAC;IAE5F5G,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAQPd,EANV,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,kBAI6C;IAFvED,EAAA,CAAA2C,gBAAA,2BAAAmE,uEAAAjE,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyG,cAAA,CAAA1C,aAAA,EAAAxB,MAAA,MAAAvC,MAAA,CAAAyG,cAAA,CAAA1C,aAAA,GAAAxB,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAA0C;IAC1C7C,EAAA,CAAAE,UAAA,mCAAA8G,+EAAA;MAAA,MAAAJ,OAAA,GAAA5G,EAAA,CAAAI,aAAA,CAAAyG,IAAA,EAAApE,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAyBJ,MAAA,CAAA2G,gBAAA,CAAAL,OAAA,CAAqB;IAAA,EAAC;IAKzD5G,EAPQ,CAAAc,YAAA,EAGyE,EACrE,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,kBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAKFd,EAJN,CAAAC,cAAA,eAA8B,gBAEN,gBACE,kBAG0B;IAD5CD,EAAA,CAAA2C,gBAAA,2BAAAuE,uEAAArE,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAA6G,kBAAA,EAAAtE,MAAA,MAAAvC,MAAA,CAAA6G,kBAAA,GAAAtE,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAAgC;IAAC7C,EAAA,CAAAE,UAAA,yBAAAkH,qEAAA;MAAApH,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAA+G,aAAA,EAAe;IAAA,EAAC;IAEpErH,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,gBAAsB,kBAG0B;IAD5CD,EAAA,CAAA2C,gBAAA,2BAAA2E,uEAAAzE,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAiH,mBAAA,EAAA1E,MAAA,MAAAvC,MAAA,CAAAiH,mBAAA,GAAA1E,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAAiC;IAAC7C,EAAA,CAAAE,UAAA,yBAAAsH,qEAAA;MAAAxH,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAA+G,aAAA,EAAe;IAAA,EAAC;IAErErH,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,gBAAsB,mBAC2D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAuH,gEAAA;MAAAzH,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoH,YAAA,EAAc;IAAA,EAAC;IAC5E1H,EAAA,CAAAY,SAAA,cAA2B;IAC7BZ,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,mBAAmE;IAA1BD,EAAA,CAAAE,UAAA,mBAAAyH,gEAAA;MAAA3H,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA+G,aAAA,EAAe;IAAA,EAAC;IAChErH,EAAA,CAAAY,SAAA,cAA6B;IAGnCZ,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAMAd,EAHN,CAAAC,cAAA,gBAAwG,gBAClC,cAC3B,kBAEO;IAA1CD,EAAA,CAAAE,UAAA,oBAAA0H,gEAAA;MAAA5H,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAAuH,eAAA,EAAiB;IAAA,EAAC;IAD9B7H,EAAA,CAAAc,YAAA,EAC4C;IAC5Cd,EAAA,CAAAC,cAAA,kBAAqD;IAAAD,EAAA,CAAAa,MAAA,4CAAM;IAC7Db,EAD6D,CAAAc,YAAA,EAAQ,EAC/D;IACNd,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAa,MAAA,IACF;IACFb,EADE,CAAAc,YAAA,EAAQ,EACJ;IAENd,EAAA,CAAAC,cAAA,eAAiB;IACfD,EAAA,CAAAwB,UAAA,KAAAsG,6CAAA,mBAA4D;IAU9D9H,EAAA,CAAAc,YAAA,EAAM;IAQNd,EALA,CAAAwB,UAAA,KAAAuG,6CAAA,mBAA8E,KAAAC,6CAAA,kBAKY;IAK5FhI,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAAwB,UAAA,KAAAyG,6CAAA,mBAA+D;IAe3EjI,EALU,CAAAc,YAAA,EAAM,EACF,EACF,EACF,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAAgI,gEAAA;MAAA,MAAAtB,OAAA,GAAA5G,EAAA,CAAAI,aAAA,CAAAyG,IAAA,EAAApE,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAkE,OAAA,CAAY;IAAA,EAAC;IAExE5G,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAAiI,gEAAA;MAAA,MAAAvB,OAAA,GAAA5G,EAAA,CAAAI,aAAA,CAAAyG,IAAA,EAAApE,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA2G,gBAAA,CAAAL,OAAA,CAAqB;IAAA,EAAC;IAElE5G,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA1GMd,EAAA,CAAA2B,SAAA,IAA0C;IAA1C3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyG,cAAA,CAAA1C,aAAA,CAA0C;IAqBtCrE,EAAA,CAAA2B,SAAA,GAAgC;IAAhC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAA6G,kBAAA,CAAgC;IAKhCnH,EAAA,CAAA2B,SAAA,GAAiC;IAAjC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAiH,mBAAA,CAAiC;IAiBKvH,EAAA,CAAA2B,SAAA,GAA6B;IAA7B3B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAA8H,iBAAA,CAA6B;IAKnEpI,EAAA,CAAA2B,SAAA,GACF;IADE3B,EAAA,CAAAqI,kBAAA,aAAA/H,MAAA,CAAA8F,iBAAA,0BAAA9F,MAAA,CAAA2F,cAAA,SAAA3F,MAAA,CAAAgI,IAAA,CAAAC,IAAA,CAAAjI,MAAA,CAAA8F,iBAAA,GAAA9F,MAAA,CAAA6F,aAAA,cACF;IAIwCnG,EAAA,CAAA2B,SAAA,GAAkB;IAAlB3B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAkI,eAAA,CAAkB;IAatDxI,EAAA,CAAA2B,SAAA,EAAkC;IAAlC3B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAkI,eAAA,CAAAhD,MAAA,OAAkC;IAKSxF,EAAA,CAAA2B,SAAA,EAAuC;IAAvC3B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8F,iBAAA,GAAA9F,MAAA,CAAA6F,aAAA,CAAuC;IAQvEnG,EAAA,CAAA2B,SAAA,EAA0C;IAA1C3B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAoG,yBAAA,CAAAlB,MAAA,KAA0C;;;;;IAsH3DxF,EADF,CAAAC,cAAA,SAA8D,SACxD;IAAAD,EAAA,CAAAa,MAAA,GAAW;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpBd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAA2C;IAAAZ,EAAA,CAAAa,MAAA,GAC7C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,GACvD;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEHd,EADF,CAAAC,cAAA,SAAI,iBACiG;IACjGD,EAAA,CAAAY,SAAA,cAAkG;IAClGZ,EAAA,CAAAa,MAAA,IACF;IAEJb,EAFI,CAAAc,YAAA,EAAO,EACJ,EACF;;;;;IAbCd,EAAA,CAAA2B,SAAA,GAAW;IAAX3B,EAAA,CAAA4B,iBAAA,CAAA6G,KAAA,KAAW;IAE8BzI,EAAA,CAAA2B,SAAA,GAC7C;IAD6C3B,EAAA,CAAA+B,kBAAA,KAAA2G,SAAA,CAAA5G,KAAA,MAC7C;IAEuD9B,EAAA,CAAA2B,SAAA,GACvD;IADuD3B,EAAA,CAAA+B,kBAAA,KAAA2G,SAAA,CAAA7G,SAAA,aACvD;IAE+B7B,EAAA,CAAA2B,SAAA,GAAqE;IAArE3B,EAAA,CAAAmC,UAAA,YAAAuG,SAAA,CAAA1G,OAAA,6CAAqE;IAC7FhC,EAAA,CAAA2B,SAAA,EAA6E;IAA7E3B,EAAA,CAAA2I,UAAA,CAAAD,SAAA,CAAA1G,OAAA,uDAA6E;IAChFhC,EAAA,CAAA2B,SAAA,EACF;IADE3B,EAAA,CAAA+B,kBAAA,MAAA2G,SAAA,CAAA1G,OAAA,8CACF;;;;;IAnBFhC,EAJR,CAAAC,cAAA,cAAsE,iBACxB,YACnC,cACkC,cACP;IAAAD,EAAA,CAAAa,MAAA,QAAC;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAEpCb,EAFoC,CAAAc,YAAA,EAAK,EAClC,EACC;IACRd,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAwB,UAAA,KAAAoH,mDAAA,kBAA8D;IAiBpE5I,EAFI,CAAAc,YAAA,EAAQ,EACF,EACJ;;;;IAjBsBd,EAAA,CAAA2B,SAAA,IAAyB;IAAzB3B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAuI,oBAAA,CAAyB;;;;;IAoBrD7I,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,0EACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;;IAzGVd,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,aAAyC;IAAAZ,EAAA,CAAAa,MAAA,gCAC3C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAA4I,+DAAA;MAAA,MAAAC,OAAA,GAAA/I,EAAA,CAAAI,aAAA,CAAA4I,IAAA,EAAAvG,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAqG,OAAA,CAAY;IAAA,EAAC;IAE5F/I,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAMXd,EAJN,CAAAC,cAAA,uBAAgC,eAEgD,eACkB,eAChD;IAC1CD,EAAA,CAAAY,SAAA,cAAoD;IAAAZ,EAAA,CAAAa,MAAA,iCACtD;IACFb,EADE,CAAAc,YAAA,EAAK,EACD;IAKEd,EAJR,CAAAC,cAAA,gBAAuB,eACJ,eACO,gBACS,kBACgB;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACvDd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAAkD;IAEtEb,EAFsE,CAAAc,YAAA,EAAI,EAClE,EACF;IAGFd,EAFJ,CAAAC,cAAA,eAAsB,gBACS,kBACgB;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAEnDd,EADF,CAAAC,cAAA,cAAgB,gBAC8F;IAC1GD,EAAA,CAAAa,MAAA,IACF;IAGNb,EAHM,CAAAc,YAAA,EAAO,EACL,EACA,EACF;IAGFd,EAFJ,CAAAC,cAAA,eAAsB,gBACS,kBACgB;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACvDd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,eAAsB,gBACS,kBACgB;IAAAD,EAAA,CAAAa,MAAA,0BAAG;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACtDd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAEjEb,EAFiE,CAAAc,YAAA,EAAI,EAC7D,EACF;IAGFd,EAFJ,CAAAC,cAAA,eAAsB,gBACS,kBACgB;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACvDd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,eAAsB,gBACS,kBACgB;IAAAD,EAAA,CAAAa,MAAA,0BAAG;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACtDd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAKvEb,EALuE,CAAAc,YAAA,EAAI,EAC7D,EACF,EACF,EACF,EACF;IAKFd,EAFJ,CAAAC,cAAA,gBAAyE,gBACyE,eAClG;IAC1CD,EAAA,CAAAY,SAAA,cAA6C;IAAAZ,EAAA,CAAAa,MAAA,uCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAa,MAAA,IAAuC;IACxEb,EADwE,CAAAc,YAAA,EAAO,EACzE;IACNd,EAAA,CAAAC,cAAA,gBAAuB;IAgCrBD,EA/BA,CAAAwB,UAAA,KAAAyH,6CAAA,oBAAsE,KAAAC,6CAAA,mBA+Ba;IAKzFlJ,EAFI,CAAAc,YAAA,EAAM,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADtBD,EAAA,CAAAE,UAAA,mBAAAiJ,gEAAA;MAAA,MAAAJ,OAAA,GAAA/I,EAAA,CAAAI,aAAA,CAAA4I,IAAA,EAAAvG,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAqG,OAAA,CAAY;IAAA,EAAC;IAE3D/I,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA9FoBd,EAAA,CAAA2B,SAAA,IAAkD;IAAlD3B,EAAA,CAAA4B,iBAAA,EAAAtB,MAAA,CAAA8I,sBAAA,kBAAA9I,MAAA,CAAA8I,sBAAA,CAAA/E,aAAA,SAAkD;IAO5CrE,EAAA,CAAA2B,SAAA,GAAuF;IAAvF3B,EAAA,CAAAmC,UAAA,aAAA7B,MAAA,CAAA8I,sBAAA,kBAAA9I,MAAA,CAAA8I,sBAAA,CAAApH,OAAA,8CAAuF;IACzGhC,EAAA,CAAA2B,SAAA,EACF;IADE3B,EAAA,CAAA+B,kBAAA,OAAAzB,MAAA,CAAA8I,sBAAA,kBAAA9I,MAAA,CAAA8I,sBAAA,CAAApH,OAAA,+CACF;IAOchC,EAAA,CAAA2B,SAAA,GAA2E;IAA3E3B,EAAA,CAAA4B,iBAAA,CAAA5B,EAAA,CAAAiC,WAAA,SAAA3B,MAAA,CAAA8I,sBAAA,kBAAA9I,MAAA,CAAA8I,sBAAA,CAAAlH,SAAA,6BAA2E;IAM3ElC,EAAA,CAAA2B,SAAA,GAA6C;IAA7C3B,EAAA,CAAA4B,iBAAA,EAAAtB,MAAA,CAAA8I,sBAAA,kBAAA9I,MAAA,CAAA8I,sBAAA,CAAA9E,QAAA,SAA6C;IAM7CtE,EAAA,CAAA2B,SAAA,GAA2E;IAA3E3B,EAAA,CAAA4B,iBAAA,CAAA5B,EAAA,CAAAiC,WAAA,SAAA3B,MAAA,CAAA8I,sBAAA,kBAAA9I,MAAA,CAAA8I,sBAAA,CAAAC,SAAA,6BAA2E;IAM3ErJ,EAAA,CAAA2B,SAAA,GAA6C;IAA7C3B,EAAA,CAAA4B,iBAAA,EAAAtB,MAAA,CAAA8I,sBAAA,kBAAA9I,MAAA,CAAA8I,sBAAA,CAAAE,QAAA,SAA6C;IAapCtJ,EAAA,CAAA2B,SAAA,GAAuC;IAAvC3B,EAAA,CAAA+B,kBAAA,YAAAzB,MAAA,CAAAuI,oBAAA,CAAArD,MAAA,wBAAuC;IAGvCxF,EAAA,CAAA2B,SAAA,GAAqC;IAArC3B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAuI,oBAAA,CAAArD,MAAA,KAAqC;IA+B9DxF,EAAA,CAAA2B,SAAA,EAAuC;IAAvC3B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAuI,oBAAA,CAAArD,MAAA,OAAuC;;;AD5jBvD,OAAM,MAAO+D,cAAe,SAAQzJ,aAAa;EAG/C0J,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,aAA2B,EAC3BC,gBAAiC,EACjCC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IARf,KAAAxB,IAAI,GAAGA,IAAI,CAAC,CAAC;IAaJ,KAAAyB,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,SAAS,GAA2B,EAAE;IACtC,KAAApH,WAAW,GAAqB,EAAE;IAClC,KAAAqH,aAAa,GAAW,EAAE;IAC1B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAkB,IAAI;IAElC;IACA,KAAA/E,YAAY,GAAmB,EAAE;IACjC,KAAAwB,cAAc,GAAqB,EAAE;IACrC,KAAAtC,iBAAiB,GAAG,CAAC;IACrB,KAAAE,gBAAgB,GAAG,EAAE;IACrB,KAAAC,oBAAoB,GAAG,CAAC;IAExB;IACA,KAAAwE,sBAAsB,GAAwB,IAAI;IAClD,KAAAP,oBAAoB,GAAwB,EAAE;IAE9C;IACA,KAAAL,eAAe,GAAwB,EAAE;IACzC,KAAA9B,yBAAyB,GAAwB,EAAE;IACnD,KAAAT,cAAc,GAAG,CAAC;IAClB,KAAAE,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAe,kBAAkB,GAAG,EAAE;IACvB,KAAAI,mBAAmB,GAAG,EAAE;IAExB;IACA,KAAAgD,iBAAiB,GAAG,KAAK;IACzB,KAAAnC,iBAAiB,GAAG,KAAK;EAnCzB;EAqCSoC,QAAQA,CAAA;IACf,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,OAAO,IAAI,CAACd,aAAa,CAACe,6BAA6B,CAAC;MACtDC,IAAI,EAAE;QACJC,SAAS,EAAE,IAAI,CAACX,SAAS;QACzBY,QAAQ,EAAE,IAAI,CAACb,QAAQ;QACvBlI,KAAK,EAAE,IAAI,CAACsI,aAAa,IAAI,IAAI;QACjCvI,SAAS,EAAE,IAAI,CAACwI,cAAc,IAAI,IAAI;QACtCrI,OAAO,EAAE,IAAI,CAACsI;;KAEjB,CAAC,CAACQ,IAAI,CACL/K,GAAG,CAACgL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACd,SAAS,GAAGY,GAAG,CAACC,OAAO;QAC5B,IAAI,CAACd,YAAY,GAAGa,GAAG,CAACG,UAAW;MACrC,CAAC,MAAM;QACL,IAAI,CAACrB,OAAO,CAACsB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACrB,SAAS,GAAG,CAAC;IAClB,IAAI,CAACQ,YAAY,EAAE;EACrB;EAEAc,OAAOA,CAAA;IACL,IAAI,CAACnB,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACL,SAAS,GAAG,CAAC;IAClB,IAAI,CAACQ,YAAY,EAAE;EACrB;EAEAe,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACxB,SAAS,GAAGwB,OAAO;IACxB,IAAI,CAAChB,YAAY,EAAE;EACrB;EAEA9J,eAAeA,CAAC+K,GAAQ;IACtB,IAAI,CAAC3I,WAAW,GAAG;MACjBjB,KAAK,EAAE,EAAE;MACTD,SAAS,EAAE,EAAE;MACbG,OAAO,EAAE,CAAC,CAAC;KACZ;IACD,IAAI,CAAC0H,aAAa,CAACiC,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEAtK,aAAaA,CAACsK,GAAQ,EAAEE,IAAS;IAC/B,IAAI,CAACC,YAAY,CAACD,IAAI,CAAC/F,QAAQ,EAAE6F,GAAG,CAAC;EACvC;EAEAG,YAAYA,CAACC,OAAe,EAAEJ,GAAQ;IACpC,IAAI,CAAC/B,aAAa,CAACoC,6BAA6B,CAAC;MAC/CpB,IAAI,EAAE;QAAE9E,QAAQ,EAAEiG;MAAO;KAC1B,CAAC,CAACT,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAClI,WAAW,GAAG;UAAE,GAAGgI,GAAG,CAACC;QAAO,CAAE;QACrC,IAAI,CAACtB,aAAa,CAACiC,IAAI,CAACD,GAAG,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAAC7B,OAAO,CAACsB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEAhI,QAAQA,CAACsI,GAAQ;IACf,IAAI,CAACM,UAAU,EAAE;IACjB,IAAI,IAAI,CAAClC,KAAK,CAACmC,aAAa,CAACzG,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACqE,OAAO,CAACqC,aAAa,CAAC,IAAI,CAACpC,KAAK,CAACmC,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACtC,aAAa,CAACwC,0BAA0B,CAAC;MAC5CxB,IAAI,EAAE,IAAI,CAAC5H;KACZ,CAAC,CAAC+H,IAAI,CACL/K,GAAG,CAACgL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpB,OAAO,CAACuC,aAAa,CAAC,MAAM,CAAC;QAClCV,GAAG,CAACW,KAAK,EAAE;QACX,IAAI,CAAC5B,YAAY,EAAE;MACrB,CAAC,MAAM;QACL,IAAI,CAACZ,OAAO,CAACsB,YAAY,CAACJ,GAAG,CAACK,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA9J,WAAWA,CAACqK,IAAS;IACnB,IAAIU,OAAO,CAAC,eAAe,CAAC,EAAE;MAC5B,IAAI,CAAC3C,aAAa,CAAC4C,4BAA4B,CAAC;QAC9C5B,IAAI,EAAE;UAAE9E,QAAQ,EAAE+F,IAAI,CAACY;QAAQ;OAChC,CAAC,CAACnB,SAAS,CAACN,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACpB,OAAO,CAACuC,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAAC3B,YAAY,EAAE;QACrB,CAAC,MAAM;UACL,IAAI,CAACZ,OAAO,CAACsB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,MAAM,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EACF;EAEAY,UAAUA,CAAA;IACR,IAAI,CAAClC,KAAK,CAAC2C,KAAK,EAAE;IAClB,IAAI,CAAC3C,KAAK,CAAC4C,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC3J,WAAW,CAACjB,KAAK,CAAC;IACrD,IAAI,CAACgI,KAAK,CAAC6C,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC5J,WAAW,CAACjB,KAAK,EAAE,EAAE,CAAC;IAClE,IAAI,CAACgI,KAAK,CAAC4C,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC3J,WAAW,CAAClB,SAAS,CAAC;IACzD,IAAI,CAACiI,KAAK,CAAC6C,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC5J,WAAW,CAAClB,SAAS,EAAE,EAAE,CAAC;EACxE;EAEA;EACA+K,2BAA2BA,CAAClB,GAAQ;IAClC,IAAI,CAACjH,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACoI,eAAe,EAAE;IACtB,IAAI,CAACnD,aAAa,CAACiC,IAAI,CAACD,GAAG,EAAE;MAAEoB,SAAS,EAAE;IAAI,CAAE,CAAC;EACnD;EAEAD,eAAeA,CAAA;IACb,IAAI,CAACjD,gBAAgB,CAACmD,mCAAmC,CAAC;MACxDpC,IAAI,EAAE;QACJC,SAAS,EAAE,IAAI,CAACnG,iBAAiB;QACjCoG,QAAQ,EAAE,IAAI,CAAClG;;KAElB,CAAC,CAAC0G,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACvC,IAAI,CAAC1F,YAAY,GAAGwF,GAAG,CAACC,OAAO,CAACgC,GAAG,CAACpB,IAAI,KAAK;UAC3CqB,WAAW,EAAErB,IAAI,CAACqB,WAAY;UAC9B5I,aAAa,EAAEuH,IAAI,CAACvH,aAAc;UAClCnC,SAAS,EAAE0J,IAAI,CAAC1J,SAAU;UAC1BmH,SAAS,EAAEuC,IAAI,CAACvC,SAAU;UAC1B/E,QAAQ,EAAEsH,IAAI,CAACtH,QAAQ;UACvBgF,QAAQ,EAAEsC,IAAI,CAACtC,QAAQ;UACvBtH,OAAO,EAAE4J,IAAI,CAAC5J;SACf,CAAC,CAAC;QACH,IAAI,CAAC4C,oBAAoB,GAAGmG,GAAG,CAACG,UAAU,IAAI,CAAC;MACjD,CAAC,MAAM;QACL,IAAI,CAACrB,OAAO,CAACsB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,UAAU,CAAC;MACtD;IACF,CAAC,CAAC;EACJ;EAEAlG,uBAAuBA,CAACwG,GAAQ;IAC9B,IAAI,CAAC3E,cAAc,GAAG;MACpB1C,aAAa,EAAE,EAAE;MACjB6I,aAAa,EAAE,CAAC;MAChBlL,OAAO,EAAE;KACV;IACD,IAAI,CAAC0E,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAACT,cAAc,GAAG,CAAC,CAAC,CAAC;IACzB,IAAI,CAACkB,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACI,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC4F,oBAAoB,EAAE;IAC3B,IAAI,CAACzD,aAAa,CAACiC,IAAI,CAACD,GAAG,EAAE;MAAEoB,SAAS,EAAE;IAAI,CAAE,CAAC;EACnD;EAEAK,oBAAoBA,CAAA;IAClB,IAAI,CAACxD,aAAa,CAACe,6BAA6B,CAAC;MAC/CC,IAAI,EAAE;QACJC,SAAS,EAAE,IAAI,CAAC3E,cAAc;QAC9B4E,QAAQ,EAAE,IAAI,CAAC1E,aAAa;QAC5BrE,KAAK,EAAE,IAAI,CAACqF,kBAAkB,IAAI,IAAI;QACtCtF,SAAS,EAAE,IAAI,CAAC0F,mBAAmB,IAAI,IAAI;QAC3CvF,OAAO,EAAE,CAAC,CAAC;;KAEd,CAAC,CAACqJ,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACvC,IAAI,CAACzC,eAAe,GAAGuC,GAAG,CAACC,OAAO,CAACgC,GAAG,CAACpB,IAAI,KAAK;UAC9C/F,QAAQ,EAAE+F,IAAI,CAAC/F,QAAS;UACxB/D,KAAK,EAAE8J,IAAI,CAAC9J,KAAM;UAClBD,SAAS,EAAE+J,IAAI,CAAC/J,SAAS;UACzBiE,QAAQ,EAAE,IAAI,CAACY,yBAAyB,CAAC0G,IAAI,CAACtH,QAAQ,IAAIA,QAAQ,CAACD,QAAQ,KAAK+F,IAAI,CAAC/F,QAAQ;SAC9F,CAAC,CAAC;QACH,IAAI,CAACO,iBAAiB,GAAG2E,GAAG,CAACG,UAAW;QACxC,IAAI,CAACmC,4BAA4B,EAAE;MACrC,CAAC,MAAM;QACL,IAAI,CAACxD,OAAO,CAACsB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,UAAU,CAAC;MACtD;IACF,CAAC,CAAC;EACJ;EAEA/D,aAAaA,CAAA;IACX,IAAI,CAACpB,cAAc,GAAG,CAAC;IACvB,IAAI,CAACkH,oBAAoB,EAAE;EAC7B;EAEAzF,YAAYA,CAAA;IACV,IAAI,CAACP,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACI,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACtB,cAAc,GAAG,CAAC;IACvB,IAAI,CAACkH,oBAAoB,EAAE;EAC7B;EAEAjH,gBAAgBA,CAACuF,OAAe;IAC9B,IAAI,CAACxF,cAAc,GAAGwF,OAAO;IAC7B,IAAI,CAAC0B,oBAAoB,EAAE;EAC7B;EAEAzI,mBAAmBA,CAAC+G,OAAe;IACjC,IAAI,CAAChH,iBAAiB,GAAGgH,OAAO;IAChC,IAAI,CAACoB,eAAe,EAAE;EACxB;EAEAjH,oBAAoBA,CAAC0H,KAAwB;IAC3CA,KAAK,CAACxH,QAAQ,GAAG,CAACwH,KAAK,CAACxH,QAAQ;IAChC,IAAIwH,KAAK,CAACxH,QAAQ,EAAE;MAClB,IAAI,CAACY,yBAAyB,CAAC6G,IAAI,CAAC;QAAE,GAAGD;MAAK,CAAE,CAAC;IACnD,CAAC,MAAM;MACL,MAAME,KAAK,GAAG,IAAI,CAAC9G,yBAAyB,CAAC+G,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC7H,QAAQ,KAAKyH,KAAK,CAACzH,QAAQ,CAAC;MAC1F,IAAI2H,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAAC9G,yBAAyB,CAACiH,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACjD;IACF;IACA,IAAI,CAACH,4BAA4B,EAAE;EACrC;EAEAxF,eAAeA,CAAA;IACb,IAAI,CAACO,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAChD,IAAI,CAACI,eAAe,CAACoF,OAAO,CAACN,KAAK,IAAG;MACnC,MAAMO,WAAW,GAAGP,KAAK,CAACxH,QAAQ;MAClCwH,KAAK,CAACxH,QAAQ,GAAG,IAAI,CAACsC,iBAAiB;MAEvC,IAAI,IAAI,CAACA,iBAAiB,IAAI,CAACyF,WAAW,EAAE;QAC1C;QACA,MAAMC,MAAM,GAAG,IAAI,CAACpH,yBAAyB,CAACqH,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7H,QAAQ,KAAKyH,KAAK,CAACzH,QAAQ,CAAC;QACtF,IAAI,CAACiI,MAAM,EAAE;UACX,IAAI,CAACpH,yBAAyB,CAAC6G,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACnD;MACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAClF,iBAAiB,IAAIyF,WAAW,EAAE;QACjD;QACA,MAAML,KAAK,GAAG,IAAI,CAAC9G,yBAAyB,CAAC+G,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC7H,QAAQ,KAAKyH,KAAK,CAACzH,QAAQ,CAAC;QAC1F,IAAI2H,KAAK,GAAG,CAAC,CAAC,EAAE;UACd,IAAI,CAAC9G,yBAAyB,CAACiH,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;QACjD;MACF;IACF,CAAC,CAAC;EACJ;EAEAH,4BAA4BA,CAAA;IAC1B,IAAI,CAACjF,iBAAiB,GAAG,IAAI,CAACI,eAAe,CAAChD,MAAM,GAAG,CAAC,IACtD,IAAI,CAACgD,eAAe,CAACwF,KAAK,CAACV,KAAK,IAAIA,KAAK,CAACxH,QAAQ,CAAC;EACvD;EAEAU,mBAAmBA,CAAC8G,KAAwB;IAC1C,MAAME,KAAK,GAAG,IAAI,CAAC9G,yBAAyB,CAAC+G,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC7H,QAAQ,KAAKyH,KAAK,CAACzH,QAAQ,CAAC;IAC1F,IAAI2H,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC9G,yBAAyB,CAACiH,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IACjD;IAEA;IACA,MAAMS,cAAc,GAAG,IAAI,CAACzF,eAAe,CAACuF,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7H,QAAQ,KAAKyH,KAAK,CAACzH,QAAQ,CAAC;IACpF,IAAIoI,cAAc,EAAE;MAClBA,cAAc,CAACnI,QAAQ,GAAG,KAAK;IACjC;IACA,IAAI,CAACuH,4BAA4B,EAAE;EACrC;EAEApG,gBAAgBA,CAACyE,GAAQ;IACvB,IAAI,CAACwC,kBAAkB,EAAE;IACzB,IAAI,IAAI,CAACpE,KAAK,CAACmC,aAAa,CAACzG,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACqE,OAAO,CAACqC,aAAa,CAAC,IAAI,CAACpC,KAAK,CAACmC,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,IAAI,CAACvF,yBAAyB,CAAClB,MAAM,KAAK,CAAC,EAAE;MAC/C,IAAI,CAACqE,OAAO,CAACsB,YAAY,CAAC,WAAW,CAAC;MACtC;IACF;IAEA,MAAMgD,eAAe,GAA6B,IAAI,CAACzH,yBAAyB,CAACsG,GAAG,CAACM,KAAK,KAAK;MAC7Fc,UAAU,EAAEd,KAAK,CAACzH,QAAQ;MAC1BwI,YAAY,EAAEf,KAAK,CAACxL;KACrB,CAAC,CAAC;IAEH,IAAI,CAAC8H,gBAAgB,CAAC0E,gCAAgC,CAAC;MACrD3D,IAAI,EAAE;QACJ,GAAG,IAAI,CAAC5D,cAAc;QACtBwH,OAAO,EAAEJ;;KAEZ,CAAC,CAAC9C,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpB,OAAO,CAACuC,aAAa,CAAC,QAAQ,CAAC;QACpCV,GAAG,CAACW,KAAK,EAAE;QACX,IAAI,CAACQ,eAAe,EAAE,CAAC,CAAC;MAC1B,CAAC,MAAM;QACL,IAAI,CAAChD,OAAO,CAACsB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEAhH,cAAcA,CAACoK,QAAsB;IACnC,IAAIlC,OAAO,CAAC,YAAYkC,QAAQ,CAACnK,aAAa,KAAK,CAAC,EAAE;MACpD,IAAI,CAACuF,gBAAgB,CAAC6E,kCAAkC,CAAC;QACvD9D,IAAI,EAAE;UAAEsC,WAAW,EAAEuB,QAAQ,CAACvB;QAAW;OAC1C,CAAC,CAAC5B,SAAS,CAACN,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACpB,OAAO,CAACuC,aAAa,CAAC,QAAQ,CAAC;UAEpC;UACA,MAAMsC,qBAAqB,GAAG,CAAC,IAAI,CAACjK,iBAAiB,GAAG,CAAC,IAAI,IAAI,CAACE,gBAAgB;UAClF,MAAMgK,cAAc,GAAG,IAAI,CAAC/J,oBAAoB,GAAG,CAAC,CAAC,CAAC;UAEtD,IAAI+J,cAAc,GAAG,CAAC,IAAID,qBAAqB,IAAIC,cAAc,EAAE;YACjE,IAAI,CAAClK,iBAAiB,GAAG6D,IAAI,CAACsG,GAAG,CAAC,CAAC,EAAE,IAAI,CAACnK,iBAAiB,GAAG,CAAC,CAAC;UAClE;UAEA,IAAI,CAACoI,eAAe,EAAE;QACxB,CAAC,MAAM;UACL,IAAI,CAAChD,OAAO,CAACsB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;QACpD;MACF,CAAC,CAAC;IACJ;EACF;EAEAlH,kBAAkBA,CAACsK,QAAsB,EAAE9C,GAAQ;IACjD,IAAI,CAACtC,sBAAsB,GAAGoF,QAAQ;IACtC,IAAI,CAACK,uBAAuB,CAACL,QAAQ,CAACvB,WAAW,CAAC;IAClD,IAAI,CAACvD,aAAa,CAACiC,IAAI,CAACD,GAAG,EAAE;MAAEoB,SAAS,EAAE;IAAI,CAAE,CAAC;EACnD;EAEA+B,uBAAuBA,CAACC,UAAkB;IACxC,IAAI,CAAClF,gBAAgB,CAACmF,yCAAyC,CAAC;MAC9DpE,IAAI,EAAE;QAAEmE,UAAU,EAAEA;MAAU;KAC/B,CAAC,CAACzD,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACvC;QACA,MAAM+D,QAAQ,GAAGjE,GAAG,CAACC,OAAO,CAACgC,GAAG,CAACpB,IAAI,IAAIA,IAAI,CAACwC,UAAU,CAAC,CAACa,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKC,SAAS,CAAa;QACpG,IAAIH,QAAQ,CAACxJ,MAAM,GAAG,CAAC,EAAE;UACvB,IAAI,CAAC4J,oBAAoB,CAACJ,QAAQ,CAAC;QACrC,CAAC,MAAM;UACL,IAAI,CAACnG,oBAAoB,GAAG,EAAE;QAChC;MACF,CAAC,MAAM;QACL,IAAI,CAACgB,OAAO,CAACsB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,UAAU,CAAC;QACpD,IAAI,CAACvC,oBAAoB,GAAG,EAAE;MAChC;IACF,CAAC,CAAC;EACJ;EAEAuG,oBAAoBA,CAACJ,QAAkB;IACrC;IACA,IAAI,CAACrF,aAAa,CAACe,6BAA6B,CAAC;MAC/CC,IAAI,EAAE;QACJC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,IAAI;QAAE;QAChB/I,KAAK,EAAE,IAAI;QACXD,SAAS,EAAE,IAAI;QACfG,OAAO,EAAE;;KAEZ,CAAC,CAACqJ,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACvC,IAAI,CAACpC,oBAAoB,GAAGkC,GAAG,CAACC,OAAO,CACpCiE,MAAM,CAAC3B,KAAK,IAAI0B,QAAQ,CAACK,QAAQ,CAAC/B,KAAK,CAACzH,QAAS,CAAC,CAAC,CACnDmH,GAAG,CAACM,KAAK,KAAK;UACbzH,QAAQ,EAAEyH,KAAK,CAACzH,QAAS;UACzB/D,KAAK,EAAEwL,KAAK,CAACxL,KAAM;UACnBD,SAAS,EAAEyL,KAAK,CAACzL,SAAS;UAC1BG,OAAO,EAAEsL,KAAK,CAACtL;SAChB,CAAC,CAAC;MACP,CAAC,MAAM;QACL,IAAI,CAAC6H,OAAO,CAACsB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,UAAU,CAAC;QACpD,IAAI,CAACvC,oBAAoB,GAAG,EAAE;MAChC;IACF,CAAC,CAAC;EACJ;EAEAqF,kBAAkBA,CAAA;IAChB,IAAI,CAACpE,KAAK,CAAC2C,KAAK,EAAE;IAClB,IAAI,CAAC3C,KAAK,CAAC4C,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC3F,cAAc,CAAC1C,aAAa,CAAC;IAChE,IAAI,CAACyF,KAAK,CAAC6C,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC5F,cAAc,CAAC1C,aAAa,EAAE,GAAG,CAAC;EAChF;EAEA3B,OAAOA,CAACgJ,GAAQ;IACdA,GAAG,CAACW,KAAK,EAAE;EACb;;;uCA1aW9C,cAAc,EAAAvJ,EAAA,CAAAsP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxP,EAAA,CAAAsP,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA1P,EAAA,CAAAsP,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAA5P,EAAA,CAAAsP,iBAAA,CAAAK,EAAA,CAAAE,eAAA,GAAA7P,EAAA,CAAAsP,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA/P,EAAA,CAAAsP,iBAAA,CAAAU,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAd1G,cAAc;MAAA2G,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApQ,EAAA,CAAAqQ,0BAAA,EAAArQ,EAAA,CAAAsQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAjC,QAAA,WAAAkC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCjDzB3Q,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,SAAA,qBAAiC;UACnCZ,EAAA,CAAAc,YAAA,EAAiB;UAIbd,EAHJ,CAAAC,cAAA,mBAAc,aAE6E,aAChD;UACrCD,EAAA,CAAAY,SAAA,WAA+E;UAE7EZ,EADF,CAAAC,cAAA,UAAK,WACmD;UACpDD,EAAA,CAAAa,MAAA,uUACF;UAGNb,EAHM,CAAAc,YAAA,EAAI,EACA,EACF,EACF;UAIAd,EAHN,CAAAC,cAAA,eAA8B,eACN,eACqC,iBACZ;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAErDd,EADF,CAAAC,cAAA,yBAA6B,iBAEE;UADoDD,EAAA,CAAA2C,gBAAA,2BAAAkO,wDAAAhO,MAAA;YAAA7C,EAAA,CAAAI,aAAA,CAAA0Q,GAAA;YAAA9Q,EAAA,CAAA8C,kBAAA,CAAA8N,GAAA,CAAAxG,aAAA,EAAAvH,MAAA,MAAA+N,GAAA,CAAAxG,aAAA,GAAAvH,MAAA;YAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;UAAA,EAA2B;UAC1G7C,EAAA,CAAAE,UAAA,yBAAA6Q,sDAAA;YAAA/Q,EAAA,CAAAI,aAAA,CAAA0Q,GAAA;YAAA,OAAA9Q,EAAA,CAAAU,WAAA,CAAekQ,GAAA,CAAAtF,QAAA,EAAU;UAAA,EAAC;UAGlCtL,EAJM,CAAAc,YAAA,EAC6B,EACf,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACb;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAEpDd,EADF,CAAAC,cAAA,yBAA6B,iBAEE;UADmDD,EAAA,CAAA2C,gBAAA,2BAAAqO,wDAAAnO,MAAA;YAAA7C,EAAA,CAAAI,aAAA,CAAA0Q,GAAA;YAAA9Q,EAAA,CAAA8C,kBAAA,CAAA8N,GAAA,CAAAvG,cAAA,EAAAxH,MAAA,MAAA+N,GAAA,CAAAvG,cAAA,GAAAxH,MAAA;YAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;UAAA,EAA4B;UAC1G7C,EAAA,CAAAE,UAAA,yBAAA+Q,sDAAA;YAAAjR,EAAA,CAAAI,aAAA,CAAA0Q,GAAA;YAAA,OAAA9Q,EAAA,CAAAU,WAAA,CAAekQ,GAAA,CAAAtF,QAAA,EAAU;UAAA,EAAC;UAGlCtL,EAJM,CAAAc,YAAA,EAC6B,EACf,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACf;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAEhDd,EADF,CAAAC,cAAA,yBAA6B,qBAC2E;UAAzDD,EAAA,CAAA2C,gBAAA,2BAAAuO,4DAAArO,MAAA;YAAA7C,EAAA,CAAAI,aAAA,CAAA0Q,GAAA;YAAA9Q,EAAA,CAAA8C,kBAAA,CAAA8N,GAAA,CAAAtG,YAAA,EAAAzH,MAAA,MAAA+N,GAAA,CAAAtG,YAAA,GAAAzH,MAAA;YAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;UAAA,EAA0B;UAAC7C,EAAA,CAAAE,UAAA,4BAAAiR,6DAAA;YAAAnR,EAAA,CAAAI,aAAA,CAAA0Q,GAAA;YAAA,OAAA9Q,EAAA,CAAAU,WAAA,CAAkBkQ,GAAA,CAAAtF,QAAA,EAAU;UAAA,EAAC;UACnGtL,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACxCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACrCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAIjCb,EAJiC,CAAAc,YAAA,EAAY,EAC3B,EACE,EACZ,EACF;UAENd,EAAA,CAAAY,SAAA,eAEM;UAKFZ,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACmB;UAApBD,EAAA,CAAAE,UAAA,mBAAAkR,iDAAA;YAAApR,EAAA,CAAAI,aAAA,CAAA0Q,GAAA;YAAA,OAAA9Q,EAAA,CAAAU,WAAA,CAASkQ,GAAA,CAAArF,OAAA,EAAS;UAAA,EAAC;UACvEvL,EAAA,CAAAY,SAAA,aAAgC;UAAAZ,EAAA,CAAAa,MAAA,qBAClC;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAC,cAAA,kBAA8D;UAArBD,EAAA,CAAAE,UAAA,mBAAAmR,iDAAA;YAAArR,EAAA,CAAAI,aAAA,CAAA0Q,GAAA;YAAA,OAAA9Q,EAAA,CAAAU,WAAA,CAASkQ,GAAA,CAAAtF,QAAA,EAAU;UAAA,EAAC;UAC3DtL,EAAA,CAAAY,SAAA,aAAkC;UAAAZ,EAAA,CAAAa,MAAA,qBACpC;UAEJb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAGJd,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAAwB,UAAA,KAAA8P,iCAAA,qBAAiG;UAGjGtR,EAAA,CAAAC,cAAA,kBAA2G;UAA/DD,EAAA,CAAAE,UAAA,mBAAAqR,iDAAA;YAAAvR,EAAA,CAAAI,aAAA,CAAA0Q,GAAA;YAAA,MAAAU,0BAAA,GAAAxR,EAAA,CAAAS,WAAA;YAAA,OAAAT,EAAA,CAAAU,WAAA,CAASkQ,GAAA,CAAAhE,2BAAA,CAAA4E,0BAAA,CAAoD;UAAA,EAAC;UACxGxR,EAAA,CAAAY,SAAA,aAAuC;UAAAZ,EAAA,CAAAa,MAAA,iCACzC;UAGNb,EAHM,CAAAc,YAAA,EAAS,EACL,EACF,EACF;UAMEd,EAJR,CAAAC,cAAA,eAAmC,iBAC2B,aACnD,cACoB,cACO;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAEpCb,EAFoC,CAAAc,YAAA,EAAK,EAClC,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAwB,UAAA,KAAAiQ,6BAAA,kBAAmC;UAmB3CzR,EAHM,CAAAc,YAAA,EAAQ,EACF,EACJ,EACO;UAEbd,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADrBD,EAAA,CAAA2C,gBAAA,wBAAA+O,8DAAA7O,MAAA;YAAA7C,EAAA,CAAAI,aAAA,CAAA0Q,GAAA;YAAA9Q,EAAA,CAAA8C,kBAAA,CAAA8N,GAAA,CAAA3G,SAAA,EAAApH,MAAA,MAAA+N,GAAA,CAAA3G,SAAA,GAAApH,MAAA;YAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;UAAA,EAAoB;UAClC7C,EAAA,CAAAE,UAAA,wBAAAwR,8DAAA7O,MAAA;YAAA7C,EAAA,CAAAI,aAAA,CAAA0Q,GAAA;YAAA,OAAA9Q,EAAA,CAAAU,WAAA,CAAckQ,GAAA,CAAApF,WAAA,CAAA3I,MAAA,CAAmB;UAAA,EAAC;UAGxC7C,EAFI,CAAAc,YAAA,EAAiB,EACF,EACT;UAiZVd,EA9YA,CAAAwB,UAAA,KAAAmQ,sCAAA,iCAAA3R,EAAA,CAAA4R,sBAAA,CAA8C,KAAAC,sCAAA,iCAAA7R,EAAA,CAAA4R,sBAAA,CA0FF,KAAAE,sCAAA,iCAAA9R,EAAA,CAAA4R,sBAAA,CA0Fc,KAAAG,sCAAA,kCAAA/R,EAAA,CAAA4R,sBAAA,CAoFJ,KAAAI,sCAAA,kCAAAhS,EAAA,CAAA4R,sBAAA,CAsIA;;;UA/euC5R,EAAA,CAAA2B,SAAA,IAA2B;UAA3B3B,EAAA,CAAAqD,gBAAA,YAAAuN,GAAA,CAAAxG,aAAA,CAA2B;UAU5BpK,EAAA,CAAA2B,SAAA,GAA4B;UAA5B3B,EAAA,CAAAqD,gBAAA,YAAAuN,GAAA,CAAAvG,cAAA,CAA4B;UAU/DrK,EAAA,CAAA2B,SAAA,GAA0B;UAA1B3B,EAAA,CAAAqD,gBAAA,YAAAuN,GAAA,CAAAtG,YAAA,CAA0B;UAC1DtK,EAAA,CAAA2B,SAAA,EAAc;UAAd3B,EAAA,CAAAmC,UAAA,eAAc;UACdnC,EAAA,CAAA2B,SAAA,GAAW;UAAX3B,EAAA,CAAAmC,UAAA,YAAW;UACXnC,EAAA,CAAA2B,SAAA,GAAW;UAAX3B,EAAA,CAAAmC,UAAA,YAAW;UAwBgBnC,EAAA,CAAA2B,SAAA,IAAc;UAAd3B,EAAA,CAAAmC,UAAA,SAAAyO,GAAA,CAAAqB,QAAA,CAAc;UAsBnCjS,EAAA,CAAA2B,SAAA,IAAY;UAAZ3B,EAAA,CAAAmC,UAAA,YAAAyO,GAAA,CAAAzG,SAAA,CAAY;UAqBvBnK,EAAA,CAAA2B,SAAA,GAAoB;UAApB3B,EAAA,CAAAqD,gBAAA,SAAAuN,GAAA,CAAA3G,SAAA,CAAoB;UAAuBjK,EAAtB,CAAAmC,UAAA,aAAAyO,GAAA,CAAA5G,QAAA,CAAqB,mBAAA4G,GAAA,CAAA1G,YAAA,CAAgC;;;qBDnE1FrK,YAAY,EAAAqS,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZ1S,YAAY,EAAA2S,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAjD,EAAA,CAAAkD,eAAA,EAAAlD,EAAA,CAAAmD,mBAAA,EAAAnD,EAAA,CAAAoD,qBAAA,EAAApD,EAAA,CAAAqD,qBAAA,EAAArD,EAAA,CAAAsD,gBAAA,EAAAtD,EAAA,CAAAuD,iBAAA,EAAAvD,EAAA,CAAAwD,iBAAA,EAAAxD,EAAA,CAAAyD,oBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}