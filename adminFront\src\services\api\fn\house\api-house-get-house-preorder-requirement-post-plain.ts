/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { TblHousePreorderDetailListResponseBase } from '../../models/tbl-house-preorder-detail-list-response-base';

export interface ApiHouseGetHousePreorderRequirementPost$Plain$Params {
}

export function apiHouseGetHousePreorderRequirementPost$Plain(http: HttpClient, rootUrl: string, params?: ApiHouseGetHousePreorderRequirementPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TblHousePreorderDetailListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiHouseGetHousePreorderRequirementPost$Plain.PATH, 'post');
  if (params) {
  }

  return http.request(
    rb.build({ responseType: 'text', accept: 'text/plain', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<TblHousePreorderDetailListResponseBase>;
    })
  );
}

apiHouseGetHousePreorderRequirementPost$Plain.PATH = '/api/House/GetHousePreorderRequirement';
