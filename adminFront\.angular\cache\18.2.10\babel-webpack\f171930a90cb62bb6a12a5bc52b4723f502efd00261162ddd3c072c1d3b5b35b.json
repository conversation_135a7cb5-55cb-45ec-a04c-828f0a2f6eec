{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { CQuotationItemType } from '../models/quotation.model';\nimport { EnumQuotationItemType } from '../../services/api/models/enum-quotation-item-type';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api/services/quotation.service\";\nimport * as i2 from \"@angular/common/http\";\nexport let QuotationService = /*#__PURE__*/(() => {\n  class QuotationService {\n    constructor(apiQuotationService, http) {\n      this.apiQuotationService = apiQuotationService;\n      this.http = http;\n      this.apiUrl = '/api/Quotation';\n    }\n    // 將 EnumQuotationItemType 轉換為 CQuotationItemType\n    mapEnumToCQuotationType(enumType) {\n      switch (enumType) {\n        case EnumQuotationItemType.客變需求:\n          return CQuotationItemType.客變需求;\n        case EnumQuotationItemType.選樣:\n          return CQuotationItemType.選樣;\n        case EnumQuotationItemType.自定義:\n        default:\n          return CQuotationItemType.自定義;\n      }\n    }\n    // 取得報價單列表\n    getQuotationList(request) {\n      return this.apiQuotationService.apiQuotationGetListPost$Json({\n        body: request\n      });\n    }\n    // 取得單筆報價單資料\n    getQuotationData(quotationId) {\n      const request = {\n        CQuotationID: quotationId\n      };\n      return this.apiQuotationService.apiQuotationGetDataPost$Json({\n        body: request\n      });\n    } // 取得戶別的報價項目\n    getQuotationByHouseId(houseId) {\n      const request = {\n        CHouseID: houseId\n      };\n      return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({\n        body: request\n      }).pipe(map(response => {\n        // 將 API 響應的小寫屬性名轉換為前端期望的大寫屬性名\n        if (response) {\n          return {\n            StatusCode: response.StatusCode,\n            Message: response.Message,\n            TotalItems: response.TotalItems,\n            Entries: response.Entries // 保持原始結構，因為 entries 是一個物件而不是陣列\n          };\n        }\n        return response;\n      }));\n    }\n    // 儲存報價單 (支援單一項目)\n    saveQuotationItem(quotation) {\n      return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n        body: quotation\n      });\n    } // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\n    saveQuotation(request) {\n      // 將 QuotationItem 轉換為 QuotationItemModel 格式\n      const quotationItems = request.items.map(item => {\n        const quotationType = item.CQuotationItemType && item.CQuotationItemType > 0 ? item.CQuotationItemType : CQuotationItemType.自定義;\n        return {\n          CItemName: item.cItemName,\n          CUnit: item.cUnit || '',\n          CUnitPrice: item.cUnitPrice,\n          CCount: item.cCount,\n          CStatus: item.cStatus || 1,\n          CQuotationItemType: quotationType,\n          CRemark: item.cRemark || ''\n        };\n      });\n      // 建立 SaveDataQuotation 請求，並直接添加額外費用欄位\n      const saveRequest = {\n        CHouseID: request.houseId,\n        CQuotationVersionId: request.quotationId || 0,\n        // 使用傳入的 quotationId，如果沒有則為 0（新建報價單）\n        Items: quotationItems,\n        // 額外費用相關欄位 - 直接添加到請求中\n        CShowOther: request.cShowOther || false,\n        COtherName: request.cOtherName || '',\n        COtherPercent: request.cOtherPercent || 0\n      };\n      // 調試用 - 印出最終送出的請求資料\n      console.log('QuotationService saveRequest:', saveRequest);\n      return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n        body: saveRequest\n      }).pipe(map(response => ({\n        success: response?.StatusCode === 0,\n        message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',\n        data: request.items\n      })));\n    }\n    // 取得預設報價項目 (保持原有方法以兼容現有代碼)\n    getDefaultQuotationItems() {\n      // 使用 GetList 方法獲取預設項目\n      const request = {\n        PageIndex: 0,\n        PageSize: 100\n        // 其他預設參數可能需要根據實際 API 需求調整\n      };\n      return this.apiQuotationService.apiQuotationGetListPost$Json({\n        body: request\n      }).pipe(map(response => {\n        // 將 GetQuotation 轉換為 QuotationItem\n        const quotationItems = (response.Entries || []).map(item => ({\n          cHouseID: item.CHouseID || 0,\n          cItemName: item.CItemName || '',\n          cUnit: item.CUnit || '',\n          cUnitPrice: item.CUnitPrice || 0,\n          cCount: item.CCount || 0,\n          cStatus: item.CStatus || 1,\n          CQuotationItemType: this.mapEnumToCQuotationType(item.CQuotationItemType),\n          cRemark: item.CRemark || ''\n        }));\n        // 轉換 API 響應格式以保持兼容性\n        return {\n          success: response.StatusCode === 0,\n          // 假設 statusCode 0 表示成功\n          message: response.Message || '',\n          data: quotationItems\n        };\n      }));\n    } // 載入預設報價項目 (LoadDefaultItems API)\n    loadDefaultItems(request) {\n      return this.apiQuotationService.apiQuotationLoadDefaultItemsPost$Json({\n        body: request\n      }).pipe(map(response => {\n        // 將 GetQuotation 轉換為 QuotationItem\n        const quotationItems = (response.Entries || []).map(item => ({\n          cHouseID: item.CHouseID || 0,\n          cItemName: item.CItemName || '',\n          cUnit: item.CUnit || '',\n          cUnitPrice: item.CUnitPrice || 0,\n          cCount: item.CCount || 0,\n          cStatus: item.CStatus || 1,\n          CQuotationItemType: this.mapEnumToCQuotationType(item.CQuotationItemType),\n          cRemark: item.CRemark || ''\n        }));\n        // 轉換 API 響應格式以保持兼容性\n        return {\n          success: response.StatusCode === 0,\n          // 假設 StatusCode 0 表示成功\n          message: response.Message || '',\n          data: quotationItems\n        };\n      }));\n    } // 載入常規報價項目 (LoadRegularItems API)\n    loadRegularItems(request) {\n      return this.apiQuotationService.apiQuotationLoadRegularItemsPost$Json({\n        body: request\n      }).pipe(map(response => {\n        // 將 GetQuotation 轉換為 QuotationItem\n        const quotationItems = (response.Entries || []).map(item => ({\n          cHouseID: item.CHouseID || 0,\n          cItemName: item.CItemName || '',\n          cUnit: item.CUnit || '',\n          cUnitPrice: item.CUnitPrice || 0,\n          cCount: item.CCount || 0,\n          cStatus: item.CStatus || 1,\n          CQuotationItemType: this.mapEnumToCQuotationType(item.CQuotationItemType),\n          cRemark: item.CRemark || ''\n        }));\n        // 轉換 API 響應格式以保持兼容性\n        return {\n          success: response.StatusCode === 0,\n          // 假設 StatusCode 0 表示成功\n          message: response.Message || '',\n          data: quotationItems\n        };\n      }));\n    }\n    // 更新報價項目 (使用 SaveData 方法)\n    updateQuotationItem(quotationId, item) {\n      const saveData = {\n        CHouseID: item.cHouseID,\n        CQuotationVersionId: quotationId,\n        Items: [{\n          CItemName: item.cItemName,\n          CUnitPrice: item.cUnitPrice,\n          CCount: item.cCount,\n          CStatus: item.cStatus || 1,\n          CQuotationItemType: item.CQuotationItemType || CQuotationItemType.自定義\n        }]\n      };\n      return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n        body: saveData\n      }).pipe(map(response => {\n        return {\n          success: response.StatusCode === 0,\n          // 假設 StatusCode 0 表示成功\n          message: response.Message || '',\n          data: [item] // 包裝為陣列以符合 QuotationResponse 格式\n        };\n      }));\n    }\n    // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\n    exportQuotation(houseId) {\n      // 這個方法可能需要使用其他 API 或保持原有實作\n      // 暫時拋出錯誤提示需要實作\n      throw new Error('Export quotation functionality needs to be implemented separately');\n    }\n    // 鎖定報價單\n    lockQuotation(quotationId) {\n      return this.apiQuotationService.apiQuotationLockQuotationPost$Json({\n        body: quotationId\n      }).pipe(map(response => {\n        return {\n          success: response.StatusCode === 0,\n          message: response.Message || '',\n          data: response.Entries || null\n        };\n      }));\n    }\n    static {\n      this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.QuotationService), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: QuotationService,\n        factory: QuotationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return QuotationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}