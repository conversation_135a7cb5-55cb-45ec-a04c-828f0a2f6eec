{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as helper from '../helper/treeHelper.js';\nimport { noop } from 'zrender/lib/core/util.js';\nvar actionTypes = ['treemapZoomToNode', 'treemapRender', 'treemapMove'];\nexport function installTreemapAction(registers) {\n  for (var i = 0; i < actionTypes.length; i++) {\n    registers.registerAction({\n      type: actionTypes[i],\n      update: 'updateView'\n    }, noop);\n  }\n  registers.registerAction({\n    type: 'treemapRootToNode',\n    update: 'updateView'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'treemap',\n      query: payload\n    }, handleRootToNode);\n    function handleRootToNode(model, index) {\n      var types = ['treemapZoomToNode', 'treemapRootToNode'];\n      var targetInfo = helper.retrieveTargetInfo(payload, types, model);\n      if (targetInfo) {\n        var originViewRoot = model.getViewRoot();\n        if (originViewRoot) {\n          payload.direction = helper.aboveViewRoot(originViewRoot, targetInfo.node) ? 'rollUp' : 'drillDown';\n        }\n        model.resetViewRoot(targetInfo.node);\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["helper", "noop", "actionTypes", "installTreemapAction", "registers", "i", "length", "registerAction", "type", "update", "payload", "ecModel", "eachComponent", "mainType", "subType", "query", "handleRootToNode", "model", "index", "types", "targetInfo", "retrieveTargetInfo", "originViewRoot", "getViewRoot", "direction", "aboveViewRoot", "node", "resetViewRoot"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/treemap/treemapAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as helper from '../helper/treeHelper.js';\nimport { noop } from 'zrender/lib/core/util.js';\nvar actionTypes = ['treemapZoomToNode', 'treemapRender', 'treemapMove'];\nexport function installTreemapAction(registers) {\n  for (var i = 0; i < actionTypes.length; i++) {\n    registers.registerAction({\n      type: actionTypes[i],\n      update: 'updateView'\n    }, noop);\n  }\n  registers.registerAction({\n    type: 'treemapRootToNode',\n    update: 'updateView'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'treemap',\n      query: payload\n    }, handleRootToNode);\n    function handleRootToNode(model, index) {\n      var types = ['treemapZoomToNode', 'treemapRootToNode'];\n      var targetInfo = helper.retrieveTargetInfo(payload, types, model);\n      if (targetInfo) {\n        var originViewRoot = model.getViewRoot();\n        if (originViewRoot) {\n          payload.direction = helper.aboveViewRoot(originViewRoot, targetInfo.node) ? 'rollUp' : 'drillDown';\n        }\n        model.resetViewRoot(targetInfo.node);\n      }\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,yBAAyB;AACjD,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,IAAIC,WAAW,GAAG,CAAC,mBAAmB,EAAE,eAAe,EAAE,aAAa,CAAC;AACvE,OAAO,SAASC,oBAAoBA,CAACC,SAAS,EAAE;EAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3CD,SAAS,CAACG,cAAc,CAAC;MACvBC,IAAI,EAAEN,WAAW,CAACG,CAAC,CAAC;MACpBI,MAAM,EAAE;IACV,CAAC,EAAER,IAAI,CAAC;EACV;EACAG,SAAS,CAACG,cAAc,CAAC;IACvBC,IAAI,EAAE,mBAAmB;IACzBC,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAE;IAC7BA,OAAO,CAACC,aAAa,CAAC;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAEL;IACT,CAAC,EAAEM,gBAAgB,CAAC;IACpB,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,KAAK,EAAE;MACtC,IAAIC,KAAK,GAAG,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;MACtD,IAAIC,UAAU,GAAGpB,MAAM,CAACqB,kBAAkB,CAACX,OAAO,EAAES,KAAK,EAAEF,KAAK,CAAC;MACjE,IAAIG,UAAU,EAAE;QACd,IAAIE,cAAc,GAAGL,KAAK,CAACM,WAAW,CAAC,CAAC;QACxC,IAAID,cAAc,EAAE;UAClBZ,OAAO,CAACc,SAAS,GAAGxB,MAAM,CAACyB,aAAa,CAACH,cAAc,EAAEF,UAAU,CAACM,IAAI,CAAC,GAAG,QAAQ,GAAG,WAAW;QACpG;QACAT,KAAK,CAACU,aAAa,CAACP,UAAU,CAACM,IAAI,CAAC;MACtC;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}