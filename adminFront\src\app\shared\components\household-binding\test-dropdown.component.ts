import { Component } from '@angular/core';

@Component({
  selector: 'app-test-dropdown',
  template: `
    <div style="padding: 20px; border: 2px solid red;">
      <h3>測試下拉選單</h3>
      <p>isOpen 狀態: {{isOpen}}</p>

      <button (click)="toggleDropdown()" style="padding: 10px; background: blue; color: white; border: none; cursor: pointer;">
        點擊切換下拉選單 (當前: {{isOpen ? '開啟' : '關閉'}})
      </button>

      <!-- 簡單的下拉選單測試 -->
      <div *ngIf="isOpen" style="position: relative; margin-top: 10px; padding: 20px; background: yellow; border: 2px solid green; z-index: 9999;">
        <h4>下拉選單內容</h4>
        <p>如果你看到這個黃色區域，表示 *ngIf 正常工作</p>
        <ul>
          <li *ngFor="let item of testItems">{{item}}</li>
        </ul>
        <button (click)="closeDropdown()" style="padding: 5px; background: red; color: white; border: none; cursor: pointer;">
          關閉
        </button>
      </div>

      <!-- 無條件顯示的區域 -->
      <div style="margin-top: 20px; padding: 10px; background: lightblue; border: 1px solid blue;">
        <h4>這個區域應該永遠可見</h4>
        <p>測試項目數量: {{testItems.length}}</p>
      </div>
    </div>
  `,
  styles: []
})
export class TestDropdownComponent {
  isOpen = false;
  testItems = ['項目1', '項目2', '項目3', '項目4', '項目5'];

  toggleDropdown() {
    this.isOpen = !this.isOpen;
    console.log('Test dropdown toggled:', this.isOpen);
  }

  closeDropdown() {
    this.isOpen = false;
    console.log('Test dropdown closed');
  }
}
