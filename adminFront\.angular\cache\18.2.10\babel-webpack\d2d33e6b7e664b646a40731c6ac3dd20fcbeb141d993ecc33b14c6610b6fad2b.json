{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { concatMap, of, tap } from 'rxjs';\nimport { Base64ImagePipe } from 'src/app/@theme/pipes/base64-image.pipe';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as JSZip from 'jszip';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nfunction PictureMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction PictureMaterialComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction PictureMaterialComponent_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.batchDelete());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.selectedCount === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u6279\\u6B21\\u522A\\u9664 (\", ctx_r4.selectedCount, \") \");\n  }\n}\nfunction PictureMaterialComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(42);\n      return i0.ɵɵresetView(ctx_r4.addNew(dialog_r7));\n    });\n    i0.ɵɵtext(1, \" \\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_th_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 21)(1, \"nb-checkbox\", 28);\n    i0.ɵɵlistener(\"ngModelChange\", function PictureMaterialComponent_th_27_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleSelectAll($event));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.selectAll);\n  }\n}\nfunction PictureMaterialComponent_tr_38_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"nb-checkbox\", 28);\n    i0.ɵɵlistener(\"ngModelChange\", function PictureMaterialComponent_tr_38_td_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleSelectItem(item_r10.CId));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.isItemSelected(item_r10.CId));\n  }\n}\nfunction PictureMaterialComponent_tr_38_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_tr_38_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(42);\n      return i0.ɵɵresetView(ctx_r4.addNew(dialog_r7, item_r10));\n    });\n    i0.ɵɵtext(1, \"\\u9810\\u89BD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_38_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_tr_38_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(42);\n      return i0.ɵɵresetView(ctx_r4.changePicture(dialog_r7, item_r10));\n    });\n    i0.ɵɵtext(1, \"\\u6539\\u8B8A\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, PictureMaterialComponent_tr_38_td_1_Template, 2, 1, \"td\", 29);\n    i0.ɵɵelementStart(2, \"td\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 30);\n    i0.ɵɵtemplate(12, PictureMaterialComponent_tr_38_button_12_Template, 2, 0, \"button\", 31)(13, PictureMaterialComponent_tr_38_button_13_Template, 2, 0, \"button\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isRead && ctx_r4.images.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r10 == null ? null : item_r10.CPictureCode, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getCategoryLabel(ctx_r4.selectedCategory));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 7, item_r10 == null ? null : item_r10.CUpdateDT, \"yyyy/MM/dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isRead);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r4.imageCounter, \") \");\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_div_6_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"p\", 48)(2, \"strong\");\n    i0.ɵɵtext(3, \"\\u5716\\u7247\\u540D\\u7A31:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 48)(6, \"strong\");\n    i0.ɵɵtext(7, \"\\u66F4\\u65B0\\u6642\\u9593:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.currentImageInfo.CPictureCode, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 2, ctx_r4.currentImageInfo.CUpdateDT, \"yyyy/MM/dd HH:mm:ss\"), \"\");\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_div_6_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_div_6_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.previousImage());\n    });\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵtext(3, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 52);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_div_6_div_6_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.nextImage());\n    });\n    i0.ɵɵtext(7, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(8, \"i\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.imageCounter);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_div_6_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_div_6_div_7_div_2_Template_div_click_0_listener() {\n      const i_r16 = i0.ɵɵrestoreView(_r15).index;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.goToImage(i_r16));\n    });\n    i0.ɵɵelement(1, \"img\", 58);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const img_r17 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"active\", i_r16 === ctx_r4.currentImageIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 3, img_r17.CBase64 || img_r17.CFile), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_div_6_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtemplate(2, PictureMaterialComponent_ng_template_41_div_6_div_7_div_2_Template, 3, 5, \"div\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.currentPreviewImages);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"div\", 43);\n    i0.ɵɵelement(3, \"img\", 44);\n    i0.ɵɵpipe(4, \"base64Image\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, PictureMaterialComponent_ng_template_41_div_6_div_5_Template, 10, 5, \"div\", 45)(6, PictureMaterialComponent_ng_template_41_div_6_div_6_Template, 9, 1, \"div\", 46)(7, PictureMaterialComponent_ng_template_41_div_6_div_7_Template, 3, 1, \"div\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(4, 4, ctx_r4.currentImageShowing), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.currentImageInfo);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.currentPreviewImages.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.currentPreviewImages.length > 1);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_ng_template_7_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 67);\n    i0.ɵɵlistener(\"blur\", function PictureMaterialComponent_ng_template_41_ng_template_7_tr_21_Template_input_blur_2_listener($event) {\n      const i_r22 = i0.ɵɵrestoreView(_r21).index;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.renameFile($event, i_r22));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 68);\n    i0.ɵɵelement(4, \"img\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 30)(6, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_ng_template_7_tr_21_Template_button_click_6_listener() {\n      const picture_r23 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.removeImage(picture_r23.id));\n    });\n    i0.ɵɵtext(7, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const picture_r23 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r23.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r23.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"h6\");\n    i0.ɵɵtext(2, \"\\u4E0A\\u50B3\\u65B9\\u5F0F\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_ng_template_7_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const inputFile_r19 = i0.ɵɵreference(8);\n      return i0.ɵɵresetView(inputFile_r19.click());\n    });\n    i0.ɵɵtext(4, \"\\u55AE\\u5F35\\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_ng_template_7_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const zipInputFile_r20 = i0.ɵɵreference(10);\n      return i0.ɵɵresetView(zipInputFile_r20.click());\n    });\n    i0.ɵɵtext(6, \"ZIP \\u6279\\u91CF\\u532F\\u5165\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"input\", 62, 2);\n    i0.ɵɵlistener(\"change\", function PictureMaterialComponent_ng_template_41_ng_template_7_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.detectFiles($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 63, 3);\n    i0.ɵɵlistener(\"change\", function PictureMaterialComponent_ng_template_41_ng_template_7_Template_input_change_9_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.detectFiles($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 64)(12, \"table\", 65)(13, \"thead\")(14, \"tr\", 19)(15, \"th\", 66);\n    i0.ɵɵtext(16, \"\\u6587\\u4EF6\\u540D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 21);\n    i0.ɵɵtext(18, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"th\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, PictureMaterialComponent_ng_template_41_ng_template_7_tr_21_Template, 8, 2, \"tr\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(!ctx_r4.isEdit ? \"btn btn-info mr-2\" : ctx_r4.listPictures.length < 1 ? \"btn btn-info mr-2\" : \"btn btn-info disable mr-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(!ctx_r4.isEdit ? \"btn btn-success\" : \"btn btn-success disable\");\n    i0.ɵɵproperty(\"disabled\", ctx_r4.isEdit);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.listPictures);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ref_r24 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.uploadImage(ref_r24));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 33)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PictureMaterialComponent_ng_template_41_span_4_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-card-body\", 35);\n    i0.ɵɵtemplate(6, PictureMaterialComponent_ng_template_41_div_6_Template, 8, 6, \"div\", 36)(7, PictureMaterialComponent_ng_template_41_ng_template_7_Template, 22, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"nb-card-footer\")(10, \"div\", 37)(11, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_Template_button_click_11_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r13).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      ref_r24.close();\n      return i0.ɵɵresetView(ctx_r4.resetPreviewState());\n    });\n    i0.ɵɵtext(12, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, PictureMaterialComponent_ng_template_41_button_13_Template, 2, 0, \"button\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const upload_r26 = i0.ɵɵreference(8);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.isPreviewMode ? \"\\u5716\\u7247\\u9810\\u89BD\" : (ctx_r4.selectedCategory === ctx_r4.PictureCategory.BUILDING_MATERIAL ? \"\\u5EFA\\u6750\\u5716\\u7247\" : \"\\u793A\\u610F\\u5716\\u7247\") + \"\\u4E0A\\u50B3\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isPreviewMode && ctx_r4.currentPreviewImages.length > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isPreviewMode)(\"ngIfElse\", upload_r26);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isPreviewMode);\n  }\n}\n// 圖片類別枚舉\nvar PictureCategory = /*#__PURE__*/function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n  return PictureCategory;\n}(PictureCategory || {});\nexport let PictureMaterialComponent = /*#__PURE__*/(() => {\n  class PictureMaterialComponent extends BaseComponent {\n    get selectedCount() {\n      return this.selectedItems.size;\n    }\n    // 獲取類別標籤的方法\n    getCategoryLabel(category) {\n      const option = this.categoryOptions.find(opt => opt.value === category);\n      return option ? option.label : '未知類別';\n    }\n    constructor(_allow, dialogService, valid, _pictureService, _buildCaseService, message, _utilityService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.valid = valid;\n      this._pictureService = _pictureService;\n      this._buildCaseService = _buildCaseService;\n      this.message = message;\n      this._utilityService = _utilityService;\n      this.images = [];\n      this.listUserBuildCases = [];\n      this.selectedCategory = PictureCategory.NONE;\n      this.isCategorySelected = false; // 追蹤用戶是否已經明確選擇類別\n      // 批次選擇相關屬性\n      this.selectedItems = new Set(); // 選中的項目 ID\n      this.selectAll = false; // 全選狀態\n      this.currentImageShowing = \"\";\n      // 輪播預覽相關屬性\n      this.currentPreviewImages = [];\n      this.currentImageIndex = 0;\n      this.isPreviewMode = false;\n      this.listPictures = [];\n      this.isEdit = false;\n      // 類別選項\n      this.categoryOptions = [{\n        value: PictureCategory.BUILDING_MATERIAL,\n        label: '建材圖片'\n      }, {\n        value: PictureCategory.SCHEMATIC,\n        label: '示意圖片'\n      }];\n      // 讓模板可以使用 enum\n      this.PictureCategory = PictureCategory;\n    }\n    ngOnInit() {\n      this.getListBuildCase();\n    }\n    getListBuildCase() {\n      this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.listUserBuildCases = res.Entries ?? [];\n          this.selectedBuildCaseId = this.listUserBuildCases[0].cID;\n        }\n      }), concatMap(() => this.getPicturelList(1))).subscribe();\n    }\n    openPdfInNewTab(CFileUrl) {\n      if (CFileUrl) {\n        this._utilityService.openFileInNewTab(CFileUrl);\n      }\n    }\n    getPicturelList(pageIndex) {\n      // 重置選擇狀態\n      this.selectedItems.clear();\n      this.selectAll = false;\n      if (this.selectedCategory === PictureCategory.BUILDING_MATERIAL) {\n        return this._pictureService.apiPictureGetPictureListPost$Json({\n          body: {\n            PageIndex: pageIndex,\n            PageSize: this.pageSize,\n            CBuildCaseId: this.selectedBuildCaseId,\n            cPictureType: this.selectedCategory\n          }\n        }).pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.images = res.Entries ?? [];\n            this.totalRecords = res.TotalItems;\n            this.updateSelectAllState();\n          }\n        }));\n      } else if (this.selectedCategory === PictureCategory.SCHEMATIC) {\n        return this._pictureService.apiPictureGetPictureListPost$Json({\n          body: {\n            PageIndex: pageIndex,\n            PageSize: this.pageSize,\n            CBuildCaseId: this.selectedBuildCaseId,\n            cPictureType: this.selectedCategory\n          }\n        }).pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.images = res.Entries ?? [];\n            this.totalRecords = res.TotalItems;\n            this.updateSelectAllState();\n          }\n        }));\n      } else {\n        // 如果沒有選擇類別，清空數據並返回預設的空 observable\n        this.images = [];\n        this.totalRecords = 0;\n        return this._pictureService.apiPictureGetPictureListPost$Json({\n          body: {\n            PageIndex: 1,\n            PageSize: 1,\n            CBuildCaseId: -1,\n            // 使用無效 ID 確保返回空結果\n            cPictureType: PictureCategory.NONE\n          }\n        }).pipe(tap(() => {\n          this.images = [];\n          this.totalRecords = 0;\n        }));\n      }\n    }\n    pageChanged(pageIndex) {\n      // this.pageIndex = newPage;\n      this.getPicturelList(pageIndex).subscribe();\n    }\n    selectedChange(buildCaseId) {\n      this.selectedBuildCaseId = buildCaseId;\n      this.getPicturelList(1).subscribe();\n    }\n    categoryChanged(category) {\n      this.selectedCategory = category;\n      this.isCategorySelected = true; // 標記用戶已經選擇了類別\n      this.getPicturelList(1).subscribe();\n    }\n    addNew(ref, item) {\n      // 如果是新增圖片（沒有傳入 item），則檢查是否已選擇類別\n      if (!item && !this.isCategorySelected) {\n        this.message.showErrorMSG('請先選擇圖片類別');\n        return;\n      }\n      this.listPictures = [];\n      this.dialogService.open(ref);\n      this.isEdit = false;\n      if (!!item) {\n        // 預覽模式 - 顯示當前類別的所有圖片並支持輪播\n        this.isPreviewMode = true;\n        this.currentPreviewImages = [...this.images];\n        this.currentImageIndex = this.images.findIndex(img => img.CId === item.CId);\n        if (this.currentImageIndex === -1) {\n          this.currentImageIndex = 0;\n        }\n        this.currentImageShowing = this.getCurrentPreviewImage();\n      } else {\n        // 上傳模式\n        this.isPreviewMode = false;\n        this.currentPreviewImages = [];\n        this.currentImageIndex = 0;\n        this.currentImageShowing = \"\";\n        this.listPictures = [];\n      }\n    }\n    changePicture(ref, item) {\n      // 檢查是否已選擇類別\n      if (!this.selectedCategory) {\n        this.message.showErrorMSG('請先選擇圖片類別');\n        return;\n      }\n      if (!!item && item.CId) {\n        this.dialogService.open(ref);\n        this.isEdit = true;\n        this.currentEditItem = item.CId;\n        this.listPictures = [];\n      }\n    }\n    validation(CFile) {\n      this.valid.clear();\n      const nameSet = new Set();\n      for (const item of CFile) {\n        if (nameSet.has(item.name)) {\n          this.valid.addErrorMessage('檔名不可重複');\n          return;\n        }\n        nameSet.add(item.name);\n      }\n    }\n    onSubmit(ref) {}\n    detectFiles(event) {\n      for (let index = 0; index < event.target.files.length; index++) {\n        const file = event.target.files[index];\n        if (file) {\n          // 檢查是否為 ZIP 檔案\n          if (file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')) {\n            this.processZipFile(file);\n          } else {\n            this.processSingleImageFile(file);\n          }\n        }\n      }\n      // Reset input file to be able to select the old file again\n      event.target.value = null;\n    }\n    processZipFile(zipFile) {\n      const zip = new JSZip();\n      zip.loadAsync(zipFile).then(contents => {\n        const imageFiles = [];\n        contents.forEach((relativePath, file) => {\n          // 只處理圖片檔案，跳過資料夾\n          if (!file.dir && this.isImageFile(relativePath)) {\n            imageFiles.push(file.async('blob').then(blob => {\n              // 只取檔案名稱，移除資料夾路徑\n              const fileName = relativePath.split('/').pop() || relativePath.split('\\\\').pop() || relativePath;\n              // 建立 File 物件\n              const imageFile = new File([blob], fileName, {\n                type: this.getImageMimeType(relativePath)\n              });\n              return this.processImageFileFromZip(imageFile, fileName);\n            }));\n          }\n        });\n        // 處理所有圖片檔案\n        Promise.all(imageFiles).then(() => {\n          this.message.showSucessMSG(`成功從 ZIP 檔案中匯入 ${imageFiles.length} 張圖片`);\n        }).catch(error => {\n          console.error('處理 ZIP 檔案中的圖片時發生錯誤:', error);\n          this.message.showErrorMSG('處理 ZIP 檔案中的圖片時發生錯誤');\n        });\n      }).catch(error => {\n        console.error('讀取 ZIP 檔案時發生錯誤:', error);\n        this.message.showErrorMSG('無法讀取 ZIP 檔案，請確認檔案格式正確');\n      });\n    }\n    processSingleImageFile(file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        this.addImageToList(file, base64Str);\n      };\n    }\n    processImageFileFromZip(file, originalPath) {\n      return new Promise((resolve, reject) => {\n        let reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => {\n          let base64Str = reader.result;\n          if (!base64Str) {\n            reject('無法讀取圖片檔案');\n            return;\n          }\n          this.addImageToList(file, base64Str, originalPath);\n          resolve();\n        };\n        reader.onerror = () => {\n          reject('讀取圖片檔案時發生錯誤');\n        };\n      });\n    }\n    addImageToList(file, base64Str, originalPath) {\n      // Get name file ( no extension)\n      let fileName = originalPath || file.name;\n      // 如果是從 ZIP 檔案來的，只取檔案名稱，移除資料夾路徑\n      if (originalPath) {\n        fileName = originalPath.split('/').pop() || originalPath.split('\\\\').pop() || originalPath;\n      }\n      const fileNameWithoutExtension = fileName.split('.')[0];\n      // Find files with duplicate names\n      const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\n      if (existingFileIndex !== -1) {\n        // If name is duplicate, update file data\n        this.listPictures[existingFileIndex] = {\n          ...this.listPictures[existingFileIndex],\n          data: base64Str,\n          CFile: file,\n          extension: this._utilityService.getFileExtension(fileName)\n        };\n      } else {\n        // If not duplicate, add new file\n        this.listPictures.push({\n          id: new Date().getTime() + Math.random(),\n          name: fileNameWithoutExtension,\n          data: base64Str,\n          extension: this._utilityService.getFileExtension(fileName),\n          CFile: file\n        });\n      }\n    }\n    isImageFile(fileName) {\n      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];\n      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n      return imageExtensions.includes(extension);\n    }\n    getImageMimeType(fileName) {\n      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n      switch (extension) {\n        case '.jpg':\n        case '.jpeg':\n          return 'image/jpeg';\n        case '.png':\n          return 'image/png';\n        case '.gif':\n          return 'image/gif';\n        case '.bmp':\n          return 'image/bmp';\n        case '.webp':\n          return 'image/webp';\n        default:\n          return 'image/jpeg';\n      }\n    }\n    removeImage(pictureId) {\n      this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n    }\n    uploadImage(ref) {\n      if (!this.isEdit) {\n        const CFile = this.listPictures.map(x => x.CFile);\n        this.validation(CFile);\n        if (this.valid.errorMessages.length > 0) {\n          this.message.showErrorMSGs(this.valid.errorMessages);\n          return;\n        } // 統一使用 PictureService 進行上傳\n        const uploadRequest = this._pictureService.apiPictureUploadListPicturePost$Json({\n          body: {\n            CBuildCaseId: this.selectedBuildCaseId,\n            CPath: this.selectedCategory === PictureCategory.BUILDING_MATERIAL ? \"picture\" : \"infoPicture\",\n            CFile: CFile,\n            CPictureType: this.selectedCategory\n          }\n        });\n        uploadRequest.pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG('執行成功');\n            this.listPictures = [];\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n          ref.close();\n          this.resetPreviewState();\n        }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n      } else {\n        if (this.listPictures.length > 0 && this.listPictures[0].CFile) {\n          // 統一使用 PictureService 進行更新\n          const updateRequest = this._pictureService.apiPictureUpdatePicturePost$Json({\n            body: {\n              CBuildCaseID: this.selectedBuildCaseId,\n              CPictureID: this.currentEditItem,\n              CFile: this.listPictures[0].CFile\n            }\n          });\n          updateRequest.pipe(tap(res => {\n            if (res.StatusCode == 0) {\n              this.message.showSucessMSG('執行成功');\n              this.listPictures = [];\n            } else {\n              this.message.showErrorMSG(res.Message);\n            }\n            ref.close();\n            this.resetPreviewState();\n          }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n        }\n      }\n    }\n    renameFile(event, index) {\n      var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n      var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n        type: this.listPictures[index].CFile.type\n      });\n      this.listPictures[index].CFile = newFile;\n    }\n    // 批次選擇相關方法\n    toggleSelectItem(itemId) {\n      if (this.selectedItems.has(itemId)) {\n        this.selectedItems.delete(itemId);\n      } else {\n        this.selectedItems.add(itemId);\n      }\n      this.updateSelectAllState();\n    }\n    toggleSelectAll(checked) {\n      this.selectAll = checked;\n      if (checked) {\n        // 全選當前頁面的所有項目\n        this.images.forEach(item => {\n          if (item.CId) {\n            this.selectedItems.add(item.CId);\n          }\n        });\n      } else {\n        // 取消全選 - 只移除當前頁面的項目\n        const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined);\n        currentPageIds.forEach(id => this.selectedItems.delete(id));\n      }\n    }\n    updateSelectAllState() {\n      const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined);\n      this.selectAll = currentPageIds.length > 0 && currentPageIds.every(id => this.selectedItems.has(id));\n    }\n    // 批次刪除方法\n    batchDelete() {\n      if (this.selectedItems.size === 0) {\n        this.message.showErrorMSG('請選擇要刪除的項目');\n        return;\n      }\n      const selectedIds = Array.from(this.selectedItems);\n      // 顯示確認對話框\n      if (confirm(`確定要刪除選中的 ${selectedIds.length} 個項目嗎？`)) {\n        this._pictureService.apiPictureDeletePicturePost$Json({\n          body: selectedIds\n        }).pipe(tap(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG(`成功刪除 ${selectedIds.length} 個項目`);\n            // 清空選擇狀態\n            this.selectedItems.clear();\n            this.selectAll = false;\n          } else {\n            this.message.showErrorMSG(res.Message || '刪除失敗');\n          }\n        }), concatMap(res => res.StatusCode === 0 ? this.getPicturelList(this.pageIndex) : of(null))).subscribe();\n      }\n    }\n    // 檢查項目是否被選中\n    isItemSelected(itemId) {\n      return this.selectedItems.has(itemId);\n    }\n    // 輪播預覽相關方法\n    getCurrentPreviewImage() {\n      if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {\n        return '';\n      }\n      const currentImage = this.currentPreviewImages[this.currentImageIndex];\n      // 優先使用 CBase64，如果沒有則使用 CFile\n      return currentImage.CBase64 || currentImage.CFile || '';\n    }\n    previousImage() {\n      if (this.currentPreviewImages.length === 0) return;\n      this.currentImageIndex = this.currentImageIndex > 0 ? this.currentImageIndex - 1 : this.currentPreviewImages.length - 1;\n      this.currentImageShowing = this.getCurrentPreviewImage();\n    }\n    nextImage() {\n      if (this.currentPreviewImages.length === 0) return;\n      this.currentImageIndex = this.currentImageIndex < this.currentPreviewImages.length - 1 ? this.currentImageIndex + 1 : 0;\n      this.currentImageShowing = this.getCurrentPreviewImage();\n    }\n    goToImage(index) {\n      if (index >= 0 && index < this.currentPreviewImages.length) {\n        this.currentImageIndex = index;\n        this.currentImageShowing = this.getCurrentPreviewImage();\n      }\n    }\n    get currentImageInfo() {\n      if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {\n        return null;\n      }\n      return this.currentPreviewImages[this.currentImageIndex];\n    }\n    get imageCounter() {\n      if (this.currentPreviewImages.length === 0) return '';\n      return `${this.currentImageIndex + 1} / ${this.currentPreviewImages.length}`;\n    }\n    // 重置預覽狀態\n    resetPreviewState() {\n      this.isPreviewMode = false;\n      this.currentPreviewImages = [];\n      this.currentImageIndex = 0;\n      this.currentImageShowing = \"\";\n    }\n    // 鍵盤導航支持\n    handleKeyboardEvent(event) {\n      if (!this.isPreviewMode || this.currentPreviewImages.length <= 1) return;\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.previousImage();\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImage();\n          break;\n        case 'Escape':\n          event.preventDefault();\n          // 可以在這裡添加關閉對話框的邏輯\n          break;\n      }\n    }\n    static {\n      this.ɵfac = function PictureMaterialComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PictureMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.PictureService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.UtilityService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PictureMaterialComponent,\n        selectors: [[\"ngx-picture-material\"]],\n        hostBindings: function PictureMaterialComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"keydown\", function PictureMaterialComponent_keydown_HostBindingHandler($event) {\n              return ctx.handleKeyboardEvent($event);\n            }, false, i0.ɵɵresolveDocument);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 43,\n        vars: 11,\n        consts: [[\"dialog\", \"\"], [\"upload\", \"\"], [\"inputFile\", \"\"], [\"zipInputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"category\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5716\\u7247\\u985E\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-danger mr-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", \"class\", \"col-1\", 4, \"ngIf\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [3, \"ngModelChange\", \"ngModel\"], [4, \"ngIf\"], [1, \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [\"class\", \"ml-2 text-muted\", 4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [\"class\", \"w-full h-auto\", 4, \"ngIf\", \"ngIfElse\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"class\", \"btn btn-success\", 3, \"click\", 4, \"ngIf\"], [1, \"ml-2\", \"text-muted\"], [1, \"w-full\", \"h-auto\"], [1, \"preview-container\", \"position-relative\", 2, \"min-height\", \"400px\"], [1, \"text-center\", \"mb-3\"], [1, \"fit-size\", 2, \"max-height\", \"400px\", \"max-width\", \"100%\", \"object-fit\", \"contain\", 3, \"src\"], [\"class\", \"text-center mb-3\", 4, \"ngIf\"], [\"class\", \"carousel-controls d-flex justify-content-between align-items-center\", 4, \"ngIf\"], [\"class\", \"thumbnail-nav mt-3\", 4, \"ngIf\"], [1, \"mb-1\"], [1, \"carousel-controls\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"nb-chevron-left\"], [1, \"text-muted\"], [1, \"nb-chevron-right\"], [1, \"thumbnail-nav\", \"mt-3\"], [1, \"d-flex\", \"flex-wrap\", \"justify-content-center\"], [\"class\", \"thumbnail-item m-1\", \"style\", \"cursor: pointer; border: 2px solid transparent; padding: 2px;\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"thumbnail-item\", \"m-1\", 2, \"cursor\", \"pointer\", \"border\", \"2px solid transparent\", \"padding\", \"2px\", 3, \"click\"], [2, \"width\", \"60px\", \"height\", \"60px\", \"object-fit\", \"cover\", \"border-radius\", \"4px\", 3, \"src\"], [1, \"mb-3\"], [3, \"click\"], [3, \"click\", \"disabled\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", \"multiple\", \"\", 1, \"hidden\", 3, \"change\"], [\"type\", \"file\", \"accept\", \".zip,application/zip\", 1, \"hidden\", 3, \"change\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"scope\", \"col\", 1, \"col-4\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"w-[100px]\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"]],\n        template: function PictureMaterialComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 4)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 5);\n            i0.ɵɵtext(5, \"\\u53EF\\u8A2D\\u5B9A\\u4E0A\\u50B3\\u5EFA\\u6750\\u793A\\u610F\\u5716\\u7247\\uFF0C\\u4E0A\\u50B3\\u524D\\u8ACB\\u5C07\\u5716\\u7247\\u6A94\\u6848\\u6539\\u70BA\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\\u3002\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"label\", 9);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PictureMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function PictureMaterialComponent_Template_nb_select_selectedChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectedChange($event));\n            });\n            i0.ɵɵtemplate(12, PictureMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 8)(15, \"label\", 12);\n            i0.ɵɵtext(16, \"\\u985E\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"nb-select\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PictureMaterialComponent_Template_nb_select_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedCategory, $event) || (ctx.selectedCategory = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function PictureMaterialComponent_Template_nb_select_selectedChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.categoryChanged($event));\n            });\n            i0.ɵɵtemplate(18, PictureMaterialComponent_nb_option_18_Template, 2, 2, \"nb-option\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"div\", 7)(20, \"div\", 14);\n            i0.ɵɵtemplate(21, PictureMaterialComponent_button_21_Template, 2, 2, \"button\", 15)(22, PictureMaterialComponent_button_22_Template, 2, 0, \"button\", 16);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(23, \"div\", 17)(24, \"table\", 18)(25, \"thead\")(26, \"tr\", 19);\n            i0.ɵɵtemplate(27, PictureMaterialComponent_th_27_Template, 3, 1, \"th\", 20);\n            i0.ɵɵelementStart(28, \"th\", 21);\n            i0.ɵɵtext(29, \"\\u9805\\u6B21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"th\", 21);\n            i0.ɵɵtext(31, \"\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"th\", 21);\n            i0.ɵɵtext(33, \"\\u985E\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"th\", 21);\n            i0.ɵɵtext(35, \"\\u6700\\u65B0\\u5716\\u7247\\u4E0A\\u50B3\\u6642\\u9593\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(36, \"th\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"tbody\");\n            i0.ɵɵtemplate(38, PictureMaterialComponent_tr_38_Template, 14, 10, \"tr\", 22);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(39, \"nb-card-footer\", 23)(40, \"ngx-pagination\", 24);\n            i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function PictureMaterialComponent_Template_ngx_pagination_CollectionSizeChange_40_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n              return i0.ɵɵresetView($event);\n            })(\"PageSizeChange\", function PictureMaterialComponent_Template_ngx_pagination_PageSizeChange_40_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n              return i0.ɵɵresetView($event);\n            })(\"PageChange\", function PictureMaterialComponent_Template_ngx_pagination_PageChange_40_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function PictureMaterialComponent_Template_ngx_pagination_PageChange_40_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(41, PictureMaterialComponent_ng_template_41_Template, 14, 5, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.listUserBuildCases);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCategory);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.categoryOptions);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isRead && ctx.images.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.isRead && ctx.images.length > 0);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngForOf\", ctx.images);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n          }\n        },\n        dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DatePipe, SharedModule, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent, Base64ImagePipe],\n        styles: [\".disable[_ngcontent-%COMP%]{opacity:.5;pointer-events:none}.preview-container[_ngcontent-%COMP%]   .carousel-controls[_ngcontent-%COMP%]{margin-top:20px}.preview-container[_ngcontent-%COMP%]   .thumbnail-nav[_ngcontent-%COMP%]{max-height:120px;overflow-y:auto}.preview-container[_ngcontent-%COMP%]   .thumbnail-nav[_ngcontent-%COMP%]   .thumbnail-item[_ngcontent-%COMP%]{transition:all .3s ease}.preview-container[_ngcontent-%COMP%]   .thumbnail-nav[_ngcontent-%COMP%]   .thumbnail-item[_ngcontent-%COMP%]:hover{border-color:#007bff!important;transform:scale(1.05)}.preview-container[_ngcontent-%COMP%]   .thumbnail-nav[_ngcontent-%COMP%]   .thumbnail-item.active[_ngcontent-%COMP%]{border-color:#27ae60!important;box-shadow:0 0 8px #27ae604d}.preview-container[_ngcontent-%COMP%]   .thumbnail-nav[_ngcontent-%COMP%]   .thumbnail-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transition:all .3s ease}.fit-size[_ngcontent-%COMP%]{max-width:100%;height:auto;border-radius:8px;box-shadow:0 2px 8px #0000001a}\"]\n      });\n    }\n  }\n  return PictureMaterialComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}