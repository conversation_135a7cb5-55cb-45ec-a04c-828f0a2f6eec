{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiSpaceDeleteSpacePost$Json } from '../fn/space/api-space-delete-space-post-json';\nimport { apiSpaceDeleteSpacePost$Plain } from '../fn/space/api-space-delete-space-post-plain';\nimport { apiSpaceGetSpaceByIdPost$Json } from '../fn/space/api-space-get-space-by-id-post-json';\nimport { apiSpaceGetSpaceByIdPost$Plain } from '../fn/space/api-space-get-space-by-id-post-plain';\nimport { apiSpaceGetSpaceListForTemplatePost$Json } from '../fn/space/api-space-get-space-list-for-template-post-json';\nimport { apiSpaceGetSpaceListForTemplatePost$Plain } from '../fn/space/api-space-get-space-list-for-template-post-plain';\nimport { apiSpaceGetSpaceListPost$Json } from '../fn/space/api-space-get-space-list-post-json';\nimport { apiSpaceGetSpaceListPost$Plain } from '../fn/space/api-space-get-space-list-post-plain';\nimport { apiSpaceSaveSpacePost$Json } from '../fn/space/api-space-save-space-post-json';\nimport { apiSpaceSaveSpacePost$Plain } from '../fn/space/api-space-save-space-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class SpaceService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiSpaceGetSpaceListPost()` */\n  static {\n    this.ApiSpaceGetSpaceListPostPath = '/api/Space/GetSpaceList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpaceGetSpaceListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceListPost$Plain$Response(params, context) {\n    return apiSpaceGetSpaceListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceListPost$Plain(params, context) {\n    return this.apiSpaceGetSpaceListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpaceGetSpaceListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceListPost$Json$Response(params, context) {\n    return apiSpaceGetSpaceListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceListPost$Json(params, context) {\n    return this.apiSpaceGetSpaceListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpaceGetSpaceByIdPost()` */\n  static {\n    this.ApiSpaceGetSpaceByIdPostPath = '/api/Space/GetSpaceById';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpaceGetSpaceByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceByIdPost$Plain$Response(params, context) {\n    return apiSpaceGetSpaceByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceByIdPost$Plain(params, context) {\n    return this.apiSpaceGetSpaceByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpaceGetSpaceByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceByIdPost$Json$Response(params, context) {\n    return apiSpaceGetSpaceByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceByIdPost$Json(params, context) {\n    return this.apiSpaceGetSpaceByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpaceSaveSpacePost()` */\n  static {\n    this.ApiSpaceSaveSpacePostPath = '/api/Space/SaveSpace';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpaceSaveSpacePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceSaveSpacePost$Plain$Response(params, context) {\n    return apiSpaceSaveSpacePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpaceSaveSpacePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceSaveSpacePost$Plain(params, context) {\n    return this.apiSpaceSaveSpacePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpaceSaveSpacePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceSaveSpacePost$Json$Response(params, context) {\n    return apiSpaceSaveSpacePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpaceSaveSpacePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceSaveSpacePost$Json(params, context) {\n    return this.apiSpaceSaveSpacePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpaceDeleteSpacePost()` */\n  static {\n    this.ApiSpaceDeleteSpacePostPath = '/api/Space/DeleteSpace';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpaceDeleteSpacePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceDeleteSpacePost$Plain$Response(params, context) {\n    return apiSpaceDeleteSpacePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpaceDeleteSpacePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceDeleteSpacePost$Plain(params, context) {\n    return this.apiSpaceDeleteSpacePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpaceDeleteSpacePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceDeleteSpacePost$Json$Response(params, context) {\n    return apiSpaceDeleteSpacePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpaceDeleteSpacePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceDeleteSpacePost$Json(params, context) {\n    return this.apiSpaceDeleteSpacePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpaceGetSpaceListForTemplatePost()` */\n  static {\n    this.ApiSpaceGetSpaceListForTemplatePostPath = '/api/Space/GetSpaceListForTemplate';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpaceGetSpaceListForTemplatePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceListForTemplatePost$Plain$Response(params, context) {\n    return apiSpaceGetSpaceListForTemplatePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceListForTemplatePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceListForTemplatePost$Plain(params, context) {\n    return this.apiSpaceGetSpaceListForTemplatePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpaceGetSpaceListForTemplatePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceListForTemplatePost$Json$Response(params, context) {\n    return apiSpaceGetSpaceListForTemplatePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceListForTemplatePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpaceGetSpaceListForTemplatePost$Json(params, context) {\n    return this.apiSpaceGetSpaceListForTemplatePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function SpaceService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SpaceService,\n      factory: SpaceService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiSpaceDeleteSpacePost$Json", "apiSpaceDeleteSpacePost$Plain", "apiSpaceGetSpaceByIdPost$Json", "apiSpaceGetSpaceByIdPost$Plain", "apiSpaceGetSpaceListForTemplatePost$Json", "apiSpaceGetSpaceListForTemplatePost$Plain", "apiSpaceGetSpaceListPost$Json", "apiSpaceGetSpaceListPost$Plain", "apiSpaceSaveSpacePost$Json", "apiSpaceSaveSpacePost$Plain", "SpaceService", "constructor", "config", "http", "ApiSpaceGetSpaceListPostPath", "apiSpaceGetSpaceListPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiSpaceGetSpaceListPost$Json$Response", "ApiSpaceGetSpaceByIdPostPath", "apiSpaceGetSpaceByIdPost$Plain$Response", "apiSpaceGetSpaceByIdPost$Json$Response", "ApiSpaceSaveSpacePostPath", "apiSpaceSaveSpacePost$Plain$Response", "apiSpaceSaveSpacePost$Json$Response", "ApiSpaceDeleteSpacePostPath", "apiSpaceDeleteSpacePost$Plain$Response", "apiSpaceDeleteSpacePost$Json$Response", "ApiSpaceGetSpaceListForTemplatePostPath", "apiSpaceGetSpaceListForTemplatePost$Plain$Response", "apiSpaceGetSpaceListForTemplatePost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\space.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiSpaceDeleteSpacePost$Json } from '../fn/space/api-space-delete-space-post-json';\r\nimport { ApiSpaceDeleteSpacePost$Json$Params } from '../fn/space/api-space-delete-space-post-json';\r\nimport { apiSpaceDeleteSpacePost$Plain } from '../fn/space/api-space-delete-space-post-plain';\r\nimport { ApiSpaceDeleteSpacePost$Plain$Params } from '../fn/space/api-space-delete-space-post-plain';\r\nimport { apiSpaceGetSpaceByIdPost$Json } from '../fn/space/api-space-get-space-by-id-post-json';\r\nimport { ApiSpaceGetSpaceByIdPost$Json$Params } from '../fn/space/api-space-get-space-by-id-post-json';\r\nimport { apiSpaceGetSpaceByIdPost$Plain } from '../fn/space/api-space-get-space-by-id-post-plain';\r\nimport { ApiSpaceGetSpaceByIdPost$Plain$Params } from '../fn/space/api-space-get-space-by-id-post-plain';\r\nimport { apiSpaceGetSpaceListForTemplatePost$Json } from '../fn/space/api-space-get-space-list-for-template-post-json';\r\nimport { ApiSpaceGetSpaceListForTemplatePost$Json$Params } from '../fn/space/api-space-get-space-list-for-template-post-json';\r\nimport { apiSpaceGetSpaceListForTemplatePost$Plain } from '../fn/space/api-space-get-space-list-for-template-post-plain';\r\nimport { ApiSpaceGetSpaceListForTemplatePost$Plain$Params } from '../fn/space/api-space-get-space-list-for-template-post-plain';\r\nimport { apiSpaceGetSpaceListPost$Json } from '../fn/space/api-space-get-space-list-post-json';\r\nimport { ApiSpaceGetSpaceListPost$Json$Params } from '../fn/space/api-space-get-space-list-post-json';\r\nimport { apiSpaceGetSpaceListPost$Plain } from '../fn/space/api-space-get-space-list-post-plain';\r\nimport { ApiSpaceGetSpaceListPost$Plain$Params } from '../fn/space/api-space-get-space-list-post-plain';\r\nimport { apiSpaceSaveSpacePost$Json } from '../fn/space/api-space-save-space-post-json';\r\nimport { ApiSpaceSaveSpacePost$Json$Params } from '../fn/space/api-space-save-space-post-json';\r\nimport { apiSpaceSaveSpacePost$Plain } from '../fn/space/api-space-save-space-post-plain';\r\nimport { ApiSpaceSaveSpacePost$Plain$Params } from '../fn/space/api-space-save-space-post-plain';\r\nimport { GetSpaceListResponseListResponseBase } from '../models/get-space-list-response-list-response-base';\r\nimport { GetSpaceListResponseResponseBase } from '../models/get-space-list-response-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class SpaceService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiSpaceGetSpaceListPost()` */\r\n  static readonly ApiSpaceGetSpaceListPostPath = '/api/Space/GetSpaceList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpaceGetSpaceListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceListPost$Plain$Response(params?: ApiSpaceGetSpaceListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseListResponseBase>> {\r\n    return apiSpaceGetSpaceListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceListPost$Plain(params?: ApiSpaceGetSpaceListPost$Plain$Params, context?: HttpContext): Observable<GetSpaceListResponseListResponseBase> {\r\n    return this.apiSpaceGetSpaceListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSpaceListResponseListResponseBase>): GetSpaceListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpaceGetSpaceListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceListPost$Json$Response(params?: ApiSpaceGetSpaceListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseListResponseBase>> {\r\n    return apiSpaceGetSpaceListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceListPost$Json(params?: ApiSpaceGetSpaceListPost$Json$Params, context?: HttpContext): Observable<GetSpaceListResponseListResponseBase> {\r\n    return this.apiSpaceGetSpaceListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSpaceListResponseListResponseBase>): GetSpaceListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpaceGetSpaceByIdPost()` */\r\n  static readonly ApiSpaceGetSpaceByIdPostPath = '/api/Space/GetSpaceById';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpaceGetSpaceByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceByIdPost$Plain$Response(params?: ApiSpaceGetSpaceByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseResponseBase>> {\r\n    return apiSpaceGetSpaceByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceByIdPost$Plain(params?: ApiSpaceGetSpaceByIdPost$Plain$Params, context?: HttpContext): Observable<GetSpaceListResponseResponseBase> {\r\n    return this.apiSpaceGetSpaceByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSpaceListResponseResponseBase>): GetSpaceListResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpaceGetSpaceByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceByIdPost$Json$Response(params?: ApiSpaceGetSpaceByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseResponseBase>> {\r\n    return apiSpaceGetSpaceByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceByIdPost$Json(params?: ApiSpaceGetSpaceByIdPost$Json$Params, context?: HttpContext): Observable<GetSpaceListResponseResponseBase> {\r\n    return this.apiSpaceGetSpaceByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSpaceListResponseResponseBase>): GetSpaceListResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpaceSaveSpacePost()` */\r\n  static readonly ApiSpaceSaveSpacePostPath = '/api/Space/SaveSpace';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpaceSaveSpacePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceSaveSpacePost$Plain$Response(params?: ApiSpaceSaveSpacePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpaceSaveSpacePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpaceSaveSpacePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceSaveSpacePost$Plain(params?: ApiSpaceSaveSpacePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpaceSaveSpacePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpaceSaveSpacePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceSaveSpacePost$Json$Response(params?: ApiSpaceSaveSpacePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpaceSaveSpacePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpaceSaveSpacePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceSaveSpacePost$Json(params?: ApiSpaceSaveSpacePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpaceSaveSpacePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpaceDeleteSpacePost()` */\r\n  static readonly ApiSpaceDeleteSpacePostPath = '/api/Space/DeleteSpace';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpaceDeleteSpacePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceDeleteSpacePost$Plain$Response(params?: ApiSpaceDeleteSpacePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpaceDeleteSpacePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpaceDeleteSpacePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceDeleteSpacePost$Plain(params?: ApiSpaceDeleteSpacePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpaceDeleteSpacePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpaceDeleteSpacePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceDeleteSpacePost$Json$Response(params?: ApiSpaceDeleteSpacePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpaceDeleteSpacePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpaceDeleteSpacePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceDeleteSpacePost$Json(params?: ApiSpaceDeleteSpacePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpaceDeleteSpacePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpaceGetSpaceListForTemplatePost()` */\r\n  static readonly ApiSpaceGetSpaceListForTemplatePostPath = '/api/Space/GetSpaceListForTemplate';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpaceGetSpaceListForTemplatePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceListForTemplatePost$Plain$Response(params?: ApiSpaceGetSpaceListForTemplatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseListResponseBase>> {\r\n    return apiSpaceGetSpaceListForTemplatePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceListForTemplatePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceListForTemplatePost$Plain(params?: ApiSpaceGetSpaceListForTemplatePost$Plain$Params, context?: HttpContext): Observable<GetSpaceListResponseListResponseBase> {\r\n    return this.apiSpaceGetSpaceListForTemplatePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSpaceListResponseListResponseBase>): GetSpaceListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpaceGetSpaceListForTemplatePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceListForTemplatePost$Json$Response(params?: ApiSpaceGetSpaceListForTemplatePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseListResponseBase>> {\r\n    return apiSpaceGetSpaceListForTemplatePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpaceGetSpaceListForTemplatePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpaceGetSpaceListForTemplatePost$Json(params?: ApiSpaceGetSpaceListForTemplatePost$Json$Params, context?: HttpContext): Observable<GetSpaceListResponseListResponseBase> {\r\n    return this.apiSpaceGetSpaceListForTemplatePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSpaceListResponseListResponseBase>): GetSpaceListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAOA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,4BAA4B,QAAQ,8CAA8C;AAE3F,SAASC,6BAA6B,QAAQ,+CAA+C;AAE7F,SAASC,6BAA6B,QAAQ,iDAAiD;AAE/F,SAASC,8BAA8B,QAAQ,kDAAkD;AAEjG,SAASC,wCAAwC,QAAQ,6DAA6D;AAEtH,SAASC,yCAAyC,QAAQ,8DAA8D;AAExH,SAASC,6BAA6B,QAAQ,gDAAgD;AAE9F,SAASC,8BAA8B,QAAQ,iDAAiD;AAEhG,SAASC,0BAA0B,QAAQ,4CAA4C;AAEvF,SAASC,2BAA2B,QAAQ,6CAA6C;;;;AAOzF,OAAM,MAAOC,YAAa,SAAQX,WAAW;EAC3CY,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,4BAA4B,GAAG,yBAAyB;EAAC;EAEzE;;;;;;EAMAC,uCAAuCA,CAACC,MAA8C,EAAEC,OAAqB;IAC3G,OAAOV,8BAA8B,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMAV,8BAA8BA,CAACS,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAACF,uCAAuC,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvErB,GAAG,CAAEsB,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;EAEA;;;;;;EAMAC,sCAAsCA,CAACN,MAA6C,EAAEC,OAAqB;IACzG,OAAOX,6BAA6B,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAX,6BAA6BA,CAACU,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACK,sCAAsC,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtErB,GAAG,CAAEsB,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;EAEA;;IACgB,KAAAE,4BAA4B,GAAG,yBAAyB;EAAC;EAEzE;;;;;;EAMAC,uCAAuCA,CAACR,MAA8C,EAAEC,OAAqB;IAC3G,OAAOd,8BAA8B,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMAd,8BAA8BA,CAACa,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAACO,uCAAuC,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvErB,GAAG,CAAEsB,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMAI,sCAAsCA,CAACT,MAA6C,EAAEC,OAAqB;IACzG,OAAOf,6BAA6B,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAf,6BAA6BA,CAACc,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACQ,sCAAsC,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtErB,GAAG,CAAEsB,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAAK,yBAAyB,GAAG,sBAAsB;EAAC;EAEnE;;;;;;EAMAC,oCAAoCA,CAACX,MAA2C,EAAEC,OAAqB;IACrG,OAAOR,2BAA2B,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9E;EAEA;;;;;;EAMAR,2BAA2BA,CAACO,MAA2C,EAAEC,OAAqB;IAC5F,OAAO,IAAI,CAACU,oCAAoC,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAO,mCAAmCA,CAACZ,MAA0C,EAAEC,OAAqB;IACnG,OAAOT,0BAA0B,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7E;EAEA;;;;;;EAMAT,0BAA0BA,CAACQ,MAA0C,EAAEC,OAAqB;IAC1F,OAAO,IAAI,CAACW,mCAAmC,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAQ,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,sCAAsCA,CAACd,MAA6C,EAAEC,OAAqB;IACzG,OAAOhB,6BAA6B,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAhB,6BAA6BA,CAACe,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACa,sCAAsC,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAU,qCAAqCA,CAACf,MAA4C,EAAEC,OAAqB;IACvG,OAAOjB,4BAA4B,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAjB,4BAA4BA,CAACgB,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACc,qCAAqC,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAW,uCAAuC,GAAG,oCAAoC;EAAC;EAE/F;;;;;;EAMAC,kDAAkDA,CAACjB,MAAyD,EAAEC,OAAqB;IACjI,OAAOZ,yCAAyC,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC5F;EAEA;;;;;;EAMAZ,yCAAyCA,CAACW,MAAyD,EAAEC,OAAqB;IACxH,OAAO,IAAI,CAACgB,kDAAkD,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAClFrB,GAAG,CAAEsB,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;EAEA;;;;;;EAMAa,iDAAiDA,CAAClB,MAAwD,EAAEC,OAAqB;IAC/H,OAAOb,wCAAwC,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3F;EAEA;;;;;;EAMAb,wCAAwCA,CAACY,MAAwD,EAAEC,OAAqB;IACtH,OAAO,IAAI,CAACiB,iDAAiD,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjFrB,GAAG,CAAEsB,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;;;uCA9OWX,YAAY,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAZ9B,YAAY;MAAA+B,OAAA,EAAZ/B,YAAY,CAAAgC,IAAA;MAAAC,UAAA,EADC;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}