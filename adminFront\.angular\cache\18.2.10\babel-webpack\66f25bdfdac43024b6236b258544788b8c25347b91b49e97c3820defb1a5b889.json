{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport { HouseholdManagementComponent } from \"./household-management.component\";\nimport { CustomerChangePictureComponent } from \"./customer-change-picture/customer-change-picture.component\";\nimport { SampleSelectionResultComponent } from \"./sample-selection-result/sample-selection-result.component\";\nimport { ModifyFloorPlanComponent } from \"./modify-floor-plan/modify-floor-plan.component\";\nimport { ModifyHouseholdComponent } from \"./modify-household/modify-household.component\";\nimport { ModifyHouseTypeComponent } from \"./modify-house-type/modify-house-type.component\";\nimport { StandardHousePlanComponent } from \"./standard-house-plan/standard-house-plan.component\";\nimport { FinaldochouseManagementComponent } from \"./finaldochouse-management/finaldochouse-management.component\";\nimport { SpaceComponent } from \"../space/space.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: HouseholdManagementComponent\n}, {\n  path: \"customer-change-picture/:id1/:id2\",\n  component: CustomerChangePictureComponent\n}, {\n  path: \"sample-selection-result/:id1/:id2\",\n  component: SampleSelectionResultComponent\n}, {\n  path: \"modify-floor-plan/:id\",\n  component: ModifyFloorPlanComponent\n}, {\n  path: \"modify-household/:id\",\n  component: ModifyHouseholdComponent\n}, {\n  path: \"modify-house-type/:id\",\n  component: ModifyHouseTypeComponent\n}, {\n  path: \"standard-house-plan/:id\",\n  component: StandardHousePlanComponent\n}, {\n  path: \"finaldochouse_management/:id1/:id2\",\n  component: FinaldochouseManagementComponent\n}, {\n  path: \"space\",\n  component: SpaceComponent\n}];\nexport class HouseholdRoutingModule {\n  static {\n    this.ɵfac = function HouseholdRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HouseholdRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HouseholdRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "HouseholdManagementComponent", "CustomerChangePictureComponent", "SampleSelectionResultComponent", "ModifyFloorPlanComponent", "ModifyHouseholdComponent", "ModifyHouseTypeComponent", "StandardHousePlanComponent", "FinaldochouseManagementComponent", "SpaceComponent", "routes", "path", "component", "HouseholdRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management-routing.module.ts"], "sourcesContent": ["\r\nimport { RouterModule, Routes } from \"@angular/router\";\r\nimport { NgModule } from \"@angular/core\";\r\nimport { HouseholdManagementComponent } from \"./household-management.component\";\r\nimport { CustomerChangePictureComponent } from \"./customer-change-picture/customer-change-picture.component\";\r\nimport { SampleSelectionResultComponent } from \"./sample-selection-result/sample-selection-result.component\";\r\nimport { ModifyFloorPlanComponent } from \"./modify-floor-plan/modify-floor-plan.component\";\r\nimport { ModifyHouseholdComponent } from \"./modify-household/modify-household.component\";\r\nimport { ModifyHouseTypeComponent } from \"./modify-house-type/modify-house-type.component\";\r\nimport { StandardHousePlanComponent } from \"./standard-house-plan/standard-house-plan.component\";\r\nimport { FinaldochouseManagementComponent } from \"./finaldochouse-management/finaldochouse-management.component\";\r\nimport { SpaceComponent } from \"../space/space.component\";\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: HouseholdManagementComponent,\r\n  },\r\n  {\r\n    path: \"customer-change-picture/:id1/:id2\",\r\n    component: CustomerChangePictureComponent,\r\n  },\r\n  {\r\n    path: \"sample-selection-result/:id1/:id2\",\r\n    component: SampleSelectionResultComponent,\r\n  },\r\n  {\r\n    path: \"modify-floor-plan/:id\",\r\n    component: ModifyFloorPlanComponent\r\n  },\r\n  {\r\n    path: \"modify-household/:id\",\r\n    component: ModifyHouseholdComponent\r\n  },\r\n  {\r\n    path: \"modify-house-type/:id\",\r\n    component: ModifyHouseTypeComponent\r\n  },\r\n  {\r\n    path: \"standard-house-plan/:id\",\r\n    component: StandardHousePlanComponent\r\n  }, {\r\n    path: \"finaldochouse_management/:id1/:id2\",\r\n    component: FinaldochouseManagementComponent\r\n  }, {\r\n    path: \"space\",\r\n    component: SpaceComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class HouseholdRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AAEtD,SAASC,4BAA4B,QAAQ,kCAAkC;AAC/E,SAASC,8BAA8B,QAAQ,6DAA6D;AAC5G,SAASC,8BAA8B,QAAQ,6DAA6D;AAC5G,SAASC,wBAAwB,QAAQ,iDAAiD;AAC1F,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,wBAAwB,QAAQ,iDAAiD;AAC1F,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,gCAAgC,QAAQ,+DAA+D;AAChH,SAASC,cAAc,QAAQ,0BAA0B;;;AAEzD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEX;CACZ,EACD;EACEU,IAAI,EAAE,mCAAmC;EACzCC,SAAS,EAAEV;CACZ,EACD;EACES,IAAI,EAAE,mCAAmC;EACzCC,SAAS,EAAET;CACZ,EACD;EACEQ,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAER;CACZ,EACD;EACEO,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAEP;CACZ,EACD;EACEM,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAEL;CACZ,EAAE;EACDI,IAAI,EAAE,oCAAoC;EAC1CC,SAAS,EAAEJ;CACZ,EAAE;EACDG,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,sBAAsB;;;uCAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAHvBb,YAAY,CAACc,QAAQ,CAACJ,MAAM,CAAC,EAC7BV,YAAY;IAAA;EAAA;;;2EAEXa,sBAAsB;IAAAE,OAAA,GAAAC,EAAA,CAAAhB,YAAA;IAAAiB,OAAA,GAFvBjB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}