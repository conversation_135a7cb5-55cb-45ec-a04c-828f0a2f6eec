{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule, NbPopoverModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nexport let SharedModule = /*#__PURE__*/(() => {\n  class SharedModule {\n    static {\n      this.ɵfac = function SharedModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SharedModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SharedModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule, NbPopoverModule,\n        // 也可以導出常用的模組供其他地方使用\n        CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule, NbPopoverModule]\n      });\n    }\n  }\n  return SharedModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}