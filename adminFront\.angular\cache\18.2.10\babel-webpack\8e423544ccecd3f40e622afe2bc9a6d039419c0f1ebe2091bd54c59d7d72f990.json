{"ast": null, "code": "import * as preact from 'preact';\nimport { Component, createElement, isValidElement, Fragment } from 'preact';\nimport { createPortal } from 'preact/compat';\nconst styleTexts = [];\nconst styleEls = new Map();\nfunction injectStyles(styleText) {\n  styleTexts.push(styleText);\n  styleEls.forEach(styleEl => {\n    appendStylesTo(styleEl, styleText);\n  });\n}\nfunction ensureElHasStyles(el) {\n  if (el.isConnected &&\n  // sometimes true if SSR system simulates DOM\n  el.getRootNode // sometimes undefined if SSR system simulates DOM\n  ) {\n    registerStylesRoot(el.getRootNode());\n  }\n}\nfunction registerStylesRoot(rootNode) {\n  let styleEl = styleEls.get(rootNode);\n  if (!styleEl || !styleEl.isConnected) {\n    styleEl = rootNode.querySelector('style[data-fullcalendar]');\n    if (!styleEl) {\n      styleEl = document.createElement('style');\n      styleEl.setAttribute('data-fullcalendar', '');\n      const nonce = getNonceValue();\n      if (nonce) {\n        styleEl.nonce = nonce;\n      }\n      const parentEl = rootNode === document ? document.head : rootNode;\n      const insertBefore = rootNode === document ? parentEl.querySelector('script,link[rel=stylesheet],link[as=style],style') : parentEl.firstChild;\n      parentEl.insertBefore(styleEl, insertBefore);\n    }\n    styleEls.set(rootNode, styleEl);\n    hydrateStylesRoot(styleEl);\n  }\n}\nfunction hydrateStylesRoot(styleEl) {\n  for (const styleText of styleTexts) {\n    appendStylesTo(styleEl, styleText);\n  }\n}\nfunction appendStylesTo(styleEl, styleText) {\n  const {\n    sheet\n  } = styleEl;\n  const ruleCnt = sheet.cssRules.length;\n  styleText.split('}').forEach((styleStr, i) => {\n    styleStr = styleStr.trim();\n    if (styleStr) {\n      sheet.insertRule(styleStr + '}', ruleCnt + i);\n    }\n  });\n}\n// nonce\n// -------------------------------------------------------------------------------------------------\nlet queriedNonceValue;\nfunction getNonceValue() {\n  if (queriedNonceValue === undefined) {\n    queriedNonceValue = queryNonceValue();\n  }\n  return queriedNonceValue;\n}\n/*\nTODO: discourage meta tag and instead put nonce attribute on placeholder <style> tag\n*/\nfunction queryNonceValue() {\n  const metaWithNonce = document.querySelector('meta[name=\"csp-nonce\"]');\n  if (metaWithNonce && metaWithNonce.hasAttribute('content')) {\n    return metaWithNonce.getAttribute('content');\n  }\n  const elWithNonce = document.querySelector('script[nonce]');\n  if (elWithNonce) {\n    return elWithNonce.nonce || '';\n  }\n  return '';\n}\n// main\n// -------------------------------------------------------------------------------------------------\nif (typeof document !== 'undefined') {\n  registerStylesRoot(document);\n}\nvar css_248z = \":root{--fc-small-font-size:.85em;--fc-page-bg-color:#fff;--fc-neutral-bg-color:hsla(0,0%,82%,.3);--fc-neutral-text-color:grey;--fc-border-color:#ddd;--fc-button-text-color:#fff;--fc-button-bg-color:#2c3e50;--fc-button-border-color:#2c3e50;--fc-button-hover-bg-color:#1e2b37;--fc-button-hover-border-color:#1a252f;--fc-button-active-bg-color:#1a252f;--fc-button-active-border-color:#151e27;--fc-event-bg-color:#3788d8;--fc-event-border-color:#3788d8;--fc-event-text-color:#fff;--fc-event-selected-overlay-color:rgba(0,0,0,.25);--fc-more-link-bg-color:#d0d0d0;--fc-more-link-text-color:inherit;--fc-event-resizer-thickness:8px;--fc-event-resizer-dot-total-width:8px;--fc-event-resizer-dot-border-width:1px;--fc-non-business-color:hsla(0,0%,84%,.3);--fc-bg-event-color:#8fdf82;--fc-bg-event-opacity:0.3;--fc-highlight-color:rgba(188,232,241,.3);--fc-today-bg-color:rgba(255,220,40,.15);--fc-now-indicator-color:red}.fc-not-allowed,.fc-not-allowed .fc-event{cursor:not-allowed}.fc{display:flex;flex-direction:column;font-size:1em}.fc,.fc *,.fc :after,.fc :before{box-sizing:border-box}.fc table{border-collapse:collapse;border-spacing:0;font-size:1em}.fc th{text-align:center}.fc td,.fc th{padding:0;vertical-align:top}.fc a[data-navlink]{cursor:pointer}.fc a[data-navlink]:hover{text-decoration:underline}.fc-direction-ltr{direction:ltr;text-align:left}.fc-direction-rtl{direction:rtl;text-align:right}.fc-theme-standard td,.fc-theme-standard th{border:1px solid var(--fc-border-color)}.fc-liquid-hack td,.fc-liquid-hack th{position:relative}@font-face{font-family:fcicons;font-style:normal;font-weight:400;src:url(\\\"data:application/x-font-ttf;charset=utf-8;base64,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\\\") format(\\\"truetype\\\")}.fc-icon{speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:inline-block;font-family:fcicons!important;font-style:normal;font-variant:normal;font-weight:400;height:1em;line-height:1;text-align:center;text-transform:none;-moz-user-select:none;user-select:none;width:1em}.fc-icon-chevron-left:before{content:\\\"\\\\e900\\\"}.fc-icon-chevron-right:before{content:\\\"\\\\e901\\\"}.fc-icon-chevrons-left:before{content:\\\"\\\\e902\\\"}.fc-icon-chevrons-right:before{content:\\\"\\\\e903\\\"}.fc-icon-minus-square:before{content:\\\"\\\\e904\\\"}.fc-icon-plus-square:before{content:\\\"\\\\e905\\\"}.fc-icon-x:before{content:\\\"\\\\e906\\\"}.fc .fc-button{border-radius:0;font-family:inherit;font-size:inherit;line-height:inherit;margin:0;overflow:visible;text-transform:none}.fc .fc-button:focus{outline:1px dotted;outline:5px auto -webkit-focus-ring-color}.fc .fc-button{-webkit-appearance:button}.fc .fc-button:not(:disabled){cursor:pointer}.fc .fc-button{background-color:transparent;border:1px solid transparent;border-radius:.25em;display:inline-block;font-size:1em;font-weight:400;line-height:1.5;padding:.4em .65em;text-align:center;-moz-user-select:none;user-select:none;vertical-align:middle}.fc .fc-button:hover{text-decoration:none}.fc .fc-button:focus{box-shadow:0 0 0 .2rem rgba(44,62,80,.25);outline:0}.fc .fc-button:disabled{opacity:.65}.fc .fc-button-primary{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:hover{background-color:var(--fc-button-hover-bg-color);border-color:var(--fc-button-hover-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:disabled{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button-primary:not(:disabled).fc-button-active,.fc .fc-button-primary:not(:disabled):active{background-color:var(--fc-button-active-bg-color);border-color:var(--fc-button-active-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:not(:disabled).fc-button-active:focus,.fc .fc-button-primary:not(:disabled):active:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button .fc-icon{font-size:1.5em;vertical-align:middle}.fc .fc-button-group{display:inline-flex;position:relative;vertical-align:middle}.fc .fc-button-group>.fc-button{flex:1 1 auto;position:relative}.fc .fc-button-group>.fc-button.fc-button-active,.fc .fc-button-group>.fc-button:active,.fc .fc-button-group>.fc-button:focus,.fc .fc-button-group>.fc-button:hover{z-index:1}.fc-direction-ltr .fc-button-group>.fc-button:not(:first-child){border-bottom-left-radius:0;border-top-left-radius:0;margin-left:-1px}.fc-direction-ltr .fc-button-group>.fc-button:not(:last-child){border-bottom-right-radius:0;border-top-right-radius:0}.fc-direction-rtl .fc-button-group>.fc-button:not(:first-child){border-bottom-right-radius:0;border-top-right-radius:0;margin-right:-1px}.fc-direction-rtl .fc-button-group>.fc-button:not(:last-child){border-bottom-left-radius:0;border-top-left-radius:0}.fc .fc-toolbar{align-items:center;display:flex;justify-content:space-between}.fc .fc-toolbar.fc-header-toolbar{margin-bottom:1.5em}.fc .fc-toolbar.fc-footer-toolbar{margin-top:1.5em}.fc .fc-toolbar-title{font-size:1.75em;margin:0}.fc-direction-ltr .fc-toolbar>*>:not(:first-child){margin-left:.75em}.fc-direction-rtl .fc-toolbar>*>:not(:first-child){margin-right:.75em}.fc-direction-rtl .fc-toolbar-ltr{flex-direction:row-reverse}.fc .fc-scroller{-webkit-overflow-scrolling:touch;position:relative}.fc .fc-scroller-liquid{height:100%}.fc .fc-scroller-liquid-absolute{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-scroller-harness{direction:ltr;overflow:hidden;position:relative}.fc .fc-scroller-harness-liquid{height:100%}.fc-direction-rtl .fc-scroller-harness>.fc-scroller{direction:rtl}.fc-theme-standard .fc-scrollgrid{border:1px solid var(--fc-border-color)}.fc .fc-scrollgrid,.fc .fc-scrollgrid table{table-layout:fixed;width:100%}.fc .fc-scrollgrid table{border-left-style:hidden;border-right-style:hidden;border-top-style:hidden}.fc .fc-scrollgrid{border-bottom-width:0;border-collapse:separate;border-right-width:0}.fc .fc-scrollgrid-liquid{height:100%}.fc .fc-scrollgrid-section,.fc .fc-scrollgrid-section table,.fc .fc-scrollgrid-section>td{height:1px}.fc .fc-scrollgrid-section-liquid>td{height:100%}.fc .fc-scrollgrid-section>*{border-left-width:0;border-top-width:0}.fc .fc-scrollgrid-section-footer>*,.fc .fc-scrollgrid-section-header>*{border-bottom-width:0}.fc .fc-scrollgrid-section-body table,.fc .fc-scrollgrid-section-footer table{border-bottom-style:hidden}.fc .fc-scrollgrid-section-sticky>*{background:var(--fc-page-bg-color);position:sticky;z-index:3}.fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky>*{top:0}.fc .fc-scrollgrid-section-footer.fc-scrollgrid-section-sticky>*{bottom:0}.fc .fc-scrollgrid-sticky-shim{height:1px;margin-bottom:-1px}.fc-sticky{position:sticky}.fc .fc-view-harness{flex-grow:1;position:relative}.fc .fc-view-harness-active>.fc-view{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-col-header-cell-cushion{display:inline-block;padding:2px 4px}.fc .fc-bg-event,.fc .fc-highlight,.fc .fc-non-business{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-non-business{background:var(--fc-non-business-color)}.fc .fc-bg-event{background:var(--fc-bg-event-color);opacity:var(--fc-bg-event-opacity)}.fc .fc-bg-event .fc-event-title{font-size:var(--fc-small-font-size);font-style:italic;margin:.5em}.fc .fc-highlight{background:var(--fc-highlight-color)}.fc .fc-cell-shaded,.fc .fc-day-disabled{background:var(--fc-neutral-bg-color)}a.fc-event,a.fc-event:hover{text-decoration:none}.fc-event.fc-event-draggable,.fc-event[href]{cursor:pointer}.fc-event .fc-event-main{position:relative;z-index:2}.fc-event-dragging:not(.fc-event-selected){opacity:.75}.fc-event-dragging.fc-event-selected{box-shadow:0 2px 7px rgba(0,0,0,.3)}.fc-event .fc-event-resizer{display:none;position:absolute;z-index:4}.fc-event-selected .fc-event-resizer,.fc-event:hover .fc-event-resizer{display:block}.fc-event-selected .fc-event-resizer{background:var(--fc-page-bg-color);border-color:inherit;border-radius:calc(var(--fc-event-resizer-dot-total-width)/2);border-style:solid;border-width:var(--fc-event-resizer-dot-border-width);height:var(--fc-event-resizer-dot-total-width);width:var(--fc-event-resizer-dot-total-width)}.fc-event-selected .fc-event-resizer:before{bottom:-20px;content:\\\"\\\";left:-20px;position:absolute;right:-20px;top:-20px}.fc-event-selected,.fc-event:focus{box-shadow:0 2px 5px rgba(0,0,0,.2)}.fc-event-selected:before,.fc-event:focus:before{bottom:0;content:\\\"\\\";left:0;position:absolute;right:0;top:0;z-index:3}.fc-event-selected:after,.fc-event:focus:after{background:var(--fc-event-selected-overlay-color);bottom:-1px;content:\\\"\\\";left:-1px;position:absolute;right:-1px;top:-1px;z-index:1}.fc-h-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-h-event .fc-event-main{color:var(--fc-event-text-color)}.fc-h-event .fc-event-main-frame{display:flex}.fc-h-event .fc-event-time{max-width:100%;overflow:hidden}.fc-h-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-width:0}.fc-h-event .fc-event-title{display:inline-block;left:0;max-width:100%;overflow:hidden;right:0;vertical-align:top}.fc-h-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end){border-bottom-left-radius:0;border-left-width:0;border-top-left-radius:0}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start){border-bottom-right-radius:0;border-right-width:0;border-top-right-radius:0}.fc-h-event:not(.fc-event-selected) .fc-event-resizer{bottom:0;top:0;width:var(--fc-event-resizer-thickness)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end{cursor:w-resize;left:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start{cursor:e-resize;right:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-h-event.fc-event-selected .fc-event-resizer{margin-top:calc(var(--fc-event-resizer-dot-total-width)*-.5);top:50%}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end{left:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start{right:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc .fc-popover{box-shadow:0 2px 6px rgba(0,0,0,.15);position:absolute;z-index:9999}.fc .fc-popover-header{align-items:center;display:flex;flex-direction:row;justify-content:space-between;padding:3px 4px}.fc .fc-popover-title{margin:0 2px}.fc .fc-popover-close{cursor:pointer;font-size:1.1em;opacity:.65}.fc-theme-standard .fc-popover{background:var(--fc-page-bg-color);border:1px solid var(--fc-border-color)}.fc-theme-standard .fc-popover-header{background:var(--fc-neutral-bg-color)}\";\ninjectStyles(css_248z);\nclass DelayedRunner {\n  constructor(drainedOption) {\n    this.drainedOption = drainedOption;\n    this.isRunning = false;\n    this.isDirty = false;\n    this.pauseDepths = {};\n    this.timeoutId = 0;\n  }\n  request(delay) {\n    this.isDirty = true;\n    if (!this.isPaused()) {\n      this.clearTimeout();\n      if (delay == null) {\n        this.tryDrain();\n      } else {\n        this.timeoutId = setTimeout(\n        // NOT OPTIMAL! TODO: look at debounce\n        this.tryDrain.bind(this), delay);\n      }\n    }\n  }\n  pause(scope = '') {\n    let {\n      pauseDepths\n    } = this;\n    pauseDepths[scope] = (pauseDepths[scope] || 0) + 1;\n    this.clearTimeout();\n  }\n  resume(scope = '', force) {\n    let {\n      pauseDepths\n    } = this;\n    if (scope in pauseDepths) {\n      if (force) {\n        delete pauseDepths[scope];\n      } else {\n        pauseDepths[scope] -= 1;\n        let depth = pauseDepths[scope];\n        if (depth <= 0) {\n          delete pauseDepths[scope];\n        }\n      }\n      this.tryDrain();\n    }\n  }\n  isPaused() {\n    return Object.keys(this.pauseDepths).length;\n  }\n  tryDrain() {\n    if (!this.isRunning && !this.isPaused()) {\n      this.isRunning = true;\n      while (this.isDirty) {\n        this.isDirty = false;\n        this.drained(); // might set isDirty to true again\n      }\n      this.isRunning = false;\n    }\n  }\n  clear() {\n    this.clearTimeout();\n    this.isDirty = false;\n    this.pauseDepths = {};\n  }\n  clearTimeout() {\n    if (this.timeoutId) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = 0;\n    }\n  }\n  drained() {\n    if (this.drainedOption) {\n      this.drainedOption();\n    }\n  }\n}\nfunction removeElement(el) {\n  if (el.parentNode) {\n    el.parentNode.removeChild(el);\n  }\n}\n// Querying\n// ----------------------------------------------------------------------------------------------------------------\nfunction elementClosest(el, selector) {\n  if (el.closest) {\n    return el.closest(selector);\n    // really bad fallback for IE\n    // from https://developer.mozilla.org/en-US/docs/Web/API/Element/closest\n  }\n  if (!document.documentElement.contains(el)) {\n    return null;\n  }\n  do {\n    if (elementMatches(el, selector)) {\n      return el;\n    }\n    el = el.parentElement || el.parentNode;\n  } while (el !== null && el.nodeType === 1);\n  return null;\n}\nfunction elementMatches(el, selector) {\n  let method = el.matches || el.matchesSelector || el.msMatchesSelector;\n  return method.call(el, selector);\n}\n// accepts multiple subject els\n// returns a real array. good for methods like forEach\n// TODO: accept the document\nfunction findElements(container, selector) {\n  let containers = container instanceof HTMLElement ? [container] : container;\n  let allMatches = [];\n  for (let i = 0; i < containers.length; i += 1) {\n    let matches = containers[i].querySelectorAll(selector);\n    for (let j = 0; j < matches.length; j += 1) {\n      allMatches.push(matches[j]);\n    }\n  }\n  return allMatches;\n}\n// accepts multiple subject els\n// only queries direct child elements // TODO: rename to findDirectChildren!\nfunction findDirectChildren(parent, selector) {\n  let parents = parent instanceof HTMLElement ? [parent] : parent;\n  let allMatches = [];\n  for (let i = 0; i < parents.length; i += 1) {\n    let childNodes = parents[i].children; // only ever elements\n    for (let j = 0; j < childNodes.length; j += 1) {\n      let childNode = childNodes[j];\n      if (!selector || elementMatches(childNode, selector)) {\n        allMatches.push(childNode);\n      }\n    }\n  }\n  return allMatches;\n}\n// Style\n// ----------------------------------------------------------------------------------------------------------------\nconst PIXEL_PROP_RE = /(top|left|right|bottom|width|height)$/i;\nfunction applyStyle(el, props) {\n  for (let propName in props) {\n    applyStyleProp(el, propName, props[propName]);\n  }\n}\nfunction applyStyleProp(el, name, val) {\n  if (val == null) {\n    el.style[name] = '';\n  } else if (typeof val === 'number' && PIXEL_PROP_RE.test(name)) {\n    el.style[name] = `${val}px`;\n  } else {\n    el.style[name] = val;\n  }\n}\n// Event Handling\n// ----------------------------------------------------------------------------------------------------------------\n// if intercepting bubbled events at the document/window/body level,\n// and want to see originating element (the 'target'), use this util instead\n// of `ev.target` because it goes within web-component boundaries.\nfunction getEventTargetViaRoot(ev) {\n  var _a, _b;\n  return (_b = (_a = ev.composedPath) === null || _a === void 0 ? void 0 : _a.call(ev)[0]) !== null && _b !== void 0 ? _b : ev.target;\n}\n// Unique ID for DOM attribute\nlet guid$1 = 0;\nfunction getUniqueDomId() {\n  guid$1 += 1;\n  return 'fc-dom-' + guid$1;\n}\n\n// Stops a mouse/touch event from doing it's native browser action\nfunction preventDefault(ev) {\n  ev.preventDefault();\n}\n// Event Delegation\n// ----------------------------------------------------------------------------------------------------------------\nfunction buildDelegationHandler(selector, handler) {\n  return ev => {\n    let matchedChild = elementClosest(ev.target, selector);\n    if (matchedChild) {\n      handler.call(matchedChild, ev, matchedChild);\n    }\n  };\n}\nfunction listenBySelector(container, eventType, selector, handler) {\n  let attachedHandler = buildDelegationHandler(selector, handler);\n  container.addEventListener(eventType, attachedHandler);\n  return () => {\n    container.removeEventListener(eventType, attachedHandler);\n  };\n}\nfunction listenToHoverBySelector(container, selector, onMouseEnter, onMouseLeave) {\n  let currentMatchedChild;\n  return listenBySelector(container, 'mouseover', selector, (mouseOverEv, matchedChild) => {\n    if (matchedChild !== currentMatchedChild) {\n      currentMatchedChild = matchedChild;\n      onMouseEnter(mouseOverEv, matchedChild);\n      let realOnMouseLeave = mouseLeaveEv => {\n        currentMatchedChild = null;\n        onMouseLeave(mouseLeaveEv, matchedChild);\n        matchedChild.removeEventListener('mouseleave', realOnMouseLeave);\n      };\n      // listen to the next mouseleave, and then unattach\n      matchedChild.addEventListener('mouseleave', realOnMouseLeave);\n    }\n  });\n}\n// Animation\n// ----------------------------------------------------------------------------------------------------------------\nconst transitionEventNames = ['webkitTransitionEnd', 'otransitionend', 'oTransitionEnd', 'msTransitionEnd', 'transitionend'];\n// triggered only when the next single subsequent transition finishes\nfunction whenTransitionDone(el, callback) {\n  let realCallback = ev => {\n    callback(ev);\n    transitionEventNames.forEach(eventName => {\n      el.removeEventListener(eventName, realCallback);\n    });\n  };\n  transitionEventNames.forEach(eventName => {\n    el.addEventListener(eventName, realCallback); // cross-browser way to determine when the transition finishes\n  });\n}\n// ARIA workarounds\n// ----------------------------------------------------------------------------------------------------------------\nfunction createAriaClickAttrs(handler) {\n  return Object.assign({\n    onClick: handler\n  }, createAriaKeyboardAttrs(handler));\n}\nfunction createAriaKeyboardAttrs(handler) {\n  return {\n    tabIndex: 0,\n    onKeyDown(ev) {\n      if (ev.key === 'Enter' || ev.key === ' ') {\n        handler(ev);\n        ev.preventDefault(); // if space, don't scroll down page\n      }\n    }\n  };\n}\nlet guidNumber = 0;\nfunction guid() {\n  guidNumber += 1;\n  return String(guidNumber);\n}\n/* FullCalendar-specific DOM Utilities\n----------------------------------------------------------------------------------------------------------------------*/\n// Make the mouse cursor express that an event is not allowed in the current area\nfunction disableCursor() {\n  document.body.classList.add('fc-not-allowed');\n}\n// Returns the mouse cursor to its original look\nfunction enableCursor() {\n  document.body.classList.remove('fc-not-allowed');\n}\n/* Selection\n----------------------------------------------------------------------------------------------------------------------*/\nfunction preventSelection(el) {\n  el.style.userSelect = 'none';\n  el.style.webkitUserSelect = 'none';\n  el.addEventListener('selectstart', preventDefault);\n}\nfunction allowSelection(el) {\n  el.style.userSelect = '';\n  el.style.webkitUserSelect = '';\n  el.removeEventListener('selectstart', preventDefault);\n}\n/* Context Menu\n----------------------------------------------------------------------------------------------------------------------*/\nfunction preventContextMenu(el) {\n  el.addEventListener('contextmenu', preventDefault);\n}\nfunction allowContextMenu(el) {\n  el.removeEventListener('contextmenu', preventDefault);\n}\nfunction parseFieldSpecs(input) {\n  let specs = [];\n  let tokens = [];\n  let i;\n  let token;\n  if (typeof input === 'string') {\n    tokens = input.split(/\\s*,\\s*/);\n  } else if (typeof input === 'function') {\n    tokens = [input];\n  } else if (Array.isArray(input)) {\n    tokens = input;\n  }\n  for (i = 0; i < tokens.length; i += 1) {\n    token = tokens[i];\n    if (typeof token === 'string') {\n      specs.push(token.charAt(0) === '-' ? {\n        field: token.substring(1),\n        order: -1\n      } : {\n        field: token,\n        order: 1\n      });\n    } else if (typeof token === 'function') {\n      specs.push({\n        func: token\n      });\n    }\n  }\n  return specs;\n}\nfunction compareByFieldSpecs(obj0, obj1, fieldSpecs) {\n  let i;\n  let cmp;\n  for (i = 0; i < fieldSpecs.length; i += 1) {\n    cmp = compareByFieldSpec(obj0, obj1, fieldSpecs[i]);\n    if (cmp) {\n      return cmp;\n    }\n  }\n  return 0;\n}\nfunction compareByFieldSpec(obj0, obj1, fieldSpec) {\n  if (fieldSpec.func) {\n    return fieldSpec.func(obj0, obj1);\n  }\n  return flexibleCompare(obj0[fieldSpec.field], obj1[fieldSpec.field]) * (fieldSpec.order || 1);\n}\nfunction flexibleCompare(a, b) {\n  if (!a && !b) {\n    return 0;\n  }\n  if (b == null) {\n    return -1;\n  }\n  if (a == null) {\n    return 1;\n  }\n  if (typeof a === 'string' || typeof b === 'string') {\n    return String(a).localeCompare(String(b));\n  }\n  return a - b;\n}\n/* String Utilities\n----------------------------------------------------------------------------------------------------------------------*/\nfunction padStart(val, len) {\n  let s = String(val);\n  return '000'.substr(0, len - s.length) + s;\n}\nfunction formatWithOrdinals(formatter, args, fallbackText) {\n  if (typeof formatter === 'function') {\n    return formatter(...args);\n  }\n  if (typeof formatter === 'string') {\n    // non-blank string\n    return args.reduce((str, arg, index) => str.replace('$' + index, arg || ''), formatter);\n  }\n  return fallbackText;\n}\n/* Number Utilities\n----------------------------------------------------------------------------------------------------------------------*/\nfunction compareNumbers(a, b) {\n  return a - b;\n}\nfunction isInt(n) {\n  return n % 1 === 0;\n}\n/* FC-specific DOM dimension stuff\n----------------------------------------------------------------------------------------------------------------------*/\nfunction computeSmallestCellWidth(cellEl) {\n  let allWidthEl = cellEl.querySelector('.fc-scrollgrid-shrink-frame');\n  let contentWidthEl = cellEl.querySelector('.fc-scrollgrid-shrink-cushion');\n  if (!allWidthEl) {\n    throw new Error('needs fc-scrollgrid-shrink-frame className'); // TODO: use const\n  }\n  if (!contentWidthEl) {\n    throw new Error('needs fc-scrollgrid-shrink-cushion className');\n  }\n  return cellEl.getBoundingClientRect().width - allWidthEl.getBoundingClientRect().width +\n  // the cell padding+border\n  contentWidthEl.getBoundingClientRect().width;\n}\nconst INTERNAL_UNITS = ['years', 'months', 'days', 'milliseconds'];\nconst PARSE_RE = /^(-?)(?:(\\d+)\\.)?(\\d+):(\\d\\d)(?::(\\d\\d)(?:\\.(\\d\\d\\d))?)?/;\n// Parsing and Creation\nfunction createDuration(input, unit) {\n  if (typeof input === 'string') {\n    return parseString(input);\n  }\n  if (typeof input === 'object' && input) {\n    // non-null object\n    return parseObject(input);\n  }\n  if (typeof input === 'number') {\n    return parseObject({\n      [unit || 'milliseconds']: input\n    });\n  }\n  return null;\n}\nfunction parseString(s) {\n  let m = PARSE_RE.exec(s);\n  if (m) {\n    let sign = m[1] ? -1 : 1;\n    return {\n      years: 0,\n      months: 0,\n      days: sign * (m[2] ? parseInt(m[2], 10) : 0),\n      milliseconds: sign * ((m[3] ? parseInt(m[3], 10) : 0) * 60 * 60 * 1000 +\n      // hours\n      (m[4] ? parseInt(m[4], 10) : 0) * 60 * 1000 +\n      // minutes\n      (m[5] ? parseInt(m[5], 10) : 0) * 1000 + (\n      // seconds\n      m[6] ? parseInt(m[6], 10) : 0) // ms\n      )\n    };\n  }\n  return null;\n}\nfunction parseObject(obj) {\n  let duration = {\n    years: obj.years || obj.year || 0,\n    months: obj.months || obj.month || 0,\n    days: obj.days || obj.day || 0,\n    milliseconds: (obj.hours || obj.hour || 0) * 60 * 60 * 1000 +\n    // hours\n    (obj.minutes || obj.minute || 0) * 60 * 1000 +\n    // minutes\n    (obj.seconds || obj.second || 0) * 1000 + (\n    // seconds\n    obj.milliseconds || obj.millisecond || obj.ms || 0) // ms\n  };\n  let weeks = obj.weeks || obj.week;\n  if (weeks) {\n    duration.days += weeks * 7;\n    duration.specifiedWeeks = true;\n  }\n  return duration;\n}\n// Equality\nfunction durationsEqual(d0, d1) {\n  return d0.years === d1.years && d0.months === d1.months && d0.days === d1.days && d0.milliseconds === d1.milliseconds;\n}\nfunction asCleanDays(dur) {\n  if (!dur.years && !dur.months && !dur.milliseconds) {\n    return dur.days;\n  }\n  return 0;\n}\n// Simple Math\nfunction addDurations(d0, d1) {\n  return {\n    years: d0.years + d1.years,\n    months: d0.months + d1.months,\n    days: d0.days + d1.days,\n    milliseconds: d0.milliseconds + d1.milliseconds\n  };\n}\nfunction subtractDurations(d1, d0) {\n  return {\n    years: d1.years - d0.years,\n    months: d1.months - d0.months,\n    days: d1.days - d0.days,\n    milliseconds: d1.milliseconds - d0.milliseconds\n  };\n}\nfunction multiplyDuration(d, n) {\n  return {\n    years: d.years * n,\n    months: d.months * n,\n    days: d.days * n,\n    milliseconds: d.milliseconds * n\n  };\n}\n// Conversions\n// \"Rough\" because they are based on average-case Gregorian months/years\nfunction asRoughYears(dur) {\n  return asRoughDays(dur) / 365;\n}\nfunction asRoughMonths(dur) {\n  return asRoughDays(dur) / 30;\n}\nfunction asRoughDays(dur) {\n  return asRoughMs(dur) / 864e5;\n}\nfunction asRoughMinutes(dur) {\n  return asRoughMs(dur) / (1000 * 60);\n}\nfunction asRoughSeconds(dur) {\n  return asRoughMs(dur) / 1000;\n}\nfunction asRoughMs(dur) {\n  return dur.years * (365 * 864e5) + dur.months * (30 * 864e5) + dur.days * 864e5 + dur.milliseconds;\n}\n// Advanced Math\nfunction wholeDivideDurations(numerator, denominator) {\n  let res = null;\n  for (let i = 0; i < INTERNAL_UNITS.length; i += 1) {\n    let unit = INTERNAL_UNITS[i];\n    if (denominator[unit]) {\n      let localRes = numerator[unit] / denominator[unit];\n      if (!isInt(localRes) || res !== null && res !== localRes) {\n        return null;\n      }\n      res = localRes;\n    } else if (numerator[unit]) {\n      // needs to divide by something but can't!\n      return null;\n    }\n  }\n  return res;\n}\nfunction greatestDurationDenominator(dur) {\n  let ms = dur.milliseconds;\n  if (ms) {\n    if (ms % 1000 !== 0) {\n      return {\n        unit: 'millisecond',\n        value: ms\n      };\n    }\n    if (ms % (1000 * 60) !== 0) {\n      return {\n        unit: 'second',\n        value: ms / 1000\n      };\n    }\n    if (ms % (1000 * 60 * 60) !== 0) {\n      return {\n        unit: 'minute',\n        value: ms / (1000 * 60)\n      };\n    }\n    if (ms) {\n      return {\n        unit: 'hour',\n        value: ms / (1000 * 60 * 60)\n      };\n    }\n  }\n  if (dur.days) {\n    if (dur.specifiedWeeks && dur.days % 7 === 0) {\n      return {\n        unit: 'week',\n        value: dur.days / 7\n      };\n    }\n    return {\n      unit: 'day',\n      value: dur.days\n    };\n  }\n  if (dur.months) {\n    return {\n      unit: 'month',\n      value: dur.months\n    };\n  }\n  if (dur.years) {\n    return {\n      unit: 'year',\n      value: dur.years\n    };\n  }\n  return {\n    unit: 'millisecond',\n    value: 0\n  };\n}\n\n// TODO: new util arrayify?\nfunction removeExact(array, exactVal) {\n  let removeCnt = 0;\n  let i = 0;\n  while (i < array.length) {\n    if (array[i] === exactVal) {\n      array.splice(i, 1);\n      removeCnt += 1;\n    } else {\n      i += 1;\n    }\n  }\n  return removeCnt;\n}\nfunction isArraysEqual(a0, a1, equalityFunc) {\n  if (a0 === a1) {\n    return true;\n  }\n  let len = a0.length;\n  let i;\n  if (len !== a1.length) {\n    // not array? or not same length?\n    return false;\n  }\n  for (i = 0; i < len; i += 1) {\n    if (!(equalityFunc ? equalityFunc(a0[i], a1[i]) : a0[i] === a1[i])) {\n      return false;\n    }\n  }\n  return true;\n}\nconst DAY_IDS = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];\n// Adding\nfunction addWeeks(m, n) {\n  let a = dateToUtcArray(m);\n  a[2] += n * 7;\n  return arrayToUtcDate(a);\n}\nfunction addDays(m, n) {\n  let a = dateToUtcArray(m);\n  a[2] += n;\n  return arrayToUtcDate(a);\n}\nfunction addMs(m, n) {\n  let a = dateToUtcArray(m);\n  a[6] += n;\n  return arrayToUtcDate(a);\n}\n// Diffing (all return floats)\n// TODO: why not use ranges?\nfunction diffWeeks(m0, m1) {\n  return diffDays(m0, m1) / 7;\n}\nfunction diffDays(m0, m1) {\n  return (m1.valueOf() - m0.valueOf()) / (1000 * 60 * 60 * 24);\n}\nfunction diffHours(m0, m1) {\n  return (m1.valueOf() - m0.valueOf()) / (1000 * 60 * 60);\n}\nfunction diffMinutes(m0, m1) {\n  return (m1.valueOf() - m0.valueOf()) / (1000 * 60);\n}\nfunction diffSeconds(m0, m1) {\n  return (m1.valueOf() - m0.valueOf()) / 1000;\n}\nfunction diffDayAndTime(m0, m1) {\n  let m0day = startOfDay(m0);\n  let m1day = startOfDay(m1);\n  return {\n    years: 0,\n    months: 0,\n    days: Math.round(diffDays(m0day, m1day)),\n    milliseconds: m1.valueOf() - m1day.valueOf() - (m0.valueOf() - m0day.valueOf())\n  };\n}\n// Diffing Whole Units\nfunction diffWholeWeeks(m0, m1) {\n  let d = diffWholeDays(m0, m1);\n  if (d !== null && d % 7 === 0) {\n    return d / 7;\n  }\n  return null;\n}\nfunction diffWholeDays(m0, m1) {\n  if (timeAsMs(m0) === timeAsMs(m1)) {\n    return Math.round(diffDays(m0, m1));\n  }\n  return null;\n}\n// Start-Of\nfunction startOfDay(m) {\n  return arrayToUtcDate([m.getUTCFullYear(), m.getUTCMonth(), m.getUTCDate()]);\n}\nfunction startOfHour(m) {\n  return arrayToUtcDate([m.getUTCFullYear(), m.getUTCMonth(), m.getUTCDate(), m.getUTCHours()]);\n}\nfunction startOfMinute(m) {\n  return arrayToUtcDate([m.getUTCFullYear(), m.getUTCMonth(), m.getUTCDate(), m.getUTCHours(), m.getUTCMinutes()]);\n}\nfunction startOfSecond(m) {\n  return arrayToUtcDate([m.getUTCFullYear(), m.getUTCMonth(), m.getUTCDate(), m.getUTCHours(), m.getUTCMinutes(), m.getUTCSeconds()]);\n}\n// Week Computation\nfunction weekOfYear(marker, dow, doy) {\n  let y = marker.getUTCFullYear();\n  let w = weekOfGivenYear(marker, y, dow, doy);\n  if (w < 1) {\n    return weekOfGivenYear(marker, y - 1, dow, doy);\n  }\n  let nextW = weekOfGivenYear(marker, y + 1, dow, doy);\n  if (nextW >= 1) {\n    return Math.min(w, nextW);\n  }\n  return w;\n}\nfunction weekOfGivenYear(marker, year, dow, doy) {\n  let firstWeekStart = arrayToUtcDate([year, 0, 1 + firstWeekOffset(year, dow, doy)]);\n  let dayStart = startOfDay(marker);\n  let days = Math.round(diffDays(firstWeekStart, dayStart));\n  return Math.floor(days / 7) + 1; // zero-indexed\n}\n// start-of-first-week - start-of-year\nfunction firstWeekOffset(year, dow, doy) {\n  // first-week day -- which january is always in the first week (4 for iso, 1 for other)\n  let fwd = 7 + dow - doy;\n  // first-week day local weekday -- which local weekday is fwd\n  let fwdlw = (7 + arrayToUtcDate([year, 0, fwd]).getUTCDay() - dow) % 7;\n  return -fwdlw + fwd - 1;\n}\n// Array Conversion\nfunction dateToLocalArray(date) {\n  return [date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()];\n}\nfunction arrayToLocalDate(a) {\n  return new Date(a[0], a[1] || 0, a[2] == null ? 1 : a[2],\n  // day of month\n  a[3] || 0, a[4] || 0, a[5] || 0);\n}\nfunction dateToUtcArray(date) {\n  return [date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds(), date.getUTCMilliseconds()];\n}\nfunction arrayToUtcDate(a) {\n  // according to web standards (and Safari), a month index is required.\n  // massage if only given a year.\n  if (a.length === 1) {\n    a = a.concat([0]);\n  }\n  return new Date(Date.UTC(...a));\n}\n// Other Utils\nfunction isValidDate(m) {\n  return !isNaN(m.valueOf());\n}\nfunction timeAsMs(m) {\n  return m.getUTCHours() * 1000 * 60 * 60 + m.getUTCMinutes() * 1000 * 60 + m.getUTCSeconds() * 1000 + m.getUTCMilliseconds();\n}\n\n// timeZoneOffset is in minutes\nfunction buildIsoString(marker, timeZoneOffset, stripZeroTime = false) {\n  let s = marker.toISOString();\n  s = s.replace('.000', '');\n  if (stripZeroTime) {\n    s = s.replace('T00:00:00Z', '');\n  }\n  if (s.length > 10) {\n    // time part wasn't stripped, can add timezone info\n    if (timeZoneOffset == null) {\n      s = s.replace('Z', '');\n    } else if (timeZoneOffset !== 0) {\n      s = s.replace('Z', formatTimeZoneOffset(timeZoneOffset, true));\n    }\n    // otherwise, its UTC-0 and we want to keep the Z\n  }\n  return s;\n}\n// formats the date, but with no time part\n// TODO: somehow merge with buildIsoString and stripZeroTime\n// TODO: rename. omit \"string\"\nfunction formatDayString(marker) {\n  return marker.toISOString().replace(/T.*$/, '');\n}\nfunction formatIsoMonthStr(marker) {\n  return marker.toISOString().match(/^\\d{4}-\\d{2}/)[0];\n}\n// TODO: use Date::toISOString and use everything after the T?\nfunction formatIsoTimeString(marker) {\n  return padStart(marker.getUTCHours(), 2) + ':' + padStart(marker.getUTCMinutes(), 2) + ':' + padStart(marker.getUTCSeconds(), 2);\n}\nfunction formatTimeZoneOffset(minutes, doIso = false) {\n  let sign = minutes < 0 ? '-' : '+';\n  let abs = Math.abs(minutes);\n  let hours = Math.floor(abs / 60);\n  let mins = Math.round(abs % 60);\n  if (doIso) {\n    return `${sign + padStart(hours, 2)}:${padStart(mins, 2)}`;\n  }\n  return `GMT${sign}${hours}${mins ? `:${padStart(mins, 2)}` : ''}`;\n}\nfunction memoize(workerFunc, resEquality, teardownFunc) {\n  let currentArgs;\n  let currentRes;\n  return function (...newArgs) {\n    if (!currentArgs) {\n      currentRes = workerFunc.apply(this, newArgs);\n    } else if (!isArraysEqual(currentArgs, newArgs)) {\n      if (teardownFunc) {\n        teardownFunc(currentRes);\n      }\n      let res = workerFunc.apply(this, newArgs);\n      if (!resEquality || !resEquality(res, currentRes)) {\n        currentRes = res;\n      }\n    }\n    currentArgs = newArgs;\n    return currentRes;\n  };\n}\nfunction memoizeObjArg(workerFunc, resEquality, teardownFunc) {\n  let currentArg;\n  let currentRes;\n  return newArg => {\n    if (!currentArg) {\n      currentRes = workerFunc.call(this, newArg);\n    } else if (!isPropsEqual(currentArg, newArg)) {\n      if (teardownFunc) {\n        teardownFunc(currentRes);\n      }\n      let res = workerFunc.call(this, newArg);\n      if (!resEquality || !resEquality(res, currentRes)) {\n        currentRes = res;\n      }\n    }\n    currentArg = newArg;\n    return currentRes;\n  };\n}\nfunction memoizeArraylike(\n// used at all?\nworkerFunc, resEquality, teardownFunc) {\n  let currentArgSets = [];\n  let currentResults = [];\n  return newArgSets => {\n    let currentLen = currentArgSets.length;\n    let newLen = newArgSets.length;\n    let i = 0;\n    for (; i < currentLen; i += 1) {\n      if (!newArgSets[i]) {\n        // one of the old sets no longer exists\n        if (teardownFunc) {\n          teardownFunc(currentResults[i]);\n        }\n      } else if (!isArraysEqual(currentArgSets[i], newArgSets[i])) {\n        if (teardownFunc) {\n          teardownFunc(currentResults[i]);\n        }\n        let res = workerFunc.apply(this, newArgSets[i]);\n        if (!resEquality || !resEquality(res, currentResults[i])) {\n          currentResults[i] = res;\n        }\n      }\n    }\n    for (; i < newLen; i += 1) {\n      currentResults[i] = workerFunc.apply(this, newArgSets[i]);\n    }\n    currentArgSets = newArgSets;\n    currentResults.splice(newLen); // remove excess\n    return currentResults;\n  };\n}\nfunction memoizeHashlike(workerFunc, resEquality, teardownFunc) {\n  let currentArgHash = {};\n  let currentResHash = {};\n  return newArgHash => {\n    let newResHash = {};\n    for (let key in newArgHash) {\n      if (!currentResHash[key]) {\n        newResHash[key] = workerFunc.apply(this, newArgHash[key]);\n      } else if (!isArraysEqual(currentArgHash[key], newArgHash[key])) {\n        if (teardownFunc) {\n          teardownFunc(currentResHash[key]);\n        }\n        let res = workerFunc.apply(this, newArgHash[key]);\n        newResHash[key] = resEquality && resEquality(res, currentResHash[key]) ? currentResHash[key] : res;\n      } else {\n        newResHash[key] = currentResHash[key];\n      }\n    }\n    currentArgHash = newArgHash;\n    currentResHash = newResHash;\n    return newResHash;\n  };\n}\nconst EXTENDED_SETTINGS_AND_SEVERITIES = {\n  week: 3,\n  separator: 0,\n  omitZeroMinute: 0,\n  meridiem: 0,\n  omitCommas: 0\n};\nconst STANDARD_DATE_PROP_SEVERITIES = {\n  timeZoneName: 7,\n  era: 6,\n  year: 5,\n  month: 4,\n  day: 2,\n  weekday: 2,\n  hour: 1,\n  minute: 1,\n  second: 1\n};\nconst MERIDIEM_RE = /\\s*([ap])\\.?m\\.?/i; // eats up leading spaces too\nconst COMMA_RE = /,/g; // we need re for globalness\nconst MULTI_SPACE_RE = /\\s+/g;\nconst LTR_RE = /\\u200e/g; // control character\nconst UTC_RE = /UTC|GMT/;\nclass NativeFormatter {\n  constructor(formatSettings) {\n    let standardDateProps = {};\n    let extendedSettings = {};\n    let severity = 0;\n    for (let name in formatSettings) {\n      if (name in EXTENDED_SETTINGS_AND_SEVERITIES) {\n        extendedSettings[name] = formatSettings[name];\n        severity = Math.max(EXTENDED_SETTINGS_AND_SEVERITIES[name], severity);\n      } else {\n        standardDateProps[name] = formatSettings[name];\n        if (name in STANDARD_DATE_PROP_SEVERITIES) {\n          // TODO: what about hour12? no severity\n          severity = Math.max(STANDARD_DATE_PROP_SEVERITIES[name], severity);\n        }\n      }\n    }\n    this.standardDateProps = standardDateProps;\n    this.extendedSettings = extendedSettings;\n    this.severity = severity;\n    this.buildFormattingFunc = memoize(buildFormattingFunc);\n  }\n  format(date, context) {\n    return this.buildFormattingFunc(this.standardDateProps, this.extendedSettings, context)(date);\n  }\n  formatRange(start, end, context, betterDefaultSeparator) {\n    let {\n      standardDateProps,\n      extendedSettings\n    } = this;\n    let diffSeverity = computeMarkerDiffSeverity(start.marker, end.marker, context.calendarSystem);\n    if (!diffSeverity) {\n      return this.format(start, context);\n    }\n    let biggestUnitForPartial = diffSeverity;\n    if (biggestUnitForPartial > 1 && (\n    // the two dates are different in a way that's larger scale than time\n    standardDateProps.year === 'numeric' || standardDateProps.year === '2-digit') && (standardDateProps.month === 'numeric' || standardDateProps.month === '2-digit') && (standardDateProps.day === 'numeric' || standardDateProps.day === '2-digit')) {\n      biggestUnitForPartial = 1; // make it look like the dates are only different in terms of time\n    }\n    let full0 = this.format(start, context);\n    let full1 = this.format(end, context);\n    if (full0 === full1) {\n      return full0;\n    }\n    let partialDateProps = computePartialFormattingOptions(standardDateProps, biggestUnitForPartial);\n    let partialFormattingFunc = buildFormattingFunc(partialDateProps, extendedSettings, context);\n    let partial0 = partialFormattingFunc(start);\n    let partial1 = partialFormattingFunc(end);\n    let insertion = findCommonInsertion(full0, partial0, full1, partial1);\n    let separator = extendedSettings.separator || betterDefaultSeparator || context.defaultSeparator || '';\n    if (insertion) {\n      return insertion.before + partial0 + separator + partial1 + insertion.after;\n    }\n    return full0 + separator + full1;\n  }\n  getLargestUnit() {\n    switch (this.severity) {\n      case 7:\n      case 6:\n      case 5:\n        return 'year';\n      case 4:\n        return 'month';\n      case 3:\n        return 'week';\n      case 2:\n        return 'day';\n      default:\n        return 'time';\n      // really?\n    }\n  }\n}\nfunction buildFormattingFunc(standardDateProps, extendedSettings, context) {\n  let standardDatePropCnt = Object.keys(standardDateProps).length;\n  if (standardDatePropCnt === 1 && standardDateProps.timeZoneName === 'short') {\n    return date => formatTimeZoneOffset(date.timeZoneOffset);\n  }\n  if (standardDatePropCnt === 0 && extendedSettings.week) {\n    return date => formatWeekNumber(context.computeWeekNumber(date.marker), context.weekText, context.weekTextLong, context.locale, extendedSettings.week);\n  }\n  return buildNativeFormattingFunc(standardDateProps, extendedSettings, context);\n}\nfunction buildNativeFormattingFunc(standardDateProps, extendedSettings, context) {\n  standardDateProps = Object.assign({}, standardDateProps); // copy\n  extendedSettings = Object.assign({}, extendedSettings); // copy\n  sanitizeSettings(standardDateProps, extendedSettings);\n  standardDateProps.timeZone = 'UTC'; // we leverage the only guaranteed timeZone for our UTC markers\n  let normalFormat = new Intl.DateTimeFormat(context.locale.codes, standardDateProps);\n  let zeroFormat; // needed?\n  if (extendedSettings.omitZeroMinute) {\n    let zeroProps = Object.assign({}, standardDateProps);\n    delete zeroProps.minute; // seconds and ms were already considered in sanitizeSettings\n    zeroFormat = new Intl.DateTimeFormat(context.locale.codes, zeroProps);\n  }\n  return date => {\n    let {\n      marker\n    } = date;\n    let format;\n    if (zeroFormat && !marker.getUTCMinutes()) {\n      format = zeroFormat;\n    } else {\n      format = normalFormat;\n    }\n    let s = format.format(marker);\n    return postProcess(s, date, standardDateProps, extendedSettings, context);\n  };\n}\nfunction sanitizeSettings(standardDateProps, extendedSettings) {\n  // deal with a browser inconsistency where formatting the timezone\n  // requires that the hour/minute be present.\n  if (standardDateProps.timeZoneName) {\n    if (!standardDateProps.hour) {\n      standardDateProps.hour = '2-digit';\n    }\n    if (!standardDateProps.minute) {\n      standardDateProps.minute = '2-digit';\n    }\n  }\n  // only support short timezone names\n  if (standardDateProps.timeZoneName === 'long') {\n    standardDateProps.timeZoneName = 'short';\n  }\n  // if requesting to display seconds, MUST display minutes\n  if (extendedSettings.omitZeroMinute && (standardDateProps.second || standardDateProps.millisecond)) {\n    delete extendedSettings.omitZeroMinute;\n  }\n}\nfunction postProcess(s, date, standardDateProps, extendedSettings, context) {\n  s = s.replace(LTR_RE, ''); // remove left-to-right control chars. do first. good for other regexes\n  if (standardDateProps.timeZoneName === 'short') {\n    s = injectTzoStr(s, context.timeZone === 'UTC' || date.timeZoneOffset == null ? 'UTC' :\n    // important to normalize for IE, which does \"GMT\"\n    formatTimeZoneOffset(date.timeZoneOffset));\n  }\n  if (extendedSettings.omitCommas) {\n    s = s.replace(COMMA_RE, '').trim();\n  }\n  if (extendedSettings.omitZeroMinute) {\n    s = s.replace(':00', ''); // zeroFormat doesn't always achieve this\n  }\n  // ^ do anything that might create adjacent spaces before this point,\n  // because MERIDIEM_RE likes to eat up loading spaces\n  if (extendedSettings.meridiem === false) {\n    s = s.replace(MERIDIEM_RE, '').trim();\n  } else if (extendedSettings.meridiem === 'narrow') {\n    // a/p\n    s = s.replace(MERIDIEM_RE, (m0, m1) => m1.toLocaleLowerCase());\n  } else if (extendedSettings.meridiem === 'short') {\n    // am/pm\n    s = s.replace(MERIDIEM_RE, (m0, m1) => `${m1.toLocaleLowerCase()}m`);\n  } else if (extendedSettings.meridiem === 'lowercase') {\n    // other meridiem transformers already converted to lowercase\n    s = s.replace(MERIDIEM_RE, m0 => m0.toLocaleLowerCase());\n  }\n  s = s.replace(MULTI_SPACE_RE, ' ');\n  s = s.trim();\n  return s;\n}\nfunction injectTzoStr(s, tzoStr) {\n  let replaced = false;\n  s = s.replace(UTC_RE, () => {\n    replaced = true;\n    return tzoStr;\n  });\n  // IE11 doesn't include UTC/GMT in the original string, so append to end\n  if (!replaced) {\n    s += ` ${tzoStr}`;\n  }\n  return s;\n}\nfunction formatWeekNumber(num, weekText, weekTextLong, locale, display) {\n  let parts = [];\n  if (display === 'long') {\n    parts.push(weekTextLong);\n  } else if (display === 'short' || display === 'narrow') {\n    parts.push(weekText);\n  }\n  if (display === 'long' || display === 'short') {\n    parts.push(' ');\n  }\n  parts.push(locale.simpleNumberFormat.format(num));\n  if (locale.options.direction === 'rtl') {\n    // TODO: use control characters instead?\n    parts.reverse();\n  }\n  return parts.join('');\n}\n// Range Formatting Utils\n// 0 = exactly the same\n// 1 = different by time\n// and bigger\nfunction computeMarkerDiffSeverity(d0, d1, ca) {\n  if (ca.getMarkerYear(d0) !== ca.getMarkerYear(d1)) {\n    return 5;\n  }\n  if (ca.getMarkerMonth(d0) !== ca.getMarkerMonth(d1)) {\n    return 4;\n  }\n  if (ca.getMarkerDay(d0) !== ca.getMarkerDay(d1)) {\n    return 2;\n  }\n  if (timeAsMs(d0) !== timeAsMs(d1)) {\n    return 1;\n  }\n  return 0;\n}\nfunction computePartialFormattingOptions(options, biggestUnit) {\n  let partialOptions = {};\n  for (let name in options) {\n    if (!(name in STANDARD_DATE_PROP_SEVERITIES) ||\n    // not a date part prop (like timeZone)\n    STANDARD_DATE_PROP_SEVERITIES[name] <= biggestUnit) {\n      partialOptions[name] = options[name];\n    }\n  }\n  return partialOptions;\n}\nfunction findCommonInsertion(full0, partial0, full1, partial1) {\n  let i0 = 0;\n  while (i0 < full0.length) {\n    let found0 = full0.indexOf(partial0, i0);\n    if (found0 === -1) {\n      break;\n    }\n    let before0 = full0.substr(0, found0);\n    i0 = found0 + partial0.length;\n    let after0 = full0.substr(i0);\n    let i1 = 0;\n    while (i1 < full1.length) {\n      let found1 = full1.indexOf(partial1, i1);\n      if (found1 === -1) {\n        break;\n      }\n      let before1 = full1.substr(0, found1);\n      i1 = found1 + partial1.length;\n      let after1 = full1.substr(i1);\n      if (before0 === before1 && after0 === after1) {\n        return {\n          before: before0,\n          after: after0\n        };\n      }\n    }\n  }\n  return null;\n}\nfunction expandZonedMarker(dateInfo, calendarSystem) {\n  let a = calendarSystem.markerToArray(dateInfo.marker);\n  return {\n    marker: dateInfo.marker,\n    timeZoneOffset: dateInfo.timeZoneOffset,\n    array: a,\n    year: a[0],\n    month: a[1],\n    day: a[2],\n    hour: a[3],\n    minute: a[4],\n    second: a[5],\n    millisecond: a[6]\n  };\n}\nfunction createVerboseFormattingArg(start, end, context, betterDefaultSeparator) {\n  let startInfo = expandZonedMarker(start, context.calendarSystem);\n  let endInfo = end ? expandZonedMarker(end, context.calendarSystem) : null;\n  return {\n    date: startInfo,\n    start: startInfo,\n    end: endInfo,\n    timeZone: context.timeZone,\n    localeCodes: context.locale.codes,\n    defaultSeparator: betterDefaultSeparator || context.defaultSeparator\n  };\n}\n\n/*\nTODO: fix the terminology of \"formatter\" vs \"formatting func\"\n*/\n/*\nAt the time of instantiation, this object does not know which cmd-formatting system it will use.\nIt receives this at the time of formatting, as a setting.\n*/\nclass CmdFormatter {\n  constructor(cmdStr) {\n    this.cmdStr = cmdStr;\n  }\n  format(date, context, betterDefaultSeparator) {\n    return context.cmdFormatter(this.cmdStr, createVerboseFormattingArg(date, null, context, betterDefaultSeparator));\n  }\n  formatRange(start, end, context, betterDefaultSeparator) {\n    return context.cmdFormatter(this.cmdStr, createVerboseFormattingArg(start, end, context, betterDefaultSeparator));\n  }\n}\nclass FuncFormatter {\n  constructor(func) {\n    this.func = func;\n  }\n  format(date, context, betterDefaultSeparator) {\n    return this.func(createVerboseFormattingArg(date, null, context, betterDefaultSeparator));\n  }\n  formatRange(start, end, context, betterDefaultSeparator) {\n    return this.func(createVerboseFormattingArg(start, end, context, betterDefaultSeparator));\n  }\n}\nfunction createFormatter(input) {\n  if (typeof input === 'object' && input) {\n    // non-null object\n    return new NativeFormatter(input);\n  }\n  if (typeof input === 'string') {\n    return new CmdFormatter(input);\n  }\n  if (typeof input === 'function') {\n    return new FuncFormatter(input);\n  }\n  return null;\n}\n\n// base options\n// ------------\nconst BASE_OPTION_REFINERS = {\n  navLinkDayClick: identity,\n  navLinkWeekClick: identity,\n  duration: createDuration,\n  bootstrapFontAwesome: identity,\n  buttonIcons: identity,\n  customButtons: identity,\n  defaultAllDayEventDuration: createDuration,\n  defaultTimedEventDuration: createDuration,\n  nextDayThreshold: createDuration,\n  scrollTime: createDuration,\n  scrollTimeReset: Boolean,\n  slotMinTime: createDuration,\n  slotMaxTime: createDuration,\n  dayPopoverFormat: createFormatter,\n  slotDuration: createDuration,\n  snapDuration: createDuration,\n  headerToolbar: identity,\n  footerToolbar: identity,\n  defaultRangeSeparator: String,\n  titleRangeSeparator: String,\n  forceEventDuration: Boolean,\n  dayHeaders: Boolean,\n  dayHeaderFormat: createFormatter,\n  dayHeaderClassNames: identity,\n  dayHeaderContent: identity,\n  dayHeaderDidMount: identity,\n  dayHeaderWillUnmount: identity,\n  dayCellClassNames: identity,\n  dayCellContent: identity,\n  dayCellDidMount: identity,\n  dayCellWillUnmount: identity,\n  initialView: String,\n  aspectRatio: Number,\n  weekends: Boolean,\n  weekNumberCalculation: identity,\n  weekNumbers: Boolean,\n  weekNumberClassNames: identity,\n  weekNumberContent: identity,\n  weekNumberDidMount: identity,\n  weekNumberWillUnmount: identity,\n  editable: Boolean,\n  viewClassNames: identity,\n  viewDidMount: identity,\n  viewWillUnmount: identity,\n  nowIndicator: Boolean,\n  nowIndicatorClassNames: identity,\n  nowIndicatorContent: identity,\n  nowIndicatorDidMount: identity,\n  nowIndicatorWillUnmount: identity,\n  showNonCurrentDates: Boolean,\n  lazyFetching: Boolean,\n  startParam: String,\n  endParam: String,\n  timeZoneParam: String,\n  timeZone: String,\n  locales: identity,\n  locale: identity,\n  themeSystem: String,\n  dragRevertDuration: Number,\n  dragScroll: Boolean,\n  allDayMaintainDuration: Boolean,\n  unselectAuto: Boolean,\n  dropAccept: identity,\n  eventOrder: parseFieldSpecs,\n  eventOrderStrict: Boolean,\n  handleWindowResize: Boolean,\n  windowResizeDelay: Number,\n  longPressDelay: Number,\n  eventDragMinDistance: Number,\n  expandRows: Boolean,\n  height: identity,\n  contentHeight: identity,\n  direction: String,\n  weekNumberFormat: createFormatter,\n  eventResizableFromStart: Boolean,\n  displayEventTime: Boolean,\n  displayEventEnd: Boolean,\n  weekText: String,\n  weekTextLong: String,\n  progressiveEventRendering: Boolean,\n  businessHours: identity,\n  initialDate: identity,\n  now: identity,\n  eventDataTransform: identity,\n  stickyHeaderDates: identity,\n  stickyFooterScrollbar: identity,\n  viewHeight: identity,\n  defaultAllDay: Boolean,\n  eventSourceFailure: identity,\n  eventSourceSuccess: identity,\n  eventDisplay: String,\n  eventStartEditable: Boolean,\n  eventDurationEditable: Boolean,\n  eventOverlap: identity,\n  eventConstraint: identity,\n  eventAllow: identity,\n  eventBackgroundColor: String,\n  eventBorderColor: String,\n  eventTextColor: String,\n  eventColor: String,\n  eventClassNames: identity,\n  eventContent: identity,\n  eventDidMount: identity,\n  eventWillUnmount: identity,\n  selectConstraint: identity,\n  selectOverlap: identity,\n  selectAllow: identity,\n  droppable: Boolean,\n  unselectCancel: String,\n  slotLabelFormat: identity,\n  slotLaneClassNames: identity,\n  slotLaneContent: identity,\n  slotLaneDidMount: identity,\n  slotLaneWillUnmount: identity,\n  slotLabelClassNames: identity,\n  slotLabelContent: identity,\n  slotLabelDidMount: identity,\n  slotLabelWillUnmount: identity,\n  dayMaxEvents: identity,\n  dayMaxEventRows: identity,\n  dayMinWidth: Number,\n  slotLabelInterval: createDuration,\n  allDayText: String,\n  allDayClassNames: identity,\n  allDayContent: identity,\n  allDayDidMount: identity,\n  allDayWillUnmount: identity,\n  slotMinWidth: Number,\n  navLinks: Boolean,\n  eventTimeFormat: createFormatter,\n  rerenderDelay: Number,\n  moreLinkText: identity,\n  moreLinkHint: identity,\n  selectMinDistance: Number,\n  selectable: Boolean,\n  selectLongPressDelay: Number,\n  eventLongPressDelay: Number,\n  selectMirror: Boolean,\n  eventMaxStack: Number,\n  eventMinHeight: Number,\n  eventMinWidth: Number,\n  eventShortHeight: Number,\n  slotEventOverlap: Boolean,\n  plugins: identity,\n  firstDay: Number,\n  dayCount: Number,\n  dateAlignment: String,\n  dateIncrement: createDuration,\n  hiddenDays: identity,\n  fixedWeekCount: Boolean,\n  validRange: identity,\n  visibleRange: identity,\n  titleFormat: identity,\n  eventInteractive: Boolean,\n  // only used by list-view, but languages define the value, so we need it in base options\n  noEventsText: String,\n  viewHint: identity,\n  navLinkHint: identity,\n  closeHint: String,\n  timeHint: String,\n  eventHint: String,\n  moreLinkClick: identity,\n  moreLinkClassNames: identity,\n  moreLinkContent: identity,\n  moreLinkDidMount: identity,\n  moreLinkWillUnmount: identity,\n  monthStartFormat: createFormatter,\n  // for connectors\n  // (can't be part of plugin system b/c must be provided at runtime)\n  handleCustomRendering: identity,\n  customRenderingMetaMap: identity,\n  customRenderingReplaces: Boolean\n};\n// do NOT give a type here. need `typeof BASE_OPTION_DEFAULTS` to give real results.\n// raw values.\nconst BASE_OPTION_DEFAULTS = {\n  eventDisplay: 'auto',\n  defaultRangeSeparator: ' - ',\n  titleRangeSeparator: ' \\u2013 ',\n  defaultTimedEventDuration: '01:00:00',\n  defaultAllDayEventDuration: {\n    day: 1\n  },\n  forceEventDuration: false,\n  nextDayThreshold: '00:00:00',\n  dayHeaders: true,\n  initialView: '',\n  aspectRatio: 1.35,\n  headerToolbar: {\n    start: 'title',\n    center: '',\n    end: 'today prev,next'\n  },\n  weekends: true,\n  weekNumbers: false,\n  weekNumberCalculation: 'local',\n  editable: false,\n  nowIndicator: false,\n  scrollTime: '06:00:00',\n  scrollTimeReset: true,\n  slotMinTime: '00:00:00',\n  slotMaxTime: '24:00:00',\n  showNonCurrentDates: true,\n  lazyFetching: true,\n  startParam: 'start',\n  endParam: 'end',\n  timeZoneParam: 'timeZone',\n  timeZone: 'local',\n  locales: [],\n  locale: '',\n  themeSystem: 'standard',\n  dragRevertDuration: 500,\n  dragScroll: true,\n  allDayMaintainDuration: false,\n  unselectAuto: true,\n  dropAccept: '*',\n  eventOrder: 'start,-duration,allDay,title',\n  dayPopoverFormat: {\n    month: 'long',\n    day: 'numeric',\n    year: 'numeric'\n  },\n  handleWindowResize: true,\n  windowResizeDelay: 100,\n  longPressDelay: 1000,\n  eventDragMinDistance: 5,\n  expandRows: false,\n  navLinks: false,\n  selectable: false,\n  eventMinHeight: 15,\n  eventMinWidth: 30,\n  eventShortHeight: 30,\n  monthStartFormat: {\n    month: 'long',\n    day: 'numeric'\n  }\n};\n// calendar listeners\n// ------------------\nconst CALENDAR_LISTENER_REFINERS = {\n  datesSet: identity,\n  eventsSet: identity,\n  eventAdd: identity,\n  eventChange: identity,\n  eventRemove: identity,\n  windowResize: identity,\n  eventClick: identity,\n  eventMouseEnter: identity,\n  eventMouseLeave: identity,\n  select: identity,\n  unselect: identity,\n  loading: identity,\n  // internal\n  _unmount: identity,\n  _beforeprint: identity,\n  _afterprint: identity,\n  _noEventDrop: identity,\n  _noEventResize: identity,\n  _resize: identity,\n  _scrollRequest: identity\n};\n// calendar-specific options\n// -------------------------\nconst CALENDAR_OPTION_REFINERS = {\n  buttonText: identity,\n  buttonHints: identity,\n  views: identity,\n  plugins: identity,\n  initialEvents: identity,\n  events: identity,\n  eventSources: identity\n};\nconst COMPLEX_OPTION_COMPARATORS = {\n  headerToolbar: isMaybeObjectsEqual,\n  footerToolbar: isMaybeObjectsEqual,\n  buttonText: isMaybeObjectsEqual,\n  buttonHints: isMaybeObjectsEqual,\n  buttonIcons: isMaybeObjectsEqual,\n  dateIncrement: isMaybeObjectsEqual,\n  plugins: isMaybeArraysEqual,\n  events: isMaybeArraysEqual,\n  eventSources: isMaybeArraysEqual,\n  ['resources']: isMaybeArraysEqual\n};\nfunction isMaybeObjectsEqual(a, b) {\n  if (typeof a === 'object' && typeof b === 'object' && a && b) {\n    // both non-null objects\n    return isPropsEqual(a, b);\n  }\n  return a === b;\n}\nfunction isMaybeArraysEqual(a, b) {\n  if (Array.isArray(a) && Array.isArray(b)) {\n    return isArraysEqual(a, b);\n  }\n  return a === b;\n}\n// view-specific options\n// ---------------------\nconst VIEW_OPTION_REFINERS = {\n  type: String,\n  component: identity,\n  buttonText: String,\n  buttonTextKey: String,\n  dateProfileGeneratorClass: identity,\n  usesMinMaxTime: Boolean,\n  classNames: identity,\n  content: identity,\n  didMount: identity,\n  willUnmount: identity\n};\n// util funcs\n// ----------------------------------------------------------------------------------------------------\nfunction mergeRawOptions(optionSets) {\n  return mergeProps(optionSets, COMPLEX_OPTION_COMPARATORS);\n}\nfunction refineProps(input, refiners) {\n  let refined = {};\n  let extra = {};\n  for (let propName in refiners) {\n    if (propName in input) {\n      refined[propName] = refiners[propName](input[propName]);\n    }\n  }\n  for (let propName in input) {\n    if (!(propName in refiners)) {\n      extra[propName] = input[propName];\n    }\n  }\n  return {\n    refined,\n    extra\n  };\n}\nfunction identity(raw) {\n  return raw;\n}\nconst {\n  hasOwnProperty\n} = Object.prototype;\n// Merges an array of objects into a single object.\n// The second argument allows for an array of property names who's object values will be merged together.\nfunction mergeProps(propObjs, complexPropsMap) {\n  let dest = {};\n  if (complexPropsMap) {\n    for (let name in complexPropsMap) {\n      if (complexPropsMap[name] === isMaybeObjectsEqual) {\n        // implies that it's object-mergeable\n        let complexObjs = [];\n        // collect the trailing object values, stopping when a non-object is discovered\n        for (let i = propObjs.length - 1; i >= 0; i -= 1) {\n          let val = propObjs[i][name];\n          if (typeof val === 'object' && val) {\n            // non-null object\n            complexObjs.unshift(val);\n          } else if (val !== undefined) {\n            dest[name] = val; // if there were no objects, this value will be used\n            break;\n          }\n        }\n        // if the trailing values were objects, use the merged value\n        if (complexObjs.length) {\n          dest[name] = mergeProps(complexObjs);\n        }\n      }\n    }\n  }\n  // copy values into the destination, going from last to first\n  for (let i = propObjs.length - 1; i >= 0; i -= 1) {\n    let props = propObjs[i];\n    for (let name in props) {\n      if (!(name in dest)) {\n        // if already assigned by previous props or complex props, don't reassign\n        dest[name] = props[name];\n      }\n    }\n  }\n  return dest;\n}\nfunction filterHash(hash, func) {\n  let filtered = {};\n  for (let key in hash) {\n    if (func(hash[key], key)) {\n      filtered[key] = hash[key];\n    }\n  }\n  return filtered;\n}\nfunction mapHash(hash, func) {\n  let newHash = {};\n  for (let key in hash) {\n    newHash[key] = func(hash[key], key);\n  }\n  return newHash;\n}\nfunction arrayToHash(a) {\n  let hash = {};\n  for (let item of a) {\n    hash[item] = true;\n  }\n  return hash;\n}\n// TODO: reassess browser support\n// https://caniuse.com/?search=object.values\nfunction hashValuesToArray(obj) {\n  let a = [];\n  for (let key in obj) {\n    a.push(obj[key]);\n  }\n  return a;\n}\nfunction isPropsEqual(obj0, obj1) {\n  if (obj0 === obj1) {\n    return true;\n  }\n  for (let key in obj0) {\n    if (hasOwnProperty.call(obj0, key)) {\n      if (!(key in obj1)) {\n        return false;\n      }\n    }\n  }\n  for (let key in obj1) {\n    if (hasOwnProperty.call(obj1, key)) {\n      if (obj0[key] !== obj1[key]) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\nconst HANDLER_RE = /^on[A-Z]/;\nfunction isNonHandlerPropsEqual(obj0, obj1) {\n  const keys = getUnequalProps(obj0, obj1);\n  for (let key of keys) {\n    if (!HANDLER_RE.test(key)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction getUnequalProps(obj0, obj1) {\n  let keys = [];\n  for (let key in obj0) {\n    if (hasOwnProperty.call(obj0, key)) {\n      if (!(key in obj1)) {\n        keys.push(key);\n      }\n    }\n  }\n  for (let key in obj1) {\n    if (hasOwnProperty.call(obj1, key)) {\n      if (obj0[key] !== obj1[key]) {\n        keys.push(key);\n      }\n    }\n  }\n  return keys;\n}\nfunction compareObjs(oldProps, newProps, equalityFuncs = {}) {\n  if (oldProps === newProps) {\n    return true;\n  }\n  for (let key in newProps) {\n    if (key in oldProps && isObjValsEqual(oldProps[key], newProps[key], equalityFuncs[key])) ;else {\n      return false;\n    }\n  }\n  // check for props that were omitted in the new\n  for (let key in oldProps) {\n    if (!(key in newProps)) {\n      return false;\n    }\n  }\n  return true;\n}\n/*\nassumed \"true\" equality for handler names like \"onReceiveSomething\"\n*/\nfunction isObjValsEqual(val0, val1, comparator) {\n  if (val0 === val1 || comparator === true) {\n    return true;\n  }\n  if (comparator) {\n    return comparator(val0, val1);\n  }\n  return false;\n}\nfunction collectFromHash(hash, startIndex = 0, endIndex, step = 1) {\n  let res = [];\n  if (endIndex == null) {\n    endIndex = Object.keys(hash).length;\n  }\n  for (let i = startIndex; i < endIndex; i += step) {\n    let val = hash[i];\n    if (val !== undefined) {\n      // will disregard undefined for sparse arrays\n      res.push(val);\n    }\n  }\n  return res;\n}\nlet calendarSystemClassMap = {};\nfunction registerCalendarSystem(name, theClass) {\n  calendarSystemClassMap[name] = theClass;\n}\nfunction createCalendarSystem(name) {\n  return new calendarSystemClassMap[name]();\n}\nclass GregorianCalendarSystem {\n  getMarkerYear(d) {\n    return d.getUTCFullYear();\n  }\n  getMarkerMonth(d) {\n    return d.getUTCMonth();\n  }\n  getMarkerDay(d) {\n    return d.getUTCDate();\n  }\n  arrayToMarker(arr) {\n    return arrayToUtcDate(arr);\n  }\n  markerToArray(marker) {\n    return dateToUtcArray(marker);\n  }\n}\nregisterCalendarSystem('gregory', GregorianCalendarSystem);\nconst ISO_RE = /^\\s*(\\d{4})(-?(\\d{2})(-?(\\d{2})([T ](\\d{2}):?(\\d{2})(:?(\\d{2})(\\.(\\d+))?)?(Z|(([-+])(\\d{2})(:?(\\d{2}))?))?)?)?)?$/;\nfunction parse(str) {\n  let m = ISO_RE.exec(str);\n  if (m) {\n    let marker = new Date(Date.UTC(Number(m[1]), m[3] ? Number(m[3]) - 1 : 0, Number(m[5] || 1), Number(m[7] || 0), Number(m[8] || 0), Number(m[10] || 0), m[12] ? Number(`0.${m[12]}`) * 1000 : 0));\n    if (isValidDate(marker)) {\n      let timeZoneOffset = null;\n      if (m[13]) {\n        timeZoneOffset = (m[15] === '-' ? -1 : 1) * (Number(m[16] || 0) * 60 + Number(m[18] || 0));\n      }\n      return {\n        marker,\n        isTimeUnspecified: !m[6],\n        timeZoneOffset\n      };\n    }\n  }\n  return null;\n}\nclass DateEnv {\n  constructor(settings) {\n    let timeZone = this.timeZone = settings.timeZone;\n    let isNamedTimeZone = timeZone !== 'local' && timeZone !== 'UTC';\n    if (settings.namedTimeZoneImpl && isNamedTimeZone) {\n      this.namedTimeZoneImpl = new settings.namedTimeZoneImpl(timeZone);\n    }\n    this.canComputeOffset = Boolean(!isNamedTimeZone || this.namedTimeZoneImpl);\n    this.calendarSystem = createCalendarSystem(settings.calendarSystem);\n    this.locale = settings.locale;\n    this.weekDow = settings.locale.week.dow;\n    this.weekDoy = settings.locale.week.doy;\n    if (settings.weekNumberCalculation === 'ISO') {\n      this.weekDow = 1;\n      this.weekDoy = 4;\n    }\n    if (typeof settings.firstDay === 'number') {\n      this.weekDow = settings.firstDay;\n    }\n    if (typeof settings.weekNumberCalculation === 'function') {\n      this.weekNumberFunc = settings.weekNumberCalculation;\n    }\n    this.weekText = settings.weekText != null ? settings.weekText : settings.locale.options.weekText;\n    this.weekTextLong = (settings.weekTextLong != null ? settings.weekTextLong : settings.locale.options.weekTextLong) || this.weekText;\n    this.cmdFormatter = settings.cmdFormatter;\n    this.defaultSeparator = settings.defaultSeparator;\n  }\n  // Creating / Parsing\n  createMarker(input) {\n    let meta = this.createMarkerMeta(input);\n    if (meta === null) {\n      return null;\n    }\n    return meta.marker;\n  }\n  createNowMarker() {\n    if (this.canComputeOffset) {\n      return this.timestampToMarker(new Date().valueOf());\n    }\n    // if we can't compute the current date val for a timezone,\n    // better to give the current local date vals than UTC\n    return arrayToUtcDate(dateToLocalArray(new Date()));\n  }\n  createMarkerMeta(input) {\n    if (typeof input === 'string') {\n      return this.parse(input);\n    }\n    let marker = null;\n    if (typeof input === 'number') {\n      marker = this.timestampToMarker(input);\n    } else if (input instanceof Date) {\n      input = input.valueOf();\n      if (!isNaN(input)) {\n        marker = this.timestampToMarker(input);\n      }\n    } else if (Array.isArray(input)) {\n      marker = arrayToUtcDate(input);\n    }\n    if (marker === null || !isValidDate(marker)) {\n      return null;\n    }\n    return {\n      marker,\n      isTimeUnspecified: false,\n      forcedTzo: null\n    };\n  }\n  parse(s) {\n    let parts = parse(s);\n    if (parts === null) {\n      return null;\n    }\n    let {\n      marker\n    } = parts;\n    let forcedTzo = null;\n    if (parts.timeZoneOffset !== null) {\n      if (this.canComputeOffset) {\n        marker = this.timestampToMarker(marker.valueOf() - parts.timeZoneOffset * 60 * 1000);\n      } else {\n        forcedTzo = parts.timeZoneOffset;\n      }\n    }\n    return {\n      marker,\n      isTimeUnspecified: parts.isTimeUnspecified,\n      forcedTzo\n    };\n  }\n  // Accessors\n  getYear(marker) {\n    return this.calendarSystem.getMarkerYear(marker);\n  }\n  getMonth(marker) {\n    return this.calendarSystem.getMarkerMonth(marker);\n  }\n  getDay(marker) {\n    return this.calendarSystem.getMarkerDay(marker);\n  }\n  // Adding / Subtracting\n  add(marker, dur) {\n    let a = this.calendarSystem.markerToArray(marker);\n    a[0] += dur.years;\n    a[1] += dur.months;\n    a[2] += dur.days;\n    a[6] += dur.milliseconds;\n    return this.calendarSystem.arrayToMarker(a);\n  }\n  subtract(marker, dur) {\n    let a = this.calendarSystem.markerToArray(marker);\n    a[0] -= dur.years;\n    a[1] -= dur.months;\n    a[2] -= dur.days;\n    a[6] -= dur.milliseconds;\n    return this.calendarSystem.arrayToMarker(a);\n  }\n  addYears(marker, n) {\n    let a = this.calendarSystem.markerToArray(marker);\n    a[0] += n;\n    return this.calendarSystem.arrayToMarker(a);\n  }\n  addMonths(marker, n) {\n    let a = this.calendarSystem.markerToArray(marker);\n    a[1] += n;\n    return this.calendarSystem.arrayToMarker(a);\n  }\n  // Diffing Whole Units\n  diffWholeYears(m0, m1) {\n    let {\n      calendarSystem\n    } = this;\n    if (timeAsMs(m0) === timeAsMs(m1) && calendarSystem.getMarkerDay(m0) === calendarSystem.getMarkerDay(m1) && calendarSystem.getMarkerMonth(m0) === calendarSystem.getMarkerMonth(m1)) {\n      return calendarSystem.getMarkerYear(m1) - calendarSystem.getMarkerYear(m0);\n    }\n    return null;\n  }\n  diffWholeMonths(m0, m1) {\n    let {\n      calendarSystem\n    } = this;\n    if (timeAsMs(m0) === timeAsMs(m1) && calendarSystem.getMarkerDay(m0) === calendarSystem.getMarkerDay(m1)) {\n      return calendarSystem.getMarkerMonth(m1) - calendarSystem.getMarkerMonth(m0) + (calendarSystem.getMarkerYear(m1) - calendarSystem.getMarkerYear(m0)) * 12;\n    }\n    return null;\n  }\n  // Range / Duration\n  greatestWholeUnit(m0, m1) {\n    let n = this.diffWholeYears(m0, m1);\n    if (n !== null) {\n      return {\n        unit: 'year',\n        value: n\n      };\n    }\n    n = this.diffWholeMonths(m0, m1);\n    if (n !== null) {\n      return {\n        unit: 'month',\n        value: n\n      };\n    }\n    n = diffWholeWeeks(m0, m1);\n    if (n !== null) {\n      return {\n        unit: 'week',\n        value: n\n      };\n    }\n    n = diffWholeDays(m0, m1);\n    if (n !== null) {\n      return {\n        unit: 'day',\n        value: n\n      };\n    }\n    n = diffHours(m0, m1);\n    if (isInt(n)) {\n      return {\n        unit: 'hour',\n        value: n\n      };\n    }\n    n = diffMinutes(m0, m1);\n    if (isInt(n)) {\n      return {\n        unit: 'minute',\n        value: n\n      };\n    }\n    n = diffSeconds(m0, m1);\n    if (isInt(n)) {\n      return {\n        unit: 'second',\n        value: n\n      };\n    }\n    return {\n      unit: 'millisecond',\n      value: m1.valueOf() - m0.valueOf()\n    };\n  }\n  countDurationsBetween(m0, m1, d) {\n    // TODO: can use greatestWholeUnit\n    let diff;\n    if (d.years) {\n      diff = this.diffWholeYears(m0, m1);\n      if (diff !== null) {\n        return diff / asRoughYears(d);\n      }\n    }\n    if (d.months) {\n      diff = this.diffWholeMonths(m0, m1);\n      if (diff !== null) {\n        return diff / asRoughMonths(d);\n      }\n    }\n    if (d.days) {\n      diff = diffWholeDays(m0, m1);\n      if (diff !== null) {\n        return diff / asRoughDays(d);\n      }\n    }\n    return (m1.valueOf() - m0.valueOf()) / asRoughMs(d);\n  }\n  // Start-Of\n  // these DON'T return zoned-dates. only UTC start-of dates\n  startOf(m, unit) {\n    if (unit === 'year') {\n      return this.startOfYear(m);\n    }\n    if (unit === 'month') {\n      return this.startOfMonth(m);\n    }\n    if (unit === 'week') {\n      return this.startOfWeek(m);\n    }\n    if (unit === 'day') {\n      return startOfDay(m);\n    }\n    if (unit === 'hour') {\n      return startOfHour(m);\n    }\n    if (unit === 'minute') {\n      return startOfMinute(m);\n    }\n    if (unit === 'second') {\n      return startOfSecond(m);\n    }\n    return null;\n  }\n  startOfYear(m) {\n    return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(m)]);\n  }\n  startOfMonth(m) {\n    return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(m), this.calendarSystem.getMarkerMonth(m)]);\n  }\n  startOfWeek(m) {\n    return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(m), this.calendarSystem.getMarkerMonth(m), m.getUTCDate() - (m.getUTCDay() - this.weekDow + 7) % 7]);\n  }\n  // Week Number\n  computeWeekNumber(marker) {\n    if (this.weekNumberFunc) {\n      return this.weekNumberFunc(this.toDate(marker));\n    }\n    return weekOfYear(marker, this.weekDow, this.weekDoy);\n  }\n  // TODO: choke on timeZoneName: long\n  format(marker, formatter, dateOptions = {}) {\n    return formatter.format({\n      marker,\n      timeZoneOffset: dateOptions.forcedTzo != null ? dateOptions.forcedTzo : this.offsetForMarker(marker)\n    }, this);\n  }\n  formatRange(start, end, formatter, dateOptions = {}) {\n    if (dateOptions.isEndExclusive) {\n      end = addMs(end, -1);\n    }\n    return formatter.formatRange({\n      marker: start,\n      timeZoneOffset: dateOptions.forcedStartTzo != null ? dateOptions.forcedStartTzo : this.offsetForMarker(start)\n    }, {\n      marker: end,\n      timeZoneOffset: dateOptions.forcedEndTzo != null ? dateOptions.forcedEndTzo : this.offsetForMarker(end)\n    }, this, dateOptions.defaultSeparator);\n  }\n  /*\n  DUMB: the omitTime arg is dumb. if we omit the time, we want to omit the timezone offset. and if we do that,\n  might as well use buildIsoString or some other util directly\n  */\n  formatIso(marker, extraOptions = {}) {\n    let timeZoneOffset = null;\n    if (!extraOptions.omitTimeZoneOffset) {\n      if (extraOptions.forcedTzo != null) {\n        timeZoneOffset = extraOptions.forcedTzo;\n      } else {\n        timeZoneOffset = this.offsetForMarker(marker);\n      }\n    }\n    return buildIsoString(marker, timeZoneOffset, extraOptions.omitTime);\n  }\n  // TimeZone\n  timestampToMarker(ms) {\n    if (this.timeZone === 'local') {\n      return arrayToUtcDate(dateToLocalArray(new Date(ms)));\n    }\n    if (this.timeZone === 'UTC' || !this.namedTimeZoneImpl) {\n      return new Date(ms);\n    }\n    return arrayToUtcDate(this.namedTimeZoneImpl.timestampToArray(ms));\n  }\n  offsetForMarker(m) {\n    if (this.timeZone === 'local') {\n      return -arrayToLocalDate(dateToUtcArray(m)).getTimezoneOffset(); // convert \"inverse\" offset to \"normal\" offset\n    }\n    if (this.timeZone === 'UTC') {\n      return 0;\n    }\n    if (this.namedTimeZoneImpl) {\n      return this.namedTimeZoneImpl.offsetForArray(dateToUtcArray(m));\n    }\n    return null;\n  }\n  // Conversion\n  toDate(m, forcedTzo) {\n    if (this.timeZone === 'local') {\n      return arrayToLocalDate(dateToUtcArray(m));\n    }\n    if (this.timeZone === 'UTC') {\n      return new Date(m.valueOf()); // make sure it's a copy\n    }\n    if (!this.namedTimeZoneImpl) {\n      return new Date(m.valueOf() - (forcedTzo || 0));\n    }\n    return new Date(m.valueOf() - this.namedTimeZoneImpl.offsetForArray(dateToUtcArray(m)) * 1000 * 60);\n  }\n}\nclass Theme {\n  constructor(calendarOptions) {\n    if (this.iconOverrideOption) {\n      this.setIconOverride(calendarOptions[this.iconOverrideOption]);\n    }\n  }\n  setIconOverride(iconOverrideHash) {\n    let iconClassesCopy;\n    let buttonName;\n    if (typeof iconOverrideHash === 'object' && iconOverrideHash) {\n      // non-null object\n      iconClassesCopy = Object.assign({}, this.iconClasses);\n      for (buttonName in iconOverrideHash) {\n        iconClassesCopy[buttonName] = this.applyIconOverridePrefix(iconOverrideHash[buttonName]);\n      }\n      this.iconClasses = iconClassesCopy;\n    } else if (iconOverrideHash === false) {\n      this.iconClasses = {};\n    }\n  }\n  applyIconOverridePrefix(className) {\n    let prefix = this.iconOverridePrefix;\n    if (prefix && className.indexOf(prefix) !== 0) {\n      // if not already present\n      className = prefix + className;\n    }\n    return className;\n  }\n  getClass(key) {\n    return this.classes[key] || '';\n  }\n  getIconClass(buttonName, isRtl) {\n    let className;\n    if (isRtl && this.rtlIconClasses) {\n      className = this.rtlIconClasses[buttonName] || this.iconClasses[buttonName];\n    } else {\n      className = this.iconClasses[buttonName];\n    }\n    if (className) {\n      return `${this.baseIconClass} ${className}`;\n    }\n    return '';\n  }\n  getCustomButtonIconClass(customButtonProps) {\n    let className;\n    if (this.iconOverrideCustomButtonOption) {\n      className = customButtonProps[this.iconOverrideCustomButtonOption];\n      if (className) {\n        return `${this.baseIconClass} ${this.applyIconOverridePrefix(className)}`;\n      }\n    }\n    return '';\n  }\n}\nTheme.prototype.classes = {};\nTheme.prototype.iconClasses = {};\nTheme.prototype.baseIconClass = '';\nTheme.prototype.iconOverridePrefix = '';\n\n/*\nNOTE: this can be a public API, especially createElement for hooks.\nSee examples/typescript-scheduler/src/index.ts\n*/\nfunction flushSync(runBeforeFlush) {\n  runBeforeFlush();\n  let oldDebounceRendering = preact.options.debounceRendering; // orig\n  let callbackQ = [];\n  function execCallbackSync(callback) {\n    callbackQ.push(callback);\n  }\n  preact.options.debounceRendering = execCallbackSync;\n  preact.render(preact.createElement(FakeComponent, {}), document.createElement('div'));\n  while (callbackQ.length) {\n    callbackQ.shift()();\n  }\n  preact.options.debounceRendering = oldDebounceRendering;\n}\nclass FakeComponent extends preact.Component {\n  render() {\n    return preact.createElement('div', {});\n  }\n  componentDidMount() {\n    this.setState({});\n  }\n}\n// TODO: use preact/compat instead?\nfunction createContext(defaultValue) {\n  let ContextType = preact.createContext(defaultValue);\n  let origProvider = ContextType.Provider;\n  ContextType.Provider = function () {\n    let isNew = !this.getChildContext;\n    let children = origProvider.apply(this, arguments); // eslint-disable-line prefer-rest-params\n    if (isNew) {\n      let subs = [];\n      this.shouldComponentUpdate = _props => {\n        if (this.props.value !== _props.value) {\n          subs.forEach(c => {\n            c.context = _props.value;\n            c.forceUpdate();\n          });\n        }\n      };\n      this.sub = c => {\n        subs.push(c);\n        let old = c.componentWillUnmount;\n        c.componentWillUnmount = () => {\n          subs.splice(subs.indexOf(c), 1);\n          old && old.call(c);\n        };\n      };\n    }\n    return children;\n  };\n  return ContextType;\n}\nclass ScrollResponder {\n  constructor(execFunc, emitter, scrollTime, scrollTimeReset) {\n    this.execFunc = execFunc;\n    this.emitter = emitter;\n    this.scrollTime = scrollTime;\n    this.scrollTimeReset = scrollTimeReset;\n    this.handleScrollRequest = request => {\n      this.queuedRequest = Object.assign({}, this.queuedRequest || {}, request);\n      this.drain();\n    };\n    emitter.on('_scrollRequest', this.handleScrollRequest);\n    this.fireInitialScroll();\n  }\n  detach() {\n    this.emitter.off('_scrollRequest', this.handleScrollRequest);\n  }\n  update(isDatesNew) {\n    if (isDatesNew && this.scrollTimeReset) {\n      this.fireInitialScroll(); // will drain\n    } else {\n      this.drain();\n    }\n  }\n  fireInitialScroll() {\n    this.handleScrollRequest({\n      time: this.scrollTime\n    });\n  }\n  drain() {\n    if (this.queuedRequest && this.execFunc(this.queuedRequest)) {\n      this.queuedRequest = null;\n    }\n  }\n}\nconst ViewContextType = createContext({}); // for Components\nfunction buildViewContext(viewSpec, viewApi, viewOptions, dateProfileGenerator, dateEnv, theme, pluginHooks, dispatch, getCurrentData, emitter, calendarApi, registerInteractiveComponent, unregisterInteractiveComponent) {\n  return {\n    dateEnv,\n    options: viewOptions,\n    pluginHooks,\n    emitter,\n    dispatch,\n    getCurrentData,\n    calendarApi,\n    viewSpec,\n    viewApi,\n    dateProfileGenerator,\n    theme,\n    isRtl: viewOptions.direction === 'rtl',\n    addResizeHandler(handler) {\n      emitter.on('_resize', handler);\n    },\n    removeResizeHandler(handler) {\n      emitter.off('_resize', handler);\n    },\n    createScrollResponder(execFunc) {\n      return new ScrollResponder(execFunc, emitter, createDuration(viewOptions.scrollTime), viewOptions.scrollTimeReset);\n    },\n    registerInteractiveComponent,\n    unregisterInteractiveComponent\n  };\n}\n\n/* eslint max-classes-per-file: off */\nlet PureComponent = /*#__PURE__*/(() => {\n  class PureComponent extends Component {\n    shouldComponentUpdate(nextProps, nextState) {\n      if (this.debug) {\n        // eslint-disable-next-line no-console\n        console.log(getUnequalProps(nextProps, this.props), getUnequalProps(nextState, this.state));\n      }\n      return !compareObjs(this.props, nextProps, this.propEquality) || !compareObjs(this.state, nextState, this.stateEquality);\n    }\n    // HACK for freakin' React StrictMode\n    safeSetState(newState) {\n      if (!compareObjs(this.state, Object.assign(Object.assign({}, this.state), newState), this.stateEquality)) {\n        this.setState(newState);\n      }\n    }\n  }\n  PureComponent.addPropsEquality = addPropsEquality;\n  PureComponent.addStateEquality = addStateEquality;\n  PureComponent.contextType = ViewContextType;\n  return PureComponent;\n})();\nPureComponent.prototype.propEquality = {};\nPureComponent.prototype.stateEquality = {};\nlet BaseComponent = /*#__PURE__*/(() => {\n  class BaseComponent extends PureComponent {}\n  BaseComponent.contextType = ViewContextType;\n  return BaseComponent;\n})();\nfunction addPropsEquality(propEquality) {\n  let hash = Object.create(this.prototype.propEquality);\n  Object.assign(hash, propEquality);\n  this.prototype.propEquality = hash;\n}\nfunction addStateEquality(stateEquality) {\n  let hash = Object.create(this.prototype.stateEquality);\n  Object.assign(hash, stateEquality);\n  this.prototype.stateEquality = hash;\n}\n// use other one\nfunction setRef(ref, current) {\n  if (typeof ref === 'function') {\n    ref(current);\n  } else if (ref) {\n    // see https://github.com/facebook/react/issues/13029\n    ref.current = current;\n  }\n}\nclass ContentInjector extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.id = guid();\n    this.queuedDomNodes = [];\n    this.currentDomNodes = [];\n    this.handleEl = el => {\n      const {\n        options\n      } = this.context;\n      const {\n        generatorName\n      } = this.props;\n      if (!options.customRenderingReplaces || !hasCustomRenderingHandler(generatorName, options)) {\n        this.updateElRef(el);\n      }\n    };\n    this.updateElRef = el => {\n      if (this.props.elRef) {\n        setRef(this.props.elRef, el);\n      }\n    };\n  }\n  render() {\n    const {\n      props,\n      context\n    } = this;\n    const {\n      options\n    } = context;\n    const {\n      customGenerator,\n      defaultGenerator,\n      renderProps\n    } = props;\n    const attrs = buildElAttrs(props, [], this.handleEl);\n    let useDefault = false;\n    let innerContent;\n    let queuedDomNodes = [];\n    let currentGeneratorMeta;\n    if (customGenerator != null) {\n      const customGeneratorRes = typeof customGenerator === 'function' ? customGenerator(renderProps, createElement) : customGenerator;\n      if (customGeneratorRes === true) {\n        useDefault = true;\n      } else {\n        const isObject = customGeneratorRes && typeof customGeneratorRes === 'object'; // non-null\n        if (isObject && 'html' in customGeneratorRes) {\n          attrs.dangerouslySetInnerHTML = {\n            __html: customGeneratorRes.html\n          };\n        } else if (isObject && 'domNodes' in customGeneratorRes) {\n          queuedDomNodes = Array.prototype.slice.call(customGeneratorRes.domNodes);\n        } else if (isObject ? isValidElement(customGeneratorRes) // vdom node\n        : typeof customGeneratorRes !== 'function' // primitive value (like string or number)\n        ) {\n          // use in vdom\n          innerContent = customGeneratorRes;\n        } else {\n          // an exotic object for handleCustomRendering\n          currentGeneratorMeta = customGeneratorRes;\n        }\n      }\n    } else {\n      useDefault = !hasCustomRenderingHandler(props.generatorName, options);\n    }\n    if (useDefault && defaultGenerator) {\n      innerContent = defaultGenerator(renderProps);\n    }\n    this.queuedDomNodes = queuedDomNodes;\n    this.currentGeneratorMeta = currentGeneratorMeta;\n    return createElement(props.elTag, attrs, innerContent);\n  }\n  componentDidMount() {\n    this.applyQueueudDomNodes();\n    this.triggerCustomRendering(true);\n  }\n  componentDidUpdate() {\n    this.applyQueueudDomNodes();\n    this.triggerCustomRendering(true);\n  }\n  componentWillUnmount() {\n    this.triggerCustomRendering(false); // TODO: different API for removal?\n  }\n  triggerCustomRendering(isActive) {\n    var _a;\n    const {\n      props,\n      context\n    } = this;\n    const {\n      handleCustomRendering,\n      customRenderingMetaMap\n    } = context.options;\n    if (handleCustomRendering) {\n      const generatorMeta = (_a = this.currentGeneratorMeta) !== null && _a !== void 0 ? _a : customRenderingMetaMap === null || customRenderingMetaMap === void 0 ? void 0 : customRenderingMetaMap[props.generatorName];\n      if (generatorMeta) {\n        handleCustomRendering(Object.assign(Object.assign({\n          id: this.id,\n          isActive,\n          containerEl: this.base,\n          reportNewContainerEl: this.updateElRef,\n          // front-end framework tells us about new container els\n          generatorMeta\n        }, props), {\n          elClasses: (props.elClasses || []).filter(isTruthy)\n        }));\n      }\n    }\n  }\n  applyQueueudDomNodes() {\n    const {\n      queuedDomNodes,\n      currentDomNodes\n    } = this;\n    const el = this.base;\n    if (!isArraysEqual(queuedDomNodes, currentDomNodes)) {\n      currentDomNodes.forEach(removeElement);\n      for (let newNode of queuedDomNodes) {\n        el.appendChild(newNode);\n      }\n      this.currentDomNodes = queuedDomNodes;\n    }\n  }\n}\nContentInjector.addPropsEquality({\n  elClasses: isArraysEqual,\n  elStyle: isPropsEqual,\n  elAttrs: isNonHandlerPropsEqual,\n  renderProps: isPropsEqual\n});\n// Util\n/*\nDoes UI-framework provide custom way of rendering that does not use Preact VDOM\nAND does the calendar's options define custom rendering?\nAKA. Should we NOT render the default content?\n*/\nfunction hasCustomRenderingHandler(generatorName, options) {\n  var _a;\n  return Boolean(options.handleCustomRendering && generatorName && ((_a = options.customRenderingMetaMap) === null || _a === void 0 ? void 0 : _a[generatorName]));\n}\nfunction buildElAttrs(props, extraClassNames, elRef) {\n  const attrs = Object.assign(Object.assign({}, props.elAttrs), {\n    ref: elRef\n  });\n  if (props.elClasses || extraClassNames) {\n    attrs.className = (props.elClasses || []).concat(extraClassNames || []).concat(attrs.className || []).filter(Boolean).join(' ');\n  }\n  if (props.elStyle) {\n    attrs.style = props.elStyle;\n  }\n  return attrs;\n}\nfunction isTruthy(val) {\n  return Boolean(val);\n}\nconst RenderId = createContext(0);\nlet ContentContainer = /*#__PURE__*/(() => {\n  class ContentContainer extends Component {\n    constructor() {\n      super(...arguments);\n      this.InnerContent = InnerContentInjector.bind(undefined, this);\n      this.handleEl = el => {\n        this.el = el;\n        if (this.props.elRef) {\n          setRef(this.props.elRef, el);\n          if (el && this.didMountMisfire) {\n            this.componentDidMount();\n          }\n        }\n      };\n    }\n    render() {\n      const {\n        props\n      } = this;\n      const generatedClassNames = generateClassNames(props.classNameGenerator, props.renderProps);\n      if (props.children) {\n        const elAttrs = buildElAttrs(props, generatedClassNames, this.handleEl);\n        const children = props.children(this.InnerContent, props.renderProps, elAttrs);\n        if (props.elTag) {\n          return createElement(props.elTag, elAttrs, children);\n        } else {\n          return children;\n        }\n      } else {\n        return createElement(ContentInjector, Object.assign(Object.assign({}, props), {\n          elRef: this.handleEl,\n          elTag: props.elTag || 'div',\n          elClasses: (props.elClasses || []).concat(generatedClassNames),\n          renderId: this.context\n        }));\n      }\n    }\n    componentDidMount() {\n      var _a, _b;\n      if (this.el) {\n        (_b = (_a = this.props).didMount) === null || _b === void 0 ? void 0 : _b.call(_a, Object.assign(Object.assign({}, this.props.renderProps), {\n          el: this.el\n        }));\n      } else {\n        this.didMountMisfire = true;\n      }\n    }\n    componentWillUnmount() {\n      var _a, _b;\n      (_b = (_a = this.props).willUnmount) === null || _b === void 0 ? void 0 : _b.call(_a, Object.assign(Object.assign({}, this.props.renderProps), {\n        el: this.el\n      }));\n    }\n  }\n  ContentContainer.contextType = RenderId;\n  return ContentContainer;\n})();\nfunction InnerContentInjector(containerComponent, props) {\n  const parentProps = containerComponent.props;\n  return createElement(ContentInjector, Object.assign({\n    renderProps: parentProps.renderProps,\n    generatorName: parentProps.generatorName,\n    customGenerator: parentProps.customGenerator,\n    defaultGenerator: parentProps.defaultGenerator,\n    renderId: containerComponent.context\n  }, props));\n}\n// Utils\nfunction generateClassNames(classNameGenerator, renderProps) {\n  const classNames = typeof classNameGenerator === 'function' ? classNameGenerator(renderProps) : classNameGenerator || [];\n  return typeof classNames === 'string' ? [classNames] : classNames;\n}\nclass ViewContainer extends BaseComponent {\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let renderProps = {\n      view: context.viewApi\n    };\n    return createElement(ContentContainer, Object.assign({}, props, {\n      elTag: props.elTag || 'div',\n      elClasses: [...buildViewClassNames(props.viewSpec), ...(props.elClasses || [])],\n      renderProps: renderProps,\n      classNameGenerator: options.viewClassNames,\n      generatorName: undefined,\n      didMount: options.viewDidMount,\n      willUnmount: options.viewWillUnmount\n    }), () => props.children);\n  }\n}\nfunction buildViewClassNames(viewSpec) {\n  return [`fc-${viewSpec.type}-view`, 'fc-view'];\n}\nfunction parseRange(input, dateEnv) {\n  let start = null;\n  let end = null;\n  if (input.start) {\n    start = dateEnv.createMarker(input.start);\n  }\n  if (input.end) {\n    end = dateEnv.createMarker(input.end);\n  }\n  if (!start && !end) {\n    return null;\n  }\n  if (start && end && end < start) {\n    return null;\n  }\n  return {\n    start,\n    end\n  };\n}\n// SIDE-EFFECT: will mutate ranges.\n// Will return a new array result.\nfunction invertRanges(ranges, constraintRange) {\n  let invertedRanges = [];\n  let {\n    start\n  } = constraintRange; // the end of the previous range. the start of the new range\n  let i;\n  let dateRange;\n  // ranges need to be in order. required for our date-walking algorithm\n  ranges.sort(compareRanges);\n  for (i = 0; i < ranges.length; i += 1) {\n    dateRange = ranges[i];\n    // add the span of time before the event (if there is any)\n    if (dateRange.start > start) {\n      // compare millisecond time (skip any ambig logic)\n      invertedRanges.push({\n        start,\n        end: dateRange.start\n      });\n    }\n    if (dateRange.end > start) {\n      start = dateRange.end;\n    }\n  }\n  // add the span of time after the last event (if there is any)\n  if (start < constraintRange.end) {\n    // compare millisecond time (skip any ambig logic)\n    invertedRanges.push({\n      start,\n      end: constraintRange.end\n    });\n  }\n  return invertedRanges;\n}\nfunction compareRanges(range0, range1) {\n  return range0.start.valueOf() - range1.start.valueOf(); // earlier ranges go first\n}\nfunction intersectRanges(range0, range1) {\n  let {\n    start,\n    end\n  } = range0;\n  let newRange = null;\n  if (range1.start !== null) {\n    if (start === null) {\n      start = range1.start;\n    } else {\n      start = new Date(Math.max(start.valueOf(), range1.start.valueOf()));\n    }\n  }\n  if (range1.end != null) {\n    if (end === null) {\n      end = range1.end;\n    } else {\n      end = new Date(Math.min(end.valueOf(), range1.end.valueOf()));\n    }\n  }\n  if (start === null || end === null || start < end) {\n    newRange = {\n      start,\n      end\n    };\n  }\n  return newRange;\n}\nfunction rangesEqual(range0, range1) {\n  return (range0.start === null ? null : range0.start.valueOf()) === (range1.start === null ? null : range1.start.valueOf()) && (range0.end === null ? null : range0.end.valueOf()) === (range1.end === null ? null : range1.end.valueOf());\n}\nfunction rangesIntersect(range0, range1) {\n  return (range0.end === null || range1.start === null || range0.end > range1.start) && (range0.start === null || range1.end === null || range0.start < range1.end);\n}\nfunction rangeContainsRange(outerRange, innerRange) {\n  return (outerRange.start === null || innerRange.start !== null && innerRange.start >= outerRange.start) && (outerRange.end === null || innerRange.end !== null && innerRange.end <= outerRange.end);\n}\nfunction rangeContainsMarker(range, date) {\n  return (range.start === null || date >= range.start) && (range.end === null || date < range.end);\n}\n// If the given date is not within the given range, move it inside.\n// (If it's past the end, make it one millisecond before the end).\nfunction constrainMarkerToRange(date, range) {\n  if (range.start != null && date < range.start) {\n    return range.start;\n  }\n  if (range.end != null && date >= range.end) {\n    return new Date(range.end.valueOf() - 1);\n  }\n  return date;\n}\n\n/* Date stuff that doesn't belong in datelib core\n----------------------------------------------------------------------------------------------------------------------*/\n// given a timed range, computes an all-day range that has the same exact duration,\n// but whose start time is aligned with the start of the day.\nfunction computeAlignedDayRange(timedRange) {\n  let dayCnt = Math.floor(diffDays(timedRange.start, timedRange.end)) || 1;\n  let start = startOfDay(timedRange.start);\n  let end = addDays(start, dayCnt);\n  return {\n    start,\n    end\n  };\n}\n// given a timed range, computes an all-day range based on how for the end date bleeds into the next day\n// TODO: give nextDayThreshold a default arg\nfunction computeVisibleDayRange(timedRange, nextDayThreshold = createDuration(0)) {\n  let startDay = null;\n  let endDay = null;\n  if (timedRange.end) {\n    endDay = startOfDay(timedRange.end);\n    let endTimeMS = timedRange.end.valueOf() - endDay.valueOf(); // # of milliseconds into `endDay`\n    // If the end time is actually inclusively part of the next day and is equal to or\n    // beyond the next day threshold, adjust the end to be the exclusive end of `endDay`.\n    // Otherwise, leaving it as inclusive will cause it to exclude `endDay`.\n    if (endTimeMS && endTimeMS >= asRoughMs(nextDayThreshold)) {\n      endDay = addDays(endDay, 1);\n    }\n  }\n  if (timedRange.start) {\n    startDay = startOfDay(timedRange.start); // the beginning of the day the range starts\n    // If end is within `startDay` but not past nextDayThreshold, assign the default duration of one day.\n    if (endDay && endDay <= startDay) {\n      endDay = addDays(startDay, 1);\n    }\n  }\n  return {\n    start: startDay,\n    end: endDay\n  };\n}\n// spans from one day into another?\nfunction isMultiDayRange(range) {\n  let visibleRange = computeVisibleDayRange(range);\n  return diffDays(visibleRange.start, visibleRange.end) > 1;\n}\nfunction diffDates(date0, date1, dateEnv, largeUnit) {\n  if (largeUnit === 'year') {\n    return createDuration(dateEnv.diffWholeYears(date0, date1), 'year');\n  }\n  if (largeUnit === 'month') {\n    return createDuration(dateEnv.diffWholeMonths(date0, date1), 'month');\n  }\n  return diffDayAndTime(date0, date1); // returns a duration\n}\nfunction reduceCurrentDate(currentDate, action) {\n  switch (action.type) {\n    case 'CHANGE_DATE':\n      return action.dateMarker;\n    default:\n      return currentDate;\n  }\n}\nfunction getInitialDate(options, dateEnv) {\n  let initialDateInput = options.initialDate;\n  // compute the initial ambig-timezone date\n  if (initialDateInput != null) {\n    return dateEnv.createMarker(initialDateInput);\n  }\n  return getNow(options.now, dateEnv); // getNow already returns unzoned\n}\nfunction getNow(nowInput, dateEnv) {\n  if (typeof nowInput === 'function') {\n    nowInput = nowInput();\n  }\n  if (nowInput == null) {\n    return dateEnv.createNowMarker();\n  }\n  return dateEnv.createMarker(nowInput);\n}\nclass DateProfileGenerator {\n  constructor(props) {\n    this.props = props;\n    this.nowDate = getNow(props.nowInput, props.dateEnv);\n    this.initHiddenDays();\n  }\n  /* Date Range Computation\n  ------------------------------------------------------------------------------------------------------------------*/\n  // Builds a structure with info about what the dates/ranges will be for the \"prev\" view.\n  buildPrev(currentDateProfile, currentDate, forceToValid) {\n    let {\n      dateEnv\n    } = this.props;\n    let prevDate = dateEnv.subtract(dateEnv.startOf(currentDate, currentDateProfile.currentRangeUnit),\n    // important for start-of-month\n    currentDateProfile.dateIncrement);\n    return this.build(prevDate, -1, forceToValid);\n  }\n  // Builds a structure with info about what the dates/ranges will be for the \"next\" view.\n  buildNext(currentDateProfile, currentDate, forceToValid) {\n    let {\n      dateEnv\n    } = this.props;\n    let nextDate = dateEnv.add(dateEnv.startOf(currentDate, currentDateProfile.currentRangeUnit),\n    // important for start-of-month\n    currentDateProfile.dateIncrement);\n    return this.build(nextDate, 1, forceToValid);\n  }\n  // Builds a structure holding dates/ranges for rendering around the given date.\n  // Optional direction param indicates whether the date is being incremented/decremented\n  // from its previous value. decremented = -1, incremented = 1 (default).\n  build(currentDate, direction, forceToValid = true) {\n    let {\n      props\n    } = this;\n    let validRange;\n    let currentInfo;\n    let isRangeAllDay;\n    let renderRange;\n    let activeRange;\n    let isValid;\n    validRange = this.buildValidRange();\n    validRange = this.trimHiddenDays(validRange);\n    if (forceToValid) {\n      currentDate = constrainMarkerToRange(currentDate, validRange);\n    }\n    currentInfo = this.buildCurrentRangeInfo(currentDate, direction);\n    isRangeAllDay = /^(year|month|week|day)$/.test(currentInfo.unit);\n    renderRange = this.buildRenderRange(this.trimHiddenDays(currentInfo.range), currentInfo.unit, isRangeAllDay);\n    renderRange = this.trimHiddenDays(renderRange);\n    activeRange = renderRange;\n    if (!props.showNonCurrentDates) {\n      activeRange = intersectRanges(activeRange, currentInfo.range);\n    }\n    activeRange = this.adjustActiveRange(activeRange);\n    activeRange = intersectRanges(activeRange, validRange); // might return null\n    // it's invalid if the originally requested date is not contained,\n    // or if the range is completely outside of the valid range.\n    isValid = rangesIntersect(currentInfo.range, validRange);\n    // HACK: constrain to render-range so `currentDate` is more useful to view rendering\n    if (!rangeContainsMarker(renderRange, currentDate)) {\n      currentDate = renderRange.start;\n    }\n    return {\n      currentDate,\n      // constraint for where prev/next operations can go and where events can be dragged/resized to.\n      // an object with optional start and end properties.\n      validRange,\n      // range the view is formally responsible for.\n      // for example, a month view might have 1st-31st, excluding padded dates\n      currentRange: currentInfo.range,\n      // name of largest unit being displayed, like \"month\" or \"week\"\n      currentRangeUnit: currentInfo.unit,\n      isRangeAllDay,\n      // dates that display events and accept drag-n-drop\n      // will be `null` if no dates accept events\n      activeRange,\n      // date range with a rendered skeleton\n      // includes not-active days that need some sort of DOM\n      renderRange,\n      // Duration object that denotes the first visible time of any given day\n      slotMinTime: props.slotMinTime,\n      // Duration object that denotes the exclusive visible end time of any given day\n      slotMaxTime: props.slotMaxTime,\n      isValid,\n      // how far the current date will move for a prev/next operation\n      dateIncrement: this.buildDateIncrement(currentInfo.duration)\n      // pass a fallback (might be null) ^\n    };\n  }\n  // Builds an object with optional start/end properties.\n  // Indicates the minimum/maximum dates to display.\n  // not responsible for trimming hidden days.\n  buildValidRange() {\n    let input = this.props.validRangeInput;\n    let simpleInput = typeof input === 'function' ? input.call(this.props.calendarApi, this.nowDate) : input;\n    return this.refineRange(simpleInput) || {\n      start: null,\n      end: null\n    }; // completely open-ended\n  }\n  // Builds a structure with info about the \"current\" range, the range that is\n  // highlighted as being the current month for example.\n  // See build() for a description of `direction`.\n  // Guaranteed to have `range` and `unit` properties. `duration` is optional.\n  buildCurrentRangeInfo(date, direction) {\n    let {\n      props\n    } = this;\n    let duration = null;\n    let unit = null;\n    let range = null;\n    let dayCount;\n    if (props.duration) {\n      duration = props.duration;\n      unit = props.durationUnit;\n      range = this.buildRangeFromDuration(date, direction, duration, unit);\n    } else if (dayCount = this.props.dayCount) {\n      unit = 'day';\n      range = this.buildRangeFromDayCount(date, direction, dayCount);\n    } else if (range = this.buildCustomVisibleRange(date)) {\n      unit = props.dateEnv.greatestWholeUnit(range.start, range.end).unit;\n    } else {\n      duration = this.getFallbackDuration();\n      unit = greatestDurationDenominator(duration).unit;\n      range = this.buildRangeFromDuration(date, direction, duration, unit);\n    }\n    return {\n      duration,\n      unit,\n      range\n    };\n  }\n  getFallbackDuration() {\n    return createDuration({\n      day: 1\n    });\n  }\n  // Returns a new activeRange to have time values (un-ambiguate)\n  // slotMinTime or slotMaxTime causes the range to expand.\n  adjustActiveRange(range) {\n    let {\n      dateEnv,\n      usesMinMaxTime,\n      slotMinTime,\n      slotMaxTime\n    } = this.props;\n    let {\n      start,\n      end\n    } = range;\n    if (usesMinMaxTime) {\n      // expand active range if slotMinTime is negative (why not when positive?)\n      if (asRoughDays(slotMinTime) < 0) {\n        start = startOfDay(start); // necessary?\n        start = dateEnv.add(start, slotMinTime);\n      }\n      // expand active range if slotMaxTime is beyond one day (why not when negative?)\n      if (asRoughDays(slotMaxTime) > 1) {\n        end = startOfDay(end); // necessary?\n        end = addDays(end, -1);\n        end = dateEnv.add(end, slotMaxTime);\n      }\n    }\n    return {\n      start,\n      end\n    };\n  }\n  // Builds the \"current\" range when it is specified as an explicit duration.\n  // `unit` is the already-computed greatestDurationDenominator unit of duration.\n  buildRangeFromDuration(date, direction, duration, unit) {\n    let {\n      dateEnv,\n      dateAlignment\n    } = this.props;\n    let start;\n    let end;\n    let res;\n    // compute what the alignment should be\n    if (!dateAlignment) {\n      let {\n        dateIncrement\n      } = this.props;\n      if (dateIncrement) {\n        // use the smaller of the two units\n        if (asRoughMs(dateIncrement) < asRoughMs(duration)) {\n          dateAlignment = greatestDurationDenominator(dateIncrement).unit;\n        } else {\n          dateAlignment = unit;\n        }\n      } else {\n        dateAlignment = unit;\n      }\n    }\n    // if the view displays a single day or smaller\n    if (asRoughDays(duration) <= 1) {\n      if (this.isHiddenDay(start)) {\n        start = this.skipHiddenDays(start, direction);\n        start = startOfDay(start);\n      }\n    }\n    function computeRes() {\n      start = dateEnv.startOf(date, dateAlignment);\n      end = dateEnv.add(start, duration);\n      res = {\n        start,\n        end\n      };\n    }\n    computeRes();\n    // if range is completely enveloped by hidden days, go past the hidden days\n    if (!this.trimHiddenDays(res)) {\n      date = this.skipHiddenDays(date, direction);\n      computeRes();\n    }\n    return res;\n  }\n  // Builds the \"current\" range when a dayCount is specified.\n  buildRangeFromDayCount(date, direction, dayCount) {\n    let {\n      dateEnv,\n      dateAlignment\n    } = this.props;\n    let runningCount = 0;\n    let start = date;\n    let end;\n    if (dateAlignment) {\n      start = dateEnv.startOf(start, dateAlignment);\n    }\n    start = startOfDay(start);\n    start = this.skipHiddenDays(start, direction);\n    end = start;\n    do {\n      end = addDays(end, 1);\n      if (!this.isHiddenDay(end)) {\n        runningCount += 1;\n      }\n    } while (runningCount < dayCount);\n    return {\n      start,\n      end\n    };\n  }\n  // Builds a normalized range object for the \"visible\" range,\n  // which is a way to define the currentRange and activeRange at the same time.\n  buildCustomVisibleRange(date) {\n    let {\n      props\n    } = this;\n    let input = props.visibleRangeInput;\n    let simpleInput = typeof input === 'function' ? input.call(props.calendarApi, props.dateEnv.toDate(date)) : input;\n    let range = this.refineRange(simpleInput);\n    if (range && (range.start == null || range.end == null)) {\n      return null;\n    }\n    return range;\n  }\n  // Computes the range that will represent the element/cells for *rendering*,\n  // but which may have voided days/times.\n  // not responsible for trimming hidden days.\n  buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay) {\n    return currentRange;\n  }\n  // Compute the duration value that should be added/substracted to the current date\n  // when a prev/next operation happens.\n  buildDateIncrement(fallback) {\n    let {\n      dateIncrement\n    } = this.props;\n    let customAlignment;\n    if (dateIncrement) {\n      return dateIncrement;\n    }\n    if (customAlignment = this.props.dateAlignment) {\n      return createDuration(1, customAlignment);\n    }\n    if (fallback) {\n      return fallback;\n    }\n    return createDuration({\n      days: 1\n    });\n  }\n  refineRange(rangeInput) {\n    if (rangeInput) {\n      let range = parseRange(rangeInput, this.props.dateEnv);\n      if (range) {\n        range = computeVisibleDayRange(range);\n      }\n      return range;\n    }\n    return null;\n  }\n  /* Hidden Days\n  ------------------------------------------------------------------------------------------------------------------*/\n  // Initializes internal variables related to calculating hidden days-of-week\n  initHiddenDays() {\n    let hiddenDays = this.props.hiddenDays || []; // array of day-of-week indices that are hidden\n    let isHiddenDayHash = []; // is the day-of-week hidden? (hash with day-of-week-index -> bool)\n    let dayCnt = 0;\n    let i;\n    if (this.props.weekends === false) {\n      hiddenDays.push(0, 6); // 0=sunday, 6=saturday\n    }\n    for (i = 0; i < 7; i += 1) {\n      if (!(isHiddenDayHash[i] = hiddenDays.indexOf(i) !== -1)) {\n        dayCnt += 1;\n      }\n    }\n    if (!dayCnt) {\n      throw new Error('invalid hiddenDays'); // all days were hidden? bad.\n    }\n    this.isHiddenDayHash = isHiddenDayHash;\n  }\n  // Remove days from the beginning and end of the range that are computed as hidden.\n  // If the whole range is trimmed off, returns null\n  trimHiddenDays(range) {\n    let {\n      start,\n      end\n    } = range;\n    if (start) {\n      start = this.skipHiddenDays(start);\n    }\n    if (end) {\n      end = this.skipHiddenDays(end, -1, true);\n    }\n    if (start == null || end == null || start < end) {\n      return {\n        start,\n        end\n      };\n    }\n    return null;\n  }\n  // Is the current day hidden?\n  // `day` is a day-of-week index (0-6), or a Date (used for UTC)\n  isHiddenDay(day) {\n    if (day instanceof Date) {\n      day = day.getUTCDay();\n    }\n    return this.isHiddenDayHash[day];\n  }\n  // Incrementing the current day until it is no longer a hidden day, returning a copy.\n  // DOES NOT CONSIDER validRange!\n  // If the initial value of `date` is not a hidden day, don't do anything.\n  // Pass `isExclusive` as `true` if you are dealing with an end date.\n  // `inc` defaults to `1` (increment one day forward each time)\n  skipHiddenDays(date, inc = 1, isExclusive = false) {\n    while (this.isHiddenDayHash[(date.getUTCDay() + (isExclusive ? inc : 0) + 7) % 7]) {\n      date = addDays(date, inc);\n    }\n    return date;\n  }\n}\nfunction createEventInstance(defId, range, forcedStartTzo, forcedEndTzo) {\n  return {\n    instanceId: guid(),\n    defId,\n    range,\n    forcedStartTzo: forcedStartTzo == null ? null : forcedStartTzo,\n    forcedEndTzo: forcedEndTzo == null ? null : forcedEndTzo\n  };\n}\nfunction parseRecurring(refined, defaultAllDay, dateEnv, recurringTypes) {\n  for (let i = 0; i < recurringTypes.length; i += 1) {\n    let parsed = recurringTypes[i].parse(refined, dateEnv);\n    if (parsed) {\n      let {\n        allDay\n      } = refined;\n      if (allDay == null) {\n        allDay = defaultAllDay;\n        if (allDay == null) {\n          allDay = parsed.allDayGuess;\n          if (allDay == null) {\n            allDay = false;\n          }\n        }\n      }\n      return {\n        allDay,\n        duration: parsed.duration,\n        typeData: parsed.typeData,\n        typeId: i\n      };\n    }\n  }\n  return null;\n}\nfunction expandRecurring(eventStore, framingRange, context) {\n  let {\n    dateEnv,\n    pluginHooks,\n    options\n  } = context;\n  let {\n    defs,\n    instances\n  } = eventStore;\n  // remove existing recurring instances\n  // TODO: bad. always expand events as a second step\n  instances = filterHash(instances, instance => !defs[instance.defId].recurringDef);\n  for (let defId in defs) {\n    let def = defs[defId];\n    if (def.recurringDef) {\n      let {\n        duration\n      } = def.recurringDef;\n      if (!duration) {\n        duration = def.allDay ? options.defaultAllDayEventDuration : options.defaultTimedEventDuration;\n      }\n      let starts = expandRecurringRanges(def, duration, framingRange, dateEnv, pluginHooks.recurringTypes);\n      for (let start of starts) {\n        let instance = createEventInstance(defId, {\n          start,\n          end: dateEnv.add(start, duration)\n        });\n        instances[instance.instanceId] = instance;\n      }\n    }\n  }\n  return {\n    defs,\n    instances\n  };\n}\n/*\nEvent MUST have a recurringDef\n*/\nfunction expandRecurringRanges(eventDef, duration, framingRange, dateEnv, recurringTypes) {\n  let typeDef = recurringTypes[eventDef.recurringDef.typeId];\n  let markers = typeDef.expand(eventDef.recurringDef.typeData, {\n    start: dateEnv.subtract(framingRange.start, duration),\n    end: framingRange.end\n  }, dateEnv);\n  // the recurrence plugins don't guarantee that all-day events are start-of-day, so we have to\n  if (eventDef.allDay) {\n    markers = markers.map(startOfDay);\n  }\n  return markers;\n}\nconst EVENT_NON_DATE_REFINERS = {\n  id: String,\n  groupId: String,\n  title: String,\n  url: String,\n  interactive: Boolean\n};\nconst EVENT_DATE_REFINERS = {\n  start: identity,\n  end: identity,\n  date: identity,\n  allDay: Boolean\n};\nconst EVENT_REFINERS = Object.assign(Object.assign(Object.assign({}, EVENT_NON_DATE_REFINERS), EVENT_DATE_REFINERS), {\n  extendedProps: identity\n});\nfunction parseEvent(raw, eventSource, context, allowOpenRange, refiners = buildEventRefiners(context), defIdMap, instanceIdMap) {\n  let {\n    refined,\n    extra\n  } = refineEventDef(raw, context, refiners);\n  let defaultAllDay = computeIsDefaultAllDay(eventSource, context);\n  let recurringRes = parseRecurring(refined, defaultAllDay, context.dateEnv, context.pluginHooks.recurringTypes);\n  if (recurringRes) {\n    let def = parseEventDef(refined, extra, eventSource ? eventSource.sourceId : '', recurringRes.allDay, Boolean(recurringRes.duration), context, defIdMap);\n    def.recurringDef = {\n      typeId: recurringRes.typeId,\n      typeData: recurringRes.typeData,\n      duration: recurringRes.duration\n    };\n    return {\n      def,\n      instance: null\n    };\n  }\n  let singleRes = parseSingle(refined, defaultAllDay, context, allowOpenRange);\n  if (singleRes) {\n    let def = parseEventDef(refined, extra, eventSource ? eventSource.sourceId : '', singleRes.allDay, singleRes.hasEnd, context, defIdMap);\n    let instance = createEventInstance(def.defId, singleRes.range, singleRes.forcedStartTzo, singleRes.forcedEndTzo);\n    if (instanceIdMap && def.publicId && instanceIdMap[def.publicId]) {\n      instance.instanceId = instanceIdMap[def.publicId];\n    }\n    return {\n      def,\n      instance\n    };\n  }\n  return null;\n}\nfunction refineEventDef(raw, context, refiners = buildEventRefiners(context)) {\n  return refineProps(raw, refiners);\n}\nfunction buildEventRefiners(context) {\n  return Object.assign(Object.assign(Object.assign({}, EVENT_UI_REFINERS), EVENT_REFINERS), context.pluginHooks.eventRefiners);\n}\n/*\nWill NOT populate extendedProps with the leftover properties.\nWill NOT populate date-related props.\n*/\nfunction parseEventDef(refined, extra, sourceId, allDay, hasEnd, context, defIdMap) {\n  let def = {\n    title: refined.title || '',\n    groupId: refined.groupId || '',\n    publicId: refined.id || '',\n    url: refined.url || '',\n    recurringDef: null,\n    defId: (defIdMap && refined.id ? defIdMap[refined.id] : '') || guid(),\n    sourceId,\n    allDay,\n    hasEnd,\n    interactive: refined.interactive,\n    ui: createEventUi(refined, context),\n    extendedProps: Object.assign(Object.assign({}, refined.extendedProps || {}), extra)\n  };\n  for (let memberAdder of context.pluginHooks.eventDefMemberAdders) {\n    Object.assign(def, memberAdder(refined));\n  }\n  // help out EventImpl from having user modify props\n  Object.freeze(def.ui.classNames);\n  Object.freeze(def.extendedProps);\n  return def;\n}\nfunction parseSingle(refined, defaultAllDay, context, allowOpenRange) {\n  let {\n    allDay\n  } = refined;\n  let startMeta;\n  let startMarker = null;\n  let hasEnd = false;\n  let endMeta;\n  let endMarker = null;\n  let startInput = refined.start != null ? refined.start : refined.date;\n  startMeta = context.dateEnv.createMarkerMeta(startInput);\n  if (startMeta) {\n    startMarker = startMeta.marker;\n  } else if (!allowOpenRange) {\n    return null;\n  }\n  if (refined.end != null) {\n    endMeta = context.dateEnv.createMarkerMeta(refined.end);\n  }\n  if (allDay == null) {\n    if (defaultAllDay != null) {\n      allDay = defaultAllDay;\n    } else {\n      // fall back to the date props LAST\n      allDay = (!startMeta || startMeta.isTimeUnspecified) && (!endMeta || endMeta.isTimeUnspecified);\n    }\n  }\n  if (allDay && startMarker) {\n    startMarker = startOfDay(startMarker);\n  }\n  if (endMeta) {\n    endMarker = endMeta.marker;\n    if (allDay) {\n      endMarker = startOfDay(endMarker);\n    }\n    if (startMarker && endMarker <= startMarker) {\n      endMarker = null;\n    }\n  }\n  if (endMarker) {\n    hasEnd = true;\n  } else if (!allowOpenRange) {\n    hasEnd = context.options.forceEventDuration || false;\n    endMarker = context.dateEnv.add(startMarker, allDay ? context.options.defaultAllDayEventDuration : context.options.defaultTimedEventDuration);\n  }\n  return {\n    allDay,\n    hasEnd,\n    range: {\n      start: startMarker,\n      end: endMarker\n    },\n    forcedStartTzo: startMeta ? startMeta.forcedTzo : null,\n    forcedEndTzo: endMeta ? endMeta.forcedTzo : null\n  };\n}\nfunction computeIsDefaultAllDay(eventSource, context) {\n  let res = null;\n  if (eventSource) {\n    res = eventSource.defaultAllDay;\n  }\n  if (res == null) {\n    res = context.options.defaultAllDay;\n  }\n  return res;\n}\nfunction parseEvents(rawEvents, eventSource, context, allowOpenRange, defIdMap, instanceIdMap) {\n  let eventStore = createEmptyEventStore();\n  let eventRefiners = buildEventRefiners(context);\n  for (let rawEvent of rawEvents) {\n    let tuple = parseEvent(rawEvent, eventSource, context, allowOpenRange, eventRefiners, defIdMap, instanceIdMap);\n    if (tuple) {\n      eventTupleToStore(tuple, eventStore);\n    }\n  }\n  return eventStore;\n}\nfunction eventTupleToStore(tuple, eventStore = createEmptyEventStore()) {\n  eventStore.defs[tuple.def.defId] = tuple.def;\n  if (tuple.instance) {\n    eventStore.instances[tuple.instance.instanceId] = tuple.instance;\n  }\n  return eventStore;\n}\n// retrieves events that have the same groupId as the instance specified by `instanceId`\n// or they are the same as the instance.\n// why might instanceId not be in the store? an event from another calendar?\nfunction getRelevantEvents(eventStore, instanceId) {\n  let instance = eventStore.instances[instanceId];\n  if (instance) {\n    let def = eventStore.defs[instance.defId];\n    // get events/instances with same group\n    let newStore = filterEventStoreDefs(eventStore, lookDef => isEventDefsGrouped(def, lookDef));\n    // add the original\n    // TODO: wish we could use eventTupleToStore or something like it\n    newStore.defs[def.defId] = def;\n    newStore.instances[instance.instanceId] = instance;\n    return newStore;\n  }\n  return createEmptyEventStore();\n}\nfunction isEventDefsGrouped(def0, def1) {\n  return Boolean(def0.groupId && def0.groupId === def1.groupId);\n}\nfunction createEmptyEventStore() {\n  return {\n    defs: {},\n    instances: {}\n  };\n}\nfunction mergeEventStores(store0, store1) {\n  return {\n    defs: Object.assign(Object.assign({}, store0.defs), store1.defs),\n    instances: Object.assign(Object.assign({}, store0.instances), store1.instances)\n  };\n}\nfunction filterEventStoreDefs(eventStore, filterFunc) {\n  let defs = filterHash(eventStore.defs, filterFunc);\n  let instances = filterHash(eventStore.instances, instance => defs[instance.defId] // still exists?\n  );\n  return {\n    defs,\n    instances\n  };\n}\nfunction excludeSubEventStore(master, sub) {\n  let {\n    defs,\n    instances\n  } = master;\n  let filteredDefs = {};\n  let filteredInstances = {};\n  for (let defId in defs) {\n    if (!sub.defs[defId]) {\n      // not explicitly excluded\n      filteredDefs[defId] = defs[defId];\n    }\n  }\n  for (let instanceId in instances) {\n    if (!sub.instances[instanceId] &&\n    // not explicitly excluded\n    filteredDefs[instances[instanceId].defId] // def wasn't filtered away\n    ) {\n      filteredInstances[instanceId] = instances[instanceId];\n    }\n  }\n  return {\n    defs: filteredDefs,\n    instances: filteredInstances\n  };\n}\nfunction normalizeConstraint(input, context) {\n  if (Array.isArray(input)) {\n    return parseEvents(input, null, context, true); // allowOpenRange=true\n  }\n  if (typeof input === 'object' && input) {\n    // non-null object\n    return parseEvents([input], null, context, true); // allowOpenRange=true\n  }\n  if (input != null) {\n    return String(input);\n  }\n  return null;\n}\nfunction parseClassNames(raw) {\n  if (Array.isArray(raw)) {\n    return raw;\n  }\n  if (typeof raw === 'string') {\n    return raw.split(/\\s+/);\n  }\n  return [];\n}\n\n// TODO: better called \"EventSettings\" or \"EventConfig\"\n// TODO: move this file into structs\n// TODO: separate constraint/overlap/allow, because selection uses only that, not other props\nconst EVENT_UI_REFINERS = {\n  display: String,\n  editable: Boolean,\n  startEditable: Boolean,\n  durationEditable: Boolean,\n  constraint: identity,\n  overlap: identity,\n  allow: identity,\n  className: parseClassNames,\n  classNames: parseClassNames,\n  color: String,\n  backgroundColor: String,\n  borderColor: String,\n  textColor: String\n};\nconst EMPTY_EVENT_UI = {\n  display: null,\n  startEditable: null,\n  durationEditable: null,\n  constraints: [],\n  overlap: null,\n  allows: [],\n  backgroundColor: '',\n  borderColor: '',\n  textColor: '',\n  classNames: []\n};\nfunction createEventUi(refined, context) {\n  let constraint = normalizeConstraint(refined.constraint, context);\n  return {\n    display: refined.display || null,\n    startEditable: refined.startEditable != null ? refined.startEditable : refined.editable,\n    durationEditable: refined.durationEditable != null ? refined.durationEditable : refined.editable,\n    constraints: constraint != null ? [constraint] : [],\n    overlap: refined.overlap != null ? refined.overlap : null,\n    allows: refined.allow != null ? [refined.allow] : [],\n    backgroundColor: refined.backgroundColor || refined.color || '',\n    borderColor: refined.borderColor || refined.color || '',\n    textColor: refined.textColor || '',\n    classNames: (refined.className || []).concat(refined.classNames || []) // join singular and plural\n  };\n}\n// TODO: prevent against problems with <2 args!\nfunction combineEventUis(uis) {\n  return uis.reduce(combineTwoEventUis, EMPTY_EVENT_UI);\n}\nfunction combineTwoEventUis(item0, item1) {\n  return {\n    display: item1.display != null ? item1.display : item0.display,\n    startEditable: item1.startEditable != null ? item1.startEditable : item0.startEditable,\n    durationEditable: item1.durationEditable != null ? item1.durationEditable : item0.durationEditable,\n    constraints: item0.constraints.concat(item1.constraints),\n    overlap: typeof item1.overlap === 'boolean' ? item1.overlap : item0.overlap,\n    allows: item0.allows.concat(item1.allows),\n    backgroundColor: item1.backgroundColor || item0.backgroundColor,\n    borderColor: item1.borderColor || item0.borderColor,\n    textColor: item1.textColor || item0.textColor,\n    classNames: item0.classNames.concat(item1.classNames)\n  };\n}\nconst EVENT_SOURCE_REFINERS = {\n  id: String,\n  defaultAllDay: Boolean,\n  url: String,\n  format: String,\n  events: identity,\n  eventDataTransform: identity,\n  // for any network-related sources\n  success: identity,\n  failure: identity\n};\nfunction parseEventSource(raw, context, refiners = buildEventSourceRefiners(context)) {\n  let rawObj;\n  if (typeof raw === 'string') {\n    rawObj = {\n      url: raw\n    };\n  } else if (typeof raw === 'function' || Array.isArray(raw)) {\n    rawObj = {\n      events: raw\n    };\n  } else if (typeof raw === 'object' && raw) {\n    // not null\n    rawObj = raw;\n  }\n  if (rawObj) {\n    let {\n      refined,\n      extra\n    } = refineProps(rawObj, refiners);\n    let metaRes = buildEventSourceMeta(refined, context);\n    if (metaRes) {\n      return {\n        _raw: raw,\n        isFetching: false,\n        latestFetchId: '',\n        fetchRange: null,\n        defaultAllDay: refined.defaultAllDay,\n        eventDataTransform: refined.eventDataTransform,\n        success: refined.success,\n        failure: refined.failure,\n        publicId: refined.id || '',\n        sourceId: guid(),\n        sourceDefId: metaRes.sourceDefId,\n        meta: metaRes.meta,\n        ui: createEventUi(refined, context),\n        extendedProps: extra\n      };\n    }\n  }\n  return null;\n}\nfunction buildEventSourceRefiners(context) {\n  return Object.assign(Object.assign(Object.assign({}, EVENT_UI_REFINERS), EVENT_SOURCE_REFINERS), context.pluginHooks.eventSourceRefiners);\n}\nfunction buildEventSourceMeta(raw, context) {\n  let defs = context.pluginHooks.eventSourceDefs;\n  for (let i = defs.length - 1; i >= 0; i -= 1) {\n    // later-added plugins take precedence\n    let def = defs[i];\n    let meta = def.parseMeta(raw);\n    if (meta) {\n      return {\n        sourceDefId: i,\n        meta\n      };\n    }\n  }\n  return null;\n}\nfunction reduceEventStore(eventStore, action, eventSources, dateProfile, context) {\n  switch (action.type) {\n    case 'RECEIVE_EVENTS':\n      // raw\n      return receiveRawEvents(eventStore, eventSources[action.sourceId], action.fetchId, action.fetchRange, action.rawEvents, context);\n    case 'RESET_RAW_EVENTS':\n      return resetRawEvents(eventStore, eventSources[action.sourceId], action.rawEvents, dateProfile.activeRange, context);\n    case 'ADD_EVENTS':\n      // already parsed, but not expanded\n      return addEvent(eventStore, action.eventStore,\n      // new ones\n      dateProfile ? dateProfile.activeRange : null, context);\n    case 'RESET_EVENTS':\n      return action.eventStore;\n    case 'MERGE_EVENTS':\n      // already parsed and expanded\n      return mergeEventStores(eventStore, action.eventStore);\n    case 'PREV': // TODO: how do we track all actions that affect dateProfile :(\n    case 'NEXT':\n    case 'CHANGE_DATE':\n    case 'CHANGE_VIEW_TYPE':\n      if (dateProfile) {\n        return expandRecurring(eventStore, dateProfile.activeRange, context);\n      }\n      return eventStore;\n    case 'REMOVE_EVENTS':\n      return excludeSubEventStore(eventStore, action.eventStore);\n    case 'REMOVE_EVENT_SOURCE':\n      return excludeEventsBySourceId(eventStore, action.sourceId);\n    case 'REMOVE_ALL_EVENT_SOURCES':\n      return filterEventStoreDefs(eventStore, eventDef => !eventDef.sourceId // only keep events with no source id\n      );\n    case 'REMOVE_ALL_EVENTS':\n      return createEmptyEventStore();\n    default:\n      return eventStore;\n  }\n}\nfunction receiveRawEvents(eventStore, eventSource, fetchId, fetchRange, rawEvents, context) {\n  if (eventSource &&\n  // not already removed\n  fetchId === eventSource.latestFetchId // TODO: wish this logic was always in event-sources\n  ) {\n    let subset = parseEvents(transformRawEvents(rawEvents, eventSource, context), eventSource, context);\n    if (fetchRange) {\n      subset = expandRecurring(subset, fetchRange, context);\n    }\n    return mergeEventStores(excludeEventsBySourceId(eventStore, eventSource.sourceId), subset);\n  }\n  return eventStore;\n}\nfunction resetRawEvents(existingEventStore, eventSource, rawEvents, activeRange, context) {\n  const {\n    defIdMap,\n    instanceIdMap\n  } = buildPublicIdMaps(existingEventStore);\n  let newEventStore = parseEvents(transformRawEvents(rawEvents, eventSource, context), eventSource, context, false, defIdMap, instanceIdMap);\n  return expandRecurring(newEventStore, activeRange, context);\n}\nfunction transformRawEvents(rawEvents, eventSource, context) {\n  let calEachTransform = context.options.eventDataTransform;\n  let sourceEachTransform = eventSource ? eventSource.eventDataTransform : null;\n  if (sourceEachTransform) {\n    rawEvents = transformEachRawEvent(rawEvents, sourceEachTransform);\n  }\n  if (calEachTransform) {\n    rawEvents = transformEachRawEvent(rawEvents, calEachTransform);\n  }\n  return rawEvents;\n}\nfunction transformEachRawEvent(rawEvents, func) {\n  let refinedEvents;\n  if (!func) {\n    refinedEvents = rawEvents;\n  } else {\n    refinedEvents = [];\n    for (let rawEvent of rawEvents) {\n      let refinedEvent = func(rawEvent);\n      if (refinedEvent) {\n        refinedEvents.push(refinedEvent);\n      } else if (refinedEvent == null) {\n        refinedEvents.push(rawEvent);\n      } // if a different falsy value, do nothing\n    }\n  }\n  return refinedEvents;\n}\nfunction addEvent(eventStore, subset, expandRange, context) {\n  if (expandRange) {\n    subset = expandRecurring(subset, expandRange, context);\n  }\n  return mergeEventStores(eventStore, subset);\n}\nfunction rezoneEventStoreDates(eventStore, oldDateEnv, newDateEnv) {\n  let {\n    defs\n  } = eventStore;\n  let instances = mapHash(eventStore.instances, instance => {\n    let def = defs[instance.defId];\n    if (def.allDay) {\n      return instance; // isn't dependent on timezone\n    }\n    return Object.assign(Object.assign({}, instance), {\n      range: {\n        start: newDateEnv.createMarker(oldDateEnv.toDate(instance.range.start, instance.forcedStartTzo)),\n        end: newDateEnv.createMarker(oldDateEnv.toDate(instance.range.end, instance.forcedEndTzo))\n      },\n      forcedStartTzo: newDateEnv.canComputeOffset ? null : instance.forcedStartTzo,\n      forcedEndTzo: newDateEnv.canComputeOffset ? null : instance.forcedEndTzo\n    });\n  });\n  return {\n    defs,\n    instances\n  };\n}\nfunction excludeEventsBySourceId(eventStore, sourceId) {\n  return filterEventStoreDefs(eventStore, eventDef => eventDef.sourceId !== sourceId);\n}\n// QUESTION: why not just return instances? do a general object-property-exclusion util\nfunction excludeInstances(eventStore, removals) {\n  return {\n    defs: eventStore.defs,\n    instances: filterHash(eventStore.instances, instance => !removals[instance.instanceId])\n  };\n}\nfunction buildPublicIdMaps(eventStore) {\n  const {\n    defs,\n    instances\n  } = eventStore;\n  const defIdMap = {};\n  const instanceIdMap = {};\n  for (let defId in defs) {\n    const def = defs[defId];\n    const {\n      publicId\n    } = def;\n    if (publicId) {\n      defIdMap[publicId] = defId;\n    }\n  }\n  for (let instanceId in instances) {\n    const instance = instances[instanceId];\n    const def = defs[instance.defId];\n    const {\n      publicId\n    } = def;\n    if (publicId) {\n      instanceIdMap[publicId] = instanceId;\n    }\n  }\n  return {\n    defIdMap,\n    instanceIdMap\n  };\n}\nclass Emitter {\n  constructor() {\n    this.handlers = {};\n    this.thisContext = null;\n  }\n  setThisContext(thisContext) {\n    this.thisContext = thisContext;\n  }\n  setOptions(options) {\n    this.options = options;\n  }\n  on(type, handler) {\n    addToHash(this.handlers, type, handler);\n  }\n  off(type, handler) {\n    removeFromHash(this.handlers, type, handler);\n  }\n  trigger(type, ...args) {\n    let attachedHandlers = this.handlers[type] || [];\n    let optionHandler = this.options && this.options[type];\n    let handlers = [].concat(optionHandler || [], attachedHandlers);\n    for (let handler of handlers) {\n      handler.apply(this.thisContext, args);\n    }\n  }\n  hasHandlers(type) {\n    return Boolean(this.handlers[type] && this.handlers[type].length || this.options && this.options[type]);\n  }\n}\nfunction addToHash(hash, type, handler) {\n  (hash[type] || (hash[type] = [])).push(handler);\n}\nfunction removeFromHash(hash, type, handler) {\n  if (handler) {\n    if (hash[type]) {\n      hash[type] = hash[type].filter(func => func !== handler);\n    }\n  } else {\n    delete hash[type]; // remove all handler funcs for this type\n  }\n}\nconst DEF_DEFAULTS = {\n  startTime: '09:00',\n  endTime: '17:00',\n  daysOfWeek: [1, 2, 3, 4, 5],\n  display: 'inverse-background',\n  classNames: 'fc-non-business',\n  groupId: '_businessHours' // so multiple defs get grouped\n};\n/*\nTODO: pass around as EventDefHash!!!\n*/\nfunction parseBusinessHours(input, context) {\n  return parseEvents(refineInputs(input), null, context);\n}\nfunction refineInputs(input) {\n  let rawDefs;\n  if (input === true) {\n    rawDefs = [{}]; // will get DEF_DEFAULTS verbatim\n  } else if (Array.isArray(input)) {\n    // if specifying an array, every sub-definition NEEDS a day-of-week\n    rawDefs = input.filter(rawDef => rawDef.daysOfWeek);\n  } else if (typeof input === 'object' && input) {\n    // non-null object\n    rawDefs = [input];\n  } else {\n    // is probably false\n    rawDefs = [];\n  }\n  rawDefs = rawDefs.map(rawDef => Object.assign(Object.assign({}, DEF_DEFAULTS), rawDef));\n  return rawDefs;\n}\nfunction triggerDateSelect(selection, pev, context) {\n  context.emitter.trigger('select', Object.assign(Object.assign({}, buildDateSpanApiWithContext(selection, context)), {\n    jsEvent: pev ? pev.origEvent : null,\n    view: context.viewApi || context.calendarApi.view\n  }));\n}\nfunction triggerDateUnselect(pev, context) {\n  context.emitter.trigger('unselect', {\n    jsEvent: pev ? pev.origEvent : null,\n    view: context.viewApi || context.calendarApi.view\n  });\n}\nfunction buildDateSpanApiWithContext(dateSpan, context) {\n  let props = {};\n  for (let transform of context.pluginHooks.dateSpanTransforms) {\n    Object.assign(props, transform(dateSpan, context));\n  }\n  Object.assign(props, buildDateSpanApi(dateSpan, context.dateEnv));\n  return props;\n}\n// Given an event's allDay status and start date, return what its fallback end date should be.\n// TODO: rename to computeDefaultEventEnd\nfunction getDefaultEventEnd(allDay, marker, context) {\n  let {\n    dateEnv,\n    options\n  } = context;\n  let end = marker;\n  if (allDay) {\n    end = startOfDay(end);\n    end = dateEnv.add(end, options.defaultAllDayEventDuration);\n  } else {\n    end = dateEnv.add(end, options.defaultTimedEventDuration);\n  }\n  return end;\n}\n\n// applies the mutation to ALL defs/instances within the event store\nfunction applyMutationToEventStore(eventStore, eventConfigBase, mutation, context) {\n  let eventConfigs = compileEventUis(eventStore.defs, eventConfigBase);\n  let dest = createEmptyEventStore();\n  for (let defId in eventStore.defs) {\n    let def = eventStore.defs[defId];\n    dest.defs[defId] = applyMutationToEventDef(def, eventConfigs[defId], mutation, context);\n  }\n  for (let instanceId in eventStore.instances) {\n    let instance = eventStore.instances[instanceId];\n    let def = dest.defs[instance.defId]; // important to grab the newly modified def\n    dest.instances[instanceId] = applyMutationToEventInstance(instance, def, eventConfigs[instance.defId], mutation, context);\n  }\n  return dest;\n}\nfunction applyMutationToEventDef(eventDef, eventConfig, mutation, context) {\n  let standardProps = mutation.standardProps || {};\n  // if hasEnd has not been specified, guess a good value based on deltas.\n  // if duration will change, there's no way the default duration will persist,\n  // and thus, we need to mark the event as having a real end\n  if (standardProps.hasEnd == null && eventConfig.durationEditable && (mutation.startDelta || mutation.endDelta)) {\n    standardProps.hasEnd = true; // TODO: is this mutation okay?\n  }\n  let copy = Object.assign(Object.assign(Object.assign({}, eventDef), standardProps), {\n    ui: Object.assign(Object.assign({}, eventDef.ui), standardProps.ui)\n  });\n  if (mutation.extendedProps) {\n    copy.extendedProps = Object.assign(Object.assign({}, copy.extendedProps), mutation.extendedProps);\n  }\n  for (let applier of context.pluginHooks.eventDefMutationAppliers) {\n    applier(copy, mutation, context);\n  }\n  if (!copy.hasEnd && context.options.forceEventDuration) {\n    copy.hasEnd = true;\n  }\n  return copy;\n}\nfunction applyMutationToEventInstance(eventInstance, eventDef,\n// must first be modified by applyMutationToEventDef\neventConfig, mutation, context) {\n  let {\n    dateEnv\n  } = context;\n  let forceAllDay = mutation.standardProps && mutation.standardProps.allDay === true;\n  let clearEnd = mutation.standardProps && mutation.standardProps.hasEnd === false;\n  let copy = Object.assign({}, eventInstance);\n  if (forceAllDay) {\n    copy.range = computeAlignedDayRange(copy.range);\n  }\n  if (mutation.datesDelta && eventConfig.startEditable) {\n    copy.range = {\n      start: dateEnv.add(copy.range.start, mutation.datesDelta),\n      end: dateEnv.add(copy.range.end, mutation.datesDelta)\n    };\n  }\n  if (mutation.startDelta && eventConfig.durationEditable) {\n    copy.range = {\n      start: dateEnv.add(copy.range.start, mutation.startDelta),\n      end: copy.range.end\n    };\n  }\n  if (mutation.endDelta && eventConfig.durationEditable) {\n    copy.range = {\n      start: copy.range.start,\n      end: dateEnv.add(copy.range.end, mutation.endDelta)\n    };\n  }\n  if (clearEnd) {\n    copy.range = {\n      start: copy.range.start,\n      end: getDefaultEventEnd(eventDef.allDay, copy.range.start, context)\n    };\n  }\n  // in case event was all-day but the supplied deltas were not\n  // better util for this?\n  if (eventDef.allDay) {\n    copy.range = {\n      start: startOfDay(copy.range.start),\n      end: startOfDay(copy.range.end)\n    };\n  }\n  // handle invalid durations\n  if (copy.range.end < copy.range.start) {\n    copy.range.end = getDefaultEventEnd(eventDef.allDay, copy.range.start, context);\n  }\n  return copy;\n}\nclass EventSourceImpl {\n  constructor(context, internalEventSource) {\n    this.context = context;\n    this.internalEventSource = internalEventSource;\n  }\n  remove() {\n    this.context.dispatch({\n      type: 'REMOVE_EVENT_SOURCE',\n      sourceId: this.internalEventSource.sourceId\n    });\n  }\n  refetch() {\n    this.context.dispatch({\n      type: 'FETCH_EVENT_SOURCES',\n      sourceIds: [this.internalEventSource.sourceId],\n      isRefetch: true\n    });\n  }\n  get id() {\n    return this.internalEventSource.publicId;\n  }\n  get url() {\n    return this.internalEventSource.meta.url;\n  }\n  get format() {\n    return this.internalEventSource.meta.format; // TODO: bad. not guaranteed\n  }\n}\nclass EventImpl {\n  // instance will be null if expressing a recurring event that has no current instances,\n  // OR if trying to validate an incoming external event that has no dates assigned\n  constructor(context, def, instance) {\n    this._context = context;\n    this._def = def;\n    this._instance = instance || null;\n  }\n  /*\n  TODO: make event struct more responsible for this\n  */\n  setProp(name, val) {\n    if (name in EVENT_DATE_REFINERS) {\n      console.warn('Could not set date-related prop \\'name\\'. Use one of the date-related methods instead.');\n      // TODO: make proper aliasing system?\n    } else if (name === 'id') {\n      val = EVENT_NON_DATE_REFINERS[name](val);\n      this.mutate({\n        standardProps: {\n          publicId: val\n        } // hardcoded internal name\n      });\n    } else if (name in EVENT_NON_DATE_REFINERS) {\n      val = EVENT_NON_DATE_REFINERS[name](val);\n      this.mutate({\n        standardProps: {\n          [name]: val\n        }\n      });\n    } else if (name in EVENT_UI_REFINERS) {\n      let ui = EVENT_UI_REFINERS[name](val);\n      if (name === 'color') {\n        ui = {\n          backgroundColor: val,\n          borderColor: val\n        };\n      } else if (name === 'editable') {\n        ui = {\n          startEditable: val,\n          durationEditable: val\n        };\n      } else {\n        ui = {\n          [name]: val\n        };\n      }\n      this.mutate({\n        standardProps: {\n          ui\n        }\n      });\n    } else {\n      console.warn(`Could not set prop '${name}'. Use setExtendedProp instead.`);\n    }\n  }\n  setExtendedProp(name, val) {\n    this.mutate({\n      extendedProps: {\n        [name]: val\n      }\n    });\n  }\n  setStart(startInput, options = {}) {\n    let {\n      dateEnv\n    } = this._context;\n    let start = dateEnv.createMarker(startInput);\n    if (start && this._instance) {\n      // TODO: warning if parsed bad\n      let instanceRange = this._instance.range;\n      let startDelta = diffDates(instanceRange.start, start, dateEnv, options.granularity); // what if parsed bad!?\n      if (options.maintainDuration) {\n        this.mutate({\n          datesDelta: startDelta\n        });\n      } else {\n        this.mutate({\n          startDelta\n        });\n      }\n    }\n  }\n  setEnd(endInput, options = {}) {\n    let {\n      dateEnv\n    } = this._context;\n    let end;\n    if (endInput != null) {\n      end = dateEnv.createMarker(endInput);\n      if (!end) {\n        return; // TODO: warning if parsed bad\n      }\n    }\n    if (this._instance) {\n      if (end) {\n        let endDelta = diffDates(this._instance.range.end, end, dateEnv, options.granularity);\n        this.mutate({\n          endDelta\n        });\n      } else {\n        this.mutate({\n          standardProps: {\n            hasEnd: false\n          }\n        });\n      }\n    }\n  }\n  setDates(startInput, endInput, options = {}) {\n    let {\n      dateEnv\n    } = this._context;\n    let standardProps = {\n      allDay: options.allDay\n    };\n    let start = dateEnv.createMarker(startInput);\n    let end;\n    if (!start) {\n      return; // TODO: warning if parsed bad\n    }\n    if (endInput != null) {\n      end = dateEnv.createMarker(endInput);\n      if (!end) {\n        // TODO: warning if parsed bad\n        return;\n      }\n    }\n    if (this._instance) {\n      let instanceRange = this._instance.range;\n      // when computing the diff for an event being converted to all-day,\n      // compute diff off of the all-day values the way event-mutation does.\n      if (options.allDay === true) {\n        instanceRange = computeAlignedDayRange(instanceRange);\n      }\n      let startDelta = diffDates(instanceRange.start, start, dateEnv, options.granularity);\n      if (end) {\n        let endDelta = diffDates(instanceRange.end, end, dateEnv, options.granularity);\n        if (durationsEqual(startDelta, endDelta)) {\n          this.mutate({\n            datesDelta: startDelta,\n            standardProps\n          });\n        } else {\n          this.mutate({\n            startDelta,\n            endDelta,\n            standardProps\n          });\n        }\n      } else {\n        // means \"clear the end\"\n        standardProps.hasEnd = false;\n        this.mutate({\n          datesDelta: startDelta,\n          standardProps\n        });\n      }\n    }\n  }\n  moveStart(deltaInput) {\n    let delta = createDuration(deltaInput);\n    if (delta) {\n      // TODO: warning if parsed bad\n      this.mutate({\n        startDelta: delta\n      });\n    }\n  }\n  moveEnd(deltaInput) {\n    let delta = createDuration(deltaInput);\n    if (delta) {\n      // TODO: warning if parsed bad\n      this.mutate({\n        endDelta: delta\n      });\n    }\n  }\n  moveDates(deltaInput) {\n    let delta = createDuration(deltaInput);\n    if (delta) {\n      // TODO: warning if parsed bad\n      this.mutate({\n        datesDelta: delta\n      });\n    }\n  }\n  setAllDay(allDay, options = {}) {\n    let standardProps = {\n      allDay\n    };\n    let {\n      maintainDuration\n    } = options;\n    if (maintainDuration == null) {\n      maintainDuration = this._context.options.allDayMaintainDuration;\n    }\n    if (this._def.allDay !== allDay) {\n      standardProps.hasEnd = maintainDuration;\n    }\n    this.mutate({\n      standardProps\n    });\n  }\n  formatRange(formatInput) {\n    let {\n      dateEnv\n    } = this._context;\n    let instance = this._instance;\n    let formatter = createFormatter(formatInput);\n    if (this._def.hasEnd) {\n      return dateEnv.formatRange(instance.range.start, instance.range.end, formatter, {\n        forcedStartTzo: instance.forcedStartTzo,\n        forcedEndTzo: instance.forcedEndTzo\n      });\n    }\n    return dateEnv.format(instance.range.start, formatter, {\n      forcedTzo: instance.forcedStartTzo\n    });\n  }\n  mutate(mutation) {\n    let instance = this._instance;\n    if (instance) {\n      let def = this._def;\n      let context = this._context;\n      let {\n        eventStore\n      } = context.getCurrentData();\n      let relevantEvents = getRelevantEvents(eventStore, instance.instanceId);\n      let eventConfigBase = {\n        '': {\n          display: '',\n          startEditable: true,\n          durationEditable: true,\n          constraints: [],\n          overlap: null,\n          allows: [],\n          backgroundColor: '',\n          borderColor: '',\n          textColor: '',\n          classNames: []\n        }\n      };\n      relevantEvents = applyMutationToEventStore(relevantEvents, eventConfigBase, mutation, context);\n      let oldEvent = new EventImpl(context, def, instance); // snapshot\n      this._def = relevantEvents.defs[def.defId];\n      this._instance = relevantEvents.instances[instance.instanceId];\n      context.dispatch({\n        type: 'MERGE_EVENTS',\n        eventStore: relevantEvents\n      });\n      context.emitter.trigger('eventChange', {\n        oldEvent,\n        event: this,\n        relatedEvents: buildEventApis(relevantEvents, context, instance),\n        revert() {\n          context.dispatch({\n            type: 'RESET_EVENTS',\n            eventStore // the ORIGINAL store\n          });\n        }\n      });\n    }\n  }\n  remove() {\n    let context = this._context;\n    let asStore = eventApiToStore(this);\n    context.dispatch({\n      type: 'REMOVE_EVENTS',\n      eventStore: asStore\n    });\n    context.emitter.trigger('eventRemove', {\n      event: this,\n      relatedEvents: [],\n      revert() {\n        context.dispatch({\n          type: 'MERGE_EVENTS',\n          eventStore: asStore\n        });\n      }\n    });\n  }\n  get source() {\n    let {\n      sourceId\n    } = this._def;\n    if (sourceId) {\n      return new EventSourceImpl(this._context, this._context.getCurrentData().eventSources[sourceId]);\n    }\n    return null;\n  }\n  get start() {\n    return this._instance ? this._context.dateEnv.toDate(this._instance.range.start) : null;\n  }\n  get end() {\n    return this._instance && this._def.hasEnd ? this._context.dateEnv.toDate(this._instance.range.end) : null;\n  }\n  get startStr() {\n    let instance = this._instance;\n    if (instance) {\n      return this._context.dateEnv.formatIso(instance.range.start, {\n        omitTime: this._def.allDay,\n        forcedTzo: instance.forcedStartTzo\n      });\n    }\n    return '';\n  }\n  get endStr() {\n    let instance = this._instance;\n    if (instance && this._def.hasEnd) {\n      return this._context.dateEnv.formatIso(instance.range.end, {\n        omitTime: this._def.allDay,\n        forcedTzo: instance.forcedEndTzo\n      });\n    }\n    return '';\n  }\n  // computable props that all access the def\n  // TODO: find a TypeScript-compatible way to do this at scale\n  get id() {\n    return this._def.publicId;\n  }\n  get groupId() {\n    return this._def.groupId;\n  }\n  get allDay() {\n    return this._def.allDay;\n  }\n  get title() {\n    return this._def.title;\n  }\n  get url() {\n    return this._def.url;\n  }\n  get display() {\n    return this._def.ui.display || 'auto';\n  } // bad. just normalize the type earlier\n  get startEditable() {\n    return this._def.ui.startEditable;\n  }\n  get durationEditable() {\n    return this._def.ui.durationEditable;\n  }\n  get constraint() {\n    return this._def.ui.constraints[0] || null;\n  }\n  get overlap() {\n    return this._def.ui.overlap;\n  }\n  get allow() {\n    return this._def.ui.allows[0] || null;\n  }\n  get backgroundColor() {\n    return this._def.ui.backgroundColor;\n  }\n  get borderColor() {\n    return this._def.ui.borderColor;\n  }\n  get textColor() {\n    return this._def.ui.textColor;\n  }\n  // NOTE: user can't modify these because Object.freeze was called in event-def parsing\n  get classNames() {\n    return this._def.ui.classNames;\n  }\n  get extendedProps() {\n    return this._def.extendedProps;\n  }\n  toPlainObject(settings = {}) {\n    let def = this._def;\n    let {\n      ui\n    } = def;\n    let {\n      startStr,\n      endStr\n    } = this;\n    let res = {\n      allDay: def.allDay\n    };\n    if (def.title) {\n      res.title = def.title;\n    }\n    if (startStr) {\n      res.start = startStr;\n    }\n    if (endStr) {\n      res.end = endStr;\n    }\n    if (def.publicId) {\n      res.id = def.publicId;\n    }\n    if (def.groupId) {\n      res.groupId = def.groupId;\n    }\n    if (def.url) {\n      res.url = def.url;\n    }\n    if (ui.display && ui.display !== 'auto') {\n      res.display = ui.display;\n    }\n    // TODO: what about recurring-event properties???\n    // TODO: include startEditable/durationEditable/constraint/overlap/allow\n    if (settings.collapseColor && ui.backgroundColor && ui.backgroundColor === ui.borderColor) {\n      res.color = ui.backgroundColor;\n    } else {\n      if (ui.backgroundColor) {\n        res.backgroundColor = ui.backgroundColor;\n      }\n      if (ui.borderColor) {\n        res.borderColor = ui.borderColor;\n      }\n    }\n    if (ui.textColor) {\n      res.textColor = ui.textColor;\n    }\n    if (ui.classNames.length) {\n      res.classNames = ui.classNames;\n    }\n    if (Object.keys(def.extendedProps).length) {\n      if (settings.collapseExtendedProps) {\n        Object.assign(res, def.extendedProps);\n      } else {\n        res.extendedProps = def.extendedProps;\n      }\n    }\n    return res;\n  }\n  toJSON() {\n    return this.toPlainObject();\n  }\n}\nfunction eventApiToStore(eventApi) {\n  let def = eventApi._def;\n  let instance = eventApi._instance;\n  return {\n    defs: {\n      [def.defId]: def\n    },\n    instances: instance ? {\n      [instance.instanceId]: instance\n    } : {}\n  };\n}\nfunction buildEventApis(eventStore, context, excludeInstance) {\n  let {\n    defs,\n    instances\n  } = eventStore;\n  let eventApis = [];\n  let excludeInstanceId = excludeInstance ? excludeInstance.instanceId : '';\n  for (let id in instances) {\n    let instance = instances[id];\n    let def = defs[instance.defId];\n    if (instance.instanceId !== excludeInstanceId) {\n      eventApis.push(new EventImpl(context, def, instance));\n    }\n  }\n  return eventApis;\n}\n\n/*\nSpecifying nextDayThreshold signals that all-day ranges should be sliced.\n*/\nfunction sliceEventStore(eventStore, eventUiBases, framingRange, nextDayThreshold) {\n  let inverseBgByGroupId = {};\n  let inverseBgByDefId = {};\n  let defByGroupId = {};\n  let bgRanges = [];\n  let fgRanges = [];\n  let eventUis = compileEventUis(eventStore.defs, eventUiBases);\n  for (let defId in eventStore.defs) {\n    let def = eventStore.defs[defId];\n    let ui = eventUis[def.defId];\n    if (ui.display === 'inverse-background') {\n      if (def.groupId) {\n        inverseBgByGroupId[def.groupId] = [];\n        if (!defByGroupId[def.groupId]) {\n          defByGroupId[def.groupId] = def;\n        }\n      } else {\n        inverseBgByDefId[defId] = [];\n      }\n    }\n  }\n  for (let instanceId in eventStore.instances) {\n    let instance = eventStore.instances[instanceId];\n    let def = eventStore.defs[instance.defId];\n    let ui = eventUis[def.defId];\n    let origRange = instance.range;\n    let normalRange = !def.allDay && nextDayThreshold ? computeVisibleDayRange(origRange, nextDayThreshold) : origRange;\n    let slicedRange = intersectRanges(normalRange, framingRange);\n    if (slicedRange) {\n      if (ui.display === 'inverse-background') {\n        if (def.groupId) {\n          inverseBgByGroupId[def.groupId].push(slicedRange);\n        } else {\n          inverseBgByDefId[instance.defId].push(slicedRange);\n        }\n      } else if (ui.display !== 'none') {\n        (ui.display === 'background' ? bgRanges : fgRanges).push({\n          def,\n          ui,\n          instance,\n          range: slicedRange,\n          isStart: normalRange.start && normalRange.start.valueOf() === slicedRange.start.valueOf(),\n          isEnd: normalRange.end && normalRange.end.valueOf() === slicedRange.end.valueOf()\n        });\n      }\n    }\n  }\n  for (let groupId in inverseBgByGroupId) {\n    // BY GROUP\n    let ranges = inverseBgByGroupId[groupId];\n    let invertedRanges = invertRanges(ranges, framingRange);\n    for (let invertedRange of invertedRanges) {\n      let def = defByGroupId[groupId];\n      let ui = eventUis[def.defId];\n      bgRanges.push({\n        def,\n        ui,\n        instance: null,\n        range: invertedRange,\n        isStart: false,\n        isEnd: false\n      });\n    }\n  }\n  for (let defId in inverseBgByDefId) {\n    let ranges = inverseBgByDefId[defId];\n    let invertedRanges = invertRanges(ranges, framingRange);\n    for (let invertedRange of invertedRanges) {\n      bgRanges.push({\n        def: eventStore.defs[defId],\n        ui: eventUis[defId],\n        instance: null,\n        range: invertedRange,\n        isStart: false,\n        isEnd: false\n      });\n    }\n  }\n  return {\n    bg: bgRanges,\n    fg: fgRanges\n  };\n}\nfunction hasBgRendering(def) {\n  return def.ui.display === 'background' || def.ui.display === 'inverse-background';\n}\nfunction setElSeg(el, seg) {\n  el.fcSeg = seg;\n}\nfunction getElSeg(el) {\n  return el.fcSeg || el.parentNode.fcSeg ||\n  // for the harness\n  null;\n}\n// event ui computation\nfunction compileEventUis(eventDefs, eventUiBases) {\n  return mapHash(eventDefs, eventDef => compileEventUi(eventDef, eventUiBases));\n}\nfunction compileEventUi(eventDef, eventUiBases) {\n  let uis = [];\n  if (eventUiBases['']) {\n    uis.push(eventUiBases['']);\n  }\n  if (eventUiBases[eventDef.defId]) {\n    uis.push(eventUiBases[eventDef.defId]);\n  }\n  uis.push(eventDef.ui);\n  return combineEventUis(uis);\n}\nfunction sortEventSegs(segs, eventOrderSpecs) {\n  let objs = segs.map(buildSegCompareObj);\n  objs.sort((obj0, obj1) => compareByFieldSpecs(obj0, obj1, eventOrderSpecs));\n  return objs.map(c => c._seg);\n}\n// returns a object with all primitive props that can be compared\nfunction buildSegCompareObj(seg) {\n  let {\n    eventRange\n  } = seg;\n  let eventDef = eventRange.def;\n  let range = eventRange.instance ? eventRange.instance.range : eventRange.range;\n  let start = range.start ? range.start.valueOf() : 0; // TODO: better support for open-range events\n  let end = range.end ? range.end.valueOf() : 0; // \"\n  return Object.assign(Object.assign(Object.assign({}, eventDef.extendedProps), eventDef), {\n    id: eventDef.publicId,\n    start,\n    end,\n    duration: end - start,\n    allDay: Number(eventDef.allDay),\n    _seg: seg\n  });\n}\nfunction computeSegDraggable(seg, context) {\n  let {\n    pluginHooks\n  } = context;\n  let transformers = pluginHooks.isDraggableTransformers;\n  let {\n    def,\n    ui\n  } = seg.eventRange;\n  let val = ui.startEditable;\n  for (let transformer of transformers) {\n    val = transformer(val, def, ui, context);\n  }\n  return val;\n}\nfunction computeSegStartResizable(seg, context) {\n  return seg.isStart && seg.eventRange.ui.durationEditable && context.options.eventResizableFromStart;\n}\nfunction computeSegEndResizable(seg, context) {\n  return seg.isEnd && seg.eventRange.ui.durationEditable;\n}\nfunction buildSegTimeText(seg, timeFormat, context, defaultDisplayEventTime,\n// defaults to true\ndefaultDisplayEventEnd,\n// defaults to true\nstartOverride, endOverride) {\n  let {\n    dateEnv,\n    options\n  } = context;\n  let {\n    displayEventTime,\n    displayEventEnd\n  } = options;\n  let eventDef = seg.eventRange.def;\n  let eventInstance = seg.eventRange.instance;\n  if (displayEventTime == null) {\n    displayEventTime = defaultDisplayEventTime !== false;\n  }\n  if (displayEventEnd == null) {\n    displayEventEnd = defaultDisplayEventEnd !== false;\n  }\n  let wholeEventStart = eventInstance.range.start;\n  let wholeEventEnd = eventInstance.range.end;\n  let segStart = startOverride || seg.start || seg.eventRange.range.start;\n  let segEnd = endOverride || seg.end || seg.eventRange.range.end;\n  let isStartDay = startOfDay(wholeEventStart).valueOf() === startOfDay(segStart).valueOf();\n  let isEndDay = startOfDay(addMs(wholeEventEnd, -1)).valueOf() === startOfDay(addMs(segEnd, -1)).valueOf();\n  if (displayEventTime && !eventDef.allDay && (isStartDay || isEndDay)) {\n    segStart = isStartDay ? wholeEventStart : segStart;\n    segEnd = isEndDay ? wholeEventEnd : segEnd;\n    if (displayEventEnd && eventDef.hasEnd) {\n      return dateEnv.formatRange(segStart, segEnd, timeFormat, {\n        forcedStartTzo: startOverride ? null : eventInstance.forcedStartTzo,\n        forcedEndTzo: endOverride ? null : eventInstance.forcedEndTzo\n      });\n    }\n    return dateEnv.format(segStart, timeFormat, {\n      forcedTzo: startOverride ? null : eventInstance.forcedStartTzo // nooooo, same\n    });\n  }\n  return '';\n}\nfunction getSegMeta(seg, todayRange, nowDate) {\n  let segRange = seg.eventRange.range;\n  return {\n    isPast: segRange.end <= (nowDate || todayRange.start),\n    isFuture: segRange.start >= (nowDate || todayRange.end),\n    isToday: todayRange && rangeContainsMarker(todayRange, segRange.start)\n  };\n}\nfunction getEventClassNames(props) {\n  let classNames = ['fc-event'];\n  if (props.isMirror) {\n    classNames.push('fc-event-mirror');\n  }\n  if (props.isDraggable) {\n    classNames.push('fc-event-draggable');\n  }\n  if (props.isStartResizable || props.isEndResizable) {\n    classNames.push('fc-event-resizable');\n  }\n  if (props.isDragging) {\n    classNames.push('fc-event-dragging');\n  }\n  if (props.isResizing) {\n    classNames.push('fc-event-resizing');\n  }\n  if (props.isSelected) {\n    classNames.push('fc-event-selected');\n  }\n  if (props.isStart) {\n    classNames.push('fc-event-start');\n  }\n  if (props.isEnd) {\n    classNames.push('fc-event-end');\n  }\n  if (props.isPast) {\n    classNames.push('fc-event-past');\n  }\n  if (props.isToday) {\n    classNames.push('fc-event-today');\n  }\n  if (props.isFuture) {\n    classNames.push('fc-event-future');\n  }\n  return classNames;\n}\nfunction buildEventRangeKey(eventRange) {\n  return eventRange.instance ? eventRange.instance.instanceId : `${eventRange.def.defId}:${eventRange.range.start.toISOString()}`;\n  // inverse-background events don't have specific instances. TODO: better solution\n}\nfunction getSegAnchorAttrs(seg, context) {\n  let {\n    def,\n    instance\n  } = seg.eventRange;\n  let {\n    url\n  } = def;\n  if (url) {\n    return {\n      href: url\n    };\n  }\n  let {\n    emitter,\n    options\n  } = context;\n  let {\n    eventInteractive\n  } = options;\n  if (eventInteractive == null) {\n    eventInteractive = def.interactive;\n    if (eventInteractive == null) {\n      eventInteractive = Boolean(emitter.hasHandlers('eventClick'));\n    }\n  }\n  // mock what happens in EventClicking\n  if (eventInteractive) {\n    // only attach keyboard-related handlers because click handler is already done in EventClicking\n    return createAriaKeyboardAttrs(ev => {\n      emitter.trigger('eventClick', {\n        el: ev.target,\n        event: new EventImpl(context, def, instance),\n        jsEvent: ev,\n        view: context.viewApi\n      });\n    });\n  }\n  return {};\n}\nconst STANDARD_PROPS = {\n  start: identity,\n  end: identity,\n  allDay: Boolean\n};\nfunction parseDateSpan(raw, dateEnv, defaultDuration) {\n  let span = parseOpenDateSpan(raw, dateEnv);\n  let {\n    range\n  } = span;\n  if (!range.start) {\n    return null;\n  }\n  if (!range.end) {\n    if (defaultDuration == null) {\n      return null;\n    }\n    range.end = dateEnv.add(range.start, defaultDuration);\n  }\n  return span;\n}\n/*\nTODO: somehow combine with parseRange?\nWill return null if the start/end props were present but parsed invalidly.\n*/\nfunction parseOpenDateSpan(raw, dateEnv) {\n  let {\n    refined: standardProps,\n    extra\n  } = refineProps(raw, STANDARD_PROPS);\n  let startMeta = standardProps.start ? dateEnv.createMarkerMeta(standardProps.start) : null;\n  let endMeta = standardProps.end ? dateEnv.createMarkerMeta(standardProps.end) : null;\n  let {\n    allDay\n  } = standardProps;\n  if (allDay == null) {\n    allDay = startMeta && startMeta.isTimeUnspecified && (!endMeta || endMeta.isTimeUnspecified);\n  }\n  return Object.assign({\n    range: {\n      start: startMeta ? startMeta.marker : null,\n      end: endMeta ? endMeta.marker : null\n    },\n    allDay\n  }, extra);\n}\nfunction isDateSpansEqual(span0, span1) {\n  return rangesEqual(span0.range, span1.range) && span0.allDay === span1.allDay && isSpanPropsEqual(span0, span1);\n}\n// the NON-DATE-RELATED props\nfunction isSpanPropsEqual(span0, span1) {\n  for (let propName in span1) {\n    if (propName !== 'range' && propName !== 'allDay') {\n      if (span0[propName] !== span1[propName]) {\n        return false;\n      }\n    }\n  }\n  // are there any props that span0 has that span1 DOESN'T have?\n  // both have range/allDay, so no need to special-case.\n  for (let propName in span0) {\n    if (!(propName in span1)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction buildDateSpanApi(span, dateEnv) {\n  return Object.assign(Object.assign({}, buildRangeApi(span.range, dateEnv, span.allDay)), {\n    allDay: span.allDay\n  });\n}\nfunction buildRangeApiWithTimeZone(range, dateEnv, omitTime) {\n  return Object.assign(Object.assign({}, buildRangeApi(range, dateEnv, omitTime)), {\n    timeZone: dateEnv.timeZone\n  });\n}\nfunction buildRangeApi(range, dateEnv, omitTime) {\n  return {\n    start: dateEnv.toDate(range.start),\n    end: dateEnv.toDate(range.end),\n    startStr: dateEnv.formatIso(range.start, {\n      omitTime\n    }),\n    endStr: dateEnv.formatIso(range.end, {\n      omitTime\n    })\n  };\n}\nfunction fabricateEventRange(dateSpan, eventUiBases, context) {\n  let res = refineEventDef({\n    editable: false\n  }, context);\n  let def = parseEventDef(res.refined, res.extra, '',\n  // sourceId\n  dateSpan.allDay, true,\n  // hasEnd\n  context);\n  return {\n    def,\n    ui: compileEventUi(def, eventUiBases),\n    instance: createEventInstance(def.defId, dateSpan.range),\n    range: dateSpan.range,\n    isStart: true,\n    isEnd: true\n  };\n}\n\n/*\ngiven a function that resolves a result asynchronously.\nthe function can either call passed-in success and failure callbacks,\nor it can return a promise.\nif you need to pass additional params to func, bind them first.\n*/\nfunction unpromisify(func, normalizedSuccessCallback, normalizedFailureCallback) {\n  // guard against success/failure callbacks being called more than once\n  // and guard against a promise AND callback being used together.\n  let isResolved = false;\n  let wrappedSuccess = function (res) {\n    if (!isResolved) {\n      isResolved = true;\n      normalizedSuccessCallback(res);\n    }\n  };\n  let wrappedFailure = function (error) {\n    if (!isResolved) {\n      isResolved = true;\n      normalizedFailureCallback(error);\n    }\n  };\n  let res = func(wrappedSuccess, wrappedFailure);\n  if (res && typeof res.then === 'function') {\n    res.then(wrappedSuccess, wrappedFailure);\n  }\n}\nclass JsonRequestError extends Error {\n  constructor(message, response) {\n    super(message);\n    this.response = response;\n  }\n}\nfunction requestJson(method, url, params) {\n  method = method.toUpperCase();\n  const fetchOptions = {\n    method\n  };\n  if (method === 'GET') {\n    url += (url.indexOf('?') === -1 ? '?' : '&') + new URLSearchParams(params);\n  } else {\n    fetchOptions.body = new URLSearchParams(params);\n    fetchOptions.headers = {\n      'Content-Type': 'application/x-www-form-urlencoded'\n    };\n  }\n  return fetch(url, fetchOptions).then(fetchRes => {\n    if (fetchRes.ok) {\n      return fetchRes.json().then(parsedResponse => {\n        return [parsedResponse, fetchRes];\n      }, () => {\n        throw new JsonRequestError('Failure parsing JSON', fetchRes);\n      });\n    } else {\n      throw new JsonRequestError('Request failed', fetchRes);\n    }\n  });\n}\nlet canVGrowWithinCell;\nfunction getCanVGrowWithinCell() {\n  if (canVGrowWithinCell == null) {\n    canVGrowWithinCell = computeCanVGrowWithinCell();\n  }\n  return canVGrowWithinCell;\n}\nfunction computeCanVGrowWithinCell() {\n  // for SSR, because this function is call immediately at top-level\n  // TODO: just make this logic execute top-level, immediately, instead of doing lazily\n  if (typeof document === 'undefined') {\n    return true;\n  }\n  let el = document.createElement('div');\n  el.style.position = 'absolute';\n  el.style.top = '0px';\n  el.style.left = '0px';\n  el.innerHTML = '<table><tr><td><div></div></td></tr></table>';\n  el.querySelector('table').style.height = '100px';\n  el.querySelector('div').style.height = '100%';\n  document.body.appendChild(el);\n  let div = el.querySelector('div');\n  let possible = div.offsetHeight > 0;\n  document.body.removeChild(el);\n  return possible;\n}\nclass CalendarRoot extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.state = {\n      forPrint: false\n    };\n    this.handleBeforePrint = () => {\n      flushSync(() => {\n        this.setState({\n          forPrint: true\n        });\n      });\n    };\n    this.handleAfterPrint = () => {\n      flushSync(() => {\n        this.setState({\n          forPrint: false\n        });\n      });\n    };\n  }\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      options\n    } = props;\n    let {\n      forPrint\n    } = this.state;\n    let isHeightAuto = forPrint || options.height === 'auto' || options.contentHeight === 'auto';\n    let height = !isHeightAuto && options.height != null ? options.height : '';\n    let classNames = ['fc', forPrint ? 'fc-media-print' : 'fc-media-screen', `fc-direction-${options.direction}`, props.theme.getClass('root')];\n    if (!getCanVGrowWithinCell()) {\n      classNames.push('fc-liquid-hack');\n    }\n    return props.children(classNames, height, isHeightAuto, forPrint);\n  }\n  componentDidMount() {\n    let {\n      emitter\n    } = this.props;\n    emitter.on('_beforeprint', this.handleBeforePrint);\n    emitter.on('_afterprint', this.handleAfterPrint);\n  }\n  componentWillUnmount() {\n    let {\n      emitter\n    } = this.props;\n    emitter.off('_beforeprint', this.handleBeforePrint);\n    emitter.off('_afterprint', this.handleAfterPrint);\n  }\n}\nclass Interaction {\n  constructor(settings) {\n    this.component = settings.component;\n    this.isHitComboAllowed = settings.isHitComboAllowed || null;\n  }\n  destroy() {}\n}\nfunction parseInteractionSettings(component, input) {\n  return {\n    component,\n    el: input.el,\n    useEventCenter: input.useEventCenter != null ? input.useEventCenter : true,\n    isHitComboAllowed: input.isHitComboAllowed || null\n  };\n}\nfunction interactionSettingsToStore(settings) {\n  return {\n    [settings.component.uid]: settings\n  };\n}\n// global state\nconst interactionSettingsStore = {};\nclass CalendarImpl {\n  getCurrentData() {\n    return this.currentDataManager.getCurrentData();\n  }\n  dispatch(action) {\n    this.currentDataManager.dispatch(action);\n  }\n  get view() {\n    return this.getCurrentData().viewApi;\n  }\n  batchRendering(callback) {\n    callback();\n  }\n  updateSize() {\n    this.trigger('_resize', true);\n  }\n  // Options\n  // -----------------------------------------------------------------------------------------------------------------\n  setOption(name, val) {\n    this.dispatch({\n      type: 'SET_OPTION',\n      optionName: name,\n      rawOptionValue: val\n    });\n  }\n  getOption(name) {\n    return this.currentDataManager.currentCalendarOptionsInput[name];\n  }\n  getAvailableLocaleCodes() {\n    return Object.keys(this.getCurrentData().availableRawLocales);\n  }\n  // Trigger\n  // -----------------------------------------------------------------------------------------------------------------\n  on(handlerName, handler) {\n    let {\n      currentDataManager\n    } = this;\n    if (currentDataManager.currentCalendarOptionsRefiners[handlerName]) {\n      currentDataManager.emitter.on(handlerName, handler);\n    } else {\n      console.warn(`Unknown listener name '${handlerName}'`);\n    }\n  }\n  off(handlerName, handler) {\n    this.currentDataManager.emitter.off(handlerName, handler);\n  }\n  // not meant for public use\n  trigger(handlerName, ...args) {\n    this.currentDataManager.emitter.trigger(handlerName, ...args);\n  }\n  // View\n  // -----------------------------------------------------------------------------------------------------------------\n  changeView(viewType, dateOrRange) {\n    this.batchRendering(() => {\n      this.unselect();\n      if (dateOrRange) {\n        if (dateOrRange.start && dateOrRange.end) {\n          // a range\n          this.dispatch({\n            type: 'CHANGE_VIEW_TYPE',\n            viewType\n          });\n          this.dispatch({\n            type: 'SET_OPTION',\n            optionName: 'visibleRange',\n            rawOptionValue: dateOrRange\n          });\n        } else {\n          let {\n            dateEnv\n          } = this.getCurrentData();\n          this.dispatch({\n            type: 'CHANGE_VIEW_TYPE',\n            viewType,\n            dateMarker: dateEnv.createMarker(dateOrRange)\n          });\n        }\n      } else {\n        this.dispatch({\n          type: 'CHANGE_VIEW_TYPE',\n          viewType\n        });\n      }\n    });\n  }\n  // Forces navigation to a view for the given date.\n  // `viewType` can be a specific view name or a generic one like \"week\" or \"day\".\n  // needs to change\n  zoomTo(dateMarker, viewType) {\n    let state = this.getCurrentData();\n    let spec;\n    viewType = viewType || 'day'; // day is default zoom\n    spec = state.viewSpecs[viewType] || this.getUnitViewSpec(viewType);\n    this.unselect();\n    if (spec) {\n      this.dispatch({\n        type: 'CHANGE_VIEW_TYPE',\n        viewType: spec.type,\n        dateMarker\n      });\n    } else {\n      this.dispatch({\n        type: 'CHANGE_DATE',\n        dateMarker\n      });\n    }\n  }\n  // Given a duration singular unit, like \"week\" or \"day\", finds a matching view spec.\n  // Preference is given to views that have corresponding buttons.\n  getUnitViewSpec(unit) {\n    let {\n      viewSpecs,\n      toolbarConfig\n    } = this.getCurrentData();\n    let viewTypes = [].concat(toolbarConfig.header ? toolbarConfig.header.viewsWithButtons : [], toolbarConfig.footer ? toolbarConfig.footer.viewsWithButtons : []);\n    let i;\n    let spec;\n    for (let viewType in viewSpecs) {\n      viewTypes.push(viewType);\n    }\n    for (i = 0; i < viewTypes.length; i += 1) {\n      spec = viewSpecs[viewTypes[i]];\n      if (spec) {\n        if (spec.singleUnit === unit) {\n          return spec;\n        }\n      }\n    }\n    return null;\n  }\n  // Current Date\n  // -----------------------------------------------------------------------------------------------------------------\n  prev() {\n    this.unselect();\n    this.dispatch({\n      type: 'PREV'\n    });\n  }\n  next() {\n    this.unselect();\n    this.dispatch({\n      type: 'NEXT'\n    });\n  }\n  prevYear() {\n    let state = this.getCurrentData();\n    this.unselect();\n    this.dispatch({\n      type: 'CHANGE_DATE',\n      dateMarker: state.dateEnv.addYears(state.currentDate, -1)\n    });\n  }\n  nextYear() {\n    let state = this.getCurrentData();\n    this.unselect();\n    this.dispatch({\n      type: 'CHANGE_DATE',\n      dateMarker: state.dateEnv.addYears(state.currentDate, 1)\n    });\n  }\n  today() {\n    let state = this.getCurrentData();\n    this.unselect();\n    this.dispatch({\n      type: 'CHANGE_DATE',\n      dateMarker: getNow(state.calendarOptions.now, state.dateEnv)\n    });\n  }\n  gotoDate(zonedDateInput) {\n    let state = this.getCurrentData();\n    this.unselect();\n    this.dispatch({\n      type: 'CHANGE_DATE',\n      dateMarker: state.dateEnv.createMarker(zonedDateInput)\n    });\n  }\n  incrementDate(deltaInput) {\n    let state = this.getCurrentData();\n    let delta = createDuration(deltaInput);\n    if (delta) {\n      // else, warn about invalid input?\n      this.unselect();\n      this.dispatch({\n        type: 'CHANGE_DATE',\n        dateMarker: state.dateEnv.add(state.currentDate, delta)\n      });\n    }\n  }\n  getDate() {\n    let state = this.getCurrentData();\n    return state.dateEnv.toDate(state.currentDate);\n  }\n  // Date Formatting Utils\n  // -----------------------------------------------------------------------------------------------------------------\n  formatDate(d, formatter) {\n    let {\n      dateEnv\n    } = this.getCurrentData();\n    return dateEnv.format(dateEnv.createMarker(d), createFormatter(formatter));\n  }\n  // `settings` is for formatter AND isEndExclusive\n  formatRange(d0, d1, settings) {\n    let {\n      dateEnv\n    } = this.getCurrentData();\n    return dateEnv.formatRange(dateEnv.createMarker(d0), dateEnv.createMarker(d1), createFormatter(settings), settings);\n  }\n  formatIso(d, omitTime) {\n    let {\n      dateEnv\n    } = this.getCurrentData();\n    return dateEnv.formatIso(dateEnv.createMarker(d), {\n      omitTime\n    });\n  }\n  // Date Selection / Event Selection / DayClick\n  // -----------------------------------------------------------------------------------------------------------------\n  select(dateOrObj, endDate) {\n    let selectionInput;\n    if (endDate == null) {\n      if (dateOrObj.start != null) {\n        selectionInput = dateOrObj;\n      } else {\n        selectionInput = {\n          start: dateOrObj,\n          end: null\n        };\n      }\n    } else {\n      selectionInput = {\n        start: dateOrObj,\n        end: endDate\n      };\n    }\n    let state = this.getCurrentData();\n    let selection = parseDateSpan(selectionInput, state.dateEnv, createDuration({\n      days: 1\n    }));\n    if (selection) {\n      // throw parse error otherwise?\n      this.dispatch({\n        type: 'SELECT_DATES',\n        selection\n      });\n      triggerDateSelect(selection, null, state);\n    }\n  }\n  unselect(pev) {\n    let state = this.getCurrentData();\n    if (state.dateSelection) {\n      this.dispatch({\n        type: 'UNSELECT_DATES'\n      });\n      triggerDateUnselect(pev, state);\n    }\n  }\n  // Public Events API\n  // -----------------------------------------------------------------------------------------------------------------\n  addEvent(eventInput, sourceInput) {\n    if (eventInput instanceof EventImpl) {\n      let def = eventInput._def;\n      let instance = eventInput._instance;\n      let currentData = this.getCurrentData();\n      // not already present? don't want to add an old snapshot\n      if (!currentData.eventStore.defs[def.defId]) {\n        this.dispatch({\n          type: 'ADD_EVENTS',\n          eventStore: eventTupleToStore({\n            def,\n            instance\n          }) // TODO: better util for two args?\n        });\n        this.triggerEventAdd(eventInput);\n      }\n      return eventInput;\n    }\n    let state = this.getCurrentData();\n    let eventSource;\n    if (sourceInput instanceof EventSourceImpl) {\n      eventSource = sourceInput.internalEventSource;\n    } else if (typeof sourceInput === 'boolean') {\n      if (sourceInput) {\n        // true. part of the first event source\n        [eventSource] = hashValuesToArray(state.eventSources);\n      }\n    } else if (sourceInput != null) {\n      // an ID. accepts a number too\n      let sourceApi = this.getEventSourceById(sourceInput); // TODO: use an internal function\n      if (!sourceApi) {\n        console.warn(`Could not find an event source with ID \"${sourceInput}\"`); // TODO: test\n        return null;\n      }\n      eventSource = sourceApi.internalEventSource;\n    }\n    let tuple = parseEvent(eventInput, eventSource, state, false);\n    if (tuple) {\n      let newEventApi = new EventImpl(state, tuple.def, tuple.def.recurringDef ? null : tuple.instance);\n      this.dispatch({\n        type: 'ADD_EVENTS',\n        eventStore: eventTupleToStore(tuple)\n      });\n      this.triggerEventAdd(newEventApi);\n      return newEventApi;\n    }\n    return null;\n  }\n  triggerEventAdd(eventApi) {\n    let {\n      emitter\n    } = this.getCurrentData();\n    emitter.trigger('eventAdd', {\n      event: eventApi,\n      relatedEvents: [],\n      revert: () => {\n        this.dispatch({\n          type: 'REMOVE_EVENTS',\n          eventStore: eventApiToStore(eventApi)\n        });\n      }\n    });\n  }\n  // TODO: optimize\n  getEventById(id) {\n    let state = this.getCurrentData();\n    let {\n      defs,\n      instances\n    } = state.eventStore;\n    id = String(id);\n    for (let defId in defs) {\n      let def = defs[defId];\n      if (def.publicId === id) {\n        if (def.recurringDef) {\n          return new EventImpl(state, def, null);\n        }\n        for (let instanceId in instances) {\n          let instance = instances[instanceId];\n          if (instance.defId === def.defId) {\n            return new EventImpl(state, def, instance);\n          }\n        }\n      }\n    }\n    return null;\n  }\n  getEvents() {\n    let currentData = this.getCurrentData();\n    return buildEventApis(currentData.eventStore, currentData);\n  }\n  removeAllEvents() {\n    this.dispatch({\n      type: 'REMOVE_ALL_EVENTS'\n    });\n  }\n  // Public Event Sources API\n  // -----------------------------------------------------------------------------------------------------------------\n  getEventSources() {\n    let state = this.getCurrentData();\n    let sourceHash = state.eventSources;\n    let sourceApis = [];\n    for (let internalId in sourceHash) {\n      sourceApis.push(new EventSourceImpl(state, sourceHash[internalId]));\n    }\n    return sourceApis;\n  }\n  getEventSourceById(id) {\n    let state = this.getCurrentData();\n    let sourceHash = state.eventSources;\n    id = String(id);\n    for (let sourceId in sourceHash) {\n      if (sourceHash[sourceId].publicId === id) {\n        return new EventSourceImpl(state, sourceHash[sourceId]);\n      }\n    }\n    return null;\n  }\n  addEventSource(sourceInput) {\n    let state = this.getCurrentData();\n    if (sourceInput instanceof EventSourceImpl) {\n      // not already present? don't want to add an old snapshot\n      if (!state.eventSources[sourceInput.internalEventSource.sourceId]) {\n        this.dispatch({\n          type: 'ADD_EVENT_SOURCES',\n          sources: [sourceInput.internalEventSource]\n        });\n      }\n      return sourceInput;\n    }\n    let eventSource = parseEventSource(sourceInput, state);\n    if (eventSource) {\n      // TODO: error otherwise?\n      this.dispatch({\n        type: 'ADD_EVENT_SOURCES',\n        sources: [eventSource]\n      });\n      return new EventSourceImpl(state, eventSource);\n    }\n    return null;\n  }\n  removeAllEventSources() {\n    this.dispatch({\n      type: 'REMOVE_ALL_EVENT_SOURCES'\n    });\n  }\n  refetchEvents() {\n    this.dispatch({\n      type: 'FETCH_EVENT_SOURCES',\n      isRefetch: true\n    });\n  }\n  // Scroll\n  // -----------------------------------------------------------------------------------------------------------------\n  scrollToTime(timeInput) {\n    let time = createDuration(timeInput);\n    if (time) {\n      this.trigger('_scrollRequest', {\n        time\n      });\n    }\n  }\n}\nfunction pointInsideRect(point, rect) {\n  return point.left >= rect.left && point.left < rect.right && point.top >= rect.top && point.top < rect.bottom;\n}\n// Returns a new rectangle that is the intersection of the two rectangles. If they don't intersect, returns false\nfunction intersectRects(rect1, rect2) {\n  let res = {\n    left: Math.max(rect1.left, rect2.left),\n    right: Math.min(rect1.right, rect2.right),\n    top: Math.max(rect1.top, rect2.top),\n    bottom: Math.min(rect1.bottom, rect2.bottom)\n  };\n  if (res.left < res.right && res.top < res.bottom) {\n    return res;\n  }\n  return false;\n}\nfunction translateRect(rect, deltaX, deltaY) {\n  return {\n    left: rect.left + deltaX,\n    right: rect.right + deltaX,\n    top: rect.top + deltaY,\n    bottom: rect.bottom + deltaY\n  };\n}\n// Returns a new point that will have been moved to reside within the given rectangle\nfunction constrainPoint(point, rect) {\n  return {\n    left: Math.min(Math.max(point.left, rect.left), rect.right),\n    top: Math.min(Math.max(point.top, rect.top), rect.bottom)\n  };\n}\n// Returns a point that is the center of the given rectangle\nfunction getRectCenter(rect) {\n  return {\n    left: (rect.left + rect.right) / 2,\n    top: (rect.top + rect.bottom) / 2\n  };\n}\n// Subtracts point2's coordinates from point1's coordinates, returning a delta\nfunction diffPoints(point1, point2) {\n  return {\n    left: point1.left - point2.left,\n    top: point1.top - point2.top\n  };\n}\nconst EMPTY_EVENT_STORE = createEmptyEventStore(); // for purecomponents. TODO: keep elsewhere\nclass Splitter {\n  constructor() {\n    this.getKeysForEventDefs = memoize(this._getKeysForEventDefs);\n    this.splitDateSelection = memoize(this._splitDateSpan);\n    this.splitEventStore = memoize(this._splitEventStore);\n    this.splitIndividualUi = memoize(this._splitIndividualUi);\n    this.splitEventDrag = memoize(this._splitInteraction);\n    this.splitEventResize = memoize(this._splitInteraction);\n    this.eventUiBuilders = {}; // TODO: typescript protection\n  }\n  splitProps(props) {\n    let keyInfos = this.getKeyInfo(props);\n    let defKeys = this.getKeysForEventDefs(props.eventStore);\n    let dateSelections = this.splitDateSelection(props.dateSelection);\n    let individualUi = this.splitIndividualUi(props.eventUiBases, defKeys); // the individual *bases*\n    let eventStores = this.splitEventStore(props.eventStore, defKeys);\n    let eventDrags = this.splitEventDrag(props.eventDrag);\n    let eventResizes = this.splitEventResize(props.eventResize);\n    let splitProps = {};\n    this.eventUiBuilders = mapHash(keyInfos, (info, key) => this.eventUiBuilders[key] || memoize(buildEventUiForKey));\n    for (let key in keyInfos) {\n      let keyInfo = keyInfos[key];\n      let eventStore = eventStores[key] || EMPTY_EVENT_STORE;\n      let buildEventUi = this.eventUiBuilders[key];\n      splitProps[key] = {\n        businessHours: keyInfo.businessHours || props.businessHours,\n        dateSelection: dateSelections[key] || null,\n        eventStore,\n        eventUiBases: buildEventUi(props.eventUiBases[''], keyInfo.ui, individualUi[key]),\n        eventSelection: eventStore.instances[props.eventSelection] ? props.eventSelection : '',\n        eventDrag: eventDrags[key] || null,\n        eventResize: eventResizes[key] || null\n      };\n    }\n    return splitProps;\n  }\n  _splitDateSpan(dateSpan) {\n    let dateSpans = {};\n    if (dateSpan) {\n      let keys = this.getKeysForDateSpan(dateSpan);\n      for (let key of keys) {\n        dateSpans[key] = dateSpan;\n      }\n    }\n    return dateSpans;\n  }\n  _getKeysForEventDefs(eventStore) {\n    return mapHash(eventStore.defs, eventDef => this.getKeysForEventDef(eventDef));\n  }\n  _splitEventStore(eventStore, defKeys) {\n    let {\n      defs,\n      instances\n    } = eventStore;\n    let splitStores = {};\n    for (let defId in defs) {\n      for (let key of defKeys[defId]) {\n        if (!splitStores[key]) {\n          splitStores[key] = createEmptyEventStore();\n        }\n        splitStores[key].defs[defId] = defs[defId];\n      }\n    }\n    for (let instanceId in instances) {\n      let instance = instances[instanceId];\n      for (let key of defKeys[instance.defId]) {\n        if (splitStores[key]) {\n          // must have already been created\n          splitStores[key].instances[instanceId] = instance;\n        }\n      }\n    }\n    return splitStores;\n  }\n  _splitIndividualUi(eventUiBases, defKeys) {\n    let splitHashes = {};\n    for (let defId in eventUiBases) {\n      if (defId) {\n        // not the '' key\n        for (let key of defKeys[defId]) {\n          if (!splitHashes[key]) {\n            splitHashes[key] = {};\n          }\n          splitHashes[key][defId] = eventUiBases[defId];\n        }\n      }\n    }\n    return splitHashes;\n  }\n  _splitInteraction(interaction) {\n    let splitStates = {};\n    if (interaction) {\n      let affectedStores = this._splitEventStore(interaction.affectedEvents, this._getKeysForEventDefs(interaction.affectedEvents));\n      // can't rely on defKeys because event data is mutated\n      let mutatedKeysByDefId = this._getKeysForEventDefs(interaction.mutatedEvents);\n      let mutatedStores = this._splitEventStore(interaction.mutatedEvents, mutatedKeysByDefId);\n      let populate = key => {\n        if (!splitStates[key]) {\n          splitStates[key] = {\n            affectedEvents: affectedStores[key] || EMPTY_EVENT_STORE,\n            mutatedEvents: mutatedStores[key] || EMPTY_EVENT_STORE,\n            isEvent: interaction.isEvent\n          };\n        }\n      };\n      for (let key in affectedStores) {\n        populate(key);\n      }\n      for (let key in mutatedStores) {\n        populate(key);\n      }\n    }\n    return splitStates;\n  }\n}\nfunction buildEventUiForKey(allUi, eventUiForKey, individualUi) {\n  let baseParts = [];\n  if (allUi) {\n    baseParts.push(allUi);\n  }\n  if (eventUiForKey) {\n    baseParts.push(eventUiForKey);\n  }\n  let stuff = {\n    '': combineEventUis(baseParts)\n  };\n  if (individualUi) {\n    Object.assign(stuff, individualUi);\n  }\n  return stuff;\n}\nfunction getDateMeta(date, todayRange, nowDate, dateProfile) {\n  return {\n    dow: date.getUTCDay(),\n    isDisabled: Boolean(dateProfile && !rangeContainsMarker(dateProfile.activeRange, date)),\n    isOther: Boolean(dateProfile && !rangeContainsMarker(dateProfile.currentRange, date)),\n    isToday: Boolean(todayRange && rangeContainsMarker(todayRange, date)),\n    isPast: Boolean(nowDate ? date < nowDate : todayRange ? date < todayRange.start : false),\n    isFuture: Boolean(nowDate ? date > nowDate : todayRange ? date >= todayRange.end : false)\n  };\n}\nfunction getDayClassNames(meta, theme) {\n  let classNames = ['fc-day', `fc-day-${DAY_IDS[meta.dow]}`];\n  if (meta.isDisabled) {\n    classNames.push('fc-day-disabled');\n  } else {\n    if (meta.isToday) {\n      classNames.push('fc-day-today');\n      classNames.push(theme.getClass('today'));\n    }\n    if (meta.isPast) {\n      classNames.push('fc-day-past');\n    }\n    if (meta.isFuture) {\n      classNames.push('fc-day-future');\n    }\n    if (meta.isOther) {\n      classNames.push('fc-day-other');\n    }\n  }\n  return classNames;\n}\nfunction getSlotClassNames(meta, theme) {\n  let classNames = ['fc-slot', `fc-slot-${DAY_IDS[meta.dow]}`];\n  if (meta.isDisabled) {\n    classNames.push('fc-slot-disabled');\n  } else {\n    if (meta.isToday) {\n      classNames.push('fc-slot-today');\n      classNames.push(theme.getClass('today'));\n    }\n    if (meta.isPast) {\n      classNames.push('fc-slot-past');\n    }\n    if (meta.isFuture) {\n      classNames.push('fc-slot-future');\n    }\n  }\n  return classNames;\n}\nconst DAY_FORMAT = createFormatter({\n  year: 'numeric',\n  month: 'long',\n  day: 'numeric'\n});\nconst WEEK_FORMAT = createFormatter({\n  week: 'long'\n});\nfunction buildNavLinkAttrs(context, dateMarker, viewType = 'day', isTabbable = true) {\n  const {\n    dateEnv,\n    options,\n    calendarApi\n  } = context;\n  let dateStr = dateEnv.format(dateMarker, viewType === 'week' ? WEEK_FORMAT : DAY_FORMAT);\n  if (options.navLinks) {\n    let zonedDate = dateEnv.toDate(dateMarker);\n    const handleInteraction = ev => {\n      let customAction = viewType === 'day' ? options.navLinkDayClick : viewType === 'week' ? options.navLinkWeekClick : null;\n      if (typeof customAction === 'function') {\n        customAction.call(calendarApi, dateEnv.toDate(dateMarker), ev);\n      } else {\n        if (typeof customAction === 'string') {\n          viewType = customAction;\n        }\n        calendarApi.zoomTo(dateMarker, viewType);\n      }\n    };\n    return Object.assign({\n      title: formatWithOrdinals(options.navLinkHint, [dateStr, zonedDate], dateStr),\n      'data-navlink': ''\n    }, isTabbable ? createAriaClickAttrs(handleInteraction) : {\n      onClick: handleInteraction\n    });\n  }\n  return {\n    'aria-label': dateStr\n  };\n}\nlet _isRtlScrollbarOnLeft = null;\nfunction getIsRtlScrollbarOnLeft() {\n  if (_isRtlScrollbarOnLeft === null) {\n    _isRtlScrollbarOnLeft = computeIsRtlScrollbarOnLeft();\n  }\n  return _isRtlScrollbarOnLeft;\n}\nfunction computeIsRtlScrollbarOnLeft() {\n  let outerEl = document.createElement('div');\n  applyStyle(outerEl, {\n    position: 'absolute',\n    top: -1000,\n    left: 0,\n    border: 0,\n    padding: 0,\n    overflow: 'scroll',\n    direction: 'rtl'\n  });\n  outerEl.innerHTML = '<div></div>';\n  document.body.appendChild(outerEl);\n  let innerEl = outerEl.firstChild;\n  let res = innerEl.getBoundingClientRect().left > outerEl.getBoundingClientRect().left;\n  removeElement(outerEl);\n  return res;\n}\nlet _scrollbarWidths;\nfunction getScrollbarWidths() {\n  if (!_scrollbarWidths) {\n    _scrollbarWidths = computeScrollbarWidths();\n  }\n  return _scrollbarWidths;\n}\nfunction computeScrollbarWidths() {\n  let el = document.createElement('div');\n  el.style.overflow = 'scroll';\n  el.style.position = 'absolute';\n  el.style.top = '-9999px';\n  el.style.left = '-9999px';\n  document.body.appendChild(el);\n  let res = computeScrollbarWidthsForEl(el);\n  document.body.removeChild(el);\n  return res;\n}\n// WARNING: will include border\nfunction computeScrollbarWidthsForEl(el) {\n  return {\n    x: el.offsetHeight - el.clientHeight,\n    y: el.offsetWidth - el.clientWidth\n  };\n}\nfunction computeEdges(el, getPadding = false) {\n  let computedStyle = window.getComputedStyle(el);\n  let borderLeft = parseInt(computedStyle.borderLeftWidth, 10) || 0;\n  let borderRight = parseInt(computedStyle.borderRightWidth, 10) || 0;\n  let borderTop = parseInt(computedStyle.borderTopWidth, 10) || 0;\n  let borderBottom = parseInt(computedStyle.borderBottomWidth, 10) || 0;\n  let badScrollbarWidths = computeScrollbarWidthsForEl(el); // includes border!\n  let scrollbarLeftRight = badScrollbarWidths.y - borderLeft - borderRight;\n  let scrollbarBottom = badScrollbarWidths.x - borderTop - borderBottom;\n  let res = {\n    borderLeft,\n    borderRight,\n    borderTop,\n    borderBottom,\n    scrollbarBottom,\n    scrollbarLeft: 0,\n    scrollbarRight: 0\n  };\n  if (getIsRtlScrollbarOnLeft() && computedStyle.direction === 'rtl') {\n    // is the scrollbar on the left side?\n    res.scrollbarLeft = scrollbarLeftRight;\n  } else {\n    res.scrollbarRight = scrollbarLeftRight;\n  }\n  if (getPadding) {\n    res.paddingLeft = parseInt(computedStyle.paddingLeft, 10) || 0;\n    res.paddingRight = parseInt(computedStyle.paddingRight, 10) || 0;\n    res.paddingTop = parseInt(computedStyle.paddingTop, 10) || 0;\n    res.paddingBottom = parseInt(computedStyle.paddingBottom, 10) || 0;\n  }\n  return res;\n}\nfunction computeInnerRect(el, goWithinPadding = false, doFromWindowViewport) {\n  let outerRect = doFromWindowViewport ? el.getBoundingClientRect() : computeRect(el);\n  let edges = computeEdges(el, goWithinPadding);\n  let res = {\n    left: outerRect.left + edges.borderLeft + edges.scrollbarLeft,\n    right: outerRect.right - edges.borderRight - edges.scrollbarRight,\n    top: outerRect.top + edges.borderTop,\n    bottom: outerRect.bottom - edges.borderBottom - edges.scrollbarBottom\n  };\n  if (goWithinPadding) {\n    res.left += edges.paddingLeft;\n    res.right -= edges.paddingRight;\n    res.top += edges.paddingTop;\n    res.bottom -= edges.paddingBottom;\n  }\n  return res;\n}\nfunction computeRect(el) {\n  let rect = el.getBoundingClientRect();\n  return {\n    left: rect.left + window.scrollX,\n    top: rect.top + window.scrollY,\n    right: rect.right + window.scrollX,\n    bottom: rect.bottom + window.scrollY\n  };\n}\nfunction computeClippedClientRect(el) {\n  let clippingParents = getClippingParents(el);\n  let rect = el.getBoundingClientRect();\n  for (let clippingParent of clippingParents) {\n    let intersection = intersectRects(rect, clippingParent.getBoundingClientRect());\n    if (intersection) {\n      rect = intersection;\n    } else {\n      return null;\n    }\n  }\n  return rect;\n}\n// does not return window\nfunction getClippingParents(el) {\n  let parents = [];\n  while (el instanceof HTMLElement) {\n    // will stop when gets to document or null\n    let computedStyle = window.getComputedStyle(el);\n    if (computedStyle.position === 'fixed') {\n      break;\n    }\n    if (/(auto|scroll)/.test(computedStyle.overflow + computedStyle.overflowY + computedStyle.overflowX)) {\n      parents.push(el);\n    }\n    el = el.parentNode;\n  }\n  return parents;\n}\n\n/*\nRecords offset information for a set of elements, relative to an origin element.\nCan record the left/right OR the top/bottom OR both.\nProvides methods for querying the cache by position.\n*/\nclass PositionCache {\n  constructor(originEl, els, isHorizontal, isVertical) {\n    this.els = els;\n    let originClientRect = this.originClientRect = originEl.getBoundingClientRect(); // relative to viewport top-left\n    if (isHorizontal) {\n      this.buildElHorizontals(originClientRect.left);\n    }\n    if (isVertical) {\n      this.buildElVerticals(originClientRect.top);\n    }\n  }\n  // Populates the left/right internal coordinate arrays\n  buildElHorizontals(originClientLeft) {\n    let lefts = [];\n    let rights = [];\n    for (let el of this.els) {\n      let rect = el.getBoundingClientRect();\n      lefts.push(rect.left - originClientLeft);\n      rights.push(rect.right - originClientLeft);\n    }\n    this.lefts = lefts;\n    this.rights = rights;\n  }\n  // Populates the top/bottom internal coordinate arrays\n  buildElVerticals(originClientTop) {\n    let tops = [];\n    let bottoms = [];\n    for (let el of this.els) {\n      let rect = el.getBoundingClientRect();\n      tops.push(rect.top - originClientTop);\n      bottoms.push(rect.bottom - originClientTop);\n    }\n    this.tops = tops;\n    this.bottoms = bottoms;\n  }\n  // Given a left offset (from document left), returns the index of the el that it horizontally intersects.\n  // If no intersection is made, returns undefined.\n  leftToIndex(leftPosition) {\n    let {\n      lefts,\n      rights\n    } = this;\n    let len = lefts.length;\n    let i;\n    for (i = 0; i < len; i += 1) {\n      if (leftPosition >= lefts[i] && leftPosition < rights[i]) {\n        return i;\n      }\n    }\n    return undefined; // TODO: better\n  }\n  // Given a top offset (from document top), returns the index of the el that it vertically intersects.\n  // If no intersection is made, returns undefined.\n  topToIndex(topPosition) {\n    let {\n      tops,\n      bottoms\n    } = this;\n    let len = tops.length;\n    let i;\n    for (i = 0; i < len; i += 1) {\n      if (topPosition >= tops[i] && topPosition < bottoms[i]) {\n        return i;\n      }\n    }\n    return undefined; // TODO: better\n  }\n  // Gets the width of the element at the given index\n  getWidth(leftIndex) {\n    return this.rights[leftIndex] - this.lefts[leftIndex];\n  }\n  // Gets the height of the element at the given index\n  getHeight(topIndex) {\n    return this.bottoms[topIndex] - this.tops[topIndex];\n  }\n  similarTo(otherCache) {\n    return similarNumArrays(this.tops || [], otherCache.tops || []) && similarNumArrays(this.bottoms || [], otherCache.bottoms || []) && similarNumArrays(this.lefts || [], otherCache.lefts || []) && similarNumArrays(this.rights || [], otherCache.rights || []);\n  }\n}\nfunction similarNumArrays(a, b) {\n  const len = a.length;\n  if (len !== b.length) {\n    return false;\n  }\n  for (let i = 0; i < len; i++) {\n    if (Math.round(a[i]) !== Math.round(b[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/* eslint max-classes-per-file: \"off\" */\n/*\nAn object for getting/setting scroll-related information for an element.\nInternally, this is done very differently for window versus DOM element,\nso this object serves as a common interface.\n*/\nclass ScrollController {\n  getMaxScrollTop() {\n    return this.getScrollHeight() - this.getClientHeight();\n  }\n  getMaxScrollLeft() {\n    return this.getScrollWidth() - this.getClientWidth();\n  }\n  canScrollVertically() {\n    return this.getMaxScrollTop() > 0;\n  }\n  canScrollHorizontally() {\n    return this.getMaxScrollLeft() > 0;\n  }\n  canScrollUp() {\n    return this.getScrollTop() > 0;\n  }\n  canScrollDown() {\n    return this.getScrollTop() < this.getMaxScrollTop();\n  }\n  canScrollLeft() {\n    return this.getScrollLeft() > 0;\n  }\n  canScrollRight() {\n    return this.getScrollLeft() < this.getMaxScrollLeft();\n  }\n}\nclass ElementScrollController extends ScrollController {\n  constructor(el) {\n    super();\n    this.el = el;\n  }\n  getScrollTop() {\n    return this.el.scrollTop;\n  }\n  getScrollLeft() {\n    return this.el.scrollLeft;\n  }\n  setScrollTop(top) {\n    this.el.scrollTop = top;\n  }\n  setScrollLeft(left) {\n    this.el.scrollLeft = left;\n  }\n  getScrollWidth() {\n    return this.el.scrollWidth;\n  }\n  getScrollHeight() {\n    return this.el.scrollHeight;\n  }\n  getClientHeight() {\n    return this.el.clientHeight;\n  }\n  getClientWidth() {\n    return this.el.clientWidth;\n  }\n}\nclass WindowScrollController extends ScrollController {\n  getScrollTop() {\n    return window.scrollY;\n  }\n  getScrollLeft() {\n    return window.scrollX;\n  }\n  setScrollTop(n) {\n    window.scroll(window.scrollX, n);\n  }\n  setScrollLeft(n) {\n    window.scroll(n, window.scrollY);\n  }\n  getScrollWidth() {\n    return document.documentElement.scrollWidth;\n  }\n  getScrollHeight() {\n    return document.documentElement.scrollHeight;\n  }\n  getClientHeight() {\n    return document.documentElement.clientHeight;\n  }\n  getClientWidth() {\n    return document.documentElement.clientWidth;\n  }\n}\n\n/*\nan INTERACTABLE date component\n\nPURPOSES:\n- hook up to fg, fill, and mirror renderers\n- interface for dragging and hits\n*/\nclass DateComponent extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.uid = guid();\n  }\n  // Hit System\n  // -----------------------------------------------------------------------------------------------------------------\n  prepareHits() {}\n  queryHit(positionLeft, positionTop, elWidth, elHeight) {\n    return null; // this should be abstract\n  }\n  // Pointer Interaction Utils\n  // -----------------------------------------------------------------------------------------------------------------\n  isValidSegDownEl(el) {\n    return !this.props.eventDrag &&\n    // HACK\n    !this.props.eventResize &&\n    // HACK\n    !elementClosest(el, '.fc-event-mirror');\n  }\n  isValidDateDownEl(el) {\n    return !elementClosest(el, '.fc-event:not(.fc-bg-event)') && !elementClosest(el, '.fc-more-link') &&\n    // a \"more..\" link\n    !elementClosest(el, 'a[data-navlink]') &&\n    // a clickable nav link\n    !elementClosest(el, '.fc-popover'); // hack\n  }\n}\nclass NamedTimeZoneImpl {\n  constructor(timeZoneName) {\n    this.timeZoneName = timeZoneName;\n  }\n}\nclass SegHierarchy {\n  constructor(getEntryThickness = entry => {\n    // if no thickness known, assume 1 (if 0, so small it always fits)\n    return entry.thickness || 1;\n  }) {\n    this.getEntryThickness = getEntryThickness;\n    // settings\n    this.strictOrder = false;\n    this.allowReslicing = false;\n    this.maxCoord = -1; // -1 means no max\n    this.maxStackCnt = -1; // -1 means no max\n    this.levelCoords = []; // ordered\n    this.entriesByLevel = []; // parallel with levelCoords\n    this.stackCnts = {}; // TODO: use better technique!?\n  }\n  addSegs(inputs) {\n    let hiddenEntries = [];\n    for (let input of inputs) {\n      this.insertEntry(input, hiddenEntries);\n    }\n    return hiddenEntries;\n  }\n  insertEntry(entry, hiddenEntries) {\n    let insertion = this.findInsertion(entry);\n    if (this.isInsertionValid(insertion, entry)) {\n      this.insertEntryAt(entry, insertion);\n    } else {\n      this.handleInvalidInsertion(insertion, entry, hiddenEntries);\n    }\n  }\n  isInsertionValid(insertion, entry) {\n    return (this.maxCoord === -1 || insertion.levelCoord + this.getEntryThickness(entry) <= this.maxCoord) && (this.maxStackCnt === -1 || insertion.stackCnt < this.maxStackCnt);\n  }\n  handleInvalidInsertion(insertion, entry, hiddenEntries) {\n    if (this.allowReslicing && insertion.touchingEntry) {\n      const hiddenEntry = Object.assign(Object.assign({}, entry), {\n        span: intersectSpans(entry.span, insertion.touchingEntry.span)\n      });\n      hiddenEntries.push(hiddenEntry);\n      this.splitEntry(entry, insertion.touchingEntry, hiddenEntries);\n    } else {\n      hiddenEntries.push(entry);\n    }\n  }\n  /*\n  Does NOT add what hit the `barrier` into hiddenEntries. Should already be done.\n  */\n  splitEntry(entry, barrier, hiddenEntries) {\n    let entrySpan = entry.span;\n    let barrierSpan = barrier.span;\n    if (entrySpan.start < barrierSpan.start) {\n      this.insertEntry({\n        index: entry.index,\n        thickness: entry.thickness,\n        span: {\n          start: entrySpan.start,\n          end: barrierSpan.start\n        }\n      }, hiddenEntries);\n    }\n    if (entrySpan.end > barrierSpan.end) {\n      this.insertEntry({\n        index: entry.index,\n        thickness: entry.thickness,\n        span: {\n          start: barrierSpan.end,\n          end: entrySpan.end\n        }\n      }, hiddenEntries);\n    }\n  }\n  insertEntryAt(entry, insertion) {\n    let {\n      entriesByLevel,\n      levelCoords\n    } = this;\n    if (insertion.lateral === -1) {\n      // create a new level\n      insertAt(levelCoords, insertion.level, insertion.levelCoord);\n      insertAt(entriesByLevel, insertion.level, [entry]);\n    } else {\n      // insert into existing level\n      insertAt(entriesByLevel[insertion.level], insertion.lateral, entry);\n    }\n    this.stackCnts[buildEntryKey(entry)] = insertion.stackCnt;\n  }\n  /*\n  does not care about limits\n  */\n  findInsertion(newEntry) {\n    let {\n      levelCoords,\n      entriesByLevel,\n      strictOrder,\n      stackCnts\n    } = this;\n    let levelCnt = levelCoords.length;\n    let candidateCoord = 0;\n    let touchingLevel = -1;\n    let touchingLateral = -1;\n    let touchingEntry = null;\n    let stackCnt = 0;\n    for (let trackingLevel = 0; trackingLevel < levelCnt; trackingLevel += 1) {\n      const trackingCoord = levelCoords[trackingLevel];\n      // if the current level is past the placed entry, we have found a good empty space and can stop.\n      // if strictOrder, keep finding more lateral intersections.\n      if (!strictOrder && trackingCoord >= candidateCoord + this.getEntryThickness(newEntry)) {\n        break;\n      }\n      let trackingEntries = entriesByLevel[trackingLevel];\n      let trackingEntry;\n      let searchRes = binarySearch(trackingEntries, newEntry.span.start, getEntrySpanEnd); // find first entry after newEntry's end\n      let lateralIndex = searchRes[0] + searchRes[1]; // if exact match (which doesn't collide), go to next one\n      while (\n      // loop through entries that horizontally intersect\n      (trackingEntry = trackingEntries[lateralIndex]) &&\n      // but not past the whole entry list\n      trackingEntry.span.start < newEntry.span.end // and not entirely past newEntry\n      ) {\n        let trackingEntryBottom = trackingCoord + this.getEntryThickness(trackingEntry);\n        // intersects into the top of the candidate?\n        if (trackingEntryBottom > candidateCoord) {\n          candidateCoord = trackingEntryBottom;\n          touchingEntry = trackingEntry;\n          touchingLevel = trackingLevel;\n          touchingLateral = lateralIndex;\n        }\n        // butts up against top of candidate? (will happen if just intersected as well)\n        if (trackingEntryBottom === candidateCoord) {\n          // accumulate the highest possible stackCnt of the trackingEntries that butt up\n          stackCnt = Math.max(stackCnt, stackCnts[buildEntryKey(trackingEntry)] + 1);\n        }\n        lateralIndex += 1;\n      }\n    }\n    // the destination level will be after touchingEntry's level. find it\n    let destLevel = 0;\n    if (touchingEntry) {\n      destLevel = touchingLevel + 1;\n      while (destLevel < levelCnt && levelCoords[destLevel] < candidateCoord) {\n        destLevel += 1;\n      }\n    }\n    // if adding to an existing level, find where to insert\n    let destLateral = -1;\n    if (destLevel < levelCnt && levelCoords[destLevel] === candidateCoord) {\n      destLateral = binarySearch(entriesByLevel[destLevel], newEntry.span.end, getEntrySpanEnd)[0];\n    }\n    return {\n      touchingLevel,\n      touchingLateral,\n      touchingEntry,\n      stackCnt,\n      levelCoord: candidateCoord,\n      level: destLevel,\n      lateral: destLateral\n    };\n  }\n  // sorted by levelCoord (lowest to highest)\n  toRects() {\n    let {\n      entriesByLevel,\n      levelCoords\n    } = this;\n    let levelCnt = entriesByLevel.length;\n    let rects = [];\n    for (let level = 0; level < levelCnt; level += 1) {\n      let entries = entriesByLevel[level];\n      let levelCoord = levelCoords[level];\n      for (let entry of entries) {\n        rects.push(Object.assign(Object.assign({}, entry), {\n          thickness: this.getEntryThickness(entry),\n          levelCoord\n        }));\n      }\n    }\n    return rects;\n  }\n}\nfunction getEntrySpanEnd(entry) {\n  return entry.span.end;\n}\nfunction buildEntryKey(entry) {\n  return entry.index + ':' + entry.span.start;\n}\n// returns groups with entries sorted by input order\nfunction groupIntersectingEntries(entries) {\n  let merges = [];\n  for (let entry of entries) {\n    let filteredMerges = [];\n    let hungryMerge = {\n      span: entry.span,\n      entries: [entry]\n    };\n    for (let merge of merges) {\n      if (intersectSpans(merge.span, hungryMerge.span)) {\n        hungryMerge = {\n          entries: merge.entries.concat(hungryMerge.entries),\n          span: joinSpans(merge.span, hungryMerge.span)\n        };\n      } else {\n        filteredMerges.push(merge);\n      }\n    }\n    filteredMerges.push(hungryMerge);\n    merges = filteredMerges;\n  }\n  return merges;\n}\nfunction joinSpans(span0, span1) {\n  return {\n    start: Math.min(span0.start, span1.start),\n    end: Math.max(span0.end, span1.end)\n  };\n}\nfunction intersectSpans(span0, span1) {\n  let start = Math.max(span0.start, span1.start);\n  let end = Math.min(span0.end, span1.end);\n  if (start < end) {\n    return {\n      start,\n      end\n    };\n  }\n  return null;\n}\n// general util\n// ---------------------------------------------------------------------------------------------------------------------\nfunction insertAt(arr, index, item) {\n  arr.splice(index, 0, item);\n}\nfunction binarySearch(a, searchVal, getItemVal) {\n  let startIndex = 0;\n  let endIndex = a.length; // exclusive\n  if (!endIndex || searchVal < getItemVal(a[startIndex])) {\n    // no items OR before first item\n    return [0, 0];\n  }\n  if (searchVal > getItemVal(a[endIndex - 1])) {\n    // after last item\n    return [endIndex, 0];\n  }\n  while (startIndex < endIndex) {\n    let middleIndex = Math.floor(startIndex + (endIndex - startIndex) / 2);\n    let middleVal = getItemVal(a[middleIndex]);\n    if (searchVal < middleVal) {\n      endIndex = middleIndex;\n    } else if (searchVal > middleVal) {\n      startIndex = middleIndex + 1;\n    } else {\n      // equal!\n      return [middleIndex, 1];\n    }\n  }\n  return [startIndex, 0];\n}\n\n/*\nAn abstraction for a dragging interaction originating on an event.\nDoes higher-level things than PointerDragger, such as possibly:\n- a \"mirror\" that moves with the pointer\n- a minimum number of pixels or other criteria for a true drag to begin\n\nsubclasses must emit:\n- pointerdown\n- dragstart\n- dragmove\n- pointerup\n- dragend\n*/\nclass ElementDragging {\n  constructor(el, selector) {\n    this.emitter = new Emitter();\n  }\n  destroy() {}\n  setMirrorIsVisible(bool) {\n    // optional if subclass doesn't want to support a mirror\n  }\n  setMirrorNeedsRevert(bool) {\n    // optional if subclass doesn't want to support a mirror\n  }\n  setAutoScrollEnabled(bool) {\n    // optional\n  }\n}\n\n// TODO: get rid of this in favor of options system,\n// tho it's really easy to access this globally rather than pass thru options.\nconst config = {};\n\n/*\nInformation about what will happen when an external element is dragged-and-dropped\nonto a calendar. Contains information for creating an event.\n*/\nconst DRAG_META_REFINERS = {\n  startTime: createDuration,\n  duration: createDuration,\n  create: Boolean,\n  sourceId: String\n};\nfunction parseDragMeta(raw) {\n  let {\n    refined,\n    extra\n  } = refineProps(raw, DRAG_META_REFINERS);\n  return {\n    startTime: refined.startTime || null,\n    duration: refined.duration || null,\n    create: refined.create != null ? refined.create : true,\n    sourceId: refined.sourceId,\n    leftoverProps: extra\n  };\n}\n\n// Computes a default column header formatting string if `colFormat` is not explicitly defined\nfunction computeFallbackHeaderFormat(datesRepDistinctDays, dayCnt) {\n  // if more than one week row, or if there are a lot of columns with not much space,\n  // put just the day numbers will be in each cell\n  if (!datesRepDistinctDays || dayCnt > 10) {\n    return createFormatter({\n      weekday: 'short'\n    }); // \"Sat\"\n  }\n  if (dayCnt > 1) {\n    return createFormatter({\n      weekday: 'short',\n      month: 'numeric',\n      day: 'numeric',\n      omitCommas: true\n    }); // \"Sat 11/12\"\n  }\n  return createFormatter({\n    weekday: 'long'\n  }); // \"Saturday\"\n}\nconst CLASS_NAME = 'fc-col-header-cell'; // do the cushion too? no\nfunction renderInner$1(renderProps) {\n  return renderProps.text;\n}\n\n// BAD name for this class now. used in the Header\nclass TableDateCell extends BaseComponent {\n  render() {\n    let {\n      dateEnv,\n      options,\n      theme,\n      viewApi\n    } = this.context;\n    let {\n      props\n    } = this;\n    let {\n      date,\n      dateProfile\n    } = props;\n    let dayMeta = getDateMeta(date, props.todayRange, null, dateProfile);\n    let classNames = [CLASS_NAME].concat(getDayClassNames(dayMeta, theme));\n    let text = dateEnv.format(date, props.dayHeaderFormat);\n    // if colCnt is 1, we are already in a day-view and don't need a navlink\n    let navLinkAttrs = !dayMeta.isDisabled && props.colCnt > 1 ? buildNavLinkAttrs(this.context, date) : {};\n    let renderProps = Object.assign(Object.assign(Object.assign({\n      date: dateEnv.toDate(date),\n      view: viewApi\n    }, props.extraRenderProps), {\n      text\n    }), dayMeta);\n    return createElement(ContentContainer, {\n      elTag: \"th\",\n      elClasses: classNames,\n      elAttrs: Object.assign({\n        role: 'columnheader',\n        colSpan: props.colSpan,\n        'data-date': !dayMeta.isDisabled ? formatDayString(date) : undefined\n      }, props.extraDataAttrs),\n      renderProps: renderProps,\n      generatorName: \"dayHeaderContent\",\n      customGenerator: options.dayHeaderContent,\n      defaultGenerator: renderInner$1,\n      classNameGenerator: options.dayHeaderClassNames,\n      didMount: options.dayHeaderDidMount,\n      willUnmount: options.dayHeaderWillUnmount\n    }, InnerContainer => createElement(\"div\", {\n      className: \"fc-scrollgrid-sync-inner\"\n    }, !dayMeta.isDisabled && createElement(InnerContainer, {\n      elTag: \"a\",\n      elAttrs: navLinkAttrs,\n      elClasses: ['fc-col-header-cell-cushion', props.isSticky && 'fc-sticky']\n    })));\n  }\n}\nconst WEEKDAY_FORMAT = createFormatter({\n  weekday: 'long'\n});\nclass TableDowCell extends BaseComponent {\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      dateEnv,\n      theme,\n      viewApi,\n      options\n    } = this.context;\n    let date = addDays(new Date(259200000), props.dow); // start with Sun, 04 Jan 1970 00:00:00 GMT\n    let dateMeta = {\n      dow: props.dow,\n      isDisabled: false,\n      isFuture: false,\n      isPast: false,\n      isToday: false,\n      isOther: false\n    };\n    let text = dateEnv.format(date, props.dayHeaderFormat);\n    let renderProps = Object.assign(Object.assign(Object.assign(Object.assign({\n      // TODO: make this public?\n      date\n    }, dateMeta), {\n      view: viewApi\n    }), props.extraRenderProps), {\n      text\n    });\n    return createElement(ContentContainer, {\n      elTag: \"th\",\n      elClasses: [CLASS_NAME, ...getDayClassNames(dateMeta, theme), ...(props.extraClassNames || [])],\n      elAttrs: Object.assign({\n        role: 'columnheader',\n        colSpan: props.colSpan\n      }, props.extraDataAttrs),\n      renderProps: renderProps,\n      generatorName: \"dayHeaderContent\",\n      customGenerator: options.dayHeaderContent,\n      defaultGenerator: renderInner$1,\n      classNameGenerator: options.dayHeaderClassNames,\n      didMount: options.dayHeaderDidMount,\n      willUnmount: options.dayHeaderWillUnmount\n    }, InnerContent => createElement(\"div\", {\n      className: \"fc-scrollgrid-sync-inner\"\n    }, createElement(InnerContent, {\n      elTag: \"a\",\n      elClasses: ['fc-col-header-cell-cushion', props.isSticky && 'fc-sticky'],\n      elAttrs: {\n        'aria-label': dateEnv.format(date, WEEKDAY_FORMAT)\n      }\n    })));\n  }\n}\nlet NowTimer = /*#__PURE__*/(() => {\n  class NowTimer extends Component {\n    constructor(props, context) {\n      super(props, context);\n      this.initialNowDate = getNow(context.options.now, context.dateEnv);\n      this.initialNowQueriedMs = new Date().valueOf();\n      this.state = this.computeTiming().currentState;\n    }\n    render() {\n      let {\n        props,\n        state\n      } = this;\n      return props.children(state.nowDate, state.todayRange);\n    }\n    componentDidMount() {\n      this.setTimeout();\n    }\n    componentDidUpdate(prevProps) {\n      if (prevProps.unit !== this.props.unit) {\n        this.clearTimeout();\n        this.setTimeout();\n      }\n    }\n    componentWillUnmount() {\n      this.clearTimeout();\n    }\n    computeTiming() {\n      let {\n        props,\n        context\n      } = this;\n      let unroundedNow = addMs(this.initialNowDate, new Date().valueOf() - this.initialNowQueriedMs);\n      let currentUnitStart = context.dateEnv.startOf(unroundedNow, props.unit);\n      let nextUnitStart = context.dateEnv.add(currentUnitStart, createDuration(1, props.unit));\n      let waitMs = nextUnitStart.valueOf() - unroundedNow.valueOf();\n      // there is a max setTimeout ms value (https://stackoverflow.com/a/3468650/96342)\n      // ensure no longer than a day\n      waitMs = Math.min(1000 * 60 * 60 * 24, waitMs);\n      return {\n        currentState: {\n          nowDate: currentUnitStart,\n          todayRange: buildDayRange(currentUnitStart)\n        },\n        nextState: {\n          nowDate: nextUnitStart,\n          todayRange: buildDayRange(nextUnitStart)\n        },\n        waitMs\n      };\n    }\n    setTimeout() {\n      let {\n        nextState,\n        waitMs\n      } = this.computeTiming();\n      this.timeoutId = setTimeout(() => {\n        this.setState(nextState, () => {\n          this.setTimeout();\n        });\n      }, waitMs);\n    }\n    clearTimeout() {\n      if (this.timeoutId) {\n        clearTimeout(this.timeoutId);\n      }\n    }\n  }\n  NowTimer.contextType = ViewContextType;\n  return NowTimer;\n})();\nfunction buildDayRange(date) {\n  let start = startOfDay(date);\n  let end = addDays(start, 1);\n  return {\n    start,\n    end\n  };\n}\nclass DayHeader extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.createDayHeaderFormatter = memoize(createDayHeaderFormatter);\n  }\n  render() {\n    let {\n      context\n    } = this;\n    let {\n      dates,\n      dateProfile,\n      datesRepDistinctDays,\n      renderIntro\n    } = this.props;\n    let dayHeaderFormat = this.createDayHeaderFormatter(context.options.dayHeaderFormat, datesRepDistinctDays, dates.length);\n    return createElement(NowTimer, {\n      unit: \"day\"\n    }, (nowDate, todayRange) => createElement(\"tr\", {\n      role: \"row\"\n    }, renderIntro && renderIntro('day'), dates.map(date => datesRepDistinctDays ? createElement(TableDateCell, {\n      key: date.toISOString(),\n      date: date,\n      dateProfile: dateProfile,\n      todayRange: todayRange,\n      colCnt: dates.length,\n      dayHeaderFormat: dayHeaderFormat\n    }) : createElement(TableDowCell, {\n      key: date.getUTCDay(),\n      dow: date.getUTCDay(),\n      dayHeaderFormat: dayHeaderFormat\n    }))));\n  }\n}\nfunction createDayHeaderFormatter(explicitFormat, datesRepDistinctDays, dateCnt) {\n  return explicitFormat || computeFallbackHeaderFormat(datesRepDistinctDays, dateCnt);\n}\nclass DaySeriesModel {\n  constructor(range, dateProfileGenerator) {\n    let date = range.start;\n    let {\n      end\n    } = range;\n    let indices = [];\n    let dates = [];\n    let dayIndex = -1;\n    while (date < end) {\n      // loop each day from start to end\n      if (dateProfileGenerator.isHiddenDay(date)) {\n        indices.push(dayIndex + 0.5); // mark that it's between indices\n      } else {\n        dayIndex += 1;\n        indices.push(dayIndex);\n        dates.push(date);\n      }\n      date = addDays(date, 1);\n    }\n    this.dates = dates;\n    this.indices = indices;\n    this.cnt = dates.length;\n  }\n  sliceRange(range) {\n    let firstIndex = this.getDateDayIndex(range.start); // inclusive first index\n    let lastIndex = this.getDateDayIndex(addDays(range.end, -1)); // inclusive last index\n    let clippedFirstIndex = Math.max(0, firstIndex);\n    let clippedLastIndex = Math.min(this.cnt - 1, lastIndex);\n    // deal with in-between indices\n    clippedFirstIndex = Math.ceil(clippedFirstIndex); // in-between starts round to next cell\n    clippedLastIndex = Math.floor(clippedLastIndex); // in-between ends round to prev cell\n    if (clippedFirstIndex <= clippedLastIndex) {\n      return {\n        firstIndex: clippedFirstIndex,\n        lastIndex: clippedLastIndex,\n        isStart: firstIndex === clippedFirstIndex,\n        isEnd: lastIndex === clippedLastIndex\n      };\n    }\n    return null;\n  }\n  // Given a date, returns its chronolocial cell-index from the first cell of the grid.\n  // If the date lies between cells (because of hiddenDays), returns a floating-point value between offsets.\n  // If before the first offset, returns a negative number.\n  // If after the last offset, returns an offset past the last cell offset.\n  // Only works for *start* dates of cells. Will not work for exclusive end dates for cells.\n  getDateDayIndex(date) {\n    let {\n      indices\n    } = this;\n    let dayOffset = Math.floor(diffDays(this.dates[0], date));\n    if (dayOffset < 0) {\n      return indices[0] - 1;\n    }\n    if (dayOffset >= indices.length) {\n      return indices[indices.length - 1] + 1;\n    }\n    return indices[dayOffset];\n  }\n}\nclass DayTableModel {\n  constructor(daySeries, breakOnWeeks) {\n    let {\n      dates\n    } = daySeries;\n    let daysPerRow;\n    let firstDay;\n    let rowCnt;\n    if (breakOnWeeks) {\n      // count columns until the day-of-week repeats\n      firstDay = dates[0].getUTCDay();\n      for (daysPerRow = 1; daysPerRow < dates.length; daysPerRow += 1) {\n        if (dates[daysPerRow].getUTCDay() === firstDay) {\n          break;\n        }\n      }\n      rowCnt = Math.ceil(dates.length / daysPerRow);\n    } else {\n      rowCnt = 1;\n      daysPerRow = dates.length;\n    }\n    this.rowCnt = rowCnt;\n    this.colCnt = daysPerRow;\n    this.daySeries = daySeries;\n    this.cells = this.buildCells();\n    this.headerDates = this.buildHeaderDates();\n  }\n  buildCells() {\n    let rows = [];\n    for (let row = 0; row < this.rowCnt; row += 1) {\n      let cells = [];\n      for (let col = 0; col < this.colCnt; col += 1) {\n        cells.push(this.buildCell(row, col));\n      }\n      rows.push(cells);\n    }\n    return rows;\n  }\n  buildCell(row, col) {\n    let date = this.daySeries.dates[row * this.colCnt + col];\n    return {\n      key: date.toISOString(),\n      date\n    };\n  }\n  buildHeaderDates() {\n    let dates = [];\n    for (let col = 0; col < this.colCnt; col += 1) {\n      dates.push(this.cells[0][col].date);\n    }\n    return dates;\n  }\n  sliceRange(range) {\n    let {\n      colCnt\n    } = this;\n    let seriesSeg = this.daySeries.sliceRange(range);\n    let segs = [];\n    if (seriesSeg) {\n      let {\n        firstIndex,\n        lastIndex\n      } = seriesSeg;\n      let index = firstIndex;\n      while (index <= lastIndex) {\n        let row = Math.floor(index / colCnt);\n        let nextIndex = Math.min((row + 1) * colCnt, lastIndex + 1);\n        segs.push({\n          row,\n          firstCol: index % colCnt,\n          lastCol: (nextIndex - 1) % colCnt,\n          isStart: seriesSeg.isStart && index === firstIndex,\n          isEnd: seriesSeg.isEnd && nextIndex - 1 === lastIndex\n        });\n        index = nextIndex;\n      }\n    }\n    return segs;\n  }\n}\nclass Slicer {\n  constructor() {\n    this.sliceBusinessHours = memoize(this._sliceBusinessHours);\n    this.sliceDateSelection = memoize(this._sliceDateSpan);\n    this.sliceEventStore = memoize(this._sliceEventStore);\n    this.sliceEventDrag = memoize(this._sliceInteraction);\n    this.sliceEventResize = memoize(this._sliceInteraction);\n    this.forceDayIfListItem = false; // hack\n  }\n  sliceProps(props, dateProfile, nextDayThreshold, context, ...extraArgs) {\n    let {\n      eventUiBases\n    } = props;\n    let eventSegs = this.sliceEventStore(props.eventStore, eventUiBases, dateProfile, nextDayThreshold, ...extraArgs);\n    return {\n      dateSelectionSegs: this.sliceDateSelection(props.dateSelection, dateProfile, nextDayThreshold, eventUiBases, context, ...extraArgs),\n      businessHourSegs: this.sliceBusinessHours(props.businessHours, dateProfile, nextDayThreshold, context, ...extraArgs),\n      fgEventSegs: eventSegs.fg,\n      bgEventSegs: eventSegs.bg,\n      eventDrag: this.sliceEventDrag(props.eventDrag, eventUiBases, dateProfile, nextDayThreshold, ...extraArgs),\n      eventResize: this.sliceEventResize(props.eventResize, eventUiBases, dateProfile, nextDayThreshold, ...extraArgs),\n      eventSelection: props.eventSelection\n    }; // TODO: give interactionSegs?\n  }\n  sliceNowDate(\n  // does not memoize\n  date, dateProfile, nextDayThreshold, context, ...extraArgs) {\n    return this._sliceDateSpan({\n      range: {\n        start: date,\n        end: addMs(date, 1)\n      },\n      allDay: false\n    },\n    // add 1 ms, protect against null range\n    dateProfile, nextDayThreshold, {}, context, ...extraArgs);\n  }\n  _sliceBusinessHours(businessHours, dateProfile, nextDayThreshold, context, ...extraArgs) {\n    if (!businessHours) {\n      return [];\n    }\n    return this._sliceEventStore(expandRecurring(businessHours, computeActiveRange(dateProfile, Boolean(nextDayThreshold)), context), {}, dateProfile, nextDayThreshold, ...extraArgs).bg;\n  }\n  _sliceEventStore(eventStore, eventUiBases, dateProfile, nextDayThreshold, ...extraArgs) {\n    if (eventStore) {\n      let rangeRes = sliceEventStore(eventStore, eventUiBases, computeActiveRange(dateProfile, Boolean(nextDayThreshold)), nextDayThreshold);\n      return {\n        bg: this.sliceEventRanges(rangeRes.bg, extraArgs),\n        fg: this.sliceEventRanges(rangeRes.fg, extraArgs)\n      };\n    }\n    return {\n      bg: [],\n      fg: []\n    };\n  }\n  _sliceInteraction(interaction, eventUiBases, dateProfile, nextDayThreshold, ...extraArgs) {\n    if (!interaction) {\n      return null;\n    }\n    let rangeRes = sliceEventStore(interaction.mutatedEvents, eventUiBases, computeActiveRange(dateProfile, Boolean(nextDayThreshold)), nextDayThreshold);\n    return {\n      segs: this.sliceEventRanges(rangeRes.fg, extraArgs),\n      affectedInstances: interaction.affectedEvents.instances,\n      isEvent: interaction.isEvent\n    };\n  }\n  _sliceDateSpan(dateSpan, dateProfile, nextDayThreshold, eventUiBases, context, ...extraArgs) {\n    if (!dateSpan) {\n      return [];\n    }\n    let activeRange = computeActiveRange(dateProfile, Boolean(nextDayThreshold));\n    let activeDateSpanRange = intersectRanges(dateSpan.range, activeRange);\n    if (activeDateSpanRange) {\n      dateSpan = Object.assign(Object.assign({}, dateSpan), {\n        range: activeDateSpanRange\n      });\n      let eventRange = fabricateEventRange(dateSpan, eventUiBases, context);\n      let segs = this.sliceRange(dateSpan.range, ...extraArgs);\n      for (let seg of segs) {\n        seg.eventRange = eventRange;\n      }\n      return segs;\n    }\n    return [];\n  }\n  /*\n  \"complete\" seg means it has component and eventRange\n  */\n  sliceEventRanges(eventRanges, extraArgs) {\n    let segs = [];\n    for (let eventRange of eventRanges) {\n      segs.push(...this.sliceEventRange(eventRange, extraArgs));\n    }\n    return segs;\n  }\n  /*\n  \"complete\" seg means it has component and eventRange\n  */\n  sliceEventRange(eventRange, extraArgs) {\n    let dateRange = eventRange.range;\n    // hack to make multi-day events that are being force-displayed as list-items to take up only one day\n    if (this.forceDayIfListItem && eventRange.ui.display === 'list-item') {\n      dateRange = {\n        start: dateRange.start,\n        end: addDays(dateRange.start, 1)\n      };\n    }\n    let segs = this.sliceRange(dateRange, ...extraArgs);\n    for (let seg of segs) {\n      seg.eventRange = eventRange;\n      seg.isStart = eventRange.isStart && seg.isStart;\n      seg.isEnd = eventRange.isEnd && seg.isEnd;\n    }\n    return segs;\n  }\n}\n/*\nfor incorporating slotMinTime/slotMaxTime if appropriate\nTODO: should be part of DateProfile!\nTimelineDateProfile already does this btw\n*/\nfunction computeActiveRange(dateProfile, isComponentAllDay) {\n  let range = dateProfile.activeRange;\n  if (isComponentAllDay) {\n    return range;\n  }\n  return {\n    start: addMs(range.start, dateProfile.slotMinTime.milliseconds),\n    end: addMs(range.end, dateProfile.slotMaxTime.milliseconds - 864e5) // 864e5 = ms in a day\n  };\n}\n\n// high-level segmenting-aware tester functions\n// ------------------------------------------------------------------------------------------------------------------------\nfunction isInteractionValid(interaction, dateProfile, context) {\n  let {\n    instances\n  } = interaction.mutatedEvents;\n  for (let instanceId in instances) {\n    if (!rangeContainsRange(dateProfile.validRange, instances[instanceId].range)) {\n      return false;\n    }\n  }\n  return isNewPropsValid({\n    eventDrag: interaction\n  }, context); // HACK: the eventDrag props is used for ALL interactions\n}\nfunction isDateSelectionValid(dateSelection, dateProfile, context) {\n  if (!rangeContainsRange(dateProfile.validRange, dateSelection.range)) {\n    return false;\n  }\n  return isNewPropsValid({\n    dateSelection\n  }, context);\n}\nfunction isNewPropsValid(newProps, context) {\n  let calendarState = context.getCurrentData();\n  let props = Object.assign({\n    businessHours: calendarState.businessHours,\n    dateSelection: '',\n    eventStore: calendarState.eventStore,\n    eventUiBases: calendarState.eventUiBases,\n    eventSelection: '',\n    eventDrag: null,\n    eventResize: null\n  }, newProps);\n  return (context.pluginHooks.isPropsValid || isPropsValid)(props, context);\n}\nfunction isPropsValid(state, context, dateSpanMeta = {}, filterConfig) {\n  if (state.eventDrag && !isInteractionPropsValid(state, context, dateSpanMeta, filterConfig)) {\n    return false;\n  }\n  if (state.dateSelection && !isDateSelectionPropsValid(state, context, dateSpanMeta, filterConfig)) {\n    return false;\n  }\n  return true;\n}\n// Moving Event Validation\n// ------------------------------------------------------------------------------------------------------------------------\nfunction isInteractionPropsValid(state, context, dateSpanMeta, filterConfig) {\n  let currentState = context.getCurrentData();\n  let interaction = state.eventDrag; // HACK: the eventDrag props is used for ALL interactions\n  let subjectEventStore = interaction.mutatedEvents;\n  let subjectDefs = subjectEventStore.defs;\n  let subjectInstances = subjectEventStore.instances;\n  let subjectConfigs = compileEventUis(subjectDefs, interaction.isEvent ? state.eventUiBases : {\n    '': currentState.selectionConfig\n  });\n  if (filterConfig) {\n    subjectConfigs = mapHash(subjectConfigs, filterConfig);\n  }\n  // exclude the subject events. TODO: exclude defs too?\n  let otherEventStore = excludeInstances(state.eventStore, interaction.affectedEvents.instances);\n  let otherDefs = otherEventStore.defs;\n  let otherInstances = otherEventStore.instances;\n  let otherConfigs = compileEventUis(otherDefs, state.eventUiBases);\n  for (let subjectInstanceId in subjectInstances) {\n    let subjectInstance = subjectInstances[subjectInstanceId];\n    let subjectRange = subjectInstance.range;\n    let subjectConfig = subjectConfigs[subjectInstance.defId];\n    let subjectDef = subjectDefs[subjectInstance.defId];\n    // constraint\n    if (!allConstraintsPass(subjectConfig.constraints, subjectRange, otherEventStore, state.businessHours, context)) {\n      return false;\n    }\n    // overlap\n    let {\n      eventOverlap\n    } = context.options;\n    let eventOverlapFunc = typeof eventOverlap === 'function' ? eventOverlap : null;\n    for (let otherInstanceId in otherInstances) {\n      let otherInstance = otherInstances[otherInstanceId];\n      // intersect! evaluate\n      if (rangesIntersect(subjectRange, otherInstance.range)) {\n        let otherOverlap = otherConfigs[otherInstance.defId].overlap;\n        // consider the other event's overlap. only do this if the subject event is a \"real\" event\n        if (otherOverlap === false && interaction.isEvent) {\n          return false;\n        }\n        if (subjectConfig.overlap === false) {\n          return false;\n        }\n        if (eventOverlapFunc && !eventOverlapFunc(new EventImpl(context, otherDefs[otherInstance.defId], otherInstance),\n        // still event\n        new EventImpl(context, subjectDef, subjectInstance))) {\n          return false;\n        }\n      }\n    }\n    // allow (a function)\n    let calendarEventStore = currentState.eventStore; // need global-to-calendar, not local to component (splittable)state\n    for (let subjectAllow of subjectConfig.allows) {\n      let subjectDateSpan = Object.assign(Object.assign({}, dateSpanMeta), {\n        range: subjectInstance.range,\n        allDay: subjectDef.allDay\n      });\n      let origDef = calendarEventStore.defs[subjectDef.defId];\n      let origInstance = calendarEventStore.instances[subjectInstanceId];\n      let eventApi;\n      if (origDef) {\n        // was previously in the calendar\n        eventApi = new EventImpl(context, origDef, origInstance);\n      } else {\n        // was an external event\n        eventApi = new EventImpl(context, subjectDef); // no instance, because had no dates\n      }\n      if (!subjectAllow(buildDateSpanApiWithContext(subjectDateSpan, context), eventApi)) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n// Date Selection Validation\n// ------------------------------------------------------------------------------------------------------------------------\nfunction isDateSelectionPropsValid(state, context, dateSpanMeta, filterConfig) {\n  let relevantEventStore = state.eventStore;\n  let relevantDefs = relevantEventStore.defs;\n  let relevantInstances = relevantEventStore.instances;\n  let selection = state.dateSelection;\n  let selectionRange = selection.range;\n  let {\n    selectionConfig\n  } = context.getCurrentData();\n  if (filterConfig) {\n    selectionConfig = filterConfig(selectionConfig);\n  }\n  // constraint\n  if (!allConstraintsPass(selectionConfig.constraints, selectionRange, relevantEventStore, state.businessHours, context)) {\n    return false;\n  }\n  // overlap\n  let {\n    selectOverlap\n  } = context.options;\n  let selectOverlapFunc = typeof selectOverlap === 'function' ? selectOverlap : null;\n  for (let relevantInstanceId in relevantInstances) {\n    let relevantInstance = relevantInstances[relevantInstanceId];\n    // intersect! evaluate\n    if (rangesIntersect(selectionRange, relevantInstance.range)) {\n      if (selectionConfig.overlap === false) {\n        return false;\n      }\n      if (selectOverlapFunc && !selectOverlapFunc(new EventImpl(context, relevantDefs[relevantInstance.defId], relevantInstance), null)) {\n        return false;\n      }\n    }\n  }\n  // allow (a function)\n  for (let selectionAllow of selectionConfig.allows) {\n    let fullDateSpan = Object.assign(Object.assign({}, dateSpanMeta), selection);\n    if (!selectionAllow(buildDateSpanApiWithContext(fullDateSpan, context), null)) {\n      return false;\n    }\n  }\n  return true;\n}\n// Constraint Utils\n// ------------------------------------------------------------------------------------------------------------------------\nfunction allConstraintsPass(constraints, subjectRange, otherEventStore, businessHoursUnexpanded, context) {\n  for (let constraint of constraints) {\n    if (!anyRangesContainRange(constraintToRanges(constraint, subjectRange, otherEventStore, businessHoursUnexpanded, context), subjectRange)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction constraintToRanges(constraint, subjectRange,\n// for expanding a recurring constraint, or expanding business hours\notherEventStore,\n// for if constraint is an even group ID\nbusinessHoursUnexpanded,\n// for if constraint is 'businessHours'\ncontext) {\n  if (constraint === 'businessHours') {\n    return eventStoreToRanges(expandRecurring(businessHoursUnexpanded, subjectRange, context));\n  }\n  if (typeof constraint === 'string') {\n    // an group ID\n    return eventStoreToRanges(filterEventStoreDefs(otherEventStore, eventDef => eventDef.groupId === constraint));\n  }\n  if (typeof constraint === 'object' && constraint) {\n    // non-null object\n    return eventStoreToRanges(expandRecurring(constraint, subjectRange, context));\n  }\n  return []; // if it's false\n}\n// TODO: move to event-store file?\nfunction eventStoreToRanges(eventStore) {\n  let {\n    instances\n  } = eventStore;\n  let ranges = [];\n  for (let instanceId in instances) {\n    ranges.push(instances[instanceId].range);\n  }\n  return ranges;\n}\n// TODO: move to geom file?\nfunction anyRangesContainRange(outerRanges, innerRange) {\n  for (let outerRange of outerRanges) {\n    if (rangeContainsRange(outerRange, innerRange)) {\n      return true;\n    }\n  }\n  return false;\n}\nconst VISIBLE_HIDDEN_RE = /^(visible|hidden)$/;\nclass Scroller extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.handleEl = el => {\n      this.el = el;\n      setRef(this.props.elRef, el);\n    };\n  }\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      liquid,\n      liquidIsAbsolute\n    } = props;\n    let isAbsolute = liquid && liquidIsAbsolute;\n    let className = ['fc-scroller'];\n    if (liquid) {\n      if (liquidIsAbsolute) {\n        className.push('fc-scroller-liquid-absolute');\n      } else {\n        className.push('fc-scroller-liquid');\n      }\n    }\n    return createElement(\"div\", {\n      ref: this.handleEl,\n      className: className.join(' '),\n      style: {\n        overflowX: props.overflowX,\n        overflowY: props.overflowY,\n        left: isAbsolute && -(props.overcomeLeft || 0) || '',\n        right: isAbsolute && -(props.overcomeRight || 0) || '',\n        bottom: isAbsolute && -(props.overcomeBottom || 0) || '',\n        marginLeft: !isAbsolute && -(props.overcomeLeft || 0) || '',\n        marginRight: !isAbsolute && -(props.overcomeRight || 0) || '',\n        marginBottom: !isAbsolute && -(props.overcomeBottom || 0) || '',\n        maxHeight: props.maxHeight || ''\n      }\n    }, props.children);\n  }\n  needsXScrolling() {\n    if (VISIBLE_HIDDEN_RE.test(this.props.overflowX)) {\n      return false;\n    }\n    // testing scrollWidth>clientWidth is unreliable cross-browser when pixel heights aren't integers.\n    // much more reliable to see if children are taller than the scroller, even tho doesn't account for\n    // inner-child margins and absolute positioning\n    let {\n      el\n    } = this;\n    let realClientWidth = this.el.getBoundingClientRect().width - this.getYScrollbarWidth();\n    let {\n      children\n    } = el;\n    for (let i = 0; i < children.length; i += 1) {\n      let childEl = children[i];\n      if (childEl.getBoundingClientRect().width > realClientWidth) {\n        return true;\n      }\n    }\n    return false;\n  }\n  needsYScrolling() {\n    if (VISIBLE_HIDDEN_RE.test(this.props.overflowY)) {\n      return false;\n    }\n    // testing scrollHeight>clientHeight is unreliable cross-browser when pixel heights aren't integers.\n    // much more reliable to see if children are taller than the scroller, even tho doesn't account for\n    // inner-child margins and absolute positioning\n    let {\n      el\n    } = this;\n    let realClientHeight = this.el.getBoundingClientRect().height - this.getXScrollbarWidth();\n    let {\n      children\n    } = el;\n    for (let i = 0; i < children.length; i += 1) {\n      let childEl = children[i];\n      if (childEl.getBoundingClientRect().height > realClientHeight) {\n        return true;\n      }\n    }\n    return false;\n  }\n  getXScrollbarWidth() {\n    if (VISIBLE_HIDDEN_RE.test(this.props.overflowX)) {\n      return 0;\n    }\n    return this.el.offsetHeight - this.el.clientHeight; // only works because we guarantee no borders. TODO: add to CSS with important?\n  }\n  getYScrollbarWidth() {\n    if (VISIBLE_HIDDEN_RE.test(this.props.overflowY)) {\n      return 0;\n    }\n    return this.el.offsetWidth - this.el.clientWidth; // only works because we guarantee no borders. TODO: add to CSS with important?\n  }\n}\n\n/*\nTODO: somehow infer OtherArgs from masterCallback?\nTODO: infer RefType from masterCallback if provided\n*/\nclass RefMap {\n  constructor(masterCallback) {\n    this.masterCallback = masterCallback;\n    this.currentMap = {};\n    this.depths = {};\n    this.callbackMap = {};\n    this.handleValue = (val, key) => {\n      let {\n        depths,\n        currentMap\n      } = this;\n      let removed = false;\n      let added = false;\n      if (val !== null) {\n        // for bug... ACTUALLY: can probably do away with this now that callers don't share numeric indices anymore\n        removed = key in currentMap;\n        currentMap[key] = val;\n        depths[key] = (depths[key] || 0) + 1;\n        added = true;\n      } else {\n        depths[key] -= 1;\n        if (!depths[key]) {\n          delete currentMap[key];\n          delete this.callbackMap[key];\n          removed = true;\n        }\n      }\n      if (this.masterCallback) {\n        if (removed) {\n          this.masterCallback(null, String(key));\n        }\n        if (added) {\n          this.masterCallback(val, String(key));\n        }\n      }\n    };\n  }\n  createRef(key) {\n    let refCallback = this.callbackMap[key];\n    if (!refCallback) {\n      refCallback = this.callbackMap[key] = val => {\n        this.handleValue(val, String(key));\n      };\n    }\n    return refCallback;\n  }\n  // TODO: check callers that don't care about order. should use getAll instead\n  // NOTE: this method has become less valuable now that we are encouraged to map order by some other index\n  // TODO: provide ONE array-export function, buildArray, which fails on non-numeric indexes. caller can manipulate and \"collect\"\n  collect(startIndex, endIndex, step) {\n    return collectFromHash(this.currentMap, startIndex, endIndex, step);\n  }\n  getAll() {\n    return hashValuesToArray(this.currentMap);\n  }\n}\nfunction computeShrinkWidth(chunkEls) {\n  let shrinkCells = findElements(chunkEls, '.fc-scrollgrid-shrink');\n  let largestWidth = 0;\n  for (let shrinkCell of shrinkCells) {\n    largestWidth = Math.max(largestWidth, computeSmallestCellWidth(shrinkCell));\n  }\n  return Math.ceil(largestWidth); // <table> elements work best with integers. round up to ensure contents fits\n}\nfunction getSectionHasLiquidHeight(props, sectionConfig) {\n  return props.liquid && sectionConfig.liquid; // does the section do liquid-height? (need to have whole scrollgrid liquid-height as well)\n}\nfunction getAllowYScrolling(props, sectionConfig) {\n  return sectionConfig.maxHeight != null ||\n  // if its possible for the height to max out, we might need scrollbars\n  getSectionHasLiquidHeight(props, sectionConfig); // if the section is liquid height, it might condense enough to require scrollbars\n}\n// TODO: ONLY use `arg`. force out internal function to use same API\nfunction renderChunkContent(sectionConfig, chunkConfig, arg, isHeader) {\n  let {\n    expandRows\n  } = arg;\n  let content = typeof chunkConfig.content === 'function' ? chunkConfig.content(arg) : createElement('table', {\n    role: 'presentation',\n    className: [chunkConfig.tableClassName, sectionConfig.syncRowHeights ? 'fc-scrollgrid-sync-table' : ''].join(' '),\n    style: {\n      minWidth: arg.tableMinWidth,\n      width: arg.clientWidth,\n      height: expandRows ? arg.clientHeight : '' // css `height` on a <table> serves as a min-height\n    }\n  }, arg.tableColGroupNode, createElement(isHeader ? 'thead' : 'tbody', {\n    role: 'presentation'\n  }, typeof chunkConfig.rowContent === 'function' ? chunkConfig.rowContent(arg) : chunkConfig.rowContent));\n  return content;\n}\nfunction isColPropsEqual(cols0, cols1) {\n  return isArraysEqual(cols0, cols1, isPropsEqual);\n}\nfunction renderMicroColGroup(cols, shrinkWidth) {\n  let colNodes = [];\n  /*\n  for ColProps with spans, it would have been great to make a single <col span=\"\">\n  HOWEVER, Chrome was getting messing up distributing the width to <td>/<th> elements with colspans.\n  SOLUTION: making individual <col> elements makes Chrome behave.\n  */\n  for (let colProps of cols) {\n    let span = colProps.span || 1;\n    for (let i = 0; i < span; i += 1) {\n      colNodes.push(createElement(\"col\", {\n        style: {\n          width: colProps.width === 'shrink' ? sanitizeShrinkWidth(shrinkWidth) : colProps.width || '',\n          minWidth: colProps.minWidth || ''\n        }\n      }));\n    }\n  }\n  return createElement('colgroup', {}, ...colNodes);\n}\nfunction sanitizeShrinkWidth(shrinkWidth) {\n  /* why 4? if we do 0, it will kill any border, which are needed for computeSmallestCellWidth\n  4 accounts for 2 2-pixel borders. TODO: better solution? */\n  return shrinkWidth == null ? 4 : shrinkWidth;\n}\nfunction hasShrinkWidth(cols) {\n  for (let col of cols) {\n    if (col.width === 'shrink') {\n      return true;\n    }\n  }\n  return false;\n}\nfunction getScrollGridClassNames(liquid, context) {\n  let classNames = ['fc-scrollgrid', context.theme.getClass('table')];\n  if (liquid) {\n    classNames.push('fc-scrollgrid-liquid');\n  }\n  return classNames;\n}\nfunction getSectionClassNames(sectionConfig, wholeTableVGrow) {\n  let classNames = ['fc-scrollgrid-section', `fc-scrollgrid-section-${sectionConfig.type}`, sectionConfig.className // used?\n  ];\n  if (wholeTableVGrow && sectionConfig.liquid && sectionConfig.maxHeight == null) {\n    classNames.push('fc-scrollgrid-section-liquid');\n  }\n  if (sectionConfig.isSticky) {\n    classNames.push('fc-scrollgrid-section-sticky');\n  }\n  return classNames;\n}\nfunction renderScrollShim(arg) {\n  return createElement(\"div\", {\n    className: \"fc-scrollgrid-sticky-shim\",\n    style: {\n      width: arg.clientWidth,\n      minWidth: arg.tableMinWidth\n    }\n  });\n}\nfunction getStickyHeaderDates(options) {\n  let {\n    stickyHeaderDates\n  } = options;\n  if (stickyHeaderDates == null || stickyHeaderDates === 'auto') {\n    stickyHeaderDates = options.height === 'auto' || options.viewHeight === 'auto';\n  }\n  return stickyHeaderDates;\n}\nfunction getStickyFooterScrollbar(options) {\n  let {\n    stickyFooterScrollbar\n  } = options;\n  if (stickyFooterScrollbar == null || stickyFooterScrollbar === 'auto') {\n    stickyFooterScrollbar = options.height === 'auto' || options.viewHeight === 'auto';\n  }\n  return stickyFooterScrollbar;\n}\nclass SimpleScrollGrid extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.processCols = memoize(a => a, isColPropsEqual); // so we get same `cols` props every time\n    // yucky to memoize VNodes, but much more efficient for consumers\n    this.renderMicroColGroup = memoize(renderMicroColGroup);\n    this.scrollerRefs = new RefMap();\n    this.scrollerElRefs = new RefMap(this._handleScrollerEl.bind(this));\n    this.state = {\n      shrinkWidth: null,\n      forceYScrollbars: false,\n      scrollerClientWidths: {},\n      scrollerClientHeights: {}\n    };\n    // TODO: can do a really simple print-view. dont need to join rows\n    this.handleSizing = () => {\n      this.safeSetState(Object.assign({\n        shrinkWidth: this.computeShrinkWidth()\n      }, this.computeScrollerDims()));\n    };\n  }\n  render() {\n    let {\n      props,\n      state,\n      context\n    } = this;\n    let sectionConfigs = props.sections || [];\n    let cols = this.processCols(props.cols);\n    let microColGroupNode = this.renderMicroColGroup(cols, state.shrinkWidth);\n    let classNames = getScrollGridClassNames(props.liquid, context);\n    if (props.collapsibleWidth) {\n      classNames.push('fc-scrollgrid-collapsible');\n    }\n    // TODO: make DRY\n    let configCnt = sectionConfigs.length;\n    let configI = 0;\n    let currentConfig;\n    let headSectionNodes = [];\n    let bodySectionNodes = [];\n    let footSectionNodes = [];\n    while (configI < configCnt && (currentConfig = sectionConfigs[configI]).type === 'header') {\n      headSectionNodes.push(this.renderSection(currentConfig, microColGroupNode, true));\n      configI += 1;\n    }\n    while (configI < configCnt && (currentConfig = sectionConfigs[configI]).type === 'body') {\n      bodySectionNodes.push(this.renderSection(currentConfig, microColGroupNode, false));\n      configI += 1;\n    }\n    while (configI < configCnt && (currentConfig = sectionConfigs[configI]).type === 'footer') {\n      footSectionNodes.push(this.renderSection(currentConfig, microColGroupNode, true));\n      configI += 1;\n    }\n    // firefox bug: when setting height on table and there is a thead or tfoot,\n    // the necessary height:100% on the liquid-height body section forces the *whole* table to be taller. (bug #5524)\n    // use getCanVGrowWithinCell as a way to detect table-stupid firefox.\n    // if so, use a simpler dom structure, jam everything into a lone tbody.\n    let isBuggy = !getCanVGrowWithinCell();\n    const roleAttrs = {\n      role: 'rowgroup'\n    };\n    return createElement('table', {\n      role: 'grid',\n      className: classNames.join(' '),\n      style: {\n        height: props.height\n      }\n    }, Boolean(!isBuggy && headSectionNodes.length) && createElement('thead', roleAttrs, ...headSectionNodes), Boolean(!isBuggy && bodySectionNodes.length) && createElement('tbody', roleAttrs, ...bodySectionNodes), Boolean(!isBuggy && footSectionNodes.length) && createElement('tfoot', roleAttrs, ...footSectionNodes), isBuggy && createElement('tbody', roleAttrs, ...headSectionNodes, ...bodySectionNodes, ...footSectionNodes));\n  }\n  renderSection(sectionConfig, microColGroupNode, isHeader) {\n    if ('outerContent' in sectionConfig) {\n      return createElement(Fragment, {\n        key: sectionConfig.key\n      }, sectionConfig.outerContent);\n    }\n    return createElement(\"tr\", {\n      key: sectionConfig.key,\n      role: \"presentation\",\n      className: getSectionClassNames(sectionConfig, this.props.liquid).join(' ')\n    }, this.renderChunkTd(sectionConfig, microColGroupNode, sectionConfig.chunk, isHeader));\n  }\n  renderChunkTd(sectionConfig, microColGroupNode, chunkConfig, isHeader) {\n    if ('outerContent' in chunkConfig) {\n      return chunkConfig.outerContent;\n    }\n    let {\n      props\n    } = this;\n    let {\n      forceYScrollbars,\n      scrollerClientWidths,\n      scrollerClientHeights\n    } = this.state;\n    let needsYScrolling = getAllowYScrolling(props, sectionConfig); // TODO: do lazily. do in section config?\n    let isLiquid = getSectionHasLiquidHeight(props, sectionConfig);\n    // for `!props.liquid` - is WHOLE scrollgrid natural height?\n    // TODO: do same thing in advanced scrollgrid? prolly not b/c always has horizontal scrollbars\n    let overflowY = !props.liquid ? 'visible' : forceYScrollbars ? 'scroll' : !needsYScrolling ? 'hidden' : 'auto';\n    let sectionKey = sectionConfig.key;\n    let content = renderChunkContent(sectionConfig, chunkConfig, {\n      tableColGroupNode: microColGroupNode,\n      tableMinWidth: '',\n      clientWidth: !props.collapsibleWidth && scrollerClientWidths[sectionKey] !== undefined ? scrollerClientWidths[sectionKey] : null,\n      clientHeight: scrollerClientHeights[sectionKey] !== undefined ? scrollerClientHeights[sectionKey] : null,\n      expandRows: sectionConfig.expandRows,\n      syncRowHeights: false,\n      rowSyncHeights: [],\n      reportRowHeightChange: () => {}\n    }, isHeader);\n    return createElement(isHeader ? 'th' : 'td', {\n      ref: chunkConfig.elRef,\n      role: 'presentation'\n    }, createElement(\"div\", {\n      className: `fc-scroller-harness${isLiquid ? ' fc-scroller-harness-liquid' : ''}`\n    }, createElement(Scroller, {\n      ref: this.scrollerRefs.createRef(sectionKey),\n      elRef: this.scrollerElRefs.createRef(sectionKey),\n      overflowY: overflowY,\n      overflowX: !props.liquid ? 'visible' : 'hidden' /* natural height? */,\n      maxHeight: sectionConfig.maxHeight,\n      liquid: isLiquid,\n      liquidIsAbsolute // because its within a harness\n      : true\n    }, content)));\n  }\n  _handleScrollerEl(scrollerEl, key) {\n    let section = getSectionByKey(this.props.sections, key);\n    if (section) {\n      setRef(section.chunk.scrollerElRef, scrollerEl);\n    }\n  }\n  componentDidMount() {\n    this.handleSizing();\n    this.context.addResizeHandler(this.handleSizing);\n  }\n  componentDidUpdate() {\n    // TODO: need better solution when state contains non-sizing things\n    this.handleSizing();\n  }\n  componentWillUnmount() {\n    this.context.removeResizeHandler(this.handleSizing);\n  }\n  computeShrinkWidth() {\n    return hasShrinkWidth(this.props.cols) ? computeShrinkWidth(this.scrollerElRefs.getAll()) : 0;\n  }\n  computeScrollerDims() {\n    let scrollbarWidth = getScrollbarWidths();\n    let {\n      scrollerRefs,\n      scrollerElRefs\n    } = this;\n    let forceYScrollbars = false;\n    let scrollerClientWidths = {};\n    let scrollerClientHeights = {};\n    for (let sectionKey in scrollerRefs.currentMap) {\n      let scroller = scrollerRefs.currentMap[sectionKey];\n      if (scroller && scroller.needsYScrolling()) {\n        forceYScrollbars = true;\n        break;\n      }\n    }\n    for (let section of this.props.sections) {\n      let sectionKey = section.key;\n      let scrollerEl = scrollerElRefs.currentMap[sectionKey];\n      if (scrollerEl) {\n        let harnessEl = scrollerEl.parentNode; // TODO: weird way to get this. need harness b/c doesn't include table borders\n        scrollerClientWidths[sectionKey] = Math.floor(harnessEl.getBoundingClientRect().width - (forceYScrollbars ? scrollbarWidth.y // use global because scroller might not have scrollbars yet but will need them in future\n        : 0));\n        scrollerClientHeights[sectionKey] = Math.floor(harnessEl.getBoundingClientRect().height);\n      }\n    }\n    return {\n      forceYScrollbars,\n      scrollerClientWidths,\n      scrollerClientHeights\n    };\n  }\n}\nSimpleScrollGrid.addStateEquality({\n  scrollerClientWidths: isPropsEqual,\n  scrollerClientHeights: isPropsEqual\n});\nfunction getSectionByKey(sections, key) {\n  for (let section of sections) {\n    if (section.key === key) {\n      return section;\n    }\n  }\n  return null;\n}\nclass EventContainer extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.handleEl = el => {\n      this.el = el;\n      if (el) {\n        setElSeg(el, this.props.seg);\n      }\n    };\n  }\n  render() {\n    const {\n      props,\n      context\n    } = this;\n    const {\n      options\n    } = context;\n    const {\n      seg\n    } = props;\n    const {\n      eventRange\n    } = seg;\n    const {\n      ui\n    } = eventRange;\n    const renderProps = {\n      event: new EventImpl(context, eventRange.def, eventRange.instance),\n      view: context.viewApi,\n      timeText: props.timeText,\n      textColor: ui.textColor,\n      backgroundColor: ui.backgroundColor,\n      borderColor: ui.borderColor,\n      isDraggable: !props.disableDragging && computeSegDraggable(seg, context),\n      isStartResizable: !props.disableResizing && computeSegStartResizable(seg, context),\n      isEndResizable: !props.disableResizing && computeSegEndResizable(seg),\n      isMirror: Boolean(props.isDragging || props.isResizing || props.isDateSelecting),\n      isStart: Boolean(seg.isStart),\n      isEnd: Boolean(seg.isEnd),\n      isPast: Boolean(props.isPast),\n      isFuture: Boolean(props.isFuture),\n      isToday: Boolean(props.isToday),\n      isSelected: Boolean(props.isSelected),\n      isDragging: Boolean(props.isDragging),\n      isResizing: Boolean(props.isResizing)\n    };\n    return createElement(ContentContainer, Object.assign({}, props /* contains children */, {\n      elRef: this.handleEl,\n      elClasses: [...getEventClassNames(renderProps), ...seg.eventRange.ui.classNames, ...(props.elClasses || [])],\n      renderProps: renderProps,\n      generatorName: \"eventContent\",\n      customGenerator: options.eventContent,\n      defaultGenerator: props.defaultGenerator,\n      classNameGenerator: options.eventClassNames,\n      didMount: options.eventDidMount,\n      willUnmount: options.eventWillUnmount\n    }));\n  }\n  componentDidUpdate(prevProps) {\n    if (this.el && this.props.seg !== prevProps.seg) {\n      setElSeg(this.el, this.props.seg);\n    }\n  }\n}\n\n// should not be a purecomponent\nclass StandardEvent extends BaseComponent {\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let {\n      seg\n    } = props;\n    let {\n      ui\n    } = seg.eventRange;\n    let timeFormat = options.eventTimeFormat || props.defaultTimeFormat;\n    let timeText = buildSegTimeText(seg, timeFormat, context, props.defaultDisplayEventTime, props.defaultDisplayEventEnd);\n    return createElement(EventContainer, Object.assign({}, props /* includes elRef */, {\n      elTag: \"a\",\n      elStyle: {\n        borderColor: ui.borderColor,\n        backgroundColor: ui.backgroundColor\n      },\n      elAttrs: getSegAnchorAttrs(seg, context),\n      defaultGenerator: renderInnerContent$1,\n      timeText: timeText\n    }), (InnerContent, eventContentArg) => createElement(Fragment, null, createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-event-main'],\n      elStyle: {\n        color: eventContentArg.textColor\n      }\n    }), Boolean(eventContentArg.isStartResizable) && createElement(\"div\", {\n      className: \"fc-event-resizer fc-event-resizer-start\"\n    }), Boolean(eventContentArg.isEndResizable) && createElement(\"div\", {\n      className: \"fc-event-resizer fc-event-resizer-end\"\n    })));\n  }\n}\nfunction renderInnerContent$1(innerProps) {\n  return createElement(\"div\", {\n    className: \"fc-event-main-frame\"\n  }, innerProps.timeText && createElement(\"div\", {\n    className: \"fc-event-time\"\n  }, innerProps.timeText), createElement(\"div\", {\n    className: \"fc-event-title-container\"\n  }, createElement(\"div\", {\n    className: \"fc-event-title fc-sticky\"\n  }, innerProps.event.title || createElement(Fragment, null, \"\\u00A0\"))));\n}\nconst NowIndicatorContainer = props => createElement(ViewContextType.Consumer, null, context => {\n  let {\n    options\n  } = context;\n  let renderProps = {\n    isAxis: props.isAxis,\n    date: context.dateEnv.toDate(props.date),\n    view: context.viewApi\n  };\n  return createElement(ContentContainer, Object.assign({}, props /* includes children */, {\n    elTag: props.elTag || 'div',\n    renderProps: renderProps,\n    generatorName: \"nowIndicatorContent\",\n    customGenerator: options.nowIndicatorContent,\n    classNameGenerator: options.nowIndicatorClassNames,\n    didMount: options.nowIndicatorDidMount,\n    willUnmount: options.nowIndicatorWillUnmount\n  }));\n});\nconst DAY_NUM_FORMAT = createFormatter({\n  day: 'numeric'\n});\nclass DayCellContainer extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.refineRenderProps = memoizeObjArg(refineRenderProps);\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let renderProps = this.refineRenderProps({\n      date: props.date,\n      dateProfile: props.dateProfile,\n      todayRange: props.todayRange,\n      isMonthStart: props.isMonthStart || false,\n      showDayNumber: props.showDayNumber,\n      extraRenderProps: props.extraRenderProps,\n      viewApi: context.viewApi,\n      dateEnv: context.dateEnv,\n      monthStartFormat: options.monthStartFormat\n    });\n    return createElement(ContentContainer, Object.assign({}, props /* includes children */, {\n      elClasses: [...getDayClassNames(renderProps, context.theme), ...(props.elClasses || [])],\n      elAttrs: Object.assign(Object.assign({}, props.elAttrs), renderProps.isDisabled ? {} : {\n        'data-date': formatDayString(props.date)\n      }),\n      renderProps: renderProps,\n      generatorName: \"dayCellContent\",\n      customGenerator: options.dayCellContent,\n      defaultGenerator: props.defaultGenerator,\n      classNameGenerator:\n      // don't use custom classNames if disabled\n      renderProps.isDisabled ? undefined : options.dayCellClassNames,\n      didMount: options.dayCellDidMount,\n      willUnmount: options.dayCellWillUnmount\n    }));\n  }\n}\nfunction hasCustomDayCellContent(options) {\n  return Boolean(options.dayCellContent || hasCustomRenderingHandler('dayCellContent', options));\n}\nfunction refineRenderProps(raw) {\n  let {\n    date,\n    dateEnv,\n    dateProfile,\n    isMonthStart\n  } = raw;\n  let dayMeta = getDateMeta(date, raw.todayRange, null, dateProfile);\n  let dayNumberText = raw.showDayNumber ? dateEnv.format(date, isMonthStart ? raw.monthStartFormat : DAY_NUM_FORMAT) : '';\n  return Object.assign(Object.assign(Object.assign({\n    date: dateEnv.toDate(date),\n    view: raw.viewApi\n  }, dayMeta), {\n    isMonthStart,\n    dayNumberText\n  }), raw.extraRenderProps);\n}\nclass BgEvent extends BaseComponent {\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      seg\n    } = props;\n    return createElement(EventContainer, {\n      elTag: \"div\",\n      elClasses: ['fc-bg-event'],\n      elStyle: {\n        backgroundColor: seg.eventRange.ui.backgroundColor\n      },\n      defaultGenerator: renderInnerContent,\n      seg: seg,\n      timeText: \"\",\n      isDragging: false,\n      isResizing: false,\n      isDateSelecting: false,\n      isSelected: false,\n      isPast: props.isPast,\n      isFuture: props.isFuture,\n      isToday: props.isToday,\n      disableDragging: true,\n      disableResizing: true\n    });\n  }\n}\nfunction renderInnerContent(props) {\n  let {\n    title\n  } = props.event;\n  return title && createElement(\"div\", {\n    className: \"fc-event-title\"\n  }, props.event.title);\n}\nfunction renderFill(fillType) {\n  return createElement(\"div\", {\n    className: `fc-${fillType}`\n  });\n}\nconst WeekNumberContainer = props => createElement(ViewContextType.Consumer, null, context => {\n  let {\n    dateEnv,\n    options\n  } = context;\n  let {\n    date\n  } = props;\n  let format = options.weekNumberFormat || props.defaultFormat;\n  let num = dateEnv.computeWeekNumber(date); // TODO: somehow use for formatting as well?\n  let text = dateEnv.format(date, format);\n  let renderProps = {\n    num,\n    text,\n    date\n  };\n  return createElement(ContentContainer // why isn't WeekNumberContentArg being auto-detected?\n  , Object.assign({}, props /* includes children */, {\n    renderProps: renderProps,\n    generatorName: \"weekNumberContent\",\n    customGenerator: options.weekNumberContent,\n    defaultGenerator: renderInner,\n    classNameGenerator: options.weekNumberClassNames,\n    didMount: options.weekNumberDidMount,\n    willUnmount: options.weekNumberWillUnmount\n  }));\n});\nfunction renderInner(innerProps) {\n  return innerProps.text;\n}\nconst PADDING_FROM_VIEWPORT = 10;\nclass Popover extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.state = {\n      titleId: getUniqueDomId()\n    };\n    this.handleRootEl = el => {\n      this.rootEl = el;\n      if (this.props.elRef) {\n        setRef(this.props.elRef, el);\n      }\n    };\n    // Triggered when the user clicks *anywhere* in the document, for the autoHide feature\n    this.handleDocumentMouseDown = ev => {\n      // only hide the popover if the click happened outside the popover\n      const target = getEventTargetViaRoot(ev);\n      if (!this.rootEl.contains(target)) {\n        this.handleCloseClick();\n      }\n    };\n    this.handleDocumentKeyDown = ev => {\n      if (ev.key === 'Escape') {\n        this.handleCloseClick();\n      }\n    };\n    this.handleCloseClick = () => {\n      let {\n        onClose\n      } = this.props;\n      if (onClose) {\n        onClose();\n      }\n    };\n  }\n  render() {\n    let {\n      theme,\n      options\n    } = this.context;\n    let {\n      props,\n      state\n    } = this;\n    let classNames = ['fc-popover', theme.getClass('popover')].concat(props.extraClassNames || []);\n    return createPortal(createElement(\"div\", Object.assign({}, props.extraAttrs, {\n      id: props.id,\n      className: classNames.join(' '),\n      \"aria-labelledby\": state.titleId,\n      ref: this.handleRootEl\n    }), createElement(\"div\", {\n      className: 'fc-popover-header ' + theme.getClass('popoverHeader')\n    }, createElement(\"span\", {\n      className: \"fc-popover-title\",\n      id: state.titleId\n    }, props.title), createElement(\"span\", {\n      className: 'fc-popover-close ' + theme.getIconClass('close'),\n      title: options.closeHint,\n      onClick: this.handleCloseClick\n    })), createElement(\"div\", {\n      className: 'fc-popover-body ' + theme.getClass('popoverContent')\n    }, props.children)), props.parentEl);\n  }\n  componentDidMount() {\n    document.addEventListener('mousedown', this.handleDocumentMouseDown);\n    document.addEventListener('keydown', this.handleDocumentKeyDown);\n    this.updateSize();\n  }\n  componentWillUnmount() {\n    document.removeEventListener('mousedown', this.handleDocumentMouseDown);\n    document.removeEventListener('keydown', this.handleDocumentKeyDown);\n  }\n  updateSize() {\n    let {\n      isRtl\n    } = this.context;\n    let {\n      alignmentEl,\n      alignGridTop\n    } = this.props;\n    let {\n      rootEl\n    } = this;\n    let alignmentRect = computeClippedClientRect(alignmentEl);\n    if (alignmentRect) {\n      let popoverDims = rootEl.getBoundingClientRect();\n      // position relative to viewport\n      let popoverTop = alignGridTop ? elementClosest(alignmentEl, '.fc-scrollgrid').getBoundingClientRect().top : alignmentRect.top;\n      let popoverLeft = isRtl ? alignmentRect.right - popoverDims.width : alignmentRect.left;\n      // constrain\n      popoverTop = Math.max(popoverTop, PADDING_FROM_VIEWPORT);\n      popoverLeft = Math.min(popoverLeft, document.documentElement.clientWidth - PADDING_FROM_VIEWPORT - popoverDims.width);\n      popoverLeft = Math.max(popoverLeft, PADDING_FROM_VIEWPORT);\n      let origin = rootEl.offsetParent.getBoundingClientRect();\n      applyStyle(rootEl, {\n        top: popoverTop - origin.top,\n        left: popoverLeft - origin.left\n      });\n    }\n  }\n}\nclass MorePopover extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.handleRootEl = rootEl => {\n      this.rootEl = rootEl;\n      if (rootEl) {\n        this.context.registerInteractiveComponent(this, {\n          el: rootEl,\n          useEventCenter: false\n        });\n      } else {\n        this.context.unregisterInteractiveComponent(this);\n      }\n    };\n  }\n  render() {\n    let {\n      options,\n      dateEnv\n    } = this.context;\n    let {\n      props\n    } = this;\n    let {\n      startDate,\n      todayRange,\n      dateProfile\n    } = props;\n    let title = dateEnv.format(startDate, options.dayPopoverFormat);\n    return createElement(DayCellContainer, {\n      elRef: this.handleRootEl,\n      date: startDate,\n      dateProfile: dateProfile,\n      todayRange: todayRange\n    }, (InnerContent, renderProps, elAttrs) => createElement(Popover, {\n      elRef: elAttrs.ref,\n      id: props.id,\n      title: title,\n      extraClassNames: ['fc-more-popover'].concat(elAttrs.className || []),\n      extraAttrs: elAttrs /* TODO: make these time-based when not whole-day? */,\n      parentEl: props.parentEl,\n      alignmentEl: props.alignmentEl,\n      alignGridTop: props.alignGridTop,\n      onClose: props.onClose\n    }, hasCustomDayCellContent(options) && createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-more-popover-misc']\n    }), props.children));\n  }\n  queryHit(positionLeft, positionTop, elWidth, elHeight) {\n    let {\n      rootEl,\n      props\n    } = this;\n    if (positionLeft >= 0 && positionLeft < elWidth && positionTop >= 0 && positionTop < elHeight) {\n      return {\n        dateProfile: props.dateProfile,\n        dateSpan: Object.assign({\n          allDay: !props.forceTimed,\n          range: {\n            start: props.startDate,\n            end: props.endDate\n          }\n        }, props.extraDateSpan),\n        dayEl: rootEl,\n        rect: {\n          left: 0,\n          top: 0,\n          right: elWidth,\n          bottom: elHeight\n        },\n        layer: 1 // important when comparing with hits from other components\n      };\n    }\n    return null;\n  }\n}\nclass MoreLinkContainer extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.state = {\n      isPopoverOpen: false,\n      popoverId: getUniqueDomId()\n    };\n    this.handleLinkEl = linkEl => {\n      this.linkEl = linkEl;\n      if (this.props.elRef) {\n        setRef(this.props.elRef, linkEl);\n      }\n    };\n    this.handleClick = ev => {\n      let {\n        props,\n        context\n      } = this;\n      let {\n        moreLinkClick\n      } = context.options;\n      let date = computeRange(props).start;\n      function buildPublicSeg(seg) {\n        let {\n          def,\n          instance,\n          range\n        } = seg.eventRange;\n        return {\n          event: new EventImpl(context, def, instance),\n          start: context.dateEnv.toDate(range.start),\n          end: context.dateEnv.toDate(range.end),\n          isStart: seg.isStart,\n          isEnd: seg.isEnd\n        };\n      }\n      if (typeof moreLinkClick === 'function') {\n        moreLinkClick = moreLinkClick({\n          date,\n          allDay: Boolean(props.allDayDate),\n          allSegs: props.allSegs.map(buildPublicSeg),\n          hiddenSegs: props.hiddenSegs.map(buildPublicSeg),\n          jsEvent: ev,\n          view: context.viewApi\n        });\n      }\n      if (!moreLinkClick || moreLinkClick === 'popover') {\n        this.setState({\n          isPopoverOpen: true\n        });\n      } else if (typeof moreLinkClick === 'string') {\n        // a view name\n        context.calendarApi.zoomTo(date, moreLinkClick);\n      }\n    };\n    this.handlePopoverClose = () => {\n      this.setState({\n        isPopoverOpen: false\n      });\n    };\n  }\n  render() {\n    let {\n      props,\n      state\n    } = this;\n    return createElement(ViewContextType.Consumer, null, context => {\n      let {\n        viewApi,\n        options,\n        calendarApi\n      } = context;\n      let {\n        moreLinkText\n      } = options;\n      let {\n        moreCnt\n      } = props;\n      let range = computeRange(props);\n      let text = typeof moreLinkText === 'function' // TODO: eventually use formatWithOrdinals\n      ? moreLinkText.call(calendarApi, moreCnt) : `+${moreCnt} ${moreLinkText}`;\n      let hint = formatWithOrdinals(options.moreLinkHint, [moreCnt], text);\n      let renderProps = {\n        num: moreCnt,\n        shortText: `+${moreCnt}`,\n        text,\n        view: viewApi\n      };\n      return createElement(Fragment, null, Boolean(props.moreCnt) && createElement(ContentContainer, {\n        elTag: props.elTag || 'a',\n        elRef: this.handleLinkEl,\n        elClasses: [...(props.elClasses || []), 'fc-more-link'],\n        elStyle: props.elStyle,\n        elAttrs: Object.assign(Object.assign(Object.assign({}, props.elAttrs), createAriaClickAttrs(this.handleClick)), {\n          title: hint,\n          'aria-expanded': state.isPopoverOpen,\n          'aria-controls': state.isPopoverOpen ? state.popoverId : ''\n        }),\n        renderProps: renderProps,\n        generatorName: \"moreLinkContent\",\n        customGenerator: options.moreLinkContent,\n        defaultGenerator: props.defaultGenerator || renderMoreLinkInner,\n        classNameGenerator: options.moreLinkClassNames,\n        didMount: options.moreLinkDidMount,\n        willUnmount: options.moreLinkWillUnmount\n      }, props.children), state.isPopoverOpen && createElement(MorePopover, {\n        id: state.popoverId,\n        startDate: range.start,\n        endDate: range.end,\n        dateProfile: props.dateProfile,\n        todayRange: props.todayRange,\n        extraDateSpan: props.extraDateSpan,\n        parentEl: this.parentEl,\n        alignmentEl: props.alignmentElRef ? props.alignmentElRef.current : this.linkEl,\n        alignGridTop: props.alignGridTop,\n        forceTimed: props.forceTimed,\n        onClose: this.handlePopoverClose\n      }, props.popoverContent()));\n    });\n  }\n  componentDidMount() {\n    this.updateParentEl();\n  }\n  componentDidUpdate() {\n    this.updateParentEl();\n  }\n  updateParentEl() {\n    if (this.linkEl) {\n      this.parentEl = elementClosest(this.linkEl, '.fc-view-harness');\n    }\n  }\n}\nfunction renderMoreLinkInner(props) {\n  return props.text;\n}\nfunction computeRange(props) {\n  if (props.allDayDate) {\n    return {\n      start: props.allDayDate,\n      end: addDays(props.allDayDate, 1)\n    };\n  }\n  let {\n    hiddenSegs\n  } = props;\n  return {\n    start: computeEarliestSegStart(hiddenSegs),\n    end: computeLatestSegEnd(hiddenSegs)\n  };\n}\nfunction computeEarliestSegStart(segs) {\n  return segs.reduce(pickEarliestStart).eventRange.range.start;\n}\nfunction pickEarliestStart(seg0, seg1) {\n  return seg0.eventRange.range.start < seg1.eventRange.range.start ? seg0 : seg1;\n}\nfunction computeLatestSegEnd(segs) {\n  return segs.reduce(pickLatestEnd).eventRange.range.end;\n}\nfunction pickLatestEnd(seg0, seg1) {\n  return seg0.eventRange.range.end > seg1.eventRange.range.end ? seg0 : seg1;\n}\nclass Store {\n  constructor() {\n    this.handlers = [];\n  }\n  set(value) {\n    this.currentValue = value;\n    for (let handler of this.handlers) {\n      handler(value);\n    }\n  }\n  subscribe(handler) {\n    this.handlers.push(handler);\n    if (this.currentValue !== undefined) {\n      handler(this.currentValue);\n    }\n  }\n}\n\n/*\nSubscribers will get a LIST of CustomRenderings\n*/\nclass CustomRenderingStore extends Store {\n  constructor() {\n    super(...arguments);\n    this.map = new Map();\n  }\n  // for consistent order\n  handle(customRendering) {\n    const {\n      map\n    } = this;\n    let updated = false;\n    if (customRendering.isActive) {\n      map.set(customRendering.id, customRendering);\n      updated = true;\n    } else if (map.has(customRendering.id)) {\n      map.delete(customRendering.id);\n      updated = true;\n    }\n    if (updated) {\n      this.set(map);\n    }\n  }\n}\nexport { elementClosest as $, memoizeObjArg as A, BaseComponent as B, ContentContainer as C, DelayedRunner as D, isPropsEqual as E, Emitter as F, getInitialDate as G, rangeContainsMarker as H, createEmptyEventStore as I, reduceCurrentDate as J, reduceEventStore as K, rezoneEventStoreDates as L, mergeRawOptions as M, BASE_OPTION_REFINERS as N, CALENDAR_LISTENER_REFINERS as O, CALENDAR_OPTION_REFINERS as P, COMPLEX_OPTION_COMPARATORS as Q, VIEW_OPTION_REFINERS as R, DateEnv as S, Theme as T, DateProfileGenerator as U, ViewContextType as V, createEventUi as W, parseBusinessHours as X, setRef as Y, Interaction as Z, getElSeg as _, mapHash as a, getSlotClassNames as a$, EventImpl as a0, listenBySelector as a1, listenToHoverBySelector as a2, PureComponent as a3, buildViewContext as a4, getUniqueDomId as a5, parseInteractionSettings as a6, interactionSettingsStore as a7, getNow as a8, CalendarImpl as a9, diffDates as aA, removeExact as aB, memoizeArraylike as aC, memoizeHashlike as aD, intersectRects as aE, pointInsideRect as aF, constrainPoint as aG, getRectCenter as aH, diffPoints as aI, translateRect as aJ, compareObjs as aK, collectFromHash as aL, findElements as aM, findDirectChildren as aN, removeElement as aO, applyStyle as aP, elementMatches as aQ, getEventTargetViaRoot as aR, parseClassNames as aS, getCanVGrowWithinCell as aT, mergeEventStores as aU, getRelevantEvents as aV, eventTupleToStore as aW, combineEventUis as aX, Splitter as aY, getDayClassNames as aZ, getDateMeta as a_, flushSync as aa, CalendarRoot as ab, RenderId as ac, ensureElHasStyles as ad, applyStyleProp as ae, sliceEventStore as af, JsonRequestError as ag, createContext as ah, refineProps as ai, createEventInstance as aj, parseEventDef as ak, refineEventDef as al, padStart as am, isInt as an, parseFieldSpecs as ao, compareByFieldSpecs as ap, flexibleCompare as aq, preventSelection as ar, allowSelection as as, preventContextMenu as at, allowContextMenu as au, compareNumbers as av, enableCursor as aw, disableCursor as ax, computeVisibleDayRange as ay, isMultiDayRange as az, buildViewClassNames as b, SimpleScrollGrid as b$, buildNavLinkAttrs as b0, preventDefault as b1, whenTransitionDone as b2, computeInnerRect as b3, computeEdges as b4, getClippingParents as b5, computeRect as b6, rangesEqual as b7, rangesIntersect as b8, rangeContainsRange as b9, SegHierarchy as bA, buildEntryKey as bB, getEntrySpanEnd as bC, binarySearch as bD, groupIntersectingEntries as bE, intersectSpans as bF, interactionSettingsToStore as bG, ElementDragging as bH, config as bI, parseDragMeta as bJ, DayHeader as bK, computeFallbackHeaderFormat as bL, TableDateCell as bM, TableDowCell as bN, DaySeriesModel as bO, hasBgRendering as bP, buildSegTimeText as bQ, sortEventSegs as bR, getSegMeta as bS, buildEventRangeKey as bT, getSegAnchorAttrs as bU, DayTableModel as bV, Slicer as bW, applyMutationToEventStore as bX, isPropsValid as bY, isInteractionValid as bZ, isDateSelectionValid as b_, PositionCache as ba, ScrollController as bb, ElementScrollController as bc, WindowScrollController as bd, DateComponent as be, isDateSpansEqual as bf, addMs as bg, addWeeks as bh, diffWeeks as bi, diffWholeWeeks as bj, diffDayAndTime as bk, diffDays as bl, isValidDate as bm, asCleanDays as bn, multiplyDuration as bo, addDurations as bp, asRoughMinutes as bq, asRoughSeconds as br, asRoughMs as bs, wholeDivideDurations as bt, formatIsoTimeString as bu, formatDayString as bv, buildIsoString as bw, formatIsoMonthStr as bx, NamedTimeZoneImpl as by, parse as bz, greatestDurationDenominator as c, hasShrinkWidth as c0, renderMicroColGroup as c1, getScrollGridClassNames as c2, getSectionClassNames as c3, getSectionHasLiquidHeight as c4, getAllowYScrolling as c5, renderChunkContent as c6, computeShrinkWidth as c7, sanitizeShrinkWidth as c8, isColPropsEqual as c9, renderScrollShim as ca, getStickyFooterScrollbar as cb, getStickyHeaderDates as cc, Scroller as cd, getScrollbarWidths as ce, RefMap as cf, getIsRtlScrollbarOnLeft as cg, NowTimer as ch, ScrollResponder as ci, StandardEvent as cj, NowIndicatorContainer as ck, DayCellContainer as cl, hasCustomDayCellContent as cm, EventContainer as cn, renderFill as co, BgEvent as cp, WeekNumberContainer as cq, MoreLinkContainer as cr, computeEarliestSegStart as cs, ViewContainer as ct, triggerDateSelect as cu, getDefaultEventEnd as cv, injectStyles as cw, buildElAttrs as cx, CustomRenderingStore as cy, createDuration as d, BASE_OPTION_DEFAULTS as e, arrayToHash as f, guid as g, filterHash as h, isArraysEqual as i, buildEventSourceRefiners as j, formatWithOrdinals as k, buildRangeApiWithTimeZone as l, mergeProps as m, identity as n, intersectRanges as o, parseEventSource as p, startOfDay as q, requestJson as r, subtractDurations as s, addDays as t, unpromisify as u, hashValuesToArray as v, buildEventApis as w, createFormatter as x, diffWholeDays as y, memoize as z };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}