{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRequirementGetRequestListForTemplatePost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRequirementGetRequestListForTemplatePost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRequirementGetRequestListForTemplatePost$Plain.PATH = '/api/Requirement/GetRequestListForTemplate';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiRequirementGetRequestListForTemplatePost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\requirement\\api-requirement-get-request-list-for-template-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetListRequirementRequest } from '../../models/get-list-requirement-request';\r\nimport { GetRequirementListResponseBase } from '../../models/get-requirement-list-response-base';\r\n\r\nexport interface ApiRequirementGetRequestListForTemplatePost$Plain$Params {\r\n      body?: GetListRequirementRequest\r\n}\r\n\r\nexport function apiRequirementGetRequestListForTemplatePost$Plain(http: HttpClient, rootUrl: string, params?: ApiRequirementGetRequestListForTemplatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRequirementListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiRequirementGetRequestListForTemplatePost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetRequirementListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiRequirementGetRequestListForTemplatePost$Plain.PATH = '/api/Requirement/GetRequestListForTemplate';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,iDAAiDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAiE,EAAEC,OAAqB;EAC3L,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,iDAAiD,CAACM,IAAI,EAAE,MAAM,CAAC;EACtG,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAAuD;EAChE,CAAC,CAAC,CACH;AACH;AAEAb,iDAAiD,CAACM,IAAI,GAAG,4CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}