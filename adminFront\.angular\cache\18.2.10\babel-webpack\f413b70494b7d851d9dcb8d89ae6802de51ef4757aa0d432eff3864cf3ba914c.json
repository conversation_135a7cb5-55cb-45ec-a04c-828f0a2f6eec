{"ast": null, "code": "import { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class DashboardService {\n  constructor() {}\n  getBuildCaseList() {\n    const mockBuildCases = [{\n      id: 'bc001',\n      name: '陽光花園',\n      code: 'YGH001',\n      totalHouses: 200\n    }, {\n      id: 'bc002',\n      name: '翡翠山莊',\n      code: 'FCSZ002',\n      totalHouses: 150\n    }, {\n      id: 'bc003',\n      name: '黃金海岸',\n      code: 'HJHA003',\n      totalHouses: 180\n    }, {\n      id: 'bc004',\n      name: '水岸人家',\n      code: 'SARJ004',\n      totalHouses: 120\n    }];\n    return of(mockBuildCases);\n  }\n  getDashboardKPI(buildCaseId) {\n    const mockData = {\n      totalHouses: {\n        value: 200,\n        label: '總戶數',\n        icon: 'fas fa-home',\n        color: '#2E86AB'\n      },\n      soldHouses: {\n        value: 180,\n        label: '已售戶數',\n        icon: 'fas fa-handshake',\n        color: '#A23B72',\n        percentage: 90\n      },\n      customizedHouses: {\n        value: 125,\n        label: '客變戶數',\n        icon: 'fas fa-tools',\n        color: '#F18F01',\n        percentage: 69.4\n      },\n      signedHouses: {\n        value: 95,\n        label: '已簽署戶數',\n        icon: 'fas fa-file-signature',\n        color: '#C73E1D',\n        percentage: 76\n      },\n      paidHouses: {\n        value: 85,\n        label: '已付款戶數',\n        icon: 'fas fa-credit-card',\n        color: '#16537e',\n        percentage: 89.5\n      },\n      completionRate: {\n        value: 75.5,\n        label: '整體完成率',\n        icon: 'fas fa-chart-line',\n        color: '#27ae60'\n      }\n    };\n    return of(mockData);\n  }\n  getProgressData() {\n    const mockData = {\n      categories: ['尚未開始', '已閱讀操作說明', '選樣完成', '簽署完成'],\n      series: [{\n        name: '地主戶',\n        data: [20, 15, 30, 45],\n        color: '#3498db'\n      }, {\n        name: '銷售戶',\n        data: [35, 25, 40, 60],\n        color: '#e74c3c'\n      }]\n    };\n    return of(mockData);\n  }\n  getPaymentStatusData() {\n    const mockData = [{\n      name: '已付款',\n      value: 85,\n      percentage: 42.5,\n      amount: 12500\n    }, {\n      name: '未付款',\n      value: 95,\n      percentage: 47.5,\n      amount: 8200\n    }, {\n      name: '無須付款',\n      value: 20,\n      percentage: 10.0,\n      amount: 0\n    }];\n    return of(mockData);\n  }\n  getQuotationTrendData() {\n    const mockData = {\n      months: ['2024-08', '2024-09', '2024-10', '2024-11', '2024-12', '2025-01'],\n      series: [{\n        name: '待報價',\n        data: [25, 30, 22, 18, 12, 8],\n        color: '#f39c12'\n      }, {\n        name: '已報價',\n        data: [15, 25, 35, 40, 45, 50],\n        color: '#3498db'\n      }, {\n        name: '已簽回',\n        data: [5, 10, 15, 25, 35, 42],\n        color: '#27ae60'\n      }]\n    };\n    return of(mockData);\n  }\n  getHeatmapData() {\n    const mockData = {\n      xAxis: ['2房2廳', '3房2廳', '4房2廳', '4房3廳', '5房3廳'],\n      yAxis: ['尚未開始', '已閱讀操作說明', '選樣完成', '簽署完成'],\n      data: [[15, 8, 12, 5, 3],\n      // 尚未開始\n      [10, 15, 8, 12, 5],\n      // 已閱讀操作說明\n      [25, 30, 20, 15, 10],\n      // 選樣完成\n      [20, 25, 15, 18, 12] // 簽署完成\n      ]\n    };\n    return of(mockData);\n  }\n  static {\n    this.ɵfac = function DashboardService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DashboardService,\n      factory: DashboardService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "DashboardService", "constructor", "getBuildCaseList", "mockBuildCases", "id", "name", "code", "totalHouses", "getDashboardKPI", "buildCaseId", "mockData", "value", "label", "icon", "color", "soldHouses", "percentage", "customizedHouses", "signedHouses", "paidHouses", "completionRate", "getProgressData", "categories", "series", "data", "getPaymentStatusData", "amount", "getQuotationTrendData", "months", "getHeatmapData", "xAxis", "yAxis", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\services\\dashboard.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, of } from 'rxjs';\nimport { DashboardKPI, ProgressData, PaymentStatusData, QuotationTrendData, HeatmapData, BuildCase } from '../models/dashboard.interface';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DashboardService {\n\n  constructor() { }\n\n  getBuildCaseList(): Observable<BuildCase[]> {\n    const mockBuildCases: BuildCase[] = [\n      { id: 'bc001', name: '陽光花園', code: 'YGH001', totalHouses: 200 },\n      { id: 'bc002', name: '翡翠山莊', code: 'FCSZ002', totalHouses: 150 },\n      { id: 'bc003', name: '黃金海岸', code: 'HJHA003', totalHouses: 180 },\n      { id: 'bc004', name: '水岸人家', code: 'SARJ004', totalHouses: 120 }\n    ];\n    return of(mockBuildCases);\n  }\n\n  getDashboardKPI(buildCaseId?: string): Observable<DashboardKPI> {\n    const mockData: DashboardKPI = {\n      totalHouses: {\n        value: 200,\n        label: '總戶數',\n        icon: 'fas fa-home',\n        color: '#2E86AB'\n      },\n      soldHouses: {\n        value: 180,\n        label: '已售戶數',\n        icon: 'fas fa-handshake',\n        color: '#A23B72',\n        percentage: 90\n      },\n      customizedHouses: {\n        value: 125,\n        label: '客變戶數',\n        icon: 'fas fa-tools',\n        color: '#F18F01',\n        percentage: 69.4\n      },\n      signedHouses: {\n        value: 95,\n        label: '已簽署戶數',\n        icon: 'fas fa-file-signature',\n        color: '#C73E1D',\n        percentage: 76\n      },\n      paidHouses: {\n        value: 85,\n        label: '已付款戶數',\n        icon: 'fas fa-credit-card',\n        color: '#16537e',\n        percentage: 89.5\n      },\n      completionRate: {\n        value: 75.5,\n        label: '整體完成率',\n        icon: 'fas fa-chart-line',\n        color: '#27ae60'\n      }\n    };\n    return of(mockData);\n  }\n\n  getProgressData(): Observable<ProgressData> {\n    const mockData: ProgressData = {\n      categories: ['尚未開始', '已閱讀操作說明', '選樣完成', '簽署完成'],\n      series: [\n        {\n          name: '地主戶',\n          data: [20, 15, 30, 45],\n          color: '#3498db'\n        },\n        {\n          name: '銷售戶',\n          data: [35, 25, 40, 60],\n          color: '#e74c3c'\n        }\n      ]\n    };\n    return of(mockData);\n  }\n\n  getPaymentStatusData(): Observable<PaymentStatusData[]> {\n    const mockData: PaymentStatusData[] = [\n      { name: '已付款', value: 85, percentage: 42.5, amount: 12500 },\n      { name: '未付款', value: 95, percentage: 47.5, amount: 8200 },\n      { name: '無須付款', value: 20, percentage: 10.0, amount: 0 }\n    ];\n    return of(mockData);\n  }\n\n  getQuotationTrendData(): Observable<QuotationTrendData> {\n    const mockData: QuotationTrendData = {\n      months: ['2024-08', '2024-09', '2024-10', '2024-11', '2024-12', '2025-01'],\n      series: [\n        {\n          name: '待報價',\n          data: [25, 30, 22, 18, 12, 8],\n          color: '#f39c12'\n        },\n        {\n          name: '已報價',\n          data: [15, 25, 35, 40, 45, 50],\n          color: '#3498db'\n        },\n        {\n          name: '已簽回',\n          data: [5, 10, 15, 25, 35, 42],\n          color: '#27ae60'\n        }\n      ]\n    };\n    return of(mockData);\n  }\n\n  getHeatmapData(): Observable<HeatmapData> {\n    const mockData: HeatmapData = {\n      xAxis: ['2房2廳', '3房2廳', '4房2廳', '4房3廳', '5房3廳'],\n      yAxis: ['尚未開始', '已閱讀操作說明', '選樣完成', '簽署完成'],\n      data: [\n        [15, 8, 12, 5, 3],   // 尚未開始\n        [10, 15, 8, 12, 5],  // 已閱讀操作說明\n        [25, 30, 20, 15, 10], // 選樣完成\n        [20, 25, 15, 18, 12]  // 簽署完成\n      ]\n    };\n    return of(mockData);\n  }\n}"], "mappings": "AACA,SAAqBA,EAAE,QAAQ,MAAM;;AAMrC,OAAM,MAAOC,gBAAgB;EAE3BC,YAAA,GAAgB;EAEhBC,gBAAgBA,CAAA;IACd,MAAMC,cAAc,GAAgB,CAClC;MAAEC,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,QAAQ;MAAEC,WAAW,EAAE;IAAG,CAAE,EAC/D;MAAEH,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEC,WAAW,EAAE;IAAG,CAAE,EAChE;MAAEH,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEC,WAAW,EAAE;IAAG,CAAE,EAChE;MAAEH,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEC,WAAW,EAAE;IAAG,CAAE,CACjE;IACD,OAAOR,EAAE,CAACI,cAAc,CAAC;EAC3B;EAEAK,eAAeA,CAACC,WAAoB;IAClC,MAAMC,QAAQ,GAAiB;MAC7BH,WAAW,EAAE;QACXI,KAAK,EAAE,GAAG;QACVC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE;OACR;MACDC,UAAU,EAAE;QACVJ,KAAK,EAAE,GAAG;QACVC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,kBAAkB;QACxBC,KAAK,EAAE,SAAS;QAChBE,UAAU,EAAE;OACb;MACDC,gBAAgB,EAAE;QAChBN,KAAK,EAAE,GAAG;QACVC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,SAAS;QAChBE,UAAU,EAAE;OACb;MACDE,YAAY,EAAE;QACZP,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,uBAAuB;QAC7BC,KAAK,EAAE,SAAS;QAChBE,UAAU,EAAE;OACb;MACDG,UAAU,EAAE;QACVR,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,oBAAoB;QAC1BC,KAAK,EAAE,SAAS;QAChBE,UAAU,EAAE;OACb;MACDI,cAAc,EAAE;QACdT,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,mBAAmB;QACzBC,KAAK,EAAE;;KAEV;IACD,OAAOf,EAAE,CAACW,QAAQ,CAAC;EACrB;EAEAW,eAAeA,CAAA;IACb,MAAMX,QAAQ,GAAiB;MAC7BY,UAAU,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;MAC/CC,MAAM,EAAE,CACN;QACElB,IAAI,EAAE,KAAK;QACXmB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QACtBV,KAAK,EAAE;OACR,EACD;QACET,IAAI,EAAE,KAAK;QACXmB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QACtBV,KAAK,EAAE;OACR;KAEJ;IACD,OAAOf,EAAE,CAACW,QAAQ,CAAC;EACrB;EAEAe,oBAAoBA,CAAA;IAClB,MAAMf,QAAQ,GAAwB,CACpC;MAAEL,IAAI,EAAE,KAAK;MAAEM,KAAK,EAAE,EAAE;MAAEK,UAAU,EAAE,IAAI;MAAEU,MAAM,EAAE;IAAK,CAAE,EAC3D;MAAErB,IAAI,EAAE,KAAK;MAAEM,KAAK,EAAE,EAAE;MAAEK,UAAU,EAAE,IAAI;MAAEU,MAAM,EAAE;IAAI,CAAE,EAC1D;MAAErB,IAAI,EAAE,MAAM;MAAEM,KAAK,EAAE,EAAE;MAAEK,UAAU,EAAE,IAAI;MAAEU,MAAM,EAAE;IAAC,CAAE,CACzD;IACD,OAAO3B,EAAE,CAACW,QAAQ,CAAC;EACrB;EAEAiB,qBAAqBA,CAAA;IACnB,MAAMjB,QAAQ,GAAuB;MACnCkB,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAC1EL,MAAM,EAAE,CACN;QACElB,IAAI,EAAE,KAAK;QACXmB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC7BV,KAAK,EAAE;OACR,EACD;QACET,IAAI,EAAE,KAAK;QACXmB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAC9BV,KAAK,EAAE;OACR,EACD;QACET,IAAI,EAAE,KAAK;QACXmB,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAC7BV,KAAK,EAAE;OACR;KAEJ;IACD,OAAOf,EAAE,CAACW,QAAQ,CAAC;EACrB;EAEAmB,cAAcA,CAAA;IACZ,MAAMnB,QAAQ,GAAgB;MAC5BoB,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;MAC/CC,KAAK,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;MAC1CP,IAAI,EAAE,CACJ,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;MAAI;MACrB,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;MAAG;MACrB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAAE;MACtB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAE;MAAA;KAEzB;IACD,OAAOzB,EAAE,CAACW,QAAQ,CAAC;EACrB;;;uCA5HWV,gBAAgB;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAAgC,OAAA,EAAhBhC,gBAAgB,CAAAiC,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}