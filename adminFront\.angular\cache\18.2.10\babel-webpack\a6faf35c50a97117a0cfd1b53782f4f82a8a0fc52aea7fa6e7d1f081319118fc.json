{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Serbian [sr]\n//! author : <PERSON><<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com> : https://github.com/milan-j\n//! author : <PERSON> <<EMAIL>> : https://github.com/c<PERSON><PERSON><PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var translator = {\n    words: {\n      //Different grammatical cases\n      ss: ['sekunda', 'sekunde', 'sekundi'],\n      m: ['jedan minut', 'jednog minuta'],\n      mm: ['minut', 'minuta', 'minuta'],\n      h: ['jedan sat', 'jednog sata'],\n      hh: ['sat', 'sata', 'sati'],\n      d: ['jedan dan', 'jednog dana'],\n      dd: ['dan', 'dana', 'dana'],\n      M: ['jedan mesec', 'jednog meseca'],\n      MM: ['mesec', 'meseca', 'meseci'],\n      y: ['jednu godinu', 'jedne godine'],\n      yy: ['godinu', 'godine', 'godina']\n    },\n    correctGrammaticalCase: function (number, wordKey) {\n      if (number % 10 >= 1 && number % 10 <= 4 && (number % 100 < 10 || number % 100 >= 20)) {\n        return number % 10 === 1 ? wordKey[0] : wordKey[1];\n      }\n      return wordKey[2];\n    },\n    translate: function (number, withoutSuffix, key, isFuture) {\n      var wordKey = translator.words[key],\n        word;\n      if (key.length === 1) {\n        // Nominativ\n        if (key === 'y' && withoutSuffix) return 'jedna godina';\n        return isFuture || withoutSuffix ? wordKey[0] : wordKey[1];\n      }\n      word = translator.correctGrammaticalCase(number, wordKey);\n      // Nominativ\n      if (key === 'yy' && withoutSuffix && word === 'godinu') {\n        return number + ' godina';\n      }\n      return number + ' ' + word;\n    }\n  };\n  var sr = moment.defineLocale('sr', {\n    months: 'januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar'.split('_'),\n    monthsShort: 'jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'nedelja_ponedeljak_utorak_sreda_četvrtak_petak_subota'.split('_'),\n    weekdaysShort: 'ned._pon._uto._sre._čet._pet._sub.'.split('_'),\n    weekdaysMin: 'ne_po_ut_sr_če_pe_su'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'D. M. YYYY.',\n      LL: 'D. MMMM YYYY.',\n      LLL: 'D. MMMM YYYY. H:mm',\n      LLLL: 'dddd, D. MMMM YYYY. H:mm'\n    },\n    calendar: {\n      sameDay: '[danas u] LT',\n      nextDay: '[sutra u] LT',\n      nextWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[u] [nedelju] [u] LT';\n          case 3:\n            return '[u] [sredu] [u] LT';\n          case 6:\n            return '[u] [subotu] [u] LT';\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[u] dddd [u] LT';\n        }\n      },\n      lastDay: '[juče u] LT',\n      lastWeek: function () {\n        var lastWeekDays = ['[prošle] [nedelje] [u] LT', '[prošlog] [ponedeljka] [u] LT', '[prošlog] [utorka] [u] LT', '[prošle] [srede] [u] LT', '[prošlog] [četvrtka] [u] LT', '[prošlog] [petka] [u] LT', '[prošle] [subote] [u] LT'];\n        return lastWeekDays[this.day()];\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'za %s',\n      past: 'pre %s',\n      s: 'nekoliko sekundi',\n      ss: translator.translate,\n      m: translator.translate,\n      mm: translator.translate,\n      h: translator.translate,\n      hh: translator.translate,\n      d: translator.translate,\n      dd: translator.translate,\n      M: translator.translate,\n      MM: translator.translate,\n      y: translator.translate,\n      yy: translator.translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return sr;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}