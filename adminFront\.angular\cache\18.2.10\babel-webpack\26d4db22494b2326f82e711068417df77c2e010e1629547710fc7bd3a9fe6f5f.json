{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nlet DetailContentManagementSalesAccountComponent = class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService, _houseService, cdr) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this._houseService = _houseService;\n    this.cdr = cdr;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    // 通知類型選項映射\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: EnumHouseType.地主戶\n    }, {\n      label: '銷售戶',\n      value: EnumHouseType.銷售戶\n    }];\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.isSubmitting = false;\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    // 新增：戶別選擇器相關屬性\n    this.buildingData = {}; // 存放建築物戶別資料\n    this.listFormItem = null;\n    this.isNew = true;\n    this.arrListFormItemReq = [];\n    this.filteredArrListFormItemReq = [];\n    this.searchQuery = '';\n  }\n  // 動態獲取標題文字\n  get dynamicTitle() {\n    const option = this.cNoticeTypeOptions.find(option => option.value === this.typeContentManagementSalesAccount.CNoticeType);\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\n  }\n  // 設置通知類型（可供外部調用）\n  setCNoticeType(noticeType) {\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\n      // 同時設定 CFormType 以保持一致性\n      this.typeContentManagementSalesAccount.CFormType = noticeType;\n    }\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId > 0) {\n          this.getListRegularNoticeFileHouseHold();\n        } else {\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\n          console.error('Invalid buildCaseId:', this.buildCaseId);\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\n          this.goBack();\n        }\n      }\n    });\n    // 處理查詢參數中的戶型\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['houseType']) {\n        const houseType = +queryParams['houseType'];\n        this.setCNoticeType(houseType);\n      }\n    });\n  }\n  ngOnDestroy() {\n    // 確保在組件銷毀時恢復body的滾動\n    document.body.style.overflow = 'auto';\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      formItemReq.isModalOpen = true;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\n  extractHouseholdCodes(households) {\n    if (!households || !Array.isArray(households)) {\n      return [];\n    }\n    return households.map(h => h.code || h);\n  }\n  // 新增：處理戶別選擇變更\n  onHouseholdSelectionChange(selectedHouseholds, formItemReq) {\n    // 重置所有戶別選擇狀態\n    Object.keys(formItemReq.selectedItems).forEach(key => {\n      formItemReq.selectedItems[key] = false;\n    });\n    // 設置選中的戶別\n    selectedHouseholds.forEach(household => {\n      formItemReq.selectedItems[household] = true;\n    });\n    // 更新全選狀態\n    formItemReq.allSelected = this.houseHoldList.length > 0 && this.houseHoldList.every(item => formItemReq.selectedItems[item]);\n    // 更新緩存\n    this.updateSelectedHouseholdsCache(formItemReq);\n  }\n  // 新增：取得已選戶別數組\n  getSelectedHouseholds(formItemReq) {\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\n  }\n  // 新增：更新已選戶別緩存\n  updateSelectedHouseholdsCache(formItemReq) {\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\n  }\n  // 新增：更新所有項目的緩存\n  updateAllSelectedHouseholdsCache() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(formItemReq => {\n        this.updateSelectedHouseholdsCache(formItemReq);\n      });\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            currentImageIndex: 0,\n            isModalOpen: false,\n            isCollapsed: true,\n            // 新項目默認收合\n            CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64).filter(url => url != null) : []\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n        // 初始化過濾列表\n        this.updateFilteredList();\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem?.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0,\n              isModalOpen: false,\n              isCollapsed: true,\n              // 現有項目默認收合\n              selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\n            };\n          });\n          // 初始化過濾列表\n          this.updateFilteredList();\n          // 手動觸發變更檢測\n          this.cdr.detectChanges();\n        } else {\n          // 當無資料時，載入材料清單供新增使用\n          this.getMaterialList();\n        }\n        // 初始化所有項目的緩存\n        this.updateAllSelectedHouseholdsCache();\n        // 最終觸發變更檢測\n        this.cdr.detectChanges();\n      } else {\n        console.error('getListFormItem failed:', res);\n      }\n    })).subscribe({\n      error: error => {\n        console.error('getListFormItem error:', error);\n      }\n    });\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    // 設置提交狀態\n    this.isSubmitting = true;\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem?.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      // 滾動到第一個有錯誤的項目\n      this.scrollToFirstErrorItem();\n      this.isSubmitting = false;\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe({\n      next: res => {\n        this.isSubmitting = false;\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          // this.getListFormItem()\n          this.goBack();\n        }\n      },\n      error: error => {\n        this.isSubmitting = false;\n        console.error('Save error:', error);\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe({\n      next: res => {\n        this.isSubmitting = false;\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          // this.getListFormItem()\n          this.goBack();\n        }\n      },\n      error: error => {\n        this.isSubmitting = false;\n        console.error('Create error:', error);\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  /**\n   * 複製當前表單到新表單\n   */\n  copyToNewForm() {\n    // 先取得當前有效的材料清單\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        // 建立有效材料清單的鍵值對應\n        const validMaterialKeys = new Set();\n        res.Entries.forEach(material => {\n          const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\n          validMaterialKeys.add(key);\n        });\n        // 篩選出仍然有效的表單項目\n        const validFormItems = this.arrListFormItemReq.filter(item => {\n          const itemKey = `${item.CLocation}_${item.CName}_${item.CPart}`;\n          return validMaterialKeys.has(itemKey);\n        });\n        if (validFormItems.length === 0) {\n          this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\n          return;\n        }\n        // 準備複製的表單項目數據\n        this.saveListFormItemReq = validFormItems.map(e => {\n          return {\n            CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n            CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n            CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n            CFormItemId: null,\n            // 設為 null 以建立新項目\n            CFormID: null,\n            // 設為 null 以建立新表單\n            CName: e.CName,\n            CPart: e.CPart,\n            CLocation: e.CLocation,\n            CItemName: e.CItemName,\n            CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n            CTotalAnswer: e.CTotalAnswer,\n            CRequireAnswer: e.CRequireAnswer,\n            CUiType: e.selectedCUiType.value\n          };\n        });\n        // 執行驗證\n        this.validation();\n        if (this.valid.errorMessages.length > 0) {\n          this.message.showErrorMSGs(this.valid.errorMessages);\n          return;\n        }\n        // 建立複製的表單\n        this.creatListFormItem = {\n          CBuildCaseId: this.buildCaseId,\n          CFormItem: this.saveListFormItemReq || null,\n          CFormType: this.typeContentManagementSalesAccount.CFormType\n        };\n        this._formItemService.apiFormItemCreateListFormItemPost$Json({\n          body: this.creatListFormItem\n        }).subscribe(createRes => {\n          if (createRes.StatusCode == 0) {\n            this.message.showSucessMSG(`複製表單成功，已篩選 ${validFormItems.length} 個有效項目`);\n            // 重新載入資料以顯示新的未鎖定表單\n            this.getListFormItem();\n          }\n        });\n      } else {\n        this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\n      }\n    })).subscribe();\n  }\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\n  loadBuildingDataFromAPI() {\n    if (!this.buildCaseId) return;\n    this._houseService.apiHouseGetDropDownPost$Json({\n      buildCaseId: this.buildCaseId\n    }).subscribe({\n      next: response => {\n        console.log('GetDropDown API response:', response);\n        if (response.Entries) {\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n          console.log('Converted buildingData:', this.buildingData);\n        }\n      },\n      error: error => {\n        console.error('Error loading building data from API:', error);\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\n        }\n      }\n    });\n  }\n  // 新增：將 API 回應轉換為建築物資料格式\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        code: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseName: house.HouseName,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  // 新增：將戶別清單轉換為建築物資料格式\n  convertHouseHoldListToBuildingData(houseHoldList) {\n    if (!houseHoldList || houseHoldList.length === 0) {\n      return {};\n    }\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\n    const buildingData = {};\n    houseHoldList.forEach(household => {\n      // 嘗試從戶別名稱中提取建築物代碼\n      const buildingMatch = household.match(/^([A-Z]+)/);\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\n      if (!buildingData[building]) {\n        buildingData[building] = [];\n      }\n      // 計算樓層（假設每4戶為一層）\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\n      const floor = Math.ceil(houseNumber / 4);\n      buildingData[building].push({\n        code: household,\n        building: building,\n        floor: `${floor}F`,\n        isSelected: false,\n        isDisabled: false\n      });\n    });\n    return buildingData;\n  }\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        // 載入建築物資料 (只呼叫一次 GetDropDown API)\n        this.loadBuildingDataFromAPI();\n        this.getListFormItem();\n      } else {\n        console.error('getListRegularNoticeFileHouseHold failed:', res);\n      }\n    })).subscribe({\n      error: error => {\n        console.error('getListRegularNoticeFileHouseHold error:', error);\n      }\n    });\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  // UI優化相關方法\n  /**\n   * 檢查項目是否已完成\n   */\n  isItemCompleted(formItemReq) {\n    // 檢查必填欄位是否都已填寫\n    const hasItemName = !!formItemReq.CItemName && formItemReq.CItemName.trim() !== '';\n    const hasUiType = !!formItemReq.selectedCUiType && formItemReq.selectedCUiType.value;\n    const hasRequireAnswer = !!formItemReq.CRequireAnswer && formItemReq.CRequireAnswer > 0;\n    // 如果是建材選樣類型，檢查是否有選擇備註類型\n    let hasRemarkType = true;\n    if (formItemReq.selectedCUiType?.value === 3) {\n      hasRemarkType = !!formItemReq.selectedRemarkType && Object.values(formItemReq.selectedRemarkType).some(selected => selected);\n    }\n    return hasItemName && hasUiType && hasRequireAnswer && hasRemarkType;\n  }\n  /**\n   * 獲取已完成項目數量\n   */\n  getCompletedItemsCount() {\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\n    return this.arrListFormItemReq.filter(item => this.isItemCompleted(item)).length;\n  }\n  /**\n   * 獲取進度百分比\n   */\n  getProgressPercentage() {\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\n    const completed = this.getCompletedItemsCount();\n    return Math.round(completed / this.arrListFormItemReq.length * 100);\n  }\n  /**\n   * 滾動到指定項目\n   */\n  scrollToItem(index) {\n    const element = document.getElementById(`form-item-${index}`);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start',\n        inline: 'nearest'\n      });\n      // 添加高亮效果\n      element.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-75');\n      setTimeout(() => {\n        element.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-75');\n      }, 2000);\n    }\n  }\n  /**\n   * 滾動到第一個未完成的項目\n   */\n  scrollToFirstIncompleteItem() {\n    if (!this.arrListFormItemReq) return;\n    const firstIncompleteIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\n    if (firstIncompleteIndex !== -1) {\n      this.scrollToItem(firstIncompleteIndex);\n    }\n  }\n  /**\n   * 滾動到第一個有錯誤的項目\n   */\n  scrollToFirstErrorItem() {\n    if (!this.arrListFormItemReq) return;\n    // 找到第一個有錯誤的項目\n    const firstErrorIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\n    if (firstErrorIndex !== -1) {\n      this.scrollToItem(firstErrorIndex);\n    }\n  }\n  /**\n   * 滾動到頂部\n   */\n  scrollToTop() {\n    window.scrollTo(0, 0);\n  }\n  /**\n   * 回到頂部\n   */\n  goToTop() {\n    console.log('goToTop clicked - method called');\n    // 優先滾動到頁面最頂部的header區塊\n    const headerElement = document.querySelector('nb-card-header') || document.querySelector('.card-header') || document.querySelector('nb-card') || document.querySelector('.header') || document.querySelector('h1, h2, h3') || document.body.firstElementChild;\n    if (headerElement) {\n      headerElement.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n      console.log('Scrolled to header element:', headerElement.tagName);\n      // 額外向上滾動一點，確保header完全可見\n      setTimeout(() => {\n        window.scrollBy({\n          top: -50,\n          // 向上滾動50px\n          behavior: 'smooth'\n        });\n      }, 500);\n      return;\n    }\n    // 備用方案：滾動到第一個表單項目的上方\n    if (this.arrListFormItemReq && this.arrListFormItemReq.length > 0) {\n      const firstElement = document.getElementById('form-item-0');\n      if (firstElement) {\n        firstElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        });\n        // 向上滾動更多距離，確保看到header\n        setTimeout(() => {\n          window.scrollBy({\n            top: -200,\n            // 向上滾動200px\n            behavior: 'smooth'\n          });\n        }, 500);\n        console.log('Scrolled to first form item with extra offset');\n        return;\n      }\n    }\n    // 最後的備用方法\n    console.log('Using fallback scroll methods');\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  }\n  /**\n   * 滾動到底部 - 滾動到 footer 資訊區塊\n   */\n  scrollToBottom() {\n    console.log('=== 至底功能被點擊 - 滾動到 footer 資訊區塊 ===');\n    // 立即顯示一個簡短的視覺反饋\n    const button = document.querySelector('button[title=\"到底部\"]');\n    if (button) {\n      button.style.transform = 'scale(0.95)';\n      setTimeout(() => {\n        button.style.transform = '';\n      }, 150);\n    }\n    // 滾動到 footer 資訊區塊\n    setTimeout(() => {\n      // 方法1: 滾動到 nb-card-footer 元素\n      const footerElement = document.querySelector('nb-card-footer');\n      if (footerElement) {\n        console.log('找到 footer 元素，滾動到 footer 資訊區塊');\n        footerElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          // 改為 center 讓 footer 在畫面中央\n          inline: 'nearest'\n        });\n        console.log('已滾動到 footer 資訊區塊');\n        return;\n      }\n      // 方法2: 尋找包含統計資訊的 footer 區域（使用更精確的選擇器）\n      const progressFooter = document.querySelector('nb-card-footer .flex.items-center.justify-center');\n      if (progressFooter) {\n        console.log('找到統計資訊 footer，滾動到該區域');\n        progressFooter.scrollIntoView({\n          behavior: 'smooth',\n          block: 'center',\n          inline: 'nearest'\n        });\n        console.log('已滾動到統計資訊區域');\n        return;\n      }\n      // 方法3: 滾動到最後一個表單項目\n      if (this.filteredArrListFormItemReq && this.filteredArrListFormItemReq.length > 0) {\n        const lastIndex = this.filteredArrListFormItemReq.length - 1;\n        const lastElement = document.getElementById(`form-item-${lastIndex}`);\n        if (lastElement) {\n          console.log(`找到最後一個表單項目: form-item-${lastIndex}`);\n          lastElement.scrollIntoView({\n            behavior: 'smooth',\n            block: 'end',\n            inline: 'nearest'\n          });\n          // 額外向下滾動到 footer 區域\n          setTimeout(() => {\n            window.scrollBy({\n              top: 200,\n              // 增加滾動距離以確保看到 footer\n              behavior: 'smooth'\n            });\n          }, 500);\n          console.log('已滾動到最後一個表單項目並向下偏移到 footer');\n          return;\n        }\n      }\n      // 備用方法: 滾動到頁面底部\n      console.log('使用備用方法：滾動到頁面底部');\n      window.scrollTo({\n        top: document.body.scrollHeight,\n        behavior: 'smooth'\n      });\n    }, 100);\n  }\n  /**\n   * 切換項目收合狀態\n   */\n  toggleItemCollapse(formItemReq) {\n    formItemReq.isCollapsed = !formItemReq.isCollapsed;\n  }\n  /**\n   * 全部展開\n   */\n  expandAll() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = false;\n      });\n      this.cdr.detectChanges();\n    }\n  }\n  /**\n   * 全部收合\n   */\n  collapseAll() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = true;\n      });\n      this.cdr.detectChanges();\n    }\n  }\n  /**\n   * 只展開未完成的項目\n   */\n  expandIncompleteOnly() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(item => {\n        item.isCollapsed = this.isItemCompleted(item);\n      });\n      this.cdr.detectChanges();\n    }\n  }\n  /**\n   * 搜尋功能\n   */\n  onSearch() {\n    if (!this.searchQuery.trim()) {\n      this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\n    } else {\n      const query = this.searchQuery.toLowerCase().trim();\n      this.filteredArrListFormItemReq = this.arrListFormItemReq.filter(item => {\n        return item.CName?.toLowerCase().includes(query) || item.CPart?.toLowerCase().includes(query) || item.CLocation?.toLowerCase().includes(query) || item.CItemName?.toLowerCase().includes(query);\n      });\n    }\n    this.cdr.detectChanges();\n  }\n  /**\n   * 清除搜尋\n   */\n  clearSearch() {\n    this.searchQuery = '';\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\n    this.cdr.detectChanges();\n  }\n  /**\n   * 更新過濾列表\n   */\n  updateFilteredList() {\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\n    if (this.searchQuery.trim()) {\n      this.onSearch();\n    }\n  }\n};\nDetailContentManagementSalesAccountComponent = __decorate([Component({\n  selector: 'ngx-detail-content-management-sales-account',\n  templateUrl: './detail-content-management-sales-account.component.html',\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\n  standalone: true,\n  imports: [CommonModule, FormsModule, SharedModule, AppSharedModule, NbCheckboxModule, Base64ImagePipe],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], DetailContentManagementSalesAccountComponent);\nexport { DetailContentManagementSalesAccountComponent };", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "CommonModule", "FormsModule", "NbCheckboxModule", "tap", "SharedModule", "AppSharedModule", "BaseComponent", "Base64ImagePipe", "EnumHouseType", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "_houseService", "cdr", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "cNoticeTypeOptions", "label", "value", "地主戶", "銷售戶", "CUiTypeOptions", "CRemarkTypeOptions", "isSubmitting", "selectedItems", "selectedRemarkType", "buildingData", "listFormItem", "isNew", "arrListFormItemReq", "filteredArrListFormItemReq", "searchQuery", "dynamicTitle", "option", "find", "setCNoticeType", "noticeType", "some", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "getListRegularNoticeFileHouseHold", "console", "error", "showErrorMSG", "goBack", "queryParams", "houseType", "ngOnDestroy", "document", "body", "style", "overflow", "getItemByValue", "options", "item", "detectFiles", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "listPictures", "length", "Date", "getTime", "name", "split", "data", "extension", "getFileExtension", "CFile", "push", "removeImage", "pictureId", "filter", "x", "renameFile", "index", "blob", "slice", "size", "type", "newFile", "File", "nextImage", "formItemReq", "CMatrialUrl", "currentImageIndex", "prevImage", "getCurrentImage", "undefined", "openImageModal", "imageIndex", "isModalOpen", "closeImageModal", "nextImageModal", "prevImageModal", "onKeydown", "key", "preventDefault", "extractHouseholdCodes", "households", "Array", "isArray", "map", "h", "code", "onHouseholdSelectionChange", "selectedHouseholds", "Object", "keys", "for<PERSON>ach", "household", "allSelected", "houseHoldList", "every", "updateSelectedHouseholdsCache", "getSelectedHouseholds", "selectedHouseholdsCached", "updateAllSelectedHouseholdsCache", "onCheckboxRemarkChange", "checked", "createRemarkObject", "CRemarkType", "remarkObject", "remarkTypes", "includes", "mergeItems", "items", "Map", "CLocation", "CName", "<PERSON>art", "has", "existing", "count", "set", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "o", "CDesignFileUrl", "CFormItemHouseHold", "CFormId", "CItemName", "CRequireAnswer", "CUiType", "selectedCUiType", "isCollapsed", "CSelectPicture", "x1", "CBase64", "url", "updateFilteredList", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFirstMatrialUrl", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "detectChanges", "changeSelectCUiType", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "scrollToFirstErrorItem", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "next", "showSucessMSG", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "bItem", "CHousehold", "CIsSelect", "copyToNewForm", "validMaterialKeys", "Set", "material", "add", "validFormItems", "itemKey", "createRes", "loadBuildingDataFromAPI", "apiHouseGetDropDownPost$Json", "response", "log", "convertApiResponseToBuildingData", "convertHouseHoldListToBuildingData", "entries", "building", "houses", "house", "HouseName", "Building", "floor", "Floor", "houseId", "HouseId", "houseName", "isSelected", "isDisabled", "buildingMatch", "match", "houseNumber", "parseInt", "replace", "Math", "ceil", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "isItemCompleted", "hasItemName", "trim", "hasUiType", "hasRequireAnswer", "hasRemarkType", "selected", "getCompletedItemsCount", "getProgressPercentage", "completed", "round", "scrollToItem", "element", "getElementById", "scrollIntoView", "behavior", "block", "inline", "classList", "setTimeout", "remove", "scrollToFirstIncompleteItem", "firstIncompleteIndex", "findIndex", "firstErrorIndex", "scrollToTop", "window", "scrollTo", "goToTop", "headerElement", "querySelector", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "tagName", "scrollBy", "top", "firstElement", "scrollToBottom", "button", "transform", "footerElement", "progressFooter", "lastIndex", "lastElement", "scrollHeight", "toggleItemCollapse", "expandAll", "collapseAll", "expandIncompleteOnly", "onSearch", "query", "toLowerCase", "clearSearch", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService, HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq, GetMaterialListResponse } from 'src/services/api/models';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n  selectedHouseholdsCached?: string[]; // 緩存已選戶別，避免重複計算\r\n  isCollapsed?: boolean; // 是否收合狀態\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, SharedModule, AppSharedModule, NbCheckboxModule, Base64ImagePipe],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit, OnDestroy {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService,\r\n    private _houseService: HouseService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    super(_allow)\r\n  }\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n  // 通知類型選項映射\r\n  cNoticeTypeOptions = [\r\n    { label: '地主戶', value: EnumHouseType.地主戶 },\r\n    { label: '銷售戶', value: EnumHouseType.銷售戶 }\r\n  ];\r\n  // 動態獲取標題文字\r\n  get dynamicTitle(): string {\r\n    const option = this.cNoticeTypeOptions.find(option =>\r\n      option.value === this.typeContentManagementSalesAccount.CNoticeType\r\n    );\r\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\r\n  }\r\n  // 設置通知類型（可供外部調用）\r\n  setCNoticeType(noticeType: EnumHouseType): void {\r\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\r\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\r\n      // 同時設定 CFormType 以保持一致性\r\n      this.typeContentManagementSalesAccount.CFormType = noticeType;\r\n    }\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }];\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"];\r\n  buildCaseId: number;\r\n  isSubmitting: boolean = false;\r\n\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n\r\n        if (this.buildCaseId > 0) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        } else {\r\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\r\n          console.error('Invalid buildCaseId:', this.buildCaseId);\r\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\r\n          this.goBack();\r\n        }\r\n      }\r\n    });\r\n\r\n    // 處理查詢參數中的戶型\r\n    this.route.queryParams.subscribe(queryParams => {\r\n      if (queryParams['houseType']) {\r\n        const houseType = +queryParams['houseType'];\r\n        this.setCNoticeType(houseType);\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保在組件銷毀時恢復body的滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n  // 新增：戶別選擇器相關屬性\r\n  buildingData: any = {}; // 存放建築物戶別資料\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      formItemReq.isModalOpen = true;\r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\r\n  extractHouseholdCodes(households: any[]): string[] {\r\n    if (!households || !Array.isArray(households)) {\r\n      return [];\r\n    }\r\n    return households.map(h => h.code || h);\r\n  }\r\n  // 新增：處理戶別選擇變更\r\n  onHouseholdSelectionChange(selectedHouseholds: string[], formItemReq: any) {\r\n    // 重置所有戶別選擇狀態\r\n    Object.keys(formItemReq.selectedItems).forEach(key => {\r\n      formItemReq.selectedItems[key] = false;\r\n    });\r\n\r\n    // 設置選中的戶別\r\n    selectedHouseholds.forEach(household => {\r\n      formItemReq.selectedItems[household] = true;\r\n    });\r\n\r\n    // 更新全選狀態\r\n    formItemReq.allSelected = this.houseHoldList.length > 0 &&\r\n      this.houseHoldList.every(item => formItemReq.selectedItems[item]);\r\n\r\n    // 更新緩存\r\n    this.updateSelectedHouseholdsCache(formItemReq);\r\n  }\r\n\r\n  // 新增：取得已選戶別數組\r\n  getSelectedHouseholds(formItemReq: any): string[] {\r\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\r\n  }\r\n\r\n  // 新增：更新已選戶別緩存\r\n  private updateSelectedHouseholdsCache(formItemReq: any): void {\r\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\r\n  }\r\n\r\n  // 新增：更新所有項目的緩存\r\n  private updateAllSelectedHouseholdsCache(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(formItemReq => {\r\n        this.updateSelectedHouseholdsCache(formItemReq);\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: GetMaterialListResponse) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0, selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [], selectedCUiType: this.CUiTypeOptions[0],\r\n              currentImageIndex: 0,\r\n              isModalOpen: false,\r\n              isCollapsed: true, // 新項目默認收合\r\n              CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64).filter((url: any) => url != null) as string[] : []\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n\r\n          // 初始化過濾列表\r\n          this.updateFilteredList()\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes | null = null\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem?.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [], selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0,\r\n                isModalOpen: false,\r\n                isCollapsed: true, // 現有項目默認收合\r\n                selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\r\n              }\r\n            })\r\n\r\n            // 初始化過濾列表\r\n            this.updateFilteredList();\r\n\r\n            // 手動觸發變更檢測\r\n            this.cdr.detectChanges();\r\n          } else {\r\n            // 當無資料時，載入材料清單供新增使用\r\n            this.getMaterialList();\r\n          }\r\n\r\n          // 初始化所有項目的緩存\r\n          this.updateAllSelectedHouseholdsCache();\r\n\r\n          // 最終觸發變更檢測\r\n          this.cdr.detectChanges();\r\n        } else {\r\n          console.error('getListFormItem failed:', res);\r\n        }\r\n      })\r\n    ).subscribe({\r\n      error: (error) => {\r\n        console.error('getListFormItem error:', error);\r\n      }\r\n    })\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[] = []\r\n  filteredArrListFormItemReq: ExtendedSaveListFormItemReq[] = []\r\n  searchQuery: string = ''\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    // 設置提交狀態\r\n    this.isSubmitting = true;\r\n\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem?.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      // 滾動到第一個有錯誤的項目\r\n      this.scrollToFirstErrorItem();\r\n      this.isSubmitting = false;\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe({\r\n      next: (res) => {\r\n        this.isSubmitting = false;\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          // this.getListFormItem()\r\n          this.goBack()\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isSubmitting = false;\r\n        console.error('Save error:', error);\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe({\r\n      next: (res) => {\r\n        this.isSubmitting = false;\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          // this.getListFormItem()\r\n          this.goBack()\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isSubmitting = false;\r\n        console.error('Create error:', error);\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n  /**\r\n   * 複製當前表單到新表單\r\n   */\r\n  copyToNewForm() {\r\n    // 先取得當前有效的材料清單\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          // 建立有效材料清單的鍵值對應\r\n          const validMaterialKeys = new Set<string>();\r\n          res.Entries.forEach((material: any) => {\r\n            const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\r\n            validMaterialKeys.add(key);\r\n          });\r\n\r\n          // 篩選出仍然有效的表單項目\r\n          const validFormItems = this.arrListFormItemReq.filter((item: any) => {\r\n            const itemKey = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n            return validMaterialKeys.has(itemKey);\r\n          });\r\n\r\n          if (validFormItems.length === 0) {\r\n            this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\r\n            return;\r\n          }\r\n\r\n          // 準備複製的表單項目數據\r\n          this.saveListFormItemReq = validFormItems.map((e: any) => {\r\n            return {\r\n              CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n              CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n              CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n              CFormItemId: null, // 設為 null 以建立新項目\r\n              CFormID: null, // 設為 null 以建立新表單\r\n              CName: e.CName,\r\n              CPart: e.CPart,\r\n              CLocation: e.CLocation,\r\n              CItemName: e.CItemName,\r\n              CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n              CTotalAnswer: e.CTotalAnswer,\r\n              CRequireAnswer: e.CRequireAnswer,\r\n              CUiType: e.selectedCUiType.value,\r\n            }\r\n          });\r\n\r\n          // 執行驗證\r\n          this.validation()\r\n          if (this.valid.errorMessages.length > 0) {\r\n            this.message.showErrorMSGs(this.valid.errorMessages);\r\n            return\r\n          }\r\n\r\n          // 建立複製的表單\r\n          this.creatListFormItem = {\r\n            CBuildCaseId: this.buildCaseId,\r\n            CFormItem: this.saveListFormItemReq || null,\r\n            CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n          }\r\n\r\n          this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n            body: this.creatListFormItem\r\n          }).subscribe(createRes => {\r\n            if (createRes.StatusCode == 0) {\r\n              this.message.showSucessMSG(`複製表單成功，已篩選 ${validFormItems.length} 個有效項目`);\r\n              // 重新載入資料以顯示新的未鎖定表單\r\n              this.getListFormItem()\r\n            }\r\n          })\r\n        } else {\r\n          this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\r\n  private loadBuildingDataFromAPI(): void {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this._houseService.apiHouseGetDropDownPost$Json({ buildCaseId: this.buildCaseId }).subscribe({\r\n      next: (response) => {\r\n        console.log('GetDropDown API response:', response);\r\n        if (response.Entries) {\r\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n          console.log('Converted buildingData:', this.buildingData);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading building data from API:', error);\r\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\r\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\r\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：將 API 回應轉換為建築物資料格式\r\n  private convertApiResponseToBuildingData(entries: any): any {\r\n    const buildingData: any = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]: [string, any]) => {\r\n      buildingData[building] = houses.map((house: any) => ({\r\n        code: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseName: house.HouseName,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  // 新增：將戶別清單轉換為建築物資料格式\r\n  convertHouseHoldListToBuildingData(houseHoldList: string[]): any {\r\n    if (!houseHoldList || houseHoldList.length === 0) {\r\n      return {};\r\n    }\r\n\r\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\r\n    const buildingData: any = {};\r\n\r\n    houseHoldList.forEach(household => {\r\n      // 嘗試從戶別名稱中提取建築物代碼\r\n      const buildingMatch = household.match(/^([A-Z]+)/);\r\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\r\n\r\n      if (!buildingData[building]) {\r\n        buildingData[building] = [];\r\n      }\r\n\r\n      // 計算樓層（假設每4戶為一層）\r\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\r\n      const floor = Math.ceil(houseNumber / 4);\r\n\r\n      buildingData[building].push({\r\n        code: household,\r\n        building: building,\r\n        floor: `${floor}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      });\r\n    }); return buildingData;\r\n  }\r\n\r\n  houseHoldList: any[];\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n\r\n          // 載入建築物資料 (只呼叫一次 GetDropDown API)\r\n          this.loadBuildingDataFromAPI();\r\n\r\n          this.getListFormItem()\r\n        } else {\r\n          console.error('getListRegularNoticeFileHouseHold failed:', res);\r\n        }\r\n      })\r\n    ).subscribe({\r\n      error: (error) => {\r\n        console.error('getListRegularNoticeFileHouseHold error:', error);\r\n      }\r\n    })\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  // UI優化相關方法\r\n\r\n  /**\r\n   * 檢查項目是否已完成\r\n   */\r\n  isItemCompleted(formItemReq: ExtendedSaveListFormItemReq): boolean {\r\n    // 檢查必填欄位是否都已填寫\r\n    const hasItemName = !!formItemReq.CItemName && formItemReq.CItemName.trim() !== '';\r\n    const hasUiType = !!formItemReq.selectedCUiType && formItemReq.selectedCUiType.value;\r\n    const hasRequireAnswer = !!formItemReq.CRequireAnswer && formItemReq.CRequireAnswer > 0;\r\n\r\n    // 如果是建材選樣類型，檢查是否有選擇備註類型\r\n    let hasRemarkType = true;\r\n    if (formItemReq.selectedCUiType?.value === 3) {\r\n      hasRemarkType = !!formItemReq.selectedRemarkType &&\r\n        Object.values(formItemReq.selectedRemarkType).some(selected => selected);\r\n    }\r\n\r\n    return hasItemName && hasUiType && hasRequireAnswer && hasRemarkType;\r\n  }\r\n\r\n  /**\r\n   * 獲取已完成項目數量\r\n   */\r\n  getCompletedItemsCount(): number {\r\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\r\n    return this.arrListFormItemReq.filter(item => this.isItemCompleted(item)).length;\r\n  }\r\n\r\n  /**\r\n   * 獲取進度百分比\r\n   */\r\n  getProgressPercentage(): number {\r\n    if (!this.arrListFormItemReq || this.arrListFormItemReq.length === 0) return 0;\r\n    const completed = this.getCompletedItemsCount();\r\n    return Math.round((completed / this.arrListFormItemReq.length) * 100);\r\n  }\r\n\r\n  /**\r\n   * 滾動到指定項目\r\n   */\r\n  scrollToItem(index: number): void {\r\n    const element = document.getElementById(`form-item-${index}`);\r\n    if (element) {\r\n      element.scrollIntoView({\r\n        behavior: 'smooth',\r\n        block: 'start',\r\n        inline: 'nearest'\r\n      });\r\n\r\n      // 添加高亮效果\r\n      element.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-75');\r\n      setTimeout(() => {\r\n        element.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-75');\r\n      }, 2000);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到第一個未完成的項目\r\n   */\r\n  scrollToFirstIncompleteItem(): void {\r\n    if (!this.arrListFormItemReq) return;\r\n\r\n    const firstIncompleteIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\r\n    if (firstIncompleteIndex !== -1) {\r\n      this.scrollToItem(firstIncompleteIndex);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到第一個有錯誤的項目\r\n   */\r\n  scrollToFirstErrorItem(): void {\r\n    if (!this.arrListFormItemReq) return;\r\n\r\n    // 找到第一個有錯誤的項目\r\n    const firstErrorIndex = this.arrListFormItemReq.findIndex(item => !this.isItemCompleted(item));\r\n    if (firstErrorIndex !== -1) {\r\n      this.scrollToItem(firstErrorIndex);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 滾動到頂部\r\n   */\r\n  scrollToTop(): void {\r\n    window.scrollTo(0, 0);\r\n  }\r\n\r\n  /**\r\n   * 回到頂部\r\n   */\r\n  goToTop(): void {\r\n    console.log('goToTop clicked - method called');\r\n\r\n    // 優先滾動到頁面最頂部的header區塊\r\n    const headerElement = document.querySelector('nb-card-header') ||\r\n      document.querySelector('.card-header') ||\r\n      document.querySelector('nb-card') ||\r\n      document.querySelector('.header') ||\r\n      document.querySelector('h1, h2, h3') ||\r\n      document.body.firstElementChild;\r\n\r\n    if (headerElement) {\r\n      (headerElement as HTMLElement).scrollIntoView({\r\n        behavior: 'smooth',\r\n        block: 'start'\r\n      });\r\n      console.log('Scrolled to header element:', headerElement.tagName);\r\n\r\n      // 額外向上滾動一點，確保header完全可見\r\n      setTimeout(() => {\r\n        window.scrollBy({\r\n          top: -50, // 向上滾動50px\r\n          behavior: 'smooth'\r\n        });\r\n      }, 500);\r\n      return;\r\n    }\r\n\r\n    // 備用方案：滾動到第一個表單項目的上方\r\n    if (this.arrListFormItemReq && this.arrListFormItemReq.length > 0) {\r\n      const firstElement = document.getElementById('form-item-0');\r\n      if (firstElement) {\r\n        firstElement.scrollIntoView({\r\n          behavior: 'smooth',\r\n          block: 'start'\r\n        });\r\n\r\n        // 向上滾動更多距離，確保看到header\r\n        setTimeout(() => {\r\n          window.scrollBy({\r\n            top: -200, // 向上滾動200px\r\n            behavior: 'smooth'\r\n          });\r\n        }, 500);\r\n        console.log('Scrolled to first form item with extra offset');\r\n        return;\r\n      }\r\n    }\r\n\r\n    // 最後的備用方法\r\n    console.log('Using fallback scroll methods');\r\n    window.scrollTo({ top: 0, behavior: 'smooth' });\r\n  }\r\n\r\n  /**\r\n   * 滾動到底部 - 滾動到 footer 資訊區塊\r\n   */\r\n  scrollToBottom(): void {\r\n    console.log('=== 至底功能被點擊 - 滾動到 footer 資訊區塊 ===');\r\n\r\n    // 立即顯示一個簡短的視覺反饋\r\n    const button = document.querySelector('button[title=\"到底部\"]') as HTMLElement;\r\n    if (button) {\r\n      button.style.transform = 'scale(0.95)';\r\n      setTimeout(() => {\r\n        button.style.transform = '';\r\n      }, 150);\r\n    }\r\n\r\n    // 滾動到 footer 資訊區塊\r\n    setTimeout(() => {\r\n      // 方法1: 滾動到 nb-card-footer 元素\r\n      const footerElement = document.querySelector('nb-card-footer') as HTMLElement;\r\n      if (footerElement) {\r\n        console.log('找到 footer 元素，滾動到 footer 資訊區塊');\r\n        footerElement.scrollIntoView({\r\n          behavior: 'smooth',\r\n          block: 'center', // 改為 center 讓 footer 在畫面中央\r\n          inline: 'nearest'\r\n        });\r\n        console.log('已滾動到 footer 資訊區塊');\r\n        return;\r\n      }\r\n\r\n      // 方法2: 尋找包含統計資訊的 footer 區域（使用更精確的選擇器）\r\n      const progressFooter = document.querySelector('nb-card-footer .flex.items-center.justify-center') as HTMLElement;\r\n      if (progressFooter) {\r\n        console.log('找到統計資訊 footer，滾動到該區域');\r\n        progressFooter.scrollIntoView({\r\n          behavior: 'smooth',\r\n          block: 'center',\r\n          inline: 'nearest'\r\n        });\r\n        console.log('已滾動到統計資訊區域');\r\n        return;\r\n      }\r\n\r\n      // 方法3: 滾動到最後一個表單項目\r\n      if (this.filteredArrListFormItemReq && this.filteredArrListFormItemReq.length > 0) {\r\n        const lastIndex = this.filteredArrListFormItemReq.length - 1;\r\n        const lastElement = document.getElementById(`form-item-${lastIndex}`);\r\n        if (lastElement) {\r\n          console.log(`找到最後一個表單項目: form-item-${lastIndex}`);\r\n          lastElement.scrollIntoView({\r\n            behavior: 'smooth',\r\n            block: 'end',\r\n            inline: 'nearest'\r\n          });\r\n\r\n          // 額外向下滾動到 footer 區域\r\n          setTimeout(() => {\r\n            window.scrollBy({\r\n              top: 200, // 增加滾動距離以確保看到 footer\r\n              behavior: 'smooth'\r\n            });\r\n          }, 500);\r\n\r\n          console.log('已滾動到最後一個表單項目並向下偏移到 footer');\r\n          return;\r\n        }\r\n      }\r\n\r\n      // 備用方法: 滾動到頁面底部\r\n      console.log('使用備用方法：滾動到頁面底部');\r\n      window.scrollTo({\r\n        top: document.body.scrollHeight,\r\n        behavior: 'smooth'\r\n      });\r\n    }, 100);\r\n  }\r\n\r\n  /**\r\n   * 切換項目收合狀態\r\n   */\r\n  toggleItemCollapse(formItemReq: ExtendedSaveListFormItemReq): void {\r\n    formItemReq.isCollapsed = !formItemReq.isCollapsed;\r\n  }\r\n\r\n  /**\r\n   * 全部展開\r\n   */\r\n  expandAll(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = false;\r\n      });\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 全部收合\r\n   */\r\n  collapseAll(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = true;\r\n      });\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 只展開未完成的項目\r\n   */\r\n  expandIncompleteOnly(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(item => {\r\n        item.isCollapsed = this.isItemCompleted(item);\r\n      });\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 搜尋功能\r\n   */\r\n  onSearch(): void {\r\n    if (!this.searchQuery.trim()) {\r\n      this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\r\n    } else {\r\n      const query = this.searchQuery.toLowerCase().trim();\r\n      this.filteredArrListFormItemReq = this.arrListFormItemReq.filter(item => {\r\n        return (\r\n          item.CName?.toLowerCase().includes(query) ||\r\n          item.CPart?.toLowerCase().includes(query) ||\r\n          item.CLocation?.toLowerCase().includes(query) ||\r\n          item.CItemName?.toLowerCase().includes(query)\r\n        );\r\n      });\r\n    }\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  /**\r\n   * 清除搜尋\r\n   */\r\n  clearSearch(): void {\r\n    this.searchQuery = '';\r\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  /**\r\n   * 更新過濾列表\r\n   */\r\n  private updateFilteredList(): void {\r\n    this.filteredArrListFormItemReq = [...this.arrListFormItemReq];\r\n    if (this.searchQuery.trim()) {\r\n      this.onSearch();\r\n    }\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAqBC,uBAAuB,QAA2B,eAAe;AACxG,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAI1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASA,YAAY,IAAIC,eAAe,QAAQ,8BAA8B;AAC9E,SAASC,aAAa,QAAQ,6CAA6C;AAE3E,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,aAAa,QAAQ,mCAAmC;AAsC1D,IAAMC,4CAA4C,GAAlD,MAAMA,4CAA6C,SAAQH,aAAa;EAC7EI,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B,EAC3BC,aAA2B,EAC3BC,GAAsB;IAE9B,KAAK,CAACX,MAAM,CAAC;IAbL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,GAAG,GAAHA,GAAG;IAIb,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IACD;IACA,KAAAC,kBAAkB,GAAG,CACnB;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAEpB,aAAa,CAACqB;IAAG,CAAE,EAC1C;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAEpB,aAAa,CAACsB;IAAG,CAAE,CAC3C;IAiBD,KAAAC,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,EACD;MACEC,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,EAAE;MACDC,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,CAAC;IACJ,KAAAK,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAEjC,KAAAC,YAAY,GAAY,KAAK;IA4C7B,KAAAC,aAAa,GAA+B,EAAE;IAC9C,KAAAC,kBAAkB,GAA+B,EAAE;IAEnD;IACA,KAAAC,YAAY,GAAQ,EAAE,CAAC,CAAC;IAiPxB,KAAAC,YAAY,GAA8B,IAAI;IAC9C,KAAAC,KAAK,GAAY,IAAI;IAqFrB,KAAAC,kBAAkB,GAAkC,EAAE;IACtD,KAAAC,0BAA0B,GAAkC,EAAE;IAC9D,KAAAC,WAAW,GAAW,EAAE;EA9ZxB;EAUA;EACA,IAAIC,YAAYA,CAAA;IACd,MAAMC,MAAM,GAAG,IAAI,CAACjB,kBAAkB,CAACkB,IAAI,CAACD,MAAM,IAChDA,MAAM,CAACf,KAAK,KAAK,IAAI,CAACL,iCAAiC,CAACE,WAAW,CACpE;IACD,OAAOkB,MAAM,GAAG,QAAQA,MAAM,CAAChB,KAAK,EAAE,GAAG,WAAW;EACtD;EACA;EACAkB,cAAcA,CAACC,UAAyB;IACtC,IAAI,IAAI,CAACpB,kBAAkB,CAACqB,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAACf,KAAK,KAAKkB,UAAU,CAAC,EAAE;MACvE,IAAI,CAACvB,iCAAiC,CAACE,WAAW,GAAGqB,UAAU;MAC/D;MACA,IAAI,CAACvB,iCAAiC,CAACC,SAAS,GAAGsB,UAAU;IAC/D;EACF;EAgBSE,QAAQA,CAAA;IACf,IAAI,CAACpC,KAAK,CAACqC,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QAErB,IAAI,IAAI,CAACC,WAAW,GAAG,CAAC,EAAE;UACxB,IAAI,CAACC,iCAAiC,EAAE;QAC1C,CAAC,MAAM;UACL;UACAC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAACH,WAAW,CAAC;UACvD,IAAI,CAAC1C,OAAO,CAAC8C,YAAY,CAAC,iBAAiB,CAAC;UAC5C,IAAI,CAACC,MAAM,EAAE;QACf;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAChD,KAAK,CAACiD,WAAW,CAACX,SAAS,CAACW,WAAW,IAAG;MAC7C,IAAIA,WAAW,CAAC,WAAW,CAAC,EAAE;QAC5B,MAAMC,SAAS,GAAG,CAACD,WAAW,CAAC,WAAW,CAAC;QAC3C,IAAI,CAAChB,cAAc,CAACiB,SAAS,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAC,cAAcA,CAACxC,KAAU,EAAEyC,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC1C,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO0C,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQAC,WAAWA,CAACC,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAACU,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UACxCX,YAAY,CAACU,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7B7B,EAAE,EAAE,IAAI+B,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAAC1E,eAAe,CAAC2E,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAACU,YAAY,CAACU,IAAI,CAAC;YAC7BvC,EAAE,EAAE,IAAI+B,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAAC1E,eAAe,CAAC2E,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAAC/C,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEAkE,WAAWA,CAACC,SAAiB,EAAEtB,YAAiB;IAC9C,IAAIA,YAAY,CAACU,YAAY,CAACC,MAAM,EAAE;MACpCX,YAAY,CAACU,YAAY,GAAGV,YAAY,CAACU,YAAY,CAACa,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC3C,EAAE,IAAIyC,SAAS,CAAC;IAC7F;EACF;EACAG,UAAUA,CAAC1B,KAAU,EAAE2B,KAAa,EAAE1B,YAAiB;IACrD,IAAI2B,IAAI,GAAG3B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACS,KAAK,CAAC,CAAC,EAAE5B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACU,IAAI,EAAE7B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAG5B,KAAK,CAACG,MAAM,CAAC/C,KAAK,GAAG,GAAG,GAAG6C,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACT,SAAS,EAAE,EAAE;MAAEa,IAAI,EAAE9B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW;IAAI,CAAE,CAAC;IACjK9B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,GAAGY,OAAO;EAClD;EAEA;EACAE,SAASA,CAACC,WAAgB;IACxB,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjEuB,WAAW,CAACE,iBAAiB,GAAG,CAACF,WAAW,CAACE,iBAAiB,GAAG,CAAC,IAAIF,WAAW,CAACC,WAAW,CAACxB,MAAM;IACtG;EACF;EAEA0B,SAASA,CAACH,WAAgB;IACxB,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjEuB,WAAW,CAACE,iBAAiB,GAAGF,WAAW,CAACE,iBAAiB,KAAK,CAAC,GAC/DF,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,GAClCuB,WAAW,CAACE,iBAAiB,GAAG,CAAC;IACvC;EACF;EACAE,eAAeA,CAACJ,WAAgB;IAC9B,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,IAAIuB,WAAW,CAACE,iBAAiB,KAAKG,SAAS,EAAE;MAChH,OAAOL,WAAW,CAACC,WAAW,CAACD,WAAW,CAACE,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EAEA;EACAI,cAAcA,CAACN,WAAgB,EAAEO,UAAmB;IAClD,IAAIP,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjE,IAAI8B,UAAU,KAAKF,SAAS,EAAE;QAC5BL,WAAW,CAACE,iBAAiB,GAAGK,UAAU;MAC5C;MACAP,WAAW,CAACQ,WAAW,GAAG,IAAI;MAC9B;MACAnD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;EACF;EAEAiD,eAAeA,CAACT,WAAgB;IAC9BA,WAAW,CAACQ,WAAW,GAAG,KAAK;IAC/B;IACAnD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACAkD,cAAcA,CAACV,WAAgB;IAC7B,IAAI,CAACD,SAAS,CAACC,WAAW,CAAC;EAC7B;EAEAW,cAAcA,CAACX,WAAgB;IAC7B,IAAI,CAACG,SAAS,CAACH,WAAW,CAAC;EAC7B;EAEA;EACAY,SAASA,CAAC/C,KAAoB,EAAEmC,WAAgB;IAC9C,IAAIA,WAAW,CAACQ,WAAW,EAAE;MAC3B,QAAQ3C,KAAK,CAACgD,GAAG;QACf,KAAK,WAAW;UACdhD,KAAK,CAACiD,cAAc,EAAE;UACtB,IAAI,CAACH,cAAc,CAACX,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACfnC,KAAK,CAACiD,cAAc,EAAE;UACtB,IAAI,CAACJ,cAAc,CAACV,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACXnC,KAAK,CAACiD,cAAc,EAAE;UACtB,IAAI,CAACL,eAAe,CAACT,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEA;EACAe,qBAAqBA,CAACC,UAAiB;IACrC,IAAI,CAACA,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;IACX;IACA,OAAOA,UAAU,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,IAAID,CAAC,CAAC;EACzC;EACA;EACAE,0BAA0BA,CAACC,kBAA4B,EAAEvB,WAAgB;IACvE;IACAwB,MAAM,CAACC,IAAI,CAACzB,WAAW,CAACzE,aAAa,CAAC,CAACmG,OAAO,CAACb,GAAG,IAAG;MACnDb,WAAW,CAACzE,aAAa,CAACsF,GAAG,CAAC,GAAG,KAAK;IACxC,CAAC,CAAC;IAEF;IACAU,kBAAkB,CAACG,OAAO,CAACC,SAAS,IAAG;MACrC3B,WAAW,CAACzE,aAAa,CAACoG,SAAS,CAAC,GAAG,IAAI;IAC7C,CAAC,CAAC;IAEF;IACA3B,WAAW,CAAC4B,WAAW,GAAG,IAAI,CAACC,aAAa,CAACpD,MAAM,GAAG,CAAC,IACrD,IAAI,CAACoD,aAAa,CAACC,KAAK,CAACnE,IAAI,IAAIqC,WAAW,CAACzE,aAAa,CAACoC,IAAI,CAAC,CAAC;IAEnE;IACA,IAAI,CAACoE,6BAA6B,CAAC/B,WAAW,CAAC;EACjD;EAEA;EACAgC,qBAAqBA,CAAChC,WAAgB;IACpC,OAAOwB,MAAM,CAACC,IAAI,CAACzB,WAAW,CAACzE,aAAa,CAAC,CAAC8D,MAAM,CAACwB,GAAG,IAAIb,WAAW,CAACzE,aAAa,CAACsF,GAAG,CAAC,CAAC;EAC7F;EAEA;EACQkB,6BAA6BA,CAAC/B,WAAgB;IACpDA,WAAW,CAACiC,wBAAwB,GAAG,IAAI,CAACD,qBAAqB,CAAChC,WAAW,CAAC;EAChF;EAEA;EACQkC,gCAAgCA,CAAA;IACtC,IAAI,IAAI,CAACtG,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC8F,OAAO,CAAC1B,WAAW,IAAG;QAC5C,IAAI,CAAC+B,6BAA6B,CAAC/B,WAAW,CAAC;MACjD,CAAC,CAAC;IACJ;EACF;EAIAmC,sBAAsBA,CAACC,OAAgB,EAAEzE,IAAY,EAAEG,YAAiB;IACtEA,YAAY,CAACtC,kBAAkB,CAACmC,IAAI,CAAC,GAAGyE,OAAO;EACjD;EAEAC,kBAAkBA,CAAChH,kBAA4B,EAAEiH,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMvG,MAAM,IAAIX,kBAAkB,EAAE;MACvCkH,YAAY,CAACvG,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMwG,WAAW,GAAGF,WAAW,CAACzD,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMe,IAAI,IAAI4C,WAAW,EAAE;MAC9B,IAAInH,kBAAkB,CAACoH,QAAQ,CAAC7C,IAAI,CAAC,EAAE;QACrC2C,YAAY,CAAC3C,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAO2C,YAAY;EACrB;EAEAG,UAAUA,CAACC,KAAoC;IAC7C,MAAMxB,GAAG,GAAG,IAAIyB,GAAG,EAAgE;IAEnFD,KAAK,CAACjB,OAAO,CAAC/D,IAAI,IAAG;MACnB,MAAMkD,GAAG,GAAG,GAAGlD,IAAI,CAACkF,SAAS,IAAIlF,IAAI,CAACmF,KAAK,IAAInF,IAAI,CAACoF,KAAK,EAAE;MAC3D,IAAI5B,GAAG,CAAC6B,GAAG,CAACnC,GAAG,CAAC,EAAE;QAChB,MAAMoC,QAAQ,GAAG9B,GAAG,CAACzE,GAAG,CAACmE,GAAG,CAAE;QAC9BoC,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACL/B,GAAG,CAACgC,GAAG,CAACtC,GAAG,EAAE;UAAElD,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAEuF,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOjC,KAAK,CAACmC,IAAI,CAACjC,GAAG,CAACkC,MAAM,EAAE,CAAC,CAAClC,GAAG,CAAC,CAAC;MAAExD,IAAI;MAAEuF;IAAK,CAAE,MAAM;MACxD,GAAGvF,IAAI;MACP2F,YAAY,EAAEJ;KACf,CAAC,CAAC;EACL;EAGAK,eAAeA,CAAA;IACb,IAAI,CAAC/I,gBAAgB,CAACgJ,mCAAmC,CAAC;MACxDlG,IAAI,EAAE;QACJmG,YAAY,EAAE,IAAI,CAAC7G,WAAW;QAC9B8G,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLnK,GAAG,CAACoK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAGtC,IAAI,CAAClI,kBAAkB,GAAGgI,GAAG,CAACC,OAAO,CAAC1C,GAAG,CAAE4C,CAA0B,IAAI;UACvE,OAAO;YACLC,cAAc,EAAE,IAAI;YACpBC,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbrB,SAAS,EAAEkB,CAAC,CAAClB,SAAS;YACtBC,KAAK,EAAEiB,CAAC,CAACjB,KAAK;YACdC,KAAK,EAAEgB,CAAC,CAAChB,KAAK;YACdoB,SAAS,EAAE,GAAGJ,CAAC,CAACjB,KAAK,IAAIiB,CAAC,CAAChB,KAAK,IAAIgB,CAAC,CAAClB,SAAS,EAAE;YACjDP,WAAW,EAAE,IAAI;YACjBgB,YAAY,EAAE,CAAC;YACfc,cAAc,EAAE,CAAC;YACjBC,OAAO,EAAE,CAAC;YAAE9I,aAAa,EAAE,EAAE;YAC7BC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3CoG,WAAW,EAAE,KAAK;YAClBpD,YAAY,EAAE,EAAE;YAAE8F,eAAe,EAAE,IAAI,CAAClJ,cAAc,CAAC,CAAC,CAAC;YACzD8E,iBAAiB,EAAE,CAAC;YACpBM,WAAW,EAAE,KAAK;YAClB+D,WAAW,EAAE,IAAI;YAAE;YACnBtE,WAAW,EAAE8D,CAAC,CAACS,cAAc,GAAGT,CAAC,CAACS,cAAc,CAACrD,GAAG,CAACsD,EAAE,IAAIA,EAAE,CAACC,OAAO,CAAC,CAACrF,MAAM,CAAEsF,GAAQ,IAAKA,GAAG,IAAI,IAAI,CAAa,GAAG;WACxH;QACH,CAAC,CAAC;QACF,IAAI,CAAC/I,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAAC8G,UAAU,CAAC,IAAI,CAAC9G,kBAAkB,CAAC,CAAC;QAEvE;QACA,IAAI,CAACgJ,kBAAkB,EAAE;MAC3B;IACF,CAAC,CAAC,CACH,CAACrI,SAAS,EAAE;EACf;EAKAsI,eAAeA,CAAA;IACb,IAAI,CAAC1K,gBAAgB,CAAC2K,mCAAmC,CAAC;MACxDxH,IAAI,EAAE;QACJmG,YAAY,EAAE,IAAI,CAAC7G,WAAW;QAC9B/B,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DkK,SAAS,EAAE;;KAEd,CAAC,CAACpB,IAAI,CACLnK,GAAG,CAACoK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACpI,YAAY,GAAGkI,GAAG,CAACC,OAAO;QAC/B,IAAI,CAAClI,KAAK,GAAGiI,GAAG,CAACC,OAAO,CAACmB,SAAS,GAAG,KAAK,GAAG,IAAI;QAEjD,IAAIpB,GAAG,CAACC,OAAO,CAACmB,SAAS,EAAE;UACzB,IAAI,CAACnD,aAAa,CAACH,OAAO,CAAC/D,IAAI,IAAI,IAAI,CAACpC,aAAa,CAACoC,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAACtC,kBAAkB,CAACqG,OAAO,CAAC/D,IAAI,IAAI,IAAI,CAACnC,kBAAkB,CAACmC,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC/B,kBAAkB,GAAGgI,GAAG,CAACC,OAAO,CAACmB,SAAS,CAAC7D,GAAG,CAAE4C,CAAM,IAAI;YAC7D,OAAO;cACLG,OAAO,EAAE,IAAI,CAACxI,YAAY,EAAEwI,OAAO;cACnCF,cAAc,EAAED,CAAC,CAACC,cAAc;cAChC/D,WAAW,EAAE8D,CAAC,CAAC9D,WAAW,KAAK8D,CAAC,CAACkB,gBAAgB,GAAG,CAAClB,CAAC,CAACkB,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9EhG,KAAK,EAAE8E,CAAC,CAAC9E,KAAK;cACdgF,kBAAkB,EAAEF,CAAC,CAACE,kBAAkB;cACxCiB,WAAW,EAAEnB,CAAC,CAACmB,WAAW;cAC1BrC,SAAS,EAAEkB,CAAC,CAAClB,SAAS;cACtBC,KAAK,EAAEiB,CAAC,CAACjB,KAAK;cACdC,KAAK,EAAEgB,CAAC,CAAChB,KAAK;cACdoB,SAAS,EAAEJ,CAAC,CAACI,SAAS,GAAGJ,CAAC,CAACI,SAAS,GAAG,GAAGJ,CAAC,CAACjB,KAAK,IAAIiB,CAAC,CAAChB,KAAK,IAAIgB,CAAC,CAAClB,SAAS,EAAE;cAC7EP,WAAW,EAAEyB,CAAC,CAACzB,WAAW;cAC1BgB,YAAY,EAAES,CAAC,CAACT,YAAY;cAC5Bc,cAAc,EAAEL,CAAC,CAACM,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGN,CAAC,CAACK,cAAc;cACtDC,OAAO,EAAEN,CAAC,CAACM,OAAO;cAClB9I,aAAa,EAAEwI,CAAC,CAACoB,qBAAqB,CAAC1G,MAAM,GAAG,IAAI,CAAC2G,0BAA0B,CAAC,IAAI,CAACvD,aAAa,EAAEkC,CAAC,CAACoB,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC5J;cAAa,CAAE;cAAEC,kBAAkB,EAAEuI,CAAC,CAACzB,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAChH,kBAAkB,EAAE0I,CAAC,CAACzB,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC9G;cAAkB,CAAE;cAC9RoG,WAAW,EAAEmC,CAAC,CAACoB,qBAAqB,CAAC1G,MAAM,KAAK,IAAI,CAACoD,aAAa,CAACpD,MAAM;cACzED,YAAY,EAAE,EAAE;cAAE8F,eAAe,EAAEP,CAAC,CAACM,OAAO,GAAG,IAAI,CAAC5G,cAAc,CAACsG,CAAC,CAACM,OAAO,EAAE,IAAI,CAACjJ,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cAC3H8E,iBAAiB,EAAE,CAAC;cACpBM,WAAW,EAAE,KAAK;cAClB+D,WAAW,EAAE,IAAI;cAAE;cACnBtC,wBAAwB,EAAE,EAAE,CAAC;aAC9B;UACH,CAAC,CAAC;UAEF;UACA,IAAI,CAAC2C,kBAAkB,EAAE;UAEzB;UACA,IAAI,CAACjK,GAAG,CAAC0K,aAAa,EAAE;QAC1B,CAAC,MAAM;UACL;UACA,IAAI,CAAC9B,eAAe,EAAE;QACxB;QAEA;QACA,IAAI,CAACrB,gCAAgC,EAAE;QAEvC;QACA,IAAI,CAACvH,GAAG,CAAC0K,aAAa,EAAE;MAC1B,CAAC,MAAM;QACLvI,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAE6G,GAAG,CAAC;MAC/C;IACF,CAAC,CAAC,CACH,CAACrH,SAAS,CAAC;MACVQ,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAuI,mBAAmBA,CAACtF,WAAgB;IAClC,IAAIA,WAAW,CAACsE,eAAe,IAAItE,WAAW,CAACsE,eAAe,CAACrJ,KAAK,KAAK,CAAC,EAAE;MAC1E+E,WAAW,CAACoE,cAAc,GAAG,CAAC;IAChC;EACF;EACAmB,4BAA4BA,CAACzG,IAAW;IACtC,KAAK,IAAInB,IAAI,IAAImB,IAAI,EAAE;MACrB,IAAInB,IAAI,CAAC7C,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAO6C,IAAI,CAAC6H,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAMAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOlE,MAAM,CAACC,IAAI,CAACiE,GAAG,CAAC,CAACrG,MAAM,CAACwB,GAAG,IAAI6E,GAAG,CAAC7E,GAAG,CAAC,CAAC;EACjD;EAEA8E,0BAA0BA,CAACD,GAA4B;IACrD,OAAOlE,MAAM,CAACC,IAAI,CAACiE,GAAG,CAAC,CACpBrG,MAAM,CAACwB,GAAG,IAAI6E,GAAG,CAAC7E,GAAG,CAAC,CAAC,CACvB+E,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACvB,eAAoB,EAAE9I,kBAAuB;IAC1D,IAAI8I,eAAe,IAAIA,eAAe,CAACrJ,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC0K,0BAA0B,CAACnK,kBAAkB,CAAC;IAC5D;EACF;EAEAsK,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAAClH,KAAK,CAAC,GAAG,CAAC;IACpC,IAAImH,KAAK,CAACvH,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOuH,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAACzH,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACLyH,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAACtH,YAAY,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,IAAI;QACpEqH,aAAa,EAAE3H,YAAY,CAAC,CAAC,CAAC,CAACO,SAAS,IAAI,IAAI;QAChDqH,QAAQ,EAAE5H,YAAY,CAAC,CAAC,CAAC,CAACS,KAAK,CAACL,IAAI,IAAIJ,YAAY,CAAC,CAAC,CAAC,CAACI,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOyB,SAAS;EAEzB;EAGAgG,UAAUA,CAAA;IACR,IAAI,CAAC/L,KAAK,CAACgM,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAM9I,IAAI,IAAI,IAAI,CAAC+I,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAC5I,IAAI,CAAC0G,OAAQ,EAAE;QACzCkC,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAC7I,IAAI,CAACyG,cAAe,EAAE;QACvDoC,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAI7I,IAAI,CAAC2F,YAAY,IAAI3F,IAAI,CAACyG,cAAc,EAAE;QAC5C,IAAIzG,IAAI,CAACyG,cAAc,GAAGzG,IAAI,CAAC2F,YAAY,IAAI3F,IAAI,CAACyG,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAAC9J,KAAK,CAACqM,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAGhJ,IAAI,CAAC2F,YAAY,GAAG,KAAK3F,IAAI,CAACwG,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACsC,kBAAkB,IAAK,CAAC9I,IAAI,CAACwG,SAAU,EAAE;QAC5CsC,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAACjM,KAAK,CAACqM,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAClM,KAAK,CAACqM,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAACnM,KAAK,CAACqM,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACtL,YAAY,GAAG,IAAI;IAExB,IAAI,CAACoL,mBAAmB,GAAG,IAAI,CAAC9K,kBAAkB,CAACuF,GAAG,CAAE0F,CAAM,IAAI;MAChE,OAAO;QACL7C,cAAc,EAAE6C,CAAC,CAAC7C,cAAc,GAAG6C,CAAC,CAAC7C,cAAc,GAAG,IAAI;QAC1D/E,KAAK,EAAE4H,CAAC,CAACrI,YAAY,GAAG,IAAI,CAACyH,UAAU,CAACY,CAAC,CAACrI,YAAY,CAAC,GAAG6B,SAAS;QACnE4D,kBAAkB,EAAE,IAAI,CAACwB,oBAAoB,CAACoB,CAAC,CAACtL,aAAa,CAAC;QAC9D2J,WAAW,EAAE2B,CAAC,CAAC3B,WAAW,GAAG2B,CAAC,CAAC3B,WAAW,GAAG,IAAI;QACjD4B,OAAO,EAAE,IAAI,CAACnL,KAAK,GAAG,IAAI,GAAG,IAAI,CAACD,YAAY,EAAEwI,OAAO;QACvDpB,KAAK,EAAE+D,CAAC,CAAC/D,KAAK;QACdC,KAAK,EAAE8D,CAAC,CAAC9D,KAAK;QACdF,SAAS,EAAEgE,CAAC,CAAChE,SAAS;QACtBsB,SAAS,EAAE0C,CAAC,CAAC1C,SAAS;QAAE;QACxB7B,WAAW,EAAEuE,CAAC,CAACvC,eAAe,CAACrJ,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC4K,cAAc,CAACgB,CAAC,CAACvC,eAAe,EAAEuC,CAAC,CAACrL,kBAAkB,CAAC,IAAI,IAAI;QACxH8H,YAAY,EAAEuD,CAAC,CAACvD,YAAY;QAC5Bc,cAAc,EAAEyC,CAAC,CAACzC,cAAc;QAChCC,OAAO,EAAEwC,CAAC,CAACvC,eAAe,CAACrJ;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACoL,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC/L,KAAK,CAACyM,aAAa,CAACtI,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACvE,OAAO,CAAC8M,aAAa,CAAC,IAAI,CAAC1M,KAAK,CAACyM,aAAa,CAAC;MACpD;MACA,IAAI,CAACE,sBAAsB,EAAE;MAC7B,IAAI,CAAC3L,YAAY,GAAG,KAAK;MACzB;IACF;IACA,IAAI,IAAI,CAACK,KAAK,EAAE;MACd,IAAI,CAACuL,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAChN,gBAAgB,CAACiN,oCAAoC,CAAC;MACzD9J,IAAI,EAAE,IAAI,CAACoJ;KACZ,CAAC,CAACnK,SAAS,CAAC;MACX8K,IAAI,EAAGzD,GAAG,IAAI;QACZ,IAAI,CAACtI,YAAY,GAAG,KAAK;QACzB,IAAIsI,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAAC5J,OAAO,CAACoN,aAAa,CAAC,MAAM,CAAC;UAClC;UACA,IAAI,CAACrK,MAAM,EAAE;QACf;MACF,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzB,YAAY,GAAG,KAAK;QACzBwB,OAAO,CAACC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;KACD,CAAC;EACJ;EAIAmK,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvB9D,YAAY,EAAE,IAAI,CAAC7G,WAAW;MAC9B4K,SAAS,EAAE,IAAI,CAACd,mBAAmB,IAAI,IAAI;MAC3C7L,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACV,gBAAgB,CAACsN,sCAAsC,CAAC;MAC3DnK,IAAI,EAAE,IAAI,CAACiK;KACZ,CAAC,CAAChL,SAAS,CAAC;MACX8K,IAAI,EAAGzD,GAAG,IAAI;QACZ,IAAI,CAACtI,YAAY,GAAG,KAAK;QACzB,IAAIsI,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAAC5J,OAAO,CAACoN,aAAa,CAAC,MAAM,CAAC;UAClC;UACA,IAAI,CAACrK,MAAM,EAAE;QACf;MACF,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzB,YAAY,GAAG,KAAK;QACzBwB,OAAO,CAACC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACvC;KACD,CAAC;EACJ;EAEAqI,0BAA0BA,CAACsC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMjK,IAAI,IAAI+J,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAAC1L,IAAI,CAAC6L,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKpK,IAAI,IAAImK,KAAK,CAACE,SAAS,CAAC;MAClFJ,CAAC,CAACjK,IAAI,CAAC,GAAG,CAAC,CAACkK,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAEF;;;EAGAK,aAAaA,CAAA;IACX;IACA,IAAI,CAACzN,gBAAgB,CAACgJ,mCAAmC,CAAC;MACxDlG,IAAI,EAAE;QACJmG,YAAY,EAAE,IAAI,CAAC7G,WAAW;QAC9B8G,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLnK,GAAG,CAACoK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC;QACA,MAAMoE,iBAAiB,GAAG,IAAIC,GAAG,EAAU;QAC3CvE,GAAG,CAACC,OAAO,CAACnC,OAAO,CAAE0G,QAAa,IAAI;UACpC,MAAMvH,GAAG,GAAG,GAAGuH,QAAQ,CAACvF,SAAS,IAAIuF,QAAQ,CAACtF,KAAK,IAAIsF,QAAQ,CAACrF,KAAK,EAAE;UACvEmF,iBAAiB,CAACG,GAAG,CAACxH,GAAG,CAAC;QAC5B,CAAC,CAAC;QAEF;QACA,MAAMyH,cAAc,GAAG,IAAI,CAAC1M,kBAAkB,CAACyD,MAAM,CAAE1B,IAAS,IAAI;UAClE,MAAM4K,OAAO,GAAG,GAAG5K,IAAI,CAACkF,SAAS,IAAIlF,IAAI,CAACmF,KAAK,IAAInF,IAAI,CAACoF,KAAK,EAAE;UAC/D,OAAOmF,iBAAiB,CAAClF,GAAG,CAACuF,OAAO,CAAC;QACvC,CAAC,CAAC;QAEF,IAAID,cAAc,CAAC7J,MAAM,KAAK,CAAC,EAAE;UAC/B,IAAI,CAACvE,OAAO,CAAC8C,YAAY,CAAC,eAAe,CAAC;UAC1C;QACF;QAEA;QACA,IAAI,CAAC0J,mBAAmB,GAAG4B,cAAc,CAACnH,GAAG,CAAE0F,CAAM,IAAI;UACvD,OAAO;YACL7C,cAAc,EAAE6C,CAAC,CAAC7C,cAAc,GAAG6C,CAAC,CAAC7C,cAAc,GAAG,IAAI;YAC1D/E,KAAK,EAAE4H,CAAC,CAACrI,YAAY,GAAG,IAAI,CAACyH,UAAU,CAACY,CAAC,CAACrI,YAAY,CAAC,GAAG6B,SAAS;YACnE4D,kBAAkB,EAAE,IAAI,CAACwB,oBAAoB,CAACoB,CAAC,CAACtL,aAAa,CAAC;YAC9D2J,WAAW,EAAE,IAAI;YAAE;YACnB4B,OAAO,EAAE,IAAI;YAAE;YACfhE,KAAK,EAAE+D,CAAC,CAAC/D,KAAK;YACdC,KAAK,EAAE8D,CAAC,CAAC9D,KAAK;YACdF,SAAS,EAAEgE,CAAC,CAAChE,SAAS;YACtBsB,SAAS,EAAE0C,CAAC,CAAC1C,SAAS;YACtB7B,WAAW,EAAEuE,CAAC,CAACvC,eAAe,CAACrJ,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC4K,cAAc,CAACgB,CAAC,CAACvC,eAAe,EAAEuC,CAAC,CAACrL,kBAAkB,CAAC,IAAI,IAAI;YACxH8H,YAAY,EAAEuD,CAAC,CAACvD,YAAY;YAC5Bc,cAAc,EAAEyC,CAAC,CAACzC,cAAc;YAChCC,OAAO,EAAEwC,CAAC,CAACvC,eAAe,CAACrJ;WAC5B;QACH,CAAC,CAAC;QAEF;QACA,IAAI,CAACoL,UAAU,EAAE;QACjB,IAAI,IAAI,CAAC/L,KAAK,CAACyM,aAAa,CAACtI,MAAM,GAAG,CAAC,EAAE;UACvC,IAAI,CAACvE,OAAO,CAAC8M,aAAa,CAAC,IAAI,CAAC1M,KAAK,CAACyM,aAAa,CAAC;UACpD;QACF;QAEA;QACA,IAAI,CAACQ,iBAAiB,GAAG;UACvB9D,YAAY,EAAE,IAAI,CAAC7G,WAAW;UAC9B4K,SAAS,EAAE,IAAI,CAACd,mBAAmB,IAAI,IAAI;UAC3C7L,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;SACnD;QAED,IAAI,CAACV,gBAAgB,CAACsN,sCAAsC,CAAC;UAC3DnK,IAAI,EAAE,IAAI,CAACiK;SACZ,CAAC,CAAChL,SAAS,CAACiM,SAAS,IAAG;UACvB,IAAIA,SAAS,CAAC1E,UAAU,IAAI,CAAC,EAAE;YAC7B,IAAI,CAAC5J,OAAO,CAACoN,aAAa,CAAC,cAAcgB,cAAc,CAAC7J,MAAM,QAAQ,CAAC;YACvE;YACA,IAAI,CAACoG,eAAe,EAAE;UACxB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAC3K,OAAO,CAAC8C,YAAY,CAAC,eAAe,CAAC;MAC5C;IACF,CAAC,CAAC,CACH,CAACT,SAAS,EAAE;EACf;EAEA;EACQkM,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC7L,WAAW,EAAE;IAEvB,IAAI,CAAClC,aAAa,CAACgO,4BAA4B,CAAC;MAAE9L,WAAW,EAAE,IAAI,CAACA;IAAW,CAAE,CAAC,CAACL,SAAS,CAAC;MAC3F8K,IAAI,EAAGsB,QAAQ,IAAI;QACjB7L,OAAO,CAAC8L,GAAG,CAAC,2BAA2B,EAAED,QAAQ,CAAC;QAClD,IAAIA,QAAQ,CAAC9E,OAAO,EAAE;UACpB,IAAI,CAACpI,YAAY,GAAG,IAAI,CAACoN,gCAAgC,CAACF,QAAQ,CAAC9E,OAAO,CAAC;UAC3E/G,OAAO,CAAC8L,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACnN,YAAY,CAAC;QAC3D;MACF,CAAC;MACDsB,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D;QACA,IAAI,IAAI,CAAC8E,aAAa,IAAI,IAAI,CAACA,aAAa,CAACpD,MAAM,GAAG,CAAC,EAAE;UACvD,IAAI,CAAChD,YAAY,GAAG,IAAI,CAACqN,kCAAkC,CAAC,IAAI,CAACjH,aAAa,CAAC;QACjF;MACF;KACD,CAAC;EACJ;EAEA;EACQgH,gCAAgCA,CAACE,OAAY;IACnD,MAAMtN,YAAY,GAAQ,EAAE;IAE5B+F,MAAM,CAACuH,OAAO,CAACA,OAAO,CAAC,CAACrH,OAAO,CAAC,CAAC,CAACsH,QAAQ,EAAEC,MAAM,CAAgB,KAAI;MACpExN,YAAY,CAACuN,QAAQ,CAAC,GAAGC,MAAM,CAAC9H,GAAG,CAAE+H,KAAU,KAAM;QACnD7H,IAAI,EAAE6H,KAAK,CAACC,SAAS;QACrBH,QAAQ,EAAEE,KAAK,CAACE,QAAQ;QACxBC,KAAK,EAAEH,KAAK,CAACI,KAAK;QAClBC,OAAO,EAAEL,KAAK,CAACM,OAAO;QACtBC,SAAS,EAAEP,KAAK,CAACC,SAAS;QAC1BO,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOlO,YAAY;EACrB;EAEA;EACAqN,kCAAkCA,CAACjH,aAAuB;IACxD,IAAI,CAACA,aAAa,IAAIA,aAAa,CAACpD,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,EAAE;IACX;IAEA;IACA,MAAMhD,YAAY,GAAQ,EAAE;IAE5BoG,aAAa,CAACH,OAAO,CAACC,SAAS,IAAG;MAChC;MACA,MAAMiI,aAAa,GAAGjI,SAAS,CAACkI,KAAK,CAAC,WAAW,CAAC;MAClD,MAAMb,QAAQ,GAAGY,aAAa,GAAG,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM;MAEhE,IAAI,CAACnO,YAAY,CAACuN,QAAQ,CAAC,EAAE;QAC3BvN,YAAY,CAACuN,QAAQ,CAAC,GAAG,EAAE;MAC7B;MAEA;MACA,MAAMc,WAAW,GAAGC,QAAQ,CAACpI,SAAS,CAACqI,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MAC7D,MAAMX,KAAK,GAAGY,IAAI,CAACC,IAAI,CAACJ,WAAW,GAAG,CAAC,CAAC;MAExCrO,YAAY,CAACuN,QAAQ,CAAC,CAAC9J,IAAI,CAAC;QAC1BmC,IAAI,EAAEM,SAAS;QACfqH,QAAQ,EAAEA,QAAQ;QAClBK,KAAK,EAAE,GAAGA,KAAK,GAAG;QAClBK,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;IAAE,OAAOlO,YAAY;EACzB;EAIAoB,iCAAiCA,CAAA;IAC/B,IAAI,CAACzC,yBAAyB,CAAC+P,8DAA8D,CAAC;MAC5F7M,IAAI,EAAE,IAAI,CAACV;KACZ,CAAC,CAAC+G,IAAI,CACLnK,GAAG,CAACoK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACjC,aAAa,GAAG,IAAI,CAAC0D,4BAA4B,CAAC3B,GAAG,CAACC,OAAO,CAAC;QAEnE;QACA,IAAI,CAAC4E,uBAAuB,EAAE;QAE9B,IAAI,CAAC5D,eAAe,EAAE;MACxB,CAAC,MAAM;QACL/H,OAAO,CAACC,KAAK,CAAC,2CAA2C,EAAE6G,GAAG,CAAC;MACjE;IACF,CAAC,CAAC,CACH,CAACrH,SAAS,CAAC;MACVQ,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAClE;KACD,CAAC;EACJ;EACAE,MAAMA,CAAA;IACJ,IAAI,CAACxC,aAAa,CAACyE,IAAI,CAAC;MACtBkL,MAAM;MACNC,OAAO,EAAE,IAAI,CAACzN;KACf,CAAC;IACF,IAAI,CAACrC,QAAQ,CAAC+P,IAAI,EAAE;EACtB;EAEA;EAEA;;;EAGAC,eAAeA,CAACvK,WAAwC;IACtD;IACA,MAAMwK,WAAW,GAAG,CAAC,CAACxK,WAAW,CAACmE,SAAS,IAAInE,WAAW,CAACmE,SAAS,CAACsG,IAAI,EAAE,KAAK,EAAE;IAClF,MAAMC,SAAS,GAAG,CAAC,CAAC1K,WAAW,CAACsE,eAAe,IAAItE,WAAW,CAACsE,eAAe,CAACrJ,KAAK;IACpF,MAAM0P,gBAAgB,GAAG,CAAC,CAAC3K,WAAW,CAACoE,cAAc,IAAIpE,WAAW,CAACoE,cAAc,GAAG,CAAC;IAEvF;IACA,IAAIwG,aAAa,GAAG,IAAI;IACxB,IAAI5K,WAAW,CAACsE,eAAe,EAAErJ,KAAK,KAAK,CAAC,EAAE;MAC5C2P,aAAa,GAAG,CAAC,CAAC5K,WAAW,CAACxE,kBAAkB,IAC9CgG,MAAM,CAAC6B,MAAM,CAACrD,WAAW,CAACxE,kBAAkB,CAAC,CAACY,IAAI,CAACyO,QAAQ,IAAIA,QAAQ,CAAC;IAC5E;IAEA,OAAOL,WAAW,IAAIE,SAAS,IAAIC,gBAAgB,IAAIC,aAAa;EACtE;EAEA;;;EAGAE,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAClP,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAAC6C,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC9E,OAAO,IAAI,CAAC7C,kBAAkB,CAACyD,MAAM,CAAC1B,IAAI,IAAI,IAAI,CAAC4M,eAAe,CAAC5M,IAAI,CAAC,CAAC,CAACc,MAAM;EAClF;EAEA;;;EAGAsM,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACnP,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAAC6C,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC9E,MAAMuM,SAAS,GAAG,IAAI,CAACF,sBAAsB,EAAE;IAC/C,OAAOb,IAAI,CAACgB,KAAK,CAAED,SAAS,GAAG,IAAI,CAACpP,kBAAkB,CAAC6C,MAAM,GAAI,GAAG,CAAC;EACvE;EAEA;;;EAGAyM,YAAYA,CAAC1L,KAAa;IACxB,MAAM2L,OAAO,GAAG9N,QAAQ,CAAC+N,cAAc,CAAC,aAAa5L,KAAK,EAAE,CAAC;IAC7D,IAAI2L,OAAO,EAAE;MACXA,OAAO,CAACE,cAAc,CAAC;QACrBC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE;OACT,CAAC;MAEF;MACAL,OAAO,CAACM,SAAS,CAACpD,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,iBAAiB,CAAC;MACnEqD,UAAU,CAAC,MAAK;QACdP,OAAO,CAACM,SAAS,CAACE,MAAM,CAAC,QAAQ,EAAE,eAAe,EAAE,iBAAiB,CAAC;MACxE,CAAC,EAAE,IAAI,CAAC;IACV;EACF;EAEA;;;EAGAC,2BAA2BA,CAAA;IACzB,IAAI,CAAC,IAAI,CAAChQ,kBAAkB,EAAE;IAE9B,MAAMiQ,oBAAoB,GAAG,IAAI,CAACjQ,kBAAkB,CAACkQ,SAAS,CAACnO,IAAI,IAAI,CAAC,IAAI,CAAC4M,eAAe,CAAC5M,IAAI,CAAC,CAAC;IACnG,IAAIkO,oBAAoB,KAAK,CAAC,CAAC,EAAE;MAC/B,IAAI,CAACX,YAAY,CAACW,oBAAoB,CAAC;IACzC;EACF;EAEA;;;EAGA5E,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACrL,kBAAkB,EAAE;IAE9B;IACA,MAAMmQ,eAAe,GAAG,IAAI,CAACnQ,kBAAkB,CAACkQ,SAAS,CAACnO,IAAI,IAAI,CAAC,IAAI,CAAC4M,eAAe,CAAC5M,IAAI,CAAC,CAAC;IAC9F,IAAIoO,eAAe,KAAK,CAAC,CAAC,EAAE;MAC1B,IAAI,CAACb,YAAY,CAACa,eAAe,CAAC;IACpC;EACF;EAEA;;;EAGAC,WAAWA,CAAA;IACTC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB;EAEA;;;EAGAC,OAAOA,CAAA;IACLrP,OAAO,CAAC8L,GAAG,CAAC,iCAAiC,CAAC;IAE9C;IACA,MAAMwD,aAAa,GAAG/O,QAAQ,CAACgP,aAAa,CAAC,gBAAgB,CAAC,IAC5DhP,QAAQ,CAACgP,aAAa,CAAC,cAAc,CAAC,IACtChP,QAAQ,CAACgP,aAAa,CAAC,SAAS,CAAC,IACjChP,QAAQ,CAACgP,aAAa,CAAC,SAAS,CAAC,IACjChP,QAAQ,CAACgP,aAAa,CAAC,YAAY,CAAC,IACpChP,QAAQ,CAACC,IAAI,CAACgP,iBAAiB;IAEjC,IAAIF,aAAa,EAAE;MAChBA,aAA6B,CAACf,cAAc,CAAC;QAC5CC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;OACR,CAAC;MACFzO,OAAO,CAAC8L,GAAG,CAAC,6BAA6B,EAAEwD,aAAa,CAACG,OAAO,CAAC;MAEjE;MACAb,UAAU,CAAC,MAAK;QACdO,MAAM,CAACO,QAAQ,CAAC;UACdC,GAAG,EAAE,CAAC,EAAE;UAAE;UACVnB,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;MACP;IACF;IAEA;IACA,IAAI,IAAI,CAAC1P,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAAC6C,MAAM,GAAG,CAAC,EAAE;MACjE,MAAMiO,YAAY,GAAGrP,QAAQ,CAAC+N,cAAc,CAAC,aAAa,CAAC;MAC3D,IAAIsB,YAAY,EAAE;QAChBA,YAAY,CAACrB,cAAc,CAAC;UAC1BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;SACR,CAAC;QAEF;QACAG,UAAU,CAAC,MAAK;UACdO,MAAM,CAACO,QAAQ,CAAC;YACdC,GAAG,EAAE,CAAC,GAAG;YAAE;YACXnB,QAAQ,EAAE;WACX,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;QACPxO,OAAO,CAAC8L,GAAG,CAAC,+CAA+C,CAAC;QAC5D;MACF;IACF;IAEA;IACA9L,OAAO,CAAC8L,GAAG,CAAC,+BAA+B,CAAC;IAC5CqD,MAAM,CAACC,QAAQ,CAAC;MAAEO,GAAG,EAAE,CAAC;MAAEnB,QAAQ,EAAE;IAAQ,CAAE,CAAC;EACjD;EAEA;;;EAGAqB,cAAcA,CAAA;IACZ7P,OAAO,CAAC8L,GAAG,CAAC,mCAAmC,CAAC;IAEhD;IACA,MAAMgE,MAAM,GAAGvP,QAAQ,CAACgP,aAAa,CAAC,qBAAqB,CAAgB;IAC3E,IAAIO,MAAM,EAAE;MACVA,MAAM,CAACrP,KAAK,CAACsP,SAAS,GAAG,aAAa;MACtCnB,UAAU,CAAC,MAAK;QACdkB,MAAM,CAACrP,KAAK,CAACsP,SAAS,GAAG,EAAE;MAC7B,CAAC,EAAE,GAAG,CAAC;IACT;IAEA;IACAnB,UAAU,CAAC,MAAK;MACd;MACA,MAAMoB,aAAa,GAAGzP,QAAQ,CAACgP,aAAa,CAAC,gBAAgB,CAAgB;MAC7E,IAAIS,aAAa,EAAE;QACjBhQ,OAAO,CAAC8L,GAAG,CAAC,8BAA8B,CAAC;QAC3CkE,aAAa,CAACzB,cAAc,CAAC;UAC3BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UAAE;UACjBC,MAAM,EAAE;SACT,CAAC;QACF1O,OAAO,CAAC8L,GAAG,CAAC,kBAAkB,CAAC;QAC/B;MACF;MAEA;MACA,MAAMmE,cAAc,GAAG1P,QAAQ,CAACgP,aAAa,CAAC,kDAAkD,CAAgB;MAChH,IAAIU,cAAc,EAAE;QAClBjQ,OAAO,CAAC8L,GAAG,CAAC,sBAAsB,CAAC;QACnCmE,cAAc,CAAC1B,cAAc,CAAC;UAC5BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UACfC,MAAM,EAAE;SACT,CAAC;QACF1O,OAAO,CAAC8L,GAAG,CAAC,YAAY,CAAC;QACzB;MACF;MAEA;MACA,IAAI,IAAI,CAAC/M,0BAA0B,IAAI,IAAI,CAACA,0BAA0B,CAAC4C,MAAM,GAAG,CAAC,EAAE;QACjF,MAAMuO,SAAS,GAAG,IAAI,CAACnR,0BAA0B,CAAC4C,MAAM,GAAG,CAAC;QAC5D,MAAMwO,WAAW,GAAG5P,QAAQ,CAAC+N,cAAc,CAAC,aAAa4B,SAAS,EAAE,CAAC;QACrE,IAAIC,WAAW,EAAE;UACfnQ,OAAO,CAAC8L,GAAG,CAAC,yBAAyBoE,SAAS,EAAE,CAAC;UACjDC,WAAW,CAAC5B,cAAc,CAAC;YACzBC,QAAQ,EAAE,QAAQ;YAClBC,KAAK,EAAE,KAAK;YACZC,MAAM,EAAE;WACT,CAAC;UAEF;UACAE,UAAU,CAAC,MAAK;YACdO,MAAM,CAACO,QAAQ,CAAC;cACdC,GAAG,EAAE,GAAG;cAAE;cACVnB,QAAQ,EAAE;aACX,CAAC;UACJ,CAAC,EAAE,GAAG,CAAC;UAEPxO,OAAO,CAAC8L,GAAG,CAAC,2BAA2B,CAAC;UACxC;QACF;MACF;MAEA;MACA9L,OAAO,CAAC8L,GAAG,CAAC,gBAAgB,CAAC;MAC7BqD,MAAM,CAACC,QAAQ,CAAC;QACdO,GAAG,EAAEpP,QAAQ,CAACC,IAAI,CAAC4P,YAAY;QAC/B5B,QAAQ,EAAE;OACX,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGA6B,kBAAkBA,CAACnN,WAAwC;IACzDA,WAAW,CAACuE,WAAW,GAAG,CAACvE,WAAW,CAACuE,WAAW;EACpD;EAEA;;;EAGA6I,SAASA,CAAA;IACP,IAAI,IAAI,CAACxR,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC8F,OAAO,CAAC/D,IAAI,IAAG;QACrCA,IAAI,CAAC4G,WAAW,GAAG,KAAK;MAC1B,CAAC,CAAC;MACF,IAAI,CAAC5J,GAAG,CAAC0K,aAAa,EAAE;IAC1B;EACF;EAEA;;;EAGAgI,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzR,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC8F,OAAO,CAAC/D,IAAI,IAAG;QACrCA,IAAI,CAAC4G,WAAW,GAAG,IAAI;MACzB,CAAC,CAAC;MACF,IAAI,CAAC5J,GAAG,CAAC0K,aAAa,EAAE;IAC1B;EACF;EAEA;;;EAGAiI,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC1R,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC8F,OAAO,CAAC/D,IAAI,IAAG;QACrCA,IAAI,CAAC4G,WAAW,GAAG,IAAI,CAACgG,eAAe,CAAC5M,IAAI,CAAC;MAC/C,CAAC,CAAC;MACF,IAAI,CAAChD,GAAG,CAAC0K,aAAa,EAAE;IAC1B;EACF;EAEA;;;EAGAkI,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACzR,WAAW,CAAC2O,IAAI,EAAE,EAAE;MAC5B,IAAI,CAAC5O,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAACD,kBAAkB,CAAC;IAChE,CAAC,MAAM;MACL,MAAM4R,KAAK,GAAG,IAAI,CAAC1R,WAAW,CAAC2R,WAAW,EAAE,CAAChD,IAAI,EAAE;MACnD,IAAI,CAAC5O,0BAA0B,GAAG,IAAI,CAACD,kBAAkB,CAACyD,MAAM,CAAC1B,IAAI,IAAG;QACtE,OACEA,IAAI,CAACmF,KAAK,EAAE2K,WAAW,EAAE,CAAChL,QAAQ,CAAC+K,KAAK,CAAC,IACzC7P,IAAI,CAACoF,KAAK,EAAE0K,WAAW,EAAE,CAAChL,QAAQ,CAAC+K,KAAK,CAAC,IACzC7P,IAAI,CAACkF,SAAS,EAAE4K,WAAW,EAAE,CAAChL,QAAQ,CAAC+K,KAAK,CAAC,IAC7C7P,IAAI,CAACwG,SAAS,EAAEsJ,WAAW,EAAE,CAAChL,QAAQ,CAAC+K,KAAK,CAAC;MAEjD,CAAC,CAAC;IACJ;IACA,IAAI,CAAC7S,GAAG,CAAC0K,aAAa,EAAE;EAC1B;EAEA;;;EAGAqI,WAAWA,CAAA;IACT,IAAI,CAAC5R,WAAW,GAAG,EAAE;IACrB,IAAI,CAACD,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAACD,kBAAkB,CAAC;IAC9D,IAAI,CAACjB,GAAG,CAAC0K,aAAa,EAAE;EAC1B;EAEA;;;EAGQT,kBAAkBA,CAAA;IACxB,IAAI,CAAC/I,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAACD,kBAAkB,CAAC;IAC9D,IAAI,IAAI,CAACE,WAAW,CAAC2O,IAAI,EAAE,EAAE;MAC3B,IAAI,CAAC8C,QAAQ,EAAE;IACjB;EACF;CAED;AA9jCYzT,4CAA4C,GAAA6T,UAAA,EATxDxU,SAAS,CAAC;EACTyU,QAAQ,EAAE,6CAA6C;EACvDC,WAAW,EAAE,0DAA0D;EACvEC,SAAS,EAAE,CAAC,0DAA0D,CAAC;EACvEC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC3U,YAAY,EAAEC,WAAW,EAAEG,YAAY,EAAEC,eAAe,EAAEH,gBAAgB,EAAEK,eAAe,CAAC;EACtGqU,eAAe,EAAE7U,uBAAuB,CAAC8U;CAC1C,CAAC,C,EAEWpU,4CAA4C,CA8jCxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}