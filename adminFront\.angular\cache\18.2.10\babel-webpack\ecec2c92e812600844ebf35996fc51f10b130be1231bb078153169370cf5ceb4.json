{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/dashboard.service\";\nimport * as i2 from \"ngx-echarts\";\nexport class PaymentChartComponent {\n  constructor(dashboardService) {\n    this.dashboardService = dashboardService;\n    this.chartOption = {};\n  }\n  ngOnInit() {\n    this.loadPaymentData();\n  }\n  loadPaymentData() {\n    this.dashboardService.getPaymentStatusData().subscribe(data => {\n      this.setupChart(data);\n    });\n  }\n  setupChart(data) {\n    const colors = ['#27ae60', '#e74c3c', '#95a5a6'];\n    this.chartOption = {\n      title: {\n        text: '繳款狀態分布',\n        left: 'center',\n        textStyle: {\n          fontSize: 16,\n          fontWeight: 'bold',\n          color: '#2c3e50'\n        }\n      },\n      tooltip: {\n        trigger: 'item',\n        formatter: params => {\n          const data = params.data;\n          return `${params.name}<br/>\n                  戶數: ${data.value}戶 (${data.percentage}%)<br/>\n                  ${data.amount ? `金額: ${data.amount}萬元` : ''}`;\n        }\n      },\n      legend: {\n        bottom: 10,\n        left: 'center'\n      },\n      series: [{\n        name: '繳款狀態',\n        type: 'pie',\n        radius: ['45%', '75%'],\n        center: ['50%', '45%'],\n        data: data.map((item, index) => ({\n          ...item,\n          itemStyle: {\n            color: colors[index]\n          }\n        })),\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowOffsetX: 0,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          }\n        },\n        label: {\n          show: true,\n          formatter: '{b}: {c}戶'\n        }\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function PaymentChartComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PaymentChartComponent)(i0.ɵɵdirectiveInject(i1.DashboardService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaymentChartComponent,\n      selectors: [[\"app-payment-chart\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"chart-container\", \"dashboard-card\"], [\"echarts\", \"\", 1, \"chart\", 3, \"options\"]],\n      template: function PaymentChartComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.chartOption);\n        }\n      },\n      dependencies: [CommonModule, NgxEchartsModule, i2.NgxEchartsDirective],\n      styles: [\".chart-container[_ngcontent-%COMP%] {\\n  height: 350px;\\n  width: 100%;\\n  background: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  padding: 20px;\\n  border: 1px solid #f0f0f0;\\n}\\n\\n.chart[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInBheW1lbnQtY2hhcnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSx5Q0FBQTtFQUNBLGFBQUE7RUFDQSx5QkFBQTtBQUNGOztBQUVBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUFDRiIsImZpbGUiOiJwYXltZW50LWNoYXJ0LmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmNoYXJ0LWNvbnRhaW5lciB7XG4gIGhlaWdodDogMzUwcHg7XG4gIHdpZHRoOiAxMDAlO1xuICBiYWNrZ3JvdW5kOiAjZmZmZmZmO1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgcGFkZGluZzogMjBweDtcbiAgYm9yZGVyOiAxcHggc29saWQgI2YwZjBmMDtcbn1cblxuLmNoYXJ0IHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG9tZS9jb21wb25lbnRzL3BheW1lbnQtY2hhcnQvcGF5bWVudC1jaGFydC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLHlDQUFBO0VBQ0EsYUFBQTtFQUNBLHlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtBQUNGO0FBQ0EsZ3RCQUFndEIiLCJzb3VyY2VzQ29udGVudCI6WyIuY2hhcnQtY29udGFpbmVyIHtcbiAgaGVpZ2h0OiAzNTBweDtcbiAgd2lkdGg6IDEwMCU7XG4gIGJhY2tncm91bmQ6ICNmZmZmZmY7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICBwYWRkaW5nOiAyMHB4O1xuICBib3JkZXI6IDFweCBzb2xpZCAjZjBmMGYwO1xufVxuXG4uY2hhcnQge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NgxEchartsModule", "PaymentChartComponent", "constructor", "dashboardService", "chartOption", "ngOnInit", "loadPaymentData", "getPaymentStatusData", "subscribe", "data", "<PERSON><PERSON><PERSON>", "colors", "title", "text", "left", "textStyle", "fontSize", "fontWeight", "color", "tooltip", "trigger", "formatter", "params", "name", "value", "percentage", "amount", "legend", "bottom", "series", "type", "radius", "center", "map", "item", "index", "itemStyle", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "label", "show", "i0", "ɵɵdirectiveInject", "i1", "DashboardService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PaymentChartComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "i2", "NgxEchartsDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\payment-chart\\payment-chart.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\payment-chart\\payment-chart.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport { EChartsOption } from 'echarts';\nimport { PaymentStatusData } from '../../models/dashboard.interface';\nimport { DashboardService } from '../../services/dashboard.service';\n\n@Component({\n  selector: 'app-payment-chart',\n  standalone: true,\n  imports: [CommonModule, NgxEchartsModule],\n  templateUrl: './payment-chart.component.html',\n  styleUrls: ['./payment-chart.component.scss']\n})\nexport class PaymentChartComponent implements OnInit {\n  chartOption: EChartsOption = {};\n\n  constructor(private dashboardService: DashboardService) { }\n\n  ngOnInit(): void {\n    this.loadPaymentData();\n  }\n\n  private loadPaymentData(): void {\n    this.dashboardService.getPaymentStatusData().subscribe(data => {\n      this.setupChart(data);\n    });\n  }\n\n  private setupChart(data: PaymentStatusData[]): void {\n    const colors = ['#27ae60', '#e74c3c', '#95a5a6'];\n    \n    this.chartOption = {\n      title: {\n        text: '繳款狀態分布',\n        left: 'center',\n        textStyle: {\n          fontSize: 16,\n          fontWeight: 'bold',\n          color: '#2c3e50'\n        }\n      },\n      tooltip: {\n        trigger: 'item',\n        formatter: (params: any) => {\n          const data = params.data;\n          return `${params.name}<br/>\n                  戶數: ${data.value}戶 (${data.percentage}%)<br/>\n                  ${data.amount ? `金額: ${data.amount}萬元` : ''}`;\n        }\n      },\n      legend: {\n        bottom: 10,\n        left: 'center'\n      },\n      series: [{\n        name: '繳款狀態',\n        type: 'pie',\n        radius: ['45%', '75%'],\n        center: ['50%', '45%'],\n        data: data.map((item, index) => ({\n          ...item,\n          itemStyle: {\n            color: colors[index]\n          }\n        })),\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowOffsetX: 0,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          }\n        },\n        label: {\n          show: true,\n          formatter: '{b}: {c}戶'\n        }\n      }]\n    };\n  }\n}", "<div class=\"chart-container dashboard-card\">\n  <div echarts [options]=\"chartOption\" class=\"chart\"></div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,aAAa;;;;AAY9C,OAAM,MAAOC,qBAAqB;EAGhCC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAFpC,KAAAC,WAAW,GAAkB,EAAE;EAE2B;EAE1DC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQA,eAAeA,CAAA;IACrB,IAAI,CAACH,gBAAgB,CAACI,oBAAoB,EAAE,CAACC,SAAS,CAACC,IAAI,IAAG;MAC5D,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQC,UAAUA,CAACD,IAAyB;IAC1C,MAAME,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IAEhD,IAAI,CAACP,WAAW,GAAG;MACjBQ,KAAK,EAAE;QACLC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,QAAQ;QACdC,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,MAAM;UAClBC,KAAK,EAAE;;OAEV;MACDC,OAAO,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAGC,MAAW,IAAI;UACzB,MAAMb,IAAI,GAAGa,MAAM,CAACb,IAAI;UACxB,OAAO,GAAGa,MAAM,CAACC,IAAI;wBACPd,IAAI,CAACe,KAAK,MAAMf,IAAI,CAACgB,UAAU;oBACnChB,IAAI,CAACiB,MAAM,GAAG,OAAOjB,IAAI,CAACiB,MAAM,IAAI,GAAG,EAAE,EAAE;QACvD;OACD;MACDC,MAAM,EAAE;QACNC,MAAM,EAAE,EAAE;QACVd,IAAI,EAAE;OACP;MACDe,MAAM,EAAE,CAAC;QACPN,IAAI,EAAE,MAAM;QACZO,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACtBvB,IAAI,EAAEA,IAAI,CAACwB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;UAC/B,GAAGD,IAAI;UACPE,SAAS,EAAE;YACTlB,KAAK,EAAEP,MAAM,CAACwB,KAAK;;SAEtB,CAAC,CAAC;QACHE,QAAQ,EAAE;UACRD,SAAS,EAAE;YACTE,UAAU,EAAE,EAAE;YACdC,aAAa,EAAE,CAAC;YAChBC,WAAW,EAAE;;SAEhB;QACDC,KAAK,EAAE;UACLC,IAAI,EAAE,IAAI;UACVrB,SAAS,EAAE;;OAEd;KACF;EACH;;;uCAjEWpB,qBAAqB,EAAA0C,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAArB7C,qBAAqB;MAAA8C,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdlCb,EAAA,CAAAe,cAAA,aAA4C;UAC1Cf,EAAA,CAAAgB,SAAA,aAAyD;UAC3DhB,EAAA,CAAAiB,YAAA,EAAM;;;UADSjB,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAAmB,UAAA,YAAAL,GAAA,CAAArD,WAAA,CAAuB;;;qBDS1BL,YAAY,EAAEC,gBAAgB,EAAA+D,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}