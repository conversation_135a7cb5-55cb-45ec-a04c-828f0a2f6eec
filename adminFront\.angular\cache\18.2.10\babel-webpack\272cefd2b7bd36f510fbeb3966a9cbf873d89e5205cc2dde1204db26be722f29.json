{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"../../components/pagination/pagination.component\";\nconst _c0 = [\"createModal\"];\nconst _c1 = [\"editModal\"];\nconst _c2 = [\"templateDetailModal\"];\nfunction TemplateComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const createModal_r4 = i0.ɵɵreference(61);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(createModal_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_56_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_56_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const editModal_r9 = i0.ɵɵreference(63);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(editModal_r9, template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_56_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_56_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteTemplate(template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 44);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 35);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 36)(12, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_56_Template_button_click_12_listener() {\n      const template_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateDetailModal_r7 = i0.ɵɵreference(65);\n      return i0.ɵɵresetView(ctx_r2.viewTemplateDetail(template_r6, templateDetailModal_r7));\n    });\n    i0.ɵɵelement(13, \"i\", 38);\n    i0.ɵɵtext(14, \"\\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, TemplateComponent_tr_56_button_15_Template, 3, 0, \"button\", 39)(16, TemplateComponent_tr_56_button_16_Template, 3, 0, \"button\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const template_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(template_r6.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", template_r6.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 7, template_r6.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r6.CCreator || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction TemplateComponent_tr_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 45);\n    i0.ɵɵelement(2, \"i\", 46);\n    i0.ɵɵtext(3, \"\\u76EE\\u524D\\u6C92\\u6709\\u4EFB\\u4F55\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_60_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 90)(2, \"input\", 91);\n    i0.ɵɵlistener(\"change\", function TemplateComponent_ng_template_60_div_57_Template_input_change_2_listener() {\n      const space_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleSpaceSelection(space_r14));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 92);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementStart(5, \"small\", 93);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const space_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"space-\" + space_r14.CSpaceID)(\"checked\", space_r14.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"space-\" + space_r14.CSpaceID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r14.CName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r14.CLocation || \"-\");\n  }\n}\nfunction TemplateComponent_ng_template_60_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"\\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_60_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"ngx-pagination\", 32);\n    i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_ng_template_60_div_59_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spacePageIndex, $event) || (ctx_r2.spacePageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function TemplateComponent_ng_template_60_div_59_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.spacePageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r2.spacePageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r2.spacePageSize)(\"CollectionSize\", ctx_r2.spaceTotalRecords);\n  }\n}\nfunction TemplateComponent_ng_template_60_div_60_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_div_60_span_4_Template_button_click_2_listener() {\n      const space_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeSelectedSpace(space_r17));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r17.CName, \" \");\n  }\n}\nfunction TemplateComponent_ng_template_60_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"label\", 97);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 98);\n    i0.ɵɵtemplate(4, TemplateComponent_ng_template_60_div_60_span_4_Template, 3, 1, \"span\", 99);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u7684\\u7A7A\\u9593 (\", ctx_r2.selectedSpacesForTemplate.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedSpacesForTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 47)(1, \"nb-card-header\", 48)(2, \"h5\", 49);\n    i0.ɵɵelement(3, \"i\", 50);\n    i0.ɵɵtext(4, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_Template_button_click_5_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(6, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 53)(8, \"div\", 54)(9, \"div\", 55)(10, \"div\", 56)(11, \"div\", 57)(12, \"label\", 58);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 59)(15, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_60_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown.control.enter\", function TemplateComponent_ng_template_60_Template_input_keydown_control_enter_15_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 55)(17, \"div\", 56)(18, \"div\", 57)(19, \"label\", 61);\n    i0.ɵɵtext(20, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 59)(22, \"nb-form-field\", 62)(23, \"nb-select\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_60_Template_nb_select_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(24, \"nb-option\", 16)(25, \"span\", 5);\n    i0.ɵɵelement(26, \"i\", 64);\n    i0.ɵɵtext(27, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"nb-option\", 16)(29, \"span\", 5);\n    i0.ɵɵelement(30, \"i\", 65);\n    i0.ɵɵtext(31, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(32, \"div\", 55)(33, \"div\", 56)(34, \"div\", 57)(35, \"label\", 66);\n    i0.ɵɵtext(36, \" \\u9078\\u64C7\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 59)(38, \"div\", 67)(39, \"div\", 68)(40, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_60_Template_input_ngModelChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchKeyword, $event) || (ctx_r2.spaceSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_60_Template_input_keyup_enter_40_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 68)(42, \"input\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_60_Template_input_ngModelChange_42_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchLocation, $event) || (ctx_r2.spaceSearchLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_60_Template_input_keyup_enter_42_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 71)(44, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceReset());\n    });\n    i0.ɵɵelement(45, \"i\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_Template_button_click_46_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelement(47, \"i\", 75);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"div\", 76)(49, \"div\", 77)(50, \"div\", 5)(51, \"input\", 78);\n    i0.ɵɵlistener(\"change\", function TemplateComponent_ng_template_60_Template_input_change_51_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"label\", 79);\n    i0.ɵɵtext(53, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"small\", 80);\n    i0.ɵɵtext(55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 54);\n    i0.ɵɵtemplate(57, TemplateComponent_ng_template_60_div_57_Template, 7, 5, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(58, TemplateComponent_ng_template_60_div_58_Template, 3, 0, \"div\", 82)(59, TemplateComponent_ng_template_60_div_59_Template, 2, 3, \"div\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(60, TemplateComponent_ng_template_60_div_60_Template, 5, 2, \"div\", 84);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(61, \"nb-card-footer\", 85)(62, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_Template_button_click_62_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(63, \"i\", 87);\n    i0.ɵɵtext(64, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_Template_button_click_65_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelement(66, \"i\", 89);\n    i0.ɵɵtext(67, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchLocation);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"checked\", ctx_r2.allSpacesSelected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx_r2.spaceTotalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx_r2.spacePageIndex, \" / \", ctx_r2.Math.ceil(ctx_r2.spaceTotalRecords / ctx_r2.spacePageSize), \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableSpaces);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableSpaces.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.spaceTotalRecords > ctx_r2.spacePageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedSpacesForTemplate.length > 0);\n  }\n}\nfunction TemplateComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 102)(1, \"nb-card-header\", 48)(2, \"h5\", 49);\n    i0.ɵɵelement(3, \"i\", 103);\n    i0.ɵɵtext(4, \"\\u7DE8\\u8F2F\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_Template_button_click_5_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r19));\n    });\n    i0.ɵɵelement(6, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 53)(8, \"div\", 54)(9, \"div\", 55)(10, \"div\", 56)(11, \"div\", 57)(12, \"label\", 104);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 59)(15, \"input\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_62_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 55)(17, \"div\", 56)(18, \"div\", 57)(19, \"label\", 106);\n    i0.ɵɵtext(20, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 59)(22, \"nb-form-field\", 62)(23, \"nb-select\", 107);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_62_Template_nb_select_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(24, \"nb-option\", 16)(25, \"span\", 5);\n    i0.ɵɵelement(26, \"i\", 64);\n    i0.ɵɵtext(27, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"nb-option\", 16)(29, \"span\", 5);\n    i0.ɵɵelement(30, \"i\", 65);\n    i0.ɵɵtext(31, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\", 85)(33, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_Template_button_click_33_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r19));\n    });\n    i0.ɵɵelement(34, \"i\", 87);\n    i0.ɵɵtext(35, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_Template_button_click_36_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r19));\n    });\n    i0.ɵɵelement(37, \"i\", 108);\n    i0.ɵɵtext(38, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction TemplateComponent_ng_template_64_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵelement(1, \"i\", 132);\n    i0.ɵɵelementStart(2, \"span\", 80);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_64_div_70_div_1_tr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"i\", 140);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵelement(7, \"i\", 141);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r22 = ctx.$implicit;\n    const i_r23 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r23 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r22.CReleateName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r22.CLocation || \"-\", \" \");\n  }\n}\nfunction TemplateComponent_ng_template_64_div_70_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"table\", 136)(2, \"thead\")(3, \"tr\")(4, \"th\", 137);\n    i0.ɵɵtext(5, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 138);\n    i0.ɵɵtext(7, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 139);\n    i0.ɵɵtext(9, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"tbody\");\n    i0.ɵɵtemplate(11, TemplateComponent_ng_template_64_div_70_div_1_tr_11_Template, 9, 3, \"tr\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateDetailSpaces);\n  }\n}\nfunction TemplateComponent_ng_template_64_div_70_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"\\u6B64\\u6A21\\u677F\\u5C1A\\u672A\\u5305\\u542B\\u4EFB\\u4F55\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_64_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TemplateComponent_ng_template_64_div_70_div_1_Template, 12, 1, \"div\", 133)(2, TemplateComponent_ng_template_64_div_70_div_2_Template, 3, 0, \"div\", 134);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length === 0);\n  }\n}\nfunction TemplateComponent_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 47)(1, \"nb-card-header\", 48)(2, \"h5\", 49);\n    i0.ɵɵelement(3, \"i\", 109);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u660E\\u7D30 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_64_Template_button_click_5_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r20).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r21));\n    });\n    i0.ɵɵelement(6, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 53)(8, \"div\", 110)(9, \"div\", 111)(10, \"h6\", 112);\n    i0.ɵɵelement(11, \"i\", 113);\n    i0.ɵɵtext(12, \"\\u57FA\\u672C\\u8CC7\\u8A0A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 114)(14, \"div\", 54)(15, \"div\", 9)(16, \"div\", 115)(17, \"label\", 116);\n    i0.ɵɵelement(18, \"i\", 117);\n    i0.ɵɵtext(19, \"\\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 118);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 9)(23, \"div\", 115)(24, \"label\", 116);\n    i0.ɵɵelement(25, \"i\", 119);\n    i0.ɵɵtext(26, \"\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 118)(28, \"span\", 35);\n    i0.ɵɵelement(29, \"i\", 120);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 9)(32, \"div\", 115)(33, \"label\", 116);\n    i0.ɵɵelement(34, \"i\", 121);\n    i0.ɵɵtext(35, \"\\u5EFA\\u7ACB\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"p\", 118);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 9)(40, \"div\", 115)(41, \"label\", 116);\n    i0.ɵɵelement(42, \"i\", 122);\n    i0.ɵɵtext(43, \"\\u5EFA\\u7ACB\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 118);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 9)(47, \"div\", 115)(48, \"label\", 116);\n    i0.ɵɵelement(49, \"i\", 123);\n    i0.ɵɵtext(50, \"\\u66F4\\u65B0\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 118);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 115)(56, \"label\", 116);\n    i0.ɵɵelement(57, \"i\", 124);\n    i0.ɵɵtext(58, \"\\u66F4\\u65B0\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\", 118);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(61, \"div\", 125)(62, \"div\", 126)(63, \"h6\", 112);\n    i0.ɵɵelement(64, \"i\", 127);\n    i0.ɵɵtext(65, \"\\u5305\\u542B\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 128);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 114);\n    i0.ɵɵtemplate(69, TemplateComponent_ng_template_64_div_69_Template, 4, 0, \"div\", 129)(70, TemplateComponent_ng_template_64_div_70_Template, 3, 2, \"div\", 30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"nb-card-footer\", 85)(72, \"button\", 130);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_64_Template_button_click_72_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r20).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r21));\n    });\n    i0.ɵɵelement(73, \"i\", 87);\n    i0.ɵɵtext(74, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CTemplateName) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"fas fa-check-circle\" : \"fas fa-times-circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(38, 12, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(53, 15, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r2.templateDetailSpaces.length, \" \\u500B\\u7A7A\\u9593\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingTemplateDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoadingTemplateDetail);\n  }\n}\nexport class TemplateComponent extends BaseComponent {\n  constructor(allow, dialogService, _templateService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._templateService = _templateService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.Math = Math; // 讓模板可以使用 Math 函數\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    // 模板相關屬性\n    this.templateList = [];\n    this.templateDetail = {};\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    // 空間選擇相關屬性\n    this.availableSpaces = [];\n    this.selectedSpacesForTemplate = [];\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.spacePageSize = 10;\n    this.spaceTotalRecords = 0;\n    this.allSpacesSelected = false;\n    // 模板明細相關屬性\n    this.selectedTemplateDetail = null;\n    this.templateDetailSpaces = [];\n    this.isLoadingTemplateDetail = false;\n  }\n  ngOnInit() {\n    this.loadTemplateList();\n    this.loadAvailableSpaces();\n  }\n  // 載入模板列表\n  loadTemplateList() {\n    const request = {\n      CTemplateName: this.searchKeyword || null,\n      CStatus: this.searchStatus,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this._templateService.apiTemplateGetTemplateListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.templateList = response.Entries?.map(item => ({\n          CTemplateId: item.CTemplateId,\n          CTemplateName: item.CTemplateName,\n          CCreateDt: item.CCreateDt,\n          CUpdateDt: item.CUpdateDt,\n          CCreator: item.CCreator,\n          CUpdator: item.CUpdator,\n          CStatus: item.CStatus\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板列表失敗');\n      }\n    })).subscribe();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    const request = {\n      CName: this.spaceSearchKeyword || null,\n      CLocation: this.spaceSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.spacePageIndex,\n      PageSize: this.spacePageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CName: item.CName,\n          CLocation: item.CLocation,\n          selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.spaceTotalRecords = response.TotalItems || 0;\n        this.updateAllSpacesSelectedState();\n      }\n    })).subscribe();\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  // 空間搜尋功能\n  onSpaceSearch() {\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  onSpaceReset() {\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 分頁功能\n  pageChanged(page) {\n    this.pageIndex = page;\n    this.loadTemplateList();\n  }\n  spacePageChanged(page) {\n    this.spacePageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  // 模態框操作\n  openCreateModal(modal) {\n    this.templateDetail = {\n      CStatus: 1\n    };\n    this.selectedSpacesForTemplate = [];\n    this.loadAvailableSpaces();\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  openEditModal(modal, template) {\n    this.templateDetail = {\n      CTemplateId: template.CTemplateId,\n      CTemplateName: template.CTemplateName,\n      CStatus: template.CStatus || 1\n    };\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onSubmit(ref) {\n    if (!this.validateTemplateForm()) {\n      return;\n    }\n    if (this.templateDetail.CTemplateId) {\n      this.updateTemplate(ref);\n    } else {\n      this.createTemplate(ref);\n    }\n  }\n  // 驗證表單\n  validateTemplateForm() {\n    if (!this.templateDetail.CTemplateName?.trim()) {\n      this.message.showErrorMSG('請輸入模板名稱');\n      return false;\n    }\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\n      this.message.showErrorMSG('請選擇模板狀態');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.selectedSpacesForTemplate.length === 0) {\n      this.message.showErrorMSG('請至少選擇一個空間');\n      return false;\n    }\n    return true;\n  }\n  // 建立模板\n  createTemplate(ref) {\n    const templateData = {\n      CTemplateName: this.templateDetail.CTemplateName,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0 && response.Entries) {\n        const templateId = parseInt(response.Entries, 10);\n        this.saveTemplateDetails(templateId, ref);\n      } else {\n        this.message.showErrorMSG(response.Message || '建立模板失敗');\n      }\n    })).subscribe();\n  }\n  // 更新模板\n  updateTemplate(ref) {\n    const templateData = {\n      CTemplateId: this.templateDetail.CTemplateId,\n      CTemplateName: this.templateDetail.CTemplateName,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.message.showSucessMSG('更新模板成功');\n        ref.close();\n        this.loadTemplateList();\n      } else {\n        this.message.showErrorMSG(response.Message || '更新模板失敗');\n      }\n    })).subscribe();\n  }\n  // 儲存模板詳細資料（關聯空間）\n  saveTemplateDetails(templateId, ref) {\n    // 目前 API 可能不支援模板詳細資料的保存，暫時跳過這個步驟\n    this.message.showSucessMSG('建立模板成功');\n    ref.close();\n    this.loadTemplateList();\n  }\n  // 刪除模板\n  deleteTemplate(template) {\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\n        body: {\n          CTemplateId: template.CTemplateId\n        }\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('刪除模板成功');\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '刪除模板失敗');\n        }\n      })).subscribe();\n    }\n  }\n  // 查看模板明細\n  viewTemplateDetail(template, modal) {\n    this.selectedTemplateDetail = template;\n    this.isLoadingTemplateDetail = true;\n    this.templateDetailSpaces = [];\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n    const request = {\n      templateId: template.CTemplateId\n    };\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      this.isLoadingTemplateDetail = false;\n      if (response.StatusCode === 0) {\n        this.templateDetailSpaces = response.Entries?.map(item => ({\n          CReleateId: item.CReleateId,\n          CReleateName: item.CReleateName,\n          CLocation: item.CLocation\n        })) || [];\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板明細失敗');\n      }\n    })).subscribe();\n  }\n  // 空間選擇相關方法\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n        this.selectedSpacesForTemplate.push({\n          ...space\n        });\n      }\n    } else {\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  toggleAllSpaces() {\n    this.allSpacesSelected = !this.allSpacesSelected;\n    this.availableSpaces.forEach(space => {\n      space.selected = this.allSpacesSelected;\n      if (this.allSpacesSelected) {\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else {\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n  }\n  removeSelectedSpace(space) {\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  updateAllSpacesSelectedState() {\n    this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  static {\n    this.ɵfac = function TemplateComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateComponent,\n      selectors: [[\"ngx-template\"]],\n      viewQuery: function TemplateComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.createModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateDetailModal = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 66,\n      vars: 11,\n      consts: [[\"createModal\", \"\"], [\"editModal\", \"\"], [\"templateDetailModal\", \"\"], [\"accent\", \"success\"], [1, \"alert\", \"alert-info\", \"mb-4\", 2, \"border-left\", \"4px solid #4a90e2\", \"background-color\", \"#f8f9ff\", \"border-radius\", \"6px\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-3\", 2, \"font-size\", \"1.2rem\"], [1, \"mb-0\", \"text-muted\", 2, \"font-size\", \"0.9rem\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"templateName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"col-3\"], [\"id\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"800px\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", 1, \"col-2\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"badge\", 3, \"ngClass\"], [1, \"table-actions\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-outline-warning btn-sm me-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-warning\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"5\", 1, \"text-center\", \"text-muted\", \"py-4\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"border-bottom\", \"py-3\", \"px-4\"], [1, \"mb-0\", \"text-primary\", \"font-weight-bold\"], [1, \"fas\", \"fa-plus-circle\", \"me-2\", \"text-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-light\", \"btn-sm\", \"rounded-circle\", 2, \"width\", \"32px\", \"height\", \"32px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"px-4\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"form-group\", \"mb-4\"], [1, \"d-flex\", \"align-items-start\"], [\"for\", \"templateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"flex-grow-1\", \"ml-3\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"templateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"keydown.control.enter\", \"ngModel\"], [\"for\", \"templateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"w-full\"], [\"id\", \"templateStatus\", \"name\", \"templateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\", \"me-2\"], [1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"row\", \"mb-3\"], [1, \"col-md-5\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"col-md-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"border\", \"rounded\", \"p-3\", 2, \"max-height\", \"300px\", \"overflow-y\", \"auto\", \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"selectAll\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAll\", 1, \"mb-0\", \"font-weight-bold\"], [1, \"text-muted\"], [\"class\", \"col-md-6\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-3\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center mt-3\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\", \"border-top\", \"pt-3\", \"px-4\", \"pb-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"btn\", \"btn-outline-secondary\", \"me-3\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", \"background\", \"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"id\", \"checked\"], [1, \"form-check-label\", 3, \"for\"], [1, \"text-muted\", \"d-block\"], [1, \"text-center\", \"text-muted\", \"py-3\"], [1, \"d-flex\", \"justify-content-center\", \"mt-3\"], [1, \"mt-3\"], [1, \"mb-2\", \"font-weight-bold\"], [1, \"border\", \"rounded\", \"p-2\", 2, \"max-height\", \"150px\", \"overflow-y\", \"auto\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", 1, \"btn-close\", \"btn-close-white\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"fas\", \"fa-edit\", \"me-2\", \"text-warning\"], [\"for\", \"editTemplateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"editTemplateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"editTemplateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"editTemplateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"id\", \"editTemplateStatus\", \"name\", \"editTemplateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-save\", \"me-1\"], [1, \"fas\", \"fa-eye\", \"me-2\", \"text-info\"], [1, \"card\", \"mb-4\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"mb-0\", \"text-dark\", \"font-weight-bold\"], [1, \"fas\", \"fa-info-circle\", \"me-2\", \"text-primary\"], [1, \"card-body\"], [1, \"form-group\", \"mb-3\"], [1, \"font-weight-bold\", \"text-muted\"], [1, \"fas\", \"fa-tag\", \"me-2\", \"text-primary\"], [1, \"mb-0\"], [1, \"fas\", \"fa-toggle-on\", \"me-2\", \"text-success\"], [1, \"me-1\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-plus\", \"me-2\", \"text-warning\"], [1, \"fas\", \"fa-calendar-edit\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-edit\", \"me-2\", \"text-warning\"], [1, \"card\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-success\"], [1, \"badge\", \"badge-info\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"text-center\", \"py-4\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"me-2\", \"text-primary\", 2, \"font-size\", \"1.2rem\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-7\"], [\"scope\", \"col\", 1, \"col-4\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\", \"text-muted\"], [1, \"text-center\", \"text-muted\", \"py-4\"]],\n      template: function TemplateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementStart(7, \"div\")(8, \"p\", 7);\n          i0.ɵɵtext(9, \" \\u5728\\u6B64\\u9801\\u9762\\u60A8\\u53EF\\u4EE5\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u7684\\u5404\\u500B\\u6A21\\u677F\\u8CC7\\u8A0A\\uFF0C\\u5305\\u62EC\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u6A21\\u677F\\uFF0C\\u4EE5\\u53CA\\u8A2D\\u5B9A\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u72C0\\u614B\\u548C\\u5305\\u542B\\u7684\\u7A7A\\u9593\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"label\", 11);\n          i0.ɵɵtext(14, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"nb-form-field\", 12)(16, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_Template_input_keyup_enter_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10)(19, \"label\", 14);\n          i0.ɵɵtext(20, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"nb-form-field\", 12)(22, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchStatus, $event) || (ctx.searchStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(23, \"nb-option\", 16);\n          i0.ɵɵtext(24, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nb-option\", 16);\n          i0.ɵɵtext(26, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-option\", 16);\n          i0.ɵɵtext(28, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(29, \"div\", 9);\n          i0.ɵɵelementStart(30, \"div\", 17)(31, \"div\", 18)(32, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_32_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReset());\n          });\n          i0.ɵɵelement(33, \"i\", 20);\n          i0.ɵɵtext(34, \"\\u91CD\\u7F6E \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_35_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelement(36, \"i\", 22);\n          i0.ɵɵtext(37, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"div\", 17)(39, \"div\", 23);\n          i0.ɵɵtemplate(40, TemplateComponent_button_40_Template, 3, 0, \"button\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 25)(42, \"table\", 26)(43, \"thead\")(44, \"tr\")(45, \"th\", 27);\n          i0.ɵɵtext(46, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"th\", 28);\n          i0.ɵɵtext(48, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"th\", 27);\n          i0.ɵɵtext(50, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"th\", 28);\n          i0.ɵɵtext(52, \"\\u5EFA\\u7ACB\\u8005\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"th\", 28);\n          i0.ɵɵtext(54, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"tbody\");\n          i0.ɵɵtemplate(56, TemplateComponent_tr_56_Template, 17, 10, \"tr\", 29)(57, TemplateComponent_tr_57_Template, 4, 0, \"tr\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(58, \"nb-card-footer\", 31)(59, \"ngx-pagination\", 32);\n          i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(60, TemplateComponent_ng_template_60_Template, 68, 14, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(62, TemplateComponent_ng_template_62_Template, 39, 4, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(64, TemplateComponent_ng_template_64_Template, 75, 18, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.templateList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.templateList.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, i6.DatePipe, SharedModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i8.BreadcrumbComponent, i9.PaginationComponent],\n      styles: [\".btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0;\\n  border-bottom-left-radius: 0;\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--color-fg-heading);\\n}\\n\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.badge.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n.badge.badge-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.required-field[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: #dc3545;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n\\n.btn-close-white[_ngcontent-%COMP%] {\\n  filter: invert(1) grayscale(100%) brightness(200%);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "CommonModule", "SharedModule", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateComponent_button_40_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "createModal_r4", "ɵɵreference", "ɵɵresetView", "openCreateModal", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "TemplateComponent_tr_56_button_15_Template_button_click_0_listener", "_r8", "template_r6", "$implicit", "editModal_r9", "openEditModal", "TemplateComponent_tr_56_button_16_Template_button_click_0_listener", "_r10", "deleteTemplate", "TemplateComponent_tr_56_Template_button_click_12_listener", "_r5", "templateDetailModal_r7", "viewTemplateDetail", "ɵɵtemplate", "TemplateComponent_tr_56_button_15_Template", "TemplateComponent_tr_56_button_16_Template", "ɵɵadvance", "ɵɵtextInterpolate", "CTemplateName", "ɵɵproperty", "CStatus", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "CCreateDt", "CCreator", "isUpdate", "isDelete", "TemplateComponent_ng_template_60_div_57_Template_input_change_2_listener", "space_r14", "_r13", "toggleSpaceSelection", "CSpaceID", "selected", "CName", "CLocation", "ɵɵtwoWayListener", "TemplateComponent_ng_template_60_div_59_Template_ngx_pagination_PageChange_1_listener", "$event", "_r15", "ɵɵtwoWayBindingSet", "spacePageIndex", "spacePageChanged", "ɵɵtwoWayProperty", "spacePageSize", "spaceTotalRecords", "TemplateComponent_ng_template_60_div_60_span_4_Template_button_click_2_listener", "space_r17", "_r16", "removeSelectedSpace", "TemplateComponent_ng_template_60_div_60_span_4_Template", "selectedSpacesForTemplate", "length", "TemplateComponent_ng_template_60_Template_button_click_5_listener", "ref_r12", "_r11", "dialogRef", "onClose", "TemplateComponent_ng_template_60_Template_input_ngModelChange_15_listener", "templateDetail", "TemplateComponent_ng_template_60_Template_input_keydown_control_enter_15_listener", "onSubmit", "TemplateComponent_ng_template_60_Template_nb_select_ngModelChange_23_listener", "TemplateComponent_ng_template_60_Template_input_ngModelChange_40_listener", "spaceSearchKeyword", "TemplateComponent_ng_template_60_Template_input_keyup_enter_40_listener", "onSpaceSearch", "TemplateComponent_ng_template_60_Template_input_ngModelChange_42_listener", "spaceSearchLocation", "TemplateComponent_ng_template_60_Template_input_keyup_enter_42_listener", "TemplateComponent_ng_template_60_Template_button_click_44_listener", "onSpaceReset", "TemplateComponent_ng_template_60_Template_button_click_46_listener", "TemplateComponent_ng_template_60_Template_input_change_51_listener", "toggleAllSpaces", "TemplateComponent_ng_template_60_div_57_Template", "TemplateComponent_ng_template_60_div_58_Template", "TemplateComponent_ng_template_60_div_59_Template", "TemplateComponent_ng_template_60_div_60_Template", "TemplateComponent_ng_template_60_Template_button_click_62_listener", "TemplateComponent_ng_template_60_Template_button_click_65_listener", "allSpacesSelected", "ɵɵtextInterpolate3", "Math", "ceil", "availableSpaces", "TemplateComponent_ng_template_62_Template_button_click_5_listener", "ref_r19", "_r18", "TemplateComponent_ng_template_62_Template_input_ngModelChange_15_listener", "TemplateComponent_ng_template_62_Template_nb_select_ngModelChange_23_listener", "TemplateComponent_ng_template_62_Template_button_click_33_listener", "TemplateComponent_ng_template_62_Template_button_click_36_listener", "i_r23", "space_r22", "CReleateName", "TemplateComponent_ng_template_64_div_70_div_1_tr_11_Template", "templateDetailSpaces", "TemplateComponent_ng_template_64_div_70_div_1_Template", "TemplateComponent_ng_template_64_div_70_div_2_Template", "TemplateComponent_ng_template_64_Template_button_click_5_listener", "ref_r21", "_r20", "TemplateComponent_ng_template_64_div_69_Template", "TemplateComponent_ng_template_64_div_70_Template", "TemplateComponent_ng_template_64_Template_button_click_72_listener", "selectedTemplateDetail", "ɵɵclassMap", "CUpdateDt", "CUpdator", "isLoadingTemplateDetail", "TemplateComponent", "constructor", "allow", "dialogService", "_templateService", "_spaceService", "message", "valid", "pageFirst", "pageSize", "pageIndex", "totalRecords", "templateList", "searchKeyword", "searchStatus", "ngOnInit", "loadTemplateList", "loadAvailableSpaces", "request", "PageIndex", "PageSize", "apiTemplateGetTemplateListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "TotalItems", "showErrorMSG", "Message", "subscribe", "apiSpaceGetSpaceListPost$Json", "some", "s", "updateAllSpacesSelectedState", "onSearch", "onReset", "pageChanged", "page", "modal", "open", "context", "autoFocus", "template", "ref", "close", "validateTemplateForm", "updateTemplate", "createTemplate", "trim", "undefined", "templateData", "apiTemplateSaveTemplatePost$Json", "templateId", "parseInt", "saveTemplateDetails", "showSucessMSG", "confirm", "apiTemplateDeleteTemplatePost$Json", "apiTemplateGetTemplateDetailByIdPost$Json", "CReleateId", "space", "push", "filter", "for<PERSON>ach", "availableSpace", "find", "every", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "TemplateService", "SpaceService", "i4", "MessageService", "i5", "ValidationHelper", "selectors", "viewQuery", "TemplateComponent_Query", "rf", "ctx", "TemplateComponent_Template_input_ngModelChange_16_listener", "_r1", "TemplateComponent_Template_input_keyup_enter_16_listener", "TemplateComponent_Template_nb_select_ngModelChange_22_listener", "TemplateComponent_Template_nb_select_selectedChange_22_listener", "TemplateComponent_Template_button_click_32_listener", "TemplateComponent_Template_button_click_35_listener", "TemplateComponent_button_40_Template", "TemplateComponent_tr_56_Template", "TemplateComponent_tr_57_Template", "TemplateComponent_Template_ngx_pagination_PageChange_59_listener", "TemplateComponent_ng_template_60_Template", "ɵɵtemplateRefExtractor", "TemplateComponent_ng_template_62_Template", "TemplateComponent_ng_template_64_Template", "isCreate", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "i8", "BreadcrumbComponent", "i9", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.html"], "sourcesContent": ["import { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { TemplateService, SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { tap } from 'rxjs';\r\nimport {\r\n  SaveTemplateArgs,\r\n  GetTemplateDetailByIdArgs,\r\n  TemplateDetailItem,\r\n  GetSpaceListResponse\r\n} from 'src/services/api/models';\r\n\r\nexport interface TemplateItem {\r\n  CTemplateId: number;\r\n  CTemplateName: string;\r\n  CCreateDt: string;\r\n  CUpdateDt: string;\r\n  CCreator?: string | null;\r\n  CUpdator?: string | null;\r\n  CStatus?: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpacePickListItem {\r\n  CSpaceID: number;\r\n  CName: string;\r\n  CLocation?: string | null;\r\n  selected?: boolean;\r\n}\r\n\r\n// 僅用於模板明細空間顯示\r\nexport interface TemplateDetailSpaceItem {\r\n  CReleateId: number;\r\n  CReleateName: string;\r\n  CLocation?: string | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-template',\r\n  templateUrl: './template.component.html',\r\n  styleUrls: ['./template.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent\r\n  ],\r\n})\r\nexport class TemplateComponent extends BaseComponent implements OnInit {\r\n  Math = Math; // 讓模板可以使用 Math 函數\r\n\r\n  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;\r\n  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;\r\n  @ViewChild('templateDetailModal', { static: false }) templateDetailModal!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _templateService: TemplateService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow);\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  // 模板相關屬性\r\n  templateList: TemplateItem[] = [];\r\n  templateDetail: SaveTemplateArgs = {};\r\n  searchKeyword: string = '';\r\n  searchStatus: number | null = null;\r\n\r\n  // 空間選擇相關屬性\r\n  availableSpaces: SpacePickListItem[] = [];\r\n  selectedSpacesForTemplate: SpacePickListItem[] = [];\r\n  spaceSearchKeyword: string = '';\r\n  spaceSearchLocation: string = '';\r\n  spacePageIndex = 1;\r\n  spacePageSize = 10;\r\n  spaceTotalRecords = 0;\r\n  allSpacesSelected = false;\r\n\r\n  // 模板明細相關屬性\r\n  selectedTemplateDetail: TemplateItem | null = null;\r\n  templateDetailSpaces: TemplateDetailSpaceItem[] = [];\r\n  isLoadingTemplateDetail = false;\r\n\r\n  override ngOnInit(): void {\r\n    this.loadTemplateList();\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 載入模板列表\r\n  loadTemplateList(): void {\r\n    const request = {\r\n      CTemplateName: this.searchKeyword || null,\r\n      CStatus: this.searchStatus,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.templateList = response.Entries?.map(item => ({\r\n            CTemplateId: item.CTemplateId!,\r\n            CTemplateName: item.CTemplateName!,\r\n            CCreateDt: item.CCreateDt!,\r\n            CUpdateDt: item.CUpdateDt!,\r\n            CCreator: item.CCreator,\r\n            CUpdator: item.CUpdator,\r\n            CStatus: item.CStatus\r\n          })) || [];\r\n          this.totalRecords = response.TotalItems || 0;\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板列表失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 載入可用空間列表\r\n  loadAvailableSpaces(): void {\r\n    const request = {\r\n      CName: this.spaceSearchKeyword || null,\r\n      CLocation: this.spaceSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.spacePageIndex,\r\n      PageSize: this.spacePageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableSpaces = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CName: item.CName!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\r\n          })) || [];\r\n          this.spaceTotalRecords = response.TotalItems || 0;\r\n          this.updateAllSpacesSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 搜尋功能\r\n  onSearch(): void {\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  onReset(): void {\r\n    this.searchKeyword = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 空間搜尋功能\r\n  onSpaceSearch(): void {\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  onSpaceReset(): void {\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 分頁功能\r\n  pageChanged(page: number): void {\r\n    this.pageIndex = page;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  spacePageChanged(page: number): void {\r\n    this.spacePageIndex = page;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 模態框操作\r\n  openCreateModal(modal: TemplateRef<any>): void {\r\n    this.templateDetail = { CStatus: 1 };\r\n    this.selectedSpacesForTemplate = [];\r\n    this.loadAvailableSpaces();\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  openEditModal(modal: TemplateRef<any>, template: TemplateItem): void {\r\n    this.templateDetail = {\r\n      CTemplateId: template.CTemplateId,\r\n      CTemplateName: template.CTemplateName,\r\n      CStatus: template.CStatus || 1\r\n    };\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  onClose(ref: any): void {\r\n    ref.close();\r\n  }\r\n\r\n  onSubmit(ref: any): void {\r\n    if (!this.validateTemplateForm()) {\r\n      return;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateId) {\r\n      this.updateTemplate(ref);\r\n    } else {\r\n      this.createTemplate(ref);\r\n    }\r\n  }\r\n\r\n  // 驗證表單\r\n  validateTemplateForm(): boolean {\r\n    if (!this.templateDetail.CTemplateName?.trim()) {\r\n      this.message.showErrorMSG('請輸入模板名稱');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\r\n      this.message.showErrorMSG('請選擇模板狀態');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.selectedSpacesForTemplate.length === 0) {\r\n      this.message.showErrorMSG('請至少選擇一個空間');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 建立模板\r\n  createTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          const templateId = parseInt(response.Entries, 10);\r\n          this.saveTemplateDetails(templateId, ref);\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '建立模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 更新模板\r\n  updateTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateId: this.templateDetail.CTemplateId,\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.message.showSucessMSG('更新模板成功');\r\n          ref.close();\r\n          this.loadTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '更新模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 儲存模板詳細資料（關聯空間）\r\n  saveTemplateDetails(templateId: number, ref: any): void {\r\n    // 目前 API 可能不支援模板詳細資料的保存，暫時跳過這個步驟\r\n    this.message.showSucessMSG('建立模板成功');\r\n    ref.close();\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 刪除模板\r\n  deleteTemplate(template: TemplateItem): void {\r\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\r\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\r\n        body: { CTemplateId: template.CTemplateId }\r\n      }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('刪除模板成功');\r\n            this.loadTemplateList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '刪除模板失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    }\r\n  }\r\n\r\n  // 查看模板明細\r\n  viewTemplateDetail(template: TemplateItem, modal: TemplateRef<any>): void {\r\n    this.selectedTemplateDetail = template;\r\n    this.isLoadingTemplateDetail = true;\r\n    this.templateDetailSpaces = [];\r\n\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n\r\n    const request: GetTemplateDetailByIdArgs = {\r\n      templateId: template.CTemplateId\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        this.isLoadingTemplateDetail = false;\r\n        if (response.StatusCode === 0) {\r\n          this.templateDetailSpaces = response.Entries?.map(item => ({\r\n            CReleateId: item.CReleateId!,\r\n            CReleateName: item.CReleateName!,\r\n            CLocation: item.CLocation\r\n          })) || [];\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板明細失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間選擇相關方法\r\n  toggleSpaceSelection(space: SpacePickListItem): void {\r\n    space.selected = !space.selected;\r\n\r\n    if (space.selected) {\r\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n        this.selectedSpacesForTemplate.push({ ...space });\r\n      }\r\n    } else {\r\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  toggleAllSpaces(): void {\r\n    this.allSpacesSelected = !this.allSpacesSelected;\r\n\r\n    this.availableSpaces.forEach(space => {\r\n      space.selected = this.allSpacesSelected;\r\n      if (this.allSpacesSelected) {\r\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n          this.selectedSpacesForTemplate.push({ ...space });\r\n        }\r\n      } else {\r\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedSpace(space: SpacePickListItem): void {\r\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n\r\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\r\n    if (availableSpace) {\r\n      availableSpace.selected = false;\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  updateAllSpacesSelectedState(): void {\r\n    this.allSpacesSelected = this.availableSpaces.length > 0 &&\r\n      this.availableSpaces.every(space => space.selected);\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"alert alert-info mb-4\"\r\n      style=\"border-left: 4px solid #4a90e2; background-color: #f8f9ff; border-radius: 6px;\">\r\n      <div class=\"d-flex align-items-center\">\r\n        <i class=\"fas fa-info-circle text-primary me-3\" style=\"font-size: 1.2rem;\"></i>\r\n        <div>\r\n          <p class=\"mb-0 text-muted\" style=\"font-size: 0.9rem;\">\r\n            在此頁面您可以管理系統中的各個模板資訊，包括新增、編輯、刪除模板，以及設定模板名稱、狀態和包含的空間等。\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜尋條件區域 -->\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"templateName\" class=\"label col-3\">模板名稱</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"templateName\" nbInput class=\"w-full\" placeholder=\"搜尋模板名稱...\"\r\n              [(ngModel)]=\"searchKeyword\" (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"status\" class=\"label col-3\">狀態</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"status\" placeholder=\"選擇狀態...\" [(ngModel)]=\"searchStatus\" (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">啟用</nb-option>\r\n              <nb-option [value]=\"0\">停用</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢和重置按鈕 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-outline-secondary btn-sm me-2\" (click)=\"onReset()\">\r\n            <i class=\"fas fa-undo me-1\"></i>重置\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search me-1\"></i>查詢\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openCreateModal(createModal)\">\r\n            <i class=\"fas fa-plus me-1\"></i>新增模板\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模板列表表格 -->\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table\" style=\"min-width: 800px;\">\r\n        <thead>\r\n          <tr>\r\n            <th scope=\"col\" class=\"col-3\">模板名稱</th>\r\n            <th scope=\"col\" class=\"col-2\">狀態</th>\r\n            <th scope=\"col\" class=\"col-3\">建立時間</th>\r\n            <th scope=\"col\" class=\"col-2\">建立者</th>\r\n            <th scope=\"col\" class=\"col-2\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let template of templateList\">\r\n            <td>{{ template.CTemplateName }}</td>\r\n            <td>\r\n              <span class=\"badge\" [ngClass]=\"template.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n              </span>\r\n            </td>\r\n            <td>{{ template.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>\r\n            <td>{{ template.CCreator || '-' }}</td>\r\n            <td class=\"table-actions\">\r\n              <button class=\"btn btn-outline-info btn-sm me-1\"\r\n                (click)=\"viewTemplateDetail(template, templateDetailModal)\">\r\n                <i class=\"fas fa-eye\"></i>查看\r\n              </button>\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-warning btn-sm me-1\"\r\n                (click)=\"openEditModal(editModal, template)\">\r\n                <i class=\"fas fa-edit\"></i>編輯\r\n              </button>\r\n              <button *ngIf=\"isDelete\" class=\"btn btn-outline-danger btn-sm\" (click)=\"deleteTemplate(template)\">\r\n                <i class=\"fas fa-trash\"></i>刪除\r\n              </button>\r\n            </td>\r\n          </tr>\r\n          <tr *ngIf=\"templateList.length === 0\">\r\n            <td colspan=\"5\" class=\"text-center text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>目前沒有任何模板\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\" [CollectionSize]=\"totalRecords\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<!-- 新增模板模態框 -->\r\n<ng-template #createModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-plus-circle me-2 text-success\"></i>新增模板\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"templateName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"templateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                  [(ngModel)]=\"templateDetail.CTemplateName\" name=\"templateName\" (keydown.control.enter)=\"onSubmit(ref)\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板狀態 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"templateStatus\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"templateStatus\" [(ngModel)]=\"templateDetail.CStatus\" name=\"templateStatus\"\r\n                    placeholder=\"選擇狀態\" style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 選擇空間區域 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                選擇空間\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <!-- 搜尋區域 -->\r\n                <div class=\"row mb-3\">\r\n                  <div class=\"col-md-5\">\r\n                    <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋項目名稱...\"\r\n                      [(ngModel)]=\"spaceSearchKeyword\" (keyup.enter)=\"onSpaceSearch()\"\r\n                      style=\"height: 32px; border-radius: 4px;\" />\r\n                  </div>\r\n                  <div class=\"col-md-5\">\r\n                    <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋所屬區域...\"\r\n                      [(ngModel)]=\"spaceSearchLocation\" (keyup.enter)=\"onSpaceSearch()\"\r\n                      style=\"height: 32px; border-radius: 4px;\" />\r\n                  </div>\r\n                  <div class=\"col-md-2\">\r\n                    <button class=\"btn btn-sm btn-outline-secondary me-1\" (click)=\"onSpaceReset()\">\r\n                      <i class=\"fas fa-undo\"></i>\r\n                    </button>\r\n                    <button class=\"btn btn-sm btn-secondary\" (click)=\"onSpaceSearch()\">\r\n                      <i class=\"fas fa-search\"></i>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 可選空間列表 -->\r\n                <div class=\"border rounded p-3\" style=\"max-height: 300px; overflow-y: auto; background-color: #f8f9fa;\">\r\n                  <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <div class=\"d-flex align-items-center\">\r\n                      <input type=\"checkbox\" id=\"selectAll\" [checked]=\"allSpacesSelected\" (change)=\"toggleAllSpaces()\"\r\n                        class=\"me-2\">\r\n                      <label for=\"selectAll\" class=\"mb-0 font-weight-bold\">全選當頁空間</label>\r\n                    </div>\r\n                    <small class=\"text-muted\">\r\n                      共 {{ spaceTotalRecords }} 筆，第 {{ spacePageIndex }} / {{ Math.ceil(spaceTotalRecords /\r\n                      spacePageSize) }} 頁\r\n                    </small>\r\n                  </div>\r\n\r\n                  <div class=\"row\">\r\n                    <div class=\"col-md-6\" *ngFor=\"let space of availableSpaces\">\r\n                      <div class=\"form-check mb-2\">\r\n                        <input type=\"checkbox\" class=\"form-check-input\" [id]=\"'space-' + space.CSpaceID\"\r\n                          [checked]=\"space.selected\" (change)=\"toggleSpaceSelection(space)\">\r\n                        <label class=\"form-check-label\" [for]=\"'space-' + space.CSpaceID\">\r\n                          {{ space.CName }}\r\n                          <small class=\"text-muted d-block\">{{ space.CLocation || '-' }}</small>\r\n                        </label>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- 空間列表為空時的提示 -->\r\n                  <div *ngIf=\"availableSpaces.length === 0\" class=\"text-center text-muted py-3\">\r\n                    <i class=\"fas fa-info-circle me-2\"></i>沒有符合條件的空間\r\n                  </div>\r\n\r\n                  <!-- 分頁 -->\r\n                  <div class=\"d-flex justify-content-center mt-3\" *ngIf=\"spaceTotalRecords > spacePageSize\">\r\n                    <ngx-pagination [(Page)]=\"spacePageIndex\" [PageSize]=\"spacePageSize\"\r\n                      [CollectionSize]=\"spaceTotalRecords\" (PageChange)=\"spacePageChanged($event)\">\r\n                    </ngx-pagination>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 已選空間 -->\r\n                <div class=\"mt-3\" *ngIf=\"selectedSpacesForTemplate.length > 0\">\r\n                  <label class=\"mb-2 font-weight-bold\">已選擇的空間 ({{ selectedSpacesForTemplate.length }})</label>\r\n                  <div class=\"border rounded p-2\" style=\"max-height: 150px; overflow-y: auto;\">\r\n                    <span class=\"badge badge-primary me-1 mb-1\" *ngFor=\"let space of selectedSpacesForTemplate\">\r\n                      {{ space.CName }}\r\n                      <button type=\"button\" class=\"btn-close btn-close-white ms-1\" (click)=\"removeSelectedSpace(space)\"\r\n                        style=\"font-size: 0.7rem;\"></button>\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-check me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 編輯模板模態框 -->\r\n<ng-template #editModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-edit me-2 text-warning\"></i>編輯模板\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"editTemplateName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"editTemplateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                  [(ngModel)]=\"templateDetail.CTemplateName\" name=\"editTemplateName\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板狀態 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"editTemplateStatus\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"editTemplateStatus\" [(ngModel)]=\"templateDetail.CStatus\" name=\"editTemplateStatus\"\r\n                    placeholder=\"選擇狀態\" style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-save me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 查看模板明細模態框 -->\r\n<ng-template #templateDetailModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-eye me-2 text-info\"></i>模板明細\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <!-- 模板基本資訊 -->\r\n      <div class=\"card mb-4\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header\" style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-info-circle me-2 text-primary\"></i>基本資訊\r\n          </h6>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div class=\"row\">\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-tag me-2 text-primary\"></i>模板名稱\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CTemplateName || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-toggle-on me-2 text-success\"></i>狀態\r\n                </label>\r\n                <p class=\"mb-0\">\r\n                  <span class=\"badge\"\r\n                    [ngClass]=\"selectedTemplateDetail?.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                    <i [class]=\"selectedTemplateDetail?.CStatus === 1 ? 'fas fa-check-circle' : 'fas fa-times-circle'\"\r\n                      class=\"me-1\"></i>\r\n                    {{ selectedTemplateDetail?.CStatus === 1 ? '啟用' : '停用' }}\r\n                  </span>\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-plus me-2 text-info\"></i>建立時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CCreateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-plus me-2 text-warning\"></i>建立者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CCreator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-edit me-2 text-info\"></i>更新時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CUpdateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-edit me-2 text-warning\"></i>更新者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CUpdator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 包含的空間列表 -->\r\n      <div class=\"card\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header d-flex justify-content-between align-items-center\"\r\n          style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-home me-2 text-success\"></i>包含的空間\r\n          </h6>\r\n          <span class=\"badge badge-info\">共 {{ templateDetailSpaces.length }} 個空間</span>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <!-- Loading 狀態 -->\r\n          <div *ngIf=\"isLoadingTemplateDetail\" class=\"text-center py-4\">\r\n            <i class=\"fas fa-spinner fa-spin me-2 text-primary\" style=\"font-size: 1.2rem;\"></i>\r\n            <span class=\"text-muted\">載入中...</span>\r\n          </div>\r\n\r\n          <!-- 空間列表 -->\r\n          <div *ngIf=\"!isLoadingTemplateDetail\">\r\n            <div class=\"table-responsive\" *ngIf=\"templateDetailSpaces.length > 0\">\r\n              <table class=\"table table-sm\">\r\n                <thead>\r\n                  <tr>\r\n                    <th scope=\"col\" class=\"col-1\">#</th>\r\n                    <th scope=\"col\" class=\"col-7\">項目名稱</th>\r\n                    <th scope=\"col\" class=\"col-4\">所屬區域</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let space of templateDetailSpaces; let i = index\">\r\n                    <td>{{ i + 1 }}</td>\r\n                    <td>\r\n                      <i class=\"fas fa-home me-2 text-muted\"></i>{{ space.CReleateName }}\r\n                    </td>\r\n                    <td>\r\n                      <i class=\"fas fa-map-marker-alt me-2 text-muted\"></i>{{ space.CLocation || '-' }}\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n\r\n            <!-- 沒有空間時的提示 -->\r\n            <div *ngIf=\"templateDetailSpaces.length === 0\" class=\"text-center text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>此模板尚未包含任何空間\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-secondary px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>關閉\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qCAAqC;AAEnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gCAAgC;AAO7D,SAASC,GAAG,QAAQ,MAAM;;;;;;;;;;;;;;;;;ICkDhBC,EAAA,CAAAC,cAAA,iBAAiG;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,cAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,eAAA,CAAAH,cAAA,CAA4B;IAAA,EAAC;IAC9FR,EAAA,CAAAY,SAAA,YAAgC;IAAAZ,EAAA,CAAAa,MAAA,gCAClC;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAgCLd,EAAA,CAAAC,cAAA,iBAC+C;IAA7CD,EAAA,CAAAE,UAAA,mBAAAa,mEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAY,YAAA,GAAAnB,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAc,aAAA,CAAAD,YAAA,EAAAF,WAAA,CAAkC;IAAA,EAAC;IAC5CjB,EAAA,CAAAY,SAAA,YAA2B;IAAAZ,EAAA,CAAAa,MAAA,oBAC7B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IACTd,EAAA,CAAAC,cAAA,iBAAkG;IAAnCD,EAAA,CAAAE,UAAA,mBAAAmB,mEAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,IAAA;MAAA,MAAAL,WAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiB,cAAA,CAAAN,WAAA,CAAwB;IAAA,EAAC;IAC/FjB,EAAA,CAAAY,SAAA,YAA4B;IAAAZ,EAAA,CAAAa,MAAA,oBAC9B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAnBXd,EADF,CAAAC,cAAA,SAA0C,SACpC;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEnCd,EADF,CAAAC,cAAA,SAAI,eAC2F;IAC3FD,EAAA,CAAAa,MAAA,GACF;IACFb,EADE,CAAAc,YAAA,EAAO,EACJ;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAmD;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC5Dd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,IAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAErCd,EADF,CAAAC,cAAA,cAA0B,kBAEsC;IAA5DD,EAAA,CAAAE,UAAA,mBAAAsB,0DAAA;MAAA,MAAAP,WAAA,GAAAjB,EAAA,CAAAI,aAAA,CAAAqB,GAAA,EAAAP,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAmB,sBAAA,GAAA1B,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAqB,kBAAA,CAAAV,WAAA,EAAAS,sBAAA,CAAiD;IAAA,EAAC;IAC3D1B,EAAA,CAAAY,SAAA,aAA0B;IAAAZ,EAAA,CAAAa,MAAA,qBAC5B;IAAAb,EAAA,CAAAc,YAAA,EAAS;IAKTd,EAJA,CAAA4B,UAAA,KAAAC,0CAAA,qBAC+C,KAAAC,0CAAA,qBAGmD;IAItG9B,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IArBCd,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAgC,iBAAA,CAAAf,WAAA,CAAAgB,aAAA,CAA4B;IAEVjC,EAAA,CAAA+B,SAAA,GAAwE;IAAxE/B,EAAA,CAAAkC,UAAA,YAAAjB,WAAA,CAAAkB,OAAA,6CAAwE;IAC1FnC,EAAA,CAAA+B,SAAA,EACF;IADE/B,EAAA,CAAAoC,kBAAA,MAAAnB,WAAA,CAAAkB,OAAA,8CACF;IAEEnC,EAAA,CAAA+B,SAAA,GAAmD;IAAnD/B,EAAA,CAAAgC,iBAAA,CAAAhC,EAAA,CAAAqC,WAAA,OAAApB,WAAA,CAAAqB,SAAA,sBAAmD;IACnDtC,EAAA,CAAA+B,SAAA,GAA8B;IAA9B/B,EAAA,CAAAgC,iBAAA,CAAAf,WAAA,CAAAsB,QAAA,QAA8B;IAMvBvC,EAAA,CAAA+B,SAAA,GAAc;IAAd/B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAkC,QAAA,CAAc;IAIdxC,EAAA,CAAA+B,SAAA,EAAc;IAAd/B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAmC,QAAA,CAAc;;;;;IAMzBzC,EADF,CAAAC,cAAA,SAAsC,aACgB;IAClDD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,wDACzC;IACFb,EADE,CAAAc,YAAA,EAAK,EACF;;;;;;IAwHSd,EAFJ,CAAAC,cAAA,aAA4D,cAC7B,gBAEyC;IAAvCD,EAAA,CAAAE,UAAA,oBAAAwC,yEAAA;MAAA,MAAAC,SAAA,GAAA3C,EAAA,CAAAI,aAAA,CAAAwC,IAAA,EAAA1B,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAAuC,oBAAA,CAAAF,SAAA,CAA2B;IAAA,EAAC;IADnE3C,EAAA,CAAAc,YAAA,EACoE;IACpEd,EAAA,CAAAC,cAAA,gBAAkE;IAChED,EAAA,CAAAa,MAAA,GACA;IAAAb,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IAGpEb,EAHoE,CAAAc,YAAA,EAAQ,EAChE,EACJ,EACF;;;;IAP8Cd,EAAA,CAAA+B,SAAA,GAAgC;IAC9E/B,EAD8C,CAAAkC,UAAA,kBAAAS,SAAA,CAAAG,QAAA,CAAgC,YAAAH,SAAA,CAAAI,QAAA,CACpD;IACI/C,EAAA,CAAA+B,SAAA,EAAiC;IAAjC/B,EAAA,CAAAkC,UAAA,mBAAAS,SAAA,CAAAG,QAAA,CAAiC;IAC/D9C,EAAA,CAAA+B,SAAA,EACA;IADA/B,EAAA,CAAAoC,kBAAA,MAAAO,SAAA,CAAAK,KAAA,MACA;IAAkChD,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAgC,iBAAA,CAAAW,SAAA,CAAAM,SAAA,QAA4B;;;;;IAOtEjD,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,8DACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;;IAIJd,EADF,CAAAC,cAAA,cAA0F,yBAET;IAD/DD,EAAA,CAAAkD,gBAAA,wBAAAC,sFAAAC,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAiD,cAAA,EAAAH,MAAA,MAAA9C,MAAA,CAAAiD,cAAA,GAAAH,MAAA;MAAA,OAAApD,EAAA,CAAAU,WAAA,CAAA0C,MAAA;IAAA,EAAyB;IACFpD,EAAA,CAAAE,UAAA,wBAAAiD,sFAAAC,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAcJ,MAAA,CAAAkD,gBAAA,CAAAJ,MAAA,CAAwB;IAAA,EAAC;IAEhFpD,EADE,CAAAc,YAAA,EAAiB,EACb;;;;IAHYd,EAAA,CAAA+B,SAAA,EAAyB;IAAzB/B,EAAA,CAAAyD,gBAAA,SAAAnD,MAAA,CAAAiD,cAAA,CAAyB;IACvCvD,EADwC,CAAAkC,UAAA,aAAA5B,MAAA,CAAAoD,aAAA,CAA0B,mBAAApD,MAAA,CAAAqD,iBAAA,CAC9B;;;;;;IAStC3D,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAa,MAAA,GACA;IAAAb,EAAA,CAAAC,cAAA,kBAC6B;IADgCD,EAAA,CAAAE,UAAA,mBAAA0D,gFAAA;MAAA,MAAAC,SAAA,GAAA7D,EAAA,CAAAI,aAAA,CAAA0D,IAAA,EAAA5C,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAyD,mBAAA,CAAAF,SAAA,CAA0B;IAAA,EAAC;IAEnG7D,EAD+B,CAAAc,YAAA,EAAS,EACjC;;;;IAHLd,EAAA,CAAA+B,SAAA,EACA;IADA/B,EAAA,CAAAoC,kBAAA,MAAAyB,SAAA,CAAAb,KAAA,MACA;;;;;IAJJhD,EADF,CAAAC,cAAA,cAA+D,gBACxB;IAAAD,EAAA,CAAAa,MAAA,GAA+C;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC5Fd,EAAA,CAAAC,cAAA,cAA6E;IAC3ED,EAAA,CAAA4B,UAAA,IAAAoC,uDAAA,mBAA4F;IAMhGhE,EADE,CAAAc,YAAA,EAAM,EACF;;;;IARiCd,EAAA,CAAA+B,SAAA,GAA+C;IAA/C/B,EAAA,CAAAoC,kBAAA,2CAAA9B,MAAA,CAAA2D,yBAAA,CAAAC,MAAA,MAA+C;IAEpBlE,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAkC,UAAA,YAAA5B,MAAA,CAAA2D,yBAAA,CAA4B;;;;;;IAnIxGjE,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,YAAoD;IAAAZ,EAAA,CAAAa,MAAA,gCACtD;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAiE,kEAAA;MAAA,MAAAC,OAAA,GAAApE,EAAA,CAAAI,aAAA,CAAAiE,IAAA,EAAAC,SAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiE,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAE5FpE,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAQPd,EANV,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAAkD,gBAAA,2BAAAsB,0EAAApB,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAmE,cAAA,CAAAxC,aAAA,EAAAmB,MAAA,MAAA9C,MAAA,CAAAmE,cAAA,CAAAxC,aAAA,GAAAmB,MAAA;MAAA,OAAApD,EAAA,CAAAU,WAAA,CAAA0C,MAAA;IAAA,EAA0C;IAAqBpD,EAAA,CAAAE,UAAA,mCAAAwE,kFAAA;MAAA,MAAAN,OAAA,GAAApE,EAAA,CAAAI,aAAA,CAAAiE,IAAA,EAAAC,SAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAyBJ,MAAA,CAAAqE,QAAA,CAAAP,OAAA,CAAa;IAAA,EAAC;IAKhHpE,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,qBAEe;IADZD,EAAA,CAAAkD,gBAAA,2BAAA0B,8EAAAxB,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAmE,cAAA,CAAAtC,OAAA,EAAAiB,MAAA,MAAA9C,MAAA,CAAAmE,cAAA,CAAAtC,OAAA,GAAAiB,MAAA;MAAA,OAAApD,EAAA,CAAAU,WAAA,CAAA0C,MAAA;IAAA,EAAoC;IAG/DpD,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IAOdb,EAPc,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAC4D;IAC9FD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAKFd,EAJN,CAAAC,cAAA,eAA8B,eAEN,eACE,iBAG0B;IAD5CD,EAAA,CAAAkD,gBAAA,2BAAA2B,0EAAAzB,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAwE,kBAAA,EAAA1B,MAAA,MAAA9C,MAAA,CAAAwE,kBAAA,GAAA1B,MAAA;MAAA,OAAApD,EAAA,CAAAU,WAAA,CAAA0C,MAAA;IAAA,EAAgC;IAACpD,EAAA,CAAAE,UAAA,yBAAA6E,wEAAA;MAAA/E,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAA0E,aAAA,EAAe;IAAA,EAAC;IAEpEhF,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,eAAsB,iBAG0B;IAD5CD,EAAA,CAAAkD,gBAAA,2BAAA+B,0EAAA7B,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAA4E,mBAAA,EAAA9B,MAAA,MAAA9C,MAAA,CAAA4E,mBAAA,GAAA9B,MAAA;MAAA,OAAApD,EAAA,CAAAU,WAAA,CAAA0C,MAAA;IAAA,EAAiC;IAACpD,EAAA,CAAAE,UAAA,yBAAAiF,wEAAA;MAAAnF,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAA0E,aAAA,EAAe;IAAA,EAAC;IAErEhF,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,eAAsB,kBAC2D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAkF,mEAAA;MAAApF,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA+E,YAAA,EAAc;IAAA,EAAC;IAC5ErF,EAAA,CAAAY,SAAA,aAA2B;IAC7BZ,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAAmE;IAA1BD,EAAA,CAAAE,UAAA,mBAAAoF,mEAAA;MAAAtF,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA0E,aAAA,EAAe;IAAA,EAAC;IAChEhF,EAAA,CAAAY,SAAA,aAA6B;IAGnCZ,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAwG,eAClC,cAC3B,iBAEtB;IADqDD,EAAA,CAAAE,UAAA,oBAAAqF,mEAAA;MAAAvF,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAAkF,eAAA,EAAiB;IAAA,EAAC;IAAhGxF,EAAA,CAAAc,YAAA,EACe;IACfd,EAAA,CAAAC,cAAA,iBAAqD;IAAAD,EAAA,CAAAa,MAAA,4CAAM;IAC7Db,EAD6D,CAAAc,YAAA,EAAQ,EAC/D;IACNd,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAa,MAAA,IAEF;IACFb,EADE,CAAAc,YAAA,EAAQ,EACJ;IAENd,EAAA,CAAAC,cAAA,eAAiB;IACfD,EAAA,CAAA4B,UAAA,KAAA6D,gDAAA,kBAA4D;IAU9DzF,EAAA,CAAAc,YAAA,EAAM;IAQNd,EALA,CAAA4B,UAAA,KAAA8D,gDAAA,kBAA8E,KAAAC,gDAAA,kBAKY;IAK5F3F,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAA4B,UAAA,KAAAgE,gDAAA,kBAA+D;IAe3E5F,EALU,CAAAc,YAAA,EAAM,EACF,EACF,EACF,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAA2F,mEAAA;MAAA,MAAAzB,OAAA,GAAApE,EAAA,CAAAI,aAAA,CAAAiE,IAAA,EAAAC,SAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiE,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAExEpE,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAA4F,mEAAA;MAAA,MAAA1B,OAAA,GAAApE,EAAA,CAAAI,aAAA,CAAAiE,IAAA,EAAAC,SAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAqE,QAAA,CAAAP,OAAA,CAAa;IAAA,EAAC;IAE1DpE,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IAtIMd,EAAA,CAAA+B,SAAA,IAA0C;IAA1C/B,EAAA,CAAAyD,gBAAA,YAAAnD,MAAA,CAAAmE,cAAA,CAAAxC,aAAA,CAA0C;IAiBXjC,EAAA,CAAA+B,SAAA,GAAoC;IAApC/B,EAAA,CAAAyD,gBAAA,YAAAnD,MAAA,CAAAmE,cAAA,CAAAtC,OAAA,CAAoC;IAEtDnC,EAAA,CAAA+B,SAAA,EAAW;IAAX/B,EAAA,CAAAkC,UAAA,YAAW;IAKXlC,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAkC,UAAA,YAAW;IAwBpBlC,EAAA,CAAA+B,SAAA,IAAgC;IAAhC/B,EAAA,CAAAyD,gBAAA,YAAAnD,MAAA,CAAAwE,kBAAA,CAAgC;IAKhC9E,EAAA,CAAA+B,SAAA,GAAiC;IAAjC/B,EAAA,CAAAyD,gBAAA,YAAAnD,MAAA,CAAA4E,mBAAA,CAAiC;IAiBKlF,EAAA,CAAA+B,SAAA,GAA6B;IAA7B/B,EAAA,CAAAkC,UAAA,YAAA5B,MAAA,CAAAyF,iBAAA,CAA6B;IAKnE/F,EAAA,CAAA+B,SAAA,GAEF;IAFE/B,EAAA,CAAAgG,kBAAA,aAAA1F,MAAA,CAAAqD,iBAAA,0BAAArD,MAAA,CAAAiD,cAAA,SAAAjD,MAAA,CAAA2F,IAAA,CAAAC,IAAA,CAAA5F,MAAA,CAAAqD,iBAAA,GAAArD,MAAA,CAAAoD,aAAA,cAEF;IAIwC1D,EAAA,CAAA+B,SAAA,GAAkB;IAAlB/B,EAAA,CAAAkC,UAAA,YAAA5B,MAAA,CAAA6F,eAAA,CAAkB;IAatDnG,EAAA,CAAA+B,SAAA,EAAkC;IAAlC/B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6F,eAAA,CAAAjC,MAAA,OAAkC;IAKSlE,EAAA,CAAA+B,SAAA,EAAuC;IAAvC/B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAqD,iBAAA,GAAArD,MAAA,CAAAoD,aAAA,CAAuC;IAQvE1D,EAAA,CAAA+B,SAAA,EAA0C;IAA1C/B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA2D,yBAAA,CAAAC,MAAA,KAA0C;;;;;;IAkCvElE,EAFJ,CAAAC,cAAA,mBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,aAA6C;IAAAZ,EAAA,CAAAa,MAAA,gCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAkG,kEAAA;MAAA,MAAAC,OAAA,GAAArG,EAAA,CAAAI,aAAA,CAAAkG,IAAA,EAAAhC,SAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiE,OAAA,CAAA8B,OAAA,CAAY;IAAA,EAAC;IAE5FrG,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAQPd,EANV,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACW,kBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,kBAG6C;IADvED,EAAA,CAAAkD,gBAAA,2BAAAqD,0EAAAnD,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAkG,IAAA;MAAA,MAAAhG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAmE,cAAA,CAAAxC,aAAA,EAAAmB,MAAA,MAAA9C,MAAA,CAAAmE,cAAA,CAAAxC,aAAA,GAAAmB,MAAA;MAAA,OAAApD,EAAA,CAAAU,WAAA,CAAA0C,MAAA;IAAA,EAA0C;IAKpDpD,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,kBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,sBAEe;IADRD,EAAA,CAAAkD,gBAAA,2BAAAsD,8EAAApD,MAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAkG,IAAA;MAAA,MAAAhG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAsD,kBAAA,CAAAhD,MAAA,CAAAmE,cAAA,CAAAtC,OAAA,EAAAiB,MAAA,MAAA9C,MAAA,CAAAmE,cAAA,CAAAtC,OAAA,GAAAiB,MAAA;MAAA,OAAApD,EAAA,CAAAU,WAAA,CAAA0C,MAAA;IAAA,EAAoC;IAGnEpD,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IASlBb,EATkB,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAAuG,mEAAA;MAAA,MAAAJ,OAAA,GAAArG,EAAA,CAAAI,aAAA,CAAAkG,IAAA,EAAAhC,SAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiE,OAAA,CAAA8B,OAAA,CAAY;IAAA,EAAC;IAExErG,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAAwG,mEAAA;MAAA,MAAAL,OAAA,GAAArG,EAAA,CAAAI,aAAA,CAAAkG,IAAA,EAAAhC,SAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAqE,QAAA,CAAA0B,OAAA,CAAa;IAAA,EAAC;IAE1DrG,EAAA,CAAAY,SAAA,cAAgC;IAAAZ,EAAA,CAAAa,MAAA,qBAClC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IAhDMd,EAAA,CAAA+B,SAAA,IAA0C;IAA1C/B,EAAA,CAAAyD,gBAAA,YAAAnD,MAAA,CAAAmE,cAAA,CAAAxC,aAAA,CAA0C;IAiBPjC,EAAA,CAAA+B,SAAA,GAAoC;IAApC/B,EAAA,CAAAyD,gBAAA,YAAAnD,MAAA,CAAAmE,cAAA,CAAAtC,OAAA,CAAoC;IAE1DnC,EAAA,CAAA+B,SAAA,EAAW;IAAX/B,EAAA,CAAAkC,UAAA,YAAW;IAKXlC,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAkC,UAAA,YAAW;;;;;IAwHhClC,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAY,SAAA,aAAmF;IACnFZ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAa,MAAA,4BAAM;IACjCb,EADiC,CAAAc,YAAA,EAAO,EAClC;;;;;IAeId,EADF,CAAAC,cAAA,SAA8D,SACxD;IAAAD,EAAA,CAAAa,MAAA,GAAW;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpBd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAA2C;IAAAZ,EAAA,CAAAa,MAAA,GAC7C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,GACvD;IACFb,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAPCd,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAgC,iBAAA,CAAA2E,KAAA,KAAW;IAE8B3G,EAAA,CAAA+B,SAAA,GAC7C;IAD6C/B,EAAA,CAAAoC,kBAAA,KAAAwE,SAAA,CAAAC,YAAA,MAC7C;IAEuD7G,EAAA,CAAA+B,SAAA,GACvD;IADuD/B,EAAA,CAAAoC,kBAAA,KAAAwE,SAAA,CAAA3D,SAAA,aACvD;;;;;IAbAjD,EAJR,CAAAC,cAAA,eAAsE,iBACtC,YACrB,SACD,cAC4B;IAAAD,EAAA,CAAAa,MAAA,QAAC;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAEtCb,EAFsC,CAAAc,YAAA,EAAK,EACpC,EACC;IACRd,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA4B,UAAA,KAAAkF,4DAAA,iBAA8D;IAWpE9G,EAFI,CAAAc,YAAA,EAAQ,EACF,EACJ;;;;IAXsBd,EAAA,CAAA+B,SAAA,IAAyB;IAAzB/B,EAAA,CAAAkC,UAAA,YAAA5B,MAAA,CAAAyG,oBAAA,CAAyB;;;;;IAcrD/G,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,0EACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IA3BRd,EAAA,CAAAC,cAAA,UAAsC;IAyBpCD,EAxBA,CAAA4B,UAAA,IAAAoF,sDAAA,oBAAsE,IAAAC,sDAAA,mBAwBa;IAGrFjH,EAAA,CAAAc,YAAA,EAAM;;;;IA3B2Bd,EAAA,CAAA+B,SAAA,EAAqC;IAArC/B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAyG,oBAAA,CAAA7C,MAAA,KAAqC;IAwB9DlE,EAAA,CAAA+B,SAAA,EAAuC;IAAvC/B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAyG,oBAAA,CAAA7C,MAAA,OAAuC;;;;;;IAxHnDlE,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,aAAyC;IAAAZ,EAAA,CAAAa,MAAA,gCAC3C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAgH,kEAAA;MAAA,MAAAC,OAAA,GAAAnH,EAAA,CAAAI,aAAA,CAAAgH,IAAA,EAAA9C,SAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiE,OAAA,CAAA4C,OAAA,CAAY;IAAA,EAAC;IAE5FnH,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAMXd,EAJN,CAAAC,cAAA,uBAAgC,eAEgD,eACkB,eAChD;IAC1CD,EAAA,CAAAY,SAAA,cAAoD;IAAAZ,EAAA,CAAAa,MAAA,iCACtD;IACFb,EADE,CAAAc,YAAA,EAAK,EACD;IAKEd,EAJR,CAAAC,cAAA,gBAAuB,eACJ,cACO,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAA4C;IAAAZ,EAAA,CAAAa,MAAA,iCAC9C;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAAkD;IAEtEb,EAFsE,CAAAc,YAAA,EAAI,EAClE,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,qBACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,cAAgB,gBAE4E;IACxFD,EAAA,CAAAY,SAAA,cACmB;IACnBZ,EAAA,CAAAa,MAAA,IACF;IAGNb,EAHM,CAAAc,YAAA,EAAO,EACL,EACA,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAmD;IAAAZ,EAAA,CAAAa,MAAA,iCACrD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,2BACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAEjEb,EAFiE,CAAAc,YAAA,EAAI,EAC7D,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAmD;IAAAZ,EAAA,CAAAa,MAAA,iCACrD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,2BACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAKvEb,EALuE,CAAAc,YAAA,EAAI,EAC7D,EACF,EACF,EACF,EACF;IAMFd,EAHJ,CAAAC,cAAA,gBAAyE,gBAEA,eACzB;IAC1CD,EAAA,CAAAY,SAAA,cAA6C;IAAAZ,EAAA,CAAAa,MAAA,uCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAa,MAAA,IAAuC;IACxEb,EADwE,CAAAc,YAAA,EAAO,EACzE;IACNd,EAAA,CAAAC,cAAA,gBAAuB;IAQrBD,EANA,CAAA4B,UAAA,KAAAyF,gDAAA,mBAA8D,KAAAC,gDAAA,kBAMxB;IA+B5CtH,EAFI,CAAAc,YAAA,EAAM,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,mBAEjD;IADtBD,EAAA,CAAAE,UAAA,mBAAAqH,mEAAA;MAAA,MAAAJ,OAAA,GAAAnH,EAAA,CAAAI,aAAA,CAAAgH,IAAA,EAAA9C,SAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiE,OAAA,CAAA4C,OAAA,CAAY;IAAA,EAAC;IAE3DnH,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA9GoBd,EAAA,CAAA+B,SAAA,IAAkD;IAAlD/B,EAAA,CAAAgC,iBAAA,EAAA1B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAAvF,aAAA,SAAkD;IAU9DjC,EAAA,CAAA+B,SAAA,GAAuF;IAAvF/B,EAAA,CAAAkC,UAAA,aAAA5B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAArF,OAAA,8CAAuF;IACpFnC,EAAA,CAAA+B,SAAA,EAA+F;IAA/F/B,EAAA,CAAAyH,UAAA,EAAAnH,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAArF,OAAA,wDAA+F;IAElGnC,EAAA,CAAA+B,SAAA,EACF;IADE/B,EAAA,CAAAoC,kBAAA,OAAA9B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAArF,OAAA,+CACF;IAScnC,EAAA,CAAA+B,SAAA,GAA2E;IAA3E/B,EAAA,CAAAgC,iBAAA,CAAAhC,EAAA,CAAAqC,WAAA,SAAA/B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAAlF,SAAA,6BAA2E;IAQ3EtC,EAAA,CAAA+B,SAAA,GAA6C;IAA7C/B,EAAA,CAAAgC,iBAAA,EAAA1B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAAjF,QAAA,SAA6C;IAQ7CvC,EAAA,CAAA+B,SAAA,GAA2E;IAA3E/B,EAAA,CAAAgC,iBAAA,CAAAhC,EAAA,CAAAqC,WAAA,SAAA/B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAAE,SAAA,6BAA2E;IAQ3E1H,EAAA,CAAA+B,SAAA,GAA6C;IAA7C/B,EAAA,CAAAgC,iBAAA,EAAA1B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAAG,QAAA,SAA6C;IAcpC3H,EAAA,CAAA+B,SAAA,GAAuC;IAAvC/B,EAAA,CAAAoC,kBAAA,YAAA9B,MAAA,CAAAyG,oBAAA,CAAA7C,MAAA,wBAAuC;IAIhElE,EAAA,CAAA+B,SAAA,GAA6B;IAA7B/B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAsH,uBAAA,CAA6B;IAM7B5H,EAAA,CAAA+B,SAAA,EAA8B;IAA9B/B,EAAA,CAAAkC,UAAA,UAAA5B,MAAA,CAAAsH,uBAAA,CAA8B;;;ADlZ9C,OAAM,MAAOC,iBAAkB,SAAQjI,aAAa;EAOlDkI,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,gBAAiC,EACjCC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAZf,KAAAnC,IAAI,GAAGA,IAAI,CAAC,CAAC;IAiBJ,KAAAoC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB;IACA,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAAhE,cAAc,GAAqB,EAAE;IACrC,KAAAiE,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAkB,IAAI;IAElC;IACA,KAAAxC,eAAe,GAAwB,EAAE;IACzC,KAAAlC,yBAAyB,GAAwB,EAAE;IACnD,KAAAa,kBAAkB,GAAW,EAAE;IAC/B,KAAAI,mBAAmB,GAAW,EAAE;IAChC,KAAA3B,cAAc,GAAG,CAAC;IAClB,KAAAG,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAoC,iBAAiB,GAAG,KAAK;IAEzB;IACA,KAAAyB,sBAAsB,GAAwB,IAAI;IAClD,KAAAT,oBAAoB,GAA8B,EAAE;IACpD,KAAAa,uBAAuB,GAAG,KAAK;EA1B/B;EA4BSgB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAD,gBAAgBA,CAAA;IACd,MAAME,OAAO,GAAG;MACd9G,aAAa,EAAE,IAAI,CAACyG,aAAa,IAAI,IAAI;MACzCvG,OAAO,EAAE,IAAI,CAACwG,YAAY;MAC1BK,SAAS,EAAE,IAAI,CAACT,SAAS;MACzBU,QAAQ,EAAE,IAAI,CAACX;KAChB;IAED,IAAI,CAACL,gBAAgB,CAACiB,mCAAmC,CAAC;MAAEC,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CAC/ErJ,GAAG,CAACsJ,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACb,YAAY,GAAGY,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACjDC,WAAW,EAAED,IAAI,CAACC,WAAY;UAC9BzH,aAAa,EAAEwH,IAAI,CAACxH,aAAc;UAClCK,SAAS,EAAEmH,IAAI,CAACnH,SAAU;UAC1BoF,SAAS,EAAE+B,IAAI,CAAC/B,SAAU;UAC1BnF,QAAQ,EAAEkH,IAAI,CAAClH,QAAQ;UACvBoF,QAAQ,EAAE8B,IAAI,CAAC9B,QAAQ;UACvBxF,OAAO,EAAEsH,IAAI,CAACtH;SACf,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAACqG,YAAY,GAAGa,QAAQ,CAACM,UAAU,IAAI,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAACxB,OAAO,CAACyB,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAhB,mBAAmBA,CAAA;IACjB,MAAMC,OAAO,GAAG;MACd/F,KAAK,EAAE,IAAI,CAAC8B,kBAAkB,IAAI,IAAI;MACtC7B,SAAS,EAAE,IAAI,CAACiC,mBAAmB,IAAI,IAAI;MAC3C/C,OAAO,EAAE,CAAC;MAAE;MACZ6G,SAAS,EAAE,IAAI,CAACzF,cAAc;MAC9B0F,QAAQ,EAAE,IAAI,CAACvF;KAChB;IAED,IAAI,CAACwE,aAAa,CAAC6B,6BAA6B,CAAC;MAAEZ,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACtErJ,GAAG,CAACsJ,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACnD,eAAe,GAAGkD,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpD3G,QAAQ,EAAE2G,IAAI,CAAC3G,QAAS;UACxBE,KAAK,EAAEyG,IAAI,CAACzG,KAAM;UAClBC,SAAS,EAAEwG,IAAI,CAACxG,SAAS;UACzBF,QAAQ,EAAE,IAAI,CAACkB,yBAAyB,CAAC+F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnH,QAAQ,KAAK2G,IAAI,CAAC3G,QAAQ;SAChF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAACa,iBAAiB,GAAG0F,QAAQ,CAACM,UAAU,IAAI,CAAC;QACjD,IAAI,CAACO,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,CACH,CAACJ,SAAS,EAAE;EACf;EAEA;EACAK,QAAQA,CAAA;IACN,IAAI,CAAC5B,SAAS,GAAG,CAAC;IAClB,IAAI,CAACM,gBAAgB,EAAE;EACzB;EAEAuB,OAAOA,CAAA;IACL,IAAI,CAAC1B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACJ,SAAS,GAAG,CAAC;IAClB,IAAI,CAACM,gBAAgB,EAAE;EACzB;EAEA;EACA7D,aAAaA,CAAA;IACX,IAAI,CAACzB,cAAc,GAAG,CAAC;IACvB,IAAI,CAACuF,mBAAmB,EAAE;EAC5B;EAEAzD,YAAYA,CAAA;IACV,IAAI,CAACP,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACI,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC3B,cAAc,GAAG,CAAC;IACvB,IAAI,CAACuF,mBAAmB,EAAE;EAC5B;EAEA;EACAuB,WAAWA,CAACC,IAAY;IACtB,IAAI,CAAC/B,SAAS,GAAG+B,IAAI;IACrB,IAAI,CAACzB,gBAAgB,EAAE;EACzB;EAEArF,gBAAgBA,CAAC8G,IAAY;IAC3B,IAAI,CAAC/G,cAAc,GAAG+G,IAAI;IAC1B,IAAI,CAACxB,mBAAmB,EAAE;EAC5B;EAEA;EACAnI,eAAeA,CAAC4J,KAAuB;IACrC,IAAI,CAAC9F,cAAc,GAAG;MAAEtC,OAAO,EAAE;IAAC,CAAE;IACpC,IAAI,CAAC8B,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAAC6E,mBAAmB,EAAE;IAC1B,IAAI,CAACd,aAAa,CAACwC,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAtJ,aAAaA,CAACmJ,KAAuB,EAAEI,QAAsB;IAC3D,IAAI,CAAClG,cAAc,GAAG;MACpBiF,WAAW,EAAEiB,QAAQ,CAACjB,WAAW;MACjCzH,aAAa,EAAE0I,QAAQ,CAAC1I,aAAa;MACrCE,OAAO,EAAEwI,QAAQ,CAACxI,OAAO,IAAI;KAC9B;IACD,IAAI,CAAC6F,aAAa,CAACwC,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAnG,OAAOA,CAACqG,GAAQ;IACdA,GAAG,CAACC,KAAK,EAAE;EACb;EAEAlG,QAAQA,CAACiG,GAAQ;IACf,IAAI,CAAC,IAAI,CAACE,oBAAoB,EAAE,EAAE;MAChC;IACF;IAEA,IAAI,IAAI,CAACrG,cAAc,CAACiF,WAAW,EAAE;MACnC,IAAI,CAACqB,cAAc,CAACH,GAAG,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACI,cAAc,CAACJ,GAAG,CAAC;IAC1B;EACF;EAEA;EACAE,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACrG,cAAc,CAACxC,aAAa,EAAEgJ,IAAI,EAAE,EAAE;MAC9C,IAAI,CAAC9C,OAAO,CAACyB,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACnF,cAAc,CAACtC,OAAO,KAAK+I,SAAS,IAAI,IAAI,CAACzG,cAAc,CAACtC,OAAO,KAAK,IAAI,EAAE;MACrF,IAAI,CAACgG,OAAO,CAACyB,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAACnF,cAAc,CAACiF,WAAW,IAAI,IAAI,CAACzF,yBAAyB,CAACC,MAAM,KAAK,CAAC,EAAE;MACnF,IAAI,CAACiE,OAAO,CAACyB,YAAY,CAAC,WAAW,CAAC;MACtC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;EAEA;EACAoB,cAAcA,CAACJ,GAAQ;IACrB,MAAMO,YAAY,GAAqB;MACrClJ,aAAa,EAAE,IAAI,CAACwC,cAAc,CAACxC,aAAa;MAChDE,OAAO,EAAE,IAAI,CAACsC,cAAc,CAACtC;KAC9B;IAED,IAAI,CAAC8F,gBAAgB,CAACmD,gCAAgC,CAAC;MAAEjC,IAAI,EAAEgC;IAAY,CAAE,CAAC,CAAC/B,IAAI,CACjFrJ,GAAG,CAACsJ,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;QACjD,MAAM8B,UAAU,GAAGC,QAAQ,CAACjC,QAAQ,CAACE,OAAO,EAAE,EAAE,CAAC;QACjD,IAAI,CAACgC,mBAAmB,CAACF,UAAU,EAAET,GAAG,CAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAACzC,OAAO,CAACyB,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAiB,cAAcA,CAACH,GAAQ;IACrB,MAAMO,YAAY,GAAqB;MACrCzB,WAAW,EAAE,IAAI,CAACjF,cAAc,CAACiF,WAAW;MAC5CzH,aAAa,EAAE,IAAI,CAACwC,cAAc,CAACxC,aAAa;MAChDE,OAAO,EAAE,IAAI,CAACsC,cAAc,CAACtC;KAC9B;IAED,IAAI,CAAC8F,gBAAgB,CAACmD,gCAAgC,CAAC;MAAEjC,IAAI,EAAEgC;IAAY,CAAE,CAAC,CAAC/B,IAAI,CACjFrJ,GAAG,CAACsJ,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACnB,OAAO,CAACqD,aAAa,CAAC,QAAQ,CAAC;QACpCZ,GAAG,CAACC,KAAK,EAAE;QACX,IAAI,CAAChC,gBAAgB,EAAE;MACzB,CAAC,MAAM;QACL,IAAI,CAACV,OAAO,CAACyB,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAyB,mBAAmBA,CAACF,UAAkB,EAAET,GAAQ;IAC9C;IACA,IAAI,CAACzC,OAAO,CAACqD,aAAa,CAAC,QAAQ,CAAC;IACpCZ,GAAG,CAACC,KAAK,EAAE;IACX,IAAI,CAAChC,gBAAgB,EAAE;EACzB;EAEA;EACAtH,cAAcA,CAACoJ,QAAsB;IACnC,IAAIc,OAAO,CAAC,WAAWd,QAAQ,CAAC1I,aAAa,KAAK,CAAC,EAAE;MACnD,IAAI,CAACgG,gBAAgB,CAACyD,kCAAkC,CAAC;QACvDvC,IAAI,EAAE;UAAEO,WAAW,EAAEiB,QAAQ,CAACjB;QAAW;OAC1C,CAAC,CAACN,IAAI,CACLrJ,GAAG,CAACsJ,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAACnB,OAAO,CAACqD,aAAa,CAAC,QAAQ,CAAC;UACpC,IAAI,CAAC3C,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAACV,OAAO,CAACyB,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;QACzD;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf;EACF;EAEA;EACAnI,kBAAkBA,CAACgJ,QAAsB,EAAEJ,KAAuB;IAChE,IAAI,CAAC/C,sBAAsB,GAAGmD,QAAQ;IACtC,IAAI,CAAC/C,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACb,oBAAoB,GAAG,EAAE;IAE9B,IAAI,CAACiB,aAAa,CAACwC,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;IAEF,MAAM3B,OAAO,GAA8B;MACzCsC,UAAU,EAAEV,QAAQ,CAACjB;KACtB;IAED,IAAI,CAACzB,gBAAgB,CAAC0D,yCAAyC,CAAC;MAAExC,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACrFrJ,GAAG,CAACsJ,QAAQ,IAAG;MACb,IAAI,CAACzB,uBAAuB,GAAG,KAAK;MACpC,IAAIyB,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACvC,oBAAoB,GAAGsC,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACzDmC,UAAU,EAAEnC,IAAI,CAACmC,UAAW;UAC5B/E,YAAY,EAAE4C,IAAI,CAAC5C,YAAa;UAChC5D,SAAS,EAAEwG,IAAI,CAACxG;SACjB,CAAC,CAAC,IAAI,EAAE;MACX,CAAC,MAAM;QACL,IAAI,CAACkF,OAAO,CAACyB,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAjH,oBAAoBA,CAACgJ,KAAwB;IAC3CA,KAAK,CAAC9I,QAAQ,GAAG,CAAC8I,KAAK,CAAC9I,QAAQ;IAEhC,IAAI8I,KAAK,CAAC9I,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAACkB,yBAAyB,CAAC+F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnH,QAAQ,KAAK+I,KAAK,CAAC/I,QAAQ,CAAC,EAAE;QAC5E,IAAI,CAACmB,yBAAyB,CAAC6H,IAAI,CAAC;UAAE,GAAGD;QAAK,CAAE,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAAC5H,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC8H,MAAM,CAAC9B,CAAC,IAAIA,CAAC,CAACnH,QAAQ,KAAK+I,KAAK,CAAC/I,QAAQ,CAAC;IAC5G;IAEA,IAAI,CAACoH,4BAA4B,EAAE;EACrC;EAEA1E,eAAeA,CAAA;IACb,IAAI,CAACO,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAEhD,IAAI,CAACI,eAAe,CAAC6F,OAAO,CAACH,KAAK,IAAG;MACnCA,KAAK,CAAC9I,QAAQ,GAAG,IAAI,CAACgD,iBAAiB;MACvC,IAAI,IAAI,CAACA,iBAAiB,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC9B,yBAAyB,CAAC+F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnH,QAAQ,KAAK+I,KAAK,CAAC/I,QAAQ,CAAC,EAAE;UAC5E,IAAI,CAACmB,yBAAyB,CAAC6H,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACnD;MACF,CAAC,MAAM;QACL,IAAI,CAAC5H,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC8H,MAAM,CAAC9B,CAAC,IAAIA,CAAC,CAACnH,QAAQ,KAAK+I,KAAK,CAAC/I,QAAQ,CAAC;MAC5G;IACF,CAAC,CAAC;EACJ;EAEAiB,mBAAmBA,CAAC8H,KAAwB;IAC1C,IAAI,CAAC5H,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC8H,MAAM,CAAC9B,CAAC,IAAIA,CAAC,CAACnH,QAAQ,KAAK+I,KAAK,CAAC/I,QAAQ,CAAC;IAE1G,MAAMmJ,cAAc,GAAG,IAAI,CAAC9F,eAAe,CAAC+F,IAAI,CAACjC,CAAC,IAAIA,CAAC,CAACnH,QAAQ,KAAK+I,KAAK,CAAC/I,QAAQ,CAAC;IACpF,IAAImJ,cAAc,EAAE;MAClBA,cAAc,CAAClJ,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAACmH,4BAA4B,EAAE;EACrC;EAEAA,4BAA4BA,CAAA;IAC1B,IAAI,CAACnE,iBAAiB,GAAG,IAAI,CAACI,eAAe,CAACjC,MAAM,GAAG,CAAC,IACtD,IAAI,CAACiC,eAAe,CAACgG,KAAK,CAACN,KAAK,IAAIA,KAAK,CAAC9I,QAAQ,CAAC;EACvD;;;uCArVW8E,iBAAiB,EAAA7H,EAAA,CAAAoM,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtM,EAAA,CAAAoM,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAxM,EAAA,CAAAoM,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA1M,EAAA,CAAAoM,iBAAA,CAAAK,EAAA,CAAAE,YAAA,GAAA3M,EAAA,CAAAoM,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA7M,EAAA,CAAAoM,iBAAA,CAAAU,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAjBlF,iBAAiB;MAAAmF,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;UCrD5BnN,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,SAAA,qBAAiC;UACnCZ,EAAA,CAAAc,YAAA,EAAiB;UAIbd,EAHJ,CAAAC,cAAA,mBAAc,aAE6E,aAChD;UACrCD,EAAA,CAAAY,SAAA,WAA+E;UAE7EZ,EADF,CAAAC,cAAA,UAAK,WACmD;UACpDD,EAAA,CAAAa,MAAA,iUACF;UAGNb,EAHM,CAAAc,YAAA,EAAI,EACA,EACF,EACF;UAMAd,EAHN,CAAAC,cAAA,cAA8B,cACN,eACqC,iBACT;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAExDd,EADF,CAAAC,cAAA,yBAA6B,iBAE8B;UAAvDD,EAAA,CAAAkD,gBAAA,2BAAAmK,2DAAAjK,MAAA;YAAApD,EAAA,CAAAI,aAAA,CAAAkN,GAAA;YAAAtN,EAAA,CAAAsD,kBAAA,CAAA8J,GAAA,CAAA1E,aAAA,EAAAtF,MAAA,MAAAgK,GAAA,CAAA1E,aAAA,GAAAtF,MAAA;YAAA,OAAApD,EAAA,CAAAU,WAAA,CAAA0C,MAAA;UAAA,EAA2B;UAACpD,EAAA,CAAAE,UAAA,yBAAAqN,yDAAA;YAAAvN,EAAA,CAAAI,aAAA,CAAAkN,GAAA;YAAA,OAAAtN,EAAA,CAAAU,WAAA,CAAe0M,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAG9DnK,EAJM,CAAAc,YAAA,EACyD,EAC3C,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACf;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAEhDd,EADF,CAAAC,cAAA,yBAA6B,qBAC2E;UAAzDD,EAAA,CAAAkD,gBAAA,2BAAAsK,+DAAApK,MAAA;YAAApD,EAAA,CAAAI,aAAA,CAAAkN,GAAA;YAAAtN,EAAA,CAAAsD,kBAAA,CAAA8J,GAAA,CAAAzE,YAAA,EAAAvF,MAAA,MAAAgK,GAAA,CAAAzE,YAAA,GAAAvF,MAAA;YAAA,OAAApD,EAAA,CAAAU,WAAA,CAAA0C,MAAA;UAAA,EAA0B;UAACpD,EAAA,CAAAE,UAAA,4BAAAuN,gEAAA;YAAAzN,EAAA,CAAAI,aAAA,CAAAkN,GAAA;YAAA,OAAAtN,EAAA,CAAAU,WAAA,CAAkB0M,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UACnGnK,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACxCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACrCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAIjCb,EAJiC,CAAAc,YAAA,EAAY,EAC3B,EACE,EACZ,EACF;UAENd,EAAA,CAAAY,SAAA,cAEM;UAKFZ,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACmB;UAApBD,EAAA,CAAAE,UAAA,mBAAAwN,oDAAA;YAAA1N,EAAA,CAAAI,aAAA,CAAAkN,GAAA;YAAA,OAAAtN,EAAA,CAAAU,WAAA,CAAS0M,GAAA,CAAAhD,OAAA,EAAS;UAAA,EAAC;UACvEpK,EAAA,CAAAY,SAAA,aAAgC;UAAAZ,EAAA,CAAAa,MAAA,qBAClC;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAC,cAAA,kBAA8D;UAArBD,EAAA,CAAAE,UAAA,mBAAAyN,oDAAA;YAAA3N,EAAA,CAAAI,aAAA,CAAAkN,GAAA;YAAA,OAAAtN,EAAA,CAAAU,WAAA,CAAS0M,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAC3DnK,EAAA,CAAAY,SAAA,aAAkC;UAAAZ,EAAA,CAAAa,MAAA,qBACpC;UAEJb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAGJd,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAA4B,UAAA,KAAAgM,oCAAA,qBAAiG;UAKvG5N,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;UAOEd,EAJR,CAAAC,cAAA,eAAmC,iBACc,aACtC,UACD,cAC4B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,0BAAG;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACtCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAEpCb,EAFoC,CAAAc,YAAA,EAAK,EAClC,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UAwBLD,EAvBA,CAAA4B,UAAA,KAAAiM,gCAAA,mBAA0C,KAAAC,gCAAA,iBAuBJ;UAQ9C9N,EAHM,CAAAc,YAAA,EAAQ,EACF,EACJ,EACO;UAEbd,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADrBD,EAAA,CAAAkD,gBAAA,wBAAA6K,iEAAA3K,MAAA;YAAApD,EAAA,CAAAI,aAAA,CAAAkN,GAAA;YAAAtN,EAAA,CAAAsD,kBAAA,CAAA8J,GAAA,CAAA7E,SAAA,EAAAnF,MAAA,MAAAgK,GAAA,CAAA7E,SAAA,GAAAnF,MAAA;YAAA,OAAApD,EAAA,CAAAU,WAAA,CAAA0C,MAAA;UAAA,EAAoB;UAClCpD,EAAA,CAAAE,UAAA,wBAAA6N,iEAAA3K,MAAA;YAAApD,EAAA,CAAAI,aAAA,CAAAkN,GAAA;YAAA,OAAAtN,EAAA,CAAAU,WAAA,CAAc0M,GAAA,CAAA/C,WAAA,CAAAjH,MAAA,CAAmB;UAAA,EAAC;UAGxCpD,EAFI,CAAAc,YAAA,EAAiB,EACF,EACT;UAiPVd,EA9OA,CAAA4B,UAAA,KAAAoM,yCAAA,kCAAAhO,EAAA,CAAAiO,sBAAA,CAA8C,KAAAC,yCAAA,iCAAAlO,EAAA,CAAAiO,sBAAA,CAkKF,KAAAE,yCAAA,kCAAAnO,EAAA,CAAAiO,sBAAA,CA4EU;;;UA9UxCjO,EAAA,CAAA+B,SAAA,IAA2B;UAA3B/B,EAAA,CAAAyD,gBAAA,YAAA2J,GAAA,CAAA1E,aAAA,CAA2B;UASgB1I,EAAA,CAAA+B,SAAA,GAA0B;UAA1B/B,EAAA,CAAAyD,gBAAA,YAAA2J,GAAA,CAAAzE,YAAA,CAA0B;UAC1D3I,EAAA,CAAA+B,SAAA,EAAc;UAAd/B,EAAA,CAAAkC,UAAA,eAAc;UACdlC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAkC,UAAA,YAAW;UACXlC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAkC,UAAA,YAAW;UAwBgBlC,EAAA,CAAA+B,SAAA,IAAc;UAAd/B,EAAA,CAAAkC,UAAA,SAAAkL,GAAA,CAAAgB,QAAA,CAAc;UAoB/BpO,EAAA,CAAA+B,SAAA,IAAe;UAAf/B,EAAA,CAAAkC,UAAA,YAAAkL,GAAA,CAAA3E,YAAA,CAAe;UAuBnCzI,EAAA,CAAA+B,SAAA,EAA+B;UAA/B/B,EAAA,CAAAkC,UAAA,SAAAkL,GAAA,CAAA3E,YAAA,CAAAvE,MAAA,OAA+B;UAU1BlE,EAAA,CAAA+B,SAAA,GAAoB;UAApB/B,EAAA,CAAAyD,gBAAA,SAAA2J,GAAA,CAAA7E,SAAA,CAAoB;UAAuBvI,EAAtB,CAAAkC,UAAA,aAAAkL,GAAA,CAAA9E,QAAA,CAAqB,mBAAA8E,GAAA,CAAA5E,YAAA,CAAgC;;;qBDhE1F3I,YAAY,EAAAwO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZ3O,YAAY,EAAA4O,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAtC,EAAA,CAAAuC,eAAA,EAAAvC,EAAA,CAAAwC,mBAAA,EAAAxC,EAAA,CAAAyC,qBAAA,EAAAzC,EAAA,CAAA0C,qBAAA,EAAA1C,EAAA,CAAA2C,gBAAA,EAAA3C,EAAA,CAAA4C,iBAAA,EAAA5C,EAAA,CAAA6C,iBAAA,EAAA7C,EAAA,CAAA8C,oBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}