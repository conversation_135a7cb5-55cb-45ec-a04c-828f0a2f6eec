{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { GetRequirement } from 'src/services/api/models';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\nimport { TemplateCreatorComponent } from 'src/app/shared/components/template-creator/template-creator.component';\nimport { SpaceTemplateSelectorComponent } from 'src/app/shared/components/space-template-selector/space-template-selector.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = [\"spaceTemplateSelector\"];\nconst _c1 = () => [];\nfunction RequirementManagementComponent_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_69_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(169);\n      return i0.ɵɵresetView(ctx_r4.add(dialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_100_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_100_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const data_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(169);\n      return i0.ɵɵresetView(ctx_r4.onEdit(data_r8, dialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 79);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_100_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_100_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const data_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onDelete(data_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 81);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 73)(1, \"td\", 74);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 74);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 75);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 75);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 75);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 75);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 75);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 75);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 75);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 75);\n    i0.ɵɵtemplate(22, RequirementManagementComponent_tr_100_button_22_Template, 3, 0, \"button\", 76)(23, RequirementManagementComponent_tr_100_button_23_Template, 3, 0, \"button\", 77);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getHouseType(data_r8.CHouseType || i0.ɵɵpureFunction0(15, _c1)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 11, data_r8.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsShowText(data_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsSimpleText(data_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 13, data_r8.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isDelete);\n  }\n}\nfunction RequirementManagementComponent_span_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getSelectedRequirements().length, \" \");\n  }\n}\nfunction RequirementManagementComponent_small_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 83);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5148\\u52FE\\u9078\\u8981\\u52A0\\u5165\\u6A21\\u677F\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_small_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 84);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u64C7 \", ctx_r4.getSelectedRequirements().length, \" \\u500B\\u9805\\u76EE \");\n  }\n}\nfunction RequirementManagementComponent_button_138_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_138_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.selectAllRequirements());\n    });\n    i0.ɵɵelement(1, \"i\", 86);\n    i0.ɵɵtext(2, \"\\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_button_139_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_139_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.clearAllSelections());\n    });\n    i0.ɵɵelement(1, \"i\", 88);\n    i0.ɵɵtext(2, \"\\u6E05\\u9664\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_166_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_166_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const data_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(169);\n      return i0.ɵɵresetView(ctx_r4.onEdit(data_r15, dialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 79);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_166_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_166_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const data_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onDelete(data_r15));\n    });\n    i0.ɵɵelement(1, \"i\", 81);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_166_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 73)(1, \"td\", 75)(2, \"input\", 89);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_tr_166_Template_input_ngModelChange_2_listener($event) {\n      const data_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      i0.ɵɵtwoWayBindingSet(data_r15.selected, $event) || (data_r15.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function RequirementManagementComponent_tr_166_Template_input_change_2_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onRequirementSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 74);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 75);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 75);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 75);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 75);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 75);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 75);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 75);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 75);\n    i0.ɵɵtemplate(22, RequirementManagementComponent_tr_166_button_22_Template, 3, 0, \"button\", 76)(23, RequirementManagementComponent_tr_166_button_23_Template, 3, 0, \"button\", 77);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r15 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"table-active\", data_r15.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", data_r15.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r15.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r15.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getHouseType(data_r15.CHouseType || i0.ɵɵpureFunction0(17, _c1)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r15.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 13, data_r15.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsShowText(data_r15));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsSimpleText(data_r15));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 15, data_r15.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_168_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_168_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_168_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_168_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_168_app_form_group_10_nb_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 110);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const b_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", b_r20.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", b_r20.CBuildCaseName, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_168_app_form_group_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-form-group\", 95)(1, \"nb-select\", 109);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_168_app_form_group_10_Template_nb_select_selectedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CBuildCaseID, $event) || (ctx_r4.saveRequirement.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_168_app_form_group_10_nb_option_2_Template, 2, 2, \"nb-option\", 100);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\\u540D\\u7A31\")(\"labelFor\", \"CBuildCaseID\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.buildCaseList);\n  }\n}\nfunction RequirementManagementComponent_ng_template_168_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 110);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r21.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r21.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_168_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 110);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r22.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r22.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_168_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 90)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_168_span_2_Template, 2, 0, \"span\", 91)(3, RequirementManagementComponent_ng_template_168_span_3_Template, 2, 0, \"span\", 91)(4, RequirementManagementComponent_ng_template_168_span_4_Template, 2, 0, \"span\", 91)(5, RequirementManagementComponent_ng_template_168_span_5_Template, 2, 0, \"span\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 92)(7, \"div\", 8)(8, \"div\", 93)(9, \"div\", 8);\n    i0.ɵɵtemplate(10, RequirementManagementComponent_ng_template_168_app_form_group_10_Template, 3, 5, \"app-form-group\", 94);\n    i0.ɵɵelementStart(11, \"app-form-group\", 95)(12, \"input\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_168_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRequirement, $event) || (ctx_r4.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"app-form-group\", 95)(14, \"input\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_168_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CGroupName, $event) || (ctx_r4.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"app-form-group\", 95)(16, \"input\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_168_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CSort, $event) || (ctx_r4.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 95)(18, \"nb-select\", 99);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_168_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CHouseType, $event) || (ctx_r4.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_168_nb_option_19_Template, 2, 2, \"nb-option\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 95)(21, \"nb-select\", 101);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_168_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CStatus, $event) || (ctx_r4.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, RequirementManagementComponent_ng_template_168_nb_option_22_Template, 2, 2, \"nb-option\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"app-form-group\", 95)(24, \"input\", 102);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_168_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnitPrice, $event) || (ctx_r4.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"app-form-group\", 95)(26, \"input\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_168_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnit, $event) || (ctx_r4.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 95)(28, \"nb-checkbox\", 104);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_168_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsShow, $event) || (ctx_r4.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u986F\\u793A\\u5728\\u9810\\u7D04\\u9700\\u6C42\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 95)(31, \"nb-checkbox\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_168_Template_nb_checkbox_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsSimple, $event) || (ctx_r4.saveRequirement.CIsSimple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(32, \" \\u8A2D\\u5B9A\\u70BA\\u7C21\\u6613\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"app-form-group\", 95)(34, \"textarea\", 106);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_168_Template_textarea_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRemark, $event) || (ctx_r4.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(35, \"nb-card-footer\")(36, \"div\", 8)(37, \"div\", 107)(38, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_168_Template_button_click_38_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.save(ref_r23));\n    });\n    i0.ɵɵtext(39, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_168_Template_button_click_40_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r18).dialogRef;\n      return i0.ɵɵresetView(ref_r23.close());\n    });\n    i0.ɵɵtext(41, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === true && ctx_r4.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === false && ctx_r4.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === true && ctx_r4.currentTab === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === false && ctx_r4.currentTab === 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CHouseType);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentTab === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u9810\\u7D04\\u9700\\u6C42\")(\"labelFor\", \"CIsShow\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u7C21\\u6613\\u5BA2\\u8B8A\")(\"labelFor\", \"CIsSimple\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsSimple);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_170_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_170_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_170_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 110);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r25.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r25.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_170_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 110);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r26 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r26.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r26.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_170_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 90)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_170_span_2_Template, 2, 0, \"span\", 91)(3, RequirementManagementComponent_ng_template_170_span_3_Template, 2, 0, \"span\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 92)(5, \"div\", 8)(6, \"div\", 93)(7, \"div\", 8)(8, \"app-form-group\", 95)(9, \"input\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_170_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRequirement, $event) || (ctx_r4.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"app-form-group\", 95)(11, \"input\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_170_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CGroupName, $event) || (ctx_r4.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"app-form-group\", 95)(13, \"input\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_170_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CSort, $event) || (ctx_r4.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"app-form-group\", 95)(15, \"nb-select\", 99);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_170_Template_nb_select_selectedChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CHouseType, $event) || (ctx_r4.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(16, RequirementManagementComponent_ng_template_170_nb_option_16_Template, 2, 2, \"nb-option\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 95)(18, \"nb-select\", 101);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_170_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CStatus, $event) || (ctx_r4.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_170_nb_option_19_Template, 2, 2, \"nb-option\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 95)(21, \"input\", 102);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_170_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnitPrice, $event) || (ctx_r4.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"app-form-group\", 95)(23, \"input\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_170_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnit, $event) || (ctx_r4.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"app-form-group\", 95)(25, \"nb-checkbox\", 104);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_170_Template_nb_checkbox_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsShow, $event) || (ctx_r4.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(26, \" \\u986F\\u793A\\u5728\\u9810\\u7D04\\u9700\\u6C42\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 95)(28, \"nb-checkbox\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_170_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsSimple, $event) || (ctx_r4.saveRequirement.CIsSimple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u8A2D\\u5B9A\\u70BA\\u7C21\\u6613\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 95)(31, \"textarea\", 106);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_170_Template_textarea_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRemark, $event) || (ctx_r4.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\")(33, \"div\", 8)(34, \"div\", 107)(35, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_170_Template_button_click_35_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r24).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.saveTemplate(ref_r27));\n    });\n    i0.ɵɵtext(36, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_170_Template_button_click_37_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r24).dialogRef;\n      return i0.ɵɵresetView(ref_r27.close());\n    });\n    i0.ɵɵtext(38, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CHouseType);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentTab === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u9810\\u7D04\\u9700\\u6C42\")(\"labelFor\", \"CIsShow\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u7C21\\u6613\\u5BA2\\u8B8A\")(\"labelFor\", \"CIsSimple\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsSimple);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_172_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-template-viewer\", 111);\n    i0.ɵɵlistener(\"selectTemplate\", function RequirementManagementComponent_ng_template_172_Template_app_template_viewer_selectTemplate_0_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectTemplate($event));\n    })(\"close\", function RequirementManagementComponent_ng_template_172_Template_app_template_viewer_close_0_listener() {\n      const ref_r29 = i0.ɵɵrestoreView(_r28).dialogRef;\n      return i0.ɵɵresetView(ref_r29.close());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"templateType\", 1);\n  }\n}\nfunction RequirementManagementComponent_ng_template_174_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-template-creator\", 112);\n    i0.ɵɵlistener(\"templateCreated\", function RequirementManagementComponent_ng_template_174_Template_app_template_creator_templateCreated_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onTemplateCreated());\n    })(\"close\", function RequirementManagementComponent_ng_template_174_Template_app_template_creator_close_0_listener() {\n      const ref_r31 = i0.ɵɵrestoreView(_r30).dialogRef;\n      return i0.ɵɵresetView(ref_r31.close());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"availableData\", ctx_r4.selectedRequirementsForTemplate)(\"templateType\", 1);\n  }\n}\nexport class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.currentTab = 0; // 追蹤當前選中的 tab\n    this.isCreatingTemplate = false; // 控制是否正在創建模板\n    this.selectedRequirementsForTemplate = []; // 用於模板創建的選中項目\n    this.showSpaceTemplateSelector = false;\n    // Tab 切換事件處理\n    this.isFirstTabChange = true;\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CIsSimple = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CGroupName = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        if (this.currentTab === 0) {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        } else {\n          this.getListRequirementRequest.CBuildCaseID = 0;\n        }\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 根據當前 tab 決定是否需要驗證建案名稱\n    if (this.currentTab === 0) {\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    }\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[排序]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false,\n      CIsSimple: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 根據當前 tab 決定是否需要建案ID\n    if (this.currentTab === 0) {\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    } else {\n      // 模板頁面 - 設定建案ID為0，CHouseType預設[1,2]\n      this.saveRequirement.CBuildCaseID = 0;\n      this.saveRequirement.CHouseType = [1, 2];\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        // 編輯時如果是共用tab，CHouseType強制為[1,2]\n        if (_this.currentTab === 1) {\n          _this.saveRequirement.CHouseType = [1, 2];\n        }\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\n    if (this.currentTab === 1) {\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 只在建案 tab 下且有建案時才查詢\n      if (this.currentTab === 0 && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      } else if (this.currentTab === 1) {\n        this.getListRequirementRequest.CBuildCaseID = 0;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動，CHouseType預設為[1,2]\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n      this.getListRequirementRequest.CHouseType = [1, 2];\n    } else {\n      // 建案頁面的邏輯保持不變\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          // 將 API 返回的數據轉換為 SelectableRequirement 並初始化 selected 屬性\n          this.requirementList = res.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false,\n            CIsSimple: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          // 共用tab時CHouseType強制為[1,2]\n          this.saveRequirement.CHouseType = this.currentTab === 1 ? [1, 2] : res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n          this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  getCIsSimpleText(data) {\n    return data.CIsSimple ? '是' : '否';\n  }\n  onTabChange(event) {\n    // 避免頁面初始化時自動觸發重複查詢\n    if (this.isFirstTabChange) {\n      this.isFirstTabChange = false;\n      return;\n    }\n    // 根據 tabTitle 來判斷當前頁面\n    if (event.tabTitle === '共用') {\n      this.currentTab = 1;\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      this.currentTab = 0;\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    }\n    this.getList();\n  }\n  // 新增模板\n  addTemplate(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false,\n      CIsSimple: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 模板設定建案ID為0，CHouseType預設[1,2]\n    this.saveRequirement.CBuildCaseID = 0;\n    this.saveRequirement.CHouseType = [1, 2];\n    this.dialogService.open(dialog);\n  }\n  // 編輯模板\n  onEditTemplate(data, dialog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this2.isNew = false;\n      try {\n        yield _this2.getData();\n        // 編輯模板時CHouseType強制為[1,2]\n        _this2.saveRequirement.CHouseType = [1, 2];\n        _this2.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get template data\", error);\n      }\n    })();\n  }\n  // 保存模板\n  saveTemplate(ref) {\n    // 模板驗證（不包含建案名稱）\n    this.valid.clear();\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    // CHouseType強制為[1,2]\n    if (this.currentTab === 1) {\n      this.saveRequirement.CHouseType = [1, 2];\n    }\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 確保模板建案ID為0\n    const templateData = {\n      ...this.saveRequirement\n    };\n    templateData.CBuildCaseID = 0;\n    // CHouseType強制為[1,2]\n    if (this.currentTab === 1) {\n      templateData.CHouseType = [1, 2];\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: templateData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  openTemplateViewer(templateViewerDialog) {\n    this.dialogService.open(templateViewerDialog);\n  }\n  onSelectTemplate(tpl) {\n    // 查看模板邏輯\n  }\n  // 獲取選中的需求項目\n  getSelectedRequirements() {\n    return this.requirementList.filter(req => req.selected);\n  }\n  // 選中狀態變更處理\n  onRequirementSelectionChange() {\n    // 可以在這裡添加額外的邏輯，比如更新選中計數等\n  }\n  // 全選功能\n  selectAllRequirements() {\n    this.requirementList.forEach(req => req.selected = true);\n  }\n  // 清除所有選擇\n  clearAllSelections() {\n    this.requirementList.forEach(req => req.selected = false);\n  }\n  // 檢查是否全選\n  isAllSelected() {\n    return this.requirementList.length > 0 && this.requirementList.every(req => req.selected);\n  }\n  // 檢查是否部分選中（用於 indeterminate 狀態）\n  isIndeterminate() {\n    const selectedCount = this.requirementList.filter(req => req.selected).length;\n    return selectedCount > 0 && selectedCount < this.requirementList.length;\n  }\n  // 切換全選狀態\n  toggleSelectAll(event) {\n    const isChecked = event.target.checked;\n    this.requirementList.forEach(req => req.selected = isChecked);\n  }\n  // 打開模板創建器\n  openTemplateCreator(templateCreatorDialog) {\n    const selectedRequirements = this.getSelectedRequirements();\n    if (selectedRequirements.length === 0) {\n      this.message.showErrorMSG('請先選擇要加入模板的項目');\n      return;\n    }\n    // 將選中的項目存儲在屬性中，確保在模板中保持引用一致性\n    this.selectedRequirementsForTemplate = [...selectedRequirements];\n    this.isCreatingTemplate = true;\n    const dialogRef = this.dialogService.open(templateCreatorDialog);\n    // 當對話框關閉時重置狀態\n    dialogRef.onClose.subscribe(() => {\n      this.isCreatingTemplate = false;\n      this.selectedRequirementsForTemplate = [];\n    });\n  }\n  // 模板創建成功回調\n  onTemplateCreated() {\n    this.message.showSucessMSG('模板創建成功');\n    // 清除選中狀態\n    this.clearAllSelections();\n  }\n  // 空間模板相關方法\n  openSpaceTemplateSelector() {\n    if (!this.getListRequirementRequest.CBuildCaseID) {\n      this.message.showErrorMSG('請先選擇建案');\n      return;\n    }\n    this.showSpaceTemplateSelector = true;\n    // 使用 setTimeout 確保組件已渲染後再調用 open 方法\n    setTimeout(() => {\n      if (this.spaceTemplateSelectorComponent) {\n        this.spaceTemplateSelectorComponent.open();\n      }\n    }, 0);\n  }\n  onSpaceTemplateApplied(config) {\n    console.log('套用空間模板配置:', config);\n    // 這裡可以實作將模板配置轉換為需求項目並批次新增的邏輯\n    // 例如：\n    const newRequirements = config.selectedItems.map(item => ({\n      CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\n      CRequirement: `${config.spaceName}-${item.CTemplateName}`,\n      CGroupName: config.spaceName,\n      CUnitPrice: 0,\n      // API 沒有價格資訊，預設為 0\n      CUnit: '個',\n      // 預設單位\n      CStatus: 1,\n      CIsShow: true,\n      CIsSimple: false,\n      CHouseType: this.houseType.map(type => type.value),\n      // 預設所有房型\n      CRemark: `由空間模板批次新增 - ID: ${item.CTemplateId}`\n    }));\n    // TODO: 調用批次新增 API\n    // this.batchCreateRequirements(newRequirements);\n    this.message.showSucessMSG(`成功套用 ${config.selectedItems.length} 個${config.spaceName}模板項目`);\n    this.getList(); // 重新載入資料\n  }\n  onSpaceTemplateClosed() {\n    this.showSpaceTemplateSelector = false;\n  }\n  // 未來可以擴展的批次新增方法\n  batchCreateRequirements(requirements) {\n    // 實作批次新增需求的 API 調用\n    // 可以參考現有的 save 方法進行批次處理\n  }\n  static {\n    this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementManagementComponent,\n      selectors: [[\"app-requirement-management\"]],\n      viewQuery: function RequirementManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.spaceTemplateSelectorComponent = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 178,\n      vars: 37,\n      consts: [[\"dialog\", \"\"], [\"templateDialog\", \"\"], [\"templateViewerDialog\", \"\"], [\"templateCreatorDialog\", \"\"], [\"spaceTemplateSelector\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\", \"pb-0\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [3, \"value\"], [\"for\", \"isShow\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5168\\u90E8\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"isSimple\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"btn\", \"btn-warning\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-layer-group\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [3, \"changeTab\"], [\"tabTitle\", \"\\u55AE\\u5EFA\\u6848\"], [1, \"pt-3\"], [1, \"col-12\", \"mt-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [\"tabTitle\", \"\\u8DE8\\u5EFA\\u6848\"], [1, \"col-12\", \"mb-3\"], [1, \"page-description-card\"], [1, \"d-flex\", \"align-items-center\", \"mb-2\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"mr-2\"], [1, \"mb-0\", \"text-primary\"], [1, \"mb-2\", \"text-muted\"], [1, \"feature-highlights\"], [1, \"badge\", \"badge-light\", \"mr-2\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"badge\", \"badge-light\"], [1, \"fas\", \"fa-share-alt\", \"mr-1\"], [1, \"template-creation-controls\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"d-flex\", \"align-items-center\"], [1, \"template-action-buttons\", \"mr-3\"], [1, \"btn\", \"btn-primary\", \"mr-2\", 3, \"click\", \"disabled\"], [\"class\", \"badge badge-light ml-1\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"mr-1\"], [1, \"template-status-info\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"class\", \"text-success\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-secondary btn-sm mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"checked\", \"indeterminate\"], [\"class\", \"d-flex\", 3, \"table-active\", 4, \"ngFor\", \"ngForOf\"], [3, \"templateApplied\", \"closed\", \"isVisible\", \"buildCaseId\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"d-flex\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"badge\", \"badge-light\", \"ml-1\"], [1, \"text-muted\"], [1, \"text-success\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-check-square\", \"mr-1\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\", 4, \"ngIf\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CGroupName\", \"name\", \"CGroupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CSort\", \"name\", \"CSort\", \"placeholder\", \"\\u6392\\u5E8F\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\", \"disabled\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CUnit\", \"name\", \"CUnit\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsShow\", \"name\", \"CIsShow\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsSimple\", \"name\", \"CIsSimple\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CBuildCaseID\", \"name\", \"CBuildCaseID\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\"], [3, \"selectTemplate\", \"close\", \"templateType\"], [3, \"templateCreated\", \"close\", \"availableData\", \"templateType\"]],\n      template: function RequirementManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 5)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 6)(4, \"div\", 7)(5, \"div\", 8)(6, \"div\", 9)(7, \"label\", 10);\n          i0.ɵɵtext(8, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"nb-select\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CBuildCaseID, $event) || (ctx.getListRequirementRequest.CBuildCaseID = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(10, RequirementManagementComponent_nb_option_10_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"label\", 13);\n          i0.ɵɵtext(13, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"label\", 15);\n          i0.ɵɵtext(17, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CGroupName, $event) || (ctx.getListRequirementRequest.CGroupName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 8)(20, \"div\", 9)(21, \"label\", 17);\n          i0.ɵɵtext(22, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-select\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(24, RequirementManagementComponent_nb_option_24_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 9)(26, \"label\", 19);\n          i0.ɵɵtext(27, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"nb-select\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(29, \"nb-option\", 20);\n          i0.ɵɵtext(30, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"nb-option\", 20);\n          i0.ɵɵtext(32, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-option\", 20);\n          i0.ɵɵtext(34, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 9)(36, \"label\", 21);\n          i0.ɵɵtext(37, \"\\u9810\\u7D04\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"nb-select\", 22);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsShow, $event) || (ctx.getListRequirementRequest.CIsShow = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(39, \"nb-option\", 20);\n          i0.ɵɵtext(40, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"nb-option\", 20);\n          i0.ɵɵtext(42, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"nb-option\", 20);\n          i0.ɵɵtext(44, \"\\u5426\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(45, \"div\", 8)(46, \"div\", 9)(47, \"label\", 23);\n          i0.ɵɵtext(48, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"nb-select\", 22);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_49_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsSimple, $event) || (ctx.getListRequirementRequest.CIsSimple = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(50, \"nb-option\", 20);\n          i0.ɵɵtext(51, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"nb-option\", 20);\n          i0.ɵɵtext(53, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"nb-option\", 20);\n          i0.ɵɵtext(55, \"\\u5426\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(56, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 8);\n          i0.ɵɵelement(58, \"div\", 24);\n          i0.ɵɵelementStart(59, \"div\", 25)(60, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_60_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetSearch());\n          });\n          i0.ɵɵelement(61, \"i\", 27);\n          i0.ɵɵtext(62, \"\\u91CD\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_63_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(64, \"i\", 29);\n          i0.ɵɵtext(65, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_66_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openSpaceTemplateSelector());\n          });\n          i0.ɵɵelement(67, \"i\", 31);\n          i0.ɵɵtext(68, \"\\u7A7A\\u9593\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(69, RequirementManagementComponent_button_69_Template, 3, 0, \"button\", 32);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(70, \"nb-card-body\", 6)(71, \"nb-tabset\", 33);\n          i0.ɵɵlistener(\"changeTab\", function RequirementManagementComponent_Template_nb_tabset_changeTab_71_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTabChange($event));\n          });\n          i0.ɵɵelementStart(72, \"nb-tab\", 34)(73, \"div\", 35)(74, \"div\", 36)(75, \"div\", 37)(76, \"table\", 38)(77, \"thead\")(78, \"tr\", 39)(79, \"th\", 40);\n          i0.ɵɵtext(80, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"th\", 40);\n          i0.ɵɵtext(82, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"th\", 41);\n          i0.ɵɵtext(84, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"th\", 41);\n          i0.ɵɵtext(86, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"th\", 41);\n          i0.ɵɵtext(88, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"th\", 41);\n          i0.ɵɵtext(90, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"th\", 41);\n          i0.ɵɵtext(92, \"\\u9810\\u7D04\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"th\", 41);\n          i0.ɵɵtext(94, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"th\", 41);\n          i0.ɵɵtext(96, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"th\", 41);\n          i0.ɵɵtext(98, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(99, \"tbody\");\n          i0.ɵɵtemplate(100, RequirementManagementComponent_tr_100_Template, 24, 16, \"tr\", 42);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(101, \"ngx-pagination\", 43);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_101_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_101_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(102, \"nb-tab\", 44)(103, \"div\", 35)(104, \"div\", 45)(105, \"div\", 46)(106, \"div\", 47);\n          i0.ɵɵelement(107, \"i\", 48);\n          i0.ɵɵelementStart(108, \"h6\", 49);\n          i0.ɵɵtext(109, \"\\u5171\\u7528\\u9700\\u6C42\\u7BA1\\u7406\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(110, \"p\", 50);\n          i0.ɵɵtext(111, \" \\u6B64\\u9801\\u9762\\u7528\\u65BC\\u7BA1\\u7406\\u5171\\u7528\\u7684\\u5BA2\\u8B8A\\u9700\\u6C42\\u9805\\u76EE\\uFF0C\\u9019\\u4E9B\\u9805\\u76EE\\u53EF\\u4EE5\\u88AB\\u591A\\u500B\\u5EFA\\u6848\\u91CD\\u8907\\u4F7F\\u7528\\u3002 \\u60A8\\u53EF\\u4EE5\\u5728\\u6B64\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u6216\\u522A\\u9664\\u5171\\u7528\\u9700\\u6C42\\uFF0C\\u4E5F\\u53EF\\u4EE5\\u5C07\\u9078\\u4E2D\\u7684\\u9700\\u6C42\\u9805\\u76EE\\u5EFA\\u7ACB\\u70BA\\u6A21\\u677F\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"div\", 51)(113, \"span\", 52);\n          i0.ɵɵelement(114, \"i\", 53);\n          i0.ɵɵtext(115, \"\\u65B0\\u589E\\u5171\\u7528\\u9700\\u6C42 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"span\", 52);\n          i0.ɵɵelement(117, \"i\", 31);\n          i0.ɵɵtext(118, \"\\u5EFA\\u7ACB\\u6A21\\u677F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(119, \"span\", 54);\n          i0.ɵɵelement(120, \"i\", 55);\n          i0.ɵɵtext(121, \"\\u8DE8\\u5EFA\\u6848\\u5171\\u7528 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(122, \"div\", 45)(123, \"div\", 56)(124, \"div\", 57)(125, \"div\", 58)(126, \"div\", 59)(127, \"button\", 60);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_127_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const templateCreatorDialog_r10 = i0.ɵɵreference(175);\n            return i0.ɵɵresetView(ctx.openTemplateCreator(templateCreatorDialog_r10));\n          });\n          i0.ɵɵelement(128, \"i\", 53);\n          i0.ɵɵtext(129, \"\\u65B0\\u589E\\u6A21\\u677F \");\n          i0.ɵɵtemplate(130, RequirementManagementComponent_span_130_Template, 2, 1, \"span\", 61);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(131, \"button\", 62);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_131_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const templateViewerDialog_r11 = i0.ɵɵreference(173);\n            return i0.ɵɵresetView(ctx.openTemplateViewer(templateViewerDialog_r11));\n          });\n          i0.ɵɵelement(132, \"i\", 63);\n          i0.ɵɵtext(133, \"\\u67E5\\u770B\\u6A21\\u677F \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(134, \"div\", 64);\n          i0.ɵɵtemplate(135, RequirementManagementComponent_small_135_Template, 2, 0, \"small\", 65)(136, RequirementManagementComponent_small_136_Template, 2, 1, \"small\", 66);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(137, \"div\", 58);\n          i0.ɵɵtemplate(138, RequirementManagementComponent_button_138_Template, 3, 0, \"button\", 67)(139, RequirementManagementComponent_button_139_Template, 3, 0, \"button\", 68);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(140, \"div\", 36)(141, \"div\", 37)(142, \"table\", 38)(143, \"thead\")(144, \"tr\", 39)(145, \"th\", 41)(146, \"input\", 69);\n          i0.ɵɵlistener(\"change\", function RequirementManagementComponent_Template_input_change_146_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleSelectAll($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(147, \"th\", 40);\n          i0.ɵɵtext(148, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(149, \"th\", 41);\n          i0.ɵɵtext(150, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(151, \"th\", 41);\n          i0.ɵɵtext(152, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(153, \"th\", 41);\n          i0.ɵɵtext(154, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"th\", 41);\n          i0.ɵɵtext(156, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(157, \"th\", 41);\n          i0.ɵɵtext(158, \"\\u9810\\u7D04\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(159, \"th\", 41);\n          i0.ɵɵtext(160, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(161, \"th\", 41);\n          i0.ɵɵtext(162, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(163, \"th\", 41);\n          i0.ɵɵtext(164, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(165, \"tbody\");\n          i0.ɵɵtemplate(166, RequirementManagementComponent_tr_166_Template, 24, 18, \"tr\", 70);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(167, \"ngx-pagination\", 43);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_167_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_167_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(168, RequirementManagementComponent_ng_template_168_Template, 42, 48, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(170, RequirementManagementComponent_ng_template_170_Template, 39, 45, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(172, RequirementManagementComponent_ng_template_172_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(174, RequirementManagementComponent_ng_template_174_Template, 1, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(176, \"app-space-template-selector\", 71, 4);\n          i0.ɵɵlistener(\"templateApplied\", function RequirementManagementComponent_Template_app_space_template_selector_templateApplied_176_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSpaceTemplateApplied($event));\n          })(\"closed\", function RequirementManagementComponent_Template_app_space_template_selector_closed_176_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSpaceTemplateClosed());\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CBuildCaseID);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CGroupName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsShow);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsSimple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(31);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"disabled\", ctx.getSelectedRequirements().length === 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length > 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.requirementList.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length > 0);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"checked\", ctx.isAllSelected())(\"indeterminate\", ctx.isIndeterminate());\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"isVisible\", ctx.showSpaceTemplateSelector)(\"buildCaseId\", (ctx.getListRequirementRequest.CBuildCaseID == null ? null : ctx.getListRequirementRequest.CBuildCaseID.toString()) || \"\");\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.CheckboxControlValueAccessor, i9.NgControlStatus, i9.MaxLengthValidator, i9.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent, NumberWithCommasPipe, NbTabsetModule, i3.NbTabsetComponent, i3.NbTabComponent, TemplateViewerComponent, TemplateCreatorComponent, SpaceTemplateSelectorComponent],\n      styles: [\".table-active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd !important;\\n  border-left: 3px solid #2196f3;\\n}\\n\\n.page-description-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1.25rem;\\n  border-radius: 10px;\\n  border: 1px solid #dee2e6;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n  margin-bottom: 1rem;\\n}\\n.page-description-card[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #007bff;\\n}\\n.page-description-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  line-height: 1.5;\\n  margin-bottom: 0.75rem;\\n}\\n.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  color: #6c757d;\\n  border: 1px solid #dee2e6;\\n  font-size: 0.8rem;\\n  padding: 0.4rem 0.8rem;\\n  font-weight: 500;\\n}\\n.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.template-creation-controls[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n  border: 1px solid #e9ecef;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .template-action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .template-status-info[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  border: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:disabled {\\n  background: #6c757d;\\n  cursor: not-allowed;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  font-size: 0.75rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #6c757d;\\n  color: #6c757d;\\n  transition: all 0.2s ease;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-style: italic;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n  margin: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n  margin: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-active[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_selectRow 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_selectRow {\\n  0% {\\n    background-color: transparent;\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.01);\\n  }\\n  100% {\\n    background-color: #e3f2fd;\\n    transform: scale(1);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 0.25rem;\\n  }\\n  .template-creation-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.375rem 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInJlcXVpcmVtZW50LW1hbmFnZW1lbnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0E7RUFDSSxvQ0FBQTtFQUNBLDhCQUFBO0FBQUo7O0FBSUE7RUFDSSw2REFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtFQUNBLHlDQUFBO0VBQ0EsbUJBQUE7QUFESjtBQUdJO0VBQ0ksZ0JBQUE7RUFDQSxjQUFBO0FBRFI7QUFJSTtFQUNJLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxzQkFBQTtBQUZSO0FBTVE7RUFDSSx5QkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtFQUNBLGlCQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQkFBQTtBQUpaO0FBTVk7RUFDSSxjQUFBO0FBSmhCOztBQVdBO0VBQ0ksbUJBQUE7RUFDQSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0FBUko7QUFXSTtFQUNJLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUFUUjtBQWFJO0VBQ0ksaUJBQUE7QUFYUjtBQWNJO0VBQ0ksNkRBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtBQVpSO0FBY1E7RUFDSSwyQkFBQTtFQUNBLDZDQUFBO0FBWlo7QUFlUTtFQUNJLG1CQUFBO0VBQ0EsbUJBQUE7QUFiWjtBQWdCUTtFQUNJLG9DQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0FBZFo7QUFvQkk7RUFDSSxxQkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQWxCUjtBQW9CUTtFQUNJLHlCQUFBO0VBQ0EscUJBQUE7RUFDQSxZQUFBO0FBbEJaO0FBc0JJO0VBQ0ksbUJBQUE7RUFDQSxrQkFBQTtBQXBCUjtBQXVCSTtFQUNJLG1CQUFBO0VBQ0EsZ0JBQUE7QUFyQlI7O0FBMkJJO0VBQ0kscUJBQUE7RUFDQSxTQUFBO0FBeEJSOztBQThCSTtFQUNJLHFCQUFBO0VBQ0EsU0FBQTtBQTNCUjs7QUFnQ0E7RUFDSSx5QkFBQTtBQTdCSjtBQStCSTtFQUNJLGtDQUFBO0FBN0JSOztBQWlDQTtFQUNJO0lBQ0ksNkJBQUE7SUFDQSxtQkFBQTtFQTlCTjtFQWlDRTtJQUNJLHNCQUFBO0VBL0JOO0VBa0NFO0lBQ0kseUJBQUE7SUFDQSxtQkFBQTtFQWhDTjtBQUNGO0FBb0NBO0VBRVE7SUFDSSxzQkFBQTtJQUNBLFdBQUE7RUFuQ1Y7RUFxQ1U7SUFDSSxtQkFBQTtJQUNBLFlBQUE7RUFuQ2Q7RUF1Q007SUFDSSxtQkFBQTtJQUNBLHlCQUFBO0VBckNWO0FBQ0YiLCJmaWxlIjoicmVxdWlyZW1lbnQtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi8vIOmBuOS4reihjOeahOaoo+W8j1xyXG4udGFibGUtYWN0aXZlIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNlM2YyZmQgIWltcG9ydGFudDtcclxuICAgIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzIxOTZmMztcclxufVxyXG5cclxuLy8g6aCB6Z2i6Kqq5piO5Y2A5Z+f5qij5byPXHJcbi5wYWdlLWRlc2NyaXB0aW9uLWNhcmQge1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmYSAwJSwgI2U5ZWNlZiAxMDAlKTtcclxuICAgIHBhZGRpbmc6IDEuMjVyZW07XHJcbiAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2RlZTJlNjtcclxuICAgIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxuXHJcbiAgICBoNiB7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICBjb2xvcjogIzAwN2JmZjtcclxuICAgIH1cclxuXHJcbiAgICBwIHtcclxuICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgICBsaW5lLWhlaWdodDogMS41O1xyXG4gICAgICAgIG1hcmdpbi1ib3R0b206IDAuNzVyZW07XHJcbiAgICB9XHJcblxyXG4gICAgLmZlYXR1cmUtaGlnaGxpZ2h0cyB7XHJcbiAgICAgICAgLmJhZGdlIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjtcclxuICAgICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZWUyZTY7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAwLjRyZW0gMC44cmVtO1xyXG4gICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG5cclxuICAgICAgICAgICAgaSB7XHJcbiAgICAgICAgICAgICAgICBjb2xvcjogIzAwN2JmZjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxyXG5cclxuLy8g5qih5p2/566h55CG5oyJ6YiV5Y2A5Z+f5qij5byPXHJcbi50ZW1wbGF0ZS1jcmVhdGlvbi1jb250cm9scyB7XHJcbiAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xyXG4gICAgcGFkZGluZzogMXJlbTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmO1xyXG5cclxuICAgIC8vIOaooeadv+aTjeS9nOaMiemIlee1hFxyXG4gICAgLnRlbXBsYXRlLWFjdGlvbi1idXR0b25zIHtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgZ2FwOiAwLjVyZW07XHJcbiAgICB9XHJcblxyXG4gICAgLy8g5qih5p2/54uA5oWL5L+h5oGvXHJcbiAgICAudGVtcGxhdGUtc3RhdHVzLWluZm8ge1xyXG4gICAgICAgIG1hcmdpbi1sZWZ0OiAxcmVtO1xyXG4gICAgfVxyXG5cclxuICAgIC5idG4tcHJpbWFyeSB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzAwN2JmZiAwJSwgIzAwNTZiMyAxMDAlKTtcclxuICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG5cclxuICAgICAgICAmOmhvdmVyOm5vdCg6ZGlzYWJsZWQpIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xyXG4gICAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMTIzLCAyNTUsIDAuMyk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmOmRpc2FibGVkIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogIzZjNzU3ZDtcclxuICAgICAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5iYWRnZSB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcclxuICAgICAgICAgICAgY29sb3I6IHdoaXRlO1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuXHJcblxyXG4gICAgLmJ0bi1vdXRsaW5lLXNlY29uZGFyeSB7XHJcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcblxyXG4gICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICBib3JkZXItY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnRleHQtbXV0ZWQge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgZm9udC1zdHlsZTogaXRhbGljO1xyXG4gICAgfVxyXG5cclxuICAgIC50ZXh0LXN1Y2Nlc3Mge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgIH1cclxufVxyXG5cclxuLy8g6KGo5qC86aCt6YOo6KSH6YG45qGG5qij5byPXHJcbi50YWJsZSB0aGVhZCB0aCB7XHJcbiAgICAuZm9ybS1jaGVjay1pbnB1dCB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgICAgIG1hcmdpbjogMDtcclxuICAgIH1cclxufVxyXG5cclxuLy8g6KGo5qC86KGM6KSH6YG45qGG5qij5byPXHJcbi50YWJsZSB0Ym9keSB0ZCB7XHJcbiAgICAuZm9ybS1jaGVjay1pbnB1dCB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgICAgIG1hcmdpbjogMDtcclxuICAgIH1cclxufVxyXG5cclxuLy8g6YG45Lit54uA5oWL55qE6KGM5YuV55Wr5pWI5p6cXHJcbi50YWJsZSB0Ym9keSB0ciB7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG5cclxuICAgICYudGFibGUtYWN0aXZlIHtcclxuICAgICAgICBhbmltYXRpb246IHNlbGVjdFJvdyAwLjNzIGVhc2Utb3V0O1xyXG4gICAgfVxyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHNlbGVjdFJvdyB7XHJcbiAgICAwJSB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxKTtcclxuICAgIH1cclxuXHJcbiAgICA1MCUge1xyXG4gICAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wMSk7XHJcbiAgICB9XHJcblxyXG4gICAgMTAwJSB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcclxuICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEpO1xyXG4gICAgfVxyXG59XHJcblxyXG4vLyDpn7/mh4nlvI/oqK3oqIhcclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgICAudGVtcGxhdGUtY3JlYXRpb24tY29udHJvbHMge1xyXG4gICAgICAgIC5kLWZsZXgge1xyXG4gICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgICAgICBnYXA6IDAuNXJlbTtcclxuXHJcbiAgICAgICAgICAgIC5kLWZsZXgge1xyXG4gICAgICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IHJvdztcclxuICAgICAgICAgICAgICAgIGdhcDogMC4yNXJlbTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmJ0biB7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICAgIHBhZGRpbmc6IDAuMzc1cmVtIDAuNzVyZW07XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "NbTabsetModule", "BaseComponent", "takeUntilDestroyed", "GetRequirement", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "TemplateViewerComponent", "TemplateCreatorComponent", "SpaceTemplateSelectorComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "type_r3", "value", "label", "ɵɵlistener", "RequirementManagementComponent_button_69_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r4", "ɵɵnextContext", "dialog_r6", "ɵɵreference", "ɵɵresetView", "add", "ɵɵelement", "RequirementManagementComponent_tr_100_button_22_Template_button_click_0_listener", "_r7", "data_r8", "$implicit", "onEdit", "RequirementManagementComponent_tr_100_button_23_Template_button_click_0_listener", "_r9", "onDelete", "ɵɵtemplate", "RequirementManagementComponent_tr_100_button_22_Template", "RequirementManagementComponent_tr_100_button_23_Template", "ɵɵtextInterpolate", "CRequirement", "CGroupName", "getHouseType", "CHouseType", "ɵɵpureFunction0", "_c1", "CSort", "ɵɵpipeBind1", "CStatus", "getCIsShowText", "getCIsSimpleText", "CUnitPrice", "isUpdate", "isDelete", "getSelectedRequirements", "length", "RequirementManagementComponent_button_138_Template_button_click_0_listener", "_r12", "selectAllRequirements", "RequirementManagementComponent_button_139_Template_button_click_0_listener", "_r13", "clearAllSelections", "RequirementManagementComponent_tr_166_button_22_Template_button_click_0_listener", "_r16", "data_r15", "RequirementManagementComponent_tr_166_button_23_Template_button_click_0_listener", "_r17", "ɵɵtwoWayListener", "RequirementManagementComponent_tr_166_Template_input_ngModelChange_2_listener", "$event", "_r14", "ɵɵtwoWayBindingSet", "selected", "RequirementManagementComponent_tr_166_Template_input_change_2_listener", "onRequirementSelectionChange", "RequirementManagementComponent_tr_166_button_22_Template", "RequirementManagementComponent_tr_166_button_23_Template", "ɵɵclassProp", "ɵɵtwoWayProperty", "b_r20", "RequirementManagementComponent_ng_template_168_app_form_group_10_Template_nb_select_selectedChange_1_listener", "_r19", "saveRequirement", "CBuildCaseID", "RequirementManagementComponent_ng_template_168_app_form_group_10_nb_option_2_Template", "buildCaseList", "type_r21", "status_r22", "RequirementManagementComponent_ng_template_168_span_2_Template", "RequirementManagementComponent_ng_template_168_span_3_Template", "RequirementManagementComponent_ng_template_168_span_4_Template", "RequirementManagementComponent_ng_template_168_span_5_Template", "RequirementManagementComponent_ng_template_168_app_form_group_10_Template", "RequirementManagementComponent_ng_template_168_Template_input_ngModelChange_12_listener", "_r18", "RequirementManagementComponent_ng_template_168_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_ng_template_168_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_ng_template_168_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_168_nb_option_19_Template", "RequirementManagementComponent_ng_template_168_Template_nb_select_selectedChange_21_listener", "RequirementManagementComponent_ng_template_168_nb_option_22_Template", "RequirementManagementComponent_ng_template_168_Template_input_ngModelChange_24_listener", "RequirementManagementComponent_ng_template_168_Template_input_ngModelChange_26_listener", "CUnit", "RequirementManagementComponent_ng_template_168_Template_nb_checkbox_ngModelChange_28_listener", "CIsShow", "RequirementManagementComponent_ng_template_168_Template_nb_checkbox_ngModelChange_31_listener", "CIsSimple", "RequirementManagementComponent_ng_template_168_Template_textarea_ngModelChange_34_listener", "CRemark", "RequirementManagementComponent_ng_template_168_Template_button_click_38_listener", "ref_r23", "dialogRef", "save", "RequirementManagementComponent_ng_template_168_Template_button_click_40_listener", "close", "isNew", "currentTab", "houseType", "statusOptions", "type_r25", "status_r26", "RequirementManagementComponent_ng_template_170_span_2_Template", "RequirementManagementComponent_ng_template_170_span_3_Template", "RequirementManagementComponent_ng_template_170_Template_input_ngModelChange_9_listener", "_r24", "RequirementManagementComponent_ng_template_170_Template_input_ngModelChange_11_listener", "RequirementManagementComponent_ng_template_170_Template_input_ngModelChange_13_listener", "RequirementManagementComponent_ng_template_170_Template_nb_select_selectedChange_15_listener", "RequirementManagementComponent_ng_template_170_nb_option_16_Template", "RequirementManagementComponent_ng_template_170_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_170_nb_option_19_Template", "RequirementManagementComponent_ng_template_170_Template_input_ngModelChange_21_listener", "RequirementManagementComponent_ng_template_170_Template_input_ngModelChange_23_listener", "RequirementManagementComponent_ng_template_170_Template_nb_checkbox_ngModelChange_25_listener", "RequirementManagementComponent_ng_template_170_Template_nb_checkbox_ngModelChange_28_listener", "RequirementManagementComponent_ng_template_170_Template_textarea_ngModelChange_31_listener", "RequirementManagementComponent_ng_template_170_Template_button_click_35_listener", "ref_r27", "saveTemplate", "RequirementManagementComponent_ng_template_170_Template_button_click_37_listener", "RequirementManagementComponent_ng_template_172_Template_app_template_viewer_selectTemplate_0_listener", "_r28", "onSelectTemplate", "RequirementManagementComponent_ng_template_172_Template_app_template_viewer_close_0_listener", "ref_r29", "RequirementManagementComponent_ng_template_174_Template_app_template_creator_templateCreated_0_listener", "_r30", "onTemplateCreated", "RequirementManagementComponent_ng_template_174_Template_app_template_creator_close_0_listener", "ref_r31", "selectedRequirementsForTemplate", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getListRequirementRequest", "getRequirementRequest", "requirementList", "getEnumOptions", "currentBuildCase", "isCreatingTemplate", "showSpaceTemplateSelector", "isFirstTabChange", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "map", "type", "resetSearch", "setTimeout", "getList", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "errorMessages", "dialog", "open", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "item", "TotalItems", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "filter", "v", "onTabChange", "event", "tabTitle", "addTemplate", "onEditTemplate", "_this2", "templateData", "openTemplateViewer", "templateViewerDialog", "tpl", "req", "isAllSelected", "every", "isIndeterminate", "selectedCount", "toggleSelectAll", "isChecked", "target", "openTemplateCreator", "templateCreatorDialog", "selectedRequirements", "onClose", "openSpaceTemplateSelector", "spaceTemplateSelectorComponent", "onSpaceTemplateApplied", "config", "newRequirements", "selectedItems", "spaceName", "CTemplateName", "CTemplateId", "onSpaceTemplateClosed", "batchCreateRequirements", "requirements", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "BuildCaseService", "RequirementService", "i7", "PetternHelper", "i8", "Router", "DestroyRef", "selectors", "viewQuery", "RequirementManagementComponent_Query", "rf", "ctx", "RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener", "_r1", "RequirementManagementComponent_nb_option_10_Template", "RequirementManagementComponent_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_Template_input_ngModelChange_18_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_23_listener", "RequirementManagementComponent_nb_option_24_Template", "RequirementManagementComponent_Template_nb_select_ngModelChange_28_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_38_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_49_listener", "RequirementManagementComponent_Template_button_click_60_listener", "RequirementManagementComponent_Template_button_click_63_listener", "RequirementManagementComponent_Template_button_click_66_listener", "RequirementManagementComponent_button_69_Template", "RequirementManagementComponent_Template_nb_tabset_changeTab_71_listener", "RequirementManagementComponent_tr_100_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_101_listener", "RequirementManagementComponent_Template_button_click_127_listener", "templateCreatorDialog_r10", "RequirementManagementComponent_span_130_Template", "RequirementManagementComponent_Template_button_click_131_listener", "templateViewerDialog_r11", "RequirementManagementComponent_small_135_Template", "RequirementManagementComponent_small_136_Template", "RequirementManagementComponent_button_138_Template", "RequirementManagementComponent_button_139_Template", "RequirementManagementComponent_Template_input_change_146_listener", "RequirementManagementComponent_tr_166_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_167_listener", "RequirementManagementComponent_ng_template_168_Template", "ɵɵtemplateRefExtractor", "RequirementManagementComponent_ng_template_170_Template", "RequirementManagementComponent_ng_template_172_Template", "RequirementManagementComponent_ng_template_174_Template", "RequirementManagementComponent_Template_app_space_template_selector_templateApplied_176_listener", "RequirementManagementComponent_Template_app_space_template_selector_closed_176_listener", "isCreate", "toString", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i9", "DefaultValueAccessor", "NumberValueAccessor", "CheckboxControlValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "NbSelectComponent", "NbOptionComponent", "NbCheckboxComponent", "NbTabsetComponent", "NbTabComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { Template, TemplateDetail, TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\r\nimport { TemplateCreatorComponent } from 'src/app/shared/components/template-creator/template-creator.component';\r\nimport { SpaceTemplateSelectorComponent, SpaceTemplateConfig } from 'src/app/shared/components/space-template-selector/space-template-selector.component';\r\n\r\n// 擴展 GetRequirement 接口以支持選中狀態\r\ninterface SelectableRequirement extends GetRequirement {\r\n  selected?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    NbTabsetModule,\r\n    TemplateViewerComponent,\r\n    TemplateCreatorComponent,\r\n    SpaceTemplateSelectorComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: SelectableRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n  currentTab = 0; // 追蹤當前選中的 tab\r\n  isCreatingTemplate = false; // 控制是否正在創建模板\r\n  selectedRequirementsForTemplate: SelectableRequirement[] = []; // 用於模板創建的選中項目\r\n\r\n  // 空間模板選擇器相關屬性\r\n  @ViewChild('spaceTemplateSelector') spaceTemplateSelectorComponent!: SpaceTemplateSelectorComponent;\r\n  showSpaceTemplateSelector = false;\r\n\r\n\r\n\r\n  override ngOnInit(): void { }\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CIsSimple = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CGroupName = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        if (this.currentTab === 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        } else {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n        }\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    // 根據當前 tab 決定是否需要驗證建案名稱\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面需要驗證建案名稱\r\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    }\r\n\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[排序]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 根據當前 tab 決定是否需要建案ID\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n      if (this.currentBuildCase != 0) {\r\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    } else {\r\n      // 模板頁面 - 設定建案ID為0，CHouseType預設[1,2]\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n    }\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: SelectableRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      // 編輯時如果是共用tab，CHouseType強制為[1,2]\r\n      if (this.currentTab === 1) {\r\n        this.saveRequirement.CHouseType = [1, 2];\r\n      }\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: SelectableRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 只在建案 tab 下且有建案時才查詢\r\n        if (this.currentTab === 0 && this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        } else if (this.currentTab === 1) {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動，CHouseType預設為[1,2]\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n      this.getListRequirementRequest.CHouseType = [1, 2];\r\n    } else {\r\n      // 建案頁面的邏輯保持不變\r\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n      }\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            // 將 API 返回的數據轉換為 SelectableRequirement 並初始化 selected 屬性\r\n            this.requirementList = res.Entries.map(item => ({\r\n              ...item,\r\n              selected: false\r\n            }));\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  } getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            // 共用tab時CHouseType強制為[1,2]\r\n            this.saveRequirement.CHouseType = this.currentTab === 1 ? [1, 2] : (res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : []);\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\r\n            this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  getCIsSimpleText(data: any): string {\r\n    return data.CIsSimple ? '是' : '否';\r\n  }\r\n\r\n  // Tab 切換事件處理\r\n  private isFirstTabChange = true;\r\n  onTabChange(event: any) {\r\n    // 避免頁面初始化時自動觸發重複查詢\r\n    if (this.isFirstTabChange) {\r\n      this.isFirstTabChange = false;\r\n      return;\r\n    }\r\n    // 根據 tabTitle 來判斷當前頁面\r\n    if (event.tabTitle === '共用') {\r\n      this.currentTab = 1;\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      this.currentTab = 0;\r\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\r\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    }\r\n    this.getList();\r\n  }\r\n\r\n  // 新增模板\r\n  addTemplate(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    // 模板設定建案ID為0，CHouseType預設[1,2]\r\n    this.saveRequirement.CBuildCaseID = 0;\r\n    this.saveRequirement.CHouseType = [1, 2];\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 編輯模板\r\n  async onEditTemplate(data: SelectableRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      // 編輯模板時CHouseType強制為[1,2]\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get template data\", error);\r\n    }\r\n  }\r\n\r\n  // 保存模板\r\n  saveTemplate(ref: any) {\r\n    // 模板驗證（不包含建案名稱）\r\n    this.valid.clear();\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    // CHouseType強制為[1,2]\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n    }\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 確保模板建案ID為0\r\n    const templateData = { ...this.saveRequirement };\r\n    templateData.CBuildCaseID = 0;\r\n    // CHouseType強制為[1,2]\r\n    if (this.currentTab === 1) {\r\n      templateData.CHouseType = [1, 2];\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: templateData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  openTemplateViewer(templateViewerDialog: TemplateRef<any>) {\r\n    this.dialogService.open(templateViewerDialog);\r\n  }\r\n\r\n  onSelectTemplate(tpl: Template) {\r\n    // 查看模板邏輯\r\n  }\r\n\r\n  // 獲取選中的需求項目\r\n  getSelectedRequirements(): SelectableRequirement[] {\r\n    return this.requirementList.filter(req => req.selected);\r\n  }\r\n\r\n  // 選中狀態變更處理\r\n  onRequirementSelectionChange() {\r\n    // 可以在這裡添加額外的邏輯，比如更新選中計數等\r\n  }\r\n\r\n  // 全選功能\r\n  selectAllRequirements() {\r\n    this.requirementList.forEach(req => req.selected = true);\r\n  }\r\n\r\n  // 清除所有選擇\r\n  clearAllSelections() {\r\n    this.requirementList.forEach(req => req.selected = false);\r\n  }\r\n\r\n  // 檢查是否全選\r\n  isAllSelected(): boolean {\r\n    return this.requirementList.length > 0 && this.requirementList.every(req => req.selected);\r\n  }\r\n\r\n  // 檢查是否部分選中（用於 indeterminate 狀態）\r\n  isIndeterminate(): boolean {\r\n    const selectedCount = this.requirementList.filter(req => req.selected).length;\r\n    return selectedCount > 0 && selectedCount < this.requirementList.length;\r\n  }\r\n\r\n  // 切換全選狀態\r\n  toggleSelectAll(event: any) {\r\n    const isChecked = event.target.checked;\r\n    this.requirementList.forEach(req => req.selected = isChecked);\r\n  }\r\n\r\n  // 打開模板創建器\r\n  openTemplateCreator(templateCreatorDialog: TemplateRef<any>) {\r\n    const selectedRequirements = this.getSelectedRequirements();\r\n    if (selectedRequirements.length === 0) {\r\n      this.message.showErrorMSG('請先選擇要加入模板的項目');\r\n      return;\r\n    }\r\n\r\n    // 將選中的項目存儲在屬性中，確保在模板中保持引用一致性\r\n    this.selectedRequirementsForTemplate = [...selectedRequirements];\r\n    this.isCreatingTemplate = true;\r\n    const dialogRef = this.dialogService.open(templateCreatorDialog);\r\n\r\n    // 當對話框關閉時重置狀態\r\n    dialogRef.onClose.subscribe(() => {\r\n      this.isCreatingTemplate = false;\r\n      this.selectedRequirementsForTemplate = [];\r\n    });\r\n  }\r\n\r\n  // 模板創建成功回調\r\n  onTemplateCreated() {\r\n    this.message.showSucessMSG('模板創建成功');\r\n    // 清除選中狀態\r\n    this.clearAllSelections();\r\n  }\r\n\r\n  // 空間模板相關方法\r\n  openSpaceTemplateSelector() {\r\n    if (!this.getListRequirementRequest.CBuildCaseID) {\r\n      this.message.showErrorMSG('請先選擇建案');\r\n      return;\r\n    }\r\n    this.showSpaceTemplateSelector = true;\r\n\r\n    // 使用 setTimeout 確保組件已渲染後再調用 open 方法\r\n    setTimeout(() => {\r\n      if (this.spaceTemplateSelectorComponent) {\r\n        this.spaceTemplateSelectorComponent.open();\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  onSpaceTemplateApplied(config: SpaceTemplateConfig) {\r\n    console.log('套用空間模板配置:', config);\r\n\r\n    // 這裡可以實作將模板配置轉換為需求項目並批次新增的邏輯\r\n    // 例如：\r\n    const newRequirements = config.selectedItems.map(item => ({\r\n      CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,\r\n      CRequirement: `${config.spaceName}-${item.CTemplateName}`,\r\n      CGroupName: config.spaceName,\r\n      CUnitPrice: 0, // API 沒有價格資訊，預設為 0\r\n      CUnit: '個', // 預設單位\r\n      CStatus: 1,\r\n      CIsShow: true,\r\n      CIsSimple: false,\r\n      CHouseType: this.houseType.map(type => type.value), // 預設所有房型\r\n      CRemark: `由空間模板批次新增 - ID: ${item.CTemplateId}`\r\n    }));\r\n\r\n    // TODO: 調用批次新增 API\r\n    // this.batchCreateRequirements(newRequirements);\r\n\r\n    this.message.showSucessMSG(`成功套用 ${config.selectedItems.length} 個${config.spaceName}模板項目`);\r\n    this.getList(); // 重新載入資料\r\n  }\r\n\r\n  onSpaceTemplateClosed() {\r\n    this.showSpaceTemplateSelector = false;\r\n  }\r\n\r\n  // 未來可以擴展的批次新增方法\r\n  private batchCreateRequirements(requirements: any[]) {\r\n    // 實作批次新增需求的 API 調用\r\n    // 可以參考現有的 save 方法進行批次處理\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n\r\n  <!-- 搜尋區域 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"buildCase\" class=\"label mr-2\">建案</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CBuildCaseID\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"requirement\" class=\"label mr-2\">工程項目</label>\r\n          <input type=\"text\" nbInput id=\"requirement\" name=\"requirement\" placeholder=\"工程項目\"\r\n            [(ngModel)]=\"getListRequirementRequest.CRequirement\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"groupName\" class=\"label mr-2\">群組類別</label>\r\n          <input type=\"text\" nbInput id=\"groupName\" name=\"groupName\" placeholder=\"群組類別\"\r\n            [(ngModel)]=\"getListRequirementRequest.CGroupName\">\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"houseType\" class=\"label mr-2\">類型</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CHouseType\" class=\"col-9\" multiple>\r\n            <nb-option *ngFor=\"let type of houseType\" [value]=\"type.value\">\r\n              {{ type.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CStatus\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isShow\" class=\"label mr-2\">預約需求</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsShow\" class=\"col-9\" placeholder=\"全部\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isSimple\" class=\"label mr-2\">簡易客變</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsSimple\" class=\"col-9\" placeholder=\"全部\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\"></div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\"></div>\r\n        <div class=\"form-group col-12 col-md-6 text-right\">\r\n          <button class=\"btn btn-secondary mr-2\" (click)=\"resetSearch()\"><i class=\"fas fa-undo mr-1\"></i>重置</button>\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <button class=\"btn btn-warning mr-2\" (click)=\"openSpaceTemplateSelector()\"><i\r\n              class=\"fas fa-layer-group mr-1\"></i>空間模板</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <!-- Tab 導航 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <nb-tabset (changeTab)=\"onTabChange($event)\">\r\n      <nb-tab tabTitle=\"單建案\">\r\n        <div class=\"pt-3\">\r\n          <!-- 建案列表 -->\r\n          <div class=\"col-12 mt-3\">\r\n            <div class=\"table-responsive\">\r\n              <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n                <thead>\r\n                  <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n                    <th scope=\"col\" class=\"col-2\">建案名稱</th>\r\n                    <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n                    <th scope=\"col\" class=\"col-1\">群組類別</th>\r\n                    <th scope=\"col\" class=\"col-1\">類型</th>\r\n                    <th scope=\"col\" class=\"col-1\">排序</th>\r\n                    <th scope=\"col\" class=\"col-1\">狀態</th>\r\n                    <th scope=\"col\" class=\"col-1\">預約需求</th>\r\n                    <th scope=\"col\" class=\"col-1\">簡易客變</th>\r\n                    <th scope=\"col\" class=\"col-1\">單價</th>\r\n                    <th scope=\"col\" class=\"col-1\">操作功能</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n                    <td class=\"col-2\">{{ data.CBuildCaseName }}</td>\r\n                    <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n                    <td class=\"col-1\">{{ data.CGroupName }}</td>\r\n                    <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n                    <td class=\"col-1\">{{ data.CSort }}</td>\r\n                    <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n                    <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n                    <td class=\"col-1\">{{ getCIsSimpleText(data) }}</td>\r\n                    <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n                    <td class=\"col-1\">\r\n                      <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                        (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                      <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                        (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n            <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n              (PageChange)=\"getList()\">\r\n            </ngx-pagination>\r\n          </div>\r\n        </div>\r\n      </nb-tab>\r\n\r\n      <nb-tab tabTitle=\"跨建案\">\r\n        <div class=\"pt-3\">\r\n          <!-- 頁面說明區域 -->\r\n          <div class=\"col-12 mb-3\">\r\n            <div class=\"page-description-card\">\r\n              <div class=\"d-flex align-items-center mb-2\">\r\n                <i class=\"fas fa-info-circle text-primary mr-2\"></i>\r\n                <h6 class=\"mb-0 text-primary\">共用需求管理</h6>\r\n              </div>\r\n              <p class=\"mb-2 text-muted\">\r\n                此頁面用於管理共用的客變需求項目，這些項目可以被多個建案重複使用。\r\n                您可以在此新增、編輯或刪除共用需求，也可以將選中的需求項目建立為模板。\r\n              </p>\r\n              <div class=\"feature-highlights\">\r\n                <span class=\"badge badge-light mr-2\">\r\n                  <i class=\"fas fa-plus mr-1\"></i>新增共用需求\r\n                </span>\r\n                <span class=\"badge badge-light mr-2\">\r\n                  <i class=\"fas fa-layer-group mr-1\"></i>建立模板\r\n                </span>\r\n                <span class=\"badge badge-light\">\r\n                  <i class=\"fas fa-share-alt mr-1\"></i>跨建案共用\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 模板管理按鈕區域 -->\r\n          <div class=\"col-12 mb-3\">\r\n            <div class=\"template-creation-controls\">\r\n              <div class=\"d-flex justify-content-between align-items-center\">\r\n                <div class=\"d-flex align-items-center\">\r\n                  <div class=\"template-action-buttons mr-3\">\r\n                    <button class=\"btn btn-primary mr-2\" (click)=\"openTemplateCreator(templateCreatorDialog)\"\r\n                      [disabled]=\"getSelectedRequirements().length === 0\">\r\n                      <i class=\"fas fa-plus mr-1\"></i>新增模板\r\n                      <span *ngIf=\"getSelectedRequirements().length > 0\" class=\"badge badge-light ml-1\">\r\n                        {{ getSelectedRequirements().length }}\r\n                      </span>\r\n                    </button>\r\n                    <button class=\"btn btn-primary\" (click)=\"openTemplateViewer(templateViewerDialog)\">\r\n                      <i class=\"fas fa-eye mr-1\"></i>查看模板\r\n                    </button>\r\n\r\n                  </div>\r\n                  <div class=\"template-status-info\">\r\n                    <small class=\"text-muted\" *ngIf=\"getSelectedRequirements().length === 0\">\r\n                      請先勾選要加入模板的項目\r\n                    </small>\r\n                    <small class=\"text-success\" *ngIf=\"getSelectedRequirements().length > 0\">\r\n                      已選擇 {{ getSelectedRequirements().length }} 個項目\r\n                    </small>\r\n                  </div>\r\n                </div>\r\n                <div class=\"d-flex align-items-center\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm mr-2\" (click)=\"selectAllRequirements()\"\r\n                    *ngIf=\"requirementList.length > 0\">\r\n                    <i class=\"fas fa-check-square mr-1\"></i>全選\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"clearAllSelections()\"\r\n                    *ngIf=\"getSelectedRequirements().length > 0\">\r\n                    <i class=\"fas fa-times mr-1\"></i>清除選擇\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 模板列表 -->\r\n            <div class=\"col-12 mt-3\">\r\n              <div class=\"table-responsive\">\r\n                <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n                  <thead>\r\n                    <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n                      <th scope=\"col\" class=\"col-1\">\r\n                        <input type=\"checkbox\" class=\"form-check-input\" [checked]=\"isAllSelected()\"\r\n                          [indeterminate]=\"isIndeterminate()\" (change)=\"toggleSelectAll($event)\">\r\n                      </th>\r\n                      <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n                      <th scope=\"col\" class=\"col-1\">群組類別</th>\r\n                      <th scope=\"col\" class=\"col-1\">類型</th>\r\n                      <th scope=\"col\" class=\"col-1\">排序</th>\r\n                      <th scope=\"col\" class=\"col-1\">狀態</th>\r\n                      <th scope=\"col\" class=\"col-1\">預約需求</th>\r\n                      <th scope=\"col\" class=\"col-1\">簡易客變</th>\r\n                      <th scope=\"col\" class=\"col-1\">單價</th>\r\n                      <th scope=\"col\" class=\"col-1\">操作功能</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\"\r\n                      [class.table-active]=\"data.selected\">\r\n                      <td class=\"col-1\">\r\n                        <input type=\"checkbox\" class=\"form-check-input\" [(ngModel)]=\"data.selected\"\r\n                          (change)=\"onRequirementSelectionChange()\">\r\n                      </td>\r\n                      <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n                      <td class=\"col-1\">{{ data.CGroupName }}</td>\r\n                      <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n                      <td class=\"col-1\">{{ data.CSort }}</td>\r\n                      <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n                      <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n                      <td class=\"col-1\">{{ getCIsSimpleText(data) }}</td>\r\n                      <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n                      <td class=\"col-1\">\r\n                        <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                          (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                        <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                          (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n                      </td>\r\n                    </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n              <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n                (PageChange)=\"getList()\">\r\n              </ngx-pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </nb-tab>\r\n    </nb-tabset>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 建案對話框 -->\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true && currentTab === 0\">新增建案需求</span>\r\n      <span *ngIf=\"isNew===false && currentTab === 0\">編輯建案需求</span>\r\n      <span *ngIf=\"isNew===true && currentTab === 1\">新增模板需求</span>\r\n      <span *ngIf=\"isNew===false && currentTab === 1\">編輯模板需求</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'建案名稱'\" [labelFor]=\"'CBuildCaseID'\" [isRequired]=\"true\" *ngIf=\"currentTab === 0\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CBuildCaseID\" name=\"CBuildCaseID\"\r\n                [(selected)]=\"saveRequirement.CBuildCaseID\">\r\n                <nb-option langg *ngFor=\"let b of buildCaseList\" [value]=\"b.cID\"> {{b.CBuildCaseName}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'群組類別'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組類別\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple [disabled]=\"currentTab === 1\">\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'預約需求'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"true\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在預約需求清單\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'簡易客變'\" [labelFor]=\"'CIsSimple'\" [isRequired]=\"true\">\r\n              <nb-checkbox id=\"CIsSimple\" name=\"CIsSimple\" [(ngModel)]=\"saveRequirement.CIsSimple\" class=\"flex-grow-1\">\r\n                設定為簡易客變\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 模板對話框 -->\r\n<ng-template #templateDialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增</span>\r\n      <span *ngIf=\"isNew===false\">編輯</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'群組類別'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組類別\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple [disabled]=\"currentTab === 1\">\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'預約需求'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"true\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在預約需求清單\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'簡易客變'\" [labelFor]=\"'CIsSimple'\" [isRequired]=\"true\">\r\n              <nb-checkbox id=\"CIsSimple\" name=\"CIsSimple\" [(ngModel)]=\"saveRequirement.CIsSimple\" class=\"flex-grow-1\">\r\n                設定為簡易客變\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"saveTemplate(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n\r\n\r\n<!-- 共用模板查看元件 Dialog -->\r\n<ng-template #templateViewerDialog let-data let-ref=\"dialogRef\">\r\n  <app-template-viewer [templateType]=\"1\" (selectTemplate)=\"onSelectTemplate($event)\" (close)=\"ref.close()\">\r\n  </app-template-viewer>\r\n</ng-template>\r\n\r\n<!-- 模板創建元件 Dialog -->\r\n<ng-template #templateCreatorDialog let-data let-ref=\"dialogRef\">\r\n  <app-template-creator [availableData]=\"selectedRequirementsForTemplate\" [templateType]=\"1\"\r\n    (templateCreated)=\"onTemplateCreated()\" (close)=\"ref.close()\">\r\n  </app-template-creator>\r\n</ng-template>\r\n\r\n<!-- 空間模板選擇器 -->\r\n<app-space-template-selector #spaceTemplateSelector [isVisible]=\"showSpaceTemplateSelector\"\r\n  [buildCaseId]=\"getListRequirementRequest.CBuildCaseID?.toString() || ''\"\r\n  (templateApplied)=\"onSpaceTemplateApplied($event)\" (closed)=\"onSpaceTemplateClosed()\">\r\n</app-space-template-selector>"], "mappings": ";AAEA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/I,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAA6DC,cAAc,QAAwD,yBAAyB;AAC5J,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAAmCC,uBAAuB,QAAQ,qEAAqE;AACvI,SAASC,wBAAwB,QAAQ,uEAAuE;AAChH,SAASC,8BAA8B,QAA6B,qFAAqF;;;;;;;;;;;;;;;ICV7IC,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;IAkBAT,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAC,KAAA,CAAoB;IAC5DX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAE,KAAA,MACF;;;;;;IAsCFZ,EAAA,CAAAC,cAAA,iBAA4E;IAAvCD,EAAA,CAAAa,UAAA,mBAAAC,0EAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAK,GAAA,CAAAH,SAAA,CAAW;IAAA,EAAC;IAAkBnB,EAAA,CAAAuB,SAAA,YAC3C;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAyChCH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAa,UAAA,mBAAAW,iFAAA;MAAAxB,EAAA,CAAAe,aAAA,CAAAU,GAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAkB,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAW,MAAA,CAAAF,OAAA,EAAAP,SAAA,CAAmB;IAAA,EAAC;IAACnB,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAa,UAAA,mBAAAgB,iFAAA;MAAA7B,EAAA,CAAAe,aAAA,CAAAe,GAAA;MAAA,MAAAJ,OAAA,GAAA1B,EAAA,CAAAkB,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAc,QAAA,CAAAL,OAAA,CAAc;IAAA,EAAC;IAAC1B,EAAA,CAAAuB,SAAA,YAAqC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAb7EH,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAgC,UAAA,KAAAC,wDAAA,qBACgC,KAAAC,wDAAA,qBAEL;IAE/BlC,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAfeH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAjB,cAAA,CAAyB;IACzBT,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAU,YAAA,CAAuB;IACvBpC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAW,UAAA,CAAqB;IACrBrC,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAmC,iBAAA,CAAAlB,MAAA,CAAAqB,YAAA,CAAAZ,OAAA,CAAAa,UAAA,IAAAvC,EAAA,CAAAwC,eAAA,KAAAC,GAAA,GAAyC;IACzCzC,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAgB,KAAA,CAAgB;IAChB1C,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAA2C,WAAA,SAAAjB,OAAA,CAAAkB,OAAA,EAAkC;IAClC5C,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAmC,iBAAA,CAAAlB,MAAA,CAAA4B,cAAA,CAAAnB,OAAA,EAA0B;IAC1B1B,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAmC,iBAAA,CAAAlB,MAAA,CAAA6B,gBAAA,CAAApB,OAAA,EAA4B;IAC5B1B,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAA2C,WAAA,SAAAjB,OAAA,CAAAqB,UAAA,OAAkD;IAEzD/C,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+B,QAAA,CAAc;IAEdhD,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAgC,QAAA,CAAc;;;;;IAkDvBjD,EAAA,CAAAC,cAAA,eAAkF;IAChFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAS,MAAA,CAAAiC,uBAAA,GAAAC,MAAA,MACF;;;;;IAQFnD,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,iFACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IADNH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,yBAAAS,MAAA,CAAAiC,uBAAA,GAAAC,MAAA,yBACF;;;;;;IAIFnD,EAAA,CAAAC,cAAA,iBACqC;IADiBD,EAAA,CAAAa,UAAA,mBAAAuC,2EAAA;MAAApD,EAAA,CAAAe,aAAA,CAAAsC,IAAA;MAAA,MAAApC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAqC,qBAAA,EAAuB;IAAA,EAAC;IAErFtD,EAAA,CAAAuB,SAAA,YAAwC;IAAAvB,EAAA,CAAAE,MAAA,oBAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAC+C;IADED,EAAA,CAAAa,UAAA,mBAAA0C,2EAAA;MAAAvD,EAAA,CAAAe,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAwC,kBAAA,EAAoB;IAAA,EAAC;IAE7EzD,EAAA,CAAAuB,SAAA,YAAiC;IAAAvB,EAAA,CAAAE,MAAA,gCACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA0CHH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAa,UAAA,mBAAA6C,iFAAA;MAAA1D,EAAA,CAAAe,aAAA,CAAA4C,IAAA;MAAA,MAAAC,QAAA,GAAA5D,EAAA,CAAAkB,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAW,MAAA,CAAAgC,QAAA,EAAAzC,SAAA,CAAmB;IAAA,EAAC;IAACnB,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAa,UAAA,mBAAAgD,iFAAA;MAAA7D,EAAA,CAAAe,aAAA,CAAA+C,IAAA;MAAA,MAAAF,QAAA,GAAA5D,EAAA,CAAAkB,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAc,QAAA,CAAA6B,QAAA,CAAc;IAAA,EAAC;IAAC5D,EAAA,CAAAuB,SAAA,YAAqC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAf3EH,EAHJ,CAAAC,cAAA,aACuC,aACnB,gBAE4B;IADID,EAAA,CAAA+D,gBAAA,2BAAAC,8EAAAC,MAAA;MAAA,MAAAL,QAAA,GAAA5D,EAAA,CAAAe,aAAA,CAAAmD,IAAA,EAAAvC,SAAA;MAAA3B,EAAA,CAAAmE,kBAAA,CAAAP,QAAA,CAAAQ,QAAA,EAAAH,MAAA,MAAAL,QAAA,CAAAQ,QAAA,GAAAH,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAA2B;IACzEjE,EAAA,CAAAa,UAAA,oBAAAwD,uEAAA;MAAArE,EAAA,CAAAe,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAAUJ,MAAA,CAAAqD,4BAAA,EAA8B;IAAA,EAAC;IAC7CtE,EAFE,CAAAG,YAAA,EAC4C,EACzC;IACLH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAgC,UAAA,KAAAuC,wDAAA,qBACgC,KAAAC,wDAAA,qBAEL;IAE/BxE,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAnBHH,EAAA,CAAAyE,WAAA,iBAAAb,QAAA,CAAAQ,QAAA,CAAoC;IAEcpE,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAA0E,gBAAA,YAAAd,QAAA,CAAAQ,QAAA,CAA2B;IAG3DpE,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAmC,iBAAA,CAAAyB,QAAA,CAAAxB,YAAA,CAAuB;IACvBpC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAmC,iBAAA,CAAAyB,QAAA,CAAAvB,UAAA,CAAqB;IACrBrC,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAmC,iBAAA,CAAAlB,MAAA,CAAAqB,YAAA,CAAAsB,QAAA,CAAArB,UAAA,IAAAvC,EAAA,CAAAwC,eAAA,KAAAC,GAAA,GAAyC;IACzCzC,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAmC,iBAAA,CAAAyB,QAAA,CAAAlB,KAAA,CAAgB;IAChB1C,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAA2C,WAAA,SAAAiB,QAAA,CAAAhB,OAAA,EAAkC;IAClC5C,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAmC,iBAAA,CAAAlB,MAAA,CAAA4B,cAAA,CAAAe,QAAA,EAA0B;IAC1B5D,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAmC,iBAAA,CAAAlB,MAAA,CAAA6B,gBAAA,CAAAc,QAAA,EAA4B;IAC5B5D,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAA2C,WAAA,SAAAiB,QAAA,CAAAb,UAAA,OAAkD;IAEzD/C,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+B,QAAA,CAAc;IAEdhD,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAgC,QAAA,CAAc;;;;;IAsBzCjD,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5DH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7DH,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5DH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IASnDH,EAAA,CAAAC,cAAA,qBAAiE;IAACD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAjDH,EAAA,CAAAI,UAAA,UAAAuE,KAAA,CAAArE,GAAA,CAAe;IAAEN,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAQ,kBAAA,MAAAmE,KAAA,CAAAlE,cAAA,KAAoB;;;;;;IAFxFT,EADF,CAAAC,cAAA,yBAA0G,qBAE1D;IAA5CD,EAAA,CAAA+D,gBAAA,4BAAAa,8GAAAX,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAC,YAAA,EAAAd,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAC,YAAA,GAAAd,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAA2C;IAC3CjE,EAAA,CAAAgC,UAAA,IAAAgD,qFAAA,yBAAiE;IAErEhF,EADE,CAAAG,YAAA,EAAY,EACG;;;;IAL4CH,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAA0E,gBAAA,aAAAzD,MAAA,CAAA6D,eAAA,CAAAC,YAAA,CAA2C;IACZ/E,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAgE,aAAA,CAAgB;;;;;IAkB/CjF,EAAA,CAAAC,cAAA,qBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAA8E,QAAA,CAAAvE,KAAA,CAAoB;IAAEX,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAA0E,QAAA,CAAAtE,KAAA,KAAc;;;;;IAMpFZ,EAAA,CAAAC,cAAA,qBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAA+E,UAAA,CAAAxE,KAAA,CAAsB;IAC1EX,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAA2E,UAAA,CAAAvE,KAAA,KAAgB;;;;;;IAtC9BZ,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAIdD,EAHA,CAAAgC,UAAA,IAAAoD,8DAAA,mBAA+C,IAAAC,8DAAA,mBACC,IAAAC,8DAAA,mBACD,IAAAC,8DAAA,mBACC;IAClDvF,EAAA,CAAAG,YAAA,EAAiB;IAIXH,EAHN,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX;IACfD,EAAA,CAAAgC,UAAA,KAAAwD,yEAAA,6BAA0G;IAOxGxF,EADF,CAAAC,cAAA,0BAAiF,iBAEnB;IAA1DD,EAAA,CAAA+D,gBAAA,2BAAA0B,wFAAAxB,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAA1C,YAAA,EAAA6B,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAA1C,YAAA,GAAA6B,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAA0C;IAC9CjE,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAgF,iBAEpB;IAAxDD,EAAA,CAAA+D,gBAAA,2BAAA4B,wFAAA1B,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAzC,UAAA,EAAA4B,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAzC,UAAA,GAAA4B,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAwC;IAC5CjE,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAA+D,gBAAA,2BAAA6B,wFAAA3B,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAApC,KAAA,EAAAuB,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAApC,KAAA,GAAAuB,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAmC;IACvCjE,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEQ;IAAjFD,EAAA,CAAA+D,gBAAA,4BAAA8B,6FAAA5B,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAvC,UAAA,EAAA0B,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAvC,UAAA,GAAA0B,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAyC;IACzCjE,EAAA,CAAAgC,UAAA,KAAA8D,oEAAA,yBAAqE;IAEzE9F,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,sBAE/B;IAAvCD,EAAA,CAAA+D,gBAAA,4BAAAgC,6FAAA9B,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAlC,OAAA,EAAAqB,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAlC,OAAA,GAAAqB,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAsC;IACtCjE,EAAA,CAAAgC,UAAA,KAAAgE,oEAAA,yBAA6E;IAGjFhG,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,kBAEhC;IAAzCD,EAAA,CAAA+D,gBAAA,2BAAAkC,wFAAAhC,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAA/B,UAAA,EAAAkB,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAA/B,UAAA,GAAAkB,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAwC;IAC5CjE,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,kBAEhC;IAApCD,EAAA,CAAA+D,gBAAA,2BAAAmC,wFAAAjC,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAqB,KAAA,EAAAlC,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAqB,KAAA,GAAAlC,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAmC;IACvCjE,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA4E,wBACyB;IAA1DD,EAAA,CAAA+D,gBAAA,2BAAAqC,8FAAAnC,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAuB,OAAA,EAAApC,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAuB,OAAA,GAAApC,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAqC;IAC5EjE,EAAA,CAAAE,MAAA,gEACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA8E,wBAC6B;IAA5DD,EAAA,CAAA+D,gBAAA,2BAAAuC,8FAAArC,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAyB,SAAA,EAAAtC,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAyB,SAAA,GAAAtC,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAuC;IAClFjE,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEV;IAA/DD,EAAA,CAAA+D,gBAAA,2BAAAyC,2FAAAvC,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAA2B,OAAA,EAAAxC,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAA2B,OAAA,GAAAxC,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAqC;IAKjDjE,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,gBACiB,kBAC2B;IAApBD,EAAA,CAAAa,UAAA,mBAAA6F,iFAAA;MAAA,MAAAC,OAAA,GAAA3G,EAAA,CAAAe,aAAA,CAAA2E,IAAA,EAAAkB,SAAA;MAAA,MAAA3F,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAA4F,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAAC3G,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,mBAA0D;IAAtBD,EAAA,CAAAa,UAAA,mBAAAiG,iFAAA;MAAA,MAAAH,OAAA,GAAA3G,EAAA,CAAAe,aAAA,CAAA2E,IAAA,EAAAkB,SAAA;MAAA,OAAA5G,EAAA,CAAAqB,WAAA,CAASsF,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAAC/G,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IA1ECH,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+F,KAAA,aAAA/F,MAAA,CAAAgG,UAAA,OAAsC;IACtCjH,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+F,KAAA,cAAA/F,MAAA,CAAAgG,UAAA,OAAuC;IACvCjH,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+F,KAAA,aAAA/F,MAAA,CAAAgG,UAAA,OAAsC;IACtCjH,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+F,KAAA,cAAA/F,MAAA,CAAAgG,UAAA,OAAuC;IAM0CjH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAgG,UAAA,OAAsB;IAMxFjH,EAAA,CAAAO,SAAA,EAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAA1C,YAAA,CAA0C;IAE9BpC,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE3EJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAAzC,UAAA,CAAwC;IAE5BrC,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAApC,KAAA,CAAmC;IAEvB1C,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAA0E,gBAAA,aAAAzD,MAAA,CAAA6D,eAAA,CAAAvC,UAAA,CAAyC;IAAUvC,EAAA,CAAAI,UAAA,aAAAa,MAAA,CAAAgG,UAAA,OAA6B;IAC9CjH,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAiG,SAAA,CAAY;IAGlClH,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAA0E,gBAAA,aAAAzD,MAAA,CAAA6D,eAAA,CAAAlC,OAAA,CAAsC;IACF5C,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAkG,aAAA,CAAgB;IAIxCnH,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAA/B,UAAA,CAAwC;IAE5B/C,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAAqB,KAAA,CAAmC;IAEvBnG,EAAA,CAAAO,SAAA,EAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,oBAAoB;IAChCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAAuB,OAAA,CAAqC;IAIhErG,EAAA,CAAAO,SAAA,GAAgB;IAA0BP,EAA1C,CAAAI,UAAA,qCAAgB,yBAAyB,oBAAoB;IAC9BJ,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAAyB,SAAA,CAAuC;IAItEvG,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAA2B,OAAA,CAAqC;;;;;IAqB/CzG,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACpCH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAqB3BH,EAAA,CAAAC,cAAA,qBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAAgH,QAAA,CAAAzG,KAAA,CAAoB;IAAEX,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAA4G,QAAA,CAAAxG,KAAA,KAAc;;;;;IAMpFZ,EAAA,CAAAC,cAAA,qBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAAiH,UAAA,CAAA1G,KAAA,CAAsB;IAC1EX,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAA6G,UAAA,CAAAzG,KAAA,KAAgB;;;;;;IA9B9BZ,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAAgC,UAAA,IAAAsF,8DAAA,mBAA2B,IAAAC,8DAAA,mBACC;IAC9BvH,EAAA,CAAAG,YAAA,EAAiB;IAMPH,EALV,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX,yBACkE,gBAEnB;IAA1DD,EAAA,CAAA+D,gBAAA,2BAAAyD,uFAAAvD,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAA1C,YAAA,EAAA6B,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAA1C,YAAA,GAAA6B,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAA0C;IAC9CjE,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAgF,iBAEpB;IAAxDD,EAAA,CAAA+D,gBAAA,2BAAA2D,wFAAAzD,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAzC,UAAA,EAAA4B,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAzC,UAAA,GAAA4B,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAwC;IAC5CjE,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAA+D,gBAAA,2BAAA4D,wFAAA1D,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAApC,KAAA,EAAAuB,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAApC,KAAA,GAAAuB,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAmC;IACvCjE,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEQ;IAAjFD,EAAA,CAAA+D,gBAAA,4BAAA6D,6FAAA3D,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAvC,UAAA,EAAA0B,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAvC,UAAA,GAAA0B,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAyC;IACzCjE,EAAA,CAAAgC,UAAA,KAAA6F,oEAAA,yBAAqE;IAEzE7H,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,sBAE/B;IAAvCD,EAAA,CAAA+D,gBAAA,4BAAA+D,6FAAA7D,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAlC,OAAA,EAAAqB,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAlC,OAAA,GAAAqB,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAsC;IACtCjE,EAAA,CAAAgC,UAAA,KAAA+F,oEAAA,yBAA6E;IAGjF/H,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,kBAEhC;IAAzCD,EAAA,CAAA+D,gBAAA,2BAAAiE,wFAAA/D,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAA/B,UAAA,EAAAkB,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAA/B,UAAA,GAAAkB,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAwC;IAC5CjE,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,kBAEhC;IAApCD,EAAA,CAAA+D,gBAAA,2BAAAkE,wFAAAhE,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAqB,KAAA,EAAAlC,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAqB,KAAA,GAAAlC,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAmC;IACvCjE,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA4E,wBACyB;IAA1DD,EAAA,CAAA+D,gBAAA,2BAAAmE,8FAAAjE,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAuB,OAAA,EAAApC,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAuB,OAAA,GAAApC,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAqC;IAC5EjE,EAAA,CAAAE,MAAA,gEACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA8E,wBAC6B;IAA5DD,EAAA,CAAA+D,gBAAA,2BAAAoE,8FAAAlE,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAAyB,SAAA,EAAAtC,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAAyB,SAAA,GAAAtC,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAuC;IAClFjE,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEV;IAA/DD,EAAA,CAAA+D,gBAAA,2BAAAqE,2FAAAnE,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmE,kBAAA,CAAAlD,MAAA,CAAA6D,eAAA,CAAA2B,OAAA,EAAAxC,MAAA,MAAAhD,MAAA,CAAA6D,eAAA,CAAA2B,OAAA,GAAAxC,MAAA;MAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;IAAA,EAAqC;IAKjDjE,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,gBACiB,kBACmC;IAA5BD,EAAA,CAAAa,UAAA,mBAAAwH,iFAAA;MAAA,MAAAC,OAAA,GAAAtI,EAAA,CAAAe,aAAA,CAAA0G,IAAA,EAAAb,SAAA;MAAA,MAAA3F,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAsH,YAAA,CAAAD,OAAA,CAAiB;IAAA,EAAC;IAACtI,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5EH,EAAA,CAAAC,cAAA,mBAA0D;IAAtBD,EAAA,CAAAa,UAAA,mBAAA2H,iFAAA;MAAA,MAAAF,OAAA,GAAAtI,EAAA,CAAAe,aAAA,CAAA0G,IAAA,EAAAb,SAAA;MAAA,OAAA5G,EAAA,CAAAqB,WAAA,CAASiH,OAAA,CAAAvB,KAAA,EAAW;IAAA,EAAC;IAAC/G,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IAlECH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+F,KAAA,UAAkB;IAClBhH,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+F,KAAA,WAAmB;IAMJhH,EAAA,CAAAO,SAAA,GAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAA1C,YAAA,CAA0C;IAE9BpC,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE3EJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAAzC,UAAA,CAAwC;IAE5BrC,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAApC,KAAA,CAAmC;IAEvB1C,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAA0E,gBAAA,aAAAzD,MAAA,CAAA6D,eAAA,CAAAvC,UAAA,CAAyC;IAAUvC,EAAA,CAAAI,UAAA,aAAAa,MAAA,CAAAgG,UAAA,OAA6B;IAC9CjH,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAiG,SAAA,CAAY;IAGlClH,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAA0E,gBAAA,aAAAzD,MAAA,CAAA6D,eAAA,CAAAlC,OAAA,CAAsC;IACF5C,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAkG,aAAA,CAAgB;IAIxCnH,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAA/B,UAAA,CAAwC;IAE5B/C,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAAqB,KAAA,CAAmC;IAEvBnG,EAAA,CAAAO,SAAA,EAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,oBAAoB;IAChCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAAuB,OAAA,CAAqC;IAIhErG,EAAA,CAAAO,SAAA,GAAgB;IAA0BP,EAA1C,CAAAI,UAAA,qCAAgB,yBAAyB,oBAAoB;IAC9BJ,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAAyB,SAAA,CAAuC;IAItEvG,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA0E,gBAAA,YAAAzD,MAAA,CAAA6D,eAAA,CAAA2B,OAAA,CAAqC;;;;;;IAqBnDzG,EAAA,CAAAC,cAAA,+BAA0G;IAAtBD,EAA5C,CAAAa,UAAA,4BAAA4H,sGAAAxE,MAAA;MAAAjE,EAAA,CAAAe,aAAA,CAAA2H,IAAA;MAAA,MAAAzH,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAAkBJ,MAAA,CAAA0H,gBAAA,CAAA1E,MAAA,CAAwB;IAAA,EAAC,mBAAA2E,6FAAA;MAAA,MAAAC,OAAA,GAAA7I,EAAA,CAAAe,aAAA,CAAA2H,IAAA,EAAA9B,SAAA;MAAA,OAAA5G,EAAA,CAAAqB,WAAA,CAAUwH,OAAA,CAAA9B,KAAA,EAAW;IAAA,EAAC;IACzG/G,EAAA,CAAAG,YAAA,EAAsB;;;IADDH,EAAA,CAAAI,UAAA,mBAAkB;;;;;;IAMvCJ,EAAA,CAAAC,cAAA,gCACgE;IAAtBD,EAAxC,CAAAa,UAAA,6BAAAiI,wGAAA;MAAA9I,EAAA,CAAAe,aAAA,CAAAgI,IAAA;MAAA,MAAA9H,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAAmBJ,MAAA,CAAA+H,iBAAA,EAAmB;IAAA,EAAC,mBAAAC,8FAAA;MAAA,MAAAC,OAAA,GAAAlJ,EAAA,CAAAe,aAAA,CAAAgI,IAAA,EAAAnC,SAAA;MAAA,OAAA5G,EAAA,CAAAqB,WAAA,CAAU6H,OAAA,CAAAnC,KAAA,EAAW;IAAA,EAAC;IAC/D/G,EAAA,CAAAG,YAAA,EAAuB;;;;IAFiDH,EAAlD,CAAAI,UAAA,kBAAAa,MAAA,CAAAkI,+BAAA,CAAiD,mBAAmB;;;AD7W5F,OAAM,MAAOC,8BAA+B,SAAQnK,aAAa;EAC/DoK,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAMpB;IACA,KAAAC,yBAAyB,GAAG,EAA0F;IACtH,KAAAC,qBAAqB,GAA8B,EAAE;IACrD;IACA,KAAAhF,aAAa,GAA8B,EAAE;IAC7C,KAAAiF,eAAe,GAA4B,EAAE;IAC7C,KAAApF,eAAe,GAAqE;MAAEvC,UAAU,EAAE;IAAE,CAAE;IAEtG,KAAA4E,aAAa,GAAG,CACd;MAAExG,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAsG,SAAS,GAAG,IAAI,CAACqC,UAAU,CAACY,cAAc,CAACvK,aAAa,CAAC;IACzD,KAAAoH,KAAK,GAAG,KAAK;IACb,KAAAoD,gBAAgB,GAAG,CAAC;IACpB,KAAAnD,UAAU,GAAG,CAAC,CAAC,CAAC;IAChB,KAAAoD,kBAAkB,GAAG,KAAK,CAAC,CAAC;IAC5B,KAAAlB,+BAA+B,GAA4B,EAAE,CAAC,CAAC;IAI/D,KAAAmB,yBAAyB,GAAG,KAAK;IA6PjC;IACQ,KAAAC,gBAAgB,GAAG,IAAI;IAtR7B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EA0BSC,QAAQA,CAAA,GAAW;EAC5B;EACAF,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACR,yBAAyB,CAACpH,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACoH,yBAAyB,CAAC3D,OAAO,GAAG,IAAI;IAC7C,IAAI,CAAC2D,yBAAyB,CAACzD,SAAS,GAAG,IAAI;IAC/C,IAAI,CAACyD,yBAAyB,CAAC5H,YAAY,GAAG,EAAE;IAChD,IAAI,CAAC4H,yBAAyB,CAAC3H,UAAU,GAAG,EAAE;IAC9C;IACA,IAAI,CAAC2H,yBAAyB,CAACzH,UAAU,GAAG,IAAI,CAAC2E,SAAS,CAACyD,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACjK,KAAK,CAAC;EACpF;EAEA;EACAkK,WAAWA,CAAA;IACT,IAAI,CAACL,oBAAoB,EAAE;IAC3B;IACA,IAAI,IAAI,CAACvF,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC9B,MAAM,GAAG,CAAC,EAAE;MACvD2H,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAAC7D,UAAU,KAAK,CAAC,EAAE;UACzB,IAAI,CAAC+C,yBAAyB,CAACjF,YAAY,GAAG,IAAI,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC3E,GAAG;QACzE,CAAC,MAAM;UACL,IAAI,CAAC0J,yBAAyB,CAACjF,YAAY,GAAG,CAAC;QACjD;QACA,IAAI,CAACgG,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEAzI,YAAYA,CAAC0I,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAACpE,SAAS,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7K,KAAK,IAAI0K,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC1K,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOuK,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAEC,UAAUA,CAAA;IACV,IAAI,CAACjC,KAAK,CAACkC,KAAK,EAAE;IAElB;IACA,IAAI,IAAI,CAAC3E,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,CAACyC,KAAK,CAACmC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC/G,eAAe,CAACC,YAAY,CAAC;IAClE;IAEA,IAAI,CAAC2E,KAAK,CAACmC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/G,eAAe,CAAC1C,YAAY,CAAC;IAC9D,IAAI,CAACsH,KAAK,CAACmC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC/G,eAAe,CAACvC,UAAU,CAAC;IAC7D,IAAI,CAACmH,KAAK,CAACmC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/G,eAAe,CAACpC,KAAK,CAAC;IACvD,IAAI,CAACgH,KAAK,CAACmC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/G,eAAe,CAAClC,OAAO,CAAC;IACzD,IAAI,CAAC8G,KAAK,CAACmC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/G,eAAe,CAAC/B,UAAU,CAAC;IAC5D,IAAI,CAAC2G,KAAK,CAACmC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/G,eAAe,CAACqB,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACrB,eAAe,CAACzC,UAAU,IAAI,IAAI,CAACyC,eAAe,CAACzC,UAAU,CAACc,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACuG,KAAK,CAACoC,aAAa,CAACL,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAC3G,eAAe,CAAC2B,OAAO,IAAI,IAAI,CAAC3B,eAAe,CAAC2B,OAAO,CAACtD,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACuG,KAAK,CAACoC,aAAa,CAACL,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEAnK,GAAGA,CAACyK,MAAwB;IAC1B,IAAI,CAAC/E,KAAK,GAAG,IAAI;IACjB,IAAI,CAAClC,eAAe,GAAG;MAAEvC,UAAU,EAAE,EAAE;MAAE8D,OAAO,EAAE,KAAK;MAAEE,SAAS,EAAE;IAAK,CAAE;IAC3E,IAAI,CAACzB,eAAe,CAAClC,OAAO,GAAG,CAAC;IAChC,IAAI,CAACkC,eAAe,CAAC/B,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAACkE,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,IAAI,CAACmD,gBAAgB,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACtF,eAAe,CAACC,YAAY,GAAG,IAAI,CAACqF,gBAAgB;MAC3D,CAAC,MAAM,IAAI,IAAI,CAACnF,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC9B,MAAM,GAAG,CAAC,EAAE;QAC9D,IAAI,CAAC2B,eAAe,CAACC,YAAY,GAAG,IAAI,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC3E,GAAG;MAC/D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACwE,eAAe,CAACC,YAAY,GAAG,CAAC;MACrC,IAAI,CAACD,eAAe,CAACvC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,CAACiH,aAAa,CAACwC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEMnK,MAAMA,CAACqK,IAA2B,EAAEF,MAAwB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MAChED,KAAI,CAACjC,qBAAqB,CAACmC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAAClF,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMkF,KAAI,CAACG,OAAO,EAAE;QACpB;QACA,IAAIH,KAAI,CAACjF,UAAU,KAAK,CAAC,EAAE;UACzBiF,KAAI,CAACpH,eAAe,CAACvC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1C;QACA2J,KAAI,CAAC1C,aAAa,CAACwC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAzF,IAAIA,CAAC4F,GAAQ;IACX,IAAI,CAACd,UAAU,EAAE;IACjB,IAAI,IAAI,CAACjC,KAAK,CAACoC,aAAa,CAAC3I,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACsG,OAAO,CAACiD,aAAa,CAAC,IAAI,CAAChD,KAAK,CAACoC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAI,IAAI,CAAC7E,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACnC,eAAe,CAACC,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAAC6E,kBAAkB,CAAC+C,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAAC9H;KACZ,CAAC,CAAC+H,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtD,OAAO,CAACuD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACjC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACtB,OAAO,CAACwD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAAC1F,KAAK,EAAE;EACb;EAEAhF,QAAQA,CAACkK,IAA2B;IAClC,IAAI,CAACnH,eAAe,CAACsH,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAACpF,KAAK,GAAG,KAAK;IAClB,IAAImG,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACzD,kBAAkB,CAAC0D,iCAAiC,CAAC;MACxDV,IAAI,EAAE;QACJR,cAAc,EAAE,IAAI,CAACtH,eAAe,CAACsH;;KAExC,CAAC,CAACS,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACrD,OAAO,CAACuD,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACjC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAN,gBAAgBA,CAAA;IACd,IAAI,CAACd,gBAAgB,CAAC4D,qCAAqC,CAAC;MAAEX,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEY,IAAI,CAACtO,kBAAkB,CAAC,IAAI,CAAC6K,UAAU,CAAC,CAAC,CAAC8C,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAC7H,aAAa,GAAG6H,GAAG,CAACW,OAAQ;MACjC;MACA,IAAI,IAAI,CAACxG,UAAU,KAAK,CAAC,IAAI,IAAI,CAAChC,aAAa,CAAC9B,MAAM,GAAG,CAAC,EAAE;QAC1D,IAAI,CAAC6G,yBAAyB,CAACjF,YAAY,GAAG,IAAI,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC3E,GAAG;QACvE,IAAI,CAACyK,OAAO,EAAE;MAChB,CAAC,MAAM,IAAI,IAAI,CAAC9D,UAAU,KAAK,CAAC,EAAE;QAChC,IAAI,CAAC+C,yBAAyB,CAACjF,YAAY,GAAG,CAAC;QAC/C,IAAI,CAACgG,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAACf,yBAAyB,CAAC0D,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAAC3D,yBAAyB,CAAC4D,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAAC3D,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAAC4D,YAAY,GAAG,CAAC;IACrB;IACA,IAAI,IAAI,CAAC7G,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC+C,yBAAyB,CAACjF,YAAY,GAAG,CAAC;MAC/C,IAAI,CAACiF,yBAAyB,CAACzH,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACpD,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACyH,yBAAyB,CAACjF,YAAY,IAAI,IAAI,CAACiF,yBAAyB,CAACjF,YAAY,IAAI,CAAC,EAAE;QACnG,IAAI,CAACqF,gBAAgB,GAAG,IAAI,CAACJ,yBAAyB,CAACjF,YAAY;MACrE;IACF;IAEA,IAAI,CAAC6E,kBAAkB,CAACmE,8BAA8B,CAAC;MAAEnB,IAAI,EAAE,IAAI,CAAC5C;IAAyB,CAAE,CAAC,CAC7FwD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf;UACA,IAAI,CAACvD,eAAe,GAAG4C,GAAG,CAACW,OAAO,CAAC9C,GAAG,CAACqD,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACP5J,QAAQ,EAAE;WACX,CAAC,CAAC;UACH,IAAI,CAAC0J,YAAY,GAAGhB,GAAG,CAACmB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAAE5B,OAAOA,CAAA;IACP,IAAI,CAACzC,kBAAkB,CAACsE,8BAA8B,CAAC;MAAEtB,IAAI,EAAE,IAAI,CAAC3C;IAAqB,CAAE,CAAC,CACzFuD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAAC3I,eAAe,GAAG;YAAEvC,UAAU,EAAE,EAAE;YAAE8D,OAAO,EAAE,KAAK;YAAEE,SAAS,EAAE;UAAK,CAAE;UAC3E,IAAI,CAACzB,eAAe,CAACC,YAAY,GAAG+H,GAAG,CAACW,OAAO,CAAC1I,YAAY;UAC5D,IAAI,CAACD,eAAe,CAACzC,UAAU,GAAGyK,GAAG,CAACW,OAAO,CAACpL,UAAU;UACxD;UACA,IAAI,CAACyC,eAAe,CAACvC,UAAU,GAAG,IAAI,CAAC0E,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAI6F,GAAG,CAACW,OAAO,CAAClL,UAAU,GAAI0I,KAAK,CAACC,OAAO,CAAC4B,GAAG,CAACW,OAAO,CAAClL,UAAU,CAAC,GAAGuK,GAAG,CAACW,OAAO,CAAClL,UAAU,GAAG,CAACuK,GAAG,CAACW,OAAO,CAAClL,UAAU,CAAC,GAAI,EAAG;UAC9L,IAAI,CAACuC,eAAe,CAAC2B,OAAO,GAAGqG,GAAG,CAACW,OAAO,CAAChH,OAAO;UAClD,IAAI,CAAC3B,eAAe,CAAC1C,YAAY,GAAG0K,GAAG,CAACW,OAAO,CAACrL,YAAY;UAC5D,IAAI,CAAC0C,eAAe,CAACsH,cAAc,GAAGU,GAAG,CAACW,OAAO,CAACrB,cAAc;UAChE,IAAI,CAACtH,eAAe,CAACpC,KAAK,GAAGoK,GAAG,CAACW,OAAO,CAAC/K,KAAK;UAC9C,IAAI,CAACoC,eAAe,CAAClC,OAAO,GAAGkK,GAAG,CAACW,OAAO,CAAC7K,OAAO;UAClD,IAAI,CAACkC,eAAe,CAAC/B,UAAU,GAAG+J,GAAG,CAACW,OAAO,CAAC1K,UAAU,IAAI,CAAC;UAC7D,IAAI,CAAC+B,eAAe,CAACqB,KAAK,GAAG2G,GAAG,CAACW,OAAO,CAACtH,KAAK;UAC9C,IAAI,CAACrB,eAAe,CAACuB,OAAO,GAAGyG,GAAG,CAACW,OAAO,CAACpH,OAAO,IAAI,KAAK;UAC3D,IAAI,CAACvB,eAAe,CAACyB,SAAS,GAAGuG,GAAG,CAACW,OAAO,CAAClH,SAAS,IAAI,KAAK;QACjE;MACF;IACF,CAAC,CAAC;EACN;EAEA4H,iBAAiBA,CAACxN,KAAa,EAAEyN,OAAY;IAC3C7B,OAAO,CAACC,GAAG,CAAC4B,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAACtJ,eAAe,CAACvC,UAAU,EAAE8L,QAAQ,CAAC1N,KAAK,CAAC,EAAE;QACrD,IAAI,CAACmE,eAAe,CAACvC,UAAU,EAAEkJ,IAAI,CAAC9K,KAAK,CAAC;MAC9C;MACA4L,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC1H,eAAe,CAACvC,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACuC,eAAe,CAACvC,UAAU,GAAG,IAAI,CAACuC,eAAe,CAACvC,UAAU,EAAE+L,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK5N,KAAK,CAAC;IAC7F;EACF;EAEAkC,cAAcA,CAACoJ,IAAS;IACtB,OAAOA,IAAI,CAAC5F,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAEAvD,gBAAgBA,CAACmJ,IAAS;IACxB,OAAOA,IAAI,CAAC1F,SAAS,GAAG,GAAG,GAAG,GAAG;EACnC;EAIAiI,WAAWA,CAACC,KAAU;IACpB;IACA,IAAI,IAAI,CAAClE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B;IACF;IACA;IACA,IAAIkE,KAAK,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACzH,UAAU,GAAG,CAAC;MACnB,IAAI,CAAC+C,yBAAyB,CAACjF,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,IAAI,CAACkC,UAAU,GAAG,CAAC;MACnB;MACA,IAAI,IAAI,CAAChC,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC9B,MAAM,GAAG,CAAC,EAAE;QACvD,IAAI,CAAC6G,yBAAyB,CAACjF,YAAY,GAAG,IAAI,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC3E,GAAG;MACzE;IACF;IACA,IAAI,CAACyK,OAAO,EAAE;EAChB;EAEA;EACA4D,WAAWA,CAAC5C,MAAwB;IAClC,IAAI,CAAC/E,KAAK,GAAG,IAAI;IACjB,IAAI,CAAClC,eAAe,GAAG;MAAEvC,UAAU,EAAE,EAAE;MAAE8D,OAAO,EAAE,KAAK;MAAEE,SAAS,EAAE;IAAK,CAAE;IAC3E,IAAI,CAACzB,eAAe,CAAClC,OAAO,GAAG,CAAC;IAChC,IAAI,CAACkC,eAAe,CAAC/B,UAAU,GAAG,CAAC;IACnC;IACA,IAAI,CAAC+B,eAAe,CAACC,YAAY,GAAG,CAAC;IACrC,IAAI,CAACD,eAAe,CAACvC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACxC,IAAI,CAACiH,aAAa,CAACwC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACM6C,cAAcA,CAAC3C,IAA2B,EAAEF,MAAwB;IAAA,IAAA8C,MAAA;IAAA,OAAA1C,iBAAA;MACxE0C,MAAI,CAAC5E,qBAAqB,CAACmC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEyC,MAAI,CAAC7H,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM6H,MAAI,CAACxC,OAAO,EAAE;QACpB;QACAwC,MAAI,CAAC/J,eAAe,CAACvC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACxCsM,MAAI,CAACrF,aAAa,CAACwC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA;EACA/D,YAAYA,CAACkE,GAAQ;IACnB;IACA,IAAI,CAAC/C,KAAK,CAACkC,KAAK,EAAE;IAClB,IAAI,CAAClC,KAAK,CAACmC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/G,eAAe,CAAC1C,YAAY,CAAC;IAC9D;IACA,IAAI,IAAI,CAAC6E,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACnC,eAAe,CAACvC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,CAACmH,KAAK,CAACmC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC/G,eAAe,CAACvC,UAAU,CAAC;IAC7D,IAAI,CAACmH,KAAK,CAACmC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/G,eAAe,CAACpC,KAAK,CAAC;IACvD,IAAI,CAACgH,KAAK,CAACmC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/G,eAAe,CAAClC,OAAO,CAAC;IACzD,IAAI,CAAC8G,KAAK,CAACmC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/G,eAAe,CAAC/B,UAAU,CAAC;IAC5D,IAAI,CAAC2G,KAAK,CAACmC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/G,eAAe,CAACqB,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACrB,eAAe,CAACzC,UAAU,IAAI,IAAI,CAACyC,eAAe,CAACzC,UAAU,CAACc,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACuG,KAAK,CAACoC,aAAa,CAACL,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAC3G,eAAe,CAAC2B,OAAO,IAAI,IAAI,CAAC3B,eAAe,CAAC2B,OAAO,CAACtD,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACuG,KAAK,CAACoC,aAAa,CAACL,IAAI,CAAC,kBAAkB,CAAC;IACnD;IAEA,IAAI,IAAI,CAAC/B,KAAK,CAACoC,aAAa,CAAC3I,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACsG,OAAO,CAACiD,aAAa,CAAC,IAAI,CAAChD,KAAK,CAACoC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMgD,YAAY,GAAG;MAAE,GAAG,IAAI,CAAChK;IAAe,CAAE;IAChDgK,YAAY,CAAC/J,YAAY,GAAG,CAAC;IAC7B;IACA,IAAI,IAAI,CAACkC,UAAU,KAAK,CAAC,EAAE;MACzB6H,YAAY,CAACvM,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClC;IAEA,IAAI,CAACqH,kBAAkB,CAAC+C,+BAA+B,CAAC;MACtDC,IAAI,EAAEkC;KACP,CAAC,CAACjC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtD,OAAO,CAACuD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACjC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACtB,OAAO,CAACwD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAAC1F,KAAK,EAAE;EACb;EAEAgI,kBAAkBA,CAACC,oBAAsC;IACvD,IAAI,CAACxF,aAAa,CAACwC,IAAI,CAACgD,oBAAoB,CAAC;EAC/C;EAEArG,gBAAgBA,CAACsG,GAAa;IAC5B;EAAA;EAGF;EACA/L,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACgH,eAAe,CAACoE,MAAM,CAACY,GAAG,IAAIA,GAAG,CAAC9K,QAAQ,CAAC;EACzD;EAEA;EACAE,4BAA4BA,CAAA;IAC1B;EAAA;EAGF;EACAhB,qBAAqBA,CAAA;IACnB,IAAI,CAAC4G,eAAe,CAACkB,OAAO,CAAC8D,GAAG,IAAIA,GAAG,CAAC9K,QAAQ,GAAG,IAAI,CAAC;EAC1D;EAEA;EACAX,kBAAkBA,CAAA;IAChB,IAAI,CAACyG,eAAe,CAACkB,OAAO,CAAC8D,GAAG,IAAIA,GAAG,CAAC9K,QAAQ,GAAG,KAAK,CAAC;EAC3D;EAEA;EACA+K,aAAaA,CAAA;IACX,OAAO,IAAI,CAACjF,eAAe,CAAC/G,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC+G,eAAe,CAACkF,KAAK,CAACF,GAAG,IAAIA,GAAG,CAAC9K,QAAQ,CAAC;EAC3F;EAEA;EACAiL,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG,IAAI,CAACpF,eAAe,CAACoE,MAAM,CAACY,GAAG,IAAIA,GAAG,CAAC9K,QAAQ,CAAC,CAACjB,MAAM;IAC7E,OAAOmM,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,IAAI,CAACpF,eAAe,CAAC/G,MAAM;EACzE;EAEA;EACAoM,eAAeA,CAACd,KAAU;IACxB,MAAMe,SAAS,GAAGf,KAAK,CAACgB,MAAM,CAACrB,OAAO;IACtC,IAAI,CAAClE,eAAe,CAACkB,OAAO,CAAC8D,GAAG,IAAIA,GAAG,CAAC9K,QAAQ,GAAGoL,SAAS,CAAC;EAC/D;EAEA;EACAE,mBAAmBA,CAACC,qBAAuC;IACzD,MAAMC,oBAAoB,GAAG,IAAI,CAAC1M,uBAAuB,EAAE;IAC3D,IAAI0M,oBAAoB,CAACzM,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACsG,OAAO,CAACwD,YAAY,CAAC,cAAc,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAAC9D,+BAA+B,GAAG,CAAC,GAAGyG,oBAAoB,CAAC;IAChE,IAAI,CAACvF,kBAAkB,GAAG,IAAI;IAC9B,MAAMzD,SAAS,GAAG,IAAI,CAAC4C,aAAa,CAACwC,IAAI,CAAC2D,qBAAqB,CAAC;IAEhE;IACA/I,SAAS,CAACiJ,OAAO,CAAChD,SAAS,CAAC,MAAK;MAC/B,IAAI,CAACxC,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAAClB,+BAA+B,GAAG,EAAE;IAC3C,CAAC,CAAC;EACJ;EAEA;EACAH,iBAAiBA,CAAA;IACf,IAAI,CAACS,OAAO,CAACuD,aAAa,CAAC,QAAQ,CAAC;IACpC;IACA,IAAI,CAACvJ,kBAAkB,EAAE;EAC3B;EAEA;EACAqM,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAAC9F,yBAAyB,CAACjF,YAAY,EAAE;MAChD,IAAI,CAAC0E,OAAO,CAACwD,YAAY,CAAC,QAAQ,CAAC;MACnC;IACF;IACA,IAAI,CAAC3C,yBAAyB,GAAG,IAAI;IAErC;IACAQ,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACiF,8BAA8B,EAAE;QACvC,IAAI,CAACA,8BAA8B,CAAC/D,IAAI,EAAE;MAC5C;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAgE,sBAAsBA,CAACC,MAA2B;IAChD1D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEyD,MAAM,CAAC;IAEhC;IACA;IACA,MAAMC,eAAe,GAAGD,MAAM,CAACE,aAAa,CAACxF,GAAG,CAACqD,IAAI,KAAK;MACxDjJ,YAAY,EAAE,IAAI,CAACiF,yBAAyB,CAACjF,YAAY;MACzD3C,YAAY,EAAE,GAAG6N,MAAM,CAACG,SAAS,IAAIpC,IAAI,CAACqC,aAAa,EAAE;MACzDhO,UAAU,EAAE4N,MAAM,CAACG,SAAS;MAC5BrN,UAAU,EAAE,CAAC;MAAE;MACfoD,KAAK,EAAE,GAAG;MAAE;MACZvD,OAAO,EAAE,CAAC;MACVyD,OAAO,EAAE,IAAI;MACbE,SAAS,EAAE,KAAK;MAChBhE,UAAU,EAAE,IAAI,CAAC2E,SAAS,CAACyD,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACjK,KAAK,CAAC;MAAE;MACpD8F,OAAO,EAAE,mBAAmBuH,IAAI,CAACsC,WAAW;KAC7C,CAAC,CAAC;IAEH;IACA;IAEA,IAAI,CAAC7G,OAAO,CAACuD,aAAa,CAAC,QAAQiD,MAAM,CAACE,aAAa,CAAChN,MAAM,KAAK8M,MAAM,CAACG,SAAS,MAAM,CAAC;IAC1F,IAAI,CAACrF,OAAO,EAAE,CAAC,CAAC;EAClB;EAEAwF,qBAAqBA,CAAA;IACnB,IAAI,CAACjG,yBAAyB,GAAG,KAAK;EACxC;EAEA;EACQkG,uBAAuBA,CAACC,YAAmB;IACjD;IACA;EAAA;;;uCA9fSrH,8BAA8B,EAAApJ,EAAA,CAAA0Q,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5Q,EAAA,CAAA0Q,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA9Q,EAAA,CAAA0Q,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAhR,EAAA,CAAA0Q,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAlR,EAAA,CAAA0Q,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAApR,EAAA,CAAA0Q,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAAtR,EAAA,CAAA0Q,iBAAA,CAAAW,EAAA,CAAAE,kBAAA,GAAAvR,EAAA,CAAA0Q,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAAzR,EAAA,CAAA0Q,iBAAA,CAAAgB,EAAA,CAAAC,MAAA,GAAA3R,EAAA,CAAA0Q,iBAAA,CAAA1Q,EAAA,CAAA4R,UAAA;IAAA;EAAA;;;YAA9BxI,8BAA8B;MAAAyI,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UCpDzChS,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAuB,SAAA,qBAAiC;UACnCvB,EAAA,CAAAG,YAAA,EAAiB;UAOTH,EAJR,CAAAC,cAAA,sBAAoC,aACd,aACD,aACyB,gBACI;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,oBAA8E;UAAnED,EAAA,CAAA+D,gBAAA,2BAAAmO,2EAAAjO,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAmE,kBAAA,CAAA8N,GAAA,CAAAjI,yBAAA,CAAAjF,YAAA,EAAAd,MAAA,MAAAgO,GAAA,CAAAjI,yBAAA,CAAAjF,YAAA,GAAAd,MAAA;YAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;UAAA,EAAoD;UAC7DjE,EAAA,CAAAgC,UAAA,KAAAoQ,oDAAA,wBAAiE;UAIrEpS,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACM;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,iBACuD;UAArDD,EAAA,CAAA+D,gBAAA,2BAAAsO,wEAAApO,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAmE,kBAAA,CAAA8N,GAAA,CAAAjI,yBAAA,CAAA5H,YAAA,EAAA6B,MAAA,MAAAgO,GAAA,CAAAjI,yBAAA,CAAA5H,YAAA,GAAA6B,MAAA;YAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;UAAA,EAAoD;UACxDjE,EAFE,CAAAG,YAAA,EACuD,EACnD;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACI;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,iBACqD;UAAnDD,EAAA,CAAA+D,gBAAA,2BAAAuO,wEAAArO,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAmE,kBAAA,CAAA8N,GAAA,CAAAjI,yBAAA,CAAA3H,UAAA,EAAA4B,MAAA,MAAAgO,GAAA,CAAAjI,yBAAA,CAAA3H,UAAA,GAAA4B,MAAA;YAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;UAAA,EAAkD;UAExDjE,EAHI,CAAAG,YAAA,EACqD,EACjD,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBAAqF;UAA1ED,EAAA,CAAA+D,gBAAA,2BAAAwO,4EAAAtO,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAmE,kBAAA,CAAA8N,GAAA,CAAAjI,yBAAA,CAAAzH,UAAA,EAAA0B,MAAA,MAAAgO,GAAA,CAAAjI,yBAAA,CAAAzH,UAAA,GAAA0B,MAAA;YAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;UAAA,EAAkD;UAC3DjE,EAAA,CAAAgC,UAAA,KAAAwQ,oDAAA,wBAA+D;UAInExS,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,qBAAyE;UAA9DD,EAAA,CAAA+D,gBAAA,2BAAA0O,4EAAAxO,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAmE,kBAAA,CAAA8N,GAAA,CAAAjI,yBAAA,CAAApH,OAAA,EAAAqB,MAAA,MAAAgO,GAAA,CAAAjI,yBAAA,CAAApH,OAAA,GAAAqB,MAAA;YAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;UAAA,EAA+C;UACxDjE,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAE7BF,EAF6B,CAAAG,YAAA,EAAY,EAC3B,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,qBAA0F;UAA/ED,EAAA,CAAA+D,gBAAA,2BAAA2O,4EAAAzO,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAmE,kBAAA,CAAA8N,GAAA,CAAAjI,yBAAA,CAAA3D,OAAA,EAAApC,MAAA,MAAAgO,GAAA,CAAAjI,yBAAA,CAAA3D,OAAA,GAAApC,MAAA;YAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;UAAA,EAA+C;UACxDjE,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAGlCF,EAHkC,CAAAG,YAAA,EAAY,EAC9B,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACG;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,qBAA4F;UAAjFD,EAAA,CAAA+D,gBAAA,2BAAA4O,4EAAA1O,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAmE,kBAAA,CAAA8N,GAAA,CAAAjI,yBAAA,CAAAzD,SAAA,EAAAtC,MAAA,MAAAgO,GAAA,CAAAjI,yBAAA,CAAAzD,SAAA,GAAAtC,MAAA;YAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;UAAA,EAAiD;UAC1DjE,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAEhCF,EAFgC,CAAAG,YAAA,EAAY,EAC9B,EACR;UACNH,EAAA,CAAAuB,SAAA,cAA8C;UAChDvB,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACfD,EAAA,CAAAuB,SAAA,eAA4B;UAE1BvB,EADF,CAAAC,cAAA,eAAmD,kBACc;UAAxBD,EAAA,CAAAa,UAAA,mBAAA+R,iEAAA;YAAA5S,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAS4Q,GAAA,CAAApH,WAAA,EAAa;UAAA,EAAC;UAAC7K,EAAA,CAAAuB,SAAA,aAAgC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1GH,EAAA,CAAAC,cAAA,kBAAsD;UAApBD,EAAA,CAAAa,UAAA,mBAAAgS,iEAAA;YAAA7S,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAS4Q,GAAA,CAAAlH,OAAA,EAAS;UAAA,EAAC;UAAC/K,EAAA,CAAAuB,SAAA,aAAkC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnGH,EAAA,CAAAC,cAAA,kBAA2E;UAAtCD,EAAA,CAAAa,UAAA,mBAAAiS,iEAAA;YAAA9S,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAS4Q,GAAA,CAAAnC,yBAAA,EAA2B;UAAA,EAAC;UAAC9P,EAAA,CAAAuB,SAAA,aACnC;UAAAvB,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrDH,EAAA,CAAAgC,UAAA,KAAA+Q,iDAAA,qBAA4E;UAKpF/S,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;UAIbH,EADF,CAAAC,cAAA,uBAAoC,qBACW;UAAlCD,EAAA,CAAAa,UAAA,uBAAAmS,wEAAA/O,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAa4Q,GAAA,CAAAzD,WAAA,CAAAvK,MAAA,CAAmB;UAAA,EAAC;UAS5BjE,EARd,CAAAC,cAAA,kBAAuB,eACH,eAES,eACO,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAgC,UAAA,MAAAiR,8CAAA,mBAAuE;UAmB7EjT,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,2BAC2B;UADqBD,EAAA,CAAA+D,gBAAA,wBAAAmP,+EAAAjP,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAmE,kBAAA,CAAA8N,GAAA,CAAApE,SAAA,EAAA5J,MAAA,MAAAgO,GAAA,CAAApE,SAAA,GAAA5J,MAAA;YAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;UAAA,EAAoB;UAClEjE,EAAA,CAAAa,UAAA,wBAAAqS,+EAAA;YAAAlT,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAc4Q,GAAA,CAAAlH,OAAA,EAAS;UAAA,EAAC;UAIhC/K,EAHM,CAAAG,YAAA,EAAiB,EACb,EACF,EACC;UAODH,EALR,CAAAC,cAAA,mBAAuB,gBACH,gBAES,gBACY,gBACW;UAC1CD,EAAA,CAAAuB,SAAA,cAAoD;UACpDvB,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,6CAAM;UACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;UACNH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAE,MAAA,oaAEF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEFH,EADF,CAAAC,cAAA,gBAAgC,iBACO;UACnCD,EAAA,CAAAuB,SAAA,cAAgC;UAAAvB,EAAA,CAAAE,MAAA,8CAClC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,iBAAqC;UACnCD,EAAA,CAAAuB,SAAA,cAAuC;UAAAvB,EAAA,CAAAE,MAAA,kCACzC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,iBAAgC;UAC9BD,EAAA,CAAAuB,SAAA,cAAqC;UAAAvB,EAAA,CAAAE,MAAA,wCACvC;UAGNF,EAHM,CAAAG,YAAA,EAAO,EACH,EACF,EACF;UAQIH,EALV,CAAAC,cAAA,gBAAyB,gBACiB,gBACyB,gBACtB,gBACK,mBAEc;UADjBD,EAAA,CAAAa,UAAA,mBAAAsS,kEAAA;YAAAnT,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,MAAAiB,yBAAA,GAAApT,EAAA,CAAAoB,WAAA;YAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAS4Q,GAAA,CAAAvC,mBAAA,CAAA0D,yBAAA,CAA0C;UAAA,EAAC;UAEvFpT,EAAA,CAAAuB,SAAA,cAAgC;UAAAvB,EAAA,CAAAE,MAAA,kCAChC;UAAAF,EAAA,CAAAgC,UAAA,MAAAqR,gDAAA,mBAAkF;UAGpFrT,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAAmF;UAAnDD,EAAA,CAAAa,UAAA,mBAAAyS,kEAAA;YAAAtT,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,MAAAoB,wBAAA,GAAAvT,EAAA,CAAAoB,WAAA;YAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAS4Q,GAAA,CAAAlD,kBAAA,CAAAwE,wBAAA,CAAwC;UAAA,EAAC;UAChFvT,EAAA,CAAAuB,SAAA,cAA+B;UAAAvB,EAAA,CAAAE,MAAA,kCACjC;UAEFF,EAFE,CAAAG,YAAA,EAAS,EAEL;UACNH,EAAA,CAAAC,cAAA,gBAAkC;UAIhCD,EAHA,CAAAgC,UAAA,MAAAwR,iDAAA,oBAAyE,MAAAC,iDAAA,oBAGA;UAI7EzT,EADE,CAAAG,YAAA,EAAM,EACF;UACNH,EAAA,CAAAC,cAAA,gBAAuC;UAKrCD,EAJA,CAAAgC,UAAA,MAAA0R,kDAAA,qBACqC,MAAAC,kDAAA,qBAIU;UAKrD3T,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UASMH,EANZ,CAAAC,cAAA,gBAAyB,gBACO,kBACmE,cACtF,eAC4D,eACjC,kBAE6C;UAAnCD,EAAA,CAAAa,UAAA,oBAAA+S,kEAAA3P,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAU4Q,GAAA,CAAA1C,eAAA,CAAAtL,MAAA,CAAuB;UAAA,EAAC;UAC1EjE,EAFE,CAAAG,YAAA,EACyE,EACtE;UACLH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAgC,UAAA,MAAA6R,8CAAA,mBACuC;UAsB7C7T,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,2BAC2B;UADqBD,EAAA,CAAA+D,gBAAA,wBAAA+P,+EAAA7P,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAAnS,EAAA,CAAAmE,kBAAA,CAAA8N,GAAA,CAAApE,SAAA,EAAA5J,MAAA,MAAAgO,GAAA,CAAApE,SAAA,GAAA5J,MAAA;YAAA,OAAAjE,EAAA,CAAAqB,WAAA,CAAA4C,MAAA;UAAA,EAAoB;UAClEjE,EAAA,CAAAa,UAAA,wBAAAiT,+EAAA;YAAA9T,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAc4Q,GAAA,CAAAlH,OAAA,EAAS;UAAA,EAAC;UAQxC/K,EAPc,CAAAG,YAAA,EAAiB,EACb,EACF,EACF,EACC,EACC,EACC,EACP;UAqKVH,EAlKA,CAAAgC,UAAA,MAAA+R,uDAAA,kCAAA/T,EAAA,CAAAgU,sBAAA,CAAkD,MAAAC,uDAAA,kCAAAjU,EAAA,CAAAgU,sBAAA,CAiFQ,MAAAE,uDAAA,gCAAAlU,EAAA,CAAAgU,sBAAA,CA2EM,MAAAG,uDAAA,gCAAAnU,EAAA,CAAAgU,sBAAA,CAMC;UAOjEhU,EAAA,CAAAC,cAAA,2CAEwF;UAAnCD,EAAnD,CAAAa,UAAA,6BAAAuT,iGAAAnQ,MAAA;YAAAjE,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAmB4Q,GAAA,CAAAjC,sBAAA,CAAA/L,MAAA,CAA8B;UAAA,EAAC,oBAAAoQ,wFAAA;YAAArU,EAAA,CAAAe,aAAA,CAAAoR,GAAA;YAAA,OAAAnS,EAAA,CAAAqB,WAAA,CAAW4Q,GAAA,CAAA1B,qBAAA,EAAuB;UAAA,EAAC;UACvFvQ,EAAA,CAAAG,YAAA,EAA8B;;;UAhaTH,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAA0E,gBAAA,YAAAuN,GAAA,CAAAjI,yBAAA,CAAAjF,YAAA,CAAoD;UACjC/E,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAA6R,GAAA,CAAAhN,aAAA,CAAgB;UAQ5CjF,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAA0E,gBAAA,YAAAuN,GAAA,CAAAjI,yBAAA,CAAA5H,YAAA,CAAoD;UAKpDpC,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAA0E,gBAAA,YAAAuN,GAAA,CAAAjI,yBAAA,CAAA3H,UAAA,CAAkD;UAMzCrC,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAA0E,gBAAA,YAAAuN,GAAA,CAAAjI,yBAAA,CAAAzH,UAAA,CAAkD;UAC/BvC,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,YAAA6R,GAAA,CAAA/K,SAAA,CAAY;UAO/BlH,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAA0E,gBAAA,YAAAuN,GAAA,CAAAjI,yBAAA,CAAApH,OAAA,CAA+C;UAC7C5C,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACZJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UACXJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UAKbJ,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAA0E,gBAAA,YAAAuN,GAAA,CAAAjI,yBAAA,CAAA3D,OAAA,CAA+C;UAC7CrG,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UAOjBJ,EAAA,CAAAO,SAAA,GAAiD;UAAjDP,EAAA,CAAA0E,gBAAA,YAAAuN,GAAA,CAAAjI,yBAAA,CAAAzD,SAAA,CAAiD;UAC/CvG,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UAYgCJ,EAAA,CAAAO,SAAA,IAAc;UAAdP,EAAA,CAAAI,UAAA,SAAA6R,GAAA,CAAAqC,QAAA,CAAc;UA+B7CtU,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAA6R,GAAA,CAAA/H,eAAA,CAAoB;UAoB/BlK,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAA6R,GAAA,CAAAnE,YAAA,CAA+B;UAAC9N,EAAA,CAAA0E,gBAAA,SAAAuN,GAAA,CAAApE,SAAA,CAAoB;UAAC7N,EAAA,CAAAI,UAAA,aAAA6R,GAAA,CAAAtE,QAAA,CAAqB;UAyChF3N,EAAA,CAAAO,SAAA,IAAmD;UAAnDP,EAAA,CAAAI,UAAA,aAAA6R,GAAA,CAAA/O,uBAAA,GAAAC,MAAA,OAAmD;UAE5CnD,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAI,UAAA,SAAA6R,GAAA,CAAA/O,uBAAA,GAAAC,MAAA,KAA0C;UAUxBnD,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAI,UAAA,SAAA6R,GAAA,CAAA/O,uBAAA,GAAAC,MAAA,OAA4C;UAG1CnD,EAAA,CAAAO,SAAA,EAA0C;UAA1CP,EAAA,CAAAI,UAAA,SAAA6R,GAAA,CAAA/O,uBAAA,GAAAC,MAAA,KAA0C;UAOtEnD,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAI,UAAA,SAAA6R,GAAA,CAAA/H,eAAA,CAAA/G,MAAA,KAAgC;UAIhCnD,EAAA,CAAAO,SAAA,EAA0C;UAA1CP,EAAA,CAAAI,UAAA,SAAA6R,GAAA,CAAA/O,uBAAA,GAAAC,MAAA,KAA0C;UAcSnD,EAAA,CAAAO,SAAA,GAA2B;UACzEP,EAD8C,CAAAI,UAAA,YAAA6R,GAAA,CAAA9C,aAAA,GAA2B,kBAAA8C,GAAA,CAAA5C,eAAA,GACtC;UAcpBrP,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAA6R,GAAA,CAAA/H,eAAA,CAAoB;UAwB/BlK,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAA6R,GAAA,CAAAnE,YAAA,CAA+B;UAAC9N,EAAA,CAAA0E,gBAAA,SAAAuN,GAAA,CAAApE,SAAA,CAAoB;UAAC7N,EAAA,CAAAI,UAAA,aAAA6R,GAAA,CAAAtE,QAAA,CAAqB;UAqLpD3N,EAAA,CAAAO,SAAA,GAAuC;UACzFP,EADkD,CAAAI,UAAA,cAAA6R,GAAA,CAAA3H,yBAAA,CAAuC,iBAAA2H,GAAA,CAAAjI,yBAAA,CAAAjF,YAAA,kBAAAkN,GAAA,CAAAjI,yBAAA,CAAAjF,YAAA,CAAAwP,QAAA,UACjB;;;qBDzYtE5V,YAAY,EAAAoS,EAAA,CAAAyD,eAAA,EAAAzD,EAAA,CAAA0D,mBAAA,EAAA1D,EAAA,CAAA2D,qBAAA,EAAA3D,EAAA,CAAA4D,qBAAA,EACZvV,mBAAmB,EACnBP,aAAa,EAAAkS,EAAA,CAAA6D,gBAAA,EACbvV,WAAW,EAAAwV,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,4BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,kBAAA,EAAAL,EAAA,CAAAM,OAAA,EACXpW,cAAc,EAAAgS,EAAA,CAAAqE,iBAAA,EAAArE,EAAA,CAAAsE,iBAAA,EACdvW,cAAc,EACdS,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVb,gBAAgB,EAAAmS,EAAA,CAAAuE,mBAAA,EAChB5V,kBAAkB,EAClBC,oBAAoB,EACpBX,cAAc,EAAA+R,EAAA,CAAAwE,iBAAA,EAAAxE,EAAA,CAAAyE,cAAA,EACd3V,uBAAuB,EACvBC,wBAAwB,EACxBC,8BAA8B;MAAA0V,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}