{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { RequirementTemplateSelectorComponent } from '../requirement-template-selector/requirement-template-selector.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"@angular/common\";\nfunction RequirementTemplateSelectorButtonComponent_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.icon);\n  }\n}\nexport class RequirementTemplateSelectorButtonComponent {\n  constructor(dialogService) {\n    this.dialogService = dialogService;\n    this.buildCaseId = '';\n    this.text = '選擇需求項目';\n    this.icon = 'fas fa-list';\n    this.buttonClass = 'btn btn-primary';\n    this.disabled = false;\n    this.multiple = true;\n    this.preSelectedItems = [];\n    this.selectionConfirmed = new EventEmitter();\n    this.selectionCancelled = new EventEmitter();\n    this.error = new EventEmitter();\n  }\n  openSelector() {\n    if (this.disabled) {\n      return;\n    }\n    if (!this.buildCaseId) {\n      this.error.emit('請先選擇建案');\n      return;\n    }\n    const dialogRef = this.dialogService.open(RequirementTemplateSelectorComponent, {\n      context: {\n        buildCaseId: parseInt(this.buildCaseId),\n        multiple: this.multiple,\n        preSelectedItems: this.preSelectedItems\n      },\n      autoFocus: false,\n      closeOnBackdropClick: false,\n      closeOnEsc: true\n    });\n    // 監聽選擇確認事件\n    dialogRef.componentRef.instance.selectionConfirmed.subscribe(config => {\n      this.selectionConfirmed.emit(config);\n    });\n    // 監聽取消事件\n    dialogRef.componentRef.instance.selectionCancelled.subscribe(() => {\n      this.selectionCancelled.emit();\n    });\n  }\n  static {\n    this.ɵfac = function RequirementTemplateSelectorButtonComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementTemplateSelectorButtonComponent)(i0.ɵɵdirectiveInject(i1.NbDialogService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementTemplateSelectorButtonComponent,\n      selectors: [[\"app-requirement-template-selector-button\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        text: \"text\",\n        icon: \"icon\",\n        buttonClass: \"buttonClass\",\n        disabled: \"disabled\",\n        multiple: \"multiple\",\n        preSelectedItems: \"preSelectedItems\"\n      },\n      outputs: {\n        selectionConfirmed: \"selectionConfirmed\",\n        selectionCancelled: \"selectionCancelled\",\n        error: \"error\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 5,\n      consts: [[3, \"click\", \"disabled\"], [3, \"class\", 4, \"ngIf\"]],\n      template: function RequirementTemplateSelectorButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorButtonComponent_Template_button_click_0_listener() {\n            return ctx.openSelector();\n          });\n          i0.ɵɵtemplate(1, RequirementTemplateSelectorButtonComponent_i_1_Template, 1, 2, \"i\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.buttonClass);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.icon);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.text, \" \");\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, NbButtonModule, NbIconModule],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "NbButtonModule", "NbIconModule", "RequirementTemplateSelectorComponent", "i0", "ɵɵelement", "ɵɵclassMap", "ctx_r0", "icon", "RequirementTemplateSelectorButtonComponent", "constructor", "dialogService", "buildCaseId", "text", "buttonClass", "disabled", "multiple", "preSelectedItems", "selectionConfirmed", "selectionCancelled", "error", "openSelector", "emit", "dialogRef", "open", "context", "parseInt", "autoFocus", "closeOnBackdropClick", "closeOnEsc", "componentRef", "instance", "subscribe", "config", "ɵɵdirectiveInject", "i1", "NbDialogService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequirementTemplateSelectorButtonComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "RequirementTemplateSelectorButtonComponent_Template_button_click_0_listener", "ɵɵtemplate", "RequirementTemplateSelectorButtonComponent_i_1_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate1", "i2", "NgIf", "encapsulation"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\requirement-template-selector-button\\requirement-template-selector-button.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbButtonModule, NbIconModule, NbDialogService } from '@nebular/theme';\nimport { \n  RequirementTemplateSelectorComponent, \n  RequirementSelectionConfig,\n  ExtendedRequirementItem \n} from '../requirement-template-selector/requirement-template-selector.component';\n\n@Component({\n  selector: 'app-requirement-template-selector-button',\n  template: `\n    <button \n      [class]=\"buttonClass\" \n      [disabled]=\"disabled\"\n      (click)=\"openSelector()\">\n      <i [class]=\"icon\" *ngIf=\"icon\"></i>\n      {{ text }}\n    </button>\n  `,\n  standalone: true,\n  imports: [\n    CommonModule,\n    NbButtonModule,\n    NbIconModule\n  ]\n})\nexport class RequirementTemplateSelectorButtonComponent {\n  @Input() buildCaseId: string = '';\n  @Input() text: string = '選擇需求項目';\n  @Input() icon: string = 'fas fa-list';\n  @Input() buttonClass: string = 'btn btn-primary';\n  @Input() disabled: boolean = false;\n  @Input() multiple: boolean = true;\n  @Input() preSelectedItems: ExtendedRequirementItem[] = [];\n  \n  @Output() selectionConfirmed = new EventEmitter<RequirementSelectionConfig>();\n  @Output() selectionCancelled = new EventEmitter<void>();\n  @Output() error = new EventEmitter<string>();\n\n  constructor(private dialogService: NbDialogService) { }\n\n  openSelector() {\n    if (this.disabled) {\n      return;\n    }\n\n    if (!this.buildCaseId) {\n      this.error.emit('請先選擇建案');\n      return;\n    }\n\n    const dialogRef = this.dialogService.open(RequirementTemplateSelectorComponent, {\n      context: {\n        buildCaseId: parseInt(this.buildCaseId),\n        multiple: this.multiple,\n        preSelectedItems: this.preSelectedItems\n      },\n      autoFocus: false,\n      closeOnBackdropClick: false,\n      closeOnEsc: true\n    });\n\n    // 監聽選擇確認事件\n    dialogRef.componentRef.instance.selectionConfirmed.subscribe(\n      (config: RequirementSelectionConfig) => {\n        this.selectionConfirmed.emit(config);\n      }\n    );\n\n    // 監聽取消事件\n    dialogRef.componentRef.instance.selectionCancelled.subscribe(\n      () => {\n        this.selectionCancelled.emit();\n      }\n    );\n  }\n}\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,EAAEC,YAAY,QAAyB,gBAAgB;AAC9E,SACEC,oCAAoC,QAG/B,0EAA0E;;;;;;IAS3EC,EAAA,CAAAC,SAAA,QAAmC;;;;IAAhCD,EAAA,CAAAE,UAAA,CAAAC,MAAA,CAAAC,IAAA,CAAc;;;AAWvB,OAAM,MAAOC,0CAA0C;EAarDC,YAAoBC,aAA8B;IAA9B,KAAAA,aAAa,GAAbA,aAAa;IAZxB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,IAAI,GAAW,QAAQ;IACvB,KAAAL,IAAI,GAAW,aAAa;IAC5B,KAAAM,WAAW,GAAW,iBAAiB;IACvC,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,QAAQ,GAAY,IAAI;IACxB,KAAAC,gBAAgB,GAA8B,EAAE;IAE/C,KAAAC,kBAAkB,GAAG,IAAInB,YAAY,EAA8B;IACnE,KAAAoB,kBAAkB,GAAG,IAAIpB,YAAY,EAAQ;IAC7C,KAAAqB,KAAK,GAAG,IAAIrB,YAAY,EAAU;EAEU;EAEtDsB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACN,QAAQ,EAAE;MACjB;IACF;IAEA,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;MACrB,IAAI,CAACQ,KAAK,CAACE,IAAI,CAAC,QAAQ,CAAC;MACzB;IACF;IAEA,MAAMC,SAAS,GAAG,IAAI,CAACZ,aAAa,CAACa,IAAI,CAACrB,oCAAoC,EAAE;MAC9EsB,OAAO,EAAE;QACPb,WAAW,EAAEc,QAAQ,CAAC,IAAI,CAACd,WAAW,CAAC;QACvCI,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,gBAAgB,EAAE,IAAI,CAACA;OACxB;MACDU,SAAS,EAAE,KAAK;MAChBC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE;KACb,CAAC;IAEF;IACAN,SAAS,CAACO,YAAY,CAACC,QAAQ,CAACb,kBAAkB,CAACc,SAAS,CACzDC,MAAkC,IAAI;MACrC,IAAI,CAACf,kBAAkB,CAACI,IAAI,CAACW,MAAM,CAAC;IACtC,CAAC,CACF;IAED;IACAV,SAAS,CAACO,YAAY,CAACC,QAAQ,CAACZ,kBAAkB,CAACa,SAAS,CAC1D,MAAK;MACH,IAAI,CAACb,kBAAkB,CAACG,IAAI,EAAE;IAChC,CAAC,CACF;EACH;;;uCAjDWb,0CAA0C,EAAAL,EAAA,CAAA8B,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAA1C3B,0CAA0C;MAAA4B,SAAA;MAAAC,MAAA;QAAA1B,WAAA;QAAAC,IAAA;QAAAL,IAAA;QAAAM,WAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,gBAAA;MAAA;MAAAsB,OAAA;QAAArB,kBAAA;QAAAC,kBAAA;QAAAC,KAAA;MAAA;MAAAoB,UAAA;MAAAC,QAAA,GAAArC,EAAA,CAAAsC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAfnD5C,EAAA,CAAA8C,cAAA,gBAG2B;UAAzB9C,EAAA,CAAA+C,UAAA,mBAAAC,4EAAA;YAAA,OAASH,GAAA,CAAA5B,YAAA,EAAc;UAAA,EAAC;UACxBjB,EAAA,CAAAiD,UAAA,IAAAC,uDAAA,eAA+B;UAC/BlD,EAAA,CAAAmD,MAAA,GACF;UAAAnD,EAAA,CAAAoD,YAAA,EAAS;;;UALPpD,EAAA,CAAAE,UAAA,CAAA2C,GAAA,CAAAnC,WAAA,CAAqB;UACrBV,EAAA,CAAAqD,UAAA,aAAAR,GAAA,CAAAlC,QAAA,CAAqB;UAEFX,EAAA,CAAAsD,SAAA,EAAU;UAAVtD,EAAA,CAAAqD,UAAA,SAAAR,GAAA,CAAAzC,IAAA,CAAU;UAC7BJ,EAAA,CAAAsD,SAAA,EACF;UADEtD,EAAA,CAAAuD,kBAAA,MAAAV,GAAA,CAAApC,IAAA,MACF;;;qBAIAb,YAAY,EAAA4D,EAAA,CAAAC,IAAA,EACZ5D,cAAc,EACdC,YAAY;MAAA4D,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}