{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /** @preserve\n   * Counter block mode compatible with  Dr <PERSON> fileenc.c\n   * derived from CryptoJS.mode.CTR\n   * <NAME_EMAIL>\n   */\n  CryptoJS.mode.CTRGladman = function () {\n    var CTRGladman = CryptoJS.lib.BlockCipherMode.extend();\n    function incWord(word) {\n      if ((word >> 24 & 0xff) === 0xff) {\n        //overflow\n        var b1 = word >> 16 & 0xff;\n        var b2 = word >> 8 & 0xff;\n        var b3 = word & 0xff;\n        if (b1 === 0xff)\n          // overflow b1\n          {\n            b1 = 0;\n            if (b2 === 0xff) {\n              b2 = 0;\n              if (b3 === 0xff) {\n                b3 = 0;\n              } else {\n                ++b3;\n              }\n            } else {\n              ++b2;\n            }\n          } else {\n          ++b1;\n        }\n        word = 0;\n        word += b1 << 16;\n        word += b2 << 8;\n        word += b3;\n      } else {\n        word += 0x01 << 24;\n      }\n      return word;\n    }\n    function incCounter(counter) {\n      if ((counter[0] = incWord(counter[0])) === 0) {\n        // encr_data in fileenc.c from  Dr Brian Gladman's counts only with DWORD j < 8\n        counter[1] = incWord(counter[1]);\n      }\n      return counter;\n    }\n    var Encryptor = CTRGladman.Encryptor = CTRGladman.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n        var iv = this._iv;\n        var counter = this._counter;\n\n        // Generate keystream\n        if (iv) {\n          counter = this._counter = iv.slice(0);\n\n          // Remove IV for subsequent blocks\n          this._iv = undefined;\n        }\n        incCounter(counter);\n        var keystream = counter.slice(0);\n        cipher.encryptBlock(keystream, 0);\n\n        // Encrypt\n        for (var i = 0; i < blockSize; i++) {\n          words[offset + i] ^= keystream[i];\n        }\n      }\n    });\n    CTRGladman.Decryptor = Encryptor;\n    return CTRGladman;\n  }();\n  return CryptoJS.mode.CTRGladman;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}