{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule, NbPopoverModule } from '@nebular/theme';\nimport { HouseholdBindingComponent } from './components/household-binding/household-binding.component';\nimport { TestDropdownComponent } from './components/household-binding/test-dropdown.component';\nimport { SimpleDropdownTestComponent } from './components/household-binding/simple-dropdown-test.component';\nlet SharedModule = class SharedModule {};\nSharedModule = __decorate([NgModule({\n  declarations: [HouseholdBindingComponent, TestDropdownComponent, SimpleDropdownTestComponent],\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule, NbPopoverModule],\n  exports: [HouseholdBindingComponent, TestDropdownComponent, SimpleDropdownTestComponent,\n  // 也可以導出常用的模組供其他地方使用\n  CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule, NbPopoverModule]\n})], SharedModule);\nexport { SharedModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "FormsModule", "ReactiveFormsModule", "NbThemeModule", "NbLayoutModule", "NbCardModule", "NbButtonModule", "NbSelectModule", "NbInputModule", "NbCheckboxModule", "NbIconModule", "NbListModule", "NbTagModule", "NbPopoverModule", "HouseholdBindingComponent", "TestDropdownComponent", "SimpleDropdownTestComponent", "SharedModule", "__decorate", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport {\n  NbThemeModule,\n  NbLayoutModule,\n  NbCardModule,\n  NbButtonModule,\n  NbSelectModule,\n  NbInputModule,\n  NbCheckboxModule,\n  NbIconModule,\n  NbListModule,\n  NbTagModule,\n  NbPopoverModule\n} from '@nebular/theme';\n\nimport { HouseholdBindingComponent } from './components/household-binding/household-binding.component';\nimport { TestDropdownComponent } from './components/household-binding/test-dropdown.component';\nimport { SimpleDropdownTestComponent } from './components/household-binding/simple-dropdown-test.component';\n\n@NgModule({\n  declarations: [\n    HouseholdBindingComponent,\n    TestDropdownComponent,\n    SimpleDropdownTestComponent\n  ],\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    NbThemeModule,\n    NbLayoutModule,\n    NbCardModule,\n    NbButtonModule,\n    NbSelectModule,\n    NbInputModule,\n    NbCheckboxModule,\n    NbIconModule,\n    NbListModule,\n    NbTagModule,\n    NbPopoverModule], exports: [\n      HouseholdBindingComponent,\n      TestDropdownComponent,\n      SimpleDropdownTestComponent,\n      // 也可以導出常用的模組供其他地方使用\n      CommonModule,\n      FormsModule,\n      ReactiveFormsModule,\n      NbThemeModule,\n      NbLayoutModule,\n      NbCardModule,\n      NbButtonModule,\n      NbSelectModule,\n      NbInputModule,\n      NbCheckboxModule,\n      NbIconModule,\n      NbListModule,\n      NbTagModule,\n      NbPopoverModule\n    ]\n})\nexport class SharedModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SACEC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,eAAe,QACV,gBAAgB;AAEvB,SAASC,yBAAyB,QAAQ,4DAA4D;AACtG,SAASC,qBAAqB,QAAQ,wDAAwD;AAC9F,SAASC,2BAA2B,QAAQ,+DAA+D;AA2CpG,IAAMC,YAAY,GAAlB,MAAMA,YAAY,GAAI;AAAhBA,YAAY,GAAAC,UAAA,EAzCxBnB,QAAQ,CAAC;EACRoB,YAAY,EAAE,CACZL,yBAAyB,EACzBC,qBAAqB,EACrBC,2BAA2B,CAC5B;EACDI,OAAO,EAAE,CACPpB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,eAAe,CAAC;EAAEQ,OAAO,EAAE,CACzBP,yBAAyB,EACzBC,qBAAqB,EACrBC,2BAA2B;EAC3B;EACAhB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,eAAe;CAEpB,CAAC,C,EACWI,YAAY,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}