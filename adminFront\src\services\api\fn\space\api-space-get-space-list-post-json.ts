/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { GetSpaceListRequest } from '../../models/get-space-list-request';
import { GetSpaceListResponseListResponseBase } from '../../models/get-space-list-response-list-response-base';

export interface ApiSpaceGetSpaceListPost$Json$Params {
      body?: GetSpaceListRequest
}

export function apiSpaceGetSpaceListPost$Json(http: HttpClient, rootUrl: string, params?: ApiSpaceGetSpaceListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpaceListResponseListResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiSpaceGetSpaceListPost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<GetSpaceListResponseListResponseBase>;
    })
  );
}

apiSpaceGetSpaceListPost$Json.PATH = '/api/Space/GetSpaceList';
