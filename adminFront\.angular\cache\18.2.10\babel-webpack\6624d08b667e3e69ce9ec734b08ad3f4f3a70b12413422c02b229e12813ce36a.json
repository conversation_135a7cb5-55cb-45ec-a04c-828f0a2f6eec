{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbButtonModule, NbCardModule, NbSelectModule, NbOptionModule, NbIconModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@nebular/theme\";\nfunction ItemSelectorComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", area_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", area_r1.label, \" \");\n  }\n}\nfunction ItemSelectorComponent_nb_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r2.label, \" \");\n  }\n}\nfunction ItemSelectorComponent_div_26_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 5)(2, \"small\", 27);\n    i0.ɵɵtext(3, \" \\u5176\\u4ED6\\u8CC7\\u8A0A\\u5C07\\u5728\\u78BA\\u8A8D\\u5F8C\\u81EA\\u52D5\\u5E36\\u5165\\u8868\\u55AE \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ItemSelectorComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 20)(3, \"h6\", 21);\n    i0.ɵɵelement(4, \"nb-icon\", 22);\n    i0.ɵɵtext(5, \" \\u5DF2\\u9078\\u64C7\\u9805\\u76EE\\u8CC7\\u8A0A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 23)(7, \"div\", 24)(8, \"strong\");\n    i0.ɵɵtext(9, \"\\u6240\\u5C6C\\u5340\\u57DF\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"strong\");\n    i0.ɵɵtext(13, \"\\u9805\\u76EE\\u540D\\u7A31\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, ItemSelectorComponent_div_26_div_15_Template, 4, 0, \"div\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getAreaLabel(ctx_r2.selectedItemData.area), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedItemData.label, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedItemData.data);\n  }\n}\nexport class ItemSelectorComponent {\n  constructor() {\n    this.areaOptions = [];\n    this.itemSelected = new EventEmitter();\n    this.close = new EventEmitter();\n    this.selectedArea = '';\n    this.selectedItem = '';\n    this.availableItems = [];\n    this.selectedItemData = null;\n  }\n  onAreaChange(areaValue) {\n    this.selectedArea = areaValue;\n    this.selectedItem = '';\n    this.selectedItemData = null;\n    // 根據選擇的區域過濾項目\n    const area = this.areaOptions.find(a => a.value === areaValue);\n    this.availableItems = area ? area.items : [];\n  }\n  onItemChange(itemValue) {\n    this.selectedItem = itemValue;\n    this.selectedItemData = this.availableItems.find(item => item.value === itemValue) || null;\n  }\n  getAreaLabel(areaValue) {\n    const area = this.areaOptions.find(a => a.value === areaValue);\n    return area ? area.label : areaValue;\n  }\n  onConfirm() {\n    if (this.selectedArea && this.selectedItem && this.selectedItemData) {\n      const result = {\n        area: this.selectedArea,\n        itemName: this.selectedItem,\n        ...this.selectedItemData.data // 展開額外的數據\n      };\n      this.itemSelected.emit(result);\n    }\n  }\n  onClose() {\n    this.close.emit();\n  }\n  static {\n    this.ɵfac = function ItemSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ItemSelectorComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ItemSelectorComponent,\n      selectors: [[\"app-item-selector\"]],\n      inputs: {\n        areaOptions: \"areaOptions\"\n      },\n      outputs: {\n        itemSelected: \"itemSelected\",\n        close: \"close\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 34,\n      vars: 7,\n      consts: [[2, \"max-width\", \"600px\", \"height\", \"auto\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"nbButton\", \"\", \"ghost\", \"\", \"size\", \"small\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"row\", \"mb-3\"], [1, \"col-12\"], [1, \"text-muted\", \"mb-3\"], [1, \"col-12\", \"col-md-6\"], [1, \"form-label\"], [1, \"text-danger\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5340\\u57DF\", \"fullWidth\", \"\", 3, \"selectedChange\", \"selected\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"\\u8ACB\\u5148\\u9078\\u64C7\\u5340\\u57DF\", \"fullWidth\", \"\", 3, \"selectedChange\", \"selected\", \"disabled\"], [\"class\", \"row mb-3\", 4, \"ngIf\"], [1, \"text-center\"], [\"nbButton\", \"\", \"status\", \"success\", 1, \"me-2\", 3, \"click\", \"disabled\"], [\"icon\", \"checkmark-outline\", 1, \"me-1\"], [\"nbButton\", \"\", \"status\", \"basic\", 3, \"click\"], [\"icon\", \"close-outline\", 1, \"me-1\"], [3, \"value\"], [1, \"preview-card\", \"p-3\", \"border\", \"rounded\", \"bg-light\"], [1, \"mb-2\"], [\"icon\", \"info-outline\", 1, \"me-2\"], [1, \"row\"], [1, \"col-6\"], [\"class\", \"row mt-2\", 4, \"ngIf\"], [1, \"row\", \"mt-2\"], [1, \"text-muted\"]],\n      template: function ItemSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\", 1)(2, \"span\");\n          i0.ɵɵtext(3, \"\\u9078\\u64C7\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function ItemSelectorComponent_Template_button_click_4_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelement(5, \"nb-icon\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\")(7, \"div\", 4)(8, \"div\", 5)(9, \"p\", 6);\n          i0.ɵɵtext(10, \"\\u8ACB\\u9078\\u64C7\\u6240\\u5C6C\\u5340\\u57DF\\u548C\\u9805\\u76EE\\u540D\\u7A31\\uFF0C\\u9078\\u64C7\\u5F8C\\u5C07\\u81EA\\u52D5\\u5E36\\u5165\\u76F8\\u95DC\\u8CC7\\u6599\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 4)(12, \"div\", 7)(13, \"label\", 8);\n          i0.ɵɵtext(14, \"\\u6240\\u5C6C\\u5340\\u57DF \");\n          i0.ɵɵelementStart(15, \"span\", 9);\n          i0.ɵɵtext(16, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"nb-select\", 10);\n          i0.ɵɵtwoWayListener(\"selectedChange\", function ItemSelectorComponent_Template_nb_select_selectedChange_17_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedArea, $event) || (ctx.selectedArea = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectedChange\", function ItemSelectorComponent_Template_nb_select_selectedChange_17_listener($event) {\n            return ctx.onAreaChange($event);\n          });\n          i0.ɵɵtemplate(18, ItemSelectorComponent_nb_option_18_Template, 2, 2, \"nb-option\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 7)(20, \"label\", 8);\n          i0.ɵɵtext(21, \"\\u9805\\u76EE\\u540D\\u7A31 \");\n          i0.ɵɵelementStart(22, \"span\", 9);\n          i0.ɵɵtext(23, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"nb-select\", 12);\n          i0.ɵɵtwoWayListener(\"selectedChange\", function ItemSelectorComponent_Template_nb_select_selectedChange_24_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedItem, $event) || (ctx.selectedItem = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectedChange\", function ItemSelectorComponent_Template_nb_select_selectedChange_24_listener($event) {\n            return ctx.onItemChange($event);\n          });\n          i0.ɵɵtemplate(25, ItemSelectorComponent_nb_option_25_Template, 2, 2, \"nb-option\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(26, ItemSelectorComponent_div_26_Template, 16, 3, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-card-footer\", 14)(28, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function ItemSelectorComponent_Template_button_click_28_listener() {\n            return ctx.onConfirm();\n          });\n          i0.ɵɵelement(29, \"nb-icon\", 16);\n          i0.ɵɵtext(30, \" \\u78BA\\u5B9A\\u9078\\u64C7 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function ItemSelectorComponent_Template_button_click_31_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelement(32, \"nb-icon\", 18);\n          i0.ɵɵtext(33, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(17);\n          i0.ɵɵtwoWayProperty(\"selected\", ctx.selectedArea);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.areaOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"selected\", ctx.selectedItem);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedArea || ctx.availableItems.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.availableItems);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItemData);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedArea || !ctx.selectedItem);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, FormsModule, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbSelectModule, i2.NbSelectComponent, i2.NbOptionComponent, NbOptionModule, NbIconModule, i2.NbIconComponent],\n      styles: [\".preview-card[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 0.5rem;\\n  display: block;\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545 !important;\\n}\\n\\n.me-1[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem !important;\\n}\\n\\n.me-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem !important;\\n}\\n\\nnb-card-header[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #ebeef2;\\n}\\n\\nnb-card-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n\\nnb-card-footer[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid #ebeef2;\\n  background-color: #f8f9fa;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIml0ZW0tc2VsZWN0b3IuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0UseUJBQUE7RUFDQSx5QkFBQTtBQUFOOztBQUdJO0VBQ0UsZ0JBQUE7RUFDQSxxQkFBQTtFQUNBLGNBQUE7QUFBTjs7QUFHSTtFQUNFLHlCQUFBO0FBQU47O0FBR0k7RUFDRSxnQ0FBQTtBQUFOOztBQUdJO0VBQ0UsK0JBQUE7QUFBTjs7QUFHSTtFQUNFLG9CQUFBO0VBQ0EsZ0NBQUE7QUFBTjs7QUFHSTtFQUNFLGVBQUE7QUFBTjs7QUFHSTtFQUNFLG9CQUFBO0VBQ0EsNkJBQUE7RUFDQSx5QkFBQTtBQUFOIiwiZmlsZSI6Iml0ZW0tc2VsZWN0b3IuY29tcG9uZW50LnRzIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLnByZXZpZXctY2FyZCB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2RlZTJlNjtcbiAgICB9XG4gICAgXG4gICAgLmZvcm0tbGFiZWwge1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgIH1cbiAgICBcbiAgICAudGV4dC1kYW5nZXIge1xuICAgICAgY29sb3I6ICNkYzM1NDUgIWltcG9ydGFudDtcbiAgICB9XG4gICAgXG4gICAgLm1lLTEge1xuICAgICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtICFpbXBvcnRhbnQ7XG4gICAgfVxuICAgIFxuICAgIC5tZS0yIHtcbiAgICAgIG1hcmdpbi1yaWdodDogMC41cmVtICFpbXBvcnRhbnQ7XG4gICAgfVxuICAgIFxuICAgIG5iLWNhcmQtaGVhZGVyIHtcbiAgICAgIHBhZGRpbmc6IDFyZW0gMS41cmVtO1xuICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjI7XG4gICAgfVxuICAgIFxuICAgIG5iLWNhcmQtYm9keSB7XG4gICAgICBwYWRkaW5nOiAxLjVyZW07XG4gICAgfVxuICAgIFxuICAgIG5iLWNhcmQtZm9vdGVyIHtcbiAgICAgIHBhZGRpbmc6IDFyZW0gMS41cmVtO1xuICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlYmVlZjI7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xuICAgIH1cbiAgIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvaXRlbS1zZWxlY3Rvci9pdGVtLXNlbGVjdG9yLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNFLHlCQUFBO0VBQ0EseUJBQUE7QUFBTjs7QUFHSTtFQUNFLGdCQUFBO0VBQ0EscUJBQUE7RUFDQSxjQUFBO0FBQU47O0FBR0k7RUFDRSx5QkFBQTtBQUFOOztBQUdJO0VBQ0UsZ0NBQUE7QUFBTjs7QUFHSTtFQUNFLCtCQUFBO0FBQU47O0FBR0k7RUFDRSxvQkFBQTtFQUNBLGdDQUFBO0FBQU47O0FBR0k7RUFDRSxlQUFBO0FBQU47O0FBR0k7RUFDRSxvQkFBQTtFQUNBLDZCQUFBO0VBQ0EseUJBQUE7QUFBTjtBQUNBLHc5Q0FBdzlDIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLnByZXZpZXctY2FyZCB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2RlZTJlNjtcbiAgICB9XG4gICAgXG4gICAgLmZvcm0tbGFiZWwge1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgIH1cbiAgICBcbiAgICAudGV4dC1kYW5nZXIge1xuICAgICAgY29sb3I6ICNkYzM1NDUgIWltcG9ydGFudDtcbiAgICB9XG4gICAgXG4gICAgLm1lLTEge1xuICAgICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtICFpbXBvcnRhbnQ7XG4gICAgfVxuICAgIFxuICAgIC5tZS0yIHtcbiAgICAgIG1hcmdpbi1yaWdodDogMC41cmVtICFpbXBvcnRhbnQ7XG4gICAgfVxuICAgIFxuICAgIG5iLWNhcmQtaGVhZGVyIHtcbiAgICAgIHBhZGRpbmc6IDFyZW0gMS41cmVtO1xuICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjI7XG4gICAgfVxuICAgIFxuICAgIG5iLWNhcmQtYm9keSB7XG4gICAgICBwYWRkaW5nOiAxLjVyZW07XG4gICAgfVxuICAgIFxuICAgIG5iLWNhcmQtZm9vdGVyIHtcbiAgICAgIHBhZGRpbmc6IDFyZW0gMS41cmVtO1xuICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlYmVlZjI7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbButtonModule", "NbCardModule", "NbSelectModule", "NbOptionModule", "NbIconModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "area_r1", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "item_r2", "ɵɵelement", "ɵɵtemplate", "ItemSelectorComponent_div_26_div_15_Template", "ctx_r2", "getAreaLabel", "selectedItemData", "area", "data", "ItemSelectorComponent", "constructor", "areaOptions", "itemSelected", "close", "<PERSON><PERSON><PERSON>", "selectedItem", "availableItems", "onAreaChange", "areaValue", "find", "a", "items", "onItemChange", "itemValue", "item", "onConfirm", "result", "itemName", "emit", "onClose", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ItemSelectorComponent_Template", "rf", "ctx", "ɵɵlistener", "ItemSelectorComponent_Template_button_click_4_listener", "ɵɵtwoWayListener", "ItemSelectorComponent_Template_nb_select_selected<PERSON><PERSON>e_17_listener", "$event", "ɵɵtwoWayBindingSet", "ItemSelectorComponent_nb_option_18_Template", "ItemSelectorComponent_Template_nb_select_selected<PERSON><PERSON><PERSON>_24_listener", "ItemSelectorComponent_nb_option_25_Template", "ItemSelectorComponent_div_26_Template", "ItemSelectorComponent_Template_button_click_28_listener", "ItemSelectorComponent_Template_button_click_31_listener", "ɵɵtwoWayProperty", "length", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbSelectComponent", "NbOptionComponent", "NbIconComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\item-selector\\item-selector.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbButtonModule, NbCardModule, NbSelectModule, NbOptionModule, NbIconModule } from '@nebular/theme';\r\n\r\n// 項目數據接口\r\nexport interface ItemSelectorData {\r\n  area: string;      // 所屬區域\r\n  itemName: string;  // 項目名稱\r\n  [key: string]: any; // 允許其他額外屬性\r\n}\r\n\r\n// 區域選項接口\r\nexport interface AreaOption {\r\n  value: string;\r\n  label: string;\r\n  items: ItemOption[];\r\n}\r\n\r\n// 項目選項接口\r\nexport interface ItemOption {\r\n  value: string;\r\n  label: string;\r\n  area: string;\r\n  data?: any; // 額外的項目數據\r\n}\r\n\r\n@Component({\r\n  selector: 'app-item-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NbIconModule\r\n  ],\r\n  template: `\r\n    <nb-card style=\"max-width: 600px; height: auto;\">\r\n      <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n        <span>選擇項目</span>\r\n        <button nbButton ghost size=\"small\" (click)=\"onClose()\">\r\n          <nb-icon icon=\"close-outline\"></nb-icon>\r\n        </button>\r\n      </nb-card-header>\r\n      \r\n      <nb-card-body>\r\n        <div class=\"row mb-3\">\r\n          <div class=\"col-12\">\r\n            <p class=\"text-muted mb-3\">請選擇所屬區域和項目名稱，選擇後將自動帶入相關資料</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"row mb-3\">\r\n          <div class=\"col-12 col-md-6\">\r\n            <label class=\"form-label\">所屬區域 <span class=\"text-danger\">*</span></label>\r\n            <nb-select \r\n              [(selected)]=\"selectedArea\" \r\n              (selectedChange)=\"onAreaChange($event)\"\r\n              placeholder=\"請選擇區域\"\r\n              fullWidth>\r\n              <nb-option *ngFor=\"let area of areaOptions\" [value]=\"area.value\">\r\n                {{ area.label }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n          \r\n          <div class=\"col-12 col-md-6\">\r\n            <label class=\"form-label\">項目名稱 <span class=\"text-danger\">*</span></label>\r\n            <nb-select \r\n              [(selected)]=\"selectedItem\" \r\n              (selectedChange)=\"onItemChange($event)\"\r\n              placeholder=\"請先選擇區域\"\r\n              [disabled]=\"!selectedArea || availableItems.length === 0\"\r\n              fullWidth>\r\n              <nb-option *ngFor=\"let item of availableItems\" [value]=\"item.value\">\r\n                {{ item.label }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 預覽選擇的項目資訊 -->\r\n        <div class=\"row mb-3\" *ngIf=\"selectedItemData\">\r\n          <div class=\"col-12\">\r\n            <div class=\"preview-card p-3 border rounded bg-light\">\r\n              <h6 class=\"mb-2\">\r\n                <nb-icon icon=\"info-outline\" class=\"me-2\"></nb-icon>\r\n                已選擇項目資訊\r\n              </h6>\r\n              <div class=\"row\">\r\n                <div class=\"col-6\">\r\n                  <strong>所屬區域：</strong> {{ getAreaLabel(selectedItemData.area) }}\r\n                </div>\r\n                <div class=\"col-6\">\r\n                  <strong>項目名稱：</strong> {{ selectedItemData.label }}\r\n                </div>\r\n              </div>\r\n              <div class=\"row mt-2\" *ngIf=\"selectedItemData.data\">\r\n                <div class=\"col-12\">\r\n                  <small class=\"text-muted\">\r\n                    其他資訊將在確認後自動帶入表單\r\n                  </small>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </nb-card-body>\r\n\r\n      <nb-card-footer class=\"text-center\">\r\n        <button \r\n          nbButton \r\n          status=\"success\" \r\n          class=\"me-2\"\r\n          [disabled]=\"!selectedArea || !selectedItem\"\r\n          (click)=\"onConfirm()\">\r\n          <nb-icon icon=\"checkmark-outline\" class=\"me-1\"></nb-icon>\r\n          確定選擇\r\n        </button>\r\n        <button \r\n          nbButton \r\n          status=\"basic\" \r\n          (click)=\"onClose()\">\r\n          <nb-icon icon=\"close-outline\" class=\"me-1\"></nb-icon>\r\n          取消\r\n        </button>\r\n      </nb-card-footer>\r\n    </nb-card>\r\n  `,\r\n  styles: [`\r\n    .preview-card {\r\n      background-color: #f8f9fa;\r\n      border: 1px solid #dee2e6;\r\n    }\r\n    \r\n    .form-label {\r\n      font-weight: 500;\r\n      margin-bottom: 0.5rem;\r\n      display: block;\r\n    }\r\n    \r\n    .text-danger {\r\n      color: #dc3545 !important;\r\n    }\r\n    \r\n    .me-1 {\r\n      margin-right: 0.25rem !important;\r\n    }\r\n    \r\n    .me-2 {\r\n      margin-right: 0.5rem !important;\r\n    }\r\n    \r\n    nb-card-header {\r\n      padding: 1rem 1.5rem;\r\n      border-bottom: 1px solid #ebeef2;\r\n    }\r\n    \r\n    nb-card-body {\r\n      padding: 1.5rem;\r\n    }\r\n    \r\n    nb-card-footer {\r\n      padding: 1rem 1.5rem;\r\n      border-top: 1px solid #ebeef2;\r\n      background-color: #f8f9fa;\r\n    }\r\n  `]\r\n})\r\nexport class ItemSelectorComponent {\r\n  @Input() areaOptions: AreaOption[] = [];\r\n  @Output() itemSelected = new EventEmitter<ItemSelectorData>();\r\n  @Output() close = new EventEmitter<void>();\r\n\r\n  selectedArea: string = '';\r\n  selectedItem: string = '';\r\n  availableItems: ItemOption[] = [];\r\n  selectedItemData: ItemOption | null = null;\r\n\r\n  onAreaChange(areaValue: string) {\r\n    this.selectedArea = areaValue;\r\n    this.selectedItem = '';\r\n    this.selectedItemData = null;\r\n    \r\n    // 根據選擇的區域過濾項目\r\n    const area = this.areaOptions.find(a => a.value === areaValue);\r\n    this.availableItems = area ? area.items : [];\r\n  }\r\n\r\n  onItemChange(itemValue: string) {\r\n    this.selectedItem = itemValue;\r\n    this.selectedItemData = this.availableItems.find(item => item.value === itemValue) || null;\r\n  }\r\n\r\n  getAreaLabel(areaValue: string): string {\r\n    const area = this.areaOptions.find(a => a.value === areaValue);\r\n    return area ? area.label : areaValue;\r\n  }\r\n\r\n  onConfirm() {\r\n    if (this.selectedArea && this.selectedItem && this.selectedItemData) {\r\n      const result: ItemSelectorData = {\r\n        area: this.selectedArea,\r\n        itemName: this.selectedItem,\r\n        ...this.selectedItemData.data // 展開額外的數據\r\n      };\r\n      this.itemSelected.emit(result);\r\n    }\r\n  }\r\n\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;;;;;;IA4D7FC,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFgCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,KAAA,CAAoB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,KAAA,MACF;;;;;IAYAT,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAJ,KAAA,CAAoB;IACjEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAD,KAAA,MACF;;;;;IAuBIT,EAFJ,CAAAC,cAAA,cAAoD,aAC9B,gBACQ;IACxBD,EAAA,CAAAE,MAAA,mGACF;IAEJF,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACF;;;;;IAlBNH,EAHN,CAAAC,cAAA,aAA+C,aACzB,cACoC,aACnC;IACfD,EAAA,CAAAW,SAAA,kBAAoD;IACpDX,EAAA,CAAAE,MAAA,mDACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGDH,EAFJ,CAAAC,cAAA,cAAiB,cACI,aACT;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACzB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAmB,cACT;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACzB;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAY,UAAA,KAAAC,4CAAA,kBAAoD;IAS1Db,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAf2BH,EAAA,CAAAO,SAAA,IACzB;IADyBP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAAC,YAAA,CAAAD,MAAA,CAAAE,gBAAA,CAAAC,IAAA,OACzB;IAEyBjB,EAAA,CAAAO,SAAA,GACzB;IADyBP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAAE,gBAAA,CAAAP,KAAA,MACzB;IAEqBT,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAE,gBAAA,CAAAE,IAAA,CAA2B;;;AAwEhE,OAAM,MAAOC,qBAAqB;EAjJlCC,YAAA;IAkJW,KAAAC,WAAW,GAAiB,EAAE;IAC7B,KAAAC,YAAY,GAAG,IAAI9B,YAAY,EAAoB;IACnD,KAAA+B,KAAK,GAAG,IAAI/B,YAAY,EAAQ;IAE1C,KAAAgC,YAAY,GAAW,EAAE;IACzB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,cAAc,GAAiB,EAAE;IACjC,KAAAV,gBAAgB,GAAsB,IAAI;;EAE1CW,YAAYA,CAACC,SAAiB;IAC5B,IAAI,CAACJ,YAAY,GAAGI,SAAS;IAC7B,IAAI,CAACH,YAAY,GAAG,EAAE;IACtB,IAAI,CAACT,gBAAgB,GAAG,IAAI;IAE5B;IACA,MAAMC,IAAI,GAAG,IAAI,CAACI,WAAW,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,KAAKsB,SAAS,CAAC;IAC9D,IAAI,CAACF,cAAc,GAAGT,IAAI,GAAGA,IAAI,CAACc,KAAK,GAAG,EAAE;EAC9C;EAEAC,YAAYA,CAACC,SAAiB;IAC5B,IAAI,CAACR,YAAY,GAAGQ,SAAS;IAC7B,IAAI,CAACjB,gBAAgB,GAAG,IAAI,CAACU,cAAc,CAACG,IAAI,CAACK,IAAI,IAAIA,IAAI,CAAC5B,KAAK,KAAK2B,SAAS,CAAC,IAAI,IAAI;EAC5F;EAEAlB,YAAYA,CAACa,SAAiB;IAC5B,MAAMX,IAAI,GAAG,IAAI,CAACI,WAAW,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,KAAKsB,SAAS,CAAC;IAC9D,OAAOX,IAAI,GAAGA,IAAI,CAACR,KAAK,GAAGmB,SAAS;EACtC;EAEAO,SAASA,CAAA;IACP,IAAI,IAAI,CAACX,YAAY,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,CAACT,gBAAgB,EAAE;MACnE,MAAMoB,MAAM,GAAqB;QAC/BnB,IAAI,EAAE,IAAI,CAACO,YAAY;QACvBa,QAAQ,EAAE,IAAI,CAACZ,YAAY;QAC3B,GAAG,IAAI,CAACT,gBAAgB,CAACE,IAAI,CAAC;OAC/B;MACD,IAAI,CAACI,YAAY,CAACgB,IAAI,CAACF,MAAM,CAAC;IAChC;EACF;EAEAG,OAAOA,CAAA;IACL,IAAI,CAAChB,KAAK,CAACe,IAAI,EAAE;EACnB;;;uCA3CWnB,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAqB,SAAA;MAAAC,MAAA;QAAApB,WAAA;MAAA;MAAAqB,OAAA;QAAApB,YAAA;QAAAC,KAAA;MAAA;MAAAoB,UAAA;MAAAC,QAAA,GAAA5C,EAAA,CAAA6C,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlI1BnD,EAFJ,CAAAC,cAAA,iBAAiD,wBAC2B,WAClE;UAAAD,EAAA,CAAAE,MAAA,+BAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjBH,EAAA,CAAAC,cAAA,gBAAwD;UAApBD,EAAA,CAAAqD,UAAA,mBAAAC,uDAAA;YAAA,OAASF,GAAA,CAAAb,OAAA,EAAS;UAAA,EAAC;UACrDvC,EAAA,CAAAW,SAAA,iBAAwC;UAE5CX,EADE,CAAAG,YAAA,EAAS,EACM;UAKXH,EAHN,CAAAC,cAAA,mBAAc,aACU,aACA,WACS;UAAAD,EAAA,CAAAE,MAAA,8JAAyB;UAExDF,EAFwD,CAAAG,YAAA,EAAI,EACpD,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACS,gBACD;UAAAD,EAAA,CAAAE,MAAA,iCAAK;UAAAF,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;UACzEH,EAAA,CAAAC,cAAA,qBAIY;UAHVD,EAAA,CAAAuD,gBAAA,4BAAAC,oEAAAC,MAAA;YAAAzD,EAAA,CAAA0D,kBAAA,CAAAN,GAAA,CAAA5B,YAAA,EAAAiC,MAAA,MAAAL,GAAA,CAAA5B,YAAA,GAAAiC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAC3BzD,EAAA,CAAAqD,UAAA,4BAAAG,oEAAAC,MAAA;YAAA,OAAkBL,GAAA,CAAAzB,YAAA,CAAA8B,MAAA,CAAoB;UAAA,EAAC;UAGvCzD,EAAA,CAAAY,UAAA,KAAA+C,2CAAA,wBAAiE;UAIrE3D,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,cAA6B,gBACD;UAAAD,EAAA,CAAAE,MAAA,iCAAK;UAAAF,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;UACzEH,EAAA,CAAAC,cAAA,qBAKY;UAJVD,EAAA,CAAAuD,gBAAA,4BAAAK,oEAAAH,MAAA;YAAAzD,EAAA,CAAA0D,kBAAA,CAAAN,GAAA,CAAA3B,YAAA,EAAAgC,MAAA,MAAAL,GAAA,CAAA3B,YAAA,GAAAgC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAC3BzD,EAAA,CAAAqD,UAAA,4BAAAO,oEAAAH,MAAA;YAAA,OAAkBL,GAAA,CAAApB,YAAA,CAAAyB,MAAA,CAAoB;UAAA,EAAC;UAIvCzD,EAAA,CAAAY,UAAA,KAAAiD,2CAAA,wBAAoE;UAK1E7D,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGNH,EAAA,CAAAY,UAAA,KAAAkD,qCAAA,mBAA+C;UAyBjD9D,EAAA,CAAAG,YAAA,EAAe;UAGbH,EADF,CAAAC,cAAA,0BAAoC,kBAMV;UAAtBD,EAAA,CAAAqD,UAAA,mBAAAU,wDAAA;YAAA,OAASX,GAAA,CAAAjB,SAAA,EAAW;UAAA,EAAC;UACrBnC,EAAA,CAAAW,SAAA,mBAAyD;UACzDX,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAGsB;UAApBD,EAAA,CAAAqD,UAAA,mBAAAW,wDAAA;YAAA,OAASZ,GAAA,CAAAb,OAAA,EAAS;UAAA,EAAC;UACnBvC,EAAA,CAAAW,SAAA,mBAAqD;UACrDX,EAAA,CAAAE,MAAA,sBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;UAvEAH,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAiE,gBAAA,aAAAb,GAAA,CAAA5B,YAAA,CAA2B;UAICxB,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,YAAAgD,GAAA,CAAA/B,WAAA,CAAc;UAS1CrB,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAiE,gBAAA,aAAAb,GAAA,CAAA3B,YAAA,CAA2B;UAG3BzB,EAAA,CAAAI,UAAA,cAAAgD,GAAA,CAAA5B,YAAA,IAAA4B,GAAA,CAAA1B,cAAA,CAAAwC,MAAA,OAAyD;UAE7BlE,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAgD,GAAA,CAAA1B,cAAA,CAAiB;UAQ5B1B,EAAA,CAAAO,SAAA,EAAsB;UAAtBP,EAAA,CAAAI,UAAA,SAAAgD,GAAA,CAAApC,gBAAA,CAAsB;UAgC3ChB,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAI,UAAA,cAAAgD,GAAA,CAAA5B,YAAA,KAAA4B,GAAA,CAAA3B,YAAA,CAA2C;;;qBAtFjDhC,YAAY,EAAA0E,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ3E,WAAW,EACXE,YAAY,EAAA0E,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EACZ/E,cAAc,EAAA2E,EAAA,CAAAK,iBAAA,EACd9E,cAAc,EAAAyE,EAAA,CAAAM,iBAAA,EAAAN,EAAA,CAAAO,iBAAA,EACd/E,cAAc,EACdC,YAAY,EAAAuE,EAAA,CAAAQ,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}