{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Zero padding strategy.\n   */\n  CryptoJS.pad.ZeroPadding = {\n    pad: function (data, blockSize) {\n      // Shortcut\n      var blockSizeBytes = blockSize * 4;\n\n      // Pad\n      data.clamp();\n      data.sigBytes += blockSizeBytes - (data.sigBytes % blockSizeBytes || blockSizeBytes);\n    },\n    unpad: function (data) {\n      // Shortcut\n      var dataWords = data.words;\n\n      // Unpad\n      var i = data.sigBytes - 1;\n      for (var i = data.sigBytes - 1; i >= 0; i--) {\n        if (dataWords[i >>> 2] >>> 24 - i % 4 * 8 & 0xff) {\n          data.sigBytes = i + 1;\n          break;\n        }\n      }\n    }\n  };\n  return CryptoJS.pad.ZeroPadding;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}