{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiRequirementBatchSaveDataPost$Json } from '../fn/requirement/api-requirement-batch-save-data-post-json';\nimport { apiRequirementBatchSaveDataPost$Plain } from '../fn/requirement/api-requirement-batch-save-data-post-plain';\nimport { apiRequirementDeleteDataPost$Json } from '../fn/requirement/api-requirement-delete-data-post-json';\nimport { apiRequirementDeleteDataPost$Plain } from '../fn/requirement/api-requirement-delete-data-post-plain';\nimport { apiRequirementGetDataPost$Json } from '../fn/requirement/api-requirement-get-data-post-json';\nimport { apiRequirementGetDataPost$Plain } from '../fn/requirement/api-requirement-get-data-post-plain';\nimport { apiRequirementGetListPost$Json } from '../fn/requirement/api-requirement-get-list-post-json';\nimport { apiRequirementGetListPost$Plain } from '../fn/requirement/api-requirement-get-list-post-plain';\nimport { apiRequirementSaveDataPost$Json } from '../fn/requirement/api-requirement-save-data-post-json';\nimport { apiRequirementSaveDataPost$Plain } from '../fn/requirement/api-requirement-save-data-post-plain';\nimport { apiRequirementSaveTemplayeDataPost$Json } from '../fn/requirement/api-requirement-save-templaye-data-post-json';\nimport { apiRequirementSaveTemplayeDataPost$Plain } from '../fn/requirement/api-requirement-save-templaye-data-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let RequirementService = /*#__PURE__*/(() => {\n  class RequirementService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiRequirementGetListPost()` */\n    static {\n      this.ApiRequirementGetListPostPath = '/api/Requirement/GetList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementGetListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementGetListPost$Plain$Response(params, context) {\n      return apiRequirementGetListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementGetListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementGetListPost$Plain(params, context) {\n      return this.apiRequirementGetListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementGetListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementGetListPost$Json$Response(params, context) {\n      return apiRequirementGetListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementGetListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementGetListPost$Json(params, context) {\n      return this.apiRequirementGetListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRequirementGetDataPost()` */\n    static {\n      this.ApiRequirementGetDataPostPath = '/api/Requirement/GetData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementGetDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementGetDataPost$Plain$Response(params, context) {\n      return apiRequirementGetDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementGetDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementGetDataPost$Plain(params, context) {\n      return this.apiRequirementGetDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementGetDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementGetDataPost$Json$Response(params, context) {\n      return apiRequirementGetDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementGetDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementGetDataPost$Json(params, context) {\n      return this.apiRequirementGetDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRequirementSaveDataPost()` */\n    static {\n      this.ApiRequirementSaveDataPostPath = '/api/Requirement/SaveData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementSaveDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementSaveDataPost$Plain$Response(params, context) {\n      return apiRequirementSaveDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementSaveDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementSaveDataPost$Plain(params, context) {\n      return this.apiRequirementSaveDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementSaveDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementSaveDataPost$Json$Response(params, context) {\n      return apiRequirementSaveDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementSaveDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementSaveDataPost$Json(params, context) {\n      return this.apiRequirementSaveDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRequirementBatchSaveDataPost()` */\n    static {\n      this.ApiRequirementBatchSaveDataPostPath = '/api/Requirement/BatchSaveData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementBatchSaveDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementBatchSaveDataPost$Plain$Response(params, context) {\n      return apiRequirementBatchSaveDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementBatchSaveDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementBatchSaveDataPost$Plain(params, context) {\n      return this.apiRequirementBatchSaveDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementBatchSaveDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementBatchSaveDataPost$Json$Response(params, context) {\n      return apiRequirementBatchSaveDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementBatchSaveDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementBatchSaveDataPost$Json(params, context) {\n      return this.apiRequirementBatchSaveDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRequirementDeleteDataPost()` */\n    static {\n      this.ApiRequirementDeleteDataPostPath = '/api/Requirement/DeleteData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementDeleteDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementDeleteDataPost$Plain$Response(params, context) {\n      return apiRequirementDeleteDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementDeleteDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementDeleteDataPost$Plain(params, context) {\n      return this.apiRequirementDeleteDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementDeleteDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementDeleteDataPost$Json$Response(params, context) {\n      return apiRequirementDeleteDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementDeleteDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementDeleteDataPost$Json(params, context) {\n      return this.apiRequirementDeleteDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRequirementSaveTemplayeDataPost()` */\n    static {\n      this.ApiRequirementSaveTemplayeDataPostPath = '/api/Requirement/SaveTemplayeData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementSaveTemplayeDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementSaveTemplayeDataPost$Plain$Response(params, context) {\n      return apiRequirementSaveTemplayeDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementSaveTemplayeDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementSaveTemplayeDataPost$Plain(params, context) {\n      return this.apiRequirementSaveTemplayeDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRequirementSaveTemplayeDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementSaveTemplayeDataPost$Json$Response(params, context) {\n      return apiRequirementSaveTemplayeDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRequirementSaveTemplayeDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRequirementSaveTemplayeDataPost$Json(params, context) {\n      return this.apiRequirementSaveTemplayeDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function RequirementService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RequirementService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RequirementService,\n        factory: RequirementService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RequirementService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}