{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"../../components/pagination/pagination.component\";\nconst _c0 = [\"createModal\"];\nconst _c1 = [\"editModal\"];\nconst _c2 = [\"templateDetailModal\"];\nfunction TemplateComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const createModal_r4 = i0.ɵɵreference(61);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(createModal_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_56_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_56_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const editModal_r9 = i0.ɵɵreference(63);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(editModal_r9, template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_56_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_56_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteTemplate(template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 44);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 35);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 36)(12, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_56_Template_button_click_12_listener() {\n      const template_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateDetailModal_r7 = i0.ɵɵreference(65);\n      return i0.ɵɵresetView(ctx_r2.viewTemplateDetail(template_r6, templateDetailModal_r7));\n    });\n    i0.ɵɵelement(13, \"i\", 38);\n    i0.ɵɵtext(14, \"\\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, TemplateComponent_tr_56_button_15_Template, 3, 0, \"button\", 39)(16, TemplateComponent_tr_56_button_16_Template, 3, 0, \"button\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const template_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(template_r6.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", template_r6.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 7, template_r6.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r6.CCreator || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction TemplateComponent_tr_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 45);\n    i0.ɵɵelement(2, \"i\", 46);\n    i0.ɵɵtext(3, \"\\u76EE\\u524D\\u6C92\\u6709\\u4EFB\\u4F55\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_60_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 90)(2, \"input\", 91);\n    i0.ɵɵlistener(\"change\", function TemplateComponent_ng_template_60_div_57_Template_input_change_2_listener() {\n      const space_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleSpaceSelection(space_r14));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 92);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementStart(5, \"small\", 93);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const space_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"space-\" + space_r14.CSpaceID)(\"checked\", space_r14.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"space-\" + space_r14.CSpaceID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r14.CPart, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r14.CLocation || \"-\");\n  }\n}\nfunction TemplateComponent_ng_template_60_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"\\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_60_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"ngx-pagination\", 32);\n    i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_ng_template_60_div_59_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spacePageIndex, $event) || (ctx_r2.spacePageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function TemplateComponent_ng_template_60_div_59_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.spacePageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r2.spacePageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r2.spacePageSize)(\"CollectionSize\", ctx_r2.spaceTotalRecords);\n  }\n}\nfunction TemplateComponent_ng_template_60_div_60_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_div_60_span_4_Template_button_click_2_listener() {\n      const space_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeSelectedSpace(space_r17));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r17.CPart, \" \");\n  }\n}\nfunction TemplateComponent_ng_template_60_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"label\", 97);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 98);\n    i0.ɵɵtemplate(4, TemplateComponent_ng_template_60_div_60_span_4_Template, 3, 1, \"span\", 99);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u7684\\u7A7A\\u9593 (\", ctx_r2.selectedSpacesForTemplate.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedSpacesForTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 47)(1, \"nb-card-header\", 48)(2, \"h5\", 49);\n    i0.ɵɵelement(3, \"i\", 50);\n    i0.ɵɵtext(4, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_Template_button_click_5_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(6, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 53)(8, \"div\", 54)(9, \"div\", 55)(10, \"div\", 56)(11, \"div\", 57)(12, \"label\", 58);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 59)(15, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_60_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown.control.enter\", function TemplateComponent_ng_template_60_Template_input_keydown_control_enter_15_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 55)(17, \"div\", 56)(18, \"div\", 57)(19, \"label\", 61);\n    i0.ɵɵtext(20, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 59)(22, \"nb-form-field\", 62)(23, \"nb-select\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_60_Template_nb_select_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(24, \"nb-option\", 16)(25, \"span\", 5);\n    i0.ɵɵelement(26, \"i\", 64);\n    i0.ɵɵtext(27, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"nb-option\", 16)(29, \"span\", 5);\n    i0.ɵɵelement(30, \"i\", 65);\n    i0.ɵɵtext(31, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(32, \"div\", 55)(33, \"div\", 56)(34, \"div\", 57)(35, \"label\", 66);\n    i0.ɵɵtext(36, \" \\u9078\\u64C7\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 59)(38, \"div\", 67)(39, \"div\", 68)(40, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_60_Template_input_ngModelChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchKeyword, $event) || (ctx_r2.spaceSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_60_Template_input_keyup_enter_40_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 68)(42, \"input\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_60_Template_input_ngModelChange_42_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchLocation, $event) || (ctx_r2.spaceSearchLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_60_Template_input_keyup_enter_42_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 71)(44, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceReset());\n    });\n    i0.ɵɵelement(45, \"i\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_Template_button_click_46_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelement(47, \"i\", 75);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"div\", 76)(49, \"div\", 77)(50, \"div\", 5)(51, \"input\", 78);\n    i0.ɵɵlistener(\"change\", function TemplateComponent_ng_template_60_Template_input_change_51_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"label\", 79);\n    i0.ɵɵtext(53, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"small\", 80);\n    i0.ɵɵtext(55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 54);\n    i0.ɵɵtemplate(57, TemplateComponent_ng_template_60_div_57_Template, 7, 5, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(58, TemplateComponent_ng_template_60_div_58_Template, 3, 0, \"div\", 82)(59, TemplateComponent_ng_template_60_div_59_Template, 2, 3, \"div\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(60, TemplateComponent_ng_template_60_div_60_Template, 5, 2, \"div\", 84);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(61, \"nb-card-footer\", 85)(62, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_Template_button_click_62_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(63, \"i\", 87);\n    i0.ɵɵtext(64, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_60_Template_button_click_65_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelement(66, \"i\", 89);\n    i0.ɵɵtext(67, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchLocation);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"checked\", ctx_r2.allSpacesSelected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx_r2.spaceTotalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx_r2.spacePageIndex, \" / \", ctx_r2.Math.ceil(ctx_r2.spaceTotalRecords / ctx_r2.spacePageSize), \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableSpaces);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableSpaces.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.spaceTotalRecords > ctx_r2.spacePageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedSpacesForTemplate.length > 0);\n  }\n}\nfunction TemplateComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 102)(1, \"nb-card-header\", 48)(2, \"h5\", 49);\n    i0.ɵɵelement(3, \"i\", 103);\n    i0.ɵɵtext(4, \"\\u7DE8\\u8F2F\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_Template_button_click_5_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r19));\n    });\n    i0.ɵɵelement(6, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 53)(8, \"div\", 54)(9, \"div\", 55)(10, \"div\", 56)(11, \"div\", 57)(12, \"label\", 104);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 59)(15, \"input\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_62_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 55)(17, \"div\", 56)(18, \"div\", 57)(19, \"label\", 106);\n    i0.ɵɵtext(20, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 59)(22, \"nb-form-field\", 62)(23, \"nb-select\", 107);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_62_Template_nb_select_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(24, \"nb-option\", 16)(25, \"span\", 5);\n    i0.ɵɵelement(26, \"i\", 64);\n    i0.ɵɵtext(27, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"nb-option\", 16)(29, \"span\", 5);\n    i0.ɵɵelement(30, \"i\", 65);\n    i0.ɵɵtext(31, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\", 85)(33, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_Template_button_click_33_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r19));\n    });\n    i0.ɵɵelement(34, \"i\", 87);\n    i0.ɵɵtext(35, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_Template_button_click_36_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r19));\n    });\n    i0.ɵɵelement(37, \"i\", 108);\n    i0.ɵɵtext(38, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction TemplateComponent_ng_template_64_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵelement(1, \"i\", 132);\n    i0.ɵɵelementStart(2, \"span\", 80);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_64_div_70_div_1_tr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"i\", 140);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵelement(7, \"i\", 141);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r22 = ctx.$implicit;\n    const i_r23 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r23 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r22.CPart, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r22.CLocation || \"-\", \" \");\n  }\n}\nfunction TemplateComponent_ng_template_64_div_70_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"table\", 136)(2, \"thead\")(3, \"tr\")(4, \"th\", 137);\n    i0.ɵɵtext(5, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 138);\n    i0.ɵɵtext(7, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 139);\n    i0.ɵɵtext(9, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"tbody\");\n    i0.ɵɵtemplate(11, TemplateComponent_ng_template_64_div_70_div_1_tr_11_Template, 9, 3, \"tr\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateDetailSpaces);\n  }\n}\nfunction TemplateComponent_ng_template_64_div_70_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"\\u6B64\\u6A21\\u677F\\u5C1A\\u672A\\u5305\\u542B\\u4EFB\\u4F55\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_64_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TemplateComponent_ng_template_64_div_70_div_1_Template, 12, 1, \"div\", 133)(2, TemplateComponent_ng_template_64_div_70_div_2_Template, 3, 0, \"div\", 134);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length === 0);\n  }\n}\nfunction TemplateComponent_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 47)(1, \"nb-card-header\", 48)(2, \"h5\", 49);\n    i0.ɵɵelement(3, \"i\", 109);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u660E\\u7D30 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_64_Template_button_click_5_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r20).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r21));\n    });\n    i0.ɵɵelement(6, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 53)(8, \"div\", 110)(9, \"div\", 111)(10, \"h6\", 112);\n    i0.ɵɵelement(11, \"i\", 113);\n    i0.ɵɵtext(12, \"\\u57FA\\u672C\\u8CC7\\u8A0A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 114)(14, \"div\", 54)(15, \"div\", 9)(16, \"div\", 115)(17, \"label\", 116);\n    i0.ɵɵelement(18, \"i\", 117);\n    i0.ɵɵtext(19, \"\\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 118);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 9)(23, \"div\", 115)(24, \"label\", 116);\n    i0.ɵɵelement(25, \"i\", 119);\n    i0.ɵɵtext(26, \"\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 118)(28, \"span\", 35);\n    i0.ɵɵelement(29, \"i\", 120);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 9)(32, \"div\", 115)(33, \"label\", 116);\n    i0.ɵɵelement(34, \"i\", 121);\n    i0.ɵɵtext(35, \"\\u5EFA\\u7ACB\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"p\", 118);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 9)(40, \"div\", 115)(41, \"label\", 116);\n    i0.ɵɵelement(42, \"i\", 122);\n    i0.ɵɵtext(43, \"\\u5EFA\\u7ACB\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 118);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 9)(47, \"div\", 115)(48, \"label\", 116);\n    i0.ɵɵelement(49, \"i\", 123);\n    i0.ɵɵtext(50, \"\\u66F4\\u65B0\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 118);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 115)(56, \"label\", 116);\n    i0.ɵɵelement(57, \"i\", 124);\n    i0.ɵɵtext(58, \"\\u66F4\\u65B0\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\", 118);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(61, \"div\", 125)(62, \"div\", 126)(63, \"h6\", 112);\n    i0.ɵɵelement(64, \"i\", 127);\n    i0.ɵɵtext(65, \"\\u5305\\u542B\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 128);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 114);\n    i0.ɵɵtemplate(69, TemplateComponent_ng_template_64_div_69_Template, 4, 0, \"div\", 129)(70, TemplateComponent_ng_template_64_div_70_Template, 3, 2, \"div\", 30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"nb-card-footer\", 85)(72, \"button\", 130);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_64_Template_button_click_72_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r20).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r21));\n    });\n    i0.ɵɵelement(73, \"i\", 87);\n    i0.ɵɵtext(74, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CTemplateName) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"fas fa-check-circle\" : \"fas fa-times-circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(38, 12, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(53, 15, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r2.templateDetailSpaces.length, \" \\u500B\\u7A7A\\u9593\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingTemplateDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoadingTemplateDetail);\n  }\n}\nexport let TemplateComponent = /*#__PURE__*/(() => {\n  class TemplateComponent extends BaseComponent {\n    constructor(allow, dialogService, _templateService, _spaceService, message, valid) {\n      super(allow);\n      this.allow = allow;\n      this.dialogService = dialogService;\n      this._templateService = _templateService;\n      this._spaceService = _spaceService;\n      this.message = message;\n      this.valid = valid;\n      this.Math = Math; // 讓模板可以使用 Math 函數\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      // 模板相關屬性\n      this.templateList = [];\n      this.templateDetail = {};\n      this.searchKeyword = '';\n      this.searchStatus = null;\n      // 空間選擇相關屬性\n      this.availableSpaces = [];\n      this.selectedSpacesForTemplate = [];\n      this.spaceSearchKeyword = '';\n      this.spaceSearchLocation = '';\n      this.spacePageIndex = 1;\n      this.spacePageSize = 10;\n      this.spaceTotalRecords = 0;\n      this.allSpacesSelected = false;\n      // 模板明細相關屬性\n      this.selectedTemplateDetail = null;\n      this.templateDetailSpaces = [];\n      this.isLoadingTemplateDetail = false;\n    }\n    ngOnInit() {\n      this.loadTemplateList();\n      this.loadAvailableSpaces();\n    }\n    // 載入模板列表\n    loadTemplateList() {\n      const request = {\n        CTemplateName: this.searchKeyword || null,\n        CStatus: this.searchStatus,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      };\n      this._templateService.apiTemplateGetTemplateListPost$Json({\n        body: request\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.templateList = response.Entries?.map(item => ({\n            CTemplateId: item.CTemplateId,\n            CTemplateName: item.CTemplateName,\n            CCreateDt: item.CCreateDt,\n            CUpdateDt: item.CUpdateDt,\n            CCreator: item.CCreator,\n            CUpdator: item.CUpdator,\n            CStatus: item.CStatus\n          })) || [];\n          this.totalRecords = response.TotalItems || 0;\n        } else {\n          this.message.showErrorMSG(response.Message || '載入模板列表失敗');\n        }\n      })).subscribe();\n    }\n    // 載入可用空間列表\n    loadAvailableSpaces() {\n      const request = {\n        CPart: this.spaceSearchKeyword || null,\n        CLocation: this.spaceSearchLocation || null,\n        CStatus: 1,\n        // 只顯示啟用的空間\n        PageIndex: this.spacePageIndex,\n        PageSize: this.spacePageSize\n      };\n      this._spaceService.apiSpaceGetSpaceListPost$Json({\n        body: request\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.availableSpaces = response.Entries?.map(item => ({\n            CSpaceID: item.CSpaceID,\n            CPart: item.CPart,\n            CLocation: item.CLocation,\n            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\n          })) || [];\n          this.spaceTotalRecords = response.TotalItems || 0;\n          this.updateAllSpacesSelectedState();\n        }\n      })).subscribe();\n    }\n    // 搜尋功能\n    onSearch() {\n      this.pageIndex = 1;\n      this.loadTemplateList();\n    }\n    onReset() {\n      this.searchKeyword = '';\n      this.searchStatus = null;\n      this.pageIndex = 1;\n      this.loadTemplateList();\n    }\n    // 空間搜尋功能\n    onSpaceSearch() {\n      this.spacePageIndex = 1;\n      this.loadAvailableSpaces();\n    }\n    onSpaceReset() {\n      this.spaceSearchKeyword = '';\n      this.spaceSearchLocation = '';\n      this.spacePageIndex = 1;\n      this.loadAvailableSpaces();\n    }\n    // 分頁功能\n    pageChanged(page) {\n      this.pageIndex = page;\n      this.loadTemplateList();\n    }\n    spacePageChanged(page) {\n      this.spacePageIndex = page;\n      this.loadAvailableSpaces();\n    }\n    // 模態框操作\n    openCreateModal(modal) {\n      this.templateDetail = {\n        CStatus: 1\n      };\n      this.selectedSpacesForTemplate = [];\n      this.loadAvailableSpaces();\n      this.dialogService.open(modal, {\n        context: {},\n        autoFocus: false\n      });\n    }\n    openEditModal(modal, template) {\n      this.templateDetail = {\n        CTemplateId: template.CTemplateId,\n        CTemplateName: template.CTemplateName,\n        CStatus: template.CStatus || 1\n      };\n      this.dialogService.open(modal, {\n        context: {},\n        autoFocus: false\n      });\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    onSubmit(ref) {\n      if (!this.validateTemplateForm()) {\n        return;\n      }\n      if (this.templateDetail.CTemplateId) {\n        this.updateTemplate(ref);\n      } else {\n        this.createTemplate(ref);\n      }\n    }\n    // 驗證表單\n    validateTemplateForm() {\n      if (!this.templateDetail.CTemplateName?.trim()) {\n        this.message.showErrorMSG('請輸入模板名稱');\n        return false;\n      }\n      if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\n        this.message.showErrorMSG('請選擇模板狀態');\n        return false;\n      }\n      if (!this.templateDetail.CTemplateId && this.selectedSpacesForTemplate.length === 0) {\n        this.message.showErrorMSG('請至少選擇一個空間');\n        return false;\n      }\n      return true;\n    }\n    // 建立模板\n    createTemplate(ref) {\n      const templateData = {\n        CTemplateName: this.templateDetail.CTemplateName,\n        CStatus: this.templateDetail.CStatus\n      };\n      this._templateService.apiTemplateSaveTemplatePost$Json({\n        body: templateData\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          const templateId = parseInt(response.Entries, 10);\n          this.saveTemplateDetails(templateId, ref);\n        } else {\n          this.message.showErrorMSG(response.Message || '建立模板失敗');\n        }\n      })).subscribe();\n    }\n    // 更新模板\n    updateTemplate(ref) {\n      const templateData = {\n        CTemplateId: this.templateDetail.CTemplateId,\n        CTemplateName: this.templateDetail.CTemplateName,\n        CStatus: this.templateDetail.CStatus\n      };\n      this._templateService.apiTemplateSaveTemplatePost$Json({\n        body: templateData\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('更新模板成功');\n          ref.close();\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '更新模板失敗');\n        }\n      })).subscribe();\n    }\n    // 儲存模板詳細資料（關聯空間）\n    saveTemplateDetails(templateId, ref) {\n      // 目前 API 可能不支援模板詳細資料的保存，暫時跳過這個步驟\n      this.message.showSucessMSG('建立模板成功');\n      ref.close();\n      this.loadTemplateList();\n    }\n    // 刪除模板\n    deleteTemplate(template) {\n      if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n        this._templateService.apiTemplateDeleteTemplatePost$Json({\n          body: {\n            CTemplateId: template.CTemplateId\n          }\n        }).pipe(tap(response => {\n          if (response.StatusCode === 0) {\n            this.message.showSucessMSG('刪除模板成功');\n            this.loadTemplateList();\n          } else {\n            this.message.showErrorMSG(response.Message || '刪除模板失敗');\n          }\n        })).subscribe();\n      }\n    }\n    // 查看模板明細\n    viewTemplateDetail(template, modal) {\n      this.selectedTemplateDetail = template;\n      this.isLoadingTemplateDetail = true;\n      this.templateDetailSpaces = [];\n      this.dialogService.open(modal, {\n        context: {},\n        autoFocus: false\n      });\n      const request = {\n        templateId: template.CTemplateId\n      };\n      this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n        body: request\n      }).pipe(tap(response => {\n        this.isLoadingTemplateDetail = false;\n        if (response.StatusCode === 0) {\n          this.templateDetailSpaces = response.Entries?.map(item => ({\n            CReleateId: item.CReleateId,\n            CPart: item.CPart,\n            CLocation: item.CLocation\n          })) || [];\n        } else {\n          this.message.showErrorMSG(response.Message || '載入模板明細失敗');\n        }\n      })).subscribe();\n    }\n    // 空間選擇相關方法\n    toggleSpaceSelection(space) {\n      space.selected = !space.selected;\n      if (space.selected) {\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else {\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n      this.updateAllSpacesSelectedState();\n    }\n    toggleAllSpaces() {\n      this.allSpacesSelected = !this.allSpacesSelected;\n      this.availableSpaces.forEach(space => {\n        space.selected = this.allSpacesSelected;\n        if (this.allSpacesSelected) {\n          if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n            this.selectedSpacesForTemplate.push({\n              ...space\n            });\n          }\n        } else {\n          this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n        }\n      });\n    }\n    removeSelectedSpace(space) {\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n      if (availableSpace) {\n        availableSpace.selected = false;\n      }\n      this.updateAllSpacesSelectedState();\n    }\n    updateAllSpacesSelectedState() {\n      this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n    }\n    static {\n      this.ɵfac = function TemplateComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TemplateComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TemplateComponent,\n        selectors: [[\"ngx-template\"]],\n        viewQuery: function TemplateComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n            i0.ɵɵviewQuery(_c2, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.createModal = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editModal = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateDetailModal = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 66,\n        vars: 11,\n        consts: [[\"createModal\", \"\"], [\"editModal\", \"\"], [\"templateDetailModal\", \"\"], [\"accent\", \"success\"], [1, \"alert\", \"alert-info\", \"mb-4\", 2, \"border-left\", \"4px solid #4a90e2\", \"background-color\", \"#f8f9ff\", \"border-radius\", \"6px\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-3\", 2, \"font-size\", \"1.2rem\"], [1, \"mb-0\", \"text-muted\", 2, \"font-size\", \"0.9rem\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"templateName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"col-3\"], [\"id\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"800px\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", 1, \"col-2\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"badge\", 3, \"ngClass\"], [1, \"table-actions\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-outline-warning btn-sm me-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-warning\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"5\", 1, \"text-muted\", \"py-4\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"border-bottom\", \"py-3\", \"px-4\"], [1, \"mb-0\", \"text-primary\", \"font-weight-bold\"], [1, \"fas\", \"fa-plus-circle\", \"me-2\", \"text-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-light\", \"btn-sm\", \"rounded-circle\", 2, \"width\", \"32px\", \"height\", \"32px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"px-4\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"form-group\", \"mb-4\"], [1, \"d-flex\", \"align-items-start\"], [\"for\", \"templateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"flex-grow-1\", \"ml-3\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"templateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"keydown.control.enter\", \"ngModel\"], [\"for\", \"templateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"w-full\"], [\"id\", \"templateStatus\", \"name\", \"templateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\", \"me-2\"], [1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"row\", \"mb-3\"], [1, \"col-md-5\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"col-md-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"border\", \"rounded\", \"p-3\", 2, \"max-height\", \"300px\", \"overflow-y\", \"auto\", \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"selectAll\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAll\", 1, \"mb-0\", \"font-weight-bold\"], [1, \"text-muted\"], [\"class\", \"col-md-6\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-3\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center mt-3\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\", \"border-top\", \"pt-3\", \"px-4\", \"pb-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"btn\", \"btn-outline-secondary\", \"me-3\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", \"background\", \"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"id\", \"checked\"], [1, \"form-check-label\", 3, \"for\"], [1, \"text-muted\", \"d-block\"], [1, \"text-center\", \"text-muted\", \"py-3\"], [1, \"d-flex\", \"justify-content-center\", \"mt-3\"], [1, \"mt-3\"], [1, \"mb-2\", \"font-weight-bold\"], [1, \"border\", \"rounded\", \"p-2\", 2, \"max-height\", \"150px\", \"overflow-y\", \"auto\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", 1, \"btn-close\", \"btn-close-white\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"fas\", \"fa-edit\", \"me-2\", \"text-warning\"], [\"for\", \"editTemplateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"editTemplateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"editTemplateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"editTemplateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"id\", \"editTemplateStatus\", \"name\", \"editTemplateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-save\", \"me-1\"], [1, \"fas\", \"fa-eye\", \"me-2\", \"text-info\"], [1, \"card\", \"mb-4\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"mb-0\", \"text-dark\", \"font-weight-bold\"], [1, \"fas\", \"fa-info-circle\", \"me-2\", \"text-primary\"], [1, \"card-body\"], [1, \"form-group\", \"mb-3\"], [1, \"font-weight-bold\", \"text-muted\"], [1, \"fas\", \"fa-tag\", \"me-2\", \"text-primary\"], [1, \"mb-0\"], [1, \"fas\", \"fa-toggle-on\", \"me-2\", \"text-success\"], [1, \"me-1\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-plus\", \"me-2\", \"text-warning\"], [1, \"fas\", \"fa-calendar-edit\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-edit\", \"me-2\", \"text-warning\"], [1, \"card\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-success\"], [1, \"badge\", \"badge-info\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"text-center\", \"py-4\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"me-2\", \"text-primary\", 2, \"font-size\", \"1.2rem\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-7\"], [\"scope\", \"col\", 1, \"col-4\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\", \"text-muted\"], [1, \"text-center\", \"text-muted\", \"py-4\"]],\n        template: function TemplateComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"i\", 6);\n            i0.ɵɵelementStart(7, \"div\")(8, \"p\", 7);\n            i0.ɵɵtext(9, \" \\u5728\\u6B64\\u9801\\u9762\\u60A8\\u53EF\\u4EE5\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u7684\\u5404\\u500B\\u6A21\\u677F\\u8CC7\\u8A0A\\uFF0C\\u5305\\u62EC\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u6A21\\u677F\\uFF0C\\u4EE5\\u53CA\\u8A2D\\u5B9A\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u72C0\\u614B\\u548C\\u5305\\u542B\\u7684\\u7A7A\\u9593\\u7B49\\u3002 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"label\", 11);\n            i0.ɵɵtext(14, \"\\u6A21\\u677F\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"nb-form-field\", 12)(16, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_input_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_Template_input_keyup_enter_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10)(19, \"label\", 14);\n            i0.ɵɵtext(20, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"nb-form-field\", 12)(22, \"nb-select\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_22_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchStatus, $event) || (ctx.searchStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_22_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelementStart(23, \"nb-option\", 16);\n            i0.ɵɵtext(24, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"nb-option\", 16);\n            i0.ɵɵtext(26, \"\\u555F\\u7528\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"nb-option\", 16);\n            i0.ɵɵtext(28, \"\\u505C\\u7528\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelement(29, \"div\", 9);\n            i0.ɵɵelementStart(30, \"div\", 17)(31, \"div\", 18)(32, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_32_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onReset());\n            });\n            i0.ɵɵelement(33, \"i\", 20);\n            i0.ɵɵtext(34, \"\\u91CD\\u7F6E \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"button\", 21);\n            i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_35_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵelement(36, \"i\", 22);\n            i0.ɵɵtext(37, \"\\u67E5\\u8A62 \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(38, \"div\", 17)(39, \"div\", 23);\n            i0.ɵɵtemplate(40, TemplateComponent_button_40_Template, 3, 0, \"button\", 24);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(41, \"div\", 25)(42, \"table\", 26)(43, \"thead\")(44, \"tr\")(45, \"th\", 27);\n            i0.ɵɵtext(46, \"\\u6A21\\u677F\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"th\", 28);\n            i0.ɵɵtext(48, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"th\", 27);\n            i0.ɵɵtext(50, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"th\", 28);\n            i0.ɵɵtext(52, \"\\u5EFA\\u7ACB\\u8005\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"th\", 28);\n            i0.ɵɵtext(54, \"\\u64CD\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(55, \"tbody\");\n            i0.ɵɵtemplate(56, TemplateComponent_tr_56_Template, 17, 10, \"tr\", 29)(57, TemplateComponent_tr_57_Template, 4, 0, \"tr\", 30);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(58, \"nb-card-footer\", 31)(59, \"ngx-pagination\", 32);\n            i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_59_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_59_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(60, TemplateComponent_ng_template_60_Template, 68, 14, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(62, TemplateComponent_ng_template_62_Template, 39, 4, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(64, TemplateComponent_ng_template_64_Template, 75, 18, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 0);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngForOf\", ctx.templateList);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.templateList.length === 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, i6.DatePipe, SharedModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i8.BreadcrumbComponent, i9.PaginationComponent],\n        styles: [\".btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-right:.25rem}.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child{margin-right:0}.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-top-left-radius:0;border-bottom-left-radius:0}nb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{color:var(--color-fg-heading)}.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-right:.25rem}.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child{margin-right:0}.badge.badge-success[_ngcontent-%COMP%]{background-color:#28a745;color:#fff}.badge.badge-secondary[_ngcontent-%COMP%]{background-color:#6c757d;color:#fff}.badge.badge-info[_ngcontent-%COMP%]{background-color:#17a2b8;color:#fff}.badge.badge-primary[_ngcontent-%COMP%]{background-color:#007bff;color:#fff}.required-field[_ngcontent-%COMP%]:after{content:\\\" *\\\";color:#dc3545}.alert[_ngcontent-%COMP%]{border-radius:.375rem}.modal-content[_ngcontent-%COMP%]{border-radius:.5rem;box-shadow:0 .5rem 1rem #00000026}.form-check-input[_ngcontent-%COMP%]:checked{background-color:#007bff;border-color:#007bff}.btn-close-white[_ngcontent-%COMP%]{filter:invert(1) grayscale(100%) brightness(200%)}\"]\n      });\n    }\n  }\n  return TemplateComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}