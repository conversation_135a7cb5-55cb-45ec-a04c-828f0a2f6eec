{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Russian [ru]\n//! author : Viktorminator : https://github.com/Viktorminator\n//! author : <PERSON><PERSON><PERSON> : https://github.com/Oire\n//! author : Коренберг Марк : https://github.com/socketpair\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function plural(word, num) {\n    var forms = word.split('_');\n    return num % 10 === 1 && num % 100 !== 11 ? forms[0] : num % 10 >= 2 && num % 10 <= 4 && (num % 100 < 10 || num % 100 >= 20) ? forms[1] : forms[2];\n  }\n  function relativeTimeWithPlural(number, withoutSuffix, key) {\n    var format = {\n      ss: withoutSuffix ? 'секунда_секунды_секунд' : 'секунду_секунды_секунд',\n      mm: withoutSuffix ? 'минута_минуты_минут' : 'минуту_минуты_минут',\n      hh: 'час_часа_часов',\n      dd: 'день_дня_дней',\n      ww: 'неделя_недели_недель',\n      MM: 'месяц_месяца_месяцев',\n      yy: 'год_года_лет'\n    };\n    if (key === 'm') {\n      return withoutSuffix ? 'минута' : 'минуту';\n    } else {\n      return number + ' ' + plural(format[key], +number);\n    }\n  }\n  var monthsParse = [/^янв/i, /^фев/i, /^мар/i, /^апр/i, /^ма[йя]/i, /^июн/i, /^июл/i, /^авг/i, /^сен/i, /^окт/i, /^ноя/i, /^дек/i];\n\n  // http://new.gramota.ru/spravka/rules/139-prop : § 103\n  // Сокращения месяцев: http://new.gramota.ru/spravka/buro/search-answer?s=242637\n  // CLDR data:          http://www.unicode.org/cldr/charts/28/summary/ru.html#1753\n  var ru = moment.defineLocale('ru', {\n    months: {\n      format: 'января_февраля_марта_апреля_мая_июня_июля_августа_сентября_октября_ноября_декабря'.split('_'),\n      standalone: 'январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь'.split('_')\n    },\n    monthsShort: {\n      // по CLDR именно \"июл.\" и \"июн.\", но какой смысл менять букву на точку?\n      format: 'янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.'.split('_'),\n      standalone: 'янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.'.split('_')\n    },\n    weekdays: {\n      standalone: 'воскресенье_понедельник_вторник_среда_четверг_пятница_суббота'.split('_'),\n      format: 'воскресенье_понедельник_вторник_среду_четверг_пятницу_субботу'.split('_'),\n      isFormat: /\\[ ?[Вв] ?(?:прошлую|следующую|эту)? ?] ?dddd/\n    },\n    weekdaysShort: 'вс_пн_вт_ср_чт_пт_сб'.split('_'),\n    weekdaysMin: 'вс_пн_вт_ср_чт_пт_сб'.split('_'),\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: monthsParse,\n    // полные названия с падежами, по три буквы, для некоторых, по 4 буквы, сокращения с точкой и без точки\n    monthsRegex: /^(январ[ья]|янв\\.?|феврал[ья]|февр?\\.?|марта?|мар\\.?|апрел[ья]|апр\\.?|ма[йя]|июн[ья]|июн\\.?|июл[ья]|июл\\.?|августа?|авг\\.?|сентябр[ья]|сент?\\.?|октябр[ья]|окт\\.?|ноябр[ья]|нояб?\\.?|декабр[ья]|дек\\.?)/i,\n    // копия предыдущего\n    monthsShortRegex: /^(январ[ья]|янв\\.?|феврал[ья]|февр?\\.?|марта?|мар\\.?|апрел[ья]|апр\\.?|ма[йя]|июн[ья]|июн\\.?|июл[ья]|июл\\.?|августа?|авг\\.?|сентябр[ья]|сент?\\.?|октябр[ья]|окт\\.?|ноябр[ья]|нояб?\\.?|декабр[ья]|дек\\.?)/i,\n    // полные названия с падежами\n    monthsStrictRegex: /^(январ[яь]|феврал[яь]|марта?|апрел[яь]|ма[яй]|июн[яь]|июл[яь]|августа?|сентябр[яь]|октябр[яь]|ноябр[яь]|декабр[яь])/i,\n    // Выражение, которое соответствует только сокращённым формам\n    monthsShortStrictRegex: /^(янв\\.|февр?\\.|мар[т.]|апр\\.|ма[яй]|июн[ья.]|июл[ья.]|авг\\.|сент?\\.|окт\\.|нояб?\\.|дек\\.)/i,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY г.',\n      LLL: 'D MMMM YYYY г., H:mm',\n      LLLL: 'dddd, D MMMM YYYY г., H:mm'\n    },\n    calendar: {\n      sameDay: '[Сегодня, в] LT',\n      nextDay: '[Завтра, в] LT',\n      lastDay: '[Вчера, в] LT',\n      nextWeek: function (now) {\n        if (now.week() !== this.week()) {\n          switch (this.day()) {\n            case 0:\n              return '[В следующее] dddd, [в] LT';\n            case 1:\n            case 2:\n            case 4:\n              return '[В следующий] dddd, [в] LT';\n            case 3:\n            case 5:\n            case 6:\n              return '[В следующую] dddd, [в] LT';\n          }\n        } else {\n          if (this.day() === 2) {\n            return '[Во] dddd, [в] LT';\n          } else {\n            return '[В] dddd, [в] LT';\n          }\n        }\n      },\n      lastWeek: function (now) {\n        if (now.week() !== this.week()) {\n          switch (this.day()) {\n            case 0:\n              return '[В прошлое] dddd, [в] LT';\n            case 1:\n            case 2:\n            case 4:\n              return '[В прошлый] dddd, [в] LT';\n            case 3:\n            case 5:\n            case 6:\n              return '[В прошлую] dddd, [в] LT';\n          }\n        } else {\n          if (this.day() === 2) {\n            return '[Во] dddd, [в] LT';\n          } else {\n            return '[В] dddd, [в] LT';\n          }\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'через %s',\n      past: '%s назад',\n      s: 'несколько секунд',\n      ss: relativeTimeWithPlural,\n      m: relativeTimeWithPlural,\n      mm: relativeTimeWithPlural,\n      h: 'час',\n      hh: relativeTimeWithPlural,\n      d: 'день',\n      dd: relativeTimeWithPlural,\n      w: 'неделя',\n      ww: relativeTimeWithPlural,\n      M: 'месяц',\n      MM: relativeTimeWithPlural,\n      y: 'год',\n      yy: relativeTimeWithPlural\n    },\n    meridiemParse: /ночи|утра|дня|вечера/i,\n    isPM: function (input) {\n      return /^(дня|вечера)$/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'ночи';\n      } else if (hour < 12) {\n        return 'утра';\n      } else if (hour < 17) {\n        return 'дня';\n      } else {\n        return 'вечера';\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(й|го|я)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'M':\n        case 'd':\n        case 'DDD':\n          return number + '-й';\n        case 'D':\n          return number + '-го';\n        case 'w':\n        case 'W':\n          return number + '-я';\n        default:\n          return number;\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return ru;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}