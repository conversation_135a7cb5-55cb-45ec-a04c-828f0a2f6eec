{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { RequirementTemplateSelectorComponent } from '../requirement-template-selector/requirement-template-selector.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"@angular/common\";\nfunction RequirementTemplateSelectorButtonComponent_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.icon);\n  }\n}\nexport let RequirementTemplateSelectorButtonComponent = /*#__PURE__*/(() => {\n  class RequirementTemplateSelectorButtonComponent {\n    constructor(dialogService) {\n      this.dialogService = dialogService;\n      this.buildCaseId = '';\n      this.text = '選擇需求項目';\n      this.icon = 'fas fa-list';\n      this.buttonClass = 'btn btn-primary';\n      this.disabled = false;\n      this.multiple = true;\n      this.preSelectedItems = [];\n      this.selectionConfirmed = new EventEmitter();\n      this.selectionCancelled = new EventEmitter();\n      this.error = new EventEmitter();\n    }\n    openSelector() {\n      if (this.disabled) {\n        return;\n      }\n      if (!this.buildCaseId) {\n        this.error.emit('請先選擇建案');\n        return;\n      }\n      const dialogRef = this.dialogService.open(RequirementTemplateSelectorComponent, {\n        context: {\n          buildCaseId: parseInt(this.buildCaseId),\n          multiple: this.multiple,\n          preSelectedItems: this.preSelectedItems\n        },\n        autoFocus: false,\n        closeOnBackdropClick: false,\n        closeOnEsc: true\n      });\n      // 監聽選擇確認事件\n      dialogRef.componentRef.instance.selectionConfirmed.subscribe(config => {\n        this.selectionConfirmed.emit(config);\n      });\n      // 監聽取消事件\n      dialogRef.componentRef.instance.selectionCancelled.subscribe(() => {\n        this.selectionCancelled.emit();\n      });\n    }\n    static {\n      this.ɵfac = function RequirementTemplateSelectorButtonComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RequirementTemplateSelectorButtonComponent)(i0.ɵɵdirectiveInject(i1.NbDialogService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RequirementTemplateSelectorButtonComponent,\n        selectors: [[\"app-requirement-template-selector-button\"]],\n        inputs: {\n          buildCaseId: \"buildCaseId\",\n          text: \"text\",\n          icon: \"icon\",\n          buttonClass: \"buttonClass\",\n          disabled: \"disabled\",\n          multiple: \"multiple\",\n          preSelectedItems: \"preSelectedItems\"\n        },\n        outputs: {\n          selectionConfirmed: \"selectionConfirmed\",\n          selectionCancelled: \"selectionCancelled\",\n          error: \"error\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 5,\n        consts: [[3, \"click\", \"disabled\"], [3, \"class\", 4, \"ngIf\"]],\n        template: function RequirementTemplateSelectorButtonComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"button\", 0);\n            i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorButtonComponent_Template_button_click_0_listener() {\n              return ctx.openSelector();\n            });\n            i0.ɵɵtemplate(1, RequirementTemplateSelectorButtonComponent_i_1_Template, 1, 2, \"i\", 1);\n            i0.ɵɵtext(2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassMap(ctx.buttonClass);\n            i0.ɵɵproperty(\"disabled\", ctx.disabled);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.icon);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.text, \" \");\n          }\n        },\n        dependencies: [CommonModule, i2.NgIf, NbButtonModule, NbIconModule],\n        encapsulation: 2\n      });\n    }\n  }\n  return RequirementTemplateSelectorButtonComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}