/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { apiFileGetFileGet } from '../fn/file/api-file-get-file-get';
import { ApiFileGetFileGet$Params } from '../fn/file/api-file-get-file-get';

@Injectable({ providedIn: 'root' })
export class FileService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `apiFileGetFileGet()` */
  static readonly ApiFileGetFileGetPath = '/api/File/GetFile';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `apiFileGetFileGet()` instead.
   *
   * This method doesn't expect any request body.
   */
  apiFileGetFileGet$Response(params?: ApiFileGetFileGet$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return apiFileGetFileGet(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `apiFileGetFileGet$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  apiFileGetFileGet(params?: ApiFileGetFileGet$Params, context?: HttpContext): Observable<void> {
    return this.apiFileGetFileGet$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /**
   * 自定義方法：取得檔案並返回 Blob
   * 這個方法正確處理檔案下載，返回 Observable<Blob>
   *
   * @param relativePath 相對路徑
   * @param fileName 檔案名稱
   * @param context HTTP 上下文
   * @returns Observable<Blob>
   */
  getFile(relativePath: string, fileName: string, context?: HttpContext): Observable<Blob> {
    // 直接使用 HttpClient 發送請求，設置正確的 responseType
    const url = `${this.rootUrl}${FileService.ApiFileGetFileGetPath}`;
    const httpParams = new URLSearchParams();
    if (relativePath) {
      httpParams.set('relativePath', relativePath);
    }
    if (fileName) {
      httpParams.set('fileName', fileName);
    }

    return this.http.get(`${url}?${httpParams.toString()}`, {
      responseType: 'blob',
      context
    });
  }

}
