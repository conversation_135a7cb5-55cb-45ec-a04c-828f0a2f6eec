{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nexport { EnumQuotationItemType } from './models/enum-quotation-item-type';\nexport { EnumStatusCode } from './models/enum-status-code';", "map": {"version": 3, "names": ["EnumQuotationItemType", "EnumStatusCode"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\models.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nexport type { AddHouseHoldMain } from './models/add-house-hold-main';\r\nexport type { ApproveRecord } from './models/approve-record';\r\nexport type { ApproveWaitingArgs } from './models/approve-waiting-args';\r\nexport type { ApproveWaitingByIdArgs } from './models/approve-waiting-by-id-args';\r\nexport type { ApproveWaitingByIdRes } from './models/approve-waiting-by-id-res';\r\nexport type { ApproveWaitingByIdResResponseBase } from './models/approve-waiting-by-id-res-response-base';\r\nexport type { ApproveWaitingRes } from './models/approve-waiting-res';\r\nexport type { ApproveWaitingResListResponseBase } from './models/approve-waiting-res-list-response-base';\r\nexport type { BooleanResponseBase } from './models/boolean-response-base';\r\nexport type { BuildCaseFileRes } from './models/build-case-file-res';\r\nexport type { BuildCaseFileResListResponseBase } from './models/build-case-file-res-list-response-base';\r\nexport type { BuildCaseFileResResponseBase } from './models/build-case-file-res-response-base';\r\nexport type { BuildCaseGetFileArgs } from './models/build-case-get-file-args';\r\nexport type { BuildCaseGetFileRespone } from './models/build-case-get-file-respone';\r\nexport type { BuildCaseGetFileResponeListResponseBase } from './models/build-case-get-file-respone-list-response-base';\r\nexport type { BuildCaseGetListReponse } from './models/build-case-get-list-reponse';\r\nexport type { BuildCaseGetListReponseListResponseBase } from './models/build-case-get-list-reponse-list-response-base';\r\nexport type { BuildCaseGetListReponseResponseBase } from './models/build-case-get-list-reponse-response-base';\r\nexport type { BuildingSample } from './models/building-sample';\r\nexport type { ByteArrayResponseBase } from './models/byte-array-response-base';\r\nexport type { CancelChangePreOrder } from './models/cancel-change-pre-order';\r\nexport type { ChangePasswordRequest } from './models/change-password-request';\r\nexport type { CheckOtpRequest } from './models/check-otp-request';\r\nexport type { CHouse } from './models/c-house';\r\nexport type { CreateAppointmentArgs } from './models/create-appointment-args';\r\nexport type { CreateFinalDocArgs } from './models/create-final-doc-args';\r\nexport type { CreateListFormItem } from './models/create-list-form-item';\r\nexport type { DeleteBuildCaseArgs } from './models/delete-build-case-args';\r\nexport type { DeleteRegularPic } from './models/delete-regular-pic';\r\nexport type { DeleteRequirementRequest } from './models/delete-requirement-request';\r\nexport type { EditAppointmentArgs } from './models/edit-appointment-args';\r\nexport type { EditHouseArgs } from './models/edit-house-args';\r\nexport type { EditHouseInfo } from './models/edit-house-info';\r\nexport type { EditHouseRegularPicture } from './models/edit-house-regular-picture';\r\nexport type { EditHouseRegularPictureArgs } from './models/edit-house-regular-picture-args';\r\nexport type { EditListHouseArgs } from './models/edit-list-house-args';\r\nexport type { EnumArgs } from './models/enum-args';\r\nexport { EnumQuotationItemType } from './models/enum-quotation-item-type';\r\nexport type { EnumResponse } from './models/enum-response';\r\nexport type { EnumResponseListResponseBase } from './models/enum-response-list-response-base';\r\nexport { EnumStatusCode } from './models/enum-status-code';\r\nexport type { ExportExcelMaterials } from './models/export-excel-materials';\r\nexport type { ExportExcelMaterialsResponseBase } from './models/export-excel-materials-response-base';\r\nexport type { ExportExcelResponse } from './models/export-excel-response';\r\nexport type { ExportExcelResponseResponseBase } from './models/export-excel-response-response-base';\r\nexport type { FileApprove } from './models/file-approve';\r\nexport type { FileRes } from './models/file-res';\r\nexport type { FileViewModel } from './models/file-view-model';\r\nexport type { FloorRange } from './models/floor-range';\r\nexport type { FormItems } from './models/form-items';\r\nexport type { FunctionDto } from './models/function-dto';\r\nexport type { GetAllBuildCaseArgs } from './models/get-all-build-case-args';\r\nexport type { GetAppoinmentArgs } from './models/get-appoinment-args';\r\nexport type { GetAppoinmentRes } from './models/get-appoinment-res';\r\nexport type { GetAppoinmentResListResponseBase } from './models/get-appoinment-res-list-response-base';\r\nexport type { GetAppoinmentResResponseBase } from './models/get-appoinment-res-response-base';\r\nexport type { GetBuildCaseById } from './models/get-build-case-by-id';\r\nexport type { GetBuildCaseFileById } from './models/get-build-case-file-by-id';\r\nexport type { GetBuildCaseMailListRequest } from './models/get-build-case-mail-list-request';\r\nexport type { GetBuildCaseMailListResponse } from './models/get-build-case-mail-list-response';\r\nexport type { GetBuildCaseMailListResponseListResponseBase } from './models/get-build-case-mail-list-response-list-response-base';\r\nexport type { GetBuildingSampleSelectionRes } from './models/get-building-sample-selection-res';\r\nexport type { GetBuildingSampleSelectionResResponseBase } from './models/get-building-sample-selection-res-response-base';\r\nexport type { GetChangePreOrderArgs } from './models/get-change-pre-order-args';\r\nexport type { GetChangePreOrderRespone } from './models/get-change-pre-order-respone';\r\nexport type { GetChangePreOrderResponeResponseBase } from './models/get-change-pre-order-respone-response-base';\r\nexport type { GetDisclaimerArgs } from './models/get-disclaimer-args';\r\nexport type { GetFinalDocAfter } from './models/get-final-doc-after';\r\nexport type { GetFinalDocBefore } from './models/get-final-doc-before';\r\nexport type { GetFinalDocListByHouse } from './models/get-final-doc-list-by-house';\r\nexport type { GetFinalDocRes } from './models/get-final-doc-res';\r\nexport type { GetFinalDocResListResponseBase } from './models/get-final-doc-res-list-response-base';\r\nexport type { GetHourListAppointmentReq } from './models/get-hour-list-appointment-req';\r\nexport type { GetHourListRespone } from './models/get-hour-list-respone';\r\nexport type { GetHourListResponeListResponseBase } from './models/get-hour-list-respone-list-response-base';\r\nexport type { GetHouseAndFloorByBuildCaseIdRes } from './models/get-house-and-floor-by-build-case-id-res';\r\nexport type { GetHouseAndFloorByBuildCaseIdResListResponseBase } from './models/get-house-and-floor-by-build-case-id-res-list-response-base';\r\nexport type { GetHouseByIdArgs } from './models/get-house-by-id-args';\r\nexport type { GetHouseChangeDateReq } from './models/get-house-change-date-req';\r\nexport type { GetHouseChangeDateRes } from './models/get-house-change-date-res';\r\nexport type { GetHouseChangeDateResListResponseBase } from './models/get-house-change-date-res-list-response-base';\r\nexport type { GetHouseListArgs } from './models/get-house-list-args';\r\nexport type { GetHouseListRes } from './models/get-house-list-res';\r\nexport type { GetHouseListResListResponseBase } from './models/get-house-list-res-list-response-base';\r\nexport type { GetHouseProgress } from './models/get-house-progress';\r\nexport type { GetHouseProgressResponseBase } from './models/get-house-progress-response-base';\r\nexport type { GetHouseReview } from './models/get-house-review';\r\nexport type { GetHouseReviewListResponseBase } from './models/get-house-review-list-response-base';\r\nexport type { GetListBuildCaseFileArgs } from './models/get-list-build-case-file-args';\r\nexport type { GetListBuildingArgs } from './models/get-list-building-args';\r\nexport type { GetListByHouseIdRequest } from './models/get-list-by-house-id-request';\r\nexport type { GetListFinalDocArgs } from './models/get-list-final-doc-args';\r\nexport type { GetListFinalDocRes } from './models/get-list-final-doc-res';\r\nexport type { GetListFinalDocResListResponseBase } from './models/get-list-final-doc-res-list-response-base';\r\nexport type { GetListFormItemReq } from './models/get-list-form-item-req';\r\nexport type { GetListFormItemRes } from './models/get-list-form-item-res';\r\nexport type { GetListFormItemResResponseBase } from './models/get-list-form-item-res-response-base';\r\nexport type { GetListHouseHoldArgs } from './models/get-list-house-hold-args';\r\nexport type { GetListHouseHoldRes } from './models/get-list-house-hold-res';\r\nexport type { GetListHouseHoldResListResponseBase } from './models/get-list-house-hold-res-list-response-base';\r\nexport type { GetListHouseRegularPicArgs } from './models/get-list-house-regular-pic-args';\r\nexport type { GetListHouseRegularPicRes } from './models/get-list-house-regular-pic-res';\r\nexport type { GetListHouseRegularPicResListResponseBase } from './models/get-list-house-regular-pic-res-list-response-base';\r\nexport type { GetListQuotationRequest } from './models/get-list-quotation-request';\r\nexport type { GetListRegularChangeItemRes } from './models/get-list-regular-change-item-res';\r\nexport type { GetListRegularChangeItemResListResponseBase } from './models/get-list-regular-change-item-res-list-response-base';\r\nexport type { GetListRequirementRequest } from './models/get-list-requirement-request';\r\nexport type { GetListSpecialChangeRequest } from './models/get-list-special-change-request';\r\nexport type { GetMaterialListRequest } from './models/get-material-list-request';\r\nexport type { GetMaterialListResponse } from './models/get-material-list-response';\r\nexport type { GetMaterialListResponseListResponseBase } from './models/get-material-list-response-list-response-base';\r\nexport type { GetMenuArgs } from './models/get-menu-args';\r\nexport type { GetMenuResponse } from './models/get-menu-response';\r\nexport type { GetMenuResponseResponseBase } from './models/get-menu-response-response-base';\r\nexport type { GetMilestoneRes } from './models/get-milestone-res';\r\nexport type { GetMilestoneResResponseBase } from './models/get-milestone-res-response-base';\r\nexport type { GetPayStatus } from './models/get-pay-status';\r\nexport type { GetPayStatusResponseBase } from './models/get-pay-status-response-base';\r\nexport type { GetPictureListRequest } from './models/get-picture-list-request';\r\nexport type { GetPictureListResponse } from './models/get-picture-list-response';\r\nexport type { GetPictureListResponseListResponseBase } from './models/get-picture-list-response-list-response-base';\r\nexport type { GetPreOrderSettingArgs } from './models/get-pre-order-setting-args';\r\nexport type { GetPreOrderSettingResponse } from './models/get-pre-order-setting-response';\r\nexport type { GetPreOrderSettingResponseListResponseBase } from './models/get-pre-order-setting-response-list-response-base';\r\nexport type { GetQuotation } from './models/get-quotation';\r\nexport type { GetQuotationByIdRequest } from './models/get-quotation-by-id-request';\r\nexport type { GetQuotationListResponseBase } from './models/get-quotation-list-response-base';\r\nexport type { GetQuotationResponseBase } from './models/get-quotation-response-base';\r\nexport type { GetQuotationVersions } from './models/get-quotation-versions';\r\nexport type { GetQuotationVersionsListResponseBase } from './models/get-quotation-versions-list-response-base';\r\nexport type { GetRegularChangeDetailByItemIdRes } from './models/get-regular-change-detail-by-item-id-res';\r\nexport type { GetRegularChangeDetailByItemIdResResponseBase } from './models/get-regular-change-detail-by-item-id-res-response-base';\r\nexport type { GetRegularNoticeFileByIdRes } from './models/get-regular-notice-file-by-id-res';\r\nexport type { GetRegularNoticeFileByIdResResponseBase } from './models/get-regular-notice-file-by-id-res-response-base';\r\nexport type { GetRegularNoticeFileListReq } from './models/get-regular-notice-file-list-req';\r\nexport type { GetRegularNoticeFileListRes } from './models/get-regular-notice-file-list-res';\r\nexport type { GetRegularNoticeFileListResResponseBase } from './models/get-regular-notice-file-list-res-response-base';\r\nexport type { GetRequirement } from './models/get-requirement';\r\nexport type { GetRequirementByIdRequest } from './models/get-requirement-by-id-request';\r\nexport type { GetRequirementListResponseBase } from './models/get-requirement-list-response-base';\r\nexport type { GetRequirementResponseBase } from './models/get-requirement-response-base';\r\nexport type { GetReviewByIdRes } from './models/get-review-by-id-res';\r\nexport type { GetReviewByIdResResponseBase } from './models/get-review-by-id-res-response-base';\r\nexport type { GetReviewListReq } from './models/get-review-list-req';\r\nexport type { GetReviewListRes } from './models/get-review-list-res';\r\nexport type { GetReviewListResListResponseBase } from './models/get-review-list-res-list-response-base';\r\nexport type { GetSpecialChangeFileArgs } from './models/get-special-change-file-args';\r\nexport type { GetSpecialNoticeFileByIdRes } from './models/get-special-notice-file-by-id-res';\r\nexport type { GetSpecialNoticeFileByIdResResponseBase } from './models/get-special-notice-file-by-id-res-response-base';\r\nexport type { GetSpecialNoticeFileListReq } from './models/get-special-notice-file-list-req';\r\nexport type { GetSpecialNoticeFileListRes } from './models/get-special-notice-file-list-res';\r\nexport type { GetSpecialNoticeFileListResResponseBase } from './models/get-special-notice-file-list-res-response-base';\r\nexport type { GetSumaryRegularChangeItemRes } from './models/get-sumary-regular-change-item-res';\r\nexport type { GetSumaryRegularChangeItemResListResponseBase } from './models/get-sumary-regular-change-item-res-list-response-base';\r\nexport type { GetTemplateByIdArgs } from './models/get-template-by-id-args';\r\nexport type { GetTemplateDetailByIdArgs } from './models/get-template-detail-by-id-args';\r\nexport type { GetUserBuildCaseArgs } from './models/get-user-build-case-args';\r\nexport type { HouseChangePreOrderArgs } from './models/house-change-pre-order-args';\r\nexport type { HouseDropDownItem } from './models/house-drop-down-item';\r\nexport type { HouseGetChangeDateArgs } from './models/house-get-change-date-args';\r\nexport type { HouseGetChangeDateRespone } from './models/house-get-change-date-respone';\r\nexport type { HouseGetChangeDateResponeResponseBase } from './models/house-get-change-date-respone-response-base';\r\nexport type { HouseGetHourListArgs } from './models/house-get-hour-list-args';\r\nexport type { HouseLoginRequest } from './models/house-login-request';\r\nexport type { HouseLoginResponse } from './models/house-login-response';\r\nexport type { HouseLoginResponseResponseBase } from './models/house-login-response-response-base';\r\nexport type { HouseLoginStep2Request } from './models/house-login-step-2-request';\r\nexport type { HouseRegularPic } from './models/house-regular-pic';\r\nexport type { HouseRegularPicResponseBase } from './models/house-regular-pic-response-base';\r\nexport type { HouseRequirement } from './models/house-requirement';\r\nexport type { HouseRequirementRes } from './models/house-requirement-res';\r\nexport type { HouseRequirementResListResponseBase } from './models/house-requirement-res-list-response-base';\r\nexport type { HouseRes } from './models/house-res';\r\nexport type { HouseReview } from './models/house-review';\r\nexport type { HouseSpecialNoticeFile } from './models/house-special-notice-file';\r\nexport type { LoadDefaultItemsRequest } from './models/load-default-items-request';\r\nexport type { LockFormItemReq } from './models/lock-form-item-req';\r\nexport type { PictureInfo } from './models/picture-info';\r\nexport type { PreOrderSetting } from './models/pre-order-setting';\r\nexport type { QuotationItemModel } from './models/quotation-item-model';\r\nexport type { RegularChangeDetail } from './models/regular-change-detail';\r\nexport type { RegularDetail } from './models/regular-detail';\r\nexport type { RegularNoticeFileList } from './models/regular-notice-file-list';\r\nexport type { RegularRemark } from './models/regular-remark';\r\nexport type { RegularRemarkArgs } from './models/regular-remark-args';\r\nexport type { ReviewHouseHold } from './models/review-house-hold';\r\nexport type { SaveBuildCaseArgs } from './models/save-build-case-args';\r\nexport type { SaveBuildCaseMailRequest } from './models/save-build-case-mail-request';\r\nexport type { SaveDataQuotation } from './models/save-data-quotation';\r\nexport type { SaveDataQuotationResponseBase } from './models/save-data-quotation-response-base';\r\nexport type { SaveDataRequirement } from './models/save-data-requirement';\r\nexport type { SaveHouseChangeDateReq } from './models/save-house-change-date-req';\r\nexport type { SaveListFormItemReq } from './models/save-list-form-item-req';\r\nexport type { SaveMaterialArgs } from './models/save-material-args';\r\nexport type { SavePreOrderSetting } from './models/save-pre-order-setting';\r\nexport type { SaveRegularChangeDetailRequest } from './models/save-regular-change-detail-request';\r\nexport type { SaveTemplateArgs } from './models/save-template-args';\r\nexport type { SaveTemplateDetailArgs } from './models/save-template-detail-args';\r\nexport type { SpecialChangeAvailableArgs } from './models/special-change-available-args';\r\nexport type { SpecialChangeAvailableRes } from './models/special-change-available-res';\r\nexport type { SpecialChangeAvailableResListResponseBase } from './models/special-change-available-res-list-response-base';\r\nexport type { SpecialChangeFile } from './models/special-change-file';\r\nexport type { SpecialChangeFileGroup } from './models/special-change-file-group';\r\nexport type { SpecialChangeFileGroupListResponseBase } from './models/special-change-file-group-list-response-base';\r\nexport type { SpecialChangeFileRespone } from './models/special-change-file-respone';\r\nexport type { SpecialChangeRes } from './models/special-change-res';\r\nexport type { SpecialChangeResListResponseBase } from './models/special-change-res-list-response-base';\r\nexport type { SpecialChangeResResponseBase } from './models/special-change-res-response-base';\r\nexport type { SpecialNoticeFileList } from './models/special-notice-file-list';\r\nexport type { StringHouseDropDownItemListDictionaryResponseBase } from './models/string-house-drop-down-item-list-dictionary-response-base';\r\nexport type { StringListResponseBase } from './models/string-list-response-base';\r\nexport type { StringResponseBase } from './models/string-response-base';\r\nexport type { TblExamineLog } from './models/tbl-examine-log';\r\nexport type { TblFinalDocument } from './models/tbl-final-document';\r\nexport type { TblFinalDocumentListResponseBase } from './models/tbl-final-document-list-response-base';\r\nexport type { TblFormItemHousehold } from './models/tbl-form-item-household';\r\nexport type { TblHouse } from './models/tbl-house';\r\nexport type { TblHouseResponseBase } from './models/tbl-house-response-base';\r\nexport type { TblPicture } from './models/tbl-picture';\r\nexport type { TblQuotationItem } from './models/tbl-quotation-item';\r\nexport type { TblRegularNoticeFile } from './models/tbl-regular-notice-file';\r\nexport type { TblRegularNoticeFileHouse } from './models/tbl-regular-notice-file-house';\r\nexport type { TblRegularNoticeFileResponseBase } from './models/tbl-regular-notice-file-response-base';\r\nexport type { TblReview } from './models/tbl-review';\r\nexport type { TblSpecialNoticeFile } from './models/tbl-special-notice-file';\r\nexport type { TblSpecialNoticeFileResponseBase } from './models/tbl-special-notice-file-response-base';\r\nexport type { TemplateDetailItem } from './models/template-detail-item';\r\nexport type { TemplateDetailItemListResponseBase } from './models/template-detail-item-list-response-base';\r\nexport type { TemplateGetListArgs } from './models/template-get-list-args';\r\nexport type { TemplateGetListResponse } from './models/template-get-list-response';\r\nexport type { TemplateGetListResponseListResponseBase } from './models/template-get-list-response-list-response-base';\r\nexport type { TemplateGetListResponseResponseBase } from './models/template-get-list-response-response-base';\r\nexport type { UnlockFormItem } from './models/unlock-form-item';\r\nexport type { UpdateApproveWaiting } from './models/update-approve-waiting';\r\nexport type { UpdateHouseRequirementArgs } from './models/update-house-requirement-args';\r\nexport type { UpdateHouseReviewArgs } from './models/update-house-review-args';\r\nexport type { UpdateSignArgs } from './models/update-sign-args';\r\nexport type { UploadFileResponse } from './models/upload-file-response';\r\nexport type { UploadFileResponseResponseBase } from './models/upload-file-response-response-base';\r\nexport type { UploadRegularPic } from './models/upload-regular-pic';\r\nexport type { UploadSpecialChangeFile } from './models/upload-special-change-file';\r\nexport type { UserBuildCase } from './models/user-build-case';\r\nexport type { UserGetDataArgs } from './models/user-get-data-args';\r\nexport type { UserGetDataResponse } from './models/user-get-data-response';\r\nexport type { UserGetDataResponseResponseBase } from './models/user-get-data-response-response-base';\r\nexport type { UserGetListArgs } from './models/user-get-list-args';\r\nexport type { UserGetListResponse } from './models/user-get-list-response';\r\nexport type { UserGetListResponseListResponseBase } from './models/user-get-list-response-list-response-base';\r\nexport type { UserGetUserLogArgs } from './models/user-get-user-log-args';\r\nexport type { UserGetUserLogResponse } from './models/user-get-user-log-response';\r\nexport type { UserGetUserLogResponseListResponseBase } from './models/user-get-user-log-response-list-response-base';\r\nexport type { UserGroupGetDataArgs } from './models/user-group-get-data-args';\r\nexport type { UserGroupGetDataAuthority } from './models/user-group-get-data-authority';\r\nexport type { UserGroupGetDataFunctionLv1 } from './models/user-group-get-data-function-lv-1';\r\nexport type { UserGroupGetDataFunctionLv2 } from './models/user-group-get-data-function-lv-2';\r\nexport type { UserGroupGetDataResponse } from './models/user-group-get-data-response';\r\nexport type { UserGroupGetDataResponseResponseBase } from './models/user-group-get-data-response-response-base';\r\nexport type { UserGroupGetListArgs } from './models/user-group-get-list-args';\r\nexport type { UserGroupGetListResponse } from './models/user-group-get-list-response';\r\nexport type { UserGroupGetListResponseListResponseBase } from './models/user-group-get-list-response-list-response-base';\r\nexport type { UserGroupRemoveDataArgs } from './models/user-group-remove-data-args';\r\nexport type { UserGroupRemoveDataResponse } from './models/user-group-remove-data-response';\r\nexport type { UserGroupRemoveDataResponseResponseBase } from './models/user-group-remove-data-response-response-base';\r\nexport type { UserGroupSaveDataArgs } from './models/user-group-save-data-args';\r\nexport type { UserGroupSaveDataResponse } from './models/user-group-save-data-response';\r\nexport type { UserGroupSaveDataResponseResponseBase } from './models/user-group-save-data-response-response-base';\r\nexport type { UserLoginRequest } from './models/user-login-request';\r\nexport type { UserRemoveDataArgs } from './models/user-remove-data-args';\r\nexport type { UserRemoveDataResponse } from './models/user-remove-data-response';\r\nexport type { UserRemoveDataResponseResponseBase } from './models/user-remove-data-response-response-base';\r\nexport type { UserSaveDataArgs } from './models/user-save-data-args';\r\nexport type { UserSaveDataResponse } from './models/user-save-data-response';\r\nexport type { UserSaveDataResponseResponseBase } from './models/user-save-data-response-response-base';\r\n"], "mappings": "AAAA;AACA;AACA;AAuCA,SAASA,qBAAqB,QAAQ,mCAAmC;AAGzE,SAASC,cAAc,QAAQ,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}