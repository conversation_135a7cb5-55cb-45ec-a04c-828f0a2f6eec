{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport { each, extend, isArray } from 'zrender/lib/core/util.js';\nimport { modifyHSL, modifyAlpha } from 'zrender/lib/tool/color.js';\nimport { makeInner } from '../../util/model.js';\nvar ITEM_STYLE_NORMAL = 'itemStyle';\nvar inner = makeInner();\nexport default {\n  seriesType: 'treemap',\n  reset: function (seriesModel) {\n    var tree = seriesModel.getData().tree;\n    var root = tree.root;\n    if (root.isRemoved()) {\n      return;\n    }\n    travelTree(root,\n    // Visual should calculate from tree root but not view root.\n    {}, seriesModel.getViewRoot().getAncestors(), seriesModel);\n  }\n};\nfunction travelTree(node, designatedVisual, viewRootAncestors, seriesModel) {\n  var nodeModel = node.getModel();\n  var nodeLayout = node.getLayout();\n  var data = node.hostTree.data;\n  // Optimize\n  if (!nodeLayout || nodeLayout.invisible || !nodeLayout.isInView) {\n    return;\n  }\n  var nodeItemStyleModel = nodeModel.getModel(ITEM_STYLE_NORMAL);\n  var visuals = buildVisuals(nodeItemStyleModel, designatedVisual, seriesModel);\n  var existsStyle = data.ensureUniqueItemVisual(node.dataIndex, 'style');\n  // calculate border color\n  var borderColor = nodeItemStyleModel.get('borderColor');\n  var borderColorSaturation = nodeItemStyleModel.get('borderColorSaturation');\n  var thisNodeColor;\n  if (borderColorSaturation != null) {\n    // For performance, do not always execute 'calculateColor'.\n    thisNodeColor = calculateColor(visuals);\n    borderColor = calculateBorderColor(borderColorSaturation, thisNodeColor);\n  }\n  existsStyle.stroke = borderColor;\n  var viewChildren = node.viewChildren;\n  if (!viewChildren || !viewChildren.length) {\n    thisNodeColor = calculateColor(visuals);\n    // Apply visual to this node.\n    existsStyle.fill = thisNodeColor;\n  } else {\n    var mapping_1 = buildVisualMapping(node, nodeModel, nodeLayout, nodeItemStyleModel, visuals, viewChildren);\n    // Designate visual to children.\n    each(viewChildren, function (child, index) {\n      // If higher than viewRoot, only ancestors of viewRoot is needed to visit.\n      if (child.depth >= viewRootAncestors.length || child === viewRootAncestors[child.depth]) {\n        var childVisual = mapVisual(nodeModel, visuals, child, index, mapping_1, seriesModel);\n        travelTree(child, childVisual, viewRootAncestors, seriesModel);\n      }\n    });\n  }\n}\nfunction buildVisuals(nodeItemStyleModel, designatedVisual, seriesModel) {\n  var visuals = extend({}, designatedVisual);\n  var designatedVisualItemStyle = seriesModel.designatedVisualItemStyle;\n  each(['color', 'colorAlpha', 'colorSaturation'], function (visualName) {\n    // Priority: thisNode > thisLevel > parentNodeDesignated > seriesModel\n    designatedVisualItemStyle[visualName] = designatedVisual[visualName];\n    var val = nodeItemStyleModel.get(visualName);\n    designatedVisualItemStyle[visualName] = null;\n    val != null && (visuals[visualName] = val);\n  });\n  return visuals;\n}\nfunction calculateColor(visuals) {\n  var color = getValueVisualDefine(visuals, 'color');\n  if (color) {\n    var colorAlpha = getValueVisualDefine(visuals, 'colorAlpha');\n    var colorSaturation = getValueVisualDefine(visuals, 'colorSaturation');\n    if (colorSaturation) {\n      color = modifyHSL(color, null, null, colorSaturation);\n    }\n    if (colorAlpha) {\n      color = modifyAlpha(color, colorAlpha);\n    }\n    return color;\n  }\n}\nfunction calculateBorderColor(borderColorSaturation, thisNodeColor) {\n  return thisNodeColor != null\n  // Can only be string\n  ? modifyHSL(thisNodeColor, null, null, borderColorSaturation) : null;\n}\nfunction getValueVisualDefine(visuals, name) {\n  var value = visuals[name];\n  if (value != null && value !== 'none') {\n    return value;\n  }\n}\nfunction buildVisualMapping(node, nodeModel, nodeLayout, nodeItemStyleModel, visuals, viewChildren) {\n  if (!viewChildren || !viewChildren.length) {\n    return;\n  }\n  var rangeVisual = getRangeVisual(nodeModel, 'color') || visuals.color != null && visuals.color !== 'none' && (getRangeVisual(nodeModel, 'colorAlpha') || getRangeVisual(nodeModel, 'colorSaturation'));\n  if (!rangeVisual) {\n    return;\n  }\n  var visualMin = nodeModel.get('visualMin');\n  var visualMax = nodeModel.get('visualMax');\n  var dataExtent = nodeLayout.dataExtent.slice();\n  visualMin != null && visualMin < dataExtent[0] && (dataExtent[0] = visualMin);\n  visualMax != null && visualMax > dataExtent[1] && (dataExtent[1] = visualMax);\n  var colorMappingBy = nodeModel.get('colorMappingBy');\n  var opt = {\n    type: rangeVisual.name,\n    dataExtent: dataExtent,\n    visual: rangeVisual.range\n  };\n  if (opt.type === 'color' && (colorMappingBy === 'index' || colorMappingBy === 'id')) {\n    opt.mappingMethod = 'category';\n    opt.loop = true;\n    // categories is ordinal, so do not set opt.categories.\n  } else {\n    opt.mappingMethod = 'linear';\n  }\n  var mapping = new VisualMapping(opt);\n  inner(mapping).drColorMappingBy = colorMappingBy;\n  return mapping;\n}\n// Notice: If we don't have the attribute 'colorRange', but only use\n// attribute 'color' to represent both concepts of 'colorRange' and 'color',\n// (It means 'colorRange' when 'color' is Array, means 'color' when not array),\n// this problem will be encountered:\n// If a level-1 node doesn't have children, and its siblings have children,\n// and colorRange is set on level-1, then the node cannot be colored.\n// So we separate 'colorRange' and 'color' to different attributes.\nfunction getRangeVisual(nodeModel, name) {\n  // 'colorRange', 'colorARange', 'colorSRange'.\n  // If not exists on this node, fetch from levels and series.\n  var range = nodeModel.get(name);\n  return isArray(range) && range.length ? {\n    name: name,\n    range: range\n  } : null;\n}\nfunction mapVisual(nodeModel, visuals, child, index, mapping, seriesModel) {\n  var childVisuals = extend({}, visuals);\n  if (mapping) {\n    // Only support color, colorAlpha, colorSaturation.\n    var mappingType = mapping.type;\n    var colorMappingBy = mappingType === 'color' && inner(mapping).drColorMappingBy;\n    var value = colorMappingBy === 'index' ? index : colorMappingBy === 'id' ? seriesModel.mapIdToIndex(child.getId()) : child.getValue(nodeModel.get('visualDimension'));\n    childVisuals[mappingType] = mapping.mapValueToVisual(value);\n  }\n  return childVisuals;\n}", "map": {"version": 3, "names": ["VisualMapping", "each", "extend", "isArray", "modifyHSL", "modifyAlpha", "makeInner", "ITEM_STYLE_NORMAL", "inner", "seriesType", "reset", "seriesModel", "tree", "getData", "root", "isRemoved", "travelTree", "getViewRoot", "getAncestors", "node", "designatedVisual", "viewRootAncestors", "nodeModel", "getModel", "nodeLayout", "getLayout", "data", "hostTree", "invisible", "isInView", "nodeItemStyleModel", "visuals", "buildVisuals", "existsStyle", "ensureUniqueItemVisual", "dataIndex", "borderColor", "get", "borderColorSaturation", "thisNodeColor", "calculateColor", "calculateBorderColor", "stroke", "viewChil<PERSON>n", "length", "fill", "mapping_1", "buildVisualMapping", "child", "index", "depth", "childVisual", "mapVisual", "designatedVisualItemStyle", "visualName", "val", "color", "getValueVisualDefine", "colorAlpha", "colorSaturation", "name", "value", "rangeVisual", "getRangeVisual", "visualMin", "visualMax", "dataExtent", "slice", "colorMappingBy", "opt", "type", "visual", "range", "mappingMethod", "loop", "mapping", "drColorMappingBy", "childVisuals", "mappingType", "mapIdToIndex", "getId", "getValue", "mapValueToVisual"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/treemap/treemapVisual.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport { each, extend, isArray } from 'zrender/lib/core/util.js';\nimport { modifyHSL, modifyAlpha } from 'zrender/lib/tool/color.js';\nimport { makeInner } from '../../util/model.js';\nvar ITEM_STYLE_NORMAL = 'itemStyle';\nvar inner = makeInner();\nexport default {\n  seriesType: 'treemap',\n  reset: function (seriesModel) {\n    var tree = seriesModel.getData().tree;\n    var root = tree.root;\n    if (root.isRemoved()) {\n      return;\n    }\n    travelTree(root,\n    // Visual should calculate from tree root but not view root.\n    {}, seriesModel.getViewRoot().getAncestors(), seriesModel);\n  }\n};\nfunction travelTree(node, designatedVisual, viewRootAncestors, seriesModel) {\n  var nodeModel = node.getModel();\n  var nodeLayout = node.getLayout();\n  var data = node.hostTree.data;\n  // Optimize\n  if (!nodeLayout || nodeLayout.invisible || !nodeLayout.isInView) {\n    return;\n  }\n  var nodeItemStyleModel = nodeModel.getModel(ITEM_STYLE_NORMAL);\n  var visuals = buildVisuals(nodeItemStyleModel, designatedVisual, seriesModel);\n  var existsStyle = data.ensureUniqueItemVisual(node.dataIndex, 'style');\n  // calculate border color\n  var borderColor = nodeItemStyleModel.get('borderColor');\n  var borderColorSaturation = nodeItemStyleModel.get('borderColorSaturation');\n  var thisNodeColor;\n  if (borderColorSaturation != null) {\n    // For performance, do not always execute 'calculateColor'.\n    thisNodeColor = calculateColor(visuals);\n    borderColor = calculateBorderColor(borderColorSaturation, thisNodeColor);\n  }\n  existsStyle.stroke = borderColor;\n  var viewChildren = node.viewChildren;\n  if (!viewChildren || !viewChildren.length) {\n    thisNodeColor = calculateColor(visuals);\n    // Apply visual to this node.\n    existsStyle.fill = thisNodeColor;\n  } else {\n    var mapping_1 = buildVisualMapping(node, nodeModel, nodeLayout, nodeItemStyleModel, visuals, viewChildren);\n    // Designate visual to children.\n    each(viewChildren, function (child, index) {\n      // If higher than viewRoot, only ancestors of viewRoot is needed to visit.\n      if (child.depth >= viewRootAncestors.length || child === viewRootAncestors[child.depth]) {\n        var childVisual = mapVisual(nodeModel, visuals, child, index, mapping_1, seriesModel);\n        travelTree(child, childVisual, viewRootAncestors, seriesModel);\n      }\n    });\n  }\n}\nfunction buildVisuals(nodeItemStyleModel, designatedVisual, seriesModel) {\n  var visuals = extend({}, designatedVisual);\n  var designatedVisualItemStyle = seriesModel.designatedVisualItemStyle;\n  each(['color', 'colorAlpha', 'colorSaturation'], function (visualName) {\n    // Priority: thisNode > thisLevel > parentNodeDesignated > seriesModel\n    designatedVisualItemStyle[visualName] = designatedVisual[visualName];\n    var val = nodeItemStyleModel.get(visualName);\n    designatedVisualItemStyle[visualName] = null;\n    val != null && (visuals[visualName] = val);\n  });\n  return visuals;\n}\nfunction calculateColor(visuals) {\n  var color = getValueVisualDefine(visuals, 'color');\n  if (color) {\n    var colorAlpha = getValueVisualDefine(visuals, 'colorAlpha');\n    var colorSaturation = getValueVisualDefine(visuals, 'colorSaturation');\n    if (colorSaturation) {\n      color = modifyHSL(color, null, null, colorSaturation);\n    }\n    if (colorAlpha) {\n      color = modifyAlpha(color, colorAlpha);\n    }\n    return color;\n  }\n}\nfunction calculateBorderColor(borderColorSaturation, thisNodeColor) {\n  return thisNodeColor != null\n  // Can only be string\n  ? modifyHSL(thisNodeColor, null, null, borderColorSaturation) : null;\n}\nfunction getValueVisualDefine(visuals, name) {\n  var value = visuals[name];\n  if (value != null && value !== 'none') {\n    return value;\n  }\n}\nfunction buildVisualMapping(node, nodeModel, nodeLayout, nodeItemStyleModel, visuals, viewChildren) {\n  if (!viewChildren || !viewChildren.length) {\n    return;\n  }\n  var rangeVisual = getRangeVisual(nodeModel, 'color') || visuals.color != null && visuals.color !== 'none' && (getRangeVisual(nodeModel, 'colorAlpha') || getRangeVisual(nodeModel, 'colorSaturation'));\n  if (!rangeVisual) {\n    return;\n  }\n  var visualMin = nodeModel.get('visualMin');\n  var visualMax = nodeModel.get('visualMax');\n  var dataExtent = nodeLayout.dataExtent.slice();\n  visualMin != null && visualMin < dataExtent[0] && (dataExtent[0] = visualMin);\n  visualMax != null && visualMax > dataExtent[1] && (dataExtent[1] = visualMax);\n  var colorMappingBy = nodeModel.get('colorMappingBy');\n  var opt = {\n    type: rangeVisual.name,\n    dataExtent: dataExtent,\n    visual: rangeVisual.range\n  };\n  if (opt.type === 'color' && (colorMappingBy === 'index' || colorMappingBy === 'id')) {\n    opt.mappingMethod = 'category';\n    opt.loop = true;\n    // categories is ordinal, so do not set opt.categories.\n  } else {\n    opt.mappingMethod = 'linear';\n  }\n  var mapping = new VisualMapping(opt);\n  inner(mapping).drColorMappingBy = colorMappingBy;\n  return mapping;\n}\n// Notice: If we don't have the attribute 'colorRange', but only use\n// attribute 'color' to represent both concepts of 'colorRange' and 'color',\n// (It means 'colorRange' when 'color' is Array, means 'color' when not array),\n// this problem will be encountered:\n// If a level-1 node doesn't have children, and its siblings have children,\n// and colorRange is set on level-1, then the node cannot be colored.\n// So we separate 'colorRange' and 'color' to different attributes.\nfunction getRangeVisual(nodeModel, name) {\n  // 'colorRange', 'colorARange', 'colorSRange'.\n  // If not exists on this node, fetch from levels and series.\n  var range = nodeModel.get(name);\n  return isArray(range) && range.length ? {\n    name: name,\n    range: range\n  } : null;\n}\nfunction mapVisual(nodeModel, visuals, child, index, mapping, seriesModel) {\n  var childVisuals = extend({}, visuals);\n  if (mapping) {\n    // Only support color, colorAlpha, colorSaturation.\n    var mappingType = mapping.type;\n    var colorMappingBy = mappingType === 'color' && inner(mapping).drColorMappingBy;\n    var value = colorMappingBy === 'index' ? index : colorMappingBy === 'id' ? seriesModel.mapIdToIndex(child.getId()) : child.getValue(nodeModel.get('visualDimension'));\n    childVisuals[mappingType] = mapping.mapValueToVisual(value);\n  }\n  return childVisuals;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,aAAa,MAAM,+BAA+B;AACzD,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,0BAA0B;AAChE,SAASC,SAAS,EAAEC,WAAW,QAAQ,2BAA2B;AAClE,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,iBAAiB,GAAG,WAAW;AACnC,IAAIC,KAAK,GAAGF,SAAS,CAAC,CAAC;AACvB,eAAe;EACbG,UAAU,EAAE,SAAS;EACrBC,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAE;IAC5B,IAAIC,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC,CAACD,IAAI;IACrC,IAAIE,IAAI,GAAGF,IAAI,CAACE,IAAI;IACpB,IAAIA,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;MACpB;IACF;IACAC,UAAU,CAACF,IAAI;IACf;IACA,CAAC,CAAC,EAAEH,WAAW,CAACM,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,EAAEP,WAAW,CAAC;EAC5D;AACF,CAAC;AACD,SAASK,UAAUA,CAACG,IAAI,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEV,WAAW,EAAE;EAC1E,IAAIW,SAAS,GAAGH,IAAI,CAACI,QAAQ,CAAC,CAAC;EAC/B,IAAIC,UAAU,GAAGL,IAAI,CAACM,SAAS,CAAC,CAAC;EACjC,IAAIC,IAAI,GAAGP,IAAI,CAACQ,QAAQ,CAACD,IAAI;EAC7B;EACA,IAAI,CAACF,UAAU,IAAIA,UAAU,CAACI,SAAS,IAAI,CAACJ,UAAU,CAACK,QAAQ,EAAE;IAC/D;EACF;EACA,IAAIC,kBAAkB,GAAGR,SAAS,CAACC,QAAQ,CAAChB,iBAAiB,CAAC;EAC9D,IAAIwB,OAAO,GAAGC,YAAY,CAACF,kBAAkB,EAAEV,gBAAgB,EAAET,WAAW,CAAC;EAC7E,IAAIsB,WAAW,GAAGP,IAAI,CAACQ,sBAAsB,CAACf,IAAI,CAACgB,SAAS,EAAE,OAAO,CAAC;EACtE;EACA,IAAIC,WAAW,GAAGN,kBAAkB,CAACO,GAAG,CAAC,aAAa,CAAC;EACvD,IAAIC,qBAAqB,GAAGR,kBAAkB,CAACO,GAAG,CAAC,uBAAuB,CAAC;EAC3E,IAAIE,aAAa;EACjB,IAAID,qBAAqB,IAAI,IAAI,EAAE;IACjC;IACAC,aAAa,GAAGC,cAAc,CAACT,OAAO,CAAC;IACvCK,WAAW,GAAGK,oBAAoB,CAACH,qBAAqB,EAAEC,aAAa,CAAC;EAC1E;EACAN,WAAW,CAACS,MAAM,GAAGN,WAAW;EAChC,IAAIO,YAAY,GAAGxB,IAAI,CAACwB,YAAY;EACpC,IAAI,CAACA,YAAY,IAAI,CAACA,YAAY,CAACC,MAAM,EAAE;IACzCL,aAAa,GAAGC,cAAc,CAACT,OAAO,CAAC;IACvC;IACAE,WAAW,CAACY,IAAI,GAAGN,aAAa;EAClC,CAAC,MAAM;IACL,IAAIO,SAAS,GAAGC,kBAAkB,CAAC5B,IAAI,EAAEG,SAAS,EAAEE,UAAU,EAAEM,kBAAkB,EAAEC,OAAO,EAAEY,YAAY,CAAC;IAC1G;IACA1C,IAAI,CAAC0C,YAAY,EAAE,UAAUK,KAAK,EAAEC,KAAK,EAAE;MACzC;MACA,IAAID,KAAK,CAACE,KAAK,IAAI7B,iBAAiB,CAACuB,MAAM,IAAII,KAAK,KAAK3B,iBAAiB,CAAC2B,KAAK,CAACE,KAAK,CAAC,EAAE;QACvF,IAAIC,WAAW,GAAGC,SAAS,CAAC9B,SAAS,EAAES,OAAO,EAAEiB,KAAK,EAAEC,KAAK,EAAEH,SAAS,EAAEnC,WAAW,CAAC;QACrFK,UAAU,CAACgC,KAAK,EAAEG,WAAW,EAAE9B,iBAAiB,EAAEV,WAAW,CAAC;MAChE;IACF,CAAC,CAAC;EACJ;AACF;AACA,SAASqB,YAAYA,CAACF,kBAAkB,EAAEV,gBAAgB,EAAET,WAAW,EAAE;EACvE,IAAIoB,OAAO,GAAG7B,MAAM,CAAC,CAAC,CAAC,EAAEkB,gBAAgB,CAAC;EAC1C,IAAIiC,yBAAyB,GAAG1C,WAAW,CAAC0C,yBAAyB;EACrEpD,IAAI,CAAC,CAAC,OAAO,EAAE,YAAY,EAAE,iBAAiB,CAAC,EAAE,UAAUqD,UAAU,EAAE;IACrE;IACAD,yBAAyB,CAACC,UAAU,CAAC,GAAGlC,gBAAgB,CAACkC,UAAU,CAAC;IACpE,IAAIC,GAAG,GAAGzB,kBAAkB,CAACO,GAAG,CAACiB,UAAU,CAAC;IAC5CD,yBAAyB,CAACC,UAAU,CAAC,GAAG,IAAI;IAC5CC,GAAG,IAAI,IAAI,KAAKxB,OAAO,CAACuB,UAAU,CAAC,GAAGC,GAAG,CAAC;EAC5C,CAAC,CAAC;EACF,OAAOxB,OAAO;AAChB;AACA,SAASS,cAAcA,CAACT,OAAO,EAAE;EAC/B,IAAIyB,KAAK,GAAGC,oBAAoB,CAAC1B,OAAO,EAAE,OAAO,CAAC;EAClD,IAAIyB,KAAK,EAAE;IACT,IAAIE,UAAU,GAAGD,oBAAoB,CAAC1B,OAAO,EAAE,YAAY,CAAC;IAC5D,IAAI4B,eAAe,GAAGF,oBAAoB,CAAC1B,OAAO,EAAE,iBAAiB,CAAC;IACtE,IAAI4B,eAAe,EAAE;MACnBH,KAAK,GAAGpD,SAAS,CAACoD,KAAK,EAAE,IAAI,EAAE,IAAI,EAAEG,eAAe,CAAC;IACvD;IACA,IAAID,UAAU,EAAE;MACdF,KAAK,GAAGnD,WAAW,CAACmD,KAAK,EAAEE,UAAU,CAAC;IACxC;IACA,OAAOF,KAAK;EACd;AACF;AACA,SAASf,oBAAoBA,CAACH,qBAAqB,EAAEC,aAAa,EAAE;EAClE,OAAOA,aAAa,IAAI;EACxB;EAAA,EACEnC,SAAS,CAACmC,aAAa,EAAE,IAAI,EAAE,IAAI,EAAED,qBAAqB,CAAC,GAAG,IAAI;AACtE;AACA,SAASmB,oBAAoBA,CAAC1B,OAAO,EAAE6B,IAAI,EAAE;EAC3C,IAAIC,KAAK,GAAG9B,OAAO,CAAC6B,IAAI,CAAC;EACzB,IAAIC,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,MAAM,EAAE;IACrC,OAAOA,KAAK;EACd;AACF;AACA,SAASd,kBAAkBA,CAAC5B,IAAI,EAAEG,SAAS,EAAEE,UAAU,EAAEM,kBAAkB,EAAEC,OAAO,EAAEY,YAAY,EAAE;EAClG,IAAI,CAACA,YAAY,IAAI,CAACA,YAAY,CAACC,MAAM,EAAE;IACzC;EACF;EACA,IAAIkB,WAAW,GAAGC,cAAc,CAACzC,SAAS,EAAE,OAAO,CAAC,IAAIS,OAAO,CAACyB,KAAK,IAAI,IAAI,IAAIzB,OAAO,CAACyB,KAAK,KAAK,MAAM,KAAKO,cAAc,CAACzC,SAAS,EAAE,YAAY,CAAC,IAAIyC,cAAc,CAACzC,SAAS,EAAE,iBAAiB,CAAC,CAAC;EACtM,IAAI,CAACwC,WAAW,EAAE;IAChB;EACF;EACA,IAAIE,SAAS,GAAG1C,SAAS,CAACe,GAAG,CAAC,WAAW,CAAC;EAC1C,IAAI4B,SAAS,GAAG3C,SAAS,CAACe,GAAG,CAAC,WAAW,CAAC;EAC1C,IAAI6B,UAAU,GAAG1C,UAAU,CAAC0C,UAAU,CAACC,KAAK,CAAC,CAAC;EAC9CH,SAAS,IAAI,IAAI,IAAIA,SAAS,GAAGE,UAAU,CAAC,CAAC,CAAC,KAAKA,UAAU,CAAC,CAAC,CAAC,GAAGF,SAAS,CAAC;EAC7EC,SAAS,IAAI,IAAI,IAAIA,SAAS,GAAGC,UAAU,CAAC,CAAC,CAAC,KAAKA,UAAU,CAAC,CAAC,CAAC,GAAGD,SAAS,CAAC;EAC7E,IAAIG,cAAc,GAAG9C,SAAS,CAACe,GAAG,CAAC,gBAAgB,CAAC;EACpD,IAAIgC,GAAG,GAAG;IACRC,IAAI,EAAER,WAAW,CAACF,IAAI;IACtBM,UAAU,EAAEA,UAAU;IACtBK,MAAM,EAAET,WAAW,CAACU;EACtB,CAAC;EACD,IAAIH,GAAG,CAACC,IAAI,KAAK,OAAO,KAAKF,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,IAAI,CAAC,EAAE;IACnFC,GAAG,CAACI,aAAa,GAAG,UAAU;IAC9BJ,GAAG,CAACK,IAAI,GAAG,IAAI;IACf;EACF,CAAC,MAAM;IACLL,GAAG,CAACI,aAAa,GAAG,QAAQ;EAC9B;EACA,IAAIE,OAAO,GAAG,IAAI3E,aAAa,CAACqE,GAAG,CAAC;EACpC7D,KAAK,CAACmE,OAAO,CAAC,CAACC,gBAAgB,GAAGR,cAAc;EAChD,OAAOO,OAAO;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASZ,cAAcA,CAACzC,SAAS,EAAEsC,IAAI,EAAE;EACvC;EACA;EACA,IAAIY,KAAK,GAAGlD,SAAS,CAACe,GAAG,CAACuB,IAAI,CAAC;EAC/B,OAAOzD,OAAO,CAACqE,KAAK,CAAC,IAAIA,KAAK,CAAC5B,MAAM,GAAG;IACtCgB,IAAI,EAAEA,IAAI;IACVY,KAAK,EAAEA;EACT,CAAC,GAAG,IAAI;AACV;AACA,SAASpB,SAASA,CAAC9B,SAAS,EAAES,OAAO,EAAEiB,KAAK,EAAEC,KAAK,EAAE0B,OAAO,EAAEhE,WAAW,EAAE;EACzE,IAAIkE,YAAY,GAAG3E,MAAM,CAAC,CAAC,CAAC,EAAE6B,OAAO,CAAC;EACtC,IAAI4C,OAAO,EAAE;IACX;IACA,IAAIG,WAAW,GAAGH,OAAO,CAACL,IAAI;IAC9B,IAAIF,cAAc,GAAGU,WAAW,KAAK,OAAO,IAAItE,KAAK,CAACmE,OAAO,CAAC,CAACC,gBAAgB;IAC/E,IAAIf,KAAK,GAAGO,cAAc,KAAK,OAAO,GAAGnB,KAAK,GAAGmB,cAAc,KAAK,IAAI,GAAGzD,WAAW,CAACoE,YAAY,CAAC/B,KAAK,CAACgC,KAAK,CAAC,CAAC,CAAC,GAAGhC,KAAK,CAACiC,QAAQ,CAAC3D,SAAS,CAACe,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACrKwC,YAAY,CAACC,WAAW,CAAC,GAAGH,OAAO,CAACO,gBAAgB,CAACrB,KAAK,CAAC;EAC7D;EACA,OAAOgB,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}