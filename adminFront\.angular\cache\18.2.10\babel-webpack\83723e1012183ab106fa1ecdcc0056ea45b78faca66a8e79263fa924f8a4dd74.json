{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport { tap } from 'rxjs';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"../../components/pagination/pagination.component\";\nconst _c0 = [\"createModal\"];\nconst _c1 = [\"editModal\"];\nconst _c2 = [\"templateDetailModal\"];\nfunction TemplateComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const createModal_r4 = i0.ɵɵreference(63);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(createModal_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_58_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_58_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const editModal_r9 = i0.ɵɵreference(65);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(editModal_r9, template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_58_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_58_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteTemplate(template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 44);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 35);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 36)(14, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_58_Template_button_click_14_listener() {\n      const template_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateDetailModal_r7 = i0.ɵɵreference(67);\n      return i0.ɵɵresetView(ctx_r2.viewTemplateDetail(template_r6, templateDetailModal_r7));\n    });\n    i0.ɵɵelement(15, \"i\", 38);\n    i0.ɵɵtext(16, \"\\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, TemplateComponent_tr_58_button_17_Template, 3, 0, \"button\", 39)(18, TemplateComponent_tr_58_button_18_Template, 3, 0, \"button\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const template_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CTemplateType === 1 ? \"\\u7A7A\\u9593\\u6A21\\u677F\" : template_r6.CTemplateType === 2 ? \"\\u9805\\u76EE\\u6A21\\u677F\" : \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(template_r6.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", template_r6.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 8, template_r6.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r6.CCreator || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction TemplateComponent_tr_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 45);\n    i0.ɵɵelement(2, \"i\", 46);\n    i0.ɵɵtext(3, \"\\u76EE\\u524D\\u6C92\\u6709\\u4EFB\\u4F55\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_62_div_47_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 97)(2, \"input\", 98);\n    i0.ɵɵlistener(\"change\", function TemplateComponent_ng_template_62_div_47_div_25_Template_input_change_2_listener() {\n      const space_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.toggleSpaceSelection(space_r15));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 99);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementStart(5, \"small\", 100);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const space_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"space-\" + space_r15.CSpaceID)(\"checked\", space_r15.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"space-\" + space_r15.CSpaceID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r15.CPart, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r15.CLocation || \"-\");\n  }\n}\nfunction TemplateComponent_ng_template_62_div_47_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"\\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_62_div_47_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"ngx-pagination\", 32);\n    i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_ng_template_62_div_47_div_27_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spacePageIndex, $event) || (ctx_r2.spacePageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function TemplateComponent_ng_template_62_div_47_div_27_Template_ngx_pagination_PageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.spacePageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r2.spacePageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r2.spacePageSize)(\"CollectionSize\", ctx_r2.spaceTotalRecords);\n  }\n}\nfunction TemplateComponent_ng_template_62_div_47_div_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_div_47_div_28_span_4_Template_button_click_2_listener() {\n      const space_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.removeSelectedSpace(space_r18));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r18 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r18.CPart, \" \");\n  }\n}\nfunction TemplateComponent_ng_template_62_div_47_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"label\", 104);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 105);\n    i0.ɵɵtemplate(4, TemplateComponent_ng_template_62_div_47_div_28_span_4_Template, 3, 1, \"span\", 106);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u7684\\u7A7A\\u9593 (\", ctx_r2.selectedSpacesForTemplate.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedSpacesForTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_62_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"div\", 57)(3, \"label\", 61);\n    i0.ɵɵtext(4, \" \\u9078\\u64C7\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 59)(6, \"div\", 79)(7, \"div\", 80)(8, \"input\", 81);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_62_div_47_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchKeyword, $event) || (ctx_r2.spaceSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_62_div_47_Template_input_keyup_enter_8_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 80)(10, \"input\", 82);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_62_div_47_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchLocation, $event) || (ctx_r2.spaceSearchLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_62_div_47_Template_input_keyup_enter_10_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 83)(12, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_div_47_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSpaceReset());\n    });\n    i0.ɵɵelement(13, \"i\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_div_47_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelement(15, \"i\", 87);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 88)(17, \"div\", 89)(18, \"div\", 5)(19, \"input\", 90);\n    i0.ɵɵlistener(\"change\", function TemplateComponent_ng_template_62_div_47_Template_input_change_19_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"label\", 91);\n    i0.ɵɵtext(21, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"small\", 92);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 54);\n    i0.ɵɵtemplate(25, TemplateComponent_ng_template_62_div_47_div_25_Template, 7, 5, \"div\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, TemplateComponent_ng_template_62_div_47_div_26_Template, 3, 0, \"div\", 94)(27, TemplateComponent_ng_template_62_div_47_div_27_Template, 2, 3, \"div\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, TemplateComponent_ng_template_62_div_47_div_28_Template, 5, 2, \"div\", 96);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchLocation);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"checked\", ctx_r2.allSpacesSelected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx_r2.spaceTotalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx_r2.spacePageIndex, \" / \", ctx_r2.Math.ceil(ctx_r2.spaceTotalRecords / ctx_r2.spacePageSize), \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableSpaces);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableSpaces.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.spaceTotalRecords > ctx_r2.spacePageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedSpacesForTemplate.length > 0);\n  }\n}\nfunction TemplateComponent_ng_template_62_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 54)(2, \"div\", 9)(3, \"div\", 56)(4, \"div\", 57)(5, \"label\", 109);\n    i0.ɵɵtext(6, \" \\u55AE\\u50F9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 59);\n    i0.ɵɵelement(8, \"input\", 110);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"div\", 9)(10, \"div\", 56)(11, \"div\", 57)(12, \"label\", 111);\n    i0.ɵɵtext(13, \" \\u55AE\\u4F4D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 59);\n    i0.ɵɵelement(15, \"input\", 112);\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction TemplateComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 47)(1, \"nb-card-header\", 48)(2, \"h5\", 49);\n    i0.ɵɵelement(3, \"i\", 50);\n    i0.ɵɵtext(4, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_Template_button_click_5_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(6, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 53)(8, \"div\", 54)(9, \"div\", 55)(10, \"div\", 56)(11, \"div\", 57)(12, \"label\", 58);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 59)(15, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_62_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown.control.enter\", function TemplateComponent_ng_template_62_Template_input_keydown_control_enter_15_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 55)(17, \"div\", 56)(18, \"div\", 57)(19, \"label\", 61);\n    i0.ɵɵtext(20, \" \\u6A21\\u677F\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 59)(22, \"nb-tabset\", 62)(23, \"nb-tab\", 63);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_Template_nb_tab_click_23_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.SpaceTemplate);\n    });\n    i0.ɵɵelementStart(24, \"span\", 64);\n    i0.ɵɵelement(25, \"i\", 65);\n    i0.ɵɵtext(26, \"\\u7A7A\\u9593\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"nb-tab\", 66);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_Template_nb_tab_click_27_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.ItemTemplate);\n    });\n    i0.ɵɵelementStart(28, \"span\", 64);\n    i0.ɵɵelement(29, \"i\", 67);\n    i0.ɵɵtext(30, \"\\u9805\\u76EE\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(31, \"div\", 55)(32, \"div\", 56)(33, \"div\", 57)(34, \"label\", 68);\n    i0.ɵɵtext(35, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 59)(37, \"nb-form-field\", 69)(38, \"nb-select\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_62_Template_nb_select_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(39, \"nb-option\", 16)(40, \"span\", 5);\n    i0.ɵɵelement(41, \"i\", 71);\n    i0.ɵɵtext(42, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"nb-option\", 16)(44, \"span\", 5);\n    i0.ɵɵelement(45, \"i\", 72);\n    i0.ɵɵtext(46, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵtemplate(47, TemplateComponent_ng_template_62_div_47_Template, 29, 10, \"div\", 73)(48, TemplateComponent_ng_template_62_div_48_Template, 16, 0, \"div\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"nb-card-footer\", 74)(50, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_Template_button_click_50_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(51, \"i\", 76);\n    i0.ɵɵtext(52, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_62_Template_button_click_53_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelement(54, \"i\", 78);\n    i0.ɵɵtext(55, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_64_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 54)(2, \"div\", 9)(3, \"div\", 56)(4, \"div\", 57)(5, \"label\", 120);\n    i0.ɵɵtext(6, \" \\u55AE\\u50F9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 59);\n    i0.ɵɵelement(8, \"input\", 121);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"div\", 9)(10, \"div\", 56)(11, \"div\", 57)(12, \"label\", 122);\n    i0.ɵɵtext(13, \" \\u55AE\\u4F4D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 59);\n    i0.ɵɵelement(15, \"input\", 123);\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction TemplateComponent_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 113)(1, \"nb-card-header\", 48)(2, \"h5\", 49);\n    i0.ɵɵelement(3, \"i\", 114);\n    i0.ɵɵtext(4, \"\\u7DE8\\u8F2F\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_64_Template_button_click_5_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r20));\n    });\n    i0.ɵɵelement(6, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 53)(8, \"div\", 54)(9, \"div\", 55)(10, \"div\", 56)(11, \"div\", 57)(12, \"label\", 115);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 59)(15, \"input\", 116);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_64_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 55)(17, \"div\", 56)(18, \"div\", 57)(19, \"label\", 61);\n    i0.ɵɵtext(20, \" \\u6A21\\u677F\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 59)(22, \"nb-tabset\", 62)(23, \"nb-tab\", 63);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_64_Template_nb_tab_click_23_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.SpaceTemplate);\n    });\n    i0.ɵɵelementStart(24, \"span\", 64);\n    i0.ɵɵelement(25, \"i\", 65);\n    i0.ɵɵtext(26, \"\\u7A7A\\u9593\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"nb-tab\", 66);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_64_Template_nb_tab_click_27_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.ItemTemplate);\n    });\n    i0.ɵɵelementStart(28, \"span\", 64);\n    i0.ɵɵelement(29, \"i\", 67);\n    i0.ɵɵtext(30, \"\\u9805\\u76EE\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(31, \"div\", 55)(32, \"div\", 56)(33, \"div\", 57)(34, \"label\", 117);\n    i0.ɵɵtext(35, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 59)(37, \"nb-form-field\", 69)(38, \"nb-select\", 118);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_64_Template_nb_select_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(39, \"nb-option\", 16)(40, \"span\", 5);\n    i0.ɵɵelement(41, \"i\", 71);\n    i0.ɵɵtext(42, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"nb-option\", 16)(44, \"span\", 5);\n    i0.ɵɵelement(45, \"i\", 72);\n    i0.ɵɵtext(46, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵtemplate(47, TemplateComponent_ng_template_64_div_47_Template, 16, 0, \"div\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"nb-card-footer\", 74)(49, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_64_Template_button_click_49_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r20));\n    });\n    i0.ɵɵelement(50, \"i\", 76);\n    i0.ɵɵtext(51, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_64_Template_button_click_52_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r20));\n    });\n    i0.ɵɵelement(53, \"i\", 119);\n    i0.ɵɵtext(54, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_66_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146);\n    i0.ɵɵelement(1, \"i\", 147);\n    i0.ɵɵelementStart(2, \"span\", 92);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_66_div_70_div_1_tr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"i\", 155);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵelement(7, \"i\", 156);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r23 = ctx.$implicit;\n    const i_r24 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r24 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r23.CPart, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r23.CLocation || \"-\", \" \");\n  }\n}\nfunction TemplateComponent_ng_template_66_div_70_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 150)(1, \"table\", 151)(2, \"thead\")(3, \"tr\")(4, \"th\", 152);\n    i0.ɵɵtext(5, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 153);\n    i0.ɵɵtext(7, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 154);\n    i0.ɵɵtext(9, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"tbody\");\n    i0.ɵɵtemplate(11, TemplateComponent_ng_template_66_div_70_div_1_tr_11_Template, 9, 3, \"tr\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateDetailSpaces);\n  }\n}\nfunction TemplateComponent_ng_template_66_div_70_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 157);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"\\u6B64\\u6A21\\u677F\\u5C1A\\u672A\\u5305\\u542B\\u4EFB\\u4F55\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_66_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TemplateComponent_ng_template_66_div_70_div_1_Template, 12, 1, \"div\", 148)(2, TemplateComponent_ng_template_66_div_70_div_2_Template, 3, 0, \"div\", 149);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length === 0);\n  }\n}\nfunction TemplateComponent_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 47)(1, \"nb-card-header\", 48)(2, \"h5\", 49);\n    i0.ɵɵelement(3, \"i\", 124);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u660E\\u7D30 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_66_Template_button_click_5_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r22));\n    });\n    i0.ɵɵelement(6, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 53)(8, \"div\", 125)(9, \"div\", 126)(10, \"h6\", 127);\n    i0.ɵɵelement(11, \"i\", 128);\n    i0.ɵɵtext(12, \"\\u57FA\\u672C\\u8CC7\\u8A0A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 129)(14, \"div\", 54)(15, \"div\", 9)(16, \"div\", 130)(17, \"label\", 131);\n    i0.ɵɵelement(18, \"i\", 132);\n    i0.ɵɵtext(19, \"\\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 133);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 9)(23, \"div\", 130)(24, \"label\", 131);\n    i0.ɵɵelement(25, \"i\", 134);\n    i0.ɵɵtext(26, \"\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 133)(28, \"span\", 35);\n    i0.ɵɵelement(29, \"i\", 135);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 9)(32, \"div\", 130)(33, \"label\", 131);\n    i0.ɵɵelement(34, \"i\", 136);\n    i0.ɵɵtext(35, \"\\u5EFA\\u7ACB\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"p\", 133);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 9)(40, \"div\", 130)(41, \"label\", 131);\n    i0.ɵɵelement(42, \"i\", 137);\n    i0.ɵɵtext(43, \"\\u5EFA\\u7ACB\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 133);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 9)(47, \"div\", 130)(48, \"label\", 131);\n    i0.ɵɵelement(49, \"i\", 138);\n    i0.ɵɵtext(50, \"\\u66F4\\u65B0\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 133);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 130)(56, \"label\", 131);\n    i0.ɵɵelement(57, \"i\", 139);\n    i0.ɵɵtext(58, \"\\u66F4\\u65B0\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\", 133);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(61, \"div\", 140)(62, \"div\", 141)(63, \"h6\", 127);\n    i0.ɵɵelement(64, \"i\", 142);\n    i0.ɵɵtext(65, \"\\u5305\\u542B\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 143);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 129);\n    i0.ɵɵtemplate(69, TemplateComponent_ng_template_66_div_69_Template, 4, 0, \"div\", 144)(70, TemplateComponent_ng_template_66_div_70_Template, 3, 2, \"div\", 30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"nb-card-footer\", 74)(72, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_66_Template_button_click_72_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r22));\n    });\n    i0.ɵɵelement(73, \"i\", 76);\n    i0.ɵɵtext(74, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CTemplateName) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"fas fa-check-circle\" : \"fas fa-times-circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(38, 12, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(53, 15, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r2.templateDetailSpaces.length, \" \\u500B\\u7A7A\\u9593\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingTemplateDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoadingTemplateDetail);\n  }\n}\nexport class TemplateComponent extends BaseComponent {\n  constructor(allow, dialogService, _templateService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._templateService = _templateService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.Math = Math; // 讓模板可以使用 Math 函數\n    this.EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\n    this.EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    // 模板相關屬性\n    this.templateList = [];\n    this.templateDetail = {};\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    // 空間選擇相關屬性\n    this.availableSpaces = [];\n    this.selectedSpacesForTemplate = [];\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.spacePageSize = 10;\n    this.spaceTotalRecords = 0;\n    this.allSpacesSelected = false;\n    // 模板明細相關屬性\n    this.selectedTemplateDetail = null;\n    this.templateDetailSpaces = [];\n    this.isLoadingTemplateDetail = false;\n  }\n  ngOnInit() {\n    this.loadTemplateList();\n    this.loadAvailableSpaces();\n  }\n  // 載入模板列表\n  loadTemplateList() {\n    const request = {\n      CTemplateName: this.searchKeyword || null,\n      CStatus: this.searchStatus,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this._templateService.apiTemplateGetTemplateListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.templateList = response.Entries?.map(item => ({\n          CTemplateId: item.CTemplateId,\n          CTemplateName: item.CTemplateName,\n          CTemplateType: item.CTemplateType,\n          // 新增模板類型\n          CCreateDt: item.CCreateDt,\n          CUpdateDt: item.CUpdateDt,\n          CCreator: item.CCreator,\n          CUpdator: item.CUpdator,\n          CStatus: item.CStatus\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板列表失敗');\n      }\n    })).subscribe();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    const request = {\n      CPart: this.spaceSearchKeyword || null,\n      CLocation: this.spaceSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.spacePageIndex,\n      PageSize: this.spacePageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.spaceTotalRecords = response.TotalItems || 0;\n        this.updateAllSpacesSelectedState();\n      }\n    })).subscribe();\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  // 空間搜尋功能\n  onSpaceSearch() {\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  onSpaceReset() {\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 分頁功能\n  pageChanged(page) {\n    this.pageIndex = page;\n    this.loadTemplateList();\n  }\n  spacePageChanged(page) {\n    this.spacePageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  // 模態框操作\n  openCreateModal(modal) {\n    this.templateDetail = {\n      CStatus: 1,\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\n    };\n    this.selectedSpacesForTemplate = [];\n    this.loadAvailableSpaces();\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  openEditModal(modal, template) {\n    this.templateDetail = {\n      CTemplateId: template.CTemplateId,\n      CTemplateName: template.CTemplateName,\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\n      CStatus: template.CStatus || 1\n    };\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onSubmit(ref) {\n    if (!this.validateTemplateForm()) {\n      return;\n    }\n    if (this.templateDetail.CTemplateId) {\n      this.updateTemplate(ref);\n    } else {\n      this.createTemplate(ref);\n    }\n  }\n  // 驗證表單\n  validateTemplateForm() {\n    if (!this.templateDetail.CTemplateName?.trim()) {\n      this.message.showErrorMSG('請輸入模板名稱');\n      return false;\n    }\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\n      this.message.showErrorMSG('請選擇模板類型');\n      return false;\n    }\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\n      this.message.showErrorMSG('請選擇模板狀態');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\n      return false;\n    }\n    return true;\n  }\n  // 建立模板\n  createTemplate(ref) {\n    const templateData = {\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0 && response.Entries) {\n        const templateId = parseInt(response.Entries, 10);\n        this.saveTemplateDetails(templateId, ref);\n      } else {\n        this.message.showErrorMSG(response.Message || '建立模板失敗');\n      }\n    })).subscribe();\n  }\n  // 更新模板\n  updateTemplate(ref) {\n    const templateData = {\n      CTemplateId: this.templateDetail.CTemplateId,\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.message.showSucessMSG('更新模板成功');\n        ref.close();\n        this.loadTemplateList();\n      } else {\n        this.message.showErrorMSG(response.Message || '更新模板失敗');\n      }\n    })).subscribe();\n  }\n  // 儲存模板詳細資料（關聯空間）\n  saveTemplateDetails(templateId, ref) {\n    // 目前 API 可能不支援模板詳細資料的保存，暫時跳過這個步驟\n    this.message.showSucessMSG('建立模板成功');\n    ref.close();\n    this.loadTemplateList();\n  }\n  // 刪除模板\n  deleteTemplate(template) {\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\n        body: {\n          CTemplateId: template.CTemplateId\n        }\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('刪除模板成功');\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '刪除模板失敗');\n        }\n      })).subscribe();\n    }\n  }\n  // 查看模板明細\n  viewTemplateDetail(template, modal) {\n    this.selectedTemplateDetail = template;\n    this.isLoadingTemplateDetail = true;\n    this.templateDetailSpaces = [];\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n    const request = {\n      templateId: template.CTemplateId\n    };\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      this.isLoadingTemplateDetail = false;\n      if (response.StatusCode === 0) {\n        this.templateDetailSpaces = response.Entries?.map(item => ({\n          CReleateId: item.CReleateId,\n          CPart: item.CPart,\n          CLocation: item.CLocation\n        })) || [];\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板明細失敗');\n      }\n    })).subscribe();\n  }\n  // 空間選擇相關方法\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n        this.selectedSpacesForTemplate.push({\n          ...space\n        });\n      }\n    } else {\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  toggleAllSpaces() {\n    this.allSpacesSelected = !this.allSpacesSelected;\n    this.availableSpaces.forEach(space => {\n      space.selected = this.allSpacesSelected;\n      if (this.allSpacesSelected) {\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else {\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n  }\n  removeSelectedSpace(space) {\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  updateAllSpacesSelectedState() {\n    this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  static {\n    this.ɵfac = function TemplateComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateComponent,\n      selectors: [[\"ngx-template\"]],\n      viewQuery: function TemplateComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.createModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateDetailModal = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 68,\n      vars: 11,\n      consts: [[\"createModal\", \"\"], [\"editModal\", \"\"], [\"templateDetailModal\", \"\"], [\"accent\", \"success\"], [1, \"alert\", \"alert-info\", \"mb-4\", 2, \"border-left\", \"4px solid #4a90e2\", \"background-color\", \"#f8f9ff\", \"border-radius\", \"6px\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-3\", 2, \"font-size\", \"1.2rem\"], [1, \"mb-0\", \"text-muted\", 2, \"font-size\", \"0.9rem\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"templateName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"col-3\"], [\"id\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"800px\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-3\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"badge\", 3, \"ngClass\"], [1, \"table-actions\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-outline-warning btn-sm me-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-warning\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"5\", 1, \"text-muted\", \"py-4\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"border-bottom\", \"py-3\", \"px-4\"], [1, \"mb-0\", \"text-primary\", \"font-weight-bold\"], [1, \"fas\", \"fa-plus-circle\", \"me-2\", \"text-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-light\", \"btn-sm\", \"rounded-circle\", 2, \"width\", \"32px\", \"height\", \"32px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"px-4\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"form-group\", \"mb-4\"], [1, \"d-flex\", \"align-items-start\"], [\"for\", \"templateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"flex-grow-1\", \"ml-3\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"templateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"keydown.control.enter\", \"ngModel\"], [1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"template-type-tabs\"], [\"tabTitle\", \"\\u7A7A\\u9593\\u6A21\\u677F\", 3, \"click\", \"active\"], [\"slot\", \"tabTitle\"], [1, \"fas\", \"fa-home\", \"me-2\"], [\"tabTitle\", \"\\u9805\\u76EE\\u6A21\\u677F\", 3, \"click\", \"active\"], [1, \"fas\", \"fa-list\", \"me-2\"], [\"for\", \"templateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"w-full\"], [\"id\", \"templateStatus\", \"name\", \"templateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\", \"me-2\"], [\"class\", \"col-12\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\", \"border-top\", \"pt-3\", \"px-4\", \"pb-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"btn\", \"btn-outline-secondary\", \"me-3\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", \"background\", \"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [1, \"row\", \"mb-3\"], [1, \"col-md-5\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"col-md-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"border\", \"rounded\", \"p-3\", 2, \"max-height\", \"300px\", \"overflow-y\", \"auto\", \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"selectAll\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAll\", 1, \"mb-0\", \"font-weight-bold\"], [1, \"text-muted\"], [\"class\", \"col-md-6\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-3\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center mt-3\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"id\", \"checked\"], [1, \"form-check-label\", 3, \"for\"], [1, \"text-muted\", \"d-block\"], [1, \"text-center\", \"text-muted\", \"py-3\"], [1, \"d-flex\", \"justify-content-center\", \"mt-3\"], [1, \"mt-3\"], [1, \"mb-2\", \"font-weight-bold\"], [1, \"border\", \"rounded\", \"p-2\", 2, \"max-height\", \"150px\", \"overflow-y\", \"auto\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", 1, \"btn-close\", \"btn-close-white\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"], [\"for\", \"unitPrice\", 1, \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"number\", \"id\", \"unitPrice\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u50F9\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\"], [\"for\", \"unit\", 1, \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"unit\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u4F4D\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"fas\", \"fa-edit\", \"me-2\", \"text-warning\"], [\"for\", \"editTemplateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"editTemplateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"editTemplateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"editTemplateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"id\", \"editTemplateStatus\", \"name\", \"editTemplateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-save\", \"me-1\"], [\"for\", \"editUnitPrice\", 1, \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"number\", \"id\", \"editUnitPrice\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u50F9\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\"], [\"for\", \"editUnit\", 1, \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"editUnit\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u4F4D\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\"], [1, \"fas\", \"fa-eye\", \"me-2\", \"text-info\"], [1, \"card\", \"mb-4\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"mb-0\", \"text-dark\", \"font-weight-bold\"], [1, \"fas\", \"fa-info-circle\", \"me-2\", \"text-primary\"], [1, \"card-body\"], [1, \"form-group\", \"mb-3\"], [1, \"font-weight-bold\", \"text-muted\"], [1, \"fas\", \"fa-tag\", \"me-2\", \"text-primary\"], [1, \"mb-0\"], [1, \"fas\", \"fa-toggle-on\", \"me-2\", \"text-success\"], [1, \"me-1\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-plus\", \"me-2\", \"text-warning\"], [1, \"fas\", \"fa-calendar-edit\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-edit\", \"me-2\", \"text-warning\"], [1, \"card\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-success\"], [1, \"badge\", \"badge-info\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"text-center\", \"py-4\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"me-2\", \"text-primary\", 2, \"font-size\", \"1.2rem\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-7\"], [\"scope\", \"col\", 1, \"col-4\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\", \"text-muted\"], [1, \"text-center\", \"text-muted\", \"py-4\"]],\n      template: function TemplateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementStart(7, \"div\")(8, \"p\", 7);\n          i0.ɵɵtext(9, \" \\u5728\\u6B64\\u9801\\u9762\\u60A8\\u53EF\\u4EE5\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u7684\\u5404\\u500B\\u6A21\\u677F\\u8CC7\\u8A0A\\uFF0C\\u5305\\u62EC\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u6A21\\u677F\\uFF0C\\u4EE5\\u53CA\\u8A2D\\u5B9A\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u72C0\\u614B\\u548C\\u5305\\u542B\\u7684\\u7A7A\\u9593\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"label\", 11);\n          i0.ɵɵtext(14, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"nb-form-field\", 12)(16, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_Template_input_keyup_enter_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10)(19, \"label\", 14);\n          i0.ɵɵtext(20, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"nb-form-field\", 12)(22, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchStatus, $event) || (ctx.searchStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(23, \"nb-option\", 16);\n          i0.ɵɵtext(24, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nb-option\", 16);\n          i0.ɵɵtext(26, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-option\", 16);\n          i0.ɵɵtext(28, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(29, \"div\", 9);\n          i0.ɵɵelementStart(30, \"div\", 17)(31, \"div\", 18)(32, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_32_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReset());\n          });\n          i0.ɵɵelement(33, \"i\", 20);\n          i0.ɵɵtext(34, \"\\u91CD\\u7F6E \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_35_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelement(36, \"i\", 22);\n          i0.ɵɵtext(37, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"div\", 17)(39, \"div\", 23);\n          i0.ɵɵtemplate(40, TemplateComponent_button_40_Template, 3, 0, \"button\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 25)(42, \"table\", 26)(43, \"thead\")(44, \"tr\")(45, \"th\", 27);\n          i0.ɵɵtext(46, \"\\u6A21\\u677F\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"th\", 28);\n          i0.ɵɵtext(48, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"th\", 27);\n          i0.ɵɵtext(50, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"th\", 28);\n          i0.ɵɵtext(52, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"th\", 27);\n          i0.ɵɵtext(54, \"\\u5EFA\\u7ACB\\u8005\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"th\", 27);\n          i0.ɵɵtext(56, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(57, \"tbody\");\n          i0.ɵɵtemplate(58, TemplateComponent_tr_58_Template, 19, 11, \"tr\", 29)(59, TemplateComponent_tr_59_Template, 4, 0, \"tr\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(60, \"nb-card-footer\", 31)(61, \"ngx-pagination\", 32);\n          i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_61_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_61_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(62, TemplateComponent_ng_template_62_Template, 56, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(64, TemplateComponent_ng_template_64_Template, 55, 7, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(66, TemplateComponent_ng_template_66_Template, 75, 18, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.templateList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.templateList.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, i6.DatePipe, SharedModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbTabsetComponent, i2.NbTabComponent, i2.NbFormFieldComponent, i8.BreadcrumbComponent, i9.PaginationComponent],\n      styles: [\".btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0;\\n  border-bottom-left-radius: 0;\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--color-fg-heading);\\n}\\n\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.badge.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n.badge.badge-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.required-field[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: #dc3545;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n\\n.btn-close-white[_ngcontent-%COMP%] {\\n  filter: invert(1) grayscale(100%) brightness(200%);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "CommonModule", "SharedModule", "tap", "EnumTemplateType", "EnumTemplateTypeHelper", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateComponent_button_40_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "createModal_r4", "ɵɵreference", "ɵɵresetView", "openCreateModal", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "TemplateComponent_tr_58_button_17_Template_button_click_0_listener", "_r8", "template_r6", "$implicit", "editModal_r9", "openEditModal", "TemplateComponent_tr_58_button_18_Template_button_click_0_listener", "_r10", "deleteTemplate", "TemplateComponent_tr_58_Template_button_click_14_listener", "_r5", "templateDetailModal_r7", "viewTemplateDetail", "ɵɵtemplate", "TemplateComponent_tr_58_button_17_Template", "TemplateComponent_tr_58_button_18_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "CTemplateType", "ɵɵtextInterpolate", "CTemplateName", "ɵɵproperty", "CStatus", "ɵɵpipeBind2", "CCreateDt", "CCreator", "isUpdate", "isDelete", "TemplateComponent_ng_template_62_div_47_div_25_Template_input_change_2_listener", "space_r15", "_r14", "toggleSpaceSelection", "CSpaceID", "selected", "<PERSON>art", "CLocation", "ɵɵtwoWayListener", "TemplateComponent_ng_template_62_div_47_div_27_Template_ngx_pagination_PageChange_1_listener", "$event", "_r16", "ɵɵtwoWayBindingSet", "spacePageIndex", "spacePageChanged", "ɵɵtwoWayProperty", "spacePageSize", "spaceTotalRecords", "TemplateComponent_ng_template_62_div_47_div_28_span_4_Template_button_click_2_listener", "space_r18", "_r17", "removeSelectedSpace", "TemplateComponent_ng_template_62_div_47_div_28_span_4_Template", "selectedSpacesForTemplate", "length", "TemplateComponent_ng_template_62_div_47_Template_input_ngModelChange_8_listener", "_r13", "spaceSearchKeyword", "TemplateComponent_ng_template_62_div_47_Template_input_keyup_enter_8_listener", "onSpaceSearch", "TemplateComponent_ng_template_62_div_47_Template_input_ngModelChange_10_listener", "spaceSearchLocation", "TemplateComponent_ng_template_62_div_47_Template_input_keyup_enter_10_listener", "TemplateComponent_ng_template_62_div_47_Template_button_click_12_listener", "onSpaceReset", "TemplateComponent_ng_template_62_div_47_Template_button_click_14_listener", "TemplateComponent_ng_template_62_div_47_Template_input_change_19_listener", "toggleAllSpaces", "TemplateComponent_ng_template_62_div_47_div_25_Template", "TemplateComponent_ng_template_62_div_47_div_26_Template", "TemplateComponent_ng_template_62_div_47_div_27_Template", "TemplateComponent_ng_template_62_div_47_div_28_Template", "allSpacesSelected", "ɵɵtextInterpolate3", "Math", "ceil", "availableSpaces", "TemplateComponent_ng_template_62_Template_button_click_5_listener", "ref_r12", "_r11", "dialogRef", "onClose", "TemplateComponent_ng_template_62_Template_input_ngModelChange_15_listener", "templateDetail", "TemplateComponent_ng_template_62_Template_input_keydown_control_enter_15_listener", "onSubmit", "TemplateComponent_ng_template_62_Template_nb_tab_click_23_listener", "SpaceTemplate", "TemplateComponent_ng_template_62_Template_nb_tab_click_27_listener", "ItemTemplate", "TemplateComponent_ng_template_62_Template_nb_select_ngModelChange_38_listener", "TemplateComponent_ng_template_62_div_47_Template", "TemplateComponent_ng_template_62_div_48_Template", "TemplateComponent_ng_template_62_Template_button_click_50_listener", "TemplateComponent_ng_template_62_Template_button_click_53_listener", "TemplateComponent_ng_template_64_Template_button_click_5_listener", "ref_r20", "_r19", "TemplateComponent_ng_template_64_Template_input_ngModelChange_15_listener", "TemplateComponent_ng_template_64_Template_nb_tab_click_23_listener", "TemplateComponent_ng_template_64_Template_nb_tab_click_27_listener", "TemplateComponent_ng_template_64_Template_nb_select_ngModelChange_38_listener", "TemplateComponent_ng_template_64_div_47_Template", "TemplateComponent_ng_template_64_Template_button_click_49_listener", "TemplateComponent_ng_template_64_Template_button_click_52_listener", "i_r24", "space_r23", "TemplateComponent_ng_template_66_div_70_div_1_tr_11_Template", "templateDetailSpaces", "TemplateComponent_ng_template_66_div_70_div_1_Template", "TemplateComponent_ng_template_66_div_70_div_2_Template", "TemplateComponent_ng_template_66_Template_button_click_5_listener", "ref_r22", "_r21", "TemplateComponent_ng_template_66_div_69_Template", "TemplateComponent_ng_template_66_div_70_Template", "TemplateComponent_ng_template_66_Template_button_click_72_listener", "selectedTemplateDetail", "ɵɵclassMap", "CUpdateDt", "CUpdator", "isLoadingTemplateDetail", "TemplateComponent", "constructor", "allow", "dialogService", "_templateService", "_spaceService", "message", "valid", "pageFirst", "pageSize", "pageIndex", "totalRecords", "templateList", "searchKeyword", "searchStatus", "ngOnInit", "loadTemplateList", "loadAvailableSpaces", "request", "PageIndex", "PageSize", "apiTemplateGetTemplateListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "TotalItems", "showErrorMSG", "Message", "subscribe", "apiSpaceGetSpaceListPost$Json", "some", "s", "updateAllSpacesSelectedState", "onSearch", "onReset", "pageChanged", "page", "modal", "open", "context", "autoFocus", "template", "ref", "close", "validateTemplateForm", "updateTemplate", "createTemplate", "trim", "undefined", "templateData", "apiTemplateSaveTemplatePost$Json", "templateId", "parseInt", "saveTemplateDetails", "showSucessMSG", "confirm", "apiTemplateDeleteTemplatePost$Json", "apiTemplateGetTemplateDetailByIdPost$Json", "CReleateId", "space", "push", "filter", "for<PERSON>ach", "availableSpace", "find", "every", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "TemplateService", "SpaceService", "i4", "MessageService", "i5", "ValidationHelper", "selectors", "viewQuery", "TemplateComponent_Query", "rf", "ctx", "TemplateComponent_Template_input_ngModelChange_16_listener", "_r1", "TemplateComponent_Template_input_keyup_enter_16_listener", "TemplateComponent_Template_nb_select_ngModelChange_22_listener", "TemplateComponent_Template_nb_select_selectedChange_22_listener", "TemplateComponent_Template_button_click_32_listener", "TemplateComponent_Template_button_click_35_listener", "TemplateComponent_button_40_Template", "TemplateComponent_tr_58_Template", "TemplateComponent_tr_59_Template", "TemplateComponent_Template_ngx_pagination_PageChange_61_listener", "TemplateComponent_ng_template_62_Template", "ɵɵtemplateRefExtractor", "TemplateComponent_ng_template_64_Template", "TemplateComponent_ng_template_66_Template", "isCreate", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbTabsetComponent", "NbTabComponent", "NbFormFieldComponent", "i8", "BreadcrumbComponent", "i9", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.html"], "sourcesContent": ["import { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { TemplateService, SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { tap } from 'rxjs';\r\nimport {\r\n  SaveTemplateArgs,\r\n  GetTemplateDetailByIdArgs,\r\n  TemplateDetailItem,\r\n  GetSpaceListResponse\r\n} from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\nexport interface TemplateItem {\r\n  CTemplateId: number;\r\n  CTemplateName: string;\r\n  CTemplateType?: number;\r\n  CCreateDt: string;\r\n  CUpdateDt: string;\r\n  CCreator?: string | null;\r\n  CUpdator?: string | null;\r\n  CStatus?: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpacePickListItem {\r\n  CSpaceID: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n  selected?: boolean;\r\n}\r\n\r\n// 僅用於模板明細空間顯示\r\nexport interface TemplateDetailSpaceItem {\r\n  CReleateId: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-template',\r\n  templateUrl: './template.component.html',\r\n  styleUrls: ['./template.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent\r\n  ],\r\n})\r\nexport class TemplateComponent extends BaseComponent implements OnInit {\r\n  Math = Math; // 讓模板可以使用 Math 函數\r\n  EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\r\n  EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\r\n\r\n  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;\r\n  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;\r\n  @ViewChild('templateDetailModal', { static: false }) templateDetailModal!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _templateService: TemplateService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow);\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  // 模板相關屬性\r\n  templateList: TemplateItem[] = [];\r\n  templateDetail: SaveTemplateArgs = {};\r\n  searchKeyword: string = '';\r\n  searchStatus: number | null = null;\r\n\r\n  // 空間選擇相關屬性\r\n  availableSpaces: SpacePickListItem[] = [];\r\n  selectedSpacesForTemplate: SpacePickListItem[] = [];\r\n  spaceSearchKeyword: string = '';\r\n  spaceSearchLocation: string = '';\r\n  spacePageIndex = 1;\r\n  spacePageSize = 10;\r\n  spaceTotalRecords = 0;\r\n  allSpacesSelected = false;\r\n\r\n  // 模板明細相關屬性\r\n  selectedTemplateDetail: TemplateItem | null = null;\r\n  templateDetailSpaces: TemplateDetailSpaceItem[] = [];\r\n  isLoadingTemplateDetail = false;\r\n\r\n  override ngOnInit(): void {\r\n    this.loadTemplateList();\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 載入模板列表\r\n  loadTemplateList(): void {\r\n    const request = {\r\n      CTemplateName: this.searchKeyword || null,\r\n      CStatus: this.searchStatus,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.templateList = response.Entries?.map(item => ({\r\n            CTemplateId: item.CTemplateId!,\r\n            CTemplateName: item.CTemplateName!,\r\n            CTemplateType: item.CTemplateType, // 新增模板類型\r\n            CCreateDt: item.CCreateDt!,\r\n            CUpdateDt: item.CUpdateDt!,\r\n            CCreator: item.CCreator,\r\n            CUpdator: item.CUpdator,\r\n            CStatus: item.CStatus\r\n          })) || [];\r\n          this.totalRecords = response.TotalItems || 0;\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板列表失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 載入可用空間列表\r\n  loadAvailableSpaces(): void {\r\n    const request = {\r\n      CPart: this.spaceSearchKeyword || null,\r\n      CLocation: this.spaceSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.spacePageIndex,\r\n      PageSize: this.spacePageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableSpaces = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\r\n          })) || [];\r\n          this.spaceTotalRecords = response.TotalItems || 0;\r\n          this.updateAllSpacesSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 搜尋功能\r\n  onSearch(): void {\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  onReset(): void {\r\n    this.searchKeyword = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 空間搜尋功能\r\n  onSpaceSearch(): void {\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  onSpaceReset(): void {\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 分頁功能\r\n  pageChanged(page: number): void {\r\n    this.pageIndex = page;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  spacePageChanged(page: number): void {\r\n    this.spacePageIndex = page;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 模態框操作\r\n  openCreateModal(modal: TemplateRef<any>): void {\r\n    this.templateDetail = {\r\n      CStatus: 1,\r\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\r\n    };\r\n    this.selectedSpacesForTemplate = [];\r\n    this.loadAvailableSpaces();\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  openEditModal(modal: TemplateRef<any>, template: TemplateItem): void {\r\n    this.templateDetail = {\r\n      CTemplateId: template.CTemplateId,\r\n      CTemplateName: template.CTemplateName,\r\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\r\n      CStatus: template.CStatus || 1\r\n    };\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  onClose(ref: any): void {\r\n    ref.close();\r\n  }\r\n\r\n  onSubmit(ref: any): void {\r\n    if (!this.validateTemplateForm()) {\r\n      return;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateId) {\r\n      this.updateTemplate(ref);\r\n    } else {\r\n      this.createTemplate(ref);\r\n    }\r\n  }\r\n\r\n  // 驗證表單\r\n  validateTemplateForm(): boolean {\r\n    if (!this.templateDetail.CTemplateName?.trim()) {\r\n      this.message.showErrorMSG('請輸入模板名稱');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\r\n      this.message.showErrorMSG('請選擇模板類型');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\r\n      this.message.showErrorMSG('請選擇模板狀態');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\r\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 建立模板\r\n  createTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          const templateId = parseInt(response.Entries, 10);\r\n          this.saveTemplateDetails(templateId, ref);\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '建立模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 更新模板\r\n  updateTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateId: this.templateDetail.CTemplateId,\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.message.showSucessMSG('更新模板成功');\r\n          ref.close();\r\n          this.loadTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '更新模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 儲存模板詳細資料（關聯空間）\r\n  saveTemplateDetails(templateId: number, ref: any): void {\r\n    // 目前 API 可能不支援模板詳細資料的保存，暫時跳過這個步驟\r\n    this.message.showSucessMSG('建立模板成功');\r\n    ref.close();\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 刪除模板\r\n  deleteTemplate(template: TemplateItem): void {\r\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\r\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\r\n        body: { CTemplateId: template.CTemplateId }\r\n      }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('刪除模板成功');\r\n            this.loadTemplateList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '刪除模板失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    }\r\n  }\r\n\r\n  // 查看模板明細\r\n  viewTemplateDetail(template: TemplateItem, modal: TemplateRef<any>): void {\r\n    this.selectedTemplateDetail = template;\r\n    this.isLoadingTemplateDetail = true;\r\n    this.templateDetailSpaces = [];\r\n\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n\r\n    const request: GetTemplateDetailByIdArgs = {\r\n      templateId: template.CTemplateId\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        this.isLoadingTemplateDetail = false;\r\n        if (response.StatusCode === 0) {\r\n          this.templateDetailSpaces = response.Entries?.map(item => ({\r\n            CReleateId: item.CReleateId!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation\r\n          })) || [];\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板明細失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間選擇相關方法\r\n  toggleSpaceSelection(space: SpacePickListItem): void {\r\n    space.selected = !space.selected;\r\n\r\n    if (space.selected) {\r\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n        this.selectedSpacesForTemplate.push({ ...space });\r\n      }\r\n    } else {\r\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  toggleAllSpaces(): void {\r\n    this.allSpacesSelected = !this.allSpacesSelected;\r\n\r\n    this.availableSpaces.forEach(space => {\r\n      space.selected = this.allSpacesSelected;\r\n      if (this.allSpacesSelected) {\r\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n          this.selectedSpacesForTemplate.push({ ...space });\r\n        }\r\n      } else {\r\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedSpace(space: SpacePickListItem): void {\r\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n\r\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\r\n    if (availableSpace) {\r\n      availableSpace.selected = false;\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  updateAllSpacesSelectedState(): void {\r\n    this.allSpacesSelected = this.availableSpaces.length > 0 &&\r\n      this.availableSpaces.every(space => space.selected);\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"alert alert-info mb-4\"\r\n      style=\"border-left: 4px solid #4a90e2; background-color: #f8f9ff; border-radius: 6px;\">\r\n      <div class=\"d-flex align-items-center\">\r\n        <i class=\"fas fa-info-circle text-primary me-3\" style=\"font-size: 1.2rem;\"></i>\r\n        <div>\r\n          <p class=\"mb-0 text-muted\" style=\"font-size: 0.9rem;\">\r\n            在此頁面您可以管理系統中的各個模板資訊，包括新增、編輯、刪除模板，以及設定模板名稱、狀態和包含的空間等。\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜尋條件區域 -->\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"templateName\" class=\"label col-3\">模板名稱</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"templateName\" nbInput class=\"w-full\" placeholder=\"搜尋模板名稱...\"\r\n              [(ngModel)]=\"searchKeyword\" (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"status\" class=\"label col-3\">狀態</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"status\" placeholder=\"選擇狀態...\" [(ngModel)]=\"searchStatus\" (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">啟用</nb-option>\r\n              <nb-option [value]=\"0\">停用</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢和重置按鈕 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-outline-secondary btn-sm me-2\" (click)=\"onReset()\">\r\n            <i class=\"fas fa-undo me-1\"></i>重置\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search me-1\"></i>查詢\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openCreateModal(createModal)\">\r\n            <i class=\"fas fa-plus me-1\"></i>新增模板\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模板列表表格 -->\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table\" style=\"min-width: 800px;\">\r\n        <thead>\r\n          <tr>\r\n            <th scope=\"col\" class=\"col-2\">模板類型</th>\r\n            <th scope=\"col\" class=\"col-3\">模板名稱</th>\r\n            <th scope=\"col\" class=\"col-2\">狀態</th>\r\n            <th scope=\"col\" class=\"col-3\">建立時間</th>\r\n            <th scope=\"col\" class=\"col-2\">建立者</th>\r\n            <th scope=\"col\" class=\"col-2\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let template of templateList\">\r\n            <td>\r\n              {{ template.CTemplateType === 1 ? '空間模板' : (template.CTemplateType === 2 ? '項目模板' : '-') }}\r\n            </td>\r\n            <td>{{ template.CTemplateName }}</td>\r\n            <td>\r\n              <span class=\"badge\" [ngClass]=\"template.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n              </span>\r\n            </td>\r\n            <td>{{ template.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>\r\n            <td>{{ template.CCreator || '-' }}</td>\r\n            <td class=\"table-actions\">\r\n              <button class=\"btn btn-outline-info btn-sm me-1\"\r\n                (click)=\"viewTemplateDetail(template, templateDetailModal)\">\r\n                <i class=\"fas fa-eye\"></i>查看\r\n              </button>\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-warning btn-sm me-1\"\r\n                (click)=\"openEditModal(editModal, template)\">\r\n                <i class=\"fas fa-edit\"></i>編輯\r\n              </button>\r\n              <button *ngIf=\"isDelete\" class=\"btn btn-outline-danger btn-sm\" (click)=\"deleteTemplate(template)\">\r\n                <i class=\"fas fa-trash\"></i>刪除\r\n              </button>\r\n            </td>\r\n          </tr>\r\n          <tr *ngIf=\"templateList.length === 0\">\r\n            <td colspan=\"5\" class=\"text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>目前沒有任何模板\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\" [CollectionSize]=\"totalRecords\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<!-- 新增模板模態框 -->\r\n<ng-template #createModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-plus-circle me-2 text-success\"></i>新增模板\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"templateName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"templateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                  [(ngModel)]=\"templateDetail.CTemplateName\" name=\"templateName\" (keydown.control.enter)=\"onSubmit(ref)\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板類型頁簽 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板類型\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-tabset class=\"template-type-tabs\">\r\n                  <nb-tab tabTitle=\"空間模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.SpaceTemplate\">\r\n                    <span slot=\"tabTitle\">\r\n                      <i class=\"fas fa-home me-2\"></i>空間模板\r\n                    </span>\r\n                  </nb-tab>\r\n                  <nb-tab tabTitle=\"項目模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.ItemTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.ItemTemplate\">\r\n                    <span slot=\"tabTitle\">\r\n                      <i class=\"fas fa-list me-2\"></i>項目模板\r\n                    </span>\r\n                  </nb-tab>\r\n                </nb-tabset>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板狀態 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"templateStatus\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"templateStatus\" [(ngModel)]=\"templateDetail.CStatus\" name=\"templateStatus\"\r\n                    placeholder=\"選擇狀態\" style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 選擇空間區域 (僅空間模板顯示) -->\r\n        <div class=\"col-12\" *ngIf=\"templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                選擇空間\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <!-- 搜尋區域 -->\r\n                <div class=\"row mb-3\">\r\n                  <div class=\"col-md-5\">\r\n                    <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋項目名稱...\"\r\n                      [(ngModel)]=\"spaceSearchKeyword\" (keyup.enter)=\"onSpaceSearch()\"\r\n                      style=\"height: 32px; border-radius: 4px;\" />\r\n                  </div>\r\n                  <div class=\"col-md-5\">\r\n                    <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋所屬區域...\"\r\n                      [(ngModel)]=\"spaceSearchLocation\" (keyup.enter)=\"onSpaceSearch()\"\r\n                      style=\"height: 32px; border-radius: 4px;\" />\r\n                  </div>\r\n                  <div class=\"col-md-2\">\r\n                    <button class=\"btn btn-sm btn-outline-secondary me-1\" (click)=\"onSpaceReset()\">\r\n                      <i class=\"fas fa-undo\"></i>\r\n                    </button>\r\n                    <button class=\"btn btn-sm btn-secondary\" (click)=\"onSpaceSearch()\">\r\n                      <i class=\"fas fa-search\"></i>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 可選空間列表 -->\r\n                <div class=\"border rounded p-3\" style=\"max-height: 300px; overflow-y: auto; background-color: #f8f9fa;\">\r\n                  <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <div class=\"d-flex align-items-center\">\r\n                      <input type=\"checkbox\" id=\"selectAll\" [checked]=\"allSpacesSelected\" (change)=\"toggleAllSpaces()\"\r\n                        class=\"me-2\">\r\n                      <label for=\"selectAll\" class=\"mb-0 font-weight-bold\">全選當頁空間</label>\r\n                    </div>\r\n                    <small class=\"text-muted\">\r\n                      共 {{ spaceTotalRecords }} 筆，第 {{ spacePageIndex }} / {{ Math.ceil(spaceTotalRecords /\r\n                      spacePageSize) }} 頁\r\n                    </small>\r\n                  </div>\r\n\r\n                  <div class=\"row\">\r\n                    <div class=\"col-md-6\" *ngFor=\"let space of availableSpaces\">\r\n                      <div class=\"form-check mb-2\">\r\n                        <input type=\"checkbox\" class=\"form-check-input\" [id]=\"'space-' + space.CSpaceID\"\r\n                          [checked]=\"space.selected\" (change)=\"toggleSpaceSelection(space)\">\r\n                        <label class=\"form-check-label\" [for]=\"'space-' + space.CSpaceID\">\r\n                          {{ space.CPart }}\r\n                          <small class=\"text-muted d-block\">{{ space.CLocation || '-' }}</small>\r\n                        </label>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- 空間列表為空時的提示 -->\r\n                  <div *ngIf=\"availableSpaces.length === 0\" class=\"text-center text-muted py-3\">\r\n                    <i class=\"fas fa-info-circle me-2\"></i>沒有符合條件的空間\r\n                  </div>\r\n\r\n                  <!-- 分頁 -->\r\n                  <div class=\"d-flex justify-content-center mt-3\" *ngIf=\"spaceTotalRecords > spacePageSize\">\r\n                    <ngx-pagination [(Page)]=\"spacePageIndex\" [PageSize]=\"spacePageSize\"\r\n                      [CollectionSize]=\"spaceTotalRecords\" (PageChange)=\"spacePageChanged($event)\">\r\n                    </ngx-pagination>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 已選空間 -->\r\n                <div class=\"mt-3\" *ngIf=\"selectedSpacesForTemplate.length > 0\">\r\n                  <label class=\"mb-2 font-weight-bold\">已選擇的空間 ({{ selectedSpacesForTemplate.length }})</label>\r\n                  <div class=\"border rounded p-2\" style=\"max-height: 150px; overflow-y: auto;\">\r\n                    <span class=\"badge badge-primary me-1 mb-1\" *ngFor=\"let space of selectedSpacesForTemplate\">\r\n                      {{ space.CPart }}\r\n                      <button type=\"button\" class=\"btn-close btn-close-white ms-1\" (click)=\"removeSelectedSpace(space)\"\r\n                        style=\"font-size: 0.7rem;\"></button>\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 項目模板專用欄位 -->\r\n        <div class=\"col-12\" *ngIf=\"templateDetail.CTemplateType === EnumTemplateType.ItemTemplate\">\r\n          <div class=\"row\">\r\n            <!-- 單價 -->\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-4\">\r\n                <div class=\"d-flex align-items-start\">\r\n                  <label for=\"unitPrice\" class=\"mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                    單價\r\n                  </label>\r\n                  <div class=\"flex-grow-1 ml-3\">\r\n                    <input type=\"number\" id=\"unitPrice\" class=\"form-control\" nbInput placeholder=\"請輸入單價\"\r\n                      style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 單位 -->\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-4\">\r\n                <div class=\"d-flex align-items-start\">\r\n                  <label for=\"unit\" class=\"mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                    單位\r\n                  </label>\r\n                  <div class=\"flex-grow-1 ml-3\">\r\n                    <input type=\"text\" id=\"unit\" class=\"form-control\" nbInput placeholder=\"請輸入單位\"\r\n                      style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-check me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 編輯模板模態框 -->\r\n<ng-template #editModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-edit me-2 text-warning\"></i>編輯模板\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"editTemplateName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"editTemplateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                  [(ngModel)]=\"templateDetail.CTemplateName\" name=\"editTemplateName\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板類型頁簽 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板類型\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-tabset class=\"template-type-tabs\">\r\n                  <nb-tab tabTitle=\"空間模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.SpaceTemplate\">\r\n                    <span slot=\"tabTitle\">\r\n                      <i class=\"fas fa-home me-2\"></i>空間模板\r\n                    </span>\r\n                  </nb-tab>\r\n                  <nb-tab tabTitle=\"項目模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.ItemTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.ItemTemplate\">\r\n                    <span slot=\"tabTitle\">\r\n                      <i class=\"fas fa-list me-2\"></i>項目模板\r\n                    </span>\r\n                  </nb-tab>\r\n                </nb-tabset>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板狀態 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"editTemplateStatus\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"editTemplateStatus\" [(ngModel)]=\"templateDetail.CStatus\" name=\"editTemplateStatus\"\r\n                    placeholder=\"選擇狀態\" style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 項目模板專用欄位 -->\r\n        <div class=\"col-12\" *ngIf=\"templateDetail.CTemplateType === EnumTemplateType.ItemTemplate\">\r\n          <div class=\"row\">\r\n            <!-- 單價 -->\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-4\">\r\n                <div class=\"d-flex align-items-start\">\r\n                  <label for=\"editUnitPrice\" class=\"mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                    單價\r\n                  </label>\r\n                  <div class=\"flex-grow-1 ml-3\">\r\n                    <input type=\"number\" id=\"editUnitPrice\" class=\"form-control\" nbInput placeholder=\"請輸入單價\"\r\n                      style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 單位 -->\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-4\">\r\n                <div class=\"d-flex align-items-start\">\r\n                  <label for=\"editUnit\" class=\"mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                    單位\r\n                  </label>\r\n                  <div class=\"flex-grow-1 ml-3\">\r\n                    <input type=\"text\" id=\"editUnit\" class=\"form-control\" nbInput placeholder=\"請輸入單位\"\r\n                      style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-save me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 查看模板明細模態框 -->\r\n<ng-template #templateDetailModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-eye me-2 text-info\"></i>模板明細\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <!-- 模板基本資訊 -->\r\n      <div class=\"card mb-4\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header\" style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-info-circle me-2 text-primary\"></i>基本資訊\r\n          </h6>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div class=\"row\">\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-tag me-2 text-primary\"></i>模板名稱\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CTemplateName || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-toggle-on me-2 text-success\"></i>狀態\r\n                </label>\r\n                <p class=\"mb-0\">\r\n                  <span class=\"badge\"\r\n                    [ngClass]=\"selectedTemplateDetail?.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                    <i [class]=\"selectedTemplateDetail?.CStatus === 1 ? 'fas fa-check-circle' : 'fas fa-times-circle'\"\r\n                      class=\"me-1\"></i>\r\n                    {{ selectedTemplateDetail?.CStatus === 1 ? '啟用' : '停用' }}\r\n                  </span>\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-plus me-2 text-info\"></i>建立時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CCreateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-plus me-2 text-warning\"></i>建立者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CCreator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-edit me-2 text-info\"></i>更新時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CUpdateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-edit me-2 text-warning\"></i>更新者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CUpdator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 包含的空間列表 -->\r\n      <div class=\"card\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header d-flex justify-content-between align-items-center\"\r\n          style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-home me-2 text-success\"></i>包含的空間\r\n          </h6>\r\n          <span class=\"badge badge-info\">共 {{ templateDetailSpaces.length }} 個空間</span>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <!-- Loading 狀態 -->\r\n          <div *ngIf=\"isLoadingTemplateDetail\" class=\"text-center py-4\">\r\n            <i class=\"fas fa-spinner fa-spin me-2 text-primary\" style=\"font-size: 1.2rem;\"></i>\r\n            <span class=\"text-muted\">載入中...</span>\r\n          </div>\r\n\r\n          <!-- 空間列表 -->\r\n          <div *ngIf=\"!isLoadingTemplateDetail\">\r\n            <div class=\"table-responsive\" *ngIf=\"templateDetailSpaces.length > 0\">\r\n              <table class=\"table table-sm\">\r\n                <thead>\r\n                  <tr>\r\n                    <th scope=\"col\" class=\"col-1\">#</th>\r\n                    <th scope=\"col\" class=\"col-7\">項目名稱</th>\r\n                    <th scope=\"col\" class=\"col-4\">所屬區域</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let space of templateDetailSpaces; let i = index\">\r\n                    <td>{{ i + 1 }}</td>\r\n                    <td>\r\n                      <i class=\"fas fa-home me-2 text-muted\"></i>{{ space.CPart }}\r\n                    </td>\r\n                    <td>\r\n                      <i class=\"fas fa-map-marker-alt me-2 text-muted\"></i>{{ space.CLocation || '-' }}\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n\r\n            <!-- 沒有空間時的提示 -->\r\n            <div *ngIf=\"templateDetailSpaces.length === 0\" class=\"text-center text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>此模板尚未包含任何空間\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-secondary px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>關閉\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qCAAqC;AAEnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gCAAgC;AAO7D,SAASC,GAAG,QAAQ,MAAM;AAO1B,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;;;;;;;;;;;;;;;;;IC2CrFC,EAAA,CAAAC,cAAA,iBAAiG;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,cAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,eAAA,CAAAH,cAAA,CAA4B;IAAA,EAAC;IAC9FR,EAAA,CAAAY,SAAA,YAAgC;IAAAZ,EAAA,CAAAa,MAAA,gCAClC;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAoCLd,EAAA,CAAAC,cAAA,iBAC+C;IAA7CD,EAAA,CAAAE,UAAA,mBAAAa,mEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAY,YAAA,GAAAnB,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAc,aAAA,CAAAD,YAAA,EAAAF,WAAA,CAAkC;IAAA,EAAC;IAC5CjB,EAAA,CAAAY,SAAA,YAA2B;IAAAZ,EAAA,CAAAa,MAAA,oBAC7B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IACTd,EAAA,CAAAC,cAAA,iBAAkG;IAAnCD,EAAA,CAAAE,UAAA,mBAAAmB,mEAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,IAAA;MAAA,MAAAL,WAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiB,cAAA,CAAAN,WAAA,CAAwB;IAAA,EAAC;IAC/FjB,EAAA,CAAAY,SAAA,YAA4B;IAAAZ,EAAA,CAAAa,MAAA,oBAC9B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAtBXd,EADF,CAAAC,cAAA,SAA0C,SACpC;IACFD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEnCd,EADF,CAAAC,cAAA,SAAI,eAC2F;IAC3FD,EAAA,CAAAa,MAAA,GACF;IACFb,EADE,CAAAc,YAAA,EAAO,EACJ;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAmD;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC5Dd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,IAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAErCd,EADF,CAAAC,cAAA,cAA0B,kBAEsC;IAA5DD,EAAA,CAAAE,UAAA,mBAAAsB,0DAAA;MAAA,MAAAP,WAAA,GAAAjB,EAAA,CAAAI,aAAA,CAAAqB,GAAA,EAAAP,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAmB,sBAAA,GAAA1B,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAqB,kBAAA,CAAAV,WAAA,EAAAS,sBAAA,CAAiD;IAAA,EAAC;IAC3D1B,EAAA,CAAAY,SAAA,aAA0B;IAAAZ,EAAA,CAAAa,MAAA,qBAC5B;IAAAb,EAAA,CAAAc,YAAA,EAAS;IAKTd,EAJA,CAAA4B,UAAA,KAAAC,0CAAA,qBAC+C,KAAAC,0CAAA,qBAGmD;IAItG9B,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAvBDd,EAAA,CAAA+B,SAAA,GACF;IADE/B,EAAA,CAAAgC,kBAAA,MAAAf,WAAA,CAAAgB,aAAA,sCAAAhB,WAAA,CAAAgB,aAAA,+CACF;IACIjC,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAkC,iBAAA,CAAAjB,WAAA,CAAAkB,aAAA,CAA4B;IAEVnC,EAAA,CAAA+B,SAAA,GAAwE;IAAxE/B,EAAA,CAAAoC,UAAA,YAAAnB,WAAA,CAAAoB,OAAA,6CAAwE;IAC1FrC,EAAA,CAAA+B,SAAA,EACF;IADE/B,EAAA,CAAAgC,kBAAA,MAAAf,WAAA,CAAAoB,OAAA,8CACF;IAEErC,EAAA,CAAA+B,SAAA,GAAmD;IAAnD/B,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAsC,WAAA,QAAArB,WAAA,CAAAsB,SAAA,sBAAmD;IACnDvC,EAAA,CAAA+B,SAAA,GAA8B;IAA9B/B,EAAA,CAAAkC,iBAAA,CAAAjB,WAAA,CAAAuB,QAAA,QAA8B;IAMvBxC,EAAA,CAAA+B,SAAA,GAAc;IAAd/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAmC,QAAA,CAAc;IAIdzC,EAAA,CAAA+B,SAAA,EAAc;IAAd/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAoC,QAAA,CAAc;;;;;IAMzB1C,EADF,CAAAC,cAAA,SAAsC,aACI;IACtCD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,wDACzC;IACFb,EADE,CAAAc,YAAA,EAAK,EACF;;;;;;IAmJSd,EAFJ,CAAAC,cAAA,aAA4D,cAC7B,gBAEyC;IAAvCD,EAAA,CAAAE,UAAA,oBAAAyC,gFAAA;MAAA,MAAAC,SAAA,GAAA5C,EAAA,CAAAI,aAAA,CAAAyC,IAAA,EAAA3B,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAAwC,oBAAA,CAAAF,SAAA,CAA2B;IAAA,EAAC;IADnE5C,EAAA,CAAAc,YAAA,EACoE;IACpEd,EAAA,CAAAC,cAAA,gBAAkE;IAChED,EAAA,CAAAa,MAAA,GACA;IAAAb,EAAA,CAAAC,cAAA,iBAAkC;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IAGpEb,EAHoE,CAAAc,YAAA,EAAQ,EAChE,EACJ,EACF;;;;IAP8Cd,EAAA,CAAA+B,SAAA,GAAgC;IAC9E/B,EAD8C,CAAAoC,UAAA,kBAAAQ,SAAA,CAAAG,QAAA,CAAgC,YAAAH,SAAA,CAAAI,QAAA,CACpD;IACIhD,EAAA,CAAA+B,SAAA,EAAiC;IAAjC/B,EAAA,CAAAoC,UAAA,mBAAAQ,SAAA,CAAAG,QAAA,CAAiC;IAC/D/C,EAAA,CAAA+B,SAAA,EACA;IADA/B,EAAA,CAAAgC,kBAAA,MAAAY,SAAA,CAAAK,KAAA,MACA;IAAkCjD,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAkC,iBAAA,CAAAU,SAAA,CAAAM,SAAA,QAA4B;;;;;IAOtElD,EAAA,CAAAC,cAAA,eAA8E;IAC5ED,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,8DACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;;IAIJd,EADF,CAAAC,cAAA,eAA0F,yBAET;IAD/DD,EAAA,CAAAmD,gBAAA,wBAAAC,6FAAAC,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAkD,cAAA,EAAAH,MAAA,MAAA/C,MAAA,CAAAkD,cAAA,GAAAH,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAyB;IACFrD,EAAA,CAAAE,UAAA,wBAAAkD,6FAAAC,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAcJ,MAAA,CAAAmD,gBAAA,CAAAJ,MAAA,CAAwB;IAAA,EAAC;IAEhFrD,EADE,CAAAc,YAAA,EAAiB,EACb;;;;IAHYd,EAAA,CAAA+B,SAAA,EAAyB;IAAzB/B,EAAA,CAAA0D,gBAAA,SAAApD,MAAA,CAAAkD,cAAA,CAAyB;IACvCxD,EADwC,CAAAoC,UAAA,aAAA9B,MAAA,CAAAqD,aAAA,CAA0B,mBAAArD,MAAA,CAAAsD,iBAAA,CAC9B;;;;;;IAStC5D,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAa,MAAA,GACA;IAAAb,EAAA,CAAAC,cAAA,kBAC6B;IADgCD,EAAA,CAAAE,UAAA,mBAAA2D,uFAAA;MAAA,MAAAC,SAAA,GAAA9D,EAAA,CAAAI,aAAA,CAAA2D,IAAA,EAAA7C,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA0D,mBAAA,CAAAF,SAAA,CAA0B;IAAA,EAAC;IAEnG9D,EAD+B,CAAAc,YAAA,EAAS,EACjC;;;;IAHLd,EAAA,CAAA+B,SAAA,EACA;IADA/B,EAAA,CAAAgC,kBAAA,MAAA8B,SAAA,CAAAb,KAAA,MACA;;;;;IAJJjD,EADF,CAAAC,cAAA,eAA+D,iBACxB;IAAAD,EAAA,CAAAa,MAAA,GAA+C;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC5Fd,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAA4B,UAAA,IAAAqC,8DAAA,oBAA4F;IAMhGjE,EADE,CAAAc,YAAA,EAAM,EACF;;;;IARiCd,EAAA,CAAA+B,SAAA,GAA+C;IAA/C/B,EAAA,CAAAgC,kBAAA,2CAAA1B,MAAA,CAAA4D,yBAAA,CAAAC,MAAA,MAA+C;IAEpBnE,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAA4D,yBAAA,CAA4B;;;;;;IAtEhGlE,EAHN,CAAAC,cAAA,cAA4F,cAC7D,cACW,gBAC4D;IAC9FD,EAAA,CAAAa,MAAA,iCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAKFd,EAJN,CAAAC,cAAA,cAA8B,cAEN,cACE,gBAG0B;IAD5CD,EAAA,CAAAmD,gBAAA,2BAAAiB,gFAAAf,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAgE,kBAAA,EAAAjB,MAAA,MAAA/C,MAAA,CAAAgE,kBAAA,GAAAjB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAgC;IAACrD,EAAA,CAAAE,UAAA,yBAAAqE,8EAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAAkE,aAAA,EAAe;IAAA,EAAC;IAEpExE,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,cAAsB,iBAG0B;IAD5CD,EAAA,CAAAmD,gBAAA,2BAAAsB,iFAAApB,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAoE,mBAAA,EAAArB,MAAA,MAAA/C,MAAA,CAAAoE,mBAAA,GAAArB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAiC;IAACrD,EAAA,CAAAE,UAAA,yBAAAyE,+EAAA;MAAA3E,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAAkE,aAAA,EAAe;IAAA,EAAC;IAErExE,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,eAAsB,kBAC2D;IAAzBD,EAAA,CAAAE,UAAA,mBAAA0E,0EAAA;MAAA5E,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAuE,YAAA,EAAc;IAAA,EAAC;IAC5E7E,EAAA,CAAAY,SAAA,aAA2B;IAC7BZ,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAAmE;IAA1BD,EAAA,CAAAE,UAAA,mBAAA4E,0EAAA;MAAA9E,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAkE,aAAA,EAAe;IAAA,EAAC;IAChExE,EAAA,CAAAY,SAAA,aAA6B;IAGnCZ,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAwG,eAClC,cAC3B,iBAEtB;IADqDD,EAAA,CAAAE,UAAA,oBAAA6E,0EAAA;MAAA/E,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAA0E,eAAA,EAAiB;IAAA,EAAC;IAAhGhF,EAAA,CAAAc,YAAA,EACe;IACfd,EAAA,CAAAC,cAAA,iBAAqD;IAAAD,EAAA,CAAAa,MAAA,4CAAM;IAC7Db,EAD6D,CAAAc,YAAA,EAAQ,EAC/D;IACNd,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAa,MAAA,IAEF;IACFb,EADE,CAAAc,YAAA,EAAQ,EACJ;IAENd,EAAA,CAAAC,cAAA,eAAiB;IACfD,EAAA,CAAA4B,UAAA,KAAAqD,uDAAA,kBAA4D;IAU9DjF,EAAA,CAAAc,YAAA,EAAM;IAQNd,EALA,CAAA4B,UAAA,KAAAsD,uDAAA,kBAA8E,KAAAC,uDAAA,kBAKY;IAK5FnF,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAA4B,UAAA,KAAAwD,uDAAA,kBAA+D;IAavEpF,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACF;;;;IAxEQd,EAAA,CAAA+B,SAAA,GAAgC;IAAhC/B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAAgE,kBAAA,CAAgC;IAKhCtE,EAAA,CAAA+B,SAAA,GAAiC;IAAjC/B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAAoE,mBAAA,CAAiC;IAiBK1E,EAAA,CAAA+B,SAAA,GAA6B;IAA7B/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAA+E,iBAAA,CAA6B;IAKnErF,EAAA,CAAA+B,SAAA,GAEF;IAFE/B,EAAA,CAAAsF,kBAAA,aAAAhF,MAAA,CAAAsD,iBAAA,0BAAAtD,MAAA,CAAAkD,cAAA,SAAAlD,MAAA,CAAAiF,IAAA,CAAAC,IAAA,CAAAlF,MAAA,CAAAsD,iBAAA,GAAAtD,MAAA,CAAAqD,aAAA,cAEF;IAIwC3D,EAAA,CAAA+B,SAAA,GAAkB;IAAlB/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAAmF,eAAA,CAAkB;IAatDzF,EAAA,CAAA+B,SAAA,EAAkC;IAAlC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAmF,eAAA,CAAAtB,MAAA,OAAkC;IAKSnE,EAAA,CAAA+B,SAAA,EAAuC;IAAvC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAsD,iBAAA,GAAAtD,MAAA,CAAAqD,aAAA,CAAuC;IAQvE3D,EAAA,CAAA+B,SAAA,EAA0C;IAA1C/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA4D,yBAAA,CAAAC,MAAA,KAA0C;;;;;IAsB3DnE,EANV,CAAAC,cAAA,cAA2F,cACxE,aAEO,cACS,cACW,iBAC6D;IAC/FD,EAAA,CAAAa,MAAA,qBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAY,SAAA,iBACyE;IAIjFZ,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,aAAsB,eACS,eACW,kBACwD;IAC1FD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAY,SAAA,kBACyE;IAMrFZ,EALU,CAAAc,YAAA,EAAM,EACF,EACF,EACF,EACF,EACF;;;;;;IA3MRd,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,YAAoD;IAAAZ,EAAA,CAAAa,MAAA,gCACtD;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAwF,kEAAA;MAAA,MAAAC,OAAA,GAAA3F,EAAA,CAAAI,aAAA,CAAAwF,IAAA,EAAAC,SAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAwF,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAE5F3F,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAQPd,EANV,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAAmD,gBAAA,2BAAA4C,0EAAA1C,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAwF,IAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAA0F,cAAA,CAAA7D,aAAA,EAAAkB,MAAA,MAAA/C,MAAA,CAAA0F,cAAA,CAAA7D,aAAA,GAAAkB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAA0C;IAAqBrD,EAAA,CAAAE,UAAA,mCAAA+F,kFAAA;MAAA,MAAAN,OAAA,GAAA3F,EAAA,CAAAI,aAAA,CAAAwF,IAAA,EAAAC,SAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAyBJ,MAAA,CAAA4F,QAAA,CAAAP,OAAA,CAAa;IAAA,EAAC;IAKhH3F,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAC4D;IAC9FD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,qBACU,kBAEsC;IAAxED,EAAA,CAAAE,UAAA,mBAAAiG,mEAAA;MAAAnG,EAAA,CAAAI,aAAA,CAAAwF,IAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAA0F,cAAA,CAAA/D,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAAsG,aAAA;IAAA,EAAuE;IACvEpG,EAAA,CAAAC,cAAA,gBAAsB;IACpBD,EAAA,CAAAY,SAAA,aAAgC;IAAAZ,EAAA,CAAAa,MAAA,iCAClC;IACFb,EADE,CAAAc,YAAA,EAAO,EACA;IACTd,EAAA,CAAAC,cAAA,kBACyE;IAAvED,EAAA,CAAAE,UAAA,mBAAAmG,mEAAA;MAAArG,EAAA,CAAAI,aAAA,CAAAwF,IAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAA0F,cAAA,CAAA/D,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAAwG,YAAA;IAAA,EAAsE;IACtEtG,EAAA,CAAAC,cAAA,gBAAsB;IACpBD,EAAA,CAAAY,SAAA,aAAgC;IAAAZ,EAAA,CAAAa,MAAA,iCAClC;IAMZb,EANY,CAAAc,YAAA,EAAO,EACA,EACC,EACR,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,qBAEe;IADZD,EAAA,CAAAmD,gBAAA,2BAAAoD,8EAAAlD,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAwF,IAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAA0F,cAAA,CAAA3D,OAAA,EAAAgB,MAAA,MAAA/C,MAAA,CAAA0F,cAAA,CAAA3D,OAAA,GAAAgB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAoC;IAG/DrD,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IAOdb,EAPc,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF;IAyFNd,EAtFA,CAAA4B,UAAA,KAAA4E,gDAAA,oBAA4F,KAAAC,gDAAA,mBAsFD;IAkC/FzG,EADE,CAAAc,YAAA,EAAM,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAAwG,mEAAA;MAAA,MAAAf,OAAA,GAAA3F,EAAA,CAAAI,aAAA,CAAAwF,IAAA,EAAAC,SAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAwF,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAExE3F,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAAyG,mEAAA;MAAA,MAAAhB,OAAA,GAAA3F,EAAA,CAAAI,aAAA,CAAAwF,IAAA,EAAAC,SAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA4F,QAAA,CAAAP,OAAA,CAAa;IAAA,EAAC;IAE1D3F,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IApMMd,EAAA,CAAA+B,SAAA,IAA0C;IAA1C/B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAA0F,cAAA,CAAA7D,aAAA,CAA0C;IAgBlBnC,EAAA,CAAA+B,SAAA,GAA0E;IAA1E/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAA0F,cAAA,CAAA/D,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAsG,aAAA,CAA0E;IAM1EpG,EAAA,CAAA+B,SAAA,GAAyE;IAAzE/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAA0F,cAAA,CAAA/D,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAwG,YAAA,CAAyE;IAsBlEtG,EAAA,CAAA+B,SAAA,IAAoC;IAApC/B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAA0F,cAAA,CAAA3D,OAAA,CAAoC;IAEtDrC,EAAA,CAAA+B,SAAA,EAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;IAKXpC,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;IAabpC,EAAA,CAAA+B,SAAA,GAAqE;IAArE/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA0F,cAAA,CAAA/D,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAsG,aAAA,CAAqE;IAsFrEpG,EAAA,CAAA+B,SAAA,EAAoE;IAApE/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA0F,cAAA,CAAA/D,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAwG,YAAA,CAAoE;;;;;IAgJ/EtG,EANV,CAAAC,cAAA,cAA2F,cACxE,aAEO,cACS,cACW,iBACiE;IACnGD,EAAA,CAAAa,MAAA,qBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAY,SAAA,iBACyE;IAIjFZ,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,aAAsB,eACS,eACW,kBAC4D;IAC9FD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAY,SAAA,kBACyE;IAMrFZ,EALU,CAAAc,YAAA,EAAM,EACF,EACF,EACF,EACF,EACF;;;;;;IArHRd,EAFJ,CAAAC,cAAA,mBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,aAA6C;IAAAZ,EAAA,CAAAa,MAAA,gCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAA0G,kEAAA;MAAA,MAAAC,OAAA,GAAA7G,EAAA,CAAAI,aAAA,CAAA0G,IAAA,EAAAjB,SAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAwF,OAAA,CAAAe,OAAA,CAAY;IAAA,EAAC;IAE5F7G,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAQPd,EANV,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACW,kBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,kBAG6C;IADvED,EAAA,CAAAmD,gBAAA,2BAAA4D,0EAAA1D,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAA0F,cAAA,CAAA7D,aAAA,EAAAkB,MAAA,MAAA/C,MAAA,CAAA0F,cAAA,CAAA7D,aAAA,GAAAkB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAA0C;IAKpDrD,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAC4D;IAC9FD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,qBACU,kBAEsC;IAAxED,EAAA,CAAAE,UAAA,mBAAA8G,mEAAA;MAAAhH,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAA0F,cAAA,CAAA/D,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAAsG,aAAA;IAAA,EAAuE;IACvEpG,EAAA,CAAAC,cAAA,gBAAsB;IACpBD,EAAA,CAAAY,SAAA,aAAgC;IAAAZ,EAAA,CAAAa,MAAA,iCAClC;IACFb,EADE,CAAAc,YAAA,EAAO,EACA;IACTd,EAAA,CAAAC,cAAA,kBACyE;IAAvED,EAAA,CAAAE,UAAA,mBAAA+G,mEAAA;MAAAjH,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAA0F,cAAA,CAAA/D,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAAwG,YAAA;IAAA,EAAsE;IACtEtG,EAAA,CAAAC,cAAA,gBAAsB;IACpBD,EAAA,CAAAY,SAAA,aAAgC;IAAAZ,EAAA,CAAAa,MAAA,iCAClC;IAMZb,EANY,CAAAc,YAAA,EAAO,EACA,EACC,EACR,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,kBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,sBAEe;IADRD,EAAA,CAAAmD,gBAAA,2BAAA+D,8EAAA7D,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAA0F,cAAA,CAAA3D,OAAA,EAAAgB,MAAA,MAAA/C,MAAA,CAAA0F,cAAA,CAAA3D,OAAA,GAAAgB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAoC;IAGnErD,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IAOdb,EAPc,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF;IAGNd,EAAA,CAAA4B,UAAA,KAAAuF,gDAAA,mBAA2F;IAkC/FnH,EADE,CAAAc,YAAA,EAAM,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAAkH,mEAAA;MAAA,MAAAP,OAAA,GAAA7G,EAAA,CAAAI,aAAA,CAAA0G,IAAA,EAAAjB,SAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAwF,OAAA,CAAAe,OAAA,CAAY;IAAA,EAAC;IAExE7G,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAAmH,mEAAA;MAAA,MAAAR,OAAA,GAAA7G,EAAA,CAAAI,aAAA,CAAA0G,IAAA,EAAAjB,SAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA4F,QAAA,CAAAW,OAAA,CAAa;IAAA,EAAC;IAE1D7G,EAAA,CAAAY,SAAA,cAAgC;IAAAZ,EAAA,CAAAa,MAAA,qBAClC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA9GMd,EAAA,CAAA+B,SAAA,IAA0C;IAA1C/B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAA0F,cAAA,CAAA7D,aAAA,CAA0C;IAgBlBnC,EAAA,CAAA+B,SAAA,GAA0E;IAA1E/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAA0F,cAAA,CAAA/D,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAsG,aAAA,CAA0E;IAM1EpG,EAAA,CAAA+B,SAAA,GAAyE;IAAzE/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAA0F,cAAA,CAAA/D,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAwG,YAAA,CAAyE;IAsB9DtG,EAAA,CAAA+B,SAAA,IAAoC;IAApC/B,EAAA,CAAA0D,gBAAA,YAAApD,MAAA,CAAA0F,cAAA,CAAA3D,OAAA,CAAoC;IAE1DrC,EAAA,CAAA+B,SAAA,EAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;IAKXpC,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;IAabpC,EAAA,CAAA+B,SAAA,GAAoE;IAApE/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA0F,cAAA,CAAA/D,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAwG,YAAA,CAAoE;;;;;IA8IvFtG,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAY,SAAA,aAAmF;IACnFZ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAa,MAAA,4BAAM;IACjCb,EADiC,CAAAc,YAAA,EAAO,EAClC;;;;;IAeId,EADF,CAAAC,cAAA,SAA8D,SACxD;IAAAD,EAAA,CAAAa,MAAA,GAAW;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpBd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAA2C;IAAAZ,EAAA,CAAAa,MAAA,GAC7C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,GACvD;IACFb,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAPCd,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAkC,iBAAA,CAAAoF,KAAA,KAAW;IAE8BtH,EAAA,CAAA+B,SAAA,GAC7C;IAD6C/B,EAAA,CAAAgC,kBAAA,KAAAuF,SAAA,CAAAtE,KAAA,MAC7C;IAEuDjD,EAAA,CAAA+B,SAAA,GACvD;IADuD/B,EAAA,CAAAgC,kBAAA,KAAAuF,SAAA,CAAArE,SAAA,aACvD;;;;;IAbAlD,EAJR,CAAAC,cAAA,eAAsE,iBACtC,YACrB,SACD,cAC4B;IAAAD,EAAA,CAAAa,MAAA,QAAC;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAEtCb,EAFsC,CAAAc,YAAA,EAAK,EACpC,EACC;IACRd,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA4B,UAAA,KAAA4F,4DAAA,iBAA8D;IAWpExH,EAFI,CAAAc,YAAA,EAAQ,EACF,EACJ;;;;IAXsBd,EAAA,CAAA+B,SAAA,IAAyB;IAAzB/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAAmH,oBAAA,CAAyB;;;;;IAcrDzH,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,0EACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IA3BRd,EAAA,CAAAC,cAAA,UAAsC;IAyBpCD,EAxBA,CAAA4B,UAAA,IAAA8F,sDAAA,oBAAsE,IAAAC,sDAAA,mBAwBa;IAGrF3H,EAAA,CAAAc,YAAA,EAAM;;;;IA3B2Bd,EAAA,CAAA+B,SAAA,EAAqC;IAArC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAmH,oBAAA,CAAAtD,MAAA,KAAqC;IAwB9DnE,EAAA,CAAA+B,SAAA,EAAuC;IAAvC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAmH,oBAAA,CAAAtD,MAAA,OAAuC;;;;;;IAxHnDnE,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,aAAyC;IAAAZ,EAAA,CAAAa,MAAA,gCAC3C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAA0H,kEAAA;MAAA,MAAAC,OAAA,GAAA7H,EAAA,CAAAI,aAAA,CAAA0H,IAAA,EAAAjC,SAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAwF,OAAA,CAAA+B,OAAA,CAAY;IAAA,EAAC;IAE5F7H,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAMXd,EAJN,CAAAC,cAAA,uBAAgC,eAEgD,eACkB,eAChD;IAC1CD,EAAA,CAAAY,SAAA,cAAoD;IAAAZ,EAAA,CAAAa,MAAA,iCACtD;IACFb,EADE,CAAAc,YAAA,EAAK,EACD;IAKEd,EAJR,CAAAC,cAAA,gBAAuB,eACJ,cACO,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAA4C;IAAAZ,EAAA,CAAAa,MAAA,iCAC9C;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAAkD;IAEtEb,EAFsE,CAAAc,YAAA,EAAI,EAClE,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,qBACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,cAAgB,gBAE4E;IACxFD,EAAA,CAAAY,SAAA,cACmB;IACnBZ,EAAA,CAAAa,MAAA,IACF;IAGNb,EAHM,CAAAc,YAAA,EAAO,EACL,EACA,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAmD;IAAAZ,EAAA,CAAAa,MAAA,iCACrD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,2BACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAEjEb,EAFiE,CAAAc,YAAA,EAAI,EAC7D,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAmD;IAAAZ,EAAA,CAAAa,MAAA,iCACrD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,2BACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAKvEb,EALuE,CAAAc,YAAA,EAAI,EAC7D,EACF,EACF,EACF,EACF;IAMFd,EAHJ,CAAAC,cAAA,gBAAyE,gBAEA,eACzB;IAC1CD,EAAA,CAAAY,SAAA,cAA6C;IAAAZ,EAAA,CAAAa,MAAA,uCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAa,MAAA,IAAuC;IACxEb,EADwE,CAAAc,YAAA,EAAO,EACzE;IACNd,EAAA,CAAAC,cAAA,gBAAuB;IAQrBD,EANA,CAAA4B,UAAA,KAAAmG,gDAAA,mBAA8D,KAAAC,gDAAA,kBAMxB;IA+B5ChI,EAFI,CAAAc,YAAA,EAAM,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,mBAEjD;IADtBD,EAAA,CAAAE,UAAA,mBAAA+H,mEAAA;MAAA,MAAAJ,OAAA,GAAA7H,EAAA,CAAAI,aAAA,CAAA0H,IAAA,EAAAjC,SAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAwF,OAAA,CAAA+B,OAAA,CAAY;IAAA,EAAC;IAE3D7H,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA9GoBd,EAAA,CAAA+B,SAAA,IAAkD;IAAlD/B,EAAA,CAAAkC,iBAAA,EAAA5B,MAAA,CAAA4H,sBAAA,kBAAA5H,MAAA,CAAA4H,sBAAA,CAAA/F,aAAA,SAAkD;IAU9DnC,EAAA,CAAA+B,SAAA,GAAuF;IAAvF/B,EAAA,CAAAoC,UAAA,aAAA9B,MAAA,CAAA4H,sBAAA,kBAAA5H,MAAA,CAAA4H,sBAAA,CAAA7F,OAAA,8CAAuF;IACpFrC,EAAA,CAAA+B,SAAA,EAA+F;IAA/F/B,EAAA,CAAAmI,UAAA,EAAA7H,MAAA,CAAA4H,sBAAA,kBAAA5H,MAAA,CAAA4H,sBAAA,CAAA7F,OAAA,wDAA+F;IAElGrC,EAAA,CAAA+B,SAAA,EACF;IADE/B,EAAA,CAAAgC,kBAAA,OAAA1B,MAAA,CAAA4H,sBAAA,kBAAA5H,MAAA,CAAA4H,sBAAA,CAAA7F,OAAA,+CACF;IAScrC,EAAA,CAAA+B,SAAA,GAA2E;IAA3E/B,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAsC,WAAA,SAAAhC,MAAA,CAAA4H,sBAAA,kBAAA5H,MAAA,CAAA4H,sBAAA,CAAA3F,SAAA,6BAA2E;IAQ3EvC,EAAA,CAAA+B,SAAA,GAA6C;IAA7C/B,EAAA,CAAAkC,iBAAA,EAAA5B,MAAA,CAAA4H,sBAAA,kBAAA5H,MAAA,CAAA4H,sBAAA,CAAA1F,QAAA,SAA6C;IAQ7CxC,EAAA,CAAA+B,SAAA,GAA2E;IAA3E/B,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAsC,WAAA,SAAAhC,MAAA,CAAA4H,sBAAA,kBAAA5H,MAAA,CAAA4H,sBAAA,CAAAE,SAAA,6BAA2E;IAQ3EpI,EAAA,CAAA+B,SAAA,GAA6C;IAA7C/B,EAAA,CAAAkC,iBAAA,EAAA5B,MAAA,CAAA4H,sBAAA,kBAAA5H,MAAA,CAAA4H,sBAAA,CAAAG,QAAA,SAA6C;IAcpCrI,EAAA,CAAA+B,SAAA,GAAuC;IAAvC/B,EAAA,CAAAgC,kBAAA,YAAA1B,MAAA,CAAAmH,oBAAA,CAAAtD,MAAA,wBAAuC;IAIhEnE,EAAA,CAAA+B,SAAA,GAA6B;IAA7B/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAgI,uBAAA,CAA6B;IAM7BtI,EAAA,CAAA+B,SAAA,EAA8B;IAA9B/B,EAAA,CAAAoC,UAAA,UAAA9B,MAAA,CAAAgI,uBAAA,CAA8B;;;ADhhB9C,OAAM,MAAOC,iBAAkB,SAAQ7I,aAAa;EASlD8I,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,gBAAiC,EACjCC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAdf,KAAAvD,IAAI,GAAGA,IAAI,CAAC,CAAC;IACb,KAAAzF,gBAAgB,GAAGA,gBAAgB,CAAC,CAAC;IACrC,KAAAC,sBAAsB,GAAGA,sBAAsB,CAAC,CAAC;IAiBxC,KAAAgJ,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB;IACA,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAAnD,cAAc,GAAqB,EAAE;IACrC,KAAAoD,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAkB,IAAI;IAElC;IACA,KAAA5D,eAAe,GAAwB,EAAE;IACzC,KAAAvB,yBAAyB,GAAwB,EAAE;IACnD,KAAAI,kBAAkB,GAAW,EAAE;IAC/B,KAAAI,mBAAmB,GAAW,EAAE;IAChC,KAAAlB,cAAc,GAAG,CAAC;IAClB,KAAAG,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAyB,iBAAiB,GAAG,KAAK;IAEzB;IACA,KAAA6C,sBAAsB,GAAwB,IAAI;IAClD,KAAAT,oBAAoB,GAA8B,EAAE;IACpD,KAAAa,uBAAuB,GAAG,KAAK;EA1B/B;EA4BSgB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAD,gBAAgBA,CAAA;IACd,MAAME,OAAO,GAAG;MACdtH,aAAa,EAAE,IAAI,CAACiH,aAAa,IAAI,IAAI;MACzC/G,OAAO,EAAE,IAAI,CAACgH,YAAY;MAC1BK,SAAS,EAAE,IAAI,CAACT,SAAS;MACzBU,QAAQ,EAAE,IAAI,CAACX;KAChB;IAED,IAAI,CAACL,gBAAgB,CAACiB,mCAAmC,CAAC;MAAEC,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CAC/EjK,GAAG,CAACkK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACb,YAAY,GAAGY,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACjDC,WAAW,EAAED,IAAI,CAACC,WAAY;UAC9BjI,aAAa,EAAEgI,IAAI,CAAChI,aAAc;UAClCF,aAAa,EAAEkI,IAAI,CAAClI,aAAa;UAAE;UACnCM,SAAS,EAAE4H,IAAI,CAAC5H,SAAU;UAC1B6F,SAAS,EAAE+B,IAAI,CAAC/B,SAAU;UAC1B5F,QAAQ,EAAE2H,IAAI,CAAC3H,QAAQ;UACvB6F,QAAQ,EAAE8B,IAAI,CAAC9B,QAAQ;UACvBhG,OAAO,EAAE8H,IAAI,CAAC9H;SACf,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAC6G,YAAY,GAAGa,QAAQ,CAACM,UAAU,IAAI,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAACxB,OAAO,CAACyB,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAhB,mBAAmBA,CAAA;IACjB,MAAMC,OAAO,GAAG;MACdxG,KAAK,EAAE,IAAI,CAACqB,kBAAkB,IAAI,IAAI;MACtCpB,SAAS,EAAE,IAAI,CAACwB,mBAAmB,IAAI,IAAI;MAC3CrC,OAAO,EAAE,CAAC;MAAE;MACZqH,SAAS,EAAE,IAAI,CAAClG,cAAc;MAC9BmG,QAAQ,EAAE,IAAI,CAAChG;KAChB;IAED,IAAI,CAACiF,aAAa,CAAC6B,6BAA6B,CAAC;MAAEZ,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACtEjK,GAAG,CAACkK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACvE,eAAe,GAAGsE,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDpH,QAAQ,EAAEoH,IAAI,CAACpH,QAAS;UACxBE,KAAK,EAAEkH,IAAI,CAAClH,KAAM;UAClBC,SAAS,EAAEiH,IAAI,CAACjH,SAAS;UACzBF,QAAQ,EAAE,IAAI,CAACkB,yBAAyB,CAACwG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5H,QAAQ,KAAKoH,IAAI,CAACpH,QAAQ;SAChF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAACa,iBAAiB,GAAGmG,QAAQ,CAACM,UAAU,IAAI,CAAC;QACjD,IAAI,CAACO,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,CACH,CAACJ,SAAS,EAAE;EACf;EAEA;EACAK,QAAQA,CAAA;IACN,IAAI,CAAC5B,SAAS,GAAG,CAAC;IAClB,IAAI,CAACM,gBAAgB,EAAE;EACzB;EAEAuB,OAAOA,CAAA;IACL,IAAI,CAAC1B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACJ,SAAS,GAAG,CAAC;IAClB,IAAI,CAACM,gBAAgB,EAAE;EACzB;EAEA;EACA/E,aAAaA,CAAA;IACX,IAAI,CAAChB,cAAc,GAAG,CAAC;IACvB,IAAI,CAACgG,mBAAmB,EAAE;EAC5B;EAEA3E,YAAYA,CAAA;IACV,IAAI,CAACP,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACI,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAClB,cAAc,GAAG,CAAC;IACvB,IAAI,CAACgG,mBAAmB,EAAE;EAC5B;EAEA;EACAuB,WAAWA,CAACC,IAAY;IACtB,IAAI,CAAC/B,SAAS,GAAG+B,IAAI;IACrB,IAAI,CAACzB,gBAAgB,EAAE;EACzB;EAEA9F,gBAAgBA,CAACuH,IAAY;IAC3B,IAAI,CAACxH,cAAc,GAAGwH,IAAI;IAC1B,IAAI,CAACxB,mBAAmB,EAAE;EAC5B;EAEA;EACA7I,eAAeA,CAACsK,KAAuB;IACrC,IAAI,CAACjF,cAAc,GAAG;MACpB3D,OAAO,EAAE,CAAC;MACVJ,aAAa,EAAEnC,gBAAgB,CAACsG,aAAa,CAAC;KAC/C;IACD,IAAI,CAAClC,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAACsF,mBAAmB,EAAE;IAC1B,IAAI,CAACd,aAAa,CAACwC,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAhK,aAAaA,CAAC6J,KAAuB,EAAEI,QAAsB;IAC3D,IAAI,CAACrF,cAAc,GAAG;MACpBoE,WAAW,EAAEiB,QAAQ,CAACjB,WAAW;MACjCjI,aAAa,EAAEkJ,QAAQ,CAAClJ,aAAa;MACrCF,aAAa,EAAEoJ,QAAQ,CAACpJ,aAAa,IAAInC,gBAAgB,CAACsG,aAAa;MACvE/D,OAAO,EAAEgJ,QAAQ,CAAChJ,OAAO,IAAI;KAC9B;IACD,IAAI,CAACqG,aAAa,CAACwC,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAtF,OAAOA,CAACwF,GAAQ;IACdA,GAAG,CAACC,KAAK,EAAE;EACb;EAEArF,QAAQA,CAACoF,GAAQ;IACf,IAAI,CAAC,IAAI,CAACE,oBAAoB,EAAE,EAAE;MAChC;IACF;IAEA,IAAI,IAAI,CAACxF,cAAc,CAACoE,WAAW,EAAE;MACnC,IAAI,CAACqB,cAAc,CAACH,GAAG,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACI,cAAc,CAACJ,GAAG,CAAC;IAC1B;EACF;EAEA;EACAE,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACxF,cAAc,CAAC7D,aAAa,EAAEwJ,IAAI,EAAE,EAAE;MAC9C,IAAI,CAAC9C,OAAO,CAACyB,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACtE,cAAc,CAAC/D,aAAa,KAAK2J,SAAS,IAAI,IAAI,CAAC5F,cAAc,CAAC/D,aAAa,KAAK,IAAI,EAAE;MACjG,IAAI,CAAC4G,OAAO,CAACyB,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACtE,cAAc,CAAC3D,OAAO,KAAKuJ,SAAS,IAAI,IAAI,CAAC5F,cAAc,CAAC3D,OAAO,KAAK,IAAI,EAAE;MACrF,IAAI,CAACwG,OAAO,CAACyB,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAACtE,cAAc,CAACoE,WAAW,IAAI,IAAI,CAACpE,cAAc,CAAC/D,aAAa,KAAKnC,gBAAgB,CAACsG,aAAa,IAAI,IAAI,CAAClC,yBAAyB,CAACC,MAAM,KAAK,CAAC,EAAE;MAC3J,IAAI,CAAC0E,OAAO,CAACyB,YAAY,CAAC,eAAe,CAAC;MAC1C,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;EAEA;EACAoB,cAAcA,CAACJ,GAAQ;IACrB,MAAMO,YAAY,GAAqB;MACrC1J,aAAa,EAAE,IAAI,CAAC6D,cAAc,CAAC7D,aAAa;MAChDF,aAAa,EAAE,IAAI,CAAC+D,cAAc,CAAC/D,aAAa;MAChDI,OAAO,EAAE,IAAI,CAAC2D,cAAc,CAAC3D;KAC9B;IAED,IAAI,CAACsG,gBAAgB,CAACmD,gCAAgC,CAAC;MAAEjC,IAAI,EAAEgC;IAAY,CAAE,CAAC,CAAC/B,IAAI,CACjFjK,GAAG,CAACkK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;QACjD,MAAM8B,UAAU,GAAGC,QAAQ,CAACjC,QAAQ,CAACE,OAAO,EAAE,EAAE,CAAC;QACjD,IAAI,CAACgC,mBAAmB,CAACF,UAAU,EAAET,GAAG,CAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAACzC,OAAO,CAACyB,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAiB,cAAcA,CAACH,GAAQ;IACrB,MAAMO,YAAY,GAAqB;MACrCzB,WAAW,EAAE,IAAI,CAACpE,cAAc,CAACoE,WAAW;MAC5CjI,aAAa,EAAE,IAAI,CAAC6D,cAAc,CAAC7D,aAAa;MAChDF,aAAa,EAAE,IAAI,CAAC+D,cAAc,CAAC/D,aAAa;MAChDI,OAAO,EAAE,IAAI,CAAC2D,cAAc,CAAC3D;KAC9B;IAED,IAAI,CAACsG,gBAAgB,CAACmD,gCAAgC,CAAC;MAAEjC,IAAI,EAAEgC;IAAY,CAAE,CAAC,CAAC/B,IAAI,CACjFjK,GAAG,CAACkK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACnB,OAAO,CAACqD,aAAa,CAAC,QAAQ,CAAC;QACpCZ,GAAG,CAACC,KAAK,EAAE;QACX,IAAI,CAAChC,gBAAgB,EAAE;MACzB,CAAC,MAAM;QACL,IAAI,CAACV,OAAO,CAACyB,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAyB,mBAAmBA,CAACF,UAAkB,EAAET,GAAQ;IAC9C;IACA,IAAI,CAACzC,OAAO,CAACqD,aAAa,CAAC,QAAQ,CAAC;IACpCZ,GAAG,CAACC,KAAK,EAAE;IACX,IAAI,CAAChC,gBAAgB,EAAE;EACzB;EAEA;EACAhI,cAAcA,CAAC8J,QAAsB;IACnC,IAAIc,OAAO,CAAC,WAAWd,QAAQ,CAAClJ,aAAa,KAAK,CAAC,EAAE;MACnD,IAAI,CAACwG,gBAAgB,CAACyD,kCAAkC,CAAC;QACvDvC,IAAI,EAAE;UAAEO,WAAW,EAAEiB,QAAQ,CAACjB;QAAW;OAC1C,CAAC,CAACN,IAAI,CACLjK,GAAG,CAACkK,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAACnB,OAAO,CAACqD,aAAa,CAAC,QAAQ,CAAC;UACpC,IAAI,CAAC3C,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAACV,OAAO,CAACyB,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;QACzD;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf;EACF;EAEA;EACA7I,kBAAkBA,CAAC0J,QAAsB,EAAEJ,KAAuB;IAChE,IAAI,CAAC/C,sBAAsB,GAAGmD,QAAQ;IACtC,IAAI,CAAC/C,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACb,oBAAoB,GAAG,EAAE;IAE9B,IAAI,CAACiB,aAAa,CAACwC,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;IAEF,MAAM3B,OAAO,GAA8B;MACzCsC,UAAU,EAAEV,QAAQ,CAACjB;KACtB;IAED,IAAI,CAACzB,gBAAgB,CAAC0D,yCAAyC,CAAC;MAAExC,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACrFjK,GAAG,CAACkK,QAAQ,IAAG;MACb,IAAI,CAACzB,uBAAuB,GAAG,KAAK;MACpC,IAAIyB,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACvC,oBAAoB,GAAGsC,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACzDmC,UAAU,EAAEnC,IAAI,CAACmC,UAAW;UAC5BrJ,KAAK,EAAEkH,IAAI,CAAClH,KAAM;UAClBC,SAAS,EAAEiH,IAAI,CAACjH;SACjB,CAAC,CAAC,IAAI,EAAE;MACX,CAAC,MAAM;QACL,IAAI,CAAC2F,OAAO,CAACyB,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACA1H,oBAAoBA,CAACyJ,KAAwB;IAC3CA,KAAK,CAACvJ,QAAQ,GAAG,CAACuJ,KAAK,CAACvJ,QAAQ;IAEhC,IAAIuJ,KAAK,CAACvJ,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAACkB,yBAAyB,CAACwG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5H,QAAQ,KAAKwJ,KAAK,CAACxJ,QAAQ,CAAC,EAAE;QAC5E,IAAI,CAACmB,yBAAyB,CAACsI,IAAI,CAAC;UAAE,GAAGD;QAAK,CAAE,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAACrI,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACuI,MAAM,CAAC9B,CAAC,IAAIA,CAAC,CAAC5H,QAAQ,KAAKwJ,KAAK,CAACxJ,QAAQ,CAAC;IAC5G;IAEA,IAAI,CAAC6H,4BAA4B,EAAE;EACrC;EAEA5F,eAAeA,CAAA;IACb,IAAI,CAACK,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAEhD,IAAI,CAACI,eAAe,CAACiH,OAAO,CAACH,KAAK,IAAG;MACnCA,KAAK,CAACvJ,QAAQ,GAAG,IAAI,CAACqC,iBAAiB;MACvC,IAAI,IAAI,CAACA,iBAAiB,EAAE;QAC1B,IAAI,CAAC,IAAI,CAACnB,yBAAyB,CAACwG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5H,QAAQ,KAAKwJ,KAAK,CAACxJ,QAAQ,CAAC,EAAE;UAC5E,IAAI,CAACmB,yBAAyB,CAACsI,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACnD;MACF,CAAC,MAAM;QACL,IAAI,CAACrI,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACuI,MAAM,CAAC9B,CAAC,IAAIA,CAAC,CAAC5H,QAAQ,KAAKwJ,KAAK,CAACxJ,QAAQ,CAAC;MAC5G;IACF,CAAC,CAAC;EACJ;EAEAiB,mBAAmBA,CAACuI,KAAwB;IAC1C,IAAI,CAACrI,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACuI,MAAM,CAAC9B,CAAC,IAAIA,CAAC,CAAC5H,QAAQ,KAAKwJ,KAAK,CAACxJ,QAAQ,CAAC;IAE1G,MAAM4J,cAAc,GAAG,IAAI,CAAClH,eAAe,CAACmH,IAAI,CAACjC,CAAC,IAAIA,CAAC,CAAC5H,QAAQ,KAAKwJ,KAAK,CAACxJ,QAAQ,CAAC;IACpF,IAAI4J,cAAc,EAAE;MAClBA,cAAc,CAAC3J,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAAC4H,4BAA4B,EAAE;EACrC;EAEAA,4BAA4BA,CAAA;IAC1B,IAAI,CAACvF,iBAAiB,GAAG,IAAI,CAACI,eAAe,CAACtB,MAAM,GAAG,CAAC,IACtD,IAAI,CAACsB,eAAe,CAACoH,KAAK,CAACN,KAAK,IAAIA,KAAK,CAACvJ,QAAQ,CAAC;EACvD;;;uCAnWWuF,iBAAiB,EAAAvI,EAAA,CAAA8M,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhN,EAAA,CAAA8M,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAlN,EAAA,CAAA8M,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAApN,EAAA,CAAA8M,iBAAA,CAAAK,EAAA,CAAAE,YAAA,GAAArN,EAAA,CAAA8M,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAvN,EAAA,CAAA8M,iBAAA,CAAAU,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAjBlF,iBAAiB;MAAAmF,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;UCvD5B7N,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,SAAA,qBAAiC;UACnCZ,EAAA,CAAAc,YAAA,EAAiB;UAIbd,EAHJ,CAAAC,cAAA,mBAAc,aAE6E,aAChD;UACrCD,EAAA,CAAAY,SAAA,WAA+E;UAE7EZ,EADF,CAAAC,cAAA,UAAK,WACmD;UACpDD,EAAA,CAAAa,MAAA,iUACF;UAGNb,EAHM,CAAAc,YAAA,EAAI,EACA,EACF,EACF;UAMAd,EAHN,CAAAC,cAAA,cAA8B,cACN,eACqC,iBACT;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAExDd,EADF,CAAAC,cAAA,yBAA6B,iBAE8B;UAAvDD,EAAA,CAAAmD,gBAAA,2BAAA4K,2DAAA1K,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAA4N,GAAA;YAAAhO,EAAA,CAAAuD,kBAAA,CAAAuK,GAAA,CAAA1E,aAAA,EAAA/F,MAAA,MAAAyK,GAAA,CAAA1E,aAAA,GAAA/F,MAAA;YAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;UAAA,EAA2B;UAACrD,EAAA,CAAAE,UAAA,yBAAA+N,yDAAA;YAAAjO,EAAA,CAAAI,aAAA,CAAA4N,GAAA;YAAA,OAAAhO,EAAA,CAAAU,WAAA,CAAeoN,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAG9D7K,EAJM,CAAAc,YAAA,EACyD,EAC3C,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACf;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAEhDd,EADF,CAAAC,cAAA,yBAA6B,qBAC2E;UAAzDD,EAAA,CAAAmD,gBAAA,2BAAA+K,+DAAA7K,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAA4N,GAAA;YAAAhO,EAAA,CAAAuD,kBAAA,CAAAuK,GAAA,CAAAzE,YAAA,EAAAhG,MAAA,MAAAyK,GAAA,CAAAzE,YAAA,GAAAhG,MAAA;YAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;UAAA,EAA0B;UAACrD,EAAA,CAAAE,UAAA,4BAAAiO,gEAAA;YAAAnO,EAAA,CAAAI,aAAA,CAAA4N,GAAA;YAAA,OAAAhO,EAAA,CAAAU,WAAA,CAAkBoN,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UACnG7K,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACxCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACrCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAIjCb,EAJiC,CAAAc,YAAA,EAAY,EAC3B,EACE,EACZ,EACF;UAENd,EAAA,CAAAY,SAAA,cAEM;UAKFZ,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACmB;UAApBD,EAAA,CAAAE,UAAA,mBAAAkO,oDAAA;YAAApO,EAAA,CAAAI,aAAA,CAAA4N,GAAA;YAAA,OAAAhO,EAAA,CAAAU,WAAA,CAASoN,GAAA,CAAAhD,OAAA,EAAS;UAAA,EAAC;UACvE9K,EAAA,CAAAY,SAAA,aAAgC;UAAAZ,EAAA,CAAAa,MAAA,qBAClC;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAC,cAAA,kBAA8D;UAArBD,EAAA,CAAAE,UAAA,mBAAAmO,oDAAA;YAAArO,EAAA,CAAAI,aAAA,CAAA4N,GAAA;YAAA,OAAAhO,EAAA,CAAAU,WAAA,CAASoN,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAC3D7K,EAAA,CAAAY,SAAA,aAAkC;UAAAZ,EAAA,CAAAa,MAAA,qBACpC;UAEJb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAGJd,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAA4B,UAAA,KAAA0M,oCAAA,qBAAiG;UAKvGtO,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;UAOEd,EAJR,CAAAC,cAAA,eAAmC,iBACc,aACtC,UACD,cAC4B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,0BAAG;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACtCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAEpCb,EAFoC,CAAAc,YAAA,EAAK,EAClC,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UA2BLD,EA1BA,CAAA4B,UAAA,KAAA2M,gCAAA,mBAA0C,KAAAC,gCAAA,iBA0BJ;UAQ9CxO,EAHM,CAAAc,YAAA,EAAQ,EACF,EACJ,EACO;UAEbd,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADrBD,EAAA,CAAAmD,gBAAA,wBAAAsL,iEAAApL,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAA4N,GAAA;YAAAhO,EAAA,CAAAuD,kBAAA,CAAAuK,GAAA,CAAA7E,SAAA,EAAA5F,MAAA,MAAAyK,GAAA,CAAA7E,SAAA,GAAA5F,MAAA;YAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;UAAA,EAAoB;UAClCrD,EAAA,CAAAE,UAAA,wBAAAuO,iEAAApL,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAA4N,GAAA;YAAA,OAAAhO,EAAA,CAAAU,WAAA,CAAcoN,GAAA,CAAA/C,WAAA,CAAA1H,MAAA,CAAmB;UAAA,EAAC;UAGxCrD,EAFI,CAAAc,YAAA,EAAiB,EACF,EACT;UA6WVd,EA1WA,CAAA4B,UAAA,KAAA8M,yCAAA,iCAAA1O,EAAA,CAAA2O,sBAAA,CAA8C,KAAAC,yCAAA,iCAAA5O,EAAA,CAAA2O,sBAAA,CAgOF,KAAAE,yCAAA,kCAAA7O,EAAA,CAAA2O,sBAAA,CA0IU;;;UA9cxC3O,EAAA,CAAA+B,SAAA,IAA2B;UAA3B/B,EAAA,CAAA0D,gBAAA,YAAAoK,GAAA,CAAA1E,aAAA,CAA2B;UASgBpJ,EAAA,CAAA+B,SAAA,GAA0B;UAA1B/B,EAAA,CAAA0D,gBAAA,YAAAoK,GAAA,CAAAzE,YAAA,CAA0B;UAC1DrJ,EAAA,CAAA+B,SAAA,EAAc;UAAd/B,EAAA,CAAAoC,UAAA,eAAc;UACdpC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAoC,UAAA,YAAW;UACXpC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAoC,UAAA,YAAW;UAwBgBpC,EAAA,CAAA+B,SAAA,IAAc;UAAd/B,EAAA,CAAAoC,UAAA,SAAA0L,GAAA,CAAAgB,QAAA,CAAc;UAqB/B9O,EAAA,CAAA+B,SAAA,IAAe;UAAf/B,EAAA,CAAAoC,UAAA,YAAA0L,GAAA,CAAA3E,YAAA,CAAe;UA0BnCnJ,EAAA,CAAA+B,SAAA,EAA+B;UAA/B/B,EAAA,CAAAoC,UAAA,SAAA0L,GAAA,CAAA3E,YAAA,CAAAhF,MAAA,OAA+B;UAU1BnE,EAAA,CAAA+B,SAAA,GAAoB;UAApB/B,EAAA,CAAA0D,gBAAA,SAAAoK,GAAA,CAAA7E,SAAA,CAAoB;UAAuBjJ,EAAtB,CAAAoC,UAAA,aAAA0L,GAAA,CAAA9E,QAAA,CAAqB,mBAAA8E,GAAA,CAAA5E,YAAA,CAAgC;;;qBDlE1FvJ,YAAY,EAAAoP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZvP,YAAY,EAAAwP,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAtC,EAAA,CAAAuC,eAAA,EAAAvC,EAAA,CAAAwC,mBAAA,EAAAxC,EAAA,CAAAyC,qBAAA,EAAAzC,EAAA,CAAA0C,qBAAA,EAAA1C,EAAA,CAAA2C,gBAAA,EAAA3C,EAAA,CAAA4C,iBAAA,EAAA5C,EAAA,CAAA6C,iBAAA,EAAA7C,EAAA,CAAA8C,iBAAA,EAAA9C,EAAA,CAAA+C,cAAA,EAAA/C,EAAA,CAAAgD,oBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}