{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./x64-core\"), require(\"./sha512\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./x64-core\", \"./sha512\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_x64 = C.x64;\n    var X64Word = C_x64.Word;\n    var X64WordArray = C_x64.WordArray;\n    var C_algo = C.algo;\n    var SHA512 = C_algo.SHA512;\n\n    /**\n     * SHA-384 hash algorithm.\n     */\n    var SHA384 = C_algo.SHA384 = SHA512.extend({\n      _doReset: function () {\n        this._hash = new X64WordArray.init([new X64Word.init(0xcbbb9d5d, 0xc1059ed8), new X64Word.init(0x629a292a, 0x367cd507), new X64Word.init(0x9159015a, 0x3070dd17), new X64Word.init(0x152fecd8, 0xf70e5939), new X64Word.init(0x67332667, 0xffc00b31), new X64Word.init(0x8eb44a87, 0x68581511), new X64Word.init(0xdb0c2e0d, 0x64f98fa7), new X64Word.init(0x47b5481d, 0xbefa4fa4)]);\n      },\n      _doFinalize: function () {\n        var hash = SHA512._doFinalize.call(this);\n        hash.sigBytes -= 16;\n        return hash;\n      }\n    });\n\n    /**\n     * Shortcut function to the hasher's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     *\n     * @return {WordArray} The hash.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hash = CryptoJS.SHA384('message');\n     *     var hash = CryptoJS.SHA384(wordArray);\n     */\n    C.SHA384 = SHA512._createHelper(SHA384);\n\n    /**\n     * Shortcut function to the HMAC's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     * @param {WordArray|string} key The secret key.\n     *\n     * @return {WordArray} The HMAC.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hmac = CryptoJS.HmacSHA384(message, key);\n     */\n    C.HmacSHA384 = SHA512._createHmacHelper(SHA384);\n  })();\n  return CryptoJS.SHA384;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}