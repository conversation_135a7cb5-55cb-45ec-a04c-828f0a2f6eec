<!-- 空間選擇器共用組件 -->
<div class="space-picker-container">
  <!-- 搜尋區域 -->
  <div class="search-section mb-3">
    <div class="row">
      <div class="col-md-5">
        <nb-form-field>
          <input type="text" nbInput class="form-control-sm" placeholder="搜尋項目名稱..." [(ngModel)]="searchKeyword"
            (keyup.enter)="onSearch()" style="height: 32px; border-radius: 4px;" />
        </nb-form-field>
      </div>
      <div class="col-md-5">
        <nb-form-field>
          <input type="text" nbInput class="form-control-sm" placeholder="搜尋所屬區域..." [(ngModel)]="searchLocation"
            (keyup.enter)="onSearch()" style="height: 32px; border-radius: 4px;" />
        </nb-form-field>
      </div>
      <div class="col-md-2">
        <button class="btn btn-sm btn-outline-secondary me-1" (click)="onReset()" nbButton ghost>
          <nb-icon icon="refresh-outline"></nb-icon>
        </button>
        <button class="btn btn-sm btn-secondary" (click)="onSearch()" nbButton>
          <nb-icon icon="search-outline"></nb-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- 空間列表區域 -->
  <div class="space-list-section border rounded p-3" style="background-color: #f8f9fa;">
    <!-- 列表標題和統計 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
      <div class="d-flex align-items-center" *ngIf="multiple">
        <input type="checkbox" id="selectAllSpaces" [checked]="allSelected" (change)="toggleAllSpaces()" class="me-2">
        <label for="selectAllSpaces" class="mb-0 font-weight-bold">全選當頁空間</label>
      </div>
      <div *ngIf="!multiple" class="font-weight-bold">
        選擇空間
      </div>
      <small class="text-muted">
        共 {{ totalRecords }} 筆，第 {{ pageIndex }} / {{ totalPages }} 頁
      </small>
    </div>

    <!-- 空間項目網格 -->
    <div class="space-grid">
      <div class="space-item" *ngFor="let space of availableSpaces" [class.selected]="space.selected"
        (click)="toggleSpaceSelection(space)">
        <div class="space-card">
          <div class="space-name">{{ space.CPart }}</div>
          <div class="space-location">{{ space.CLocation || '-' }}</div>
        </div>
      </div>
    </div>

    <!-- 空間列表為空時的提示 -->
    <div *ngIf="availableSpaces.length === 0" class="text-center text-muted py-4">
      <nb-icon icon="info-outline" class="me-2"></nb-icon>
      沒有符合條件的空間
    </div>

    <!-- 分頁控制 -->
    <div class="d-flex justify-content-center mt-3" *ngIf="totalRecords > pageSize">
      <nav aria-label="空間分頁">
        <ul class="pagination pagination-sm">
          <li class="page-item" [class.disabled]="pageIndex === 1">
            <button class="page-link" (click)="onPageChange(1)" [disabled]="pageIndex === 1">
              第一頁
            </button>
          </li>
          <li class="page-item" [class.disabled]="pageIndex === 1">
            <button class="page-link" (click)="onPageChange(pageIndex - 1)" [disabled]="pageIndex === 1">
              上一頁
            </button>
          </li>
          <li class="page-item active">
            <span class="page-link">{{ pageIndex }} / {{ totalPages }}</span>
          </li>
          <li class="page-item" [class.disabled]="pageIndex === totalPages">
            <button class="page-link" (click)="onPageChange(pageIndex + 1)" [disabled]="pageIndex === totalPages">
              下一頁
            </button>
          </li>
          <li class="page-item" [class.disabled]="pageIndex === totalPages">
            <button class="page-link" (click)="onPageChange(totalPages)" [disabled]="pageIndex === totalPages">
              最後一頁
            </button>
          </li>
        </ul>
      </nav>
    </div>
  </div>

  <!-- 已選空間摘要 -->
  <div *ngIf="selectedItems.length > 0" class="selected-summary mt-3 p-3 bg-light border rounded">
    <h6 class="mb-2">
      <nb-icon icon="checkmark-circle-outline" class="text-success me-2"></nb-icon>
      已選空間 ({{ selectedItems.length }} 項)
    </h6>
    <div class="selected-spaces-list">
      <span *ngFor="let space of selectedItems" class="badge badge-primary me-1 mb-1">
        {{ space.CPart }}
        <button type="button" class="btn-close ms-1" (click)="removeSelectedSpace(space)" style="font-size: 0.7rem;"
          aria-label="移除">
        </button>
      </span>
    </div>
  </div>
</div>
