{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbInputModule, NbSelectModule, NbOptionModule } from '@nebular/theme';\nimport { GetRequirement } from 'src/services/api/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/requirement.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction RequirementTemplateSelectorComponent_nb_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r1.label, \" \");\n  }\n}\nfunction RequirementTemplateSelectorComponent_nb_option_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r2.label, \" \");\n  }\n}\nfunction RequirementTemplateSelectorComponent_span_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\\u5DF2\\u9078\\u64C7 \", ctx_r2.getSelectedItems().length, \" \\u9805) \");\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"nb-checkbox\", 40);\n    i0.ɵɵlistener(\"ngModelChange\", function RequirementTemplateSelectorComponent_div_64_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSelectAll($event));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D\\u9801\\u9762 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.isAllSelected())(\"indeterminate\", ctx_r2.isIndeterminate());\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"nb-icon\", 42);\n    i0.ɵɵtext(2, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_66_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"nb-checkbox\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_div_66_div_1_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const requirement_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      i0.ɵɵtwoWayBindingSet(requirement_r6.selected, $event) || (requirement_r6.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function RequirementTemplateSelectorComponent_div_66_div_1_Template_nb_checkbox_ngModelChange_2_listener() {\n      const requirement_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleItemSelection(requirement_r6));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 48)(4, \"div\", 49)(5, \"div\", 50);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 51);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 52)(10, \"div\", 53)(11, \"span\", 54);\n    i0.ɵɵtext(12, \"\\u985E\\u578B:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 55);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 53)(16, \"span\", 54);\n    i0.ɵɵtext(17, \"\\u55AE\\u50F9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 55);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 53)(22, \"span\", 54);\n    i0.ɵɵtext(23, \"\\u72C0\\u614B:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 55);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 53)(27, \"span\", 54);\n    i0.ɵɵtext(28, \"\\u9810\\u7D04\\u9700\\u6C42:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 55);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 53)(32, \"span\", 54);\n    i0.ɵɵtext(33, \"\\u7C21\\u6613\\u5BA2\\u8B8A:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\", 55);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const requirement_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", requirement_r6.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", requirement_r6.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(requirement_r6.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(requirement_r6.CLocation || \"-\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseTypeText(requirement_r6.CHouseType));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind2(20, 11, requirement_r6.CUnitPrice, \"1.0-2\"), \" \", requirement_r6.CUnit || \"\", \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.getStatusText(requirement_r6.CStatus));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getIsShowText(requirement_r6.CIsShow));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getIsSimpleText(requirement_r6.CIsSimple));\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, RequirementTemplateSelectorComponent_div_66_div_1_Template, 36, 14, \"div\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.requirements);\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"nb-icon\", 57);\n    i0.ɵɵelementStart(2, \"div\", 58);\n    i0.ɵɵtext(3, \"\\u6C92\\u6709\\u627E\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u9700\\u6C42\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 61)(4, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_div_68_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onPageChange(ctx_r2.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"nb-icon\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 64);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_div_68_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onPageChange(ctx_r2.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"nb-icon\", 65);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u5171 \", ctx_r2.totalRecords, \" \\u7B46\\u8CC7\\u6599\\uFF0C\\u7B2C \", ctx_r2.currentPage, \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.currentPage <= 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.currentPage, \" / \", ctx_r2.getTotalPages(), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.currentPage >= ctx_r2.getTotalPages());\n  }\n}\nfunction RequirementTemplateSelectorComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67);\n    i0.ɵɵtext(2, \"\\u9078\\u64C7\\u6458\\u8981\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 68)(4, \"div\", 69)(5, \"span\", 70);\n    i0.ɵɵtext(6, \"\\u5DF2\\u9078\\u64C7\\u9805\\u76EE:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 71);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 69)(10, \"span\", 70);\n    i0.ɵɵtext(11, \"\\u7E3D\\u91D1\\u984D:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 72);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getSelectedItems().length, \" \\u9805\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 2, ctx_r2.getTotalPrice(), \"1.0-2\"));\n  }\n}\nexport let RequirementTemplateSelectorComponent = /*#__PURE__*/(() => {\n  class RequirementTemplateSelectorComponent {\n    constructor(requirementService, dialogRef) {\n      this.requirementService = requirementService;\n      this.dialogRef = dialogRef;\n      this.buildCaseId = 0;\n      this.multiple = true;\n      this.preSelectedItems = [];\n      this.selectionConfirmed = new EventEmitter();\n      this.selectionCancelled = new EventEmitter();\n      // 資料相關屬性\n      this.requirements = [];\n      this.isLoading = false;\n      // 搜尋相關屬性\n      this.searchFilters = {\n        CBuildCaseID: 0,\n        CLocation: null,\n        CRequirement: null,\n        CHouseType: null,\n        CStatus: 1,\n        // 預設只顯示啟用的\n        CIsShow: null,\n        CIsSimple: null,\n        PageIndex: 1,\n        PageSize: 20\n      };\n      // 分頁相關屬性\n      this.totalRecords = 0;\n      this.currentPage = 1;\n      this.pageSize = 20;\n      // 房屋類型選項\n      this.houseTypeOptions = [{\n        value: 1,\n        label: '套房'\n      }, {\n        value: 2,\n        label: '1房'\n      }, {\n        value: 3,\n        label: '2房'\n      }, {\n        value: 4,\n        label: '3房'\n      }, {\n        value: 5,\n        label: '4房'\n      }, {\n        value: 6,\n        label: '5房以上'\n      }];\n      // 狀態選項\n      this.statusOptions = [{\n        value: null,\n        label: '全部'\n      }, {\n        value: 1,\n        label: '啟用'\n      }, {\n        value: 0,\n        label: '停用'\n      }];\n    }\n    ngOnInit() {\n      this.searchFilters.CBuildCaseID = this.buildCaseId;\n      this.loadRequirements();\n      this.initializePreSelectedItems();\n    }\n    // 初始化預選項目\n    initializePreSelectedItems() {\n      if (this.preSelectedItems && this.preSelectedItems.length > 0) {\n        // 將預選項目標記為已選擇\n        this.preSelectedItems.forEach(preSelected => {\n          const existingItem = this.requirements.find(req => req.CRequirementID === preSelected.CRequirementID);\n          if (existingItem) {\n            existingItem.selected = true;\n          }\n        });\n      }\n    }\n    // 載入需求資料\n    loadRequirements() {\n      this.isLoading = true;\n      const requestParams = {\n        ...this.searchFilters,\n        PageIndex: this.currentPage,\n        PageSize: this.pageSize\n      };\n      this.requirementService.apiRequirementGetListPost$Json({\n        body: requestParams\n      }).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.StatusCode === 0 && response.Entries) {\n            this.requirements = response.Entries.map(item => ({\n              ...item,\n              selected: false\n            }));\n            this.totalRecords = response.TotalItems || 0;\n            this.initializePreSelectedItems();\n          } else {\n            this.requirements = [];\n            this.totalRecords = 0;\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.requirements = [];\n          this.totalRecords = 0;\n          console.error('載入需求資料失敗:', error);\n        }\n      });\n    }\n    // 搜尋功能\n    onSearch() {\n      this.currentPage = 1;\n      this.loadRequirements();\n    }\n    // 重置搜尋\n    onReset() {\n      this.searchFilters = {\n        CBuildCaseID: this.buildCaseId,\n        CLocation: null,\n        CRequirement: null,\n        CHouseType: null,\n        CStatus: 1,\n        CIsShow: null,\n        CIsSimple: null,\n        PageIndex: 1,\n        PageSize: 20\n      };\n      this.currentPage = 1;\n      this.loadRequirements();\n    }\n    // 分頁變更\n    onPageChange(page) {\n      this.currentPage = page;\n      this.loadRequirements();\n    }\n    // 切換項目選擇狀態\n    toggleItemSelection(item) {\n      if (!this.multiple) {\n        // 單選模式：取消其他項目的選擇\n        this.requirements.forEach(req => {\n          if (req.CRequirementID !== item.CRequirementID) {\n            req.selected = false;\n          }\n        });\n      }\n      item.selected = !item.selected;\n    }\n    // 全選/取消全選\n    toggleSelectAll(selectAll) {\n      this.requirements.forEach(item => {\n        item.selected = selectAll;\n      });\n    }\n    // 獲取已選擇的項目\n    getSelectedItems() {\n      return this.requirements.filter(item => item.selected);\n    }\n    // 計算總價\n    getTotalPrice() {\n      return this.getSelectedItems().reduce((total, item) => {\n        return total + (item.CUnitPrice || 0);\n      }, 0);\n    }\n    // 確認選擇\n    confirmSelection() {\n      const selectedItems = this.getSelectedItems();\n      if (selectedItems.length === 0) {\n        alert('請至少選擇一個項目');\n        return;\n      }\n      const config = {\n        selectedItems: selectedItems,\n        totalPrice: this.getTotalPrice(),\n        buildCaseId: this.buildCaseId\n      };\n      this.selectionConfirmed.emit(config);\n      this.close();\n    }\n    // 關閉對話框\n    close() {\n      this.selectionCancelled.emit();\n      this.dialogRef.close();\n    }\n    // 獲取房屋類型顯示文字\n    getHouseTypeText(houseTypes) {\n      if (!houseTypes || houseTypes.length === 0) {\n        return '-';\n      }\n      const typeNames = houseTypes.map(type => {\n        const option = this.houseTypeOptions.find(opt => opt.value === type);\n        return option ? option.label : type.toString();\n      });\n      return typeNames.join(', ');\n    }\n    // 獲取狀態顯示文字\n    getStatusText(status) {\n      return status === 1 ? '啟用' : '停用';\n    }\n    // 獲取是否顯示文字\n    getIsShowText(isShow) {\n      return isShow ? '是' : '否';\n    }\n    // 獲取簡易客變文字\n    getIsSimpleText(isSimple) {\n      return isSimple ? '是' : '否';\n    }\n    // 獲取總頁數\n    getTotalPages() {\n      return Math.ceil(this.totalRecords / this.pageSize);\n    }\n    // 檢查是否全選\n    isAllSelected() {\n      return this.requirements.length > 0 && this.requirements.every(item => item.selected);\n    }\n    // 檢查是否部分選擇\n    isIndeterminate() {\n      return this.requirements.some(item => item.selected) && !this.requirements.every(item => item.selected);\n    }\n    static {\n      this.ɵfac = function RequirementTemplateSelectorComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RequirementTemplateSelectorComponent)(i0.ɵɵdirectiveInject(i1.RequirementService), i0.ɵɵdirectiveInject(i2.NbDialogRef));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RequirementTemplateSelectorComponent,\n        selectors: [[\"app-requirement-template-selector\"]],\n        inputs: {\n          buildCaseId: \"buildCaseId\",\n          multiple: \"multiple\",\n          preSelectedItems: \"preSelectedItems\"\n        },\n        outputs: {\n          selectionConfirmed: \"selectionConfirmed\",\n          selectionCancelled: \"selectionCancelled\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 76,\n        vars: 23,\n        consts: [[1, \"requirement-template-dialog\"], [1, \"requirement-template-header\"], [1, \"requirement-template-title\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"close-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"requirement-template-body\"], [1, \"search-section\"], [1, \"section-title\"], [\"icon\", \"search-outline\", 1, \"mr-2\"], [1, \"search-form\"], [1, \"row\"], [1, \"col-md-4\"], [1, \"search-label\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5340\\u57DF\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u72C0\\u614B\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"mt-3\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u985E\\u578B\", \"multiple\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u5168\\u90E8\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [1, \"search-actions\", \"mt-3\"], [\"nbButton\", \"\", \"status\", \"basic\", 1, \"mr-2\", 3, \"click\"], [\"icon\", \"refresh-outline\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\"], [\"icon\", \"search-outline\"], [1, \"selection-section\"], [\"icon\", \"list-outline\", 1, \"mr-2\"], [\"class\", \"selected-count\", 4, \"ngIf\"], [\"class\", \"select-all-control\", 4, \"ngIf\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"requirement-list\", 4, \"ngIf\"], [\"class\", \"no-data-state\", 4, \"ngIf\"], [\"class\", \"pagination-section\", 4, \"ngIf\"], [\"class\", \"selection-summary\", 4, \"ngIf\"], [1, \"requirement-template-footer\"], [1, \"footer-actions\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [1, \"selected-count\"], [1, \"select-all-control\"], [3, \"ngModelChange\", \"ngModel\", \"indeterminate\"], [1, \"loading-state\"], [\"icon\", \"loader-outline\", 1, \"spinning\"], [1, \"requirement-list\"], [\"class\", \"requirement-item\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"requirement-item\"], [1, \"requirement-checkbox\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"requirement-info\"], [1, \"requirement-main\"], [1, \"requirement-name\"], [1, \"requirement-location\"], [1, \"requirement-details\"], [1, \"detail-item\"], [1, \"detail-label\"], [1, \"detail-value\"], [1, \"no-data-state\"], [\"icon\", \"inbox-outline\", 1, \"no-data-icon\"], [1, \"no-data-text\"], [1, \"pagination-section\"], [1, \"pagination-info\"], [1, \"pagination-controls\"], [\"nbButton\", \"\", \"size\", \"small\", \"status\", \"basic\", 3, \"click\", \"disabled\"], [\"icon\", \"chevron-left-outline\"], [1, \"page-info\"], [\"icon\", \"chevron-right-outline\"], [1, \"selection-summary\"], [1, \"summary-title\"], [1, \"summary-content\"], [1, \"summary-item\"], [1, \"summary-label\"], [1, \"summary-value\"], [1, \"summary-value\", \"total-price\"]],\n        template: function RequirementTemplateSelectorComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\", 1)(2, \"div\", 2);\n            i0.ɵɵtext(3, \"\\u9700\\u6C42\\u9805\\u76EE\\u9078\\u64C7\\u5668\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_Template_button_click_4_listener() {\n              return ctx.close();\n            });\n            i0.ɵɵelement(5, \"nb-icon\", 4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"nb-card-body\", 5)(7, \"div\", 6)(8, \"div\", 7);\n            i0.ɵɵelement(9, \"nb-icon\", 8);\n            i0.ɵɵtext(10, \"\\u641C\\u5C0B\\u689D\\u4EF6 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"label\", 12);\n            i0.ɵɵtext(15, \"\\u5340\\u57DF\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_input_ngModelChange_16_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CLocation, $event) || (ctx.searchFilters.CLocation = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 11)(18, \"label\", 12);\n            i0.ɵɵtext(19, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"input\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_input_ngModelChange_20_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CRequirement, $event) || (ctx.searchFilters.CRequirement = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 11)(22, \"label\", 12);\n            i0.ɵɵtext(23, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"nb-select\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_24_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CStatus, $event) || (ctx.searchFilters.CStatus = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(25, RequirementTemplateSelectorComponent_nb_option_25_Template, 2, 2, \"nb-option\", 16);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(26, \"div\", 17)(27, \"div\", 11)(28, \"label\", 12);\n            i0.ɵɵtext(29, \"\\u623F\\u5C4B\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"nb-select\", 18);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_30_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CHouseType, $event) || (ctx.searchFilters.CHouseType = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(31, RequirementTemplateSelectorComponent_nb_option_31_Template, 2, 2, \"nb-option\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"div\", 11)(33, \"label\", 12);\n            i0.ɵɵtext(34, \"\\u9810\\u7D04\\u9700\\u6C42\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"nb-select\", 19);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_35_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CIsShow, $event) || (ctx.searchFilters.CIsShow = $event);\n              return $event;\n            });\n            i0.ɵɵelementStart(36, \"nb-option\", 20);\n            i0.ɵɵtext(37, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"nb-option\", 20);\n            i0.ɵɵtext(39, \"\\u662F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"nb-option\", 20);\n            i0.ɵɵtext(41, \"\\u5426\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(42, \"div\", 11)(43, \"label\", 12);\n            i0.ɵɵtext(44, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"nb-select\", 19);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementTemplateSelectorComponent_Template_nb_select_ngModelChange_45_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchFilters.CIsSimple, $event) || (ctx.searchFilters.CIsSimple = $event);\n              return $event;\n            });\n            i0.ɵɵelementStart(46, \"nb-option\", 20);\n            i0.ɵɵtext(47, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"nb-option\", 20);\n            i0.ɵɵtext(49, \"\\u662F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"nb-option\", 20);\n            i0.ɵɵtext(51, \"\\u5426\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(52, \"div\", 21)(53, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_Template_button_click_53_listener() {\n              return ctx.onReset();\n            });\n            i0.ɵɵelement(54, \"nb-icon\", 23);\n            i0.ɵɵtext(55, \"\\u91CD\\u7F6E \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_Template_button_click_56_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵelement(57, \"nb-icon\", 25);\n            i0.ɵɵtext(58, \"\\u641C\\u5C0B \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(59, \"div\", 26)(60, \"div\", 7);\n            i0.ɵɵelement(61, \"nb-icon\", 27);\n            i0.ɵɵtext(62, \"\\u9078\\u64C7\\u9805\\u76EE \");\n            i0.ɵɵtemplate(63, RequirementTemplateSelectorComponent_span_63_Template, 2, 1, \"span\", 28);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(64, RequirementTemplateSelectorComponent_div_64_Template, 3, 2, \"div\", 29)(65, RequirementTemplateSelectorComponent_div_65_Template, 3, 0, \"div\", 30)(66, RequirementTemplateSelectorComponent_div_66_Template, 2, 1, \"div\", 31)(67, RequirementTemplateSelectorComponent_div_67_Template, 4, 0, \"div\", 32)(68, RequirementTemplateSelectorComponent_div_68_Template, 10, 6, \"div\", 33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(69, RequirementTemplateSelectorComponent_div_69_Template, 15, 5, \"div\", 34);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(70, \"nb-card-footer\", 35)(71, \"div\", 36)(72, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_Template_button_click_72_listener() {\n              return ctx.close();\n            });\n            i0.ɵɵtext(73, \" \\u53D6\\u6D88 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"button\", 37);\n            i0.ɵɵlistener(\"click\", function RequirementTemplateSelectorComponent_Template_button_click_74_listener() {\n              return ctx.confirmSelection();\n            });\n            i0.ɵɵtext(75);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CLocation);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CRequirement);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CHouseType);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CIsShow);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", false);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchFilters.CIsSimple);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", true);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", false);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngIf\", ctx.getSelectedItems().length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.multiple && ctx.requirements.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.requirements.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.requirements.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.totalRecords > ctx.pageSize);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.getSelectedItems().length > 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.getSelectedItems().length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \\u78BA\\u8A8D\\u9078\\u64C7 (\", ctx.getSelectedItems().length, \") \");\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.DecimalPipe, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbIconModule, i2.NbIconComponent, NbCheckboxModule, i2.NbCheckboxComponent, NbInputModule, i2.NbInputDirective, NbSelectModule, i2.NbSelectComponent, i2.NbOptionComponent, NbOptionModule],\n        styles: [\".requirement-template-dialog[_ngcontent-%COMP%]{width:90vw;max-width:1200px;max-height:90vh;margin:0 auto}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem 1.5rem;border-bottom:1px solid #e4e9f2;background-color:#f7f9fc}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-header[_ngcontent-%COMP%]   .requirement-template-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#2c3e50;display:flex;align-items:center}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-header[_ngcontent-%COMP%]   .requirement-template-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%]{margin-right:.5rem;color:#36f}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]{padding:.25rem;min-width:auto}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%]{font-size:1.25rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]{padding:1.5rem;max-height:70vh;overflow-y:auto}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;color:#2c3e50;margin-bottom:1rem;display:flex;align-items:center;padding-bottom:.5rem;border-bottom:2px solid #3366ff}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%]{color:#36f}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]{margin-left:.5rem;font-size:.9rem;color:#27ae60;font-weight:500}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]{margin-bottom:2rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:1.5rem;border-radius:8px;border:1px solid #e9ecef}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-label[_ngcontent-%COMP%]{display:block;font-weight:500;color:#495057;margin-bottom:.5rem;font-size:.9rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{margin-left:-.5rem;margin-right:-.5rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{padding-left:.5rem;padding-right:.5rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]{text-align:right;padding-top:1rem;border-top:1px solid #dee2e6}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:100px}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .select-all-control[_ngcontent-%COMP%]{margin-bottom:1rem;padding:.75rem;background-color:#f8f9fa;border-radius:6px;border:1px solid #e9ecef}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]{text-align:center;padding:3rem;color:#6c757d}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .spinning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite;font-size:2rem;margin-bottom:1rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;padding:1rem;border:1px solid #e9ecef;border-radius:8px;margin-bottom:.75rem;background-color:#fff;transition:all .2s ease;cursor:pointer}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover{border-color:#36f;box-shadow:0 2px 8px #3366ff1a}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item.selected[_ngcontent-%COMP%]{border-color:#27ae60;background-color:#f8fff9;box-shadow:0 2px 8px #27ae601a}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-checkbox[_ngcontent-%COMP%]{margin-right:1rem;margin-top:.25rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]{flex:1}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-main[_ngcontent-%COMP%]{margin-bottom:.75rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-main[_ngcontent-%COMP%]   .requirement-name[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;color:#2c3e50;margin-bottom:.25rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-main[_ngcontent-%COMP%]   .requirement-location[_ngcontent-%COMP%]{font-size:.9rem;color:#6c757d}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:.5rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:.85rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%]{font-weight:500;color:#495057;margin-right:.5rem;min-width:60px}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%]{color:#6c757d}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .no-data-state[_ngcontent-%COMP%]{text-align:center;padding:3rem;color:#6c757d}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .no-data-state[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:1rem;opacity:.5}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .no-data-state[_ngcontent-%COMP%]   .no-data-text[_ngcontent-%COMP%]{font-size:1.1rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .pagination-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:1.5rem;padding-top:1rem;border-top:1px solid #e9ecef}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .pagination-section[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]{font-size:.9rem;color:#6c757d}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .pagination-section[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .pagination-section[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .page-info[_ngcontent-%COMP%]{margin:0 .5rem;font-size:.9rem;color:#495057;min-width:60px;text-align:center}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]{margin-top:2rem;padding:1rem;background-color:#e8f5e8;border:1px solid #27ae60;border-radius:8px}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-title[_ngcontent-%COMP%]{font-weight:600;color:#27ae60;margin-bottom:.75rem;font-size:1rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]{display:flex;align-items:center}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-label[_ngcontent-%COMP%]{font-weight:500;color:#495057;margin-right:.5rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%]{color:#2c3e50;font-weight:600}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .summary-value.total-price[_ngcontent-%COMP%]{color:#27ae60;font-size:1.1rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-footer[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-top:1px solid #e4e9f2;background-color:#f7f9fc}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;align-items:center}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.requirement-template-dialog[_ngcontent-%COMP%]{width:95vw;max-height:95vh}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%], .requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]{padding:1rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%]{grid-template-columns:1fr}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-checkbox[_ngcontent-%COMP%]{margin-bottom:.5rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-section[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%]{grid-template-columns:1fr}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-body[_ngcontent-%COMP%]   .selection-summary[_ngcontent-%COMP%]   .summary-content[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.5rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}.requirement-template-dialog[_ngcontent-%COMP%]   .requirement-template-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}}\"]\n      });\n    }\n  }\n  return RequirementTemplateSelectorComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}