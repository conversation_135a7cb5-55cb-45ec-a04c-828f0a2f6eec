{"ast": null, "code": "import { SpaceTemplateSelectorComponent } from './space-template-selector.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nexport class SpaceTemplateSelectorService {\n  constructor(dialogService) {\n    this.dialogService = dialogService;\n  }\n  /**\n   * 開啟空間模板選擇器對話框\n   * @param buildCaseId 建案ID\n   * @returns Observable 當用戶確認套用模板時回傳配置，取消時回傳 null\n   */\n  openSelector(buildCaseId = '') {\n    const dialogRef = this.dialogService.open(SpaceTemplateSelectorComponent, {\n      context: {\n        buildCaseId: buildCaseId\n      },\n      closeOnBackdropClick: true,\n      closeOnEsc: true,\n      hasScroll: true,\n      autoFocus: false\n    });\n    // 監聽模板套用事件\n    const templateApplied$ = dialogRef.componentRef.instance.templateApplied.asObservable();\n    // 當有模板套用時，關閉對話框並回傳結果\n    templateApplied$.subscribe(config => {\n      dialogRef.close(config);\n    });\n    return dialogRef.onClose;\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorService)(i0.ɵɵinject(i1.NbDialogService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SpaceTemplateSelectorService,\n      factory: SpaceTemplateSelectorService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["SpaceTemplateSelectorComponent", "SpaceTemplateSelectorService", "constructor", "dialogService", "openSelector", "buildCaseId", "dialogRef", "open", "context", "closeOnBackdropClick", "closeOnEsc", "hasScroll", "autoFocus", "templateApplied$", "componentRef", "instance", "templateApplied", "asObservable", "subscribe", "config", "close", "onClose", "i0", "ɵɵinject", "i1", "NbDialogService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { Observable } from 'rxjs';\r\nimport { SpaceTemplateSelectorComponent, SpaceTemplateConfig } from './space-template-selector.component';\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n})\r\nexport class SpaceTemplateSelectorService {\r\n\r\n    constructor(private dialogService: NbDialogService) { }\r\n\r\n    /**\r\n     * 開啟空間模板選擇器對話框\r\n     * @param buildCaseId 建案ID\r\n     * @returns Observable 當用戶確認套用模板時回傳配置，取消時回傳 null\r\n     */\r\n    openSelector(buildCaseId: string = ''): Observable<SpaceTemplateConfig | null> {\r\n        const dialogRef = this.dialogService.open(SpaceTemplateSelectorComponent, {\r\n            context: {\r\n                buildCaseId: buildCaseId\r\n            },\r\n            closeOnBackdropClick: true,\r\n            closeOnEsc: true,\r\n            hasScroll: true,\r\n            autoFocus: false\r\n        });\r\n\r\n        // 監聽模板套用事件\r\n        const templateApplied$ = dialogRef.componentRef.instance.templateApplied.asObservable();\r\n\r\n        // 當有模板套用時，關閉對話框並回傳結果\r\n        templateApplied$.subscribe((config: SpaceTemplateConfig) => {\r\n            dialogRef.close(config);\r\n        });\r\n\r\n        return dialogRef.onClose;\r\n    }\r\n}\r\n"], "mappings": "AAGA,SAASA,8BAA8B,QAA6B,qCAAqC;;;AAKzG,OAAM,MAAOC,4BAA4B;EAErCC,YAAoBC,aAA8B;IAA9B,KAAAA,aAAa,GAAbA,aAAa;EAAqB;EAEtD;;;;;EAKAC,YAAYA,CAACC,WAAA,GAAsB,EAAE;IACjC,MAAMC,SAAS,GAAG,IAAI,CAACH,aAAa,CAACI,IAAI,CAACP,8BAA8B,EAAE;MACtEQ,OAAO,EAAE;QACLH,WAAW,EAAEA;OAChB;MACDI,oBAAoB,EAAE,IAAI;MAC1BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE;KACd,CAAC;IAEF;IACA,MAAMC,gBAAgB,GAAGP,SAAS,CAACQ,YAAY,CAACC,QAAQ,CAACC,eAAe,CAACC,YAAY,EAAE;IAEvF;IACAJ,gBAAgB,CAACK,SAAS,CAAEC,MAA2B,IAAI;MACvDb,SAAS,CAACc,KAAK,CAACD,MAAM,CAAC;IAC3B,CAAC,CAAC;IAEF,OAAOb,SAAS,CAACe,OAAO;EAC5B;;;uCA7BSpB,4BAA4B,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;aAA5BxB,4BAA4B;MAAAyB,OAAA,EAA5BzB,4BAA4B,CAAA0B,IAAA;MAAAC,UAAA,EAFzB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}