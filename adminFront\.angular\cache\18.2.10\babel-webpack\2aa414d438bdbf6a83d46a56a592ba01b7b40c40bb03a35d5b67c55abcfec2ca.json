{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { GetRequirement } from 'src/services/api/models';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\nimport { TemplateCreatorComponent } from 'src/app/shared/components/template-creator/template-creator.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = () => [];\nfunction RequirementManagementComponent_div_6_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"label\", 68);\n    i0.ɵɵtext(2, \"\\u5EFA\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_div_6_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.getListRequirementRequest.CBuildCaseID, $event) || (ctx_r2.getListRequirementRequest.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, RequirementManagementComponent_div_6_nb_option_4_Template, 2, 2, \"nb-option\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.getListRequirementRequest.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildCaseList);\n  }\n}\nfunction RequirementManagementComponent_nb_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r5.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r5.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_62_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(162);\n      return i0.ɵɵresetView(ctx_r2.add(dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_93_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_93_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const data_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(162);\n      return i0.ɵɵresetView(ctx_r2.onEdit(data_r9, dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 76);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_93_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_93_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const data_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDelete(data_r9));\n    });\n    i0.ɵɵelement(1, \"i\", 78);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 70)(1, \"td\", 71);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 72);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 72);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 72);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 72);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 72);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 72);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 72);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 72);\n    i0.ɵɵtemplate(22, RequirementManagementComponent_tr_93_button_22_Template, 3, 0, \"button\", 73)(23, RequirementManagementComponent_tr_93_button_23_Template, 3, 0, \"button\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseType(data_r9.CHouseType || i0.ɵɵpureFunction0(15, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 11, data_r9.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getCIsShowText(data_r9));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getCIsSimpleText(data_r9));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 13, data_r9.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction RequirementManagementComponent_span_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 79);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getSelectedRequirements().length, \" \");\n  }\n}\nfunction RequirementManagementComponent_small_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 80);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5148\\u52FE\\u9078\\u8981\\u52A0\\u5165\\u6A21\\u677F\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_small_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u64C7 \", ctx_r2.getSelectedRequirements().length, \" \\u500B\\u9805\\u76EE \");\n  }\n}\nfunction RequirementManagementComponent_button_131_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_131_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectAllRequirements());\n    });\n    i0.ɵɵelement(1, \"i\", 83);\n    i0.ɵɵtext(2, \"\\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_button_132_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_132_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearAllSelections());\n    });\n    i0.ɵɵelement(1, \"i\", 85);\n    i0.ɵɵtext(2, \"\\u6E05\\u9664\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_159_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_159_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const data_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(162);\n      return i0.ɵɵresetView(ctx_r2.onEdit(data_r16, dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 76);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_159_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_159_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const data_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDelete(data_r16));\n    });\n    i0.ɵɵelement(1, \"i\", 78);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_159_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 70)(1, \"td\", 72)(2, \"input\", 86);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_tr_159_Template_input_ngModelChange_2_listener($event) {\n      const data_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      i0.ɵɵtwoWayBindingSet(data_r16.selected, $event) || (data_r16.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function RequirementManagementComponent_tr_159_Template_input_change_2_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRequirementSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 72);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 72);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 72);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 72);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 72);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 72);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 72);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 72);\n    i0.ɵɵtemplate(22, RequirementManagementComponent_tr_159_button_22_Template, 3, 0, \"button\", 73)(23, RequirementManagementComponent_tr_159_button_23_Template, 3, 0, \"button\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r16 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"table-active\", data_r16.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", data_r16.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r16.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r16.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseType(data_r16.CHouseType || i0.ɵɵpureFunction0(17, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r16.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 13, data_r16.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getCIsShowText(data_r16));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getCIsSimpleText(data_r16));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 15, data_r16.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_161_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_161_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_161_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_161_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_161_app_form_group_10_nb_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const b_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", b_r21.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", b_r21.CBuildCaseName, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_161_app_form_group_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-form-group\", 92)(1, \"nb-select\", 106);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_161_app_form_group_10_Template_nb_select_selectedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CBuildCaseID, $event) || (ctx_r2.saveRequirement.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_161_app_form_group_10_nb_option_2_Template, 2, 2, \"nb-option\", 97);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\\u540D\\u7A31\")(\"labelFor\", \"CBuildCaseID\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildCaseList);\n  }\n}\nfunction RequirementManagementComponent_ng_template_161_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r22.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r22.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_161_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r23.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_161_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 87)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_161_span_2_Template, 2, 0, \"span\", 88)(3, RequirementManagementComponent_ng_template_161_span_3_Template, 2, 0, \"span\", 88)(4, RequirementManagementComponent_ng_template_161_span_4_Template, 2, 0, \"span\", 88)(5, RequirementManagementComponent_ng_template_161_span_5_Template, 2, 0, \"span\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 89)(7, \"div\", 7)(8, \"div\", 90)(9, \"div\", 7);\n    i0.ɵɵtemplate(10, RequirementManagementComponent_ng_template_161_app_form_group_10_Template, 3, 5, \"app-form-group\", 91);\n    i0.ɵɵelementStart(11, \"app-form-group\", 92)(12, \"input\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_161_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRequirement, $event) || (ctx_r2.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"app-form-group\", 92)(14, \"input\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_161_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CGroupName, $event) || (ctx_r2.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"app-form-group\", 92)(16, \"input\", 95);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_161_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CSort, $event) || (ctx_r2.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 92)(18, \"nb-select\", 96);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_161_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CHouseType, $event) || (ctx_r2.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_161_nb_option_19_Template, 2, 2, \"nb-option\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 92)(21, \"nb-select\", 98);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_161_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CStatus, $event) || (ctx_r2.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, RequirementManagementComponent_ng_template_161_nb_option_22_Template, 2, 2, \"nb-option\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"app-form-group\", 92)(24, \"input\", 99);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_161_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnitPrice, $event) || (ctx_r2.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"app-form-group\", 92)(26, \"input\", 100);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_161_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnit, $event) || (ctx_r2.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 92)(28, \"nb-checkbox\", 101);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_161_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CIsShow, $event) || (ctx_r2.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u986F\\u793A\\u5728\\u9810\\u7D04\\u9700\\u6C42\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 92)(31, \"nb-checkbox\", 102);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_161_Template_nb_checkbox_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CIsSimple, $event) || (ctx_r2.saveRequirement.CIsSimple = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(32, \" \\u8A2D\\u5B9A\\u70BA\\u7C21\\u6613\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"app-form-group\", 92)(34, \"textarea\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_161_Template_textarea_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRemark, $event) || (ctx_r2.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(35, \"nb-card-footer\")(36, \"div\", 7)(37, \"div\", 104)(38, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_161_Template_button_click_38_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save(ref_r24));\n    });\n    i0.ɵɵtext(39, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_161_Template_button_click_40_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r19).dialogRef;\n      return i0.ɵɵresetView(ref_r24.close());\n    });\n    i0.ɵɵtext(41, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true && ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false && ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true && ctx_r2.currentTab === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false && ctx_r2.currentTab === 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CHouseType);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.currentTab === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u9810\\u7D04\\u9700\\u6C42\")(\"labelFor\", \"CIsShow\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u7C21\\u6613\\u5BA2\\u8B8A\")(\"labelFor\", \"CIsSimple\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CIsSimple);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_163_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_163_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_163_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r26 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r26.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r26.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_163_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r27 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r27.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r27.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_163_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 87)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_163_span_2_Template, 2, 0, \"span\", 88)(3, RequirementManagementComponent_ng_template_163_span_3_Template, 2, 0, \"span\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 89)(5, \"div\", 7)(6, \"div\", 90)(7, \"div\", 7)(8, \"app-form-group\", 92)(9, \"input\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_163_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRequirement, $event) || (ctx_r2.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"app-form-group\", 92)(11, \"input\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_163_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CGroupName, $event) || (ctx_r2.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"app-form-group\", 92)(13, \"input\", 95);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_163_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CSort, $event) || (ctx_r2.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"app-form-group\", 92)(15, \"nb-select\", 96);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_163_Template_nb_select_selectedChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CHouseType, $event) || (ctx_r2.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(16, RequirementManagementComponent_ng_template_163_nb_option_16_Template, 2, 2, \"nb-option\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 92)(18, \"nb-select\", 98);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_163_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CStatus, $event) || (ctx_r2.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_163_nb_option_19_Template, 2, 2, \"nb-option\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 92)(21, \"input\", 99);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_163_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnitPrice, $event) || (ctx_r2.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"app-form-group\", 92)(23, \"input\", 100);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_163_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnit, $event) || (ctx_r2.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"app-form-group\", 92)(25, \"nb-checkbox\", 101);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_163_Template_nb_checkbox_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CIsShow, $event) || (ctx_r2.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(26, \" \\u986F\\u793A\\u5728\\u9810\\u7D04\\u9700\\u6C42\\u6E05\\u55AE \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 92)(28, \"textarea\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_163_Template_textarea_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRemark, $event) || (ctx_r2.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(29, \"nb-card-footer\")(30, \"div\", 7)(31, \"div\", 104)(32, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_163_Template_button_click_32_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r25).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveTemplate(ref_r28));\n    });\n    i0.ɵɵtext(33, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_163_Template_button_click_34_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r25).dialogRef;\n      return i0.ɵɵresetView(ref_r28.close());\n    });\n    i0.ɵɵtext(35, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CHouseType);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.currentTab === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u9810\\u7D04\\u9700\\u6C42\")(\"labelFor\", \"CIsShow\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_165_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-template-viewer\", 108);\n    i0.ɵɵlistener(\"selectTemplate\", function RequirementManagementComponent_ng_template_165_Template_app_template_viewer_selectTemplate_0_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSelectTemplate($event));\n    })(\"close\", function RequirementManagementComponent_ng_template_165_Template_app_template_viewer_close_0_listener() {\n      const ref_r30 = i0.ɵɵrestoreView(_r29).dialogRef;\n      return i0.ɵɵresetView(ref_r30.close());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"templateType\", 1);\n  }\n}\nfunction RequirementManagementComponent_ng_template_167_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-template-creator\", 109);\n    i0.ɵɵlistener(\"templateCreated\", function RequirementManagementComponent_ng_template_167_Template_app_template_creator_templateCreated_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTemplateCreated());\n    })(\"close\", function RequirementManagementComponent_ng_template_167_Template_app_template_creator_close_0_listener() {\n      const ref_r32 = i0.ɵɵrestoreView(_r31).dialogRef;\n      return i0.ɵɵresetView(ref_r32.close());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"availableData\", ctx_r2.selectedRequirementsForTemplate)(\"templateType\", 1);\n  }\n}\nexport class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.currentTab = 0; // 追蹤當前選中的 tab\n    this.isCreatingTemplate = false; // 控制是否正在創建模板\n    this.selectedRequirementsForTemplate = []; // 用於模板創建的選中項目\n    // Tab 切換事件處理\n    this.isFirstTabChange = true;\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CIsSimple = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CGroupName = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        if (this.currentTab === 0) {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        } else {\n          this.getListRequirementRequest.CBuildCaseID = 0;\n        }\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 根據當前 tab 決定是否需要驗證建案名稱\n    if (this.currentTab === 0) {\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    }\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[排序]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false,\n      CIsSimple: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 根據當前 tab 決定是否需要建案ID\n    if (this.currentTab === 0) {\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    } else {\n      // 模板頁面 - 設定建案ID為0，CHouseType預設[1,2]\n      this.saveRequirement.CBuildCaseID = 0;\n      this.saveRequirement.CHouseType = [1, 2];\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        // 編輯時如果是共用tab，CHouseType強制為[1,2]\n        if (_this.currentTab === 1) {\n          _this.saveRequirement.CHouseType = [1, 2];\n        }\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\n    if (this.currentTab === 1) {\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 只在建案 tab 下且有建案時才查詢\n      if (this.currentTab === 0 && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      } else if (this.currentTab === 1) {\n        this.getListRequirementRequest.CBuildCaseID = 0;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動，CHouseType預設為[1,2]\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n      this.getListRequirementRequest.CHouseType = [1, 2];\n    } else {\n      // 建案頁面的邏輯保持不變\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          // 將 API 返回的數據轉換為 SelectableRequirement 並初始化 selected 屬性\n          this.requirementList = res.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false,\n            CIsSimple: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          // 共用tab時CHouseType強制為[1,2]\n          this.saveRequirement.CHouseType = this.currentTab === 1 ? [1, 2] : res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          // TODO: 等後端API更新後啟用這行\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n          this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  getCIsSimpleText(data) {\n    return data.CIsSimple ? '是' : '否';\n  }\n  onTabChange(event) {\n    // 避免頁面初始化時自動觸發重複查詢\n    if (this.isFirstTabChange) {\n      this.isFirstTabChange = false;\n      return;\n    }\n    // 根據 tabTitle 來判斷當前頁面\n    if (event.tabTitle === '共用') {\n      this.currentTab = 1;\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      this.currentTab = 0;\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    }\n    this.getList();\n  }\n  // 新增模板\n  addTemplate(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false,\n      CIsSimple: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 模板設定建案ID為0，CHouseType預設[1,2]\n    this.saveRequirement.CBuildCaseID = 0;\n    this.saveRequirement.CHouseType = [1, 2];\n    this.dialogService.open(dialog);\n  }\n  // 編輯模板\n  onEditTemplate(data, dialog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this2.isNew = false;\n      try {\n        yield _this2.getData();\n        // 編輯模板時CHouseType強制為[1,2]\n        _this2.saveRequirement.CHouseType = [1, 2];\n        _this2.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get template data\", error);\n      }\n    })();\n  }\n  // 保存模板\n  saveTemplate(ref) {\n    // 模板驗證（不包含建案名稱）\n    this.valid.clear();\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    // CHouseType強制為[1,2]\n    if (this.currentTab === 1) {\n      this.saveRequirement.CHouseType = [1, 2];\n    }\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 確保模板建案ID為0\n    const templateData = {\n      ...this.saveRequirement\n    };\n    templateData.CBuildCaseID = 0;\n    // CHouseType強制為[1,2]\n    if (this.currentTab === 1) {\n      templateData.CHouseType = [1, 2];\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: templateData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  openTemplateViewer(templateViewerDialog) {\n    this.dialogService.open(templateViewerDialog);\n  }\n  onSelectTemplate(tpl) {\n    // 查看模板邏輯\n  }\n  // 獲取選中的需求項目\n  getSelectedRequirements() {\n    return this.requirementList.filter(req => req.selected);\n  }\n  // 選中狀態變更處理\n  onRequirementSelectionChange() {\n    // 可以在這裡添加額外的邏輯，比如更新選中計數等\n  }\n  // 全選功能\n  selectAllRequirements() {\n    this.requirementList.forEach(req => req.selected = true);\n  }\n  // 清除所有選擇\n  clearAllSelections() {\n    this.requirementList.forEach(req => req.selected = false);\n  }\n  // 檢查是否全選\n  isAllSelected() {\n    return this.requirementList.length > 0 && this.requirementList.every(req => req.selected);\n  }\n  // 檢查是否部分選中（用於 indeterminate 狀態）\n  isIndeterminate() {\n    const selectedCount = this.requirementList.filter(req => req.selected).length;\n    return selectedCount > 0 && selectedCount < this.requirementList.length;\n  }\n  // 切換全選狀態\n  toggleSelectAll(event) {\n    const isChecked = event.target.checked;\n    this.requirementList.forEach(req => req.selected = isChecked);\n  }\n  // 打開模板創建器\n  openTemplateCreator(templateCreatorDialog) {\n    const selectedRequirements = this.getSelectedRequirements();\n    if (selectedRequirements.length === 0) {\n      this.message.showErrorMSG('請先選擇要加入模板的項目');\n      return;\n    }\n    // 將選中的項目存儲在屬性中，確保在模板中保持引用一致性\n    this.selectedRequirementsForTemplate = [...selectedRequirements];\n    this.isCreatingTemplate = true;\n    const dialogRef = this.dialogService.open(templateCreatorDialog);\n    // 當對話框關閉時重置狀態\n    dialogRef.onClose.subscribe(() => {\n      this.isCreatingTemplate = false;\n      this.selectedRequirementsForTemplate = [];\n    });\n  }\n  // 模板創建成功回調\n  onTemplateCreated() {\n    this.message.showSucessMSG('模板創建成功');\n    // 清除選中狀態\n    this.clearAllSelections();\n  }\n  static {\n    this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementManagementComponent,\n      selectors: [[\"app-requirement-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 169,\n      vars: 34,\n      consts: [[\"dialog\", \"\"], [\"templateDialog\", \"\"], [\"templateViewerDialog\", \"\"], [\"templateCreatorDialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\", \"pb-0\"], [1, \"col-12\"], [1, \"row\"], [\"class\", \"form-group col-12 col-md-4\", 4, \"ngIf\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [\"for\", \"isShow\", 1, \"label\", \"mr-2\"], [\"for\", \"isSimple\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [3, \"changeTab\"], [\"tabTitle\", \"\\u55AE\\u5EFA\\u6848\"], [1, \"pt-3\"], [1, \"col-12\", \"mt-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [\"tabTitle\", \"\\u8DE8\\u5EFA\\u6848\"], [1, \"col-12\", \"mb-3\"], [1, \"page-description-card\"], [1, \"d-flex\", \"align-items-center\", \"mb-2\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"mr-2\"], [1, \"mb-0\", \"text-primary\"], [1, \"mb-2\", \"text-muted\"], [1, \"feature-highlights\"], [1, \"badge\", \"badge-light\", \"mr-2\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"fas\", \"fa-layer-group\", \"mr-1\"], [1, \"badge\", \"badge-light\"], [1, \"fas\", \"fa-share-alt\", \"mr-1\"], [1, \"template-creation-controls\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"d-flex\", \"align-items-center\"], [1, \"template-action-buttons\", \"mr-3\"], [1, \"btn\", \"btn-primary\", \"mr-2\", 3, \"click\", \"disabled\"], [\"class\", \"badge badge-light ml-1\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"mr-1\"], [1, \"template-status-info\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"class\", \"text-success\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-secondary btn-sm mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"checked\", \"indeterminate\"], [\"class\", \"d-flex\", 3, \"table-active\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"d-flex\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"badge\", \"badge-light\", \"ml-1\"], [1, \"text-muted\"], [1, \"text-success\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-check-square\", \"mr-1\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\", 4, \"ngIf\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CGroupName\", \"name\", \"CGroupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CSort\", \"name\", \"CSort\", \"placeholder\", \"\\u6392\\u5E8F\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\", \"disabled\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CUnit\", \"name\", \"CUnit\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsShow\", \"name\", \"CIsShow\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsSimple\", \"name\", \"CIsSimple\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CBuildCaseID\", \"name\", \"CBuildCaseID\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\"], [3, \"selectTemplate\", \"close\", \"templateType\"], [3, \"templateCreated\", \"close\", \"availableData\", \"templateType\"]],\n      template: function RequirementManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 4)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 5)(4, \"div\", 6)(5, \"div\", 7);\n          i0.ɵɵtemplate(6, RequirementManagementComponent_div_6_Template, 5, 2, \"div\", 8);\n          i0.ɵɵelementStart(7, \"div\", 9)(8, \"label\", 10);\n          i0.ɵɵtext(9, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"label\", 12);\n          i0.ɵɵtext(13, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CGroupName, $event) || (ctx.getListRequirementRequest.CGroupName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"div\", 9)(17, \"label\", 14);\n          i0.ɵɵtext(18, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(20, RequirementManagementComponent_nb_option_20_Template, 2, 2, \"nb-option\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 9)(22, \"label\", 17);\n          i0.ɵɵtext(23, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"nb-select\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(25, \"nb-option\", 19);\n          i0.ɵɵtext(26, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-option\", 19);\n          i0.ɵɵtext(28, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"nb-option\", 19);\n          i0.ɵɵtext(30, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 9)(32, \"label\", 20);\n          i0.ɵɵtext(33, \"\\u9810\\u7D04\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"nb-select\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsShow, $event) || (ctx.getListRequirementRequest.CIsShow = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(35, \"nb-option\", 19);\n          i0.ɵɵtext(36, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"nb-option\", 19);\n          i0.ɵɵtext(38, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"nb-option\", 19);\n          i0.ɵɵtext(40, \"\\u5426\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 7)(42, \"div\", 9)(43, \"label\", 21);\n          i0.ɵɵtext(44, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"nb-select\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_45_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsSimple, $event) || (ctx.getListRequirementRequest.CIsSimple = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(46, \"nb-option\", 19);\n          i0.ɵɵtext(47, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"nb-option\", 19);\n          i0.ɵɵtext(49, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"nb-option\", 19);\n          i0.ɵɵtext(51, \"\\u5426\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(52, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 7);\n          i0.ɵɵelement(54, \"div\", 22);\n          i0.ɵɵelementStart(55, \"div\", 23)(56, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_56_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetSearch());\n          });\n          i0.ɵɵelement(57, \"i\", 25);\n          i0.ɵɵtext(58, \"\\u91CD\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_59_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(60, \"i\", 27);\n          i0.ɵɵtext(61, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(62, RequirementManagementComponent_button_62_Template, 3, 0, \"button\", 28);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(63, \"nb-card-body\", 5)(64, \"nb-tabset\", 29);\n          i0.ɵɵlistener(\"changeTab\", function RequirementManagementComponent_Template_nb_tabset_changeTab_64_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTabChange($event));\n          });\n          i0.ɵɵelementStart(65, \"nb-tab\", 30)(66, \"div\", 31)(67, \"div\", 32)(68, \"div\", 33)(69, \"table\", 34)(70, \"thead\")(71, \"tr\", 35)(72, \"th\", 36);\n          i0.ɵɵtext(73, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"th\", 36);\n          i0.ɵɵtext(75, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"th\", 37);\n          i0.ɵɵtext(77, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"th\", 37);\n          i0.ɵɵtext(79, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"th\", 37);\n          i0.ɵɵtext(81, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"th\", 37);\n          i0.ɵɵtext(83, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"th\", 37);\n          i0.ɵɵtext(85, \"\\u9810\\u7D04\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"th\", 37);\n          i0.ɵɵtext(87, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"th\", 37);\n          i0.ɵɵtext(89, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"th\", 37);\n          i0.ɵɵtext(91, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(92, \"tbody\");\n          i0.ɵɵtemplate(93, RequirementManagementComponent_tr_93_Template, 24, 16, \"tr\", 38);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(94, \"ngx-pagination\", 39);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_94_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_94_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(95, \"nb-tab\", 40)(96, \"div\", 31)(97, \"div\", 41)(98, \"div\", 42)(99, \"div\", 43);\n          i0.ɵɵelement(100, \"i\", 44);\n          i0.ɵɵelementStart(101, \"h6\", 45);\n          i0.ɵɵtext(102, \"\\u5171\\u7528\\u9700\\u6C42\\u7BA1\\u7406\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(103, \"p\", 46);\n          i0.ɵɵtext(104, \" \\u6B64\\u9801\\u9762\\u7528\\u65BC\\u7BA1\\u7406\\u5171\\u7528\\u7684\\u5BA2\\u8B8A\\u9700\\u6C42\\u9805\\u76EE\\uFF0C\\u9019\\u4E9B\\u9805\\u76EE\\u53EF\\u4EE5\\u88AB\\u591A\\u500B\\u5EFA\\u6848\\u91CD\\u8907\\u4F7F\\u7528\\u3002 \\u60A8\\u53EF\\u4EE5\\u5728\\u6B64\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u6216\\u522A\\u9664\\u5171\\u7528\\u9700\\u6C42\\uFF0C\\u4E5F\\u53EF\\u4EE5\\u5C07\\u9078\\u4E2D\\u7684\\u9700\\u6C42\\u9805\\u76EE\\u5EFA\\u7ACB\\u70BA\\u6A21\\u677F\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"div\", 47)(106, \"span\", 48);\n          i0.ɵɵelement(107, \"i\", 49);\n          i0.ɵɵtext(108, \"\\u65B0\\u589E\\u5171\\u7528\\u9700\\u6C42 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"span\", 48);\n          i0.ɵɵelement(110, \"i\", 50);\n          i0.ɵɵtext(111, \"\\u5EFA\\u7ACB\\u6A21\\u677F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"span\", 51);\n          i0.ɵɵelement(113, \"i\", 52);\n          i0.ɵɵtext(114, \"\\u8DE8\\u5EFA\\u6848\\u5171\\u7528 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(115, \"div\", 41)(116, \"div\", 53)(117, \"div\", 54)(118, \"div\", 55)(119, \"div\", 56)(120, \"button\", 57);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_120_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const templateCreatorDialog_r11 = i0.ɵɵreference(168);\n            return i0.ɵɵresetView(ctx.openTemplateCreator(templateCreatorDialog_r11));\n          });\n          i0.ɵɵelement(121, \"i\", 49);\n          i0.ɵɵtext(122, \"\\u65B0\\u589E\\u6A21\\u677F \");\n          i0.ɵɵtemplate(123, RequirementManagementComponent_span_123_Template, 2, 1, \"span\", 58);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"button\", 59);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_124_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const templateViewerDialog_r12 = i0.ɵɵreference(166);\n            return i0.ɵɵresetView(ctx.openTemplateViewer(templateViewerDialog_r12));\n          });\n          i0.ɵɵelement(125, \"i\", 60);\n          i0.ɵɵtext(126, \"\\u67E5\\u770B\\u6A21\\u677F \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(127, \"div\", 61);\n          i0.ɵɵtemplate(128, RequirementManagementComponent_small_128_Template, 2, 0, \"small\", 62)(129, RequirementManagementComponent_small_129_Template, 2, 1, \"small\", 63);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(130, \"div\", 55);\n          i0.ɵɵtemplate(131, RequirementManagementComponent_button_131_Template, 3, 0, \"button\", 64)(132, RequirementManagementComponent_button_132_Template, 3, 0, \"button\", 65);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(133, \"div\", 32)(134, \"div\", 33)(135, \"table\", 34)(136, \"thead\")(137, \"tr\", 35)(138, \"th\", 37)(139, \"input\", 66);\n          i0.ɵɵlistener(\"change\", function RequirementManagementComponent_Template_input_change_139_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleSelectAll($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(140, \"th\", 36);\n          i0.ɵɵtext(141, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(142, \"th\", 37);\n          i0.ɵɵtext(143, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(144, \"th\", 37);\n          i0.ɵɵtext(145, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(146, \"th\", 37);\n          i0.ɵɵtext(147, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(148, \"th\", 37);\n          i0.ɵɵtext(149, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(150, \"th\", 37);\n          i0.ɵɵtext(151, \"\\u9810\\u7D04\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"th\", 37);\n          i0.ɵɵtext(153, \"\\u7C21\\u6613\\u5BA2\\u8B8A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(154, \"th\", 37);\n          i0.ɵɵtext(155, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(156, \"th\", 37);\n          i0.ɵɵtext(157, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(158, \"tbody\");\n          i0.ɵɵtemplate(159, RequirementManagementComponent_tr_159_Template, 24, 18, \"tr\", 67);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(160, \"ngx-pagination\", 39);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_160_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_160_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(161, RequirementManagementComponent_ng_template_161_Template, 42, 48, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(163, RequirementManagementComponent_ng_template_163_Template, 36, 41, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(165, RequirementManagementComponent_ng_template_165_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(167, RequirementManagementComponent_ng_template_167_Template, 1, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentTab === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CGroupName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsShow);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsSimple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(31);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"disabled\", ctx.getSelectedRequirements().length === 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length > 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.requirementList.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getSelectedRequirements().length > 0);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"checked\", ctx.isAllSelected())(\"indeterminate\", ctx.isIndeterminate());\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.CheckboxControlValueAccessor, i9.NgControlStatus, i9.MaxLengthValidator, i9.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent, NumberWithCommasPipe, NbTabsetModule, i3.NbTabsetComponent, i3.NbTabComponent, TemplateViewerComponent, TemplateCreatorComponent],\n      styles: [\".table-active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd !important;\\n  border-left: 3px solid #2196f3;\\n}\\n\\n.page-description-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1.25rem;\\n  border-radius: 10px;\\n  border: 1px solid #dee2e6;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n  margin-bottom: 1rem;\\n}\\n.page-description-card[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #007bff;\\n}\\n.page-description-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  line-height: 1.5;\\n  margin-bottom: 0.75rem;\\n}\\n.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  color: #6c757d;\\n  border: 1px solid #dee2e6;\\n  font-size: 0.8rem;\\n  padding: 0.4rem 0.8rem;\\n  font-weight: 500;\\n}\\n.page-description-card[_ngcontent-%COMP%]   .feature-highlights[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.template-creation-controls[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n  border: 1px solid #e9ecef;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .template-action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .template-status-info[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  border: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:disabled {\\n  background: #6c757d;\\n  cursor: not-allowed;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  font-size: 0.75rem;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #6c757d;\\n  color: #6c757d;\\n  transition: all 0.2s ease;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-style: italic;\\n}\\n.template-creation-controls[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n  margin: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n  margin: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-active[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_selectRow 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_selectRow {\\n  0% {\\n    background-color: transparent;\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.01);\\n  }\\n  100% {\\n    background-color: #e3f2fd;\\n    transform: scale(1);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .template-creation-controls[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 0.25rem;\\n  }\\n  .template-creation-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.375rem 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "NbTabsetModule", "BaseComponent", "takeUntilDestroyed", "GetRequirement", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "TemplateViewerComponent", "TemplateCreatorComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r4", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵtwoWayListener", "RequirementManagementComponent_div_6_Template_nb_select_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "getListRequirementRequest", "CBuildCaseID", "ɵɵresetView", "ɵɵtemplate", "RequirementManagementComponent_div_6_nb_option_4_Template", "ɵɵtwoWayProperty", "buildCaseList", "type_r5", "value", "label", "ɵɵlistener", "RequirementManagementComponent_button_62_Template_button_click_0_listener", "_r6", "dialog_r7", "ɵɵreference", "add", "ɵɵelement", "RequirementManagementComponent_tr_93_button_22_Template_button_click_0_listener", "_r8", "data_r9", "$implicit", "onEdit", "RequirementManagementComponent_tr_93_button_23_Template_button_click_0_listener", "_r10", "onDelete", "RequirementManagementComponent_tr_93_button_22_Template", "RequirementManagementComponent_tr_93_button_23_Template", "ɵɵtextInterpolate", "CRequirement", "CGroupName", "getHouseType", "CHouseType", "ɵɵpureFunction0", "_c0", "CSort", "ɵɵpipeBind1", "CStatus", "getCIsShowText", "getCIsSimpleText", "CUnitPrice", "isUpdate", "isDelete", "getSelectedRequirements", "length", "RequirementManagementComponent_button_131_Template_button_click_0_listener", "_r13", "selectAllRequirements", "RequirementManagementComponent_button_132_Template_button_click_0_listener", "_r14", "clearAllSelections", "RequirementManagementComponent_tr_159_button_22_Template_button_click_0_listener", "_r17", "data_r16", "RequirementManagementComponent_tr_159_button_23_Template_button_click_0_listener", "_r18", "RequirementManagementComponent_tr_159_Template_input_ngModelChange_2_listener", "_r15", "selected", "RequirementManagementComponent_tr_159_Template_input_change_2_listener", "onRequirementSelectionChange", "RequirementManagementComponent_tr_159_button_22_Template", "RequirementManagementComponent_tr_159_button_23_Template", "ɵɵclassProp", "b_r21", "RequirementManagementComponent_ng_template_161_app_form_group_10_Template_nb_select_selectedChange_1_listener", "_r20", "saveRequirement", "RequirementManagementComponent_ng_template_161_app_form_group_10_nb_option_2_Template", "type_r22", "status_r23", "RequirementManagementComponent_ng_template_161_span_2_Template", "RequirementManagementComponent_ng_template_161_span_3_Template", "RequirementManagementComponent_ng_template_161_span_4_Template", "RequirementManagementComponent_ng_template_161_span_5_Template", "RequirementManagementComponent_ng_template_161_app_form_group_10_Template", "RequirementManagementComponent_ng_template_161_Template_input_ngModelChange_12_listener", "_r19", "RequirementManagementComponent_ng_template_161_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_ng_template_161_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_ng_template_161_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_161_nb_option_19_Template", "RequirementManagementComponent_ng_template_161_Template_nb_select_selectedChange_21_listener", "RequirementManagementComponent_ng_template_161_nb_option_22_Template", "RequirementManagementComponent_ng_template_161_Template_input_ngModelChange_24_listener", "RequirementManagementComponent_ng_template_161_Template_input_ngModelChange_26_listener", "CUnit", "RequirementManagementComponent_ng_template_161_Template_nb_checkbox_ngModelChange_28_listener", "CIsShow", "RequirementManagementComponent_ng_template_161_Template_nb_checkbox_ngModelChange_31_listener", "CIsSimple", "RequirementManagementComponent_ng_template_161_Template_textarea_ngModelChange_34_listener", "CRemark", "RequirementManagementComponent_ng_template_161_Template_button_click_38_listener", "ref_r24", "dialogRef", "save", "RequirementManagementComponent_ng_template_161_Template_button_click_40_listener", "close", "isNew", "currentTab", "houseType", "statusOptions", "type_r26", "status_r27", "RequirementManagementComponent_ng_template_163_span_2_Template", "RequirementManagementComponent_ng_template_163_span_3_Template", "RequirementManagementComponent_ng_template_163_Template_input_ngModelChange_9_listener", "_r25", "RequirementManagementComponent_ng_template_163_Template_input_ngModelChange_11_listener", "RequirementManagementComponent_ng_template_163_Template_input_ngModelChange_13_listener", "RequirementManagementComponent_ng_template_163_Template_nb_select_selectedChange_15_listener", "RequirementManagementComponent_ng_template_163_nb_option_16_Template", "RequirementManagementComponent_ng_template_163_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_163_nb_option_19_Template", "RequirementManagementComponent_ng_template_163_Template_input_ngModelChange_21_listener", "RequirementManagementComponent_ng_template_163_Template_input_ngModelChange_23_listener", "RequirementManagementComponent_ng_template_163_Template_nb_checkbox_ngModelChange_25_listener", "RequirementManagementComponent_ng_template_163_Template_textarea_ngModelChange_28_listener", "RequirementManagementComponent_ng_template_163_Template_button_click_32_listener", "ref_r28", "saveTemplate", "RequirementManagementComponent_ng_template_163_Template_button_click_34_listener", "RequirementManagementComponent_ng_template_165_Template_app_template_viewer_selectTemplate_0_listener", "_r29", "onSelectTemplate", "RequirementManagementComponent_ng_template_165_Template_app_template_viewer_close_0_listener", "ref_r30", "RequirementManagementComponent_ng_template_167_Template_app_template_creator_templateCreated_0_listener", "_r31", "onTemplateCreated", "RequirementManagementComponent_ng_template_167_Template_app_template_creator_close_0_listener", "ref_r32", "selectedRequirementsForTemplate", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getRequirementRequest", "requirementList", "getEnumOptions", "currentBuildCase", "isCreatingTemplate", "isFirstTabChange", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "map", "type", "resetSearch", "setTimeout", "getList", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "errorMessages", "dialog", "open", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "item", "TotalItems", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "filter", "v", "onTabChange", "event", "tabTitle", "addTemplate", "onEditTemplate", "_this2", "templateData", "openTemplateViewer", "templateViewerDialog", "tpl", "req", "isAllSelected", "every", "isIndeterminate", "selectedCount", "toggleSelectAll", "isChecked", "target", "openTemplateCreator", "templateCreatorDialog", "selectedRequirements", "onClose", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "BuildCaseService", "RequirementService", "i7", "PetternHelper", "i8", "Router", "DestroyRef", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequirementManagementComponent_Template", "rf", "ctx", "RequirementManagementComponent_div_6_Template", "RequirementManagementComponent_Template_input_ngModelChange_10_listener", "_r1", "RequirementManagementComponent_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_19_listener", "RequirementManagementComponent_nb_option_20_Template", "RequirementManagementComponent_Template_nb_select_ngModelChange_24_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_34_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_45_listener", "RequirementManagementComponent_Template_button_click_56_listener", "RequirementManagementComponent_Template_button_click_59_listener", "RequirementManagementComponent_button_62_Template", "RequirementManagementComponent_Template_nb_tabset_changeTab_64_listener", "RequirementManagementComponent_tr_93_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_94_listener", "RequirementManagementComponent_Template_button_click_120_listener", "templateCreatorDialog_r11", "RequirementManagementComponent_span_123_Template", "RequirementManagementComponent_Template_button_click_124_listener", "templateViewerDialog_r12", "RequirementManagementComponent_small_128_Template", "RequirementManagementComponent_small_129_Template", "RequirementManagementComponent_button_131_Template", "RequirementManagementComponent_button_132_Template", "RequirementManagementComponent_Template_input_change_139_listener", "RequirementManagementComponent_tr_159_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_160_listener", "RequirementManagementComponent_ng_template_161_Template", "ɵɵtemplateRefExtractor", "RequirementManagementComponent_ng_template_163_Template", "RequirementManagementComponent_ng_template_165_Template", "RequirementManagementComponent_ng_template_167_Template", "isCreate", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i9", "DefaultValueAccessor", "NumberValueAccessor", "CheckboxControlValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "NbSelectComponent", "NbOptionComponent", "NbCheckboxComponent", "NbTabsetComponent", "NbTabComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { Template, TemplateDetail, TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\r\nimport { TemplateCreatorComponent } from 'src/app/shared/components/template-creator/template-creator.component';\r\n\r\n// 擴展 GetRequirement 接口以支持選中狀態\r\ninterface SelectableRequirement extends GetRequirement {\r\n  selected?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    NbTabsetModule,\r\n    TemplateViewerComponent,\r\n    TemplateCreatorComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: SelectableRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n  currentTab = 0; // 追蹤當前選中的 tab\r\n  isCreatingTemplate = false; // 控制是否正在創建模板\r\n  selectedRequirementsForTemplate: SelectableRequirement[] = []; // 用於模板創建的選中項目\r\n\r\n\r\n\r\n  override ngOnInit(): void { }\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CIsSimple = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CGroupName = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        if (this.currentTab === 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        } else {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n        }\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    // 根據當前 tab 決定是否需要驗證建案名稱\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面需要驗證建案名稱\r\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    }\r\n\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[排序]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 根據當前 tab 決定是否需要建案ID\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n      if (this.currentBuildCase != 0) {\r\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    } else {\r\n      // 模板頁面 - 設定建案ID為0，CHouseType預設[1,2]\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n    }\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: SelectableRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      // 編輯時如果是共用tab，CHouseType強制為[1,2]\r\n      if (this.currentTab === 1) {\r\n        this.saveRequirement.CHouseType = [1, 2];\r\n      }\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: SelectableRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 只在建案 tab 下且有建案時才查詢\r\n        if (this.currentTab === 0 && this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        } else if (this.currentTab === 1) {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動，CHouseType預設為[1,2]\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n      this.getListRequirementRequest.CHouseType = [1, 2];\r\n    } else {\r\n      // 建案頁面的邏輯保持不變\r\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n      }\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            // 將 API 返回的數據轉換為 SelectableRequirement 並初始化 selected 屬性\r\n            this.requirementList = res.Entries.map(item => ({\r\n              ...item,\r\n              selected: false\r\n            }));\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  } getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            // 共用tab時CHouseType強制為[1,2]\r\n            this.saveRequirement.CHouseType = this.currentTab === 1 ? [1, 2] : (res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : []);\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            // TODO: 等後端API更新後啟用這行\r\n            this.saveRequirement.CIsShow = (res.Entries as any).CIsShow || false;\r\n            this.saveRequirement.CIsSimple = (res.Entries as any).CIsSimple || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  getCIsSimpleText(data: any): string {\r\n    return data.CIsSimple ? '是' : '否';\r\n  }\r\n\r\n  // Tab 切換事件處理\r\n  private isFirstTabChange = true;\r\n  onTabChange(event: any) {\r\n    // 避免頁面初始化時自動觸發重複查詢\r\n    if (this.isFirstTabChange) {\r\n      this.isFirstTabChange = false;\r\n      return;\r\n    }\r\n    // 根據 tabTitle 來判斷當前頁面\r\n    if (event.tabTitle === '共用') {\r\n      this.currentTab = 1;\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      this.currentTab = 0;\r\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\r\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    }\r\n    this.getList();\r\n  }\r\n\r\n  // 新增模板\r\n  addTemplate(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    // 模板設定建案ID為0，CHouseType預設[1,2]\r\n    this.saveRequirement.CBuildCaseID = 0;\r\n    this.saveRequirement.CHouseType = [1, 2];\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 編輯模板\r\n  async onEditTemplate(data: SelectableRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      // 編輯模板時CHouseType強制為[1,2]\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get template data\", error);\r\n    }\r\n  }\r\n\r\n  // 保存模板\r\n  saveTemplate(ref: any) {\r\n    // 模板驗證（不包含建案名稱）\r\n    this.valid.clear();\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    // CHouseType強制為[1,2]\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CHouseType = [1, 2];\r\n    }\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 確保模板建案ID為0\r\n    const templateData = { ...this.saveRequirement };\r\n    templateData.CBuildCaseID = 0;\r\n    // CHouseType強制為[1,2]\r\n    if (this.currentTab === 1) {\r\n      templateData.CHouseType = [1, 2];\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: templateData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  openTemplateViewer(templateViewerDialog: TemplateRef<any>) {\r\n    this.dialogService.open(templateViewerDialog);\r\n  }\r\n\r\n  onSelectTemplate(tpl: Template) {\r\n    // 查看模板邏輯\r\n  }\r\n\r\n  // 獲取選中的需求項目\r\n  getSelectedRequirements(): SelectableRequirement[] {\r\n    return this.requirementList.filter(req => req.selected);\r\n  }\r\n\r\n  // 選中狀態變更處理\r\n  onRequirementSelectionChange() {\r\n    // 可以在這裡添加額外的邏輯，比如更新選中計數等\r\n  }\r\n\r\n  // 全選功能\r\n  selectAllRequirements() {\r\n    this.requirementList.forEach(req => req.selected = true);\r\n  }\r\n\r\n  // 清除所有選擇\r\n  clearAllSelections() {\r\n    this.requirementList.forEach(req => req.selected = false);\r\n  }\r\n\r\n  // 檢查是否全選\r\n  isAllSelected(): boolean {\r\n    return this.requirementList.length > 0 && this.requirementList.every(req => req.selected);\r\n  }\r\n\r\n  // 檢查是否部分選中（用於 indeterminate 狀態）\r\n  isIndeterminate(): boolean {\r\n    const selectedCount = this.requirementList.filter(req => req.selected).length;\r\n    return selectedCount > 0 && selectedCount < this.requirementList.length;\r\n  }\r\n\r\n  // 切換全選狀態\r\n  toggleSelectAll(event: any) {\r\n    const isChecked = event.target.checked;\r\n    this.requirementList.forEach(req => req.selected = isChecked);\r\n  }\r\n\r\n  // 打開模板創建器\r\n  openTemplateCreator(templateCreatorDialog: TemplateRef<any>) {\r\n    const selectedRequirements = this.getSelectedRequirements();\r\n    if (selectedRequirements.length === 0) {\r\n      this.message.showErrorMSG('請先選擇要加入模板的項目');\r\n      return;\r\n    }\r\n\r\n    // 將選中的項目存儲在屬性中，確保在模板中保持引用一致性\r\n    this.selectedRequirementsForTemplate = [...selectedRequirements];\r\n    this.isCreatingTemplate = true;\r\n    const dialogRef = this.dialogService.open(templateCreatorDialog);\r\n\r\n    // 當對話框關閉時重置狀態\r\n    dialogRef.onClose.subscribe(() => {\r\n      this.isCreatingTemplate = false;\r\n      this.selectedRequirementsForTemplate = [];\r\n    });\r\n  }\r\n\r\n  // 模板創建成功回調\r\n  onTemplateCreated() {\r\n    this.message.showSucessMSG('模板創建成功');\r\n    // 清除選中狀態\r\n    this.clearAllSelections();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n\r\n  <!-- 共用搜尋區域 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\" *ngIf=\"currentTab === 0\">\r\n          <label for=\"buildCase\" class=\"label mr-2\">建案</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CBuildCaseID\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"requirement\" class=\"label mr-2\">工程項目</label>\r\n          <input type=\"text\" nbInput id=\"requirement\" name=\"requirement\" placeholder=\"工程項目\"\r\n            [(ngModel)]=\"getListRequirementRequest.CRequirement\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"groupName\" class=\"label mr-2\">群組類別</label>\r\n          <input type=\"text\" nbInput id=\"groupName\" name=\"groupName\" placeholder=\"群組類別\"\r\n            [(ngModel)]=\"getListRequirementRequest.CGroupName\">\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"houseType\" class=\"label mr-2\">類型</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CHouseType\" class=\"col-9\" multiple>\r\n            <nb-option *ngFor=\"let type of houseType\" [value]=\"type.value\">\r\n              {{ type.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CStatus\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isShow\" class=\"label mr-2\">預約需求</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsShow\" class=\"col-9\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isSimple\" class=\"label mr-2\">簡易客變</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsSimple\" class=\"col-9\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\"></div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\"></div>\r\n        <div class=\"form-group col-12 col-md-6 text-right\">\r\n          <button class=\"btn btn-secondary mr-2\" (click)=\"resetSearch()\"><i class=\"fas fa-undo mr-1\"></i>重置</button>\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增</button>\r\n\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <!-- Tab 導航 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <nb-tabset (changeTab)=\"onTabChange($event)\">\r\n      <nb-tab tabTitle=\"單建案\">\r\n        <div class=\"pt-3\">\r\n          <!-- 建案列表 -->\r\n          <div class=\"col-12 mt-3\">\r\n            <div class=\"table-responsive\">\r\n              <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n                <thead>\r\n                  <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n                    <th scope=\"col\" class=\"col-2\">建案名稱</th>\r\n                    <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n                    <th scope=\"col\" class=\"col-1\">群組類別</th>\r\n                    <th scope=\"col\" class=\"col-1\">類型</th>\r\n                    <th scope=\"col\" class=\"col-1\">排序</th>\r\n                    <th scope=\"col\" class=\"col-1\">狀態</th>\r\n                    <th scope=\"col\" class=\"col-1\">預約需求</th>\r\n                    <th scope=\"col\" class=\"col-1\">簡易客變</th>\r\n                    <th scope=\"col\" class=\"col-1\">單價</th>\r\n                    <th scope=\"col\" class=\"col-1\">操作功能</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n                    <td class=\"col-2\">{{ data.CBuildCaseName }}</td>\r\n                    <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n                    <td class=\"col-1\">{{ data.CGroupName }}</td>\r\n                    <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n                    <td class=\"col-1\">{{ data.CSort }}</td>\r\n                    <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n                    <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n                    <td class=\"col-1\">{{ getCIsSimpleText(data) }}</td>\r\n                    <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n                    <td class=\"col-1\">\r\n                      <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                        (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                      <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                        (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n            <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n              (PageChange)=\"getList()\">\r\n            </ngx-pagination>\r\n          </div>\r\n        </div>\r\n      </nb-tab>\r\n\r\n      <nb-tab tabTitle=\"跨建案\">\r\n        <div class=\"pt-3\">\r\n          <!-- 頁面說明區域 -->\r\n          <div class=\"col-12 mb-3\">\r\n            <div class=\"page-description-card\">\r\n              <div class=\"d-flex align-items-center mb-2\">\r\n                <i class=\"fas fa-info-circle text-primary mr-2\"></i>\r\n                <h6 class=\"mb-0 text-primary\">共用需求管理</h6>\r\n              </div>\r\n              <p class=\"mb-2 text-muted\">\r\n                此頁面用於管理共用的客變需求項目，這些項目可以被多個建案重複使用。\r\n                您可以在此新增、編輯或刪除共用需求，也可以將選中的需求項目建立為模板。\r\n              </p>\r\n              <div class=\"feature-highlights\">\r\n                <span class=\"badge badge-light mr-2\">\r\n                  <i class=\"fas fa-plus mr-1\"></i>新增共用需求\r\n                </span>\r\n                <span class=\"badge badge-light mr-2\">\r\n                  <i class=\"fas fa-layer-group mr-1\"></i>建立模板\r\n                </span>\r\n                <span class=\"badge badge-light\">\r\n                  <i class=\"fas fa-share-alt mr-1\"></i>跨建案共用\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 模板管理按鈕區域 -->\r\n          <div class=\"col-12 mb-3\">\r\n            <div class=\"template-creation-controls\">\r\n              <div class=\"d-flex justify-content-between align-items-center\">\r\n                <div class=\"d-flex align-items-center\">\r\n                  <div class=\"template-action-buttons mr-3\">\r\n                    <button class=\"btn btn-primary mr-2\" (click)=\"openTemplateCreator(templateCreatorDialog)\"\r\n                      [disabled]=\"getSelectedRequirements().length === 0\">\r\n                      <i class=\"fas fa-plus mr-1\"></i>新增模板\r\n                      <span *ngIf=\"getSelectedRequirements().length > 0\" class=\"badge badge-light ml-1\">\r\n                        {{ getSelectedRequirements().length }}\r\n                      </span>\r\n                    </button>\r\n                    <button class=\"btn btn-primary\" (click)=\"openTemplateViewer(templateViewerDialog)\">\r\n                      <i class=\"fas fa-eye mr-1\"></i>查看模板\r\n                    </button>\r\n\r\n                  </div>\r\n                  <div class=\"template-status-info\">\r\n                    <small class=\"text-muted\" *ngIf=\"getSelectedRequirements().length === 0\">\r\n                      請先勾選要加入模板的項目\r\n                    </small>\r\n                    <small class=\"text-success\" *ngIf=\"getSelectedRequirements().length > 0\">\r\n                      已選擇 {{ getSelectedRequirements().length }} 個項目\r\n                    </small>\r\n                  </div>\r\n                </div>\r\n                <div class=\"d-flex align-items-center\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm mr-2\" (click)=\"selectAllRequirements()\"\r\n                    *ngIf=\"requirementList.length > 0\">\r\n                    <i class=\"fas fa-check-square mr-1\"></i>全選\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"clearAllSelections()\"\r\n                    *ngIf=\"getSelectedRequirements().length > 0\">\r\n                    <i class=\"fas fa-times mr-1\"></i>清除選擇\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 模板列表 -->\r\n            <div class=\"col-12 mt-3\">\r\n              <div class=\"table-responsive\">\r\n                <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n                  <thead>\r\n                    <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n                      <th scope=\"col\" class=\"col-1\">\r\n                        <input type=\"checkbox\" class=\"form-check-input\" [checked]=\"isAllSelected()\"\r\n                          [indeterminate]=\"isIndeterminate()\" (change)=\"toggleSelectAll($event)\">\r\n                      </th>\r\n                      <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n                      <th scope=\"col\" class=\"col-1\">群組類別</th>\r\n                      <th scope=\"col\" class=\"col-1\">類型</th>\r\n                      <th scope=\"col\" class=\"col-1\">排序</th>\r\n                      <th scope=\"col\" class=\"col-1\">狀態</th>\r\n                      <th scope=\"col\" class=\"col-1\">預約需求</th>\r\n                      <th scope=\"col\" class=\"col-1\">簡易客變</th>\r\n                      <th scope=\"col\" class=\"col-1\">單價</th>\r\n                      <th scope=\"col\" class=\"col-1\">操作功能</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\"\r\n                      [class.table-active]=\"data.selected\">\r\n                      <td class=\"col-1\">\r\n                        <input type=\"checkbox\" class=\"form-check-input\" [(ngModel)]=\"data.selected\"\r\n                          (change)=\"onRequirementSelectionChange()\">\r\n                      </td>\r\n                      <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n                      <td class=\"col-1\">{{ data.CGroupName }}</td>\r\n                      <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n                      <td class=\"col-1\">{{ data.CSort }}</td>\r\n                      <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n                      <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n                      <td class=\"col-1\">{{ getCIsSimpleText(data) }}</td>\r\n                      <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n                      <td class=\"col-1\">\r\n                        <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                          (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                        <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                          (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n                      </td>\r\n                    </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n              <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n                (PageChange)=\"getList()\">\r\n              </ngx-pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </nb-tab>\r\n    </nb-tabset>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 建案對話框 -->\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true && currentTab === 0\">新增建案需求</span>\r\n      <span *ngIf=\"isNew===false && currentTab === 0\">編輯建案需求</span>\r\n      <span *ngIf=\"isNew===true && currentTab === 1\">新增模板需求</span>\r\n      <span *ngIf=\"isNew===false && currentTab === 1\">編輯模板需求</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'建案名稱'\" [labelFor]=\"'CBuildCaseID'\" [isRequired]=\"true\" *ngIf=\"currentTab === 0\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CBuildCaseID\" name=\"CBuildCaseID\"\r\n                [(selected)]=\"saveRequirement.CBuildCaseID\">\r\n                <nb-option langg *ngFor=\"let b of buildCaseList\" [value]=\"b.cID\"> {{b.CBuildCaseName}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'群組類別'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組類別\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple [disabled]=\"currentTab === 1\">\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'預約需求'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"true\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在預約需求清單\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'簡易客變'\" [labelFor]=\"'CIsSimple'\" [isRequired]=\"true\">\r\n              <nb-checkbox id=\"CIsSimple\" name=\"CIsSimple\" [(ngModel)]=\"saveRequirement.CIsSimple\" class=\"flex-grow-1\">\r\n                設定為簡易客變\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 模板對話框 -->\r\n<ng-template #templateDialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增</span>\r\n      <span *ngIf=\"isNew===false\">編輯</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'群組類別'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組類別\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple [disabled]=\"currentTab === 1\">\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'預約需求'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"true\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在預約需求清單\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"saveTemplate(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n\r\n\r\n<!-- 共用模板查看元件 Dialog -->\r\n<ng-template #templateViewerDialog let-data let-ref=\"dialogRef\">\r\n  <app-template-viewer [templateType]=\"1\" (selectTemplate)=\"onSelectTemplate($event)\" (close)=\"ref.close()\">\r\n  </app-template-viewer>\r\n</ng-template>\r\n\r\n<!-- 模板創建元件 Dialog -->\r\n<ng-template #templateCreatorDialog let-data let-ref=\"dialogRef\">\r\n  <app-template-creator [availableData]=\"selectedRequirementsForTemplate\" [templateType]=\"1\"\r\n    (templateCreated)=\"onTemplateCreated()\" (close)=\"ref.close()\">\r\n  </app-template-creator>\r\n</ng-template>"], "mappings": ";AAEA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/I,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAA6DC,cAAc,QAAwD,yBAAyB;AAC5J,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAAmCC,uBAAuB,QAAQ,qEAAqE;AACvI,SAASC,wBAAwB,QAAQ,uEAAuE;;;;;;;;;;;;;;ICTpGC,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;;IAJFT,EADF,CAAAC,cAAA,aAAiE,gBACrB;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpDH,EAAA,CAAAC,cAAA,oBAA8E;IAAnED,EAAA,CAAAU,gBAAA,2BAAAC,iFAAAC,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAG,yBAAA,CAAAC,YAAA,EAAAP,MAAA,MAAAG,MAAA,CAAAG,yBAAA,CAAAC,YAAA,GAAAP,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAoD;IAC7DZ,EAAA,CAAAqB,UAAA,IAAAC,yDAAA,wBAAiE;IAIrEtB,EADE,CAAAG,YAAA,EAAY,EACR;;;;IALOH,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAG,yBAAA,CAAAC,YAAA,CAAoD;IACjCnB,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAS,aAAA,CAAgB;;;;;IAoB5CxB,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAqB,OAAA,CAAAC,KAAA,CAAoB;IAC5D1B,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAiB,OAAA,CAAAE,KAAA,MACF;;;;;;IAoCF3B,EAAA,CAAAC,cAAA,iBAA4E;IAAvCD,EAAA,CAAA4B,UAAA,mBAAAC,0EAAA;MAAA7B,EAAA,CAAAa,aAAA,CAAAiB,GAAA;MAAA,MAAAf,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAe,SAAA,GAAA/B,EAAA,CAAAgC,WAAA;MAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAkB,GAAA,CAAAF,SAAA,CAAW;IAAA,EAAC;IAAkB/B,EAAA,CAAAkC,SAAA,YAC3C;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA0ChCH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAA4B,UAAA,mBAAAO,gFAAA;MAAAnC,EAAA,CAAAa,aAAA,CAAAuB,GAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAgB,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAe,SAAA,GAAA/B,EAAA,CAAAgC,WAAA;MAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAwB,MAAA,CAAAF,OAAA,EAAAN,SAAA,CAAmB;IAAA,EAAC;IAAC/B,EAAA,CAAAkC,SAAA,YAAgC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAA4B,UAAA,mBAAAY,gFAAA;MAAAxC,EAAA,CAAAa,aAAA,CAAA4B,IAAA;MAAA,MAAAJ,OAAA,GAAArC,EAAA,CAAAgB,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA2B,QAAA,CAAAL,OAAA,CAAc;IAAA,EAAC;IAACrC,EAAA,CAAAkC,SAAA,YAAqC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAb7EH,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAqB,UAAA,KAAAsB,uDAAA,qBACgC,KAAAC,uDAAA,qBAEL;IAE/B5C,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAfeH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAA6C,iBAAA,CAAAR,OAAA,CAAA5B,cAAA,CAAyB;IACzBT,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAA6C,iBAAA,CAAAR,OAAA,CAAAS,YAAA,CAAuB;IACvB9C,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAA6C,iBAAA,CAAAR,OAAA,CAAAU,UAAA,CAAqB;IACrB/C,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAA6C,iBAAA,CAAA9B,MAAA,CAAAiC,YAAA,CAAAX,OAAA,CAAAY,UAAA,IAAAjD,EAAA,CAAAkD,eAAA,KAAAC,GAAA,GAAyC;IACzCnD,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAA6C,iBAAA,CAAAR,OAAA,CAAAe,KAAA,CAAgB;IAChBpD,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,SAAAhB,OAAA,CAAAiB,OAAA,EAAkC;IAClCtD,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAA6C,iBAAA,CAAA9B,MAAA,CAAAwC,cAAA,CAAAlB,OAAA,EAA0B;IAC1BrC,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAA6C,iBAAA,CAAA9B,MAAA,CAAAyC,gBAAA,CAAAnB,OAAA,EAA4B;IAC5BrC,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,SAAAhB,OAAA,CAAAoB,UAAA,OAAkD;IAEzDzD,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA2C,QAAA,CAAc;IAEd1D,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA4C,QAAA,CAAc;;;;;IAkDvB3D,EAAA,CAAAC,cAAA,eAAkF;IAChFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAO,MAAA,CAAA6C,uBAAA,GAAAC,MAAA,MACF;;;;;IAQF7D,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,iFACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IADNH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,yBAAAO,MAAA,CAAA6C,uBAAA,GAAAC,MAAA,yBACF;;;;;;IAIF7D,EAAA,CAAAC,cAAA,iBACqC;IADiBD,EAAA,CAAA4B,UAAA,mBAAAkC,2EAAA;MAAA9D,EAAA,CAAAa,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAiD,qBAAA,EAAuB;IAAA,EAAC;IAErFhE,EAAA,CAAAkC,SAAA,YAAwC;IAAAlC,EAAA,CAAAE,MAAA,oBAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAC+C;IADED,EAAA,CAAA4B,UAAA,mBAAAqC,2EAAA;MAAAjE,EAAA,CAAAa,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAoD,kBAAA,EAAoB;IAAA,EAAC;IAE7EnE,EAAA,CAAAkC,SAAA,YAAiC;IAAAlC,EAAA,CAAAE,MAAA,gCACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA0CHH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAA4B,UAAA,mBAAAwC,iFAAA;MAAApE,EAAA,CAAAa,aAAA,CAAAwD,IAAA;MAAA,MAAAC,QAAA,GAAAtE,EAAA,CAAAgB,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAe,SAAA,GAAA/B,EAAA,CAAAgC,WAAA;MAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAwB,MAAA,CAAA+B,QAAA,EAAAvC,SAAA,CAAmB;IAAA,EAAC;IAAC/B,EAAA,CAAAkC,SAAA,YAAgC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAA4B,UAAA,mBAAA2C,iFAAA;MAAAvE,EAAA,CAAAa,aAAA,CAAA2D,IAAA;MAAA,MAAAF,QAAA,GAAAtE,EAAA,CAAAgB,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA2B,QAAA,CAAA4B,QAAA,CAAc;IAAA,EAAC;IAACtE,EAAA,CAAAkC,SAAA,YAAqC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAf3EH,EAHJ,CAAAC,cAAA,aACuC,aACnB,gBAE4B;IADID,EAAA,CAAAU,gBAAA,2BAAA+D,8EAAA7D,MAAA;MAAA,MAAA0D,QAAA,GAAAtE,EAAA,CAAAa,aAAA,CAAA6D,IAAA,EAAApC,SAAA;MAAAtC,EAAA,CAAAiB,kBAAA,CAAAqD,QAAA,CAAAK,QAAA,EAAA/D,MAAA,MAAA0D,QAAA,CAAAK,QAAA,GAAA/D,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAA2B;IACzEZ,EAAA,CAAA4B,UAAA,oBAAAgD,uEAAA;MAAA5E,EAAA,CAAAa,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAAUL,MAAA,CAAA8D,4BAAA,EAA8B;IAAA,EAAC;IAC7C7E,EAFE,CAAAG,YAAA,EAC4C,EACzC;IACLH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAqB,UAAA,KAAAyD,wDAAA,qBACgC,KAAAC,wDAAA,qBAEL;IAE/B/E,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAnBHH,EAAA,CAAAgF,WAAA,iBAAAV,QAAA,CAAAK,QAAA,CAAoC;IAEc3E,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAuB,gBAAA,YAAA+C,QAAA,CAAAK,QAAA,CAA2B;IAG3D3E,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAA6C,iBAAA,CAAAyB,QAAA,CAAAxB,YAAA,CAAuB;IACvB9C,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAA6C,iBAAA,CAAAyB,QAAA,CAAAvB,UAAA,CAAqB;IACrB/C,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAA6C,iBAAA,CAAA9B,MAAA,CAAAiC,YAAA,CAAAsB,QAAA,CAAArB,UAAA,IAAAjD,EAAA,CAAAkD,eAAA,KAAAC,GAAA,GAAyC;IACzCnD,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAA6C,iBAAA,CAAAyB,QAAA,CAAAlB,KAAA,CAAgB;IAChBpD,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,SAAAiB,QAAA,CAAAhB,OAAA,EAAkC;IAClCtD,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAA6C,iBAAA,CAAA9B,MAAA,CAAAwC,cAAA,CAAAe,QAAA,EAA0B;IAC1BtE,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAA6C,iBAAA,CAAA9B,MAAA,CAAAyC,gBAAA,CAAAc,QAAA,EAA4B;IAC5BtE,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,SAAAiB,QAAA,CAAAb,UAAA,OAAkD;IAEzDzD,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA2C,QAAA,CAAc;IAEd1D,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA4C,QAAA,CAAc;;;;;IAsBzC3D,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5DH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7DH,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5DH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IASnDH,EAAA,CAAAC,cAAA,qBAAiE;IAACD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAjDH,EAAA,CAAAI,UAAA,UAAA6E,KAAA,CAAA3E,GAAA,CAAe;IAAEN,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAQ,kBAAA,MAAAyE,KAAA,CAAAxE,cAAA,KAAoB;;;;;;IAFxFT,EADF,CAAAC,cAAA,yBAA0G,qBAE1D;IAA5CD,EAAA,CAAAU,gBAAA,4BAAAwE,8GAAAtE,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAjE,YAAA,EAAAP,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAjE,YAAA,GAAAP,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAA2C;IAC3CZ,EAAA,CAAAqB,UAAA,IAAAgE,qFAAA,wBAAiE;IAErErF,EADE,CAAAG,YAAA,EAAY,EACG;;;;IAL4CH,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAAqE,eAAA,CAAAjE,YAAA,CAA2C;IACZnB,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAS,aAAA,CAAgB;;;;;IAkB/CxB,EAAA,CAAAC,cAAA,qBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAAkF,QAAA,CAAA5D,KAAA,CAAoB;IAAE1B,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAA8E,QAAA,CAAA3D,KAAA,KAAc;;;;;IAMpF3B,EAAA,CAAAC,cAAA,qBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAAmF,UAAA,CAAA7D,KAAA,CAAsB;IAC1E1B,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAA+E,UAAA,CAAA5D,KAAA,KAAgB;;;;;;IAtC9B3B,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAIdD,EAHA,CAAAqB,UAAA,IAAAmE,8DAAA,mBAA+C,IAAAC,8DAAA,mBACC,IAAAC,8DAAA,mBACD,IAAAC,8DAAA,mBACC;IAClD3F,EAAA,CAAAG,YAAA,EAAiB;IAIXH,EAHN,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX;IACfD,EAAA,CAAAqB,UAAA,KAAAuE,yEAAA,6BAA0G;IAOxG5F,EADF,CAAAC,cAAA,0BAAiF,iBAEnB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAAmF,wFAAAjF,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAtC,YAAA,EAAAlC,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAtC,YAAA,GAAAlC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAA0C;IAC9CZ,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAgF,iBAEpB;IAAxDD,EAAA,CAAAU,gBAAA,2BAAAqF,wFAAAnF,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAArC,UAAA,EAAAnC,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAArC,UAAA,GAAAnC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAAsF,wFAAApF,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAhC,KAAA,EAAAxC,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAhC,KAAA,GAAAxC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEQ;IAAjFD,EAAA,CAAAU,gBAAA,4BAAAuF,6FAAArF,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAnC,UAAA,EAAArC,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAnC,UAAA,GAAArC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAyC;IACzCZ,EAAA,CAAAqB,UAAA,KAAA6E,oEAAA,wBAAqE;IAEzElG,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAU,gBAAA,4BAAAyF,6FAAAvF,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAA9B,OAAA,EAAA1C,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAA9B,OAAA,GAAA1C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAsC;IACtCZ,EAAA,CAAAqB,UAAA,KAAA+E,oEAAA,wBAA6E;IAGjFpG,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAEhC;IAAzCD,EAAA,CAAAU,gBAAA,2BAAA2F,wFAAAzF,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAA3B,UAAA,EAAA7C,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAA3B,UAAA,GAAA7C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,kBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAA4F,wFAAA1F,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAmB,KAAA,EAAA3F,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAmB,KAAA,GAAA3F,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA4E,wBACyB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAA8F,8FAAA5F,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAqB,OAAA,EAAA7F,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAqB,OAAA,GAAA7F,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAC5EZ,EAAA,CAAAE,MAAA,gEACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA8E,wBAC6B;IAA5DD,EAAA,CAAAU,gBAAA,2BAAAgG,8FAAA9F,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAuB,SAAA,EAAA/F,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAuB,SAAA,GAAA/F,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAuC;IAClFZ,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEV;IAA/DD,EAAA,CAAAU,gBAAA,2BAAAkG,2FAAAhG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAiF,IAAA;MAAA,MAAA/E,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAyB,OAAA,EAAAjG,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAyB,OAAA,GAAAjG,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAKjDZ,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,gBACiB,kBAC2B;IAApBD,EAAA,CAAA4B,UAAA,mBAAAkF,iFAAA;MAAA,MAAAC,OAAA,GAAA/G,EAAA,CAAAa,aAAA,CAAAiF,IAAA,EAAAkB,SAAA;MAAA,MAAAjG,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAkG,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAAC/G,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,mBAA0D;IAAtBD,EAAA,CAAA4B,UAAA,mBAAAsF,iFAAA;MAAA,MAAAH,OAAA,GAAA/G,EAAA,CAAAa,aAAA,CAAAiF,IAAA,EAAAkB,SAAA;MAAA,OAAAhH,EAAA,CAAAoB,WAAA,CAAS2F,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAACnH,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IA1ECH,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAqG,KAAA,aAAArG,MAAA,CAAAsG,UAAA,OAAsC;IACtCrH,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAqG,KAAA,cAAArG,MAAA,CAAAsG,UAAA,OAAuC;IACvCrH,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAqG,KAAA,aAAArG,MAAA,CAAAsG,UAAA,OAAsC;IACtCrH,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAqG,KAAA,cAAArG,MAAA,CAAAsG,UAAA,OAAuC;IAM0CrH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAsG,UAAA,OAAsB;IAMxFrH,EAAA,CAAAO,SAAA,EAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAAtC,YAAA,CAA0C;IAE9B9C,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE3EJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAArC,UAAA,CAAwC;IAE5B/C,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAAhC,KAAA,CAAmC;IAEvBpD,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAAqE,eAAA,CAAAnC,UAAA,CAAyC;IAAUjD,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAsG,UAAA,OAA6B;IAC9CrH,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAuG,SAAA,CAAY;IAGlCtH,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAAqE,eAAA,CAAA9B,OAAA,CAAsC;IACFtD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAwG,aAAA,CAAgB;IAIxCvH,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAA3B,UAAA,CAAwC;IAE5BzD,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAAmB,KAAA,CAAmC;IAEvBvG,EAAA,CAAAO,SAAA,EAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,oBAAoB;IAChCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAAqB,OAAA,CAAqC;IAIhEzG,EAAA,CAAAO,SAAA,GAAgB;IAA0BP,EAA1C,CAAAI,UAAA,qCAAgB,yBAAyB,oBAAoB;IAC9BJ,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAAuB,SAAA,CAAuC;IAItE3G,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAAyB,OAAA,CAAqC;;;;;IAqB/C7G,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACpCH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAqB3BH,EAAA,CAAAC,cAAA,qBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAAoH,QAAA,CAAA9F,KAAA,CAAoB;IAAE1B,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAAgH,QAAA,CAAA7F,KAAA,KAAc;;;;;IAMpF3B,EAAA,CAAAC,cAAA,qBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAAqH,UAAA,CAAA/F,KAAA,CAAsB;IAC1E1B,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAAiH,UAAA,CAAA9F,KAAA,KAAgB;;;;;;IA9B9B3B,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAAqB,UAAA,IAAAqG,8DAAA,mBAA2B,IAAAC,8DAAA,mBACC;IAC9B3H,EAAA,CAAAG,YAAA,EAAiB;IAMPH,EALV,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX,yBACkE,gBAEnB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAAkH,uFAAAhH,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAtC,YAAA,EAAAlC,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAtC,YAAA,GAAAlC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAA0C;IAC9CZ,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAgF,iBAEpB;IAAxDD,EAAA,CAAAU,gBAAA,2BAAAoH,wFAAAlH,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAArC,UAAA,EAAAnC,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAArC,UAAA,GAAAnC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAAqH,wFAAAnH,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAhC,KAAA,EAAAxC,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAhC,KAAA,GAAAxC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEQ;IAAjFD,EAAA,CAAAU,gBAAA,4BAAAsH,6FAAApH,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAnC,UAAA,EAAArC,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAnC,UAAA,GAAArC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAyC;IACzCZ,EAAA,CAAAqB,UAAA,KAAA4G,oEAAA,wBAAqE;IAEzEjI,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAU,gBAAA,4BAAAwH,6FAAAtH,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAA9B,OAAA,EAAA1C,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAA9B,OAAA,GAAA1C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAsC;IACtCZ,EAAA,CAAAqB,UAAA,KAAA8G,oEAAA,wBAA6E;IAGjFnI,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAEhC;IAAzCD,EAAA,CAAAU,gBAAA,2BAAA0H,wFAAAxH,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAA3B,UAAA,EAAA7C,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAA3B,UAAA,GAAA7C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,kBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAA2H,wFAAAzH,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAmB,KAAA,EAAA3F,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAmB,KAAA,GAAA3F,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA4E,wBACyB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAA4H,8FAAA1H,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAqB,OAAA,EAAA7F,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAqB,OAAA,GAAA7F,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAC5EZ,EAAA,CAAAE,MAAA,gEACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEV;IAA/DD,EAAA,CAAAU,gBAAA,2BAAA6H,2FAAA3H,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAgH,IAAA;MAAA,MAAA9G,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAqE,eAAA,CAAAyB,OAAA,EAAAjG,MAAA,MAAAG,MAAA,CAAAqE,eAAA,CAAAyB,OAAA,GAAAjG,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAKjDZ,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,gBACiB,kBACmC;IAA5BD,EAAA,CAAA4B,UAAA,mBAAA4G,iFAAA;MAAA,MAAAC,OAAA,GAAAzI,EAAA,CAAAa,aAAA,CAAAgH,IAAA,EAAAb,SAAA;MAAA,MAAAjG,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA2H,YAAA,CAAAD,OAAA,CAAiB;IAAA,EAAC;IAACzI,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5EH,EAAA,CAAAC,cAAA,mBAA0D;IAAtBD,EAAA,CAAA4B,UAAA,mBAAA+G,iFAAA;MAAA,MAAAF,OAAA,GAAAzI,EAAA,CAAAa,aAAA,CAAAgH,IAAA,EAAAb,SAAA;MAAA,OAAAhH,EAAA,CAAAoB,WAAA,CAASqH,OAAA,CAAAtB,KAAA,EAAW;IAAA,EAAC;IAACnH,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IA7DCH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAqG,KAAA,UAAkB;IAClBpH,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAqG,KAAA,WAAmB;IAMJpH,EAAA,CAAAO,SAAA,GAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAAtC,YAAA,CAA0C;IAE9B9C,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE3EJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAArC,UAAA,CAAwC;IAE5B/C,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAAhC,KAAA,CAAmC;IAEvBpD,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAAqE,eAAA,CAAAnC,UAAA,CAAyC;IAAUjD,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAsG,UAAA,OAA6B;IAC9CrH,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAuG,SAAA,CAAY;IAGlCtH,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAAqE,eAAA,CAAA9B,OAAA,CAAsC;IACFtD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAwG,aAAA,CAAgB;IAIxCvH,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAA3B,UAAA,CAAwC;IAE5BzD,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAAmB,KAAA,CAAmC;IAEvBvG,EAAA,CAAAO,SAAA,EAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,oBAAoB;IAChCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAAqB,OAAA,CAAqC;IAIhEzG,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAqE,eAAA,CAAAyB,OAAA,CAAqC;;;;;;IAqBnD7G,EAAA,CAAAC,cAAA,+BAA0G;IAAtBD,EAA5C,CAAA4B,UAAA,4BAAAgH,sGAAAhI,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAgI,IAAA;MAAA,MAAA9H,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAAkBL,MAAA,CAAA+H,gBAAA,CAAAlI,MAAA,CAAwB;IAAA,EAAC,mBAAAmI,6FAAA;MAAA,MAAAC,OAAA,GAAAhJ,EAAA,CAAAa,aAAA,CAAAgI,IAAA,EAAA7B,SAAA;MAAA,OAAAhH,EAAA,CAAAoB,WAAA,CAAU4H,OAAA,CAAA7B,KAAA,EAAW;IAAA,EAAC;IACzGnH,EAAA,CAAAG,YAAA,EAAsB;;;IADDH,EAAA,CAAAI,UAAA,mBAAkB;;;;;;IAMvCJ,EAAA,CAAAC,cAAA,gCACgE;IAAtBD,EAAxC,CAAA4B,UAAA,6BAAAqH,wGAAA;MAAAjJ,EAAA,CAAAa,aAAA,CAAAqI,IAAA;MAAA,MAAAnI,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAAmBL,MAAA,CAAAoI,iBAAA,EAAmB;IAAA,EAAC,mBAAAC,8FAAA;MAAA,MAAAC,OAAA,GAAArJ,EAAA,CAAAa,aAAA,CAAAqI,IAAA,EAAAlC,SAAA;MAAA,OAAAhH,EAAA,CAAAoB,WAAA,CAAUiI,OAAA,CAAAlC,KAAA,EAAW;IAAA,EAAC;IAC/DnH,EAAA,CAAAG,YAAA,EAAuB;;;;IAFiDH,EAAlD,CAAAI,UAAA,kBAAAW,MAAA,CAAAuI,+BAAA,CAAiD,mBAAmB;;;ADzW5F,OAAM,MAAOC,8BAA+B,SAAQrK,aAAa;EAC/DsK,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAMpB;IACA,KAAAhJ,yBAAyB,GAAG,EAA0F;IACtH,KAAAiJ,qBAAqB,GAA8B,EAAE;IACrD;IACA,KAAA3I,aAAa,GAA8B,EAAE;IAC7C,KAAA4I,eAAe,GAA4B,EAAE;IAC7C,KAAAhF,eAAe,GAAqE;MAAEnC,UAAU,EAAE;IAAE,CAAE;IAEtG,KAAAsE,aAAa,GAAG,CACd;MAAE7F,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAA2F,SAAS,GAAG,IAAI,CAACoC,UAAU,CAACW,cAAc,CAACxK,aAAa,CAAC;IACzD,KAAAuH,KAAK,GAAG,KAAK;IACb,KAAAkD,gBAAgB,GAAG,CAAC;IACpB,KAAAjD,UAAU,GAAG,CAAC,CAAC,CAAC;IAChB,KAAAkD,kBAAkB,GAAG,KAAK,CAAC,CAAC;IAC5B,KAAAjB,+BAA+B,GAA4B,EAAE,CAAC,CAAC;IA8P/D;IACQ,KAAAkB,gBAAgB,GAAG,IAAI;IAnR7B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAsBSC,QAAQA,CAAA,GAAW;EAC5B;EACAF,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACvJ,yBAAyB,CAACoC,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACpC,yBAAyB,CAACuF,OAAO,GAAG,IAAI;IAC7C,IAAI,CAACvF,yBAAyB,CAACyF,SAAS,GAAG,IAAI;IAC/C,IAAI,CAACzF,yBAAyB,CAAC4B,YAAY,GAAG,EAAE;IAChD,IAAI,CAAC5B,yBAAyB,CAAC6B,UAAU,GAAG,EAAE;IAC9C;IACA,IAAI,CAAC7B,yBAAyB,CAAC+B,UAAU,GAAG,IAAI,CAACqE,SAAS,CAACsD,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACnJ,KAAK,CAAC;EACpF;EAEA;EACAoJ,WAAWA,CAAA;IACT,IAAI,CAACL,oBAAoB,EAAE;IAC3B;IACA,IAAI,IAAI,CAACjJ,aAAa,IAAI,IAAI,CAACA,aAAa,CAACqC,MAAM,GAAG,CAAC,EAAE;MACvDkH,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAAC1D,UAAU,KAAK,CAAC,EAAE;UACzB,IAAI,CAACnG,yBAAyB,CAACC,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;QACzE,CAAC,MAAM;UACL,IAAI,CAACY,yBAAyB,CAACC,YAAY,GAAG,CAAC;QACjD;QACA,IAAI,CAAC6J,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEAhI,YAAYA,CAACiI,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAACjE,SAAS,CAACkE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/J,KAAK,IAAI4J,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC5J,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOyJ,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAC/B,KAAK,CAACgC,KAAK,EAAE;IAElB;IACA,IAAI,IAAI,CAACxE,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,CAACwC,KAAK,CAACiC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC1G,eAAe,CAACjE,YAAY,CAAC;IAClE;IAEA,IAAI,CAAC0I,KAAK,CAACiC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1G,eAAe,CAACtC,YAAY,CAAC;IAC9D,IAAI,CAAC+G,KAAK,CAACiC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC1G,eAAe,CAACnC,UAAU,CAAC;IAC7D,IAAI,CAAC4G,KAAK,CAACiC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1G,eAAe,CAAChC,KAAK,CAAC;IACvD,IAAI,CAACyG,KAAK,CAACiC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1G,eAAe,CAAC9B,OAAO,CAAC;IACzD,IAAI,CAACuG,KAAK,CAACiC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1G,eAAe,CAAC3B,UAAU,CAAC;IAC5D,IAAI,CAACoG,KAAK,CAACiC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1G,eAAe,CAACmB,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACnB,eAAe,CAACrC,UAAU,IAAI,IAAI,CAACqC,eAAe,CAACrC,UAAU,CAACc,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACgG,KAAK,CAACkC,aAAa,CAACL,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACtG,eAAe,CAACyB,OAAO,IAAI,IAAI,CAACzB,eAAe,CAACyB,OAAO,CAAChD,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACgG,KAAK,CAACkC,aAAa,CAACL,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEAzJ,GAAGA,CAAC+J,MAAwB;IAC1B,IAAI,CAAC5E,KAAK,GAAG,IAAI;IACjB,IAAI,CAAChC,eAAe,GAAG;MAAEnC,UAAU,EAAE,EAAE;MAAEwD,OAAO,EAAE,KAAK;MAAEE,SAAS,EAAE;IAAK,CAAE;IAC3E,IAAI,CAACvB,eAAe,CAAC9B,OAAO,GAAG,CAAC;IAChC,IAAI,CAAC8B,eAAe,CAAC3B,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAAC4D,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,IAAI,CAACiD,gBAAgB,IAAI,CAAC,EAAE;QAC9B,IAAI,CAAClF,eAAe,CAACjE,YAAY,GAAG,IAAI,CAACmJ,gBAAgB;MAC3D,CAAC,MAAM,IAAI,IAAI,CAAC9I,aAAa,IAAI,IAAI,CAACA,aAAa,CAACqC,MAAM,GAAG,CAAC,EAAE;QAC9D,IAAI,CAACuB,eAAe,CAACjE,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;MAC/D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAC8E,eAAe,CAACjE,YAAY,GAAG,CAAC;MACrC,IAAI,CAACiE,eAAe,CAACnC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC0G,aAAa,CAACsC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEMzJ,MAAMA,CAAC2J,IAA2B,EAAEF,MAAwB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MAChED,KAAI,CAAChC,qBAAqB,CAACkC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAAC/E,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM+E,KAAI,CAACG,OAAO,EAAE;QACpB;QACA,IAAIH,KAAI,CAAC9E,UAAU,KAAK,CAAC,EAAE;UACzB8E,KAAI,CAAC/G,eAAe,CAACnC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1C;QACAkJ,KAAI,CAACxC,aAAa,CAACsC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAtF,IAAIA,CAACyF,GAAQ;IACX,IAAI,CAACd,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC/B,KAAK,CAACkC,aAAa,CAAClI,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC+F,OAAO,CAAC+C,aAAa,CAAC,IAAI,CAAC9C,KAAK,CAACkC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAI,IAAI,CAAC1E,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACjC,eAAe,CAACjE,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAAC4I,kBAAkB,CAAC6C,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAACzH;KACZ,CAAC,CAAC0H,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpD,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACjC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACpB,OAAO,CAACsD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACvF,KAAK,EAAE;EACb;EAEAzE,QAAQA,CAACwJ,IAA2B;IAClC,IAAI,CAAC9G,eAAe,CAACiH,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAACjF,KAAK,GAAG,KAAK;IAClB,IAAIgG,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACvD,kBAAkB,CAACwD,iCAAiC,CAAC;MACxDV,IAAI,EAAE;QACJR,cAAc,EAAE,IAAI,CAACjH,eAAe,CAACiH;;KAExC,CAAC,CAACS,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACnD,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACjC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAN,gBAAgBA,CAAA;IACd,IAAI,CAACZ,gBAAgB,CAAC0D,qCAAqC,CAAC;MAAEX,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEY,IAAI,CAACtO,kBAAkB,CAAC,IAAI,CAAC+K,UAAU,CAAC,CAAC,CAAC4C,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAACvL,aAAa,GAAGuL,GAAG,CAACW,OAAQ;MACjC;MACA,IAAI,IAAI,CAACrG,UAAU,KAAK,CAAC,IAAI,IAAI,CAAC7F,aAAa,CAACqC,MAAM,GAAG,CAAC,EAAE;QAC1D,IAAI,CAAC3C,yBAAyB,CAACC,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;QACvE,IAAI,CAAC0K,OAAO,EAAE;MAChB,CAAC,MAAM,IAAI,IAAI,CAAC3D,UAAU,KAAK,CAAC,EAAE;QAChC,IAAI,CAACnG,yBAAyB,CAACC,YAAY,GAAG,CAAC;QAC/C,IAAI,CAAC6J,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAAC9J,yBAAyB,CAACyM,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAAC1M,yBAAyB,CAAC2M,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAAC1D,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAAC2D,YAAY,GAAG,CAAC;IACrB;IACA,IAAI,IAAI,CAAC1G,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACnG,yBAAyB,CAACC,YAAY,GAAG,CAAC;MAC/C,IAAI,CAACD,yBAAyB,CAAC+B,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACpD,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAAC/B,yBAAyB,CAACC,YAAY,IAAI,IAAI,CAACD,yBAAyB,CAACC,YAAY,IAAI,CAAC,EAAE;QACnG,IAAI,CAACmJ,gBAAgB,GAAG,IAAI,CAACpJ,yBAAyB,CAACC,YAAY;MACrE;IACF;IAEA,IAAI,CAAC4I,kBAAkB,CAACiE,8BAA8B,CAAC;MAAEnB,IAAI,EAAE,IAAI,CAAC3L;IAAyB,CAAE,CAAC,CAC7FuM,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf;UACA,IAAI,CAACtD,eAAe,GAAG2C,GAAG,CAACW,OAAO,CAAC9C,GAAG,CAACqD,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACPtJ,QAAQ,EAAE;WACX,CAAC,CAAC;UACH,IAAI,CAACoJ,YAAY,GAAGhB,GAAG,CAACmB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAAE5B,OAAOA,CAAA;IACP,IAAI,CAACvC,kBAAkB,CAACoE,8BAA8B,CAAC;MAAEtB,IAAI,EAAE,IAAI,CAAC1C;IAAqB,CAAE,CAAC,CACzFsD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACtI,eAAe,GAAG;YAAEnC,UAAU,EAAE,EAAE;YAAEwD,OAAO,EAAE,KAAK;YAAEE,SAAS,EAAE;UAAK,CAAE;UAC3E,IAAI,CAACvB,eAAe,CAACjE,YAAY,GAAG4L,GAAG,CAACW,OAAO,CAACvM,YAAY;UAC5D,IAAI,CAACiE,eAAe,CAACrC,UAAU,GAAGgK,GAAG,CAACW,OAAO,CAAC3K,UAAU;UACxD;UACA,IAAI,CAACqC,eAAe,CAACnC,UAAU,GAAG,IAAI,CAACoE,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAI0F,GAAG,CAACW,OAAO,CAACzK,UAAU,GAAIiI,KAAK,CAACC,OAAO,CAAC4B,GAAG,CAACW,OAAO,CAACzK,UAAU,CAAC,GAAG8J,GAAG,CAACW,OAAO,CAACzK,UAAU,GAAG,CAAC8J,GAAG,CAACW,OAAO,CAACzK,UAAU,CAAC,GAAI,EAAG;UAC9L,IAAI,CAACmC,eAAe,CAACyB,OAAO,GAAGkG,GAAG,CAACW,OAAO,CAAC7G,OAAO;UAClD,IAAI,CAACzB,eAAe,CAACtC,YAAY,GAAGiK,GAAG,CAACW,OAAO,CAAC5K,YAAY;UAC5D,IAAI,CAACsC,eAAe,CAACiH,cAAc,GAAGU,GAAG,CAACW,OAAO,CAACrB,cAAc;UAChE,IAAI,CAACjH,eAAe,CAAChC,KAAK,GAAG2J,GAAG,CAACW,OAAO,CAACtK,KAAK;UAC9C,IAAI,CAACgC,eAAe,CAAC9B,OAAO,GAAGyJ,GAAG,CAACW,OAAO,CAACpK,OAAO;UAClD,IAAI,CAAC8B,eAAe,CAAC3B,UAAU,GAAGsJ,GAAG,CAACW,OAAO,CAACjK,UAAU,IAAI,CAAC;UAC7D,IAAI,CAAC2B,eAAe,CAACmB,KAAK,GAAGwG,GAAG,CAACW,OAAO,CAACnH,KAAK;UAC9C;UACA,IAAI,CAACnB,eAAe,CAACqB,OAAO,GAAIsG,GAAG,CAACW,OAAe,CAACjH,OAAO,IAAI,KAAK;UACpE,IAAI,CAACrB,eAAe,CAACuB,SAAS,GAAIoG,GAAG,CAACW,OAAe,CAAC/G,SAAS,IAAI,KAAK;QAC1E;MACF;IACF,CAAC,CAAC;EACN;EAEAyH,iBAAiBA,CAAC1M,KAAa,EAAE2M,OAAY;IAC3C7B,OAAO,CAACC,GAAG,CAAC4B,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAACjJ,eAAe,CAACnC,UAAU,EAAEqL,QAAQ,CAAC5M,KAAK,CAAC,EAAE;QACrD,IAAI,CAAC0D,eAAe,CAACnC,UAAU,EAAEyI,IAAI,CAAChK,KAAK,CAAC;MAC9C;MACA8K,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrH,eAAe,CAACnC,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACmC,eAAe,CAACnC,UAAU,GAAG,IAAI,CAACmC,eAAe,CAACnC,UAAU,EAAEsL,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK9M,KAAK,CAAC;IAC7F;EACF;EAEA6B,cAAcA,CAAC2I,IAAS;IACtB,OAAOA,IAAI,CAACzF,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAEAjD,gBAAgBA,CAAC0I,IAAS;IACxB,OAAOA,IAAI,CAACvF,SAAS,GAAG,GAAG,GAAG,GAAG;EACnC;EAIA8H,WAAWA,CAACC,KAAU;IACpB;IACA,IAAI,IAAI,CAAClE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B;IACF;IACA;IACA,IAAIkE,KAAK,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACtH,UAAU,GAAG,CAAC;MACnB,IAAI,CAACnG,yBAAyB,CAACC,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,IAAI,CAACkG,UAAU,GAAG,CAAC;MACnB;MACA,IAAI,IAAI,CAAC7F,aAAa,IAAI,IAAI,CAACA,aAAa,CAACqC,MAAM,GAAG,CAAC,EAAE;QACvD,IAAI,CAAC3C,yBAAyB,CAACC,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;MACzE;IACF;IACA,IAAI,CAAC0K,OAAO,EAAE;EAChB;EAEA;EACA4D,WAAWA,CAAC5C,MAAwB;IAClC,IAAI,CAAC5E,KAAK,GAAG,IAAI;IACjB,IAAI,CAAChC,eAAe,GAAG;MAAEnC,UAAU,EAAE,EAAE;MAAEwD,OAAO,EAAE,KAAK;MAAEE,SAAS,EAAE;IAAK,CAAE;IAC3E,IAAI,CAACvB,eAAe,CAAC9B,OAAO,GAAG,CAAC;IAChC,IAAI,CAAC8B,eAAe,CAAC3B,UAAU,GAAG,CAAC;IACnC;IACA,IAAI,CAAC2B,eAAe,CAACjE,YAAY,GAAG,CAAC;IACrC,IAAI,CAACiE,eAAe,CAACnC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACxC,IAAI,CAAC0G,aAAa,CAACsC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACM6C,cAAcA,CAAC3C,IAA2B,EAAEF,MAAwB;IAAA,IAAA8C,MAAA;IAAA,OAAA1C,iBAAA;MACxE0C,MAAI,CAAC3E,qBAAqB,CAACkC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEyC,MAAI,CAAC1H,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM0H,MAAI,CAACxC,OAAO,EAAE;QACpB;QACAwC,MAAI,CAAC1J,eAAe,CAACnC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACxC6L,MAAI,CAACnF,aAAa,CAACsC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA;EACA7D,YAAYA,CAACgE,GAAQ;IACnB;IACA,IAAI,CAAC7C,KAAK,CAACgC,KAAK,EAAE;IAClB,IAAI,CAAChC,KAAK,CAACiC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1G,eAAe,CAACtC,YAAY,CAAC;IAC9D;IACA,IAAI,IAAI,CAACuE,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACjC,eAAe,CAACnC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC4G,KAAK,CAACiC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC1G,eAAe,CAACnC,UAAU,CAAC;IAC7D,IAAI,CAAC4G,KAAK,CAACiC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1G,eAAe,CAAChC,KAAK,CAAC;IACvD,IAAI,CAACyG,KAAK,CAACiC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1G,eAAe,CAAC9B,OAAO,CAAC;IACzD,IAAI,CAACuG,KAAK,CAACiC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1G,eAAe,CAAC3B,UAAU,CAAC;IAC5D,IAAI,CAACoG,KAAK,CAACiC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1G,eAAe,CAACmB,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACnB,eAAe,CAACrC,UAAU,IAAI,IAAI,CAACqC,eAAe,CAACrC,UAAU,CAACc,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACgG,KAAK,CAACkC,aAAa,CAACL,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACtG,eAAe,CAACyB,OAAO,IAAI,IAAI,CAACzB,eAAe,CAACyB,OAAO,CAAChD,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACgG,KAAK,CAACkC,aAAa,CAACL,IAAI,CAAC,kBAAkB,CAAC;IACnD;IAEA,IAAI,IAAI,CAAC7B,KAAK,CAACkC,aAAa,CAAClI,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC+F,OAAO,CAAC+C,aAAa,CAAC,IAAI,CAAC9C,KAAK,CAACkC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMgD,YAAY,GAAG;MAAE,GAAG,IAAI,CAAC3J;IAAe,CAAE;IAChD2J,YAAY,CAAC5N,YAAY,GAAG,CAAC;IAC7B;IACA,IAAI,IAAI,CAACkG,UAAU,KAAK,CAAC,EAAE;MACzB0H,YAAY,CAAC9L,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClC;IAEA,IAAI,CAAC8G,kBAAkB,CAAC6C,+BAA+B,CAAC;MACtDC,IAAI,EAAEkC;KACP,CAAC,CAACjC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpD,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACjC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACpB,OAAO,CAACsD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACvF,KAAK,EAAE;EACb;EAEA6H,kBAAkBA,CAACC,oBAAsC;IACvD,IAAI,CAACtF,aAAa,CAACsC,IAAI,CAACgD,oBAAoB,CAAC;EAC/C;EAEAnG,gBAAgBA,CAACoG,GAAa;IAC5B;EAAA;EAGF;EACAtL,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACwG,eAAe,CAACmE,MAAM,CAACY,GAAG,IAAIA,GAAG,CAACxK,QAAQ,CAAC;EACzD;EAEA;EACAE,4BAA4BA,CAAA;IAC1B;EAAA;EAGF;EACAb,qBAAqBA,CAAA;IACnB,IAAI,CAACoG,eAAe,CAACiB,OAAO,CAAC8D,GAAG,IAAIA,GAAG,CAACxK,QAAQ,GAAG,IAAI,CAAC;EAC1D;EAEA;EACAR,kBAAkBA,CAAA;IAChB,IAAI,CAACiG,eAAe,CAACiB,OAAO,CAAC8D,GAAG,IAAIA,GAAG,CAACxK,QAAQ,GAAG,KAAK,CAAC;EAC3D;EAEA;EACAyK,aAAaA,CAAA;IACX,OAAO,IAAI,CAAChF,eAAe,CAACvG,MAAM,GAAG,CAAC,IAAI,IAAI,CAACuG,eAAe,CAACiF,KAAK,CAACF,GAAG,IAAIA,GAAG,CAACxK,QAAQ,CAAC;EAC3F;EAEA;EACA2K,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG,IAAI,CAACnF,eAAe,CAACmE,MAAM,CAACY,GAAG,IAAIA,GAAG,CAACxK,QAAQ,CAAC,CAACd,MAAM;IAC7E,OAAO0L,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,IAAI,CAACnF,eAAe,CAACvG,MAAM;EACzE;EAEA;EACA2L,eAAeA,CAACd,KAAU;IACxB,MAAMe,SAAS,GAAGf,KAAK,CAACgB,MAAM,CAACrB,OAAO;IACtC,IAAI,CAACjE,eAAe,CAACiB,OAAO,CAAC8D,GAAG,IAAIA,GAAG,CAACxK,QAAQ,GAAG8K,SAAS,CAAC;EAC/D;EAEA;EACAE,mBAAmBA,CAACC,qBAAuC;IACzD,MAAMC,oBAAoB,GAAG,IAAI,CAACjM,uBAAuB,EAAE;IAC3D,IAAIiM,oBAAoB,CAAChM,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC+F,OAAO,CAACsD,YAAY,CAAC,cAAc,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAAC5D,+BAA+B,GAAG,CAAC,GAAGuG,oBAAoB,CAAC;IAChE,IAAI,CAACtF,kBAAkB,GAAG,IAAI;IAC9B,MAAMvD,SAAS,GAAG,IAAI,CAAC2C,aAAa,CAACsC,IAAI,CAAC2D,qBAAqB,CAAC;IAEhE;IACA5I,SAAS,CAAC8I,OAAO,CAAChD,SAAS,CAAC,MAAK;MAC/B,IAAI,CAACvC,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACjB,+BAA+B,GAAG,EAAE;IAC3C,CAAC,CAAC;EACJ;EAEA;EACAH,iBAAiBA,CAAA;IACf,IAAI,CAACS,OAAO,CAACqD,aAAa,CAAC,QAAQ,CAAC;IACpC;IACA,IAAI,CAAC9I,kBAAkB,EAAE;EAC3B;;;uCAzcWoF,8BAA8B,EAAAvJ,EAAA,CAAA+P,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjQ,EAAA,CAAA+P,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAnQ,EAAA,CAAA+P,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAArQ,EAAA,CAAA+P,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAvQ,EAAA,CAAA+P,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAzQ,EAAA,CAAA+P,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA3Q,EAAA,CAAA+P,iBAAA,CAAAW,EAAA,CAAAE,kBAAA,GAAA5Q,EAAA,CAAA+P,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAA9Q,EAAA,CAAA+P,iBAAA,CAAAgB,EAAA,CAAAC,MAAA,GAAAhR,EAAA,CAAA+P,iBAAA,CAAA/P,EAAA,CAAAiR,UAAA;IAAA;EAAA;;;YAA9B1H,8BAA8B;MAAA2H,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApR,EAAA,CAAAqR,0BAAA,EAAArR,EAAA,CAAAsR,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UClDzC5R,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAkC,SAAA,qBAAiC;UACnClC,EAAA,CAAAG,YAAA,EAAiB;UAKbH,EAFJ,CAAAC,cAAA,sBAAoC,aACd,aACD;UACfD,EAAA,CAAAqB,UAAA,IAAAyQ,6CAAA,iBAAiE;UAS/D9R,EADF,CAAAC,cAAA,aAAwC,gBACM;UAAAD,EAAA,CAAAE,MAAA,+BAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,iBACuD;UAArDD,EAAA,CAAAU,gBAAA,2BAAAqR,wEAAAnR,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAAhS,EAAA,CAAAiB,kBAAA,CAAA4Q,GAAA,CAAA3Q,yBAAA,CAAA4B,YAAA,EAAAlC,MAAA,MAAAiR,GAAA,CAAA3Q,yBAAA,CAAA4B,YAAA,GAAAlC,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAoD;UACxDZ,EAFE,CAAAG,YAAA,EACuD,EACnD;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACI;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,iBACqD;UAAnDD,EAAA,CAAAU,gBAAA,2BAAAuR,wEAAArR,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAAhS,EAAA,CAAAiB,kBAAA,CAAA4Q,GAAA,CAAA3Q,yBAAA,CAAA6B,UAAA,EAAAnC,MAAA,MAAAiR,GAAA,CAAA3Q,yBAAA,CAAA6B,UAAA,GAAAnC,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAkD;UAExDZ,EAHI,CAAAG,YAAA,EACqD,EACjD,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBAAqF;UAA1ED,EAAA,CAAAU,gBAAA,2BAAAwR,4EAAAtR,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAAhS,EAAA,CAAAiB,kBAAA,CAAA4Q,GAAA,CAAA3Q,yBAAA,CAAA+B,UAAA,EAAArC,MAAA,MAAAiR,GAAA,CAAA3Q,yBAAA,CAAA+B,UAAA,GAAArC,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAkD;UAC3DZ,EAAA,CAAAqB,UAAA,KAAA8Q,oDAAA,wBAA+D;UAInEnS,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,qBAAyE;UAA9DD,EAAA,CAAAU,gBAAA,2BAAA0R,4EAAAxR,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAAhS,EAAA,CAAAiB,kBAAA,CAAA4Q,GAAA,CAAA3Q,yBAAA,CAAAoC,OAAA,EAAA1C,MAAA,MAAAiR,GAAA,CAAA3Q,yBAAA,CAAAoC,OAAA,GAAA1C,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAA+C;UACxDZ,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAE7BF,EAF6B,CAAAG,YAAA,EAAY,EAC3B,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,qBAAyE;UAA9DD,EAAA,CAAAU,gBAAA,2BAAA2R,4EAAAzR,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAAhS,EAAA,CAAAiB,kBAAA,CAAA4Q,GAAA,CAAA3Q,yBAAA,CAAAuF,OAAA,EAAA7F,MAAA,MAAAiR,GAAA,CAAA3Q,yBAAA,CAAAuF,OAAA,GAAA7F,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAA+C;UACxDZ,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAGlCF,EAHkC,CAAAG,YAAA,EAAY,EAC9B,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACG;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,qBAA2E;UAAhED,EAAA,CAAAU,gBAAA,2BAAA4R,4EAAA1R,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAAhS,EAAA,CAAAiB,kBAAA,CAAA4Q,GAAA,CAAA3Q,yBAAA,CAAAyF,SAAA,EAAA/F,MAAA,MAAAiR,GAAA,CAAA3Q,yBAAA,CAAAyF,SAAA,GAAA/F,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAiD;UAC1DZ,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAEhCF,EAFgC,CAAAG,YAAA,EAAY,EAC9B,EACR;UACNH,EAAA,CAAAkC,SAAA,cAA8C;UAChDlC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACfD,EAAA,CAAAkC,SAAA,eAA4B;UAE1BlC,EADF,CAAAC,cAAA,eAAmD,kBACc;UAAxBD,EAAA,CAAA4B,UAAA,mBAAA2Q,iEAAA;YAAAvS,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAA,OAAAhS,EAAA,CAAAoB,WAAA,CAASyQ,GAAA,CAAA/G,WAAA,EAAa;UAAA,EAAC;UAAC9K,EAAA,CAAAkC,SAAA,aAAgC;UAAAlC,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1GH,EAAA,CAAAC,cAAA,kBAAsD;UAApBD,EAAA,CAAA4B,UAAA,mBAAA4Q,iEAAA;YAAAxS,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAA,OAAAhS,EAAA,CAAAoB,WAAA,CAASyQ,GAAA,CAAA7G,OAAA,EAAS;UAAA,EAAC;UAAChL,EAAA,CAAAkC,SAAA,aAAkC;UAAAlC,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnGH,EAAA,CAAAqB,UAAA,KAAAoR,iDAAA,qBAA4E;UAMpFzS,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;UAIbH,EADF,CAAAC,cAAA,uBAAoC,qBACW;UAAlCD,EAAA,CAAA4B,UAAA,uBAAA8Q,wEAAA9R,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAA,OAAAhS,EAAA,CAAAoB,WAAA,CAAayQ,GAAA,CAAApD,WAAA,CAAA7N,MAAA,CAAmB;UAAA,EAAC;UAS5BZ,EARd,CAAAC,cAAA,kBAAuB,eACH,eAES,eACO,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAqB,UAAA,KAAAsR,6CAAA,mBAAuE;UAmB7E3S,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,0BAC2B;UADqBD,EAAA,CAAAU,gBAAA,wBAAAkS,8EAAAhS,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAAhS,EAAA,CAAAiB,kBAAA,CAAA4Q,GAAA,CAAA/D,SAAA,EAAAlN,MAAA,MAAAiR,GAAA,CAAA/D,SAAA,GAAAlN,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAoB;UAClEZ,EAAA,CAAA4B,UAAA,wBAAAgR,8EAAA;YAAA5S,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAA,OAAAhS,EAAA,CAAAoB,WAAA,CAAcyQ,GAAA,CAAA7G,OAAA,EAAS;UAAA,EAAC;UAIhChL,EAHM,CAAAG,YAAA,EAAiB,EACb,EACF,EACC;UAODH,EALR,CAAAC,cAAA,kBAAuB,eACH,eAES,eACY,eACW;UAC1CD,EAAA,CAAAkC,SAAA,cAAoD;UACpDlC,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,6CAAM;UACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;UACNH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAE,MAAA,oaAEF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEFH,EADF,CAAAC,cAAA,gBAAgC,iBACO;UACnCD,EAAA,CAAAkC,SAAA,cAAgC;UAAAlC,EAAA,CAAAE,MAAA,8CAClC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,iBAAqC;UACnCD,EAAA,CAAAkC,SAAA,cAAuC;UAAAlC,EAAA,CAAAE,MAAA,kCACzC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,iBAAgC;UAC9BD,EAAA,CAAAkC,SAAA,cAAqC;UAAAlC,EAAA,CAAAE,MAAA,wCACvC;UAGNF,EAHM,CAAAG,YAAA,EAAO,EACH,EACF,EACF;UAQIH,EALV,CAAAC,cAAA,gBAAyB,gBACiB,gBACyB,gBACtB,gBACK,mBAEc;UADjBD,EAAA,CAAA4B,UAAA,mBAAAiR,kEAAA;YAAA7S,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAA,MAAAc,yBAAA,GAAA9S,EAAA,CAAAgC,WAAA;YAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASyQ,GAAA,CAAAlC,mBAAA,CAAAmD,yBAAA,CAA0C;UAAA,EAAC;UAEvF9S,EAAA,CAAAkC,SAAA,cAAgC;UAAAlC,EAAA,CAAAE,MAAA,kCAChC;UAAAF,EAAA,CAAAqB,UAAA,MAAA0R,gDAAA,mBAAkF;UAGpF/S,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAAmF;UAAnDD,EAAA,CAAA4B,UAAA,mBAAAoR,kEAAA;YAAAhT,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAA,MAAAiB,wBAAA,GAAAjT,EAAA,CAAAgC,WAAA;YAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASyQ,GAAA,CAAA7C,kBAAA,CAAAiE,wBAAA,CAAwC;UAAA,EAAC;UAChFjT,EAAA,CAAAkC,SAAA,cAA+B;UAAAlC,EAAA,CAAAE,MAAA,kCACjC;UAEFF,EAFE,CAAAG,YAAA,EAAS,EAEL;UACNH,EAAA,CAAAC,cAAA,gBAAkC;UAIhCD,EAHA,CAAAqB,UAAA,MAAA6R,iDAAA,oBAAyE,MAAAC,iDAAA,oBAGA;UAI7EnT,EADE,CAAAG,YAAA,EAAM,EACF;UACNH,EAAA,CAAAC,cAAA,gBAAuC;UAKrCD,EAJA,CAAAqB,UAAA,MAAA+R,kDAAA,qBACqC,MAAAC,kDAAA,qBAIU;UAKrDrT,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UASMH,EANZ,CAAAC,cAAA,gBAAyB,gBACO,kBACmE,cACtF,eAC4D,eACjC,kBAE6C;UAAnCD,EAAA,CAAA4B,UAAA,oBAAA0R,kEAAA1S,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAA,OAAAhS,EAAA,CAAAoB,WAAA,CAAUyQ,GAAA,CAAArC,eAAA,CAAA5O,MAAA,CAAuB;UAAA,EAAC;UAC1EZ,EAFE,CAAAG,YAAA,EACyE,EACtE;UACLH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAqB,UAAA,MAAAkS,8CAAA,mBACuC;UAsB7CvT,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,2BAC2B;UADqBD,EAAA,CAAAU,gBAAA,wBAAA8S,+EAAA5S,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAAhS,EAAA,CAAAiB,kBAAA,CAAA4Q,GAAA,CAAA/D,SAAA,EAAAlN,MAAA,MAAAiR,GAAA,CAAA/D,SAAA,GAAAlN,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAoB;UAClEZ,EAAA,CAAA4B,UAAA,wBAAA4R,+EAAA;YAAAxT,EAAA,CAAAa,aAAA,CAAAmR,GAAA;YAAA,OAAAhS,EAAA,CAAAoB,WAAA,CAAcyQ,GAAA,CAAA7G,OAAA,EAAS;UAAA,EAAC;UAQxChL,EAPc,CAAAG,YAAA,EAAiB,EACb,EACF,EACF,EACC,EACC,EACC,EACP;UAgKVH,EA7JA,CAAAqB,UAAA,MAAAoS,uDAAA,kCAAAzT,EAAA,CAAA0T,sBAAA,CAAkD,MAAAC,uDAAA,kCAAA3T,EAAA,CAAA0T,sBAAA,CAiFQ,MAAAE,uDAAA,gCAAA5T,EAAA,CAAA0T,sBAAA,CAsEM,MAAAG,uDAAA,gCAAA7T,EAAA,CAAA0T,sBAAA,CAMC;;;UAlZhB1T,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAI,UAAA,SAAAyR,GAAA,CAAAxK,UAAA,OAAsB;UAW3DrH,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAAuB,gBAAA,YAAAsQ,GAAA,CAAA3Q,yBAAA,CAAA4B,YAAA,CAAoD;UAKpD9C,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAuB,gBAAA,YAAAsQ,GAAA,CAAA3Q,yBAAA,CAAA6B,UAAA,CAAkD;UAMzC/C,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAuB,gBAAA,YAAAsQ,GAAA,CAAA3Q,yBAAA,CAAA+B,UAAA,CAAkD;UAC/BjD,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAyR,GAAA,CAAAvK,SAAA,CAAY;UAO/BtH,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAuB,gBAAA,YAAAsQ,GAAA,CAAA3Q,yBAAA,CAAAoC,OAAA,CAA+C;UAC7CtD,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACZJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UACXJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UAKbJ,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAuB,gBAAA,YAAAsQ,GAAA,CAAA3Q,yBAAA,CAAAuF,OAAA,CAA+C;UAC7CzG,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UAOjBJ,EAAA,CAAAO,SAAA,GAAiD;UAAjDP,EAAA,CAAAuB,gBAAA,YAAAsQ,GAAA,CAAA3Q,yBAAA,CAAAyF,SAAA,CAAiD;UAC/C3G,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UAUgCJ,EAAA,CAAAO,SAAA,IAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAyR,GAAA,CAAAiC,QAAA,CAAc;UAgC7C9T,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAAyR,GAAA,CAAAzH,eAAA,CAAoB;UAoB/BpK,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAAyR,GAAA,CAAA9D,YAAA,CAA+B;UAAC/N,EAAA,CAAAuB,gBAAA,SAAAsQ,GAAA,CAAA/D,SAAA,CAAoB;UAAC9N,EAAA,CAAAI,UAAA,aAAAyR,GAAA,CAAAjE,QAAA,CAAqB;UAyChF5N,EAAA,CAAAO,SAAA,IAAmD;UAAnDP,EAAA,CAAAI,UAAA,aAAAyR,GAAA,CAAAjO,uBAAA,GAAAC,MAAA,OAAmD;UAE5C7D,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAI,UAAA,SAAAyR,GAAA,CAAAjO,uBAAA,GAAAC,MAAA,KAA0C;UAUxB7D,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAI,UAAA,SAAAyR,GAAA,CAAAjO,uBAAA,GAAAC,MAAA,OAA4C;UAG1C7D,EAAA,CAAAO,SAAA,EAA0C;UAA1CP,EAAA,CAAAI,UAAA,SAAAyR,GAAA,CAAAjO,uBAAA,GAAAC,MAAA,KAA0C;UAOtE7D,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAI,UAAA,SAAAyR,GAAA,CAAAzH,eAAA,CAAAvG,MAAA,KAAgC;UAIhC7D,EAAA,CAAAO,SAAA,EAA0C;UAA1CP,EAAA,CAAAI,UAAA,SAAAyR,GAAA,CAAAjO,uBAAA,GAAAC,MAAA,KAA0C;UAcS7D,EAAA,CAAAO,SAAA,GAA2B;UACzEP,EAD8C,CAAAI,UAAA,YAAAyR,GAAA,CAAAzC,aAAA,GAA2B,kBAAAyC,GAAA,CAAAvC,eAAA,GACtC;UAcpBtP,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAAyR,GAAA,CAAAzH,eAAA,CAAoB;UAwB/BpK,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAAyR,GAAA,CAAA9D,YAAA,CAA+B;UAAC/N,EAAA,CAAAuB,gBAAA,SAAAsQ,GAAA,CAAA/D,SAAA,CAAoB;UAAC9N,EAAA,CAAAI,UAAA,aAAAyR,GAAA,CAAAjE,QAAA,CAAqB;;;qBDnNpGhP,YAAY,EAAAwR,EAAA,CAAA2D,eAAA,EAAA3D,EAAA,CAAA4D,mBAAA,EAAA5D,EAAA,CAAA6D,qBAAA,EAAA7D,EAAA,CAAA8D,qBAAA,EACZ7U,mBAAmB,EACnBP,aAAa,EAAAsR,EAAA,CAAA+D,gBAAA,EACb7U,WAAW,EAAA8U,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,4BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,kBAAA,EAAAL,EAAA,CAAAM,OAAA,EACX1V,cAAc,EAAAoR,EAAA,CAAAuE,iBAAA,EAAAvE,EAAA,CAAAwE,iBAAA,EACd7V,cAAc,EACdS,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVb,gBAAgB,EAAAuR,EAAA,CAAAyE,mBAAA,EAChBlV,kBAAkB,EAClBC,oBAAoB,EACpBX,cAAc,EAAAmR,EAAA,CAAA0E,iBAAA,EAAA1E,EAAA,CAAA2E,cAAA,EACdjV,uBAAuB,EACvBC,wBAAwB;MAAAiV,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}