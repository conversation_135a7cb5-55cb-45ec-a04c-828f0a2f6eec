/**
 * 統一色彩系統 - 金色主題
 * 基於主色系 #B8A676 設計的完整色彩體系
 */

// ===== 主要品牌色彩 =====
$primary-gold-light: #B8A676;      // 主要金色 - 淺色
$primary-gold-base: #AE9B66;       // 主要金色 - 基礎色
$primary-gold-dark: #A69660;       // 主要金色 - 深色
$primary-gold-darker: #9B8A5A;     // 主要金色 - 更深
$primary-gold-hover: #C4B382;      // 懸停狀態
$primary-gold-active: #A89660;     // 活動狀態
$primary-gold-disabled: #D4C8A8;   // 禁用狀態

// ===== 輔助金色調色板 =====
$gold-50: #FEFCF8;                 // 極淡金色背景
$gold-100: #F8F6F0;                // 淡金色背景
$gold-200: #F0EDE5;                // 淺金色背景
$gold-300: #E8E2D5;                // 中淺金色
$gold-400: #D4C8A8;                // 中金色
$gold-500: #B8A676;                // 主金色
$gold-600: #AE9B66;                // 深金色
$gold-700: #9B8A5A;                // 更深金色
$gold-800: #8A7A4F;                // 暗金色
$gold-900: #6B5F3E;                // 最深金色

// ===== 漸變色彩 =====
$gradient-primary: linear-gradient(135deg, $primary-gold-light 0%, $primary-gold-dark 100%);
$gradient-primary-hover: linear-gradient(135deg, $primary-gold-hover 0%, $primary-gold-active 100%);
$gradient-primary-light: linear-gradient(135deg, $gold-200 0%, $gold-300 100%);
$gradient-background: linear-gradient(to bottom, $gold-50 0%, $gold-100 100%);

// ===== 文字色彩 =====
$text-primary: #2C3E50;            // 主要文字
$text-secondary: #5A5A5A;          // 次要文字
$text-tertiary: #6C757D;           // 輔助文字
$text-muted: #ADB5BD;              // 靜音文字
$text-disabled: #CED4DA;           // 禁用文字
$text-light: #FFFFFF;              // 淺色文字
$text-dark: #212529;               // 深色文字

// ===== 背景色彩 =====
$bg-primary: #FFFFFF;              // 主要背景
$bg-secondary: #F8F9FA;            // 次要背景
$bg-tertiary: #F5F5F5;             // 第三背景
$bg-cream: #FEFCF8;                // 奶油色背景
$bg-light-gold: rgba(184, 166, 118, 0.03); // 極淡金色背景
$bg-hover: rgba(184, 166, 118, 0.05);      // 懸停背景
$bg-selected: rgba(184, 166, 118, 0.15);   // 選中背景

// ===== 邊框色彩 =====
$border-light: #E9ECEF;            // 淺色邊框
$border-medium: #CDCDCD;           // 中等邊框
$border-dark: #ADB5BD;             // 深色邊框
$border-primary: rgba(184, 166, 118, 0.3); // 主色邊框
$border-focus: rgba(184, 166, 118, 0.5);   // 焦點邊框

// ===== 狀態色彩 =====
// 成功狀態
$success-light: #D4EDDA;
$success-base: #28A745;
$success-dark: #1E7E34;
$success-gradient: linear-gradient(135deg, #E8F5E8 0%, #D4EDDA 100%);

// 警告狀態
$warning-light: #FFF3CD;
$warning-base: #FFC107;
$warning-dark: #E0A800;
$warning-gradient: linear-gradient(135deg, #FFF8E1 0%, #FFF3CD 100%);

// 錯誤狀態
$error-light: #F8D7DA;
$error-base: #DC3545;
$error-dark: #C82333;
$error-gradient: linear-gradient(135deg, #FFEBEE 0%, #F8D7DA 100%);

// 資訊狀態
$info-light: #D1ECF1;
$info-base: #17A2B8;
$info-dark: #138496;
$info-gradient: linear-gradient(135deg, #E3F2FD 0%, #D1ECF1 100%);

// ===== 陰影系統 =====
$shadow-sm: 0 1px 3px rgba(184, 166, 118, 0.1);
$shadow-md: 0 2px 8px rgba(184, 166, 118, 0.15);
$shadow-lg: 0 4px 12px rgba(184, 166, 118, 0.2);
$shadow-xl: 0 8px 24px rgba(184, 166, 118, 0.25);

// 特殊陰影
$shadow-focus: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);
$shadow-inset: inset 0 1px 0 rgba(255, 255, 255, 0.3), inset 0 -1px 0 rgba(0, 0, 0, 0.1);

// ===== 動畫和過渡 =====
$transition-fast: 0.15s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;
$transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

// ===== Nebular 主題色彩映射 =====
// 主要色彩
$nb-primary: $primary-gold-light;
$nb-primary-light: $primary-gold-hover;
$nb-primary-dark: $primary-gold-dark;

// 次要色彩
$nb-success: $success-base;
$nb-success-light: $success-light;
$nb-success-dark: $success-dark;

$nb-warning: $warning-base;
$nb-warning-light: $warning-light;
$nb-warning-dark: $warning-dark;

$nb-danger: $error-base;
$nb-danger-light: $error-light;
$nb-danger-dark: $error-dark;

$nb-info: $info-base;
$nb-info-light: $info-light;
$nb-info-dark: $info-dark;

// 背景和文字
$nb-bg-primary: $bg-primary;
$nb-bg-secondary: $bg-secondary;
$nb-text-primary: $text-primary;
$nb-text-secondary: $text-secondary;
$nb-text-hint: $text-muted;

// 邊框
$nb-border-basic: $border-light;
$nb-border-alternative: $border-medium;

// ===== 特殊用途色彩 =====
// Radio Button 專用
$radio-bg-hover: radial-gradient(circle at center, rgba(184, 166, 118, 0.1) 0%, transparent 70%);
$radio-bg-selected: $gradient-primary;
$radio-bg-selected-hover: $gradient-primary-hover;
$radio-inner-dot: radial-gradient(circle, $text-light 0%, rgba(255, 255, 255, 0.9) 100%);

// 表格專用
$table-header-bg: $gradient-primary-light;
$table-row-hover: $bg-hover;
$table-row-selected: $bg-selected;
$table-border: $border-primary;

// 卡片專用
$card-bg: $bg-primary;
$card-header-bg: $gradient-primary;
$card-border: $border-primary;
$card-shadow: $shadow-md;

// 按鈕專用
$btn-primary-bg: $gradient-primary;
$btn-primary-hover: $gradient-primary-hover;
$btn-secondary-bg: $bg-secondary;
$btn-secondary-hover: $bg-hover;

// ===== 響應式斷點色彩調整 =====
// 在小螢幕上使用更柔和的色彩
$mobile-primary: lighten($primary-gold-light, 5%);
$mobile-shadow: rgba(184, 166, 118, 0.08);

// ===== 深色主題支援 =====
$dark-bg-primary: #1A1A1A;
$dark-bg-secondary: #2D2D2D;
$dark-text-primary: #FFFFFF;
$dark-text-secondary: #CCCCCC;
$dark-border: #404040;

// ===== 輔助函數色彩 =====
// 透明度變化
@function alpha-gold($alpha) {
  @return rgba(184, 166, 118, $alpha);
}

// 亮度調整
@function lighten-gold($amount) {
  @return lighten($primary-gold-light, $amount);
}

@function darken-gold($amount) {
  @return darken($primary-gold-light, $amount);
}

// ===== 色彩驗證 =====
// 確保色彩對比度符合 WCAG 標準
$contrast-ratio-aa: 4.5;
$contrast-ratio-aaa: 7;

// ===== 舊版相容性 =====
// 保持向後相容性的變數映射
$mainColorB: $primary-gold-light;
$mainColorG: $success-base;
$mainColorGray: $text-secondary;
$textColor: $text-primary;
$mainColorY: $warning-base;
$color-drop: $shadow-md;
