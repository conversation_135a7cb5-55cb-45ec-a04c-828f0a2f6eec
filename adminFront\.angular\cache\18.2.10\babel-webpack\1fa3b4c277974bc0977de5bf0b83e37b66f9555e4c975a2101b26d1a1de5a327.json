{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nlet DetailContentManagementSalesAccountComponent = class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService, _houseCustomService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this._houseCustomService = _houseCustomService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    // 通知類型選項映射\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: EnumHouseType.地主戶\n    }, {\n      label: '銷售戶',\n      value: EnumHouseType.銷售戶\n    }];\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    // 新增：戶別選擇器相關屬性\n    this.buildingData = {}; // 存放建築物戶別資料\n    this.listFormItem = null;\n    this.isNew = true;\n  }\n  // 動態獲取標題文字\n  get dynamicTitle() {\n    const option = this.cNoticeTypeOptions.find(option => option.value === this.typeContentManagementSalesAccount.CNoticeType);\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\n  }\n  // 設置通知類型（可供外部調用）\n  setCNoticeType(noticeType) {\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\n      // 同時設定 CFormType 以保持一致性\n      this.typeContentManagementSalesAccount.CFormType = noticeType;\n    }\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId > 0) {\n          this.getListRegularNoticeFileHouseHold();\n        } else {\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\n          console.error('Invalid buildCaseId:', this.buildCaseId);\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\n          this.goBack();\n        }\n      }\n    });\n    // 處理查詢參數中的戶型\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['houseType']) {\n        const houseType = +queryParams['houseType'];\n        this.setCNoticeType(houseType);\n      }\n    });\n    // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\n    this.loadBuildingDataFromAPI();\n  }\n  ngOnDestroy() {\n    // 確保在組件銷毀時恢復body的滾動\n    document.body.style.overflow = 'auto';\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      formItemReq.isModalOpen = true;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\n  extractHouseholdCodes(households) {\n    if (!households || !Array.isArray(households)) {\n      return [];\n    }\n    return households.map(h => h.code || h);\n  }\n  // 新增：處理戶別選擇變更\n  onHouseholdSelectionChange(selectedHouseholds, formItemReq) {\n    // 重置所有戶別選擇狀態\n    Object.keys(formItemReq.selectedItems).forEach(key => {\n      formItemReq.selectedItems[key] = false;\n    });\n    // 設置選中的戶別\n    selectedHouseholds.forEach(household => {\n      formItemReq.selectedItems[household] = true;\n    });\n    // 更新全選狀態\n    formItemReq.allSelected = this.houseHoldList.length > 0 && this.houseHoldList.every(item => formItemReq.selectedItems[item]);\n    // 更新緩存\n    this.updateSelectedHouseholdsCache(formItemReq);\n  }\n  // 新增：取得已選戶別數組\n  getSelectedHouseholds(formItemReq) {\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\n  }\n  // 新增：更新已選戶別緩存\n  updateSelectedHouseholdsCache(formItemReq) {\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\n    console.log('Updated selected households cache:', formItemReq.selectedHouseholdsCached);\n  }\n  // 新增：更新所有項目的緩存\n  updateAllSelectedHouseholdsCache() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(formItemReq => {\n        this.updateSelectedHouseholdsCache(formItemReq);\n      });\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            currentImageIndex: 0,\n            isModalOpen: false,\n            CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64).filter(url => url != null) : []\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem?.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0,\n              isModalOpen: false,\n              selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\n            };\n          });\n        } else {\n          // 當無資料時，載入材料清單供新增使用\n          this.getMaterialList();\n        }\n        // 初始化所有項目的緩存\n        this.updateAllSelectedHouseholdsCache();\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem?.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  /**\n   * 複製當前表單到新表單\n   */\n  copyToNewForm() {\n    // 先取得當前有效的材料清單\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        // 建立有效材料清單的鍵值對應\n        const validMaterialKeys = new Set();\n        res.Entries.forEach(material => {\n          const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\n          validMaterialKeys.add(key);\n        });\n        // 篩選出仍然有效的表單項目\n        const validFormItems = this.arrListFormItemReq.filter(item => {\n          const itemKey = `${item.CLocation}_${item.CName}_${item.CPart}`;\n          return validMaterialKeys.has(itemKey);\n        });\n        if (validFormItems.length === 0) {\n          this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\n          return;\n        }\n        // 準備複製的表單項目數據\n        this.saveListFormItemReq = validFormItems.map(e => {\n          return {\n            CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n            CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n            CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n            CFormItemId: null,\n            // 設為 null 以建立新項目\n            CFormID: null,\n            // 設為 null 以建立新表單\n            CName: e.CName,\n            CPart: e.CPart,\n            CLocation: e.CLocation,\n            CItemName: e.CItemName,\n            CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n            CTotalAnswer: e.CTotalAnswer,\n            CRequireAnswer: e.CRequireAnswer,\n            CUiType: e.selectedCUiType.value\n          };\n        });\n        // 執行驗證\n        this.validation();\n        if (this.valid.errorMessages.length > 0) {\n          this.message.showErrorMSGs(this.valid.errorMessages);\n          return;\n        }\n        // 建立複製的表單\n        this.creatListFormItem = {\n          CBuildCaseId: this.buildCaseId,\n          CFormItem: this.saveListFormItemReq || null,\n          CFormType: this.typeContentManagementSalesAccount.CFormType\n        };\n        this._formItemService.apiFormItemCreateListFormItemPost$Json({\n          body: this.creatListFormItem\n        }).subscribe(createRes => {\n          if (createRes.StatusCode == 0) {\n            this.message.showSucessMSG(`複製表單成功，已篩選 ${validFormItems.length} 個有效項目`);\n            // 重新載入資料以顯示新的未鎖定表單\n            this.getListFormItem();\n          }\n        });\n      } else {\n        this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\n      }\n    })).subscribe();\n  }\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\n  loadBuildingDataFromAPI() {\n    if (!this.buildCaseId) return;\n    this._houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('GetDropDown API response:', response);\n        if (response.Entries) {\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n          console.log('Converted buildingData:', this.buildingData);\n        }\n      },\n      error: error => {\n        console.error('Error loading building data from API:', error);\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\n        }\n      }\n    });\n  }\n  // 新增：將 API 回應轉換為建築物資料格式\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        code: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseName: house.HouseName,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  // 新增：將戶別清單轉換為建築物資料格式\n  convertHouseHoldListToBuildingData(houseHoldList) {\n    if (!houseHoldList || houseHoldList.length === 0) {\n      return {};\n    }\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\n    const buildingData = {};\n    houseHoldList.forEach(household => {\n      // 嘗試從戶別名稱中提取建築物代碼\n      const buildingMatch = household.match(/^([A-Z]+)/);\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\n      if (!buildingData[building]) {\n        buildingData[building] = [];\n      }\n      // 計算樓層（假設每4戶為一層）\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\n      const floor = Math.ceil(houseNumber / 4);\n      buildingData[building].push({\n        code: household,\n        building: building,\n        floor: `${floor}F`,\n        isSelected: false,\n        isDisabled: false\n      });\n    });\n    return buildingData;\n  }\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        // 載入建築物資料 (只呼叫一次 GetDropDown API)\n        this.loadBuildingDataFromAPI();\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n};\nDetailContentManagementSalesAccountComponent = __decorate([Component({\n  selector: 'ngx-detail-content-management-sales-account',\n  templateUrl: './detail-content-management-sales-account.component.html',\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, AppSharedModule, NbCheckboxModule, Base64ImagePipe],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], DetailContentManagementSalesAccountComponent);\nexport { DetailContentManagementSalesAccountComponent };", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "CommonModule", "NbCheckboxModule", "tap", "SharedModule", "AppSharedModule", "BaseComponent", "Base64ImagePipe", "EnumHouseType", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "_houseCustomService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "cNoticeTypeOptions", "label", "value", "地主戶", "銷售戶", "CUiTypeOptions", "CRemarkTypeOptions", "selectedItems", "selectedRemarkType", "buildingData", "listFormItem", "isNew", "dynamicTitle", "option", "find", "setCNoticeType", "noticeType", "some", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "getListRegularNoticeFileHouseHold", "console", "error", "showErrorMSG", "goBack", "queryParams", "houseType", "loadBuildingDataFromAPI", "ngOnDestroy", "document", "body", "style", "overflow", "getItemByValue", "options", "item", "detectFiles", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "listPictures", "length", "Date", "getTime", "name", "split", "data", "extension", "getFileExtension", "CFile", "push", "removeImage", "pictureId", "filter", "x", "renameFile", "index", "blob", "slice", "size", "type", "newFile", "File", "nextImage", "formItemReq", "CMatrialUrl", "currentImageIndex", "prevImage", "getCurrentImage", "undefined", "openImageModal", "imageIndex", "isModalOpen", "closeImageModal", "nextImageModal", "prevImageModal", "onKeydown", "key", "preventDefault", "extractHouseholdCodes", "households", "Array", "isArray", "map", "h", "code", "onHouseholdSelectionChange", "selectedHouseholds", "Object", "keys", "for<PERSON>ach", "household", "allSelected", "houseHoldList", "every", "updateSelectedHouseholdsCache", "getSelectedHouseholds", "selectedHouseholdsCached", "log", "updateAllSelectedHouseholdsCache", "arrListFormItemReq", "onCheckboxRemarkChange", "checked", "createRemarkObject", "CRemarkType", "remarkObject", "remarkTypes", "includes", "mergeItems", "items", "Map", "CLocation", "CName", "<PERSON>art", "has", "existing", "count", "set", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "o", "CDesignFileUrl", "CFormItemHouseHold", "CFormId", "CItemName", "CRequireAnswer", "CUiType", "selectedCUiType", "CSelectPicture", "x1", "CBase64", "url", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFirstMatrialUrl", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "changeSelectCUiType", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "bItem", "CHousehold", "CIsSelect", "copyToNewForm", "validMaterialKeys", "Set", "material", "add", "validFormItems", "itemKey", "createRes", "getDropDown", "next", "response", "convertApiResponseToBuildingData", "convertHouseHoldListToBuildingData", "entries", "building", "houses", "house", "HouseName", "Building", "floor", "Floor", "houseId", "HouseId", "houseName", "isSelected", "isDisabled", "buildingMatch", "match", "houseNumber", "parseInt", "replace", "Math", "ceil", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { HouseCustomService } from 'src/services/api/services/HouseCustom.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq, GetMaterialListResponse } from 'src/services/api/models';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n  selectedHouseholdsCached?: string[]; // 緩存已選戶別，避免重複計算\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, AppSharedModule, NbCheckboxModule, Base64ImagePipe],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit, OnDestroy {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService,\r\n    private _houseCustomService: HouseCustomService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n  // 通知類型選項映射\r\n  cNoticeTypeOptions = [\r\n    { label: '地主戶', value: EnumHouseType.地主戶 },\r\n    { label: '銷售戶', value: EnumHouseType.銷售戶 }\r\n  ];\r\n  // 動態獲取標題文字\r\n  get dynamicTitle(): string {\r\n    const option = this.cNoticeTypeOptions.find(option =>\r\n      option.value === this.typeContentManagementSalesAccount.CNoticeType\r\n    );\r\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\r\n  }\r\n  // 設置通知類型（可供外部調用）\r\n  setCNoticeType(noticeType: EnumHouseType): void {\r\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\r\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\r\n      // 同時設定 CFormType 以保持一致性\r\n      this.typeContentManagementSalesAccount.CFormType = noticeType;\r\n    }\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }];\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"];\r\n  buildCaseId: number;\r\n\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n\r\n        if (this.buildCaseId > 0) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        } else {\r\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\r\n          console.error('Invalid buildCaseId:', this.buildCaseId);\r\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\r\n          this.goBack();\r\n        }\r\n      }\r\n    });\r\n\r\n    // 處理查詢參數中的戶型\r\n    this.route.queryParams.subscribe(queryParams => {\r\n      if (queryParams['houseType']) {\r\n        const houseType = +queryParams['houseType'];\r\n        this.setCNoticeType(houseType);\r\n      }\r\n    });\r\n\r\n    // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\r\n    this.loadBuildingDataFromAPI();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保在組件銷毀時恢復body的滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n  // 新增：戶別選擇器相關屬性\r\n  buildingData: any = {}; // 存放建築物戶別資料\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      formItemReq.isModalOpen = true;\r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\r\n  extractHouseholdCodes(households: any[]): string[] {\r\n    if (!households || !Array.isArray(households)) {\r\n      return [];\r\n    }\r\n    return households.map(h => h.code || h);\r\n  }\r\n  // 新增：處理戶別選擇變更\r\n  onHouseholdSelectionChange(selectedHouseholds: string[], formItemReq: any) {\r\n    // 重置所有戶別選擇狀態\r\n    Object.keys(formItemReq.selectedItems).forEach(key => {\r\n      formItemReq.selectedItems[key] = false;\r\n    });\r\n\r\n    // 設置選中的戶別\r\n    selectedHouseholds.forEach(household => {\r\n      formItemReq.selectedItems[household] = true;\r\n    });\r\n\r\n    // 更新全選狀態\r\n    formItemReq.allSelected = this.houseHoldList.length > 0 &&\r\n      this.houseHoldList.every(item => formItemReq.selectedItems[item]);\r\n\r\n    // 更新緩存\r\n    this.updateSelectedHouseholdsCache(formItemReq);\r\n  }\r\n\r\n  // 新增：取得已選戶別數組\r\n  getSelectedHouseholds(formItemReq: any): string[] {\r\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\r\n  }\r\n\r\n  // 新增：更新已選戶別緩存\r\n  private updateSelectedHouseholdsCache(formItemReq: any): void {\r\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\r\n    console.log('Updated selected households cache:', formItemReq.selectedHouseholdsCached);\r\n\r\n  }\r\n\r\n  // 新增：更新所有項目的緩存\r\n  private updateAllSelectedHouseholdsCache(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(formItemReq => {\r\n        this.updateSelectedHouseholdsCache(formItemReq);\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: GetMaterialListResponse) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0, selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [], selectedCUiType: this.CUiTypeOptions[0],\r\n              currentImageIndex: 0,\r\n              isModalOpen: false,\r\n              CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64).filter((url: any) => url != null) as string[] : []\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes | null = null\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem?.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [], selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0,\r\n                isModalOpen: false,\r\n                selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\r\n              }\r\n            })\r\n          } else {\r\n            // 當無資料時，載入材料清單供新增使用\r\n            this.getMaterialList();\r\n          }\r\n\r\n          // 初始化所有項目的緩存\r\n          this.updateAllSelectedHouseholdsCache();\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem?.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n  /**\r\n   * 複製當前表單到新表單\r\n   */\r\n  copyToNewForm() {\r\n    // 先取得當前有效的材料清單\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          // 建立有效材料清單的鍵值對應\r\n          const validMaterialKeys = new Set<string>();\r\n          res.Entries.forEach((material: any) => {\r\n            const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\r\n            validMaterialKeys.add(key);\r\n          });\r\n\r\n          // 篩選出仍然有效的表單項目\r\n          const validFormItems = this.arrListFormItemReq.filter((item: any) => {\r\n            const itemKey = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n            return validMaterialKeys.has(itemKey);\r\n          });\r\n\r\n          if (validFormItems.length === 0) {\r\n            this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\r\n            return;\r\n          }\r\n\r\n          // 準備複製的表單項目數據\r\n          this.saveListFormItemReq = validFormItems.map((e: any) => {\r\n            return {\r\n              CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n              CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n              CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n              CFormItemId: null, // 設為 null 以建立新項目\r\n              CFormID: null, // 設為 null 以建立新表單\r\n              CName: e.CName,\r\n              CPart: e.CPart,\r\n              CLocation: e.CLocation,\r\n              CItemName: e.CItemName,\r\n              CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n              CTotalAnswer: e.CTotalAnswer,\r\n              CRequireAnswer: e.CRequireAnswer,\r\n              CUiType: e.selectedCUiType.value,\r\n            }\r\n          });\r\n\r\n          // 執行驗證\r\n          this.validation()\r\n          if (this.valid.errorMessages.length > 0) {\r\n            this.message.showErrorMSGs(this.valid.errorMessages);\r\n            return\r\n          }\r\n\r\n          // 建立複製的表單\r\n          this.creatListFormItem = {\r\n            CBuildCaseId: this.buildCaseId,\r\n            CFormItem: this.saveListFormItemReq || null,\r\n            CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n          }\r\n\r\n          this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n            body: this.creatListFormItem\r\n          }).subscribe(createRes => {\r\n            if (createRes.StatusCode == 0) {\r\n              this.message.showSucessMSG(`複製表單成功，已篩選 ${validFormItems.length} 個有效項目`);\r\n              // 重新載入資料以顯示新的未鎖定表單\r\n              this.getListFormItem()\r\n            }\r\n          })\r\n        } else {\r\n          this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\r\n  private loadBuildingDataFromAPI(): void {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this._houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('GetDropDown API response:', response);\r\n        if (response.Entries) {\r\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n          console.log('Converted buildingData:', this.buildingData);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading building data from API:', error);\r\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\r\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\r\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：將 API 回應轉換為建築物資料格式\r\n  private convertApiResponseToBuildingData(entries: any): any {\r\n    const buildingData: any = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]: [string, any]) => {\r\n      buildingData[building] = houses.map((house: any) => ({\r\n        code: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseName: house.HouseName,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  // 新增：將戶別清單轉換為建築物資料格式\r\n  convertHouseHoldListToBuildingData(houseHoldList: string[]): any {\r\n    if (!houseHoldList || houseHoldList.length === 0) {\r\n      return {};\r\n    }\r\n\r\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\r\n    const buildingData: any = {};\r\n\r\n    houseHoldList.forEach(household => {\r\n      // 嘗試從戶別名稱中提取建築物代碼\r\n      const buildingMatch = household.match(/^([A-Z]+)/);\r\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\r\n\r\n      if (!buildingData[building]) {\r\n        buildingData[building] = [];\r\n      }\r\n\r\n      // 計算樓層（假設每4戶為一層）\r\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\r\n      const floor = Math.ceil(houseNumber / 4);\r\n\r\n      buildingData[building].push({\r\n        code: household,\r\n        building: building,\r\n        floor: `${floor}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      });\r\n    }); return buildingData;\r\n  }\r\n\r\n  houseHoldList: any[];\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n\r\n          // 載入建築物資料 (只呼叫一次 GetDropDown API)\r\n          this.loadBuildingDataFromAPI();\r\n\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAqBC,uBAAuB,QAAQ,eAAe;AACrF,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAMjD,SAASC,GAAG,QAAQ,MAAM;AAI1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASA,YAAY,IAAIC,eAAe,QAAQ,8BAA8B;AAC9E,SAASC,aAAa,QAAQ,6CAA6C;AAE3E,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,aAAa,QAAQ,mCAAmC;AAqC1D,IAAMC,4CAA4C,GAAlD,MAAMA,4CAA6C,SAAQH,aAAa;EAC7EI,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B,EAC3BC,mBAAuC;IAE/C,KAAK,CAACV,MAAM,CAAC;IAZL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAI7B,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IACD;IACA,KAAAC,kBAAkB,GAAG,CACnB;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAEnB,aAAa,CAACoB;IAAG,CAAE,EAC1C;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAEnB,aAAa,CAACqB;IAAG,CAAE,CAC3C;IAiBD,KAAAC,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,EACD;MACEC,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,EAAE;MACDC,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,CAAC;IACJ,KAAAK,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAgDjC,KAAAC,aAAa,GAA+B,EAAE;IAC9C,KAAAC,kBAAkB,GAA+B,EAAE;IAEnD;IACA,KAAAC,YAAY,GAAQ,EAAE,CAAC,CAAC;IA+OxB,KAAAC,YAAY,GAA8B,IAAI;IAC9C,KAAAC,KAAK,GAAY,IAAI;EAvUrB;EAUA;EACA,IAAIC,YAAYA,CAAA;IACd,MAAMC,MAAM,GAAG,IAAI,CAACb,kBAAkB,CAACc,IAAI,CAACD,MAAM,IAChDA,MAAM,CAACX,KAAK,KAAK,IAAI,CAACL,iCAAiC,CAACE,WAAW,CACpE;IACD,OAAOc,MAAM,GAAG,QAAQA,MAAM,CAACZ,KAAK,EAAE,GAAG,WAAW;EACtD;EACA;EACAc,cAAcA,CAACC,UAAyB;IACtC,IAAI,IAAI,CAAChB,kBAAkB,CAACiB,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAACX,KAAK,KAAKc,UAAU,CAAC,EAAE;MACvE,IAAI,CAACnB,iCAAiC,CAACE,WAAW,GAAGiB,UAAU;MAC/D;MACA,IAAI,CAACnB,iCAAiC,CAACC,SAAS,GAAGkB,UAAU;IAC/D;EACF;EAeSE,QAAQA,CAAA;IACf,IAAI,CAAC/B,KAAK,CAACgC,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QAErB,IAAI,IAAI,CAACC,WAAW,GAAG,CAAC,EAAE;UACxB,IAAI,CAACC,iCAAiC,EAAE;QAC1C,CAAC,MAAM;UACL;UACAC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAACH,WAAW,CAAC;UACvD,IAAI,CAACrC,OAAO,CAACyC,YAAY,CAAC,iBAAiB,CAAC;UAC5C,IAAI,CAACC,MAAM,EAAE;QACf;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAC3C,KAAK,CAAC4C,WAAW,CAACX,SAAS,CAACW,WAAW,IAAG;MAC7C,IAAIA,WAAW,CAAC,WAAW,CAAC,EAAE;QAC5B,MAAMC,SAAS,GAAG,CAACD,WAAW,CAAC,WAAW,CAAC;QAC3C,IAAI,CAAChB,cAAc,CAACiB,SAAS,CAAC;MAChC;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAC,cAAcA,CAACrC,KAAU,EAAEsC,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAACvC,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOuC,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQAC,WAAWA,CAACC,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAACU,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UACxCX,YAAY,CAACU,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7B9B,EAAE,EAAE,IAAIgC,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAACtE,eAAe,CAACuE,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAACU,YAAY,CAACU,IAAI,CAAC;YAC7BxC,EAAE,EAAE,IAAIgC,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAACtE,eAAe,CAACuE,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAAC5C,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEA+D,WAAWA,CAACC,SAAiB,EAAEtB,YAAiB;IAC9C,IAAIA,YAAY,CAACU,YAAY,CAACC,MAAM,EAAE;MACpCX,YAAY,CAACU,YAAY,GAAGV,YAAY,CAACU,YAAY,CAACa,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC5C,EAAE,IAAI0C,SAAS,CAAC;IAC7F;EACF;EACAG,UAAUA,CAAC1B,KAAU,EAAE2B,KAAa,EAAE1B,YAAiB;IACrD,IAAI2B,IAAI,GAAG3B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACS,KAAK,CAAC,CAAC,EAAE5B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACU,IAAI,EAAE7B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAG5B,KAAK,CAACG,MAAM,CAAC5C,KAAK,GAAG,GAAG,GAAG0C,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACT,SAAS,EAAE,EAAE;MAAEa,IAAI,EAAE9B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW;IAAI,CAAE,CAAC;IACjK9B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,GAAGY,OAAO;EAClD;EAEA;EACAE,SAASA,CAACC,WAAgB;IACxB,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjEuB,WAAW,CAACE,iBAAiB,GAAG,CAACF,WAAW,CAACE,iBAAiB,GAAG,CAAC,IAAIF,WAAW,CAACC,WAAW,CAACxB,MAAM;IACtG;EACF;EAEA0B,SAASA,CAACH,WAAgB;IACxB,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjEuB,WAAW,CAACE,iBAAiB,GAAGF,WAAW,CAACE,iBAAiB,KAAK,CAAC,GAC/DF,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,GAClCuB,WAAW,CAACE,iBAAiB,GAAG,CAAC;IACvC;EACF;EACAE,eAAeA,CAACJ,WAAgB;IAC9B,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,IAAIuB,WAAW,CAACE,iBAAiB,KAAKG,SAAS,EAAE;MAChH,OAAOL,WAAW,CAACC,WAAW,CAACD,WAAW,CAACE,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EAEA;EACAI,cAAcA,CAACN,WAAgB,EAAEO,UAAmB;IAClD,IAAIP,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjE,IAAI8B,UAAU,KAAKF,SAAS,EAAE;QAC5BL,WAAW,CAACE,iBAAiB,GAAGK,UAAU;MAC5C;MACAP,WAAW,CAACQ,WAAW,GAAG,IAAI;MAC9B;MACAnD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;EACF;EAEAiD,eAAeA,CAACT,WAAgB;IAC9BA,WAAW,CAACQ,WAAW,GAAG,KAAK;IAC/B;IACAnD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACAkD,cAAcA,CAACV,WAAgB;IAC7B,IAAI,CAACD,SAAS,CAACC,WAAW,CAAC;EAC7B;EAEAW,cAAcA,CAACX,WAAgB;IAC7B,IAAI,CAACG,SAAS,CAACH,WAAW,CAAC;EAC7B;EAEA;EACAY,SAASA,CAAC/C,KAAoB,EAAEmC,WAAgB;IAC9C,IAAIA,WAAW,CAACQ,WAAW,EAAE;MAC3B,QAAQ3C,KAAK,CAACgD,GAAG;QACf,KAAK,WAAW;UACdhD,KAAK,CAACiD,cAAc,EAAE;UACtB,IAAI,CAACH,cAAc,CAACX,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACfnC,KAAK,CAACiD,cAAc,EAAE;UACtB,IAAI,CAACJ,cAAc,CAACV,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACXnC,KAAK,CAACiD,cAAc,EAAE;UACtB,IAAI,CAACL,eAAe,CAACT,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEA;EACAe,qBAAqBA,CAACC,UAAiB;IACrC,IAAI,CAACA,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;IACX;IACA,OAAOA,UAAU,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,IAAID,CAAC,CAAC;EACzC;EACA;EACAE,0BAA0BA,CAACC,kBAA4B,EAAEvB,WAAgB;IACvE;IACAwB,MAAM,CAACC,IAAI,CAACzB,WAAW,CAACvE,aAAa,CAAC,CAACiG,OAAO,CAACb,GAAG,IAAG;MACnDb,WAAW,CAACvE,aAAa,CAACoF,GAAG,CAAC,GAAG,KAAK;IACxC,CAAC,CAAC;IAEF;IACAU,kBAAkB,CAACG,OAAO,CAACC,SAAS,IAAG;MACrC3B,WAAW,CAACvE,aAAa,CAACkG,SAAS,CAAC,GAAG,IAAI;IAC7C,CAAC,CAAC;IAEF;IACA3B,WAAW,CAAC4B,WAAW,GAAG,IAAI,CAACC,aAAa,CAACpD,MAAM,GAAG,CAAC,IACrD,IAAI,CAACoD,aAAa,CAACC,KAAK,CAACnE,IAAI,IAAIqC,WAAW,CAACvE,aAAa,CAACkC,IAAI,CAAC,CAAC;IAEnE;IACA,IAAI,CAACoE,6BAA6B,CAAC/B,WAAW,CAAC;EACjD;EAEA;EACAgC,qBAAqBA,CAAChC,WAAgB;IACpC,OAAOwB,MAAM,CAACC,IAAI,CAACzB,WAAW,CAACvE,aAAa,CAAC,CAAC4D,MAAM,CAACwB,GAAG,IAAIb,WAAW,CAACvE,aAAa,CAACoF,GAAG,CAAC,CAAC;EAC7F;EAEA;EACQkB,6BAA6BA,CAAC/B,WAAgB;IACpDA,WAAW,CAACiC,wBAAwB,GAAG,IAAI,CAACD,qBAAqB,CAAChC,WAAW,CAAC;IAC9EnD,OAAO,CAACqF,GAAG,CAAC,oCAAoC,EAAElC,WAAW,CAACiC,wBAAwB,CAAC;EAEzF;EAEA;EACQE,gCAAgCA,CAAA;IACtC,IAAI,IAAI,CAACC,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACV,OAAO,CAAC1B,WAAW,IAAG;QAC5C,IAAI,CAAC+B,6BAA6B,CAAC/B,WAAW,CAAC;MACjD,CAAC,CAAC;IACJ;EACF;EAIAqC,sBAAsBA,CAACC,OAAgB,EAAE3E,IAAY,EAAEG,YAAiB;IACtEA,YAAY,CAACpC,kBAAkB,CAACiC,IAAI,CAAC,GAAG2E,OAAO;EACjD;EAEAC,kBAAkBA,CAAC/G,kBAA4B,EAAEgH,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAM1G,MAAM,IAAIP,kBAAkB,EAAE;MACvCiH,YAAY,CAAC1G,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAM2G,WAAW,GAAGF,WAAW,CAAC3D,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMe,IAAI,IAAI8C,WAAW,EAAE;MAC9B,IAAIlH,kBAAkB,CAACmH,QAAQ,CAAC/C,IAAI,CAAC,EAAE;QACrC6C,YAAY,CAAC7C,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAO6C,YAAY;EACrB;EAEAG,UAAUA,CAACC,KAAoC;IAC7C,MAAM1B,GAAG,GAAG,IAAI2B,GAAG,EAAgE;IAEnFD,KAAK,CAACnB,OAAO,CAAC/D,IAAI,IAAG;MACnB,MAAMkD,GAAG,GAAG,GAAGlD,IAAI,CAACoF,SAAS,IAAIpF,IAAI,CAACqF,KAAK,IAAIrF,IAAI,CAACsF,KAAK,EAAE;MAC3D,IAAI9B,GAAG,CAAC+B,GAAG,CAACrC,GAAG,CAAC,EAAE;QAChB,MAAMsC,QAAQ,GAAGhC,GAAG,CAAC1E,GAAG,CAACoE,GAAG,CAAE;QAC9BsC,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLjC,GAAG,CAACkC,GAAG,CAACxC,GAAG,EAAE;UAAElD,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAEyF,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOnC,KAAK,CAACqC,IAAI,CAACnC,GAAG,CAACoC,MAAM,EAAE,CAAC,CAACpC,GAAG,CAAC,CAAC;MAAExD,IAAI;MAAEyF;IAAK,CAAE,MAAM;MACxD,GAAGzF,IAAI;MACP6F,YAAY,EAAEJ;KACf,CAAC,CAAC;EACL;EAGAK,eAAeA,CAAA;IACb,IAAI,CAAC7I,gBAAgB,CAAC8I,mCAAmC,CAAC;MACxDpG,IAAI,EAAE;QACJqG,YAAY,EAAE,IAAI,CAAChH,WAAW;QAC9BiH,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLjK,GAAG,CAACkK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAGtC,IAAI,CAAC5B,kBAAkB,GAAG0B,GAAG,CAACC,OAAO,CAAC5C,GAAG,CAAE8C,CAA0B,IAAI;UACvE,OAAO;YACLC,cAAc,EAAE,IAAI;YACpBC,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbrB,SAAS,EAAEkB,CAAC,CAAClB,SAAS;YACtBC,KAAK,EAAEiB,CAAC,CAACjB,KAAK;YACdC,KAAK,EAAEgB,CAAC,CAAChB,KAAK;YACdoB,SAAS,EAAE,GAAGJ,CAAC,CAACjB,KAAK,IAAIiB,CAAC,CAAChB,KAAK,IAAIgB,CAAC,CAAClB,SAAS,EAAE;YACjDP,WAAW,EAAE,IAAI;YACjBgB,YAAY,EAAE,CAAC;YACfc,cAAc,EAAE,CAAC;YACjBC,OAAO,EAAE,CAAC;YAAE9I,aAAa,EAAE,EAAE;YAC7BC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3CkG,WAAW,EAAE,KAAK;YAClBpD,YAAY,EAAE,EAAE;YAAEgG,eAAe,EAAE,IAAI,CAACjJ,cAAc,CAAC,CAAC,CAAC;YACzD2E,iBAAiB,EAAE,CAAC;YACpBM,WAAW,EAAE,KAAK;YAClBP,WAAW,EAAEgE,CAAC,CAACQ,cAAc,GAAGR,CAAC,CAACQ,cAAc,CAACtD,GAAG,CAACuD,EAAE,IAAIA,EAAE,CAACC,OAAO,CAAC,CAACtF,MAAM,CAAEuF,GAAQ,IAAKA,GAAG,IAAI,IAAI,CAAa,GAAG;WACxH;QACH,CAAC,CAAC;QACF,IAAI,CAACxC,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACQ,UAAU,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAAC9F,SAAS,EAAE;EACf;EAKAuI,eAAeA,CAAA;IACb,IAAI,CAACtK,gBAAgB,CAACuK,mCAAmC,CAAC;MACxDxH,IAAI,EAAE;QACJqG,YAAY,EAAE,IAAI,CAAChH,WAAW;QAC9B3B,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3D+J,SAAS,EAAE;;KAEd,CAAC,CAAClB,IAAI,CACLjK,GAAG,CAACkK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACpI,YAAY,GAAGkI,GAAG,CAACC,OAAO;QAC/B,IAAI,CAAClI,KAAK,GAAGiI,GAAG,CAACC,OAAO,CAACiB,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIlB,GAAG,CAACC,OAAO,CAACiB,SAAS,EAAE;UACzB,IAAI,CAACnD,aAAa,CAACH,OAAO,CAAC/D,IAAI,IAAI,IAAI,CAAClC,aAAa,CAACkC,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAACnC,kBAAkB,CAACkG,OAAO,CAAC/D,IAAI,IAAI,IAAI,CAACjC,kBAAkB,CAACiC,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAACyE,kBAAkB,GAAG0B,GAAG,CAACC,OAAO,CAACiB,SAAS,CAAC7D,GAAG,CAAE8C,CAAM,IAAI;YAC7D,OAAO;cACLG,OAAO,EAAE,IAAI,CAACxI,YAAY,EAAEwI,OAAO;cACnCF,cAAc,EAAED,CAAC,CAACC,cAAc;cAChCjE,WAAW,EAAEgE,CAAC,CAAChE,WAAW,KAAKgE,CAAC,CAACgB,gBAAgB,GAAG,CAAChB,CAAC,CAACgB,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9EhG,KAAK,EAAEgF,CAAC,CAAChF,KAAK;cACdkF,kBAAkB,EAAEF,CAAC,CAACE,kBAAkB;cACxCe,WAAW,EAAEjB,CAAC,CAACiB,WAAW;cAC1BnC,SAAS,EAAEkB,CAAC,CAAClB,SAAS;cACtBC,KAAK,EAAEiB,CAAC,CAACjB,KAAK;cACdC,KAAK,EAAEgB,CAAC,CAAChB,KAAK;cACdoB,SAAS,EAAEJ,CAAC,CAACI,SAAS,GAAGJ,CAAC,CAACI,SAAS,GAAG,GAAGJ,CAAC,CAACjB,KAAK,IAAIiB,CAAC,CAAChB,KAAK,IAAIgB,CAAC,CAAClB,SAAS,EAAE;cAC7EP,WAAW,EAAEyB,CAAC,CAACzB,WAAW;cAC1BgB,YAAY,EAAES,CAAC,CAACT,YAAY;cAC5Bc,cAAc,EAAEL,CAAC,CAACM,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGN,CAAC,CAACK,cAAc;cACtDC,OAAO,EAAEN,CAAC,CAACM,OAAO;cAClB9I,aAAa,EAAEwI,CAAC,CAACkB,qBAAqB,CAAC1G,MAAM,GAAG,IAAI,CAAC2G,0BAA0B,CAAC,IAAI,CAACvD,aAAa,EAAEoC,CAAC,CAACkB,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC1J;cAAa,CAAE;cAAEC,kBAAkB,EAAEuI,CAAC,CAACzB,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC/G,kBAAkB,EAAEyI,CAAC,CAACzB,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC9G;cAAkB,CAAE;cAC9RkG,WAAW,EAAEqC,CAAC,CAACkB,qBAAqB,CAAC1G,MAAM,KAAK,IAAI,CAACoD,aAAa,CAACpD,MAAM;cACzED,YAAY,EAAE,EAAE;cAAEgG,eAAe,EAAEP,CAAC,CAACM,OAAO,GAAG,IAAI,CAAC9G,cAAc,CAACwG,CAAC,CAACM,OAAO,EAAE,IAAI,CAAChJ,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cAC3H2E,iBAAiB,EAAE,CAAC;cACpBM,WAAW,EAAE,KAAK;cAClByB,wBAAwB,EAAE,EAAE,CAAC;aAC9B;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACA,IAAI,CAACwB,eAAe,EAAE;QACxB;QAEA;QACA,IAAI,CAACtB,gCAAgC,EAAE;MACzC;IACF,CAAC,CAAC,CACH,CAAC7F,SAAS,EAAE;EACf;EAEA+I,mBAAmBA,CAACrF,WAAgB;IAClC,IAAIA,WAAW,CAACwE,eAAe,IAAIxE,WAAW,CAACwE,eAAe,CAACpJ,KAAK,KAAK,CAAC,EAAE;MAC1E4E,WAAW,CAACsE,cAAc,GAAG,CAAC;IAChC;EACF;EACAgB,4BAA4BA,CAACxG,IAAW;IACtC,KAAK,IAAInB,IAAI,IAAImB,IAAI,EAAE;MACrB,IAAInB,IAAI,CAAC1C,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAO0C,IAAI,CAAC4H,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOjE,MAAM,CAACC,IAAI,CAACgE,GAAG,CAAC,CAACpG,MAAM,CAACwB,GAAG,IAAI4E,GAAG,CAAC5E,GAAG,CAAC,CAAC;EACjD;EAEA6E,0BAA0BA,CAACD,GAA4B;IACrD,OAAOjE,MAAM,CAACC,IAAI,CAACgE,GAAG,CAAC,CACpBpG,MAAM,CAACwB,GAAG,IAAI4E,GAAG,CAAC5E,GAAG,CAAC,CAAC,CACvB8E,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACpB,eAAoB,EAAE9I,kBAAuB;IAC1D,IAAI8I,eAAe,IAAIA,eAAe,CAACpJ,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAACsK,0BAA0B,CAAChK,kBAAkB,CAAC;IAC5D;EACF;EAEAmK,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACjH,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIkH,KAAK,CAACtH,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOsH,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAACxH,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACLwH,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAACrH,YAAY,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,IAAI;QACpEoH,aAAa,EAAE1H,YAAY,CAAC,CAAC,CAAC,CAACO,SAAS,IAAI,IAAI;QAChDoH,QAAQ,EAAE3H,YAAY,CAAC,CAAC,CAAC,CAACS,KAAK,CAACL,IAAI,IAAIJ,YAAY,CAAC,CAAC,CAAC,CAACI,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOyB,SAAS;EAEzB;EAGA+F,UAAUA,CAAA;IACR,IAAI,CAAC1L,KAAK,CAAC2L,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAM7I,IAAI,IAAI,IAAI,CAAC8I,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAC3I,IAAI,CAAC4G,OAAQ,EAAE;QACzC+B,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAC5I,IAAI,CAAC2G,cAAe,EAAE;QACvDiC,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAI5I,IAAI,CAAC6F,YAAY,IAAI7F,IAAI,CAAC2G,cAAc,EAAE;QAC5C,IAAI3G,IAAI,CAAC2G,cAAc,GAAG3G,IAAI,CAAC6F,YAAY,IAAI7F,IAAI,CAAC2G,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAAC5J,KAAK,CAACgM,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAG/I,IAAI,CAAC6F,YAAY,GAAG,KAAK7F,IAAI,CAAC0G,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACmC,kBAAkB,IAAK,CAAC7I,IAAI,CAAC0G,SAAU,EAAE;QAC5CmC,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAAC5L,KAAK,CAACgM,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAC7L,KAAK,CAACgM,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAC9L,KAAK,CAACgM,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACrE,kBAAkB,CAACjB,GAAG,CAAEyF,CAAM,IAAI;MAChE,OAAO;QACL1C,cAAc,EAAE0C,CAAC,CAAC1C,cAAc,GAAG0C,CAAC,CAAC1C,cAAc,GAAG,IAAI;QAC1DjF,KAAK,EAAE2H,CAAC,CAACpI,YAAY,GAAG,IAAI,CAACwH,UAAU,CAACY,CAAC,CAACpI,YAAY,CAAC,GAAG6B,SAAS;QACnE8D,kBAAkB,EAAE,IAAI,CAACqB,oBAAoB,CAACoB,CAAC,CAACnL,aAAa,CAAC;QAC9DyJ,WAAW,EAAE0B,CAAC,CAAC1B,WAAW,GAAG0B,CAAC,CAAC1B,WAAW,GAAG,IAAI;QACjD2B,OAAO,EAAE,IAAI,CAAChL,KAAK,GAAG,IAAI,GAAG,IAAI,CAACD,YAAY,EAAEwI,OAAO;QACvDpB,KAAK,EAAE4D,CAAC,CAAC5D,KAAK;QACdC,KAAK,EAAE2D,CAAC,CAAC3D,KAAK;QACdF,SAAS,EAAE6D,CAAC,CAAC7D,SAAS;QACtBsB,SAAS,EAAEuC,CAAC,CAACvC,SAAS;QAAE;QACxB7B,WAAW,EAAEoE,CAAC,CAACpC,eAAe,CAACpJ,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACwK,cAAc,CAACgB,CAAC,CAACpC,eAAe,EAAEoC,CAAC,CAAClL,kBAAkB,CAAC,IAAI,IAAI;QACxH8H,YAAY,EAAEoD,CAAC,CAACpD,YAAY;QAC5Bc,cAAc,EAAEsC,CAAC,CAACtC,cAAc;QAChCC,OAAO,EAAEqC,CAAC,CAACpC,eAAe,CAACpJ;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACgL,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC1L,KAAK,CAACoM,aAAa,CAACrI,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACnE,OAAO,CAACyM,aAAa,CAAC,IAAI,CAACrM,KAAK,CAACoM,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAACjL,KAAK,EAAE;MACd,IAAI,CAACmL,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC1M,gBAAgB,CAAC2M,oCAAoC,CAAC;MACzD5J,IAAI,EAAE,IAAI,CAACmJ;KACZ,CAAC,CAACnK,SAAS,CAACwH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC1J,OAAO,CAAC6M,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACnK,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAgK,kBAAkBA,CAAA;IAChB,IAAI,CAACI,iBAAiB,GAAG;MACvBzD,YAAY,EAAE,IAAI,CAAChH,WAAW;MAC9B0K,SAAS,EAAE,IAAI,CAACZ,mBAAmB,IAAI,IAAI;MAC3CzL,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACT,gBAAgB,CAAC+M,sCAAsC,CAAC;MAC3DhK,IAAI,EAAE,IAAI,CAAC8J;KACZ,CAAC,CAAC9K,SAAS,CAACwH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC1J,OAAO,CAAC6M,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACnK,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEAoI,0BAA0BA,CAACmC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAM9J,IAAI,IAAI4J,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACxL,IAAI,CAAC2L,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKjK,IAAI,IAAIgK,KAAK,CAACE,SAAS,CAAC;MAClFJ,CAAC,CAAC9J,IAAI,CAAC,GAAG,CAAC,CAAC+J,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAEF;;;EAGAK,aAAaA,CAAA;IACX;IACA,IAAI,CAAClN,gBAAgB,CAAC8I,mCAAmC,CAAC;MACxDpG,IAAI,EAAE;QACJqG,YAAY,EAAE,IAAI,CAAChH,WAAW;QAC9BiH,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLjK,GAAG,CAACkK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC;QACA,MAAM+D,iBAAiB,GAAG,IAAIC,GAAG,EAAU;QAC3ClE,GAAG,CAACC,OAAO,CAACrC,OAAO,CAAEuG,QAAa,IAAI;UACpC,MAAMpH,GAAG,GAAG,GAAGoH,QAAQ,CAAClF,SAAS,IAAIkF,QAAQ,CAACjF,KAAK,IAAIiF,QAAQ,CAAChF,KAAK,EAAE;UACvE8E,iBAAiB,CAACG,GAAG,CAACrH,GAAG,CAAC;QAC5B,CAAC,CAAC;QAEF;QACA,MAAMsH,cAAc,GAAG,IAAI,CAAC/F,kBAAkB,CAAC/C,MAAM,CAAE1B,IAAS,IAAI;UAClE,MAAMyK,OAAO,GAAG,GAAGzK,IAAI,CAACoF,SAAS,IAAIpF,IAAI,CAACqF,KAAK,IAAIrF,IAAI,CAACsF,KAAK,EAAE;UAC/D,OAAO8E,iBAAiB,CAAC7E,GAAG,CAACkF,OAAO,CAAC;QACvC,CAAC,CAAC;QAEF,IAAID,cAAc,CAAC1J,MAAM,KAAK,CAAC,EAAE;UAC/B,IAAI,CAACnE,OAAO,CAACyC,YAAY,CAAC,eAAe,CAAC;UAC1C;QACF;QAEA;QACA,IAAI,CAAC0J,mBAAmB,GAAG0B,cAAc,CAAChH,GAAG,CAAEyF,CAAM,IAAI;UACvD,OAAO;YACL1C,cAAc,EAAE0C,CAAC,CAAC1C,cAAc,GAAG0C,CAAC,CAAC1C,cAAc,GAAG,IAAI;YAC1DjF,KAAK,EAAE2H,CAAC,CAACpI,YAAY,GAAG,IAAI,CAACwH,UAAU,CAACY,CAAC,CAACpI,YAAY,CAAC,GAAG6B,SAAS;YACnE8D,kBAAkB,EAAE,IAAI,CAACqB,oBAAoB,CAACoB,CAAC,CAACnL,aAAa,CAAC;YAC9DyJ,WAAW,EAAE,IAAI;YAAE;YACnB2B,OAAO,EAAE,IAAI;YAAE;YACf7D,KAAK,EAAE4D,CAAC,CAAC5D,KAAK;YACdC,KAAK,EAAE2D,CAAC,CAAC3D,KAAK;YACdF,SAAS,EAAE6D,CAAC,CAAC7D,SAAS;YACtBsB,SAAS,EAAEuC,CAAC,CAACvC,SAAS;YACtB7B,WAAW,EAAEoE,CAAC,CAACpC,eAAe,CAACpJ,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACwK,cAAc,CAACgB,CAAC,CAACpC,eAAe,EAAEoC,CAAC,CAAClL,kBAAkB,CAAC,IAAI,IAAI;YACxH8H,YAAY,EAAEoD,CAAC,CAACpD,YAAY;YAC5Bc,cAAc,EAAEsC,CAAC,CAACtC,cAAc;YAChCC,OAAO,EAAEqC,CAAC,CAACpC,eAAe,CAACpJ;WAC5B;QACH,CAAC,CAAC;QAEF;QACA,IAAI,CAACgL,UAAU,EAAE;QACjB,IAAI,IAAI,CAAC1L,KAAK,CAACoM,aAAa,CAACrI,MAAM,GAAG,CAAC,EAAE;UACvC,IAAI,CAACnE,OAAO,CAACyM,aAAa,CAAC,IAAI,CAACrM,KAAK,CAACoM,aAAa,CAAC;UACpD;QACF;QAEA;QACA,IAAI,CAACM,iBAAiB,GAAG;UACvBzD,YAAY,EAAE,IAAI,CAAChH,WAAW;UAC9B0K,SAAS,EAAE,IAAI,CAACZ,mBAAmB,IAAI,IAAI;UAC3CzL,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;SACnD;QAED,IAAI,CAACT,gBAAgB,CAAC+M,sCAAsC,CAAC;UAC3DhK,IAAI,EAAE,IAAI,CAAC8J;SACZ,CAAC,CAAC9K,SAAS,CAAC+L,SAAS,IAAG;UACvB,IAAIA,SAAS,CAACrE,UAAU,IAAI,CAAC,EAAE;YAC7B,IAAI,CAAC1J,OAAO,CAAC6M,aAAa,CAAC,cAAcgB,cAAc,CAAC1J,MAAM,QAAQ,CAAC;YACvE;YACA,IAAI,CAACoG,eAAe,EAAE;UACxB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACvK,OAAO,CAACyC,YAAY,CAAC,eAAe,CAAC;MAC5C;IACF,CAAC,CAAC,CACH,CAACT,SAAS,EAAE;EACf;EAEA;EACQa,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACR,WAAW,EAAE;IAEvB,IAAI,CAAC7B,mBAAmB,CAACwN,WAAW,CAAC,IAAI,CAAC3L,WAAW,CAAC,CAACL,SAAS,CAAC;MAC/DiM,IAAI,EAAGC,QAAQ,IAAI;QACjB3L,OAAO,CAACqF,GAAG,CAAC,2BAA2B,EAAEsG,QAAQ,CAAC;QAClD,IAAIA,QAAQ,CAACzE,OAAO,EAAE;UACpB,IAAI,CAACpI,YAAY,GAAG,IAAI,CAAC8M,gCAAgC,CAACD,QAAQ,CAACzE,OAAO,CAAC;UAC3ElH,OAAO,CAACqF,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACvG,YAAY,CAAC;QAC3D;MACF,CAAC;MACDmB,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D;QACA,IAAI,IAAI,CAAC+E,aAAa,IAAI,IAAI,CAACA,aAAa,CAACpD,MAAM,GAAG,CAAC,EAAE;UACvD,IAAI,CAAC9C,YAAY,GAAG,IAAI,CAAC+M,kCAAkC,CAAC,IAAI,CAAC7G,aAAa,CAAC;QACjF;MACF;KACD,CAAC;EACJ;EAEA;EACQ4G,gCAAgCA,CAACE,OAAY;IACnD,MAAMhN,YAAY,GAAQ,EAAE;IAE5B6F,MAAM,CAACmH,OAAO,CAACA,OAAO,CAAC,CAACjH,OAAO,CAAC,CAAC,CAACkH,QAAQ,EAAEC,MAAM,CAAgB,KAAI;MACpElN,YAAY,CAACiN,QAAQ,CAAC,GAAGC,MAAM,CAAC1H,GAAG,CAAE2H,KAAU,KAAM;QACnDzH,IAAI,EAAEyH,KAAK,CAACC,SAAS;QACrBH,QAAQ,EAAEE,KAAK,CAACE,QAAQ;QACxBC,KAAK,EAAEH,KAAK,CAACI,KAAK;QAClBC,OAAO,EAAEL,KAAK,CAACM,OAAO;QACtBC,SAAS,EAAEP,KAAK,CAACC,SAAS;QAC1BO,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO5N,YAAY;EACrB;EAEA;EACA+M,kCAAkCA,CAAC7G,aAAuB;IACxD,IAAI,CAACA,aAAa,IAAIA,aAAa,CAACpD,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,EAAE;IACX;IAEA;IACA,MAAM9C,YAAY,GAAQ,EAAE;IAE5BkG,aAAa,CAACH,OAAO,CAACC,SAAS,IAAG;MAChC;MACA,MAAM6H,aAAa,GAAG7H,SAAS,CAAC8H,KAAK,CAAC,WAAW,CAAC;MAClD,MAAMb,QAAQ,GAAGY,aAAa,GAAG,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM;MAEhE,IAAI,CAAC7N,YAAY,CAACiN,QAAQ,CAAC,EAAE;QAC3BjN,YAAY,CAACiN,QAAQ,CAAC,GAAG,EAAE;MAC7B;MAEA;MACA,MAAMc,WAAW,GAAGC,QAAQ,CAAChI,SAAS,CAACiI,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MAC7D,MAAMX,KAAK,GAAGY,IAAI,CAACC,IAAI,CAACJ,WAAW,GAAG,CAAC,CAAC;MAExC/N,YAAY,CAACiN,QAAQ,CAAC,CAAC1J,IAAI,CAAC;QAC1BmC,IAAI,EAAEM,SAAS;QACfiH,QAAQ,EAAEA,QAAQ;QAClBK,KAAK,EAAE,GAAGA,KAAK,GAAG;QAClBK,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;IAAE,OAAO5N,YAAY;EACzB;EAIAiB,iCAAiCA,CAAA;IAC/B,IAAI,CAACpC,yBAAyB,CAACuP,8DAA8D,CAAC;MAC5FzM,IAAI,EAAE,IAAI,CAACX;KACZ,CAAC,CAACkH,IAAI,CACLjK,GAAG,CAACkK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACnC,aAAa,GAAG,IAAI,CAACyD,4BAA4B,CAACxB,GAAG,CAACC,OAAO,CAAC;QAEnE;QACA,IAAI,CAAC5G,uBAAuB,EAAE;QAE9B,IAAI,CAAC0H,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAACvI,SAAS,EAAE;EACf;EACAU,MAAMA,CAAA;IACJ,IAAI,CAACnC,aAAa,CAACqE,IAAI,CAAC;MACtB8K,MAAM;MACNC,OAAO,EAAE,IAAI,CAACtN;KACf,CAAC;IACF,IAAI,CAAChC,QAAQ,CAACuP,IAAI,EAAE;EACtB;CAED;AA9tBYhQ,4CAA4C,GAAAiQ,UAAA,EATxD3Q,SAAS,CAAC;EACT4Q,QAAQ,EAAE,6CAA6C;EACvDC,WAAW,EAAE,0DAA0D;EACvEC,SAAS,EAAE,CAAC,0DAA0D,CAAC;EACvEC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC9Q,YAAY,EAAEG,YAAY,EAAEC,eAAe,EAAEH,gBAAgB,EAAEK,eAAe,CAAC;EACzFyQ,eAAe,EAAEhR,uBAAuB,CAACiR;CAC1C,CAAC,C,EAEWxQ,4CAA4C,CA8tBxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}