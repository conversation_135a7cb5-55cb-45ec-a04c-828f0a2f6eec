{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_div_12_div_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\\u5DF2\\u9078 \", ctx_r1.selectedHouses.length, \" \\u7B46) \");\n  }\n}\nfunction SettingTimePeriodComponent_div_12_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, SettingTimePeriodComponent_div_12_div_4_span_2_Template, 2, 1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r1.flattenedHouses.length, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵelement(2, \"i\", 54);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SettingTimePeriodComponent_div_12_div_4_Template, 3, 2, \"div\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchQuery.CBuildCaseSelected.CBuildCaseName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.flattenedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r4, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_64_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", floor_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", floor_r6, \"F \");\n  }\n}\nfunction SettingTimePeriodComponent_div_64_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\\u5DF2\\u9078 \", ctx_r1.selectedHouses.length, \" \\u7B46) \");\n  }\n}\nfunction SettingTimePeriodComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"div\", 18)(3, \"nb-form-field\");\n    i0.ɵɵelement(4, \"nb-icon\", 62);\n    i0.ɵɵelementStart(5, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_64_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.filterOptions.searchKeyword, $event) || (ctx_r1.filterOptions.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_64_Template_input_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 22)(7, \"nb-select\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_64_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.filterOptions.statusFilter, $event) || (ctx_r1.filterOptions.statusFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_64_Template_nb_select_selectedChange_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementStart(8, \"nb-option\", 24);\n    i0.ɵɵtext(9, \"\\u5168\\u90E8\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-option\", 65);\n    i0.ɵɵtext(11, \"\\u9032\\u884C\\u4E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-option\", 66);\n    i0.ɵɵtext(13, \"\\u5F85\\u958B\\u653E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-option\", 67);\n    i0.ɵɵtext(15, \"\\u5DF2\\u904E\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-option\", 68);\n    i0.ɵɵtext(17, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-option\", 69);\n    i0.ɵɵtext(19, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 22)(21, \"nb-select\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_64_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.filterOptions.floorFilter, $event) || (ctx_r1.filterOptions.floorFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_64_Template_nb_select_selectedChange_21_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementStart(22, \"nb-option\", 24);\n    i0.ɵɵtext(23, \"\\u5168\\u90E8\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SettingTimePeriodComponent_div_64_nb_option_24_Template, 2, 2, \"nb-option\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 71)(26, \"nb-select\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_64_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.pageSize, $event) || (ctx_r1.pageSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_64_Template_nb_select_selectedChange_26_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageSizeChange());\n    });\n    i0.ɵɵelementStart(27, \"nb-option\", 59);\n    i0.ɵɵtext(28, \"50\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-option\", 59);\n    i0.ɵɵtext(30, \"100\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"nb-option\", 59);\n    i0.ɵɵtext(32, \"200\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-option\", 59);\n    i0.ɵɵtext(34, \"500\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 33)(36, \"div\", 12)(37, \"div\", 73)(38, \"span\", 74);\n    i0.ɵɵtext(39);\n    i0.ɵɵtemplate(40, SettingTimePeriodComponent_div_64_span_40_Template, 2, 1, \"span\", 75);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 76)(42, \"div\", 77)(43, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_64_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setViewMode(\"table\"));\n    });\n    i0.ɵɵelement(44, \"i\", 79);\n    i0.ɵɵtext(45, \" \\u8868\\u683C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_64_Template_button_click_46_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setViewMode(\"card\"));\n    });\n    i0.ɵɵelement(47, \"i\", 80);\n    i0.ɵɵtext(48, \" \\u5361\\u7247 \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.filterOptions.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.filterOptions.statusFilter);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.filterOptions.floorFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.availableFloors);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 50);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 200);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 500);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r1.filteredHouses.length, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedHouses.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r1.viewMode === \"table\")(\"btn-outline-primary\", ctx_r1.viewMode !== \"table\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r1.viewMode === \"card\")(\"btn-outline-primary\", ctx_r1.viewMode !== \"card\");\n  }\n}\nfunction SettingTimePeriodComponent_div_65_tr_40_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 108);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r9.CChangeStartDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_65_tr_40_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 109);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_65_tr_40_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 108);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r9.CChangeEndDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_65_tr_40_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 109);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_65_tr_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_65_tr_40_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const house_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r9.selected, $event) || (house_r9.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_65_tr_40_Template_nb_checkbox_ngModelChange_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtemplate(10, SettingTimePeriodComponent_div_65_tr_40_span_10_Template, 3, 4, \"span\", 104)(11, SettingTimePeriodComponent_div_65_tr_40_span_11_Template, 2, 0, \"span\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtemplate(13, SettingTimePeriodComponent_div_65_tr_40_span_13_Template, 3, 4, \"span\", 104)(14, SettingTimePeriodComponent_div_65_tr_40_span_14_Template, 2, 0, \"span\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\")(16, \"span\", 106);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_tr_40_Template_button_click_19_listener() {\n      const house_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      const dialog_r10 = i0.ɵɵreference(74);\n      return i0.ɵɵresetView(ctx_r1.openModel(dialog_r10, house_r9));\n    });\n    i0.ɵɵelement(20, \"i\", 87);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const house_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"table-row-selected\", house_r9.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r9.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r9.CHouseId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r9.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r9.CBuildingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", house_r9.CFloor, \"F\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", house_r9.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r9.CChangeStartDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", house_r9.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r9.CChangeEndDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r1.getStatusClass(house_r9));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusText(house_r9), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !house_r9.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_65_div_41_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 112)(1, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_div_41_li_9_Template_button_click_1_listener() {\n      const page_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToPage(page_r13));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r13 === ctx_r1.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r13);\n  }\n}\nfunction SettingTimePeriodComponent_div_65_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"nav\")(2, \"ul\", 111)(3, \"li\", 112)(4, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_div_41_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPage(1));\n    });\n    i0.ɵɵtext(5, \"\\u9996\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 112)(7, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_div_41_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.currentPage - 1));\n    });\n    i0.ɵɵtext(8, \"\\u4E0A\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, SettingTimePeriodComponent_div_65_div_41_li_9_Template, 3, 3, \"li\", 114);\n    i0.ɵɵelementStart(10, \"li\", 112)(11, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_div_41_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.currentPage + 1));\n    });\n    i0.ɵɵtext(12, \"\\u4E0B\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\", 112)(14, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_div_41_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.totalPages));\n    });\n    i0.ɵɵtext(15, \"\\u672B\\u9801\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 83)(2, \"div\", 12)(3, \"div\", 84)(4, \"nb-checkbox\", 85);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_65_Template_nb_checkbox_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectAll, $event) || (ctx_r1.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_65_Template_nb_checkbox_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelectAllChange());\n    });\n    i0.ɵɵtext(5, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openBatchSetting());\n    });\n    i0.ɵɵelement(7, \"i\", 87);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.exportData());\n    });\n    i0.ɵɵelement(10, \"i\", 89);\n    i0.ɵɵtext(11, \" \\u532F\\u51FA \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 90);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 91)(15, \"table\", 92)(16, \"thead\", 93)(17, \"tr\")(18, \"th\", 94)(19, \"nb-checkbox\", 85);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_65_Template_nb_checkbox_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectAll, $event) || (ctx_r1.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_65_Template_nb_checkbox_ngModelChange_19_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelectAllChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"th\", 95);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_Template_th_click_20_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sort(\"CHouseHold\"));\n    });\n    i0.ɵɵtext(21, \" \\u6236\\u578B \");\n    i0.ɵɵelement(22, \"i\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 95);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_Template_th_click_23_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sort(\"CBuildingName\"));\n    });\n    i0.ɵɵtext(24, \" \\u68DF\\u5225 \");\n    i0.ɵɵelement(25, \"i\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\", 97);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_Template_th_click_26_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sort(\"CFloor\"));\n    });\n    i0.ɵɵtext(27, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelement(28, \"i\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"th\", 98);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_Template_th_click_29_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sort(\"CChangeStartDate\"));\n    });\n    i0.ɵɵtext(30, \" \\u958B\\u59CB\\u65E5\\u671F \");\n    i0.ɵɵelement(31, \"i\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"th\", 98);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_65_Template_th_click_32_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sort(\"CChangeEndDate\"));\n    });\n    i0.ɵɵtext(33, \" \\u7D50\\u675F\\u65E5\\u671F \");\n    i0.ɵɵelement(34, \"i\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"th\", 99);\n    i0.ɵɵtext(36, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"th\", 100);\n    i0.ɵɵtext(38, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"tbody\");\n    i0.ɵɵtemplate(40, SettingTimePeriodComponent_div_65_tr_40_Template, 21, 15, \"tr\", 101);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(41, SettingTimePeriodComponent_div_65_div_41_Template, 16, 13, \"div\", 102);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectAll);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.selectedHouses.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u6279\\u6B21\\u8A2D\\u5B9A (\", ctx_r1.selectedHouses.length, \") \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.filteredHouses.length === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", (ctx_r1.currentPage - 1) * ctx_r1.pageSize + 1, \" - \", ctx_r1.Math.min(ctx_r1.currentPage * ctx_r1.pageSize, ctx_r1.filteredHouses.length), \" / \\u5171 \", ctx_r1.filteredHouses.length, \" \\u7B46 \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectAll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r1.sortField === \"CHouseHold\" && ctx_r1.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r1.sortField === \"CHouseHold\" && ctx_r1.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r1.sortField === \"CBuildingName\" && ctx_r1.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r1.sortField === \"CBuildingName\" && ctx_r1.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r1.sortField === \"CFloor\" && ctx_r1.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r1.sortField === \"CFloor\" && ctx_r1.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r1.sortField === \"CChangeStartDate\" && ctx_r1.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r1.sortField === \"CChangeStartDate\" && ctx_r1.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r1.sortField === \"CChangeEndDate\" && ctx_r1.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r1.sortField === \"CChangeEndDate\" && ctx_r1.sortDirection === \"desc\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedHouses)(\"ngForTrackBy\", ctx_r1.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_66_div_2_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 129);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r15.CChangeStartDate, \"MM/dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_66_div_2_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 109);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_66_div_2_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 129);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r15.CChangeEndDate, \"MM/dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_66_div_2_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 109);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_66_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"nb-card\", 120)(2, \"nb-card-body\", 121)(3, \"div\", 122)(4, \"div\", 123)(5, \"h6\", 124);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\", 109);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"nb-checkbox\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_66_div_2_Template_nb_checkbox_ngModelChange_9_listener($event) {\n      const house_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r15.selected, $event) || (house_r15.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_66_div_2_Template_nb_checkbox_ngModelChange_9_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 125)(11, \"div\", 126)(12, \"small\", 109);\n    i0.ɵɵtext(13, \"\\u958B\\u59CB\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, SettingTimePeriodComponent_div_66_div_2_span_14_Template, 3, 4, \"span\", 127)(15, SettingTimePeriodComponent_div_66_div_2_span_15_Template, 2, 0, \"span\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 126)(17, \"small\", 109);\n    i0.ɵɵtext(18, \"\\u7D50\\u675F\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, SettingTimePeriodComponent_div_66_div_2_span_19_Template, 3, 4, \"span\", 127)(20, SettingTimePeriodComponent_div_66_div_2_span_20_Template, 2, 0, \"span\", 105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 12)(22, \"span\", 128);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_66_div_2_Template_button_click_24_listener() {\n      const house_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      const dialog_r10 = i0.ɵɵreference(74);\n      return i0.ɵɵresetView(ctx_r1.openModel(dialog_r10, house_r15));\n    });\n    i0.ɵɵelement(25, \"i\", 87);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const house_r15 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", house_r15.selected)(\"disabled\", !house_r15.CHouseId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(house_r15.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", house_r15.CBuildingName, \" - \", house_r15.CFloor, \"F\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r15.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r15.CHouseId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", house_r15.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r15.CChangeStartDate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", house_r15.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r15.CChangeEndDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r1.getStatusClass(house_r15));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusText(house_r15), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !house_r15.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_66_div_3_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 112)(1, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_66_div_3_li_9_Template_button_click_1_listener() {\n      const page_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToPage(page_r18));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r18 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r18 === ctx_r1.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r18);\n  }\n}\nfunction SettingTimePeriodComponent_div_66_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"nav\")(2, \"ul\", 111)(3, \"li\", 112)(4, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_66_div_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPage(1));\n    });\n    i0.ɵɵtext(5, \"\\u9996\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 112)(7, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_66_div_3_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.currentPage - 1));\n    });\n    i0.ɵɵtext(8, \"\\u4E0A\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, SettingTimePeriodComponent_div_66_div_3_li_9_Template, 3, 3, \"li\", 114);\n    i0.ɵɵelementStart(10, \"li\", 112)(11, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_66_div_3_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.currentPage + 1));\n    });\n    i0.ɵɵtext(12, \"\\u4E0B\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\", 112)(14, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_66_div_3_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPage(ctx_r1.totalPages));\n    });\n    i0.ɵɵtext(15, \"\\u672B\\u9801\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116)(1, \"div\", 117);\n    i0.ɵɵtemplate(2, SettingTimePeriodComponent_div_66_div_2_Template, 26, 17, \"div\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_div_66_div_3_Template, 16, 13, \"div\", 102);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedHouses)(\"ngForTrackBy\", ctx_r1.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 109);\n    i0.ɵɵelement(4, \"i\", 131);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\\u5F8C\\u67E5\\u8A62\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 109)(4, \"div\", 132)(5, \"span\", 133);\n    i0.ɵɵtext(6, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 134);\n    i0.ɵɵtext(8, \"\\u8CC7\\u6599\\u8F09\\u5165\\u4E2D\\uFF0C\\u8ACB\\u7A0D\\u5019...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 146);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \\u5DF2\\u9078\\u64C7 \", ctx_r1.selectedHouses.length, \" \\u500B\\u6236\\u5225 \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r1.selectedBuildingForBatch.name, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_div_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 150);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", house_r20.CHouseHold, \" (\", house_r20.CBuildingName, \"-\", house_r20.CFloor, \"F) \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_div_28_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 109);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" ...\\u7B49 \", ctx_r1.selectedHouses.length - 10, \" \\u500B \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 148);\n    i0.ɵɵtemplate(4, SettingTimePeriodComponent_ng_template_69_div_28_span_4_Template, 2, 3, \"span\", 149)(5, SettingTimePeriodComponent_ng_template_69_div_28_span_5_Template, 2, 1, \"span\", 105);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5C07\\u5957\\u7528\\u5230\\u5DF2\\u9078\\u64C7\\u7684 \", ctx_r1.selectedHouses.length, \" \\u500B\\u6236\\u5225\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedHouses.slice(0, 10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedHouses.length > 10);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_div_3_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      const house_r25 = i0.ɵɵrestoreView(_r24).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r25.selected, $event) || (house_r25.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r25 = ctx.$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r25.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r25.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r25.CHouseHold, \" (\", house_r25.CBuildingName, \") \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 155);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_div_3_nb_checkbox_1_Template, 2, 4, \"nb-checkbox\", 156);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", floor_r23.houses);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 153)(1, \"nb-checkbox\", 85);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const floor_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      i0.ɵɵtwoWayBindingSet(floor_r23.selected, $event) || (floor_r23.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      const floor_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onFloorSelectionChange(floor_r23));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_div_3_Template, 2, 1, \"div\", 154);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r23 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", floor_r23.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r23.floorNumber, \"F (\", floor_r23.houses.length, \" \\u6236) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", floor_r23.selected);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_div_29_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 134);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_Template, 4, 4, \"div\", 152);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedBuildingForBatch.floors);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"nb-checkbox\", 85);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_69_div_29_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.batchSettings.applyToAll, $event) || (ctx_r1.batchSettings.applyToAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_69_div_29_div_3_Template, 2, 1, \"div\", 151);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.batchSettings.applyToAll);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u90E8\\u6236\\u5225 (\", ctx_r1.flattenedHouses.length, \" \\u500B) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.batchSettings.applyToAll && ctx_r1.selectedBuildingForBatch);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.selectedHouses.length, \" \\u500B\\u6236\\u5225)\");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 135)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_69_span_3_Template, 2, 1, \"span\", 136)(4, SettingTimePeriodComponent_ng_template_69_span_4_Template, 2, 1, \"span\", 137);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"div\", 138)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n    i0.ɵɵelementStart(9, \"span\", 139);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 26)(12, \"nb-form-field\", 27);\n    i0.ɵɵelement(13, \"nb-icon\", 28);\n    i0.ɵɵelementStart(14, \"input\", 140);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_69_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.batchSettings.startDate, $event) || (ctx_r1.batchSettings.startDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 30, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 31);\n    i0.ɵɵtext(18, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\", 27);\n    i0.ɵɵelement(20, \"nb-icon\", 28);\n    i0.ɵɵelementStart(21, \"input\", 140);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_69_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.batchSettings.endDate, $event) || (ctx_r1.batchSettings.endDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 30, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 138)(25, \"label\");\n    i0.ɵɵtext(26, \"\\u9069\\u7528\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 141);\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_ng_template_69_div_28_Template, 6, 3, \"div\", 142)(29, SettingTimePeriodComponent_ng_template_69_div_29_Template, 4, 3, \"div\", 137);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-card-footer\", 143)(31, \"button\", 144);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_69_Template_button_click_31_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClose(ref_r26));\n    });\n    i0.ɵɵtext(32, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_69_Template_button_click_33_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBatchSubmit(ref_r26));\n    });\n    i0.ɵɵtext(34, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(35, SettingTimePeriodComponent_ng_template_69_span_35_Template, 2, 1, \"span\", 137);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const batchStartDate_r27 = i0.ɵɵreference(16);\n    const batchEndDate_r28 = i0.ɵɵreference(23);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuildingForBatch && ctx_r1.selectedHouses.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", batchStartDate_r27);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.batchSettings.startDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", batchEndDate_r28);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.batchSettings.endDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedHouses.length === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 157);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 158);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 157)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 159);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 160)(7, \"div\", 161)(8, \"label\", 162);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 139);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 163);\n    i0.ɵɵelement(13, \"nb-icon\", 28);\n    i0.ɵɵelementStart(14, \"input\", 164);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_73_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r1.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 30, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 165);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 28);\n    i0.ɵɵelementStart(21, \"input\", 166);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_73_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r1.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 30, 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 158)(25, \"button\", 167);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_73_Template_button_click_25_listener() {\n      const ref_r30 = i0.ɵɵrestoreView(_r29).dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClose(ref_r30));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 168);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_73_Template_button_click_27_listener() {\n      const ref_r30 = i0.ɵɵrestoreView(_r29).dialogRef;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit(ref_r30));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r31 = i0.ɵɵreference(16);\n    const changeEndDate_r32 = i0.ɵɵreference(23);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r1.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r31);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r32);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.isStatus = true;\n    // 新增的屬性\n    this.buildingGroups = [];\n    this.buildingOptions = [];\n    this.selectedBuilding = '';\n    this.availableFloors = [];\n    // 篩選和搜尋\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 批次設定\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: true,\n      selectedBuildings: [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    this.selectedBuildingForBatch = null;\n    // 新增：表格視圖相關屬性\n    this.viewMode = 'table';\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.loading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 100;\n    this.totalPages = 1;\n    // 排序相關\n    this.sortField = '';\n    this.sortDirection = 'asc';\n    // 數學函數引用\n    this.Math = Math;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CBuildingName: '未分類',\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  // 新增：建案變更處理\n  onBuildCaseChange() {\n    // 重置所有相關狀態\n    this.resetAllStates();\n    // 執行查詢\n    this.getHouseChangeDate();\n  }\n  // 新增：重置所有狀態\n  resetAllStates() {\n    // 重置數據\n    this.houseChangeDates = [];\n    this.convertedHouseArray = [];\n    this.buildingGroups = [];\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    // 重置篩選條件\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 重置選擇狀態\n    this.selectAll = false;\n    this.selectedBuilding = '';\n    // 重置分頁\n    this.currentPage = 1;\n    this.totalPages = 1;\n    // 重置可用選項\n    this.buildingOptions = [];\n    this.availableFloors = [];\n    // 重置排序\n    this.sortField = '';\n    this.sortDirection = 'asc';\n  }\n  getHouseChangeDate() {\n    // 如果沒有選擇建案，直接返回\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\n      this.loading = false;\n      return;\n    }\n    this.loading = true;\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      this.loading = false;\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          // 新增：建立棟別分組資料\n          this.buildBuildingGroups(res.Entries);\n          // 新增：建立扁平化資料\n          this.buildFlattenedHouses(res.Entries);\n        }\n      }\n    });\n  }\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n  buildBuildingGroups(data) {\n    const buildingMap = new Map();\n    data.forEach(household => {\n      const houseType = household.CHouseHold || ''; // 戶型\n      household.CHouses?.forEach(house => {\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\n        const floor = house.CFloor || 0;\n        if (!buildingMap.has(buildingName)) {\n          buildingMap.set(buildingName, new Map());\n        }\n        const floorMap = buildingMap.get(buildingName);\n        if (!floorMap.has(floor)) {\n          floorMap.set(floor, []);\n        }\n        floorMap.get(floor).push({\n          CHouseHold: houseType,\n          // 戶型\n          CBuildingName: buildingName,\n          // 棟別\n          CHouseId: house.CHouseId || 0,\n          CFloor: floor,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 轉換為BuildingGroup格式\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n      const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n      .map(([floorNumber, houses]) => ({\n        floorNumber,\n        houses: houses.sort((a, b) => {\n          // 排序邏輯：先按戶型排序，再按樓層排序\n          if (a.CHouseHold !== b.CHouseHold) {\n            return a.CHouseHold.localeCompare(b.CHouseHold);\n          }\n          return a.CFloor - b.CFloor;\n        }),\n        selected: false\n      }));\n      return {\n        name: buildingName,\n        floors,\n        selected: false\n      };\n    }).sort((a, b) => a.name.localeCompare(b.name));\n    // 更新棟別選項和可用樓層\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n    this.updateAvailableFloors();\n  }\n  // 新增：更新可用樓層\n  updateAvailableFloors() {\n    const floorsSet = new Set();\n    this.buildingGroups.forEach(building => {\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n        building.floors.forEach(floor => {\n          floorsSet.add(floor.floorNumber);\n        });\n      }\n    });\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n  }\n  // 修改：棟別選擇變更處理\n  onBuildingChange() {\n    // 重置選擇狀態\n    this.selectedHouses.forEach(house => house.selected = false);\n    this.selectedHouses = [];\n    this.selectAll = false;\n    // 重置分頁到第一頁\n    this.currentPage = 1;\n    // 重置樓層篩選\n    this.filterOptions.floorFilter = '';\n    // 更新可用樓層\n    this.updateAvailableFloors();\n    // 設定棟別篩選並執行搜尋\n    this.filterOptions.buildingFilter = this.selectedBuilding;\n    this.onSearch();\n  }\n  // 新增：取得過濾後的棟別資料\n  getFilteredBuildings() {\n    return this.buildingGroups.filter(building => {\n      // 棟別篩選\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n        return false;\n      }\n      // 關鍵字搜尋 (搜尋戶型)\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n        if (!hasMatchingHouse) {\n          return false;\n        }\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n        if (!hasMatchingStatus) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n        if (!hasMatchingFloor) {\n          return false;\n        }\n      }\n      return true;\n    }).map(building => {\n      // 對每個棟別，也要篩選其樓層和戶別\n      const filteredBuilding = {\n        ...building\n      };\n      filteredBuilding.floors = building.floors.filter(floor => {\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (floor.floorNumber !== floorNumber) {\n            return false;\n          }\n        }\n        // 檢查該樓層是否有符合條件的戶別\n        const hasValidHouses = floor.houses.some(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return hasValidHouses;\n      }).map(floor => {\n        // 篩選戶別\n        const filteredFloor = {\n          ...floor\n        };\n        filteredFloor.houses = floor.houses.filter(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return filteredFloor;\n      });\n      return filteredBuilding;\n    });\n  }\n  // 修復：檢查戶別是否符合狀態篩選\n  matchesStatusFilter(house) {\n    const status = this.getHouseStatus(house);\n    switch (this.filterOptions.statusFilter) {\n      case 'active':\n        return status === 'active';\n      case 'pending':\n        return status === 'pending';\n      case 'expired':\n        return status === 'expired';\n      case 'not-set':\n        return status === 'not-set';\n      case 'disabled':\n        return status === 'disabled';\n      default:\n        return true;\n      // 全部狀態\n    }\n  }\n  // 修復：取得戶別狀態\n  getHouseStatus(house) {\n    if (!house.CHouseId) {\n      return 'disabled';\n    }\n    // 檢查是否有設定開放時段\n    if (!house.CChangeStartDate || !house.CChangeEndDate || house.CChangeStartDate === '' || house.CChangeEndDate === '') {\n      return 'not-set';\n    }\n    try {\n      // 處理日期字串，支援多種格式\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      // 解析開始日期\n      let startDate;\n      if (house.CChangeStartDate.includes('T')) {\n        startDate = new Date(house.CChangeStartDate);\n      } else {\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\n      }\n      // 解析結束日期\n      let endDate;\n      if (house.CChangeEndDate.includes('T')) {\n        endDate = new Date(house.CChangeEndDate);\n      } else {\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\n      }\n      // 檢查日期有效性\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\n        console.warn('Invalid date format:', {\n          start: house.CChangeStartDate,\n          end: house.CChangeEndDate,\n          houseId: house.CHouseId\n        });\n        return 'not-set';\n      }\n      // 轉換為日期比較（不含時間）\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n      // 判斷狀態\n      if (today < startDateOnly) {\n        return 'pending'; // 待開放\n      } else if (today >= startDateOnly && today <= endDateOnly) {\n        return 'active'; // 進行中\n      } else {\n        return 'expired'; // 已過期\n      }\n    } catch (error) {\n      console.error('Error parsing dates:', error, house);\n      return 'not-set';\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  // 修改：開啟批次設定對話框\n  openBatchSetting(building) {\n    this.selectedBuildingForBatch = building || null;\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\n    const hasSelectedHouses = this.selectedHouses.length > 0;\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: !hasSelectedHouses && !building,\n      selectedBuildings: building ? [building.name] : [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    // 重置選擇狀態\n    if (building) {\n      building.floors.forEach(floor => {\n        floor.selected = false;\n        floor.houses.forEach(house => house.selected = false);\n      });\n    }\n    // 開啟對話框\n    this.dialogService.open(this.batchSettingDialog);\n  }\n  // 新增：樓層選擇變更處理\n  onFloorSelectionChange(floor) {\n    if (floor.selected) {\n      floor.houses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = true;\n        }\n      });\n    } else {\n      floor.houses.forEach(house => house.selected = false);\n    }\n  }\n  // 修改：批次提交\n  onBatchSubmit(ref) {\n    // 驗證批次設定\n    this.valid.clear();\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 收集要更新的房屋\n    const housesToUpdate = [];\n    if (this.batchSettings.applyToAll) {\n      // 全部戶別\n      this.flattenedHouses.forEach(house => {\n        if (house.CHouseId) {\n          housesToUpdate.push({\n            CHouseId: house.CHouseId,\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n          });\n        }\n      });\n    } else {\n      // 使用已選擇的戶別\n      if (this.selectedHouses.length > 0) {\n        this.selectedHouses.forEach(house => {\n          if (house.CHouseId) {\n            housesToUpdate.push({\n              CHouseId: house.CHouseId,\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n            });\n          }\n        });\n      } else if (this.selectedBuildingForBatch) {\n        // 如果沒有選擇的戶別，使用舊的邏輯\n        this.selectedBuildingForBatch.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.selected && house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      }\n    }\n    if (housesToUpdate.length === 0) {\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\n      return;\n    }\n    // 調用API進行批次更新\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: housesToUpdate\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n        // 清除選擇狀態\n        this.selectedHouses.forEach(house => house.selected = false);\n        this.selectedHouses = [];\n        this.selectAll = false;\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  // 新增：取得狀態樣式類別\n  getStatusClass(house) {\n    const status = this.getHouseStatus(house);\n    return `status-${status}`;\n  }\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n  openHouseDialog(house) {\n    if (house.CHouseId) {\n      // 使用現有的openModel方法\n      this.openModel(this.dialog, house);\n    }\n  }\n  // 新增：建立扁平化房屋資料\n  buildFlattenedHouses(data) {\n    this.flattenedHouses = [];\n    data.forEach(household => {\n      const houseType = household.CHouseHold || '';\n      household.CHouses?.forEach(house => {\n        this.flattenedHouses.push({\n          CHouseHold: houseType,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId || 0,\n          CFloor: house.CFloor || 0,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 初始化篩選和分頁\n    this.onSearch();\n    // 調試：輸出狀態統計\n    this.debugStatusCounts();\n  }\n  // 調試：輸出狀態統計\n  debugStatusCounts() {\n    const statusCounts = {\n      active: 0,\n      pending: 0,\n      expired: 0,\n      'not-set': 0,\n      disabled: 0\n    };\n    this.flattenedHouses.forEach(house => {\n      const status = this.getHouseStatus(house);\n      if (statusCounts.hasOwnProperty(status)) {\n        statusCounts[status]++;\n      }\n    });\n    console.log('狀態統計:', statusCounts);\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\n  }\n  // 新增：視圖模式切換\n  setViewMode(mode) {\n    this.viewMode = mode;\n  }\n  // 修改：搜尋和篩選\n  onSearch() {\n    // 記錄篩選前的已選擇項目\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\n    this.filteredHouses = this.flattenedHouses.filter(house => {\n      // 關鍵字搜尋\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n          return false;\n        }\n      }\n      // 棟別篩選\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n        return false;\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        if (!this.matchesStatusFilter(house)) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        if (house.CFloor !== floorNumber) {\n          return false;\n        }\n      }\n      return true;\n    });\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\n    this.selectedHouses = this.filteredHouses.filter(house => previouslySelectedIds.includes(house.CHouseId));\n    // 更新扁平化資料中的選擇狀態\n    this.flattenedHouses.forEach(house => {\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\n    });\n    // 更新全選狀態\n    this.updateSelectAllState();\n    // 重新計算分頁\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：更新全選狀態\n  updateSelectAllState() {\n    if (this.paginatedHouses.length === 0) {\n      this.selectAll = false;\n    } else {\n      this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n    }\n  }\n  // 修改：更新分頁\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n    // 更新全選狀態\n    this.updateSelectAllState();\n  }\n  // 新增：頁面大小變更\n  onPageSizeChange() {\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：跳轉頁面\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.updatePagination();\n    }\n  }\n  // 新增：取得可見頁碼\n  getVisiblePages() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 新增：全選/取消全選\n  onSelectAllChange() {\n    this.paginatedHouses.forEach(house => {\n      if (house.CHouseId) {\n        house.selected = this.selectAll;\n      }\n    });\n    this.updateSelectedHouses();\n  }\n  // 新增：單一選擇變更\n  onHouseSelectionChange() {\n    this.updateSelectedHouses();\n    this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n  }\n  // 新增：更新已選擇房屋列表\n  updateSelectedHouses() {\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n  }\n  // 新增：排序\n  sort(field) {\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    this.filteredHouses.sort((a, b) => {\n      let aValue = a[field];\n      let bValue = b[field];\n      // 處理日期排序\n      if (field.includes('Date')) {\n        aValue = aValue ? new Date(aValue).getTime() : 0;\n        bValue = bValue ? new Date(bValue).getTime() : 0;\n      }\n      // 處理數字排序\n      if (field === 'CFloor') {\n        aValue = Number(aValue) || 0;\n        bValue = Number(bValue) || 0;\n      }\n      // 處理字串排序\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (aValue < bValue) {\n        return this.sortDirection === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return this.sortDirection === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    this.updatePagination();\n  }\n  // 新增：TrackBy函數\n  trackByHouseId(_index, house) {\n    return house.CHouseId;\n  }\n  // 新增：取得狀態文字\n  getStatusText(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return '進行中';\n      case 'pending':\n        return '待開放';\n      case 'expired':\n        return '已過期';\n      case 'not-set':\n        return '未設定';\n      case 'disabled':\n        return '已停用';\n      default:\n        return '未知';\n    }\n  }\n  // 新增：匯出資料\n  exportData() {\n    // 實現匯出功能\n    const csvContent = this.generateCSV();\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  // 新增：產生CSV內容\n  generateCSV() {\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\n    const rows = this.filteredHouses.map(house => [house.CHouseHold, house.CBuildingName, `${house.CFloor}F`, house.CChangeStartDate || '未設定', house.CChangeEndDate || '未設定', this.getStatusText(house)]);\n    const csvContent = [headers, ...rows].map(row => row.map(cell => `\"${cell}\"`).join(',')).join('\\n');\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\n  }\n  static {\n    this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingTimePeriodComponent,\n      selectors: [[\"ngx-setting-time-period\"]],\n      viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 75,\n      vars: 20,\n      consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"batchSettingDialog\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"batchStartDate\", \"\"], [\"batchEndDate\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"integrated-header\"], [1, \"page-header\", \"mb-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"page-title\"], [1, \"page-description\", \"text-muted\", \"mb-0\"], [\"class\", \"current-info text-end\", 4, \"ngIf\"], [1, \"primary-filters\", \"mb-3\"], [1, \"row\", \"align-items-end\"], [1, \"col-lg-3\", \"col-md-4\", \"mb-2\"], [1, \"form-label\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-2\", \"col-md-3\", \"mb-2\"], [\"placeholder\", \"\\u5168\\u90E8\\u68DF\\u5225\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"\"], [1, \"col-lg-4\", \"col-md-5\", \"mb-2\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-fill\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u958B\\u59CB\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [1, \"mx-2\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u7D50\\u675F\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-lg-3\", \"col-md-12\", \"mb-2\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-end\", \"h-100\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-cogs\", \"me-1\"], [1, \"row\", \"mt-2\"], [1, \"col-md-6\"], [1, \"display-mode-options\"], [1, \"form-label\", \"me-3\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"radio\", \"name\", \"displayMode\", \"id\", \"timeMode\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"timeMode\", 1, \"form-check-label\"], [\"type\", \"radio\", \"name\", \"displayMode\", \"id\", \"statusMode\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"statusMode\", 1, \"form-check-label\"], [\"class\", \"advanced-filters\", 4, \"ngIf\"], [\"class\", \"table-view mt-4\", 4, \"ngIf\"], [\"class\", \"card-view mt-4\", 4, \"ngIf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [1, \"current-info\", \"text-end\"], [1, \"current-case\", \"text-white\"], [1, \"fas\", \"fa-building\", \"me-1\"], [\"class\", \"data-count text-white-50 small\", 4, \"ngIf\"], [1, \"data-count\", \"text-white-50\", \"small\"], [\"class\", \"text-warning ms-1\", 4, \"ngIf\"], [1, \"text-warning\", \"ms-1\"], [3, \"value\"], [1, \"advanced-filters\"], [1, \"row\", \"align-items-center\"], [\"nbPrefix\", \"\", \"icon\", \"search-outline\"], [\"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u578B\\u3001\\u68DF\\u5225...\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u72C0\\u614B\\u7BE9\\u9078\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"active\"], [\"value\", \"pending\"], [\"value\", \"expired\"], [\"value\", \"not-set\"], [\"value\", \"disabled\"], [\"placeholder\", \"\\u6A13\\u5C64\\u7BE9\\u9078\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-lg-2\", \"col-md-2\", \"mb-2\"], [\"placeholder\", \"\\u986F\\u793A\\u7B46\\u6578\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"data-summary\"], [1, \"text-muted\", \"small\"], [\"class\", \"text-primary ms-1\", 4, \"ngIf\"], [1, \"view-toggle\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [1, \"fas\", \"fa-table\"], [1, \"fas\", \"fa-th\"], [1, \"text-primary\", \"ms-1\"], [1, \"table-view\", \"mt-4\"], [1, \"table-toolbar\", \"mb-3\"], [1, \"batch-actions\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-sm\", \"btn-success\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\"], [1, \"pagination-info\"], [1, \"table-container\"], [1, \"table\", \"table-hover\"], [1, \"table-header\"], [\"width\", \"50\"], [\"width\", \"100\", 1, \"sortable\", 3, \"click\"], [1, \"fas\", \"fa-sort\"], [\"width\", \"80\", 1, \"sortable\", 3, \"click\"], [\"width\", \"120\", 1, \"sortable\", 3, \"click\"], [\"width\", \"100\"], [\"width\", \"80\"], [3, \"table-row-selected\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"pagination-container mt-3\", 4, \"ngIf\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"date-display\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"status-badge\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\", \"disabled\"], [1, \"date-display\"], [1, \"text-muted\"], [1, \"pagination-container\", \"mt-3\"], [1, \"pagination\", \"justify-content-center\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"page-link\", 3, \"click\"], [1, \"card-view\", \"mt-4\"], [1, \"row\"], [\"class\", \"col-lg-3 col-md-4 col-sm-6 mb-3\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"col-lg-3\", \"col-md-4\", \"col-sm-6\", \"mb-3\"], [1, \"house-card\"], [1, \"p-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"mb-2\"], [1, \"house-info\"], [1, \"house-number\", \"mb-1\"], [1, \"date-info\", \"mb-2\"], [1, \"date-row\"], [\"class\", \"date-value\", 4, \"ngIf\"], [1, \"status-badge\", \"small\"], [1, \"date-value\"], [1, \"text-center\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"mb-3\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"mt-2\"], [2, \"width\", \"600px\", \"max-height\", \"90vh\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"form-group\"], [1, \"text-red-600\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"selection-options\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"text-primary\"], [1, \"alert\", \"alert-info\"], [1, \"selected-houses-preview\"], [\"class\", \"badge badge-primary mr-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"mr-1\", \"mb-1\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"floor-selection\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-selection\"], [\"class\", \"house-selection ml-4\", 4, \"ngIf\"], [1, \"house-selection\", \"ml-4\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n      template: function SettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 9)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 10)(5, \"div\", 11)(6, \"div\", 12)(7, \"div\")(8, \"h1\", 13);\n          i0.ɵɵtext(9, \"\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5\\u8A2D\\u5B9A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 14);\n          i0.ɵɵtext(11, \" \\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, SettingTimePeriodComponent_div_12_Template, 5, 2, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 16)(14, \"div\", 17)(15, \"div\", 18)(16, \"label\", 19);\n          i0.ɵɵtext(17, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"nb-select\", 20);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_18_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildCaseChange());\n          });\n          i0.ɵɵtemplate(19, SettingTimePeriodComponent_nb_option_19_Template, 2, 2, \"nb-option\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 22)(21, \"label\", 19);\n          i0.ɵɵtext(22, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-select\", 23);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildingChange());\n          });\n          i0.ɵɵelementStart(24, \"nb-option\", 24);\n          i0.ɵɵtext(25, \"\\u5168\\u90E8\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(26, SettingTimePeriodComponent_nb_option_26_Template, 2, 2, \"nb-option\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 25)(28, \"label\", 19);\n          i0.ɵɵtext(29, \"\\u958B\\u653E\\u65E5\\u671F\\u7BC4\\u570D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 26)(31, \"nb-form-field\", 27);\n          i0.ɵɵelement(32, \"nb-icon\", 28);\n          i0.ɵɵelementStart(33, \"input\", 29);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_33_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"nb-datepicker\", 30, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"span\", 31);\n          i0.ɵɵtext(37, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"nb-form-field\", 27);\n          i0.ɵɵelement(39, \"nb-icon\", 28);\n          i0.ɵɵelementStart(40, \"input\", 32);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(41, \"nb-datepicker\", 30, 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"div\", 33)(44, \"div\", 34)(45, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_45_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵelement(46, \"i\", 36);\n          i0.ɵɵtext(47, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_48_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openBatchSetting());\n          });\n          i0.ɵɵelement(49, \"i\", 38);\n          i0.ɵɵtext(50, \"\\u6279\\u6B21\\u8A2D\\u5B9A \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(51, \"div\", 39)(52, \"div\", 40)(53, \"div\", 41)(54, \"label\", 42);\n          i0.ɵɵtext(55, \"\\u986F\\u793A\\u6A21\\u5F0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\", 43)(57, \"input\", 44);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_57_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"label\", 45);\n          i0.ɵɵtext(59, \"\\u4F9D\\u958B\\u653E\\u6642\\u6BB5\\u986F\\u793A\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 43)(61, \"input\", 46);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_61_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"label\", 47);\n          i0.ɵɵtext(63, \"\\u4F9D\\u958B\\u653E\\u72C0\\u614B\\u986F\\u793A\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(64, SettingTimePeriodComponent_div_64_Template, 49, 19, \"div\", 48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(65, SettingTimePeriodComponent_div_65_Template, 42, 31, \"div\", 49)(66, SettingTimePeriodComponent_div_66_Template, 4, 3, \"div\", 50)(67, SettingTimePeriodComponent_div_67_Template, 7, 0, \"div\", 51)(68, SettingTimePeriodComponent_div_68_Template, 9, 0, \"div\", 51);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(69, SettingTimePeriodComponent_ng_template_69_Template, 36, 9, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(71, SettingTimePeriodComponent_ng_template_71_Template, 4, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(73, SettingTimePeriodComponent_ng_template_73_Template, 29, 6, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const StartDate_r33 = i0.ɵɵreference(35);\n          const EndDate_r34 = i0.ɵɵreference(42);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingOptions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", StartDate_r33);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", EndDate_r34);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.flattenedHouses.length);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"table\" && ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"card\" && ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length === 0 && ctx.houseChangeDates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.RadioControlValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, RadioButtonModule, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\".integrated-header[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 0.5rem;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 1.5rem;\\n  overflow: hidden;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 1.5rem;\\n  margin-bottom: 0;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  color: white;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  opacity: 0.9;\\n  font-size: 0.9rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  opacity: 0.8;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%]   .text-warning[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  background-color: #f8f9fa;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding-top: 0.5rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  margin-right: 1rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.2s ease;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 1.5rem;\\n  background-color: #ffffff;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]   .text-primary[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0;\\n  font-size: 0.8rem;\\n  padding: 0.375rem 0.75rem;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:first-child {\\n  border-top-left-radius: 0.375rem;\\n  border-bottom-left-radius: 0.375rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n  border-top-right-radius: 0.375rem;\\n  border-bottom-right-radius: 0.375rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n  font-size: 0.75rem;\\n}\\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%], \\n.integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n  border-radius: 0.375rem;\\n  overflow: hidden;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 0.75rem;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  opacity: 0.5;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-selected[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  vertical-align: middle;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-active[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #155724;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  color: #856404;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-expired[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-not-set[_ngcontent-%COMP%] {\\n  background-color: #e2e3e5;\\n  color: #383d41;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n}\\n.table-view[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%] {\\n  font-family: monospace;\\n  font-size: 0.875rem;\\n}\\n\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  height: 100%;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card.selected[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card.disabled[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n  box-shadow: none;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .house-number[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.25rem;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-row[_ngcontent-%COMP%]   .date-value[_ngcontent-%COMP%] {\\n  font-family: monospace;\\n  font-size: 0.875rem;\\n  color: #495057;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-active[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #155724;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  color: #856404;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-expired[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-not-set[_ngcontent-%COMP%] {\\n  background-color: #e2e3e5;\\n  color: #383d41;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n}\\n\\n.search-enhanced[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0.25rem;\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #495057;\\n  border-color: #dee2e6;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  background-color: #fff;\\n  border-color: #dee2e6;\\n}\\n\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  padding-left: 1rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\\n  gap: 0.25rem;\\n  margin-top: 0.5rem;\\n  padding: 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  margin-bottom: 1rem;\\n  border: 1px solid transparent;\\n  border-radius: 0.375rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%] {\\n  color: #0c5460;\\n  background-color: #d1ecf1;\\n  border-color: #bee5eb;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  font-weight: 600;\\n}\\n\\n@media (max-width: 992px) {\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n    justify-content: center;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .card-view[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%] {\\n    flex: 0 0 50%;\\n    max-width: 50%;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.75rem;\\n    text-align: left !important;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .flex-fill[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .mx-2[_ngcontent-%COMP%] {\\n    margin: 0.25rem 0 !important;\\n    text-align: center;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n    margin-bottom: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .search-enhanced[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .search-enhanced[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    margin-top: 0.5rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0.25rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(3) {\\n    display: none;\\n  }\\n  .card-view[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .card-view[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%] {\\n    flex: 0 0 100%;\\n    max-width: 100%;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .integrated-header[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n    border-radius: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n    display: none;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n    margin-bottom: 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .query-section[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(4) {\\n    display: none;\\n  }\\n}\\nnb-card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  margin-right: 0.5rem;\\n}\\n.status-indicator.active[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n}\\n.status-indicator.pending[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n}\\n.status-indicator.expired[_ngcontent-%COMP%] {\\n  background-color: #dc3545;\\n}\\n.status-indicator.not-set[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n}\\n.status-indicator.disabled[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "RadioButtonModule", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "selectedHouses", "length", "ɵɵtemplate", "SettingTimePeriodComponent_div_12_div_4_span_2_Template", "flattenedHouses", "ɵɵproperty", "ɵɵelement", "SettingTimePeriodComponent_div_12_div_4_Template", "searchQuery", "CBuildCaseSelected", "CBuildCaseName", "case_r3", "building_r4", "floor_r6", "ɵɵtwoWayListener", "SettingTimePeriodComponent_div_64_Template_input_ngModelChange_5_listener", "$event", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "filterOptions", "searchKeyword", "ɵɵresetView", "ɵɵlistener", "onSearch", "SettingTimePeriodComponent_div_64_Template_nb_select_ngModelChange_7_listener", "statusFilter", "SettingTimePeriodComponent_div_64_Template_nb_select_selectedChange_7_listener", "SettingTimePeriodComponent_div_64_Template_nb_select_ngModelChange_21_listener", "floorFilter", "SettingTimePeriodComponent_div_64_Template_nb_select_selectedChange_21_listener", "SettingTimePeriodComponent_div_64_nb_option_24_Template", "SettingTimePeriodComponent_div_64_Template_nb_select_ngModelChange_26_listener", "pageSize", "SettingTimePeriodComponent_div_64_Template_nb_select_selectedChange_26_listener", "onPageSizeChange", "SettingTimePeriodComponent_div_64_span_40_Template", "SettingTimePeriodComponent_div_64_Template_button_click_43_listener", "setViewMode", "SettingTimePeriodComponent_div_64_Template_button_click_46_listener", "ɵɵtwoWayProperty", "availableFloors", "filteredHouses", "ɵɵclassProp", "viewMode", "ɵɵpipeBind2", "house_r9", "CChangeStartDate", "CChangeEndDate", "SettingTimePeriodComponent_div_65_tr_40_Template_nb_checkbox_ngModelChange_2_listener", "_r8", "$implicit", "selected", "onHouseSelectionChange", "SettingTimePeriodComponent_div_65_tr_40_span_10_Template", "SettingTimePeriodComponent_div_65_tr_40_span_11_Template", "SettingTimePeriodComponent_div_65_tr_40_span_13_Template", "SettingTimePeriodComponent_div_65_tr_40_span_14_Template", "SettingTimePeriodComponent_div_65_tr_40_Template_button_click_19_listener", "dialog_r10", "ɵɵreference", "openModel", "CHouseId", "ɵɵtextInterpolate", "CHouseHold", "CBuildingName", "CFloor", "ɵɵclassMap", "getStatusClass", "getStatusText", "SettingTimePeriodComponent_div_65_div_41_li_9_Template_button_click_1_listener", "page_r13", "_r12", "goToPage", "currentPage", "SettingTimePeriodComponent_div_65_div_41_Template_button_click_4_listener", "_r11", "SettingTimePeriodComponent_div_65_div_41_Template_button_click_7_listener", "SettingTimePeriodComponent_div_65_div_41_li_9_Template", "SettingTimePeriodComponent_div_65_div_41_Template_button_click_11_listener", "SettingTimePeriodComponent_div_65_div_41_Template_button_click_14_listener", "totalPages", "getVisiblePages", "SettingTimePeriodComponent_div_65_Template_nb_checkbox_ngModelChange_4_listener", "_r7", "selectAll", "onSelectAllChange", "SettingTimePeriodComponent_div_65_Template_button_click_6_listener", "openBatchSetting", "SettingTimePeriodComponent_div_65_Template_button_click_9_listener", "exportData", "SettingTimePeriodComponent_div_65_Template_nb_checkbox_ngModelChange_19_listener", "SettingTimePeriodComponent_div_65_Template_th_click_20_listener", "sort", "SettingTimePeriodComponent_div_65_Template_th_click_23_listener", "SettingTimePeriodComponent_div_65_Template_th_click_26_listener", "SettingTimePeriodComponent_div_65_Template_th_click_29_listener", "SettingTimePeriodComponent_div_65_Template_th_click_32_listener", "SettingTimePeriodComponent_div_65_tr_40_Template", "SettingTimePeriodComponent_div_65_div_41_Template", "ɵɵtextInterpolate3", "Math", "min", "sortField", "sortDirection", "paginatedHouses", "trackByHouseId", "house_r15", "SettingTimePeriodComponent_div_66_div_2_Template_nb_checkbox_ngModelChange_9_listener", "_r14", "SettingTimePeriodComponent_div_66_div_2_span_14_Template", "SettingTimePeriodComponent_div_66_div_2_span_15_Template", "SettingTimePeriodComponent_div_66_div_2_span_19_Template", "SettingTimePeriodComponent_div_66_div_2_span_20_Template", "SettingTimePeriodComponent_div_66_div_2_Template_button_click_24_listener", "ɵɵtextInterpolate2", "SettingTimePeriodComponent_div_66_div_3_li_9_Template_button_click_1_listener", "page_r18", "_r17", "SettingTimePeriodComponent_div_66_div_3_Template_button_click_4_listener", "_r16", "SettingTimePeriodComponent_div_66_div_3_Template_button_click_7_listener", "SettingTimePeriodComponent_div_66_div_3_li_9_Template", "SettingTimePeriodComponent_div_66_div_3_Template_button_click_11_listener", "SettingTimePeriodComponent_div_66_div_3_Template_button_click_14_listener", "SettingTimePeriodComponent_div_66_div_2_Template", "SettingTimePeriodComponent_div_66_div_3_Template", "selectedBuildingForBatch", "name", "house_r20", "SettingTimePeriodComponent_ng_template_69_div_28_span_4_Template", "SettingTimePeriodComponent_ng_template_69_div_28_span_5_Template", "slice", "SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener", "house_r25", "_r24", "SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_div_3_nb_checkbox_1_Template", "floor_r23", "houses", "SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener", "_r22", "onFloorSelectionChange", "SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_div_3_Template", "floorNumber", "SettingTimePeriodComponent_ng_template_69_div_29_div_3_div_1_Template", "floors", "SettingTimePeriodComponent_ng_template_69_div_29_Template_nb_checkbox_ngModelChange_1_listener", "_r21", "batchSettings", "applyToAll", "SettingTimePeriodComponent_ng_template_69_div_29_div_3_Template", "SettingTimePeriodComponent_ng_template_69_span_3_Template", "SettingTimePeriodComponent_ng_template_69_span_4_Template", "SettingTimePeriodComponent_ng_template_69_Template_input_ngModelChange_14_listener", "_r19", "startDate", "SettingTimePeriodComponent_ng_template_69_Template_input_ngModelChange_21_listener", "endDate", "SettingTimePeriodComponent_ng_template_69_div_28_Template", "SettingTimePeriodComponent_ng_template_69_div_29_Template", "SettingTimePeriodComponent_ng_template_69_Template_button_click_31_listener", "ref_r26", "dialogRef", "onClose", "SettingTimePeriodComponent_ng_template_69_Template_button_click_33_listener", "onBatchSubmit", "SettingTimePeriodComponent_ng_template_69_span_35_Template", "batchStartDate_r27", "batchEndDate_r28", "SettingTimePeriodComponent_ng_template_73_Template_input_ngModelChange_14_listener", "_r29", "selectedHouseChangeDate", "SettingTimePeriodComponent_ng_template_73_Template_input_ngModelChange_21_listener", "SettingTimePeriodComponent_ng_template_73_Template_button_click_25_listener", "ref_r30", "SettingTimePeriodComponent_ng_template_73_Template_button_click_27_listener", "onSubmit", "changeStartDate_r31", "changeEndDate_r32", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "isStatus", "buildingGroups", "buildingOptions", "selectedBuilding", "buildingFilter", "selectedBuildings", "selectedFloors", "loading", "undefined", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "CBuildingNameSelected", "getUserBuildCase", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "validation", "errorMessages", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "for<PERSON>ach", "household", "CHouses", "house", "floor", "push", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "onBuildCaseChange", "resetAllStates", "houseChangeDates", "convertedHouseArray", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "buildBuildingGroups", "buildFlattenedHouses", "data", "buildingMap", "Map", "houseType", "buildingName", "has", "set", "floorMap", "get", "localeCompare", "bg", "updateAvailableFloors", "building", "onBuildingChange", "getFilteredBuildings", "filter", "keyword", "toLowerCase", "hasMatchingHouse", "some", "includes", "hasMatchingStatus", "matchesStatusFilter", "parseInt", "hasMatchingFloor", "filteredBuilding", "hasValidHouses", "filteredFloor", "status", "getHouseStatus", "now", "today", "getFullYear", "getMonth", "getDate", "isNaN", "getTime", "console", "warn", "start", "end", "houseId", "startDateOnly", "endDateOnly", "error", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "hasSelectedHouses", "batchSettingDialog", "housesToUpdate", "openHouseDialog", "dialog", "debugStatusCounts", "statusCounts", "active", "pending", "expired", "disabled", "hasOwnProperty", "log", "toISOString", "split", "mode", "previouslySelectedIds", "updateSelectAllState", "updatePagination", "every", "ceil", "startIndex", "endIndex", "page", "pages", "maxVisible", "max", "i", "updateSelectedHouses", "field", "aValue", "bValue", "Number", "_index", "csv<PERSON><PERSON>nt", "generateCSV", "blob", "Blob", "type", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "rows", "row", "cell", "join", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "Router", "i7", "EventService", "selectors", "viewQuery", "SettingTimePeriodComponent_Query", "rf", "ctx", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingTimePeriodComponent_Template", "SettingTimePeriodComponent_div_12_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_18_listener", "_r1", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_18_listener", "SettingTimePeriodComponent_nb_option_19_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_23_listener", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON><PERSON>_23_listener", "SettingTimePeriodComponent_nb_option_26_Template", "SettingTimePeriodComponent_Template_input_ngModelChange_33_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_40_listener", "SettingTimePeriodComponent_Template_button_click_45_listener", "SettingTimePeriodComponent_Template_button_click_48_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_57_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_61_listener", "SettingTimePeriodComponent_div_64_Template", "SettingTimePeriodComponent_div_65_Template", "SettingTimePeriodComponent_div_66_Template", "SettingTimePeriodComponent_div_67_Template", "SettingTimePeriodComponent_div_68_Template", "SettingTimePeriodComponent_ng_template_69_Template", "ɵɵtemplateRefExtractor", "SettingTimePeriodComponent_ng_template_71_Template", "SettingTimePeriodComponent_ng_template_73_Template", "StartDate_r33", "EndDate_r34", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "RadioControlValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string; // 戶型\r\n  CBuildingName: string; // 棟別\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule, RadioButtonModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n\r\n  @ViewChild('batchSettingDialog') batchSettingDialog!: TemplateRef<any>;\r\n  @ViewChild('dialog') dialog!: TemplateRef<any>;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  isStatus: boolean = true;\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  // 新增的屬性\r\n  buildingGroups: BuildingGroup[] = [];\r\n  buildingOptions: string[] = [];\r\n  selectedBuilding: string = '';\r\n  availableFloors: number[] = [];\r\n\r\n  // 篩選和搜尋\r\n  filterOptions: FilterOptions = {\r\n    searchKeyword: '',\r\n    statusFilter: '',\r\n    floorFilter: '',\r\n    buildingFilter: ''\r\n  };\r\n\r\n  // 批次設定\r\n  batchSettings: BatchSettings = {\r\n    startDate: null,\r\n    endDate: null,\r\n    applyToAll: true,\r\n    selectedBuildings: [],\r\n    selectedFloors: [],\r\n    selectedHouses: []\r\n  };\r\n\r\n  selectedBuildingForBatch: BuildingGroup | null = null;\r\n\r\n  // 新增：表格視圖相關屬性\r\n  viewMode: 'table' | 'card' = 'table';\r\n  flattenedHouses: ConvertedHouse[] = [];\r\n  filteredHouses: ConvertedHouse[] = [];\r\n  paginatedHouses: ConvertedHouse[] = [];\r\n  selectedHouses: ConvertedHouse[] = [];\r\n  selectAll: boolean = false;\r\n  loading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  override pageSize: number = 100;\r\n  totalPages: number = 1;\r\n\r\n  // 排序相關\r\n  sortField: string = '';\r\n  sortDirection: 'asc' | 'desc' = 'asc';\r\n\r\n  // 數學函數引用\r\n  Math = Math;\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CBuildingName: '未分類',\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：建案變更處理\r\n  onBuildCaseChange() {\r\n    // 重置所有相關狀態\r\n    this.resetAllStates();\r\n    // 執行查詢\r\n    this.getHouseChangeDate();\r\n  }\r\n\r\n  // 新增：重置所有狀態\r\n  resetAllStates() {\r\n    // 重置數據\r\n    this.houseChangeDates = [];\r\n    this.convertedHouseArray = [];\r\n    this.buildingGroups = [];\r\n    this.flattenedHouses = [];\r\n    this.filteredHouses = [];\r\n    this.paginatedHouses = [];\r\n    this.selectedHouses = [];\r\n\r\n    // 重置篩選條件\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    this.selectAll = false;\r\n    this.selectedBuilding = '';\r\n\r\n    // 重置分頁\r\n    this.currentPage = 1;\r\n    this.totalPages = 1;\r\n\r\n    // 重置可用選項\r\n    this.buildingOptions = [];\r\n    this.availableFloors = [];\r\n\r\n    // 重置排序\r\n    this.sortField = '';\r\n    this.sortDirection = 'asc';\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    // 如果沒有選擇建案，直接返回\r\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      this.loading = false;\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n          // 新增：建立棟別分組資料\r\n          this.buildBuildingGroups(res.Entries)\r\n          // 新增：建立扁平化資料\r\n          this.buildFlattenedHouses(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\r\n  buildBuildingGroups(data: GetHouseChangeDateRes[]) {\r\n    const buildingMap = new Map<string, Map<number, ConvertedHouse[]>>();\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || ''; // 戶型\r\n\r\n      household.CHouses?.forEach(house => {\r\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\r\n        const floor = house.CFloor || 0;\r\n\r\n        if (!buildingMap.has(buildingName)) {\r\n          buildingMap.set(buildingName, new Map<number, ConvertedHouse[]>());\r\n        }\r\n\r\n        const floorMap = buildingMap.get(buildingName)!;\r\n\r\n        if (!floorMap.has(floor)) {\r\n          floorMap.set(floor, []);\r\n        }\r\n\r\n        floorMap.get(floor)!.push({\r\n          CHouseHold: houseType, // 戶型\r\n          CBuildingName: buildingName, // 棟別\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: floor,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 轉換為BuildingGroup格式\r\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\r\n      const floors: FloorGroup[] = Array.from(floorMap.entries())\r\n        .sort(([a], [b]) => b - a) // 樓層由高到低排序\r\n        .map(([floorNumber, houses]) => ({\r\n          floorNumber,\r\n          houses: houses.sort((a, b) => {\r\n            // 排序邏輯：先按戶型排序，再按樓層排序\r\n            if (a.CHouseHold !== b.CHouseHold) {\r\n              return a.CHouseHold.localeCompare(b.CHouseHold);\r\n            }\r\n            return a.CFloor - b.CFloor;\r\n          }),\r\n          selected: false\r\n        }));\r\n\r\n      return {\r\n        name: buildingName,\r\n        floors,\r\n        selected: false\r\n      };\r\n    }).sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    // 更新棟別選項和可用樓層\r\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\r\n    this.updateAvailableFloors();\r\n  }\r\n\r\n  // 新增：更新可用樓層\r\n  updateAvailableFloors() {\r\n    const floorsSet = new Set<number>();\r\n\r\n    this.buildingGroups.forEach(building => {\r\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\r\n        building.floors.forEach(floor => {\r\n          floorsSet.add(floor.floorNumber);\r\n        });\r\n      }\r\n    });\r\n\r\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\r\n  }\r\n\r\n  // 修改：棟別選擇變更處理\r\n  onBuildingChange() {\r\n    // 重置選擇狀態\r\n    this.selectedHouses.forEach(house => house.selected = false);\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n\r\n    // 重置分頁到第一頁\r\n    this.currentPage = 1;\r\n\r\n    // 重置樓層篩選\r\n    this.filterOptions.floorFilter = '';\r\n\r\n    // 更新可用樓層\r\n    this.updateAvailableFloors();\r\n\r\n    // 設定棟別篩選並執行搜尋\r\n    this.filterOptions.buildingFilter = this.selectedBuilding;\r\n    this.onSearch();\r\n  }\r\n\r\n\r\n\r\n  // 新增：取得過濾後的棟別資料\r\n  getFilteredBuildings(): BuildingGroup[] {\r\n    return this.buildingGroups.filter(building => {\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 關鍵字搜尋 (搜尋戶型)\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        const hasMatchingHouse = building.floors.some(floor =>\r\n          floor.houses.some(house =>\r\n            house.CHouseHold.toLowerCase().includes(keyword) ||\r\n            house.CBuildingName.toLowerCase().includes(keyword)\r\n          )\r\n        );\r\n        if (!hasMatchingHouse) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        const hasMatchingStatus = building.floors.some(floor =>\r\n          floor.houses.some(house => this.matchesStatusFilter(house))\r\n        );\r\n        if (!hasMatchingStatus) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        const hasMatchingFloor = building.floors.some(floor =>\r\n          floor.floorNumber === floorNumber\r\n        );\r\n        if (!hasMatchingFloor) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }).map(building => {\r\n      // 對每個棟別，也要篩選其樓層和戶別\r\n      const filteredBuilding = { ...building };\r\n      filteredBuilding.floors = building.floors.filter(floor => {\r\n        // 樓層篩選\r\n        if (this.filterOptions.floorFilter) {\r\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n          if (floor.floorNumber !== floorNumber) {\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 檢查該樓層是否有符合條件的戶別\r\n        const hasValidHouses = floor.houses.some(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return hasValidHouses;\r\n      }).map(floor => {\r\n        // 篩選戶別\r\n        const filteredFloor = { ...floor };\r\n        filteredFloor.houses = floor.houses.filter(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return filteredFloor;\r\n      });\r\n\r\n      return filteredBuilding;\r\n    });\r\n  }\r\n\r\n  // 修復：檢查戶別是否符合狀態篩選\r\n  private matchesStatusFilter(house: ConvertedHouse): boolean {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (this.filterOptions.statusFilter) {\r\n      case 'active':\r\n        return status === 'active';\r\n      case 'pending':\r\n        return status === 'pending';\r\n      case 'expired':\r\n        return status === 'expired';\r\n      case 'not-set':\r\n        return status === 'not-set';\r\n      case 'disabled':\r\n        return status === 'disabled';\r\n      default:\r\n        return true; // 全部狀態\r\n    }\r\n  }\r\n\r\n  // 修復：取得戶別狀態\r\n  private getHouseStatus(house: ConvertedHouse): string {\r\n    if (!house.CHouseId) {\r\n      return 'disabled';\r\n    }\r\n\r\n    // 檢查是否有設定開放時段\r\n    if (!house.CChangeStartDate || !house.CChangeEndDate ||\r\n      house.CChangeStartDate === '' || house.CChangeEndDate === '') {\r\n      return 'not-set';\r\n    }\r\n\r\n    try {\r\n      // 處理日期字串，支援多種格式\r\n      const now = new Date();\r\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n\r\n      // 解析開始日期\r\n      let startDate: Date;\r\n      if (house.CChangeStartDate.includes('T')) {\r\n        startDate = new Date(house.CChangeStartDate);\r\n      } else {\r\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\r\n      }\r\n\r\n      // 解析結束日期\r\n      let endDate: Date;\r\n      if (house.CChangeEndDate.includes('T')) {\r\n        endDate = new Date(house.CChangeEndDate);\r\n      } else {\r\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\r\n      }\r\n\r\n      // 檢查日期有效性\r\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\r\n        console.warn('Invalid date format:', {\r\n          start: house.CChangeStartDate,\r\n          end: house.CChangeEndDate,\r\n          houseId: house.CHouseId\r\n        });\r\n        return 'not-set';\r\n      }\r\n\r\n      // 轉換為日期比較（不含時間）\r\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\r\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\r\n\r\n      // 判斷狀態\r\n      if (today < startDateOnly) {\r\n        return 'pending'; // 待開放\r\n      } else if (today >= startDateOnly && today <= endDateOnly) {\r\n        return 'active'; // 進行中\r\n      } else {\r\n        return 'expired'; // 已過期\r\n      }\r\n    } catch (error) {\r\n      console.error('Error parsing dates:', error, house);\r\n      return 'not-set';\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n\r\n  // 修改：開啟批次設定對話框\r\n  openBatchSetting(building?: BuildingGroup) {\r\n    this.selectedBuildingForBatch = building || null;\r\n\r\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\r\n    const hasSelectedHouses = this.selectedHouses.length > 0;\r\n\r\n    this.batchSettings = {\r\n      startDate: null,\r\n      endDate: null,\r\n      applyToAll: !hasSelectedHouses && !building,\r\n      selectedBuildings: building ? [building.name] : [],\r\n      selectedFloors: [],\r\n      selectedHouses: []\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    if (building) {\r\n      building.floors.forEach(floor => {\r\n        floor.selected = false;\r\n        floor.houses.forEach(house => house.selected = false);\r\n      });\r\n    }\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(this.batchSettingDialog);\r\n  }\r\n\r\n  // 新增：樓層選擇變更處理\r\n  onFloorSelectionChange(floor: FloorGroup) {\r\n    if (floor.selected) {\r\n      floor.houses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          house.selected = true;\r\n        }\r\n      });\r\n    } else {\r\n      floor.houses.forEach(house => house.selected = false);\r\n    }\r\n  }\r\n\r\n  // 修改：批次提交\r\n  onBatchSubmit(ref: any) {\r\n    // 驗證批次設定\r\n    this.valid.clear();\r\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\r\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 收集要更新的房屋\r\n    const housesToUpdate: any[] = [];\r\n\r\n    if (this.batchSettings.applyToAll) {\r\n      // 全部戶別\r\n      this.flattenedHouses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          housesToUpdate.push({\r\n            CHouseId: house.CHouseId,\r\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // 使用已選擇的戶別\r\n      if (this.selectedHouses.length > 0) {\r\n        this.selectedHouses.forEach(house => {\r\n          if (house.CHouseId) {\r\n            housesToUpdate.push({\r\n              CHouseId: house.CHouseId,\r\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n            });\r\n          }\r\n        });\r\n      } else if (this.selectedBuildingForBatch) {\r\n        // 如果沒有選擇的戶別，使用舊的邏輯\r\n        this.selectedBuildingForBatch.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.selected && house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n\r\n    if (housesToUpdate.length === 0) {\r\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\r\n      return;\r\n    }\r\n\r\n    // 調用API進行批次更新\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: housesToUpdate\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\r\n        // 清除選擇狀態\r\n        this.selectedHouses.forEach(house => house.selected = false);\r\n        this.selectedHouses = [];\r\n        this.selectAll = false;\r\n        this.getHouseChangeDate();\r\n        ref.close();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：取得狀態樣式類別\r\n  getStatusClass(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    return `status-${status}`;\r\n  }\r\n\r\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\r\n  openHouseDialog(house: ConvertedHouse) {\r\n    if (house.CHouseId) {\r\n      // 使用現有的openModel方法\r\n      this.openModel(this.dialog, house);\r\n    }\r\n  }\r\n\r\n  // 新增：建立扁平化房屋資料\r\n  buildFlattenedHouses(data: GetHouseChangeDateRes[]) {\r\n    this.flattenedHouses = [];\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || '';\r\n\r\n      household.CHouses?.forEach(house => {\r\n        this.flattenedHouses.push({\r\n          CHouseHold: houseType,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: house.CFloor || 0,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 初始化篩選和分頁\r\n    this.onSearch();\r\n\r\n    // 調試：輸出狀態統計\r\n    this.debugStatusCounts();\r\n  }\r\n\r\n  // 調試：輸出狀態統計\r\n  private debugStatusCounts() {\r\n    const statusCounts = {\r\n      active: 0,\r\n      pending: 0,\r\n      expired: 0,\r\n      'not-set': 0,\r\n      disabled: 0\r\n    };\r\n\r\n    this.flattenedHouses.forEach(house => {\r\n      const status = this.getHouseStatus(house);\r\n      if (statusCounts.hasOwnProperty(status)) {\r\n        statusCounts[status as keyof typeof statusCounts]++;\r\n      }\r\n    });\r\n\r\n    console.log('狀態統計:', statusCounts);\r\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\r\n  }\r\n\r\n  // 新增：視圖模式切換\r\n  setViewMode(mode: 'table' | 'card') {\r\n    this.viewMode = mode;\r\n  }\r\n\r\n  // 修改：搜尋和篩選\r\n  onSearch() {\r\n    // 記錄篩選前的已選擇項目\r\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\r\n\r\n    this.filteredHouses = this.flattenedHouses.filter(house => {\r\n      // 關鍵字搜尋\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n          !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        if (!this.matchesStatusFilter(house)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        if (house.CFloor !== floorNumber) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    });\r\n\r\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\r\n    this.selectedHouses = this.filteredHouses.filter(house =>\r\n      previouslySelectedIds.includes(house.CHouseId)\r\n    );\r\n\r\n    // 更新扁平化資料中的選擇狀態\r\n    this.flattenedHouses.forEach(house => {\r\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\r\n    });\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n\r\n    // 重新計算分頁\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：更新全選狀態\r\n  private updateSelectAllState() {\r\n    if (this.paginatedHouses.length === 0) {\r\n      this.selectAll = false;\r\n    } else {\r\n      this.selectAll = this.paginatedHouses.every(house =>\r\n        !house.CHouseId || house.selected\r\n      );\r\n    }\r\n  }\r\n\r\n  // 修改：更新分頁\r\n  updatePagination() {\r\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\r\n    const startIndex = (this.currentPage - 1) * this.pageSize;\r\n    const endIndex = startIndex + this.pageSize;\r\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 新增：頁面大小變更\r\n  onPageSizeChange() {\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：跳轉頁面\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  // 新增：取得可見頁碼\r\n  getVisiblePages(): number[] {\r\n    const pages: number[] = [];\r\n    const maxVisible = 5;\r\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\r\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\r\n\r\n    if (end - start + 1 < maxVisible) {\r\n      start = Math.max(1, end - maxVisible + 1);\r\n    }\r\n\r\n    for (let i = start; i <= end; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 新增：全選/取消全選\r\n  onSelectAllChange() {\r\n    this.paginatedHouses.forEach(house => {\r\n      if (house.CHouseId) {\r\n        house.selected = this.selectAll;\r\n      }\r\n    });\r\n    this.updateSelectedHouses();\r\n  }\r\n\r\n  // 新增：單一選擇變更\r\n  onHouseSelectionChange() {\r\n    this.updateSelectedHouses();\r\n    this.selectAll = this.paginatedHouses.every(house =>\r\n      !house.CHouseId || house.selected\r\n    );\r\n  }\r\n\r\n  // 新增：更新已選擇房屋列表\r\n  updateSelectedHouses() {\r\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\r\n  }\r\n\r\n  // 新增：排序\r\n  sort(field: string) {\r\n    if (this.sortField === field) {\r\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortDirection = 'asc';\r\n    }\r\n\r\n    this.filteredHouses.sort((a, b) => {\r\n      let aValue = (a as any)[field];\r\n      let bValue = (b as any)[field];\r\n\r\n      // 處理日期排序\r\n      if (field.includes('Date')) {\r\n        aValue = aValue ? new Date(aValue).getTime() : 0;\r\n        bValue = bValue ? new Date(bValue).getTime() : 0;\r\n      }\r\n\r\n      // 處理數字排序\r\n      if (field === 'CFloor') {\r\n        aValue = Number(aValue) || 0;\r\n        bValue = Number(bValue) || 0;\r\n      }\r\n\r\n      // 處理字串排序\r\n      if (typeof aValue === 'string') {\r\n        aValue = aValue.toLowerCase();\r\n        bValue = bValue.toLowerCase();\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return this.sortDirection === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return this.sortDirection === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：TrackBy函數\r\n  trackByHouseId(_index: number, house: ConvertedHouse): number {\r\n    return house.CHouseId;\r\n  }\r\n\r\n  // 新增：取得狀態文字\r\n  getStatusText(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (status) {\r\n      case 'active':\r\n        return '進行中';\r\n      case 'pending':\r\n        return '待開放';\r\n      case 'expired':\r\n        return '已過期';\r\n      case 'not-set':\r\n        return '未設定';\r\n      case 'disabled':\r\n        return '已停用';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  // 新增：匯出資料\r\n  exportData() {\r\n    // 實現匯出功能\r\n    const csvContent = this.generateCSV();\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  // 新增：產生CSV內容\r\n  private generateCSV(): string {\r\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\r\n    const rows = this.filteredHouses.map(house => [\r\n      house.CHouseHold,\r\n      house.CBuildingName,\r\n      `${house.CFloor}F`,\r\n      house.CChangeStartDate || '未設定',\r\n      house.CChangeEndDate || '未設定',\r\n      this.getStatusText(house)\r\n    ]);\r\n\r\n    const csvContent = [headers, ...rows]\r\n      .map(row => row.map(cell => `\"${cell}\"`).join(','))\r\n      .join('\\n');\r\n\r\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <!-- 整合的Header區域 -->\r\n    <div class=\"integrated-header\">\r\n      <!-- 頁面標題和描述 -->\r\n      <div class=\"page-header mb-4\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div>\r\n            <h1 class=\"page-title\">選樣開放時段設定</h1>\r\n            <p class=\"page-description text-muted mb-0\">\r\n              您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。\r\n            </p>\r\n          </div>\r\n          <div class=\"current-info text-end\" *ngIf=\"searchQuery.CBuildCaseSelected\">\r\n            <div class=\"current-case text-white\">\r\n              <i class=\"fas fa-building me-1\"></i>\r\n              {{ searchQuery.CBuildCaseSelected.CBuildCaseName }}\r\n            </div>\r\n            <div class=\"data-count text-white-50 small\" *ngIf=\"flattenedHouses.length > 0\">\r\n              共 {{ flattenedHouses.length }} 筆資料\r\n              <span *ngIf=\"selectedHouses.length > 0\" class=\"text-warning ms-1\">\r\n                (已選 {{ selectedHouses.length }} 筆)\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要查詢條件 -->\r\n      <div class=\"primary-filters mb-3\">\r\n        <div class=\"row align-items-end\">\r\n          <div class=\"col-lg-3 col-md-4 mb-2\">\r\n            <label class=\"form-label\">建案</label>\r\n            <nb-select placeholder=\"請選擇建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\"\r\n              (selectedChange)=\"onBuildCaseChange()\">\r\n              <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n                {{ case.CBuildCaseName }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3 mb-2\">\r\n            <label class=\"form-label\">棟別</label>\r\n            <nb-select placeholder=\"全部棟別\" [(ngModel)]=\"selectedBuilding\" (selectedChange)=\"onBuildingChange()\">\r\n              <nb-option value=\"\">全部棟別</nb-option>\r\n              <nb-option *ngFor=\"let building of buildingOptions\" [value]=\"building\">\r\n                {{ building }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-4 col-md-5 mb-2\">\r\n            <label class=\"form-label\">開放日期範圍</label>\r\n            <div class=\"d-flex align-items-center\">\r\n              <nb-form-field class=\"flex-fill\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"開始日期\" [nbDatepicker]=\"StartDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n                <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n              <span class=\"mx-2\">~</span>\r\n              <nb-form-field class=\"flex-fill\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"結束日期\" [nbDatepicker]=\"EndDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n                <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12 mb-2\">\r\n            <div class=\"d-flex align-items-center justify-content-end h-100\">\r\n              <button class=\"btn btn-primary btn-sm me-2\" (click)=\"getHouseChangeDate()\" [disabled]=\"loading\">\r\n                <i class=\"fas fa-search me-1\"></i>查詢\r\n              </button>\r\n              <button class=\"btn btn-success btn-sm\" (click)=\"openBatchSetting()\" [disabled]=\"!flattenedHouses.length\">\r\n                <i class=\"fas fa-cogs me-1\"></i>批次設定\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 顯示模式選項 -->\r\n        <div class=\"row mt-2\">\r\n          <div class=\"col-md-6\">\r\n            <div class=\"display-mode-options\">\r\n              <label class=\"form-label me-3\">顯示模式</label>\r\n              <div class=\"form-check form-check-inline\">\r\n                <input class=\"form-check-input\" type=\"radio\" name=\"displayMode\" id=\"timeMode\" [value]=\"true\"\r\n                  [(ngModel)]=\"isStatus\">\r\n                <label class=\"form-check-label\" for=\"timeMode\">依開放時段顯示</label>\r\n              </div>\r\n              <div class=\"form-check form-check-inline\">\r\n                <input class=\"form-check-input\" type=\"radio\" name=\"displayMode\" id=\"statusMode\" [value]=\"false\"\r\n                  [(ngModel)]=\"isStatus\">\r\n                <label class=\"form-check-label\" for=\"statusMode\">依開放狀態顯示</label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 進階篩選和操作區域 -->\r\n      <div class=\"advanced-filters\" *ngIf=\"flattenedHouses.length > 0\">\r\n        <div class=\"row align-items-center\">\r\n          <div class=\"col-lg-3 col-md-4 mb-2\">\r\n            <nb-form-field>\r\n              <nb-icon nbPrefix icon=\"search-outline\"></nb-icon>\r\n              <input nbInput placeholder=\"搜尋戶型、棟別...\" [(ngModel)]=\"filterOptions.searchKeyword\"\r\n                (ngModelChange)=\"onSearch()\">\r\n            </nb-form-field>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3 mb-2\">\r\n            <nb-select placeholder=\"狀態篩選\" [(ngModel)]=\"filterOptions.statusFilter\" (selectedChange)=\"onSearch()\">\r\n              <nb-option value=\"\">全部狀態</nb-option>\r\n              <nb-option value=\"active\">進行中</nb-option>\r\n              <nb-option value=\"pending\">待開放</nb-option>\r\n              <nb-option value=\"expired\">已過期</nb-option>\r\n              <nb-option value=\"not-set\">未設定</nb-option>\r\n              <nb-option value=\"disabled\">已停用</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3 mb-2\">\r\n            <nb-select placeholder=\"樓層篩選\" [(ngModel)]=\"filterOptions.floorFilter\" (selectedChange)=\"onSearch()\">\r\n              <nb-option value=\"\">全部樓層</nb-option>\r\n              <nb-option *ngFor=\"let floor of availableFloors\" [value]=\"floor\">\r\n                {{ floor }}F\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-2 mb-2\">\r\n            <nb-select placeholder=\"顯示筆數\" [(ngModel)]=\"pageSize\" (selectedChange)=\"onPageSizeChange()\">\r\n              <nb-option [value]=\"50\">50筆/頁</nb-option>\r\n              <nb-option [value]=\"100\">100筆/頁</nb-option>\r\n              <nb-option [value]=\"200\">200筆/頁</nb-option>\r\n              <nb-option [value]=\"500\">500筆/頁</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12 mb-2\">\r\n            <div class=\"d-flex justify-content-between align-items-center\">\r\n              <div class=\"data-summary\">\r\n                <span class=\"text-muted small\">\r\n                  共 {{ filteredHouses.length }} 筆資料\r\n                  <span *ngIf=\"selectedHouses.length > 0\" class=\"text-primary ms-1\">\r\n                    (已選 {{ selectedHouses.length }} 筆)\r\n                  </span>\r\n                </span>\r\n              </div>\r\n              <div class=\"view-toggle\">\r\n                <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n                  <button type=\"button\" class=\"btn\" [class.btn-primary]=\"viewMode === 'table'\"\r\n                    [class.btn-outline-primary]=\"viewMode !== 'table'\" (click)=\"setViewMode('table')\">\r\n                    <i class=\"fas fa-table\"></i> 表格\r\n                  </button>\r\n                  <button type=\"button\" class=\"btn\" [class.btn-primary]=\"viewMode === 'card'\"\r\n                    [class.btn-outline-primary]=\"viewMode !== 'card'\" (click)=\"setViewMode('card')\">\r\n                    <i class=\"fas fa-th\"></i> 卡片\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 表格視圖 -->\r\n    <div class=\"table-view mt-4\" *ngIf=\"viewMode === 'table' && flattenedHouses.length > 0\">\r\n      <!-- 工具列 -->\r\n      <div class=\"table-toolbar mb-3\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"batch-actions\">\r\n            <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\">\r\n              全選\r\n            </nb-checkbox>\r\n            <button class=\"btn btn-sm btn-primary ml-2\" [disabled]=\"selectedHouses.length === 0\"\r\n              (click)=\"openBatchSetting()\">\r\n              <i class=\"fas fa-edit\"></i> 批次設定 ({{ selectedHouses.length }})\r\n            </button>\r\n            <button class=\"btn btn-sm btn-success ml-2\" [disabled]=\"filteredHouses.length === 0\" (click)=\"exportData()\">\r\n              <i class=\"fas fa-download\"></i> 匯出\r\n            </button>\r\n          </div>\r\n          <div class=\"pagination-info\">\r\n            顯示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredHouses.length) }}\r\n            / 共 {{ filteredHouses.length }} 筆\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格 -->\r\n      <div class=\"table-container\">\r\n        <table class=\"table table-hover\">\r\n          <thead class=\"table-header\">\r\n            <tr>\r\n              <th width=\"50\">\r\n                <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\"></nb-checkbox>\r\n              </th>\r\n              <th width=\"100\" (click)=\"sort('CHouseHold')\" class=\"sortable\">\r\n                戶型\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CHouseHold' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CHouseHold' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"100\" (click)=\"sort('CBuildingName')\" class=\"sortable\">\r\n                棟別\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CBuildingName' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CBuildingName' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"80\" (click)=\"sort('CFloor')\" class=\"sortable\">\r\n                樓層\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CFloor' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CFloor' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"120\" (click)=\"sort('CChangeStartDate')\" class=\"sortable\">\r\n                開始日期\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CChangeStartDate' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CChangeStartDate' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"120\" (click)=\"sort('CChangeEndDate')\" class=\"sortable\">\r\n                結束日期\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CChangeEndDate' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CChangeEndDate' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"100\">狀態</th>\r\n              <th width=\"80\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let house of paginatedHouses; trackBy: trackByHouseId\"\r\n              [class.table-row-selected]=\"house.selected\">\r\n              <td>\r\n                <nb-checkbox [(ngModel)]=\"house.selected\" [disabled]=\"!house.CHouseId\"\r\n                  (ngModelChange)=\"onHouseSelectionChange()\"></nb-checkbox>\r\n              </td>\r\n              <td>{{ house.CHouseHold }}</td>\r\n              <td>{{ house.CBuildingName }}</td>\r\n              <td>{{ house.CFloor }}F</td>\r\n              <td>\r\n                <span *ngIf=\"house.CChangeStartDate\" class=\"date-display\">\r\n                  {{ house.CChangeStartDate | date:'yyyy-MM-dd' }}\r\n                </span>\r\n                <span *ngIf=\"!house.CChangeStartDate\" class=\"text-muted\">未設定</span>\r\n              </td>\r\n              <td>\r\n                <span *ngIf=\"house.CChangeEndDate\" class=\"date-display\">\r\n                  {{ house.CChangeEndDate | date:'yyyy-MM-dd' }}\r\n                </span>\r\n                <span *ngIf=\"!house.CChangeEndDate\" class=\"text-muted\">未設定</span>\r\n              </td>\r\n              <td>\r\n                <span class=\"status-badge\" [class]=\"getStatusClass(house)\">\r\n                  {{ getStatusText(house) }}\r\n                </span>\r\n              </td>\r\n              <td>\r\n                <button class=\"btn btn-sm btn-outline-primary\" [disabled]=\"!house.CHouseId\"\r\n                  (click)=\"openModel(dialog, house)\">\r\n                  <i class=\"fas fa-edit\"></i>\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 分頁控制 -->\r\n      <div class=\"pagination-container mt-3\" *ngIf=\"totalPages > 1\">\r\n        <nav>\r\n          <ul class=\"pagination justify-content-center\">\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\">首頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\">上一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" *ngFor=\"let page of getVisiblePages()\" [class.active]=\"page === currentPage\">\r\n              <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\"\r\n                [disabled]=\"currentPage === totalPages\">下一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(totalPages)\"\r\n                [disabled]=\"currentPage === totalPages\">末頁</button>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 優化的卡片視圖 -->\r\n    <div class=\"card-view mt-4\" *ngIf=\"viewMode === 'card' && flattenedHouses.length > 0\">\r\n      <div class=\"row\">\r\n        <div class=\"col-lg-3 col-md-4 col-sm-6 mb-3\" *ngFor=\"let house of paginatedHouses; trackBy: trackByHouseId\">\r\n          <nb-card class=\"house-card\" [class.selected]=\"house.selected\" [class.disabled]=\"!house.CHouseId\">\r\n            <nb-card-body class=\"p-3\">\r\n              <div class=\"d-flex justify-content-between align-items-start mb-2\">\r\n                <div class=\"house-info\">\r\n                  <h6 class=\"house-number mb-1\">{{ house.CHouseHold }}</h6>\r\n                  <small class=\"text-muted\">{{ house.CBuildingName }} - {{ house.CFloor }}F</small>\r\n                </div>\r\n                <nb-checkbox [(ngModel)]=\"house.selected\" [disabled]=\"!house.CHouseId\"\r\n                  (ngModelChange)=\"onHouseSelectionChange()\"></nb-checkbox>\r\n              </div>\r\n\r\n              <div class=\"date-info mb-2\">\r\n                <div class=\"date-row\">\r\n                  <small class=\"text-muted\">開始：</small>\r\n                  <span *ngIf=\"house.CChangeStartDate\" class=\"date-value\">\r\n                    {{ house.CChangeStartDate | date:'MM/dd' }}\r\n                  </span>\r\n                  <span *ngIf=\"!house.CChangeStartDate\" class=\"text-muted\">未設定</span>\r\n                </div>\r\n                <div class=\"date-row\">\r\n                  <small class=\"text-muted\">結束：</small>\r\n                  <span *ngIf=\"house.CChangeEndDate\" class=\"date-value\">\r\n                    {{ house.CChangeEndDate | date:'MM/dd' }}\r\n                  </span>\r\n                  <span *ngIf=\"!house.CChangeEndDate\" class=\"text-muted\">未設定</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"d-flex justify-content-between align-items-center\">\r\n                <span class=\"status-badge small\" [class]=\"getStatusClass(house)\">\r\n                  {{ getStatusText(house) }}\r\n                </span>\r\n                <button class=\"btn btn-sm btn-outline-primary\" [disabled]=\"!house.CHouseId\"\r\n                  (click)=\"openModel(dialog, house)\">\r\n                  <i class=\"fas fa-edit\"></i>\r\n                </button>\r\n              </div>\r\n            </nb-card-body>\r\n          </nb-card>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分頁控制 -->\r\n      <div class=\"pagination-container mt-3\" *ngIf=\"totalPages > 1\">\r\n        <nav>\r\n          <ul class=\"pagination justify-content-center\">\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\">首頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\">上一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" *ngFor=\"let page of getVisiblePages()\" [class.active]=\"page === currentPage\">\r\n              <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\"\r\n                [disabled]=\"currentPage === totalPages\">下一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(totalPages)\"\r\n                [disabled]=\"currentPage === totalPages\">末頁</button>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 當沒有資料時顯示 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"flattenedHouses.length === 0 && houseChangeDates.length === 0\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <i class=\"fas fa-info-circle fa-2x mb-3\"></i>\r\n            <p>請選擇建案後查詢資料</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n\r\n    <!-- 載入中狀態 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"loading\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <div class=\"spinner-border\" role=\"status\">\r\n              <span class=\"sr-only\">載入中...</span>\r\n            </div>\r\n            <p class=\"mt-2\">資料載入中，請稍候...</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 批次設定對話框 -->\r\n<ng-template #batchSettingDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:600px; max-height: 90vh\">\r\n    <nb-card-header>\r\n      批次設定\r\n      <span *ngIf=\"selectedHouses.length > 0\" class=\"text-primary\">\r\n        - 已選擇 {{ selectedHouses.length }} 個戶別\r\n      </span>\r\n      <span *ngIf=\"selectedBuildingForBatch && selectedHouses.length === 0\">\r\n        - {{ selectedBuildingForBatch.name }}\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 時間設定 -->\r\n      <div class=\"form-group\">\r\n        <label>開放時間起訖 <span class=\"text-red-600\">*</span></label>\r\n        <div class=\"d-flex align-items-center\">\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchStartDate\"\r\n              [(ngModel)]=\"batchSettings.startDate\">\r\n            <nb-datepicker #batchStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <span class=\"mx-2\">~</span>\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchEndDate\"\r\n              [(ngModel)]=\"batchSettings.endDate\">\r\n            <nb-datepicker #batchEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 選擇範圍 -->\r\n      <div class=\"form-group\">\r\n        <label>適用範圍</label>\r\n        <div class=\"selection-options\">\r\n          <!-- 如果有已選擇的戶別，顯示已選擇的資訊 -->\r\n          <div *ngIf=\"selectedHouses.length > 0\" class=\"alert alert-info\">\r\n            <h6>將套用到已選擇的 {{ selectedHouses.length }} 個戶別：</h6>\r\n            <div class=\"selected-houses-preview\">\r\n              <span *ngFor=\"let house of selectedHouses.slice(0, 10); let i = index\"\r\n                class=\"badge badge-primary mr-1 mb-1\">\r\n                {{ house.CHouseHold }} ({{ house.CBuildingName }}-{{ house.CFloor }}F)\r\n              </span>\r\n              <span *ngIf=\"selectedHouses.length > 10\" class=\"text-muted\">\r\n                ...等 {{ selectedHouses.length - 10 }} 個\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 如果沒有已選擇的戶別，顯示選擇選項 -->\r\n          <div *ngIf=\"selectedHouses.length === 0\">\r\n            <nb-checkbox [(ngModel)]=\"batchSettings.applyToAll\">\r\n              全部戶別 ({{ flattenedHouses.length }} 個)\r\n            </nb-checkbox>\r\n            <div class=\"mt-2\" *ngIf=\"!batchSettings.applyToAll && selectedBuildingForBatch\">\r\n              <div class=\"floor-selection\" *ngFor=\"let floor of selectedBuildingForBatch.floors\">\r\n                <nb-checkbox [(ngModel)]=\"floor.selected\" (ngModelChange)=\"onFloorSelectionChange(floor)\">\r\n                  {{ floor.floorNumber }}F ({{ floor.houses.length }} 戶)\r\n                </nb-checkbox>\r\n                <div class=\"house-selection ml-4\" *ngIf=\"floor.selected\">\r\n                  <nb-checkbox *ngFor=\"let house of floor.houses\" [(ngModel)]=\"house.selected\"\r\n                    [disabled]=\"!house.CHouseId\">\r\n                    {{ house.CHouseHold }} ({{ house.CBuildingName }})\r\n                  </nb-checkbox>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-end\">\r\n      <button class=\"btn btn-secondary me-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onBatchSubmit(ref)\">\r\n        批次設定\r\n        <span *ngIf=\"selectedHouses.length > 0\">({{ selectedHouses.length }} 個戶別)</span>\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      您正在編輯 <span class=\"text-green-600\">{{selectedHouseChangeDate.CHouseHold}} -\r\n        {{selectedHouseChangeDate.CFloor}}F</span> 的選樣開放時段\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cFloorFrom\" class=\"label col-3\">開放時間起訖<span class=\"text-red-600\">*</span>\r\n        </label>\r\n        <nb-form-field class=\"ml-3\">\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeStartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeStartDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeStartDate\">\r\n          <nb-datepicker #changeStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n        <label for=\"CChangeStartDate\" class=\"label col-1\"> ~\r\n        </label>\r\n        <nb-form-field>\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeEndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeEndDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeEndDate\">\r\n          <nb-datepicker #changeEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AAQvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;;;ICOtEC,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,oBAAAC,MAAA,CAAAC,cAAA,CAAAC,MAAA,cACF;;;;;IAJFR,EAAA,CAAAC,cAAA,cAA+E;IAC7ED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAS,UAAA,IAAAC,uDAAA,mBAAkE;IAGpEV,EAAA,CAAAG,YAAA,EAAM;;;;IAJJH,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAK,kBAAA,aAAAC,MAAA,CAAAK,eAAA,CAAAH,MAAA,yBACA;IAAOR,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAA+B;;;;;IANxCR,EADF,CAAAC,cAAA,cAA0E,cACnC;IACnCD,EAAA,CAAAa,SAAA,YAAoC;IACpCb,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAS,UAAA,IAAAK,gDAAA,kBAA+E;IAMjFd,EAAA,CAAAG,YAAA,EAAM;;;;IARFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAS,WAAA,CAAAC,kBAAA,CAAAC,cAAA,MACF;IAC6CjB,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAK,eAAA,CAAAH,MAAA,KAAgC;;;;;IAiB3ER,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAY,UAAA,UAAAM,OAAA,CAAc;IACjElB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAa,OAAA,CAAAD,cAAA,MACF;;;;;IAQAjB,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAY,UAAA,UAAAO,WAAA,CAAkB;IACpEnB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAc,WAAA,MACF;;;;;IAgFAnB,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAY,UAAA,UAAAQ,QAAA,CAAe;IAC9DpB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAe,QAAA,OACF;;;;;IAkBIpB,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,oBAAAC,MAAA,CAAAC,cAAA,CAAAC,MAAA,cACF;;;;;;IA3CNR,EAHN,CAAAC,cAAA,cAAiE,cAC3B,cACE,oBACnB;IACbD,EAAA,CAAAa,SAAA,kBAAkD;IAClDb,EAAA,CAAAC,cAAA,gBAC+B;IADSD,EAAA,CAAAqB,gBAAA,2BAAAC,0EAAAC,MAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAAsB,aAAA,CAAAC,aAAA,EAAAN,MAAA,MAAAjB,MAAA,CAAAsB,aAAA,CAAAC,aAAA,GAAAN,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAAyC;IAC/EvB,EAAA,CAAA+B,UAAA,2BAAAT,0EAAA;MAAAtB,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAAiBxB,MAAA,CAAA0B,QAAA,EAAU;IAAA,EAAC;IAElChC,EAHI,CAAAG,YAAA,EAC+B,EACjB,EACZ;IAGJH,EADF,CAAAC,cAAA,cAAoC,oBACmE;IAAvED,EAAA,CAAAqB,gBAAA,2BAAAY,8EAAAV,MAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAAsB,aAAA,CAAAM,YAAA,EAAAX,MAAA,MAAAjB,MAAA,CAAAsB,aAAA,CAAAM,YAAA,GAAAX,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAAwC;IAACvB,EAAA,CAAA+B,UAAA,4BAAAI,+EAAA;MAAAnC,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAAkBxB,MAAA,CAAA0B,QAAA,EAAU;IAAA,EAAC;IAClGhC,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,qBAA0B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA4B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAAoC,qBACkE;IAAtED,EAAA,CAAAqB,gBAAA,2BAAAe,+EAAAb,MAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAAsB,aAAA,CAAAS,WAAA,EAAAd,MAAA,MAAAjB,MAAA,CAAAsB,aAAA,CAAAS,WAAA,GAAAd,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAAuC;IAACvB,EAAA,CAAA+B,UAAA,4BAAAO,gFAAA;MAAAtC,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAAkBxB,MAAA,CAAA0B,QAAA,EAAU;IAAA,EAAC;IACjGhC,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAS,UAAA,KAAA8B,uDAAA,wBAAiE;IAIrEvC,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,eAAoC,qBACyD;IAA7DD,EAAA,CAAAqB,gBAAA,2BAAAmB,+EAAAjB,MAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAAmC,QAAA,EAAAlB,MAAA,MAAAjB,MAAA,CAAAmC,QAAA,GAAAlB,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAAsB;IAACvB,EAAA,CAAA+B,UAAA,4BAAAW,gFAAA;MAAA1C,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAAkBxB,MAAA,CAAAqC,gBAAA,EAAkB;IAAA,EAAC;IACxF3C,EAAA,CAAAC,cAAA,qBAAwB;IAAAD,EAAA,CAAAE,MAAA,uBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAKAH,EAHN,CAAAC,cAAA,eAAqC,eAC4B,eACnC,gBACO;IAC7BD,EAAA,CAAAE,MAAA,IACA;IAAAF,EAAA,CAAAS,UAAA,KAAAmC,kDAAA,mBAAkE;IAItE5C,EADE,CAAAG,YAAA,EAAO,EACH;IAGFH,EAFJ,CAAAC,cAAA,eAAyB,eAC0B,kBAEqC;IAA/BD,EAAA,CAAA+B,UAAA,mBAAAc,oEAAA;MAAA7C,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAwC,WAAA,CAAY,OAAO,CAAC;IAAA,EAAC;IACjF9C,EAAA,CAAAa,SAAA,aAA4B;IAACb,EAAA,CAAAE,MAAA,sBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACkF;IAA9BD,EAAA,CAAA+B,UAAA,mBAAAgB,oEAAA;MAAA/C,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAwC,WAAA,CAAY,MAAM,CAAC;IAAA,EAAC;IAC/E9C,EAAA,CAAAa,SAAA,aAAyB;IAACb,EAAA,CAAAE,MAAA,sBAC5B;IAMZF,EANY,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF,EACF;;;;IA3D0CH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAgD,gBAAA,YAAA1C,MAAA,CAAAsB,aAAA,CAAAC,aAAA,CAAyC;IAMrD7B,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAgD,gBAAA,YAAA1C,MAAA,CAAAsB,aAAA,CAAAM,YAAA,CAAwC;IAWxClC,EAAA,CAAAI,SAAA,IAAuC;IAAvCJ,EAAA,CAAAgD,gBAAA,YAAA1C,MAAA,CAAAsB,aAAA,CAAAS,WAAA,CAAuC;IAEtCrC,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAA2C,eAAA,CAAkB;IAOnBjD,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAgD,gBAAA,YAAA1C,MAAA,CAAAmC,QAAA,CAAsB;IACvCzC,EAAA,CAAAI,SAAA,EAAY;IAAZJ,EAAA,CAAAY,UAAA,aAAY;IACZZ,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAY,UAAA,cAAa;IACbZ,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAY,UAAA,cAAa;IACbZ,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAY,UAAA,cAAa;IAQpBZ,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAK,kBAAA,aAAAC,MAAA,CAAA4C,cAAA,CAAA1C,MAAA,yBACA;IAAOR,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAA+B;IAOJR,EAAA,CAAAI,SAAA,GAA0C;IAC1EJ,EADgC,CAAAmD,WAAA,gBAAA7C,MAAA,CAAA8C,QAAA,aAA0C,wBAAA9C,MAAA,CAAA8C,QAAA,aACxB;IAGlBpD,EAAA,CAAAI,SAAA,GAAyC;IACzEJ,EADgC,CAAAmD,WAAA,gBAAA7C,MAAA,CAAA8C,QAAA,YAAyC,wBAAA9C,MAAA,CAAA8C,QAAA,YACxB;;;;;IAmFrDpD,EAAA,CAAAC,cAAA,gBAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAqD,WAAA,OAAAC,QAAA,CAAAC,gBAAA,qBACF;;;;;IACAvD,EAAA,CAAAC,cAAA,gBAAyD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGnEH,EAAA,CAAAC,cAAA,gBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAqD,WAAA,OAAAC,QAAA,CAAAE,cAAA,qBACF;;;;;IACAxD,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAhBjEH,EAHJ,CAAAC,cAAA,SAC8C,SACxC,uBAE2C;IADhCD,EAAA,CAAAqB,gBAAA,2BAAAoC,sFAAAlC,MAAA;MAAA,MAAA+B,QAAA,GAAAtD,EAAA,CAAAwB,aAAA,CAAAkC,GAAA,EAAAC,SAAA;MAAA3D,EAAA,CAAA2B,kBAAA,CAAA2B,QAAA,CAAAM,QAAA,EAAArC,MAAA,MAAA+B,QAAA,CAAAM,QAAA,GAAArC,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAA4B;IACvCvB,EAAA,CAAA+B,UAAA,2BAAA0B,sFAAA;MAAAzD,EAAA,CAAAwB,aAAA,CAAAkC,GAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAAiBxB,MAAA,CAAAuD,sBAAA,EAAwB;IAAA,EAAC;IAC9C7D,EAD+C,CAAAG,YAAA,EAAc,EACxD;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IAIFD,EAHA,CAAAS,UAAA,KAAAqD,wDAAA,oBAA0D,KAAAC,wDAAA,oBAGD;IAC3D/D,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAIFD,EAHA,CAAAS,UAAA,KAAAuD,wDAAA,oBAAwD,KAAAC,wDAAA,oBAGD;IACzDjE,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,UAAI,iBACyD;IACzDD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IAEHH,EADF,CAAAC,cAAA,UAAI,mBAEmC;IAAnCD,EAAA,CAAA+B,UAAA,mBAAAmC,0EAAA;MAAA,MAAAZ,QAAA,GAAAtD,EAAA,CAAAwB,aAAA,CAAAkC,GAAA,EAAAC,SAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,MAAAyC,UAAA,GAAAnE,EAAA,CAAAoE,WAAA;MAAA,OAAApE,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA+D,SAAA,CAAAF,UAAA,EAAAb,QAAA,CAAwB;IAAA,EAAC;IAClCtD,EAAA,CAAAa,SAAA,aAA2B;IAGjCb,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;IA/BHH,EAAA,CAAAmD,WAAA,uBAAAG,QAAA,CAAAM,QAAA,CAA2C;IAE5B5D,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAgD,gBAAA,YAAAM,QAAA,CAAAM,QAAA,CAA4B;IAAC5D,EAAA,CAAAY,UAAA,cAAA0C,QAAA,CAAAgB,QAAA,CAA4B;IAGpEtE,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAuE,iBAAA,CAAAjB,QAAA,CAAAkB,UAAA,CAAsB;IACtBxE,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAuE,iBAAA,CAAAjB,QAAA,CAAAmB,aAAA,CAAyB;IACzBzE,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,kBAAA,KAAAiD,QAAA,CAAAoB,MAAA,MAAmB;IAEd1E,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAY,UAAA,SAAA0C,QAAA,CAAAC,gBAAA,CAA4B;IAG5BvD,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAY,UAAA,UAAA0C,QAAA,CAAAC,gBAAA,CAA6B;IAG7BvD,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAY,UAAA,SAAA0C,QAAA,CAAAE,cAAA,CAA0B;IAG1BxD,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAY,UAAA,UAAA0C,QAAA,CAAAE,cAAA,CAA2B;IAGPxD,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAA2E,UAAA,CAAArE,MAAA,CAAAsE,cAAA,CAAAtB,QAAA,EAA+B;IACxDtD,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuE,aAAA,CAAAvB,QAAA,OACF;IAG+CtD,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAY,UAAA,cAAA0C,QAAA,CAAAgB,QAAA,CAA4B;;;;;;IAqB7EtE,EADF,CAAAC,cAAA,cAAmG,kBAC9C;IAAzBD,EAAA,CAAA+B,UAAA,mBAAA+C,+EAAA;MAAA,MAAAC,QAAA,GAAA/E,EAAA,CAAAwB,aAAA,CAAAwD,IAAA,EAAArB,SAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA2E,QAAA,CAAAF,QAAA,CAAc;IAAA,EAAC;IAAC/E,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFwDH,EAAA,CAAAmD,WAAA,WAAA4B,QAAA,KAAAzE,MAAA,CAAA4E,WAAA,CAAqC;IAC7ClF,EAAA,CAAAI,SAAA,GAAU;IAAVJ,EAAA,CAAAuE,iBAAA,CAAAQ,QAAA,CAAU;;;;;;IAN7D/E,EAJR,CAAAC,cAAA,eAA8D,UACvD,cAC2C,cACe,kBACsB;IAArDD,EAAA,CAAA+B,UAAA,mBAAAoD,0EAAA;MAAAnF,EAAA,CAAAwB,aAAA,CAAA4D,IAAA;MAAA,MAAA9E,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA2E,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAAgCjF,EAAA,CAAAE,MAAA,mBAAE;IACnFF,EADmF,CAAAG,YAAA,EAAS,EACvF;IAEHH,EADF,CAAAC,cAAA,cAA2D,kBACoC;IAAnED,EAAA,CAAA+B,UAAA,mBAAAsD,0EAAA;MAAArF,EAAA,CAAAwB,aAAA,CAAA4D,IAAA;MAAA,MAAA9E,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA2E,QAAA,CAAA3E,MAAA,CAAA4E,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAAgClF,EAAA,CAAAE,MAAA,yBAAG;IAClGF,EADkG,CAAAG,YAAA,EAAS,EACtG;IACLH,EAAA,CAAAS,UAAA,IAAA6E,sDAAA,kBAAmG;IAIjGtF,EADF,CAAAC,cAAA,eAAoE,mBAExB;IADhBD,EAAA,CAAA+B,UAAA,mBAAAwD,2EAAA;MAAAvF,EAAA,CAAAwB,aAAA,CAAA4D,IAAA;MAAA,MAAA9E,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA2E,QAAA,CAAA3E,MAAA,CAAA4E,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IACnBlF,EAAA,CAAAE,MAAA,0BAAG;IAC/CF,EAD+C,CAAAG,YAAA,EAAS,EACnD;IAEHH,EADF,CAAAC,cAAA,eAAoE,mBAExB;IADhBD,EAAA,CAAA+B,UAAA,mBAAAyD,2EAAA;MAAAxF,EAAA,CAAAwB,aAAA,CAAA4D,IAAA;MAAA,MAAA9E,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA2E,QAAA,CAAA3E,MAAA,CAAAmF,UAAA,CAAoB;IAAA,EAAC;IACdzF,EAAA,CAAAE,MAAA,oBAAE;IAIpDF,EAJoD,CAAAG,YAAA,EAAS,EAClD,EACF,EACD,EACF;;;;IAnBsBH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA4E,WAAA,OAAoC;IACRlF,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAY,UAAA,aAAAN,MAAA,CAAA4E,WAAA,OAA8B;IAE1DlF,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA4E,WAAA,OAAoC;IACMlF,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAY,UAAA,aAAAN,MAAA,CAAA4E,WAAA,OAA8B;IAEvDlF,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAoF,eAAA,GAAoB;IAGrC1F,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA4E,WAAA,KAAA5E,MAAA,CAAAmF,UAAA,CAA6C;IAE/DzF,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAY,UAAA,aAAAN,MAAA,CAAA4E,WAAA,KAAA5E,MAAA,CAAAmF,UAAA,CAAuC;IAErBzF,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA4E,WAAA,KAAA5E,MAAA,CAAAmF,UAAA,CAA6C;IAE/DzF,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAY,UAAA,aAAAN,MAAA,CAAA4E,WAAA,KAAA5E,MAAA,CAAAmF,UAAA,CAAuC;;;;;;IAhH3CzF,EALR,CAAAC,cAAA,cAAwF,cAEtD,cACiC,cAClC,sBACkD;IAA9DD,EAAA,CAAAqB,gBAAA,2BAAAsE,gFAAApE,MAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAAoE,GAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAAuF,SAAA,EAAAtE,MAAA,MAAAjB,MAAA,CAAAuF,SAAA,GAAAtE,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAAuB;IAACvB,EAAA,CAAA+B,UAAA,2BAAA4D,gFAAA;MAAA3F,EAAA,CAAAwB,aAAA,CAAAoE,GAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAAiBxB,MAAA,CAAAwF,iBAAA,EAAmB;IAAA,EAAC;IACxE9F,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAC,cAAA,iBAC+B;IAA7BD,EAAA,CAAA+B,UAAA,mBAAAgE,mEAAA;MAAA/F,EAAA,CAAAwB,aAAA,CAAAoE,GAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA0F,gBAAA,EAAkB;IAAA,EAAC;IAC5BhG,EAAA,CAAAa,SAAA,YAA2B;IAACb,EAAA,CAAAE,MAAA,GAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA4G;IAAvBD,EAAA,CAAA+B,UAAA,mBAAAkE,mEAAA;MAAAjG,EAAA,CAAAwB,aAAA,CAAAoE,GAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA4F,UAAA,EAAY;IAAA,EAAC;IACzGlG,EAAA,CAAAa,SAAA,aAA+B;IAACb,EAAA,CAAAE,MAAA,sBAClC;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,MAAA,IAEF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAQIH,EALV,CAAAC,cAAA,eAA6B,iBACM,iBACH,UACtB,cACa,uBAC8D;IAA9DD,EAAA,CAAAqB,gBAAA,2BAAA8E,iFAAA5E,MAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAAoE,GAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAAuF,SAAA,EAAAtE,MAAA,MAAAjB,MAAA,CAAAuF,SAAA,GAAAtE,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAAuB;IAACvB,EAAA,CAAA+B,UAAA,2BAAAoE,iFAAA;MAAAnG,EAAA,CAAAwB,aAAA,CAAAoE,GAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAAiBxB,MAAA,CAAAwF,iBAAA,EAAmB;IAAA,EAAC;IAC5E9F,EAD6E,CAAAG,YAAA,EAAc,EACtF;IACLH,EAAA,CAAAC,cAAA,cAA8D;IAA9CD,EAAA,CAAA+B,UAAA,mBAAAqE,gEAAA;MAAApG,EAAA,CAAAwB,aAAA,CAAAoE,GAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA+F,IAAA,CAAK,YAAY,CAAC;IAAA,EAAC;IAC1CrG,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAa,SAAA,aACoF;IACtFb,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAiE;IAAjDD,EAAA,CAAA+B,UAAA,mBAAAuE,gEAAA;MAAAtG,EAAA,CAAAwB,aAAA,CAAAoE,GAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA+F,IAAA,CAAK,eAAe,CAAC;IAAA,EAAC;IAC7CrG,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAa,SAAA,aACuF;IACzFb,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAyD;IAA1CD,EAAA,CAAA+B,UAAA,mBAAAwE,gEAAA;MAAAvG,EAAA,CAAAwB,aAAA,CAAAoE,GAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA+F,IAAA,CAAK,QAAQ,CAAC;IAAA,EAAC;IACrCrG,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAa,SAAA,aACgF;IAClFb,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAoE;IAApDD,EAAA,CAAA+B,UAAA,mBAAAyE,gEAAA;MAAAxG,EAAA,CAAAwB,aAAA,CAAAoE,GAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA+F,IAAA,CAAK,kBAAkB,CAAC;IAAA,EAAC;IAChDrG,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAa,SAAA,aAC0F;IAC5Fb,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAkE;IAAlDD,EAAA,CAAA+B,UAAA,mBAAA0E,gEAAA;MAAAzG,EAAA,CAAAwB,aAAA,CAAAoE,GAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA+F,IAAA,CAAK,gBAAgB,CAAC;IAAA,EAAC;IAC9CrG,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAa,SAAA,aACwF;IAC1Fb,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,eAAe;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAErBF,EAFqB,CAAAG,YAAA,EAAK,EACnB,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAS,UAAA,KAAAiG,gDAAA,oBAC8C;IAkCpD1G,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAGNH,EAAA,CAAAS,UAAA,KAAAkG,iDAAA,qBAA8D;IAuBhE3G,EAAA,CAAAG,YAAA,EAAM;;;;IArHeH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAgD,gBAAA,YAAA1C,MAAA,CAAAuF,SAAA,CAAuB;IAGQ7F,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAY,UAAA,aAAAN,MAAA,CAAAC,cAAA,CAAAC,MAAA,OAAwC;IAEtDR,EAAA,CAAAI,SAAA,GAC9B;IAD8BJ,EAAA,CAAAK,kBAAA,gCAAAC,MAAA,CAAAC,cAAA,CAAAC,MAAA,OAC9B;IAC4CR,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAY,UAAA,aAAAN,MAAA,CAAA4C,cAAA,CAAA1C,MAAA,OAAwC;IAKpFR,EAAA,CAAAI,SAAA,GAEF;IAFEJ,EAAA,CAAA4G,kBAAA,oBAAAtG,MAAA,CAAA4E,WAAA,QAAA5E,MAAA,CAAAmC,QAAA,aAAAnC,MAAA,CAAAuG,IAAA,CAAAC,GAAA,CAAAxG,MAAA,CAAA4E,WAAA,GAAA5E,MAAA,CAAAmC,QAAA,EAAAnC,MAAA,CAAA4C,cAAA,CAAA1C,MAAA,iBAAAF,MAAA,CAAA4C,cAAA,CAAA1C,MAAA,aAEF;IAUmBR,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAgD,gBAAA,YAAA1C,MAAA,CAAAuF,SAAA,CAAuB;IAIb7F,EAAA,CAAAI,SAAA,GAA0E;IAC/FJ,EADqB,CAAAmD,WAAA,eAAA7C,MAAA,CAAAyG,SAAA,qBAAAzG,MAAA,CAAA0G,aAAA,WAA0E,iBAAA1G,MAAA,CAAAyG,SAAA,qBAAAzG,MAAA,CAAA0G,aAAA,YAClB;IAIxDhH,EAAA,CAAAI,SAAA,GAA6E;IAClGJ,EADqB,CAAAmD,WAAA,eAAA7C,MAAA,CAAAyG,SAAA,wBAAAzG,MAAA,CAAA0G,aAAA,WAA6E,iBAAA1G,MAAA,CAAAyG,SAAA,wBAAAzG,MAAA,CAAA0G,aAAA,YAClB;IAI3DhH,EAAA,CAAAI,SAAA,GAAsE;IAC3FJ,EADqB,CAAAmD,WAAA,eAAA7C,MAAA,CAAAyG,SAAA,iBAAAzG,MAAA,CAAA0G,aAAA,WAAsE,iBAAA1G,MAAA,CAAAyG,SAAA,iBAAAzG,MAAA,CAAA0G,aAAA,YAClB;IAIpDhH,EAAA,CAAAI,SAAA,GAAgF;IACrGJ,EADqB,CAAAmD,WAAA,eAAA7C,MAAA,CAAAyG,SAAA,2BAAAzG,MAAA,CAAA0G,aAAA,WAAgF,iBAAA1G,MAAA,CAAAyG,SAAA,2BAAAzG,MAAA,CAAA0G,aAAA,YAClB;IAI9DhH,EAAA,CAAAI,SAAA,GAA8E;IACnGJ,EADqB,CAAAmD,WAAA,eAAA7C,MAAA,CAAAyG,SAAA,yBAAAzG,MAAA,CAAA0G,aAAA,WAA8E,iBAAA1G,MAAA,CAAAyG,SAAA,yBAAAzG,MAAA,CAAA0G,aAAA,YAClB;IAOjEhH,EAAA,CAAAI,SAAA,GAAoB;IAAAJ,EAApB,CAAAY,UAAA,YAAAN,MAAA,CAAA2G,eAAA,CAAoB,iBAAA3G,MAAA,CAAA4G,cAAA,CAAuB;IAsC/BlH,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAmF,UAAA,KAAoB;;;;;IA2ChDzF,EAAA,CAAAC,cAAA,gBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAqD,WAAA,OAAA8D,SAAA,CAAA5D,gBAAA,gBACF;;;;;IACAvD,EAAA,CAAAC,cAAA,gBAAyD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAInEH,EAAA,CAAAC,cAAA,gBAAsD;IACpDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAqD,WAAA,OAAA8D,SAAA,CAAA3D,cAAA,gBACF;;;;;IACAxD,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IApBjEH,EALV,CAAAC,cAAA,eAA4G,mBACT,wBACrE,eAC2C,eACzC,cACQ;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAC3EF,EAD2E,CAAAG,YAAA,EAAQ,EAC7E;IACNH,EAAA,CAAAC,cAAA,uBAC6C;IADhCD,EAAA,CAAAqB,gBAAA,2BAAA+F,sFAAA7F,MAAA;MAAA,MAAA4F,SAAA,GAAAnH,EAAA,CAAAwB,aAAA,CAAA6F,IAAA,EAAA1D,SAAA;MAAA3D,EAAA,CAAA2B,kBAAA,CAAAwF,SAAA,CAAAvD,QAAA,EAAArC,MAAA,MAAA4F,SAAA,CAAAvD,QAAA,GAAArC,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAA4B;IACvCvB,EAAA,CAAA+B,UAAA,2BAAAqF,sFAAA;MAAApH,EAAA,CAAAwB,aAAA,CAAA6F,IAAA;MAAA,MAAA/G,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAAiBxB,MAAA,CAAAuD,sBAAA,EAAwB;IAAA,EAAC;IAC9C7D,EAD+C,CAAAG,YAAA,EAAc,EACvD;IAIFH,EAFJ,CAAAC,cAAA,gBAA4B,gBACJ,kBACM;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAIrCH,EAHA,CAAAS,UAAA,KAAA6G,wDAAA,oBAAwD,KAAAC,wDAAA,oBAGC;IAC3DvH,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,gBAAsB,kBACM;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAIrCH,EAHA,CAAAS,UAAA,KAAA+G,wDAAA,oBAAsD,KAAAC,wDAAA,oBAGC;IAE3DzH,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA+D,iBACI;IAC/DD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,mBACqC;IAAnCD,EAAA,CAAA+B,UAAA,mBAAA2F,0EAAA;MAAA,MAAAP,SAAA,GAAAnH,EAAA,CAAAwB,aAAA,CAAA6F,IAAA,EAAA1D,SAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,MAAAyC,UAAA,GAAAnE,EAAA,CAAAoE,WAAA;MAAA,OAAApE,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA+D,SAAA,CAAAF,UAAA,EAAAgD,SAAA,CAAwB;IAAA,EAAC;IAClCnH,EAAA,CAAAa,SAAA,aAA2B;IAKrCb,EAJQ,CAAAG,YAAA,EAAS,EACL,EACO,EACP,EACN;;;;;IAvCwBH,EAAA,CAAAI,SAAA,EAAiC;IAACJ,EAAlC,CAAAmD,WAAA,aAAAgE,SAAA,CAAAvD,QAAA,CAAiC,cAAAuD,SAAA,CAAA7C,QAAA,CAAmC;IAI1DtE,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAuE,iBAAA,CAAA4C,SAAA,CAAA3C,UAAA,CAAsB;IAC1BxE,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAA2H,kBAAA,KAAAR,SAAA,CAAA1C,aAAA,SAAA0C,SAAA,CAAAzC,MAAA,MAA+C;IAE9D1E,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAgD,gBAAA,YAAAmE,SAAA,CAAAvD,QAAA,CAA4B;IAAC5D,EAAA,CAAAY,UAAA,cAAAuG,SAAA,CAAA7C,QAAA,CAA4B;IAO7DtE,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAY,UAAA,SAAAuG,SAAA,CAAA5D,gBAAA,CAA4B;IAG5BvD,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAY,UAAA,UAAAuG,SAAA,CAAA5D,gBAAA,CAA6B;IAI7BvD,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAY,UAAA,SAAAuG,SAAA,CAAA3D,cAAA,CAA0B;IAG1BxD,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAY,UAAA,UAAAuG,SAAA,CAAA3D,cAAA,CAA2B;IAKHxD,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAA2E,UAAA,CAAArE,MAAA,CAAAsE,cAAA,CAAAuC,SAAA,EAA+B;IAC9DnH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuE,aAAA,CAAAsC,SAAA,OACF;IAC+CnH,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAY,UAAA,cAAAuG,SAAA,CAAA7C,QAAA,CAA4B;;;;;;IAqB7EtE,EADF,CAAAC,cAAA,cAAmG,kBAC9C;IAAzBD,EAAA,CAAA+B,UAAA,mBAAA6F,8EAAA;MAAA,MAAAC,QAAA,GAAA7H,EAAA,CAAAwB,aAAA,CAAAsG,IAAA,EAAAnE,SAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA2E,QAAA,CAAA4C,QAAA,CAAc;IAAA,EAAC;IAAC7H,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFwDH,EAAA,CAAAmD,WAAA,WAAA0E,QAAA,KAAAvH,MAAA,CAAA4E,WAAA,CAAqC;IAC7ClF,EAAA,CAAAI,SAAA,GAAU;IAAVJ,EAAA,CAAAuE,iBAAA,CAAAsD,QAAA,CAAU;;;;;;IAN7D7H,EAJR,CAAAC,cAAA,eAA8D,UACvD,cAC2C,cACe,kBACsB;IAArDD,EAAA,CAAA+B,UAAA,mBAAAgG,yEAAA;MAAA/H,EAAA,CAAAwB,aAAA,CAAAwG,IAAA;MAAA,MAAA1H,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA2E,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAAgCjF,EAAA,CAAAE,MAAA,mBAAE;IACnFF,EADmF,CAAAG,YAAA,EAAS,EACvF;IAEHH,EADF,CAAAC,cAAA,cAA2D,kBACoC;IAAnED,EAAA,CAAA+B,UAAA,mBAAAkG,yEAAA;MAAAjI,EAAA,CAAAwB,aAAA,CAAAwG,IAAA;MAAA,MAAA1H,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA2E,QAAA,CAAA3E,MAAA,CAAA4E,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAAgClF,EAAA,CAAAE,MAAA,yBAAG;IAClGF,EADkG,CAAAG,YAAA,EAAS,EACtG;IACLH,EAAA,CAAAS,UAAA,IAAAyH,qDAAA,kBAAmG;IAIjGlI,EADF,CAAAC,cAAA,eAAoE,mBAExB;IADhBD,EAAA,CAAA+B,UAAA,mBAAAoG,0EAAA;MAAAnI,EAAA,CAAAwB,aAAA,CAAAwG,IAAA;MAAA,MAAA1H,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA2E,QAAA,CAAA3E,MAAA,CAAA4E,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IACnBlF,EAAA,CAAAE,MAAA,0BAAG;IAC/CF,EAD+C,CAAAG,YAAA,EAAS,EACnD;IAEHH,EADF,CAAAC,cAAA,eAAoE,mBAExB;IADhBD,EAAA,CAAA+B,UAAA,mBAAAqG,0EAAA;MAAApI,EAAA,CAAAwB,aAAA,CAAAwG,IAAA;MAAA,MAAA1H,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA2E,QAAA,CAAA3E,MAAA,CAAAmF,UAAA,CAAoB;IAAA,EAAC;IACdzF,EAAA,CAAAE,MAAA,oBAAE;IAIpDF,EAJoD,CAAAG,YAAA,EAAS,EAClD,EACF,EACD,EACF;;;;IAnBsBH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA4E,WAAA,OAAoC;IACRlF,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAY,UAAA,aAAAN,MAAA,CAAA4E,WAAA,OAA8B;IAE1DlF,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA4E,WAAA,OAAoC;IACMlF,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAY,UAAA,aAAAN,MAAA,CAAA4E,WAAA,OAA8B;IAEvDlF,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAoF,eAAA,GAAoB;IAGrC1F,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA4E,WAAA,KAAA5E,MAAA,CAAAmF,UAAA,CAA6C;IAE/DzF,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAY,UAAA,aAAAN,MAAA,CAAA4E,WAAA,KAAA5E,MAAA,CAAAmF,UAAA,CAAuC;IAErBzF,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAmD,WAAA,aAAA7C,MAAA,CAAA4E,WAAA,KAAA5E,MAAA,CAAAmF,UAAA,CAA6C;IAE/DzF,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAY,UAAA,aAAAN,MAAA,CAAA4E,WAAA,KAAA5E,MAAA,CAAAmF,UAAA,CAAuC;;;;;IA/DjDzF,EADF,CAAAC,cAAA,eAAsF,eACnE;IACfD,EAAA,CAAAS,UAAA,IAAA4H,gDAAA,qBAA4G;IAyC9GrI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAS,UAAA,IAAA6H,gDAAA,qBAA8D;IAuBhEtI,EAAA,CAAAG,YAAA,EAAM;;;;IAnE6DH,EAAA,CAAAI,SAAA,GAAoB;IAAAJ,EAApB,CAAAY,UAAA,YAAAN,MAAA,CAAA2G,eAAA,CAAoB,iBAAA3G,MAAA,CAAA4G,cAAA,CAAuB;IA4CpElH,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAmF,UAAA,KAAoB;;;;;IA6BxDzF,EAHN,CAAAC,cAAA,eAAoG,cACzF,mBACO,eACY;IACtBD,EAAA,CAAAa,SAAA,aAA6C;IAC7Cb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IAIrBF,EAJqB,CAAAG,YAAA,EAAI,EACb,EACO,EACP,EACN;;;;;IAQIH,EALV,CAAAC,cAAA,eAA8C,cACnC,mBACO,eACY,eACoB,gBAClB;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAE,MAAA,gEAAY;IAIpCF,EAJoC,CAAAG,YAAA,EAAI,EAC5B,EACO,EACP,EACN;;;;;IASJH,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,2BAAAC,MAAA,CAAAC,cAAA,CAAAC,MAAA,yBACF;;;;;IACAR,EAAA,CAAAC,cAAA,WAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,QAAAC,MAAA,CAAAiI,wBAAA,CAAAC,IAAA,MACF;;;;;IA+BQxI,EAAA,CAAAC,cAAA,gBACwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA4G,kBAAA,MAAA6B,SAAA,CAAAjE,UAAA,QAAAiE,SAAA,CAAAhE,aAAA,OAAAgE,SAAA,CAAA/D,MAAA,QACF;;;;;IACA1E,EAAA,CAAAC,cAAA,gBAA4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,gBAAAC,MAAA,CAAAC,cAAA,CAAAC,MAAA,kBACF;;;;;IARFR,EADF,CAAAC,cAAA,eAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,eAAqC;IAKnCD,EAJA,CAAAS,UAAA,IAAAiI,gEAAA,oBACwC,IAAAC,gEAAA,oBAGoB;IAIhE3I,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAVAH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,kBAAA,sDAAAC,MAAA,CAAAC,cAAA,CAAAC,MAAA,8BAAyC;IAEnBR,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAC,cAAA,CAAAqI,KAAA,QAAgC;IAIjD5I,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAC,cAAA,CAAAC,MAAA,MAAgC;;;;;;IAiBnCR,EAAA,CAAAC,cAAA,uBAC+B;IADiBD,EAAA,CAAAqB,gBAAA,2BAAAwH,+HAAAtH,MAAA;MAAA,MAAAuH,SAAA,GAAA9I,EAAA,CAAAwB,aAAA,CAAAuH,IAAA,EAAApF,SAAA;MAAA3D,EAAA,CAAA2B,kBAAA,CAAAmH,SAAA,CAAAlF,QAAA,EAAArC,MAAA,MAAAuH,SAAA,CAAAlF,QAAA,GAAArC,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAA4B;IAE1EvB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;;;;IAHkCH,EAAA,CAAAgD,gBAAA,YAAA8F,SAAA,CAAAlF,QAAA,CAA4B;IAC1E5D,EAAA,CAAAY,UAAA,cAAAkI,SAAA,CAAAxE,QAAA,CAA4B;IAC5BtE,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA2H,kBAAA,MAAAmB,SAAA,CAAAtE,UAAA,QAAAsE,SAAA,CAAArE,aAAA,OACF;;;;;IAJFzE,EAAA,CAAAC,cAAA,eAAyD;IACvDD,EAAA,CAAAS,UAAA,IAAAuI,yFAAA,2BAC+B;IAGjChJ,EAAA,CAAAG,YAAA,EAAM;;;;IAJ2BH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAY,UAAA,YAAAqI,SAAA,CAAAC,MAAA,CAAe;;;;;;IAJhDlJ,EADF,CAAAC,cAAA,eAAmF,sBACS;IAA7ED,EAAA,CAAAqB,gBAAA,2BAAA8H,2GAAA5H,MAAA;MAAA,MAAA0H,SAAA,GAAAjJ,EAAA,CAAAwB,aAAA,CAAA4H,IAAA,EAAAzF,SAAA;MAAA3D,EAAA,CAAA2B,kBAAA,CAAAsH,SAAA,CAAArF,QAAA,EAAArC,MAAA,MAAA0H,SAAA,CAAArF,QAAA,GAAArC,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAA4B;IAACvB,EAAA,CAAA+B,UAAA,2BAAAoH,2GAAA;MAAA,MAAAF,SAAA,GAAAjJ,EAAA,CAAAwB,aAAA,CAAA4H,IAAA,EAAAzF,SAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAAiBxB,MAAA,CAAA+I,sBAAA,CAAAJ,SAAA,CAA6B;IAAA,EAAC;IACvFjJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAS,UAAA,IAAA6I,2EAAA,mBAAyD;IAM3DtJ,EAAA,CAAAG,YAAA,EAAM;;;;IATSH,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAgD,gBAAA,YAAAiG,SAAA,CAAArF,QAAA,CAA4B;IACvC5D,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA2H,kBAAA,MAAAsB,SAAA,CAAAM,WAAA,SAAAN,SAAA,CAAAC,MAAA,CAAA1I,MAAA,cACF;IACmCR,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAY,UAAA,SAAAqI,SAAA,CAAArF,QAAA,CAAoB;;;;;IAL3D5D,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAS,UAAA,IAAA+I,qEAAA,mBAAmF;IAWrFxJ,EAAA,CAAAG,YAAA,EAAM;;;;IAX2CH,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAiI,wBAAA,CAAAkB,MAAA,CAAkC;;;;;;IAJnFzJ,EADF,CAAAC,cAAA,UAAyC,sBACa;IAAvCD,EAAA,CAAAqB,gBAAA,2BAAAqI,+FAAAnI,MAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAAmI,IAAA;MAAA,MAAArJ,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAAsJ,aAAA,CAAAC,UAAA,EAAAtI,MAAA,MAAAjB,MAAA,CAAAsJ,aAAA,CAAAC,UAAA,GAAAtI,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAAsC;IACjDvB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAS,UAAA,IAAAqJ,+DAAA,mBAAgF;IAalF9J,EAAA,CAAAG,YAAA,EAAM;;;;IAhBSH,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAgD,gBAAA,YAAA1C,MAAA,CAAAsJ,aAAA,CAAAC,UAAA,CAAsC;IACjD7J,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,gCAAAC,MAAA,CAAAK,eAAA,CAAAH,MAAA,cACF;IACmBR,EAAA,CAAAI,SAAA,EAA2D;IAA3DJ,EAAA,CAAAY,UAAA,UAAAN,MAAA,CAAAsJ,aAAA,CAAAC,UAAA,IAAAvJ,MAAA,CAAAiI,wBAAA,CAA2D;;;;;IAqBlFvI,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAxCH,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAC,MAAA,yBAAiC;;;;;;IA1E7ER,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,iCACA;IAGAF,EAHA,CAAAS,UAAA,IAAAsJ,yDAAA,oBAA6D,IAAAC,yDAAA,oBAGS;IAGxEhK,EAAA,CAAAG,YAAA,EAAiB;IAIbH,EAHJ,CAAAC,cAAA,mBAAc,eAEY,YACf;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAEvDH,EADF,CAAAC,cAAA,eAAuC,yBACJ;IAC/BD,EAAA,CAAAa,SAAA,mBAAoD;IACpDb,EAAA,CAAAC,cAAA,kBACwC;IAAtCD,EAAA,CAAAqB,gBAAA,2BAAA4I,mFAAA1I,MAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAA0I,IAAA;MAAA,MAAA5J,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAAsJ,aAAA,CAAAO,SAAA,EAAA5I,MAAA,MAAAjB,MAAA,CAAAsJ,aAAA,CAAAO,SAAA,GAAA5I,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAAqC;IADvCvB,EAAA,CAAAG,YAAA,EACwC;IACxCH,EAAA,CAAAa,SAAA,4BAAmE;IACrEb,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,yBAAiC;IAC/BD,EAAA,CAAAa,SAAA,mBAAoD;IACpDb,EAAA,CAAAC,cAAA,kBACsC;IAApCD,EAAA,CAAAqB,gBAAA,2BAAA+I,mFAAA7I,MAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAA0I,IAAA;MAAA,MAAA5J,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAAsJ,aAAA,CAAAS,OAAA,EAAA9I,MAAA,MAAAjB,MAAA,CAAAsJ,aAAA,CAAAS,OAAA,GAAA9I,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAAmC;IADrCvB,EAAA,CAAAG,YAAA,EACsC;IACtCH,EAAA,CAAAa,SAAA,4BAAiE;IAGvEb,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAwB,aACf;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnBH,EAAA,CAAAC,cAAA,gBAA+B;IAgB7BD,EAdA,CAAAS,UAAA,KAAA6J,yDAAA,mBAAgE,KAAAC,yDAAA,mBAcvB;IAoB/CvK,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAmD,mBACa;IAAvBD,EAAA,CAAA+B,UAAA,mBAAAyI,4EAAA;MAAA,MAAAC,OAAA,GAAAzK,EAAA,CAAAwB,aAAA,CAAA0I,IAAA,EAAAQ,SAAA;MAAA,MAAApK,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAqK,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAACzK,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzEH,EAAA,CAAAC,cAAA,mBAA6D;IAA7BD,EAAA,CAAA+B,UAAA,mBAAA6I,4EAAA;MAAA,MAAAH,OAAA,GAAAzK,EAAA,CAAAwB,aAAA,CAAA0I,IAAA,EAAAQ,SAAA;MAAA,MAAApK,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAuK,aAAA,CAAAJ,OAAA,CAAkB;IAAA,EAAC;IAC1DzK,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAS,UAAA,KAAAqK,0DAAA,oBAAwC;IAG9C9K,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;;;IA3ECH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAA+B;IAG/BR,EAAA,CAAAI,SAAA,EAA6D;IAA7DJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAiI,wBAAA,IAAAjI,MAAA,CAAAC,cAAA,CAAAC,MAAA,OAA6D;IAWfR,EAAA,CAAAI,SAAA,IAA+B;IAA/BJ,EAAA,CAAAY,UAAA,iBAAAmK,kBAAA,CAA+B;IAC5E/K,EAAA,CAAAgD,gBAAA,YAAA1C,MAAA,CAAAsJ,aAAA,CAAAO,SAAA,CAAqC;IAMQnK,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAY,UAAA,iBAAAoK,gBAAA,CAA6B;IAC1EhL,EAAA,CAAAgD,gBAAA,YAAA1C,MAAA,CAAAsJ,aAAA,CAAAS,OAAA,CAAmC;IAWjCrK,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAA+B;IAc/BR,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAC,cAAA,CAAAC,MAAA,OAAiC;IAyBlCR,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAA+B;;;;;IAO5CR,EAAA,CAAAC,cAAA,mBAA+C;IAM7CD,EALA,CAAAa,SAAA,qBACiB,mBAGF,0BAGE;IACnBb,EAAA,CAAAG,YAAA,EAAU;;;;;;IAKRH,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GACE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mDAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,wBAA2B,eACyB,iBACJ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC7E;IACRH,EAAA,CAAAC,cAAA,0BAA4B;IAC1BD,EAAA,CAAAa,SAAA,mBAAoD;IACpDb,EAAA,CAAAC,cAAA,kBAC8E;IAAvDD,EAAA,CAAAqB,gBAAA,2BAAA4J,mFAAA1J,MAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAA0J,IAAA;MAAA,MAAA5K,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAA6K,uBAAA,CAAA5H,gBAAA,EAAAhC,MAAA,MAAAjB,MAAA,CAAA6K,uBAAA,CAAA5H,gBAAA,GAAAhC,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAAsD;IAD7EvB,EAAA,CAAAG,YAAA,EAC8E;IAC9EH,EAAA,CAAAa,SAAA,4BAAoE;IACtEb,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,kBAAkD;IAACD,EAAA,CAAAE,MAAA,WACnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAa,SAAA,mBAAoD;IACpDb,EAAA,CAAAC,cAAA,kBAC4E;IAArDD,EAAA,CAAAqB,gBAAA,2BAAA+J,mFAAA7J,MAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAA0J,IAAA;MAAA,MAAA5K,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAA6K,uBAAA,CAAA3H,cAAA,EAAAjC,MAAA,MAAAjB,MAAA,CAAA6K,uBAAA,CAAA3H,cAAA,GAAAjC,MAAA;MAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;IAAA,EAAoD;IAD3EvB,EAAA,CAAAG,YAAA,EAC4E;IAC5EH,EAAA,CAAAa,SAAA,4BAAkE;IAGxEb,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAsD,mBACiB;IAAvBD,EAAA,CAAA+B,UAAA,mBAAAsJ,4EAAA;MAAA,MAAAC,OAAA,GAAAtL,EAAA,CAAAwB,aAAA,CAAA0J,IAAA,EAAAR,SAAA;MAAA,MAAApK,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAqK,OAAA,CAAAW,OAAA,CAAY;IAAA,EAAC;IAACtL,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,mBAAoE;IAAxBD,EAAA,CAAA+B,UAAA,mBAAAwJ,4EAAA;MAAA,MAAAD,OAAA,GAAAtL,EAAA,CAAAwB,aAAA,CAAA0J,IAAA,EAAAR,SAAA;MAAA,MAAApK,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAkL,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IAACtL,EAAA,CAAAE,MAAA,oBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAChE,EACT;;;;;;IA3B6BH,EAAA,CAAAI,SAAA,GACE;IADFJ,EAAA,CAAA2H,kBAAA,KAAArH,MAAA,CAAA6K,uBAAA,CAAA3G,UAAA,SAAAlE,MAAA,CAAA6K,uBAAA,CAAAzG,MAAA,MACE;IAQoC1E,EAAA,CAAAI,SAAA,IAAgC;IAAhCJ,EAAA,CAAAY,UAAA,iBAAA6K,mBAAA,CAAgC;IAC9EzL,EAAA,CAAAgD,gBAAA,YAAA1C,MAAA,CAAA6K,uBAAA,CAAA5H,gBAAA,CAAsD;IAOVvD,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAY,UAAA,iBAAA8K,iBAAA,CAA8B;IAC1E1L,EAAA,CAAAgD,gBAAA,YAAA1C,MAAA,CAAA6K,uBAAA,CAAA3H,cAAA,CAAoD;;;ADtbrF,OAAM,MAAOmI,0BAA2B,SAAQ7L,aAAa;EAK3D8L,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA6C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAItD,KAAAC,QAAQ,GAAY,IAAI;IAOxB;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAA3J,eAAe,GAAa,EAAE;IAE9B;IACA,KAAArB,aAAa,GAAkB;MAC7BC,aAAa,EAAE,EAAE;MACjBK,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfwK,cAAc,EAAE;KACjB;IAED;IACA,KAAAjD,aAAa,GAAkB;MAC7BO,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbR,UAAU,EAAE,IAAI;MAChBiD,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBxM,cAAc,EAAE;KACjB;IAED,KAAAgI,wBAAwB,GAAyB,IAAI;IAErD;IACA,KAAAnF,QAAQ,GAAqB,OAAO;IACpC,KAAAzC,eAAe,GAAqB,EAAE;IACtC,KAAAuC,cAAc,GAAqB,EAAE;IACrC,KAAA+D,eAAe,GAAqB,EAAE;IACtC,KAAA1G,cAAc,GAAqB,EAAE;IACrC,KAAAsF,SAAS,GAAY,KAAK;IAC1B,KAAAmH,OAAO,GAAY,KAAK;IAExB;IACA,KAAA9H,WAAW,GAAW,CAAC;IACd,KAAAzC,QAAQ,GAAW,GAAG;IAC/B,KAAAgD,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAsB,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAmB,KAAK;IAErC;IACA,KAAAH,IAAI,GAAGA,IAAI;IApFT,IAAI,CAACsE,uBAAuB,GAAG;MAC7B5H,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBkB,MAAM,EAAEuI,SAAS;MACjBzI,UAAU,EAAE,EAAE;MACdF,QAAQ,EAAE2I;KACX;IAED,IAAI,CAACb,aAAa,CAACc,OAAO,EAAE,CAACC,IAAI,CAC/B3N,GAAG,CAAE4N,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAAChB,eAAe,GAAGe,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAuESC,QAAQA,CAAA;IACf,IAAI,CAACzM,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxByM,qBAAqB,EAAE,IAAI,CAACnB,gBAAgB,CAAC,CAAC,CAAC;MAC/C/I,gBAAgB,EAAE0J,SAAS;MAC3BzJ,cAAc,EAAEyJ;KACjB;IACD,IAAI,CAACS,gBAAgB,EAAE;EACzB;EAEArJ,SAASA,CAACsJ,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACtJ,QAAQ,EAAE;MACjB,IAAI,CAAC6G,uBAAuB,GAAG;QAC7B,GAAGyC,IAAI;QACPrK,gBAAgB,EAAEqK,IAAI,CAACrK,gBAAgB,GAAG,IAAIsK,IAAI,CAACD,IAAI,CAACrK,gBAAgB,CAAC,GAAG0J,SAAS;QACrFzJ,cAAc,EAAEoK,IAAI,CAACpK,cAAc,GAAG,IAAIqK,IAAI,CAACD,IAAI,CAACpK,cAAc,CAAC,GAAGyJ;OACvE;MACD,IAAI,CAACnB,aAAa,CAACgC,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOpO,MAAM,CAACoO,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAzC,QAAQA,CAACmC,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAAClC,KAAK,CAACmC,aAAa,CAAC3N,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACuL,OAAO,CAACqC,aAAa,CAAC,IAAI,CAACpC,KAAK,CAACmC,aAAa,CAAC;MACpD;IACF;IACA,MAAME,KAAK,GAAG;MACZ/J,QAAQ,EAAE,IAAI,CAAC6G,uBAAuB,CAAC7G,QAAQ;MAC/Cf,gBAAgB,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAAC5C,uBAAuB,CAAC5H,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAACuK,UAAU,CAAC,IAAI,CAAC5C,uBAAuB,CAAC3H,cAAc;KAC5E;IAED,IAAI,CAACyI,aAAa,CAACqC,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAACd,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACoB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACzC,OAAO,CAAC0C,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAjB,gBAAgBA,CAAA;IACd,IAAI,CAACxB,iBAAiB,CAAC0C,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAACpB,IAAI,CAC7E3N,GAAG,CAAC4N,GAAG,IAAG;MACR,MAAMyB,OAAO,GAAGzB,GAAG,CAAC0B,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAACrO,MAAM,IAAI4M,GAAG,CAACoB,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChDhO,cAAc,EAAEgO,KAAK,CAAChO,cAAc;UACpCiO,GAAG,EAAED,KAAK,CAACC;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAAC7C,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAI8C,KAAK,GAAG,IAAI,CAACJ,oBAAoB,CAACK,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAAC7C,eAAe,CAAC;UAC1F,IAAI,CAACtL,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC+N,oBAAoB,CAACI,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAACpO,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC+N,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMO,WAAW,GAAG,IAAI,CAACvO,WAAW,EAAEC,kBAAkB,EAAEkO,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACZ,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAACnB,SAAS,EAAE;EACf;EAEAgC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvC,MAAMC,KAAK,GAAGD,KAAK,CAACnL,MAAM;QAC1B,IAAI,CAAC+K,SAAS,CAACK,KAAK,CAAC,EAAE;UAAE;UACvBL,SAAS,CAACK,KAAK,CAAC,GAAG,EAAE;QACvB;QACAL,SAAS,CAACK,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBvL,UAAU,EAAEmL,SAAS,CAACnL,UAAU;UAChCC,aAAa,EAAEoL,KAAK,CAACpL,aAAa,IAAI,KAAK;UAC3CH,QAAQ,EAAEuL,KAAK,CAACvL,QAAQ;UACxBI,MAAM,EAAEmL,KAAK,CAACnL,MAAM;UACpBnB,gBAAgB,EAAEsM,KAAK,CAACtM,gBAAgB;UACxCC,cAAc,EAAEqM,KAAK,CAACrM;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACiG,MAAM,CAACpD,IAAI,CAAC,CAAC2J,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAACzG,MAAM,CAACuF,GAAG,CAAEc,KAAU,IAAI;MAChE,OAAO,IAAI,CAACK,UAAU,CAACnB,GAAG,CAAEW,SAAc,IAAI;QAC5C,MAAME,KAAK,GAAGJ,SAAS,CAACK,KAAK,CAAC,CAACM,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAAC7L,UAAU,KAAKmL,SAAS,CAAC;QAC5F,OAAOE,KAAK,IAAI;UACdrL,UAAU,EAAEmL,SAAS;UACrBlL,aAAa,EAAE,KAAK;UACpBH,QAAQ,EAAE,IAAI;UACdI,MAAM,EAAEoL,KAAK;UACbvM,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO0M,MAAM;EACf;EAEAI,sBAAsBA,CAACd,GAAU;IAC/B,MAAMe,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5ChB,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBc,aAAa,CAACC,GAAG,CAACf,SAAS,CAACnL,UAAU,CAAC;MACvCmL,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvCU,SAAS,CAACG,GAAG,CAACb,KAAK,CAACnL,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAAC+E,MAAM,GAAGkH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLhH,MAAM,EAAEkH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC9P,WAAW,CAACwC,gBAAgB,IAAI,IAAI,CAACxC,WAAW,CAACyC,cAAc,EAAE;MACxE,MAAM2G,SAAS,GAAG,IAAI0D,IAAI,CAAC,IAAI,CAAC9M,WAAW,CAACwC,gBAAgB,CAAC;MAC7D,MAAM8G,OAAO,GAAG,IAAIwD,IAAI,CAAC,IAAI,CAAC9M,WAAW,CAACyC,cAAc,CAAC;MACzD,IAAI2G,SAAS,IAAIE,OAAO,IAAIF,SAAS,GAAGE,OAAO,EAAE;QAC/C,IAAI,CAAC0B,OAAO,CAACqC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEA;EACA0C,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACC,cAAc,EAAE;IACrB;IACA,IAAI,CAACrC,kBAAkB,EAAE;EAC3B;EAEA;EACAqC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACvE,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC/L,eAAe,GAAG,EAAE;IACzB,IAAI,CAACuC,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC+D,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC1G,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAACqB,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBK,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfwK,cAAc,EAAE;KACjB;IAED;IACA,IAAI,CAAChH,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC+G,gBAAgB,GAAG,EAAE;IAE1B;IACA,IAAI,CAAC1H,WAAW,GAAG,CAAC;IACpB,IAAI,CAACO,UAAU,GAAG,CAAC;IAEnB;IACA,IAAI,CAACkH,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC1J,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,CAAC8D,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC5B;EAEA0H,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC,IAAI,CAAC3N,WAAW,CAACC,kBAAkB,EAAEkO,GAAG,EAAE;MAC7C,IAAI,CAAClC,OAAO,GAAG,KAAK;MACpB;IACF;IAEA,IAAI,CAACA,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC6D,cAAc,EAAE;IACrB,IAAI,CAAC5E,aAAa,CAACiF,mCAAmC,CAAC;MACrD3C,IAAI,EAAE;QACJ4C,YAAY,EAAE,IAAI,CAACpQ,WAAW,CAACC,kBAAkB,CAACkO,GAAG;QACrD3L,gBAAgB,EAAE,IAAI,CAACxC,WAAW,CAACwC,gBAAgB,GAAG,IAAI,CAACwK,UAAU,CAAC,IAAI,CAAChN,WAAW,CAACwC,gBAAgB,CAAC,GAAG0J,SAAS;QACpHzJ,cAAc,EAAE,IAAI,CAACzC,WAAW,CAACyC,cAAc,GAAG,IAAI,CAACuK,UAAU,CAAC,IAAI,CAAChN,WAAW,CAACyC,cAAc,CAAC,GAAGyJ;;KAExG,CAAC,CAACM,SAAS,CAACH,GAAG,IAAG;MACjB,IAAI,CAACJ,OAAO,GAAG,KAAK;MACpB,IAAII,GAAG,CAAC0B,OAAO,IAAI1B,GAAG,CAACoB,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACwC,gBAAgB,GAAG5D,GAAG,CAAC0B,OAAO,GAAG1B,GAAG,CAAC0B,OAAO,GAAG,EAAE;QACtD,IAAI1B,GAAG,CAAC0B,OAAO,EAAE;UACf,IAAI,CAACkC,gBAAgB,GAAG,CAAC,GAAG5D,GAAG,CAAC0B,OAAO,CAAC;UACxC,IAAI,CAACwB,sBAAsB,CAAClD,GAAG,CAAC0B,OAAO,CAAC;UACxC,IAAI,CAACmC,mBAAmB,GAAG,IAAI,CAAC1B,8BAA8B,CAACnC,GAAG,CAAC0B,OAAO,CAAC;UAC3E;UACA,IAAI,CAACsC,mBAAmB,CAAChE,GAAG,CAAC0B,OAAO,CAAC;UACrC;UACA,IAAI,CAACuC,oBAAoB,CAACjE,GAAG,CAAC0B,OAAO,CAAC;QACxC;MACF;IACF,CAAC,CAAC;EACJ;EAEA;EACAsC,mBAAmBA,CAACE,IAA6B;IAC/C,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAyC;IAEpEF,IAAI,CAAC5B,OAAO,CAACC,SAAS,IAAG;MACvB,MAAM8B,SAAS,GAAG9B,SAAS,CAACnL,UAAU,IAAI,EAAE,CAAC,CAAC;MAE9CmL,SAAS,CAACC,OAAO,EAAEF,OAAO,CAACG,KAAK,IAAG;QACjC,MAAM6B,YAAY,GAAG7B,KAAK,CAACpL,aAAa,IAAI,KAAK,CAAC,CAAC;QACnD,MAAMqL,KAAK,GAAGD,KAAK,CAACnL,MAAM,IAAI,CAAC;QAE/B,IAAI,CAAC6M,WAAW,CAACI,GAAG,CAACD,YAAY,CAAC,EAAE;UAClCH,WAAW,CAACK,GAAG,CAACF,YAAY,EAAE,IAAIF,GAAG,EAA4B,CAAC;QACpE;QAEA,MAAMK,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACJ,YAAY,CAAE;QAE/C,IAAI,CAACG,QAAQ,CAACF,GAAG,CAAC7B,KAAK,CAAC,EAAE;UACxB+B,QAAQ,CAACD,GAAG,CAAC9B,KAAK,EAAE,EAAE,CAAC;QACzB;QAEA+B,QAAQ,CAACC,GAAG,CAAChC,KAAK,CAAE,CAACC,IAAI,CAAC;UACxBvL,UAAU,EAAEiN,SAAS;UAAE;UACvBhN,aAAa,EAAEiN,YAAY;UAAE;UAC7BpN,QAAQ,EAAEuL,KAAK,CAACvL,QAAQ,IAAI,CAAC;UAC7BI,MAAM,EAAEoL,KAAK;UACbvM,gBAAgB,EAAEsM,KAAK,CAACtM,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEqM,KAAK,CAACrM,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC8I,cAAc,GAAGiE,KAAK,CAACC,IAAI,CAACW,WAAW,CAAC1C,OAAO,EAAE,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC0C,YAAY,EAAEG,QAAQ,CAAC,KAAI;MACvF,MAAMpI,MAAM,GAAiBkH,KAAK,CAACC,IAAI,CAACiB,QAAQ,CAAChD,OAAO,EAAE,CAAC,CACxDxI,IAAI,CAAC,CAAC,CAAC2J,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;MAAA,CAC1BhB,GAAG,CAAC,CAAC,CAACzF,WAAW,EAAEL,MAAM,CAAC,MAAM;QAC/BK,WAAW;QACXL,MAAM,EAAEA,MAAM,CAAC7C,IAAI,CAAC,CAAC2J,CAAC,EAAEC,CAAC,KAAI;UAC3B;UACA,IAAID,CAAC,CAACxL,UAAU,KAAKyL,CAAC,CAACzL,UAAU,EAAE;YACjC,OAAOwL,CAAC,CAACxL,UAAU,CAACuN,aAAa,CAAC9B,CAAC,CAACzL,UAAU,CAAC;UACjD;UACA,OAAOwL,CAAC,CAACtL,MAAM,GAAGuL,CAAC,CAACvL,MAAM;QAC5B,CAAC,CAAC;QACFd,QAAQ,EAAE;OACX,CAAC,CAAC;MAEL,OAAO;QACL4E,IAAI,EAAEkJ,YAAY;QAClBjI,MAAM;QACN7F,QAAQ,EAAE;OACX;IACH,CAAC,CAAC,CAACyC,IAAI,CAAC,CAAC2J,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxH,IAAI,CAACuJ,aAAa,CAAC9B,CAAC,CAACzH,IAAI,CAAC,CAAC;IAE/C;IACA,IAAI,CAACmE,eAAe,GAAG,IAAI,CAACD,cAAc,CAACsC,GAAG,CAACgD,EAAE,IAAIA,EAAE,CAACxJ,IAAI,CAAC;IAC7D,IAAI,CAACyJ,qBAAqB,EAAE;EAC9B;EAEA;EACAA,qBAAqBA,CAAA;IACnB,MAAM1B,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,IAAI,CAAC9D,cAAc,CAACgD,OAAO,CAACwC,QAAQ,IAAG;MACrC,IAAI,CAAC,IAAI,CAACtF,gBAAgB,IAAIsF,QAAQ,CAAC1J,IAAI,KAAK,IAAI,CAACoE,gBAAgB,EAAE;QACrEsF,QAAQ,CAACzI,MAAM,CAACiG,OAAO,CAACI,KAAK,IAAG;UAC9BS,SAAS,CAACG,GAAG,CAACZ,KAAK,CAACvG,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACtG,eAAe,GAAG0N,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC,CAAClK,IAAI,CAAC,CAAC2J,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpE;EAEA;EACAmC,gBAAgBA,CAAA;IACd;IACA,IAAI,CAAC5R,cAAc,CAACmP,OAAO,CAACG,KAAK,IAAIA,KAAK,CAACjM,QAAQ,GAAG,KAAK,CAAC;IAC5D,IAAI,CAACrD,cAAc,GAAG,EAAE;IACxB,IAAI,CAACsF,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAACX,WAAW,GAAG,CAAC;IAEpB;IACA,IAAI,CAACtD,aAAa,CAACS,WAAW,GAAG,EAAE;IAEnC;IACA,IAAI,CAAC4P,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACrQ,aAAa,CAACiL,cAAc,GAAG,IAAI,CAACD,gBAAgB;IACzD,IAAI,CAAC5K,QAAQ,EAAE;EACjB;EAIA;EACAoQ,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC1F,cAAc,CAAC2F,MAAM,CAACH,QAAQ,IAAG;MAC3C;MACA,IAAI,IAAI,CAACtF,gBAAgB,IAAIsF,QAAQ,CAAC1J,IAAI,KAAK,IAAI,CAACoE,gBAAgB,EAAE;QACpE,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAAChL,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMyQ,OAAO,GAAG,IAAI,CAAC1Q,aAAa,CAACC,aAAa,CAAC0Q,WAAW,EAAE;QAC9D,MAAMC,gBAAgB,GAAGN,QAAQ,CAACzI,MAAM,CAACgJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAAC5G,MAAM,CAACuJ,IAAI,CAAC5C,KAAK,IACrBA,KAAK,CAACrL,UAAU,CAAC+N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IAChDzC,KAAK,CAACpL,aAAa,CAAC8N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,CACpD,CACF;QACD,IAAI,CAACE,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC5Q,aAAa,CAACM,YAAY,EAAE;QACnC,MAAMyQ,iBAAiB,GAAGT,QAAQ,CAACzI,MAAM,CAACgJ,IAAI,CAAC3C,KAAK,IAClDA,KAAK,CAAC5G,MAAM,CAACuJ,IAAI,CAAC5C,KAAK,IAAI,IAAI,CAAC+C,mBAAmB,CAAC/C,KAAK,CAAC,CAAC,CAC5D;QACD,IAAI,CAAC8C,iBAAiB,EAAE;UACtB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC/Q,aAAa,CAACS,WAAW,EAAE;QAClC,MAAMkH,WAAW,GAAGsJ,QAAQ,CAAC,IAAI,CAACjR,aAAa,CAACS,WAAW,CAAC;QAC5D,MAAMyQ,gBAAgB,GAAGZ,QAAQ,CAACzI,MAAM,CAACgJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAACvG,WAAW,KAAKA,WAAW,CAClC;QACD,IAAI,CAACuJ,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CAAC9D,GAAG,CAACkD,QAAQ,IAAG;MAChB;MACA,MAAMa,gBAAgB,GAAG;QAAE,GAAGb;MAAQ,CAAE;MACxCa,gBAAgB,CAACtJ,MAAM,GAAGyI,QAAQ,CAACzI,MAAM,CAAC4I,MAAM,CAACvC,KAAK,IAAG;QACvD;QACA,IAAI,IAAI,CAAClO,aAAa,CAACS,WAAW,EAAE;UAClC,MAAMkH,WAAW,GAAGsJ,QAAQ,CAAC,IAAI,CAACjR,aAAa,CAACS,WAAW,CAAC;UAC5D,IAAIyN,KAAK,CAACvG,WAAW,KAAKA,WAAW,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QAEA;QACA,MAAMyJ,cAAc,GAAGlD,KAAK,CAAC5G,MAAM,CAACuJ,IAAI,CAAC5C,KAAK,IAAG;UAC/C;UACA,IAAI,IAAI,CAACjO,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMyQ,OAAO,GAAG,IAAI,CAAC1Q,aAAa,CAACC,aAAa,CAAC0Q,WAAW,EAAE;YAC9D,IAAI,CAAC1C,KAAK,CAACrL,UAAU,CAAC+N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzC,KAAK,CAACpL,aAAa,CAAC8N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAC1Q,aAAa,CAACM,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC0Q,mBAAmB,CAAC/C,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOmD,cAAc;MACvB,CAAC,CAAC,CAAChE,GAAG,CAACc,KAAK,IAAG;QACb;QACA,MAAMmD,aAAa,GAAG;UAAE,GAAGnD;QAAK,CAAE;QAClCmD,aAAa,CAAC/J,MAAM,GAAG4G,KAAK,CAAC5G,MAAM,CAACmJ,MAAM,CAACxC,KAAK,IAAG;UACjD;UACA,IAAI,IAAI,CAACjO,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMyQ,OAAO,GAAG,IAAI,CAAC1Q,aAAa,CAACC,aAAa,CAAC0Q,WAAW,EAAE;YAC9D,IAAI,CAAC1C,KAAK,CAACrL,UAAU,CAAC+N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzC,KAAK,CAACpL,aAAa,CAAC8N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAC1Q,aAAa,CAACM,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC0Q,mBAAmB,CAAC/C,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOoD,aAAa;MACtB,CAAC,CAAC;MAEF,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ;EAEA;EACQH,mBAAmBA,CAAC/C,KAAqB;IAC/C,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;IAEzC,QAAQ,IAAI,CAACjO,aAAa,CAACM,YAAY;MACrC,KAAK,QAAQ;QACX,OAAOgR,MAAM,KAAK,QAAQ;MAC5B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,UAAU;MAC9B;QACE,OAAO,IAAI;MAAE;IACjB;EACF;EAEA;EACQC,cAAcA,CAACtD,KAAqB;IAC1C,IAAI,CAACA,KAAK,CAACvL,QAAQ,EAAE;MACnB,OAAO,UAAU;IACnB;IAEA;IACA,IAAI,CAACuL,KAAK,CAACtM,gBAAgB,IAAI,CAACsM,KAAK,CAACrM,cAAc,IAClDqM,KAAK,CAACtM,gBAAgB,KAAK,EAAE,IAAIsM,KAAK,CAACrM,cAAc,KAAK,EAAE,EAAE;MAC9D,OAAO,SAAS;IAClB;IAEA,IAAI;MACF;MACA,MAAM4P,GAAG,GAAG,IAAIvF,IAAI,EAAE;MACtB,MAAMwF,KAAK,GAAG,IAAIxF,IAAI,CAACuF,GAAG,CAACE,WAAW,EAAE,EAAEF,GAAG,CAACG,QAAQ,EAAE,EAAEH,GAAG,CAACI,OAAO,EAAE,CAAC;MAExE;MACA,IAAIrJ,SAAe;MACnB,IAAI0F,KAAK,CAACtM,gBAAgB,CAACmP,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxCvI,SAAS,GAAG,IAAI0D,IAAI,CAACgC,KAAK,CAACtM,gBAAgB,CAAC;MAC9C,CAAC,MAAM;QACL4G,SAAS,GAAG,IAAI0D,IAAI,CAACgC,KAAK,CAACtM,gBAAgB,GAAG,WAAW,CAAC;MAC5D;MAEA;MACA,IAAI8G,OAAa;MACjB,IAAIwF,KAAK,CAACrM,cAAc,CAACkP,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtCrI,OAAO,GAAG,IAAIwD,IAAI,CAACgC,KAAK,CAACrM,cAAc,CAAC;MAC1C,CAAC,MAAM;QACL6G,OAAO,GAAG,IAAIwD,IAAI,CAACgC,KAAK,CAACrM,cAAc,GAAG,WAAW,CAAC;MACxD;MAEA;MACA,IAAIiQ,KAAK,CAACtJ,SAAS,CAACuJ,OAAO,EAAE,CAAC,IAAID,KAAK,CAACpJ,OAAO,CAACqJ,OAAO,EAAE,CAAC,EAAE;QAC1DC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAE;UACnCC,KAAK,EAAEhE,KAAK,CAACtM,gBAAgB;UAC7BuQ,GAAG,EAAEjE,KAAK,CAACrM,cAAc;UACzBuQ,OAAO,EAAElE,KAAK,CAACvL;SAChB,CAAC;QACF,OAAO,SAAS;MAClB;MAEA;MACA,MAAM0P,aAAa,GAAG,IAAInG,IAAI,CAAC1D,SAAS,CAACmJ,WAAW,EAAE,EAAEnJ,SAAS,CAACoJ,QAAQ,EAAE,EAAEpJ,SAAS,CAACqJ,OAAO,EAAE,CAAC;MAClG,MAAMS,WAAW,GAAG,IAAIpG,IAAI,CAACxD,OAAO,CAACiJ,WAAW,EAAE,EAAEjJ,OAAO,CAACkJ,QAAQ,EAAE,EAAElJ,OAAO,CAACmJ,OAAO,EAAE,CAAC;MAE1F;MACA,IAAIH,KAAK,GAAGW,aAAa,EAAE;QACzB,OAAO,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIX,KAAK,IAAIW,aAAa,IAAIX,KAAK,IAAIY,WAAW,EAAE;QACzD,OAAO,QAAQ,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,OAAO,SAAS,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,EAAErE,KAAK,CAAC;MACnD,OAAO,SAAS;IAClB;EACF;EAEAlF,OAAOA,CAACgD,GAAQ;IACdA,GAAG,CAACgB,KAAK,EAAE;EACb;EAEAT,UAAUA,CAAA;IACR,IAAI,CAAClC,KAAK,CAACmI,KAAK,EAAE;IAClB,IAAI,CAACnI,KAAK,CAACoI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACjJ,uBAAuB,CAAC5H,gBAAgB,CAAC;IAC9E,IAAI,CAACyI,KAAK,CAACoI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACjJ,uBAAuB,CAAC3H,cAAc,CAAC;IAC5E,IAAI,CAACwI,KAAK,CAACqI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAClJ,uBAAuB,CAAC5H,gBAAgB,EAAE,IAAI,CAAC4H,uBAAuB,CAAC3H,cAAc,CAAC;EACtI;EAEA8Q,gBAAgBA,CAAA;IACd,IAAI,CAACnI,MAAM,CAACoI,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAACxT,WAAW,EAAEC,kBAAkB,EAAEkO,GAAG,EAAE,CAAC,CAAC;EACnG;EAEA;EACAlJ,gBAAgBA,CAACkM,QAAwB;IACvC,IAAI,CAAC3J,wBAAwB,GAAG2J,QAAQ,IAAI,IAAI;IAEhD;IACA,MAAMsC,iBAAiB,GAAG,IAAI,CAACjU,cAAc,CAACC,MAAM,GAAG,CAAC;IAExD,IAAI,CAACoJ,aAAa,GAAG;MACnBO,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbR,UAAU,EAAE,CAAC2K,iBAAiB,IAAI,CAACtC,QAAQ;MAC3CpF,iBAAiB,EAAEoF,QAAQ,GAAG,CAACA,QAAQ,CAAC1J,IAAI,CAAC,GAAG,EAAE;MAClDuE,cAAc,EAAE,EAAE;MAClBxM,cAAc,EAAE;KACjB;IAED;IACA,IAAI2R,QAAQ,EAAE;MACZA,QAAQ,CAACzI,MAAM,CAACiG,OAAO,CAACI,KAAK,IAAG;QAC9BA,KAAK,CAAClM,QAAQ,GAAG,KAAK;QACtBkM,KAAK,CAAC5G,MAAM,CAACwG,OAAO,CAACG,KAAK,IAAIA,KAAK,CAACjM,QAAQ,GAAG,KAAK,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAACkI,aAAa,CAACgC,IAAI,CAAC,IAAI,CAAC2G,kBAAkB,CAAC;EAClD;EAEA;EACApL,sBAAsBA,CAACyG,KAAiB;IACtC,IAAIA,KAAK,CAAClM,QAAQ,EAAE;MAClBkM,KAAK,CAAC5G,MAAM,CAACwG,OAAO,CAACG,KAAK,IAAG;QAC3B,IAAIA,KAAK,CAACvL,QAAQ,EAAE;UAClBuL,KAAK,CAACjM,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLkM,KAAK,CAAC5G,MAAM,CAACwG,OAAO,CAACG,KAAK,IAAIA,KAAK,CAACjM,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA;EACAiH,aAAaA,CAAC8C,GAAQ;IACpB;IACA,IAAI,CAAC3B,KAAK,CAACmI,KAAK,EAAE;IAClB,IAAI,CAACnI,KAAK,CAACoI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACxK,aAAa,CAACO,SAAS,CAAC;IAC3D,IAAI,CAAC6B,KAAK,CAACoI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACxK,aAAa,CAACS,OAAO,CAAC;IACzD,IAAI,CAAC2B,KAAK,CAACqI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACzK,aAAa,CAACO,SAAS,EAAE,IAAI,CAACP,aAAa,CAACS,OAAO,CAAC;IAElG,IAAI,IAAI,CAAC2B,KAAK,CAACmC,aAAa,CAAC3N,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACuL,OAAO,CAACqC,aAAa,CAAC,IAAI,CAACpC,KAAK,CAACmC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMuG,cAAc,GAAU,EAAE;IAEhC,IAAI,IAAI,CAAC9K,aAAa,CAACC,UAAU,EAAE;MACjC;MACA,IAAI,CAAClJ,eAAe,CAAC+O,OAAO,CAACG,KAAK,IAAG;QACnC,IAAIA,KAAK,CAACvL,QAAQ,EAAE;UAClBoQ,cAAc,CAAC3E,IAAI,CAAC;YAClBzL,QAAQ,EAAEuL,KAAK,CAACvL,QAAQ;YACxBf,gBAAgB,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAACnE,aAAa,CAACO,SAAS,CAAC;YAC/D3G,cAAc,EAAE,IAAI,CAACuK,UAAU,CAAC,IAAI,CAACnE,aAAa,CAACS,OAAO;WAC3D,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAAC9J,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACD,cAAc,CAACmP,OAAO,CAACG,KAAK,IAAG;UAClC,IAAIA,KAAK,CAACvL,QAAQ,EAAE;YAClBoQ,cAAc,CAAC3E,IAAI,CAAC;cAClBzL,QAAQ,EAAEuL,KAAK,CAACvL,QAAQ;cACxBf,gBAAgB,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAACnE,aAAa,CAACO,SAAS,CAAC;cAC/D3G,cAAc,EAAE,IAAI,CAACuK,UAAU,CAAC,IAAI,CAACnE,aAAa,CAACS,OAAO;aAC3D,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAAC9B,wBAAwB,EAAE;QACxC;QACA,IAAI,CAACA,wBAAwB,CAACkB,MAAM,CAACiG,OAAO,CAACI,KAAK,IAAG;UACnDA,KAAK,CAAC5G,MAAM,CAACwG,OAAO,CAACG,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAACjM,QAAQ,IAAIiM,KAAK,CAACvL,QAAQ,EAAE;cACpCoQ,cAAc,CAAC3E,IAAI,CAAC;gBAClBzL,QAAQ,EAAEuL,KAAK,CAACvL,QAAQ;gBACxBf,gBAAgB,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAACnE,aAAa,CAACO,SAAS,CAAC;gBAC/D3G,cAAc,EAAE,IAAI,CAACuK,UAAU,CAAC,IAAI,CAACnE,aAAa,CAACS,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIqK,cAAc,CAAClU,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAACuL,OAAO,CAACqC,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAACnC,aAAa,CAACqC,oCAAoC,CAAC;MACtDC,IAAI,EAAEmG;KACP,CAAC,CAACnH,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACoB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACzC,OAAO,CAAC0C,aAAa,CAAC,QAAQiG,cAAc,CAAClU,MAAM,WAAW,CAAC;QACpE;QACA,IAAI,CAACD,cAAc,CAACmP,OAAO,CAACG,KAAK,IAAIA,KAAK,CAACjM,QAAQ,GAAG,KAAK,CAAC;QAC5D,IAAI,CAACrD,cAAc,GAAG,EAAE;QACxB,IAAI,CAACsF,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC6I,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA;EACA/J,cAAcA,CAACiL,KAAqB;IAClC,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;IACzC,OAAO,UAAUqD,MAAM,EAAE;EAC3B;EAEA;EACAyB,eAAeA,CAAC9E,KAAqB;IACnC,IAAIA,KAAK,CAACvL,QAAQ,EAAE;MAClB;MACA,IAAI,CAACD,SAAS,CAAC,IAAI,CAACuQ,MAAM,EAAE/E,KAAK,CAAC;IACpC;EACF;EAEA;EACAwB,oBAAoBA,CAACC,IAA6B;IAChD,IAAI,CAAC3Q,eAAe,GAAG,EAAE;IAEzB2Q,IAAI,CAAC5B,OAAO,CAACC,SAAS,IAAG;MACvB,MAAM8B,SAAS,GAAG9B,SAAS,CAACnL,UAAU,IAAI,EAAE;MAE5CmL,SAAS,CAACC,OAAO,EAAEF,OAAO,CAACG,KAAK,IAAG;QACjC,IAAI,CAAClP,eAAe,CAACoP,IAAI,CAAC;UACxBvL,UAAU,EAAEiN,SAAS;UACrBhN,aAAa,EAAEoL,KAAK,CAACpL,aAAa,IAAI,KAAK;UAC3CH,QAAQ,EAAEuL,KAAK,CAACvL,QAAQ,IAAI,CAAC;UAC7BI,MAAM,EAAEmL,KAAK,CAACnL,MAAM,IAAI,CAAC;UACzBnB,gBAAgB,EAAEsM,KAAK,CAACtM,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEqM,KAAK,CAACrM,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC5B,QAAQ,EAAE;IAEf;IACA,IAAI,CAAC6S,iBAAiB,EAAE;EAC1B;EAEA;EACQA,iBAAiBA,CAAA;IACvB,MAAMC,YAAY,GAAG;MACnBC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACV,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAACvU,eAAe,CAAC+O,OAAO,CAACG,KAAK,IAAG;MACnC,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;MACzC,IAAIiF,YAAY,CAACK,cAAc,CAACjC,MAAM,CAAC,EAAE;QACvC4B,YAAY,CAAC5B,MAAmC,CAAC,EAAE;MACrD;IACF,CAAC,CAAC;IAEFS,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAEN,YAAY,CAAC;IAClCnB,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAE,IAAIvH,IAAI,EAAE,CAACwH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;EAEA;EACAxS,WAAWA,CAACyS,IAAsB;IAChC,IAAI,CAACnS,QAAQ,GAAGmS,IAAI;EACtB;EAEA;EACAvT,QAAQA,CAAA;IACN;IACA,MAAMwT,qBAAqB,GAAG,IAAI,CAACjV,cAAc,CAACyO,GAAG,CAACa,KAAK,IAAIA,KAAK,CAACvL,QAAQ,CAAC;IAE9E,IAAI,CAACpB,cAAc,GAAG,IAAI,CAACvC,eAAe,CAAC0R,MAAM,CAACxC,KAAK,IAAG;MACxD;MACA,IAAI,IAAI,CAACjO,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMyQ,OAAO,GAAG,IAAI,CAAC1Q,aAAa,CAACC,aAAa,CAAC0Q,WAAW,EAAE;QAC9D,IAAI,CAAC1C,KAAK,CAACrL,UAAU,CAAC+N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzC,KAAK,CAACpL,aAAa,CAAC8N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;UACtD,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC1F,gBAAgB,IAAIiD,KAAK,CAACpL,aAAa,KAAK,IAAI,CAACmI,gBAAgB,EAAE;QAC1E,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAAChL,aAAa,CAACM,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAAC0Q,mBAAmB,CAAC/C,KAAK,CAAC,EAAE;UACpC,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACjO,aAAa,CAACS,WAAW,EAAE;QAClC,MAAMkH,WAAW,GAAGsJ,QAAQ,CAAC,IAAI,CAACjR,aAAa,CAACS,WAAW,CAAC;QAC5D,IAAIwN,KAAK,CAACnL,MAAM,KAAK6E,WAAW,EAAE;UAChC,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF;IACA,IAAI,CAAChJ,cAAc,GAAG,IAAI,CAAC2C,cAAc,CAACmP,MAAM,CAACxC,KAAK,IACpD2F,qBAAqB,CAAC9C,QAAQ,CAAC7C,KAAK,CAACvL,QAAQ,CAAC,CAC/C;IAED;IACA,IAAI,CAAC3D,eAAe,CAAC+O,OAAO,CAACG,KAAK,IAAG;MACnCA,KAAK,CAACjM,QAAQ,GAAG,IAAI,CAACrD,cAAc,CAACkS,IAAI,CAAC7O,QAAQ,IAAIA,QAAQ,CAACU,QAAQ,KAAKuL,KAAK,CAACvL,QAAQ,CAAC;IAC7F,CAAC,CAAC;IAEF;IACA,IAAI,CAACmR,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACvQ,WAAW,GAAG,CAAC;IACpB,IAAI,CAACwQ,gBAAgB,EAAE;EACzB;EAEA;EACQD,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACxO,eAAe,CAACzG,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACqF,SAAS,GAAG,KAAK;IACxB,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI,CAACoB,eAAe,CAAC0O,KAAK,CAAC9F,KAAK,IAC/C,CAACA,KAAK,CAACvL,QAAQ,IAAIuL,KAAK,CAACjM,QAAQ,CAClC;IACH;EACF;EAEA;EACA8R,gBAAgBA,CAAA;IACd,IAAI,CAACjQ,UAAU,GAAGoB,IAAI,CAAC+O,IAAI,CAAC,IAAI,CAAC1S,cAAc,CAAC1C,MAAM,GAAG,IAAI,CAACiC,QAAQ,CAAC;IACvE,MAAMoT,UAAU,GAAG,CAAC,IAAI,CAAC3Q,WAAW,GAAG,CAAC,IAAI,IAAI,CAACzC,QAAQ;IACzD,MAAMqT,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACpT,QAAQ;IAC3C,IAAI,CAACwE,eAAe,GAAG,IAAI,CAAC/D,cAAc,CAAC0F,KAAK,CAACiN,UAAU,EAAEC,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACL,oBAAoB,EAAE;EAC7B;EAEA;EACA9S,gBAAgBA,CAAA;IACd,IAAI,CAACuC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACwQ,gBAAgB,EAAE;EACzB;EAEA;EACAzQ,QAAQA,CAAC8Q,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACtQ,UAAU,EAAE;MACxC,IAAI,CAACP,WAAW,GAAG6Q,IAAI;MACvB,IAAI,CAACL,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAhQ,eAAeA,CAAA;IACb,MAAMsQ,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAIpC,KAAK,GAAGhN,IAAI,CAACqP,GAAG,CAAC,CAAC,EAAE,IAAI,CAAChR,WAAW,GAAG2B,IAAI,CAACiJ,KAAK,CAACmG,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAInC,GAAG,GAAGjN,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrB,UAAU,EAAEoO,KAAK,GAAGoC,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAInC,GAAG,GAAGD,KAAK,GAAG,CAAC,GAAGoC,UAAU,EAAE;MAChCpC,KAAK,GAAGhN,IAAI,CAACqP,GAAG,CAAC,CAAC,EAAEpC,GAAG,GAAGmC,UAAU,GAAG,CAAC,CAAC;IAC3C;IAEA,KAAK,IAAIE,CAAC,GAAGtC,KAAK,EAAEsC,CAAC,IAAIrC,GAAG,EAAEqC,CAAC,EAAE,EAAE;MACjCH,KAAK,CAACjG,IAAI,CAACoG,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACAlQ,iBAAiBA,CAAA;IACf,IAAI,CAACmB,eAAe,CAACyI,OAAO,CAACG,KAAK,IAAG;MACnC,IAAIA,KAAK,CAACvL,QAAQ,EAAE;QAClBuL,KAAK,CAACjM,QAAQ,GAAG,IAAI,CAACiC,SAAS;MACjC;IACF,CAAC,CAAC;IACF,IAAI,CAACuQ,oBAAoB,EAAE;EAC7B;EAEA;EACAvS,sBAAsBA,CAAA;IACpB,IAAI,CAACuS,oBAAoB,EAAE;IAC3B,IAAI,CAACvQ,SAAS,GAAG,IAAI,CAACoB,eAAe,CAAC0O,KAAK,CAAC9F,KAAK,IAC/C,CAACA,KAAK,CAACvL,QAAQ,IAAIuL,KAAK,CAACjM,QAAQ,CAClC;EACH;EAEA;EACAwS,oBAAoBA,CAAA;IAClB,IAAI,CAAC7V,cAAc,GAAG,IAAI,CAACI,eAAe,CAAC0R,MAAM,CAACxC,KAAK,IAAIA,KAAK,CAACjM,QAAQ,CAAC;EAC5E;EAEA;EACAyC,IAAIA,CAACgQ,KAAa;IAChB,IAAI,IAAI,CAACtP,SAAS,KAAKsP,KAAK,EAAE;MAC5B,IAAI,CAACrP,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IACpE,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAGsP,KAAK;MACtB,IAAI,CAACrP,aAAa,GAAG,KAAK;IAC5B;IAEA,IAAI,CAAC9D,cAAc,CAACmD,IAAI,CAAC,CAAC2J,CAAC,EAAEC,CAAC,KAAI;MAChC,IAAIqG,MAAM,GAAItG,CAAS,CAACqG,KAAK,CAAC;MAC9B,IAAIE,MAAM,GAAItG,CAAS,CAACoG,KAAK,CAAC;MAE9B;MACA,IAAIA,KAAK,CAAC3D,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC1B4D,MAAM,GAAGA,MAAM,GAAG,IAAIzI,IAAI,CAACyI,MAAM,CAAC,CAAC5C,OAAO,EAAE,GAAG,CAAC;QAChD6C,MAAM,GAAGA,MAAM,GAAG,IAAI1I,IAAI,CAAC0I,MAAM,CAAC,CAAC7C,OAAO,EAAE,GAAG,CAAC;MAClD;MAEA;MACA,IAAI2C,KAAK,KAAK,QAAQ,EAAE;QACtBC,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC,IAAI,CAAC;QAC5BC,MAAM,GAAGC,MAAM,CAACD,MAAM,CAAC,IAAI,CAAC;MAC9B;MAEA;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAAC/D,WAAW,EAAE;QAC7BgE,MAAM,GAAGA,MAAM,CAAChE,WAAW,EAAE;MAC/B;MAEA,IAAI+D,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACvP,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC9C;MACA,IAAIsP,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACvP,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9C;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAAC0O,gBAAgB,EAAE;EACzB;EAEA;EACAxO,cAAcA,CAACuP,MAAc,EAAE5G,KAAqB;IAClD,OAAOA,KAAK,CAACvL,QAAQ;EACvB;EAEA;EACAO,aAAaA,CAACgL,KAAqB;IACjC,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;IAEzC,QAAQqD,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,UAAU;QACb,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACAhN,UAAUA,CAAA;IACR;IACA,MAAMwQ,UAAU,GAAG,IAAI,CAACC,WAAW,EAAE;IACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACrCG,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,UAAU,IAAIxJ,IAAI,EAAE,CAACwH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACrFyB,IAAI,CAACO,KAAK,CAACC,UAAU,GAAG,QAAQ;IAChCP,QAAQ,CAACzI,IAAI,CAACiJ,WAAW,CAACT,IAAI,CAAC;IAC/BA,IAAI,CAACU,KAAK,EAAE;IACZT,QAAQ,CAACzI,IAAI,CAACmJ,WAAW,CAACX,IAAI,CAAC;EACjC;EAEA;EACQJ,WAAWA,CAAA;IACjB,MAAMgB,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;IACxD,MAAMC,IAAI,GAAG,IAAI,CAAC1U,cAAc,CAAC8L,GAAG,CAACa,KAAK,IAAI,CAC5CA,KAAK,CAACrL,UAAU,EAChBqL,KAAK,CAACpL,aAAa,EACnB,GAAGoL,KAAK,CAACnL,MAAM,GAAG,EAClBmL,KAAK,CAACtM,gBAAgB,IAAI,KAAK,EAC/BsM,KAAK,CAACrM,cAAc,IAAI,KAAK,EAC7B,IAAI,CAACqB,aAAa,CAACgL,KAAK,CAAC,CAC1B,CAAC;IAEF,MAAM6G,UAAU,GAAG,CAACiB,OAAO,EAAE,GAAGC,IAAI,CAAC,CAClC5I,GAAG,CAAC6I,GAAG,IAAIA,GAAG,CAAC7I,GAAG,CAAC8I,IAAI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAClDA,IAAI,CAAC,IAAI,CAAC;IAEb,OAAO,QAAQ,GAAGrB,UAAU,CAAC,CAAC;EAChC;;;uCAjhCW/K,0BAA0B,EAAA3L,EAAA,CAAAgY,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlY,EAAA,CAAAgY,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAApY,EAAA,CAAAgY,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAtY,EAAA,CAAAgY,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAxY,EAAA,CAAAgY,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA1Y,EAAA,CAAAgY,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAA3Y,EAAA,CAAAgY,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAA7Y,EAAA,CAAAgY,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA1BpN,0BAA0B;MAAAqN,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;uCAR1B,EAAE,GAAAnZ,EAAA,CAAAqZ,0BAAA,EAAArZ,EAAA,CAAAsZ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCpEbnZ,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAa,SAAA,qBAAiC;UACnCb,EAAA,CAAAG,YAAA,EAAiB;UAQPH,EAPV,CAAAC,cAAA,mBAAc,cAEmB,cAEC,cACmC,UACxD,aACoB;UAAAD,EAAA,CAAAE,MAAA,uDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,aAA4C;UAC1CD,EAAA,CAAAE,MAAA,kRACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UACNH,EAAA,CAAAS,UAAA,KAAAmZ,0CAAA,kBAA0E;UAa9E5Z,EADE,CAAAG,YAAA,EAAM,EACF;UAMAH,EAHN,CAAAC,cAAA,eAAkC,eACC,eACK,iBACR;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpCH,EAAA,CAAAC,cAAA,qBACyC;UADVD,EAAA,CAAAqB,gBAAA,2BAAAwY,wEAAAtY,MAAA;YAAAvB,EAAA,CAAAwB,aAAA,CAAAsY,GAAA;YAAA9Z,EAAA,CAAA2B,kBAAA,CAAAyX,GAAA,CAAArY,WAAA,CAAAC,kBAAA,EAAAO,MAAA,MAAA6X,GAAA,CAAArY,WAAA,CAAAC,kBAAA,GAAAO,MAAA;YAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;UAAA,EAA4C;UACzEvB,EAAA,CAAA+B,UAAA,4BAAAgY,yEAAA;YAAA/Z,EAAA,CAAAwB,aAAA,CAAAsY,GAAA;YAAA,OAAA9Z,EAAA,CAAA8B,WAAA,CAAkBsX,GAAA,CAAAtI,iBAAA,EAAmB;UAAA,EAAC;UACtC9Q,EAAA,CAAAS,UAAA,KAAAuZ,gDAAA,wBAAoE;UAIxEha,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAAoC,iBACR;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpCH,EAAA,CAAAC,cAAA,qBAAmG;UAArED,EAAA,CAAAqB,gBAAA,2BAAA4Y,wEAAA1Y,MAAA;YAAAvB,EAAA,CAAAwB,aAAA,CAAAsY,GAAA;YAAA9Z,EAAA,CAAA2B,kBAAA,CAAAyX,GAAA,CAAAxM,gBAAA,EAAArL,MAAA,MAAA6X,GAAA,CAAAxM,gBAAA,GAAArL,MAAA;YAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;UAAA,EAA8B;UAACvB,EAAA,CAAA+B,UAAA,4BAAAmY,yEAAA;YAAAla,EAAA,CAAAwB,aAAA,CAAAsY,GAAA;YAAA,OAAA9Z,EAAA,CAAA8B,WAAA,CAAkBsX,GAAA,CAAAjH,gBAAA,EAAkB;UAAA,EAAC;UAChGnS,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAS,UAAA,KAAA0Z,gDAAA,wBAAuE;UAI3Ena,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAAoC,iBACR;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEtCH,EADF,CAAAC,cAAA,eAAuC,yBACJ;UAC/BD,EAAA,CAAAa,SAAA,mBAAoD;UACpDb,EAAA,CAAAC,cAAA,iBAC6C;UAA3CD,EAAA,CAAAqB,gBAAA,2BAAA+Y,oEAAA7Y,MAAA;YAAAvB,EAAA,CAAAwB,aAAA,CAAAsY,GAAA;YAAA9Z,EAAA,CAAA2B,kBAAA,CAAAyX,GAAA,CAAArY,WAAA,CAAAwC,gBAAA,EAAAhC,MAAA,MAAA6X,GAAA,CAAArY,WAAA,CAAAwC,gBAAA,GAAAhC,MAAA;YAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;UAAA,EAA0C;UAD5CvB,EAAA,CAAAG,YAAA,EAC6C;UAC7CH,EAAA,CAAAa,SAAA,4BAA8D;UAChEb,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,yBAAiC;UAC/BD,EAAA,CAAAa,SAAA,mBAAoD;UACpDb,EAAA,CAAAC,cAAA,iBAC2C;UAAzCD,EAAA,CAAAqB,gBAAA,2BAAAgZ,oEAAA9Y,MAAA;YAAAvB,EAAA,CAAAwB,aAAA,CAAAsY,GAAA;YAAA9Z,EAAA,CAAA2B,kBAAA,CAAAyX,GAAA,CAAArY,WAAA,CAAAyC,cAAA,EAAAjC,MAAA,MAAA6X,GAAA,CAAArY,WAAA,CAAAyC,cAAA,GAAAjC,MAAA;YAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;UAAA,EAAwC;UAD1CvB,EAAA,CAAAG,YAAA,EAC2C;UAC3CH,EAAA,CAAAa,SAAA,4BAA4D;UAGlEb,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAqC,eAC8B,kBACiC;UAApDD,EAAA,CAAA+B,UAAA,mBAAAuY,6DAAA;YAAAta,EAAA,CAAAwB,aAAA,CAAAsY,GAAA;YAAA,OAAA9Z,EAAA,CAAA8B,WAAA,CAASsX,GAAA,CAAA1K,kBAAA,EAAoB;UAAA,EAAC;UACxE1O,EAAA,CAAAa,SAAA,aAAkC;UAAAb,EAAA,CAAAE,MAAA,qBACpC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAyG;UAAlED,EAAA,CAAA+B,UAAA,mBAAAwY,6DAAA;YAAAva,EAAA,CAAAwB,aAAA,CAAAsY,GAAA;YAAA,OAAA9Z,EAAA,CAAA8B,WAAA,CAASsX,GAAA,CAAApT,gBAAA,EAAkB;UAAA,EAAC;UACjEhG,EAAA,CAAAa,SAAA,aAAgC;UAAAb,EAAA,CAAAE,MAAA,iCAClC;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAMAH,EAHN,CAAAC,cAAA,eAAsB,eACE,eACc,iBACD;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEzCH,EADF,CAAAC,cAAA,eAA0C,iBAEf;UAAvBD,EAAA,CAAAqB,gBAAA,2BAAAmZ,oEAAAjZ,MAAA;YAAAvB,EAAA,CAAAwB,aAAA,CAAAsY,GAAA;YAAA9Z,EAAA,CAAA2B,kBAAA,CAAAyX,GAAA,CAAA3M,QAAA,EAAAlL,MAAA,MAAA6X,GAAA,CAAA3M,QAAA,GAAAlL,MAAA;YAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;UAAA,EAAsB;UADxBvB,EAAA,CAAAG,YAAA,EACyB;UACzBH,EAAA,CAAAC,cAAA,iBAA+C;UAAAD,EAAA,CAAAE,MAAA,kDAAO;UACxDF,EADwD,CAAAG,YAAA,EAAQ,EAC1D;UAEJH,EADF,CAAAC,cAAA,eAA0C,iBAEf;UAAvBD,EAAA,CAAAqB,gBAAA,2BAAAoZ,oEAAAlZ,MAAA;YAAAvB,EAAA,CAAAwB,aAAA,CAAAsY,GAAA;YAAA9Z,EAAA,CAAA2B,kBAAA,CAAAyX,GAAA,CAAA3M,QAAA,EAAAlL,MAAA,MAAA6X,GAAA,CAAA3M,QAAA,GAAAlL,MAAA;YAAA,OAAAvB,EAAA,CAAA8B,WAAA,CAAAP,MAAA;UAAA,EAAsB;UADxBvB,EAAA,CAAAG,YAAA,EACyB;UACzBH,EAAA,CAAAC,cAAA,iBAAiD;UAAAD,EAAA,CAAAE,MAAA,kDAAO;UAKlEF,EALkE,CAAAG,YAAA,EAAQ,EAC5D,EACF,EACF,EACF,EACF;UAGNH,EAAA,CAAAS,UAAA,KAAAia,0CAAA,oBAAiE;UAiEnE1a,EAAA,CAAAG,YAAA,EAAM;UAoNNH,EAjNA,CAAAS,UAAA,KAAAka,0CAAA,oBAAwF,KAAAC,0CAAA,kBA6HF,KAAAC,0CAAA,kBAwEc,KAAAC,0CAAA,kBAYtD;UAalD9a,EADE,CAAAG,YAAA,EAAe,EACP;UAkGVH,EA/FA,CAAAS,UAAA,KAAAsa,kDAAA,iCAAA/a,EAAA,CAAAgb,sBAAA,CAAgE,KAAAC,kDAAA,gCAAAjb,EAAA,CAAAgb,sBAAA,CAkFG,KAAAE,kDAAA,iCAAAlb,EAAA,CAAAgb,sBAAA,CAaf;;;;;UA9dNhb,EAAA,CAAAI,SAAA,IAAoC;UAApCJ,EAAA,CAAAY,UAAA,SAAAwY,GAAA,CAAArY,WAAA,CAAAC,kBAAA,CAAoC;UAoBvChB,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAgD,gBAAA,YAAAoW,GAAA,CAAArY,WAAA,CAAAC,kBAAA,CAA4C;UAE7ChB,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAY,UAAA,YAAAwY,GAAA,CAAArK,oBAAA,CAAuB;UAQvB/O,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAgD,gBAAA,YAAAoW,GAAA,CAAAxM,gBAAA,CAA8B;UAE1B5M,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAY,UAAA,YAAAwY,GAAA,CAAAzM,eAAA,CAAkB;UAWF3M,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAY,UAAA,iBAAAua,aAAA,CAA0B;UACtEnb,EAAA,CAAAgD,gBAAA,YAAAoW,GAAA,CAAArY,WAAA,CAAAwC,gBAAA,CAA0C;UAMEvD,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAY,UAAA,iBAAAwa,WAAA,CAAwB;UACpEpb,EAAA,CAAAgD,gBAAA,YAAAoW,GAAA,CAAArY,WAAA,CAAAyC,cAAA,CAAwC;UAQ+BxD,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAY,UAAA,aAAAwY,GAAA,CAAApM,OAAA,CAAoB;UAG3BhN,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAY,UAAA,cAAAwY,GAAA,CAAAzY,eAAA,CAAAH,MAAA,CAAoC;UAaxBR,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAY,UAAA,eAAc;UAC1FZ,EAAA,CAAAgD,gBAAA,YAAAoW,GAAA,CAAA3M,QAAA,CAAsB;UAIwDzM,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAY,UAAA,gBAAe;UAC7FZ,EAAA,CAAAgD,gBAAA,YAAAoW,GAAA,CAAA3M,QAAA,CAAsB;UASHzM,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAY,UAAA,SAAAwY,GAAA,CAAAzY,eAAA,CAAAH,MAAA,KAAgC;UAoEnCR,EAAA,CAAAI,SAAA,EAAwD;UAAxDJ,EAAA,CAAAY,UAAA,SAAAwY,GAAA,CAAAhW,QAAA,gBAAAgW,GAAA,CAAAzY,eAAA,CAAAH,MAAA,KAAwD;UA6HzDR,EAAA,CAAAI,SAAA,EAAuD;UAAvDJ,EAAA,CAAAY,UAAA,SAAAwY,GAAA,CAAAhW,QAAA,eAAAgW,GAAA,CAAAzY,eAAA,CAAAH,MAAA,KAAuD;UAwErDR,EAAA,CAAAI,SAAA,EAAmE;UAAnEJ,EAAA,CAAAY,UAAA,SAAAwY,GAAA,CAAAzY,eAAA,CAAAH,MAAA,UAAA4Y,GAAA,CAAApI,gBAAA,CAAAxQ,MAAA,OAAmE;UAYnER,EAAA,CAAAI,SAAA,EAAa;UAAbJ,EAAA,CAAAY,UAAA,SAAAwY,GAAA,CAAApM,OAAA,CAAa;;;qBDxT5CzN,YAAY,EAAA8b,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAE3b,YAAY,EAAA4b,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,yBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAA1D,EAAA,CAAA2D,eAAA,EAAA3D,EAAA,CAAA4D,mBAAA,EAAA5D,EAAA,CAAA6D,qBAAA,EAAA7D,EAAA,CAAA8D,qBAAA,EAAA9D,EAAA,CAAA+D,mBAAA,EAAA/D,EAAA,CAAAgE,gBAAA,EAAAhE,EAAA,CAAAiE,iBAAA,EAAAjE,EAAA,CAAAkE,iBAAA,EAAAlE,EAAA,CAAAmE,oBAAA,EAAAnE,EAAA,CAAAoE,iBAAA,EAAApE,EAAA,CAAAqE,eAAA,EAAArE,EAAA,CAAAsE,qBAAA,EAAAtE,EAAA,CAAAuE,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAEjd,iBAAiB,EAC7CF,kBAAkB,EAAEC,mBAAmB;MAAAmd,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}