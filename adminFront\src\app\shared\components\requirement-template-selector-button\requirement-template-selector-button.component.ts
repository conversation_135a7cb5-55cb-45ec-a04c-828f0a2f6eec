import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbButtonModule, NbIconModule, NbDialogService } from '@nebular/theme';
import { 
  RequirementTemplateSelectorComponent, 
  RequirementSelectionConfig,
  ExtendedRequirementItem 
} from '../requirement-template-selector/requirement-template-selector.component';

@Component({
  selector: 'app-requirement-template-selector-button',
  template: `
    <button 
      [class]="buttonClass" 
      [disabled]="disabled"
      (click)="openSelector()">
      <i [class]="icon" *ngIf="icon"></i>
      {{ text }}
    </button>
  `,
  standalone: true,
  imports: [
    CommonModule,
    NbButtonModule,
    NbIconModule
  ]
})
export class RequirementTemplateSelectorButtonComponent {
  @Input() buildCaseId: string = '';
  @Input() text: string = '選擇需求項目';
  @Input() icon: string = 'fas fa-list';
  @Input() buttonClass: string = 'btn btn-primary';
  @Input() disabled: boolean = false;
  @Input() multiple: boolean = true;
  @Input() preSelectedItems: ExtendedRequirementItem[] = [];
  
  @Output() selectionConfirmed = new EventEmitter<RequirementSelectionConfig>();
  @Output() selectionCancelled = new EventEmitter<void>();
  @Output() error = new EventEmitter<string>();

  constructor(private dialogService: NbDialogService) { }

  openSelector() {
    if (this.disabled) {
      return;
    }

    if (!this.buildCaseId) {
      this.error.emit('請先選擇建案');
      return;
    }

    const dialogRef = this.dialogService.open(RequirementTemplateSelectorComponent, {
      context: {
        buildCaseId: parseInt(this.buildCaseId),
        multiple: this.multiple,
        preSelectedItems: this.preSelectedItems
      },
      autoFocus: false,
      closeOnBackdropClick: false,
      closeOnEsc: true
    });

    // 監聽選擇確認事件
    dialogRef.componentRef.instance.selectionConfirmed.subscribe(
      (config: RequirementSelectionConfig) => {
        this.selectionConfirmed.emit(config);
      }
    );

    // 監聽取消事件
    dialogRef.componentRef.instance.selectionCancelled.subscribe(
      () => {
        this.selectionCancelled.emit();
      }
    );
  }
}
