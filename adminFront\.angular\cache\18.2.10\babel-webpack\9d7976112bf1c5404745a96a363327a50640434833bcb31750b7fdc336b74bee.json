{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function mapSymbolLayout(ecModel) {\n  var processedMapType = {};\n  ecModel.eachSeriesByType('map', function (mapSeries) {\n    var mapType = mapSeries.getMapType();\n    if (mapSeries.getHostGeoModel() || processedMapType[mapType]) {\n      return;\n    }\n    var mapSymbolOffsets = {};\n    zrUtil.each(mapSeries.seriesGroup, function (subMapSeries) {\n      var geo = subMapSeries.coordinateSystem;\n      var data = subMapSeries.originalData;\n      if (subMapSeries.get('showLegendSymbol') && ecModel.getComponent('legend')) {\n        data.each(data.mapDimension('value'), function (value, idx) {\n          var name = data.getName(idx);\n          var region = geo.getRegion(name);\n          // If input series.data is [11, 22, '-'/null/undefined, 44],\n          // it will be filled with NaN: [11, 22, NaN, 44] and NaN will\n          // not be drawn. So here must validate if value is NaN.\n          if (!region || isNaN(value)) {\n            return;\n          }\n          var offset = mapSymbolOffsets[name] || 0;\n          var point = geo.dataToPoint(region.getCenter());\n          mapSymbolOffsets[name] = offset + 1;\n          data.setItemLayout(idx, {\n            point: point,\n            offset: offset\n          });\n        });\n      }\n    });\n    // Show label of those region not has legendIcon (which is offset 0)\n    var data = mapSeries.getData();\n    data.each(function (idx) {\n      var name = data.getName(idx);\n      var layout = data.getItemLayout(idx) || {};\n      layout.showLabel = !mapSymbolOffsets[name];\n      data.setItemLayout(idx, layout);\n    });\n    processedMapType[mapType] = true;\n  });\n}", "map": {"version": 3, "names": ["zrUtil", "mapSymbolLayout", "ecModel", "processedMapType", "eachSeriesByType", "mapSeries", "mapType", "getMapType", "getHostGeoModel", "mapSymbolOffsets", "each", "seriesGroup", "subMapSeries", "geo", "coordinateSystem", "data", "originalData", "get", "getComponent", "mapDimension", "value", "idx", "name", "getName", "region", "getRegion", "isNaN", "offset", "point", "dataToPoint", "getCenter", "setItemLayout", "getData", "layout", "getItemLayout", "showLabel"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/map/mapSymbolLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function mapSymbolLayout(ecModel) {\n  var processedMapType = {};\n  ecModel.eachSeriesByType('map', function (mapSeries) {\n    var mapType = mapSeries.getMapType();\n    if (mapSeries.getHostGeoModel() || processedMapType[mapType]) {\n      return;\n    }\n    var mapSymbolOffsets = {};\n    zrUtil.each(mapSeries.seriesGroup, function (subMapSeries) {\n      var geo = subMapSeries.coordinateSystem;\n      var data = subMapSeries.originalData;\n      if (subMapSeries.get('showLegendSymbol') && ecModel.getComponent('legend')) {\n        data.each(data.mapDimension('value'), function (value, idx) {\n          var name = data.getName(idx);\n          var region = geo.getRegion(name);\n          // If input series.data is [11, 22, '-'/null/undefined, 44],\n          // it will be filled with NaN: [11, 22, NaN, 44] and NaN will\n          // not be drawn. So here must validate if value is NaN.\n          if (!region || isNaN(value)) {\n            return;\n          }\n          var offset = mapSymbolOffsets[name] || 0;\n          var point = geo.dataToPoint(region.getCenter());\n          mapSymbolOffsets[name] = offset + 1;\n          data.setItemLayout(idx, {\n            point: point,\n            offset: offset\n          });\n        });\n      }\n    });\n    // Show label of those region not has legendIcon (which is offset 0)\n    var data = mapSeries.getData();\n    data.each(function (idx) {\n      var name = data.getName(idx);\n      var layout = data.getItemLayout(idx) || {};\n      layout.showLabel = !mapSymbolOffsets[name];\n      data.setItemLayout(idx, layout);\n    });\n    processedMapType[mapType] = true;\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,eAAe,SAASC,eAAeA,CAACC,OAAO,EAAE;EAC/C,IAAIC,gBAAgB,GAAG,CAAC,CAAC;EACzBD,OAAO,CAACE,gBAAgB,CAAC,KAAK,EAAE,UAAUC,SAAS,EAAE;IACnD,IAAIC,OAAO,GAAGD,SAAS,CAACE,UAAU,CAAC,CAAC;IACpC,IAAIF,SAAS,CAACG,eAAe,CAAC,CAAC,IAAIL,gBAAgB,CAACG,OAAO,CAAC,EAAE;MAC5D;IACF;IACA,IAAIG,gBAAgB,GAAG,CAAC,CAAC;IACzBT,MAAM,CAACU,IAAI,CAACL,SAAS,CAACM,WAAW,EAAE,UAAUC,YAAY,EAAE;MACzD,IAAIC,GAAG,GAAGD,YAAY,CAACE,gBAAgB;MACvC,IAAIC,IAAI,GAAGH,YAAY,CAACI,YAAY;MACpC,IAAIJ,YAAY,CAACK,GAAG,CAAC,kBAAkB,CAAC,IAAIf,OAAO,CAACgB,YAAY,CAAC,QAAQ,CAAC,EAAE;QAC1EH,IAAI,CAACL,IAAI,CAACK,IAAI,CAACI,YAAY,CAAC,OAAO,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;UAC1D,IAAIC,IAAI,GAAGP,IAAI,CAACQ,OAAO,CAACF,GAAG,CAAC;UAC5B,IAAIG,MAAM,GAAGX,GAAG,CAACY,SAAS,CAACH,IAAI,CAAC;UAChC;UACA;UACA;UACA,IAAI,CAACE,MAAM,IAAIE,KAAK,CAACN,KAAK,CAAC,EAAE;YAC3B;UACF;UACA,IAAIO,MAAM,GAAGlB,gBAAgB,CAACa,IAAI,CAAC,IAAI,CAAC;UACxC,IAAIM,KAAK,GAAGf,GAAG,CAACgB,WAAW,CAACL,MAAM,CAACM,SAAS,CAAC,CAAC,CAAC;UAC/CrB,gBAAgB,CAACa,IAAI,CAAC,GAAGK,MAAM,GAAG,CAAC;UACnCZ,IAAI,CAACgB,aAAa,CAACV,GAAG,EAAE;YACtBO,KAAK,EAAEA,KAAK;YACZD,MAAM,EAAEA;UACV,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF;IACA,IAAIZ,IAAI,GAAGV,SAAS,CAAC2B,OAAO,CAAC,CAAC;IAC9BjB,IAAI,CAACL,IAAI,CAAC,UAAUW,GAAG,EAAE;MACvB,IAAIC,IAAI,GAAGP,IAAI,CAACQ,OAAO,CAACF,GAAG,CAAC;MAC5B,IAAIY,MAAM,GAAGlB,IAAI,CAACmB,aAAa,CAACb,GAAG,CAAC,IAAI,CAAC,CAAC;MAC1CY,MAAM,CAACE,SAAS,GAAG,CAAC1B,gBAAgB,CAACa,IAAI,CAAC;MAC1CP,IAAI,CAACgB,aAAa,CAACV,GAAG,EAAEY,MAAM,CAAC;IACjC,CAAC,CAAC;IACF9B,gBAAgB,CAACG,OAAO,CAAC,GAAG,IAAI;EAClC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}