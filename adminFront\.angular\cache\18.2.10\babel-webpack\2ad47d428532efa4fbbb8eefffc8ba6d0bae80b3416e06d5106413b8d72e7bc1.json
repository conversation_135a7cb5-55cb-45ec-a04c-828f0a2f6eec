{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { QUOTATION_TEMPLATE } from 'src/assets/template/quotation-template';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { CQuotationItemType } from 'src/app/models/quotation.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/services/event.service\";\nimport * as i10 from \"src/app/shared/services/utility.service\";\nimport * as i11 from \"src/app/services/quotation.service\";\nimport * as i12 from \"@angular/common\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i15 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i16 from \"../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction HouseholdManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r6.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r7.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r9.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_button_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_button_74_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogHouseholdMain_r12 = i0.ɵɵreference(114);\n      return i0.ɵɵresetView(ctx_r10.openModel(dialogHouseholdMain_r12));\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_108_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const item_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogUpdateHousehold_r16 = i0.ɵɵreference(112);\n      return i0.ɵɵresetView(ctx_r10.openModelDetail(dialogUpdateHousehold_r16, item_r15));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_108_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 54);\n    i0.ɵɵtemplate(20, HouseholdManagementComponent_tr_108_button_20_Template, 2, 0, \"button\", 55);\n    i0.ɵɵelementStart(21, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_21_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"customer-change-picture\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(22, \" \\u6D3D\\u8AC7\\u7D00\\u9304 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_23_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"sample-selection-result\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(24, \" \\u5BA2\\u8B8A\\u78BA\\u8A8D\\u5716\\u8AAA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_25_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"finaldochouse_management\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(26, \" \\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_27_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.resetSecureKey(item_r15));\n    });\n    i0.ɵɵtext(28, \" \\u91CD\\u7F6E\\u5BC6\\u78BC \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_29_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogQuotation_r17 = i0.ɵɵreference(116);\n      return i0.ɵɵresetView(ctx_r10.openQuotation(dialogQuotation_r17, item_r15));\n    });\n    i0.ɵɵtext(30, \" \\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", item_r15.CHouseType === 2 ? \"\\u92B7\\u552E\\u6236\" : \"\", \" \", item_r15.CHouseType === 1 ? \"\\u5730\\u4E3B\\u6236\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsChange === true ? \"\\u5BA2\\u8B8A\" : item_r15.CIsChange === false ? \"\\u6A19\\u6E96\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CProgressName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", item_r15.CPayStatus === 0 ? \"\\u672A\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 1 ? \"\\u5DF2\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 2 ? \"\\u7121\\u9808\\u4ED8\\u6B3E\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CSignStatus === 0 || item_r15.CSignStatus == null ? \"\\u672A\\u7C3D\\u56DE\" : \"\\u5DF2\\u7C3D\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r10.getQuotationStatusText(item_r15.CQuotationStatus));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsEnable ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isUpdate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r20);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r20.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r21);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r21.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r23.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"label\", 92);\n    i0.ɵɵtext(2, \" \\u4ED8\\u6B3E\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CPayStatusSelected, $event) || (ctx_r10.detailSelected.CPayStatusSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_nb_option_4_Template, 2, 2, \"nb-option\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CPayStatusSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.payStatusOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r25);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r25.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"label\", 94);\n    i0.ɵɵtext(2, \" \\u9032\\u5EA6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 95);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CProgressSelected, $event) || (ctx_r10.detailSelected.CProgressSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_nb_option_4_Template, 2, 2, \"nb-option\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CProgressSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.progressOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-body\", 62)(1, \"div\", 63)(2, \"label\", 64);\n    i0.ɵɵtext(3, \" \\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-select\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CBuildCaseSelected, $event) || (ctx_r10.detailSelected.CBuildCaseSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_5_Template, 2, 2, \"nb-option\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 63)(7, \"label\", 66);\n    i0.ɵɵtext(8, \" \\u6236\\u578B\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CHousehold, $event) || (ctx_r10.houseDetail.CHousehold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 63)(11, \"label\", 68);\n    i0.ɵɵtext(12, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CFloor, $event) || (ctx_r10.houseDetail.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 63)(15, \"label\", 70);\n    i0.ɵɵtext(16, \" \\u5BA2\\u6236\\u59D3\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CCustomerName, $event) || (ctx_r10.houseDetail.CCustomerName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 63)(19, \"label\", 72);\n    i0.ɵɵtext(20, \" \\u8EAB\\u5206\\u8B49\\u5B57\\u865F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CNationalId, $event) || (ctx_r10.houseDetail.CNationalId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 63)(23, \"label\", 74);\n    i0.ɵɵtext(24, \" \\u96FB\\u5B50\\u90F5\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CMail, $event) || (ctx_r10.houseDetail.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 63)(27, \"label\", 76);\n    i0.ɵɵtext(28, \" \\u806F\\u7D61\\u96FB\\u8A71 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"input\", 77);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CPhone, $event) || (ctx_r10.houseDetail.CPhone = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 63)(31, \"label\", 78);\n    i0.ɵɵtext(32, \" \\u6236\\u5225\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-select\", 79);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CHouseTypeSelected, $event) || (ctx_r10.detailSelected.CHouseTypeSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(34, HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_34_Template, 2, 2, \"nb-option\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template, 5, 2, \"div\", 80)(36, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template, 5, 2, \"div\", 80);\n    i0.ɵɵelementStart(37, \"div\", 63)(38, \"label\", 81);\n    i0.ɵɵtext(39, \" \\u662F\\u5426\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"nb-checkbox\", 82);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsChange, $event) || (ctx_r10.houseDetail.CIsChange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(41, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 63)(43, \"label\", 83);\n    i0.ɵɵtext(44, \" \\u662F\\u5426\\u555F\\u7528 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"nb-checkbox\", 82);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsEnable, $event) || (ctx_r10.houseDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(46, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 84)(48, \"label\", 85);\n    i0.ɵɵtext(49, \" \\u5BA2\\u8B8A\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 86)(51, \"nb-form-field\", 87);\n    i0.ɵɵelement(52, \"nb-icon\", 88);\n    i0.ɵɵelementStart(53, \"input\", 89);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeStartDate, $event) || (ctx_r10.houseDetail.changeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"nb-datepicker\", 90, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"nb-form-field\", 87);\n    i0.ɵɵelement(57, \"nb-icon\", 88);\n    i0.ɵɵelementStart(58, \"input\", 91);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_58_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeEndDate, $event) || (ctx_r10.houseDetail.changeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"nb-datepicker\", 90, 6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const StartDate_r26 = i0.ɵɵreference(55);\n    const EndDate_r27 = i0.ɵɵreference(60);\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CBuildCaseSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.userBuildCaseOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CHousehold);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CFloor);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CCustomerName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CNationalId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CMail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CPhone);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CHouseTypeSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.houseTypeOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isChangePayStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isChangeProgress);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsChange);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsEnable);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"nbDatepicker\", StartDate_r26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeStartDate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"nbDatepicker\", EndDate_r27);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeEndDate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_111_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ref_r28 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onSubmitDetail(ref_r28));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 58);\n    i0.ɵɵtemplate(1, HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template, 61, 18, \"nb-card-body\", 59);\n    i0.ɵɵelementStart(2, \"nb-card-footer\", 51)(3, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_111_Template_button_click_3_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r28));\n    });\n    i0.ɵɵtext(4, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_111_button_5_Template, 2, 0, \"button\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.houseDetail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_113_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_113_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ref_r31 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.addHouseHoldMain(ref_r31));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_113_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 58)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6236\\u5225\\u7BA1\\u7406 \\u300B\\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 62)(4, \"div\", 63)(5, \"label\", 97);\n    i0.ɵɵtext(6, \" \\u68DF\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CBuildingName, $event) || (ctx_r10.houseHoldMain.CBuildingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 63)(9, \"label\", 99);\n    i0.ɵɵtext(10, \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 100);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CHouseHoldCount, $event) || (ctx_r10.houseHoldMain.CHouseHoldCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 63)(13, \"label\", 101);\n    i0.ɵɵtext(14, \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 102);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CFloor, $event) || (ctx_r10.houseHoldMain.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"nb-card-footer\", 51)(17, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_113_Template_button_click_17_listener() {\n      const ref_r31 = i0.ɵɵrestoreView(_r30).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r31));\n    });\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, HouseholdManagementComponent_ng_template_113_button_19_Template, 2, 0, \"button\", 104);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CBuildingName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CHouseHoldCount);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CFloor);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(\"\\u95DC\\u9589\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      const dialogTemplateImport_r35 = i0.ɵɵreference(118);\n      return i0.ɵɵresetView(ctx_r10.openTemplateImportDialog(dialogTemplateImport_r35));\n    });\n    i0.ɵɵtext(2, \" + \\u5F9E\\u6A21\\u677F\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.loadDefaultItems());\n    });\n    i0.ɵɵtext(5, \" \\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_div_4_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.loadRegularItems());\n    });\n    i0.ɵɵtext(7, \" \\u8F09\\u5165\\u9078\\u6A23\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146);\n    i0.ɵɵelement(1, \"i\", 147);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" - \\u6B64\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\\uFF0C\\u7121\\u6CD5\\u9032\\u884C\\u4FEE\\u6539\\u3002\\u60A8\\u53EF\\u4EE5\\u5217\\u5370\\u6B64\\u5831\\u50F9\\u55AE\\u6216\\u7522\\u751F\\u65B0\\u7684\\u5831\\u50F9\\u55AE\\u3002 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const i_r39 = i0.ɵɵnextContext().index;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.removeQuotationItem(i_r39));\n    });\n    i0.ɵɵtext(1, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_25_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 157);\n    i0.ɵɵelement(1, \"i\", 158);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 148);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_2_listener($event) {\n      const item_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r37.cItemName, $event) || (item_r37.cItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"input\", 149);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_4_listener($event) {\n      const item_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r37.cUnitPrice, $event) || (item_r37.cUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\")(6, \"input\", 150);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_6_listener($event) {\n      const item_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r37.cUnit, $event) || (item_r37.cUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"input\", 151);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_8_listener($event) {\n      const item_r37 = i0.ɵɵrestoreView(_r36).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r37.cCount, $event) || (item_r37.cCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_25_Template_input_ngModelChange_8_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 152);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"span\", 153);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtemplate(15, HouseholdManagementComponent_ng_template_115_tr_25_button_15_Template, 2, 0, \"button\", 154)(16, HouseholdManagementComponent_ng_template_115_tr_25_span_16_Template, 2, 0, \"span\", 155);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r37 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 1 || item_r37.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r37.cItemName);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 1 || item_r37.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r37.cUnitPrice);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 1 || item_r37.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r37.cUnit);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 1 || item_r37.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r37.cCount);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r37.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.formatCurrency(item_r37.cUnitPrice * item_r37.cCount), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"badge-primary\", item_r37.CQuotationItemType === 1)(\"badge-info\", item_r37.CQuotationItemType === 3)(\"badge-secondary\", item_r37.CQuotationItemType !== 1 && item_r37.CQuotationItemType !== 3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getQuotationTypeText(item_r37.CQuotationItemType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 159);\n    i0.ɵɵtext(2, \" \\u8ACB\\u9EDE\\u64CA\\u300C\\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE\\u300D\\u6216\\u300C\\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42\\u300D\\u958B\\u59CB\\u5EFA\\u7ACB\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_button_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 160);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_button_63_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.createNewQuotation());\n    });\n    i0.ɵɵelement(1, \"i\", 161);\n    i0.ɵɵtext(2, \" \\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_button_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_button_67_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ref_r41 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.lockQuotation(ref_r41));\n    });\n    i0.ɵɵtext(1, \" \\u9396\\u5B9A\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_button_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_button_68_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const ref_r41 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.saveQuotation(ref_r41));\n    });\n    i0.ɵɵtext(1, \" \\u5132\\u5B58\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 106)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\");\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_115_div_4_Template, 8, 0, \"div\", 107)(5, HouseholdManagementComponent_ng_template_115_div_5_Template, 5, 0, \"div\", 108);\n    i0.ɵɵelementStart(6, \"div\", 109)(7, \"table\", 110)(8, \"thead\")(9, \"tr\")(10, \"th\", 111);\n    i0.ɵɵtext(11, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 112);\n    i0.ɵɵtext(13, \"\\u55AE\\u50F9 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 113);\n    i0.ɵɵtext(15, \"\\u55AE\\u4F4D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 113);\n    i0.ɵɵtext(17, \"\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 114);\n    i0.ɵɵtext(19, \"\\u5C0F\\u8A08 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 115);\n    i0.ɵɵtext(21, \"\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 115);\n    i0.ɵɵtext(23, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"tbody\");\n    i0.ɵɵtemplate(25, HouseholdManagementComponent_ng_template_115_tr_25_Template, 17, 22, \"tr\", 50)(26, HouseholdManagementComponent_ng_template_115_tr_26_Template, 3, 0, \"tr\", 116);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 117)(28, \"div\", 118)(29, \"div\", 119)(30, \"div\", 120)(31, \"span\", 121);\n    i0.ɵɵtext(32, \"\\u5C0F\\u8A08\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\", 122);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 123)(36, \"div\", 124)(37, \"div\", 125);\n    i0.ɵɵelement(38, \"i\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\")(40, \"span\", 127);\n    i0.ɵɵtext(41, \"\\u71DF\\u696D\\u7A05\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\", 128);\n    i0.ɵɵtext(43, \"5%\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 129);\n    i0.ɵɵelement(45, \"i\", 130);\n    i0.ɵɵtext(46, \" \\u56FA\\u5B9A\\u70BA\\u5C0F\\u8A08\\u91D1\\u984D\\u76845% \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(47, \"div\", 131)(48, \"div\", 132);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 133);\n    i0.ɵɵtext(51, \"\\u542B\\u7A05\\u91D1\\u984D\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(52, \"hr\", 134);\n    i0.ɵɵelementStart(53, \"div\", 135)(54, \"span\", 122);\n    i0.ɵɵtext(55, \"\\u7E3D\\u91D1\\u984D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"span\", 136);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(58, \"nb-card-footer\", 137)(59, \"div\")(60, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_Template_button_click_60_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.printQuotation());\n    });\n    i0.ɵɵelement(61, \"i\", 139);\n    i0.ɵɵtext(62, \" \\u5217\\u5370\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(63, HouseholdManagementComponent_ng_template_115_button_63_Template, 3, 0, \"button\", 140);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"div\")(65, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_Template_button_click_65_listener() {\n      const ref_r41 = i0.ɵɵrestoreView(_r33).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r41));\n    });\n    i0.ɵɵtext(66, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(67, HouseholdManagementComponent_ng_template_115_button_67_Template, 2, 1, \"button\", 142)(68, HouseholdManagementComponent_ng_template_115_button_68_Template, 2, 1, \"button\", 143);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u5831\\u50F9\\u55AE - \", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CHouseHold, \" (\", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CFloor, \"\\u6A13) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.quotationItems);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.quotationItems.length === 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.totalAmount));\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.additionalFeeAmount));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.finalTotalAmount));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 187)(1, \"button\", 188);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_19_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.clearTemplateSearch());\n    });\n    i0.ɵɵelement(2, \"i\", 189);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_21_i_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 197);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 190);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_21_Template_div_click_0_listener() {\n      const template_r47 = i0.ɵɵrestoreView(_r46).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.selectTemplateForImport(template_r47));\n    });\n    i0.ɵɵelementStart(1, \"div\", 191)(2, \"div\", 192);\n    i0.ɵɵelement(3, \"i\", 193);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 194);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 195);\n    i0.ɵɵtemplate(8, HouseholdManagementComponent_ng_template_117_div_21_i_8_Template, 1, 0, \"i\", 196);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const template_r47 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", (ctx_r10.selectedTemplateForImport == null ? null : ctx_r10.selectedTemplateForImport.TemplateID) === template_r47.TemplateID);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", template_r47.TemplateName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", template_r47.Description || \"\\u7121\\u63CF\\u8FF0\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r10.selectedTemplateForImport == null ? null : ctx_r10.selectedTemplateForImport.TemplateID) === template_r47.TemplateID);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 198);\n    i0.ɵɵelement(1, \"i\", 199);\n    i0.ɵɵelementStart(2, \"p\", 200);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.templateSearchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u8F09\\u5165\\u4E2D...\", \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 201)(1, \"div\", 135)(2, \"div\", 202)(3, \"small\", 157);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 203)(6, \"button\", 204);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_23_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.previousTemplatePage());\n    });\n    i0.ɵɵelement(7, \"i\", 205);\n    i0.ɵɵtext(8, \" \\u4E0A\\u4E00\\u9801 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 206);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 207);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_23_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.nextTemplatePage());\n    });\n    i0.ɵɵtext(12, \" \\u4E0B\\u4E00\\u9801 \");\n    i0.ɵɵelement(13, \"i\", 208);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", ctx_r10.getTemplateStartIndex() + 1, \" - \", ctx_r10.getTemplateEndIndex(), \" \\u7B46\\uFF0C\\u5171 \", ctx_r10.templateTotalItems || ctx_r10.templateList.length, \" \\u7B46\\u6A21\\u677F \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.templateCurrentPage === 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r10.templateCurrentPage, \" / \", ctx_r10.getTotalTemplatePages(), \" \\u9801 \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r10.templateCurrentPage === ctx_r10.getTotalTemplatePages());\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_24_div_7_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 222);\n    i0.ɵɵelement(1, \"i\", 224);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r50 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", detail_r50.CGroupName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_24_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 215)(1, \"div\", 216)(2, \"nb-checkbox\", 217);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_117_div_24_div_7_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const detail_r50 = i0.ɵɵrestoreView(_r49).$implicit;\n      i0.ɵɵtwoWayBindingSet(detail_r50.selected, $event) || (detail_r50.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function HouseholdManagementComponent_ng_template_117_div_24_div_7_Template_nb_checkbox_checkedChange_2_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r10.updateSelectedCount());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 218)(4, \"div\", 219)(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 220);\n    i0.ɵɵtemplate(8, HouseholdManagementComponent_ng_template_117_div_24_div_7_span_8_Template, 3, 1, \"span\", 221);\n    i0.ɵɵelementStart(9, \"span\", 222);\n    i0.ɵɵelement(10, \"i\", 223);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const detail_r50 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"checked\", detail_r50.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(detail_r50.CReleateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r50.CGroupName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", detail_r50.CReleateId, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_24_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 198);\n    i0.ɵɵelement(1, \"i\", 225);\n    i0.ɵɵelementStart(2, \"p\", 200);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_24_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 226)(1, \"button\", 227);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_24_div_9_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r10.selectAllTemplateItems());\n    });\n    i0.ɵɵelement(2, \"i\", 228);\n    i0.ɵɵtext(3, \"\\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 229);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_div_24_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r10.deselectAllTemplateItems());\n    });\n    i0.ɵɵelement(5, \"i\", 230);\n    i0.ɵɵtext(6, \"\\u53D6\\u6D88\\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 231);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate2(\" \\u5DF2\\u9078\\u64C7 \", ctx_r10.getSelectedTemplateItemsCount(), \" / \", ctx_r10.templateDetailList.length, \" \\u9805 \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 209)(1, \"h6\", 169);\n    i0.ɵɵelement(2, \"i\", 210);\n    i0.ɵɵtext(3, \"\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵelementStart(4, \"span\", 211);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 212);\n    i0.ɵɵtemplate(7, HouseholdManagementComponent_ng_template_117_div_24_div_7_Template, 12, 4, \"div\", 213)(8, HouseholdManagementComponent_ng_template_117_div_24_div_8_Template, 4, 0, \"div\", 179);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, HouseholdManagementComponent_ng_template_117_div_24_div_9_Template, 9, 2, \"div\", 214);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r10.templateDetailList.length, \" \\u9805\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.templateDetailList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.templateDetailList.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.templateDetailList.length > 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 232)(1, \"small\", 157);\n    i0.ɵɵelement(2, \"i\", 233);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5C07\\u532F\\u5165 \", ctx_r10.getSelectedTemplateItemsCount(), \" \\u500B\\u9805\\u76EE\\u5230\\u5831\\u50F9\\u55AE \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_117_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 164)(1, \"nb-card-header\")(2, \"div\", 135)(3, \"h5\", 165);\n    i0.ɵɵelement(4, \"i\", 166);\n    i0.ɵɵtext(5, \"\\u5F9E\\u6A21\\u677F\\u532F\\u5165\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 167);\n    i0.ɵɵtext(7, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u6A21\\u677F\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"nb-card-body\")(9, \"div\", 168)(10, \"h6\", 169);\n    i0.ɵɵelement(11, \"i\", 170);\n    i0.ɵɵtext(12, \"\\u9078\\u64C7\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 171)(14, \"div\", 172)(15, \"div\", 173)(16, \"span\", 174);\n    i0.ɵɵelement(17, \"i\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"input\", 175);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_117_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.templateSearchKeyword, $event) || (ctx_r10.templateSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdManagementComponent_ng_template_117_Template_input_input_18_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.filterTemplates());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, HouseholdManagementComponent_ng_template_117_div_19_Template, 3, 0, \"div\", 176);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 177);\n    i0.ɵɵtemplate(21, HouseholdManagementComponent_ng_template_117_div_21_Template, 9, 5, \"div\", 178)(22, HouseholdManagementComponent_ng_template_117_div_22_Template, 4, 1, \"div\", 179);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, HouseholdManagementComponent_ng_template_117_div_23_Template, 14, 7, \"div\", 180);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, HouseholdManagementComponent_ng_template_117_div_24_Template, 10, 4, \"div\", 181);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"nb-card-footer\", 137);\n    i0.ɵɵtemplate(26, HouseholdManagementComponent_ng_template_117_div_26_Template, 4, 1, \"div\", 182);\n    i0.ɵɵelementStart(27, \"div\", 183)(28, \"button\", 184);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_Template_button_click_28_listener() {\n      const ref_r52 = i0.ɵɵrestoreView(_r44).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r52));\n    });\n    i0.ɵɵtext(29, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 185);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_117_Template_button_click_30_listener() {\n      const ref_r52 = i0.ɵɵrestoreView(_r44).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.importSelectedTemplateItems(ref_r52));\n    });\n    i0.ɵɵelement(31, \"i\", 186);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.templateSearchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.templateSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.templateList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.templateList.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.templateTotalItems > 0 || ctx_r10.templateList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.selectedTemplateForImport);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.selectedTemplateForImport);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.selectedTemplateForImport || ctx_r10.getSelectedTemplateItemsCount() === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u532F\\u5165\\u9078\\u4E2D\\u9805\\u76EE (\", ctx_r10.getSelectedTemplateItemsCount(), \") \");\n  }\n}\nexport let HouseholdManagementComponent = /*#__PURE__*/(() => {\n  class HouseholdManagementComponent extends BaseComponent {\n    constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService, quotationService, templateService) {\n      super(_allow);\n      this._allow = _allow;\n      this.enumHelper = enumHelper;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._houseService = _houseService;\n      this._houseHoldMainService = _houseHoldMainService;\n      this._buildCaseService = _buildCaseService;\n      this.pettern = pettern;\n      this.router = router;\n      this._eventService = _eventService;\n      this._ultilityService = _ultilityService;\n      this.quotationService = quotationService;\n      this.templateService = templateService;\n      this.tempBuildCaseID = -1;\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      this.statusOptions = [{\n        value: 0,\n        key: 'allow',\n        label: '允許'\n      }, {\n        value: 1,\n        key: 'not allowed',\n        label: '不允許'\n      }];\n      this.cIsEnableOptions = [{\n        value: null,\n        key: 'all',\n        label: '全部'\n      }, {\n        value: true,\n        key: 'enable',\n        label: '啟用'\n      }, {\n        value: false,\n        key: 'deactivate',\n        label: '停用'\n      }];\n      this.buildCaseOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      this.houseHoldOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      this.progressOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.houseTypeOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.payStatusOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.signStatusOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.quotationStatusOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.options = {\n        progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\n        payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\n        houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\n        quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus)\n      };\n      this.initDetail = {\n        CHouseID: 0,\n        CMail: \"\",\n        CIsChange: false,\n        CPayStatus: 0,\n        CIsEnable: false,\n        CCustomerName: \"\",\n        CNationalID: \"\",\n        CProgress: \"\",\n        CHouseType: 0,\n        CHouseHold: \"\",\n        CPhone: \"\"\n      };\n      // 報價單相關\n      this.quotationItems = [];\n      this.totalAmount = 0;\n      // 新增：百分比費用設定\n      this.additionalFeeName = '營業稅'; // 固定名稱\n      this.additionalFeePercentage = 5; // 固定5%\n      this.additionalFeeAmount = 0; // 百分比費用金額\n      this.finalTotalAmount = 0; // 最終總金額（含百分比費用）\n      this.enableAdditionalFee = true; // 固定啟用營業稅\n      this.currentHouse = null;\n      this.currentQuotationId = 0;\n      this.isQuotationEditable = true; // 報價單是否可編輯\n      // 模板匯入相關屬性\n      this.templateList = [];\n      this.templateDetailList = [];\n      this.selectedTemplateForImport = null;\n      this.templateSearchKeyword = '';\n      // 模板分頁相關屬性\n      this.templateCurrentPage = 1;\n      this.templatePageSize = 5; // 每頁顯示5個模板\n      this.paginatedTemplateList = [];\n      this.templateTotalItems = 0; // API 返回的總項目數\n      this.selectedFile = null;\n      this.buildingSelectedOptions = [{\n        value: '',\n        label: '全部'\n      }];\n      this._eventService.receive().pipe(tap(res => {\n        if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n          this.tempBuildCaseID = res.payload;\n        }\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(EnumHouseProgress)];\n      this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(EnumHouseType)];\n      this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(EnumPayStatus)];\n      this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(EnumSignStatus)];\n      this.quotationStatusOptions = [...this.quotationStatusOptions, ...this.enumHelper.getEnumOptions(EnumQuotationStatus)];\n      if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n        let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n        this.searchQuery = {\n          CBuildCaseSelected: null,\n          // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\n          //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\n          //   : this.buildingSelectedOptions[0],\n          CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value) : this.houseHoldOptions[0],\n          CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value) : this.houseTypeOptions[0],\n          CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value) : this.payStatusOptions[0],\n          CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value) : this.progressOptions[0],\n          CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value) : this.signStatusOptions[0],\n          CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value) : this.quotationStatusOptions[0],\n          CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value) : this.cIsEnableOptions[0],\n          CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined ? previous_search.CFrom : '',\n          CTo: previous_search.CTo != null && previous_search.CTo != undefined ? previous_search.CTo : ''\n        };\n      } else {\n        this.searchQuery = {\n          CBuildCaseSelected: null,\n          // CBuildingNameSelected: this.buildingSelectedOptions[0],\n          CHouseHoldSelected: this.houseHoldOptions[0],\n          CHouseTypeSelected: this.houseTypeOptions[0],\n          CPayStatusSelected: this.payStatusOptions[0],\n          CProgressSelected: this.progressOptions[0],\n          CSignStatusSelected: this.signStatusOptions[0],\n          CQuotationStatusSelected: this.quotationStatusOptions[0],\n          CIsEnableSeleted: this.cIsEnableOptions[0],\n          CFrom: '',\n          CTo: ''\n        };\n      }\n      this.getListBuildCase();\n    }\n    onSearch() {\n      let sessionSave = {\n        CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n        // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\n        CFrom: this.searchQuery.CFrom,\n        CTo: this.searchQuery.CTo,\n        CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\n        CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\n        CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\n        CPayStatusSelected: this.searchQuery.CPayStatusSelected,\n        CProgressSelected: this.searchQuery.CProgressSelected,\n        CSignStatusSelected: this.searchQuery.CSignStatusSelected,\n        CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\n      };\n      LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\n      this.getHouseList().subscribe();\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getHouseList().subscribe();\n    }\n    exportHouse() {\n      if (this.searchQuery.CBuildCaseSelected.cID) {\n        this._houseService.apiHouseExportHousePost$Json({\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n        }).subscribe(res => {\n          if (res.Entries && res.StatusCode == 0) {\n            this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        });\n      }\n    }\n    triggerFileInput() {\n      this.fileInput.nativeElement.click();\n    }\n    onFileSelected(event) {\n      const input = event.target;\n      if (input.files && input.files.length > 0) {\n        this.selectedFile = input.files[0];\n        this.importExcel();\n      }\n    }\n    importExcel() {\n      if (this.selectedFile) {\n        const formData = new FormData();\n        formData.append('CFile', this.selectedFile);\n        this._houseService.apiHouseImportHousePost$Json({\n          body: {\n            CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n            CFile: this.selectedFile\n          }\n        }).subscribe(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG(res.Message);\n            this.getHouseList().subscribe();\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        });\n      }\n    }\n    getListHouseHold() {\n      this._houseService.apiHouseGetListHouseHoldPost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseHoldOptions = [{\n            value: '',\n            label: '全部'\n          }, ...res.Entries.map(e => {\n            return {\n              value: e,\n              label: e\n            };\n          })];\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n            if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\n              let index = this.houseHoldOptions.findIndex(x => x.value == previous_search.CHouseHoldSelected.value);\n              this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index];\n            } else {\n              this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n            }\n          }\n        }\n      });\n    }\n    formatQuery() {\n      this.bodyRequest = {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      };\n      if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n        this.bodyRequest['CFloor'] = {\n          CFrom: this.searchQuery.CFrom,\n          CTo: this.searchQuery.CTo\n        };\n      }\n      if (this.searchQuery.CHouseHoldSelected) {\n        this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n      }\n      if (this.searchQuery.CHouseTypeSelected.value) {\n        this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n      }\n      if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n        this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n      }\n      if (this.searchQuery.CPayStatusSelected.value) {\n        this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n      }\n      if (this.searchQuery.CProgressSelected.value) {\n        this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n      }\n      if (this.searchQuery.CSignStatusSelected.value) {\n        this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n      }\n      if (this.searchQuery.CQuotationStatusSelected.value) {\n        this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value;\n      }\n      return this.bodyRequest;\n    }\n    sortByFloorDescending(arr) {\n      return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n    }\n    getHouseList() {\n      return this._houseService.apiHouseGetHouseListPost$Json({\n        body: this.formatQuery()\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      }));\n    }\n    onSelectionChangeBuildCase() {\n      // this.getListBuilding()\n      this.getListHouseHold();\n      this.getHouseList().subscribe();\n    }\n    getListBuildCase() {\n      this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n        body: {\n          CIsPagi: false,\n          CStatus: 1\n        }\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\n            return {\n              CBuildCaseName: res.CBuildCaseName,\n              cID: res.cID\n            };\n          }) : [];\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\n              let index = this.userBuildCaseOptions.findIndex(x => x.cID == previous_search.CBuildCaseSelected.cID);\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n            } else {\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n            }\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        }\n      }), tap(() => {\n        // this.getListBuilding()\n        this.getListHouseHold();\n        setTimeout(() => {\n          this.getHouseList().subscribe();\n        }, 500);\n      })).subscribe();\n    }\n    getHouseById(CID, ref) {\n      this.detailSelected = {};\n      this._houseService.apiHouseGetHouseByIdPost$Json({\n        body: {\n          CHouseID: CID\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseDetail = {\n            ...res.Entries,\n            changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n            changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n          };\n          if (res.Entries.CBuildCaseId) {\n            this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n          }\n          this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n          if (res.Entries.CHouseType) {\n            this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n          } else {\n            this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n          }\n          this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n          if (res.Entries.CBuildCaseId) {\n            if (this.houseHoldMain) {\n              this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n            }\n          }\n          this.dialogService.open(ref);\n        }\n      });\n    }\n    findItemInArray(array, key, value) {\n      return array.find(item => item[key] === value);\n    }\n    openModelDetail(ref, item) {\n      this.getHouseById(item.CID, ref);\n    }\n    openModel(ref) {\n      this.houseHoldMain = {\n        CBuildingName: '',\n        CFloor: undefined,\n        CHouseHoldCount: undefined\n      };\n      this.dialogService.open(ref);\n    }\n    formatDate(CChangeDate) {\n      if (CChangeDate) {\n        return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n      }\n      return '';\n    }\n    onSubmitDetail(ref) {\n      this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n        CCustomerName: this.houseDetail.CCustomerName,\n        CHouseHold: this.houseDetail.CHousehold,\n        CHouseID: this.houseDetail.CId,\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n        CIsChange: this.houseDetail.CIsChange,\n        CIsEnable: this.houseDetail.CIsEnable,\n        CMail: this.houseDetail.CMail,\n        CNationalID: this.houseDetail.CNationalId,\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n        CPhone: this.houseDetail.CPhone,\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\n        CChangeEndDate: this.houseDetail.CChangeEndDate\n      };\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._houseService.apiHouseEditHousePost$Json({\n        body: this.editHouseArgsParam\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        } else {\n          this.message.showErrorMSG(res.Message);\n          ref.close();\n        }\n      }), concatMap(() => this.getHouseList())).subscribe();\n    }\n    onSubmit(ref) {\n      let bodyReq = {\n        CCustomerName: this.houseDetail.CCustomerName,\n        CHouseHold: this.houseDetail.CHousehold,\n        CHouseID: this.houseDetail.CId,\n        CHouseType: this.houseDetail.CHouseType,\n        CIsChange: this.houseDetail.CIsChange,\n        CIsEnable: this.houseDetail.CIsEnable,\n        CMail: this.houseDetail.CMail,\n        CNationalID: this.houseDetail.CNationalId,\n        CPayStatus: this.houseDetail.CPayStatus,\n        CPhone: this.houseDetail.CPhone,\n        CProgress: this.houseDetail.CProgress\n      };\n      this._houseService.apiHouseEditHousePost$Json({\n        body: bodyReq\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        }\n      });\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    onNavidateId(type, id) {\n      const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n      this.router.navigate([`/pages/household-management/${type}`, idURL]);\n    }\n    onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n      this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n    }\n    resetSecureKey(item) {\n      if (confirm(\"您想重設密碼嗎？\")) {\n        this._houseService.apiHouseResetHouseSecureKeyPost$Json({\n          body: item.CID\n        }).subscribe(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG(\"執行成功\");\n          }\n        });\n      }\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[建案名稱]', this.houseDetail.CId);\n      this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n      this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n      this.valid.required('[樓層]', this.houseDetail.CFloor);\n      this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n      // if (this.editHouseArgsParam.CNationalID) {\n      //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\n      // }\n      this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n      this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n      this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n      this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n      this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n      if (this.houseDetail.CChangeStartDate) {\n        this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n      }\n      if (this.houseDetail.CChangeEndDate) {\n        this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n      }\n      this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n    }\n    validationHouseHoldMain() {\n      this.valid.clear();\n      this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n      this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n      this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n      this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n      this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n    }\n    addHouseHoldMain(ref) {\n      this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n        body: this.houseHoldMain\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        }\n      }), concatMap(() => this.getHouseList())).subscribe();\n    } // 開啟報價單對話框\n    openQuotation(dialog, item) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.currentHouse = item;\n        _this.quotationItems = [];\n        _this.totalAmount = 0;\n        _this.currentQuotationId = 0; // 重置報價單ID\n        _this.isQuotationEditable = true; // 預設可編輯\n        // 重置百分比費用設定（固定營業稅5%）\n        _this.additionalFeeName = '營業稅';\n        _this.additionalFeePercentage = 5;\n        _this.additionalFeeAmount = 0;\n        _this.finalTotalAmount = 0;\n        _this.enableAdditionalFee = true;\n        // 載入現有報價資料\n        try {\n          const response = yield _this.quotationService.getQuotationByHouseId(item.CID).toPromise();\n          if (response && response.StatusCode === 0 && response.Entries) {\n            // 保存當前的報價單ID\n            _this.currentQuotationId = response.Entries.CQuotationVersionId || 0;\n            // 根據 cQuotationStatus 決定是否可編輯\n            if (response.Entries.CQuotationStatus === 2) {\n              // 2: 已報價\n              _this.isQuotationEditable = false;\n            } else {\n              _this.isQuotationEditable = true;\n            }\n            // 載入額外費用設定（固定營業稅5%，不從後端載入）\n            _this.enableAdditionalFee = true;\n            _this.additionalFeeName = '營業稅';\n            _this.additionalFeePercentage = 5;\n            // 檢查 Entries 是否有 Items 陣列\n            if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\n              // 將 API 回傳的資料轉換為 QuotationItem 格式\n              _this.quotationItems = response.Entries.Items.map(entry => ({\n                cHouseID: response.Entries.CHouseID || item.CID,\n                cQuotationID: response.Entries.CQuotationID,\n                cItemName: entry.CItemName || '',\n                cUnit: entry.CUnit || '',\n                cUnitPrice: entry.CUnitPrice || 0,\n                cCount: entry.CCount || 1,\n                cStatus: entry.CStatus || 1,\n                CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\n                cRemark: entry.CRemark || '',\n                cQuotationStatus: entry.CQuotationStatus\n              }));\n              _this.calculateTotal();\n            } else {}\n          } else {}\n        } catch (error) {\n          console.error('載入報價資料失敗:', error);\n        }\n        _this.dialogService.open(dialog, {\n          context: item,\n          closeOnBackdropClick: false\n        });\n      })();\n    }\n    // 產生新報價單\n    createNewQuotation() {\n      this.currentQuotationId = 0;\n      this.quotationItems = [];\n      this.isQuotationEditable = true;\n      this.totalAmount = 0;\n      this.finalTotalAmount = 0;\n      this.additionalFeeAmount = 0;\n      this.enableAdditionalFee = true;\n      // 顯示成功訊息\n      this.message.showSucessMSG('已產生新報價單，可開始編輯');\n    }\n    // 新增自定義報價項目\n    addQuotationItem() {\n      this.quotationItems.push({\n        cHouseID: this.currentHouse?.CID || 0,\n        cItemName: '',\n        cUnit: '',\n        cUnitPrice: 0,\n        cCount: 1,\n        cStatus: 1,\n        CQuotationItemType: CQuotationItemType.自定義,\n        cRemark: ''\n      });\n    }\n    // 載入客變需求\n    loadDefaultItems() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (!_this2.currentHouse?.CID) {\n            _this2.message.showErrorMSG('請先選擇戶別');\n            return;\n          }\n          const request = {\n            CBuildCaseID: _this2.searchQuery?.CBuildCaseSelected?.cID || 0,\n            CHouseID: _this2.currentHouse.CID\n          };\n          const response = yield _this2.quotationService.loadDefaultItems(request).toPromise();\n          if (response?.success && response.data) {\n            const defaultItems = response.data.map(x => ({\n              cQuotationID: x.CQuotationID,\n              cHouseID: _this2.currentHouse?.CID,\n              cItemName: x.CItemName,\n              cUnit: x.CUnit || '',\n              cUnitPrice: x.CUnitPrice,\n              cCount: x.CCount,\n              cStatus: x.CStatus,\n              CQuotationItemType: CQuotationItemType.客變需求,\n              cRemark: x.CRemark\n            }));\n            _this2.quotationItems.push(...defaultItems);\n            _this2.calculateTotal();\n            _this2.message.showSucessMSG('載入客變需求成功');\n          } else {\n            _this2.message.showErrorMSG(response?.message || '載入客變需求失敗');\n          }\n        } catch (error) {\n          console.error('載入客變需求錯誤:', error);\n          _this2.message.showErrorMSG('載入客變需求失敗');\n        }\n      })();\n    }\n    // 載入選樣資料\n    loadRegularItems() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (!_this3.currentHouse?.CID) {\n            _this3.message.showErrorMSG('請先選擇戶別');\n            return;\n          }\n          const request = {\n            CBuildCaseID: _this3.searchQuery?.CBuildCaseSelected?.cID || 0,\n            CHouseID: _this3.currentHouse.CID\n          };\n          const response = yield _this3.quotationService.loadRegularItems(request).toPromise();\n          if (response?.success && response.data) {\n            const regularItems = response.data.map(x => ({\n              cQuotationID: x.CQuotationID,\n              cHouseID: _this3.currentHouse?.CID,\n              cItemName: x.CItemName,\n              cUnit: x.CUnit || '',\n              cUnitPrice: x.CUnitPrice,\n              cCount: x.CCount,\n              cStatus: x.CStatus,\n              CQuotationItemType: CQuotationItemType.選樣,\n              // 選樣資料\n              cRemark: x.CRemark || ''\n            }));\n            _this3.quotationItems.push(...regularItems);\n            _this3.calculateTotal();\n            _this3.message.showSucessMSG('載入選樣資料成功');\n          } else {\n            _this3.message.showErrorMSG(response?.message || '載入選樣資料失敗');\n          }\n        } catch (error) {\n          console.error('載入選樣資料錯誤:', error);\n          _this3.message.showErrorMSG('載入選樣資料失敗');\n        }\n      })();\n    }\n    // 移除報價項目\n    removeQuotationItem(index) {\n      const item = this.quotationItems[index];\n      this.quotationItems.splice(index, 1);\n      this.calculateTotal();\n    }\n    // 計算總金額\n    calculateTotal() {\n      this.totalAmount = this.quotationItems.reduce((sum, item) => {\n        return sum + item.cUnitPrice * item.cCount;\n      }, 0);\n      this.calculateFinalTotal();\n    }\n    // 計算百分比費用和最終總金額（固定營業稅5%）\n    calculateFinalTotal() {\n      // 固定計算營業稅5%\n      this.additionalFeeAmount = Math.round(this.totalAmount * 0.05);\n      this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\n    }\n    // 格式化金額\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('zh-TW', {\n        style: 'currency',\n        currency: 'TWD',\n        minimumFractionDigits: 0\n      }).format(amount);\n    }\n    // 儲存報價單\n    saveQuotation(ref) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        if (_this4.quotationItems.length === 0) {\n          _this4.message.showErrorMSG('請先新增報價項目');\n          return;\n        }\n        // 驗證必填欄位 (調整：允許單價和數量為負數)\n        const invalidItems = _this4.quotationItems.filter(item => !item.cItemName.trim());\n        if (invalidItems.length > 0) {\n          _this4.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\n          return;\n        }\n        try {\n          const request = {\n            houseId: _this4.currentHouse.CID,\n            items: _this4.quotationItems,\n            quotationId: _this4.currentQuotationId,\n            // 傳遞當前的報價單ID\n            // 額外費用相關欄位\n            cShowOther: _this4.enableAdditionalFee,\n            // 啟用額外費用\n            cOtherName: _this4.additionalFeeName,\n            // 額外費用名稱\n            cOtherPercent: _this4.additionalFeePercentage // 額外費用百分比\n          };\n          const response = yield _this4.quotationService.saveQuotation(request).toPromise();\n          if (response?.success) {\n            _this4.message.showSucessMSG('報價單儲存成功');\n            ref.close();\n          } else {\n            _this4.message.showErrorMSG(response?.message || '儲存失敗');\n          }\n        } catch (error) {\n          _this4.message.showErrorMSG('報價單儲存失敗');\n        }\n      })();\n    }\n    // 匯出報價單\n    exportQuotation() {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const blob = yield _this5.quotationService.exportQuotation(_this5.currentHouse.CID).toPromise();\n          if (blob) {\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `報價單_${_this5.currentHouse.CHouseHold}_${_this5.currentHouse.CFloor}樓.pdf`;\n            link.click();\n            window.URL.revokeObjectURL(url);\n          } else {\n            _this5.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\n          }\n        } catch (error) {\n          _this5.message.showErrorMSG('匯出報價單失敗');\n        }\n      })();\n    }\n    // 列印報價單\n    printQuotation() {\n      if (this.quotationItems.length === 0) {\n        this.message.showErrorMSG('沒有可列印的報價項目');\n        return;\n      }\n      try {\n        // 建立列印內容\n        const printContent = this.generatePrintContent();\n        // 建立新的視窗進行列印\n        const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n        if (printWindow) {\n          printWindow.document.open();\n          printWindow.document.write(printContent);\n          printWindow.document.close();\n          // 等待內容載入完成後列印\n          printWindow.onload = function () {\n            setTimeout(() => {\n              printWindow.print();\n              // 列印後不自動關閉視窗，讓使用者可以預覽\n            }, 500);\n          };\n        } else {\n          this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\n        }\n      } catch (error) {\n        console.error('列印報價單錯誤:', error);\n        this.message.showErrorMSG('列印報價單時發生錯誤');\n      }\n    }\n    // 產生列印內容\n    generatePrintContent() {\n      // 使用導入的模板\n      const template = QUOTATION_TEMPLATE;\n      // 準備數據\n      const currentDate = new Date().toLocaleDateString('zh-TW');\n      const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\n      // 生成項目HTML\n      let itemsHtml = '';\n      this.quotationItems.forEach((item, index) => {\n        const subtotal = item.cUnitPrice * item.cCount;\n        const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\n        const unit = item.cUnit || '';\n        itemsHtml += `\n          <tr>\n            <td class=\"text-center\">${index + 1}</td>\n            <td>${item.cItemName}</td>\n            <td class=\"text-right\">${this.formatCurrency(item.cUnitPrice)}</td>\n            <td class=\"text-center\">${unit}</td>\n            <td class=\"text-center\">${item.cCount}</td>\n            <td class=\"text-right\">${this.formatCurrency(subtotal)}</td>\n            <td class=\"text-center\">${quotationType}</td>\n          </tr>\n        `;\n      });\n      // 生成額外費用HTML\n      const additionalFeeHtml = this.enableAdditionalFee ? `\n        <div class=\"additional-fee\">\n          ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\n        </div>\n      ` : '';\n      // 替換模板中的占位符\n      const html = template.replace(/{{buildCaseName}}/g, buildCaseName).replace(/{{houseHold}}/g, this.currentHouse?.CHouseHold || '').replace(/{{floor}}/g, this.currentHouse?.CFloor || '').replace(/{{customerName}}/g, this.currentHouse?.CCustomerName || '').replace(/{{printDate}}/g, currentDate).replace(/{{itemsHtml}}/g, itemsHtml).replace(/{{subtotalAmount}}/g, this.formatCurrency(this.totalAmount)).replace(/{{additionalFeeHtml}}/g, additionalFeeHtml).replace(/{{totalAmount}}/g, this.formatCurrency(this.finalTotalAmount)).replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\n      return html;\n    }\n    // 鎖定報價單\n    lockQuotation(ref) {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        if (_this6.quotationItems.length === 0) {\n          _this6.message.showErrorMSG('請先新增報價項目');\n          return;\n        }\n        if (!_this6.currentQuotationId) {\n          _this6.message.showErrorMSG('無效的報價單ID');\n          return;\n        }\n        try {\n          const response = yield _this6.quotationService.lockQuotation(_this6.currentQuotationId).toPromise();\n          if (response.success) {\n            _this6.message.showSucessMSG('報價單已成功鎖定');\n            console.log('報價單鎖定成功:', {\n              quotationId: _this6.currentQuotationId,\n              message: response.message\n            });\n          } else {\n            _this6.message.showErrorMSG(response.message || '報價單鎖定失敗');\n            console.error('報價單鎖定失敗:', response.message);\n          }\n          ref.close();\n        } catch (error) {\n          _this6.message.showErrorMSG('報價單鎖定失敗');\n          console.error('鎖定報價單錯誤:', error);\n        }\n      })();\n    }\n    // 取得報價類型文字\n    getQuotationTypeText(quotationType) {\n      switch (quotationType) {\n        case CQuotationItemType.客變需求:\n          return '客變需求';\n        case CQuotationItemType.自定義:\n          return '自定義';\n        case CQuotationItemType.選樣:\n          return '選樣';\n        default:\n          return '未知';\n      }\n    }\n    getQuotationStatusText(status) {\n      switch (status) {\n        case EnumQuotationStatus.待報價:\n          return '待報價';\n        case EnumQuotationStatus.已報價:\n          return '已報價';\n        case EnumQuotationStatus.已簽回:\n          return '已簽回';\n        default:\n          return '未知';\n      }\n    }\n    // 模板匯入相關方法\n    openTemplateImportDialog(dialog) {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        // 重置模板相關資料\n        _this7.templateList = [];\n        _this7.templateDetailList = [];\n        _this7.selectedTemplateForImport = null;\n        _this7.templateSearchKeyword = '';\n        // 重置分頁相關資料\n        _this7.templateCurrentPage = 1;\n        _this7.paginatedTemplateList = [];\n        _this7.templateTotalItems = 0;\n        // 載入模板列表\n        yield _this7.loadTemplateList();\n        // 開啟對話框\n        _this7.dialogService.open(dialog, {\n          closeOnBackdropClick: false\n        });\n      })();\n    }\n    // 載入模板列表\n    loadTemplateList() {\n      var _this8 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const getTemplateListArgs = {\n            CTemplateType: 1,\n            // 1=客變需求\n            PageIndex: _this8.templateCurrentPage,\n            PageSize: _this8.templatePageSize,\n            CTemplateName: _this8.templateSearchKeyword || null\n          };\n          const response = yield _this8.templateService.apiTemplateGetTemplateListPost$Json({\n            body: getTemplateListArgs\n          }).toPromise();\n          if (response?.StatusCode === 0 && response.Entries) {\n            _this8.templateList = response.Entries.map(item => ({\n              TemplateID: item.CTemplateId,\n              TemplateName: item.CTemplateName || '',\n              Description: `建立時間: ${item.CCreateDt ? new Date(item.CCreateDt).toLocaleDateString() : '未知'}`\n            }));\n            // 不再需要 filteredTemplateList，直接使用 templateList\n            // 保存 API 返回的總項目數\n            _this8.templateTotalItems = response.TotalItems || 0;\n            // 初始化分頁\n            _this8.updatePaginatedTemplateList();\n          } else {\n            _this8.templateList = [];\n            _this8.templateTotalItems = 0;\n            _this8.message.showErrorMSG('載入模板列表失敗');\n          }\n        } catch (error) {\n          console.error('載入模板列表錯誤:', error);\n          _this8.message.showErrorMSG('載入模板列表失敗');\n        }\n      })();\n    }\n    // 篩選模板\n    filterTemplates() {\n      var _this9 = this;\n      return _asyncToGenerator(function* () {\n        // 重置到第一頁並重新載入資料\n        _this9.templateCurrentPage = 1;\n        yield _this9.loadTemplateList();\n      })();\n    }\n    // 清除模板搜尋\n    clearTemplateSearch() {\n      this.templateSearchKeyword = '';\n      this.filterTemplates();\n    }\n    // 選擇模板\n    selectTemplateForImport(template) {\n      var _this10 = this;\n      return _asyncToGenerator(function* () {\n        _this10.selectedTemplateForImport = template;\n        yield _this10.loadTemplateDetails(template.TemplateID);\n      })();\n    }\n    // 載入模板詳情\n    loadTemplateDetails(templateId) {\n      var _this11 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const args = {\n            templateId: templateId\n          };\n          const response = yield _this11.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n            body: args\n          }).toPromise();\n          if (response?.StatusCode === 0 && response.Entries) {\n            _this11.templateDetailList = response.Entries.map(item => ({\n              CTemplateDetailId: item.CTemplateDetailId || 0,\n              CTemplateId: item.CTemplateId || templateId,\n              CReleateId: item.CReleateId || 0,\n              CReleateName: item.CReleateName || '',\n              CGroupName: item.CGroupName || '',\n              selected: false // 預設不選中\n            }));\n          } else {\n            _this11.templateDetailList = [];\n            _this11.message.showErrorMSG('載入模板詳情失敗');\n          }\n        } catch (error) {\n          console.error('載入模板詳情錯誤:', error);\n          _this11.message.showErrorMSG('載入模板詳情失敗');\n        }\n      })();\n    }\n    // 更新選中項目計數\n    updateSelectedCount() {\n      // 這個方法在 HTML 中被調用，用於觸發變更檢測\n    }\n    // 全選模板項目\n    selectAllTemplateItems() {\n      this.templateDetailList.forEach(item => {\n        item.selected = true;\n      });\n    }\n    // 取消全選模板項目\n    deselectAllTemplateItems() {\n      this.templateDetailList.forEach(item => {\n        item.selected = false;\n      });\n    }\n    // 獲取選中的模板項目數量\n    getSelectedTemplateItemsCount() {\n      return this.templateDetailList.filter(item => item.selected).length;\n    }\n    // 匯入選中的模板項目\n    importSelectedTemplateItems(ref) {\n      var _this12 = this;\n      return _asyncToGenerator(function* () {\n        const selectedItems = _this12.templateDetailList.filter(item => item.selected);\n        if (selectedItems.length === 0) {\n          _this12.message.showErrorMSG('請選擇要匯入的項目');\n          return;\n        }\n        try {\n          // 將選中的模板項目轉換為報價單項目格式\n          const importedItems = selectedItems.map(item => ({\n            cHouseID: _this12.currentHouse?.CID || 0,\n            cItemName: item.CReleateName,\n            cUnit: '',\n            // 模板中沒有單位資訊，預設為空\n            cUnitPrice: 0,\n            // 模板中沒有價格資訊，預設為0\n            cCount: 1,\n            // 預設數量為1\n            cStatus: 1,\n            CQuotationItemType: CQuotationItemType.自定義,\n            // 從模板匯入的項目保持舊版設定\n            cRemark: item.CGroupName ? `群組: ${item.CGroupName}` : '' // 將群組名稱作為備註\n          }));\n          // 添加到報價單項目列表\n          _this12.quotationItems.push(...importedItems);\n          // 重新計算總金額\n          _this12.calculateTotal();\n          // 顯示成功訊息\n          _this12.message.showSucessMSG(`成功匯入 ${selectedItems.length} 個項目`);\n          // 關閉對話框\n          ref.close();\n        } catch (error) {\n          console.error('匯入模板項目錯誤:', error);\n          _this12.message.showErrorMSG('匯入模板項目失敗');\n        }\n      })();\n    }\n    // 模板分頁相關方法\n    updatePaginatedTemplateList() {\n      // 由於使用 API 分頁，直接使用當前載入的模板列表\n      this.paginatedTemplateList = [...this.templateList];\n    }\n    getTotalTemplatePages() {\n      const totalItems = this.templateTotalItems || this.templateList.length;\n      const totalPages = Math.ceil(totalItems / this.templatePageSize);\n      return Math.max(1, totalPages); // 至少返回1頁\n    }\n    getTemplateStartIndex() {\n      return (this.templateCurrentPage - 1) * this.templatePageSize;\n    }\n    getTemplateEndIndex() {\n      const endIndex = this.templateCurrentPage * this.templatePageSize;\n      const totalItems = this.templateTotalItems || this.templateList.length;\n      return Math.min(endIndex, totalItems);\n    }\n    previousTemplatePage() {\n      var _this13 = this;\n      return _asyncToGenerator(function* () {\n        if (_this13.templateCurrentPage > 1) {\n          _this13.templateCurrentPage--;\n          yield _this13.loadTemplateList();\n        }\n      })();\n    }\n    nextTemplatePage() {\n      var _this14 = this;\n      return _asyncToGenerator(function* () {\n        if (_this14.templateCurrentPage < _this14.getTotalTemplatePages()) {\n          _this14.templateCurrentPage++;\n          yield _this14.loadTemplateList();\n        }\n      })();\n    }\n    static {\n      this.ɵfac = function HouseholdManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HouseholdManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i6.HouseHoldMainService), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i9.EventService), i0.ɵɵdirectiveInject(i10.UtilityService), i0.ɵɵdirectiveInject(i11.QuotationService), i0.ɵɵdirectiveInject(i6.TemplateService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HouseholdManagementComponent,\n        selectors: [[\"ngx-household-management\"]],\n        viewQuery: function HouseholdManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 119,\n        vars: 23,\n        consts: [[\"fileInput\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialogHouseholdMain\", \"\"], [\"dialogQuotation\", \"\"], [\"dialogTemplateImport\", \"\"], [\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cHouseType\", 1, \"label\", \"col-3\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"text\", \"id\", \"CFrom\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"text\", \"id\", \"CTo\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPayStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7E73\\u6B3E\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cQuotationStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5831\\u50F9\\u55AE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [\"type\", \"file\", \"accept\", \".xlsx, .xls\", 2, \"display\", \"none\", 3, \"change\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"text-center\", \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [\"class\", \"px-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", \"px-8\", 3, \"click\"], [\"class\", \"btn btn-primary m-2 bg-[#169BD5] px-8\", 3, \"click\", 4, \"ngIf\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"cBuildCaseId\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6236\\u578B\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6A13\\u5C64\", \"min\", \"1\", \"max\", \"100\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cCustomerName\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5BA2\\u6236\\u59D3\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cNationalId\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8EAB\\u5206\\u8B49\\u5B57\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cMail\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPhone\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u806F\\u7D61\\u96FB\\u8A71\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHouseType\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u6236\\u5225\\u985E\\u578B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"form-group\", 4, \"ngIf\"], [\"for\", \"cIsChange\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"form-group\", \"flex\", \"flex-row\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", \"content-center\", 2, \"min-width\", \"75px\"], [1, \"max-w-xs\", \"flex\", \"flex-row\"], [1, \"w-1/2\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"mr-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"ml-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"cPayStatus\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u4ED8\\u6B3E\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"m-2\", \"bg-[#169BD5]\", \"px-8\", 3, \"click\"], [\"for\", \"cBuildingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u68DF\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CHouseHoldCount\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"mr-4\", 3, \"click\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [2, \"width\", \"1200px\", \"max-height\", \"95vh\"], [\"class\", \"mb-4 d-flex justify-content-between\", 4, \"ngIf\"], [\"class\", \"mb-4 alert alert-warning\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-bordered\"], [\"width\", \"25%\"], [\"width\", \"15%\"], [\"width\", \"8%\"], [\"width\", \"18%\"], [\"width\", \"10%\"], [4, \"ngIf\"], [1, \"mt-4\"], [1, \"card\", \"border-0\", \"shadow-sm\"], [1, \"card-body\", \"p-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"h6\", \"mb-0\", \"text-muted\"], [1, \"h5\", \"mb-0\", \"text-dark\", \"fw-bold\"], [1, \"tax-section\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\", \"p-3\", \"bg-light\", \"rounded\"], [1, \"d-flex\", \"align-items-center\"], [1, \"tax-icon-wrapper\", \"me-3\"], [1, \"fas\", \"fa-receipt\", \"text-info\"], [1, \"fw-medium\", \"text-dark\"], [1, \"tax-percentage\", \"ms-1\", \"badge\", \"bg-info\", \"text-white\"], [1, \"small\", \"text-muted\", \"mt-1\"], [1, \"fas\", \"fa-info-circle\", \"me-1\"], [1, \"text-end\"], [1, \"tax-amount\", \"h6\", \"mb-0\", \"text-info\", \"fw-bold\"], [1, \"small\", \"text-muted\"], [1, \"my-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"h4\", \"mb-0\", \"text-primary\", \"fw-bold\"], [1, \"d-flex\", \"justify-content-between\"], [\"title\", \"\\u5217\\u5370\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-print\", \"me-1\"], [\"class\", \"btn btn-outline-success btn-sm me-2\", \"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-warning m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"mb-4\", \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"mb-4\", \"alert\", \"alert-warning\"], [1, \"fas\", \"fa-lock\", \"me-2\"], [\"type\", \"text\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"min\", \"0\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"text-right\"], [1, \"badge\"], [\"class\", \"btn btn-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"text-muted\"], [1, \"fas\", \"fa-lock\"], [\"colspan\", \"7\", 1, \"text-center\", \"text-muted\", \"py-4\"], [\"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"btn\", \"btn-warning\", \"m-2\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-primary\", \"m-2\", 3, \"click\", \"disabled\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\"], [1, \"mb-0\"], [1, \"fas\", \"fa-download\", \"mr-2\", \"text-primary\"], [1, \"badge\", \"badge-info\"], [1, \"template-selection-section\", \"mb-4\"], [1, \"section-title\", \"mb-3\"], [1, \"fas\", \"fa-list\", \"mr-2\"], [1, \"search-container\", \"mb-3\"], [1, \"input-group\"], [1, \"input-group-prepend\"], [1, \"input-group-text\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"class\", \"input-group-append\", 4, \"ngIf\"], [1, \"template-list\"], [\"class\", \"template-item\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"empty-state text-center py-4\", 4, \"ngIf\"], [\"class\", \"pagination-container mt-3\", 4, \"ngIf\"], [\"class\", \"template-details-section\", 4, \"ngIf\"], [\"class\", \"import-info\", 4, \"ngIf\"], [1, \"actions\"], [1, \"btn\", \"btn-outline-secondary\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\", \"mr-1\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"template-item\", 3, \"click\"], [1, \"template-info\"], [1, \"template-name\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\"], [1, \"template-description\", \"text-muted\", \"small\"], [1, \"template-actions\"], [\"class\", \"fas fa-check-circle text-success\", 4, \"ngIf\"], [1, \"fas\", \"fa-check-circle\", \"text-success\"], [1, \"empty-state\", \"text-center\", \"py-4\"], [1, \"fas\", \"fa-search\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"], [1, \"pagination-container\", \"mt-3\"], [1, \"pagination-info\"], [1, \"pagination-controls\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"mr-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"pagination-current\", \"mx-2\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"template-details-section\"], [1, \"fas\", \"fa-list-ul\", \"mr-2\"], [1, \"badge\", \"badge-secondary\", \"ml-2\"], [1, \"details-list\", 2, \"max-height\", \"250px\", \"overflow-y\", \"auto\"], [\"class\", \"detail-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"bulk-actions mt-3\", 4, \"ngIf\"], [1, \"detail-item\"], [1, \"detail-checkbox\"], [3, \"checkedChange\", \"checked\"], [1, \"detail-content\"], [1, \"detail-name\"], [1, \"detail-meta\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [1, \"meta-item\"], [1, \"fas\", \"fa-hashtag\", \"mr-1\"], [1, \"fas\", \"fa-layer-group\", \"mr-1\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"bulk-actions\", \"mt-3\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-check-square\", \"mr-1\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-square\", \"mr-1\"], [1, \"ml-3\", \"text-muted\"], [1, \"import-info\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"]],\n        template: function HouseholdManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 7)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 8);\n            i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 9)(7, \"div\", 10)(8, \"div\", 11)(9, \"label\", 12);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function HouseholdManagementComponent_Template_nb_select_selectedChange_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n            });\n            i0.ɵɵtemplate(12, HouseholdManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11)(15, \"label\", 15);\n            i0.ɵɵtext(16, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"nb-select\", 16);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseTypeSelected, $event) || (ctx.searchQuery.CHouseTypeSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(18, HouseholdManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"div\", 10)(20, \"div\", 17)(21, \"label\", 18);\n            i0.ɵɵtext(22, \"\\u6A13 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"nb-form-field\", 19)(24, \"input\", 20);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_24_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"label\", 21);\n            i0.ɵɵtext(26, \"~ \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"nb-form-field\", 22)(28, \"input\", 23);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_28_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelement(29, \"div\", 10);\n            i0.ɵɵelementStart(30, \"div\", 10)(31, \"div\", 11)(32, \"label\", 24);\n            i0.ɵɵtext(33, \" \\u6236\\u578B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"nb-select\", 25);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseHoldSelected, $event) || (ctx.searchQuery.CHouseHoldSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(35, HouseholdManagementComponent_nb_option_35_Template, 2, 2, \"nb-option\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(36, \"div\", 10)(37, \"div\", 11)(38, \"label\", 26);\n            i0.ɵɵtext(39, \" \\u7E73\\u6B3E\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"nb-select\", 27);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CPayStatusSelected, $event) || (ctx.searchQuery.CPayStatusSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(41, HouseholdManagementComponent_nb_option_41_Template, 2, 2, \"nb-option\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(42, \"div\", 10)(43, \"div\", 11)(44, \"label\", 28);\n            i0.ɵɵtext(45, \" \\u9032\\u5EA6 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"nb-select\", 29);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CProgressSelected, $event) || (ctx.searchQuery.CProgressSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(47, HouseholdManagementComponent_nb_option_47_Template, 2, 2, \"nb-option\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(48, \"div\", 10)(49, \"div\", 11)(50, \"label\", 30);\n            i0.ɵɵtext(51, \" \\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"nb-select\", 31);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CIsEnableSeleted, $event) || (ctx.searchQuery.CIsEnableSeleted = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(53, HouseholdManagementComponent_nb_option_53_Template, 2, 2, \"nb-option\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(54, \"div\", 10)(55, \"div\", 11)(56, \"label\", 32);\n            i0.ɵɵtext(57, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"nb-select\", 33);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CSignStatusSelected, $event) || (ctx.searchQuery.CSignStatusSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(59, HouseholdManagementComponent_nb_option_59_Template, 2, 2, \"nb-option\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(60, \"div\", 10)(61, \"div\", 11)(62, \"label\", 34);\n            i0.ɵɵtext(63, \" \\u5831\\u50F9\\u55AE\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"nb-select\", 35);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_64_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CQuotationStatusSelected, $event) || (ctx.searchQuery.CQuotationStatusSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(65, HouseholdManagementComponent_nb_option_65_Template, 2, 2, \"nb-option\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelement(66, \"div\", 10);\n            i0.ɵɵelementStart(67, \"div\", 36)(68, \"div\", 37)(69, \"button\", 38);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_69_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵtext(70, \" \\u67E5\\u8A62 \");\n            i0.ɵɵelement(71, \"i\", 39);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(72, \"div\", 36)(73, \"div\", 40);\n            i0.ɵɵtemplate(74, HouseholdManagementComponent_button_74_Template, 2, 0, \"button\", 41);\n            i0.ɵɵelementStart(75, \"button\", 42);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_75_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onNavidateId(\"modify-floor-plan\"));\n            });\n            i0.ɵɵtext(76, \" \\u4FEE\\u6539\\u6A13\\u5C64\\u6236\\u578B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"button\", 42);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_77_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.exportHouse());\n            });\n            i0.ɵɵtext(78, \" \\u532F\\u51FA\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"input\", 43, 0);\n            i0.ɵɵlistener(\"change\", function HouseholdManagementComponent_Template_input_change_79_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileSelected($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(81, \"button\", 44);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_81_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.triggerFileInput());\n            });\n            i0.ɵɵtext(82, \" \\u532F\\u5165\\u66F4\\u65B0\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(83, \"div\", 45)(84, \"table\", 46)(85, \"thead\")(86, \"tr\", 47)(87, \"th\", 48);\n            i0.ɵɵtext(88, \"\\u6236\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(89, \"th\", 48);\n            i0.ɵɵtext(90, \"\\u6A13\\u5C64\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"th\", 48);\n            i0.ɵɵtext(92, \"\\u6236\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"th\", 48);\n            i0.ɵɵtext(94, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(95, \"th\", 48);\n            i0.ɵɵtext(96, \"\\u9032\\u5EA6\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(97, \"th\", 48);\n            i0.ɵɵtext(98, \"\\u7E73\\u6B3E\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(99, \"th\", 48);\n            i0.ɵɵtext(100, \"\\u7C3D\\u56DE\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(101, \"th\", 48);\n            i0.ɵɵtext(102, \"\\u5831\\u50F9\\u55AE\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(103, \"th\", 48);\n            i0.ɵɵtext(104, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(105, \"th\", 49);\n            i0.ɵɵtext(106, \"\\u64CD\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(107, \"tbody\");\n            i0.ɵɵtemplate(108, HouseholdManagementComponent_tr_108_Template, 31, 13, \"tr\", 50);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(109, \"nb-card-footer\", 51)(110, \"ngb-pagination\", 52);\n            i0.ɵɵtwoWayListener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_110_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_110_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(111, HouseholdManagementComponent_ng_template_111_Template, 6, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(113, HouseholdManagementComponent_ng_template_113_Template, 20, 5, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(115, HouseholdManagementComponent_ng_template_115_Template, 69, 13, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(117, HouseholdManagementComponent_ng_template_117_Template, 33, 9, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseTypeSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseHoldSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseHoldOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CPayStatusSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.payStatusOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CProgressSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.progressOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CIsEnableSeleted);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.cIsEnableOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CSignStatusSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.signStatusOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CQuotationStatusSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.quotationStatusOptions);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(34);\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseList);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i12.NgForOf, i12.NgIf, SharedModule, i13.DefaultValueAccessor, i13.NumberValueAccessor, i13.NgControlStatus, i13.MaxLengthValidator, i13.MinValidator, i13.MaxValidator, i13.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, i3.NbCheckboxComponent, i3.NbInputDirective, i3.NbSelectComponent, i3.NbOptionComponent, i3.NbFormFieldComponent, i3.NbPrefixDirective, i3.NbIconComponent, i3.NbDatepickerDirective, i3.NbDatepickerComponent, i14.NgbPagination, i15.BreadcrumbComponent, i16.BaseLabelDirective, NbDatepickerModule, NbDateFnsDateModule],\n        styles: [\".card[_ngcontent-%COMP%]{transition:all .3s ease}.card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #0000001a!important}.tax-section[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa,#e9ecef)!important;border:1px solid #dee2e6;transition:all .3s ease;position:relative;overflow:hidden}.tax-section[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#e9ecef,#dee2e6)!important;transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}.tax-section[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:4px;height:100%;background:linear-gradient(to bottom,#0dcaf0,#0aa2c0);transition:width .3s ease}.tax-section[_ngcontent-%COMP%]:hover:before{width:6px}.tax-icon-wrapper[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:linear-gradient(135deg,#0dcaf0,#0aa2c0);display:flex;align-items:center;justify-content:center;box-shadow:0 2px 8px #0dcaf04d;transition:all .3s ease}.tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem;color:#fff!important}.tax-icon-wrapper[_ngcontent-%COMP%]:hover{transform:scale(1.1);box-shadow:0 4px 12px #0dcaf066}.tax-percentage[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem;border-radius:12px;animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}.tax-amount[_ngcontent-%COMP%]{transition:all .3s ease}.tax-amount[_ngcontent-%COMP%]:hover{transform:scale(1.05);color:#0aa2c0!important}.text-primary[_ngcontent-%COMP%]{color:#0d6efd!important}.text-info[_ngcontent-%COMP%]{color:#0dcaf0!important}hr[_ngcontent-%COMP%]{border-top:2px solid #dee2e6;opacity:.5}.h5[_ngcontent-%COMP%], .h6[_ngcontent-%COMP%]{transition:all .2s ease}.text-primary.fw-bold[_ngcontent-%COMP%]{text-shadow:0 1px 2px rgba(13,110,253,.1);transition:all .3s ease}.text-primary.fw-bold[_ngcontent-%COMP%]:hover{transform:scale(1.02);text-shadow:0 2px 4px rgba(13,110,253,.2)}.fa-info-circle[_ngcontent-%COMP%]{opacity:.7;transition:opacity .2s ease}.fa-info-circle[_ngcontent-%COMP%]:hover{opacity:1}@media (max-width: 768px){.card-body[_ngcontent-%COMP%]{padding:1.5rem!important}.h4[_ngcontent-%COMP%], .h5[_ngcontent-%COMP%], .h6[_ngcontent-%COMP%]{font-size:1rem!important}.tax-icon-wrapper[_ngcontent-%COMP%]{width:35px;height:35px}.tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.tax-section[_ngcontent-%COMP%]{padding:1rem!important}.tax-section[_ngcontent-%COMP%]:before{width:3px}.tax-section[_ngcontent-%COMP%]:hover:before{width:4px}}.template-selection-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#495057;font-weight:600;border-bottom:2px solid #e9ecef;padding-bottom:.5rem}.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%]{background-color:#f8f9fa;border-color:#ced4da;color:#6c757d}.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-color:#ced4da;transition:all .3s ease}.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#0d6efd;box-shadow:0 0 0 .2rem #0d6efd40}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]{border:1px solid #e9ecef;border-radius:.375rem;background-color:#fff;min-height:400px;max-height:500px;overflow-y:auto}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]{padding:.75rem 1rem;border-bottom:1px solid #f1f3f4;cursor:pointer;transition:all .3s ease;display:flex;justify-content:space-between;align-items:center}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;transform:translate(4px)}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]{background-color:#e7f3ff;border-left:4px solid #0d6efd;transform:translate(4px)}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]{color:#0d6efd;font-weight:600}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]{flex:1}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]{font-weight:500;margin-bottom:.25rem;transition:color .3s ease}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#6c757d}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.4}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem;opacity:0;transition:opacity .3s ease}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:1}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{color:#6c757d}.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.template-details-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#495057;font-weight:600;border-bottom:2px solid #e9ecef;padding-bottom:.5rem}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]{border:1px solid #e9ecef;border-radius:.375rem;background-color:#fff}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{padding:.75rem 1rem;border-bottom:1px solid #f1f3f4;display:flex;align-items:center;transition:background-color .3s ease}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-checkbox[_ngcontent-%COMP%]{margin-right:1rem;flex-shrink:0}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]{flex:1}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-name[_ngcontent-%COMP%]{margin-bottom:.25rem;color:#212529}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]{display:flex;gap:1rem;flex-wrap:wrap}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d;display:flex;align-items:center}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem;opacity:.7}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{color:#6c757d}.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]{padding:.75rem;background-color:#f8f9fa;border-radius:.375rem;border:1px solid #e9ecef}.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{transition:all .3s ease}.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px)}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]{color:#6c757d;font-size:.875rem}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]{display:flex;align-items:center}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{transition:all .3s ease;border-radius:.375rem}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .pagination-current[_ngcontent-%COMP%]{font-weight:500;color:#495057;font-size:.875rem;white-space:nowrap}\"]\n      });\n    }\n  }\n  return HouseholdManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}