{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nlet SettingTimePeriodComponent = class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    // 新增的屬性\n    this.buildingGroups = [];\n    this.buildingOptions = [];\n    this.selectedBuilding = '';\n    this.availableFloors = [];\n    // 篩選和搜尋\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 批次設定\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: true,\n      selectedBuildings: [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    this.selectedBuildingForBatch = null;\n    // 表格視圖相關屬性\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.loading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalPages = 1;\n    // 排序相關\n    this.sortField = '';\n    this.sortDirection = 'asc';\n    // 數學函數引用\n    this.Math = Math;\n    // 新增的UI控制屬性\n    this.jumpToPage = 1;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  // 新增的UI控制方法\n  hasActiveFilters() {\n    return !!(this.filterOptions.searchKeyword || this.filterOptions.statusFilter || this.filterOptions.floorFilter);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.filterOptions.searchKeyword) count++;\n    if (this.filterOptions.statusFilter) count++;\n    if (this.filterOptions.floorFilter) count++;\n    return count;\n  }\n  resetFilters() {\n    this.searchQuery = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.selectedBuilding = '';\n    this.clearAllFilters();\n  }\n  clearAllFilters() {\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    this.onSearch();\n  }\n  setQuickFilter(status) {\n    if (this.filterOptions.statusFilter === status) {\n      this.filterOptions.statusFilter = '';\n    } else {\n      this.filterOptions.statusFilter = status;\n    }\n    this.onSearch();\n  }\n  clearSelection() {\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.flattenedHouses.forEach(house => house.selected = false);\n  }\n  getStatusIcon(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return 'fas fa-play-circle';\n      case 'pending':\n        return 'fas fa-clock';\n      case 'expired':\n        return 'fas fa-times-circle';\n      case 'not-set':\n        return 'fas fa-exclamation-triangle';\n      case 'disabled':\n        return 'fas fa-ban';\n      default:\n        return 'fas fa-exclamation-triangle';\n    }\n  }\n  jumpToPageAction() {\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\n      this.goToPage(this.jumpToPage);\n    }\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CBuildingName: '未分類',\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  // 新增：建案變更處理\n  onBuildCaseChange() {\n    // 重置所有相關狀態\n    this.resetAllStates();\n    // 執行查詢\n    this.getHouseChangeDate();\n  }\n  // 新增：重置所有狀態\n  resetAllStates() {\n    // 重置數據\n    this.houseChangeDates = [];\n    this.convertedHouseArray = [];\n    this.buildingGroups = [];\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    // 重置篩選條件\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 重置選擇狀態\n    this.selectAll = false;\n    this.selectedBuilding = '';\n    // 重置分頁\n    this.currentPage = 1;\n    this.totalPages = 1;\n    // 重置可用選項\n    this.buildingOptions = [];\n    this.availableFloors = [];\n    // 重置排序\n    this.sortField = '';\n    this.sortDirection = 'asc';\n  }\n  getHouseChangeDate() {\n    // 如果沒有選擇建案，直接返回\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\n      this.loading = false;\n      return;\n    }\n    this.loading = true;\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      this.loading = false;\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          // 新增：建立棟別分組資料\n          this.buildBuildingGroups(res.Entries);\n          // 新增：建立扁平化資料\n          this.buildFlattenedHouses(res.Entries);\n        }\n      }\n    });\n  }\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n  buildBuildingGroups(data) {\n    const buildingMap = new Map();\n    data.forEach(household => {\n      const houseType = household.CHouseHold || ''; // 戶型\n      household.CHouses?.forEach(house => {\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\n        const floor = house.CFloor || 0;\n        if (!buildingMap.has(buildingName)) {\n          buildingMap.set(buildingName, new Map());\n        }\n        const floorMap = buildingMap.get(buildingName);\n        if (!floorMap.has(floor)) {\n          floorMap.set(floor, []);\n        }\n        floorMap.get(floor).push({\n          CHouseHold: houseType,\n          // 戶型\n          CBuildingName: buildingName,\n          // 棟別\n          CHouseId: house.CHouseId || 0,\n          CFloor: floor,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 轉換為BuildingGroup格式\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n      const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n      .map(([floorNumber, houses]) => ({\n        floorNumber,\n        houses: houses.sort((a, b) => {\n          // 排序邏輯：先按戶型排序，再按樓層排序\n          if (a.CHouseHold !== b.CHouseHold) {\n            return a.CHouseHold.localeCompare(b.CHouseHold);\n          }\n          return a.CFloor - b.CFloor;\n        }),\n        selected: false\n      }));\n      return {\n        name: buildingName,\n        floors,\n        selected: false\n      };\n    }).sort((a, b) => a.name.localeCompare(b.name));\n    // 更新棟別選項和可用樓層\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n    this.updateAvailableFloors();\n  }\n  // 新增：更新可用樓層\n  updateAvailableFloors() {\n    const floorsSet = new Set();\n    this.buildingGroups.forEach(building => {\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n        building.floors.forEach(floor => {\n          floorsSet.add(floor.floorNumber);\n        });\n      }\n    });\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n  }\n  // 修改：棟別選擇變更處理\n  onBuildingChange() {\n    // 重置選擇狀態\n    this.selectedHouses.forEach(house => house.selected = false);\n    this.selectedHouses = [];\n    this.selectAll = false;\n    // 重置分頁到第一頁\n    this.currentPage = 1;\n    // 重置樓層篩選\n    this.filterOptions.floorFilter = '';\n    // 更新可用樓層\n    this.updateAvailableFloors();\n    // 設定棟別篩選並執行搜尋\n    this.filterOptions.buildingFilter = this.selectedBuilding;\n    this.onSearch();\n  }\n  // 新增：取得過濾後的棟別資料\n  getFilteredBuildings() {\n    return this.buildingGroups.filter(building => {\n      // 棟別篩選\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n        return false;\n      }\n      // 關鍵字搜尋 (搜尋戶型)\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n        if (!hasMatchingHouse) {\n          return false;\n        }\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n        if (!hasMatchingStatus) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n        if (!hasMatchingFloor) {\n          return false;\n        }\n      }\n      return true;\n    }).map(building => {\n      // 對每個棟別，也要篩選其樓層和戶別\n      const filteredBuilding = {\n        ...building\n      };\n      filteredBuilding.floors = building.floors.filter(floor => {\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (floor.floorNumber !== floorNumber) {\n            return false;\n          }\n        }\n        // 檢查該樓層是否有符合條件的戶別\n        const hasValidHouses = floor.houses.some(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return hasValidHouses;\n      }).map(floor => {\n        // 篩選戶別\n        const filteredFloor = {\n          ...floor\n        };\n        filteredFloor.houses = floor.houses.filter(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return filteredFloor;\n      });\n      return filteredBuilding;\n    });\n  }\n  // 修復：檢查戶別是否符合狀態篩選\n  matchesStatusFilter(house) {\n    const status = this.getHouseStatus(house);\n    switch (this.filterOptions.statusFilter) {\n      case 'active':\n        return status === 'active';\n      case 'pending':\n        return status === 'pending';\n      case 'expired':\n        return status === 'expired';\n      case 'not-set':\n        return status === 'not-set';\n      case 'disabled':\n        return status === 'disabled';\n      default:\n        return true;\n      // 全部狀態\n    }\n  }\n  // 修復：取得戶別狀態\n  getHouseStatus(house) {\n    if (!house.CHouseId) {\n      return 'disabled';\n    }\n    // 檢查是否有設定開放時段\n    if (!house.CChangeStartDate || !house.CChangeEndDate || house.CChangeStartDate === '' || house.CChangeEndDate === '') {\n      return 'not-set';\n    }\n    try {\n      // 處理日期字串，支援多種格式\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      // 解析開始日期\n      let startDate;\n      if (house.CChangeStartDate.includes('T')) {\n        startDate = new Date(house.CChangeStartDate);\n      } else {\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\n      }\n      // 解析結束日期\n      let endDate;\n      if (house.CChangeEndDate.includes('T')) {\n        endDate = new Date(house.CChangeEndDate);\n      } else {\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\n      }\n      // 檢查日期有效性\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\n        console.warn('Invalid date format:', {\n          start: house.CChangeStartDate,\n          end: house.CChangeEndDate,\n          houseId: house.CHouseId\n        });\n        return 'not-set';\n      }\n      // 轉換為日期比較（不含時間）\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n      // 判斷狀態\n      if (today < startDateOnly) {\n        return 'pending'; // 待開放\n      } else if (today >= startDateOnly && today <= endDateOnly) {\n        return 'active'; // 進行中\n      } else {\n        return 'expired'; // 已過期\n      }\n    } catch (error) {\n      console.error('Error parsing dates:', error, house);\n      return 'not-set';\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  // 修改：開啟批次設定對話框\n  openBatchSetting(building) {\n    this.selectedBuildingForBatch = building || null;\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\n    const hasSelectedHouses = this.selectedHouses.length > 0;\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: !hasSelectedHouses && !building,\n      selectedBuildings: building ? [building.name] : [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    // 重置選擇狀態\n    if (building) {\n      building.floors.forEach(floor => {\n        floor.selected = false;\n        floor.houses.forEach(house => house.selected = false);\n      });\n    }\n    // 開啟對話框\n    this.dialogService.open(this.batchSettingDialog);\n  }\n  // 新增：樓層選擇變更處理\n  onFloorSelectionChange(floor) {\n    if (floor.selected) {\n      floor.houses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = true;\n        }\n      });\n    } else {\n      floor.houses.forEach(house => house.selected = false);\n    }\n  }\n  // 修改：批次提交\n  onBatchSubmit(ref) {\n    // 驗證批次設定\n    this.valid.clear();\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 收集要更新的房屋\n    const housesToUpdate = [];\n    if (this.batchSettings.applyToAll) {\n      // 全部戶別\n      this.flattenedHouses.forEach(house => {\n        if (house.CHouseId) {\n          housesToUpdate.push({\n            CHouseId: house.CHouseId,\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n          });\n        }\n      });\n    } else {\n      // 使用已選擇的戶別\n      if (this.selectedHouses.length > 0) {\n        this.selectedHouses.forEach(house => {\n          if (house.CHouseId) {\n            housesToUpdate.push({\n              CHouseId: house.CHouseId,\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n            });\n          }\n        });\n      } else if (this.selectedBuildingForBatch) {\n        // 如果沒有選擇的戶別，使用舊的邏輯\n        this.selectedBuildingForBatch.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.selected && house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      }\n    }\n    if (housesToUpdate.length === 0) {\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\n      return;\n    }\n    // 調用API進行批次更新\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: housesToUpdate\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n        // 清除選擇狀態\n        this.selectedHouses.forEach(house => house.selected = false);\n        this.selectedHouses = [];\n        this.selectAll = false;\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  // 新增：取得狀態樣式類別\n  getStatusClass(house) {\n    const status = this.getHouseStatus(house);\n    return `status-${status}`;\n  }\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n  openHouseDialog(house) {\n    if (house.CHouseId) {\n      // 使用現有的openModel方法\n      this.openModel(this.dialog, house);\n    }\n  }\n  // 新增：建立扁平化房屋資料\n  buildFlattenedHouses(data) {\n    this.flattenedHouses = [];\n    data.forEach(household => {\n      const houseType = household.CHouseHold || '';\n      household.CHouses?.forEach(house => {\n        this.flattenedHouses.push({\n          CHouseHold: houseType,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId || 0,\n          CFloor: house.CFloor || 0,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 初始化篩選和分頁\n    this.onSearch();\n    // 調試：輸出狀態統計\n    this.debugStatusCounts();\n  }\n  // 調試：輸出狀態統計\n  debugStatusCounts() {\n    const statusCounts = {\n      active: 0,\n      pending: 0,\n      expired: 0,\n      'not-set': 0,\n      disabled: 0\n    };\n    this.flattenedHouses.forEach(house => {\n      const status = this.getHouseStatus(house);\n      if (statusCounts.hasOwnProperty(status)) {\n        statusCounts[status]++;\n      }\n    });\n    console.log('狀態統計:', statusCounts);\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\n  }\n  // 修改：搜尋和篩選\n  onSearch() {\n    // 記錄篩選前的已選擇項目\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\n    this.filteredHouses = this.flattenedHouses.filter(house => {\n      // 關鍵字搜尋\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n          return false;\n        }\n      }\n      // 棟別篩選\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n        return false;\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        if (!this.matchesStatusFilter(house)) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        if (house.CFloor !== floorNumber) {\n          return false;\n        }\n      }\n      return true;\n    });\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\n    this.selectedHouses = this.filteredHouses.filter(house => previouslySelectedIds.includes(house.CHouseId));\n    // 更新扁平化資料中的選擇狀態\n    this.flattenedHouses.forEach(house => {\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\n    });\n    // 更新全選狀態\n    this.updateSelectAllState();\n    // 重新計算分頁\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：更新全選狀態\n  updateSelectAllState() {\n    if (this.paginatedHouses.length === 0) {\n      this.selectAll = false;\n    } else {\n      this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n    }\n  }\n  // 修改：更新分頁\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n    // 更新全選狀態\n    this.updateSelectAllState();\n  }\n  // 新增：頁面大小變更\n  onPageSizeChange() {\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：跳轉頁面\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.updatePagination();\n    }\n  }\n  // 新增：取得可見頁碼\n  getVisiblePages() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 新增：全選/取消全選\n  onSelectAllChange() {\n    this.paginatedHouses.forEach(house => {\n      if (house.CHouseId) {\n        house.selected = this.selectAll;\n      }\n    });\n    this.updateSelectedHouses();\n  }\n  // 新增：單一選擇變更\n  onHouseSelectionChange() {\n    this.updateSelectedHouses();\n    this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n  }\n  // 新增：更新已選擇房屋列表\n  updateSelectedHouses() {\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n  }\n  // 新增：排序\n  sort(field) {\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    this.filteredHouses.sort((a, b) => {\n      let aValue = a[field];\n      let bValue = b[field];\n      // 處理日期排序\n      if (field.includes('Date')) {\n        aValue = aValue ? new Date(aValue).getTime() : 0;\n        bValue = bValue ? new Date(bValue).getTime() : 0;\n      }\n      // 處理數字排序\n      if (field === 'CFloor') {\n        aValue = Number(aValue) || 0;\n        bValue = Number(bValue) || 0;\n      }\n      // 處理字串排序\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (aValue < bValue) {\n        return this.sortDirection === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return this.sortDirection === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    this.updatePagination();\n  }\n  // 新增：TrackBy函數\n  trackByHouseId(_index, house) {\n    return house.CHouseId;\n  }\n  // 新增：取得狀態文字\n  getStatusText(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return '進行中';\n      case 'pending':\n        return '待開放';\n      case 'expired':\n        return '已過期';\n      case 'not-set':\n        return '未設定';\n      case 'disabled':\n        return '已停用';\n      default:\n        return '未知';\n    }\n  }\n  // 新增：匯出資料\n  exportData() {\n    // 實現匯出功能\n    const csvContent = this.generateCSV();\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  // 新增：產生CSV內容\n  generateCSV() {\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\n    const rows = this.filteredHouses.map(house => [house.CHouseHold, house.CBuildingName, `${house.CFloor}F`, house.CChangeStartDate || '未設定', house.CChangeEndDate || '未設定', this.getStatusText(house)]);\n    const csvContent = [headers, ...rows].map(row => row.map(cell => `\"${cell}\"`).join(',')).join('\\n');\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\n  }\n};\n__decorate([ViewChild('batchSettingDialog')], SettingTimePeriodComponent.prototype, \"batchSettingDialog\", void 0);\n__decorate([ViewChild('dialog')], SettingTimePeriodComponent.prototype, \"dialog\", void 0);\nSettingTimePeriodComponent = __decorate([Component({\n  selector: 'ngx-setting-time-period',\n  templateUrl: './setting-time-period.component.html',\n  styleUrls: ['./setting-time-period.component.scss'],\n  standalone: true,\n  providers: [],\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule, SettingTimeStatusPipe]\n})], SettingTimePeriodComponent);\nexport { SettingTimePeriodComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "SettingTimeStatusPipe", "moment", "SharedModule", "BaseComponent", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "buildingGroups", "buildingOptions", "selectedBuilding", "availableFloors", "filterOptions", "searchKeyword", "statusFilter", "floorFilter", "buildingFilter", "batchSettings", "startDate", "endDate", "applyToAll", "selectedBuildings", "selectedFloors", "selectedHouses", "selectedBuildingForBatch", "flattenedHouses", "filteredHouses", "paginatedHouses", "selectAll", "loading", "currentPage", "pageSize", "totalPages", "sortField", "sortDirection", "Math", "jumpToPage", "selectedHouseChangeDate", "CChangeStartDate", "CChangeEndDate", "CFloor", "undefined", "CHouseHold", "CHouseId", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "searchQuery", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "hasActiveFilters", "getActiveFiltersCount", "count", "resetFilters", "clearAllFilters", "onSearch", "setQuickFilter", "status", "clearSelection", "for<PERSON>ach", "house", "selected", "getStatusIcon", "getHouseStatus", "jumpToPageAction", "goToPage", "openModel", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "onSubmit", "validation", "errorMessages", "length", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "CBuildCaseName", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "household", "CHouses", "floor", "push", "CBuildingName", "floors", "sort", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "onBuildCaseChange", "resetAllStates", "houseChangeDates", "convertedHouseArray", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "buildBuildingGroups", "buildFlattenedHouses", "data", "buildingMap", "Map", "houseType", "buildingName", "has", "set", "floorMap", "get", "floorNumber", "houses", "localeCompare", "name", "bg", "updateAvailableFloors", "building", "onBuildingChange", "getFilteredBuildings", "filter", "keyword", "toLowerCase", "hasMatchingHouse", "some", "includes", "hasMatchingStatus", "matchesStatusFilter", "parseInt", "hasMatchingFloor", "filteredBuilding", "hasValidHouses", "filteredFloor", "now", "today", "getFullYear", "getMonth", "getDate", "isNaN", "getTime", "console", "warn", "start", "end", "houseId", "startDateOnly", "endDateOnly", "error", "onClose", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "openBatchSetting", "hasSelectedHouses", "batchSettingDialog", "onFloorSelectionChange", "onBatchSubmit", "housesToUpdate", "getStatusClass", "openHouseDialog", "dialog", "debugStatusCounts", "statusCounts", "active", "pending", "expired", "disabled", "hasOwnProperty", "log", "toISOString", "split", "previouslySelectedIds", "updateSelectAllState", "updatePagination", "every", "ceil", "startIndex", "endIndex", "slice", "onPageSizeChange", "page", "getVisiblePages", "pages", "maxVisible", "max", "min", "i", "onSelectAllChange", "updateSelectedHouses", "onHouseSelectionChange", "field", "aValue", "bValue", "Number", "trackByHouseId", "_index", "getStatusText", "exportData", "csv<PERSON><PERSON>nt", "generateCSV", "blob", "Blob", "type", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "rows", "row", "cell", "join", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "providers", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\n\r\n\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string; // 戶型\r\n  CBuildingName: string; // 棟別\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n\r\n  @ViewChild('batchSettingDialog') batchSettingDialog!: TemplateRef<any>;\r\n  @ViewChild('dialog') dialog!: TemplateRef<any>;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  // 新增的屬性\r\n  buildingGroups: BuildingGroup[] = [];\r\n  buildingOptions: string[] = [];\r\n  selectedBuilding: string = '';\r\n  availableFloors: number[] = [];\r\n\r\n  // 篩選和搜尋\r\n  filterOptions: FilterOptions = {\r\n    searchKeyword: '',\r\n    statusFilter: '',\r\n    floorFilter: '',\r\n    buildingFilter: ''\r\n  };\r\n\r\n  // 批次設定\r\n  batchSettings: BatchSettings = {\r\n    startDate: null,\r\n    endDate: null,\r\n    applyToAll: true,\r\n    selectedBuildings: [],\r\n    selectedFloors: [],\r\n    selectedHouses: []\r\n  };\r\n\r\n  selectedBuildingForBatch: BuildingGroup | null = null;\r\n\r\n  // 表格視圖相關屬性\r\n  flattenedHouses: ConvertedHouse[] = [];\r\n  filteredHouses: ConvertedHouse[] = [];\r\n  paginatedHouses: ConvertedHouse[] = [];\r\n  selectedHouses: ConvertedHouse[] = [];\r\n  selectAll: boolean = false;\r\n  loading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  override pageSize: number = 50;\r\n  totalPages: number = 1;\r\n\r\n  // 排序相關\r\n  sortField: string = '';\r\n  sortDirection: 'asc' | 'desc' = 'asc';\r\n\r\n  // 數學函數引用\r\n  Math = Math;\r\n\r\n  // 新增的UI控制屬性\r\n  jumpToPage: number = 1;\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  // 新增的UI控制方法\r\n  hasActiveFilters(): boolean {\r\n    return !!(this.filterOptions.searchKeyword ||\r\n      this.filterOptions.statusFilter ||\r\n      this.filterOptions.floorFilter);\r\n  }\r\n\r\n  getActiveFiltersCount(): number {\r\n    let count = 0;\r\n    if (this.filterOptions.searchKeyword) count++;\r\n    if (this.filterOptions.statusFilter) count++;\r\n    if (this.filterOptions.floorFilter) count++;\r\n    return count;\r\n  }\r\n\r\n  resetFilters(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    };\r\n    this.selectedBuilding = '';\r\n    this.clearAllFilters();\r\n  }\r\n\r\n  clearAllFilters(): void {\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n    this.onSearch();\r\n  }\r\n\r\n  setQuickFilter(status: string): void {\r\n    if (this.filterOptions.statusFilter === status) {\r\n      this.filterOptions.statusFilter = '';\r\n    } else {\r\n      this.filterOptions.statusFilter = status;\r\n    }\r\n    this.onSearch();\r\n  }\r\n\r\n  clearSelection(): void {\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n    this.flattenedHouses.forEach(house => house.selected = false);\r\n  }\r\n\r\n  getStatusIcon(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    switch (status) {\r\n      case 'active': return 'fas fa-play-circle';\r\n      case 'pending': return 'fas fa-clock';\r\n      case 'expired': return 'fas fa-times-circle';\r\n      case 'not-set': return 'fas fa-exclamation-triangle';\r\n      case 'disabled': return 'fas fa-ban';\r\n      default: return 'fas fa-exclamation-triangle';\r\n    }\r\n  }\r\n\r\n  jumpToPageAction(): void {\r\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\r\n      this.goToPage(this.jumpToPage);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CBuildingName: '未分類',\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：建案變更處理\r\n  onBuildCaseChange() {\r\n    // 重置所有相關狀態\r\n    this.resetAllStates();\r\n    // 執行查詢\r\n    this.getHouseChangeDate();\r\n  }\r\n\r\n  // 新增：重置所有狀態\r\n  resetAllStates() {\r\n    // 重置數據\r\n    this.houseChangeDates = [];\r\n    this.convertedHouseArray = [];\r\n    this.buildingGroups = [];\r\n    this.flattenedHouses = [];\r\n    this.filteredHouses = [];\r\n    this.paginatedHouses = [];\r\n    this.selectedHouses = [];\r\n\r\n    // 重置篩選條件\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    this.selectAll = false;\r\n    this.selectedBuilding = '';\r\n\r\n    // 重置分頁\r\n    this.currentPage = 1;\r\n    this.totalPages = 1;\r\n\r\n    // 重置可用選項\r\n    this.buildingOptions = [];\r\n    this.availableFloors = [];\r\n\r\n    // 重置排序\r\n    this.sortField = '';\r\n    this.sortDirection = 'asc';\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    // 如果沒有選擇建案，直接返回\r\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      this.loading = false;\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n          // 新增：建立棟別分組資料\r\n          this.buildBuildingGroups(res.Entries)\r\n          // 新增：建立扁平化資料\r\n          this.buildFlattenedHouses(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\r\n  buildBuildingGroups(data: GetHouseChangeDateRes[]) {\r\n    const buildingMap = new Map<string, Map<number, ConvertedHouse[]>>();\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || ''; // 戶型\r\n\r\n      household.CHouses?.forEach(house => {\r\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\r\n        const floor = house.CFloor || 0;\r\n\r\n        if (!buildingMap.has(buildingName)) {\r\n          buildingMap.set(buildingName, new Map<number, ConvertedHouse[]>());\r\n        }\r\n\r\n        const floorMap = buildingMap.get(buildingName)!;\r\n\r\n        if (!floorMap.has(floor)) {\r\n          floorMap.set(floor, []);\r\n        }\r\n\r\n        floorMap.get(floor)!.push({\r\n          CHouseHold: houseType, // 戶型\r\n          CBuildingName: buildingName, // 棟別\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: floor,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 轉換為BuildingGroup格式\r\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\r\n      const floors: FloorGroup[] = Array.from(floorMap.entries())\r\n        .sort(([a], [b]) => b - a) // 樓層由高到低排序\r\n        .map(([floorNumber, houses]) => ({\r\n          floorNumber,\r\n          houses: houses.sort((a, b) => {\r\n            // 排序邏輯：先按戶型排序，再按樓層排序\r\n            if (a.CHouseHold !== b.CHouseHold) {\r\n              return a.CHouseHold.localeCompare(b.CHouseHold);\r\n            }\r\n            return a.CFloor - b.CFloor;\r\n          }),\r\n          selected: false\r\n        }));\r\n\r\n      return {\r\n        name: buildingName,\r\n        floors,\r\n        selected: false\r\n      };\r\n    }).sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    // 更新棟別選項和可用樓層\r\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\r\n    this.updateAvailableFloors();\r\n  }\r\n\r\n  // 新增：更新可用樓層\r\n  updateAvailableFloors() {\r\n    const floorsSet = new Set<number>();\r\n\r\n    this.buildingGroups.forEach(building => {\r\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\r\n        building.floors.forEach(floor => {\r\n          floorsSet.add(floor.floorNumber);\r\n        });\r\n      }\r\n    });\r\n\r\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\r\n  }\r\n\r\n  // 修改：棟別選擇變更處理\r\n  onBuildingChange() {\r\n    // 重置選擇狀態\r\n    this.selectedHouses.forEach(house => house.selected = false);\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n\r\n    // 重置分頁到第一頁\r\n    this.currentPage = 1;\r\n\r\n    // 重置樓層篩選\r\n    this.filterOptions.floorFilter = '';\r\n\r\n    // 更新可用樓層\r\n    this.updateAvailableFloors();\r\n\r\n    // 設定棟別篩選並執行搜尋\r\n    this.filterOptions.buildingFilter = this.selectedBuilding;\r\n    this.onSearch();\r\n  }\r\n\r\n\r\n\r\n  // 新增：取得過濾後的棟別資料\r\n  getFilteredBuildings(): BuildingGroup[] {\r\n    return this.buildingGroups.filter(building => {\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 關鍵字搜尋 (搜尋戶型)\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        const hasMatchingHouse = building.floors.some(floor =>\r\n          floor.houses.some(house =>\r\n            house.CHouseHold.toLowerCase().includes(keyword) ||\r\n            house.CBuildingName.toLowerCase().includes(keyword)\r\n          )\r\n        );\r\n        if (!hasMatchingHouse) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        const hasMatchingStatus = building.floors.some(floor =>\r\n          floor.houses.some(house => this.matchesStatusFilter(house))\r\n        );\r\n        if (!hasMatchingStatus) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        const hasMatchingFloor = building.floors.some(floor =>\r\n          floor.floorNumber === floorNumber\r\n        );\r\n        if (!hasMatchingFloor) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }).map(building => {\r\n      // 對每個棟別，也要篩選其樓層和戶別\r\n      const filteredBuilding = { ...building };\r\n      filteredBuilding.floors = building.floors.filter(floor => {\r\n        // 樓層篩選\r\n        if (this.filterOptions.floorFilter) {\r\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n          if (floor.floorNumber !== floorNumber) {\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 檢查該樓層是否有符合條件的戶別\r\n        const hasValidHouses = floor.houses.some(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return hasValidHouses;\r\n      }).map(floor => {\r\n        // 篩選戶別\r\n        const filteredFloor = { ...floor };\r\n        filteredFloor.houses = floor.houses.filter(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return filteredFloor;\r\n      });\r\n\r\n      return filteredBuilding;\r\n    });\r\n  }\r\n\r\n  // 修復：檢查戶別是否符合狀態篩選\r\n  private matchesStatusFilter(house: ConvertedHouse): boolean {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (this.filterOptions.statusFilter) {\r\n      case 'active':\r\n        return status === 'active';\r\n      case 'pending':\r\n        return status === 'pending';\r\n      case 'expired':\r\n        return status === 'expired';\r\n      case 'not-set':\r\n        return status === 'not-set';\r\n      case 'disabled':\r\n        return status === 'disabled';\r\n      default:\r\n        return true; // 全部狀態\r\n    }\r\n  }\r\n\r\n  // 修復：取得戶別狀態\r\n  private getHouseStatus(house: ConvertedHouse): string {\r\n    if (!house.CHouseId) {\r\n      return 'disabled';\r\n    }\r\n\r\n    // 檢查是否有設定開放時段\r\n    if (!house.CChangeStartDate || !house.CChangeEndDate ||\r\n      house.CChangeStartDate === '' || house.CChangeEndDate === '') {\r\n      return 'not-set';\r\n    }\r\n\r\n    try {\r\n      // 處理日期字串，支援多種格式\r\n      const now = new Date();\r\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n\r\n      // 解析開始日期\r\n      let startDate: Date;\r\n      if (house.CChangeStartDate.includes('T')) {\r\n        startDate = new Date(house.CChangeStartDate);\r\n      } else {\r\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\r\n      }\r\n\r\n      // 解析結束日期\r\n      let endDate: Date;\r\n      if (house.CChangeEndDate.includes('T')) {\r\n        endDate = new Date(house.CChangeEndDate);\r\n      } else {\r\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\r\n      }\r\n\r\n      // 檢查日期有效性\r\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\r\n        console.warn('Invalid date format:', {\r\n          start: house.CChangeStartDate,\r\n          end: house.CChangeEndDate,\r\n          houseId: house.CHouseId\r\n        });\r\n        return 'not-set';\r\n      }\r\n\r\n      // 轉換為日期比較（不含時間）\r\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\r\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\r\n\r\n      // 判斷狀態\r\n      if (today < startDateOnly) {\r\n        return 'pending'; // 待開放\r\n      } else if (today >= startDateOnly && today <= endDateOnly) {\r\n        return 'active'; // 進行中\r\n      } else {\r\n        return 'expired'; // 已過期\r\n      }\r\n    } catch (error) {\r\n      console.error('Error parsing dates:', error, house);\r\n      return 'not-set';\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n\r\n  // 修改：開啟批次設定對話框\r\n  openBatchSetting(building?: BuildingGroup) {\r\n    this.selectedBuildingForBatch = building || null;\r\n\r\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\r\n    const hasSelectedHouses = this.selectedHouses.length > 0;\r\n\r\n    this.batchSettings = {\r\n      startDate: null,\r\n      endDate: null,\r\n      applyToAll: !hasSelectedHouses && !building,\r\n      selectedBuildings: building ? [building.name] : [],\r\n      selectedFloors: [],\r\n      selectedHouses: []\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    if (building) {\r\n      building.floors.forEach(floor => {\r\n        floor.selected = false;\r\n        floor.houses.forEach(house => house.selected = false);\r\n      });\r\n    }\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(this.batchSettingDialog);\r\n  }\r\n\r\n  // 新增：樓層選擇變更處理\r\n  onFloorSelectionChange(floor: FloorGroup) {\r\n    if (floor.selected) {\r\n      floor.houses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          house.selected = true;\r\n        }\r\n      });\r\n    } else {\r\n      floor.houses.forEach(house => house.selected = false);\r\n    }\r\n  }\r\n\r\n  // 修改：批次提交\r\n  onBatchSubmit(ref: any) {\r\n    // 驗證批次設定\r\n    this.valid.clear();\r\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\r\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 收集要更新的房屋\r\n    const housesToUpdate: any[] = [];\r\n\r\n    if (this.batchSettings.applyToAll) {\r\n      // 全部戶別\r\n      this.flattenedHouses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          housesToUpdate.push({\r\n            CHouseId: house.CHouseId,\r\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // 使用已選擇的戶別\r\n      if (this.selectedHouses.length > 0) {\r\n        this.selectedHouses.forEach(house => {\r\n          if (house.CHouseId) {\r\n            housesToUpdate.push({\r\n              CHouseId: house.CHouseId,\r\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n            });\r\n          }\r\n        });\r\n      } else if (this.selectedBuildingForBatch) {\r\n        // 如果沒有選擇的戶別，使用舊的邏輯\r\n        this.selectedBuildingForBatch.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.selected && house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n\r\n    if (housesToUpdate.length === 0) {\r\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\r\n      return;\r\n    }\r\n\r\n    // 調用API進行批次更新\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: housesToUpdate\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\r\n        // 清除選擇狀態\r\n        this.selectedHouses.forEach(house => house.selected = false);\r\n        this.selectedHouses = [];\r\n        this.selectAll = false;\r\n        this.getHouseChangeDate();\r\n        ref.close();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：取得狀態樣式類別\r\n  getStatusClass(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    return `status-${status}`;\r\n  }\r\n\r\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\r\n  openHouseDialog(house: ConvertedHouse) {\r\n    if (house.CHouseId) {\r\n      // 使用現有的openModel方法\r\n      this.openModel(this.dialog, house);\r\n    }\r\n  }\r\n\r\n  // 新增：建立扁平化房屋資料\r\n  buildFlattenedHouses(data: GetHouseChangeDateRes[]) {\r\n    this.flattenedHouses = [];\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || '';\r\n\r\n      household.CHouses?.forEach(house => {\r\n        this.flattenedHouses.push({\r\n          CHouseHold: houseType,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: house.CFloor || 0,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 初始化篩選和分頁\r\n    this.onSearch();\r\n\r\n    // 調試：輸出狀態統計\r\n    this.debugStatusCounts();\r\n  }\r\n\r\n  // 調試：輸出狀態統計\r\n  private debugStatusCounts() {\r\n    const statusCounts = {\r\n      active: 0,\r\n      pending: 0,\r\n      expired: 0,\r\n      'not-set': 0,\r\n      disabled: 0\r\n    };\r\n\r\n    this.flattenedHouses.forEach(house => {\r\n      const status = this.getHouseStatus(house);\r\n      if (statusCounts.hasOwnProperty(status)) {\r\n        statusCounts[status as keyof typeof statusCounts]++;\r\n      }\r\n    });\r\n\r\n    console.log('狀態統計:', statusCounts);\r\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\r\n  }\r\n\r\n\r\n\r\n  // 修改：搜尋和篩選\r\n  onSearch() {\r\n    // 記錄篩選前的已選擇項目\r\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\r\n\r\n    this.filteredHouses = this.flattenedHouses.filter(house => {\r\n      // 關鍵字搜尋\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n          !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        if (!this.matchesStatusFilter(house)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        if (house.CFloor !== floorNumber) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    });\r\n\r\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\r\n    this.selectedHouses = this.filteredHouses.filter(house =>\r\n      previouslySelectedIds.includes(house.CHouseId)\r\n    );\r\n\r\n    // 更新扁平化資料中的選擇狀態\r\n    this.flattenedHouses.forEach(house => {\r\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\r\n    });\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n\r\n    // 重新計算分頁\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：更新全選狀態\r\n  private updateSelectAllState() {\r\n    if (this.paginatedHouses.length === 0) {\r\n      this.selectAll = false;\r\n    } else {\r\n      this.selectAll = this.paginatedHouses.every(house =>\r\n        !house.CHouseId || house.selected\r\n      );\r\n    }\r\n  }\r\n\r\n  // 修改：更新分頁\r\n  updatePagination() {\r\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\r\n    const startIndex = (this.currentPage - 1) * this.pageSize;\r\n    const endIndex = startIndex + this.pageSize;\r\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 新增：頁面大小變更\r\n  onPageSizeChange() {\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：跳轉頁面\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  // 新增：取得可見頁碼\r\n  getVisiblePages(): number[] {\r\n    const pages: number[] = [];\r\n    const maxVisible = 5;\r\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\r\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\r\n\r\n    if (end - start + 1 < maxVisible) {\r\n      start = Math.max(1, end - maxVisible + 1);\r\n    }\r\n\r\n    for (let i = start; i <= end; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 新增：全選/取消全選\r\n  onSelectAllChange() {\r\n    this.paginatedHouses.forEach(house => {\r\n      if (house.CHouseId) {\r\n        house.selected = this.selectAll;\r\n      }\r\n    });\r\n    this.updateSelectedHouses();\r\n  }\r\n\r\n  // 新增：單一選擇變更\r\n  onHouseSelectionChange() {\r\n    this.updateSelectedHouses();\r\n    this.selectAll = this.paginatedHouses.every(house =>\r\n      !house.CHouseId || house.selected\r\n    );\r\n  }\r\n\r\n  // 新增：更新已選擇房屋列表\r\n  updateSelectedHouses() {\r\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\r\n  }\r\n\r\n  // 新增：排序\r\n  sort(field: string) {\r\n    if (this.sortField === field) {\r\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortDirection = 'asc';\r\n    }\r\n\r\n    this.filteredHouses.sort((a, b) => {\r\n      let aValue = (a as any)[field];\r\n      let bValue = (b as any)[field];\r\n\r\n      // 處理日期排序\r\n      if (field.includes('Date')) {\r\n        aValue = aValue ? new Date(aValue).getTime() : 0;\r\n        bValue = bValue ? new Date(bValue).getTime() : 0;\r\n      }\r\n\r\n      // 處理數字排序\r\n      if (field === 'CFloor') {\r\n        aValue = Number(aValue) || 0;\r\n        bValue = Number(bValue) || 0;\r\n      }\r\n\r\n      // 處理字串排序\r\n      if (typeof aValue === 'string') {\r\n        aValue = aValue.toLowerCase();\r\n        bValue = bValue.toLowerCase();\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return this.sortDirection === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return this.sortDirection === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：TrackBy函數\r\n  trackByHouseId(_index: number, house: ConvertedHouse): number {\r\n    return house.CHouseId;\r\n  }\r\n\r\n  // 新增：取得狀態文字\r\n  getStatusText(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (status) {\r\n      case 'active':\r\n        return '進行中';\r\n      case 'pending':\r\n        return '待開放';\r\n      case 'expired':\r\n        return '已過期';\r\n      case 'not-set':\r\n        return '未設定';\r\n      case 'disabled':\r\n        return '已停用';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  // 新增：匯出資料\r\n  exportData() {\r\n    // 實現匯出功能\r\n    const csvContent = this.generateCSV();\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  // 新增：產生CSV內容\r\n  private generateCSV(): string {\r\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\r\n    const rows = this.filteredHouses.map(house => [\r\n      house.CHouseHold,\r\n      house.CBuildingName,\r\n      `${house.CFloor}F`,\r\n      house.CChangeStartDate || '未設定',\r\n      house.CChangeEndDate || '未設定',\r\n      this.getStatusText(house)\r\n    ]);\r\n\r\n    const csvContent = [headers, ...rows]\r\n      .map(row => row.map(cell => `\"${cell}\"`).join(','))\r\n      .join('\\n');\r\n\r\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,SAAS,QAAqB,eAAe;AACzE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AAQvD,SAASC,qBAAqB,QAAQ,mCAAmC;AAEzE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AA+D5D,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA2B,SAAQD,aAAa;EAK3DE,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA6C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAUtD;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,eAAe,GAAa,EAAE;IAE9B;IACA,KAAAC,aAAa,GAAkB;MAC7BC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE;KACjB;IAED;IACA,KAAAC,aAAa,GAAkB;MAC7BC,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;KACjB;IAED,KAAAC,wBAAwB,GAAyB,IAAI;IAErD;IACA,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAC,cAAc,GAAqB,EAAE;IACrC,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAJ,cAAc,GAAqB,EAAE;IACrC,KAAAK,SAAS,GAAY,KAAK;IAC1B,KAAAC,OAAO,GAAY,KAAK;IAExB;IACA,KAAAC,WAAW,GAAW,CAAC;IACd,KAAAC,QAAQ,GAAW,EAAE;IAC9B,KAAAC,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAmB,KAAK;IAErC;IACA,KAAAC,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAC,UAAU,GAAW,CAAC;IArFpB,IAAI,CAACC,uBAAuB,GAAG;MAC7BC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBC,MAAM,EAAEC,SAAS;MACjBC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAEF;KACX;IAED,IAAI,CAACtC,aAAa,CAACyC,OAAO,EAAE,CAACC,IAAI,CAC/B1D,GAAG,CAAE2D,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAAC3C,eAAe,GAAG0C,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAwESC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAAChD,gBAAgB,CAAC,CAAC,CAAC;MAC/CiC,gBAAgB,EAAEG,SAAS;MAC3BF,cAAc,EAAEE;KACjB;IACD,IAAI,CAACa,gBAAgB,EAAE;EACzB;EAEA;EACAC,gBAAgBA,CAAA;IACd,OAAO,CAAC,EAAE,IAAI,CAAC3C,aAAa,CAACC,aAAa,IACxC,IAAI,CAACD,aAAa,CAACE,YAAY,IAC/B,IAAI,CAACF,aAAa,CAACG,WAAW,CAAC;EACnC;EAEAyC,qBAAqBA,CAAA;IACnB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAC7C,aAAa,CAACC,aAAa,EAAE4C,KAAK,EAAE;IAC7C,IAAI,IAAI,CAAC7C,aAAa,CAACE,YAAY,EAAE2C,KAAK,EAAE;IAC5C,IAAI,IAAI,CAAC7C,aAAa,CAACG,WAAW,EAAE0C,KAAK,EAAE;IAC3C,OAAOA,KAAK;EACd;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACP,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvDC,qBAAqB,EAAE,IAAI,CAAChD,gBAAgB,CAAC,CAAC,CAAC;MAC/CiC,gBAAgB,EAAEG,SAAS;MAC3BF,cAAc,EAAEE;KACjB;IACD,IAAI,CAAC/B,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACiD,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAAC/C,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE;KACjB;IACD,IAAI,CAAC4C,QAAQ,EAAE;EACjB;EAEAC,cAAcA,CAACC,MAAc;IAC3B,IAAI,IAAI,CAAClD,aAAa,CAACE,YAAY,KAAKgD,MAAM,EAAE;MAC9C,IAAI,CAAClD,aAAa,CAACE,YAAY,GAAG,EAAE;IACtC,CAAC,MAAM;MACL,IAAI,CAACF,aAAa,CAACE,YAAY,GAAGgD,MAAM;IAC1C;IACA,IAAI,CAACF,QAAQ,EAAE;EACjB;EAEAG,cAAcA,CAAA;IACZ,IAAI,CAACxC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACK,SAAS,GAAG,KAAK;IACtB,IAAI,CAACH,eAAe,CAACuC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,KAAK,CAAC;EAC/D;EAEAC,aAAaA,CAACF,KAAqB;IACjC,MAAMH,MAAM,GAAG,IAAI,CAACM,cAAc,CAACH,KAAK,CAAC;IACzC,QAAQH,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,oBAAoB;MAC1C,KAAK,SAAS;QAAE,OAAO,cAAc;MACrC,KAAK,SAAS;QAAE,OAAO,qBAAqB;MAC5C,KAAK,SAAS;QAAE,OAAO,6BAA6B;MACpD,KAAK,UAAU;QAAE,OAAO,YAAY;MACpC;QAAS,OAAO,6BAA6B;IAC/C;EACF;EAEAO,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACjC,UAAU,IAAI,IAAI,CAACA,UAAU,IAAI,CAAC,IAAI,IAAI,CAACA,UAAU,IAAI,IAAI,CAACJ,UAAU,EAAE;MACjF,IAAI,CAACsC,QAAQ,CAAC,IAAI,CAAClC,UAAU,CAAC;IAChC;EACF;EAIAmC,SAASA,CAACC,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAAC9B,QAAQ,EAAE;MACjB,IAAI,CAACN,uBAAuB,GAAG;QAC7B,GAAGoC,IAAI;QACPnC,gBAAgB,EAAEmC,IAAI,CAACnC,gBAAgB,GAAG,IAAIoC,IAAI,CAACD,IAAI,CAACnC,gBAAgB,CAAC,GAAGG,SAAS;QACrFF,cAAc,EAAEkC,IAAI,CAAClC,cAAc,GAAG,IAAImC,IAAI,CAACD,IAAI,CAAClC,cAAc,CAAC,GAAGE;OACvE;MACD,IAAI,CAAC5C,aAAa,CAAC8E,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOtF,MAAM,CAACsF,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAC,QAAQA,CAACP,GAAQ;IACf,IAAI,CAACQ,UAAU,EAAE;IACjB,IAAI,IAAI,CAACjF,KAAK,CAACkF,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACpF,OAAO,CAACqF,aAAa,CAAC,IAAI,CAACpF,KAAK,CAACkF,aAAa,CAAC;MACpD;IACF;IACA,MAAMG,KAAK,GAAG;MACZzC,QAAQ,EAAE,IAAI,CAACN,uBAAuB,CAACM,QAAQ;MAC/CL,gBAAgB,EAAE,IAAI,CAACsC,UAAU,CAAC,IAAI,CAACvC,uBAAuB,CAACC,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAACqC,UAAU,CAAC,IAAI,CAACvC,uBAAuB,CAACE,cAAc;KAC5E;IAED,IAAI,CAACvC,aAAa,CAACqF,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAACnC,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACyC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACzF,OAAO,CAAC0F,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBjB,GAAG,CAACkB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEApC,gBAAgBA,CAAA;IACd,IAAI,CAACrD,iBAAiB,CAAC0F,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAACzC,IAAI,CAC7E1D,GAAG,CAAC2D,GAAG,IAAG;MACR,MAAM8C,OAAO,GAAG9C,GAAG,CAAC+C,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAACV,MAAM,IAAIpC,GAAG,CAACyC,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChDC,cAAc,EAAED,KAAK,CAACC,cAAc;UACpCC,GAAG,EAAEF,KAAK,CAACE;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAAC9F,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAI+F,KAAK,GAAG,IAAI,CAACL,oBAAoB,CAACM,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAAC9F,eAAe,CAAC;UAC1F,IAAI,CAAC+C,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC0C,oBAAoB,CAACK,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAAChD,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC0C,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMQ,WAAW,GAAG,IAAI,CAACnD,WAAW,EAAEC,kBAAkB,EAAE8C,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACb,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAACxC,SAAS,EAAE;EACf;EAEAsD,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAACxC,OAAO,CAAC0C,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAAC3C,OAAO,CAAEC,KAAU,IAAI;QACvC,MAAM2C,KAAK,GAAG3C,KAAK,CAACzB,MAAM;QAC1B,IAAI,CAACiE,SAAS,CAACG,KAAK,CAAC,EAAE;UAAE;UACvBH,SAAS,CAACG,KAAK,CAAC,GAAG,EAAE;QACvB;QACAH,SAAS,CAACG,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBnE,UAAU,EAAEgE,SAAS,CAAChE,UAAU;UAChCoE,aAAa,EAAE7C,KAAK,CAAC6C,aAAa,IAAI,KAAK;UAC3CnE,QAAQ,EAAEsB,KAAK,CAACtB,QAAQ;UACxBH,MAAM,EAAEyB,KAAK,CAACzB,MAAM;UACpBF,gBAAgB,EAAE2B,KAAK,CAAC3B,gBAAgB;UACxCC,cAAc,EAAE0B,KAAK,CAAC1B;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACwE,MAAM,CAACC,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAACJ,MAAM,CAAChB,GAAG,CAAEa,KAAU,IAAI;MAChE,OAAO,IAAI,CAACQ,UAAU,CAACrB,GAAG,CAAEW,SAAc,IAAI;QAC5C,MAAMzC,KAAK,GAAGwC,SAAS,CAACG,KAAK,CAAC,CAACS,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAAC5E,UAAU,KAAKgE,SAAS,CAAC;QAC5F,OAAOzC,KAAK,IAAI;UACdvB,UAAU,EAAEgE,SAAS;UACrBI,aAAa,EAAE,KAAK;UACpBnE,QAAQ,EAAE,IAAI;UACdH,MAAM,EAAEoE,KAAK;UACbtE,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO4E,MAAM;EACf;EAEAI,sBAAsBA,CAACf,GAAU;IAC/B,MAAMgB,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5CjB,GAAG,CAACxC,OAAO,CAAC0C,SAAS,IAAG;MACtBgB,aAAa,CAACC,GAAG,CAACjB,SAAS,CAAChE,UAAU,CAAC;MACvCgE,SAAS,CAACC,OAAO,CAAC3C,OAAO,CAAEC,KAAU,IAAI;QACvCuD,SAAS,CAACG,GAAG,CAAC1D,KAAK,CAACzB,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACuE,MAAM,GAAGa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLX,MAAM,EAAEa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC3E,WAAW,CAACb,gBAAgB,IAAI,IAAI,CAACa,WAAW,CAACZ,cAAc,EAAE;MACxE,MAAMrB,SAAS,GAAG,IAAIwD,IAAI,CAAC,IAAI,CAACvB,WAAW,CAACb,gBAAgB,CAAC;MAC7D,MAAMnB,OAAO,GAAG,IAAIuD,IAAI,CAAC,IAAI,CAACvB,WAAW,CAACZ,cAAc,CAAC;MACzD,IAAIrB,SAAS,IAAIC,OAAO,IAAID,SAAS,GAAGC,OAAO,EAAE;QAC/C,IAAI,CAACrB,OAAO,CAACqF,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEA;EACA4C,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACC,cAAc,EAAE;IACrB;IACA,IAAI,CAACvC,kBAAkB,EAAE;EAC3B;EAEA;EACAuC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC1H,cAAc,GAAG,EAAE;IACxB,IAAI,CAACiB,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACJ,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAACX,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE;KACjB;IAED;IACA,IAAI,CAACY,SAAS,GAAG,KAAK;IACtB,IAAI,CAAClB,gBAAgB,GAAG,EAAE;IAE1B;IACA,IAAI,CAACoB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACE,UAAU,GAAG,CAAC;IAEnB;IACA,IAAI,CAACvB,eAAe,GAAG,EAAE;IACzB,IAAI,CAACE,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,CAACsB,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC5B;EAEAuD,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC,IAAI,CAACtC,WAAW,CAACC,kBAAkB,EAAE8C,GAAG,EAAE;MAC7C,IAAI,CAACrE,OAAO,GAAG,KAAK;MACpB;IACF;IAEA,IAAI,CAACA,OAAO,GAAG,IAAI;IACnB,IAAI,CAACiG,cAAc,EAAE;IACrB,IAAI,CAAC9H,aAAa,CAACmI,mCAAmC,CAAC;MACrD7C,IAAI,EAAE;QACJ8C,YAAY,EAAE,IAAI,CAACjF,WAAW,CAACC,kBAAkB,CAAC8C,GAAG;QACrD5D,gBAAgB,EAAE,IAAI,CAACa,WAAW,CAACb,gBAAgB,GAAG,IAAI,CAACsC,UAAU,CAAC,IAAI,CAACzB,WAAW,CAACb,gBAAgB,CAAC,GAAGG,SAAS;QACpHF,cAAc,EAAE,IAAI,CAACY,WAAW,CAACZ,cAAc,GAAG,IAAI,CAACqC,UAAU,CAAC,IAAI,CAACzB,WAAW,CAACZ,cAAc,CAAC,GAAGE;;KAExG,CAAC,CAACQ,SAAS,CAACH,GAAG,IAAG;MACjB,IAAI,CAACjB,OAAO,GAAG,KAAK;MACpB,IAAIiB,GAAG,CAAC+C,OAAO,IAAI/C,GAAG,CAACyC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC0C,gBAAgB,GAAGnF,GAAG,CAAC+C,OAAO,GAAG/C,GAAG,CAAC+C,OAAO,GAAG,EAAE;QACtD,IAAI/C,GAAG,CAAC+C,OAAO,EAAE;UACf,IAAI,CAACoC,gBAAgB,GAAG,CAAC,GAAGnF,GAAG,CAAC+C,OAAO,CAAC;UACxC,IAAI,CAAC0B,sBAAsB,CAACzE,GAAG,CAAC+C,OAAO,CAAC;UACxC,IAAI,CAACqC,mBAAmB,GAAG,IAAI,CAAC3B,8BAA8B,CAACzD,GAAG,CAAC+C,OAAO,CAAC;UAC3E;UACA,IAAI,CAACwC,mBAAmB,CAACvF,GAAG,CAAC+C,OAAO,CAAC;UACrC;UACA,IAAI,CAACyC,oBAAoB,CAACxF,GAAG,CAAC+C,OAAO,CAAC;QACxC;MACF;IACF,CAAC,CAAC;EACJ;EAEA;EACAwC,mBAAmBA,CAACE,IAA6B;IAC/C,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAyC;IAEpEF,IAAI,CAACvE,OAAO,CAAC0C,SAAS,IAAG;MACvB,MAAMgC,SAAS,GAAGhC,SAAS,CAAChE,UAAU,IAAI,EAAE,CAAC,CAAC;MAE9CgE,SAAS,CAACC,OAAO,EAAE3C,OAAO,CAACC,KAAK,IAAG;QACjC,MAAM0E,YAAY,GAAG1E,KAAK,CAAC6C,aAAa,IAAI,KAAK,CAAC,CAAC;QACnD,MAAMF,KAAK,GAAG3C,KAAK,CAACzB,MAAM,IAAI,CAAC;QAE/B,IAAI,CAACgG,WAAW,CAACI,GAAG,CAACD,YAAY,CAAC,EAAE;UAClCH,WAAW,CAACK,GAAG,CAACF,YAAY,EAAE,IAAIF,GAAG,EAA4B,CAAC;QACpE;QAEA,MAAMK,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACJ,YAAY,CAAE;QAE/C,IAAI,CAACG,QAAQ,CAACF,GAAG,CAAChC,KAAK,CAAC,EAAE;UACxBkC,QAAQ,CAACD,GAAG,CAACjC,KAAK,EAAE,EAAE,CAAC;QACzB;QAEAkC,QAAQ,CAACC,GAAG,CAACnC,KAAK,CAAE,CAACC,IAAI,CAAC;UACxBnE,UAAU,EAAEgG,SAAS;UAAE;UACvB5B,aAAa,EAAE6B,YAAY;UAAE;UAC7BhG,QAAQ,EAAEsB,KAAK,CAACtB,QAAQ,IAAI,CAAC;UAC7BH,MAAM,EAAEoE,KAAK;UACbtE,gBAAgB,EAAE2B,KAAK,CAAC3B,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAE0B,KAAK,CAAC1B,cAAc,IAAI,EAAE;UAC1C2B,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC1D,cAAc,GAAGoH,KAAK,CAACC,IAAI,CAACW,WAAW,CAAC5C,OAAO,EAAE,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC4C,YAAY,EAAEG,QAAQ,CAAC,KAAI;MACvF,MAAM/B,MAAM,GAAiBa,KAAK,CAACC,IAAI,CAACiB,QAAQ,CAAClD,OAAO,EAAE,CAAC,CACxDoB,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;MAAA,CAC1BlB,GAAG,CAAC,CAAC,CAACiD,WAAW,EAAEC,MAAM,CAAC,MAAM;QAC/BD,WAAW;QACXC,MAAM,EAAEA,MAAM,CAACjC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAC3B;UACA,IAAID,CAAC,CAACvE,UAAU,KAAKwE,CAAC,CAACxE,UAAU,EAAE;YACjC,OAAOuE,CAAC,CAACvE,UAAU,CAACwG,aAAa,CAAChC,CAAC,CAACxE,UAAU,CAAC;UACjD;UACA,OAAOuE,CAAC,CAACzE,MAAM,GAAG0E,CAAC,CAAC1E,MAAM;QAC5B,CAAC,CAAC;QACF0B,QAAQ,EAAE;OACX,CAAC,CAAC;MAEL,OAAO;QACLiF,IAAI,EAAER,YAAY;QAClB5B,MAAM;QACN7C,QAAQ,EAAE;OACX;IACH,CAAC,CAAC,CAAC8C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACkC,IAAI,CAACD,aAAa,CAAChC,CAAC,CAACiC,IAAI,CAAC,CAAC;IAE/C;IACA,IAAI,CAAC1I,eAAe,GAAG,IAAI,CAACD,cAAc,CAACuF,GAAG,CAACqD,EAAE,IAAIA,EAAE,CAACD,IAAI,CAAC;IAC7D,IAAI,CAACE,qBAAqB,EAAE;EAC9B;EAEA;EACAA,qBAAqBA,CAAA;IACnB,MAAM7B,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,IAAI,CAACjH,cAAc,CAACwD,OAAO,CAACsF,QAAQ,IAAG;MACrC,IAAI,CAAC,IAAI,CAAC5I,gBAAgB,IAAI4I,QAAQ,CAACH,IAAI,KAAK,IAAI,CAACzI,gBAAgB,EAAE;QACrE4I,QAAQ,CAACvC,MAAM,CAAC/C,OAAO,CAAC4C,KAAK,IAAG;UAC9BY,SAAS,CAACG,GAAG,CAACf,KAAK,CAACoC,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACrI,eAAe,GAAGiH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC,CAACR,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpE;EAEA;EACAsC,gBAAgBA,CAAA;IACd;IACA,IAAI,CAAChI,cAAc,CAACyC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,KAAK,CAAC;IAC5D,IAAI,CAAC3C,cAAc,GAAG,EAAE;IACxB,IAAI,CAACK,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAACE,WAAW,GAAG,CAAC;IAEpB;IACA,IAAI,CAAClB,aAAa,CAACG,WAAW,GAAG,EAAE;IAEnC;IACA,IAAI,CAACsI,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACzI,aAAa,CAACI,cAAc,GAAG,IAAI,CAACN,gBAAgB;IACzD,IAAI,CAACkD,QAAQ,EAAE;EACjB;EAIA;EACA4F,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAChJ,cAAc,CAACiJ,MAAM,CAACH,QAAQ,IAAG;MAC3C;MACA,IAAI,IAAI,CAAC5I,gBAAgB,IAAI4I,QAAQ,CAACH,IAAI,KAAK,IAAI,CAACzI,gBAAgB,EAAE;QACpE,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAACE,aAAa,CAACC,aAAa,EAAE;QACpC,MAAM6I,OAAO,GAAG,IAAI,CAAC9I,aAAa,CAACC,aAAa,CAAC8I,WAAW,EAAE;QAC9D,MAAMC,gBAAgB,GAAGN,QAAQ,CAACvC,MAAM,CAAC8C,IAAI,CAACjD,KAAK,IACjDA,KAAK,CAACqC,MAAM,CAACY,IAAI,CAAC5F,KAAK,IACrBA,KAAK,CAACvB,UAAU,CAACiH,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IAChDzF,KAAK,CAAC6C,aAAa,CAAC6C,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,CACpD,CACF;QACD,IAAI,CAACE,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAChJ,aAAa,CAACE,YAAY,EAAE;QACnC,MAAMiJ,iBAAiB,GAAGT,QAAQ,CAACvC,MAAM,CAAC8C,IAAI,CAACjD,KAAK,IAClDA,KAAK,CAACqC,MAAM,CAACY,IAAI,CAAC5F,KAAK,IAAI,IAAI,CAAC+F,mBAAmB,CAAC/F,KAAK,CAAC,CAAC,CAC5D;QACD,IAAI,CAAC8F,iBAAiB,EAAE;UACtB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACnJ,aAAa,CAACG,WAAW,EAAE;QAClC,MAAMiI,WAAW,GAAGiB,QAAQ,CAAC,IAAI,CAACrJ,aAAa,CAACG,WAAW,CAAC;QAC5D,MAAMmJ,gBAAgB,GAAGZ,QAAQ,CAACvC,MAAM,CAAC8C,IAAI,CAACjD,KAAK,IACjDA,KAAK,CAACoC,WAAW,KAAKA,WAAW,CAClC;QACD,IAAI,CAACkB,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CAACnE,GAAG,CAACuD,QAAQ,IAAG;MAChB;MACA,MAAMa,gBAAgB,GAAG;QAAE,GAAGb;MAAQ,CAAE;MACxCa,gBAAgB,CAACpD,MAAM,GAAGuC,QAAQ,CAACvC,MAAM,CAAC0C,MAAM,CAAC7C,KAAK,IAAG;QACvD;QACA,IAAI,IAAI,CAAChG,aAAa,CAACG,WAAW,EAAE;UAClC,MAAMiI,WAAW,GAAGiB,QAAQ,CAAC,IAAI,CAACrJ,aAAa,CAACG,WAAW,CAAC;UAC5D,IAAI6F,KAAK,CAACoC,WAAW,KAAKA,WAAW,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QAEA;QACA,MAAMoB,cAAc,GAAGxD,KAAK,CAACqC,MAAM,CAACY,IAAI,CAAC5F,KAAK,IAAG;UAC/C;UACA,IAAI,IAAI,CAACrD,aAAa,CAACC,aAAa,EAAE;YACpC,MAAM6I,OAAO,GAAG,IAAI,CAAC9I,aAAa,CAACC,aAAa,CAAC8I,WAAW,EAAE;YAC9D,IAAI,CAAC1F,KAAK,CAACvB,UAAU,CAACiH,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzF,KAAK,CAAC6C,aAAa,CAAC6C,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAC9I,aAAa,CAACE,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACkJ,mBAAmB,CAAC/F,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOmG,cAAc;MACvB,CAAC,CAAC,CAACrE,GAAG,CAACa,KAAK,IAAG;QACb;QACA,MAAMyD,aAAa,GAAG;UAAE,GAAGzD;QAAK,CAAE;QAClCyD,aAAa,CAACpB,MAAM,GAAGrC,KAAK,CAACqC,MAAM,CAACQ,MAAM,CAACxF,KAAK,IAAG;UACjD;UACA,IAAI,IAAI,CAACrD,aAAa,CAACC,aAAa,EAAE;YACpC,MAAM6I,OAAO,GAAG,IAAI,CAAC9I,aAAa,CAACC,aAAa,CAAC8I,WAAW,EAAE;YAC9D,IAAI,CAAC1F,KAAK,CAACvB,UAAU,CAACiH,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzF,KAAK,CAAC6C,aAAa,CAAC6C,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAC9I,aAAa,CAACE,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACkJ,mBAAmB,CAAC/F,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOoG,aAAa;MACtB,CAAC,CAAC;MAEF,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ;EAEA;EACQH,mBAAmBA,CAAC/F,KAAqB;IAC/C,MAAMH,MAAM,GAAG,IAAI,CAACM,cAAc,CAACH,KAAK,CAAC;IAEzC,QAAQ,IAAI,CAACrD,aAAa,CAACE,YAAY;MACrC,KAAK,QAAQ;QACX,OAAOgD,MAAM,KAAK,QAAQ;MAC5B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,UAAU;MAC9B;QACE,OAAO,IAAI;MAAE;IACjB;EACF;EAEA;EACQM,cAAcA,CAACH,KAAqB;IAC1C,IAAI,CAACA,KAAK,CAACtB,QAAQ,EAAE;MACnB,OAAO,UAAU;IACnB;IAEA;IACA,IAAI,CAACsB,KAAK,CAAC3B,gBAAgB,IAAI,CAAC2B,KAAK,CAAC1B,cAAc,IAClD0B,KAAK,CAAC3B,gBAAgB,KAAK,EAAE,IAAI2B,KAAK,CAAC1B,cAAc,KAAK,EAAE,EAAE;MAC9D,OAAO,SAAS;IAClB;IAEA,IAAI;MACF;MACA,MAAM+H,GAAG,GAAG,IAAI5F,IAAI,EAAE;MACtB,MAAM6F,KAAK,GAAG,IAAI7F,IAAI,CAAC4F,GAAG,CAACE,WAAW,EAAE,EAAEF,GAAG,CAACG,QAAQ,EAAE,EAAEH,GAAG,CAACI,OAAO,EAAE,CAAC;MAExE;MACA,IAAIxJ,SAAe;MACnB,IAAI+C,KAAK,CAAC3B,gBAAgB,CAACwH,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxC5I,SAAS,GAAG,IAAIwD,IAAI,CAACT,KAAK,CAAC3B,gBAAgB,CAAC;MAC9C,CAAC,MAAM;QACLpB,SAAS,GAAG,IAAIwD,IAAI,CAACT,KAAK,CAAC3B,gBAAgB,GAAG,WAAW,CAAC;MAC5D;MAEA;MACA,IAAInB,OAAa;MACjB,IAAI8C,KAAK,CAAC1B,cAAc,CAACuH,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtC3I,OAAO,GAAG,IAAIuD,IAAI,CAACT,KAAK,CAAC1B,cAAc,CAAC;MAC1C,CAAC,MAAM;QACLpB,OAAO,GAAG,IAAIuD,IAAI,CAACT,KAAK,CAAC1B,cAAc,GAAG,WAAW,CAAC;MACxD;MAEA;MACA,IAAIoI,KAAK,CAACzJ,SAAS,CAAC0J,OAAO,EAAE,CAAC,IAAID,KAAK,CAACxJ,OAAO,CAACyJ,OAAO,EAAE,CAAC,EAAE;QAC1DC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAE;UACnCC,KAAK,EAAE9G,KAAK,CAAC3B,gBAAgB;UAC7B0I,GAAG,EAAE/G,KAAK,CAAC1B,cAAc;UACzB0I,OAAO,EAAEhH,KAAK,CAACtB;SAChB,CAAC;QACF,OAAO,SAAS;MAClB;MAEA;MACA,MAAMuI,aAAa,GAAG,IAAIxG,IAAI,CAACxD,SAAS,CAACsJ,WAAW,EAAE,EAAEtJ,SAAS,CAACuJ,QAAQ,EAAE,EAAEvJ,SAAS,CAACwJ,OAAO,EAAE,CAAC;MAClG,MAAMS,WAAW,GAAG,IAAIzG,IAAI,CAACvD,OAAO,CAACqJ,WAAW,EAAE,EAAErJ,OAAO,CAACsJ,QAAQ,EAAE,EAAEtJ,OAAO,CAACuJ,OAAO,EAAE,CAAC;MAE1F;MACA,IAAIH,KAAK,GAAGW,aAAa,EAAE;QACzB,OAAO,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIX,KAAK,IAAIW,aAAa,IAAIX,KAAK,IAAIY,WAAW,EAAE;QACzD,OAAO,QAAQ,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,OAAO,SAAS,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,EAAEnH,KAAK,CAAC;MACnD,OAAO,SAAS;IAClB;EACF;EAEAoH,OAAOA,CAAC7G,GAAQ;IACdA,GAAG,CAACkB,KAAK,EAAE;EACb;EAEAV,UAAUA,CAAA;IACR,IAAI,CAACjF,KAAK,CAACuL,KAAK,EAAE;IAClB,IAAI,CAACvL,KAAK,CAACwL,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAClJ,uBAAuB,CAACC,gBAAgB,CAAC;IAC9E,IAAI,CAACvC,KAAK,CAACwL,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAClJ,uBAAuB,CAACE,cAAc,CAAC;IAC5E,IAAI,CAACxC,KAAK,CAACyL,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACnJ,uBAAuB,CAACC,gBAAgB,EAAE,IAAI,CAACD,uBAAuB,CAACE,cAAc,CAAC;EACtI;EAEAkJ,gBAAgBA,CAAA;IACd,IAAI,CAACvL,MAAM,CAACwL,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAACvI,WAAW,EAAEC,kBAAkB,EAAE8C,GAAG,EAAE,CAAC,CAAC;EACnG;EAEA;EACAyF,gBAAgBA,CAACrC,QAAwB;IACvC,IAAI,CAAC9H,wBAAwB,GAAG8H,QAAQ,IAAI,IAAI;IAEhD;IACA,MAAMsC,iBAAiB,GAAG,IAAI,CAACrK,cAAc,CAAC2D,MAAM,GAAG,CAAC;IAExD,IAAI,CAACjE,aAAa,GAAG;MACnBC,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CAACwK,iBAAiB,IAAI,CAACtC,QAAQ;MAC3CjI,iBAAiB,EAAEiI,QAAQ,GAAG,CAACA,QAAQ,CAACH,IAAI,CAAC,GAAG,EAAE;MAClD7H,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;KACjB;IAED;IACA,IAAI+H,QAAQ,EAAE;MACZA,QAAQ,CAACvC,MAAM,CAAC/C,OAAO,CAAC4C,KAAK,IAAG;QAC9BA,KAAK,CAAC1C,QAAQ,GAAG,KAAK;QACtB0C,KAAK,CAACqC,MAAM,CAACjF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,KAAK,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAACrE,aAAa,CAAC8E,IAAI,CAAC,IAAI,CAACkH,kBAAkB,CAAC;EAClD;EAEA;EACAC,sBAAsBA,CAAClF,KAAiB;IACtC,IAAIA,KAAK,CAAC1C,QAAQ,EAAE;MAClB0C,KAAK,CAACqC,MAAM,CAACjF,OAAO,CAACC,KAAK,IAAG;QAC3B,IAAIA,KAAK,CAACtB,QAAQ,EAAE;UAClBsB,KAAK,CAACC,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL0C,KAAK,CAACqC,MAAM,CAACjF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA;EACA6H,aAAaA,CAACvH,GAAQ;IACpB;IACA,IAAI,CAACzE,KAAK,CAACuL,KAAK,EAAE;IAClB,IAAI,CAACvL,KAAK,CAACwL,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtK,aAAa,CAACC,SAAS,CAAC;IAC3D,IAAI,CAACnB,KAAK,CAACwL,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtK,aAAa,CAACE,OAAO,CAAC;IACzD,IAAI,CAACpB,KAAK,CAACyL,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACvK,aAAa,CAACC,SAAS,EAAE,IAAI,CAACD,aAAa,CAACE,OAAO,CAAC;IAElG,IAAI,IAAI,CAACpB,KAAK,CAACkF,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACpF,OAAO,CAACqF,aAAa,CAAC,IAAI,CAACpF,KAAK,CAACkF,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAM+G,cAAc,GAAU,EAAE;IAEhC,IAAI,IAAI,CAAC/K,aAAa,CAACG,UAAU,EAAE;MACjC;MACA,IAAI,CAACK,eAAe,CAACuC,OAAO,CAACC,KAAK,IAAG;QACnC,IAAIA,KAAK,CAACtB,QAAQ,EAAE;UAClBqJ,cAAc,CAACnF,IAAI,CAAC;YAClBlE,QAAQ,EAAEsB,KAAK,CAACtB,QAAQ;YACxBL,gBAAgB,EAAE,IAAI,CAACsC,UAAU,CAAC,IAAI,CAAC3D,aAAa,CAACC,SAAS,CAAC;YAC/DqB,cAAc,EAAE,IAAI,CAACqC,UAAU,CAAC,IAAI,CAAC3D,aAAa,CAACE,OAAO;WAC3D,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACI,cAAc,CAAC2D,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAAC3D,cAAc,CAACyC,OAAO,CAACC,KAAK,IAAG;UAClC,IAAIA,KAAK,CAACtB,QAAQ,EAAE;YAClBqJ,cAAc,CAACnF,IAAI,CAAC;cAClBlE,QAAQ,EAAEsB,KAAK,CAACtB,QAAQ;cACxBL,gBAAgB,EAAE,IAAI,CAACsC,UAAU,CAAC,IAAI,CAAC3D,aAAa,CAACC,SAAS,CAAC;cAC/DqB,cAAc,EAAE,IAAI,CAACqC,UAAU,CAAC,IAAI,CAAC3D,aAAa,CAACE,OAAO;aAC3D,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAACK,wBAAwB,EAAE;QACxC;QACA,IAAI,CAACA,wBAAwB,CAACuF,MAAM,CAAC/C,OAAO,CAAC4C,KAAK,IAAG;UACnDA,KAAK,CAACqC,MAAM,CAACjF,OAAO,CAACC,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACtB,QAAQ,EAAE;cACpCqJ,cAAc,CAACnF,IAAI,CAAC;gBAClBlE,QAAQ,EAAEsB,KAAK,CAACtB,QAAQ;gBACxBL,gBAAgB,EAAE,IAAI,CAACsC,UAAU,CAAC,IAAI,CAAC3D,aAAa,CAACC,SAAS,CAAC;gBAC/DqB,cAAc,EAAE,IAAI,CAACqC,UAAU,CAAC,IAAI,CAAC3D,aAAa,CAACE,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,IAAI6K,cAAc,CAAC9G,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAACpF,OAAO,CAACqF,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAACnF,aAAa,CAACqF,oCAAoC,CAAC;MACtDC,IAAI,EAAE0G;KACP,CAAC,CAAC/I,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACyC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACzF,OAAO,CAAC0F,aAAa,CAAC,QAAQwG,cAAc,CAAC9G,MAAM,WAAW,CAAC;QACpE;QACA,IAAI,CAAC3D,cAAc,CAACyC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,KAAK,CAAC;QAC5D,IAAI,CAAC3C,cAAc,GAAG,EAAE;QACxB,IAAI,CAACK,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC6D,kBAAkB,EAAE;QACzBjB,GAAG,CAACkB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA;EACAuG,cAAcA,CAAChI,KAAqB;IAClC,MAAMH,MAAM,GAAG,IAAI,CAACM,cAAc,CAACH,KAAK,CAAC;IACzC,OAAO,UAAUH,MAAM,EAAE;EAC3B;EAEA;EACAoI,eAAeA,CAACjI,KAAqB;IACnC,IAAIA,KAAK,CAACtB,QAAQ,EAAE;MAClB;MACA,IAAI,CAAC4B,SAAS,CAAC,IAAI,CAAC4H,MAAM,EAAElI,KAAK,CAAC;IACpC;EACF;EAEA;EACAqE,oBAAoBA,CAACC,IAA6B;IAChD,IAAI,CAAC9G,eAAe,GAAG,EAAE;IAEzB8G,IAAI,CAACvE,OAAO,CAAC0C,SAAS,IAAG;MACvB,MAAMgC,SAAS,GAAGhC,SAAS,CAAChE,UAAU,IAAI,EAAE;MAE5CgE,SAAS,CAACC,OAAO,EAAE3C,OAAO,CAACC,KAAK,IAAG;QACjC,IAAI,CAACxC,eAAe,CAACoF,IAAI,CAAC;UACxBnE,UAAU,EAAEgG,SAAS;UACrB5B,aAAa,EAAE7C,KAAK,CAAC6C,aAAa,IAAI,KAAK;UAC3CnE,QAAQ,EAAEsB,KAAK,CAACtB,QAAQ,IAAI,CAAC;UAC7BH,MAAM,EAAEyB,KAAK,CAACzB,MAAM,IAAI,CAAC;UACzBF,gBAAgB,EAAE2B,KAAK,CAAC3B,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAE0B,KAAK,CAAC1B,cAAc,IAAI,EAAE;UAC1C2B,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACN,QAAQ,EAAE;IAEf;IACA,IAAI,CAACwI,iBAAiB,EAAE;EAC1B;EAEA;EACQA,iBAAiBA,CAAA;IACvB,MAAMC,YAAY,GAAG;MACnBC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACV,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAAChL,eAAe,CAACuC,OAAO,CAACC,KAAK,IAAG;MACnC,MAAMH,MAAM,GAAG,IAAI,CAACM,cAAc,CAACH,KAAK,CAAC;MACzC,IAAIoI,YAAY,CAACK,cAAc,CAAC5I,MAAM,CAAC,EAAE;QACvCuI,YAAY,CAACvI,MAAmC,CAAC,EAAE;MACrD;IACF,CAAC,CAAC;IAEF+G,OAAO,CAAC8B,GAAG,CAAC,OAAO,EAAEN,YAAY,CAAC;IAClCxB,OAAO,CAAC8B,GAAG,CAAC,OAAO,EAAE,IAAIjI,IAAI,EAAE,CAACkI,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;EAIA;EACAjJ,QAAQA,CAAA;IACN;IACA,MAAMkJ,qBAAqB,GAAG,IAAI,CAACvL,cAAc,CAACwE,GAAG,CAAC9B,KAAK,IAAIA,KAAK,CAACtB,QAAQ,CAAC;IAE9E,IAAI,CAACjB,cAAc,GAAG,IAAI,CAACD,eAAe,CAACgI,MAAM,CAACxF,KAAK,IAAG;MACxD;MACA,IAAI,IAAI,CAACrD,aAAa,CAACC,aAAa,EAAE;QACpC,MAAM6I,OAAO,GAAG,IAAI,CAAC9I,aAAa,CAACC,aAAa,CAAC8I,WAAW,EAAE;QAC9D,IAAI,CAAC1F,KAAK,CAACvB,UAAU,CAACiH,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzF,KAAK,CAAC6C,aAAa,CAAC6C,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;UACtD,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAChJ,gBAAgB,IAAIuD,KAAK,CAAC6C,aAAa,KAAK,IAAI,CAACpG,gBAAgB,EAAE;QAC1E,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAACE,aAAa,CAACE,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAACkJ,mBAAmB,CAAC/F,KAAK,CAAC,EAAE;UACpC,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACrD,aAAa,CAACG,WAAW,EAAE;QAClC,MAAMiI,WAAW,GAAGiB,QAAQ,CAAC,IAAI,CAACrJ,aAAa,CAACG,WAAW,CAAC;QAC5D,IAAIkD,KAAK,CAACzB,MAAM,KAAKwG,WAAW,EAAE;UAChC,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF;IACA,IAAI,CAACzH,cAAc,GAAG,IAAI,CAACG,cAAc,CAAC+H,MAAM,CAACxF,KAAK,IACpD6I,qBAAqB,CAAChD,QAAQ,CAAC7F,KAAK,CAACtB,QAAQ,CAAC,CAC/C;IAED;IACA,IAAI,CAAClB,eAAe,CAACuC,OAAO,CAACC,KAAK,IAAG;MACnCA,KAAK,CAACC,QAAQ,GAAG,IAAI,CAAC3C,cAAc,CAACsI,IAAI,CAAC3F,QAAQ,IAAIA,QAAQ,CAACvB,QAAQ,KAAKsB,KAAK,CAACtB,QAAQ,CAAC;IAC7F,CAAC,CAAC;IAEF;IACA,IAAI,CAACoK,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACjL,WAAW,GAAG,CAAC;IACpB,IAAI,CAACkL,gBAAgB,EAAE;EACzB;EAEA;EACQD,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACpL,eAAe,CAACuD,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACtD,SAAS,GAAG,KAAK;IACxB,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI,CAACD,eAAe,CAACsL,KAAK,CAAChJ,KAAK,IAC/C,CAACA,KAAK,CAACtB,QAAQ,IAAIsB,KAAK,CAACC,QAAQ,CAClC;IACH;EACF;EAEA;EACA8I,gBAAgBA,CAAA;IACd,IAAI,CAAChL,UAAU,GAAGG,IAAI,CAAC+K,IAAI,CAAC,IAAI,CAACxL,cAAc,CAACwD,MAAM,GAAG,IAAI,CAACnD,QAAQ,CAAC;IACvE,MAAMoL,UAAU,GAAG,CAAC,IAAI,CAACrL,WAAW,GAAG,CAAC,IAAI,IAAI,CAACC,QAAQ;IACzD,MAAMqL,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACpL,QAAQ;IAC3C,IAAI,CAACJ,eAAe,GAAG,IAAI,CAACD,cAAc,CAAC2L,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACL,oBAAoB,EAAE;EAC7B;EAEA;EACAO,gBAAgBA,CAAA;IACd,IAAI,CAACxL,WAAW,GAAG,CAAC;IACpB,IAAI,CAACkL,gBAAgB,EAAE;EACzB;EAEA;EACA1I,QAAQA,CAACiJ,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACvL,UAAU,EAAE;MACxC,IAAI,CAACF,WAAW,GAAGyL,IAAI;MACvB,IAAI,CAACP,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAQ,eAAeA,CAAA;IACb,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAI3C,KAAK,GAAG5I,IAAI,CAACwL,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7L,WAAW,GAAGK,IAAI,CAACyE,KAAK,CAAC8G,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAI1C,GAAG,GAAG7I,IAAI,CAACyL,GAAG,CAAC,IAAI,CAAC5L,UAAU,EAAE+I,KAAK,GAAG2C,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAI1C,GAAG,GAAGD,KAAK,GAAG,CAAC,GAAG2C,UAAU,EAAE;MAChC3C,KAAK,GAAG5I,IAAI,CAACwL,GAAG,CAAC,CAAC,EAAE3C,GAAG,GAAG0C,UAAU,GAAG,CAAC,CAAC;IAC3C;IAEA,KAAK,IAAIG,CAAC,GAAG9C,KAAK,EAAE8C,CAAC,IAAI7C,GAAG,EAAE6C,CAAC,EAAE,EAAE;MACjCJ,KAAK,CAAC5G,IAAI,CAACgH,CAAC,CAAC;IACf;IAEA,OAAOJ,KAAK;EACd;EAEA;EACAK,iBAAiBA,CAAA;IACf,IAAI,CAACnM,eAAe,CAACqC,OAAO,CAACC,KAAK,IAAG;MACnC,IAAIA,KAAK,CAACtB,QAAQ,EAAE;QAClBsB,KAAK,CAACC,QAAQ,GAAG,IAAI,CAACtC,SAAS;MACjC;IACF,CAAC,CAAC;IACF,IAAI,CAACmM,oBAAoB,EAAE;EAC7B;EAEA;EACAC,sBAAsBA,CAAA;IACpB,IAAI,CAACD,oBAAoB,EAAE;IAC3B,IAAI,CAACnM,SAAS,GAAG,IAAI,CAACD,eAAe,CAACsL,KAAK,CAAChJ,KAAK,IAC/C,CAACA,KAAK,CAACtB,QAAQ,IAAIsB,KAAK,CAACC,QAAQ,CAClC;EACH;EAEA;EACA6J,oBAAoBA,CAAA;IAClB,IAAI,CAACxM,cAAc,GAAG,IAAI,CAACE,eAAe,CAACgI,MAAM,CAACxF,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EAC5E;EAEA;EACA8C,IAAIA,CAACiH,KAAa;IAChB,IAAI,IAAI,CAAChM,SAAS,KAAKgM,KAAK,EAAE;MAC5B,IAAI,CAAC/L,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IACpE,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAGgM,KAAK;MACtB,IAAI,CAAC/L,aAAa,GAAG,KAAK;IAC5B;IAEA,IAAI,CAACR,cAAc,CAACsF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAChC,IAAIgH,MAAM,GAAIjH,CAAS,CAACgH,KAAK,CAAC;MAC9B,IAAIE,MAAM,GAAIjH,CAAS,CAAC+G,KAAK,CAAC;MAE9B;MACA,IAAIA,KAAK,CAACnE,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC1BoE,MAAM,GAAGA,MAAM,GAAG,IAAIxJ,IAAI,CAACwJ,MAAM,CAAC,CAACtD,OAAO,EAAE,GAAG,CAAC;QAChDuD,MAAM,GAAGA,MAAM,GAAG,IAAIzJ,IAAI,CAACyJ,MAAM,CAAC,CAACvD,OAAO,EAAE,GAAG,CAAC;MAClD;MAEA;MACA,IAAIqD,KAAK,KAAK,QAAQ,EAAE;QACtBC,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC,IAAI,CAAC;QAC5BC,MAAM,GAAGC,MAAM,CAACD,MAAM,CAAC,IAAI,CAAC;MAC9B;MAEA;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAACvE,WAAW,EAAE;QAC7BwE,MAAM,GAAGA,MAAM,CAACxE,WAAW,EAAE;MAC/B;MAEA,IAAIuE,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACjM,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC9C;MACA,IAAIgM,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACjM,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9C;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAAC8K,gBAAgB,EAAE;EACzB;EAEA;EACAqB,cAAcA,CAACC,MAAc,EAAErK,KAAqB;IAClD,OAAOA,KAAK,CAACtB,QAAQ;EACvB;EAEA;EACA4L,aAAaA,CAACtK,KAAqB;IACjC,MAAMH,MAAM,GAAG,IAAI,CAACM,cAAc,CAACH,KAAK,CAAC;IAEzC,QAAQH,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,UAAU;QACb,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACA0K,UAAUA,CAAA;IACR;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,WAAW,EAAE;IACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACrCG,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,UAAU,IAAI1K,IAAI,EAAE,CAACkI,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACrFiC,IAAI,CAACO,KAAK,CAACC,UAAU,GAAG,QAAQ;IAChCP,QAAQ,CAACzJ,IAAI,CAACiK,WAAW,CAACT,IAAI,CAAC;IAC/BA,IAAI,CAACU,KAAK,EAAE;IACZT,QAAQ,CAACzJ,IAAI,CAACmK,WAAW,CAACX,IAAI,CAAC;EACjC;EAEA;EACQJ,WAAWA,CAAA;IACjB,MAAMgB,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;IACxD,MAAMC,IAAI,GAAG,IAAI,CAACjO,cAAc,CAACqE,GAAG,CAAC9B,KAAK,IAAI,CAC5CA,KAAK,CAACvB,UAAU,EAChBuB,KAAK,CAAC6C,aAAa,EACnB,GAAG7C,KAAK,CAACzB,MAAM,GAAG,EAClByB,KAAK,CAAC3B,gBAAgB,IAAI,KAAK,EAC/B2B,KAAK,CAAC1B,cAAc,IAAI,KAAK,EAC7B,IAAI,CAACgM,aAAa,CAACtK,KAAK,CAAC,CAC1B,CAAC;IAEF,MAAMwK,UAAU,GAAG,CAACiB,OAAO,EAAE,GAAGC,IAAI,CAAC,CAClC5J,GAAG,CAAC6J,GAAG,IAAIA,GAAG,CAAC7J,GAAG,CAAC8J,IAAI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAClDA,IAAI,CAAC,IAAI,CAAC;IAEb,OAAO,QAAQ,GAAGrB,UAAU,CAAC,CAAC;EAChC;CACD;AAplCkCsB,UAAA,EAAhC9Q,SAAS,CAAC,oBAAoB,CAAC,C,qEAAuC;AAClD8Q,UAAA,EAApB9Q,SAAS,CAAC,QAAQ,CAAC,C,yDAA2B;AAJpCS,0BAA0B,GAAAqQ,UAAA,EAdtC/Q,SAAS,CAAC;EACTgR,QAAQ,EAAE,yBAAyB;EACnCC,WAAW,EAAE,sCAAsC;EACnDC,SAAS,EAAE,CAAC,sCAAsC,CAAC;EACnDC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,CACPnR,YAAY,EAAEM,YAAY,EAC1BJ,kBAAkB,EAAEC,mBAAmB,EACvCC,qBAAqB;CAGxB,CAAC,C,EAEWI,0BAA0B,CAulCtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}