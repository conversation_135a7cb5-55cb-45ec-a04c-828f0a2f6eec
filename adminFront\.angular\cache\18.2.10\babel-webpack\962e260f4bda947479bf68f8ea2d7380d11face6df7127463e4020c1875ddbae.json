{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction TemplateCreatorComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.validationErrors[\"api\"], \" \");\n  }\n}\nfunction TemplateCreatorComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.validationErrors[\"name\"], \" \");\n  }\n}\nfunction TemplateCreatorComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\", 38)(2, \"input\", 39);\n    i0.ɵɵlistener(\"change\", function TemplateCreatorComponent_div_32_Template_input_change_2_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleSelectAll());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 40);\n    i0.ɵɵelementStart(4, \"span\", 41);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r0.isAllSelected())(\"indeterminate\", ctx_r0.isIndeterminate())(\"disabled\", ctx_r0.isSubmitting);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isAllSelected() ? \"\\u53D6\\u6D88\\u5168\\u9078\" : \"\\u5168\\u9078\", \" \");\n  }\n}\nfunction TemplateCreatorComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u66AB\\u7121\\u53EF\\u9078\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateCreatorComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"label\", 45);\n    i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_div_35_Template_label_click_1_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleItemSelection(item_r4));\n    });\n    i0.ɵɵelementStart(2, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateCreatorComponent_div_35_Template_input_ngModelChange_2_listener($event) {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r4.selected, $event) || (item_r4.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_div_35_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 47)(4, \"div\", 48);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"item_\", i_r5, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r4.selected);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSubmitting);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r4.CRequirement || item_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.CGroupName || item_r4.description);\n  }\n}\nfunction TemplateCreatorComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.validationErrors[\"items\"], \" \");\n  }\n}\nfunction TemplateCreatorComponent_i_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 52);\n  }\n}\nfunction TemplateCreatorComponent_i_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 53);\n  }\n}\nexport let TemplateCreatorComponent = /*#__PURE__*/(() => {\n  class TemplateCreatorComponent {\n    constructor(templateService) {\n      this.templateService = templateService;\n      this.availableData = []; // 父元件傳入的可選資料\n      this.templateType = 1; // 模板類型，1=客變需求\n      this.close = new EventEmitter(); // 關閉事件\n      this.templateCreated = new EventEmitter(); // 模板創建成功事件\n      // 新增模板表單\n      this.newTemplate = {\n        name: ''\n      };\n      // 表單驗證狀態\n      this.isSubmitting = false;\n      this.validationErrors = {};\n    }\n    ngOnInit() {\n      // 初始化時重置所有選擇狀態\n      this.resetForm();\n    }\n    // 重置表單\n    resetForm() {\n      this.newTemplate = {\n        name: ''\n      };\n      this.validationErrors = {};\n      this.isSubmitting = false;\n      // 重置選擇狀態\n      if (this.availableData) {\n        this.availableData.forEach(item => item.selected = false);\n      }\n    }\n    // 驗證表單\n    validateForm() {\n      this.validationErrors = {};\n      let isValid = true;\n      // 驗證模板名稱\n      if (!this.newTemplate.name.trim()) {\n        this.validationErrors['name'] = '請輸入模板名稱';\n        isValid = false;\n      } else if (this.newTemplate.name.trim().length > 50) {\n        this.validationErrors['name'] = '模板名稱不能超過50個字符';\n        isValid = false;\n      }\n      // 驗證是否選擇了項目\n      const selectedItems = this.getSelectedItems();\n      if (selectedItems.length === 0) {\n        this.validationErrors['items'] = '請至少選擇一個項目';\n        isValid = false;\n      }\n      return isValid;\n    }\n    // 獲取選中的項目\n    getSelectedItems() {\n      if (!this.availableData) return [];\n      return this.availableData.filter(item => item.selected).map(item => ({\n        CGroupName: item.CGroupName || null,\n        CReleateName: item.CReleateName || item.CRequirement || item.name || null,\n        CReleateId: item.CReleateId || item.CRequirementID || item.ID || item.id || 0\n      }));\n    }\n    // 儲存新模板\n    saveTemplate() {\n      if (!this.validateForm()) {\n        return;\n      }\n      this.isSubmitting = true;\n      const selectedItems = this.getSelectedItems();\n      // 準備 API 請求資料\n      const saveTemplateArgs = {\n        CTemplateId: null,\n        // 新增時為 null\n        CTemplateName: this.newTemplate.name.trim(),\n        CTemplateType: this.templateType,\n        // 1=客變需求\n        CStatus: 1,\n        // 啟用狀態\n        Details: selectedItems.map(item => ({\n          CTemplateDetailId: null,\n          // 新增時為 null\n          CReleateId: item.CReleateId,\n          // 關聯主檔ID\n          CReleateName: item.CReleateName,\n          // 關聯名稱\n          CGroupName: item.CGroupName // 群組名稱\n        }))\n      };\n      // 調用 SaveTemplate API\n      this.templateService.apiTemplateSaveTemplatePost$Json({\n        body: saveTemplateArgs\n      }).subscribe({\n        next: response => {\n          this.isSubmitting = false;\n          if (response.StatusCode === 0) {\n            // API 調用成功\n            this.templateCreated.emit(); // 通知父組件模板創建成功\n            this.close.emit(); // 關閉對話框\n          } else {\n            // API 返回錯誤\n            this.validationErrors['api'] = response.Message || '保存失敗，請稍後重試';\n          }\n        },\n        error: error => {\n          this.isSubmitting = false;\n          this.validationErrors['api'] = '網絡錯誤，請檢查網絡連接後重試';\n          console.error('保存模板失敗:', error);\n        }\n      });\n    }\n    // 取消操作\n    cancel() {\n      this.close.emit();\n    }\n    // 獲取選中項目數量\n    getSelectedCount() {\n      return this.availableData ? this.availableData.filter(item => item.selected).length : 0;\n    }\n    // 切換項目選擇狀態\n    toggleItemSelection(item) {\n      item.selected = !item.selected;\n      // 清除項目選擇相關的驗證錯誤\n      if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\n        delete this.validationErrors['items'];\n      }\n    }\n    // 全選/取消全選\n    toggleSelectAll() {\n      const hasUnselected = this.availableData.some(item => !item.selected);\n      this.availableData.forEach(item => item.selected = hasUnselected);\n      // 清除項目選擇相關的驗證錯誤\n      if (this.validationErrors['items'] && this.getSelectedCount() > 0) {\n        delete this.validationErrors['items'];\n      }\n    }\n    // 檢查是否全選\n    isAllSelected() {\n      return this.availableData && this.availableData.length > 0 && this.availableData.every(item => item.selected);\n    }\n    // 檢查是否部分選中\n    isIndeterminate() {\n      const selectedCount = this.getSelectedCount();\n      return selectedCount > 0 && selectedCount < this.availableData.length;\n    }\n    static {\n      this.ɵfac = function TemplateCreatorComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TemplateCreatorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TemplateCreatorComponent,\n        selectors: [[\"app-template-creator\"]],\n        inputs: {\n          availableData: \"availableData\",\n          templateType: \"templateType\"\n        },\n        outputs: {\n          close: \"close\",\n          templateCreated: \"templateCreated\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 46,\n        vars: 18,\n        consts: [[2, \"width\", \"90vw\", \"max-width\", \"800px\", \"height\", \"80vh\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"fas\", \"fa-plus\", \"mr-2\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [2, \"overflow\", \"auto\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [1, \"template-form\", 3, \"ngSubmit\"], [1, \"form-section\", \"mb-4\"], [1, \"section-title\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [1, \"input-group\"], [1, \"input-label\"], [1, \"required\"], [\"type\", \"text\", \"name\", \"templateName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"maxlength\", \"50\", 1, \"input-field\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"form-section\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"section-title\", \"mb-0\"], [1, \"fas\", \"fa-list\", \"mr-2\"], [1, \"selection-summary\"], [1, \"badge\", \"badge-primary\"], [\"class\", \"select-all-control mb-3\", 4, \"ngIf\"], [1, \"items-selector\"], [\"class\", \"empty-items\", 4, \"ngIf\"], [\"class\", \"item-option\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"invalid-feedback d-block\", 4, \"ngIf\"], [1, \"form-actions\", \"d-flex\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"mr-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", 3, \"click\", \"disabled\"], [\"class\", \"fas fa-spinner fa-spin mr-1\", 4, \"ngIf\"], [\"class\", \"fas fa-save mr-1\", 4, \"ngIf\"], [1, \"alert\", \"alert-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-2\"], [1, \"invalid-feedback\"], [1, \"select-all-control\", \"mb-3\"], [1, \"select-all-label\"], [\"type\", \"checkbox\", 1, \"select-all-checkbox\", 3, \"change\", \"checked\", \"indeterminate\", \"disabled\"], [1, \"checkmark\"], [1, \"select-all-text\"], [1, \"empty-items\"], [1, \"fas\", \"fa-info-circle\"], [1, \"item-option\"], [1, \"item-label\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"item-checkbox\", 3, \"ngModelChange\", \"click\", \"ngModel\", \"name\", \"disabled\"], [1, \"item-content\"], [1, \"item-title\"], [1, \"item-desc\"], [1, \"invalid-feedback\", \"d-block\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-1\"], [1, \"fas\", \"fa-save\", \"mr-1\"]],\n        template: function TemplateCreatorComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\")(2, \"div\", 1)(3, \"h5\", 2);\n            i0.ɵɵelement(4, \"i\", 3);\n            i0.ɵɵtext(5, \"\\u65B0\\u589E\\u6A21\\u677F \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_Template_button_click_6_listener() {\n              return ctx.cancel();\n            });\n            i0.ɵɵelement(7, \"i\", 5);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(8, \"nb-card-body\", 6);\n            i0.ɵɵtemplate(9, TemplateCreatorComponent_div_9_Template, 3, 1, \"div\", 7);\n            i0.ɵɵelementStart(10, \"form\", 8);\n            i0.ɵɵlistener(\"ngSubmit\", function TemplateCreatorComponent_Template_form_ngSubmit_10_listener() {\n              return ctx.saveTemplate();\n            });\n            i0.ɵɵelementStart(11, \"div\", 9)(12, \"h6\", 10);\n            i0.ɵɵelement(13, \"i\", 11);\n            i0.ɵɵtext(14, \"\\u6A21\\u677F\\u8CC7\\u8A0A \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 12)(16, \"label\", 13);\n            i0.ɵɵtext(17, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n            i0.ɵɵelementStart(18, \"span\", 14);\n            i0.ɵɵtext(19, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"input\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateCreatorComponent_Template_input_ngModelChange_20_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.newTemplate.name, $event) || (ctx.newTemplate.name = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(21, TemplateCreatorComponent_div_21_Template, 2, 1, \"div\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 17)(23, \"div\", 18)(24, \"h6\", 19);\n            i0.ɵɵelement(25, \"i\", 20);\n            i0.ɵɵtext(26, \"\\u9078\\u64C7\\u8981\\u52A0\\u5165\\u6A21\\u677F\\u7684\\u9805\\u76EE \");\n            i0.ɵɵelementStart(27, \"span\", 14);\n            i0.ɵɵtext(28, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(29, \"div\", 21)(30, \"span\", 22);\n            i0.ɵɵtext(31);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(32, TemplateCreatorComponent_div_32_Template, 6, 4, \"div\", 23);\n            i0.ɵɵelementStart(33, \"div\", 24);\n            i0.ɵɵtemplate(34, TemplateCreatorComponent_div_34_Template, 4, 0, \"div\", 25)(35, TemplateCreatorComponent_div_35_Template, 8, 6, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(36, TemplateCreatorComponent_div_36_Template, 3, 1, \"div\", 27);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"nb-card-footer\")(38, \"div\", 28)(39, \"button\", 29);\n            i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_Template_button_click_39_listener() {\n              return ctx.cancel();\n            });\n            i0.ɵɵelement(40, \"i\", 30);\n            i0.ɵɵtext(41, \"\\u53D6\\u6D88 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"button\", 31);\n            i0.ɵɵlistener(\"click\", function TemplateCreatorComponent_Template_button_click_42_listener() {\n              return ctx.saveTemplate();\n            });\n            i0.ɵɵtemplate(43, TemplateCreatorComponent_i_43_Template, 1, 0, \"i\", 32)(44, TemplateCreatorComponent_i_44_Template, 1, 0, \"i\", 33);\n            i0.ɵɵtext(45);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.validationErrors[\"api\"]);\n            i0.ɵɵadvance(11);\n            i0.ɵɵclassProp(\"is-invalid\", ctx.validationErrors[\"name\"]);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newTemplate.name);\n            i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.validationErrors[\"name\"]);\n            i0.ɵɵadvance(10);\n            i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7 \", ctx.getSelectedCount(), \" \\u9805\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.availableData.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"has-error\", ctx.validationErrors[\"items\"]);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.availableData.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.availableData);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.validationErrors[\"items\"]);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.isSubmitting);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"\\u4FDD\\u5B58\\u4E2D...\" : \"\\u5132\\u5B58\\u6A21\\u677F\", \" \");\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, NbCardModule, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardFooterComponent, i4.NbCardHeaderComponent, NbButtonModule],\n        styles: [\".template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]{border:1px solid #e4e9f0;border-radius:8px;padding:1.5rem;background-color:#fafbfc}.template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#2c3e50;font-weight:600;margin-bottom:1rem}.template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3498db}.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{margin-bottom:1rem}.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]{display:block;font-weight:500;color:#2c3e50;margin-bottom:.5rem;font-size:.9rem}.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%]{color:#e74c3c;margin-left:2px}.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]{width:100%;padding:.75rem;border:1px solid #ddd;border-radius:4px;font-size:.9rem;transition:border-color .3s ease}.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus{outline:none;border-color:#3498db;box-shadow:0 0 0 2px #3498db33}.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field.is-invalid[_ngcontent-%COMP%]{border-color:#e74c3c;box-shadow:0 0 0 2px #e74c3c33}.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:disabled{background-color:#f8f9fa;cursor:not-allowed}.template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%]{color:#e74c3c;font-size:.8rem;margin-top:.25rem;display:block}.selection-summary[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.8rem;padding:.4rem .8rem}.select-all-control[_ngcontent-%COMP%]{padding:.75rem;background-color:#f8f9fa;border:1px solid #dee2e6;border-radius:4px}.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%]{display:flex;align-items:center;margin:0;cursor:pointer;font-weight:500}.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]{margin-right:.5rem;transform:scale(1.1)}.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%]   .select-all-text[_ngcontent-%COMP%]{color:#495057}.select-all-control[_ngcontent-%COMP%]   .select-all-label[_ngcontent-%COMP%]:hover   .select-all-text[_ngcontent-%COMP%]{color:#007bff}.items-selector[_ngcontent-%COMP%]{max-height:300px;overflow-y:auto;border:1px solid #dee2e6;border-radius:4px;background-color:#fff}.items-selector.has-error[_ngcontent-%COMP%]{border-color:#e74c3c}.items-selector[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]{text-align:center;padding:2rem;color:#6c757d}.items-selector[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:.5rem;display:block}.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]{border-bottom:1px solid #f1f3f4}.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:last-child{border-bottom:none}.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem;margin:0;cursor:pointer;transition:background-color .2s ease}.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]{margin-right:.75rem;transform:scale(1.1)}.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]:disabled{cursor:not-allowed}.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]{flex:1}.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%]{font-weight:500;color:#2c3e50;margin-bottom:.25rem}.items-selector[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-desc[_ngcontent-%COMP%]{font-size:.85rem;color:#6c757d}.form-actions[_ngcontent-%COMP%]{gap:.5rem}.form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{min-width:100px;font-weight:500}.form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem}.form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled{cursor:not-allowed}@media (max-width: 768px){.template-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]{padding:1rem}.items-selector[_ngcontent-%COMP%]{max-height:250px}.form-actions[_ngcontent-%COMP%]{flex-direction:column}.form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%;margin-bottom:.5rem}}.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}.items-selector[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}.item-option[_ngcontent-%COMP%]{transition:all .2s ease}.form-section[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.form-section[_ngcontent-%COMP%]:hover{box-shadow:0 2px 8px #0000001a}\"]\n      });\n    }\n  }\n  return TemplateCreatorComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}