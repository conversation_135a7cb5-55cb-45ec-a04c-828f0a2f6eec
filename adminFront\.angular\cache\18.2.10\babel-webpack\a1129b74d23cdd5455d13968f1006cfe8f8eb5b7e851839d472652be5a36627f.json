{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction TemplateViewerComponent_div_13_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_13_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(2, \"i\", 30);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_13_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\", 32);\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u627E\\u5230 \", ctx_r1.filteredTemplates.length, \" \\u500B\\u76F8\\u95DC\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_div_13_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"small\", 35);\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵtext(3, \"\\u672A\\u627E\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"div\", 21)(3, \"div\", 22);\n    i0.ɵɵelement(4, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_13_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchKeyword, $event) || (ctx_r1.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function TemplateViewerComponent_div_13_Template_input_input_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    })(\"keyup.enter\", function TemplateViewerComponent_div_13_Template_input_keyup_enter_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_13_div_6_Template, 3, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, TemplateViewerComponent_div_13_div_7_Template, 4, 1, \"div\", 26)(8, TemplateViewerComponent_div_13_div_8_Template, 4, 0, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword && ctx_r1.filteredTemplates.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword && ctx_r1.filteredTemplates.length === 0);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 4)(2, \"div\", 43)(3, \"span\", 44);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 45)(6, \"small\", 8);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A\\u7B2C \", (ctx_r1.templatePagination.currentPage - 1) * ctx_r1.templatePagination.pageSize + 1, \" - \", ctx_r1.Math.min(ctx_r1.templatePagination.currentPage * ctx_r1.templatePagination.pageSize, ctx_r1.templatePagination.totalItems), \" \\u9805\\uFF0C \\u5171 \", ctx_r1.templatePagination.totalItems, \" \\u9805\\u6A21\\u677F \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\\u7B2C \", ctx_r1.templatePagination.currentPage, \" / \", ctx_r1.templatePagination.totalPages, \" \\u9801\");\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"span\", 59);\n    i0.ɵɵelement(2, \"i\", 60);\n    i0.ɵɵtext(3, \"\\u5EFA\\u7ACB\\u65E5\\u671F\\uFF1A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 61);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 1, tpl_r5.CreateTime, \"yyyy/MM/dd HH:mm\"));\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"span\", 59);\n    i0.ɵɵelement(2, \"i\", 62);\n    i0.ɵɵtext(3, \"\\u66F4\\u65B0\\u6642\\u9593\\uFF1A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 61);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 1, tpl_r5.UpdateTime, \"yyyy/MM/dd HH:mm\"));\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_3_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const tpl_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(tpl_r5.TemplateID && ctx_r1.onDeleteTemplate(tpl_r5.TemplateID));\n    });\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_14_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 48)(3, \"span\", 49);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\", 50);\n    i0.ɵɵelement(6, \"i\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 52);\n    i0.ɵɵtemplate(9, TemplateViewerComponent_div_14_div_3_div_9_Template, 7, 4, \"div\", 53)(10, TemplateViewerComponent_div_14_div_3_div_10_Template, 7, 4, \"div\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 54)(12, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_3_Template_button_click_12_listener() {\n      const tpl_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r5));\n    });\n    i0.ɵɵelement(13, \"i\", 56);\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, TemplateViewerComponent_div_14_div_3_button_16_Template, 4, 0, \"button\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r5 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", tpl_r5.TemplateName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", tpl_r5.CreateTime);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", tpl_r5.UpdateTime);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", tpl_r5.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 23);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 72);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p\", 73);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5617\\u8A66\\u5176\\u4ED6\\u95DC\\u9375\\u5B57\\u6216 \");\n    i0.ɵɵelementStart(2, \"a\", 74);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_4_p_7_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(3, \"\\u6E05\\u9664\\u641C\\u5C0B\\u689D\\u4EF6\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 73);\n    i0.ɵɵtext(1, \" \\u76EE\\u524D\\u9084\\u6C92\\u6709\\u5EFA\\u7ACB\\u4EFB\\u4F55\\u6A21\\u677F\\uFF0C\\u8ACB\\u5148\\u5EFA\\u7ACB\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66)(2, \"div\", 67);\n    i0.ɵɵtemplate(3, TemplateViewerComponent_div_14_div_4_i_3_Template, 1, 0, \"i\", 68)(4, TemplateViewerComponent_div_14_div_4_i_4_Template, 1, 0, \"i\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\", 70);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, TemplateViewerComponent_div_14_div_4_p_7_Template, 4, 0, \"p\", 71)(8, TemplateViewerComponent_div_14_div_4_p_8_Template, 2, 0, \"p\", 71);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u66AB\\u7121\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_5_li_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 81)(1, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_li_13_Template_button_click_1_listener() {\n      const page_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(page_r10));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r10 === ctx_r1.templatePagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r10);\n  }\n}\nfunction TemplateViewerComponent_div_14_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"div\", 77)(3, \"span\", 78);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"nav\", 79)(6, \"ul\", 80)(7, \"li\", 81)(8, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(1));\n    });\n    i0.ɵɵelement(9, \"i\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"li\", 81)(11, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage - 1));\n    });\n    i0.ɵɵelement(12, \"i\", 85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, TemplateViewerComponent_div_14_div_5_li_13_Template, 3, 3, \"li\", 86);\n    i0.ɵɵelementStart(14, \"li\", 81)(15, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage + 1));\n    });\n    i0.ɵɵelement(16, \"i\", 88);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"li\", 81)(18, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_14_div_5_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.totalPages));\n    });\n    i0.ɵɵelement(19, \"i\", 90);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.templatePagination.currentPage, \" \\u9801\\uFF0C\\u5171 \", ctx_r1.templatePagination.totalPages, \" \\u9801 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getTemplatePageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_14_div_1_Template, 8, 5, \"div\", 38);\n    i0.ɵɵelementStart(2, \"div\", 37);\n    i0.ɵɵtemplate(3, TemplateViewerComponent_div_14_div_3_Template, 17, 4, \"div\", 39)(4, TemplateViewerComponent_div_14_div_4_Template, 9, 5, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TemplateViewerComponent_div_14_div_5_Template, 20, 15, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalItems > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedTemplates)(\"ngForTrackBy\", ctx_r1.trackByTemplateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.paginatedTemplates || ctx_r1.paginatedTemplates.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templatePagination.totalPages > 1);\n  }\n}\nfunction TemplateViewerComponent_div_15_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 111);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedTemplate.Description, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"span\", 104);\n    i0.ɵɵtext(2, \"\\u9801\\u6578\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 105);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.detailPagination.currentPage, \" / \", ctx_r1.detailPagination.totalPages, \"\");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_div_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearDetailSearch());\n    });\n    i0.ɵɵelement(2, \"i\", 30);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_15_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"small\", 32);\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u627E\\u5230 \", ctx_r1.detailPagination.totalItems, \" \\u500B\\u76F8\\u95DC\\u9805\\u76EE \");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"small\", 35);\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵtext(3, \"\\u672A\\u627E\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 130);\n    i0.ɵɵelement(1, \"i\", 131);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r13.CGroupName);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 132);\n    i0.ɵɵelement(1, \"i\", 133);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r13.CCategory);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_div_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 139)(1, \"span\", 140);\n    i0.ɵɵtext(2, \"\\u55AE\\u50F9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 141);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"NT$ \", i0.ɵɵpipeBind1(5, 1, detail_r13.CUnitPrice), \"\");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_div_21_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 142)(1, \"span\", 140);\n    i0.ɵɵtext(2, \"\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 143);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", detail_r13.CQuantity, \" \", detail_r13.CUnit, \"\");\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_div_21_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"div\", 145);\n    i0.ɵɵelement(2, \"i\", 146);\n    i0.ɵɵelementStart(3, \"span\", 147);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(detail_r13.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 134)(1, \"div\", 135);\n    i0.ɵɵtemplate(2, TemplateViewerComponent_div_15_div_28_div_1_div_21_div_2_Template, 6, 3, \"div\", 136)(3, TemplateViewerComponent_div_15_div_28_div_1_div_21_div_3_Template, 5, 2, \"div\", 137);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TemplateViewerComponent_div_15_div_28_div_1_div_21_div_4_Template, 5, 1, \"div\", 138);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CQuantity && detail_r13.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114)(1, \"div\", 115)(2, \"div\", 116)(3, \"div\", 117)(4, \"span\", 118);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 119)(7, \"h6\", 120);\n    i0.ɵɵelement(8, \"i\", 121);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 122)(11, \"span\", 123);\n    i0.ɵɵelement(12, \"i\", 124);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, TemplateViewerComponent_div_15_div_28_div_1_span_15_Template, 4, 1, \"span\", 125)(16, TemplateViewerComponent_div_15_div_28_div_1_span_16_Template, 4, 1, \"span\", 126);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 127)(18, \"span\", 128);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, TemplateViewerComponent_div_15_div_28_div_1_div_21_Template, 5, 3, \"div\", 129);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r13 = ctx.$implicit;\n    const i_r14 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r14 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", detail_r13.CReleateName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(detail_r13.CReleateId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CCategory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 7, detail_r13.CCreateDt, \"MM/dd\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", detail_r13.CUnitPrice || detail_r13.CQuantity || detail_r13.CUnit || detail_r13.CRemark);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_15_div_28_div_1_Template, 22, 10, \"div\", 113);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetailsData);\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_29_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 151)(1, \"div\", 152)(2, \"span\", 153);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 154)(5, \"div\", 155)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 156);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r15 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r16 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", detail_r15.FieldName, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", detail_r15.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_29_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 149);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_15_ng_template_29_div_0_div_1_Template, 10, 3, \"div\", 150);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedDetails);\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TemplateViewerComponent_div_15_ng_template_29_div_0_Template, 2, 1, \"div\", 148);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const noDetails_r17 = i0.ɵɵreference(33);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetails.length > 0)(\"ngIfElse\", noDetails_r17);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_31_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 81)(1, \"button\", 161);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_div_31_li_6_Template_button_click_1_listener() {\n      const page_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(page_r20));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r20 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r20 === ctx_r1.detailPagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r20);\n  }\n}\nfunction TemplateViewerComponent_div_15_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 157)(1, \"nav\", 158)(2, \"ul\", 159)(3, \"li\", 81)(4, \"button\", 160);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_div_31_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_15_div_31_li_6_Template, 3, 3, \"li\", 86);\n    i0.ɵɵelementStart(7, \"li\", 81)(8, \"button\", 160);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_div_31_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 88);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getDetailPageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_15_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 162);\n    i0.ɵɵelement(1, \"i\", 163);\n    i0.ɵɵelementStart(2, \"p\", 164);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 93)(2, \"div\", 94)(3, \"div\", 95)(4, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵelement(5, \"i\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 98)(7, \"h5\", 99);\n    i0.ɵɵelement(8, \"i\", 100);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, TemplateViewerComponent_div_15_p_10_Template, 2, 1, \"p\", 101);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 102)(12, \"div\", 103)(13, \"span\", 104);\n    i0.ɵɵtext(14, \"\\u9805\\u76EE\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 105);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, TemplateViewerComponent_div_15_div_17_Template, 5, 2, \"div\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 19)(19, \"div\", 20)(20, \"div\", 21)(21, \"div\", 22);\n    i0.ɵɵelement(22, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"input\", 107);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_15_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.detailSearchKeyword, $event) || (ctx_r1.detailSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function TemplateViewerComponent_div_15_Template_input_input_23_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDetailSearch());\n    })(\"keyup.enter\", function TemplateViewerComponent_div_15_Template_input_keyup_enter_23_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDetailSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, TemplateViewerComponent_div_15_div_24_Template, 3, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, TemplateViewerComponent_div_15_div_25_Template, 4, 1, \"div\", 26)(26, TemplateViewerComponent_div_15_div_26_Template, 4, 0, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 108);\n    i0.ɵɵtemplate(28, TemplateViewerComponent_div_15_div_28_Template, 2, 1, \"div\", 109)(29, TemplateViewerComponent_div_15_ng_template_29_Template, 1, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(31, TemplateViewerComponent_div_15_div_31_Template, 10, 7, \"div\", 110)(32, TemplateViewerComponent_div_15_ng_template_32_Template, 4, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const checkOldDetails_r21 = i0.ɵɵreference(30);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedTemplate.TemplateName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTemplate.Description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.currentTemplateDetailsData.length > 0 ? ctx_r1.detailPagination.totalItems : ctx_r1.currentTemplateDetails.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailSearchKeyword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailSearchKeyword && ctx_r1.detailPagination.totalItems > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailSearchKeyword && ctx_r1.detailPagination.totalItems === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetailsData.length > 0)(\"ngIfElse\", checkOldDetails_r21);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n  }\n}\nexport let TemplateViewerComponent = /*#__PURE__*/(() => {\n  class TemplateViewerComponent {\n    constructor(templateService) {\n      this.templateService = templateService;\n      this.templateType = 1; // 模板類型，1=客變需求，從父元件傳入不可異動\n      this.selectTemplate = new EventEmitter();\n      this.close = new EventEmitter(); // 關閉事件\n      // 公開Math對象供模板使用\n      this.Math = Math;\n      // 內部管理的模板資料\n      this.templates = [];\n      this.templateDetails = []; // 保留用於向後相容\n      this.selectedTemplate = null;\n      // 新的詳情資料管理\n      this.currentTemplateDetailsData = [];\n      this.detailSearchKeyword = ''; // 明細專用搜尋關鍵字\n      // 查詢功能\n      this.searchKeyword = '';\n      this.filteredTemplates = [];\n      // 分頁功能 - 模板列表\n      this.templatePagination = {\n        currentPage: 1,\n        pageSize: 10,\n        totalItems: 0,\n        totalPages: 0\n      };\n      this.paginatedTemplates = [];\n      // 分頁功能 - 模板詳情\n      this.detailPagination = {\n        currentPage: 1,\n        pageSize: 5,\n        totalItems: 0,\n        totalPages: 0\n      };\n      this.paginatedDetails = [];\n    }\n    ngOnInit() {\n      this.loadTemplates();\n      this.updateFilteredTemplates();\n    }\n    ngOnChanges() {\n      this.updateFilteredTemplates();\n    }\n    // 載入模板列表 - 使用真實的 API 調用\n    loadTemplates() {\n      // 準備 API 請求參數\n      const getTemplateListArgs = {\n        CTemplateType: this.templateType,\n        // 1=客變需求\n        PageIndex: 1,\n        // 暫時載入第一頁\n        PageSize: 100,\n        // 暫時載入較多資料\n        CTemplateName: null // 不篩選名稱\n      };\n      // 調用 GetTemplateList API\n      this.templateService.apiTemplateGetTemplateListPost$Json({\n        body: getTemplateListArgs\n      }).subscribe({\n        next: response => {\n          if (response.StatusCode === 0 && response.Entries) {\n            // API 調用成功\n            // 轉換 API 回應為內部格式\n            this.templates = response.Entries.map(item => ({\n              TemplateID: item.CTemplateId,\n              TemplateName: item.CTemplateName || '',\n              Description: item.CTemplateName || '',\n              CreateTime: item.CCreateDt,\n              UpdateTime: item.CUpdateDt,\n              Creator: item.CCreator || undefined,\n              Updator: item.CUpdator || undefined\n            }));\n            // 更新過濾列表\n            this.updateFilteredTemplates();\n            // 清空詳情資料，改為按需載入\n            this.templateDetails = [];\n            this.currentTemplateDetailsData = [];\n          } else {\n            // API 返回錯誤\n            this.templates = [];\n            this.templateDetails = [];\n            this.updateFilteredTemplates();\n          }\n        },\n        error: () => {\n          // HTTP 請求錯誤\n          this.templates = [];\n          this.templateDetails = [];\n          this.updateFilteredTemplates();\n        }\n      });\n    }\n    // 更新過濾後的模板列表\n    updateFilteredTemplates() {\n      if (!this.searchKeyword.trim()) {\n        this.filteredTemplates = [...this.templates];\n      } else {\n        const keyword = this.searchKeyword.toLowerCase();\n        this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n      }\n      this.updateTemplatePagination();\n    }\n    // 更新模板分頁\n    updateTemplatePagination() {\n      this.templatePagination.totalItems = this.filteredTemplates.length;\n      this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\n      // 確保當前頁面不超過總頁數\n      if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\n        this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\n      }\n      this.updatePaginatedTemplates();\n    }\n    // 更新分頁後的模板列表\n    updatePaginatedTemplates() {\n      const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\n      const endIndex = startIndex + this.templatePagination.pageSize;\n      this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\n    }\n    // 模板分頁導航\n    goToTemplatePage(page) {\n      if (page >= 1 && page <= this.templatePagination.totalPages) {\n        this.templatePagination.currentPage = page;\n        this.updatePaginatedTemplates();\n      }\n    }\n    // 獲取模板分頁頁碼數組\n    getTemplatePageNumbers() {\n      const pages = [];\n      const totalPages = this.templatePagination.totalPages;\n      const currentPage = this.templatePagination.currentPage;\n      // 顯示當前頁面前後各2頁\n      const startPage = Math.max(1, currentPage - 2);\n      const endPage = Math.min(totalPages, currentPage + 2);\n      for (let i = startPage; i <= endPage; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n    // 搜尋模板\n    onSearch() {\n      this.updateFilteredTemplates();\n    }\n    // 清除搜尋\n    clearSearch() {\n      this.searchKeyword = '';\n      this.updateFilteredTemplates();\n    }\n    // 查看模板\n    onSelectTemplate(template) {\n      this.selectedTemplate = template;\n      this.selectTemplate.emit(template);\n      // 按需載入模板詳情\n      if (template.TemplateID) {\n        this.loadTemplateDetails(template.TemplateID);\n      }\n      this.updateDetailPagination();\n    }\n    // 載入模板詳情 - 使用真實的 GetTemplateDetailById API\n    loadTemplateDetails(templateId, pageIndex = 1, searchKeyword) {\n      const args = {\n        templateId: templateId\n      };\n      // 調用真實的 GetTemplateDetailById API\n      this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n        body: args\n      }).subscribe({\n        next: response => {\n          if (response.StatusCode === 0 && response.Entries) {\n            // 轉換 API 回應為內部格式，並處理搜尋和分頁\n            let allDetails = response.Entries.map(item => ({\n              CTemplateDetailId: item.CTemplateDetailId || 0,\n              CTemplateId: item.CTemplateId || templateId,\n              CReleateId: item.CReleateId || 0,\n              CReleateName: item.CReleateName || '',\n              CGroupName: item.CGroupName || '',\n              // 新增群組名稱欄位\n              CSort: undefined,\n              CRemark: undefined,\n              CCreateDt: new Date().toISOString(),\n              CCreator: '系統',\n              CCategory: undefined,\n              CUnitPrice: undefined,\n              CQuantity: undefined,\n              CUnit: undefined\n            }));\n            // 前端搜尋篩選 (因為 API 不支援搜尋參數)\n            if (searchKeyword && searchKeyword.trim()) {\n              allDetails = allDetails.filter(detail => detail.CReleateName.toLowerCase().includes(searchKeyword.toLowerCase()) || detail.CGroupName && detail.CGroupName.toLowerCase().includes(searchKeyword.toLowerCase()));\n            }\n            // 前端分頁處理 (因為 API 不支援分頁參數)\n            const startIndex = (pageIndex - 1) * this.detailPagination.pageSize;\n            const endIndex = startIndex + this.detailPagination.pageSize;\n            const pagedDetails = allDetails.slice(startIndex, endIndex);\n            // 更新詳情資料和分頁資訊\n            this.currentTemplateDetailsData = pagedDetails;\n            this.detailPagination.totalItems = allDetails.length;\n            this.detailPagination.totalPages = Math.ceil(allDetails.length / this.detailPagination.pageSize);\n            this.detailPagination.currentPage = pageIndex;\n          } else {\n            this.currentTemplateDetailsData = [];\n            this.detailPagination.totalItems = 0;\n            this.detailPagination.totalPages = 0;\n            this.detailPagination.currentPage = 1;\n          }\n        },\n        error: () => {\n          this.currentTemplateDetailsData = [];\n          this.detailPagination.totalItems = 0;\n          this.detailPagination.totalPages = 0;\n          this.detailPagination.currentPage = 1;\n        }\n      });\n    }\n    // 搜尋模板詳情 (明細專用搜尋)\n    searchTemplateDetails(keyword) {\n      this.detailSearchKeyword = keyword;\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, keyword);\n      }\n    }\n    // 清除明細搜尋\n    clearDetailSearch() {\n      this.detailSearchKeyword = '';\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1);\n      }\n    }\n    // 明細搜尋輸入事件處理\n    onDetailSearchInput() {\n      // 可以在這裡添加即時搜尋邏輯，目前保持原有的 Enter 鍵搜尋方式\n    }\n    // 明細搜尋事件處理（即時搜尋）\n    onDetailSearch() {\n      if (this.selectedTemplate && this.selectedTemplate.TemplateID) {\n        this.loadTemplateDetails(this.selectedTemplate.TemplateID, 1, this.detailSearchKeyword);\n      }\n    }\n    // 更新詳情分頁 (保留向後相容)\n    updateDetailPagination() {\n      // 如果使用新的詳情資料，直接返回\n      if (this.currentTemplateDetailsData.length > 0) {\n        return;\n      }\n      // 向後相容：使用舊的詳情資料\n      const details = this.currentTemplateDetails;\n      this.detailPagination.totalItems = details.length;\n      this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\n      this.detailPagination.currentPage = 1; // 重置到第一頁\n      this.updatePaginatedDetails();\n    }\n    // 更新分頁後的詳情列表\n    updatePaginatedDetails() {\n      const details = this.currentTemplateDetails;\n      const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\n      const endIndex = startIndex + this.detailPagination.pageSize;\n      this.paginatedDetails = details.slice(startIndex, endIndex);\n    }\n    // 詳情分頁導航\n    goToDetailPage(page) {\n      if (page >= 1 && page <= this.detailPagination.totalPages) {\n        // 如果使用新的詳情資料，重新載入\n        if (this.selectedTemplate && this.selectedTemplate.TemplateID && this.currentTemplateDetailsData.length > 0) {\n          this.loadTemplateDetails(this.selectedTemplate.TemplateID, page, this.detailSearchKeyword);\n        } else {\n          // 向後相容：使用舊的分頁邏輯\n          this.detailPagination.currentPage = page;\n          this.updatePaginatedDetails();\n        }\n      }\n    }\n    // 獲取詳情分頁頁碼數組\n    getDetailPageNumbers() {\n      const pages = [];\n      const totalPages = this.detailPagination.totalPages;\n      const currentPage = this.detailPagination.currentPage;\n      // 顯示當前頁面前後各2頁\n      const startPage = Math.max(1, currentPage - 2);\n      const endPage = Math.min(totalPages, currentPage + 2);\n      for (let i = startPage; i <= endPage; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n    // 關閉模板查看器\n    onClose() {\n      this.close.emit();\n    }\n    // 刪除模板\n    onDeleteTemplate(templateID) {\n      if (confirm('確定刪除此模板？')) {\n        // TODO: 替換為實際的API調用\n        this.deleteTemplateById(templateID);\n      }\n    }\n    // 刪除模板 - 使用真實的 API 調用\n    deleteTemplateById(templateID) {\n      // 準備 API 請求參數\n      const deleteTemplateArgs = {\n        CTemplateId: templateID\n      };\n      // 調用 DeleteTemplate API\n      this.templateService.apiTemplateDeleteTemplatePost$Json({\n        body: deleteTemplateArgs\n      }).subscribe({\n        next: response => {\n          if (response.StatusCode === 0) {\n            // API 調用成功\n            // 重新載入模板列表\n            this.loadTemplates();\n            // 如果當前查看的模板被刪除，關閉詳情\n            if (this.selectedTemplate?.TemplateID === templateID) {\n              this.selectedTemplate = null;\n            }\n          } else {\n            // API 返回錯誤\n          }\n        },\n        error: () => {\n          // HTTP 請求錯誤\n        }\n      });\n    }\n    /*\n     * API 設計說明：\n     *\n     * 1. 載入模板列表 API:\n     *    GET /api/Template/GetTemplateList\n     *    請求體: { CTemplateType: number, PageIndex: number, PageSize: number }\n     *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n     *\n     * 2. 創建模板 API:\n     *    POST /api/Template/SaveTemplate\n     *    請求體: {\n     *      CTemplateName: string,\n     *      CTemplateType: number,  // 1=客變需求\n     *      CStatus: number,\n     *      Details: [{\n     *        CReleateId: number,        // 關聯主檔ID\n     *        CReleateName: string       // 關聯名稱\n     *      }]\n     *    }\n     *\n     * 3. 刪除模板 API:\n     *    POST /api/Template/DeleteTemplate\n     *    請求體: { CTemplateId: number }\n     *\n     * 資料庫設計重點：\n     * - template 表：存放模板基本資訊 (CTemplateId, CTemplateName, CTemplateType, CStatus)\n     * - templatedetail 表：存放模板詳情 (CTemplateDetailId, CTemplateId, CReleateId, CReleateName)\n     * - CTemplateType 欄位用於區分不同模板類型 (1=客變需求)\n     * - CReleateId 存放關聯主檔的ID，CReleateName 存放關聯名稱\n     */\n    // 關閉模板詳情\n    closeTemplateDetail() {\n      this.selectedTemplate = null;\n    }\n    // 取得當前選中模板的詳情\n    get currentTemplateDetails() {\n      if (!this.selectedTemplate) {\n        return [];\n      }\n      return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n    }\n    // TrackBy函數用於優化ngFor性能\n    trackByTemplateId(index, template) {\n      return template.TemplateID || index;\n    }\n    static {\n      this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TemplateViewerComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TemplateViewerComponent,\n        selectors: [[\"app-template-viewer\"]],\n        inputs: {\n          templateType: \"templateType\"\n        },\n        outputs: {\n          selectTemplate: \"selectTemplate\",\n          close: \"close\"\n        },\n        standalone: true,\n        features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n        decls: 21,\n        vars: 4,\n        consts: [[\"checkOldDetails\", \"\"], [\"noDetails\", \"\"], [1, \"template-viewer-card\"], [1, \"template-viewer-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"header-title\"], [1, \"mb-0\"], [1, \"fas\", \"fa-layer-group\", \"mr-2\", \"text-primary\"], [1, \"text-muted\"], [1, \"header-actions\"], [1, \"badge\", \"badge-info\"], [1, \"template-viewer-body\"], [\"class\", \"enhanced-search-container mb-4\", 4, \"ngIf\"], [\"class\", \"template-list-container\", 4, \"ngIf\"], [\"class\", \"template-detail-view\", 4, \"ngIf\"], [1, \"template-viewer-footer\"], [1, \"footer-actions\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [1, \"enhanced-search-container\", \"mb-4\"], [1, \"search-wrapper\"], [1, \"search-input-group\"], [1, \"search-icon\"], [1, \"fas\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u63CF\\u8FF0\\u6216\\u95DC\\u9375\\u5B57...\", 1, \"search-input\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [\"class\", \"search-actions\", 4, \"ngIf\"], [\"class\", \"search-suggestions\", 4, \"ngIf\"], [\"class\", \"search-no-results\", 4, \"ngIf\"], [1, \"search-actions\"], [\"type\", \"button\", \"title\", \"\\u6E05\\u9664\\u641C\\u5C0B\", 1, \"clear-search-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"search-suggestions\"], [1, \"text-success\"], [1, \"fas\", \"fa-check-circle\", \"mr-1\"], [1, \"search-no-results\"], [1, \"text-warning\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"template-list-container\"], [\"class\", \"list-controls mb-3\", 4, \"ngIf\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"empty-state-card\", 4, \"ngIf\"], [\"class\", \"enhanced-pagination-container mt-4\", 4, \"ngIf\"], [1, \"list-controls\", \"mb-3\"], [1, \"list-info\"], [1, \"info-text\"], [1, \"view-options\"], [1, \"template-item\"], [1, \"template-main-info\"], [1, \"template-header\"], [1, \"template-label\"], [1, \"template-name\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\"], [1, \"template-meta\"], [\"class\", \"meta-row\", 4, \"ngIf\"], [1, \"template-actions\"], [\"title\", \"\\u67E5\\u770B\\u8A73\\u60C5\", 1, \"action-btn\", \"view-btn\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"action-btn delete-btn\", \"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 3, \"click\", 4, \"ngIf\"], [1, \"meta-row\"], [1, \"meta-label\"], [1, \"fas\", \"fa-calendar-plus\", \"mr-1\"], [1, \"meta-value\"], [1, \"fas\", \"fa-clock\", \"mr-1\"], [\"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 1, \"action-btn\", \"delete-btn\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"empty-state-card\"], [1, \"empty-content\"], [1, \"empty-icon\"], [\"class\", \"fas fa-search\", 4, \"ngIf\"], [\"class\", \"fas fa-folder-open\", 4, \"ngIf\"], [1, \"empty-title\"], [\"class\", \"empty-description\", 4, \"ngIf\"], [1, \"fas\", \"fa-folder-open\"], [1, \"empty-description\"], [\"href\", \"javascript:void(0)\", 1, \"clear-link\", 3, \"click\"], [1, \"enhanced-pagination-container\", \"mt-4\"], [1, \"pagination-wrapper\"], [1, \"pagination-info\"], [1, \"page-info\"], [\"aria-label\", \"\\u6A21\\u677F\\u5217\\u8868\\u5206\\u9801\", 1, \"pagination-nav\"], [1, \"enhanced-pagination\"], [1, \"page-item\"], [\"title\", \"\\u7B2C\\u4E00\\u9801\", 1, \"page-btn\", \"first-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [\"title\", \"\\u4E0A\\u4E00\\u9801\", 1, \"page-btn\", \"prev-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"\\u4E0B\\u4E00\\u9801\", 1, \"page-btn\", \"next-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [\"title\", \"\\u6700\\u5F8C\\u4E00\\u9801\", 1, \"page-btn\", \"last-page\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"page-btn\", \"page-number\", 3, \"click\"], [1, \"template-detail-view\"], [1, \"detail-header\"], [1, \"detail-title-section\"], [1, \"back-button\"], [\"title\", \"\\u8FD4\\u56DE\\u6A21\\u677F\\u5217\\u8868\", 1, \"back-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"detail-title-info\"], [1, \"detail-title\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\", \"text-primary\"], [\"class\", \"detail-subtitle\", 4, \"ngIf\"], [1, \"detail-stats\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u660E\\u7D30\\u9805\\u76EE\\u540D\\u7A31\\u3001\\u7FA4\\u7D44...\", 1, \"search-input\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [1, \"detail-content\"], [\"class\", \"enhanced-detail-list\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"detail-pagination mt-3\", 4, \"ngIf\"], [1, \"detail-subtitle\"], [1, \"enhanced-detail-list\"], [\"class\", \"enhanced-detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"enhanced-detail-item\"], [1, \"detail-item-card\"], [1, \"detail-item-header\"], [1, \"item-index\"], [1, \"index-badge\"], [1, \"item-main-info\"], [1, \"item-name\"], [1, \"fas\", \"fa-cog\", \"mr-2\", \"text-secondary\"], [1, \"item-meta\"], [1, \"meta-item\", \"id-meta\"], [1, \"fas\", \"fa-hashtag\"], [\"class\", \"meta-item group-meta\", 4, \"ngIf\"], [\"class\", \"meta-item category-meta\", 4, \"ngIf\"], [1, \"item-actions\"], [1, \"create-date\"], [\"class\", \"detail-item-body\", 4, \"ngIf\"], [1, \"meta-item\", \"group-meta\"], [1, \"fas\", \"fa-layer-group\"], [1, \"meta-item\", \"category-meta\"], [1, \"fas\", \"fa-tag\"], [1, \"detail-item-body\"], [1, \"item-details-grid\"], [\"class\", \"detail-group price-group\", 4, \"ngIf\"], [\"class\", \"detail-group quantity-group\", 4, \"ngIf\"], [\"class\", \"item-remark\", 4, \"ngIf\"], [1, \"detail-group\", \"price-group\"], [1, \"detail-label\"], [1, \"detail-value\", \"price-value\"], [1, \"detail-group\", \"quantity-group\"], [1, \"detail-value\", \"quantity-value\"], [1, \"item-remark\"], [1, \"remark-content\"], [1, \"fas\", \"fa-comment-alt\", \"mr-2\", \"text-muted\"], [1, \"remark-text\"], [\"class\", \"detail-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-list\"], [\"class\", \"detail-item d-flex align-items-center py-2 border-bottom\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\", \"d-flex\", \"align-items-center\", \"py-2\", \"border-bottom\"], [1, \"detail-index\"], [1, \"badge\", \"badge-light\"], [1, \"detail-content\", \"flex-grow-1\", \"ml-2\"], [1, \"detail-field\"], [1, \"detail-value\", \"text-muted\"], [1, \"detail-pagination\", \"mt-3\"], [\"aria-label\", \"\\u6A21\\u677F\\u8A73\\u60C5\\u5206\\u9801\"], [1, \"pagination\", \"pagination-sm\", \"justify-content-center\", \"mb-0\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"py-3\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"]],\n        template: function TemplateViewerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"h5\", 6);\n            i0.ɵɵelement(5, \"i\", 7);\n            i0.ɵɵtext(6, \"\\u6A21\\u677F\\u7BA1\\u7406 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"small\", 8);\n            i0.ɵɵtext(8, \"\\u7BA1\\u7406\\u548C\\u67E5\\u770B\\u5BA2\\u8B8A\\u9700\\u6C42\\u6A21\\u677F\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 9)(10, \"span\", 10);\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(12, \"nb-card-body\", 11);\n            i0.ɵɵtemplate(13, TemplateViewerComponent_div_13_Template, 9, 4, \"div\", 12)(14, TemplateViewerComponent_div_14_Template, 6, 5, \"div\", 13)(15, TemplateViewerComponent_div_15_Template, 34, 11, \"div\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"nb-card-footer\", 15)(17, \"div\", 16)(18, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_18_listener() {\n              return ctx.onClose();\n            });\n            i0.ɵɵelement(19, \"i\", 18);\n            i0.ɵɵtext(20, \"\\u95DC\\u9589 \");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate1(\"\", ctx.templatePagination.totalItems, \" \\u500B\\u6A21\\u677F\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.selectedTemplate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.selectedTemplate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, i2.DatePipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, NbCardModule, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardFooterComponent, i4.NbCardHeaderComponent, NbButtonModule],\n        styles: [\".template-viewer-card[_ngcontent-%COMP%]{width:90vw;max-width:1200px;height:80vh;border-radius:12px;box-shadow:0 8px 32px #ae9b6640;border:none;overflow:hidden;background:#fff}.template-viewer-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#f8f9fa);color:#2c3e50;border-bottom:2px solid #e9ecef;padding:1.5rem;box-shadow:0 2px 4px #0000000d}.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-weight:700;margin-bottom:.25rem;color:#2c3e50;font-size:1.25rem;display:flex;align-items:center;gap:.5rem}.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#007bff;font-size:1.1rem}.template-viewer-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:#6c757d;font-weight:500;font-size:.875rem}.template-viewer-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff,#0056b3);color:#fff;font-size:.875rem;font-weight:600;padding:.5rem 1rem;border-radius:8px;box-shadow:0 2px 4px #007bff33;border:none}.template-viewer-body[_ngcontent-%COMP%]{overflow:auto;padding:1.5rem;background:#fff}.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:1.5rem;box-shadow:0 2px 8px #ae9b6626}.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;background:#f8f9fa;border-radius:8px;padding:.75rem 1rem;border:2px solid transparent;transition:.3s ease}.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]:focus-within{border-color:#b8a676;background:#fff;box-shadow:0 0 0 3px #b8a6761a}.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%]{color:#6c757d;margin-right:.75rem;font-size:1rem}.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]{flex:1;border:none;background:transparent;outline:none;font-size:1rem;color:#2c3e50}.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder{color:#adb5bd}.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]{display:flex;gap:.5rem}.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-search-btn[_ngcontent-%COMP%]{background:#dc3545;color:#fff;border:none;border-radius:50%;width:24px;height:24px;display:flex;align-items:center;justify-content:center;font-size:.75rem;cursor:pointer;transition:.3s ease}.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .search-actions[_ngcontent-%COMP%]   .clear-search-btn[_ngcontent-%COMP%]:hover{background:#c82333;transform:scale(1.1)}.enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-suggestions[_ngcontent-%COMP%], .enhanced-search-container[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-no-results[_ngcontent-%COMP%]{margin-top:.75rem;padding:.5rem 0}.template-list-container[_ngcontent-%COMP%]   .list-controls[_ngcontent-%COMP%]{background:#fff;border-radius:8px;padding:1rem 1.5rem;box-shadow:0 2px 8px #0000000f}.template-list-container[_ngcontent-%COMP%]   .list-controls[_ngcontent-%COMP%]   .list-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem}.template-list-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#f8f9fa);border-radius:10px;box-shadow:0 2px 8px #00000014;transition:all .3s ease;border:1px solid #e9ecef;padding:1.25rem;display:flex;justify-content:space-between;align-items:flex-start;position:relative;overflow:hidden}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:4px;height:100%;background:linear-gradient(135deg,#007bff,#0056b3);opacity:0;transition:opacity .3s ease}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover{box-shadow:0 4px 16px #0000001f;transform:translateY(-2px);border-color:#007bff}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover:before{opacity:1}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]{flex:1;margin-right:1.5rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]{margin-bottom:.75rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]   .template-label[_ngcontent-%COMP%]{display:inline-block;background:linear-gradient(135deg,#007bff,#0056b3);color:#fff;padding:.25rem .75rem;border-radius:12px;font-size:.7rem;font-weight:600;margin-bottom:.5rem;text-transform:uppercase;letter-spacing:.5px;box-shadow:0 2px 4px #007bff33}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]{color:#2c3e50;font-weight:600;margin:0;font-size:1.1rem;display:flex;align-items:center;gap:.5rem;line-height:1.4}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#007bff;font-size:1rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.875rem;padding:.375rem 0}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%]{color:#6c757d;font-weight:500;display:flex;align-items:center;gap:.25rem;min-width:90px}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#007bff;font-size:.8rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%]{color:#495057;font-weight:500;background:#f8f9fa;padding:.25rem .75rem;border-radius:6px;font-size:.85rem;border:1px solid #e9ecef}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem;min-width:110px}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.5rem;padding:.625rem .875rem;border:none;border-radius:6px;font-size:.8rem;font-weight:500;cursor:pointer;transition:all .2s ease;white-space:nowrap;text-decoration:none}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745,#20c997);color:#fff;box-shadow:0 2px 4px #28a74533}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#218838,#1e7e34);transform:translateY(-1px);box-shadow:0 4px 8px #28a7454d}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.view-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#dc3545,#c82333);color:#fff;box-shadow:0 2px 4px #dc354533}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#c82333,#a71e2a);transform:translateY(-1px);box-shadow:0 4px 8px #dc35454d}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn.delete-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.empty-state-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 2px 12px #00000014;padding:3rem 2rem;text-align:center;border:2px dashed #dee2e6}.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:3rem;color:#adb5bd;margin-bottom:1rem}.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{color:#495057;font-weight:600;margin-bottom:.5rem}.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]{color:#6c757d;margin:0}.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]   .clear-link[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;font-weight:500}.empty-state-card[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .empty-description[_ngcontent-%COMP%]   .clear-link[_ngcontent-%COMP%]:hover{text-decoration:underline}.enhanced-pagination-container[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:1.5rem;box-shadow:0 2px 8px #ae9b6626}.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]   .page-info[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem;font-weight:500}.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]{display:flex;list-style:none;margin:0;padding:0;gap:.5rem}.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;min-width:40px;height:40px;border:none;border-radius:8px;background:#f8f9fa;color:#6c757d;font-weight:500;cursor:pointer;transition:.3s ease}.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;transform:translateY(-1px)}.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.page-number[_ngcontent-%COMP%]{font-size:.9rem}.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.first-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.last-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.prev-page[_ngcontent-%COMP%], .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-btn.next-page[_ngcontent-%COMP%]{font-size:.8rem}.enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-wrapper[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .enhanced-pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;box-shadow:0 2px 8px #b8a6764d}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#f8f9fa);border-radius:10px;padding:1.5rem;margin-bottom:1.5rem;box-shadow:0 2px 8px #00000014;border:1px solid #e9ecef}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]{display:flex;align-items:flex-start;margin-bottom:1rem}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{margin-right:1rem}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:42px;height:42px;border:none;border-radius:8px;background:linear-gradient(135deg,#6c757d,#5a6268);color:#fff;cursor:pointer;transition:all .2s ease;box-shadow:0 2px 4px #6c757d33}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#007bff,#0056b3);transform:translateY(-1px);box-shadow:0 4px 8px #007bff4d}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]{flex:1}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-title[_ngcontent-%COMP%]{color:#2c3e50;font-weight:700;margin-bottom:.5rem;font-size:1.2rem;display:flex;align-items:center;gap:.5rem}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#007bff;font-size:1.1rem}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-title-section[_ngcontent-%COMP%]   .detail-title-info[_ngcontent-%COMP%]   .detail-subtitle[_ngcontent-%COMP%]{color:#6c757d;margin:0;font-size:.9rem;font-weight:500}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]{display:flex;gap:1.5rem}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;background:#f8f9fa;padding:.75rem 1rem;border-radius:8px;border:1px solid #e9ecef;min-width:80px}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d;margin-bottom:.25rem;font-weight:600;text-transform:uppercase;letter-spacing:.5px}.template-detail-view[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:700;color:#007bff}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]{margin-bottom:1rem}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 2px 8px #ae9b6626;border:1px solid #E9ECEF;overflow:hidden;transition:.3s ease}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 16px #ae9b6633;border-color:#b8a676}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]{padding:1.25rem;display:flex;align-items:flex-start;gap:1rem;border-bottom:1px solid #F8F9FA}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-index[_ngcontent-%COMP%]   .index-badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;width:32px;height:32px;background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;border-radius:8px;font-weight:600;font-size:.875rem}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]{flex:1}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%]{color:#2c3e50;font-weight:600;margin-bottom:.75rem;font-size:1.1rem}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.75rem}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.375rem .75rem;border-radius:6px;font-size:.875rem}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.id-meta[_ngcontent-%COMP%]{background:#b8a6761a;color:#9b8a5a}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.group-meta[_ngcontent-%COMP%]{background:#b8a67626;color:#ae9b66}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item.category-meta[_ngcontent-%COMP%]{background:#b8a67633;color:#9b8a5a}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-main-info[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-header[_ngcontent-%COMP%]   .item-actions[_ngcontent-%COMP%]   .create-date[_ngcontent-%COMP%]{color:#6c757d;font-size:.875rem;background:#f8f9fa;padding:.375rem .75rem;border-radius:6px}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]{padding:1.25rem}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem;margin-bottom:1rem}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.25rem}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%]{font-size:.8rem;color:#6c757d;font-weight:500;text-transform:uppercase;letter-spacing:.5px}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%]{font-weight:600}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value.price-value[_ngcontent-%COMP%]{color:#28a745;font-size:1.1rem}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-value.quantity-value[_ngcontent-%COMP%]{color:#007bff;font-size:1rem}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%]   .remark-content[_ngcontent-%COMP%]{background:#f8f9fa;border-left:4px solid #B8A676;padding:1rem;border-radius:0 8px 8px 0}.enhanced-detail-list[_ngcontent-%COMP%]   .enhanced-detail-item[_ngcontent-%COMP%]   .detail-item-card[_ngcontent-%COMP%]   .detail-item-body[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%]   .remark-content[_ngcontent-%COMP%]   .remark-text[_ngcontent-%COMP%]{color:#2c3e50;font-style:italic;line-height:1.5}.template-viewer-footer[_ngcontent-%COMP%]{background:#f8f9fa;border-top:1px solid #E9ECEF;padding:1rem 1.5rem}.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]{display:flex;justify-content:center}.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]{background:#6c757d;color:#fff;border:none;border-radius:8px;padding:.75rem 2rem;font-weight:500;cursor:pointer;transition:.3s ease}.template-viewer-footer[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover{background:#ae9b66;transform:translateY(-1px)}nb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745,#20c997);border:none;font-weight:500;transition:all .2s ease}nb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #28a7454d}.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{box-shadow:0 2px 8px #0000001a;border-radius:8px;overflow:hidden}.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border:none;padding:.75rem 1rem;font-size:.95rem}.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{box-shadow:none;border-color:transparent}.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder{color:#999;font-style:italic}.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border:none;background:#f8f9fa;color:#6c757d;padding:.75rem 1rem;transition:all .2s ease}.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{background:#e9ecef;color:#495057}.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus{box-shadow:none}.search-results-info[_ngcontent-%COMP%]{border-left:3px solid #007bff;padding:.5rem 0 .5rem .75rem;background:#f8f9ff;border-radius:4px}.pagination-info[_ngcontent-%COMP%]{border-left:3px solid #28a745;padding:.5rem 0 .5rem .75rem;background:#f8fff8;border-radius:4px}.table-responsive[_ngcontent-%COMP%]{border-radius:8px;overflow:hidden;box-shadow:0 2px 8px #0000000d}.table[_ngcontent-%COMP%]{margin-bottom:0}.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa,#e9ecef)}.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border:none;font-weight:600;color:#495057;padding:1rem .75rem;font-size:.9rem;text-transform:uppercase;letter-spacing:.5px}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:all .2s ease}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#f8f9ff;transform:translateY(-1px);box-shadow:0 2px 8px #0000001a}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:1rem .75rem;border-color:#f0f0f0;vertical-align:middle}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333;font-weight:600}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{font-size:.9rem}.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.375rem .75rem;font-size:.875rem;border-radius:4px;font-weight:500;transition:all .2s ease}.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]{background:linear-gradient(135deg,#17a2b8,#138496);border:none}.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #17a2b84d}.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]{background:linear-gradient(135deg,#dc3545,#c82333);border:none}.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #dc35454d}.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem}.empty-state[_ngcontent-%COMP%]{padding:2rem}.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{display:block;margin:0 auto 1rem;opacity:.5}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;margin-bottom:.5rem}.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#007bff;text-decoration:none}.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.template-detail-modal[_ngcontent-%COMP%]{margin-top:1.5rem;background:#fff;border:2px solid #e9ecef;border-radius:12px;overflow:hidden;box-shadow:0 4px 20px #0000001a;animation:_ngcontent-%COMP%_slideDown .3s ease-out}@keyframes _ngcontent-%COMP%_slideDown{0%{opacity:0;transform:translateY(-20px)}to{opacity:1;transform:translateY(0)}}.template-detail-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#f8f9fa);padding:1.25rem 1.5rem;border-bottom:2px solid #e9ecef;box-shadow:0 2px 4px #0000000d}.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#2c3e50;font-weight:700;font-size:1.1rem;margin-bottom:0;display:flex;align-items:center;gap:.5rem}.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#007bff;font-size:1rem}.template-detail-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.875rem;font-weight:600;padding:.5rem 1rem;border-radius:6px;transition:all .2s ease}.template-detail-header[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#6c757d,#5a6268);border:none;color:#fff}.template-detail-header[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#5a6268,#495057);transform:translateY(-1px);box-shadow:0 2px 4px #6c757d4d}.template-detail-content[_ngcontent-%COMP%]{padding:1.5rem;max-height:400px;overflow-y:auto}.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]{background:#f8f9ff;padding:1rem;border-radius:8px;border-left:4px solid #007bff}.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#495057}.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#495057;font-weight:600;padding-bottom:.5rem;border-bottom:1px solid #e9ecef}.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#6c757d}.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{transition:all .2s ease}.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover{background:#f8f9ff;border-radius:6px;margin:0 -.5rem;padding-left:.5rem!important;padding-right:.5rem!important}.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{background:#e9ecef;color:#6c757d;font-weight:500;padding:.375rem .5rem;border-radius:50%;min-width:2rem;text-align:center}.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%]{font-size:.9rem;margin-bottom:.25rem}.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#495057}.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.4;word-break:break-word}.add-template-form[_ngcontent-%COMP%]{margin-bottom:1.5rem}.add-template-form[_ngcontent-%COMP%]   .template-form[_ngcontent-%COMP%]{background:#fff;border:1px solid #e8ecef;border-radius:12px;padding:2rem;box-shadow:0 2px 12px #00000014;transition:all .3s ease}.add-template-form[_ngcontent-%COMP%]   .template-form[_ngcontent-%COMP%]:hover{box-shadow:0 4px 20px #0000001f}.add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:1rem;margin-bottom:1.5rem}@media (max-width: 768px){.add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%]{grid-template-columns:1fr}}.add-template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{display:flex;flex-direction:column}.add-template-form[_ngcontent-%COMP%]   .input-group.full-width[_ngcontent-%COMP%]{grid-column:1/-1}.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500;color:#374151;margin-bottom:.5rem}.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%]{color:#ef4444;margin-left:.125rem}.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]{padding:.75rem 1rem;border:1px solid #d1d5db;border-radius:8px;font-size:.875rem;transition:all .2s ease;background:#fff}.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus{outline:none;border-color:#28a745;box-shadow:0 0 0 3px #28a7451a}.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]::placeholder{color:#9ca3af}.add-template-form[_ngcontent-%COMP%]   .items-selector[_ngcontent-%COMP%]{border:1px solid #d1d5db;border-radius:8px;background:#f9fafb;max-height:200px;overflow-y:auto}.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.5rem;padding:2rem;color:#6b7280;font-size:.875rem}.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#9ca3af}.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]{border-bottom:1px solid #e5e7eb}.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:last-child{border-bottom:none}.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:hover{background:#f3f4f6}.add-template-form[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:.75rem;padding:.875rem 1rem;cursor:pointer;margin:0;width:100%}.add-template-form[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]{margin:0;margin-top:.125rem;width:1rem;height:1rem;accent-color:#28a745}.add-template-form[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]{flex:1;min-width:0}.add-template-form[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500;color:#374151;margin-bottom:.25rem;line-height:1.4}.add-template-form[_ngcontent-%COMP%]   .item-desc[_ngcontent-%COMP%]{font-size:.8rem;color:#6b7280;line-height:1.3}.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:.75rem;margin-top:1.5rem;padding-top:1.5rem;border-top:1px solid #e5e7eb}.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%], .add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]{padding:.625rem 1.25rem;border-radius:8px;font-size:.875rem;font-weight:500;border:none;cursor:pointer;transition:all .2s ease;min-width:80px}.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]{background:#f3f4f6;color:#374151}.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover{background:#e5e7eb;transform:translateY(-1px)}.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745,#20c997);color:#fff}.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #28a74540}.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;transform:none;box-shadow:none}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{border:1px solid #dee2e6;color:#6c757d;padding:.375rem .75rem;font-size:.875rem;transition:all .2s ease;border-radius:4px;margin:0 .125rem}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover{background-color:#e9ecef;border-color:#adb5bd;color:#495057;transform:translateY(-1px)}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 .2rem #007bff40}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{background-color:#007bff;border-color:#007bff;color:#fff;font-weight:600;box-shadow:0 2px 4px #007bff4d}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{color:#6c757d;background-color:#fff;border-color:#dee2e6;opacity:.5;cursor:not-allowed}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover{transform:none}.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.8rem;border-radius:3px;margin:0 .0625rem}.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.detail-pagination[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{background-color:#28a745;border-color:#28a745;box-shadow:0 2px 4px #28a7454d}.detail-item[_ngcontent-%COMP%]{transition:background-color .2s}.detail-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-name[_ngcontent-%COMP%]{color:#495057;font-size:.95rem}.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-info[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;margin-bottom:.25rem}.detail-item[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .detail-main[_ngcontent-%COMP%]   .detail-remark[_ngcontent-%COMP%]{font-size:.8rem;padding:.25rem .5rem;background-color:#f8f9fa;border-radius:.25rem;border-left:3px solid #dee2e6}.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-radius:.25rem 0 0 .25rem}.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:0}.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{border-radius:0 .25rem .25rem 0}.template-detail-search[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#6c757d;border-color:#6c757d;color:#fff}@media (max-width: 768px){.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]{flex-direction:column;gap:1rem;padding:1rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]{margin-right:0}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-header[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]{font-size:1rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%]{min-width:80px;font-size:.8rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-main-info[_ngcontent-%COMP%]   .template-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%]{font-size:.8rem;padding:.2rem .5rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]{flex-direction:row;justify-content:center;min-width:auto;gap:.75rem}.template-list-container[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{flex:1;min-width:80px;padding:.5rem .75rem;font-size:.75rem}.list-controls[_ngcontent-%COMP%]{padding:.75rem 1rem!important}.list-controls[_ngcontent-%COMP%]   .list-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:.8rem}}\"]\n      });\n    }\n  }\n  return TemplateViewerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}