{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let FooterComponent = /*#__PURE__*/(() => {\n  class FooterComponent {\n    static {\n      this.ɵfac = function FooterComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FooterComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FooterComponent,\n        selectors: [[\"ngx-footer\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 0,\n        vars: 0,\n        template: function FooterComponent_Template(rf, ctx) {},\n        styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n[_nghost-%COMP%]{width:100%;display:flex;justify-content:space-between;align-items:center}[_nghost-%COMP%]   .socials[_ngcontent-%COMP%]{font-size:2rem}[_nghost-%COMP%]   .socials[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{padding:.4rem;color:var(--text-hint-color);transition:color ease-out .1s}[_nghost-%COMP%]   .socials[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:var(--text-basic-color)}@media (max-width: 575.98px){[_nghost-%COMP%]   .socials[_ngcontent-%COMP%]{font-size:1.5rem}}\"]\n      });\n    }\n  }\n  return FooterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}