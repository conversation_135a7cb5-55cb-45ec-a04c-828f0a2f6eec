{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport { tap } from 'rxjs';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"../../components/pagination/pagination.component\";\nconst _c0 = [\"createModal\"];\nconst _c1 = [\"editModal\"];\nconst _c2 = [\"templateDetailModal\"];\nfunction TemplateComponent_button_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_button_52_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const createModal_r4 = i0.ɵɵreference(75);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(createModal_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 39);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const editModal_r9 = i0.ɵɵreference(77);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(editModal_r9, template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteTemplate(template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 40);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 41)(14, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_Template_button_click_14_listener() {\n      const template_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateDetailModal_r7 = i0.ɵɵreference(79);\n      return i0.ɵɵresetView(ctx_r2.viewTemplateDetail(template_r6, templateDetailModal_r7));\n    });\n    i0.ɵɵelement(15, \"i\", 43);\n    i0.ɵɵtext(16, \"\\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, TemplateComponent_tr_70_button_17_Template, 3, 0, \"button\", 44)(18, TemplateComponent_tr_70_button_18_Template, 3, 0, \"button\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const template_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CTemplateType === 1 ? \"\\u7A7A\\u9593\\u6A21\\u677F\" : template_r6.CTemplateType === 2 ? \"\\u9805\\u76EE\\u6A21\\u677F\" : \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(template_r6.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", template_r6.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 8, template_r6.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r6.CCreator || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction TemplateComponent_tr_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 50);\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵtext(3, \"\\u76EE\\u524D\\u6C92\\u6709\\u4EFB\\u4F55\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_44_Template_div_click_0_listener() {\n      const space_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleSpaceSelection(space_r14));\n    });\n    i0.ɵɵelementStart(1, \"div\", 103)(2, \"div\", 104);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 105);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const space_r14 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", space_r14.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(space_r14.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r14.CLocation || \"-\");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \"\\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"div\", 108);\n    i0.ɵɵtext(2, \" \\u5171 \");\n    i0.ɵɵelementStart(3, \"span\", 109);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ngx-pagination\", 37);\n    i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_ng_template_74_div_46_Template_ngx_pagination_PageChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spacePageIndex, $event) || (ctx_r2.spacePageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function TemplateComponent_ng_template_74_div_46_Template_ngx_pagination_PageChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.spacePageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.spaceTotalRecords);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r2.spacePageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r2.spacePageSize)(\"CollectionSize\", ctx_r2.spaceTotalRecords);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_47_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_47_span_4_Template_button_click_2_listener() {\n      const space_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeSelectedSpace(space_r17));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", space_r17.CPart, \" \");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"label\", 110);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 111);\n    i0.ɵɵtemplate(4, TemplateComponent_ng_template_74_div_47_span_4_Template, 3, 1, \"span\", 112);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u7684\\u7A7A\\u9593 (\", ctx_r2.selectedSpacesForTemplate.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedSpacesForTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_69_Template_div_click_0_listener() {\n      const item_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleItemSelection(item_r19));\n    });\n    i0.ɵɵelementStart(1, \"div\", 103)(2, \"div\", 104);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 105);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r19 = ctx.$implicit;\n    i0.ɵɵclassProp(\"selected\", item_r19.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r19.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r19.CLocation || \"-\");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \"\\u6C92\\u6709\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"div\", 108);\n    i0.ɵɵtext(2, \" \\u5171 \");\n    i0.ɵɵelementStart(3, \"span\", 109);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ngx-pagination\", 37);\n    i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_ng_template_74_div_71_Template_ngx_pagination_PageChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.itemPageIndex, $event) || (ctx_r2.itemPageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function TemplateComponent_ng_template_74_div_71_Template_ngx_pagination_PageChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.itemPageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.itemTotalRecords);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"Page\", ctx_r2.itemPageIndex);\n    i0.ɵɵproperty(\"PageSize\", ctx_r2.itemPageSize)(\"CollectionSize\", ctx_r2.itemTotalRecords);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_div_4_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 126);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_div_4_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 127);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_div_4_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 128);\n    i0.ɵɵtext(1, \" \\u55AE\\u50F9\\u5FC5\\u9808\\u5927\\u65BC0 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_div_4_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 128);\n    i0.ɵɵtext(1, \" \\u8ACB\\u8F38\\u5165\\u5177\\u9AD4\\u7684\\u55AE\\u4F4D\\uFF0C\\u4E0D\\u80FD\\u70BA\\u7A7A\\u6216\\u9810\\u8A2D\\u503C\\u300C\\u5F0F\\u300D \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 117)(2, \"div\", 5)(3, \"span\", 118);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TemplateComponent_ng_template_74_div_72_div_4_i_5_Template, 1, 0, \"i\", 119)(6, TemplateComponent_ng_template_74_div_72_div_4_i_6_Template, 1, 0, \"i\", 120);\n    i0.ɵɵelementStart(7, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_72_div_4_Template_button_click_7_listener() {\n      const item_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeSelectedItem(item_r22));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"small\", 83);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 117)(11, \"label\", 122);\n    i0.ɵɵtext(12, \"\\u55AE\\u50F9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 123);\n    i0.ɵɵlistener(\"input\", function TemplateComponent_ng_template_74_div_72_div_4_Template_input_input_13_listener($event) {\n      const item_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.updateItemPrice(item_r22, +$event.target.value));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, TemplateComponent_ng_template_74_div_72_div_4_div_14_Template, 2, 0, \"div\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 117)(16, \"label\", 122);\n    i0.ɵɵtext(17, \"\\u55AE\\u4F4D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 125);\n    i0.ɵɵlistener(\"input\", function TemplateComponent_ng_template_74_div_72_div_4_Template_input_input_18_listener($event) {\n      const item_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.updateItemUnit(item_r22, $event.target.value));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, TemplateComponent_ng_template_74_div_72_div_4_div_19_Template, 2, 0, \"div\", 124);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r22 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"badge-success\", ctx_r2.isItemValid(item_r22))(\"badge-warning\", !ctx_r2.isItemValid(item_r22));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r22.CPart, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemValid(item_r22));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isItemValid(item_r22));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r22.CLocation || \"-\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", !item_r22.CUnitPrice || item_r22.CUnitPrice <= 0);\n    i0.ɵɵproperty(\"value\", item_r22.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r22.CUnitPrice || item_r22.CUnitPrice <= 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", !item_r22.CUnit || item_r22.CUnit.trim() === \"\" || item_r22.CUnit.trim() === \"\\u5F0F\");\n    i0.ɵɵproperty(\"value\", item_r22.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r22.CUnit || item_r22.CUnit.trim() === \"\" || item_r22.CUnit.trim() === \"\\u5F0F\");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"label\", 110);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 115);\n    i0.ɵɵtemplate(4, TemplateComponent_ng_template_74_div_72_div_4_Template, 20, 16, \"div\", 116);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u7684\\u9805\\u76EE (\", ctx_r2.selectedItemsForTemplate.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedItemsForTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 52)(1, \"nb-card-header\", 53)(2, \"h5\", 54);\n    i0.ɵɵelement(3, \"i\", 55);\n    i0.ɵɵtext(4, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_5_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 58)(8, \"div\", 59)(9, \"div\", 60)(10, \"div\", 61)(11, \"div\", 62)(12, \"label\", 63);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 64)(15, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown.control.enter\", function TemplateComponent_ng_template_74_Template_input_keydown_control_enter_15_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 60)(17, \"div\", 61)(18, \"div\", 62)(19, \"label\", 66);\n    i0.ɵɵtext(20, \" \\u6A21\\u677F\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 64)(22, \"nb-tabset\", 67)(23, \"nb-tab\", 68);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_nb_tab_click_23_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.SpaceTemplate);\n    });\n    i0.ɵɵelementStart(24, \"div\", 69)(25, \"div\", 70)(26, \"div\", 71)(27, \"input\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchKeyword, $event) || (ctx_r2.spaceSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_74_Template_input_keyup_enter_27_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 71)(29, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceSearchLocation, $event) || (ctx_r2.spaceSearchLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_74_Template_input_keyup_enter_29_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 74)(31, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceReset());\n    });\n    i0.ɵɵelement(32, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSpaceSearch());\n    });\n    i0.ɵɵelement(34, \"i\", 78);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 79)(36, \"div\", 80)(37, \"div\", 5)(38, \"input\", 81);\n    i0.ɵɵlistener(\"change\", function TemplateComponent_ng_template_74_Template_input_change_38_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAllSpaces());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"label\", 82);\n    i0.ɵɵtext(40, \"\\u5168\\u9078\\u7576\\u9801\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"small\", 83);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 84);\n    i0.ɵɵtemplate(44, TemplateComponent_ng_template_74_div_44_Template, 6, 4, \"div\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, TemplateComponent_ng_template_74_div_45_Template, 3, 0, \"div\", 86)(46, TemplateComponent_ng_template_74_div_46_Template, 7, 4, \"div\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, TemplateComponent_ng_template_74_div_47_Template, 5, 2, \"div\", 88);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"nb-tab\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_nb_tab_click_48_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.ItemTemplate);\n    });\n    i0.ɵɵelementStart(49, \"div\", 69)(50, \"div\", 70)(51, \"div\", 71)(52, \"input\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_52_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.itemSearchKeyword, $event) || (ctx_r2.itemSearchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_74_Template_input_keyup_enter_52_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 71)(54, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_54_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.itemSearchLocation, $event) || (ctx_r2.itemSearchLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_ng_template_74_Template_input_keyup_enter_54_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 74)(56, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemReset());\n    });\n    i0.ɵɵelement(57, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_58_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemSearch());\n    });\n    i0.ɵɵelement(59, \"i\", 78);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"div\", 79)(61, \"div\", 80)(62, \"div\", 5)(63, \"input\", 90);\n    i0.ɵɵlistener(\"change\", function TemplateComponent_ng_template_74_Template_input_change_63_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAllItems());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"label\", 91);\n    i0.ɵɵtext(65, \"\\u5168\\u9078\\u7576\\u9801\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"small\", 83);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 84);\n    i0.ɵɵtemplate(69, TemplateComponent_ng_template_74_div_69_Template, 6, 4, \"div\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(70, TemplateComponent_ng_template_74_div_70_Template, 3, 0, \"div\", 86)(71, TemplateComponent_ng_template_74_div_71_Template, 7, 4, \"div\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(72, TemplateComponent_ng_template_74_div_72_Template, 5, 2, \"div\", 88);\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(73, \"div\", 60)(74, \"div\", 61)(75, \"div\", 62)(76, \"label\", 92);\n    i0.ɵɵtext(77, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"div\", 64)(79, \"nb-form-field\", 93)(80, \"nb-select\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_nb_select_ngModelChange_80_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(81, \"nb-option\", 16)(82, \"span\", 5);\n    i0.ɵɵelement(83, \"i\", 95);\n    i0.ɵɵtext(84, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"nb-option\", 16)(86, \"span\", 5);\n    i0.ɵɵelement(87, \"i\", 96);\n    i0.ɵɵtext(88, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(89, \"nb-card-footer\", 97)(90, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_90_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(91, \"i\", 99);\n    i0.ɵɵtext(92, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_93_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelement(94, \"i\", 101);\n    i0.ɵɵtext(95, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceSearchLocation);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"checked\", ctx_r2.allSpacesSelected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx_r2.spaceTotalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx_r2.spacePageIndex, \" / \", ctx_r2.Math.ceil(ctx_r2.spaceTotalRecords / ctx_r2.spacePageSize), \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableSpaces);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableSpaces.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.spaceTotalRecords > ctx_r2.spacePageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedSpacesForTemplate.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.itemSearchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.itemSearchLocation);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"checked\", ctx_r2.allItemsSelected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u5171 \", ctx_r2.itemTotalRecords, \" \\u7B46\\uFF0C\\u7B2C \", ctx_r2.itemPageIndex, \" / \", ctx_r2.Math.ceil(ctx_r2.itemTotalRecords / ctx_r2.itemPageSize), \" \\u9801 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableItemsForTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableItemsForTemplate.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.itemTotalRecords > ctx_r2.itemPageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedItemsForTemplate.length > 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction TemplateComponent_ng_template_76_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 129)(1, \"nb-card-header\", 53)(2, \"h5\", 54);\n    i0.ɵɵelement(3, \"i\", 130);\n    i0.ɵɵtext(4, \"\\u7DE8\\u8F2F\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_5_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r23).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r24));\n    });\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 58)(8, \"div\", 59)(9, \"div\", 60)(10, \"div\", 61)(11, \"div\", 62)(12, \"label\", 131);\n    i0.ɵɵtext(13, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 64)(15, \"input\", 132);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_76_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 60)(17, \"div\", 61)(18, \"div\", 62)(19, \"label\", 66);\n    i0.ɵɵtext(20, \" \\u6A21\\u677F\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 64)(22, \"nb-tabset\", 67)(23, \"nb-tab\", 68);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_nb_tab_click_23_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.SpaceTemplate);\n    });\n    i0.ɵɵelementStart(24, \"span\", 133);\n    i0.ɵɵtext(25, \"\\u7A7A\\u9593\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 69)(27, \"div\", 134);\n    i0.ɵɵelement(28, \"i\", 51);\n    i0.ɵɵtext(29, \" \\u7DE8\\u8F2F\\u6A21\\u5F0F\\u4E0B\\uFF0C\\u7A7A\\u9593\\u914D\\u7F6E\\u8ACB\\u5728\\u6A21\\u677F\\u8A73\\u60C5\\u4E2D\\u9032\\u884C\\u7BA1\\u7406\\u3002 \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-tab\", 89);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_nb_tab_click_30_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.templateDetail.CTemplateType = ctx_r2.EnumTemplateType.ItemTemplate);\n    });\n    i0.ɵɵelementStart(31, \"span\", 133);\n    i0.ɵɵtext(32, \"\\u9805\\u76EE\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 69)(34, \"div\", 134);\n    i0.ɵɵelement(35, \"i\", 51);\n    i0.ɵɵtext(36, \" \\u7DE8\\u8F2F\\u6A21\\u5F0F\\u4E0B\\uFF0C\\u9805\\u76EE\\u914D\\u7F6E\\u53CA\\u55AE\\u50F9\\u55AE\\u4F4D\\u8A2D\\u5B9A\\u8ACB\\u5728\\u6A21\\u677F\\u8A73\\u60C5\\u4E2D\\u9032\\u884C\\u7BA1\\u7406\\u3002 \");\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(37, \"div\", 60)(38, \"div\", 61)(39, \"div\", 62)(40, \"label\", 135);\n    i0.ɵɵtext(41, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 64)(43, \"nb-form-field\", 93)(44, \"nb-select\", 136);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_76_Template_nb_select_ngModelChange_44_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(45, \"nb-option\", 16)(46, \"span\", 5);\n    i0.ɵɵelement(47, \"i\", 95);\n    i0.ɵɵtext(48, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"nb-option\", 16)(50, \"span\", 5);\n    i0.ɵɵelement(51, \"i\", 96);\n    i0.ɵɵtext(52, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(53, \"nb-card-footer\", 97)(54, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_54_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r23).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r24));\n    });\n    i0.ɵɵelement(55, \"i\", 99);\n    i0.ɵɵtext(56, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_57_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r23).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r24));\n    });\n    i0.ɵɵelement(58, \"i\", 137);\n    i0.ɵɵtext(59, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"active\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction TemplateComponent_ng_template_78_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 160);\n    i0.ɵɵelement(1, \"i\", 161);\n    i0.ɵɵelementStart(2, \"span\", 83);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_div_1_tr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"i\", 168);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵelement(7, \"i\", 169);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r27 = ctx.$implicit;\n    const i_r28 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r28 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r27.CPart, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r27.CLocation || \"-\", \" \");\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 163)(1, \"table\", 164)(2, \"thead\")(3, \"tr\")(4, \"th\", 165);\n    i0.ɵɵtext(5, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 166);\n    i0.ɵɵtext(7, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 167);\n    i0.ɵɵtext(9, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"tbody\");\n    i0.ɵɵtemplate(11, TemplateComponent_ng_template_78_div_70_div_1_tr_11_Template, 9, 3, \"tr\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateDetailSpaces);\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \"\\u6B64\\u6A21\\u677F\\u5C1A\\u672A\\u5305\\u542B\\u4EFB\\u4F55\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_78_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TemplateComponent_ng_template_78_div_70_div_1_Template, 12, 1, \"div\", 162)(2, TemplateComponent_ng_template_78_div_70_div_2_Template, 3, 0, \"div\", 86);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length === 0);\n  }\n}\nfunction TemplateComponent_ng_template_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 52)(1, \"nb-card-header\", 53)(2, \"h5\", 54);\n    i0.ɵɵelement(3, \"i\", 138);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u660E\\u7D30 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_78_Template_button_click_5_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r25).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r26));\n    });\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 58)(8, \"div\", 139)(9, \"div\", 140)(10, \"h6\", 141);\n    i0.ɵɵelement(11, \"i\", 142);\n    i0.ɵɵtext(12, \"\\u57FA\\u672C\\u8CC7\\u8A0A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 143)(14, \"div\", 59)(15, \"div\", 9)(16, \"div\", 144)(17, \"label\", 145);\n    i0.ɵɵelement(18, \"i\", 146);\n    i0.ɵɵtext(19, \"\\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 147);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 9)(23, \"div\", 144)(24, \"label\", 145);\n    i0.ɵɵelement(25, \"i\", 148);\n    i0.ɵɵtext(26, \"\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 147)(28, \"span\", 40);\n    i0.ɵɵelement(29, \"i\", 149);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 9)(32, \"div\", 144)(33, \"label\", 145);\n    i0.ɵɵelement(34, \"i\", 150);\n    i0.ɵɵtext(35, \"\\u5EFA\\u7ACB\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"p\", 147);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 9)(40, \"div\", 144)(41, \"label\", 145);\n    i0.ɵɵelement(42, \"i\", 151);\n    i0.ɵɵtext(43, \"\\u5EFA\\u7ACB\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 147);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 9)(47, \"div\", 144)(48, \"label\", 145);\n    i0.ɵɵelement(49, \"i\", 152);\n    i0.ɵɵtext(50, \"\\u66F4\\u65B0\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 147);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 144)(56, \"label\", 145);\n    i0.ɵɵelement(57, \"i\", 153);\n    i0.ɵɵtext(58, \"\\u66F4\\u65B0\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\", 147);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(61, \"div\", 154)(62, \"div\", 155)(63, \"h6\", 141);\n    i0.ɵɵelement(64, \"i\", 156);\n    i0.ɵɵtext(65, \"\\u5305\\u542B\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 157);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 143);\n    i0.ɵɵtemplate(69, TemplateComponent_ng_template_78_div_69_Template, 4, 0, \"div\", 158)(70, TemplateComponent_ng_template_78_div_70_Template, 3, 2, \"div\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"nb-card-footer\", 97)(72, \"button\", 159);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_78_Template_button_click_72_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r25).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r26));\n    });\n    i0.ɵɵelement(73, \"i\", 99);\n    i0.ɵɵtext(74, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CTemplateName) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"fas fa-check-circle\" : \"fas fa-times-circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(38, 12, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(53, 15, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r2.templateDetailSpaces.length, \" \\u500B\\u7A7A\\u9593\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingTemplateDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoadingTemplateDetail);\n  }\n}\nexport class TemplateComponent extends BaseComponent {\n  constructor(allow, dialogService, _templateService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._templateService = _templateService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.Math = Math; // 讓模板可以使用 Math 函數\n    this.EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\n    this.EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    // 模板相關屬性\n    this.templateList = [];\n    this.templateDetail = {};\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.searchTemplateType = null;\n    // 空間選擇相關屬性\n    this.availableSpaces = [];\n    this.selectedSpacesForTemplate = [];\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.spacePageSize = 10;\n    this.spaceTotalRecords = 0;\n    this.allSpacesSelected = false;\n    // 項目選擇相關屬性（項目模板使用空間列表作為基礎，但添加單價和單位）\n    this.availableItemsForTemplate = [];\n    this.selectedItemsForTemplate = [];\n    this.itemSearchKeyword = '';\n    this.itemSearchLocation = '';\n    this.itemPageIndex = 1;\n    this.itemPageSize = 10;\n    this.itemTotalRecords = 0;\n    this.allItemsSelected = false;\n    // 模板明細相關屬性\n    this.selectedTemplateDetail = null;\n    this.templateDetailSpaces = [];\n    this.isLoadingTemplateDetail = false;\n  }\n  ngOnInit() {\n    this.loadTemplateList();\n    this.loadAvailableSpaces();\n  }\n  // 載入模板列表\n  loadTemplateList() {\n    const request = {\n      CTemplateName: this.searchKeyword || null,\n      CStatus: this.searchStatus,\n      CTemplateType: this.searchTemplateType,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this._templateService.apiTemplateGetTemplateListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.templateList = response.Entries?.map(item => ({\n          CTemplateId: item.CTemplateId,\n          CTemplateName: item.CTemplateName,\n          CTemplateType: item.CTemplateType,\n          // 新增模板類型\n          CCreateDt: item.CCreateDt,\n          CUpdateDt: item.CUpdateDt,\n          CCreator: item.CCreator,\n          CUpdator: item.CUpdator,\n          CStatus: item.CStatus\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板列表失敗');\n      }\n    })).subscribe();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    const request = {\n      CPart: this.spaceSearchKeyword || null,\n      CLocation: this.spaceSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.spacePageIndex,\n      PageSize: this.spacePageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.spaceTotalRecords = response.TotalItems || 0;\n        this.updateAllSpacesSelectedState();\n      }\n    })).subscribe();\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  // 載入項目模板可用項目（使用空間列表作為基礎）\n  loadAvailableItemsForTemplate() {\n    const request = {\n      CPart: this.itemSearchKeyword || null,\n      CLocation: this.itemSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.itemPageIndex,\n      PageSize: this.itemPageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableItemsForTemplate = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID),\n          CUnitPrice: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnitPrice || 0,\n          CUnit: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnit || '式'\n        })) || [];\n        this.itemTotalRecords = response.TotalItems || 0;\n        this.updateAllItemsSelectedState();\n      }\n    })).subscribe();\n  }\n  // 空間搜尋功能\n  onSpaceSearch() {\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  onSpaceReset() {\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 項目搜尋功能\n  onItemSearch() {\n    this.itemPageIndex = 1;\n    this.loadAvailableItemsForTemplate();\n  }\n  onItemReset() {\n    this.itemSearchKeyword = '';\n    this.itemSearchLocation = '';\n    this.itemPageIndex = 1;\n    this.loadAvailableItemsForTemplate();\n  }\n  // 分頁功能\n  pageChanged(page) {\n    this.pageIndex = page;\n    this.loadTemplateList();\n  }\n  spacePageChanged(page) {\n    this.spacePageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  itemPageChanged(page) {\n    this.itemPageIndex = page;\n    this.loadAvailableItemsForTemplate();\n  }\n  // 模態框操作\n  openCreateModal(modal) {\n    this.templateDetail = {\n      CStatus: 1,\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\n    };\n    this.selectedSpacesForTemplate = [];\n    this.selectedItemsForTemplate = [];\n    this.loadAvailableSpaces();\n    this.loadAvailableItemsForTemplate();\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  openEditModal(modal, template) {\n    this.templateDetail = {\n      CTemplateId: template.CTemplateId,\n      CTemplateName: template.CTemplateName,\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\n      CStatus: template.CStatus || 1\n    };\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onSubmit(ref) {\n    if (!this.validateTemplateForm()) {\n      return;\n    }\n    if (this.templateDetail.CTemplateId) {\n      this.updateTemplate(ref);\n    } else {\n      this.createTemplate(ref);\n    }\n  }\n  // 驗證表單\n  validateTemplateForm() {\n    if (!this.templateDetail.CTemplateName?.trim()) {\n      this.message.showErrorMSG('請輸入模板名稱');\n      return false;\n    }\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\n      this.message.showErrorMSG('請選擇模板類型');\n      return false;\n    }\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\n      this.message.showErrorMSG('請選擇模板狀態');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && this.selectedItemsForTemplate.length === 0) {\n      this.message.showErrorMSG('項目模板請至少選擇一個項目');\n      return false;\n    }\n    // 驗證項目模板的單價和單位\n    if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n      for (const item of this.selectedItemsForTemplate) {\n        // 檢核單價\n        if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須大於0，請重新輸入`);\n          return false;\n        }\n        // 檢核單位\n        if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\n          this.message.showErrorMSG(`項目「${item.CPart}」請輸入具體的單位（不能為空或預設值「式」）`);\n          return false;\n        }\n        // 檢核單價是否為有效數字\n        if (isNaN(item.CUnitPrice)) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須為有效數字`);\n          return false;\n        }\n        // 檢核單位長度\n        if (item.CUnit.trim().length > 10) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單位長度不能超過10個字元`);\n          return false;\n        }\n      }\n    }\n    return true;\n  }\n  // 建立模板\n  createTemplate(ref) {\n    const templateData = {\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0 && response.Entries) {\n        const templateId = parseInt(response.Entries, 10);\n        this.saveTemplateDetails(templateId, ref);\n      } else {\n        this.message.showErrorMSG(response.Message || '建立模板失敗');\n      }\n    })).subscribe();\n  }\n  // 更新模板\n  updateTemplate(ref) {\n    const templateData = {\n      CTemplateId: this.templateDetail.CTemplateId,\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.message.showSucessMSG('更新模板成功');\n        ref.close();\n        this.loadTemplateList();\n      } else {\n        this.message.showErrorMSG(response.Message || '更新模板失敗');\n      }\n    })).subscribe();\n  }\n  // 儲存模板詳細資料（關聯空間或項目）\n  saveTemplateDetails(templateId, ref) {\n    let details = [];\n    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\n      // 空間模板的詳細資料\n      details = this.selectedSpacesForTemplate.map(space => ({\n        CTemplateDetailId: null,\n        CReleateId: space.CSpaceID,\n        CPart: space.CPart,\n        CLocation: space.CLocation\n      }));\n    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n      // 項目模板的詳細資料，包含單價和單位\n      details = this.selectedItemsForTemplate.map(item => ({\n        CTemplateDetailId: null,\n        CReleateId: item.CSpaceID,\n        CPart: item.CPart,\n        CLocation: item.CLocation,\n        CUnitPrice: item.CUnitPrice,\n        CUnit: item.CUnit\n      }));\n    }\n    if (details.length > 0) {\n      const templateData = {\n        CTemplateId: templateId,\n        CTemplateName: this.templateDetail.CTemplateName,\n        CTemplateType: this.templateDetail.CTemplateType,\n        CStatus: this.templateDetail.CStatus,\n        Details: details\n      };\n      this._templateService.apiTemplateSaveTemplatePost$Json({\n        body: templateData\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('建立模板成功');\n          ref.close();\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '保存模板詳細資料失敗');\n        }\n      })).subscribe();\n    } else {\n      this.message.showSucessMSG('建立模板成功');\n      ref.close();\n      this.loadTemplateList();\n    }\n  }\n  // 刪除模板\n  deleteTemplate(template) {\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\n        body: {\n          CTemplateId: template.CTemplateId\n        }\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('刪除模板成功');\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '刪除模板失敗');\n        }\n      })).subscribe();\n    }\n  }\n  // 查看模板明細\n  viewTemplateDetail(template, modal) {\n    this.selectedTemplateDetail = template;\n    this.isLoadingTemplateDetail = true;\n    this.templateDetailSpaces = [];\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n    const request = {\n      templateId: template.CTemplateId\n    };\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      this.isLoadingTemplateDetail = false;\n      if (response.StatusCode === 0) {\n        this.templateDetailSpaces = response.Entries?.map(item => ({\n          CReleateId: item.CReleateId,\n          CPart: item.CPart,\n          CLocation: item.CLocation\n        })) || [];\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板明細失敗');\n      }\n    })).subscribe();\n  }\n  // 空間選擇相關方法\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n        this.selectedSpacesForTemplate.push({\n          ...space\n        });\n      }\n    } else {\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  toggleAllSpaces() {\n    this.allSpacesSelected = !this.allSpacesSelected;\n    this.availableSpaces.forEach(space => {\n      space.selected = this.allSpacesSelected;\n      if (this.allSpacesSelected) {\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else {\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n  }\n  removeSelectedSpace(space) {\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  updateAllSpacesSelectedState() {\n    this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  // 項目選擇相關方法\n  toggleItemSelection(item) {\n    item.selected = !item.selected;\n    if (item.selected) {\n      if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\n        this.selectedItemsForTemplate.push({\n          ...item\n        });\n      }\n    } else {\n      this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n    }\n    this.updateAllItemsSelectedState();\n  }\n  toggleAllItems() {\n    this.allItemsSelected = !this.allItemsSelected;\n    this.availableItemsForTemplate.forEach(item => {\n      item.selected = this.allItemsSelected;\n      if (this.allItemsSelected) {\n        if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\n          this.selectedItemsForTemplate.push({\n            ...item\n          });\n        }\n      } else {\n        this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n      }\n    });\n  }\n  removeSelectedItem(item) {\n    this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.selected = false;\n    }\n    this.updateAllItemsSelectedState();\n  }\n  updateAllItemsSelectedState() {\n    this.allItemsSelected = this.availableItemsForTemplate.length > 0 && this.availableItemsForTemplate.every(item => item.selected);\n  }\n  // 更新選中項目的單價和單位\n  updateItemPrice(item, price) {\n    // 確保價格為有效數字且大於0\n    if (isNaN(price) || price < 0) {\n      price = 0;\n    }\n    item.CUnitPrice = price;\n    // 同步更新 availableItemsForTemplate 中對應項目的單價\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.CUnitPrice = price;\n    }\n  }\n  updateItemUnit(item, unit) {\n    // 清理單位字串\n    unit = unit ? unit.trim() : '';\n    item.CUnit = unit;\n    // 同步更新 availableItemsForTemplate 中對應項目的單位\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.CUnit = unit;\n    }\n  }\n  // 檢查項目是否有效（用於UI顯示）\n  isItemValid(item) {\n    return !!(item.CUnitPrice && item.CUnitPrice > 0 && item.CUnit && item.CUnit.trim() !== '' && item.CUnit.trim() !== '式');\n  }\n  static {\n    this.ɵfac = function TemplateComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateComponent,\n      selectors: [[\"ngx-template\"]],\n      viewQuery: function TemplateComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.createModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateDetailModal = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 80,\n      vars: 15,\n      consts: [[\"createModal\", \"\"], [\"editModal\", \"\"], [\"templateDetailModal\", \"\"], [\"accent\", \"success\"], [1, \"alert\", \"alert-info\", \"mb-4\", 2, \"border-left\", \"4px solid #4a90e2\", \"background-color\", \"#f8f9ff\", \"border-radius\", \"6px\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-3\", 2, \"font-size\", \"1.2rem\"], [1, \"mb-0\", \"text-muted\", 2, \"font-size\", \"0.9rem\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"templateName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"col-3\"], [\"id\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\"], [\"for\", \"templateType\", 1, \"label\", \"col-3\"], [\"id\", \"templateType\", \"placeholder\", \"\\u9078\\u64C7\\u6A21\\u677F\\u985E\\u578B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"800px\"], [\"scope\", \"col\", 2, \"width\", \"120px\"], [\"scope\", \"col\", 2, \"width\", \"200px\"], [\"scope\", \"col\", 2, \"width\", \"100px\"], [\"scope\", \"col\", 2, \"width\", \"180px\"], [\"scope\", \"col\", 2, \"width\", \"140px\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"badge\", 3, \"ngClass\"], [1, \"table-actions\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-outline-warning btn-sm me-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-warning\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"5\", 1, \"text-muted\", \"py-4\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"border-bottom\", \"py-3\", \"px-4\"], [1, \"mb-0\", \"text-primary\", \"font-weight-bold\"], [1, \"fas\", \"fa-plus-circle\", \"me-2\", \"text-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-light\", \"btn-sm\", \"rounded-circle\", 2, \"width\", \"32px\", \"height\", \"32px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"px-4\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"form-group\", \"mb-4\"], [1, \"d-flex\", \"align-items-start\"], [\"for\", \"templateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"flex-grow-1\", \"ml-3\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"templateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"keydown.control.enter\", \"ngModel\"], [1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"template-type-tabs\"], [\"tabTitle\", \"\\u7A7A\\u9593\\u6A21\\u677F\", 3, \"click\", \"active\"], [1, \"mt-3\"], [1, \"row\", \"mb-3\"], [1, \"col-md-5\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"form-control\", \"form-control-sm\", 2, \"height\", \"32px\", \"border-radius\", \"4px\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"col-md-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"border\", \"rounded\", \"p-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [\"type\", \"checkbox\", \"id\", \"selectAll\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAll\", 1, \"mb-0\", \"font-weight-bold\"], [1, \"text-muted\"], [1, \"space-grid\"], [\"class\", \"space-item\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [\"class\", \"w-100 d-flex flex-column align-items-center mt-4\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [\"tabTitle\", \"\\u9805\\u76EE\\u6A21\\u677F\", 3, \"click\", \"active\"], [\"type\", \"checkbox\", \"id\", \"selectAllItems\", 1, \"me-2\", 3, \"change\", \"checked\"], [\"for\", \"selectAllItems\", 1, \"mb-0\", \"font-weight-bold\"], [\"for\", \"templateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"w-full\"], [\"id\", \"templateStatus\", \"name\", \"templateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\", \"me-2\"], [1, \"d-flex\", \"justify-content-end\", \"border-top\", \"pt-3\", \"px-4\", \"pb-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"btn\", \"btn-outline-secondary\", \"me-3\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", \"background\", \"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [1, \"space-item\", 3, \"click\"], [1, \"space-card\"], [1, \"space-name\"], [1, \"space-location\"], [1, \"text-center\", \"text-muted\", \"py-4\"], [1, \"w-100\", \"d-flex\", \"flex-column\", \"align-items-center\", \"mt-4\"], [1, \"mb-2\", \"text-secondary\", 2, \"font-size\", \"0.95rem\"], [1, \"fw-bold\", \"text-primary\"], [1, \"mb-2\", \"font-weight-bold\"], [1, \"border\", \"rounded\", \"p-2\", 2, \"max-height\", \"150px\", \"overflow-y\", \"auto\"], [\"class\", \"badge badge-primary me-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"me-1\", \"mb-1\"], [\"type\", \"button\", 1, \"btn-close\", \"btn-close-white\", \"ms-1\", 2, \"font-size\", \"0.7rem\", 3, \"click\"], [1, \"border\", \"rounded\", \"p-3\", 2, \"max-height\", \"400px\", \"overflow-y\", \"auto\"], [\"class\", \"row\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"mb-3\"], [1, \"badge\", \"me-2\"], [\"class\", \"fas fa-check-circle text-success me-1\", \"title\", \"\\u5DF2\\u5B8C\\u6210\\u8A2D\\u5B9A\", 4, \"ngIf\"], [\"class\", \"fas fa-exclamation-triangle text-warning me-1\", \"title\", \"\\u8ACB\\u5B8C\\u6210\\u55AE\\u50F9\\u548C\\u55AE\\u4F4D\\u8A2D\\u5B9A\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn-close\", \"ms-auto\", 2, \"font-size\", \"0.7rem\", 3, \"click\"], [1, \"form-label\", \"small\", \"required-field\"], [\"type\", \"number\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u50F9\", \"min\", \"0.01\", \"step\", \"0.01\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"input\", \"value\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"type\", \"text\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5177\\u9AD4\\u55AE\\u4F4D\\uFF08\\u5982\\uFF1A\\u576A\\u3001\\u7D44\\u3001\\u500B\\uFF09\", \"maxlength\", \"10\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"input\", \"value\"], [\"title\", \"\\u5DF2\\u5B8C\\u6210\\u8A2D\\u5B9A\", 1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-1\"], [\"title\", \"\\u8ACB\\u5B8C\\u6210\\u55AE\\u50F9\\u548C\\u55AE\\u4F4D\\u8A2D\\u5B9A\", 1, \"fas\", \"fa-exclamation-triangle\", \"text-warning\", \"me-1\"], [1, \"invalid-feedback\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"fas\", \"fa-edit\", \"me-2\", \"text-warning\"], [\"for\", \"editTemplateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"editTemplateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"editTemplateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"slot\", \"tabTitle\"], [1, \"alert\", \"alert-info\"], [\"for\", \"editTemplateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"id\", \"editTemplateStatus\", \"name\", \"editTemplateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-save\", \"me-1\"], [1, \"fas\", \"fa-eye\", \"me-2\", \"text-info\"], [1, \"card\", \"mb-4\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"mb-0\", \"text-dark\", \"font-weight-bold\"], [1, \"fas\", \"fa-info-circle\", \"me-2\", \"text-primary\"], [1, \"card-body\"], [1, \"form-group\", \"mb-3\"], [1, \"font-weight-bold\", \"text-muted\"], [1, \"fas\", \"fa-tag\", \"me-2\", \"text-primary\"], [1, \"mb-0\"], [1, \"fas\", \"fa-toggle-on\", \"me-2\", \"text-success\"], [1, \"me-1\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-plus\", \"me-2\", \"text-warning\"], [1, \"fas\", \"fa-calendar-edit\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-edit\", \"me-2\", \"text-warning\"], [1, \"card\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-success\"], [1, \"badge\", \"badge-info\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"text-center\", \"py-4\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"me-2\", \"text-primary\", 2, \"font-size\", \"1.2rem\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-7\"], [\"scope\", \"col\", 1, \"col-4\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\", \"text-muted\"]],\n      template: function TemplateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementStart(7, \"div\")(8, \"p\", 7);\n          i0.ɵɵtext(9, \" \\u5728\\u6B64\\u9801\\u9762\\u60A8\\u53EF\\u4EE5\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u7684\\u5404\\u500B\\u6A21\\u677F\\u8CC7\\u8A0A\\uFF0C\\u5305\\u62EC\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u6A21\\u677F\\uFF0C\\u4EE5\\u53CA\\u8A2D\\u5B9A\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u72C0\\u614B\\u548C\\u5305\\u542B\\u7684\\u7A7A\\u9593\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"label\", 11);\n          i0.ɵɵtext(14, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"nb-form-field\", 12)(16, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_Template_input_keyup_enter_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10)(19, \"label\", 14);\n          i0.ɵɵtext(20, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"nb-form-field\", 12)(22, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchStatus, $event) || (ctx.searchStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(23, \"nb-option\", 16);\n          i0.ɵɵtext(24, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nb-option\", 16);\n          i0.ɵɵtext(26, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-option\", 16);\n          i0.ɵɵtext(28, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(29, \"div\", 9)(30, \"div\", 10)(31, \"label\", 17);\n          i0.ɵɵtext(32, \"\\u6A21\\u677F\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-form-field\", 12)(34, \"nb-select\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchTemplateType, $event) || (ctx.searchTemplateType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_34_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(35, \"nb-option\", 16);\n          i0.ɵɵtext(36, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"nb-option\", 16);\n          i0.ɵɵtext(38, \"\\u7A7A\\u9593\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"nb-option\", 16);\n          i0.ɵɵtext(40, \"\\u9805\\u76EE\\u6A21\\u677F\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(41, \"div\", 9);\n          i0.ɵɵelementStart(42, \"div\", 19)(43, \"div\", 20)(44, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_44_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReset());\n          });\n          i0.ɵɵelement(45, \"i\", 22);\n          i0.ɵɵtext(46, \"\\u91CD\\u7F6E \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_47_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelement(48, \"i\", 24);\n          i0.ɵɵtext(49, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(50, \"div\", 19)(51, \"div\", 25);\n          i0.ɵɵtemplate(52, TemplateComponent_button_52_Template, 3, 0, \"button\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"div\", 27)(54, \"table\", 28)(55, \"thead\")(56, \"tr\")(57, \"th\", 29);\n          i0.ɵɵtext(58, \"\\u6A21\\u677F\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\", 30);\n          i0.ɵɵtext(60, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"th\", 31);\n          i0.ɵɵtext(62, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\", 32);\n          i0.ɵɵtext(64, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\", 29);\n          i0.ɵɵtext(66, \"\\u5EFA\\u7ACB\\u8005\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\", 33);\n          i0.ɵɵtext(68, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(69, \"tbody\");\n          i0.ɵɵtemplate(70, TemplateComponent_tr_70_Template, 19, 11, \"tr\", 34)(71, TemplateComponent_tr_71_Template, 4, 0, \"tr\", 35);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(72, \"nb-card-footer\", 36)(73, \"ngx-pagination\", 37);\n          i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_73_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_73_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(74, TemplateComponent_ng_template_74_Template, 96, 26, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(76, TemplateComponent_ng_template_76_Template, 60, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(78, TemplateComponent_ng_template_78_Template, 75, 18, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTemplateType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.templateList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.templateList.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, i6.DatePipe, SharedModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbTabsetComponent, i2.NbTabComponent, i2.NbFormFieldComponent, i8.BreadcrumbComponent, i9.PaginationComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0;\\n  border-bottom-left-radius: 0;\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--color-fg-heading);\\n}\\n\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.badge.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n.badge.badge-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.required-field[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: #dc3545;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n\\n.btn-close-white[_ngcontent-%COMP%] {\\n  filter: invert(1) grayscale(100%) brightness(200%);\\n}\\n\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset {\\n  border-bottom: 2px solid #f1f3f4;\\n  margin-bottom: 0;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab {\\n  padding: 0;\\n  margin-right: 8px;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link {\\n  padding: 14px 24px;\\n  border: none;\\n  border-radius: 8px 8px 0 0;\\n  background-color: transparent;\\n  color: #6c757d;\\n  font-weight: 500;\\n  font-size: 0.95rem;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link:hover {\\n  background-color: #f8f9fa;\\n  color: #495057;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link.active {\\n  background-color: #007bff;\\n  color: #fff;\\n  font-weight: 600;\\n  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link.active::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background-color: #007bff;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab-content {\\n  padding: 0;\\n  border: none;\\n  background: transparent;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab-content .tab-pane.active {\\n  display: block;\\n}\\n\\n.space-grid[_ngcontent-%COMP%] {\\n  display: grid !important;\\n  grid-template-columns: repeat(5, 1fr);\\n  gap: 16px;\\n  margin-bottom: 20px;\\n  width: 100%;\\n  border: 1px dashed #ccc;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: block;\\n  width: 100%;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border: 2px solid #e9ecef !important;\\n  border-radius: 12px;\\n  background-color: #fff !important;\\n  transition: all 0.3s ease;\\n  min-height: 80px;\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50 !important;\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n  margin-bottom: 4px;\\n  text-align: center;\\n  display: block;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d !important;\\n  line-height: 1.3;\\n  font-weight: 400;\\n  text-align: center;\\n  display: block;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]:hover   .space-card[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background-color: #f8f9ff;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  color: #0056b3;\\n  font-weight: 700;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]::after {\\n  content: \\\"\\u2713\\\";\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  width: 20px;\\n  height: 20px;\\n  background-color: #007bff;\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: bold;\\n}\\n\\n@media (max-width: 1200px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(4, 1fr);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 8px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "CommonModule", "SharedModule", "tap", "EnumTemplateType", "EnumTemplateTypeHelper", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateComponent_button_52_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "createModal_r4", "ɵɵreference", "ɵɵresetView", "openCreateModal", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "TemplateComponent_tr_70_button_17_Template_button_click_0_listener", "_r8", "template_r6", "$implicit", "editModal_r9", "openEditModal", "TemplateComponent_tr_70_button_18_Template_button_click_0_listener", "_r10", "deleteTemplate", "TemplateComponent_tr_70_Template_button_click_14_listener", "_r5", "templateDetailModal_r7", "viewTemplateDetail", "ɵɵtemplate", "TemplateComponent_tr_70_button_17_Template", "TemplateComponent_tr_70_button_18_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "CTemplateType", "ɵɵtextInterpolate", "CTemplateName", "ɵɵproperty", "CStatus", "ɵɵpipeBind2", "CCreateDt", "CCreator", "isUpdate", "isDelete", "TemplateComponent_ng_template_74_div_44_Template_div_click_0_listener", "space_r14", "_r13", "toggleSpaceSelection", "ɵɵclassProp", "selected", "<PERSON>art", "CLocation", "ɵɵtwoWayListener", "TemplateComponent_ng_template_74_div_46_Template_ngx_pagination_PageChange_6_listener", "$event", "_r15", "ɵɵtwoWayBindingSet", "spacePageIndex", "spacePageChanged", "spaceTotalRecords", "ɵɵtwoWayProperty", "spacePageSize", "TemplateComponent_ng_template_74_div_47_span_4_Template_button_click_2_listener", "space_r17", "_r16", "removeSelectedSpace", "TemplateComponent_ng_template_74_div_47_span_4_Template", "selectedSpacesForTemplate", "length", "TemplateComponent_ng_template_74_div_69_Template_div_click_0_listener", "item_r19", "_r18", "toggleItemSelection", "TemplateComponent_ng_template_74_div_71_Template_ngx_pagination_PageChange_6_listener", "_r20", "itemPageIndex", "itemPageChanged", "itemTotalRecords", "itemPageSize", "TemplateComponent_ng_template_74_div_72_div_4_i_5_Template", "TemplateComponent_ng_template_74_div_72_div_4_i_6_Template", "TemplateComponent_ng_template_74_div_72_div_4_Template_button_click_7_listener", "item_r22", "_r21", "removeSelectedItem", "TemplateComponent_ng_template_74_div_72_div_4_Template_input_input_13_listener", "updateItemPrice", "target", "value", "TemplateComponent_ng_template_74_div_72_div_4_div_14_Template", "TemplateComponent_ng_template_74_div_72_div_4_Template_input_input_18_listener", "updateItemUnit", "TemplateComponent_ng_template_74_div_72_div_4_div_19_Template", "isItemValid", "CUnitPrice", "CUnit", "trim", "TemplateComponent_ng_template_74_div_72_div_4_Template", "selectedItemsForTemplate", "TemplateComponent_ng_template_74_Template_button_click_5_listener", "ref_r12", "_r11", "dialogRef", "onClose", "TemplateComponent_ng_template_74_Template_input_ngModelChange_15_listener", "templateDetail", "TemplateComponent_ng_template_74_Template_input_keydown_control_enter_15_listener", "onSubmit", "TemplateComponent_ng_template_74_Template_nb_tab_click_23_listener", "SpaceTemplate", "TemplateComponent_ng_template_74_Template_input_ngModelChange_27_listener", "spaceSearchKeyword", "TemplateComponent_ng_template_74_Template_input_keyup_enter_27_listener", "onSpaceSearch", "TemplateComponent_ng_template_74_Template_input_ngModelChange_29_listener", "spaceSearchLocation", "TemplateComponent_ng_template_74_Template_input_keyup_enter_29_listener", "TemplateComponent_ng_template_74_Template_button_click_31_listener", "onSpaceReset", "TemplateComponent_ng_template_74_Template_button_click_33_listener", "TemplateComponent_ng_template_74_Template_input_change_38_listener", "toggleAllSpaces", "TemplateComponent_ng_template_74_div_44_Template", "TemplateComponent_ng_template_74_div_45_Template", "TemplateComponent_ng_template_74_div_46_Template", "TemplateComponent_ng_template_74_div_47_Template", "TemplateComponent_ng_template_74_Template_nb_tab_click_48_listener", "ItemTemplate", "TemplateComponent_ng_template_74_Template_input_ngModelChange_52_listener", "itemSearchKeyword", "TemplateComponent_ng_template_74_Template_input_keyup_enter_52_listener", "onItemSearch", "TemplateComponent_ng_template_74_Template_input_ngModelChange_54_listener", "itemSearchLocation", "TemplateComponent_ng_template_74_Template_input_keyup_enter_54_listener", "TemplateComponent_ng_template_74_Template_button_click_56_listener", "onItemReset", "TemplateComponent_ng_template_74_Template_button_click_58_listener", "TemplateComponent_ng_template_74_Template_input_change_63_listener", "toggleAllItems", "TemplateComponent_ng_template_74_div_69_Template", "TemplateComponent_ng_template_74_div_70_Template", "TemplateComponent_ng_template_74_div_71_Template", "TemplateComponent_ng_template_74_div_72_Template", "TemplateComponent_ng_template_74_Template_nb_select_ngModelChange_80_listener", "TemplateComponent_ng_template_74_Template_button_click_90_listener", "TemplateComponent_ng_template_74_Template_button_click_93_listener", "allSpacesSelected", "ɵɵtextInterpolate3", "Math", "ceil", "availableSpaces", "allItemsSelected", "availableItemsForTemplate", "TemplateComponent_ng_template_76_Template_button_click_5_listener", "ref_r24", "_r23", "TemplateComponent_ng_template_76_Template_input_ngModelChange_15_listener", "TemplateComponent_ng_template_76_Template_nb_tab_click_23_listener", "TemplateComponent_ng_template_76_Template_nb_tab_click_30_listener", "TemplateComponent_ng_template_76_Template_nb_select_ngModelChange_44_listener", "TemplateComponent_ng_template_76_Template_button_click_54_listener", "TemplateComponent_ng_template_76_Template_button_click_57_listener", "i_r28", "space_r27", "TemplateComponent_ng_template_78_div_70_div_1_tr_11_Template", "templateDetailSpaces", "TemplateComponent_ng_template_78_div_70_div_1_Template", "TemplateComponent_ng_template_78_div_70_div_2_Template", "TemplateComponent_ng_template_78_Template_button_click_5_listener", "ref_r26", "_r25", "TemplateComponent_ng_template_78_div_69_Template", "TemplateComponent_ng_template_78_div_70_Template", "TemplateComponent_ng_template_78_Template_button_click_72_listener", "selectedTemplateDetail", "ɵɵclassMap", "CUpdateDt", "CUpdator", "isLoadingTemplateDetail", "TemplateComponent", "constructor", "allow", "dialogService", "_templateService", "_spaceService", "message", "valid", "pageFirst", "pageSize", "pageIndex", "totalRecords", "templateList", "searchKeyword", "searchStatus", "searchTemplateType", "ngOnInit", "loadTemplateList", "loadAvailableSpaces", "request", "PageIndex", "PageSize", "apiTemplateGetTemplateListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "TotalItems", "showErrorMSG", "Message", "subscribe", "apiSpaceGetSpaceListPost$Json", "CSpaceID", "some", "s", "updateAllSpacesSelectedState", "onSearch", "onReset", "loadAvailableItemsForTemplate", "find", "updateAllItemsSelectedState", "pageChanged", "page", "modal", "open", "context", "autoFocus", "template", "ref", "close", "validateTemplateForm", "updateTemplate", "createTemplate", "undefined", "isNaN", "templateData", "apiTemplateSaveTemplatePost$Json", "templateId", "parseInt", "saveTemplateDetails", "showSucessMSG", "details", "space", "CTemplateDetailId", "CReleateId", "Details", "confirm", "apiTemplateDeleteTemplatePost$Json", "apiTemplateGetTemplateDetailByIdPost$Json", "push", "filter", "for<PERSON>ach", "availableSpace", "every", "availableItem", "price", "unit", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "TemplateService", "SpaceService", "i4", "MessageService", "i5", "ValidationHelper", "selectors", "viewQuery", "TemplateComponent_Query", "rf", "ctx", "TemplateComponent_Template_input_ngModelChange_16_listener", "_r1", "TemplateComponent_Template_input_keyup_enter_16_listener", "TemplateComponent_Template_nb_select_ngModelChange_22_listener", "TemplateComponent_Template_nb_select_selectedChange_22_listener", "TemplateComponent_Template_nb_select_ngModelChange_34_listener", "TemplateComponent_Template_nb_select_selected<PERSON><PERSON>e_34_listener", "TemplateComponent_Template_button_click_44_listener", "TemplateComponent_Template_button_click_47_listener", "TemplateComponent_button_52_Template", "TemplateComponent_tr_70_Template", "TemplateComponent_tr_71_Template", "TemplateComponent_Template_ngx_pagination_PageChange_73_listener", "TemplateComponent_ng_template_74_Template", "ɵɵtemplateRefExtractor", "TemplateComponent_ng_template_76_Template", "TemplateComponent_ng_template_78_Template", "isCreate", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbTabsetComponent", "NbTabComponent", "NbFormFieldComponent", "i8", "BreadcrumbComponent", "i9", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.html"], "sourcesContent": ["import { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { TemplateService, SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { tap } from 'rxjs';\r\nimport {\r\n  SaveTemplateArgs,\r\n  GetTemplateDetailByIdArgs,\r\n  TemplateDetailItem,\r\n  GetSpaceListResponse\r\n} from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\nexport interface TemplateItem {\r\n  CTemplateId: number;\r\n  CTemplateName: string;\r\n  CTemplateType?: number;\r\n  CCreateDt: string;\r\n  CUpdateDt: string;\r\n  CCreator?: string | null;\r\n  CUpdator?: string | null;\r\n  CStatus?: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpacePickListItem {\r\n  CSpaceID: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n  selected?: boolean;\r\n}\r\n\r\n// 僅用於模板明細空間顯示\r\nexport interface TemplateDetailSpaceItem {\r\n  CReleateId: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n}\r\n\r\n// 項目模板選擇項目介面（擴展空間選擇項目，添加單價和單位）\r\nexport interface ItemPickListItem extends SpacePickListItem {\r\n  CUnitPrice?: number;\r\n  CUnit?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-template',\r\n  templateUrl: './template.component.html',\r\n  styleUrls: ['./template.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent\r\n  ],\r\n})\r\nexport class TemplateComponent extends BaseComponent implements OnInit {\r\n  Math = Math; // 讓模板可以使用 Math 函數\r\n  EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\r\n  EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\r\n\r\n  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;\r\n  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;\r\n  @ViewChild('templateDetailModal', { static: false }) templateDetailModal!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _templateService: TemplateService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow);\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  // 模板相關屬性\r\n  templateList: TemplateItem[] = [];\r\n  templateDetail: SaveTemplateArgs = {};\r\n  searchKeyword: string = '';\r\n  searchStatus: number | null = null;\r\n  searchTemplateType: number | null = null;\r\n\r\n  // 空間選擇相關屬性\r\n  availableSpaces: SpacePickListItem[] = [];\r\n  selectedSpacesForTemplate: SpacePickListItem[] = [];\r\n  spaceSearchKeyword: string = '';\r\n  spaceSearchLocation: string = '';\r\n  spacePageIndex = 1;\r\n  spacePageSize = 10;\r\n  spaceTotalRecords = 0;\r\n  allSpacesSelected = false;\r\n\r\n  // 項目選擇相關屬性（項目模板使用空間列表作為基礎，但添加單價和單位）\r\n  availableItemsForTemplate: ItemPickListItem[] = [];\r\n  selectedItemsForTemplate: ItemPickListItem[] = [];\r\n  itemSearchKeyword: string = '';\r\n  itemSearchLocation: string = '';\r\n  itemPageIndex = 1;\r\n  itemPageSize = 10;\r\n  itemTotalRecords = 0;\r\n  allItemsSelected = false;\r\n\r\n  // 模板明細相關屬性\r\n  selectedTemplateDetail: TemplateItem | null = null;\r\n  templateDetailSpaces: TemplateDetailSpaceItem[] = [];\r\n  isLoadingTemplateDetail = false;\r\n\r\n  override ngOnInit(): void {\r\n    this.loadTemplateList();\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 載入模板列表\r\n  loadTemplateList(): void {\r\n    const request = {\r\n      CTemplateName: this.searchKeyword || null,\r\n      CStatus: this.searchStatus,\r\n      CTemplateType: this.searchTemplateType,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.templateList = response.Entries?.map(item => ({\r\n            CTemplateId: item.CTemplateId!,\r\n            CTemplateName: item.CTemplateName!,\r\n            CTemplateType: item.CTemplateType, // 新增模板類型\r\n            CCreateDt: item.CCreateDt!,\r\n            CUpdateDt: item.CUpdateDt!,\r\n            CCreator: item.CCreator,\r\n            CUpdator: item.CUpdator,\r\n            CStatus: item.CStatus\r\n          })) || [];\r\n          this.totalRecords = response.TotalItems || 0;\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板列表失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 載入可用空間列表\r\n  loadAvailableSpaces(): void {\r\n    const request = {\r\n      CPart: this.spaceSearchKeyword || null,\r\n      CLocation: this.spaceSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.spacePageIndex,\r\n      PageSize: this.spacePageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableSpaces = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\r\n          })) || [];\r\n          this.spaceTotalRecords = response.TotalItems || 0;\r\n          this.updateAllSpacesSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 搜尋功能\r\n  onSearch(): void {\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  onReset(): void {\r\n    this.searchKeyword = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 載入項目模板可用項目（使用空間列表作為基礎）\r\n  loadAvailableItemsForTemplate(): void {\r\n    const request = {\r\n      CPart: this.itemSearchKeyword || null,\r\n      CLocation: this.itemSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.itemPageIndex,\r\n      PageSize: this.itemPageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableItemsForTemplate = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID),\r\n            CUnitPrice: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnitPrice || 0,\r\n            CUnit: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnit || '式'\r\n          })) || [];\r\n          this.itemTotalRecords = response.TotalItems || 0;\r\n          this.updateAllItemsSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間搜尋功能\r\n  onSpaceSearch(): void {\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  onSpaceReset(): void {\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 項目搜尋功能\r\n  onItemSearch(): void {\r\n    this.itemPageIndex = 1;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  onItemReset(): void {\r\n    this.itemSearchKeyword = '';\r\n    this.itemSearchLocation = '';\r\n    this.itemPageIndex = 1;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  // 分頁功能\r\n  pageChanged(page: number): void {\r\n    this.pageIndex = page;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  spacePageChanged(page: number): void {\r\n    this.spacePageIndex = page;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  itemPageChanged(page: number): void {\r\n    this.itemPageIndex = page;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  // 模態框操作\r\n  openCreateModal(modal: TemplateRef<any>): void {\r\n    this.templateDetail = {\r\n      CStatus: 1,\r\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\r\n    };\r\n    this.selectedSpacesForTemplate = [];\r\n    this.selectedItemsForTemplate = [];\r\n    this.loadAvailableSpaces();\r\n    this.loadAvailableItemsForTemplate();\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  openEditModal(modal: TemplateRef<any>, template: TemplateItem): void {\r\n    this.templateDetail = {\r\n      CTemplateId: template.CTemplateId,\r\n      CTemplateName: template.CTemplateName,\r\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\r\n      CStatus: template.CStatus || 1\r\n    };\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  onClose(ref: any): void {\r\n    ref.close();\r\n  }\r\n\r\n  onSubmit(ref: any): void {\r\n    if (!this.validateTemplateForm()) {\r\n      return;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateId) {\r\n      this.updateTemplate(ref);\r\n    } else {\r\n      this.createTemplate(ref);\r\n    }\r\n  }\r\n\r\n  // 驗證表單\r\n  validateTemplateForm(): boolean {\r\n    if (!this.templateDetail.CTemplateName?.trim()) {\r\n      this.message.showErrorMSG('請輸入模板名稱');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\r\n      this.message.showErrorMSG('請選擇模板類型');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\r\n      this.message.showErrorMSG('請選擇模板狀態');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\r\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && this.selectedItemsForTemplate.length === 0) {\r\n      this.message.showErrorMSG('項目模板請至少選擇一個項目');\r\n      return false;\r\n    }\r\n\r\n    // 驗證項目模板的單價和單位\r\n    if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\r\n      for (const item of this.selectedItemsForTemplate) {\r\n        // 檢核單價\r\n        if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須大於0，請重新輸入`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單位\r\n        if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」請輸入具體的單位（不能為空或預設值「式」）`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單價是否為有效數字\r\n        if (isNaN(item.CUnitPrice)) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須為有效數字`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單位長度\r\n        if (item.CUnit.trim().length > 10) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單位長度不能超過10個字元`);\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 建立模板\r\n  createTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          const templateId = parseInt(response.Entries, 10);\r\n          this.saveTemplateDetails(templateId, ref);\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '建立模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 更新模板\r\n  updateTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateId: this.templateDetail.CTemplateId,\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.message.showSucessMSG('更新模板成功');\r\n          ref.close();\r\n          this.loadTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '更新模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 儲存模板詳細資料（關聯空間或項目）\r\n  saveTemplateDetails(templateId: number, ref: any): void {\r\n    let details: any[] = [];\r\n\r\n    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      // 空間模板的詳細資料\r\n      details = this.selectedSpacesForTemplate.map(space => ({\r\n        CTemplateDetailId: null,\r\n        CReleateId: space.CSpaceID,\r\n        CPart: space.CPart,\r\n        CLocation: space.CLocation\r\n      }));\r\n    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\r\n      // 項目模板的詳細資料，包含單價和單位\r\n      details = this.selectedItemsForTemplate.map(item => ({\r\n        CTemplateDetailId: null,\r\n        CReleateId: item.CSpaceID,\r\n        CPart: item.CPart,\r\n        CLocation: item.CLocation,\r\n        CUnitPrice: item.CUnitPrice,\r\n        CUnit: item.CUnit\r\n      }));\r\n    }\r\n\r\n    if (details.length > 0) {\r\n      const templateData: SaveTemplateArgs = {\r\n        CTemplateId: templateId,\r\n        CTemplateName: this.templateDetail.CTemplateName,\r\n        CTemplateType: this.templateDetail.CTemplateType,\r\n        CStatus: this.templateDetail.CStatus,\r\n        Details: details\r\n      };\r\n\r\n      this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('建立模板成功');\r\n            ref.close();\r\n            this.loadTemplateList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '保存模板詳細資料失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    } else {\r\n      this.message.showSucessMSG('建立模板成功');\r\n      ref.close();\r\n      this.loadTemplateList();\r\n    }\r\n  }\r\n\r\n  // 刪除模板\r\n  deleteTemplate(template: TemplateItem): void {\r\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\r\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\r\n        body: { CTemplateId: template.CTemplateId }\r\n      }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('刪除模板成功');\r\n            this.loadTemplateList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '刪除模板失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    }\r\n  }\r\n\r\n  // 查看模板明細\r\n  viewTemplateDetail(template: TemplateItem, modal: TemplateRef<any>): void {\r\n    this.selectedTemplateDetail = template;\r\n    this.isLoadingTemplateDetail = true;\r\n    this.templateDetailSpaces = [];\r\n\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n\r\n    const request: GetTemplateDetailByIdArgs = {\r\n      templateId: template.CTemplateId\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        this.isLoadingTemplateDetail = false;\r\n        if (response.StatusCode === 0) {\r\n          this.templateDetailSpaces = response.Entries?.map(item => ({\r\n            CReleateId: item.CReleateId!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation\r\n          })) || [];\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板明細失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間選擇相關方法\r\n  toggleSpaceSelection(space: SpacePickListItem): void {\r\n    space.selected = !space.selected;\r\n\r\n    if (space.selected) {\r\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n        this.selectedSpacesForTemplate.push({ ...space });\r\n      }\r\n    } else {\r\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  toggleAllSpaces(): void {\r\n    this.allSpacesSelected = !this.allSpacesSelected;\r\n\r\n    this.availableSpaces.forEach(space => {\r\n      space.selected = this.allSpacesSelected;\r\n      if (this.allSpacesSelected) {\r\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n          this.selectedSpacesForTemplate.push({ ...space });\r\n        }\r\n      } else {\r\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedSpace(space: SpacePickListItem): void {\r\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n\r\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\r\n    if (availableSpace) {\r\n      availableSpace.selected = false;\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  updateAllSpacesSelectedState(): void {\r\n    this.allSpacesSelected = this.availableSpaces.length > 0 &&\r\n      this.availableSpaces.every(space => space.selected);\r\n  }\r\n\r\n  // 項目選擇相關方法\r\n  toggleItemSelection(item: ItemPickListItem): void {\r\n    item.selected = !item.selected;\r\n\r\n    if (item.selected) {\r\n      if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\r\n        this.selectedItemsForTemplate.push({ ...item });\r\n      }\r\n    } else {\r\n      this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n    }\r\n\r\n    this.updateAllItemsSelectedState();\r\n  }\r\n\r\n  toggleAllItems(): void {\r\n    this.allItemsSelected = !this.allItemsSelected;\r\n\r\n    this.availableItemsForTemplate.forEach(item => {\r\n      item.selected = this.allItemsSelected;\r\n      if (this.allItemsSelected) {\r\n        if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\r\n          this.selectedItemsForTemplate.push({ ...item });\r\n        }\r\n      } else {\r\n        this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedItem(item: ItemPickListItem): void {\r\n    this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.selected = false;\r\n    }\r\n\r\n    this.updateAllItemsSelectedState();\r\n  }\r\n\r\n  updateAllItemsSelectedState(): void {\r\n    this.allItemsSelected = this.availableItemsForTemplate.length > 0 &&\r\n      this.availableItemsForTemplate.every(item => item.selected);\r\n  }\r\n\r\n  // 更新選中項目的單價和單位\r\n  updateItemPrice(item: ItemPickListItem, price: number): void {\r\n    // 確保價格為有效數字且大於0\r\n    if (isNaN(price) || price < 0) {\r\n      price = 0;\r\n    }\r\n\r\n    item.CUnitPrice = price;\r\n    // 同步更新 availableItemsForTemplate 中對應項目的單價\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.CUnitPrice = price;\r\n    }\r\n  }\r\n\r\n  updateItemUnit(item: ItemPickListItem, unit: string): void {\r\n    // 清理單位字串\r\n    unit = unit ? unit.trim() : '';\r\n\r\n    item.CUnit = unit;\r\n    // 同步更新 availableItemsForTemplate 中對應項目的單位\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.CUnit = unit;\r\n    }\r\n  }\r\n\r\n  // 檢查項目是否有效（用於UI顯示）\r\n  isItemValid(item: ItemPickListItem): boolean {\r\n    return !!(item.CUnitPrice && item.CUnitPrice > 0 &&\r\n      item.CUnit && item.CUnit.trim() !== '' && item.CUnit.trim() !== '式');\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"alert alert-info mb-4\"\r\n      style=\"border-left: 4px solid #4a90e2; background-color: #f8f9ff; border-radius: 6px;\">\r\n      <div class=\"d-flex align-items-center\">\r\n        <i class=\"fas fa-info-circle text-primary me-3\" style=\"font-size: 1.2rem;\"></i>\r\n        <div>\r\n          <p class=\"mb-0 text-muted\" style=\"font-size: 0.9rem;\">\r\n            在此頁面您可以管理系統中的各個模板資訊，包括新增、編輯、刪除模板，以及設定模板名稱、狀態和包含的空間等。\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜尋條件區域 -->\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"templateName\" class=\"label col-3\">模板名稱</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"templateName\" nbInput class=\"w-full\" placeholder=\"搜尋模板名稱...\"\r\n              [(ngModel)]=\"searchKeyword\" (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"status\" class=\"label col-3\">狀態</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"status\" placeholder=\"選擇狀態...\" [(ngModel)]=\"searchStatus\" (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">啟用</nb-option>\r\n              <nb-option [value]=\"0\">停用</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"templateType\" class=\"label col-3\">模板類型</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"templateType\" placeholder=\"選擇模板類型...\" [(ngModel)]=\"searchTemplateType\"\r\n              (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">空間模板</nb-option>\r\n              <nb-option [value]=\"2\">項目模板</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢和重置按鈕 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-outline-secondary btn-sm me-2\" (click)=\"onReset()\">\r\n            <i class=\"fas fa-undo me-1\"></i>重置\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search me-1\"></i>查詢\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openCreateModal(createModal)\">\r\n            <i class=\"fas fa-plus me-1\"></i>新增模板\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模板列表表格 -->\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table\" style=\"min-width: 800px;\">\r\n        <thead>\r\n          <tr>\r\n            <th scope=\"col\" style=\"width: 120px;\">模板類型</th>\r\n            <th scope=\"col\" style=\"width: 200px;\">模板名稱</th>\r\n            <th scope=\"col\" style=\"width: 100px;\">狀態</th>\r\n            <th scope=\"col\" style=\"width: 180px;\">建立時間</th>\r\n            <th scope=\"col\" style=\"width: 120px;\">建立者</th>\r\n            <th scope=\"col\" style=\"width: 140px;\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let template of templateList\">\r\n            <td>\r\n              {{ template.CTemplateType === 1 ? '空間模板' : (template.CTemplateType === 2 ? '項目模板' : '-') }}\r\n            </td>\r\n            <td>{{ template.CTemplateName }}</td>\r\n            <td>\r\n              <span class=\"badge\" [ngClass]=\"template.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n              </span>\r\n            </td>\r\n            <td>{{ template.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>\r\n            <td>{{ template.CCreator || '-' }}</td>\r\n            <td class=\"table-actions\">\r\n              <button class=\"btn btn-outline-info btn-sm me-1\"\r\n                (click)=\"viewTemplateDetail(template, templateDetailModal)\">\r\n                <i class=\"fas fa-eye\"></i>查看\r\n              </button>\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-warning btn-sm me-1\"\r\n                (click)=\"openEditModal(editModal, template)\">\r\n                <i class=\"fas fa-edit\"></i>編輯\r\n              </button>\r\n              <button *ngIf=\"isDelete\" class=\"btn btn-outline-danger btn-sm\" (click)=\"deleteTemplate(template)\">\r\n                <i class=\"fas fa-trash\"></i>刪除\r\n              </button>\r\n            </td>\r\n          </tr>\r\n          <tr *ngIf=\"templateList.length === 0\">\r\n            <td colspan=\"5\" class=\"text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>目前沒有任何模板\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\" [CollectionSize]=\"totalRecords\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<!-- 新增模板模態框 -->\r\n<ng-template #createModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-plus-circle me-2 text-success\"></i>新增模板\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"templateName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"templateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                  [(ngModel)]=\"templateDetail.CTemplateName\" name=\"templateName\" (keydown.control.enter)=\"onSubmit(ref)\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板類型頁簽 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板類型\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-tabset class=\"template-type-tabs\">\r\n                  <nb-tab tabTitle=\"空間模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.SpaceTemplate\">\r\n                    <!-- 空間選擇區域 -->\r\n                    <div class=\"mt-3\">\r\n                      <!-- 搜尋區域 -->\r\n                      <div class=\"row mb-3\">\r\n                        <div class=\"col-md-5\">\r\n                          <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋項目名稱...\"\r\n                            [(ngModel)]=\"spaceSearchKeyword\" (keyup.enter)=\"onSpaceSearch()\"\r\n                            style=\"height: 32px; border-radius: 4px;\" />\r\n                        </div>\r\n                        <div class=\"col-md-5\">\r\n                          <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋所屬區域...\"\r\n                            [(ngModel)]=\"spaceSearchLocation\" (keyup.enter)=\"onSpaceSearch()\"\r\n                            style=\"height: 32px; border-radius: 4px;\" />\r\n                        </div>\r\n                        <div class=\"col-md-2\">\r\n                          <button class=\"btn btn-sm btn-outline-secondary me-1\" (click)=\"onSpaceReset()\">\r\n                            <i class=\"fas fa-undo\"></i>\r\n                          </button>\r\n                          <button class=\"btn btn-sm btn-secondary\" (click)=\"onSpaceSearch()\">\r\n                            <i class=\"fas fa-search\"></i>\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 可選空間列表 -->\r\n                      <div class=\"border rounded p-3\" style=\"background-color: #f8f9fa;\">\r\n                        <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n                          <div class=\"d-flex align-items-center\">\r\n                            <input type=\"checkbox\" id=\"selectAll\" [checked]=\"allSpacesSelected\"\r\n                              (change)=\"toggleAllSpaces()\" class=\"me-2\">\r\n                            <label for=\"selectAll\" class=\"mb-0 font-weight-bold\">全選當頁空間</label>\r\n                          </div>\r\n                          <small class=\"text-muted\">\r\n                            共 {{ spaceTotalRecords }} 筆，第 {{ spacePageIndex }} / {{ Math.ceil(spaceTotalRecords /\r\n                            spacePageSize) }} 頁\r\n                          </small>\r\n                        </div>\r\n\r\n                        <!-- 空間項目網格 -->\r\n                        <div class=\"space-grid\">\r\n                          <div class=\"space-item\" *ngFor=\"let space of availableSpaces\"\r\n                            [class.selected]=\"space.selected\" (click)=\"toggleSpaceSelection(space)\">\r\n                            <div class=\"space-card\">\r\n                              <div class=\"space-name\">{{ space.CPart }}</div>\r\n                              <div class=\"space-location\">{{ space.CLocation || '-' }}</div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- 空間列表為空時的提示 -->\r\n                        <div *ngIf=\"availableSpaces.length === 0\" class=\"text-center text-muted py-4\">\r\n                          <i class=\"fas fa-info-circle me-2\"></i>沒有符合條件的空間\r\n                        </div>\r\n\r\n                        <!-- 分頁 -->\r\n                        <div class=\"w-100 d-flex flex-column align-items-center mt-4\"\r\n                          *ngIf=\"spaceTotalRecords > spacePageSize\">\r\n                          <div class=\"mb-2 text-secondary\" style=\"font-size: 0.95rem;\">\r\n                            共 <span class=\"fw-bold text-primary\">{{ spaceTotalRecords }}</span> 筆資料\r\n                          </div>\r\n                          <ngx-pagination [(Page)]=\"spacePageIndex\" [PageSize]=\"spacePageSize\"\r\n                            [CollectionSize]=\"spaceTotalRecords\" (PageChange)=\"spacePageChanged($event)\">\r\n                          </ngx-pagination>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 已選空間 -->\r\n                      <div class=\"mt-3\" *ngIf=\"selectedSpacesForTemplate.length > 0\">\r\n                        <label class=\"mb-2 font-weight-bold\">已選擇的空間 ({{ selectedSpacesForTemplate.length }})</label>\r\n                        <div class=\"border rounded p-2\" style=\"max-height: 150px; overflow-y: auto;\">\r\n                          <span class=\"badge badge-primary me-1 mb-1\" *ngFor=\"let space of selectedSpacesForTemplate\">\r\n                            {{ space.CPart }}\r\n                            <button type=\"button\" class=\"btn-close btn-close-white ms-1\"\r\n                              (click)=\"removeSelectedSpace(space)\" style=\"font-size: 0.7rem;\"></button>\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                  <nb-tab tabTitle=\"項目模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.ItemTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.ItemTemplate\">\r\n                    <!-- 項目選擇區域 -->\r\n                    <div class=\"mt-3\">\r\n                      <!-- 搜尋區域 -->\r\n                      <div class=\"row mb-3\">\r\n                        <div class=\"col-md-5\">\r\n                          <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋項目名稱...\"\r\n                            [(ngModel)]=\"itemSearchKeyword\" (keyup.enter)=\"onItemSearch()\"\r\n                            style=\"height: 32px; border-radius: 4px;\" />\r\n                        </div>\r\n                        <div class=\"col-md-5\">\r\n                          <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋所屬區域...\"\r\n                            [(ngModel)]=\"itemSearchLocation\" (keyup.enter)=\"onItemSearch()\"\r\n                            style=\"height: 32px; border-radius: 4px;\" />\r\n                        </div>\r\n                        <div class=\"col-md-2\">\r\n                          <button class=\"btn btn-sm btn-outline-secondary me-1\" (click)=\"onItemReset()\">\r\n                            <i class=\"fas fa-undo\"></i>\r\n                          </button>\r\n                          <button class=\"btn btn-sm btn-secondary\" (click)=\"onItemSearch()\">\r\n                            <i class=\"fas fa-search\"></i>\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 可選項目列表 -->\r\n                      <div class=\"border rounded p-3\" style=\"background-color: #f8f9fa;\">\r\n                        <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n                          <div class=\"d-flex align-items-center\">\r\n                            <input type=\"checkbox\" id=\"selectAllItems\" [checked]=\"allItemsSelected\"\r\n                              (change)=\"toggleAllItems()\" class=\"me-2\">\r\n                            <label for=\"selectAllItems\" class=\"mb-0 font-weight-bold\">全選當頁項目</label>\r\n                          </div>\r\n                          <small class=\"text-muted\">\r\n                            共 {{ itemTotalRecords }} 筆，第 {{ itemPageIndex }} / {{ Math.ceil(itemTotalRecords /\r\n                            itemPageSize) }} 頁\r\n                          </small>\r\n                        </div>\r\n\r\n                        <!-- 項目網格 -->\r\n                        <div class=\"space-grid\">\r\n                          <div class=\"space-item\" *ngFor=\"let item of availableItemsForTemplate\"\r\n                            [class.selected]=\"item.selected\" (click)=\"toggleItemSelection(item)\">\r\n                            <div class=\"space-card\">\r\n                              <div class=\"space-name\">{{ item.CPart }}</div>\r\n                              <div class=\"space-location\">{{ item.CLocation || '-' }}</div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- 項目列表為空時的提示 -->\r\n                        <div *ngIf=\"availableItemsForTemplate.length === 0\" class=\"text-center text-muted py-4\">\r\n                          <i class=\"fas fa-info-circle me-2\"></i>沒有符合條件的項目\r\n                        </div>\r\n\r\n                        <!-- 分頁 -->\r\n                        <div class=\"w-100 d-flex flex-column align-items-center mt-4\"\r\n                          *ngIf=\"itemTotalRecords > itemPageSize\">\r\n                          <div class=\"mb-2 text-secondary\" style=\"font-size: 0.95rem;\">\r\n                            共 <span class=\"fw-bold text-primary\">{{ itemTotalRecords }}</span> 筆資料\r\n                          </div>\r\n                          <ngx-pagination [(Page)]=\"itemPageIndex\" [PageSize]=\"itemPageSize\"\r\n                            [CollectionSize]=\"itemTotalRecords\" (PageChange)=\"itemPageChanged($event)\">\r\n                          </ngx-pagination>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 已選項目及單價單位設定 -->\r\n                      <div class=\"mt-3\" *ngIf=\"selectedItemsForTemplate.length > 0\">\r\n                        <label class=\"mb-2 font-weight-bold\">已選擇的項目 ({{ selectedItemsForTemplate.length }})</label>\r\n                        <div class=\"border rounded p-3\" style=\"max-height: 400px; overflow-y: auto;\">\r\n                          <div class=\"row\" *ngFor=\"let item of selectedItemsForTemplate; let i = index\">\r\n                            <div class=\"col-md-4 mb-3\">\r\n                              <div class=\"d-flex align-items-center\">\r\n                                <span class=\"badge me-2\" [class.badge-success]=\"isItemValid(item)\"\r\n                                  [class.badge-warning]=\"!isItemValid(item)\">\r\n                                  {{ item.CPart }}\r\n                                </span>\r\n                                <i class=\"fas fa-check-circle text-success me-1\" *ngIf=\"isItemValid(item)\"\r\n                                  title=\"已完成設定\"></i>\r\n                                <i class=\"fas fa-exclamation-triangle text-warning me-1\" *ngIf=\"!isItemValid(item)\"\r\n                                  title=\"請完成單價和單位設定\"></i>\r\n                                <button type=\"button\" class=\"btn-close ms-auto\" (click)=\"removeSelectedItem(item)\"\r\n                                  style=\"font-size: 0.7rem;\"></button>\r\n                              </div>\r\n                              <small class=\"text-muted\">{{ item.CLocation || '-' }}</small>\r\n                            </div>\r\n                            <div class=\"col-md-4 mb-3\">\r\n                              <label class=\"form-label small required-field\">單價</label>\r\n                              <input type=\"number\" class=\"form-control form-control-sm\" [value]=\"item.CUnitPrice\"\r\n                                (input)=\"updateItemPrice(item, +$any($event.target).value)\" placeholder=\"請輸入單價\"\r\n                                min=\"0.01\" step=\"0.01\" required\r\n                                [class.is-invalid]=\"!item.CUnitPrice || item.CUnitPrice <= 0\" />\r\n                              <div class=\"invalid-feedback\" *ngIf=\"!item.CUnitPrice || item.CUnitPrice <= 0\">\r\n                                單價必須大於0\r\n                              </div>\r\n                            </div>\r\n                            <div class=\"col-md-4 mb-3\">\r\n                              <label class=\"form-label small required-field\">單位</label>\r\n                              <input type=\"text\" class=\"form-control form-control-sm\" [value]=\"item.CUnit\"\r\n                                (input)=\"updateItemUnit(item, $any($event.target).value)\" placeholder=\"請輸入具體單位（如：坪、組、個）\"\r\n                                maxlength=\"10\" required\r\n                                [class.is-invalid]=\"!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式'\" />\r\n                              <div class=\"invalid-feedback\"\r\n                                *ngIf=\"!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式'\">\r\n                                請輸入具體的單位，不能為空或預設值「式」\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                </nb-tabset>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板狀態 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"templateStatus\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"templateStatus\" [(ngModel)]=\"templateDetail.CStatus\" name=\"templateStatus\"\r\n                    placeholder=\"選擇狀態\" style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-check me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 編輯模板模態框 -->\r\n<ng-template #editModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-edit me-2 text-warning\"></i>編輯模板\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"editTemplateName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"editTemplateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                  [(ngModel)]=\"templateDetail.CTemplateName\" name=\"editTemplateName\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板類型頁簽 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\" style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                模板類型\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-tabset class=\"template-type-tabs\">\r\n                  <nb-tab tabTitle=\"空間模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.SpaceTemplate\">\r\n                    <span slot=\"tabTitle\">空間模板</span>\r\n\r\n                    <!-- 編輯模式下空間模板說明 -->\r\n                    <div class=\"mt-3\">\r\n                      <div class=\"alert alert-info\">\r\n                        <i class=\"fas fa-info-circle me-2\"></i>\r\n                        編輯模式下，空間配置請在模板詳情中進行管理。\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                  <nb-tab tabTitle=\"項目模板\" [active]=\"templateDetail.CTemplateType === EnumTemplateType.ItemTemplate\"\r\n                    (click)=\"templateDetail.CTemplateType = EnumTemplateType.ItemTemplate\">\r\n                    <span slot=\"tabTitle\">項目模板</span>\r\n\r\n                    <!-- 編輯模式下項目模板說明 -->\r\n                    <div class=\"mt-3\">\r\n                      <div class=\"alert alert-info\">\r\n                        <i class=\"fas fa-info-circle me-2\"></i>\r\n                        編輯模式下，項目配置及單價單位設定請在模板詳情中進行管理。\r\n                      </div>\r\n                    </div>\r\n                  </nb-tab>\r\n                </nb-tabset>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板狀態 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"editTemplateStatus\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"editTemplateStatus\" [(ngModel)]=\"templateDetail.CStatus\" name=\"editTemplateStatus\"\r\n                    placeholder=\"選擇狀態\" style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-save me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 查看模板明細模態框 -->\r\n<ng-template #templateDetailModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-eye me-2 text-info\"></i>模板明細\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <!-- 模板基本資訊 -->\r\n      <div class=\"card mb-4\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header\" style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-info-circle me-2 text-primary\"></i>基本資訊\r\n          </h6>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div class=\"row\">\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-tag me-2 text-primary\"></i>模板名稱\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CTemplateName || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-toggle-on me-2 text-success\"></i>狀態\r\n                </label>\r\n                <p class=\"mb-0\">\r\n                  <span class=\"badge\"\r\n                    [ngClass]=\"selectedTemplateDetail?.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                    <i [class]=\"selectedTemplateDetail?.CStatus === 1 ? 'fas fa-check-circle' : 'fas fa-times-circle'\"\r\n                      class=\"me-1\"></i>\r\n                    {{ selectedTemplateDetail?.CStatus === 1 ? '啟用' : '停用' }}\r\n                  </span>\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-plus me-2 text-info\"></i>建立時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CCreateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-plus me-2 text-warning\"></i>建立者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CCreator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-edit me-2 text-info\"></i>更新時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CUpdateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-edit me-2 text-warning\"></i>更新者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CUpdator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 包含的空間列表 -->\r\n      <div class=\"card\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header d-flex justify-content-between align-items-center\"\r\n          style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-home me-2 text-success\"></i>包含的空間\r\n          </h6>\r\n          <span class=\"badge badge-info\">共 {{ templateDetailSpaces.length }} 個空間</span>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <!-- Loading 狀態 -->\r\n          <div *ngIf=\"isLoadingTemplateDetail\" class=\"text-center py-4\">\r\n            <i class=\"fas fa-spinner fa-spin me-2 text-primary\" style=\"font-size: 1.2rem;\"></i>\r\n            <span class=\"text-muted\">載入中...</span>\r\n          </div>\r\n\r\n          <!-- 空間列表 -->\r\n          <div *ngIf=\"!isLoadingTemplateDetail\">\r\n            <div class=\"table-responsive\" *ngIf=\"templateDetailSpaces.length > 0\">\r\n              <table class=\"table table-sm\">\r\n                <thead>\r\n                  <tr>\r\n                    <th scope=\"col\" class=\"col-1\">#</th>\r\n                    <th scope=\"col\" class=\"col-7\">項目名稱</th>\r\n                    <th scope=\"col\" class=\"col-4\">所屬區域</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let space of templateDetailSpaces; let i = index\">\r\n                    <td>{{ i + 1 }}</td>\r\n                    <td>\r\n                      <i class=\"fas fa-home me-2 text-muted\"></i>{{ space.CPart }}\r\n                    </td>\r\n                    <td>\r\n                      <i class=\"fas fa-map-marker-alt me-2 text-muted\"></i>{{ space.CLocation || '-' }}\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n\r\n            <!-- 沒有空間時的提示 -->\r\n            <div *ngIf=\"templateDetailSpaces.length === 0\" class=\"text-center text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>此模板尚未包含任何空間\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-secondary px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>關閉\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qCAAqC;AAEnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gCAAgC;AAO7D,SAASC,GAAG,QAAQ,MAAM;AAO1B,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;;;;;;;;;;;;;;;;;ICyDrFC,EAAA,CAAAC,cAAA,iBAAiG;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,cAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,eAAA,CAAAH,cAAA,CAA4B;IAAA,EAAC;IAC9FR,EAAA,CAAAY,SAAA,YAAgC;IAAAZ,EAAA,CAAAa,MAAA,gCAClC;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAoCLd,EAAA,CAAAC,cAAA,iBAC+C;IAA7CD,EAAA,CAAAE,UAAA,mBAAAa,mEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAY,YAAA,GAAAnB,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAc,aAAA,CAAAD,YAAA,EAAAF,WAAA,CAAkC;IAAA,EAAC;IAC5CjB,EAAA,CAAAY,SAAA,YAA2B;IAAAZ,EAAA,CAAAa,MAAA,oBAC7B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IACTd,EAAA,CAAAC,cAAA,iBAAkG;IAAnCD,EAAA,CAAAE,UAAA,mBAAAmB,mEAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,IAAA;MAAA,MAAAL,WAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiB,cAAA,CAAAN,WAAA,CAAwB;IAAA,EAAC;IAC/FjB,EAAA,CAAAY,SAAA,YAA4B;IAAAZ,EAAA,CAAAa,MAAA,oBAC9B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAtBXd,EADF,CAAAC,cAAA,SAA0C,SACpC;IACFD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEnCd,EADF,CAAAC,cAAA,SAAI,eAC2F;IAC3FD,EAAA,CAAAa,MAAA,GACF;IACFb,EADE,CAAAc,YAAA,EAAO,EACJ;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAmD;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC5Dd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,IAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAErCd,EADF,CAAAC,cAAA,cAA0B,kBAEsC;IAA5DD,EAAA,CAAAE,UAAA,mBAAAsB,0DAAA;MAAA,MAAAP,WAAA,GAAAjB,EAAA,CAAAI,aAAA,CAAAqB,GAAA,EAAAP,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAmB,sBAAA,GAAA1B,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAqB,kBAAA,CAAAV,WAAA,EAAAS,sBAAA,CAAiD;IAAA,EAAC;IAC3D1B,EAAA,CAAAY,SAAA,aAA0B;IAAAZ,EAAA,CAAAa,MAAA,qBAC5B;IAAAb,EAAA,CAAAc,YAAA,EAAS;IAKTd,EAJA,CAAA4B,UAAA,KAAAC,0CAAA,qBAC+C,KAAAC,0CAAA,qBAGmD;IAItG9B,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAvBDd,EAAA,CAAA+B,SAAA,GACF;IADE/B,EAAA,CAAAgC,kBAAA,MAAAf,WAAA,CAAAgB,aAAA,sCAAAhB,WAAA,CAAAgB,aAAA,+CACF;IACIjC,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAkC,iBAAA,CAAAjB,WAAA,CAAAkB,aAAA,CAA4B;IAEVnC,EAAA,CAAA+B,SAAA,GAAwE;IAAxE/B,EAAA,CAAAoC,UAAA,YAAAnB,WAAA,CAAAoB,OAAA,6CAAwE;IAC1FrC,EAAA,CAAA+B,SAAA,EACF;IADE/B,EAAA,CAAAgC,kBAAA,MAAAf,WAAA,CAAAoB,OAAA,8CACF;IAEErC,EAAA,CAAA+B,SAAA,GAAmD;IAAnD/B,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAsC,WAAA,QAAArB,WAAA,CAAAsB,SAAA,sBAAmD;IACnDvC,EAAA,CAAA+B,SAAA,GAA8B;IAA9B/B,EAAA,CAAAkC,iBAAA,CAAAjB,WAAA,CAAAuB,QAAA,QAA8B;IAMvBxC,EAAA,CAAA+B,SAAA,GAAc;IAAd/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAmC,QAAA,CAAc;IAIdzC,EAAA,CAAA+B,SAAA,EAAc;IAAd/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAoC,QAAA,CAAc;;;;;IAMzB1C,EADF,CAAAC,cAAA,SAAsC,aACI;IACtCD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,wDACzC;IACFb,EADE,CAAAc,YAAA,EAAK,EACF;;;;;;IA+FWd,EAAA,CAAAC,cAAA,eAC0E;IAAtCD,EAAA,CAAAE,UAAA,mBAAAyC,sEAAA;MAAA,MAAAC,SAAA,GAAA5C,EAAA,CAAAI,aAAA,CAAAyC,IAAA,EAAA3B,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAwC,oBAAA,CAAAF,SAAA,CAA2B;IAAA,EAAC;IAErE5C,EADF,CAAAC,cAAA,eAAwB,eACE;IAAAD,EAAA,CAAAa,MAAA,GAAiB;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAC/Cd,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IAE5Db,EAF4D,CAAAc,YAAA,EAAM,EAC1D,EACF;;;;IALJd,EAAA,CAAA+C,WAAA,aAAAH,SAAA,CAAAI,QAAA,CAAiC;IAEPhD,EAAA,CAAA+B,SAAA,GAAiB;IAAjB/B,EAAA,CAAAkC,iBAAA,CAAAU,SAAA,CAAAK,KAAA,CAAiB;IACbjD,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAkC,iBAAA,CAAAU,SAAA,CAAAM,SAAA,QAA4B;;;;;IAM9DlD,EAAA,CAAAC,cAAA,eAA8E;IAC5ED,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,8DACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;;IAKJd,EAFF,CAAAC,cAAA,eAC4C,eACmB;IAC3DD,EAAA,CAAAa,MAAA,eAAE;IAAAb,EAAA,CAAAC,cAAA,gBAAmC;IAAAD,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAACd,EAAA,CAAAa,MAAA,2BACtE;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,yBAC+E;IAD/DD,EAAA,CAAAmD,gBAAA,wBAAAC,sFAAAC,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAkD,cAAA,EAAAH,MAAA,MAAA/C,MAAA,CAAAkD,cAAA,GAAAH,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAyB;IACFrD,EAAA,CAAAE,UAAA,wBAAAkD,sFAAAC,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAcJ,MAAA,CAAAmD,gBAAA,CAAAJ,MAAA,CAAwB;IAAA,EAAC;IAEhFrD,EADE,CAAAc,YAAA,EAAiB,EACb;;;;IALmCd,EAAA,CAAA+B,SAAA,GAAuB;IAAvB/B,EAAA,CAAAkC,iBAAA,CAAA5B,MAAA,CAAAoD,iBAAA,CAAuB;IAE9C1D,EAAA,CAAA+B,SAAA,GAAyB;IAAzB/B,EAAA,CAAA2D,gBAAA,SAAArD,MAAA,CAAAkD,cAAA,CAAyB;IACvCxD,EADwC,CAAAoC,UAAA,aAAA9B,MAAA,CAAAsD,aAAA,CAA0B,mBAAAtD,MAAA,CAAAoD,iBAAA,CAC9B;;;;;;IAStC1D,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAa,MAAA,GACA;IAAAb,EAAA,CAAAC,cAAA,kBACkE;IAAhED,EAAA,CAAAE,UAAA,mBAAA2D,gFAAA;MAAA,MAAAC,SAAA,GAAA9D,EAAA,CAAAI,aAAA,CAAA2D,IAAA,EAAA7C,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA0D,mBAAA,CAAAF,SAAA,CAA0B;IAAA,EAAC;IACxC9D,EADoE,CAAAc,YAAA,EAAS,EACtE;;;;IAHLd,EAAA,CAAA+B,SAAA,EACA;IADA/B,EAAA,CAAAgC,kBAAA,MAAA8B,SAAA,CAAAb,KAAA,MACA;;;;;IAJJjD,EADF,CAAAC,cAAA,cAA+D,iBACxB;IAAAD,EAAA,CAAAa,MAAA,GAA+C;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC5Fd,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAA4B,UAAA,IAAAqC,uDAAA,oBAA4F;IAMhGjE,EADE,CAAAc,YAAA,EAAM,EACF;;;;IARiCd,EAAA,CAAA+B,SAAA,GAA+C;IAA/C/B,EAAA,CAAAgC,kBAAA,2CAAA1B,MAAA,CAAA4D,yBAAA,CAAAC,MAAA,MAA+C;IAEpBnE,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAA4D,yBAAA,CAA4B;;;;;;IAmD1FlE,EAAA,CAAAC,cAAA,eACuE;IAApCD,EAAA,CAAAE,UAAA,mBAAAkE,sEAAA;MAAA,MAAAC,QAAA,GAAArE,EAAA,CAAAI,aAAA,CAAAkE,IAAA,EAAApD,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiE,mBAAA,CAAAF,QAAA,CAAyB;IAAA,EAAC;IAElErE,EADF,CAAAC,cAAA,eAAwB,eACE;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAC9Cd,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IAE3Db,EAF2D,CAAAc,YAAA,EAAM,EACzD,EACF;;;;IALJd,EAAA,CAAA+C,WAAA,aAAAsB,QAAA,CAAArB,QAAA,CAAgC;IAENhD,EAAA,CAAA+B,SAAA,GAAgB;IAAhB/B,EAAA,CAAAkC,iBAAA,CAAAmC,QAAA,CAAApB,KAAA,CAAgB;IACZjD,EAAA,CAAA+B,SAAA,GAA2B;IAA3B/B,EAAA,CAAAkC,iBAAA,CAAAmC,QAAA,CAAAnB,SAAA,QAA2B;;;;;IAM7DlD,EAAA,CAAAC,cAAA,eAAwF;IACtFD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,8DACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;;IAKJd,EAFF,CAAAC,cAAA,eAC0C,eACqB;IAC3DD,EAAA,CAAAa,MAAA,eAAE;IAAAb,EAAA,CAAAC,cAAA,gBAAmC;IAAAD,EAAA,CAAAa,MAAA,GAAsB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAACd,EAAA,CAAAa,MAAA,2BACrE;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,yBAC6E;IAD7DD,EAAA,CAAAmD,gBAAA,wBAAAqB,sFAAAnB,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAqE,IAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAoE,aAAA,EAAArB,MAAA,MAAA/C,MAAA,CAAAoE,aAAA,GAAArB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAwB;IACFrD,EAAA,CAAAE,UAAA,wBAAAsE,sFAAAnB,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAqE,IAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAcJ,MAAA,CAAAqE,eAAA,CAAAtB,MAAA,CAAuB;IAAA,EAAC;IAE9ErD,EADE,CAAAc,YAAA,EAAiB,EACb;;;;IALmCd,EAAA,CAAA+B,SAAA,GAAsB;IAAtB/B,EAAA,CAAAkC,iBAAA,CAAA5B,MAAA,CAAAsE,gBAAA,CAAsB;IAE7C5E,EAAA,CAAA+B,SAAA,GAAwB;IAAxB/B,EAAA,CAAA2D,gBAAA,SAAArD,MAAA,CAAAoE,aAAA,CAAwB;IACtC1E,EADuC,CAAAoC,UAAA,aAAA9B,MAAA,CAAAuE,YAAA,CAAyB,mBAAAvE,MAAA,CAAAsE,gBAAA,CAC7B;;;;;IAgB/B5E,EAAA,CAAAY,SAAA,aACoB;;;;;IACpBZ,EAAA,CAAAY,SAAA,aACyB;;;;;IAY3BZ,EAAA,CAAAC,cAAA,eAA+E;IAC7ED,EAAA,CAAAa,MAAA,8CACF;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IAQNd,EAAA,CAAAC,cAAA,eAC+E;IAC7ED,EAAA,CAAAa,MAAA,iIACF;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;;IAhCJd,EAHN,CAAAC,cAAA,cAA8E,eACjD,aACc,gBAEQ;IAC3CD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAGPd,EAFA,CAAA4B,UAAA,IAAAkD,0DAAA,iBACgB,IAAAC,0DAAA,iBAEK;IACrB/E,EAAA,CAAAC,cAAA,kBAC6B;IADmBD,EAAA,CAAAE,UAAA,mBAAA8E,+EAAA;MAAA,MAAAC,QAAA,GAAAjF,EAAA,CAAAI,aAAA,CAAA8E,IAAA,EAAAhE,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA6E,kBAAA,CAAAF,QAAA,CAAwB;IAAA,EAAC;IAEpFjF,EAD+B,CAAAc,YAAA,EAAS,EAClC;IACNd,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IACvDb,EADuD,CAAAc,YAAA,EAAQ,EACzD;IAEJd,EADF,CAAAC,cAAA,gBAA2B,kBACsB;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACzDd,EAAA,CAAAC,cAAA,kBAGkE;IAFhED,EAAA,CAAAE,UAAA,mBAAAkF,+EAAA/B,MAAA;MAAA,MAAA4B,QAAA,GAAAjF,EAAA,CAAAI,aAAA,CAAA8E,IAAA,EAAAhE,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA+E,eAAA,CAAAJ,QAAA,GAAA5B,MAAA,CAAAiC,MAAA,CAAAC,KAAA,CAAiD;IAAA,EAAC;IAD7DvF,EAAA,CAAAc,YAAA,EAGkE;IAClEd,EAAA,CAAA4B,UAAA,KAAA4D,6DAAA,mBAA+E;IAGjFxF,EAAA,CAAAc,YAAA,EAAM;IAEJd,EADF,CAAAC,cAAA,gBAA2B,kBACsB;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACzDd,EAAA,CAAAC,cAAA,kBAG8F;IAF5FD,EAAA,CAAAE,UAAA,mBAAAuF,+EAAApC,MAAA;MAAA,MAAA4B,QAAA,GAAAjF,EAAA,CAAAI,aAAA,CAAA8E,IAAA,EAAAhE,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoF,cAAA,CAAAT,QAAA,EAAA5B,MAAA,CAAAiC,MAAA,CAAAC,KAAA,CAA+C;IAAA,EAAC;IAD3DvF,EAAA,CAAAc,YAAA,EAG8F;IAC9Fd,EAAA,CAAA4B,UAAA,KAAA+D,6DAAA,mBAC+E;IAInF3F,EADE,CAAAc,YAAA,EAAM,EACF;;;;;IAlCyBd,EAAA,CAAA+B,SAAA,GAAyC;IAChE/B,EADuB,CAAA+C,WAAA,kBAAAzC,MAAA,CAAAsF,WAAA,CAAAX,QAAA,EAAyC,mBAAA3E,MAAA,CAAAsF,WAAA,CAAAX,QAAA,EACtB;IAC1CjF,EAAA,CAAA+B,SAAA,EACF;IADE/B,EAAA,CAAAgC,kBAAA,MAAAiD,QAAA,CAAAhC,KAAA,MACF;IACkDjD,EAAA,CAAA+B,SAAA,EAAuB;IAAvB/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAsF,WAAA,CAAAX,QAAA,EAAuB;IAEfjF,EAAA,CAAA+B,SAAA,EAAwB;IAAxB/B,EAAA,CAAAoC,UAAA,UAAA9B,MAAA,CAAAsF,WAAA,CAAAX,QAAA,EAAwB;IAK1DjF,EAAA,CAAA+B,SAAA,GAA2B;IAA3B/B,EAAA,CAAAkC,iBAAA,CAAA+C,QAAA,CAAA/B,SAAA,QAA2B;IAOnDlD,EAAA,CAAA+B,SAAA,GAA6D;IAA7D/B,EAAA,CAAA+C,WAAA,gBAAAkC,QAAA,CAAAY,UAAA,IAAAZ,QAAA,CAAAY,UAAA,MAA6D;IAHL7F,EAAA,CAAAoC,UAAA,UAAA6C,QAAA,CAAAY,UAAA,CAAyB;IAIpD7F,EAAA,CAAA+B,SAAA,EAA8C;IAA9C/B,EAAA,CAAAoC,UAAA,UAAA6C,QAAA,CAAAY,UAAA,IAAAZ,QAAA,CAAAY,UAAA,MAA8C;IAS3E7F,EAAA,CAAA+B,SAAA,GAAyF;IAAzF/B,EAAA,CAAA+C,WAAA,gBAAAkC,QAAA,CAAAa,KAAA,IAAAb,QAAA,CAAAa,KAAA,CAAAC,IAAA,aAAAd,QAAA,CAAAa,KAAA,CAAAC,IAAA,gBAAyF;IAHnC/F,EAAA,CAAAoC,UAAA,UAAA6C,QAAA,CAAAa,KAAA,CAAoB;IAKzE9F,EAAA,CAAA+B,SAAA,EAA0E;IAA1E/B,EAAA,CAAAoC,UAAA,UAAA6C,QAAA,CAAAa,KAAA,IAAAb,QAAA,CAAAa,KAAA,CAAAC,IAAA,aAAAd,QAAA,CAAAa,KAAA,CAAAC,IAAA,gBAA0E;;;;;IAnCnF/F,EADF,CAAAC,cAAA,cAA8D,iBACvB;IAAAD,EAAA,CAAAa,MAAA,GAA8C;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC3Fd,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAA4B,UAAA,IAAAoE,sDAAA,qBAA8E;IAuClFhG,EADE,CAAAc,YAAA,EAAM,EACF;;;;IAzCiCd,EAAA,CAAA+B,SAAA,GAA8C;IAA9C/B,EAAA,CAAAgC,kBAAA,2CAAA1B,MAAA,CAAA2F,wBAAA,CAAA9B,MAAA,MAA8C;IAE/CnE,EAAA,CAAA+B,SAAA,GAA6B;IAA7B/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAA2F,wBAAA,CAA6B;;;;;;IA9LnFjG,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,YAAoD;IAAAZ,EAAA,CAAAa,MAAA,gCACtD;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAgG,kEAAA;MAAA,MAAAC,OAAA,GAAAnG,EAAA,CAAAI,aAAA,CAAAgG,IAAA,EAAAC,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAgG,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAE5FnG,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAQPd,EANV,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAAmD,gBAAA,2BAAAoD,0EAAAlD,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAkG,cAAA,CAAArE,aAAA,EAAAkB,MAAA,MAAA/C,MAAA,CAAAkG,cAAA,CAAArE,aAAA,GAAAkB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAA0C;IAAqBrD,EAAA,CAAAE,UAAA,mCAAAuG,kFAAA;MAAA,MAAAN,OAAA,GAAAnG,EAAA,CAAAI,aAAA,CAAAgG,IAAA,EAAAC,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAyBJ,MAAA,CAAAoG,QAAA,CAAAP,OAAA,CAAa;IAAA,EAAC;IAKhHnG,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAC4D;IAC9FD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,qBACU,kBAEsC;IAAxED,EAAA,CAAAE,UAAA,mBAAAyG,mEAAA;MAAA3G,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAAkG,cAAA,CAAAvE,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAA8G,aAAA;IAAA,EAAuE;IAMjE5G,EAJN,CAAAC,cAAA,eAAkB,eAEM,eACE,iBAG0B;IAD5CD,EAAA,CAAAmD,gBAAA,2BAAA0D,0EAAAxD,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAwG,kBAAA,EAAAzD,MAAA,MAAA/C,MAAA,CAAAwG,kBAAA,GAAAzD,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAgC;IAACrD,EAAA,CAAAE,UAAA,yBAAA6G,wEAAA;MAAA/G,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAA0G,aAAA,EAAe;IAAA,EAAC;IAEpEhH,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,eAAsB,iBAG0B;IAD5CD,EAAA,CAAAmD,gBAAA,2BAAA8D,0EAAA5D,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAA4G,mBAAA,EAAA7D,MAAA,MAAA/C,MAAA,CAAA4G,mBAAA,GAAA7D,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAiC;IAACrD,EAAA,CAAAE,UAAA,yBAAAiH,wEAAA;MAAAnH,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAA0G,aAAA,EAAe;IAAA,EAAC;IAErEhH,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,eAAsB,kBAC2D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAkH,mEAAA;MAAApH,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA+G,YAAA,EAAc;IAAA,EAAC;IAC5ErH,EAAA,CAAAY,SAAA,aAA2B;IAC7BZ,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAAmE;IAA1BD,EAAA,CAAAE,UAAA,mBAAAoH,mEAAA;MAAAtH,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA0G,aAAA,EAAe;IAAA,EAAC;IAChEhH,EAAA,CAAAY,SAAA,aAA6B;IAGnCZ,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAmE,eACG,cAC3B,iBAEO;IAA1CD,EAAA,CAAAE,UAAA,oBAAAqH,mEAAA;MAAAvH,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAAkH,eAAA,EAAiB;IAAA,EAAC;IAD9BxH,EAAA,CAAAc,YAAA,EAC4C;IAC5Cd,EAAA,CAAAC,cAAA,iBAAqD;IAAAD,EAAA,CAAAa,MAAA,4CAAM;IAC7Db,EAD6D,CAAAc,YAAA,EAAQ,EAC/D;IACNd,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAa,MAAA,IAEF;IACFb,EADE,CAAAc,YAAA,EAAQ,EACJ;IAGNd,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAA4B,UAAA,KAAA6F,gDAAA,kBAC0E;IAM5EzH,EAAA,CAAAc,YAAA,EAAM;IAQNd,EALA,CAAA4B,UAAA,KAAA8F,gDAAA,kBAA8E,KAAAC,gDAAA,kBAMlC;IAQ9C3H,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAA4B,UAAA,KAAAgG,gDAAA,kBAA+D;IAWnE5H,EADE,CAAAc,YAAA,EAAM,EACC;IACTd,EAAA,CAAAC,cAAA,kBACyE;IAAvED,EAAA,CAAAE,UAAA,mBAAA2H,mEAAA;MAAA7H,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAAkG,cAAA,CAAAvE,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAAgI,YAAA;IAAA,EAAsE;IAMhE9H,EAJN,CAAAC,cAAA,eAAkB,eAEM,eACE,iBAG0B;IAD5CD,EAAA,CAAAmD,gBAAA,2BAAA4E,0EAAA1E,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAA0H,iBAAA,EAAA3E,MAAA,MAAA/C,MAAA,CAAA0H,iBAAA,GAAA3E,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAA+B;IAACrD,EAAA,CAAAE,UAAA,yBAAA+H,wEAAA;MAAAjI,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAA4H,YAAA,EAAc;IAAA,EAAC;IAElElI,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,eAAsB,iBAG0B;IAD5CD,EAAA,CAAAmD,gBAAA,2BAAAgF,0EAAA9E,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAA8H,kBAAA,EAAA/E,MAAA,MAAA/C,MAAA,CAAA8H,kBAAA,GAAA/E,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAgC;IAACrD,EAAA,CAAAE,UAAA,yBAAAmI,wEAAA;MAAArI,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAeJ,MAAA,CAAA4H,YAAA,EAAc;IAAA,EAAC;IAEnElI,EAHE,CAAAc,YAAA,EAE8C,EAC1C;IAEJd,EADF,CAAAC,cAAA,eAAsB,kBAC0D;IAAxBD,EAAA,CAAAE,UAAA,mBAAAoI,mEAAA;MAAAtI,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiI,WAAA,EAAa;IAAA,EAAC;IAC3EvI,EAAA,CAAAY,SAAA,aAA2B;IAC7BZ,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAAkE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAsI,mEAAA;MAAAxI,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA4H,YAAA,EAAc;IAAA,EAAC;IAC/DlI,EAAA,CAAAY,SAAA,aAA6B;IAGnCZ,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAmE,eACG,cAC3B,iBAEM;IAAzCD,EAAA,CAAAE,UAAA,oBAAAuI,mEAAA;MAAAzI,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAAoI,cAAA,EAAgB;IAAA,EAAC;IAD7B1I,EAAA,CAAAc,YAAA,EAC2C;IAC3Cd,EAAA,CAAAC,cAAA,iBAA0D;IAAAD,EAAA,CAAAa,MAAA,4CAAM;IAClEb,EADkE,CAAAc,YAAA,EAAQ,EACpE;IACNd,EAAA,CAAAC,cAAA,iBAA0B;IACxBD,EAAA,CAAAa,MAAA,IAEF;IACFb,EADE,CAAAc,YAAA,EAAQ,EACJ;IAGNd,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAA4B,UAAA,KAAA+G,gDAAA,kBACuE;IAMzE3I,EAAA,CAAAc,YAAA,EAAM;IAQNd,EALA,CAAA4B,UAAA,KAAAgH,gDAAA,kBAAwF,KAAAC,gDAAA,kBAM9C;IAQ5C7I,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAA4B,UAAA,KAAAkH,gDAAA,kBAA8D;IAiD5E9I,EANY,CAAAc,YAAA,EAAM,EACC,EACC,EACR,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,qBAEe;IADZD,EAAA,CAAAmD,gBAAA,2BAAA4F,8EAAA1F,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAkG,cAAA,CAAAnE,OAAA,EAAAgB,MAAA,MAAA/C,MAAA,CAAAkG,cAAA,CAAAnE,OAAA,GAAAgB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAoC;IAG/DrD,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IAWlBb,EAXkB,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF,EAGF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAA8I,mEAAA;MAAA,MAAA7C,OAAA,GAAAnG,EAAA,CAAAI,aAAA,CAAAgG,IAAA,EAAAC,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAgG,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAExEnG,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,mBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAA+I,mEAAA;MAAA,MAAA9C,OAAA,GAAAnG,EAAA,CAAAI,aAAA,CAAAgG,IAAA,EAAAC,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoG,QAAA,CAAAP,OAAA,CAAa;IAAA,EAAC;IAE1DnG,EAAA,CAAAY,SAAA,cAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IApQMd,EAAA,CAAA+B,SAAA,IAA0C;IAA1C/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAAkG,cAAA,CAAArE,aAAA,CAA0C;IAgBlBnC,EAAA,CAAA+B,SAAA,GAA0E;IAA1E/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAAkG,cAAA,CAAAvE,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAA8G,aAAA,CAA0E;IAQxF5G,EAAA,CAAA+B,SAAA,GAAgC;IAAhC/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAAwG,kBAAA,CAAgC;IAKhC9G,EAAA,CAAA+B,SAAA,GAAiC;IAAjC/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAA4G,mBAAA,CAAiC;IAiBKlH,EAAA,CAAA+B,SAAA,GAA6B;IAA7B/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAA4I,iBAAA,CAA6B;IAKnElJ,EAAA,CAAA+B,SAAA,GAEF;IAFE/B,EAAA,CAAAmJ,kBAAA,aAAA7I,MAAA,CAAAoD,iBAAA,0BAAApD,MAAA,CAAAkD,cAAA,SAAAlD,MAAA,CAAA8I,IAAA,CAAAC,IAAA,CAAA/I,MAAA,CAAAoD,iBAAA,GAAApD,MAAA,CAAAsD,aAAA,cAEF;IAK0C5D,EAAA,CAAA+B,SAAA,GAAkB;IAAlB/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAAgJ,eAAA,CAAkB;IAUxDtJ,EAAA,CAAA+B,SAAA,EAAkC;IAAlC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAgJ,eAAA,CAAAnF,MAAA,OAAkC;IAMrCnE,EAAA,CAAA+B,SAAA,EAAuC;IAAvC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAoD,iBAAA,GAAApD,MAAA,CAAAsD,aAAA,CAAuC;IAWzB5D,EAAA,CAAA+B,SAAA,EAA0C;IAA1C/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA4D,yBAAA,CAAAC,MAAA,KAA0C;IAYzCnE,EAAA,CAAA+B,SAAA,EAAyE;IAAzE/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAAkG,cAAA,CAAAvE,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAgI,YAAA,CAAyE;IAQvF9H,EAAA,CAAA+B,SAAA,GAA+B;IAA/B/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAA0H,iBAAA,CAA+B;IAK/BhI,EAAA,CAAA+B,SAAA,GAAgC;IAAhC/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAA8H,kBAAA,CAAgC;IAiBWpI,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAAiJ,gBAAA,CAA4B;IAKvEvJ,EAAA,CAAA+B,SAAA,GAEF;IAFE/B,EAAA,CAAAmJ,kBAAA,aAAA7I,MAAA,CAAAsE,gBAAA,0BAAAtE,MAAA,CAAAoE,aAAA,SAAApE,MAAA,CAAA8I,IAAA,CAAAC,IAAA,CAAA/I,MAAA,CAAAsE,gBAAA,GAAAtE,MAAA,CAAAuE,YAAA,cAEF;IAKyC7E,EAAA,CAAA+B,SAAA,GAA4B;IAA5B/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAAkJ,yBAAA,CAA4B;IAUjExJ,EAAA,CAAA+B,SAAA,EAA4C;IAA5C/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAkJ,yBAAA,CAAArF,MAAA,OAA4C;IAM/CnE,EAAA,CAAA+B,SAAA,EAAqC;IAArC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAAsE,gBAAA,GAAAtE,MAAA,CAAAuE,YAAA,CAAqC;IAWvB7E,EAAA,CAAA+B,SAAA,EAAyC;IAAzC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA2F,wBAAA,CAAA9B,MAAA,KAAyC;IA6DjCnE,EAAA,CAAA+B,SAAA,GAAoC;IAApC/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAAkG,cAAA,CAAAnE,OAAA,CAAoC;IAEtDrC,EAAA,CAAA+B,SAAA,EAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;IAKXpC,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;;;;;;IAiCpCpC,EAFJ,CAAAC,cAAA,mBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,aAA6C;IAAAZ,EAAA,CAAAa,MAAA,gCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAuJ,kEAAA;MAAA,MAAAC,OAAA,GAAA1J,EAAA,CAAAI,aAAA,CAAAuJ,IAAA,EAAAtD,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAgG,OAAA,CAAAoD,OAAA,CAAY;IAAA,EAAC;IAE5F1J,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAQPd,EANV,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACW,kBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,kBAG6C;IADvED,EAAA,CAAAmD,gBAAA,2BAAAyG,0EAAAvG,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAuJ,IAAA;MAAA,MAAArJ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAkG,cAAA,CAAArE,aAAA,EAAAkB,MAAA,MAAA/C,MAAA,CAAAkG,cAAA,CAAArE,aAAA,GAAAkB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAA0C;IAKpDrD,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAC4D;IAC9FD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,qBACU,kBAEsC;IAAxED,EAAA,CAAAE,UAAA,mBAAA2J,mEAAA;MAAA7J,EAAA,CAAAI,aAAA,CAAAuJ,IAAA;MAAA,MAAArJ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAAkG,cAAA,CAAAvE,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAA8G,aAAA;IAAA,EAAuE;IACvE5G,EAAA,CAAAC,cAAA,iBAAsB;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAI/Bd,EADF,CAAAC,cAAA,eAAkB,gBACc;IAC5BD,EAAA,CAAAY,SAAA,aAAuC;IACvCZ,EAAA,CAAAa,MAAA,8IACF;IAEJb,EAFI,CAAAc,YAAA,EAAM,EACF,EACC;IACTd,EAAA,CAAAC,cAAA,kBACyE;IAAvED,EAAA,CAAAE,UAAA,mBAAA4J,mEAAA;MAAA9J,EAAA,CAAAI,aAAA,CAAAuJ,IAAA;MAAA,MAAArJ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAAJ,MAAA,CAAAkG,cAAA,CAAAvE,aAAA,GAAA3B,MAAA,CAAAR,gBAAA,CAAAgI,YAAA;IAAA,EAAsE;IACtE9H,EAAA,CAAAC,cAAA,iBAAsB;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAI/Bd,EADF,CAAAC,cAAA,eAAkB,gBACc;IAC5BD,EAAA,CAAAY,SAAA,aAAuC;IACvCZ,EAAA,CAAAa,MAAA,wLACF;IAOdb,EAPc,CAAAc,YAAA,EAAM,EACF,EACC,EACC,EACR,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,kBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,sBAEe;IADRD,EAAA,CAAAmD,gBAAA,2BAAA4G,8EAAA1G,MAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAuJ,IAAA;MAAA,MAAArJ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuD,kBAAA,CAAAjD,MAAA,CAAAkG,cAAA,CAAAnE,OAAA,EAAAgB,MAAA,MAAA/C,MAAA,CAAAkG,cAAA,CAAAnE,OAAA,GAAAgB,MAAA;MAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;IAAA,EAAoC;IAGnErD,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IAWlBb,EAXkB,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF,EAGF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAA8J,mEAAA;MAAA,MAAAN,OAAA,GAAA1J,EAAA,CAAAI,aAAA,CAAAuJ,IAAA,EAAAtD,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAgG,OAAA,CAAAoD,OAAA,CAAY;IAAA,EAAC;IAExE1J,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,mBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAA+J,mEAAA;MAAA,MAAAP,OAAA,GAAA1J,EAAA,CAAAI,aAAA,CAAAuJ,IAAA,EAAAtD,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoG,QAAA,CAAAgD,OAAA,CAAa;IAAA,EAAC;IAE1D1J,EAAA,CAAAY,SAAA,cAAgC;IAAAZ,EAAA,CAAAa,MAAA,qBAClC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IAzFMd,EAAA,CAAA+B,SAAA,IAA0C;IAA1C/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAAkG,cAAA,CAAArE,aAAA,CAA0C;IAgBlBnC,EAAA,CAAA+B,SAAA,GAA0E;IAA1E/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAAkG,cAAA,CAAAvE,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAA8G,aAAA,CAA0E;IAY1E5G,EAAA,CAAA+B,SAAA,GAAyE;IAAzE/B,EAAA,CAAAoC,UAAA,WAAA9B,MAAA,CAAAkG,cAAA,CAAAvE,aAAA,KAAA3B,MAAA,CAAAR,gBAAA,CAAAgI,YAAA,CAAyE;IA4B9D9H,EAAA,CAAA+B,SAAA,IAAoC;IAApC/B,EAAA,CAAA2D,gBAAA,YAAArD,MAAA,CAAAkG,cAAA,CAAAnE,OAAA,CAAoC;IAE1DrC,EAAA,CAAA+B,SAAA,EAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;IAKXpC,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAoC,UAAA,YAAW;;;;;IA0HhCpC,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAY,SAAA,aAAmF;IACnFZ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAa,MAAA,4BAAM;IACjCb,EADiC,CAAAc,YAAA,EAAO,EAClC;;;;;IAeId,EADF,CAAAC,cAAA,SAA8D,SACxD;IAAAD,EAAA,CAAAa,MAAA,GAAW;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpBd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAA2C;IAAAZ,EAAA,CAAAa,MAAA,GAC7C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,GACvD;IACFb,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAPCd,EAAA,CAAA+B,SAAA,GAAW;IAAX/B,EAAA,CAAAkC,iBAAA,CAAAgI,KAAA,KAAW;IAE8BlK,EAAA,CAAA+B,SAAA,GAC7C;IAD6C/B,EAAA,CAAAgC,kBAAA,KAAAmI,SAAA,CAAAlH,KAAA,MAC7C;IAEuDjD,EAAA,CAAA+B,SAAA,GACvD;IADuD/B,EAAA,CAAAgC,kBAAA,KAAAmI,SAAA,CAAAjH,SAAA,aACvD;;;;;IAbAlD,EAJR,CAAAC,cAAA,eAAsE,iBACtC,YACrB,SACD,cAC4B;IAAAD,EAAA,CAAAa,MAAA,QAAC;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAEtCb,EAFsC,CAAAc,YAAA,EAAK,EACpC,EACC;IACRd,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA4B,UAAA,KAAAwI,4DAAA,iBAA8D;IAWpEpK,EAFI,CAAAc,YAAA,EAAQ,EACF,EACJ;;;;IAXsBd,EAAA,CAAA+B,SAAA,IAAyB;IAAzB/B,EAAA,CAAAoC,UAAA,YAAA9B,MAAA,CAAA+J,oBAAA,CAAyB;;;;;IAcrDrK,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,0EACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IA3BRd,EAAA,CAAAC,cAAA,UAAsC;IAyBpCD,EAxBA,CAAA4B,UAAA,IAAA0I,sDAAA,oBAAsE,IAAAC,sDAAA,kBAwBa;IAGrFvK,EAAA,CAAAc,YAAA,EAAM;;;;IA3B2Bd,EAAA,CAAA+B,SAAA,EAAqC;IAArC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+J,oBAAA,CAAAlG,MAAA,KAAqC;IAwB9DnE,EAAA,CAAA+B,SAAA,EAAuC;IAAvC/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA+J,oBAAA,CAAAlG,MAAA,OAAuC;;;;;;IAxHnDnE,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,aAAyC;IAAAZ,EAAA,CAAAa,MAAA,gCAC3C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAsK,kEAAA;MAAA,MAAAC,OAAA,GAAAzK,EAAA,CAAAI,aAAA,CAAAsK,IAAA,EAAArE,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAgG,OAAA,CAAAmE,OAAA,CAAY;IAAA,EAAC;IAE5FzK,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAMXd,EAJN,CAAAC,cAAA,uBAAgC,eAEgD,eACkB,eAChD;IAC1CD,EAAA,CAAAY,SAAA,cAAoD;IAAAZ,EAAA,CAAAa,MAAA,iCACtD;IACFb,EADE,CAAAc,YAAA,EAAK,EACD;IAKEd,EAJR,CAAAC,cAAA,gBAAuB,eACJ,cACO,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAA4C;IAAAZ,EAAA,CAAAa,MAAA,iCAC9C;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAAkD;IAEtEb,EAFsE,CAAAc,YAAA,EAAI,EAClE,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,qBACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,cAAgB,gBAE4E;IACxFD,EAAA,CAAAY,SAAA,cACmB;IACnBZ,EAAA,CAAAa,MAAA,IACF;IAGNb,EAHM,CAAAc,YAAA,EAAO,EACL,EACA,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAmD;IAAAZ,EAAA,CAAAa,MAAA,iCACrD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,2BACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAEjEb,EAFiE,CAAAc,YAAA,EAAI,EAC7D,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAmD;IAAAZ,EAAA,CAAAa,MAAA,iCACrD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,2BACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAKvEb,EALuE,CAAAc,YAAA,EAAI,EAC7D,EACF,EACF,EACF,EACF;IAMFd,EAHJ,CAAAC,cAAA,gBAAyE,gBAEA,eACzB;IAC1CD,EAAA,CAAAY,SAAA,cAA6C;IAAAZ,EAAA,CAAAa,MAAA,uCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAa,MAAA,IAAuC;IACxEb,EADwE,CAAAc,YAAA,EAAO,EACzE;IACNd,EAAA,CAAAC,cAAA,gBAAuB;IAQrBD,EANA,CAAA4B,UAAA,KAAA+I,gDAAA,mBAA8D,KAAAC,gDAAA,kBAMxB;IA+B5C5K,EAFI,CAAAc,YAAA,EAAM,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,mBAEjD;IADtBD,EAAA,CAAAE,UAAA,mBAAA2K,mEAAA;MAAA,MAAAJ,OAAA,GAAAzK,EAAA,CAAAI,aAAA,CAAAsK,IAAA,EAAArE,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAgG,OAAA,CAAAmE,OAAA,CAAY;IAAA,EAAC;IAE3DzK,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA9GoBd,EAAA,CAAA+B,SAAA,IAAkD;IAAlD/B,EAAA,CAAAkC,iBAAA,EAAA5B,MAAA,CAAAwK,sBAAA,kBAAAxK,MAAA,CAAAwK,sBAAA,CAAA3I,aAAA,SAAkD;IAU9DnC,EAAA,CAAA+B,SAAA,GAAuF;IAAvF/B,EAAA,CAAAoC,UAAA,aAAA9B,MAAA,CAAAwK,sBAAA,kBAAAxK,MAAA,CAAAwK,sBAAA,CAAAzI,OAAA,8CAAuF;IACpFrC,EAAA,CAAA+B,SAAA,EAA+F;IAA/F/B,EAAA,CAAA+K,UAAA,EAAAzK,MAAA,CAAAwK,sBAAA,kBAAAxK,MAAA,CAAAwK,sBAAA,CAAAzI,OAAA,wDAA+F;IAElGrC,EAAA,CAAA+B,SAAA,EACF;IADE/B,EAAA,CAAAgC,kBAAA,OAAA1B,MAAA,CAAAwK,sBAAA,kBAAAxK,MAAA,CAAAwK,sBAAA,CAAAzI,OAAA,+CACF;IAScrC,EAAA,CAAA+B,SAAA,GAA2E;IAA3E/B,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAsC,WAAA,SAAAhC,MAAA,CAAAwK,sBAAA,kBAAAxK,MAAA,CAAAwK,sBAAA,CAAAvI,SAAA,6BAA2E;IAQ3EvC,EAAA,CAAA+B,SAAA,GAA6C;IAA7C/B,EAAA,CAAAkC,iBAAA,EAAA5B,MAAA,CAAAwK,sBAAA,kBAAAxK,MAAA,CAAAwK,sBAAA,CAAAtI,QAAA,SAA6C;IAQ7CxC,EAAA,CAAA+B,SAAA,GAA2E;IAA3E/B,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAsC,WAAA,SAAAhC,MAAA,CAAAwK,sBAAA,kBAAAxK,MAAA,CAAAwK,sBAAA,CAAAE,SAAA,6BAA2E;IAQ3EhL,EAAA,CAAA+B,SAAA,GAA6C;IAA7C/B,EAAA,CAAAkC,iBAAA,EAAA5B,MAAA,CAAAwK,sBAAA,kBAAAxK,MAAA,CAAAwK,sBAAA,CAAAG,QAAA,SAA6C;IAcpCjL,EAAA,CAAA+B,SAAA,GAAuC;IAAvC/B,EAAA,CAAAgC,kBAAA,YAAA1B,MAAA,CAAA+J,oBAAA,CAAAlG,MAAA,wBAAuC;IAIhEnE,EAAA,CAAA+B,SAAA,GAA6B;IAA7B/B,EAAA,CAAAoC,UAAA,SAAA9B,MAAA,CAAA4K,uBAAA,CAA6B;IAM7BlL,EAAA,CAAA+B,SAAA,EAA8B;IAA9B/B,EAAA,CAAAoC,UAAA,UAAA9B,MAAA,CAAA4K,uBAAA,CAA8B;;;ADnkB9C,OAAM,MAAOC,iBAAkB,SAAQzL,aAAa;EASlD0L,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,gBAAiC,EACjCC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAdf,KAAAtC,IAAI,GAAGA,IAAI,CAAC,CAAC;IACb,KAAAtJ,gBAAgB,GAAGA,gBAAgB,CAAC,CAAC;IACrC,KAAAC,sBAAsB,GAAGA,sBAAsB,CAAC,CAAC;IAiBxC,KAAA4L,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB;IACA,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAAvF,cAAc,GAAqB,EAAE;IACrC,KAAAwF,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAkB,IAAI;IAClC,KAAAC,kBAAkB,GAAkB,IAAI;IAExC;IACA,KAAA5C,eAAe,GAAwB,EAAE;IACzC,KAAApF,yBAAyB,GAAwB,EAAE;IACnD,KAAA4C,kBAAkB,GAAW,EAAE;IAC/B,KAAAI,mBAAmB,GAAW,EAAE;IAChC,KAAA1D,cAAc,GAAG,CAAC;IAClB,KAAAI,aAAa,GAAG,EAAE;IAClB,KAAAF,iBAAiB,GAAG,CAAC;IACrB,KAAAwF,iBAAiB,GAAG,KAAK;IAEzB;IACA,KAAAM,yBAAyB,GAAuB,EAAE;IAClD,KAAAvD,wBAAwB,GAAuB,EAAE;IACjD,KAAA+B,iBAAiB,GAAW,EAAE;IAC9B,KAAAI,kBAAkB,GAAW,EAAE;IAC/B,KAAA1D,aAAa,GAAG,CAAC;IACjB,KAAAG,YAAY,GAAG,EAAE;IACjB,KAAAD,gBAAgB,GAAG,CAAC;IACpB,KAAA2E,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAuB,sBAAsB,GAAwB,IAAI;IAClD,KAAAT,oBAAoB,GAA8B,EAAE;IACpD,KAAAa,uBAAuB,GAAG,KAAK;EArC/B;EAuCSiB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAD,gBAAgBA,CAAA;IACd,MAAME,OAAO,GAAG;MACdnK,aAAa,EAAE,IAAI,CAAC6J,aAAa,IAAI,IAAI;MACzC3J,OAAO,EAAE,IAAI,CAAC4J,YAAY;MAC1BhK,aAAa,EAAE,IAAI,CAACiK,kBAAkB;MACtCK,SAAS,EAAE,IAAI,CAACV,SAAS;MACzBW,QAAQ,EAAE,IAAI,CAACZ;KAChB;IAED,IAAI,CAACL,gBAAgB,CAACkB,mCAAmC,CAAC;MAAEC,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CAC/E9M,GAAG,CAAC+M,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACd,YAAY,GAAGa,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACjDC,WAAW,EAAED,IAAI,CAACC,WAAY;UAC9B9K,aAAa,EAAE6K,IAAI,CAAC7K,aAAc;UAClCF,aAAa,EAAE+K,IAAI,CAAC/K,aAAa;UAAE;UACnCM,SAAS,EAAEyK,IAAI,CAACzK,SAAU;UAC1ByI,SAAS,EAAEgC,IAAI,CAAChC,SAAU;UAC1BxI,QAAQ,EAAEwK,IAAI,CAACxK,QAAQ;UACvByI,QAAQ,EAAE+B,IAAI,CAAC/B,QAAQ;UACvB5I,OAAO,EAAE2K,IAAI,CAAC3K;SACf,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAACyJ,YAAY,GAAGc,QAAQ,CAACM,UAAU,IAAI,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAACzB,OAAO,CAAC0B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAhB,mBAAmBA,CAAA;IACjB,MAAMC,OAAO,GAAG;MACdrJ,KAAK,EAAE,IAAI,CAAC6D,kBAAkB,IAAI,IAAI;MACtC5D,SAAS,EAAE,IAAI,CAACgE,mBAAmB,IAAI,IAAI;MAC3C7E,OAAO,EAAE,CAAC;MAAE;MACZkK,SAAS,EAAE,IAAI,CAAC/I,cAAc;MAC9BgJ,QAAQ,EAAE,IAAI,CAAC5I;KAChB;IAED,IAAI,CAAC4H,aAAa,CAAC8B,6BAA6B,CAAC;MAAEZ,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACtE9M,GAAG,CAAC+M,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACvD,eAAe,GAAGsD,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDO,QAAQ,EAAEP,IAAI,CAACO,QAAS;UACxBtK,KAAK,EAAE+J,IAAI,CAAC/J,KAAM;UAClBC,SAAS,EAAE8J,IAAI,CAAC9J,SAAS;UACzBF,QAAQ,EAAE,IAAI,CAACkB,yBAAyB,CAACsJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ;SAChF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAC7J,iBAAiB,GAAGkJ,QAAQ,CAACM,UAAU,IAAI,CAAC;QACjD,IAAI,CAACQ,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,CACH,CAACL,SAAS,EAAE;EACf;EAEA;EACAM,QAAQA,CAAA;IACN,IAAI,CAAC9B,SAAS,GAAG,CAAC;IAClB,IAAI,CAACO,gBAAgB,EAAE;EACzB;EAEAwB,OAAOA,CAAA;IACL,IAAI,CAAC5B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACJ,SAAS,GAAG,CAAC;IAClB,IAAI,CAACO,gBAAgB,EAAE;EACzB;EAEA;EACAyB,6BAA6BA,CAAA;IAC3B,MAAMvB,OAAO,GAAG;MACdrJ,KAAK,EAAE,IAAI,CAAC+E,iBAAiB,IAAI,IAAI;MACrC9E,SAAS,EAAE,IAAI,CAACkF,kBAAkB,IAAI,IAAI;MAC1C/F,OAAO,EAAE,CAAC;MAAE;MACZkK,SAAS,EAAE,IAAI,CAAC7H,aAAa;MAC7B8H,QAAQ,EAAE,IAAI,CAAC3H;KAChB;IAED,IAAI,CAAC2G,aAAa,CAAC8B,6BAA6B,CAAC;MAAEZ,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACtE9M,GAAG,CAAC+M,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACrD,yBAAyB,GAAGoD,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UAC9DO,QAAQ,EAAEP,IAAI,CAACO,QAAS;UACxBtK,KAAK,EAAE+J,IAAI,CAAC/J,KAAM;UAClBC,SAAS,EAAE8J,IAAI,CAAC9J,SAAS;UACzBF,QAAQ,EAAE,IAAI,CAACiD,wBAAwB,CAACuH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;UAC/E1H,UAAU,EAAE,IAAI,CAACI,wBAAwB,CAAC6H,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAE1H,UAAU,IAAI,CAAC;UAClGC,KAAK,EAAE,IAAI,CAACG,wBAAwB,CAAC6H,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAEzH,KAAK,IAAI;SACxF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAClB,gBAAgB,GAAGgI,QAAQ,CAACM,UAAU,IAAI,CAAC;QAChD,IAAI,CAACa,2BAA2B,EAAE;MACpC;IACF,CAAC,CAAC,CACH,CAACV,SAAS,EAAE;EACf;EAEA;EACArG,aAAaA,CAAA;IACX,IAAI,CAACxD,cAAc,GAAG,CAAC;IACvB,IAAI,CAAC6I,mBAAmB,EAAE;EAC5B;EAEAhF,YAAYA,CAAA;IACV,IAAI,CAACP,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACI,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC1D,cAAc,GAAG,CAAC;IACvB,IAAI,CAAC6I,mBAAmB,EAAE;EAC5B;EAEA;EACAnE,YAAYA,CAAA;IACV,IAAI,CAACxD,aAAa,GAAG,CAAC;IACtB,IAAI,CAACmJ,6BAA6B,EAAE;EACtC;EAEAtF,WAAWA,CAAA;IACT,IAAI,CAACP,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACI,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC1D,aAAa,GAAG,CAAC;IACtB,IAAI,CAACmJ,6BAA6B,EAAE;EACtC;EAEA;EACAG,WAAWA,CAACC,IAAY;IACtB,IAAI,CAACpC,SAAS,GAAGoC,IAAI;IACrB,IAAI,CAAC7B,gBAAgB,EAAE;EACzB;EAEA3I,gBAAgBA,CAACwK,IAAY;IAC3B,IAAI,CAACzK,cAAc,GAAGyK,IAAI;IAC1B,IAAI,CAAC5B,mBAAmB,EAAE;EAC5B;EAEA1H,eAAeA,CAACsJ,IAAY;IAC1B,IAAI,CAACvJ,aAAa,GAAGuJ,IAAI;IACzB,IAAI,CAACJ,6BAA6B,EAAE;EACtC;EAEA;EACAlN,eAAeA,CAACuN,KAAuB;IACrC,IAAI,CAAC1H,cAAc,GAAG;MACpBnE,OAAO,EAAE,CAAC;MACVJ,aAAa,EAAEnC,gBAAgB,CAAC8G,aAAa,CAAC;KAC/C;IACD,IAAI,CAAC1C,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAAC+B,wBAAwB,GAAG,EAAE;IAClC,IAAI,CAACoG,mBAAmB,EAAE;IAC1B,IAAI,CAACwB,6BAA6B,EAAE;IACpC,IAAI,CAACvC,aAAa,CAAC6C,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAjN,aAAaA,CAAC8M,KAAuB,EAAEI,QAAsB;IAC3D,IAAI,CAAC9H,cAAc,GAAG;MACpByG,WAAW,EAAEqB,QAAQ,CAACrB,WAAW;MACjC9K,aAAa,EAAEmM,QAAQ,CAACnM,aAAa;MACrCF,aAAa,EAAEqM,QAAQ,CAACrM,aAAa,IAAInC,gBAAgB,CAAC8G,aAAa;MACvEvE,OAAO,EAAEiM,QAAQ,CAACjM,OAAO,IAAI;KAC9B;IACD,IAAI,CAACiJ,aAAa,CAAC6C,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEA/H,OAAOA,CAACiI,GAAQ;IACdA,GAAG,CAACC,KAAK,EAAE;EACb;EAEA9H,QAAQA,CAAC6H,GAAQ;IACf,IAAI,CAAC,IAAI,CAACE,oBAAoB,EAAE,EAAE;MAChC;IACF;IAEA,IAAI,IAAI,CAACjI,cAAc,CAACyG,WAAW,EAAE;MACnC,IAAI,CAACyB,cAAc,CAACH,GAAG,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACI,cAAc,CAACJ,GAAG,CAAC;IAC1B;EACF;EAEA;EACAE,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACjI,cAAc,CAACrE,aAAa,EAAE4D,IAAI,EAAE,EAAE;MAC9C,IAAI,CAAC0F,OAAO,CAAC0B,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAAC3G,cAAc,CAACvE,aAAa,KAAK2M,SAAS,IAAI,IAAI,CAACpI,cAAc,CAACvE,aAAa,KAAK,IAAI,EAAE;MACjG,IAAI,CAACwJ,OAAO,CAAC0B,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAAC3G,cAAc,CAACnE,OAAO,KAAKuM,SAAS,IAAI,IAAI,CAACpI,cAAc,CAACnE,OAAO,KAAK,IAAI,EAAE;MACrF,IAAI,CAACoJ,OAAO,CAAC0B,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAAC3G,cAAc,CAACyG,WAAW,IAAI,IAAI,CAACzG,cAAc,CAACvE,aAAa,KAAKnC,gBAAgB,CAAC8G,aAAa,IAAI,IAAI,CAAC1C,yBAAyB,CAACC,MAAM,KAAK,CAAC,EAAE;MAC3J,IAAI,CAACsH,OAAO,CAAC0B,YAAY,CAAC,eAAe,CAAC;MAC1C,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAAC3G,cAAc,CAACyG,WAAW,IAAI,IAAI,CAACzG,cAAc,CAACvE,aAAa,KAAKnC,gBAAgB,CAACgI,YAAY,IAAI,IAAI,CAAC7B,wBAAwB,CAAC9B,MAAM,KAAK,CAAC,EAAE;MACzJ,IAAI,CAACsH,OAAO,CAAC0B,YAAY,CAAC,eAAe,CAAC;MAC1C,OAAO,KAAK;IACd;IAEA;IACA,IAAI,IAAI,CAAC3G,cAAc,CAACvE,aAAa,KAAKnC,gBAAgB,CAACgI,YAAY,EAAE;MACvE,KAAK,MAAMkF,IAAI,IAAI,IAAI,CAAC/G,wBAAwB,EAAE;QAChD;QACA,IAAI+G,IAAI,CAACnH,UAAU,KAAK+I,SAAS,IAAI5B,IAAI,CAACnH,UAAU,KAAK,IAAI,IAAImH,IAAI,CAACnH,UAAU,IAAI,CAAC,EAAE;UACrF,IAAI,CAAC4F,OAAO,CAAC0B,YAAY,CAAC,MAAMH,IAAI,CAAC/J,KAAK,iBAAiB,CAAC;UAC5D,OAAO,KAAK;QACd;QAEA;QACA,IAAI,CAAC+J,IAAI,CAAClH,KAAK,IAAIkH,IAAI,CAAClH,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,IAAIiH,IAAI,CAAClH,KAAK,CAACC,IAAI,EAAE,KAAK,GAAG,EAAE;UACxE,IAAI,CAAC0F,OAAO,CAAC0B,YAAY,CAAC,MAAMH,IAAI,CAAC/J,KAAK,wBAAwB,CAAC;UACnE,OAAO,KAAK;QACd;QAEA;QACA,IAAI4L,KAAK,CAAC7B,IAAI,CAACnH,UAAU,CAAC,EAAE;UAC1B,IAAI,CAAC4F,OAAO,CAAC0B,YAAY,CAAC,MAAMH,IAAI,CAAC/J,KAAK,aAAa,CAAC;UACxD,OAAO,KAAK;QACd;QAEA;QACA,IAAI+J,IAAI,CAAClH,KAAK,CAACC,IAAI,EAAE,CAAC5B,MAAM,GAAG,EAAE,EAAE;UACjC,IAAI,CAACsH,OAAO,CAAC0B,YAAY,CAAC,MAAMH,IAAI,CAAC/J,KAAK,iBAAiB,CAAC;UAC5D,OAAO,KAAK;QACd;MACF;IACF;IAEA,OAAO,IAAI;EACb;EAEA;EACA0L,cAAcA,CAACJ,GAAQ;IACrB,MAAMO,YAAY,GAAqB;MACrC3M,aAAa,EAAE,IAAI,CAACqE,cAAc,CAACrE,aAAa;MAChDF,aAAa,EAAE,IAAI,CAACuE,cAAc,CAACvE,aAAa;MAChDI,OAAO,EAAE,IAAI,CAACmE,cAAc,CAACnE;KAC9B;IAED,IAAI,CAACkJ,gBAAgB,CAACwD,gCAAgC,CAAC;MAAErC,IAAI,EAAEoC;IAAY,CAAE,CAAC,CAACnC,IAAI,CACjF9M,GAAG,CAAC+M,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;QACjD,MAAMkC,UAAU,GAAGC,QAAQ,CAACrC,QAAQ,CAACE,OAAO,EAAE,EAAE,CAAC;QACjD,IAAI,CAACoC,mBAAmB,CAACF,UAAU,EAAET,GAAG,CAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAAC9C,OAAO,CAAC0B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAqB,cAAcA,CAACH,GAAQ;IACrB,MAAMO,YAAY,GAAqB;MACrC7B,WAAW,EAAE,IAAI,CAACzG,cAAc,CAACyG,WAAW;MAC5C9K,aAAa,EAAE,IAAI,CAACqE,cAAc,CAACrE,aAAa;MAChDF,aAAa,EAAE,IAAI,CAACuE,cAAc,CAACvE,aAAa;MAChDI,OAAO,EAAE,IAAI,CAACmE,cAAc,CAACnE;KAC9B;IAED,IAAI,CAACkJ,gBAAgB,CAACwD,gCAAgC,CAAC;MAAErC,IAAI,EAAEoC;IAAY,CAAE,CAAC,CAACnC,IAAI,CACjF9M,GAAG,CAAC+M,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACpB,OAAO,CAAC0D,aAAa,CAAC,QAAQ,CAAC;QACpCZ,GAAG,CAACC,KAAK,EAAE;QACX,IAAI,CAACpC,gBAAgB,EAAE;MACzB,CAAC,MAAM;QACL,IAAI,CAACX,OAAO,CAAC0B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACA6B,mBAAmBA,CAACF,UAAkB,EAAET,GAAQ;IAC9C,IAAIa,OAAO,GAAU,EAAE;IAEvB,IAAI,IAAI,CAAC5I,cAAc,CAACvE,aAAa,KAAKnC,gBAAgB,CAAC8G,aAAa,EAAE;MACxE;MACAwI,OAAO,GAAG,IAAI,CAAClL,yBAAyB,CAAC6I,GAAG,CAACsC,KAAK,KAAK;QACrDC,iBAAiB,EAAE,IAAI;QACvBC,UAAU,EAAEF,KAAK,CAAC9B,QAAQ;QAC1BtK,KAAK,EAAEoM,KAAK,CAACpM,KAAK;QAClBC,SAAS,EAAEmM,KAAK,CAACnM;OAClB,CAAC,CAAC;IACL,CAAC,MAAM,IAAI,IAAI,CAACsD,cAAc,CAACvE,aAAa,KAAKnC,gBAAgB,CAACgI,YAAY,EAAE;MAC9E;MACAsH,OAAO,GAAG,IAAI,CAACnJ,wBAAwB,CAAC8G,GAAG,CAACC,IAAI,KAAK;QACnDsC,iBAAiB,EAAE,IAAI;QACvBC,UAAU,EAAEvC,IAAI,CAACO,QAAQ;QACzBtK,KAAK,EAAE+J,IAAI,CAAC/J,KAAK;QACjBC,SAAS,EAAE8J,IAAI,CAAC9J,SAAS;QACzB2C,UAAU,EAAEmH,IAAI,CAACnH,UAAU;QAC3BC,KAAK,EAAEkH,IAAI,CAAClH;OACb,CAAC,CAAC;IACL;IAEA,IAAIsJ,OAAO,CAACjL,MAAM,GAAG,CAAC,EAAE;MACtB,MAAM2K,YAAY,GAAqB;QACrC7B,WAAW,EAAE+B,UAAU;QACvB7M,aAAa,EAAE,IAAI,CAACqE,cAAc,CAACrE,aAAa;QAChDF,aAAa,EAAE,IAAI,CAACuE,cAAc,CAACvE,aAAa;QAChDI,OAAO,EAAE,IAAI,CAACmE,cAAc,CAACnE,OAAO;QACpCmN,OAAO,EAAEJ;OACV;MAED,IAAI,CAAC7D,gBAAgB,CAACwD,gCAAgC,CAAC;QAAErC,IAAI,EAAEoC;MAAY,CAAE,CAAC,CAACnC,IAAI,CACjF9M,GAAG,CAAC+M,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAACpB,OAAO,CAAC0D,aAAa,CAAC,QAAQ,CAAC;UACpCZ,GAAG,CAACC,KAAK,EAAE;UACX,IAAI,CAACpC,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAACX,OAAO,CAAC0B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,YAAY,CAAC;QAC7D;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf,CAAC,MAAM;MACL,IAAI,CAAC5B,OAAO,CAAC0D,aAAa,CAAC,QAAQ,CAAC;MACpCZ,GAAG,CAACC,KAAK,EAAE;MACX,IAAI,CAACpC,gBAAgB,EAAE;IACzB;EACF;EAEA;EACA7K,cAAcA,CAAC+M,QAAsB;IACnC,IAAImB,OAAO,CAAC,WAAWnB,QAAQ,CAACnM,aAAa,KAAK,CAAC,EAAE;MACnD,IAAI,CAACoJ,gBAAgB,CAACmE,kCAAkC,CAAC;QACvDhD,IAAI,EAAE;UAAEO,WAAW,EAAEqB,QAAQ,CAACrB;QAAW;OAC1C,CAAC,CAACN,IAAI,CACL9M,GAAG,CAAC+M,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAACpB,OAAO,CAAC0D,aAAa,CAAC,QAAQ,CAAC;UACpC,IAAI,CAAC/C,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAACX,OAAO,CAAC0B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;QACzD;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf;EACF;EAEA;EACA1L,kBAAkBA,CAAC2M,QAAsB,EAAEJ,KAAuB;IAChE,IAAI,CAACpD,sBAAsB,GAAGwD,QAAQ;IACtC,IAAI,CAACpD,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACb,oBAAoB,GAAG,EAAE;IAE9B,IAAI,CAACiB,aAAa,CAAC6C,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;IAEF,MAAM/B,OAAO,GAA8B;MACzC0C,UAAU,EAAEV,QAAQ,CAACrB;KACtB;IAED,IAAI,CAAC1B,gBAAgB,CAACoE,yCAAyC,CAAC;MAAEjD,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACrF9M,GAAG,CAAC+M,QAAQ,IAAG;MACb,IAAI,CAAC1B,uBAAuB,GAAG,KAAK;MACpC,IAAI0B,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACxC,oBAAoB,GAAGuC,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACzDuC,UAAU,EAAEvC,IAAI,CAACuC,UAAW;UAC5BtM,KAAK,EAAE+J,IAAI,CAAC/J,KAAM;UAClBC,SAAS,EAAE8J,IAAI,CAAC9J;SACjB,CAAC,CAAC,IAAI,EAAE;MACX,CAAC,MAAM;QACL,IAAI,CAACuI,OAAO,CAAC0B,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAvK,oBAAoBA,CAACuM,KAAwB;IAC3CA,KAAK,CAACrM,QAAQ,GAAG,CAACqM,KAAK,CAACrM,QAAQ;IAEhC,IAAIqM,KAAK,CAACrM,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAACkB,yBAAyB,CAACsJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC,EAAE;QAC5E,IAAI,CAACrJ,yBAAyB,CAAC0L,IAAI,CAAC;UAAE,GAAGP;QAAK,CAAE,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAACnL,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC2L,MAAM,CAACpC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC;IAC5G;IAEA,IAAI,CAACG,4BAA4B,EAAE;EACrC;EAEAlG,eAAeA,CAAA;IACb,IAAI,CAAC0B,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAEhD,IAAI,CAACI,eAAe,CAACwG,OAAO,CAACT,KAAK,IAAG;MACnCA,KAAK,CAACrM,QAAQ,GAAG,IAAI,CAACkG,iBAAiB;MACvC,IAAI,IAAI,CAACA,iBAAiB,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAChF,yBAAyB,CAACsJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC,EAAE;UAC5E,IAAI,CAACrJ,yBAAyB,CAAC0L,IAAI,CAAC;YAAE,GAAGP;UAAK,CAAE,CAAC;QACnD;MACF,CAAC,MAAM;QACL,IAAI,CAACnL,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC2L,MAAM,CAACpC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC;MAC5G;IACF,CAAC,CAAC;EACJ;EAEAvJ,mBAAmBA,CAACqL,KAAwB;IAC1C,IAAI,CAACnL,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC2L,MAAM,CAACpC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC;IAE1G,MAAMwC,cAAc,GAAG,IAAI,CAACzG,eAAe,CAACwE,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAK8B,KAAK,CAAC9B,QAAQ,CAAC;IACpF,IAAIwC,cAAc,EAAE;MAClBA,cAAc,CAAC/M,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAAC0K,4BAA4B,EAAE;EACrC;EAEAA,4BAA4BA,CAAA;IAC1B,IAAI,CAACxE,iBAAiB,GAAG,IAAI,CAACI,eAAe,CAACnF,MAAM,GAAG,CAAC,IACtD,IAAI,CAACmF,eAAe,CAAC0G,KAAK,CAACX,KAAK,IAAIA,KAAK,CAACrM,QAAQ,CAAC;EACvD;EAEA;EACAuB,mBAAmBA,CAACyI,IAAsB;IACxCA,IAAI,CAAChK,QAAQ,GAAG,CAACgK,IAAI,CAAChK,QAAQ;IAE9B,IAAIgK,IAAI,CAAChK,QAAQ,EAAE;MACjB,IAAI,CAAC,IAAI,CAACiD,wBAAwB,CAACuH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAE;QAC1E,IAAI,CAACtH,wBAAwB,CAAC2J,IAAI,CAAC;UAAE,GAAG5C;QAAI,CAAE,CAAC;MACjD;IACF,CAAC,MAAM;MACL,IAAI,CAAC/G,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAAC4J,MAAM,CAACpC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IACzG;IAEA,IAAI,CAACQ,2BAA2B,EAAE;EACpC;EAEArF,cAAcA,CAAA;IACZ,IAAI,CAACa,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAE9C,IAAI,CAACC,yBAAyB,CAACsG,OAAO,CAAC9C,IAAI,IAAG;MAC5CA,IAAI,CAAChK,QAAQ,GAAG,IAAI,CAACuG,gBAAgB;MACrC,IAAI,IAAI,CAACA,gBAAgB,EAAE;QACzB,IAAI,CAAC,IAAI,CAACtD,wBAAwB,CAACuH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAE;UAC1E,IAAI,CAACtH,wBAAwB,CAAC2J,IAAI,CAAC;YAAE,GAAG5C;UAAI,CAAE,CAAC;QACjD;MACF,CAAC,MAAM;QACL,IAAI,CAAC/G,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAAC4J,MAAM,CAACpC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;MACzG;IACF,CAAC,CAAC;EACJ;EAEApI,kBAAkBA,CAAC6H,IAAsB;IACvC,IAAI,CAAC/G,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAAC4J,MAAM,CAACpC,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAEvG,MAAM0C,aAAa,GAAG,IAAI,CAACzG,yBAAyB,CAACsE,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAC5F,IAAI0C,aAAa,EAAE;MACjBA,aAAa,CAACjN,QAAQ,GAAG,KAAK;IAChC;IAEA,IAAI,CAAC+K,2BAA2B,EAAE;EACpC;EAEAA,2BAA2BA,CAAA;IACzB,IAAI,CAACxE,gBAAgB,GAAG,IAAI,CAACC,yBAAyB,CAACrF,MAAM,GAAG,CAAC,IAC/D,IAAI,CAACqF,yBAAyB,CAACwG,KAAK,CAAChD,IAAI,IAAIA,IAAI,CAAChK,QAAQ,CAAC;EAC/D;EAEA;EACAqC,eAAeA,CAAC2H,IAAsB,EAAEkD,KAAa;IACnD;IACA,IAAIrB,KAAK,CAACqB,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC7BA,KAAK,GAAG,CAAC;IACX;IAEAlD,IAAI,CAACnH,UAAU,GAAGqK,KAAK;IACvB;IACA,MAAMD,aAAa,GAAG,IAAI,CAACzG,yBAAyB,CAACsE,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAC5F,IAAI0C,aAAa,EAAE;MACjBA,aAAa,CAACpK,UAAU,GAAGqK,KAAK;IAClC;EACF;EAEAxK,cAAcA,CAACsH,IAAsB,EAAEmD,IAAY;IACjD;IACAA,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAACpK,IAAI,EAAE,GAAG,EAAE;IAE9BiH,IAAI,CAAClH,KAAK,GAAGqK,IAAI;IACjB;IACA,MAAMF,aAAa,GAAG,IAAI,CAACzG,yBAAyB,CAACsE,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACF,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAC5F,IAAI0C,aAAa,EAAE;MACjBA,aAAa,CAACnK,KAAK,GAAGqK,IAAI;IAC5B;EACF;EAEA;EACAvK,WAAWA,CAACoH,IAAsB;IAChC,OAAO,CAAC,EAAEA,IAAI,CAACnH,UAAU,IAAImH,IAAI,CAACnH,UAAU,GAAG,CAAC,IAC9CmH,IAAI,CAAClH,KAAK,IAAIkH,IAAI,CAAClH,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,IAAIiH,IAAI,CAAClH,KAAK,CAACC,IAAI,EAAE,KAAK,GAAG,CAAC;EACxE;;;uCA3jBWoF,iBAAiB,EAAAnL,EAAA,CAAAoQ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtQ,EAAA,CAAAoQ,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAxQ,EAAA,CAAAoQ,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA1Q,EAAA,CAAAoQ,iBAAA,CAAAK,EAAA,CAAAE,YAAA,GAAA3Q,EAAA,CAAAoQ,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA7Q,EAAA,CAAAoQ,iBAAA,CAAAU,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAjB5F,iBAAiB;MAAA6F,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;UC7D5BnR,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,SAAA,qBAAiC;UACnCZ,EAAA,CAAAc,YAAA,EAAiB;UAIbd,EAHJ,CAAAC,cAAA,mBAAc,aAE6E,aAChD;UACrCD,EAAA,CAAAY,SAAA,WAA+E;UAE7EZ,EADF,CAAAC,cAAA,UAAK,WACmD;UACpDD,EAAA,CAAAa,MAAA,iUACF;UAGNb,EAHM,CAAAc,YAAA,EAAI,EACA,EACF,EACF;UAMAd,EAHN,CAAAC,cAAA,cAA8B,cACN,eACqC,iBACT;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAExDd,EADF,CAAAC,cAAA,yBAA6B,iBAE8B;UAAvDD,EAAA,CAAAmD,gBAAA,2BAAAkO,2DAAAhO,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAAkR,GAAA;YAAAtR,EAAA,CAAAuD,kBAAA,CAAA6N,GAAA,CAAApF,aAAA,EAAA3I,MAAA,MAAA+N,GAAA,CAAApF,aAAA,GAAA3I,MAAA;YAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;UAAA,EAA2B;UAACrD,EAAA,CAAAE,UAAA,yBAAAqR,yDAAA;YAAAvR,EAAA,CAAAI,aAAA,CAAAkR,GAAA;YAAA,OAAAtR,EAAA,CAAAU,WAAA,CAAe0Q,GAAA,CAAAzD,QAAA,EAAU;UAAA,EAAC;UAG9D3N,EAJM,CAAAc,YAAA,EACyD,EAC3C,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACf;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAEhDd,EADF,CAAAC,cAAA,yBAA6B,qBAC2E;UAAzDD,EAAA,CAAAmD,gBAAA,2BAAAqO,+DAAAnO,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAAkR,GAAA;YAAAtR,EAAA,CAAAuD,kBAAA,CAAA6N,GAAA,CAAAnF,YAAA,EAAA5I,MAAA,MAAA+N,GAAA,CAAAnF,YAAA,GAAA5I,MAAA;YAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;UAAA,EAA0B;UAACrD,EAAA,CAAAE,UAAA,4BAAAuR,gEAAA;YAAAzR,EAAA,CAAAI,aAAA,CAAAkR,GAAA;YAAA,OAAAtR,EAAA,CAAAU,WAAA,CAAkB0Q,GAAA,CAAAzD,QAAA,EAAU;UAAA,EAAC;UACnG3N,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACxCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACrCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAIjCb,EAJiC,CAAAc,YAAA,EAAY,EAC3B,EACE,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACT;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAExDd,EADF,CAAAC,cAAA,yBAA6B,qBAEK;UADqBD,EAAA,CAAAmD,gBAAA,2BAAAuO,+DAAArO,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAAkR,GAAA;YAAAtR,EAAA,CAAAuD,kBAAA,CAAA6N,GAAA,CAAAlF,kBAAA,EAAA7I,MAAA,MAAA+N,GAAA,CAAAlF,kBAAA,GAAA7I,MAAA;YAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;UAAA,EAAgC;UACnFrD,EAAA,CAAAE,UAAA,4BAAAyR,gEAAA;YAAA3R,EAAA,CAAAI,aAAA,CAAAkR,GAAA;YAAA,OAAAtR,EAAA,CAAAU,WAAA,CAAkB0Q,GAAA,CAAAzD,QAAA,EAAU;UAAA,EAAC;UAC7B3N,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACxCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACvCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAInCb,EAJmC,CAAAc,YAAA,EAAY,EAC7B,EACE,EACZ,EACF;UAENd,EAAA,CAAAY,SAAA,cAEM;UAKFZ,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACmB;UAApBD,EAAA,CAAAE,UAAA,mBAAA0R,oDAAA;YAAA5R,EAAA,CAAAI,aAAA,CAAAkR,GAAA;YAAA,OAAAtR,EAAA,CAAAU,WAAA,CAAS0Q,GAAA,CAAAxD,OAAA,EAAS;UAAA,EAAC;UACvE5N,EAAA,CAAAY,SAAA,aAAgC;UAAAZ,EAAA,CAAAa,MAAA,qBAClC;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAC,cAAA,kBAA8D;UAArBD,EAAA,CAAAE,UAAA,mBAAA2R,oDAAA;YAAA7R,EAAA,CAAAI,aAAA,CAAAkR,GAAA;YAAA,OAAAtR,EAAA,CAAAU,WAAA,CAAS0Q,GAAA,CAAAzD,QAAA,EAAU;UAAA,EAAC;UAC3D3N,EAAA,CAAAY,SAAA,aAAkC;UAAAZ,EAAA,CAAAa,MAAA,qBACpC;UAEJb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAGJd,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAA4B,UAAA,KAAAkQ,oCAAA,qBAAiG;UAKvG9R,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;UAOEd,EAJR,CAAAC,cAAA,eAAmC,iBACc,aACtC,UACD,cACoC;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC7Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,0BAAG;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC9Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAE5Cb,EAF4C,CAAAc,YAAA,EAAK,EAC1C,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UA2BLD,EA1BA,CAAA4B,UAAA,KAAAmQ,gCAAA,mBAA0C,KAAAC,gCAAA,iBA0BJ;UAQ9ChS,EAHM,CAAAc,YAAA,EAAQ,EACF,EACJ,EACO;UAEbd,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADrBD,EAAA,CAAAmD,gBAAA,wBAAA8O,iEAAA5O,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAAkR,GAAA;YAAAtR,EAAA,CAAAuD,kBAAA,CAAA6N,GAAA,CAAAvF,SAAA,EAAAxI,MAAA,MAAA+N,GAAA,CAAAvF,SAAA,GAAAxI,MAAA;YAAA,OAAArD,EAAA,CAAAU,WAAA,CAAA2C,MAAA;UAAA,EAAoB;UAClCrD,EAAA,CAAAE,UAAA,wBAAA+R,iEAAA5O,MAAA;YAAArD,EAAA,CAAAI,aAAA,CAAAkR,GAAA;YAAA,OAAAtR,EAAA,CAAAU,WAAA,CAAc0Q,GAAA,CAAApD,WAAA,CAAA3K,MAAA,CAAmB;UAAA,EAAC;UAGxCrD,EAFI,CAAAc,YAAA,EAAiB,EACF,EACT;UAwZVd,EArZA,CAAA4B,UAAA,KAAAsQ,yCAAA,kCAAAlS,EAAA,CAAAmS,sBAAA,CAA8C,KAAAC,yCAAA,iCAAApS,EAAA,CAAAmS,sBAAA,CAgSF,KAAAE,yCAAA,kCAAArS,EAAA,CAAAmS,sBAAA,CAqHU;;;UAvgBxCnS,EAAA,CAAA+B,SAAA,IAA2B;UAA3B/B,EAAA,CAAA2D,gBAAA,YAAAyN,GAAA,CAAApF,aAAA,CAA2B;UASgBhM,EAAA,CAAA+B,SAAA,GAA0B;UAA1B/B,EAAA,CAAA2D,gBAAA,YAAAyN,GAAA,CAAAnF,YAAA,CAA0B;UAC1DjM,EAAA,CAAA+B,SAAA,EAAc;UAAd/B,EAAA,CAAAoC,UAAA,eAAc;UACdpC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAoC,UAAA,YAAW;UACXpC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAoC,UAAA,YAAW;UAU6BpC,EAAA,CAAA+B,SAAA,GAAgC;UAAhC/B,EAAA,CAAA2D,gBAAA,YAAAyN,GAAA,CAAAlF,kBAAA,CAAgC;UAExElM,EAAA,CAAA+B,SAAA,EAAc;UAAd/B,EAAA,CAAAoC,UAAA,eAAc;UACdpC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAoC,UAAA,YAAW;UACXpC,EAAA,CAAA+B,SAAA,GAAW;UAAX/B,EAAA,CAAAoC,UAAA,YAAW;UAwBgBpC,EAAA,CAAA+B,SAAA,IAAc;UAAd/B,EAAA,CAAAoC,UAAA,SAAAgP,GAAA,CAAAkB,QAAA,CAAc;UAqB/BtS,EAAA,CAAA+B,SAAA,IAAe;UAAf/B,EAAA,CAAAoC,UAAA,YAAAgP,GAAA,CAAArF,YAAA,CAAe;UA0BnC/L,EAAA,CAAA+B,SAAA,EAA+B;UAA/B/B,EAAA,CAAAoC,UAAA,SAAAgP,GAAA,CAAArF,YAAA,CAAA5H,MAAA,OAA+B;UAU1BnE,EAAA,CAAA+B,SAAA,GAAoB;UAApB/B,EAAA,CAAA2D,gBAAA,SAAAyN,GAAA,CAAAvF,SAAA,CAAoB;UAAuB7L,EAAtB,CAAAoC,UAAA,aAAAgP,GAAA,CAAAxF,QAAA,CAAqB,mBAAAwF,GAAA,CAAAtF,YAAA,CAAgC;;;qBD1E1FnM,YAAY,EAAA4S,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZ/S,YAAY,EAAAgT,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAxC,EAAA,CAAAyC,eAAA,EAAAzC,EAAA,CAAA0C,mBAAA,EAAA1C,EAAA,CAAA2C,qBAAA,EAAA3C,EAAA,CAAA4C,qBAAA,EAAA5C,EAAA,CAAA6C,gBAAA,EAAA7C,EAAA,CAAA8C,iBAAA,EAAA9C,EAAA,CAAA+C,iBAAA,EAAA/C,EAAA,CAAAgD,iBAAA,EAAAhD,EAAA,CAAAiD,cAAA,EAAAjD,EAAA,CAAAkD,oBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}