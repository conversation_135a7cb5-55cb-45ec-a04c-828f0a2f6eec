{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { TemplateGetListResponse } from 'src/services/api/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"input\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_ngModelChange_1_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r5.selected, $event) || (item_r5.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onTemplateItemChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 32)(3, \"div\")(4, \"div\", 33);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 34);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 35);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 36);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_ngModelChange_13_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r5.price, $event) || (item_r5.price = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_change_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onTemplateItemChange());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r5.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.unit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", i0.ɵɵpipeBind1(12, 6, item_r5.price), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r5.price);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_div_13_div_6_Template_div_click_1_listener() {\n      const group_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleTemplateGroup(group_r2));\n    });\n    i0.ɵɵelementStart(2, \"div\", 24)(3, \"div\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 27);\n    i0.ɵɵtext(9, \"\\u25BC\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 28);\n    i0.ɵɵtemplate(11, SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template, 14, 8, \"div\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", group_r2.expanded);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", group_r2.icon, \" \", group_r2.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", group_r2.items.length, \"\\u500B\\u9805\\u76EE \\u2022 $\", i0.ɵɵpipeBind1(7, 11, ctx_r2.getTotalPrice(group_r2.items)), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"expanded\", group_r2.expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"expanded\", group_r2.expanded);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", group_r2.items);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18);\n    i0.ɵɵelement(3, \"i\", 19);\n    i0.ɵɵtext(4, \"\\u9078\\u64C7\\u6A21\\u677F\\u7FA4\\u7D44 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 20);\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_13_div_6_Template, 12, 13, \"div\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateGroups);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_li_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", item_r6.name, \" (\", item_r6.code, \") - $\", i0.ɵɵpipeBind1(2, 3, item_r6.price), \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48)(2, \"strong\");\n    i0.ɵɵelement(3, \"i\", 49);\n    i0.ɵɵtext(4, \"\\u885D\\u7A81\\u6AA2\\u6E2C\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6AA2\\u6E2C\\u5230 \", ctx_r2.getConflictCount(), \" \\u500B\\u9805\\u76EE\\u53EF\\u80FD\\u8207\\u73FE\\u6709\\u9700\\u6C42\\u91CD\\u8907\\uFF0C\\u7CFB\\u7D71\\u5C07\\u81EA\\u52D5\\u8655\\u7406\\u885D\\u7A81\\u9805\\u76EE\\u3002 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 37)(2, \"div\", 18);\n    i0.ɵɵelement(3, \"i\", 38);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u5957\\u7528\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 39)(6, \"div\", 40);\n    i0.ɵɵtext(7, \" \\u5C07\\u5957\\u7528 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9, \"\\u901A\\u7528\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 41);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 42)(15, \"div\", 43);\n    i0.ɵɵtext(16, \"\\u5957\\u7528\\u9805\\u76EE\\u6E05\\u55AE\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"ul\", 44);\n    i0.ɵɵtemplate(18, SpaceTemplateSelectorComponent_div_14_li_18_Template, 3, 5, \"li\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, SpaceTemplateSelectorComponent_div_14_div_19_Template, 6, 1, \"div\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\\uFF1A\", ctx_r2.getSelectedItems().length, \"\\u500B\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u7E3D\\u50F9\\u503C $\", i0.ɵɵpipeBind1(13, 4, ctx_r2.getSelectedTotalPrice()), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSelectedItems());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasConflicts());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.previousStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0B\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canProceed());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyTemplate());\n    });\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u5957\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.getSelectedItems().length === 0);\n  }\n}\nexport class SpaceTemplateSelectorComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.isVisible = false;\n    this.buildCaseId = '';\n    this.templateApplied = new EventEmitter();\n    this.closed = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templateGroups = [];\n  }\n  ngOnInit() {\n    // 自動載入模板資料\n    this.loadTemplatesFromAPI();\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: 1,\n      // 1=客變需求\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 將API資料轉換為組件需要的格式\n          this.convertAPIDataToTemplateGroups(response.Entries);\n        } else {\n          // API 返回錯誤\n          this.templateGroups = [];\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n        this.templateGroups = [];\n      }\n    });\n  }\n  convertAPIDataToTemplateGroups(apiData) {\n    // 將API資料轉換為模板群組格式\n    const groupMap = new Map();\n    apiData.forEach(item => {\n      const groupName = item.CGroupName || '預設群組';\n      if (!groupMap.has(groupName)) {\n        groupMap.set(groupName, []);\n      }\n      groupMap.get(groupName).push({\n        id: item.CTemplateId?.toString() || '',\n        name: item.CTemplateName || '',\n        code: item.CTemplateId?.toString() || '',\n        price: 0,\n        // API中沒有價格資訊，預設為0\n        unit: '個',\n        selected: false\n      });\n    });\n    // 轉換為模板群組陣列\n    this.templateGroups = Array.from(groupMap.entries()).map(([groupName, items], index) => ({\n      id: `group-${index}`,\n      name: groupName,\n      icon: '📋',\n      // 預設圖示\n      items: items,\n      expanded: false\n    }));\n  }\n  selectSpace(space) {\n    // 移除此方法，不再需要\n  }\n  loadTemplatesForSpace(spaceId) {\n    // 根據空間ID載入對應的模板群組\n    // 這裡可以調用API或使用預設資料\n    this.templateGroups = this.getDefaultTemplatesForSpace(spaceId);\n  }\n  getDefaultTemplatesForSpace(spaceId) {\n    const templates = {\n      kitchen: [{\n        id: 'kitchen-standard',\n        name: '廚房標準配備',\n        icon: '🔧',\n        expanded: false,\n        items: [{\n          id: 'kt001',\n          name: '洗碗機',\n          code: 'KT001',\n          price: 38000,\n          unit: '台',\n          selected: false\n        }, {\n          id: 'kt002',\n          name: '烤箱',\n          code: 'KT002',\n          price: 25000,\n          unit: '台',\n          selected: false\n        }, {\n          id: 'kt003',\n          name: '抽油煙機升級',\n          code: 'KT003',\n          price: 15000,\n          unit: '台',\n          selected: false\n        }]\n      }, {\n        id: 'kitchen-premium',\n        name: '廚房高級配備',\n        icon: '⭐',\n        expanded: false,\n        items: [{\n          id: 'kt004',\n          name: '中島檯面',\n          code: 'KT004',\n          price: 80000,\n          unit: '組',\n          selected: false\n        }, {\n          id: 'kt005',\n          name: '智能電磁爐',\n          code: 'KT005',\n          price: 45000,\n          unit: '台',\n          selected: false\n        }]\n      }],\n      living: [{\n        id: 'living-basic',\n        name: '客廳基本配備',\n        icon: '🏠',\n        expanded: false,\n        items: [{\n          id: 'lv001',\n          name: '投影設備',\n          code: 'LV001',\n          price: 50000,\n          unit: '組',\n          selected: false\n        }, {\n          id: 'lv002',\n          name: '音響系統',\n          code: 'LV002',\n          price: 35000,\n          unit: '組',\n          selected: false\n        }]\n      }]\n    };\n    return templates[spaceId] || [];\n  }\n  toggleTemplateGroup(group) {\n    // 收合其他群組\n    this.templateGroups.forEach(g => {\n      if (g.id !== group.id) {\n        g.expanded = false;\n      }\n    });\n    // 切換當前群組\n    group.expanded = !group.expanded;\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  getTotalPrice(items) {\n    return items.reduce((total, item) => total + (item.selected ? item.price : 0), 0);\n  }\n  getSelectedItems() {\n    const selected = [];\n    this.templateGroups.forEach(group => {\n      group.items.forEach(item => {\n        if (item.selected) {\n          selected.push(item);\n        }\n      });\n    });\n    return selected;\n  }\n  getSelectedTotalPrice() {\n    return this.getSelectedItems().reduce((total, item) => total + item.price, 0);\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要套用的模板項目',\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: '通用模板',\n      selectedItems: this.getSelectedItems(),\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.isVisible = false;\n    this.reset();\n    this.closed.emit();\n  }\n  onBackdropClick(event) {\n    if (event.target === event.currentTarget) {\n      this.close();\n    }\n  }\n  reset() {\n    this.currentStep = 1;\n    this.templateGroups = [];\n  }\n  // 公共API方法\n  open() {\n    this.isVisible = true;\n    this.reset();\n    this.loadTemplatesFromAPI();\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceTemplateSelectorComponent,\n      selectors: [[\"app-space-template-selector\"]],\n      inputs: {\n        isVisible: \"isVisible\",\n        buildCaseId: \"buildCaseId\"\n      },\n      outputs: {\n        templateApplied: \"templateApplied\",\n        closed: \"closed\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 25,\n      vars: 18,\n      consts: [[1, \"space-template-modal\", 3, \"click\"], [1, \"space-template-content\", 3, \"click\"], [1, \"space-template-header\"], [1, \"space-template-title\"], [1, \"close-btn\", 3, \"click\"], [1, \"space-template-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"space-template-footer\"], [1, \"progress-info\"], [1, \"step-buttons\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"class\", \"btn btn-secondary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"template-selection\"], [1, \"section-title\"], [1, \"fas\", \"fa-layer-group\", \"mr-2\"], [1, \"template-groups\"], [\"class\", \"template-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-group\"], [1, \"template-group-header\", 3, \"click\"], [1, \"group-info\"], [1, \"group-name\"], [1, \"group-stats\"], [1, \"group-toggle\"], [1, \"template-items\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-item\"], [\"type\", \"checkbox\", 1, \"template-checkbox\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"template-info\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"item-price\"], [\"type\", \"number\", 1, \"price-input\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"confirmation-area\"], [1, \"fas\", \"fa-check-circle\", \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"summary-price\"], [1, \"selected-items-list\"], [1, \"items-list-title\"], [1, \"items-list\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"conflict-warning\", 4, \"ngIf\"], [1, \"conflict-warning\"], [1, \"warning-text\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-success\", 3, \"click\", \"disabled\"]],\n      template: function SpaceTemplateSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_div_click_0_listener($event) {\n            return ctx.onBackdropClick($event);\n          });\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_div_click_1_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵtext(4, \"\\u7A7A\\u9593\\u6A21\\u677F\\u9078\\u64C7\\u5668\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_5_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(6, \"\\u00D7\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7);\n          i0.ɵɵtext(10, \"1. \\u9078\\u64C7\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 7);\n          i0.ɵɵtext(12, \"2. \\u78BA\\u8A8D\\u5957\\u7528\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, SpaceTemplateSelectorComponent_div_13_Template, 7, 1, \"div\", 8)(14, SpaceTemplateSelectorComponent_div_14_Template, 20, 6, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"div\", 10)(17, \"span\");\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 11)(20, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_20_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(21, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, SpaceTemplateSelectorComponent_button_22_Template, 2, 0, \"button\", 13)(23, SpaceTemplateSelectorComponent_button_23_Template, 2, 1, \"button\", 14)(24, SpaceTemplateSelectorComponent_button_24_Template, 2, 1, \"button\", 15);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"show\", ctx.isVisible);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(10, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(14, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.getProgressText());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i2.NgClass, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.space-template-modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.space-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--color-basic-800);\\n  margin-bottom: 4px;\\n}\\n\\n.space-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-basic-600);\\n}\\n\\n.space-template-modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.space-template-content[_ngcontent-%COMP%] {\\n  background: var(--color-basic-100);\\n  border-radius: 12px;\\n  width: 90%;\\n  max-width: 1000px;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n\\n.space-template-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);\\n  color: var(--color-basic-100);\\n  padding: 20px 30px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.space-template-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  color: var(--color-basic-100);\\n  font-size: 24px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  border: none;\\n  background: none;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: background 0.3s ease;\\n}\\n\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n.space-template-body[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n\\n\\n\\n.step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n  gap: 10px;\\n}\\n\\n.step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n\\n.step-item.active[_ngcontent-%COMP%] {\\n  background: var(--color-primary-600);\\n  color: var(--color-basic-100);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.3);\\n}\\n\\n.step-item.completed[_ngcontent-%COMP%] {\\n  background: var(--color-success-600);\\n  color: var(--color-basic-100);\\n}\\n\\n.step-item.pending[_ngcontent-%COMP%] {\\n  background: var(--color-basic-200);\\n  color: var(--color-basic-600);\\n}\\n\\n\\n\\n.space-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-basic-800);\\n  margin-bottom: 15px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.space-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n\\n.space-card[_ngcontent-%COMP%] {\\n  border: 2px solid var(--color-basic-300);\\n  border-radius: 8px;\\n  padding: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-align: center;\\n  background: var(--color-basic-100);\\n}\\n\\n.space-card[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-primary-500);\\n  background: var(--color-primary-100);\\n}\\n\\n.space-card.selected[_ngcontent-%COMP%] {\\n  border-color: var(--color-primary-600);\\n  background: var(--color-primary-100);\\n}\\n\\n.space-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 8px;\\n}\\n\\n.space-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 4px;\\n}\\n\\n.space-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n\\n\\n.template-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.template-groups[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 15px;\\n}\\n\\n.template-group[_ngcontent-%COMP%] {\\n  border: 1px solid var(--color-basic-300);\\n  border-radius: 8px;\\n  overflow: hidden;\\n  background: var(--color-basic-100);\\n}\\n\\n.template-group-header[_ngcontent-%COMP%] {\\n  background: var(--color-basic-200);\\n  padding: 16px 20px;\\n  cursor: pointer;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  transition: background 0.3s ease;\\n}\\n\\n.template-group-header[_ngcontent-%COMP%]:hover {\\n  background: var(--color-basic-300);\\n}\\n\\n.template-group-header.active[_ngcontent-%COMP%] {\\n  background: var(--color-primary-100);\\n  border-color: var(--color-primary-400);\\n  border-left: 4px solid var(--color-primary-600);\\n}\\n\\n.group-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.group-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.group-stats[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n.group-toggle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  transition: transform 0.3s ease;\\n}\\n\\n.group-toggle.expanded[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.template-items[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n\\n.template-items.expanded[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n}\\n\\n.template-item[_ngcontent-%COMP%] {\\n  padding: 12px 20px;\\n  border-bottom: 1px solid #f0f0f0;\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  transition: background 0.2s ease;\\n}\\n\\n.template-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n\\n.template-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.template-checkbox[_ngcontent-%COMP%] {\\n  transform: scale(1.2);\\n}\\n\\n.template-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr 1fr;\\n  gap: 20px;\\n  align-items: center;\\n}\\n\\n.item-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.item-code[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 12px;\\n}\\n\\n.item-price[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n\\n.price-input[_ngcontent-%COMP%] {\\n  width: 90px;\\n  padding: 4px 8px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  text-align: right;\\n  font-size: 12px;\\n}\\n\\n\\n\\n.confirmation-area[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.selected-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.summary-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.summary-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n\\n.selected-items-list[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 15px;\\n  border-radius: 6px;\\n  margin-bottom: 15px;\\n}\\n\\n.items-list-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 10px;\\n}\\n\\n.items-list[_ngcontent-%COMP%] {\\n  margin-left: 20px;\\n  line-height: 1.6;\\n}\\n\\n.conflict-warning[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n\\n.warning-text[_ngcontent-%COMP%] {\\n  color: #856404;\\n  font-size: 14px;\\n}\\n\\n\\n\\n.space-template-footer[_ngcontent-%COMP%] {\\n  padding: 20px 30px;\\n  border-top: 1px solid #e9ecef;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background: #f8f9fa;\\n  border-radius: 0 0 12px 12px;\\n}\\n\\n.step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    transform: translateY(-50px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  .template-info[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n  .space-template-content[_ngcontent-%COMP%] {\\n    width: 95%;\\n    margin: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "TemplateGetListResponse", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_ngModelChange_1_listener", "$event", "item_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_change_1_listener", "ctx_r2", "ɵɵnextContext", "onTemplateItemChange", "ɵɵelementEnd", "ɵɵtext", "SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_ngModelChange_13_listener", "price", "SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_change_13_listener", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "name", "code", "unit", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "SpaceTemplateSelectorComponent_div_13_div_6_Template_div_click_1_listener", "group_r2", "_r1", "toggleTemplateGroup", "ɵɵtemplate", "SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template", "ɵɵclassProp", "expanded", "ɵɵtextInterpolate2", "icon", "items", "length", "getTotalPrice", "ɵɵproperty", "ɵɵelement", "SpaceTemplateSelectorComponent_div_13_div_6_Template", "templateGroups", "ɵɵtextInterpolate3", "item_r6", "getConflictCount", "SpaceTemplateSelectorComponent_div_14_li_18_Template", "SpaceTemplateSelectorComponent_div_14_div_19_Template", "getSelectedItems", "getSelectedTotalPrice", "hasConflicts", "SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener", "_r7", "previousStep", "SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener", "_r8", "nextStep", "canProceed", "SpaceTemplateSelectorComponent_button_24_Template_button_click_0_listener", "_r9", "applyTemplate", "SpaceTemplateSelectorComponent", "constructor", "templateService", "isVisible", "buildCaseId", "templateApplied", "closed", "currentStep", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "convertAPIDataToTemplateGroups", "error", "apiData", "groupMap", "Map", "for<PERSON>ach", "item", "groupName", "CGroupName", "has", "set", "get", "push", "id", "CTemplateId", "toString", "Array", "from", "entries", "map", "index", "selectSpace", "space", "loadTemplatesForSpace", "spaceId", "getDefaultTemplatesForSpace", "templates", "kitchen", "living", "group", "g", "reduce", "total", "getProgressText", "progressTexts", "config", "spaceName", "selectedItems", "totalPrice", "emit", "close", "reset", "onBackdropClick", "event", "target", "currentTarget", "open", "ɵɵdirectiveInject", "i1", "TemplateService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SpaceTemplateSelectorComponent_Template", "rf", "ctx", "SpaceTemplateSelectorComponent_Template_div_click_0_listener", "SpaceTemplateSelectorComponent_Template_div_click_1_listener", "stopPropagation", "SpaceTemplateSelectorComponent_Template_button_click_5_listener", "SpaceTemplateSelectorComponent_div_13_Template", "SpaceTemplateSelectorComponent_div_14_Template", "SpaceTemplateSelectorComponent_Template_button_click_20_listener", "SpaceTemplateSelectorComponent_button_22_Template", "SpaceTemplateSelectorComponent_button_23_Template", "SpaceTemplateSelectorComponent_button_24_Template", "ɵɵpureFunction3", "_c0", "i2", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "DefaultValueAccessor", "NumberValueAccessor", "CheckboxControlValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, TemplateGetListResponse } from 'src/services/api/models';\r\n\r\nexport interface SpaceOption {\r\n  id: string;\r\n  name: string;\r\n  icon: string;\r\n  templateCount: number;\r\n}\r\n\r\n// 擴展 API 模型以支援前端功能\r\nexport interface ExtendedTemplateItem extends TemplateGetListResponse {\r\n  selected?: boolean;\r\n  price?: number;\r\n  unit?: string;\r\n}\r\n\r\nexport interface TemplateGroup {\r\n  id: string;\r\n  name: string;\r\n  icon: string;\r\n  items: ExtendedTemplateItem[];\r\n  expanded: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: ExtendedTemplateItem[];\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule\r\n  ],\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() buildCaseId: string = '';\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n  @Output() closed = new EventEmitter<void>();\r\n\r\n  currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n  templateGroups: TemplateGroup[] = [];\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    // 自動載入模板資料\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n\r\n  loadTemplatesFromAPI() {\r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: 1, // 1=客變需求\r\n      PageIndex: 1,\r\n      PageSize: 100, // 載入足夠的資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateListForCommon API\r\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // 將API資料轉換為組件需要的格式\r\n          this.convertAPIDataToTemplateGroups(response.Entries);\r\n        } else {\r\n          // API 返回錯誤\r\n          this.templateGroups = [];\r\n        }\r\n      },\r\n      error: () => {\r\n        // HTTP 請求錯誤\r\n        this.templateGroups = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  convertAPIDataToTemplateGroups(apiData: any[]) {\r\n    // 將API資料轉換為模板群組格式\r\n    const groupMap = new Map<string, TemplateItem[]>();\r\n\r\n    apiData.forEach(item => {\r\n      const groupName = item.CGroupName || '預設群組';\r\n      if (!groupMap.has(groupName)) {\r\n        groupMap.set(groupName, []);\r\n      }\r\n\r\n      groupMap.get(groupName)!.push({\r\n        id: item.CTemplateId?.toString() || '',\r\n        name: item.CTemplateName || '',\r\n        code: item.CTemplateId?.toString() || '',\r\n        price: 0, // API中沒有價格資訊，預設為0\r\n        unit: '個',\r\n        selected: false\r\n      });\r\n    });\r\n\r\n    // 轉換為模板群組陣列\r\n    this.templateGroups = Array.from(groupMap.entries()).map(([groupName, items], index) => ({\r\n      id: `group-${index}`,\r\n      name: groupName,\r\n      icon: '📋', // 預設圖示\r\n      items: items,\r\n      expanded: false\r\n    }));\r\n  }\r\n\r\n  selectSpace(space: SpaceOption) {\r\n    // 移除此方法，不再需要\r\n  }\r\n\r\n  loadTemplatesForSpace(spaceId: string) {\r\n    // 根據空間ID載入對應的模板群組\r\n    // 這裡可以調用API或使用預設資料\r\n    this.templateGroups = this.getDefaultTemplatesForSpace(spaceId);\r\n  }\r\n\r\n  getDefaultTemplatesForSpace(spaceId: string): TemplateGroup[] {\r\n    const templates: Record<string, TemplateGroup[]> = {\r\n      kitchen: [\r\n        {\r\n          id: 'kitchen-standard',\r\n          name: '廚房標準配備',\r\n          icon: '🔧',\r\n          expanded: false,\r\n          items: [\r\n            { id: 'kt001', name: '洗碗機', code: 'KT001', price: 38000, unit: '台', selected: false },\r\n            { id: 'kt002', name: '烤箱', code: 'KT002', price: 25000, unit: '台', selected: false },\r\n            { id: 'kt003', name: '抽油煙機升級', code: 'KT003', price: 15000, unit: '台', selected: false }\r\n          ]\r\n        },\r\n        {\r\n          id: 'kitchen-premium',\r\n          name: '廚房高級配備',\r\n          icon: '⭐',\r\n          expanded: false,\r\n          items: [\r\n            { id: 'kt004', name: '中島檯面', code: 'KT004', price: 80000, unit: '組', selected: false },\r\n            { id: 'kt005', name: '智能電磁爐', code: 'KT005', price: 45000, unit: '台', selected: false }\r\n          ]\r\n        }\r\n      ],\r\n      living: [\r\n        {\r\n          id: 'living-basic',\r\n          name: '客廳基本配備',\r\n          icon: '🏠',\r\n          expanded: false,\r\n          items: [\r\n            { id: 'lv001', name: '投影設備', code: 'LV001', price: 50000, unit: '組', selected: false },\r\n            { id: 'lv002', name: '音響系統', code: 'LV002', price: 35000, unit: '組', selected: false }\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n\r\n    return templates[spaceId] || [];\r\n  }\r\n\r\n  toggleTemplateGroup(group: TemplateGroup) {\r\n    // 收合其他群組\r\n    this.templateGroups.forEach(g => {\r\n      if (g.id !== group.id) {\r\n        g.expanded = false;\r\n      }\r\n    });\r\n\r\n    // 切換當前群組\r\n    group.expanded = !group.expanded;\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  getTotalPrice(items: TemplateItem[]): number {\r\n    return items.reduce((total, item) => total + (item.selected ? item.price : 0), 0);\r\n  }\r\n\r\n  getSelectedItems(): TemplateItem[] {\r\n    const selected: TemplateItem[] = [];\r\n    this.templateGroups.forEach(group => {\r\n      group.items.forEach(item => {\r\n        if (item.selected) {\r\n          selected.push(item);\r\n        }\r\n      });\r\n    });\r\n    return selected;\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    return this.getSelectedItems().reduce((total, item) => total + item.price, 0);\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要套用的模板項目',\r\n      2: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: 'common', // 通用模板，不特定空間\r\n      spaceName: '通用模板',\r\n      selectedItems: this.getSelectedItems(),\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.isVisible = false;\r\n    this.reset();\r\n    this.closed.emit();\r\n  }\r\n\r\n  onBackdropClick(event: Event) {\r\n    if (event.target === event.currentTarget) {\r\n      this.close();\r\n    }\r\n  }\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.templateGroups = [];\r\n  }\r\n\r\n  // 公共API方法\r\n  open() {\r\n    this.isVisible = true;\r\n    this.reset();\r\n    this.loadTemplatesFromAPI();\r\n  }\r\n}\r\n", "<!-- 空間模板選擇器共用元件 -->\r\n<div class=\"space-template-modal\" [class.show]=\"isVisible\" (click)=\"onBackdropClick($event)\">\r\n  <div class=\"space-template-content\" (click)=\"$event.stopPropagation()\">\r\n    <div class=\"space-template-header\">\r\n      <div class=\"space-template-title\">空間模板選擇器</div>\r\n      <button class=\"close-btn\" (click)=\"close()\">&times;</button>\r\n    </div>\r\n\r\n    <div class=\"space-template-body\">\r\n      <!-- 步驟導航 -->\r\n      <div class=\"step-nav\">\r\n        <div class=\"step-item\" [ngClass]=\"{\r\n          'active': currentStep === 1,\r\n          'completed': currentStep > 1,\r\n          'pending': currentStep < 1\r\n        }\">1. 選擇模板</div>\r\n        <div class=\"step-item\" [ngClass]=\"{\r\n          'active': currentStep === 2,\r\n          'completed': currentStep > 2,\r\n          'pending': currentStep < 2\r\n        }\">2. 確認套用</div>\r\n      </div>\r\n\r\n      <!-- 步驟1: 選擇模板 -->\r\n      <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n        <div class=\"template-selection\">\r\n          <div class=\"section-title\">\r\n            <i class=\"fas fa-layer-group mr-2\"></i>選擇模板群組\r\n          </div>\r\n          <div class=\"template-groups\">\r\n            <div *ngFor=\"let group of templateGroups\" class=\"template-group\">\r\n              <div class=\"template-group-header\" [class.active]=\"group.expanded\" (click)=\"toggleTemplateGroup(group)\">\r\n                <div class=\"group-info\">\r\n                  <div class=\"group-name\">{{ group.icon }} {{ group.name }}</div>\r\n                  <div class=\"group-stats\">{{ group.items.length }}個項目 • ${{ getTotalPrice(group.items) | number }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"group-toggle\" [class.expanded]=\"group.expanded\">▼</div>\r\n              </div>\r\n              <div class=\"template-items\" [class.expanded]=\"group.expanded\">\r\n                <div *ngFor=\"let item of group.items\" class=\"template-item\">\r\n                  <input type=\"checkbox\" class=\"template-checkbox\" [(ngModel)]=\"item.selected\"\r\n                    (change)=\"onTemplateItemChange()\">\r\n                  <div class=\"template-info\">\r\n                    <div>\r\n                      <div class=\"item-name\">{{ item.name }}</div>\r\n                      <div class=\"item-code\">{{ item.code }}</div>\r\n                    </div>\r\n                    <div>{{ item.unit }}</div>\r\n                    <div class=\"item-price\">${{ item.price | number }}</div>\r\n                    <input type=\"number\" class=\"price-input\" [(ngModel)]=\"item.price\" (change)=\"onTemplateItemChange()\">\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 步驟2: 確認套用 -->\r\n      <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n        <div class=\"confirmation-area\">\r\n          <div class=\"section-title\">\r\n            <i class=\"fas fa-check-circle mr-2\"></i>確認套用詳情\r\n          </div>\r\n\r\n          <div class=\"selected-summary\">\r\n            <div class=\"summary-text\">\r\n              將套用 <strong>通用模板</strong>：{{ getSelectedItems().length }}個模板項目\r\n            </div>\r\n            <div class=\"summary-price\">總價值 ${{ getSelectedTotalPrice() | number }}</div>\r\n          </div>\r\n\r\n          <div class=\"selected-items-list\">\r\n            <div class=\"items-list-title\">套用項目清單：</div>\r\n            <ul class=\"items-list\">\r\n              <li *ngFor=\"let item of getSelectedItems()\">\r\n                {{ item.name }} ({{ item.code }}) - ${{ item.price | number }}\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div *ngIf=\"hasConflicts()\" class=\"conflict-warning\">\r\n            <div class=\"warning-text\">\r\n              <strong><i class=\"fas fa-exclamation-triangle mr-1\"></i>衝突檢測：</strong>\r\n              檢測到 {{ getConflictCount() }} 個項目可能與現有需求重複，系統將自動處理衝突項目。\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"space-template-footer\">\r\n      <div class=\"progress-info\">\r\n        <span>{{ getProgressText() }}</span>\r\n      </div>\r\n      <div class=\"step-buttons\">\r\n        <button class=\"btn btn-secondary\" (click)=\"close()\">取消</button>\r\n        <button *ngIf=\"currentStep > 1\" class=\"btn btn-secondary\" (click)=\"previousStep()\">上一步</button>\r\n        <button *ngIf=\"currentStep < 2\" class=\"btn btn-primary\" [disabled]=\"!canProceed()\"\r\n          (click)=\"nextStep()\">下一步</button>\r\n        <button *ngIf=\"currentStep === 2\" class=\"btn btn-success\" [disabled]=\"getSelectedItems().length === 0\"\r\n          (click)=\"applyTemplate()\">確認套用</button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAA8BC,uBAAuB,QAAQ,yBAAyB;;;;;;;;;;;;;ICqCpEC,EADF,CAAAC,cAAA,cAA4D,gBAEtB;IADaD,EAAA,CAAAE,gBAAA,2BAAAC,2FAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAJ,OAAA,CAAAK,QAAA,EAAAN,MAAA,MAAAC,OAAA,CAAAK,QAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA2B;IAC1EJ,EAAA,CAAAY,UAAA,oBAAAC,oFAAA;MAAAb,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAE,oBAAA,EAAsB;IAAA,EAAC;IADnChB,EAAA,CAAAiB,YAAA,EACoC;IAGhCjB,EAFJ,CAAAC,cAAA,cAA2B,UACpB,cACoB;IAAAD,EAAA,CAAAkB,MAAA,GAAe;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAC5CjB,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAkB,MAAA,GAAe;IACxClB,EADwC,CAAAiB,YAAA,EAAM,EACxC;IACNjB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAkB,MAAA,GAAe;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAC1BjB,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAkB,MAAA,IAA0B;;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IACxDjB,EAAA,CAAAC,cAAA,iBAAoG;IAA3DD,EAAA,CAAAE,gBAAA,2BAAAiB,4FAAAf,MAAA;MAAA,MAAAC,OAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAJ,OAAA,CAAAe,KAAA,EAAAhB,MAAA,MAAAC,OAAA,CAAAe,KAAA,GAAAhB,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAACJ,EAAA,CAAAY,UAAA,oBAAAS,qFAAA;MAAArB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAE,oBAAA,EAAsB;IAAA,EAAC;IAEvGhB,EAFI,CAAAiB,YAAA,EAAoG,EAChG,EACF;;;;IAX6CjB,EAAA,CAAAsB,SAAA,EAA2B;IAA3BtB,EAAA,CAAAuB,gBAAA,YAAAlB,OAAA,CAAAK,QAAA,CAA2B;IAIjDV,EAAA,CAAAsB,SAAA,GAAe;IAAftB,EAAA,CAAAwB,iBAAA,CAAAnB,OAAA,CAAAoB,IAAA,CAAe;IACfzB,EAAA,CAAAsB,SAAA,GAAe;IAAftB,EAAA,CAAAwB,iBAAA,CAAAnB,OAAA,CAAAqB,IAAA,CAAe;IAEnC1B,EAAA,CAAAsB,SAAA,GAAe;IAAftB,EAAA,CAAAwB,iBAAA,CAAAnB,OAAA,CAAAsB,IAAA,CAAe;IACI3B,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,QAAAxB,OAAA,CAAAe,KAAA,MAA0B;IACTpB,EAAA,CAAAsB,SAAA,GAAwB;IAAxBtB,EAAA,CAAAuB,gBAAA,YAAAlB,OAAA,CAAAe,KAAA,CAAwB;;;;;;IAnBvEpB,EADF,CAAAC,cAAA,cAAiE,cACyC;IAArCD,EAAA,CAAAY,UAAA,mBAAAkB,0EAAA;MAAA,MAAAC,QAAA,GAAA/B,EAAA,CAAAM,aAAA,CAAA0B,GAAA,EAAAxB,SAAA;MAAA,MAAAM,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAmB,mBAAA,CAAAF,QAAA,CAA0B;IAAA,EAAC;IAEnG/B,EADF,CAAAC,cAAA,cAAwB,cACE;IAAAD,EAAA,CAAAkB,MAAA,GAAiC;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAC/DjB,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAkB,MAAA,GACzB;;IACFlB,EADE,CAAAiB,YAAA,EAAM,EACF;IACNjB,EAAA,CAAAC,cAAA,cAA4D;IAAAD,EAAA,CAAAkB,MAAA,aAAC;IAC/DlB,EAD+D,CAAAiB,YAAA,EAAM,EAC/D;IACNjB,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAkC,UAAA,KAAAC,2DAAA,mBAA4D;IAchEnC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IAvB+BjB,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAoC,WAAA,WAAAL,QAAA,CAAAM,QAAA,CAA+B;IAEtCrC,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAsC,kBAAA,KAAAP,QAAA,CAAAQ,IAAA,OAAAR,QAAA,CAAAN,IAAA,KAAiC;IAChCzB,EAAA,CAAAsB,SAAA,GACzB;IADyBtB,EAAA,CAAAsC,kBAAA,KAAAP,QAAA,CAAAS,KAAA,CAAAC,MAAA,iCAAAzC,EAAA,CAAA6B,WAAA,QAAAf,MAAA,CAAA4B,aAAA,CAAAX,QAAA,CAAAS,KAAA,QACzB;IAEwBxC,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAoC,WAAA,aAAAL,QAAA,CAAAM,QAAA,CAAiC;IAEjCrC,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAoC,WAAA,aAAAL,QAAA,CAAAM,QAAA,CAAiC;IACrCrC,EAAA,CAAAsB,SAAA,EAAc;IAAdtB,EAAA,CAAA2C,UAAA,YAAAZ,QAAA,CAAAS,KAAA,CAAc;;;;;IAd1CxC,EAFJ,CAAAC,cAAA,cAAoD,cAClB,cACH;IACzBD,EAAA,CAAA4C,SAAA,YAAuC;IAAA5C,EAAA,CAAAkB,MAAA,4CACzC;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAkC,UAAA,IAAAW,oDAAA,oBAAiE;IA2BvE7C,EAFI,CAAAiB,YAAA,EAAM,EACF,EACF;;;;IA3BuBjB,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAA2C,UAAA,YAAA7B,MAAA,CAAAgC,cAAA,CAAiB;;;;;IA8CtC9C,EAAA,CAAAC,cAAA,SAA4C;IAC1CD,EAAA,CAAAkB,MAAA,GACF;;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;;;;IADHjB,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAA+C,kBAAA,MAAAC,OAAA,CAAAvB,IAAA,QAAAuB,OAAA,CAAAtB,IAAA,WAAA1B,EAAA,CAAA6B,WAAA,OAAAmB,OAAA,CAAA5B,KAAA,OACF;;;;;IAMApB,EAFJ,CAAAC,cAAA,cAAqD,cACzB,aAChB;IAAAD,EAAA,CAAA4C,SAAA,YAAgD;IAAA5C,EAAA,CAAAkB,MAAA,qCAAK;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IACtEjB,EAAA,CAAAkB,MAAA,GACF;IACFlB,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IAFFjB,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAA4B,kBAAA,yBAAAd,MAAA,CAAAmC,gBAAA,+JACF;;;;;IAxBFjD,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAA4C,SAAA,YAAwC;IAAA5C,EAAA,CAAAkB,MAAA,4CAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAGJjB,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAkB,MAAA,2BAAI;IAAAlB,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAkB,MAAA,+BAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IAAAjB,EAAA,CAAAkB,MAAA,IAC3B;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAkB,MAAA,IAA2C;;IACxElB,EADwE,CAAAiB,YAAA,EAAM,EACxE;IAGJjB,EADF,CAAAC,cAAA,eAAiC,eACD;IAAAD,EAAA,CAAAkB,MAAA,kDAAO;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAC3CjB,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAkC,UAAA,KAAAgB,oDAAA,iBAA4C;IAIhDlD,EADE,CAAAiB,YAAA,EAAK,EACD;IAENjB,EAAA,CAAAkC,UAAA,KAAAiB,qDAAA,kBAAqD;IAOzDnD,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IArB2BjB,EAAA,CAAAsB,SAAA,IAC3B;IAD2BtB,EAAA,CAAA4B,kBAAA,WAAAd,MAAA,CAAAsC,gBAAA,GAAAX,MAAA,oCAC3B;IAC2BzC,EAAA,CAAAsB,SAAA,GAA2C;IAA3CtB,EAAA,CAAA4B,kBAAA,yBAAA5B,EAAA,CAAA6B,WAAA,QAAAf,MAAA,CAAAuC,qBAAA,QAA2C;IAM/CrD,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAA2C,UAAA,YAAA7B,MAAA,CAAAsC,gBAAA,GAAqB;IAMxCpD,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAA2C,UAAA,SAAA7B,MAAA,CAAAwC,YAAA,GAAoB;;;;;;IAgB5BtD,EAAA,CAAAC,cAAA,iBAAmF;IAAzBD,EAAA,CAAAY,UAAA,mBAAA2C,0EAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAAkD,GAAA;MAAA,MAAA1C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAA2C,YAAA,EAAc;IAAA,EAAC;IAACzD,EAAA,CAAAkB,MAAA,yBAAG;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;;;;;;IAC/FjB,EAAA,CAAAC,cAAA,iBACuB;IAArBD,EAAA,CAAAY,UAAA,mBAAA8C,0EAAA;MAAA1D,EAAA,CAAAM,aAAA,CAAAqD,GAAA;MAAA,MAAA7C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAA8C,QAAA,EAAU;IAAA,EAAC;IAAC5D,EAAA,CAAAkB,MAAA,yBAAG;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;;;;IADqBjB,EAAA,CAAA2C,UAAA,cAAA7B,MAAA,CAAA+C,UAAA,GAA0B;;;;;;IAElF7D,EAAA,CAAAC,cAAA,iBAC4B;IAA1BD,EAAA,CAAAY,UAAA,mBAAAkD,0EAAA;MAAA9D,EAAA,CAAAM,aAAA,CAAAyD,GAAA;MAAA,MAAAjD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAkD,aAAA,EAAe;IAAA,EAAC;IAAChE,EAAA,CAAAkB,MAAA,+BAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;;;;IADiBjB,EAAA,CAAA2C,UAAA,aAAA7B,MAAA,CAAAsC,gBAAA,GAAAX,MAAA,OAA4C;;;ADxD9G,OAAM,MAAOwB,8BAA8B;EASzCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAR1B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,WAAW,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAG,IAAI1E,YAAY,EAAuB;IACzD,KAAA2E,MAAM,GAAG,IAAI3E,YAAY,EAAQ;IAE3C,KAAA4E,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAA1B,cAAc,GAAoB,EAAE;EAEoB;EAExD2B,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB;IACA,MAAMC,mBAAmB,GAAwB;MAC/CC,aAAa,EAAE,CAAC;MAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAACZ,eAAe,CAACa,4CAA4C,CAAC;MAChEC,IAAI,EAAEN;KACP,CAAC,CAACO,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACA,IAAI,CAACC,8BAA8B,CAACH,QAAQ,CAACE,OAAO,CAAC;QACvD,CAAC,MAAM;UACL;UACA,IAAI,CAACxC,cAAc,GAAG,EAAE;QAC1B;MACF,CAAC;MACD0C,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAAC1C,cAAc,GAAG,EAAE;MAC1B;KACD,CAAC;EACJ;EAEAyC,8BAA8BA,CAACE,OAAc;IAC3C;IACA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,EAA0B;IAElDF,OAAO,CAACG,OAAO,CAACC,IAAI,IAAG;MACrB,MAAMC,SAAS,GAAGD,IAAI,CAACE,UAAU,IAAI,MAAM;MAC3C,IAAI,CAACL,QAAQ,CAACM,GAAG,CAACF,SAAS,CAAC,EAAE;QAC5BJ,QAAQ,CAACO,GAAG,CAACH,SAAS,EAAE,EAAE,CAAC;MAC7B;MAEAJ,QAAQ,CAACQ,GAAG,CAACJ,SAAS,CAAE,CAACK,IAAI,CAAC;QAC5BC,EAAE,EAAEP,IAAI,CAACQ,WAAW,EAAEC,QAAQ,EAAE,IAAI,EAAE;QACtC7E,IAAI,EAAEoE,IAAI,CAACd,aAAa,IAAI,EAAE;QAC9BrD,IAAI,EAAEmE,IAAI,CAACQ,WAAW,EAAEC,QAAQ,EAAE,IAAI,EAAE;QACxClF,KAAK,EAAE,CAAC;QAAE;QACVO,IAAI,EAAE,GAAG;QACTjB,QAAQ,EAAE;OACX,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACoC,cAAc,GAAGyD,KAAK,CAACC,IAAI,CAACd,QAAQ,CAACe,OAAO,EAAE,CAAC,CAACC,GAAG,CAAC,CAAC,CAACZ,SAAS,EAAEtD,KAAK,CAAC,EAAEmE,KAAK,MAAM;MACvFP,EAAE,EAAE,SAASO,KAAK,EAAE;MACpBlF,IAAI,EAAEqE,SAAS;MACfvD,IAAI,EAAE,IAAI;MAAE;MACZC,KAAK,EAAEA,KAAK;MACZH,QAAQ,EAAE;KACX,CAAC,CAAC;EACL;EAEAuE,WAAWA,CAACC,KAAkB;IAC5B;EAAA;EAGFC,qBAAqBA,CAACC,OAAe;IACnC;IACA;IACA,IAAI,CAACjE,cAAc,GAAG,IAAI,CAACkE,2BAA2B,CAACD,OAAO,CAAC;EACjE;EAEAC,2BAA2BA,CAACD,OAAe;IACzC,MAAME,SAAS,GAAoC;MACjDC,OAAO,EAAE,CACP;QACEd,EAAE,EAAE,kBAAkB;QACtB3E,IAAI,EAAE,QAAQ;QACdc,IAAI,EAAE,IAAI;QACVF,QAAQ,EAAE,KAAK;QACfG,KAAK,EAAE,CACL;UAAE4D,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,KAAK;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE,EACrF;UAAE0F,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,IAAI;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE,EACpF;UAAE0F,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,QAAQ;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE;OAE3F,EACD;QACE0F,EAAE,EAAE,iBAAiB;QACrB3E,IAAI,EAAE,QAAQ;QACdc,IAAI,EAAE,GAAG;QACTF,QAAQ,EAAE,KAAK;QACfG,KAAK,EAAE,CACL;UAAE4D,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE,EACtF;UAAE0F,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,OAAO;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE;OAE1F,CACF;MACDyG,MAAM,EAAE,CACN;QACEf,EAAE,EAAE,cAAc;QAClB3E,IAAI,EAAE,QAAQ;QACdc,IAAI,EAAE,IAAI;QACVF,QAAQ,EAAE,KAAK;QACfG,KAAK,EAAE,CACL;UAAE4D,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE,EACtF;UAAE0F,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE;OAEzF;KAEJ;IAED,OAAOuG,SAAS,CAACF,OAAO,CAAC,IAAI,EAAE;EACjC;EAEA9E,mBAAmBA,CAACmF,KAAoB;IACtC;IACA,IAAI,CAACtE,cAAc,CAAC8C,OAAO,CAACyB,CAAC,IAAG;MAC9B,IAAIA,CAAC,CAACjB,EAAE,KAAKgB,KAAK,CAAChB,EAAE,EAAE;QACrBiB,CAAC,CAAChF,QAAQ,GAAG,KAAK;MACpB;IACF,CAAC,CAAC;IAEF;IACA+E,KAAK,CAAC/E,QAAQ,GAAG,CAAC+E,KAAK,CAAC/E,QAAQ;EAClC;EAEArB,oBAAoBA,CAAA;IAClB;EAAA;EAGF0B,aAAaA,CAACF,KAAqB;IACjC,OAAOA,KAAK,CAAC8E,MAAM,CAAC,CAACC,KAAK,EAAE1B,IAAI,KAAK0B,KAAK,IAAI1B,IAAI,CAACnF,QAAQ,GAAGmF,IAAI,CAACzE,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACnF;EAEAgC,gBAAgBA,CAAA;IACd,MAAM1C,QAAQ,GAAmB,EAAE;IACnC,IAAI,CAACoC,cAAc,CAAC8C,OAAO,CAACwB,KAAK,IAAG;MAClCA,KAAK,CAAC5E,KAAK,CAACoD,OAAO,CAACC,IAAI,IAAG;QACzB,IAAIA,IAAI,CAACnF,QAAQ,EAAE;UACjBA,QAAQ,CAACyF,IAAI,CAACN,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOnF,QAAQ;EACjB;EAEA2C,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACD,gBAAgB,EAAE,CAACkE,MAAM,CAAC,CAACC,KAAK,EAAE1B,IAAI,KAAK0B,KAAK,GAAG1B,IAAI,CAACzE,KAAK,EAAE,CAAC,CAAC;EAC/E;EAEAyC,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACW,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACpB,gBAAgB,EAAE,CAACX,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAmB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACW,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAf,YAAYA,CAAA;IACV,IAAI,IAAI,CAACe,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAgD,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAACjD,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAlB,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACF,gBAAgB,EAAE,CAACX,MAAM,GAAG,CAAC;EAC3C;EAEAQ,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACK,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAU,aAAaA,CAAA;IACX,MAAM0D,MAAM,GAAwB;MAClCX,OAAO,EAAE,QAAQ;MAAE;MACnBY,SAAS,EAAE,MAAM;MACjBC,aAAa,EAAE,IAAI,CAACxE,gBAAgB,EAAE;MACtCyE,UAAU,EAAE,IAAI,CAACxE,qBAAqB;KACvC;IAED,IAAI,CAACiB,eAAe,CAACwD,IAAI,CAACJ,MAAM,CAAC;IACjC,IAAI,CAACK,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAAC3D,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC4D,KAAK,EAAE;IACZ,IAAI,CAACzD,MAAM,CAACuD,IAAI,EAAE;EACpB;EAEAG,eAAeA,CAACC,KAAY;IAC1B,IAAIA,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,aAAa,EAAE;MACxC,IAAI,CAACL,KAAK,EAAE;IACd;EACF;EAEQC,KAAKA,CAAA;IACX,IAAI,CAACxD,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC1B,cAAc,GAAG,EAAE;EAC1B;EAEA;EACAuF,IAAIA,CAAA;IACF,IAAI,CAACjE,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC4D,KAAK,EAAE;IACZ,IAAI,CAACtD,oBAAoB,EAAE;EAC7B;;;uCA9OWT,8BAA8B,EAAAjE,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAA9BvE,8BAA8B;MAAAwE,SAAA;MAAAC,MAAA;QAAAtE,SAAA;QAAAC,WAAA;MAAA;MAAAsE,OAAA;QAAArE,eAAA;QAAAC,MAAA;MAAA;MAAAqE,UAAA;MAAAC,QAAA,GAAA7I,EAAA,CAAA8I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5C3CpJ,EAAA,CAAAC,cAAA,aAA6F;UAAlCD,EAAA,CAAAY,UAAA,mBAAA0I,6DAAAlJ,MAAA;YAAA,OAASiJ,GAAA,CAAApB,eAAA,CAAA7H,MAAA,CAAuB;UAAA,EAAC;UAC1FJ,EAAA,CAAAC,cAAA,aAAuE;UAAnCD,EAAA,CAAAY,UAAA,mBAAA2I,6DAAAnJ,MAAA;YAAA,OAASA,MAAA,CAAAoJ,eAAA,EAAwB;UAAA,EAAC;UAElExJ,EADF,CAAAC,cAAA,aAAmC,aACC;UAAAD,EAAA,CAAAkB,MAAA,iDAAO;UAAAlB,EAAA,CAAAiB,YAAA,EAAM;UAC/CjB,EAAA,CAAAC,cAAA,gBAA4C;UAAlBD,EAAA,CAAAY,UAAA,mBAAA6I,gEAAA;YAAA,OAASJ,GAAA,CAAAtB,KAAA,EAAO;UAAA,EAAC;UAAC/H,EAAA,CAAAkB,MAAA,aAAO;UACrDlB,EADqD,CAAAiB,YAAA,EAAS,EACxD;UAKFjB,EAHJ,CAAAC,cAAA,aAAiC,aAET,aAKjB;UAAAD,EAAA,CAAAkB,MAAA,mCAAO;UAAAlB,EAAA,CAAAiB,YAAA,EAAM;UAChBjB,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAkB,MAAA,mCAAO;UACZlB,EADY,CAAAiB,YAAA,EAAM,EACZ;UAuCNjB,EApCA,CAAAkC,UAAA,KAAAwH,8CAAA,iBAAoD,KAAAC,8CAAA,kBAoCA;UA8BtD3J,EAAA,CAAAiB,YAAA,EAAM;UAIFjB,EAFJ,CAAAC,cAAA,cAAmC,eACN,YACnB;UAAAD,EAAA,CAAAkB,MAAA,IAAuB;UAC/BlB,EAD+B,CAAAiB,YAAA,EAAO,EAChC;UAEJjB,EADF,CAAAC,cAAA,eAA0B,kBAC4B;UAAlBD,EAAA,CAAAY,UAAA,mBAAAgJ,iEAAA;YAAA,OAASP,GAAA,CAAAtB,KAAA,EAAO;UAAA,EAAC;UAAC/H,EAAA,CAAAkB,MAAA,oBAAE;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UAI/DjB,EAHA,CAAAkC,UAAA,KAAA2H,iDAAA,qBAAmF,KAAAC,iDAAA,qBAE5D,KAAAC,iDAAA,qBAEK;UAIpC/J,EAHM,CAAAiB,YAAA,EAAM,EACF,EACF,EACF;;;UAzG4BjB,EAAA,CAAAoC,WAAA,SAAAiH,GAAA,CAAAjF,SAAA,CAAwB;UAU3BpE,EAAA,CAAAsB,SAAA,GAIrB;UAJqBtB,EAAA,CAAA2C,UAAA,YAAA3C,EAAA,CAAAgK,eAAA,KAAAC,GAAA,EAAAZ,GAAA,CAAA7E,WAAA,QAAA6E,GAAA,CAAA7E,WAAA,MAAA6E,GAAA,CAAA7E,WAAA,MAIrB;UACqBxE,EAAA,CAAAsB,SAAA,GAIrB;UAJqBtB,EAAA,CAAA2C,UAAA,YAAA3C,EAAA,CAAAgK,eAAA,KAAAC,GAAA,EAAAZ,GAAA,CAAA7E,WAAA,QAAA6E,GAAA,CAAA7E,WAAA,MAAA6E,GAAA,CAAA7E,WAAA,MAIrB;UAIExE,EAAA,CAAAsB,SAAA,GAAuB;UAAvBtB,EAAA,CAAA2C,UAAA,SAAA0G,GAAA,CAAA7E,WAAA,OAAuB;UAoCvBxE,EAAA,CAAAsB,SAAA,EAAuB;UAAvBtB,EAAA,CAAA2C,UAAA,SAAA0G,GAAA,CAAA7E,WAAA,OAAuB;UAkCrBxE,EAAA,CAAAsB,SAAA,GAAuB;UAAvBtB,EAAA,CAAAwB,iBAAA,CAAA6H,GAAA,CAAA7B,eAAA,GAAuB;UAIpBxH,EAAA,CAAAsB,SAAA,GAAqB;UAArBtB,EAAA,CAAA2C,UAAA,SAAA0G,GAAA,CAAA7E,WAAA,KAAqB;UACrBxE,EAAA,CAAAsB,SAAA,EAAqB;UAArBtB,EAAA,CAAA2C,UAAA,SAAA0G,GAAA,CAAA7E,WAAA,KAAqB;UAErBxE,EAAA,CAAAsB,SAAA,EAAuB;UAAvBtB,EAAA,CAAA2C,UAAA,SAAA0G,GAAA,CAAA7E,WAAA,OAAuB;;;qBD9DpC3E,YAAY,EAAAqK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,WAAA,EACZxK,WAAW,EAAAyK,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,4BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}