{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { tap } from 'rxjs';\nlet SpaceComponent = class SpaceComponent extends BaseComponent {\n  constructor(allow, dialogService, _spaceService, _templateService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._spaceService = _spaceService;\n    this._templateService = _templateService;\n    this.message = message;\n    this.valid = valid;\n    this.Math = Math; // 讓模板可以使用 Math 函數\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.spaceList = [];\n    this.spaceDetail = {};\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.searchStatus = null;\n    // 模板相關屬性\n    this.templateList = [];\n    this.templateDetail = {};\n    this.templatePageIndex = 1;\n    this.templatePageSize = 10;\n    this.templateTotalRecords = 0;\n    // 查看模板明細相關屬性\n    this.selectedTemplateDetail = null;\n    this.templateDetailSpaces = [];\n    this.isLoadingTemplateDetail = false;\n    // 新增模板時的空間選擇器相關屬性\n    this.availableSpaces = [];\n    this.selectedSpacesForTemplate = [];\n    this.spacePageIndex = 1;\n    this.spacePageSize = 10;\n    this.spaceTotalRecords = 0;\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    // 控制UI顯示\n    this.showSpacePickList = false;\n    this.allSpacesSelected = false;\n  }\n  // 開啟編輯模板模態框\n  openEditTemplateModal(template) {\n    this.templateDetail = {\n      ...template,\n      CStatus: template.CStatus || 1\n    };\n    this.dialogService.open(this.editTemplateModal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  // 更新模板\n  onUpdateTemplate(ref) {\n    if (!this.templateDetail.CTemplateName?.trim()) {\n      this.message.showErrorMSG('請填寫模板名稱');\n      return;\n    }\n    const updateArgs = {\n      CTemplateId: this.templateDetail.CTemplateId,\n      CTemplateName: this.templateDetail.CTemplateName.trim(),\n      CStatus: this.templateDetail.CStatus || 1\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: updateArgs\n    }).subscribe({\n      next: response => {\n        if (response.CReturnCode === \"SUCCESS\") {\n          this.message.showSucessMSG('模板更新成功');\n          this.getTemplateList();\n          this.onClose(ref);\n        } else {\n          this.message.showErrorMSG(response.CMessage || '模板更新失敗');\n        }\n      },\n      error: error => {\n        console.error('更新模板錯誤:', error);\n        this.message.showErrorMSG('更新模板失敗，請稍後再試');\n      }\n    });\n  }\n  ngOnInit() {\n    this.getSpaceList();\n  }\n  getSpaceList() {\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CName: this.searchKeyword || null,\n        CLocation: this.searchLocation || null,\n        CStatus: this.searchStatus\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    })).subscribe();\n  }\n  onSearch() {\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getSpaceList();\n  }\n  openCreateModal(ref) {\n    this.spaceDetail = {\n      CName: '',\n      CLocation: '',\n      CStatus: 1 // 1 = 啟用, 0 = 停用\n    };\n    this.dialogService.open(ref);\n  }\n  openEditModal(ref, item) {\n    this.getSpaceById(item.CSpaceID, ref);\n  }\n  getSpaceById(spaceId, ref) {\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\n      body: {\n        CSpaceID: spaceId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceDetail = {\n          ...res.Entries\n        };\n        this.dialogService.open(ref);\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    });\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._spaceService.apiSpaceSaveSpacePost$Json({\n      body: this.spaceDetail\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n        this.getSpaceList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    })).subscribe();\n  }\n  deleteSpace(item) {\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\n        body: {\n          CSpaceID: item.CSpaceId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"刪除成功\");\n          this.getSpaceList();\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除失敗');\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[空間名稱]', this.spaceDetail.CName);\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CName, 50);\n    this.valid.required('[所屬區域]', this.spaceDetail.CLocation);\n    this.valid.isStringMaxLength('[所屬區域]', this.spaceDetail.CLocation, 50);\n  }\n  // 模板管理方法\n  openTemplateManagementModal(ref) {\n    this.templatePageIndex = 1; // 重置到第一頁\n    this.getTemplateList();\n    this.dialogService.open(ref, {\n      hasScroll: true\n    });\n  }\n  getTemplateList() {\n    this._templateService.apiTemplateGetTemplateListPost$Json({\n      body: {\n        PageIndex: this.templatePageIndex,\n        PageSize: this.templatePageSize\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode === 0) {\n        this.templateList = res.Entries.map(item => ({\n          CTemplateId: item.CTemplateId,\n          CTemplateName: item.CTemplateName,\n          CCreateDt: item.CCreateDt,\n          CUpdateDt: item.CUpdateDt,\n          CCreator: item.CCreator,\n          CUpdator: item.CUpdator,\n          CStatus: item.CStatus\n        }));\n        this.templateTotalRecords = res.TotalItems || 0;\n      } else {\n        this.message.showErrorMSG(res.Message || '載入模板資料失敗');\n      }\n    });\n  }\n  openCreateTemplateModal(ref) {\n    this.templateDetail = {\n      CTemplateName: '',\n      CTemplateType: 1,\n      CStatus: 1\n    };\n    this.selectedSpacesForTemplate = [];\n    this.spacePageIndex = 1; // 重置到第一頁\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.getSpacesForPickList();\n    this.dialogService.open(ref, {\n      hasScroll: true\n    });\n  }\n  getSpacesForPickList() {\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: {\n        PageIndex: this.spacePageIndex,\n        PageSize: this.spacePageSize,\n        CName: this.spaceSearchKeyword || null,\n        CLocation: this.spaceSearchLocation || null,\n        CStatus: 1 // 只顯示啟用的空間\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode === 0) {\n        this.availableSpaces = res.Entries.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CName: item.CName,\n          CLocation: item.CLocation,\n          selected: this.selectedSpacesForTemplate.some(selected => selected.CSpaceID === item.CSpaceID)\n        }));\n        this.spaceTotalRecords = res.TotalItems;\n        this.updateAllSpacesSelectedState();\n      } else {\n        this.message.showErrorMSG(res.Message || '載入空間資料失敗');\n      }\n    });\n  }\n  onSpaceSearch() {\n    this.spacePageIndex = 1;\n    this.getSpacesForPickList();\n  }\n  onSpaceReset() {\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.getSpacesForPickList();\n  }\n  spacePageChanged(newPage) {\n    this.spacePageIndex = newPage;\n    this.getSpacesForPickList();\n  }\n  templatePageChanged(newPage) {\n    this.templatePageIndex = newPage;\n    this.getTemplateList();\n  }\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      this.selectedSpacesForTemplate.push({\n        ...space\n      });\n    } else {\n      const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\n      if (index > -1) {\n        this.selectedSpacesForTemplate.splice(index, 1);\n      }\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  toggleAllSpaces() {\n    this.allSpacesSelected = !this.allSpacesSelected;\n    this.availableSpaces.forEach(space => {\n      const wasSelected = space.selected;\n      space.selected = this.allSpacesSelected;\n      if (this.allSpacesSelected && !wasSelected) {\n        // 新選中的空間，添加到已選列表\n        const exists = this.selectedSpacesForTemplate.find(s => s.CSpaceID === space.CSpaceID);\n        if (!exists) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else if (!this.allSpacesSelected && wasSelected) {\n        // 取消選中的空間，從已選列表移除\n        const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\n        if (index > -1) {\n          this.selectedSpacesForTemplate.splice(index, 1);\n        }\n      }\n    });\n  }\n  updateAllSpacesSelectedState() {\n    this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  removeSelectedSpace(space) {\n    const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\n    if (index > -1) {\n      this.selectedSpacesForTemplate.splice(index, 1);\n    }\n    // 更新可用空間列表中的選擇狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  onSubmitTemplate(ref) {\n    this.templateValidation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.selectedSpacesForTemplate.length === 0) {\n      this.message.showErrorMSG('請至少選擇一個空間');\n      return;\n    }\n    const templateDetails = this.selectedSpacesForTemplate.map(space => ({\n      CReleateId: space.CSpaceID,\n      CReleateName: space.CName,\n      CLocation: space.CLocation || null\n    }));\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: {\n        ...this.templateDetail,\n        Details: templateDetails\n      }\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"模板建立成功\");\n        ref.close();\n        this.getTemplateList(); // 刷新模板列表\n      } else {\n        this.message.showErrorMSG(res.Message || '建立模板失敗');\n      }\n    });\n  }\n  deleteTemplate(template) {\n    if (confirm(`您確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\n        body: {\n          CTemplateId: template.CTemplateId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"模板刪除成功\");\n          // 檢查刪除後當前頁是否還有資料，如果沒有則跳轉到上一頁\n          const currentPageStartIndex = (this.templatePageIndex - 1) * this.templatePageSize;\n          const remainingItems = this.templateTotalRecords - 1; // 刪除一筆後的總數\n          if (remainingItems > 0 && currentPageStartIndex >= remainingItems) {\n            this.templatePageIndex = Math.max(1, this.templatePageIndex - 1);\n          }\n          this.getTemplateList();\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除模板失敗');\n        }\n      });\n    }\n  }\n  viewTemplateDetail(template, ref) {\n    this.selectedTemplateDetail = template;\n    this.templateDetailSpaces = [];\n    this.isLoadingTemplateDetail = true;\n    this.getTemplateDetailSpaces(template.CTemplateId);\n    this.dialogService.open(ref, {\n      hasScroll: true\n    });\n  }\n  getTemplateDetailSpaces(templateId) {\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: {\n        templateId: templateId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode === 0) {\n        // 直接使用API回傳的型別\n        this.templateDetailSpaces = res.Entries.map(item => ({\n          CReleateId: item.CReleateId,\n          CReleateName: item.CReleateName,\n          CLocation: item.CLocation || null\n        }));\n      } else {\n        this.message.showErrorMSG(res.Message || '載入模板明細失敗');\n        this.templateDetailSpaces = [];\n      }\n      this.isLoadingTemplateDetail = false;\n    });\n  }\n  templateValidation() {\n    this.valid.clear();\n    this.valid.required('[模板名稱]', this.templateDetail.CTemplateName);\n    this.valid.isStringMaxLength('[模板名稱]', this.templateDetail.CTemplateName, 100);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n};\n__decorate([ViewChild('createTemplateModal', {\n  static: false\n})], SpaceComponent.prototype, \"createTemplateModal\", void 0);\n__decorate([ViewChild('templateDetailModal', {\n  static: false\n})], SpaceComponent.prototype, \"templateDetailModal\", void 0);\n__decorate([ViewChild('editTemplateModal', {\n  static: false\n})], SpaceComponent.prototype, \"editTemplateModal\", void 0);\nSpaceComponent = __decorate([Component({\n  selector: 'ngx-space',\n  templateUrl: './space.component.html',\n  styleUrls: ['./space.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, BreadcrumbComponent]\n})], SpaceComponent);\nexport { SpaceComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "SharedModule", "CommonModule", "BaseComponent", "BreadcrumbComponent", "tap", "SpaceComponent", "constructor", "allow", "dialogService", "_spaceService", "_templateService", "message", "valid", "Math", "pageFirst", "pageSize", "pageIndex", "totalRecords", "spaceList", "spaceDetail", "searchKeyword", "searchLocation", "searchStatus", "templateList", "templateDetail", "templatePageIndex", "templatePageSize", "templateTotalRecords", "selectedTemplateDetail", "templateDetailSpaces", "isLoadingTemplateDetail", "availableSpaces", "selectedSpacesForTemplate", "spacePageIndex", "spacePageSize", "spaceTotalRecords", "spaceSearchKeyword", "spaceSearchLocation", "showSpacePickList", "allSpacesSelected", "openEditTemplateModal", "template", "CStatus", "open", "editTemplateModal", "context", "autoFocus", "onUpdateTemplate", "ref", "CTemplateName", "trim", "showErrorMSG", "updateArgs", "CTemplateId", "apiTemplateSaveTemplatePost$Json", "body", "subscribe", "next", "response", "CReturnCode", "showSucessMSG", "getTemplateList", "onClose", "CMessage", "error", "console", "ngOnInit", "getSpaceList", "apiSpaceGetSpaceListPost$Json", "PageIndex", "PageSize", "CName", "CLocation", "pipe", "res", "Entries", "StatusCode", "TotalItems", "Message", "onSearch", "onReset", "pageChanged", "newPage", "openCreateModal", "openEditModal", "item", "getSpaceById", "CSpaceID", "spaceId", "apiSpaceGetSpaceByIdPost$Json", "onSubmit", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpaceSaveSpacePost$Json", "close", "deleteSpace", "confirm", "apiSpaceDeleteSpacePost$Json", "CSpaceId", "clear", "required", "isStringMaxLength", "openTemplateManagementModal", "hasScroll", "apiTemplateGetTemplateListPost$Json", "map", "CCreateDt", "CUpdateDt", "CCreator", "CUpdator", "openCreateTemplateModal", "CTemplateType", "getSpacesForPickList", "selected", "some", "updateAllSpacesSelectedState", "onSpaceSearch", "onSpaceReset", "spacePageChanged", "templatePageChanged", "toggleSpaceSelection", "space", "push", "index", "findIndex", "s", "splice", "toggleAllSpaces", "for<PERSON>ach", "wasSelected", "exists", "find", "every", "removeSelectedSpace", "availableSpace", "onSubmitTemplate", "templateValidation", "templateDetails", "CReleateId", "CReleateName", "Details", "deleteTemplate", "apiTemplateDeleteTemplatePost$Json", "currentPageStartIndex", "remainingItems", "max", "viewTemplateDetail", "getTemplateDetailSpaces", "templateId", "apiTemplateGetTemplateDetailByIdPost$Json", "__decorate", "static", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\space\\space.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { tap } from 'rxjs';\r\nimport { GetSpaceListResponse, SaveSpaceRequest } from 'src/services/api/models';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-space',\r\n  templateUrl: './space.component.html',\r\n  styleUrls: ['./space.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent,\r\n  ],\r\n})\r\n\r\nexport class SpaceComponent extends BaseComponent implements OnInit {\r\n  Math = Math; // 讓模板可以使用 Math 函數\r\n\r\n  @ViewChild('createTemplateModal', { static: false }) createTemplateModal!: TemplateRef<any>;\r\n  @ViewChild('templateDetailModal', { static: false }) templateDetailModal!: TemplateRef<any>;\r\n  @ViewChild('editTemplateModal', { static: false }) editTemplateModal!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _spaceService: SpaceService,\r\n    private _templateService: TemplateService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  spaceList: GetSpaceListResponse[] = [];\r\n  spaceDetail: SaveSpaceRequest = {};\r\n  searchKeyword: string = '';\r\n  searchLocation: string = '';\r\n  searchStatus: number | null = null;\r\n\r\n  // 模板相關屬性\r\n  templateList: TemplateItem[] = [];\r\n  templateDetail: SaveTemplateArgs = {};\r\n  templatePageIndex = 1;\r\n  templatePageSize = 10;\r\n  templateTotalRecords = 0;\r\n\r\n  // 開啟編輯模板模態框\r\n  openEditTemplateModal(template: TemplateItem): void {\r\n    this.templateDetail = { ...template, CStatus: template.CStatus || 1 };\r\n    this.dialogService.open(this.editTemplateModal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  // 更新模板\r\n  onUpdateTemplate(ref: any): void {\r\n    if (!this.templateDetail.CTemplateName?.trim()) {\r\n      this.message.showErrorMSG('請填寫模板名稱');\r\n      return;\r\n    }\r\n\r\n    const updateArgs: SaveTemplateArgs = {\r\n      CTemplateId: this.templateDetail.CTemplateId,\r\n      CTemplateName: this.templateDetail.CTemplateName.trim(),\r\n      CStatus: this.templateDetail.CStatus || 1\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({\r\n      body: updateArgs\r\n    }).subscribe({\r\n      next: (response: any) => {\r\n        if (response.CReturnCode === \"SUCCESS\") {\r\n          this.message.showSucessMSG('模板更新成功');\r\n          this.getTemplateList();\r\n          this.onClose(ref);\r\n        } else {\r\n          this.message.showErrorMSG(response.CMessage || '模板更新失敗');\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        console.error('更新模板錯誤:', error);\r\n        this.message.showErrorMSG('更新模板失敗，請稍後再試');\r\n      }\r\n    });\r\n  }\r\n\r\n  // 查看模板明細相關屬性\r\n  selectedTemplateDetail: TemplateItem | null = null;\r\n  templateDetailSpaces: TemplateDetailSpaceItem[] = [];\r\n  isLoadingTemplateDetail = false;\r\n\r\n  // 新增模板時的空間選擇器相關屬性\r\n  availableSpaces: SpacePickListItem[] = [];\r\n  selectedSpacesForTemplate: SpacePickListItem[] = [];\r\n  spacePageIndex = 1;\r\n  spacePageSize = 10;\r\n  spaceTotalRecords = 0;\r\n  spaceSearchKeyword = '';\r\n  spaceSearchLocation = '';\r\n\r\n  // 控制UI顯示\r\n  showSpacePickList = false;\r\n  allSpacesSelected = false;\r\n\r\n  override ngOnInit(): void {\r\n    this.getSpaceList();\r\n  }\r\n\r\n  getSpaceList() {\r\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\r\n      body: {\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CName: this.searchKeyword || null,\r\n        CLocation: this.searchLocation || null,\r\n        CStatus: this.searchStatus\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.spaceList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSearch() {\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  onReset() {\r\n    this.searchKeyword = '';\r\n    this.searchLocation = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  openCreateModal(ref: any) {\r\n    this.spaceDetail = {\r\n      CName: '',\r\n      CLocation: '',\r\n      CStatus: 1 // 1 = 啟用, 0 = 停用\r\n    };\r\n    this.dialogService.open(ref);\r\n  }\r\n\r\n  openEditModal(ref: any, item: any) {\r\n    this.getSpaceById(item.CSpaceID, ref);\r\n  }\r\n\r\n  getSpaceById(spaceId: number, ref: any) {\r\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\r\n      body: { CSpaceID: spaceId }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.spaceDetail = { ...res.Entries };\r\n        this.dialogService.open(ref);\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this._spaceService.apiSpaceSaveSpacePost$Json({\r\n      body: this.spaceDetail\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  deleteSpace(item: any) {\r\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\r\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\r\n        body: { CSpaceID: item.CSpaceId }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"刪除成功\");\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '刪除失敗');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[空間名稱]', this.spaceDetail.CName);\r\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CName, 50);\r\n    this.valid.required('[所屬區域]', this.spaceDetail.CLocation);\r\n    this.valid.isStringMaxLength('[所屬區域]', this.spaceDetail.CLocation, 50);\r\n  }\r\n\r\n  // 模板管理方法\r\n  openTemplateManagementModal(ref: any) {\r\n    this.templatePageIndex = 1; // 重置到第一頁\r\n    this.getTemplateList();\r\n    this.dialogService.open(ref, { hasScroll: true });\r\n  }\r\n\r\n  getTemplateList() {\r\n    this._templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: {\r\n        PageIndex: this.templatePageIndex,\r\n        PageSize: this.templatePageSize\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode === 0) {\r\n        this.templateList = res.Entries.map(item => ({\r\n          CTemplateId: item.CTemplateId!,\r\n          CTemplateName: item.CTemplateName!,\r\n          CCreateDt: item.CCreateDt!,\r\n          CUpdateDt: item.CUpdateDt!,\r\n          CCreator: item.CCreator,\r\n          CUpdator: item.CUpdator,\r\n          CStatus: item.CStatus\r\n        }));\r\n        this.templateTotalRecords = res.TotalItems || 0;\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入模板資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  openCreateTemplateModal(ref: any) {\r\n    this.templateDetail = {\r\n      CTemplateName: '',\r\n      CTemplateType: 1,\r\n      CStatus: 1\r\n    };\r\n    this.selectedSpacesForTemplate = [];\r\n    this.spacePageIndex = 1; // 重置到第一頁\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.getSpacesForPickList();\r\n    this.dialogService.open(ref, { hasScroll: true });\r\n  }\r\n\r\n  getSpacesForPickList() {\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\r\n      body: {\r\n        PageIndex: this.spacePageIndex,\r\n        PageSize: this.spacePageSize,\r\n        CName: this.spaceSearchKeyword || null,\r\n        CLocation: this.spaceSearchLocation || null,\r\n        CStatus: 1 // 只顯示啟用的空間\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode === 0) {\r\n        this.availableSpaces = res.Entries.map(item => ({\r\n          CSpaceID: item.CSpaceID!,\r\n          CName: item.CName!,\r\n          CLocation: item.CLocation,\r\n          selected: this.selectedSpacesForTemplate.some(selected => selected.CSpaceID === item.CSpaceID)\r\n        }));\r\n        this.spaceTotalRecords = res.TotalItems!;\r\n        this.updateAllSpacesSelectedState();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入空間資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  onSpaceSearch() {\r\n    this.spacePageIndex = 1;\r\n    this.getSpacesForPickList();\r\n  }\r\n\r\n  onSpaceReset() {\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.spacePageIndex = 1;\r\n    this.getSpacesForPickList();\r\n  }\r\n\r\n  spacePageChanged(newPage: number) {\r\n    this.spacePageIndex = newPage;\r\n    this.getSpacesForPickList();\r\n  }\r\n\r\n  templatePageChanged(newPage: number) {\r\n    this.templatePageIndex = newPage;\r\n    this.getTemplateList();\r\n  }\r\n\r\n  toggleSpaceSelection(space: SpacePickListItem) {\r\n    space.selected = !space.selected;\r\n    if (space.selected) {\r\n      this.selectedSpacesForTemplate.push({ ...space });\r\n    } else {\r\n      const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\r\n      if (index > -1) {\r\n        this.selectedSpacesForTemplate.splice(index, 1);\r\n      }\r\n    }\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  toggleAllSpaces() {\r\n    this.allSpacesSelected = !this.allSpacesSelected;\r\n    this.availableSpaces.forEach(space => {\r\n      const wasSelected = space.selected;\r\n      space.selected = this.allSpacesSelected;\r\n\r\n      if (this.allSpacesSelected && !wasSelected) {\r\n        // 新選中的空間，添加到已選列表\r\n        const exists = this.selectedSpacesForTemplate.find(s => s.CSpaceID === space.CSpaceID);\r\n        if (!exists) {\r\n          this.selectedSpacesForTemplate.push({ ...space });\r\n        }\r\n      } else if (!this.allSpacesSelected && wasSelected) {\r\n        // 取消選中的空間，從已選列表移除\r\n        const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\r\n        if (index > -1) {\r\n          this.selectedSpacesForTemplate.splice(index, 1);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  updateAllSpacesSelectedState() {\r\n    this.allSpacesSelected = this.availableSpaces.length > 0 &&\r\n      this.availableSpaces.every(space => space.selected);\r\n  }\r\n\r\n  removeSelectedSpace(space: SpacePickListItem) {\r\n    const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\r\n    if (index > -1) {\r\n      this.selectedSpacesForTemplate.splice(index, 1);\r\n    }\r\n\r\n    // 更新可用空間列表中的選擇狀態\r\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\r\n    if (availableSpace) {\r\n      availableSpace.selected = false;\r\n    }\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  onSubmitTemplate(ref: any) {\r\n    this.templateValidation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    if (this.selectedSpacesForTemplate.length === 0) {\r\n      this.message.showErrorMSG('請至少選擇一個空間');\r\n      return;\r\n    }\r\n\r\n    const templateDetails: SaveTemplateDetailArgs[] = this.selectedSpacesForTemplate.map(space => ({\r\n      CReleateId: space.CSpaceID,\r\n      CReleateName: space.CName,\r\n      CLocation: space.CLocation || null\r\n    }));\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({\r\n      body: {\r\n        ...this.templateDetail,\r\n        Details: templateDetails\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"模板建立成功\");\r\n        ref.close();\r\n        this.getTemplateList(); // 刷新模板列表\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '建立模板失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  deleteTemplate(template: TemplateItem) {\r\n    if (confirm(`您確定要刪除模板「${template.CTemplateName}」嗎？`)) {\r\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\r\n        body: { CTemplateId: template.CTemplateId }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"模板刪除成功\");\r\n\r\n          // 檢查刪除後當前頁是否還有資料，如果沒有則跳轉到上一頁\r\n          const currentPageStartIndex = (this.templatePageIndex - 1) * this.templatePageSize;\r\n          const remainingItems = this.templateTotalRecords - 1; // 刪除一筆後的總數\r\n\r\n          if (remainingItems > 0 && currentPageStartIndex >= remainingItems) {\r\n            this.templatePageIndex = Math.max(1, this.templatePageIndex - 1);\r\n          }\r\n\r\n          this.getTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '刪除模板失敗');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  viewTemplateDetail(template: TemplateItem, ref: any) {\r\n    this.selectedTemplateDetail = template;\r\n    this.templateDetailSpaces = [];\r\n    this.isLoadingTemplateDetail = true;\r\n    this.getTemplateDetailSpaces(template.CTemplateId);\r\n    this.dialogService.open(ref, { hasScroll: true });\r\n  }\r\n\r\n  getTemplateDetailSpaces(templateId: number) {\r\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: { templateId: templateId }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode === 0) {\r\n        // 直接使用API回傳的型別\r\n        this.templateDetailSpaces = res.Entries.map(item => ({\r\n          CReleateId: item.CReleateId!,\r\n          CReleateName: item.CReleateName!,\r\n          CLocation: item.CLocation || null\r\n        }));\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入模板明細失敗');\r\n        this.templateDetailSpaces = [];\r\n      }\r\n      this.isLoadingTemplateDetail = false;\r\n    });\r\n  }\r\n\r\n  templateValidation() {\r\n    this.valid.clear();\r\n    this.valid.required('[模板名稱]', this.templateDetail.CTemplateName);\r\n    this.valid.isStringMaxLength('[模板名稱]', this.templateDetail.CTemplateName, 100);\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,SAAS,QAAqB,eAAe;AAEzE,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,kCAAkC;AAKhE,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,GAAG,QAAQ,MAAM;AAqBnB,IAAMC,cAAc,GAApB,MAAMA,cAAe,SAAQH,aAAa;EAO/CI,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,aAA2B,EAC3BC,gBAAiC,EACjCC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAZf,KAAAC,IAAI,GAAGA,IAAI,CAAC,CAAC;IAiBJ,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,SAAS,GAA2B,EAAE;IACtC,KAAAC,WAAW,GAAqB,EAAE;IAClC,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAkB,IAAI;IAElC;IACA,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAAC,cAAc,GAAqB,EAAE;IACrC,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,oBAAoB,GAAG,CAAC;IA2CxB;IACA,KAAAC,sBAAsB,GAAwB,IAAI;IAClD,KAAAC,oBAAoB,GAA8B,EAAE;IACpD,KAAAC,uBAAuB,GAAG,KAAK;IAE/B;IACA,KAAAC,eAAe,GAAwB,EAAE;IACzC,KAAAC,yBAAyB,GAAwB,EAAE;IACnD,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,mBAAmB,GAAG,EAAE;IAExB;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,iBAAiB,GAAG,KAAK;EA7EzB;EAoBA;EACAC,qBAAqBA,CAACC,QAAsB;IAC1C,IAAI,CAACjB,cAAc,GAAG;MAAE,GAAGiB,QAAQ;MAAEC,OAAO,EAAED,QAAQ,CAACC,OAAO,IAAI;IAAC,CAAE;IACrE,IAAI,CAAClC,aAAa,CAACmC,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;MAC9CC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEA;EACAC,gBAAgBA,CAACC,GAAQ;IACvB,IAAI,CAAC,IAAI,CAACxB,cAAc,CAACyB,aAAa,EAAEC,IAAI,EAAE,EAAE;MAC9C,IAAI,CAACvC,OAAO,CAACwC,YAAY,CAAC,SAAS,CAAC;MACpC;IACF;IAEA,MAAMC,UAAU,GAAqB;MACnCC,WAAW,EAAE,IAAI,CAAC7B,cAAc,CAAC6B,WAAW;MAC5CJ,aAAa,EAAE,IAAI,CAACzB,cAAc,CAACyB,aAAa,CAACC,IAAI,EAAE;MACvDR,OAAO,EAAE,IAAI,CAAClB,cAAc,CAACkB,OAAO,IAAI;KACzC;IAED,IAAI,CAAChC,gBAAgB,CAAC4C,gCAAgC,CAAC;MACrDC,IAAI,EAAEH;KACP,CAAC,CAACI,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,CAACC,WAAW,KAAK,SAAS,EAAE;UACtC,IAAI,CAAChD,OAAO,CAACiD,aAAa,CAAC,QAAQ,CAAC;UACpC,IAAI,CAACC,eAAe,EAAE;UACtB,IAAI,CAACC,OAAO,CAACd,GAAG,CAAC;QACnB,CAAC,MAAM;UACL,IAAI,CAACrC,OAAO,CAACwC,YAAY,CAACO,QAAQ,CAACK,QAAQ,IAAI,QAAQ,CAAC;QAC1D;MACF,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAACrD,OAAO,CAACwC,YAAY,CAAC,cAAc,CAAC;MAC3C;KACD,CAAC;EACJ;EAoBSe,QAAQA,CAAA;IACf,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC1D,aAAa,CAAC2D,6BAA6B,CAAC;MACtDb,IAAI,EAAE;QACJc,SAAS,EAAE,IAAI,CAACrD,SAAS;QACzBsD,QAAQ,EAAE,IAAI,CAACvD,QAAQ;QACvBwD,KAAK,EAAE,IAAI,CAACnD,aAAa,IAAI,IAAI;QACjCoD,SAAS,EAAE,IAAI,CAACnD,cAAc,IAAI,IAAI;QACtCqB,OAAO,EAAE,IAAI,CAACpB;;KAEjB,CAAC,CAACmD,IAAI,CACLrE,GAAG,CAACsE,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC1D,SAAS,GAAGwD,GAAG,CAACC,OAAO;QAC5B,IAAI,CAAC1D,YAAY,GAAGyD,GAAG,CAACG,UAAW;MACrC,CAAC,MAAM;QACL,IAAI,CAAClE,OAAO,CAACwC,YAAY,CAACuB,GAAG,CAACI,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,CACH,CAACtB,SAAS,EAAE;EACf;EAEAuB,QAAQA,CAAA;IACN,IAAI,CAAC/D,SAAS,GAAG,CAAC;IAClB,IAAI,CAACmD,YAAY,EAAE;EACrB;EAEAa,OAAOA,CAAA;IACL,IAAI,CAAC5D,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACN,SAAS,GAAG,CAAC;IAClB,IAAI,CAACmD,YAAY,EAAE;EACrB;EAEAc,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAClE,SAAS,GAAGkE,OAAO;IACxB,IAAI,CAACf,YAAY,EAAE;EACrB;EAEAgB,eAAeA,CAACnC,GAAQ;IACtB,IAAI,CAAC7B,WAAW,GAAG;MACjBoD,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACb9B,OAAO,EAAE,CAAC,CAAC;KACZ;IACD,IAAI,CAAClC,aAAa,CAACmC,IAAI,CAACK,GAAG,CAAC;EAC9B;EAEAoC,aAAaA,CAACpC,GAAQ,EAAEqC,IAAS;IAC/B,IAAI,CAACC,YAAY,CAACD,IAAI,CAACE,QAAQ,EAAEvC,GAAG,CAAC;EACvC;EAEAsC,YAAYA,CAACE,OAAe,EAAExC,GAAQ;IACpC,IAAI,CAACvC,aAAa,CAACgF,6BAA6B,CAAC;MAC/ClC,IAAI,EAAE;QAAEgC,QAAQ,EAAEC;MAAO;KAC1B,CAAC,CAAChC,SAAS,CAACkB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACzD,WAAW,GAAG;UAAE,GAAGuD,GAAG,CAACC;QAAO,CAAE;QACrC,IAAI,CAACnE,aAAa,CAACmC,IAAI,CAACK,GAAG,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAACrC,OAAO,CAACwC,YAAY,CAACuB,GAAG,CAACI,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEAY,QAAQA,CAAC1C,GAAQ;IACf,IAAI,CAAC2C,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC/E,KAAK,CAACgF,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClF,OAAO,CAACmF,aAAa,CAAC,IAAI,CAAClF,KAAK,CAACgF,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACnF,aAAa,CAACsF,0BAA0B,CAAC;MAC5CxC,IAAI,EAAE,IAAI,CAACpC;KACZ,CAAC,CAACsD,IAAI,CACLrE,GAAG,CAACsE,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACjE,OAAO,CAACiD,aAAa,CAAC,MAAM,CAAC;QAClCZ,GAAG,CAACgD,KAAK,EAAE;QACX,IAAI,CAAC7B,YAAY,EAAE;MACrB,CAAC,MAAM;QACL,IAAI,CAACxD,OAAO,CAACwC,YAAY,CAACuB,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,CACH,CAACtB,SAAS,EAAE;EACf;EAEAyC,WAAWA,CAACZ,IAAS;IACnB,IAAIa,OAAO,CAAC,eAAe,CAAC,EAAE;MAC5B,IAAI,CAACzF,aAAa,CAAC0F,4BAA4B,CAAC;QAC9C5C,IAAI,EAAE;UAAEgC,QAAQ,EAAEF,IAAI,CAACe;QAAQ;OAChC,CAAC,CAAC5C,SAAS,CAACkB,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACjE,OAAO,CAACiD,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACO,YAAY,EAAE;QACrB,CAAC,MAAM;UACL,IAAI,CAACxD,OAAO,CAACwC,YAAY,CAACuB,GAAG,CAACI,OAAO,IAAI,MAAM,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EACF;EAEAa,UAAUA,CAAA;IACR,IAAI,CAAC/E,KAAK,CAACyF,KAAK,EAAE;IAClB,IAAI,CAACzF,KAAK,CAAC0F,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACnF,WAAW,CAACoD,KAAK,CAAC;IACrD,IAAI,CAAC3D,KAAK,CAAC2F,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACpF,WAAW,CAACoD,KAAK,EAAE,EAAE,CAAC;IAClE,IAAI,CAAC3D,KAAK,CAAC0F,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACnF,WAAW,CAACqD,SAAS,CAAC;IACzD,IAAI,CAAC5D,KAAK,CAAC2F,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACpF,WAAW,CAACqD,SAAS,EAAE,EAAE,CAAC;EACxE;EAEA;EACAgC,2BAA2BA,CAACxD,GAAQ;IAClC,IAAI,CAACvB,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACoC,eAAe,EAAE;IACtB,IAAI,CAACrD,aAAa,CAACmC,IAAI,CAACK,GAAG,EAAE;MAAEyD,SAAS,EAAE;IAAI,CAAE,CAAC;EACnD;EAEA5C,eAAeA,CAAA;IACb,IAAI,CAACnD,gBAAgB,CAACgG,mCAAmC,CAAC;MACxDnD,IAAI,EAAE;QACJc,SAAS,EAAE,IAAI,CAAC5C,iBAAiB;QACjC6C,QAAQ,EAAE,IAAI,CAAC5C;;KAElB,CAAC,CAAC8B,SAAS,CAACkB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACvC,IAAI,CAACrD,YAAY,GAAGmD,GAAG,CAACC,OAAO,CAACgC,GAAG,CAACtB,IAAI,KAAK;UAC3ChC,WAAW,EAAEgC,IAAI,CAAChC,WAAY;UAC9BJ,aAAa,EAAEoC,IAAI,CAACpC,aAAc;UAClC2D,SAAS,EAAEvB,IAAI,CAACuB,SAAU;UAC1BC,SAAS,EAAExB,IAAI,CAACwB,SAAU;UAC1BC,QAAQ,EAAEzB,IAAI,CAACyB,QAAQ;UACvBC,QAAQ,EAAE1B,IAAI,CAAC0B,QAAQ;UACvBrE,OAAO,EAAE2C,IAAI,CAAC3C;SACf,CAAC,CAAC;QACH,IAAI,CAACf,oBAAoB,GAAG+C,GAAG,CAACG,UAAU,IAAI,CAAC;MACjD,CAAC,MAAM;QACL,IAAI,CAAClE,OAAO,CAACwC,YAAY,CAACuB,GAAG,CAACI,OAAO,IAAI,UAAU,CAAC;MACtD;IACF,CAAC,CAAC;EACJ;EAEAkC,uBAAuBA,CAAChE,GAAQ;IAC9B,IAAI,CAACxB,cAAc,GAAG;MACpByB,aAAa,EAAE,EAAE;MACjBgE,aAAa,EAAE,CAAC;MAChBvE,OAAO,EAAE;KACV;IACD,IAAI,CAACV,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC,CAAC;IACzB,IAAI,CAACG,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC6E,oBAAoB,EAAE;IAC3B,IAAI,CAAC1G,aAAa,CAACmC,IAAI,CAACK,GAAG,EAAE;MAAEyD,SAAS,EAAE;IAAI,CAAE,CAAC;EACnD;EAEAS,oBAAoBA,CAAA;IAClB,IAAI,CAACzG,aAAa,CAAC2D,6BAA6B,CAAC;MAC/Cb,IAAI,EAAE;QACJc,SAAS,EAAE,IAAI,CAACpC,cAAc;QAC9BqC,QAAQ,EAAE,IAAI,CAACpC,aAAa;QAC5BqC,KAAK,EAAE,IAAI,CAACnC,kBAAkB,IAAI,IAAI;QACtCoC,SAAS,EAAE,IAAI,CAACnC,mBAAmB,IAAI,IAAI;QAC3CK,OAAO,EAAE,CAAC,CAAC;;KAEd,CAAC,CAACc,SAAS,CAACkB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACvC,IAAI,CAAC7C,eAAe,GAAG2C,GAAG,CAACC,OAAO,CAACgC,GAAG,CAACtB,IAAI,KAAK;UAC9CE,QAAQ,EAAEF,IAAI,CAACE,QAAS;UACxBhB,KAAK,EAAEc,IAAI,CAACd,KAAM;UAClBC,SAAS,EAAEa,IAAI,CAACb,SAAS;UACzB2C,QAAQ,EAAE,IAAI,CAACnF,yBAAyB,CAACoF,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAAC5B,QAAQ,KAAKF,IAAI,CAACE,QAAQ;SAC9F,CAAC,CAAC;QACH,IAAI,CAACpD,iBAAiB,GAAGuC,GAAG,CAACG,UAAW;QACxC,IAAI,CAACwC,4BAA4B,EAAE;MACrC,CAAC,MAAM;QACL,IAAI,CAAC1G,OAAO,CAACwC,YAAY,CAACuB,GAAG,CAACI,OAAO,IAAI,UAAU,CAAC;MACtD;IACF,CAAC,CAAC;EACJ;EAEAwC,aAAaA,CAAA;IACX,IAAI,CAACrF,cAAc,GAAG,CAAC;IACvB,IAAI,CAACiF,oBAAoB,EAAE;EAC7B;EAEAK,YAAYA,CAAA;IACV,IAAI,CAACnF,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACJ,cAAc,GAAG,CAAC;IACvB,IAAI,CAACiF,oBAAoB,EAAE;EAC7B;EAEAM,gBAAgBA,CAACtC,OAAe;IAC9B,IAAI,CAACjD,cAAc,GAAGiD,OAAO;IAC7B,IAAI,CAACgC,oBAAoB,EAAE;EAC7B;EAEAO,mBAAmBA,CAACvC,OAAe;IACjC,IAAI,CAACzD,iBAAiB,GAAGyD,OAAO;IAChC,IAAI,CAACrB,eAAe,EAAE;EACxB;EAEA6D,oBAAoBA,CAACC,KAAwB;IAC3CA,KAAK,CAACR,QAAQ,GAAG,CAACQ,KAAK,CAACR,QAAQ;IAChC,IAAIQ,KAAK,CAACR,QAAQ,EAAE;MAClB,IAAI,CAACnF,yBAAyB,CAAC4F,IAAI,CAAC;QAAE,GAAGD;MAAK,CAAE,CAAC;IACnD,CAAC,MAAM;MACL,MAAME,KAAK,GAAG,IAAI,CAAC7F,yBAAyB,CAAC8F,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACxC,QAAQ,KAAKoC,KAAK,CAACpC,QAAQ,CAAC;MAC1F,IAAIsC,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAAC7F,yBAAyB,CAACgG,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACjD;IACF;IACA,IAAI,CAACR,4BAA4B,EAAE;EACrC;EAEAY,eAAeA,CAAA;IACb,IAAI,CAAC1F,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAChD,IAAI,CAACR,eAAe,CAACmG,OAAO,CAACP,KAAK,IAAG;MACnC,MAAMQ,WAAW,GAAGR,KAAK,CAACR,QAAQ;MAClCQ,KAAK,CAACR,QAAQ,GAAG,IAAI,CAAC5E,iBAAiB;MAEvC,IAAI,IAAI,CAACA,iBAAiB,IAAI,CAAC4F,WAAW,EAAE;QAC1C;QACA,MAAMC,MAAM,GAAG,IAAI,CAACpG,yBAAyB,CAACqG,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACxC,QAAQ,KAAKoC,KAAK,CAACpC,QAAQ,CAAC;QACtF,IAAI,CAAC6C,MAAM,EAAE;UACX,IAAI,CAACpG,yBAAyB,CAAC4F,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACnD;MACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAACpF,iBAAiB,IAAI4F,WAAW,EAAE;QACjD;QACA,MAAMN,KAAK,GAAG,IAAI,CAAC7F,yBAAyB,CAAC8F,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACxC,QAAQ,KAAKoC,KAAK,CAACpC,QAAQ,CAAC;QAC1F,IAAIsC,KAAK,GAAG,CAAC,CAAC,EAAE;UACd,IAAI,CAAC7F,yBAAyB,CAACgG,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;QACjD;MACF;IACF,CAAC,CAAC;EACJ;EAEAR,4BAA4BA,CAAA;IAC1B,IAAI,CAAC9E,iBAAiB,GAAG,IAAI,CAACR,eAAe,CAAC8D,MAAM,GAAG,CAAC,IACtD,IAAI,CAAC9D,eAAe,CAACuG,KAAK,CAACX,KAAK,IAAIA,KAAK,CAACR,QAAQ,CAAC;EACvD;EAEAoB,mBAAmBA,CAACZ,KAAwB;IAC1C,MAAME,KAAK,GAAG,IAAI,CAAC7F,yBAAyB,CAAC8F,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACxC,QAAQ,KAAKoC,KAAK,CAACpC,QAAQ,CAAC;IAC1F,IAAIsC,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC7F,yBAAyB,CAACgG,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IACjD;IAEA;IACA,MAAMW,cAAc,GAAG,IAAI,CAACzG,eAAe,CAACsG,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACxC,QAAQ,KAAKoC,KAAK,CAACpC,QAAQ,CAAC;IACpF,IAAIiD,cAAc,EAAE;MAClBA,cAAc,CAACrB,QAAQ,GAAG,KAAK;IACjC;IACA,IAAI,CAACE,4BAA4B,EAAE;EACrC;EAEAoB,gBAAgBA,CAACzF,GAAQ;IACvB,IAAI,CAAC0F,kBAAkB,EAAE;IACzB,IAAI,IAAI,CAAC9H,KAAK,CAACgF,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClF,OAAO,CAACmF,aAAa,CAAC,IAAI,CAAClF,KAAK,CAACgF,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,IAAI,CAAC5D,yBAAyB,CAAC6D,MAAM,KAAK,CAAC,EAAE;MAC/C,IAAI,CAAClF,OAAO,CAACwC,YAAY,CAAC,WAAW,CAAC;MACtC;IACF;IAEA,MAAMwF,eAAe,GAA6B,IAAI,CAAC3G,yBAAyB,CAAC2E,GAAG,CAACgB,KAAK,KAAK;MAC7FiB,UAAU,EAAEjB,KAAK,CAACpC,QAAQ;MAC1BsD,YAAY,EAAElB,KAAK,CAACpD,KAAK;MACzBC,SAAS,EAAEmD,KAAK,CAACnD,SAAS,IAAI;KAC/B,CAAC,CAAC;IAEH,IAAI,CAAC9D,gBAAgB,CAAC4C,gCAAgC,CAAC;MACrDC,IAAI,EAAE;QACJ,GAAG,IAAI,CAAC/B,cAAc;QACtBsH,OAAO,EAAEH;;KAEZ,CAAC,CAACnF,SAAS,CAACkB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACjE,OAAO,CAACiD,aAAa,CAAC,QAAQ,CAAC;QACpCZ,GAAG,CAACgD,KAAK,EAAE;QACX,IAAI,CAACnC,eAAe,EAAE,CAAC,CAAC;MAC1B,CAAC,MAAM;QACL,IAAI,CAAClD,OAAO,CAACwC,YAAY,CAACuB,GAAG,CAACI,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEAiE,cAAcA,CAACtG,QAAsB;IACnC,IAAIyD,OAAO,CAAC,YAAYzD,QAAQ,CAACQ,aAAa,KAAK,CAAC,EAAE;MACpD,IAAI,CAACvC,gBAAgB,CAACsI,kCAAkC,CAAC;QACvDzF,IAAI,EAAE;UAAEF,WAAW,EAAEZ,QAAQ,CAACY;QAAW;OAC1C,CAAC,CAACG,SAAS,CAACkB,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACjE,OAAO,CAACiD,aAAa,CAAC,QAAQ,CAAC;UAEpC;UACA,MAAMqF,qBAAqB,GAAG,CAAC,IAAI,CAACxH,iBAAiB,GAAG,CAAC,IAAI,IAAI,CAACC,gBAAgB;UAClF,MAAMwH,cAAc,GAAG,IAAI,CAACvH,oBAAoB,GAAG,CAAC,CAAC,CAAC;UAEtD,IAAIuH,cAAc,GAAG,CAAC,IAAID,qBAAqB,IAAIC,cAAc,EAAE;YACjE,IAAI,CAACzH,iBAAiB,GAAGZ,IAAI,CAACsI,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC1H,iBAAiB,GAAG,CAAC,CAAC;UAClE;UAEA,IAAI,CAACoC,eAAe,EAAE;QACxB,CAAC,MAAM;UACL,IAAI,CAAClD,OAAO,CAACwC,YAAY,CAACuB,GAAG,CAACI,OAAO,IAAI,QAAQ,CAAC;QACpD;MACF,CAAC,CAAC;IACJ;EACF;EAEAsE,kBAAkBA,CAAC3G,QAAsB,EAAEO,GAAQ;IACjD,IAAI,CAACpB,sBAAsB,GAAGa,QAAQ;IACtC,IAAI,CAACZ,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACuH,uBAAuB,CAAC5G,QAAQ,CAACY,WAAW,CAAC;IAClD,IAAI,CAAC7C,aAAa,CAACmC,IAAI,CAACK,GAAG,EAAE;MAAEyD,SAAS,EAAE;IAAI,CAAE,CAAC;EACnD;EAEA4C,uBAAuBA,CAACC,UAAkB;IACxC,IAAI,CAAC5I,gBAAgB,CAAC6I,yCAAyC,CAAC;MAC9DhG,IAAI,EAAE;QAAE+F,UAAU,EAAEA;MAAU;KAC/B,CAAC,CAAC9F,SAAS,CAACkB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACvC;QACA,IAAI,CAAC/C,oBAAoB,GAAG6C,GAAG,CAACC,OAAO,CAACgC,GAAG,CAACtB,IAAI,KAAK;UACnDuD,UAAU,EAAEvD,IAAI,CAACuD,UAAW;UAC5BC,YAAY,EAAExD,IAAI,CAACwD,YAAa;UAChCrE,SAAS,EAAEa,IAAI,CAACb,SAAS,IAAI;SAC9B,CAAC,CAAC;MACL,CAAC,MAAM;QACL,IAAI,CAAC7D,OAAO,CAACwC,YAAY,CAACuB,GAAG,CAACI,OAAO,IAAI,UAAU,CAAC;QACpD,IAAI,CAACjD,oBAAoB,GAAG,EAAE;MAChC;MACA,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACtC,CAAC,CAAC;EACJ;EAEA4G,kBAAkBA,CAAA;IAChB,IAAI,CAAC9H,KAAK,CAACyF,KAAK,EAAE;IAClB,IAAI,CAACzF,KAAK,CAAC0F,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9E,cAAc,CAACyB,aAAa,CAAC;IAChE,IAAI,CAACrC,KAAK,CAAC2F,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC/E,cAAc,CAACyB,aAAa,EAAE,GAAG,CAAC;EAChF;EAEAa,OAAOA,CAACd,GAAQ;IACdA,GAAG,CAACgD,KAAK,EAAE;EACb;CACD;AA9bsDwD,UAAA,EAApDzJ,SAAS,CAAC,qBAAqB,EAAE;EAAE0J,MAAM,EAAE;AAAK,CAAE,CAAC,C,0DAAwC;AACvCD,UAAA,EAApDzJ,SAAS,CAAC,qBAAqB,EAAE;EAAE0J,MAAM,EAAE;AAAK,CAAE,CAAC,C,0DAAwC;AACzCD,UAAA,EAAlDzJ,SAAS,CAAC,mBAAmB,EAAE;EAAE0J,MAAM,EAAE;AAAK,CAAE,CAAC,C,wDAAsC;AAL7EpJ,cAAc,GAAAmJ,UAAA,EAZ1B1J,SAAS,CAAC;EACT4J,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE,CAAC,wBAAwB,CAAC;EACrCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP7J,YAAY,EACZD,YAAY,EACZG,mBAAmB;CAEtB,CAAC,C,EAEWE,cAAc,CAic1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}