{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction SpaceTemplateSelectorComponent_div_15_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_div_15_div_6_Template_div_click_0_listener() {\n      const space_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectSpace(space_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 25);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", (ctx_r2.selectedSpace == null ? null : ctx_r2.selectedSpace.id) === space_r2.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(space_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", space_r2.templateCount, \"\\u500B\\u53EF\\u7528\\u6A21\\u677F\");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18);\n    i0.ɵɵelement(3, \"i\", 19);\n    i0.ɵɵtext(4, \"\\u9078\\u64C7\\u76EE\\u6A19\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 20);\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_15_div_6_Template, 7, 5, \"div\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableSpaces);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_16_div_6_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_16_div_6_div_11_Template_input_ngModelChange_1_listener($event) {\n      const item_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r7.selected, $event) || (item_r7.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SpaceTemplateSelectorComponent_div_16_div_6_div_11_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onTemplateItemChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 40)(3, \"div\")(4, \"div\", 41);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 42);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 43);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_16_div_6_div_11_Template_input_ngModelChange_13_listener($event) {\n      const item_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r7.price, $event) || (item_r7.price = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SpaceTemplateSelectorComponent_div_16_div_6_div_11_Template_input_change_13_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onTemplateItemChange());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r7.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.unit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", i0.ɵɵpipeBind1(12, 6, item_r7.price), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r7.price);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_16_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_div_16_div_6_Template_div_click_1_listener() {\n      const group_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleTemplateGroup(group_r5));\n    });\n    i0.ɵɵelementStart(2, \"div\", 32)(3, \"div\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 34);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 35);\n    i0.ɵɵtext(9, \"\\u25BC\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 36);\n    i0.ɵɵtemplate(11, SpaceTemplateSelectorComponent_div_16_div_6_div_11_Template, 14, 8, \"div\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", group_r5.expanded);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", group_r5.icon, \" \", group_r5.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", group_r5.items.length, \"\\u500B\\u9805\\u76EE \\u2022 $\", i0.ɵɵpipeBind1(7, 11, ctx_r2.getTotalPrice(group_r5.items)), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"expanded\", group_r5.expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"expanded\", group_r5.expanded);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", group_r5.items);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 26)(2, \"div\", 18);\n    i0.ɵɵelement(3, \"i\", 27);\n    i0.ɵɵtext(4, \"\\u9078\\u64C7\\u6A21\\u677F\\u7FA4\\u7D44 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 28);\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_16_div_6_Template, 12, 13, \"div\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateGroups);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_17_li_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", item_r8.name, \" (\", item_r8.code, \") - $\", i0.ɵɵpipeBind1(2, 3, item_r8.price), \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_17_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"strong\");\n    i0.ɵɵelement(3, \"i\", 57);\n    i0.ɵɵtext(4, \"\\u885D\\u7A81\\u6AA2\\u6E2C\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6AA2\\u6E2C\\u5230 \", ctx_r2.getConflictCount(), \" \\u500B\\u9805\\u76EE\\u53EF\\u80FD\\u8207\\u73FE\\u6709\\u9700\\u6C42\\u91CD\\u8907\\uFF0C\\u7CFB\\u7D71\\u5C07\\u81EA\\u52D5\\u8655\\u7406\\u885D\\u7A81\\u9805\\u76EE\\u3002 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 45)(2, \"div\", 18);\n    i0.ɵɵelement(3, \"i\", 46);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u5957\\u7528\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 47)(6, \"div\", 48);\n    i0.ɵɵtext(7, \" \\u5C07\\u5957\\u7528\\u5230 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 49);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 50)(15, \"div\", 51);\n    i0.ɵɵtext(16, \"\\u5957\\u7528\\u9805\\u76EE\\u6E05\\u55AE\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"ul\", 52);\n    i0.ɵɵtemplate(18, SpaceTemplateSelectorComponent_div_17_li_18_Template, 3, 5, \"li\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, SpaceTemplateSelectorComponent_div_17_div_19_Template, 6, 1, \"div\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedSpace == null ? null : ctx_r2.selectedSpace.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\uFF1A\", ctx_r2.getSelectedItems().length, \"\\u500B\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u7E3D\\u50F9\\u503C $\", i0.ɵɵpipeBind1(13, 5, ctx_r2.getSelectedTotalPrice()), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSelectedItems());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasConflicts());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.previousStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0B\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canProceed());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyTemplate());\n    });\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u5957\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.getSelectedItems().length === 0);\n  }\n}\nexport class SpaceTemplateSelectorComponent {\n  constructor() {\n    this.isVisible = false;\n    this.buildCaseId = '';\n    this.availableSpaces = [];\n    this.templateApplied = new EventEmitter();\n    this.closed = new EventEmitter();\n    this.currentStep = 1;\n    this.selectedSpace = null;\n    this.templateGroups = [];\n    // 預設空間選項\n    this.defaultSpaces = [{\n      id: 'kitchen',\n      name: '廚房',\n      icon: '🍳',\n      templateCount: 8\n    }, {\n      id: 'living',\n      name: '客廳',\n      icon: '🛋️',\n      templateCount: 5\n    }, {\n      id: 'bedroom',\n      name: '主臥室',\n      icon: '🛏️',\n      templateCount: 6\n    }, {\n      id: 'bathroom',\n      name: '主衛浴',\n      icon: '🚿',\n      templateCount: 4\n    }, {\n      id: 'balcony',\n      name: '陽台',\n      icon: '🏢',\n      templateCount: 3\n    }, {\n      id: 'entrance',\n      name: '玄關',\n      icon: '🚪',\n      templateCount: 2\n    }];\n  }\n  ngOnInit() {\n    if (this.availableSpaces.length === 0) {\n      this.availableSpaces = this.defaultSpaces;\n    }\n  }\n  selectSpace(space) {\n    this.selectedSpace = space;\n    this.loadTemplatesForSpace(space.id);\n  }\n  loadTemplatesForSpace(spaceId) {\n    // 根據空間ID載入對應的模板群組\n    // 這裡可以調用API或使用預設資料\n    this.templateGroups = this.getDefaultTemplatesForSpace(spaceId);\n  }\n  getDefaultTemplatesForSpace(spaceId) {\n    const templates = {\n      kitchen: [{\n        id: 'kitchen-standard',\n        name: '廚房標準配備',\n        icon: '🔧',\n        expanded: false,\n        items: [{\n          id: 'kt001',\n          name: '洗碗機',\n          code: 'KT001',\n          price: 38000,\n          unit: '台',\n          selected: false\n        }, {\n          id: 'kt002',\n          name: '烤箱',\n          code: 'KT002',\n          price: 25000,\n          unit: '台',\n          selected: false\n        }, {\n          id: 'kt003',\n          name: '抽油煙機升級',\n          code: 'KT003',\n          price: 15000,\n          unit: '台',\n          selected: false\n        }]\n      }, {\n        id: 'kitchen-premium',\n        name: '廚房高級配備',\n        icon: '⭐',\n        expanded: false,\n        items: [{\n          id: 'kt004',\n          name: '中島檯面',\n          code: 'KT004',\n          price: 80000,\n          unit: '組',\n          selected: false\n        }, {\n          id: 'kt005',\n          name: '智能電磁爐',\n          code: 'KT005',\n          price: 45000,\n          unit: '台',\n          selected: false\n        }]\n      }],\n      living: [{\n        id: 'living-basic',\n        name: '客廳基本配備',\n        icon: '🏠',\n        expanded: false,\n        items: [{\n          id: 'lv001',\n          name: '投影設備',\n          code: 'LV001',\n          price: 50000,\n          unit: '組',\n          selected: false\n        }, {\n          id: 'lv002',\n          name: '音響系統',\n          code: 'LV002',\n          price: 35000,\n          unit: '組',\n          selected: false\n        }]\n      }]\n    };\n    return templates[spaceId] || [];\n  }\n  toggleTemplateGroup(group) {\n    // 收合其他群組\n    this.templateGroups.forEach(g => {\n      if (g.id !== group.id) {\n        g.expanded = false;\n      }\n    });\n    // 切換當前群組\n    group.expanded = !group.expanded;\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  getTotalPrice(items) {\n    return items.reduce((total, item) => total + (item.selected ? item.price : 0), 0);\n  }\n  getSelectedItems() {\n    const selected = [];\n    this.templateGroups.forEach(group => {\n      group.items.forEach(item => {\n        if (item.selected) {\n          selected.push(item);\n        }\n      });\n    });\n    return selected;\n  }\n  getSelectedTotalPrice() {\n    return this.getSelectedItems().reduce((total, item) => total + item.price, 0);\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.selectedSpace !== null;\n      case 2:\n        return this.getSelectedItems().length > 0;\n      case 3:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 3) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇目標空間',\n      2: '請選擇要套用的模板項目',\n      3: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    if (!this.selectedSpace) return;\n    const config = {\n      spaceId: this.selectedSpace.id,\n      spaceName: this.selectedSpace.name,\n      selectedItems: this.getSelectedItems(),\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.isVisible = false;\n    this.reset();\n    this.closed.emit();\n  }\n  onBackdropClick(event) {\n    if (event.target === event.currentTarget) {\n      this.close();\n    }\n  }\n  reset() {\n    this.currentStep = 1;\n    this.selectedSpace = null;\n    this.templateGroups = [];\n  }\n  // 公共API方法\n  open(preSelectedSpaceId) {\n    this.isVisible = true;\n    this.reset();\n    if (preSelectedSpaceId) {\n      const space = this.availableSpaces.find(s => s.id === preSelectedSpaceId);\n      if (space) {\n        this.selectSpace(space);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceTemplateSelectorComponent,\n      selectors: [[\"app-space-template-selector\"]],\n      inputs: {\n        isVisible: \"isVisible\",\n        buildCaseId: \"buildCaseId\",\n        availableSpaces: \"availableSpaces\"\n      },\n      outputs: {\n        templateApplied: \"templateApplied\",\n        closed: \"closed\"\n      },\n      decls: 28,\n      vars: 24,\n      consts: [[1, \"space-template-modal\", 3, \"click\"], [1, \"space-template-content\", 3, \"click\"], [1, \"space-template-header\"], [1, \"space-template-title\"], [1, \"close-btn\", 3, \"click\"], [1, \"space-template-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"space-template-footer\"], [1, \"progress-info\"], [1, \"step-buttons\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"class\", \"btn btn-secondary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"space-selection\"], [1, \"section-title\"], [1, \"fas\", \"fa-home\", \"mr-2\"], [1, \"space-grid\"], [\"class\", \"space-card\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"space-card\", 3, \"click\"], [1, \"space-icon\"], [1, \"space-name\"], [1, \"space-count\"], [1, \"template-selection\"], [1, \"fas\", \"fa-layer-group\", \"mr-2\"], [1, \"template-groups\"], [\"class\", \"template-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-group\"], [1, \"template-group-header\", 3, \"click\"], [1, \"group-info\"], [1, \"group-name\"], [1, \"group-stats\"], [1, \"group-toggle\"], [1, \"template-items\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-item\"], [\"type\", \"checkbox\", 1, \"template-checkbox\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"template-info\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"item-price\"], [\"type\", \"number\", 1, \"price-input\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"confirmation-area\"], [1, \"fas\", \"fa-check-circle\", \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"summary-price\"], [1, \"selected-items-list\"], [1, \"items-list-title\"], [1, \"items-list\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"conflict-warning\", 4, \"ngIf\"], [1, \"conflict-warning\"], [1, \"warning-text\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-success\", 3, \"click\", \"disabled\"]],\n      template: function SpaceTemplateSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_div_click_0_listener($event) {\n            return ctx.onBackdropClick($event);\n          });\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_div_click_1_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵtext(4, \"\\u7A7A\\u9593\\u6A21\\u677F\\u9078\\u64C7\\u5668\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_5_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(6, \"\\u00D7\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7);\n          i0.ɵɵtext(10, \"1. \\u9078\\u64C7\\u7A7A\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 7);\n          i0.ɵɵtext(12, \"2. \\u9078\\u64C7\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 7);\n          i0.ɵɵtext(14, \"3. \\u78BA\\u8A8D\\u5957\\u7528\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(15, SpaceTemplateSelectorComponent_div_15_Template, 7, 1, \"div\", 8)(16, SpaceTemplateSelectorComponent_div_16_Template, 7, 1, \"div\", 8)(17, SpaceTemplateSelectorComponent_div_17_Template, 20, 7, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"span\");\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 11)(23, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_23_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(24, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, SpaceTemplateSelectorComponent_button_25_Template, 2, 0, \"button\", 13)(26, SpaceTemplateSelectorComponent_button_26_Template, 2, 1, \"button\", 14)(27, SpaceTemplateSelectorComponent_button_27_Template, 2, 1, \"button\", 15);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"show\", ctx.isVisible);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(12, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(16, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(20, _c0, ctx.currentStep === 3, ctx.currentStep > 3, ctx.currentStep < 3));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.getProgressText());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 3);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgModel, i1.DecimalPipe],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.space-template-modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.space-template-modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.space-template-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  width: 90%;\\n  max-width: 1000px;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease;\\n}\\n\\n.space-template-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 20px 30px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.space-template-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 24px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  border: none;\\n  background: none;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: background 0.3s ease;\\n}\\n\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n.space-template-body[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n\\n\\n\\n.step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n  gap: 10px;\\n}\\n\\n.step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n\\n.step-item.active[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n}\\n\\n.step-item.completed[_ngcontent-%COMP%] {\\n  background: #28a745;\\n  color: white;\\n}\\n\\n.step-item.pending[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #6c757d;\\n}\\n\\n\\n\\n.space-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 15px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.space-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n\\n.space-card[_ngcontent-%COMP%] {\\n  border: 2px solid #e9ecef;\\n  border-radius: 8px;\\n  padding: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-align: center;\\n}\\n\\n.space-card[_ngcontent-%COMP%]:hover {\\n  border-color: #667eea;\\n  background: #f8f9ff;\\n}\\n\\n.space-card.selected[_ngcontent-%COMP%] {\\n  border-color: #667eea;\\n  background: #e7f3ff;\\n}\\n\\n.space-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 8px;\\n}\\n\\n.space-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 4px;\\n}\\n\\n.space-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n\\n\\n.template-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.template-groups[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 15px;\\n}\\n\\n.template-group[_ngcontent-%COMP%] {\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.template-group-header[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 16px 20px;\\n  cursor: pointer;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  transition: background 0.3s ease;\\n}\\n\\n.template-group-header[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n}\\n\\n.template-group-header.active[_ngcontent-%COMP%] {\\n  background: #e7f3ff;\\n  border-left: 4px solid #667eea;\\n}\\n\\n.group-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.group-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.group-stats[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n.group-toggle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  transition: transform 0.3s ease;\\n}\\n\\n.group-toggle.expanded[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.template-items[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n\\n.template-items.expanded[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n}\\n\\n.template-item[_ngcontent-%COMP%] {\\n  padding: 12px 20px;\\n  border-bottom: 1px solid #f0f0f0;\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  transition: background 0.2s ease;\\n}\\n\\n.template-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n\\n.template-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.template-checkbox[_ngcontent-%COMP%] {\\n  transform: scale(1.2);\\n}\\n\\n.template-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr 1fr;\\n  gap: 20px;\\n  align-items: center;\\n}\\n\\n.item-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.item-code[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 12px;\\n}\\n\\n.item-price[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n\\n.price-input[_ngcontent-%COMP%] {\\n  width: 90px;\\n  padding: 4px 8px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  text-align: right;\\n  font-size: 12px;\\n}\\n\\n\\n\\n.confirmation-area[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.selected-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.summary-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.summary-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n\\n.selected-items-list[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 15px;\\n  border-radius: 6px;\\n  margin-bottom: 15px;\\n}\\n\\n.items-list-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 10px;\\n}\\n\\n.items-list[_ngcontent-%COMP%] {\\n  margin-left: 20px;\\n  line-height: 1.6;\\n}\\n\\n.conflict-warning[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n\\n.warning-text[_ngcontent-%COMP%] {\\n  color: #856404;\\n  font-size: 14px;\\n}\\n\\n\\n\\n.space-template-footer[_ngcontent-%COMP%] {\\n  padding: 20px 30px;\\n  border-top: 1px solid #e9ecef;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background: #f8f9fa;\\n  border-radius: 0 0 12px 12px;\\n}\\n\\n.step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    transform: translateY(-50px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  .template-info[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n  .space-template-content[_ngcontent-%COMP%] {\\n    width: 95%;\\n    margin: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵlistener", "SpaceTemplateSelectorComponent_div_15_div_6_Template_div_click_0_listener", "space_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "selectSpace", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "selectedSpace", "id", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "name", "ɵɵtextInterpolate1", "templateCount", "ɵɵelement", "ɵɵtemplate", "SpaceTemplateSelectorComponent_div_15_div_6_Template", "ɵɵproperty", "availableSpaces", "ɵɵtwoWayListener", "SpaceTemplateSelectorComponent_div_16_div_6_div_11_Template_input_ngModelChange_1_listener", "$event", "item_r7", "_r6", "ɵɵtwoWayBindingSet", "selected", "SpaceTemplateSelectorComponent_div_16_div_6_div_11_Template_input_change_1_listener", "onTemplateItemChange", "SpaceTemplateSelectorComponent_div_16_div_6_div_11_Template_input_ngModelChange_13_listener", "price", "SpaceTemplateSelectorComponent_div_16_div_6_div_11_Template_input_change_13_listener", "ɵɵtwoWayProperty", "code", "unit", "ɵɵpipeBind1", "SpaceTemplateSelectorComponent_div_16_div_6_Template_div_click_1_listener", "group_r5", "_r4", "toggleTemplateGroup", "SpaceTemplateSelectorComponent_div_16_div_6_div_11_Template", "expanded", "ɵɵtextInterpolate2", "items", "length", "getTotalPrice", "SpaceTemplateSelectorComponent_div_16_div_6_Template", "templateGroups", "ɵɵtextInterpolate3", "item_r8", "getConflictCount", "SpaceTemplateSelectorComponent_div_17_li_18_Template", "SpaceTemplateSelectorComponent_div_17_div_19_Template", "getSelectedItems", "getSelectedTotalPrice", "hasConflicts", "SpaceTemplateSelectorComponent_button_25_Template_button_click_0_listener", "_r9", "previousStep", "SpaceTemplateSelectorComponent_button_26_Template_button_click_0_listener", "_r10", "nextStep", "canProceed", "SpaceTemplateSelectorComponent_button_27_Template_button_click_0_listener", "_r11", "applyTemplate", "SpaceTemplateSelectorComponent", "constructor", "isVisible", "buildCaseId", "templateApplied", "closed", "currentStep", "defaultSpaces", "ngOnInit", "space", "loadTemplatesForSpace", "spaceId", "getDefaultTemplatesForSpace", "templates", "kitchen", "living", "group", "for<PERSON>ach", "g", "reduce", "total", "item", "push", "getProgressText", "progressTexts", "config", "spaceName", "selectedItems", "totalPrice", "emit", "close", "reset", "onBackdropClick", "event", "target", "currentTarget", "open", "preSelectedSpaceId", "find", "s", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "SpaceTemplateSelectorComponent_Template", "rf", "ctx", "SpaceTemplateSelectorComponent_Template_div_click_0_listener", "SpaceTemplateSelectorComponent_Template_div_click_1_listener", "stopPropagation", "SpaceTemplateSelectorComponent_Template_button_click_5_listener", "SpaceTemplateSelectorComponent_div_15_Template", "SpaceTemplateSelectorComponent_div_16_Template", "SpaceTemplateSelectorComponent_div_17_Template", "SpaceTemplateSelectorComponent_Template_button_click_23_listener", "SpaceTemplateSelectorComponent_button_25_Template", "SpaceTemplateSelectorComponent_button_26_Template", "SpaceTemplateSelectorComponent_button_27_Template", "ɵɵpureFunction3", "_c0"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\n\r\nexport interface SpaceOption {\r\n  id: string;\r\n  name: string;\r\n  icon: string;\r\n  templateCount: number;\r\n}\r\n\r\nexport interface TemplateItem {\r\n  id: string;\r\n  name: string;\r\n  code: string;\r\n  price: number;\r\n  unit: string;\r\n  selected: boolean;\r\n}\r\n\r\nexport interface TemplateGroup {\r\n  id: string;\r\n  name: string;\r\n  icon: string;\r\n  items: TemplateItem[];\r\n  expanded: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: TemplateItem[];\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() buildCaseId: string = '';\r\n  @Input() availableSpaces: SpaceOption[] = [];\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n  @Output() closed = new EventEmitter<void>();\r\n\r\n  currentStep: number = 1;\r\n  selectedSpace: SpaceOption | null = null;\r\n  templateGroups: TemplateGroup[] = [];\r\n\r\n  // 預設空間選項\r\n  defaultSpaces: SpaceOption[] = [\r\n    { id: 'kitchen', name: '廚房', icon: '🍳', templateCount: 8 },\r\n    { id: 'living', name: '客廳', icon: '🛋️', templateCount: 5 },\r\n    { id: 'bedroom', name: '主臥室', icon: '🛏️', templateCount: 6 },\r\n    { id: 'bathroom', name: '主衛浴', icon: '🚿', templateCount: 4 },\r\n    { id: 'balcony', name: '陽台', icon: '🏢', templateCount: 3 },\r\n    { id: 'entrance', name: '玄關', icon: '🚪', templateCount: 2 }\r\n  ];\r\n\r\n  ngOnInit() {\r\n    if (this.availableSpaces.length === 0) {\r\n      this.availableSpaces = this.defaultSpaces;\r\n    }\r\n  }\r\n\r\n  selectSpace(space: SpaceOption) {\r\n    this.selectedSpace = space;\r\n    this.loadTemplatesForSpace(space.id);\r\n  }\r\n\r\n  loadTemplatesForSpace(spaceId: string) {\r\n    // 根據空間ID載入對應的模板群組\r\n    // 這裡可以調用API或使用預設資料\r\n    this.templateGroups = this.getDefaultTemplatesForSpace(spaceId);\r\n  }\r\n\r\n  getDefaultTemplatesForSpace(spaceId: string): TemplateGroup[] {\r\n    const templates: Record<string, TemplateGroup[]> = {\r\n      kitchen: [\r\n        {\r\n          id: 'kitchen-standard',\r\n          name: '廚房標準配備',\r\n          icon: '🔧',\r\n          expanded: false,\r\n          items: [\r\n            { id: 'kt001', name: '洗碗機', code: 'KT001', price: 38000, unit: '台', selected: false },\r\n            { id: 'kt002', name: '烤箱', code: 'KT002', price: 25000, unit: '台', selected: false },\r\n            { id: 'kt003', name: '抽油煙機升級', code: 'KT003', price: 15000, unit: '台', selected: false }\r\n          ]\r\n        },\r\n        {\r\n          id: 'kitchen-premium',\r\n          name: '廚房高級配備',\r\n          icon: '⭐',\r\n          expanded: false,\r\n          items: [\r\n            { id: 'kt004', name: '中島檯面', code: 'KT004', price: 80000, unit: '組', selected: false },\r\n            { id: 'kt005', name: '智能電磁爐', code: 'KT005', price: 45000, unit: '台', selected: false }\r\n          ]\r\n        }\r\n      ],\r\n      living: [\r\n        {\r\n          id: 'living-basic',\r\n          name: '客廳基本配備',\r\n          icon: '🏠',\r\n          expanded: false,\r\n          items: [\r\n            { id: 'lv001', name: '投影設備', code: 'LV001', price: 50000, unit: '組', selected: false },\r\n            { id: 'lv002', name: '音響系統', code: 'LV002', price: 35000, unit: '組', selected: false }\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n\r\n    return templates[spaceId] || [];\r\n  }\r\n\r\n  toggleTemplateGroup(group: TemplateGroup) {\r\n    // 收合其他群組\r\n    this.templateGroups.forEach(g => {\r\n      if (g.id !== group.id) {\r\n        g.expanded = false;\r\n      }\r\n    });\r\n    \r\n    // 切換當前群組\r\n    group.expanded = !group.expanded;\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  getTotalPrice(items: TemplateItem[]): number {\r\n    return items.reduce((total, item) => total + (item.selected ? item.price : 0), 0);\r\n  }\r\n\r\n  getSelectedItems(): TemplateItem[] {\r\n    const selected: TemplateItem[] = [];\r\n    this.templateGroups.forEach(group => {\r\n      group.items.forEach(item => {\r\n        if (item.selected) {\r\n          selected.push(item);\r\n        }\r\n      });\r\n    });\r\n    return selected;\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    return this.getSelectedItems().reduce((total, item) => total + item.price, 0);\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.selectedSpace !== null;\r\n      case 2:\r\n        return this.getSelectedItems().length > 0;\r\n      case 3:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 3) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇目標空間',\r\n      2: '請選擇要套用的模板項目',\r\n      3: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    if (!this.selectedSpace) return;\r\n\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: this.selectedSpace.id,\r\n      spaceName: this.selectedSpace.name,\r\n      selectedItems: this.getSelectedItems(),\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.isVisible = false;\r\n    this.reset();\r\n    this.closed.emit();\r\n  }\r\n\r\n  onBackdropClick(event: Event) {\r\n    if (event.target === event.currentTarget) {\r\n      this.close();\r\n    }\r\n  }\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.selectedSpace = null;\r\n    this.templateGroups = [];\r\n  }\r\n\r\n  // 公共API方法\r\n  open(preSelectedSpaceId?: string) {\r\n    this.isVisible = true;\r\n    this.reset();\r\n    \r\n    if (preSelectedSpaceId) {\r\n      const space = this.availableSpaces.find(s => s.id === preSelectedSpaceId);\r\n      if (space) {\r\n        this.selectSpace(space);\r\n      }\r\n    }\r\n  }\r\n}\r\n", "<!-- 空間模板選擇器共用元件 -->\r\n<div class=\"space-template-modal\" [class.show]=\"isVisible\" (click)=\"onBackdropClick($event)\">\r\n  <div class=\"space-template-content\" (click)=\"$event.stopPropagation()\">\r\n    <div class=\"space-template-header\">\r\n      <div class=\"space-template-title\">空間模板選擇器</div>\r\n      <button class=\"close-btn\" (click)=\"close()\">&times;</button>\r\n    </div>\r\n\r\n    <div class=\"space-template-body\">\r\n      <!-- 步驟導航 -->\r\n      <div class=\"step-nav\">\r\n        <div class=\"step-item\" [ngClass]=\"{\r\n          'active': currentStep === 1,\r\n          'completed': currentStep > 1,\r\n          'pending': currentStep < 1\r\n        }\">1. 選擇空間</div>\r\n        <div class=\"step-item\" [ngClass]=\"{\r\n          'active': currentStep === 2,\r\n          'completed': currentStep > 2,\r\n          'pending': currentStep < 2\r\n        }\">2. 選擇模板</div>\r\n        <div class=\"step-item\" [ngClass]=\"{\r\n          'active': currentStep === 3,\r\n          'completed': currentStep > 3,\r\n          'pending': currentStep < 3\r\n        }\">3. 確認套用</div>\r\n      </div>\r\n\r\n      <!-- 步驟1: 選擇空間 -->\r\n      <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n        <div class=\"space-selection\">\r\n          <div class=\"section-title\">\r\n            <i class=\"fas fa-home mr-2\"></i>選擇目標空間\r\n          </div>\r\n          <div class=\"space-grid\">\r\n            <div *ngFor=\"let space of availableSpaces\" \r\n                 class=\"space-card\" \r\n                 [class.selected]=\"selectedSpace?.id === space.id\"\r\n                 (click)=\"selectSpace(space)\">\r\n              <div class=\"space-icon\">{{ space.icon }}</div>\r\n              <div class=\"space-name\">{{ space.name }}</div>\r\n              <div class=\"space-count\">{{ space.templateCount }}個可用模板</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 步驟2: 選擇模板 -->\r\n      <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n        <div class=\"template-selection\">\r\n          <div class=\"section-title\">\r\n            <i class=\"fas fa-layer-group mr-2\"></i>選擇模板群組\r\n          </div>\r\n          <div class=\"template-groups\">\r\n            <div *ngFor=\"let group of templateGroups\" class=\"template-group\">\r\n              <div class=\"template-group-header\" \r\n                   [class.active]=\"group.expanded\"\r\n                   (click)=\"toggleTemplateGroup(group)\">\r\n                <div class=\"group-info\">\r\n                  <div class=\"group-name\">{{ group.icon }} {{ group.name }}</div>\r\n                  <div class=\"group-stats\">{{ group.items.length }}個項目 • ${{ getTotalPrice(group.items) | number }}</div>\r\n                </div>\r\n                <div class=\"group-toggle\" [class.expanded]=\"group.expanded\">▼</div>\r\n              </div>\r\n              <div class=\"template-items\" [class.expanded]=\"group.expanded\">\r\n                <div *ngFor=\"let item of group.items\" class=\"template-item\">\r\n                  <input type=\"checkbox\" \r\n                         class=\"template-checkbox\" \r\n                         [(ngModel)]=\"item.selected\"\r\n                         (change)=\"onTemplateItemChange()\">\r\n                  <div class=\"template-info\">\r\n                    <div>\r\n                      <div class=\"item-name\">{{ item.name }}</div>\r\n                      <div class=\"item-code\">{{ item.code }}</div>\r\n                    </div>\r\n                    <div>{{ item.unit }}</div>\r\n                    <div class=\"item-price\">${{ item.price | number }}</div>\r\n                    <input type=\"number\" \r\n                           class=\"price-input\" \r\n                           [(ngModel)]=\"item.price\"\r\n                           (change)=\"onTemplateItemChange()\">\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 步驟3: 確認套用 -->\r\n      <div *ngIf=\"currentStep === 3\" class=\"step-content\">\r\n        <div class=\"confirmation-area\">\r\n          <div class=\"section-title\">\r\n            <i class=\"fas fa-check-circle mr-2\"></i>確認套用詳情\r\n          </div>\r\n          \r\n          <div class=\"selected-summary\">\r\n            <div class=\"summary-text\">\r\n              將套用到 <strong>{{ selectedSpace?.name }}</strong>：{{ getSelectedItems().length }}個模板項目\r\n            </div>\r\n            <div class=\"summary-price\">總價值 ${{ getSelectedTotalPrice() | number }}</div>\r\n          </div>\r\n\r\n          <div class=\"selected-items-list\">\r\n            <div class=\"items-list-title\">套用項目清單：</div>\r\n            <ul class=\"items-list\">\r\n              <li *ngFor=\"let item of getSelectedItems()\">\r\n                {{ item.name }} ({{ item.code }}) - ${{ item.price | number }}\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div *ngIf=\"hasConflicts()\" class=\"conflict-warning\">\r\n            <div class=\"warning-text\">\r\n              <strong><i class=\"fas fa-exclamation-triangle mr-1\"></i>衝突檢測：</strong>\r\n              檢測到 {{ getConflictCount() }} 個項目可能與現有需求重複，系統將自動處理衝突項目。\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"space-template-footer\">\r\n      <div class=\"progress-info\">\r\n        <span>{{ getProgressText() }}</span>\r\n      </div>\r\n      <div class=\"step-buttons\">\r\n        <button class=\"btn btn-secondary\" (click)=\"close()\">取消</button>\r\n        <button *ngIf=\"currentStep > 1\" \r\n                class=\"btn btn-secondary\" \r\n                (click)=\"previousStep()\">上一步</button>\r\n        <button *ngIf=\"currentStep < 3\" \r\n                class=\"btn btn-primary\" \r\n                [disabled]=\"!canProceed()\"\r\n                (click)=\"nextStep()\">下一步</button>\r\n        <button *ngIf=\"currentStep === 3\" \r\n                class=\"btn btn-success\" \r\n                [disabled]=\"getSelectedItems().length === 0\"\r\n                (click)=\"applyTemplate()\">確認套用</button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;;;;;;;;;;;;ICmClEC,EAAA,CAAAC,cAAA,cAGkC;IAA7BD,EAAA,CAAAE,UAAA,mBAAAC,0EAAA;MAAA,MAAAC,QAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,QAAA,CAAkB;IAAA,EAAC;IAC/BJ,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAY,MAAA,GAAgB;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAC9Cb,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAY,MAAA,GAAgB;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAC9Cb,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAY,MAAA,GAA8B;IACzDZ,EADyD,CAAAa,YAAA,EAAM,EACzD;;;;;IALDb,EAAA,CAAAc,WAAA,cAAAN,MAAA,CAAAO,aAAA,kBAAAP,MAAA,CAAAO,aAAA,CAAAC,EAAA,MAAAZ,QAAA,CAAAY,EAAA,CAAiD;IAE5BhB,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,iBAAA,CAAAd,QAAA,CAAAe,IAAA,CAAgB;IAChBnB,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,iBAAA,CAAAd,QAAA,CAAAgB,IAAA,CAAgB;IACfpB,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAqB,kBAAA,KAAAjB,QAAA,CAAAkB,aAAA,mCAA8B;;;;;IAV3DtB,EAFJ,CAAAC,cAAA,cAAoD,cACrB,cACA;IACzBD,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAY,MAAA,4CAClC;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAwB,UAAA,IAAAC,oDAAA,kBAGkC;IAOxCzB,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;;;;IAVuBb,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAA0B,UAAA,YAAAlB,MAAA,CAAAmB,eAAA,CAAkB;;;;;;IA+BnC3B,EADF,CAAAC,cAAA,cAA4D,gBAIjB;IADlCD,EAAA,CAAA4B,gBAAA,2BAAAC,2FAAAC,MAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAK,aAAA,CAAA2B,GAAA,EAAAzB,SAAA;MAAAP,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAAG,QAAA,EAAAJ,MAAA,MAAAC,OAAA,CAAAG,QAAA,GAAAJ,MAAA;MAAA,OAAA9B,EAAA,CAAAU,WAAA,CAAAoB,MAAA;IAAA,EAA2B;IAC3B9B,EAAA,CAAAE,UAAA,oBAAAiC,oFAAA;MAAAnC,EAAA,CAAAK,aAAA,CAAA2B,GAAA;MAAA,MAAAxB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAA4B,oBAAA,EAAsB;IAAA,EAAC;IAHxCpC,EAAA,CAAAa,YAAA,EAGyC;IAGrCb,EAFJ,CAAAC,cAAA,cAA2B,UACpB,cACoB;IAAAD,EAAA,CAAAY,MAAA,GAAe;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAC5Cb,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAY,MAAA,GAAe;IACxCZ,EADwC,CAAAa,YAAA,EAAM,EACxC;IACNb,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAY,MAAA,GAAe;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAC1Bb,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAY,MAAA,IAA0B;;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IACxDb,EAAA,CAAAC,cAAA,iBAGyC;IADlCD,EAAA,CAAA4B,gBAAA,2BAAAS,4FAAAP,MAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAK,aAAA,CAAA2B,GAAA,EAAAzB,SAAA;MAAAP,EAAA,CAAAiC,kBAAA,CAAAF,OAAA,CAAAO,KAAA,EAAAR,MAAA,MAAAC,OAAA,CAAAO,KAAA,GAAAR,MAAA;MAAA,OAAA9B,EAAA,CAAAU,WAAA,CAAAoB,MAAA;IAAA,EAAwB;IACxB9B,EAAA,CAAAE,UAAA,oBAAAqC,qFAAA;MAAAvC,EAAA,CAAAK,aAAA,CAAA2B,GAAA;MAAA,MAAAxB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAA4B,oBAAA,EAAsB;IAAA,EAAC;IAE5CpC,EALI,CAAAa,YAAA,EAGyC,EACrC,EACF;;;;IAdGb,EAAA,CAAAiB,SAAA,EAA2B;IAA3BjB,EAAA,CAAAwC,gBAAA,YAAAT,OAAA,CAAAG,QAAA,CAA2B;IAIPlC,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAkB,iBAAA,CAAAa,OAAA,CAAAX,IAAA,CAAe;IACfpB,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAkB,iBAAA,CAAAa,OAAA,CAAAU,IAAA,CAAe;IAEnCzC,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAkB,iBAAA,CAAAa,OAAA,CAAAW,IAAA,CAAe;IACI1C,EAAA,CAAAiB,SAAA,GAA0B;IAA1BjB,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAA2C,WAAA,QAAAZ,OAAA,CAAAO,KAAA,MAA0B;IAG3CtC,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAwC,gBAAA,YAAAT,OAAA,CAAAO,KAAA,CAAwB;;;;;;IAxBrCtC,EADF,CAAAC,cAAA,cAAiE,cAGrB;IAArCD,EAAA,CAAAE,UAAA,mBAAA0C,0EAAA;MAAA,MAAAC,QAAA,GAAA7C,EAAA,CAAAK,aAAA,CAAAyC,GAAA,EAAAvC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAuC,mBAAA,CAAAF,QAAA,CAA0B;IAAA,EAAC;IAErC7C,EADF,CAAAC,cAAA,cAAwB,cACE;IAAAD,EAAA,CAAAY,MAAA,GAAiC;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAC/Db,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAY,MAAA,GAAwE;;IACnGZ,EADmG,CAAAa,YAAA,EAAM,EACnG;IACNb,EAAA,CAAAC,cAAA,cAA4D;IAAAD,EAAA,CAAAY,MAAA,aAAC;IAC/DZ,EAD+D,CAAAa,YAAA,EAAM,EAC/D;IACNb,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAwB,UAAA,KAAAwB,2DAAA,mBAA4D;IAmBhEhD,EADE,CAAAa,YAAA,EAAM,EACF;;;;;IA5BCb,EAAA,CAAAiB,SAAA,EAA+B;IAA/BjB,EAAA,CAAAc,WAAA,WAAA+B,QAAA,CAAAI,QAAA,CAA+B;IAGRjD,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAkD,kBAAA,KAAAL,QAAA,CAAA1B,IAAA,OAAA0B,QAAA,CAAAzB,IAAA,KAAiC;IAChCpB,EAAA,CAAAiB,SAAA,GAAwE;IAAxEjB,EAAA,CAAAkD,kBAAA,KAAAL,QAAA,CAAAM,KAAA,CAAAC,MAAA,iCAAApD,EAAA,CAAA2C,WAAA,QAAAnC,MAAA,CAAA6C,aAAA,CAAAR,QAAA,CAAAM,KAAA,OAAwE;IAEzEnD,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAc,WAAA,aAAA+B,QAAA,CAAAI,QAAA,CAAiC;IAEjCjD,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAc,WAAA,aAAA+B,QAAA,CAAAI,QAAA,CAAiC;IACrCjD,EAAA,CAAAiB,SAAA,EAAc;IAAdjB,EAAA,CAAA0B,UAAA,YAAAmB,QAAA,CAAAM,KAAA,CAAc;;;;;IAf1CnD,EAFJ,CAAAC,cAAA,cAAoD,cAClB,cACH;IACzBD,EAAA,CAAAuB,SAAA,YAAuC;IAAAvB,EAAA,CAAAY,MAAA,4CACzC;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAwB,UAAA,IAAA8B,oDAAA,oBAAiE;IAiCvEtD,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;;;;IAjCuBb,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAA0B,UAAA,YAAAlB,MAAA,CAAA+C,cAAA,CAAiB;;;;;IAoDtCvD,EAAA,CAAAC,cAAA,SAA4C;IAC1CD,EAAA,CAAAY,MAAA,GACF;;IAAAZ,EAAA,CAAAa,YAAA,EAAK;;;;IADHb,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAwD,kBAAA,MAAAC,OAAA,CAAArC,IAAA,QAAAqC,OAAA,CAAAhB,IAAA,WAAAzC,EAAA,CAAA2C,WAAA,OAAAc,OAAA,CAAAnB,KAAA,OACF;;;;;IAMAtC,EAFJ,CAAAC,cAAA,cAAqD,cACzB,aAChB;IAAAD,EAAA,CAAAuB,SAAA,YAAgD;IAAAvB,EAAA,CAAAY,MAAA,qCAAK;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IACtEb,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAa,YAAA,EAAM,EACF;;;;IAFFb,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAqB,kBAAA,yBAAAb,MAAA,CAAAkD,gBAAA,+JACF;;;;;IAxBF1D,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAAuB,SAAA,YAAwC;IAAAvB,EAAA,CAAAY,MAAA,4CAC1C;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAGJb,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAY,MAAA,iCAAK;IAAAZ,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAY,MAAA,GAAyB;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAAAb,EAAA,CAAAY,MAAA,IACjD;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAY,MAAA,IAA2C;;IACxEZ,EADwE,CAAAa,YAAA,EAAM,EACxE;IAGJb,EADF,CAAAC,cAAA,eAAiC,eACD;IAAAD,EAAA,CAAAY,MAAA,kDAAO;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAC3Cb,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAwB,UAAA,KAAAmC,oDAAA,iBAA4C;IAIhD3D,EADE,CAAAa,YAAA,EAAK,EACD;IAENb,EAAA,CAAAwB,UAAA,KAAAoC,qDAAA,kBAAqD;IAOzD5D,EADE,CAAAa,YAAA,EAAM,EACF;;;;IArBeb,EAAA,CAAAiB,SAAA,GAAyB;IAAzBjB,EAAA,CAAAkB,iBAAA,CAAAV,MAAA,CAAAO,aAAA,kBAAAP,MAAA,CAAAO,aAAA,CAAAK,IAAA,CAAyB;IAASpB,EAAA,CAAAiB,SAAA,EACjD;IADiDjB,EAAA,CAAAqB,kBAAA,WAAAb,MAAA,CAAAqD,gBAAA,GAAAT,MAAA,oCACjD;IAC2BpD,EAAA,CAAAiB,SAAA,GAA2C;IAA3CjB,EAAA,CAAAqB,kBAAA,yBAAArB,EAAA,CAAA2C,WAAA,QAAAnC,MAAA,CAAAsD,qBAAA,QAA2C;IAM/C9D,EAAA,CAAAiB,SAAA,GAAqB;IAArBjB,EAAA,CAAA0B,UAAA,YAAAlB,MAAA,CAAAqD,gBAAA,GAAqB;IAMxC7D,EAAA,CAAAiB,SAAA,EAAoB;IAApBjB,EAAA,CAAA0B,UAAA,SAAAlB,MAAA,CAAAuD,YAAA,GAAoB;;;;;;IAgB5B/D,EAAA,CAAAC,cAAA,iBAEiC;IAAzBD,EAAA,CAAAE,UAAA,mBAAA8D,0EAAA;MAAAhE,EAAA,CAAAK,aAAA,CAAA4D,GAAA;MAAA,MAAAzD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA0D,YAAA,EAAc;IAAA,EAAC;IAAClE,EAAA,CAAAY,MAAA,yBAAG;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAC7Cb,EAAA,CAAAC,cAAA,iBAG6B;IAArBD,EAAA,CAAAE,UAAA,mBAAAiE,0EAAA;MAAAnE,EAAA,CAAAK,aAAA,CAAA+D,IAAA;MAAA,MAAA5D,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA6D,QAAA,EAAU;IAAA,EAAC;IAACrE,EAAA,CAAAY,MAAA,yBAAG;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;IADjCb,EAAA,CAAA0B,UAAA,cAAAlB,MAAA,CAAA8D,UAAA,GAA0B;;;;;;IAElCtE,EAAA,CAAAC,cAAA,iBAGkC;IAA1BD,EAAA,CAAAE,UAAA,mBAAAqE,0EAAA;MAAAvE,EAAA,CAAAK,aAAA,CAAAmE,IAAA;MAAA,MAAAhE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiE,aAAA,EAAe;IAAA,EAAC;IAACzE,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;IADvCb,EAAA,CAAA0B,UAAA,aAAAlB,MAAA,CAAAqD,gBAAA,GAAAT,MAAA,OAA4C;;;ADnG5D,OAAM,MAAOsB,8BAA8B;EAL3CC,YAAA;IAMW,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAlD,eAAe,GAAkB,EAAE;IAClC,KAAAmD,eAAe,GAAG,IAAI/E,YAAY,EAAuB;IACzD,KAAAgF,MAAM,GAAG,IAAIhF,YAAY,EAAQ;IAE3C,KAAAiF,WAAW,GAAW,CAAC;IACvB,KAAAjE,aAAa,GAAuB,IAAI;IACxC,KAAAwC,cAAc,GAAoB,EAAE;IAEpC;IACA,KAAA0B,aAAa,GAAkB,CAC7B;MAAEjE,EAAE,EAAE,SAAS;MAAEI,IAAI,EAAE,IAAI;MAAED,IAAI,EAAE,IAAI;MAAEG,aAAa,EAAE;IAAC,CAAE,EAC3D;MAAEN,EAAE,EAAE,QAAQ;MAAEI,IAAI,EAAE,IAAI;MAAED,IAAI,EAAE,KAAK;MAAEG,aAAa,EAAE;IAAC,CAAE,EAC3D;MAAEN,EAAE,EAAE,SAAS;MAAEI,IAAI,EAAE,KAAK;MAAED,IAAI,EAAE,KAAK;MAAEG,aAAa,EAAE;IAAC,CAAE,EAC7D;MAAEN,EAAE,EAAE,UAAU;MAAEI,IAAI,EAAE,KAAK;MAAED,IAAI,EAAE,IAAI;MAAEG,aAAa,EAAE;IAAC,CAAE,EAC7D;MAAEN,EAAE,EAAE,SAAS;MAAEI,IAAI,EAAE,IAAI;MAAED,IAAI,EAAE,IAAI;MAAEG,aAAa,EAAE;IAAC,CAAE,EAC3D;MAAEN,EAAE,EAAE,UAAU;MAAEI,IAAI,EAAE,IAAI;MAAED,IAAI,EAAE,IAAI;MAAEG,aAAa,EAAE;IAAC,CAAE,CAC7D;;EAED4D,QAAQA,CAAA;IACN,IAAI,IAAI,CAACvD,eAAe,CAACyB,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACzB,eAAe,GAAG,IAAI,CAACsD,aAAa;IAC3C;EACF;EAEAtE,WAAWA,CAACwE,KAAkB;IAC5B,IAAI,CAACpE,aAAa,GAAGoE,KAAK;IAC1B,IAAI,CAACC,qBAAqB,CAACD,KAAK,CAACnE,EAAE,CAAC;EACtC;EAEAoE,qBAAqBA,CAACC,OAAe;IACnC;IACA;IACA,IAAI,CAAC9B,cAAc,GAAG,IAAI,CAAC+B,2BAA2B,CAACD,OAAO,CAAC;EACjE;EAEAC,2BAA2BA,CAACD,OAAe;IACzC,MAAME,SAAS,GAAoC;MACjDC,OAAO,EAAE,CACP;QACExE,EAAE,EAAE,kBAAkB;QACtBI,IAAI,EAAE,QAAQ;QACdD,IAAI,EAAE,IAAI;QACV8B,QAAQ,EAAE,KAAK;QACfE,KAAK,EAAE,CACL;UAAEnC,EAAE,EAAE,OAAO;UAAEI,IAAI,EAAE,KAAK;UAAEqB,IAAI,EAAE,OAAO;UAAEH,KAAK,EAAE,KAAK;UAAEI,IAAI,EAAE,GAAG;UAAER,QAAQ,EAAE;QAAK,CAAE,EACrF;UAAElB,EAAE,EAAE,OAAO;UAAEI,IAAI,EAAE,IAAI;UAAEqB,IAAI,EAAE,OAAO;UAAEH,KAAK,EAAE,KAAK;UAAEI,IAAI,EAAE,GAAG;UAAER,QAAQ,EAAE;QAAK,CAAE,EACpF;UAAElB,EAAE,EAAE,OAAO;UAAEI,IAAI,EAAE,QAAQ;UAAEqB,IAAI,EAAE,OAAO;UAAEH,KAAK,EAAE,KAAK;UAAEI,IAAI,EAAE,GAAG;UAAER,QAAQ,EAAE;QAAK,CAAE;OAE3F,EACD;QACElB,EAAE,EAAE,iBAAiB;QACrBI,IAAI,EAAE,QAAQ;QACdD,IAAI,EAAE,GAAG;QACT8B,QAAQ,EAAE,KAAK;QACfE,KAAK,EAAE,CACL;UAAEnC,EAAE,EAAE,OAAO;UAAEI,IAAI,EAAE,MAAM;UAAEqB,IAAI,EAAE,OAAO;UAAEH,KAAK,EAAE,KAAK;UAAEI,IAAI,EAAE,GAAG;UAAER,QAAQ,EAAE;QAAK,CAAE,EACtF;UAAElB,EAAE,EAAE,OAAO;UAAEI,IAAI,EAAE,OAAO;UAAEqB,IAAI,EAAE,OAAO;UAAEH,KAAK,EAAE,KAAK;UAAEI,IAAI,EAAE,GAAG;UAAER,QAAQ,EAAE;QAAK,CAAE;OAE1F,CACF;MACDuD,MAAM,EAAE,CACN;QACEzE,EAAE,EAAE,cAAc;QAClBI,IAAI,EAAE,QAAQ;QACdD,IAAI,EAAE,IAAI;QACV8B,QAAQ,EAAE,KAAK;QACfE,KAAK,EAAE,CACL;UAAEnC,EAAE,EAAE,OAAO;UAAEI,IAAI,EAAE,MAAM;UAAEqB,IAAI,EAAE,OAAO;UAAEH,KAAK,EAAE,KAAK;UAAEI,IAAI,EAAE,GAAG;UAAER,QAAQ,EAAE;QAAK,CAAE,EACtF;UAAElB,EAAE,EAAE,OAAO;UAAEI,IAAI,EAAE,MAAM;UAAEqB,IAAI,EAAE,OAAO;UAAEH,KAAK,EAAE,KAAK;UAAEI,IAAI,EAAE,GAAG;UAAER,QAAQ,EAAE;QAAK,CAAE;OAEzF;KAEJ;IAED,OAAOqD,SAAS,CAACF,OAAO,CAAC,IAAI,EAAE;EACjC;EAEAtC,mBAAmBA,CAAC2C,KAAoB;IACtC;IACA,IAAI,CAACnC,cAAc,CAACoC,OAAO,CAACC,CAAC,IAAG;MAC9B,IAAIA,CAAC,CAAC5E,EAAE,KAAK0E,KAAK,CAAC1E,EAAE,EAAE;QACrB4E,CAAC,CAAC3C,QAAQ,GAAG,KAAK;MACpB;IACF,CAAC,CAAC;IAEF;IACAyC,KAAK,CAACzC,QAAQ,GAAG,CAACyC,KAAK,CAACzC,QAAQ;EAClC;EAEAb,oBAAoBA,CAAA;IAClB;EAAA;EAGFiB,aAAaA,CAACF,KAAqB;IACjC,OAAOA,KAAK,CAAC0C,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,IAAIC,IAAI,CAAC7D,QAAQ,GAAG6D,IAAI,CAACzD,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACnF;EAEAuB,gBAAgBA,CAAA;IACd,MAAM3B,QAAQ,GAAmB,EAAE;IACnC,IAAI,CAACqB,cAAc,CAACoC,OAAO,CAACD,KAAK,IAAG;MAClCA,KAAK,CAACvC,KAAK,CAACwC,OAAO,CAACI,IAAI,IAAG;QACzB,IAAIA,IAAI,CAAC7D,QAAQ,EAAE;UACjBA,QAAQ,CAAC8D,IAAI,CAACD,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO7D,QAAQ;EACjB;EAEA4B,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACD,gBAAgB,EAAE,CAACgC,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACzD,KAAK,EAAE,CAAC,CAAC;EAC/E;EAEAgC,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACU,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACjE,aAAa,KAAK,IAAI;MACpC,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC8C,gBAAgB,EAAE,CAACT,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAiB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACU,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAd,YAAYA,CAAA;IACV,IAAI,IAAI,CAACc,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAiB,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAAClB,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAjB,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACF,gBAAgB,EAAE,CAACT,MAAM,GAAG,CAAC;EAC3C;EAEAM,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACK,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAU,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAAC1D,aAAa,EAAE;IAEzB,MAAMoF,MAAM,GAAwB;MAClCd,OAAO,EAAE,IAAI,CAACtE,aAAa,CAACC,EAAE;MAC9BoF,SAAS,EAAE,IAAI,CAACrF,aAAa,CAACK,IAAI;MAClCiF,aAAa,EAAE,IAAI,CAACxC,gBAAgB,EAAE;MACtCyC,UAAU,EAAE,IAAI,CAACxC,qBAAqB;KACvC;IAED,IAAI,CAACgB,eAAe,CAACyB,IAAI,CAACJ,MAAM,CAAC;IACjC,IAAI,CAACK,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAAC5B,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC6B,KAAK,EAAE;IACZ,IAAI,CAAC1B,MAAM,CAACwB,IAAI,EAAE;EACpB;EAEAG,eAAeA,CAACC,KAAY;IAC1B,IAAIA,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,aAAa,EAAE;MACxC,IAAI,CAACL,KAAK,EAAE;IACd;EACF;EAEQC,KAAKA,CAAA;IACX,IAAI,CAACzB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACjE,aAAa,GAAG,IAAI;IACzB,IAAI,CAACwC,cAAc,GAAG,EAAE;EAC1B;EAEA;EACAuD,IAAIA,CAACC,kBAA2B;IAC9B,IAAI,CAACnC,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC6B,KAAK,EAAE;IAEZ,IAAIM,kBAAkB,EAAE;MACtB,MAAM5B,KAAK,GAAG,IAAI,CAACxD,eAAe,CAACqF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjG,EAAE,KAAK+F,kBAAkB,CAAC;MACzE,IAAI5B,KAAK,EAAE;QACT,IAAI,CAACxE,WAAW,CAACwE,KAAK,CAAC;MACzB;IACF;EACF;;;uCA3MWT,8BAA8B;IAAA;EAAA;;;YAA9BA,8BAA8B;MAAAwC,SAAA;MAAAC,MAAA;QAAAvC,SAAA;QAAAC,WAAA;QAAAlD,eAAA;MAAA;MAAAyF,OAAA;QAAAtC,eAAA;QAAAC,MAAA;MAAA;MAAAsC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrC3C1H,EAAA,CAAAC,cAAA,aAA6F;UAAlCD,EAAA,CAAAE,UAAA,mBAAA0H,6DAAA9F,MAAA;YAAA,OAAS6F,GAAA,CAAAjB,eAAA,CAAA5E,MAAA,CAAuB;UAAA,EAAC;UAC1F9B,EAAA,CAAAC,cAAA,aAAuE;UAAnCD,EAAA,CAAAE,UAAA,mBAAA2H,6DAAA/F,MAAA;YAAA,OAASA,MAAA,CAAAgG,eAAA,EAAwB;UAAA,EAAC;UAElE9H,EADF,CAAAC,cAAA,aAAmC,aACC;UAAAD,EAAA,CAAAY,MAAA,iDAAO;UAAAZ,EAAA,CAAAa,YAAA,EAAM;UAC/Cb,EAAA,CAAAC,cAAA,gBAA4C;UAAlBD,EAAA,CAAAE,UAAA,mBAAA6H,gEAAA;YAAA,OAASJ,GAAA,CAAAnB,KAAA,EAAO;UAAA,EAAC;UAACxG,EAAA,CAAAY,MAAA,aAAO;UACrDZ,EADqD,CAAAa,YAAA,EAAS,EACxD;UAKFb,EAHJ,CAAAC,cAAA,aAAiC,aAET,aAKjB;UAAAD,EAAA,CAAAY,MAAA,mCAAO;UAAAZ,EAAA,CAAAa,YAAA,EAAM;UAChBb,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAY,MAAA,mCAAO;UAAAZ,EAAA,CAAAa,YAAA,EAAM;UAChBb,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAY,MAAA,mCAAO;UACZZ,EADY,CAAAa,YAAA,EAAM,EACZ;UAgENb,EA7DA,CAAAwB,UAAA,KAAAwG,8CAAA,iBAAoD,KAAAC,8CAAA,iBAmBA,KAAAC,8CAAA,kBA0CA;UA8BtDlI,EAAA,CAAAa,YAAA,EAAM;UAIFb,EAFJ,CAAAC,cAAA,cAAmC,eACN,YACnB;UAAAD,EAAA,CAAAY,MAAA,IAAuB;UAC/BZ,EAD+B,CAAAa,YAAA,EAAO,EAChC;UAEJb,EADF,CAAAC,cAAA,eAA0B,kBAC4B;UAAlBD,EAAA,CAAAE,UAAA,mBAAAiI,iEAAA;YAAA,OAASR,GAAA,CAAAnB,KAAA,EAAO;UAAA,EAAC;UAACxG,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAa,YAAA,EAAS;UAQ/Db,EAPA,CAAAwB,UAAA,KAAA4G,iDAAA,qBAEiC,KAAAC,iDAAA,qBAIJ,KAAAC,iDAAA,qBAIK;UAI1CtI,EAHM,CAAAa,YAAA,EAAM,EACF,EACF,EACF;;;UA7I4Bb,EAAA,CAAAc,WAAA,SAAA6G,GAAA,CAAA/C,SAAA,CAAwB;UAU3B5E,EAAA,CAAAiB,SAAA,GAIrB;UAJqBjB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuI,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAA3C,WAAA,QAAA2C,GAAA,CAAA3C,WAAA,MAAA2C,GAAA,CAAA3C,WAAA,MAIrB;UACqBhF,EAAA,CAAAiB,SAAA,GAIrB;UAJqBjB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuI,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAA3C,WAAA,QAAA2C,GAAA,CAAA3C,WAAA,MAAA2C,GAAA,CAAA3C,WAAA,MAIrB;UACqBhF,EAAA,CAAAiB,SAAA,GAIrB;UAJqBjB,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAuI,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAA3C,WAAA,QAAA2C,GAAA,CAAA3C,WAAA,MAAA2C,GAAA,CAAA3C,WAAA,MAIrB;UAIEhF,EAAA,CAAAiB,SAAA,GAAuB;UAAvBjB,EAAA,CAAA0B,UAAA,SAAAiG,GAAA,CAAA3C,WAAA,OAAuB;UAmBvBhF,EAAA,CAAAiB,SAAA,EAAuB;UAAvBjB,EAAA,CAAA0B,UAAA,SAAAiG,GAAA,CAAA3C,WAAA,OAAuB;UA0CvBhF,EAAA,CAAAiB,SAAA,EAAuB;UAAvBjB,EAAA,CAAA0B,UAAA,SAAAiG,GAAA,CAAA3C,WAAA,OAAuB;UAkCrBhF,EAAA,CAAAiB,SAAA,GAAuB;UAAvBjB,EAAA,CAAAkB,iBAAA,CAAAyG,GAAA,CAAA1B,eAAA,GAAuB;UAIpBjG,EAAA,CAAAiB,SAAA,GAAqB;UAArBjB,EAAA,CAAA0B,UAAA,SAAAiG,GAAA,CAAA3C,WAAA,KAAqB;UAGrBhF,EAAA,CAAAiB,SAAA,EAAqB;UAArBjB,EAAA,CAAA0B,UAAA,SAAAiG,GAAA,CAAA3C,WAAA,KAAqB;UAIrBhF,EAAA,CAAAiB,SAAA,EAAuB;UAAvBjB,EAAA,CAAA0B,UAAA,SAAAiG,GAAA,CAAA3C,WAAA,OAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}