{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@ng-bootstrap/ng-bootstrap\";\nfunction SpaceComponent_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const createModal_r4 = i0.ɵɵreference(35);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(createModal_r4));\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 20);\n    i0.ɵɵtext(2, \" \\u65B0\\u589E\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_32_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_tr_32_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const editModal_r7 = i0.ɵɵreference(37);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(editModal_r7, item_r6));\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 26);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_32_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_tr_32_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteSpace(item_r6));\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 28);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵelement(6, \"nb-badge\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\")(11, \"div\", 22);\n    i0.ɵɵtemplate(12, SpaceComponent_tr_32_button_12_Template, 2, 0, \"button\", 23)(13, SpaceComponent_tr_32_button_13_Template, 2, 0, \"button\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CSpaceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CDescription || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", item_r6.CIsEnable ? \"success\" : \"danger\")(\"text\", item_r6.CIsEnable ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 7, item_r6.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction SpaceComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"ngb-pagination\", 30);\n    i0.ɵɵtwoWayListener(\"pageChange\", function SpaceComponent_div_33_Template_ngb_pagination_pageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.pageIndex, $event) || (ctx_r2.pageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function SpaceComponent_div_33_Template_ngb_pagination_pageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.pageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r2.pageIndex);\n    i0.ɵɵproperty(\"pageSize\", ctx_r2.pageSize)(\"collectionSize\", ctx_r2.totalRecords)(\"maxSize\", 5)(\"rotate\", true);\n  }\n}\nfunction SpaceComponent_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-header\")(2, \"h5\");\n    i0.ɵɵtext(3, \"\\u65B0\\u589E\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\")(5, \"form\")(6, \"div\", 31)(7, \"label\", 32);\n    i0.ɵɵtext(8, \"\\u7A7A\\u9593\\u540D\\u7A31 \");\n    i0.ɵɵelementStart(9, \"span\", 33);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"input\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_34_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CSpaceName, $event) || (ctx_r2.spaceDetail.CSpaceName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 31)(13, \"label\", 35);\n    i0.ɵɵtext(14, \"\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"textarea\", 36);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_34_Template_textarea_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CDescription, $event) || (ctx_r2.spaceDetail.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(16, \"          \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 31)(18, \"nb-checkbox\", 37);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_34_Template_nb_checkbox_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CIsEnable, $event) || (ctx_r2.spaceDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(19, \" \\u555F\\u7528 \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"nb-card-footer\")(21, \"div\", 38)(22, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_34_Template_button_click_22_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r11));\n    });\n    i0.ɵɵtext(23, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_34_Template_button_click_24_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r11));\n    });\n    i0.ɵɵtext(25, \" \\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CSpaceName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CDescription);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CIsEnable);\n  }\n}\nfunction SpaceComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-header\")(2, \"h5\");\n    i0.ɵɵtext(3, \"\\u7DE8\\u8F2F\\u7A7A\\u9593\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\")(5, \"form\")(6, \"div\", 31)(7, \"label\", 41);\n    i0.ɵɵtext(8, \"\\u7A7A\\u9593\\u540D\\u7A31 \");\n    i0.ɵɵelementStart(9, \"span\", 33);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"input\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_36_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CSpaceName, $event) || (ctx_r2.spaceDetail.CSpaceName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 31)(13, \"label\", 43);\n    i0.ɵɵtext(14, \"\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"textarea\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_36_Template_textarea_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CDescription, $event) || (ctx_r2.spaceDetail.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(16, \"          \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 31)(18, \"nb-checkbox\", 37);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_36_Template_nb_checkbox_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CIsEnable, $event) || (ctx_r2.spaceDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(19, \" \\u555F\\u7528 \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"nb-card-footer\")(21, \"div\", 38)(22, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_36_Template_button_click_22_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r13));\n    });\n    i0.ɵɵtext(23, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_36_Template_button_click_24_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r13));\n    });\n    i0.ɵɵtext(25, \" \\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CSpaceName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CDescription);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CIsEnable);\n  }\n}\nexport class SpaceComponent extends BaseComponent {\n  constructor(allow, dialogService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.spaceList = [];\n    this.spaceDetail = {};\n    this.searchKeyword = '';\n  }\n  ngOnInit() {\n    this.getSpaceList();\n  }\n  getSpaceList() {\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CSpaceName: this.searchKeyword || null\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    })).subscribe();\n  }\n  onSearch() {\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getSpaceList();\n  }\n  openCreateModal(ref) {\n    this.spaceDetail = {\n      CSpaceName: '',\n      CDescription: '',\n      CIsEnable: true\n    };\n    this.dialogService.open(ref);\n  }\n  openEditModal(ref, item) {\n    this.getSpaceById(item.CSpaceId, ref);\n  }\n  getSpaceById(spaceId, ref) {\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\n      body: {\n        CSpaceId: spaceId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceDetail = {\n          ...res.Entries\n        };\n        this.dialogService.open(ref);\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    });\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._spaceService.apiSpaceSaveSpacePost$Json({\n      body: this.spaceDetail\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n        this.getSpaceList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    })).subscribe();\n  }\n  deleteSpace(item) {\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\n        body: {\n          CSpaceId: item.CSpaceId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"刪除成功\");\n          this.getSpaceList();\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除失敗');\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[空間名稱]', this.spaceDetail.CSpaceName);\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CSpaceName, 50);\n    this.valid.isStringMaxLength('[描述]', this.spaceDetail.CDescription, 200);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  static {\n    this.ɵfac = function SpaceComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceComponent,\n      selectors: [[\"ngx-space\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 38,\n      vars: 4,\n      consts: [[\"createModal\", \"\"], [\"editModal\", \"\"], [1, \"row\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [\"nbButton\", \"\", \"status\", \"primary\", \"size\", \"small\", 3, \"click\", 4, \"ngIf\"], [1, \"row\", \"mb-3\"], [1, \"col-md-6\"], [1, \"input-group\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u7A7A\\u9593\\u540D\\u7A31...\", 1, \"form-control\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"icon\", \"search-outline\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\"], [\"width\", \"120\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"d-flex justify-content-center mt-3\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"primary\", \"size\", \"small\", 3, \"click\"], [\"icon\", \"plus-outline\"], [3, \"status\", \"text\"], [1, \"btn-group\"], [\"nbButton\", \"\", \"status\", \"info\", \"size\", \"tiny\", \"nbTooltip\", \"\\u7DE8\\u8F2F\", 3, \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"danger\", \"size\", \"tiny\", \"nbTooltip\", \"\\u522A\\u9664\", 3, \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"info\", \"size\", \"tiny\", \"nbTooltip\", \"\\u7DE8\\u8F2F\", 3, \"click\"], [\"icon\", \"edit-2-outline\"], [\"nbButton\", \"\", \"status\", \"danger\", \"size\", \"tiny\", \"nbTooltip\", \"\\u522A\\u9664\", 3, \"click\"], [\"icon\", \"trash-2-outline\"], [1, \"d-flex\", \"justify-content-center\", \"mt-3\"], [3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\", \"maxSize\", \"rotate\"], [1, \"form-group\"], [\"for\", \"spaceName\"], [1, \"text-danger\"], [\"type\", \"text\", \"id\", \"spaceName\", \"name\", \"spaceName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u7A7A\\u9593\\u540D\\u7A31\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"description\"], [\"id\", \"description\", \"name\", \"description\", \"rows\", \"3\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u7A7A\\u9593\\u63CF\\u8FF0\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"isEnable\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\"], [\"nbButton\", \"\", \"status\", \"basic\", 1, \"mr-2\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\"], [\"for\", \"spaceNameEdit\"], [\"type\", \"text\", \"id\", \"spaceNameEdit\", \"name\", \"spaceName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u7A7A\\u9593\\u540D\\u7A31\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"descriptionEdit\"], [\"id\", \"descriptionEdit\", \"name\", \"description\", \"rows\", \"3\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u7A7A\\u9593\\u63CF\\u8FF0\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"]],\n      template: function SpaceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"nb-card\")(3, \"nb-card-header\")(4, \"div\", 4)(5, \"h5\", 5);\n          i0.ɵɵtext(6, \"\\u7A7A\\u9593\\u8A2D\\u5B9A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, SpaceComponent_button_7_Template, 3, 0, \"button\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"nb-card-body\")(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"input\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_input_ngModelChange_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpaceComponent_Template_input_keyup_enter_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 11)(14, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function SpaceComponent_Template_button_click_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelement(15, \"nb-icon\", 13);\n          i0.ɵɵtext(16, \" \\u641C\\u5C0B \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(17, \"div\", 14)(18, \"table\", 15)(19, \"thead\")(20, \"tr\")(21, \"th\");\n          i0.ɵɵtext(22, \"\\u7A7A\\u9593\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"th\");\n          i0.ɵɵtext(24, \"\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"th\");\n          i0.ɵɵtext(26, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"th\");\n          i0.ɵɵtext(28, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"th\", 16);\n          i0.ɵɵtext(30, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"tbody\");\n          i0.ɵɵtemplate(32, SpaceComponent_tr_32_Template, 14, 10, \"tr\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(33, SpaceComponent_div_33_Template, 2, 5, \"div\", 18);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(34, SpaceComponent_ng_template_34_Template, 26, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(36, SpaceComponent_ng_template_36_Template, 26, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngForOf\", ctx.spaceList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.totalRecords > ctx.pageSize);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.NgModel, i7.NgForm, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbButtonComponent, i2.NbIconComponent, i8.NgbPagination, i6.DatePipe],\n      styles: [\".btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: var(--color-bg-2);\\n  font-weight: 600;\\n  border-bottom: 2px solid var(--color-bg-3);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: var(--color-bg-1);\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0;\\n  border-bottom-left-radius: 0;\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--color-fg-heading);\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: var(--color-danger) !important;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.mt-3[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInNwYWNlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNFO0VBQ0UscUJBQUE7QUFBSjtBQUVJO0VBQ0UsZUFBQTtBQUFOOztBQU1FO0VBQ0UsbUNBQUE7RUFDQSxnQkFBQTtFQUNBLDBDQUFBO0FBSEo7QUFPSTtFQUNFLG1DQUFBO0FBTE47O0FBWUk7RUFDRSx5QkFBQTtFQUNBLDRCQUFBO0FBVE47O0FBZUU7RUFDRSw4QkFBQTtBQVpKOztBQWdCQTtFQUNFLHFDQUFBO0FBYkY7O0FBZ0JBO0VBQ0Usb0JBQUE7QUFiRjs7QUFnQkE7RUFDRSxtQkFBQTtBQWJGOztBQWdCQTtFQUNFLGdCQUFBO0FBYkYiLCJmaWxlIjoic3BhY2UuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuYnRuLWdyb3VwIHtcclxuICBidXR0b24ge1xyXG4gICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtO1xyXG5cclxuICAgICY6bGFzdC1jaGlsZCB7XHJcbiAgICAgIG1hcmdpbi1yaWdodDogMDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi50YWJsZSB7XHJcbiAgdGgge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tY29sb3ItYmctMik7XHJcbiAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkIHZhcigtLWNvbG9yLWJnLTMpO1xyXG4gIH1cclxuXHJcbiAgdGJvZHkgdHIge1xyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWNvbG9yLWJnLTEpO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLmlucHV0LWdyb3VwIHtcclxuICAuaW5wdXQtZ3JvdXAtYXBwZW5kIHtcclxuICAgIC5idG4ge1xyXG4gICAgICBib3JkZXItdG9wLWxlZnQtcmFkaXVzOiAwO1xyXG4gICAgICBib3JkZXItYm90dG9tLWxlZnQtcmFkaXVzOiAwO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxubmItY2FyZC1oZWFkZXIge1xyXG4gIGg1IHtcclxuICAgIGNvbG9yOiB2YXIoLS1jb2xvci1mZy1oZWFkaW5nKTtcclxuICB9XHJcbn1cclxuXHJcbi50ZXh0LWRhbmdlciB7XHJcbiAgY29sb3I6IHZhcigtLWNvbG9yLWRhbmdlcikgIWltcG9ydGFudDtcclxufVxyXG5cclxuLm1yLTIge1xyXG4gIG1hcmdpbi1yaWdodDogMC41cmVtO1xyXG59XHJcblxyXG4ubWItMyB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxufVxyXG5cclxuLm10LTMge1xyXG4gIG1hcmdpbi10b3A6IDFyZW07XHJcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvc3BhY2Uvc3BhY2UuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0U7RUFDRSxxQkFBQTtBQUFKO0FBRUk7RUFDRSxlQUFBO0FBQU47O0FBTUU7RUFDRSxtQ0FBQTtFQUNBLGdCQUFBO0VBQ0EsMENBQUE7QUFISjtBQU9JO0VBQ0UsbUNBQUE7QUFMTjs7QUFZSTtFQUNFLHlCQUFBO0VBQ0EsNEJBQUE7QUFUTjs7QUFlRTtFQUNFLDhCQUFBO0FBWko7O0FBZ0JBO0VBQ0UscUNBQUE7QUFiRjs7QUFnQkE7RUFDRSxvQkFBQTtBQWJGOztBQWdCQTtFQUNFLG1CQUFBO0FBYkY7O0FBZ0JBO0VBQ0UsZ0JBQUE7QUFiRjtBQUNBLHdvREFBd29EIiwic291cmNlc0NvbnRlbnQiOlsiLmJ0bi1ncm91cCB7XHJcbiAgYnV0dG9uIHtcclxuICAgIG1hcmdpbi1yaWdodDogMC4yNXJlbTtcclxuXHJcbiAgICAmOmxhc3QtY2hpbGQge1xyXG4gICAgICBtYXJnaW4tcmlnaHQ6IDA7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4udGFibGUge1xyXG4gIHRoIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWNvbG9yLWJnLTIpO1xyXG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCB2YXIoLS1jb2xvci1iZy0zKTtcclxuICB9XHJcblxyXG4gIHRib2R5IHRyIHtcclxuICAgICY6aG92ZXIge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1jb2xvci1iZy0xKTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi5pbnB1dC1ncm91cCB7XHJcbiAgLmlucHV0LWdyb3VwLWFwcGVuZCB7XHJcbiAgICAuYnRuIHtcclxuICAgICAgYm9yZGVyLXRvcC1sZWZ0LXJhZGl1czogMDtcclxuICAgICAgYm9yZGVyLWJvdHRvbS1sZWZ0LXJhZGl1czogMDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbm5iLWNhcmQtaGVhZGVyIHtcclxuICBoNSB7XHJcbiAgICBjb2xvcjogdmFyKC0tY29sb3ItZmctaGVhZGluZyk7XHJcbiAgfVxyXG59XHJcblxyXG4udGV4dC1kYW5nZXIge1xyXG4gIGNvbG9yOiB2YXIoLS1jb2xvci1kYW5nZXIpICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5tci0yIHtcclxuICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcclxufVxyXG5cclxuLm1iLTMge1xyXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbn1cclxuXHJcbi5tdC0zIHtcclxuICBtYXJnaW4tdG9wOiAxcmVtO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "SpaceComponent_button_7_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "createModal_r4", "ɵɵreference", "ɵɵresetView", "openCreateModal", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "SpaceComponent_tr_32_button_12_Template_button_click_0_listener", "_r5", "item_r6", "$implicit", "editModal_r7", "openEditModal", "SpaceComponent_tr_32_button_13_Template_button_click_0_listener", "_r8", "deleteSpace", "ɵɵtemplate", "SpaceComponent_tr_32_button_12_Template", "SpaceComponent_tr_32_button_13_Template", "ɵɵadvance", "ɵɵtextInterpolate", "CSpaceName", "CDescription", "ɵɵproperty", "CIsEnable", "ɵɵpipeBind2", "CCreateDt", "isUpdate", "isDelete", "ɵɵtwoWayListener", "SpaceComponent_div_33_Template_ngb_pagination_pageChange_1_listener", "$event", "_r9", "ɵɵtwoWayBindingSet", "pageIndex", "pageChanged", "ɵɵtwoWayProperty", "pageSize", "totalRecords", "SpaceComponent_ng_template_34_Template_input_ngModelChange_11_listener", "_r10", "spaceDetail", "SpaceComponent_ng_template_34_Template_textarea_ngModelChange_15_listener", "SpaceComponent_ng_template_34_Template_nb_checkbox_ngModelChange_18_listener", "SpaceComponent_ng_template_34_Template_button_click_22_listener", "ref_r11", "dialogRef", "onClose", "SpaceComponent_ng_template_34_Template_button_click_24_listener", "onSubmit", "SpaceComponent_ng_template_36_Template_input_ngModelChange_11_listener", "_r12", "SpaceComponent_ng_template_36_Template_textarea_ngModelChange_15_listener", "SpaceComponent_ng_template_36_Template_nb_checkbox_ngModelChange_18_listener", "SpaceComponent_ng_template_36_Template_button_click_22_listener", "ref_r13", "SpaceComponent_ng_template_36_Template_button_click_24_listener", "SpaceComponent", "constructor", "allow", "dialogService", "_spaceService", "message", "valid", "pageFirst", "spaceList", "searchKeyword", "ngOnInit", "getSpaceList", "apiSpaceGetSpaceListPost$Json", "body", "PageIndex", "PageSize", "pipe", "res", "Entries", "StatusCode", "TotalItems", "showErrorMSG", "Message", "subscribe", "onSearch", "newPage", "ref", "open", "item", "getSpaceById", "CSpaceId", "spaceId", "apiSpaceGetSpaceByIdPost$Json", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpaceSaveSpacePost$Json", "showSucessMSG", "close", "confirm", "apiSpaceDeleteSpacePost$Json", "clear", "required", "isStringMaxLength", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "SpaceService", "i4", "MessageService", "i5", "ValidationHelper", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "SpaceComponent_Template", "rf", "ctx", "SpaceComponent_button_7_Template", "SpaceComponent_Template_input_ngModelChange_12_listener", "_r1", "SpaceComponent_Template_input_keyup_enter_12_listener", "SpaceComponent_Template_button_click_14_listener", "SpaceComponent_tr_32_Template", "SpaceComponent_div_33_Template", "SpaceComponent_ng_template_34_Template", "ɵɵtemplateRefExtractor", "SpaceComponent_ng_template_36_Template", "isCreate"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\space\\space.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\space\\space.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { tap } from 'rxjs';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-space',\r\n  templateUrl: './space.component.html',\r\n  styleUrls: ['./space.component.scss'],\r\n})\r\n\r\nexport class SpaceComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    protected allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  spaceList: any[] = [];\r\n  spaceDetail: any = {};\r\n  searchKeyword: string = '';\r\n\r\n  override ngOnInit(): void {\r\n    this.getSpaceList();\r\n  }\r\n\r\n  getSpaceList() {\r\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\r\n      body: {\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CSpaceName: this.searchKeyword || null\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.spaceList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSearch() {\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  openCreateModal(ref: any) {\r\n    this.spaceDetail = {\r\n      CSpaceName: '',\r\n      CDescription: '',\r\n      CIsEnable: true\r\n    };\r\n    this.dialogService.open(ref);\r\n  }\r\n\r\n  openEditModal(ref: any, item: any) {\r\n    this.getSpaceById(item.CSpaceId, ref);\r\n  }\r\n\r\n  getSpaceById(spaceId: number, ref: any) {\r\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\r\n      body: { CSpaceId: spaceId }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.spaceDetail = { ...res.Entries };\r\n        this.dialogService.open(ref);\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this._spaceService.apiSpaceSaveSpacePost$Json({\r\n      body: this.spaceDetail\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  deleteSpace(item: any) {\r\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\r\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\r\n        body: { CSpaceId: item.CSpaceId }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"刪除成功\");\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '刪除失敗');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[空間名稱]', this.spaceDetail.CSpaceName);\r\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CSpaceName, 50);\r\n    this.valid.isStringMaxLength('[描述]', this.spaceDetail.CDescription, 200);\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}", "<div class=\"row\">\r\n  <div class=\"col-12\">\r\n    <nb-card>\r\n      <nb-card-header>\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <h5 class=\"mb-0\">空間設定</h5>\r\n          <button \r\n            nbButton \r\n            status=\"primary\" \r\n            size=\"small\"\r\n            (click)=\"openCreateModal(createModal)\"\r\n            *ngIf=\"isCreate\">\r\n            <nb-icon icon=\"plus-outline\"></nb-icon>\r\n            新增空間\r\n          </button>\r\n        </div>\r\n      </nb-card-header>\r\n\r\n      <nb-card-body>\r\n        <!-- 搜尋區域 -->\r\n        <div class=\"row mb-3\">\r\n          <div class=\"col-md-6\">\r\n            <div class=\"input-group\">\r\n              <input \r\n                type=\"text\" \r\n                class=\"form-control\" \r\n                placeholder=\"搜尋空間名稱...\"\r\n                [(ngModel)]=\"searchKeyword\"\r\n                (keyup.enter)=\"onSearch()\">\r\n              <div class=\"input-group-append\">\r\n                <button class=\"btn btn-primary\" type=\"button\" (click)=\"onSearch()\">\r\n                  <nb-icon icon=\"search-outline\"></nb-icon>\r\n                  搜尋\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 資料表格 -->\r\n        <div class=\"table-responsive\">\r\n          <table class=\"table table-striped\">\r\n            <thead>\r\n              <tr>\r\n                <th>空間名稱</th>\r\n                <th>描述</th>\r\n                <th>狀態</th>\r\n                <th>建立時間</th>\r\n                <th width=\"120\">操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let item of spaceList\">\r\n                <td>{{ item.CSpaceName }}</td>\r\n                <td>{{ item.CDescription || '-' }}</td>\r\n                <td>\r\n                  <nb-badge \r\n                    [status]=\"item.CIsEnable ? 'success' : 'danger'\"\r\n                    [text]=\"item.CIsEnable ? '啟用' : '停用'\">\r\n                  </nb-badge>\r\n                </td>\r\n                <td>{{ item.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>\r\n                <td>\r\n                  <div class=\"btn-group\">\r\n                    <button \r\n                      nbButton \r\n                      status=\"info\" \r\n                      size=\"tiny\"\r\n                      (click)=\"openEditModal(editModal, item)\"\r\n                      *ngIf=\"isUpdate\"\r\n                      nbTooltip=\"編輯\">\r\n                      <nb-icon icon=\"edit-2-outline\"></nb-icon>\r\n                    </button>\r\n                    <button \r\n                      nbButton \r\n                      status=\"danger\" \r\n                      size=\"tiny\"\r\n                      (click)=\"deleteSpace(item)\"\r\n                      *ngIf=\"isDelete\"\r\n                      nbTooltip=\"刪除\">\r\n                      <nb-icon icon=\"trash-2-outline\"></nb-icon>\r\n                    </button>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n\r\n        <!-- 分頁 -->\r\n        <div class=\"d-flex justify-content-center mt-3\" *ngIf=\"totalRecords > pageSize\">\r\n          <ngb-pagination \r\n            [(page)]=\"pageIndex\"\r\n            [pageSize]=\"pageSize\"\r\n            [collectionSize]=\"totalRecords\"\r\n            [maxSize]=\"5\"\r\n            [rotate]=\"true\"\r\n            (pageChange)=\"pageChanged($event)\">\r\n          </ngb-pagination>\r\n        </div>\r\n      </nb-card-body>\r\n    </nb-card>\r\n  </div>\r\n</div>\r\n\r\n<!-- 新增模態框 -->\r\n<ng-template #createModal let-ref=\"dialogRef\">\r\n  <nb-card>\r\n    <nb-card-header>\r\n      <h5>新增空間</h5>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <form>\r\n        <div class=\"form-group\">\r\n          <label for=\"spaceName\">空間名稱 <span class=\"text-danger\">*</span></label>\r\n          <input \r\n            type=\"text\" \r\n            id=\"spaceName\"\r\n            class=\"form-control\" \r\n            [(ngModel)]=\"spaceDetail.CSpaceName\"\r\n            name=\"spaceName\"\r\n            placeholder=\"請輸入空間名稱\">\r\n        </div>\r\n        \r\n        <div class=\"form-group\">\r\n          <label for=\"description\">描述</label>\r\n          <textarea \r\n            id=\"description\"\r\n            class=\"form-control\" \r\n            [(ngModel)]=\"spaceDetail.CDescription\"\r\n            name=\"description\"\r\n            rows=\"3\"\r\n            placeholder=\"請輸入空間描述\">\r\n          </textarea>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <nb-checkbox [(ngModel)]=\"spaceDetail.CIsEnable\" name=\"isEnable\">\r\n            啟用\r\n          </nb-checkbox>\r\n        </div>\r\n      </form>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"d-flex justify-content-end\">\r\n        <button nbButton status=\"basic\" class=\"mr-2\" (click)=\"onClose(ref)\">\r\n          取消\r\n        </button>\r\n        <button nbButton status=\"primary\" (click)=\"onSubmit(ref)\">\r\n          確認\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 編輯模態框 -->\r\n<ng-template #editModal let-ref=\"dialogRef\">\r\n  <nb-card>\r\n    <nb-card-header>\r\n      <h5>編輯空間</h5>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <form>\r\n        <div class=\"form-group\">\r\n          <label for=\"spaceNameEdit\">空間名稱 <span class=\"text-danger\">*</span></label>\r\n          <input \r\n            type=\"text\" \r\n            id=\"spaceNameEdit\"\r\n            class=\"form-control\" \r\n            [(ngModel)]=\"spaceDetail.CSpaceName\"\r\n            name=\"spaceName\"\r\n            placeholder=\"請輸入空間名稱\">\r\n        </div>\r\n        \r\n        <div class=\"form-group\">\r\n          <label for=\"descriptionEdit\">描述</label>\r\n          <textarea \r\n            id=\"descriptionEdit\"\r\n            class=\"form-control\" \r\n            [(ngModel)]=\"spaceDetail.CDescription\"\r\n            name=\"description\"\r\n            rows=\"3\"\r\n            placeholder=\"請輸入空間描述\">\r\n          </textarea>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <nb-checkbox [(ngModel)]=\"spaceDetail.CIsEnable\" name=\"isEnable\">\r\n            啟用\r\n          </nb-checkbox>\r\n        </div>\r\n      </form>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"d-flex justify-content-end\">\r\n        <button nbButton status=\"basic\" class=\"mr-2\" (click)=\"onClose(ref)\">\r\n          取消\r\n        </button>\r\n        <button nbButton status=\"primary\" (click)=\"onSubmit(ref)\">\r\n          確認\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAIA,SAASA,aAAa,QAAQ,qCAAqC;AAKnE,SAASC,GAAG,QAAQ,MAAM;;;;;;;;;;;;;ICHhBC,EAAA,CAAAC,cAAA,iBAKmB;IADjBD,EAAA,CAAAE,UAAA,mBAAAC,yDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,cAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,eAAA,CAAAH,cAAA,CAA4B;IAAA,EAAC;IAEtCR,EAAA,CAAAY,SAAA,kBAAuC;IACvCZ,EAAA,CAAAa,MAAA,iCACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAkDCd,EAAA,CAAAC,cAAA,iBAMiB;IAFfD,EAAA,CAAAE,UAAA,mBAAAa,gEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAY,YAAA,GAAAnB,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAc,aAAA,CAAAD,YAAA,EAAAF,OAAA,CAA8B;IAAA,EAAC;IAGxCjB,EAAA,CAAAY,SAAA,kBAAyC;IAC3CZ,EAAA,CAAAc,YAAA,EAAS;;;;;;IACTd,EAAA,CAAAC,cAAA,iBAMiB;IAFfD,EAAA,CAAAE,UAAA,mBAAAmB,gEAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA;MAAA,MAAAL,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiB,WAAA,CAAAN,OAAA,CAAiB;IAAA,EAAC;IAG3BjB,EAAA,CAAAY,SAAA,kBAA0C;IAC5CZ,EAAA,CAAAc,YAAA,EAAS;;;;;IA5Bbd,EADF,CAAAC,cAAA,SAAmC,SAC7B;IAAAD,EAAA,CAAAa,MAAA,GAAqB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC9Bd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,mBAGW;IACbZ,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAA+C;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEtDd,EADF,CAAAC,cAAA,UAAI,eACqB;IAUrBD,EATA,CAAAwB,UAAA,KAAAC,uCAAA,qBAMiB,KAAAC,uCAAA,qBASA;IAKvB1B,EAFI,CAAAc,YAAA,EAAM,EACH,EACF;;;;;IA/BCd,EAAA,CAAA2B,SAAA,GAAqB;IAArB3B,EAAA,CAAA4B,iBAAA,CAAAX,OAAA,CAAAY,UAAA,CAAqB;IACrB7B,EAAA,CAAA2B,SAAA,GAA8B;IAA9B3B,EAAA,CAAA4B,iBAAA,CAAAX,OAAA,CAAAa,YAAA,QAA8B;IAG9B9B,EAAA,CAAA2B,SAAA,GAAgD;IAChD3B,EADA,CAAA+B,UAAA,WAAAd,OAAA,CAAAe,SAAA,wBAAgD,SAAAf,OAAA,CAAAe,SAAA,mCACX;IAGrChC,EAAA,CAAA2B,SAAA,GAA+C;IAA/C3B,EAAA,CAAA4B,iBAAA,CAAA5B,EAAA,CAAAiC,WAAA,OAAAhB,OAAA,CAAAiB,SAAA,sBAA+C;IAQ5ClC,EAAA,CAAA2B,SAAA,GAAc;IAAd3B,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA6B,QAAA,CAAc;IASdnC,EAAA,CAAA2B,SAAA,EAAc;IAAd3B,EAAA,CAAA+B,UAAA,SAAAzB,MAAA,CAAA8B,QAAA,CAAc;;;;;;IAa3BpC,EADF,CAAAC,cAAA,cAAgF,yBAOzC;IALnCD,EAAA,CAAAqC,gBAAA,wBAAAC,oEAAAC,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAyC,kBAAA,CAAAnC,MAAA,CAAAoC,SAAA,EAAAH,MAAA,MAAAjC,MAAA,CAAAoC,SAAA,GAAAH,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAAoB;IAKpBvC,EAAA,CAAAE,UAAA,wBAAAoC,oEAAAC,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAcJ,MAAA,CAAAqC,WAAA,CAAAJ,MAAA,CAAmB;IAAA,EAAC;IAEtCvC,EADE,CAAAc,YAAA,EAAiB,EACb;;;;IAPFd,EAAA,CAAA2B,SAAA,EAAoB;IAApB3B,EAAA,CAAA4C,gBAAA,SAAAtC,MAAA,CAAAoC,SAAA,CAAoB;IAIpB1C,EAHA,CAAA+B,UAAA,aAAAzB,MAAA,CAAAuC,QAAA,CAAqB,mBAAAvC,MAAA,CAAAwC,YAAA,CACU,cAClB,gBACE;;;;;;IAarB9C,EAFJ,CAAAC,cAAA,cAAS,qBACS,SACV;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IACVb,EADU,CAAAc,YAAA,EAAK,EACE;IAIXd,EAHN,CAAAC,cAAA,mBAAc,WACN,cACoB,gBACC;IAAAD,EAAA,CAAAa,MAAA,gCAAK;IAAAb,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAa,MAAA,SAAC;IAAOb,EAAP,CAAAc,YAAA,EAAO,EAAQ;IACtEd,EAAA,CAAAC,cAAA,iBAMwB;IAFtBD,EAAA,CAAAqC,gBAAA,2BAAAU,uEAAAR,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAA4C,IAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAyC,kBAAA,CAAAnC,MAAA,CAAA2C,WAAA,CAAApB,UAAA,EAAAU,MAAA,MAAAjC,MAAA,CAAA2C,WAAA,CAAApB,UAAA,GAAAU,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAAoC;IAGxCvC,EAPE,CAAAc,YAAA,EAMwB,EACpB;IAGJd,EADF,CAAAC,cAAA,eAAwB,iBACG;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACnCd,EAAA,CAAAC,cAAA,oBAMwB;IAHtBD,EAAA,CAAAqC,gBAAA,2BAAAa,0EAAAX,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAA4C,IAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAyC,kBAAA,CAAAnC,MAAA,CAAA2C,WAAA,CAAAnB,YAAA,EAAAS,MAAA,MAAAjC,MAAA,CAAA2C,WAAA,CAAAnB,YAAA,GAAAS,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAAsC;IAIxCvC,EAAA,CAAAa,MAAA;IACFb,EADE,CAAAc,YAAA,EAAW,EACP;IAGJd,EADF,CAAAC,cAAA,eAAwB,uBAC2C;IAApDD,EAAA,CAAAqC,gBAAA,2BAAAc,6EAAAZ,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAA4C,IAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAyC,kBAAA,CAAAnC,MAAA,CAAA2C,WAAA,CAAAjB,SAAA,EAAAO,MAAA,MAAAjC,MAAA,CAAA2C,WAAA,CAAAjB,SAAA,GAAAO,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAAmC;IAC9CvC,EAAA,CAAAa,MAAA,sBACF;IAGNb,EAHM,CAAAc,YAAA,EAAc,EACV,EACD,EACM;IAGXd,EAFJ,CAAAC,cAAA,sBAAgB,eAC0B,kBAC8B;IAAvBD,EAAA,CAAAE,UAAA,mBAAAkD,gEAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAAI,aAAA,CAAA4C,IAAA,EAAAM,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiD,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IACjErD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAA0D;IAAxBD,EAAA,CAAAE,UAAA,mBAAAsD,gEAAA;MAAA,MAAAH,OAAA,GAAArD,EAAA,CAAAI,aAAA,CAAA4C,IAAA,EAAAM,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAmD,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IACvDrD,EAAA,CAAAa,MAAA,sBACF;IAGNb,EAHM,CAAAc,YAAA,EAAS,EACL,EACS,EACT;;;;IAlCAd,EAAA,CAAA2B,SAAA,IAAoC;IAApC3B,EAAA,CAAA4C,gBAAA,YAAAtC,MAAA,CAAA2C,WAAA,CAAApB,UAAA,CAAoC;IAUpC7B,EAAA,CAAA2B,SAAA,GAAsC;IAAtC3B,EAAA,CAAA4C,gBAAA,YAAAtC,MAAA,CAAA2C,WAAA,CAAAnB,YAAA,CAAsC;IAQ3B9B,EAAA,CAAA2B,SAAA,GAAmC;IAAnC3B,EAAA,CAAA4C,gBAAA,YAAAtC,MAAA,CAAA2C,WAAA,CAAAjB,SAAA,CAAmC;;;;;;IAuBpDhC,EAFJ,CAAAC,cAAA,cAAS,qBACS,SACV;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IACVb,EADU,CAAAc,YAAA,EAAK,EACE;IAIXd,EAHN,CAAAC,cAAA,mBAAc,WACN,cACoB,gBACK;IAAAD,EAAA,CAAAa,MAAA,gCAAK;IAAAb,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAa,MAAA,SAAC;IAAOb,EAAP,CAAAc,YAAA,EAAO,EAAQ;IAC1Ed,EAAA,CAAAC,cAAA,iBAMwB;IAFtBD,EAAA,CAAAqC,gBAAA,2BAAAqB,uEAAAnB,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAuD,IAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAyC,kBAAA,CAAAnC,MAAA,CAAA2C,WAAA,CAAApB,UAAA,EAAAU,MAAA,MAAAjC,MAAA,CAAA2C,WAAA,CAAApB,UAAA,GAAAU,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAAoC;IAGxCvC,EAPE,CAAAc,YAAA,EAMwB,EACpB;IAGJd,EADF,CAAAC,cAAA,eAAwB,iBACO;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACvCd,EAAA,CAAAC,cAAA,oBAMwB;IAHtBD,EAAA,CAAAqC,gBAAA,2BAAAuB,0EAAArB,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAuD,IAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAyC,kBAAA,CAAAnC,MAAA,CAAA2C,WAAA,CAAAnB,YAAA,EAAAS,MAAA,MAAAjC,MAAA,CAAA2C,WAAA,CAAAnB,YAAA,GAAAS,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAAsC;IAIxCvC,EAAA,CAAAa,MAAA;IACFb,EADE,CAAAc,YAAA,EAAW,EACP;IAGJd,EADF,CAAAC,cAAA,eAAwB,uBAC2C;IAApDD,EAAA,CAAAqC,gBAAA,2BAAAwB,6EAAAtB,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAuD,IAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAyC,kBAAA,CAAAnC,MAAA,CAAA2C,WAAA,CAAAjB,SAAA,EAAAO,MAAA,MAAAjC,MAAA,CAAA2C,WAAA,CAAAjB,SAAA,GAAAO,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAAmC;IAC9CvC,EAAA,CAAAa,MAAA,sBACF;IAGNb,EAHM,CAAAc,YAAA,EAAc,EACV,EACD,EACM;IAGXd,EAFJ,CAAAC,cAAA,sBAAgB,eAC0B,kBAC8B;IAAvBD,EAAA,CAAAE,UAAA,mBAAA4D,gEAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAI,aAAA,CAAAuD,IAAA,EAAAL,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiD,OAAA,CAAAQ,OAAA,CAAY;IAAA,EAAC;IACjE/D,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAA0D;IAAxBD,EAAA,CAAAE,UAAA,mBAAA8D,gEAAA;MAAA,MAAAD,OAAA,GAAA/D,EAAA,CAAAI,aAAA,CAAAuD,IAAA,EAAAL,SAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAmD,QAAA,CAAAM,OAAA,CAAa;IAAA,EAAC;IACvD/D,EAAA,CAAAa,MAAA,sBACF;IAGNb,EAHM,CAAAc,YAAA,EAAS,EACL,EACS,EACT;;;;IAlCAd,EAAA,CAAA2B,SAAA,IAAoC;IAApC3B,EAAA,CAAA4C,gBAAA,YAAAtC,MAAA,CAAA2C,WAAA,CAAApB,UAAA,CAAoC;IAUpC7B,EAAA,CAAA2B,SAAA,GAAsC;IAAtC3B,EAAA,CAAA4C,gBAAA,YAAAtC,MAAA,CAAA2C,WAAA,CAAAnB,YAAA,CAAsC;IAQ3B9B,EAAA,CAAA2B,SAAA,GAAmC;IAAnC3B,EAAA,CAAA4C,gBAAA,YAAAtC,MAAA,CAAA2C,WAAA,CAAAjB,SAAA,CAAmC;;;ADrK1D,OAAM,MAAOiC,cAAe,SAAQnE,aAAa;EAC/CoE,YACYC,KAAkB,EACpBC,aAA8B,EAC9BC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACJ,KAAK,CAAC;IANF,KAAAA,KAAK,GAALA,KAAK;IACP,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAKN,KAAAC,SAAS,GAAG,CAAC;IACb,KAAA3B,QAAQ,GAAG,EAAE;IACb,KAAAH,SAAS,GAAG,CAAC;IACb,KAAAI,YAAY,GAAG,CAAC;IAEzB,KAAA2B,SAAS,GAAU,EAAE;IACrB,KAAAxB,WAAW,GAAQ,EAAE;IACrB,KAAAyB,aAAa,GAAW,EAAE;EAT1B;EAWSC,QAAQA,CAAA;IACf,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,OAAO,IAAI,CAACP,aAAa,CAACQ,6BAA6B,CAAC;MACtDC,IAAI,EAAE;QACJC,SAAS,EAAE,IAAI,CAACrC,SAAS;QACzBsC,QAAQ,EAAE,IAAI,CAACnC,QAAQ;QACvBhB,UAAU,EAAE,IAAI,CAAC6C,aAAa,IAAI;;KAErC,CAAC,CAACO,IAAI,CACLlF,GAAG,CAACmF,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACX,SAAS,GAAGS,GAAG,CAACC,OAAO;QAC5B,IAAI,CAACrC,YAAY,GAAGoC,GAAG,CAACG,UAAW;MACrC,CAAC,MAAM;QACL,IAAI,CAACf,OAAO,CAACgB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC/C,SAAS,GAAG,CAAC;IAClB,IAAI,CAACkC,YAAY,EAAE;EACrB;EAEAjC,WAAWA,CAAC+C,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;IACxB,IAAI,CAACd,YAAY,EAAE;EACrB;EAEAjE,eAAeA,CAACgF,GAAQ;IACtB,IAAI,CAAC1C,WAAW,GAAG;MACjBpB,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBE,SAAS,EAAE;KACZ;IACD,IAAI,CAACoC,aAAa,CAACwB,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEAvE,aAAaA,CAACuE,GAAQ,EAAEE,IAAS;IAC/B,IAAI,CAACC,YAAY,CAACD,IAAI,CAACE,QAAQ,EAAEJ,GAAG,CAAC;EACvC;EAEAG,YAAYA,CAACE,OAAe,EAAEL,GAAQ;IACpC,IAAI,CAACtB,aAAa,CAAC4B,6BAA6B,CAAC;MAC/CnB,IAAI,EAAE;QAAEiB,QAAQ,EAAEC;MAAO;KAC1B,CAAC,CAACR,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACnC,WAAW,GAAG;UAAE,GAAGiC,GAAG,CAACC;QAAO,CAAE;QACrC,IAAI,CAACf,aAAa,CAACwB,IAAI,CAACD,GAAG,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAACrB,OAAO,CAACgB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEA9B,QAAQA,CAACkC,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC3B,KAAK,CAAC4B,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC9B,OAAO,CAAC+B,aAAa,CAAC,IAAI,CAAC9B,KAAK,CAAC4B,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC9B,aAAa,CAACiC,0BAA0B,CAAC;MAC5CxB,IAAI,EAAE,IAAI,CAAC7B;KACZ,CAAC,CAACgC,IAAI,CACLlF,GAAG,CAACmF,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACd,OAAO,CAACiC,aAAa,CAAC,MAAM,CAAC;QAClCZ,GAAG,CAACa,KAAK,EAAE;QACX,IAAI,CAAC5B,YAAY,EAAE;MACrB,CAAC,MAAM;QACL,IAAI,CAACN,OAAO,CAACgB,YAAY,CAACJ,GAAG,CAACK,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAjE,WAAWA,CAACsE,IAAS;IACnB,IAAIY,OAAO,CAAC,eAAe,CAAC,EAAE;MAC5B,IAAI,CAACpC,aAAa,CAACqC,4BAA4B,CAAC;QAC9C5B,IAAI,EAAE;UAAEiB,QAAQ,EAAEF,IAAI,CAACE;QAAQ;OAChC,CAAC,CAACP,SAAS,CAACN,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACd,OAAO,CAACiC,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAAC3B,YAAY,EAAE;QACrB,CAAC,MAAM;UACL,IAAI,CAACN,OAAO,CAACgB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,MAAM,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EACF;EAEAW,UAAUA,CAAA;IACR,IAAI,CAAC3B,KAAK,CAACoC,KAAK,EAAE;IAClB,IAAI,CAACpC,KAAK,CAACqC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC3D,WAAW,CAACpB,UAAU,CAAC;IAC1D,IAAI,CAAC0C,KAAK,CAACsC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC5D,WAAW,CAACpB,UAAU,EAAE,EAAE,CAAC;IACvE,IAAI,CAAC0C,KAAK,CAACsC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC5D,WAAW,CAACnB,YAAY,EAAE,GAAG,CAAC;EAC1E;EAEAyB,OAAOA,CAACoC,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;;;uCA7HWvC,cAAc,EAAAjE,EAAA,CAAA8G,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhH,EAAA,CAAA8G,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAlH,EAAA,CAAA8G,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAApH,EAAA,CAAA8G,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAtH,EAAA,CAAA8G,iBAAA,CAAAS,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAdvD,cAAc;MAAAwD,SAAA;MAAAC,QAAA,GAAA1H,EAAA,CAAA2H,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UClBjBjI,EALV,CAAAC,cAAA,aAAiB,aACK,cACT,qBACS,aACiD,YAC5C;UAAAD,EAAA,CAAAa,MAAA,+BAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC1Bd,EAAA,CAAAwB,UAAA,IAAA2G,gCAAA,oBAKmB;UAKvBnI,EADE,CAAAc,YAAA,EAAM,EACS;UAOTd,EALR,CAAAC,cAAA,mBAAc,aAEU,cACE,cACK,iBAMM;UAD3BD,EAAA,CAAAqC,gBAAA,2BAAA+F,wDAAA7F,MAAA;YAAAvC,EAAA,CAAAI,aAAA,CAAAiI,GAAA;YAAArI,EAAA,CAAAyC,kBAAA,CAAAyF,GAAA,CAAAxD,aAAA,EAAAnC,MAAA,MAAA2F,GAAA,CAAAxD,aAAA,GAAAnC,MAAA;YAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;UAAA,EAA2B;UAC3BvC,EAAA,CAAAE,UAAA,yBAAAoI,sDAAA;YAAAtI,EAAA,CAAAI,aAAA,CAAAiI,GAAA;YAAA,OAAArI,EAAA,CAAAU,WAAA,CAAewH,GAAA,CAAAzC,QAAA,EAAU;UAAA,EAAC;UAL5BzF,EAAA,CAAAc,YAAA,EAK6B;UAE3Bd,EADF,CAAAC,cAAA,eAAgC,kBACqC;UAArBD,EAAA,CAAAE,UAAA,mBAAAqI,iDAAA;YAAAvI,EAAA,CAAAI,aAAA,CAAAiI,GAAA;YAAA,OAAArI,EAAA,CAAAU,WAAA,CAASwH,GAAA,CAAAzC,QAAA,EAAU;UAAA,EAAC;UAChEzF,EAAA,CAAAY,SAAA,mBAAyC;UACzCZ,EAAA,CAAAa,MAAA,sBACF;UAIRb,EAJQ,CAAAc,YAAA,EAAS,EACL,EACF,EACF,EACF;UAOEd,EAJR,CAAAC,cAAA,eAA8B,iBACO,aAC1B,UACD,UACE;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACbd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACXd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACXd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACbd,EAAA,CAAAC,cAAA,cAAgB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAEtBb,EAFsB,CAAAc,YAAA,EAAK,EACpB,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAwB,UAAA,KAAAgH,6BAAA,mBAAmC;UAmCzCxI,EAFI,CAAAc,YAAA,EAAQ,EACF,EACJ;UAGNd,EAAA,CAAAwB,UAAA,KAAAiH,8BAAA,kBAAgF;UAaxFzI,EAHM,CAAAc,YAAA,EAAe,EACP,EACN,EACF;UAsDNd,EAnDA,CAAAwB,UAAA,KAAAkH,sCAAA,iCAAA1I,EAAA,CAAA2I,sBAAA,CAA8C,KAAAC,sCAAA,iCAAA5I,EAAA,CAAA2I,sBAAA,CAmDF;;;UAlJ/B3I,EAAA,CAAA2B,SAAA,GAAc;UAAd3B,EAAA,CAAA+B,UAAA,SAAAmG,GAAA,CAAAW,QAAA,CAAc;UAgBX7I,EAAA,CAAA2B,SAAA,GAA2B;UAA3B3B,EAAA,CAAA4C,gBAAA,YAAAsF,GAAA,CAAAxD,aAAA,CAA2B;UAyBR1E,EAAA,CAAA2B,SAAA,IAAY;UAAZ3B,EAAA,CAAA+B,UAAA,YAAAmG,GAAA,CAAAzD,SAAA,CAAY;UAsCUzE,EAAA,CAAA2B,SAAA,EAA6B;UAA7B3B,EAAA,CAAA+B,UAAA,SAAAmG,GAAA,CAAApF,YAAA,GAAAoF,GAAA,CAAArF,QAAA,CAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}