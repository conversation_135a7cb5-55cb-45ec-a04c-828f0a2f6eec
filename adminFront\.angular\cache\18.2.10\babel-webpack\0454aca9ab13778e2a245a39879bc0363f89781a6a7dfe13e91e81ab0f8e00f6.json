{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_nb_option_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r3, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", floor_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", floor_r6, \"F \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_span_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\\u5DF2\\u9078 \", ctx_r4.selectedHouses.length, \" \\u7B46) \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 48)(3, \"nb-form-field\");\n    i0.ɵɵelement(4, \"nb-icon\", 49);\n    i0.ɵɵelementStart(5, \"input\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.searchKeyword, $event) || (ctx_r4.filterOptions.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_input_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 51)(7, \"nb-select\", 52);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.statusFilter, $event) || (ctx_r4.filterOptions.statusFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_selectedChange_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(8, \"nb-option\", 20);\n    i0.ɵɵtext(9, \"\\u5168\\u90E8\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-option\", 53);\n    i0.ɵɵtext(11, \"\\u9032\\u884C\\u4E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-option\", 54);\n    i0.ɵɵtext(13, \"\\u5F85\\u958B\\u653E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-option\", 55);\n    i0.ɵɵtext(15, \"\\u5DF2\\u904E\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-option\", 56);\n    i0.ɵɵtext(17, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-option\", 57);\n    i0.ɵɵtext(19, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 51)(21, \"nb-select\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.floorFilter, $event) || (ctx_r4.filterOptions.floorFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_selectedChange_21_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(22, \"nb-option\", 20);\n    i0.ɵɵtext(23, \"\\u5168\\u90E8\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SettingTimePeriodComponent_div_57_nb_option_24_Template, 2, 2, \"nb-option\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 51)(26, \"nb-select\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.pageSize, $event) || (ctx_r4.pageSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_selectedChange_26_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onPageSizeChange());\n    });\n    i0.ɵɵelementStart(27, \"nb-option\", 45);\n    i0.ɵɵtext(28, \"50\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-option\", 45);\n    i0.ɵɵtext(30, \"100\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"nb-option\", 45);\n    i0.ɵɵtext(32, \"200\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-option\", 45);\n    i0.ɵɵtext(34, \"500\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 48)(36, \"div\", 60)(37, \"div\", 61);\n    i0.ɵɵtext(38);\n    i0.ɵɵtemplate(39, SettingTimePeriodComponent_div_57_span_39_Template, 2, 1, \"span\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 63)(41, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setViewMode(\"table\"));\n    });\n    i0.ɵɵelement(42, \"i\", 65);\n    i0.ɵɵtext(43, \" \\u8868\\u683C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setViewMode(\"card\"));\n    });\n    i0.ɵɵelement(45, \"i\", 67);\n    i0.ɵɵtext(46, \" \\u5361\\u7247 \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.statusFilter);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.floorFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.availableFloors);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 50);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 200);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 500);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r4.viewMode === \"table\")(\"btn-outline-primary\", ctx_r4.viewMode !== \"table\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r4.viewMode === \"card\")(\"btn-outline-primary\", ctx_r4.viewMode !== \"card\");\n  }\n}\nfunction SettingTimePeriodComponent_div_58_tr_40_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r9.CChangeStartDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_58_tr_40_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 96);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_58_tr_40_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r9.CChangeEndDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_58_tr_40_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 96);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_58_tr_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_tr_40_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const house_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r9.selected, $event) || (house_r9.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_tr_40_Template_nb_checkbox_ngModelChange_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtemplate(10, SettingTimePeriodComponent_div_58_tr_40_span_10_Template, 3, 4, \"span\", 91)(11, SettingTimePeriodComponent_div_58_tr_40_span_11_Template, 2, 0, \"span\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtemplate(13, SettingTimePeriodComponent_div_58_tr_40_span_13_Template, 3, 4, \"span\", 91)(14, SettingTimePeriodComponent_div_58_tr_40_span_14_Template, 2, 0, \"span\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\")(16, \"span\", 93);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_tr_40_Template_button_click_19_listener() {\n      const house_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const dialog_r10 = i0.ɵɵreference(67);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r10, house_r9));\n    });\n    i0.ɵɵelement(20, \"i\", 74);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const house_r9 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"table-row-selected\", house_r9.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r9.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r9.CHouseId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r9.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r9.CBuildingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", house_r9.CFloor, \"F\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", house_r9.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r9.CChangeStartDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", house_r9.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r9.CChangeEndDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r9));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getStatusText(house_r9), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !house_r9.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_58_div_41_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 99)(1, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_div_41_li_9_Template_button_click_1_listener() {\n      const page_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(page_r13));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r13 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r13 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r13);\n  }\n}\nfunction SettingTimePeriodComponent_div_58_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"nav\")(2, \"ul\", 98)(3, \"li\", 99)(4, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_div_41_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(1));\n    });\n    i0.ɵɵtext(5, \"\\u9996\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 99)(7, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_div_41_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage - 1));\n    });\n    i0.ɵɵtext(8, \"\\u4E0A\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, SettingTimePeriodComponent_div_58_div_41_li_9_Template, 3, 3, \"li\", 101);\n    i0.ɵɵelementStart(10, \"li\", 99)(11, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_div_41_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage + 1));\n    });\n    i0.ɵɵtext(12, \"\\u4E0B\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\", 99)(14, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_div_41_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.totalPages));\n    });\n    i0.ɵɵtext(15, \"\\u672B\\u9801\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"div\", 60)(3, \"div\", 71)(4, \"nb-checkbox\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_Template_nb_checkbox_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_Template_nb_checkbox_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵtext(5, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openBatchSetting());\n    });\n    i0.ɵɵelement(7, \"i\", 74);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.exportData());\n    });\n    i0.ɵɵelement(10, \"i\", 76);\n    i0.ɵɵtext(11, \" \\u532F\\u51FA \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 77);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 78)(15, \"table\", 79)(16, \"thead\", 80)(17, \"tr\")(18, \"th\", 81)(19, \"nb-checkbox\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_Template_nb_checkbox_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_Template_nb_checkbox_ngModelChange_19_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"th\", 82);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_th_click_20_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CHouseHold\"));\n    });\n    i0.ɵɵtext(21, \" \\u6236\\u578B \");\n    i0.ɵɵelement(22, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 82);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_th_click_23_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CBuildingName\"));\n    });\n    i0.ɵɵtext(24, \" \\u68DF\\u5225 \");\n    i0.ɵɵelement(25, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\", 84);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_th_click_26_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CFloor\"));\n    });\n    i0.ɵɵtext(27, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelement(28, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"th\", 85);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_th_click_29_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeStartDate\"));\n    });\n    i0.ɵɵtext(30, \" \\u958B\\u59CB\\u65E5\\u671F \");\n    i0.ɵɵelement(31, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"th\", 85);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_th_click_32_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeEndDate\"));\n    });\n    i0.ɵɵtext(33, \" \\u7D50\\u675F\\u65E5\\u671F \");\n    i0.ɵɵelement(34, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"th\", 86);\n    i0.ɵɵtext(36, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"th\", 87);\n    i0.ɵɵtext(38, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"tbody\");\n    i0.ɵɵtemplate(40, SettingTimePeriodComponent_div_58_tr_40_Template, 21, 15, \"tr\", 88);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(41, SettingTimePeriodComponent_div_58_div_41_Template, 16, 13, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u6279\\u6B21\\u8A2D\\u5B9A (\", ctx_r4.selectedHouses.length, \") \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.filteredHouses.length === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" / \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46 \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.paginatedHouses)(\"ngForTrackBy\", ctx_r4.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_2_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 116);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r15.CChangeStartDate, \"MM/dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_2_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 96);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_2_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 116);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r15.CChangeEndDate, \"MM/dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_2_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 96);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"nb-card\", 107)(2, \"nb-card-body\", 108)(3, \"div\", 109)(4, \"div\", 110)(5, \"h6\", 111);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\", 96);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"nb-checkbox\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_59_div_2_Template_nb_checkbox_ngModelChange_9_listener($event) {\n      const house_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r15.selected, $event) || (house_r15.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_59_div_2_Template_nb_checkbox_ngModelChange_9_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 112)(11, \"div\", 113)(12, \"small\", 96);\n    i0.ɵɵtext(13, \"\\u958B\\u59CB\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, SettingTimePeriodComponent_div_59_div_2_span_14_Template, 3, 4, \"span\", 114)(15, SettingTimePeriodComponent_div_59_div_2_span_15_Template, 2, 0, \"span\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 113)(17, \"small\", 96);\n    i0.ɵɵtext(18, \"\\u7D50\\u675F\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, SettingTimePeriodComponent_div_59_div_2_span_19_Template, 3, 4, \"span\", 114)(20, SettingTimePeriodComponent_div_59_div_2_span_20_Template, 2, 0, \"span\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 60)(22, \"span\", 115);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_2_Template_button_click_24_listener() {\n      const house_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const dialog_r10 = i0.ɵɵreference(67);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r10, house_r15));\n    });\n    i0.ɵɵelement(25, \"i\", 74);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const house_r15 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", house_r15.selected)(\"disabled\", !house_r15.CHouseId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(house_r15.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", house_r15.CBuildingName, \" - \", house_r15.CFloor, \"F\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r15.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r15.CHouseId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", house_r15.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r15.CChangeStartDate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", house_r15.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r15.CChangeEndDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r15));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getStatusText(house_r15), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !house_r15.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_3_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 99)(1, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_3_li_9_Template_button_click_1_listener() {\n      const page_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(page_r18));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r18 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r18 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r18);\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"nav\")(2, \"ul\", 98)(3, \"li\", 99)(4, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(1));\n    });\n    i0.ɵɵtext(5, \"\\u9996\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 99)(7, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_3_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage - 1));\n    });\n    i0.ɵɵtext(8, \"\\u4E0A\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, SettingTimePeriodComponent_div_59_div_3_li_9_Template, 3, 3, \"li\", 101);\n    i0.ɵɵelementStart(10, \"li\", 99)(11, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_3_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage + 1));\n    });\n    i0.ɵɵtext(12, \"\\u4E0B\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\", 99)(14, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_3_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.totalPages));\n    });\n    i0.ɵɵtext(15, \"\\u672B\\u9801\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 104);\n    i0.ɵɵtemplate(2, SettingTimePeriodComponent_div_59_div_2_Template, 26, 17, \"div\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_div_59_div_3_Template, 16, 13, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.paginatedHouses)(\"ngForTrackBy\", ctx_r4.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 96);\n    i0.ɵɵelement(4, \"i\", 118);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\\u5F8C\\u67E5\\u8A62\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 96)(4, \"div\", 119)(5, \"span\", 120);\n    i0.ɵɵtext(6, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 121);\n    i0.ɵɵtext(8, \"\\u8CC7\\u6599\\u8F09\\u5165\\u4E2D\\uFF0C\\u8ACB\\u7A0D\\u5019...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \\u5DF2\\u9078\\u64C7 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225 \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r4.selectedBuildingForBatch.name, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 138);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", house_r20.CHouseHold, \" (\", house_r20.CBuildingName, \"-\", house_r20.CFloor, \"F) \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_28_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 96);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" ...\\u7B49 \", ctx_r4.selectedHouses.length - 10, \" \\u500B \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 136);\n    i0.ɵɵtemplate(4, SettingTimePeriodComponent_ng_template_62_div_28_span_4_Template, 2, 3, \"span\", 137)(5, SettingTimePeriodComponent_ng_template_62_div_28_span_5_Template, 2, 1, \"span\", 92);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5C07\\u5957\\u7528\\u5230\\u5DF2\\u9078\\u64C7\\u7684 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedHouses.slice(0, 10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 10);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_29_div_3_div_1_div_3_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      const house_r25 = i0.ɵɵrestoreView(_r24).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r25.selected, $event) || (house_r25.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r25 = ctx.$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r25.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r25.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r25.CHouseHold, \" (\", house_r25.CBuildingName, \") \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_29_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 143);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_62_div_29_div_3_div_1_div_3_nb_checkbox_1_Template, 2, 4, \"nb-checkbox\", 144);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", floor_r23.houses);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_29_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 141)(1, \"nb-checkbox\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const floor_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      i0.ɵɵtwoWayBindingSet(floor_r23.selected, $event) || (floor_r23.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      const floor_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.onFloorSelectionChange(floor_r23));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_62_div_29_div_3_div_1_div_3_Template, 2, 1, \"div\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r23 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", floor_r23.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r23.floorNumber, \"F (\", floor_r23.houses.length, \" \\u6236) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", floor_r23.selected);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_29_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_62_div_29_div_3_div_1_Template, 4, 4, \"div\", 140);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedBuildingForBatch.floors);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"nb-checkbox\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_div_29_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.applyToAll, $event) || (ctx_r4.batchSettings.applyToAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_62_div_29_div_3_Template, 2, 1, \"div\", 139);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.applyToAll);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u90E8\\u6236\\u5225 (\", ctx_r4.flattenedHouses.length, \" \\u500B) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.batchSettings.applyToAll && ctx_r4.selectedBuildingForBatch);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225)\");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 122)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_62_span_3_Template, 2, 1, \"span\", 62)(4, SettingTimePeriodComponent_ng_template_62_span_4_Template, 2, 1, \"span\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"div\", 124)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n    i0.ɵɵelementStart(9, \"span\", 125);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 126)(12, \"nb-form-field\", 127);\n    i0.ɵɵelement(13, \"nb-icon\", 24);\n    i0.ɵɵelementStart(14, \"input\", 128);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.startDate, $event) || (ctx_r4.batchSettings.startDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 26, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 129);\n    i0.ɵɵtext(18, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\", 127);\n    i0.ɵɵelement(20, \"nb-icon\", 24);\n    i0.ɵɵelementStart(21, \"input\", 128);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.endDate, $event) || (ctx_r4.batchSettings.endDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 26, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 124)(25, \"label\");\n    i0.ɵɵtext(26, \"\\u9069\\u7528\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 130);\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_ng_template_62_div_28_Template, 6, 3, \"div\", 131)(29, SettingTimePeriodComponent_ng_template_62_div_29_Template, 4, 3, \"div\", 123);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-card-footer\", 132)(31, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_62_Template_button_click_31_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r26));\n    });\n    i0.ɵɵtext(32, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_62_Template_button_click_33_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onBatchSubmit(ref_r26));\n    });\n    i0.ɵɵtext(34, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(35, SettingTimePeriodComponent_ng_template_62_span_35_Template, 2, 1, \"span\", 123);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const batchStartDate_r27 = i0.ɵɵreference(16);\n    const batchEndDate_r28 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedBuildingForBatch && ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", batchStartDate_r27);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.startDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", batchEndDate_r28);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.endDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 145);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 146);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 145)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 147);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 148)(7, \"div\", 21)(8, \"label\", 22);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 125);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 149);\n    i0.ɵɵelement(13, \"nb-icon\", 24);\n    i0.ɵɵelementStart(14, \"input\", 150);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_66_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 26, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 151);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 24);\n    i0.ɵɵelementStart(21, \"input\", 152);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_66_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 26, 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 146)(25, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_66_Template_button_click_25_listener() {\n      const ref_r30 = i0.ɵɵrestoreView(_r29).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r30));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 153);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_66_Template_button_click_27_listener() {\n      const ref_r30 = i0.ɵɵrestoreView(_r29).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSubmit(ref_r30));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r31 = i0.ɵɵreference(16);\n    const changeEndDate_r32 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r4.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r31);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r32);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport let SettingTimePeriodComponent = /*#__PURE__*/(() => {\n  class SettingTimePeriodComponent extends BaseComponent {\n    constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._houseService = _houseService;\n      this._buildCaseService = _buildCaseService;\n      this.router = router;\n      this._eventService = _eventService;\n      this.tempBuildCaseId = -1;\n      this.buildCaseOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      this.isStatus = true;\n      // 新增的屬性\n      this.buildingGroups = [];\n      this.buildingOptions = [];\n      this.selectedBuilding = '';\n      this.availableFloors = [];\n      // 篩選和搜尋\n      this.filterOptions = {\n        searchKeyword: '',\n        statusFilter: '',\n        floorFilter: '',\n        buildingFilter: ''\n      };\n      // 批次設定\n      this.batchSettings = {\n        startDate: null,\n        endDate: null,\n        applyToAll: true,\n        selectedBuildings: [],\n        selectedFloors: [],\n        selectedHouses: []\n      };\n      this.selectedBuildingForBatch = null;\n      // 新增：表格視圖相關屬性\n      this.viewMode = 'table';\n      this.flattenedHouses = [];\n      this.filteredHouses = [];\n      this.paginatedHouses = [];\n      this.selectedHouses = [];\n      this.selectAll = false;\n      this.loading = false;\n      // 分頁相關\n      this.currentPage = 1;\n      this.pageSize = 100;\n      this.totalPages = 1;\n      // 排序相關\n      this.sortField = '';\n      this.sortDirection = 'asc';\n      // 數學函數引用\n      this.Math = Math;\n      this.selectedHouseChangeDate = {\n        CChangeStartDate: '',\n        CChangeEndDate: '',\n        CFloor: undefined,\n        CHouseHold: '',\n        CHouseId: undefined\n      };\n      this._eventService.receive().pipe(tap(res => {\n        if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n          this.tempBuildCaseId = res.payload;\n        }\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        CBuildingNameSelected: this.buildCaseOptions[0],\n        CChangeStartDate: undefined,\n        CChangeEndDate: undefined\n      };\n      this.getUserBuildCase();\n    }\n    openModel(ref, item) {\n      if (item.CHouseId) {\n        this.selectedHouseChangeDate = {\n          ...item,\n          CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n          CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n        };\n        this.dialogService.open(ref);\n      }\n    }\n    formatDate(CChangeDate) {\n      if (CChangeDate) {\n        return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n      }\n      return '';\n    }\n    onSubmit(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      const param = {\n        CHouseId: this.selectedHouseChangeDate.CHouseId,\n        CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n        CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n      };\n      this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n        body: [param]\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getHouseChangeDate();\n          ref.close();\n        }\n      });\n    }\n    getUserBuildCase() {\n      this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n        body: {}\n      }).pipe(tap(res => {\n        const entries = res.Entries ?? []; // entries not undefined and not null\n        if (entries.length && res.StatusCode === 0) {\n          this.userBuildCaseOptions = entries.map(entry => ({\n            CBuildCaseName: entry.CBuildCaseName,\n            cID: entry.cID\n          }));\n          if (this.tempBuildCaseId != -1) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n          }\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n          if (selectedCID) {\n            this.getHouseChangeDate();\n          }\n        }\n      })).subscribe();\n    }\n    convertHouseholdArrayOptimized(arr) {\n      const floorDict = {}; // Initialize dictionary to group elements by CFloor\n      arr.forEach(household => {\n        household.CHouses.forEach(house => {\n          const floor = house.CFloor;\n          if (!floorDict[floor]) {\n            // If CFloor is not in the dictionary, initialize an empty list\n            floorDict[floor] = [];\n          }\n          floorDict[floor].push({\n            CHouseHold: household.CHouseHold,\n            CBuildingName: house.CBuildingName || '未分類',\n            CHouseId: house.CHouseId,\n            CFloor: house.CFloor,\n            CChangeStartDate: house.CChangeStartDate,\n            CChangeEndDate: house.CChangeEndDate\n          });\n        });\n      });\n      // Arrange floors in ascending order\n      this.floors.sort((a, b) => b - a);\n      const result = this.floors.map(floor => {\n        return this.households.map(household => {\n          const house = floorDict[floor].find(h => h.CHouseHold === household);\n          return house || {\n            CHouseHold: household,\n            CBuildingName: '未分類',\n            CHouseId: null,\n            CFloor: floor,\n            CChangeStartDate: null,\n            CChangeEndDate: null\n          };\n        });\n      });\n      return result;\n    }\n    getFloorsAndHouseholds(arr) {\n      const floorsSet = new Set();\n      const householdsSet = new Set();\n      arr.forEach(household => {\n        householdsSet.add(household.CHouseHold);\n        household.CHouses.forEach(house => {\n          floorsSet.add(house.CFloor);\n        });\n      });\n      this.floors = Array.from(floorsSet);\n      this.households = Array.from(householdsSet);\n      return {\n        floors: Array.from(floorsSet),\n        households: Array.from(householdsSet)\n      };\n    }\n    validationDate() {\n      if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n        const startDate = new Date(this.searchQuery.CChangeStartDate);\n        const endDate = new Date(this.searchQuery.CChangeEndDate);\n        if (startDate && endDate && startDate > endDate) {\n          this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n        }\n      }\n    }\n    getHouseChangeDate() {\n      this.loading = true;\n      this.validationDate();\n      this._houseService.apiHouseGetHouseChangeDatePost$Json({\n        body: {\n          CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n          CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n          CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n        }\n      }).subscribe(res => {\n        this.loading = false;\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseChangeDates = res.Entries ? res.Entries : [];\n          if (res.Entries) {\n            this.houseChangeDates = [...res.Entries];\n            this.getFloorsAndHouseholds(res.Entries);\n            this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n            // 新增：建立棟別分組資料\n            this.buildBuildingGroups(res.Entries);\n            // 新增：建立扁平化資料\n            this.buildFlattenedHouses(res.Entries);\n          }\n        }\n      });\n    }\n    // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n    buildBuildingGroups(data) {\n      const buildingMap = new Map();\n      data.forEach(household => {\n        const houseType = household.CHouseHold || ''; // 戶型\n        household.CHouses?.forEach(house => {\n          const buildingName = house.CBuildingName || '未分類'; // 棟別\n          const floor = house.CFloor || 0;\n          if (!buildingMap.has(buildingName)) {\n            buildingMap.set(buildingName, new Map());\n          }\n          const floorMap = buildingMap.get(buildingName);\n          if (!floorMap.has(floor)) {\n            floorMap.set(floor, []);\n          }\n          floorMap.get(floor).push({\n            CHouseHold: houseType,\n            // 戶型\n            CBuildingName: buildingName,\n            // 棟別\n            CHouseId: house.CHouseId || 0,\n            CFloor: floor,\n            CChangeStartDate: house.CChangeStartDate || '',\n            CChangeEndDate: house.CChangeEndDate || '',\n            selected: false\n          });\n        });\n      });\n      // 轉換為BuildingGroup格式\n      this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n        const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n        .map(([floorNumber, houses]) => ({\n          floorNumber,\n          houses: houses.sort((a, b) => {\n            // 排序邏輯：先按戶型排序，再按樓層排序\n            if (a.CHouseHold !== b.CHouseHold) {\n              return a.CHouseHold.localeCompare(b.CHouseHold);\n            }\n            return a.CFloor - b.CFloor;\n          }),\n          selected: false\n        }));\n        return {\n          name: buildingName,\n          floors,\n          selected: false\n        };\n      }).sort((a, b) => a.name.localeCompare(b.name));\n      // 更新棟別選項和可用樓層\n      this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n      this.updateAvailableFloors();\n    }\n    // 新增：更新可用樓層\n    updateAvailableFloors() {\n      const floorsSet = new Set();\n      this.buildingGroups.forEach(building => {\n        if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n          building.floors.forEach(floor => {\n            floorsSet.add(floor.floorNumber);\n          });\n        }\n      });\n      this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n    }\n    // 新增：棟別選擇變更處理\n    onBuildingChange() {\n      this.updateAvailableFloors();\n      this.filterOptions.buildingFilter = this.selectedBuilding;\n    }\n    // 新增：取得過濾後的棟別資料\n    getFilteredBuildings() {\n      return this.buildingGroups.filter(building => {\n        // 棟別篩選\n        if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n          return false;\n        }\n        // 關鍵字搜尋 (搜尋戶型)\n        if (this.filterOptions.searchKeyword) {\n          const keyword = this.filterOptions.searchKeyword.toLowerCase();\n          const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n          if (!hasMatchingHouse) {\n            return false;\n          }\n        }\n        // 狀態篩選\n        if (this.filterOptions.statusFilter) {\n          const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n          if (!hasMatchingStatus) {\n            return false;\n          }\n        }\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n          if (!hasMatchingFloor) {\n            return false;\n          }\n        }\n        return true;\n      }).map(building => {\n        // 對每個棟別，也要篩選其樓層和戶別\n        const filteredBuilding = {\n          ...building\n        };\n        filteredBuilding.floors = building.floors.filter(floor => {\n          // 樓層篩選\n          if (this.filterOptions.floorFilter) {\n            const floorNumber = parseInt(this.filterOptions.floorFilter);\n            if (floor.floorNumber !== floorNumber) {\n              return false;\n            }\n          }\n          // 檢查該樓層是否有符合條件的戶別\n          const hasValidHouses = floor.houses.some(house => {\n            // 關鍵字篩選 (搜尋戶型或棟別)\n            if (this.filterOptions.searchKeyword) {\n              const keyword = this.filterOptions.searchKeyword.toLowerCase();\n              if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n                return false;\n              }\n            }\n            // 狀態篩選\n            if (this.filterOptions.statusFilter) {\n              if (!this.matchesStatusFilter(house)) {\n                return false;\n              }\n            }\n            return true;\n          });\n          return hasValidHouses;\n        }).map(floor => {\n          // 篩選戶別\n          const filteredFloor = {\n            ...floor\n          };\n          filteredFloor.houses = floor.houses.filter(house => {\n            // 關鍵字篩選 (搜尋戶型或棟別)\n            if (this.filterOptions.searchKeyword) {\n              const keyword = this.filterOptions.searchKeyword.toLowerCase();\n              if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n                return false;\n              }\n            }\n            // 狀態篩選\n            if (this.filterOptions.statusFilter) {\n              if (!this.matchesStatusFilter(house)) {\n                return false;\n              }\n            }\n            return true;\n          });\n          return filteredFloor;\n        });\n        return filteredBuilding;\n      });\n    }\n    // 新增：檢查戶別是否符合狀態篩選\n    matchesStatusFilter(house) {\n      const status = this.getHouseStatus(house);\n      switch (this.filterOptions.statusFilter) {\n        case 'active':\n          return status === 'active';\n        case 'inactive':\n          return status === 'not-set';\n        case 'disabled':\n          return status === 'disabled';\n        default:\n          return true;\n      }\n    }\n    // 新增：取得戶別狀態\n    getHouseStatus(house) {\n      if (!house.CHouseId) {\n        return 'disabled';\n      }\n      if (house.CChangeStartDate && house.CChangeEndDate) {\n        const now = new Date();\n        const startDate = new Date(house.CChangeStartDate);\n        const endDate = new Date(house.CChangeEndDate);\n        if (now < startDate) {\n          return 'pending';\n        } else if (now >= startDate && now <= endDate) {\n          return 'active';\n        } else {\n          return 'expired';\n        }\n      }\n      return 'not-set';\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n      this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n      this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n    }\n    onNavigateWithId() {\n      this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n    }\n    // 修改：開啟批次設定對話框\n    openBatchSetting(building) {\n      this.selectedBuildingForBatch = building || null;\n      // 如果有選中的房屋，使用選中的房屋；否則使用全部\n      const hasSelectedHouses = this.selectedHouses.length > 0;\n      this.batchSettings = {\n        startDate: null,\n        endDate: null,\n        applyToAll: !hasSelectedHouses && !building,\n        selectedBuildings: building ? [building.name] : [],\n        selectedFloors: [],\n        selectedHouses: []\n      };\n      // 重置選擇狀態\n      if (building) {\n        building.floors.forEach(floor => {\n          floor.selected = false;\n          floor.houses.forEach(house => house.selected = false);\n        });\n      }\n      // 開啟對話框\n      this.dialogService.open(this.batchSettingDialog);\n    }\n    // 新增：樓層選擇變更處理\n    onFloorSelectionChange(floor) {\n      if (floor.selected) {\n        floor.houses.forEach(house => {\n          if (house.CHouseId) {\n            house.selected = true;\n          }\n        });\n      } else {\n        floor.houses.forEach(house => house.selected = false);\n      }\n    }\n    // 修改：批次提交\n    onBatchSubmit(ref) {\n      // 驗證批次設定\n      this.valid.clear();\n      this.valid.required('[開始日期]', this.batchSettings.startDate);\n      this.valid.required('[結束日期]', this.batchSettings.endDate);\n      this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      // 收集要更新的房屋\n      const housesToUpdate = [];\n      if (this.batchSettings.applyToAll) {\n        // 全部戶別\n        this.flattenedHouses.forEach(house => {\n          if (house.CHouseId) {\n            housesToUpdate.push({\n              CHouseId: house.CHouseId,\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n            });\n          }\n        });\n      } else {\n        // 使用已選擇的戶別\n        if (this.selectedHouses.length > 0) {\n          this.selectedHouses.forEach(house => {\n            if (house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        } else if (this.selectedBuildingForBatch) {\n          // 如果沒有選擇的戶別，使用舊的邏輯\n          this.selectedBuildingForBatch.floors.forEach(floor => {\n            floor.houses.forEach(house => {\n              if (house.selected && house.CHouseId) {\n                housesToUpdate.push({\n                  CHouseId: house.CHouseId,\n                  CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                  CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n                });\n              }\n            });\n          });\n        }\n      }\n      if (housesToUpdate.length === 0) {\n        this.message.showErrorMSGs(['請選擇要設定的戶別']);\n        return;\n      }\n      // 調用API進行批次更新\n      this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n        body: housesToUpdate\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n          // 清除選擇狀態\n          this.selectedHouses.forEach(house => house.selected = false);\n          this.selectedHouses = [];\n          this.selectAll = false;\n          this.getHouseChangeDate();\n          ref.close();\n        }\n      });\n    }\n    // 新增：取得狀態樣式類別\n    getStatusClass(house) {\n      const status = this.getHouseStatus(house);\n      return `status-${status}`;\n    }\n    // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n    openHouseDialog(house) {\n      if (house.CHouseId) {\n        // 使用現有的openModel方法\n        this.openModel(this.dialog, house);\n      }\n    }\n    // 新增：建立扁平化房屋資料\n    buildFlattenedHouses(data) {\n      this.flattenedHouses = [];\n      data.forEach(household => {\n        const houseType = household.CHouseHold || '';\n        household.CHouses?.forEach(house => {\n          this.flattenedHouses.push({\n            CHouseHold: houseType,\n            CBuildingName: house.CBuildingName || '未分類',\n            CHouseId: house.CHouseId || 0,\n            CFloor: house.CFloor || 0,\n            CChangeStartDate: house.CChangeStartDate || '',\n            CChangeEndDate: house.CChangeEndDate || '',\n            selected: false\n          });\n        });\n      });\n      // 初始化篩選和分頁\n      this.onSearch();\n    }\n    // 新增：視圖模式切換\n    setViewMode(mode) {\n      this.viewMode = mode;\n    }\n    // 新增：搜尋和篩選\n    onSearch() {\n      this.filteredHouses = this.flattenedHouses.filter(house => {\n        // 關鍵字搜尋\n        if (this.filterOptions.searchKeyword) {\n          const keyword = this.filterOptions.searchKeyword.toLowerCase();\n          if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n            return false;\n          }\n        }\n        // 棟別篩選\n        if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n          return false;\n        }\n        // 狀態篩選\n        if (this.filterOptions.statusFilter) {\n          if (!this.matchesStatusFilter(house)) {\n            return false;\n          }\n        }\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (house.CFloor !== floorNumber) {\n            return false;\n          }\n        }\n        return true;\n      });\n      // 重新計算分頁\n      this.currentPage = 1;\n      this.updatePagination();\n    }\n    // 新增：更新分頁\n    updatePagination() {\n      this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n      const startIndex = (this.currentPage - 1) * this.pageSize;\n      const endIndex = startIndex + this.pageSize;\n      this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n    }\n    // 新增：頁面大小變更\n    onPageSizeChange() {\n      this.currentPage = 1;\n      this.updatePagination();\n    }\n    // 新增：跳轉頁面\n    goToPage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n        this.updatePagination();\n      }\n    }\n    // 新增：取得可見頁碼\n    getVisiblePages() {\n      const pages = [];\n      const maxVisible = 5;\n      let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n      let end = Math.min(this.totalPages, start + maxVisible - 1);\n      if (end - start + 1 < maxVisible) {\n        start = Math.max(1, end - maxVisible + 1);\n      }\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n    // 新增：全選/取消全選\n    onSelectAllChange() {\n      this.paginatedHouses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = this.selectAll;\n        }\n      });\n      this.updateSelectedHouses();\n    }\n    // 新增：單一選擇變更\n    onHouseSelectionChange() {\n      this.updateSelectedHouses();\n      this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n    }\n    // 新增：更新已選擇房屋列表\n    updateSelectedHouses() {\n      this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n    }\n    // 新增：排序\n    sort(field) {\n      if (this.sortField === field) {\n        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n      } else {\n        this.sortField = field;\n        this.sortDirection = 'asc';\n      }\n      this.filteredHouses.sort((a, b) => {\n        let aValue = a[field];\n        let bValue = b[field];\n        // 處理日期排序\n        if (field.includes('Date')) {\n          aValue = aValue ? new Date(aValue).getTime() : 0;\n          bValue = bValue ? new Date(bValue).getTime() : 0;\n        }\n        // 處理數字排序\n        if (field === 'CFloor') {\n          aValue = Number(aValue) || 0;\n          bValue = Number(bValue) || 0;\n        }\n        // 處理字串排序\n        if (typeof aValue === 'string') {\n          aValue = aValue.toLowerCase();\n          bValue = bValue.toLowerCase();\n        }\n        if (aValue < bValue) {\n          return this.sortDirection === 'asc' ? -1 : 1;\n        }\n        if (aValue > bValue) {\n          return this.sortDirection === 'asc' ? 1 : -1;\n        }\n        return 0;\n      });\n      this.updatePagination();\n    }\n    // 新增：TrackBy函數\n    trackByHouseId(_index, house) {\n      return house.CHouseId;\n    }\n    // 新增：取得狀態文字\n    getStatusText(house) {\n      const status = this.getHouseStatus(house);\n      switch (status) {\n        case 'active':\n          return '進行中';\n        case 'pending':\n          return '待開放';\n        case 'expired':\n          return '已過期';\n        case 'not-set':\n          return '未設定';\n        case 'disabled':\n          return '已停用';\n        default:\n          return '未知';\n      }\n    }\n    // 新增：匯出資料\n    exportData() {\n      // 實現匯出功能\n      const csvContent = this.generateCSV();\n      const blob = new Blob([csvContent], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n    // 新增：產生CSV內容\n    generateCSV() {\n      const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\n      const rows = this.filteredHouses.map(house => [house.CHouseHold, house.CBuildingName, `${house.CFloor}F`, house.CChangeStartDate || '未設定', house.CChangeEndDate || '未設定', this.getStatusText(house)]);\n      const csvContent = [headers, ...rows].map(row => row.map(cell => `\"${cell}\"`).join(',')).join('\\n');\n      return '\\uFEFF' + csvContent; // 加入BOM以支援中文\n    }\n    static {\n      this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SettingTimePeriodComponent,\n        selectors: [[\"ngx-setting-time-period\"]],\n        viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 68,\n        vars: 17,\n        consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"batchSettingDialog\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"batchStartDate\", \"\"], [\"batchEndDate\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"query-section\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"buildingSelect\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u68DF\\u5225\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-1\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [1, \"mx-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-md-7\", \"max-sm:col-md-12\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [1, \"form-check\", \"mx-2\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault1\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault1\", 1, \"form-check-label\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault2\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault2\", 1, \"form-check-label\"], [1, \"col-md-5\", \"max-sm:col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [\"class\", \"search-enhanced mt-3\", 4, \"ngIf\"], [\"class\", \"table-view mt-4\", 4, \"ngIf\"], [\"class\", \"card-view mt-4\", 4, \"ngIf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [3, \"value\"], [1, \"search-enhanced\", \"mt-3\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-3\"], [\"nbPrefix\", \"\", \"icon\", \"search-outline\"], [\"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u578B\\u3001\\u68DF\\u5225...\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-2\"], [\"placeholder\", \"\\u72C0\\u614B\\u7BE9\\u9078\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"active\"], [\"value\", \"pending\"], [\"value\", \"expired\"], [\"value\", \"not-set\"], [\"value\", \"disabled\"], [\"placeholder\", \"\\u6A13\\u5C64\\u7BE9\\u9078\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"placeholder\", \"\\u986F\\u793A\\u7B46\\u6578\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-muted\", \"small\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [1, \"view-toggle\"], [1, \"btn\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-table\"], [1, \"btn\", \"btn-sm\", \"ml-1\", 3, \"click\"], [1, \"fas\", \"fa-th\"], [1, \"text-primary\"], [1, \"table-view\", \"mt-4\"], [1, \"table-toolbar\", \"mb-3\"], [1, \"batch-actions\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-sm\", \"btn-success\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\"], [1, \"pagination-info\"], [1, \"table-container\"], [1, \"table\", \"table-hover\"], [1, \"table-header\"], [\"width\", \"50\"], [\"width\", \"100\", 1, \"sortable\", 3, \"click\"], [1, \"fas\", \"fa-sort\"], [\"width\", \"80\", 1, \"sortable\", 3, \"click\"], [\"width\", \"120\", 1, \"sortable\", 3, \"click\"], [\"width\", \"100\"], [\"width\", \"80\"], [3, \"table-row-selected\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"pagination-container mt-3\", 4, \"ngIf\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"date-display\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"status-badge\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\", \"disabled\"], [1, \"date-display\"], [1, \"text-muted\"], [1, \"pagination-container\", \"mt-3\"], [1, \"pagination\", \"justify-content-center\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"page-link\", 3, \"click\"], [1, \"card-view\", \"mt-4\"], [1, \"row\"], [\"class\", \"col-lg-3 col-md-4 col-sm-6 mb-3\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"col-lg-3\", \"col-md-4\", \"col-sm-6\", \"mb-3\"], [1, \"house-card\"], [1, \"p-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"mb-2\"], [1, \"house-info\"], [1, \"house-number\", \"mb-1\"], [1, \"date-info\", \"mb-2\"], [1, \"date-row\"], [\"class\", \"date-value\", 4, \"ngIf\"], [1, \"status-badge\", \"small\"], [1, \"date-value\"], [1, \"text-center\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"mb-3\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"mt-2\"], [2, \"width\", \"600px\", \"max-height\", \"90vh\"], [4, \"ngIf\"], [1, \"form-group\"], [1, \"text-red-600\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-fill\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"mx-2\"], [1, \"selection-options\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"alert\", \"alert-info\"], [1, \"selected-houses-preview\"], [\"class\", \"badge badge-primary mr-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"mr-1\", \"mb-1\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"floor-selection\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-selection\"], [\"class\", \"house-selection ml-4\", 4, \"ngIf\"], [1, \"house-selection\", \"ml-4\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"ml-3\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n        template: function SettingTimePeriodComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 9)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 10);\n            i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 11)(7, \"div\", 12)(8, \"div\", 13)(9, \"div\", 14)(10, \"label\", 15);\n            i0.ɵɵtext(11, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"nb-select\", 16);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_12_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_12_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getHouseChangeDate());\n            });\n            i0.ɵɵtemplate(13, SettingTimePeriodComponent_nb_option_13_Template, 2, 2, \"nb-option\", 17);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(14, \"div\", 13)(15, \"div\", 14)(16, \"label\", 18);\n            i0.ɵɵtext(17, \"\\u68DF\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"nb-select\", 19);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_18_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_18_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onBuildingChange());\n            });\n            i0.ɵɵelementStart(19, \"nb-option\", 20);\n            i0.ɵɵtext(20, \"\\u5168\\u90E8\\u68DF\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(21, SettingTimePeriodComponent_nb_option_21_Template, 2, 2, \"nb-option\", 17);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(22, \"div\", 13)(23, \"div\", 21)(24, \"label\", 22);\n            i0.ɵɵtext(25, \"\\u958B\\u653E\\u65E5\\u671F \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"nb-form-field\", 23);\n            i0.ɵɵelement(27, \"nb-icon\", 24);\n            i0.ɵɵelementStart(28, \"input\", 25);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_28_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(29, \"nb-datepicker\", 26, 0);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"span\", 27);\n            i0.ɵɵtext(32, \"~\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"nb-form-field\");\n            i0.ɵɵelement(34, \"nb-icon\", 24);\n            i0.ɵɵelementStart(35, \"input\", 28);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_35_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(36, \"nb-datepicker\", 26, 1);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(38, \"div\", 29)(39, \"div\", 14)(40, \"label\", 30);\n            i0.ɵɵtext(41, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"div\", 31)(43, \"input\", 32);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_43_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"label\", 33);\n            i0.ɵɵtext(45, \" \\u4F9D\\u958B\\u653E\\u6642\\u6BB5\\u986F\\u793A \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"div\", 31)(47, \"input\", 34);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_47_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"label\", 35);\n            i0.ɵɵtext(49, \" \\u4F9D\\u958B\\u653E\\u72C0\\u614B\\u986F\\u793A \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(50, \"div\", 36)(51, \"div\", 37)(52, \"button\", 38);\n            i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_52_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getHouseChangeDate());\n            });\n            i0.ɵɵtext(53, \" \\u67E5\\u8A62 \");\n            i0.ɵɵelement(54, \"i\", 39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"button\", 40);\n            i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_55_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.openBatchSetting());\n            });\n            i0.ɵɵtext(56, \" \\u5168\\u90E8\\u6279\\u6B21\\u8A2D\\u5B9A \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(57, SettingTimePeriodComponent_div_57_Template, 47, 19, \"div\", 41)(58, SettingTimePeriodComponent_div_58_Template, 42, 31, \"div\", 42)(59, SettingTimePeriodComponent_div_59_Template, 4, 3, \"div\", 43)(60, SettingTimePeriodComponent_div_60_Template, 7, 0, \"div\", 44)(61, SettingTimePeriodComponent_div_61_Template, 9, 0, \"div\", 44);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(62, SettingTimePeriodComponent_ng_template_62_Template, 36, 9, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(64, SettingTimePeriodComponent_ng_template_64_Template, 4, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(66, SettingTimePeriodComponent_ng_template_66_Template, 29, 6, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            const StartDate_r33 = i0.ɵɵreference(30);\n            const EndDate_r34 = i0.ɵɵreference(37);\n            i0.ɵɵadvance(12);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.buildingOptions);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"nbDatepicker\", StartDate_r33);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"nbDatepicker\", EndDate_r34);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"value\", true);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"value\", false);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"table\" && ctx.flattenedHouses.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"card\" && ctx.flattenedHouses.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length === 0 && ctx.houseChangeDates.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          }\n        },\n        dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.RadioControlValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, RadioButtonModule, NbDatepickerModule, NbDateFnsDateModule],\n        styles: [\".query-section[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:1rem;border-radius:.375rem;border:1px solid #dee2e6;margin-bottom:1rem}.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:1rem;border-radius:.375rem;border:1px solid #dee2e6}.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]{border:1px solid #dee2e6;border-radius:.375rem;overflow:hidden}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]{margin-bottom:0}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]{background-color:#f8f9fa}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-top:none;font-weight:600;color:#495057;padding:.75rem}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover{background-color:#e9ecef}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-left:.5rem;opacity:.5}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{opacity:1}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-selected[_ngcontent-%COMP%]{background-color:#e3f2fd}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:.75rem;vertical-align:middle}.table-view[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{padding:.25rem .5rem;border-radius:.25rem;font-size:.75rem;font-weight:500}.table-view[_ngcontent-%COMP%]   .status-badge.status-active[_ngcontent-%COMP%]{background-color:#d4edda;color:#155724}.table-view[_ngcontent-%COMP%]   .status-badge.status-pending[_ngcontent-%COMP%]{background-color:#fff3cd;color:#856404}.table-view[_ngcontent-%COMP%]   .status-badge.status-expired[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24}.table-view[_ngcontent-%COMP%]   .status-badge.status-not-set[_ngcontent-%COMP%]{background-color:#e2e3e5;color:#383d41}.table-view[_ngcontent-%COMP%]   .status-badge.status-disabled[_ngcontent-%COMP%]{background-color:#f8f9fa;color:#6c757d}.table-view[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%]{font-family:monospace;font-size:.875rem}.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]{transition:all .2s ease;cursor:pointer;height:100%}.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]:hover:not(.disabled){transform:translateY(-2px);box-shadow:0 4px 8px #0000001a}.card-view[_ngcontent-%COMP%]   .house-card.selected[_ngcontent-%COMP%]{border-color:#007bff;box-shadow:0 0 0 2px #007bff40}.card-view[_ngcontent-%COMP%]   .house-card.disabled[_ngcontent-%COMP%]{opacity:.6;cursor:not-allowed}.card-view[_ngcontent-%COMP%]   .house-card.disabled[_ngcontent-%COMP%]:hover{transform:none;box-shadow:none}.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .house-number[_ngcontent-%COMP%]{font-weight:600;color:#495057}.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.25rem}.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-row[_ngcontent-%COMP%]   .date-value[_ngcontent-%COMP%]{font-family:monospace;font-size:.875rem;color:#495057}.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{padding:.25rem .5rem;border-radius:.25rem;font-size:.75rem;font-weight:500}.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-active[_ngcontent-%COMP%]{background-color:#d4edda;color:#155724}.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-pending[_ngcontent-%COMP%]{background-color:#fff3cd;color:#856404}.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-expired[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24}.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-not-set[_ngcontent-%COMP%]{background-color:#e2e3e5;color:#383d41}.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-disabled[_ngcontent-%COMP%]{background-color:#f8f9fa;color:#6c757d}.search-enhanced[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:1rem;border-radius:.375rem;border:1px solid #dee2e6}.search-enhanced[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], .search-enhanced[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], .search-enhanced[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]{margin-bottom:0}.search-enhanced[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%]{display:flex;gap:.25rem}.search-enhanced[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:.25rem;font-size:.875rem;padding:.375rem .75rem}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{color:#495057;border-color:#dee2e6}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover{background-color:#e9ecef;border-color:#adb5bd}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{background-color:#007bff;border-color:#007bff}.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{color:#6c757d;background-color:#fff;border-color:#dee2e6}.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]{margin-bottom:.5rem;padding-left:1rem}.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(150px,1fr));gap:.25rem;margin-top:.5rem;padding:.5rem;background-color:#f8f9fa;border-radius:.25rem}.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]{margin-bottom:.25rem}.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%]{max-height:200px;overflow-y:auto}.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem}.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{padding:.75rem;margin-bottom:1rem;border:1px solid transparent;border-radius:.375rem}.selection-options[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%]{color:#0c5460;background-color:#d1ecf1;border-color:#bee5eb}.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin-bottom:.5rem;font-weight:600}@media (max-width: 992px){.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]{overflow-x:auto}.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%], .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%]{justify-content:center}.card-view[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%]{flex:0 0 50%;max-width:50%}}@media (max-width: 768px){.search-enhanced[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{margin-bottom:.5rem}.search-enhanced[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%]{justify-content:center;margin-top:.5rem}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]{font-size:.875rem}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:.5rem .25rem}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3), .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(3){display:none}.card-view[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], .card-view[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%]{flex:0 0 100%;max-width:100%}}@media (max-width: 576px){.query-section[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%]{margin-bottom:1rem}.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4), .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(4){display:none}}nb-card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#495057;font-weight:600}nb-card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]{font-size:.8rem;padding:.25rem .5rem}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:500;color:#495057;margin-bottom:.5rem}.status-indicator[_ngcontent-%COMP%]{display:inline-block;width:8px;height:8px;border-radius:50%;margin-right:.5rem}.status-indicator.active[_ngcontent-%COMP%]{background-color:#28a745}.status-indicator.pending[_ngcontent-%COMP%]{background-color:#ffc107}.status-indicator.expired[_ngcontent-%COMP%]{background-color:#dc3545}.status-indicator.not-set[_ngcontent-%COMP%]{background-color:#6c757d}.status-indicator.disabled[_ngcontent-%COMP%]{background-color:#e9ecef}\"]\n      });\n    }\n  }\n  return SettingTimePeriodComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}