{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Telugu [te]\n//! author : <PERSON>hota : https://github.com/kcthota\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var te = moment.defineLocale('te', {\n    months: 'జనవరి_ఫిబ్రవరి_మార్చి_ఏప్రిల్_మే_జూన్_జులై_ఆగస్టు_సెప్టెంబర్_అక్టోబర్_నవంబర్_డిసెంబర్'.split('_'),\n    monthsShort: 'జన._ఫిబ్ర._మార్చి_ఏప్రి._మే_జూన్_జులై_ఆగ._సెప్._అక్టో._నవ._డిసె.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'ఆదివారం_సోమవారం_మంగళవారం_బుధవారం_గురువారం_శుక్రవారం_శనివారం'.split('_'),\n    weekdaysShort: 'ఆది_సోమ_మంగళ_బుధ_గురు_శుక్ర_శని'.split('_'),\n    weekdaysMin: 'ఆ_సో_మం_బు_గు_శు_శ'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm',\n      LTS: 'A h:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm'\n    },\n    calendar: {\n      sameDay: '[నేడు] LT',\n      nextDay: '[రేపు] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[నిన్న] LT',\n      lastWeek: '[గత] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s లో',\n      past: '%s క్రితం',\n      s: 'కొన్ని క్షణాలు',\n      ss: '%d సెకన్లు',\n      m: 'ఒక నిమిషం',\n      mm: '%d నిమిషాలు',\n      h: 'ఒక గంట',\n      hh: '%d గంటలు',\n      d: 'ఒక రోజు',\n      dd: '%d రోజులు',\n      M: 'ఒక నెల',\n      MM: '%d నెలలు',\n      y: 'ఒక సంవత్సరం',\n      yy: '%d సంవత్సరాలు'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}వ/,\n    ordinal: '%dవ',\n    meridiemParse: /రాత్రి|ఉదయం|మధ్యాహ్నం|సాయంత్రం/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'రాత్రి') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'ఉదయం') {\n        return hour;\n      } else if (meridiem === 'మధ్యాహ్నం') {\n        return hour >= 10 ? hour : hour + 12;\n      } else if (meridiem === 'సాయంత్రం') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'రాత్రి';\n      } else if (hour < 10) {\n        return 'ఉదయం';\n      } else if (hour < 17) {\n        return 'మధ్యాహ్నం';\n      } else if (hour < 20) {\n        return 'సాయంత్రం';\n      } else {\n        return 'రాత్రి';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return te;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}