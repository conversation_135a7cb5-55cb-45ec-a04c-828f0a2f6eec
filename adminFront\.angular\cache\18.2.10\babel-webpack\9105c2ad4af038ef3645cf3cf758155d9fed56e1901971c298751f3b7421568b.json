{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { forceLayout } from './forceHelper.js';\nimport { simpleLayout } from './simpleLayoutHelper.js';\nimport { circularLayout } from './circularLayoutHelper.js';\nimport { linearMap } from '../../util/number.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getCurvenessForEdge } from '../helper/multipleGraphEdgeHelper.js';\nexport default function graphForceLayout(ecModel) {\n  ecModel.eachSeriesByType('graph', function (graphSeries) {\n    var coordSys = graphSeries.coordinateSystem;\n    if (coordSys && coordSys.type !== 'view') {\n      return;\n    }\n    if (graphSeries.get('layout') === 'force') {\n      var preservedPoints_1 = graphSeries.preservedPoints || {};\n      var graph_1 = graphSeries.getGraph();\n      var nodeData_1 = graph_1.data;\n      var edgeData = graph_1.edgeData;\n      var forceModel = graphSeries.getModel('force');\n      var initLayout = forceModel.get('initLayout');\n      if (graphSeries.preservedPoints) {\n        nodeData_1.each(function (idx) {\n          var id = nodeData_1.getId(idx);\n          nodeData_1.setItemLayout(idx, preservedPoints_1[id] || [NaN, NaN]);\n        });\n      } else if (!initLayout || initLayout === 'none') {\n        simpleLayout(graphSeries);\n      } else if (initLayout === 'circular') {\n        circularLayout(graphSeries, 'value');\n      }\n      var nodeDataExtent_1 = nodeData_1.getDataExtent('value');\n      var edgeDataExtent_1 = edgeData.getDataExtent('value');\n      // let edgeDataExtent = edgeData.getDataExtent('value');\n      var repulsion = forceModel.get('repulsion');\n      var edgeLength = forceModel.get('edgeLength');\n      var repulsionArr_1 = zrUtil.isArray(repulsion) ? repulsion : [repulsion, repulsion];\n      var edgeLengthArr_1 = zrUtil.isArray(edgeLength) ? edgeLength : [edgeLength, edgeLength];\n      // Larger value has smaller length\n      edgeLengthArr_1 = [edgeLengthArr_1[1], edgeLengthArr_1[0]];\n      var nodes_1 = nodeData_1.mapArray('value', function (value, idx) {\n        var point = nodeData_1.getItemLayout(idx);\n        var rep = linearMap(value, nodeDataExtent_1, repulsionArr_1);\n        if (isNaN(rep)) {\n          rep = (repulsionArr_1[0] + repulsionArr_1[1]) / 2;\n        }\n        return {\n          w: rep,\n          rep: rep,\n          fixed: nodeData_1.getItemModel(idx).get('fixed'),\n          p: !point || isNaN(point[0]) || isNaN(point[1]) ? null : point\n        };\n      });\n      var edges = edgeData.mapArray('value', function (value, idx) {\n        var edge = graph_1.getEdgeByIndex(idx);\n        var d = linearMap(value, edgeDataExtent_1, edgeLengthArr_1);\n        if (isNaN(d)) {\n          d = (edgeLengthArr_1[0] + edgeLengthArr_1[1]) / 2;\n        }\n        var edgeModel = edge.getModel();\n        var curveness = zrUtil.retrieve3(edge.getModel().get(['lineStyle', 'curveness']), -getCurvenessForEdge(edge, graphSeries, idx, true), 0);\n        return {\n          n1: nodes_1[edge.node1.dataIndex],\n          n2: nodes_1[edge.node2.dataIndex],\n          d: d,\n          curveness: curveness,\n          ignoreForceLayout: edgeModel.get('ignoreForceLayout')\n        };\n      });\n      // let coordSys = graphSeries.coordinateSystem;\n      var rect = coordSys.getBoundingRect();\n      var forceInstance = forceLayout(nodes_1, edges, {\n        rect: rect,\n        gravity: forceModel.get('gravity'),\n        friction: forceModel.get('friction')\n      });\n      forceInstance.beforeStep(function (nodes, edges) {\n        for (var i = 0, l = nodes.length; i < l; i++) {\n          if (nodes[i].fixed) {\n            // Write back to layout instance\n            vec2.copy(nodes[i].p, graph_1.getNodeByIndex(i).getLayout());\n          }\n        }\n      });\n      forceInstance.afterStep(function (nodes, edges, stopped) {\n        for (var i = 0, l = nodes.length; i < l; i++) {\n          if (!nodes[i].fixed) {\n            graph_1.getNodeByIndex(i).setLayout(nodes[i].p);\n          }\n          preservedPoints_1[nodeData_1.getId(i)] = nodes[i].p;\n        }\n        for (var i = 0, l = edges.length; i < l; i++) {\n          var e = edges[i];\n          var edge = graph_1.getEdgeByIndex(i);\n          var p1 = e.n1.p;\n          var p2 = e.n2.p;\n          var points = edge.getLayout();\n          points = points ? points.slice() : [];\n          points[0] = points[0] || [];\n          points[1] = points[1] || [];\n          vec2.copy(points[0], p1);\n          vec2.copy(points[1], p2);\n          if (+e.curveness) {\n            points[2] = [(p1[0] + p2[0]) / 2 - (p1[1] - p2[1]) * e.curveness, (p1[1] + p2[1]) / 2 - (p2[0] - p1[0]) * e.curveness];\n          }\n          edge.setLayout(points);\n        }\n      });\n      graphSeries.forceLayout = forceInstance;\n      graphSeries.preservedPoints = preservedPoints_1;\n      // Step to get the layout\n      forceInstance.step();\n    } else {\n      // Remove prev injected forceLayout instance\n      graphSeries.forceLayout = null;\n    }\n  });\n}", "map": {"version": 3, "names": ["forceLayout", "simpleLayout", "circularLayout", "linearMap", "vec2", "zrUtil", "getCurvenessForEdge", "graphForceLayout", "ecModel", "eachSeriesByType", "graphSeries", "coordSys", "coordinateSystem", "type", "get", "preservedPoints_1", "preservedPoints", "graph_1", "getGraph", "nodeData_1", "data", "edgeData", "forceModel", "getModel", "initLayout", "each", "idx", "id", "getId", "setItemLayout", "NaN", "nodeDataExtent_1", "getDataExtent", "edgeDataExtent_1", "repulsion", "edge<PERSON><PERSON><PERSON>", "repulsionArr_1", "isArray", "edgeLengthArr_1", "nodes_1", "mapArray", "value", "point", "getItemLayout", "rep", "isNaN", "w", "fixed", "getItemModel", "p", "edges", "edge", "getEdgeByIndex", "d", "edgeModel", "curveness", "retrieve3", "n1", "node1", "dataIndex", "n2", "node2", "ignoreForceLayout", "rect", "getBoundingRect", "forceInstance", "gravity", "friction", "beforeStep", "nodes", "i", "l", "length", "copy", "getNodeByIndex", "getLayout", "afterStep", "stopped", "setLayout", "e", "p1", "p2", "points", "slice", "step"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/graph/forceLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { forceLayout } from './forceHelper.js';\nimport { simpleLayout } from './simpleLayoutHelper.js';\nimport { circularLayout } from './circularLayoutHelper.js';\nimport { linearMap } from '../../util/number.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getCurvenessForEdge } from '../helper/multipleGraphEdgeHelper.js';\nexport default function graphForceLayout(ecModel) {\n  ecModel.eachSeriesByType('graph', function (graphSeries) {\n    var coordSys = graphSeries.coordinateSystem;\n    if (coordSys && coordSys.type !== 'view') {\n      return;\n    }\n    if (graphSeries.get('layout') === 'force') {\n      var preservedPoints_1 = graphSeries.preservedPoints || {};\n      var graph_1 = graphSeries.getGraph();\n      var nodeData_1 = graph_1.data;\n      var edgeData = graph_1.edgeData;\n      var forceModel = graphSeries.getModel('force');\n      var initLayout = forceModel.get('initLayout');\n      if (graphSeries.preservedPoints) {\n        nodeData_1.each(function (idx) {\n          var id = nodeData_1.getId(idx);\n          nodeData_1.setItemLayout(idx, preservedPoints_1[id] || [NaN, NaN]);\n        });\n      } else if (!initLayout || initLayout === 'none') {\n        simpleLayout(graphSeries);\n      } else if (initLayout === 'circular') {\n        circularLayout(graphSeries, 'value');\n      }\n      var nodeDataExtent_1 = nodeData_1.getDataExtent('value');\n      var edgeDataExtent_1 = edgeData.getDataExtent('value');\n      // let edgeDataExtent = edgeData.getDataExtent('value');\n      var repulsion = forceModel.get('repulsion');\n      var edgeLength = forceModel.get('edgeLength');\n      var repulsionArr_1 = zrUtil.isArray(repulsion) ? repulsion : [repulsion, repulsion];\n      var edgeLengthArr_1 = zrUtil.isArray(edgeLength) ? edgeLength : [edgeLength, edgeLength];\n      // Larger value has smaller length\n      edgeLengthArr_1 = [edgeLengthArr_1[1], edgeLengthArr_1[0]];\n      var nodes_1 = nodeData_1.mapArray('value', function (value, idx) {\n        var point = nodeData_1.getItemLayout(idx);\n        var rep = linearMap(value, nodeDataExtent_1, repulsionArr_1);\n        if (isNaN(rep)) {\n          rep = (repulsionArr_1[0] + repulsionArr_1[1]) / 2;\n        }\n        return {\n          w: rep,\n          rep: rep,\n          fixed: nodeData_1.getItemModel(idx).get('fixed'),\n          p: !point || isNaN(point[0]) || isNaN(point[1]) ? null : point\n        };\n      });\n      var edges = edgeData.mapArray('value', function (value, idx) {\n        var edge = graph_1.getEdgeByIndex(idx);\n        var d = linearMap(value, edgeDataExtent_1, edgeLengthArr_1);\n        if (isNaN(d)) {\n          d = (edgeLengthArr_1[0] + edgeLengthArr_1[1]) / 2;\n        }\n        var edgeModel = edge.getModel();\n        var curveness = zrUtil.retrieve3(edge.getModel().get(['lineStyle', 'curveness']), -getCurvenessForEdge(edge, graphSeries, idx, true), 0);\n        return {\n          n1: nodes_1[edge.node1.dataIndex],\n          n2: nodes_1[edge.node2.dataIndex],\n          d: d,\n          curveness: curveness,\n          ignoreForceLayout: edgeModel.get('ignoreForceLayout')\n        };\n      });\n      // let coordSys = graphSeries.coordinateSystem;\n      var rect = coordSys.getBoundingRect();\n      var forceInstance = forceLayout(nodes_1, edges, {\n        rect: rect,\n        gravity: forceModel.get('gravity'),\n        friction: forceModel.get('friction')\n      });\n      forceInstance.beforeStep(function (nodes, edges) {\n        for (var i = 0, l = nodes.length; i < l; i++) {\n          if (nodes[i].fixed) {\n            // Write back to layout instance\n            vec2.copy(nodes[i].p, graph_1.getNodeByIndex(i).getLayout());\n          }\n        }\n      });\n      forceInstance.afterStep(function (nodes, edges, stopped) {\n        for (var i = 0, l = nodes.length; i < l; i++) {\n          if (!nodes[i].fixed) {\n            graph_1.getNodeByIndex(i).setLayout(nodes[i].p);\n          }\n          preservedPoints_1[nodeData_1.getId(i)] = nodes[i].p;\n        }\n        for (var i = 0, l = edges.length; i < l; i++) {\n          var e = edges[i];\n          var edge = graph_1.getEdgeByIndex(i);\n          var p1 = e.n1.p;\n          var p2 = e.n2.p;\n          var points = edge.getLayout();\n          points = points ? points.slice() : [];\n          points[0] = points[0] || [];\n          points[1] = points[1] || [];\n          vec2.copy(points[0], p1);\n          vec2.copy(points[1], p2);\n          if (+e.curveness) {\n            points[2] = [(p1[0] + p2[0]) / 2 - (p1[1] - p2[1]) * e.curveness, (p1[1] + p2[1]) / 2 - (p2[0] - p1[0]) * e.curveness];\n          }\n          edge.setLayout(points);\n        }\n      });\n      graphSeries.forceLayout = forceInstance;\n      graphSeries.preservedPoints = preservedPoints_1;\n      // Step to get the layout\n      forceInstance.step();\n    } else {\n      // Remove prev injected forceLayout instance\n      graphSeries.forceLayout = null;\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,OAAO,KAAKC,IAAI,MAAM,4BAA4B;AAClD,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,eAAe,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAChDA,OAAO,CAACC,gBAAgB,CAAC,OAAO,EAAE,UAAUC,WAAW,EAAE;IACvD,IAAIC,QAAQ,GAAGD,WAAW,CAACE,gBAAgB;IAC3C,IAAID,QAAQ,IAAIA,QAAQ,CAACE,IAAI,KAAK,MAAM,EAAE;MACxC;IACF;IACA,IAAIH,WAAW,CAACI,GAAG,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;MACzC,IAAIC,iBAAiB,GAAGL,WAAW,CAACM,eAAe,IAAI,CAAC,CAAC;MACzD,IAAIC,OAAO,GAAGP,WAAW,CAACQ,QAAQ,CAAC,CAAC;MACpC,IAAIC,UAAU,GAAGF,OAAO,CAACG,IAAI;MAC7B,IAAIC,QAAQ,GAAGJ,OAAO,CAACI,QAAQ;MAC/B,IAAIC,UAAU,GAAGZ,WAAW,CAACa,QAAQ,CAAC,OAAO,CAAC;MAC9C,IAAIC,UAAU,GAAGF,UAAU,CAACR,GAAG,CAAC,YAAY,CAAC;MAC7C,IAAIJ,WAAW,CAACM,eAAe,EAAE;QAC/BG,UAAU,CAACM,IAAI,CAAC,UAAUC,GAAG,EAAE;UAC7B,IAAIC,EAAE,GAAGR,UAAU,CAACS,KAAK,CAACF,GAAG,CAAC;UAC9BP,UAAU,CAACU,aAAa,CAACH,GAAG,EAAEX,iBAAiB,CAACY,EAAE,CAAC,IAAI,CAACG,GAAG,EAAEA,GAAG,CAAC,CAAC;QACpE,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,CAACN,UAAU,IAAIA,UAAU,KAAK,MAAM,EAAE;QAC/CvB,YAAY,CAACS,WAAW,CAAC;MAC3B,CAAC,MAAM,IAAIc,UAAU,KAAK,UAAU,EAAE;QACpCtB,cAAc,CAACQ,WAAW,EAAE,OAAO,CAAC;MACtC;MACA,IAAIqB,gBAAgB,GAAGZ,UAAU,CAACa,aAAa,CAAC,OAAO,CAAC;MACxD,IAAIC,gBAAgB,GAAGZ,QAAQ,CAACW,aAAa,CAAC,OAAO,CAAC;MACtD;MACA,IAAIE,SAAS,GAAGZ,UAAU,CAACR,GAAG,CAAC,WAAW,CAAC;MAC3C,IAAIqB,UAAU,GAAGb,UAAU,CAACR,GAAG,CAAC,YAAY,CAAC;MAC7C,IAAIsB,cAAc,GAAG/B,MAAM,CAACgC,OAAO,CAACH,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,EAAEA,SAAS,CAAC;MACnF,IAAII,eAAe,GAAGjC,MAAM,CAACgC,OAAO,CAACF,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,EAAEA,UAAU,CAAC;MACxF;MACAG,eAAe,GAAG,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEA,eAAe,CAAC,CAAC,CAAC,CAAC;MAC1D,IAAIC,OAAO,GAAGpB,UAAU,CAACqB,QAAQ,CAAC,OAAO,EAAE,UAAUC,KAAK,EAAEf,GAAG,EAAE;QAC/D,IAAIgB,KAAK,GAAGvB,UAAU,CAACwB,aAAa,CAACjB,GAAG,CAAC;QACzC,IAAIkB,GAAG,GAAGzC,SAAS,CAACsC,KAAK,EAAEV,gBAAgB,EAAEK,cAAc,CAAC;QAC5D,IAAIS,KAAK,CAACD,GAAG,CAAC,EAAE;UACdA,GAAG,GAAG,CAACR,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC;QACnD;QACA,OAAO;UACLU,CAAC,EAAEF,GAAG;UACNA,GAAG,EAAEA,GAAG;UACRG,KAAK,EAAE5B,UAAU,CAAC6B,YAAY,CAACtB,GAAG,CAAC,CAACZ,GAAG,CAAC,OAAO,CAAC;UAChDmC,CAAC,EAAE,CAACP,KAAK,IAAIG,KAAK,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIG,KAAK,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA;QAC3D,CAAC;MACH,CAAC,CAAC;MACF,IAAIQ,KAAK,GAAG7B,QAAQ,CAACmB,QAAQ,CAAC,OAAO,EAAE,UAAUC,KAAK,EAAEf,GAAG,EAAE;QAC3D,IAAIyB,IAAI,GAAGlC,OAAO,CAACmC,cAAc,CAAC1B,GAAG,CAAC;QACtC,IAAI2B,CAAC,GAAGlD,SAAS,CAACsC,KAAK,EAAER,gBAAgB,EAAEK,eAAe,CAAC;QAC3D,IAAIO,KAAK,CAACQ,CAAC,CAAC,EAAE;UACZA,CAAC,GAAG,CAACf,eAAe,CAAC,CAAC,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC;QACnD;QACA,IAAIgB,SAAS,GAAGH,IAAI,CAAC5B,QAAQ,CAAC,CAAC;QAC/B,IAAIgC,SAAS,GAAGlD,MAAM,CAACmD,SAAS,CAACL,IAAI,CAAC5B,QAAQ,CAAC,CAAC,CAACT,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,EAAE,CAACR,mBAAmB,CAAC6C,IAAI,EAAEzC,WAAW,EAAEgB,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACxI,OAAO;UACL+B,EAAE,EAAElB,OAAO,CAACY,IAAI,CAACO,KAAK,CAACC,SAAS,CAAC;UACjCC,EAAE,EAAErB,OAAO,CAACY,IAAI,CAACU,KAAK,CAACF,SAAS,CAAC;UACjCN,CAAC,EAAEA,CAAC;UACJE,SAAS,EAAEA,SAAS;UACpBO,iBAAiB,EAAER,SAAS,CAACxC,GAAG,CAAC,mBAAmB;QACtD,CAAC;MACH,CAAC,CAAC;MACF;MACA,IAAIiD,IAAI,GAAGpD,QAAQ,CAACqD,eAAe,CAAC,CAAC;MACrC,IAAIC,aAAa,GAAGjE,WAAW,CAACuC,OAAO,EAAEW,KAAK,EAAE;QAC9Ca,IAAI,EAAEA,IAAI;QACVG,OAAO,EAAE5C,UAAU,CAACR,GAAG,CAAC,SAAS,CAAC;QAClCqD,QAAQ,EAAE7C,UAAU,CAACR,GAAG,CAAC,UAAU;MACrC,CAAC,CAAC;MACFmD,aAAa,CAACG,UAAU,CAAC,UAAUC,KAAK,EAAEnB,KAAK,EAAE;QAC/C,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;UAC5C,IAAID,KAAK,CAACC,CAAC,CAAC,CAACvB,KAAK,EAAE;YAClB;YACA3C,IAAI,CAACqE,IAAI,CAACJ,KAAK,CAACC,CAAC,CAAC,CAACrB,CAAC,EAAEhC,OAAO,CAACyD,cAAc,CAACJ,CAAC,CAAC,CAACK,SAAS,CAAC,CAAC,CAAC;UAC9D;QACF;MACF,CAAC,CAAC;MACFV,aAAa,CAACW,SAAS,CAAC,UAAUP,KAAK,EAAEnB,KAAK,EAAE2B,OAAO,EAAE;QACvD,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;UAC5C,IAAI,CAACD,KAAK,CAACC,CAAC,CAAC,CAACvB,KAAK,EAAE;YACnB9B,OAAO,CAACyD,cAAc,CAACJ,CAAC,CAAC,CAACQ,SAAS,CAACT,KAAK,CAACC,CAAC,CAAC,CAACrB,CAAC,CAAC;UACjD;UACAlC,iBAAiB,CAACI,UAAU,CAACS,KAAK,CAAC0C,CAAC,CAAC,CAAC,GAAGD,KAAK,CAACC,CAAC,CAAC,CAACrB,CAAC;QACrD;QACA,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGrB,KAAK,CAACsB,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;UAC5C,IAAIS,CAAC,GAAG7B,KAAK,CAACoB,CAAC,CAAC;UAChB,IAAInB,IAAI,GAAGlC,OAAO,CAACmC,cAAc,CAACkB,CAAC,CAAC;UACpC,IAAIU,EAAE,GAAGD,CAAC,CAACtB,EAAE,CAACR,CAAC;UACf,IAAIgC,EAAE,GAAGF,CAAC,CAACnB,EAAE,CAACX,CAAC;UACf,IAAIiC,MAAM,GAAG/B,IAAI,CAACwB,SAAS,CAAC,CAAC;UAC7BO,MAAM,GAAGA,MAAM,GAAGA,MAAM,CAACC,KAAK,CAAC,CAAC,GAAG,EAAE;UACrCD,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE;UAC3BA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE;UAC3B9E,IAAI,CAACqE,IAAI,CAACS,MAAM,CAAC,CAAC,CAAC,EAAEF,EAAE,CAAC;UACxB5E,IAAI,CAACqE,IAAI,CAACS,MAAM,CAAC,CAAC,CAAC,EAAED,EAAE,CAAC;UACxB,IAAI,CAACF,CAAC,CAACxB,SAAS,EAAE;YAChB2B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAACF,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAACD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,IAAIF,CAAC,CAACxB,SAAS,EAAE,CAACyB,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAACA,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,IAAID,CAAC,CAACxB,SAAS,CAAC;UACxH;UACAJ,IAAI,CAAC2B,SAAS,CAACI,MAAM,CAAC;QACxB;MACF,CAAC,CAAC;MACFxE,WAAW,CAACV,WAAW,GAAGiE,aAAa;MACvCvD,WAAW,CAACM,eAAe,GAAGD,iBAAiB;MAC/C;MACAkD,aAAa,CAACmB,IAAI,CAAC,CAAC;IACtB,CAAC,MAAM;MACL;MACA1E,WAAW,CAACV,WAAW,GAAG,IAAI;IAChC;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}