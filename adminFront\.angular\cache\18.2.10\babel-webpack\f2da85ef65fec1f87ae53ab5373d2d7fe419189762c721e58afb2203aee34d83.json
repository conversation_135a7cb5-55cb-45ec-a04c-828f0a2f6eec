{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiFileGetFileGet } from '../fn/file/api-file-get-file-get';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class FileService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiFileGetFileGet()` */\n  static {\n    this.ApiFileGetFileGetPath = '/api/File/GetFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFileGetFileGet()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiFileGetFileGet$Response(params, context) {\n    return apiFileGetFileGet(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFileGetFileGet$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiFileGetFileGet(params, context) {\n    return this.apiFileGetFileGet$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * 自定義方法：取得檔案並返回 Blob\n   * 這個方法正確處理檔案下載，返回 Observable<Blob>\n   *\n   * @param relativePath 相對路徑\n   * @param fileName 檔案名稱\n   * @param context HTTP 上下文\n   * @returns Observable<Blob>\n   */\n  getFile(relativePath, fileName, context) {\n    // 直接使用 HttpClient 發送請求，設置正確的 responseType\n    const url = `${this.rootUrl}${FileService.ApiFileGetFileGetPath}`;\n    const httpParams = new URLSearchParams();\n    if (relativePath) {\n      httpParams.set('relativePath', relativePath);\n    }\n    if (fileName) {\n      httpParams.set('fileName', fileName);\n    }\n    return this.http.get(`${url}?${httpParams.toString()}`, {\n      responseType: 'blob',\n      context\n    });\n  }\n  static {\n    this.ɵfac = function FileService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FileService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FileService,\n      factory: FileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiFileGetFileGet", "FileService", "constructor", "config", "http", "ApiFileGetFileGetPath", "apiFileGetFileGet$Response", "params", "context", "rootUrl", "pipe", "r", "body", "getFile", "relativePath", "fileName", "url", "httpParams", "URLSearchParams", "set", "get", "toString", "responseType", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\file.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiFileGetFileGet } from '../fn/file/api-file-get-file-get';\r\nimport { ApiFileGetFileGet$Params } from '../fn/file/api-file-get-file-get';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class FileService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiFileGetFileGet()` */\r\n  static readonly ApiFileGetFileGetPath = '/api/File/GetFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFileGetFileGet()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiFileGetFileGet$Response(params?: ApiFileGetFileGet$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {\r\n    return apiFileGetFileGet(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFileGetFileGet$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiFileGetFileGet(params?: ApiFileGetFileGet$Params, context?: HttpContext): Observable<void> {\r\n    return this.apiFileGetFileGet$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<void>): void => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * 自定義方法：取得檔案並返回 Blob\r\n   * 這個方法正確處理檔案下載，返回 Observable<Blob>\r\n   *\r\n   * @param relativePath 相對路徑\r\n   * @param fileName 檔案名稱\r\n   * @param context HTTP 上下文\r\n   * @returns Observable<Blob>\r\n   */\r\n  getFile(relativePath: string, fileName: string, context?: HttpContext): Observable<Blob> {\r\n    // 直接使用 HttpClient 發送請求，設置正確的 responseType\r\n    const url = `${this.rootUrl}${FileService.ApiFileGetFileGetPath}`;\r\n    const httpParams = new URLSearchParams();\r\n    if (relativePath) {\r\n      httpParams.set('relativePath', relativePath);\r\n    }\r\n    if (fileName) {\r\n      httpParams.set('fileName', fileName);\r\n    }\r\n\r\n    return this.http.get(`${url}?${httpParams.toString()}`, {\r\n      responseType: 'blob',\r\n      context\r\n    });\r\n  }\r\n\r\n}\r\n"], "mappings": "AAOA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,iBAAiB,QAAQ,kCAAkC;;;;AAIpE,OAAM,MAAOC,WAAY,SAAQF,WAAW;EAC1CG,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,qBAAqB,GAAG,mBAAmB;EAAC;EAE5D;;;;;;EAMAC,0BAA0BA,CAACC,MAAiC,EAAEC,OAAqB;IACjF,OAAOR,iBAAiB,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpE;EAEA;;;;;;EAMAR,iBAAiBA,CAACO,MAAiC,EAAEC,OAAqB;IACxE,OAAO,IAAI,CAACF,0BAA0B,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1DZ,GAAG,CAAEa,CAA2B,IAAWA,CAAC,CAACC,IAAI,CAAC,CACnD;EACH;EAEA;;;;;;;;;EASAC,OAAOA,CAACC,YAAoB,EAAEC,QAAgB,EAAEP,OAAqB;IACnE;IACA,MAAMQ,GAAG,GAAG,GAAG,IAAI,CAACP,OAAO,GAAGR,WAAW,CAACI,qBAAqB,EAAE;IACjE,MAAMY,UAAU,GAAG,IAAIC,eAAe,EAAE;IACxC,IAAIJ,YAAY,EAAE;MAChBG,UAAU,CAACE,GAAG,CAAC,cAAc,EAAEL,YAAY,CAAC;IAC9C;IACA,IAAIC,QAAQ,EAAE;MACZE,UAAU,CAACE,GAAG,CAAC,UAAU,EAAEJ,QAAQ,CAAC;IACtC;IAEA,OAAO,IAAI,CAACX,IAAI,CAACgB,GAAG,CAAC,GAAGJ,GAAG,IAAIC,UAAU,CAACI,QAAQ,EAAE,EAAE,EAAE;MACtDC,YAAY,EAAE,MAAM;MACpBd;KACD,CAAC;EACJ;;;uCAtDWP,WAAW,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAX3B,WAAW;MAAA4B,OAAA,EAAX5B,WAAW,CAAA6B,IAAA;MAAAC,UAAA,EADE;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}