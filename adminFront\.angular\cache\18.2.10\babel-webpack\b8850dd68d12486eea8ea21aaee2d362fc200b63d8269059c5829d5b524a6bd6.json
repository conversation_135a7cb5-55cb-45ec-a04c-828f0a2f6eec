{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { concatMap, finalize, tap } from 'rxjs';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { SharedModule } from '../../components/shared.module';\nimport * as _ from 'lodash';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/utility.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction ProjectManagementComponent_input_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_input_10_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.search, $event) || (ctx_r2.search = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.search);\n  }\n}\nfunction ProjectManagementComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialog_r5));\n    });\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵtext(2, \" \\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_tr_28_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.onSelectedBuildCase(item_r7, dialog_r5));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_tr_28_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRelatedDocument(item_r7.cID));\n    });\n    i0.ɵɵtext(1, \"\\u5EFA\\u6848\\u516C\\u4F48\\u6B04\\u6587\\u4EF6\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_tr_28_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.onDelete(item_r7, dialog_r5));\n    });\n    i0.ɵɵtext(1, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 23);\n    i0.ɵɵtemplate(6, ProjectManagementComponent_tr_28_button_6_Template, 2, 0, \"button\", 24)(7, ProjectManagementComponent_tr_28_button_7_Template, 2, 0, \"button\", 24)(8, ProjectManagementComponent_tr_28_button_8_Template, 2, 0, \"button\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.cID);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"span\", 58);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_ng_template_31_div_25_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearImage());\n    });\n    i0.ɵɵelement(4, \"i\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.fileName);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_img_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 61);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.imageUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_img_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 61);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.selectedBuildCase.CFrontImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_nb_option_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r12);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r12.CTitle, \" \");\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_nb_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r13);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r13.CTitle, \" \");\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_nb_option_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r14);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r14.CTitle, \" \");\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 28)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 29)(4, \"div\", 30)(5, \"label\", 31);\n    i0.ɵɵtext(6, \"\\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedBuildCase.CBuildCaseName, $event) || (ctx_r2.selectedBuildCase.CBuildCaseName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 33)(9, \"div\", 34)(10, \"label\", 35);\n    i0.ɵɵtext(11, \"\\u524D\\u53F0\\u5716\\u7247 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"h3\", 36);\n    i0.ɵɵtext(13, \"\\u53EA\\u63A5\\u53D7pdf, \\u5716\\u7247\\u6A94\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"h3\", 36);\n    i0.ɵɵtext(15, \"\\u5EFA\\u8B70\\u5C3A\\u5BF8:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"h3\", 36);\n    i0.ɵɵtext(17, \"\\u76F4\\u5F0F 1080 x 1440 px\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"h3\", 36);\n    i0.ɵɵtext(19, \"\\u6A6B\\u5F0F 1440 x 1080 px\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 37)(21, \"input\", 38);\n    i0.ɵɵlistener(\"change\", function ProjectManagementComponent_ng_template_31_Template_input_change_21_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"label\", 39);\n    i0.ɵɵelement(23, \"i\", 40);\n    i0.ɵɵtext(24, \" \\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, ProjectManagementComponent_ng_template_31_div_25_Template, 5, 1, \"div\", 41)(26, ProjectManagementComponent_ng_template_31_img_26_Template, 1, 1, \"img\", 42)(27, ProjectManagementComponent_ng_template_31_img_27_Template, 1, 1, \"img\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 43)(29, \"label\", 44);\n    i0.ɵɵtext(30, \"\\u7CFB\\u7D71\\u64CD\\u4F5C\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"textarea\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_textarea_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedBuildCase.CSystemInstruction, $event) || (ctx_r2.selectedBuildCase.CSystemInstruction = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 46)(33, \"label\", 47);\n    i0.ɵɵtext(34, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"nb-select\", 48);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_nb_select_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedStatus, $event) || (ctx_r2.selectedStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(36, ProjectManagementComponent_ng_template_31_nb_option_36_Template, 2, 2, \"nb-option\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 43)(38, \"label\", 47);\n    i0.ɵɵtext(39, \"PDF\\u6BCF\\u9801\\u7C3D\\u540D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"nb-select\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_nb_select_ngModelChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedShowAllSign, $event) || (ctx_r2.selectedShowAllSign = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(41, ProjectManagementComponent_ng_template_31_nb_option_41_Template, 2, 2, \"nb-option\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 43)(43, \"label\", 47);\n    i0.ɵɵtext(44, \"\\u986F\\u793A\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"nb-select\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_nb_select_ngModelChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedShowPrice, $event) || (ctx_r2.selectedShowPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(46, ProjectManagementComponent_ng_template_31_nb_option_46_Template, 2, 2, \"nb-option\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 43)(48, \"label\", 52);\n    i0.ɵɵtext(49, \"\\u7C3D\\u7F72\\u6587\\u4EF6\\u6CE8\\u610F\\u4E8B\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"textarea\", 53);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_textarea_ngModelChange_50_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedBuildCase.CFinalFileNotice, $event) || (ctx_r2.selectedBuildCase.CFinalFileNotice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 43)(52, \"label\", 52);\n    i0.ɵɵtext(53, \"\\u5BA2\\u6236\\u9808\\u7C3D\\u7F72\\u6587\\u4EF6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"nb-checkbox\", 54);\n    i0.ɵɵlistener(\"checkedChange\", function ProjectManagementComponent_ng_template_31_Template_nb_checkbox_checkedChange_54_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.checkedCustomerSign($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(55, \"nb-card-footer\", 18)(56, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_ng_template_31_Template_button_click_56_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r15));\n    });\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_ng_template_31_Template_button_click_58_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r15));\n    });\n    i0.ɵɵtext(59, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isNew ? \"\\u65B0\\u589E\\u5EFA\\u6848\" : \"\\u7DE8\\u8F2F\\u5EFA\\u6848\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedBuildCase.CBuildCaseName);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.imageUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuildCase.CFrontImage && !ctx_r2.imageUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedBuildCase.CSystemInstruction);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedShowAllSign);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptionsShow);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedShowPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptionsShow);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedBuildCase.CFinalFileNotice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"checked\", ctx_r2.selectedBuildCase.CCustomerSign);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.isNew ? \"\\u53D6\\u6D88\" : \"\\u95DC\\u9589\");\n  }\n}\nexport let ProjectManagementComponent = /*#__PURE__*/(() => {\n  class ProjectManagementComponent extends BaseComponent {\n    constructor(_allow, dialogService, message, valid, _buildCaseService, router, _utilityService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._buildCaseService = _buildCaseService;\n      this.router = router;\n      this._utilityService = _utilityService;\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      this.statusOptions = [{\n        cID: 0,\n        CValue: 0,\n        CTitle: '停用'\n      }, {\n        cID: 1,\n        CValue: 1,\n        CTitle: '啟用'\n      }];\n      this.isConfirmOptions = [{\n        cID: 0,\n        CValue: 0,\n        CTitle: '未確認'\n      }, {\n        cID: 1,\n        CValue: 1,\n        CTitle: '已確認'\n      }];\n      this.statusOptionsShow = [{\n        cID: 0,\n        CValue: false,\n        CTitle: '停用'\n      }, {\n        cID: 1,\n        CValue: true,\n        CTitle: '啟用'\n      }];\n      // CFinalFileNotice: string = \"\"\n      this.listBuildCase = [];\n      this.search = \"\";\n      this.initBuildCase = {\n        CBuildCaseName: '',\n        CFrontImage: '',\n        CSystemInstruction: '',\n        ImageList: null,\n        cID: undefined,\n        CStatus: undefined,\n        CFinalFileNotice: ''\n      };\n      this.isNew = true;\n      this.imgSrc = null;\n      this.imageUrl = null;\n      this.fileName = null;\n    }\n    onRelatedDocument(id) {\n      this.router.navigateByUrl(`/pages/related-documents/${id}`);\n    }\n    ngOnInit() {\n      this.getListBuildCase().subscribe();\n      this.selectedStatus = this.statusOptions[0];\n      this.selectedShowPrice = this.statusOptionsShow[0];\n      this.selectedShowAllSign = this.statusOptionsShow[0];\n      this.selectedIsConfirm = this.isConfirmOptions[0];\n      this.selectedBuildCase = {\n        ...this.initBuildCase\n      };\n    }\n    getListBuildCase() {\n      return this._buildCaseService.apiBuildCaseGetAllBuildCasePost$Json({\n        body: {\n          PageIndex: this.pageIndex,\n          PageSize: this.pageSize,\n          CName: this.search,\n          CIsPagi: true\n        }\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.listBuildCase = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n        }\n      }));\n    }\n    addNew(ref) {\n      this.isNew = true;\n      this.clearForm();\n      this.selectedStatus = this.statusOptions[0];\n      this.selectedShowPrice = this.statusOptionsShow[0];\n      this.selectedShowAllSign = this.statusOptionsShow[0];\n      this.selectedBuildCase.CStatus = this.statusOptions[0].CValue;\n      this.selectedIsConfirm = this.isConfirmOptions[0];\n      this.selectedBuildCase.CIsConfirm = this.isConfirmOptions[0].CValue;\n      this.selectedBuildCase.CCustomerSign = true;\n      this.dialogService.open(ref);\n    }\n    getBase64Image(file) {\n      return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => resolve(reader.result);\n        reader.onerror = error => reject(error);\n      });\n    }\n    onFileSelected(event) {\n      const file = event.target.files[0];\n      const fileRegex = /pdf|jpg|jpeg|png/i;\n      if (!fileRegex.test(file.type)) {\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n        return;\n      }\n      if (file) {\n        const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];\n        if (allowedTypes.includes(file.type)) {\n          this.fileName = file.name;\n          const reader = new FileReader();\n          reader.onload = e => {\n            this.imageUrl = e.target.result;\n            if (this.fileInput) {\n              this.fileInput.nativeElement.value = null;\n            }\n          };\n          reader.readAsDataURL(file);\n        }\n      }\n    }\n    clearImage() {\n      if (this.imageUrl) {\n        this.imageUrl = null;\n        this.fileName = null;\n        if (this.fileInput) {\n          this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n        }\n      }\n    }\n    clearForm() {\n      this.selectedBuildCase = {\n        ...this.initBuildCase\n      };\n      this.clearImage();\n    }\n    onSelectedBuildCase(data, ref) {\n      console.log(data);\n      this.isNew = false;\n      this.dialogService.open(ref);\n      this.clearImage();\n      this.selectedBuildCase = _.cloneDeep(data);\n      this.selectedBuildCase.CSystemInstruction = this._utilityService.htmltoText(data.CSystemInstruction);\n      this.selectedStatus = this.statusOptions[this.selectedBuildCase.CStatus];\n      this.selectedShowPrice = this.statusOptionsShow[this.selectedBuildCase.CShowPrice == true ? 1 : 0];\n      this.selectedShowAllSign = this.statusOptionsShow[this.selectedBuildCase.CShowSignAll == true ? 1 : 0];\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[建案名稱]', this.selectedBuildCase.CBuildCaseName);\n      this.valid.isStringMaxLength('[建案名稱]', this.selectedBuildCase.CBuildCaseName, 50);\n      if (this.isNew) {\n        this.valid.required('[前台圖片]', this.selectedBuildCase.CFrontImage);\n      }\n      this.valid.required('[系統操作說明]', this.selectedBuildCase.CSystemInstruction);\n      this.valid.required('[狀態]', this.selectedBuildCase.CStatus?.toString());\n    }\n    removeBase64Prefix(base64String) {\n      const prefixIndex = base64String.indexOf(\",\");\n      if (prefixIndex !== -1) {\n        return base64String.substring(prefixIndex + 1);\n      }\n      return base64String;\n    }\n    nl2br(str) {\n      if (typeof str === 'undefined' || str === null) {\n        return '';\n      }\n      return str.replace(/\\n/g, '<br>');\n      ;\n    }\n    onSubmit(ref) {\n      this.selectedBuildCase.CStatus = this.selectedStatus.CValue;\n      this.selectedBuildCase.CShowPrice = this.selectedShowPrice.CValue;\n      this.selectedBuildCase.CShowSignAll = this.selectedShowAllSign.CValue;\n      const requestBody = {\n        CBuildCaseName: this.selectedBuildCase.CBuildCaseName,\n        CStatus: this.selectedStatus.CValue,\n        CSystemInstruction: this.nl2br(this.selectedBuildCase.CSystemInstruction),\n        CIsConfirm: this.selectedIsConfirm.CValue == 0 ? false : true,\n        CFinalFileNotice: this.selectedBuildCase.CFinalFileNotice,\n        CShowPrice: this.selectedShowPrice.CValue,\n        CShowSignAll: this.selectedShowAllSign.CValue,\n        CCustomerSign: this.selectedBuildCase.CCustomerSign == true ? true : false\n      };\n      if (this.isNew && this.imageUrl) {\n        // NEW\n        this.selectedBuildCase.CFrontImage = this.removeBase64Prefix(this.imageUrl); // as string\n        requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl);\n      } else {\n        // EDIT\n        if (this.imageUrl) {\n          requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl);\n        }\n        requestBody.CBuildCaseID = this.selectedBuildCase.cID;\n      }\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._buildCaseService.apiBuildCaseSaveBuildCasePost$Json({\n        body: requestBody\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        } else {\n          this.message.showSucessMSG(\"建案名稱不可重複\");\n        }\n      }), concatMap(() => this.getListBuildCase()), finalize(() => ref.close())).subscribe();\n    }\n    checkedCustomerSign(checked) {\n      this.selectedBuildCase.CCustomerSign = checked;\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getListBuildCase().subscribe();\n    }\n    onDelete(data, ref) {\n      if (window.confirm(`確定要刪除【項目${data.CBuildCaseName}】?`)) {\n        this._buildCaseService.apiBuildCaseDeleteBuildCasePost$Json({\n          body: {\n            \"CBuildCaseID\": data.cID\n          }\n        }).pipe(tap(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG(\"執行成功\");\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        }), concatMap(() => this.getListBuildCase()), finalize(() => ref.close())).subscribe();\n      }\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    static {\n      this.ɵfac = function ProjectManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ProjectManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.UtilityService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectManagementComponent,\n        selectors: [[\"ngx-project-management\"]],\n        viewQuery: function ProjectManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 33,\n        vars: 6,\n        consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"row\"], [1, \"col-1\"], [\"for\", \"project\", 1, \"text-nowrap\", \"mr-1\", \"h-full\", \"mt-2\"], [1, \"col-5\"], [\"type\", \"text\", \"nbInput\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [1, \"col-6\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [\"type\", \"text\", \"nbInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-primary btn-sm m-1 text-red-500 border-red-500\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", \"text-red-500\", \"border-red-500\", 3, \"click\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"imageName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"100px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-baseline\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", \"baseLabel\", \"\", 1, \"mb-0\", 2, \"min-width\", \"100px\"], [2, \"color\", \"red\"], [1, \"flex\", \"flex-col\", \"items-start\", \"space-y-4\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\"], [\"for\", \"fileInput\", 1, \"cursor-pointer\", \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [1, \"fa-solid\", \"fa-cloud-arrow-up\", \"mr-2\"], [\"class\", \"flex items-center space-x-2\", 4, \"ngIf\"], [\"alt\", \"Uploaded Image\", \"class\", \"max-w-xs h-auto rounded-md\", 3, \"src\", 4, \"ngIf\"], [1, \"form-group\", \"d-flex\", \"mt-2\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"100px\"], [\"contenteditable\", \"\", \"name\", \"remark\", \"id\", \"remark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"100px\"], [\"placeholder\", \"Select Status\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"PDF\\u6BCF\\u9801\\u7C3D\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u986F\\u793A\\u50F9\\u683C\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"finalfilenotice\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"100px\"], [\"name\", \"finalfilenotice\", \"id\", \"finalfilenotice\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\"], [\"alt\", \"Uploaded Image\", 1, \"max-w-xs\", \"h-auto\", \"rounded-md\", 3, \"src\"], [3, \"value\"]],\n        template: function ProjectManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 2)(5, \"div\", 3)(6, \"label\", 4);\n            i0.ɵɵtext(7, \"\\u5EFA\\u6848\\u540D\\u7A31 \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 5)(9, \"nb-form-field\");\n            i0.ɵɵtemplate(10, ProjectManagementComponent_input_10_Template, 1, 1, \"input\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function ProjectManagementComponent_Template_button_click_13_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getListBuildCase().subscribe());\n            });\n            i0.ɵɵelement(14, \"i\", 10);\n            i0.ɵɵtext(15, \" \\u67E5\\u8A62 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(16, ProjectManagementComponent_button_16_Template, 3, 0, \"button\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 12)(18, \"table\", 13)(19, \"thead\")(20, \"tr\", 14)(21, \"th\", 15);\n            i0.ɵɵtext(22, \"ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"th\", 15);\n            i0.ɵɵtext(24, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"th\", 16);\n            i0.ɵɵtext(26, \"\\u52D5\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(27, \"tbody\");\n            i0.ɵɵtemplate(28, ProjectManagementComponent_tr_28_Template, 9, 5, \"tr\", 17);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(29, \"nb-card-footer\", 18)(30, \"ngb-pagination\", 19);\n            i0.ɵɵtwoWayListener(\"pageChange\", function ProjectManagementComponent_Template_ngb_pagination_pageChange_30_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"pageChange\", function ProjectManagementComponent_Template_ngb_pagination_pageChange_30_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(31, ProjectManagementComponent_ng_template_31_Template, 60, 15, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCase);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i8.NgForOf, i8.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i10.NgbPagination, i11.BreadcrumbComponent, i12.BaseLabelDirective]\n      });\n    }\n  }\n  return ProjectManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}