{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { trigger, style, transition, animate } from '@angular/animations';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r3, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_55_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.getActiveFiltersCount());\n  }\n}\nfunction SettingTimePeriodComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_55_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleAdvancedFilters());\n    });\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementStart(3, \"span\", 52);\n    i0.ɵɵtext(4, \"\\u9032\\u968E\\u7BE9\\u9078\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SettingTimePeriodComponent_div_55_span_5_Template, 2, 1, \"span\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fa-chevron-down\", !ctx_r4.showAdvancedFilters)(\"fa-chevron-up\", ctx_r4.showAdvancedFilters);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.hasActiveFilters());\n  }\n}\nfunction SettingTimePeriodComponent_div_56_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", floor_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", floor_r7, \"F \");\n  }\n}\nfunction SettingTimePeriodComponent_div_56_button_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_button_37_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearAllFilters());\n    });\n    i0.ɵɵelement(1, \"i\", 71);\n    i0.ɵɵtext(2, \"\\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"div\", 22)(3, \"nb-form-field\", 32);\n    i0.ɵɵelement(4, \"nb-icon\", 57);\n    i0.ɵɵelementStart(5, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.searchKeyword, $event) || (ctx_r4.filterOptions.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_input_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"nb-select\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.statusFilter, $event) || (ctx_r4.filterOptions.statusFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(8, \"nb-option\", 29);\n    i0.ɵɵtext(9, \"\\u5168\\u90E8\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-option\", 60);\n    i0.ɵɵtext(11, \"\\u9032\\u884C\\u4E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-option\", 61);\n    i0.ɵɵtext(13, \"\\u5F85\\u958B\\u653E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-option\", 62);\n    i0.ɵɵtext(15, \"\\u5DF2\\u904E\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-option\", 63);\n    i0.ɵɵtext(17, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-option\", 64);\n    i0.ɵɵtext(19, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 27)(21, \"nb-select\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.floorFilter, $event) || (ctx_r4.filterOptions.floorFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_21_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(22, \"nb-option\", 29);\n    i0.ɵɵtext(23, \"\\u5168\\u90E8\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SettingTimePeriodComponent_div_56_nb_option_24_Template, 2, 2, \"nb-option\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 66)(26, \"nb-select\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.pageSize, $event) || (ctx_r4.pageSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_26_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onPageSizeChange());\n    });\n    i0.ɵɵelementStart(27, \"nb-option\", 48);\n    i0.ɵɵtext(28, \"50\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-option\", 48);\n    i0.ɵɵtext(30, \"100\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"nb-option\", 48);\n    i0.ɵɵtext(32, \"200\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-option\", 48);\n    i0.ɵɵtext(34, \"500\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 38)(36, \"div\", 68);\n    i0.ɵɵtemplate(37, SettingTimePeriodComponent_div_56_button_37_Template, 3, 0, \"button\", 69);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@slideInOut\", undefined);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.statusFilter);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.floorFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.availableFloors);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 50);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 200);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 500);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.hasActiveFilters());\n  }\n}\nfunction SettingTimePeriodComponent_div_57_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 114);\n    i0.ɵɵelement(1, \"i\", 115);\n    i0.ɵɵtext(2, \" \\u5DF2\\u9078 \");\n    i0.ɵɵelementStart(3, \"strong\", 116);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7B46 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedHouses.length);\n  }\n}\nfunction SettingTimePeriodComponent_div_57_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_32_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.openBatchSetting());\n    });\n    i0.ɵɵelement(2, \"i\", 119);\n    i0.ɵɵtext(3, \"\\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵelementStart(4, \"span\", 120);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_32_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearSelection());\n    });\n    i0.ɵɵelement(7, \"i\", 71);\n    i0.ɵɵtext(8, \"\\u6E05\\u9664\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedHouses.length);\n  }\n}\nfunction SettingTimePeriodComponent_div_57_tr_74_small_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 138);\n    i0.ɵɵtext(1, \"\\u7121\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_57_tr_74_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 139);\n    i0.ɵɵelement(1, \"i\", 140);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 1, house_r12.CChangeStartDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_tr_74_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 98);\n    i0.ɵɵelement(1, \"i\", 141);\n    i0.ɵɵtext(2, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_57_tr_74_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 139);\n    i0.ɵɵelement(1, \"i\", 142);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 1, house_r12.CChangeEndDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_tr_74_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 98);\n    i0.ɵɵelement(1, \"i\", 141);\n    i0.ɵɵtext(2, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_57_tr_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 122)(2, \"nb-checkbox\", 123);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_tr_74_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const house_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r12.selected, $event) || (house_r12.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_tr_74_Template_nb_checkbox_ngModelChange_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 124)(5, \"span\", 125);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SettingTimePeriodComponent_div_57_tr_74_small_7_Template, 2, 0, \"small\", 126);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\")(9, \"span\", 127);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\", 122)(12, \"span\", 128);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\")(15, \"div\", 129);\n    i0.ɵɵtemplate(16, SettingTimePeriodComponent_div_57_tr_74_span_16_Template, 4, 4, \"span\", 130)(17, SettingTimePeriodComponent_div_57_tr_74_span_17_Template, 3, 0, \"span\", 131);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"div\", 129);\n    i0.ɵɵtemplate(20, SettingTimePeriodComponent_div_57_tr_74_span_20_Template, 4, 4, \"span\", 130)(21, SettingTimePeriodComponent_div_57_tr_74_span_21_Template, 3, 0, \"span\", 131);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"td\", 122)(23, \"div\", 132)(24, \"span\", 133);\n    i0.ɵɵelement(25, \"i\", 134);\n    i0.ɵɵelementStart(26, \"span\", 135);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"td\", 122)(29, \"div\", 39)(30, \"button\", 136);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_tr_74_Template_button_click_30_listener() {\n      const house_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const dialog_r13 = i0.ɵɵreference(65);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r13, house_r12));\n    });\n    i0.ɵɵelement(31, \"i\", 137);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const house_r12 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"table-row-selected\", house_r12.selected)(\"table-row-disabled\", !house_r12.CHouseId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r12.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r12.CHouseId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(house_r12.CHouseHold);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r12.CHouseId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(house_r12.CBuildingName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", house_r12.CFloor, \"F\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", house_r12.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r12.CChangeStartDate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", house_r12.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r12.CChangeEndDate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r12));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r4.getStatusIcon(house_r12));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getStatusText(house_r12));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !house_r12.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_57_div_75_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 148)(1, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_75_li_21_Template_button_click_1_listener() {\n      const page_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(page_r16));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r16 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r16 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r16);\n  }\n}\nfunction SettingTimePeriodComponent_div_57_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 143)(1, \"div\", 74)(2, \"div\", 144)(3, \"span\", 98);\n    i0.ɵɵtext(4, \" \\u7B2C \");\n    i0.ɵɵelementStart(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" \\u9801\\uFF0C\\u5171 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" \\u9801 \");\n    i0.ɵɵelementStart(11, \"span\", 145);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"nav\", 146)(14, \"ul\", 147)(15, \"li\", 148)(16, \"button\", 149);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_75_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(1));\n    });\n    i0.ɵɵelement(17, \"i\", 150);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\", 148)(19, \"button\", 151);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_75_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage - 1));\n    });\n    i0.ɵɵelement(20, \"i\", 152);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, SettingTimePeriodComponent_div_57_div_75_li_21_Template, 3, 3, \"li\", 153);\n    i0.ɵɵelementStart(22, \"li\", 148)(23, \"button\", 154);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_75_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage + 1));\n    });\n    i0.ɵɵelement(24, \"i\", 155);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"li\", 148)(26, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_75_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.totalPages));\n    });\n    i0.ɵɵelement(27, \"i\", 157);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"div\", 158)(29, \"div\", 159)(30, \"input\", 160);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_div_75_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.jumpToPage, $event) || (ctx_r4.jumpToPage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function SettingTimePeriodComponent_div_57_div_75_Template_input_keyup_enter_30_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.jumpToPageAction());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 161);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_75_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.jumpToPageAction());\n    });\n    i0.ɵɵelement(32, \"i\", 162);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r4.currentPage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.totalPages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" (\\u986F\\u793A\\u7B2C \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" \\u7B46\\uFF0C \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46) \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.jumpToPage);\n    i0.ɵɵproperty(\"min\", 1)(\"max\", ctx_r4.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"div\", 74)(3, \"div\", 75)(4, \"span\", 76);\n    i0.ɵɵelement(5, \"i\", 77);\n    i0.ɵɵtext(6, \" \\u5171 \");\n    i0.ɵɵelementStart(7, \"strong\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, SettingTimePeriodComponent_div_57_span_10_Template, 6, 1, \"span\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 79)(12, \"div\", 80)(13, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"active\"));\n    });\n    i0.ɵɵelement(14, \"i\", 82);\n    i0.ɵɵtext(15, \"\\u9032\\u884C\\u4E2D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"pending\"));\n    });\n    i0.ɵɵelement(17, \"i\", 83);\n    i0.ɵɵtext(18, \"\\u5F85\\u958B\\u653E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"expired\"));\n    });\n    i0.ɵɵelement(20, \"i\", 84);\n    i0.ɵɵtext(21, \"\\u5DF2\\u904E\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"not-set\"));\n    });\n    i0.ɵɵelement(23, \"i\", 86);\n    i0.ɵɵtext(24, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(25, \"div\", 87)(26, \"div\", 74)(27, \"div\", 88)(28, \"div\", 89)(29, \"nb-checkbox\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_29_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementStart(30, \"span\", 91);\n    i0.ɵɵtext(31, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, SettingTimePeriodComponent_div_57_div_32_Template, 9, 1, \"div\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 93)(34, \"div\", 94)(35, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.exportData());\n    });\n    i0.ɵɵelement(36, \"i\", 96);\n    i0.ɵɵtext(37, \"\\u532F\\u51FA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 97)(39, \"small\", 98);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(41, \"div\", 99)(42, \"table\", 100)(43, \"thead\", 101)(44, \"tr\")(45, \"th\", 102)(46, \"nb-checkbox\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_46_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_46_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"th\", 104);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_th_click_47_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CHouseHold\"));\n    });\n    i0.ɵɵelementStart(48, \"div\", 105);\n    i0.ɵɵtext(49, \" \\u6236\\u578B \");\n    i0.ɵɵelement(50, \"i\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"th\", 107);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_th_click_51_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CBuildingName\"));\n    });\n    i0.ɵɵelementStart(52, \"div\", 105);\n    i0.ɵɵtext(53, \" \\u68DF\\u5225 \");\n    i0.ɵɵelement(54, \"i\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"th\", 108);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_th_click_55_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CFloor\"));\n    });\n    i0.ɵɵelementStart(56, \"div\", 105);\n    i0.ɵɵtext(57, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelement(58, \"i\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"th\", 109);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_th_click_59_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeStartDate\"));\n    });\n    i0.ɵɵelementStart(60, \"div\", 105);\n    i0.ɵɵtext(61, \" \\u958B\\u59CB\\u65E5\\u671F \");\n    i0.ɵɵelement(62, \"i\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"th\", 109);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_th_click_63_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeEndDate\"));\n    });\n    i0.ɵɵelementStart(64, \"div\", 105);\n    i0.ɵɵtext(65, \" \\u7D50\\u675F\\u65E5\\u671F \");\n    i0.ɵɵelement(66, \"i\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"th\", 110)(68, \"div\", 105);\n    i0.ɵɵtext(69, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"th\", 111)(71, \"div\", 105);\n    i0.ɵɵtext(72, \" \\u64CD\\u4F5C \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(73, \"tbody\");\n    i0.ɵɵtemplate(74, SettingTimePeriodComponent_div_57_tr_74_Template, 32, 20, \"tr\", 112);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(75, SettingTimePeriodComponent_div_57_div_75_Template, 33, 21, \"div\", 113);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.filteredHouses.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r4.filterOptions.statusFilter === \"active\")(\"btn-outline-primary\", ctx_r4.filterOptions.statusFilter !== \"active\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-warning\", ctx_r4.filterOptions.statusFilter === \"pending\")(\"btn-outline-warning\", ctx_r4.filterOptions.statusFilter !== \"pending\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-danger\", ctx_r4.filterOptions.statusFilter === \"expired\")(\"btn-outline-danger\", ctx_r4.filterOptions.statusFilter !== \"expired\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-not-set-active\", ctx_r4.filterOptions.statusFilter === \"not-set\")(\"btn-not-set-outline\", ctx_r4.filterOptions.statusFilter !== \"not-set\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.filteredHouses.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" / \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46 \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.paginatedHouses)(\"ngForTrackBy\", ctx_r4.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 98);\n    i0.ɵɵelement(4, \"i\", 165);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\\u5F8C\\u67E5\\u8A62\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 98)(4, \"div\", 166)(5, \"span\", 167);\n    i0.ɵɵtext(6, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 168);\n    i0.ɵɵtext(8, \"\\u8CC7\\u6599\\u8F09\\u5165\\u4E2D\\uFF0C\\u8ACB\\u7A0D\\u5019...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 116);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \\u5DF2\\u9078\\u64C7 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225 \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r4.selectedBuildingForBatch.name, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 186);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r18 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", house_r18.CHouseHold, \" (\", house_r18.CBuildingName, \"-\", house_r18.CFloor, \"F) \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_28_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 98);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" ...\\u7B49 \", ctx_r4.selectedHouses.length - 10, \" \\u500B \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 183)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 184);\n    i0.ɵɵtemplate(4, SettingTimePeriodComponent_ng_template_60_div_28_span_4_Template, 2, 3, \"span\", 185)(5, SettingTimePeriodComponent_ng_template_60_div_28_span_5_Template, 2, 1, \"span\", 131);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5C07\\u5957\\u7528\\u5230\\u5DF2\\u9078\\u64C7\\u7684 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedHouses.slice(0, 10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 10);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 123);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      const house_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r23.selected, $event) || (house_r23.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r23 = ctx.$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r23.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r23.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r23.CHouseHold, \" (\", house_r23.CBuildingName, \") \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_nb_checkbox_1_Template, 2, 4, \"nb-checkbox\", 192);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", floor_r21.houses);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 189)(1, \"nb-checkbox\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const floor_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      i0.ɵɵtwoWayBindingSet(floor_r21.selected, $event) || (floor_r21.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      const floor_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.onFloorSelectionChange(floor_r21));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_Template, 2, 1, \"div\", 190);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r21 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", floor_r21.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r21.floorNumber, \"F (\", floor_r21.houses.length, \" \\u6236) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", floor_r21.selected);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_29_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 168);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template, 4, 4, \"div\", 188);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedBuildingForBatch.floors);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"nb-checkbox\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_div_29_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.applyToAll, $event) || (ctx_r4.batchSettings.applyToAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_60_div_29_div_3_Template, 2, 1, \"div\", 187);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.applyToAll);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u90E8\\u6236\\u5225 (\", ctx_r4.flattenedHouses.length, \" \\u500B) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.batchSettings.applyToAll && ctx_r4.selectedBuildingForBatch);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225)\");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 169)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_60_span_3_Template, 2, 1, \"span\", 170)(4, SettingTimePeriodComponent_ng_template_60_span_4_Template, 2, 1, \"span\", 171);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"div\", 172)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n    i0.ɵɵelementStart(9, \"span\", 173);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 174)(12, \"nb-form-field\", 175);\n    i0.ɵɵelement(13, \"nb-icon\", 33);\n    i0.ɵɵelementStart(14, \"input\", 176);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.startDate, $event) || (ctx_r4.batchSettings.startDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 35, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 177);\n    i0.ɵɵtext(18, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\", 175);\n    i0.ɵɵelement(20, \"nb-icon\", 33);\n    i0.ɵɵelementStart(21, \"input\", 176);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.endDate, $event) || (ctx_r4.batchSettings.endDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 35, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 172)(25, \"label\");\n    i0.ɵɵtext(26, \"\\u9069\\u7528\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 178);\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_ng_template_60_div_28_Template, 6, 3, \"div\", 179)(29, SettingTimePeriodComponent_ng_template_60_div_29_Template, 4, 3, \"div\", 171);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-card-footer\", 180)(31, \"button\", 181);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_60_Template_button_click_31_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r24));\n    });\n    i0.ɵɵtext(32, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 182);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_60_Template_button_click_33_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onBatchSubmit(ref_r24));\n    });\n    i0.ɵɵtext(34, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(35, SettingTimePeriodComponent_ng_template_60_span_35_Template, 2, 1, \"span\", 171);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const batchStartDate_r25 = i0.ɵɵreference(16);\n    const batchEndDate_r26 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedBuildingForBatch && ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", batchStartDate_r25);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.startDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", batchEndDate_r26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.endDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 193);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 194);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 193)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 195);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 196)(7, \"div\", 197)(8, \"label\", 198);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 173);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 199);\n    i0.ɵɵelement(13, \"nb-icon\", 33);\n    i0.ɵɵelementStart(14, \"input\", 200);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_64_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 35, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 201);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 33);\n    i0.ɵɵelementStart(21, \"input\", 202);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_64_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 35, 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 194)(25, \"button\", 203);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_64_Template_button_click_25_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r27).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r28));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 204);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_64_Template_button_click_27_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r27).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSubmit(ref_r28));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r29 = i0.ɵɵreference(16);\n    const changeEndDate_r30 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r4.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r29);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r30);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    // 新增的屬性\n    this.buildingGroups = [];\n    this.buildingOptions = [];\n    this.selectedBuilding = '';\n    this.availableFloors = [];\n    // 篩選和搜尋\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 批次設定\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: true,\n      selectedBuildings: [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    this.selectedBuildingForBatch = null;\n    // 表格視圖相關屬性\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.loading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalPages = 1;\n    // 排序相關\n    this.sortField = '';\n    this.sortDirection = 'asc';\n    // 數學函數引用\n    this.Math = Math;\n    // 新增的UI控制屬性\n    this.showAdvancedFilters = false;\n    this.jumpToPage = 1;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  // 新增的UI控制方法\n  toggleAdvancedFilters() {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n  hasActiveFilters() {\n    return !!(this.filterOptions.searchKeyword || this.filterOptions.statusFilter || this.filterOptions.floorFilter);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.filterOptions.searchKeyword) count++;\n    if (this.filterOptions.statusFilter) count++;\n    if (this.filterOptions.floorFilter) count++;\n    return count;\n  }\n  resetFilters() {\n    this.searchQuery = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.selectedBuilding = '';\n    this.clearAllFilters();\n  }\n  clearAllFilters() {\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    this.onSearch();\n  }\n  setQuickFilter(status) {\n    if (this.filterOptions.statusFilter === status) {\n      this.filterOptions.statusFilter = '';\n    } else {\n      this.filterOptions.statusFilter = status;\n    }\n    this.onSearch();\n  }\n  clearSelection() {\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.flattenedHouses.forEach(house => house.selected = false);\n  }\n  getStatusIcon(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return 'fas fa-play-circle';\n      case 'pending':\n        return 'fas fa-clock';\n      case 'expired':\n        return 'fas fa-times-circle';\n      case 'not-set':\n        return 'fas fa-question-circle';\n      case 'disabled':\n        return 'fas fa-ban';\n      default:\n        return 'fas fa-question-circle';\n    }\n  }\n  jumpToPageAction() {\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\n      this.goToPage(this.jumpToPage);\n    }\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CBuildingName: '未分類',\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  // 新增：建案變更處理\n  onBuildCaseChange() {\n    // 重置所有相關狀態\n    this.resetAllStates();\n    // 執行查詢\n    this.getHouseChangeDate();\n  }\n  // 新增：重置所有狀態\n  resetAllStates() {\n    // 重置數據\n    this.houseChangeDates = [];\n    this.convertedHouseArray = [];\n    this.buildingGroups = [];\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    // 重置篩選條件\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 重置選擇狀態\n    this.selectAll = false;\n    this.selectedBuilding = '';\n    // 重置分頁\n    this.currentPage = 1;\n    this.totalPages = 1;\n    // 重置可用選項\n    this.buildingOptions = [];\n    this.availableFloors = [];\n    // 重置排序\n    this.sortField = '';\n    this.sortDirection = 'asc';\n  }\n  getHouseChangeDate() {\n    // 如果沒有選擇建案，直接返回\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\n      this.loading = false;\n      return;\n    }\n    this.loading = true;\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      this.loading = false;\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          // 新增：建立棟別分組資料\n          this.buildBuildingGroups(res.Entries);\n          // 新增：建立扁平化資料\n          this.buildFlattenedHouses(res.Entries);\n        }\n      }\n    });\n  }\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n  buildBuildingGroups(data) {\n    const buildingMap = new Map();\n    data.forEach(household => {\n      const houseType = household.CHouseHold || ''; // 戶型\n      household.CHouses?.forEach(house => {\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\n        const floor = house.CFloor || 0;\n        if (!buildingMap.has(buildingName)) {\n          buildingMap.set(buildingName, new Map());\n        }\n        const floorMap = buildingMap.get(buildingName);\n        if (!floorMap.has(floor)) {\n          floorMap.set(floor, []);\n        }\n        floorMap.get(floor).push({\n          CHouseHold: houseType,\n          // 戶型\n          CBuildingName: buildingName,\n          // 棟別\n          CHouseId: house.CHouseId || 0,\n          CFloor: floor,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 轉換為BuildingGroup格式\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n      const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n      .map(([floorNumber, houses]) => ({\n        floorNumber,\n        houses: houses.sort((a, b) => {\n          // 排序邏輯：先按戶型排序，再按樓層排序\n          if (a.CHouseHold !== b.CHouseHold) {\n            return a.CHouseHold.localeCompare(b.CHouseHold);\n          }\n          return a.CFloor - b.CFloor;\n        }),\n        selected: false\n      }));\n      return {\n        name: buildingName,\n        floors,\n        selected: false\n      };\n    }).sort((a, b) => a.name.localeCompare(b.name));\n    // 更新棟別選項和可用樓層\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n    this.updateAvailableFloors();\n  }\n  // 新增：更新可用樓層\n  updateAvailableFloors() {\n    const floorsSet = new Set();\n    this.buildingGroups.forEach(building => {\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n        building.floors.forEach(floor => {\n          floorsSet.add(floor.floorNumber);\n        });\n      }\n    });\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n  }\n  // 修改：棟別選擇變更處理\n  onBuildingChange() {\n    // 重置選擇狀態\n    this.selectedHouses.forEach(house => house.selected = false);\n    this.selectedHouses = [];\n    this.selectAll = false;\n    // 重置分頁到第一頁\n    this.currentPage = 1;\n    // 重置樓層篩選\n    this.filterOptions.floorFilter = '';\n    // 更新可用樓層\n    this.updateAvailableFloors();\n    // 設定棟別篩選並執行搜尋\n    this.filterOptions.buildingFilter = this.selectedBuilding;\n    this.onSearch();\n  }\n  // 新增：取得過濾後的棟別資料\n  getFilteredBuildings() {\n    return this.buildingGroups.filter(building => {\n      // 棟別篩選\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n        return false;\n      }\n      // 關鍵字搜尋 (搜尋戶型)\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n        if (!hasMatchingHouse) {\n          return false;\n        }\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n        if (!hasMatchingStatus) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n        if (!hasMatchingFloor) {\n          return false;\n        }\n      }\n      return true;\n    }).map(building => {\n      // 對每個棟別，也要篩選其樓層和戶別\n      const filteredBuilding = {\n        ...building\n      };\n      filteredBuilding.floors = building.floors.filter(floor => {\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (floor.floorNumber !== floorNumber) {\n            return false;\n          }\n        }\n        // 檢查該樓層是否有符合條件的戶別\n        const hasValidHouses = floor.houses.some(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return hasValidHouses;\n      }).map(floor => {\n        // 篩選戶別\n        const filteredFloor = {\n          ...floor\n        };\n        filteredFloor.houses = floor.houses.filter(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return filteredFloor;\n      });\n      return filteredBuilding;\n    });\n  }\n  // 修復：檢查戶別是否符合狀態篩選\n  matchesStatusFilter(house) {\n    const status = this.getHouseStatus(house);\n    switch (this.filterOptions.statusFilter) {\n      case 'active':\n        return status === 'active';\n      case 'pending':\n        return status === 'pending';\n      case 'expired':\n        return status === 'expired';\n      case 'not-set':\n        return status === 'not-set';\n      case 'disabled':\n        return status === 'disabled';\n      default:\n        return true;\n      // 全部狀態\n    }\n  }\n  // 修復：取得戶別狀態\n  getHouseStatus(house) {\n    if (!house.CHouseId) {\n      return 'disabled';\n    }\n    // 檢查是否有設定開放時段\n    if (!house.CChangeStartDate || !house.CChangeEndDate || house.CChangeStartDate === '' || house.CChangeEndDate === '') {\n      return 'not-set';\n    }\n    try {\n      // 處理日期字串，支援多種格式\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      // 解析開始日期\n      let startDate;\n      if (house.CChangeStartDate.includes('T')) {\n        startDate = new Date(house.CChangeStartDate);\n      } else {\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\n      }\n      // 解析結束日期\n      let endDate;\n      if (house.CChangeEndDate.includes('T')) {\n        endDate = new Date(house.CChangeEndDate);\n      } else {\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\n      }\n      // 檢查日期有效性\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\n        console.warn('Invalid date format:', {\n          start: house.CChangeStartDate,\n          end: house.CChangeEndDate,\n          houseId: house.CHouseId\n        });\n        return 'not-set';\n      }\n      // 轉換為日期比較（不含時間）\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n      // 判斷狀態\n      if (today < startDateOnly) {\n        return 'pending'; // 待開放\n      } else if (today >= startDateOnly && today <= endDateOnly) {\n        return 'active'; // 進行中\n      } else {\n        return 'expired'; // 已過期\n      }\n    } catch (error) {\n      console.error('Error parsing dates:', error, house);\n      return 'not-set';\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  // 修改：開啟批次設定對話框\n  openBatchSetting(building) {\n    this.selectedBuildingForBatch = building || null;\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\n    const hasSelectedHouses = this.selectedHouses.length > 0;\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: !hasSelectedHouses && !building,\n      selectedBuildings: building ? [building.name] : [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    // 重置選擇狀態\n    if (building) {\n      building.floors.forEach(floor => {\n        floor.selected = false;\n        floor.houses.forEach(house => house.selected = false);\n      });\n    }\n    // 開啟對話框\n    this.dialogService.open(this.batchSettingDialog);\n  }\n  // 新增：樓層選擇變更處理\n  onFloorSelectionChange(floor) {\n    if (floor.selected) {\n      floor.houses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = true;\n        }\n      });\n    } else {\n      floor.houses.forEach(house => house.selected = false);\n    }\n  }\n  // 修改：批次提交\n  onBatchSubmit(ref) {\n    // 驗證批次設定\n    this.valid.clear();\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 收集要更新的房屋\n    const housesToUpdate = [];\n    if (this.batchSettings.applyToAll) {\n      // 全部戶別\n      this.flattenedHouses.forEach(house => {\n        if (house.CHouseId) {\n          housesToUpdate.push({\n            CHouseId: house.CHouseId,\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n          });\n        }\n      });\n    } else {\n      // 使用已選擇的戶別\n      if (this.selectedHouses.length > 0) {\n        this.selectedHouses.forEach(house => {\n          if (house.CHouseId) {\n            housesToUpdate.push({\n              CHouseId: house.CHouseId,\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n            });\n          }\n        });\n      } else if (this.selectedBuildingForBatch) {\n        // 如果沒有選擇的戶別，使用舊的邏輯\n        this.selectedBuildingForBatch.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.selected && house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      }\n    }\n    if (housesToUpdate.length === 0) {\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\n      return;\n    }\n    // 調用API進行批次更新\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: housesToUpdate\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n        // 清除選擇狀態\n        this.selectedHouses.forEach(house => house.selected = false);\n        this.selectedHouses = [];\n        this.selectAll = false;\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  // 新增：取得狀態樣式類別\n  getStatusClass(house) {\n    const status = this.getHouseStatus(house);\n    return `status-${status}`;\n  }\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n  openHouseDialog(house) {\n    if (house.CHouseId) {\n      // 使用現有的openModel方法\n      this.openModel(this.dialog, house);\n    }\n  }\n  // 新增：建立扁平化房屋資料\n  buildFlattenedHouses(data) {\n    this.flattenedHouses = [];\n    data.forEach(household => {\n      const houseType = household.CHouseHold || '';\n      household.CHouses?.forEach(house => {\n        this.flattenedHouses.push({\n          CHouseHold: houseType,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId || 0,\n          CFloor: house.CFloor || 0,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 初始化篩選和分頁\n    this.onSearch();\n    // 調試：輸出狀態統計\n    this.debugStatusCounts();\n  }\n  // 調試：輸出狀態統計\n  debugStatusCounts() {\n    const statusCounts = {\n      active: 0,\n      pending: 0,\n      expired: 0,\n      'not-set': 0,\n      disabled: 0\n    };\n    this.flattenedHouses.forEach(house => {\n      const status = this.getHouseStatus(house);\n      if (statusCounts.hasOwnProperty(status)) {\n        statusCounts[status]++;\n      }\n    });\n    console.log('狀態統計:', statusCounts);\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\n  }\n  // 修改：搜尋和篩選\n  onSearch() {\n    // 記錄篩選前的已選擇項目\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\n    this.filteredHouses = this.flattenedHouses.filter(house => {\n      // 關鍵字搜尋\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n          return false;\n        }\n      }\n      // 棟別篩選\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n        return false;\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        if (!this.matchesStatusFilter(house)) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        if (house.CFloor !== floorNumber) {\n          return false;\n        }\n      }\n      return true;\n    });\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\n    this.selectedHouses = this.filteredHouses.filter(house => previouslySelectedIds.includes(house.CHouseId));\n    // 更新扁平化資料中的選擇狀態\n    this.flattenedHouses.forEach(house => {\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\n    });\n    // 更新全選狀態\n    this.updateSelectAllState();\n    // 重新計算分頁\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：更新全選狀態\n  updateSelectAllState() {\n    if (this.paginatedHouses.length === 0) {\n      this.selectAll = false;\n    } else {\n      this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n    }\n  }\n  // 修改：更新分頁\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n    // 更新全選狀態\n    this.updateSelectAllState();\n  }\n  // 新增：頁面大小變更\n  onPageSizeChange() {\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：跳轉頁面\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.updatePagination();\n    }\n  }\n  // 新增：取得可見頁碼\n  getVisiblePages() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 新增：全選/取消全選\n  onSelectAllChange() {\n    this.paginatedHouses.forEach(house => {\n      if (house.CHouseId) {\n        house.selected = this.selectAll;\n      }\n    });\n    this.updateSelectedHouses();\n  }\n  // 新增：單一選擇變更\n  onHouseSelectionChange() {\n    this.updateSelectedHouses();\n    this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n  }\n  // 新增：更新已選擇房屋列表\n  updateSelectedHouses() {\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n  }\n  // 新增：排序\n  sort(field) {\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    this.filteredHouses.sort((a, b) => {\n      let aValue = a[field];\n      let bValue = b[field];\n      // 處理日期排序\n      if (field.includes('Date')) {\n        aValue = aValue ? new Date(aValue).getTime() : 0;\n        bValue = bValue ? new Date(bValue).getTime() : 0;\n      }\n      // 處理數字排序\n      if (field === 'CFloor') {\n        aValue = Number(aValue) || 0;\n        bValue = Number(bValue) || 0;\n      }\n      // 處理字串排序\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (aValue < bValue) {\n        return this.sortDirection === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return this.sortDirection === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    this.updatePagination();\n  }\n  // 新增：TrackBy函數\n  trackByHouseId(_index, house) {\n    return house.CHouseId;\n  }\n  // 新增：取得狀態文字\n  getStatusText(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return '進行中';\n      case 'pending':\n        return '待開放';\n      case 'expired':\n        return '已過期';\n      case 'not-set':\n        return '未設定';\n      case 'disabled':\n        return '已停用';\n      default:\n        return '未知';\n    }\n  }\n  // 新增：匯出資料\n  exportData() {\n    // 實現匯出功能\n    const csvContent = this.generateCSV();\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  // 新增：產生CSV內容\n  generateCSV() {\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\n    const rows = this.filteredHouses.map(house => [house.CHouseHold, house.CBuildingName, `${house.CFloor}F`, house.CChangeStartDate || '未設定', house.CChangeEndDate || '未設定', this.getStatusText(house)]);\n    const csvContent = [headers, ...rows].map(row => row.map(cell => `\"${cell}\"`).join(',')).join('\\n');\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\n  }\n  static {\n    this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingTimePeriodComponent,\n      selectors: [[\"ngx-setting-time-period\"]],\n      viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 66,\n      vars: 15,\n      consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"batchSettingDialog\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"batchStartDate\", \"\"], [\"batchEndDate\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"page-header-optimized\"], [1, \"page-title-section\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"title-group\"], [1, \"page-title\", \"mb-1\"], [1, \"fas\", \"fa-clock\", \"me-2\"], [1, \"page-subtitle\", \"text-muted\", \"mb-0\"], [1, \"help-section\"], [\"type\", \"button\", \"data-bs-toggle\", \"tooltip\", \"title\", \"\\u8A2D\\u5B9A\\u5404\\u6236\\u5225\\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5\\uFF0C\\u63A7\\u5236\\u5BA2\\u6236\\u9078\\u6A23\\u6B0A\\u9650\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\"], [1, \"fas\", \"fa-question-circle\"], [1, \"compact-filters\"], [1, \"row\", \"g-3\", \"align-items-end\"], [1, \"col-lg-3\", \"col-md-4\"], [1, \"form-label\", \"small\", \"fw-medium\"], [1, \"text-danger\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-2\", \"col-md-3\"], [\"placeholder\", \"\\u5168\\u90E8\\u68DF\\u5225\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"\"], [1, \"col-lg-4\", \"col-md-5\"], [1, \"date-range-group\"], [\"size\", \"small\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u958B\\u59CB\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [1, \"date-separator\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u7D50\\u675F\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-lg-3\", \"col-md-12\"], [1, \"action-buttons\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-search\", \"me-1\"], [\"title\", \"\\u91CD\\u7F6E\\u7BE9\\u9078\\u689D\\u4EF6\", 1, \"btn\", \"btn-outline-secondary\", \"ms-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-undo\"], [\"class\", \"advanced-filters-toggle\", 4, \"ngIf\"], [\"class\", \"advanced-filters-panel\", 4, \"ngIf\"], [\"class\", \"table-view-enhanced mt-4\", 4, \"ngIf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [3, \"value\"], [1, \"advanced-filters-toggle\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\", \"btn-sm\", \"p-0\", 3, \"click\"], [1, \"fas\"], [1, \"ms-1\"], [\"class\", \"badge bg-primary ms-2\", 4, \"ngIf\"], [1, \"badge\", \"bg-primary\", \"ms-2\"], [1, \"advanced-filters-panel\"], [1, \"row\", \"g-3\", \"align-items-center\"], [\"nbPrefix\", \"\", \"icon\", \"search-outline\"], [\"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u578B\\u3001\\u68DF\\u5225...\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u72C0\\u614B\\u7BE9\\u9078\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"active\"], [\"value\", \"pending\"], [\"value\", \"expired\"], [\"value\", \"not-set\"], [\"value\", \"disabled\"], [\"placeholder\", \"\\u6A13\\u5C64\\u7BE9\\u9078\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-lg-2\", \"col-md-2\"], [\"placeholder\", \"\\u986F\\u793A\\u7B46\\u6578\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"filter-actions\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"table-view-enhanced\", \"mt-4\"], [1, \"data-summary-bar\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"summary-info\"], [1, \"total-count\"], [1, \"fas\", \"fa-database\", \"me-1\"], [\"class\", \"selected-count\", 4, \"ngIf\"], [1, \"quick-filters\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [1, \"fas\", \"fa-play-circle\", \"me-1\"], [1, \"fas\", \"fa-clock\", \"me-1\"], [1, \"fas\", \"fa-times-circle\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-not-set\", 3, \"click\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-1\"], [1, \"enhanced-toolbar\"], [1, \"batch-operations\"], [1, \"selection-controls\"], [1, \"select-all-checkbox\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fw-medium\"], [\"class\", \"batch-actions ms-3\", 4, \"ngIf\"], [1, \"table-controls\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\"], [\"title\", \"\\u532F\\u51FA\\u8CC7\\u6599\", 1, \"btn\", \"btn-outline-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\", \"me-1\"], [1, \"pagination-summary\"], [1, \"text-muted\"], [1, \"enhanced-table-container\"], [1, \"table\", \"table-hover\", \"enhanced-table\"], [1, \"enhanced-table-header\"], [\"width\", \"50\", 1, \"text-center\"], [3, \"ngModelChange\", \"ngModel\"], [\"width\", \"120\", 1, \"sortable\", 3, \"click\"], [1, \"header-content\"], [1, \"fas\", \"fa-sort\", \"sort-icon\"], [\"width\", \"100\", 1, \"sortable\", 3, \"click\"], [\"width\", \"80\", 1, \"sortable\", \"text-center\", 3, \"click\"], [\"width\", \"140\", 1, \"sortable\", 3, \"click\"], [\"width\", \"120\", 1, \"text-center\"], [\"width\", \"100\", 1, \"text-center\"], [3, \"table-row-selected\", \"table-row-disabled\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"enhanced-pagination-container\", 4, \"ngIf\"], [1, \"selected-count\"], [1, \"fas\", \"fa-check-square\", \"me-1\", \"text-primary\"], [1, \"text-primary\"], [1, \"batch-actions\", \"ms-3\"], [\"title\", \"\\u6279\\u6B21\\u8A2D\\u5B9A\\u9078\\u4E2D\\u7684\\u6236\\u5225\\u958B\\u653E\\u6642\\u6BB5\", 1, \"btn\", \"btn-warning\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-cogs\", \"me-1\"], [1, \"badge\", \"bg-light\", \"text-dark\", \"ms-1\"], [\"title\", \"\\u6E05\\u9664\\u9078\\u64C7\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"ms-2\", 3, \"click\"], [1, \"text-center\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"house-info\"], [1, \"house-name\", \"fw-medium\"], [\"class\", \"text-muted d-block\", 4, \"ngIf\"], [1, \"building-name\"], [1, \"floor-badge\"], [1, \"date-info\"], [\"class\", \"date-display\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"status-display\"], [1, \"enhanced-status-badge\"], [1, \"status-icon\"], [1, \"status-text\"], [\"title\", \"\\u7DE8\\u8F2F\\u6642\\u6BB5\\u8A2D\\u5B9A\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-edit\"], [1, \"text-muted\", \"d-block\"], [1, \"date-display\"], [1, \"fas\", \"fa-calendar\", \"me-1\", \"text-success\"], [1, \"fas\", \"fa-minus\", \"me-1\"], [1, \"fas\", \"fa-calendar\", \"me-1\", \"text-danger\"], [1, \"enhanced-pagination-container\"], [1, \"pagination-info-detailed\"], [1, \"ms-2\"], [1, \"pagination-nav\"], [1, \"pagination\", \"pagination-sm\", \"mb-0\"], [1, \"page-item\"], [\"title\", \"\\u7B2C\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [\"title\", \"\\u4E0A\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"\\u4E0B\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-right\"], [\"title\", \"\\u6700\\u5F8C\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"quick-jump\"], [1, \"input-group\", \"input-group-sm\", 2, \"width\", \"120px\"], [\"type\", \"number\", \"placeholder\", \"\\u9801\\u78BC\", 1, \"form-control\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"min\", \"max\"], [\"type\", \"button\", \"title\", \"\\u8DF3\\u8F49\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"mb-3\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"mt-2\"], [2, \"width\", \"600px\", \"max-height\", \"90vh\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"form-group\"], [1, \"text-red-600\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-fill\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"mx-2\"], [1, \"selection-options\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"alert\", \"alert-info\"], [1, \"selected-houses-preview\"], [\"class\", \"badge badge-primary mr-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"mr-1\", \"mb-1\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"floor-selection\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-selection\"], [\"class\", \"house-selection ml-4\", 4, \"ngIf\"], [1, \"house-selection\", \"ml-4\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n      template: function SettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 9)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 10)(5, \"div\", 11)(6, \"div\", 12)(7, \"div\", 13)(8, \"h5\", 14);\n          i0.ɵɵelement(9, \"i\", 15);\n          i0.ɵɵtext(10, \"\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5\\u8A2D\\u5B9A \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 16);\n          i0.ɵɵtext(12, \"\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u9593\\u7BC4\\u570D\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 17)(14, \"button\", 18);\n          i0.ɵɵelement(15, \"i\", 19);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(16, \"div\", 20)(17, \"div\", 21)(18, \"div\", 22)(19, \"label\", 23);\n          i0.ɵɵtext(20, \"\\u5EFA\\u6848 \");\n          i0.ɵɵelementStart(21, \"span\", 24);\n          i0.ɵɵtext(22, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"nb-select\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildCaseChange());\n          });\n          i0.ɵɵtemplate(24, SettingTimePeriodComponent_nb_option_24_Template, 2, 2, \"nb-option\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 27)(26, \"label\", 23);\n          i0.ɵɵtext(27, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"nb-select\", 28);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_28_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildingChange());\n          });\n          i0.ɵɵelementStart(29, \"nb-option\", 29);\n          i0.ɵɵtext(30, \"\\u5168\\u90E8\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, SettingTimePeriodComponent_nb_option_31_Template, 2, 2, \"nb-option\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 30)(33, \"label\", 23);\n          i0.ɵɵtext(34, \"\\u958B\\u653E\\u65E5\\u671F\\u7BC4\\u570D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 31)(36, \"nb-form-field\", 32);\n          i0.ɵɵelement(37, \"nb-icon\", 33);\n          i0.ɵɵelementStart(38, \"input\", 34);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(39, \"nb-datepicker\", 35, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"span\", 36);\n          i0.ɵɵtext(42, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"nb-form-field\", 32);\n          i0.ɵɵelement(44, \"nb-icon\", 33);\n          i0.ɵɵelementStart(45, \"input\", 37);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_45_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"nb-datepicker\", 35, 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 38)(49, \"div\", 39)(50, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_50_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵelement(51, \"i\", 41);\n          i0.ɵɵtext(52, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"button\", 42);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_53_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetFilters());\n          });\n          i0.ɵɵelement(54, \"i\", 43);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(55, SettingTimePeriodComponent_div_55_Template, 6, 5, \"div\", 44)(56, SettingTimePeriodComponent_div_56_Template, 38, 11, \"div\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(57, SettingTimePeriodComponent_div_57_Template, 76, 48, \"div\", 46)(58, SettingTimePeriodComponent_div_58_Template, 7, 0, \"div\", 47)(59, SettingTimePeriodComponent_div_59_Template, 9, 0, \"div\", 47);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(60, SettingTimePeriodComponent_ng_template_60_Template, 36, 9, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(62, SettingTimePeriodComponent_ng_template_62_Template, 4, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(64, SettingTimePeriodComponent_ng_template_64_Template, 29, 6, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const StartDate_r31 = i0.ɵɵreference(40);\n          const EndDate_r32 = i0.ɵɵreference(47);\n          i0.ɵɵadvance(23);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingOptions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", StartDate_r31);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", EndDate_r32);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAdvancedFilters && ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length === 0 && ctx.houseChangeDates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.MinValidator, i9.MaxValidator, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n.page-header-optimized[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n  border-radius: 0.75rem;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.1);\\n  margin-bottom: 1.5rem;\\n  overflow: hidden;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%] {\\n  padding: 1.25rem 1.5rem 1rem;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .title-group[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n  font-size: 1.25rem;\\n  margin-bottom: 0.25rem;\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .title-group[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n  font-size: 1.1rem;\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .title-group[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n  line-height: 1.4;\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .help-section[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  width: 32px;\\n  height: 32px;\\n  padding: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .help-section[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: #B8A676;\\n  transform: scale(1.05);\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .help-section[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #B8A676;\\n}\\n\\n.compact-filters[_ngcontent-%COMP%] {\\n  padding: 1.25rem 1.5rem;\\n  background: linear-gradient(to bottom, #fafafa 0%, #f8f9fa 100%);\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.15);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin-bottom: 0.375rem;\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545 !important;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0 0.25rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  justify-content: flex-end;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.3s ease;\\n  padding: 0.5rem 1rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #A69660 0%, #95854A 100%);\\n  border-color: #A69660;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(184, 166, 118, 0.3);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #6c757d;\\n  color: #6c757d;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n  transform: translateY(-1px);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.advanced-filters-toggle[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1.5rem 0;\\n}\\n.advanced-filters-toggle[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n  text-decoration: none;\\n  font-weight: 500;\\n  font-size: 0.875rem;\\n  transition: all 0.3s ease;\\n}\\n.advanced-filters-toggle[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover {\\n  color: #a59056;\\n  text-decoration: none;\\n}\\n.advanced-filters-toggle[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.advanced-filters-toggle[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  padding: 0.2rem 0.4rem;\\n}\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 1.25rem;\\n  background-color: #ffffff;\\n  border-top: 1px solid rgba(184, 166, 118, 0.1);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .btn-outline-danger[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.advanced-filters[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 1.5rem;\\n  background-color: #ffffff;\\n  border-top: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]   .text-primary[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #B8A676;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%], \\n.advanced-filters[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-select.ng-touched.ng-valid[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.5);\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.25rem;\\n  border-radius: 0.5rem 0.5rem 0 0;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-bottom: none;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-warning[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-danger[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%] {\\n  background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);\\n  padding: 1rem 1.25rem;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-top: none;\\n  border-bottom: none;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  font-size: 0.875rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #D4B96A 0%, #C4A85A 100%);\\n  border-color: #D4B96A;\\n  color: #5a4a2a;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #C4A85A 0%, #B4984A 100%);\\n  border-color: #C4A85A;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  color: #B8A676;\\n  font-weight: 600;\\n  font-size: 0.7rem;\\n  padding: 0.2rem 0.4rem;\\n  border-radius: 0.25rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .pagination-summary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d;\\n  margin-left: 1rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(184, 166, 118, 0.2);\\n  border-radius: 0 0 0.5rem 0.5rem;\\n  overflow: hidden;\\n  box-shadow: 0 4px 8px rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  font-size: 0.875rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F8F6F0 0%, #F0EDE5 100%);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  border-bottom: 2px solid rgba(184, 166, 118, 0.2);\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .sort-icon[_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n  color: #6c757d;\\n  transition: opacity 0.3s ease;\\n  margin-left: 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover   .sort-icon[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  color: #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-selected[_ngcontent-%COMP%] {\\n  background-color: rgba(184, 166, 118, 0.15);\\n  border-left: 3px solid #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  background-color: #f8f9fa;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  vertical-align: middle;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .house-info[_ngcontent-%COMP%]   .house-name[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .house-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 500;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .floor-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);\\n  color: #495057;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 0.85rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 1rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.375rem;\\n  font-size: 0.7rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border-radius: 0.375rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: scale(1.1);\\n  box-shadow: 0 3px 6px rgba(0, 123, 255, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #E8F5E8 0%, #D4EDDA 100%);\\n  color: #2D5A2D;\\n  border-color: rgba(45, 90, 45, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-active[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-pending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FFF8E1 0%, #FFF3CD 100%);\\n  color: #8B6914;\\n  border-color: rgba(139, 105, 20, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-pending[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-expired[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FFEBEE 0%, #F8D7DA 100%);\\n  color: #8B2635;\\n  border-color: rgba(139, 38, 53, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-expired[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-not-set[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F5F5F5 0%, #E2E3E5 100%);\\n  color: #5A5A5A;\\n  border-color: rgba(90, 90, 90, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-not-set[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-disabled[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FAFAFA 0%, #F8F9FA 100%);\\n  color: #8A8A8A;\\n  border-color: rgba(138, 138, 138, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-disabled[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);\\n  padding: 1.25rem;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-top: none;\\n  border-radius: 0 0 0.5rem 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-info-detailed[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-info-detailed[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%] {\\n  margin: 0 0.125rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #495057;\\n  border-color: rgba(184, 166, 118, 0.3);\\n  transition: all 0.3s ease;\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  font-weight: 500;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: rgba(184, 166, 118, 0.5);\\n  color: #B8A676;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(184, 166, 118, 0.2);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n  box-shadow: 0 3px 6px rgba(184, 166, 118, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n  background-color: #fff;\\n  border-color: rgba(184, 166, 118, 0.2);\\n  cursor: not-allowed;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.3);\\n  font-size: 0.8rem;\\n  text-align: center;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.3);\\n  color: #495057;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n.search-enhanced[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #5a5a5a;\\n  border-color: rgba(184, 166, 118, 0.3);\\n  transition: all 0.2s ease;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: rgba(184, 166, 118, 0.5);\\n  color: #B8A676;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #aaa;\\n  background-color: #fff;\\n  border-color: rgba(184, 166, 118, 0.2);\\n}\\n\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  padding-left: 1rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\\n  gap: 0.25rem;\\n  margin-top: 0.5rem;\\n  padding: 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  margin-bottom: 1rem;\\n  border: 1px solid transparent;\\n  border-radius: 0.375rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%] {\\n  color: #0c5460;\\n  background-color: #d1ecf1;\\n  border-color: #bee5eb;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  font-weight: 600;\\n}\\n\\n@media (max-width: 992px) {\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n    justify-content: center;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    border-radius: 0.375rem 0.375rem 0 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    margin-top: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.75rem;\\n    text-align: left !important;\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .flex-fill[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .mx-2[_ngcontent-%COMP%] {\\n    margin: 0.25rem 0 !important;\\n    text-align: center;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n    margin-bottom: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 0.5rem;\\n  }\\n  .search-enhanced[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0.25rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(3) {\\n    display: none;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .integrated-header[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n    border-radius: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    border-radius: 0.25rem 0.25rem 0 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    margin-right: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n    display: none;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n    margin-bottom: 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .query-section[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(4) {\\n    display: none;\\n  }\\n}\\nnb-card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #5a5a5a;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n  border-color: #B8A676;\\n  transition: all 0.3s ease;\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(184, 166, 118, 0.3);\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-color: #dee2e6;\\n  color: #6c757d;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  color: #5a5a5a;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  margin-right: 0.5rem;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n.status-indicator.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);\\n}\\n.status-indicator.pending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #D4B96A 0%, #C4A85A 100%);\\n}\\n.status-indicator.expired[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F44336 0%, #E53935 100%);\\n}\\n.status-indicator.not-set[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9E9E9E 0%, #757575 100%);\\n}\\n.status-indicator.disabled[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #E0E0E0 0%, #BDBDBD 100%);\\n}\\n\\n  nb-select.appearance-outline .select-button {\\n  border-color: rgba(184, 166, 118, 0.3);\\n}\\n  nb-select.appearance-outline .select-button:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n  nb-form-field.appearance-outline .form-control {\\n  border-color: rgba(184, 166, 118, 0.3);\\n}\\n  nb-form-field.appearance-outline .form-control:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n  nb-checkbox .customised-control-input:checked ~ .customised-control-indicator {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n}\\n  nb-calendar-day-cell.selected {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      data: {\n        animation: [trigger('slideInOut', [transition(':enter', [style({\n          opacity: 0,\n          transform: 'translateY(-10px)'\n        }), animate('300ms ease-out', style({\n          opacity: 1,\n          transform: 'translateY(0)'\n        }))]), transition(':leave', [animate('300ms ease-in', style({\n          opacity: 0,\n          transform: 'translateY(-10px)'\n        }))])])]\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "trigger", "style", "transition", "animate", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "building_r3", "ɵɵtextInterpolate", "ctx_r4", "getActiveFiltersCount", "ɵɵlistener", "SettingTimePeriodComponent_div_55_Template_button_click_1_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "toggleAdvancedFilters", "ɵɵelement", "ɵɵtemplate", "SettingTimePeriodComponent_div_55_span_5_Template", "ɵɵclassProp", "showAdvancedFilters", "hasActiveFilters", "floor_r7", "SettingTimePeriodComponent_div_56_button_37_Template_button_click_0_listener", "_r8", "clearAllFilters", "ɵɵtwoWayListener", "SettingTimePeriodComponent_div_56_Template_input_ngModelChange_5_listener", "$event", "_r6", "ɵɵtwoWayBindingSet", "filterOptions", "searchKeyword", "onSearch", "SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_7_listener", "statusFilter", "SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_7_listener", "SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_21_listener", "floorFilter", "SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_21_listener", "SettingTimePeriodComponent_div_56_nb_option_24_Template", "SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_26_listener", "pageSize", "SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_26_listener", "onPageSizeChange", "SettingTimePeriodComponent_div_56_button_37_Template", "undefined", "ɵɵtwoWayProperty", "availableFloors", "selectedHouses", "length", "SettingTimePeriodComponent_div_57_div_32_Template_button_click_1_listener", "_r10", "openBatchSetting", "SettingTimePeriodComponent_div_57_div_32_Template_button_click_6_listener", "clearSelection", "ɵɵpipeBind2", "house_r12", "CChangeStartDate", "CChangeEndDate", "SettingTimePeriodComponent_div_57_tr_74_Template_nb_checkbox_ngModelChange_2_listener", "_r11", "$implicit", "selected", "onHouseSelectionChange", "SettingTimePeriodComponent_div_57_tr_74_small_7_Template", "SettingTimePeriodComponent_div_57_tr_74_span_16_Template", "SettingTimePeriodComponent_div_57_tr_74_span_17_Template", "SettingTimePeriodComponent_div_57_tr_74_span_20_Template", "SettingTimePeriodComponent_div_57_tr_74_span_21_Template", "SettingTimePeriodComponent_div_57_tr_74_Template_button_click_30_listener", "dialog_r13", "ɵɵreference", "openModel", "CHouseId", "CHouseHold", "CBuildingName", "CFloor", "ɵɵclassMap", "getStatusClass", "getStatusIcon", "getStatusText", "SettingTimePeriodComponent_div_57_div_75_li_21_Template_button_click_1_listener", "page_r16", "_r15", "goToPage", "currentPage", "SettingTimePeriodComponent_div_57_div_75_Template_button_click_16_listener", "_r14", "SettingTimePeriodComponent_div_57_div_75_Template_button_click_19_listener", "SettingTimePeriodComponent_div_57_div_75_li_21_Template", "SettingTimePeriodComponent_div_57_div_75_Template_button_click_23_listener", "SettingTimePeriodComponent_div_57_div_75_Template_button_click_26_listener", "totalPages", "SettingTimePeriodComponent_div_57_div_75_Template_input_ngModelChange_30_listener", "jumpToPage", "SettingTimePeriodComponent_div_57_div_75_Template_input_keyup_enter_30_listener", "jumpToPageAction", "SettingTimePeriodComponent_div_57_div_75_Template_button_click_31_listener", "ɵɵtextInterpolate3", "Math", "min", "filteredHouses", "getVisiblePages", "SettingTimePeriodComponent_div_57_span_10_Template", "SettingTimePeriodComponent_div_57_Template_button_click_13_listener", "_r9", "setQuickFilter", "SettingTimePeriodComponent_div_57_Template_button_click_16_listener", "SettingTimePeriodComponent_div_57_Template_button_click_19_listener", "SettingTimePeriodComponent_div_57_Template_button_click_22_listener", "SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_29_listener", "selectAll", "onSelectAllChange", "SettingTimePeriodComponent_div_57_div_32_Template", "SettingTimePeriodComponent_div_57_Template_button_click_35_listener", "exportData", "SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_46_listener", "SettingTimePeriodComponent_div_57_Template_th_click_47_listener", "sort", "SettingTimePeriodComponent_div_57_Template_th_click_51_listener", "SettingTimePeriodComponent_div_57_Template_th_click_55_listener", "SettingTimePeriodComponent_div_57_Template_th_click_59_listener", "SettingTimePeriodComponent_div_57_Template_th_click_63_listener", "SettingTimePeriodComponent_div_57_tr_74_Template", "SettingTimePeriodComponent_div_57_div_75_Template", "sortField", "sortDirection", "paginatedHouses", "trackByHouseId", "selectedBuildingForBatch", "name", "house_r18", "SettingTimePeriodComponent_ng_template_60_div_28_span_4_Template", "SettingTimePeriodComponent_ng_template_60_div_28_span_5_Template", "slice", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener", "house_r23", "_r22", "ɵɵtextInterpolate2", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_nb_checkbox_1_Template", "floor_r21", "houses", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener", "_r20", "onFloorSelectionChange", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_Template", "floorNumber", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template", "floors", "SettingTimePeriodComponent_ng_template_60_div_29_Template_nb_checkbox_ngModelChange_1_listener", "_r19", "batchSettings", "applyToAll", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_Template", "flattenedHouses", "SettingTimePeriodComponent_ng_template_60_span_3_Template", "SettingTimePeriodComponent_ng_template_60_span_4_Template", "SettingTimePeriodComponent_ng_template_60_Template_input_ngModelChange_14_listener", "_r17", "startDate", "SettingTimePeriodComponent_ng_template_60_Template_input_ngModelChange_21_listener", "endDate", "SettingTimePeriodComponent_ng_template_60_div_28_Template", "SettingTimePeriodComponent_ng_template_60_div_29_Template", "SettingTimePeriodComponent_ng_template_60_Template_button_click_31_listener", "ref_r24", "dialogRef", "onClose", "SettingTimePeriodComponent_ng_template_60_Template_button_click_33_listener", "onBatchSubmit", "SettingTimePeriodComponent_ng_template_60_span_35_Template", "batchStartDate_r25", "batchEndDate_r26", "SettingTimePeriodComponent_ng_template_64_Template_input_ngModelChange_14_listener", "_r27", "selectedHouseChangeDate", "SettingTimePeriodComponent_ng_template_64_Template_input_ngModelChange_21_listener", "SettingTimePeriodComponent_ng_template_64_Template_button_click_25_listener", "ref_r28", "SettingTimePeriodComponent_ng_template_64_Template_button_click_27_listener", "onSubmit", "changeStartDate_r29", "changeEndDate_r30", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "buildingGroups", "buildingOptions", "selectedBuilding", "buildingFilter", "selectedBuildings", "selectedFloors", "loading", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "searchQuery", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "count", "resetFilters", "status", "for<PERSON>ach", "house", "getHouseStatus", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "validation", "errorMessages", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "household", "CHouses", "floor", "push", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "onBuildCaseChange", "resetAllStates", "houseChangeDates", "convertedHouseArray", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "buildBuildingGroups", "buildFlattenedHouses", "data", "buildingMap", "Map", "houseType", "buildingName", "has", "set", "floorMap", "get", "localeCompare", "bg", "updateAvailableFloors", "building", "onBuildingChange", "getFilteredBuildings", "filter", "keyword", "toLowerCase", "hasMatchingHouse", "some", "includes", "hasMatchingStatus", "matchesStatusFilter", "parseInt", "hasMatchingFloor", "filteredBuilding", "hasValidHouses", "filteredFloor", "now", "today", "getFullYear", "getMonth", "getDate", "isNaN", "getTime", "console", "warn", "start", "end", "houseId", "startDateOnly", "endDateOnly", "error", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "hasSelectedHouses", "batchSettingDialog", "housesToUpdate", "openHouseDialog", "dialog", "debugStatusCounts", "statusCounts", "active", "pending", "expired", "disabled", "hasOwnProperty", "log", "toISOString", "split", "previouslySelectedIds", "updateSelectAllState", "updatePagination", "every", "ceil", "startIndex", "endIndex", "page", "pages", "maxVisible", "max", "i", "updateSelectedHouses", "field", "aValue", "bValue", "Number", "_index", "csv<PERSON><PERSON>nt", "generateCSV", "blob", "Blob", "type", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "visibility", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "rows", "row", "cell", "join", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "Router", "i7", "EventService", "selectors", "viewQuery", "SettingTimePeriodComponent_Query", "rf", "ctx", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingTimePeriodComponent_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_23_listener", "_r1", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON><PERSON>_23_listener", "SettingTimePeriodComponent_nb_option_24_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_28_listener", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_28_listener", "SettingTimePeriodComponent_nb_option_31_Template", "SettingTimePeriodComponent_Template_input_ngModelChange_38_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_45_listener", "SettingTimePeriodComponent_Template_button_click_50_listener", "SettingTimePeriodComponent_Template_button_click_53_listener", "SettingTimePeriodComponent_div_55_Template", "SettingTimePeriodComponent_div_56_Template", "SettingTimePeriodComponent_div_57_Template", "SettingTimePeriodComponent_div_58_Template", "SettingTimePeriodComponent_div_59_Template", "SettingTimePeriodComponent_ng_template_60_Template", "ɵɵtemplateRefExtractor", "SettingTimePeriodComponent_ng_template_62_Template", "SettingTimePeriodComponent_ng_template_64_Template", "StartDate_r31", "EndDate_r32", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MinValidator", "MaxValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles", "animation", "opacity", "transform"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport { trigger, state, style, transition, animate } from '@angular/animations';\r\n\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string; // 戶型\r\n  CBuildingName: string; // 棟別\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n  animations: [\r\n    trigger('slideInOut', [\r\n      transition(':enter', [\r\n        style({ opacity: 0, transform: 'translateY(-10px)' }),\r\n        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))\r\n      ]),\r\n      transition(':leave', [\r\n        animate('300ms ease-in', style({ opacity: 0, transform: 'translateY(-10px)' }))\r\n      ])\r\n    ])\r\n  ]\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n\r\n  @ViewChild('batchSettingDialog') batchSettingDialog!: TemplateRef<any>;\r\n  @ViewChild('dialog') dialog!: TemplateRef<any>;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  // 新增的屬性\r\n  buildingGroups: BuildingGroup[] = [];\r\n  buildingOptions: string[] = [];\r\n  selectedBuilding: string = '';\r\n  availableFloors: number[] = [];\r\n\r\n  // 篩選和搜尋\r\n  filterOptions: FilterOptions = {\r\n    searchKeyword: '',\r\n    statusFilter: '',\r\n    floorFilter: '',\r\n    buildingFilter: ''\r\n  };\r\n\r\n  // 批次設定\r\n  batchSettings: BatchSettings = {\r\n    startDate: null,\r\n    endDate: null,\r\n    applyToAll: true,\r\n    selectedBuildings: [],\r\n    selectedFloors: [],\r\n    selectedHouses: []\r\n  };\r\n\r\n  selectedBuildingForBatch: BuildingGroup | null = null;\r\n\r\n  // 表格視圖相關屬性\r\n  flattenedHouses: ConvertedHouse[] = [];\r\n  filteredHouses: ConvertedHouse[] = [];\r\n  paginatedHouses: ConvertedHouse[] = [];\r\n  selectedHouses: ConvertedHouse[] = [];\r\n  selectAll: boolean = false;\r\n  loading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  override pageSize: number = 50;\r\n  totalPages: number = 1;\r\n\r\n  // 排序相關\r\n  sortField: string = '';\r\n  sortDirection: 'asc' | 'desc' = 'asc';\r\n\r\n  // 數學函數引用\r\n  Math = Math;\r\n\r\n  // 新增的UI控制屬性\r\n  showAdvancedFilters: boolean = false;\r\n  jumpToPage: number = 1;\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  // 新增的UI控制方法\r\n  toggleAdvancedFilters(): void {\r\n    this.showAdvancedFilters = !this.showAdvancedFilters;\r\n  }\r\n\r\n  hasActiveFilters(): boolean {\r\n    return !!(this.filterOptions.searchKeyword ||\r\n      this.filterOptions.statusFilter ||\r\n      this.filterOptions.floorFilter);\r\n  }\r\n\r\n  getActiveFiltersCount(): number {\r\n    let count = 0;\r\n    if (this.filterOptions.searchKeyword) count++;\r\n    if (this.filterOptions.statusFilter) count++;\r\n    if (this.filterOptions.floorFilter) count++;\r\n    return count;\r\n  }\r\n\r\n  resetFilters(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    };\r\n    this.selectedBuilding = '';\r\n    this.clearAllFilters();\r\n  }\r\n\r\n  clearAllFilters(): void {\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n    this.onSearch();\r\n  }\r\n\r\n  setQuickFilter(status: string): void {\r\n    if (this.filterOptions.statusFilter === status) {\r\n      this.filterOptions.statusFilter = '';\r\n    } else {\r\n      this.filterOptions.statusFilter = status;\r\n    }\r\n    this.onSearch();\r\n  }\r\n\r\n  clearSelection(): void {\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n    this.flattenedHouses.forEach(house => house.selected = false);\r\n  }\r\n\r\n  getStatusIcon(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    switch (status) {\r\n      case 'active': return 'fas fa-play-circle';\r\n      case 'pending': return 'fas fa-clock';\r\n      case 'expired': return 'fas fa-times-circle';\r\n      case 'not-set': return 'fas fa-question-circle';\r\n      case 'disabled': return 'fas fa-ban';\r\n      default: return 'fas fa-question-circle';\r\n    }\r\n  }\r\n\r\n  jumpToPageAction(): void {\r\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\r\n      this.goToPage(this.jumpToPage);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CBuildingName: '未分類',\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：建案變更處理\r\n  onBuildCaseChange() {\r\n    // 重置所有相關狀態\r\n    this.resetAllStates();\r\n    // 執行查詢\r\n    this.getHouseChangeDate();\r\n  }\r\n\r\n  // 新增：重置所有狀態\r\n  resetAllStates() {\r\n    // 重置數據\r\n    this.houseChangeDates = [];\r\n    this.convertedHouseArray = [];\r\n    this.buildingGroups = [];\r\n    this.flattenedHouses = [];\r\n    this.filteredHouses = [];\r\n    this.paginatedHouses = [];\r\n    this.selectedHouses = [];\r\n\r\n    // 重置篩選條件\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    this.selectAll = false;\r\n    this.selectedBuilding = '';\r\n\r\n    // 重置分頁\r\n    this.currentPage = 1;\r\n    this.totalPages = 1;\r\n\r\n    // 重置可用選項\r\n    this.buildingOptions = [];\r\n    this.availableFloors = [];\r\n\r\n    // 重置排序\r\n    this.sortField = '';\r\n    this.sortDirection = 'asc';\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    // 如果沒有選擇建案，直接返回\r\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      this.loading = false;\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n          // 新增：建立棟別分組資料\r\n          this.buildBuildingGroups(res.Entries)\r\n          // 新增：建立扁平化資料\r\n          this.buildFlattenedHouses(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\r\n  buildBuildingGroups(data: GetHouseChangeDateRes[]) {\r\n    const buildingMap = new Map<string, Map<number, ConvertedHouse[]>>();\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || ''; // 戶型\r\n\r\n      household.CHouses?.forEach(house => {\r\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\r\n        const floor = house.CFloor || 0;\r\n\r\n        if (!buildingMap.has(buildingName)) {\r\n          buildingMap.set(buildingName, new Map<number, ConvertedHouse[]>());\r\n        }\r\n\r\n        const floorMap = buildingMap.get(buildingName)!;\r\n\r\n        if (!floorMap.has(floor)) {\r\n          floorMap.set(floor, []);\r\n        }\r\n\r\n        floorMap.get(floor)!.push({\r\n          CHouseHold: houseType, // 戶型\r\n          CBuildingName: buildingName, // 棟別\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: floor,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 轉換為BuildingGroup格式\r\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\r\n      const floors: FloorGroup[] = Array.from(floorMap.entries())\r\n        .sort(([a], [b]) => b - a) // 樓層由高到低排序\r\n        .map(([floorNumber, houses]) => ({\r\n          floorNumber,\r\n          houses: houses.sort((a, b) => {\r\n            // 排序邏輯：先按戶型排序，再按樓層排序\r\n            if (a.CHouseHold !== b.CHouseHold) {\r\n              return a.CHouseHold.localeCompare(b.CHouseHold);\r\n            }\r\n            return a.CFloor - b.CFloor;\r\n          }),\r\n          selected: false\r\n        }));\r\n\r\n      return {\r\n        name: buildingName,\r\n        floors,\r\n        selected: false\r\n      };\r\n    }).sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    // 更新棟別選項和可用樓層\r\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\r\n    this.updateAvailableFloors();\r\n  }\r\n\r\n  // 新增：更新可用樓層\r\n  updateAvailableFloors() {\r\n    const floorsSet = new Set<number>();\r\n\r\n    this.buildingGroups.forEach(building => {\r\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\r\n        building.floors.forEach(floor => {\r\n          floorsSet.add(floor.floorNumber);\r\n        });\r\n      }\r\n    });\r\n\r\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\r\n  }\r\n\r\n  // 修改：棟別選擇變更處理\r\n  onBuildingChange() {\r\n    // 重置選擇狀態\r\n    this.selectedHouses.forEach(house => house.selected = false);\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n\r\n    // 重置分頁到第一頁\r\n    this.currentPage = 1;\r\n\r\n    // 重置樓層篩選\r\n    this.filterOptions.floorFilter = '';\r\n\r\n    // 更新可用樓層\r\n    this.updateAvailableFloors();\r\n\r\n    // 設定棟別篩選並執行搜尋\r\n    this.filterOptions.buildingFilter = this.selectedBuilding;\r\n    this.onSearch();\r\n  }\r\n\r\n\r\n\r\n  // 新增：取得過濾後的棟別資料\r\n  getFilteredBuildings(): BuildingGroup[] {\r\n    return this.buildingGroups.filter(building => {\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 關鍵字搜尋 (搜尋戶型)\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        const hasMatchingHouse = building.floors.some(floor =>\r\n          floor.houses.some(house =>\r\n            house.CHouseHold.toLowerCase().includes(keyword) ||\r\n            house.CBuildingName.toLowerCase().includes(keyword)\r\n          )\r\n        );\r\n        if (!hasMatchingHouse) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        const hasMatchingStatus = building.floors.some(floor =>\r\n          floor.houses.some(house => this.matchesStatusFilter(house))\r\n        );\r\n        if (!hasMatchingStatus) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        const hasMatchingFloor = building.floors.some(floor =>\r\n          floor.floorNumber === floorNumber\r\n        );\r\n        if (!hasMatchingFloor) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }).map(building => {\r\n      // 對每個棟別，也要篩選其樓層和戶別\r\n      const filteredBuilding = { ...building };\r\n      filteredBuilding.floors = building.floors.filter(floor => {\r\n        // 樓層篩選\r\n        if (this.filterOptions.floorFilter) {\r\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n          if (floor.floorNumber !== floorNumber) {\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 檢查該樓層是否有符合條件的戶別\r\n        const hasValidHouses = floor.houses.some(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return hasValidHouses;\r\n      }).map(floor => {\r\n        // 篩選戶別\r\n        const filteredFloor = { ...floor };\r\n        filteredFloor.houses = floor.houses.filter(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return filteredFloor;\r\n      });\r\n\r\n      return filteredBuilding;\r\n    });\r\n  }\r\n\r\n  // 修復：檢查戶別是否符合狀態篩選\r\n  private matchesStatusFilter(house: ConvertedHouse): boolean {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (this.filterOptions.statusFilter) {\r\n      case 'active':\r\n        return status === 'active';\r\n      case 'pending':\r\n        return status === 'pending';\r\n      case 'expired':\r\n        return status === 'expired';\r\n      case 'not-set':\r\n        return status === 'not-set';\r\n      case 'disabled':\r\n        return status === 'disabled';\r\n      default:\r\n        return true; // 全部狀態\r\n    }\r\n  }\r\n\r\n  // 修復：取得戶別狀態\r\n  private getHouseStatus(house: ConvertedHouse): string {\r\n    if (!house.CHouseId) {\r\n      return 'disabled';\r\n    }\r\n\r\n    // 檢查是否有設定開放時段\r\n    if (!house.CChangeStartDate || !house.CChangeEndDate ||\r\n      house.CChangeStartDate === '' || house.CChangeEndDate === '') {\r\n      return 'not-set';\r\n    }\r\n\r\n    try {\r\n      // 處理日期字串，支援多種格式\r\n      const now = new Date();\r\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n\r\n      // 解析開始日期\r\n      let startDate: Date;\r\n      if (house.CChangeStartDate.includes('T')) {\r\n        startDate = new Date(house.CChangeStartDate);\r\n      } else {\r\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\r\n      }\r\n\r\n      // 解析結束日期\r\n      let endDate: Date;\r\n      if (house.CChangeEndDate.includes('T')) {\r\n        endDate = new Date(house.CChangeEndDate);\r\n      } else {\r\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\r\n      }\r\n\r\n      // 檢查日期有效性\r\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\r\n        console.warn('Invalid date format:', {\r\n          start: house.CChangeStartDate,\r\n          end: house.CChangeEndDate,\r\n          houseId: house.CHouseId\r\n        });\r\n        return 'not-set';\r\n      }\r\n\r\n      // 轉換為日期比較（不含時間）\r\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\r\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\r\n\r\n      // 判斷狀態\r\n      if (today < startDateOnly) {\r\n        return 'pending'; // 待開放\r\n      } else if (today >= startDateOnly && today <= endDateOnly) {\r\n        return 'active'; // 進行中\r\n      } else {\r\n        return 'expired'; // 已過期\r\n      }\r\n    } catch (error) {\r\n      console.error('Error parsing dates:', error, house);\r\n      return 'not-set';\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n\r\n  // 修改：開啟批次設定對話框\r\n  openBatchSetting(building?: BuildingGroup) {\r\n    this.selectedBuildingForBatch = building || null;\r\n\r\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\r\n    const hasSelectedHouses = this.selectedHouses.length > 0;\r\n\r\n    this.batchSettings = {\r\n      startDate: null,\r\n      endDate: null,\r\n      applyToAll: !hasSelectedHouses && !building,\r\n      selectedBuildings: building ? [building.name] : [],\r\n      selectedFloors: [],\r\n      selectedHouses: []\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    if (building) {\r\n      building.floors.forEach(floor => {\r\n        floor.selected = false;\r\n        floor.houses.forEach(house => house.selected = false);\r\n      });\r\n    }\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(this.batchSettingDialog);\r\n  }\r\n\r\n  // 新增：樓層選擇變更處理\r\n  onFloorSelectionChange(floor: FloorGroup) {\r\n    if (floor.selected) {\r\n      floor.houses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          house.selected = true;\r\n        }\r\n      });\r\n    } else {\r\n      floor.houses.forEach(house => house.selected = false);\r\n    }\r\n  }\r\n\r\n  // 修改：批次提交\r\n  onBatchSubmit(ref: any) {\r\n    // 驗證批次設定\r\n    this.valid.clear();\r\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\r\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 收集要更新的房屋\r\n    const housesToUpdate: any[] = [];\r\n\r\n    if (this.batchSettings.applyToAll) {\r\n      // 全部戶別\r\n      this.flattenedHouses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          housesToUpdate.push({\r\n            CHouseId: house.CHouseId,\r\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // 使用已選擇的戶別\r\n      if (this.selectedHouses.length > 0) {\r\n        this.selectedHouses.forEach(house => {\r\n          if (house.CHouseId) {\r\n            housesToUpdate.push({\r\n              CHouseId: house.CHouseId,\r\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n            });\r\n          }\r\n        });\r\n      } else if (this.selectedBuildingForBatch) {\r\n        // 如果沒有選擇的戶別，使用舊的邏輯\r\n        this.selectedBuildingForBatch.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.selected && house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n\r\n    if (housesToUpdate.length === 0) {\r\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\r\n      return;\r\n    }\r\n\r\n    // 調用API進行批次更新\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: housesToUpdate\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\r\n        // 清除選擇狀態\r\n        this.selectedHouses.forEach(house => house.selected = false);\r\n        this.selectedHouses = [];\r\n        this.selectAll = false;\r\n        this.getHouseChangeDate();\r\n        ref.close();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：取得狀態樣式類別\r\n  getStatusClass(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    return `status-${status}`;\r\n  }\r\n\r\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\r\n  openHouseDialog(house: ConvertedHouse) {\r\n    if (house.CHouseId) {\r\n      // 使用現有的openModel方法\r\n      this.openModel(this.dialog, house);\r\n    }\r\n  }\r\n\r\n  // 新增：建立扁平化房屋資料\r\n  buildFlattenedHouses(data: GetHouseChangeDateRes[]) {\r\n    this.flattenedHouses = [];\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || '';\r\n\r\n      household.CHouses?.forEach(house => {\r\n        this.flattenedHouses.push({\r\n          CHouseHold: houseType,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: house.CFloor || 0,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 初始化篩選和分頁\r\n    this.onSearch();\r\n\r\n    // 調試：輸出狀態統計\r\n    this.debugStatusCounts();\r\n  }\r\n\r\n  // 調試：輸出狀態統計\r\n  private debugStatusCounts() {\r\n    const statusCounts = {\r\n      active: 0,\r\n      pending: 0,\r\n      expired: 0,\r\n      'not-set': 0,\r\n      disabled: 0\r\n    };\r\n\r\n    this.flattenedHouses.forEach(house => {\r\n      const status = this.getHouseStatus(house);\r\n      if (statusCounts.hasOwnProperty(status)) {\r\n        statusCounts[status as keyof typeof statusCounts]++;\r\n      }\r\n    });\r\n\r\n    console.log('狀態統計:', statusCounts);\r\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\r\n  }\r\n\r\n\r\n\r\n  // 修改：搜尋和篩選\r\n  onSearch() {\r\n    // 記錄篩選前的已選擇項目\r\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\r\n\r\n    this.filteredHouses = this.flattenedHouses.filter(house => {\r\n      // 關鍵字搜尋\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n          !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        if (!this.matchesStatusFilter(house)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        if (house.CFloor !== floorNumber) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    });\r\n\r\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\r\n    this.selectedHouses = this.filteredHouses.filter(house =>\r\n      previouslySelectedIds.includes(house.CHouseId)\r\n    );\r\n\r\n    // 更新扁平化資料中的選擇狀態\r\n    this.flattenedHouses.forEach(house => {\r\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\r\n    });\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n\r\n    // 重新計算分頁\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：更新全選狀態\r\n  private updateSelectAllState() {\r\n    if (this.paginatedHouses.length === 0) {\r\n      this.selectAll = false;\r\n    } else {\r\n      this.selectAll = this.paginatedHouses.every(house =>\r\n        !house.CHouseId || house.selected\r\n      );\r\n    }\r\n  }\r\n\r\n  // 修改：更新分頁\r\n  updatePagination() {\r\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\r\n    const startIndex = (this.currentPage - 1) * this.pageSize;\r\n    const endIndex = startIndex + this.pageSize;\r\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 新增：頁面大小變更\r\n  onPageSizeChange() {\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：跳轉頁面\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  // 新增：取得可見頁碼\r\n  getVisiblePages(): number[] {\r\n    const pages: number[] = [];\r\n    const maxVisible = 5;\r\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\r\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\r\n\r\n    if (end - start + 1 < maxVisible) {\r\n      start = Math.max(1, end - maxVisible + 1);\r\n    }\r\n\r\n    for (let i = start; i <= end; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 新增：全選/取消全選\r\n  onSelectAllChange() {\r\n    this.paginatedHouses.forEach(house => {\r\n      if (house.CHouseId) {\r\n        house.selected = this.selectAll;\r\n      }\r\n    });\r\n    this.updateSelectedHouses();\r\n  }\r\n\r\n  // 新增：單一選擇變更\r\n  onHouseSelectionChange() {\r\n    this.updateSelectedHouses();\r\n    this.selectAll = this.paginatedHouses.every(house =>\r\n      !house.CHouseId || house.selected\r\n    );\r\n  }\r\n\r\n  // 新增：更新已選擇房屋列表\r\n  updateSelectedHouses() {\r\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\r\n  }\r\n\r\n  // 新增：排序\r\n  sort(field: string) {\r\n    if (this.sortField === field) {\r\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortDirection = 'asc';\r\n    }\r\n\r\n    this.filteredHouses.sort((a, b) => {\r\n      let aValue = (a as any)[field];\r\n      let bValue = (b as any)[field];\r\n\r\n      // 處理日期排序\r\n      if (field.includes('Date')) {\r\n        aValue = aValue ? new Date(aValue).getTime() : 0;\r\n        bValue = bValue ? new Date(bValue).getTime() : 0;\r\n      }\r\n\r\n      // 處理數字排序\r\n      if (field === 'CFloor') {\r\n        aValue = Number(aValue) || 0;\r\n        bValue = Number(bValue) || 0;\r\n      }\r\n\r\n      // 處理字串排序\r\n      if (typeof aValue === 'string') {\r\n        aValue = aValue.toLowerCase();\r\n        bValue = bValue.toLowerCase();\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return this.sortDirection === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return this.sortDirection === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：TrackBy函數\r\n  trackByHouseId(_index: number, house: ConvertedHouse): number {\r\n    return house.CHouseId;\r\n  }\r\n\r\n  // 新增：取得狀態文字\r\n  getStatusText(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (status) {\r\n      case 'active':\r\n        return '進行中';\r\n      case 'pending':\r\n        return '待開放';\r\n      case 'expired':\r\n        return '已過期';\r\n      case 'not-set':\r\n        return '未設定';\r\n      case 'disabled':\r\n        return '已停用';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  // 新增：匯出資料\r\n  exportData() {\r\n    // 實現匯出功能\r\n    const csvContent = this.generateCSV();\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  // 新增：產生CSV內容\r\n  private generateCSV(): string {\r\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\r\n    const rows = this.filteredHouses.map(house => [\r\n      house.CHouseHold,\r\n      house.CBuildingName,\r\n      `${house.CFloor}F`,\r\n      house.CChangeStartDate || '未設定',\r\n      house.CChangeEndDate || '未設定',\r\n      this.getStatusText(house)\r\n    ]);\r\n\r\n    const csvContent = [headers, ...rows]\r\n      .map(row => row.map(cell => `\"${cell}\"`).join(','))\r\n      .join('\\n');\r\n\r\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <!-- 優化的Header區域 -->\r\n    <div class=\"page-header-optimized\">\r\n      <!-- 頁面標題和幫助 -->\r\n      <div class=\"page-title-section\">\r\n        <div class=\"d-flex align-items-center justify-content-between\">\r\n          <div class=\"title-group\">\r\n            <h5 class=\"page-title mb-1\">\r\n              <i class=\"fas fa-clock me-2\"></i>選樣開放時段設定\r\n            </h5>\r\n            <p class=\"page-subtitle text-muted mb-0\">管理各戶別的選樣開放時間範圍</p>\r\n          </div>\r\n          <div class=\"help-section\">\r\n            <button class=\"btn btn-outline-secondary btn-sm\" type=\"button\" data-bs-toggle=\"tooltip\"\r\n              title=\"設定各戶別的選樣開放時段，控制客戶選樣權限\">\r\n              <i class=\"fas fa-question-circle\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 緊湊的主要篩選區域 -->\r\n      <div class=\"compact-filters\">\r\n        <div class=\"row g-3 align-items-end\">\r\n          <div class=\"col-lg-3 col-md-4\">\r\n            <label class=\"form-label small fw-medium\">建案 <span class=\"text-danger\">*</span></label>\r\n            <nb-select placeholder=\"請選擇建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\"\r\n              (selectedChange)=\"onBuildCaseChange()\" size=\"small\">\r\n              <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n                {{ case.CBuildCaseName }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <label class=\"form-label small fw-medium\">棟別</label>\r\n            <nb-select placeholder=\"全部棟別\" [(ngModel)]=\"selectedBuilding\" (selectedChange)=\"onBuildingChange()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部棟別</nb-option>\r\n              <nb-option *ngFor=\"let building of buildingOptions\" [value]=\"building\">\r\n                {{ building }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-4 col-md-5\">\r\n            <label class=\"form-label small fw-medium\">開放日期範圍</label>\r\n            <div class=\"date-range-group\">\r\n              <nb-form-field size=\"small\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"開始日期\" [nbDatepicker]=\"StartDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n                <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n              <span class=\"date-separator\">~</span>\r\n              <nb-form-field size=\"small\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"結束日期\" [nbDatepicker]=\"EndDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n                <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12\">\r\n            <div class=\"action-buttons\">\r\n              <button class=\"btn btn-primary\" (click)=\"getHouseChangeDate()\" [disabled]=\"loading\">\r\n                <i class=\"fas fa-search me-1\"></i>查詢\r\n              </button>\r\n              <button class=\"btn btn-outline-secondary ms-2\" (click)=\"resetFilters()\" [disabled]=\"loading\"\r\n                title=\"重置篩選條件\">\r\n                <i class=\"fas fa-undo\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 可摺疊的進階篩選 -->\r\n      <div class=\"advanced-filters-toggle\" *ngIf=\"flattenedHouses.length > 0\">\r\n        <button class=\"btn btn-link btn-sm p-0\" (click)=\"toggleAdvancedFilters()\" type=\"button\">\r\n          <i class=\"fas\" [class.fa-chevron-down]=\"!showAdvancedFilters\" [class.fa-chevron-up]=\"showAdvancedFilters\"></i>\r\n          <span class=\"ms-1\">進階篩選</span>\r\n          <span class=\"badge bg-primary ms-2\" *ngIf=\"hasActiveFilters()\">{{ getActiveFiltersCount() }}</span>\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"advanced-filters-panel\" *ngIf=\"showAdvancedFilters && flattenedHouses.length > 0\" [@slideInOut]>\r\n        <div class=\"row g-3 align-items-center\">\r\n          <div class=\"col-lg-3 col-md-4\">\r\n            <nb-form-field size=\"small\">\r\n              <nb-icon nbPrefix icon=\"search-outline\"></nb-icon>\r\n              <input nbInput placeholder=\"搜尋戶型、棟別...\" [(ngModel)]=\"filterOptions.searchKeyword\"\r\n                (ngModelChange)=\"onSearch()\">\r\n            </nb-form-field>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <nb-select placeholder=\"狀態篩選\" [(ngModel)]=\"filterOptions.statusFilter\" (selectedChange)=\"onSearch()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部狀態</nb-option>\r\n              <nb-option value=\"active\">進行中</nb-option>\r\n              <nb-option value=\"pending\">待開放</nb-option>\r\n              <nb-option value=\"expired\">已過期</nb-option>\r\n              <nb-option value=\"not-set\">未設定</nb-option>\r\n              <nb-option value=\"disabled\">已停用</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <nb-select placeholder=\"樓層篩選\" [(ngModel)]=\"filterOptions.floorFilter\" (selectedChange)=\"onSearch()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部樓層</nb-option>\r\n              <nb-option *ngFor=\"let floor of availableFloors\" [value]=\"floor\">\r\n                {{ floor }}F\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-2\">\r\n            <nb-select placeholder=\"顯示筆數\" [(ngModel)]=\"pageSize\" (selectedChange)=\"onPageSizeChange()\" size=\"small\">\r\n              <nb-option [value]=\"50\">50筆/頁</nb-option>\r\n              <nb-option [value]=\"100\">100筆/頁</nb-option>\r\n              <nb-option [value]=\"200\">200筆/頁</nb-option>\r\n              <nb-option [value]=\"500\">500筆/頁</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12\">\r\n            <div class=\"filter-actions\">\r\n              <button class=\"btn btn-outline-danger btn-sm\" (click)=\"clearAllFilters()\" *ngIf=\"hasActiveFilters()\">\r\n                <i class=\"fas fa-times me-1\"></i>清除篩選\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 優化的表格視圖 -->\r\n    <div class=\"table-view-enhanced mt-4\" *ngIf=\"flattenedHouses.length > 0\">\r\n      <!-- 資料統計和快速篩選 -->\r\n      <div class=\"data-summary-bar\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"summary-info\">\r\n            <span class=\"total-count\">\r\n              <i class=\"fas fa-database me-1\"></i>\r\n              共 <strong>{{ filteredHouses.length }}</strong> 筆資料\r\n            </span>\r\n            <span class=\"selected-count\" *ngIf=\"selectedHouses.length > 0\">\r\n              <i class=\"fas fa-check-square me-1 text-primary\"></i>\r\n              已選 <strong class=\"text-primary\">{{ selectedHouses.length }}</strong> 筆\r\n            </span>\r\n          </div>\r\n          <div class=\"quick-filters\">\r\n            <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n              <button type=\"button\" class=\"btn\" [class.btn-primary]=\"filterOptions.statusFilter === 'active'\"\r\n                [class.btn-outline-primary]=\"filterOptions.statusFilter !== 'active'\"\r\n                (click)=\"setQuickFilter('active')\">\r\n                <i class=\"fas fa-play-circle me-1\"></i>進行中\r\n              </button>\r\n              <button type=\"button\" class=\"btn\" [class.btn-warning]=\"filterOptions.statusFilter === 'pending'\"\r\n                [class.btn-outline-warning]=\"filterOptions.statusFilter !== 'pending'\"\r\n                (click)=\"setQuickFilter('pending')\">\r\n                <i class=\"fas fa-clock me-1\"></i>待開放\r\n              </button>\r\n              <button type=\"button\" class=\"btn\" [class.btn-danger]=\"filterOptions.statusFilter === 'expired'\"\r\n                [class.btn-outline-danger]=\"filterOptions.statusFilter !== 'expired'\"\r\n                (click)=\"setQuickFilter('expired')\">\r\n                <i class=\"fas fa-times-circle me-1\"></i>已過期\r\n              </button>\r\n              <button type=\"button\" class=\"btn btn-not-set\"\r\n                [class.btn-not-set-active]=\"filterOptions.statusFilter === 'not-set'\"\r\n                [class.btn-not-set-outline]=\"filterOptions.statusFilter !== 'not-set'\"\r\n                (click)=\"setQuickFilter('not-set')\">\r\n                <i class=\"fas fa-exclamation-triangle me-1\"></i>未設定\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 優化的工具列 -->\r\n      <div class=\"enhanced-toolbar\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"batch-operations\">\r\n            <div class=\"selection-controls\">\r\n              <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\" class=\"select-all-checkbox\">\r\n                <span class=\"fw-medium\">全選</span>\r\n              </nb-checkbox>\r\n              <div class=\"batch-actions ms-3\" *ngIf=\"selectedHouses.length > 0\">\r\n                <button class=\"btn btn-warning btn-sm\" (click)=\"openBatchSetting()\" title=\"批次設定選中的戶別開放時段\">\r\n                  <i class=\"fas fa-cogs me-1\"></i>批次設定\r\n                  <span class=\"badge bg-light text-dark ms-1\">{{ selectedHouses.length }}</span>\r\n                </button>\r\n                <button class=\"btn btn-outline-danger btn-sm ms-2\" (click)=\"clearSelection()\" title=\"清除選擇\">\r\n                  <i class=\"fas fa-times me-1\"></i>清除選擇\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"table-controls\">\r\n            <div class=\"d-flex align-items-center gap-2\">\r\n              <button class=\"btn btn-outline-success btn-sm\" [disabled]=\"filteredHouses.length === 0\"\r\n                (click)=\"exportData()\" title=\"匯出資料\">\r\n                <i class=\"fas fa-download me-1\"></i>匯出\r\n              </button>\r\n              <div class=\"pagination-summary\">\r\n                <small class=\"text-muted\">\r\n                  顯示 {{ (currentPage - 1) * pageSize + 1 }} -\r\n                  {{ Math.min(currentPage * pageSize, filteredHouses.length) }} /\r\n                  共 {{ filteredHouses.length }} 筆\r\n                </small>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 優化的表格 -->\r\n      <div class=\"enhanced-table-container\">\r\n        <table class=\"table table-hover enhanced-table\">\r\n          <thead class=\"enhanced-table-header\">\r\n            <tr>\r\n              <th width=\"50\" class=\"text-center\">\r\n                <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\"></nb-checkbox>\r\n              </th>\r\n              <th width=\"120\" (click)=\"sort('CHouseHold')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  戶型\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CHouseHold' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CHouseHold' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"100\" (click)=\"sort('CBuildingName')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  棟別\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CBuildingName' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CBuildingName' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"80\" (click)=\"sort('CFloor')\" class=\"sortable text-center\">\r\n                <div class=\"header-content\">\r\n                  樓層\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CFloor' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CFloor' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"140\" (click)=\"sort('CChangeStartDate')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  開始日期\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CChangeStartDate' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CChangeStartDate' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"140\" (click)=\"sort('CChangeEndDate')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  結束日期\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CChangeEndDate' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CChangeEndDate' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"120\" class=\"text-center\">\r\n                <div class=\"header-content\">\r\n                  狀態\r\n                </div>\r\n              </th>\r\n              <th width=\"100\" class=\"text-center\">\r\n                <div class=\"header-content\">\r\n                  操作\r\n                </div>\r\n              </th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let house of paginatedHouses; trackBy: trackByHouseId\"\r\n              [class.table-row-selected]=\"house.selected\" [class.table-row-disabled]=\"!house.CHouseId\">\r\n              <td class=\"text-center\">\r\n                <nb-checkbox [(ngModel)]=\"house.selected\" [disabled]=\"!house.CHouseId\"\r\n                  (ngModelChange)=\"onHouseSelectionChange()\"></nb-checkbox>\r\n              </td>\r\n              <td>\r\n                <div class=\"house-info\">\r\n                  <span class=\"house-name fw-medium\">{{ house.CHouseHold }}</span>\r\n                  <small class=\"text-muted d-block\" *ngIf=\"!house.CHouseId\">無資料</small>\r\n                </div>\r\n              </td>\r\n              <td>\r\n                <span class=\"building-name\">{{ house.CBuildingName }}</span>\r\n              </td>\r\n              <td class=\"text-center\">\r\n                <span class=\"floor-badge\">{{ house.CFloor }}F</span>\r\n              </td>\r\n              <td>\r\n                <div class=\"date-info\">\r\n                  <span *ngIf=\"house.CChangeStartDate\" class=\"date-display\">\r\n                    <i class=\"fas fa-calendar me-1 text-success\"></i>\r\n                    {{ house.CChangeStartDate | date:'yyyy-MM-dd' }}\r\n                  </span>\r\n                  <span *ngIf=\"!house.CChangeStartDate\" class=\"text-muted\">\r\n                    <i class=\"fas fa-minus me-1\"></i>未設定\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td>\r\n                <div class=\"date-info\">\r\n                  <span *ngIf=\"house.CChangeEndDate\" class=\"date-display\">\r\n                    <i class=\"fas fa-calendar me-1 text-danger\"></i>\r\n                    {{ house.CChangeEndDate | date:'yyyy-MM-dd' }}\r\n                  </span>\r\n                  <span *ngIf=\"!house.CChangeEndDate\" class=\"text-muted\">\r\n                    <i class=\"fas fa-minus me-1\"></i>未設定\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"text-center\">\r\n                <div class=\"status-display\">\r\n                  <span class=\"enhanced-status-badge\" [class]=\"getStatusClass(house)\">\r\n                    <i class=\"status-icon\" [class]=\"getStatusIcon(house)\"></i>\r\n                    <span class=\"status-text\">{{ getStatusText(house) }}</span>\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"text-center\">\r\n                <div class=\"action-buttons\">\r\n                  <button class=\"btn btn-sm btn-outline-primary\" [disabled]=\"!house.CHouseId\"\r\n                    (click)=\"openModel(dialog, house)\" title=\"編輯時段設定\">\r\n                    <i class=\"fas fa-edit\"></i>\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 優化的分頁控制 -->\r\n      <div class=\"enhanced-pagination-container\" *ngIf=\"totalPages > 1\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"pagination-info-detailed\">\r\n            <span class=\"text-muted\">\r\n              第 <strong>{{ currentPage }}</strong> 頁，共 <strong>{{ totalPages }}</strong> 頁\r\n              <span class=\"ms-2\">\r\n                (顯示第 {{ (currentPage - 1) * pageSize + 1 }} -\r\n                {{ Math.min(currentPage * pageSize, filteredHouses.length) }} 筆，\r\n                共 {{ filteredHouses.length }} 筆)\r\n              </span>\r\n            </span>\r\n          </div>\r\n\r\n          <nav class=\"pagination-nav\">\r\n            <ul class=\"pagination pagination-sm mb-0\">\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\" title=\"第一頁\">\r\n                  <i class=\"fas fa-angle-double-left\"></i>\r\n                </button>\r\n              </li>\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\"\r\n                  title=\"上一頁\">\r\n                  <i class=\"fas fa-angle-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 頁碼顯示 -->\r\n              <li class=\"page-item\" *ngFor=\"let page of getVisiblePages()\" [class.active]=\"page === currentPage\">\r\n                <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n              </li>\r\n\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\" [disabled]=\"currentPage === totalPages\"\r\n                  title=\"下一頁\">\r\n                  <i class=\"fas fa-angle-right\"></i>\r\n                </button>\r\n              </li>\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                <button class=\"page-link\" (click)=\"goToPage(totalPages)\" [disabled]=\"currentPage === totalPages\"\r\n                  title=\"最後一頁\">\r\n                  <i class=\"fas fa-angle-double-right\"></i>\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n\r\n          <!-- 快速跳轉 -->\r\n          <div class=\"quick-jump\">\r\n            <div class=\"input-group input-group-sm\" style=\"width: 120px;\">\r\n              <input type=\"number\" class=\"form-control\" placeholder=\"頁碼\" [(ngModel)]=\"jumpToPage\"\r\n                (keyup.enter)=\"jumpToPageAction()\" [min]=\"1\" [max]=\"totalPages\">\r\n              <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"jumpToPageAction()\" title=\"跳轉\">\r\n                <i class=\"fas fa-arrow-right\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 當沒有資料時顯示 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"flattenedHouses.length === 0 && houseChangeDates.length === 0\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <i class=\"fas fa-info-circle fa-2x mb-3\"></i>\r\n            <p>請選擇建案後查詢資料</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n\r\n    <!-- 載入中狀態 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"loading\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <div class=\"spinner-border\" role=\"status\">\r\n              <span class=\"sr-only\">載入中...</span>\r\n            </div>\r\n            <p class=\"mt-2\">資料載入中，請稍候...</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 批次設定對話框 -->\r\n<ng-template #batchSettingDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:600px; max-height: 90vh\">\r\n    <nb-card-header>\r\n      批次設定\r\n      <span *ngIf=\"selectedHouses.length > 0\" class=\"text-primary\">\r\n        - 已選擇 {{ selectedHouses.length }} 個戶別\r\n      </span>\r\n      <span *ngIf=\"selectedBuildingForBatch && selectedHouses.length === 0\">\r\n        - {{ selectedBuildingForBatch.name }}\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 時間設定 -->\r\n      <div class=\"form-group\">\r\n        <label>開放時間起訖 <span class=\"text-red-600\">*</span></label>\r\n        <div class=\"d-flex align-items-center\">\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchStartDate\"\r\n              [(ngModel)]=\"batchSettings.startDate\">\r\n            <nb-datepicker #batchStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <span class=\"mx-2\">~</span>\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchEndDate\"\r\n              [(ngModel)]=\"batchSettings.endDate\">\r\n            <nb-datepicker #batchEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 選擇範圍 -->\r\n      <div class=\"form-group\">\r\n        <label>適用範圍</label>\r\n        <div class=\"selection-options\">\r\n          <!-- 如果有已選擇的戶別，顯示已選擇的資訊 -->\r\n          <div *ngIf=\"selectedHouses.length > 0\" class=\"alert alert-info\">\r\n            <h6>將套用到已選擇的 {{ selectedHouses.length }} 個戶別：</h6>\r\n            <div class=\"selected-houses-preview\">\r\n              <span *ngFor=\"let house of selectedHouses.slice(0, 10); let i = index\"\r\n                class=\"badge badge-primary mr-1 mb-1\">\r\n                {{ house.CHouseHold }} ({{ house.CBuildingName }}-{{ house.CFloor }}F)\r\n              </span>\r\n              <span *ngIf=\"selectedHouses.length > 10\" class=\"text-muted\">\r\n                ...等 {{ selectedHouses.length - 10 }} 個\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 如果沒有已選擇的戶別，顯示選擇選項 -->\r\n          <div *ngIf=\"selectedHouses.length === 0\">\r\n            <nb-checkbox [(ngModel)]=\"batchSettings.applyToAll\">\r\n              全部戶別 ({{ flattenedHouses.length }} 個)\r\n            </nb-checkbox>\r\n            <div class=\"mt-2\" *ngIf=\"!batchSettings.applyToAll && selectedBuildingForBatch\">\r\n              <div class=\"floor-selection\" *ngFor=\"let floor of selectedBuildingForBatch.floors\">\r\n                <nb-checkbox [(ngModel)]=\"floor.selected\" (ngModelChange)=\"onFloorSelectionChange(floor)\">\r\n                  {{ floor.floorNumber }}F ({{ floor.houses.length }} 戶)\r\n                </nb-checkbox>\r\n                <div class=\"house-selection ml-4\" *ngIf=\"floor.selected\">\r\n                  <nb-checkbox *ngFor=\"let house of floor.houses\" [(ngModel)]=\"house.selected\"\r\n                    [disabled]=\"!house.CHouseId\">\r\n                    {{ house.CHouseHold }} ({{ house.CBuildingName }})\r\n                  </nb-checkbox>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-end\">\r\n      <button class=\"btn btn-secondary me-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onBatchSubmit(ref)\">\r\n        批次設定\r\n        <span *ngIf=\"selectedHouses.length > 0\">({{ selectedHouses.length }} 個戶別)</span>\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      您正在編輯 <span class=\"text-green-600\">{{selectedHouseChangeDate.CHouseHold}} -\r\n        {{selectedHouseChangeDate.CFloor}}F</span> 的選樣開放時段\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cFloorFrom\" class=\"label col-3\">開放時間起訖<span class=\"text-red-600\">*</span>\r\n        </label>\r\n        <nb-form-field class=\"ml-3\">\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeStartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeStartDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeStartDate\">\r\n          <nb-datepicker #changeStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n        <label for=\"CChangeStartDate\" class=\"label col-1\"> ~\r\n        </label>\r\n        <nb-form-field>\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeEndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeEndDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeEndDate\">\r\n          <nb-datepicker #changeEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,OAAO,EAASC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAShF,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;;;ICetEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IASAR,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAK,WAAA,CAAkB;IACpET,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,WAAA,MACF;;;;;IA0CJT,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApCH,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAAC,qBAAA,GAA6B;;;;;;IAH9FZ,EADF,CAAAC,cAAA,cAAwE,iBACkB;IAAhDD,EAAA,CAAAa,UAAA,mBAAAC,mEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAQ,qBAAA,EAAuB;IAAA,EAAC;IACvEnB,EAAA,CAAAoB,SAAA,YAA8G;IAC9GpB,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9BH,EAAA,CAAAqB,UAAA,IAAAC,iDAAA,mBAA+D;IAEnEtB,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAJaH,EAAA,CAAAM,SAAA,GAA8C;IAACN,EAA/C,CAAAuB,WAAA,qBAAAZ,MAAA,CAAAa,mBAAA,CAA8C,kBAAAb,MAAA,CAAAa,mBAAA,CAA4C;IAEpExB,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAc,gBAAA,GAAwB;;;;;IA8BzDzB,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAsB,QAAA,CAAe;IAC9D1B,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAmB,QAAA,OACF;;;;;;IAeA1B,EAAA,CAAAC,cAAA,iBAAqG;IAAvDD,EAAA,CAAAa,UAAA,mBAAAc,6EAAA;MAAA3B,EAAA,CAAAe,aAAA,CAAAa,GAAA;MAAA,MAAAjB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAkB,eAAA,EAAiB;IAAA,EAAC;IACvE7B,EAAA,CAAAoB,SAAA,YAAiC;IAAApB,EAAA,CAAAE,MAAA,gCACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA1CXH,EAHN,CAAAC,cAAA,cAA4G,cAClE,cACP,wBACD;IAC1BD,EAAA,CAAAoB,SAAA,kBAAkD;IAClDpB,EAAA,CAAAC,cAAA,gBAC+B;IADSD,EAAA,CAAA8B,gBAAA,2BAAAC,0EAAAC,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAwB,aAAA,CAAAC,aAAA,EAAAJ,MAAA,MAAArB,MAAA,CAAAwB,aAAA,CAAAC,aAAA,GAAAJ,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAyC;IAC/EhC,EAAA,CAAAa,UAAA,2BAAAkB,0EAAA;MAAA/B,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBP,MAAA,CAAA0B,QAAA,EAAU;IAAA,EAAC;IAElCrC,EAHI,CAAAG,YAAA,EAC+B,EACjB,EACZ;IAGJH,EADF,CAAAC,cAAA,cAA+B,oBAEd;IADeD,EAAA,CAAA8B,gBAAA,2BAAAQ,8EAAAN,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAwB,aAAA,CAAAI,YAAA,EAAAP,MAAA,MAAArB,MAAA,CAAAwB,aAAA,CAAAI,YAAA,GAAAP,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAwC;IAAChC,EAAA,CAAAa,UAAA,4BAAA2B,+EAAA;MAAAxC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAkBP,MAAA,CAAA0B,QAAA,EAAU;IAAA,EAAC;IAElGrC,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,qBAA0B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA4B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAA+B,qBAEd;IADeD,EAAA,CAAA8B,gBAAA,2BAAAW,+EAAAT,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAwB,aAAA,CAAAO,WAAA,EAAAV,MAAA,MAAArB,MAAA,CAAAwB,aAAA,CAAAO,WAAA,GAAAV,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAuC;IAAChC,EAAA,CAAAa,UAAA,4BAAA8B,gFAAA;MAAA3C,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAkBP,MAAA,CAAA0B,QAAA,EAAU;IAAA,EAAC;IAEjGrC,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAqB,UAAA,KAAAuB,uDAAA,wBAAiE;IAIrE5C,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,eAA+B,qBAC2E;IAA1ED,EAAA,CAAA8B,gBAAA,2BAAAe,+EAAAb,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAmC,QAAA,EAAAd,MAAA,MAAArB,MAAA,CAAAmC,QAAA,GAAAd,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAsB;IAAChC,EAAA,CAAAa,UAAA,4BAAAkC,gFAAA;MAAA/C,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAkBP,MAAA,CAAAqC,gBAAA,EAAkB;IAAA,EAAC;IACxFhD,EAAA,CAAAC,cAAA,qBAAwB;IAAAD,EAAA,CAAAE,MAAA,uBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAAgC,eACF;IAC1BD,EAAA,CAAAqB,UAAA,KAAA4B,oDAAA,qBAAqG;IAM7GjD,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IAjDwFH,EAAA,CAAAI,UAAA,gBAAA8C,SAAA,CAAa;IAK3DlD,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAwB,aAAA,CAAAC,aAAA,CAAyC;IAMrDpC,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAwB,aAAA,CAAAI,YAAA,CAAwC;IAYxCvC,EAAA,CAAAM,SAAA,IAAuC;IAAvCN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAwB,aAAA,CAAAO,WAAA,CAAuC;IAGtC1C,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAyC,eAAA,CAAkB;IAOnBpD,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAmC,QAAA,CAAsB;IACvC9C,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAI,UAAA,aAAY;IACZJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IAMmDJ,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAc,gBAAA,GAAwB;;;;;IAmBrGzB,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAoB,SAAA,aAAqD;IACrDpB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,eACvE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAD2BH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAA0C,cAAA,CAAAC,MAAA,CAA2B;;;;;;IAwCzDtD,EADF,CAAAC,cAAA,eAAkE,kBAC0B;IAAnDD,EAAA,CAAAa,UAAA,mBAAA0C,0EAAA;MAAAvD,EAAA,CAAAe,aAAA,CAAAyC,IAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA8C,gBAAA,EAAkB;IAAA,EAAC;IACjEzD,EAAA,CAAAoB,SAAA,aAAgC;IAAApB,EAAA,CAAAE,MAAA,gCAChC;IAAAF,EAAA,CAAAC,cAAA,gBAA4C;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IACzEF,EADyE,CAAAG,YAAA,EAAO,EACvE;IACTH,EAAA,CAAAC,cAAA,kBAA2F;IAAxCD,EAAA,CAAAa,UAAA,mBAAA6C,0EAAA;MAAA1D,EAAA,CAAAe,aAAA,CAAAyC,IAAA;MAAA,MAAA7C,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAgD,cAAA,EAAgB;IAAA,EAAC;IAC3E3D,EAAA,CAAAoB,SAAA,YAAiC;IAAApB,EAAA,CAAAE,MAAA,gCACnC;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAL0CH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAA0C,cAAA,CAAAC,MAAA,CAA2B;;;;;IAiGvEtD,EAAA,CAAAC,cAAA,iBAA0D;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAWrEH,EAAA,CAAAC,cAAA,gBAA0D;IACxDD,EAAA,CAAAoB,SAAA,aAAiD;IACjDpB,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAA4D,WAAA,OAAAC,SAAA,CAAAC,gBAAA,qBACF;;;;;IACA9D,EAAA,CAAAC,cAAA,eAAyD;IACvDD,EAAA,CAAAoB,SAAA,aAAiC;IAAApB,EAAA,CAAAE,MAAA,0BACnC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAKPH,EAAA,CAAAC,cAAA,gBAAwD;IACtDD,EAAA,CAAAoB,SAAA,aAAgD;IAChDpB,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAA4D,WAAA,OAAAC,SAAA,CAAAE,cAAA,qBACF;;;;;IACA/D,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAoB,SAAA,aAAiC;IAAApB,EAAA,CAAAE,MAAA,0BACnC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAlCTH,EAHJ,CAAAC,cAAA,SAC2F,cACjE,uBAEuB;IADhCD,EAAA,CAAA8B,gBAAA,2BAAAkC,sFAAAhC,MAAA;MAAA,MAAA6B,SAAA,GAAA7D,EAAA,CAAAe,aAAA,CAAAkD,IAAA,EAAAC,SAAA;MAAAlE,EAAA,CAAAkC,kBAAA,CAAA2B,SAAA,CAAAM,QAAA,EAAAnC,MAAA,MAAA6B,SAAA,CAAAM,QAAA,GAAAnC,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAA4B;IACvChC,EAAA,CAAAa,UAAA,2BAAAmD,sFAAA;MAAAhE,EAAA,CAAAe,aAAA,CAAAkD,IAAA;MAAA,MAAAtD,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBP,MAAA,CAAAyD,sBAAA,EAAwB;IAAA,EAAC;IAC9CpE,EAD+C,CAAAG,YAAA,EAAc,EACxD;IAGDH,EAFJ,CAAAC,cAAA,SAAI,eACsB,gBACa;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAAqB,UAAA,IAAAgD,wDAAA,qBAA0D;IAE9DrE,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,SAAI,gBAC0B;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACzD;IAEHH,EADF,CAAAC,cAAA,eAAwB,iBACI;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAC/CF,EAD+C,CAAAG,YAAA,EAAO,EACjD;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACqB;IAKrBD,EAJA,CAAAqB,UAAA,KAAAiD,wDAAA,oBAA0D,KAAAC,wDAAA,oBAID;IAI7DvE,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACqB;IAKrBD,EAJA,CAAAqB,UAAA,KAAAmD,wDAAA,oBAAwD,KAAAC,wDAAA,oBAID;IAI3DzE,EADE,CAAAG,YAAA,EAAM,EACH;IAGDH,EAFJ,CAAAC,cAAA,eAAwB,gBACM,iBAC0C;IAClED,EAAA,CAAAoB,SAAA,cAA0D;IAC1DpB,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAG1DF,EAH0D,CAAAG,YAAA,EAAO,EACtD,EACH,EACH;IAGDH,EAFJ,CAAAC,cAAA,eAAwB,eACM,mBAE0B;IAAlDD,EAAA,CAAAa,UAAA,mBAAA6D,0EAAA;MAAA,MAAAb,SAAA,GAAA7D,EAAA,CAAAe,aAAA,CAAAkD,IAAA,EAAAC,SAAA;MAAA,MAAAvD,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,MAAA0D,UAAA,GAAA3E,EAAA,CAAA4E,WAAA;MAAA,OAAA5E,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAkE,SAAA,CAAAF,UAAA,EAAAd,SAAA,CAAwB;IAAA,EAAC;IAClC7D,EAAA,CAAAoB,SAAA,cAA2B;IAInCpB,EAHM,CAAAG,YAAA,EAAS,EACL,EACH,EACF;;;;;IAvDyCH,EAA5C,CAAAuB,WAAA,uBAAAsC,SAAA,CAAAM,QAAA,CAA2C,wBAAAN,SAAA,CAAAiB,QAAA,CAA6C;IAEzE9E,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAmD,gBAAA,YAAAU,SAAA,CAAAM,QAAA,CAA4B;IAACnE,EAAA,CAAAI,UAAA,cAAAyD,SAAA,CAAAiB,QAAA,CAA4B;IAKjC9E,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAU,iBAAA,CAAAmD,SAAA,CAAAkB,UAAA,CAAsB;IACtB/E,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAI,UAAA,UAAAyD,SAAA,CAAAiB,QAAA,CAAqB;IAI9B9E,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAU,iBAAA,CAAAmD,SAAA,CAAAmB,aAAA,CAAyB;IAG3BhF,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,kBAAA,KAAAsD,SAAA,CAAAoB,MAAA,MAAmB;IAIpCjF,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,SAAAyD,SAAA,CAAAC,gBAAA,CAA4B;IAI5B9D,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAI,UAAA,UAAAyD,SAAA,CAAAC,gBAAA,CAA6B;IAO7B9D,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,SAAAyD,SAAA,CAAAE,cAAA,CAA0B;IAI1B/D,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAAyD,SAAA,CAAAE,cAAA,CAA2B;IAOE/D,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAkF,UAAA,CAAAvE,MAAA,CAAAwE,cAAA,CAAAtB,SAAA,EAA+B;IAC1C7D,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAkF,UAAA,CAAAvE,MAAA,CAAAyE,aAAA,CAAAvB,SAAA,EAA8B;IAC3B7D,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAA0E,aAAA,CAAAxB,SAAA,EAA0B;IAMP7D,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,cAAAyD,SAAA,CAAAiB,QAAA,CAA4B;;;;;;IAyC7E9E,EADF,CAAAC,cAAA,cAAmG,kBAC9C;IAAzBD,EAAA,CAAAa,UAAA,mBAAAyE,gFAAA;MAAA,MAAAC,QAAA,GAAAvF,EAAA,CAAAe,aAAA,CAAAyE,IAAA,EAAAtB,SAAA;MAAA,MAAAvD,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA8E,QAAA,CAAAF,QAAA,CAAc;IAAA,EAAC;IAACvF,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFwDH,EAAA,CAAAuB,WAAA,WAAAgE,QAAA,KAAA5E,MAAA,CAAA+E,WAAA,CAAqC;IAC7C1F,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAU,iBAAA,CAAA6E,QAAA,CAAU;;;;;;IA1BjEvF,EAHN,CAAAC,cAAA,eAAkE,cACD,eACvB,eACX;IACvBD,EAAA,CAAAE,MAAA,eAAE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,gBAC3E;IAAAF,EAAA,CAAAC,cAAA,iBAAmB;IACjBD,EAAA,CAAAE,MAAA,IAGF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACF,EACH;IAKAH,EAHN,CAAAC,cAAA,gBAA4B,eACgB,eACmB,mBACkC;IAAjED,EAAA,CAAAa,UAAA,mBAAA8E,2EAAA;MAAA3F,EAAA,CAAAe,aAAA,CAAA6E,IAAA;MAAA,MAAAjF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA8E,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAC7CzF,EAAA,CAAAoB,SAAA,cAAwC;IAE5CpB,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,eAA2D,mBAE3C;IADYD,EAAA,CAAAa,UAAA,mBAAAgF,2EAAA;MAAA7F,EAAA,CAAAe,aAAA,CAAA6E,IAAA;MAAA,MAAAjF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA8E,QAAA,CAAA9E,MAAA,CAAA+E,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAE3D1F,EAAA,CAAAoB,SAAA,cAAiC;IAErCpB,EADE,CAAAG,YAAA,EAAS,EACN;IAGLH,EAAA,CAAAqB,UAAA,KAAAyE,uDAAA,kBAAmG;IAKjG9F,EADF,CAAAC,cAAA,eAAoE,mBAEpD;IADYD,EAAA,CAAAa,UAAA,mBAAAkF,2EAAA;MAAA/F,EAAA,CAAAe,aAAA,CAAA6E,IAAA;MAAA,MAAAjF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA8E,QAAA,CAAA9E,MAAA,CAAA+E,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAE3D1F,EAAA,CAAAoB,SAAA,cAAkC;IAEtCpB,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,eAAoE,mBAEnD;IADWD,EAAA,CAAAa,UAAA,mBAAAmF,2EAAA;MAAAhG,EAAA,CAAAe,aAAA,CAAA6E,IAAA;MAAA,MAAAjF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA8E,QAAA,CAAA9E,MAAA,CAAAsF,UAAA,CAAoB;IAAA,EAAC;IAEtDjG,EAAA,CAAAoB,SAAA,cAAyC;IAIjDpB,EAHM,CAAAG,YAAA,EAAS,EACN,EACF,EACD;IAKFH,EAFJ,CAAAC,cAAA,gBAAwB,gBACwC,kBAEM;IADPD,EAAA,CAAA8B,gBAAA,2BAAAoE,kFAAAlE,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAA6E,IAAA;MAAA,MAAAjF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAwF,UAAA,EAAAnE,MAAA,MAAArB,MAAA,CAAAwF,UAAA,GAAAnE,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAwB;IACjFhC,EAAA,CAAAa,UAAA,yBAAAuF,gFAAA;MAAApG,EAAA,CAAAe,aAAA,CAAA6E,IAAA;MAAA,MAAAjF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAeP,MAAA,CAAA0F,gBAAA,EAAkB;IAAA,EAAC;IADpCrG,EAAA,CAAAG,YAAA,EACkE;IAClEH,EAAA,CAAAC,cAAA,mBAAgG;IAAxCD,EAAA,CAAAa,UAAA,mBAAAyF,2EAAA;MAAAtG,EAAA,CAAAe,aAAA,CAAA6E,IAAA;MAAA,MAAAjF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA0F,gBAAA,EAAkB;IAAA,EAAC;IAClFrG,EAAA,CAAAoB,SAAA,cAAkC;IAK5CpB,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAtDYH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAA+E,WAAA,CAAiB;IAAsB1F,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAAsF,UAAA,CAAgB;IAE/DjG,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAuG,kBAAA,2BAAA5F,MAAA,CAAA+E,WAAA,QAAA/E,MAAA,CAAAmC,QAAA,aAAAnC,MAAA,CAAA6F,IAAA,CAAAC,GAAA,CAAA9F,MAAA,CAAA+E,WAAA,GAAA/E,MAAA,CAAAmC,QAAA,EAAAnC,MAAA,CAAA+F,cAAA,CAAApD,MAAA,4BAAA3C,MAAA,CAAA+F,cAAA,CAAApD,MAAA,cAGF;IAMsBtD,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAuB,WAAA,aAAAZ,MAAA,CAAA+E,WAAA,OAAoC;IACR1F,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA+E,WAAA,OAA8B;IAI1D1F,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAuB,WAAA,aAAAZ,MAAA,CAAA+E,WAAA,OAAoC;IACM1F,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA+E,WAAA,OAA8B;IAOvD1F,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAgG,eAAA,GAAoB;IAIrC3G,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAuB,WAAA,aAAAZ,MAAA,CAAA+E,WAAA,KAAA/E,MAAA,CAAAsF,UAAA,CAA6C;IACHjG,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA+E,WAAA,KAAA/E,MAAA,CAAAsF,UAAA,CAAuC;IAKjFjG,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAuB,WAAA,aAAAZ,MAAA,CAAA+E,WAAA,KAAA/E,MAAA,CAAAsF,UAAA,CAA6C;IACRjG,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA+E,WAAA,KAAA/E,MAAA,CAAAsF,UAAA,CAAuC;IAWvCjG,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAwF,UAAA,CAAwB;IACpCnG,EAAV,CAAAI,UAAA,UAAS,QAAAO,MAAA,CAAAsF,UAAA,CAAmB;;;;;;IAzPnEjG,EALR,CAAAC,cAAA,cAAyE,cAEzC,cACmC,cACnC,eACE;IACxBD,EAAA,CAAAoB,SAAA,YAAoC;IACpCpB,EAAA,CAAAE,MAAA,eAAE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BACjD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAqB,UAAA,KAAAuF,kDAAA,mBAA+D;IAIjE5G,EAAA,CAAAG,YAAA,EAAM;IAGFH,EAFJ,CAAAC,cAAA,eAA2B,eACwB,kBAGV;IAAnCD,EAAA,CAAAa,UAAA,mBAAAgG,oEAAA;MAAA7G,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAoG,cAAA,CAAe,QAAQ,CAAC;IAAA,EAAC;IAClC/G,EAAA,CAAAoB,SAAA,aAAuC;IAAApB,EAAA,CAAAE,MAAA,2BACzC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEsC;IAApCD,EAAA,CAAAa,UAAA,mBAAAmG,oEAAA;MAAAhH,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAoG,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnC/G,EAAA,CAAAoB,SAAA,aAAiC;IAAApB,EAAA,CAAAE,MAAA,2BACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEsC;IAApCD,EAAA,CAAAa,UAAA,mBAAAoG,oEAAA;MAAAjH,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAoG,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnC/G,EAAA,CAAAoB,SAAA,aAAwC;IAAApB,EAAA,CAAAE,MAAA,2BAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGsC;IAApCD,EAAA,CAAAa,UAAA,mBAAAqG,oEAAA;MAAAlH,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAoG,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnC/G,EAAA,CAAAoB,SAAA,aAAgD;IAAApB,EAAA,CAAAE,MAAA,2BAClD;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;IAOEH,EAJR,CAAAC,cAAA,eAA8B,eACmC,eAC/B,eACI,uBACyE;IAA1FD,EAAA,CAAA8B,gBAAA,2BAAAqF,iFAAAnF,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAyG,SAAA,EAAApF,MAAA,MAAArB,MAAA,CAAAyG,SAAA,GAAApF,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAuB;IAAChC,EAAA,CAAAa,UAAA,2BAAAsG,iFAAA;MAAAnH,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBP,MAAA,CAAA0G,iBAAA,EAAmB;IAAA,EAAC;IACxErH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EACrB;IACdH,EAAA,CAAAqB,UAAA,KAAAiG,iDAAA,kBAAkE;IAUtEtH,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA4B,eACmB,kBAEL;IAApCD,EAAA,CAAAa,UAAA,mBAAA0G,oEAAA;MAAAvH,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA6G,UAAA,EAAY;IAAA,EAAC;IACtBxH,EAAA,CAAAoB,SAAA,aAAoC;IAAApB,EAAA,CAAAE,MAAA,qBACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,eAAgC,iBACJ;IACxBD,EAAA,CAAAE,MAAA,IAGF;IAKVF,EALU,CAAAG,YAAA,EAAQ,EACJ,EACF,EACF,EACF,EACF;IAQIH,EALV,CAAAC,cAAA,eAAsC,kBACY,kBACT,UAC/B,eACiC,wBAC0C;IAA9DD,EAAA,CAAA8B,gBAAA,2BAAA2F,iFAAAzF,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAyG,SAAA,EAAApF,MAAA,MAAArB,MAAA,CAAAyG,SAAA,GAAApF,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAuB;IAAChC,EAAA,CAAAa,UAAA,2BAAA4G,iFAAA;MAAAzH,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBP,MAAA,CAAA0G,iBAAA,EAAmB;IAAA,EAAC;IAC5ErH,EAD6E,CAAAG,YAAA,EAAc,EACtF;IACLH,EAAA,CAAAC,cAAA,eAA8D;IAA9CD,EAAA,CAAAa,UAAA,mBAAA6G,gEAAA;MAAA1H,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAgH,IAAA,CAAK,YAAY,CAAC;IAAA,EAAC;IAC1C3H,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAoB,SAAA,cAEoF;IAExFpB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,eAAiE;IAAjDD,EAAA,CAAAa,UAAA,mBAAA+G,gEAAA;MAAA5H,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAgH,IAAA,CAAK,eAAe,CAAC;IAAA,EAAC;IAC7C3H,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAoB,SAAA,cAEuF;IAE3FpB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,eAAqE;IAAtDD,EAAA,CAAAa,UAAA,mBAAAgH,gEAAA;MAAA7H,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAgH,IAAA,CAAK,QAAQ,CAAC;IAAA,EAAC;IACrC3H,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAoB,SAAA,cAEgF;IAEpFpB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,eAAoE;IAApDD,EAAA,CAAAa,UAAA,mBAAAiH,gEAAA;MAAA9H,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAgH,IAAA,CAAK,kBAAkB,CAAC;IAAA,EAAC;IAChD3H,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAoB,SAAA,cAE0F;IAE9FpB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,eAAkE;IAAlDD,EAAA,CAAAa,UAAA,mBAAAkH,gEAAA;MAAA/H,EAAA,CAAAe,aAAA,CAAA+F,GAAA;MAAA,MAAAnG,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAgH,IAAA,CAAK,gBAAgB,CAAC;IAAA,EAAC;IAC9C3H,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAoB,SAAA,cAEwF;IAE5FpB,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,eAAoC,gBACN;IAC1BD,EAAA,CAAAE,MAAA,sBACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,eAAoC,gBACN;IAC1BD,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAM,EACH,EACF,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAqB,UAAA,KAAA2G,gDAAA,oBAC2F;IA0DjGhI,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAGNH,EAAA,CAAAqB,UAAA,KAAA4G,iDAAA,qBAAkE;IA2DpEjI,EAAA,CAAAG,YAAA,EAAM;;;;IA/PcH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAA+F,cAAA,CAAApD,MAAA,CAA2B;IAETtD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,KAA+B;IAOzBtD,EAAA,CAAAM,SAAA,GAA6D;IAC7FN,EADgC,CAAAuB,WAAA,gBAAAZ,MAAA,CAAAwB,aAAA,CAAAI,YAAA,cAA6D,wBAAA5B,MAAA,CAAAwB,aAAA,CAAAI,YAAA,cACxB;IAIrCvC,EAAA,CAAAM,SAAA,GAA8D;IAC9FN,EADgC,CAAAuB,WAAA,gBAAAZ,MAAA,CAAAwB,aAAA,CAAAI,YAAA,eAA8D,wBAAA5B,MAAA,CAAAwB,aAAA,CAAAI,YAAA,eACxB;IAItCvC,EAAA,CAAAM,SAAA,GAA6D;IAC7FN,EADgC,CAAAuB,WAAA,eAAAZ,MAAA,CAAAwB,aAAA,CAAAI,YAAA,eAA6D,uBAAA5B,MAAA,CAAAwB,aAAA,CAAAI,YAAA,eACxB;IAKrEvC,EAAA,CAAAM,SAAA,GAAqE;IACrEN,EADA,CAAAuB,WAAA,uBAAAZ,MAAA,CAAAwB,aAAA,CAAAI,YAAA,eAAqE,wBAAA5B,MAAA,CAAAwB,aAAA,CAAAI,YAAA,eACC;IAc3DvC,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAyG,SAAA,CAAuB;IAGHpH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,KAA+B;IAcjBtD,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA+F,cAAA,CAAApD,MAAA,OAAwC;IAMnFtD,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAuG,kBAAA,oBAAA5F,MAAA,CAAA+E,WAAA,QAAA/E,MAAA,CAAAmC,QAAA,aAAAnC,MAAA,CAAA6F,IAAA,CAAAC,GAAA,CAAA9F,MAAA,CAAA+E,WAAA,GAAA/E,MAAA,CAAAmC,QAAA,EAAAnC,MAAA,CAAA+F,cAAA,CAAApD,MAAA,iBAAA3C,MAAA,CAAA+F,cAAA,CAAApD,MAAA,aAGF;IAaatD,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAyG,SAAA,CAAuB;IAMhCpH,EAAA,CAAAM,SAAA,GAA0E;IAC1EN,EADA,CAAAuB,WAAA,eAAAZ,MAAA,CAAAuH,SAAA,qBAAAvH,MAAA,CAAAwH,aAAA,WAA0E,iBAAAxH,MAAA,CAAAuH,SAAA,qBAAAvH,MAAA,CAAAwH,aAAA,YACG;IAO7EnI,EAAA,CAAAM,SAAA,GAA6E;IAC7EN,EADA,CAAAuB,WAAA,eAAAZ,MAAA,CAAAuH,SAAA,wBAAAvH,MAAA,CAAAwH,aAAA,WAA6E,iBAAAxH,MAAA,CAAAuH,SAAA,wBAAAvH,MAAA,CAAAwH,aAAA,YACG;IAOhFnI,EAAA,CAAAM,SAAA,GAAsE;IACtEN,EADA,CAAAuB,WAAA,eAAAZ,MAAA,CAAAuH,SAAA,iBAAAvH,MAAA,CAAAwH,aAAA,WAAsE,iBAAAxH,MAAA,CAAAuH,SAAA,iBAAAvH,MAAA,CAAAwH,aAAA,YACG;IAOzEnI,EAAA,CAAAM,SAAA,GAAgF;IAChFN,EADA,CAAAuB,WAAA,eAAAZ,MAAA,CAAAuH,SAAA,2BAAAvH,MAAA,CAAAwH,aAAA,WAAgF,iBAAAxH,MAAA,CAAAuH,SAAA,2BAAAvH,MAAA,CAAAwH,aAAA,YACG;IAOnFnI,EAAA,CAAAM,SAAA,GAA8E;IAC9EN,EADA,CAAAuB,WAAA,eAAAZ,MAAA,CAAAuH,SAAA,yBAAAvH,MAAA,CAAAwH,aAAA,WAA8E,iBAAAxH,MAAA,CAAAuH,SAAA,yBAAAvH,MAAA,CAAAwH,aAAA,YACG;IAgBnEnI,EAAA,CAAAM,SAAA,GAAoB;IAAAN,EAApB,CAAAI,UAAA,YAAAO,MAAA,CAAAyH,eAAA,CAAoB,iBAAAzH,MAAA,CAAA0H,cAAA,CAAuB;IA8D3BrI,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAsF,UAAA,KAAoB;;;;;IAmE5DjG,EAHN,CAAAC,cAAA,eAAoG,cACzF,mBACO,cACY;IACtBD,EAAA,CAAAoB,SAAA,aAA6C;IAC7CpB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IAIrBF,EAJqB,CAAAG,YAAA,EAAI,EACb,EACO,EACP,EACN;;;;;IAQIH,EALV,CAAAC,cAAA,eAA8C,cACnC,mBACO,cACY,eACoB,gBAClB;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAE,MAAA,gEAAY;IAIpCF,EAJoC,CAAAG,YAAA,EAAI,EAC5B,EACO,EACP,EACN;;;;;IASJH,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,2BAAAI,MAAA,CAAA0C,cAAA,CAAAC,MAAA,yBACF;;;;;IACAtD,EAAA,CAAAC,cAAA,WAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,QAAAI,MAAA,CAAA2H,wBAAA,CAAAC,IAAA,MACF;;;;;IA+BQvI,EAAA,CAAAC,cAAA,gBACwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAuG,kBAAA,MAAAiC,SAAA,CAAAzD,UAAA,QAAAyD,SAAA,CAAAxD,aAAA,OAAAwD,SAAA,CAAAvD,MAAA,QACF;;;;;IACAjF,EAAA,CAAAC,cAAA,eAA4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gBAAAI,MAAA,CAAA0C,cAAA,CAAAC,MAAA,kBACF;;;;;IARFtD,EADF,CAAAC,cAAA,eAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,eAAqC;IAKnCD,EAJA,CAAAqB,UAAA,IAAAoH,gEAAA,oBACwC,IAAAC,gEAAA,oBAGoB;IAIhE1I,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAVAH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAO,kBAAA,sDAAAI,MAAA,CAAA0C,cAAA,CAAAC,MAAA,8BAAyC;IAEnBtD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA0C,cAAA,CAAAsF,KAAA,QAAgC;IAIjD3I,EAAA,CAAAM,SAAA,EAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,MAAgC;;;;;;IAiBnCtD,EAAA,CAAAC,cAAA,uBAC+B;IADiBD,EAAA,CAAA8B,gBAAA,2BAAA8G,+HAAA5G,MAAA;MAAA,MAAA6G,SAAA,GAAA7I,EAAA,CAAAe,aAAA,CAAA+H,IAAA,EAAA5E,SAAA;MAAAlE,EAAA,CAAAkC,kBAAA,CAAA2G,SAAA,CAAA1E,QAAA,EAAAnC,MAAA,MAAA6G,SAAA,CAAA1E,QAAA,GAAAnC,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAA4B;IAE1EhC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;;;;IAHkCH,EAAA,CAAAmD,gBAAA,YAAA0F,SAAA,CAAA1E,QAAA,CAA4B;IAC1EnE,EAAA,CAAAI,UAAA,cAAAyI,SAAA,CAAA/D,QAAA,CAA4B;IAC5B9E,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAA+I,kBAAA,MAAAF,SAAA,CAAA9D,UAAA,QAAA8D,SAAA,CAAA7D,aAAA,OACF;;;;;IAJFhF,EAAA,CAAAC,cAAA,eAAyD;IACvDD,EAAA,CAAAqB,UAAA,IAAA2H,yFAAA,2BAC+B;IAGjChJ,EAAA,CAAAG,YAAA,EAAM;;;;IAJ2BH,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAI,UAAA,YAAA6I,SAAA,CAAAC,MAAA,CAAe;;;;;;IAJhDlJ,EADF,CAAAC,cAAA,eAAmF,uBACS;IAA7ED,EAAA,CAAA8B,gBAAA,2BAAAqH,2GAAAnH,MAAA;MAAA,MAAAiH,SAAA,GAAAjJ,EAAA,CAAAe,aAAA,CAAAqI,IAAA,EAAAlF,SAAA;MAAAlE,EAAA,CAAAkC,kBAAA,CAAA+G,SAAA,CAAA9E,QAAA,EAAAnC,MAAA,MAAAiH,SAAA,CAAA9E,QAAA,GAAAnC,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAA4B;IAAChC,EAAA,CAAAa,UAAA,2BAAAsI,2GAAA;MAAA,MAAAF,SAAA,GAAAjJ,EAAA,CAAAe,aAAA,CAAAqI,IAAA,EAAAlF,SAAA;MAAA,MAAAvD,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBP,MAAA,CAAA0I,sBAAA,CAAAJ,SAAA,CAA6B;IAAA,EAAC;IACvFjJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAqB,UAAA,IAAAiI,2EAAA,mBAAyD;IAM3DtJ,EAAA,CAAAG,YAAA,EAAM;;;;IATSH,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAAmD,gBAAA,YAAA8F,SAAA,CAAA9E,QAAA,CAA4B;IACvCnE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAA+I,kBAAA,MAAAE,SAAA,CAAAM,WAAA,SAAAN,SAAA,CAAAC,MAAA,CAAA5F,MAAA,cACF;IACmCtD,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAA6I,SAAA,CAAA9E,QAAA,CAAoB;;;;;IAL3DnE,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAqB,UAAA,IAAAmI,qEAAA,mBAAmF;IAWrFxJ,EAAA,CAAAG,YAAA,EAAM;;;;IAX2CH,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA2H,wBAAA,CAAAmB,MAAA,CAAkC;;;;;;IAJnFzJ,EADF,CAAAC,cAAA,UAAyC,uBACa;IAAvCD,EAAA,CAAA8B,gBAAA,2BAAA4H,+FAAA1H,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAA4I,IAAA;MAAA,MAAAhJ,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAiJ,aAAA,CAAAC,UAAA,EAAA7H,MAAA,MAAArB,MAAA,CAAAiJ,aAAA,CAAAC,UAAA,GAAA7H,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAsC;IACjDhC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAqB,UAAA,IAAAyI,+DAAA,mBAAgF;IAalF9J,EAAA,CAAAG,YAAA,EAAM;;;;IAhBSH,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAiJ,aAAA,CAAAC,UAAA,CAAsC;IACjD7J,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gCAAAI,MAAA,CAAAoJ,eAAA,CAAAzG,MAAA,cACF;IACmBtD,EAAA,CAAAM,SAAA,EAA2D;IAA3DN,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAiJ,aAAA,CAAAC,UAAA,IAAAlJ,MAAA,CAAA2H,wBAAA,CAA2D;;;;;IAqBlFtI,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAxCH,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAO,kBAAA,MAAAI,MAAA,CAAA0C,cAAA,CAAAC,MAAA,yBAAiC;;;;;;IA1E7EtD,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,iCACA;IAGAF,EAHA,CAAAqB,UAAA,IAAA2I,yDAAA,oBAA6D,IAAAC,yDAAA,oBAGS;IAGxEjK,EAAA,CAAAG,YAAA,EAAiB;IAIbH,EAHJ,CAAAC,cAAA,mBAAc,eAEY,YACf;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAEvDH,EADF,CAAAC,cAAA,gBAAuC,0BACJ;IAC/BD,EAAA,CAAAoB,SAAA,mBAAoD;IACpDpB,EAAA,CAAAC,cAAA,kBACwC;IAAtCD,EAAA,CAAA8B,gBAAA,2BAAAoI,mFAAAlI,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAoJ,IAAA;MAAA,MAAAxJ,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAiJ,aAAA,CAAAQ,SAAA,EAAApI,MAAA,MAAArB,MAAA,CAAAiJ,aAAA,CAAAQ,SAAA,GAAApI,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAqC;IADvChC,EAAA,CAAAG,YAAA,EACwC;IACxCH,EAAA,CAAAoB,SAAA,4BAAmE;IACrEpB,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,iBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,0BAAiC;IAC/BD,EAAA,CAAAoB,SAAA,mBAAoD;IACpDpB,EAAA,CAAAC,cAAA,kBACsC;IAApCD,EAAA,CAAA8B,gBAAA,2BAAAuI,mFAAArI,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAoJ,IAAA;MAAA,MAAAxJ,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAiJ,aAAA,CAAAU,OAAA,EAAAtI,MAAA,MAAArB,MAAA,CAAAiJ,aAAA,CAAAU,OAAA,GAAAtI,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAmC;IADrChC,EAAA,CAAAG,YAAA,EACsC;IACtCH,EAAA,CAAAoB,SAAA,4BAAiE;IAGvEpB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAwB,aACf;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnBH,EAAA,CAAAC,cAAA,gBAA+B;IAgB7BD,EAdA,CAAAqB,UAAA,KAAAkJ,yDAAA,mBAAgE,KAAAC,yDAAA,mBAcvB;IAoB/CxK,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAmD,mBACa;IAAvBD,EAAA,CAAAa,UAAA,mBAAA4J,4EAAA;MAAA,MAAAC,OAAA,GAAA1K,EAAA,CAAAe,aAAA,CAAAoJ,IAAA,EAAAQ,SAAA;MAAA,MAAAhK,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAiK,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAAC1K,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzEH,EAAA,CAAAC,cAAA,mBAA6D;IAA7BD,EAAA,CAAAa,UAAA,mBAAAgK,4EAAA;MAAA,MAAAH,OAAA,GAAA1K,EAAA,CAAAe,aAAA,CAAAoJ,IAAA,EAAAQ,SAAA;MAAA,MAAAhK,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAmK,aAAA,CAAAJ,OAAA,CAAkB;IAAA,EAAC;IAC1D1K,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAqB,UAAA,KAAA0J,0DAAA,oBAAwC;IAG9C/K,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;;;IA3ECH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,KAA+B;IAG/BtD,EAAA,CAAAM,SAAA,EAA6D;IAA7DN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA2H,wBAAA,IAAA3H,MAAA,CAAA0C,cAAA,CAAAC,MAAA,OAA6D;IAWftD,EAAA,CAAAM,SAAA,IAA+B;IAA/BN,EAAA,CAAAI,UAAA,iBAAA4K,kBAAA,CAA+B;IAC5EhL,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAiJ,aAAA,CAAAQ,SAAA,CAAqC;IAMQpK,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,iBAAA6K,gBAAA,CAA6B;IAC1EjL,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAiJ,aAAA,CAAAU,OAAA,CAAmC;IAWjCtK,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,KAA+B;IAc/BtD,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,OAAiC;IAyBlCtD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,KAA+B;;;;;IAO5CtD,EAAA,CAAAC,cAAA,mBAA+C;IAM7CD,EALA,CAAAoB,SAAA,qBACiB,mBAGF,0BAGE;IACnBpB,EAAA,CAAAG,YAAA,EAAU;;;;;;IAKRH,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GACE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mDAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,wBAA2B,eACyB,iBACJ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC7E;IACRH,EAAA,CAAAC,cAAA,0BAA4B;IAC1BD,EAAA,CAAAoB,SAAA,mBAAoD;IACpDpB,EAAA,CAAAC,cAAA,kBAC8E;IAAvDD,EAAA,CAAA8B,gBAAA,2BAAAoJ,mFAAAlJ,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAoK,IAAA;MAAA,MAAAxK,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAyK,uBAAA,CAAAtH,gBAAA,EAAA9B,MAAA,MAAArB,MAAA,CAAAyK,uBAAA,CAAAtH,gBAAA,GAAA9B,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAsD;IAD7EhC,EAAA,CAAAG,YAAA,EAC8E;IAC9EH,EAAA,CAAAoB,SAAA,4BAAoE;IACtEpB,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,kBAAkD;IAACD,EAAA,CAAAE,MAAA,WACnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAoB,SAAA,mBAAoD;IACpDpB,EAAA,CAAAC,cAAA,kBAC4E;IAArDD,EAAA,CAAA8B,gBAAA,2BAAAuJ,mFAAArJ,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAoK,IAAA;MAAA,MAAAxK,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAyK,uBAAA,CAAArH,cAAA,EAAA/B,MAAA,MAAArB,MAAA,CAAAyK,uBAAA,CAAArH,cAAA,GAAA/B,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAoD;IAD3EhC,EAAA,CAAAG,YAAA,EAC4E;IAC5EH,EAAA,CAAAoB,SAAA,4BAAkE;IAGxEpB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAsD,mBACiB;IAAvBD,EAAA,CAAAa,UAAA,mBAAAyK,4EAAA;MAAA,MAAAC,OAAA,GAAAvL,EAAA,CAAAe,aAAA,CAAAoK,IAAA,EAAAR,SAAA;MAAA,MAAAhK,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAiK,OAAA,CAAAW,OAAA,CAAY;IAAA,EAAC;IAACvL,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,mBAAoE;IAAxBD,EAAA,CAAAa,UAAA,mBAAA2K,4EAAA;MAAA,MAAAD,OAAA,GAAAvL,EAAA,CAAAe,aAAA,CAAAoK,IAAA,EAAAR,SAAA;MAAA,MAAAhK,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA8K,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IAACvL,EAAA,CAAAE,MAAA,oBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAChE,EACT;;;;;;IA3B6BH,EAAA,CAAAM,SAAA,GACE;IADFN,EAAA,CAAA+I,kBAAA,KAAApI,MAAA,CAAAyK,uBAAA,CAAArG,UAAA,SAAApE,MAAA,CAAAyK,uBAAA,CAAAnG,MAAA,MACE;IAQoCjF,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,iBAAAsL,mBAAA,CAAgC;IAC9E1L,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAyK,uBAAA,CAAAtH,gBAAA,CAAsD;IAOV9D,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAI,UAAA,iBAAAuL,iBAAA,CAA8B;IAC1E3L,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAyK,uBAAA,CAAArH,cAAA,CAAoD;;;ADldrF,OAAM,MAAO6H,0BAA2B,SAAQ9L,aAAa;EAK3D+L,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA6C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAUtD;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAxJ,eAAe,GAAa,EAAE;IAE9B;IACA,KAAAjB,aAAa,GAAkB;MAC7BC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfmK,cAAc,EAAE;KACjB;IAED;IACA,KAAAjD,aAAa,GAAkB;MAC7BQ,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbT,UAAU,EAAE,IAAI;MAChBiD,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClB1J,cAAc,EAAE;KACjB;IAED,KAAAiF,wBAAwB,GAAyB,IAAI;IAErD;IACA,KAAAyB,eAAe,GAAqB,EAAE;IACtC,KAAArD,cAAc,GAAqB,EAAE;IACrC,KAAA0B,eAAe,GAAqB,EAAE;IACtC,KAAA/E,cAAc,GAAqB,EAAE;IACrC,KAAA+D,SAAS,GAAY,KAAK;IAC1B,KAAA4F,OAAO,GAAY,KAAK;IAExB;IACA,KAAAtH,WAAW,GAAW,CAAC;IACd,KAAA5C,QAAQ,GAAW,EAAE;IAC9B,KAAAmD,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAiC,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAmB,KAAK;IAErC;IACA,KAAA3B,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAhF,mBAAmB,GAAY,KAAK;IACpC,KAAA2E,UAAU,GAAW,CAAC;IAtFpB,IAAI,CAACiF,uBAAuB,GAAG;MAC7BtH,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBkB,MAAM,EAAE/B,SAAS;MACjB6B,UAAU,EAAE,EAAE;MACdD,QAAQ,EAAE5B;KACX;IAED,IAAI,CAACmJ,aAAa,CAACY,OAAO,EAAE,CAACC,IAAI,CAC/B7N,GAAG,CAAE8N,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAACd,eAAe,GAAGa,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAyESC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAACnB,gBAAgB,CAAC,CAAC,CAAC;MAC/CzI,gBAAgB,EAAEZ,SAAS;MAC3Ba,cAAc,EAAEb;KACjB;IACD,IAAI,CAACyK,gBAAgB,EAAE;EACzB;EAEA;EACAxM,qBAAqBA,CAAA;IACnB,IAAI,CAACK,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEAC,gBAAgBA,CAAA;IACd,OAAO,CAAC,EAAE,IAAI,CAACU,aAAa,CAACC,aAAa,IACxC,IAAI,CAACD,aAAa,CAACI,YAAY,IAC/B,IAAI,CAACJ,aAAa,CAACO,WAAW,CAAC;EACnC;EAEA9B,qBAAqBA,CAAA;IACnB,IAAIgN,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAACzL,aAAa,CAACC,aAAa,EAAEwL,KAAK,EAAE;IAC7C,IAAI,IAAI,CAACzL,aAAa,CAACI,YAAY,EAAEqL,KAAK,EAAE;IAC5C,IAAI,IAAI,CAACzL,aAAa,CAACO,WAAW,EAAEkL,KAAK,EAAE;IAC3C,OAAOA,KAAK;EACd;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACL,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvDC,qBAAqB,EAAE,IAAI,CAACnB,gBAAgB,CAAC,CAAC,CAAC;MAC/CzI,gBAAgB,EAAEZ,SAAS;MAC3Ba,cAAc,EAAEb;KACjB;IACD,IAAI,CAAC0J,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC/K,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACM,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfmK,cAAc,EAAE;KACjB;IACD,IAAI,CAACxK,QAAQ,EAAE;EACjB;EAEA0E,cAAcA,CAAC+G,MAAc;IAC3B,IAAI,IAAI,CAAC3L,aAAa,CAACI,YAAY,KAAKuL,MAAM,EAAE;MAC9C,IAAI,CAAC3L,aAAa,CAACI,YAAY,GAAG,EAAE;IACtC,CAAC,MAAM;MACL,IAAI,CAACJ,aAAa,CAACI,YAAY,GAAGuL,MAAM;IAC1C;IACA,IAAI,CAACzL,QAAQ,EAAE;EACjB;EAEAsB,cAAcA,CAAA;IACZ,IAAI,CAACN,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC+D,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC2C,eAAe,CAACgE,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC7J,QAAQ,GAAG,KAAK,CAAC;EAC/D;EAEAiB,aAAaA,CAAC4I,KAAqB;IACjC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IACzC,QAAQF,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,oBAAoB;MAC1C,KAAK,SAAS;QAAE,OAAO,cAAc;MACrC,KAAK,SAAS;QAAE,OAAO,qBAAqB;MAC5C,KAAK,SAAS;QAAE,OAAO,wBAAwB;MAC/C,KAAK,UAAU;QAAE,OAAO,YAAY;MACpC;QAAS,OAAO,wBAAwB;IAC1C;EACF;EAEAzH,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACF,UAAU,IAAI,IAAI,CAACA,UAAU,IAAI,CAAC,IAAI,IAAI,CAACA,UAAU,IAAI,IAAI,CAACF,UAAU,EAAE;MACjF,IAAI,CAACR,QAAQ,CAAC,IAAI,CAACU,UAAU,CAAC;IAChC;EACF;EAIAtB,SAASA,CAACqJ,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACrJ,QAAQ,EAAE;MACjB,IAAI,CAACsG,uBAAuB,GAAG;QAC7B,GAAG+C,IAAI;QACPrK,gBAAgB,EAAEqK,IAAI,CAACrK,gBAAgB,GAAG,IAAIsK,IAAI,CAACD,IAAI,CAACrK,gBAAgB,CAAC,GAAGZ,SAAS;QACrFa,cAAc,EAAEoK,IAAI,CAACpK,cAAc,GAAG,IAAIqK,IAAI,CAACD,IAAI,CAACpK,cAAc,CAAC,GAAGb;OACvE;MACD,IAAI,CAAC6I,aAAa,CAACsC,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO3O,MAAM,CAAC2O,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEA/C,QAAQA,CAACyC,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAACxC,KAAK,CAACyC,aAAa,CAACpL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC0I,OAAO,CAAC2C,aAAa,CAAC,IAAI,CAAC1C,KAAK,CAACyC,aAAa,CAAC;MACpD;IACF;IACA,MAAME,KAAK,GAAG;MACZ9J,QAAQ,EAAE,IAAI,CAACsG,uBAAuB,CAACtG,QAAQ;MAC/ChB,gBAAgB,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAAClD,uBAAuB,CAACtH,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAACuK,UAAU,CAAC,IAAI,CAAClD,uBAAuB,CAACrH,cAAc;KAC5E;IAED,IAAI,CAACmI,aAAa,CAAC2C,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAACtB,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC4B,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC/C,OAAO,CAACgD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAvB,gBAAgBA,CAAA;IACd,IAAI,CAACxB,iBAAiB,CAACgD,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAAC5B,IAAI,CAC7E7N,GAAG,CAAC8N,GAAG,IAAG;MACR,MAAMiC,OAAO,GAAGjC,GAAG,CAACkC,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAAC9L,MAAM,IAAI6J,GAAG,CAAC4B,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChDhP,cAAc,EAAEgP,KAAK,CAAChP,cAAc;UACpCiP,GAAG,EAAED,KAAK,CAACC;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAACnD,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAIoD,KAAK,GAAG,IAAI,CAACJ,oBAAoB,CAACK,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAACnD,eAAe,CAAC;UAC1F,IAAI,CAACkB,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC6B,oBAAoB,CAACI,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAAClC,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC6B,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMO,WAAW,GAAG,IAAI,CAACrC,WAAW,EAAEC,kBAAkB,EAAEgC,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACZ,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAAC3B,SAAS,EAAE;EACf;EAEAwC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAAChC,OAAO,CAACkC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACnC,OAAO,CAAEC,KAAU,IAAI;QACvC,MAAMmC,KAAK,GAAGnC,KAAK,CAAC/I,MAAM;QAC1B,IAAI,CAAC+K,SAAS,CAACG,KAAK,CAAC,EAAE;UAAE;UACvBH,SAAS,CAACG,KAAK,CAAC,GAAG,EAAE;QACvB;QACAH,SAAS,CAACG,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBrL,UAAU,EAAEkL,SAAS,CAAClL,UAAU;UAChCC,aAAa,EAAEgJ,KAAK,CAAChJ,aAAa,IAAI,KAAK;UAC3CF,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ;UACxBG,MAAM,EAAE+I,KAAK,CAAC/I,MAAM;UACpBnB,gBAAgB,EAAEkK,KAAK,CAAClK,gBAAgB;UACxCC,cAAc,EAAEiK,KAAK,CAACjK;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC0F,MAAM,CAAC9B,IAAI,CAAC,CAAC0I,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAAC9G,MAAM,CAAC8F,GAAG,CAAEY,KAAU,IAAI;MAChE,OAAO,IAAI,CAACK,UAAU,CAACjB,GAAG,CAAEU,SAAc,IAAI;QAC5C,MAAMjC,KAAK,GAAGgC,SAAS,CAACG,KAAK,CAAC,CAACM,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAAC3L,UAAU,KAAKkL,SAAS,CAAC;QAC5F,OAAOjC,KAAK,IAAI;UACdjJ,UAAU,EAAEkL,SAAS;UACrBjL,aAAa,EAAE,KAAK;UACpBF,QAAQ,EAAE,IAAI;UACdG,MAAM,EAAEkL,KAAK;UACbrM,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOwM,MAAM;EACf;EAEAI,sBAAsBA,CAACZ,GAAU;IAC/B,MAAMa,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5Cd,GAAG,CAAChC,OAAO,CAACkC,SAAS,IAAG;MACtBa,aAAa,CAACC,GAAG,CAACd,SAAS,CAAClL,UAAU,CAAC;MACvCkL,SAAS,CAACC,OAAO,CAACnC,OAAO,CAAEC,KAAU,IAAI;QACvC4C,SAAS,CAACG,GAAG,CAAC/C,KAAK,CAAC/I,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACwE,MAAM,GAAGuH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLrH,MAAM,EAAEuH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC1D,WAAW,CAAC1J,gBAAgB,IAAI,IAAI,CAAC0J,WAAW,CAACzJ,cAAc,EAAE;MACxE,MAAMqG,SAAS,GAAG,IAAIgE,IAAI,CAAC,IAAI,CAACZ,WAAW,CAAC1J,gBAAgB,CAAC;MAC7D,MAAMwG,OAAO,GAAG,IAAI8D,IAAI,CAAC,IAAI,CAACZ,WAAW,CAACzJ,cAAc,CAAC;MACzD,IAAIqG,SAAS,IAAIE,OAAO,IAAIF,SAAS,GAAGE,OAAO,EAAE;QAC/C,IAAI,CAAC0B,OAAO,CAAC2C,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEA;EACAwC,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACC,cAAc,EAAE;IACrB;IACA,IAAI,CAACnC,kBAAkB,EAAE;EAC3B;EAEA;EACAmC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC5E,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC3C,eAAe,GAAG,EAAE;IACzB,IAAI,CAACrD,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC0B,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC/E,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAAClB,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfmK,cAAc,EAAE;KACjB;IAED;IACA,IAAI,CAACzF,SAAS,GAAG,KAAK;IACtB,IAAI,CAACwF,gBAAgB,GAAG,EAAE;IAE1B;IACA,IAAI,CAAClH,WAAW,GAAG,CAAC;IACpB,IAAI,CAACO,UAAU,GAAG,CAAC;IAEnB;IACA,IAAI,CAAC0G,eAAe,GAAG,EAAE;IACzB,IAAI,CAACvJ,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,CAAC8E,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC5B;EAEA8G,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC,IAAI,CAACzB,WAAW,CAACC,kBAAkB,EAAEgC,GAAG,EAAE;MAC7C,IAAI,CAACzC,OAAO,GAAG,KAAK;MACpB;IACF;IAEA,IAAI,CAACA,OAAO,GAAG,IAAI;IACnB,IAAI,CAACkE,cAAc,EAAE;IACrB,IAAI,CAAChF,aAAa,CAACqF,mCAAmC,CAAC;MACrDzC,IAAI,EAAE;QACJ0C,YAAY,EAAE,IAAI,CAAChE,WAAW,CAACC,kBAAkB,CAACgC,GAAG;QACrD3L,gBAAgB,EAAE,IAAI,CAAC0J,WAAW,CAAC1J,gBAAgB,GAAG,IAAI,CAACwK,UAAU,CAAC,IAAI,CAACd,WAAW,CAAC1J,gBAAgB,CAAC,GAAGZ,SAAS;QACpHa,cAAc,EAAE,IAAI,CAACyJ,WAAW,CAACzJ,cAAc,GAAG,IAAI,CAACuK,UAAU,CAAC,IAAI,CAACd,WAAW,CAACzJ,cAAc,CAAC,GAAGb;;KAExG,CAAC,CAACoK,SAAS,CAACH,GAAG,IAAG;MACjB,IAAI,CAACH,OAAO,GAAG,KAAK;MACpB,IAAIG,GAAG,CAACkC,OAAO,IAAIlC,GAAG,CAAC4B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACsC,gBAAgB,GAAGlE,GAAG,CAACkC,OAAO,GAAGlC,GAAG,CAACkC,OAAO,GAAG,EAAE;QACtD,IAAIlC,GAAG,CAACkC,OAAO,EAAE;UACf,IAAI,CAACgC,gBAAgB,GAAG,CAAC,GAAGlE,GAAG,CAACkC,OAAO,CAAC;UACxC,IAAI,CAACsB,sBAAsB,CAACxD,GAAG,CAACkC,OAAO,CAAC;UACxC,IAAI,CAACiC,mBAAmB,GAAG,IAAI,CAACxB,8BAA8B,CAAC3C,GAAG,CAACkC,OAAO,CAAC;UAC3E;UACA,IAAI,CAACoC,mBAAmB,CAACtE,GAAG,CAACkC,OAAO,CAAC;UACrC;UACA,IAAI,CAACqC,oBAAoB,CAACvE,GAAG,CAACkC,OAAO,CAAC;QACxC;MACF;IACF,CAAC,CAAC;EACJ;EAEA;EACAoC,mBAAmBA,CAACE,IAA6B;IAC/C,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAyC;IAEpEF,IAAI,CAAC5D,OAAO,CAACkC,SAAS,IAAG;MACvB,MAAM6B,SAAS,GAAG7B,SAAS,CAAClL,UAAU,IAAI,EAAE,CAAC,CAAC;MAE9CkL,SAAS,CAACC,OAAO,EAAEnC,OAAO,CAACC,KAAK,IAAG;QACjC,MAAM+D,YAAY,GAAG/D,KAAK,CAAChJ,aAAa,IAAI,KAAK,CAAC,CAAC;QACnD,MAAMmL,KAAK,GAAGnC,KAAK,CAAC/I,MAAM,IAAI,CAAC;QAE/B,IAAI,CAAC2M,WAAW,CAACI,GAAG,CAACD,YAAY,CAAC,EAAE;UAClCH,WAAW,CAACK,GAAG,CAACF,YAAY,EAAE,IAAIF,GAAG,EAA4B,CAAC;QACpE;QAEA,MAAMK,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACJ,YAAY,CAAE;QAE/C,IAAI,CAACG,QAAQ,CAACF,GAAG,CAAC7B,KAAK,CAAC,EAAE;UACxB+B,QAAQ,CAACD,GAAG,CAAC9B,KAAK,EAAE,EAAE,CAAC;QACzB;QAEA+B,QAAQ,CAACC,GAAG,CAAChC,KAAK,CAAE,CAACC,IAAI,CAAC;UACxBrL,UAAU,EAAE+M,SAAS;UAAE;UACvB9M,aAAa,EAAE+M,YAAY;UAAE;UAC7BjN,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ,IAAI,CAAC;UAC7BG,MAAM,EAAEkL,KAAK;UACbrM,gBAAgB,EAAEkK,KAAK,CAAClK,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEiK,KAAK,CAACjK,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACuI,cAAc,GAAGsE,KAAK,CAACC,IAAI,CAACW,WAAW,CAACxC,OAAO,EAAE,CAAC,CAACG,GAAG,CAAC,CAAC,CAACwC,YAAY,EAAEG,QAAQ,CAAC,KAAI;MACvF,MAAMzI,MAAM,GAAiBuH,KAAK,CAACC,IAAI,CAACiB,QAAQ,CAAC9C,OAAO,EAAE,CAAC,CACxDzH,IAAI,CAAC,CAAC,CAAC0I,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;MAAA,CAC1Bd,GAAG,CAAC,CAAC,CAAChG,WAAW,EAAEL,MAAM,CAAC,MAAM;QAC/BK,WAAW;QACXL,MAAM,EAAEA,MAAM,CAACvB,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAI;UAC3B;UACA,IAAID,CAAC,CAACtL,UAAU,KAAKuL,CAAC,CAACvL,UAAU,EAAE;YACjC,OAAOsL,CAAC,CAACtL,UAAU,CAACqN,aAAa,CAAC9B,CAAC,CAACvL,UAAU,CAAC;UACjD;UACA,OAAOsL,CAAC,CAACpL,MAAM,GAAGqL,CAAC,CAACrL,MAAM;QAC5B,CAAC,CAAC;QACFd,QAAQ,EAAE;OACX,CAAC,CAAC;MAEL,OAAO;QACLoE,IAAI,EAAEwJ,YAAY;QAClBtI,MAAM;QACNtF,QAAQ,EAAE;OACX;IACH,CAAC,CAAC,CAACwD,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC9H,IAAI,CAAC6J,aAAa,CAAC9B,CAAC,CAAC/H,IAAI,CAAC,CAAC;IAE/C;IACA,IAAI,CAACoE,eAAe,GAAG,IAAI,CAACD,cAAc,CAAC6C,GAAG,CAAC8C,EAAE,IAAIA,EAAE,CAAC9J,IAAI,CAAC;IAC7D,IAAI,CAAC+J,qBAAqB,EAAE;EAC9B;EAEA;EACAA,qBAAqBA,CAAA;IACnB,MAAM1B,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,IAAI,CAACnE,cAAc,CAACqB,OAAO,CAACwE,QAAQ,IAAG;MACrC,IAAI,CAAC,IAAI,CAAC3F,gBAAgB,IAAI2F,QAAQ,CAAChK,IAAI,KAAK,IAAI,CAACqE,gBAAgB,EAAE;QACrE2F,QAAQ,CAAC9I,MAAM,CAACsE,OAAO,CAACoC,KAAK,IAAG;UAC9BS,SAAS,CAACG,GAAG,CAACZ,KAAK,CAAC5G,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACnG,eAAe,GAAG4N,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC,CAACjJ,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpE;EAEA;EACAmC,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACnP,cAAc,CAAC0K,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC7J,QAAQ,GAAG,KAAK,CAAC;IAC5D,IAAI,CAACd,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC+D,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAAC1B,WAAW,GAAG,CAAC;IAEpB;IACA,IAAI,CAACvD,aAAa,CAACO,WAAW,GAAG,EAAE;IAEnC;IACA,IAAI,CAAC4P,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAACnQ,aAAa,CAAC0K,cAAc,GAAG,IAAI,CAACD,gBAAgB;IACzD,IAAI,CAACvK,QAAQ,EAAE;EACjB;EAIA;EACAoQ,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC/F,cAAc,CAACgG,MAAM,CAACH,QAAQ,IAAG;MAC3C;MACA,IAAI,IAAI,CAAC3F,gBAAgB,IAAI2F,QAAQ,CAAChK,IAAI,KAAK,IAAI,CAACqE,gBAAgB,EAAE;QACpE,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAACzK,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMuQ,OAAO,GAAG,IAAI,CAACxQ,aAAa,CAACC,aAAa,CAACwQ,WAAW,EAAE;QAC9D,MAAMC,gBAAgB,GAAGN,QAAQ,CAAC9I,MAAM,CAACqJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAACjH,MAAM,CAAC4J,IAAI,CAAC9E,KAAK,IACrBA,KAAK,CAACjJ,UAAU,CAAC6N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IAChD3E,KAAK,CAAChJ,aAAa,CAAC4N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,CACpD,CACF;QACD,IAAI,CAACE,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC1Q,aAAa,CAACI,YAAY,EAAE;QACnC,MAAMyQ,iBAAiB,GAAGT,QAAQ,CAAC9I,MAAM,CAACqJ,IAAI,CAAC3C,KAAK,IAClDA,KAAK,CAACjH,MAAM,CAAC4J,IAAI,CAAC9E,KAAK,IAAI,IAAI,CAACiF,mBAAmB,CAACjF,KAAK,CAAC,CAAC,CAC5D;QACD,IAAI,CAACgF,iBAAiB,EAAE;UACtB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC7Q,aAAa,CAACO,WAAW,EAAE;QAClC,MAAM6G,WAAW,GAAG2J,QAAQ,CAAC,IAAI,CAAC/Q,aAAa,CAACO,WAAW,CAAC;QAC5D,MAAMyQ,gBAAgB,GAAGZ,QAAQ,CAAC9I,MAAM,CAACqJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAAC5G,WAAW,KAAKA,WAAW,CAClC;QACD,IAAI,CAAC4J,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CAAC5D,GAAG,CAACgD,QAAQ,IAAG;MAChB;MACA,MAAMa,gBAAgB,GAAG;QAAE,GAAGb;MAAQ,CAAE;MACxCa,gBAAgB,CAAC3J,MAAM,GAAG8I,QAAQ,CAAC9I,MAAM,CAACiJ,MAAM,CAACvC,KAAK,IAAG;QACvD;QACA,IAAI,IAAI,CAAChO,aAAa,CAACO,WAAW,EAAE;UAClC,MAAM6G,WAAW,GAAG2J,QAAQ,CAAC,IAAI,CAAC/Q,aAAa,CAACO,WAAW,CAAC;UAC5D,IAAIyN,KAAK,CAAC5G,WAAW,KAAKA,WAAW,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QAEA;QACA,MAAM8J,cAAc,GAAGlD,KAAK,CAACjH,MAAM,CAAC4J,IAAI,CAAC9E,KAAK,IAAG;UAC/C;UACA,IAAI,IAAI,CAAC7L,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMuQ,OAAO,GAAG,IAAI,CAACxQ,aAAa,CAACC,aAAa,CAACwQ,WAAW,EAAE;YAC9D,IAAI,CAAC5E,KAAK,CAACjJ,UAAU,CAAC6N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAAC3E,KAAK,CAAChJ,aAAa,CAAC4N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAACxQ,aAAa,CAACI,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC0Q,mBAAmB,CAACjF,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOqF,cAAc;MACvB,CAAC,CAAC,CAAC9D,GAAG,CAACY,KAAK,IAAG;QACb;QACA,MAAMmD,aAAa,GAAG;UAAE,GAAGnD;QAAK,CAAE;QAClCmD,aAAa,CAACpK,MAAM,GAAGiH,KAAK,CAACjH,MAAM,CAACwJ,MAAM,CAAC1E,KAAK,IAAG;UACjD;UACA,IAAI,IAAI,CAAC7L,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMuQ,OAAO,GAAG,IAAI,CAACxQ,aAAa,CAACC,aAAa,CAACwQ,WAAW,EAAE;YAC9D,IAAI,CAAC5E,KAAK,CAACjJ,UAAU,CAAC6N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAAC3E,KAAK,CAAChJ,aAAa,CAAC4N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAACxQ,aAAa,CAACI,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC0Q,mBAAmB,CAACjF,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOsF,aAAa;MACtB,CAAC,CAAC;MAEF,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ;EAEA;EACQH,mBAAmBA,CAACjF,KAAqB;IAC/C,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IAEzC,QAAQ,IAAI,CAAC7L,aAAa,CAACI,YAAY;MACrC,KAAK,QAAQ;QACX,OAAOuL,MAAM,KAAK,QAAQ;MAC5B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,UAAU;MAC9B;QACE,OAAO,IAAI;MAAE;IACjB;EACF;EAEA;EACQG,cAAcA,CAACD,KAAqB;IAC1C,IAAI,CAACA,KAAK,CAAClJ,QAAQ,EAAE;MACnB,OAAO,UAAU;IACnB;IAEA;IACA,IAAI,CAACkJ,KAAK,CAAClK,gBAAgB,IAAI,CAACkK,KAAK,CAACjK,cAAc,IAClDiK,KAAK,CAAClK,gBAAgB,KAAK,EAAE,IAAIkK,KAAK,CAACjK,cAAc,KAAK,EAAE,EAAE;MAC9D,OAAO,SAAS;IAClB;IAEA,IAAI;MACF;MACA,MAAMwP,GAAG,GAAG,IAAInF,IAAI,EAAE;MACtB,MAAMoF,KAAK,GAAG,IAAIpF,IAAI,CAACmF,GAAG,CAACE,WAAW,EAAE,EAAEF,GAAG,CAACG,QAAQ,EAAE,EAAEH,GAAG,CAACI,OAAO,EAAE,CAAC;MAExE;MACA,IAAIvJ,SAAe;MACnB,IAAI4D,KAAK,CAAClK,gBAAgB,CAACiP,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxC3I,SAAS,GAAG,IAAIgE,IAAI,CAACJ,KAAK,CAAClK,gBAAgB,CAAC;MAC9C,CAAC,MAAM;QACLsG,SAAS,GAAG,IAAIgE,IAAI,CAACJ,KAAK,CAAClK,gBAAgB,GAAG,WAAW,CAAC;MAC5D;MAEA;MACA,IAAIwG,OAAa;MACjB,IAAI0D,KAAK,CAACjK,cAAc,CAACgP,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtCzI,OAAO,GAAG,IAAI8D,IAAI,CAACJ,KAAK,CAACjK,cAAc,CAAC;MAC1C,CAAC,MAAM;QACLuG,OAAO,GAAG,IAAI8D,IAAI,CAACJ,KAAK,CAACjK,cAAc,GAAG,WAAW,CAAC;MACxD;MAEA;MACA,IAAI6P,KAAK,CAACxJ,SAAS,CAACyJ,OAAO,EAAE,CAAC,IAAID,KAAK,CAACtJ,OAAO,CAACuJ,OAAO,EAAE,CAAC,EAAE;QAC1DC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAE;UACnCC,KAAK,EAAEhG,KAAK,CAAClK,gBAAgB;UAC7BmQ,GAAG,EAAEjG,KAAK,CAACjK,cAAc;UACzBmQ,OAAO,EAAElG,KAAK,CAAClJ;SAChB,CAAC;QACF,OAAO,SAAS;MAClB;MAEA;MACA,MAAMqP,aAAa,GAAG,IAAI/F,IAAI,CAAChE,SAAS,CAACqJ,WAAW,EAAE,EAAErJ,SAAS,CAACsJ,QAAQ,EAAE,EAAEtJ,SAAS,CAACuJ,OAAO,EAAE,CAAC;MAClG,MAAMS,WAAW,GAAG,IAAIhG,IAAI,CAAC9D,OAAO,CAACmJ,WAAW,EAAE,EAAEnJ,OAAO,CAACoJ,QAAQ,EAAE,EAAEpJ,OAAO,CAACqJ,OAAO,EAAE,CAAC;MAE1F;MACA,IAAIH,KAAK,GAAGW,aAAa,EAAE;QACzB,OAAO,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIX,KAAK,IAAIW,aAAa,IAAIX,KAAK,IAAIY,WAAW,EAAE;QACzD,OAAO,QAAQ,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,OAAO,SAAS,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,EAAErG,KAAK,CAAC;MACnD,OAAO,SAAS;IAClB;EACF;EAEApD,OAAOA,CAACsD,GAAQ;IACdA,GAAG,CAACgB,KAAK,EAAE;EACb;EAEAT,UAAUA,CAAA;IACR,IAAI,CAACxC,KAAK,CAACqI,KAAK,EAAE;IAClB,IAAI,CAACrI,KAAK,CAACsI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACnJ,uBAAuB,CAACtH,gBAAgB,CAAC;IAC9E,IAAI,CAACmI,KAAK,CAACsI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACnJ,uBAAuB,CAACrH,cAAc,CAAC;IAC5E,IAAI,CAACkI,KAAK,CAACuI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACpJ,uBAAuB,CAACtH,gBAAgB,EAAE,IAAI,CAACsH,uBAAuB,CAACrH,cAAc,CAAC;EACtI;EAEA0Q,gBAAgBA,CAAA;IACd,IAAI,CAACrI,MAAM,CAACsI,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAAClH,WAAW,EAAEC,kBAAkB,EAAEgC,GAAG,EAAE,CAAC,CAAC;EACnG;EAEA;EACAhM,gBAAgBA,CAAC8O,QAAwB;IACvC,IAAI,CAACjK,wBAAwB,GAAGiK,QAAQ,IAAI,IAAI;IAEhD;IACA,MAAMoC,iBAAiB,GAAG,IAAI,CAACtR,cAAc,CAACC,MAAM,GAAG,CAAC;IAExD,IAAI,CAACsG,aAAa,GAAG;MACnBQ,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbT,UAAU,EAAE,CAAC8K,iBAAiB,IAAI,CAACpC,QAAQ;MAC3CzF,iBAAiB,EAAEyF,QAAQ,GAAG,CAACA,QAAQ,CAAChK,IAAI,CAAC,GAAG,EAAE;MAClDwE,cAAc,EAAE,EAAE;MAClB1J,cAAc,EAAE;KACjB;IAED;IACA,IAAIkP,QAAQ,EAAE;MACZA,QAAQ,CAAC9I,MAAM,CAACsE,OAAO,CAACoC,KAAK,IAAG;QAC9BA,KAAK,CAAChM,QAAQ,GAAG,KAAK;QACtBgM,KAAK,CAACjH,MAAM,CAAC6E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC7J,QAAQ,GAAG,KAAK,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAAC4H,aAAa,CAACsC,IAAI,CAAC,IAAI,CAACuG,kBAAkB,CAAC;EAClD;EAEA;EACAvL,sBAAsBA,CAAC8G,KAAiB;IACtC,IAAIA,KAAK,CAAChM,QAAQ,EAAE;MAClBgM,KAAK,CAACjH,MAAM,CAAC6E,OAAO,CAACC,KAAK,IAAG;QAC3B,IAAIA,KAAK,CAAClJ,QAAQ,EAAE;UAClBkJ,KAAK,CAAC7J,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLgM,KAAK,CAACjH,MAAM,CAAC6E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC7J,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA;EACA2G,aAAaA,CAACoD,GAAQ;IACpB;IACA,IAAI,CAACjC,KAAK,CAACqI,KAAK,EAAE;IAClB,IAAI,CAACrI,KAAK,CAACsI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC3K,aAAa,CAACQ,SAAS,CAAC;IAC3D,IAAI,CAAC6B,KAAK,CAACsI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC3K,aAAa,CAACU,OAAO,CAAC;IACzD,IAAI,CAAC2B,KAAK,CAACuI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC5K,aAAa,CAACQ,SAAS,EAAE,IAAI,CAACR,aAAa,CAACU,OAAO,CAAC;IAElG,IAAI,IAAI,CAAC2B,KAAK,CAACyC,aAAa,CAACpL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC0I,OAAO,CAAC2C,aAAa,CAAC,IAAI,CAAC1C,KAAK,CAACyC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMmG,cAAc,GAAU,EAAE;IAEhC,IAAI,IAAI,CAACjL,aAAa,CAACC,UAAU,EAAE;MACjC;MACA,IAAI,CAACE,eAAe,CAACgE,OAAO,CAACC,KAAK,IAAG;QACnC,IAAIA,KAAK,CAAClJ,QAAQ,EAAE;UAClB+P,cAAc,CAACzE,IAAI,CAAC;YAClBtL,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ;YACxBhB,gBAAgB,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAAC1E,aAAa,CAACQ,SAAS,CAAC;YAC/DrG,cAAc,EAAE,IAAI,CAACuK,UAAU,CAAC,IAAI,CAAC1E,aAAa,CAACU,OAAO;WAC3D,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACjH,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACD,cAAc,CAAC0K,OAAO,CAACC,KAAK,IAAG;UAClC,IAAIA,KAAK,CAAClJ,QAAQ,EAAE;YAClB+P,cAAc,CAACzE,IAAI,CAAC;cAClBtL,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ;cACxBhB,gBAAgB,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAAC1E,aAAa,CAACQ,SAAS,CAAC;cAC/DrG,cAAc,EAAE,IAAI,CAACuK,UAAU,CAAC,IAAI,CAAC1E,aAAa,CAACU,OAAO;aAC3D,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAAChC,wBAAwB,EAAE;QACxC;QACA,IAAI,CAACA,wBAAwB,CAACmB,MAAM,CAACsE,OAAO,CAACoC,KAAK,IAAG;UACnDA,KAAK,CAACjH,MAAM,CAAC6E,OAAO,CAACC,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAAC7J,QAAQ,IAAI6J,KAAK,CAAClJ,QAAQ,EAAE;cACpC+P,cAAc,CAACzE,IAAI,CAAC;gBAClBtL,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ;gBACxBhB,gBAAgB,EAAE,IAAI,CAACwK,UAAU,CAAC,IAAI,CAAC1E,aAAa,CAACQ,SAAS,CAAC;gBAC/DrG,cAAc,EAAE,IAAI,CAACuK,UAAU,CAAC,IAAI,CAAC1E,aAAa,CAACU,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIuK,cAAc,CAACvR,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC0I,OAAO,CAAC2C,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAACzC,aAAa,CAAC2C,oCAAoC,CAAC;MACtDC,IAAI,EAAE+F;KACP,CAAC,CAACvH,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC4B,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC/C,OAAO,CAACgD,aAAa,CAAC,QAAQ6F,cAAc,CAACvR,MAAM,WAAW,CAAC;QACpE;QACA,IAAI,CAACD,cAAc,CAAC0K,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC7J,QAAQ,GAAG,KAAK,CAAC;QAC5D,IAAI,CAACd,cAAc,GAAG,EAAE;QACxB,IAAI,CAAC+D,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC6H,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA;EACA/J,cAAcA,CAAC6I,KAAqB;IAClC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IACzC,OAAO,UAAUF,MAAM,EAAE;EAC3B;EAEA;EACAgH,eAAeA,CAAC9G,KAAqB;IACnC,IAAIA,KAAK,CAAClJ,QAAQ,EAAE;MAClB;MACA,IAAI,CAACD,SAAS,CAAC,IAAI,CAACkQ,MAAM,EAAE/G,KAAK,CAAC;IACpC;EACF;EAEA;EACA0D,oBAAoBA,CAACC,IAA6B;IAChD,IAAI,CAAC5H,eAAe,GAAG,EAAE;IAEzB4H,IAAI,CAAC5D,OAAO,CAACkC,SAAS,IAAG;MACvB,MAAM6B,SAAS,GAAG7B,SAAS,CAAClL,UAAU,IAAI,EAAE;MAE5CkL,SAAS,CAACC,OAAO,EAAEnC,OAAO,CAACC,KAAK,IAAG;QACjC,IAAI,CAACjE,eAAe,CAACqG,IAAI,CAAC;UACxBrL,UAAU,EAAE+M,SAAS;UACrB9M,aAAa,EAAEgJ,KAAK,CAAChJ,aAAa,IAAI,KAAK;UAC3CF,QAAQ,EAAEkJ,KAAK,CAAClJ,QAAQ,IAAI,CAAC;UAC7BG,MAAM,EAAE+I,KAAK,CAAC/I,MAAM,IAAI,CAAC;UACzBnB,gBAAgB,EAAEkK,KAAK,CAAClK,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEiK,KAAK,CAACjK,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC9B,QAAQ,EAAE;IAEf;IACA,IAAI,CAAC2S,iBAAiB,EAAE;EAC1B;EAEA;EACQA,iBAAiBA,CAAA;IACvB,MAAMC,YAAY,GAAG;MACnBC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACV,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAACtL,eAAe,CAACgE,OAAO,CAACC,KAAK,IAAG;MACnC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;MACzC,IAAIiH,YAAY,CAACK,cAAc,CAACxH,MAAM,CAAC,EAAE;QACvCmH,YAAY,CAACnH,MAAmC,CAAC,EAAE;MACrD;IACF,CAAC,CAAC;IAEFgG,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAEN,YAAY,CAAC;IAClCnB,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAE,IAAInH,IAAI,EAAE,CAACoH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;EAIA;EACApT,QAAQA,CAAA;IACN;IACA,MAAMqT,qBAAqB,GAAG,IAAI,CAACrS,cAAc,CAACkM,GAAG,CAACvB,KAAK,IAAIA,KAAK,CAAClJ,QAAQ,CAAC;IAE9E,IAAI,CAAC4B,cAAc,GAAG,IAAI,CAACqD,eAAe,CAAC2I,MAAM,CAAC1E,KAAK,IAAG;MACxD;MACA,IAAI,IAAI,CAAC7L,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMuQ,OAAO,GAAG,IAAI,CAACxQ,aAAa,CAACC,aAAa,CAACwQ,WAAW,EAAE;QAC9D,IAAI,CAAC5E,KAAK,CAACjJ,UAAU,CAAC6N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAAC3E,KAAK,CAAChJ,aAAa,CAAC4N,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;UACtD,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC/F,gBAAgB,IAAIoB,KAAK,CAAChJ,aAAa,KAAK,IAAI,CAAC4H,gBAAgB,EAAE;QAC1E,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAACzK,aAAa,CAACI,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAAC0Q,mBAAmB,CAACjF,KAAK,CAAC,EAAE;UACpC,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC7L,aAAa,CAACO,WAAW,EAAE;QAClC,MAAM6G,WAAW,GAAG2J,QAAQ,CAAC,IAAI,CAAC/Q,aAAa,CAACO,WAAW,CAAC;QAC5D,IAAIsL,KAAK,CAAC/I,MAAM,KAAKsE,WAAW,EAAE;UAChC,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF;IACA,IAAI,CAAClG,cAAc,GAAG,IAAI,CAACqD,cAAc,CAACgM,MAAM,CAAC1E,KAAK,IACpD0H,qBAAqB,CAAC3C,QAAQ,CAAC/E,KAAK,CAAClJ,QAAQ,CAAC,CAC/C;IAED;IACA,IAAI,CAACiF,eAAe,CAACgE,OAAO,CAACC,KAAK,IAAG;MACnCA,KAAK,CAAC7J,QAAQ,GAAG,IAAI,CAACd,cAAc,CAACyP,IAAI,CAAC3O,QAAQ,IAAIA,QAAQ,CAACW,QAAQ,KAAKkJ,KAAK,CAAClJ,QAAQ,CAAC;IAC7F,CAAC,CAAC;IAEF;IACA,IAAI,CAAC6Q,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACjQ,WAAW,GAAG,CAAC;IACpB,IAAI,CAACkQ,gBAAgB,EAAE;EACzB;EAEA;EACQD,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACvN,eAAe,CAAC9E,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC8D,SAAS,GAAG,KAAK;IACxB,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI,CAACgB,eAAe,CAACyN,KAAK,CAAC7H,KAAK,IAC/C,CAACA,KAAK,CAAClJ,QAAQ,IAAIkJ,KAAK,CAAC7J,QAAQ,CAClC;IACH;EACF;EAEA;EACAyR,gBAAgBA,CAAA;IACd,IAAI,CAAC3P,UAAU,GAAGO,IAAI,CAACsP,IAAI,CAAC,IAAI,CAACpP,cAAc,CAACpD,MAAM,GAAG,IAAI,CAACR,QAAQ,CAAC;IACvE,MAAMiT,UAAU,GAAG,CAAC,IAAI,CAACrQ,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC5C,QAAQ;IACzD,MAAMkT,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACjT,QAAQ;IAC3C,IAAI,CAACsF,eAAe,GAAG,IAAI,CAAC1B,cAAc,CAACiC,KAAK,CAACoN,UAAU,EAAEC,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACL,oBAAoB,EAAE;EAC7B;EAEA;EACA3S,gBAAgBA,CAAA;IACd,IAAI,CAAC0C,WAAW,GAAG,CAAC;IACpB,IAAI,CAACkQ,gBAAgB,EAAE;EACzB;EAEA;EACAnQ,QAAQA,CAACwQ,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAChQ,UAAU,EAAE;MACxC,IAAI,CAACP,WAAW,GAAGuQ,IAAI;MACvB,IAAI,CAACL,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAjP,eAAeA,CAAA;IACb,MAAMuP,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAInC,KAAK,GAAGxN,IAAI,CAAC4P,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC1Q,WAAW,GAAGc,IAAI,CAAC2J,KAAK,CAACgG,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAIlC,GAAG,GAAGzN,IAAI,CAACC,GAAG,CAAC,IAAI,CAACR,UAAU,EAAE+N,KAAK,GAAGmC,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAIlC,GAAG,GAAGD,KAAK,GAAG,CAAC,GAAGmC,UAAU,EAAE;MAChCnC,KAAK,GAAGxN,IAAI,CAAC4P,GAAG,CAAC,CAAC,EAAEnC,GAAG,GAAGkC,UAAU,GAAG,CAAC,CAAC;IAC3C;IAEA,KAAK,IAAIE,CAAC,GAAGrC,KAAK,EAAEqC,CAAC,IAAIpC,GAAG,EAAEoC,CAAC,EAAE,EAAE;MACjCH,KAAK,CAAC9F,IAAI,CAACiG,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACA7O,iBAAiBA,CAAA;IACf,IAAI,CAACe,eAAe,CAAC2F,OAAO,CAACC,KAAK,IAAG;MACnC,IAAIA,KAAK,CAAClJ,QAAQ,EAAE;QAClBkJ,KAAK,CAAC7J,QAAQ,GAAG,IAAI,CAACiD,SAAS;MACjC;IACF,CAAC,CAAC;IACF,IAAI,CAACkP,oBAAoB,EAAE;EAC7B;EAEA;EACAlS,sBAAsBA,CAAA;IACpB,IAAI,CAACkS,oBAAoB,EAAE;IAC3B,IAAI,CAAClP,SAAS,GAAG,IAAI,CAACgB,eAAe,CAACyN,KAAK,CAAC7H,KAAK,IAC/C,CAACA,KAAK,CAAClJ,QAAQ,IAAIkJ,KAAK,CAAC7J,QAAQ,CAClC;EACH;EAEA;EACAmS,oBAAoBA,CAAA;IAClB,IAAI,CAACjT,cAAc,GAAG,IAAI,CAAC0G,eAAe,CAAC2I,MAAM,CAAC1E,KAAK,IAAIA,KAAK,CAAC7J,QAAQ,CAAC;EAC5E;EAEA;EACAwD,IAAIA,CAAC4O,KAAa;IAChB,IAAI,IAAI,CAACrO,SAAS,KAAKqO,KAAK,EAAE;MAC5B,IAAI,CAACpO,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IACpE,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAGqO,KAAK;MACtB,IAAI,CAACpO,aAAa,GAAG,KAAK;IAC5B;IAEA,IAAI,CAACzB,cAAc,CAACiB,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAI;MAChC,IAAIkG,MAAM,GAAInG,CAAS,CAACkG,KAAK,CAAC;MAC9B,IAAIE,MAAM,GAAInG,CAAS,CAACiG,KAAK,CAAC;MAE9B;MACA,IAAIA,KAAK,CAACxD,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC1ByD,MAAM,GAAGA,MAAM,GAAG,IAAIpI,IAAI,CAACoI,MAAM,CAAC,CAAC3C,OAAO,EAAE,GAAG,CAAC;QAChD4C,MAAM,GAAGA,MAAM,GAAG,IAAIrI,IAAI,CAACqI,MAAM,CAAC,CAAC5C,OAAO,EAAE,GAAG,CAAC;MAClD;MAEA;MACA,IAAI0C,KAAK,KAAK,QAAQ,EAAE;QACtBC,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC,IAAI,CAAC;QAC5BC,MAAM,GAAGC,MAAM,CAACD,MAAM,CAAC,IAAI,CAAC;MAC9B;MAEA;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAAC5D,WAAW,EAAE;QAC7B6D,MAAM,GAAGA,MAAM,CAAC7D,WAAW,EAAE;MAC/B;MAEA,IAAI4D,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACtO,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC9C;MACA,IAAIqO,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACtO,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9C;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAACyN,gBAAgB,EAAE;EACzB;EAEA;EACAvN,cAAcA,CAACsO,MAAc,EAAE3I,KAAqB;IAClD,OAAOA,KAAK,CAAClJ,QAAQ;EACvB;EAEA;EACAO,aAAaA,CAAC2I,KAAqB;IACjC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IAEzC,QAAQF,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,UAAU;QACb,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACAtG,UAAUA,CAAA;IACR;IACA,MAAMoP,UAAU,GAAG,IAAI,CAACC,WAAW,EAAE;IACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACrCG,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,UAAU,IAAInJ,IAAI,EAAE,CAACoH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACrFwB,IAAI,CAACxX,KAAK,CAAC+X,UAAU,GAAG,QAAQ;IAChCN,QAAQ,CAACpI,IAAI,CAAC2I,WAAW,CAACR,IAAI,CAAC;IAC/BA,IAAI,CAACS,KAAK,EAAE;IACZR,QAAQ,CAACpI,IAAI,CAAC6I,WAAW,CAACV,IAAI,CAAC;EACjC;EAEA;EACQJ,WAAWA,CAAA;IACjB,MAAMe,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;IACxD,MAAMC,IAAI,GAAG,IAAI,CAACnR,cAAc,CAAC6I,GAAG,CAACvB,KAAK,IAAI,CAC5CA,KAAK,CAACjJ,UAAU,EAChBiJ,KAAK,CAAChJ,aAAa,EACnB,GAAGgJ,KAAK,CAAC/I,MAAM,GAAG,EAClB+I,KAAK,CAAClK,gBAAgB,IAAI,KAAK,EAC/BkK,KAAK,CAACjK,cAAc,IAAI,KAAK,EAC7B,IAAI,CAACsB,aAAa,CAAC2I,KAAK,CAAC,CAC1B,CAAC;IAEF,MAAM4I,UAAU,GAAG,CAACgB,OAAO,EAAE,GAAGC,IAAI,CAAC,CAClCtI,GAAG,CAACuI,GAAG,IAAIA,GAAG,CAACvI,GAAG,CAACwI,IAAI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAClDA,IAAI,CAAC,IAAI,CAAC;IAEb,OAAO,QAAQ,GAAGpB,UAAU,CAAC,CAAC;EAChC;;;uCA3lCWhL,0BAA0B,EAAA5L,EAAA,CAAAiY,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnY,EAAA,CAAAiY,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAArY,EAAA,CAAAiY,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAvY,EAAA,CAAAiY,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAzY,EAAA,CAAAiY,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA3Y,EAAA,CAAAiY,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAA5Y,EAAA,CAAAiY,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAA9Y,EAAA,CAAAiY,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA1BpN,0BAA0B;MAAAqN,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;uCAnB1B,EAAE,GAAApZ,EAAA,CAAAsZ,0BAAA,EAAAtZ,EAAA,CAAAuZ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCrEbpZ,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoB,SAAA,qBAAiC;UACnCpB,EAAA,CAAAG,YAAA,EAAiB;UAQPH,EAPV,CAAAC,cAAA,mBAAc,cAEuB,cAED,cACiC,cACpC,aACK;UAC1BD,EAAA,CAAAoB,SAAA,YAAiC;UAAApB,EAAA,CAAAE,MAAA,yDACnC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,MAAA,4FAAc;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACvD;UAEJH,EADF,CAAAC,cAAA,eAA0B,kBAEQ;UAC9BD,EAAA,CAAAoB,SAAA,aAAsC;UAI9CpB,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAMAH,EAHN,CAAAC,cAAA,eAA6B,eACU,eACJ,iBACa;UAAAD,EAAA,CAAAE,MAAA,qBAAG;UAAAF,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;UACvFH,EAAA,CAAAC,cAAA,qBACsD;UADvBD,EAAA,CAAA8B,gBAAA,2BAAA+X,wEAAA7X,MAAA;YAAAhC,EAAA,CAAAe,aAAA,CAAA+Y,GAAA;YAAA9Z,EAAA,CAAAkC,kBAAA,CAAAmX,GAAA,CAAA7L,WAAA,CAAAC,kBAAA,EAAAzL,MAAA,MAAAqX,GAAA,CAAA7L,WAAA,CAAAC,kBAAA,GAAAzL,MAAA;YAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;UAAA,EAA4C;UACzEhC,EAAA,CAAAa,UAAA,4BAAAkZ,yEAAA;YAAA/Z,EAAA,CAAAe,aAAA,CAAA+Y,GAAA;YAAA,OAAA9Z,EAAA,CAAAkB,WAAA,CAAkBmY,GAAA,CAAAlI,iBAAA,EAAmB;UAAA,EAAC;UACtCnR,EAAA,CAAAqB,UAAA,KAAA2Y,gDAAA,wBAAoE;UAIxEha,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAA+B,iBACa;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBACe;UADeD,EAAA,CAAA8B,gBAAA,2BAAAmY,wEAAAjY,MAAA;YAAAhC,EAAA,CAAAe,aAAA,CAAA+Y,GAAA;YAAA9Z,EAAA,CAAAkC,kBAAA,CAAAmX,GAAA,CAAAzM,gBAAA,EAAA5K,MAAA,MAAAqX,GAAA,CAAAzM,gBAAA,GAAA5K,MAAA;YAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;UAAA,EAA8B;UAAChC,EAAA,CAAAa,UAAA,4BAAAqZ,yEAAA;YAAAla,EAAA,CAAAe,aAAA,CAAA+Y,GAAA;YAAA,OAAA9Z,EAAA,CAAAkB,WAAA,CAAkBmY,GAAA,CAAA7G,gBAAA,EAAkB;UAAA,EAAC;UAEhGxS,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAqB,UAAA,KAAA8Y,gDAAA,wBAAuE;UAI3Ena,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAA+B,iBACa;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEtDH,EADF,CAAAC,cAAA,eAA8B,yBACA;UAC1BD,EAAA,CAAAoB,SAAA,mBAAoD;UACpDpB,EAAA,CAAAC,cAAA,iBAC6C;UAA3CD,EAAA,CAAA8B,gBAAA,2BAAAsY,oEAAApY,MAAA;YAAAhC,EAAA,CAAAe,aAAA,CAAA+Y,GAAA;YAAA9Z,EAAA,CAAAkC,kBAAA,CAAAmX,GAAA,CAAA7L,WAAA,CAAA1J,gBAAA,EAAA9B,MAAA,MAAAqX,GAAA,CAAA7L,WAAA,CAAA1J,gBAAA,GAAA9B,MAAA;YAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;UAAA,EAA0C;UAD5ChC,EAAA,CAAAG,YAAA,EAC6C;UAC7CH,EAAA,CAAAoB,SAAA,4BAA8D;UAChEpB,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,yBAA4B;UAC1BD,EAAA,CAAAoB,SAAA,mBAAoD;UACpDpB,EAAA,CAAAC,cAAA,iBAC2C;UAAzCD,EAAA,CAAA8B,gBAAA,2BAAAuY,oEAAArY,MAAA;YAAAhC,EAAA,CAAAe,aAAA,CAAA+Y,GAAA;YAAA9Z,EAAA,CAAAkC,kBAAA,CAAAmX,GAAA,CAAA7L,WAAA,CAAAzJ,cAAA,EAAA/B,MAAA,MAAAqX,GAAA,CAAA7L,WAAA,CAAAzJ,cAAA,GAAA/B,MAAA;YAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;UAAA,EAAwC;UAD1ChC,EAAA,CAAAG,YAAA,EAC2C;UAC3CH,EAAA,CAAAoB,SAAA,4BAA4D;UAGlEpB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAgC,eACF,kBAC0D;UAApDD,EAAA,CAAAa,UAAA,mBAAAyZ,6DAAA;YAAAta,EAAA,CAAAe,aAAA,CAAA+Y,GAAA;YAAA,OAAA9Z,EAAA,CAAAkB,WAAA,CAASmY,GAAA,CAAApK,kBAAA,EAAoB;UAAA,EAAC;UAC5DjP,EAAA,CAAAoB,SAAA,aAAkC;UAAApB,EAAA,CAAAE,MAAA,qBACpC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBACiB;UAD8BD,EAAA,CAAAa,UAAA,mBAAA0Z,6DAAA;YAAAva,EAAA,CAAAe,aAAA,CAAA+Y,GAAA;YAAA,OAAA9Z,EAAA,CAAAkB,WAAA,CAASmY,GAAA,CAAAxL,YAAA,EAAc;UAAA,EAAC;UAErE7N,EAAA,CAAAoB,SAAA,aAA2B;UAKrCpB,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;UAWNH,EARA,CAAAqB,UAAA,KAAAmZ,0CAAA,kBAAwE,KAAAC,0CAAA,oBAQoC;UAkD9Gza,EAAA,CAAAG,YAAA,EAAM;UA0RNH,EAvRA,CAAAqB,UAAA,KAAAqZ,0CAAA,oBAAyE,KAAAC,0CAAA,kBA2Q2B,KAAAC,0CAAA,kBAYtD;UAalD5a,EADE,CAAAG,YAAA,EAAe,EACP;UAkGVH,EA/FA,CAAAqB,UAAA,KAAAwZ,kDAAA,iCAAA7a,EAAA,CAAA8a,sBAAA,CAAgE,KAAAC,kDAAA,gCAAA/a,EAAA,CAAA8a,sBAAA,CAkFG,KAAAE,kDAAA,iCAAAhb,EAAA,CAAA8a,sBAAA,CAaf;;;;;UAxfT9a,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAmD,gBAAA,YAAAkW,GAAA,CAAA7L,WAAA,CAAAC,kBAAA,CAA4C;UAE7CzN,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAiZ,GAAA,CAAA/J,oBAAA,CAAuB;UAQvBtP,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAmD,gBAAA,YAAAkW,GAAA,CAAAzM,gBAAA,CAA8B;UAG1B5M,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAAiZ,GAAA,CAAA1M,eAAA,CAAkB;UAWF3M,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,iBAAA6a,aAAA,CAA0B;UACtEjb,EAAA,CAAAmD,gBAAA,YAAAkW,GAAA,CAAA7L,WAAA,CAAA1J,gBAAA,CAA0C;UAME9D,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,iBAAA8a,WAAA,CAAwB;UACpElb,EAAA,CAAAmD,gBAAA,YAAAkW,GAAA,CAAA7L,WAAA,CAAAzJ,cAAA,CAAwC;UAQmB/D,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,aAAAiZ,GAAA,CAAArM,OAAA,CAAoB;UAGXhN,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,aAAAiZ,GAAA,CAAArM,OAAA,CAAoB;UAU9DhN,EAAA,CAAAM,SAAA,GAAgC;UAAhCN,EAAA,CAAAI,UAAA,SAAAiZ,GAAA,CAAAtP,eAAA,CAAAzG,MAAA,KAAgC;UAQjCtD,EAAA,CAAAM,SAAA,EAAuD;UAAvDN,EAAA,CAAAI,UAAA,SAAAiZ,GAAA,CAAA7X,mBAAA,IAAA6X,GAAA,CAAAtP,eAAA,CAAAzG,MAAA,KAAuD;UAqDvDtD,EAAA,CAAAM,SAAA,EAAgC;UAAhCN,EAAA,CAAAI,UAAA,SAAAiZ,GAAA,CAAAtP,eAAA,CAAAzG,MAAA,KAAgC;UA2QxCtD,EAAA,CAAAM,SAAA,EAAmE;UAAnEN,EAAA,CAAAI,UAAA,SAAAiZ,GAAA,CAAAtP,eAAA,CAAAzG,MAAA,UAAA+V,GAAA,CAAAhI,gBAAA,CAAA/N,MAAA,OAAmE;UAYnEtD,EAAA,CAAAM,SAAA,EAAa;UAAbN,EAAA,CAAAI,UAAA,SAAAiZ,GAAA,CAAArM,OAAA,CAAa;;;qBD/V5C5N,YAAY,EAAA+b,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAEzb,YAAY,EAAA0b,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,YAAA,EAAAL,EAAA,CAAAM,OAAA,EAAAzD,EAAA,CAAA0D,eAAA,EAAA1D,EAAA,CAAA2D,mBAAA,EAAA3D,EAAA,CAAA4D,qBAAA,EAAA5D,EAAA,CAAA6D,qBAAA,EAAA7D,EAAA,CAAA8D,mBAAA,EAAA9D,EAAA,CAAA+D,gBAAA,EAAA/D,EAAA,CAAAgE,iBAAA,EAAAhE,EAAA,CAAAiE,iBAAA,EAAAjE,EAAA,CAAAkE,oBAAA,EAAAlE,EAAA,CAAAmE,iBAAA,EAAAnE,EAAA,CAAAoE,eAAA,EAAApE,EAAA,CAAAqE,qBAAA,EAAArE,EAAA,CAAAsE,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAC1Btd,kBAAkB,EAAEC,mBAAmB;MAAAsd,MAAA;MAAAlL,IAAA;QAAAmL,SAAA,EAG7B,CACVtd,OAAO,CAAC,YAAY,EAAE,CACpBE,UAAU,CAAC,QAAQ,EAAE,CACnBD,KAAK,CAAC;UAAEsd,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAmB,CAAE,CAAC,EACrDrd,OAAO,CAAC,gBAAgB,EAAEF,KAAK,CAAC;UAAEsd,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAAC,CAC7E,CAAC,EACFtd,UAAU,CAAC,QAAQ,EAAE,CACnBC,OAAO,CAAC,eAAe,EAAEF,KAAK,CAAC;UAAEsd,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAmB,CAAE,CAAC,CAAC,CAChF,CAAC,CACH,CAAC;MACH;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}