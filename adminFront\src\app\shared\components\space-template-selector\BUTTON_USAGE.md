# 空間模板選擇器按鈕元件

## 概述

`SpaceTemplateSelectorButtonComponent` 是一個獨立的按鈕元件，專門用於開啟空間模板選擇器對話框。這個元件提供了高度的自定義能力，讓您可以輕鬆地在任何地方添加模板選擇功能。

## 特色

- **高度可自定義**: 支持自定義按鈕文字、圖標、樣式類別
- **易於使用**: 只需要幾行 HTML 代碼即可集成
- **完整的事件支持**: 提供模板套用、錯誤處理等事件
- **類型安全**: 使用 TypeScript 確保類型安全
- **響應式設計**: 支持禁用狀態和條件渲染

## 基本使用

### 1. 在組件中導入

```typescript
// your-component.ts
import { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';
import { SpaceTemplateConfig } from 'src/app/shared/components/space-template-selector/space-template-selector.component';

@Component({
  selector: 'app-your-component',
  standalone: true,
  imports: [
    // ... 其他導入
    SpaceTemplateSelectorButtonComponent
  ],
  // ...
})
export class YourComponent {
  buildCaseId: string = 'your-build-case-id';

  onTemplateApplied(config: SpaceTemplateConfig) {
    console.log('模板已套用:', config);
    // 處理模板套用邏輯
  }

  onTemplateError(error: string) {
    console.error('錯誤:', error);
    // 處理錯誤
  }
}
```

### 2. 在模板中使用

```html
<!-- 基本使用 -->
<app-space-template-selector-button
  [buildCaseId]="buildCaseId"
  (templateApplied)="onTemplateApplied($event)"
  (error)="onTemplateError($event)">
</app-space-template-selector-button>
```

## 進階配置

### 自定義按鈕外觀

```html
<app-space-template-selector-button
  [buildCaseId]="buildCaseId"
  [text]="'選擇模板'"
  [icon]="'fas fa-magic'"
  [buttonClass]="'btn btn-success btn-lg'"
  (templateApplied)="onTemplateApplied($event)"
  (error)="onTemplateError($event)">
</app-space-template-selector-button>
```

### 條件禁用

```html
<app-space-template-selector-button
  [buildCaseId]="selectedBuildCase?.id || ''"
  [disabled]="!selectedBuildCase"
  [text]="selectedBuildCase ? '模板新增' : '請先選擇建案'"
  (templateApplied)="onTemplateApplied($event)"
  (error)="onTemplateError($event)">
</app-space-template-selector-button>
```

### 使用配置對象

```html
<app-space-template-selector-button
  [buildCaseId]="buildCaseId"
  [config]="customConfig"
  (templateApplied)="onTemplateApplied($event)"
  (error)="onTemplateError($event)">
</app-space-template-selector-button>
```

```typescript
// 組件中
customConfig = {
  dialogTitle: '請選擇空間模板',
  // 其他配置選項
};
```

## API 參考

### 輸入屬性 (Inputs)

| 屬性 | 類型 | 預設值 | 描述 |
|------|------|--------|------|
| `buildCaseId` | `string` | `''` | 建案 ID，必填 |
| `text` | `string` | `'模板新增'` | 按鈕顯示文字 |
| `icon` | `string` | `'fas fa-layer-group'` | 按鈕圖標 CSS 類別 |
| `buttonClass` | `string` | `'btn btn-warning mr-2'` | 按鈕 CSS 類別 |
| `disabled` | `boolean` | `false` | 是否禁用按鈕 |
| `config` | `Partial<SpaceTemplateSelectorConfig>` | `{}` | 額外的配置選項 |

### 輸出事件 (Outputs)

| 事件 | 類型 | 描述 |
|------|------|------|
| `templateApplied` | `EventEmitter<SpaceTemplateConfig>` | 當用戶套用模板時觸發 |
| `beforeOpen` | `EventEmitter<void>` | 在開啟對話框前觸發 |
| `error` | `EventEmitter<string>` | 當發生錯誤時觸發 |

## 常見使用案例

### 案例 1: 需求管理頁面 (現有實現)

```html
<app-space-template-selector-button
  [buildCaseId]="getListRequirementRequest.CBuildCaseID?.toString() || ''"
  [text]="'模板新增'"
  [disabled]="!getListRequirementRequest.CBuildCaseID"
  (templateApplied)="onSpaceTemplateApplied($event)"
  (error)="onTemplateError($event)">
</app-space-template-selector-button>
```

### 案例 2: 小型快速選擇按鈕

```html
<app-space-template-selector-button
  [buildCaseId]="item.buildCaseId"
  [text]="'快選'"
  [icon]="'fas fa-bolt'"
  [buttonClass]="'btn btn-outline-primary btn-sm'"
  (templateApplied)="quickApplyTemplate($event)">
</app-space-template-selector-button>
```

### 案例 3: 表格行動按鈕

```html
<tr *ngFor="let item of dataList">
  <td>{{ item.name }}</td>
  <td>{{ item.description }}</td>
  <td>
    <app-space-template-selector-button
      [buildCaseId]="item.buildCaseId"
      [text]="'套用模板'"
      [icon]="'fas fa-copy'"
      [buttonClass]="'btn btn-link btn-sm'"
      (templateApplied)="applyToItem(item, $event)">
    </app-space-template-selector-button>
  </td>
</tr>
```

## 配合 Helper 類別使用

您也可以搭配 `SpaceTemplateSelectorHelper` 來快速創建常用配置：

```typescript
import { SpaceTemplateSelectorHelper } from 'src/app/shared/components/space-template-selector/space-template-selector.helper';

constructor(private templateHelper: SpaceTemplateSelectorHelper) {}

// 使用輔助方法
openStandardTemplate() {
  this.templateHelper.openStandardSelector(this.buildCaseId)
    .subscribe(result => {
      if (result) this.handleTemplate(result);
    });
}

openCustomTemplate() {
  this.templateHelper.openCustomSelector({
    buildCaseId: this.buildCaseId,
    text: '自定義選擇',
    buttonClass: 'btn btn-info'
  }).subscribe(result => {
    if (result) this.handleTemplate(result);
  });
}
```

## 樣式自定義

### 常用按鈕樣式類別

```css
/* 主要按鈕 */
.btn.btn-primary

/* 成功按鈕 */
.btn.btn-success

/* 警告按鈕 */
.btn.btn-warning

/* 危險按鈕 */
.btn.btn-danger

/* 次要按鈕 */
.btn.btn-secondary

/* 輪廓按鈕 */
.btn.btn-outline-primary
.btn.btn-outline-success

/* 尺寸變化 */
.btn.btn-sm      /* 小尺寸 */
.btn.btn-lg      /* 大尺寸 */

/* 邊距 */
.mr-2           /* 右邊距 */
.ml-2           /* 左邊距 */
.mx-2           /* 左右邊距 */
```

## 故障排除

### 常見問題

1. **按鈕沒有顯示**
   - 確保已在組件的 `imports` 中加入 `SpaceTemplateSelectorButtonComponent`
   - 檢查是否有條件渲染導致按鈕被隱藏

2. **事件沒有觸發**
   - 確保事件處理方法已正確定義
   - 檢查方法名稱是否拼寫正確

3. **按鈕被禁用**
   - 檢查 `disabled` 屬性的條件
   - 確保 `buildCaseId` 有正確的值

4. **樣式不符合預期**
   - 檢查 `buttonClass` 屬性
   - 確保 CSS 類別已正確加載

### 調試技巧

```typescript
// 在事件處理方法中加入日誌
onTemplateApplied(config: SpaceTemplateConfig) {
  console.log('模板配置:', config);
  console.log('選中項目數量:', config.selectedItems.length);
  // 您的處理邏輯
}

onTemplateError(error: string) {
  console.error('模板選擇錯誤:', error);
  // 錯誤處理邏輯
}

// 檢查按鈕開啟前的狀態
onBeforeOpen() {
  console.log('準備開啟模板選擇器');
  console.log('建案 ID:', this.buildCaseId);
}
```

## 版本兼容性

- Angular 18+
- Nebular Theme 13+
- 與現有的 `SpaceTemplateSelectorService` 完全兼容
- 支持 Standalone Components
