{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiQuotationGetDataPost$Json } from '../fn/quotation/api-quotation-get-data-post-json';\nimport { apiQuotationGetDataPost$Plain } from '../fn/quotation/api-quotation-get-data-post-plain';\nimport { apiQuotationGetListByHouseIdPost$Json } from '../fn/quotation/api-quotation-get-list-by-house-id-post-json';\nimport { apiQuotationGetListByHouseIdPost$Plain } from '../fn/quotation/api-quotation-get-list-by-house-id-post-plain';\nimport { apiQuotationGetListPost$Json } from '../fn/quotation/api-quotation-get-list-post-json';\nimport { apiQuotationGetListPost$Plain } from '../fn/quotation/api-quotation-get-list-post-plain';\nimport { apiQuotationGetQuotationHistoryPost$Json } from '../fn/quotation/api-quotation-get-quotation-history-post-json';\nimport { apiQuotationGetQuotationHistoryPost$Plain } from '../fn/quotation/api-quotation-get-quotation-history-post-plain';\nimport { apiQuotationLoadDefaultItemsPost$Json } from '../fn/quotation/api-quotation-load-default-items-post-json';\nimport { apiQuotationLoadDefaultItemsPost$Plain } from '../fn/quotation/api-quotation-load-default-items-post-plain';\nimport { apiQuotationLoadRegularItemsPost$Json } from '../fn/quotation/api-quotation-load-regular-items-post-json';\nimport { apiQuotationLoadRegularItemsPost$Plain } from '../fn/quotation/api-quotation-load-regular-items-post-plain';\nimport { apiQuotationLockQuotationPost$Json } from '../fn/quotation/api-quotation-lock-quotation-post-json';\nimport { apiQuotationLockQuotationPost$Plain } from '../fn/quotation/api-quotation-lock-quotation-post-plain';\nimport { apiQuotationSaveDataPost$Json } from '../fn/quotation/api-quotation-save-data-post-json';\nimport { apiQuotationSaveDataPost$Plain } from '../fn/quotation/api-quotation-save-data-post-plain';\nimport { apiQuotationSignQuotationPost$Json } from '../fn/quotation/api-quotation-sign-quotation-post-json';\nimport { apiQuotationSignQuotationPost$Plain } from '../fn/quotation/api-quotation-sign-quotation-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let QuotationService = /*#__PURE__*/(() => {\n  class QuotationService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiQuotationGetListPost()` */\n    static {\n      this.ApiQuotationGetListPostPath = '/api/Quotation/GetList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationGetListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetListPost$Plain$Response(params, context) {\n      return apiQuotationGetListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationGetListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetListPost$Plain(params, context) {\n      return this.apiQuotationGetListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationGetListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetListPost$Json$Response(params, context) {\n      return apiQuotationGetListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationGetListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetListPost$Json(params, context) {\n      return this.apiQuotationGetListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiQuotationGetDataPost()` */\n    static {\n      this.ApiQuotationGetDataPostPath = '/api/Quotation/GetData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationGetDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetDataPost$Plain$Response(params, context) {\n      return apiQuotationGetDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationGetDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetDataPost$Plain(params, context) {\n      return this.apiQuotationGetDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationGetDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetDataPost$Json$Response(params, context) {\n      return apiQuotationGetDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationGetDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetDataPost$Json(params, context) {\n      return this.apiQuotationGetDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiQuotationSaveDataPost()` */\n    static {\n      this.ApiQuotationSaveDataPostPath = '/api/Quotation/SaveData';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationSaveDataPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationSaveDataPost$Plain$Response(params, context) {\n      return apiQuotationSaveDataPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationSaveDataPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationSaveDataPost$Plain(params, context) {\n      return this.apiQuotationSaveDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationSaveDataPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationSaveDataPost$Json$Response(params, context) {\n      return apiQuotationSaveDataPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationSaveDataPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationSaveDataPost$Json(params, context) {\n      return this.apiQuotationSaveDataPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiQuotationGetListByHouseIdPost()` */\n    static {\n      this.ApiQuotationGetListByHouseIdPostPath = '/api/Quotation/GetListByHouseID';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationGetListByHouseIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetListByHouseIdPost$Plain$Response(params, context) {\n      return apiQuotationGetListByHouseIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationGetListByHouseIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetListByHouseIdPost$Plain(params, context) {\n      return this.apiQuotationGetListByHouseIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationGetListByHouseIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetListByHouseIdPost$Json$Response(params, context) {\n      return apiQuotationGetListByHouseIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationGetListByHouseIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationGetListByHouseIdPost$Json(params, context) {\n      return this.apiQuotationGetListByHouseIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiQuotationLoadDefaultItemsPost()` */\n    static {\n      this.ApiQuotationLoadDefaultItemsPostPath = '/api/Quotation/LoadDefaultItems';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationLoadDefaultItemsPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLoadDefaultItemsPost$Plain$Response(params, context) {\n      return apiQuotationLoadDefaultItemsPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationLoadDefaultItemsPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLoadDefaultItemsPost$Plain(params, context) {\n      return this.apiQuotationLoadDefaultItemsPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationLoadDefaultItemsPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLoadDefaultItemsPost$Json$Response(params, context) {\n      return apiQuotationLoadDefaultItemsPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationLoadDefaultItemsPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLoadDefaultItemsPost$Json(params, context) {\n      return this.apiQuotationLoadDefaultItemsPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiQuotationLoadRegularItemsPost()` */\n    static {\n      this.ApiQuotationLoadRegularItemsPostPath = '/api/Quotation/LoadRegularItems';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationLoadRegularItemsPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLoadRegularItemsPost$Plain$Response(params, context) {\n      return apiQuotationLoadRegularItemsPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationLoadRegularItemsPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLoadRegularItemsPost$Plain(params, context) {\n      return this.apiQuotationLoadRegularItemsPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationLoadRegularItemsPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLoadRegularItemsPost$Json$Response(params, context) {\n      return apiQuotationLoadRegularItemsPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationLoadRegularItemsPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLoadRegularItemsPost$Json(params, context) {\n      return this.apiQuotationLoadRegularItemsPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiQuotationLockQuotationPost()` */\n    static {\n      this.ApiQuotationLockQuotationPostPath = '/api/Quotation/LockQuotation';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationLockQuotationPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLockQuotationPost$Plain$Response(params, context) {\n      return apiQuotationLockQuotationPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationLockQuotationPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLockQuotationPost$Plain(params, context) {\n      return this.apiQuotationLockQuotationPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationLockQuotationPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLockQuotationPost$Json$Response(params, context) {\n      return apiQuotationLockQuotationPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationLockQuotationPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationLockQuotationPost$Json(params, context) {\n      return this.apiQuotationLockQuotationPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiQuotationGetQuotationHistoryPost()` */\n    static {\n      this.ApiQuotationGetQuotationHistoryPostPath = '/api/Quotation/GetQuotationHistory';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationGetQuotationHistoryPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiQuotationGetQuotationHistoryPost$Plain$Response(params, context) {\n      return apiQuotationGetQuotationHistoryPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationGetQuotationHistoryPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiQuotationGetQuotationHistoryPost$Plain(params, context) {\n      return this.apiQuotationGetQuotationHistoryPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationGetQuotationHistoryPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiQuotationGetQuotationHistoryPost$Json$Response(params, context) {\n      return apiQuotationGetQuotationHistoryPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationGetQuotationHistoryPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiQuotationGetQuotationHistoryPost$Json(params, context) {\n      return this.apiQuotationGetQuotationHistoryPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiQuotationSignQuotationPost()` */\n    static {\n      this.ApiQuotationSignQuotationPostPath = '/api/Quotation/SignQuotation';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationSignQuotationPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationSignQuotationPost$Plain$Response(params, context) {\n      return apiQuotationSignQuotationPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationSignQuotationPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationSignQuotationPost$Plain(params, context) {\n      return this.apiQuotationSignQuotationPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiQuotationSignQuotationPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationSignQuotationPost$Json$Response(params, context) {\n      return apiQuotationSignQuotationPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiQuotationSignQuotationPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiQuotationSignQuotationPost$Json(params, context) {\n      return this.apiQuotationSignQuotationPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: QuotationService,\n        factory: QuotationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return QuotationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}