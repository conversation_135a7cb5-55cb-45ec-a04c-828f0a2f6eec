{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The \"scaleLevels\" was originally copied from \"d3.js\" with some\r\n* modifications made for this project.\r\n* (See more details in the comment on the definition of \"scaleLevels\" below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\n// [About UTC and local time zone]:\n// In most cases, `number.parseDate` will treat input data string as local time\n// (except time zone is specified in time string). And `format.formateTime` returns\n// local time by default. option.useUTC is false by default. This design has\n// considered these common cases:\n// (1) Time that is persistent in server is in UTC, but it is needed to be displayed\n// in local time by default.\n// (2) By default, the input data string (e.g., '2011-01-02') should be displayed\n// as its original time, without any time difference.\nimport * as numberUtil from '../util/number.js';\nimport { ONE_SECOND, ONE_MINUTE, ONE_HOUR, ONE_DAY, ONE_YEAR, format, leveledFormat, getUnitValue, timeUnits, fullLeveledFormatter, getPrimaryTimeUnit, isPrimaryTimeUnit, getDefaultFormatPrecisionOfInterval, fullYearGetterName, monthSetterName, fullYearSetterName, dateSetterName, hoursGetterName, hoursSetterName, minutesSetterName, secondsSetterName, millisecondsSetterName, monthGetterName, dateGetterName, minutesGetterName, secondsGetterName, millisecondsGetterName } from '../util/time.js';\nimport * as scaleHelper from './helper.js';\nimport IntervalScale from './Interval.js';\nimport Scale from './Scale.js';\nimport { warn } from '../util/log.js';\nimport { filter, isNumber, map } from 'zrender/lib/core/util.js';\n// FIXME 公用？\nvar bisect = function (a, x, lo, hi) {\n  while (lo < hi) {\n    var mid = lo + hi >>> 1;\n    if (a[mid][1] < x) {\n      lo = mid + 1;\n    } else {\n      hi = mid;\n    }\n  }\n  return lo;\n};\nvar TimeScale = /** @class */function (_super) {\n  __extends(TimeScale, _super);\n  function TimeScale(settings) {\n    var _this = _super.call(this, settings) || this;\n    _this.type = 'time';\n    return _this;\n  }\n  /**\r\n   * Get label is mainly for other components like dataZoom, tooltip.\r\n   */\n  TimeScale.prototype.getLabel = function (tick) {\n    var useUTC = this.getSetting('useUTC');\n    return format(tick.value, fullLeveledFormatter[getDefaultFormatPrecisionOfInterval(getPrimaryTimeUnit(this._minLevelUnit))] || fullLeveledFormatter.second, useUTC, this.getSetting('locale'));\n  };\n  TimeScale.prototype.getFormattedLabel = function (tick, idx, labelFormatter) {\n    var isUTC = this.getSetting('useUTC');\n    var lang = this.getSetting('locale');\n    return leveledFormat(tick, idx, labelFormatter, lang, isUTC);\n  };\n  /**\r\n   * @override\r\n   */\n  TimeScale.prototype.getTicks = function () {\n    var interval = this._interval;\n    var extent = this._extent;\n    var ticks = [];\n    // If interval is 0, return [];\n    if (!interval) {\n      return ticks;\n    }\n    ticks.push({\n      value: extent[0],\n      level: 0\n    });\n    var useUTC = this.getSetting('useUTC');\n    var innerTicks = getIntervalTicks(this._minLevelUnit, this._approxInterval, useUTC, extent);\n    ticks = ticks.concat(innerTicks);\n    ticks.push({\n      value: extent[1],\n      level: 0\n    });\n    return ticks;\n  };\n  TimeScale.prototype.calcNiceExtent = function (opt) {\n    var extent = this._extent;\n    // If extent start and end are same, expand them\n    if (extent[0] === extent[1]) {\n      // Expand extent\n      extent[0] -= ONE_DAY;\n      extent[1] += ONE_DAY;\n    }\n    // If there are no data and extent are [Infinity, -Infinity]\n    if (extent[1] === -Infinity && extent[0] === Infinity) {\n      var d = new Date();\n      extent[1] = +new Date(d.getFullYear(), d.getMonth(), d.getDate());\n      extent[0] = extent[1] - ONE_DAY;\n    }\n    this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);\n  };\n  TimeScale.prototype.calcNiceTicks = function (approxTickNum, minInterval, maxInterval) {\n    approxTickNum = approxTickNum || 10;\n    var extent = this._extent;\n    var span = extent[1] - extent[0];\n    this._approxInterval = span / approxTickNum;\n    if (minInterval != null && this._approxInterval < minInterval) {\n      this._approxInterval = minInterval;\n    }\n    if (maxInterval != null && this._approxInterval > maxInterval) {\n      this._approxInterval = maxInterval;\n    }\n    var scaleIntervalsLen = scaleIntervals.length;\n    var idx = Math.min(bisect(scaleIntervals, this._approxInterval, 0, scaleIntervalsLen), scaleIntervalsLen - 1);\n    // Interval that can be used to calculate ticks\n    this._interval = scaleIntervals[idx][1];\n    // Min level used when picking ticks from top down.\n    // We check one more level to avoid the ticks are to sparse in some case.\n    this._minLevelUnit = scaleIntervals[Math.max(idx - 1, 0)][0];\n  };\n  TimeScale.prototype.parse = function (val) {\n    // val might be float.\n    return isNumber(val) ? val : +numberUtil.parseDate(val);\n  };\n  TimeScale.prototype.contain = function (val) {\n    return scaleHelper.contain(this.parse(val), this._extent);\n  };\n  TimeScale.prototype.normalize = function (val) {\n    return scaleHelper.normalize(this.parse(val), this._extent);\n  };\n  TimeScale.prototype.scale = function (val) {\n    return scaleHelper.scale(val, this._extent);\n  };\n  TimeScale.type = 'time';\n  return TimeScale;\n}(IntervalScale);\n/**\r\n * This implementation was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3/blob/b516d77fb8566b576088e73410437494717ada26/src/time/scale.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n */\nvar scaleIntervals = [\n// Format                           interval\n['second', ONE_SECOND], ['minute', ONE_MINUTE], ['hour', ONE_HOUR], ['quarter-day', ONE_HOUR * 6], ['half-day', ONE_HOUR * 12], ['day', ONE_DAY * 1.2], ['half-week', ONE_DAY * 3.5], ['week', ONE_DAY * 7], ['month', ONE_DAY * 31], ['quarter', ONE_DAY * 95], ['half-year', ONE_YEAR / 2], ['year', ONE_YEAR] // 1Y\n];\nfunction isUnitValueSame(unit, valueA, valueB, isUTC) {\n  var dateA = numberUtil.parseDate(valueA);\n  var dateB = numberUtil.parseDate(valueB);\n  var isSame = function (unit) {\n    return getUnitValue(dateA, unit, isUTC) === getUnitValue(dateB, unit, isUTC);\n  };\n  var isSameYear = function () {\n    return isSame('year');\n  };\n  // const isSameHalfYear = () => isSameYear() && isSame('half-year');\n  // const isSameQuater = () => isSameYear() && isSame('quarter');\n  var isSameMonth = function () {\n    return isSameYear() && isSame('month');\n  };\n  var isSameDay = function () {\n    return isSameMonth() && isSame('day');\n  };\n  // const isSameHalfDay = () => isSameDay() && isSame('half-day');\n  var isSameHour = function () {\n    return isSameDay() && isSame('hour');\n  };\n  var isSameMinute = function () {\n    return isSameHour() && isSame('minute');\n  };\n  var isSameSecond = function () {\n    return isSameMinute() && isSame('second');\n  };\n  var isSameMilliSecond = function () {\n    return isSameSecond() && isSame('millisecond');\n  };\n  switch (unit) {\n    case 'year':\n      return isSameYear();\n    case 'month':\n      return isSameMonth();\n    case 'day':\n      return isSameDay();\n    case 'hour':\n      return isSameHour();\n    case 'minute':\n      return isSameMinute();\n    case 'second':\n      return isSameSecond();\n    case 'millisecond':\n      return isSameMilliSecond();\n  }\n}\n// const primaryUnitGetters = {\n//     year: fullYearGetterName(),\n//     month: monthGetterName(),\n//     day: dateGetterName(),\n//     hour: hoursGetterName(),\n//     minute: minutesGetterName(),\n//     second: secondsGetterName(),\n//     millisecond: millisecondsGetterName()\n// };\n// const primaryUnitUTCGetters = {\n//     year: fullYearGetterName(true),\n//     month: monthGetterName(true),\n//     day: dateGetterName(true),\n//     hour: hoursGetterName(true),\n//     minute: minutesGetterName(true),\n//     second: secondsGetterName(true),\n//     millisecond: millisecondsGetterName(true)\n// };\n// function moveTick(date: Date, unitName: TimeUnit, step: number, isUTC: boolean) {\n//     step = step || 1;\n//     switch (getPrimaryTimeUnit(unitName)) {\n//         case 'year':\n//             date[fullYearSetterName(isUTC)](date[fullYearGetterName(isUTC)]() + step);\n//             break;\n//         case 'month':\n//             date[monthSetterName(isUTC)](date[monthGetterName(isUTC)]() + step);\n//             break;\n//         case 'day':\n//             date[dateSetterName(isUTC)](date[dateGetterName(isUTC)]() + step);\n//             break;\n//         case 'hour':\n//             date[hoursSetterName(isUTC)](date[hoursGetterName(isUTC)]() + step);\n//             break;\n//         case 'minute':\n//             date[minutesSetterName(isUTC)](date[minutesGetterName(isUTC)]() + step);\n//             break;\n//         case 'second':\n//             date[secondsSetterName(isUTC)](date[secondsGetterName(isUTC)]() + step);\n//             break;\n//         case 'millisecond':\n//             date[millisecondsSetterName(isUTC)](date[millisecondsGetterName(isUTC)]() + step);\n//             break;\n//     }\n//     return date.getTime();\n// }\n// const DATE_INTERVALS = [[8, 7.5], [4, 3.5], [2, 1.5]];\n// const MONTH_INTERVALS = [[6, 5.5], [3, 2.5], [2, 1.5]];\n// const MINUTES_SECONDS_INTERVALS = [[30, 30], [20, 20], [15, 15], [10, 10], [5, 5], [2, 2]];\nfunction getDateInterval(approxInterval, daysInMonth) {\n  approxInterval /= ONE_DAY;\n  return approxInterval > 16 ? 16\n  // Math.floor(daysInMonth / 2) + 1  // In this case we only want one tick between two months.\n  : approxInterval > 7.5 ? 7 // TODO week 7 or day 8?\n  : approxInterval > 3.5 ? 4 : approxInterval > 1.5 ? 2 : 1;\n}\nfunction getMonthInterval(approxInterval) {\n  var APPROX_ONE_MONTH = 30 * ONE_DAY;\n  approxInterval /= APPROX_ONE_MONTH;\n  return approxInterval > 6 ? 6 : approxInterval > 3 ? 3 : approxInterval > 2 ? 2 : 1;\n}\nfunction getHourInterval(approxInterval) {\n  approxInterval /= ONE_HOUR;\n  return approxInterval > 12 ? 12 : approxInterval > 6 ? 6 : approxInterval > 3.5 ? 4 : approxInterval > 2 ? 2 : 1;\n}\nfunction getMinutesAndSecondsInterval(approxInterval, isMinutes) {\n  approxInterval /= isMinutes ? ONE_MINUTE : ONE_SECOND;\n  return approxInterval > 30 ? 30 : approxInterval > 20 ? 20 : approxInterval > 15 ? 15 : approxInterval > 10 ? 10 : approxInterval > 5 ? 5 : approxInterval > 2 ? 2 : 1;\n}\nfunction getMillisecondsInterval(approxInterval) {\n  return numberUtil.nice(approxInterval, true);\n}\nfunction getFirstTimestampOfUnit(date, unitName, isUTC) {\n  var outDate = new Date(date);\n  switch (getPrimaryTimeUnit(unitName)) {\n    case 'year':\n    case 'month':\n      outDate[monthSetterName(isUTC)](0);\n    case 'day':\n      outDate[dateSetterName(isUTC)](1);\n    case 'hour':\n      outDate[hoursSetterName(isUTC)](0);\n    case 'minute':\n      outDate[minutesSetterName(isUTC)](0);\n    case 'second':\n      outDate[secondsSetterName(isUTC)](0);\n      outDate[millisecondsSetterName(isUTC)](0);\n  }\n  return outDate.getTime();\n}\nfunction getIntervalTicks(bottomUnitName, approxInterval, isUTC, extent) {\n  var safeLimit = 10000;\n  var unitNames = timeUnits;\n  var iter = 0;\n  function addTicksInSpan(interval, minTimestamp, maxTimestamp, getMethodName, setMethodName, isDate, out) {\n    var date = new Date(minTimestamp);\n    var dateTime = minTimestamp;\n    var d = date[getMethodName]();\n    // if (isDate) {\n    //     d -= 1; // Starts with 0;   PENDING\n    // }\n    while (dateTime < maxTimestamp && dateTime <= extent[1]) {\n      out.push({\n        value: dateTime\n      });\n      d += interval;\n      date[setMethodName](d);\n      dateTime = date.getTime();\n    }\n    // This extra tick is for calcuating ticks of next level. Will not been added to the final result\n    out.push({\n      value: dateTime,\n      notAdd: true\n    });\n  }\n  function addLevelTicks(unitName, lastLevelTicks, levelTicks) {\n    var newAddedTicks = [];\n    var isFirstLevel = !lastLevelTicks.length;\n    if (isUnitValueSame(getPrimaryTimeUnit(unitName), extent[0], extent[1], isUTC)) {\n      return;\n    }\n    if (isFirstLevel) {\n      lastLevelTicks = [{\n        // TODO Optimize. Not include so may ticks.\n        value: getFirstTimestampOfUnit(new Date(extent[0]), unitName, isUTC)\n      }, {\n        value: extent[1]\n      }];\n    }\n    for (var i = 0; i < lastLevelTicks.length - 1; i++) {\n      var startTick = lastLevelTicks[i].value;\n      var endTick = lastLevelTicks[i + 1].value;\n      if (startTick === endTick) {\n        continue;\n      }\n      var interval = void 0;\n      var getterName = void 0;\n      var setterName = void 0;\n      var isDate = false;\n      switch (unitName) {\n        case 'year':\n          interval = Math.max(1, Math.round(approxInterval / ONE_DAY / 365));\n          getterName = fullYearGetterName(isUTC);\n          setterName = fullYearSetterName(isUTC);\n          break;\n        case 'half-year':\n        case 'quarter':\n        case 'month':\n          interval = getMonthInterval(approxInterval);\n          getterName = monthGetterName(isUTC);\n          setterName = monthSetterName(isUTC);\n          break;\n        case 'week': // PENDING If week is added. Ignore day.\n        case 'half-week':\n        case 'day':\n          interval = getDateInterval(approxInterval, 31); // Use 32 days and let interval been 16\n          getterName = dateGetterName(isUTC);\n          setterName = dateSetterName(isUTC);\n          isDate = true;\n          break;\n        case 'half-day':\n        case 'quarter-day':\n        case 'hour':\n          interval = getHourInterval(approxInterval);\n          getterName = hoursGetterName(isUTC);\n          setterName = hoursSetterName(isUTC);\n          break;\n        case 'minute':\n          interval = getMinutesAndSecondsInterval(approxInterval, true);\n          getterName = minutesGetterName(isUTC);\n          setterName = minutesSetterName(isUTC);\n          break;\n        case 'second':\n          interval = getMinutesAndSecondsInterval(approxInterval, false);\n          getterName = secondsGetterName(isUTC);\n          setterName = secondsSetterName(isUTC);\n          break;\n        case 'millisecond':\n          interval = getMillisecondsInterval(approxInterval);\n          getterName = millisecondsGetterName(isUTC);\n          setterName = millisecondsSetterName(isUTC);\n          break;\n      }\n      addTicksInSpan(interval, startTick, endTick, getterName, setterName, isDate, newAddedTicks);\n      if (unitName === 'year' && levelTicks.length > 1 && i === 0) {\n        // Add nearest years to the left extent.\n        levelTicks.unshift({\n          value: levelTicks[0].value - interval\n        });\n      }\n    }\n    for (var i = 0; i < newAddedTicks.length; i++) {\n      levelTicks.push(newAddedTicks[i]);\n    }\n    // newAddedTicks.length && console.log(unitName, newAddedTicks);\n    return newAddedTicks;\n  }\n  var levelsTicks = [];\n  var currentLevelTicks = [];\n  var tickCount = 0;\n  var lastLevelTickCount = 0;\n  for (var i = 0; i < unitNames.length && iter++ < safeLimit; ++i) {\n    var primaryTimeUnit = getPrimaryTimeUnit(unitNames[i]);\n    if (!isPrimaryTimeUnit(unitNames[i])) {\n      // TODO\n      continue;\n    }\n    addLevelTicks(unitNames[i], levelsTicks[levelsTicks.length - 1] || [], currentLevelTicks);\n    var nextPrimaryTimeUnit = unitNames[i + 1] ? getPrimaryTimeUnit(unitNames[i + 1]) : null;\n    if (primaryTimeUnit !== nextPrimaryTimeUnit) {\n      if (currentLevelTicks.length) {\n        lastLevelTickCount = tickCount;\n        // Remove the duplicate so the tick count can be precisely.\n        currentLevelTicks.sort(function (a, b) {\n          return a.value - b.value;\n        });\n        var levelTicksRemoveDuplicated = [];\n        for (var i_1 = 0; i_1 < currentLevelTicks.length; ++i_1) {\n          var tickValue = currentLevelTicks[i_1].value;\n          if (i_1 === 0 || currentLevelTicks[i_1 - 1].value !== tickValue) {\n            levelTicksRemoveDuplicated.push(currentLevelTicks[i_1]);\n            if (tickValue >= extent[0] && tickValue <= extent[1]) {\n              tickCount++;\n            }\n          }\n        }\n        var targetTickNum = (extent[1] - extent[0]) / approxInterval;\n        // Added too much in this level and not too less in last level\n        if (tickCount > targetTickNum * 1.5 && lastLevelTickCount > targetTickNum / 1.5) {\n          break;\n        }\n        // Only treat primary time unit as one level.\n        levelsTicks.push(levelTicksRemoveDuplicated);\n        if (tickCount > targetTickNum || bottomUnitName === unitNames[i]) {\n          break;\n        }\n      }\n      // Reset if next unitName is primary\n      currentLevelTicks = [];\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (iter >= safeLimit) {\n      warn('Exceed safe limit.');\n    }\n  }\n  var levelsTicksInExtent = filter(map(levelsTicks, function (levelTicks) {\n    return filter(levelTicks, function (tick) {\n      return tick.value >= extent[0] && tick.value <= extent[1] && !tick.notAdd;\n    });\n  }), function (levelTicks) {\n    return levelTicks.length > 0;\n  });\n  var ticks = [];\n  var maxLevel = levelsTicksInExtent.length - 1;\n  for (var i = 0; i < levelsTicksInExtent.length; ++i) {\n    var levelTicks = levelsTicksInExtent[i];\n    for (var k = 0; k < levelTicks.length; ++k) {\n      ticks.push({\n        value: levelTicks[k].value,\n        level: maxLevel - i\n      });\n    }\n  }\n  ticks.sort(function (a, b) {\n    return a.value - b.value;\n  });\n  // Remove duplicates\n  var result = [];\n  for (var i = 0; i < ticks.length; ++i) {\n    if (i === 0 || ticks[i].value !== ticks[i - 1].value) {\n      result.push(ticks[i]);\n    }\n  }\n  return result;\n}\nScale.registerClass(TimeScale);\nexport default TimeScale;", "map": {"version": 3, "names": ["__extends", "numberUtil", "ONE_SECOND", "ONE_MINUTE", "ONE_HOUR", "ONE_DAY", "ONE_YEAR", "format", "leveledFormat", "getUnitValue", "timeUnits", "fullLeveledFormatter", "getPrimaryTimeUnit", "isPrimaryTimeUnit", "getDefaultFormatPrecisionOfInterval", "fullYearGetterName", "monthSetterName", "fullYearSetterName", "dateSetterName", "hoursGetterName", "hoursSetterName", "minutesSetterName", "seconds<PERSON><PERSON><PERSON><PERSON><PERSON>", "millisecondsSetterName", "monthGetterName", "dateGetterName", "minutesGetterName", "seconds<PERSON><PERSON><PERSON><PERSON><PERSON>", "millisecondsGetterName", "scaleHelper", "IntervalScale", "Scale", "warn", "filter", "isNumber", "map", "bisect", "a", "x", "lo", "hi", "mid", "TimeScale", "_super", "settings", "_this", "call", "type", "prototype", "get<PERSON><PERSON><PERSON>", "tick", "useUTC", "getSetting", "value", "_minLevelUnit", "second", "getFormattedLabel", "idx", "labelFormatter", "isUTC", "lang", "getTicks", "interval", "_interval", "extent", "_extent", "ticks", "push", "level", "innerTicks", "getIntervalTicks", "_approxInterval", "concat", "calcNiceExtent", "opt", "Infinity", "d", "Date", "getFullYear", "getMonth", "getDate", "calcNiceTicks", "splitNumber", "minInterval", "maxInterval", "approxTickNum", "span", "scaleIntervalsLen", "scaleIntervals", "length", "Math", "min", "max", "parse", "val", "parseDate", "contain", "normalize", "scale", "isUnitValueSame", "unit", "valueA", "valueB", "dateA", "dateB", "isSame", "isSameYear", "isSameMonth", "isSameDay", "isSameHour", "isSameMinute", "isSameSecond", "isSameMilliSecond", "getDateInterval", "approxInterval", "daysInMonth", "getMonthInterval", "APPROX_ONE_MONTH", "getHourInterval", "getMinutesAndSecondsInterval", "isMinutes", "getMillisecondsInterval", "nice", "getFirstTimestampOfUnit", "date", "unitName", "outDate", "getTime", "bottomUnitName", "safeLimit", "unitNames", "iter", "addTicksInSpan", "minTimestamp", "maxTimestamp", "getMethodName", "setMethodName", "isDate", "out", "dateTime", "notAdd", "addLevelTicks", "lastLevelTicks", "levelTicks", "newAddedTicks", "isFirstLevel", "i", "startTick", "endTick", "getterName", "setter<PERSON><PERSON>", "round", "unshift", "levelsTicks", "currentLevelTicks", "tickCount", "lastLevelTickCount", "primaryTimeUnit", "nextPrimaryTimeUnit", "sort", "b", "levelTicksRemoveDuplicated", "i_1", "tickValue", "targetTickNum", "process", "env", "NODE_ENV", "levelsTicksInExtent", "maxLevel", "k", "result", "registerClass"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/scale/Time.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The \"scaleLevels\" was originally copied from \"d3.js\" with some\r\n* modifications made for this project.\r\n* (See more details in the comment on the definition of \"scaleLevels\" below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\n// [About UTC and local time zone]:\n// In most cases, `number.parseDate` will treat input data string as local time\n// (except time zone is specified in time string). And `format.formateTime` returns\n// local time by default. option.useUTC is false by default. This design has\n// considered these common cases:\n// (1) Time that is persistent in server is in UTC, but it is needed to be displayed\n// in local time by default.\n// (2) By default, the input data string (e.g., '2011-01-02') should be displayed\n// as its original time, without any time difference.\nimport * as numberUtil from '../util/number.js';\nimport { ONE_SECOND, ONE_MINUTE, ONE_HOUR, ONE_DAY, ONE_YEAR, format, leveledFormat, getUnitValue, timeUnits, fullLeveledFormatter, getPrimaryTimeUnit, isPrimaryTimeUnit, getDefaultFormatPrecisionOfInterval, fullYearGetterName, monthSetterName, fullYearSetterName, dateSetterName, hoursGetterName, hoursSetterName, minutesSetterName, secondsSetterName, millisecondsSetterName, monthGetterName, dateGetterName, minutesGetterName, secondsGetterName, millisecondsGetterName } from '../util/time.js';\nimport * as scaleHelper from './helper.js';\nimport IntervalScale from './Interval.js';\nimport Scale from './Scale.js';\nimport { warn } from '../util/log.js';\nimport { filter, isNumber, map } from 'zrender/lib/core/util.js';\n// FIXME 公用？\nvar bisect = function (a, x, lo, hi) {\n  while (lo < hi) {\n    var mid = lo + hi >>> 1;\n    if (a[mid][1] < x) {\n      lo = mid + 1;\n    } else {\n      hi = mid;\n    }\n  }\n  return lo;\n};\nvar TimeScale = /** @class */function (_super) {\n  __extends(TimeScale, _super);\n  function TimeScale(settings) {\n    var _this = _super.call(this, settings) || this;\n    _this.type = 'time';\n    return _this;\n  }\n  /**\r\n   * Get label is mainly for other components like dataZoom, tooltip.\r\n   */\n  TimeScale.prototype.getLabel = function (tick) {\n    var useUTC = this.getSetting('useUTC');\n    return format(tick.value, fullLeveledFormatter[getDefaultFormatPrecisionOfInterval(getPrimaryTimeUnit(this._minLevelUnit))] || fullLeveledFormatter.second, useUTC, this.getSetting('locale'));\n  };\n  TimeScale.prototype.getFormattedLabel = function (tick, idx, labelFormatter) {\n    var isUTC = this.getSetting('useUTC');\n    var lang = this.getSetting('locale');\n    return leveledFormat(tick, idx, labelFormatter, lang, isUTC);\n  };\n  /**\r\n   * @override\r\n   */\n  TimeScale.prototype.getTicks = function () {\n    var interval = this._interval;\n    var extent = this._extent;\n    var ticks = [];\n    // If interval is 0, return [];\n    if (!interval) {\n      return ticks;\n    }\n    ticks.push({\n      value: extent[0],\n      level: 0\n    });\n    var useUTC = this.getSetting('useUTC');\n    var innerTicks = getIntervalTicks(this._minLevelUnit, this._approxInterval, useUTC, extent);\n    ticks = ticks.concat(innerTicks);\n    ticks.push({\n      value: extent[1],\n      level: 0\n    });\n    return ticks;\n  };\n  TimeScale.prototype.calcNiceExtent = function (opt) {\n    var extent = this._extent;\n    // If extent start and end are same, expand them\n    if (extent[0] === extent[1]) {\n      // Expand extent\n      extent[0] -= ONE_DAY;\n      extent[1] += ONE_DAY;\n    }\n    // If there are no data and extent are [Infinity, -Infinity]\n    if (extent[1] === -Infinity && extent[0] === Infinity) {\n      var d = new Date();\n      extent[1] = +new Date(d.getFullYear(), d.getMonth(), d.getDate());\n      extent[0] = extent[1] - ONE_DAY;\n    }\n    this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);\n  };\n  TimeScale.prototype.calcNiceTicks = function (approxTickNum, minInterval, maxInterval) {\n    approxTickNum = approxTickNum || 10;\n    var extent = this._extent;\n    var span = extent[1] - extent[0];\n    this._approxInterval = span / approxTickNum;\n    if (minInterval != null && this._approxInterval < minInterval) {\n      this._approxInterval = minInterval;\n    }\n    if (maxInterval != null && this._approxInterval > maxInterval) {\n      this._approxInterval = maxInterval;\n    }\n    var scaleIntervalsLen = scaleIntervals.length;\n    var idx = Math.min(bisect(scaleIntervals, this._approxInterval, 0, scaleIntervalsLen), scaleIntervalsLen - 1);\n    // Interval that can be used to calculate ticks\n    this._interval = scaleIntervals[idx][1];\n    // Min level used when picking ticks from top down.\n    // We check one more level to avoid the ticks are to sparse in some case.\n    this._minLevelUnit = scaleIntervals[Math.max(idx - 1, 0)][0];\n  };\n  TimeScale.prototype.parse = function (val) {\n    // val might be float.\n    return isNumber(val) ? val : +numberUtil.parseDate(val);\n  };\n  TimeScale.prototype.contain = function (val) {\n    return scaleHelper.contain(this.parse(val), this._extent);\n  };\n  TimeScale.prototype.normalize = function (val) {\n    return scaleHelper.normalize(this.parse(val), this._extent);\n  };\n  TimeScale.prototype.scale = function (val) {\n    return scaleHelper.scale(val, this._extent);\n  };\n  TimeScale.type = 'time';\n  return TimeScale;\n}(IntervalScale);\n/**\r\n * This implementation was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3/blob/b516d77fb8566b576088e73410437494717ada26/src/time/scale.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n */\nvar scaleIntervals = [\n// Format                           interval\n['second', ONE_SECOND], ['minute', ONE_MINUTE], ['hour', ONE_HOUR], ['quarter-day', ONE_HOUR * 6], ['half-day', ONE_HOUR * 12], ['day', ONE_DAY * 1.2], ['half-week', ONE_DAY * 3.5], ['week', ONE_DAY * 7], ['month', ONE_DAY * 31], ['quarter', ONE_DAY * 95], ['half-year', ONE_YEAR / 2], ['year', ONE_YEAR] // 1Y\n];\nfunction isUnitValueSame(unit, valueA, valueB, isUTC) {\n  var dateA = numberUtil.parseDate(valueA);\n  var dateB = numberUtil.parseDate(valueB);\n  var isSame = function (unit) {\n    return getUnitValue(dateA, unit, isUTC) === getUnitValue(dateB, unit, isUTC);\n  };\n  var isSameYear = function () {\n    return isSame('year');\n  };\n  // const isSameHalfYear = () => isSameYear() && isSame('half-year');\n  // const isSameQuater = () => isSameYear() && isSame('quarter');\n  var isSameMonth = function () {\n    return isSameYear() && isSame('month');\n  };\n  var isSameDay = function () {\n    return isSameMonth() && isSame('day');\n  };\n  // const isSameHalfDay = () => isSameDay() && isSame('half-day');\n  var isSameHour = function () {\n    return isSameDay() && isSame('hour');\n  };\n  var isSameMinute = function () {\n    return isSameHour() && isSame('minute');\n  };\n  var isSameSecond = function () {\n    return isSameMinute() && isSame('second');\n  };\n  var isSameMilliSecond = function () {\n    return isSameSecond() && isSame('millisecond');\n  };\n  switch (unit) {\n    case 'year':\n      return isSameYear();\n    case 'month':\n      return isSameMonth();\n    case 'day':\n      return isSameDay();\n    case 'hour':\n      return isSameHour();\n    case 'minute':\n      return isSameMinute();\n    case 'second':\n      return isSameSecond();\n    case 'millisecond':\n      return isSameMilliSecond();\n  }\n}\n// const primaryUnitGetters = {\n//     year: fullYearGetterName(),\n//     month: monthGetterName(),\n//     day: dateGetterName(),\n//     hour: hoursGetterName(),\n//     minute: minutesGetterName(),\n//     second: secondsGetterName(),\n//     millisecond: millisecondsGetterName()\n// };\n// const primaryUnitUTCGetters = {\n//     year: fullYearGetterName(true),\n//     month: monthGetterName(true),\n//     day: dateGetterName(true),\n//     hour: hoursGetterName(true),\n//     minute: minutesGetterName(true),\n//     second: secondsGetterName(true),\n//     millisecond: millisecondsGetterName(true)\n// };\n// function moveTick(date: Date, unitName: TimeUnit, step: number, isUTC: boolean) {\n//     step = step || 1;\n//     switch (getPrimaryTimeUnit(unitName)) {\n//         case 'year':\n//             date[fullYearSetterName(isUTC)](date[fullYearGetterName(isUTC)]() + step);\n//             break;\n//         case 'month':\n//             date[monthSetterName(isUTC)](date[monthGetterName(isUTC)]() + step);\n//             break;\n//         case 'day':\n//             date[dateSetterName(isUTC)](date[dateGetterName(isUTC)]() + step);\n//             break;\n//         case 'hour':\n//             date[hoursSetterName(isUTC)](date[hoursGetterName(isUTC)]() + step);\n//             break;\n//         case 'minute':\n//             date[minutesSetterName(isUTC)](date[minutesGetterName(isUTC)]() + step);\n//             break;\n//         case 'second':\n//             date[secondsSetterName(isUTC)](date[secondsGetterName(isUTC)]() + step);\n//             break;\n//         case 'millisecond':\n//             date[millisecondsSetterName(isUTC)](date[millisecondsGetterName(isUTC)]() + step);\n//             break;\n//     }\n//     return date.getTime();\n// }\n// const DATE_INTERVALS = [[8, 7.5], [4, 3.5], [2, 1.5]];\n// const MONTH_INTERVALS = [[6, 5.5], [3, 2.5], [2, 1.5]];\n// const MINUTES_SECONDS_INTERVALS = [[30, 30], [20, 20], [15, 15], [10, 10], [5, 5], [2, 2]];\nfunction getDateInterval(approxInterval, daysInMonth) {\n  approxInterval /= ONE_DAY;\n  return approxInterval > 16 ? 16\n  // Math.floor(daysInMonth / 2) + 1  // In this case we only want one tick between two months.\n  : approxInterval > 7.5 ? 7 // TODO week 7 or day 8?\n  : approxInterval > 3.5 ? 4 : approxInterval > 1.5 ? 2 : 1;\n}\nfunction getMonthInterval(approxInterval) {\n  var APPROX_ONE_MONTH = 30 * ONE_DAY;\n  approxInterval /= APPROX_ONE_MONTH;\n  return approxInterval > 6 ? 6 : approxInterval > 3 ? 3 : approxInterval > 2 ? 2 : 1;\n}\nfunction getHourInterval(approxInterval) {\n  approxInterval /= ONE_HOUR;\n  return approxInterval > 12 ? 12 : approxInterval > 6 ? 6 : approxInterval > 3.5 ? 4 : approxInterval > 2 ? 2 : 1;\n}\nfunction getMinutesAndSecondsInterval(approxInterval, isMinutes) {\n  approxInterval /= isMinutes ? ONE_MINUTE : ONE_SECOND;\n  return approxInterval > 30 ? 30 : approxInterval > 20 ? 20 : approxInterval > 15 ? 15 : approxInterval > 10 ? 10 : approxInterval > 5 ? 5 : approxInterval > 2 ? 2 : 1;\n}\nfunction getMillisecondsInterval(approxInterval) {\n  return numberUtil.nice(approxInterval, true);\n}\nfunction getFirstTimestampOfUnit(date, unitName, isUTC) {\n  var outDate = new Date(date);\n  switch (getPrimaryTimeUnit(unitName)) {\n    case 'year':\n    case 'month':\n      outDate[monthSetterName(isUTC)](0);\n    case 'day':\n      outDate[dateSetterName(isUTC)](1);\n    case 'hour':\n      outDate[hoursSetterName(isUTC)](0);\n    case 'minute':\n      outDate[minutesSetterName(isUTC)](0);\n    case 'second':\n      outDate[secondsSetterName(isUTC)](0);\n      outDate[millisecondsSetterName(isUTC)](0);\n  }\n  return outDate.getTime();\n}\nfunction getIntervalTicks(bottomUnitName, approxInterval, isUTC, extent) {\n  var safeLimit = 10000;\n  var unitNames = timeUnits;\n  var iter = 0;\n  function addTicksInSpan(interval, minTimestamp, maxTimestamp, getMethodName, setMethodName, isDate, out) {\n    var date = new Date(minTimestamp);\n    var dateTime = minTimestamp;\n    var d = date[getMethodName]();\n    // if (isDate) {\n    //     d -= 1; // Starts with 0;   PENDING\n    // }\n    while (dateTime < maxTimestamp && dateTime <= extent[1]) {\n      out.push({\n        value: dateTime\n      });\n      d += interval;\n      date[setMethodName](d);\n      dateTime = date.getTime();\n    }\n    // This extra tick is for calcuating ticks of next level. Will not been added to the final result\n    out.push({\n      value: dateTime,\n      notAdd: true\n    });\n  }\n  function addLevelTicks(unitName, lastLevelTicks, levelTicks) {\n    var newAddedTicks = [];\n    var isFirstLevel = !lastLevelTicks.length;\n    if (isUnitValueSame(getPrimaryTimeUnit(unitName), extent[0], extent[1], isUTC)) {\n      return;\n    }\n    if (isFirstLevel) {\n      lastLevelTicks = [{\n        // TODO Optimize. Not include so may ticks.\n        value: getFirstTimestampOfUnit(new Date(extent[0]), unitName, isUTC)\n      }, {\n        value: extent[1]\n      }];\n    }\n    for (var i = 0; i < lastLevelTicks.length - 1; i++) {\n      var startTick = lastLevelTicks[i].value;\n      var endTick = lastLevelTicks[i + 1].value;\n      if (startTick === endTick) {\n        continue;\n      }\n      var interval = void 0;\n      var getterName = void 0;\n      var setterName = void 0;\n      var isDate = false;\n      switch (unitName) {\n        case 'year':\n          interval = Math.max(1, Math.round(approxInterval / ONE_DAY / 365));\n          getterName = fullYearGetterName(isUTC);\n          setterName = fullYearSetterName(isUTC);\n          break;\n        case 'half-year':\n        case 'quarter':\n        case 'month':\n          interval = getMonthInterval(approxInterval);\n          getterName = monthGetterName(isUTC);\n          setterName = monthSetterName(isUTC);\n          break;\n        case 'week': // PENDING If week is added. Ignore day.\n        case 'half-week':\n        case 'day':\n          interval = getDateInterval(approxInterval, 31); // Use 32 days and let interval been 16\n          getterName = dateGetterName(isUTC);\n          setterName = dateSetterName(isUTC);\n          isDate = true;\n          break;\n        case 'half-day':\n        case 'quarter-day':\n        case 'hour':\n          interval = getHourInterval(approxInterval);\n          getterName = hoursGetterName(isUTC);\n          setterName = hoursSetterName(isUTC);\n          break;\n        case 'minute':\n          interval = getMinutesAndSecondsInterval(approxInterval, true);\n          getterName = minutesGetterName(isUTC);\n          setterName = minutesSetterName(isUTC);\n          break;\n        case 'second':\n          interval = getMinutesAndSecondsInterval(approxInterval, false);\n          getterName = secondsGetterName(isUTC);\n          setterName = secondsSetterName(isUTC);\n          break;\n        case 'millisecond':\n          interval = getMillisecondsInterval(approxInterval);\n          getterName = millisecondsGetterName(isUTC);\n          setterName = millisecondsSetterName(isUTC);\n          break;\n      }\n      addTicksInSpan(interval, startTick, endTick, getterName, setterName, isDate, newAddedTicks);\n      if (unitName === 'year' && levelTicks.length > 1 && i === 0) {\n        // Add nearest years to the left extent.\n        levelTicks.unshift({\n          value: levelTicks[0].value - interval\n        });\n      }\n    }\n    for (var i = 0; i < newAddedTicks.length; i++) {\n      levelTicks.push(newAddedTicks[i]);\n    }\n    // newAddedTicks.length && console.log(unitName, newAddedTicks);\n    return newAddedTicks;\n  }\n  var levelsTicks = [];\n  var currentLevelTicks = [];\n  var tickCount = 0;\n  var lastLevelTickCount = 0;\n  for (var i = 0; i < unitNames.length && iter++ < safeLimit; ++i) {\n    var primaryTimeUnit = getPrimaryTimeUnit(unitNames[i]);\n    if (!isPrimaryTimeUnit(unitNames[i])) {\n      // TODO\n      continue;\n    }\n    addLevelTicks(unitNames[i], levelsTicks[levelsTicks.length - 1] || [], currentLevelTicks);\n    var nextPrimaryTimeUnit = unitNames[i + 1] ? getPrimaryTimeUnit(unitNames[i + 1]) : null;\n    if (primaryTimeUnit !== nextPrimaryTimeUnit) {\n      if (currentLevelTicks.length) {\n        lastLevelTickCount = tickCount;\n        // Remove the duplicate so the tick count can be precisely.\n        currentLevelTicks.sort(function (a, b) {\n          return a.value - b.value;\n        });\n        var levelTicksRemoveDuplicated = [];\n        for (var i_1 = 0; i_1 < currentLevelTicks.length; ++i_1) {\n          var tickValue = currentLevelTicks[i_1].value;\n          if (i_1 === 0 || currentLevelTicks[i_1 - 1].value !== tickValue) {\n            levelTicksRemoveDuplicated.push(currentLevelTicks[i_1]);\n            if (tickValue >= extent[0] && tickValue <= extent[1]) {\n              tickCount++;\n            }\n          }\n        }\n        var targetTickNum = (extent[1] - extent[0]) / approxInterval;\n        // Added too much in this level and not too less in last level\n        if (tickCount > targetTickNum * 1.5 && lastLevelTickCount > targetTickNum / 1.5) {\n          break;\n        }\n        // Only treat primary time unit as one level.\n        levelsTicks.push(levelTicksRemoveDuplicated);\n        if (tickCount > targetTickNum || bottomUnitName === unitNames[i]) {\n          break;\n        }\n      }\n      // Reset if next unitName is primary\n      currentLevelTicks = [];\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (iter >= safeLimit) {\n      warn('Exceed safe limit.');\n    }\n  }\n  var levelsTicksInExtent = filter(map(levelsTicks, function (levelTicks) {\n    return filter(levelTicks, function (tick) {\n      return tick.value >= extent[0] && tick.value <= extent[1] && !tick.notAdd;\n    });\n  }), function (levelTicks) {\n    return levelTicks.length > 0;\n  });\n  var ticks = [];\n  var maxLevel = levelsTicksInExtent.length - 1;\n  for (var i = 0; i < levelsTicksInExtent.length; ++i) {\n    var levelTicks = levelsTicksInExtent[i];\n    for (var k = 0; k < levelTicks.length; ++k) {\n      ticks.push({\n        value: levelTicks[k].value,\n        level: maxLevel - i\n      });\n    }\n  }\n  ticks.sort(function (a, b) {\n    return a.value - b.value;\n  });\n  // Remove duplicates\n  var result = [];\n  for (var i = 0; i < ticks.length; ++i) {\n    if (i === 0 || ticks[i].value !== ticks[i - 1].value) {\n      result.push(ticks[i]);\n    }\n  }\n  return result;\n}\nScale.registerClass(TimeScale);\nexport default TimeScale;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKC,UAAU,MAAM,mBAAmB;AAC/C,SAASC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,aAAa,EAAEC,YAAY,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,mCAAmC,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,sBAAsB,QAAQ,iBAAiB;AAC/e,OAAO,KAAKC,WAAW,MAAM,aAAa;AAC1C,OAAOC,aAAa,MAAM,eAAe;AACzC,OAAOC,KAAK,MAAM,YAAY;AAC9B,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,0BAA0B;AAChE;AACA,IAAIC,MAAM,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACnC,OAAOD,EAAE,GAAGC,EAAE,EAAE;IACd,IAAIC,GAAG,GAAGF,EAAE,GAAGC,EAAE,KAAK,CAAC;IACvB,IAAIH,CAAC,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGH,CAAC,EAAE;MACjBC,EAAE,GAAGE,GAAG,GAAG,CAAC;IACd,CAAC,MAAM;MACLD,EAAE,GAAGC,GAAG;IACV;EACF;EACA,OAAOF,EAAE;AACX,CAAC;AACD,IAAIG,SAAS,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC7C3C,SAAS,CAAC0C,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAACE,QAAQ,EAAE;IAC3B,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,QAAQ,CAAC,IAAI,IAAI;IAC/CC,KAAK,CAACE,IAAI,GAAG,MAAM;IACnB,OAAOF,KAAK;EACd;EACA;AACF;AACA;EACEH,SAAS,CAACM,SAAS,CAACC,QAAQ,GAAG,UAAUC,IAAI,EAAE;IAC7C,IAAIC,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC;IACtC,OAAO7C,MAAM,CAAC2C,IAAI,CAACG,KAAK,EAAE1C,oBAAoB,CAACG,mCAAmC,CAACF,kBAAkB,CAAC,IAAI,CAAC0C,aAAa,CAAC,CAAC,CAAC,IAAI3C,oBAAoB,CAAC4C,MAAM,EAAEJ,MAAM,EAAE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC;EAChM,CAAC;EACDV,SAAS,CAACM,SAAS,CAACQ,iBAAiB,GAAG,UAAUN,IAAI,EAAEO,GAAG,EAAEC,cAAc,EAAE;IAC3E,IAAIC,KAAK,GAAG,IAAI,CAACP,UAAU,CAAC,QAAQ,CAAC;IACrC,IAAIQ,IAAI,GAAG,IAAI,CAACR,UAAU,CAAC,QAAQ,CAAC;IACpC,OAAO5C,aAAa,CAAC0C,IAAI,EAAEO,GAAG,EAAEC,cAAc,EAAEE,IAAI,EAAED,KAAK,CAAC;EAC9D,CAAC;EACD;AACF;AACA;EACEjB,SAAS,CAACM,SAAS,CAACa,QAAQ,GAAG,YAAY;IACzC,IAAIC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC7B,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAIC,KAAK,GAAG,EAAE;IACd;IACA,IAAI,CAACJ,QAAQ,EAAE;MACb,OAAOI,KAAK;IACd;IACAA,KAAK,CAACC,IAAI,CAAC;MACTd,KAAK,EAAEW,MAAM,CAAC,CAAC,CAAC;MAChBI,KAAK,EAAE;IACT,CAAC,CAAC;IACF,IAAIjB,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC;IACtC,IAAIiB,UAAU,GAAGC,gBAAgB,CAAC,IAAI,CAAChB,aAAa,EAAE,IAAI,CAACiB,eAAe,EAAEpB,MAAM,EAAEa,MAAM,CAAC;IAC3FE,KAAK,GAAGA,KAAK,CAACM,MAAM,CAACH,UAAU,CAAC;IAChCH,KAAK,CAACC,IAAI,CAAC;MACTd,KAAK,EAAEW,MAAM,CAAC,CAAC,CAAC;MAChBI,KAAK,EAAE;IACT,CAAC,CAAC;IACF,OAAOF,KAAK;EACd,CAAC;EACDxB,SAAS,CAACM,SAAS,CAACyB,cAAc,GAAG,UAAUC,GAAG,EAAE;IAClD,IAAIV,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB;IACA,IAAID,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,EAAE;MAC3B;MACAA,MAAM,CAAC,CAAC,CAAC,IAAI3D,OAAO;MACpB2D,MAAM,CAAC,CAAC,CAAC,IAAI3D,OAAO;IACtB;IACA;IACA,IAAI2D,MAAM,CAAC,CAAC,CAAC,KAAK,CAACW,QAAQ,IAAIX,MAAM,CAAC,CAAC,CAAC,KAAKW,QAAQ,EAAE;MACrD,IAAIC,CAAC,GAAG,IAAIC,IAAI,CAAC,CAAC;MAClBb,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIa,IAAI,CAACD,CAAC,CAACE,WAAW,CAAC,CAAC,EAAEF,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAEH,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC;MACjEhB,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAG3D,OAAO;IACjC;IACA,IAAI,CAAC4E,aAAa,CAACP,GAAG,CAACQ,WAAW,EAAER,GAAG,CAACS,WAAW,EAAET,GAAG,CAACU,WAAW,CAAC;EACvE,CAAC;EACD1C,SAAS,CAACM,SAAS,CAACiC,aAAa,GAAG,UAAUI,aAAa,EAAEF,WAAW,EAAEC,WAAW,EAAE;IACrFC,aAAa,GAAGA,aAAa,IAAI,EAAE;IACnC,IAAIrB,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAIqB,IAAI,GAAGtB,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IAChC,IAAI,CAACO,eAAe,GAAGe,IAAI,GAAGD,aAAa;IAC3C,IAAIF,WAAW,IAAI,IAAI,IAAI,IAAI,CAACZ,eAAe,GAAGY,WAAW,EAAE;MAC7D,IAAI,CAACZ,eAAe,GAAGY,WAAW;IACpC;IACA,IAAIC,WAAW,IAAI,IAAI,IAAI,IAAI,CAACb,eAAe,GAAGa,WAAW,EAAE;MAC7D,IAAI,CAACb,eAAe,GAAGa,WAAW;IACpC;IACA,IAAIG,iBAAiB,GAAGC,cAAc,CAACC,MAAM;IAC7C,IAAIhC,GAAG,GAAGiC,IAAI,CAACC,GAAG,CAACvD,MAAM,CAACoD,cAAc,EAAE,IAAI,CAACjB,eAAe,EAAE,CAAC,EAAEgB,iBAAiB,CAAC,EAAEA,iBAAiB,GAAG,CAAC,CAAC;IAC7G;IACA,IAAI,CAACxB,SAAS,GAAGyB,cAAc,CAAC/B,GAAG,CAAC,CAAC,CAAC,CAAC;IACvC;IACA;IACA,IAAI,CAACH,aAAa,GAAGkC,cAAc,CAACE,IAAI,CAACE,GAAG,CAACnC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC;EACDf,SAAS,CAACM,SAAS,CAAC6C,KAAK,GAAG,UAAUC,GAAG,EAAE;IACzC;IACA,OAAO5D,QAAQ,CAAC4D,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC7F,UAAU,CAAC8F,SAAS,CAACD,GAAG,CAAC;EACzD,CAAC;EACDpD,SAAS,CAACM,SAAS,CAACgD,OAAO,GAAG,UAAUF,GAAG,EAAE;IAC3C,OAAOjE,WAAW,CAACmE,OAAO,CAAC,IAAI,CAACH,KAAK,CAACC,GAAG,CAAC,EAAE,IAAI,CAAC7B,OAAO,CAAC;EAC3D,CAAC;EACDvB,SAAS,CAACM,SAAS,CAACiD,SAAS,GAAG,UAAUH,GAAG,EAAE;IAC7C,OAAOjE,WAAW,CAACoE,SAAS,CAAC,IAAI,CAACJ,KAAK,CAACC,GAAG,CAAC,EAAE,IAAI,CAAC7B,OAAO,CAAC;EAC7D,CAAC;EACDvB,SAAS,CAACM,SAAS,CAACkD,KAAK,GAAG,UAAUJ,GAAG,EAAE;IACzC,OAAOjE,WAAW,CAACqE,KAAK,CAACJ,GAAG,EAAE,IAAI,CAAC7B,OAAO,CAAC;EAC7C,CAAC;EACDvB,SAAS,CAACK,IAAI,GAAG,MAAM;EACvB,OAAOL,SAAS;AAClB,CAAC,CAACZ,aAAa,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI0D,cAAc,GAAG;AACrB;AACA,CAAC,QAAQ,EAAEtF,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAEC,UAAU,CAAC,EAAE,CAAC,MAAM,EAAEC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAEA,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,UAAU,EAAEA,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,KAAK,EAAEC,OAAO,GAAG,GAAG,CAAC,EAAE,CAAC,WAAW,EAAEA,OAAO,GAAG,GAAG,CAAC,EAAE,CAAC,MAAM,EAAEA,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAEA,OAAO,GAAG,EAAE,CAAC,EAAE,CAAC,SAAS,EAAEA,OAAO,GAAG,EAAE,CAAC,EAAE,CAAC,WAAW,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,EAAEA,QAAQ,CAAC,CAAC;AAAA,CAChT;AACD,SAAS6F,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAE3C,KAAK,EAAE;EACpD,IAAI4C,KAAK,GAAGtG,UAAU,CAAC8F,SAAS,CAACM,MAAM,CAAC;EACxC,IAAIG,KAAK,GAAGvG,UAAU,CAAC8F,SAAS,CAACO,MAAM,CAAC;EACxC,IAAIG,MAAM,GAAG,SAAAA,CAAUL,IAAI,EAAE;IAC3B,OAAO3F,YAAY,CAAC8F,KAAK,EAAEH,IAAI,EAAEzC,KAAK,CAAC,KAAKlD,YAAY,CAAC+F,KAAK,EAAEJ,IAAI,EAAEzC,KAAK,CAAC;EAC9E,CAAC;EACD,IAAI+C,UAAU,GAAG,SAAAA,CAAA,EAAY;IAC3B,OAAOD,MAAM,CAAC,MAAM,CAAC;EACvB,CAAC;EACD;EACA;EACA,IAAIE,WAAW,GAAG,SAAAA,CAAA,EAAY;IAC5B,OAAOD,UAAU,CAAC,CAAC,IAAID,MAAM,CAAC,OAAO,CAAC;EACxC,CAAC;EACD,IAAIG,SAAS,GAAG,SAAAA,CAAA,EAAY;IAC1B,OAAOD,WAAW,CAAC,CAAC,IAAIF,MAAM,CAAC,KAAK,CAAC;EACvC,CAAC;EACD;EACA,IAAII,UAAU,GAAG,SAAAA,CAAA,EAAY;IAC3B,OAAOD,SAAS,CAAC,CAAC,IAAIH,MAAM,CAAC,MAAM,CAAC;EACtC,CAAC;EACD,IAAIK,YAAY,GAAG,SAAAA,CAAA,EAAY;IAC7B,OAAOD,UAAU,CAAC,CAAC,IAAIJ,MAAM,CAAC,QAAQ,CAAC;EACzC,CAAC;EACD,IAAIM,YAAY,GAAG,SAAAA,CAAA,EAAY;IAC7B,OAAOD,YAAY,CAAC,CAAC,IAAIL,MAAM,CAAC,QAAQ,CAAC;EAC3C,CAAC;EACD,IAAIO,iBAAiB,GAAG,SAAAA,CAAA,EAAY;IAClC,OAAOD,YAAY,CAAC,CAAC,IAAIN,MAAM,CAAC,aAAa,CAAC;EAChD,CAAC;EACD,QAAQL,IAAI;IACV,KAAK,MAAM;MACT,OAAOM,UAAU,CAAC,CAAC;IACrB,KAAK,OAAO;MACV,OAAOC,WAAW,CAAC,CAAC;IACtB,KAAK,KAAK;MACR,OAAOC,SAAS,CAAC,CAAC;IACpB,KAAK,MAAM;MACT,OAAOC,UAAU,CAAC,CAAC;IACrB,KAAK,QAAQ;MACX,OAAOC,YAAY,CAAC,CAAC;IACvB,KAAK,QAAQ;MACX,OAAOC,YAAY,CAAC,CAAC;IACvB,KAAK,aAAa;MAChB,OAAOC,iBAAiB,CAAC,CAAC;EAC9B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,cAAc,EAAEC,WAAW,EAAE;EACpDD,cAAc,IAAI7G,OAAO;EACzB,OAAO6G,cAAc,GAAG,EAAE,GAAG;EAC7B;EAAA,EACEA,cAAc,GAAG,GAAG,GAAG,CAAC,CAAC;EAAA,EACzBA,cAAc,GAAG,GAAG,GAAG,CAAC,GAAGA,cAAc,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AAC3D;AACA,SAASE,gBAAgBA,CAACF,cAAc,EAAE;EACxC,IAAIG,gBAAgB,GAAG,EAAE,GAAGhH,OAAO;EACnC6G,cAAc,IAAIG,gBAAgB;EAClC,OAAOH,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACrF;AACA,SAASI,eAAeA,CAACJ,cAAc,EAAE;EACvCA,cAAc,IAAI9G,QAAQ;EAC1B,OAAO8G,cAAc,GAAG,EAAE,GAAG,EAAE,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc,GAAG,GAAG,GAAG,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AAClH;AACA,SAASK,4BAA4BA,CAACL,cAAc,EAAEM,SAAS,EAAE;EAC/DN,cAAc,IAAIM,SAAS,GAAGrH,UAAU,GAAGD,UAAU;EACrD,OAAOgH,cAAc,GAAG,EAAE,GAAG,EAAE,GAAGA,cAAc,GAAG,EAAE,GAAG,EAAE,GAAGA,cAAc,GAAG,EAAE,GAAG,EAAE,GAAGA,cAAc,GAAG,EAAE,GAAG,EAAE,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACxK;AACA,SAASO,uBAAuBA,CAACP,cAAc,EAAE;EAC/C,OAAOjH,UAAU,CAACyH,IAAI,CAACR,cAAc,EAAE,IAAI,CAAC;AAC9C;AACA,SAASS,uBAAuBA,CAACC,IAAI,EAAEC,QAAQ,EAAElE,KAAK,EAAE;EACtD,IAAImE,OAAO,GAAG,IAAIjD,IAAI,CAAC+C,IAAI,CAAC;EAC5B,QAAQhH,kBAAkB,CAACiH,QAAQ,CAAC;IAClC,KAAK,MAAM;IACX,KAAK,OAAO;MACVC,OAAO,CAAC9G,eAAe,CAAC2C,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,KAAK,KAAK;MACRmE,OAAO,CAAC5G,cAAc,CAACyC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,KAAK,MAAM;MACTmE,OAAO,CAAC1G,eAAe,CAACuC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,KAAK,QAAQ;MACXmE,OAAO,CAACzG,iBAAiB,CAACsC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,KAAK,QAAQ;MACXmE,OAAO,CAACxG,iBAAiB,CAACqC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACpCmE,OAAO,CAACvG,sBAAsB,CAACoC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C;EACA,OAAOmE,OAAO,CAACC,OAAO,CAAC,CAAC;AAC1B;AACA,SAASzD,gBAAgBA,CAAC0D,cAAc,EAAEd,cAAc,EAAEvD,KAAK,EAAEK,MAAM,EAAE;EACvE,IAAIiE,SAAS,GAAG,KAAK;EACrB,IAAIC,SAAS,GAAGxH,SAAS;EACzB,IAAIyH,IAAI,GAAG,CAAC;EACZ,SAASC,cAAcA,CAACtE,QAAQ,EAAEuE,YAAY,EAAEC,YAAY,EAAEC,aAAa,EAAEC,aAAa,EAAEC,MAAM,EAAEC,GAAG,EAAE;IACvG,IAAId,IAAI,GAAG,IAAI/C,IAAI,CAACwD,YAAY,CAAC;IACjC,IAAIM,QAAQ,GAAGN,YAAY;IAC3B,IAAIzD,CAAC,GAAGgD,IAAI,CAACW,aAAa,CAAC,CAAC,CAAC;IAC7B;IACA;IACA;IACA,OAAOI,QAAQ,GAAGL,YAAY,IAAIK,QAAQ,IAAI3E,MAAM,CAAC,CAAC,CAAC,EAAE;MACvD0E,GAAG,CAACvE,IAAI,CAAC;QACPd,KAAK,EAAEsF;MACT,CAAC,CAAC;MACF/D,CAAC,IAAId,QAAQ;MACb8D,IAAI,CAACY,aAAa,CAAC,CAAC5D,CAAC,CAAC;MACtB+D,QAAQ,GAAGf,IAAI,CAACG,OAAO,CAAC,CAAC;IAC3B;IACA;IACAW,GAAG,CAACvE,IAAI,CAAC;MACPd,KAAK,EAAEsF,QAAQ;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA,SAASC,aAAaA,CAAChB,QAAQ,EAAEiB,cAAc,EAAEC,UAAU,EAAE;IAC3D,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIC,YAAY,GAAG,CAACH,cAAc,CAACrD,MAAM;IACzC,IAAIU,eAAe,CAACvF,kBAAkB,CAACiH,QAAQ,CAAC,EAAE7D,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEL,KAAK,CAAC,EAAE;MAC9E;IACF;IACA,IAAIsF,YAAY,EAAE;MAChBH,cAAc,GAAG,CAAC;QAChB;QACAzF,KAAK,EAAEsE,uBAAuB,CAAC,IAAI9C,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE6D,QAAQ,EAAElE,KAAK;MACrE,CAAC,EAAE;QACDN,KAAK,EAAEW,MAAM,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ;IACA,KAAK,IAAIkF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAACrD,MAAM,GAAG,CAAC,EAAEyD,CAAC,EAAE,EAAE;MAClD,IAAIC,SAAS,GAAGL,cAAc,CAACI,CAAC,CAAC,CAAC7F,KAAK;MACvC,IAAI+F,OAAO,GAAGN,cAAc,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC7F,KAAK;MACzC,IAAI8F,SAAS,KAAKC,OAAO,EAAE;QACzB;MACF;MACA,IAAItF,QAAQ,GAAG,KAAK,CAAC;MACrB,IAAIuF,UAAU,GAAG,KAAK,CAAC;MACvB,IAAIC,UAAU,GAAG,KAAK,CAAC;MACvB,IAAIb,MAAM,GAAG,KAAK;MAClB,QAAQZ,QAAQ;QACd,KAAK,MAAM;UACT/D,QAAQ,GAAG4B,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAAC6D,KAAK,CAACrC,cAAc,GAAG7G,OAAO,GAAG,GAAG,CAAC,CAAC;UAClEgJ,UAAU,GAAGtI,kBAAkB,CAAC4C,KAAK,CAAC;UACtC2F,UAAU,GAAGrI,kBAAkB,CAAC0C,KAAK,CAAC;UACtC;QACF,KAAK,WAAW;QAChB,KAAK,SAAS;QACd,KAAK,OAAO;UACVG,QAAQ,GAAGsD,gBAAgB,CAACF,cAAc,CAAC;UAC3CmC,UAAU,GAAG7H,eAAe,CAACmC,KAAK,CAAC;UACnC2F,UAAU,GAAGtI,eAAe,CAAC2C,KAAK,CAAC;UACnC;QACF,KAAK,MAAM,CAAC,CAAC;QACb,KAAK,WAAW;QAChB,KAAK,KAAK;UACRG,QAAQ,GAAGmD,eAAe,CAACC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;UAChDmC,UAAU,GAAG5H,cAAc,CAACkC,KAAK,CAAC;UAClC2F,UAAU,GAAGpI,cAAc,CAACyC,KAAK,CAAC;UAClC8E,MAAM,GAAG,IAAI;UACb;QACF,KAAK,UAAU;QACf,KAAK,aAAa;QAClB,KAAK,MAAM;UACT3E,QAAQ,GAAGwD,eAAe,CAACJ,cAAc,CAAC;UAC1CmC,UAAU,GAAGlI,eAAe,CAACwC,KAAK,CAAC;UACnC2F,UAAU,GAAGlI,eAAe,CAACuC,KAAK,CAAC;UACnC;QACF,KAAK,QAAQ;UACXG,QAAQ,GAAGyD,4BAA4B,CAACL,cAAc,EAAE,IAAI,CAAC;UAC7DmC,UAAU,GAAG3H,iBAAiB,CAACiC,KAAK,CAAC;UACrC2F,UAAU,GAAGjI,iBAAiB,CAACsC,KAAK,CAAC;UACrC;QACF,KAAK,QAAQ;UACXG,QAAQ,GAAGyD,4BAA4B,CAACL,cAAc,EAAE,KAAK,CAAC;UAC9DmC,UAAU,GAAG1H,iBAAiB,CAACgC,KAAK,CAAC;UACrC2F,UAAU,GAAGhI,iBAAiB,CAACqC,KAAK,CAAC;UACrC;QACF,KAAK,aAAa;UAChBG,QAAQ,GAAG2D,uBAAuB,CAACP,cAAc,CAAC;UAClDmC,UAAU,GAAGzH,sBAAsB,CAAC+B,KAAK,CAAC;UAC1C2F,UAAU,GAAG/H,sBAAsB,CAACoC,KAAK,CAAC;UAC1C;MACJ;MACAyE,cAAc,CAACtE,QAAQ,EAAEqF,SAAS,EAAEC,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAEb,MAAM,EAAEO,aAAa,CAAC;MAC3F,IAAInB,QAAQ,KAAK,MAAM,IAAIkB,UAAU,CAACtD,MAAM,GAAG,CAAC,IAAIyD,CAAC,KAAK,CAAC,EAAE;QAC3D;QACAH,UAAU,CAACS,OAAO,CAAC;UACjBnG,KAAK,EAAE0F,UAAU,CAAC,CAAC,CAAC,CAAC1F,KAAK,GAAGS;QAC/B,CAAC,CAAC;MACJ;IACF;IACA,KAAK,IAAIoF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,CAACvD,MAAM,EAAEyD,CAAC,EAAE,EAAE;MAC7CH,UAAU,CAAC5E,IAAI,CAAC6E,aAAa,CAACE,CAAC,CAAC,CAAC;IACnC;IACA;IACA,OAAOF,aAAa;EACtB;EACA,IAAIS,WAAW,GAAG,EAAE;EACpB,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,SAAS,CAACzC,MAAM,IAAI0C,IAAI,EAAE,GAAGF,SAAS,EAAE,EAAEiB,CAAC,EAAE;IAC/D,IAAIW,eAAe,GAAGjJ,kBAAkB,CAACsH,SAAS,CAACgB,CAAC,CAAC,CAAC;IACtD,IAAI,CAACrI,iBAAiB,CAACqH,SAAS,CAACgB,CAAC,CAAC,CAAC,EAAE;MACpC;MACA;IACF;IACAL,aAAa,CAACX,SAAS,CAACgB,CAAC,CAAC,EAAEO,WAAW,CAACA,WAAW,CAAChE,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,EAAEiE,iBAAiB,CAAC;IACzF,IAAII,mBAAmB,GAAG5B,SAAS,CAACgB,CAAC,GAAG,CAAC,CAAC,GAAGtI,kBAAkB,CAACsH,SAAS,CAACgB,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;IACxF,IAAIW,eAAe,KAAKC,mBAAmB,EAAE;MAC3C,IAAIJ,iBAAiB,CAACjE,MAAM,EAAE;QAC5BmE,kBAAkB,GAAGD,SAAS;QAC9B;QACAD,iBAAiB,CAACK,IAAI,CAAC,UAAU1H,CAAC,EAAE2H,CAAC,EAAE;UACrC,OAAO3H,CAAC,CAACgB,KAAK,GAAG2G,CAAC,CAAC3G,KAAK;QAC1B,CAAC,CAAC;QACF,IAAI4G,0BAA0B,GAAG,EAAE;QACnC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGR,iBAAiB,CAACjE,MAAM,EAAE,EAAEyE,GAAG,EAAE;UACvD,IAAIC,SAAS,GAAGT,iBAAiB,CAACQ,GAAG,CAAC,CAAC7G,KAAK;UAC5C,IAAI6G,GAAG,KAAK,CAAC,IAAIR,iBAAiB,CAACQ,GAAG,GAAG,CAAC,CAAC,CAAC7G,KAAK,KAAK8G,SAAS,EAAE;YAC/DF,0BAA0B,CAAC9F,IAAI,CAACuF,iBAAiB,CAACQ,GAAG,CAAC,CAAC;YACvD,IAAIC,SAAS,IAAInG,MAAM,CAAC,CAAC,CAAC,IAAImG,SAAS,IAAInG,MAAM,CAAC,CAAC,CAAC,EAAE;cACpD2F,SAAS,EAAE;YACb;UACF;QACF;QACA,IAAIS,aAAa,GAAG,CAACpG,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,IAAIkD,cAAc;QAC5D;QACA,IAAIyC,SAAS,GAAGS,aAAa,GAAG,GAAG,IAAIR,kBAAkB,GAAGQ,aAAa,GAAG,GAAG,EAAE;UAC/E;QACF;QACA;QACAX,WAAW,CAACtF,IAAI,CAAC8F,0BAA0B,CAAC;QAC5C,IAAIN,SAAS,GAAGS,aAAa,IAAIpC,cAAc,KAAKE,SAAS,CAACgB,CAAC,CAAC,EAAE;UAChE;QACF;MACF;MACA;MACAQ,iBAAiB,GAAG,EAAE;IACxB;EACF;EACA,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIpC,IAAI,IAAIF,SAAS,EAAE;MACrBjG,IAAI,CAAC,oBAAoB,CAAC;IAC5B;EACF;EACA,IAAIwI,mBAAmB,GAAGvI,MAAM,CAACE,GAAG,CAACsH,WAAW,EAAE,UAAUV,UAAU,EAAE;IACtE,OAAO9G,MAAM,CAAC8G,UAAU,EAAE,UAAU7F,IAAI,EAAE;MACxC,OAAOA,IAAI,CAACG,KAAK,IAAIW,MAAM,CAAC,CAAC,CAAC,IAAId,IAAI,CAACG,KAAK,IAAIW,MAAM,CAAC,CAAC,CAAC,IAAI,CAACd,IAAI,CAAC0F,MAAM;IAC3E,CAAC,CAAC;EACJ,CAAC,CAAC,EAAE,UAAUG,UAAU,EAAE;IACxB,OAAOA,UAAU,CAACtD,MAAM,GAAG,CAAC;EAC9B,CAAC,CAAC;EACF,IAAIvB,KAAK,GAAG,EAAE;EACd,IAAIuG,QAAQ,GAAGD,mBAAmB,CAAC/E,MAAM,GAAG,CAAC;EAC7C,KAAK,IAAIyD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,mBAAmB,CAAC/E,MAAM,EAAE,EAAEyD,CAAC,EAAE;IACnD,IAAIH,UAAU,GAAGyB,mBAAmB,CAACtB,CAAC,CAAC;IACvC,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,UAAU,CAACtD,MAAM,EAAE,EAAEiF,CAAC,EAAE;MAC1CxG,KAAK,CAACC,IAAI,CAAC;QACTd,KAAK,EAAE0F,UAAU,CAAC2B,CAAC,CAAC,CAACrH,KAAK;QAC1Be,KAAK,EAAEqG,QAAQ,GAAGvB;MACpB,CAAC,CAAC;IACJ;EACF;EACAhF,KAAK,CAAC6F,IAAI,CAAC,UAAU1H,CAAC,EAAE2H,CAAC,EAAE;IACzB,OAAO3H,CAAC,CAACgB,KAAK,GAAG2G,CAAC,CAAC3G,KAAK;EAC1B,CAAC,CAAC;EACF;EACA,IAAIsH,MAAM,GAAG,EAAE;EACf,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhF,KAAK,CAACuB,MAAM,EAAE,EAAEyD,CAAC,EAAE;IACrC,IAAIA,CAAC,KAAK,CAAC,IAAIhF,KAAK,CAACgF,CAAC,CAAC,CAAC7F,KAAK,KAAKa,KAAK,CAACgF,CAAC,GAAG,CAAC,CAAC,CAAC7F,KAAK,EAAE;MACpDsH,MAAM,CAACxG,IAAI,CAACD,KAAK,CAACgF,CAAC,CAAC,CAAC;IACvB;EACF;EACA,OAAOyB,MAAM;AACf;AACA5I,KAAK,CAAC6I,aAAa,CAAClI,SAAS,CAAC;AAC9B,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}