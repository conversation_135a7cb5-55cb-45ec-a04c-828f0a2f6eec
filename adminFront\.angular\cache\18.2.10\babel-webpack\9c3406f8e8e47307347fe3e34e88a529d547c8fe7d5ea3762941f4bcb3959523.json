{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport axisDefault from '../axisDefault.js';\nimport Model from '../../model/Model.js';\nimport { AxisModelCommonMixin } from '../axisModelCommonMixin.js';\nimport ComponentModel from '../../model/Component.js';\nvar valueAxisDefault = axisDefault.value;\nfunction defaultsShow(opt, show) {\n  return zrUtil.defaults({\n    show: show\n  }, opt);\n}\nvar RadarModel = /** @class */function (_super) {\n  __extends(RadarModel, _super);\n  function RadarModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = RadarModel.type;\n    return _this;\n  }\n  RadarModel.prototype.optionUpdated = function () {\n    var boundaryGap = this.get('boundaryGap');\n    var splitNumber = this.get('splitNumber');\n    var scale = this.get('scale');\n    var axisLine = this.get('axisLine');\n    var axisTick = this.get('axisTick');\n    // let axisType = this.get('axisType');\n    var axisLabel = this.get('axisLabel');\n    var nameTextStyle = this.get('axisName');\n    var showName = this.get(['axisName', 'show']);\n    var nameFormatter = this.get(['axisName', 'formatter']);\n    var nameGap = this.get('axisNameGap');\n    var triggerEvent = this.get('triggerEvent');\n    var indicatorModels = zrUtil.map(this.get('indicator') || [], function (indicatorOpt) {\n      // PENDING\n      if (indicatorOpt.max != null && indicatorOpt.max > 0 && !indicatorOpt.min) {\n        indicatorOpt.min = 0;\n      } else if (indicatorOpt.min != null && indicatorOpt.min < 0 && !indicatorOpt.max) {\n        indicatorOpt.max = 0;\n      }\n      var iNameTextStyle = nameTextStyle;\n      if (indicatorOpt.color != null) {\n        iNameTextStyle = zrUtil.defaults({\n          color: indicatorOpt.color\n        }, nameTextStyle);\n      }\n      // Use same configuration\n      var innerIndicatorOpt = zrUtil.merge(zrUtil.clone(indicatorOpt), {\n        boundaryGap: boundaryGap,\n        splitNumber: splitNumber,\n        scale: scale,\n        axisLine: axisLine,\n        axisTick: axisTick,\n        // axisType: axisType,\n        axisLabel: axisLabel,\n        // Compatible with 2 and use text\n        name: indicatorOpt.text,\n        showName: showName,\n        nameLocation: 'end',\n        nameGap: nameGap,\n        // min: 0,\n        nameTextStyle: iNameTextStyle,\n        triggerEvent: triggerEvent\n      }, false);\n      if (zrUtil.isString(nameFormatter)) {\n        var indName = innerIndicatorOpt.name;\n        innerIndicatorOpt.name = nameFormatter.replace('{value}', indName != null ? indName : '');\n      } else if (zrUtil.isFunction(nameFormatter)) {\n        innerIndicatorOpt.name = nameFormatter(innerIndicatorOpt.name, innerIndicatorOpt);\n      }\n      var model = new Model(innerIndicatorOpt, null, this.ecModel);\n      zrUtil.mixin(model, AxisModelCommonMixin.prototype);\n      // For triggerEvent.\n      model.mainType = 'radar';\n      model.componentIndex = this.componentIndex;\n      return model;\n    }, this);\n    this._indicatorModels = indicatorModels;\n  };\n  RadarModel.prototype.getIndicatorModels = function () {\n    return this._indicatorModels;\n  };\n  RadarModel.type = 'radar';\n  RadarModel.defaultOption = {\n    // zlevel: 0,\n    z: 0,\n    center: ['50%', '50%'],\n    radius: '75%',\n    startAngle: 90,\n    axisName: {\n      show: true\n      // formatter: null\n      // textStyle: {}\n    },\n    boundaryGap: [0, 0],\n    splitNumber: 5,\n    axisNameGap: 15,\n    scale: false,\n    // Polygon or circle\n    shape: 'polygon',\n    axisLine: zrUtil.merge({\n      lineStyle: {\n        color: '#bbb'\n      }\n    }, valueAxisDefault.axisLine),\n    axisLabel: defaultsShow(valueAxisDefault.axisLabel, false),\n    axisTick: defaultsShow(valueAxisDefault.axisTick, false),\n    // axisType: 'value',\n    splitLine: defaultsShow(valueAxisDefault.splitLine, true),\n    splitArea: defaultsShow(valueAxisDefault.splitArea, true),\n    // {text, min, max}\n    indicator: []\n  };\n  return RadarModel;\n}(ComponentModel);\nexport default RadarModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "axisDefault", "Model", "AxisModelCommonMixin", "ComponentModel", "valueAxisDefault", "value", "defaultsShow", "opt", "show", "defaults", "RadarModel", "_super", "_this", "apply", "arguments", "type", "prototype", "optionUpdated", "boundaryGap", "get", "splitNumber", "scale", "axisLine", "axisTick", "axisLabel", "nameTextStyle", "showName", "nameF<PERSON>att<PERSON>", "nameGap", "triggerEvent", "indicatorModels", "map", "indicatorOpt", "max", "min", "iNameTextStyle", "color", "innerIndicatorOpt", "merge", "clone", "name", "text", "nameLocation", "isString", "indName", "replace", "isFunction", "model", "ecModel", "mixin", "mainType", "componentIndex", "_indicatorModels", "getIndicatorModels", "defaultOption", "z", "center", "radius", "startAngle", "axisName", "axisNameGap", "shape", "lineStyle", "splitLine", "splitArea", "indicator"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/coord/radar/RadarModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport axisDefault from '../axisDefault.js';\nimport Model from '../../model/Model.js';\nimport { AxisModelCommonMixin } from '../axisModelCommonMixin.js';\nimport ComponentModel from '../../model/Component.js';\nvar valueAxisDefault = axisDefault.value;\nfunction defaultsShow(opt, show) {\n  return zrUtil.defaults({\n    show: show\n  }, opt);\n}\nvar RadarModel = /** @class */function (_super) {\n  __extends(RadarModel, _super);\n  function RadarModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = RadarModel.type;\n    return _this;\n  }\n  RadarModel.prototype.optionUpdated = function () {\n    var boundaryGap = this.get('boundaryGap');\n    var splitNumber = this.get('splitNumber');\n    var scale = this.get('scale');\n    var axisLine = this.get('axisLine');\n    var axisTick = this.get('axisTick');\n    // let axisType = this.get('axisType');\n    var axisLabel = this.get('axisLabel');\n    var nameTextStyle = this.get('axisName');\n    var showName = this.get(['axisName', 'show']);\n    var nameFormatter = this.get(['axisName', 'formatter']);\n    var nameGap = this.get('axisNameGap');\n    var triggerEvent = this.get('triggerEvent');\n    var indicatorModels = zrUtil.map(this.get('indicator') || [], function (indicatorOpt) {\n      // PENDING\n      if (indicatorOpt.max != null && indicatorOpt.max > 0 && !indicatorOpt.min) {\n        indicatorOpt.min = 0;\n      } else if (indicatorOpt.min != null && indicatorOpt.min < 0 && !indicatorOpt.max) {\n        indicatorOpt.max = 0;\n      }\n      var iNameTextStyle = nameTextStyle;\n      if (indicatorOpt.color != null) {\n        iNameTextStyle = zrUtil.defaults({\n          color: indicatorOpt.color\n        }, nameTextStyle);\n      }\n      // Use same configuration\n      var innerIndicatorOpt = zrUtil.merge(zrUtil.clone(indicatorOpt), {\n        boundaryGap: boundaryGap,\n        splitNumber: splitNumber,\n        scale: scale,\n        axisLine: axisLine,\n        axisTick: axisTick,\n        // axisType: axisType,\n        axisLabel: axisLabel,\n        // Compatible with 2 and use text\n        name: indicatorOpt.text,\n        showName: showName,\n        nameLocation: 'end',\n        nameGap: nameGap,\n        // min: 0,\n        nameTextStyle: iNameTextStyle,\n        triggerEvent: triggerEvent\n      }, false);\n      if (zrUtil.isString(nameFormatter)) {\n        var indName = innerIndicatorOpt.name;\n        innerIndicatorOpt.name = nameFormatter.replace('{value}', indName != null ? indName : '');\n      } else if (zrUtil.isFunction(nameFormatter)) {\n        innerIndicatorOpt.name = nameFormatter(innerIndicatorOpt.name, innerIndicatorOpt);\n      }\n      var model = new Model(innerIndicatorOpt, null, this.ecModel);\n      zrUtil.mixin(model, AxisModelCommonMixin.prototype);\n      // For triggerEvent.\n      model.mainType = 'radar';\n      model.componentIndex = this.componentIndex;\n      return model;\n    }, this);\n    this._indicatorModels = indicatorModels;\n  };\n  RadarModel.prototype.getIndicatorModels = function () {\n    return this._indicatorModels;\n  };\n  RadarModel.type = 'radar';\n  RadarModel.defaultOption = {\n    // zlevel: 0,\n    z: 0,\n    center: ['50%', '50%'],\n    radius: '75%',\n    startAngle: 90,\n    axisName: {\n      show: true\n      // formatter: null\n      // textStyle: {}\n    },\n    boundaryGap: [0, 0],\n    splitNumber: 5,\n    axisNameGap: 15,\n    scale: false,\n    // Polygon or circle\n    shape: 'polygon',\n    axisLine: zrUtil.merge({\n      lineStyle: {\n        color: '#bbb'\n      }\n    }, valueAxisDefault.axisLine),\n    axisLabel: defaultsShow(valueAxisDefault.axisLabel, false),\n    axisTick: defaultsShow(valueAxisDefault.axisTick, false),\n    // axisType: 'value',\n    splitLine: defaultsShow(valueAxisDefault.splitLine, true),\n    splitArea: defaultsShow(valueAxisDefault.splitArea, true),\n    // {text, min, max}\n    indicator: []\n  };\n  return RadarModel;\n}(ComponentModel);\nexport default RadarModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,oBAAoB,QAAQ,4BAA4B;AACjE,OAAOC,cAAc,MAAM,0BAA0B;AACrD,IAAIC,gBAAgB,GAAGJ,WAAW,CAACK,KAAK;AACxC,SAASC,YAAYA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC/B,OAAOT,MAAM,CAACU,QAAQ,CAAC;IACrBD,IAAI,EAAEA;EACR,CAAC,EAAED,GAAG,CAAC;AACT;AACA,IAAIG,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9Cb,SAAS,CAACY,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,UAAU,CAACK,IAAI;IAC5B,OAAOH,KAAK;EACd;EACAF,UAAU,CAACM,SAAS,CAACC,aAAa,GAAG,YAAY;IAC/C,IAAIC,WAAW,GAAG,IAAI,CAACC,GAAG,CAAC,aAAa,CAAC;IACzC,IAAIC,WAAW,GAAG,IAAI,CAACD,GAAG,CAAC,aAAa,CAAC;IACzC,IAAIE,KAAK,GAAG,IAAI,CAACF,GAAG,CAAC,OAAO,CAAC;IAC7B,IAAIG,QAAQ,GAAG,IAAI,CAACH,GAAG,CAAC,UAAU,CAAC;IACnC,IAAII,QAAQ,GAAG,IAAI,CAACJ,GAAG,CAAC,UAAU,CAAC;IACnC;IACA,IAAIK,SAAS,GAAG,IAAI,CAACL,GAAG,CAAC,WAAW,CAAC;IACrC,IAAIM,aAAa,GAAG,IAAI,CAACN,GAAG,CAAC,UAAU,CAAC;IACxC,IAAIO,QAAQ,GAAG,IAAI,CAACP,GAAG,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAC7C,IAAIQ,aAAa,GAAG,IAAI,CAACR,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;IACvD,IAAIS,OAAO,GAAG,IAAI,CAACT,GAAG,CAAC,aAAa,CAAC;IACrC,IAAIU,YAAY,GAAG,IAAI,CAACV,GAAG,CAAC,cAAc,CAAC;IAC3C,IAAIW,eAAe,GAAG/B,MAAM,CAACgC,GAAG,CAAC,IAAI,CAACZ,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,UAAUa,YAAY,EAAE;MACpF;MACA,IAAIA,YAAY,CAACC,GAAG,IAAI,IAAI,IAAID,YAAY,CAACC,GAAG,GAAG,CAAC,IAAI,CAACD,YAAY,CAACE,GAAG,EAAE;QACzEF,YAAY,CAACE,GAAG,GAAG,CAAC;MACtB,CAAC,MAAM,IAAIF,YAAY,CAACE,GAAG,IAAI,IAAI,IAAIF,YAAY,CAACE,GAAG,GAAG,CAAC,IAAI,CAACF,YAAY,CAACC,GAAG,EAAE;QAChFD,YAAY,CAACC,GAAG,GAAG,CAAC;MACtB;MACA,IAAIE,cAAc,GAAGV,aAAa;MAClC,IAAIO,YAAY,CAACI,KAAK,IAAI,IAAI,EAAE;QAC9BD,cAAc,GAAGpC,MAAM,CAACU,QAAQ,CAAC;UAC/B2B,KAAK,EAAEJ,YAAY,CAACI;QACtB,CAAC,EAAEX,aAAa,CAAC;MACnB;MACA;MACA,IAAIY,iBAAiB,GAAGtC,MAAM,CAACuC,KAAK,CAACvC,MAAM,CAACwC,KAAK,CAACP,YAAY,CAAC,EAAE;QAC/Dd,WAAW,EAAEA,WAAW;QACxBE,WAAW,EAAEA,WAAW;QACxBC,KAAK,EAAEA,KAAK;QACZC,QAAQ,EAAEA,QAAQ;QAClBC,QAAQ,EAAEA,QAAQ;QAClB;QACAC,SAAS,EAAEA,SAAS;QACpB;QACAgB,IAAI,EAAER,YAAY,CAACS,IAAI;QACvBf,QAAQ,EAAEA,QAAQ;QAClBgB,YAAY,EAAE,KAAK;QACnBd,OAAO,EAAEA,OAAO;QAChB;QACAH,aAAa,EAAEU,cAAc;QAC7BN,YAAY,EAAEA;MAChB,CAAC,EAAE,KAAK,CAAC;MACT,IAAI9B,MAAM,CAAC4C,QAAQ,CAAChB,aAAa,CAAC,EAAE;QAClC,IAAIiB,OAAO,GAAGP,iBAAiB,CAACG,IAAI;QACpCH,iBAAiB,CAACG,IAAI,GAAGb,aAAa,CAACkB,OAAO,CAAC,SAAS,EAAED,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAG,EAAE,CAAC;MAC3F,CAAC,MAAM,IAAI7C,MAAM,CAAC+C,UAAU,CAACnB,aAAa,CAAC,EAAE;QAC3CU,iBAAiB,CAACG,IAAI,GAAGb,aAAa,CAACU,iBAAiB,CAACG,IAAI,EAAEH,iBAAiB,CAAC;MACnF;MACA,IAAIU,KAAK,GAAG,IAAI9C,KAAK,CAACoC,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAACW,OAAO,CAAC;MAC5DjD,MAAM,CAACkD,KAAK,CAACF,KAAK,EAAE7C,oBAAoB,CAACc,SAAS,CAAC;MACnD;MACA+B,KAAK,CAACG,QAAQ,GAAG,OAAO;MACxBH,KAAK,CAACI,cAAc,GAAG,IAAI,CAACA,cAAc;MAC1C,OAAOJ,KAAK;IACd,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAACK,gBAAgB,GAAGtB,eAAe;EACzC,CAAC;EACDpB,UAAU,CAACM,SAAS,CAACqC,kBAAkB,GAAG,YAAY;IACpD,OAAO,IAAI,CAACD,gBAAgB;EAC9B,CAAC;EACD1C,UAAU,CAACK,IAAI,GAAG,OAAO;EACzBL,UAAU,CAAC4C,aAAa,GAAG;IACzB;IACAC,CAAC,EAAE,CAAC;IACJC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;IACtBC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;MACRnD,IAAI,EAAE;MACN;MACA;IACF,CAAC;IACDU,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBE,WAAW,EAAE,CAAC;IACdwC,WAAW,EAAE,EAAE;IACfvC,KAAK,EAAE,KAAK;IACZ;IACAwC,KAAK,EAAE,SAAS;IAChBvC,QAAQ,EAAEvB,MAAM,CAACuC,KAAK,CAAC;MACrBwB,SAAS,EAAE;QACT1B,KAAK,EAAE;MACT;IACF,CAAC,EAAEhC,gBAAgB,CAACkB,QAAQ,CAAC;IAC7BE,SAAS,EAAElB,YAAY,CAACF,gBAAgB,CAACoB,SAAS,EAAE,KAAK,CAAC;IAC1DD,QAAQ,EAAEjB,YAAY,CAACF,gBAAgB,CAACmB,QAAQ,EAAE,KAAK,CAAC;IACxD;IACAwC,SAAS,EAAEzD,YAAY,CAACF,gBAAgB,CAAC2D,SAAS,EAAE,IAAI,CAAC;IACzDC,SAAS,EAAE1D,YAAY,CAACF,gBAAgB,CAAC4D,SAAS,EAAE,IAAI,CAAC;IACzD;IACAC,SAAS,EAAE;EACb,CAAC;EACD,OAAOvD,UAAU;AACnB,CAAC,CAACP,cAAc,CAAC;AACjB,eAAeO,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}