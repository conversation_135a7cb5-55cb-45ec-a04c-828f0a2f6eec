import { Injectable } from '@angular/core';
import { NbDialogService } from '@nebular/theme';
import { Observable } from 'rxjs';
import { SpaceTemplateSelectorComponent, SpaceTemplateConfig } from './space-template-selector.component';

export interface SpaceTemplateSelectorConfig {
  buildCaseId?: string;
  buttonText?: string;
  buttonIcon?: string;
  buttonClass?: string;
  dialogTitle?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SpaceTemplateSelectorService {

  constructor(private dialogService: NbDialogService) { }

  /**
   * 開啟空間模板選擇器對話框
   * @param config 配置選項
   * @returns Observable 當用戶確認套用模板時回傳配置，取消時回傳 null
   */
  openSelector(config?: SpaceTemplateSelectorConfig): Observable<SpaceTemplateConfig | null> {
    const dialogRef = this.dialogService.open(SpaceTemplateSelectorComponent, {
      closeOnBackdropClick: true,
      closeOnEsc: true,
      hasScroll: true,
      autoFocus: false
    });

    // 監聽模板套用事件
    const templateApplied$ = dialogRef.componentRef.instance.templateApplied.asObservable();

    // 當有模板套用時，關閉對話框並回傳結果
    templateApplied$.subscribe((config: SpaceTemplateConfig) => {
      dialogRef.close(config);
    });

    return dialogRef.onClose;
  }
}
