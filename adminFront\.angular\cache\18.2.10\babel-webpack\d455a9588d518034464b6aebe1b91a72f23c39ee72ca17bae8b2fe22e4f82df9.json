{"ast": null, "code": "var Entry = function () {\n  function Entry(val) {\n    this.value = val;\n  }\n  return Entry;\n}();\nexport { Entry };\nvar LinkedList = function () {\n  function LinkedList() {\n    this._len = 0;\n  }\n  LinkedList.prototype.insert = function (val) {\n    var entry = new Entry(val);\n    this.insertEntry(entry);\n    return entry;\n  };\n  LinkedList.prototype.insertEntry = function (entry) {\n    if (!this.head) {\n      this.head = this.tail = entry;\n    } else {\n      this.tail.next = entry;\n      entry.prev = this.tail;\n      entry.next = null;\n      this.tail = entry;\n    }\n    this._len++;\n  };\n  LinkedList.prototype.remove = function (entry) {\n    var prev = entry.prev;\n    var next = entry.next;\n    if (prev) {\n      prev.next = next;\n    } else {\n      this.head = next;\n    }\n    if (next) {\n      next.prev = prev;\n    } else {\n      this.tail = prev;\n    }\n    entry.next = entry.prev = null;\n    this._len--;\n  };\n  LinkedList.prototype.len = function () {\n    return this._len;\n  };\n  LinkedList.prototype.clear = function () {\n    this.head = this.tail = null;\n    this._len = 0;\n  };\n  return LinkedList;\n}();\nexport { LinkedList };\nvar LRU = function () {\n  function LRU(maxSize) {\n    this._list = new LinkedList();\n    this._maxSize = 10;\n    this._map = {};\n    this._maxSize = maxSize;\n  }\n  LRU.prototype.put = function (key, value) {\n    var list = this._list;\n    var map = this._map;\n    var removed = null;\n    if (map[key] == null) {\n      var len = list.len();\n      var entry = this._lastRemovedEntry;\n      if (len >= this._maxSize && len > 0) {\n        var leastUsedEntry = list.head;\n        list.remove(leastUsedEntry);\n        delete map[leastUsedEntry.key];\n        removed = leastUsedEntry.value;\n        this._lastRemovedEntry = leastUsedEntry;\n      }\n      if (entry) {\n        entry.value = value;\n      } else {\n        entry = new Entry(value);\n      }\n      entry.key = key;\n      list.insertEntry(entry);\n      map[key] = entry;\n    }\n    return removed;\n  };\n  LRU.prototype.get = function (key) {\n    var entry = this._map[key];\n    var list = this._list;\n    if (entry != null) {\n      if (entry !== list.tail) {\n        list.remove(entry);\n        list.insertEntry(entry);\n      }\n      return entry.value;\n    }\n  };\n  LRU.prototype.clear = function () {\n    this._list.clear();\n    this._map = {};\n  };\n  LRU.prototype.len = function () {\n    return this._list.len();\n  };\n  return LRU;\n}();\nexport default LRU;", "map": {"version": 3, "names": ["Entry", "val", "value", "LinkedList", "_len", "prototype", "insert", "entry", "insertEntry", "head", "tail", "next", "prev", "remove", "len", "clear", "LRU", "maxSize", "_list", "_maxSize", "_map", "put", "key", "list", "map", "removed", "_lastRemovedEntry", "leastUsedEntry", "get"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/zrender/lib/core/LRU.js"], "sourcesContent": ["var Entry = (function () {\n    function Entry(val) {\n        this.value = val;\n    }\n    return Entry;\n}());\nexport { Entry };\nvar LinkedList = (function () {\n    function LinkedList() {\n        this._len = 0;\n    }\n    LinkedList.prototype.insert = function (val) {\n        var entry = new Entry(val);\n        this.insertEntry(entry);\n        return entry;\n    };\n    LinkedList.prototype.insertEntry = function (entry) {\n        if (!this.head) {\n            this.head = this.tail = entry;\n        }\n        else {\n            this.tail.next = entry;\n            entry.prev = this.tail;\n            entry.next = null;\n            this.tail = entry;\n        }\n        this._len++;\n    };\n    LinkedList.prototype.remove = function (entry) {\n        var prev = entry.prev;\n        var next = entry.next;\n        if (prev) {\n            prev.next = next;\n        }\n        else {\n            this.head = next;\n        }\n        if (next) {\n            next.prev = prev;\n        }\n        else {\n            this.tail = prev;\n        }\n        entry.next = entry.prev = null;\n        this._len--;\n    };\n    LinkedList.prototype.len = function () {\n        return this._len;\n    };\n    LinkedList.prototype.clear = function () {\n        this.head = this.tail = null;\n        this._len = 0;\n    };\n    return LinkedList;\n}());\nexport { LinkedList };\nvar LRU = (function () {\n    function LRU(maxSize) {\n        this._list = new LinkedList();\n        this._maxSize = 10;\n        this._map = {};\n        this._maxSize = maxSize;\n    }\n    LRU.prototype.put = function (key, value) {\n        var list = this._list;\n        var map = this._map;\n        var removed = null;\n        if (map[key] == null) {\n            var len = list.len();\n            var entry = this._lastRemovedEntry;\n            if (len >= this._maxSize && len > 0) {\n                var leastUsedEntry = list.head;\n                list.remove(leastUsedEntry);\n                delete map[leastUsedEntry.key];\n                removed = leastUsedEntry.value;\n                this._lastRemovedEntry = leastUsedEntry;\n            }\n            if (entry) {\n                entry.value = value;\n            }\n            else {\n                entry = new Entry(value);\n            }\n            entry.key = key;\n            list.insertEntry(entry);\n            map[key] = entry;\n        }\n        return removed;\n    };\n    LRU.prototype.get = function (key) {\n        var entry = this._map[key];\n        var list = this._list;\n        if (entry != null) {\n            if (entry !== list.tail) {\n                list.remove(entry);\n                list.insertEntry(entry);\n            }\n            return entry.value;\n        }\n    };\n    LRU.prototype.clear = function () {\n        this._list.clear();\n        this._map = {};\n    };\n    LRU.prototype.len = function () {\n        return this._list.len();\n    };\n    return LRU;\n}());\nexport default LRU;\n"], "mappings": "AAAA,IAAIA,KAAK,GAAI,YAAY;EACrB,SAASA,KAAKA,CAACC,GAAG,EAAE;IAChB,IAAI,CAACC,KAAK,GAAGD,GAAG;EACpB;EACA,OAAOD,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,SAASA,KAAK;AACd,IAAIG,UAAU,GAAI,YAAY;EAC1B,SAASA,UAAUA,CAAA,EAAG;IAClB,IAAI,CAACC,IAAI,GAAG,CAAC;EACjB;EACAD,UAAU,CAACE,SAAS,CAACC,MAAM,GAAG,UAAUL,GAAG,EAAE;IACzC,IAAIM,KAAK,GAAG,IAAIP,KAAK,CAACC,GAAG,CAAC;IAC1B,IAAI,CAACO,WAAW,CAACD,KAAK,CAAC;IACvB,OAAOA,KAAK;EAChB,CAAC;EACDJ,UAAU,CAACE,SAAS,CAACG,WAAW,GAAG,UAAUD,KAAK,EAAE;IAChD,IAAI,CAAC,IAAI,CAACE,IAAI,EAAE;MACZ,IAAI,CAACA,IAAI,GAAG,IAAI,CAACC,IAAI,GAAGH,KAAK;IACjC,CAAC,MACI;MACD,IAAI,CAACG,IAAI,CAACC,IAAI,GAAGJ,KAAK;MACtBA,KAAK,CAACK,IAAI,GAAG,IAAI,CAACF,IAAI;MACtBH,KAAK,CAACI,IAAI,GAAG,IAAI;MACjB,IAAI,CAACD,IAAI,GAAGH,KAAK;IACrB;IACA,IAAI,CAACH,IAAI,EAAE;EACf,CAAC;EACDD,UAAU,CAACE,SAAS,CAACQ,MAAM,GAAG,UAAUN,KAAK,EAAE;IAC3C,IAAIK,IAAI,GAAGL,KAAK,CAACK,IAAI;IACrB,IAAID,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACrB,IAAIC,IAAI,EAAE;MACNA,IAAI,CAACD,IAAI,GAAGA,IAAI;IACpB,CAAC,MACI;MACD,IAAI,CAACF,IAAI,GAAGE,IAAI;IACpB;IACA,IAAIA,IAAI,EAAE;MACNA,IAAI,CAACC,IAAI,GAAGA,IAAI;IACpB,CAAC,MACI;MACD,IAAI,CAACF,IAAI,GAAGE,IAAI;IACpB;IACAL,KAAK,CAACI,IAAI,GAAGJ,KAAK,CAACK,IAAI,GAAG,IAAI;IAC9B,IAAI,CAACR,IAAI,EAAE;EACf,CAAC;EACDD,UAAU,CAACE,SAAS,CAACS,GAAG,GAAG,YAAY;IACnC,OAAO,IAAI,CAACV,IAAI;EACpB,CAAC;EACDD,UAAU,CAACE,SAAS,CAACU,KAAK,GAAG,YAAY;IACrC,IAAI,CAACN,IAAI,GAAG,IAAI,CAACC,IAAI,GAAG,IAAI;IAC5B,IAAI,CAACN,IAAI,GAAG,CAAC;EACjB,CAAC;EACD,OAAOD,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU;AACnB,IAAIa,GAAG,GAAI,YAAY;EACnB,SAASA,GAAGA,CAACC,OAAO,EAAE;IAClB,IAAI,CAACC,KAAK,GAAG,IAAIf,UAAU,CAAC,CAAC;IAC7B,IAAI,CAACgB,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACD,QAAQ,GAAGF,OAAO;EAC3B;EACAD,GAAG,CAACX,SAAS,CAACgB,GAAG,GAAG,UAAUC,GAAG,EAAEpB,KAAK,EAAE;IACtC,IAAIqB,IAAI,GAAG,IAAI,CAACL,KAAK;IACrB,IAAIM,GAAG,GAAG,IAAI,CAACJ,IAAI;IACnB,IAAIK,OAAO,GAAG,IAAI;IAClB,IAAID,GAAG,CAACF,GAAG,CAAC,IAAI,IAAI,EAAE;MAClB,IAAIR,GAAG,GAAGS,IAAI,CAACT,GAAG,CAAC,CAAC;MACpB,IAAIP,KAAK,GAAG,IAAI,CAACmB,iBAAiB;MAClC,IAAIZ,GAAG,IAAI,IAAI,CAACK,QAAQ,IAAIL,GAAG,GAAG,CAAC,EAAE;QACjC,IAAIa,cAAc,GAAGJ,IAAI,CAACd,IAAI;QAC9Bc,IAAI,CAACV,MAAM,CAACc,cAAc,CAAC;QAC3B,OAAOH,GAAG,CAACG,cAAc,CAACL,GAAG,CAAC;QAC9BG,OAAO,GAAGE,cAAc,CAACzB,KAAK;QAC9B,IAAI,CAACwB,iBAAiB,GAAGC,cAAc;MAC3C;MACA,IAAIpB,KAAK,EAAE;QACPA,KAAK,CAACL,KAAK,GAAGA,KAAK;MACvB,CAAC,MACI;QACDK,KAAK,GAAG,IAAIP,KAAK,CAACE,KAAK,CAAC;MAC5B;MACAK,KAAK,CAACe,GAAG,GAAGA,GAAG;MACfC,IAAI,CAACf,WAAW,CAACD,KAAK,CAAC;MACvBiB,GAAG,CAACF,GAAG,CAAC,GAAGf,KAAK;IACpB;IACA,OAAOkB,OAAO;EAClB,CAAC;EACDT,GAAG,CAACX,SAAS,CAACuB,GAAG,GAAG,UAAUN,GAAG,EAAE;IAC/B,IAAIf,KAAK,GAAG,IAAI,CAACa,IAAI,CAACE,GAAG,CAAC;IAC1B,IAAIC,IAAI,GAAG,IAAI,CAACL,KAAK;IACrB,IAAIX,KAAK,IAAI,IAAI,EAAE;MACf,IAAIA,KAAK,KAAKgB,IAAI,CAACb,IAAI,EAAE;QACrBa,IAAI,CAACV,MAAM,CAACN,KAAK,CAAC;QAClBgB,IAAI,CAACf,WAAW,CAACD,KAAK,CAAC;MAC3B;MACA,OAAOA,KAAK,CAACL,KAAK;IACtB;EACJ,CAAC;EACDc,GAAG,CAACX,SAAS,CAACU,KAAK,GAAG,YAAY;IAC9B,IAAI,CAACG,KAAK,CAACH,KAAK,CAAC,CAAC;IAClB,IAAI,CAACK,IAAI,GAAG,CAAC,CAAC;EAClB,CAAC;EACDJ,GAAG,CAACX,SAAS,CAACS,GAAG,GAAG,YAAY;IAC5B,OAAO,IAAI,CAACI,KAAK,CAACJ,GAAG,CAAC,CAAC;EAC3B,CAAC;EACD,OAAOE,GAAG;AACd,CAAC,CAAC,CAAE;AACJ,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}