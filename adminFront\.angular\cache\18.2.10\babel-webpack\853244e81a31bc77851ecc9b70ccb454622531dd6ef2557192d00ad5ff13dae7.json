{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter, forwardRef, ViewChild } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet HouseholdBindingComponent = class HouseholdBindingComponent {\n  constructor(cdr, dialogService) {\n    this.cdr = cdr;\n    this.dialogService = dialogService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 建案ID（用於識別）\n    this.buildingData = {};\n    this.allowBatchSelect = true;\n    this.excludedHouseIds = []; // 改為：排除的戶別ID（已被其他元件選擇）\n    this.useHouseNameMode = false; // 新增：使用戶別名稱模式\n    this.selectionChange = new EventEmitter();\n    this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n    this.houseNameChange = new EventEmitter(); // 新增：useHouseNameMode 時回傳戶別名稱陣列\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseIds = []; // 改為：使用 houseId 作為選擇的key\n    this.selectedHouseNames = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列\n    this.buildings = [];\n    this.floors = []; // 新增：當前棧別的樓層列表\n    this.filteredHouseholds = []; // 保持為字串陣列用於UI顯示\n    this.selectedByBuilding = {}; // 改為：儲存 houseId\n    this.isLoading = false; // 新增：載入狀態  // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  /**\n   * [優化] 產生已選戶別的分組摘要文字。\n   * 用於簡潔地顯示選擇結果。\n   * @returns string - 例如: \"棟 A (10戶), 棟 B (5戶)\"\n   */\n  getGroupedSummary() {\n    if (this.selectedHouseIds.length === 0) {\n      return '';\n    }\n    return this.buildings.map(building => {\n      const count = this.selectedByBuilding[building]?.length || 0;\n      return count > 0 ? `${building} (${count}戶)` : null;\n    }).filter(Boolean) // 過濾掉沒有選擇的棟別\n    .join(', ');\n  }\n  /**\n   * [優化] 獲取所有已選戶別的詳細資訊列表。\n   * 用於在 Popover 中顯示。\n   * @returns HouseholdItem[]\n   */\n  getAllSelectedItems() {\n    return this.selectedHouseIds.map(id => this.getHouseholdByHouseId(id)).filter(item => !!item);\n  }\n  writeValue(value) {\n    if (!value || value.length === 0) {\n      this.selectedHouseIds = [];\n      this.selectedHouseNames = [];\n    } else {\n      const firstItem = value[0];\n      if (this.useHouseNameMode) {\n        // useHouseNameMode: 期望接收戶別名稱陣列\n        if (typeof firstItem === 'string') {\n          this.selectedHouseNames = [...new Set(value)]; // 去除重複的戶別名稱\n          // 將戶別名稱轉換為 houseId（用於內部邏輯），會自動處理重複名稱\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\n          console.log('useHouseNameMode: 使用傳入的戶別名稱陣列（已去重）');\n        } else {\n          console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);\n          this.selectedHouseNames = [];\n          this.selectedHouseIds = [];\n        }\n      } else {\n        // 一般模式: 期望接收 houseId 陣列\n        if (typeof firstItem === 'number') {\n          this.selectedHouseIds = value;\n          // 將 houseId 轉換為戶別名稱（用於顯示）\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n          console.log('一般模式: 使用傳入的 houseId 陣列');\n        } else if (typeof firstItem === 'string') {\n          // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告\n          console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');\n          console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');\n          return;\n        } else {\n          console.error('writeValue 收到未知格式的資料:', value);\n          this.selectedHouseIds = [];\n          this.selectedHouseNames = [];\n        }\n      }\n    }\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseIds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n    }\n    if (changes['useHouseNameMode']) {\n      if (this.useHouseNameMode) {\n        this.selectedFloor = '';\n      }\n    }\n  }\n  initializeData() {\n    // 使用傳入的 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateSelectedByBuilding();\n    } else {\n      // 沒有 buildingData，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData provided');\n    }\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseIds.forEach(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseId);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    const filteredItems = households.filter(h => {\n      // 樓層篩選：在 useHouseNameMode 時跳過樓層篩選，否則按原邏輯篩選\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(houseId) {\n    console.log('onHouseholdToggle called with houseId:', houseId);\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\n    if (!houseId) {\n      console.log(`無效的 houseId: ${houseId}`);\n      return;\n    }\n    // 防止選擇已排除的戶別\n    if (this.isHouseIdExcluded(houseId)) {\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    // 取得被點擊戶別的名稱\n    const clickedHousehold = this.getHouseholdByHouseId(houseId);\n    if (!clickedHousehold) {\n      console.log(`找不到 houseId ${houseId} 對應的戶別資訊`);\n      return;\n    }\n    let newSelection;\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 處理同名戶別的邏輯\n      const houseName = clickedHousehold.houseName;\n      const allMatchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n      // 檢查是否有任何同名戶別已被選中\n      const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n      if (hasAnySelected) {\n        // 如果有同名戶別被選中，移除所有同名戶別\n        newSelection = this.selectedHouseIds.filter(id => !allMatchingHouseIds.includes(id));\n        console.log(`useHouseNameMode: 移除所有同名戶別 \"${houseName}\":`, allMatchingHouseIds);\n      } else {\n        // 如果沒有同名戶別被選中，只添加第一個（通常是當前點擊的）\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, allMatchingHouseIds[0]];\n        console.log(`useHouseNameMode: 添加戶別 \"${houseName}\" 的第一個項目:`, allMatchingHouseIds[0]);\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      const isSelected = this.isHouseIdSelected(houseId);\n      if (isSelected) {\n        newSelection = this.selectedHouseIds.filter(id => id !== houseId);\n      } else {\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, houseId];\n      }\n    }\n    this.selectedHouseIds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(houseId) {\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    console.log('onSelectAllFiltered called');\n    console.log('selectedBuilding:', this.selectedBuilding);\n    console.log('selectedFloor:', this.selectedFloor);\n    console.log('searchTerm:', this.searchTerm);\n    if (!this.selectedBuilding) {\n      console.log('No building selected');\n      return;\n    }\n    // 使用 getUniqueHouseholdsForDisplay 方法來獲取要處理的戶別列表\n    const filteredHouseholdItems = this.getUniqueHouseholdsForDisplay();\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n    if (filteredHouseholdItems.length === 0) {\n      console.log('No filtered households found');\n      return;\n    }\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的過濾戶別ID\n    const unselectedFilteredIds = [];\n    for (const household of filteredHouseholdItems) {\n      if (household.houseId) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 檢查是否有任何同名戶別已被選擇\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseIdExcluded(id));\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedFilteredIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        } else {\n          // 一般模式: 原有邏輯\n          if (!this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\n            unselectedFilteredIds.push(household.houseId);\n          }\n        }\n      }\n    }\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\n    } else {\n      console.log('No households to add');\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的棟別戶別 ID\n    const unselectedBuildingIds = [];\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 只選擇唯一的戶別名稱\n      const processedHouseNames = new Set();\n      for (const household of buildingHouseholds) {\n        if (household.houseId && household.houseName && !processedHouseNames.has(household.houseName)) {\n          processedHouseNames.add(household.houseName);\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseholdExcluded(id));\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedBuildingIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        }\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      for (const household of buildingHouseholds) {\n        if (household.houseId && !this.selectedHouseIds.includes(household.houseId) && !this.isHouseholdExcluded(household.houseId)) {\n          unselectedBuildingIds.push(household.houseId);\n        }\n      }\n    }\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined);\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseIds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds); // 根據模式決定要回傳的資料格式\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 回傳戶別名稱陣列（去重複）\n      this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      const uniqueHouseNames = [...new Set(this.selectedHouseNames)]; // 確保去重複\n      console.log('useHouseNameMode - 回傳戶別名稱陣列（已去重）:', uniqueHouseNames);\n      this.onChange([...uniqueHouseNames]);\n      this.houseNameChange.emit([...uniqueHouseNames]);\n    } else {\n      // 一般模式: 回傳 houseId 陣列\n      console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);\n      this.onChange([...this.selectedHouseIds]);\n      // 回傳 houseId 陣列\n      const houseIds = this.selectedHouseIds.filter(id => id !== undefined);\n      console.log('House IDs to emit:', houseIds);\n      this.houseIdChange.emit(houseIds);\n    }\n    this.onTouched();\n    // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）\n    const selectedItems = this.selectedHouseIds.map(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    console.log('Selected items to emit:', selectedItems);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false\n    });\n  }\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  }\n  isHouseholdSelected(houseId) {\n    if (!houseId) return false;\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 檢查是否有任何同名戶別被選中\n      const household = this.getHouseholdByHouseId(houseId);\n      if (household) {\n        const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n        return allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n      }\n      return false;\n    } else {\n      // 一般模式: 直接檢查 houseId\n      return this.selectedHouseIds.includes(houseId);\n    }\n  }\n  isHouseholdExcluded(houseId) {\n    if (!houseId) return false;\n    return this.excludedHouseIds.includes(houseId);\n  }\n  isHouseholdDisabled(houseId) {\n    if (!houseId) return true;\n    return this.isHouseholdExcluded(houseId) || !this.canSelectMore() && !this.isHouseholdSelected(houseId);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled && h.houseId !== undefined);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const households = this.buildingData[building] || [];\n      const uniqueHouseNames = new Set(households.map(h => h.houseName));\n      return uniqueHouseNames.size;\n    } else {\n      // 一般模式: 返回總戶別數量\n      return this.buildingData[building]?.length || 0;\n    }\n  }\n  getSelectedCount() {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const uniqueHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      return uniqueHouseNames.length;\n    } else {\n      // 一般模式: 返回實際選中的戶別數量\n      return this.selectedHouseIds.length;\n    }\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\n  getBuildingSelectedHouseIds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棧別的樓層計數\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棧別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: householdCode,\n      floor: ''\n    };\n  }\n  // 新增：根據 houseId 取得戶別的完整資訊\n  getHouseholdInfoById(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: `ID:${houseId}`,\n      floor: ''\n    };\n  }\n  // 新增：檢查搜尋是否有結果\n  hasNoSearchResults() {\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return false;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length === 0;\n  }\n  // 新增：取得過濾後的戶別數量\n  getFilteredHouseholdsCount() {\n    return this.getUniqueHouseholdsForDisplay().length;\n  }\n  // 新增：產生戶別的唯一識別符\n  getHouseholdUniqueId(household) {\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n  }\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\n  getHouseholdByHouseId(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) return household;\n    }\n    return null;\n  } // 新增：輔助方法 - 根據 houseName 查找 houseId\n  getHouseIdByHouseName(houseName) {\n    const matchingHouseholds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        matchingHouseholds.push({\n          building,\n          household\n        });\n      });\n    }\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\n    if (matchingHouseholds.length === 0) {\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n      return null;\n    }\n    if (matchingHouseholds.length > 1) {\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\n    }\n    const firstMatch = matchingHouseholds[0];\n    return firstMatch.household.houseId || null;\n  }\n  // 新增：輔助方法 - 根據 houseName 查找所有對應的 houseId（處理重複名稱）\n  getAllHouseIdsByHouseName(houseName) {\n    const houseIds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        if (household.houseId) {\n          houseIds.push(household.houseId);\n        }\n      });\n    }\n    return houseIds;\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\n  isHouseIdSelected(houseId) {\n    return this.selectedHouseIds.includes(houseId);\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\n  isHouseIdExcluded(houseId) {\n    return this.excludedHouseIds.includes(houseId);\n  }\n  // 新增：從唯一識別符獲取戶別物件\n  getHouseholdFromUniqueId(uniqueId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n      if (household) return household;\n    }\n    return null;\n  } // 新增：將戶別名稱陣列轉換為 houseId 陣列（在 useHouseNameMode 下只選擇第一個匹配項）\n  convertHouseNamesToIds(houseNames) {\n    const houseIds = [];\n    const uniqueHouseNames = [...new Set(houseNames)]; // 去除重複的戶別名稱\n    for (const houseName of uniqueHouseNames) {\n      const matchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n      if (matchingHouseIds.length > 0) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 只選擇第一個匹配的 houseId，避免多個同名戶別都被選中\n          houseIds.push(matchingHouseIds[0]);\n          if (matchingHouseIds.length > 1) {\n            console.log(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，在 useHouseNameMode 下只選擇第一個:`, matchingHouseIds[0]);\n          }\n        } else {\n          // 一般模式: 將所有對應的 houseId 都加入（保持原有邏輯）\n          houseIds.push(...matchingHouseIds);\n          if (matchingHouseIds.length > 1) {\n            console.warn(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，已全部加入選擇:`, matchingHouseIds);\n          }\n        }\n      } else {\n        console.warn(`無法找到戶別名稱 \"${houseName}\" 對應的 houseId`);\n      }\n    }\n    // 去除重複的 houseId\n    return [...new Set(houseIds)];\n  }\n  // 新增：將 houseId 陣列轉換為戶別名稱陣列（去重複）\n  convertIdsToHouseNames(houseIds) {\n    const houseNames = [];\n    const uniqueHouseIds = [...new Set(houseIds)]; // 去除重複的 houseId\n    for (const houseId of uniqueHouseIds) {\n      const householdInfo = this.getHouseholdInfoById(houseId);\n      if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {\n        houseNames.push(householdInfo.houseName);\n      } else {\n        console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);\n      }\n    }\n    // 去除重複的戶別名稱\n    return [...new Set(houseNames)];\n  }\n  // 新增：取得去重複的戶別列表（用於 useHouseNameMode 顯示）\n  getUniqueHouseholdsForDisplay() {\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return [];\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    if (!this.useHouseNameMode) {\n      // 一般模式：返回所有戶別\n      return households.filter(h => {\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n        const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n        return floorMatch && searchMatch;\n      });\n    }\n    // useHouseNameMode：只返回唯一的戶別名稱\n    const uniqueHouseNames = new Set();\n    const uniqueHouseholds = [];\n    for (const household of households) {\n      // 搜尋篩選\n      const searchMatch = !this.searchTerm || household.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      if (searchMatch && !uniqueHouseNames.has(household.houseName)) {\n        uniqueHouseNames.add(household.houseName);\n        uniqueHouseholds.push(household);\n      }\n    }\n    return uniqueHouseholds;\n  }\n  // 新增：動態獲取文案的getter方法\n  get displayText() {\n    return {\n      unitType: this.useHouseNameMode ? '戶型' : '戶別',\n      placeholder: this.useHouseNameMode ? '請選擇戶型' : '請選擇戶別',\n      selectedPrefix: this.useHouseNameMode ? '已選擇戶型' : '已選擇戶別',\n      selectUnit: this.useHouseNameMode ? '選擇戶型' : '選擇戶別',\n      unitSelection: this.useHouseNameMode ? '戶型選擇' : '戶別選擇',\n      selectedCount: this.useHouseNameMode ? '個戶型' : '個戶',\n      searchPlaceholder: this.useHouseNameMode ? '搜尋戶型...' : '搜尋戶別...',\n      noResults: this.useHouseNameMode ? '找不到符合的戶型' : '找不到符合的戶別',\n      noAvailable: this.useHouseNameMode ? '此棟別沒有可選擇的戶型' : '此棟別沒有可選擇的戶別'\n    };\n  }\n};\n__decorate([ViewChild('householdDialog', {\n  static: false\n})], HouseholdBindingComponent.prototype, \"householdDialog\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"placeholder\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"maxSelections\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"disabled\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildingData\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowBatchSelect\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"excludedHouseIds\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"useHouseNameMode\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"selectionChange\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"houseIdChange\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"houseNameChange\", void 0);\nHouseholdBindingComponent = __decorate([Component({\n  selector: 'app-household-binding',\n  templateUrl: './household-binding.component.html',\n  styleUrls: ['./household-binding.component.scss'],\n  standalone: true,\n  imports: [CommonModule, NbPopoverModule, NbIconModule],\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => HouseholdBindingComponent),\n    multi: true\n  }]\n})], HouseholdBindingComponent);\nexport { HouseholdBindingComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "forwardRef", "ViewChild", "NG_VALUE_ACCESSOR", "HouseholdBindingComponent", "constructor", "cdr", "dialogService", "placeholder", "maxSelections", "disabled", "buildCaseId", "buildingData", "allowBatchSelect", "excludedHouseIds", "useHouseNameMode", "selectionChange", "houseIdChange", "houseNameChange", "isOpen", "selectedBuilding", "searchTerm", "selectedF<PERSON>or", "selectedHouseIds", "selectedHouseNames", "buildings", "floors", "filteredHouseholds", "selectedByBuilding", "isLoading", "onChange", "value", "onTouched", "getGroupedSummary", "length", "map", "building", "count", "filter", "Boolean", "join", "getAllSelectedItems", "id", "getHouseholdByHouseId", "item", "writeValue", "firstItem", "Set", "convertHouseNamesToIds", "console", "log", "error", "convertIdsToHouseNames", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "grouped", "for<PERSON>ach", "houseId", "find", "h", "push", "onBuildingSelect", "updateFloorsForBuilding", "detectChanges", "onBuildingClick", "households", "filteredItems", "floorMatch", "floor", "searchMatch", "houseName", "toLowerCase", "includes", "onSearchChange", "event", "target", "resetSearch", "onHouseholdToggle", "isHouseIdExcluded", "clickedHousehold", "newSelection", "allMatchingHouseIds", "getAllHouseIdsByHouseName", "hasAnySelected", "some", "isSelected", "isHouseIdSelected", "emitChanges", "onRemoveHousehold", "onSelectAllFiltered", "filteredHouseholdItems", "getUniqueHouseholdsForDisplay", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFilteredIds", "household", "hasAnyExcluded", "toAdd", "slice", "onSelectAllBuilding", "buildingHouseholds", "unselectedBuildingIds", "processedHouseNames", "has", "add", "isHouseholdExcluded", "onUnselectAllBuilding", "buildingHouseIds", "undefined", "onClearAll", "uniqueHouseNames", "emit", "houseIds", "selectedItems", "toggleDropdown", "openDialog", "open", "householdDialog", "context", "closeOnBackdropClick", "closeOnEsc", "autoFocus", "closeDropdown", "isHouseholdSelected", "isHouseholdDisabled", "canSelectMore", "isAllBuildingSelected", "every", "isSomeBuildingSelected", "getSelectedByBuilding", "getBuildingCount", "size", "getSelectedCount", "getBuildingSelectedHouseIds", "hasBuildingSelected", "floorSet", "Array", "from", "sort", "a", "b", "numA", "parseInt", "replace", "numB", "onFloorSelect", "getFloorCount", "getHouseholdFloor", "householdCode", "getHouseholdInfo", "getHouseholdInfoById", "hasNoSearchResults", "filtered", "getFilteredHouseholdsCount", "getHouseholdUniqueId", "toString", "getHouseIdByHouseName", "matchingHouseholds", "matches", "warn", "m", "firstMatch", "getHouseholdFromUniqueId", "uniqueId", "houseNames", "matchingHouseIds", "uniqueHouseIds", "householdInfo", "startsWith", "uniqueHouseholds", "displayText", "unitType", "selectedPrefix", "selectUnit", "unitSelection", "selectedCount", "searchPlaceholder", "noResults", "noAvailable", "__decorate", "static", "selector", "templateUrl", "styleUrls", "standalone", "imports", "CommonModule", "NbPopoverModule", "NbIconModule", "providers", "provide", "useExisting", "multi"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { NbDialogService } from '@nebular/theme';\n\nexport interface HouseholdItem {\n  houseName: string;\n  building: string;\n  floor?: string;\n  houseId?: number;\n  houseType?: number; // 新增：戶別類型\n  isSelected?: boolean;\n  isDisabled?: boolean;\n}\n\nexport interface BuildingData {\n  [key: string]: HouseholdItem[];\n}\n\n@Component({\n  selector: 'app-household-binding',\n  templateUrl: './household-binding.component.html',\n  styleUrls: ['./household-binding.component.scss'],\n  standalone: true,\n  imports: [\n    CommonModule,\n    NbPopoverModule,\n    NbIconModule,\n  ],\n  providers: [\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => HouseholdBindingComponent),\n      multi: true,\n    },\n  ],\n})\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\n  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;\n  @Input() placeholder: string = '請選擇戶別';\n  @Input() maxSelections: number | null = null;\n  @Input() disabled: boolean = false;\n  @Input() buildCaseId: number | null = null; // 建案ID（用於識別）\n  @Input() buildingData: BuildingData = {};\n  @Input() allowBatchSelect: boolean = true;\n  @Input() excludedHouseIds: number[] = []; // 改為：排除的戶別ID（已被其他元件選擇）\n  @Input() useHouseNameMode: boolean = false; // 新增：使用戶別名稱模式\n\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\n  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列\n  @Output() houseNameChange = new EventEmitter<string[]>(); // 新增：useHouseNameMode 時回傳戶別名稱陣列\n  isOpen = false;\n  selectedBuilding = '';\n  searchTerm = ''; selectedFloor = ''; // 新增：選中的樓層\n  selectedHouseIds: number[] = []; // 改為：使用 houseId 作為選擇的key\n  selectedHouseNames: string[] = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列\n  buildings: string[] = [];\n  floors: string[] = []; // 新增：當前棧別的樓層列表\n  filteredHouseholds: string[] = [];  // 保持為字串陣列用於UI顯示\n  selectedByBuilding: { [building: string]: number[] } = {}; // 改為：儲存 houseId\n  isLoading: boolean = false; // 新增：載入狀態  // ControlValueAccessor implementation\n  private onChange = (value: number[] | string[]) => { };\n  private onTouched = () => { }; constructor(\n    private cdr: ChangeDetectorRef,\n    private dialogService: NbDialogService\n  ) { }\n\n  /**\n   * [優化] 產生已選戶別的分組摘要文字。\n   * 用於簡潔地顯示選擇結果。\n   * @returns string - 例如: \"棟 A (10戶), 棟 B (5戶)\"\n   */\n  getGroupedSummary(): string {\n    if (this.selectedHouseIds.length === 0) {\n      return '';\n    }\n\n    return this.buildings\n      .map(building => {\n        const count = this.selectedByBuilding[building]?.length || 0;\n        return count > 0 ? `${building} (${count}戶)` : null;\n      })\n      .filter(Boolean) // 過濾掉沒有選擇的棟別\n      .join(', ');\n  }\n\n  /**\n   * [優化] 獲取所有已選戶別的詳細資訊列表。\n   * 用於在 Popover 中顯示。\n   * @returns HouseholdItem[]\n   */\n  getAllSelectedItems(): HouseholdItem[] {\n    return this.selectedHouseIds\n      .map(id => this.getHouseholdByHouseId(id))\n      .filter((item): item is HouseholdItem => !!item);\n  }\n\n  writeValue(value: any[]): void {\n    if (!value || value.length === 0) {\n      this.selectedHouseIds = [];\n      this.selectedHouseNames = [];\n    } else {\n      const firstItem = value[0]; if (this.useHouseNameMode) {\n        // useHouseNameMode: 期望接收戶別名稱陣列\n        if (typeof firstItem === 'string') {\n          this.selectedHouseNames = [...new Set(value as string[])]; // 去除重複的戶別名稱\n          // 將戶別名稱轉換為 houseId（用於內部邏輯），會自動處理重複名稱\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\n          console.log('useHouseNameMode: 使用傳入的戶別名稱陣列（已去重）');\n        } else {\n          console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);\n          this.selectedHouseNames = [];\n          this.selectedHouseIds = [];\n        }\n      } else {\n        // 一般模式: 期望接收 houseId 陣列\n        if (typeof firstItem === 'number') {\n          this.selectedHouseIds = value as number[];\n          // 將 houseId 轉換為戶別名稱（用於顯示）\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n          console.log('一般模式: 使用傳入的 houseId 陣列');\n        } else if (typeof firstItem === 'string') {\n          // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告\n          console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');\n          console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');\n          return;\n        } else {\n          console.error('writeValue 收到未知格式的資料:', value);\n          this.selectedHouseIds = [];\n          this.selectedHouseNames = [];\n        }\n      }\n    }\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn: (value: number[] | string[]) => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n  } ngOnInit() {\n    this.initializeData();\n  } ngOnChanges(changes: SimpleChanges) {\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    } if (changes['excludedHouseIds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n    }\n    if (changes['useHouseNameMode']) {\n      if (this.useHouseNameMode) {\n        this.selectedFloor = '';\n      }\n    }\n  } private initializeData() {\n    // 使用傳入的 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateSelectedByBuilding();\n    } else {\n      // 沒有 buildingData，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData provided');\n    }\n  } private updateSelectedByBuilding() {\n    const grouped: { [building: string]: number[] } = {};\n\n    this.selectedHouseIds.forEach(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseId);\n          break;\n        }\n      }\n    });\n\n    this.selectedByBuilding = grouped;\n  } onBuildingSelect(building: string) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n\n  onBuildingClick(building: string) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    const filteredItems = households.filter(h => {\n      // 樓層篩選：在 useHouseNameMode 時跳過樓層篩選，否則按原邏輯篩選\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\n\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n  }\n\n  onSearchChange(event: any) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  } onHouseholdToggle(houseId: number | undefined) {\n    console.log('onHouseholdToggle called with houseId:', houseId);\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\n\n    if (!houseId) {\n      console.log(`無效的 houseId: ${houseId}`);\n      return;\n    }\n\n    // 防止選擇已排除的戶別\n    if (this.isHouseIdExcluded(houseId)) {\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n\n    // 取得被點擊戶別的名稱\n    const clickedHousehold = this.getHouseholdByHouseId(houseId);\n    if (!clickedHousehold) {\n      console.log(`找不到 houseId ${houseId} 對應的戶別資訊`);\n      return;\n    }\n\n    let newSelection: number[];\n\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 處理同名戶別的邏輯\n      const houseName = clickedHousehold.houseName;\n      const allMatchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n\n      // 檢查是否有任何同名戶別已被選中\n      const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n\n      if (hasAnySelected) {\n        // 如果有同名戶別被選中，移除所有同名戶別\n        newSelection = this.selectedHouseIds.filter(id => !allMatchingHouseIds.includes(id));\n        console.log(`useHouseNameMode: 移除所有同名戶別 \"${houseName}\":`, allMatchingHouseIds);\n      } else {\n        // 如果沒有同名戶別被選中，只添加第一個（通常是當前點擊的）\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, allMatchingHouseIds[0]];\n        console.log(`useHouseNameMode: 添加戶別 \"${houseName}\" 的第一個項目:`, allMatchingHouseIds[0]);\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      const isSelected = this.isHouseIdSelected(houseId);\n\n      if (isSelected) {\n        newSelection = this.selectedHouseIds.filter(id => id !== houseId);\n      } else {\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, houseId];\n      }\n    }\n\n    this.selectedHouseIds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(houseId: number) {\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\n    this.emitChanges();\n  } onSelectAllFiltered() {\n    console.log('onSelectAllFiltered called');\n    console.log('selectedBuilding:', this.selectedBuilding);\n    console.log('selectedFloor:', this.selectedFloor);\n    console.log('searchTerm:', this.searchTerm);\n\n    if (!this.selectedBuilding) {\n      console.log('No building selected');\n      return;\n    }\n\n    // 使用 getUniqueHouseholdsForDisplay 方法來獲取要處理的戶別列表\n    const filteredHouseholdItems = this.getUniqueHouseholdsForDisplay();\n\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n\n    if (filteredHouseholdItems.length === 0) {\n      console.log('No filtered households found');\n      return;\n    }\n\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n\n    // 取得尚未選擇且未被排除的過濾戶別ID\n    const unselectedFilteredIds: number[] = [];\n    for (const household of filteredHouseholdItems) {\n      if (household.houseId) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 檢查是否有任何同名戶別已被選擇\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseIdExcluded(id));\n\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedFilteredIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        } else {\n          // 一般模式: 原有邏輯\n          if (!this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\n            unselectedFilteredIds.push(household.houseId);\n          }\n        }\n      }\n    }\n\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\n\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\n\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\n    } else {\n      console.log('No households to add');\n    }\n  } onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n\n    // 取得尚未選擇且未被排除的棟別戶別 ID\n    const unselectedBuildingIds: number[] = [];\n\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 只選擇唯一的戶別名稱\n      const processedHouseNames = new Set<string>();\n\n      for (const household of buildingHouseholds) {\n        if (household.houseId && household.houseName && !processedHouseNames.has(household.houseName)) {\n          processedHouseNames.add(household.houseName);\n\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseholdExcluded(id));\n\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedBuildingIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        }\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      for (const household of buildingHouseholds) {\n        if (household.houseId &&\n          !this.selectedHouseIds.includes(household.houseId) &&\n          !this.isHouseholdExcluded(household.houseId)) {\n          unselectedBuildingIds.push(household.houseId);\n        }\n      }\n    }\n\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\n\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined) as number[];\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\n    this.emitChanges();\n  }\n\n  onClearAll() {\n    this.selectedHouseIds = [];\n    this.emitChanges();\n  } private emitChanges() {\n    this.updateSelectedByBuilding();\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds);    // 根據模式決定要回傳的資料格式\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 回傳戶別名稱陣列（去重複）\n      this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      const uniqueHouseNames = [...new Set(this.selectedHouseNames)]; // 確保去重複\n      console.log('useHouseNameMode - 回傳戶別名稱陣列（已去重）:', uniqueHouseNames);\n      this.onChange([...uniqueHouseNames]);\n      this.houseNameChange.emit([...uniqueHouseNames]);\n    } else {\n      // 一般模式: 回傳 houseId 陣列\n      console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);\n      this.onChange([...this.selectedHouseIds]);\n\n      // 回傳 houseId 陣列\n      const houseIds = this.selectedHouseIds.filter(id => id !== undefined);\n      console.log('House IDs to emit:', houseIds);\n      this.houseIdChange.emit(houseIds);\n    }\n\n    this.onTouched();\n\n    // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）\n    const selectedItems = this.selectedHouseIds.map(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null) as HouseholdItem[];\n\n    console.log('Selected items to emit:', selectedItems);\n    this.selectionChange.emit(selectedItems);\n  } toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false,\n    });\n  }\n\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  } isHouseholdSelected(houseId: number | undefined): boolean {\n    if (!houseId) return false;\n\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 檢查是否有任何同名戶別被選中\n      const household = this.getHouseholdByHouseId(houseId);\n      if (household) {\n        const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n        return allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n      }\n      return false;\n    } else {\n      // 一般模式: 直接檢查 houseId\n      return this.selectedHouseIds.includes(houseId);\n    }\n  }\n\n  isHouseholdExcluded(houseId: number | undefined): boolean {\n    if (!houseId) return false;\n    return this.excludedHouseIds.includes(houseId);\n  }\n\n  isHouseholdDisabled(houseId: number | undefined): boolean {\n    if (!houseId) return true;\n    return this.isHouseholdExcluded(houseId) ||\n      (!this.canSelectMore() && !this.isHouseholdSelected(houseId));\n  }\n  canSelectMore(): boolean {\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\n  } isAllBuildingSelected(): boolean {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\n      .filter(h => !h.isDisabled && h.houseId !== undefined);\n    return buildingHouseholds.length > 0 &&\n      buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n\n  isSomeBuildingSelected(): boolean {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  } getSelectedByBuilding(): { [building: string]: number[] } {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building: string): number {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const households = this.buildingData[building] || [];\n      const uniqueHouseNames = new Set(households.map(h => h.houseName));\n      return uniqueHouseNames.size;\n    } else {\n      // 一般模式: 返回總戶別數量\n      return this.buildingData[building]?.length || 0;\n    }\n  }\n  getSelectedCount(): number {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const uniqueHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      return uniqueHouseNames.length;\n    } else {\n      // 一般模式: 返回實際選中的戶別數量\n      return this.selectedHouseIds.length;\n    }\n  }\n\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\n  getBuildingSelectedHouseIds(building: string): number[] {\n    return this.selectedByBuilding[building] || [];\n  }\n\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building: string): boolean {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n\n  // 新增：更新當前棧別的樓層計數\n  private updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set<string>();\n\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n\n  // 新增：樓層選擇處理\n  onFloorSelect(floor: string) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n\n  // 新增：取得當前棧別的樓層計數\n  getFloorCount(floor: string): number {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode: string): string {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode: string): { houseName: string, floor: string } {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return { houseName: householdCode, floor: '' };\n  }\n\n  // 新增：根據 houseId 取得戶別的完整資訊\n  getHouseholdInfoById(houseId: number): { houseName: string, floor: string } {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return { houseName: `ID:${houseId}`, floor: '' };\n  }\n\n  // 新增：檢查搜尋是否有結果\n  hasNoSearchResults(): boolean {\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return false;\n    } const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n\n    return filtered.length === 0;\n  }\n  // 新增：取得過濾後的戶別數量\n  getFilteredHouseholdsCount(): number {\n    return this.getUniqueHouseholdsForDisplay().length;\n  }\n\n  // 新增：產生戶別的唯一識別符\n  getHouseholdUniqueId(household: HouseholdItem): string {\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n  }\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\n  private getHouseholdByHouseId(houseId: number): HouseholdItem | null {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) return household;\n    }\n    return null;\n  }  // 新增：輔助方法 - 根據 houseName 查找 houseId\n  private getHouseIdByHouseName(houseName: string): number | null {\n    const matchingHouseholds: { building: string, household: HouseholdItem }[] = [];\n\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        matchingHouseholds.push({ building, household });\n      });\n    }\n\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\n\n    if (matchingHouseholds.length === 0) {\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n      return null;\n    }\n\n    if (matchingHouseholds.length > 1) {\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\n    }\n\n    const firstMatch = matchingHouseholds[0];\n    return firstMatch.household.houseId || null;\n  }\n\n  // 新增：輔助方法 - 根據 houseName 查找所有對應的 houseId（處理重複名稱）\n  private getAllHouseIdsByHouseName(houseName: string): number[] {\n    const houseIds: number[] = [];\n\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        if (household.houseId) {\n          houseIds.push(household.houseId);\n        }\n      });\n    }\n\n    return houseIds;\n  }\n\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\n  private isHouseIdSelected(houseId: number): boolean {\n    return this.selectedHouseIds.includes(houseId);\n  }\n\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\n  private isHouseIdExcluded(houseId: number): boolean {\n    return this.excludedHouseIds.includes(houseId);\n  }\n  // 新增：從唯一識別符獲取戶別物件\n  getHouseholdFromUniqueId(uniqueId: string): HouseholdItem | null {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n      if (household) return household;\n    }\n    return null;\n  }  // 新增：將戶別名稱陣列轉換為 houseId 陣列（在 useHouseNameMode 下只選擇第一個匹配項）\n  private convertHouseNamesToIds(houseNames: string[]): number[] {\n    const houseIds: number[] = [];\n    const uniqueHouseNames = [...new Set(houseNames)]; // 去除重複的戶別名稱\n\n    for (const houseName of uniqueHouseNames) {\n      const matchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n      if (matchingHouseIds.length > 0) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 只選擇第一個匹配的 houseId，避免多個同名戶別都被選中\n          houseIds.push(matchingHouseIds[0]);\n          if (matchingHouseIds.length > 1) {\n            console.log(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，在 useHouseNameMode 下只選擇第一個:`, matchingHouseIds[0]);\n          }\n        } else {\n          // 一般模式: 將所有對應的 houseId 都加入（保持原有邏輯）\n          houseIds.push(...matchingHouseIds);\n          if (matchingHouseIds.length > 1) {\n            console.warn(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，已全部加入選擇:`, matchingHouseIds);\n          }\n        }\n      } else {\n        console.warn(`無法找到戶別名稱 \"${houseName}\" 對應的 houseId`);\n      }\n    }\n\n    // 去除重複的 houseId\n    return [...new Set(houseIds)];\n  }\n  // 新增：將 houseId 陣列轉換為戶別名稱陣列（去重複）\n  private convertIdsToHouseNames(houseIds: number[]): string[] {\n    const houseNames: string[] = [];\n    const uniqueHouseIds = [...new Set(houseIds)]; // 去除重複的 houseId\n\n    for (const houseId of uniqueHouseIds) {\n      const householdInfo = this.getHouseholdInfoById(houseId);\n      if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {\n        houseNames.push(householdInfo.houseName);\n      } else {\n        console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);\n      }\n    }\n\n    // 去除重複的戶別名稱\n    return [...new Set(houseNames)];\n  }\n\n  // 新增：取得去重複的戶別列表（用於 useHouseNameMode 顯示）\n  getUniqueHouseholdsForDisplay(): HouseholdItem[] {\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return [];\n    }\n\n    const households = this.buildingData[this.selectedBuilding] || [];\n\n    if (!this.useHouseNameMode) {\n      // 一般模式：返回所有戶別\n      return households.filter(h => {\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n        const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n        return floorMatch && searchMatch;\n      });\n    }\n\n    // useHouseNameMode：只返回唯一的戶別名稱\n    const uniqueHouseNames = new Set<string>();\n    const uniqueHouseholds: HouseholdItem[] = [];\n\n    for (const household of households) {\n      // 搜尋篩選\n      const searchMatch = !this.searchTerm || household.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n\n      if (searchMatch && !uniqueHouseNames.has(household.houseName)) {\n        uniqueHouseNames.add(household.houseName);\n        uniqueHouseholds.push(household);\n      }\n    }\n    return uniqueHouseholds;\n  }\n\n  // 新增：動態獲取文案的getter方法\n  get displayText() {\n    return {\n      unitType: this.useHouseNameMode ? '戶型' : '戶別',\n      placeholder: this.useHouseNameMode ? '請選擇戶型' : '請選擇戶別',\n      selectedPrefix: this.useHouseNameMode ? '已選擇戶型' : '已選擇戶別',\n      selectUnit: this.useHouseNameMode ? '選擇戶型' : '選擇戶別',\n      unitSelection: this.useHouseNameMode ? '戶型選擇' : '戶別選擇',\n      selectedCount: this.useHouseNameMode ? '個戶型' : '個戶',\n      searchPlaceholder: this.useHouseNameMode ? '搜尋戶型...' : '搜尋戶別...',\n      noResults: this.useHouseNameMode ? '找不到符合的戶型' : '找不到符合的戶別',\n      noAvailable: this.useHouseNameMode ? '此棟別沒有可選擇的戶型' : '此棟別沒有可選擇的戶別'\n    };\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAoCC,UAAU,EAAkCC,SAAS,QAAQ,eAAe;AAC/J,SAA+BC,iBAAiB,QAAQ,gBAAgB;AAmCjE,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EAyBLC,YACrBC,GAAsB,EACtBC,aAA8B;IAD9B,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,aAAa,GAAbA,aAAa;IAzBd,KAAAC,WAAW,GAAW,OAAO;IAC7B,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAC,YAAY,GAAiB,EAAE;IAC/B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,gBAAgB,GAAY,KAAK,CAAC,CAAC;IAElC,KAAAC,eAAe,GAAG,IAAIhB,YAAY,EAAmB;IACrD,KAAAiB,aAAa,GAAG,IAAIjB,YAAY,EAAY,CAAC,CAAC;IAC9C,KAAAkB,eAAe,GAAG,IAAIlB,YAAY,EAAY,CAAC,CAAC;IAC1D,KAAAmB,MAAM,GAAG,KAAK;IACd,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,UAAU,GAAG,EAAE;IAAE,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACrC,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAC;IACnC,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE,CAAC,CAAC;IAC3D,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAC;IACpB,KAAAC,QAAQ,GAAIC,KAA0B,IAAI,CAAG,CAAC;IAC9C,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAGzB;EAEJ;;;;;EAKAC,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACV,gBAAgB,CAACW,MAAM,KAAK,CAAC,EAAE;MACtC,OAAO,EAAE;IACX;IAEA,OAAO,IAAI,CAACT,SAAS,CAClBU,GAAG,CAACC,QAAQ,IAAG;MACd,MAAMC,KAAK,GAAG,IAAI,CAACT,kBAAkB,CAACQ,QAAQ,CAAC,EAAEF,MAAM,IAAI,CAAC;MAC5D,OAAOG,KAAK,GAAG,CAAC,GAAG,GAAGD,QAAQ,KAAKC,KAAK,IAAI,GAAG,IAAI;IACrD,CAAC,CAAC,CACDC,MAAM,CAACC,OAAO,CAAC,CAAC;IAAA,CAChBC,IAAI,CAAC,IAAI,CAAC;EACf;EAEA;;;;;EAKAC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAClB,gBAAgB,CACzBY,GAAG,CAACO,EAAE,IAAI,IAAI,CAACC,qBAAqB,CAACD,EAAE,CAAC,CAAC,CACzCJ,MAAM,CAAEM,IAAI,IAA4B,CAAC,CAACA,IAAI,CAAC;EACpD;EAEAC,UAAUA,CAACd,KAAY;IACrB,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACX,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC9B,CAAC,MAAM;MACL,MAAMsB,SAAS,GAAGf,KAAK,CAAC,CAAC,CAAC;MAAE,IAAI,IAAI,CAAChB,gBAAgB,EAAE;QACrD;QACA,IAAI,OAAO+B,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACtB,kBAAkB,GAAG,CAAC,GAAG,IAAIuB,GAAG,CAAChB,KAAiB,CAAC,CAAC,CAAC,CAAC;UAC3D;UACA,IAAI,CAACR,gBAAgB,GAAG,IAAI,CAACyB,sBAAsB,CAAC,IAAI,CAACxB,kBAAkB,CAAC;UAC5EyB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACnD,CAAC,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAE,OAAOL,SAAS,CAAC;UACtE,IAAI,CAACtB,kBAAkB,GAAG,EAAE;UAC5B,IAAI,CAACD,gBAAgB,GAAG,EAAE;QAC5B;MACF,CAAC,MAAM;QACL;QACA,IAAI,OAAOuB,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACvB,gBAAgB,GAAGQ,KAAiB;UACzC;UACA,IAAI,CAACP,kBAAkB,GAAG,IAAI,CAAC4B,sBAAsB,CAAC,IAAI,CAAC7B,gBAAgB,CAAC;UAC5E0B,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACvC,CAAC,MAAM,IAAI,OAAOJ,SAAS,KAAK,QAAQ,EAAE;UACxC;UACAG,OAAO,CAACE,KAAK,CAAC,oCAAoC,CAAC;UACnDF,OAAO,CAACE,KAAK,CAAC,2CAA2C,CAAC;UAC1D;QACF,CAAC,MAAM;UACLF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEpB,KAAK,CAAC;UAC7C,IAAI,CAACR,gBAAgB,GAAG,EAAE;UAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE;QAC9B;MACF;IACF;IACA,IAAI,CAAC6B,wBAAwB,EAAE;EACjC;EACAC,gBAAgBA,CAACC,EAAwC;IACvD,IAAI,CAACzB,QAAQ,GAAGyB,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACvB,SAAS,GAAGuB,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAAChD,QAAQ,GAAGgD,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EAAEC,WAAWA,CAACC,OAAsB;IAClC,IAAIA,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B;MACA,IAAI,CAACrC,SAAS,GAAGsC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpD,YAAY,IAAI,EAAE,CAAC;MACrDqC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACtC,YAAY,CAAC;MACvD,IAAI,CAACqD,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IAAE,IAAIS,OAAO,CAAC,kBAAkB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;IACjC;IACA,IAAIH,OAAO,CAAC,kBAAkB,CAAC,EAAE;MAC/B,IAAI,IAAI,CAAC/C,gBAAgB,EAAE;QACzB,IAAI,CAACO,aAAa,GAAG,EAAE;MACzB;IACF;EACF;EAAUsC,cAAcA,CAAA;IACtB;IACA,IAAI,IAAI,CAAChD,YAAY,IAAImD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpD,YAAY,CAAC,CAACsB,MAAM,GAAG,CAAC,EAAE;MAClE,IAAI,CAACT,SAAS,GAAGsC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpD,YAAY,CAAC;MAC/C,IAAI,CAACyC,wBAAwB,EAAE;IACjC,CAAC,MAAM;MACL;MACA,IAAI,CAAC5B,SAAS,GAAG,EAAE;MACnBwB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC;EACF;EAAUG,wBAAwBA,CAAA;IAChC,MAAMa,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAAC3C,gBAAgB,CAAC4C,OAAO,CAACC,OAAO,IAAG;MACtC,KAAK,MAAMhC,QAAQ,IAAI,IAAI,CAACX,SAAS,EAAE;QACrC,MAAMmB,IAAI,GAAG,IAAI,CAAChC,YAAY,CAACwB,QAAQ,CAAC,EAAEiC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAIxB,IAAI,EAAE;UACR,IAAI,CAACsB,OAAO,CAAC9B,QAAQ,CAAC,EAAE8B,OAAO,CAAC9B,QAAQ,CAAC,GAAG,EAAE;UAC9C8B,OAAO,CAAC9B,QAAQ,CAAC,CAACmC,IAAI,CAACH,OAAO,CAAC;UAC/B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACxC,kBAAkB,GAAGsC,OAAO;EACnC;EAAEM,gBAAgBA,CAACpC,QAAgB;IACjCa,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEd,QAAQ,CAAC;IAC3C,IAAI,CAAChB,gBAAgB,GAAGgB,QAAQ;IAChC,IAAI,CAACd,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACD,UAAU,GAAG,EAAE;IACpB,IAAI,CAACoD,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAACR,wBAAwB,EAAE;IAC/BhB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACvB,kBAAkB,CAACO,MAAM,CAAC;IACzE;IACA,IAAI,CAAC5B,GAAG,CAACoE,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAACvC,QAAgB;IAC9Ba,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEd,QAAQ,CAAC;EACxD;EACA6B,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAAC7C,gBAAgB,EAAE;MAC1B,IAAI,CAACO,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMiD,UAAU,GAAG,IAAI,CAAChE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjE6B,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE0B,UAAU,CAAC1C,MAAM,CAAC;IAEpE;IACA,MAAM2C,aAAa,GAAGD,UAAU,CAACtC,MAAM,CAACgC,CAAC,IAAG;MAC1C;MACA,MAAMQ,UAAU,GAAG,IAAI,CAAC/D,gBAAgB,IAAI,CAAC,IAAI,CAACO,aAAa,IAAIgD,CAAC,CAACS,KAAK,KAAK,IAAI,CAACzD,aAAa;MACjG;MACA,MAAM0D,WAAW,GAAG,CAAC,IAAI,CAAC3D,UAAU,IAAIiD,CAAC,CAACW,SAAS,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC9D,UAAU,CAAC6D,WAAW,EAAE,CAAC;MACzG,OAAOJ,UAAU,IAAIE,WAAW;IAClC,CAAC,CAAC;IAEF;IACA,IAAI,CAACrD,kBAAkB,GAAGkD,aAAa,CAAC1C,GAAG,CAACmC,CAAC,IAAIA,CAAC,CAACW,SAAS,CAAC;IAE7DhC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACvB,kBAAkB,CAACO,MAAM,CAAC;IAC1Ee,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE2B,aAAa,CAAC1C,GAAG,CAACmC,CAAC,IAAI,GAAGA,CAAC,CAACW,SAAS,IAAIX,CAAC,CAACS,KAAK,QAAQT,CAAC,CAACF,OAAO,GAAG,CAAC,CAAC;EAC/G;EAEAgB,cAAcA,CAACC,KAAU;IACvB,IAAI,CAAChE,UAAU,GAAGgE,KAAK,CAACC,MAAM,CAACvD,KAAK;IACpC,IAAI,CAACkC,wBAAwB,EAAE;IAC/BhB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC7B,UAAU,CAAC;EACtD;EACAkE,WAAWA,CAAA;IACT,IAAI,CAAClE,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC4C,wBAAwB,EAAE;IAC/BhB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EAAEsC,iBAAiBA,CAACpB,OAA2B;IAC7CnB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEkB,OAAO,CAAC;IAC9DnB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC3B,gBAAgB,CAAC;IAE/D,IAAI,CAAC6C,OAAO,EAAE;MACZnB,OAAO,CAACC,GAAG,CAAC,gBAAgBkB,OAAO,EAAE,CAAC;MACtC;IACF;IAEA;IACA,IAAI,IAAI,CAACqB,iBAAiB,CAACrB,OAAO,CAAC,EAAE;MACnCnB,OAAO,CAACC,GAAG,CAAC,SAASkB,OAAO,kBAAkB,CAAC;MAC/C;IACF;IAEA;IACA,MAAMsB,gBAAgB,GAAG,IAAI,CAAC/C,qBAAqB,CAACyB,OAAO,CAAC;IAC5D,IAAI,CAACsB,gBAAgB,EAAE;MACrBzC,OAAO,CAACC,GAAG,CAAC,eAAekB,OAAO,UAAU,CAAC;MAC7C;IACF;IAEA,IAAIuB,YAAsB;IAE1B,IAAI,IAAI,CAAC5E,gBAAgB,EAAE;MACzB;MACA,MAAMkE,SAAS,GAAGS,gBAAgB,CAACT,SAAS;MAC5C,MAAMW,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACZ,SAAS,CAAC;MAErE;MACA,MAAMa,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAACrD,EAAE,IAAI,IAAI,CAACnB,gBAAgB,CAAC4D,QAAQ,CAACzC,EAAE,CAAC,CAAC;MAEzF,IAAIoD,cAAc,EAAE;QAClB;QACAH,YAAY,GAAG,IAAI,CAACpE,gBAAgB,CAACe,MAAM,CAACI,EAAE,IAAI,CAACkD,mBAAmB,CAACT,QAAQ,CAACzC,EAAE,CAAC,CAAC;QACpFO,OAAO,CAACC,GAAG,CAAC,+BAA+B+B,SAAS,IAAI,EAAEW,mBAAmB,CAAC;MAChF,CAAC,MAAM;QACL;QACA,IAAI,IAAI,CAACnF,aAAa,IAAI,IAAI,CAACc,gBAAgB,CAACW,MAAM,IAAI,IAAI,CAACzB,aAAa,EAAE;UAC5EwC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;UACxB;QACF;QACAyC,YAAY,GAAG,CAAC,GAAG,IAAI,CAACpE,gBAAgB,EAAEqE,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACjE3C,OAAO,CAACC,GAAG,CAAC,2BAA2B+B,SAAS,WAAW,EAAEW,mBAAmB,CAAC,CAAC,CAAC,CAAC;MACtF;IACF,CAAC,MAAM;MACL;MACA,MAAMI,UAAU,GAAG,IAAI,CAACC,iBAAiB,CAAC7B,OAAO,CAAC;MAElD,IAAI4B,UAAU,EAAE;QACdL,YAAY,GAAG,IAAI,CAACpE,gBAAgB,CAACe,MAAM,CAACI,EAAE,IAAIA,EAAE,KAAK0B,OAAO,CAAC;MACnE,CAAC,MAAM;QACL,IAAI,IAAI,CAAC3D,aAAa,IAAI,IAAI,CAACc,gBAAgB,CAACW,MAAM,IAAI,IAAI,CAACzB,aAAa,EAAE;UAC5EwC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;UACxB;QACF;QACAyC,YAAY,GAAG,CAAC,GAAG,IAAI,CAACpE,gBAAgB,EAAE6C,OAAO,CAAC;MACpD;IACF;IAEA,IAAI,CAAC7C,gBAAgB,GAAGoE,YAAY;IACpC,IAAI,CAACO,WAAW,EAAE;EACpB;EACAC,iBAAiBA,CAAC/B,OAAe;IAC/B,IAAI,CAAC7C,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACe,MAAM,CAACI,EAAE,IAAIA,EAAE,KAAK0B,OAAO,CAAC;IAC1E,IAAI,CAAC8B,WAAW,EAAE;EACpB;EAAEE,mBAAmBA,CAAA;IACnBnD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC9B,gBAAgB,CAAC;IACvD6B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC5B,aAAa,CAAC;IACjD2B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC7B,UAAU,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC1B6B,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEA;IACA,MAAMmD,sBAAsB,GAAG,IAAI,CAACC,6BAA6B,EAAE;IAEnErD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEmD,sBAAsB,CAAClE,GAAG,CAACmC,CAAC,IAAI,GAAGA,CAAC,CAACW,SAAS,IAAIX,CAAC,CAACS,KAAK,QAAQT,CAAC,CAACF,OAAO,GAAG,CAAC,CAAC;IAExH,IAAIiC,sBAAsB,CAACnE,MAAM,KAAK,CAAC,EAAE;MACvCe,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;IACF;IAEA;IACA,MAAMqD,YAAY,GAAG,IAAI,CAAChF,gBAAgB,CAACW,MAAM;IACjD,MAAMsE,UAAU,GAAG,IAAI,CAAC/F,aAAa,IAAIgG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMI,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAMC,SAAS,IAAIP,sBAAsB,EAAE;MAC9C,IAAIO,SAAS,CAACxC,OAAO,EAAE;QACrB,IAAI,IAAI,CAACrD,gBAAgB,EAAE;UACzB;UACA,MAAM6E,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACe,SAAS,CAAC3B,SAAS,CAAC;UAC/E,MAAMa,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAACrD,EAAE,IAAI,IAAI,CAACnB,gBAAgB,CAAC4D,QAAQ,CAACzC,EAAE,CAAC,CAAC;UACzF,MAAMmE,cAAc,GAAGjB,mBAAmB,CAACG,IAAI,CAACrD,EAAE,IAAI,IAAI,CAAC+C,iBAAiB,CAAC/C,EAAE,CAAC,CAAC;UAEjF,IAAI,CAACoD,cAAc,IAAI,CAACe,cAAc,EAAE;YACtCF,qBAAqB,CAACpC,IAAI,CAACqB,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD;QACF,CAAC,MAAM;UACL;UACA,IAAI,CAAC,IAAI,CAACK,iBAAiB,CAACW,SAAS,CAACxC,OAAO,CAAC,IAAI,CAAC,IAAI,CAACqB,iBAAiB,CAACmB,SAAS,CAACxC,OAAO,CAAC,EAAE;YAC5FuC,qBAAqB,CAACpC,IAAI,CAACqC,SAAS,CAACxC,OAAO,CAAC;UAC/C;QACF;MACF;IACF;IAEAnB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEyD,qBAAqB,CAAC;IAE9D;IACA,MAAMG,KAAK,GAAGH,qBAAqB,CAACI,KAAK,CAAC,CAAC,EAAEL,cAAc,CAAC;IAE5D,IAAII,KAAK,CAAC5E,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACX,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGuF,KAAK,CAAC;MAC5D,IAAI,CAACZ,WAAW,EAAE;MAClBjD,OAAO,CAACC,GAAG,CAAC,aAAa4D,KAAK,CAAC5E,MAAM,WAAW,EAAE4E,KAAK,CAAC;IAC1D,CAAC,MAAM;MACL7D,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF;EAAE8D,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC5F,gBAAgB,EAAE;IAE5B;IACA,MAAM6F,kBAAkB,GAAG,IAAI,CAACrG,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IAEzE;IACA,MAAMmF,YAAY,GAAG,IAAI,CAAChF,gBAAgB,CAACW,MAAM;IACjD,MAAMsE,UAAU,GAAG,IAAI,CAAC/F,aAAa,IAAIgG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMW,qBAAqB,GAAa,EAAE;IAE1C,IAAI,IAAI,CAACnG,gBAAgB,EAAE;MACzB;MACA,MAAMoG,mBAAmB,GAAG,IAAIpE,GAAG,EAAU;MAE7C,KAAK,MAAM6D,SAAS,IAAIK,kBAAkB,EAAE;QAC1C,IAAIL,SAAS,CAACxC,OAAO,IAAIwC,SAAS,CAAC3B,SAAS,IAAI,CAACkC,mBAAmB,CAACC,GAAG,CAACR,SAAS,CAAC3B,SAAS,CAAC,EAAE;UAC7FkC,mBAAmB,CAACE,GAAG,CAACT,SAAS,CAAC3B,SAAS,CAAC;UAE5C,MAAMW,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACe,SAAS,CAAC3B,SAAS,CAAC;UAC/E,MAAMa,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAACrD,EAAE,IAAI,IAAI,CAACnB,gBAAgB,CAAC4D,QAAQ,CAACzC,EAAE,CAAC,CAAC;UACzF,MAAMmE,cAAc,GAAGjB,mBAAmB,CAACG,IAAI,CAACrD,EAAE,IAAI,IAAI,CAAC4E,mBAAmB,CAAC5E,EAAE,CAAC,CAAC;UAEnF,IAAI,CAACoD,cAAc,IAAI,CAACe,cAAc,EAAE;YACtCK,qBAAqB,CAAC3C,IAAI,CAACqB,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD;QACF;MACF;IACF,CAAC,MAAM;MACL;MACA,KAAK,MAAMgB,SAAS,IAAIK,kBAAkB,EAAE;QAC1C,IAAIL,SAAS,CAACxC,OAAO,IACnB,CAAC,IAAI,CAAC7C,gBAAgB,CAAC4D,QAAQ,CAACyB,SAAS,CAACxC,OAAO,CAAC,IAClD,CAAC,IAAI,CAACkD,mBAAmB,CAACV,SAAS,CAACxC,OAAO,CAAC,EAAE;UAC9C8C,qBAAqB,CAAC3C,IAAI,CAACqC,SAAS,CAACxC,OAAO,CAAC;QAC/C;MACF;IACF;IAEA;IACA,MAAM0C,KAAK,GAAGI,qBAAqB,CAACH,KAAK,CAAC,CAAC,EAAEL,cAAc,CAAC;IAE5D,IAAII,KAAK,CAAC5E,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACX,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGuF,KAAK,CAAC;MAC5D,IAAI,CAACZ,WAAW,EAAE;MAClBjD,OAAO,CAACC,GAAG,CAAC,aAAa4D,KAAK,CAAC5E,MAAM,MAAM,CAAC;IAC9C;EACF;EAEAqF,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACnG,gBAAgB,EAAE;IAE5B,MAAM6F,kBAAkB,GAAG,IAAI,CAACrG,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACzE,MAAMoG,gBAAgB,GAAGP,kBAAkB,CAAC9E,GAAG,CAACmC,CAAC,IAAIA,CAAC,CAACF,OAAO,CAAC,CAAC9B,MAAM,CAACI,EAAE,IAAIA,EAAE,KAAK+E,SAAS,CAAa;IAC1G,IAAI,CAAClG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACe,MAAM,CAACI,EAAE,IAAI,CAAC8E,gBAAgB,CAACrC,QAAQ,CAACzC,EAAE,CAAC,CAAC;IAC1F,IAAI,CAACwD,WAAW,EAAE;EACpB;EAEAwB,UAAUA,CAAA;IACR,IAAI,CAACnG,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC2E,WAAW,EAAE;EACpB;EAAUA,WAAWA,CAAA;IACnB,IAAI,CAAC7C,wBAAwB,EAAE;IAC/BJ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC3B,gBAAgB,CAAC,CAAC,CAAI;IAC/E,IAAI,IAAI,CAACR,gBAAgB,EAAE;MACzB;MACA,IAAI,CAACS,kBAAkB,GAAG,IAAI,CAAC4B,sBAAsB,CAAC,IAAI,CAAC7B,gBAAgB,CAAC;MAC5E,MAAMoG,gBAAgB,GAAG,CAAC,GAAG,IAAI5E,GAAG,CAAC,IAAI,CAACvB,kBAAkB,CAAC,CAAC,CAAC,CAAC;MAChEyB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyE,gBAAgB,CAAC;MAClE,IAAI,CAAC7F,QAAQ,CAAC,CAAC,GAAG6F,gBAAgB,CAAC,CAAC;MACpC,IAAI,CAACzG,eAAe,CAAC0G,IAAI,CAAC,CAAC,GAAGD,gBAAgB,CAAC,CAAC;IAClD,CAAC,MAAM;MACL;MACA1E,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC3B,gBAAgB,CAAC;MAC3D,IAAI,CAACO,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACP,gBAAgB,CAAC,CAAC;MAEzC;MACA,MAAMsG,QAAQ,GAAG,IAAI,CAACtG,gBAAgB,CAACe,MAAM,CAACI,EAAE,IAAIA,EAAE,KAAK+E,SAAS,CAAC;MACrExE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2E,QAAQ,CAAC;MAC3C,IAAI,CAAC5G,aAAa,CAAC2G,IAAI,CAACC,QAAQ,CAAC;IACnC;IAEA,IAAI,CAAC7F,SAAS,EAAE;IAEhB;IACA,MAAM8F,aAAa,GAAG,IAAI,CAACvG,gBAAgB,CAACY,GAAG,CAACiC,OAAO,IAAG;MACxD,KAAK,MAAMhC,QAAQ,IAAI,IAAI,CAACX,SAAS,EAAE;QACrC,MAAMmB,IAAI,GAAG,IAAI,CAAChC,YAAY,CAACwB,QAAQ,CAAC,EAAEiC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAIxB,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACN,MAAM,CAACM,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnDK,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4E,aAAa,CAAC;IACrD,IAAI,CAAC9G,eAAe,CAAC4G,IAAI,CAACE,aAAa,CAAC;EAC1C;EAAEC,cAAcA,CAAA;IACd,IAAI,CAAC,IAAI,CAACrH,QAAQ,EAAE;MAClB,IAAI,CAACsH,UAAU,EAAE;MACjB/E,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACzB,SAAS,CAAC;IACrD;EACF;EAEAuG,UAAUA,CAAA;IACR,IAAI,CAACzH,aAAa,CAAC0H,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC5CC,OAAO,EAAE,EAAE;MACXC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA;EAAA;EACAC,mBAAmBA,CAACpE,OAA2B;IAC/C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B,IAAI,IAAI,CAACrD,gBAAgB,EAAE;MACzB;MACA,MAAM6F,SAAS,GAAG,IAAI,CAACjE,qBAAqB,CAACyB,OAAO,CAAC;MACrD,IAAIwC,SAAS,EAAE;QACb,MAAMhB,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACe,SAAS,CAAC3B,SAAS,CAAC;QAC/E,OAAOW,mBAAmB,CAACG,IAAI,CAACrD,EAAE,IAAI,IAAI,CAACnB,gBAAgB,CAAC4D,QAAQ,CAACzC,EAAE,CAAC,CAAC;MAC3E;MACA,OAAO,KAAK;IACd,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAACnB,gBAAgB,CAAC4D,QAAQ,CAACf,OAAO,CAAC;IAChD;EACF;EAEAkD,mBAAmBA,CAAClD,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAACtD,gBAAgB,CAACqE,QAAQ,CAACf,OAAO,CAAC;EAChD;EAEAqE,mBAAmBA,CAACrE,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB,OAAO,IAAI,CAACkD,mBAAmB,CAAClD,OAAO,CAAC,IACrC,CAAC,IAAI,CAACsE,aAAa,EAAE,IAAI,CAAC,IAAI,CAACF,mBAAmB,CAACpE,OAAO,CAAE;EACjE;EACAsE,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAACjI,aAAa,IAAI,IAAI,CAACc,gBAAgB,CAACW,MAAM,GAAG,IAAI,CAACzB,aAAa;EACjF;EAAEkI,qBAAqBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACvH,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM6F,kBAAkB,GAAG,IAAI,CAACrG,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,CAChEkB,MAAM,CAACgC,CAAC,IAAI,CAACA,CAAC,CAACZ,UAAU,IAAIY,CAAC,CAACF,OAAO,KAAKqD,SAAS,CAAC;IACxD,OAAOR,kBAAkB,CAAC/E,MAAM,GAAG,CAAC,IAClC+E,kBAAkB,CAAC2B,KAAK,CAAChC,SAAS,IAAIA,SAAS,CAACxC,OAAO,IAAI,IAAI,CAAC7C,gBAAgB,CAAC4D,QAAQ,CAACyB,SAAS,CAACxC,OAAO,CAAC,CAAC;EACjH;EAEAyE,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACzH,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM6F,kBAAkB,GAAG,IAAI,CAACrG,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACzE,OAAO6F,kBAAkB,CAAClB,IAAI,CAACa,SAAS,IAAIA,SAAS,CAACxC,OAAO,IAAI,IAAI,CAAC7C,gBAAgB,CAAC4D,QAAQ,CAACyB,SAAS,CAACxC,OAAO,CAAC,CAAC;EACrH;EAAE0E,qBAAqBA,CAAA;IACrB,OAAO,IAAI,CAAClH,kBAAkB;EAChC;EACAmH,gBAAgBA,CAAC3G,QAAgB;IAC/B,IAAI,IAAI,CAACrB,gBAAgB,EAAE;MACzB;MACA,MAAM6D,UAAU,GAAG,IAAI,CAAChE,YAAY,CAACwB,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMuF,gBAAgB,GAAG,IAAI5E,GAAG,CAAC6B,UAAU,CAACzC,GAAG,CAACmC,CAAC,IAAIA,CAAC,CAACW,SAAS,CAAC,CAAC;MAClE,OAAO0C,gBAAgB,CAACqB,IAAI;IAC9B,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAACpI,YAAY,CAACwB,QAAQ,CAAC,EAAEF,MAAM,IAAI,CAAC;IACjD;EACF;EACA+G,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAClI,gBAAgB,EAAE;MACzB;MACA,MAAM4G,gBAAgB,GAAG,IAAI,CAACvE,sBAAsB,CAAC,IAAI,CAAC7B,gBAAgB,CAAC;MAC3E,OAAOoG,gBAAgB,CAACzF,MAAM;IAChC,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAACX,gBAAgB,CAACW,MAAM;IACrC;EACF;EAEA;EACAgH,2BAA2BA,CAAC9G,QAAgB;IAC1C,OAAO,IAAI,CAACR,kBAAkB,CAACQ,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACA+G,mBAAmBA,CAAC/G,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAACR,kBAAkB,CAACQ,QAAQ,CAAC,IAAI,IAAI,CAACR,kBAAkB,CAACQ,QAAQ,CAAC,CAACF,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQuC,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACrD,gBAAgB,EAAE;MAC1B,IAAI,CAACM,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAMkD,UAAU,GAAG,IAAI,CAAChE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMgI,QAAQ,GAAG,IAAIrG,GAAG,EAAU;IAElC6B,UAAU,CAACT,OAAO,CAACyC,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAAC7B,KAAK,EAAE;QACnBqE,QAAQ,CAAC/B,GAAG,CAACT,SAAS,CAAC7B,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACrD,MAAM,GAAG2H,KAAK,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGC,QAAQ,CAACH,CAAC,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGF,QAAQ,CAACF,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOF,IAAI,GAAGG,IAAI;IACpB,CAAC,CAAC;IAEF5G,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC9B,gBAAgB,EAAE,IAAI,CAACM,MAAM,CAAC;EACjF;EAEA;EACAoI,aAAaA,CAAC/E,KAAa;IACzB9B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE6B,KAAK,CAAC;IACrC,IAAI,CAACzD,aAAa,GAAG,IAAI,CAACA,aAAa,KAAKyD,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAACd,wBAAwB,EAAE;IAC/B,IAAI,CAAC3D,GAAG,CAACoE,aAAa,EAAE;EAC1B;EAEA;EACAqF,aAAaA,CAAChF,KAAa;IACzB,IAAI,CAAC,IAAI,CAAC3D,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAMwD,UAAU,GAAG,IAAI,CAAChE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAOwD,UAAU,CAACtC,MAAM,CAACgC,CAAC,IAAIA,CAAC,CAACS,KAAK,KAAKA,KAAK,CAAC,CAAC7C,MAAM;EACzD;EACA;EACA8H,iBAAiBA,CAACC,aAAqB;IACrC,IAAI,CAAC,IAAI,CAAC7I,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAMwD,UAAU,GAAG,IAAI,CAAChE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMwF,SAAS,GAAGhC,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACW,SAAS,KAAKgF,aAAa,CAAC;IACrE,OAAOrD,SAAS,EAAE7B,KAAK,IAAI,EAAE;EAC/B;EACA;EACAmF,gBAAgBA,CAACD,aAAqB;IACpC,KAAK,MAAM7H,QAAQ,IAAI,IAAI,CAACX,SAAS,EAAE;MACrC,MAAMmD,UAAU,GAAG,IAAI,CAAChE,YAAY,CAACwB,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMwE,SAAS,GAAGhC,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACW,SAAS,KAAKgF,aAAa,CAAC;MACrE,IAAIrD,SAAS,EAAE;QACb,OAAO;UACL3B,SAAS,EAAE2B,SAAS,CAAC3B,SAAS;UAC9BF,KAAK,EAAE6B,SAAS,CAAC7B,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEE,SAAS,EAAEgF,aAAa;MAAElF,KAAK,EAAE;IAAE,CAAE;EAChD;EAEA;EACAoF,oBAAoBA,CAAC/F,OAAe;IAClC,KAAK,MAAMhC,QAAQ,IAAI,IAAI,CAACX,SAAS,EAAE;MACrC,MAAMmD,UAAU,GAAG,IAAI,CAAChE,YAAY,CAACwB,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMwE,SAAS,GAAGhC,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAIwC,SAAS,EAAE;QACb,OAAO;UACL3B,SAAS,EAAE2B,SAAS,CAAC3B,SAAS;UAC9BF,KAAK,EAAE6B,SAAS,CAAC7B,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEE,SAAS,EAAE,MAAMb,OAAO,EAAE;MAAEW,KAAK,EAAE;IAAE,CAAE;EAClD;EAEA;EACAqF,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC/I,UAAU,IAAI,CAAC,IAAI,CAACD,gBAAgB,IAAI,CAAC,IAAI,CAACR,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,EAAE;MAC3F,OAAO,KAAK;IACd;IAAE,MAAMiJ,QAAQ,GAAG,IAAI,CAACzJ,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,CAACkB,MAAM,CAACgC,CAAC,IAAG;MACrE,MAAMQ,UAAU,GAAG,IAAI,CAAC/D,gBAAgB,IAAI,CAAC,IAAI,CAACO,aAAa,IAAIgD,CAAC,CAACS,KAAK,KAAK,IAAI,CAACzD,aAAa;MACjG,MAAM0D,WAAW,GAAGV,CAAC,CAACW,SAAS,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC9D,UAAU,CAAC6D,WAAW,EAAE,CAAC;MACrF,OAAOJ,UAAU,IAAIE,WAAW;IAClC,CAAC,CAAC;IAEF,OAAOqF,QAAQ,CAACnI,MAAM,KAAK,CAAC;EAC9B;EACA;EACAoI,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAAChE,6BAA6B,EAAE,CAACpE,MAAM;EACpD;EAEA;EACAqI,oBAAoBA,CAAC3D,SAAwB;IAC3C,OAAOA,SAAS,CAACxC,OAAO,GAAGwC,SAAS,CAACxC,OAAO,CAACoG,QAAQ,EAAE,GAAG,GAAG5D,SAAS,CAAC3B,SAAS,IAAI2B,SAAS,CAAC7B,KAAK,EAAE;EACvG;EACA;EACQpC,qBAAqBA,CAACyB,OAAe;IAC3C,KAAK,MAAMhC,QAAQ,IAAI,IAAI,CAACX,SAAS,EAAE;MACrC,MAAMmD,UAAU,GAAG,IAAI,CAAChE,YAAY,CAACwB,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMwE,SAAS,GAAGhC,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAIwC,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb,CAAC,CAAE;EACK6D,qBAAqBA,CAACxF,SAAiB;IAC7C,MAAMyF,kBAAkB,GAAqD,EAAE;IAE/E;IACA,KAAK,MAAMtI,QAAQ,IAAI,IAAI,CAACX,SAAS,EAAE;MACrC,MAAMmD,UAAU,GAAG,IAAI,CAAChE,YAAY,CAACwB,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMuI,OAAO,GAAG/F,UAAU,CAACtC,MAAM,CAACgC,CAAC,IAAIA,CAAC,CAACW,SAAS,KAAKA,SAAS,CAAC;MACjE0F,OAAO,CAACxG,OAAO,CAACyC,SAAS,IAAG;QAC1B8D,kBAAkB,CAACnG,IAAI,CAAC;UAAEnC,QAAQ;UAAEwE;QAAS,CAAE,CAAC;MAClD,CAAC,CAAC;IACJ;IAEA3D,OAAO,CAACC,GAAG,CAAC,iBAAiB+B,SAAS,QAAQ,EAAEyF,kBAAkB,CAAC;IAEnE,IAAIA,kBAAkB,CAACxI,MAAM,KAAK,CAAC,EAAE;MACnCe,OAAO,CAAC2H,IAAI,CAAC,kBAAkB3F,SAAS,SAAS,CAAC;MAClD,OAAO,IAAI;IACb;IAEA,IAAIyF,kBAAkB,CAACxI,MAAM,GAAG,CAAC,EAAE;MACjCe,OAAO,CAAC2H,IAAI,CAAC,aAAa3F,SAAS,IAAI,EAAEyF,kBAAkB,CAACvI,GAAG,CAAC0I,CAAC,IAAI,GAAGA,CAAC,CAACzI,QAAQ,IAAIyI,CAAC,CAACjE,SAAS,CAAC7B,KAAK,EAAE,CAAC,CAAC;MAC3G9B,OAAO,CAAC2H,IAAI,CAAC,cAAcF,kBAAkB,CAAC,CAAC,CAAC,CAACtI,QAAQ,IAAIsI,kBAAkB,CAAC,CAAC,CAAC,CAAC9D,SAAS,CAAC7B,KAAK,EAAE,CAAC;IACvG;IAEA,MAAM+F,UAAU,GAAGJ,kBAAkB,CAAC,CAAC,CAAC;IACxC,OAAOI,UAAU,CAAClE,SAAS,CAACxC,OAAO,IAAI,IAAI;EAC7C;EAEA;EACQyB,yBAAyBA,CAACZ,SAAiB;IACjD,MAAM4C,QAAQ,GAAa,EAAE;IAE7B;IACA,KAAK,MAAMzF,QAAQ,IAAI,IAAI,CAACX,SAAS,EAAE;MACrC,MAAMmD,UAAU,GAAG,IAAI,CAAChE,YAAY,CAACwB,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMuI,OAAO,GAAG/F,UAAU,CAACtC,MAAM,CAACgC,CAAC,IAAIA,CAAC,CAACW,SAAS,KAAKA,SAAS,CAAC;MACjE0F,OAAO,CAACxG,OAAO,CAACyC,SAAS,IAAG;QAC1B,IAAIA,SAAS,CAACxC,OAAO,EAAE;UACrByD,QAAQ,CAACtD,IAAI,CAACqC,SAAS,CAACxC,OAAO,CAAC;QAClC;MACF,CAAC,CAAC;IACJ;IAEA,OAAOyD,QAAQ;EACjB;EAEA;EACQ5B,iBAAiBA,CAAC7B,OAAe;IACvC,OAAO,IAAI,CAAC7C,gBAAgB,CAAC4D,QAAQ,CAACf,OAAO,CAAC;EAChD;EAEA;EACQqB,iBAAiBA,CAACrB,OAAe;IACvC,OAAO,IAAI,CAACtD,gBAAgB,CAACqE,QAAQ,CAACf,OAAO,CAAC;EAChD;EACA;EACA2G,wBAAwBA,CAACC,QAAgB;IACvC,KAAK,MAAM5I,QAAQ,IAAI,IAAI,CAACX,SAAS,EAAE;MACrC,MAAMmD,UAAU,GAAG,IAAI,CAAChE,YAAY,CAACwB,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMwE,SAAS,GAAGhC,UAAU,CAACP,IAAI,CAACC,CAAC,IAAI,IAAI,CAACiG,oBAAoB,CAACjG,CAAC,CAAC,KAAK0G,QAAQ,CAAC;MACjF,IAAIpE,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb,CAAC,CAAE;EACK5D,sBAAsBA,CAACiI,UAAoB;IACjD,MAAMpD,QAAQ,GAAa,EAAE;IAC7B,MAAMF,gBAAgB,GAAG,CAAC,GAAG,IAAI5E,GAAG,CAACkI,UAAU,CAAC,CAAC,CAAC,CAAC;IAEnD,KAAK,MAAMhG,SAAS,IAAI0C,gBAAgB,EAAE;MACxC,MAAMuD,gBAAgB,GAAG,IAAI,CAACrF,yBAAyB,CAACZ,SAAS,CAAC;MAClE,IAAIiG,gBAAgB,CAAChJ,MAAM,GAAG,CAAC,EAAE;QAC/B,IAAI,IAAI,CAACnB,gBAAgB,EAAE;UACzB;UACA8G,QAAQ,CAACtD,IAAI,CAAC2G,gBAAgB,CAAC,CAAC,CAAC,CAAC;UAClC,IAAIA,gBAAgB,CAAChJ,MAAM,GAAG,CAAC,EAAE;YAC/Be,OAAO,CAACC,GAAG,CAAC,SAAS+B,SAAS,OAAOiG,gBAAgB,CAAChJ,MAAM,oCAAoC,EAAEgJ,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACxH;QACF,CAAC,MAAM;UACL;UACArD,QAAQ,CAACtD,IAAI,CAAC,GAAG2G,gBAAgB,CAAC;UAClC,IAAIA,gBAAgB,CAAChJ,MAAM,GAAG,CAAC,EAAE;YAC/Be,OAAO,CAAC2H,IAAI,CAAC,SAAS3F,SAAS,OAAOiG,gBAAgB,CAAChJ,MAAM,iBAAiB,EAAEgJ,gBAAgB,CAAC;UACnG;QACF;MACF,CAAC,MAAM;QACLjI,OAAO,CAAC2H,IAAI,CAAC,aAAa3F,SAAS,eAAe,CAAC;MACrD;IACF;IAEA;IACA,OAAO,CAAC,GAAG,IAAIlC,GAAG,CAAC8E,QAAQ,CAAC,CAAC;EAC/B;EACA;EACQzE,sBAAsBA,CAACyE,QAAkB;IAC/C,MAAMoD,UAAU,GAAa,EAAE;IAC/B,MAAME,cAAc,GAAG,CAAC,GAAG,IAAIpI,GAAG,CAAC8E,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE/C,KAAK,MAAMzD,OAAO,IAAI+G,cAAc,EAAE;MACpC,MAAMC,aAAa,GAAG,IAAI,CAACjB,oBAAoB,CAAC/F,OAAO,CAAC;MACxD,IAAIgH,aAAa,CAACnG,SAAS,IAAI,CAACmG,aAAa,CAACnG,SAAS,CAACoG,UAAU,CAAC,KAAK,CAAC,EAAE;QACzEJ,UAAU,CAAC1G,IAAI,CAAC6G,aAAa,CAACnG,SAAS,CAAC;MAC1C,CAAC,MAAM;QACLhC,OAAO,CAAC2H,IAAI,CAAC,gBAAgBxG,OAAO,UAAU,CAAC;MACjD;IACF;IAEA;IACA,OAAO,CAAC,GAAG,IAAIrB,GAAG,CAACkI,UAAU,CAAC,CAAC;EACjC;EAEA;EACA3E,6BAA6BA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAAClF,gBAAgB,IAAI,CAAC,IAAI,CAACR,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,EAAE;MACvE,OAAO,EAAE;IACX;IAEA,MAAMwD,UAAU,GAAG,IAAI,CAAChE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IAEjE,IAAI,CAAC,IAAI,CAACL,gBAAgB,EAAE;MAC1B;MACA,OAAO6D,UAAU,CAACtC,MAAM,CAACgC,CAAC,IAAG;QAC3B,MAAMQ,UAAU,GAAG,CAAC,IAAI,CAACxD,aAAa,IAAIgD,CAAC,CAACS,KAAK,KAAK,IAAI,CAACzD,aAAa;QACxE,MAAM0D,WAAW,GAAG,CAAC,IAAI,CAAC3D,UAAU,IAAIiD,CAAC,CAACW,SAAS,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC9D,UAAU,CAAC6D,WAAW,EAAE,CAAC;QACzG,OAAOJ,UAAU,IAAIE,WAAW;MAClC,CAAC,CAAC;IACJ;IAEA;IACA,MAAM2C,gBAAgB,GAAG,IAAI5E,GAAG,EAAU;IAC1C,MAAMuI,gBAAgB,GAAoB,EAAE;IAE5C,KAAK,MAAM1E,SAAS,IAAIhC,UAAU,EAAE;MAClC;MACA,MAAMI,WAAW,GAAG,CAAC,IAAI,CAAC3D,UAAU,IAAIuF,SAAS,CAAC3B,SAAS,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC9D,UAAU,CAAC6D,WAAW,EAAE,CAAC;MAEjH,IAAIF,WAAW,IAAI,CAAC2C,gBAAgB,CAACP,GAAG,CAACR,SAAS,CAAC3B,SAAS,CAAC,EAAE;QAC7D0C,gBAAgB,CAACN,GAAG,CAACT,SAAS,CAAC3B,SAAS,CAAC;QACzCqG,gBAAgB,CAAC/G,IAAI,CAACqC,SAAS,CAAC;MAClC;IACF;IACA,OAAO0E,gBAAgB;EACzB;EAEA;EACA,IAAIC,WAAWA,CAAA;IACb,OAAO;MACLC,QAAQ,EAAE,IAAI,CAACzK,gBAAgB,GAAG,IAAI,GAAG,IAAI;MAC7CP,WAAW,EAAE,IAAI,CAACO,gBAAgB,GAAG,OAAO,GAAG,OAAO;MACtD0K,cAAc,EAAE,IAAI,CAAC1K,gBAAgB,GAAG,OAAO,GAAG,OAAO;MACzD2K,UAAU,EAAE,IAAI,CAAC3K,gBAAgB,GAAG,MAAM,GAAG,MAAM;MACnD4K,aAAa,EAAE,IAAI,CAAC5K,gBAAgB,GAAG,MAAM,GAAG,MAAM;MACtD6K,aAAa,EAAE,IAAI,CAAC7K,gBAAgB,GAAG,KAAK,GAAG,IAAI;MACnD8K,iBAAiB,EAAE,IAAI,CAAC9K,gBAAgB,GAAG,SAAS,GAAG,SAAS;MAChE+K,SAAS,EAAE,IAAI,CAAC/K,gBAAgB,GAAG,UAAU,GAAG,UAAU;MAC1DgL,WAAW,EAAE,IAAI,CAAChL,gBAAgB,GAAG,aAAa,GAAG;KACtD;EACH;CACD;AA9wBkDiL,UAAA,EAAhD9L,SAAS,CAAC,iBAAiB,EAAE;EAAE+L,MAAM,EAAE;AAAK,CAAE,CAAC,C,iEAAoC;AAC3ED,UAAA,EAARlM,KAAK,EAAE,C,6DAA+B;AAC9BkM,UAAA,EAARlM,KAAK,EAAE,C,+DAAqC;AACpCkM,UAAA,EAARlM,KAAK,EAAE,C,0DAA2B;AAC1BkM,UAAA,EAARlM,KAAK,EAAE,C,6DAAmC;AAClCkM,UAAA,EAARlM,KAAK,EAAE,C,8DAAiC;AAChCkM,UAAA,EAARlM,KAAK,EAAE,C,kEAAkC;AACjCkM,UAAA,EAARlM,KAAK,EAAE,C,kEAAiC;AAChCkM,UAAA,EAARlM,KAAK,EAAE,C,kEAAmC;AAEjCkM,UAAA,EAATjM,MAAM,EAAE,C,iEAAuD;AACtDiM,UAAA,EAATjM,MAAM,EAAE,C,+DAA8C;AAC7CiM,UAAA,EAATjM,MAAM,EAAE,C,iEAAgD;AAb9CK,yBAAyB,GAAA4L,UAAA,EAlBrCnM,SAAS,CAAC;EACTqM,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPC,YAAY,EACZC,eAAe,EACfC,YAAY,CACb;EACDC,SAAS,EAAE,CACT;IACEC,OAAO,EAAExM,iBAAiB;IAC1ByM,WAAW,EAAE3M,UAAU,CAAC,MAAMG,yBAAyB,CAAC;IACxDyM,KAAK,EAAE;GACR;CAEJ,CAAC,C,EACWzM,yBAAyB,CA+wBrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}