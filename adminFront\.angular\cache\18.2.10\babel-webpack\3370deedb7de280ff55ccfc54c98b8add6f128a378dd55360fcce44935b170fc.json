{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r3, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_55_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.getActiveFiltersCount());\n  }\n}\nfunction SettingTimePeriodComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_55_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleAdvancedFilters());\n    });\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementStart(3, \"span\", 52);\n    i0.ɵɵtext(4, \"\\u9032\\u968E\\u7BE9\\u9078\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SettingTimePeriodComponent_div_55_span_5_Template, 2, 1, \"span\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fa-chevron-down\", !ctx_r4.showAdvancedFilters)(\"fa-chevron-up\", ctx_r4.showAdvancedFilters);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.hasActiveFilters());\n  }\n}\nfunction SettingTimePeriodComponent_div_56_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", floor_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", floor_r7, \"F \");\n  }\n}\nfunction SettingTimePeriodComponent_div_56_button_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_button_37_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearAllFilters());\n    });\n    i0.ɵɵelement(1, \"i\", 71);\n    i0.ɵɵtext(2, \"\\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"div\", 22)(3, \"nb-form-field\", 32);\n    i0.ɵɵelement(4, \"nb-icon\", 57);\n    i0.ɵɵelementStart(5, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.searchKeyword, $event) || (ctx_r4.filterOptions.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_input_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"nb-select\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.statusFilter, $event) || (ctx_r4.filterOptions.statusFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(8, \"nb-option\", 29);\n    i0.ɵɵtext(9, \"\\u5168\\u90E8\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-option\", 60);\n    i0.ɵɵtext(11, \"\\u9032\\u884C\\u4E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-option\", 61);\n    i0.ɵɵtext(13, \"\\u5F85\\u958B\\u653E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-option\", 62);\n    i0.ɵɵtext(15, \"\\u5DF2\\u904E\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-option\", 63);\n    i0.ɵɵtext(17, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-option\", 64);\n    i0.ɵɵtext(19, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 27)(21, \"nb-select\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.floorFilter, $event) || (ctx_r4.filterOptions.floorFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_21_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(22, \"nb-option\", 29);\n    i0.ɵɵtext(23, \"\\u5168\\u90E8\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SettingTimePeriodComponent_div_56_nb_option_24_Template, 2, 2, \"nb-option\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 66)(26, \"nb-select\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.pageSize, $event) || (ctx_r4.pageSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_26_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onPageSizeChange());\n    });\n    i0.ɵɵelementStart(27, \"nb-option\", 48);\n    i0.ɵɵtext(28, \"50\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-option\", 48);\n    i0.ɵɵtext(30, \"100\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"nb-option\", 48);\n    i0.ɵɵtext(32, \"200\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-option\", 48);\n    i0.ɵɵtext(34, \"500\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 38)(36, \"div\", 68);\n    i0.ɵɵtemplate(37, SettingTimePeriodComponent_div_56_button_37_Template, 3, 0, \"button\", 69);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@slideInOut\", undefined);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.statusFilter);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.floorFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.availableFloors);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 50);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 200);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 500);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.hasActiveFilters());\n  }\n}\nfunction SettingTimePeriodComponent_div_57_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.selectedHouses.length, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_tr_41_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r11.CChangeStartDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_tr_41_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 103);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_57_tr_41_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r11.CChangeEndDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_tr_41_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 103);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_57_tr_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_tr_41_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const house_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r11.selected, $event) || (house_r11.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_tr_41_Template_nb_checkbox_ngModelChange_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtemplate(10, SettingTimePeriodComponent_div_57_tr_41_span_10_Template, 3, 4, \"span\", 97)(11, SettingTimePeriodComponent_div_57_tr_41_span_11_Template, 2, 0, \"span\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtemplate(13, SettingTimePeriodComponent_div_57_tr_41_span_13_Template, 3, 4, \"span\", 97)(14, SettingTimePeriodComponent_div_57_tr_41_span_14_Template, 2, 0, \"span\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\")(16, \"span\", 99);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_tr_41_Template_button_click_19_listener() {\n      const house_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const dialog_r12 = i0.ɵɵreference(65);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r12, house_r11));\n    });\n    i0.ɵɵelement(20, \"i\", 101);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const house_r11 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"table-row-selected\", house_r11.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r11.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r11.CHouseId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r11.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r11.CBuildingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", house_r11.CFloor, \"F\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", house_r11.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r11.CChangeStartDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", house_r11.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r11.CChangeEndDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r11));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getStatusText(house_r11), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !house_r11.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_57_div_42_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 106)(1, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_42_li_9_Template_button_click_1_listener() {\n      const page_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(page_r15));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r15 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r15 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r15);\n  }\n}\nfunction SettingTimePeriodComponent_div_57_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 104)(1, \"nav\")(2, \"ul\", 105)(3, \"li\", 106)(4, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_42_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(1));\n    });\n    i0.ɵɵtext(5, \"\\u9996\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 106)(7, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_42_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage - 1));\n    });\n    i0.ɵɵtext(8, \"\\u4E0A\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, SettingTimePeriodComponent_div_57_div_42_li_9_Template, 3, 3, \"li\", 108);\n    i0.ɵɵelementStart(10, \"li\", 106)(11, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_42_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage + 1));\n    });\n    i0.ɵɵtext(12, \"\\u4E0B\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\", 106)(14, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_div_42_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.totalPages));\n    });\n    i0.ɵɵtext(15, \"\\u672B\\u9801\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"div\", 74)(3, \"div\", 75)(4, \"nb-checkbox\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵtext(5, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openBatchSetting());\n    });\n    i0.ɵɵelement(7, \"i\", 78);\n    i0.ɵɵtext(8, \"\\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(9, SettingTimePeriodComponent_div_57_span_9_Template, 2, 1, \"span\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.exportData());\n    });\n    i0.ɵɵelement(11, \"i\", 81);\n    i0.ɵɵtext(12, \" \\u532F\\u51FA \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 82);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 83)(16, \"table\", 84)(17, \"thead\", 85)(18, \"tr\")(19, \"th\", 86)(20, \"nb-checkbox\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_20_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"th\", 87);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_th_click_21_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CHouseHold\"));\n    });\n    i0.ɵɵtext(22, \" \\u6236\\u578B \");\n    i0.ɵɵelement(23, \"i\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 87);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_th_click_24_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CBuildingName\"));\n    });\n    i0.ɵɵtext(25, \" \\u68DF\\u5225 \");\n    i0.ɵɵelement(26, \"i\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"th\", 89);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_th_click_27_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CFloor\"));\n    });\n    i0.ɵɵtext(28, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelement(29, \"i\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\", 90);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_th_click_30_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeStartDate\"));\n    });\n    i0.ɵɵtext(31, \" \\u958B\\u59CB\\u65E5\\u671F \");\n    i0.ɵɵelement(32, \"i\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\", 90);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_th_click_33_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeEndDate\"));\n    });\n    i0.ɵɵtext(34, \" \\u7D50\\u675F\\u65E5\\u671F \");\n    i0.ɵɵelement(35, \"i\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"th\", 91);\n    i0.ɵɵtext(37, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"th\", 92);\n    i0.ɵɵtext(39, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"tbody\");\n    i0.ɵɵtemplate(41, SettingTimePeriodComponent_div_57_tr_41_Template, 21, 15, \"tr\", 93);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(42, SettingTimePeriodComponent_div_57_div_42_Template, 16, 13, \"div\", 94);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.filteredHouses.length === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" / \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46 \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.paginatedHouses)(\"ngForTrackBy\", ctx_r4.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 103);\n    i0.ɵɵelement(4, \"i\", 111);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\\u5F8C\\u67E5\\u8A62\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 103)(4, \"div\", 112)(5, \"span\", 113);\n    i0.ɵɵtext(6, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 114);\n    i0.ɵɵtext(8, \"\\u8CC7\\u6599\\u8F09\\u5165\\u4E2D\\uFF0C\\u8ACB\\u7A0D\\u5019...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 129);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \\u5DF2\\u9078\\u64C7 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225 \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r4.selectedBuildingForBatch.name, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 133);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", house_r17.CHouseHold, \" (\", house_r17.CBuildingName, \"-\", house_r17.CFloor, \"F) \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_28_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 103);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" ...\\u7B49 \", ctx_r4.selectedHouses.length - 10, \" \\u500B \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 131);\n    i0.ɵɵtemplate(4, SettingTimePeriodComponent_ng_template_60_div_28_span_4_Template, 2, 3, \"span\", 132)(5, SettingTimePeriodComponent_ng_template_60_div_28_span_5_Template, 2, 1, \"span\", 98);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5C07\\u5957\\u7528\\u5230\\u5DF2\\u9078\\u64C7\\u7684 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedHouses.slice(0, 10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 10);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      const house_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r22.selected, $event) || (house_r22.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r22 = ctx.$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r22.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r22.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r22.CHouseHold, \" (\", house_r22.CBuildingName, \") \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 138);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_nb_checkbox_1_Template, 2, 4, \"nb-checkbox\", 139);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", floor_r20.houses);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 136)(1, \"nb-checkbox\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const floor_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(floor_r20.selected, $event) || (floor_r20.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      const floor_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.onFloorSelectionChange(floor_r20));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_Template, 2, 1, \"div\", 137);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", floor_r20.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r20.floorNumber, \"F (\", floor_r20.houses.length, \" \\u6236) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", floor_r20.selected);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_29_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template, 4, 4, \"div\", 135);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedBuildingForBatch.floors);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"nb-checkbox\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_div_29_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.applyToAll, $event) || (ctx_r4.batchSettings.applyToAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_60_div_29_div_3_Template, 2, 1, \"div\", 134);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.applyToAll);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u90E8\\u6236\\u5225 (\", ctx_r4.flattenedHouses.length, \" \\u500B) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.batchSettings.applyToAll && ctx_r4.selectedBuildingForBatch);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225)\");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 115)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_60_span_3_Template, 2, 1, \"span\", 116)(4, SettingTimePeriodComponent_ng_template_60_span_4_Template, 2, 1, \"span\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"div\", 118)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n    i0.ɵɵelementStart(9, \"span\", 119);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 120)(12, \"nb-form-field\", 121);\n    i0.ɵɵelement(13, \"nb-icon\", 33);\n    i0.ɵɵelementStart(14, \"input\", 122);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.startDate, $event) || (ctx_r4.batchSettings.startDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 35, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 123);\n    i0.ɵɵtext(18, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\", 121);\n    i0.ɵɵelement(20, \"nb-icon\", 33);\n    i0.ɵɵelementStart(21, \"input\", 122);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_60_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.endDate, $event) || (ctx_r4.batchSettings.endDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 35, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 118)(25, \"label\");\n    i0.ɵɵtext(26, \"\\u9069\\u7528\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 124);\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_ng_template_60_div_28_Template, 6, 3, \"div\", 125)(29, SettingTimePeriodComponent_ng_template_60_div_29_Template, 4, 3, \"div\", 117);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-card-footer\", 126)(31, \"button\", 127);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_60_Template_button_click_31_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r23));\n    });\n    i0.ɵɵtext(32, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_60_Template_button_click_33_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onBatchSubmit(ref_r23));\n    });\n    i0.ɵɵtext(34, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(35, SettingTimePeriodComponent_ng_template_60_span_35_Template, 2, 1, \"span\", 117);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const batchStartDate_r24 = i0.ɵɵreference(16);\n    const batchEndDate_r25 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedBuildingForBatch && ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", batchStartDate_r24);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.startDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", batchEndDate_r25);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.endDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 140);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 141);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 140)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 142);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 143)(7, \"div\", 144)(8, \"label\", 145);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 119);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 146);\n    i0.ɵɵelement(13, \"nb-icon\", 33);\n    i0.ɵɵelementStart(14, \"input\", 147);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_64_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 35, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 148);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 33);\n    i0.ɵɵelementStart(21, \"input\", 149);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_64_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 35, 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 141)(25, \"button\", 150);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_64_Template_button_click_25_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r26).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r27));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 151);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_64_Template_button_click_27_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r26).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSubmit(ref_r27));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r28 = i0.ɵɵreference(16);\n    const changeEndDate_r29 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r4.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r28);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r29);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    // 新增的屬性\n    this.buildingGroups = [];\n    this.buildingOptions = [];\n    this.selectedBuilding = '';\n    this.availableFloors = [];\n    // 篩選和搜尋\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 批次設定\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: true,\n      selectedBuildings: [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    this.selectedBuildingForBatch = null;\n    // 表格視圖相關屬性\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.loading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalPages = 1;\n    // 排序相關\n    this.sortField = '';\n    this.sortDirection = 'asc';\n    // 數學函數引用\n    this.Math = Math;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CBuildingName: '未分類',\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  // 新增：建案變更處理\n  onBuildCaseChange() {\n    // 重置所有相關狀態\n    this.resetAllStates();\n    // 執行查詢\n    this.getHouseChangeDate();\n  }\n  // 新增：重置所有狀態\n  resetAllStates() {\n    // 重置數據\n    this.houseChangeDates = [];\n    this.convertedHouseArray = [];\n    this.buildingGroups = [];\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    // 重置篩選條件\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 重置選擇狀態\n    this.selectAll = false;\n    this.selectedBuilding = '';\n    // 重置分頁\n    this.currentPage = 1;\n    this.totalPages = 1;\n    // 重置可用選項\n    this.buildingOptions = [];\n    this.availableFloors = [];\n    // 重置排序\n    this.sortField = '';\n    this.sortDirection = 'asc';\n  }\n  getHouseChangeDate() {\n    // 如果沒有選擇建案，直接返回\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\n      this.loading = false;\n      return;\n    }\n    this.loading = true;\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      this.loading = false;\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          // 新增：建立棟別分組資料\n          this.buildBuildingGroups(res.Entries);\n          // 新增：建立扁平化資料\n          this.buildFlattenedHouses(res.Entries);\n        }\n      }\n    });\n  }\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n  buildBuildingGroups(data) {\n    const buildingMap = new Map();\n    data.forEach(household => {\n      const houseType = household.CHouseHold || ''; // 戶型\n      household.CHouses?.forEach(house => {\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\n        const floor = house.CFloor || 0;\n        if (!buildingMap.has(buildingName)) {\n          buildingMap.set(buildingName, new Map());\n        }\n        const floorMap = buildingMap.get(buildingName);\n        if (!floorMap.has(floor)) {\n          floorMap.set(floor, []);\n        }\n        floorMap.get(floor).push({\n          CHouseHold: houseType,\n          // 戶型\n          CBuildingName: buildingName,\n          // 棟別\n          CHouseId: house.CHouseId || 0,\n          CFloor: floor,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 轉換為BuildingGroup格式\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n      const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n      .map(([floorNumber, houses]) => ({\n        floorNumber,\n        houses: houses.sort((a, b) => {\n          // 排序邏輯：先按戶型排序，再按樓層排序\n          if (a.CHouseHold !== b.CHouseHold) {\n            return a.CHouseHold.localeCompare(b.CHouseHold);\n          }\n          return a.CFloor - b.CFloor;\n        }),\n        selected: false\n      }));\n      return {\n        name: buildingName,\n        floors,\n        selected: false\n      };\n    }).sort((a, b) => a.name.localeCompare(b.name));\n    // 更新棟別選項和可用樓層\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n    this.updateAvailableFloors();\n  }\n  // 新增：更新可用樓層\n  updateAvailableFloors() {\n    const floorsSet = new Set();\n    this.buildingGroups.forEach(building => {\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n        building.floors.forEach(floor => {\n          floorsSet.add(floor.floorNumber);\n        });\n      }\n    });\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n  }\n  // 修改：棟別選擇變更處理\n  onBuildingChange() {\n    // 重置選擇狀態\n    this.selectedHouses.forEach(house => house.selected = false);\n    this.selectedHouses = [];\n    this.selectAll = false;\n    // 重置分頁到第一頁\n    this.currentPage = 1;\n    // 重置樓層篩選\n    this.filterOptions.floorFilter = '';\n    // 更新可用樓層\n    this.updateAvailableFloors();\n    // 設定棟別篩選並執行搜尋\n    this.filterOptions.buildingFilter = this.selectedBuilding;\n    this.onSearch();\n  }\n  // 新增：取得過濾後的棟別資料\n  getFilteredBuildings() {\n    return this.buildingGroups.filter(building => {\n      // 棟別篩選\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n        return false;\n      }\n      // 關鍵字搜尋 (搜尋戶型)\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n        if (!hasMatchingHouse) {\n          return false;\n        }\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n        if (!hasMatchingStatus) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n        if (!hasMatchingFloor) {\n          return false;\n        }\n      }\n      return true;\n    }).map(building => {\n      // 對每個棟別，也要篩選其樓層和戶別\n      const filteredBuilding = {\n        ...building\n      };\n      filteredBuilding.floors = building.floors.filter(floor => {\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (floor.floorNumber !== floorNumber) {\n            return false;\n          }\n        }\n        // 檢查該樓層是否有符合條件的戶別\n        const hasValidHouses = floor.houses.some(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return hasValidHouses;\n      }).map(floor => {\n        // 篩選戶別\n        const filteredFloor = {\n          ...floor\n        };\n        filteredFloor.houses = floor.houses.filter(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return filteredFloor;\n      });\n      return filteredBuilding;\n    });\n  }\n  // 修復：檢查戶別是否符合狀態篩選\n  matchesStatusFilter(house) {\n    const status = this.getHouseStatus(house);\n    switch (this.filterOptions.statusFilter) {\n      case 'active':\n        return status === 'active';\n      case 'pending':\n        return status === 'pending';\n      case 'expired':\n        return status === 'expired';\n      case 'not-set':\n        return status === 'not-set';\n      case 'disabled':\n        return status === 'disabled';\n      default:\n        return true;\n      // 全部狀態\n    }\n  }\n  // 修復：取得戶別狀態\n  getHouseStatus(house) {\n    if (!house.CHouseId) {\n      return 'disabled';\n    }\n    // 檢查是否有設定開放時段\n    if (!house.CChangeStartDate || !house.CChangeEndDate || house.CChangeStartDate === '' || house.CChangeEndDate === '') {\n      return 'not-set';\n    }\n    try {\n      // 處理日期字串，支援多種格式\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      // 解析開始日期\n      let startDate;\n      if (house.CChangeStartDate.includes('T')) {\n        startDate = new Date(house.CChangeStartDate);\n      } else {\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\n      }\n      // 解析結束日期\n      let endDate;\n      if (house.CChangeEndDate.includes('T')) {\n        endDate = new Date(house.CChangeEndDate);\n      } else {\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\n      }\n      // 檢查日期有效性\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\n        console.warn('Invalid date format:', {\n          start: house.CChangeStartDate,\n          end: house.CChangeEndDate,\n          houseId: house.CHouseId\n        });\n        return 'not-set';\n      }\n      // 轉換為日期比較（不含時間）\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n      // 判斷狀態\n      if (today < startDateOnly) {\n        return 'pending'; // 待開放\n      } else if (today >= startDateOnly && today <= endDateOnly) {\n        return 'active'; // 進行中\n      } else {\n        return 'expired'; // 已過期\n      }\n    } catch (error) {\n      console.error('Error parsing dates:', error, house);\n      return 'not-set';\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  // 修改：開啟批次設定對話框\n  openBatchSetting(building) {\n    this.selectedBuildingForBatch = building || null;\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\n    const hasSelectedHouses = this.selectedHouses.length > 0;\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: !hasSelectedHouses && !building,\n      selectedBuildings: building ? [building.name] : [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    // 重置選擇狀態\n    if (building) {\n      building.floors.forEach(floor => {\n        floor.selected = false;\n        floor.houses.forEach(house => house.selected = false);\n      });\n    }\n    // 開啟對話框\n    this.dialogService.open(this.batchSettingDialog);\n  }\n  // 新增：樓層選擇變更處理\n  onFloorSelectionChange(floor) {\n    if (floor.selected) {\n      floor.houses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = true;\n        }\n      });\n    } else {\n      floor.houses.forEach(house => house.selected = false);\n    }\n  }\n  // 修改：批次提交\n  onBatchSubmit(ref) {\n    // 驗證批次設定\n    this.valid.clear();\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 收集要更新的房屋\n    const housesToUpdate = [];\n    if (this.batchSettings.applyToAll) {\n      // 全部戶別\n      this.flattenedHouses.forEach(house => {\n        if (house.CHouseId) {\n          housesToUpdate.push({\n            CHouseId: house.CHouseId,\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n          });\n        }\n      });\n    } else {\n      // 使用已選擇的戶別\n      if (this.selectedHouses.length > 0) {\n        this.selectedHouses.forEach(house => {\n          if (house.CHouseId) {\n            housesToUpdate.push({\n              CHouseId: house.CHouseId,\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n            });\n          }\n        });\n      } else if (this.selectedBuildingForBatch) {\n        // 如果沒有選擇的戶別，使用舊的邏輯\n        this.selectedBuildingForBatch.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.selected && house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      }\n    }\n    if (housesToUpdate.length === 0) {\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\n      return;\n    }\n    // 調用API進行批次更新\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: housesToUpdate\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n        // 清除選擇狀態\n        this.selectedHouses.forEach(house => house.selected = false);\n        this.selectedHouses = [];\n        this.selectAll = false;\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  // 新增：取得狀態樣式類別\n  getStatusClass(house) {\n    const status = this.getHouseStatus(house);\n    return `status-${status}`;\n  }\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n  openHouseDialog(house) {\n    if (house.CHouseId) {\n      // 使用現有的openModel方法\n      this.openModel(this.dialog, house);\n    }\n  }\n  // 新增：建立扁平化房屋資料\n  buildFlattenedHouses(data) {\n    this.flattenedHouses = [];\n    data.forEach(household => {\n      const houseType = household.CHouseHold || '';\n      household.CHouses?.forEach(house => {\n        this.flattenedHouses.push({\n          CHouseHold: houseType,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId || 0,\n          CFloor: house.CFloor || 0,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 初始化篩選和分頁\n    this.onSearch();\n    // 調試：輸出狀態統計\n    this.debugStatusCounts();\n  }\n  // 調試：輸出狀態統計\n  debugStatusCounts() {\n    const statusCounts = {\n      active: 0,\n      pending: 0,\n      expired: 0,\n      'not-set': 0,\n      disabled: 0\n    };\n    this.flattenedHouses.forEach(house => {\n      const status = this.getHouseStatus(house);\n      if (statusCounts.hasOwnProperty(status)) {\n        statusCounts[status]++;\n      }\n    });\n    console.log('狀態統計:', statusCounts);\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\n  }\n  // 修改：搜尋和篩選\n  onSearch() {\n    // 記錄篩選前的已選擇項目\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\n    this.filteredHouses = this.flattenedHouses.filter(house => {\n      // 關鍵字搜尋\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n          return false;\n        }\n      }\n      // 棟別篩選\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n        return false;\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        if (!this.matchesStatusFilter(house)) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        if (house.CFloor !== floorNumber) {\n          return false;\n        }\n      }\n      return true;\n    });\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\n    this.selectedHouses = this.filteredHouses.filter(house => previouslySelectedIds.includes(house.CHouseId));\n    // 更新扁平化資料中的選擇狀態\n    this.flattenedHouses.forEach(house => {\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\n    });\n    // 更新全選狀態\n    this.updateSelectAllState();\n    // 重新計算分頁\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：更新全選狀態\n  updateSelectAllState() {\n    if (this.paginatedHouses.length === 0) {\n      this.selectAll = false;\n    } else {\n      this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n    }\n  }\n  // 修改：更新分頁\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n    // 更新全選狀態\n    this.updateSelectAllState();\n  }\n  // 新增：頁面大小變更\n  onPageSizeChange() {\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：跳轉頁面\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.updatePagination();\n    }\n  }\n  // 新增：取得可見頁碼\n  getVisiblePages() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 新增：全選/取消全選\n  onSelectAllChange() {\n    this.paginatedHouses.forEach(house => {\n      if (house.CHouseId) {\n        house.selected = this.selectAll;\n      }\n    });\n    this.updateSelectedHouses();\n  }\n  // 新增：單一選擇變更\n  onHouseSelectionChange() {\n    this.updateSelectedHouses();\n    this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n  }\n  // 新增：更新已選擇房屋列表\n  updateSelectedHouses() {\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n  }\n  // 新增：排序\n  sort(field) {\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    this.filteredHouses.sort((a, b) => {\n      let aValue = a[field];\n      let bValue = b[field];\n      // 處理日期排序\n      if (field.includes('Date')) {\n        aValue = aValue ? new Date(aValue).getTime() : 0;\n        bValue = bValue ? new Date(bValue).getTime() : 0;\n      }\n      // 處理數字排序\n      if (field === 'CFloor') {\n        aValue = Number(aValue) || 0;\n        bValue = Number(bValue) || 0;\n      }\n      // 處理字串排序\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (aValue < bValue) {\n        return this.sortDirection === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return this.sortDirection === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    this.updatePagination();\n  }\n  // 新增：TrackBy函數\n  trackByHouseId(_index, house) {\n    return house.CHouseId;\n  }\n  // 新增：取得狀態文字\n  getStatusText(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return '進行中';\n      case 'pending':\n        return '待開放';\n      case 'expired':\n        return '已過期';\n      case 'not-set':\n        return '未設定';\n      case 'disabled':\n        return '已停用';\n      default:\n        return '未知';\n    }\n  }\n  // 新增：匯出資料\n  exportData() {\n    // 實現匯出功能\n    const csvContent = this.generateCSV();\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  // 新增：產生CSV內容\n  generateCSV() {\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\n    const rows = this.filteredHouses.map(house => [house.CHouseHold, house.CBuildingName, `${house.CFloor}F`, house.CChangeStartDate || '未設定', house.CChangeEndDate || '未設定', this.getStatusText(house)]);\n    const csvContent = [headers, ...rows].map(row => row.map(cell => `\"${cell}\"`).join(',')).join('\\n');\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\n  }\n  static {\n    this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingTimePeriodComponent,\n      selectors: [[\"ngx-setting-time-period\"]],\n      viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 66,\n      vars: 15,\n      consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"batchSettingDialog\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"batchStartDate\", \"\"], [\"batchEndDate\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"page-header-optimized\"], [1, \"page-title-section\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"title-group\"], [1, \"page-title\", \"mb-1\"], [1, \"fas\", \"fa-clock\", \"me-2\"], [1, \"page-subtitle\", \"text-muted\", \"mb-0\"], [1, \"help-section\"], [\"type\", \"button\", \"data-bs-toggle\", \"tooltip\", \"title\", \"\\u8A2D\\u5B9A\\u5404\\u6236\\u5225\\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5\\uFF0C\\u63A7\\u5236\\u5BA2\\u6236\\u9078\\u6A23\\u6B0A\\u9650\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\"], [1, \"fas\", \"fa-question-circle\"], [1, \"compact-filters\"], [1, \"row\", \"g-3\", \"align-items-end\"], [1, \"col-lg-3\", \"col-md-4\"], [1, \"form-label\", \"small\", \"fw-medium\"], [1, \"text-danger\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-2\", \"col-md-3\"], [\"placeholder\", \"\\u5168\\u90E8\\u68DF\\u5225\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"\"], [1, \"col-lg-4\", \"col-md-5\"], [1, \"date-range-group\"], [\"size\", \"small\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u958B\\u59CB\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [1, \"date-separator\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u7D50\\u675F\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-lg-3\", \"col-md-12\"], [1, \"action-buttons\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-search\", \"me-1\"], [\"title\", \"\\u91CD\\u7F6E\\u7BE9\\u9078\\u689D\\u4EF6\", 1, \"btn\", \"btn-outline-secondary\", \"ms-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-undo\"], [\"class\", \"advanced-filters-toggle\", 4, \"ngIf\"], [\"class\", \"advanced-filters-panel\", 4, \"ngIf\"], [\"class\", \"table-view mt-4\", 4, \"ngIf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [3, \"value\"], [1, \"advanced-filters-toggle\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\", \"btn-sm\", \"p-0\", 3, \"click\"], [1, \"fas\"], [1, \"ms-1\"], [\"class\", \"badge bg-primary ms-2\", 4, \"ngIf\"], [1, \"badge\", \"bg-primary\", \"ms-2\"], [1, \"advanced-filters-panel\"], [1, \"row\", \"g-3\", \"align-items-center\"], [\"nbPrefix\", \"\", \"icon\", \"search-outline\"], [\"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u578B\\u3001\\u68DF\\u5225...\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u72C0\\u614B\\u7BE9\\u9078\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"active\"], [\"value\", \"pending\"], [\"value\", \"expired\"], [\"value\", \"not-set\"], [\"value\", \"disabled\"], [\"placeholder\", \"\\u6A13\\u5C64\\u7BE9\\u9078\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-lg-2\", \"col-md-2\"], [\"placeholder\", \"\\u986F\\u793A\\u7B46\\u6578\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"filter-actions\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"table-view\", \"mt-4\"], [1, \"table-toolbar\", \"mb-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"batch-actions\"], [3, \"ngModelChange\", \"ngModel\"], [\"title\", \"\\u6279\\u6B21\\u8A2D\\u5B9A\\u9078\\u4E2D\\u7684\\u6236\\u5225\\u958B\\u653E\\u6642\\u6BB5\", 1, \"btn\", \"btn-sm\", \"btn-warning\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-cogs\", \"me-1\"], [\"class\", \"badge badge-light ml-1\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-success\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\"], [1, \"pagination-info\"], [1, \"table-container\"], [1, \"table\", \"table-hover\"], [1, \"table-header\"], [\"width\", \"50\"], [\"width\", \"100\", 1, \"sortable\", 3, \"click\"], [1, \"fas\", \"fa-sort\"], [\"width\", \"80\", 1, \"sortable\", 3, \"click\"], [\"width\", \"120\", 1, \"sortable\", 3, \"click\"], [\"width\", \"100\"], [\"width\", \"80\"], [3, \"table-row-selected\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"pagination-container mt-3\", 4, \"ngIf\"], [1, \"badge\", \"badge-light\", \"ml-1\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"date-display\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"status-badge\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-edit\"], [1, \"date-display\"], [1, \"text-muted\"], [1, \"pagination-container\", \"mt-3\"], [1, \"pagination\", \"justify-content-center\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"mb-3\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"mt-2\"], [2, \"width\", \"600px\", \"max-height\", \"90vh\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"form-group\"], [1, \"text-red-600\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-fill\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"mx-2\"], [1, \"selection-options\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"text-primary\"], [1, \"alert\", \"alert-info\"], [1, \"selected-houses-preview\"], [\"class\", \"badge badge-primary mr-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-primary\", \"mr-1\", \"mb-1\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"floor-selection\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-selection\"], [\"class\", \"house-selection ml-4\", 4, \"ngIf\"], [1, \"house-selection\", \"ml-4\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n      template: function SettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 9)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 10)(5, \"div\", 11)(6, \"div\", 12)(7, \"div\", 13)(8, \"h5\", 14);\n          i0.ɵɵelement(9, \"i\", 15);\n          i0.ɵɵtext(10, \"\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5\\u8A2D\\u5B9A \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 16);\n          i0.ɵɵtext(12, \"\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u9593\\u7BC4\\u570D\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 17)(14, \"button\", 18);\n          i0.ɵɵelement(15, \"i\", 19);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(16, \"div\", 20)(17, \"div\", 21)(18, \"div\", 22)(19, \"label\", 23);\n          i0.ɵɵtext(20, \"\\u5EFA\\u6848 \");\n          i0.ɵɵelementStart(21, \"span\", 24);\n          i0.ɵɵtext(22, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"nb-select\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildCaseChange());\n          });\n          i0.ɵɵtemplate(24, SettingTimePeriodComponent_nb_option_24_Template, 2, 2, \"nb-option\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 27)(26, \"label\", 23);\n          i0.ɵɵtext(27, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"nb-select\", 28);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_28_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildingChange());\n          });\n          i0.ɵɵelementStart(29, \"nb-option\", 29);\n          i0.ɵɵtext(30, \"\\u5168\\u90E8\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, SettingTimePeriodComponent_nb_option_31_Template, 2, 2, \"nb-option\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 30)(33, \"label\", 23);\n          i0.ɵɵtext(34, \"\\u958B\\u653E\\u65E5\\u671F\\u7BC4\\u570D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 31)(36, \"nb-form-field\", 32);\n          i0.ɵɵelement(37, \"nb-icon\", 33);\n          i0.ɵɵelementStart(38, \"input\", 34);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(39, \"nb-datepicker\", 35, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"span\", 36);\n          i0.ɵɵtext(42, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"nb-form-field\", 32);\n          i0.ɵɵelement(44, \"nb-icon\", 33);\n          i0.ɵɵelementStart(45, \"input\", 37);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_45_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"nb-datepicker\", 35, 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 38)(49, \"div\", 39)(50, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_50_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵelement(51, \"i\", 41);\n          i0.ɵɵtext(52, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"button\", 42);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_53_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetFilters());\n          });\n          i0.ɵɵelement(54, \"i\", 43);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(55, SettingTimePeriodComponent_div_55_Template, 6, 5, \"div\", 44)(56, SettingTimePeriodComponent_div_56_Template, 38, 11, \"div\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(57, SettingTimePeriodComponent_div_57_Template, 43, 31, \"div\", 46)(58, SettingTimePeriodComponent_div_58_Template, 7, 0, \"div\", 47)(59, SettingTimePeriodComponent_div_59_Template, 9, 0, \"div\", 47);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(60, SettingTimePeriodComponent_ng_template_60_Template, 36, 9, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(62, SettingTimePeriodComponent_ng_template_62_Template, 4, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(64, SettingTimePeriodComponent_ng_template_64_Template, 29, 6, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const StartDate_r30 = i0.ɵɵreference(40);\n          const EndDate_r31 = i0.ɵɵreference(47);\n          i0.ɵɵadvance(23);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingOptions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", StartDate_r30);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", EndDate_r31);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAdvancedFilters && ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length === 0 && ctx.houseChangeDates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 234:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nunmatched \\\\\\\"}\\\\\\\".\\\\n    \\u2577\\\\n147 \\u2502 }\\\\r\\\\n    \\u2502 ^\\\\n    \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\selection-management\\\\\\\\setting-time-period\\\\\\\\setting-time-period.component.scss 147:1  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t})[_ngcontent-%COMP%];\\n\\n\\n\\n \\t\\n\\n \\t//[_ngcontent-%COMP%]   startup\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   Load[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   and[_ngcontent-%COMP%]   return[_ngcontent-%COMP%]   exports\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   This[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   doesn't[_ngcontent-%COMP%]   tell[_ngcontent-%COMP%]   about[_ngcontent-%COMP%]   it's[_ngcontent-%COMP%]   top-level[_ngcontent-%COMP%]   declarations[_ngcontent-%COMP%]   so[_ngcontent-%COMP%]   it[_ngcontent-%COMP%]   can't[_ngcontent-%COMP%]   be[_ngcontent-%COMP%]   inlined\\n\\n[_ngcontent-%COMP%]   var[_ngcontent-%COMP%]   __webpack_exports__[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] {};\\n\\n \\t__webpack_modules__[234]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "building_r3", "ɵɵtextInterpolate", "ctx_r4", "getActiveFiltersCount", "ɵɵlistener", "SettingTimePeriodComponent_div_55_Template_button_click_1_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "toggleAdvancedFilters", "ɵɵelement", "ɵɵtemplate", "SettingTimePeriodComponent_div_55_span_5_Template", "ɵɵclassProp", "showAdvancedFilters", "hasActiveFilters", "floor_r7", "SettingTimePeriodComponent_div_56_button_37_Template_button_click_0_listener", "_r8", "clearAllFilters", "ɵɵtwoWayListener", "SettingTimePeriodComponent_div_56_Template_input_ngModelChange_5_listener", "$event", "_r6", "ɵɵtwoWayBindingSet", "filterOptions", "searchKeyword", "onSearch", "SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_7_listener", "statusFilter", "SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_7_listener", "SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_21_listener", "floorFilter", "SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_21_listener", "SettingTimePeriodComponent_div_56_nb_option_24_Template", "SettingTimePeriodComponent_div_56_Template_nb_select_ngModelChange_26_listener", "pageSize", "SettingTimePeriodComponent_div_56_Template_nb_select_selectedChange_26_listener", "onPageSizeChange", "SettingTimePeriodComponent_div_56_button_37_Template", "undefined", "ɵɵtwoWayProperty", "availableFloors", "selectedHouses", "length", "ɵɵpipeBind2", "house_r11", "CChangeStartDate", "CChangeEndDate", "SettingTimePeriodComponent_div_57_tr_41_Template_nb_checkbox_ngModelChange_2_listener", "_r10", "$implicit", "selected", "onHouseSelectionChange", "SettingTimePeriodComponent_div_57_tr_41_span_10_Template", "SettingTimePeriodComponent_div_57_tr_41_span_11_Template", "SettingTimePeriodComponent_div_57_tr_41_span_13_Template", "SettingTimePeriodComponent_div_57_tr_41_span_14_Template", "SettingTimePeriodComponent_div_57_tr_41_Template_button_click_19_listener", "dialog_r12", "ɵɵreference", "openModel", "CHouseId", "CHouseHold", "CBuildingName", "CFloor", "ɵɵclassMap", "getStatusClass", "getStatusText", "SettingTimePeriodComponent_div_57_div_42_li_9_Template_button_click_1_listener", "page_r15", "_r14", "goToPage", "currentPage", "SettingTimePeriodComponent_div_57_div_42_Template_button_click_4_listener", "_r13", "SettingTimePeriodComponent_div_57_div_42_Template_button_click_7_listener", "SettingTimePeriodComponent_div_57_div_42_li_9_Template", "SettingTimePeriodComponent_div_57_div_42_Template_button_click_11_listener", "SettingTimePeriodComponent_div_57_div_42_Template_button_click_14_listener", "totalPages", "getVisiblePages", "SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_4_listener", "_r9", "selectAll", "onSelectAllChange", "SettingTimePeriodComponent_div_57_Template_button_click_6_listener", "openBatchSetting", "SettingTimePeriodComponent_div_57_span_9_Template", "SettingTimePeriodComponent_div_57_Template_button_click_10_listener", "exportData", "SettingTimePeriodComponent_div_57_Template_nb_checkbox_ngModelChange_20_listener", "SettingTimePeriodComponent_div_57_Template_th_click_21_listener", "sort", "SettingTimePeriodComponent_div_57_Template_th_click_24_listener", "SettingTimePeriodComponent_div_57_Template_th_click_27_listener", "SettingTimePeriodComponent_div_57_Template_th_click_30_listener", "SettingTimePeriodComponent_div_57_Template_th_click_33_listener", "SettingTimePeriodComponent_div_57_tr_41_Template", "SettingTimePeriodComponent_div_57_div_42_Template", "filteredHouses", "ɵɵtextInterpolate3", "Math", "min", "sortField", "sortDirection", "paginatedHouses", "trackByHouseId", "selectedBuildingForBatch", "name", "house_r17", "SettingTimePeriodComponent_ng_template_60_div_28_span_4_Template", "SettingTimePeriodComponent_ng_template_60_div_28_span_5_Template", "slice", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener", "house_r22", "_r21", "ɵɵtextInterpolate2", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_nb_checkbox_1_Template", "floor_r20", "houses", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener", "_r19", "onFloorSelectionChange", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_div_3_Template", "floorNumber", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_div_1_Template", "floors", "SettingTimePeriodComponent_ng_template_60_div_29_Template_nb_checkbox_ngModelChange_1_listener", "_r18", "batchSettings", "applyToAll", "SettingTimePeriodComponent_ng_template_60_div_29_div_3_Template", "flattenedHouses", "SettingTimePeriodComponent_ng_template_60_span_3_Template", "SettingTimePeriodComponent_ng_template_60_span_4_Template", "SettingTimePeriodComponent_ng_template_60_Template_input_ngModelChange_14_listener", "_r16", "startDate", "SettingTimePeriodComponent_ng_template_60_Template_input_ngModelChange_21_listener", "endDate", "SettingTimePeriodComponent_ng_template_60_div_28_Template", "SettingTimePeriodComponent_ng_template_60_div_29_Template", "SettingTimePeriodComponent_ng_template_60_Template_button_click_31_listener", "ref_r23", "dialogRef", "onClose", "SettingTimePeriodComponent_ng_template_60_Template_button_click_33_listener", "onBatchSubmit", "SettingTimePeriodComponent_ng_template_60_span_35_Template", "batchStartDate_r24", "batchEndDate_r25", "SettingTimePeriodComponent_ng_template_64_Template_input_ngModelChange_14_listener", "_r26", "selectedHouseChangeDate", "SettingTimePeriodComponent_ng_template_64_Template_input_ngModelChange_21_listener", "SettingTimePeriodComponent_ng_template_64_Template_button_click_25_listener", "ref_r27", "SettingTimePeriodComponent_ng_template_64_Template_button_click_27_listener", "onSubmit", "changeStartDate_r28", "changeEndDate_r29", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "buildingGroups", "buildingOptions", "selectedBuilding", "buildingFilter", "selectedBuildings", "selectedFloors", "loading", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "searchQuery", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "validation", "errorMessages", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "for<PERSON>ach", "household", "CHouses", "house", "floor", "push", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "onBuildCaseChange", "resetAllStates", "houseChangeDates", "convertedHouseArray", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "buildBuildingGroups", "buildFlattenedHouses", "data", "buildingMap", "Map", "houseType", "buildingName", "has", "set", "floorMap", "get", "localeCompare", "bg", "updateAvailableFloors", "building", "onBuildingChange", "getFilteredBuildings", "filter", "keyword", "toLowerCase", "hasMatchingHouse", "some", "includes", "hasMatchingStatus", "matchesStatusFilter", "parseInt", "hasMatchingFloor", "filteredBuilding", "hasValidHouses", "filteredFloor", "status", "getHouseStatus", "now", "today", "getFullYear", "getMonth", "getDate", "isNaN", "getTime", "console", "warn", "start", "end", "houseId", "startDateOnly", "endDateOnly", "error", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "hasSelectedHouses", "batchSettingDialog", "housesToUpdate", "openHouseDialog", "dialog", "debugStatusCounts", "statusCounts", "active", "pending", "expired", "disabled", "hasOwnProperty", "log", "toISOString", "split", "previouslySelectedIds", "updateSelectAllState", "updatePagination", "every", "ceil", "startIndex", "endIndex", "page", "pages", "maxVisible", "max", "i", "updateSelectedHouses", "field", "aValue", "bValue", "Number", "_index", "csv<PERSON><PERSON>nt", "generateCSV", "blob", "Blob", "type", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "rows", "row", "cell", "join", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "Router", "i7", "EventService", "selectors", "viewQuery", "SettingTimePeriodComponent_Query", "rf", "ctx", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingTimePeriodComponent_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_23_listener", "_r1", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON><PERSON>_23_listener", "SettingTimePeriodComponent_nb_option_24_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_28_listener", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_28_listener", "SettingTimePeriodComponent_nb_option_31_Template", "SettingTimePeriodComponent_Template_input_ngModelChange_38_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_45_listener", "SettingTimePeriodComponent_Template_button_click_50_listener", "SettingTimePeriodComponent_Template_button_click_53_listener", "resetFilters", "SettingTimePeriodComponent_div_55_Template", "SettingTimePeriodComponent_div_56_Template", "SettingTimePeriodComponent_div_57_Template", "SettingTimePeriodComponent_div_58_Template", "SettingTimePeriodComponent_div_59_Template", "SettingTimePeriodComponent_ng_template_60_Template", "ɵɵtemplateRefExtractor", "SettingTimePeriodComponent_ng_template_62_Template", "SettingTimePeriodComponent_ng_template_64_Template", "StartDate_r30", "EndDate_r31", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\n\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string; // 戶型\r\n  CBuildingName: string; // 棟別\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n\r\n  @ViewChild('batchSettingDialog') batchSettingDialog!: TemplateRef<any>;\r\n  @ViewChild('dialog') dialog!: TemplateRef<any>;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  // 新增的屬性\r\n  buildingGroups: BuildingGroup[] = [];\r\n  buildingOptions: string[] = [];\r\n  selectedBuilding: string = '';\r\n  availableFloors: number[] = [];\r\n\r\n  // 篩選和搜尋\r\n  filterOptions: FilterOptions = {\r\n    searchKeyword: '',\r\n    statusFilter: '',\r\n    floorFilter: '',\r\n    buildingFilter: ''\r\n  };\r\n\r\n  // 批次設定\r\n  batchSettings: BatchSettings = {\r\n    startDate: null,\r\n    endDate: null,\r\n    applyToAll: true,\r\n    selectedBuildings: [],\r\n    selectedFloors: [],\r\n    selectedHouses: []\r\n  };\r\n\r\n  selectedBuildingForBatch: BuildingGroup | null = null;\r\n\r\n  // 表格視圖相關屬性\r\n  flattenedHouses: ConvertedHouse[] = [];\r\n  filteredHouses: ConvertedHouse[] = [];\r\n  paginatedHouses: ConvertedHouse[] = [];\r\n  selectedHouses: ConvertedHouse[] = [];\r\n  selectAll: boolean = false;\r\n  loading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  override pageSize: number = 50;\r\n  totalPages: number = 1;\r\n\r\n  // 排序相關\r\n  sortField: string = '';\r\n  sortDirection: 'asc' | 'desc' = 'asc';\r\n\r\n  // 數學函數引用\r\n  Math = Math;\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CBuildingName: '未分類',\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：建案變更處理\r\n  onBuildCaseChange() {\r\n    // 重置所有相關狀態\r\n    this.resetAllStates();\r\n    // 執行查詢\r\n    this.getHouseChangeDate();\r\n  }\r\n\r\n  // 新增：重置所有狀態\r\n  resetAllStates() {\r\n    // 重置數據\r\n    this.houseChangeDates = [];\r\n    this.convertedHouseArray = [];\r\n    this.buildingGroups = [];\r\n    this.flattenedHouses = [];\r\n    this.filteredHouses = [];\r\n    this.paginatedHouses = [];\r\n    this.selectedHouses = [];\r\n\r\n    // 重置篩選條件\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    this.selectAll = false;\r\n    this.selectedBuilding = '';\r\n\r\n    // 重置分頁\r\n    this.currentPage = 1;\r\n    this.totalPages = 1;\r\n\r\n    // 重置可用選項\r\n    this.buildingOptions = [];\r\n    this.availableFloors = [];\r\n\r\n    // 重置排序\r\n    this.sortField = '';\r\n    this.sortDirection = 'asc';\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    // 如果沒有選擇建案，直接返回\r\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      this.loading = false;\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n          // 新增：建立棟別分組資料\r\n          this.buildBuildingGroups(res.Entries)\r\n          // 新增：建立扁平化資料\r\n          this.buildFlattenedHouses(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\r\n  buildBuildingGroups(data: GetHouseChangeDateRes[]) {\r\n    const buildingMap = new Map<string, Map<number, ConvertedHouse[]>>();\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || ''; // 戶型\r\n\r\n      household.CHouses?.forEach(house => {\r\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\r\n        const floor = house.CFloor || 0;\r\n\r\n        if (!buildingMap.has(buildingName)) {\r\n          buildingMap.set(buildingName, new Map<number, ConvertedHouse[]>());\r\n        }\r\n\r\n        const floorMap = buildingMap.get(buildingName)!;\r\n\r\n        if (!floorMap.has(floor)) {\r\n          floorMap.set(floor, []);\r\n        }\r\n\r\n        floorMap.get(floor)!.push({\r\n          CHouseHold: houseType, // 戶型\r\n          CBuildingName: buildingName, // 棟別\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: floor,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 轉換為BuildingGroup格式\r\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\r\n      const floors: FloorGroup[] = Array.from(floorMap.entries())\r\n        .sort(([a], [b]) => b - a) // 樓層由高到低排序\r\n        .map(([floorNumber, houses]) => ({\r\n          floorNumber,\r\n          houses: houses.sort((a, b) => {\r\n            // 排序邏輯：先按戶型排序，再按樓層排序\r\n            if (a.CHouseHold !== b.CHouseHold) {\r\n              return a.CHouseHold.localeCompare(b.CHouseHold);\r\n            }\r\n            return a.CFloor - b.CFloor;\r\n          }),\r\n          selected: false\r\n        }));\r\n\r\n      return {\r\n        name: buildingName,\r\n        floors,\r\n        selected: false\r\n      };\r\n    }).sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    // 更新棟別選項和可用樓層\r\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\r\n    this.updateAvailableFloors();\r\n  }\r\n\r\n  // 新增：更新可用樓層\r\n  updateAvailableFloors() {\r\n    const floorsSet = new Set<number>();\r\n\r\n    this.buildingGroups.forEach(building => {\r\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\r\n        building.floors.forEach(floor => {\r\n          floorsSet.add(floor.floorNumber);\r\n        });\r\n      }\r\n    });\r\n\r\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\r\n  }\r\n\r\n  // 修改：棟別選擇變更處理\r\n  onBuildingChange() {\r\n    // 重置選擇狀態\r\n    this.selectedHouses.forEach(house => house.selected = false);\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n\r\n    // 重置分頁到第一頁\r\n    this.currentPage = 1;\r\n\r\n    // 重置樓層篩選\r\n    this.filterOptions.floorFilter = '';\r\n\r\n    // 更新可用樓層\r\n    this.updateAvailableFloors();\r\n\r\n    // 設定棟別篩選並執行搜尋\r\n    this.filterOptions.buildingFilter = this.selectedBuilding;\r\n    this.onSearch();\r\n  }\r\n\r\n\r\n\r\n  // 新增：取得過濾後的棟別資料\r\n  getFilteredBuildings(): BuildingGroup[] {\r\n    return this.buildingGroups.filter(building => {\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 關鍵字搜尋 (搜尋戶型)\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        const hasMatchingHouse = building.floors.some(floor =>\r\n          floor.houses.some(house =>\r\n            house.CHouseHold.toLowerCase().includes(keyword) ||\r\n            house.CBuildingName.toLowerCase().includes(keyword)\r\n          )\r\n        );\r\n        if (!hasMatchingHouse) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        const hasMatchingStatus = building.floors.some(floor =>\r\n          floor.houses.some(house => this.matchesStatusFilter(house))\r\n        );\r\n        if (!hasMatchingStatus) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        const hasMatchingFloor = building.floors.some(floor =>\r\n          floor.floorNumber === floorNumber\r\n        );\r\n        if (!hasMatchingFloor) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }).map(building => {\r\n      // 對每個棟別，也要篩選其樓層和戶別\r\n      const filteredBuilding = { ...building };\r\n      filteredBuilding.floors = building.floors.filter(floor => {\r\n        // 樓層篩選\r\n        if (this.filterOptions.floorFilter) {\r\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n          if (floor.floorNumber !== floorNumber) {\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 檢查該樓層是否有符合條件的戶別\r\n        const hasValidHouses = floor.houses.some(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return hasValidHouses;\r\n      }).map(floor => {\r\n        // 篩選戶別\r\n        const filteredFloor = { ...floor };\r\n        filteredFloor.houses = floor.houses.filter(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return filteredFloor;\r\n      });\r\n\r\n      return filteredBuilding;\r\n    });\r\n  }\r\n\r\n  // 修復：檢查戶別是否符合狀態篩選\r\n  private matchesStatusFilter(house: ConvertedHouse): boolean {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (this.filterOptions.statusFilter) {\r\n      case 'active':\r\n        return status === 'active';\r\n      case 'pending':\r\n        return status === 'pending';\r\n      case 'expired':\r\n        return status === 'expired';\r\n      case 'not-set':\r\n        return status === 'not-set';\r\n      case 'disabled':\r\n        return status === 'disabled';\r\n      default:\r\n        return true; // 全部狀態\r\n    }\r\n  }\r\n\r\n  // 修復：取得戶別狀態\r\n  private getHouseStatus(house: ConvertedHouse): string {\r\n    if (!house.CHouseId) {\r\n      return 'disabled';\r\n    }\r\n\r\n    // 檢查是否有設定開放時段\r\n    if (!house.CChangeStartDate || !house.CChangeEndDate ||\r\n      house.CChangeStartDate === '' || house.CChangeEndDate === '') {\r\n      return 'not-set';\r\n    }\r\n\r\n    try {\r\n      // 處理日期字串，支援多種格式\r\n      const now = new Date();\r\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n\r\n      // 解析開始日期\r\n      let startDate: Date;\r\n      if (house.CChangeStartDate.includes('T')) {\r\n        startDate = new Date(house.CChangeStartDate);\r\n      } else {\r\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\r\n      }\r\n\r\n      // 解析結束日期\r\n      let endDate: Date;\r\n      if (house.CChangeEndDate.includes('T')) {\r\n        endDate = new Date(house.CChangeEndDate);\r\n      } else {\r\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\r\n      }\r\n\r\n      // 檢查日期有效性\r\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\r\n        console.warn('Invalid date format:', {\r\n          start: house.CChangeStartDate,\r\n          end: house.CChangeEndDate,\r\n          houseId: house.CHouseId\r\n        });\r\n        return 'not-set';\r\n      }\r\n\r\n      // 轉換為日期比較（不含時間）\r\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\r\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\r\n\r\n      // 判斷狀態\r\n      if (today < startDateOnly) {\r\n        return 'pending'; // 待開放\r\n      } else if (today >= startDateOnly && today <= endDateOnly) {\r\n        return 'active'; // 進行中\r\n      } else {\r\n        return 'expired'; // 已過期\r\n      }\r\n    } catch (error) {\r\n      console.error('Error parsing dates:', error, house);\r\n      return 'not-set';\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n\r\n  // 修改：開啟批次設定對話框\r\n  openBatchSetting(building?: BuildingGroup) {\r\n    this.selectedBuildingForBatch = building || null;\r\n\r\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\r\n    const hasSelectedHouses = this.selectedHouses.length > 0;\r\n\r\n    this.batchSettings = {\r\n      startDate: null,\r\n      endDate: null,\r\n      applyToAll: !hasSelectedHouses && !building,\r\n      selectedBuildings: building ? [building.name] : [],\r\n      selectedFloors: [],\r\n      selectedHouses: []\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    if (building) {\r\n      building.floors.forEach(floor => {\r\n        floor.selected = false;\r\n        floor.houses.forEach(house => house.selected = false);\r\n      });\r\n    }\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(this.batchSettingDialog);\r\n  }\r\n\r\n  // 新增：樓層選擇變更處理\r\n  onFloorSelectionChange(floor: FloorGroup) {\r\n    if (floor.selected) {\r\n      floor.houses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          house.selected = true;\r\n        }\r\n      });\r\n    } else {\r\n      floor.houses.forEach(house => house.selected = false);\r\n    }\r\n  }\r\n\r\n  // 修改：批次提交\r\n  onBatchSubmit(ref: any) {\r\n    // 驗證批次設定\r\n    this.valid.clear();\r\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\r\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 收集要更新的房屋\r\n    const housesToUpdate: any[] = [];\r\n\r\n    if (this.batchSettings.applyToAll) {\r\n      // 全部戶別\r\n      this.flattenedHouses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          housesToUpdate.push({\r\n            CHouseId: house.CHouseId,\r\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // 使用已選擇的戶別\r\n      if (this.selectedHouses.length > 0) {\r\n        this.selectedHouses.forEach(house => {\r\n          if (house.CHouseId) {\r\n            housesToUpdate.push({\r\n              CHouseId: house.CHouseId,\r\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n            });\r\n          }\r\n        });\r\n      } else if (this.selectedBuildingForBatch) {\r\n        // 如果沒有選擇的戶別，使用舊的邏輯\r\n        this.selectedBuildingForBatch.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.selected && house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n\r\n    if (housesToUpdate.length === 0) {\r\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\r\n      return;\r\n    }\r\n\r\n    // 調用API進行批次更新\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: housesToUpdate\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\r\n        // 清除選擇狀態\r\n        this.selectedHouses.forEach(house => house.selected = false);\r\n        this.selectedHouses = [];\r\n        this.selectAll = false;\r\n        this.getHouseChangeDate();\r\n        ref.close();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：取得狀態樣式類別\r\n  getStatusClass(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    return `status-${status}`;\r\n  }\r\n\r\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\r\n  openHouseDialog(house: ConvertedHouse) {\r\n    if (house.CHouseId) {\r\n      // 使用現有的openModel方法\r\n      this.openModel(this.dialog, house);\r\n    }\r\n  }\r\n\r\n  // 新增：建立扁平化房屋資料\r\n  buildFlattenedHouses(data: GetHouseChangeDateRes[]) {\r\n    this.flattenedHouses = [];\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || '';\r\n\r\n      household.CHouses?.forEach(house => {\r\n        this.flattenedHouses.push({\r\n          CHouseHold: houseType,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: house.CFloor || 0,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 初始化篩選和分頁\r\n    this.onSearch();\r\n\r\n    // 調試：輸出狀態統計\r\n    this.debugStatusCounts();\r\n  }\r\n\r\n  // 調試：輸出狀態統計\r\n  private debugStatusCounts() {\r\n    const statusCounts = {\r\n      active: 0,\r\n      pending: 0,\r\n      expired: 0,\r\n      'not-set': 0,\r\n      disabled: 0\r\n    };\r\n\r\n    this.flattenedHouses.forEach(house => {\r\n      const status = this.getHouseStatus(house);\r\n      if (statusCounts.hasOwnProperty(status)) {\r\n        statusCounts[status as keyof typeof statusCounts]++;\r\n      }\r\n    });\r\n\r\n    console.log('狀態統計:', statusCounts);\r\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\r\n  }\r\n\r\n\r\n\r\n  // 修改：搜尋和篩選\r\n  onSearch() {\r\n    // 記錄篩選前的已選擇項目\r\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\r\n\r\n    this.filteredHouses = this.flattenedHouses.filter(house => {\r\n      // 關鍵字搜尋\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n          !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        if (!this.matchesStatusFilter(house)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        if (house.CFloor !== floorNumber) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    });\r\n\r\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\r\n    this.selectedHouses = this.filteredHouses.filter(house =>\r\n      previouslySelectedIds.includes(house.CHouseId)\r\n    );\r\n\r\n    // 更新扁平化資料中的選擇狀態\r\n    this.flattenedHouses.forEach(house => {\r\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\r\n    });\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n\r\n    // 重新計算分頁\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：更新全選狀態\r\n  private updateSelectAllState() {\r\n    if (this.paginatedHouses.length === 0) {\r\n      this.selectAll = false;\r\n    } else {\r\n      this.selectAll = this.paginatedHouses.every(house =>\r\n        !house.CHouseId || house.selected\r\n      );\r\n    }\r\n  }\r\n\r\n  // 修改：更新分頁\r\n  updatePagination() {\r\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\r\n    const startIndex = (this.currentPage - 1) * this.pageSize;\r\n    const endIndex = startIndex + this.pageSize;\r\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 新增：頁面大小變更\r\n  onPageSizeChange() {\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：跳轉頁面\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  // 新增：取得可見頁碼\r\n  getVisiblePages(): number[] {\r\n    const pages: number[] = [];\r\n    const maxVisible = 5;\r\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\r\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\r\n\r\n    if (end - start + 1 < maxVisible) {\r\n      start = Math.max(1, end - maxVisible + 1);\r\n    }\r\n\r\n    for (let i = start; i <= end; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 新增：全選/取消全選\r\n  onSelectAllChange() {\r\n    this.paginatedHouses.forEach(house => {\r\n      if (house.CHouseId) {\r\n        house.selected = this.selectAll;\r\n      }\r\n    });\r\n    this.updateSelectedHouses();\r\n  }\r\n\r\n  // 新增：單一選擇變更\r\n  onHouseSelectionChange() {\r\n    this.updateSelectedHouses();\r\n    this.selectAll = this.paginatedHouses.every(house =>\r\n      !house.CHouseId || house.selected\r\n    );\r\n  }\r\n\r\n  // 新增：更新已選擇房屋列表\r\n  updateSelectedHouses() {\r\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\r\n  }\r\n\r\n  // 新增：排序\r\n  sort(field: string) {\r\n    if (this.sortField === field) {\r\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortDirection = 'asc';\r\n    }\r\n\r\n    this.filteredHouses.sort((a, b) => {\r\n      let aValue = (a as any)[field];\r\n      let bValue = (b as any)[field];\r\n\r\n      // 處理日期排序\r\n      if (field.includes('Date')) {\r\n        aValue = aValue ? new Date(aValue).getTime() : 0;\r\n        bValue = bValue ? new Date(bValue).getTime() : 0;\r\n      }\r\n\r\n      // 處理數字排序\r\n      if (field === 'CFloor') {\r\n        aValue = Number(aValue) || 0;\r\n        bValue = Number(bValue) || 0;\r\n      }\r\n\r\n      // 處理字串排序\r\n      if (typeof aValue === 'string') {\r\n        aValue = aValue.toLowerCase();\r\n        bValue = bValue.toLowerCase();\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return this.sortDirection === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return this.sortDirection === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：TrackBy函數\r\n  trackByHouseId(_index: number, house: ConvertedHouse): number {\r\n    return house.CHouseId;\r\n  }\r\n\r\n  // 新增：取得狀態文字\r\n  getStatusText(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (status) {\r\n      case 'active':\r\n        return '進行中';\r\n      case 'pending':\r\n        return '待開放';\r\n      case 'expired':\r\n        return '已過期';\r\n      case 'not-set':\r\n        return '未設定';\r\n      case 'disabled':\r\n        return '已停用';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  // 新增：匯出資料\r\n  exportData() {\r\n    // 實現匯出功能\r\n    const csvContent = this.generateCSV();\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  // 新增：產生CSV內容\r\n  private generateCSV(): string {\r\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\r\n    const rows = this.filteredHouses.map(house => [\r\n      house.CHouseHold,\r\n      house.CBuildingName,\r\n      `${house.CFloor}F`,\r\n      house.CChangeStartDate || '未設定',\r\n      house.CChangeEndDate || '未設定',\r\n      this.getStatusText(house)\r\n    ]);\r\n\r\n    const csvContent = [headers, ...rows]\r\n      .map(row => row.map(cell => `\"${cell}\"`).join(','))\r\n      .join('\\n');\r\n\r\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <!-- 優化的Header區域 -->\r\n    <div class=\"page-header-optimized\">\r\n      <!-- 頁面標題和幫助 -->\r\n      <div class=\"page-title-section\">\r\n        <div class=\"d-flex align-items-center justify-content-between\">\r\n          <div class=\"title-group\">\r\n            <h5 class=\"page-title mb-1\">\r\n              <i class=\"fas fa-clock me-2\"></i>選樣開放時段設定\r\n            </h5>\r\n            <p class=\"page-subtitle text-muted mb-0\">管理各戶別的選樣開放時間範圍</p>\r\n          </div>\r\n          <div class=\"help-section\">\r\n            <button class=\"btn btn-outline-secondary btn-sm\" type=\"button\" data-bs-toggle=\"tooltip\"\r\n              title=\"設定各戶別的選樣開放時段，控制客戶選樣權限\">\r\n              <i class=\"fas fa-question-circle\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 緊湊的主要篩選區域 -->\r\n      <div class=\"compact-filters\">\r\n        <div class=\"row g-3 align-items-end\">\r\n          <div class=\"col-lg-3 col-md-4\">\r\n            <label class=\"form-label small fw-medium\">建案 <span class=\"text-danger\">*</span></label>\r\n            <nb-select placeholder=\"請選擇建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\"\r\n              (selectedChange)=\"onBuildCaseChange()\" size=\"small\">\r\n              <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n                {{ case.CBuildCaseName }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <label class=\"form-label small fw-medium\">棟別</label>\r\n            <nb-select placeholder=\"全部棟別\" [(ngModel)]=\"selectedBuilding\" (selectedChange)=\"onBuildingChange()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部棟別</nb-option>\r\n              <nb-option *ngFor=\"let building of buildingOptions\" [value]=\"building\">\r\n                {{ building }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-4 col-md-5\">\r\n            <label class=\"form-label small fw-medium\">開放日期範圍</label>\r\n            <div class=\"date-range-group\">\r\n              <nb-form-field size=\"small\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"開始日期\" [nbDatepicker]=\"StartDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n                <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n              <span class=\"date-separator\">~</span>\r\n              <nb-form-field size=\"small\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"結束日期\" [nbDatepicker]=\"EndDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n                <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12\">\r\n            <div class=\"action-buttons\">\r\n              <button class=\"btn btn-primary\" (click)=\"getHouseChangeDate()\" [disabled]=\"loading\">\r\n                <i class=\"fas fa-search me-1\"></i>查詢\r\n              </button>\r\n              <button class=\"btn btn-outline-secondary ms-2\" (click)=\"resetFilters()\" [disabled]=\"loading\"\r\n                title=\"重置篩選條件\">\r\n                <i class=\"fas fa-undo\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 可摺疊的進階篩選 -->\r\n      <div class=\"advanced-filters-toggle\" *ngIf=\"flattenedHouses.length > 0\">\r\n        <button class=\"btn btn-link btn-sm p-0\" (click)=\"toggleAdvancedFilters()\" type=\"button\">\r\n          <i class=\"fas\" [class.fa-chevron-down]=\"!showAdvancedFilters\" [class.fa-chevron-up]=\"showAdvancedFilters\"></i>\r\n          <span class=\"ms-1\">進階篩選</span>\r\n          <span class=\"badge bg-primary ms-2\" *ngIf=\"hasActiveFilters()\">{{ getActiveFiltersCount() }}</span>\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"advanced-filters-panel\" *ngIf=\"showAdvancedFilters && flattenedHouses.length > 0\" [@slideInOut]>\r\n        <div class=\"row g-3 align-items-center\">\r\n          <div class=\"col-lg-3 col-md-4\">\r\n            <nb-form-field size=\"small\">\r\n              <nb-icon nbPrefix icon=\"search-outline\"></nb-icon>\r\n              <input nbInput placeholder=\"搜尋戶型、棟別...\" [(ngModel)]=\"filterOptions.searchKeyword\"\r\n                (ngModelChange)=\"onSearch()\">\r\n            </nb-form-field>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <nb-select placeholder=\"狀態篩選\" [(ngModel)]=\"filterOptions.statusFilter\" (selectedChange)=\"onSearch()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部狀態</nb-option>\r\n              <nb-option value=\"active\">進行中</nb-option>\r\n              <nb-option value=\"pending\">待開放</nb-option>\r\n              <nb-option value=\"expired\">已過期</nb-option>\r\n              <nb-option value=\"not-set\">未設定</nb-option>\r\n              <nb-option value=\"disabled\">已停用</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <nb-select placeholder=\"樓層篩選\" [(ngModel)]=\"filterOptions.floorFilter\" (selectedChange)=\"onSearch()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部樓層</nb-option>\r\n              <nb-option *ngFor=\"let floor of availableFloors\" [value]=\"floor\">\r\n                {{ floor }}F\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-2\">\r\n            <nb-select placeholder=\"顯示筆數\" [(ngModel)]=\"pageSize\" (selectedChange)=\"onPageSizeChange()\" size=\"small\">\r\n              <nb-option [value]=\"50\">50筆/頁</nb-option>\r\n              <nb-option [value]=\"100\">100筆/頁</nb-option>\r\n              <nb-option [value]=\"200\">200筆/頁</nb-option>\r\n              <nb-option [value]=\"500\">500筆/頁</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12\">\r\n            <div class=\"filter-actions\">\r\n              <button class=\"btn btn-outline-danger btn-sm\" (click)=\"clearAllFilters()\" *ngIf=\"hasActiveFilters()\">\r\n                <i class=\"fas fa-times me-1\"></i>清除篩選\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 表格視圖 -->\r\n    <div class=\"table-view mt-4\" *ngIf=\"flattenedHouses.length > 0\">\r\n      <!-- 工具列 -->\r\n      <div class=\"table-toolbar mb-3\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"batch-actions\">\r\n            <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\">\r\n              全選\r\n            </nb-checkbox>\r\n            <button class=\"btn btn-sm btn-warning ml-2\" [disabled]=\"selectedHouses.length === 0\"\r\n              (click)=\"openBatchSetting()\" title=\"批次設定選中的戶別開放時段\">\r\n              <i class=\"fas fa-cogs me-1\"></i>批次設定\r\n              <span *ngIf=\"selectedHouses.length > 0\" class=\"badge badge-light ml-1\">\r\n                {{ selectedHouses.length }}\r\n              </span>\r\n            </button>\r\n            <button class=\"btn btn-sm btn-success ml-2\" [disabled]=\"filteredHouses.length === 0\" (click)=\"exportData()\">\r\n              <i class=\"fas fa-download\"></i> 匯出\r\n            </button>\r\n          </div>\r\n          <div class=\"pagination-info\">\r\n            顯示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredHouses.length) }}\r\n            / 共 {{ filteredHouses.length }} 筆\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格 -->\r\n      <div class=\"table-container\">\r\n        <table class=\"table table-hover\">\r\n          <thead class=\"table-header\">\r\n            <tr>\r\n              <th width=\"50\">\r\n                <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\"></nb-checkbox>\r\n              </th>\r\n              <th width=\"100\" (click)=\"sort('CHouseHold')\" class=\"sortable\">\r\n                戶型\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CHouseHold' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CHouseHold' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"100\" (click)=\"sort('CBuildingName')\" class=\"sortable\">\r\n                棟別\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CBuildingName' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CBuildingName' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"80\" (click)=\"sort('CFloor')\" class=\"sortable\">\r\n                樓層\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CFloor' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CFloor' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"120\" (click)=\"sort('CChangeStartDate')\" class=\"sortable\">\r\n                開始日期\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CChangeStartDate' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CChangeStartDate' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"120\" (click)=\"sort('CChangeEndDate')\" class=\"sortable\">\r\n                結束日期\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CChangeEndDate' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CChangeEndDate' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"100\">狀態</th>\r\n              <th width=\"80\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let house of paginatedHouses; trackBy: trackByHouseId\"\r\n              [class.table-row-selected]=\"house.selected\">\r\n              <td>\r\n                <nb-checkbox [(ngModel)]=\"house.selected\" [disabled]=\"!house.CHouseId\"\r\n                  (ngModelChange)=\"onHouseSelectionChange()\"></nb-checkbox>\r\n              </td>\r\n              <td>{{ house.CHouseHold }}</td>\r\n              <td>{{ house.CBuildingName }}</td>\r\n              <td>{{ house.CFloor }}F</td>\r\n              <td>\r\n                <span *ngIf=\"house.CChangeStartDate\" class=\"date-display\">\r\n                  {{ house.CChangeStartDate | date:'yyyy-MM-dd' }}\r\n                </span>\r\n                <span *ngIf=\"!house.CChangeStartDate\" class=\"text-muted\">未設定</span>\r\n              </td>\r\n              <td>\r\n                <span *ngIf=\"house.CChangeEndDate\" class=\"date-display\">\r\n                  {{ house.CChangeEndDate | date:'yyyy-MM-dd' }}\r\n                </span>\r\n                <span *ngIf=\"!house.CChangeEndDate\" class=\"text-muted\">未設定</span>\r\n              </td>\r\n              <td>\r\n                <span class=\"status-badge\" [class]=\"getStatusClass(house)\">\r\n                  {{ getStatusText(house) }}\r\n                </span>\r\n              </td>\r\n              <td>\r\n                <button class=\"btn btn-sm btn-outline-primary\" [disabled]=\"!house.CHouseId\"\r\n                  (click)=\"openModel(dialog, house)\">\r\n                  <i class=\"fas fa-edit\"></i>\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 分頁控制 -->\r\n      <div class=\"pagination-container mt-3\" *ngIf=\"totalPages > 1\">\r\n        <nav>\r\n          <ul class=\"pagination justify-content-center\">\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\">首頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\">上一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" *ngFor=\"let page of getVisiblePages()\" [class.active]=\"page === currentPage\">\r\n              <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\"\r\n                [disabled]=\"currentPage === totalPages\">下一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(totalPages)\"\r\n                [disabled]=\"currentPage === totalPages\">末頁</button>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 當沒有資料時顯示 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"flattenedHouses.length === 0 && houseChangeDates.length === 0\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <i class=\"fas fa-info-circle fa-2x mb-3\"></i>\r\n            <p>請選擇建案後查詢資料</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n\r\n    <!-- 載入中狀態 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"loading\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <div class=\"spinner-border\" role=\"status\">\r\n              <span class=\"sr-only\">載入中...</span>\r\n            </div>\r\n            <p class=\"mt-2\">資料載入中，請稍候...</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 批次設定對話框 -->\r\n<ng-template #batchSettingDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:600px; max-height: 90vh\">\r\n    <nb-card-header>\r\n      批次設定\r\n      <span *ngIf=\"selectedHouses.length > 0\" class=\"text-primary\">\r\n        - 已選擇 {{ selectedHouses.length }} 個戶別\r\n      </span>\r\n      <span *ngIf=\"selectedBuildingForBatch && selectedHouses.length === 0\">\r\n        - {{ selectedBuildingForBatch.name }}\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 時間設定 -->\r\n      <div class=\"form-group\">\r\n        <label>開放時間起訖 <span class=\"text-red-600\">*</span></label>\r\n        <div class=\"d-flex align-items-center\">\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchStartDate\"\r\n              [(ngModel)]=\"batchSettings.startDate\">\r\n            <nb-datepicker #batchStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <span class=\"mx-2\">~</span>\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchEndDate\"\r\n              [(ngModel)]=\"batchSettings.endDate\">\r\n            <nb-datepicker #batchEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 選擇範圍 -->\r\n      <div class=\"form-group\">\r\n        <label>適用範圍</label>\r\n        <div class=\"selection-options\">\r\n          <!-- 如果有已選擇的戶別，顯示已選擇的資訊 -->\r\n          <div *ngIf=\"selectedHouses.length > 0\" class=\"alert alert-info\">\r\n            <h6>將套用到已選擇的 {{ selectedHouses.length }} 個戶別：</h6>\r\n            <div class=\"selected-houses-preview\">\r\n              <span *ngFor=\"let house of selectedHouses.slice(0, 10); let i = index\"\r\n                class=\"badge badge-primary mr-1 mb-1\">\r\n                {{ house.CHouseHold }} ({{ house.CBuildingName }}-{{ house.CFloor }}F)\r\n              </span>\r\n              <span *ngIf=\"selectedHouses.length > 10\" class=\"text-muted\">\r\n                ...等 {{ selectedHouses.length - 10 }} 個\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 如果沒有已選擇的戶別，顯示選擇選項 -->\r\n          <div *ngIf=\"selectedHouses.length === 0\">\r\n            <nb-checkbox [(ngModel)]=\"batchSettings.applyToAll\">\r\n              全部戶別 ({{ flattenedHouses.length }} 個)\r\n            </nb-checkbox>\r\n            <div class=\"mt-2\" *ngIf=\"!batchSettings.applyToAll && selectedBuildingForBatch\">\r\n              <div class=\"floor-selection\" *ngFor=\"let floor of selectedBuildingForBatch.floors\">\r\n                <nb-checkbox [(ngModel)]=\"floor.selected\" (ngModelChange)=\"onFloorSelectionChange(floor)\">\r\n                  {{ floor.floorNumber }}F ({{ floor.houses.length }} 戶)\r\n                </nb-checkbox>\r\n                <div class=\"house-selection ml-4\" *ngIf=\"floor.selected\">\r\n                  <nb-checkbox *ngFor=\"let house of floor.houses\" [(ngModel)]=\"house.selected\"\r\n                    [disabled]=\"!house.CHouseId\">\r\n                    {{ house.CHouseHold }} ({{ house.CBuildingName }})\r\n                  </nb-checkbox>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-end\">\r\n      <button class=\"btn btn-secondary me-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onBatchSubmit(ref)\">\r\n        批次設定\r\n        <span *ngIf=\"selectedHouses.length > 0\">({{ selectedHouses.length }} 個戶別)</span>\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      您正在編輯 <span class=\"text-green-600\">{{selectedHouseChangeDate.CHouseHold}} -\r\n        {{selectedHouseChangeDate.CFloor}}F</span> 的選樣開放時段\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cFloorFrom\" class=\"label col-3\">開放時間起訖<span class=\"text-red-600\">*</span>\r\n        </label>\r\n        <nb-form-field class=\"ml-3\">\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeStartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeStartDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeStartDate\">\r\n          <nb-datepicker #changeStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n        <label for=\"CChangeStartDate\" class=\"label col-1\"> ~\r\n        </label>\r\n        <nb-form-field>\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeEndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeEndDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeEndDate\">\r\n          <nb-datepicker #changeEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AASvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;;;ICgBtEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IASAR,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAK,WAAA,CAAkB;IACpET,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,WAAA,MACF;;;;;IA0CJT,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApCH,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAU,iBAAA,CAAAC,MAAA,CAAAC,qBAAA,GAA6B;;;;;;IAH9FZ,EADF,CAAAC,cAAA,cAAwE,iBACkB;IAAhDD,EAAA,CAAAa,UAAA,mBAAAC,mEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAQ,qBAAA,EAAuB;IAAA,EAAC;IACvEnB,EAAA,CAAAoB,SAAA,YAA8G;IAC9GpB,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9BH,EAAA,CAAAqB,UAAA,IAAAC,iDAAA,mBAA+D;IAEnEtB,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAJaH,EAAA,CAAAM,SAAA,GAA8C;IAACN,EAA/C,CAAAuB,WAAA,qBAAAZ,MAAA,CAAAa,mBAAA,CAA8C,kBAAAb,MAAA,CAAAa,mBAAA,CAA4C;IAEpExB,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAc,gBAAA,GAAwB;;;;;IA8BzDzB,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAsB,QAAA,CAAe;IAC9D1B,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAmB,QAAA,OACF;;;;;;IAeA1B,EAAA,CAAAC,cAAA,iBAAqG;IAAvDD,EAAA,CAAAa,UAAA,mBAAAc,6EAAA;MAAA3B,EAAA,CAAAe,aAAA,CAAAa,GAAA;MAAA,MAAAjB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAkB,eAAA,EAAiB;IAAA,EAAC;IACvE7B,EAAA,CAAAoB,SAAA,YAAiC;IAAApB,EAAA,CAAAE,MAAA,gCACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA1CXH,EAHN,CAAAC,cAAA,cAA4G,cAClE,cACP,wBACD;IAC1BD,EAAA,CAAAoB,SAAA,kBAAkD;IAClDpB,EAAA,CAAAC,cAAA,gBAC+B;IADSD,EAAA,CAAA8B,gBAAA,2BAAAC,0EAAAC,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAwB,aAAA,CAAAC,aAAA,EAAAJ,MAAA,MAAArB,MAAA,CAAAwB,aAAA,CAAAC,aAAA,GAAAJ,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAyC;IAC/EhC,EAAA,CAAAa,UAAA,2BAAAkB,0EAAA;MAAA/B,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBP,MAAA,CAAA0B,QAAA,EAAU;IAAA,EAAC;IAElCrC,EAHI,CAAAG,YAAA,EAC+B,EACjB,EACZ;IAGJH,EADF,CAAAC,cAAA,cAA+B,oBAEd;IADeD,EAAA,CAAA8B,gBAAA,2BAAAQ,8EAAAN,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAwB,aAAA,CAAAI,YAAA,EAAAP,MAAA,MAAArB,MAAA,CAAAwB,aAAA,CAAAI,YAAA,GAAAP,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAwC;IAAChC,EAAA,CAAAa,UAAA,4BAAA2B,+EAAA;MAAAxC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAkBP,MAAA,CAAA0B,QAAA,EAAU;IAAA,EAAC;IAElGrC,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,qBAA0B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA4B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAA+B,qBAEd;IADeD,EAAA,CAAA8B,gBAAA,2BAAAW,+EAAAT,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAwB,aAAA,CAAAO,WAAA,EAAAV,MAAA,MAAArB,MAAA,CAAAwB,aAAA,CAAAO,WAAA,GAAAV,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAuC;IAAChC,EAAA,CAAAa,UAAA,4BAAA8B,gFAAA;MAAA3C,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAkBP,MAAA,CAAA0B,QAAA,EAAU;IAAA,EAAC;IAEjGrC,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAqB,UAAA,KAAAuB,uDAAA,wBAAiE;IAIrE5C,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,eAA+B,qBAC2E;IAA1ED,EAAA,CAAA8B,gBAAA,2BAAAe,+EAAAb,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAmC,QAAA,EAAAd,MAAA,MAAArB,MAAA,CAAAmC,QAAA,GAAAd,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAsB;IAAChC,EAAA,CAAAa,UAAA,4BAAAkC,gFAAA;MAAA/C,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAtB,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAkBP,MAAA,CAAAqC,gBAAA,EAAkB;IAAA,EAAC;IACxFhD,EAAA,CAAAC,cAAA,qBAAwB;IAAAD,EAAA,CAAAE,MAAA,uBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAAgC,eACF;IAC1BD,EAAA,CAAAqB,UAAA,KAAA4B,oDAAA,qBAAqG;IAM7GjD,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IAjDwFH,EAAA,CAAAI,UAAA,gBAAA8C,SAAA,CAAa;IAK3DlD,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAwB,aAAA,CAAAC,aAAA,CAAyC;IAMrDpC,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAwB,aAAA,CAAAI,YAAA,CAAwC;IAYxCvC,EAAA,CAAAM,SAAA,IAAuC;IAAvCN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAwB,aAAA,CAAAO,WAAA,CAAuC;IAGtC1C,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAyC,eAAA,CAAkB;IAOnBpD,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAmC,QAAA,CAAsB;IACvC9C,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAI,UAAA,aAAY;IACZJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IAMmDJ,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAc,gBAAA,GAAwB;;;;;IAqBnGzB,EAAA,CAAAC,cAAA,eAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,MAAA,CAAA0C,cAAA,CAAAC,MAAA,MACF;;;;;IA6DEtD,EAAA,CAAAC,cAAA,gBAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAuD,WAAA,OAAAC,SAAA,CAAAC,gBAAA,qBACF;;;;;IACAzD,EAAA,CAAAC,cAAA,gBAAyD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGnEH,EAAA,CAAAC,cAAA,gBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAuD,WAAA,OAAAC,SAAA,CAAAE,cAAA,qBACF;;;;;IACA1D,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAhBjEH,EAHJ,CAAAC,cAAA,SAC8C,SACxC,sBAE2C;IADhCD,EAAA,CAAA8B,gBAAA,2BAAA6B,sFAAA3B,MAAA;MAAA,MAAAwB,SAAA,GAAAxD,EAAA,CAAAe,aAAA,CAAA6C,IAAA,EAAAC,SAAA;MAAA7D,EAAA,CAAAkC,kBAAA,CAAAsB,SAAA,CAAAM,QAAA,EAAA9B,MAAA,MAAAwB,SAAA,CAAAM,QAAA,GAAA9B,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAA4B;IACvChC,EAAA,CAAAa,UAAA,2BAAA8C,sFAAA;MAAA3D,EAAA,CAAAe,aAAA,CAAA6C,IAAA;MAAA,MAAAjD,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBP,MAAA,CAAAoD,sBAAA,EAAwB;IAAA,EAAC;IAC9C/D,EAD+C,CAAAG,YAAA,EAAc,EACxD;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IAIFD,EAHA,CAAAqB,UAAA,KAAA2C,wDAAA,mBAA0D,KAAAC,wDAAA,mBAGD;IAC3DjE,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAIFD,EAHA,CAAAqB,UAAA,KAAA6C,wDAAA,mBAAwD,KAAAC,wDAAA,mBAGD;IACzDnE,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACyD;IACzDD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IAEHH,EADF,CAAAC,cAAA,UAAI,mBAEmC;IAAnCD,EAAA,CAAAa,UAAA,mBAAAuD,0EAAA;MAAA,MAAAZ,SAAA,GAAAxD,EAAA,CAAAe,aAAA,CAAA6C,IAAA,EAAAC,SAAA;MAAA,MAAAlD,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,MAAAoD,UAAA,GAAArE,EAAA,CAAAsE,WAAA;MAAA,OAAAtE,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA4D,SAAA,CAAAF,UAAA,EAAAb,SAAA,CAAwB;IAAA,EAAC;IAClCxD,EAAA,CAAAoB,SAAA,cAA2B;IAGjCpB,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;IA/BHH,EAAA,CAAAuB,WAAA,uBAAAiC,SAAA,CAAAM,QAAA,CAA2C;IAE5B9D,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAmD,gBAAA,YAAAK,SAAA,CAAAM,QAAA,CAA4B;IAAC9D,EAAA,CAAAI,UAAA,cAAAoD,SAAA,CAAAgB,QAAA,CAA4B;IAGpExE,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAU,iBAAA,CAAA8C,SAAA,CAAAiB,UAAA,CAAsB;IACtBzE,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAU,iBAAA,CAAA8C,SAAA,CAAAkB,aAAA,CAAyB;IACzB1E,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,kBAAA,KAAAiD,SAAA,CAAAmB,MAAA,MAAmB;IAEd3E,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,SAAAoD,SAAA,CAAAC,gBAAA,CAA4B;IAG5BzD,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAI,UAAA,UAAAoD,SAAA,CAAAC,gBAAA,CAA6B;IAG7BzD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,SAAAoD,SAAA,CAAAE,cAAA,CAA0B;IAG1B1D,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAAoD,SAAA,CAAAE,cAAA,CAA2B;IAGP1D,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAA4E,UAAA,CAAAjE,MAAA,CAAAkE,cAAA,CAAArB,SAAA,EAA+B;IACxDxD,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,MAAA,CAAAmE,aAAA,CAAAtB,SAAA,OACF;IAG+CxD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,cAAAoD,SAAA,CAAAgB,QAAA,CAA4B;;;;;;IAqB7ExE,EADF,CAAAC,cAAA,cAAmG,kBAC9C;IAAzBD,EAAA,CAAAa,UAAA,mBAAAkE,+EAAA;MAAA,MAAAC,QAAA,GAAAhF,EAAA,CAAAe,aAAA,CAAAkE,IAAA,EAAApB,SAAA;MAAA,MAAAlD,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAuE,QAAA,CAAAF,QAAA,CAAc;IAAA,EAAC;IAAChF,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFwDH,EAAA,CAAAuB,WAAA,WAAAyD,QAAA,KAAArE,MAAA,CAAAwE,WAAA,CAAqC;IAC7CnF,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAU,iBAAA,CAAAsE,QAAA,CAAU;;;;;;IAN7DhF,EAJR,CAAAC,cAAA,eAA8D,UACvD,cAC2C,cACe,kBACsB;IAArDD,EAAA,CAAAa,UAAA,mBAAAuE,0EAAA;MAAApF,EAAA,CAAAe,aAAA,CAAAsE,IAAA;MAAA,MAAA1E,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAuE,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAAgClF,EAAA,CAAAE,MAAA,mBAAE;IACnFF,EADmF,CAAAG,YAAA,EAAS,EACvF;IAEHH,EADF,CAAAC,cAAA,cAA2D,kBACoC;IAAnED,EAAA,CAAAa,UAAA,mBAAAyE,0EAAA;MAAAtF,EAAA,CAAAe,aAAA,CAAAsE,IAAA;MAAA,MAAA1E,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAuE,QAAA,CAAAvE,MAAA,CAAAwE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAAgCnF,EAAA,CAAAE,MAAA,yBAAG;IAClGF,EADkG,CAAAG,YAAA,EAAS,EACtG;IACLH,EAAA,CAAAqB,UAAA,IAAAkE,sDAAA,kBAAmG;IAIjGvF,EADF,CAAAC,cAAA,eAAoE,mBAExB;IADhBD,EAAA,CAAAa,UAAA,mBAAA2E,2EAAA;MAAAxF,EAAA,CAAAe,aAAA,CAAAsE,IAAA;MAAA,MAAA1E,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAuE,QAAA,CAAAvE,MAAA,CAAAwE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IACnBnF,EAAA,CAAAE,MAAA,0BAAG;IAC/CF,EAD+C,CAAAG,YAAA,EAAS,EACnD;IAEHH,EADF,CAAAC,cAAA,eAAoE,mBAExB;IADhBD,EAAA,CAAAa,UAAA,mBAAA4E,2EAAA;MAAAzF,EAAA,CAAAe,aAAA,CAAAsE,IAAA;MAAA,MAAA1E,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAuE,QAAA,CAAAvE,MAAA,CAAA+E,UAAA,CAAoB;IAAA,EAAC;IACd1F,EAAA,CAAAE,MAAA,oBAAE;IAIpDF,EAJoD,CAAAG,YAAA,EAAS,EAClD,EACF,EACD,EACF;;;;IAnBsBH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAuB,WAAA,aAAAZ,MAAA,CAAAwE,WAAA,OAAoC;IACRnF,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAwE,WAAA,OAA8B;IAE1DnF,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAuB,WAAA,aAAAZ,MAAA,CAAAwE,WAAA,OAAoC;IACMnF,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAwE,WAAA,OAA8B;IAEvDnF,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAgF,eAAA,GAAoB;IAGrC3F,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAuB,WAAA,aAAAZ,MAAA,CAAAwE,WAAA,KAAAxE,MAAA,CAAA+E,UAAA,CAA6C;IAE/D1F,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAwE,WAAA,KAAAxE,MAAA,CAAA+E,UAAA,CAAuC;IAErB1F,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAuB,WAAA,aAAAZ,MAAA,CAAAwE,WAAA,KAAAxE,MAAA,CAAA+E,UAAA,CAA6C;IAE/D1F,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAwE,WAAA,KAAAxE,MAAA,CAAA+E,UAAA,CAAuC;;;;;;IAnH3C1F,EALR,CAAAC,cAAA,cAAgE,cAE9B,cACiC,cAClC,sBACkD;IAA9DD,EAAA,CAAA8B,gBAAA,2BAAA8D,gFAAA5D,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAA8E,GAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAmF,SAAA,EAAA9D,MAAA,MAAArB,MAAA,CAAAmF,SAAA,GAAA9D,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAuB;IAAChC,EAAA,CAAAa,UAAA,2BAAA+E,gFAAA;MAAA5F,EAAA,CAAAe,aAAA,CAAA8E,GAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBP,MAAA,CAAAoF,iBAAA,EAAmB;IAAA,EAAC;IACxE/F,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAC,cAAA,iBACqD;IAAnDD,EAAA,CAAAa,UAAA,mBAAAmF,mEAAA;MAAAhG,EAAA,CAAAe,aAAA,CAAA8E,GAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAsF,gBAAA,EAAkB;IAAA,EAAC;IAC5BjG,EAAA,CAAAoB,SAAA,YAAgC;IAAApB,EAAA,CAAAE,MAAA,gCAChC;IAAAF,EAAA,CAAAqB,UAAA,IAAA6E,iDAAA,mBAAuE;IAGzElG,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4G;IAAvBD,EAAA,CAAAa,UAAA,mBAAAsF,oEAAA;MAAAnG,EAAA,CAAAe,aAAA,CAAA8E,GAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAyF,UAAA,EAAY;IAAA,EAAC;IACzGpG,EAAA,CAAAoB,SAAA,aAA+B;IAACpB,EAAA,CAAAE,MAAA,sBAClC;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,MAAA,IAEF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAQIH,EALV,CAAAC,cAAA,eAA6B,iBACM,iBACH,UACtB,cACa,uBAC8D;IAA9DD,EAAA,CAAA8B,gBAAA,2BAAAuE,iFAAArE,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAA8E,GAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAmF,SAAA,EAAA9D,MAAA,MAAArB,MAAA,CAAAmF,SAAA,GAAA9D,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAuB;IAAChC,EAAA,CAAAa,UAAA,2BAAAwF,iFAAA;MAAArG,EAAA,CAAAe,aAAA,CAAA8E,GAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBP,MAAA,CAAAoF,iBAAA,EAAmB;IAAA,EAAC;IAC5E/F,EAD6E,CAAAG,YAAA,EAAc,EACtF;IACLH,EAAA,CAAAC,cAAA,cAA8D;IAA9CD,EAAA,CAAAa,UAAA,mBAAAyF,gEAAA;MAAAtG,EAAA,CAAAe,aAAA,CAAA8E,GAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA4F,IAAA,CAAK,YAAY,CAAC;IAAA,EAAC;IAC1CvG,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAoB,SAAA,aACoF;IACtFpB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAiE;IAAjDD,EAAA,CAAAa,UAAA,mBAAA2F,gEAAA;MAAAxG,EAAA,CAAAe,aAAA,CAAA8E,GAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA4F,IAAA,CAAK,eAAe,CAAC;IAAA,EAAC;IAC7CvG,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAoB,SAAA,aACuF;IACzFpB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAyD;IAA1CD,EAAA,CAAAa,UAAA,mBAAA4F,gEAAA;MAAAzG,EAAA,CAAAe,aAAA,CAAA8E,GAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA4F,IAAA,CAAK,QAAQ,CAAC;IAAA,EAAC;IACrCvG,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAoB,SAAA,aACgF;IAClFpB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAoE;IAApDD,EAAA,CAAAa,UAAA,mBAAA6F,gEAAA;MAAA1G,EAAA,CAAAe,aAAA,CAAA8E,GAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA4F,IAAA,CAAK,kBAAkB,CAAC;IAAA,EAAC;IAChDvG,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAoB,SAAA,aAC0F;IAC5FpB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAkE;IAAlDD,EAAA,CAAAa,UAAA,mBAAA8F,gEAAA;MAAA3G,EAAA,CAAAe,aAAA,CAAA8E,GAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA4F,IAAA,CAAK,gBAAgB,CAAC;IAAA,EAAC;IAC9CvG,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAoB,SAAA,aACwF;IAC1FpB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,cAAe;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAErBF,EAFqB,CAAAG,YAAA,EAAK,EACnB,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAqB,UAAA,KAAAuF,gDAAA,mBAC8C;IAkCpD5G,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAGNH,EAAA,CAAAqB,UAAA,KAAAwF,iDAAA,oBAA8D;IAuBhE7G,EAAA,CAAAG,YAAA,EAAM;;;;IAxHeH,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAmF,SAAA,CAAuB;IAGQ9F,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,OAAwC;IAG3EtD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,KAA+B;IAIItD,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAmG,cAAA,CAAAxD,MAAA,OAAwC;IAKpFtD,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAA+G,kBAAA,oBAAApG,MAAA,CAAAwE,WAAA,QAAAxE,MAAA,CAAAmC,QAAA,aAAAnC,MAAA,CAAAqG,IAAA,CAAAC,GAAA,CAAAtG,MAAA,CAAAwE,WAAA,GAAAxE,MAAA,CAAAmC,QAAA,EAAAnC,MAAA,CAAAmG,cAAA,CAAAxD,MAAA,iBAAA3C,MAAA,CAAAmG,cAAA,CAAAxD,MAAA,aAEF;IAUmBtD,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAmF,SAAA,CAAuB;IAIb9F,EAAA,CAAAM,SAAA,GAA0E;IAC/FN,EADqB,CAAAuB,WAAA,eAAAZ,MAAA,CAAAuG,SAAA,qBAAAvG,MAAA,CAAAwG,aAAA,WAA0E,iBAAAxG,MAAA,CAAAuG,SAAA,qBAAAvG,MAAA,CAAAwG,aAAA,YAClB;IAIxDnH,EAAA,CAAAM,SAAA,GAA6E;IAClGN,EADqB,CAAAuB,WAAA,eAAAZ,MAAA,CAAAuG,SAAA,wBAAAvG,MAAA,CAAAwG,aAAA,WAA6E,iBAAAxG,MAAA,CAAAuG,SAAA,wBAAAvG,MAAA,CAAAwG,aAAA,YAClB;IAI3DnH,EAAA,CAAAM,SAAA,GAAsE;IAC3FN,EADqB,CAAAuB,WAAA,eAAAZ,MAAA,CAAAuG,SAAA,iBAAAvG,MAAA,CAAAwG,aAAA,WAAsE,iBAAAxG,MAAA,CAAAuG,SAAA,iBAAAvG,MAAA,CAAAwG,aAAA,YAClB;IAIpDnH,EAAA,CAAAM,SAAA,GAAgF;IACrGN,EADqB,CAAAuB,WAAA,eAAAZ,MAAA,CAAAuG,SAAA,2BAAAvG,MAAA,CAAAwG,aAAA,WAAgF,iBAAAxG,MAAA,CAAAuG,SAAA,2BAAAvG,MAAA,CAAAwG,aAAA,YAClB;IAI9DnH,EAAA,CAAAM,SAAA,GAA8E;IACnGN,EADqB,CAAAuB,WAAA,eAAAZ,MAAA,CAAAuG,SAAA,yBAAAvG,MAAA,CAAAwG,aAAA,WAA8E,iBAAAxG,MAAA,CAAAuG,SAAA,yBAAAvG,MAAA,CAAAwG,aAAA,YAClB;IAOjEnH,EAAA,CAAAM,SAAA,GAAoB;IAAAN,EAApB,CAAAI,UAAA,YAAAO,MAAA,CAAAyG,eAAA,CAAoB,iBAAAzG,MAAA,CAAA0G,cAAA,CAAuB;IAsC/BrH,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA+E,UAAA,KAAoB;;;;;IA+BxD1F,EAHN,CAAAC,cAAA,eAAoG,cACzF,mBACO,eACY;IACtBD,EAAA,CAAAoB,SAAA,aAA6C;IAC7CpB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IAIrBF,EAJqB,CAAAG,YAAA,EAAI,EACb,EACO,EACP,EACN;;;;;IAQIH,EALV,CAAAC,cAAA,eAA8C,cACnC,mBACO,eACY,eACoB,gBAClB;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAE,MAAA,gEAAY;IAIpCF,EAJoC,CAAAG,YAAA,EAAI,EAC5B,EACO,EACP,EACN;;;;;IASJH,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,2BAAAI,MAAA,CAAA0C,cAAA,CAAAC,MAAA,yBACF;;;;;IACAtD,EAAA,CAAAC,cAAA,WAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,QAAAI,MAAA,CAAA2G,wBAAA,CAAAC,IAAA,MACF;;;;;IA+BQvH,EAAA,CAAAC,cAAA,gBACwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAA+G,kBAAA,MAAAS,SAAA,CAAA/C,UAAA,QAAA+C,SAAA,CAAA9C,aAAA,OAAA8C,SAAA,CAAA7C,MAAA,QACF;;;;;IACA3E,EAAA,CAAAC,cAAA,gBAA4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gBAAAI,MAAA,CAAA0C,cAAA,CAAAC,MAAA,kBACF;;;;;IARFtD,EADF,CAAAC,cAAA,eAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,eAAqC;IAKnCD,EAJA,CAAAqB,UAAA,IAAAoG,gEAAA,oBACwC,IAAAC,gEAAA,mBAGoB;IAIhE1H,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAVAH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAO,kBAAA,sDAAAI,MAAA,CAAA0C,cAAA,CAAAC,MAAA,8BAAyC;IAEnBtD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA0C,cAAA,CAAAsE,KAAA,QAAgC;IAIjD3H,EAAA,CAAAM,SAAA,EAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,MAAgC;;;;;;IAiBnCtD,EAAA,CAAAC,cAAA,sBAC+B;IADiBD,EAAA,CAAA8B,gBAAA,2BAAA8F,+HAAA5F,MAAA;MAAA,MAAA6F,SAAA,GAAA7H,EAAA,CAAAe,aAAA,CAAA+G,IAAA,EAAAjE,SAAA;MAAA7D,EAAA,CAAAkC,kBAAA,CAAA2F,SAAA,CAAA/D,QAAA,EAAA9B,MAAA,MAAA6F,SAAA,CAAA/D,QAAA,GAAA9B,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAA4B;IAE1EhC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;;;;IAHkCH,EAAA,CAAAmD,gBAAA,YAAA0E,SAAA,CAAA/D,QAAA,CAA4B;IAC1E9D,EAAA,CAAAI,UAAA,cAAAyH,SAAA,CAAArD,QAAA,CAA4B;IAC5BxE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAA+H,kBAAA,MAAAF,SAAA,CAAApD,UAAA,QAAAoD,SAAA,CAAAnD,aAAA,OACF;;;;;IAJF1E,EAAA,CAAAC,cAAA,eAAyD;IACvDD,EAAA,CAAAqB,UAAA,IAAA2G,yFAAA,2BAC+B;IAGjChI,EAAA,CAAAG,YAAA,EAAM;;;;IAJ2BH,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAI,UAAA,YAAA6H,SAAA,CAAAC,MAAA,CAAe;;;;;;IAJhDlI,EADF,CAAAC,cAAA,eAAmF,sBACS;IAA7ED,EAAA,CAAA8B,gBAAA,2BAAAqG,2GAAAnG,MAAA;MAAA,MAAAiG,SAAA,GAAAjI,EAAA,CAAAe,aAAA,CAAAqH,IAAA,EAAAvE,SAAA;MAAA7D,EAAA,CAAAkC,kBAAA,CAAA+F,SAAA,CAAAnE,QAAA,EAAA9B,MAAA,MAAAiG,SAAA,CAAAnE,QAAA,GAAA9B,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAA4B;IAAChC,EAAA,CAAAa,UAAA,2BAAAsH,2GAAA;MAAA,MAAAF,SAAA,GAAAjI,EAAA,CAAAe,aAAA,CAAAqH,IAAA,EAAAvE,SAAA;MAAA,MAAAlD,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBP,MAAA,CAAA0H,sBAAA,CAAAJ,SAAA,CAA6B;IAAA,EAAC;IACvFjI,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAqB,UAAA,IAAAiH,2EAAA,mBAAyD;IAM3DtI,EAAA,CAAAG,YAAA,EAAM;;;;IATSH,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAAmD,gBAAA,YAAA8E,SAAA,CAAAnE,QAAA,CAA4B;IACvC9D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAA+H,kBAAA,MAAAE,SAAA,CAAAM,WAAA,SAAAN,SAAA,CAAAC,MAAA,CAAA5E,MAAA,cACF;IACmCtD,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAA6H,SAAA,CAAAnE,QAAA,CAAoB;;;;;IAL3D9D,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAqB,UAAA,IAAAmH,qEAAA,mBAAmF;IAWrFxI,EAAA,CAAAG,YAAA,EAAM;;;;IAX2CH,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA2G,wBAAA,CAAAmB,MAAA,CAAkC;;;;;;IAJnFzI,EADF,CAAAC,cAAA,UAAyC,sBACa;IAAvCD,EAAA,CAAA8B,gBAAA,2BAAA4G,+FAAA1G,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAA4H,IAAA;MAAA,MAAAhI,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAiI,aAAA,CAAAC,UAAA,EAAA7G,MAAA,MAAArB,MAAA,CAAAiI,aAAA,CAAAC,UAAA,GAAA7G,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAsC;IACjDhC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAqB,UAAA,IAAAyH,+DAAA,mBAAgF;IAalF9I,EAAA,CAAAG,YAAA,EAAM;;;;IAhBSH,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAiI,aAAA,CAAAC,UAAA,CAAsC;IACjD7I,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gCAAAI,MAAA,CAAAoI,eAAA,CAAAzF,MAAA,cACF;IACmBtD,EAAA,CAAAM,SAAA,EAA2D;IAA3DN,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAiI,aAAA,CAAAC,UAAA,IAAAlI,MAAA,CAAA2G,wBAAA,CAA2D;;;;;IAqBlFtH,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAxCH,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAO,kBAAA,MAAAI,MAAA,CAAA0C,cAAA,CAAAC,MAAA,yBAAiC;;;;;;IA1E7EtD,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,iCACA;IAGAF,EAHA,CAAAqB,UAAA,IAAA2H,yDAAA,oBAA6D,IAAAC,yDAAA,oBAGS;IAGxEjJ,EAAA,CAAAG,YAAA,EAAiB;IAIbH,EAHJ,CAAAC,cAAA,mBAAc,eAEY,YACf;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAEvDH,EADF,CAAAC,cAAA,gBAAuC,0BACJ;IAC/BD,EAAA,CAAAoB,SAAA,mBAAoD;IACpDpB,EAAA,CAAAC,cAAA,kBACwC;IAAtCD,EAAA,CAAA8B,gBAAA,2BAAAoH,mFAAAlH,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAoI,IAAA;MAAA,MAAAxI,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAiI,aAAA,CAAAQ,SAAA,EAAApH,MAAA,MAAArB,MAAA,CAAAiI,aAAA,CAAAQ,SAAA,GAAApH,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAqC;IADvChC,EAAA,CAAAG,YAAA,EACwC;IACxCH,EAAA,CAAAoB,SAAA,4BAAmE;IACrEpB,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,iBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,0BAAiC;IAC/BD,EAAA,CAAAoB,SAAA,mBAAoD;IACpDpB,EAAA,CAAAC,cAAA,kBACsC;IAApCD,EAAA,CAAA8B,gBAAA,2BAAAuH,mFAAArH,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAoI,IAAA;MAAA,MAAAxI,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAiI,aAAA,CAAAU,OAAA,EAAAtH,MAAA,MAAArB,MAAA,CAAAiI,aAAA,CAAAU,OAAA,GAAAtH,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAmC;IADrChC,EAAA,CAAAG,YAAA,EACsC;IACtCH,EAAA,CAAAoB,SAAA,4BAAiE;IAGvEpB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAwB,aACf;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnBH,EAAA,CAAAC,cAAA,gBAA+B;IAgB7BD,EAdA,CAAAqB,UAAA,KAAAkI,yDAAA,mBAAgE,KAAAC,yDAAA,mBAcvB;IAoB/CxJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAmD,mBACa;IAAvBD,EAAA,CAAAa,UAAA,mBAAA4I,4EAAA;MAAA,MAAAC,OAAA,GAAA1J,EAAA,CAAAe,aAAA,CAAAoI,IAAA,EAAAQ,SAAA;MAAA,MAAAhJ,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAiJ,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAAC1J,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzEH,EAAA,CAAAC,cAAA,mBAA6D;IAA7BD,EAAA,CAAAa,UAAA,mBAAAgJ,4EAAA;MAAA,MAAAH,OAAA,GAAA1J,EAAA,CAAAe,aAAA,CAAAoI,IAAA,EAAAQ,SAAA;MAAA,MAAAhJ,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAmJ,aAAA,CAAAJ,OAAA,CAAkB;IAAA,EAAC;IAC1D1J,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAqB,UAAA,KAAA0I,0DAAA,oBAAwC;IAG9C/J,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;;;IA3ECH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,KAA+B;IAG/BtD,EAAA,CAAAM,SAAA,EAA6D;IAA7DN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA2G,wBAAA,IAAA3G,MAAA,CAAA0C,cAAA,CAAAC,MAAA,OAA6D;IAWftD,EAAA,CAAAM,SAAA,IAA+B;IAA/BN,EAAA,CAAAI,UAAA,iBAAA4J,kBAAA,CAA+B;IAC5EhK,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAiI,aAAA,CAAAQ,SAAA,CAAqC;IAMQpJ,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,iBAAA6J,gBAAA,CAA6B;IAC1EjK,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAiI,aAAA,CAAAU,OAAA,CAAmC;IAWjCtJ,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,KAA+B;IAc/BtD,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,OAAiC;IAyBlCtD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAA0C,cAAA,CAAAC,MAAA,KAA+B;;;;;IAO5CtD,EAAA,CAAAC,cAAA,mBAA+C;IAM7CD,EALA,CAAAoB,SAAA,qBACiB,mBAGF,0BAGE;IACnBpB,EAAA,CAAAG,YAAA,EAAU;;;;;;IAKRH,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GACE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mDAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,wBAA2B,eACyB,iBACJ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC7E;IACRH,EAAA,CAAAC,cAAA,0BAA4B;IAC1BD,EAAA,CAAAoB,SAAA,mBAAoD;IACpDpB,EAAA,CAAAC,cAAA,kBAC8E;IAAvDD,EAAA,CAAA8B,gBAAA,2BAAAoI,mFAAAlI,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAoJ,IAAA;MAAA,MAAAxJ,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAyJ,uBAAA,CAAA3G,gBAAA,EAAAzB,MAAA,MAAArB,MAAA,CAAAyJ,uBAAA,CAAA3G,gBAAA,GAAAzB,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAsD;IAD7EhC,EAAA,CAAAG,YAAA,EAC8E;IAC9EH,EAAA,CAAAoB,SAAA,4BAAoE;IACtEpB,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,kBAAkD;IAACD,EAAA,CAAAE,MAAA,WACnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAoB,SAAA,mBAAoD;IACpDpB,EAAA,CAAAC,cAAA,kBAC4E;IAArDD,EAAA,CAAA8B,gBAAA,2BAAAuI,mFAAArI,MAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAoJ,IAAA;MAAA,MAAAxJ,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkC,kBAAA,CAAAvB,MAAA,CAAAyJ,uBAAA,CAAA1G,cAAA,EAAA1B,MAAA,MAAArB,MAAA,CAAAyJ,uBAAA,CAAA1G,cAAA,GAAA1B,MAAA;MAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;IAAA,EAAoD;IAD3EhC,EAAA,CAAAG,YAAA,EAC4E;IAC5EH,EAAA,CAAAoB,SAAA,4BAAkE;IAGxEpB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAsD,mBACiB;IAAvBD,EAAA,CAAAa,UAAA,mBAAAyJ,4EAAA;MAAA,MAAAC,OAAA,GAAAvK,EAAA,CAAAe,aAAA,CAAAoJ,IAAA,EAAAR,SAAA;MAAA,MAAAhJ,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAAiJ,OAAA,CAAAW,OAAA,CAAY;IAAA,EAAC;IAACvK,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,mBAAoE;IAAxBD,EAAA,CAAAa,UAAA,mBAAA2J,4EAAA;MAAA,MAAAD,OAAA,GAAAvK,EAAA,CAAAe,aAAA,CAAAoJ,IAAA,EAAAR,SAAA;MAAA,MAAAhJ,MAAA,GAAAX,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASP,MAAA,CAAA8J,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IAACvK,EAAA,CAAAE,MAAA,oBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAChE,EACT;;;;;;IA3B6BH,EAAA,CAAAM,SAAA,GACE;IADFN,EAAA,CAAA+H,kBAAA,KAAApH,MAAA,CAAAyJ,uBAAA,CAAA3F,UAAA,SAAA9D,MAAA,CAAAyJ,uBAAA,CAAAzF,MAAA,MACE;IAQoC3E,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,iBAAAsK,mBAAA,CAAgC;IAC9E1K,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAyJ,uBAAA,CAAA3G,gBAAA,CAAsD;IAOVzD,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAI,UAAA,iBAAAuK,iBAAA,CAA8B;IAC1E3K,EAAA,CAAAmD,gBAAA,YAAAxC,MAAA,CAAAyJ,uBAAA,CAAA1G,cAAA,CAAoD;;;ADrVrF,OAAM,MAAOkH,0BAA2B,SAAQ9K,aAAa;EAK3D+K,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA6C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAUtD;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAxI,eAAe,GAAa,EAAE;IAE9B;IACA,KAAAjB,aAAa,GAAkB;MAC7BC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfmJ,cAAc,EAAE;KACjB;IAED;IACA,KAAAjD,aAAa,GAAkB;MAC7BQ,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbT,UAAU,EAAE,IAAI;MAChBiD,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClB1I,cAAc,EAAE;KACjB;IAED,KAAAiE,wBAAwB,GAAyB,IAAI;IAErD;IACA,KAAAyB,eAAe,GAAqB,EAAE;IACtC,KAAAjC,cAAc,GAAqB,EAAE;IACrC,KAAAM,eAAe,GAAqB,EAAE;IACtC,KAAA/D,cAAc,GAAqB,EAAE;IACrC,KAAAyC,SAAS,GAAY,KAAK;IAC1B,KAAAkG,OAAO,GAAY,KAAK;IAExB;IACA,KAAA7G,WAAW,GAAW,CAAC;IACd,KAAArC,QAAQ,GAAW,EAAE;IAC9B,KAAA4C,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAwB,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAmB,KAAK;IAErC;IACA,KAAAH,IAAI,GAAGA,IAAI;IAlFT,IAAI,CAACoD,uBAAuB,GAAG;MAC7B3G,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBiB,MAAM,EAAEzB,SAAS;MACjBuB,UAAU,EAAE,EAAE;MACdD,QAAQ,EAAEtB;KACX;IAED,IAAI,CAACmI,aAAa,CAACY,OAAO,EAAE,CAACC,IAAI,CAC/BzM,GAAG,CAAE0M,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAACd,eAAe,GAAGa,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAqESC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAACnB,gBAAgB,CAAC,CAAC,CAAC;MAC/C9H,gBAAgB,EAAEP,SAAS;MAC3BQ,cAAc,EAAER;KACjB;IACD,IAAI,CAACyJ,gBAAgB,EAAE;EACzB;EAEApI,SAASA,CAACqI,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACrI,QAAQ,EAAE;MACjB,IAAI,CAAC4F,uBAAuB,GAAG;QAC7B,GAAGyC,IAAI;QACPpJ,gBAAgB,EAAEoJ,IAAI,CAACpJ,gBAAgB,GAAG,IAAIqJ,IAAI,CAACD,IAAI,CAACpJ,gBAAgB,CAAC,GAAGP,SAAS;QACrFQ,cAAc,EAAEmJ,IAAI,CAACnJ,cAAc,GAAG,IAAIoJ,IAAI,CAACD,IAAI,CAACnJ,cAAc,CAAC,GAAGR;OACvE;MACD,IAAI,CAAC6H,aAAa,CAACgC,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOrN,MAAM,CAACqN,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAzC,QAAQA,CAACmC,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAAClC,KAAK,CAACmC,aAAa,CAAC9J,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC0H,OAAO,CAACqC,aAAa,CAAC,IAAI,CAACpC,KAAK,CAACmC,aAAa,CAAC;MACpD;IACF;IACA,MAAME,KAAK,GAAG;MACZ9I,QAAQ,EAAE,IAAI,CAAC4F,uBAAuB,CAAC5F,QAAQ;MAC/Cf,gBAAgB,EAAE,IAAI,CAACuJ,UAAU,CAAC,IAAI,CAAC5C,uBAAuB,CAAC3G,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAACsJ,UAAU,CAAC,IAAI,CAAC5C,uBAAuB,CAAC1G,cAAc;KAC5E;IAED,IAAI,CAACwH,aAAa,CAACqC,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAAChB,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACzC,OAAO,CAAC0C,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAjB,gBAAgBA,CAAA;IACd,IAAI,CAACxB,iBAAiB,CAAC0C,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAACtB,IAAI,CAC7EzM,GAAG,CAAC0M,GAAG,IAAG;MACR,MAAM2B,OAAO,GAAG3B,GAAG,CAAC4B,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAACxK,MAAM,IAAI6I,GAAG,CAACsB,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChD1N,cAAc,EAAE0N,KAAK,CAAC1N,cAAc;UACpC2N,GAAG,EAAED,KAAK,CAACC;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAAC7C,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAI8C,KAAK,GAAG,IAAI,CAACJ,oBAAoB,CAACK,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAAC7C,eAAe,CAAC;UAC1F,IAAI,CAACkB,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuB,oBAAoB,CAACI,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAAC5B,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuB,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMO,WAAW,GAAG,IAAI,CAAC/B,WAAW,EAAEC,kBAAkB,EAAE0B,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACZ,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAACrB,SAAS,EAAE;EACf;EAEAkC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvC,MAAMC,KAAK,GAAGD,KAAK,CAACnK,MAAM;QAC1B,IAAI,CAAC+J,SAAS,CAACK,KAAK,CAAC,EAAE;UAAE;UACvBL,SAAS,CAACK,KAAK,CAAC,GAAG,EAAE;QACvB;QACAL,SAAS,CAACK,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBvK,UAAU,EAAEmK,SAAS,CAACnK,UAAU;UAChCC,aAAa,EAAEoK,KAAK,CAACpK,aAAa,IAAI,KAAK;UAC3CF,QAAQ,EAAEsK,KAAK,CAACtK,QAAQ;UACxBG,MAAM,EAAEmK,KAAK,CAACnK,MAAM;UACpBlB,gBAAgB,EAAEqL,KAAK,CAACrL,gBAAgB;UACxCC,cAAc,EAAEoL,KAAK,CAACpL;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC+E,MAAM,CAAClC,IAAI,CAAC,CAAC0I,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAAC1G,MAAM,CAACwF,GAAG,CAAEc,KAAU,IAAI;MAChE,OAAO,IAAI,CAACK,UAAU,CAACnB,GAAG,CAAEW,SAAc,IAAI;QAC5C,MAAME,KAAK,GAAGJ,SAAS,CAACK,KAAK,CAAC,CAACM,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAAC7K,UAAU,KAAKmK,SAAS,CAAC;QAC5F,OAAOE,KAAK,IAAI;UACdrK,UAAU,EAAEmK,SAAS;UACrBlK,aAAa,EAAE,KAAK;UACpBF,QAAQ,EAAE,IAAI;UACdG,MAAM,EAAEoK,KAAK;UACbtL,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOyL,MAAM;EACf;EAEAI,sBAAsBA,CAACd,GAAU;IAC/B,MAAMe,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5ChB,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBc,aAAa,CAACC,GAAG,CAACf,SAAS,CAACnK,UAAU,CAAC;MACvCmK,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvCU,SAAS,CAACG,GAAG,CAACb,KAAK,CAACnK,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAAC8D,MAAM,GAAGmH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLjH,MAAM,EAAEmH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACtD,WAAW,CAAC/I,gBAAgB,IAAI,IAAI,CAAC+I,WAAW,CAAC9I,cAAc,EAAE;MACxE,MAAM0F,SAAS,GAAG,IAAI0D,IAAI,CAAC,IAAI,CAACN,WAAW,CAAC/I,gBAAgB,CAAC;MAC7D,MAAM6F,OAAO,GAAG,IAAIwD,IAAI,CAAC,IAAI,CAACN,WAAW,CAAC9I,cAAc,CAAC;MACzD,IAAI0F,SAAS,IAAIE,OAAO,IAAIF,SAAS,GAAGE,OAAO,EAAE;QAC/C,IAAI,CAAC0B,OAAO,CAACqC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEA;EACA0C,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACC,cAAc,EAAE;IACrB;IACA,IAAI,CAACrC,kBAAkB,EAAE;EAC3B;EAEA;EACAqC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACxE,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC3C,eAAe,GAAG,EAAE;IACzB,IAAI,CAACjC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACM,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC/D,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAAClB,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfmJ,cAAc,EAAE;KACjB;IAED;IACA,IAAI,CAAC/F,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC8F,gBAAgB,GAAG,EAAE;IAE1B;IACA,IAAI,CAACzG,WAAW,GAAG,CAAC;IACpB,IAAI,CAACO,UAAU,GAAG,CAAC;IAEnB;IACA,IAAI,CAACiG,eAAe,GAAG,EAAE;IACzB,IAAI,CAACvI,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,CAAC8D,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC5B;EAEAwG,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC,IAAI,CAACnB,WAAW,CAACC,kBAAkB,EAAE0B,GAAG,EAAE;MAC7C,IAAI,CAACnC,OAAO,GAAG,KAAK;MACpB;IACF;IAEA,IAAI,CAACA,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC8D,cAAc,EAAE;IACrB,IAAI,CAAC5E,aAAa,CAACiF,mCAAmC,CAAC;MACrD3C,IAAI,EAAE;QACJ4C,YAAY,EAAE,IAAI,CAAC5D,WAAW,CAACC,kBAAkB,CAAC0B,GAAG;QACrD1K,gBAAgB,EAAE,IAAI,CAAC+I,WAAW,CAAC/I,gBAAgB,GAAG,IAAI,CAACuJ,UAAU,CAAC,IAAI,CAACR,WAAW,CAAC/I,gBAAgB,CAAC,GAAGP,SAAS;QACpHQ,cAAc,EAAE,IAAI,CAAC8I,WAAW,CAAC9I,cAAc,GAAG,IAAI,CAACsJ,UAAU,CAAC,IAAI,CAACR,WAAW,CAAC9I,cAAc,CAAC,GAAGR;;KAExG,CAAC,CAACoJ,SAAS,CAACH,GAAG,IAAG;MACjB,IAAI,CAACH,OAAO,GAAG,KAAK;MACpB,IAAIG,GAAG,CAAC4B,OAAO,IAAI5B,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACwC,gBAAgB,GAAG9D,GAAG,CAAC4B,OAAO,GAAG5B,GAAG,CAAC4B,OAAO,GAAG,EAAE;QACtD,IAAI5B,GAAG,CAAC4B,OAAO,EAAE;UACf,IAAI,CAACkC,gBAAgB,GAAG,CAAC,GAAG9D,GAAG,CAAC4B,OAAO,CAAC;UACxC,IAAI,CAACwB,sBAAsB,CAACpD,GAAG,CAAC4B,OAAO,CAAC;UACxC,IAAI,CAACmC,mBAAmB,GAAG,IAAI,CAAC1B,8BAA8B,CAACrC,GAAG,CAAC4B,OAAO,CAAC;UAC3E;UACA,IAAI,CAACsC,mBAAmB,CAAClE,GAAG,CAAC4B,OAAO,CAAC;UACrC;UACA,IAAI,CAACuC,oBAAoB,CAACnE,GAAG,CAAC4B,OAAO,CAAC;QACxC;MACF;IACF,CAAC,CAAC;EACJ;EAEA;EACAsC,mBAAmBA,CAACE,IAA6B;IAC/C,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAyC;IAEpEF,IAAI,CAAC5B,OAAO,CAACC,SAAS,IAAG;MACvB,MAAM8B,SAAS,GAAG9B,SAAS,CAACnK,UAAU,IAAI,EAAE,CAAC,CAAC;MAE9CmK,SAAS,CAACC,OAAO,EAAEF,OAAO,CAACG,KAAK,IAAG;QACjC,MAAM6B,YAAY,GAAG7B,KAAK,CAACpK,aAAa,IAAI,KAAK,CAAC,CAAC;QACnD,MAAMqK,KAAK,GAAGD,KAAK,CAACnK,MAAM,IAAI,CAAC;QAE/B,IAAI,CAAC6L,WAAW,CAACI,GAAG,CAACD,YAAY,CAAC,EAAE;UAClCH,WAAW,CAACK,GAAG,CAACF,YAAY,EAAE,IAAIF,GAAG,EAA4B,CAAC;QACpE;QAEA,MAAMK,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACJ,YAAY,CAAE;QAE/C,IAAI,CAACG,QAAQ,CAACF,GAAG,CAAC7B,KAAK,CAAC,EAAE;UACxB+B,QAAQ,CAACD,GAAG,CAAC9B,KAAK,EAAE,EAAE,CAAC;QACzB;QAEA+B,QAAQ,CAACC,GAAG,CAAChC,KAAK,CAAE,CAACC,IAAI,CAAC;UACxBvK,UAAU,EAAEiM,SAAS;UAAE;UACvBhM,aAAa,EAAEiM,YAAY;UAAE;UAC7BnM,QAAQ,EAAEsK,KAAK,CAACtK,QAAQ,IAAI,CAAC;UAC7BG,MAAM,EAAEoK,KAAK;UACbtL,gBAAgB,EAAEqL,KAAK,CAACrL,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEoL,KAAK,CAACpL,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC4H,cAAc,GAAGkE,KAAK,CAACC,IAAI,CAACW,WAAW,CAAC1C,OAAO,EAAE,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC0C,YAAY,EAAEG,QAAQ,CAAC,KAAI;MACvF,MAAMrI,MAAM,GAAiBmH,KAAK,CAACC,IAAI,CAACiB,QAAQ,CAAChD,OAAO,EAAE,CAAC,CACxDvH,IAAI,CAAC,CAAC,CAAC0I,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;MAAA,CAC1BhB,GAAG,CAAC,CAAC,CAAC1F,WAAW,EAAEL,MAAM,CAAC,MAAM;QAC/BK,WAAW;QACXL,MAAM,EAAEA,MAAM,CAAC3B,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAI;UAC3B;UACA,IAAID,CAAC,CAACxK,UAAU,KAAKyK,CAAC,CAACzK,UAAU,EAAE;YACjC,OAAOwK,CAAC,CAACxK,UAAU,CAACuM,aAAa,CAAC9B,CAAC,CAACzK,UAAU,CAAC;UACjD;UACA,OAAOwK,CAAC,CAACtK,MAAM,GAAGuK,CAAC,CAACvK,MAAM;QAC5B,CAAC,CAAC;QACFb,QAAQ,EAAE;OACX,CAAC,CAAC;MAEL,OAAO;QACLyD,IAAI,EAAEoJ,YAAY;QAClBlI,MAAM;QACN3E,QAAQ,EAAE;OACX;IACH,CAAC,CAAC,CAACyC,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC1H,IAAI,CAACyJ,aAAa,CAAC9B,CAAC,CAAC3H,IAAI,CAAC,CAAC;IAE/C;IACA,IAAI,CAACoE,eAAe,GAAG,IAAI,CAACD,cAAc,CAACuC,GAAG,CAACgD,EAAE,IAAIA,EAAE,CAAC1J,IAAI,CAAC;IAC7D,IAAI,CAAC2J,qBAAqB,EAAE;EAC9B;EAEA;EACAA,qBAAqBA,CAAA;IACnB,MAAM1B,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,IAAI,CAAC/D,cAAc,CAACiD,OAAO,CAACwC,QAAQ,IAAG;MACrC,IAAI,CAAC,IAAI,CAACvF,gBAAgB,IAAIuF,QAAQ,CAAC5J,IAAI,KAAK,IAAI,CAACqE,gBAAgB,EAAE;QACrEuF,QAAQ,CAAC1I,MAAM,CAACkG,OAAO,CAACI,KAAK,IAAG;UAC9BS,SAAS,CAACG,GAAG,CAACZ,KAAK,CAACxG,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACnF,eAAe,GAAGwM,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC,CAACjJ,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpE;EAEA;EACAmC,gBAAgBA,CAAA;IACd;IACA,IAAI,CAAC/N,cAAc,CAACsL,OAAO,CAACG,KAAK,IAAIA,KAAK,CAAChL,QAAQ,GAAG,KAAK,CAAC;IAC5D,IAAI,CAACT,cAAc,GAAG,EAAE;IACxB,IAAI,CAACyC,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAACX,WAAW,GAAG,CAAC;IAEpB;IACA,IAAI,CAAChD,aAAa,CAACO,WAAW,GAAG,EAAE;IAEnC;IACA,IAAI,CAACwO,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAAC/O,aAAa,CAAC0J,cAAc,GAAG,IAAI,CAACD,gBAAgB;IACzD,IAAI,CAACvJ,QAAQ,EAAE;EACjB;EAIA;EACAgP,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC3F,cAAc,CAAC4F,MAAM,CAACH,QAAQ,IAAG;MAC3C;MACA,IAAI,IAAI,CAACvF,gBAAgB,IAAIuF,QAAQ,CAAC5J,IAAI,KAAK,IAAI,CAACqE,gBAAgB,EAAE;QACpE,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAACzJ,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMmP,OAAO,GAAG,IAAI,CAACpP,aAAa,CAACC,aAAa,CAACoP,WAAW,EAAE;QAC9D,MAAMC,gBAAgB,GAAGN,QAAQ,CAAC1I,MAAM,CAACiJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAAC7G,MAAM,CAACwJ,IAAI,CAAC5C,KAAK,IACrBA,KAAK,CAACrK,UAAU,CAAC+M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IAChDzC,KAAK,CAACpK,aAAa,CAAC8M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,CACpD,CACF;QACD,IAAI,CAACE,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACtP,aAAa,CAACI,YAAY,EAAE;QACnC,MAAMqP,iBAAiB,GAAGT,QAAQ,CAAC1I,MAAM,CAACiJ,IAAI,CAAC3C,KAAK,IAClDA,KAAK,CAAC7G,MAAM,CAACwJ,IAAI,CAAC5C,KAAK,IAAI,IAAI,CAAC+C,mBAAmB,CAAC/C,KAAK,CAAC,CAAC,CAC5D;QACD,IAAI,CAAC8C,iBAAiB,EAAE;UACtB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACzP,aAAa,CAACO,WAAW,EAAE;QAClC,MAAM6F,WAAW,GAAGuJ,QAAQ,CAAC,IAAI,CAAC3P,aAAa,CAACO,WAAW,CAAC;QAC5D,MAAMqP,gBAAgB,GAAGZ,QAAQ,CAAC1I,MAAM,CAACiJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAACxG,WAAW,KAAKA,WAAW,CAClC;QACD,IAAI,CAACwJ,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CAAC9D,GAAG,CAACkD,QAAQ,IAAG;MAChB;MACA,MAAMa,gBAAgB,GAAG;QAAE,GAAGb;MAAQ,CAAE;MACxCa,gBAAgB,CAACvJ,MAAM,GAAG0I,QAAQ,CAAC1I,MAAM,CAAC6I,MAAM,CAACvC,KAAK,IAAG;QACvD;QACA,IAAI,IAAI,CAAC5M,aAAa,CAACO,WAAW,EAAE;UAClC,MAAM6F,WAAW,GAAGuJ,QAAQ,CAAC,IAAI,CAAC3P,aAAa,CAACO,WAAW,CAAC;UAC5D,IAAIqM,KAAK,CAACxG,WAAW,KAAKA,WAAW,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QAEA;QACA,MAAM0J,cAAc,GAAGlD,KAAK,CAAC7G,MAAM,CAACwJ,IAAI,CAAC5C,KAAK,IAAG;UAC/C;UACA,IAAI,IAAI,CAAC3M,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMmP,OAAO,GAAG,IAAI,CAACpP,aAAa,CAACC,aAAa,CAACoP,WAAW,EAAE;YAC9D,IAAI,CAAC1C,KAAK,CAACrK,UAAU,CAAC+M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzC,KAAK,CAACpK,aAAa,CAAC8M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAACpP,aAAa,CAACI,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACsP,mBAAmB,CAAC/C,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOmD,cAAc;MACvB,CAAC,CAAC,CAAChE,GAAG,CAACc,KAAK,IAAG;QACb;QACA,MAAMmD,aAAa,GAAG;UAAE,GAAGnD;QAAK,CAAE;QAClCmD,aAAa,CAAChK,MAAM,GAAG6G,KAAK,CAAC7G,MAAM,CAACoJ,MAAM,CAACxC,KAAK,IAAG;UACjD;UACA,IAAI,IAAI,CAAC3M,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMmP,OAAO,GAAG,IAAI,CAACpP,aAAa,CAACC,aAAa,CAACoP,WAAW,EAAE;YAC9D,IAAI,CAAC1C,KAAK,CAACrK,UAAU,CAAC+M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzC,KAAK,CAACpK,aAAa,CAAC8M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAACpP,aAAa,CAACI,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACsP,mBAAmB,CAAC/C,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOoD,aAAa;MACtB,CAAC,CAAC;MAEF,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ;EAEA;EACQH,mBAAmBA,CAAC/C,KAAqB;IAC/C,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;IAEzC,QAAQ,IAAI,CAAC3M,aAAa,CAACI,YAAY;MACrC,KAAK,QAAQ;QACX,OAAO4P,MAAM,KAAK,QAAQ;MAC5B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,UAAU;MAC9B;QACE,OAAO,IAAI;MAAE;IACjB;EACF;EAEA;EACQC,cAAcA,CAACtD,KAAqB;IAC1C,IAAI,CAACA,KAAK,CAACtK,QAAQ,EAAE;MACnB,OAAO,UAAU;IACnB;IAEA;IACA,IAAI,CAACsK,KAAK,CAACrL,gBAAgB,IAAI,CAACqL,KAAK,CAACpL,cAAc,IAClDoL,KAAK,CAACrL,gBAAgB,KAAK,EAAE,IAAIqL,KAAK,CAACpL,cAAc,KAAK,EAAE,EAAE;MAC9D,OAAO,SAAS;IAClB;IAEA,IAAI;MACF;MACA,MAAM2O,GAAG,GAAG,IAAIvF,IAAI,EAAE;MACtB,MAAMwF,KAAK,GAAG,IAAIxF,IAAI,CAACuF,GAAG,CAACE,WAAW,EAAE,EAAEF,GAAG,CAACG,QAAQ,EAAE,EAAEH,GAAG,CAACI,OAAO,EAAE,CAAC;MAExE;MACA,IAAIrJ,SAAe;MACnB,IAAI0F,KAAK,CAACrL,gBAAgB,CAACkO,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxCvI,SAAS,GAAG,IAAI0D,IAAI,CAACgC,KAAK,CAACrL,gBAAgB,CAAC;MAC9C,CAAC,MAAM;QACL2F,SAAS,GAAG,IAAI0D,IAAI,CAACgC,KAAK,CAACrL,gBAAgB,GAAG,WAAW,CAAC;MAC5D;MAEA;MACA,IAAI6F,OAAa;MACjB,IAAIwF,KAAK,CAACpL,cAAc,CAACiO,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtCrI,OAAO,GAAG,IAAIwD,IAAI,CAACgC,KAAK,CAACpL,cAAc,CAAC;MAC1C,CAAC,MAAM;QACL4F,OAAO,GAAG,IAAIwD,IAAI,CAACgC,KAAK,CAACpL,cAAc,GAAG,WAAW,CAAC;MACxD;MAEA;MACA,IAAIgP,KAAK,CAACtJ,SAAS,CAACuJ,OAAO,EAAE,CAAC,IAAID,KAAK,CAACpJ,OAAO,CAACqJ,OAAO,EAAE,CAAC,EAAE;QAC1DC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAE;UACnCC,KAAK,EAAEhE,KAAK,CAACrL,gBAAgB;UAC7BsP,GAAG,EAAEjE,KAAK,CAACpL,cAAc;UACzBsP,OAAO,EAAElE,KAAK,CAACtK;SAChB,CAAC;QACF,OAAO,SAAS;MAClB;MAEA;MACA,MAAMyO,aAAa,GAAG,IAAInG,IAAI,CAAC1D,SAAS,CAACmJ,WAAW,EAAE,EAAEnJ,SAAS,CAACoJ,QAAQ,EAAE,EAAEpJ,SAAS,CAACqJ,OAAO,EAAE,CAAC;MAClG,MAAMS,WAAW,GAAG,IAAIpG,IAAI,CAACxD,OAAO,CAACiJ,WAAW,EAAE,EAAEjJ,OAAO,CAACkJ,QAAQ,EAAE,EAAElJ,OAAO,CAACmJ,OAAO,EAAE,CAAC;MAE1F;MACA,IAAIH,KAAK,GAAGW,aAAa,EAAE;QACzB,OAAO,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIX,KAAK,IAAIW,aAAa,IAAIX,KAAK,IAAIY,WAAW,EAAE;QACzD,OAAO,QAAQ,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,OAAO,SAAS,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,EAAErE,KAAK,CAAC;MACnD,OAAO,SAAS;IAClB;EACF;EAEAlF,OAAOA,CAACgD,GAAQ;IACdA,GAAG,CAACgB,KAAK,EAAE;EACb;EAEAT,UAAUA,CAAA;IACR,IAAI,CAAClC,KAAK,CAACmI,KAAK,EAAE;IAClB,IAAI,CAACnI,KAAK,CAACoI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACjJ,uBAAuB,CAAC3G,gBAAgB,CAAC;IAC9E,IAAI,CAACwH,KAAK,CAACoI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACjJ,uBAAuB,CAAC1G,cAAc,CAAC;IAC5E,IAAI,CAACuH,KAAK,CAACqI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAClJ,uBAAuB,CAAC3G,gBAAgB,EAAE,IAAI,CAAC2G,uBAAuB,CAAC1G,cAAc,CAAC;EACtI;EAEA6P,gBAAgBA,CAAA;IACd,IAAI,CAACnI,MAAM,CAACoI,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAAChH,WAAW,EAAEC,kBAAkB,EAAE0B,GAAG,EAAE,CAAC,CAAC;EACnG;EAEA;EACAlI,gBAAgBA,CAACkL,QAAwB;IACvC,IAAI,CAAC7J,wBAAwB,GAAG6J,QAAQ,IAAI,IAAI;IAEhD;IACA,MAAMsC,iBAAiB,GAAG,IAAI,CAACpQ,cAAc,CAACC,MAAM,GAAG,CAAC;IAExD,IAAI,CAACsF,aAAa,GAAG;MACnBQ,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbT,UAAU,EAAE,CAAC4K,iBAAiB,IAAI,CAACtC,QAAQ;MAC3CrF,iBAAiB,EAAEqF,QAAQ,GAAG,CAACA,QAAQ,CAAC5J,IAAI,CAAC,GAAG,EAAE;MAClDwE,cAAc,EAAE,EAAE;MAClB1I,cAAc,EAAE;KACjB;IAED;IACA,IAAI8N,QAAQ,EAAE;MACZA,QAAQ,CAAC1I,MAAM,CAACkG,OAAO,CAACI,KAAK,IAAG;QAC9BA,KAAK,CAACjL,QAAQ,GAAG,KAAK;QACtBiL,KAAK,CAAC7G,MAAM,CAACyG,OAAO,CAACG,KAAK,IAAIA,KAAK,CAAChL,QAAQ,GAAG,KAAK,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAACiH,aAAa,CAACgC,IAAI,CAAC,IAAI,CAAC2G,kBAAkB,CAAC;EAClD;EAEA;EACArL,sBAAsBA,CAAC0G,KAAiB;IACtC,IAAIA,KAAK,CAACjL,QAAQ,EAAE;MAClBiL,KAAK,CAAC7G,MAAM,CAACyG,OAAO,CAACG,KAAK,IAAG;QAC3B,IAAIA,KAAK,CAACtK,QAAQ,EAAE;UAClBsK,KAAK,CAAChL,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLiL,KAAK,CAAC7G,MAAM,CAACyG,OAAO,CAACG,KAAK,IAAIA,KAAK,CAAChL,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA;EACAgG,aAAaA,CAAC8C,GAAQ;IACpB;IACA,IAAI,CAAC3B,KAAK,CAACmI,KAAK,EAAE;IAClB,IAAI,CAACnI,KAAK,CAACoI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzK,aAAa,CAACQ,SAAS,CAAC;IAC3D,IAAI,CAAC6B,KAAK,CAACoI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzK,aAAa,CAACU,OAAO,CAAC;IACzD,IAAI,CAAC2B,KAAK,CAACqI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC1K,aAAa,CAACQ,SAAS,EAAE,IAAI,CAACR,aAAa,CAACU,OAAO,CAAC;IAElG,IAAI,IAAI,CAAC2B,KAAK,CAACmC,aAAa,CAAC9J,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC0H,OAAO,CAACqC,aAAa,CAAC,IAAI,CAACpC,KAAK,CAACmC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMuG,cAAc,GAAU,EAAE;IAEhC,IAAI,IAAI,CAAC/K,aAAa,CAACC,UAAU,EAAE;MACjC;MACA,IAAI,CAACE,eAAe,CAAC4F,OAAO,CAACG,KAAK,IAAG;QACnC,IAAIA,KAAK,CAACtK,QAAQ,EAAE;UAClBmP,cAAc,CAAC3E,IAAI,CAAC;YAClBxK,QAAQ,EAAEsK,KAAK,CAACtK,QAAQ;YACxBf,gBAAgB,EAAE,IAAI,CAACuJ,UAAU,CAAC,IAAI,CAACpE,aAAa,CAACQ,SAAS,CAAC;YAC/D1F,cAAc,EAAE,IAAI,CAACsJ,UAAU,CAAC,IAAI,CAACpE,aAAa,CAACU,OAAO;WAC3D,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACjG,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACD,cAAc,CAACsL,OAAO,CAACG,KAAK,IAAG;UAClC,IAAIA,KAAK,CAACtK,QAAQ,EAAE;YAClBmP,cAAc,CAAC3E,IAAI,CAAC;cAClBxK,QAAQ,EAAEsK,KAAK,CAACtK,QAAQ;cACxBf,gBAAgB,EAAE,IAAI,CAACuJ,UAAU,CAAC,IAAI,CAACpE,aAAa,CAACQ,SAAS,CAAC;cAC/D1F,cAAc,EAAE,IAAI,CAACsJ,UAAU,CAAC,IAAI,CAACpE,aAAa,CAACU,OAAO;aAC3D,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAAChC,wBAAwB,EAAE;QACxC;QACA,IAAI,CAACA,wBAAwB,CAACmB,MAAM,CAACkG,OAAO,CAACI,KAAK,IAAG;UACnDA,KAAK,CAAC7G,MAAM,CAACyG,OAAO,CAACG,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAAChL,QAAQ,IAAIgL,KAAK,CAACtK,QAAQ,EAAE;cACpCmP,cAAc,CAAC3E,IAAI,CAAC;gBAClBxK,QAAQ,EAAEsK,KAAK,CAACtK,QAAQ;gBACxBf,gBAAgB,EAAE,IAAI,CAACuJ,UAAU,CAAC,IAAI,CAACpE,aAAa,CAACQ,SAAS,CAAC;gBAC/D1F,cAAc,EAAE,IAAI,CAACsJ,UAAU,CAAC,IAAI,CAACpE,aAAa,CAACU,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIqK,cAAc,CAACrQ,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC0H,OAAO,CAACqC,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAACnC,aAAa,CAACqC,oCAAoC,CAAC;MACtDC,IAAI,EAAEmG;KACP,CAAC,CAACrH,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACzC,OAAO,CAAC0C,aAAa,CAAC,QAAQiG,cAAc,CAACrQ,MAAM,WAAW,CAAC;QACpE;QACA,IAAI,CAACD,cAAc,CAACsL,OAAO,CAACG,KAAK,IAAIA,KAAK,CAAChL,QAAQ,GAAG,KAAK,CAAC;QAC5D,IAAI,CAACT,cAAc,GAAG,EAAE;QACxB,IAAI,CAACyC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC6H,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA;EACA/I,cAAcA,CAACiK,KAAqB;IAClC,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;IACzC,OAAO,UAAUqD,MAAM,EAAE;EAC3B;EAEA;EACAyB,eAAeA,CAAC9E,KAAqB;IACnC,IAAIA,KAAK,CAACtK,QAAQ,EAAE;MAClB;MACA,IAAI,CAACD,SAAS,CAAC,IAAI,CAACsP,MAAM,EAAE/E,KAAK,CAAC;IACpC;EACF;EAEA;EACAwB,oBAAoBA,CAACC,IAA6B;IAChD,IAAI,CAACxH,eAAe,GAAG,EAAE;IAEzBwH,IAAI,CAAC5B,OAAO,CAACC,SAAS,IAAG;MACvB,MAAM8B,SAAS,GAAG9B,SAAS,CAACnK,UAAU,IAAI,EAAE;MAE5CmK,SAAS,CAACC,OAAO,EAAEF,OAAO,CAACG,KAAK,IAAG;QACjC,IAAI,CAAC/F,eAAe,CAACiG,IAAI,CAAC;UACxBvK,UAAU,EAAEiM,SAAS;UACrBhM,aAAa,EAAEoK,KAAK,CAACpK,aAAa,IAAI,KAAK;UAC3CF,QAAQ,EAAEsK,KAAK,CAACtK,QAAQ,IAAI,CAAC;UAC7BG,MAAM,EAAEmK,KAAK,CAACnK,MAAM,IAAI,CAAC;UACzBlB,gBAAgB,EAAEqL,KAAK,CAACrL,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEoL,KAAK,CAACpL,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACzB,QAAQ,EAAE;IAEf;IACA,IAAI,CAACyR,iBAAiB,EAAE;EAC1B;EAEA;EACQA,iBAAiBA,CAAA;IACvB,MAAMC,YAAY,GAAG;MACnBC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACV,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAACpL,eAAe,CAAC4F,OAAO,CAACG,KAAK,IAAG;MACnC,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;MACzC,IAAIiF,YAAY,CAACK,cAAc,CAACjC,MAAM,CAAC,EAAE;QACvC4B,YAAY,CAAC5B,MAAmC,CAAC,EAAE;MACrD;IACF,CAAC,CAAC;IAEFS,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAEN,YAAY,CAAC;IAClCnB,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAE,IAAIvH,IAAI,EAAE,CAACwH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;EAIA;EACAlS,QAAQA,CAAA;IACN;IACA,MAAMmS,qBAAqB,GAAG,IAAI,CAACnR,cAAc,CAAC4K,GAAG,CAACa,KAAK,IAAIA,KAAK,CAACtK,QAAQ,CAAC;IAE9E,IAAI,CAACsC,cAAc,GAAG,IAAI,CAACiC,eAAe,CAACuI,MAAM,CAACxC,KAAK,IAAG;MACxD;MACA,IAAI,IAAI,CAAC3M,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMmP,OAAO,GAAG,IAAI,CAACpP,aAAa,CAACC,aAAa,CAACoP,WAAW,EAAE;QAC9D,IAAI,CAAC1C,KAAK,CAACrK,UAAU,CAAC+M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACzC,KAAK,CAACpK,aAAa,CAAC8M,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;UACtD,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC3F,gBAAgB,IAAIkD,KAAK,CAACpK,aAAa,KAAK,IAAI,CAACkH,gBAAgB,EAAE;QAC1E,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAACzJ,aAAa,CAACI,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAACsP,mBAAmB,CAAC/C,KAAK,CAAC,EAAE;UACpC,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC3M,aAAa,CAACO,WAAW,EAAE;QAClC,MAAM6F,WAAW,GAAGuJ,QAAQ,CAAC,IAAI,CAAC3P,aAAa,CAACO,WAAW,CAAC;QAC5D,IAAIoM,KAAK,CAACnK,MAAM,KAAK4D,WAAW,EAAE;UAChC,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF;IACA,IAAI,CAAClF,cAAc,GAAG,IAAI,CAACyD,cAAc,CAACwK,MAAM,CAACxC,KAAK,IACpD0F,qBAAqB,CAAC7C,QAAQ,CAAC7C,KAAK,CAACtK,QAAQ,CAAC,CAC/C;IAED;IACA,IAAI,CAACuE,eAAe,CAAC4F,OAAO,CAACG,KAAK,IAAG;MACnCA,KAAK,CAAChL,QAAQ,GAAG,IAAI,CAACT,cAAc,CAACqO,IAAI,CAAC5N,QAAQ,IAAIA,QAAQ,CAACU,QAAQ,KAAKsK,KAAK,CAACtK,QAAQ,CAAC;IAC7F,CAAC,CAAC;IAEF;IACA,IAAI,CAACiQ,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACtP,WAAW,GAAG,CAAC;IACpB,IAAI,CAACuP,gBAAgB,EAAE;EACzB;EAEA;EACQD,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACrN,eAAe,CAAC9D,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACwC,SAAS,GAAG,KAAK;IACxB,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI,CAACsB,eAAe,CAACuN,KAAK,CAAC7F,KAAK,IAC/C,CAACA,KAAK,CAACtK,QAAQ,IAAIsK,KAAK,CAAChL,QAAQ,CAClC;IACH;EACF;EAEA;EACA4Q,gBAAgBA,CAAA;IACd,IAAI,CAAChP,UAAU,GAAGsB,IAAI,CAAC4N,IAAI,CAAC,IAAI,CAAC9N,cAAc,CAACxD,MAAM,GAAG,IAAI,CAACR,QAAQ,CAAC;IACvE,MAAM+R,UAAU,GAAG,CAAC,IAAI,CAAC1P,WAAW,GAAG,CAAC,IAAI,IAAI,CAACrC,QAAQ;IACzD,MAAMgS,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC/R,QAAQ;IAC3C,IAAI,CAACsE,eAAe,GAAG,IAAI,CAACN,cAAc,CAACa,KAAK,CAACkN,UAAU,EAAEC,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACL,oBAAoB,EAAE;EAC7B;EAEA;EACAzR,gBAAgBA,CAAA;IACd,IAAI,CAACmC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACuP,gBAAgB,EAAE;EACzB;EAEA;EACAxP,QAAQA,CAAC6P,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACrP,UAAU,EAAE;MACxC,IAAI,CAACP,WAAW,GAAG4P,IAAI;MACvB,IAAI,CAACL,gBAAgB,EAAE;IACzB;EACF;EAEA;EACA/O,eAAeA,CAAA;IACb,MAAMqP,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAInC,KAAK,GAAG9L,IAAI,CAACkO,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC/P,WAAW,GAAG6B,IAAI,CAAC+H,KAAK,CAACkG,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAIlC,GAAG,GAAG/L,IAAI,CAACC,GAAG,CAAC,IAAI,CAACvB,UAAU,EAAEoN,KAAK,GAAGmC,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAIlC,GAAG,GAAGD,KAAK,GAAG,CAAC,GAAGmC,UAAU,EAAE;MAChCnC,KAAK,GAAG9L,IAAI,CAACkO,GAAG,CAAC,CAAC,EAAEnC,GAAG,GAAGkC,UAAU,GAAG,CAAC,CAAC;IAC3C;IAEA,KAAK,IAAIE,CAAC,GAAGrC,KAAK,EAAEqC,CAAC,IAAIpC,GAAG,EAAEoC,CAAC,EAAE,EAAE;MACjCH,KAAK,CAAChG,IAAI,CAACmG,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACAjP,iBAAiBA,CAAA;IACf,IAAI,CAACqB,eAAe,CAACuH,OAAO,CAACG,KAAK,IAAG;MACnC,IAAIA,KAAK,CAACtK,QAAQ,EAAE;QAClBsK,KAAK,CAAChL,QAAQ,GAAG,IAAI,CAACgC,SAAS;MACjC;IACF,CAAC,CAAC;IACF,IAAI,CAACsP,oBAAoB,EAAE;EAC7B;EAEA;EACArR,sBAAsBA,CAAA;IACpB,IAAI,CAACqR,oBAAoB,EAAE;IAC3B,IAAI,CAACtP,SAAS,GAAG,IAAI,CAACsB,eAAe,CAACuN,KAAK,CAAC7F,KAAK,IAC/C,CAACA,KAAK,CAACtK,QAAQ,IAAIsK,KAAK,CAAChL,QAAQ,CAClC;EACH;EAEA;EACAsR,oBAAoBA,CAAA;IAClB,IAAI,CAAC/R,cAAc,GAAG,IAAI,CAAC0F,eAAe,CAACuI,MAAM,CAACxC,KAAK,IAAIA,KAAK,CAAChL,QAAQ,CAAC;EAC5E;EAEA;EACAyC,IAAIA,CAAC8O,KAAa;IAChB,IAAI,IAAI,CAACnO,SAAS,KAAKmO,KAAK,EAAE;MAC5B,IAAI,CAAClO,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IACpE,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAGmO,KAAK;MACtB,IAAI,CAAClO,aAAa,GAAG,KAAK;IAC5B;IAEA,IAAI,CAACL,cAAc,CAACP,IAAI,CAAC,CAAC0I,CAAC,EAAEC,CAAC,KAAI;MAChC,IAAIoG,MAAM,GAAIrG,CAAS,CAACoG,KAAK,CAAC;MAC9B,IAAIE,MAAM,GAAIrG,CAAS,CAACmG,KAAK,CAAC;MAE9B;MACA,IAAIA,KAAK,CAAC1D,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC1B2D,MAAM,GAAGA,MAAM,GAAG,IAAIxI,IAAI,CAACwI,MAAM,CAAC,CAAC3C,OAAO,EAAE,GAAG,CAAC;QAChD4C,MAAM,GAAGA,MAAM,GAAG,IAAIzI,IAAI,CAACyI,MAAM,CAAC,CAAC5C,OAAO,EAAE,GAAG,CAAC;MAClD;MAEA;MACA,IAAI0C,KAAK,KAAK,QAAQ,EAAE;QACtBC,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC,IAAI,CAAC;QAC5BC,MAAM,GAAGC,MAAM,CAACD,MAAM,CAAC,IAAI,CAAC;MAC9B;MAEA;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAAC9D,WAAW,EAAE;QAC7B+D,MAAM,GAAGA,MAAM,CAAC/D,WAAW,EAAE;MAC/B;MAEA,IAAI8D,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACpO,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC9C;MACA,IAAImO,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACpO,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9C;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAACuN,gBAAgB,EAAE;EACzB;EAEA;EACArN,cAAcA,CAACoO,MAAc,EAAE3G,KAAqB;IAClD,OAAOA,KAAK,CAACtK,QAAQ;EACvB;EAEA;EACAM,aAAaA,CAACgK,KAAqB;IACjC,MAAMqD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACtD,KAAK,CAAC;IAEzC,QAAQqD,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,UAAU;QACb,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACA/L,UAAUA,CAAA;IACR;IACA,MAAMsP,UAAU,GAAG,IAAI,CAACC,WAAW,EAAE;IACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACrCG,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,UAAU,IAAIvJ,IAAI,EAAE,CAACwH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACrFwB,IAAI,CAACO,KAAK,CAACC,UAAU,GAAG,QAAQ;IAChCP,QAAQ,CAACxI,IAAI,CAACgJ,WAAW,CAACT,IAAI,CAAC;IAC/BA,IAAI,CAACU,KAAK,EAAE;IACZT,QAAQ,CAACxI,IAAI,CAACkJ,WAAW,CAACX,IAAI,CAAC;EACjC;EAEA;EACQJ,WAAWA,CAAA;IACjB,MAAMgB,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;IACxD,MAAMC,IAAI,GAAG,IAAI,CAAC9P,cAAc,CAACmH,GAAG,CAACa,KAAK,IAAI,CAC5CA,KAAK,CAACrK,UAAU,EAChBqK,KAAK,CAACpK,aAAa,EACnB,GAAGoK,KAAK,CAACnK,MAAM,GAAG,EAClBmK,KAAK,CAACrL,gBAAgB,IAAI,KAAK,EAC/BqL,KAAK,CAACpL,cAAc,IAAI,KAAK,EAC7B,IAAI,CAACoB,aAAa,CAACgK,KAAK,CAAC,CAC1B,CAAC;IAEF,MAAM4G,UAAU,GAAG,CAACiB,OAAO,EAAE,GAAGC,IAAI,CAAC,CAClC3I,GAAG,CAAC4I,GAAG,IAAIA,GAAG,CAAC5I,GAAG,CAAC6I,IAAI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAClDA,IAAI,CAAC,IAAI,CAAC;IAEb,OAAO,QAAQ,GAAGrB,UAAU,CAAC,CAAC;EAChC;;;uCA5gCW9K,0BAA0B,EAAA5K,EAAA,CAAAgX,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlX,EAAA,CAAAgX,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAApX,EAAA,CAAAgX,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAtX,EAAA,CAAAgX,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAxX,EAAA,CAAAgX,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA1X,EAAA,CAAAgX,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAA3X,EAAA,CAAAgX,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAA7X,EAAA,CAAAgX,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA1BnN,0BAA0B;MAAAoN,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;uCAR1B,EAAE,GAAAnY,EAAA,CAAAqY,0BAAA,EAAArY,EAAA,CAAAsY,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCpEbnY,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoB,SAAA,qBAAiC;UACnCpB,EAAA,CAAAG,YAAA,EAAiB;UAQPH,EAPV,CAAAC,cAAA,mBAAc,cAEuB,cAED,cACiC,cACpC,aACK;UAC1BD,EAAA,CAAAoB,SAAA,YAAiC;UAAApB,EAAA,CAAAE,MAAA,yDACnC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,MAAA,4FAAc;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACvD;UAEJH,EADF,CAAAC,cAAA,eAA0B,kBAEQ;UAC9BD,EAAA,CAAAoB,SAAA,aAAsC;UAI9CpB,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAMAH,EAHN,CAAAC,cAAA,eAA6B,eACU,eACJ,iBACa;UAAAD,EAAA,CAAAE,MAAA,qBAAG;UAAAF,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;UACvFH,EAAA,CAAAC,cAAA,qBACsD;UADvBD,EAAA,CAAA8B,gBAAA,2BAAA8W,wEAAA5W,MAAA;YAAAhC,EAAA,CAAAe,aAAA,CAAA8X,GAAA;YAAA7Y,EAAA,CAAAkC,kBAAA,CAAAkW,GAAA,CAAA5L,WAAA,CAAAC,kBAAA,EAAAzK,MAAA,MAAAoW,GAAA,CAAA5L,WAAA,CAAAC,kBAAA,GAAAzK,MAAA;YAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;UAAA,EAA4C;UACzEhC,EAAA,CAAAa,UAAA,4BAAAiY,yEAAA;YAAA9Y,EAAA,CAAAe,aAAA,CAAA8X,GAAA;YAAA,OAAA7Y,EAAA,CAAAkB,WAAA,CAAkBkX,GAAA,CAAArI,iBAAA,EAAmB;UAAA,EAAC;UACtC/P,EAAA,CAAAqB,UAAA,KAAA0X,gDAAA,wBAAoE;UAIxE/Y,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAA+B,iBACa;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBACe;UADeD,EAAA,CAAA8B,gBAAA,2BAAAkX,wEAAAhX,MAAA;YAAAhC,EAAA,CAAAe,aAAA,CAAA8X,GAAA;YAAA7Y,EAAA,CAAAkC,kBAAA,CAAAkW,GAAA,CAAAxM,gBAAA,EAAA5J,MAAA,MAAAoW,GAAA,CAAAxM,gBAAA,GAAA5J,MAAA;YAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;UAAA,EAA8B;UAAChC,EAAA,CAAAa,UAAA,4BAAAoY,yEAAA;YAAAjZ,EAAA,CAAAe,aAAA,CAAA8X,GAAA;YAAA,OAAA7Y,EAAA,CAAAkB,WAAA,CAAkBkX,GAAA,CAAAhH,gBAAA,EAAkB;UAAA,EAAC;UAEhGpR,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAqB,UAAA,KAAA6X,gDAAA,wBAAuE;UAI3ElZ,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAA+B,iBACa;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEtDH,EADF,CAAAC,cAAA,eAA8B,yBACA;UAC1BD,EAAA,CAAAoB,SAAA,mBAAoD;UACpDpB,EAAA,CAAAC,cAAA,iBAC6C;UAA3CD,EAAA,CAAA8B,gBAAA,2BAAAqX,oEAAAnX,MAAA;YAAAhC,EAAA,CAAAe,aAAA,CAAA8X,GAAA;YAAA7Y,EAAA,CAAAkC,kBAAA,CAAAkW,GAAA,CAAA5L,WAAA,CAAA/I,gBAAA,EAAAzB,MAAA,MAAAoW,GAAA,CAAA5L,WAAA,CAAA/I,gBAAA,GAAAzB,MAAA;YAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;UAAA,EAA0C;UAD5ChC,EAAA,CAAAG,YAAA,EAC6C;UAC7CH,EAAA,CAAAoB,SAAA,4BAA8D;UAChEpB,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,yBAA4B;UAC1BD,EAAA,CAAAoB,SAAA,mBAAoD;UACpDpB,EAAA,CAAAC,cAAA,iBAC2C;UAAzCD,EAAA,CAAA8B,gBAAA,2BAAAsX,oEAAApX,MAAA;YAAAhC,EAAA,CAAAe,aAAA,CAAA8X,GAAA;YAAA7Y,EAAA,CAAAkC,kBAAA,CAAAkW,GAAA,CAAA5L,WAAA,CAAA9I,cAAA,EAAA1B,MAAA,MAAAoW,GAAA,CAAA5L,WAAA,CAAA9I,cAAA,GAAA1B,MAAA;YAAA,OAAAhC,EAAA,CAAAkB,WAAA,CAAAc,MAAA;UAAA,EAAwC;UAD1ChC,EAAA,CAAAG,YAAA,EAC2C;UAC3CH,EAAA,CAAAoB,SAAA,4BAA4D;UAGlEpB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAgC,eACF,kBAC0D;UAApDD,EAAA,CAAAa,UAAA,mBAAAwY,6DAAA;YAAArZ,EAAA,CAAAe,aAAA,CAAA8X,GAAA;YAAA,OAAA7Y,EAAA,CAAAkB,WAAA,CAASkX,GAAA,CAAAzK,kBAAA,EAAoB;UAAA,EAAC;UAC5D3N,EAAA,CAAAoB,SAAA,aAAkC;UAAApB,EAAA,CAAAE,MAAA,qBACpC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBACiB;UAD8BD,EAAA,CAAAa,UAAA,mBAAAyY,6DAAA;YAAAtZ,EAAA,CAAAe,aAAA,CAAA8X,GAAA;YAAA,OAAA7Y,EAAA,CAAAkB,WAAA,CAASkX,GAAA,CAAAmB,YAAA,EAAc;UAAA,EAAC;UAErEvZ,EAAA,CAAAoB,SAAA,aAA2B;UAKrCpB,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;UAWNH,EARA,CAAAqB,UAAA,KAAAmY,0CAAA,kBAAwE,KAAAC,0CAAA,oBAQoC;UAkD9GzZ,EAAA,CAAAG,YAAA,EAAM;UAiJNH,EA9IA,CAAAqB,UAAA,KAAAqY,0CAAA,oBAAgE,KAAAC,0CAAA,kBAkIoC,KAAAC,0CAAA,kBAYtD;UAalD5Z,EADE,CAAAG,YAAA,EAAe,EACP;UAkGVH,EA/FA,CAAAqB,UAAA,KAAAwY,kDAAA,iCAAA7Z,EAAA,CAAA8Z,sBAAA,CAAgE,KAAAC,kDAAA,gCAAA/Z,EAAA,CAAA8Z,sBAAA,CAkFG,KAAAE,kDAAA,iCAAAha,EAAA,CAAA8Z,sBAAA,CAaf;;;;;UA/WT9Z,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAmD,gBAAA,YAAAiV,GAAA,CAAA5L,WAAA,CAAAC,kBAAA,CAA4C;UAE7CzM,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAgY,GAAA,CAAApK,oBAAA,CAAuB;UAQvBhO,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAmD,gBAAA,YAAAiV,GAAA,CAAAxM,gBAAA,CAA8B;UAG1B5L,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAAgY,GAAA,CAAAzM,eAAA,CAAkB;UAWF3L,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,iBAAA6Z,aAAA,CAA0B;UACtEja,EAAA,CAAAmD,gBAAA,YAAAiV,GAAA,CAAA5L,WAAA,CAAA/I,gBAAA,CAA0C;UAMEzD,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,iBAAA8Z,WAAA,CAAwB;UACpEla,EAAA,CAAAmD,gBAAA,YAAAiV,GAAA,CAAA5L,WAAA,CAAA9I,cAAA,CAAwC;UAQmB1D,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,aAAAgY,GAAA,CAAApM,OAAA,CAAoB;UAGXhM,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,aAAAgY,GAAA,CAAApM,OAAA,CAAoB;UAU9DhM,EAAA,CAAAM,SAAA,GAAgC;UAAhCN,EAAA,CAAAI,UAAA,SAAAgY,GAAA,CAAArP,eAAA,CAAAzF,MAAA,KAAgC;UAQjCtD,EAAA,CAAAM,SAAA,EAAuD;UAAvDN,EAAA,CAAAI,UAAA,SAAAgY,GAAA,CAAA5W,mBAAA,IAAA4W,GAAA,CAAArP,eAAA,CAAAzF,MAAA,KAAuD;UAqDhEtD,EAAA,CAAAM,SAAA,EAAgC;UAAhCN,EAAA,CAAAI,UAAA,SAAAgY,GAAA,CAAArP,eAAA,CAAAzF,MAAA,KAAgC;UAkI/BtD,EAAA,CAAAM,SAAA,EAAmE;UAAnEN,EAAA,CAAAI,UAAA,SAAAgY,GAAA,CAAArP,eAAA,CAAAzF,MAAA,UAAA8U,GAAA,CAAAnI,gBAAA,CAAA3M,MAAA,OAAmE;UAYnEtD,EAAA,CAAAM,SAAA,EAAa;UAAbN,EAAA,CAAAI,UAAA,SAAAgY,GAAA,CAAApM,OAAA,CAAa;;;qBDvN5CxM,YAAY,EAAA2a,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAEza,YAAY,EAAA0a,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAvD,EAAA,CAAAwD,eAAA,EAAAxD,EAAA,CAAAyD,mBAAA,EAAAzD,EAAA,CAAA0D,qBAAA,EAAA1D,EAAA,CAAA2D,qBAAA,EAAA3D,EAAA,CAAA4D,mBAAA,EAAA5D,EAAA,CAAA6D,gBAAA,EAAA7D,EAAA,CAAA8D,iBAAA,EAAA9D,EAAA,CAAA+D,iBAAA,EAAA/D,EAAA,CAAAgE,oBAAA,EAAAhE,EAAA,CAAAiE,iBAAA,EAAAjE,EAAA,CAAAkE,eAAA,EAAAlE,EAAA,CAAAmE,qBAAA,EAAAnE,EAAA,CAAAoE,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAC1B/b,kBAAkB,EAAEC,mBAAmB;MAAA+b,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}