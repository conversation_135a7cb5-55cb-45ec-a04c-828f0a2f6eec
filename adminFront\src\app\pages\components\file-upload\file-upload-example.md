# File Upload Component 編輯模式使用說明

## 功能說明

在編輯模式下，檔案上傳組件現在支援透過 `GetFile` API 取得後端檔案的 blob 資料。當使用者點選已存在的檔案時，組件會自動判斷是否需要從後端取得檔案。

## 使用方式

### 1. 在父組件中準備檔案資料

```typescript
export class YourComponent {
  // 編輯模式的檔案列表
  existingFiles = [
    {
      CFileName: 'document.pdf',
      CFile: 'base64string...', // 可選，如果有 relativePath 則會優先使用 API
      relativePath: 'uploads/documents', // 檔案在伺服器上的相對路徑
      fileName: 'document_20250618.pdf' // 檔案在伺服器上的實際檔名
    },
    {
      CFileName: 'image.jpg',
      CFile: 'base64string...',
      relativePath: 'uploads/images',
      fileName: 'photo_001.jpg'
    }
  ];
}
```

### 2. 在模板中使用

```html
<app-file-upload
  [config]="{
    multiple: true,
    showPreview: true,
    label: '檔案上傳',
    acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf']
  }"
  [existingFiles]="existingFiles">
</app-file-upload>
```

## 功能流程

1. **檔案判斷**: 當使用者點選檔案時，組件會檢查檔案物件是否包含 `relativePath` 和 `fileName` 屬性
2. **API 呼叫**: 如果包含這些屬性，會呼叫 `FileService.getFile(relativePath, fileName)` 取得檔案 blob
3. **檔案處理**: 根據檔案類型進行不同處理：
   - **圖片**: 顯示放大預覽
   - **PDF**: 在新視窗中開啟
   - **其他檔案**: 直接下載

## API 參數說明

### existingFiles 陣列物件屬性

| 屬性名稱 | 類型 | 必要 | 說明 |
|---------|------|------|------|
| CFileName | string | ✓ | 檔案顯示名稱 |
| CFile | string | | base64 檔案內容（如果有 relativePath 則為可選） |
| relativePath | string | | 檔案在伺服器上的相對路徑 |
| fileName | string | | 檔案在伺服器上的實際檔名 |

### 檔案處理邏輯

```typescript
// 如果檔案來自後端（存在 relativePath），使用 GetFile API 取得 blob
if (file.relativePath && file.fileName) {
  this.getFileFromServer(file.relativePath, file.fileName, displayName, isImage, isPdf, isCad);
  return;
}

// 否則使用原有的本地檔案或 base64 處理邏輯
```

## 錯誤處理

- 當 API 呼叫失敗時，會顯示錯誤訊息：「取得檔案失敗，請稍後再試」
- 檔案預覽失敗時，會顯示預設的檔案圖示

## 注意事項

1. 確保後端 `GetFile` API 已正確實作
2. `relativePath` 和 `fileName` 必須同時存在才會使用 API
3. Blob URL 會在使用後自動清理以避免記憶體洩漏
