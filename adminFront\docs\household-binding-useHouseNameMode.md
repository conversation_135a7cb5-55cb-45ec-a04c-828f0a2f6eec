# HouseholdBinding useHouseNameMode 使用說明

## 新功能：useHouseNameMode

當 `useHouseNameMode=true` 時，元件行為會有以下變化：

### 🎯 主要改變

1. **UI 變化**
   - ❌ 隱藏樓層篩選器
   - ❌ 隱藏樓層標籤
   - ✅ 只顯示唯一的戶別名稱按鈕

2. **數據格式**
   - 輸入：`string[]` (戶別名稱陣列)
   - 輸出：`string[]` (戶別名稱陣列)
   - 事件：`houseNameChange` 取代 `houseIdChange`

3. **計數邏輯**
   - 顯示唯一戶別名稱的數量
   - 例如：25個實際戶別，但只有5個唯一名稱，顯示「5個戶別」
   - 棟別列表中的戶數也會顯示唯一名稱數量，而非總戶別數量

### 📋 使用範例

```html
<!-- useHouseNameMode 模式 -->
<app-household-binding
  [(ngModel)]="selectedHouseNames"
  [buildingData]="buildingData"
  [useHouseNameMode]="true"
  (houseNameChange)="onHouseNameChange($event)">
</app-household-binding>
```

```typescript
export class MyComponent {
  selectedHouseNames: string[] = ['A1', 'B2']; // 戶別名稱陣列
  
  onHouseNameChange(houseNames: string[]) {
    console.log('選中的戶別名稱:', houseNames);
    // 輸出: ['A1', 'B2', 'C3'] (唯一名稱，已去重)
  }
}
```

### ⚡ 重要特性

- **去重複**: 多個同名戶別只顯示一個按鈕
- **聯動選擇**: 點擊一個戶別名稱，所有同名戶別都會被選中/取消
- **正確計數**: 顯示的數量是唯一戶別名稱的數量，不是實際戶別數量

現在您的 `useHouseNameMode` 功能已經完成並且正確運作了！
