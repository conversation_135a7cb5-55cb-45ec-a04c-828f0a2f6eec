{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { KpiCardsComponent } from './components/kpi-cards/kpi-cards.component';\nimport { ProgressChartComponent } from './components/progress-chart/progress-chart.component';\nimport { PaymentChartComponent } from './components/payment-chart/payment-chart.component';\nimport { QuotationTrendsComponent } from './components/quotation-trends/quotation-trends.component';\nimport { HeatmapChartComponent } from './components/heatmap-chart/heatmap-chart.component';\nimport { BuildCaseSelectorComponent } from './components/build-case-selector/build-case-selector.component';\nimport { NgxEchartsModule } from 'ngx-echarts';\nlet HomeComponent = class HomeComponent {\n  constructor() {\n    this.selectedBuildCase = 'all';\n  }\n  ngOnInit() {}\n  onBuildCaseChange(buildCaseId) {\n    this.selectedBuildCase = buildCaseId;\n    // 通知所有圖表組件更新資料\n    if (this.kpiCardsComponent) {\n      this.kpiCardsComponent.loadKpiData(buildCaseId);\n    }\n    if (this.progressChartComponent) {\n      this.progressChartComponent.loadProgressData(buildCaseId);\n    }\n    if (this.paymentChartComponent) {\n      this.paymentChartComponent.loadPaymentData(buildCaseId);\n    }\n    if (this.quotationTrendsComponent) {\n      this.quotationTrendsComponent.loadQuotationData(buildCaseId);\n    }\n    if (this.heatmapChartComponent) {\n      this.heatmapChartComponent.loadHeatmapData(buildCaseId);\n    }\n  }\n};\n__decorate([ViewChild(KpiCardsComponent)], HomeComponent.prototype, \"kpiCardsComponent\", void 0);\n__decorate([ViewChild(ProgressChartComponent)], HomeComponent.prototype, \"progressChartComponent\", void 0);\n__decorate([ViewChild(PaymentChartComponent)], HomeComponent.prototype, \"paymentChartComponent\", void 0);\n__decorate([ViewChild(QuotationTrendsComponent)], HomeComponent.prototype, \"quotationTrendsComponent\", void 0);\n__decorate([ViewChild(HeatmapChartComponent)], HomeComponent.prototype, \"heatmapChartComponent\", void 0);\nHomeComponent = __decorate([Component({\n  selector: 'ngx-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss'],\n  standalone: true,\n  imports: [RouterModule, CommonModule, NgxEchartsModule, KpiCardsComponent, ProgressChartComponent, PaymentChartComponent, QuotationTrendsComponent, HeatmapChartComponent, BuildCaseSelectorComponent]\n})], HomeComponent);\nexport { HomeComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "RouterModule", "CommonModule", "KpiCardsComponent", "ProgressChartComponent", "PaymentChartComponent", "QuotationTrendsComponent", "HeatmapChartComponent", "BuildCaseSelectorComponent", "NgxEchartsModule", "HomeComponent", "constructor", "selectedBuildCase", "ngOnInit", "onBuildCaseChange", "buildCaseId", "kpiCardsComponent", "loadKpiData", "progressChartComponent", "loadProgressData", "paymentChartComponent", "loadPaymentData", "quotationTrendsComponent", "loadQuotationData", "heatmapChartComponent", "loadHeatmapData", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\home.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { KpiCardsComponent } from './components/kpi-cards/kpi-cards.component';\r\nimport { ProgressChartComponent } from './components/progress-chart/progress-chart.component';\r\nimport { PaymentChartComponent } from './components/payment-chart/payment-chart.component';\r\nimport { QuotationTrendsComponent } from './components/quotation-trends/quotation-trends.component';\r\nimport { HeatmapChartComponent } from './components/heatmap-chart/heatmap-chart.component';\r\nimport { BuildCaseSelectorComponent } from './components/build-case-selector/build-case-selector.component';\r\nimport { NgxEchartsModule } from 'ngx-echarts';\r\n\r\n@Component({\r\n    selector: 'ngx-home',\r\n    templateUrl: './home.component.html',\r\n    styleUrls: ['./home.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        RouterModule,\r\n        CommonModule,\r\n        NgxEchartsModule,\r\n        KpiCardsComponent,\r\n        ProgressChartComponent,\r\n        PaymentChartComponent,\r\n        QuotationTrendsComponent,\r\n        HeatmapChartComponent,\r\n        BuildCaseSelectorComponent\r\n    ]\r\n})\r\nexport class HomeComponent implements OnInit {\r\n  @ViewChild(KpiCardsComponent) kpiCardsComponent!: KpiCardsComponent;\r\n  @ViewChild(ProgressChartComponent) progressChartComponent!: ProgressChartComponent;\r\n  @ViewChild(PaymentChartComponent) paymentChartComponent!: PaymentChartComponent;\r\n  @ViewChild(QuotationTrendsComponent) quotationTrendsComponent!: QuotationTrendsComponent;\r\n  @ViewChild(HeatmapChartComponent) heatmapChartComponent!: HeatmapChartComponent;\r\n\r\n  selectedBuildCase: string = 'all';\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  onBuildCaseChange(buildCaseId: string): void {\r\n    this.selectedBuildCase = buildCaseId;\r\n    \r\n    // 通知所有圖表組件更新資料\r\n    if (this.kpiCardsComponent) {\r\n      this.kpiCardsComponent.loadKpiData(buildCaseId);\r\n    }\r\n    if (this.progressChartComponent) {\r\n      this.progressChartComponent.loadProgressData(buildCaseId);\r\n    }\r\n    if (this.paymentChartComponent) {\r\n      this.paymentChartComponent.loadPaymentData(buildCaseId);\r\n    }\r\n    if (this.quotationTrendsComponent) {\r\n      this.quotationTrendsComponent.loadQuotationData(buildCaseId);\r\n    }\r\n    if (this.heatmapChartComponent) {\r\n      this.heatmapChartComponent.loadHeatmapData(buildCaseId);\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,SAAS,QAAQ,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,wBAAwB,QAAQ,0DAA0D;AACnG,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,0BAA0B,QAAQ,gEAAgE;AAC3G,SAASC,gBAAgB,QAAQ,aAAa;AAmBvC,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EASxBC,YAAA;IAFA,KAAAC,iBAAiB,GAAW,KAAK;EAEjB;EAEhBC,QAAQA,CAAA,GACR;EAEAC,iBAAiBA,CAACC,WAAmB;IACnC,IAAI,CAACH,iBAAiB,GAAGG,WAAW;IAEpC;IACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACC,WAAW,CAACF,WAAW,CAAC;IACjD;IACA,IAAI,IAAI,CAACG,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACC,gBAAgB,CAACJ,WAAW,CAAC;IAC3D;IACA,IAAI,IAAI,CAACK,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACC,eAAe,CAACN,WAAW,CAAC;IACzD;IACA,IAAI,IAAI,CAACO,wBAAwB,EAAE;MACjC,IAAI,CAACA,wBAAwB,CAACC,iBAAiB,CAACR,WAAW,CAAC;IAC9D;IACA,IAAI,IAAI,CAACS,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACC,eAAe,CAACV,WAAW,CAAC;IACzD;EACF;CACD;AAjC+BW,UAAA,EAA7B1B,SAAS,CAACG,iBAAiB,CAAC,C,uDAAuC;AACjCuB,UAAA,EAAlC1B,SAAS,CAACI,sBAAsB,CAAC,C,4DAAiD;AACjDsB,UAAA,EAAjC1B,SAAS,CAACK,qBAAqB,CAAC,C,2DAA+C;AAC3CqB,UAAA,EAApC1B,SAAS,CAACM,wBAAwB,CAAC,C,8DAAqD;AACvDoB,UAAA,EAAjC1B,SAAS,CAACO,qBAAqB,CAAC,C,2DAA+C;AALrEG,aAAa,GAAAgB,UAAA,EAjBzB3B,SAAS,CAAC;EACP4B,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,CAAC,uBAAuB,CAAC;EACpCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACL9B,YAAY,EACZC,YAAY,EACZO,gBAAgB,EAChBN,iBAAiB,EACjBC,sBAAsB,EACtBC,qBAAqB,EACrBC,wBAAwB,EACxBC,qBAAqB,EACrBC,0BAA0B;CAEjC,CAAC,C,EACWE,aAAa,CAkCzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}