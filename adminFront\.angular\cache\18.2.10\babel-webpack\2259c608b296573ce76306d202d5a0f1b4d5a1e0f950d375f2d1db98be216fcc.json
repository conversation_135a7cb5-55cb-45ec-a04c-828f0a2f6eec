{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Afrikaans [af]\n//! author : <PERSON> : https://github.com/wernerm\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var af = moment.defineLocale('af', {\n    months: 'Januarie_Februarie_Maart_April_Mei_Junie_<PERSON>_<PERSON>_September_Oktober_November_Desember'.split('_'),\n    monthsShort: 'Jan_Feb_Mrt_Apr_Mei_Jun_Jul_Aug_Sep_Okt_Nov_Des'.split('_'),\n    weekdays: 'Sondag_Maandag_Dinsdag_Woensdag_Donderdag_Vrydag_Saterdag'.split('_'),\n    weekdaysShort: '<PERSON>_<PERSON><PERSON>_<PERSON>_Woe_Don_Vry_Sat'.split('_'),\n    weekdaysMin: 'So_Ma_Di_Wo_Do_Vr_Sa'.split('_'),\n    meridiemParse: /vm|nm/i,\n    isPM: function (input) {\n      return /^nm$/i.test(input);\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 12) {\n        return isLower ? 'vm' : 'VM';\n      } else {\n        return isLower ? 'nm' : 'NM';\n      }\n    },\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Vandag om] LT',\n      nextDay: '[Môre om] LT',\n      nextWeek: 'dddd [om] LT',\n      lastDay: '[Gister om] LT',\n      lastWeek: '[Laas] dddd [om] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'oor %s',\n      past: '%s gelede',\n      s: \"'n paar sekondes\",\n      ss: '%d sekondes',\n      m: \"'n minuut\",\n      mm: '%d minute',\n      h: \"'n uur\",\n      hh: '%d ure',\n      d: \"'n dag\",\n      dd: '%d dae',\n      M: \"'n maand\",\n      MM: '%d maande',\n      y: \"'n jaar\",\n      yy: '%d jaar'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n    ordinal: function (number) {\n      return number + (number === 1 || number === 8 || number >= 20 ? 'ste' : 'de'); // Thanks to Joris Röling : https://github.com/jjupiter\n    },\n    week: {\n      dow: 1,\n      // Maandag is die eerste dag van die week.\n      doy: 4 // Die week wat die 4de Januarie bevat is die eerste week van die jaar.\n    }\n  });\n  return af;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}