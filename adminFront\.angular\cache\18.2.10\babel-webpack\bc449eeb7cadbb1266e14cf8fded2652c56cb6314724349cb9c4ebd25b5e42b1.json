{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\n// Backward compat for radar chart in 2\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function radarBackwardCompat(option) {\n  var polarOptArr = option.polar;\n  if (polarOptArr) {\n    if (!zrUtil.isArray(polarOptArr)) {\n      polarOptArr = [polarOptArr];\n    }\n    var polarNotRadar_1 = [];\n    zrUtil.each(polarOptArr, function (polarOpt, idx) {\n      if (polarOpt.indicator) {\n        if (polarOpt.type && !polarOpt.shape) {\n          polarOpt.shape = polarOpt.type;\n        }\n        option.radar = option.radar || [];\n        if (!zrUtil.isArray(option.radar)) {\n          option.radar = [option.radar];\n        }\n        option.radar.push(polarOpt);\n      } else {\n        polarNotRadar_1.push(polarOpt);\n      }\n    });\n    option.polar = polarNotRadar_1;\n  }\n  zrUtil.each(option.series, function (seriesOpt) {\n    if (seriesOpt && seriesOpt.type === 'radar' && seriesOpt.polarIndex) {\n      seriesOpt.radarIndex = seriesOpt.polarIndex;\n    }\n  });\n}", "map": {"version": 3, "names": ["zrUtil", "radarBackwardCompat", "option", "polarOptArr", "polar", "isArray", "polarNotRadar_1", "each", "polarOpt", "idx", "indicator", "type", "shape", "radar", "push", "series", "seriesOpt", "polarIndex", "radarIndex"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/radar/backwardCompat.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\n// Backward compat for radar chart in 2\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function radarBackwardCompat(option) {\n  var polarOptArr = option.polar;\n  if (polarOptArr) {\n    if (!zrUtil.isArray(polarOptArr)) {\n      polarOptArr = [polarOptArr];\n    }\n    var polarNotRadar_1 = [];\n    zrUtil.each(polarOptArr, function (polarOpt, idx) {\n      if (polarOpt.indicator) {\n        if (polarOpt.type && !polarOpt.shape) {\n          polarOpt.shape = polarOpt.type;\n        }\n        option.radar = option.radar || [];\n        if (!zrUtil.isArray(option.radar)) {\n          option.radar = [option.radar];\n        }\n        option.radar.push(polarOpt);\n      } else {\n        polarNotRadar_1.push(polarOpt);\n      }\n    });\n    option.polar = polarNotRadar_1;\n  }\n  zrUtil.each(option.series, function (seriesOpt) {\n    if (seriesOpt && seriesOpt.type === 'radar' && seriesOpt.polarIndex) {\n      seriesOpt.radarIndex = seriesOpt.polarIndex;\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,eAAe,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EAClD,IAAIC,WAAW,GAAGD,MAAM,CAACE,KAAK;EAC9B,IAAID,WAAW,EAAE;IACf,IAAI,CAACH,MAAM,CAACK,OAAO,CAACF,WAAW,CAAC,EAAE;MAChCA,WAAW,GAAG,CAACA,WAAW,CAAC;IAC7B;IACA,IAAIG,eAAe,GAAG,EAAE;IACxBN,MAAM,CAACO,IAAI,CAACJ,WAAW,EAAE,UAAUK,QAAQ,EAAEC,GAAG,EAAE;MAChD,IAAID,QAAQ,CAACE,SAAS,EAAE;QACtB,IAAIF,QAAQ,CAACG,IAAI,IAAI,CAACH,QAAQ,CAACI,KAAK,EAAE;UACpCJ,QAAQ,CAACI,KAAK,GAAGJ,QAAQ,CAACG,IAAI;QAChC;QACAT,MAAM,CAACW,KAAK,GAAGX,MAAM,CAACW,KAAK,IAAI,EAAE;QACjC,IAAI,CAACb,MAAM,CAACK,OAAO,CAACH,MAAM,CAACW,KAAK,CAAC,EAAE;UACjCX,MAAM,CAACW,KAAK,GAAG,CAACX,MAAM,CAACW,KAAK,CAAC;QAC/B;QACAX,MAAM,CAACW,KAAK,CAACC,IAAI,CAACN,QAAQ,CAAC;MAC7B,CAAC,MAAM;QACLF,eAAe,CAACQ,IAAI,CAACN,QAAQ,CAAC;MAChC;IACF,CAAC,CAAC;IACFN,MAAM,CAACE,KAAK,GAAGE,eAAe;EAChC;EACAN,MAAM,CAACO,IAAI,CAACL,MAAM,CAACa,MAAM,EAAE,UAAUC,SAAS,EAAE;IAC9C,IAAIA,SAAS,IAAIA,SAAS,CAACL,IAAI,KAAK,OAAO,IAAIK,SAAS,CAACC,UAAU,EAAE;MACnED,SAAS,CAACE,UAAU,GAAGF,SAAS,CAACC,UAAU;IAC7C;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}