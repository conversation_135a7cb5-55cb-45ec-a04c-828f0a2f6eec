{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/dashboard.service\";\nimport * as i2 from \"ngx-echarts\";\nexport class HeatmapChartComponent {\n  constructor(dashboardService) {\n    this.dashboardService = dashboardService;\n    this.chartOption = {};\n  }\n  ngOnInit() {\n    this.loadHeatmapData();\n  }\n  loadHeatmapData() {\n    this.dashboardService.getHeatmapData().subscribe(data => {\n      this.setupChart(data);\n    });\n  }\n  setupChart(data) {\n    const heatmapData = [];\n    let maxValue = 0;\n    for (let i = 0; i < data.yAxis.length; i++) {\n      for (let j = 0; j < data.xAxis.length; j++) {\n        const value = data.data[i][j];\n        heatmapData.push([j, i, value]);\n        if (value > maxValue) {\n          maxValue = value;\n        }\n      }\n    }\n    this.chartOption = {\n      title: {\n        text: '戶型vs進度熱力圖',\n        left: 'center',\n        textStyle: {\n          fontSize: 16,\n          fontWeight: 'bold',\n          color: '#2c3e50'\n        }\n      },\n      tooltip: {\n        position: 'top',\n        formatter: params => {\n          const x = params.data[0];\n          const y = params.data[1];\n          const value = params.data[2];\n          return `${data.xAxis[x]} - ${data.yAxis[y]}<br/>戶數: ${value}戶`;\n        }\n      },\n      grid: {\n        left: '10%',\n        right: '10%',\n        bottom: '15%',\n        top: '20%',\n        containLabel: true\n      },\n      xAxis: {\n        type: 'category',\n        data: data.xAxis,\n        splitArea: {\n          show: true\n        }\n      },\n      yAxis: {\n        type: 'category',\n        data: data.yAxis,\n        splitArea: {\n          show: true\n        }\n      },\n      visualMap: {\n        min: 0,\n        max: maxValue,\n        calculable: true,\n        orient: 'horizontal',\n        left: 'center',\n        bottom: '5%',\n        inRange: {\n          color: ['#e8f4f8', '#2980b9']\n        }\n      },\n      series: [{\n        name: '戶數',\n        type: 'heatmap',\n        data: heatmapData,\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          }\n        },\n        label: {\n          show: true,\n          color: '#fff',\n          fontWeight: 'bold'\n        }\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function HeatmapChartComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HeatmapChartComponent)(i0.ɵɵdirectiveInject(i1.DashboardService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeatmapChartComponent,\n      selectors: [[\"app-heatmap-chart\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"chart-container\", \"dashboard-card\"], [\"echarts\", \"\", 1, \"chart\", 3, \"options\"]],\n      template: function HeatmapChartComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.chartOption);\n        }\n      },\n      dependencies: [CommonModule, NgxEchartsModule, i2.NgxEchartsDirective],\n      styles: [\".chart-container[_ngcontent-%COMP%] {\\n  height: 350px;\\n  width: 100%;\\n  background: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  padding: 20px;\\n  border: 1px solid #f0f0f0;\\n}\\n\\n.chart[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImhlYXRtYXAtY2hhcnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSx5Q0FBQTtFQUNBLGFBQUE7RUFDQSx5QkFBQTtBQUNGOztBQUVBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUFDRiIsImZpbGUiOiJoZWF0bWFwLWNoYXJ0LmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmNoYXJ0LWNvbnRhaW5lciB7XG4gIGhlaWdodDogMzUwcHg7XG4gIHdpZHRoOiAxMDAlO1xuICBiYWNrZ3JvdW5kOiAjZmZmZmZmO1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgcGFkZGluZzogMjBweDtcbiAgYm9yZGVyOiAxcHggc29saWQgI2YwZjBmMDtcbn1cblxuLmNoYXJ0IHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG9tZS9jb21wb25lbnRzL2hlYXRtYXAtY2hhcnQvaGVhdG1hcC1jaGFydC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLHlDQUFBO0VBQ0EsYUFBQTtFQUNBLHlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtBQUNGO0FBQ0EsZ3RCQUFndEIiLCJzb3VyY2VzQ29udGVudCI6WyIuY2hhcnQtY29udGFpbmVyIHtcbiAgaGVpZ2h0OiAzNTBweDtcbiAgd2lkdGg6IDEwMCU7XG4gIGJhY2tncm91bmQ6ICNmZmZmZmY7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICBwYWRkaW5nOiAyMHB4O1xuICBib3JkZXI6IDFweCBzb2xpZCAjZjBmMGYwO1xufVxuXG4uY2hhcnQge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NgxEchartsModule", "HeatmapChartComponent", "constructor", "dashboardService", "chartOption", "ngOnInit", "loadHeatmapData", "getHeatmapData", "subscribe", "data", "<PERSON><PERSON><PERSON>", "heatmapData", "maxValue", "i", "yAxis", "length", "j", "xAxis", "value", "push", "title", "text", "left", "textStyle", "fontSize", "fontWeight", "color", "tooltip", "position", "formatter", "params", "x", "y", "grid", "right", "bottom", "top", "containLabel", "type", "splitArea", "show", "visualMap", "min", "max", "calculable", "orient", "inRange", "series", "name", "emphasis", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "label", "i0", "ɵɵdirectiveInject", "i1", "DashboardService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HeatmapChartComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "i2", "NgxEchartsDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\heatmap-chart\\heatmap-chart.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\heatmap-chart\\heatmap-chart.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport { EChartsOption } from 'echarts';\nimport { HeatmapData } from '../../models/dashboard.interface';\nimport { DashboardService } from '../../services/dashboard.service';\n\n@Component({\n  selector: 'app-heatmap-chart',\n  standalone: true,\n  imports: [CommonModule, NgxEchartsModule],\n  templateUrl: './heatmap-chart.component.html',\n  styleUrls: ['./heatmap-chart.component.scss']\n})\nexport class HeatmapChartComponent implements OnInit {\n  chartOption: EChartsOption = {};\n\n  constructor(private dashboardService: DashboardService) { }\n\n  ngOnInit(): void {\n    this.loadHeatmapData();\n  }\n\n  private loadHeatmapData(): void {\n    this.dashboardService.getHeatmapData().subscribe(data => {\n      this.setupChart(data);\n    });\n  }\n\n  private setupChart(data: HeatmapData): void {\n    const heatmapData: [number, number, number][] = [];\n    let maxValue = 0;\n\n    for (let i = 0; i < data.yAxis.length; i++) {\n      for (let j = 0; j < data.xAxis.length; j++) {\n        const value = data.data[i][j];\n        heatmapData.push([j, i, value]);\n        if (value > maxValue) {\n          maxValue = value;\n        }\n      }\n    }\n\n    this.chartOption = {\n      title: {\n        text: '戶型vs進度熱力圖',\n        left: 'center',\n        textStyle: {\n          fontSize: 16,\n          fontWeight: 'bold',\n          color: '#2c3e50'\n        }\n      },\n      tooltip: {\n        position: 'top',\n        formatter: (params: any) => {\n          const x = params.data[0];\n          const y = params.data[1];\n          const value = params.data[2];\n          return `${data.xAxis[x]} - ${data.yAxis[y]}<br/>戶數: ${value}戶`;\n        }\n      },\n      grid: {\n        left: '10%',\n        right: '10%',\n        bottom: '15%',\n        top: '20%',\n        containLabel: true\n      },\n      xAxis: {\n        type: 'category',\n        data: data.xAxis,\n        splitArea: {\n          show: true\n        }\n      },\n      yAxis: {\n        type: 'category',\n        data: data.yAxis,\n        splitArea: {\n          show: true\n        }\n      },\n      visualMap: {\n        min: 0,\n        max: maxValue,\n        calculable: true,\n        orient: 'horizontal',\n        left: 'center',\n        bottom: '5%',\n        inRange: {\n          color: ['#e8f4f8', '#2980b9']\n        }\n      },\n      series: [{\n        name: '戶數',\n        type: 'heatmap',\n        data: heatmapData,\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          }\n        },\n        label: {\n          show: true,\n          color: '#fff',\n          fontWeight: 'bold'\n        }\n      }]\n    };\n  }\n}", "<div class=\"chart-container dashboard-card\">\n  <div echarts [options]=\"chartOption\" class=\"chart\"></div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,aAAa;;;;AAY9C,OAAM,MAAOC,qBAAqB;EAGhCC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAFpC,KAAAC,WAAW,GAAkB,EAAE;EAE2B;EAE1DC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQA,eAAeA,CAAA;IACrB,IAAI,CAACH,gBAAgB,CAACI,cAAc,EAAE,CAACC,SAAS,CAACC,IAAI,IAAG;MACtD,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQC,UAAUA,CAACD,IAAiB;IAClC,MAAME,WAAW,GAA+B,EAAE;IAClD,IAAIC,QAAQ,GAAG,CAAC;IAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACK,KAAK,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC1C,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,CAACQ,KAAK,CAACF,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC1C,MAAME,KAAK,GAAGT,IAAI,CAACA,IAAI,CAACI,CAAC,CAAC,CAACG,CAAC,CAAC;QAC7BL,WAAW,CAACQ,IAAI,CAAC,CAACH,CAAC,EAAEH,CAAC,EAAEK,KAAK,CAAC,CAAC;QAC/B,IAAIA,KAAK,GAAGN,QAAQ,EAAE;UACpBA,QAAQ,GAAGM,KAAK;QAClB;MACF;IACF;IAEA,IAAI,CAACd,WAAW,GAAG;MACjBgB,KAAK,EAAE;QACLC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,QAAQ;QACdC,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,MAAM;UAClBC,KAAK,EAAE;;OAEV;MACDC,OAAO,EAAE;QACPC,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAGC,MAAW,IAAI;UACzB,MAAMC,CAAC,GAAGD,MAAM,CAACrB,IAAI,CAAC,CAAC,CAAC;UACxB,MAAMuB,CAAC,GAAGF,MAAM,CAACrB,IAAI,CAAC,CAAC,CAAC;UACxB,MAAMS,KAAK,GAAGY,MAAM,CAACrB,IAAI,CAAC,CAAC,CAAC;UAC5B,OAAO,GAAGA,IAAI,CAACQ,KAAK,CAACc,CAAC,CAAC,MAAMtB,IAAI,CAACK,KAAK,CAACkB,CAAC,CAAC,YAAYd,KAAK,GAAG;QAChE;OACD;MACDe,IAAI,EAAE;QACJX,IAAI,EAAE,KAAK;QACXY,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE,KAAK;QACVC,YAAY,EAAE;OACf;MACDpB,KAAK,EAAE;QACLqB,IAAI,EAAE,UAAU;QAChB7B,IAAI,EAAEA,IAAI,CAACQ,KAAK;QAChBsB,SAAS,EAAE;UACTC,IAAI,EAAE;;OAET;MACD1B,KAAK,EAAE;QACLwB,IAAI,EAAE,UAAU;QAChB7B,IAAI,EAAEA,IAAI,CAACK,KAAK;QAChByB,SAAS,EAAE;UACTC,IAAI,EAAE;;OAET;MACDC,SAAS,EAAE;QACTC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE/B,QAAQ;QACbgC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,YAAY;QACpBvB,IAAI,EAAE,QAAQ;QACda,MAAM,EAAE,IAAI;QACZW,OAAO,EAAE;UACPpB,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS;;OAE/B;MACDqB,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,IAAI;QACVV,IAAI,EAAE,SAAS;QACf7B,IAAI,EAAEE,WAAW;QACjBsC,QAAQ,EAAE;UACRC,SAAS,EAAE;YACTC,UAAU,EAAE,EAAE;YACdC,WAAW,EAAE;;SAEhB;QACDC,KAAK,EAAE;UACLb,IAAI,EAAE,IAAI;UACVd,KAAK,EAAE,MAAM;UACbD,UAAU,EAAE;;OAEf;KACF;EACH;;;uCAjGWxB,qBAAqB,EAAAqD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAArBxD,qBAAqB;MAAAyD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdlCb,EAAA,CAAAe,cAAA,aAA4C;UAC1Cf,EAAA,CAAAgB,SAAA,aAAyD;UAC3DhB,EAAA,CAAAiB,YAAA,EAAM;;;UADSjB,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAAmB,UAAA,YAAAL,GAAA,CAAAhE,WAAA,CAAuB;;;qBDS1BL,YAAY,EAAEC,gBAAgB,EAAA0E,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}