{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/dashboard.service\";\nimport * as i2 from \"@angular/common\";\nfunction KpiCardsComponent_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtext(1, \"%\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction KpiCardsComponent_div_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵelement(1, \"i\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const kpi_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", kpi_r1.percentage, \"% \");\n  }\n}\nfunction KpiCardsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"h3\", 5);\n    i0.ɵɵtext(5);\n    i0.ɵɵtemplate(6, KpiCardsComponent_div_1_span_6_Template, 2, 0, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 7);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, KpiCardsComponent_div_1_span_9_Template, 3, 1, \"span\", 8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const kpi_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", kpi_r1.color + \"20\")(\"color\", kpi_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(kpi_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", kpi_r1.value, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", kpi_r1.label === \"\\u6574\\u9AD4\\u5B8C\\u6210\\u7387\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(kpi_r1.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", kpi_r1.percentage);\n  }\n}\nexport class KpiCardsComponent {\n  constructor(dashboardService) {\n    this.dashboardService = dashboardService;\n    this.kpiData = null;\n  }\n  ngOnInit() {\n    this.loadKpiData();\n  }\n  loadKpiData(buildCaseId) {\n    this.dashboardService.getDashboardKPI(buildCaseId).subscribe(data => {\n      this.kpiData = data;\n    });\n  }\n  getKpiArray() {\n    if (!this.kpiData) return [];\n    return [this.kpiData.totalHouses, this.kpiData.soldHouses, this.kpiData.customizedHouses, this.kpiData.signedHouses, this.kpiData.paidHouses, this.kpiData.completionRate];\n  }\n  static {\n    this.ɵfac = function KpiCardsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || KpiCardsComponent)(i0.ɵɵdirectiveInject(i1.DashboardService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: KpiCardsComponent,\n      selectors: [[\"app-kpi-cards\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"kpi-grid\"], [\"class\", \"kpi-card dashboard-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"kpi-card\", \"dashboard-card\"], [1, \"kpi-icon\"], [1, \"kpi-content\"], [1, \"kpi-value\"], [\"class\", \"percentage-symbol\", 4, \"ngIf\"], [1, \"kpi-label\"], [\"class\", \"kpi-trend\", 4, \"ngIf\"], [1, \"percentage-symbol\"], [1, \"kpi-trend\"], [1, \"fas\", \"fa-arrow-up\", \"trend-icon\"]],\n      template: function KpiCardsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, KpiCardsComponent_div_1_Template, 10, 10, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.getKpiArray());\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf],\n      styles: [\".kpi-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(6, 1fr);\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n@media (max-width: 1200px) {\\n  .kpi-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .kpi-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .kpi-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n.dashboard-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  padding: 24px;\\n  transition: all 0.3s ease;\\n  border: 1px solid #f0f0f0;\\n}\\n.dashboard-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n\\n.kpi-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  min-height: 120px;\\n}\\n.kpi-card[_ngcontent-%COMP%]   .kpi-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.kpi-card[_ngcontent-%COMP%]   .kpi-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.kpi-card[_ngcontent-%COMP%]   .kpi-value[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  margin: 0 0 4px 0;\\n  color: #2c3e50;\\n  line-height: 1.2;\\n}\\n.kpi-card[_ngcontent-%COMP%]   .kpi-value[_ngcontent-%COMP%]   .percentage-symbol[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: #7f8c8d;\\n}\\n.kpi-card[_ngcontent-%COMP%]   .kpi-label[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  color: #7f8c8d;\\n  margin: 0 0 8px 0;\\n  font-weight: 500;\\n}\\n.kpi-card[_ngcontent-%COMP%]   .kpi-trend[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 12px;\\n  color: #27ae60;\\n  font-weight: 600;\\n}\\n.kpi-card[_ngcontent-%COMP%]   .kpi-trend[_ngcontent-%COMP%]   .trend-icon[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n}\\n\\n@media (max-width: 768px) {\\n  .kpi-card[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 12px;\\n    min-height: 140px;\\n    padding: 20px;\\n  }\\n  .kpi-card[_ngcontent-%COMP%]   .kpi-content[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .kpi-card[_ngcontent-%COMP%]   .kpi-value[_ngcontent-%COMP%] {\\n    font-size: 22px;\\n  }\\n  .kpi-card[_ngcontent-%COMP%]   .kpi-icon[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n    font-size: 24px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "kpi_r1", "percentage", "ɵɵtemplate", "KpiCardsComponent_div_1_span_6_Template", "KpiCardsComponent_div_1_span_9_Template", "ɵɵstyleProp", "color", "ɵɵclassMap", "icon", "value", "ɵɵproperty", "label", "ɵɵtextInterpolate", "KpiCardsComponent", "constructor", "dashboardService", "kpiData", "ngOnInit", "loadKpiData", "buildCaseId", "getDashboardKPI", "subscribe", "data", "getKpiArray", "totalHouses", "soldHouses", "customizedHouses", "signedHouses", "paidHouses", "completionRate", "ɵɵdirectiveInject", "i1", "DashboardService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "KpiCardsComponent_Template", "rf", "ctx", "KpiCardsComponent_div_1_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\kpi-cards\\kpi-cards.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\kpi-cards\\kpi-cards.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DashboardKPI } from '../../models/dashboard.interface';\nimport { DashboardService } from '../../services/dashboard.service';\n\n@Component({\n  selector: 'app-kpi-cards',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './kpi-cards.component.html',\n  styleUrls: ['./kpi-cards.component.scss']\n})\nexport class KpiCardsComponent implements OnInit {\n  kpiData: DashboardKPI | null = null;\n\n  constructor(private dashboardService: DashboardService) { }\n\n  ngOnInit(): void {\n    this.loadKpiData();\n  }\n\n  loadKpiData(buildCaseId?: string): void {\n    this.dashboardService.getDashboardKPI(buildCaseId).subscribe(data => {\n      this.kpiData = data;\n    });\n  }\n\n  getKpiArray(): any[] {\n    if (!this.kpiData) return [];\n    \n    return [\n      this.kpiData.totalHouses,\n      this.kpiData.soldHouses,\n      this.kpiData.customizedHouses,\n      this.kpiData.signedHouses,\n      this.kpiData.paidHouses,\n      this.kpiData.completionRate\n    ];\n  }\n}", "<div class=\"kpi-grid\">\n  <div *ngFor=\"let kpi of getKpiArray()\" class=\"kpi-card dashboard-card\">\n    <div class=\"kpi-icon\" [style.background-color]=\"kpi.color + '20'\" [style.color]=\"kpi.color\">\n      <i [class]=\"kpi.icon\"></i>\n    </div>\n    <div class=\"kpi-content\">\n      <h3 class=\"kpi-value\">\n        {{ kpi.value }}\n        <span *ngIf=\"kpi.label === '整體完成率'\" class=\"percentage-symbol\">%</span>\n      </h3>\n      <p class=\"kpi-label\">{{ kpi.label }}</p>\n      <span *ngIf=\"kpi.percentage\" class=\"kpi-trend\">\n        <i class=\"fas fa-arrow-up trend-icon\"></i>\n        {{ kpi.percentage }}%\n      </span>\n    </div>\n  </div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;ICOtCC,EAAA,CAAAC,cAAA,cAA8D;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGxEH,EAAA,CAAAC,cAAA,eAA+C;IAC7CD,EAAA,CAAAI,SAAA,YAA0C;IAC1CJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,UAAA,OACF;;;;;IAZFR,EADF,CAAAC,cAAA,aAAuE,aACuB;IAC1FD,EAAA,CAAAI,SAAA,QAA0B;IAC5BJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,aAAyB,YACD;IACpBD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAS,UAAA,IAAAC,uCAAA,kBAA8D;IAChEV,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,WAAqB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAS,UAAA,IAAAE,uCAAA,kBAA+C;IAKnDX,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAdkBH,EAAA,CAAAK,SAAA,EAA2C;IAACL,EAA5C,CAAAY,WAAA,qBAAAL,MAAA,CAAAM,KAAA,QAA2C,UAAAN,MAAA,CAAAM,KAAA,CAA0B;IACtFb,EAAA,CAAAK,SAAA,EAAkB;IAAlBL,EAAA,CAAAc,UAAA,CAAAP,MAAA,CAAAQ,IAAA,CAAkB;IAInBf,EAAA,CAAAK,SAAA,GACA;IADAL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAS,KAAA,MACA;IAAOhB,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAiB,UAAA,SAAAV,MAAA,CAAAW,KAAA,sCAA2B;IAEflB,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAmB,iBAAA,CAAAZ,MAAA,CAAAW,KAAA,CAAe;IAC7BlB,EAAA,CAAAK,SAAA,EAAoB;IAApBL,EAAA,CAAAiB,UAAA,SAAAV,MAAA,CAAAC,UAAA,CAAoB;;;ADCjC,OAAM,MAAOY,iBAAiB;EAG5BC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAFpC,KAAAC,OAAO,GAAwB,IAAI;EAEuB;EAE1DC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAACC,WAAoB;IAC9B,IAAI,CAACJ,gBAAgB,CAACK,eAAe,CAACD,WAAW,CAAC,CAACE,SAAS,CAACC,IAAI,IAAG;MAClE,IAAI,CAACN,OAAO,GAAGM,IAAI;IACrB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACP,OAAO,EAAE,OAAO,EAAE;IAE5B,OAAO,CACL,IAAI,CAACA,OAAO,CAACQ,WAAW,EACxB,IAAI,CAACR,OAAO,CAACS,UAAU,EACvB,IAAI,CAACT,OAAO,CAACU,gBAAgB,EAC7B,IAAI,CAACV,OAAO,CAACW,YAAY,EACzB,IAAI,CAACX,OAAO,CAACY,UAAU,EACvB,IAAI,CAACZ,OAAO,CAACa,cAAc,CAC5B;EACH;;;uCA1BWhB,iBAAiB,EAAApB,EAAA,CAAAqC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAjBnB,iBAAiB;MAAAoB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA1C,EAAA,CAAA2C,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ9BjD,EAAA,CAAAC,cAAA,aAAsB;UACpBD,EAAA,CAAAS,UAAA,IAAA0C,gCAAA,mBAAuE;UAgBzEnD,EAAA,CAAAG,YAAA,EAAM;;;UAhBiBH,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAiB,UAAA,YAAAiC,GAAA,CAAApB,WAAA,GAAgB;;;qBDO3B/B,YAAY,EAAAqD,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}