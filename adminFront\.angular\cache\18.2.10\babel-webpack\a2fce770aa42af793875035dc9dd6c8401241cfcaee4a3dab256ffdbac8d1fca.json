{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { NgbPagination } from '@ng-bootstrap/ng-bootstrap';\nimport { NgIf } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nfunction PaginationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"ngb-pagination\", 4);\n    i0.ɵɵtwoWayListener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.Page, $event) || (ctx_r1.Page = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pageChange());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"collectionSize\", ctx_r1.CollectionSize);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r1.Page);\n    i0.ɵɵproperty(\"pageSize\", ctx_r1.PageSize)(\"maxSize\", 5)(\"boundaryLinks\", true);\n  }\n}\nfunction PaginationComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" TotalRecords\\uFF1A\", ctx_r1.CollectionSize, \"\\n\");\n  }\n}\nfunction PaginationComponent_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 6);\n    i0.ɵɵtext(1, \"\\u7121\\u4EFB\\u4F55\\u8CC7\\u6599.\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PaginationComponent {\n  constructor() {\n    this.PageChange = new EventEmitter();\n    this.PageSize = 0;\n    this.CollectionSize = 0;\n    this.PageSizeChange = new EventEmitter();\n    this.CollectionSizeChange = new EventEmitter();\n  }\n  ngOnInit() {}\n  pageChange() {\n    this.PageChange.emit(this.Page);\n  }\n  static {\n    this.ɵfac = function PaginationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PaginationComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaginationComponent,\n      selectors: [[\"ngx-pagination\"]],\n      inputs: {\n        Page: \"Page\",\n        PageSize: \"PageSize\",\n        CollectionSize: \"CollectionSize\"\n      },\n      outputs: {\n        PageChange: \"PageChange\",\n        PageSizeChange: \"PageSizeChange\",\n        CollectionSizeChange: \"CollectionSizeChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"class\", \"d-flex justify-content-center p-2\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"text-center text-danger fw-bold\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"p-2\"], [3, \"pageChange\", \"collectionSize\", \"page\", \"pageSize\", \"maxSize\", \"boundaryLinks\"], [1, \"text-center\"], [1, \"text-center\", \"text-danger\", \"fw-bold\"]],\n      template: function PaginationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, PaginationComponent_div_0_Template, 2, 5, \"div\", 0)(1, PaginationComponent_div_1_Template, 2, 1, \"div\", 1)(2, PaginationComponent_p_2_Template, 2, 0, \"p\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize === 0);\n        }\n      },\n      dependencies: [NgIf, NgbPagination],\n      styles: [\".pagination-theme[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\\n  color: #fff;\\n  border-color: #357abd;\\n  box-shadow: 0 2px 8px rgba(53, 122, 189, 0.08);\\n}\\n.pagination-theme[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #357abd;\\n  border-radius: 6px;\\n  border: 1px solid #e4e7ea;\\n  background: #fff;\\n  transition: all 0.2s;\\n}\\n.pagination-theme[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background: #e4e7ea;\\n  color: #4a90e2;\\n  border-color: #4a90e2;\\n}\\n\\n.pagination-theme[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%] {\\n  gap: 4px;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInBhZ2luYXRpb24uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBRUU7RUFDRSw2REFBQTtFQUNBLFdBQUE7RUFDQSxxQkFBQTtFQUNBLDhDQUFBO0FBREo7QUFJRTtFQUNFLGNBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQkFBQTtBQUZKO0FBSUk7RUFDRSxtQkFBQTtFQUNBLGNBQUE7RUFDQSxxQkFBQTtBQUZOOztBQU9BO0VBQ0UsUUFBQTtBQUpGIiwiZmlsZSI6InBhZ2luYXRpb24uY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvLyA1ajN3dTYg5Li76aGM5YiG6aCB6YWN6ImyXHJcbi5wYWdpbmF0aW9uLXRoZW1lIC5wYWdlLWl0ZW0ge1xyXG4gICYuYWN0aXZlIC5wYWdlLWxpbmsge1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRhOTBlMiAwJSwgIzM1N2FiZCAxMDAlKTtcclxuICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgYm9yZGVyLWNvbG9yOiAjMzU3YWJkO1xyXG4gICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNTMsIDEyMiwgMTg5LCAwLjA4KTtcclxuICB9XHJcblxyXG4gIC5wYWdlLWxpbmsge1xyXG4gICAgY29sb3I6ICMzNTdhYmQ7XHJcbiAgICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VhO1xyXG4gICAgYmFja2dyb3VuZDogI2ZmZjtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzO1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjZTRlN2VhO1xyXG4gICAgICBjb2xvcjogIzRhOTBlMjtcclxuICAgICAgYm9yZGVyLWNvbG9yOiAjNGE5MGUyO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLnBhZ2luYXRpb24tdGhlbWUgLnBhZ2luYXRpb24ge1xyXG4gIGdhcDogNHB4O1xyXG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29tcG9uZW50cy9wYWdpbmF0aW9uL3BhZ2luYXRpb24uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBRUU7RUFDRSw2REFBQTtFQUNBLFdBQUE7RUFDQSxxQkFBQTtFQUNBLDhDQUFBO0FBREo7QUFJRTtFQUNFLGNBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQkFBQTtBQUZKO0FBSUk7RUFDRSxtQkFBQTtFQUNBLGNBQUE7RUFDQSxxQkFBQTtBQUZOOztBQU9BO0VBQ0UsUUFBQTtBQUpGO0FBQ0EsZ3hDQUFneEMiLCJzb3VyY2VzQ29udGVudCI6WyIvLyA1ajN3dTYgw6TCuMK7w6nCocKMw6XCiMKGw6nCoMKBw6nChcKNw6jCicKyXHJcbi5wYWdpbmF0aW9uLXRoZW1lIC5wYWdlLWl0ZW0ge1xyXG4gICYuYWN0aXZlIC5wYWdlLWxpbmsge1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRhOTBlMiAwJSwgIzM1N2FiZCAxMDAlKTtcclxuICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgYm9yZGVyLWNvbG9yOiAjMzU3YWJkO1xyXG4gICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNTMsIDEyMiwgMTg5LCAwLjA4KTtcclxuICB9XHJcblxyXG4gIC5wYWdlLWxpbmsge1xyXG4gICAgY29sb3I6ICMzNTdhYmQ7XHJcbiAgICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VhO1xyXG4gICAgYmFja2dyb3VuZDogI2ZmZjtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzO1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjZTRlN2VhO1xyXG4gICAgICBjb2xvcjogIzRhOTBlMjtcclxuICAgICAgYm9yZGVyLWNvbG9yOiAjNGE5MGUyO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLnBhZ2luYXRpb24tdGhlbWUgLnBhZ2luYXRpb24ge1xyXG4gIGdhcDogNHB4O1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "NgbPagination", "NgIf", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "Page", "ɵɵresetView", "ɵɵlistener", "pageChange", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "CollectionSize", "ɵɵtwoWayProperty", "PageSize", "ɵɵtext", "ɵɵtextInterpolate1", "PaginationComponent", "constructor", "PageChange", "PageSizeChange", "CollectionSizeChange", "ngOnInit", "emit", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PaginationComponent_Template", "rf", "ctx", "ɵɵtemplate", "PaginationComponent_div_0_Template", "PaginationComponent_div_1_Template", "PaginationComponent_p_2_Template", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\pagination\\pagination.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\pagination\\pagination.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { NgbPagination } from '@ng-bootstrap/ng-bootstrap';\r\nimport { NgIf } from '@angular/common';\r\n\r\n@Component({\r\n    selector: 'ngx-pagination',\r\n    templateUrl: './pagination.component.html',\r\n    styleUrls: ['./pagination.component.scss'],\r\n    standalone: true,\r\n    imports: [NgIf, NgbPagination]\r\n})\r\nexport class PaginationComponent implements OnInit {\r\n\r\n  @Output() PageChange = new EventEmitter();\r\n  @Input() Page: number | undefined;\r\n  @Input() PageSize: number = 0;\r\n  @Input() CollectionSize: number = 0;\r\n\r\n  @Output() PageSizeChange = new EventEmitter()\r\n  @Output() CollectionSizeChange = new EventEmitter()\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  pageChange() {\r\n    this.PageChange.emit(this.Page);\r\n  }\r\n\r\n}\r\n", "<div class=\"d-flex justify-content-center p-2\" *ngIf=\"CollectionSize!>0\">\r\n  <ngb-pagination [collectionSize]=\"CollectionSize\" [(page)]=\"Page!\" [pageSize]=\"PageSize\"\r\n    (pageChange)=\"pageChange()\" [maxSize]=\"5\" [boundaryLinks]=\"true\">\r\n  </ngb-pagination>\r\n\r\n</div>\r\n<div class=\"text-center\" *ngIf=\"CollectionSize>0\">\r\n  TotalRecords：{{CollectionSize}}\r\n</div>\r\n\r\n<p class=\"text-center text-danger fw-bold\" *ngIf=\"CollectionSize===0\">無任何資料.</p>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,IAAI,QAAQ,iBAAiB;;;;;ICDpCC,EADF,CAAAC,cAAA,aAAyE,wBAEJ;IADjBD,EAAA,CAAAE,gBAAA,wBAAAC,wEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAG,IAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,IAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAgB;IAChEJ,EAAA,CAAAY,UAAA,wBAAAT,wEAAA;MAAAH,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAcJ,MAAA,CAAAM,UAAA,EAAY;IAAA,EAAC;IAG/Bb,EAFE,CAAAc,YAAA,EAAiB,EAEb;;;;IAJYd,EAAA,CAAAe,SAAA,EAAiC;IAAjCf,EAAA,CAAAgB,UAAA,mBAAAT,MAAA,CAAAU,cAAA,CAAiC;IAACjB,EAAA,CAAAkB,gBAAA,SAAAX,MAAA,CAAAG,IAAA,CAAgB;IACtBV,EADuB,CAAAgB,UAAA,aAAAT,MAAA,CAAAY,QAAA,CAAqB,cAC7C,uBAAuB;;;;;IAIpEnB,EAAA,CAAAC,cAAA,aAAkD;IAChDD,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAc,YAAA,EAAM;;;;IADJd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAqB,kBAAA,wBAAAd,MAAA,CAAAU,cAAA,OACF;;;;;IAEAjB,EAAA,CAAAC,cAAA,WAAsE;IAAAD,EAAA,CAAAoB,MAAA,sCAAM;IAAApB,EAAA,CAAAc,YAAA,EAAI;;;ADChF,OAAM,MAAOQ,mBAAmB;EAU9BC,YAAA;IARU,KAAAC,UAAU,GAAG,IAAI3B,YAAY,EAAE;IAEhC,KAAAsB,QAAQ,GAAW,CAAC;IACpB,KAAAF,cAAc,GAAW,CAAC;IAEzB,KAAAQ,cAAc,GAAG,IAAI5B,YAAY,EAAE;IACnC,KAAA6B,oBAAoB,GAAG,IAAI7B,YAAY,EAAE;EAEnC;EAEhB8B,QAAQA,CAAA,GACR;EAEAd,UAAUA,CAAA;IACR,IAAI,CAACW,UAAU,CAACI,IAAI,CAAC,IAAI,CAAClB,IAAI,CAAC;EACjC;;;uCAjBWY,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAO,SAAA;MAAAC,MAAA;QAAApB,IAAA;QAAAS,QAAA;QAAAF,cAAA;MAAA;MAAAc,OAAA;QAAAP,UAAA;QAAAC,cAAA;QAAAC,oBAAA;MAAA;MAAAM,UAAA;MAAAC,QAAA,GAAAjC,EAAA,CAAAkC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCDhCxC,EAVA,CAAA0C,UAAA,IAAAC,kCAAA,iBAAyE,IAAAC,kCAAA,iBAMvB,IAAAC,gCAAA,eAIoB;;;UAVtB7C,EAAA,CAAAgB,UAAA,SAAAyB,GAAA,CAAAxB,cAAA,KAAuB;UAM7CjB,EAAA,CAAAe,SAAA,EAAsB;UAAtBf,EAAA,CAAAgB,UAAA,SAAAyB,GAAA,CAAAxB,cAAA,KAAsB;UAIJjB,EAAA,CAAAe,SAAA,EAAwB;UAAxBf,EAAA,CAAAgB,UAAA,SAAAyB,GAAA,CAAAxB,cAAA,OAAwB;;;qBDDtDlB,IAAI,EAAED,aAAa;MAAAgD,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}