{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, isArray, isObject, isTypedArray, defaults } from 'zrender/lib/core/util.js';\nimport compatStyle from './helper/compatStyle.js';\nimport { normalizeToArray } from '../util/model.js';\nimport { deprecateLog, deprecateReplaceLog } from '../util/log.js';\nfunction get(opt, path) {\n  var pathArr = path.split(',');\n  var obj = opt;\n  for (var i = 0; i < pathArr.length; i++) {\n    obj = obj && obj[pathArr[i]];\n    if (obj == null) {\n      break;\n    }\n  }\n  return obj;\n}\nfunction set(opt, path, val, overwrite) {\n  var pathArr = path.split(',');\n  var obj = opt;\n  var key;\n  var i = 0;\n  for (; i < pathArr.length - 1; i++) {\n    key = pathArr[i];\n    if (obj[key] == null) {\n      obj[key] = {};\n    }\n    obj = obj[key];\n  }\n  if (overwrite || obj[pathArr[i]] == null) {\n    obj[pathArr[i]] = val;\n  }\n}\nfunction compatLayoutProperties(option) {\n  option && each(LAYOUT_PROPERTIES, function (prop) {\n    if (prop[0] in option && !(prop[1] in option)) {\n      option[prop[1]] = option[prop[0]];\n    }\n  });\n}\nvar LAYOUT_PROPERTIES = [['x', 'left'], ['y', 'top'], ['x2', 'right'], ['y2', 'bottom']];\nvar COMPATITABLE_COMPONENTS = ['grid', 'geo', 'parallel', 'legend', 'toolbox', 'title', 'visualMap', 'dataZoom', 'timeline'];\nvar BAR_ITEM_STYLE_MAP = [['borderRadius', 'barBorderRadius'], ['borderColor', 'barBorderColor'], ['borderWidth', 'barBorderWidth']];\nfunction compatBarItemStyle(option) {\n  var itemStyle = option && option.itemStyle;\n  if (itemStyle) {\n    for (var i = 0; i < BAR_ITEM_STYLE_MAP.length; i++) {\n      var oldName = BAR_ITEM_STYLE_MAP[i][1];\n      var newName = BAR_ITEM_STYLE_MAP[i][0];\n      if (itemStyle[oldName] != null) {\n        itemStyle[newName] = itemStyle[oldName];\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog(oldName, newName);\n        }\n      }\n    }\n  }\n}\nfunction compatPieLabel(option) {\n  if (!option) {\n    return;\n  }\n  if (option.alignTo === 'edge' && option.margin != null && option.edgeDistance == null) {\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('label.margin', 'label.edgeDistance', 'pie');\n    }\n    option.edgeDistance = option.margin;\n  }\n}\nfunction compatSunburstState(option) {\n  if (!option) {\n    return;\n  }\n  if (option.downplay && !option.blur) {\n    option.blur = option.downplay;\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('downplay', 'blur', 'sunburst');\n    }\n  }\n}\nfunction compatGraphFocus(option) {\n  if (!option) {\n    return;\n  }\n  if (option.focusNodeAdjacency != null) {\n    option.emphasis = option.emphasis || {};\n    if (option.emphasis.focus == null) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog('focusNodeAdjacency', 'emphasis: { focus: \\'adjacency\\'}', 'graph/sankey');\n      }\n      option.emphasis.focus = 'adjacency';\n    }\n  }\n}\nfunction traverseTree(data, cb) {\n  if (data) {\n    for (var i = 0; i < data.length; i++) {\n      cb(data[i]);\n      data[i] && traverseTree(data[i].children, cb);\n    }\n  }\n}\nexport default function globalBackwardCompat(option, isTheme) {\n  compatStyle(option, isTheme);\n  // Make sure series array for model initialization.\n  option.series = normalizeToArray(option.series);\n  each(option.series, function (seriesOpt) {\n    if (!isObject(seriesOpt)) {\n      return;\n    }\n    var seriesType = seriesOpt.type;\n    if (seriesType === 'line') {\n      if (seriesOpt.clipOverflow != null) {\n        seriesOpt.clip = seriesOpt.clipOverflow;\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('clipOverflow', 'clip', 'line');\n        }\n      }\n    } else if (seriesType === 'pie' || seriesType === 'gauge') {\n      if (seriesOpt.clockWise != null) {\n        seriesOpt.clockwise = seriesOpt.clockWise;\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('clockWise', 'clockwise');\n        }\n      }\n      compatPieLabel(seriesOpt.label);\n      var data = seriesOpt.data;\n      if (data && !isTypedArray(data)) {\n        for (var i = 0; i < data.length; i++) {\n          compatPieLabel(data[i]);\n        }\n      }\n      if (seriesOpt.hoverOffset != null) {\n        seriesOpt.emphasis = seriesOpt.emphasis || {};\n        if (seriesOpt.emphasis.scaleSize = null) {\n          if (process.env.NODE_ENV !== 'production') {\n            deprecateReplaceLog('hoverOffset', 'emphasis.scaleSize');\n          }\n          seriesOpt.emphasis.scaleSize = seriesOpt.hoverOffset;\n        }\n      }\n    } else if (seriesType === 'gauge') {\n      var pointerColor = get(seriesOpt, 'pointer.color');\n      pointerColor != null && set(seriesOpt, 'itemStyle.color', pointerColor);\n    } else if (seriesType === 'bar') {\n      compatBarItemStyle(seriesOpt);\n      compatBarItemStyle(seriesOpt.backgroundStyle);\n      compatBarItemStyle(seriesOpt.emphasis);\n      var data = seriesOpt.data;\n      if (data && !isTypedArray(data)) {\n        for (var i = 0; i < data.length; i++) {\n          if (typeof data[i] === 'object') {\n            compatBarItemStyle(data[i]);\n            compatBarItemStyle(data[i] && data[i].emphasis);\n          }\n        }\n      }\n    } else if (seriesType === 'sunburst') {\n      var highlightPolicy = seriesOpt.highlightPolicy;\n      if (highlightPolicy) {\n        seriesOpt.emphasis = seriesOpt.emphasis || {};\n        if (!seriesOpt.emphasis.focus) {\n          seriesOpt.emphasis.focus = highlightPolicy;\n          if (process.env.NODE_ENV !== 'production') {\n            deprecateReplaceLog('highlightPolicy', 'emphasis.focus', 'sunburst');\n          }\n        }\n      }\n      compatSunburstState(seriesOpt);\n      traverseTree(seriesOpt.data, compatSunburstState);\n    } else if (seriesType === 'graph' || seriesType === 'sankey') {\n      compatGraphFocus(seriesOpt);\n      // TODO nodes, edges?\n    } else if (seriesType === 'map') {\n      if (seriesOpt.mapType && !seriesOpt.map) {\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('mapType', 'map', 'map');\n        }\n        seriesOpt.map = seriesOpt.mapType;\n      }\n      if (seriesOpt.mapLocation) {\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateLog('`mapLocation` is not used anymore.');\n        }\n        defaults(seriesOpt, seriesOpt.mapLocation);\n      }\n    }\n    if (seriesOpt.hoverAnimation != null) {\n      seriesOpt.emphasis = seriesOpt.emphasis || {};\n      if (seriesOpt.emphasis && seriesOpt.emphasis.scale == null) {\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('hoverAnimation', 'emphasis.scale');\n        }\n        seriesOpt.emphasis.scale = seriesOpt.hoverAnimation;\n      }\n    }\n    compatLayoutProperties(seriesOpt);\n  });\n  // dataRange has changed to visualMap\n  if (option.dataRange) {\n    option.visualMap = option.dataRange;\n  }\n  each(COMPATITABLE_COMPONENTS, function (componentName) {\n    var options = option[componentName];\n    if (options) {\n      if (!isArray(options)) {\n        options = [options];\n      }\n      each(options, function (option) {\n        compatLayoutProperties(option);\n      });\n    }\n  });\n}", "map": {"version": 3, "names": ["each", "isArray", "isObject", "isTypedArray", "defaults", "compatStyle", "normalizeToArray", "deprecateLog", "deprecateReplaceLog", "get", "opt", "path", "pathArr", "split", "obj", "i", "length", "set", "val", "overwrite", "key", "compatLayoutProperties", "option", "LAYOUT_PROPERTIES", "prop", "COMPATITABLE_COMPONENTS", "BAR_ITEM_STYLE_MAP", "compatBarItemStyle", "itemStyle", "old<PERSON>ame", "newName", "process", "env", "NODE_ENV", "compatPieLabel", "alignTo", "margin", "edgeDistance", "compatSunburstState", "downplay", "blur", "compatGraphFocus", "focusNodeAdjacency", "emphasis", "focus", "traverseTree", "data", "cb", "children", "globalBackwardCompat", "isTheme", "series", "seriesOpt", "seriesType", "type", "clipOverflow", "clip", "clockWise", "clockwise", "label", "hoverOffset", "scaleSize", "pointerColor", "backgroundStyle", "highlightPolicy", "mapType", "map", "mapLocation", "hoverAnimation", "scale", "dataRange", "visualMap", "componentName", "options"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/preprocessor/backwardCompat.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, isArray, isObject, isTypedArray, defaults } from 'zrender/lib/core/util.js';\nimport compatStyle from './helper/compatStyle.js';\nimport { normalizeToArray } from '../util/model.js';\nimport { deprecateLog, deprecateReplaceLog } from '../util/log.js';\nfunction get(opt, path) {\n  var pathArr = path.split(',');\n  var obj = opt;\n  for (var i = 0; i < pathArr.length; i++) {\n    obj = obj && obj[pathArr[i]];\n    if (obj == null) {\n      break;\n    }\n  }\n  return obj;\n}\nfunction set(opt, path, val, overwrite) {\n  var pathArr = path.split(',');\n  var obj = opt;\n  var key;\n  var i = 0;\n  for (; i < pathArr.length - 1; i++) {\n    key = pathArr[i];\n    if (obj[key] == null) {\n      obj[key] = {};\n    }\n    obj = obj[key];\n  }\n  if (overwrite || obj[pathArr[i]] == null) {\n    obj[pathArr[i]] = val;\n  }\n}\nfunction compatLayoutProperties(option) {\n  option && each(LAYOUT_PROPERTIES, function (prop) {\n    if (prop[0] in option && !(prop[1] in option)) {\n      option[prop[1]] = option[prop[0]];\n    }\n  });\n}\nvar LAYOUT_PROPERTIES = [['x', 'left'], ['y', 'top'], ['x2', 'right'], ['y2', 'bottom']];\nvar COMPATITABLE_COMPONENTS = ['grid', 'geo', 'parallel', 'legend', 'toolbox', 'title', 'visualMap', 'dataZoom', 'timeline'];\nvar BAR_ITEM_STYLE_MAP = [['borderRadius', 'barBorderRadius'], ['borderColor', 'barBorderColor'], ['borderWidth', 'barBorderWidth']];\nfunction compatBarItemStyle(option) {\n  var itemStyle = option && option.itemStyle;\n  if (itemStyle) {\n    for (var i = 0; i < BAR_ITEM_STYLE_MAP.length; i++) {\n      var oldName = BAR_ITEM_STYLE_MAP[i][1];\n      var newName = BAR_ITEM_STYLE_MAP[i][0];\n      if (itemStyle[oldName] != null) {\n        itemStyle[newName] = itemStyle[oldName];\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog(oldName, newName);\n        }\n      }\n    }\n  }\n}\nfunction compatPieLabel(option) {\n  if (!option) {\n    return;\n  }\n  if (option.alignTo === 'edge' && option.margin != null && option.edgeDistance == null) {\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('label.margin', 'label.edgeDistance', 'pie');\n    }\n    option.edgeDistance = option.margin;\n  }\n}\nfunction compatSunburstState(option) {\n  if (!option) {\n    return;\n  }\n  if (option.downplay && !option.blur) {\n    option.blur = option.downplay;\n    if (process.env.NODE_ENV !== 'production') {\n      deprecateReplaceLog('downplay', 'blur', 'sunburst');\n    }\n  }\n}\nfunction compatGraphFocus(option) {\n  if (!option) {\n    return;\n  }\n  if (option.focusNodeAdjacency != null) {\n    option.emphasis = option.emphasis || {};\n    if (option.emphasis.focus == null) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog('focusNodeAdjacency', 'emphasis: { focus: \\'adjacency\\'}', 'graph/sankey');\n      }\n      option.emphasis.focus = 'adjacency';\n    }\n  }\n}\nfunction traverseTree(data, cb) {\n  if (data) {\n    for (var i = 0; i < data.length; i++) {\n      cb(data[i]);\n      data[i] && traverseTree(data[i].children, cb);\n    }\n  }\n}\nexport default function globalBackwardCompat(option, isTheme) {\n  compatStyle(option, isTheme);\n  // Make sure series array for model initialization.\n  option.series = normalizeToArray(option.series);\n  each(option.series, function (seriesOpt) {\n    if (!isObject(seriesOpt)) {\n      return;\n    }\n    var seriesType = seriesOpt.type;\n    if (seriesType === 'line') {\n      if (seriesOpt.clipOverflow != null) {\n        seriesOpt.clip = seriesOpt.clipOverflow;\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('clipOverflow', 'clip', 'line');\n        }\n      }\n    } else if (seriesType === 'pie' || seriesType === 'gauge') {\n      if (seriesOpt.clockWise != null) {\n        seriesOpt.clockwise = seriesOpt.clockWise;\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('clockWise', 'clockwise');\n        }\n      }\n      compatPieLabel(seriesOpt.label);\n      var data = seriesOpt.data;\n      if (data && !isTypedArray(data)) {\n        for (var i = 0; i < data.length; i++) {\n          compatPieLabel(data[i]);\n        }\n      }\n      if (seriesOpt.hoverOffset != null) {\n        seriesOpt.emphasis = seriesOpt.emphasis || {};\n        if (seriesOpt.emphasis.scaleSize = null) {\n          if (process.env.NODE_ENV !== 'production') {\n            deprecateReplaceLog('hoverOffset', 'emphasis.scaleSize');\n          }\n          seriesOpt.emphasis.scaleSize = seriesOpt.hoverOffset;\n        }\n      }\n    } else if (seriesType === 'gauge') {\n      var pointerColor = get(seriesOpt, 'pointer.color');\n      pointerColor != null && set(seriesOpt, 'itemStyle.color', pointerColor);\n    } else if (seriesType === 'bar') {\n      compatBarItemStyle(seriesOpt);\n      compatBarItemStyle(seriesOpt.backgroundStyle);\n      compatBarItemStyle(seriesOpt.emphasis);\n      var data = seriesOpt.data;\n      if (data && !isTypedArray(data)) {\n        for (var i = 0; i < data.length; i++) {\n          if (typeof data[i] === 'object') {\n            compatBarItemStyle(data[i]);\n            compatBarItemStyle(data[i] && data[i].emphasis);\n          }\n        }\n      }\n    } else if (seriesType === 'sunburst') {\n      var highlightPolicy = seriesOpt.highlightPolicy;\n      if (highlightPolicy) {\n        seriesOpt.emphasis = seriesOpt.emphasis || {};\n        if (!seriesOpt.emphasis.focus) {\n          seriesOpt.emphasis.focus = highlightPolicy;\n          if (process.env.NODE_ENV !== 'production') {\n            deprecateReplaceLog('highlightPolicy', 'emphasis.focus', 'sunburst');\n          }\n        }\n      }\n      compatSunburstState(seriesOpt);\n      traverseTree(seriesOpt.data, compatSunburstState);\n    } else if (seriesType === 'graph' || seriesType === 'sankey') {\n      compatGraphFocus(seriesOpt);\n      // TODO nodes, edges?\n    } else if (seriesType === 'map') {\n      if (seriesOpt.mapType && !seriesOpt.map) {\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('mapType', 'map', 'map');\n        }\n        seriesOpt.map = seriesOpt.mapType;\n      }\n      if (seriesOpt.mapLocation) {\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateLog('`mapLocation` is not used anymore.');\n        }\n        defaults(seriesOpt, seriesOpt.mapLocation);\n      }\n    }\n    if (seriesOpt.hoverAnimation != null) {\n      seriesOpt.emphasis = seriesOpt.emphasis || {};\n      if (seriesOpt.emphasis && seriesOpt.emphasis.scale == null) {\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateReplaceLog('hoverAnimation', 'emphasis.scale');\n        }\n        seriesOpt.emphasis.scale = seriesOpt.hoverAnimation;\n      }\n    }\n    compatLayoutProperties(seriesOpt);\n  });\n  // dataRange has changed to visualMap\n  if (option.dataRange) {\n    option.visualMap = option.dataRange;\n  }\n  each(COMPATITABLE_COMPONENTS, function (componentName) {\n    var options = option[componentName];\n    if (options) {\n      if (!isArray(options)) {\n        options = [options];\n      }\n      each(options, function (option) {\n        compatLayoutProperties(option);\n      });\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,0BAA0B;AAC1F,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,gBAAgB;AAClE,SAASC,GAAGA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACtB,IAAIC,OAAO,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;EAC7B,IAAIC,GAAG,GAAGJ,GAAG;EACb,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACvCD,GAAG,GAAGA,GAAG,IAAIA,GAAG,CAACF,OAAO,CAACG,CAAC,CAAC,CAAC;IAC5B,IAAID,GAAG,IAAI,IAAI,EAAE;MACf;IACF;EACF;EACA,OAAOA,GAAG;AACZ;AACA,SAASG,GAAGA,CAACP,GAAG,EAAEC,IAAI,EAAEO,GAAG,EAAEC,SAAS,EAAE;EACtC,IAAIP,OAAO,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;EAC7B,IAAIC,GAAG,GAAGJ,GAAG;EACb,IAAIU,GAAG;EACP,IAAIL,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGH,OAAO,CAACI,MAAM,GAAG,CAAC,EAAED,CAAC,EAAE,EAAE;IAClCK,GAAG,GAAGR,OAAO,CAACG,CAAC,CAAC;IAChB,IAAID,GAAG,CAACM,GAAG,CAAC,IAAI,IAAI,EAAE;MACpBN,GAAG,CAACM,GAAG,CAAC,GAAG,CAAC,CAAC;IACf;IACAN,GAAG,GAAGA,GAAG,CAACM,GAAG,CAAC;EAChB;EACA,IAAID,SAAS,IAAIL,GAAG,CAACF,OAAO,CAACG,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;IACxCD,GAAG,CAACF,OAAO,CAACG,CAAC,CAAC,CAAC,GAAGG,GAAG;EACvB;AACF;AACA,SAASG,sBAAsBA,CAACC,MAAM,EAAE;EACtCA,MAAM,IAAItB,IAAI,CAACuB,iBAAiB,EAAE,UAAUC,IAAI,EAAE;IAChD,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAIF,MAAM,IAAI,EAAEE,IAAI,CAAC,CAAC,CAAC,IAAIF,MAAM,CAAC,EAAE;MAC7CA,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGF,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;IACnC;EACF,CAAC,CAAC;AACJ;AACA,IAAID,iBAAiB,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACxF,IAAIE,uBAAuB,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC;AAC5H,IAAIC,kBAAkB,GAAG,CAAC,CAAC,cAAc,EAAE,iBAAiB,CAAC,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;AACpI,SAASC,kBAAkBA,CAACL,MAAM,EAAE;EAClC,IAAIM,SAAS,GAAGN,MAAM,IAAIA,MAAM,CAACM,SAAS;EAC1C,IAAIA,SAAS,EAAE;IACb,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,kBAAkB,CAACV,MAAM,EAAED,CAAC,EAAE,EAAE;MAClD,IAAIc,OAAO,GAAGH,kBAAkB,CAACX,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,IAAIe,OAAO,GAAGJ,kBAAkB,CAACX,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,IAAIa,SAAS,CAACC,OAAO,CAAC,IAAI,IAAI,EAAE;QAC9BD,SAAS,CAACE,OAAO,CAAC,GAAGF,SAAS,CAACC,OAAO,CAAC;QACvC,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCzB,mBAAmB,CAACqB,OAAO,EAAEC,OAAO,CAAC;QACvC;MACF;IACF;EACF;AACF;AACA,SAASI,cAAcA,CAACZ,MAAM,EAAE;EAC9B,IAAI,CAACA,MAAM,EAAE;IACX;EACF;EACA,IAAIA,MAAM,CAACa,OAAO,KAAK,MAAM,IAAIb,MAAM,CAACc,MAAM,IAAI,IAAI,IAAId,MAAM,CAACe,YAAY,IAAI,IAAI,EAAE;IACrF,IAAIN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCzB,mBAAmB,CAAC,cAAc,EAAE,oBAAoB,EAAE,KAAK,CAAC;IAClE;IACAc,MAAM,CAACe,YAAY,GAAGf,MAAM,CAACc,MAAM;EACrC;AACF;AACA,SAASE,mBAAmBA,CAAChB,MAAM,EAAE;EACnC,IAAI,CAACA,MAAM,EAAE;IACX;EACF;EACA,IAAIA,MAAM,CAACiB,QAAQ,IAAI,CAACjB,MAAM,CAACkB,IAAI,EAAE;IACnClB,MAAM,CAACkB,IAAI,GAAGlB,MAAM,CAACiB,QAAQ;IAC7B,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCzB,mBAAmB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC;IACrD;EACF;AACF;AACA,SAASiC,gBAAgBA,CAACnB,MAAM,EAAE;EAChC,IAAI,CAACA,MAAM,EAAE;IACX;EACF;EACA,IAAIA,MAAM,CAACoB,kBAAkB,IAAI,IAAI,EAAE;IACrCpB,MAAM,CAACqB,QAAQ,GAAGrB,MAAM,CAACqB,QAAQ,IAAI,CAAC,CAAC;IACvC,IAAIrB,MAAM,CAACqB,QAAQ,CAACC,KAAK,IAAI,IAAI,EAAE;MACjC,IAAIb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCzB,mBAAmB,CAAC,oBAAoB,EAAE,mCAAmC,EAAE,cAAc,CAAC;MAChG;MACAc,MAAM,CAACqB,QAAQ,CAACC,KAAK,GAAG,WAAW;IACrC;EACF;AACF;AACA,SAASC,YAAYA,CAACC,IAAI,EAAEC,EAAE,EAAE;EAC9B,IAAID,IAAI,EAAE;IACR,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,IAAI,CAAC9B,MAAM,EAAED,CAAC,EAAE,EAAE;MACpCgC,EAAE,CAACD,IAAI,CAAC/B,CAAC,CAAC,CAAC;MACX+B,IAAI,CAAC/B,CAAC,CAAC,IAAI8B,YAAY,CAACC,IAAI,CAAC/B,CAAC,CAAC,CAACiC,QAAQ,EAAED,EAAE,CAAC;IAC/C;EACF;AACF;AACA,eAAe,SAASE,oBAAoBA,CAAC3B,MAAM,EAAE4B,OAAO,EAAE;EAC5D7C,WAAW,CAACiB,MAAM,EAAE4B,OAAO,CAAC;EAC5B;EACA5B,MAAM,CAAC6B,MAAM,GAAG7C,gBAAgB,CAACgB,MAAM,CAAC6B,MAAM,CAAC;EAC/CnD,IAAI,CAACsB,MAAM,CAAC6B,MAAM,EAAE,UAAUC,SAAS,EAAE;IACvC,IAAI,CAAClD,QAAQ,CAACkD,SAAS,CAAC,EAAE;MACxB;IACF;IACA,IAAIC,UAAU,GAAGD,SAAS,CAACE,IAAI;IAC/B,IAAID,UAAU,KAAK,MAAM,EAAE;MACzB,IAAID,SAAS,CAACG,YAAY,IAAI,IAAI,EAAE;QAClCH,SAAS,CAACI,IAAI,GAAGJ,SAAS,CAACG,YAAY;QACvC,IAAIxB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCzB,mBAAmB,CAAC,cAAc,EAAE,MAAM,EAAE,MAAM,CAAC;QACrD;MACF;IACF,CAAC,MAAM,IAAI6C,UAAU,KAAK,KAAK,IAAIA,UAAU,KAAK,OAAO,EAAE;MACzD,IAAID,SAAS,CAACK,SAAS,IAAI,IAAI,EAAE;QAC/BL,SAAS,CAACM,SAAS,GAAGN,SAAS,CAACK,SAAS;QACzC,IAAI1B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCzB,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC;QAC/C;MACF;MACA0B,cAAc,CAACkB,SAAS,CAACO,KAAK,CAAC;MAC/B,IAAIb,IAAI,GAAGM,SAAS,CAACN,IAAI;MACzB,IAAIA,IAAI,IAAI,CAAC3C,YAAY,CAAC2C,IAAI,CAAC,EAAE;QAC/B,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,IAAI,CAAC9B,MAAM,EAAED,CAAC,EAAE,EAAE;UACpCmB,cAAc,CAACY,IAAI,CAAC/B,CAAC,CAAC,CAAC;QACzB;MACF;MACA,IAAIqC,SAAS,CAACQ,WAAW,IAAI,IAAI,EAAE;QACjCR,SAAS,CAACT,QAAQ,GAAGS,SAAS,CAACT,QAAQ,IAAI,CAAC,CAAC;QAC7C,IAAIS,SAAS,CAACT,QAAQ,CAACkB,SAAS,GAAG,IAAI,EAAE;UACvC,IAAI9B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzCzB,mBAAmB,CAAC,aAAa,EAAE,oBAAoB,CAAC;UAC1D;UACA4C,SAAS,CAACT,QAAQ,CAACkB,SAAS,GAAGT,SAAS,CAACQ,WAAW;QACtD;MACF;IACF,CAAC,MAAM,IAAIP,UAAU,KAAK,OAAO,EAAE;MACjC,IAAIS,YAAY,GAAGrD,GAAG,CAAC2C,SAAS,EAAE,eAAe,CAAC;MAClDU,YAAY,IAAI,IAAI,IAAI7C,GAAG,CAACmC,SAAS,EAAE,iBAAiB,EAAEU,YAAY,CAAC;IACzE,CAAC,MAAM,IAAIT,UAAU,KAAK,KAAK,EAAE;MAC/B1B,kBAAkB,CAACyB,SAAS,CAAC;MAC7BzB,kBAAkB,CAACyB,SAAS,CAACW,eAAe,CAAC;MAC7CpC,kBAAkB,CAACyB,SAAS,CAACT,QAAQ,CAAC;MACtC,IAAIG,IAAI,GAAGM,SAAS,CAACN,IAAI;MACzB,IAAIA,IAAI,IAAI,CAAC3C,YAAY,CAAC2C,IAAI,CAAC,EAAE;QAC/B,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,IAAI,CAAC9B,MAAM,EAAED,CAAC,EAAE,EAAE;UACpC,IAAI,OAAO+B,IAAI,CAAC/B,CAAC,CAAC,KAAK,QAAQ,EAAE;YAC/BY,kBAAkB,CAACmB,IAAI,CAAC/B,CAAC,CAAC,CAAC;YAC3BY,kBAAkB,CAACmB,IAAI,CAAC/B,CAAC,CAAC,IAAI+B,IAAI,CAAC/B,CAAC,CAAC,CAAC4B,QAAQ,CAAC;UACjD;QACF;MACF;IACF,CAAC,MAAM,IAAIU,UAAU,KAAK,UAAU,EAAE;MACpC,IAAIW,eAAe,GAAGZ,SAAS,CAACY,eAAe;MAC/C,IAAIA,eAAe,EAAE;QACnBZ,SAAS,CAACT,QAAQ,GAAGS,SAAS,CAACT,QAAQ,IAAI,CAAC,CAAC;QAC7C,IAAI,CAACS,SAAS,CAACT,QAAQ,CAACC,KAAK,EAAE;UAC7BQ,SAAS,CAACT,QAAQ,CAACC,KAAK,GAAGoB,eAAe;UAC1C,IAAIjC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzCzB,mBAAmB,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,CAAC;UACtE;QACF;MACF;MACA8B,mBAAmB,CAACc,SAAS,CAAC;MAC9BP,YAAY,CAACO,SAAS,CAACN,IAAI,EAAER,mBAAmB,CAAC;IACnD,CAAC,MAAM,IAAIe,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,QAAQ,EAAE;MAC5DZ,gBAAgB,CAACW,SAAS,CAAC;MAC3B;IACF,CAAC,MAAM,IAAIC,UAAU,KAAK,KAAK,EAAE;MAC/B,IAAID,SAAS,CAACa,OAAO,IAAI,CAACb,SAAS,CAACc,GAAG,EAAE;QACvC,IAAInC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCzB,mBAAmB,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC;QAC9C;QACA4C,SAAS,CAACc,GAAG,GAAGd,SAAS,CAACa,OAAO;MACnC;MACA,IAAIb,SAAS,CAACe,WAAW,EAAE;QACzB,IAAIpC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC1B,YAAY,CAAC,oCAAoC,CAAC;QACpD;QACAH,QAAQ,CAACgD,SAAS,EAAEA,SAAS,CAACe,WAAW,CAAC;MAC5C;IACF;IACA,IAAIf,SAAS,CAACgB,cAAc,IAAI,IAAI,EAAE;MACpChB,SAAS,CAACT,QAAQ,GAAGS,SAAS,CAACT,QAAQ,IAAI,CAAC,CAAC;MAC7C,IAAIS,SAAS,CAACT,QAAQ,IAAIS,SAAS,CAACT,QAAQ,CAAC0B,KAAK,IAAI,IAAI,EAAE;QAC1D,IAAItC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCzB,mBAAmB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;QACzD;QACA4C,SAAS,CAACT,QAAQ,CAAC0B,KAAK,GAAGjB,SAAS,CAACgB,cAAc;MACrD;IACF;IACA/C,sBAAsB,CAAC+B,SAAS,CAAC;EACnC,CAAC,CAAC;EACF;EACA,IAAI9B,MAAM,CAACgD,SAAS,EAAE;IACpBhD,MAAM,CAACiD,SAAS,GAAGjD,MAAM,CAACgD,SAAS;EACrC;EACAtE,IAAI,CAACyB,uBAAuB,EAAE,UAAU+C,aAAa,EAAE;IACrD,IAAIC,OAAO,GAAGnD,MAAM,CAACkD,aAAa,CAAC;IACnC,IAAIC,OAAO,EAAE;MACX,IAAI,CAACxE,OAAO,CAACwE,OAAO,CAAC,EAAE;QACrBA,OAAO,GAAG,CAACA,OAAO,CAAC;MACrB;MACAzE,IAAI,CAACyE,OAAO,EAAE,UAAUnD,MAAM,EAAE;QAC9BD,sBAAsB,CAACC,MAAM,CAAC;MAChC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}