{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isDimensionStacked } from '../../data/helper/dataStackHelper.js';\nimport { isNumber, map } from 'zrender/lib/core/util.js';\nexport function prepareDataCoordInfo(coordSys, data, valueOrigin) {\n  var baseAxis = coordSys.getBaseAxis();\n  var valueAxis = coordSys.getOtherAxis(baseAxis);\n  var valueStart = getValueStart(valueAxis, valueOrigin);\n  var baseAxisDim = baseAxis.dim;\n  var valueAxisDim = valueAxis.dim;\n  var valueDim = data.mapDimension(valueAxisDim);\n  var baseDim = data.mapDimension(baseAxisDim);\n  var baseDataOffset = valueAxisDim === 'x' || valueAxisDim === 'radius' ? 1 : 0;\n  var dims = map(coordSys.dimensions, function (coordDim) {\n    return data.mapDimension(coordDim);\n  });\n  var stacked = false;\n  var stackResultDim = data.getCalculationInfo('stackResultDimension');\n  if (isDimensionStacked(data, dims[0] /* , dims[1] */)) {\n    // jshint ignore:line\n    stacked = true;\n    dims[0] = stackResultDim;\n  }\n  if (isDimensionStacked(data, dims[1] /* , dims[0] */)) {\n    // jshint ignore:line\n    stacked = true;\n    dims[1] = stackResultDim;\n  }\n  return {\n    dataDimsForPoint: dims,\n    valueStart: valueStart,\n    valueAxisDim: valueAxisDim,\n    baseAxisDim: baseAxisDim,\n    stacked: !!stacked,\n    valueDim: valueDim,\n    baseDim: baseDim,\n    baseDataOffset: baseDataOffset,\n    stackedOverDimension: data.getCalculationInfo('stackedOverDimension')\n  };\n}\nfunction getValueStart(valueAxis, valueOrigin) {\n  var valueStart = 0;\n  var extent = valueAxis.scale.getExtent();\n  if (valueOrigin === 'start') {\n    valueStart = extent[0];\n  } else if (valueOrigin === 'end') {\n    valueStart = extent[1];\n  }\n  // If origin is specified as a number, use it as\n  // valueStart directly\n  else if (isNumber(valueOrigin) && !isNaN(valueOrigin)) {\n    valueStart = valueOrigin;\n  }\n  // auto\n  else {\n    // Both positive\n    if (extent[0] > 0) {\n      valueStart = extent[0];\n    }\n    // Both negative\n    else if (extent[1] < 0) {\n      valueStart = extent[1];\n    }\n    // If is one positive, and one negative, onZero shall be true\n  }\n  return valueStart;\n}\nexport function getStackedOnPoint(dataCoordInfo, coordSys, data, idx) {\n  var value = NaN;\n  if (dataCoordInfo.stacked) {\n    value = data.get(data.getCalculationInfo('stackedOverDimension'), idx);\n  }\n  if (isNaN(value)) {\n    value = dataCoordInfo.valueStart;\n  }\n  var baseDataOffset = dataCoordInfo.baseDataOffset;\n  var stackedData = [];\n  stackedData[baseDataOffset] = data.get(dataCoordInfo.baseDim, idx);\n  stackedData[1 - baseDataOffset] = value;\n  return coordSys.dataToPoint(stackedData);\n}", "map": {"version": 3, "names": ["isDimensionStacked", "isNumber", "map", "prepareDataCoordInfo", "coordSys", "data", "valueOrigin", "baseAxis", "getBaseAxis", "valueAxis", "getOtherAxis", "valueStart", "getValueStart", "baseAxisDim", "dim", "valueAxisDim", "valueDim", "mapDimension", "baseDim", "baseDataOffset", "dims", "dimensions", "coordDim", "stacked", "stackResultDim", "getCalculationInfo", "dataDimsForPoint", "stackedOverDimension", "extent", "scale", "getExtent", "isNaN", "getStackedOnPoint", "dataCoordInfo", "idx", "value", "NaN", "get", "stackedData", "dataToPoint"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/line/helper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isDimensionStacked } from '../../data/helper/dataStackHelper.js';\nimport { isNumber, map } from 'zrender/lib/core/util.js';\nexport function prepareDataCoordInfo(coordSys, data, valueOrigin) {\n  var baseAxis = coordSys.getBaseAxis();\n  var valueAxis = coordSys.getOtherAxis(baseAxis);\n  var valueStart = getValueStart(valueAxis, valueOrigin);\n  var baseAxisDim = baseAxis.dim;\n  var valueAxisDim = valueAxis.dim;\n  var valueDim = data.mapDimension(valueAxisDim);\n  var baseDim = data.mapDimension(baseAxisDim);\n  var baseDataOffset = valueAxisDim === 'x' || valueAxisDim === 'radius' ? 1 : 0;\n  var dims = map(coordSys.dimensions, function (coordDim) {\n    return data.mapDimension(coordDim);\n  });\n  var stacked = false;\n  var stackResultDim = data.getCalculationInfo('stackResultDimension');\n  if (isDimensionStacked(data, dims[0] /* , dims[1] */)) {\n    // jshint ignore:line\n    stacked = true;\n    dims[0] = stackResultDim;\n  }\n  if (isDimensionStacked(data, dims[1] /* , dims[0] */)) {\n    // jshint ignore:line\n    stacked = true;\n    dims[1] = stackResultDim;\n  }\n  return {\n    dataDimsForPoint: dims,\n    valueStart: valueStart,\n    valueAxisDim: valueAxisDim,\n    baseAxisDim: baseAxisDim,\n    stacked: !!stacked,\n    valueDim: valueDim,\n    baseDim: baseDim,\n    baseDataOffset: baseDataOffset,\n    stackedOverDimension: data.getCalculationInfo('stackedOverDimension')\n  };\n}\nfunction getValueStart(valueAxis, valueOrigin) {\n  var valueStart = 0;\n  var extent = valueAxis.scale.getExtent();\n  if (valueOrigin === 'start') {\n    valueStart = extent[0];\n  } else if (valueOrigin === 'end') {\n    valueStart = extent[1];\n  }\n  // If origin is specified as a number, use it as\n  // valueStart directly\n  else if (isNumber(valueOrigin) && !isNaN(valueOrigin)) {\n    valueStart = valueOrigin;\n  }\n  // auto\n  else {\n    // Both positive\n    if (extent[0] > 0) {\n      valueStart = extent[0];\n    }\n    // Both negative\n    else if (extent[1] < 0) {\n      valueStart = extent[1];\n    }\n    // If is one positive, and one negative, onZero shall be true\n  }\n  return valueStart;\n}\nexport function getStackedOnPoint(dataCoordInfo, coordSys, data, idx) {\n  var value = NaN;\n  if (dataCoordInfo.stacked) {\n    value = data.get(data.getCalculationInfo('stackedOverDimension'), idx);\n  }\n  if (isNaN(value)) {\n    value = dataCoordInfo.valueStart;\n  }\n  var baseDataOffset = dataCoordInfo.baseDataOffset;\n  var stackedData = [];\n  stackedData[baseDataOffset] = data.get(dataCoordInfo.baseDim, idx);\n  stackedData[1 - baseDataOffset] = value;\n  return coordSys.dataToPoint(stackedData);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,QAAQ,EAAEC,GAAG,QAAQ,0BAA0B;AACxD,OAAO,SAASC,oBAAoBA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,WAAW,EAAE;EAChE,IAAIC,QAAQ,GAAGH,QAAQ,CAACI,WAAW,CAAC,CAAC;EACrC,IAAIC,SAAS,GAAGL,QAAQ,CAACM,YAAY,CAACH,QAAQ,CAAC;EAC/C,IAAII,UAAU,GAAGC,aAAa,CAACH,SAAS,EAAEH,WAAW,CAAC;EACtD,IAAIO,WAAW,GAAGN,QAAQ,CAACO,GAAG;EAC9B,IAAIC,YAAY,GAAGN,SAAS,CAACK,GAAG;EAChC,IAAIE,QAAQ,GAAGX,IAAI,CAACY,YAAY,CAACF,YAAY,CAAC;EAC9C,IAAIG,OAAO,GAAGb,IAAI,CAACY,YAAY,CAACJ,WAAW,CAAC;EAC5C,IAAIM,cAAc,GAAGJ,YAAY,KAAK,GAAG,IAAIA,YAAY,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC;EAC9E,IAAIK,IAAI,GAAGlB,GAAG,CAACE,QAAQ,CAACiB,UAAU,EAAE,UAAUC,QAAQ,EAAE;IACtD,OAAOjB,IAAI,CAACY,YAAY,CAACK,QAAQ,CAAC;EACpC,CAAC,CAAC;EACF,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIC,cAAc,GAAGnB,IAAI,CAACoB,kBAAkB,CAAC,sBAAsB,CAAC;EACpE,IAAIzB,kBAAkB,CAACK,IAAI,EAAEe,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE;IACrD;IACAG,OAAO,GAAG,IAAI;IACdH,IAAI,CAAC,CAAC,CAAC,GAAGI,cAAc;EAC1B;EACA,IAAIxB,kBAAkB,CAACK,IAAI,EAAEe,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE;IACrD;IACAG,OAAO,GAAG,IAAI;IACdH,IAAI,CAAC,CAAC,CAAC,GAAGI,cAAc;EAC1B;EACA,OAAO;IACLE,gBAAgB,EAAEN,IAAI;IACtBT,UAAU,EAAEA,UAAU;IACtBI,YAAY,EAAEA,YAAY;IAC1BF,WAAW,EAAEA,WAAW;IACxBU,OAAO,EAAE,CAAC,CAACA,OAAO;IAClBP,QAAQ,EAAEA,QAAQ;IAClBE,OAAO,EAAEA,OAAO;IAChBC,cAAc,EAAEA,cAAc;IAC9BQ,oBAAoB,EAAEtB,IAAI,CAACoB,kBAAkB,CAAC,sBAAsB;EACtE,CAAC;AACH;AACA,SAASb,aAAaA,CAACH,SAAS,EAAEH,WAAW,EAAE;EAC7C,IAAIK,UAAU,GAAG,CAAC;EAClB,IAAIiB,MAAM,GAAGnB,SAAS,CAACoB,KAAK,CAACC,SAAS,CAAC,CAAC;EACxC,IAAIxB,WAAW,KAAK,OAAO,EAAE;IAC3BK,UAAU,GAAGiB,MAAM,CAAC,CAAC,CAAC;EACxB,CAAC,MAAM,IAAItB,WAAW,KAAK,KAAK,EAAE;IAChCK,UAAU,GAAGiB,MAAM,CAAC,CAAC,CAAC;EACxB;EACA;EACA;EAAA,KACK,IAAI3B,QAAQ,CAACK,WAAW,CAAC,IAAI,CAACyB,KAAK,CAACzB,WAAW,CAAC,EAAE;IACrDK,UAAU,GAAGL,WAAW;EAC1B;EACA;EAAA,KACK;IACH;IACA,IAAIsB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACjBjB,UAAU,GAAGiB,MAAM,CAAC,CAAC,CAAC;IACxB;IACA;IAAA,KACK,IAAIA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACtBjB,UAAU,GAAGiB,MAAM,CAAC,CAAC,CAAC;IACxB;IACA;EACF;EACA,OAAOjB,UAAU;AACnB;AACA,OAAO,SAASqB,iBAAiBA,CAACC,aAAa,EAAE7B,QAAQ,EAAEC,IAAI,EAAE6B,GAAG,EAAE;EACpE,IAAIC,KAAK,GAAGC,GAAG;EACf,IAAIH,aAAa,CAACV,OAAO,EAAE;IACzBY,KAAK,GAAG9B,IAAI,CAACgC,GAAG,CAAChC,IAAI,CAACoB,kBAAkB,CAAC,sBAAsB,CAAC,EAAES,GAAG,CAAC;EACxE;EACA,IAAIH,KAAK,CAACI,KAAK,CAAC,EAAE;IAChBA,KAAK,GAAGF,aAAa,CAACtB,UAAU;EAClC;EACA,IAAIQ,cAAc,GAAGc,aAAa,CAACd,cAAc;EACjD,IAAImB,WAAW,GAAG,EAAE;EACpBA,WAAW,CAACnB,cAAc,CAAC,GAAGd,IAAI,CAACgC,GAAG,CAACJ,aAAa,CAACf,OAAO,EAAEgB,GAAG,CAAC;EAClEI,WAAW,CAAC,CAAC,GAAGnB,cAAc,CAAC,GAAGgB,KAAK;EACvC,OAAO/B,QAAQ,CAACmC,WAAW,CAACD,WAAW,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}