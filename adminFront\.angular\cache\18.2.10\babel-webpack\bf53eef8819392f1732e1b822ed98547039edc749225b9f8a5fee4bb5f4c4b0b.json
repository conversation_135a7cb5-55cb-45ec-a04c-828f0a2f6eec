{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/@core/service/notice.service\";\nimport * as i8 from \"src/services/File.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i12 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i13 from \"../../../@theme/directives/label.directive\";\nimport * as i14 from \"../../components/file-upload/file-upload.component\";\nimport * as i15 from \"../../../shared/components/household-binding/household-binding.component\";\nconst _c0 = () => [0, 1, 2];\nfunction NoticeManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.label, \" \");\n  }\n}\nfunction NoticeManagementComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(19);\n      return i0.ɵɵresetView(ctx_r3.openModel(dialog_r5));\n    });\n    i0.ɵɵtext(1, \" \\u65B0\\u589E\\u6A94\\u6848 \");\n    i0.ɵɵelement(2, \"i\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_18_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_18_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r5 = i0.ɵɵreference(19);\n      return i0.ɵɵresetView(ctx_r3.openModel(dialog_r5, item_r8));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_18_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_18_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onDelete(item_r8));\n    });\n    i0.ɵɵtext(1, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 24)(1, \"td\")(2, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_18_Template_a_click_2_listener() {\n      const item_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openPdfInNewTab(item_r8));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtemplate(12, NoticeManagementComponent_ng_container_16_tr_18_button_12_Template, 2, 0, \"button\", 26)(13, NoticeManagementComponent_ng_container_16_tr_18_button_13_Template, 2, 0, \"button\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r8 == null ? null : item_r8.CFileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CHouseHold == null ? null : item_r8.CHouseHold.join(\"\\u3001\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CExamineStatus != null && i0.ɵɵpureFunction0(9, _c0).includes(item_r8.CExamineStatus) ? ctx_r3.cExamineStatusOption[item_r8.CExamineStatus] : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 6, item_r8 == null ? null : item_r8.CApproveDate, \"yyyy/MM/dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isDelete);\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_38_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_38_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r5 = i0.ɵɵreference(19);\n      return i0.ɵɵresetView(ctx_r3.openModel(dialog_r5, item_r12));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_38_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_38_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onDelete(item_r12));\n    });\n    i0.ɵɵtext(1, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 24)(1, \"td\")(2, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_38_Template_a_click_2_listener() {\n      const item_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openPdfInNewTab(item_r12));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtemplate(12, NoticeManagementComponent_ng_container_16_tr_38_button_12_Template, 2, 0, \"button\", 26)(13, NoticeManagementComponent_ng_container_16_tr_38_button_13_Template, 2, 0, \"button\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r12 == null ? null : item_r12.CFileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r12.CHouseHold == null ? null : item_r12.CHouseHold.join(\"\\u3001\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r12.CExamineStatus != null && i0.ɵɵpureFunction0(9, _c0).includes(item_r12.CExamineStatus) ? ctx_r3.cExamineStatusOption[item_r12.CExamineStatus] : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 6, item_r12 == null ? null : item_r12.CApproveDate, \"yyyy-MM-dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isDelete);\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"h4\", 17);\n    i0.ɵɵtext(3, \"\\u5730\\u4E3B\\u6236 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"table\", 18)(5, \"thead\")(6, \"tr\", 19)(7, \"th\", 20);\n    i0.ɵɵtext(8, \"\\u6A94\\u6848\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 20);\n    i0.ɵɵtext(10, \"\\u9069\\u7528\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 20);\n    i0.ɵɵtext(12, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 20);\n    i0.ɵɵtext(14, \"\\u5BE9\\u6838\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 20);\n    i0.ɵɵtext(16, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"tbody\");\n    i0.ɵɵtemplate(18, NoticeManagementComponent_ng_container_16_tr_18_Template, 14, 10, \"tr\", 21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 22)(20, \"ngb-pagination\", 23);\n    i0.ɵɵtwoWayListener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.pageIndex, $event) || (ctx_r3.pageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.pageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 16)(22, \"h4\", 17);\n    i0.ɵɵtext(23, \"\\u92B7\\u552E\\u6236\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"table\", 18)(25, \"thead\")(26, \"tr\", 19)(27, \"th\", 20);\n    i0.ɵɵtext(28, \"\\u6A94\\u6848\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"th\", 20);\n    i0.ɵɵtext(30, \"\\u9069\\u7528\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"th\", 20);\n    i0.ɵɵtext(32, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\", 20);\n    i0.ɵɵtext(34, \"\\u5BE9\\u6838\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"th\", 20);\n    i0.ɵɵtext(36, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"tbody\");\n    i0.ɵɵtemplate(38, NoticeManagementComponent_ng_container_16_tr_38_Template, 14, 10, \"tr\", 21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 22)(40, \"ngb-pagination\", 23);\n    i0.ɵɵtwoWayListener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.pageIndexSales, $event) || (ctx_r3.pageIndexSales = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.pageChangedSales($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listSpecialNoticeFile == null ? null : ctx_r3.listSpecialNoticeFile.ListLandLords);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r3.pageIndex);\n    i0.ɵɵproperty(\"pageSize\", ctx_r3.pageSize)(\"collectionSize\", ctx_r3.totalRecords);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listSpecialNoticeFile == null ? null : ctx_r3.listSpecialNoticeFile.ListSales);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r3.pageIndexSales);\n    i0.ɵɵproperty(\"pageSize\", ctx_r3.pageSizeSales)(\"collectionSize\", ctx_r3.totalRecordsSales);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r16);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r16.label, \" \");\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_div_25_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"dateFormatHour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r18 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, row_r18.CCreateDt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(row_r18.CCreator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getActionName(row_r18.CAction));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r18.CExamineNote);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 41)(2, \"label\", 49);\n    i0.ɵɵtext(3, \"\\u5BE9\\u6838\\u6B77\\u7A0B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"table\", 50)(5, \"thead\")(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"\\u6642\\u9593\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"\\u4F7F\\u7528\\u8005\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"\\u52D5\\u4F5C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"tbody\");\n    i0.ɵɵtemplate(16, NoticeManagementComponent_ng_template_18_div_25_tr_16_Template, 10, 6, \"tr\", 51);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.saveSpecialNoticeFile.tblExamineLogs);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 30)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 31)(4, \"div\", 32)(5, \"label\", 33);\n    i0.ɵɵtext(6, \"\\u6A94\\u6848\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"nb-select\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NoticeManagementComponent_ng_template_18_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.saveSpecialNoticeFile.selectedCNoticeType, $event) || (ctx_r3.saveSpecialNoticeFile.selectedCNoticeType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function NoticeManagementComponent_ng_template_18_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onStatusChange($event));\n    });\n    i0.ɵɵtemplate(8, NoticeManagementComponent_ng_template_18_nb_option_8_Template, 2, 2, \"nb-option\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 35)(10, \"app-file-upload\", 36);\n    i0.ɵɵlistener(\"fileSelected\", function NoticeManagementComponent_ng_template_18_Template_app_file_upload_fileSelected_10_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onFileUpload($event));\n    })(\"fileCleared\", function NoticeManagementComponent_ng_template_18_Template_app_file_upload_fileCleared_10_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onFileClear());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 37)(12, \"label\", 38);\n    i0.ɵɵtext(13, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 39)(15, \"app-household-binding\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NoticeManagementComponent_ng_template_18_Template_app_household_binding_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedHouseholds, $event) || (ctx_r3.selectedHouseholds = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"houseIdChange\", function NoticeManagementComponent_ng_template_18_Template_app_household_binding_houseIdChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onHouseholdIdChange($event));\n    })(\"selectionChange\", function NoticeManagementComponent_ng_template_18_Template_app_household_binding_selectionChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onHouseholdSelectionChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 41)(17, \"label\", 42);\n    i0.ɵɵtext(18, \"\\u9001\\u5BE9\\u5099\\u8A3B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"textarea\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NoticeManagementComponent_ng_template_18_Template_textarea_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.saveSpecialNoticeFile.CExamineNote, $event) || (ctx_r3.saveSpecialNoticeFile.CExamineNote = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 44)(21, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_template_18_Template_button_click_21_listener() {\n      const ref_r17 = i0.ɵɵrestoreView(_r15).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClose(ref_r17));\n    });\n    i0.ɵɵtext(22, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_template_18_Template_button_click_23_listener() {\n      const ref_r17 = i0.ɵɵrestoreView(_r15).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSaveSpecialNoticeFile(ref_r17));\n    });\n    i0.ɵɵtext(24, \" \\u5132\\u5B58\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, NoticeManagementComponent_ng_template_18_div_25_Template, 17, 1, \"div\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.isNew ? \"\\u65B0\\u589E\\u6A94\\u6848-\\u5730\\u4E3B\\u6236\" : \"\\u7DE8\\u8F2F\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.saveSpecialNoticeFile.selectedCNoticeType);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.cNoticeTypeOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"config\", ctx_r3.fileUploadConfig)(\"currentFileName\", (ctx_r3.selectedFile == null ? null : ctx_r3.selectedFile.fileName) || null)(\"currentFileUrl\", ctx_r3.saveSpecialNoticeFile && ctx_r3.saveSpecialNoticeFile.CFileUrl || null)(\"labelMinWidth\", \"75px\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedHouseholds);\n    i0.ɵɵproperty(\"buildCaseId\", ctx_r3.selectedCBuildCase == null ? null : ctx_r3.selectedCBuildCase.value)(\"buildingData\", ctx_r3.buildingData)(\"maxSelections\", 50)(\"disabled\", (ctx_r3.saveSpecialNoticeFile == null ? null : ctx_r3.saveSpecialNoticeFile.CExamineStauts) == 0)(\"allowBatchSelect\", true)(\"useHouseNameMode\", false)(\"preFilterHouseType\", ctx_r3.saveSpecialNoticeFile == null ? null : ctx_r3.saveSpecialNoticeFile.selectedCNoticeType == null ? null : ctx_r3.saveSpecialNoticeFile.selectedCNoticeType.value)(\"reminderText\", ctx_r3.getHouseholdReminderText());\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.saveSpecialNoticeFile.CExamineNote);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isNew);\n  }\n}\nexport class NoticeManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _utilityService, _buildCaseService, _specialNoticeFileService, _regularNoticeFileService, _houseService, _specialChangeCustomService, _fileService, _houseService2) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._utilityService = _utilityService;\n    this._buildCaseService = _buildCaseService;\n    this._specialNoticeFileService = _specialNoticeFileService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._houseService = _houseService;\n    this._specialChangeCustomService = _specialChangeCustomService;\n    this._fileService = _fileService;\n    this._houseService2 = _houseService2;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.pageFirstSales = 1;\n    this.pageSizeSales = 10;\n    this.pageIndexSales = 1;\n    this.totalRecordsSales = 0;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: 1\n    }, {\n      label: '銷售戶',\n      value: 2\n    }];\n    this.typeContentManagementLandowner = {\n      CFormType: 1,\n      CNoticeType: 1\n    };\n    // 新增：新戶別選擇器相關屬性\n    this.buildingData = {}; // 存放建築物戶別資料\n    this.selectedHouseholds = []; // 選中的戶別ID (使用 houseId)\n    this.isBuildingDataLoading = false; // 新增：建築物資料載入狀態\n    // 檔案上傳配置\n    this.fileUploadConfig = {\n      acceptedTypes: ['application/pdf'],\n      acceptedFileRegex: /pdf/i,\n      acceptAttribute: 'application/pdf',\n      label: '上傳檔案',\n      helpText: '*請上傳PDF格式',\n      required: false,\n      disabled: false,\n      autoFillName: false,\n      buttonText: '上傳',\n      buttonIcon: 'fa-solid fa-cloud-arrow-up',\n      maxFileSize: 10,\n      multiple: false,\n      showPreview: false\n    };\n    this.selectedFile = null;\n    this.cExamineStatusOption = ['待審核', '已通過', '已駁回'];\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.getUserBuildCase();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n  }\n  pageChangedSales(newPage) {\n    this.pageIndexSales = newPage;\n  }\n  // 檔案選擇事件處理\n  onFileUpload(fileResult) {\n    this.selectedFile = fileResult;\n  }\n  // 檔案清除事件處理\n  onFileClear() {\n    this.selectedFile = null;\n  }\n  onChangeBuildCase() {\n    if (this.selectedCBuildCase.value) {\n      this.getSpecialNoticeFileHouseHoldList();\n      this.getSpecialNoticeFileList();\n      // 新增：載入建築物戶別資料\n      this.loadBuildingDataFromAPI();\n    }\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            label: res.CBuildCaseName,\n            value: res.cID\n          };\n        });\n        this.selectedCBuildCase = this.userBuildCaseOptions[0];\n        if (this.selectedCBuildCase.value) {\n          this.getSpecialNoticeFileHouseHoldList();\n          this.getSpecialNoticeFileList();\n          // 新增：載入建築物戶別資料\n          this.loadBuildingDataFromAPI();\n        }\n      }\n    })).subscribe();\n  }\n  groupByFloor(customerData) {\n    const groupedData = [];\n    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n    for (const floor of uniqueFloors) {\n      groupedData.push([]);\n    }\n    for (const customer of customerData) {\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor);\n      if (floorIndex !== -1) {\n        groupedData[floorIndex].push({\n          CIsSelect: customer?.CIsSelect || false,\n          CHouseID: customer.CID,\n          CHouseType: customer.CHouseType,\n          CFloor: customer.CFloor,\n          CHouseHold: customer.CHouseHold,\n          CIsEnable: customer.CIsEnable\n        });\n      }\n    }\n    return groupedData;\n  }\n  getSpecialNoticeFileList() {\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedCBuildCase.value,\n        PageIndexLandLord: this.pageIndex,\n        PageIndexSales: this.pageIndexSales,\n        PageSizeLandLord: this.pageSize,\n        PageSizeSales: this.pageSizeSales\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialNoticeFile = res.Entries;\n        this.totalRecords = res.Entries.TotalListLandLords || 0;\n        this.totalRecordsSales = res.Entries.TotalListSales || 0;\n      }\n    });\n  }\n  onStatusChange(newStatus) {\n    // 觸發建築物資料重新篩選\n    this.onNoticeTypeChange();\n  }\n  getSpecialNoticeFileHouseHoldList() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.selectedCBuildCase.value\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialNoticeFileHouseHold = res.Entries;\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  updateCIsEnable(houseList2D, houseSpecialNoticeFile) {\n    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));\n    return houseList2D.map(floorArray => {\n      return floorArray.map(item => {\n        const key = `${item.CHouseHold}-${item.CFloor}`;\n        if (selectedHouses.has(key)) {\n          item.CIsSelect = true;\n        } else {\n          item.CIsSelect = false;\n        }\n        return item;\n      });\n    });\n  }\n  addCIsSelectToA(A, B) {\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\n    return A.map(item => {\n      const key = `${item.CHouseHold}-${item.CFloor}`;\n      return {\n        ...item,\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\n      };\n    });\n  }\n  getSpecialNoticeFileById(item, ref) {\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({\n      body: item.CSpecialNoticeFileId\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        const data = res.Entries;\n        this.saveSpecialNoticeFile = {\n          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,\n          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,\n          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,\n          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,\n          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],\n          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,\n          CExamineNote: data.CExamineNote,\n          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter(i => i.CIsSelect),\n          CExamineStauts: data.CExamineStauts\n        };\n        // 新增：初始化選中的戶別ID\n        this.selectedHouseholds = this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses?.map(house => house.CHouseID) || [];\n        // 新增：根據載入的通知類型重新篩選建築物資料\n        this.loadBuildingDataFromAPI();\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  openPdfInNewTab(file) {\n    // 檢查檔案資訊是否存在\n    if (!file.CFile || !file.CFileName) {\n      this.message.showErrorMSG('檔案資訊不完整');\n      return;\n    }\n    // 使用 FileService 下載檔案\n    this._fileService.getFile(file.CFile, file.CFileName).subscribe({\n      next: blob => {\n        // 建立 Blob URL\n        const blobUrl = URL.createObjectURL(blob);\n        // 在新頁籤開啟 PDF\n        const newWindow = window.open(blobUrl, '_blank');\n        if (!newWindow) {\n          this.message.showErrorMSG('無法開啟新視窗，請檢查瀏覽器設定');\n          URL.revokeObjectURL(blobUrl); // 清理 URL\n          return;\n        }\n        // 當新視窗關閉時清理 Blob URL\n        newWindow.addEventListener('beforeunload', () => {\n          URL.revokeObjectURL(blobUrl);\n        });\n      },\n      error: error => {\n        console.error('下載檔案失敗:', error);\n        this.message.showErrorMSG('檔案下載失敗，請稍後再試');\n      }\n    });\n  }\n  openModel(ref, item) {\n    this.isNew = true;\n    this.onFileClear(); // 替換 clearImage()\n    this.selectedHouseholds = []; // 新增：清空選中的戶別\n    this.saveSpecialNoticeFile = {\n      CNoticeType: 1,\n      CBuildCaseId: undefined,\n      CFile: undefined,\n      CHouse: [],\n      CSpecialNoticeFileId: undefined,\n      CIsSelectAll: false,\n      selectedCNoticeType: this.cNoticeTypeOptions[0],\n      CExamineNote: '',\n      tblSpecialNoticeFileHouses: undefined\n    };\n    if (item) {\n      this.isNew = false;\n      this.getSpecialNoticeFileById(item, ref);\n    } else {\n      this.isNew = true;\n      this.dialogService.open(ref);\n      // 新增：在對話框開啟後載入建築物資料（確保預設通知類型已設置）\n      setTimeout(() => {\n        this.loadBuildingDataFromAPI();\n      }, 0);\n    }\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  onDelete(item) {\n    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {\n      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({\n        body: item.CSpecialNoticeFileId\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.onChangeBuildCase();\n        }\n      });\n    }\n    return item;\n  }\n  getTrueKeys(inputDict) {\n    const trueKeys = [];\n    for (const key in inputDict) {\n      if (inputDict[key]) {\n        trueKeys.push(key);\n      }\n    }\n    return trueKeys;\n  }\n  flattenAndFilter(data) {\n    const flattened = [];\n    for (const floorData of data) {\n      for (const house of floorData) {\n        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {\n          flattened.push({\n            CHouseID: house.CHouseID,\n            CIsSelect: house.CIsSelect\n          });\n        }\n      }\n    }\n    return flattened;\n  }\n  // 新增：從新戶別選擇器的選擇結果轉換為原有格式\n  convertSelectedHouseholdsToHouseSpecialNoticeFile() {\n    const result = [];\n    // 使用 buildingData 和 selectedHouseholds 來產生結果\n    if (this.selectedHouseholds && this.selectedHouseholds.length > 0 && this.buildingData) {\n      // 從 buildingData 中找到對應的戶別資訊\n      Object.values(this.buildingData).forEach(buildings => {\n        buildings.forEach(house => {\n          if (house.houseId && this.selectedHouseholds.includes(house.houseId) && !house.isDisabled) {\n            result.push({\n              CHouseID: house.houseId,\n              CIsSelect: true\n            });\n          }\n        });\n      });\n    }\n    return result;\n  }\n  onSaveSpecialNoticeFile(ref) {\n    const selectedHouses = this.convertSelectedHouseholdsToHouseSpecialNoticeFile();\n    // 除錯資訊\n    console.log('選中的戶別ID:', this.selectedHouseholds);\n    console.log('轉換後的戶別資料:', selectedHouses);\n    console.log('建築物資料 keys:', Object.keys(this.buildingData));\n    console.log('建築物資料總戶數:', Object.values(this.buildingData).reduce((total, houses) => total + houses.length, 0));\n    console.log('通知類型:', this.saveSpecialNoticeFile.selectedCNoticeType);\n    const param = {\n      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,\n      CBuildCaseId: this.selectedCBuildCase.value,\n      CFile: this.selectedFile ? this.selectedFile.CFileUpload : undefined,\n      CHouse: selectedHouses,\n      // 使用新的轉換方法\n      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,\n      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,\n      CExamineNote: this.saveSpecialNoticeFile.CExamineNote\n    };\n    this.validation(param.CHouse);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {\n      if (res && res.body && res.body.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.onFileClear(); // 替換 clearImage()\n        this.getSpecialNoticeFileList();\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res && res.body && res.body.Message);\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation(CHouse) {\n    this.valid.clear();\n    if (this.isNew && !this.selectedFile) {\n      this.valid.required('[檔案]', '');\n    }\n    if (!(CHouse.length > 0)) {\n      this.valid.required('[適用戶別]', '');\n    }\n    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote);\n  }\n  getActionName(actionID) {\n    let textR = \"\";\n    if (actionID != undefined) {\n      switch (actionID) {\n        case 1:\n          textR = \"傳送\";\n          break;\n        case 2:\n          textR = \"通過\";\n          break;\n        case 3:\n          textR = \"駁回\";\n          break;\n        default:\n          break;\n      }\n    }\n    return textR;\n  }\n  updateCIsClick(houseList2D, houseSpecialNoticeFile) {\n    const selectedHouses = houseSpecialNoticeFile.map(item => item.CHouseID);\n    return houseList2D.map(floorArray => {\n      return floorArray.map(item => {\n        if (selectedHouses.includes(item.CHouseID)) {\n          item.CIsSelect = true;\n        } else {\n          item.CIsSelect = false;\n        }\n        return item;\n      });\n    });\n  } // 新增：載入建築物戶別資料 (使用 GetDropDown API)\n  loadBuildingDataFromAPI() {\n    if (!this.selectedCBuildCase?.value) return;\n    this.isBuildingDataLoading = true;\n    this.buildingData = {}; // 清空舊資料\n    this._houseService2.apiHouseGetDropDownPost$Json({\n      buildCaseId: this.selectedCBuildCase.value\n    }).subscribe({\n      next: response => {\n        this.isBuildingDataLoading = false;\n        if (response.Entries) {\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        }\n      },\n      error: error => {\n        this.isBuildingDataLoading = false;\n        console.error('Error loading building data from API:', error);\n        // 清空建築物資料，避免使用過時的資料\n        this.buildingData = {};\n      }\n    });\n  } // 新增：將 API 回應轉換為建築物資料格式\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      // 不在父元件中預先篩選，將所有戶別資料傳給子元件\n      // 篩選邏輯由戶別綁定元件根據 preFilterHouseType 參數處理\n      buildingData[building] = houses.map(house => ({\n        houseName: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseType: house.HouseType,\n        // 保留戶別類型資訊供子元件篩選使用\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  // 新增：將現有的 houseList2D 轉換為新戶別選擇器的格式\n  convertHouseList2DToBuildingData() {\n    const buildingData = {};\n    if (!this.houseList2D || this.houseList2D.length === 0) {\n      return buildingData;\n    }\n    this.houseList2D.forEach(row => {\n      row.forEach(house => {\n        if (!house.CHouseHold) return;\n        // 不在父元件中預先篩選，將所有戶別資料傳給子元件\n        // 篩選邏輯由戶別綁定元件根據 preFilterHouseType 參數處理\n        // 嘗試從戶別名稱中提取建築物代碼（假設格式為 A001, B002 等）\n        const buildingMatch = house.CHouseHold.match(/^([A-Z]+)/);\n        const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\n        if (!buildingData[building]) {\n          buildingData[building] = [];\n        }\n        buildingData[building].push({\n          houseName: house.CHouseHold,\n          building: building,\n          floor: house.CFloor ? `${house.CFloor}F` : undefined,\n          houseId: house.CHouseID,\n          houseType: house.CHouseType || undefined,\n          // 新增：戶別類型\n          isSelected: house.CIsSelect || false,\n          isDisabled: !house.CIsEnable || !(this.saveSpecialNoticeFile?.selectedCNoticeType?.value === house.CHouseType) || this.saveSpecialNoticeFile?.CExamineStauts === 0\n        });\n      });\n    });\n    return buildingData;\n  } // 新增：處理戶別選擇變更\n  onHouseholdSelectionChange(selectedItems) {\n    // 更新 selectedHouseholds (使用 houseId)\n    this.selectedHouseholds = selectedItems.map(item => item.houseId).filter(id => id !== undefined);\n  }\n  // 新增：處理戶別ID變更事件\n  onHouseholdIdChange(selectedIds) {\n    this.selectedHouseholds = selectedIds;\n  }\n  // 新增：處理通知類型變更\n  onNoticeTypeChange() {\n    // 清空當前選擇的戶別\n    this.selectedHouseholds = [];\n    // 不需要重新載入資料，因為篩選邏輯已移至戶別綁定元件內部\n    // 戶別綁定元件會根據 preFilterHouseType 參數自動篩選顯示的戶別\n  }\n  // 新增：取得戶別選擇的提醒文案\n  getHouseholdReminderText() {\n    if (!this.saveSpecialNoticeFile?.selectedCNoticeType) {\n      return '請先選擇檔案類型，系統將根據類型篩選對應的戶別';\n    }\n    const noticeType = this.saveSpecialNoticeFile.selectedCNoticeType;\n    if (noticeType.value === 1) {\n      return '目前篩選顯示：地主戶，只會顯示地主戶相關的戶別選項';\n    } else if (noticeType.value === 2) {\n      return '目前篩選顯示：銷售戶，只會顯示銷售戶相關的戶別選項';\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function NoticeManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NoticeManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.SpecialNoticeFileService), i0.ɵɵdirectiveInject(i6.RegularNoticeFileService), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i7.NoticeServiceCustom), i0.ɵɵdirectiveInject(i8.FileService), i0.ɵɵdirectiveInject(i6.HouseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NoticeManagementComponent,\n      selectors: [[\"ngx-notice-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 20,\n      vars: 4,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"value\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"table-responsive\", \"mt-4\"], [1, \"text-xl\", \"font-bold\"], [1, \"table\", \"table-striped\", \"border\", \"mt-3\", 2, \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"justify-center\", \"my-3\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"text-center\"], [1, \"cursor-pointer\", \"text-blue-500\", 3, \"click\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"Select Status\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"align-items-baseline\"], [3, \"fileSelected\", \"fileCleared\", \"config\", \"currentFileName\", \"currentFileUrl\", \"labelMinWidth\"], [1, \"form-group\", \"d-flex\", \"mb-0\"], [\"for\", \"householdBinding\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-3\", 2, \"min-width\", \"75px\"], [1, \"mt-1\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u9069\\u7528\\u6236\\u5225\", 3, \"ngModelChange\", \"houseIdChange\", \"selectionChange\", \"ngModel\", \"buildCaseId\", \"buildingData\", \"maxSelections\", \"disabled\", \"allowBatchSelect\", \"useHouseNameMode\", \"preFilterHouseType\", \"reminderText\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-3\", 2, \"min-width\", \"75px\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [1, \"d-flex\", \"justify-content-center\", \"min-w-[90px]\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", \"min-w-[90px]\", 3, \"click\"], [\"class\", \"w-full\", 4, \"ngIf\"], [1, \"w-full\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"mr-3\", 2, \"min-width\", \"75px\"], [1, \"table\", \"table-bordered\", 2, \"background-color\", \"#f3f3f3\"], [4, \"ngFor\", \"ngForOf\"]],\n      template: function NoticeManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 2);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 5)(9, \"label\", 6);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NoticeManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCBuildCase, $event) || (ctx.selectedCBuildCase = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function NoticeManagementComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onChangeBuildCase());\n          });\n          i0.ɵɵtemplate(12, NoticeManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 4)(14, \"div\", 9);\n          i0.ɵɵtemplate(15, NoticeManagementComponent_button_15_Template, 3, 0, \"button\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(16, NoticeManagementComponent_ng_container_16_Template, 41, 8, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"nb-card-footer\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, NoticeManagementComponent_ng_template_18_Template, 26, 20, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCBuildCase);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listSpecialNoticeFile);\n        }\n      },\n      dependencies: [CommonModule, i9.NgForOf, i9.NgIf, i9.DatePipe, SharedModule, i10.DefaultValueAccessor, i10.NgControlStatus, i10.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i11.NgbPagination, i12.BreadcrumbComponent, i13.BaseLabelDirective, i14.FileUploadComponent, AppSharedModule, i15.HouseholdBindingComponent, DateFormatHourPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJub3RpY2UtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29uc3RydWN0aW9uLXByb2plY3QtbWFuYWdlbWVudC9ub3RpY2UtbWFuYWdlbWVudC9ub3RpY2UtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ0xBQWdMIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "SharedModule", "AppSharedModule", "BaseComponent", "DateFormatHourPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵlistener", "NoticeManagementComponent_button_15_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "dialog_r5", "ɵɵreference", "ɵɵresetView", "openModel", "ɵɵelement", "NoticeManagementComponent_ng_container_16_tr_18_button_12_Template_button_click_0_listener", "_r9", "item_r8", "$implicit", "NoticeManagementComponent_ng_container_16_tr_18_button_13_Template_button_click_0_listener", "_r10", "onDelete", "NoticeManagementComponent_ng_container_16_tr_18_Template_a_click_2_listener", "_r7", "openPdfInNewTab", "ɵɵtemplate", "NoticeManagementComponent_ng_container_16_tr_18_button_12_Template", "NoticeManagementComponent_ng_container_16_tr_18_button_13_Template", "ɵɵtextInterpolate", "CFileName", "CHouseHold", "join", "CExamineStatus", "ɵɵpureFunction0", "_c0", "includes", "cExamineStatusOption", "ɵɵpipeBind2", "CApproveDate", "isUpdate", "isDelete", "NoticeManagementComponent_ng_container_16_tr_38_button_12_Template_button_click_0_listener", "_r13", "item_r12", "NoticeManagementComponent_ng_container_16_tr_38_button_13_Template_button_click_0_listener", "_r14", "NoticeManagementComponent_ng_container_16_tr_38_Template_a_click_2_listener", "_r11", "NoticeManagementComponent_ng_container_16_tr_38_button_12_Template", "NoticeManagementComponent_ng_container_16_tr_38_button_13_Template", "ɵɵelementContainerStart", "NoticeManagementComponent_ng_container_16_tr_18_Template", "ɵɵtwoWayListener", "NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_20_listener", "$event", "_r6", "ɵɵtwoWayBindingSet", "pageIndex", "pageChanged", "NoticeManagementComponent_ng_container_16_tr_38_Template", "NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_40_listener", "pageIndexSales", "pageChangedSales", "listSpecialNoticeFile", "ListLandLords", "ɵɵtwoWayProperty", "pageSize", "totalRecords", "ListSales", "pageSizeSales", "totalRecordsSales", "status_r16", "ɵɵpipeBind1", "row_r18", "CCreateDt", "CCreator", "getActionName", "CAction", "CExamineNote", "NoticeManagementComponent_ng_template_18_div_25_tr_16_Template", "saveSpecialNoticeFile", "tblExamineLogs", "NoticeManagementComponent_ng_template_18_Template_nb_select_ngModelChange_7_listener", "_r15", "selectedCNoticeType", "onStatusChange", "NoticeManagementComponent_ng_template_18_nb_option_8_Template", "NoticeManagementComponent_ng_template_18_Template_app_file_upload_fileSelected_10_listener", "onFileUpload", "NoticeManagementComponent_ng_template_18_Template_app_file_upload_fileCleared_10_listener", "onFileClear", "NoticeManagementComponent_ng_template_18_Template_app_household_binding_ngModelChange_15_listener", "selectedHouseholds", "NoticeManagementComponent_ng_template_18_Template_app_household_binding_houseIdChange_15_listener", "onHouseholdIdChange", "NoticeManagementComponent_ng_template_18_Template_app_household_binding_selectionChange_15_listener", "onHouseholdSelectionChange", "NoticeManagementComponent_ng_template_18_Template_textarea_ngModelChange_19_listener", "NoticeManagementComponent_ng_template_18_Template_button_click_21_listener", "ref_r17", "dialogRef", "onClose", "NoticeManagementComponent_ng_template_18_Template_button_click_23_listener", "onSaveSpecialNoticeFile", "NoticeManagementComponent_ng_template_18_div_25_Template", "isNew", "cNoticeTypeOptions", "fileUploadConfig", "selectedFile", "fileName", "CFileUrl", "selectedCBuildCase", "value", "buildingData", "CExamineStauts", "getHouseholdReminderText", "NoticeManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_utilityService", "_buildCaseService", "_specialNoticeFileService", "_regularNoticeFileService", "_houseService", "_specialChangeCustomService", "_fileService", "_houseService2", "pageFirst", "pageFirstSales", "buildCaseOptions", "typeContentManagementLandowner", "CFormType", "CNoticeType", "isBuildingDataLoading", "acceptedTypes", "acceptedFileRegex", "acceptAttribute", "helpText", "required", "disabled", "autoFillName", "buttonText", "buttonIcon", "maxFileSize", "multiple", "showPreview", "ngOnInit", "getUserBuildCase", "newPage", "fileResult", "onChangeBuildCase", "getSpecialNoticeFileHouseHoldList", "getSpecialNoticeFileList", "loadBuildingDataFromAPI", "apiBuildCaseGetUserBuildCasePost$Json", "body", "pipe", "res", "Entries", "StatusCode", "userBuildCaseOptions", "map", "CBuildCaseName", "cID", "subscribe", "groupByFloor", "customerData", "groupedData", "uniqueFloors", "Array", "from", "Set", "customer", "CFloor", "filter", "floor", "push", "floorIndex", "indexOf", "CIsSelect", "CHouseID", "CID", "CHouseType", "CIsEnable", "apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json", "CBuildCaseId", "PageIndexLandLord", "PageIndexSales", "PageSizeLandLord", "PageSizeSales", "TotalListLandLords", "TotalListSales", "newStatus", "onNoticeTypeChange", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "listSpecialNoticeFileHouseHold", "createArrayObjectFromArray", "a", "b", "c", "item", "matchingItem", "find", "bItem", "CHousehold", "CIsSelected", "getItemByValue", "options", "updateCIsEnable", "houseList2D", "houseSpecialNoticeFile", "selectedHouses", "floorArray", "key", "has", "addCIsSelectToA", "A", "B", "mapB", "Map", "get", "getSpecialNoticeFileById", "ref", "apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json", "CSpecialNoticeFileId", "data", "tblSpecialNoticeFile", "undefined", "tblSpecialNoticeFileHouses", "i", "house", "open", "file", "CFile", "showErrorMSG", "getFile", "next", "blob", "blobUrl", "URL", "createObjectURL", "newWindow", "window", "revokeObjectURL", "addEventListener", "error", "console", "CHouse", "CIsSelectAll", "setTimeout", "removeBase64Prefix", "base64String", "prefixIndex", "substring", "confirm", "apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json", "showSucessMSG", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputDict", "true<PERSON>eys", "flattenAndFilter", "flattened", "floorData", "convertSelectedHouseholdsToHouseSpecialNoticeFile", "result", "length", "Object", "values", "for<PERSON>ach", "buildings", "houseId", "isDisabled", "log", "keys", "reduce", "total", "houses", "param", "CFileUpload", "validation", "errorMessages", "showErrorMSGs", "SaveSpecialNoticeFile", "close", "Message", "clear", "actionID", "textR", "updateCIsClick", "apiHouseGetDropDownPost$Json", "buildCaseId", "response", "convertApiResponseToBuildingData", "entries", "building", "houseName", "HouseName", "Building", "Floor", "HouseId", "houseType", "HouseType", "isSelected", "convertHouseList2DToBuildingData", "row", "buildingMatch", "match", "selectedItems", "id", "selectedIds", "noticeType", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "UtilityService", "i6", "BuildCaseService", "SpecialNoticeFileService", "RegularNoticeFileService", "HouseService", "i7", "NoticeServiceCustom", "i8", "FileService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NoticeManagementComponent_Template", "rf", "ctx", "NoticeManagementComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "NoticeManagementComponent_Template_nb_select_selected<PERSON>hange_11_listener", "NoticeManagementComponent_nb_option_12_Template", "NoticeManagementComponent_button_15_Template", "NoticeManagementComponent_ng_container_16_Template", "NoticeManagementComponent_ng_template_18_Template", "ɵɵtemplateRefExtractor", "isCreate", "i9", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i10", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "NgbPagination", "i12", "BreadcrumbComponent", "i13", "BaseLabelDirective", "i14", "FileUploadComponent", "i15", "HouseholdBindingComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\notice-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\notice-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { CommonModule, DatePipe } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseService, RegularNoticeFileService, SpecialNoticeFileService } from 'src/services/api/services';\r\nimport { FileService } from 'src/services/File.service';\r\nimport { LabelInOptionsPipe, TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetListHouseHoldRes, GetSpecialNoticeFileListRes, HouseSpecialNoticeFile, SpecialNoticeFileList, TblExamineLog } from 'src/services/api/models';\r\nimport { tap } from 'rxjs';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { NoticeServiceCustom } from 'src/app/@core/service/notice.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\r\nimport { FileUploadComponent, FileUploadConfig, FileUploadResult } from '../../components/file-upload/file-upload.component';\r\nimport { HouseholdItem, BuildingData } from 'src/app/shared/components/household-binding/household-binding.component';\r\n\r\nexport interface HouseList {\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseID?: number;\r\n  CID?: number;\r\n  CIsSelect?: boolean | null;\r\n  CHouseType?: number | null;\r\n  CIsEnable?: boolean | null;\r\n}\r\nexport interface SaveSpecialNoticeFileCus {\r\n  CFileUrl?: string\r\n  CNoticeType?: number\r\n  CBuildCaseId?: number\r\n  CFile?: Blob\r\n  CHouse?: Array<string>\r\n  CSpecialNoticeFileId?: number\r\n  CIsSelectAll?: boolean\r\n  selectedCNoticeType?: any\r\n  CExamineNote?: string | null;\r\n  tblExamineLogs?: TblExamineLog[],\r\n  tblSpecialNoticeFileHouses: any\r\n  CExamineStauts?: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-notice-management',\r\n  templateUrl: './notice-management.component.html',\r\n  styleUrls: ['./notice-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, AppSharedModule, DatePipe, DateFormatHourPipe, FileUploadComponent],\r\n})\r\n\r\nexport class NoticeManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _utilityService: UtilityService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _specialNoticeFileService: SpecialNoticeFileService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _houseService: HouseService,\r\n    private _specialChangeCustomService: NoticeServiceCustom,\r\n    private _fileService: FileService,\r\n    private _houseService2: HouseService\r\n  ) { super(_allow) }\r\n\r\n  override ngOnInit(): void {\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  selectedCBuildCase: any\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n  pageFirstSales = 1;\r\n  pageSizeSales = 10;\r\n  pageIndexSales = 1;\r\n  totalRecordsSales = 0;\r\n\r\n\r\n  saveSpecialNoticeFile: SaveSpecialNoticeFileCus\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  cNoticeTypeOptions: any[] = [\r\n    { label: '地主戶', value: 1 },\r\n    { label: '銷售戶', value: 2 }\r\n  ]\r\n  seletectedNoticeType: any\r\n\r\n  typeContentManagementLandowner = {\r\n    CFormType: 1,\r\n    CNoticeType: 1\r\n  }\r\n  houseList2D: HouseList[][]\r\n  userBuildCaseOptions: any\r\n  // 新增：新戶別選擇器相關屬性\r\n  buildingData: BuildingData = {} // 存放建築物戶別資料\r\n  selectedHouseholds: number[] = [] // 選中的戶別ID (使用 houseId)\r\n  isBuildingDataLoading: boolean = false // 新增：建築物資料載入狀態\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n  }\r\n  pageChangedSales(newPage: number) {\r\n    this.pageIndexSales = newPage;\r\n  }\r\n\r\n  // 檔案上傳配置\r\n  fileUploadConfig: FileUploadConfig = {\r\n    acceptedTypes: ['application/pdf'],\r\n    acceptedFileRegex: /pdf/i,\r\n    acceptAttribute: 'application/pdf',\r\n    label: '上傳檔案',\r\n    helpText: '*請上傳PDF格式',\r\n    required: false,\r\n    disabled: false,\r\n    autoFillName: false,\r\n    buttonText: '上傳',\r\n    buttonIcon: 'fa-solid fa-cloud-arrow-up',\r\n    maxFileSize: 10,\r\n    multiple: false,\r\n    showPreview: false\r\n  };\r\n\r\n  selectedFile: FileUploadResult | null = null;\r\n\r\n  // 檔案選擇事件處理\r\n  onFileUpload(fileResult: FileUploadResult) {\r\n    this.selectedFile = fileResult;\r\n  }\r\n  // 檔案清除事件處理\r\n  onFileClear() {\r\n    this.selectedFile = null;\r\n  }\r\n\r\n  userBuildCaseSelected: any\r\n  onChangeBuildCase() {\r\n    if (this.selectedCBuildCase.value) {\r\n      this.getSpecialNoticeFileHouseHoldList()\r\n      this.getSpecialNoticeFileList()\r\n      // 新增：載入建築物戶別資料\r\n      this.loadBuildingDataFromAPI()\r\n    }\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              label: res.CBuildCaseName,\r\n              value: res.cID\r\n            }\r\n          })\r\n          this.selectedCBuildCase = this.userBuildCaseOptions[0]\r\n          if (this.selectedCBuildCase.value) {\r\n            this.getSpecialNoticeFileHouseHoldList()\r\n            this.getSpecialNoticeFileList()\r\n            // 新增：載入建築物戶別資料\r\n            this.loadBuildingDataFromAPI()\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe()\r\n  }\r\n\r\n  groupByFloor(customerData: HouseList[]): HouseList[][] {\r\n\r\n    const groupedData: HouseList[][] = [];\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number);\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push({\r\n          CIsSelect: customer?.CIsSelect || false,\r\n          CHouseID: customer.CID,\r\n          CHouseType: customer.CHouseType,\r\n          CFloor: customer.CFloor,\r\n          CHouseHold: customer.CHouseHold,\r\n          CIsEnable: customer.CIsEnable,\r\n        });\r\n      }\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n  listSpecialNoticeFile: GetSpecialNoticeFileListRes\r\n\r\n  getSpecialNoticeFileList() {\r\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedCBuildCase.value,\r\n        PageIndexLandLord: this.pageIndex,\r\n        PageIndexSales: this.pageIndexSales,\r\n        PageSizeLandLord: this.pageSize,\r\n        PageSizeSales: this.pageSizeSales,\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialNoticeFile = res.Entries\r\n        this.totalRecords = res.Entries.TotalListLandLords || 0\r\n        this.totalRecordsSales = res.Entries.TotalListSales || 0\r\n      }\r\n    })\r\n  }\r\n  listSpecialNoticeFileHouseHold: GetListHouseHoldRes[]\r\n  houseHoldList: string[];\r\n\r\n  onStatusChange(newStatus: any) {\r\n    // 觸發建築物資料重新篩選\r\n    this.onNoticeTypeChange();\r\n  }\r\n\r\n  getSpecialNoticeFileHouseHoldList() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.selectedCBuildCase.value\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialNoticeFileHouseHold = res.Entries\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CSpecialNoticeFileHouseholdId: number, CSpecialNoticeFileId: number, CHousehold: string, CIsSelected: boolean }[] | any[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  cExamineStatusOption = ['待審核', '已通過', '已駁回']\r\n\r\n  updateCIsEnable(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {\r\n    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));\r\n    return houseList2D.map(floorArray => {\r\n      return floorArray.map(item => {\r\n        const key = `${item.CHouseHold}-${item.CFloor}`;\r\n        if (selectedHouses.has(key)) {\r\n          item.CIsSelect = true;\r\n        } else {\r\n          item.CIsSelect = false;\r\n        }\r\n        return item;\r\n      });\r\n    });\r\n  }\r\n\r\n  addCIsSelectToA(A: any[], B: any[]): any[] {\r\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\r\n    return A.map(item => {\r\n      const key = `${item.CHouseHold}-${item.CFloor}`;\r\n      return {\r\n        ...item,\r\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\r\n      };\r\n    });\r\n  }\r\n\r\n  getSpecialNoticeFileById(item: any, ref: any) {\r\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({\r\n      body: item.CSpecialNoticeFileId\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        const data = res.Entries\r\n        this.saveSpecialNoticeFile = {\r\n          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,\r\n          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,\r\n          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,\r\n          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,\r\n          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],\r\n          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,\r\n          CExamineNote: data.CExamineNote,\r\n          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter((i: any) => i.CIsSelect),\r\n          CExamineStauts: data.CExamineStauts\r\n        }\r\n        // 新增：初始化選中的戶別ID\r\n        this.selectedHouseholds = this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses?.map((house: any) => house.CHouseID) || []\r\n\r\n        // 新增：根據載入的通知類型重新篩選建築物資料\r\n        this.loadBuildingDataFromAPI()\r\n\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  isNew = true;\r\n\r\n  openPdfInNewTab(file: SpecialNoticeFileList) {\r\n    // 檢查檔案資訊是否存在\r\n    if (!file.CFile || !file.CFileName) {\r\n      this.message.showErrorMSG('檔案資訊不完整');\r\n      return;\r\n    }\r\n\r\n    // 使用 FileService 下載檔案\r\n    this._fileService.getFile(file.CFile, file.CFileName).subscribe({\r\n      next: (blob: Blob) => {\r\n        // 建立 Blob URL\r\n        const blobUrl = URL.createObjectURL(blob);\r\n\r\n        // 在新頁籤開啟 PDF\r\n        const newWindow = window.open(blobUrl, '_blank');\r\n\r\n        if (!newWindow) {\r\n          this.message.showErrorMSG('無法開啟新視窗，請檢查瀏覽器設定');\r\n          URL.revokeObjectURL(blobUrl); // 清理 URL\r\n          return;\r\n        }\r\n\r\n        // 當新視窗關閉時清理 Blob URL\r\n        newWindow.addEventListener('beforeunload', () => {\r\n          URL.revokeObjectURL(blobUrl);\r\n        });\r\n      },\r\n      error: (error) => {\r\n        console.error('下載檔案失敗:', error);\r\n        this.message.showErrorMSG('檔案下載失敗，請稍後再試');\r\n      }\r\n    });\r\n  } openModel(ref: any, item?: any) {\r\n    this.isNew = true\r\n    this.onFileClear() // 替換 clearImage()\r\n    this.selectedHouseholds = [] // 新增：清空選中的戶別\r\n    this.saveSpecialNoticeFile = {\r\n      CNoticeType: 1,\r\n      CBuildCaseId: undefined,\r\n      CFile: undefined,\r\n      CHouse: [],\r\n      CSpecialNoticeFileId: undefined,\r\n      CIsSelectAll: false,\r\n      selectedCNoticeType: this.cNoticeTypeOptions[0],\r\n      CExamineNote: '',\r\n      tblSpecialNoticeFileHouses: undefined\r\n    }\r\n\r\n    if (item) {\r\n      this.isNew = false\r\n      this.getSpecialNoticeFileById(item, ref)\r\n    } else {\r\n      this.isNew = true\r\n      this.dialogService.open(ref)\r\n      // 新增：在對話框開啟後載入建築物資料（確保預設通知類型已設置）\r\n      setTimeout(() => {\r\n        this.loadBuildingDataFromAPI()\r\n      }, 0);\r\n    }\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  onDelete(item: any) {\r\n    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {\r\n      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({\r\n        body: item.CSpecialNoticeFileId\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.onChangeBuildCase()\r\n        }\r\n      })\r\n    }\r\n    return item\r\n  }\r\n\r\n  getTrueKeys(inputDict: { [key: string]: boolean }): string[] {\r\n    const trueKeys: string[] = [];\r\n    for (const key in inputDict) {\r\n      if (inputDict[key]) {\r\n        trueKeys.push(key);\r\n      }\r\n    }\r\n    return trueKeys;\r\n  }\r\n  flattenAndFilter(data: any[][]): HouseSpecialNoticeFile[] {\r\n    const flattened: HouseSpecialNoticeFile[] = [];\r\n    for (const floorData of data) {\r\n      for (const house of floorData) {\r\n        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {\r\n          flattened.push({\r\n            CHouseID: house.CHouseID,\r\n            CIsSelect: house.CIsSelect,\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return flattened;\r\n  }\r\n\r\n  // 新增：從新戶別選擇器的選擇結果轉換為原有格式\r\n  convertSelectedHouseholdsToHouseSpecialNoticeFile(): HouseSpecialNoticeFile[] {\r\n    const result: HouseSpecialNoticeFile[] = [];\r\n\r\n    // 使用 buildingData 和 selectedHouseholds 來產生結果\r\n    if (this.selectedHouseholds && this.selectedHouseholds.length > 0 && this.buildingData) {\r\n      // 從 buildingData 中找到對應的戶別資訊\r\n      Object.values(this.buildingData).forEach(buildings => {\r\n        buildings.forEach(house => {\r\n          if (house.houseId &&\r\n            this.selectedHouseholds.includes(house.houseId) &&\r\n            !house.isDisabled) {\r\n            result.push({\r\n              CHouseID: house.houseId,\r\n              CIsSelect: true,\r\n            });\r\n          }\r\n        });\r\n      });\r\n    }\r\n\r\n    return result;\r\n  } onSaveSpecialNoticeFile(ref: any) {\r\n    const selectedHouses = this.convertSelectedHouseholdsToHouseSpecialNoticeFile();\r\n\r\n    // 除錯資訊\r\n    console.log('選中的戶別ID:', this.selectedHouseholds);\r\n    console.log('轉換後的戶別資料:', selectedHouses);\r\n    console.log('建築物資料 keys:', Object.keys(this.buildingData));\r\n    console.log('建築物資料總戶數:', Object.values(this.buildingData).reduce((total, houses) => total + houses.length, 0));\r\n    console.log('通知類型:', this.saveSpecialNoticeFile.selectedCNoticeType);\r\n\r\n    const param = {\r\n      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,\r\n      CBuildCaseId: this.selectedCBuildCase.value,\r\n      CFile: this.selectedFile ? this.selectedFile.CFileUpload : undefined,\r\n      CHouse: selectedHouses, // 使用新的轉換方法\r\n      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,\r\n      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,\r\n      CExamineNote: this.saveSpecialNoticeFile.CExamineNote\r\n    }\r\n\r\n    this.validation(param.CHouse)\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    } this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {\r\n      if (res && res.body! && res.body.StatusCode! === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.onFileClear() // 替換 clearImage()\r\n        this.getSpecialNoticeFileList()\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res && res.body && res.body.Message!);\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation(CHouse: any[]) {\r\n    this.valid.clear();\r\n    if (this.isNew && !this.selectedFile) {\r\n      this.valid.required('[檔案]', '')\r\n    }\r\n    if (!(CHouse.length > 0)) {\r\n      this.valid.required('[適用戶別]', '')\r\n    }\r\n    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote)\r\n  }\r\n\r\n  getActionName(actionID: number | undefined) {\r\n    let textR = \"\";\r\n    if (actionID != undefined) {\r\n      switch (actionID) {\r\n        case 1:\r\n          textR = \"傳送\";\r\n          break;\r\n        case 2:\r\n          textR = \"通過\";\r\n          break;\r\n        case 3:\r\n          textR = \"駁回\";\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n    return textR;\r\n  }\r\n\r\n\r\n  updateCIsClick(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {\r\n    const selectedHouses = houseSpecialNoticeFile.map(item => item.CHouseID);\r\n    return houseList2D.map(floorArray => {\r\n      return floorArray.map(item => {\r\n        if (selectedHouses.includes(item.CHouseID)) {\r\n          item.CIsSelect = true;\r\n        } else {\r\n          item.CIsSelect = false;\r\n        }\r\n        return item;\r\n      });\r\n    });\r\n  }  // 新增：載入建築物戶別資料 (使用 GetDropDown API)\r\n  private loadBuildingDataFromAPI(): void {\r\n    if (!this.selectedCBuildCase?.value) return;\r\n\r\n    this.isBuildingDataLoading = true;\r\n    this.buildingData = {}; // 清空舊資料\r\n\r\n    this._houseService2.apiHouseGetDropDownPost$Json({\r\n      buildCaseId: this.selectedCBuildCase.value\r\n    }).subscribe({\r\n      next: (response) => {\r\n        this.isBuildingDataLoading = false;\r\n        if (response.Entries) {\r\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isBuildingDataLoading = false;\r\n        console.error('Error loading building data from API:', error);\r\n        // 清空建築物資料，避免使用過時的資料\r\n        this.buildingData = {};\r\n      }\r\n    });\r\n  }  // 新增：將 API 回應轉換為建築物資料格式\r\n  private convertApiResponseToBuildingData(entries: any): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]: [string, any]) => {\r\n      // 不在父元件中預先篩選，將所有戶別資料傳給子元件\r\n      // 篩選邏輯由戶別綁定元件根據 preFilterHouseType 參數處理\r\n      buildingData[building] = houses.map((house: any) => ({\r\n        houseName: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseType: house.HouseType, // 保留戶別類型資訊供子元件篩選使用\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n  // 新增：將現有的 houseList2D 轉換為新戶別選擇器的格式\r\n  convertHouseList2DToBuildingData(): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    if (!this.houseList2D || this.houseList2D.length === 0) {\r\n      return buildingData;\r\n    }\r\n\r\n    this.houseList2D.forEach(row => {\r\n      row.forEach(house => {\r\n        if (!house.CHouseHold) return;\r\n\r\n        // 不在父元件中預先篩選，將所有戶別資料傳給子元件\r\n        // 篩選邏輯由戶別綁定元件根據 preFilterHouseType 參數處理\r\n\r\n        // 嘗試從戶別名稱中提取建築物代碼（假設格式為 A001, B002 等）\r\n        const buildingMatch = house.CHouseHold.match(/^([A-Z]+)/);\r\n        const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\r\n\r\n        if (!buildingData[building]) {\r\n          buildingData[building] = [];\r\n        } buildingData[building].push({\r\n          houseName: house.CHouseHold,\r\n          building: building,\r\n          floor: house.CFloor ? `${house.CFloor}F` : undefined,\r\n          houseId: house.CHouseID,\r\n          houseType: house.CHouseType || undefined, // 新增：戶別類型\r\n          isSelected: house.CIsSelect || false,\r\n          isDisabled: !house.CIsEnable ||\r\n            !(this.saveSpecialNoticeFile?.selectedCNoticeType?.value === house.CHouseType) ||\r\n            this.saveSpecialNoticeFile?.CExamineStauts === 0\r\n        });\r\n      });\r\n    });\r\n\r\n    return buildingData;\r\n  }  // 新增：處理戶別選擇變更\r\n  onHouseholdSelectionChange(selectedItems: HouseholdItem[]) {\r\n    // 更新 selectedHouseholds (使用 houseId)\r\n    this.selectedHouseholds = selectedItems.map(item => item.houseId).filter(id => id !== undefined) as number[];\r\n  }\r\n  // 新增：處理戶別ID變更事件\r\n  onHouseholdIdChange(selectedIds: number[]) {\r\n    this.selectedHouseholds = selectedIds;\r\n  }\r\n  // 新增：處理通知類型變更\r\n  onNoticeTypeChange() {\r\n    // 清空當前選擇的戶別\r\n    this.selectedHouseholds = [];\r\n\r\n    // 不需要重新載入資料，因為篩選邏輯已移至戶別綁定元件內部\r\n    // 戶別綁定元件會根據 preFilterHouseType 參數自動篩選顯示的戶別\r\n  }\r\n\r\n  // 新增：取得戶別選擇的提醒文案\r\n  getHouseholdReminderText(): string {\r\n    if (!this.saveSpecialNoticeFile?.selectedCNoticeType) {\r\n      return '請先選擇檔案類型，系統將根據類型篩選對應的戶別';\r\n    }\r\n\r\n    const noticeType = this.saveSpecialNoticeFile.selectedCNoticeType;\r\n    if (noticeType.value === 1) {\r\n      return '目前篩選顯示：地主戶，只會顯示地主戶相關的戶別選項';\r\n    } else if (noticeType.value === 2) {\r\n      return '目前篩選顯示：銷售戶，只會顯示銷售戶相關的戶別選項';\r\n    }\r\n\r\n    return '';\r\n  }\r\n}\r\n\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedCBuildCase\" class=\"col-9\"\r\n            (selectedChange)=\"onChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"openModel(dialog)\" *ngIf=\"isCreate\">\r\n            新增檔案 <i class=\"fas fa-plus\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container *ngIf=\"listSpecialNoticeFile\">\r\n      <div class=\"table-responsive mt-4\">\r\n        <h4 class=\"text-xl font-bold\">地主戶 </h4>\r\n        <table class=\"table table-striped border mt-3\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n              <th scope=\"col\" class=\"col-1 \">檔案名稱</th>\r\n              <th scope=\"col\" class=\"col-1\">適用戶別 </th>\r\n              <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n              <th scope=\"col\" class=\"col-1\">審核日期</th>\r\n              <th scope=\"col\" class=\"col-1\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of listSpecialNoticeFile?.ListLandLords ; let i = index\" class=\"text-center\">\r\n              <td>\r\n                <a class=\"cursor-pointer text-blue-500\" (click)=\"openPdfInNewTab(item)\">{{ item?.CFileName}}</a>\r\n              </td>\r\n              <td>{{ item.CHouseHold?.join('、')}}</td>\r\n              <td>{{ item.CExamineStatus != null && [0, 1, 2].includes(item.CExamineStatus) ?\r\n                cExamineStatusOption[item.CExamineStatus] : '' }}</td>\r\n              <td>{{ item?.CApproveDate | date:'yyyy/MM/dd HH:mm:ss' }}</td>\r\n              <td>\r\n                <button class=\"btn btn-outline-success btn-sm text-left m-[2px]\" *ngIf=\"isUpdate\"\r\n                  (click)=\"openModel(dialog, item)\">\r\n                  編輯\r\n                </button>\r\n                <button class=\"btn btn-outline-danger btn-sm m-[2px]\" *ngIf=\"isDelete\" (click)=\"onDelete(item)\">\r\n                  刪除\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div class=\"flex justify-center my-3\">\r\n        <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n          (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n        </ngb-pagination>\r\n      </div>\r\n      <div class=\"table-responsive mt-4\">\r\n        <h4 class=\"text-xl font-bold\">銷售戶</h4>\r\n        <table class=\"table table-striped border mt-3\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n              <th scope=\"col\" class=\"col-1 \">檔案名稱</th>\r\n              <th scope=\"col\" class=\"col-1\">適用戶別 </th>\r\n              <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n              <th scope=\"col\" class=\"col-1\">審核日期</th>\r\n              <th scope=\"col\" class=\"col-1\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of listSpecialNoticeFile?.ListSales ; let i = index\" class=\"text-center\">\r\n              <td>\r\n                <a class=\"cursor-pointer text-blue-500\" (click)=\"openPdfInNewTab(item)\">{{ item?.CFileName}}</a>\r\n              </td>\r\n              <td>{{ item.CHouseHold?.join('、')}}</td>\r\n              <td>\r\n                {{ item.CExamineStatus != null && [0, 1, 2].includes(item.CExamineStatus) ?\r\n                cExamineStatusOption[item.CExamineStatus] : '' }}\r\n              </td>\r\n              <td>{{ item?.CApproveDate | date:'yyyy-MM-dd HH:mm:ss'}}</td>\r\n              <td>\r\n                <button class=\"btn btn-outline-success btn-sm text-left m-[2px]\" (click)=\"openModel(dialog, item)\"\r\n                  *ngIf=\"isUpdate\">\r\n                  編輯\r\n                </button>\r\n                <button class=\"btn btn-outline-danger btn-sm m-[2px]\" *ngIf=\"isDelete\" (click)=\"onDelete(item)\">\r\n                  刪除\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div class=\"flex justify-center my-3\">\r\n        <ngb-pagination [(page)]=\"pageIndexSales\" [pageSize]=\"pageSizeSales\" [collectionSize]=\"totalRecordsSales\"\r\n          (pageChange)=\"pageChangedSales($event)\" aria-label=\"Pagination\">\r\n        </ngb-pagination>\r\n      </div>\r\n    </ng-container>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:800px; max-height: 95vh\">\r\n    <nb-card-header> {{isNew ? '新增檔案-地主戶': '編輯' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex\">\r\n        <label for=\"remark\" style=\"min-width:75px\" class=\"required-field mr-4\" baseLabel>檔案類型</label>\r\n        <nb-select placeholder=\"Select Status\" [(ngModel)]=\"saveSpecialNoticeFile.selectedCNoticeType\" class=\"w-full\"\r\n          [disabled]=\"!isNew\" (ngModelChange)=\"onStatusChange($event)\">\r\n          <nb-option *ngFor=\"let status of cNoticeTypeOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-baseline\">\r\n        <app-file-upload [config]=\"fileUploadConfig\" [currentFileName]=\"selectedFile?.fileName || null\"\r\n          [currentFileUrl]=\"saveSpecialNoticeFile && saveSpecialNoticeFile.CFileUrl || null\" [labelMinWidth]=\"'75px'\"\r\n          (fileSelected)=\"onFileUpload($event)\" (fileCleared)=\"onFileClear()\">\r\n        </app-file-upload>\r\n      </div>\r\n      <div class=\"form-group d-flex mb-0\">\r\n        <label for=\"householdBinding\" baseLabel class=\"required-field mr-3\" style=\"min-width:75px\">適用戶別</label>\r\n      </div>\r\n      <div class=\"mt-1\">\r\n        <app-household-binding [(ngModel)]=\"selectedHouseholds\" [buildCaseId]=\"selectedCBuildCase?.value\"\r\n          [buildingData]=\"buildingData\" [maxSelections]=\"50\" placeholder=\"請選擇適用戶別\"\r\n          [disabled]=\" saveSpecialNoticeFile?.CExamineStauts == 0\" [allowBatchSelect]=\"true\" [useHouseNameMode]=\"false\"\r\n          [preFilterHouseType]=\"saveSpecialNoticeFile?.selectedCNoticeType?.value\"\r\n          [reminderText]=\"getHouseholdReminderText()\" (houseIdChange)=\"onHouseholdIdChange($event)\"\r\n          (selectionChange)=\"onHouseholdSelectionChange($event)\">\r\n        </app-household-binding>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"status\" baseLabel class=\"required-field mr-3\" style=\"min-width:75px\">送審備註</label>\r\n        <textarea nbInput [(ngModel)]=\"saveSpecialNoticeFile.CExamineNote\" [rows]=\"4\"\r\n          class=\"resize-none !max-w-full w-full\"></textarea>\r\n      </div>\r\n\r\n      <div class=\"d-flex justify-content-center min-w-[90px]\">\r\n        <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">\r\n          取消\r\n        </button>\r\n        <button class=\"btn btn-success btn-sm min-w-[90px]\" (click)=\"onSaveSpecialNoticeFile(ref)\">\r\n          儲存</button>\r\n      </div>\r\n      <div class=\"w-full\" *ngIf=\"!isNew\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"status\" baseLabel class=\"mr-3\" style=\"min-width:75px\">審核歷程</label>\r\n        </div>\r\n        <table class=\"table table-bordered\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr>\r\n              <th>時間</th>\r\n              <th>使用者</th>\r\n              <th>動作</th>\r\n              <th>說明</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let row of saveSpecialNoticeFile.tblExamineLogs\">\r\n              <td>{{row.CCreateDt | dateFormatHour}}</td>\r\n              <td>{{row.CCreator}}</td>\r\n              <td>{{getActionName(row.CAction)}}</td>\r\n              <td>{{row.CExamineNote}}</td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AASxD,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASA,YAAY,IAAIC,eAAe,QAAQ,8BAA8B;AAC9E,SAASC,aAAa,QAAQ,qCAAqC;AAKnE,SAASC,kBAAkB,QAAQ,sBAAsB;;;;;;;;;;;;;;;;;;;;ICN7CC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,KAAA,MACF;;;;;;IAMFR,EAAA,CAAAC,cAAA,iBAAsF;IAA7CD,EAAA,CAAAS,UAAA,mBAAAC,qEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,MAAAC,SAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAK,SAAA,CAAAH,SAAA,CAAiB;IAAA,EAAC;IAClEf,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAmB,SAAA,YAA2B;IAClCnB,EAAA,CAAAG,YAAA,EAAS;;;;;;IA4BHH,EAAA,CAAAC,cAAA,iBACoC;IAAlCD,EAAA,CAAAS,UAAA,mBAAAW,2FAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,MAAAC,SAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAK,SAAA,CAAAH,SAAA,EAAAO,OAAA,CAAuB;IAAA,EAAC;IACjCtB,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAgG;IAAzBD,EAAA,CAAAS,UAAA,mBAAAe,2FAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAAc,IAAA;MAAA,MAAAH,OAAA,GAAAtB,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAa,QAAA,CAAAJ,OAAA,CAAc;IAAA,EAAC;IAC7FtB,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAbTH,EAFJ,CAAAC,cAAA,aAAkG,SAC5F,YACsE;IAAhCD,EAAA,CAAAS,UAAA,mBAAAkB,4EAAA;MAAA,MAAAL,OAAA,GAAAtB,EAAA,CAAAW,aAAA,CAAAiB,GAAA,EAAAL,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAgB,eAAA,CAAAP,OAAA,CAAqB;IAAA,EAAC;IAACtB,EAAA,CAAAE,MAAA,GAAoB;IAC9FF,EAD8F,CAAAG,YAAA,EAAI,EAC7F;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAC+C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,UAAI;IAKFD,EAJA,CAAA8B,UAAA,KAAAC,kEAAA,qBACoC,KAAAC,kEAAA,qBAG4D;IAIpGhC,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAfuEH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAiC,iBAAA,CAAAX,OAAA,kBAAAA,OAAA,CAAAY,SAAA,CAAoB;IAE1FlC,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAiC,iBAAA,CAAAX,OAAA,CAAAa,UAAA,kBAAAb,OAAA,CAAAa,UAAA,CAAAC,IAAA,WAA+B;IAC/BpC,EAAA,CAAAM,SAAA,GAC+C;IAD/CN,EAAA,CAAAiC,iBAAA,CAAAX,OAAA,CAAAe,cAAA,YAAArC,EAAA,CAAAsC,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAlB,OAAA,CAAAe,cAAA,IAAAxB,MAAA,CAAA4B,oBAAA,CAAAnB,OAAA,CAAAe,cAAA,OAC+C;IAC/CrC,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAA0C,WAAA,QAAApB,OAAA,kBAAAA,OAAA,CAAAqB,YAAA,yBAAqD;IAEW3C,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAA+B,QAAA,CAAc;IAIzB5C,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAgC,QAAA,CAAc;;;;;;IAqCrE7C,EAAA,CAAAC,cAAA,iBACmB;IAD8CD,EAAA,CAAAS,UAAA,mBAAAqC,2FAAA;MAAA9C,EAAA,CAAAW,aAAA,CAAAoC,IAAA;MAAA,MAAAC,QAAA,GAAAhD,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,MAAAC,SAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAK,SAAA,CAAAH,SAAA,EAAAiC,QAAA,CAAuB;IAAA,EAAC;IAEhGhD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAgG;IAAzBD,EAAA,CAAAS,UAAA,mBAAAwC,2FAAA;MAAAjD,EAAA,CAAAW,aAAA,CAAAuC,IAAA;MAAA,MAAAF,QAAA,GAAAhD,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAa,QAAA,CAAAsB,QAAA,CAAc;IAAA,EAAC;IAC7FhD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAfTH,EAFJ,CAAAC,cAAA,aAA8F,SACxF,YACsE;IAAhCD,EAAA,CAAAS,UAAA,mBAAA0C,4EAAA;MAAA,MAAAH,QAAA,GAAAhD,EAAA,CAAAW,aAAA,CAAAyC,IAAA,EAAA7B,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAgB,eAAA,CAAAmB,QAAA,CAAqB;IAAA,EAAC;IAAChD,EAAA,CAAAE,MAAA,GAAoB;IAC9FF,EAD8F,CAAAG,YAAA,EAAI,EAC7F;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAoD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7DH,EAAA,CAAAC,cAAA,UAAI;IAKFD,EAJA,CAAA8B,UAAA,KAAAuB,kEAAA,qBACmB,KAAAC,kEAAA,qBAG6E;IAIpGtD,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAjBuEH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAiC,iBAAA,CAAAe,QAAA,kBAAAA,QAAA,CAAAd,SAAA,CAAoB;IAE1FlC,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAiC,iBAAA,CAAAe,QAAA,CAAAb,UAAA,kBAAAa,QAAA,CAAAb,UAAA,CAAAC,IAAA,WAA+B;IAEjCpC,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAAO,kBAAA,MAAAyC,QAAA,CAAAX,cAAA,YAAArC,EAAA,CAAAsC,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAQ,QAAA,CAAAX,cAAA,IAAAxB,MAAA,CAAA4B,oBAAA,CAAAO,QAAA,CAAAX,cAAA,YAEF;IACIrC,EAAA,CAAAM,SAAA,GAAoD;IAApDN,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAA0C,WAAA,QAAAM,QAAA,kBAAAA,QAAA,CAAAL,YAAA,yBAAoD;IAGnD3C,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAA+B,QAAA,CAAc;IAGsC5C,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAgC,QAAA,CAAc;;;;;;IApEjF7C,EAAA,CAAAuD,uBAAA,GAA4C;IAExCvD,EADF,CAAAC,cAAA,cAAmC,aACH;IAAAD,EAAA,CAAAE,MAAA,0BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIjCH,EAHN,CAAAC,cAAA,gBAAiF,YACxE,aACoE,aACxC;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA8B,UAAA,KAAA0B,wDAAA,mBAAkG;IAoBxGxD,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAEJH,EADF,CAAAC,cAAA,eAAsC,0BAEyB;IAD7CD,EAAA,CAAAyD,gBAAA,wBAAAC,yFAAAC,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA6D,kBAAA,CAAAhD,MAAA,CAAAiD,SAAA,EAAAH,MAAA,MAAA9C,MAAA,CAAAiD,SAAA,GAAAH,MAAA;MAAA,OAAA3D,EAAA,CAAAiB,WAAA,CAAA0C,MAAA;IAAA,EAAoB;IAClC3D,EAAA,CAAAS,UAAA,wBAAAiD,yFAAAC,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAcJ,MAAA,CAAAkD,WAAA,CAAAJ,MAAA,CAAmB;IAAA,EAAC;IAEtC3D,EADE,CAAAG,YAAA,EAAiB,EACb;IAEJH,EADF,CAAAC,cAAA,eAAmC,cACH;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIhCH,EAHN,CAAAC,cAAA,iBAAiF,aACxE,cACoE,cACxC;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA8B,UAAA,KAAAkC,wDAAA,mBAA8F;IAsBpGhE,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAEJH,EADF,CAAAC,cAAA,eAAsC,0BAE8B;IADlDD,EAAA,CAAAyD,gBAAA,wBAAAQ,yFAAAN,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA6D,kBAAA,CAAAhD,MAAA,CAAAqD,cAAA,EAAAP,MAAA,MAAA9C,MAAA,CAAAqD,cAAA,GAAAP,MAAA;MAAA,OAAA3D,EAAA,CAAAiB,WAAA,CAAA0C,MAAA;IAAA,EAAyB;IACvC3D,EAAA,CAAAS,UAAA,wBAAAwD,yFAAAN,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAcJ,MAAA,CAAAsD,gBAAA,CAAAR,MAAA,CAAwB;IAAA,EAAC;IAE3C3D,EADE,CAAAG,YAAA,EAAiB,EACb;;;;;IAlEqBH,EAAA,CAAAM,SAAA,IAA0C;IAA1CN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAuD,qBAAA,kBAAAvD,MAAA,CAAAuD,qBAAA,CAAAC,aAAA,CAA0C;IAsBnDrE,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAsE,gBAAA,SAAAzD,MAAA,CAAAiD,SAAA,CAAoB;IAAuB9D,EAAtB,CAAAI,UAAA,aAAAS,MAAA,CAAA0D,QAAA,CAAqB,mBAAA1D,MAAA,CAAA2D,YAAA,CAAgC;IAiBjExE,EAAA,CAAAM,SAAA,IAAsC;IAAtCN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAuD,qBAAA,kBAAAvD,MAAA,CAAAuD,qBAAA,CAAAK,SAAA,CAAsC;IAwB/CzE,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAsE,gBAAA,SAAAzD,MAAA,CAAAqD,cAAA,CAAyB;IAA4BlE,EAA3B,CAAAI,UAAA,aAAAS,MAAA,CAAA6D,aAAA,CAA0B,mBAAA7D,MAAA,CAAA8D,iBAAA,CAAqC;;;;;IAsBvG3E,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAwE,UAAA,CAAgB;IACnE5E,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqE,UAAA,CAAApE,KAAA,MACF;;;;;IAiDIR,EADF,CAAAC,cAAA,SAA6D,SACvD;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC1BF,EAD0B,CAAAG,YAAA,EAAK,EAC1B;;;;;IAJCH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAA6E,WAAA,OAAAC,OAAA,CAAAC,SAAA,EAAkC;IAClC/E,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAiC,iBAAA,CAAA6C,OAAA,CAAAE,QAAA,CAAgB;IAChBhF,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAiC,iBAAA,CAAApB,MAAA,CAAAoE,aAAA,CAAAH,OAAA,CAAAI,OAAA,EAA8B;IAC9BlF,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAiC,iBAAA,CAAA6C,OAAA,CAAAK,YAAA,CAAoB;;;;;IAhB5BnF,EAFJ,CAAAC,cAAA,cAAmC,cACiB,gBACkB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACxEF,EADwE,CAAAG,YAAA,EAAQ,EAC1E;IAIAH,EAHN,CAAAC,cAAA,gBAAsE,YAC7D,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACZH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEVF,EAFU,CAAAG,YAAA,EAAK,EACR,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA8B,UAAA,KAAAsD,8DAAA,kBAA6D;IAQnEpF,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IARoBH,EAAA,CAAAM,SAAA,IAAuC;IAAvCN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAwE,qBAAA,CAAAC,cAAA,CAAuC;;;;;;IAzDnEtF,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IAACD,EAAA,CAAAE,MAAA,GACjB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,uBAA2B,cACM,gBACoD;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7FH,EAAA,CAAAC,cAAA,oBAC+D;IADxBD,EAAA,CAAAyD,gBAAA,2BAAA8B,qFAAA5B,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAA6E,IAAA;MAAA,MAAA3E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA6D,kBAAA,CAAAhD,MAAA,CAAAwE,qBAAA,CAAAI,mBAAA,EAAA9B,MAAA,MAAA9C,MAAA,CAAAwE,qBAAA,CAAAI,mBAAA,GAAA9B,MAAA;MAAA,OAAA3D,EAAA,CAAAiB,WAAA,CAAA0C,MAAA;IAAA,EAAuD;IACxE3D,EAAA,CAAAS,UAAA,2BAAA8E,qFAAA5B,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAA6E,IAAA;MAAA,MAAA3E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBJ,MAAA,CAAA6E,cAAA,CAAA/B,MAAA,CAAsB;IAAA,EAAC;IAC5D3D,EAAA,CAAA8B,UAAA,IAAA6D,6DAAA,uBAAsE;IAI1E3F,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,cAAoD,2BAGoB;IAA9BD,EAAtC,CAAAS,UAAA,0BAAAmF,2FAAAjC,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAA6E,IAAA;MAAA,MAAA3E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAgBJ,MAAA,CAAAgF,YAAA,CAAAlC,MAAA,CAAoB;IAAA,EAAC,yBAAAmC,0FAAA;MAAA9F,EAAA,CAAAW,aAAA,CAAA6E,IAAA;MAAA,MAAA3E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAgBJ,MAAA,CAAAkF,WAAA,EAAa;IAAA,EAAC;IAEvE/F,EADE,CAAAG,YAAA,EAAkB,EACd;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACyD;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IACjGF,EADiG,CAAAG,YAAA,EAAQ,EACnG;IAEJH,EADF,CAAAC,cAAA,eAAkB,iCAMyC;IALlCD,EAAA,CAAAyD,gBAAA,2BAAAuC,kGAAArC,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAA6E,IAAA;MAAA,MAAA3E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA6D,kBAAA,CAAAhD,MAAA,CAAAoF,kBAAA,EAAAtC,MAAA,MAAA9C,MAAA,CAAAoF,kBAAA,GAAAtC,MAAA;MAAA,OAAA3D,EAAA,CAAAiB,WAAA,CAAA0C,MAAA;IAAA,EAAgC;IAKrD3D,EAD4C,CAAAS,UAAA,2BAAAyF,kGAAAvC,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAA6E,IAAA;MAAA,MAAA3E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBJ,MAAA,CAAAsF,mBAAA,CAAAxC,MAAA,CAA2B;IAAA,EAAC,6BAAAyC,oGAAAzC,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAA6E,IAAA;MAAA,MAAA3E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CACtEJ,MAAA,CAAAwF,0BAAA,CAAA1C,MAAA,CAAkC;IAAA,EAAC;IAE1D3D,EADE,CAAAG,YAAA,EAAwB,EACpB;IAEJH,EADF,CAAAC,cAAA,eAAkD,iBACiC;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7FH,EAAA,CAAAC,cAAA,oBACyC;IADvBD,EAAA,CAAAyD,gBAAA,2BAAA6C,qFAAA3C,MAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAA6E,IAAA;MAAA,MAAA3E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA6D,kBAAA,CAAAhD,MAAA,CAAAwE,qBAAA,CAAAF,YAAA,EAAAxB,MAAA,MAAA9C,MAAA,CAAAwE,qBAAA,CAAAF,YAAA,GAAAxB,MAAA;MAAA,OAAA3D,EAAA,CAAAiB,WAAA,CAAA0C,MAAA;IAAA,EAAgD;IAEpE3D,EAD2C,CAAAG,YAAA,EAAW,EAChD;IAGJH,EADF,CAAAC,cAAA,eAAwD,kBACY;IAAvBD,EAAA,CAAAS,UAAA,mBAAA8F,2EAAA;MAAA,MAAAC,OAAA,GAAAxG,EAAA,CAAAW,aAAA,CAAA6E,IAAA,EAAAiB,SAAA;MAAA,MAAA5F,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAA6F,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAC/DxG,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA2F;IAAvCD,EAAA,CAAAS,UAAA,mBAAAkG,2EAAA;MAAA,MAAAH,OAAA,GAAAxG,EAAA,CAAAW,aAAA,CAAA6E,IAAA,EAAAiB,SAAA;MAAA,MAAA5F,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAA+F,uBAAA,CAAAJ,OAAA,CAA4B;IAAA,EAAC;IACxFxG,EAAA,CAAAE,MAAA,qBAAE;IACNF,EADM,CAAAG,YAAA,EAAS,EACT;IACNH,EAAA,CAAA8B,UAAA,KAAA+E,wDAAA,mBAAmC;IAwBvC7G,EADE,CAAAG,YAAA,EAAe,EACP;;;;IAnESH,EAAA,CAAAM,SAAA,GACjB;IADiBN,EAAA,CAAAO,kBAAA,MAAAM,MAAA,CAAAiG,KAAA,uEACjB;IAI2C9G,EAAA,CAAAM,SAAA,GAAuD;IAAvDN,EAAA,CAAAsE,gBAAA,YAAAzD,MAAA,CAAAwE,qBAAA,CAAAI,mBAAA,CAAuD;IAC5FzF,EAAA,CAAAI,UAAA,cAAAS,MAAA,CAAAiG,KAAA,CAAmB;IACW9G,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAkG,kBAAA,CAAqB;IAMpC/G,EAAA,CAAAM,SAAA,GAA2B;IACyCN,EADpE,CAAAI,UAAA,WAAAS,MAAA,CAAAmG,gBAAA,CAA2B,qBAAAnG,MAAA,CAAAoG,YAAA,kBAAApG,MAAA,CAAAoG,YAAA,CAAAC,QAAA,UAAmD,mBAAArG,MAAA,CAAAwE,qBAAA,IAAAxE,MAAA,CAAAwE,qBAAA,CAAA8B,QAAA,SACX,yBAAyB;IAQtFnH,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAsE,gBAAA,YAAAzD,MAAA,CAAAoF,kBAAA,CAAgC;IAIrDjG,EAJsD,CAAAI,UAAA,gBAAAS,MAAA,CAAAuG,kBAAA,kBAAAvG,MAAA,CAAAuG,kBAAA,CAAAC,KAAA,CAAyC,iBAAAxG,MAAA,CAAAyG,YAAA,CAClE,qBAAqB,cAAAzG,MAAA,CAAAwE,qBAAA,kBAAAxE,MAAA,CAAAwE,qBAAA,CAAAkC,cAAA,OACM,0BAA0B,2BAA2B,uBAAA1G,MAAA,CAAAwE,qBAAA,kBAAAxE,MAAA,CAAAwE,qBAAA,CAAAI,mBAAA,kBAAA5E,MAAA,CAAAwE,qBAAA,CAAAI,mBAAA,CAAA4B,KAAA,CACrC,iBAAAxG,MAAA,CAAA2G,wBAAA,GAC7B;IAM3BxH,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAsE,gBAAA,YAAAzD,MAAA,CAAAwE,qBAAA,CAAAF,YAAA,CAAgD;IAACnF,EAAA,CAAAI,UAAA,WAAU;IAW1DJ,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAI,UAAA,UAAAS,MAAA,CAAAiG,KAAA,CAAY;;;AD5GvC,OAAM,MAAOW,yBAA0B,SAAQ3H,aAAa;EAC1D4H,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,eAA+B,EAC/BC,iBAAmC,EACnCC,yBAAmD,EACnDC,yBAAmD,EACnDC,aAA2B,EAC3BC,2BAAgD,EAChDC,YAAyB,EACzBC,cAA4B;IAClC,KAAK,CAACX,MAAM,CAAC;IAZP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,2BAA2B,GAA3BA,2BAA2B;IAC3B,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IASf,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAhE,QAAQ,GAAG,EAAE;IACb,KAAAT,SAAS,GAAG,CAAC;IACb,KAAAU,YAAY,GAAG,CAAC;IACzB,KAAAgE,cAAc,GAAG,CAAC;IAClB,KAAA9D,aAAa,GAAG,EAAE;IAClB,KAAAR,cAAc,GAAG,CAAC;IAClB,KAAAS,iBAAiB,GAAG,CAAC;IAKrB,KAAA8D,gBAAgB,GAAU,CAAC;MAAEjI,KAAK,EAAE,IAAI;MAAE6G,KAAK,EAAE;IAAE,CAAE,CAAC;IAEtD,KAAAN,kBAAkB,GAAU,CAC1B;MAAEvG,KAAK,EAAE,KAAK;MAAE6G,KAAK,EAAE;IAAC,CAAE,EAC1B;MAAE7G,KAAK,EAAE,KAAK;MAAE6G,KAAK,EAAE;IAAC,CAAE,CAC3B;IAGD,KAAAqB,8BAA8B,GAAG;MAC/BC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAGD;IACA,KAAAtB,YAAY,GAAiB,EAAE,EAAC;IAChC,KAAArB,kBAAkB,GAAa,EAAE,EAAC;IAClC,KAAA4C,qBAAqB,GAAY,KAAK,EAAC;IASvC;IACA,KAAA7B,gBAAgB,GAAqB;MACnC8B,aAAa,EAAE,CAAC,iBAAiB,CAAC;MAClCC,iBAAiB,EAAE,MAAM;MACzBC,eAAe,EAAE,iBAAiB;MAClCxI,KAAK,EAAE,MAAM;MACbyI,QAAQ,EAAE,WAAW;MACrBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,4BAA4B;MACxCC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE;KACd;IAED,KAAAxC,YAAY,GAA4B,IAAI;IA4H5C,KAAAxE,oBAAoB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAwD5C,KAAAqE,KAAK,GAAG,IAAI;EAnPM;EAET4C,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAmCA5F,WAAWA,CAAC6F,OAAe;IACzB,IAAI,CAAC9F,SAAS,GAAG8F,OAAO;EAC1B;EACAzF,gBAAgBA,CAACyF,OAAe;IAC9B,IAAI,CAAC1F,cAAc,GAAG0F,OAAO;EAC/B;EAqBA;EACA/D,YAAYA,CAACgE,UAA4B;IACvC,IAAI,CAAC5C,YAAY,GAAG4C,UAAU;EAChC;EACA;EACA9D,WAAWA,CAAA;IACT,IAAI,CAACkB,YAAY,GAAG,IAAI;EAC1B;EAGA6C,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC1C,kBAAkB,CAACC,KAAK,EAAE;MACjC,IAAI,CAAC0C,iCAAiC,EAAE;MACxC,IAAI,CAACC,wBAAwB,EAAE;MAC/B;MACA,IAAI,CAACC,uBAAuB,EAAE;IAChC;EACF;EAEAN,gBAAgBA,CAAA;IACd,IAAI,CAAC3B,iBAAiB,CAACkC,qCAAqC,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC,CAACC,IAAI,CAC7EzK,GAAG,CAAC0K,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,oBAAoB,GAAGH,GAAG,CAACC,OAAO,CAACG,GAAG,CAACJ,GAAG,IAAG;UAChD,OAAO;YACL7J,KAAK,EAAE6J,GAAG,CAACK,cAAc;YACzBrD,KAAK,EAAEgD,GAAG,CAACM;WACZ;QACH,CAAC,CAAC;QACF,IAAI,CAACvD,kBAAkB,GAAG,IAAI,CAACoD,oBAAoB,CAAC,CAAC,CAAC;QACtD,IAAI,IAAI,CAACpD,kBAAkB,CAACC,KAAK,EAAE;UACjC,IAAI,CAAC0C,iCAAiC,EAAE;UACxC,IAAI,CAACC,wBAAwB,EAAE;UAC/B;UACA,IAAI,CAACC,uBAAuB,EAAE;QAChC;MACF;IACF,CAAC,CAAC,CACH,CAACW,SAAS,EAAE;EACf;EAEAC,YAAYA,CAACC,YAAyB;IAEpC,MAAMC,WAAW,GAAkB,EAAE;IACrC,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACrCL,YAAY,CAACL,GAAG,CAACW,QAAQ,IAAIA,QAAQ,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF,KAAK,MAAMA,KAAK,IAAIP,YAAY,EAAE;MAChCD,WAAW,CAACS,IAAI,CAAC,EAAE,CAAC;IACtB;IACA,KAAK,MAAMJ,QAAQ,IAAIN,YAAY,EAAE;MACnC,MAAMW,UAAU,GAAGT,YAAY,CAACU,OAAO,CAACN,QAAQ,CAACC,MAAgB,CAAC;MAClE,IAAII,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBV,WAAW,CAACU,UAAU,CAAC,CAACD,IAAI,CAAC;UAC3BG,SAAS,EAAEP,QAAQ,EAAEO,SAAS,IAAI,KAAK;UACvCC,QAAQ,EAAER,QAAQ,CAACS,GAAG;UACtBC,UAAU,EAAEV,QAAQ,CAACU,UAAU;UAC/BT,MAAM,EAAED,QAAQ,CAACC,MAAM;UACvBlJ,UAAU,EAAEiJ,QAAQ,CAACjJ,UAAU;UAC/B4J,SAAS,EAAEX,QAAQ,CAACW;SACrB,CAAC;MACJ;IACF;IACA,OAAOhB,WAAW;EACpB;EAIAf,wBAAwBA,CAAA;IACtB,IAAI,CAAC/B,yBAAyB,CAAC+D,qDAAqD,CAAC;MACnF7B,IAAI,EAAE;QACJ8B,YAAY,EAAE,IAAI,CAAC7E,kBAAkB,CAACC,KAAK;QAC3C6E,iBAAiB,EAAE,IAAI,CAACpI,SAAS;QACjCqI,cAAc,EAAE,IAAI,CAACjI,cAAc;QACnCkI,gBAAgB,EAAE,IAAI,CAAC7H,QAAQ;QAC/B8H,aAAa,EAAE,IAAI,CAAC3H;;KAEvB,CAAC,CAACkG,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACnG,qBAAqB,GAAGiG,GAAG,CAACC,OAAO;QACxC,IAAI,CAAC9F,YAAY,GAAG6F,GAAG,CAACC,OAAO,CAACgC,kBAAkB,IAAI,CAAC;QACvD,IAAI,CAAC3H,iBAAiB,GAAG0F,GAAG,CAACC,OAAO,CAACiC,cAAc,IAAI,CAAC;MAC1D;IACF,CAAC,CAAC;EACJ;EAIA7G,cAAcA,CAAC8G,SAAc;IAC3B;IACA,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEA1C,iCAAiCA,CAAA;IAC/B,IAAI,CAAC7B,yBAAyB,CAACwE,8DAA8D,CAAC;MAC5FvC,IAAI,EAAE,IAAI,CAAC/C,kBAAkB,CAACC;KAC/B,CAAC,CAACuD,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACoC,8BAA8B,GAAGtC,GAAG,CAACC,OAAO;MACnD;IACF,CAAC,CAAC;EACJ;EAEAsC,0BAA0BA,CAACC,CAAW,EAAEC,CAA8H;IACpK,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMC,IAAI,IAAIH,CAAC,EAAE;MACpB,MAAMI,YAAY,GAAGH,CAAC,CAACI,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKJ,IAAI,IAAIG,KAAK,CAACE,WAAW,CAAC;MACpFN,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAACC,YAAY;IAC1B;IACA,OAAOF,CAAC;EACV;EAGAO,cAAcA,CAACjG,KAAU,EAAEkG,OAAc;IACvC,KAAK,MAAMP,IAAI,IAAIO,OAAO,EAAE;MAC1B,IAAIP,IAAI,CAAC3F,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO2F,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAIAQ,eAAeA,CAACC,WAA0B,EAAEC,sBAAgD;IAC1F,MAAMC,cAAc,GAAG,IAAIxC,GAAG,CAACuC,sBAAsB,CAACjD,GAAG,CAACuC,IAAI,IAAI,GAAGA,IAAI,CAAC7K,UAAU,IAAI6K,IAAI,CAAC3B,MAAM,EAAE,CAAC,CAAC;IACvG,OAAOoC,WAAW,CAAChD,GAAG,CAACmD,UAAU,IAAG;MAClC,OAAOA,UAAU,CAACnD,GAAG,CAACuC,IAAI,IAAG;QAC3B,MAAMa,GAAG,GAAG,GAAGb,IAAI,CAAC7K,UAAU,IAAI6K,IAAI,CAAC3B,MAAM,EAAE;QAC/C,IAAIsC,cAAc,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;UAC3Bb,IAAI,CAACrB,SAAS,GAAG,IAAI;QACvB,CAAC,MAAM;UACLqB,IAAI,CAACrB,SAAS,GAAG,KAAK;QACxB;QACA,OAAOqB,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAe,eAAeA,CAACC,CAAQ,EAAEC,CAAQ;IAChC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAACF,CAAC,CAACxD,GAAG,CAACuC,IAAI,IAAI,CAAC,GAAGA,IAAI,CAAC7K,UAAU,IAAI6K,IAAI,CAAC3B,MAAM,EAAE,EAAE2B,IAAI,CAACrB,SAAS,CAAC,CAAC,CAAC;IAC1F,OAAOqC,CAAC,CAACvD,GAAG,CAACuC,IAAI,IAAG;MAClB,MAAMa,GAAG,GAAG,GAAGb,IAAI,CAAC7K,UAAU,IAAI6K,IAAI,CAAC3B,MAAM,EAAE;MAC/C,OAAO;QACL,GAAG2B,IAAI;QACPrB,SAAS,EAAEuC,IAAI,CAACJ,GAAG,CAACD,GAAG,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACP,GAAG,CAAC,GAAG;OAC5C;IACH,CAAC,CAAC;EACJ;EAEAQ,wBAAwBA,CAACrB,IAAS,EAAEsB,GAAQ;IAC1C,IAAI,CAACrG,yBAAyB,CAACsG,qDAAqD,CAAC;MACnFpE,IAAI,EAAE6C,IAAI,CAACwB;KACZ,CAAC,CAAC5D,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,MAAMkE,IAAI,GAAGpE,GAAG,CAACC,OAAO;QACxB,IAAI,CAACjF,qBAAqB,GAAG;UAC3B8B,QAAQ,EAAEsH,IAAI,CAACC,oBAAoB,EAAEvH,QAAQ,IAAIwH,SAAS;UAC1D/F,WAAW,EAAE6F,IAAI,CAACC,oBAAoB,EAAE9F,WAAW;UACnDqD,YAAY,EAAEwC,IAAI,CAACC,oBAAoB,EAAEzC,YAAY;UACrDuC,oBAAoB,EAAEC,IAAI,CAACC,oBAAoB,EAAEF,oBAAoB;UACrE/I,mBAAmB,EAAEgJ,IAAI,CAACC,oBAAoB,EAAE9F,WAAW,GAAG,IAAI,CAAC0E,cAAc,CAACmB,IAAI,CAACC,oBAAoB,EAAE9F,WAAW,EAAE,IAAI,CAAC7B,kBAAkB,CAAC,GAAG,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAAC;UAC/KzB,cAAc,EAAEmJ,IAAI,CAACnJ,cAAc,GAAGmJ,IAAI,CAACnJ,cAAc,GAAGqJ,SAAS;UACrExJ,YAAY,EAAEsJ,IAAI,CAACtJ,YAAY;UAC/ByJ,0BAA0B,EAAEH,IAAI,CAACG,0BAA0B,EAAEtD,MAAM,CAAEuD,CAAM,IAAKA,CAAC,CAAClD,SAAS,CAAC;UAC5FpE,cAAc,EAAEkH,IAAI,CAAClH;SACtB;QACD;QACA,IAAI,CAACtB,kBAAkB,GAAG,IAAI,CAACZ,qBAAqB,CAACuJ,0BAA0B,EAAEnE,GAAG,CAAEqE,KAAU,IAAKA,KAAK,CAAClD,QAAQ,CAAC,IAAI,EAAE;QAE1H;QACA,IAAI,CAAC3B,uBAAuB,EAAE;QAE9B,IAAI,CAACrC,aAAa,CAACmH,IAAI,CAACT,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIAzM,eAAeA,CAACmN,IAA2B;IACzC;IACA,IAAI,CAACA,IAAI,CAACC,KAAK,IAAI,CAACD,IAAI,CAAC9M,SAAS,EAAE;MAClC,IAAI,CAAC2F,OAAO,CAACqH,YAAY,CAAC,SAAS,CAAC;MACpC;IACF;IAEA;IACA,IAAI,CAAC7G,YAAY,CAAC8G,OAAO,CAACH,IAAI,CAACC,KAAK,EAAED,IAAI,CAAC9M,SAAS,CAAC,CAAC0I,SAAS,CAAC;MAC9DwE,IAAI,EAAGC,IAAU,IAAI;QACnB;QACA,MAAMC,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAEzC;QACA,MAAMI,SAAS,GAAGC,MAAM,CAACX,IAAI,CAACO,OAAO,EAAE,QAAQ,CAAC;QAEhD,IAAI,CAACG,SAAS,EAAE;UACd,IAAI,CAAC5H,OAAO,CAACqH,YAAY,CAAC,kBAAkB,CAAC;UAC7CK,GAAG,CAACI,eAAe,CAACL,OAAO,CAAC,CAAC,CAAC;UAC9B;QACF;QAEA;QACAG,SAAS,CAACG,gBAAgB,CAAC,cAAc,EAAE,MAAK;UAC9CL,GAAG,CAACI,eAAe,CAACL,OAAO,CAAC;QAC9B,CAAC,CAAC;MACJ,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAAChI,OAAO,CAACqH,YAAY,CAAC,cAAc,CAAC;MAC3C;KACD,CAAC;EACJ;EAAEhO,SAASA,CAACoN,GAAQ,EAAEtB,IAAU;IAC9B,IAAI,CAAClG,KAAK,GAAG,IAAI;IACjB,IAAI,CAACf,WAAW,EAAE,EAAC;IACnB,IAAI,CAACE,kBAAkB,GAAG,EAAE,EAAC;IAC7B,IAAI,CAACZ,qBAAqB,GAAG;MAC3BuD,WAAW,EAAE,CAAC;MACdqD,YAAY,EAAE0C,SAAS;MACvBM,KAAK,EAAEN,SAAS;MAChBoB,MAAM,EAAE,EAAE;MACVvB,oBAAoB,EAAEG,SAAS;MAC/BqB,YAAY,EAAE,KAAK;MACnBvK,mBAAmB,EAAE,IAAI,CAACsB,kBAAkB,CAAC,CAAC,CAAC;MAC/C5B,YAAY,EAAE,EAAE;MAChByJ,0BAA0B,EAAED;KAC7B;IAED,IAAI3B,IAAI,EAAE;MACR,IAAI,CAAClG,KAAK,GAAG,KAAK;MAClB,IAAI,CAACuH,wBAAwB,CAACrB,IAAI,EAAEsB,GAAG,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAACxH,KAAK,GAAG,IAAI;MACjB,IAAI,CAACc,aAAa,CAACmH,IAAI,CAACT,GAAG,CAAC;MAC5B;MACA2B,UAAU,CAAC,MAAK;QACd,IAAI,CAAChG,uBAAuB,EAAE;MAChC,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEAiG,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACzE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAI0E,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACE,SAAS,CAACD,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEAzO,QAAQA,CAACsL,IAAS;IAChB,IAAI0C,MAAM,CAACY,OAAO,CAAC,WAAWtD,IAAI,CAACwB,oBAAoB,IAAI,CAAC,EAAE;MAC5D,IAAI,CAACvG,yBAAyB,CAACsI,oDAAoD,CAAC;QAClFpG,IAAI,EAAE6C,IAAI,CAACwB;OACZ,CAAC,CAAC5D,SAAS,CAACP,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC1C,OAAO,CAAC2I,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAAC1G,iBAAiB,EAAE;QAC1B;MACF,CAAC,CAAC;IACJ;IACA,OAAOkD,IAAI;EACb;EAEAyD,WAAWA,CAACC,SAAqC;IAC/C,MAAMC,QAAQ,GAAa,EAAE;IAC7B,KAAK,MAAM9C,GAAG,IAAI6C,SAAS,EAAE;MAC3B,IAAIA,SAAS,CAAC7C,GAAG,CAAC,EAAE;QAClB8C,QAAQ,CAACnF,IAAI,CAACqC,GAAG,CAAC;MACpB;IACF;IACA,OAAO8C,QAAQ;EACjB;EACAC,gBAAgBA,CAACnC,IAAa;IAC5B,MAAMoC,SAAS,GAA6B,EAAE;IAC9C,KAAK,MAAMC,SAAS,IAAIrC,IAAI,EAAE;MAC5B,KAAK,MAAMK,KAAK,IAAIgC,SAAS,EAAE;QAC7B,IAAIhC,KAAK,CAACnD,SAAS,IAAImD,KAAK,CAAC/C,SAAS,IAAI+C,KAAK,CAAChD,UAAU,KAAK,IAAI,CAACzG,qBAAqB,CAACI,mBAAmB,CAAC4B,KAAK,EAAE;UACnHwJ,SAAS,CAACrF,IAAI,CAAC;YACbI,QAAQ,EAAEkD,KAAK,CAAClD,QAAQ;YACxBD,SAAS,EAAEmD,KAAK,CAACnD;WAClB,CAAC;QACJ;MACF;IACF;IACA,OAAOkF,SAAS;EAClB;EAEA;EACAE,iDAAiDA,CAAA;IAC/C,MAAMC,MAAM,GAA6B,EAAE;IAE3C;IACA,IAAI,IAAI,CAAC/K,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACgL,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC3J,YAAY,EAAE;MACtF;MACA4J,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC7J,YAAY,CAAC,CAAC8J,OAAO,CAACC,SAAS,IAAG;QACnDA,SAAS,CAACD,OAAO,CAACtC,KAAK,IAAG;UACxB,IAAIA,KAAK,CAACwC,OAAO,IACf,IAAI,CAACrL,kBAAkB,CAACzD,QAAQ,CAACsM,KAAK,CAACwC,OAAO,CAAC,IAC/C,CAACxC,KAAK,CAACyC,UAAU,EAAE;YACnBP,MAAM,CAACxF,IAAI,CAAC;cACVI,QAAQ,EAAEkD,KAAK,CAACwC,OAAO;cACvB3F,SAAS,EAAE;aACZ,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,OAAOqF,MAAM;EACf;EAAEpK,uBAAuBA,CAAC0H,GAAQ;IAChC,MAAMX,cAAc,GAAG,IAAI,CAACoD,iDAAiD,EAAE;IAE/E;IACAjB,OAAO,CAAC0B,GAAG,CAAC,UAAU,EAAE,IAAI,CAACvL,kBAAkB,CAAC;IAChD6J,OAAO,CAAC0B,GAAG,CAAC,WAAW,EAAE7D,cAAc,CAAC;IACxCmC,OAAO,CAAC0B,GAAG,CAAC,aAAa,EAAEN,MAAM,CAACO,IAAI,CAAC,IAAI,CAACnK,YAAY,CAAC,CAAC;IAC1DwI,OAAO,CAAC0B,GAAG,CAAC,WAAW,EAAEN,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC7J,YAAY,CAAC,CAACoK,MAAM,CAAC,CAACC,KAAK,EAAEC,MAAM,KAAKD,KAAK,GAAGC,MAAM,CAACX,MAAM,EAAE,CAAC,CAAC,CAAC;IAC9GnB,OAAO,CAAC0B,GAAG,CAAC,OAAO,EAAE,IAAI,CAACnM,qBAAqB,CAACI,mBAAmB,CAAC;IAEpE,MAAMoM,KAAK,GAAG;MACZjJ,WAAW,EAAE,IAAI,CAACvD,qBAAqB,CAACI,mBAAmB,CAAC4B,KAAK;MACjE4E,YAAY,EAAE,IAAI,CAAC7E,kBAAkB,CAACC,KAAK;MAC3C4H,KAAK,EAAE,IAAI,CAAChI,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC6K,WAAW,GAAGnD,SAAS;MACpEoB,MAAM,EAAEpC,cAAc;MAAE;MACxBa,oBAAoB,EAAE,IAAI,CAACnJ,qBAAqB,CAACmJ,oBAAoB,IAAIG,SAAS;MAClFqB,YAAY,EAAE,IAAI,CAAC3K,qBAAqB,CAAC2K,YAAY,IAAI,KAAK;MAC9D7K,YAAY,EAAE,IAAI,CAACE,qBAAqB,CAACF;KAC1C;IAED,IAAI,CAAC4M,UAAU,CAACF,KAAK,CAAC9B,MAAM,CAAC;IAE7B,IAAI,IAAI,CAACjI,KAAK,CAACkK,aAAa,CAACf,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACpJ,OAAO,CAACoK,aAAa,CAAC,IAAI,CAACnK,KAAK,CAACkK,aAAa,CAAC;MACpD;IACF;IAAE,IAAI,CAAC5J,2BAA2B,CAAC8J,qBAAqB,CAACL,KAAK,CAAC,CAACjH,SAAS,CAACP,GAAG,IAAG;MAC9E,IAAIA,GAAG,IAAIA,GAAG,CAACF,IAAK,IAAIE,GAAG,CAACF,IAAI,CAACI,UAAW,KAAK,CAAC,EAAE;QAClD,IAAI,CAAC1C,OAAO,CAAC2I,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACzK,WAAW,EAAE,EAAC;QACnB,IAAI,CAACiE,wBAAwB,EAAE;QAC/BsE,GAAG,CAAC6D,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACtK,OAAO,CAACqH,YAAY,CAAC7E,GAAG,IAAIA,GAAG,CAACF,IAAI,IAAIE,GAAG,CAACF,IAAI,CAACiI,OAAQ,CAAC;MACjE;IACF,CAAC,CAAC;EACJ;EAGA1L,OAAOA,CAAC4H,GAAQ;IACdA,GAAG,CAAC6D,KAAK,EAAE;EACb;EAEAJ,UAAUA,CAAChC,MAAa;IACtB,IAAI,CAACjI,KAAK,CAACuK,KAAK,EAAE;IAClB,IAAI,IAAI,CAACvL,KAAK,IAAI,CAAC,IAAI,CAACG,YAAY,EAAE;MACpC,IAAI,CAACa,KAAK,CAACoB,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;IACjC;IACA,IAAI,EAAE6G,MAAM,CAACkB,MAAM,GAAG,CAAC,CAAC,EAAE;MACxB,IAAI,CAACnJ,KAAK,CAACoB,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC;IACnC;IACA,IAAI,CAACpB,KAAK,CAACoB,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC7D,qBAAqB,CAACF,YAAY,CAAC;EACxE;EAEAF,aAAaA,CAACqN,QAA4B;IACxC,IAAIC,KAAK,GAAG,EAAE;IACd,IAAID,QAAQ,IAAI3D,SAAS,EAAE;MACzB,QAAQ2D,QAAQ;QACd,KAAK,CAAC;UACJC,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF;UACE;MACJ;IACF;IACA,OAAOA,KAAK;EACd;EAGAC,cAAcA,CAAC/E,WAA0B,EAAEC,sBAAgD;IACzF,MAAMC,cAAc,GAAGD,sBAAsB,CAACjD,GAAG,CAACuC,IAAI,IAAIA,IAAI,CAACpB,QAAQ,CAAC;IACxE,OAAO6B,WAAW,CAAChD,GAAG,CAACmD,UAAU,IAAG;MAClC,OAAOA,UAAU,CAACnD,GAAG,CAACuC,IAAI,IAAG;QAC3B,IAAIW,cAAc,CAACnL,QAAQ,CAACwK,IAAI,CAACpB,QAAQ,CAAC,EAAE;UAC1CoB,IAAI,CAACrB,SAAS,GAAG,IAAI;QACvB,CAAC,MAAM;UACLqB,IAAI,CAACrB,SAAS,GAAG,KAAK;QACxB;QACA,OAAOqB,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAE;EACK/C,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC7C,kBAAkB,EAAEC,KAAK,EAAE;IAErC,IAAI,CAACwB,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACvB,YAAY,GAAG,EAAE,CAAC,CAAC;IAExB,IAAI,CAACgB,cAAc,CAACmK,4BAA4B,CAAC;MAC/CC,WAAW,EAAE,IAAI,CAACtL,kBAAkB,CAACC;KACtC,CAAC,CAACuD,SAAS,CAAC;MACXwE,IAAI,EAAGuD,QAAQ,IAAI;QACjB,IAAI,CAAC9J,qBAAqB,GAAG,KAAK;QAClC,IAAI8J,QAAQ,CAACrI,OAAO,EAAE;UACpB,IAAI,CAAChD,YAAY,GAAG,IAAI,CAACsL,gCAAgC,CAACD,QAAQ,CAACrI,OAAO,CAAC;QAC7E;MACF,CAAC;MACDuF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChH,qBAAqB,GAAG,KAAK;QAClCiH,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D;QACA,IAAI,CAACvI,YAAY,GAAG,EAAE;MACxB;KACD,CAAC;EACJ,CAAC,CAAE;EACKsL,gCAAgCA,CAACC,OAAY;IACnD,MAAMvL,YAAY,GAAiB,EAAE;IAErC4J,MAAM,CAAC2B,OAAO,CAACA,OAAO,CAAC,CAACzB,OAAO,CAAC,CAAC,CAAC0B,QAAQ,EAAElB,MAAM,CAAgB,KAAI;MACpE;MACA;MACAtK,YAAY,CAACwL,QAAQ,CAAC,GAAGlB,MAAM,CAACnH,GAAG,CAAEqE,KAAU,KAAM;QACnDiE,SAAS,EAAEjE,KAAK,CAACkE,SAAS;QAC1BF,QAAQ,EAAEhE,KAAK,CAACmE,QAAQ;QACxB1H,KAAK,EAAEuD,KAAK,CAACoE,KAAK;QAClB5B,OAAO,EAAExC,KAAK,CAACqE,OAAO;QACtBC,SAAS,EAAEtE,KAAK,CAACuE,SAAS;QAAE;QAC5BC,UAAU,EAAE,KAAK;QACjB/B,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOjK,YAAY;EACrB;EACA;EACAiM,gCAAgCA,CAAA;IAC9B,MAAMjM,YAAY,GAAiB,EAAE;IAErC,IAAI,CAAC,IAAI,CAACmG,WAAW,IAAI,IAAI,CAACA,WAAW,CAACwD,MAAM,KAAK,CAAC,EAAE;MACtD,OAAO3J,YAAY;IACrB;IAEA,IAAI,CAACmG,WAAW,CAAC2D,OAAO,CAACoC,GAAG,IAAG;MAC7BA,GAAG,CAACpC,OAAO,CAACtC,KAAK,IAAG;QAClB,IAAI,CAACA,KAAK,CAAC3M,UAAU,EAAE;QAEvB;QACA;QAEA;QACA,MAAMsR,aAAa,GAAG3E,KAAK,CAAC3M,UAAU,CAACuR,KAAK,CAAC,WAAW,CAAC;QACzD,MAAMZ,QAAQ,GAAGW,aAAa,GAAG,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM;QAEhE,IAAI,CAACnM,YAAY,CAACwL,QAAQ,CAAC,EAAE;UAC3BxL,YAAY,CAACwL,QAAQ,CAAC,GAAG,EAAE;QAC7B;QAAExL,YAAY,CAACwL,QAAQ,CAAC,CAACtH,IAAI,CAAC;UAC5BuH,SAAS,EAAEjE,KAAK,CAAC3M,UAAU;UAC3B2Q,QAAQ,EAAEA,QAAQ;UAClBvH,KAAK,EAAEuD,KAAK,CAACzD,MAAM,GAAG,GAAGyD,KAAK,CAACzD,MAAM,GAAG,GAAGsD,SAAS;UACpD2C,OAAO,EAAExC,KAAK,CAAClD,QAAQ;UACvBwH,SAAS,EAAEtE,KAAK,CAAChD,UAAU,IAAI6C,SAAS;UAAE;UAC1C2E,UAAU,EAAExE,KAAK,CAACnD,SAAS,IAAI,KAAK;UACpC4F,UAAU,EAAE,CAACzC,KAAK,CAAC/C,SAAS,IAC1B,EAAE,IAAI,CAAC1G,qBAAqB,EAAEI,mBAAmB,EAAE4B,KAAK,KAAKyH,KAAK,CAAChD,UAAU,CAAC,IAC9E,IAAI,CAACzG,qBAAqB,EAAEkC,cAAc,KAAK;SAClD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOD,YAAY;EACrB,CAAC,CAAE;EACHjB,0BAA0BA,CAACsN,aAA8B;IACvD;IACA,IAAI,CAAC1N,kBAAkB,GAAG0N,aAAa,CAAClJ,GAAG,CAACuC,IAAI,IAAIA,IAAI,CAACsE,OAAO,CAAC,CAAChG,MAAM,CAACsI,EAAE,IAAIA,EAAE,KAAKjF,SAAS,CAAa;EAC9G;EACA;EACAxI,mBAAmBA,CAAC0N,WAAqB;IACvC,IAAI,CAAC5N,kBAAkB,GAAG4N,WAAW;EACvC;EACA;EACApH,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACxG,kBAAkB,GAAG,EAAE;IAE5B;IACA;EACF;EAEA;EACAuB,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACnC,qBAAqB,EAAEI,mBAAmB,EAAE;MACpD,OAAO,yBAAyB;IAClC;IAEA,MAAMqO,UAAU,GAAG,IAAI,CAACzO,qBAAqB,CAACI,mBAAmB;IACjE,IAAIqO,UAAU,CAACzM,KAAK,KAAK,CAAC,EAAE;MAC1B,OAAO,2BAA2B;IACpC,CAAC,MAAM,IAAIyM,UAAU,CAACzM,KAAK,KAAK,CAAC,EAAE;MACjC,OAAO,2BAA2B;IACpC;IAEA,OAAO,EAAE;EACX;;;uCAzkBWI,yBAAyB,EAAAzH,EAAA,CAAA+T,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjU,EAAA,CAAA+T,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAnU,EAAA,CAAA+T,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAArU,EAAA,CAAA+T,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAvU,EAAA,CAAA+T,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAzU,EAAA,CAAA+T,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA3U,EAAA,CAAA+T,iBAAA,CAAAW,EAAA,CAAAE,wBAAA,GAAA5U,EAAA,CAAA+T,iBAAA,CAAAW,EAAA,CAAAG,wBAAA,GAAA7U,EAAA,CAAA+T,iBAAA,CAAAW,EAAA,CAAAI,YAAA,GAAA9U,EAAA,CAAA+T,iBAAA,CAAAgB,EAAA,CAAAC,mBAAA,GAAAhV,EAAA,CAAA+T,iBAAA,CAAAkB,EAAA,CAAAC,WAAA,GAAAlV,EAAA,CAAA+T,iBAAA,CAAAW,EAAA,CAAAI,YAAA;IAAA;EAAA;;;YAAzBrN,yBAAyB;MAAA0N,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArV,EAAA,CAAAsV,0BAAA,EAAAtV,EAAA,CAAAuV,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCrDpC7V,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAmB,SAAA,qBAAiC;UACnCnB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBACyC;UADbD,EAAA,CAAAyD,gBAAA,2BAAAsS,uEAAApS,MAAA;YAAA3D,EAAA,CAAAW,aAAA,CAAAqV,GAAA;YAAAhW,EAAA,CAAA6D,kBAAA,CAAAiS,GAAA,CAAA1O,kBAAA,EAAAzD,MAAA,MAAAmS,GAAA,CAAA1O,kBAAA,GAAAzD,MAAA;YAAA,OAAA3D,EAAA,CAAAiB,WAAA,CAAA0C,MAAA;UAAA,EAAgC;UAC1D3D,EAAA,CAAAS,UAAA,4BAAAwV,wEAAA;YAAAjW,EAAA,CAAAW,aAAA,CAAAqV,GAAA;YAAA,OAAAhW,EAAA,CAAAiB,WAAA,CAAkB6U,GAAA,CAAAhM,iBAAA,EAAmB;UAAA,EAAC;UACtC9J,EAAA,CAAA8B,UAAA,KAAAoU,+CAAA,uBAAoE;UAK1ElW,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,cAAsB,cAC2B;UAC7CD,EAAA,CAAA8B,UAAA,KAAAqU,4CAAA,qBAAsF;UAK5FnW,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAENH,EAAA,CAAA8B,UAAA,KAAAsU,kDAAA,4BAA4C;UAkF9CpW,EAAA,CAAAG,YAAA,EAAe;UACfH,EAAA,CAAAmB,SAAA,0BAEiB;UACnBnB,EAAA,CAAAG,YAAA,EAAU;UAIVH,EAAA,CAAA8B,UAAA,KAAAuU,iDAAA,kCAAArW,EAAA,CAAAsW,sBAAA,CAAoD;;;UA3GdtW,EAAA,CAAAM,SAAA,IAAgC;UAAhCN,EAAA,CAAAsE,gBAAA,YAAAwR,GAAA,CAAA1O,kBAAA,CAAgC;UAE9BpH,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA0V,GAAA,CAAAtL,oBAAA,CAAuB;UAQiBxK,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,SAAA0V,GAAA,CAAAS,QAAA,CAAc;UAO3EvW,EAAA,CAAAM,SAAA,EAA2B;UAA3BN,EAAA,CAAAI,UAAA,SAAA0V,GAAA,CAAA1R,qBAAA,CAA2B;;;qBDwBlC1E,YAAY,EAAA8W,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAE/W,YAAY,EAAAgX,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA,EAAA7C,EAAA,CAAA8C,eAAA,EAAA9C,EAAA,CAAA+C,mBAAA,EAAA/C,EAAA,CAAAgD,qBAAA,EAAAhD,EAAA,CAAAiD,qBAAA,EAAAjD,EAAA,CAAAkD,gBAAA,EAAAlD,EAAA,CAAAmD,iBAAA,EAAAnD,EAAA,CAAAoD,iBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAEjY,eAAe,EAAAkY,GAAA,CAAAC,yBAAA,EAAYjY,kBAAkB;MAAAkY,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}