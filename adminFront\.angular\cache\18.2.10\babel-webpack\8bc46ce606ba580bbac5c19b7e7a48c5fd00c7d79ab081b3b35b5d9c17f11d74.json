{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Electronic Codebook block mode.\n   */\n  CryptoJS.mode.ECB = function () {\n    var ECB = CryptoJS.lib.BlockCipherMode.extend();\n    ECB.Encryptor = ECB.extend({\n      processBlock: function (words, offset) {\n        this._cipher.encryptBlock(words, offset);\n      }\n    });\n    ECB.Decryptor = ECB.extend({\n      processBlock: function (words, offset) {\n        this._cipher.decryptBlock(words, offset);\n      }\n    });\n    return ECB;\n  }();\n  return CryptoJS.mode.ECB;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}