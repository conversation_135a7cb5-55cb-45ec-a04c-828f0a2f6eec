{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbInputModule, NbSelectModule, NbOptionModule } from '@nebular/theme';\nlet RequirementTemplateSelectorComponent = class RequirementTemplateSelectorComponent {\n  constructor(requirementService, dialogRef) {\n    this.requirementService = requirementService;\n    this.dialogRef = dialogRef;\n    this.buildCaseId = 0;\n    this.multiple = true;\n    this.preSelectedItems = [];\n    this.selectionConfirmed = new EventEmitter();\n    this.selectionCancelled = new EventEmitter();\n    // 資料相關屬性\n    this.requirements = [];\n    this.isLoading = false;\n    // 搜尋相關屬性\n    this.searchFilters = {\n      CBuildCaseID: 0,\n      CLocation: null,\n      CRequirement: null,\n      CHouseType: null,\n      CStatus: 1,\n      // 預設只顯示啟用的\n      CIsShow: null,\n      CIsSimple: null,\n      PageIndex: 1,\n      PageSize: 20\n    };\n    // 分頁相關屬性\n    this.totalRecords = 0;\n    this.currentPage = 1;\n    this.pageSize = 20;\n    // 房屋類型選項\n    this.houseTypeOptions = [{\n      value: 1,\n      label: '套房'\n    }, {\n      value: 2,\n      label: '1房'\n    }, {\n      value: 3,\n      label: '2房'\n    }, {\n      value: 4,\n      label: '3房'\n    }, {\n      value: 5,\n      label: '4房'\n    }, {\n      value: 6,\n      label: '5房以上'\n    }];\n    // 狀態選項\n    this.statusOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: 1,\n      label: '啟用'\n    }, {\n      value: 0,\n      label: '停用'\n    }];\n  }\n  ngOnInit() {\n    this.searchFilters.CBuildCaseID = this.buildCaseId;\n    this.loadRequirements();\n    this.initializePreSelectedItems();\n  }\n  // 初始化預選項目\n  initializePreSelectedItems() {\n    if (this.preSelectedItems && this.preSelectedItems.length > 0) {\n      // 將預選項目標記為已選擇\n      this.preSelectedItems.forEach(preSelected => {\n        const existingItem = this.requirements.find(req => req.CRequirementID === preSelected.CRequirementID);\n        if (existingItem) {\n          existingItem.selected = true;\n        }\n      });\n    }\n  }\n  // 載入需求資料\n  loadRequirements() {\n    this.isLoading = true;\n    const requestParams = {\n      ...this.searchFilters,\n      PageIndex: this.currentPage,\n      PageSize: this.pageSize\n    };\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: requestParams\n    }).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.StatusCode === 0 && response.Entries) {\n          this.requirements = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n          this.totalRecords = response.TotalItems || 0;\n          this.initializePreSelectedItems();\n        } else {\n          this.requirements = [];\n          this.totalRecords = 0;\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.requirements = [];\n        this.totalRecords = 0;\n        console.error('載入需求資料失敗:', error);\n      }\n    });\n  }\n  // 搜尋功能\n  onSearch() {\n    this.currentPage = 1;\n    this.loadRequirements();\n  }\n  // 重置搜尋\n  onReset() {\n    this.searchFilters = {\n      CBuildCaseID: this.buildCaseId,\n      CLocation: null,\n      CRequirement: null,\n      CHouseType: null,\n      CStatus: 1,\n      CIsShow: null,\n      CIsSimple: null,\n      PageIndex: 1,\n      PageSize: 20\n    };\n    this.currentPage = 1;\n    this.loadRequirements();\n  }\n  // 分頁變更\n  onPageChange(page) {\n    this.currentPage = page;\n    this.loadRequirements();\n  }\n  // 切換項目選擇狀態\n  toggleItemSelection(item) {\n    if (!this.multiple) {\n      // 單選模式：取消其他項目的選擇\n      this.requirements.forEach(req => {\n        if (req.CRequirementID !== item.CRequirementID) {\n          req.selected = false;\n        }\n      });\n    }\n    item.selected = !item.selected;\n  }\n  // 全選/取消全選\n  toggleSelectAll(selectAll) {\n    this.requirements.forEach(item => {\n      item.selected = selectAll;\n    });\n  }\n  // 獲取已選擇的項目\n  getSelectedItems() {\n    return this.requirements.filter(item => item.selected);\n  }\n  // 計算總價\n  getTotalPrice() {\n    return this.getSelectedItems().reduce((total, item) => {\n      return total + (item.CUnitPrice || 0);\n    }, 0);\n  }\n  // 確認選擇\n  confirmSelection() {\n    const selectedItems = this.getSelectedItems();\n    if (selectedItems.length === 0) {\n      alert('請至少選擇一個項目');\n      return;\n    }\n    const config = {\n      selectedItems: selectedItems,\n      totalPrice: this.getTotalPrice(),\n      buildCaseId: this.buildCaseId\n    };\n    this.selectionConfirmed.emit(config);\n    this.close();\n  }\n  // 關閉對話框\n  close() {\n    this.selectionCancelled.emit();\n    this.dialogRef.close();\n  }\n  // 獲取房屋類型顯示文字\n  getHouseTypeText(houseTypes) {\n    if (!houseTypes || houseTypes.length === 0) {\n      return '-';\n    }\n    const typeNames = houseTypes.map(type => {\n      const option = this.houseTypeOptions.find(opt => opt.value === type);\n      return option ? option.label : type.toString();\n    });\n    return typeNames.join(', ');\n  }\n  // 獲取狀態顯示文字\n  getStatusText(status) {\n    return status === 1 ? '啟用' : '停用';\n  }\n  // 獲取是否顯示文字\n  getIsShowText(isShow) {\n    return isShow ? '是' : '否';\n  }\n  // 獲取簡易客變文字\n  getIsSimpleText(isSimple) {\n    return isSimple ? '是' : '否';\n  }\n  // 獲取總頁數\n  getTotalPages() {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n  // 檢查是否全選\n  isAllSelected() {\n    return this.requirements.length > 0 && this.requirements.every(item => item.selected);\n  }\n  // 檢查是否部分選擇\n  isIndeterminate() {\n    return this.requirements.some(item => item.selected) && !this.requirements.every(item => item.selected);\n  }\n};\n__decorate([Input()], RequirementTemplateSelectorComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], RequirementTemplateSelectorComponent.prototype, \"multiple\", void 0);\n__decorate([Input()], RequirementTemplateSelectorComponent.prototype, \"preSelectedItems\", void 0);\n__decorate([Output()], RequirementTemplateSelectorComponent.prototype, \"selectionConfirmed\", void 0);\n__decorate([Output()], RequirementTemplateSelectorComponent.prototype, \"selectionCancelled\", void 0);\nRequirementTemplateSelectorComponent = __decorate([Component({\n  selector: 'app-requirement-template-selector',\n  templateUrl: './requirement-template-selector.component.html',\n  styleUrls: ['./requirement-template-selector.component.scss'],\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbInputModule, NbSelectModule, NbOptionModule]\n})], RequirementTemplateSelectorComponent);\nexport { RequirementTemplateSelectorComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "NbInputModule", "NbSelectModule", "NbOptionModule", "RequirementTemplateSelectorComponent", "constructor", "requirementService", "dialogRef", "buildCaseId", "multiple", "preSelectedItems", "selectionConfirmed", "selectionCancelled", "requirements", "isLoading", "searchFilters", "CBuildCaseID", "CLocation", "CRequirement", "CHouseType", "CStatus", "CIsShow", "CIsSimple", "PageIndex", "PageSize", "totalRecords", "currentPage", "pageSize", "houseTypeOptions", "value", "label", "statusOptions", "ngOnInit", "loadRequirements", "initializePreSelectedItems", "length", "for<PERSON>ach", "preSelected", "existingItem", "find", "req", "CRequirementID", "selected", "requestParams", "apiRequirementGetListPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "TotalItems", "error", "console", "onSearch", "onReset", "onPageChange", "page", "toggleItemSelection", "toggleSelectAll", "selectAll", "getSelectedItems", "filter", "getTotalPrice", "reduce", "total", "CUnitPrice", "confirmSelection", "selectedItems", "alert", "config", "totalPrice", "emit", "close", "getHouseTypeText", "houseTypes", "typeNames", "type", "option", "opt", "toString", "join", "getStatusText", "status", "getIsShowText", "isShow", "getIsSimpleText", "isSimple", "getTotalPages", "Math", "ceil", "isAllSelected", "every", "isIndeterminate", "some", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\requirement-template-selector\\requirement-template-selector.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport {\n  NbCardModule,\n  NbButtonModule,\n  NbIconModule,\n  NbCheckboxModule,\n  NbDialogRef,\n  NbInputModule,\n  NbSelectModule,\n  NbOptionModule\n} from '@nebular/theme';\nimport { RequirementService } from 'src/services/api/services/requirement.service';\nimport {\n  GetListRequirementRequest,\n  GetRequirement,\n  GetRequirementListResponseBase\n} from 'src/services/api/models';\n\n// 擴展 API 模型以支援前端選擇功能\nexport interface ExtendedRequirementItem extends GetRequirement {\n  selected?: boolean;\n}\n\n// 需求選擇配置介面\nexport interface RequirementSelectionConfig {\n  selectedItems: ExtendedRequirementItem[];\n  totalPrice: number;\n  buildCaseId: number;\n}\n\n@Component({\n  selector: 'app-requirement-template-selector',\n  templateUrl: './requirement-template-selector.component.html',\n  styleUrls: ['./requirement-template-selector.component.scss'],\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    NbCardModule,\n    NbButtonModule,\n    NbIconModule,\n    NbCheckboxModule,\n    NbInputModule,\n    NbSelectModule,\n    NbOptionModule\n  ]\n})\nexport class RequirementTemplateSelectorComponent implements OnInit {\n  @Input() buildCaseId: number = 0;\n  @Input() multiple: boolean = true;\n  @Input() preSelectedItems: ExtendedRequirementItem[] = [];\n  @Output() selectionConfirmed = new EventEmitter<RequirementSelectionConfig>();\n  @Output() selectionCancelled = new EventEmitter<void>();\n\n  // 資料相關屬性\n  requirements: ExtendedRequirementItem[] = [];\n  isLoading: boolean = false;\n\n  // 搜尋相關屬性\n  searchFilters: GetListRequirementRequest = {\n    CBuildCaseID: 0,\n    CLocation: null,\n    CRequirement: null,\n    CHouseType: null,\n    CStatus: 1, // 預設只顯示啟用的\n    CIsShow: null,\n    CIsSimple: null,\n    PageIndex: 1,\n    PageSize: 20\n  };\n\n  // 分頁相關屬性\n  totalRecords: number = 0;\n  currentPage: number = 1;\n  pageSize: number = 20;\n\n  // 房屋類型選項\n  houseTypeOptions = [\n    { value: 1, label: '套房' },\n    { value: 2, label: '1房' },\n    { value: 3, label: '2房' },\n    { value: 4, label: '3房' },\n    { value: 5, label: '4房' },\n    { value: 6, label: '5房以上' }\n  ];\n\n  // 狀態選項\n  statusOptions = [\n    { value: null, label: '全部' },\n    { value: 1, label: '啟用' },\n    { value: 0, label: '停用' }\n  ];\n\n  constructor(\n    private requirementService: RequirementService,\n    private dialogRef: NbDialogRef<RequirementTemplateSelectorComponent>\n  ) { }\n\n  ngOnInit() {\n    this.searchFilters.CBuildCaseID = this.buildCaseId;\n    this.loadRequirements();\n    this.initializePreSelectedItems();\n  }\n\n  // 初始化預選項目\n  initializePreSelectedItems() {\n    if (this.preSelectedItems && this.preSelectedItems.length > 0) {\n      // 將預選項目標記為已選擇\n      this.preSelectedItems.forEach(preSelected => {\n        const existingItem = this.requirements.find(req =>\n          req.CRequirementID === preSelected.CRequirementID\n        );\n        if (existingItem) {\n          existingItem.selected = true;\n        }\n      });\n    }\n  }\n\n  // 載入需求資料\n  loadRequirements() {\n    this.isLoading = true;\n\n    const requestParams = {\n      ...this.searchFilters,\n      PageIndex: this.currentPage,\n      PageSize: this.pageSize\n    };\n\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: requestParams\n    }).subscribe({\n      next: (response: GetRequirementListResponseBase) => {\n        this.isLoading = false;\n        if (response.StatusCode === 0 && response.Entries) {\n          this.requirements = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n          this.totalRecords = response.TotalItems || 0;\n          this.initializePreSelectedItems();\n        } else {\n          this.requirements = [];\n          this.totalRecords = 0;\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.requirements = [];\n        this.totalRecords = 0;\n        console.error('載入需求資料失敗:', error);\n      }\n    });\n  }\n\n  // 搜尋功能\n  onSearch() {\n    this.currentPage = 1;\n    this.loadRequirements();\n  }\n\n  // 重置搜尋\n  onReset() {\n    this.searchFilters = {\n      CBuildCaseID: this.buildCaseId,\n      CLocation: null,\n      CRequirement: null,\n      CHouseType: null,\n      CStatus: 1,\n      CIsShow: null,\n      CIsSimple: null,\n      PageIndex: 1,\n      PageSize: 20\n    };\n    this.currentPage = 1;\n    this.loadRequirements();\n  }\n\n  // 分頁變更\n  onPageChange(page: number) {\n    this.currentPage = page;\n    this.loadRequirements();\n  }\n\n  // 切換項目選擇狀態\n  toggleItemSelection(item: ExtendedRequirementItem) {\n    if (!this.multiple) {\n      // 單選模式：取消其他項目的選擇\n      this.requirements.forEach(req => {\n        if (req.CRequirementID !== item.CRequirementID) {\n          req.selected = false;\n        }\n      });\n    }\n    item.selected = !item.selected;\n  }\n\n  // 全選/取消全選\n  toggleSelectAll(selectAll: boolean) {\n    this.requirements.forEach(item => {\n      item.selected = selectAll;\n    });\n  }\n\n  // 獲取已選擇的項目\n  getSelectedItems(): ExtendedRequirementItem[] {\n    return this.requirements.filter(item => item.selected);\n  }\n\n  // 計算總價\n  getTotalPrice(): number {\n    return this.getSelectedItems().reduce((total, item) => {\n      return total + (item.CUnitPrice || 0);\n    }, 0);\n  }\n\n  // 確認選擇\n  confirmSelection() {\n    const selectedItems = this.getSelectedItems();\n    if (selectedItems.length === 0) {\n      alert('請至少選擇一個項目');\n      return;\n    }\n\n    const config: RequirementSelectionConfig = {\n      selectedItems: selectedItems,\n      totalPrice: this.getTotalPrice(),\n      buildCaseId: this.buildCaseId\n    };\n\n    this.selectionConfirmed.emit(config);\n    this.close();\n  }\n\n  // 關閉對話框\n  close() {\n    this.selectionCancelled.emit();\n    this.dialogRef.close();\n  }\n\n  // 獲取房屋類型顯示文字\n  getHouseTypeText(houseTypes: number[] | null | undefined): string {\n    if (!houseTypes || houseTypes.length === 0) {\n      return '-';\n    }\n\n    const typeNames = houseTypes.map(type => {\n      const option = this.houseTypeOptions.find(opt => opt.value === type);\n      return option ? option.label : type.toString();\n    });\n\n    return typeNames.join(', ');\n  }\n\n  // 獲取狀態顯示文字\n  getStatusText(status: number | undefined): string {\n    return status === 1 ? '啟用' : '停用';\n  }\n\n  // 獲取是否顯示文字\n  getIsShowText(isShow: boolean | undefined): string {\n    return isShow ? '是' : '否';\n  }\n\n  // 獲取簡易客變文字\n  getIsSimpleText(isSimple: boolean | undefined): string {\n    return isSimple ? '是' : '否';\n  }\n\n  // 獲取總頁數\n  getTotalPages(): number {\n    return Math.ceil(this.totalRecords / this.pageSize);\n  }\n\n  // 檢查是否全選\n  isAllSelected(): boolean {\n    return this.requirements.length > 0 && this.requirements.every(item => item.selected);\n  }\n\n  // 檢查是否部分選擇\n  isIndeterminate(): boolean {\n    return this.requirements.some(item => item.selected) && !this.requirements.every(item => item.selected);\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAEhBC,aAAa,EACbC,cAAc,EACdC,cAAc,QACT,gBAAgB;AAqChB,IAAMC,oCAAoC,GAA1C,MAAMA,oCAAoC;EA8C/CC,YACUC,kBAAsC,EACtCC,SAA4D;IAD5D,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,SAAS,GAATA,SAAS;IA/CV,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,QAAQ,GAAY,IAAI;IACxB,KAAAC,gBAAgB,GAA8B,EAAE;IAC/C,KAAAC,kBAAkB,GAAG,IAAInB,YAAY,EAA8B;IACnE,KAAAoB,kBAAkB,GAAG,IAAIpB,YAAY,EAAQ;IAEvD;IACA,KAAAqB,YAAY,GAA8B,EAAE;IAC5C,KAAAC,SAAS,GAAY,KAAK;IAE1B;IACA,KAAAC,aAAa,GAA8B;MACzCC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,CAAC;MAAE;MACZC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED;IACA,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,QAAQ,GAAW,EAAE;IAErB;IACA,KAAAC,gBAAgB,GAAG,CACjB;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAM,CAAE,CAC5B;IAED;IACA,KAAAC,aAAa,GAAG,CACd;MAAEF,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;EAKG;EAEJE,QAAQA,CAAA;IACN,IAAI,CAACjB,aAAa,CAACC,YAAY,GAAG,IAAI,CAACR,WAAW;IAClD,IAAI,CAACyB,gBAAgB,EAAE;IACvB,IAAI,CAACC,0BAA0B,EAAE;EACnC;EAEA;EACAA,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAACxB,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACyB,MAAM,GAAG,CAAC,EAAE;MAC7D;MACA,IAAI,CAACzB,gBAAgB,CAAC0B,OAAO,CAACC,WAAW,IAAG;QAC1C,MAAMC,YAAY,GAAG,IAAI,CAACzB,YAAY,CAAC0B,IAAI,CAACC,GAAG,IAC7CA,GAAG,CAACC,cAAc,KAAKJ,WAAW,CAACI,cAAc,CAClD;QACD,IAAIH,YAAY,EAAE;UAChBA,YAAY,CAACI,QAAQ,GAAG,IAAI;QAC9B;MACF,CAAC,CAAC;IACJ;EACF;EAEA;EACAT,gBAAgBA,CAAA;IACd,IAAI,CAACnB,SAAS,GAAG,IAAI;IAErB,MAAM6B,aAAa,GAAG;MACpB,GAAG,IAAI,CAAC5B,aAAa;MACrBQ,SAAS,EAAE,IAAI,CAACG,WAAW;MAC3BF,QAAQ,EAAE,IAAI,CAACG;KAChB;IAED,IAAI,CAACrB,kBAAkB,CAACsC,8BAA8B,CAAC;MACrDC,IAAI,EAAEF;KACP,CAAC,CAACG,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAwC,IAAI;QACjD,IAAI,CAAClC,SAAS,GAAG,KAAK;QACtB,IAAIkC,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAACrC,YAAY,GAAGmC,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAChD,GAAGA,IAAI;YACPV,QAAQ,EAAE;WACX,CAAC,CAAC;UACH,IAAI,CAACjB,YAAY,GAAGuB,QAAQ,CAACK,UAAU,IAAI,CAAC;UAC5C,IAAI,CAACnB,0BAA0B,EAAE;QACnC,CAAC,MAAM;UACL,IAAI,CAACrB,YAAY,GAAG,EAAE;UACtB,IAAI,CAACY,YAAY,GAAG,CAAC;QACvB;MACF,CAAC;MACD6B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACxC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACD,YAAY,GAAG,EAAE;QACtB,IAAI,CAACY,YAAY,GAAG,CAAC;QACrB8B,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;KACD,CAAC;EACJ;EAEA;EACAE,QAAQA,CAAA;IACN,IAAI,CAAC9B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACO,gBAAgB,EAAE;EACzB;EAEA;EACAwB,OAAOA,CAAA;IACL,IAAI,CAAC1C,aAAa,GAAG;MACnBC,YAAY,EAAE,IAAI,CAACR,WAAW;MAC9BS,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IACD,IAAI,CAACE,WAAW,GAAG,CAAC;IACpB,IAAI,CAACO,gBAAgB,EAAE;EACzB;EAEA;EACAyB,YAAYA,CAACC,IAAY;IACvB,IAAI,CAACjC,WAAW,GAAGiC,IAAI;IACvB,IAAI,CAAC1B,gBAAgB,EAAE;EACzB;EAEA;EACA2B,mBAAmBA,CAACR,IAA6B;IAC/C,IAAI,CAAC,IAAI,CAAC3C,QAAQ,EAAE;MAClB;MACA,IAAI,CAACI,YAAY,CAACuB,OAAO,CAACI,GAAG,IAAG;QAC9B,IAAIA,GAAG,CAACC,cAAc,KAAKW,IAAI,CAACX,cAAc,EAAE;UAC9CD,GAAG,CAACE,QAAQ,GAAG,KAAK;QACtB;MACF,CAAC,CAAC;IACJ;IACAU,IAAI,CAACV,QAAQ,GAAG,CAACU,IAAI,CAACV,QAAQ;EAChC;EAEA;EACAmB,eAAeA,CAACC,SAAkB;IAChC,IAAI,CAACjD,YAAY,CAACuB,OAAO,CAACgB,IAAI,IAAG;MAC/BA,IAAI,CAACV,QAAQ,GAAGoB,SAAS;IAC3B,CAAC,CAAC;EACJ;EAEA;EACAC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAClD,YAAY,CAACmD,MAAM,CAACZ,IAAI,IAAIA,IAAI,CAACV,QAAQ,CAAC;EACxD;EAEA;EACAuB,aAAaA,CAAA;IACX,OAAO,IAAI,CAACF,gBAAgB,EAAE,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEf,IAAI,KAAI;MACpD,OAAOe,KAAK,IAAIf,IAAI,CAACgB,UAAU,IAAI,CAAC,CAAC;IACvC,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACAC,gBAAgBA,CAAA;IACd,MAAMC,aAAa,GAAG,IAAI,CAACP,gBAAgB,EAAE;IAC7C,IAAIO,aAAa,CAACnC,MAAM,KAAK,CAAC,EAAE;MAC9BoC,KAAK,CAAC,WAAW,CAAC;MAClB;IACF;IAEA,MAAMC,MAAM,GAA+B;MACzCF,aAAa,EAAEA,aAAa;MAC5BG,UAAU,EAAE,IAAI,CAACR,aAAa,EAAE;MAChCzD,WAAW,EAAE,IAAI,CAACA;KACnB;IAED,IAAI,CAACG,kBAAkB,CAAC+D,IAAI,CAACF,MAAM,CAAC;IACpC,IAAI,CAACG,KAAK,EAAE;EACd;EAEA;EACAA,KAAKA,CAAA;IACH,IAAI,CAAC/D,kBAAkB,CAAC8D,IAAI,EAAE;IAC9B,IAAI,CAACnE,SAAS,CAACoE,KAAK,EAAE;EACxB;EAEA;EACAC,gBAAgBA,CAACC,UAAuC;IACtD,IAAI,CAACA,UAAU,IAAIA,UAAU,CAAC1C,MAAM,KAAK,CAAC,EAAE;MAC1C,OAAO,GAAG;IACZ;IAEA,MAAM2C,SAAS,GAAGD,UAAU,CAAC1B,GAAG,CAAC4B,IAAI,IAAG;MACtC,MAAMC,MAAM,GAAG,IAAI,CAACpD,gBAAgB,CAACW,IAAI,CAAC0C,GAAG,IAAIA,GAAG,CAACpD,KAAK,KAAKkD,IAAI,CAAC;MACpE,OAAOC,MAAM,GAAGA,MAAM,CAAClD,KAAK,GAAGiD,IAAI,CAACG,QAAQ,EAAE;IAChD,CAAC,CAAC;IAEF,OAAOJ,SAAS,CAACK,IAAI,CAAC,IAAI,CAAC;EAC7B;EAEA;EACAC,aAAaA,CAACC,MAA0B;IACtC,OAAOA,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI;EACnC;EAEA;EACAC,aAAaA,CAACC,MAA2B;IACvC,OAAOA,MAAM,GAAG,GAAG,GAAG,GAAG;EAC3B;EAEA;EACAC,eAAeA,CAACC,QAA6B;IAC3C,OAAOA,QAAQ,GAAG,GAAG,GAAG,GAAG;EAC7B;EAEA;EACAC,aAAaA,CAAA;IACX,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACnE,YAAY,GAAG,IAAI,CAACE,QAAQ,CAAC;EACrD;EAEA;EACAkE,aAAaA,CAAA;IACX,OAAO,IAAI,CAAChF,YAAY,CAACsB,MAAM,GAAG,CAAC,IAAI,IAAI,CAACtB,YAAY,CAACiF,KAAK,CAAC1C,IAAI,IAAIA,IAAI,CAACV,QAAQ,CAAC;EACvF;EAEA;EACAqD,eAAeA,CAAA;IACb,OAAO,IAAI,CAAClF,YAAY,CAACmF,IAAI,CAAC5C,IAAI,IAAIA,IAAI,CAACV,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC7B,YAAY,CAACiF,KAAK,CAAC1C,IAAI,IAAIA,IAAI,CAACV,QAAQ,CAAC;EACzG;CACD;AA3OUuD,UAAA,EAARxG,KAAK,EAAE,C,wEAAyB;AACxBwG,UAAA,EAARxG,KAAK,EAAE,C,qEAA0B;AACzBwG,UAAA,EAARxG,KAAK,EAAE,C,6EAAkD;AAChDwG,UAAA,EAATvG,MAAM,EAAE,C,+EAAqE;AACpEuG,UAAA,EAATvG,MAAM,EAAE,C,+EAA+C;AAL7CU,oCAAoC,GAAA6F,UAAA,EAjBhD1G,SAAS,CAAC;EACT2G,QAAQ,EAAE,mCAAmC;EAC7CC,WAAW,EAAE,gDAAgD;EAC7DC,SAAS,EAAE,CAAC,gDAAgD,CAAC;EAC7DC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP3G,YAAY,EACZC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACbC,cAAc,EACdC,cAAc;CAEjB,CAAC,C,EACWC,oCAAoC,CA4OhD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}