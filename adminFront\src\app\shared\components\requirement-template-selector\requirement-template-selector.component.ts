import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  NbCardModule,
  NbButtonModule,
  NbIconModule,
  NbCheckboxModule,
  NbDialogRef,
  NbInputModule,
  NbSelectModule,
  NbOptionModule
} from '@nebular/theme';
import { RequirementService } from 'src/services/api/services/requirement.service';
import {
  GetListRequirementRequest,
  GetRequirement,
  GetRequirementListResponseBase
} from 'src/services/api/models';

// 擴展 API 模型以支援前端選擇功能
export interface ExtendedRequirementItem extends GetRequirement {
  selected?: boolean;
}

// 需求選擇配置介面
export interface RequirementSelectionConfig {
  selectedItems: ExtendedRequirementItem[];
  totalPrice: number;
  buildCaseId: number;
}

@Component({
  selector: 'app-requirement-template-selector',
  templateUrl: './requirement-template-selector.component.html',
  styleUrls: ['./requirement-template-selector.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbCardModule,
    NbButtonModule,
    NbIconModule,
    NbCheckboxModule,
    NbInputModule,
    NbSelectModule,
    NbOptionModule
  ]
})
export class RequirementTemplateSelectorComponent implements OnInit {
  @Input() buildCaseId: number = 0;
  @Input() multiple: boolean = true;
  @Input() preSelectedItems: ExtendedRequirementItem[] = [];
  @Output() selectionConfirmed = new EventEmitter<RequirementSelectionConfig>();
  @Output() selectionCancelled = new EventEmitter<void>();

  // 資料相關屬性
  requirements: ExtendedRequirementItem[] = [];
  isLoading: boolean = false;

  // 搜尋相關屬性
  searchFilters: GetListRequirementRequest = {
    CBuildCaseID: 0,
    CLocation: null,
    CRequirement: null,
    CHouseType: null,
    CStatus: 1, // 預設只顯示啟用的
    CIsShow: null,
    CIsSimple: null,
    PageIndex: 1,
    PageSize: 20
  };

  // 分頁相關屬性
  totalRecords: number = 0;
  currentPage: number = 1;
  pageSize: number = 20;

  // 房屋類型選項
  houseTypeOptions = [
    { value: 1, label: '套房' },
    { value: 2, label: '1房' },
    { value: 3, label: '2房' },
    { value: 4, label: '3房' },
    { value: 5, label: '4房' },
    { value: 6, label: '5房以上' }
  ];

  // 狀態選項
  statusOptions = [
    { value: null, label: '全部' },
    { value: 1, label: '啟用' },
    { value: 0, label: '停用' }
  ];

  constructor(
    private requirementService: RequirementService,
    private dialogRef: NbDialogRef<RequirementTemplateSelectorComponent>
  ) { }

  ngOnInit() {
    this.searchFilters.CBuildCaseID = this.buildCaseId;
    this.loadRequirements();
    this.initializePreSelectedItems();
  }

  // 初始化預選項目
  initializePreSelectedItems() {
    if (this.preSelectedItems && this.preSelectedItems.length > 0) {
      // 將預選項目標記為已選擇
      this.preSelectedItems.forEach(preSelected => {
        const existingItem = this.requirements.find(req =>
          req.CRequirementID === preSelected.CRequirementID
        );
        if (existingItem) {
          existingItem.selected = true;
        }
      });
    }
  }

  // 載入需求資料
  loadRequirements() {
    this.isLoading = true;

    const requestParams = {
      ...this.searchFilters,
      PageIndex: this.currentPage,
      PageSize: this.pageSize
    };

    this.requirementService.apiRequirementGetListPost$Json({
      body: requestParams
    }).subscribe({
      next: (response: GetRequirementListResponseBase) => {
        this.isLoading = false;
        if (response.StatusCode === 0 && response.Entries) {
          this.requirements = response.Entries.map(item => ({
            ...item,
            selected: false
          }));
          this.totalRecords = response.TotalItems || 0;
          this.initializePreSelectedItems();
        } else {
          this.requirements = [];
          this.totalRecords = 0;
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.requirements = [];
        this.totalRecords = 0;
        console.error('載入需求資料失敗:', error);
      }
    });
  }

  // 搜尋功能
  onSearch() {
    this.currentPage = 1;
    this.loadRequirements();
  }

  // 重置搜尋
  onReset() {
    this.searchFilters = {
      CBuildCaseID: this.buildCaseId,
      CLocation: null,
      CRequirement: null,
      CHouseType: null,
      CStatus: 1,
      CIsShow: null,
      CIsSimple: null,
      PageIndex: 1,
      PageSize: 20
    };
    this.currentPage = 1;
    this.loadRequirements();
  }

  // 分頁變更
  onPageChange(page: number) {
    this.currentPage = page;
    this.loadRequirements();
  }

  // 切換項目選擇狀態
  toggleItemSelection(item: ExtendedRequirementItem) {
    if (!this.multiple) {
      // 單選模式：取消其他項目的選擇
      this.requirements.forEach(req => {
        if (req.CRequirementID !== item.CRequirementID) {
          req.selected = false;
        }
      });
    }
    item.selected = !item.selected;
  }

  // 全選/取消全選
  toggleSelectAll(selectAll: boolean) {
    this.requirements.forEach(item => {
      item.selected = selectAll;
    });
  }

  // 獲取已選擇的項目
  getSelectedItems(): ExtendedRequirementItem[] {
    return this.requirements.filter(item => item.selected);
  }

  // 計算總價
  getTotalPrice(): number {
    return this.getSelectedItems().reduce((total, item) => {
      return total + (item.CUnitPrice || 0);
    }, 0);
  }

  // 確認選擇
  confirmSelection() {
    const selectedItems = this.getSelectedItems();
    if (selectedItems.length === 0) {
      alert('請至少選擇一個項目');
      return;
    }

    const config: RequirementSelectionConfig = {
      selectedItems: selectedItems,
      totalPrice: this.getTotalPrice(),
      buildCaseId: this.buildCaseId
    };

    this.selectionConfirmed.emit(config);
    this.close();
  }

  // 關閉對話框
  close() {
    this.selectionCancelled.emit();
    this.dialogRef.close();
  }

  // 獲取房屋類型顯示文字
  getHouseTypeText(houseTypes: number[] | null | undefined): string {
    if (!houseTypes || houseTypes.length === 0) {
      return '-';
    }

    const typeNames = houseTypes.map(type => {
      const option = this.houseTypeOptions.find(opt => opt.value === type);
      return option ? option.label : type.toString();
    });

    return typeNames.join(', ');
  }

  // 獲取狀態顯示文字
  getStatusText(status: number | undefined): string {
    return status === 1 ? '啟用' : '停用';
  }

  // 獲取是否顯示文字
  getIsShowText(isShow: boolean | undefined): string {
    return isShow ? '是' : '否';
  }

  // 獲取簡易客變文字
  getIsSimpleText(isSimple: boolean | undefined): string {
    return isSimple ? '是' : '否';
  }

  // 獲取總頁數
  getTotalPages(): number {
    return Math.ceil(this.totalRecords / this.pageSize);
  }

  // 檢查是否全選
  isAllSelected(): boolean {
    return this.requirements.length > 0 && this.requirements.every(item => item.selected);
  }

  // 檢查是否部分選擇
  isIndeterminate(): boolean {
    return this.requirements.some(item => item.selected) && !this.requirements.every(item => item.selected);
  }
}
