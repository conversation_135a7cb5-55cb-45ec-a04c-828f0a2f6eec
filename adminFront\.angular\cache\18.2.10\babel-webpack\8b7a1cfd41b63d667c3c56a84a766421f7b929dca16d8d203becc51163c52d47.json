{"ast": null, "code": "import * as moment from 'moment';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nimport * as i12 from \"../../components/file-upload/file-upload.component\";\nimport * as i13 from \"primeng/calendar\";\nimport * as i14 from \"../../../@theme/pipes/specialChangeSource.pipe\";\nconst _c0 = [\"fileInput\"];\nconst _c1 = () => [];\nfunction CustomerChangePictureComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(35);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialogUploadDrawing_r4));\n    });\n    i0.ɵɵtext(1, \" \\u4E0A\\u50B3\\u5716\\u9762\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_27_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_tr_27_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(35);\n      return i0.ɵɵresetView(ctx_r2.onEdit(dialogUploadDrawing_r4, item_r6));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 17)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"specialChangeSource\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 18);\n    i0.ɵɵtemplate(13, CustomerChangePictureComponent_tr_27_button_13_Template, 2, 0, \"button\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 6, item_r6.CSource));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CChangeDate));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CDrawingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CCreateDT));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CIsApprove == null ? \"\\u5F85\\u5BE9\\u6838\" : item_r6.CIsApprove ? \"\\u901A\\u904E\" : \"\\u99C1\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_34_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 32);\n    i0.ɵɵtext(2, \" \\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"app-file-upload\", 33);\n    i0.ɵɵlistener(\"multiFileSelected\", function CustomerChangePictureComponent_ng_template_34_div_13_Template_app_file_upload_multiFileSelected_3_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onMultiFileSelected($event));\n    })(\"nameAutoFilled\", function CustomerChangePictureComponent_ng_template_34_div_13_Template_app_file_upload_nameAutoFilled_3_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onNameAutoFilled($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"config\", ctx_r2.fileUploadConfig)(\"fileList\", ctx_r2.imageUrlList)(\"existingFiles\", ctx_r2.isEdit && ctx_r2.SpecialChange && ctx_r2.SpecialChange.CFileRes ? ctx_r2.SpecialChange.CFileRes : i0.ɵɵpureFunction0(3, _c1));\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const file_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openPdfInNewTab(file_r10));\n    });\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵelementStart(2, \"span\", 44);\n    i0.ɵɵtext(3, \"PDF\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"img\", 46);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_3_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const file_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openPdfInNewTab(file_r10));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 47);\n    i0.ɵɵelement(3, \"i\", 48);\n    i0.ɵɵtext(4, \"\\u5716\\u7247 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", file_r10.CFile, i0.ɵɵsanitizeUrl)(\"title\", \"\\u9EDE\\u64CA\\u6AA2\\u8996: \" + file_r10.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const file_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openPdfInNewTab(file_r10));\n    });\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵelementStart(2, \"span\", 51);\n    i0.ɵɵtext(3, \"CAD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_34_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37);\n    i0.ɵɵtemplate(2, CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_2_Template, 4, 0, \"div\", 38)(3, CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_3_Template, 5, 2, \"div\", 39)(4, CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_4_Template, 4, 0, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 41);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isPdfFile(file_r10.CFileName));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isImageFile(file_r10.CFileName));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isCadFile(file_r10.CFileName));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r10.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_34_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 32);\n    i0.ɵɵtext(2, \" \\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34);\n    i0.ɵɵtemplate(4, CustomerChangePictureComponent_ng_template_34_div_14_div_4_Template, 7, 4, \"div\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.SpecialChange.CFileRes);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_34_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"label\", 53);\n    i0.ɵɵtext(2, \"\\u5BE9\\u6838\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"textarea\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_34_div_15_Template_textarea_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CApproveRemark, $event) || (ctx_r2.formSpecialChange.CApproveRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CApproveRemark);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_34_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_34_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ref_r14 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSaveSpecialChange(ref_r14));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\\u5BE9\\u6838\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 21)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 22)(4, \"div\", 23)(5, \"label\", 24, 1);\n    i0.ɵɵtext(7, \" \\u8A0E\\u8AD6\\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-calendar\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_34_Template_p_calendar_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CChangeDate, $event) || (ctx_r2.formSpecialChange.CChangeDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 23)(10, \"label\", 26);\n    i0.ɵɵtext(11, \" \\u5716\\u9762\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_34_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CDrawingName, $event) || (ctx_r2.formSpecialChange.CDrawingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, CustomerChangePictureComponent_ng_template_34_div_13_Template, 4, 4, \"div\", 28)(14, CustomerChangePictureComponent_ng_template_34_div_14_Template, 5, 1, \"div\", 28)(15, CustomerChangePictureComponent_ng_template_34_div_15_Template, 4, 2, \"div\", 29);\n    i0.ɵɵelementStart(16, \"div\", 13)(17, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_34_Template_button_click_17_listener() {\n      const ref_r14 = i0.ɵɵrestoreView(_r7).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r14));\n    });\n    i0.ɵɵtext(18, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, CustomerChangePictureComponent_ng_template_34_button_19_Template, 2, 0, \"button\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u6D3D\\u8AC7\\u7D00\\u9304\\u4E0A\\u50B3 > \", ctx_r2.house.CHousehold, \" \\u00A0 \", ctx_r2.house.CFloor, \"F \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"appendTo\", \"CChangeDate\")(\"iconDisplay\", \"input\")(\"showIcon\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CChangeDate);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit)(\"showButtonBar\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CDrawingName);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEdit && ctx_r2.SpecialChange && ctx_r2.SpecialChange.CFileRes && ctx_r2.SpecialChange.CFileRes.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isFromFrontend && !ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit && !ctx_r2.isFromFrontend);\n  }\n}\nexport class CustomerChangePictureComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _specialChangeService, _houseService, route, message, location, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._specialChangeService = _specialChangeService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.message = message;\n    this.location = location;\n    this._eventService = _eventService;\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.fileUploadConfig = {\n      acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'],\n      acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\n      acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\n      label: '',\n      helpText: '支援格式：圖片 (JPG, JPEG)、PDF、CAD (DWG, DXF)',\n      required: false,\n      disabled: false,\n      autoFillName: true,\n      buttonText: '選擇檔案',\n      buttonIcon: 'fa fa-upload',\n      maxFileSize: 10,\n      multiple: true,\n      showPreview: true\n    };\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.listPictures = [];\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id1');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        const idParam2 = params.get('id2');\n        const id2 = idParam2 ? +idParam2 : 0;\n        this.houseId = id2;\n        this.getListSpecialChange();\n        this.getHouseById();\n      }\n    });\n    // 動態更新配置\n    this.updateFileUploadConfig();\n  }\n  updateFileUploadConfig() {\n    this.fileUploadConfig = {\n      ...this.fileUploadConfig,\n      disabled: this.isEdit && this.SpecialChange?.CIsApprove === null || this.isFromFrontend\n    };\n  }\n  // 判斷是否為前台來源\n  get isFromFrontend() {\n    return this.SpecialChange?.CSource === 2;\n  }\n  openPdfInNewTab(data) {\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\n  }\n  getListSpecialChange() {\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\n      body: {\n        CHouseId: this.houseId,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.listSpecialChange = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  getHouseById() {\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: this.houseId\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.house = res.Entries;\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`;\n      }\n    });\n  }\n  getSpecialChangeById(ref, CSpecialChangeID) {\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({\n      body: CSpecialChangeID\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.SpecialChange = res.Entries;\n        this.formSpecialChange = {\n          CApproveRemark: this.SpecialChange.CApproveRemark,\n          CBuildCaseID: this.buildCaseId,\n          CDrawingName: this.SpecialChange.CDrawingName,\n          CHouseID: this.houseId,\n          SpecialChangeFiles: null\n        };\n        if (this.SpecialChange.CChangeDate) {\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\n        }\n        // 載入資料後更新檔案上傳配置\n        this.updateFileUploadConfig();\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  onSaveSpecialChange(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({\n      body: this.formatParam()\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getListSpecialChange();\n        ref.close();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListSpecialChange();\n  }\n  addNew(ref) {\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.updateFileUploadConfig();\n    this.formSpecialChange = {\n      CApproveRemark: '',\n      CBuildCaseID: this.buildCaseId,\n      CChangeDate: '',\n      CDrawingName: '',\n      CHouseID: this.houseId,\n      SpecialChangeFiles: null\n    };\n    this.dialogService.open(ref);\n  }\n  onEdit(ref, specialChange) {\n    this.imageUrlList = [];\n    this.isEdit = true;\n    this.updateFileUploadConfig();\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID);\n  }\n  onMultiFileSelected(files) {\n    this.imageUrlList = files;\n  }\n  onNameAutoFilled(name) {\n    if (!this.formSpecialChange.CDrawingName) {\n      this.formSpecialChange.CDrawingName = name;\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate);\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName);\n    // 前台來源時不需要驗證審核說明\n    if (!this.isFromFrontend) {\n      this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DD');\n    }\n    return '';\n  }\n  deleteDataFields(array) {\n    for (const item of array) {\n      delete item.data;\n    }\n    return array;\n  }\n  formatParam() {\n    const result = {\n      ...this.formSpecialChange,\n      SpecialChangeFiles: this.imageUrlList\n    };\n    this.deleteDataFields(result.SpecialChangeFiles);\n    if (this.formSpecialChange.CChangeDate) {\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate);\n    }\n    return result;\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {}\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  openNewTab(url) {\n    if (url) window.open(url, \"_blank\");\n  }\n  static {\n    this.ɵfac = function CustomerChangePictureComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerChangePictureComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.SpecialChangeService), i0.ɵɵdirectiveInject(i4.HouseService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomerChangePictureComponent,\n      selectors: [[\"ngx-customer-change-picture\"]],\n      viewQuery: function CustomerChangePictureComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 36,\n      vars: 6,\n      consts: [[\"dialogUploadDrawing\", \"\"], [\"CChangeDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"min-width\", \"600px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"CChangeDate\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"inputId\", \"icondisplay\", \"dateFormat\", \"yy/mm/dd\", 1, \"!w-[400px]\", 3, \"ngModelChange\", \"appendTo\", \"iconDisplay\", \"showIcon\", \"ngModel\", \"disabled\", \"showButtonBar\"], [\"for\", \"cDrawingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5716\\u9762\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"form-group\", 4, \"ngIf\"], [\"class\", \"form-group d-flex align-items-center\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-success m-2\", 3, \"click\", 4, \"ngIf\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"labelMinWidth\", \"0px\", 3, \"multiFileSelected\", \"nameAutoFilled\", \"config\", \"fileList\", \"existingFiles\"], [1, \"flex\", \"flex-wrap\", \"mt-2\", \"file-type-container\"], [\"class\", \"relative w-28 h-28 mr-3 mb-6 file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"relative\", \"w-28\", \"h-28\", \"mr-3\", \"mb-6\", \"file-item\"], [1, \"w-full\", \"h-full\", \"border\", \"border-gray-300\", \"rounded-lg\", \"shadow-sm\", \"bg-white\", \"hover:shadow-md\", \"transition-shadow\", \"duration-200\"], [\"class\", \"w-full h-full flex flex-col items-center justify-center bg-red-50 rounded-lg cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"w-full h-full relative rounded-lg overflow-hidden\", 4, \"ngIf\"], [\"class\", \"w-full h-full flex flex-col items-center justify-center bg-green-50 rounded-lg cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [1, \"absolute\", \"-bottom-4\", \"left-0\", \"w-full\", \"text-xs\", \"truncate\", \"px-1\", \"text-center\"], [1, \"w-full\", \"h-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"bg-red-50\", \"rounded-lg\", \"cursor-pointer\", 3, \"click\"], [1, \"fa\", \"fa-file-pdf\", \"text-red-500\", \"text-2xl\", \"mb-1\"], [1, \"text-xs\", \"text-red-600\", \"font-medium\"], [1, \"w-full\", \"h-full\", \"relative\", \"rounded-lg\", \"overflow-hidden\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"cursor-pointer\", 3, \"click\", \"src\", \"title\"], [1, \"absolute\", \"top-1\", \"left-1\", \"bg-blue-500\", \"text-white\", \"text-xs\", \"px-1\", \"py-0.5\", \"rounded\"], [1, \"fa\", \"fa-image\", \"mr-1\"], [1, \"w-full\", \"h-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"bg-green-50\", \"rounded-lg\", \"cursor-pointer\", 3, \"click\"], [1, \"fa\", \"fa-file\", \"text-green-500\", \"text-2xl\", \"mb-1\"], [1, \"text-xs\", \"text-green-600\", \"font-medium\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cApproveRemark\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"name\", \"remark\", \"id\", \"cApproveRemark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"]],\n      template: function CustomerChangePictureComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 3);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u4E0A\\u50B3\\u8207\\u8A72\\u6236\\u5225\\u5BA2\\u6236\\u8A0E\\u8AD6\\u7684\\u5BA2\\u6236\\u5716\\u9762\\uFF0C\\u5BE9\\u6838\\u901A\\u904E\\u5F8C\\u5BA2\\u6236\\u5C31\\u53EF\\u4EE5\\u5728\\u524D\\u53F0\\u6AA2\\u8996\\u8A72\\u5716\\u9762\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 5)(8, \"div\", 6);\n          i0.ɵɵtemplate(9, CustomerChangePictureComponent_button_9_Template, 2, 0, \"button\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"table\", 9)(12, \"thead\")(13, \"tr\", 10)(14, \"th\", 11);\n          i0.ɵɵtext(15, \"\\u4F86\\u6E90\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"th\", 11);\n          i0.ɵɵtext(17, \"\\u8A0E\\u8AD6\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"th\", 11);\n          i0.ɵɵtext(19, \"\\u5716\\u9762\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"th\", 11);\n          i0.ɵɵtext(21, \"\\u4E0A\\u50B3\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"th\", 11);\n          i0.ɵɵtext(23, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"th\", 11);\n          i0.ɵɵtext(25, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"tbody\");\n          i0.ɵɵtemplate(27, CustomerChangePictureComponent_tr_27_Template, 14, 8, \"tr\", 12);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"nb-card-footer\", 13)(29, \"ngb-pagination\", 14);\n          i0.ɵɵtwoWayListener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_29_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_29_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"nb-card-footer\")(31, \"div\", 13)(32, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_Template_button_click_32_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goBack());\n          });\n          i0.ɵɵtext(33, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(34, CustomerChangePictureComponent_ng_template_34_Template, 20, 14, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u6D3D\\u8AC7\\u7D00\\u9304\\u4E0A\\u50B3 > \", ctx.houseTitle, \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listSpecialChange);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i10.NgbPagination, i11.BaseLabelDirective, i12.FileUploadComponent, i13.Calendar, i14.SpecialChangeSourcePipe],\n      styles: [\"#icondisplay {\\n  width: 318px;\\n}\\n\\n  [id^=pn_id_] {\\n  z-index: 10;\\n}\\n\\n.file-type-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out;\\n}\\n.file-type-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n.file-type-container[_ngcontent-%COMP%]   .file-type-label[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n  background-color: rgba(0, 0, 0, 0.7);\\n}\\n.file-type-container[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out;\\n}\\n.file-type-container[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.pdf-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.7;\\n  }\\n}\\n.cad-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.cad-icon[_ngcontent-%COMP%]:hover {\\n  transform: rotate(15deg);\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksWUFBQTtBQUNKOztBQUVBO0VBQ0ksV0FBQTtBQUNKOztBQUlJO0VBQ0ksZ0NBQUE7QUFEUjtBQUdRO0VBQ0ksMkJBQUE7QUFEWjtBQUtJO0VBQ0ksa0NBQUE7VUFBQSwwQkFBQTtFQUNBLG9DQUFBO0FBSFI7QUFNSTtFQUNJLGdDQUFBO0FBSlI7QUFNUTtFQUNJLHFCQUFBO0FBSlo7O0FBVUE7RUFDSSw0QkFBQTtBQVBKOztBQVVBO0VBRUk7SUFFSSxVQUFBO0VBVE47RUFZRTtJQUNJLFlBQUE7RUFWTjtBQUNGO0FBY0E7RUFDSSwrQkFBQTtBQVpKO0FBY0k7RUFDSSx3QkFBQTtBQVpSIiwiZmlsZSI6ImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwICNpY29uZGlzcGxheSB7XHJcbiAgICB3aWR0aDogMzE4cHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCBbaWRePVwicG5faWRfXCJdIHtcclxuICAgIHotaW5kZXg6IDEwO1xyXG59XHJcblxyXG4vLyDmqpTmoYjpoZ7lnovpoa/npLrmqKPlvI9cclxuLmZpbGUtdHlwZS1jb250YWluZXIge1xyXG4gICAgLmZpbGUtaXRlbSB7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbi1vdXQ7XHJcblxyXG4gICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5maWxlLXR5cGUtbGFiZWwge1xyXG4gICAgICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cig0cHgpO1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMC43KTtcclxuICAgIH1cclxuXHJcbiAgICAucmVtb3ZlLWJ0biB7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbi1vdXQ7XHJcblxyXG4gICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4vLyBQREbmlofku7blnJbmqJnli5XnlatcclxuLnBkZi1pY29uIHtcclxuICAgIGFuaW1hdGlvbjogcHVsc2UgMnMgaW5maW5pdGU7XHJcbn1cclxuXHJcbkBrZXlmcmFtZXMgcHVsc2Uge1xyXG5cclxuICAgIDAlLFxyXG4gICAgMTAwJSB7XHJcbiAgICAgICAgb3BhY2l0eTogMTtcclxuICAgIH1cclxuXHJcbiAgICA1MCUge1xyXG4gICAgICAgIG9wYWNpdHk6IDAuNztcclxuICAgIH1cclxufVxyXG5cclxuLy8gQ0FE5paH5Lu25ZyW5qiZ5peL6L2J5pWI5p6cXHJcbi5jYWQtaWNvbiB7XHJcbiAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICAgIHRyYW5zZm9ybTogcm90YXRlKDE1ZGVnKTtcclxuICAgIH1cclxufVxyXG4iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["moment", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵlistener", "CustomerChangePictureComponent_button_9_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "dialogUploadDrawing_r4", "ɵɵreference", "ɵɵresetView", "addNew", "ɵɵtext", "ɵɵelementEnd", "CustomerChangePictureComponent_tr_27_button_13_Template_button_click_0_listener", "_r5", "item_r6", "$implicit", "onEdit", "ɵɵtemplate", "CustomerChangePictureComponent_tr_27_button_13_Template", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "CSource", "formatDate", "CChangeDate", "CDrawingName", "CCreateDT", "CIsApprove", "ɵɵproperty", "isUpdate", "CustomerChangePictureComponent_ng_template_34_div_13_Template_app_file_upload_multiFileSelected_3_listener", "$event", "_r8", "onMultiFileSelected", "CustomerChangePictureComponent_ng_template_34_div_13_Template_app_file_upload_nameAutoFilled_3_listener", "onNameAutoFilled", "fileUploadConfig", "imageUrlList", "isEdit", "SpecialChange", "CFileRes", "ɵɵpureFunction0", "_c1", "CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_2_Template_div_click_0_listener", "_r9", "file_r10", "openPdfInNewTab", "ɵɵelement", "CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_3_Template_img_click_1_listener", "_r11", "CFile", "ɵɵsanitizeUrl", "CFileName", "CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_4_Template_div_click_0_listener", "_r12", "CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_2_Template", "CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_3_Template", "CustomerChangePictureComponent_ng_template_34_div_14_div_4_div_4_Template", "isPdfFile", "isImageFile", "isCadFile", "CustomerChangePictureComponent_ng_template_34_div_14_div_4_Template", "ɵɵtwoWayListener", "CustomerChangePictureComponent_ng_template_34_div_15_Template_textarea_ngModelChange_3_listener", "_r13", "ɵɵtwoWayBindingSet", "formSpecialChange", "CApproveRemark", "ɵɵtwoWayProperty", "CustomerChangePictureComponent_ng_template_34_button_19_Template_button_click_0_listener", "_r15", "ref_r14", "dialogRef", "onSaveSpecialChange", "CustomerChangePictureComponent_ng_template_34_Template_p_calendar_ngModelChange_8_listener", "_r7", "CustomerChangePictureComponent_ng_template_34_Template_input_ngModelChange_12_listener", "CustomerChangePictureComponent_ng_template_34_div_13_Template", "CustomerChangePictureComponent_ng_template_34_div_14_Template", "CustomerChangePictureComponent_ng_template_34_div_15_Template", "CustomerChangePictureComponent_ng_template_34_Template_button_click_17_listener", "onClose", "CustomerChangePictureComponent_ng_template_34_button_19_Template", "ɵɵtextInterpolate2", "house", "CHousehold", "CFloor", "length", "isFromFrontend", "CustomerChangePictureComponent", "constructor", "_allow", "dialogService", "valid", "_specialChangeService", "_houseService", "route", "message", "location", "_eventService", "acceptedTypes", "acceptedFileRegex", "acceptAttribute", "label", "helpText", "required", "disabled", "autoFillName", "buttonText", "buttonIcon", "maxFileSize", "multiple", "showPreview", "statusOptions", "value", "key", "pageFirst", "pageSize", "pageIndex", "totalRecords", "listPictures", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "idParam2", "id2", "houseId", "getListSpecialChange", "getHouseById", "updateFileUploadConfig", "data", "window", "open", "apiSpecialChangeGetListSpecialChangePost$Json", "body", "CHouseId", "PageIndex", "PageSize", "res", "TotalItems", "Entries", "StatusCode", "listSpecialChange", "apiHouseGetHouseByIdPost$Json", "CHouseID", "houseTitle", "getSpecialChangeById", "ref", "CSpecialChangeID", "apiSpecialChangeGetSpecialChangeByIdPost$Json", "CBuildCaseID", "SpecialChangeFiles", "Date", "validation", "errorMessages", "showErrorMSGs", "apiSpecialChangeSaveSpecialChangePost$Json", "formatParam", "showSucessMSG", "close", "pageChanged", "newPage", "specialChange", "files", "name", "clear", "format", "deleteDataFields", "array", "item", "result", "removeBase64Prefix", "base64String", "prefixIndex", "indexOf", "substring", "removeImage", "pictureId", "filter", "x", "uploadImage", "renameFile", "event", "index", "blob", "slice", "size", "type", "newFile", "File", "target", "extension", "goBack", "push", "action", "payload", "back", "openNewTab", "url", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "SpecialChangeService", "HouseService", "i5", "ActivatedRoute", "i6", "MessageService", "i7", "Location", "i8", "EventService", "selectors", "viewQuery", "CustomerChangePictureComponent_Query", "rf", "ctx", "CustomerChangePictureComponent_button_9_Template", "CustomerChangePictureComponent_tr_27_Template", "CustomerChangePictureComponent_Template_ngb_pagination_pageChange_29_listener", "_r1", "CustomerChangePictureComponent_Template_button_click_32_listener", "CustomerChangePictureComponent_ng_template_34_Template", "ɵɵtemplateRefExtractor", "ɵɵtextInterpolate1", "isCreate"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { HouseService, SpecialChangeService } from 'src/services/api/services';\r\nimport { TblHouse, SpecialChangeRes } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport * as moment from 'moment';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { MultiFileUploadResult, FileUploadConfig } from '../../components/file-upload/file-upload.component';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-customer-change-picture',\r\n  templateUrl: './customer-change-picture.component.html',\r\n  styleUrls: ['./customer-change-picture.component.scss'],\r\n})\r\n\r\nexport class CustomerChangePictureComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _houseService: HouseService,\r\n\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private location: Location,\r\n    private _eventService: EventService\r\n  ) { super(_allow) }\r\n  @ViewChild('fileInput') fileInput: ElementRef;\r\n  imageUrlList: MultiFileUploadResult[] = [];\r\n  isEdit = false;\r\n\r\n  fileUploadConfig: FileUploadConfig = {\r\n    acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'],\r\n    acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\r\n    acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\r\n    label: '',\r\n    helpText: '支援格式：圖片 (JPG, JPEG)、PDF、CAD (DWG, DXF)',\r\n    required: false,\r\n    disabled: false,\r\n    autoFillName: true,\r\n    buttonText: '選擇檔案',\r\n    buttonIcon: 'fa fa-upload',\r\n    maxFileSize: 10,\r\n    multiple: true,\r\n    showPreview: true\r\n  };\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  selectedBuildCase: selectItem\r\n\r\n  buildCaseId: number\r\n  houseId: number\r\n  house: TblHouse\r\n  houseTitle: string\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.houseId = id2\r\n        this.getListSpecialChange()\r\n        this.getHouseById()\r\n      }\r\n    });\r\n\r\n    // 動態更新配置\r\n    this.updateFileUploadConfig();\r\n  }\r\n\r\n  updateFileUploadConfig() {\r\n    this.fileUploadConfig = {\r\n      ...this.fileUploadConfig,\r\n      disabled: (this.isEdit && this.SpecialChange?.CIsApprove === null) || this.isFromFrontend\r\n    };\r\n  }\r\n\r\n  // 判斷是否為前台來源\r\n  get isFromFrontend(): boolean {\r\n    return this.SpecialChange?.CSource === 2;\r\n  }\r\n\r\n\r\n  openPdfInNewTab(data?: any) {\r\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\r\n  }\r\n\r\n\r\n  listSpecialChange: any[]\r\n\r\n  getListSpecialChange() {\r\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\r\n      body: {\r\n        CHouseId: this.houseId,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChange = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems;\r\n      }\r\n    })\r\n  }\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: {\r\n        CHouseID: this.houseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.house = res.Entries\r\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`\r\n      }\r\n    })\r\n  }\r\n\r\n  SpecialChange: SpecialChangeRes\r\n  fileUrl: any\r\n  getSpecialChangeById(ref: any, CSpecialChangeID: any) {\r\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({ body: CSpecialChangeID }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.SpecialChange = res.Entries\r\n        this.formSpecialChange = {\r\n          CApproveRemark: this.SpecialChange.CApproveRemark,\r\n          CBuildCaseID: this.buildCaseId,\r\n          CDrawingName: this.SpecialChange.CDrawingName,\r\n          CHouseID: this.houseId,\r\n          SpecialChangeFiles: null\r\n        }\r\n        if (this.SpecialChange.CChangeDate) {\r\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\r\n        }\r\n        // 載入資料後更新檔案上傳配置\r\n        this.updateFileUploadConfig();\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  formSpecialChange: any\r\n\r\n  onSaveSpecialChange(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({ body: this.formatParam() }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListSpecialChange()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListSpecialChange();\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = false;\r\n    this.updateFileUploadConfig();\r\n    this.formSpecialChange = {\r\n      CApproveRemark: '',\r\n      CBuildCaseID: this.buildCaseId,\r\n      CChangeDate: '',\r\n      CDrawingName: '',\r\n      CHouseID: this.houseId,\r\n      SpecialChangeFiles: null\r\n    }\r\n    this.dialogService.open(ref);\r\n  }\r\n\r\n  onEdit(ref: any, specialChange: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = true;\r\n    this.updateFileUploadConfig();\r\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID);\r\n  }\r\n\r\n  onMultiFileSelected(files: MultiFileUploadResult[]) {\r\n    this.imageUrlList = files;\r\n  }\r\n\r\n  onNameAutoFilled(name: string) {\r\n    if (!this.formSpecialChange.CDrawingName) {\r\n      this.formSpecialChange.CDrawingName = name;\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate)\r\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName)\r\n    // 前台來源時不需要驗證審核說明\r\n    if (!this.isFromFrontend) {\r\n      this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DD');\r\n    }\r\n    return ''\r\n  }\r\n\r\n  deleteDataFields(array: any[]) {\r\n    for (const item of array) {\r\n      delete item.data;\r\n    }\r\n    return array;\r\n  }\r\n\r\n  formatParam() {\r\n    const result = {\r\n      ...this.formSpecialChange,\r\n      SpecialChangeFiles: this.imageUrlList\r\n    }\r\n    this.deleteDataFields(result.SpecialChangeFiles)\r\n\r\n    if (this.formSpecialChange.CChangeDate) {\r\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate)\r\n    }\r\n    return result\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  listPictures: any[] = []\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  openNewTab(url: any) {\r\n    if (url) window.open(url, \"_blank\");\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    戶別管理 > 洽談紀錄上傳 > {{houseTitle}}\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此上傳與該戶別客戶討論的客戶圖面，審核通過後客戶就可以在前台檢視該圖面。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialogUploadDrawing)\" *ngIf=\"isCreate\">\r\n            上傳圖面</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n            <th scope=\"col\" class=\"col-1\">來源</th>\r\n            <th scope=\"col\" class=\"col-1\">討論日期</th>\r\n            <th scope=\"col\" class=\"col-1\">圖面名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">上傳日期</th>\r\n            <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listSpecialChange ; let i = index\" class=\"text-center\">\r\n            <td>{{ item.CSource| specialChangeSource }}</td>\r\n            <td>{{ formatDate(item.CChangeDate)}}</td>\r\n            <td>{{ item.CDrawingName}}</td>\r\n            <td>{{formatDate(item.CCreateDT)}}</td>\r\n            <td>{{ item.CIsApprove == null ? '待審核' : ( item.CIsApprove ? \"通過\" : \"駁回\")}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isUpdate\"\r\n                (click)=\"onEdit(dialogUploadDrawing, item)\">檢視</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\" (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialogUploadDrawing let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"min-width:600px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 > 洽談紀錄上傳 > {{house.CHousehold}} &nbsp; {{house.CFloor}}F\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"CChangeDate\" #CChangeDate class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          討論日期\r\n        </label>\r\n        <p-calendar [appendTo]=\"'CChangeDate'\" placeholder=\"年/月/日\" [iconDisplay]=\"'input'\" [showIcon]=\"true\"\r\n          inputId=\"icondisplay\" dateFormat=\"yy/mm/dd\" [(ngModel)]=\"formSpecialChange.CChangeDate\" [disabled]=\"isEdit\"\r\n          [showButtonBar]=\"true\" class=\"!w-[400px]\"></p-calendar>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cDrawingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          圖面名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"圖面名稱\" [(ngModel)]=\"formSpecialChange.CDrawingName\"\r\n          [disabled]=\"isEdit\" />\r\n      </div>\r\n      <div class=\"form-group\" *ngIf=\"!isEdit\">\r\n        <label for=\"cIsEnable\" class=\" mr-4\" style=\"min-width:75px\" baseLabel>\r\n          選樣結果\r\n        </label>\r\n        <app-file-upload [config]=\"fileUploadConfig\" [fileList]=\"imageUrlList\"\r\n          [existingFiles]=\"isEdit && SpecialChange && SpecialChange.CFileRes ? SpecialChange.CFileRes : []\"\r\n          (multiFileSelected)=\"onMultiFileSelected($event)\" (nameAutoFilled)=\"onNameAutoFilled($event)\"\r\n          labelMinWidth=\"0px\">\r\n        </app-file-upload>\r\n      </div>\r\n      <!-- 檢視模式：只顯示現有檔案 -->\r\n      <div class=\"form-group\"\r\n        *ngIf=\"isEdit && SpecialChange && SpecialChange.CFileRes && SpecialChange.CFileRes.length > 0\">\r\n        <label for=\"cIsEnable\" class=\" mr-4\" style=\"min-width:75px\" baseLabel>\r\n          選樣結果\r\n        </label>\r\n        <div class=\"flex flex-wrap mt-2 file-type-container\">\r\n          <div *ngFor=\"let file of SpecialChange.CFileRes; let i = index\"\r\n            class=\"relative w-28 h-28 mr-3 mb-6 file-item\">\r\n            <div\r\n              class=\"w-full h-full border border-gray-300 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200\">\r\n              <!-- PDF 類型 -->\r\n              <div *ngIf=\"isPdfFile(file.CFileName)\"\r\n                class=\"w-full h-full flex flex-col items-center justify-center bg-red-50 rounded-lg cursor-pointer\"\r\n                (click)=\"openPdfInNewTab(file)\">\r\n                <i class=\"fa fa-file-pdf text-red-500 text-2xl mb-1\"></i>\r\n                <span class=\"text-xs text-red-600 font-medium\">PDF</span>\r\n              </div>\r\n              <!-- 圖片類型 -->\r\n              <div *ngIf=\"isImageFile(file.CFileName)\" class=\"w-full h-full relative rounded-lg overflow-hidden\">\r\n                <img class=\"w-full h-full object-cover cursor-pointer\" [src]=\"file.CFile\"\r\n                  (click)=\"openPdfInNewTab(file)\" [title]=\"'點擊檢視: ' + file.CFileName\">\r\n                <div class=\"absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded\">\r\n                  <i class=\"fa fa-image mr-1\"></i>圖片\r\n                </div>\r\n              </div>\r\n              <!-- CAD 類型 -->\r\n              <div *ngIf=\"isCadFile(file.CFileName)\"\r\n                class=\"w-full h-full flex flex-col items-center justify-center bg-green-50 rounded-lg cursor-pointer\"\r\n                (click)=\"openPdfInNewTab(file)\">\r\n                <i class=\"fa fa-file text-green-500 text-2xl mb-1\"></i>\r\n                <span class=\"text-xs text-green-600 font-medium\">CAD</span>\r\n              </div>\r\n            </div>\r\n            <p class=\"absolute -bottom-4 left-0 w-full text-xs truncate px-1 text-center\">{{ file.CFileName }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\" *ngIf=\"!isFromFrontend && !isEdit\">\r\n        <label for=\"cApproveRemark\" baseLabel class=\"required-field align-self-start mr-4\"\r\n          style=\"min-width:75px\">審核說明</label>\r\n        <textarea name=\"remark\" id=\"cApproveRemark\" rows=\"5\" nbInput style=\"resize: none;\" class=\"w-full\"\r\n          [disabled]=\"isEdit\" class=\"w-full\" [(ngModel)]=\"formSpecialChange.CApproveRemark\"></textarea>\r\n      </div>\r\n\r\n      <div class=\"d-flex justify-content-center\">\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <button class=\"btn btn-success m-2\" *ngIf=\"!isEdit && !isFromFrontend\"\r\n          (click)=\"onSaveSpecialChange(ref)\">送出審核</button>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AASA,OAAO,KAAKA,MAAM,MAAM,QAAQ;AAChC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;;;;;ICAlEC,EAAA,CAAAC,cAAA,iBAAoF;IAAvDD,EAAA,CAAAE,UAAA,mBAAAC,yEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,sBAAA,CAA2B;IAAA,EAAC;IAChER,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAyBXb,EAAA,CAAAC,cAAA,iBAC8C;IAA5CD,EAAA,CAAAE,UAAA,mBAAAY,gFAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAW,GAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAY,MAAA,CAAAV,sBAAA,EAAAQ,OAAA,CAAiC;IAAA,EAAC;IAAChB,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;IAP3Db,EADF,CAAAC,cAAA,aAA+E,SACzE;IAAAD,EAAA,CAAAY,MAAA,GAAuC;;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAChDb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAiC;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC1Cb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC/Bb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAA8B;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACvCb,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAY,MAAA,IAAuE;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAChFb,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAmB,UAAA,KAAAC,uDAAA,qBAC8C;IAElDpB,EADE,CAAAa,YAAA,EAAK,EACF;;;;;IATCb,EAAA,CAAAqB,SAAA,GAAuC;IAAvCrB,EAAA,CAAAsB,iBAAA,CAAAtB,EAAA,CAAAuB,WAAA,OAAAP,OAAA,CAAAQ,OAAA,EAAuC;IACvCxB,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAmB,UAAA,CAAAT,OAAA,CAAAU,WAAA,EAAiC;IACjC1B,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAW,YAAA,CAAsB;IACtB3B,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAmB,UAAA,CAAAT,OAAA,CAAAY,SAAA,EAA8B;IAC9B5B,EAAA,CAAAqB,SAAA,GAAuE;IAAvErB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAa,UAAA,kCAAAb,OAAA,CAAAa,UAAA,mCAAuE;IAErB7B,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAyB,QAAA,CAAc;;;;;;IA8CxE/B,EADF,CAAAC,cAAA,cAAwC,gBACgC;IACpED,EAAA,CAAAY,MAAA,iCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,0BAGsB;IAD8BD,EAAlD,CAAAE,UAAA,+BAAA8B,2GAAAC,MAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAqBJ,MAAA,CAAA6B,mBAAA,CAAAF,MAAA,CAA2B;IAAA,EAAC,4BAAAG,wGAAAH,MAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAmBJ,MAAA,CAAA+B,gBAAA,CAAAJ,MAAA,CAAwB;IAAA,EAAC;IAGjGjC,EADE,CAAAa,YAAA,EAAkB,EACd;;;;IALab,EAAA,CAAAqB,SAAA,GAA2B;IAC1CrB,EADe,CAAA8B,UAAA,WAAAxB,MAAA,CAAAgC,gBAAA,CAA2B,aAAAhC,MAAA,CAAAiC,YAAA,CAA0B,kBAAAjC,MAAA,CAAAkC,MAAA,IAAAlC,MAAA,CAAAmC,aAAA,IAAAnC,MAAA,CAAAmC,aAAA,CAAAC,QAAA,GAAApC,MAAA,CAAAmC,aAAA,CAAAC,QAAA,GAAA1C,EAAA,CAAA2C,eAAA,IAAAC,GAAA,EAC6B;;;;;;IAiB7F5C,EAAA,CAAAC,cAAA,cAEkC;IAAhCD,EAAA,CAAAE,UAAA,mBAAA2C,+FAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAA0C,GAAA;MAAA,MAAAC,QAAA,GAAA/C,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA0C,eAAA,CAAAD,QAAA,CAAqB;IAAA,EAAC;IAC/B/C,EAAA,CAAAiD,SAAA,YAAyD;IACzDjD,EAAA,CAAAC,cAAA,eAA+C;IAAAD,EAAA,CAAAY,MAAA,UAAG;IACpDZ,EADoD,CAAAa,YAAA,EAAO,EACrD;;;;;;IAGJb,EADF,CAAAC,cAAA,cAAmG,cAE3B;IAApED,EAAA,CAAAE,UAAA,mBAAAgD,+FAAA;MAAAlD,EAAA,CAAAI,aAAA,CAAA+C,IAAA;MAAA,MAAAJ,QAAA,GAAA/C,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA0C,eAAA,CAAAD,QAAA,CAAqB;IAAA,EAAC;IADjC/C,EAAA,CAAAa,YAAA,EACsE;IACtEb,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAiD,SAAA,YAAgC;IAAAjD,EAAA,CAAAY,MAAA,oBAClC;IACFZ,EADE,CAAAa,YAAA,EAAM,EACF;;;;IALmDb,EAAA,CAAAqB,SAAA,EAAkB;IACvCrB,EADqB,CAAA8B,UAAA,QAAAiB,QAAA,CAAAK,KAAA,EAAApD,EAAA,CAAAqD,aAAA,CAAkB,yCAAAN,QAAA,CAAAO,SAAA,CACJ;;;;;;IAMvEtD,EAAA,CAAAC,cAAA,cAEkC;IAAhCD,EAAA,CAAAE,UAAA,mBAAAqD,+FAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAT,QAAA,GAAA/C,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA0C,eAAA,CAAAD,QAAA,CAAqB;IAAA,EAAC;IAC/B/C,EAAA,CAAAiD,SAAA,YAAuD;IACvDjD,EAAA,CAAAC,cAAA,eAAiD;IAAAD,EAAA,CAAAY,MAAA,UAAG;IACtDZ,EADsD,CAAAa,YAAA,EAAO,EACvD;;;;;IAvBRb,EAFF,CAAAC,cAAA,cACiD,cAE6E;IAiB1HD,EAfA,CAAAmB,UAAA,IAAAsC,yEAAA,kBAEkC,IAAAC,yEAAA,kBAKiE,IAAAC,yEAAA,kBAUjE;IAIpC3D,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAC,cAAA,YAA8E;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IACpGZ,EADoG,CAAAa,YAAA,EAAI,EAClG;;;;;IAvBIb,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAsD,SAAA,CAAAb,QAAA,CAAAO,SAAA,EAA+B;IAO/BtD,EAAA,CAAAqB,SAAA,EAAiC;IAAjCrB,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuD,WAAA,CAAAd,QAAA,CAAAO,SAAA,EAAiC;IAQjCtD,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAwD,SAAA,CAAAf,QAAA,CAAAO,SAAA,EAA+B;IAOuCtD,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAsB,iBAAA,CAAAyB,QAAA,CAAAO,SAAA,CAAoB;;;;;IA/BtGtD,EAFF,CAAAC,cAAA,cACiG,gBACzB;IACpED,EAAA,CAAAY,MAAA,iCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAmB,UAAA,IAAA4C,mEAAA,kBACiD;IA6BrD/D,EADE,CAAAa,YAAA,EAAM,EACF;;;;IA9BoBb,EAAA,CAAAqB,SAAA,GAA2B;IAA3BrB,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAAmC,aAAA,CAAAC,QAAA,CAA2B;;;;;;IAgCnD1C,EADF,CAAAC,cAAA,cAAqF,gBAE1D;IAAAD,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACrCb,EAAA,CAAAC,cAAA,mBACoF;IAA/CD,EAAA,CAAAgE,gBAAA,2BAAAC,gGAAAhC,MAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAmE,kBAAA,CAAA7D,MAAA,CAAA8D,iBAAA,CAAAC,cAAA,EAAApC,MAAA,MAAA3B,MAAA,CAAA8D,iBAAA,CAAAC,cAAA,GAAApC,MAAA;MAAA,OAAAjC,EAAA,CAAAU,WAAA,CAAAuB,MAAA;IAAA,EAA8C;IACrFjC,EADsF,CAAAa,YAAA,EAAW,EAC3F;;;;IADFb,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAkC,MAAA,CAAmB;IAAgBxC,EAAA,CAAAsE,gBAAA,YAAAhE,MAAA,CAAA8D,iBAAA,CAAAC,cAAA,CAA8C;;;;;;IAKnFrE,EAAA,CAAAC,cAAA,iBACqC;IAAnCD,EAAA,CAAAE,UAAA,mBAAAqE,yFAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAAC,OAAA,GAAAzE,EAAA,CAAAO,aAAA,GAAAmE,SAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAqE,mBAAA,CAAAF,OAAA,CAAwB;IAAA,EAAC;IAACzE,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IA9EtDb,EADF,CAAAC,cAAA,kBAAmD,qBACjC;IACdD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAiB;IAIbb,EAHJ,CAAAC,cAAA,uBAA2B,cAED,mBAC6E;IACjGD,EAAA,CAAAY,MAAA,iCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,qBAE4C;IADED,EAAA,CAAAgE,gBAAA,2BAAAY,2FAAA3C,MAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAAyE,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAmE,kBAAA,CAAA7D,MAAA,CAAA8D,iBAAA,CAAA1C,WAAA,EAAAO,MAAA,MAAA3B,MAAA,CAAA8D,iBAAA,CAAA1C,WAAA,GAAAO,MAAA;MAAA,OAAAjC,EAAA,CAAAU,WAAA,CAAAuB,MAAA;IAAA,EAA2C;IAE3FjC,EAD8C,CAAAa,YAAA,EAAa,EACrD;IAEJb,EADF,CAAAC,cAAA,cAAwB,iBACiE;IACrFD,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,iBACwB;IADqCD,EAAA,CAAAgE,gBAAA,2BAAAc,uFAAA7C,MAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAAyE,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAmE,kBAAA,CAAA7D,MAAA,CAAA8D,iBAAA,CAAAzC,YAAA,EAAAM,MAAA,MAAA3B,MAAA,CAAA8D,iBAAA,CAAAzC,YAAA,GAAAM,MAAA;MAAA,OAAAjC,EAAA,CAAAU,WAAA,CAAAuB,MAAA;IAAA,EAA4C;IAE3GjC,EAFE,CAAAa,YAAA,EACwB,EACpB;IAiDNb,EAhDA,CAAAmB,UAAA,KAAA4D,6DAAA,kBAAwC,KAAAC,6DAAA,kBAYyD,KAAAC,6DAAA,kBAoCZ;IAQnFjF,EADF,CAAAC,cAAA,eAA2C,kBAC4B;IAAvBD,EAAA,CAAAE,UAAA,mBAAAgF,gFAAA;MAAA,MAAAT,OAAA,GAAAzE,EAAA,CAAAI,aAAA,CAAAyE,GAAA,EAAAH,SAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA6E,OAAA,CAAAV,OAAA,CAAY;IAAA,EAAC;IAACzE,EAAA,CAAAY,MAAA,oBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAChFb,EAAA,CAAAmB,UAAA,KAAAiE,gEAAA,qBACqC;IAG3CpF,EAFI,CAAAa,YAAA,EAAM,EACO,EACP;;;;IAhFNb,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAqF,kBAAA,wEAAA/E,MAAA,CAAAgF,KAAA,CAAAC,UAAA,cAAAjF,MAAA,CAAAgF,KAAA,CAAAE,MAAA,OACF;IAOgBxF,EAAA,CAAAqB,SAAA,GAA0B;IAA6CrB,EAAvE,CAAA8B,UAAA,2BAA0B,wBAA4C,kBAAkB;IACtD9B,EAAA,CAAAsE,gBAAA,YAAAhE,MAAA,CAAA8D,iBAAA,CAAA1C,WAAA,CAA2C;IACvF1B,EADwF,CAAA8B,UAAA,aAAAxB,MAAA,CAAAkC,MAAA,CAAmB,uBACrF;IAMqCxC,EAAA,CAAAqB,SAAA,GAA4C;IAA5CrB,EAAA,CAAAsE,gBAAA,YAAAhE,MAAA,CAAA8D,iBAAA,CAAAzC,YAAA,CAA4C;IACvG3B,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAkC,MAAA,CAAmB;IAEExC,EAAA,CAAAqB,SAAA,EAAa;IAAbrB,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAkC,MAAA,CAAa;IAYnCxC,EAAA,CAAAqB,SAAA,EAA4F;IAA5FrB,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAkC,MAAA,IAAAlC,MAAA,CAAAmC,aAAA,IAAAnC,MAAA,CAAAmC,aAAA,CAAAC,QAAA,IAAApC,MAAA,CAAAmC,aAAA,CAAAC,QAAA,CAAA+C,MAAA,KAA4F;IAoC5CzF,EAAA,CAAAqB,SAAA,EAAgC;IAAhCrB,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAoF,cAAA,KAAApF,MAAA,CAAAkC,MAAA,CAAgC;IAS5CxC,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAkC,MAAA,KAAAlC,MAAA,CAAAoF,cAAA,CAAgC;;;ADjH7E,OAAM,MAAOC,8BAA+B,SAAQ7F,aAAa;EAC/D8F,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,qBAA2C,EAC3CC,aAA2B,EAE3BC,KAAqB,EACrBC,OAAuB,EACvBC,QAAkB,EAClBC,aAA2B;IACjC,KAAK,CAACR,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,aAAa,GAAbA,aAAa;IAEb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IAGvB,KAAA9D,YAAY,GAA4B,EAAE;IAC1C,KAAAC,MAAM,GAAG,KAAK;IAEd,KAAAF,gBAAgB,GAAqB;MACnCgE,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,eAAe,EAAE,aAAa,CAAC;MAC1IC,iBAAiB,EAAE,2BAA2B;MAC9CC,eAAe,EAAE,oDAAoD;MACrEC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,wCAAwC;MAClDC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAE,cAAc;MAC1BC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;KACd;IACD,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZZ,KAAK,EAAE;KACR,EACD;MACEW,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBZ,KAAK,EAAE;KACR,CACF;IAEQ,KAAAa,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAwMzB,KAAAC,YAAY,GAAU,EAAE;EA5ON;EA4CTC,QAAQA,CAAA;IACf,IAAI,CAACzB,KAAK,CAAC0B,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAACE,OAAO,GAAGD,GAAG;QAClB,IAAI,CAACE,oBAAoB,EAAE;QAC3B,IAAI,CAACC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAA,sBAAsBA,CAAA;IACpB,IAAI,CAAClG,gBAAgB,GAAG;MACtB,GAAG,IAAI,CAACA,gBAAgB;MACxBsE,QAAQ,EAAG,IAAI,CAACpE,MAAM,IAAI,IAAI,CAACC,aAAa,EAAEZ,UAAU,KAAK,IAAI,IAAK,IAAI,CAAC6D;KAC5E;EACH;EAEA;EACA,IAAIA,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACjD,aAAa,EAAEjB,OAAO,KAAK,CAAC;EAC1C;EAGAwB,eAAeA,CAACyF,IAAU;IACxB,IAAIA,IAAI,IAAIA,IAAI,CAAC/F,QAAQ,CAACU,KAAK,EAAEsF,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC/F,QAAQ,CAACU,KAAK,EAAE,QAAQ,CAAC;EAC7E;EAKAkF,oBAAoBA,CAAA;IAClB,IAAI,CAACtC,qBAAqB,CAAC4C,6CAA6C,CAAC;MACvEC,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAACT,OAAO;QACtBU,SAAS,EAAE,IAAI,CAACvB,SAAS;QACzBwB,QAAQ,EAAE,IAAI,CAACzB;;KAElB,CAAC,CAACM,SAAS,CAACoB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACC,iBAAiB,GAAGJ,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC3C,IAAI,CAAC1B,YAAY,GAAGwB,GAAG,CAACC,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAEAX,YAAYA,CAAA;IACV,IAAI,CAACtC,aAAa,CAACqD,6BAA6B,CAAC;MAC/CT,IAAI,EAAE;QACJU,QAAQ,EAAE,IAAI,CAAClB;;KAElB,CAAC,CAACR,SAAS,CAACoB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAAC9D,KAAK,GAAG2D,GAAG,CAACE,OAAO;QACxB,IAAI,CAACK,UAAU,GAAG,GAAG,IAAI,CAAClE,KAAK,CAACC,UAAU,IAAI,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG;MACpE;IACF,CAAC,CAAC;EACJ;EAIAiE,oBAAoBA,CAACC,GAAQ,EAAEC,gBAAqB;IAClD,IAAI,CAAC3D,qBAAqB,CAAC4D,6CAA6C,CAAC;MAAEf,IAAI,EAAEc;IAAgB,CAAE,CAAC,CAAC9B,SAAS,CAACoB,GAAG,IAAG;MACnH,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAAC3G,aAAa,GAAGwG,GAAG,CAACE,OAAO;QAChC,IAAI,CAAC/E,iBAAiB,GAAG;UACvBC,cAAc,EAAE,IAAI,CAAC5B,aAAa,CAAC4B,cAAc;UACjDwF,YAAY,EAAE,IAAI,CAAC3B,WAAW;UAC9BvG,YAAY,EAAE,IAAI,CAACc,aAAa,CAACd,YAAY;UAC7C4H,QAAQ,EAAE,IAAI,CAAClB,OAAO;UACtByB,kBAAkB,EAAE;SACrB;QACD,IAAI,IAAI,CAACrH,aAAa,CAACf,WAAW,EAAE;UAClC,IAAI,CAAC0C,iBAAiB,CAAC1C,WAAW,GAAG,IAAIqI,IAAI,CAAC,IAAI,CAACtH,aAAa,CAACf,WAAW,CAAC;QAC/E;QACA;QACA,IAAI,CAAC8G,sBAAsB,EAAE;QAC7B,IAAI,CAAC1C,aAAa,CAAC6C,IAAI,CAACe,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIA/E,mBAAmBA,CAAC+E,GAAQ;IAC1B,IAAI,CAACM,UAAU,EAAE;IACjB,IAAI,IAAI,CAACjE,KAAK,CAACkE,aAAa,CAACxE,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACU,OAAO,CAAC+D,aAAa,CAAC,IAAI,CAACnE,KAAK,CAACkE,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACjE,qBAAqB,CAACmE,0CAA0C,CAAC;MAAEtB,IAAI,EAAE,IAAI,CAACuB,WAAW;IAAE,CAAE,CAAC,CAACvC,SAAS,CAACoB,GAAG,IAAG;MAClH,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACjD,OAAO,CAACkE,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC/B,oBAAoB,EAAE;QAC3BoB,GAAG,CAACY,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;IACxB,IAAI,CAAClC,oBAAoB,EAAE;EAC7B;EAEA3H,MAAMA,CAAC+I,GAAQ;IACb,IAAI,CAACnH,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACgG,sBAAsB,EAAE;IAC7B,IAAI,CAACpE,iBAAiB,GAAG;MACvBC,cAAc,EAAE,EAAE;MAClBwF,YAAY,EAAE,IAAI,CAAC3B,WAAW;MAC9BxG,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChB4H,QAAQ,EAAE,IAAI,CAAClB,OAAO;MACtByB,kBAAkB,EAAE;KACrB;IACD,IAAI,CAAChE,aAAa,CAAC6C,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEAxI,MAAMA,CAACwI,GAAQ,EAAEe,aAAkB;IACjC,IAAI,CAAClI,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACgG,sBAAsB,EAAE;IAC7B,IAAI,CAACiB,oBAAoB,CAACC,GAAG,EAAEe,aAAa,CAACd,gBAAgB,CAAC;EAChE;EAEAxH,mBAAmBA,CAACuI,KAA8B;IAChD,IAAI,CAACnI,YAAY,GAAGmI,KAAK;EAC3B;EAEArI,gBAAgBA,CAACsI,IAAY;IAC3B,IAAI,CAAC,IAAI,CAACvG,iBAAiB,CAACzC,YAAY,EAAE;MACxC,IAAI,CAACyC,iBAAiB,CAACzC,YAAY,GAAGgJ,IAAI;IAC5C;EACF;EAEAX,UAAUA,CAAA;IACR,IAAI,CAACjE,KAAK,CAAC6E,KAAK,EAAE;IAClB,IAAI,CAAC7E,KAAK,CAACY,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvC,iBAAiB,CAAC1C,WAAW,CAAC;IACjE,IAAI,CAACqE,KAAK,CAACY,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvC,iBAAiB,CAACzC,YAAY,CAAC;IAClE;IACA,IAAI,CAAC,IAAI,CAAC+D,cAAc,EAAE;MACxB,IAAI,CAACK,KAAK,CAACY,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvC,iBAAiB,CAACC,cAAc,CAAC;IACtE;EACF;EAEA5C,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO7B,MAAM,CAAC6B,WAAW,CAAC,CAACmJ,MAAM,CAAC,YAAY,CAAC;IACjD;IACA,OAAO,EAAE;EACX;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;MACxB,OAAOC,IAAI,CAACvC,IAAI;IAClB;IACA,OAAOsC,KAAK;EACd;EAEAX,WAAWA,CAAA;IACT,MAAMa,MAAM,GAAG;MACb,GAAG,IAAI,CAAC7G,iBAAiB;MACzB0F,kBAAkB,EAAE,IAAI,CAACvH;KAC1B;IACD,IAAI,CAACuI,gBAAgB,CAACG,MAAM,CAACnB,kBAAkB,CAAC;IAEhD,IAAI,IAAI,CAAC1F,iBAAiB,CAAC1C,WAAW,EAAE;MACtCuJ,MAAM,CAACvJ,WAAW,GAAG,IAAI,CAACD,UAAU,CAAC,IAAI,CAAC2C,iBAAiB,CAAC1C,WAAW,CAAC;IAC1E;IACA,OAAOuJ,MAAM;EACf;EAEA9F,OAAOA,CAACuE,GAAQ;IACdA,GAAG,CAACY,KAAK,EAAE;EACb;EACAY,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACG,SAAS,CAACF,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAIAI,WAAWA,CAACC,SAAiB;IAC3B,IAAI,CAAC9D,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC+D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzD,EAAE,IAAIuD,SAAS,CAAC;EACtE;EAEAG,WAAWA,CAACjC,GAAQ,GACpB;EAEAkC,UAAUA,CAACC,KAAU,EAAEC,KAAa;IAClC,IAAIC,IAAI,GAAG,IAAI,CAACrE,YAAY,CAACoE,KAAK,CAAC,CAAC1I,KAAK,CAAC4I,KAAK,CAAC,CAAC,EAAE,IAAI,CAACtE,YAAY,CAACoE,KAAK,CAAC,CAAC1I,KAAK,CAAC6I,IAAI,EAAE,IAAI,CAACvE,YAAY,CAACoE,KAAK,CAAC,CAAC1I,KAAK,CAAC8I,IAAI,CAAC;IAC5H,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGF,KAAK,CAACQ,MAAM,CAACjF,KAAK,GAAG,GAAG,GAAG,IAAI,CAACM,YAAY,CAACoE,KAAK,CAAC,CAACQ,SAAS,EAAE,EAAE;MAAEJ,IAAI,EAAE,IAAI,CAACxE,YAAY,CAACoE,KAAK,CAAC,CAAC1I,KAAK,CAAC8I;IAAI,CAAE,CAAC;IACjJ,IAAI,CAACxE,YAAY,CAACoE,KAAK,CAAC,CAAC1I,KAAK,GAAG+I,OAAO;EAC1C;EAEAI,MAAMA,CAAA;IACJ,IAAI,CAAClG,aAAa,CAACmG,IAAI,CAAC;MACtBC,MAAM;MACNC,OAAO,EAAE,IAAI,CAACxE;KACf,CAAC;IACF,IAAI,CAAC9B,QAAQ,CAACuG,IAAI,EAAE;EACtB;EAEAC,UAAUA,CAACC,GAAQ;IACjB,IAAIA,GAAG,EAAEnE,MAAM,CAACC,IAAI,CAACkE,GAAG,EAAE,QAAQ,CAAC;EACrC;;;uCAjRWlH,8BAA8B,EAAA3F,EAAA,CAAA8M,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhN,EAAA,CAAA8M,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAlN,EAAA,CAAA8M,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAApN,EAAA,CAAA8M,iBAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAAtN,EAAA,CAAA8M,iBAAA,CAAAO,EAAA,CAAAE,YAAA,GAAAvN,EAAA,CAAA8M,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAzN,EAAA,CAAA8M,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAA3N,EAAA,CAAA8M,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAA7N,EAAA,CAAA8M,iBAAA,CAAAgB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA9BpI,8BAA8B;MAAAqI,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCzBzCnO,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,MAAA,GACF;UAAAZ,EAAA,CAAAa,YAAA,EAAiB;UAEfb,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAY,MAAA,iPAAuC;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAK7Eb,EAHJ,CAAAC,cAAA,aAA8B,aAEL,aAC0B;UAC7CD,EAAA,CAAAmB,UAAA,IAAAkN,gDAAA,oBAAoF;UAI1FrO,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;UAMEb,EAJR,CAAAC,cAAA,cAAmC,gBAC+D,aACvF,cACoE,cACzC;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACrCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAEpCZ,EAFoC,CAAAa,YAAA,EAAK,EAClC,EACC;UACRb,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAmB,UAAA,KAAAmN,6CAAA,kBAA+E;UAcvFtO,EAHM,CAAAa,YAAA,EAAQ,EACF,EACJ,EACO;UAEbb,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAgE,gBAAA,wBAAAuK,8EAAAtM,MAAA;YAAAjC,EAAA,CAAAI,aAAA,CAAAoO,GAAA;YAAAxO,EAAA,CAAAmE,kBAAA,CAAAiK,GAAA,CAAA5G,SAAA,EAAAvF,MAAA,MAAAmM,GAAA,CAAA5G,SAAA,GAAAvF,MAAA;YAAA,OAAAjC,EAAA,CAAAU,WAAA,CAAAuB,MAAA;UAAA,EAAoB;UAClCjC,EAAA,CAAAE,UAAA,wBAAAqO,8EAAAtM,MAAA;YAAAjC,EAAA,CAAAI,aAAA,CAAAoO,GAAA;YAAA,OAAAxO,EAAA,CAAAU,WAAA,CAAc0N,GAAA,CAAA7D,WAAA,CAAAtI,MAAA,CAAmB;UAAA,EAAC;UAEtCjC,EADE,CAAAa,YAAA,EAAiB,EACF;UAGbb,EAFJ,CAAAC,cAAA,sBAAgB,eAC6B,kBACmB;UAAnBD,EAAA,CAAAE,UAAA,mBAAAuO,iEAAA;YAAAzO,EAAA,CAAAI,aAAA,CAAAoO,GAAA;YAAA,OAAAxO,EAAA,CAAAU,WAAA,CAAS0N,GAAA,CAAA7B,MAAA,EAAQ;UAAA,EAAC;UACzDvM,EAAA,CAAAY,MAAA,wCACF;UAGNZ,EAHM,CAAAa,YAAA,EAAS,EACL,EACS,EACT;UAGVb,EAAA,CAAAmB,UAAA,KAAAuN,sDAAA,kCAAA1O,EAAA,CAAA2O,sBAAA,CAAiE;;;UA1D7D3O,EAAA,CAAAqB,SAAA,GACF;UADErB,EAAA,CAAA4O,kBAAA,wEAAAR,GAAA,CAAA5E,UAAA,MACF;UAQ4ExJ,EAAA,CAAAqB,SAAA,GAAc;UAAdrB,EAAA,CAAA8B,UAAA,SAAAsM,GAAA,CAAAS,QAAA,CAAc;UAmB7D7O,EAAA,CAAAqB,SAAA,IAAuB;UAAvBrB,EAAA,CAAA8B,UAAA,YAAAsM,GAAA,CAAA/E,iBAAA,CAAuB;UAgBlCrJ,EAAA,CAAAqB,SAAA,GAAoB;UAApBrB,EAAA,CAAAsE,gBAAA,SAAA8J,GAAA,CAAA5G,SAAA,CAAoB;UAAuBxH,EAAtB,CAAA8B,UAAA,aAAAsM,GAAA,CAAA7G,QAAA,CAAqB,mBAAA6G,GAAA,CAAA3G,YAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}