{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { parseNumericPattern, parseNDigits } from \"../utils.js\";\nimport setUTCWeek from \"../../../_lib/setUTCWeek/index.js\";\nimport startOfUTCWeek from \"../../../_lib/startOfUTCWeek/index.js\"; // Local week of year\nexport var LocalWeekParser = /*#__PURE__*/function (_Parser) {\n  _inherits(LocalWeekParser, _Parser);\n  var _super = _createSuper(LocalWeekParser);\n  function LocalWeekParser() {\n    var _this;\n    _classCallCheck(this, LocalWeekParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 100);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['y', 'R', 'u', 'q', 'Q', 'M', 'L', 'I', 'd', 'D', 'i', 't', 'T']);\n    return _this;\n  }\n  _createClass(LocalWeekParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      switch (token) {\n        case 'w':\n          return parseNumericPattern(numericPatterns.week, dateString);\n        case 'wo':\n          return match.ordinalNumber(dateString, {\n            unit: 'week'\n          });\n        default:\n          return parseNDigits(token.length, dateString);\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value >= 1 && value <= 53;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value, options) {\n      return startOfUTCWeek(setUTCWeek(date, value, options), options);\n    }\n  }]);\n  return LocalWeekParser;\n}(Parser);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}