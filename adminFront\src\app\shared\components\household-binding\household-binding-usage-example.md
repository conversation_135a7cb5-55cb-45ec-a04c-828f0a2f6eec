# HouseholdBindingComponent 使用說明

## 更新內容

HouseholdBindingComponent 現在支援透過建案ID從 API 載入戶別資料。

## 新增功能

1. **建案ID輸入**: 透過 `buildCaseId` 屬性傳入建案ID
2. **自動載入資料**: 組件會自動呼叫 `HouseCustomService.getDropDown` 載入戶別資料
3. **載入狀態顯示**: 在載入過程中顯示載入動畫
4. **錯誤處理**: API 載入失敗時會回退到 mock 資料

## 使用方式

### 基本用法（使用 API 資料）

```html
<app-household-binding
  [buildCaseId]="123"
  [maxSelections]="10"
  (selectionChange)="onHouseholdSelectionChange($event)"
  [(ngModel)]="selectedHouseholds">
</app-household-binding>
```

### 進階用法（包含排除戶別）

```html
<app-household-binding
  [buildCaseId]="buildCaseId"
  [maxSelections]="maxSelections"
  [excludedHouseholds]="alreadySelectedHouseholds"
  [allowBatchSelect]="true"
  (selectionChange)="onHouseholdSelectionChange($event)"
  [(ngModel)]="selectedHouseholds">
</app-household-binding>
```

### TypeScript 組件範例

```typescript
export class YourComponent {
  buildCaseId: number = 123;
  selectedHouseholds: string[] = [];
  maxSelections: number = 10;
  alreadySelectedHouseholds: string[] = ['A001', 'B002'];

  onHouseholdSelectionChange(households: HouseholdItem[]) {
    console.log('選擇的戶別:', households);
    // 處理選擇變更邏輯
  }
}
```

## 屬性說明

| 屬性名稱 | 類型 | 必填 | 預設值 | 說明 |
|---------|------|------|-------|------|
| `buildCaseId` | `number \| null` | 否 | `null` | 建案ID，用於從 API 載入戶別資料 |
| `buildingData` | `BuildingData` | 否 | `{}` | 手動提供的戶別資料（當未提供 buildCaseId 時使用） |
| `maxSelections` | `number \| null` | 否 | `null` | 最大選擇數量限制 |
| `excludedHouseholds` | `string[]` | 否 | `[]` | 排除的戶別列表 |
| `allowBatchSelect` | `boolean` | 否 | `true` | 是否允許批量選擇功能 |
| `disabled` | `boolean` | 否 | `false` | 是否禁用組件 |
| `placeholder` | `string` | 否 | `'請選擇戶別'` | 佔位符文字 |

## 事件說明

| 事件名稱 | 參數類型 | 說明 |
|---------|----------|------|
| `selectionChange` | `HouseholdItem[]` | 當選擇變更時觸發 |

## 資料介面

```typescript
export interface HouseholdItem {
  code: string;          // 戶別代碼
  building: string;      // 棟別
  floor?: string;        // 樓層
  houseId?: number;      // 戶別ID（來自API）
  houseName?: string;    // 戶別名稱（來自API）
  isSelected?: boolean;  // 是否被選中
  isDisabled?: boolean;  // 是否被禁用
}

export interface BuildingData {
  [key: string]: HouseholdItem[];
}
```

## 注意事項

1. 當同時提供 `buildCaseId` 和 `buildingData` 時，優先使用 `buildCaseId` 從 API 載入資料
2. 如果 API 載入失敗，組件會自動回退到 mock 資料
3. 載入過程中，組件會顯示載入狀態並禁用操作
4. 組件實作了 `ControlValueAccessor`，可以與 Angular Forms 無縫整合
