{"ast": null, "code": "import { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"../components/pagination/pagination.component\";\nfunction SpaceComponent_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const createModal_r4 = i0.ɵɵreference(66);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(createModal_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_62_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_tr_62_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const editModal_r7 = i0.ɵɵreference(68);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(editModal_r7, item_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_62_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_tr_62_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteSpace(item_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceComponent_tr_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 36);\n    i0.ɵɵtemplate(11, SpaceComponent_tr_62_button_11_Template, 3, 0, \"button\", 37)(12, SpaceComponent_tr_62_button_12_Template, 3, 0, \"button\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CLocation || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r6.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 6, item_r6.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction SpaceComponent_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 43)(1, \"nb-card-header\", 44)(2, \"h5\", 45);\n    i0.ɵɵelement(3, \"i\", 46);\n    i0.ɵɵtext(4, \"\\u65B0\\u589E\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_65_Template_button_click_5_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r10));\n    });\n    i0.ɵɵelement(6, \"i\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 49)(8, \"div\", 50)(9, \"div\", 51)(10, \"div\", 52)(11, \"div\", 53)(12, \"label\", 54);\n    i0.ɵɵtext(13, \" \\u9805\\u76EE\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 55)(15, \"input\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_65_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CName, $event) || (ctx_r2.spaceDetail.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 51)(17, \"div\", 52)(18, \"div\", 53)(19, \"label\", 57);\n    i0.ɵɵtext(20, \" \\u6240\\u5C6C\\u5340\\u57DF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 55)(22, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_65_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CLocation, $event) || (ctx_r2.spaceDetail.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(23, \"div\", 51)(24, \"div\", 52)(25, \"div\", 53)(26, \"label\", 59);\n    i0.ɵɵtext(27, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 55)(29, \"nb-form-field\", 60)(30, \"nb-select\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_65_Template_nb_select_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CStatus, $event) || (ctx_r2.spaceDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(31, \"nb-option\", 17)(32, \"span\", 4);\n    i0.ɵɵelement(33, \"i\", 62);\n    i0.ɵɵtext(34, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"nb-option\", 17)(36, \"span\", 4);\n    i0.ɵɵelement(37, \"i\", 63);\n    i0.ɵɵtext(38, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(39, \"nb-card-footer\", 64)(40, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_65_Template_button_click_40_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r10));\n    });\n    i0.ɵɵelement(41, \"i\", 66);\n    i0.ɵɵtext(42, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_65_Template_button_click_43_listener() {\n      const ref_r10 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r10));\n    });\n    i0.ɵɵelement(44, \"i\", 68);\n    i0.ɵɵtext(45, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CName);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CLocation);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction SpaceComponent_ng_template_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 43)(1, \"nb-card-header\", 44)(2, \"h5\", 45);\n    i0.ɵɵelement(3, \"i\", 69);\n    i0.ɵɵtext(4, \"\\u7DE8\\u8F2F\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_67_Template_button_click_5_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(6, \"i\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 49)(8, \"div\", 50)(9, \"div\", 51)(10, \"div\", 52)(11, \"div\", 53)(12, \"label\", 70);\n    i0.ɵɵtext(13, \" \\u6240\\u5C6C\\u5340\\u57DF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 55)(15, \"input\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_67_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CLocation, $event) || (ctx_r2.spaceDetail.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"div\", 51)(17, \"div\", 52)(18, \"div\", 53)(19, \"label\", 72);\n    i0.ɵɵtext(20, \" \\u9805\\u76EE\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 55)(22, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_67_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CName, $event) || (ctx_r2.spaceDetail.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(23, \"div\", 51)(24, \"div\", 52)(25, \"div\", 53)(26, \"label\", 74);\n    i0.ɵɵtext(27, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 55)(29, \"nb-form-field\", 60)(30, \"nb-select\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_ng_template_67_Template_nb_select_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.spaceDetail.CStatus, $event) || (ctx_r2.spaceDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(31, \"nb-option\", 17)(32, \"span\", 4);\n    i0.ɵɵelement(33, \"i\", 62);\n    i0.ɵɵtext(34, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"nb-option\", 17)(36, \"span\", 4);\n    i0.ɵɵelement(37, \"i\", 63);\n    i0.ɵɵtext(38, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(39, \"nb-card-footer\", 64)(40, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_67_Template_button_click_40_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r12));\n    });\n    i0.ɵɵelement(41, \"i\", 66);\n    i0.ɵɵtext(42, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function SpaceComponent_ng_template_67_Template_button_click_43_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r11).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r12));\n    });\n    i0.ɵɵelement(44, \"i\", 76);\n    i0.ɵɵtext(45, \"\\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CLocation);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.spaceDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nexport class SpaceComponent extends BaseComponent {\n  constructor(allow, dialogService, _spaceService, _templateService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._spaceService = _spaceService;\n    this._templateService = _templateService;\n    this.message = message;\n    this.valid = valid;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.spaceList = [];\n    this.spaceDetail = {};\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.searchStatus = null;\n    // 模板相關屬性\n    this.templateList = [];\n    this.templateDetail = {};\n    // 新增模板時的空間選擇器相關屬性\n    this.availableSpaces = [];\n    this.selectedSpacesForTemplate = [];\n    this.spacePageIndex = 1;\n    this.spacePageSize = 10;\n    this.spaceTotalRecords = 0;\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    // 控制UI顯示\n    this.showSpacePickList = false;\n    this.allSpacesSelected = false;\n  }\n  ngOnInit() {\n    this.getSpaceList();\n  }\n  getSpaceList() {\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CName: this.searchKeyword || null,\n        CLocation: this.searchLocation || null,\n        CStatus: this.searchStatus\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    })).subscribe();\n  }\n  onSearch() {\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getSpaceList();\n  }\n  openCreateModal(ref) {\n    this.spaceDetail = {\n      CName: '',\n      CLocation: '',\n      CStatus: 1 // 1 = 啟用, 0 = 停用\n    };\n    this.dialogService.open(ref);\n  }\n  openEditModal(ref, item) {\n    this.getSpaceById(item.CSpaceID, ref);\n  }\n  getSpaceById(spaceId, ref) {\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\n      body: {\n        CSpaceID: spaceId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceDetail = {\n          ...res.Entries\n        };\n        this.dialogService.open(ref);\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    });\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._spaceService.apiSpaceSaveSpacePost$Json({\n      body: this.spaceDetail\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n        this.getSpaceList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    })).subscribe();\n  }\n  deleteSpace(item) {\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\n        body: {\n          CSpaceID: item.CSpaceId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"刪除成功\");\n          this.getSpaceList();\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除失敗');\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[空間名稱]', this.spaceDetail.CName);\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CName, 50);\n    this.valid.required('[所屬區域]', this.spaceDetail.CLocation);\n    this.valid.isStringMaxLength('[所屬區域]', this.spaceDetail.CLocation, 50);\n  }\n  // 模板管理方法\n  openTemplateManagementModal(ref) {\n    this.getTemplateList();\n    this.dialogService.open(ref, {\n      hasScroll: true\n    });\n  }\n  getTemplateList() {\n    this._templateService.apiTemplateGetTemplateListPost$Json({\n      body: {\n        PageIndex: 1,\n        PageSize: 100 // 先載入所有模板\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode === 0) {\n        this.templateList = res.Entries.map(item => ({\n          CTemplateId: item.CTemplateId,\n          CTemplateName: item.CTemplateName,\n          CCreateDt: item.CCreateDt,\n          CUpdateDt: item.CUpdateDt,\n          CCreator: item.CCreator,\n          CUpdator: item.CUpdator,\n          CStatus: item.CStatus\n        }));\n      } else {\n        this.message.showErrorMSG(res.Message || '載入模板資料失敗');\n      }\n    });\n  }\n  openCreateTemplateModal(ref) {\n    this.templateDetail = {\n      CTemplateName: '',\n      CTemplateType: 1,\n      CStatus: 1\n    };\n    this.selectedSpacesForTemplate = [];\n    this.getSpacesForPickList();\n    this.dialogService.open(ref, {\n      hasScroll: true\n    });\n  }\n  getSpacesForPickList() {\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: {\n        PageIndex: this.spacePageIndex,\n        PageSize: this.spacePageSize,\n        CName: this.spaceSearchKeyword || null,\n        CLocation: this.spaceSearchLocation || null,\n        CStatus: 1 // 只顯示啟用的空間\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode === 0) {\n        this.availableSpaces = res.Entries.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CName: item.CName,\n          CLocation: item.CLocation,\n          selected: false\n        }));\n        this.spaceTotalRecords = res.TotalItems;\n      } else {\n        this.message.showErrorMSG(res.Message || '載入空間資料失敗');\n      }\n    });\n  }\n  onSpaceSearch() {\n    this.spacePageIndex = 1;\n    this.getSpacesForPickList();\n  }\n  onSpaceReset() {\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.getSpacesForPickList();\n  }\n  spacePageChanged(newPage) {\n    this.spacePageIndex = newPage;\n    this.getSpacesForPickList();\n  }\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      this.selectedSpacesForTemplate.push({\n        ...space\n      });\n    } else {\n      const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\n      if (index > -1) {\n        this.selectedSpacesForTemplate.splice(index, 1);\n      }\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  toggleAllSpaces() {\n    this.allSpacesSelected = !this.allSpacesSelected;\n    this.availableSpaces.forEach(space => {\n      space.selected = this.allSpacesSelected;\n      if (this.allSpacesSelected) {\n        const exists = this.selectedSpacesForTemplate.find(s => s.CSpaceID === space.CSpaceID);\n        if (!exists) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      }\n    });\n    if (!this.allSpacesSelected) {\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(selected => !this.availableSpaces.some(available => available.CSpaceID === selected.CSpaceID));\n    }\n  }\n  updateAllSpacesSelectedState() {\n    this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  removeSelectedSpace(space) {\n    const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\n    if (index > -1) {\n      this.selectedSpacesForTemplate.splice(index, 1);\n    }\n    // 更新可用空間列表中的選擇狀態\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  onSubmitTemplate(ref) {\n    this.templateValidation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.selectedSpacesForTemplate.length === 0) {\n      this.message.showErrorMSG('請至少選擇一個空間');\n      return;\n    }\n    const templateDetails = this.selectedSpacesForTemplate.map(space => ({\n      SpaceID: space.CSpaceID\n    }));\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: {\n        ...this.templateDetail,\n        TemplateDetails: templateDetails\n      }\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"模板建立成功\");\n        ref.close();\n        this.getTemplateList();\n      } else {\n        this.message.showErrorMSG(res.Message || '建立模板失敗');\n      }\n    });\n  }\n  deleteTemplate(template) {\n    if (confirm(`您確定要刪除模板「${template.TemplateName}」嗎？`)) {\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\n        body: {\n          TemplateID: template.TemplateID\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"模板刪除成功\");\n          this.getTemplateList();\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除模板失敗');\n        }\n      });\n    }\n  }\n  templateValidation() {\n    this.valid.clear();\n    this.valid.required('[模板名稱]', this.templateDetail.TemplateName);\n    this.valid.isStringMaxLength('[模板名稱]', this.templateDetail.TemplateName, 100);\n    if (this.templateDetail.Description) {\n      this.valid.isStringMaxLength('[描述]', this.templateDetail.Description, 500);\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  static {\n    this.ɵfac = function SpaceComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceComponent,\n      selectors: [[\"ngx-space\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 69,\n      vars: 11,\n      consts: [[\"createModal\", \"\"], [\"editModal\", \"\"], [\"accent\", \"success\"], [1, \"alert\", \"alert-info\", \"mb-4\", 2, \"border-left\", \"4px solid #4a90e2\", \"background-color\", \"#f8f9ff\", \"border-radius\", \"6px\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-3\", 2, \"font-size\", \"1.2rem\"], [1, \"mb-0\", \"text-muted\", 2, \"font-size\", \"0.9rem\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"spaceName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"spaceName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u9805\\u76EE\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"location\", 1, \"label\", \"col-3\"], [\"type\", \"text\", \"id\", \"location\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6240\\u5C6C\\u5340\\u57DF...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"col-3\"], [\"id\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"custom-table\", 2, \"min-width\", \"800px\"], [1, \"table-header\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"table-actions\"], [\"class\", \"btn btn-outline-success btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"border-bottom\", \"py-3\", \"px-4\"], [1, \"mb-0\", \"text-primary\", \"font-weight-bold\"], [1, \"fas\", \"fa-plus-circle\", \"me-2\", \"text-success\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-light\", \"btn-sm\", \"rounded-circle\", 2, \"width\", \"32px\", \"height\", \"32px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"px-4\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"form-group\", \"mb-4\"], [1, \"d-flex\", \"align-items-start\"], [\"for\", \"spaceName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"flex-grow-1\", \"ml-3\"], [\"type\", \"text\", \"id\", \"spaceName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u9805\\u76EE\\u540D\\u7A31\", \"name\", \"spaceName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"location\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"location\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6240\\u5C6C\\u5340\\u57DF\", \"name\", \"location\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"status\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"w-full\"], [\"id\", \"status\", \"name\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\", \"me-2\"], [1, \"d-flex\", \"justify-content-end\", \"border-top\", \"pt-3\", \"px-4\", \"pb-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"btn\", \"btn-outline-secondary\", \"me-3\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", \"background\", \"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [1, \"fas\", \"fa-edit\", \"me-2\", \"text-warning\"], [\"for\", \"locationEdit\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"locationEdit\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6240\\u5C6C\\u5340\\u57DF\", \"name\", \"location\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"spaceNameEdit\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"type\", \"text\", \"id\", \"spaceNameEdit\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u9805\\u76EE\\u540D\\u7A31\", \"name\", \"spaceName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"statusEdit\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"id\", \"statusEdit\", \"name\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-save\", \"me-1\"]],\n      template: function SpaceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 3)(5, \"div\", 4);\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵelementStart(7, \"div\")(8, \"p\", 6);\n          i0.ɵɵtext(9, \" \\u5728\\u6B64\\u9801\\u9762\\u60A8\\u53EF\\u4EE5\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u7684\\u5404\\u500B\\u7A7A\\u9593\\u8CC7\\u8A0A\\uFF0C\\u5305\\u62EC\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u7A7A\\u9593\\uFF0C\\u4EE5\\u53CA\\u8A2D\\u5B9A\\u9805\\u76EE\\u540D\\u7A31\\u3001\\u6240\\u5C6C\\u5340\\u57DF\\u548C\\u555F\\u7528\\u72C0\\u614B\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"label\", 10);\n          i0.ɵɵtext(14, \"\\u9805\\u76EE\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"nb-form-field\", 11)(16, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpaceComponent_Template_input_keyup_enter_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"div\", 8)(18, \"div\", 9)(19, \"label\", 13);\n          i0.ɵɵtext(20, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"nb-form-field\", 11)(22, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_input_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchLocation, $event) || (ctx.searchLocation = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function SpaceComponent_Template_input_keyup_enter_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 8)(24, \"div\", 9)(25, \"label\", 15);\n          i0.ɵɵtext(26, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-form-field\", 11)(28, \"nb-select\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceComponent_Template_nb_select_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchStatus, $event) || (ctx.searchStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SpaceComponent_Template_nb_select_selectedChange_28_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(29, \"nb-option\", 17);\n          i0.ɵɵtext(30, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"nb-option\", 17);\n          i0.ɵɵtext(32, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-option\", 17);\n          i0.ɵɵtext(34, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(35, \"div\", 8);\n          i0.ɵɵelementStart(36, \"div\", 18)(37, \"div\", 19)(38, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function SpaceComponent_Template_button_click_38_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReset());\n          });\n          i0.ɵɵelement(39, \"i\", 21);\n          i0.ɵɵtext(40, \"\\u91CD\\u7F6E \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function SpaceComponent_Template_button_click_41_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelement(42, \"i\", 23);\n          i0.ɵɵtext(43, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 18)(45, \"div\", 24);\n          i0.ɵɵtemplate(46, SpaceComponent_button_46_Template, 3, 0, \"button\", 25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"div\", 26)(48, \"table\", 27)(49, \"thead\")(50, \"tr\", 28)(51, \"th\", 29);\n          i0.ɵɵtext(52, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"th\", 29);\n          i0.ɵɵtext(54, \"\\u9805\\u76EE\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"th\", 29);\n          i0.ɵɵtext(56, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"th\", 30);\n          i0.ɵɵtext(58, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\", 30);\n          i0.ɵɵtext(60, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(61, \"tbody\");\n          i0.ɵɵtemplate(62, SpaceComponent_tr_62_Template, 13, 9, \"tr\", 31);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(63, \"nb-card-footer\", 32)(64, \"ngx-pagination\", 33);\n          i0.ɵɵtwoWayListener(\"PageChange\", function SpaceComponent_Template_ngx_pagination_PageChange_64_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function SpaceComponent_Template_ngx_pagination_PageChange_64_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(65, SpaceComponent_ng_template_65_Template, 46, 5, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(67, SpaceComponent_ng_template_67_Template, 46, 5, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchLocation);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.spaceList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, i6.DatePipe, SharedModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i8.BreadcrumbComponent, i9.PaginationComponent],\n      styles: [\".btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: var(--color-bg-2);\\n  font-weight: 600;\\n  border-bottom: 2px solid var(--color-bg-3);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: var(--color-bg-1);\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0;\\n  border-bottom-left-radius: 0;\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--color-fg-heading);\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: var(--color-danger) !important;\\n}\\n\\n.alert-info[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.alert-info[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);\\n  transform: translateY(-1px);\\n}\\n.alert-info[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  letter-spacing: 0.3px;\\n}\\n.alert-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  line-height: 1.6;\\n  color: #6c757d !important;\\n}\\n.alert-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.mt-3[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%], \\n.btn-outline-secondary[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:hover, \\n.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.btn-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.btn-outline-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #6c757d;\\n  color: #6c757d;\\n}\\n.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: #fff;\\n}\\n\\nnb-card[style*=box-shadow][_ngcontent-%COMP%] {\\n  transition: all 0.3s ease-in-out;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  letter-spacing: 0.5px;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  transform: scale(1.05);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n  letter-spacing: 0.3px;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label.required-field[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: #dc3545;\\n  font-weight: bold;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[nbInput][_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  font-size: 0.875rem;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[nbInput][_ngcontent-%COMP%]:focus {\\n  border-color: #4a90e2;\\n  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);\\n  transform: translateY(-1px);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[nbInput][_ngcontent-%COMP%]::placeholder {\\n  color: #adb5bd;\\n  font-style: italic;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]   nb-option[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]   nb-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%]   nb-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  letter-spacing: 0.3px;\\n  transition: all 0.2s ease;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #357abd 0%, #2c5282 100%);\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: #fff;\\n}\\nnb-card[style*=box-shadow][_ngcontent-%COMP%]   nb-card-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus-within, \\nnb-select[_ngcontent-%COMP%]:focus-within {\\n  border-color: #4a90e2;\\n  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);\\n}\\n\\n@media (max-width: 576px) {\\n  nb-card[style*=\\\"width: 550px\\\"][_ngcontent-%COMP%] {\\n    width: 95vw !important;\\n    margin: 0 auto;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "BaseComponent", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "SpaceComponent_button_46_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "createModal_r4", "ɵɵreference", "ɵɵresetView", "openCreateModal", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "SpaceComponent_tr_62_button_11_Template_button_click_0_listener", "_r5", "item_r6", "$implicit", "editModal_r7", "openEditModal", "SpaceComponent_tr_62_button_12_Template_button_click_0_listener", "_r8", "deleteSpace", "ɵɵtemplate", "SpaceComponent_tr_62_button_11_Template", "SpaceComponent_tr_62_button_12_Template", "ɵɵadvance", "ɵɵtextInterpolate", "CLocation", "CName", "ɵɵtextInterpolate1", "CStatus", "ɵɵpipeBind2", "CCreateDt", "ɵɵproperty", "isUpdate", "isDelete", "SpaceComponent_ng_template_65_Template_button_click_5_listener", "ref_r10", "_r9", "dialogRef", "onClose", "ɵɵtwoWayListener", "SpaceComponent_ng_template_65_Template_input_ngModelChange_15_listener", "$event", "ɵɵtwoWayBindingSet", "spaceDetail", "SpaceComponent_ng_template_65_Template_input_ngModelChange_22_listener", "SpaceComponent_ng_template_65_Template_nb_select_ngModelChange_30_listener", "SpaceComponent_ng_template_65_Template_button_click_40_listener", "SpaceComponent_ng_template_65_Template_button_click_43_listener", "onSubmit", "ɵɵtwoWayProperty", "SpaceComponent_ng_template_67_Template_button_click_5_listener", "ref_r12", "_r11", "SpaceComponent_ng_template_67_Template_input_ngModelChange_15_listener", "SpaceComponent_ng_template_67_Template_input_ngModelChange_22_listener", "SpaceComponent_ng_template_67_Template_nb_select_ngModelChange_30_listener", "SpaceComponent_ng_template_67_Template_button_click_40_listener", "SpaceComponent_ng_template_67_Template_button_click_43_listener", "SpaceComponent", "constructor", "allow", "dialogService", "_spaceService", "_templateService", "message", "valid", "pageFirst", "pageSize", "pageIndex", "totalRecords", "spaceList", "searchKeyword", "searchLocation", "searchStatus", "templateList", "templateDetail", "availableSpaces", "selectedSpacesForTemplate", "spacePageIndex", "spacePageSize", "spaceTotalRecords", "spaceSearchKeyword", "spaceSearchLocation", "showSpacePickList", "allSpacesSelected", "ngOnInit", "getSpaceList", "apiSpaceGetSpaceListPost$Json", "body", "PageIndex", "PageSize", "pipe", "res", "Entries", "StatusCode", "TotalItems", "showErrorMSG", "Message", "subscribe", "onSearch", "onReset", "pageChanged", "newPage", "ref", "open", "item", "getSpaceById", "CSpaceID", "spaceId", "apiSpaceGetSpaceByIdPost$Json", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpaceSaveSpacePost$Json", "showSucessMSG", "close", "confirm", "apiSpaceDeleteSpacePost$Json", "CSpaceId", "clear", "required", "isStringMaxLength", "openTemplateManagementModal", "getTemplateList", "hasScroll", "apiTemplateGetTemplateListPost$Json", "map", "CTemplateId", "CTemplateName", "CUpdateDt", "CCreator", "CUpdator", "openCreateTemplateModal", "CTemplateType", "getSpacesForPickList", "selected", "onSpaceSearch", "onSpaceReset", "spacePageChanged", "toggleSpaceSelection", "space", "push", "index", "findIndex", "s", "splice", "updateAllSpacesSelectedState", "toggleAllSpaces", "for<PERSON>ach", "exists", "find", "filter", "some", "available", "every", "removeSelectedSpace", "availableSpace", "onSubmitTemplate", "templateValidation", "templateDetails", "SpaceID", "apiTemplateSaveTemplatePost$Json", "TemplateDetails", "deleteTemplate", "template", "TemplateName", "apiTemplateDeleteTemplatePost$Json", "TemplateID", "Description", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "SpaceService", "TemplateService", "i4", "MessageService", "i5", "ValidationHelper", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "SpaceComponent_Template", "rf", "ctx", "SpaceComponent_Template_input_ngModelChange_16_listener", "_r1", "SpaceComponent_Template_input_keyup_enter_16_listener", "SpaceComponent_Template_input_ngModelChange_22_listener", "SpaceComponent_Template_input_keyup_enter_22_listener", "SpaceComponent_Template_nb_select_ngModelChange_28_listener", "SpaceComponent_Template_nb_select_selectedChange_28_listener", "SpaceComponent_Template_button_click_38_listener", "SpaceComponent_Template_button_click_41_listener", "SpaceComponent_button_46_Template", "SpaceComponent_tr_62_Template", "SpaceComponent_Template_ngx_pagination_PageChange_64_listener", "SpaceComponent_ng_template_65_Template", "ɵɵtemplateRefExtractor", "SpaceComponent_ng_template_67_Template", "isCreate", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "i8", "BreadcrumbComponent", "i9", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\space\\space.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\space\\space.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { SpaceService, TemplateService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { tap } from 'rxjs';\r\nimport { GetSpaceListResponse, SaveSpaceRequest, TemplateGetListResponse, SaveTemplateArgs, SaveTemplateDetailArgs } from 'src/services/api/models';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\nexport interface TemplateItem {\r\n  CTemplateId: number;\r\n  CTemplateName: string;\r\n  CCreateDt: string;\r\n  CUpdateDt: string;\r\n  CCreator?: string | null;\r\n  CUpdator?: string | null;\r\n  CStatus?: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpacePickListItem {\r\n  CSpaceID: number;\r\n  CName: string;\r\n  CLocation?: string | null;\r\n  selected?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-space',\r\n  templateUrl: './space.component.html',\r\n  styleUrls: ['./space.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent,\r\n  ],\r\n})\r\n\r\nexport class SpaceComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _spaceService: SpaceService,\r\n    private _templateService: TemplateService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  spaceList: GetSpaceListResponse[] = [];\r\n  spaceDetail: SaveSpaceRequest = {};\r\n  searchKeyword: string = '';\r\n  searchLocation: string = '';\r\n  searchStatus: number | null = null;\r\n\r\n  // 模板相關屬性\r\n  templateList: TemplateItem[] = [];\r\n  templateDetail: SaveTemplateArgs = {};\r\n  \r\n  // 新增模板時的空間選擇器相關屬性\r\n  availableSpaces: SpacePickListItem[] = [];\r\n  selectedSpacesForTemplate: SpacePickListItem[] = [];\r\n  spacePageIndex = 1;\r\n  spacePageSize = 10;\r\n  spaceTotalRecords = 0;\r\n  spaceSearchKeyword = '';\r\n  spaceSearchLocation = '';\r\n  \r\n  // 控制UI顯示\r\n  showSpacePickList = false;\r\n  allSpacesSelected = false;\r\n\r\n  override ngOnInit(): void {\r\n    this.getSpaceList();\r\n  }\r\n\r\n  getSpaceList() {\r\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\r\n      body: {\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CName: this.searchKeyword || null,\r\n        CLocation: this.searchLocation || null,\r\n        CStatus: this.searchStatus\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.spaceList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSearch() {\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  onReset() {\r\n    this.searchKeyword = '';\r\n    this.searchLocation = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  openCreateModal(ref: any) {\r\n    this.spaceDetail = {\r\n      CName: '',\r\n      CLocation: '',\r\n      CStatus: 1 // 1 = 啟用, 0 = 停用\r\n    };\r\n    this.dialogService.open(ref);\r\n  }\r\n\r\n  openEditModal(ref: any, item: any) {\r\n    this.getSpaceById(item.CSpaceID, ref);\r\n  }\r\n\r\n  getSpaceById(spaceId: number, ref: any) {\r\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\r\n      body: { CSpaceID: spaceId }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.spaceDetail = { ...res.Entries };\r\n        this.dialogService.open(ref);\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this._spaceService.apiSpaceSaveSpacePost$Json({\r\n      body: this.spaceDetail\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  deleteSpace(item: any) {\r\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\r\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\r\n        body: { CSpaceID: item.CSpaceId }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"刪除成功\");\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '刪除失敗');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[空間名稱]', this.spaceDetail.CName);\r\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CName, 50);\r\n    this.valid.required('[所屬區域]', this.spaceDetail.CLocation);\r\n    this.valid.isStringMaxLength('[所屬區域]', this.spaceDetail.CLocation, 50);\r\n  }\r\n\r\n  // 模板管理方法\r\n  openTemplateManagementModal(ref: any) {\r\n    this.getTemplateList();\r\n    this.dialogService.open(ref, { hasScroll: true });\r\n  }\r\n\r\n  getTemplateList() {\r\n    this._templateService.apiTemplateGetTemplateListPost$Json({\r\n      body: {\r\n        PageIndex: 1,\r\n        PageSize: 100 // 先載入所有模板\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode === 0) {\r\n        this.templateList = res.Entries.map(item => ({\r\n          CTemplateId: item.CTemplateId!,\r\n          CTemplateName: item.CTemplateName!,\r\n          CCreateDt: item.CCreateDt!,\r\n          CUpdateDt: item.CUpdateDt!,\r\n          CCreator: item.CCreator,\r\n          CUpdator: item.CUpdator,\r\n          CStatus: item.CStatus\r\n        }));\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入模板資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  openCreateTemplateModal(ref: any) {\r\n    this.templateDetail = {\r\n      CTemplateName: '',\r\n      CTemplateType: 1,\r\n      CStatus: 1\r\n    };\r\n    this.selectedSpacesForTemplate = [];\r\n    this.getSpacesForPickList();\r\n    this.dialogService.open(ref, { hasScroll: true });\r\n  }\r\n\r\n  getSpacesForPickList() {\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\r\n      body: {\r\n        PageIndex: this.spacePageIndex,\r\n        PageSize: this.spacePageSize,\r\n        CName: this.spaceSearchKeyword || null,\r\n        CLocation: this.spaceSearchLocation || null,\r\n        CStatus: 1 // 只顯示啟用的空間\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode === 0) {\r\n        this.availableSpaces = res.Entries.map(item => ({\r\n          CSpaceID: item.CSpaceID!,\r\n          CName: item.CName!,\r\n          CLocation: item.CLocation,\r\n          selected: false\r\n        }));\r\n        this.spaceTotalRecords = res.TotalItems!;\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入空間資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  onSpaceSearch() {\r\n    this.spacePageIndex = 1;\r\n    this.getSpacesForPickList();\r\n  }\r\n\r\n  onSpaceReset() {\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.spacePageIndex = 1;\r\n    this.getSpacesForPickList();\r\n  }\r\n\r\n  spacePageChanged(newPage: number) {\r\n    this.spacePageIndex = newPage;\r\n    this.getSpacesForPickList();\r\n  }\r\n\r\n  toggleSpaceSelection(space: SpacePickListItem) {\r\n    space.selected = !space.selected;\r\n    if (space.selected) {\r\n      this.selectedSpacesForTemplate.push({ ...space });\r\n    } else {\r\n      const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\r\n      if (index > -1) {\r\n        this.selectedSpacesForTemplate.splice(index, 1);\r\n      }\r\n    }\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  toggleAllSpaces() {\r\n    this.allSpacesSelected = !this.allSpacesSelected;\r\n    this.availableSpaces.forEach(space => {\r\n      space.selected = this.allSpacesSelected;\r\n      if (this.allSpacesSelected) {\r\n        const exists = this.selectedSpacesForTemplate.find(s => s.CSpaceID === space.CSpaceID);\r\n        if (!exists) {\r\n          this.selectedSpacesForTemplate.push({ ...space });\r\n        }\r\n      }\r\n    });\r\n    \r\n    if (!this.allSpacesSelected) {\r\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(selected => \r\n        !this.availableSpaces.some(available => available.CSpaceID === selected.CSpaceID)\r\n      );\r\n    }\r\n  }\r\n\r\n  updateAllSpacesSelectedState() {\r\n    this.allSpacesSelected = this.availableSpaces.length > 0 && \r\n      this.availableSpaces.every(space => space.selected);\r\n  }\r\n\r\n  removeSelectedSpace(space: SpacePickListItem) {\r\n    const index = this.selectedSpacesForTemplate.findIndex(s => s.CSpaceID === space.CSpaceID);\r\n    if (index > -1) {\r\n      this.selectedSpacesForTemplate.splice(index, 1);\r\n    }\r\n    \r\n    // 更新可用空間列表中的選擇狀態\r\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\r\n    if (availableSpace) {\r\n      availableSpace.selected = false;\r\n    }\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  onSubmitTemplate(ref: any) {\r\n    this.templateValidation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    if (this.selectedSpacesForTemplate.length === 0) {\r\n      this.message.showErrorMSG('請至少選擇一個空間');\r\n      return;\r\n    }\r\n\r\n    const templateDetails: SaveTemplateDetailArgs[] = this.selectedSpacesForTemplate.map(space => ({\r\n      SpaceID: space.CSpaceID\r\n    }));\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({\r\n      body: {\r\n        ...this.templateDetail,\r\n        TemplateDetails: templateDetails\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"模板建立成功\");\r\n        ref.close();\r\n        this.getTemplateList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '建立模板失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  deleteTemplate(template: TemplateItem) {\r\n    if (confirm(`您確定要刪除模板「${template.TemplateName}」嗎？`)) {\r\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\r\n        body: { TemplateID: template.TemplateID }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"模板刪除成功\");\r\n          this.getTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '刪除模板失敗');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  templateValidation() {\r\n    this.valid.clear();\r\n    this.valid.required('[模板名稱]', this.templateDetail.TemplateName);\r\n    this.valid.isStringMaxLength('[模板名稱]', this.templateDetail.TemplateName, 100);\r\n    if (this.templateDetail.Description) {\r\n      this.valid.isStringMaxLength('[描述]', this.templateDetail.Description, 500);\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"alert alert-info mb-4\"\r\n      style=\"border-left: 4px solid #4a90e2; background-color: #f8f9ff; border-radius: 6px;\">\r\n      <div class=\"d-flex align-items-center\">\r\n        <i class=\"fas fa-info-circle text-primary me-3\" style=\"font-size: 1.2rem;\"></i>\r\n        <div>\r\n          <p class=\"mb-0 text-muted\" style=\"font-size: 0.9rem;\">\r\n            在此頁面您可以管理系統中的各個空間資訊，包括新增、編輯、刪除空間，以及設定項目名稱、所屬區域和啟用狀態等。\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"spaceName\" class=\"label col-3\">項目名稱</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"spaceName\" nbInput class=\"w-full\" placeholder=\"搜尋項目名稱...\" [(ngModel)]=\"searchKeyword\"\r\n              (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"location\" class=\"label col-3\">所屬區域</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"location\" nbInput class=\"w-full\" placeholder=\"搜尋所屬區域...\" [(ngModel)]=\"searchLocation\"\r\n              (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"status\" class=\"label col-3\">狀態</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"status\" placeholder=\"選擇狀態...\" [(ngModel)]=\"searchStatus\" (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">啟用</nb-option>\r\n              <nb-option [value]=\"0\">停用</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢和重置按鈕移到這裡，放在搜尋條件的右下角 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-outline-secondary btn-sm me-2\" (click)=\"onReset()\">\r\n            <i class=\"fas fa-undo me-1\"></i>重置\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search me-1\"></i>查詢\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openCreateModal(createModal)\">\r\n            <i class=\"fas fa-plus me-1\"></i>新增空間\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table custom-table\" style=\"min-width: 800px;\">\r\n        <thead>\r\n          <tr class=\"table-header\">\r\n            <th scope=\"col\" class=\"col-2\">所屬區域</th>\r\n            <th scope=\"col\" class=\"col-2\">項目名稱</th>\r\n            <th scope=\"col\" class=\"col-2\">狀態</th>\r\n            <th scope=\"col\" class=\"col-3\">建立時間</th>\r\n            <th scope=\"col\" class=\"col-3\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of spaceList\">\r\n            <td>{{ item.CLocation || '-' }}</td>\r\n            <td>{{ item.CName }}</td>\r\n            <td>\r\n              {{ item.CStatus === 1 ? '啟用' : '停用' }}\r\n            </td>\r\n            <td>{{ item.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>\r\n            <td class=\"table-actions\">\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-success btn-sm\" (click)=\"openEditModal(editModal, item)\">\r\n                <i class=\"fas fa-edit\"></i>編輯\r\n              </button>\r\n              <button *ngIf=\"isDelete\" class=\"btn btn-outline-danger btn-sm\" (click)=\"deleteSpace(item)\">\r\n                <i class=\"fas fa-trash\"></i>刪除\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\" [CollectionSize]=\"totalRecords\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<!-- 新增模態框 -->\r\n<ng-template #createModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-plus-circle me-2 text-success\"></i>新增空間\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"spaceName\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                項目名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"spaceName\" class=\"form-control\" nbInput placeholder=\"請輸入項目名稱\"\r\n                  [(ngModel)]=\"spaceDetail.CName\" name=\"spaceName\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"location\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                所屬區域\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"location\" class=\"form-control\" nbInput placeholder=\"請輸入所屬區域\"\r\n                  [(ngModel)]=\"spaceDetail.CLocation\" name=\"location\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"status\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"status\" [(ngModel)]=\"spaceDetail.CStatus\" name=\"status\" placeholder=\"選擇狀態\"\r\n                    style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-check me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 編輯模態框 -->\r\n<ng-template #editModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-edit me-2 text-warning\"></i>編輯空間\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"locationEdit\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                所屬區域\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"locationEdit\" class=\"form-control\" nbInput placeholder=\"請輸入所屬區域\"\r\n                  [(ngModel)]=\"spaceDetail.CLocation\" name=\"location\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"spaceNameEdit\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                項目名稱\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <input type=\"text\" id=\"spaceNameEdit\" class=\"form-control\" nbInput placeholder=\"請輸入項目名稱\"\r\n                  [(ngModel)]=\"spaceDetail.CName\" name=\"spaceName\"\r\n                  style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"d-flex align-items-start\">\r\n              <label for=\"statusEdit\" class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">\r\n                狀態\r\n              </label>\r\n              <div class=\"flex-grow-1 ml-3\">\r\n                <nb-form-field class=\"w-full\">\r\n                  <nb-select id=\"statusEdit\" [(ngModel)]=\"spaceDetail.CStatus\" name=\"status\" placeholder=\"選擇狀態\"\r\n                    style=\"height: 42px;\">\r\n                    <nb-option [value]=\"1\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                      </span>\r\n                    </nb-option>\r\n                    <nb-option [value]=\"0\">\r\n                      <span class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                      </span>\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </nb-form-field>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-save me-1\"></i>確認\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,kCAAkC;AAMhE,SAASC,GAAG,QAAQ,MAAM;;;;;;;;;;;;;;IC0DhBC,EAAA,CAAAC,cAAA,iBAAiG;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,0DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,cAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,eAAA,CAAAH,cAAA,CAA4B;IAAA,EAAC;IAC9FR,EAAA,CAAAY,SAAA,YAAgC;IAAAZ,EAAA,CAAAa,MAAA,gCAClC;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAyBLd,EAAA,CAAAC,cAAA,iBAAyG;IAAzCD,EAAA,CAAAE,UAAA,mBAAAa,gEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAY,YAAA,GAAAnB,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAc,aAAA,CAAAD,YAAA,EAAAF,OAAA,CAA8B;IAAA,EAAC;IACtGjB,EAAA,CAAAY,SAAA,YAA2B;IAAAZ,EAAA,CAAAa,MAAA,oBAC7B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IACTd,EAAA,CAAAC,cAAA,iBAA2F;IAA5BD,EAAA,CAAAE,UAAA,mBAAAmB,gEAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA;MAAA,MAAAL,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiB,WAAA,CAAAN,OAAA,CAAiB;IAAA,EAAC;IACxFjB,EAAA,CAAAY,SAAA,YAA4B;IAAAZ,EAAA,CAAAa,MAAA,oBAC9B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;IAZXd,EADF,CAAAC,cAAA,SAAmC,SAC7B;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpCd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACzBd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAA+C;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACxDd,EAAA,CAAAC,cAAA,cAA0B;IAIxBD,EAHA,CAAAwB,UAAA,KAAAC,uCAAA,qBAAyG,KAAAC,uCAAA,qBAGd;IAI/F1B,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAdCd,EAAA,CAAA2B,SAAA,GAA2B;IAA3B3B,EAAA,CAAA4B,iBAAA,CAAAX,OAAA,CAAAY,SAAA,QAA2B;IAC3B7B,EAAA,CAAA2B,SAAA,GAAgB;IAAhB3B,EAAA,CAAA4B,iBAAA,CAAAX,OAAA,CAAAa,KAAA,CAAgB;IAElB9B,EAAA,CAAA2B,SAAA,GACF;IADE3B,EAAA,CAAA+B,kBAAA,MAAAd,OAAA,CAAAe,OAAA,8CACF;IACIhC,EAAA,CAAA2B,SAAA,GAA+C;IAA/C3B,EAAA,CAAA4B,iBAAA,CAAA5B,EAAA,CAAAiC,WAAA,OAAAhB,OAAA,CAAAiB,SAAA,sBAA+C;IAExClC,EAAA,CAAA2B,SAAA,GAAc;IAAd3B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,QAAA,CAAc;IAGdpC,EAAA,CAAA2B,SAAA,EAAc;IAAd3B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA+B,QAAA,CAAc;;;;;;IAoB/BrC,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,YAAoD;IAAAZ,EAAA,CAAAa,MAAA,gCACtD;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAoC,+DAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAE5FvC,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAOPd,EALV,CAAAC,cAAA,uBAAgC,cACb,cACK,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAA2C,gBAAA,2BAAAC,uEAAAC,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,EAAAe,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,GAAAe,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAA+B;IAKzC7C,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAKAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAA2C,gBAAA,2BAAAK,uEAAAH,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,EAAAgB,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,GAAAgB,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAAmC;IAK7C7C,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAKAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,qBAEJ;IADDD,EAAA,CAAA2C,gBAAA,2BAAAM,2EAAAJ,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAf,OAAA,EAAAa,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAf,OAAA,GAAAa,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAAiC;IAGpD7C,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IASlBb,EATkB,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAAgD,gEAAA;MAAA,MAAAX,OAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAExEvC,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAAiD,gEAAA;MAAA,MAAAZ,OAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA8C,QAAA,CAAAb,OAAA,CAAa;IAAA,EAAC;IAE1DvC,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA/DMd,EAAA,CAAA2B,SAAA,IAA+B;IAA/B3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,CAA+B;IAgB/B9B,EAAA,CAAA2B,SAAA,GAAmC;IAAnC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,CAAmC;IAgBZ7B,EAAA,CAAA2B,SAAA,GAAiC;IAAjC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAf,OAAA,CAAiC;IAE3ChC,EAAA,CAAA2B,SAAA,EAAW;IAAX3B,EAAA,CAAAmC,UAAA,YAAW;IAKXnC,EAAA,CAAA2B,SAAA,GAAW;IAAX3B,EAAA,CAAAmC,UAAA,YAAW;;;;;;IA+BpCnC,EAFJ,CAAAC,cAAA,kBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,YAA6C;IAAAZ,EAAA,CAAAa,MAAA,gCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAoD,+DAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAI,aAAA,CAAAoD,IAAA,EAAAf,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAa,OAAA,CAAY;IAAA,EAAC;IAE5FvD,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAOPd,EALV,CAAAC,cAAA,uBAAgC,cACb,cACK,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAA2C,gBAAA,2BAAAc,uEAAAZ,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,EAAAgB,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,GAAAgB,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAAmC;IAK7C7C,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAKAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,eAA8B,iBAG6C;IADvED,EAAA,CAAA2C,gBAAA,2BAAAe,uEAAAb,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,EAAAe,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,GAAAe,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAA+B;IAKzC7C,EANQ,CAAAc,YAAA,EAEyE,EACrE,EACF,EACF,EACF;IAKAd,EAHN,CAAAC,cAAA,eAAoB,eACW,eACW,iBAE2B;IAC7DD,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGJd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,qBAEJ;IADGD,EAAA,CAAA2C,gBAAA,2BAAAgB,2EAAAd,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8C,kBAAA,CAAAxC,MAAA,CAAAyC,WAAA,CAAAf,OAAA,EAAAa,MAAA,MAAAvC,MAAA,CAAAyC,WAAA,CAAAf,OAAA,GAAAa,MAAA;MAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;IAAA,EAAiC;IAGxD7C,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IASlBb,EATkB,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAA0D,gEAAA;MAAA,MAAAL,OAAA,GAAAvD,EAAA,CAAAI,aAAA,CAAAoD,IAAA,EAAAf,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,OAAA,CAAAa,OAAA,CAAY;IAAA,EAAC;IAExEvD,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAA2D,gEAAA;MAAA,MAAAN,OAAA,GAAAvD,EAAA,CAAAI,aAAA,CAAAoD,IAAA,EAAAf,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA8C,QAAA,CAAAG,OAAA,CAAa;IAAA,EAAC;IAE1DvD,EAAA,CAAAY,SAAA,aAAgC;IAAAZ,EAAA,CAAAa,MAAA,qBAClC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA/DMd,EAAA,CAAA2B,SAAA,IAAmC;IAAnC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAlB,SAAA,CAAmC;IAgBnC7B,EAAA,CAAA2B,SAAA,GAA+B;IAA/B3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAjB,KAAA,CAA+B;IAgBJ9B,EAAA,CAAA2B,SAAA,GAAiC;IAAjC3B,EAAA,CAAAqD,gBAAA,YAAA/C,MAAA,CAAAyC,WAAA,CAAAf,OAAA,CAAiC;IAE/ChC,EAAA,CAAA2B,SAAA,EAAW;IAAX3B,EAAA,CAAAmC,UAAA,YAAW;IAKXnC,EAAA,CAAA2B,SAAA,GAAW;IAAX3B,EAAA,CAAAmC,UAAA,YAAW;;;AD1N1C,OAAM,MAAO2B,cAAe,SAAQhE,aAAa;EAC/CiE,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,aAA2B,EAC3BC,gBAAiC,EACjCC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAKN,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,SAAS,GAA2B,EAAE;IACtC,KAAA3B,WAAW,GAAqB,EAAE;IAClC,KAAA4B,aAAa,GAAW,EAAE;IAC1B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAkB,IAAI;IAElC;IACA,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAAC,cAAc,GAAqB,EAAE;IAErC;IACA,KAAAC,eAAe,GAAwB,EAAE;IACzC,KAAAC,yBAAyB,GAAwB,EAAE;IACnD,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,mBAAmB,GAAG,EAAE;IAExB;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,iBAAiB,GAAG,KAAK;EA5BzB;EA8BSC,QAAQA,CAAA;IACf,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,OAAO,IAAI,CAACxB,aAAa,CAACyB,6BAA6B,CAAC;MACtDC,IAAI,EAAE;QACJC,SAAS,EAAE,IAAI,CAACrB,SAAS;QACzBsB,QAAQ,EAAE,IAAI,CAACvB,QAAQ;QACvBzC,KAAK,EAAE,IAAI,CAAC6C,aAAa,IAAI,IAAI;QACjC9C,SAAS,EAAE,IAAI,CAAC+C,cAAc,IAAI,IAAI;QACtC5C,OAAO,EAAE,IAAI,CAAC6C;;KAEjB,CAAC,CAACkB,IAAI,CACLhG,GAAG,CAACiG,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACxB,SAAS,GAAGsB,GAAG,CAACC,OAAO;QAC5B,IAAI,CAACxB,YAAY,GAAGuB,GAAG,CAACG,UAAW;MACrC,CAAC,MAAM;QACL,IAAI,CAAC/B,OAAO,CAACgC,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC/B,SAAS,GAAG,CAAC;IAClB,IAAI,CAACkB,YAAY,EAAE;EACrB;EAEAc,OAAOA,CAAA;IACL,IAAI,CAAC7B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACL,SAAS,GAAG,CAAC;IAClB,IAAI,CAACkB,YAAY,EAAE;EACrB;EAEAe,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAClC,SAAS,GAAGkC,OAAO;IACxB,IAAI,CAAChB,YAAY,EAAE;EACrB;EAEA/E,eAAeA,CAACgG,GAAQ;IACtB,IAAI,CAAC5D,WAAW,GAAG;MACjBjB,KAAK,EAAE,EAAE;MACTD,SAAS,EAAE,EAAE;MACbG,OAAO,EAAE,CAAC,CAAC;KACZ;IACD,IAAI,CAACiC,aAAa,CAAC2C,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEAvF,aAAaA,CAACuF,GAAQ,EAAEE,IAAS;IAC/B,IAAI,CAACC,YAAY,CAACD,IAAI,CAACE,QAAQ,EAAEJ,GAAG,CAAC;EACvC;EAEAG,YAAYA,CAACE,OAAe,EAAEL,GAAQ;IACpC,IAAI,CAACzC,aAAa,CAAC+C,6BAA6B,CAAC;MAC/CrB,IAAI,EAAE;QAAEmB,QAAQ,EAAEC;MAAO;KAC1B,CAAC,CAACV,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACnD,WAAW,GAAG;UAAE,GAAGiD,GAAG,CAACC;QAAO,CAAE;QACrC,IAAI,CAAChC,aAAa,CAAC2C,IAAI,CAACD,GAAG,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAACvC,OAAO,CAACgC,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEAjD,QAAQA,CAACuD,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC7C,KAAK,CAAC8C,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAChD,OAAO,CAACiD,aAAa,CAAC,IAAI,CAAChD,KAAK,CAAC8C,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACjD,aAAa,CAACoD,0BAA0B,CAAC;MAC5C1B,IAAI,EAAE,IAAI,CAAC7C;KACZ,CAAC,CAACgD,IAAI,CACLhG,GAAG,CAACiG,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC9B,OAAO,CAACmD,aAAa,CAAC,MAAM,CAAC;QAClCZ,GAAG,CAACa,KAAK,EAAE;QACX,IAAI,CAAC9B,YAAY,EAAE;MACrB,CAAC,MAAM;QACL,IAAI,CAACtB,OAAO,CAACgC,YAAY,CAACJ,GAAG,CAACK,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA/E,WAAWA,CAACsF,IAAS;IACnB,IAAIY,OAAO,CAAC,eAAe,CAAC,EAAE;MAC5B,IAAI,CAACvD,aAAa,CAACwD,4BAA4B,CAAC;QAC9C9B,IAAI,EAAE;UAAEmB,QAAQ,EAAEF,IAAI,CAACc;QAAQ;OAChC,CAAC,CAACrB,SAAS,CAACN,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAAC9B,OAAO,CAACmD,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAAC7B,YAAY,EAAE;QACrB,CAAC,MAAM;UACL,IAAI,CAACtB,OAAO,CAACgC,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,MAAM,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EACF;EAEAa,UAAUA,CAAA;IACR,IAAI,CAAC7C,KAAK,CAACuD,KAAK,EAAE;IAClB,IAAI,CAACvD,KAAK,CAACwD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAACjB,KAAK,CAAC;IACrD,IAAI,CAACuC,KAAK,CAACyD,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC/E,WAAW,CAACjB,KAAK,EAAE,EAAE,CAAC;IAClE,IAAI,CAACuC,KAAK,CAACwD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAAClB,SAAS,CAAC;IACzD,IAAI,CAACwC,KAAK,CAACyD,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC/E,WAAW,CAAClB,SAAS,EAAE,EAAE,CAAC;EACxE;EAEA;EACAkG,2BAA2BA,CAACpB,GAAQ;IAClC,IAAI,CAACqB,eAAe,EAAE;IACtB,IAAI,CAAC/D,aAAa,CAAC2C,IAAI,CAACD,GAAG,EAAE;MAAEsB,SAAS,EAAE;IAAI,CAAE,CAAC;EACnD;EAEAD,eAAeA,CAAA;IACb,IAAI,CAAC7D,gBAAgB,CAAC+D,mCAAmC,CAAC;MACxDtC,IAAI,EAAE;QACJC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,GAAG,CAAC;;KAEjB,CAAC,CAACQ,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACvC,IAAI,CAACpB,YAAY,GAAGkB,GAAG,CAACC,OAAO,CAACkC,GAAG,CAACtB,IAAI,KAAK;UAC3CuB,WAAW,EAAEvB,IAAI,CAACuB,WAAY;UAC9BC,aAAa,EAAExB,IAAI,CAACwB,aAAc;UAClCnG,SAAS,EAAE2E,IAAI,CAAC3E,SAAU;UAC1BoG,SAAS,EAAEzB,IAAI,CAACyB,SAAU;UAC1BC,QAAQ,EAAE1B,IAAI,CAAC0B,QAAQ;UACvBC,QAAQ,EAAE3B,IAAI,CAAC2B,QAAQ;UACvBxG,OAAO,EAAE6E,IAAI,CAAC7E;SACf,CAAC,CAAC;MACL,CAAC,MAAM;QACL,IAAI,CAACoC,OAAO,CAACgC,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,UAAU,CAAC;MACtD;IACF,CAAC,CAAC;EACJ;EAEAoC,uBAAuBA,CAAC9B,GAAQ;IAC9B,IAAI,CAAC5B,cAAc,GAAG;MACpBsD,aAAa,EAAE,EAAE;MACjBK,aAAa,EAAE,CAAC;MAChB1G,OAAO,EAAE;KACV;IACD,IAAI,CAACiD,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAAC0D,oBAAoB,EAAE;IAC3B,IAAI,CAAC1E,aAAa,CAAC2C,IAAI,CAACD,GAAG,EAAE;MAAEsB,SAAS,EAAE;IAAI,CAAE,CAAC;EACnD;EAEAU,oBAAoBA,CAAA;IAClB,IAAI,CAACzE,aAAa,CAACyB,6BAA6B,CAAC;MAC/CC,IAAI,EAAE;QACJC,SAAS,EAAE,IAAI,CAACX,cAAc;QAC9BY,QAAQ,EAAE,IAAI,CAACX,aAAa;QAC5BrD,KAAK,EAAE,IAAI,CAACuD,kBAAkB,IAAI,IAAI;QACtCxD,SAAS,EAAE,IAAI,CAACyD,mBAAmB,IAAI,IAAI;QAC3CtD,OAAO,EAAE,CAAC,CAAC;;KAEd,CAAC,CAACsE,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACvC,IAAI,CAAClB,eAAe,GAAGgB,GAAG,CAACC,OAAO,CAACkC,GAAG,CAACtB,IAAI,KAAK;UAC9CE,QAAQ,EAAEF,IAAI,CAACE,QAAS;UACxBjF,KAAK,EAAE+E,IAAI,CAAC/E,KAAM;UAClBD,SAAS,EAAEgF,IAAI,CAAChF,SAAS;UACzB+G,QAAQ,EAAE;SACX,CAAC,CAAC;QACH,IAAI,CAACxD,iBAAiB,GAAGY,GAAG,CAACG,UAAW;MAC1C,CAAC,MAAM;QACL,IAAI,CAAC/B,OAAO,CAACgC,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,UAAU,CAAC;MACtD;IACF,CAAC,CAAC;EACJ;EAEAwC,aAAaA,CAAA;IACX,IAAI,CAAC3D,cAAc,GAAG,CAAC;IACvB,IAAI,CAACyD,oBAAoB,EAAE;EAC7B;EAEAG,YAAYA,CAAA;IACV,IAAI,CAACzD,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACJ,cAAc,GAAG,CAAC;IACvB,IAAI,CAACyD,oBAAoB,EAAE;EAC7B;EAEAI,gBAAgBA,CAACrC,OAAe;IAC9B,IAAI,CAACxB,cAAc,GAAGwB,OAAO;IAC7B,IAAI,CAACiC,oBAAoB,EAAE;EAC7B;EAEAK,oBAAoBA,CAACC,KAAwB;IAC3CA,KAAK,CAACL,QAAQ,GAAG,CAACK,KAAK,CAACL,QAAQ;IAChC,IAAIK,KAAK,CAACL,QAAQ,EAAE;MAClB,IAAI,CAAC3D,yBAAyB,CAACiE,IAAI,CAAC;QAAE,GAAGD;MAAK,CAAE,CAAC;IACnD,CAAC,MAAM;MACL,MAAME,KAAK,GAAG,IAAI,CAAClE,yBAAyB,CAACmE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACtC,QAAQ,KAAKkC,KAAK,CAAClC,QAAQ,CAAC;MAC1F,IAAIoC,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAAClE,yBAAyB,CAACqE,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACjD;IACF;IACA,IAAI,CAACI,4BAA4B,EAAE;EACrC;EAEAC,eAAeA,CAAA;IACb,IAAI,CAAChE,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAChD,IAAI,CAACR,eAAe,CAACyE,OAAO,CAACR,KAAK,IAAG;MACnCA,KAAK,CAACL,QAAQ,GAAG,IAAI,CAACpD,iBAAiB;MACvC,IAAI,IAAI,CAACA,iBAAiB,EAAE;QAC1B,MAAMkE,MAAM,GAAG,IAAI,CAACzE,yBAAyB,CAAC0E,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACtC,QAAQ,KAAKkC,KAAK,CAAClC,QAAQ,CAAC;QACtF,IAAI,CAAC2C,MAAM,EAAE;UACX,IAAI,CAACzE,yBAAyB,CAACiE,IAAI,CAAC;YAAE,GAAGD;UAAK,CAAE,CAAC;QACnD;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAAC,IAAI,CAACzD,iBAAiB,EAAE;MAC3B,IAAI,CAACP,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAAC2E,MAAM,CAAChB,QAAQ,IAC7E,CAAC,IAAI,CAAC5D,eAAe,CAAC6E,IAAI,CAACC,SAAS,IAAIA,SAAS,CAAC/C,QAAQ,KAAK6B,QAAQ,CAAC7B,QAAQ,CAAC,CAClF;IACH;EACF;EAEAwC,4BAA4BA,CAAA;IAC1B,IAAI,CAAC/D,iBAAiB,GAAG,IAAI,CAACR,eAAe,CAACoC,MAAM,GAAG,CAAC,IACtD,IAAI,CAACpC,eAAe,CAAC+E,KAAK,CAACd,KAAK,IAAIA,KAAK,CAACL,QAAQ,CAAC;EACvD;EAEAoB,mBAAmBA,CAACf,KAAwB;IAC1C,MAAME,KAAK,GAAG,IAAI,CAAClE,yBAAyB,CAACmE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACtC,QAAQ,KAAKkC,KAAK,CAAClC,QAAQ,CAAC;IAC1F,IAAIoC,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAClE,yBAAyB,CAACqE,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IACjD;IAEA;IACA,MAAMc,cAAc,GAAG,IAAI,CAACjF,eAAe,CAAC2E,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACtC,QAAQ,KAAKkC,KAAK,CAAClC,QAAQ,CAAC;IACpF,IAAIkD,cAAc,EAAE;MAClBA,cAAc,CAACrB,QAAQ,GAAG,KAAK;IACjC;IACA,IAAI,CAACW,4BAA4B,EAAE;EACrC;EAEAW,gBAAgBA,CAACvD,GAAQ;IACvB,IAAI,CAACwD,kBAAkB,EAAE;IACzB,IAAI,IAAI,CAAC9F,KAAK,CAAC8C,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAChD,OAAO,CAACiD,aAAa,CAAC,IAAI,CAAChD,KAAK,CAAC8C,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,IAAI,CAAClC,yBAAyB,CAACmC,MAAM,KAAK,CAAC,EAAE;MAC/C,IAAI,CAAChD,OAAO,CAACgC,YAAY,CAAC,WAAW,CAAC;MACtC;IACF;IAEA,MAAMgE,eAAe,GAA6B,IAAI,CAACnF,yBAAyB,CAACkD,GAAG,CAACc,KAAK,KAAK;MAC7FoB,OAAO,EAAEpB,KAAK,CAAClC;KAChB,CAAC,CAAC;IAEH,IAAI,CAAC5C,gBAAgB,CAACmG,gCAAgC,CAAC;MACrD1E,IAAI,EAAE;QACJ,GAAG,IAAI,CAACb,cAAc;QACtBwF,eAAe,EAAEH;;KAEpB,CAAC,CAAC9D,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC9B,OAAO,CAACmD,aAAa,CAAC,QAAQ,CAAC;QACpCZ,GAAG,CAACa,KAAK,EAAE;QACX,IAAI,CAACQ,eAAe,EAAE;MACxB,CAAC,MAAM;QACL,IAAI,CAAC5D,OAAO,CAACgC,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEAmE,cAAcA,CAACC,QAAsB;IACnC,IAAIhD,OAAO,CAAC,YAAYgD,QAAQ,CAACC,YAAY,KAAK,CAAC,EAAE;MACnD,IAAI,CAACvG,gBAAgB,CAACwG,kCAAkC,CAAC;QACvD/E,IAAI,EAAE;UAAEgF,UAAU,EAAEH,QAAQ,CAACG;QAAU;OACxC,CAAC,CAACtE,SAAS,CAACN,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC9B,OAAO,CAACmD,aAAa,CAAC,QAAQ,CAAC;UACpC,IAAI,CAACS,eAAe,EAAE;QACxB,CAAC,MAAM;UACL,IAAI,CAAC5D,OAAO,CAACgC,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;QACpD;MACF,CAAC,CAAC;IACJ;EACF;EAEA8D,kBAAkBA,CAAA;IAChB,IAAI,CAAC9F,KAAK,CAACuD,KAAK,EAAE;IAClB,IAAI,CAACvD,KAAK,CAACwD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9C,cAAc,CAAC2F,YAAY,CAAC;IAC/D,IAAI,CAACrG,KAAK,CAACyD,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC/C,cAAc,CAAC2F,YAAY,EAAE,GAAG,CAAC;IAC7E,IAAI,IAAI,CAAC3F,cAAc,CAAC8F,WAAW,EAAE;MACnC,IAAI,CAACxG,KAAK,CAACyD,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC/C,cAAc,CAAC8F,WAAW,EAAE,GAAG,CAAC;IAC5E;EACF;EAEAnI,OAAOA,CAACiE,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;;;uCAxVW1D,cAAc,EAAA9D,EAAA,CAAA8K,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhL,EAAA,CAAA8K,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAlL,EAAA,CAAA8K,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAApL,EAAA,CAAA8K,iBAAA,CAAAK,EAAA,CAAAE,eAAA,GAAArL,EAAA,CAAA8K,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAvL,EAAA,CAAA8K,iBAAA,CAAAU,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAd3H,cAAc;MAAA4H,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA5L,EAAA,CAAA6L,0BAAA,EAAA7L,EAAA,CAAA8L,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAxB,QAAA,WAAAyB,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UChDzBnM,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,SAAA,qBAAiC;UACnCZ,EAAA,CAAAc,YAAA,EAAiB;UAIbd,EAHJ,CAAAC,cAAA,mBAAc,aAE6E,aAChD;UACrCD,EAAA,CAAAY,SAAA,WAA+E;UAE7EZ,EADF,CAAAC,cAAA,UAAK,WACmD;UACpDD,EAAA,CAAAa,MAAA,uUACF;UAGNb,EAHM,CAAAc,YAAA,EAAI,EACA,EACF,EACF;UAIAd,EAHN,CAAAC,cAAA,cAA8B,cACN,cACqC,iBACZ;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAErDd,EADF,CAAAC,cAAA,yBAA6B,iBAEE;UADoDD,EAAA,CAAA2C,gBAAA,2BAAA0J,wDAAAxJ,MAAA;YAAA7C,EAAA,CAAAI,aAAA,CAAAkM,GAAA;YAAAtM,EAAA,CAAA8C,kBAAA,CAAAsJ,GAAA,CAAAzH,aAAA,EAAA9B,MAAA,MAAAuJ,GAAA,CAAAzH,aAAA,GAAA9B,MAAA;YAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;UAAA,EAA2B;UAC1G7C,EAAA,CAAAE,UAAA,yBAAAqM,sDAAA;YAAAvM,EAAA,CAAAI,aAAA,CAAAkM,GAAA;YAAA,OAAAtM,EAAA,CAAAU,WAAA,CAAe0L,GAAA,CAAA7F,QAAA,EAAU;UAAA,EAAC;UAGlCvG,EAJM,CAAAc,YAAA,EAC6B,EACf,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACb;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAEpDd,EADF,CAAAC,cAAA,yBAA6B,iBAEE;UADmDD,EAAA,CAAA2C,gBAAA,2BAAA6J,wDAAA3J,MAAA;YAAA7C,EAAA,CAAAI,aAAA,CAAAkM,GAAA;YAAAtM,EAAA,CAAA8C,kBAAA,CAAAsJ,GAAA,CAAAxH,cAAA,EAAA/B,MAAA,MAAAuJ,GAAA,CAAAxH,cAAA,GAAA/B,MAAA;YAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;UAAA,EAA4B;UAC1G7C,EAAA,CAAAE,UAAA,yBAAAuM,sDAAA;YAAAzM,EAAA,CAAAI,aAAA,CAAAkM,GAAA;YAAA,OAAAtM,EAAA,CAAAU,WAAA,CAAe0L,GAAA,CAAA7F,QAAA,EAAU;UAAA,EAAC;UAGlCvG,EAJM,CAAAc,YAAA,EAC6B,EACf,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACf;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAEhDd,EADF,CAAAC,cAAA,yBAA6B,qBAC2E;UAAzDD,EAAA,CAAA2C,gBAAA,2BAAA+J,4DAAA7J,MAAA;YAAA7C,EAAA,CAAAI,aAAA,CAAAkM,GAAA;YAAAtM,EAAA,CAAA8C,kBAAA,CAAAsJ,GAAA,CAAAvH,YAAA,EAAAhC,MAAA,MAAAuJ,GAAA,CAAAvH,YAAA,GAAAhC,MAAA;YAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;UAAA,EAA0B;UAAC7C,EAAA,CAAAE,UAAA,4BAAAyM,6DAAA;YAAA3M,EAAA,CAAAI,aAAA,CAAAkM,GAAA;YAAA,OAAAtM,EAAA,CAAAU,WAAA,CAAkB0L,GAAA,CAAA7F,QAAA,EAAU;UAAA,EAAC;UACnGvG,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACxCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACrCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAIjCb,EAJiC,CAAAc,YAAA,EAAY,EAC3B,EACE,EACZ,EACF;UAENd,EAAA,CAAAY,SAAA,cAEM;UAKFZ,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACmB;UAApBD,EAAA,CAAAE,UAAA,mBAAA0M,iDAAA;YAAA5M,EAAA,CAAAI,aAAA,CAAAkM,GAAA;YAAA,OAAAtM,EAAA,CAAAU,WAAA,CAAS0L,GAAA,CAAA5F,OAAA,EAAS;UAAA,EAAC;UACvExG,EAAA,CAAAY,SAAA,aAAgC;UAAAZ,EAAA,CAAAa,MAAA,qBAClC;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAC,cAAA,kBAA8D;UAArBD,EAAA,CAAAE,UAAA,mBAAA2M,iDAAA;YAAA7M,EAAA,CAAAI,aAAA,CAAAkM,GAAA;YAAA,OAAAtM,EAAA,CAAAU,WAAA,CAAS0L,GAAA,CAAA7F,QAAA,EAAU;UAAA,EAAC;UAC3DvG,EAAA,CAAAY,SAAA,aAAkC;UAAAZ,EAAA,CAAAa,MAAA,qBACpC;UAEJb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAGJd,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAAwB,UAAA,KAAAsL,iCAAA,qBAAiG;UAKvG9M,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;UAMEd,EAJR,CAAAC,cAAA,eAAmC,iBAC2B,aACnD,cACoB,cACO;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAEpCb,EAFoC,CAAAc,YAAA,EAAK,EAClC,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAwB,UAAA,KAAAuL,6BAAA,kBAAmC;UAmB3C/M,EAHM,CAAAc,YAAA,EAAQ,EACF,EACJ,EACO;UAEbd,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADrBD,EAAA,CAAA2C,gBAAA,wBAAAqK,8DAAAnK,MAAA;YAAA7C,EAAA,CAAAI,aAAA,CAAAkM,GAAA;YAAAtM,EAAA,CAAA8C,kBAAA,CAAAsJ,GAAA,CAAA5H,SAAA,EAAA3B,MAAA,MAAAuJ,GAAA,CAAA5H,SAAA,GAAA3B,MAAA;YAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAAmC,MAAA;UAAA,EAAoB;UAClC7C,EAAA,CAAAE,UAAA,wBAAA8M,8DAAAnK,MAAA;YAAA7C,EAAA,CAAAI,aAAA,CAAAkM,GAAA;YAAA,OAAAtM,EAAA,CAAAU,WAAA,CAAc0L,GAAA,CAAA3F,WAAA,CAAA5D,MAAA,CAAmB;UAAA,EAAC;UAGxC7C,EAFI,CAAAc,YAAA,EAAiB,EACF,EACT;UA6FVd,EA1FA,CAAAwB,UAAA,KAAAyL,sCAAA,iCAAAjN,EAAA,CAAAkN,sBAAA,CAA8C,KAAAC,sCAAA,iCAAAnN,EAAA,CAAAkN,sBAAA,CA0FF;;;UAxLiDlN,EAAA,CAAA2B,SAAA,IAA2B;UAA3B3B,EAAA,CAAAqD,gBAAA,YAAA+I,GAAA,CAAAzH,aAAA,CAA2B;UAU5B3E,EAAA,CAAA2B,SAAA,GAA4B;UAA5B3B,EAAA,CAAAqD,gBAAA,YAAA+I,GAAA,CAAAxH,cAAA,CAA4B;UAU/D5E,EAAA,CAAA2B,SAAA,GAA0B;UAA1B3B,EAAA,CAAAqD,gBAAA,YAAA+I,GAAA,CAAAvH,YAAA,CAA0B;UAC1D7E,EAAA,CAAA2B,SAAA,EAAc;UAAd3B,EAAA,CAAAmC,UAAA,eAAc;UACdnC,EAAA,CAAA2B,SAAA,GAAW;UAAX3B,EAAA,CAAAmC,UAAA,YAAW;UACXnC,EAAA,CAAA2B,SAAA,GAAW;UAAX3B,EAAA,CAAAmC,UAAA,YAAW;UAwBgBnC,EAAA,CAAA2B,SAAA,IAAc;UAAd3B,EAAA,CAAAmC,UAAA,SAAAiK,GAAA,CAAAgB,QAAA,CAAc;UAmBnCpN,EAAA,CAAA2B,SAAA,IAAY;UAAZ3B,EAAA,CAAAmC,UAAA,YAAAiK,GAAA,CAAA1H,SAAA,CAAY;UAqBvB1E,EAAA,CAAA2B,SAAA,GAAoB;UAApB3B,EAAA,CAAAqD,gBAAA,SAAA+I,GAAA,CAAA5H,SAAA,CAAoB;UAAuBxE,EAAtB,CAAAmC,UAAA,aAAAiK,GAAA,CAAA7H,QAAA,CAAqB,mBAAA6H,GAAA,CAAA3H,YAAA,CAAgC;;;qBDjE1F5E,YAAY,EAAAwN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EACZ5N,YAAY,EAAA6N,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAA3C,EAAA,CAAA4C,eAAA,EAAA5C,EAAA,CAAA6C,mBAAA,EAAA7C,EAAA,CAAA8C,qBAAA,EAAA9C,EAAA,CAAA+C,qBAAA,EAAA/C,EAAA,CAAAgD,gBAAA,EAAAhD,EAAA,CAAAiD,iBAAA,EAAAjD,EAAA,CAAAkD,iBAAA,EAAAlD,EAAA,CAAAmD,oBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}