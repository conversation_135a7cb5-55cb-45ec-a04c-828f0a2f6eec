{"ast": null, "code": "import { DateComponent, getStickyHeaderDates, ViewContainer, SimpleScrollGrid, getStickyFooterScrollbar, renderScrollShim, createFormatter, BaseComponent, StandardEvent, buildSegTimeText, EventContainer, getSegAnchorAttrs, memoize, MoreLinkContainer, getSegMeta, getUniqueDomId, setRef, DayCellContainer, WeekNumberContainer, buildNavLinkAttrs, hasCustomDayCellContent, addMs, intersectRanges, addDays, SegHierarchy, buildEntryKey, intersectSpans, RefMap, sortEventSegs, isPropsEqual, buildEventRangeKey, BgEvent, renderFill, PositionCache, NowTimer, formatIsoMonthStr, formatDayString, Slicer, DayHeader, DaySeriesModel, DayTableModel, DateProfileGenerator, addWeeks, diffWeeks, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createRef, createElement, Fragment } from '@fullcalendar/core/preact.js';\n\n/* An abstract class for the daygrid views, as well as month view. Renders one or more rows of day cells.\n----------------------------------------------------------------------------------------------------------------------*/\n// It is a manager for a Table subcomponent, which does most of the heavy lifting.\n// It is responsible for managing width/height.\nclass TableView extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.headerElRef = createRef();\n  }\n  renderSimpleLayout(headerRowContent, bodyContent) {\n    let {\n      props,\n      context\n    } = this;\n    let sections = [];\n    let stickyHeaderDates = getStickyHeaderDates(context.options);\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        chunk: {\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent\n        }\n      });\n    }\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      chunk: {\n        content: bodyContent\n      }\n    });\n    return createElement(ViewContainer, {\n      elClasses: ['fc-daygrid'],\n      viewSpec: context.viewSpec\n    }, createElement(SimpleScrollGrid, {\n      liquid: !props.isHeightAuto && !props.forPrint,\n      collapsibleWidth: props.forPrint,\n      cols: [] /* TODO: make optional? */,\n      sections: sections\n    }));\n  }\n  renderHScrollLayout(headerRowContent, bodyContent, colCnt, dayMinWidth) {\n    let ScrollGrid = this.context.pluginHooks.scrollGridImpl;\n    if (!ScrollGrid) {\n      throw new Error('No ScrollGrid implementation');\n    }\n    let {\n      props,\n      context\n    } = this;\n    let stickyHeaderDates = !props.forPrint && getStickyHeaderDates(context.options);\n    let stickyFooterScrollbar = !props.forPrint && getStickyFooterScrollbar(context.options);\n    let sections = [];\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        chunks: [{\n          key: 'main',\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent\n        }]\n      });\n    }\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      chunks: [{\n        key: 'main',\n        content: bodyContent\n      }]\n    });\n    if (stickyFooterScrollbar) {\n      sections.push({\n        type: 'footer',\n        key: 'footer',\n        isSticky: true,\n        chunks: [{\n          key: 'main',\n          content: renderScrollShim\n        }]\n      });\n    }\n    return createElement(ViewContainer, {\n      elClasses: ['fc-daygrid'],\n      viewSpec: context.viewSpec\n    }, createElement(ScrollGrid, {\n      liquid: !props.isHeightAuto && !props.forPrint,\n      forPrint: props.forPrint,\n      collapsibleWidth: props.forPrint,\n      colGroups: [{\n        cols: [{\n          span: colCnt,\n          minWidth: dayMinWidth\n        }]\n      }],\n      sections: sections\n    }));\n  }\n}\nfunction splitSegsByRow(segs, rowCnt) {\n  let byRow = [];\n  for (let i = 0; i < rowCnt; i += 1) {\n    byRow[i] = [];\n  }\n  for (let seg of segs) {\n    byRow[seg.row].push(seg);\n  }\n  return byRow;\n}\nfunction splitSegsByFirstCol(segs, colCnt) {\n  let byCol = [];\n  for (let i = 0; i < colCnt; i += 1) {\n    byCol[i] = [];\n  }\n  for (let seg of segs) {\n    byCol[seg.firstCol].push(seg);\n  }\n  return byCol;\n}\nfunction splitInteractionByRow(ui, rowCnt) {\n  let byRow = [];\n  if (!ui) {\n    for (let i = 0; i < rowCnt; i += 1) {\n      byRow[i] = null;\n    }\n  } else {\n    for (let i = 0; i < rowCnt; i += 1) {\n      byRow[i] = {\n        affectedInstances: ui.affectedInstances,\n        isEvent: ui.isEvent,\n        segs: []\n      };\n    }\n    for (let seg of ui.segs) {\n      byRow[seg.row].segs.push(seg);\n    }\n  }\n  return byRow;\n}\nconst DEFAULT_TABLE_EVENT_TIME_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  omitZeroMinute: true,\n  meridiem: 'narrow'\n});\nfunction hasListItemDisplay(seg) {\n  let {\n    display\n  } = seg.eventRange.ui;\n  return display === 'list-item' || display === 'auto' && !seg.eventRange.def.allDay && seg.firstCol === seg.lastCol &&\n  // can't be multi-day\n  seg.isStart &&\n  // \"\n  seg.isEnd // \"\n  ;\n}\nclass TableBlockEvent extends BaseComponent {\n  render() {\n    let {\n      props\n    } = this;\n    return createElement(StandardEvent, Object.assign({}, props, {\n      elClasses: ['fc-daygrid-event', 'fc-daygrid-block-event', 'fc-h-event'],\n      defaultTimeFormat: DEFAULT_TABLE_EVENT_TIME_FORMAT,\n      defaultDisplayEventEnd: props.defaultDisplayEventEnd,\n      disableResizing: !props.seg.eventRange.def.allDay\n    }));\n  }\n}\nclass TableListItemEvent extends BaseComponent {\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let {\n      seg\n    } = props;\n    let timeFormat = options.eventTimeFormat || DEFAULT_TABLE_EVENT_TIME_FORMAT;\n    let timeText = buildSegTimeText(seg, timeFormat, context, true, props.defaultDisplayEventEnd);\n    return createElement(EventContainer, Object.assign({}, props, {\n      elTag: \"a\",\n      elClasses: ['fc-daygrid-event', 'fc-daygrid-dot-event'],\n      elAttrs: getSegAnchorAttrs(props.seg, context),\n      defaultGenerator: renderInnerContent,\n      timeText: timeText,\n      isResizing: false,\n      isDateSelecting: false\n    }));\n  }\n}\nfunction renderInnerContent(renderProps) {\n  return createElement(Fragment, null, createElement(\"div\", {\n    className: \"fc-daygrid-event-dot\",\n    style: {\n      borderColor: renderProps.borderColor || renderProps.backgroundColor\n    }\n  }), renderProps.timeText && createElement(\"div\", {\n    className: \"fc-event-time\"\n  }, renderProps.timeText), createElement(\"div\", {\n    className: \"fc-event-title\"\n  }, renderProps.event.title || createElement(Fragment, null, \"\\u00A0\")));\n}\nclass TableCellMoreLink extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.compileSegs = memoize(compileSegs);\n  }\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      allSegs,\n      invisibleSegs\n    } = this.compileSegs(props.singlePlacements);\n    return createElement(MoreLinkContainer, {\n      elClasses: ['fc-daygrid-more-link'],\n      dateProfile: props.dateProfile,\n      todayRange: props.todayRange,\n      allDayDate: props.allDayDate,\n      moreCnt: props.moreCnt,\n      allSegs: allSegs,\n      hiddenSegs: invisibleSegs,\n      alignmentElRef: props.alignmentElRef,\n      alignGridTop: props.alignGridTop,\n      extraDateSpan: props.extraDateSpan,\n      popoverContent: () => {\n        let isForcedInvisible = (props.eventDrag ? props.eventDrag.affectedInstances : null) || (props.eventResize ? props.eventResize.affectedInstances : null) || {};\n        return createElement(Fragment, null, allSegs.map(seg => {\n          let instanceId = seg.eventRange.instance.instanceId;\n          return createElement(\"div\", {\n            className: \"fc-daygrid-event-harness\",\n            key: instanceId,\n            style: {\n              visibility: isForcedInvisible[instanceId] ? 'hidden' : ''\n            }\n          }, hasListItemDisplay(seg) ? createElement(TableListItemEvent, Object.assign({\n            seg: seg,\n            isDragging: false,\n            isSelected: instanceId === props.eventSelection,\n            defaultDisplayEventEnd: false\n          }, getSegMeta(seg, props.todayRange))) : createElement(TableBlockEvent, Object.assign({\n            seg: seg,\n            isDragging: false,\n            isResizing: false,\n            isDateSelecting: false,\n            isSelected: instanceId === props.eventSelection,\n            defaultDisplayEventEnd: false\n          }, getSegMeta(seg, props.todayRange))));\n        }));\n      }\n    });\n  }\n}\nfunction compileSegs(singlePlacements) {\n  let allSegs = [];\n  let invisibleSegs = [];\n  for (let placement of singlePlacements) {\n    allSegs.push(placement.seg);\n    if (!placement.isVisible) {\n      invisibleSegs.push(placement.seg);\n    }\n  }\n  return {\n    allSegs,\n    invisibleSegs\n  };\n}\nconst DEFAULT_WEEK_NUM_FORMAT = createFormatter({\n  week: 'narrow'\n});\nclass TableCell extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.rootElRef = createRef();\n    this.state = {\n      dayNumberId: getUniqueDomId()\n    };\n    this.handleRootEl = el => {\n      setRef(this.rootElRef, el);\n      setRef(this.props.elRef, el);\n    };\n  }\n  render() {\n    let {\n      context,\n      props,\n      state,\n      rootElRef\n    } = this;\n    let {\n      options,\n      dateEnv\n    } = context;\n    let {\n      date,\n      dateProfile\n    } = props;\n    // TODO: memoize this?\n    const isMonthStart = props.showDayNumber && shouldDisplayMonthStart(date, dateProfile.currentRange, dateEnv);\n    return createElement(DayCellContainer, {\n      elTag: \"td\",\n      elRef: this.handleRootEl,\n      elClasses: ['fc-daygrid-day', ...(props.extraClassNames || [])],\n      elAttrs: Object.assign(Object.assign(Object.assign({}, props.extraDataAttrs), props.showDayNumber ? {\n        'aria-labelledby': state.dayNumberId\n      } : {}), {\n        role: 'gridcell'\n      }),\n      defaultGenerator: renderTopInner,\n      date: date,\n      dateProfile: dateProfile,\n      todayRange: props.todayRange,\n      showDayNumber: props.showDayNumber,\n      isMonthStart: isMonthStart,\n      extraRenderProps: props.extraRenderProps\n    }, (InnerContent, renderProps) => createElement(\"div\", {\n      ref: props.innerElRef,\n      className: \"fc-daygrid-day-frame fc-scrollgrid-sync-inner\",\n      style: {\n        minHeight: props.minHeight\n      }\n    }, props.showWeekNumber && createElement(WeekNumberContainer, {\n      elTag: \"a\",\n      elClasses: ['fc-daygrid-week-number'],\n      elAttrs: buildNavLinkAttrs(context, date, 'week'),\n      date: date,\n      defaultFormat: DEFAULT_WEEK_NUM_FORMAT\n    }), !renderProps.isDisabled && (props.showDayNumber || hasCustomDayCellContent(options) || props.forceDayTop) ? createElement(\"div\", {\n      className: \"fc-daygrid-day-top\"\n    }, createElement(InnerContent, {\n      elTag: \"a\",\n      elClasses: ['fc-daygrid-day-number', isMonthStart && 'fc-daygrid-month-start'],\n      elAttrs: Object.assign(Object.assign({}, buildNavLinkAttrs(context, date)), {\n        id: state.dayNumberId\n      })\n    })) : props.showDayNumber ?\n    // for creating correct amount of space (see issue #7162)\n    createElement(\"div\", {\n      className: \"fc-daygrid-day-top\",\n      style: {\n        visibility: 'hidden'\n      }\n    }, createElement(\"a\", {\n      className: \"fc-daygrid-day-number\"\n    }, \"\\u00A0\")) : undefined, createElement(\"div\", {\n      className: \"fc-daygrid-day-events\",\n      ref: props.fgContentElRef\n    }, props.fgContent, createElement(\"div\", {\n      className: \"fc-daygrid-day-bottom\",\n      style: {\n        marginTop: props.moreMarginTop\n      }\n    }, createElement(TableCellMoreLink, {\n      allDayDate: date,\n      singlePlacements: props.singlePlacements,\n      moreCnt: props.moreCnt,\n      alignmentElRef: rootElRef,\n      alignGridTop: !props.showDayNumber,\n      extraDateSpan: props.extraDateSpan,\n      dateProfile: props.dateProfile,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      todayRange: props.todayRange\n    }))), createElement(\"div\", {\n      className: \"fc-daygrid-day-bg\"\n    }, props.bgContent)));\n  }\n}\nfunction renderTopInner(props) {\n  return props.dayNumberText || createElement(Fragment, null, \"\\u00A0\");\n}\nfunction shouldDisplayMonthStart(date, currentRange, dateEnv) {\n  const {\n    start: currentStart,\n    end: currentEnd\n  } = currentRange;\n  const currentEndIncl = addMs(currentEnd, -1);\n  const currentFirstYear = dateEnv.getYear(currentStart);\n  const currentFirstMonth = dateEnv.getMonth(currentStart);\n  const currentLastYear = dateEnv.getYear(currentEndIncl);\n  const currentLastMonth = dateEnv.getMonth(currentEndIncl);\n  // spans more than one month?\n  return !(currentFirstYear === currentLastYear && currentFirstMonth === currentLastMonth) && Boolean(\n  // first date in current view?\n  date.valueOf() === currentStart.valueOf() ||\n  // a month-start that's within the current range?\n  dateEnv.getDay(date) === 1 && date.valueOf() < currentEnd.valueOf());\n}\nfunction generateSegKey(seg) {\n  return seg.eventRange.instance.instanceId + ':' + seg.firstCol;\n}\nfunction generateSegUid(seg) {\n  return generateSegKey(seg) + ':' + seg.lastCol;\n}\nfunction computeFgSegPlacement(segs,\n// assumed already sorted\ndayMaxEvents, dayMaxEventRows, strictOrder, segHeights, maxContentHeight, cells) {\n  let hierarchy = new DayGridSegHierarchy(segEntry => {\n    // TODO: more DRY with generateSegUid\n    let segUid = segs[segEntry.index].eventRange.instance.instanceId + ':' + segEntry.span.start + ':' + (segEntry.span.end - 1);\n    // if no thickness known, assume 1 (if 0, so small it always fits)\n    return segHeights[segUid] || 1;\n  });\n  hierarchy.allowReslicing = true;\n  hierarchy.strictOrder = strictOrder;\n  if (dayMaxEvents === true || dayMaxEventRows === true) {\n    hierarchy.maxCoord = maxContentHeight;\n    hierarchy.hiddenConsumes = true;\n  } else if (typeof dayMaxEvents === 'number') {\n    hierarchy.maxStackCnt = dayMaxEvents;\n  } else if (typeof dayMaxEventRows === 'number') {\n    hierarchy.maxStackCnt = dayMaxEventRows;\n    hierarchy.hiddenConsumes = true;\n  }\n  // create segInputs only for segs with known heights\n  let segInputs = [];\n  let unknownHeightSegs = [];\n  for (let i = 0; i < segs.length; i += 1) {\n    let seg = segs[i];\n    let segUid = generateSegUid(seg);\n    let eventHeight = segHeights[segUid];\n    if (eventHeight != null) {\n      segInputs.push({\n        index: i,\n        span: {\n          start: seg.firstCol,\n          end: seg.lastCol + 1\n        }\n      });\n    } else {\n      unknownHeightSegs.push(seg);\n    }\n  }\n  let hiddenEntries = hierarchy.addSegs(segInputs);\n  let segRects = hierarchy.toRects();\n  let {\n    singleColPlacements,\n    multiColPlacements,\n    leftoverMargins\n  } = placeRects(segRects, segs, cells);\n  let moreCnts = [];\n  let moreMarginTops = [];\n  // add segs with unknown heights\n  for (let seg of unknownHeightSegs) {\n    multiColPlacements[seg.firstCol].push({\n      seg,\n      isVisible: false,\n      isAbsolute: true,\n      absoluteTop: 0,\n      marginTop: 0\n    });\n    for (let col = seg.firstCol; col <= seg.lastCol; col += 1) {\n      singleColPlacements[col].push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: false,\n        isAbsolute: false,\n        absoluteTop: 0,\n        marginTop: 0\n      });\n    }\n  }\n  // add the hidden entries\n  for (let col = 0; col < cells.length; col += 1) {\n    moreCnts.push(0);\n  }\n  for (let hiddenEntry of hiddenEntries) {\n    let seg = segs[hiddenEntry.index];\n    let hiddenSpan = hiddenEntry.span;\n    multiColPlacements[hiddenSpan.start].push({\n      seg: resliceSeg(seg, hiddenSpan.start, hiddenSpan.end, cells),\n      isVisible: false,\n      isAbsolute: true,\n      absoluteTop: 0,\n      marginTop: 0\n    });\n    for (let col = hiddenSpan.start; col < hiddenSpan.end; col += 1) {\n      moreCnts[col] += 1;\n      singleColPlacements[col].push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: false,\n        isAbsolute: false,\n        absoluteTop: 0,\n        marginTop: 0\n      });\n    }\n  }\n  // deal with leftover margins\n  for (let col = 0; col < cells.length; col += 1) {\n    moreMarginTops.push(leftoverMargins[col]);\n  }\n  return {\n    singleColPlacements,\n    multiColPlacements,\n    moreCnts,\n    moreMarginTops\n  };\n}\n// rects ordered by top coord, then left\nfunction placeRects(allRects, segs, cells) {\n  let rectsByEachCol = groupRectsByEachCol(allRects, cells.length);\n  let singleColPlacements = [];\n  let multiColPlacements = [];\n  let leftoverMargins = [];\n  for (let col = 0; col < cells.length; col += 1) {\n    let rects = rectsByEachCol[col];\n    // compute all static segs in singlePlacements\n    let singlePlacements = [];\n    let currentHeight = 0;\n    let currentMarginTop = 0;\n    for (let rect of rects) {\n      let seg = segs[rect.index];\n      singlePlacements.push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: true,\n        isAbsolute: false,\n        absoluteTop: rect.levelCoord,\n        marginTop: rect.levelCoord - currentHeight\n      });\n      currentHeight = rect.levelCoord + rect.thickness;\n    }\n    // compute mixed static/absolute segs in multiPlacements\n    let multiPlacements = [];\n    currentHeight = 0;\n    currentMarginTop = 0;\n    for (let rect of rects) {\n      let seg = segs[rect.index];\n      let isAbsolute = rect.span.end - rect.span.start > 1; // multi-column?\n      let isFirstCol = rect.span.start === col;\n      currentMarginTop += rect.levelCoord - currentHeight; // amount of space since bottom of previous seg\n      currentHeight = rect.levelCoord + rect.thickness; // height will now be bottom of current seg\n      if (isAbsolute) {\n        currentMarginTop += rect.thickness;\n        if (isFirstCol) {\n          multiPlacements.push({\n            seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n            isVisible: true,\n            isAbsolute: true,\n            absoluteTop: rect.levelCoord,\n            marginTop: 0\n          });\n        }\n      } else if (isFirstCol) {\n        multiPlacements.push({\n          seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n          isVisible: true,\n          isAbsolute: false,\n          absoluteTop: rect.levelCoord,\n          marginTop: currentMarginTop // claim the margin\n        });\n        currentMarginTop = 0;\n      }\n    }\n    singleColPlacements.push(singlePlacements);\n    multiColPlacements.push(multiPlacements);\n    leftoverMargins.push(currentMarginTop);\n  }\n  return {\n    singleColPlacements,\n    multiColPlacements,\n    leftoverMargins\n  };\n}\nfunction groupRectsByEachCol(rects, colCnt) {\n  let rectsByEachCol = [];\n  for (let col = 0; col < colCnt; col += 1) {\n    rectsByEachCol.push([]);\n  }\n  for (let rect of rects) {\n    for (let col = rect.span.start; col < rect.span.end; col += 1) {\n      rectsByEachCol[col].push(rect);\n    }\n  }\n  return rectsByEachCol;\n}\nfunction resliceSeg(seg, spanStart, spanEnd, cells) {\n  if (seg.firstCol === spanStart && seg.lastCol === spanEnd - 1) {\n    return seg;\n  }\n  let eventRange = seg.eventRange;\n  let origRange = eventRange.range;\n  let slicedRange = intersectRanges(origRange, {\n    start: cells[spanStart].date,\n    end: addDays(cells[spanEnd - 1].date, 1)\n  });\n  return Object.assign(Object.assign({}, seg), {\n    firstCol: spanStart,\n    lastCol: spanEnd - 1,\n    eventRange: {\n      def: eventRange.def,\n      ui: Object.assign(Object.assign({}, eventRange.ui), {\n        durationEditable: false\n      }),\n      instance: eventRange.instance,\n      range: slicedRange\n    },\n    isStart: seg.isStart && slicedRange.start.valueOf() === origRange.start.valueOf(),\n    isEnd: seg.isEnd && slicedRange.end.valueOf() === origRange.end.valueOf()\n  });\n}\nclass DayGridSegHierarchy extends SegHierarchy {\n  constructor() {\n    super(...arguments);\n    // config\n    this.hiddenConsumes = false;\n    // allows us to keep hidden entries in the hierarchy so they take up space\n    this.forceHidden = {};\n  }\n  addSegs(segInputs) {\n    const hiddenSegs = super.addSegs(segInputs);\n    const {\n      entriesByLevel\n    } = this;\n    const excludeHidden = entry => !this.forceHidden[buildEntryKey(entry)];\n    // remove the forced-hidden segs\n    for (let level = 0; level < entriesByLevel.length; level += 1) {\n      entriesByLevel[level] = entriesByLevel[level].filter(excludeHidden);\n    }\n    return hiddenSegs;\n  }\n  handleInvalidInsertion(insertion, entry, hiddenEntries) {\n    const {\n      entriesByLevel,\n      forceHidden\n    } = this;\n    const {\n      touchingEntry,\n      touchingLevel,\n      touchingLateral\n    } = insertion;\n    // the entry that the new insertion is touching must be hidden\n    if (this.hiddenConsumes && touchingEntry) {\n      const touchingEntryId = buildEntryKey(touchingEntry);\n      if (!forceHidden[touchingEntryId]) {\n        if (this.allowReslicing) {\n          // split up the touchingEntry, reinsert it\n          const hiddenEntry = Object.assign(Object.assign({}, touchingEntry), {\n            span: intersectSpans(touchingEntry.span, entry.span)\n          });\n          // reinsert the area that turned into a \"more\" link (so no other entries try to\n          // occupy the space) but mark it forced-hidden\n          const hiddenEntryId = buildEntryKey(hiddenEntry);\n          forceHidden[hiddenEntryId] = true;\n          entriesByLevel[touchingLevel][touchingLateral] = hiddenEntry;\n          hiddenEntries.push(hiddenEntry);\n          this.splitEntry(touchingEntry, entry, hiddenEntries);\n        } else {\n          forceHidden[touchingEntryId] = true;\n          hiddenEntries.push(touchingEntry);\n        }\n      }\n    }\n    // will try to reslice...\n    super.handleInvalidInsertion(insertion, entry, hiddenEntries);\n  }\n}\nclass TableRow extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.cellElRefs = new RefMap(); // the <td>\n    this.frameElRefs = new RefMap(); // the fc-daygrid-day-frame\n    this.fgElRefs = new RefMap(); // the fc-daygrid-day-events\n    this.segHarnessRefs = new RefMap(); // indexed by \"instanceId:firstCol\"\n    this.rootElRef = createRef();\n    this.state = {\n      framePositions: null,\n      maxContentHeight: null,\n      segHeights: {}\n    };\n    this.handleResize = isForced => {\n      if (isForced) {\n        this.updateSizing(true); // isExternal=true\n      }\n    };\n  }\n  render() {\n    let {\n      props,\n      state,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let colCnt = props.cells.length;\n    let businessHoursByCol = splitSegsByFirstCol(props.businessHourSegs, colCnt);\n    let bgEventSegsByCol = splitSegsByFirstCol(props.bgEventSegs, colCnt);\n    let highlightSegsByCol = splitSegsByFirstCol(this.getHighlightSegs(), colCnt);\n    let mirrorSegsByCol = splitSegsByFirstCol(this.getMirrorSegs(), colCnt);\n    let {\n      singleColPlacements,\n      multiColPlacements,\n      moreCnts,\n      moreMarginTops\n    } = computeFgSegPlacement(sortEventSegs(props.fgEventSegs, options.eventOrder), props.dayMaxEvents, props.dayMaxEventRows, options.eventOrderStrict, state.segHeights, state.maxContentHeight, props.cells);\n    let isForcedInvisible =\n    // TODO: messy way to compute this\n    props.eventDrag && props.eventDrag.affectedInstances || props.eventResize && props.eventResize.affectedInstances || {};\n    return createElement(\"tr\", {\n      ref: this.rootElRef,\n      role: \"row\"\n    }, props.renderIntro && props.renderIntro(), props.cells.map((cell, col) => {\n      let normalFgNodes = this.renderFgSegs(col, props.forPrint ? singleColPlacements[col] : multiColPlacements[col], props.todayRange, isForcedInvisible);\n      let mirrorFgNodes = this.renderFgSegs(col, buildMirrorPlacements(mirrorSegsByCol[col], multiColPlacements), props.todayRange, {}, Boolean(props.eventDrag), Boolean(props.eventResize), false);\n      return createElement(TableCell, {\n        key: cell.key,\n        elRef: this.cellElRefs.createRef(cell.key),\n        innerElRef: this.frameElRefs.createRef(cell.key) /* FF <td> problem, but okay to use for left/right. TODO: rename prop */,\n        dateProfile: props.dateProfile,\n        date: cell.date,\n        showDayNumber: props.showDayNumbers,\n        showWeekNumber: props.showWeekNumbers && col === 0,\n        forceDayTop: props.showWeekNumbers /* even displaying weeknum for row, not necessarily day */,\n        todayRange: props.todayRange,\n        eventSelection: props.eventSelection,\n        eventDrag: props.eventDrag,\n        eventResize: props.eventResize,\n        extraRenderProps: cell.extraRenderProps,\n        extraDataAttrs: cell.extraDataAttrs,\n        extraClassNames: cell.extraClassNames,\n        extraDateSpan: cell.extraDateSpan,\n        moreCnt: moreCnts[col],\n        moreMarginTop: moreMarginTops[col],\n        singlePlacements: singleColPlacements[col],\n        fgContentElRef: this.fgElRefs.createRef(cell.key),\n        fgContent:\n        // Fragment scopes the keys\n        createElement(Fragment, null, createElement(Fragment, null, normalFgNodes), createElement(Fragment, null, mirrorFgNodes)),\n        bgContent:\n        // Fragment scopes the keys\n        createElement(Fragment, null, this.renderFillSegs(highlightSegsByCol[col], 'highlight'), this.renderFillSegs(businessHoursByCol[col], 'non-business'), this.renderFillSegs(bgEventSegsByCol[col], 'bg-event')),\n        minHeight: props.cellMinHeight\n      });\n    }));\n  }\n  componentDidMount() {\n    this.updateSizing(true);\n    this.context.addResizeHandler(this.handleResize);\n  }\n  componentDidUpdate(prevProps, prevState) {\n    let currentProps = this.props;\n    this.updateSizing(!isPropsEqual(prevProps, currentProps));\n  }\n  componentWillUnmount() {\n    this.context.removeResizeHandler(this.handleResize);\n  }\n  getHighlightSegs() {\n    let {\n      props\n    } = this;\n    if (props.eventDrag && props.eventDrag.segs.length) {\n      // messy check\n      return props.eventDrag.segs;\n    }\n    if (props.eventResize && props.eventResize.segs.length) {\n      // messy check\n      return props.eventResize.segs;\n    }\n    return props.dateSelectionSegs;\n  }\n  getMirrorSegs() {\n    let {\n      props\n    } = this;\n    if (props.eventResize && props.eventResize.segs.length) {\n      // messy check\n      return props.eventResize.segs;\n    }\n    return [];\n  }\n  renderFgSegs(col, segPlacements, todayRange, isForcedInvisible, isDragging, isResizing, isDateSelecting) {\n    let {\n      context\n    } = this;\n    let {\n      eventSelection\n    } = this.props;\n    let {\n      framePositions\n    } = this.state;\n    let defaultDisplayEventEnd = this.props.cells.length === 1; // colCnt === 1\n    let isMirror = isDragging || isResizing || isDateSelecting;\n    let nodes = [];\n    if (framePositions) {\n      for (let placement of segPlacements) {\n        let {\n          seg\n        } = placement;\n        let {\n          instanceId\n        } = seg.eventRange.instance;\n        let isVisible = placement.isVisible && !isForcedInvisible[instanceId];\n        let isAbsolute = placement.isAbsolute;\n        let left = '';\n        let right = '';\n        if (isAbsolute) {\n          if (context.isRtl) {\n            right = 0;\n            left = framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol];\n          } else {\n            left = 0;\n            right = framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol];\n          }\n        }\n        /*\n        known bug: events that are force to be list-item but span multiple days still take up space in later columns\n        todo: in print view, for multi-day events, don't display title within non-start/end segs\n        */\n        nodes.push(createElement(\"div\", {\n          className: 'fc-daygrid-event-harness' + (isAbsolute ? ' fc-daygrid-event-harness-abs' : ''),\n          key: generateSegKey(seg),\n          ref: isMirror ? null : this.segHarnessRefs.createRef(generateSegUid(seg)),\n          style: {\n            visibility: isVisible ? '' : 'hidden',\n            marginTop: isAbsolute ? '' : placement.marginTop,\n            top: isAbsolute ? placement.absoluteTop : '',\n            left,\n            right\n          }\n        }, hasListItemDisplay(seg) ? createElement(TableListItemEvent, Object.assign({\n          seg: seg,\n          isDragging: isDragging,\n          isSelected: instanceId === eventSelection,\n          defaultDisplayEventEnd: defaultDisplayEventEnd\n        }, getSegMeta(seg, todayRange))) : createElement(TableBlockEvent, Object.assign({\n          seg: seg,\n          isDragging: isDragging,\n          isResizing: isResizing,\n          isDateSelecting: isDateSelecting,\n          isSelected: instanceId === eventSelection,\n          defaultDisplayEventEnd: defaultDisplayEventEnd\n        }, getSegMeta(seg, todayRange)))));\n      }\n    }\n    return nodes;\n  }\n  renderFillSegs(segs, fillType) {\n    let {\n      isRtl\n    } = this.context;\n    let {\n      todayRange\n    } = this.props;\n    let {\n      framePositions\n    } = this.state;\n    let nodes = [];\n    if (framePositions) {\n      for (let seg of segs) {\n        let leftRightCss = isRtl ? {\n          right: 0,\n          left: framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol]\n        } : {\n          left: 0,\n          right: framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol]\n        };\n        nodes.push(createElement(\"div\", {\n          key: buildEventRangeKey(seg.eventRange),\n          className: \"fc-daygrid-bg-harness\",\n          style: leftRightCss\n        }, fillType === 'bg-event' ? createElement(BgEvent, Object.assign({\n          seg: seg\n        }, getSegMeta(seg, todayRange))) : renderFill(fillType)));\n      }\n    }\n    return createElement(Fragment, {}, ...nodes);\n  }\n  updateSizing(isExternalSizingChange) {\n    let {\n      props,\n      state,\n      frameElRefs\n    } = this;\n    if (!props.forPrint && props.clientWidth !== null // positioning ready?\n    ) {\n      if (isExternalSizingChange) {\n        let frameEls = props.cells.map(cell => frameElRefs.currentMap[cell.key]);\n        if (frameEls.length) {\n          let originEl = this.rootElRef.current;\n          let newPositionCache = new PositionCache(originEl, frameEls, true,\n          // isHorizontal\n          false);\n          if (!state.framePositions || !state.framePositions.similarTo(newPositionCache)) {\n            this.setState({\n              framePositions: new PositionCache(originEl, frameEls, true,\n              // isHorizontal\n              false)\n            });\n          }\n        }\n      }\n      const oldSegHeights = this.state.segHeights;\n      const newSegHeights = this.querySegHeights();\n      const limitByContentHeight = props.dayMaxEvents === true || props.dayMaxEventRows === true;\n      this.safeSetState({\n        // HACK to prevent oscillations of events being shown/hidden from max-event-rows\n        // Essentially, once you compute an element's height, never null-out.\n        // TODO: always display all events, as visibility:hidden?\n        segHeights: Object.assign(Object.assign({}, oldSegHeights), newSegHeights),\n        maxContentHeight: limitByContentHeight ? this.computeMaxContentHeight() : null\n      });\n    }\n  }\n  querySegHeights() {\n    let segElMap = this.segHarnessRefs.currentMap;\n    let segHeights = {};\n    // get the max height amongst instance segs\n    for (let segUid in segElMap) {\n      let height = Math.round(segElMap[segUid].getBoundingClientRect().height);\n      segHeights[segUid] = Math.max(segHeights[segUid] || 0, height);\n    }\n    return segHeights;\n  }\n  computeMaxContentHeight() {\n    let firstKey = this.props.cells[0].key;\n    let cellEl = this.cellElRefs.currentMap[firstKey];\n    let fcContainerEl = this.fgElRefs.currentMap[firstKey];\n    return cellEl.getBoundingClientRect().bottom - fcContainerEl.getBoundingClientRect().top;\n  }\n  getCellEls() {\n    let elMap = this.cellElRefs.currentMap;\n    return this.props.cells.map(cell => elMap[cell.key]);\n  }\n}\nTableRow.addStateEquality({\n  segHeights: isPropsEqual\n});\nfunction buildMirrorPlacements(mirrorSegs, colPlacements) {\n  if (!mirrorSegs.length) {\n    return [];\n  }\n  let topsByInstanceId = buildAbsoluteTopHash(colPlacements); // TODO: cache this at first render?\n  return mirrorSegs.map(seg => ({\n    seg,\n    isVisible: true,\n    isAbsolute: true,\n    absoluteTop: topsByInstanceId[seg.eventRange.instance.instanceId],\n    marginTop: 0\n  }));\n}\nfunction buildAbsoluteTopHash(colPlacements) {\n  let topsByInstanceId = {};\n  for (let placements of colPlacements) {\n    for (let placement of placements) {\n      topsByInstanceId[placement.seg.eventRange.instance.instanceId] = placement.absoluteTop;\n    }\n  }\n  return topsByInstanceId;\n}\nclass TableRows extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.splitBusinessHourSegs = memoize(splitSegsByRow);\n    this.splitBgEventSegs = memoize(splitSegsByRow);\n    this.splitFgEventSegs = memoize(splitSegsByRow);\n    this.splitDateSelectionSegs = memoize(splitSegsByRow);\n    this.splitEventDrag = memoize(splitInteractionByRow);\n    this.splitEventResize = memoize(splitInteractionByRow);\n    this.rowRefs = new RefMap();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let rowCnt = props.cells.length;\n    let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, rowCnt);\n    let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, rowCnt);\n    let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, rowCnt);\n    let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, rowCnt);\n    let eventDragByRow = this.splitEventDrag(props.eventDrag, rowCnt);\n    let eventResizeByRow = this.splitEventResize(props.eventResize, rowCnt);\n    // for DayGrid view with many rows, force a min-height on cells so doesn't appear squished\n    // choose 7 because a month view will have max 6 rows\n    let cellMinHeight = rowCnt >= 7 && props.clientWidth ? props.clientWidth / context.options.aspectRatio / 6 : null;\n    return createElement(NowTimer, {\n      unit: \"day\"\n    }, (nowDate, todayRange) => createElement(Fragment, null, props.cells.map((cells, row) => createElement(TableRow, {\n      ref: this.rowRefs.createRef(row),\n      key: cells.length ? cells[0].date.toISOString() /* best? or put key on cell? or use diff formatter? */ : row // in case there are no cells (like when resource view is loading)\n      ,\n      showDayNumbers: rowCnt > 1,\n      showWeekNumbers: props.showWeekNumbers,\n      todayRange: todayRange,\n      dateProfile: props.dateProfile,\n      cells: cells,\n      renderIntro: props.renderRowIntro,\n      businessHourSegs: businessHourSegsByRow[row],\n      eventSelection: props.eventSelection,\n      bgEventSegs: bgEventSegsByRow[row].filter(isSegAllDay) /* hack */,\n      fgEventSegs: fgEventSegsByRow[row],\n      dateSelectionSegs: dateSelectionSegsByRow[row],\n      eventDrag: eventDragByRow[row],\n      eventResize: eventResizeByRow[row],\n      dayMaxEvents: props.dayMaxEvents,\n      dayMaxEventRows: props.dayMaxEventRows,\n      clientWidth: props.clientWidth,\n      clientHeight: props.clientHeight,\n      cellMinHeight: cellMinHeight,\n      forPrint: props.forPrint\n    }))));\n  }\n  componentDidMount() {\n    this.registerInteractiveComponent();\n  }\n  componentDidUpdate() {\n    // for if started with zero cells\n    this.registerInteractiveComponent();\n  }\n  registerInteractiveComponent() {\n    if (!this.rootEl) {\n      // HACK: need a daygrid wrapper parent to do positioning\n      // NOTE: a daygrid resource view w/o resources can have zero cells\n      const firstCellEl = this.rowRefs.currentMap[0].getCellEls()[0];\n      const rootEl = firstCellEl ? firstCellEl.closest('.fc-daygrid-body') : null;\n      if (rootEl) {\n        this.rootEl = rootEl;\n        this.context.registerInteractiveComponent(this, {\n          el: rootEl,\n          isHitComboAllowed: this.props.isHitComboAllowed\n        });\n      }\n    }\n  }\n  componentWillUnmount() {\n    if (this.rootEl) {\n      this.context.unregisterInteractiveComponent(this);\n      this.rootEl = null;\n    }\n  }\n  // Hit System\n  // ----------------------------------------------------------------------------------------------------\n  prepareHits() {\n    this.rowPositions = new PositionCache(this.rootEl, this.rowRefs.collect().map(rowObj => rowObj.getCellEls()[0]),\n    // first cell el in each row. TODO: not optimal\n    false, true);\n    this.colPositions = new PositionCache(this.rootEl, this.rowRefs.currentMap[0].getCellEls(),\n    // cell els in first row\n    true,\n    // horizontal\n    false);\n  }\n  queryHit(positionLeft, positionTop) {\n    let {\n      colPositions,\n      rowPositions\n    } = this;\n    let col = colPositions.leftToIndex(positionLeft);\n    let row = rowPositions.topToIndex(positionTop);\n    if (row != null && col != null) {\n      let cell = this.props.cells[row][col];\n      return {\n        dateProfile: this.props.dateProfile,\n        dateSpan: Object.assign({\n          range: this.getCellRange(row, col),\n          allDay: true\n        }, cell.extraDateSpan),\n        dayEl: this.getCellEl(row, col),\n        rect: {\n          left: colPositions.lefts[col],\n          right: colPositions.rights[col],\n          top: rowPositions.tops[row],\n          bottom: rowPositions.bottoms[row]\n        },\n        layer: 0\n      };\n    }\n    return null;\n  }\n  getCellEl(row, col) {\n    return this.rowRefs.currentMap[row].getCellEls()[col]; // TODO: not optimal\n  }\n  getCellRange(row, col) {\n    let start = this.props.cells[row][col].date;\n    let end = addDays(start, 1);\n    return {\n      start,\n      end\n    };\n  }\n}\nfunction isSegAllDay(seg) {\n  return seg.eventRange.def.allDay;\n}\nclass Table extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.elRef = createRef();\n    this.needsScrollReset = false;\n  }\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      dayMaxEventRows,\n      dayMaxEvents,\n      expandRows\n    } = props;\n    let limitViaBalanced = dayMaxEvents === true || dayMaxEventRows === true;\n    // if rows can't expand to fill fixed height, can't do balanced-height event limit\n    // TODO: best place to normalize these options?\n    if (limitViaBalanced && !expandRows) {\n      limitViaBalanced = false;\n      dayMaxEventRows = null;\n      dayMaxEvents = null;\n    }\n    let classNames = ['fc-daygrid-body', limitViaBalanced ? 'fc-daygrid-body-balanced' : 'fc-daygrid-body-unbalanced', expandRows ? '' : 'fc-daygrid-body-natural' // will height of one row depend on the others?\n    ];\n    return createElement(\"div\", {\n      ref: this.elRef,\n      className: classNames.join(' '),\n      style: {\n        // these props are important to give this wrapper correct dimensions for interactions\n        // TODO: if we set it here, can we avoid giving to inner tables?\n        width: props.clientWidth,\n        minWidth: props.tableMinWidth\n      }\n    }, createElement(\"table\", {\n      role: \"presentation\",\n      className: \"fc-scrollgrid-sync-table\",\n      style: {\n        width: props.clientWidth,\n        minWidth: props.tableMinWidth,\n        height: expandRows ? props.clientHeight : ''\n      }\n    }, props.colGroupNode, createElement(\"tbody\", {\n      role: \"presentation\"\n    }, createElement(TableRows, {\n      dateProfile: props.dateProfile,\n      cells: props.cells,\n      renderRowIntro: props.renderRowIntro,\n      showWeekNumbers: props.showWeekNumbers,\n      clientWidth: props.clientWidth,\n      clientHeight: props.clientHeight,\n      businessHourSegs: props.businessHourSegs,\n      bgEventSegs: props.bgEventSegs,\n      fgEventSegs: props.fgEventSegs,\n      dateSelectionSegs: props.dateSelectionSegs,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      dayMaxEvents: dayMaxEvents,\n      dayMaxEventRows: dayMaxEventRows,\n      forPrint: props.forPrint,\n      isHitComboAllowed: props.isHitComboAllowed\n    }))));\n  }\n  componentDidMount() {\n    this.requestScrollReset();\n  }\n  componentDidUpdate(prevProps) {\n    if (prevProps.dateProfile !== this.props.dateProfile) {\n      this.requestScrollReset();\n    } else {\n      this.flushScrollReset();\n    }\n  }\n  requestScrollReset() {\n    this.needsScrollReset = true;\n    this.flushScrollReset();\n  }\n  flushScrollReset() {\n    if (this.needsScrollReset && this.props.clientWidth // sizes computed?\n    ) {\n      const subjectEl = getScrollSubjectEl(this.elRef.current, this.props.dateProfile);\n      if (subjectEl) {\n        const originEl = subjectEl.closest('.fc-daygrid-body');\n        const scrollEl = originEl.closest('.fc-scroller');\n        const scrollTop = subjectEl.getBoundingClientRect().top - originEl.getBoundingClientRect().top;\n        scrollEl.scrollTop = scrollTop ? scrollTop + 1 : 0; // overcome border\n      }\n      this.needsScrollReset = false;\n    }\n  }\n}\nfunction getScrollSubjectEl(containerEl, dateProfile) {\n  let el;\n  if (dateProfile.currentRangeUnit.match(/year|month/)) {\n    el = containerEl.querySelector(`[data-date=\"${formatIsoMonthStr(dateProfile.currentDate)}-01\"]`);\n    // even if view is month-based, first-of-month might be hidden...\n  }\n  if (!el) {\n    el = containerEl.querySelector(`[data-date=\"${formatDayString(dateProfile.currentDate)}\"]`);\n    // could still be hidden if an interior-view hidden day\n  }\n  return el;\n}\nclass DayTableSlicer extends Slicer {\n  constructor() {\n    super(...arguments);\n    this.forceDayIfListItem = true;\n  }\n  sliceRange(dateRange, dayTableModel) {\n    return dayTableModel.sliceRange(dateRange);\n  }\n}\nclass DayTable extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.slicer = new DayTableSlicer();\n    this.tableRef = createRef();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    return createElement(Table, Object.assign({\n      ref: this.tableRef\n    }, this.slicer.sliceProps(props, props.dateProfile, props.nextDayThreshold, context, props.dayTableModel), {\n      dateProfile: props.dateProfile,\n      cells: props.dayTableModel.cells,\n      colGroupNode: props.colGroupNode,\n      tableMinWidth: props.tableMinWidth,\n      renderRowIntro: props.renderRowIntro,\n      dayMaxEvents: props.dayMaxEvents,\n      dayMaxEventRows: props.dayMaxEventRows,\n      showWeekNumbers: props.showWeekNumbers,\n      expandRows: props.expandRows,\n      headerAlignElRef: props.headerAlignElRef,\n      clientWidth: props.clientWidth,\n      clientHeight: props.clientHeight,\n      forPrint: props.forPrint\n    }));\n  }\n}\nclass DayTableView extends TableView {\n  constructor() {\n    super(...arguments);\n    this.buildDayTableModel = memoize(buildDayTableModel);\n    this.headerRef = createRef();\n    this.tableRef = createRef();\n    // can't override any lifecycle methods from parent\n  }\n  render() {\n    let {\n      options,\n      dateProfileGenerator\n    } = this.context;\n    let {\n      props\n    } = this;\n    let dayTableModel = this.buildDayTableModel(props.dateProfile, dateProfileGenerator);\n    let headerContent = options.dayHeaders && createElement(DayHeader, {\n      ref: this.headerRef,\n      dateProfile: props.dateProfile,\n      dates: dayTableModel.headerDates,\n      datesRepDistinctDays: dayTableModel.rowCnt === 1\n    });\n    let bodyContent = contentArg => createElement(DayTable, {\n      ref: this.tableRef,\n      dateProfile: props.dateProfile,\n      dayTableModel: dayTableModel,\n      businessHours: props.businessHours,\n      dateSelection: props.dateSelection,\n      eventStore: props.eventStore,\n      eventUiBases: props.eventUiBases,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      nextDayThreshold: options.nextDayThreshold,\n      colGroupNode: contentArg.tableColGroupNode,\n      tableMinWidth: contentArg.tableMinWidth,\n      dayMaxEvents: options.dayMaxEvents,\n      dayMaxEventRows: options.dayMaxEventRows,\n      showWeekNumbers: options.weekNumbers,\n      expandRows: !props.isHeightAuto,\n      headerAlignElRef: this.headerElRef,\n      clientWidth: contentArg.clientWidth,\n      clientHeight: contentArg.clientHeight,\n      forPrint: props.forPrint\n    });\n    return options.dayMinWidth ? this.renderHScrollLayout(headerContent, bodyContent, dayTableModel.colCnt, options.dayMinWidth) : this.renderSimpleLayout(headerContent, bodyContent);\n  }\n}\nfunction buildDayTableModel(dateProfile, dateProfileGenerator) {\n  let daySeries = new DaySeriesModel(dateProfile.renderRange, dateProfileGenerator);\n  return new DayTableModel(daySeries, /year|month|week/.test(dateProfile.currentRangeUnit));\n}\nclass TableDateProfileGenerator extends DateProfileGenerator {\n  // Computes the date range that will be rendered\n  buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay) {\n    let renderRange = super.buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay);\n    let {\n      props\n    } = this;\n    return buildDayTableRenderRange({\n      currentRange: renderRange,\n      snapToWeek: /^(year|month)$/.test(currentRangeUnit),\n      fixedWeekCount: props.fixedWeekCount,\n      dateEnv: props.dateEnv\n    });\n  }\n}\nfunction buildDayTableRenderRange(props) {\n  let {\n    dateEnv,\n    currentRange\n  } = props;\n  let {\n    start,\n    end\n  } = currentRange;\n  let endOfWeek;\n  // year and month views should be aligned with weeks. this is already done for week\n  if (props.snapToWeek) {\n    start = dateEnv.startOfWeek(start);\n    // make end-of-week if not already\n    endOfWeek = dateEnv.startOfWeek(end);\n    if (endOfWeek.valueOf() !== end.valueOf()) {\n      end = addWeeks(endOfWeek, 1);\n    }\n  }\n  // ensure 6 weeks\n  if (props.fixedWeekCount) {\n    // TODO: instead of these date-math gymnastics (for multimonth view),\n    // compute dateprofiles of all months, then use start of first and end of last.\n    let lastMonthRenderStart = dateEnv.startOfWeek(dateEnv.startOfMonth(addDays(currentRange.end, -1)));\n    let rowCnt = Math.ceil(\n    // could be partial weeks due to hiddenDays\n    diffWeeks(lastMonthRenderStart, end));\n    end = addWeeks(end, 6 - rowCnt);\n  }\n  return {\n    start,\n    end\n  };\n}\nvar css_248z = \":root{--fc-daygrid-event-dot-width:8px}.fc-daygrid-day-events:after,.fc-daygrid-day-events:before,.fc-daygrid-day-frame:after,.fc-daygrid-day-frame:before,.fc-daygrid-event-harness:after,.fc-daygrid-event-harness:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-body{position:relative;z-index:1}.fc .fc-daygrid-day.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-daygrid-day-frame{min-height:100%;position:relative}.fc .fc-daygrid-day-top{display:flex;flex-direction:row-reverse}.fc .fc-day-other .fc-daygrid-day-top{opacity:.3}.fc .fc-daygrid-day-number{padding:4px;position:relative;z-index:4}.fc .fc-daygrid-month-start{font-size:1.1em;font-weight:700}.fc .fc-daygrid-day-events{margin-top:1px}.fc .fc-daygrid-body-balanced .fc-daygrid-day-events{left:0;position:absolute;right:0}.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events{min-height:2em;position:relative}.fc .fc-daygrid-body-natural .fc-daygrid-day-events{margin-bottom:1em}.fc .fc-daygrid-event-harness{position:relative}.fc .fc-daygrid-event-harness-abs{left:0;position:absolute;right:0;top:0}.fc .fc-daygrid-bg-harness{bottom:0;position:absolute;top:0}.fc .fc-daygrid-day-bg .fc-non-business{z-index:1}.fc .fc-daygrid-day-bg .fc-bg-event{z-index:2}.fc .fc-daygrid-day-bg .fc-highlight{z-index:3}.fc .fc-daygrid-event{margin-top:1px;z-index:6}.fc .fc-daygrid-event.fc-event-mirror{z-index:7}.fc .fc-daygrid-day-bottom{font-size:.85em;margin:0 2px}.fc .fc-daygrid-day-bottom:after,.fc .fc-daygrid-day-bottom:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-more-link{border-radius:3px;cursor:pointer;line-height:1;margin-top:1px;max-width:100%;overflow:hidden;padding:2px;position:relative;white-space:nowrap;z-index:4}.fc .fc-daygrid-more-link:hover{background-color:rgba(0,0,0,.1)}.fc .fc-daygrid-week-number{background-color:var(--fc-neutral-bg-color);color:var(--fc-neutral-text-color);min-width:1.5em;padding:2px;position:absolute;text-align:center;top:0;z-index:5}.fc .fc-more-popover .fc-popover-body{min-width:220px;padding:10px}.fc-direction-ltr .fc-daygrid-event.fc-event-start,.fc-direction-rtl .fc-daygrid-event.fc-event-end{margin-left:2px}.fc-direction-ltr .fc-daygrid-event.fc-event-end,.fc-direction-rtl .fc-daygrid-event.fc-event-start{margin-right:2px}.fc-direction-ltr .fc-daygrid-more-link{float:left}.fc-direction-ltr .fc-daygrid-week-number{border-radius:0 0 3px 0;left:0}.fc-direction-rtl .fc-daygrid-more-link{float:right}.fc-direction-rtl .fc-daygrid-week-number{border-radius:0 0 0 3px;right:0}.fc-liquid-hack .fc-daygrid-day-frame{position:static}.fc-daygrid-event{border-radius:3px;font-size:var(--fc-small-font-size);position:relative;white-space:nowrap}.fc-daygrid-block-event .fc-event-time{font-weight:700}.fc-daygrid-block-event .fc-event-time,.fc-daygrid-block-event .fc-event-title{padding:1px}.fc-daygrid-dot-event{align-items:center;display:flex;padding:2px 0}.fc-daygrid-dot-event .fc-event-title{flex-grow:1;flex-shrink:1;font-weight:700;min-width:0;overflow:hidden}.fc-daygrid-dot-event.fc-event-mirror,.fc-daygrid-dot-event:hover{background:rgba(0,0,0,.1)}.fc-daygrid-dot-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-daygrid-event-dot{border:calc(var(--fc-daygrid-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-daygrid-event-dot-width)/2);box-sizing:content-box;height:0;margin:0 4px;width:0}.fc-direction-ltr .fc-daygrid-event .fc-event-time{margin-right:3px}.fc-direction-rtl .fc-daygrid-event .fc-event-time{margin-left:3px}\";\ninjectStyles(css_248z);\nexport { DayTableView as DayGridView, DayTable, DayTableSlicer, Table, TableDateProfileGenerator, TableRows, TableView, buildDayTableModel, buildDayTableRenderRange };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}