# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm start` - Start development server (serves on http://localhost:4200/)
- `npm run build` - Build for production
- `npm run build:dev` - Build for development environment with output hashing
- `npm run build:sit` - Build for SIT environment 
- `npm run build:prod` - Build for production environment
- `npm run watch` - Build and watch for changes in development mode
- `npm test` - Run unit tests with Karma
- `ng serve` - Alternative way to start dev server

### API Code Generation
- `npm run codegen:LOCAL` - Generate API client from local swagger (localhost:9453)
- `npm run codegen:SIT` - Generate API client from SIT environment
- `npm run codegen:UAT` - Generate API client from UAT environment

The API client is generated using `ng-openapi-gen` and outputs to `./src/services/api`

## Project Architecture

### Framework & Key Dependencies
- **Angular 18** application with TypeScript
- **Nebular UI** framework for components and theming
- **Bootstrap 4.6** for layout and utilities
- **PrimeNG** for additional UI components
- **FullCalendar** and **angular-calendar** for calendar functionality
- **ng-openapi-gen** for automatic API client generation
- **TinyMCE** for rich text editing
- **Crypto-js** for encryption/decryption

### Directory Structure

#### Core Application (`src/app/`)
- `@core/` - Core services and utilities (analytics, layout, state management)
- `@theme/` - UI theme components, layouts, pipes, and styles
- `pages/` - Feature modules and components
- `shared/` - Shared utilities, models, services, and constants
- `models/` - Application-wide TypeScript interfaces
- `services/` - Business logic services

#### Key Feature Areas
- **Household Management** - Property and resident management functionality
- **Approval System** - Multi-stage approval workflows for various processes
- **Construction Project Management** - Project tracking and document management
- **Selection Management** - Material and option selection systems
- **Reservation Management** - Appointment and time slot booking
- **System Management** - User, role, and system configuration

#### Auto-Generated API Layer (`src/services/api/`)
- Generated TypeScript client from OpenAPI/Swagger specifications
- Models, services, and function wrappers for all API endpoints
- **DO NOT manually edit** - use codegen commands to regenerate

### Environment Configuration
- `src/environments/` contains environment-specific configurations
- Default environment points to SIT: `https://jeanjalechange.shindabu4.com`
- Different build configurations replace environment files

### Authentication & Security
- JWT token-based authentication with token interceptor
- Crypto utilities for secure token storage
- Auth guards for route protection
- Token stored in localStorage with encryption

### Routing Architecture
- Main layout component (`PagesComponent`) wraps authenticated routes
- Lazy-loaded feature modules for better performance
- Auth guard protects all routes except login/logout

## Development Guidelines

### API Integration
- Always use the generated API client in `src/services/api/`
- Regenerate API client when backend swagger changes
- Services are organized by feature area (house, quotation, build-case, etc.)

### State Management
- Shared observables in `src/app/pages/components/shared.observable.ts`
- Local storage service for persistent data
- Event service for cross-component communication

### UI/UX Patterns
- Follow Nebular design patterns and components
- Use custom pipes for data formatting (date, status, mapping)
- Consistent status badge styling across the application
- Responsive design with Bootstrap grid system

### File Upload
- Custom file upload components in `src/app/pages/components/file-upload/`
- Support for multiple file types and validation
- Integration with backend file storage system

### Constants & Configuration
- Shared constants in `src/app/shared/constant/constant.ts`
- Storage keys, crypto keys, and form utilities
- Environment-specific API base URLs

## Testing
- Karma + Jasmine setup for unit testing
- Test files follow `.spec.ts` naming convention
- Use `npm test` to run the test suite

## Build Output
- Production builds output to `dist/Backend/`
- Multiple environment configurations available
- Bundle size limits set to 10MB for initial and component styles

## Component Development Patterns

### Base Component Architecture
- All page components should extend `BaseComponent` (`src/app/pages/components/base/baseComponent.ts`)
- BaseComponent provides permission properties: `isCreate`, `isUpdate`, `isDelete`, `isRead`, `isExcelImport`, `isExcelExport`
- Use `AllowHelper` injected as `protected allow: AllowHelper` in constructor
- BaseComponent includes common pagination properties: `pageIndex`, `pageSize`, `totalRecords`

### Permission System
- Permission checks are URL-based through `AllowHelper`
- Template permissions use inherited properties from BaseComponent: `*ngIf="isCreate"`, `*ngIf="isUpdate"`, etc.
- Never use direct permission methods in templates - use the inherited boolean properties

### Module Organization
- Feature modules follow consistent patterns with routing and component declarations
- ThemeModule (`@theme/theme.module.ts`) exports all Nebular UI components
- When adding new Nebular components, add to both imports and NB_MODULES array in ThemeModule
- SharedModule provides common form components and utilities

### Component Creation Checklist
1. Extend BaseComponent with proper AllowHelper injection
2. Use generated API services from `src/services/api/services/`
3. Follow naming convention: feature-name.component.ts/html/scss/spec.ts
4. Add component to feature module declarations and routing
5. Use ThemeModule for Nebular UI components
6. Implement proper validation using ValidationHelper

### API Integration Standards
- Use only generated API services - never create manual HTTP calls
- API services follow pattern: `this._serviceInstance.apiMethodPost$Json({ body: requestObject })`
- Handle responses with StatusCode checking and proper error messaging
- Use MessageService for user notifications: `showSucessMSG()`, `showErrorMSG()`

### Form Validation Pattern
- Use ValidationHelper for consistent validation
- Call `this.valid.clear()` before validation
- Use validation methods like `required()`, `isStringMaxLength()`, `pattern()`
- Display errors with `this.message.showErrorMSGs(this.valid.errorMessages)`

### UI Component Guidelines
- Use Nebular components from ThemeModule for consistent styling
- Common components: `nb-card`, `nb-badge`, `nb-button`, `nb-select`, `nb-input`
- Status badges use `[status]="condition ? 'success' : 'danger'"` pattern
- Modal dialogs use `ng-template` with `dialogRef` pattern
- Form inputs follow Bootstrap grid system with `col-*` classes