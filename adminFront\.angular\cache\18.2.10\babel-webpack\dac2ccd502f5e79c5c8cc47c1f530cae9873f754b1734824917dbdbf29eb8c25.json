{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Cipher Feedback block mode.\n   */\n  CryptoJS.mode.CFB = function () {\n    var CFB = CryptoJS.lib.BlockCipherMode.extend();\n    CFB.Encryptor = CFB.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n        generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher);\n\n        // Remember this block to use with next block\n        this._prevBlock = words.slice(offset, offset + blockSize);\n      }\n    });\n    CFB.Decryptor = CFB.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n\n        // Remember this block to use with next block\n        var thisBlock = words.slice(offset, offset + blockSize);\n        generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher);\n\n        // This block becomes the previous block\n        this._prevBlock = thisBlock;\n      }\n    });\n    function generateKeystreamAndEncrypt(words, offset, blockSize, cipher) {\n      var keystream;\n\n      // Shortcut\n      var iv = this._iv;\n\n      // Generate keystream\n      if (iv) {\n        keystream = iv.slice(0);\n\n        // Remove IV for subsequent blocks\n        this._iv = undefined;\n      } else {\n        keystream = this._prevBlock;\n      }\n      cipher.encryptBlock(keystream, 0);\n\n      // Encrypt\n      for (var i = 0; i < blockSize; i++) {\n        words[offset + i] ^= keystream[i];\n      }\n    }\n    return CFB;\n  }();\n  return CryptoJS.mode.CFB;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}