{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setSeconds\n * @category Second Helpers\n * @summary Set the seconds to the given date.\n *\n * @description\n * Set the seconds to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} seconds - the seconds of the new date\n * @returns {Date} the new date with the seconds set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set 45 seconds to 1 September 2014 11:30:40:\n * const result = setSeconds(new Date(2014, 8, 1, 11, 30, 40), 45)\n * //=> Mon Sep 01 2014 11:30:45\n */\nexport default function setSeconds(dirtyDate, dirtySeconds) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var seconds = toInteger(dirtySeconds);\n  date.setSeconds(seconds);\n  return date;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}