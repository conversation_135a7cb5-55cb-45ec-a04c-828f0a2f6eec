{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let SpecialChangeSourcePipe = /*#__PURE__*/(() => {\n  class SpecialChangeSourcePipe {\n    transform(value) {\n      switch (value) {\n        case 1:\n          return '後台';\n        case 2:\n          return '前台';\n        default:\n          return '';\n      }\n    }\n    static {\n      this.ɵfac = function SpecialChangeSourcePipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SpecialChangeSourcePipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"specialChangeSource\",\n        type: SpecialChangeSourcePipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return SpecialChangeSourcePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}