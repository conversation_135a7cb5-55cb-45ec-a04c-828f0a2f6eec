{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/helper/petternHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nimport * as i12 from \"../../../@theme/pipes/mapping.pipe\";\nfunction NotificationSettingComponent_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.label, \" \");\n  }\n}\nfunction NotificationSettingComponent_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.label, \" \");\n  }\n}\nfunction NotificationSettingComponent_tr_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"getTypeMailName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 30);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 28);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"getStatusMailName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 31)(14, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function NotificationSettingComponent_tr_48_Template_button_click_14_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      const dialog_r4 = i0.ɵɵreference(52);\n      return i0.ɵɵresetView(ctx_r6.onSelectedBuildCaseMail(item_r6, dialog_r4));\n    });\n    i0.ɵɵtext(15, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function NotificationSettingComponent_tr_48_Template_button_click_16_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onDelete(item_r6));\n    });\n    i0.ɵɵtext(17, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CBuildCaseMailId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 5, item_r6.CMailType));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r6.CMail);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 7, item_r6.CStatus));\n  }\n}\nfunction NotificationSettingComponent_ng_template_51_nb_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r9.label, \" \");\n  }\n}\nfunction NotificationSettingComponent_ng_template_51_nb_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r10);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r10.label, \" \");\n  }\n}\nfunction NotificationSettingComponent_ng_template_51_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r11);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r11.label, \" \");\n  }\n}\nfunction NotificationSettingComponent_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 34);\n    i0.ɵɵelement(1, \"nb-card-header\");\n    i0.ɵɵelementStart(2, \"nb-card-body\", 35)(3, \"div\", 36)(4, \"label\", 37);\n    i0.ɵɵtext(5, \"\\u5EFA\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-select\", 38);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_ng_template_51_Template_nb_select_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedDetail.CBuildCaseId, $event) || (ctx_r6.selectedDetail.CBuildCaseId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(7, NotificationSettingComponent_ng_template_51_nb_option_7_Template, 2, 2, \"nb-option\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 39)(9, \"label\", 40);\n    i0.ɵɵtext(10, \"\\u96FB\\u5B50\\u90F5\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"textarea\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_ng_template_51_Template_textarea_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedBuildCaseMail.CMail, $event) || (ctx_r6.selectedBuildCaseMail.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(12, \"        \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 36)(14, \"label\", 42);\n    i0.ɵɵtext(15, \"\\u767C\\u9001\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-select\", 12);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_ng_template_51_Template_nb_select_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedDetail.CMailType, $event) || (ctx_r6.selectedDetail.CMailType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(17, NotificationSettingComponent_ng_template_51_nb_option_17_Template, 2, 2, \"nb-option\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 36)(19, \"label\", 43);\n    i0.ɵɵtext(20, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"nb-select\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_ng_template_51_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedDetail.CStatus, $event) || (ctx_r6.selectedDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, NotificationSettingComponent_ng_template_51_nb_option_22_Template, 2, 2, \"nb-option\", 7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"nb-card-footer\", 25)(24, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function NotificationSettingComponent_ng_template_51_Template_button_click_24_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r8).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onClose(ref_r12));\n    });\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function NotificationSettingComponent_ng_template_51_Template_button_click_26_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r8).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onSubmit(ref_r12));\n    });\n    i0.ɵɵtext(27, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedDetail.CBuildCaseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.buildCaseOptionsDialog);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedBuildCaseMail.CMail);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedDetail.CMailType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.typeOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.statusOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r6.isNew ? \"\\u53D6\\u6D88\" : \"\\u95DC\\u9589\");\n  }\n}\nexport class NotificationSettingComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _buildCaseMaildService, pettern) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._buildCaseMaildService = _buildCaseMaildService;\n    this.pettern = pettern;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.buildCaseMailList = [];\n    this.initBuildCaseMail = {\n      CStatus: 0,\n      CBuildCaseId: 0,\n      CMailType: 0,\n      CMail: \"\"\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.typeOptionsAll = [{\n      value: '',\n      label: '全部'\n    }, {\n      value: 1,\n      label: '簽署完成'\n    }, {\n      value: 2,\n      label: '已預約客變'\n    }];\n    this.typeOptions = [{\n      value: 1,\n      label: '簽署完成'\n    }, {\n      value: 2,\n      label: '已預約客變'\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.buildCaseOptionsDialog = [];\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n    this.selected = {\n      CMailType: this.typeOptionsAll[0],\n      CBuildCaseId: this.buildCaseOptions[0]\n    };\n    this.selectedDetail = {\n      CMailType: this.typeOptions[0],\n      CBuildCaseId: this.buildCaseOptionsDialog[0],\n      CStatus: this.statusOptions[0]\n    };\n    this.searchQuery = {\n      CBuildCaseId: this.buildCaseOptions[0].value,\n      CMailType: this.typeOptions[0].value,\n      CMail: ''\n    };\n    this.getListBuildCaseMail();\n    this.selectedBuildCaseMail = this.initBuildCaseMail;\n  }\n  removeEmptyValues(obj) {\n    const newObj = {\n      ...obj\n    };\n    for (const key in newObj) {\n      if (newObj[key] === '' || newObj[key] === 0) {\n        delete newObj[key];\n      }\n    }\n    return newObj;\n  }\n  handleParamRequest() {\n    this.searchQuery = {\n      ...this.searchQuery,\n      CBuildCaseId: this.selected.CBuildCaseId.value,\n      CMailType: this.selected.CMailType.value,\n      pageIndex: this.pageIndex,\n      pageSize: this.pageSize\n    };\n    return {\n      ...this.removeEmptyValues(this.searchQuery)\n    };\n  }\n  getListBuildCaseMail() {\n    this._buildCaseMaildService.apiBuildCaseMailGetBuildCaseMailListPost$Json({\n      body: this.handleParamRequest()\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode === 0) {\n        this.buildCaseMailList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListBuildCaseMail();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        const options = res.Entries.map(e => {\n          return {\n            label: e?.CBuildCaseName,\n            value: e?.cID\n          };\n        });\n        this.buildCaseOptions = [{\n          label: '全部',\n          value: ''\n        }, ...options];\n        this.buildCaseOptionsDialog = [...options];\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedBuildCaseMail = this.initBuildCaseMail;\n    this.selectedDetail = {\n      CMailType: this.typeOptions[0],\n      CBuildCaseId: this.buildCaseOptionsDialog[0],\n      CStatus: this.statusOptions[0]\n    };\n    this.dialogService.open(ref);\n  }\n  onSelectedBuildCaseMail(data, ref) {\n    this._buildCaseMaildService.apiBuildCaseMailGetBuildCaseMailListPost$Json({\n      body: {\n        CBuildCaseMailId: data.CBuildCaseMailId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0 && res.Entries.length) {\n        this.selectedBuildCaseMail = {\n          CBuildCaseMailId: res.Entries[0].CBuildCaseMailId,\n          CBuildCaseId: res.Entries[0].CBuildCaseid,\n          CMail: res.Entries[0].CMail,\n          CMailType: res.Entries[0].CMailType,\n          CStatus: res.Entries[0].CStatus\n        };\n        this.selectedDetail = {\n          CMailType: this.typeOptions.find(item => item.value === this.selectedBuildCaseMail.CMailType),\n          CBuildCaseId: this.buildCaseOptionsDialog.find(item => item.value === this.selectedBuildCaseMail.CBuildCaseId),\n          CStatus: this.statusOptions.find(item => item.value === this.selectedBuildCaseMail.CStatus)\n        };\n      }\n    });\n    this.selectedBuildCaseMail = data;\n    this.dialogService.open(ref);\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案]', this.selectedBuildCaseMail.CBuildCaseId);\n    // this.valid.pattern('[電子郵件]', this.selectedBuildCaseMail.CMail, this.pettern.MailPettern)\n  }\n  onSubmit(ref) {\n    this.selectedBuildCaseMail = {\n      ...this.selectedBuildCaseMail,\n      CBuildCaseId: this.selectedDetail.CBuildCaseId.value,\n      CMailType: this.selectedDetail.CMailType.value,\n      CStatus: this.selectedDetail.CStatus.value\n    };\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._buildCaseMaildService.apiBuildCaseMailSaveBuildCaseMailPost$Json({\n      body: this.selectedBuildCaseMail\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getListBuildCaseMail();\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n  }\n  onDelete(data) {\n    if (window.confirm(`確定要刪除【項目${data.CBuildCaseName}】?`)) {\n      this._buildCaseMaildService.apiBuildCaseMailDeleteBuildCaseMailPost$Json({\n        body: data.CBuildCaseMailId\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getListBuildCaseMail();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  static {\n    this.ɵfac = function NotificationSettingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NotificationSettingComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.BuildCaseMailService), i0.ɵɵdirectiveInject(i6.PetternHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotificationSettingComponent,\n      selectors: [[\"ngx-notification-setting\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 53,\n      vars: 9,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"search\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"id\", \"search\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"preOrderDate\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u767C\\u9001\\u985E\\u578B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-info\", \"mx-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"1000px\", \"table-layout\", \"fixed\"], [1, \"table-header\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"col-1\"], [1, \"col-2\"], [1, \"col-4\", \"ellipsis\"], [1, \"text-center\", \"w-32\", \"col-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", \"text-red-500\", \"border-red-500\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", \"mt-[10px]\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\"], [\"for\", \"CMail\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CMailType\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", \"mt-[10px]\", 2, \"min-width\", \"75px\"], [\"for\", \"CStatus\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", \"mt-[10px]\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"]],\n      template: function NotificationSettingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 2)(5, \"div\", 3)(6, \"div\", 4)(7, \"label\", 5);\n          i0.ɵɵtext(8, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"nb-select\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_Template_nb_select_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selected.CBuildCaseId, $event) || (ctx.selected.CBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(10, NotificationSettingComponent_nb_option_10_Template, 2, 2, \"nb-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 3)(12, \"div\", 8)(13, \"label\", 9);\n          i0.ɵɵtext(14, \"\\u96FB\\u5B50\\u90F5\\u4EF6 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"nb-form-field\")(16, \"input\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.cMail, $event) || (ctx.searchQuery.cMail = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"div\", 3)(18, \"div\", 8)(19, \"label\", 11);\n          i0.ɵɵtext(20, \"\\u767C\\u9001\\u985E\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"nb-select\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_Template_nb_select_ngModelChange_21_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selected.CMailType, $event) || (ctx.selected.CMailType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(22, NotificationSettingComponent_nb_option_22_Template, 2, 2, \"nb-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 3)(24, \"div\", 13)(25, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function NotificationSettingComponent_Template_button_click_25_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const dialog_r4 = i0.ɵɵreference(52);\n            return i0.ɵɵresetView(ctx.addNew(dialog_r4));\n          });\n          i0.ɵɵtext(26, \" \\u65B0\\u589E \");\n          i0.ɵɵelement(27, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function NotificationSettingComponent_Template_button_click_28_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getListBuildCaseMail());\n          });\n          i0.ɵɵtext(29, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(30, \"i\", 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(31, \"div\", 18)(32, \"table\", 19)(33, \"thead\", 20)(34, \"tr\")(35, \"th\", 21);\n          i0.ɵɵtext(36, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"th\", 22);\n          i0.ɵɵtext(38, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"th\", 22);\n          i0.ɵɵtext(40, \"\\u767C\\u9001\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"th\", 23);\n          i0.ɵɵtext(42, \"\\u96FB\\u5B50\\u90F5\\u4EF6\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"th\", 21);\n          i0.ɵɵtext(44, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"th\", 22);\n          i0.ɵɵtext(46, \"\\u52D5\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"tbody\");\n          i0.ɵɵtemplate(48, NotificationSettingComponent_tr_48_Template, 18, 9, \"tr\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(49, \"nb-card-footer\", 25)(50, \"ngb-pagination\", 26);\n          i0.ɵɵtwoWayListener(\"pageChange\", function NotificationSettingComponent_Template_ngb_pagination_pageChange_50_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function NotificationSettingComponent_Template_ngb_pagination_pageChange_50_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(51, NotificationSettingComponent_ng_template_51_Template, 28, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selected.CBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.cMail);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selected.CMailType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.typeOptionsAll);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseMailList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, SharedModule, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i9.NgbPagination, i10.BreadcrumbComponent, i11.BaseLabelDirective, i12.StatusMailPipe, i12.TypeMailPipe],\n      styles: [\".ellipsis[_ngcontent-%COMP%] {\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n  white-space: normal;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm5vdGlmaWNhdGlvbi1zZXR0aW5nLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UscUJBQUE7RUFDQSx5QkFBQTtFQUNBLG1CQUFBO0FBQ0YiLCJmaWxlIjoibm90aWZpY2F0aW9uLXNldHRpbmcuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuZWxsaXBzaXMge1xyXG4gIHdvcmQtd3JhcDogYnJlYWstd29yZDtcclxuICBvdmVyZmxvdy13cmFwOiBicmVhay13b3JkO1xyXG4gIHdoaXRlLXNwYWNlOiBub3JtYWw7XHJcbn1cclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc3lzdGVtLW1hbmFnZW1lbnQvbm90aWZpY2F0aW9uLXNldHRpbmcvbm90aWZpY2F0aW9uLXNldHRpbmcuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxxQkFBQTtFQUNBLHlCQUFBO0VBQ0EsbUJBQUE7QUFDRjtBQUNBLHdjQUF3YyIsInNvdXJjZXNDb250ZW50IjpbIi5lbGxpcHNpcyB7XHJcbiAgd29yZC13cmFwOiBicmVhay13b3JkO1xyXG4gIG92ZXJmbG93LXdyYXA6IGJyZWFrLXdvcmQ7XHJcbiAgd2hpdGUtc3BhY2U6IG5vcm1hbDtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "CommonModule", "SharedModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "type_r3", "ɵɵlistener", "NotificationSettingComponent_tr_48_Template_button_click_14_listener", "item_r6", "ɵɵrestoreView", "_r5", "$implicit", "ctx_r6", "ɵɵnextContext", "dialog_r4", "ɵɵreference", "ɵɵresetView", "onSelectedBuildCaseMail", "NotificationSettingComponent_tr_48_Template_button_click_16_listener", "onDelete", "ɵɵtextInterpolate", "CBuildCaseMailId", "CBuildCaseName", "ɵɵpipeBind1", "CMailType", "CMail", "CStatus", "case_r9", "type_r10", "status_r11", "ɵɵelement", "ɵɵtwoWayListener", "NotificationSettingComponent_ng_template_51_Template_nb_select_ngModelChange_6_listener", "$event", "_r8", "ɵɵtwoWayBindingSet", "selectedDetail", "CBuildCaseId", "ɵɵtemplate", "NotificationSettingComponent_ng_template_51_nb_option_7_Template", "NotificationSettingComponent_ng_template_51_Template_textarea_ngModelChange_11_listener", "selectedBuildCaseMail", "NotificationSettingComponent_ng_template_51_Template_nb_select_ngModelChange_16_listener", "NotificationSettingComponent_ng_template_51_nb_option_17_Template", "NotificationSettingComponent_ng_template_51_Template_nb_select_ngModelChange_21_listener", "NotificationSettingComponent_ng_template_51_nb_option_22_Template", "NotificationSettingComponent_ng_template_51_Template_button_click_24_listener", "ref_r12", "dialogRef", "onClose", "NotificationSettingComponent_ng_template_51_Template_button_click_26_listener", "onSubmit", "ɵɵtwoWayProperty", "buildCaseOptionsDialog", "typeOptions", "statusOptions", "isNew", "NotificationSettingComponent", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_buildCaseMaildService", "pettern", "pageFirst", "pageSize", "pageIndex", "totalRecords", "buildCaseMailList", "initBuildCaseMail", "value", "typeOptionsAll", "buildCaseOptions", "ngOnInit", "getListBuildCase", "selected", "searchQuery", "getListBuildCaseMail", "removeEmptyValues", "obj", "newObj", "key", "handleParamRequest", "apiBuildCaseMailGetBuildCaseMailListPost$Json", "body", "subscribe", "res", "Entries", "StatusCode", "TotalItems", "pageChanged", "newPage", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "CIsPagi", "options", "map", "e", "cID", "addNew", "ref", "open", "data", "length", "CBuildCaseid", "find", "item", "validation", "clear", "required", "errorMessages", "showErrorMSGs", "apiBuildCaseMailSaveBuildCaseMailPost$Json", "showSucessMSG", "close", "showErrorMSG", "Message", "window", "confirm", "apiBuildCaseMailDeleteBuildCaseMailPost$Json", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "BuildCaseService", "BuildCaseMailService", "i6", "PetternHelper", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NotificationSettingComponent_Template", "rf", "ctx", "NotificationSettingComponent_Template_nb_select_ngModelChange_9_listener", "_r1", "NotificationSettingComponent_nb_option_10_Template", "NotificationSettingComponent_Template_input_ngModelChange_16_listener", "cMail", "NotificationSettingComponent_Template_nb_select_ngModelChange_21_listener", "NotificationSettingComponent_nb_option_22_Template", "NotificationSettingComponent_Template_button_click_25_listener", "NotificationSettingComponent_Template_button_click_28_listener", "NotificationSettingComponent_tr_48_Template", "NotificationSettingComponent_Template_ngb_pagination_pageChange_50_listener", "NotificationSettingComponent_ng_template_51_Template", "ɵɵtemplateRefExtractor", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i8", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "i9", "NgbPagination", "i10", "BreadcrumbComponent", "i11", "BaseLabelDirective", "i12", "StatusMailPipe", "TypeMailPipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\notification-setting\\notification-setting.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\notification-setting\\notification-setting.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseMailService, BuildCaseService } from 'src/services/api/services';\r\nimport { GetBuildCaseMailListResponse, SaveBuildCaseMailRequest } from 'src/services/api/models';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SharedModule } from '../../components/shared.module';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-notification-setting',\r\n  templateUrl: './notification-setting.component.html',\r\n  styleUrls: ['./notification-setting.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule\r\n  ]\r\n})\r\n\r\nexport class NotificationSettingComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _buildCaseMaildService: BuildCaseMailService,\r\n    private pettern: PetternHelper\r\n  ) { super(_allow) }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  buildCaseMailList: GetBuildCaseMailListResponse[] = []\r\n\r\n  selectedBuildCaseMail: SaveBuildCaseMailRequest\r\n\r\n  initBuildCaseMail: SaveBuildCaseMailRequest = {\r\n    CStatus: 0,\r\n    CBuildCaseId: 0,\r\n    CMailType: 0,\r\n    CMail: \"\",\r\n  }\r\n\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      label: '停用',\r\n    },\r\n    {\r\n      value: 1,\r\n      label: '啟用',\r\n    }\r\n  ]\r\n\r\n  typeOptionsAll: any[] = [\r\n    {\r\n      value: '',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: 1,\r\n      label: '簽署完成',\r\n    }, {\r\n      value: 2,\r\n      label: '已預約客變',\r\n    }\r\n  ]\r\n\r\n  typeOptions: any[] = [\r\n    {\r\n      value: 1,\r\n      label: '簽署完成',\r\n    }, {\r\n      value: 2,\r\n      label: '已預約客變',\r\n    }\r\n  ]\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n  buildCaseOptionsDialog: any[] = []\r\n  isNew = true\r\n  selected: any\r\n\r\n  selectedDetail:  { CMailType?: any,CBuildCaseId?: any, CStatus?: any}\r\n  searchQuery: any\r\n\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase();\r\n    this.selected = {\r\n      CMailType: this.typeOptionsAll[0],\r\n      CBuildCaseId: this.buildCaseOptions[0],\r\n    }\r\n\r\n    this.selectedDetail = {\r\n      CMailType: this.typeOptions[0],\r\n      CBuildCaseId: this.buildCaseOptionsDialog[0],\r\n      CStatus: this.statusOptions[0],\r\n    }\r\n\r\n    this.searchQuery = {\r\n      CBuildCaseId: this.buildCaseOptions[0].value,\r\n      CMailType: this.typeOptions[0].value,\r\n      CMail: ''\r\n    }\r\n\r\n    this.getListBuildCaseMail()\r\n\r\n    this.selectedBuildCaseMail = this.initBuildCaseMail\r\n  }\r\n\r\n  removeEmptyValues(obj: any) {\r\n    const newObj = { ...obj };\r\n    for (const key in newObj) {\r\n      if (newObj[key] === '' || newObj[key] === 0) {\r\n        delete newObj[key];\r\n      }\r\n    }\r\n    return newObj;\r\n  }\r\n\r\n\r\n  handleParamRequest() {\r\n    this.searchQuery = {\r\n      ...this.searchQuery,\r\n      CBuildCaseId: this.selected.CBuildCaseId.value,\r\n      CMailType: this.selected.CMailType.value,\r\n      pageIndex: this.pageIndex,\r\n      pageSize: this.pageSize,\r\n    }\r\n    return { ...this.removeEmptyValues(this.searchQuery) }\r\n\r\n  }\r\n\r\n  getListBuildCaseMail() {\r\n    this._buildCaseMaildService.apiBuildCaseMailGetBuildCaseMailListPost$Json({ body: this.handleParamRequest() }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode === 0) {\r\n        this.buildCaseMailList = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems!\r\n      }\r\n    })\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListBuildCaseMail();\r\n  }\r\n\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({ body: {\r\n      CIsPagi: false,\r\n      CStatus: 1,\r\n    } }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        const options = res.Entries.map(e => {\r\n          return {\r\n            label: e?.CBuildCaseName,\r\n            value: e?.cID\r\n          }\r\n        })\r\n        this.buildCaseOptions = [{ label: '全部', value: '' }, ...options]\r\n        this.buildCaseOptionsDialog = [ ...options]\r\n      }\r\n    })\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.isNew = true;\r\n    this.selectedBuildCaseMail = this.initBuildCaseMail\r\n    this.selectedDetail = {\r\n      CMailType: this.typeOptions[0],\r\n      CBuildCaseId: this.buildCaseOptionsDialog[0],\r\n      CStatus: this.statusOptions[0],\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onSelectedBuildCaseMail(data: any, ref: any) {\r\n    this._buildCaseMaildService.apiBuildCaseMailGetBuildCaseMailListPost$Json({\r\n      body: {\r\n        CBuildCaseMailId: data.CBuildCaseMailId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0 && res.Entries.length) {\r\n        this.selectedBuildCaseMail = {\r\n          CBuildCaseMailId : res.Entries[0].CBuildCaseMailId,\r\n          CBuildCaseId : res.Entries[0].CBuildCaseid,\r\n          CMail : res.Entries[0].CMail,\r\n          CMailType : res.Entries[0].CMailType,\r\n          CStatus :  res.Entries[0].CStatus,\r\n        }\r\n        this.selectedDetail = {\r\n          CMailType: this.typeOptions.find(item => item.value === this.selectedBuildCaseMail.CMailType),\r\n          CBuildCaseId: this.buildCaseOptionsDialog.find(item => item.value === this.selectedBuildCaseMail.CBuildCaseId),\r\n          CStatus: this.statusOptions.find(item => item.value === this.selectedBuildCaseMail.CStatus)\r\n        }\r\n      }\r\n    })\r\n    this.selectedBuildCaseMail = data\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案]', this.selectedBuildCaseMail.CBuildCaseId)\r\n    // this.valid.pattern('[電子郵件]', this.selectedBuildCaseMail.CMail, this.pettern.MailPettern)\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.selectedBuildCaseMail = {\r\n      ...this.selectedBuildCaseMail,\r\n      CBuildCaseId: this.selectedDetail.CBuildCaseId.value,\r\n      CMailType: this.selectedDetail.CMailType.value,\r\n      CStatus: this.selectedDetail.CStatus.value,\r\n    }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._buildCaseMaildService.apiBuildCaseMailSaveBuildCaseMailPost$Json({\r\n      body: this.selectedBuildCaseMail\r\n    }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListBuildCaseMail()\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!);\r\n      }\r\n    })\r\n  }\r\n\r\n  onDelete(data: any) {\r\n    if (window.confirm(`確定要刪除【項目${data.CBuildCaseName}】?`)) {\r\n      this._buildCaseMaildService.apiBuildCaseMailDeleteBuildCaseMailPost$Json({\r\n        body: data.CBuildCaseMailId\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.getListBuildCaseMail()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!)\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2\">建案名稱</label>\r\n          <nb-select placeholder=\"建案名稱\" [(ngModel)]=\"selected.CBuildCaseId\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let case of buildCaseOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"search\" class=\"label mr-2\">電子郵件\r\n          </label>\r\n          <nb-form-field>\r\n            <input type=\"text\" id=\"search\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.cMail\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"preOrderDate\" class=\"label mr-2\">發送類型 </label>\r\n          <nb-select placeholder=\"發送類型\" [(ngModel)]=\"selected.CMailType\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let type of typeOptionsAll\" [value]=\"type\">\r\n              {{ type.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info mx-2\" (click)=\"addNew(dialog)\">\r\n            新增 <i class=\"fas fa-plus\"></i>\r\n          </button>\r\n          <button class=\"btn btn-info\" (click)=\"getListBuildCaseMail()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table\" style=\"min-width: 1000px; table-layout: fixed;\">\r\n        <thead class=\"table-header\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"col-1\">ID</th>\r\n            <th scope=\"col\" class=\"col-2\">建案名稱</th>\r\n            <th scope=\"col\" class=\"col-2\">發送類型</th>\r\n            <th scope=\"col\" class=\"col-4\">電子郵件</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-2\">動作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of buildCaseMailList ; let i = index\">\r\n            <td class=\"col-1\">{{ item.CBuildCaseMailId}}</td>\r\n            <td class=\"col-2\">{{ item.CBuildCaseName}}</td>\r\n            <td class=\"col-2\">{{ item.CMailType | getTypeMailName}}</td>\r\n            <td class=\"col-4 ellipsis\">{{ item.CMail}}</td>\r\n            <td class=\"col-1\">{{ item.CStatus | getStatusMailName}}</td>\r\n            <td class=\"text-center w-32 col-2\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\"\r\n                (click)=\"onSelectedBuildCaseMail(item, dialog)\">編輯</button>\r\n              <button class=\"btn btn-outline-primary btn-sm m-1 text-red-500 border-red-500\"\r\n                (click)=\"onDelete(item)\">刪除</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex\">\r\n        <label for=\"remark\" style=\"min-width:75px\" class=\"required-field mr-4 mt-[10px]\" baseLabel>建案</label>\r\n        <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedDetail.CBuildCaseId\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let case of buildCaseOptionsDialog\" [value]=\"case\">\r\n            {{ case.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CMail\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>電子郵件\r\n        </label>\r\n        <textarea type=\"text\" class=\"w-full\" nbInput placeholder=\"請輸入電子郵件\" [(ngModel)]=\"selectedBuildCaseMail.CMail\">\r\n        </textarea>\r\n      </div>\r\n      <div class=\"form-group d-flex\">\r\n        <label for=\"CMailType\" style=\"min-width:75px;\" class=\"required-field mr-4 mt-[10px]\" baseLabel>發送類型</label>\r\n        <nb-select placeholder=\"發送類型\" [(ngModel)]=\"selectedDetail.CMailType\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let type of typeOptions\" [value]=\"type\">\r\n            {{ type.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group d-flex\">\r\n        <label for=\"CStatus\" style=\"min-width:75px\" class=\"required-field mr-4 mt-[10px]\" baseLabel>狀態</label>\r\n        <nb-select placeholder=\"狀態\" [(ngModel)]=\"selectedDetail.CStatus\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of statusOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">{{ isNew ? '取消' : '關閉'}}</button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAQA,SAASA,aAAa,QAAQ,qCAAqC;AACnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gCAAgC;;;;;;;;;;;;;;;;ICAjDC,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IAC7DL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,KAAA,MACF;;;;;IAiBAR,EAAA,CAAAC,cAAA,oBAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFmCH,EAAA,CAAAI,UAAA,UAAAK,OAAA,CAAc;IAC3DT,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,OAAA,CAAAD,KAAA,MACF;;;;;;IA8BAR,EADF,CAAAC,cAAA,SAA2D,aACvC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAqC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE1DH,EADF,CAAAC,cAAA,cAAmC,kBAEiB;IAAhDD,EAAA,CAAAU,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,OAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,MAAAC,SAAA,GAAAlB,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASJ,MAAA,CAAAK,uBAAA,CAAAT,OAAA,EAAAM,SAAA,CAAqC;IAAA,EAAC;IAAClB,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7DH,EAAA,CAAAC,cAAA,kBAC2B;IAAzBD,EAAA,CAAAU,UAAA,mBAAAY,qEAAA;MAAA,MAAAV,OAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASJ,MAAA,CAAAO,QAAA,CAAAX,OAAA,CAAc;IAAA,EAAC;IAACZ,EAAA,CAAAE,MAAA,oBAAE;IAEjCF,EAFiC,CAAAG,YAAA,EAAS,EACnC,EACF;;;;IAXeH,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAwB,iBAAA,CAAAZ,OAAA,CAAAa,gBAAA,CAA0B;IAC1BzB,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAwB,iBAAA,CAAAZ,OAAA,CAAAc,cAAA,CAAwB;IACxB1B,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAwB,iBAAA,CAAAxB,EAAA,CAAA2B,WAAA,OAAAf,OAAA,CAAAgB,SAAA,EAAqC;IAC5B5B,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAwB,iBAAA,CAAAZ,OAAA,CAAAiB,KAAA,CAAe;IACxB7B,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAwB,iBAAA,CAAAxB,EAAA,CAAA2B,WAAA,QAAAf,OAAA,CAAAkB,OAAA,EAAqC;;;;;IA2BzD9B,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAA2B,OAAA,CAAc;IACnE/B,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAwB,OAAA,CAAAvB,KAAA,MACF;;;;;IAYAR,EAAA,CAAAC,cAAA,oBAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFgCH,EAAA,CAAAI,UAAA,UAAA4B,QAAA,CAAc;IACxDhC,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAyB,QAAA,CAAAxB,KAAA,MACF;;;;;IAMAR,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAA6B,UAAA,CAAgB;IAC9DjC,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAA0B,UAAA,CAAAzB,KAAA,MACF;;;;;;IA/BRR,EAAA,CAAAC,cAAA,kBAA+C;IAC7CD,EAAA,CAAAkC,SAAA,qBACiB;IAGblC,EAFJ,CAAAC,cAAA,uBAA2B,cACM,gBAC8D;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrGH,EAAA,CAAAC,cAAA,oBAAqF;IAAzDD,EAAA,CAAAmC,gBAAA,2BAAAC,wFAAAC,MAAA;MAAArC,EAAA,CAAAa,aAAA,CAAAyB,GAAA;MAAA,MAAAtB,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAuC,kBAAA,CAAAvB,MAAA,CAAAwB,cAAA,CAAAC,YAAA,EAAAJ,MAAA,MAAArB,MAAA,CAAAwB,cAAA,CAAAC,YAAA,GAAAJ,MAAA;MAAA,OAAArC,EAAA,CAAAoB,WAAA,CAAAiB,MAAA;IAAA,EAAyC;IACnErC,EAAA,CAAA0C,UAAA,IAAAC,gEAAA,uBAAsE;IAI1E3C,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,cAAwB,gBAC0D;IAAAD,EAAA,CAAAE,MAAA,iCAChF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA6G;IAA1CD,EAAA,CAAAmC,gBAAA,2BAAAS,wFAAAP,MAAA;MAAArC,EAAA,CAAAa,aAAA,CAAAyB,GAAA;MAAA,MAAAtB,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAuC,kBAAA,CAAAvB,MAAA,CAAA6B,qBAAA,CAAAhB,KAAA,EAAAQ,MAAA,MAAArB,MAAA,CAAA6B,qBAAA,CAAAhB,KAAA,GAAAQ,MAAA;MAAA,OAAArC,EAAA,CAAAoB,WAAA,CAAAiB,MAAA;IAAA,EAAyC;IAC5GrC,EAAA,CAAAE,MAAA;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,eAA+B,iBACkE;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3GH,EAAA,CAAAC,cAAA,qBAAoF;IAAtDD,EAAA,CAAAmC,gBAAA,2BAAAW,yFAAAT,MAAA;MAAArC,EAAA,CAAAa,aAAA,CAAAyB,GAAA;MAAA,MAAAtB,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAuC,kBAAA,CAAAvB,MAAA,CAAAwB,cAAA,CAAAZ,SAAA,EAAAS,MAAA,MAAArB,MAAA,CAAAwB,cAAA,CAAAZ,SAAA,GAAAS,MAAA;MAAA,OAAArC,EAAA,CAAAoB,WAAA,CAAAiB,MAAA;IAAA,EAAsC;IAClErC,EAAA,CAAA0C,UAAA,KAAAK,iEAAA,uBAA2D;IAI/D/C,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,eAA+B,iBAC+D;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtGH,EAAA,CAAAC,cAAA,qBAAgF;IAApDD,EAAA,CAAAmC,gBAAA,2BAAAa,yFAAAX,MAAA;MAAArC,EAAA,CAAAa,aAAA,CAAAyB,GAAA;MAAA,MAAAtB,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAuC,kBAAA,CAAAvB,MAAA,CAAAwB,cAAA,CAAAV,OAAA,EAAAO,MAAA,MAAArB,MAAA,CAAAwB,cAAA,CAAAV,OAAA,GAAAO,MAAA;MAAA,OAAArC,EAAA,CAAAoB,WAAA,CAAAiB,MAAA;IAAA,EAAoC;IAC9DrC,EAAA,CAAA0C,UAAA,KAAAO,iEAAA,uBAAiE;IAKvEjD,EAFI,CAAAG,YAAA,EAAY,EACR,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACc;IAAvBD,EAAA,CAAAU,UAAA,mBAAAwC,8EAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAa,aAAA,CAAAyB,GAAA,EAAAc,SAAA;MAAA,MAAApC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASJ,MAAA,CAAAqC,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAACnD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnGH,EAAA,CAAAC,cAAA,kBAA+D;IAAxBD,EAAA,CAAAU,UAAA,mBAAA4C,8EAAA;MAAA,MAAAH,OAAA,GAAAnD,EAAA,CAAAa,aAAA,CAAAyB,GAAA,EAAAc,SAAA;MAAA,MAAApC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASJ,MAAA,CAAAuC,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAACnD,EAAA,CAAAE,MAAA,oBAAE;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EAC3D,EACT;;;;IAjCwBH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAwD,gBAAA,YAAAxC,MAAA,CAAAwB,cAAA,CAAAC,YAAA,CAAyC;IACvCzC,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAyC,sBAAA,CAAyB;IAQYzD,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAwD,gBAAA,YAAAxC,MAAA,CAAA6B,qBAAA,CAAAhB,KAAA,CAAyC;IAK9E7B,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAAwD,gBAAA,YAAAxC,MAAA,CAAAwB,cAAA,CAAAZ,SAAA,CAAsC;IACtC5B,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAA0C,WAAA,CAAc;IAOhB1D,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAwD,gBAAA,YAAAxC,MAAA,CAAAwB,cAAA,CAAAV,OAAA,CAAoC;IAChC9B,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAA2C,aAAA,CAAgB;IAOgB3D,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAwB,iBAAA,CAAAR,MAAA,CAAA4C,KAAA,mCAAwB;;;AD7FhG,OAAM,MAAOC,4BAA6B,SAAQhE,aAAa;EAC7DiE,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,sBAA4C,EAC5CC,OAAsB;IAC5B,KAAK,CAACN,MAAM,CAAC;IAPP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,OAAO,GAAPA,OAAO;IAGR,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,iBAAiB,GAAmC,EAAE;IAItD,KAAAC,iBAAiB,GAA6B;MAC5C7C,OAAO,EAAE,CAAC;MACVW,YAAY,EAAE,CAAC;MACfb,SAAS,EAAE,CAAC;MACZC,KAAK,EAAE;KACR;IAED,KAAA8B,aAAa,GAAiB,CAC5B;MACEiB,KAAK,EAAE,CAAC;MACRpE,KAAK,EAAE;KACR,EACD;MACEoE,KAAK,EAAE,CAAC;MACRpE,KAAK,EAAE;KACR,CACF;IAED,KAAAqE,cAAc,GAAU,CACtB;MACED,KAAK,EAAE,EAAE;MACTpE,KAAK,EAAE;KACR,EACD;MACEoE,KAAK,EAAE,CAAC;MACRpE,KAAK,EAAE;KACR,EAAE;MACDoE,KAAK,EAAE,CAAC;MACRpE,KAAK,EAAE;KACR,CACF;IAED,KAAAkD,WAAW,GAAU,CACnB;MACEkB,KAAK,EAAE,CAAC;MACRpE,KAAK,EAAE;KACR,EAAE;MACDoE,KAAK,EAAE,CAAC;MACRpE,KAAK,EAAE;KACR,CACF;IAED,KAAAsE,gBAAgB,GAAU,CAAC;MAAEtE,KAAK,EAAE,IAAI;MAAEoE,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAnB,sBAAsB,GAAU,EAAE;IAClC,KAAAG,KAAK,GAAG,IAAI;EAvDM;EA8DTmB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,QAAQ,GAAG;MACdrD,SAAS,EAAE,IAAI,CAACiD,cAAc,CAAC,CAAC,CAAC;MACjCpC,YAAY,EAAE,IAAI,CAACqC,gBAAgB,CAAC,CAAC;KACtC;IAED,IAAI,CAACtC,cAAc,GAAG;MACpBZ,SAAS,EAAE,IAAI,CAAC8B,WAAW,CAAC,CAAC,CAAC;MAC9BjB,YAAY,EAAE,IAAI,CAACgB,sBAAsB,CAAC,CAAC,CAAC;MAC5C3B,OAAO,EAAE,IAAI,CAAC6B,aAAa,CAAC,CAAC;KAC9B;IAED,IAAI,CAACuB,WAAW,GAAG;MACjBzC,YAAY,EAAE,IAAI,CAACqC,gBAAgB,CAAC,CAAC,CAAC,CAACF,KAAK;MAC5ChD,SAAS,EAAE,IAAI,CAAC8B,WAAW,CAAC,CAAC,CAAC,CAACkB,KAAK;MACpC/C,KAAK,EAAE;KACR;IAED,IAAI,CAACsD,oBAAoB,EAAE;IAE3B,IAAI,CAACtC,qBAAqB,GAAG,IAAI,CAAC8B,iBAAiB;EACrD;EAEAS,iBAAiBA,CAACC,GAAQ;IACxB,MAAMC,MAAM,GAAG;MAAE,GAAGD;IAAG,CAAE;IACzB,KAAK,MAAME,GAAG,IAAID,MAAM,EAAE;MACxB,IAAIA,MAAM,CAACC,GAAG,CAAC,KAAK,EAAE,IAAID,MAAM,CAACC,GAAG,CAAC,KAAK,CAAC,EAAE;QAC3C,OAAOD,MAAM,CAACC,GAAG,CAAC;MACpB;IACF;IACA,OAAOD,MAAM;EACf;EAGAE,kBAAkBA,CAAA;IAChB,IAAI,CAACN,WAAW,GAAG;MACjB,GAAG,IAAI,CAACA,WAAW;MACnBzC,YAAY,EAAE,IAAI,CAACwC,QAAQ,CAACxC,YAAY,CAACmC,KAAK;MAC9ChD,SAAS,EAAE,IAAI,CAACqD,QAAQ,CAACrD,SAAS,CAACgD,KAAK;MACxCJ,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBD,QAAQ,EAAE,IAAI,CAACA;KAChB;IACD,OAAO;MAAE,GAAG,IAAI,CAACa,iBAAiB,CAAC,IAAI,CAACF,WAAW;IAAC,CAAE;EAExD;EAEAC,oBAAoBA,CAAA;IAClB,IAAI,CAACf,sBAAsB,CAACqB,6CAA6C,CAAC;MAAEC,IAAI,EAAE,IAAI,CAACF,kBAAkB;IAAE,CAAE,CAAC,CAACG,SAAS,CAACC,GAAG,IAAG;MAC7H,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACvC,IAAI,CAACpB,iBAAiB,GAAGkB,GAAG,CAACC,OAAQ,IAAI,EAAE;QAC3C,IAAI,CAACpB,YAAY,GAAGmB,GAAG,CAACG,UAAW;MACrC;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACzB,SAAS,GAAGyB,OAAO;IACxB,IAAI,CAACd,oBAAoB,EAAE;EAC7B;EAGAH,gBAAgBA,CAAA;IACd,IAAI,CAACb,iBAAiB,CAAC+B,6CAA6C,CAAC;MAAER,IAAI,EAAE;QAC3ES,OAAO,EAAE,KAAK;QACdrE,OAAO,EAAE;;IACV,CAAE,CAAC,CAAC6D,SAAS,CAACC,GAAG,IAAG;MACnB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,MAAMM,OAAO,GAAGR,GAAG,CAACC,OAAO,CAACQ,GAAG,CAACC,CAAC,IAAG;UAClC,OAAO;YACL9F,KAAK,EAAE8F,CAAC,EAAE5E,cAAc;YACxBkD,KAAK,EAAE0B,CAAC,EAAEC;WACX;QACH,CAAC,CAAC;QACF,IAAI,CAACzB,gBAAgB,GAAG,CAAC;UAAEtE,KAAK,EAAE,IAAI;UAAEoE,KAAK,EAAE;QAAE,CAAE,EAAE,GAAGwB,OAAO,CAAC;QAChE,IAAI,CAAC3C,sBAAsB,GAAG,CAAE,GAAG2C,OAAO,CAAC;MAC7C;IACF,CAAC,CAAC;EACJ;EAEAI,MAAMA,CAACC,GAAQ;IACb,IAAI,CAAC7C,KAAK,GAAG,IAAI;IACjB,IAAI,CAACf,qBAAqB,GAAG,IAAI,CAAC8B,iBAAiB;IACnD,IAAI,CAACnC,cAAc,GAAG;MACpBZ,SAAS,EAAE,IAAI,CAAC8B,WAAW,CAAC,CAAC,CAAC;MAC9BjB,YAAY,EAAE,IAAI,CAACgB,sBAAsB,CAAC,CAAC,CAAC;MAC5C3B,OAAO,EAAE,IAAI,CAAC6B,aAAa,CAAC,CAAC;KAC9B;IACD,IAAI,CAACK,aAAa,CAAC0C,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEApF,uBAAuBA,CAACsF,IAAS,EAAEF,GAAQ;IACzC,IAAI,CAACrC,sBAAsB,CAACqB,6CAA6C,CAAC;MACxEC,IAAI,EAAE;QACJjE,gBAAgB,EAAEkF,IAAI,CAAClF;;KAE1B,CAAC,CAACkE,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,IAAIF,GAAG,CAACC,OAAO,CAACe,MAAM,EAAE;QAC5D,IAAI,CAAC/D,qBAAqB,GAAG;UAC3BpB,gBAAgB,EAAGmE,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,CAACpE,gBAAgB;UAClDgB,YAAY,EAAGmD,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,CAACgB,YAAY;UAC1ChF,KAAK,EAAG+D,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,CAAChE,KAAK;UAC5BD,SAAS,EAAGgE,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,CAACjE,SAAS;UACpCE,OAAO,EAAI8D,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC/D;SAC3B;QACD,IAAI,CAACU,cAAc,GAAG;UACpBZ,SAAS,EAAE,IAAI,CAAC8B,WAAW,CAACoD,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACnC,KAAK,KAAK,IAAI,CAAC/B,qBAAqB,CAACjB,SAAS,CAAC;UAC7Fa,YAAY,EAAE,IAAI,CAACgB,sBAAsB,CAACqD,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACnC,KAAK,KAAK,IAAI,CAAC/B,qBAAqB,CAACJ,YAAY,CAAC;UAC9GX,OAAO,EAAE,IAAI,CAAC6B,aAAa,CAACmD,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACnC,KAAK,KAAK,IAAI,CAAC/B,qBAAqB,CAACf,OAAO;SAC3F;MACH;IACF,CAAC,CAAC;IACF,IAAI,CAACe,qBAAqB,GAAG8D,IAAI;IACjC,IAAI,CAAC3C,aAAa,CAAC0C,IAAI,CAACD,GAAG,CAAC;EAC9B;EAIAO,UAAUA,CAAA;IACR,IAAI,CAAC9C,KAAK,CAAC+C,KAAK,EAAE;IAClB,IAAI,CAAC/C,KAAK,CAACgD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACrE,qBAAqB,CAACJ,YAAY,CAAC;IACpE;EACF;EAEAc,QAAQA,CAACkD,GAAQ;IACf,IAAI,CAAC5D,qBAAqB,GAAG;MAC3B,GAAG,IAAI,CAACA,qBAAqB;MAC7BJ,YAAY,EAAE,IAAI,CAACD,cAAc,CAACC,YAAY,CAACmC,KAAK;MACpDhD,SAAS,EAAE,IAAI,CAACY,cAAc,CAACZ,SAAS,CAACgD,KAAK;MAC9C9C,OAAO,EAAE,IAAI,CAACU,cAAc,CAACV,OAAO,CAAC8C;KACtC;IACD,IAAI,CAACoC,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC9C,KAAK,CAACiD,aAAa,CAACP,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC3C,OAAO,CAACmD,aAAa,CAAC,IAAI,CAAClD,KAAK,CAACiD,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAAC/C,sBAAsB,CAACiD,0CAA0C,CAAC;MACrE3B,IAAI,EAAE,IAAI,CAAC7C;KACZ,CAAC,CAAC8C,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAC7B,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACnC,oBAAoB,EAAE;QAC3BsB,GAAG,CAACc,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACtD,OAAO,CAACuD,YAAY,CAAC5B,GAAG,CAAC6B,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;EAEAlG,QAAQA,CAACoF,IAAS;IAChB,IAAIe,MAAM,CAACC,OAAO,CAAC,WAAWhB,IAAI,CAACjF,cAAc,IAAI,CAAC,EAAE;MACtD,IAAI,CAAC0C,sBAAsB,CAACwD,4CAA4C,CAAC;QACvElC,IAAI,EAAEiB,IAAI,CAAClF;OACZ,CAAC,CAACkE,SAAS,CAACC,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC7B,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACnC,oBAAoB,EAAE;QAC7B,CAAC,MAAM;UACL,IAAI,CAAClB,OAAO,CAACuD,YAAY,CAAC5B,GAAG,CAAC6B,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAEApE,OAAOA,CAACoD,GAAQ;IACdA,GAAG,CAACc,KAAK,EAAE;EACb;;;uCA7OW1D,4BAA4B,EAAA7D,EAAA,CAAA6H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/H,EAAA,CAAA6H,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAjI,EAAA,CAAA6H,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAnI,EAAA,CAAA6H,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAArI,EAAA,CAAA6H,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAvI,EAAA,CAAA6H,iBAAA,CAAAS,EAAA,CAAAE,oBAAA,GAAAxI,EAAA,CAAA6H,iBAAA,CAAAY,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA5B7E,4BAA4B;MAAA8E,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7I,EAAA,CAAA8I,0BAAA,EAAA9I,EAAA,CAAA+I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC3BvCrJ,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAkC,SAAA,qBAAiC;UACnClC,EAAA,CAAAG,YAAA,EAAiB;UAKTH,EAJR,CAAAC,cAAA,mBAAc,aACkB,aACN,aACqC,eACV;UAAAD,EAAA,CAAAE,MAAA,+BAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzDH,EAAA,CAAAC,cAAA,mBAAiF;UAAnDD,EAAA,CAAAmC,gBAAA,2BAAAoH,yEAAAlH,MAAA;YAAArC,EAAA,CAAAa,aAAA,CAAA2I,GAAA;YAAAxJ,EAAA,CAAAuC,kBAAA,CAAA+G,GAAA,CAAArE,QAAA,CAAAxC,YAAA,EAAAJ,MAAA,MAAAiH,GAAA,CAAArE,QAAA,CAAAxC,YAAA,GAAAJ,MAAA;YAAA,OAAArC,EAAA,CAAAoB,WAAA,CAAAiB,MAAA;UAAA,EAAmC;UAC/DrC,EAAA,CAAA0C,UAAA,KAAA+G,kDAAA,uBAAgE;UAKtEzJ,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cAC8B,gBACT;UAAAD,EAAA,CAAAE,MAAA,iCACvC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,qBAAe,iBACyE;UAAhCD,EAAA,CAAAmC,gBAAA,2BAAAuH,sEAAArH,MAAA;YAAArC,EAAA,CAAAa,aAAA,CAAA2I,GAAA;YAAAxJ,EAAA,CAAAuC,kBAAA,CAAA+G,GAAA,CAAApE,WAAA,CAAAyE,KAAA,EAAAtH,MAAA,MAAAiH,GAAA,CAAApE,WAAA,CAAAyE,KAAA,GAAAtH,MAAA;YAAA,OAAArC,EAAA,CAAAoB,WAAA,CAAAiB,MAAA;UAAA,EAA+B;UAG3FrC,EAHM,CAAAG,YAAA,EAAsF,EACxE,EACZ,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cAC8B,iBACH;UAAAD,EAAA,CAAAE,MAAA,iCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1DH,EAAA,CAAAC,cAAA,qBAA8E;UAAhDD,EAAA,CAAAmC,gBAAA,2BAAAyH,0EAAAvH,MAAA;YAAArC,EAAA,CAAAa,aAAA,CAAA2I,GAAA;YAAAxJ,EAAA,CAAAuC,kBAAA,CAAA+G,GAAA,CAAArE,QAAA,CAAArD,SAAA,EAAAS,MAAA,MAAAiH,GAAA,CAAArE,QAAA,CAAArD,SAAA,GAAAS,MAAA;YAAA,OAAArC,EAAA,CAAAoB,WAAA,CAAAiB,MAAA;UAAA,EAAgC;UAC5DrC,EAAA,CAAA0C,UAAA,KAAAmH,kDAAA,uBAA8D;UAKpE7J,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eAC2B,kBACc;UAAzBD,EAAA,CAAAU,UAAA,mBAAAoJ,+DAAA;YAAA9J,EAAA,CAAAa,aAAA,CAAA2I,GAAA;YAAA,MAAAtI,SAAA,GAAAlB,EAAA,CAAAmB,WAAA;YAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASkI,GAAA,CAAA9C,MAAA,CAAAtF,SAAA,CAAc;UAAA,EAAC;UACxDlB,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAkC,SAAA,aAA2B;UAChClC,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA8D;UAAjCD,EAAA,CAAAU,UAAA,mBAAAqJ,+DAAA;YAAA/J,EAAA,CAAAa,aAAA,CAAA2I,GAAA;YAAA,OAAAxJ,EAAA,CAAAoB,WAAA,CAASkI,GAAA,CAAAnE,oBAAA,EAAsB;UAAA,EAAC;UAC3DnF,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAkC,SAAA,aAA6B;UAIxClC,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBACoC,iBACvC,UACtB,cAC4B;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA0C,UAAA,KAAAsH,2CAAA,kBAA2D;UAgBnEhK,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAmC,gBAAA,wBAAA8H,4EAAA5H,MAAA;YAAArC,EAAA,CAAAa,aAAA,CAAA2I,GAAA;YAAAxJ,EAAA,CAAAuC,kBAAA,CAAA+G,GAAA,CAAA9E,SAAA,EAAAnC,MAAA,MAAAiH,GAAA,CAAA9E,SAAA,GAAAnC,MAAA;YAAA,OAAArC,EAAA,CAAAoB,WAAA,CAAAiB,MAAA;UAAA,EAAoB;UAClCrC,EAAA,CAAAU,UAAA,wBAAAuJ,4EAAA5H,MAAA;YAAArC,EAAA,CAAAa,aAAA,CAAA2I,GAAA;YAAA,OAAAxJ,EAAA,CAAAoB,WAAA,CAAckI,GAAA,CAAAtD,WAAA,CAAA3D,MAAA,CAAmB;UAAA,EAAC;UAGxCrC,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAEVH,EAAA,CAAA0C,UAAA,KAAAwH,oDAAA,iCAAAlK,EAAA,CAAAmK,sBAAA,CAAoD;;;UA3EZnK,EAAA,CAAAM,SAAA,GAAmC;UAAnCN,EAAA,CAAAwD,gBAAA,YAAA8F,GAAA,CAAArE,QAAA,CAAAxC,YAAA,CAAmC;UACnCzC,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAkJ,GAAA,CAAAxE,gBAAA,CAAmB;UAWO9E,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAwD,gBAAA,YAAA8F,GAAA,CAAApE,WAAA,CAAAyE,KAAA,CAA+B;UAOzD3J,EAAA,CAAAM,SAAA,GAAgC;UAAhCN,EAAA,CAAAwD,gBAAA,YAAA8F,GAAA,CAAArE,QAAA,CAAArD,SAAA,CAAgC;UAChC5B,EAAA,CAAAM,SAAA,EAAiB;UAAjBN,EAAA,CAAAI,UAAA,YAAAkJ,GAAA,CAAAzE,cAAA,CAAiB;UA+B1B7E,EAAA,CAAAM,SAAA,IAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAkJ,GAAA,CAAA5E,iBAAA,CAAuB;UAkBlC1E,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAwD,gBAAA,SAAA8F,GAAA,CAAA9E,SAAA,CAAoB;UAAuBxE,EAAtB,CAAAI,UAAA,aAAAkJ,GAAA,CAAA/E,QAAA,CAAqB,mBAAA+E,GAAA,CAAA7E,YAAA,CAAgC;;;qBDvD1F3E,YAAY,EAAAsK,EAAA,CAAAC,OAAA,EACZtK,YAAY,EAAAuK,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAzC,EAAA,CAAA0C,eAAA,EAAA1C,EAAA,CAAA2C,mBAAA,EAAA3C,EAAA,CAAA4C,qBAAA,EAAA5C,EAAA,CAAA6C,qBAAA,EAAA7C,EAAA,CAAA8C,gBAAA,EAAA9C,EAAA,CAAA+C,iBAAA,EAAA/C,EAAA,CAAAgD,iBAAA,EAAAhD,EAAA,CAAAiD,oBAAA,EAAAC,EAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAAC,GAAA,CAAAC,cAAA,EAAAD,GAAA,CAAAE,YAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}