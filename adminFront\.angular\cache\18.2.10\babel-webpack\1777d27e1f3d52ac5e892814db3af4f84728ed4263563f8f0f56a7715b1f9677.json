{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nlet SpaceTemplateSelectorComponent = class SpaceTemplateSelectorComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.isVisible = false;\n    this.buildCaseId = '';\n    this.templateApplied = new EventEmitter();\n    this.closed = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templateGroups = [];\n  }\n  ngOnInit() {\n    // 自動載入模板資料\n    this.loadTemplatesFromAPI();\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: 1,\n      // 1=客變需求\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 將API資料轉換為組件需要的格式\n          this.convertAPIDataToTemplateGroups(response.Entries);\n        } else {\n          // API 返回錯誤\n          this.templateGroups = [];\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n        this.templateGroups = [];\n      }\n    });\n  }\n  convertAPIDataToTemplateGroups(apiData) {\n    // 將API資料轉換為模板群組格式\n    const groupMap = new Map();\n    apiData.forEach(item => {\n      const groupName = item.CGroupName || '預設群組';\n      if (!groupMap.has(groupName)) {\n        groupMap.set(groupName, []);\n      }\n      groupMap.get(groupName).push({\n        id: item.CTemplateId?.toString() || '',\n        name: item.CTemplateName || '',\n        code: item.CTemplateId?.toString() || '',\n        price: 0,\n        // API中沒有價格資訊，預設為0\n        unit: '個',\n        selected: false\n      });\n    });\n    // 轉換為模板群組陣列\n    this.templateGroups = Array.from(groupMap.entries()).map(([groupName, items], index) => ({\n      id: `group-${index}`,\n      name: groupName,\n      icon: '📋',\n      // 預設圖示\n      items: items,\n      expanded: false\n    }));\n  }\n  selectSpace(space) {\n    // 移除此方法，不再需要\n  }\n  loadTemplatesForSpace(spaceId) {\n    // 根據空間ID載入對應的模板群組\n    // 這裡可以調用API或使用預設資料\n    this.templateGroups = this.getDefaultTemplatesForSpace(spaceId);\n  }\n  getDefaultTemplatesForSpace(spaceId) {\n    const templates = {\n      kitchen: [{\n        id: 'kitchen-standard',\n        name: '廚房標準配備',\n        icon: '🔧',\n        expanded: false,\n        items: [{\n          id: 'kt001',\n          name: '洗碗機',\n          code: 'KT001',\n          price: 38000,\n          unit: '台',\n          selected: false\n        }, {\n          id: 'kt002',\n          name: '烤箱',\n          code: 'KT002',\n          price: 25000,\n          unit: '台',\n          selected: false\n        }, {\n          id: 'kt003',\n          name: '抽油煙機升級',\n          code: 'KT003',\n          price: 15000,\n          unit: '台',\n          selected: false\n        }]\n      }, {\n        id: 'kitchen-premium',\n        name: '廚房高級配備',\n        icon: '⭐',\n        expanded: false,\n        items: [{\n          id: 'kt004',\n          name: '中島檯面',\n          code: 'KT004',\n          price: 80000,\n          unit: '組',\n          selected: false\n        }, {\n          id: 'kt005',\n          name: '智能電磁爐',\n          code: 'KT005',\n          price: 45000,\n          unit: '台',\n          selected: false\n        }]\n      }],\n      living: [{\n        id: 'living-basic',\n        name: '客廳基本配備',\n        icon: '🏠',\n        expanded: false,\n        items: [{\n          id: 'lv001',\n          name: '投影設備',\n          code: 'LV001',\n          price: 50000,\n          unit: '組',\n          selected: false\n        }, {\n          id: 'lv002',\n          name: '音響系統',\n          code: 'LV002',\n          price: 35000,\n          unit: '組',\n          selected: false\n        }]\n      }]\n    };\n    return templates[spaceId] || [];\n  }\n  toggleTemplateGroup(group) {\n    // 收合其他群組\n    this.templateGroups.forEach(g => {\n      if (g.id !== group.id) {\n        g.expanded = false;\n      }\n    });\n    // 切換當前群組\n    group.expanded = !group.expanded;\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  getTotalPrice(items) {\n    return items.reduce((total, item) => total + (item.selected ? item.price : 0), 0);\n  }\n  getSelectedItems() {\n    const selected = [];\n    this.templateGroups.forEach(group => {\n      group.items.forEach(item => {\n        if (item.selected) {\n          selected.push(item);\n        }\n      });\n    });\n    return selected;\n  }\n  getSelectedTotalPrice() {\n    return this.getSelectedItems().reduce((total, item) => total + item.price, 0);\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要套用的模板項目',\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    if (!this.selectedSpace) return;\n    const config = {\n      spaceId: this.selectedSpace.id,\n      spaceName: this.selectedSpace.name,\n      selectedItems: this.getSelectedItems(),\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.isVisible = false;\n    this.reset();\n    this.closed.emit();\n  }\n  onBackdropClick(event) {\n    if (event.target === event.currentTarget) {\n      this.close();\n    }\n  }\n  reset() {\n    this.currentStep = 1;\n    this.selectedSpace = null;\n    this.templateGroups = [];\n  }\n  // 公共API方法\n  open(preSelectedSpaceId) {\n    this.isVisible = true;\n    this.reset();\n    if (preSelectedSpaceId) {\n      const space = this.availableSpaces.find(s => s.id === preSelectedSpaceId);\n      if (space) {\n        this.selectSpace(space);\n      }\n    }\n  }\n};\n__decorate([Input()], SpaceTemplateSelectorComponent.prototype, \"isVisible\", void 0);\n__decorate([Input()], SpaceTemplateSelectorComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Output()], SpaceTemplateSelectorComponent.prototype, \"templateApplied\", void 0);\n__decorate([Output()], SpaceTemplateSelectorComponent.prototype, \"closed\", void 0);\nSpaceTemplateSelectorComponent = __decorate([Component({\n  selector: 'app-space-template-selector',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './space-template-selector.component.html',\n  styleUrls: ['./space-template-selector.component.scss']\n})], SpaceTemplateSelectorComponent);\nexport { SpaceTemplateSelectorComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "FormsModule", "SpaceTemplateSelectorComponent", "constructor", "templateService", "isVisible", "buildCaseId", "templateApplied", "closed", "currentStep", "templateGroups", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "convertAPIDataToTemplateGroups", "error", "apiData", "groupMap", "Map", "for<PERSON>ach", "item", "groupName", "CGroupName", "has", "set", "get", "push", "id", "CTemplateId", "toString", "name", "code", "price", "unit", "selected", "Array", "from", "entries", "map", "items", "index", "icon", "expanded", "selectSpace", "space", "loadTemplatesForSpace", "spaceId", "getDefaultTemplatesForSpace", "templates", "kitchen", "living", "toggleTemplateGroup", "group", "g", "onTemplateItemChange", "getTotalPrice", "reduce", "total", "getSelectedItems", "getSelectedTotalPrice", "canProceed", "length", "nextStep", "previousStep", "getProgressText", "progressTexts", "hasConflicts", "getConflictCount", "applyTemplate", "selectedSpace", "config", "spaceName", "selectedItems", "totalPrice", "emit", "close", "reset", "onBackdropClick", "event", "target", "currentTarget", "open", "preSelectedSpaceId", "availableSpaces", "find", "s", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { TemplateService } from '../../../services/api/services/template.service';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs } from 'src/services/api/models';\r\n\r\nexport interface SpaceOption {\r\n    id: string;\r\n    name: string;\r\n    icon: string;\r\n    templateCount: number;\r\n}\r\n\r\nexport interface TemplateItem {\r\n    id: string;\r\n    name: string;\r\n    code: string;\r\n    price: number;\r\n    unit: string;\r\n    selected: boolean;\r\n}\r\n\r\nexport interface TemplateGroup {\r\n    id: string;\r\n    name: string;\r\n    icon: string;\r\n    items: TemplateItem[];\r\n    expanded: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n    spaceId: string;\r\n    spaceName: string;\r\n    selectedItems: TemplateItem[];\r\n    totalPrice: number;\r\n}\r\n\r\n@Component({\r\n    selector: 'app-space-template-selector',\r\n    standalone: true,\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule\r\n    ],\r\n    templateUrl: './space-template-selector.component.html',\r\n    styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n    @Input() isVisible: boolean = false;\r\n    @Input() buildCaseId: string = '';\r\n    @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n    @Output() closed = new EventEmitter<void>();\r\n\r\n    currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n    templateGroups: TemplateGroup[] = [];\r\n\r\n    constructor(private templateService: TemplateService) {}\r\n\r\n    ngOnInit() {\r\n        // 自動載入模板資料\r\n        this.loadTemplatesFromAPI();\r\n    }\r\n\r\n    loadTemplatesFromAPI() {\r\n        // 準備 API 請求參數\r\n        const getTemplateListArgs: TemplateGetListArgs = {\r\n            CTemplateType: 1, // 1=客變需求\r\n            PageIndex: 1,\r\n            PageSize: 100, // 載入足夠的資料\r\n            CTemplateName: null // 不篩選名稱\r\n        };\r\n\r\n        // 調用 GetTemplateListForCommon API\r\n        this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n            body: getTemplateListArgs\r\n        }).subscribe({\r\n            next: (response) => {\r\n                if (response.StatusCode === 0 && response.Entries) {\r\n                    // 將API資料轉換為組件需要的格式\r\n                    this.convertAPIDataToTemplateGroups(response.Entries);\r\n                } else {\r\n                    // API 返回錯誤\r\n                    this.templateGroups = [];\r\n                }\r\n            },\r\n            error: () => {\r\n                // HTTP 請求錯誤\r\n                this.templateGroups = [];\r\n            }\r\n        });\r\n    }\r\n\r\n    convertAPIDataToTemplateGroups(apiData: any[]) {\r\n        // 將API資料轉換為模板群組格式\r\n        const groupMap = new Map<string, TemplateItem[]>();\r\n\r\n        apiData.forEach(item => {\r\n            const groupName = item.CGroupName || '預設群組';\r\n            if (!groupMap.has(groupName)) {\r\n                groupMap.set(groupName, []);\r\n            }\r\n            \r\n            groupMap.get(groupName)!.push({\r\n                id: item.CTemplateId?.toString() || '',\r\n                name: item.CTemplateName || '',\r\n                code: item.CTemplateId?.toString() || '',\r\n                price: 0, // API中沒有價格資訊，預設為0\r\n                unit: '個',\r\n                selected: false\r\n            });\r\n        });\r\n\r\n        // 轉換為模板群組陣列\r\n        this.templateGroups = Array.from(groupMap.entries()).map(([groupName, items], index) => ({\r\n            id: `group-${index}`,\r\n            name: groupName,\r\n            icon: '📋', // 預設圖示\r\n            items: items,\r\n            expanded: false\r\n        }));\r\n    }\r\n\r\n    selectSpace(space: SpaceOption) {\r\n        // 移除此方法，不再需要\r\n    }\r\n\r\n    loadTemplatesForSpace(spaceId: string) {\r\n        // 根據空間ID載入對應的模板群組\r\n        // 這裡可以調用API或使用預設資料\r\n        this.templateGroups = this.getDefaultTemplatesForSpace(spaceId);\r\n    }\r\n\r\n    getDefaultTemplatesForSpace(spaceId: string): TemplateGroup[] {\r\n        const templates: Record<string, TemplateGroup[]> = {\r\n            kitchen: [\r\n                {\r\n                    id: 'kitchen-standard',\r\n                    name: '廚房標準配備',\r\n                    icon: '🔧',\r\n                    expanded: false,\r\n                    items: [\r\n                        { id: 'kt001', name: '洗碗機', code: 'KT001', price: 38000, unit: '台', selected: false },\r\n                        { id: 'kt002', name: '烤箱', code: 'KT002', price: 25000, unit: '台', selected: false },\r\n                        { id: 'kt003', name: '抽油煙機升級', code: 'KT003', price: 15000, unit: '台', selected: false }\r\n                    ]\r\n                },\r\n                {\r\n                    id: 'kitchen-premium',\r\n                    name: '廚房高級配備',\r\n                    icon: '⭐',\r\n                    expanded: false,\r\n                    items: [\r\n                        { id: 'kt004', name: '中島檯面', code: 'KT004', price: 80000, unit: '組', selected: false },\r\n                        { id: 'kt005', name: '智能電磁爐', code: 'KT005', price: 45000, unit: '台', selected: false }\r\n                    ]\r\n                }\r\n            ],\r\n            living: [\r\n                {\r\n                    id: 'living-basic',\r\n                    name: '客廳基本配備',\r\n                    icon: '🏠',\r\n                    expanded: false,\r\n                    items: [\r\n                        { id: 'lv001', name: '投影設備', code: 'LV001', price: 50000, unit: '組', selected: false },\r\n                        { id: 'lv002', name: '音響系統', code: 'LV002', price: 35000, unit: '組', selected: false }\r\n                    ]\r\n                }\r\n            ]\r\n        };\r\n\r\n        return templates[spaceId] || [];\r\n    }\r\n\r\n    toggleTemplateGroup(group: TemplateGroup) {\r\n        // 收合其他群組\r\n        this.templateGroups.forEach(g => {\r\n            if (g.id !== group.id) {\r\n                g.expanded = false;\r\n            }\r\n        });\r\n\r\n        // 切換當前群組\r\n        group.expanded = !group.expanded;\r\n    }\r\n\r\n    onTemplateItemChange() {\r\n        // 當模板項目選擇變更時的處理\r\n    }\r\n\r\n    getTotalPrice(items: TemplateItem[]): number {\r\n        return items.reduce((total, item) => total + (item.selected ? item.price : 0), 0);\r\n    }\r\n\r\n    getSelectedItems(): TemplateItem[] {\r\n        const selected: TemplateItem[] = [];\r\n        this.templateGroups.forEach(group => {\r\n            group.items.forEach(item => {\r\n                if (item.selected) {\r\n                    selected.push(item);\r\n                }\r\n            });\r\n        });\r\n        return selected;\r\n    }\r\n\r\n    getSelectedTotalPrice(): number {\r\n        return this.getSelectedItems().reduce((total, item) => total + item.price, 0);\r\n    }\r\n\r\n    canProceed(): boolean {\r\n        switch (this.currentStep) {\r\n            case 1:\r\n                return this.getSelectedItems().length > 0;\r\n            case 2:\r\n                return true;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n\r\n    nextStep() {\r\n        if (this.canProceed() && this.currentStep < 2) {\r\n            this.currentStep++;\r\n        }\r\n    }\r\n\r\n    previousStep() {\r\n        if (this.currentStep > 1) {\r\n            this.currentStep--;\r\n        }\r\n    }\r\n\r\n    getProgressText(): string {\r\n        const progressTexts = {\r\n            1: '請選擇要套用的模板項目',\r\n            2: '確認套用詳情'\r\n        };\r\n        return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n    }\r\n\r\n    hasConflicts(): boolean {\r\n        // 模擬衝突檢測邏輯\r\n        return this.getSelectedItems().length > 2;\r\n    }\r\n\r\n    getConflictCount(): number {\r\n        // 模擬衝突數量\r\n        return this.hasConflicts() ? 1 : 0;\r\n    }\r\n\r\n    applyTemplate() {\r\n        if (!this.selectedSpace) return;\r\n\r\n        const config: SpaceTemplateConfig = {\r\n            spaceId: this.selectedSpace.id,\r\n            spaceName: this.selectedSpace.name,\r\n            selectedItems: this.getSelectedItems(),\r\n            totalPrice: this.getSelectedTotalPrice()\r\n        };\r\n\r\n        this.templateApplied.emit(config);\r\n        this.close();\r\n    }\r\n\r\n    close() {\r\n        this.isVisible = false;\r\n        this.reset();\r\n        this.closed.emit();\r\n    }\r\n\r\n    onBackdropClick(event: Event) {\r\n        if (event.target === event.currentTarget) {\r\n            this.close();\r\n        }\r\n    }\r\n\r\n    private reset() {\r\n        this.currentStep = 1;\r\n        this.selectedSpace = null;\r\n        this.templateGroups = [];\r\n    }\r\n\r\n    // 公共API方法\r\n    open(preSelectedSpaceId?: string) {\r\n        this.isVisible = true;\r\n        this.reset();\r\n\r\n        if (preSelectedSpaceId) {\r\n            const space = this.availableSpaces.find(s => s.id === preSelectedSpaceId);\r\n            if (space) {\r\n                this.selectSpace(space);\r\n            }\r\n        }\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAwB,eAAe;AACtF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AA8CrC,IAAMC,8BAA8B,GAApC,MAAMA,8BAA8B;EASvCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAR1B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,WAAW,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAG,IAAIV,YAAY,EAAuB;IACzD,KAAAW,MAAM,GAAG,IAAIX,YAAY,EAAQ;IAE3C,KAAAY,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAC,cAAc,GAAoB,EAAE;EAEmB;EAEvDC,QAAQA,CAAA;IACJ;IACA,IAAI,CAACC,oBAAoB,EAAE;EAC/B;EAEAA,oBAAoBA,CAAA;IAChB;IACA,MAAMC,mBAAmB,GAAwB;MAC7CC,aAAa,EAAE,CAAC;MAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACvB;IAED;IACA,IAAI,CAACb,eAAe,CAACc,4CAA4C,CAAC;MAC9DC,IAAI,EAAEN;KACT,CAAC,CAACO,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACf,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UAC/C;UACA,IAAI,CAACC,8BAA8B,CAACH,QAAQ,CAACE,OAAO,CAAC;QACzD,CAAC,MAAM;UACH;UACA,IAAI,CAACd,cAAc,GAAG,EAAE;QAC5B;MACJ,CAAC;MACDgB,KAAK,EAAEA,CAAA,KAAK;QACR;QACA,IAAI,CAAChB,cAAc,GAAG,EAAE;MAC5B;KACH,CAAC;EACN;EAEAe,8BAA8BA,CAACE,OAAc;IACzC;IACA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,EAA0B;IAElDF,OAAO,CAACG,OAAO,CAACC,IAAI,IAAG;MACnB,MAAMC,SAAS,GAAGD,IAAI,CAACE,UAAU,IAAI,MAAM;MAC3C,IAAI,CAACL,QAAQ,CAACM,GAAG,CAACF,SAAS,CAAC,EAAE;QAC1BJ,QAAQ,CAACO,GAAG,CAACH,SAAS,EAAE,EAAE,CAAC;MAC/B;MAEAJ,QAAQ,CAACQ,GAAG,CAACJ,SAAS,CAAE,CAACK,IAAI,CAAC;QAC1BC,EAAE,EAAEP,IAAI,CAACQ,WAAW,EAAEC,QAAQ,EAAE,IAAI,EAAE;QACtCC,IAAI,EAAEV,IAAI,CAACd,aAAa,IAAI,EAAE;QAC9ByB,IAAI,EAAEX,IAAI,CAACQ,WAAW,EAAEC,QAAQ,EAAE,IAAI,EAAE;QACxCG,KAAK,EAAE,CAAC;QAAE;QACVC,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE;OACb,CAAC;IACN,CAAC,CAAC;IAEF;IACA,IAAI,CAACnC,cAAc,GAAGoC,KAAK,CAACC,IAAI,CAACnB,QAAQ,CAACoB,OAAO,EAAE,CAAC,CAACC,GAAG,CAAC,CAAC,CAACjB,SAAS,EAAEkB,KAAK,CAAC,EAAEC,KAAK,MAAM;MACrFb,EAAE,EAAE,SAASa,KAAK,EAAE;MACpBV,IAAI,EAAET,SAAS;MACfoB,IAAI,EAAE,IAAI;MAAE;MACZF,KAAK,EAAEA,KAAK;MACZG,QAAQ,EAAE;KACb,CAAC,CAAC;EACP;EAEAC,WAAWA,CAACC,KAAkB;IAC1B;EAAA;EAGJC,qBAAqBA,CAACC,OAAe;IACjC;IACA;IACA,IAAI,CAAC/C,cAAc,GAAG,IAAI,CAACgD,2BAA2B,CAACD,OAAO,CAAC;EACnE;EAEAC,2BAA2BA,CAACD,OAAe;IACvC,MAAME,SAAS,GAAoC;MAC/CC,OAAO,EAAE,CACL;QACItB,EAAE,EAAE,kBAAkB;QACtBG,IAAI,EAAE,QAAQ;QACdW,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,KAAK;QACfH,KAAK,EAAE,CACH;UAAEZ,EAAE,EAAE,OAAO;UAAEG,IAAI,EAAE,KAAK;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACrF;UAAEP,EAAE,EAAE,OAAO;UAAEG,IAAI,EAAE,IAAI;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACpF;UAAEP,EAAE,EAAE,OAAO;UAAEG,IAAI,EAAE,QAAQ;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE;OAE/F,EACD;QACIP,EAAE,EAAE,iBAAiB;QACrBG,IAAI,EAAE,QAAQ;QACdW,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE,KAAK;QACfH,KAAK,EAAE,CACH;UAAEZ,EAAE,EAAE,OAAO;UAAEG,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACtF;UAAEP,EAAE,EAAE,OAAO;UAAEG,IAAI,EAAE,OAAO;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE;OAE9F,CACJ;MACDgB,MAAM,EAAE,CACJ;QACIvB,EAAE,EAAE,cAAc;QAClBG,IAAI,EAAE,QAAQ;QACdW,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,KAAK;QACfH,KAAK,EAAE,CACH;UAAEZ,EAAE,EAAE,OAAO;UAAEG,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE,EACtF;UAAEP,EAAE,EAAE,OAAO;UAAEG,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAE;OAE7F;KAER;IAED,OAAOc,SAAS,CAACF,OAAO,CAAC,IAAI,EAAE;EACnC;EAEAK,mBAAmBA,CAACC,KAAoB;IACpC;IACA,IAAI,CAACrD,cAAc,CAACoB,OAAO,CAACkC,CAAC,IAAG;MAC5B,IAAIA,CAAC,CAAC1B,EAAE,KAAKyB,KAAK,CAACzB,EAAE,EAAE;QACnB0B,CAAC,CAACX,QAAQ,GAAG,KAAK;MACtB;IACJ,CAAC,CAAC;IAEF;IACAU,KAAK,CAACV,QAAQ,GAAG,CAACU,KAAK,CAACV,QAAQ;EACpC;EAEAY,oBAAoBA,CAAA;IAChB;EAAA;EAGJC,aAAaA,CAAChB,KAAqB;IAC/B,OAAOA,KAAK,CAACiB,MAAM,CAAC,CAACC,KAAK,EAAErC,IAAI,KAAKqC,KAAK,IAAIrC,IAAI,CAACc,QAAQ,GAAGd,IAAI,CAACY,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACrF;EAEA0B,gBAAgBA,CAAA;IACZ,MAAMxB,QAAQ,GAAmB,EAAE;IACnC,IAAI,CAACnC,cAAc,CAACoB,OAAO,CAACiC,KAAK,IAAG;MAChCA,KAAK,CAACb,KAAK,CAACpB,OAAO,CAACC,IAAI,IAAG;QACvB,IAAIA,IAAI,CAACc,QAAQ,EAAE;UACfA,QAAQ,CAACR,IAAI,CAACN,IAAI,CAAC;QACvB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAOc,QAAQ;EACnB;EAEAyB,qBAAqBA,CAAA;IACjB,OAAO,IAAI,CAACD,gBAAgB,EAAE,CAACF,MAAM,CAAC,CAACC,KAAK,EAAErC,IAAI,KAAKqC,KAAK,GAAGrC,IAAI,CAACY,KAAK,EAAE,CAAC,CAAC;EACjF;EAEA4B,UAAUA,CAAA;IACN,QAAQ,IAAI,CAAC9D,WAAW;MACpB,KAAK,CAAC;QACF,OAAO,IAAI,CAAC4D,gBAAgB,EAAE,CAACG,MAAM,GAAG,CAAC;MAC7C,KAAK,CAAC;QACF,OAAO,IAAI;MACf;QACI,OAAO,KAAK;IACpB;EACJ;EAEAC,QAAQA,CAAA;IACJ,IAAI,IAAI,CAACF,UAAU,EAAE,IAAI,IAAI,CAAC9D,WAAW,GAAG,CAAC,EAAE;MAC3C,IAAI,CAACA,WAAW,EAAE;IACtB;EACJ;EAEAiE,YAAYA,CAAA;IACR,IAAI,IAAI,CAACjE,WAAW,GAAG,CAAC,EAAE;MACtB,IAAI,CAACA,WAAW,EAAE;IACtB;EACJ;EAEAkE,eAAeA,CAAA;IACX,MAAMC,aAAa,GAAG;MAClB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACN;IACD,OAAOA,aAAa,CAAC,IAAI,CAACnE,WAAyC,CAAC,IAAI,EAAE;EAC9E;EAEAoE,YAAYA,CAAA;IACR;IACA,OAAO,IAAI,CAACR,gBAAgB,EAAE,CAACG,MAAM,GAAG,CAAC;EAC7C;EAEAM,gBAAgBA,CAAA;IACZ;IACA,OAAO,IAAI,CAACD,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACtC;EAEAE,aAAaA,CAAA;IACT,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;IAEzB,MAAMC,MAAM,GAAwB;MAChCxB,OAAO,EAAE,IAAI,CAACuB,aAAa,CAAC1C,EAAE;MAC9B4C,SAAS,EAAE,IAAI,CAACF,aAAa,CAACvC,IAAI;MAClC0C,aAAa,EAAE,IAAI,CAACd,gBAAgB,EAAE;MACtCe,UAAU,EAAE,IAAI,CAACd,qBAAqB;KACzC;IAED,IAAI,CAAC/D,eAAe,CAAC8E,IAAI,CAACJ,MAAM,CAAC;IACjC,IAAI,CAACK,KAAK,EAAE;EAChB;EAEAA,KAAKA,CAAA;IACD,IAAI,CAACjF,SAAS,GAAG,KAAK;IACtB,IAAI,CAACkF,KAAK,EAAE;IACZ,IAAI,CAAC/E,MAAM,CAAC6E,IAAI,EAAE;EACtB;EAEAG,eAAeA,CAACC,KAAY;IACxB,IAAIA,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,aAAa,EAAE;MACtC,IAAI,CAACL,KAAK,EAAE;IAChB;EACJ;EAEQC,KAAKA,CAAA;IACT,IAAI,CAAC9E,WAAW,GAAG,CAAC;IACpB,IAAI,CAACuE,aAAa,GAAG,IAAI;IACzB,IAAI,CAACtE,cAAc,GAAG,EAAE;EAC5B;EAEA;EACAkF,IAAIA,CAACC,kBAA2B;IAC5B,IAAI,CAACxF,SAAS,GAAG,IAAI;IACrB,IAAI,CAACkF,KAAK,EAAE;IAEZ,IAAIM,kBAAkB,EAAE;MACpB,MAAMtC,KAAK,GAAG,IAAI,CAACuC,eAAe,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAKuD,kBAAkB,CAAC;MACzE,IAAItC,KAAK,EAAE;QACP,IAAI,CAACD,WAAW,CAACC,KAAK,CAAC;MAC3B;IACJ;EACJ;CACH;AAvPY0C,UAAA,EAARnG,KAAK,EAAE,C,gEAA4B;AAC3BmG,UAAA,EAARnG,KAAK,EAAE,C,kEAA0B;AACxBmG,UAAA,EAATlG,MAAM,EAAE,C,sEAA2D;AAC1DkG,UAAA,EAATlG,MAAM,EAAE,C,6DAAmC;AAJnCG,8BAA8B,GAAA+F,UAAA,EAV1CrG,SAAS,CAAC;EACPsG,QAAQ,EAAE,6BAA6B;EACvCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACLpG,YAAY,EACZC,WAAW,CACd;EACDoG,WAAW,EAAE,0CAA0C;EACvDC,SAAS,EAAE,CAAC,0CAA0C;CACzD,CAAC,C,EACWpG,8BAA8B,CAwP1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}