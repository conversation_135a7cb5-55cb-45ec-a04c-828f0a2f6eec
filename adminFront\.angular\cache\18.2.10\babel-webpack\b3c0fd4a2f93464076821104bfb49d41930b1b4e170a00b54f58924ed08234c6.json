{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\nimport { FileUploadComponent } from '../../components/file-upload/file-upload.component';\nlet NoticeManagementComponent = class NoticeManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _utilityService, _buildCaseService, _specialNoticeFileService, _regularNoticeFileService, _houseService, _specialChangeCustomService, _fileService, _houseService2) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._utilityService = _utilityService;\n    this._buildCaseService = _buildCaseService;\n    this._specialNoticeFileService = _specialNoticeFileService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._houseService = _houseService;\n    this._specialChangeCustomService = _specialChangeCustomService;\n    this._fileService = _fileService;\n    this._houseService2 = _houseService2;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.pageFirstSales = 1;\n    this.pageSizeSales = 10;\n    this.pageIndexSales = 1;\n    this.totalRecordsSales = 0;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: 1\n    }, {\n      label: '銷售戶',\n      value: 2\n    }];\n    this.typeContentManagementLandowner = {\n      CFormType: 1,\n      CNoticeType: 1\n    };\n    // 新增：新戶別選擇器相關屬性\n    this.buildingData = {}; // 存放建築物戶別資料\n    this.selectedHouseholds = []; // 選中的戶別ID (使用 houseId)\n    this.isBuildingDataLoading = false; // 新增：建築物資料載入狀態\n    // 檔案上傳配置\n    this.fileUploadConfig = {\n      acceptedTypes: ['application/pdf'],\n      acceptedFileRegex: /pdf/i,\n      acceptAttribute: 'application/pdf',\n      label: '上傳檔案',\n      helpText: '*請上傳PDF格式',\n      required: false,\n      disabled: false,\n      autoFillName: false,\n      buttonText: '上傳',\n      buttonIcon: 'fa-solid fa-cloud-arrow-up',\n      maxFileSize: 10,\n      multiple: false,\n      showPreview: false\n    };\n    this.selectedFile = null;\n    this.cExamineStatusOption = ['待審核', '已通過', '已駁回'];\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.getUserBuildCase();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n  }\n  pageChangedSales(newPage) {\n    this.pageIndexSales = newPage;\n  }\n  // 檔案選擇事件處理\n  onFileUpload(fileResult) {\n    this.selectedFile = fileResult;\n  }\n  // 檔案清除事件處理\n  onFileClear() {\n    this.selectedFile = null;\n  }\n  onChangeBuildCase() {\n    if (this.selectedCBuildCase.value) {\n      this.getSpecialNoticeFileHouseHoldList();\n      this.getSpecialNoticeFileList();\n      // 新增：載入建築物戶別資料\n      this.loadBuildingDataFromAPI();\n    }\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            label: res.CBuildCaseName,\n            value: res.cID\n          };\n        });\n        this.selectedCBuildCase = this.userBuildCaseOptions[0];\n        if (this.selectedCBuildCase.value) {\n          this.getSpecialNoticeFileHouseHoldList();\n          this.getSpecialNoticeFileList();\n          // 新增：載入建築物戶別資料\n          this.loadBuildingDataFromAPI();\n        }\n      }\n    })).subscribe();\n  }\n  groupByFloor(customerData) {\n    const groupedData = [];\n    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n    for (const floor of uniqueFloors) {\n      groupedData.push([]);\n    }\n    for (const customer of customerData) {\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor);\n      if (floorIndex !== -1) {\n        groupedData[floorIndex].push({\n          CIsSelect: customer?.CIsSelect || false,\n          CHouseID: customer.CID,\n          CHouseType: customer.CHouseType,\n          CFloor: customer.CFloor,\n          CHouseHold: customer.CHouseHold,\n          CIsEnable: customer.CIsEnable\n        });\n      }\n    }\n    return groupedData;\n  }\n  getSpecialNoticeFileList() {\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedCBuildCase.value,\n        PageIndexLandLord: this.pageIndex,\n        PageIndexSales: this.pageIndexSales,\n        PageSizeLandLord: this.pageSize,\n        PageSizeSales: this.pageSizeSales\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialNoticeFile = res.Entries;\n        this.totalRecords = res.Entries.TotalListLandLords || 0;\n        this.totalRecordsSales = res.Entries.TotalListSales || 0;\n      }\n    });\n  }\n  onStatusChange(newStatus) {\n    // 觸發建築物資料重新篩選\n    this.onNoticeTypeChange();\n  }\n  getSpecialNoticeFileHouseHoldList() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.selectedCBuildCase.value\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialNoticeFileHouseHold = res.Entries;\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  updateCIsEnable(houseList2D, houseSpecialNoticeFile) {\n    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));\n    return houseList2D.map(floorArray => {\n      return floorArray.map(item => {\n        const key = `${item.CHouseHold}-${item.CFloor}`;\n        if (selectedHouses.has(key)) {\n          item.CIsSelect = true;\n        } else {\n          item.CIsSelect = false;\n        }\n        return item;\n      });\n    });\n  }\n  addCIsSelectToA(A, B) {\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\n    return A.map(item => {\n      const key = `${item.CHouseHold}-${item.CFloor}`;\n      return {\n        ...item,\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\n      };\n    });\n  }\n  getSpecialNoticeFileById(item, ref) {\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({\n      body: item.CSpecialNoticeFileId\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        const data = res.Entries;\n        this.saveSpecialNoticeFile = {\n          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,\n          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,\n          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,\n          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,\n          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],\n          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,\n          CExamineNote: data.CExamineNote,\n          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter(i => i.CIsSelect),\n          CExamineStauts: data.CExamineStauts\n        };\n        // 新增：初始化選中的戶別ID\n        this.selectedHouseholds = this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses?.map(house => house.CHouseID) || [];\n        // 新增：根據載入的通知類型重新篩選建築物資料\n        this.loadBuildingDataFromAPI();\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  openPdfInNewTab(file) {\n    // 檢查檔案資訊是否存在\n    if (!file.CFile || !file.CFileName) {\n      this.message.showErrorMSG('檔案資訊不完整');\n      return;\n    }\n    // 使用 FileService 下載檔案\n    this._fileService.getFile(file.CFile, file.CFileName).subscribe({\n      next: blob => {\n        // 建立 Blob URL\n        const blobUrl = URL.createObjectURL(blob);\n        // 在新頁籤開啟 PDF\n        const newWindow = window.open(blobUrl, '_blank');\n        if (!newWindow) {\n          this.message.showErrorMSG('無法開啟新視窗，請檢查瀏覽器設定');\n          URL.revokeObjectURL(blobUrl); // 清理 URL\n          return;\n        }\n        // 當新視窗關閉時清理 Blob URL\n        newWindow.addEventListener('beforeunload', () => {\n          URL.revokeObjectURL(blobUrl);\n        });\n      },\n      error: error => {\n        console.error('下載檔案失敗:', error);\n        this.message.showErrorMSG('檔案下載失敗，請稍後再試');\n      }\n    });\n  }\n  openModel(ref, item) {\n    this.isNew = true;\n    this.onFileClear(); // 替換 clearImage()\n    this.selectedHouseholds = []; // 新增：清空選中的戶別\n    this.saveSpecialNoticeFile = {\n      CNoticeType: 1,\n      CBuildCaseId: undefined,\n      CFile: undefined,\n      CHouse: [],\n      CSpecialNoticeFileId: undefined,\n      CIsSelectAll: false,\n      selectedCNoticeType: this.cNoticeTypeOptions[0],\n      CExamineNote: '',\n      tblSpecialNoticeFileHouses: undefined\n    };\n    if (item) {\n      this.isNew = false;\n      this.getSpecialNoticeFileById(item, ref);\n    } else {\n      this.isNew = true;\n      this.dialogService.open(ref);\n      // 新增：在對話框開啟後載入建築物資料（確保預設通知類型已設置）\n      setTimeout(() => {\n        this.loadBuildingDataFromAPI();\n      }, 0);\n    }\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  onDelete(item) {\n    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {\n      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({\n        body: item.CSpecialNoticeFileId\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.onChangeBuildCase();\n        }\n      });\n    }\n    return item;\n  }\n  getTrueKeys(inputDict) {\n    const trueKeys = [];\n    for (const key in inputDict) {\n      if (inputDict[key]) {\n        trueKeys.push(key);\n      }\n    }\n    return trueKeys;\n  }\n  flattenAndFilter(data) {\n    const flattened = [];\n    for (const floorData of data) {\n      for (const house of floorData) {\n        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {\n          flattened.push({\n            CHouseID: house.CHouseID,\n            CIsSelect: house.CIsSelect\n          });\n        }\n      }\n    }\n    return flattened;\n  }\n  // 新增：從新戶別選擇器的選擇結果轉換為原有格式\n  convertSelectedHouseholdsToHouseSpecialNoticeFile() {\n    const result = [];\n    // 使用 buildingData 和 selectedHouseholds 來產生結果\n    if (this.selectedHouseholds && this.selectedHouseholds.length > 0 && this.buildingData) {\n      // 從 buildingData 中找到對應的戶別資訊\n      Object.values(this.buildingData).forEach(buildings => {\n        buildings.forEach(house => {\n          if (house.houseId && this.selectedHouseholds.includes(house.houseId) && !house.isDisabled) {\n            result.push({\n              CHouseID: house.houseId,\n              CIsSelect: true\n            });\n          }\n        });\n      });\n    }\n    return result;\n  }\n  onSaveSpecialNoticeFile(ref) {\n    const selectedHouses = this.convertSelectedHouseholdsToHouseSpecialNoticeFile();\n    // 除錯資訊\n    console.log('選中的戶別ID:', this.selectedHouseholds);\n    console.log('轉換後的戶別資料:', selectedHouses);\n    console.log('建築物資料 keys:', Object.keys(this.buildingData));\n    console.log('建築物資料總戶數:', Object.values(this.buildingData).reduce((total, houses) => total + houses.length, 0));\n    console.log('通知類型:', this.saveSpecialNoticeFile.selectedCNoticeType);\n    const param = {\n      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,\n      CBuildCaseId: this.selectedCBuildCase.value,\n      CFile: this.selectedFile ? this.selectedFile.CFileUpload : undefined,\n      CHouse: selectedHouses,\n      // 使用新的轉換方法\n      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,\n      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,\n      CExamineNote: this.saveSpecialNoticeFile.CExamineNote\n    };\n    this.validation(param.CHouse);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {\n      if (res && res.body && res.body.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.onFileClear(); // 替換 clearImage()\n        this.getSpecialNoticeFileList();\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res && res.body && res.body.Message);\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation(CHouse) {\n    this.valid.clear();\n    if (this.isNew && !this.selectedFile) {\n      this.valid.required('[檔案]', '');\n    }\n    if (!(CHouse.length > 0)) {\n      this.valid.required('[適用戶別]', '');\n    }\n    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote);\n  }\n  getActionName(actionID) {\n    let textR = \"\";\n    if (actionID != undefined) {\n      switch (actionID) {\n        case 1:\n          textR = \"傳送\";\n          break;\n        case 2:\n          textR = \"通過\";\n          break;\n        case 3:\n          textR = \"駁回\";\n          break;\n        default:\n          break;\n      }\n    }\n    return textR;\n  }\n  updateCIsClick(houseList2D, houseSpecialNoticeFile) {\n    const selectedHouses = houseSpecialNoticeFile.map(item => item.CHouseID);\n    return houseList2D.map(floorArray => {\n      return floorArray.map(item => {\n        if (selectedHouses.includes(item.CHouseID)) {\n          item.CIsSelect = true;\n        } else {\n          item.CIsSelect = false;\n        }\n        return item;\n      });\n    });\n  } // 新增：載入建築物戶別資料 (使用 GetDropDown API)\n  loadBuildingDataFromAPI() {\n    if (!this.selectedCBuildCase?.value) return;\n    this.isBuildingDataLoading = true;\n    this.buildingData = {}; // 清空舊資料\n    this._houseService2.apiHouseGetDropDownPost$Json({\n      buildCaseId: this.selectedCBuildCase.value\n    }).subscribe({\n      next: response => {\n        this.isBuildingDataLoading = false;\n        if (response.Entries) {\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        }\n      },\n      error: error => {\n        this.isBuildingDataLoading = false;\n        console.error('Error loading building data from API:', error);\n        // 清空建築物資料，避免使用過時的資料\n        this.buildingData = {};\n      }\n    });\n  } // 新增：將 API 回應轉換為建築物資料格式\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      // 不在父元件中預先篩選，將所有戶別資料傳給子元件\n      // 篩選邏輯由戶別綁定元件根據 preFilterHouseType 參數處理\n      buildingData[building] = houses.map(house => ({\n        houseName: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseType: house.HouseType,\n        // 保留戶別類型資訊供子元件篩選使用\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  // 新增：將現有的 houseList2D 轉換為新戶別選擇器的格式\n  convertHouseList2DToBuildingData() {\n    const buildingData = {};\n    if (!this.houseList2D || this.houseList2D.length === 0) {\n      return buildingData;\n    }\n    this.houseList2D.forEach(row => {\n      row.forEach(house => {\n        if (!house.CHouseHold) return;\n        // 不在父元件中預先篩選，將所有戶別資料傳給子元件\n        // 篩選邏輯由戶別綁定元件根據 preFilterHouseType 參數處理\n        // 嘗試從戶別名稱中提取建築物代碼（假設格式為 A001, B002 等）\n        const buildingMatch = house.CHouseHold.match(/^([A-Z]+)/);\n        const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\n        if (!buildingData[building]) {\n          buildingData[building] = [];\n        }\n        buildingData[building].push({\n          houseName: house.CHouseHold,\n          building: building,\n          floor: house.CFloor ? `${house.CFloor}F` : undefined,\n          houseId: house.CHouseID,\n          houseType: house.CHouseType || undefined,\n          // 新增：戶別類型\n          isSelected: house.CIsSelect || false,\n          isDisabled: !house.CIsEnable || !(this.saveSpecialNoticeFile?.selectedCNoticeType?.value === house.CHouseType) || this.saveSpecialNoticeFile?.CExamineStauts === 0\n        });\n      });\n    });\n    return buildingData;\n  } // 新增：處理戶別選擇變更\n  onHouseholdSelectionChange(selectedItems) {\n    // 更新 selectedHouseholds (使用 houseId)\n    this.selectedHouseholds = selectedItems.map(item => item.houseId).filter(id => id !== undefined);\n  }\n  // 新增：處理戶別ID變更事件\n  onHouseholdIdChange(selectedIds) {\n    this.selectedHouseholds = selectedIds;\n  }\n  // 新增：處理通知類型變更\n  onNoticeTypeChange() {\n    // 清空當前選擇的戶別\n    this.selectedHouseholds = [];\n    // 不需要重新載入資料，因為篩選邏輯已移至戶別綁定元件內部\n    // 戶別綁定元件會根據 preFilterHouseType 參數自動篩選顯示的戶別\n  }\n  // 新增：取得戶別選擇的提醒文案\n  getHouseholdReminderText() {\n    if (!this.saveSpecialNoticeFile?.selectedCNoticeType) {\n      return '請先選擇檔案類型，系統將根據類型篩選對應的戶別';\n    }\n    const noticeType = this.saveSpecialNoticeFile.selectedCNoticeType;\n    if (noticeType.value === 1) {\n      return '目前篩選顯示：地主戶，只會顯示地主戶相關的戶別選項';\n    } else if (noticeType.value === 2) {\n      return '目前篩選顯示：銷售戶，只會顯示銷售戶相關的戶別選項';\n    }\n    return '';\n  }\n};\nNoticeManagementComponent = __decorate([Component({\n  selector: 'ngx-notice-management',\n  templateUrl: './notice-management.component.html',\n  styleUrls: ['./notice-management.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, AppSharedModule, DatePipe, DateFormatHourPipe, FileUploadComponent]\n})], NoticeManagementComponent);\nexport { NoticeManagementComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "DatePipe", "tap", "SharedModule", "AppSharedModule", "BaseComponent", "DateFormatHourPipe", "FileUploadComponent", "NoticeManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_utilityService", "_buildCaseService", "_specialNoticeFileService", "_regularNoticeFileService", "_houseService", "_specialChangeCustomService", "_fileService", "_houseService2", "pageFirst", "pageSize", "pageIndex", "totalRecords", "pageFirstSales", "pageSizeSales", "pageIndexSales", "totalRecordsSales", "buildCaseOptions", "label", "value", "cNoticeTypeOptions", "typeContentManagementLandowner", "CFormType", "CNoticeType", "buildingData", "selectedHouseholds", "isBuildingDataLoading", "fileUploadConfig", "acceptedTypes", "acceptedFileRegex", "acceptAttribute", "helpText", "required", "disabled", "autoFillName", "buttonText", "buttonIcon", "maxFileSize", "multiple", "showPreview", "selectedFile", "cExamineStatusOption", "isNew", "ngOnInit", "getUserBuildCase", "pageChanged", "newPage", "pageChangedSales", "onFileUpload", "fileResult", "onFileClear", "onChangeBuildCase", "selectedCBuildCase", "getSpecialNoticeFileHouseHoldList", "getSpecialNoticeFileList", "loadBuildingDataFromAPI", "apiBuildCaseGetUserBuildCasePost$Json", "body", "pipe", "res", "Entries", "StatusCode", "userBuildCaseOptions", "map", "CBuildCaseName", "cID", "subscribe", "groupByFloor", "customerData", "groupedData", "uniqueFloors", "Array", "from", "Set", "customer", "CFloor", "filter", "floor", "push", "floorIndex", "indexOf", "CIsSelect", "CHouseID", "CID", "CHouseType", "CHouseHold", "CIsEnable", "apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json", "CBuildCaseId", "PageIndexLandLord", "PageIndexSales", "PageSizeLandLord", "PageSizeSales", "listSpecialNoticeFile", "TotalListLandLords", "TotalListSales", "onStatusChange", "newStatus", "onNoticeTypeChange", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "listSpecialNoticeFileHouseHold", "createArrayObjectFromArray", "a", "b", "c", "item", "matchingItem", "find", "bItem", "CHousehold", "CIsSelected", "getItemByValue", "options", "updateCIsEnable", "houseList2D", "houseSpecialNoticeFile", "selectedHouses", "floorArray", "key", "has", "addCIsSelectToA", "A", "B", "mapB", "Map", "get", "getSpecialNoticeFileById", "ref", "apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json", "CSpecialNoticeFileId", "data", "saveSpecialNoticeFile", "CFileUrl", "tblSpecialNoticeFile", "undefined", "selectedCNoticeType", "tblExamineLogs", "CExamineNote", "tblSpecialNoticeFileHouses", "i", "CExamineStauts", "house", "open", "openPdfInNewTab", "file", "CFile", "CFileName", "showErrorMSG", "getFile", "next", "blob", "blobUrl", "URL", "createObjectURL", "newWindow", "window", "revokeObjectURL", "addEventListener", "error", "console", "openModel", "CHouse", "CIsSelectAll", "setTimeout", "removeBase64Prefix", "base64String", "prefixIndex", "substring", "onDelete", "confirm", "apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json", "showSucessMSG", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputDict", "true<PERSON>eys", "flattenAndFilter", "flattened", "floorData", "convertSelectedHouseholdsToHouseSpecialNoticeFile", "result", "length", "Object", "values", "for<PERSON>ach", "buildings", "houseId", "includes", "isDisabled", "onSaveSpecialNoticeFile", "log", "keys", "reduce", "total", "houses", "param", "CFileUpload", "validation", "errorMessages", "showErrorMSGs", "SaveSpecialNoticeFile", "close", "Message", "onClose", "clear", "getActionName", "actionID", "textR", "updateCIsClick", "apiHouseGetDropDownPost$Json", "buildCaseId", "response", "convertApiResponseToBuildingData", "entries", "building", "houseName", "HouseName", "Building", "Floor", "HouseId", "houseType", "HouseType", "isSelected", "convertHouseList2DToBuildingData", "row", "buildingMatch", "match", "onHouseholdSelectionChange", "selectedItems", "id", "onHouseholdIdChange", "selectedIds", "getHouseholdReminderText", "noticeType", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\notice-management.component.ts"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { CommonModule, DatePipe } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, FileService, HouseService, RegularNoticeFileService, SpecialNoticeFileService } from 'src/services/api/services';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetListHouseHoldRes, GetSpecialNoticeFileListRes, HouseSpecialNoticeFile, SpecialNoticeFileList, TblExamineLog } from 'src/services/api/models';\r\nimport { tap } from 'rxjs';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { NoticeServiceCustom } from 'src/app/@core/service/notice.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\r\nimport { FileUploadComponent, FileUploadConfig, FileUploadResult } from '../../components/file-upload/file-upload.component';\r\nimport { HouseholdItem, BuildingData } from 'src/app/shared/components/household-binding/household-binding.component';\r\n\r\nexport interface HouseList {\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseID?: number;\r\n  CID?: number;\r\n  CIsSelect?: boolean | null;\r\n  CHouseType?: number | null;\r\n  CIsEnable?: boolean | null;\r\n}\r\nexport interface SaveSpecialNoticeFileCus {\r\n  CFileUrl?: string\r\n  CNoticeType?: number\r\n  CBuildCaseId?: number\r\n  CFile?: Blob\r\n  CHouse?: Array<string>\r\n  CSpecialNoticeFileId?: number\r\n  CIsSelectAll?: boolean\r\n  selectedCNoticeType?: any\r\n  CExamineNote?: string | null;\r\n  tblExamineLogs?: TblExamineLog[],\r\n  tblSpecialNoticeFileHouses: any\r\n  CExamineStauts?: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-notice-management',\r\n  templateUrl: './notice-management.component.html',\r\n  styleUrls: ['./notice-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, AppSharedModule, DatePipe, DateFormatHourPipe, FileUploadComponent],\r\n})\r\n\r\nexport class NoticeManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _utilityService: UtilityService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _specialNoticeFileService: SpecialNoticeFileService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _houseService: HouseService,\r\n    private _specialChangeCustomService: NoticeServiceCustom,\r\n    private _fileService: FileService,\r\n    private _houseService2: HouseService\r\n  ) { super(_allow) }\r\n\r\n  override ngOnInit(): void {\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  selectedCBuildCase: any\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n  pageFirstSales = 1;\r\n  pageSizeSales = 10;\r\n  pageIndexSales = 1;\r\n  totalRecordsSales = 0;\r\n\r\n\r\n  saveSpecialNoticeFile: SaveSpecialNoticeFileCus\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  cNoticeTypeOptions: any[] = [\r\n    { label: '地主戶', value: 1 },\r\n    { label: '銷售戶', value: 2 }\r\n  ]\r\n  seletectedNoticeType: any\r\n\r\n  typeContentManagementLandowner = {\r\n    CFormType: 1,\r\n    CNoticeType: 1\r\n  }\r\n  houseList2D: HouseList[][]\r\n  userBuildCaseOptions: any\r\n  // 新增：新戶別選擇器相關屬性\r\n  buildingData: BuildingData = {} // 存放建築物戶別資料\r\n  selectedHouseholds: number[] = [] // 選中的戶別ID (使用 houseId)\r\n  isBuildingDataLoading: boolean = false // 新增：建築物資料載入狀態\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n  }\r\n  pageChangedSales(newPage: number) {\r\n    this.pageIndexSales = newPage;\r\n  }\r\n\r\n  // 檔案上傳配置\r\n  fileUploadConfig: FileUploadConfig = {\r\n    acceptedTypes: ['application/pdf'],\r\n    acceptedFileRegex: /pdf/i,\r\n    acceptAttribute: 'application/pdf',\r\n    label: '上傳檔案',\r\n    helpText: '*請上傳PDF格式',\r\n    required: false,\r\n    disabled: false,\r\n    autoFillName: false,\r\n    buttonText: '上傳',\r\n    buttonIcon: 'fa-solid fa-cloud-arrow-up',\r\n    maxFileSize: 10,\r\n    multiple: false,\r\n    showPreview: false\r\n  };\r\n\r\n  selectedFile: FileUploadResult | null = null;\r\n\r\n  // 檔案選擇事件處理\r\n  onFileUpload(fileResult: FileUploadResult) {\r\n    this.selectedFile = fileResult;\r\n  }\r\n  // 檔案清除事件處理\r\n  onFileClear() {\r\n    this.selectedFile = null;\r\n  }\r\n\r\n  userBuildCaseSelected: any\r\n  onChangeBuildCase() {\r\n    if (this.selectedCBuildCase.value) {\r\n      this.getSpecialNoticeFileHouseHoldList()\r\n      this.getSpecialNoticeFileList()\r\n      // 新增：載入建築物戶別資料\r\n      this.loadBuildingDataFromAPI()\r\n    }\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              label: res.CBuildCaseName,\r\n              value: res.cID\r\n            }\r\n          })\r\n          this.selectedCBuildCase = this.userBuildCaseOptions[0]\r\n          if (this.selectedCBuildCase.value) {\r\n            this.getSpecialNoticeFileHouseHoldList()\r\n            this.getSpecialNoticeFileList()\r\n            // 新增：載入建築物戶別資料\r\n            this.loadBuildingDataFromAPI()\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe()\r\n  }\r\n\r\n  groupByFloor(customerData: HouseList[]): HouseList[][] {\r\n\r\n    const groupedData: HouseList[][] = [];\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number);\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push({\r\n          CIsSelect: customer?.CIsSelect || false,\r\n          CHouseID: customer.CID,\r\n          CHouseType: customer.CHouseType,\r\n          CFloor: customer.CFloor,\r\n          CHouseHold: customer.CHouseHold,\r\n          CIsEnable: customer.CIsEnable,\r\n        });\r\n      }\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n  listSpecialNoticeFile: GetSpecialNoticeFileListRes\r\n\r\n  getSpecialNoticeFileList() {\r\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedCBuildCase.value,\r\n        PageIndexLandLord: this.pageIndex,\r\n        PageIndexSales: this.pageIndexSales,\r\n        PageSizeLandLord: this.pageSize,\r\n        PageSizeSales: this.pageSizeSales,\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialNoticeFile = res.Entries\r\n        this.totalRecords = res.Entries.TotalListLandLords || 0\r\n        this.totalRecordsSales = res.Entries.TotalListSales || 0\r\n      }\r\n    })\r\n  }\r\n  listSpecialNoticeFileHouseHold: GetListHouseHoldRes[]\r\n  houseHoldList: string[];\r\n\r\n  onStatusChange(newStatus: any) {\r\n    // 觸發建築物資料重新篩選\r\n    this.onNoticeTypeChange();\r\n  }\r\n\r\n  getSpecialNoticeFileHouseHoldList() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.selectedCBuildCase.value\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialNoticeFileHouseHold = res.Entries\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CSpecialNoticeFileHouseholdId: number, CSpecialNoticeFileId: number, CHousehold: string, CIsSelected: boolean }[] | any[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  cExamineStatusOption = ['待審核', '已通過', '已駁回']\r\n\r\n  updateCIsEnable(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {\r\n    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));\r\n    return houseList2D.map(floorArray => {\r\n      return floorArray.map(item => {\r\n        const key = `${item.CHouseHold}-${item.CFloor}`;\r\n        if (selectedHouses.has(key)) {\r\n          item.CIsSelect = true;\r\n        } else {\r\n          item.CIsSelect = false;\r\n        }\r\n        return item;\r\n      });\r\n    });\r\n  }\r\n\r\n  addCIsSelectToA(A: any[], B: any[]): any[] {\r\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\r\n    return A.map(item => {\r\n      const key = `${item.CHouseHold}-${item.CFloor}`;\r\n      return {\r\n        ...item,\r\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\r\n      };\r\n    });\r\n  }\r\n\r\n  getSpecialNoticeFileById(item: any, ref: any) {\r\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({\r\n      body: item.CSpecialNoticeFileId\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        const data = res.Entries\r\n        this.saveSpecialNoticeFile = {\r\n          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,\r\n          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,\r\n          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,\r\n          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,\r\n          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],\r\n          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,\r\n          CExamineNote: data.CExamineNote,\r\n          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter((i: any) => i.CIsSelect),\r\n          CExamineStauts: data.CExamineStauts\r\n        }\r\n        // 新增：初始化選中的戶別ID\r\n        this.selectedHouseholds = this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses?.map((house: any) => house.CHouseID) || []\r\n\r\n        // 新增：根據載入的通知類型重新篩選建築物資料\r\n        this.loadBuildingDataFromAPI()\r\n\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  isNew = true;\r\n\r\n  openPdfInNewTab(file: SpecialNoticeFileList) {\r\n    // 檢查檔案資訊是否存在\r\n    if (!file.CFile || !file.CFileName) {\r\n      this.message.showErrorMSG('檔案資訊不完整');\r\n      return;\r\n    }\r\n\r\n    // 使用 FileService 下載檔案\r\n    this._fileService.getFile(file.CFile, file.CFileName).subscribe({\r\n      next: (blob: Blob) => {\r\n        // 建立 Blob URL\r\n        const blobUrl = URL.createObjectURL(blob);\r\n\r\n        // 在新頁籤開啟 PDF\r\n        const newWindow = window.open(blobUrl, '_blank');\r\n\r\n        if (!newWindow) {\r\n          this.message.showErrorMSG('無法開啟新視窗，請檢查瀏覽器設定');\r\n          URL.revokeObjectURL(blobUrl); // 清理 URL\r\n          return;\r\n        }\r\n\r\n        // 當新視窗關閉時清理 Blob URL\r\n        newWindow.addEventListener('beforeunload', () => {\r\n          URL.revokeObjectURL(blobUrl);\r\n        });\r\n      },\r\n      error: (error) => {\r\n        console.error('下載檔案失敗:', error);\r\n        this.message.showErrorMSG('檔案下載失敗，請稍後再試');\r\n      }\r\n    });\r\n  } openModel(ref: any, item?: any) {\r\n    this.isNew = true\r\n    this.onFileClear() // 替換 clearImage()\r\n    this.selectedHouseholds = [] // 新增：清空選中的戶別\r\n    this.saveSpecialNoticeFile = {\r\n      CNoticeType: 1,\r\n      CBuildCaseId: undefined,\r\n      CFile: undefined,\r\n      CHouse: [],\r\n      CSpecialNoticeFileId: undefined,\r\n      CIsSelectAll: false,\r\n      selectedCNoticeType: this.cNoticeTypeOptions[0],\r\n      CExamineNote: '',\r\n      tblSpecialNoticeFileHouses: undefined\r\n    }\r\n\r\n    if (item) {\r\n      this.isNew = false\r\n      this.getSpecialNoticeFileById(item, ref)\r\n    } else {\r\n      this.isNew = true\r\n      this.dialogService.open(ref)\r\n      // 新增：在對話框開啟後載入建築物資料（確保預設通知類型已設置）\r\n      setTimeout(() => {\r\n        this.loadBuildingDataFromAPI()\r\n      }, 0);\r\n    }\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  onDelete(item: any) {\r\n    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {\r\n      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({\r\n        body: item.CSpecialNoticeFileId\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.onChangeBuildCase()\r\n        }\r\n      })\r\n    }\r\n    return item\r\n  }\r\n\r\n  getTrueKeys(inputDict: { [key: string]: boolean }): string[] {\r\n    const trueKeys: string[] = [];\r\n    for (const key in inputDict) {\r\n      if (inputDict[key]) {\r\n        trueKeys.push(key);\r\n      }\r\n    }\r\n    return trueKeys;\r\n  }\r\n  flattenAndFilter(data: any[][]): HouseSpecialNoticeFile[] {\r\n    const flattened: HouseSpecialNoticeFile[] = [];\r\n    for (const floorData of data) {\r\n      for (const house of floorData) {\r\n        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {\r\n          flattened.push({\r\n            CHouseID: house.CHouseID,\r\n            CIsSelect: house.CIsSelect,\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return flattened;\r\n  }\r\n\r\n  // 新增：從新戶別選擇器的選擇結果轉換為原有格式\r\n  convertSelectedHouseholdsToHouseSpecialNoticeFile(): HouseSpecialNoticeFile[] {\r\n    const result: HouseSpecialNoticeFile[] = [];\r\n\r\n    // 使用 buildingData 和 selectedHouseholds 來產生結果\r\n    if (this.selectedHouseholds && this.selectedHouseholds.length > 0 && this.buildingData) {\r\n      // 從 buildingData 中找到對應的戶別資訊\r\n      Object.values(this.buildingData).forEach(buildings => {\r\n        buildings.forEach(house => {\r\n          if (house.houseId &&\r\n            this.selectedHouseholds.includes(house.houseId) &&\r\n            !house.isDisabled) {\r\n            result.push({\r\n              CHouseID: house.houseId,\r\n              CIsSelect: true,\r\n            });\r\n          }\r\n        });\r\n      });\r\n    }\r\n\r\n    return result;\r\n  } onSaveSpecialNoticeFile(ref: any) {\r\n    const selectedHouses = this.convertSelectedHouseholdsToHouseSpecialNoticeFile();\r\n\r\n    // 除錯資訊\r\n    console.log('選中的戶別ID:', this.selectedHouseholds);\r\n    console.log('轉換後的戶別資料:', selectedHouses);\r\n    console.log('建築物資料 keys:', Object.keys(this.buildingData));\r\n    console.log('建築物資料總戶數:', Object.values(this.buildingData).reduce((total, houses) => total + houses.length, 0));\r\n    console.log('通知類型:', this.saveSpecialNoticeFile.selectedCNoticeType);\r\n\r\n    const param = {\r\n      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,\r\n      CBuildCaseId: this.selectedCBuildCase.value,\r\n      CFile: this.selectedFile ? this.selectedFile.CFileUpload : undefined,\r\n      CHouse: selectedHouses, // 使用新的轉換方法\r\n      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,\r\n      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,\r\n      CExamineNote: this.saveSpecialNoticeFile.CExamineNote\r\n    }\r\n\r\n    this.validation(param.CHouse)\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    } this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {\r\n      if (res && res.body! && res.body.StatusCode! === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.onFileClear() // 替換 clearImage()\r\n        this.getSpecialNoticeFileList()\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res && res.body && res.body.Message!);\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation(CHouse: any[]) {\r\n    this.valid.clear();\r\n    if (this.isNew && !this.selectedFile) {\r\n      this.valid.required('[檔案]', '')\r\n    }\r\n    if (!(CHouse.length > 0)) {\r\n      this.valid.required('[適用戶別]', '')\r\n    }\r\n    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote)\r\n  }\r\n\r\n  getActionName(actionID: number | undefined) {\r\n    let textR = \"\";\r\n    if (actionID != undefined) {\r\n      switch (actionID) {\r\n        case 1:\r\n          textR = \"傳送\";\r\n          break;\r\n        case 2:\r\n          textR = \"通過\";\r\n          break;\r\n        case 3:\r\n          textR = \"駁回\";\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n    return textR;\r\n  }\r\n\r\n\r\n  updateCIsClick(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {\r\n    const selectedHouses = houseSpecialNoticeFile.map(item => item.CHouseID);\r\n    return houseList2D.map(floorArray => {\r\n      return floorArray.map(item => {\r\n        if (selectedHouses.includes(item.CHouseID)) {\r\n          item.CIsSelect = true;\r\n        } else {\r\n          item.CIsSelect = false;\r\n        }\r\n        return item;\r\n      });\r\n    });\r\n  }  // 新增：載入建築物戶別資料 (使用 GetDropDown API)\r\n  private loadBuildingDataFromAPI(): void {\r\n    if (!this.selectedCBuildCase?.value) return;\r\n\r\n    this.isBuildingDataLoading = true;\r\n    this.buildingData = {}; // 清空舊資料\r\n\r\n    this._houseService2.apiHouseGetDropDownPost$Json({\r\n      buildCaseId: this.selectedCBuildCase.value\r\n    }).subscribe({\r\n      next: (response) => {\r\n        this.isBuildingDataLoading = false;\r\n        if (response.Entries) {\r\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.isBuildingDataLoading = false;\r\n        console.error('Error loading building data from API:', error);\r\n        // 清空建築物資料，避免使用過時的資料\r\n        this.buildingData = {};\r\n      }\r\n    });\r\n  }  // 新增：將 API 回應轉換為建築物資料格式\r\n  private convertApiResponseToBuildingData(entries: any): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]: [string, any]) => {\r\n      // 不在父元件中預先篩選，將所有戶別資料傳給子元件\r\n      // 篩選邏輯由戶別綁定元件根據 preFilterHouseType 參數處理\r\n      buildingData[building] = houses.map((house: any) => ({\r\n        houseName: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseType: house.HouseType, // 保留戶別類型資訊供子元件篩選使用\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n  // 新增：將現有的 houseList2D 轉換為新戶別選擇器的格式\r\n  convertHouseList2DToBuildingData(): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    if (!this.houseList2D || this.houseList2D.length === 0) {\r\n      return buildingData;\r\n    }\r\n\r\n    this.houseList2D.forEach(row => {\r\n      row.forEach(house => {\r\n        if (!house.CHouseHold) return;\r\n\r\n        // 不在父元件中預先篩選，將所有戶別資料傳給子元件\r\n        // 篩選邏輯由戶別綁定元件根據 preFilterHouseType 參數處理\r\n\r\n        // 嘗試從戶別名稱中提取建築物代碼（假設格式為 A001, B002 等）\r\n        const buildingMatch = house.CHouseHold.match(/^([A-Z]+)/);\r\n        const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\r\n\r\n        if (!buildingData[building]) {\r\n          buildingData[building] = [];\r\n        } buildingData[building].push({\r\n          houseName: house.CHouseHold,\r\n          building: building,\r\n          floor: house.CFloor ? `${house.CFloor}F` : undefined,\r\n          houseId: house.CHouseID,\r\n          houseType: house.CHouseType || undefined, // 新增：戶別類型\r\n          isSelected: house.CIsSelect || false,\r\n          isDisabled: !house.CIsEnable ||\r\n            !(this.saveSpecialNoticeFile?.selectedCNoticeType?.value === house.CHouseType) ||\r\n            this.saveSpecialNoticeFile?.CExamineStauts === 0\r\n        });\r\n      });\r\n    });\r\n\r\n    return buildingData;\r\n  }  // 新增：處理戶別選擇變更\r\n  onHouseholdSelectionChange(selectedItems: HouseholdItem[]) {\r\n    // 更新 selectedHouseholds (使用 houseId)\r\n    this.selectedHouseholds = selectedItems.map(item => item.houseId).filter(id => id !== undefined) as number[];\r\n  }\r\n  // 新增：處理戶別ID變更事件\r\n  onHouseholdIdChange(selectedIds: number[]) {\r\n    this.selectedHouseholds = selectedIds;\r\n  }\r\n  // 新增：處理通知類型變更\r\n  onNoticeTypeChange() {\r\n    // 清空當前選擇的戶別\r\n    this.selectedHouseholds = [];\r\n\r\n    // 不需要重新載入資料，因為篩選邏輯已移至戶別綁定元件內部\r\n    // 戶別綁定元件會根據 preFilterHouseType 參數自動篩選顯示的戶別\r\n  }\r\n\r\n  // 新增：取得戶別選擇的提醒文案\r\n  getHouseholdReminderText(): string {\r\n    if (!this.saveSpecialNoticeFile?.selectedCNoticeType) {\r\n      return '請先選擇檔案類型，系統將根據類型篩選對應的戶別';\r\n    }\r\n\r\n    const noticeType = this.saveSpecialNoticeFile.selectedCNoticeType;\r\n    if (noticeType.value === 1) {\r\n      return '目前篩選顯示：地主戶，只會顯示地主戶相關的戶別選項';\r\n    } else if (noticeType.value === 2) {\r\n      return '目前篩選顯示：銷售戶，只會顯示銷售戶相關的戶別選項';\r\n    }\r\n\r\n    return '';\r\n  }\r\n}\r\n\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAuC,eAAe;AACxE,SAASC,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAOxD,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASA,YAAY,IAAIC,eAAe,QAAQ,8BAA8B;AAC9E,SAASC,aAAa,QAAQ,qCAAqC;AAKnE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,mBAAmB,QAA4C,oDAAoD;AAmCrH,IAAMC,yBAAyB,GAA/B,MAAMA,yBAA0B,SAAQH,aAAa;EAC1DI,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,eAA+B,EAC/BC,iBAAmC,EACnCC,yBAAmD,EACnDC,yBAAmD,EACnDC,aAA2B,EAC3BC,2BAAgD,EAChDC,YAAyB,EACzBC,cAA4B;IAClC,KAAK,CAACX,MAAM,CAAC;IAZP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,2BAA2B,GAA3BA,2BAA2B;IAC3B,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IASf,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IACzB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IAKrB,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAEtD,KAAAC,kBAAkB,GAAU,CAC1B;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAC,CAAE,EAC1B;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAC,CAAE,CAC3B;IAGD,KAAAE,8BAA8B,GAAG;MAC/BC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAGD;IACA,KAAAC,YAAY,GAAiB,EAAE,EAAC;IAChC,KAAAC,kBAAkB,GAAa,EAAE,EAAC;IAClC,KAAAC,qBAAqB,GAAY,KAAK,EAAC;IASvC;IACA,KAAAC,gBAAgB,GAAqB;MACnCC,aAAa,EAAE,CAAC,iBAAiB,CAAC;MAClCC,iBAAiB,EAAE,MAAM;MACzBC,eAAe,EAAE,iBAAiB;MAClCZ,KAAK,EAAE,MAAM;MACba,QAAQ,EAAE,WAAW;MACrBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,4BAA4B;MACxCC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE;KACd;IAED,KAAAC,YAAY,GAA4B,IAAI;IA4H5C,KAAAC,oBAAoB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAwD5C,KAAAC,KAAK,GAAG,IAAI;EAnPM;EAETC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAmCAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACnC,SAAS,GAAGmC,OAAO;EAC1B;EACAC,gBAAgBA,CAACD,OAAe;IAC9B,IAAI,CAAC/B,cAAc,GAAG+B,OAAO;EAC/B;EAqBA;EACAE,YAAYA,CAACC,UAA4B;IACvC,IAAI,CAACT,YAAY,GAAGS,UAAU;EAChC;EACA;EACAC,WAAWA,CAAA;IACT,IAAI,CAACV,YAAY,GAAG,IAAI;EAC1B;EAGAW,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACC,kBAAkB,CAACjC,KAAK,EAAE;MACjC,IAAI,CAACkC,iCAAiC,EAAE;MACxC,IAAI,CAACC,wBAAwB,EAAE;MAC/B;MACA,IAAI,CAACC,uBAAuB,EAAE;IAChC;EACF;EAEAX,gBAAgBA,CAAA;IACd,IAAI,CAAC1C,iBAAiB,CAACsD,qCAAqC,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC,CAACC,IAAI,CAC7ErE,GAAG,CAACsE,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,oBAAoB,GAAGH,GAAG,CAACC,OAAO,CAACG,GAAG,CAACJ,GAAG,IAAG;UAChD,OAAO;YACLzC,KAAK,EAAEyC,GAAG,CAACK,cAAc;YACzB7C,KAAK,EAAEwC,GAAG,CAACM;WACZ;QACH,CAAC,CAAC;QACF,IAAI,CAACb,kBAAkB,GAAG,IAAI,CAACU,oBAAoB,CAAC,CAAC,CAAC;QACtD,IAAI,IAAI,CAACV,kBAAkB,CAACjC,KAAK,EAAE;UACjC,IAAI,CAACkC,iCAAiC,EAAE;UACxC,IAAI,CAACC,wBAAwB,EAAE;UAC/B;UACA,IAAI,CAACC,uBAAuB,EAAE;QAChC;MACF;IACF,CAAC,CAAC,CACH,CAACW,SAAS,EAAE;EACf;EAEAC,YAAYA,CAACC,YAAyB;IAEpC,MAAMC,WAAW,GAAkB,EAAE;IACrC,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACrCL,YAAY,CAACL,GAAG,CAACW,QAAQ,IAAIA,QAAQ,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF,KAAK,MAAMA,KAAK,IAAIP,YAAY,EAAE;MAChCD,WAAW,CAACS,IAAI,CAAC,EAAE,CAAC;IACtB;IACA,KAAK,MAAMJ,QAAQ,IAAIN,YAAY,EAAE;MACnC,MAAMW,UAAU,GAAGT,YAAY,CAACU,OAAO,CAACN,QAAQ,CAACC,MAAgB,CAAC;MAClE,IAAII,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBV,WAAW,CAACU,UAAU,CAAC,CAACD,IAAI,CAAC;UAC3BG,SAAS,EAAEP,QAAQ,EAAEO,SAAS,IAAI,KAAK;UACvCC,QAAQ,EAAER,QAAQ,CAACS,GAAG;UACtBC,UAAU,EAAEV,QAAQ,CAACU,UAAU;UAC/BT,MAAM,EAAED,QAAQ,CAACC,MAAM;UACvBU,UAAU,EAAEX,QAAQ,CAACW,UAAU;UAC/BC,SAAS,EAAEZ,QAAQ,CAACY;SACrB,CAAC;MACJ;IACF;IACA,OAAOjB,WAAW;EACpB;EAIAf,wBAAwBA,CAAA;IACtB,IAAI,CAACnD,yBAAyB,CAACoF,qDAAqD,CAAC;MACnF9B,IAAI,EAAE;QACJ+B,YAAY,EAAE,IAAI,CAACpC,kBAAkB,CAACjC,KAAK;QAC3CsE,iBAAiB,EAAE,IAAI,CAAC9E,SAAS;QACjC+E,cAAc,EAAE,IAAI,CAAC3E,cAAc;QACnC4E,gBAAgB,EAAE,IAAI,CAACjF,QAAQ;QAC/BkF,aAAa,EAAE,IAAI,CAAC9E;;KAEvB,CAAC,CAACoD,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACgC,qBAAqB,GAAGlC,GAAG,CAACC,OAAO;QACxC,IAAI,CAAChD,YAAY,GAAG+C,GAAG,CAACC,OAAO,CAACkC,kBAAkB,IAAI,CAAC;QACvD,IAAI,CAAC9E,iBAAiB,GAAG2C,GAAG,CAACC,OAAO,CAACmC,cAAc,IAAI,CAAC;MAC1D;IACF,CAAC,CAAC;EACJ;EAIAC,cAAcA,CAACC,SAAc;IAC3B;IACA,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEA7C,iCAAiCA,CAAA;IAC/B,IAAI,CAACjD,yBAAyB,CAAC+F,8DAA8D,CAAC;MAC5F1C,IAAI,EAAE,IAAI,CAACL,kBAAkB,CAACjC;KAC/B,CAAC,CAAC+C,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACuC,8BAA8B,GAAGzC,GAAG,CAACC,OAAO;MACnD;IACF,CAAC,CAAC;EACJ;EAEAyC,0BAA0BA,CAACC,CAAW,EAAEC,CAA8H;IACpK,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMC,IAAI,IAAIH,CAAC,EAAE;MACpB,MAAMI,YAAY,GAAGH,CAAC,CAACI,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKJ,IAAI,IAAIG,KAAK,CAACE,WAAW,CAAC;MACpFN,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAACC,YAAY;IAC1B;IACA,OAAOF,CAAC;EACV;EAGAO,cAAcA,CAAC5F,KAAU,EAAE6F,OAAc;IACvC,KAAK,MAAMP,IAAI,IAAIO,OAAO,EAAE;MAC1B,IAAIP,IAAI,CAACtF,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOsF,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAIAQ,eAAeA,CAACC,WAA0B,EAAEC,sBAAgD;IAC1F,MAAMC,cAAc,GAAG,IAAI3C,GAAG,CAAC0C,sBAAsB,CAACpD,GAAG,CAAC0C,IAAI,IAAI,GAAGA,IAAI,CAACpB,UAAU,IAAIoB,IAAI,CAAC9B,MAAM,EAAE,CAAC,CAAC;IACvG,OAAOuC,WAAW,CAACnD,GAAG,CAACsD,UAAU,IAAG;MAClC,OAAOA,UAAU,CAACtD,GAAG,CAAC0C,IAAI,IAAG;QAC3B,MAAMa,GAAG,GAAG,GAAGb,IAAI,CAACpB,UAAU,IAAIoB,IAAI,CAAC9B,MAAM,EAAE;QAC/C,IAAIyC,cAAc,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;UAC3Bb,IAAI,CAACxB,SAAS,GAAG,IAAI;QACvB,CAAC,MAAM;UACLwB,IAAI,CAACxB,SAAS,GAAG,KAAK;QACxB;QACA,OAAOwB,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAe,eAAeA,CAACC,CAAQ,EAAEC,CAAQ;IAChC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAACF,CAAC,CAAC3D,GAAG,CAAC0C,IAAI,IAAI,CAAC,GAAGA,IAAI,CAACpB,UAAU,IAAIoB,IAAI,CAAC9B,MAAM,EAAE,EAAE8B,IAAI,CAACxB,SAAS,CAAC,CAAC,CAAC;IAC1F,OAAOwC,CAAC,CAAC1D,GAAG,CAAC0C,IAAI,IAAG;MAClB,MAAMa,GAAG,GAAG,GAAGb,IAAI,CAACpB,UAAU,IAAIoB,IAAI,CAAC9B,MAAM,EAAE;MAC/C,OAAO;QACL,GAAG8B,IAAI;QACPxB,SAAS,EAAE0C,IAAI,CAACJ,GAAG,CAACD,GAAG,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACP,GAAG,CAAC,GAAG;OAC5C;IACH,CAAC,CAAC;EACJ;EAEAQ,wBAAwBA,CAACrB,IAAS,EAAEsB,GAAQ;IAC1C,IAAI,CAAC5H,yBAAyB,CAAC6H,qDAAqD,CAAC;MACnFvE,IAAI,EAAEgD,IAAI,CAACwB;KACZ,CAAC,CAAC/D,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,MAAMqE,IAAI,GAAGvE,GAAG,CAACC,OAAO;QACxB,IAAI,CAACuE,qBAAqB,GAAG;UAC3BC,QAAQ,EAAEF,IAAI,CAACG,oBAAoB,EAAED,QAAQ,IAAIE,SAAS;UAC1D/G,WAAW,EAAE2G,IAAI,CAACG,oBAAoB,EAAE9G,WAAW;UACnDiE,YAAY,EAAE0C,IAAI,CAACG,oBAAoB,EAAE7C,YAAY;UACrDyC,oBAAoB,EAAEC,IAAI,CAACG,oBAAoB,EAAEJ,oBAAoB;UACrEM,mBAAmB,EAAEL,IAAI,CAACG,oBAAoB,EAAE9G,WAAW,GAAG,IAAI,CAACwF,cAAc,CAACmB,IAAI,CAACG,oBAAoB,EAAE9G,WAAW,EAAE,IAAI,CAACH,kBAAkB,CAAC,GAAG,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAAC;UAC/KoH,cAAc,EAAEN,IAAI,CAACM,cAAc,GAAGN,IAAI,CAACM,cAAc,GAAGF,SAAS;UACrEG,YAAY,EAAEP,IAAI,CAACO,YAAY;UAC/BC,0BAA0B,EAAER,IAAI,CAACQ,0BAA0B,EAAE9D,MAAM,CAAE+D,CAAM,IAAKA,CAAC,CAAC1D,SAAS,CAAC;UAC5F2D,cAAc,EAAEV,IAAI,CAACU;SACtB;QACD;QACA,IAAI,CAACnH,kBAAkB,GAAG,IAAI,CAAC0G,qBAAqB,CAACO,0BAA0B,EAAE3E,GAAG,CAAE8E,KAAU,IAAKA,KAAK,CAAC3D,QAAQ,CAAC,IAAI,EAAE;QAE1H;QACA,IAAI,CAAC3B,uBAAuB,EAAE;QAE9B,IAAI,CAACzD,aAAa,CAACgJ,IAAI,CAACf,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIAgB,eAAeA,CAACC,IAA2B;IACzC;IACA,IAAI,CAACA,IAAI,CAACC,KAAK,IAAI,CAACD,IAAI,CAACE,SAAS,EAAE;MAClC,IAAI,CAACnJ,OAAO,CAACoJ,YAAY,CAAC,SAAS,CAAC;MACpC;IACF;IAEA;IACA,IAAI,CAAC5I,YAAY,CAAC6I,OAAO,CAACJ,IAAI,CAACC,KAAK,EAAED,IAAI,CAACE,SAAS,CAAC,CAAChF,SAAS,CAAC;MAC9DmF,IAAI,EAAGC,IAAU,IAAI;QACnB;QACA,MAAMC,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAEzC;QACA,MAAMI,SAAS,GAAGC,MAAM,CAACb,IAAI,CAACS,OAAO,EAAE,QAAQ,CAAC;QAEhD,IAAI,CAACG,SAAS,EAAE;UACd,IAAI,CAAC3J,OAAO,CAACoJ,YAAY,CAAC,kBAAkB,CAAC;UAC7CK,GAAG,CAACI,eAAe,CAACL,OAAO,CAAC,CAAC,CAAC;UAC9B;QACF;QAEA;QACAG,SAAS,CAACG,gBAAgB,CAAC,cAAc,EAAE,MAAK;UAC9CL,GAAG,CAACI,eAAe,CAACL,OAAO,CAAC;QAC9B,CAAC,CAAC;MACJ,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAAC/J,OAAO,CAACoJ,YAAY,CAAC,cAAc,CAAC;MAC3C;KACD,CAAC;EACJ;EAAEa,SAASA,CAACjC,GAAQ,EAAEtB,IAAU;IAC9B,IAAI,CAAC/D,KAAK,GAAG,IAAI;IACjB,IAAI,CAACQ,WAAW,EAAE,EAAC;IACnB,IAAI,CAACzB,kBAAkB,GAAG,EAAE,EAAC;IAC7B,IAAI,CAAC0G,qBAAqB,GAAG;MAC3B5G,WAAW,EAAE,CAAC;MACdiE,YAAY,EAAE8C,SAAS;MACvBW,KAAK,EAAEX,SAAS;MAChB2B,MAAM,EAAE,EAAE;MACVhC,oBAAoB,EAAEK,SAAS;MAC/B4B,YAAY,EAAE,KAAK;MACnB3B,mBAAmB,EAAE,IAAI,CAACnH,kBAAkB,CAAC,CAAC,CAAC;MAC/CqH,YAAY,EAAE,EAAE;MAChBC,0BAA0B,EAAEJ;KAC7B;IAED,IAAI7B,IAAI,EAAE;MACR,IAAI,CAAC/D,KAAK,GAAG,KAAK;MAClB,IAAI,CAACoF,wBAAwB,CAACrB,IAAI,EAAEsB,GAAG,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAACrF,KAAK,GAAG,IAAI;MACjB,IAAI,CAAC5C,aAAa,CAACgJ,IAAI,CAACf,GAAG,CAAC;MAC5B;MACAoC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC5G,uBAAuB,EAAE;MAChC,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEA6G,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACrF,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAIsF,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACE,SAAS,CAACD,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEAG,QAAQA,CAAC/D,IAAS;IAChB,IAAIkD,MAAM,CAACc,OAAO,CAAC,WAAWhE,IAAI,CAACwB,oBAAoB,IAAI,CAAC,EAAE;MAC5D,IAAI,CAAC9H,yBAAyB,CAACuK,oDAAoD,CAAC;QAClFjH,IAAI,EAAEgD,IAAI,CAACwB;OACZ,CAAC,CAAC/D,SAAS,CAACP,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC9D,OAAO,CAAC4K,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACxH,iBAAiB,EAAE;QAC1B;MACF,CAAC,CAAC;IACJ;IACA,OAAOsD,IAAI;EACb;EAEAmE,WAAWA,CAACC,SAAqC;IAC/C,MAAMC,QAAQ,GAAa,EAAE;IAC7B,KAAK,MAAMxD,GAAG,IAAIuD,SAAS,EAAE;MAC3B,IAAIA,SAAS,CAACvD,GAAG,CAAC,EAAE;QAClBwD,QAAQ,CAAChG,IAAI,CAACwC,GAAG,CAAC;MACpB;IACF;IACA,OAAOwD,QAAQ;EACjB;EACAC,gBAAgBA,CAAC7C,IAAa;IAC5B,MAAM8C,SAAS,GAA6B,EAAE;IAC9C,KAAK,MAAMC,SAAS,IAAI/C,IAAI,EAAE;MAC5B,KAAK,MAAMW,KAAK,IAAIoC,SAAS,EAAE;QAC7B,IAAIpC,KAAK,CAAC5D,SAAS,IAAI4D,KAAK,CAACvD,SAAS,IAAIuD,KAAK,CAACzD,UAAU,KAAK,IAAI,CAAC+C,qBAAqB,CAACI,mBAAmB,CAACpH,KAAK,EAAE;UACnH6J,SAAS,CAAClG,IAAI,CAAC;YACbI,QAAQ,EAAE2D,KAAK,CAAC3D,QAAQ;YACxBD,SAAS,EAAE4D,KAAK,CAAC5D;WAClB,CAAC;QACJ;MACF;IACF;IACA,OAAO+F,SAAS;EAClB;EAEA;EACAE,iDAAiDA,CAAA;IAC/C,MAAMC,MAAM,GAA6B,EAAE;IAE3C;IACA,IAAI,IAAI,CAAC1J,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAAC2J,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC5J,YAAY,EAAE;MACtF;MACA6J,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC9J,YAAY,CAAC,CAAC+J,OAAO,CAACC,SAAS,IAAG;QACnDA,SAAS,CAACD,OAAO,CAAC1C,KAAK,IAAG;UACxB,IAAIA,KAAK,CAAC4C,OAAO,IACf,IAAI,CAAChK,kBAAkB,CAACiK,QAAQ,CAAC7C,KAAK,CAAC4C,OAAO,CAAC,IAC/C,CAAC5C,KAAK,CAAC8C,UAAU,EAAE;YACnBR,MAAM,CAACrG,IAAI,CAAC;cACVI,QAAQ,EAAE2D,KAAK,CAAC4C,OAAO;cACvBxG,SAAS,EAAE;aACZ,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,OAAOkG,MAAM;EACf;EAAES,uBAAuBA,CAAC7D,GAAQ;IAChC,MAAMX,cAAc,GAAG,IAAI,CAAC8D,iDAAiD,EAAE;IAE/E;IACAnB,OAAO,CAAC8B,GAAG,CAAC,UAAU,EAAE,IAAI,CAACpK,kBAAkB,CAAC;IAChDsI,OAAO,CAAC8B,GAAG,CAAC,WAAW,EAAEzE,cAAc,CAAC;IACxC2C,OAAO,CAAC8B,GAAG,CAAC,aAAa,EAAER,MAAM,CAACS,IAAI,CAAC,IAAI,CAACtK,YAAY,CAAC,CAAC;IAC1DuI,OAAO,CAAC8B,GAAG,CAAC,WAAW,EAAER,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC9J,YAAY,CAAC,CAACuK,MAAM,CAAC,CAACC,KAAK,EAAEC,MAAM,KAAKD,KAAK,GAAGC,MAAM,CAACb,MAAM,EAAE,CAAC,CAAC,CAAC;IAC9GrB,OAAO,CAAC8B,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC1D,qBAAqB,CAACI,mBAAmB,CAAC;IAEpE,MAAM2D,KAAK,GAAG;MACZ3K,WAAW,EAAE,IAAI,CAAC4G,qBAAqB,CAACI,mBAAmB,CAACpH,KAAK;MACjEqE,YAAY,EAAE,IAAI,CAACpC,kBAAkB,CAACjC,KAAK;MAC3C8H,KAAK,EAAE,IAAI,CAACzG,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC2J,WAAW,GAAG7D,SAAS;MACpE2B,MAAM,EAAE7C,cAAc;MAAE;MACxBa,oBAAoB,EAAE,IAAI,CAACE,qBAAqB,CAACF,oBAAoB,IAAIK,SAAS;MAClF4B,YAAY,EAAE,IAAI,CAAC/B,qBAAqB,CAAC+B,YAAY,IAAI,KAAK;MAC9DzB,YAAY,EAAE,IAAI,CAACN,qBAAqB,CAACM;KAC1C;IAED,IAAI,CAAC2D,UAAU,CAACF,KAAK,CAACjC,MAAM,CAAC;IAE7B,IAAI,IAAI,CAACjK,KAAK,CAACqM,aAAa,CAACjB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACrL,OAAO,CAACuM,aAAa,CAAC,IAAI,CAACtM,KAAK,CAACqM,aAAa,CAAC;MACpD;IACF;IAAE,IAAI,CAAC/L,2BAA2B,CAACiM,qBAAqB,CAACL,KAAK,CAAC,CAAChI,SAAS,CAACP,GAAG,IAAG;MAC9E,IAAIA,GAAG,IAAIA,GAAG,CAACF,IAAK,IAAIE,GAAG,CAACF,IAAI,CAACI,UAAW,KAAK,CAAC,EAAE;QAClD,IAAI,CAAC9D,OAAO,CAAC4K,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACzH,WAAW,EAAE,EAAC;QACnB,IAAI,CAACI,wBAAwB,EAAE;QAC/ByE,GAAG,CAACyE,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACzM,OAAO,CAACoJ,YAAY,CAACxF,GAAG,IAAIA,GAAG,CAACF,IAAI,IAAIE,GAAG,CAACF,IAAI,CAACgJ,OAAQ,CAAC;MACjE;IACF,CAAC,CAAC;EACJ;EAGAC,OAAOA,CAAC3E,GAAQ;IACdA,GAAG,CAACyE,KAAK,EAAE;EACb;EAEAJ,UAAUA,CAACnC,MAAa;IACtB,IAAI,CAACjK,KAAK,CAAC2M,KAAK,EAAE;IAClB,IAAI,IAAI,CAACjK,KAAK,IAAI,CAAC,IAAI,CAACF,YAAY,EAAE;MACpC,IAAI,CAACxC,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;IACjC;IACA,IAAI,EAAEiI,MAAM,CAACmB,MAAM,GAAG,CAAC,CAAC,EAAE;MACxB,IAAI,CAACpL,KAAK,CAACgC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC;IACnC;IACA,IAAI,CAAChC,KAAK,CAACgC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACmG,qBAAqB,CAACM,YAAY,CAAC;EACxE;EAEAmE,aAAaA,CAACC,QAA4B;IACxC,IAAIC,KAAK,GAAG,EAAE;IACd,IAAID,QAAQ,IAAIvE,SAAS,EAAE;MACzB,QAAQuE,QAAQ;QACd,KAAK,CAAC;UACJC,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF;UACE;MACJ;IACF;IACA,OAAOA,KAAK;EACd;EAGAC,cAAcA,CAAC7F,WAA0B,EAAEC,sBAAgD;IACzF,MAAMC,cAAc,GAAGD,sBAAsB,CAACpD,GAAG,CAAC0C,IAAI,IAAIA,IAAI,CAACvB,QAAQ,CAAC;IACxE,OAAOgC,WAAW,CAACnD,GAAG,CAACsD,UAAU,IAAG;MAClC,OAAOA,UAAU,CAACtD,GAAG,CAAC0C,IAAI,IAAG;QAC3B,IAAIW,cAAc,CAACsE,QAAQ,CAACjF,IAAI,CAACvB,QAAQ,CAAC,EAAE;UAC1CuB,IAAI,CAACxB,SAAS,GAAG,IAAI;QACvB,CAAC,MAAM;UACLwB,IAAI,CAACxB,SAAS,GAAG,KAAK;QACxB;QACA,OAAOwB,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAE;EACKlD,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACH,kBAAkB,EAAEjC,KAAK,EAAE;IAErC,IAAI,CAACO,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACF,YAAY,GAAG,EAAE,CAAC,CAAC;IAExB,IAAI,CAAChB,cAAc,CAACwM,4BAA4B,CAAC;MAC/CC,WAAW,EAAE,IAAI,CAAC7J,kBAAkB,CAACjC;KACtC,CAAC,CAAC+C,SAAS,CAAC;MACXmF,IAAI,EAAG6D,QAAQ,IAAI;QACjB,IAAI,CAACxL,qBAAqB,GAAG,KAAK;QAClC,IAAIwL,QAAQ,CAACtJ,OAAO,EAAE;UACpB,IAAI,CAACpC,YAAY,GAAG,IAAI,CAAC2L,gCAAgC,CAACD,QAAQ,CAACtJ,OAAO,CAAC;QAC7E;MACF,CAAC;MACDkG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpI,qBAAqB,GAAG,KAAK;QAClCqI,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D;QACA,IAAI,CAACtI,YAAY,GAAG,EAAE;MACxB;KACD,CAAC;EACJ,CAAC,CAAE;EACK2L,gCAAgCA,CAACC,OAAY;IACnD,MAAM5L,YAAY,GAAiB,EAAE;IAErC6J,MAAM,CAAC+B,OAAO,CAACA,OAAO,CAAC,CAAC7B,OAAO,CAAC,CAAC,CAAC8B,QAAQ,EAAEpB,MAAM,CAAgB,KAAI;MACpE;MACA;MACAzK,YAAY,CAAC6L,QAAQ,CAAC,GAAGpB,MAAM,CAAClI,GAAG,CAAE8E,KAAU,KAAM;QACnDyE,SAAS,EAAEzE,KAAK,CAAC0E,SAAS;QAC1BF,QAAQ,EAAExE,KAAK,CAAC2E,QAAQ;QACxB3I,KAAK,EAAEgE,KAAK,CAAC4E,KAAK;QAClBhC,OAAO,EAAE5C,KAAK,CAAC6E,OAAO;QACtBC,SAAS,EAAE9E,KAAK,CAAC+E,SAAS;QAAE;QAC5BC,UAAU,EAAE,KAAK;QACjBlC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOnK,YAAY;EACrB;EACA;EACAsM,gCAAgCA,CAAA;IAC9B,MAAMtM,YAAY,GAAiB,EAAE;IAErC,IAAI,CAAC,IAAI,CAAC0F,WAAW,IAAI,IAAI,CAACA,WAAW,CAACkE,MAAM,KAAK,CAAC,EAAE;MACtD,OAAO5J,YAAY;IACrB;IAEA,IAAI,CAAC0F,WAAW,CAACqE,OAAO,CAACwC,GAAG,IAAG;MAC7BA,GAAG,CAACxC,OAAO,CAAC1C,KAAK,IAAG;QAClB,IAAI,CAACA,KAAK,CAACxD,UAAU,EAAE;QAEvB;QACA;QAEA;QACA,MAAM2I,aAAa,GAAGnF,KAAK,CAACxD,UAAU,CAAC4I,KAAK,CAAC,WAAW,CAAC;QACzD,MAAMZ,QAAQ,GAAGW,aAAa,GAAG,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM;QAEhE,IAAI,CAACxM,YAAY,CAAC6L,QAAQ,CAAC,EAAE;UAC3B7L,YAAY,CAAC6L,QAAQ,CAAC,GAAG,EAAE;QAC7B;QAAE7L,YAAY,CAAC6L,QAAQ,CAAC,CAACvI,IAAI,CAAC;UAC5BwI,SAAS,EAAEzE,KAAK,CAACxD,UAAU;UAC3BgI,QAAQ,EAAEA,QAAQ;UAClBxI,KAAK,EAAEgE,KAAK,CAAClE,MAAM,GAAG,GAAGkE,KAAK,CAAClE,MAAM,GAAG,GAAG2D,SAAS;UACpDmD,OAAO,EAAE5C,KAAK,CAAC3D,QAAQ;UACvByI,SAAS,EAAE9E,KAAK,CAACzD,UAAU,IAAIkD,SAAS;UAAE;UAC1CuF,UAAU,EAAEhF,KAAK,CAAC5D,SAAS,IAAI,KAAK;UACpC0G,UAAU,EAAE,CAAC9C,KAAK,CAACvD,SAAS,IAC1B,EAAE,IAAI,CAAC6C,qBAAqB,EAAEI,mBAAmB,EAAEpH,KAAK,KAAK0H,KAAK,CAACzD,UAAU,CAAC,IAC9E,IAAI,CAAC+C,qBAAqB,EAAES,cAAc,KAAK;SAClD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOpH,YAAY;EACrB,CAAC,CAAE;EACH0M,0BAA0BA,CAACC,aAA8B;IACvD;IACA,IAAI,CAAC1M,kBAAkB,GAAG0M,aAAa,CAACpK,GAAG,CAAC0C,IAAI,IAAIA,IAAI,CAACgF,OAAO,CAAC,CAAC7G,MAAM,CAACwJ,EAAE,IAAIA,EAAE,KAAK9F,SAAS,CAAa;EAC9G;EACA;EACA+F,mBAAmBA,CAACC,WAAqB;IACvC,IAAI,CAAC7M,kBAAkB,GAAG6M,WAAW;EACvC;EACA;EACApI,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACzE,kBAAkB,GAAG,EAAE;IAE5B;IACA;EACF;EAEA;EACA8M,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACpG,qBAAqB,EAAEI,mBAAmB,EAAE;MACpD,OAAO,yBAAyB;IAClC;IAEA,MAAMiG,UAAU,GAAG,IAAI,CAACrG,qBAAqB,CAACI,mBAAmB;IACjE,IAAIiG,UAAU,CAACrN,KAAK,KAAK,CAAC,EAAE;MAC1B,OAAO,2BAA2B;IACpC,CAAC,MAAM,IAAIqN,UAAU,CAACrN,KAAK,KAAK,CAAC,EAAE;MACjC,OAAO,2BAA2B;IACpC;IAEA,OAAO,EAAE;EACX;CACD;AA1kBYxB,yBAAyB,GAAA8O,UAAA,EARrCvP,SAAS,CAAC;EACTwP,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC3P,YAAY,EAAEG,YAAY,EAAEC,eAAe,EAAEH,QAAQ,EAAEK,kBAAkB,EAAEC,mBAAmB;CACzG,CAAC,C,EAEWC,yBAAyB,CA0kBrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}