{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiTemplateDeleteTemplatePost$Json } from '../fn/template/api-template-delete-template-post-json';\nimport { apiTemplateDeleteTemplatePost$Plain } from '../fn/template/api-template-delete-template-post-plain';\nimport { apiTemplateGetTemplateByIdPost$Json } from '../fn/template/api-template-get-template-by-id-post-json';\nimport { apiTemplateGetTemplateByIdPost$Plain } from '../fn/template/api-template-get-template-by-id-post-plain';\nimport { apiTemplateGetTemplateDetailByIdPost$Json } from '../fn/template/api-template-get-template-detail-by-id-post-json';\nimport { apiTemplateGetTemplateDetailByIdPost$Plain } from '../fn/template/api-template-get-template-detail-by-id-post-plain';\nimport { apiTemplateGetTemplateListForCommonPost$Json } from '../fn/template/api-template-get-template-list-for-common-post-json';\nimport { apiTemplateGetTemplateListForCommonPost$Plain } from '../fn/template/api-template-get-template-list-for-common-post-plain';\nimport { apiTemplateGetTemplateListPost$Json } from '../fn/template/api-template-get-template-list-post-json';\nimport { apiTemplateGetTemplateListPost$Plain } from '../fn/template/api-template-get-template-list-post-plain';\nimport { apiTemplateSaveTemplatePost$Json } from '../fn/template/api-template-save-template-post-json';\nimport { apiTemplateSaveTemplatePost$Plain } from '../fn/template/api-template-save-template-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let TemplateService = /*#__PURE__*/(() => {\n  class TemplateService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiTemplateGetTemplateListPost()` */\n    static {\n      this.ApiTemplateGetTemplateListPostPath = '/api/Template/GetTemplateList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateGetTemplateListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateListPost$Plain$Response(params, context) {\n      return apiTemplateGetTemplateListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateGetTemplateListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateListPost$Plain(params, context) {\n      return this.apiTemplateGetTemplateListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateGetTemplateListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateListPost$Json$Response(params, context) {\n      return apiTemplateGetTemplateListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateGetTemplateListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateListPost$Json(params, context) {\n      return this.apiTemplateGetTemplateListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiTemplateGetTemplateByIdPost()` */\n    static {\n      this.ApiTemplateGetTemplateByIdPostPath = '/api/Template/GetTemplateById';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateGetTemplateByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateByIdPost$Plain$Response(params, context) {\n      return apiTemplateGetTemplateByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateGetTemplateByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateByIdPost$Plain(params, context) {\n      return this.apiTemplateGetTemplateByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateGetTemplateByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateByIdPost$Json$Response(params, context) {\n      return apiTemplateGetTemplateByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateGetTemplateByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateByIdPost$Json(params, context) {\n      return this.apiTemplateGetTemplateByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiTemplateDeleteTemplatePost()` */\n    static {\n      this.ApiTemplateDeleteTemplatePostPath = '/api/Template/DeleteTemplate';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateDeleteTemplatePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateDeleteTemplatePost$Plain$Response(params, context) {\n      return apiTemplateDeleteTemplatePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateDeleteTemplatePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateDeleteTemplatePost$Plain(params, context) {\n      return this.apiTemplateDeleteTemplatePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateDeleteTemplatePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateDeleteTemplatePost$Json$Response(params, context) {\n      return apiTemplateDeleteTemplatePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateDeleteTemplatePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateDeleteTemplatePost$Json(params, context) {\n      return this.apiTemplateDeleteTemplatePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiTemplateSaveTemplatePost()` */\n    static {\n      this.ApiTemplateSaveTemplatePostPath = '/api/Template/SaveTemplate';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateSaveTemplatePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateSaveTemplatePost$Plain$Response(params, context) {\n      return apiTemplateSaveTemplatePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateSaveTemplatePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateSaveTemplatePost$Plain(params, context) {\n      return this.apiTemplateSaveTemplatePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateSaveTemplatePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateSaveTemplatePost$Json$Response(params, context) {\n      return apiTemplateSaveTemplatePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateSaveTemplatePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateSaveTemplatePost$Json(params, context) {\n      return this.apiTemplateSaveTemplatePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiTemplateGetTemplateDetailByIdPost()` */\n    static {\n      this.ApiTemplateGetTemplateDetailByIdPostPath = '/api/Template/GetTemplateDetailById';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateGetTemplateDetailByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateDetailByIdPost$Plain$Response(params, context) {\n      return apiTemplateGetTemplateDetailByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateGetTemplateDetailByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateDetailByIdPost$Plain(params, context) {\n      return this.apiTemplateGetTemplateDetailByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateGetTemplateDetailByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateDetailByIdPost$Json$Response(params, context) {\n      return apiTemplateGetTemplateDetailByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateGetTemplateDetailByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateDetailByIdPost$Json(params, context) {\n      return this.apiTemplateGetTemplateDetailByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiTemplateGetTemplateListForCommonPost()` */\n    static {\n      this.ApiTemplateGetTemplateListForCommonPostPath = '/api/Template/GetTemplateListForCommon';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateGetTemplateListForCommonPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateListForCommonPost$Plain$Response(params, context) {\n      return apiTemplateGetTemplateListForCommonPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateGetTemplateListForCommonPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateListForCommonPost$Plain(params, context) {\n      return this.apiTemplateGetTemplateListForCommonPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiTemplateGetTemplateListForCommonPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateListForCommonPost$Json$Response(params, context) {\n      return apiTemplateGetTemplateListForCommonPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiTemplateGetTemplateListForCommonPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiTemplateGetTemplateListForCommonPost$Json(params, context) {\n      return this.apiTemplateGetTemplateListForCommonPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function TemplateService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TemplateService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: TemplateService,\n        factory: TemplateService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return TemplateService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}