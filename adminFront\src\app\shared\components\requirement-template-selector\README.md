# RequirementTemplateSelectorComponent

需求項目選擇器共用元件，參考 space-template-selector 的設計模式，提供可重複使用的需求項目選擇功能。

## 功能特色

- 使用 Requirement/GetList API 作為資料源
- 支援多種搜尋條件（區域、工程項目、類型、狀態等）
- 支援多選和單選模式
- 分頁功能
- 選擇摘要和總金額計算
- 響應式設計
- 完整的錯誤處理

## 使用方式

### 1. 直接使用選擇器元件

```typescript
import { RequirementTemplateSelectorComponent } from './requirement-template-selector.component';

// 在對話框中開啟
const dialogRef = this.dialogService.open(RequirementTemplateSelectorComponent, {
  context: {
    buildCaseId: 123,
    multiple: true,
    preSelectedItems: []
  }
});

// 監聽事件
dialogRef.componentRef.instance.selectionConfirmed.subscribe(config => {
  console.log('選擇的項目:', config.selectedItems);
});
```

### 2. 使用按鈕元件

```html
<app-requirement-template-selector-button
  [buildCaseId]="buildCaseId"
  [text]="'選擇需求項目'"
  [icon]="'fas fa-list-check'"
  [buttonClass]="'btn btn-primary'"
  [multiple]="true"
  (selectionConfirmed)="onSelectionConfirmed($event)"
  (selectionCancelled)="onSelectionCancelled()"
  (error)="onError($event)">
</app-requirement-template-selector-button>
```

## 輸入參數

### RequirementTemplateSelectorComponent

- `buildCaseId: number` - 建案 ID（必填）
- `multiple: boolean` - 是否支援多選（預設：true）
- `preSelectedItems: ExtendedRequirementItem[]` - 預選項目

### RequirementTemplateSelectorButtonComponent

- `buildCaseId: string` - 建案 ID（必填）
- `text: string` - 按鈕文字（預設：'選擇需求項目'）
- `icon: string` - 按鈕圖示（預設：'fas fa-list'）
- `buttonClass: string` - 按鈕樣式類別（預設：'btn btn-primary'）
- `disabled: boolean` - 是否停用按鈕
- `multiple: boolean` - 是否支援多選（預設：true）
- `preSelectedItems: ExtendedRequirementItem[]` - 預選項目

## 輸出事件

- `selectionConfirmed: RequirementSelectionConfig` - 確認選擇事件
- `selectionCancelled: void` - 取消選擇事件
- `error: string` - 錯誤事件

## 資料介面

```typescript
export interface ExtendedRequirementItem extends GetRequirement {
  selected?: boolean;
}

export interface RequirementSelectionConfig {
  selectedItems: ExtendedRequirementItem[];
  totalPrice: number;
  buildCaseId: number;
}
```

## 依賴項目

- RequirementService (API 服務)
- Nebular UI 元件
- Angular 基本模組
- GetListRequirementRequest, GetRequirement 等 API 模型
