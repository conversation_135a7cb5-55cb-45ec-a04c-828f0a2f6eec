{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { parseNumericPattern, parseNDigits } from \"../utils.js\";\nexport var SecondParser = /*#__PURE__*/function (_Parser) {\n  _inherits(SecondParser, _Parser);\n  var _super = _createSuper(SecondParser);\n  function SecondParser() {\n    var _this;\n    _classCallCheck(this, SecondParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 50);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['t', 'T']);\n    return _this;\n  }\n  _createClass(SecondParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      switch (token) {\n        case 's':\n          return parseNumericPattern(numericPatterns.second, dateString);\n        case 'so':\n          return match.ordinalNumber(dateString, {\n            unit: 'second'\n          });\n        default:\n          return parseNDigits(token.length, dateString);\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value >= 0 && value <= 59;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      date.setUTCSeconds(value, 0);\n      return date;\n    }\n  }]);\n  return SecondParser;\n}(Parser);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}