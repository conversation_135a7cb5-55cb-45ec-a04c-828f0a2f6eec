{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend, indexOf, isArrayLike, isObject, keys, isArray, each } from 'zrender/lib/core/util.js';\nimport { getECData } from './innerStore.js';\nimport { liftColor } from 'zrender/lib/tool/color.js';\nimport { queryDataIndex, makeInner } from './model.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { error } from './log.js';\n// Reserve 0 as default.\nvar _highlightNextDigit = 1;\nvar _highlightKeyMap = {};\nvar getSavedStates = makeInner();\nvar getComponentStates = makeInner();\nexport var HOVER_STATE_NORMAL = 0;\nexport var HOVER_STATE_BLUR = 1;\nexport var HOVER_STATE_EMPHASIS = 2;\nexport var SPECIAL_STATES = ['emphasis', 'blur', 'select'];\nexport var DISPLAY_STATES = ['normal', 'emphasis', 'blur', 'select'];\nexport var Z2_EMPHASIS_LIFT = 10;\nexport var Z2_SELECT_LIFT = 9;\nexport var HIGHLIGHT_ACTION_TYPE = 'highlight';\nexport var DOWNPLAY_ACTION_TYPE = 'downplay';\nexport var SELECT_ACTION_TYPE = 'select';\nexport var UNSELECT_ACTION_TYPE = 'unselect';\nexport var TOGGLE_SELECT_ACTION_TYPE = 'toggleSelect';\nfunction hasFillOrStroke(fillOrStroke) {\n  return fillOrStroke != null && fillOrStroke !== 'none';\n}\nfunction doChangeHoverState(el, stateName, hoverStateEnum) {\n  if (el.onHoverStateChange && (el.hoverState || 0) !== hoverStateEnum) {\n    el.onHoverStateChange(stateName);\n  }\n  el.hoverState = hoverStateEnum;\n}\nfunction singleEnterEmphasis(el) {\n  // Only mark the flag.\n  // States will be applied in the echarts.ts in next frame.\n  doChangeHoverState(el, 'emphasis', HOVER_STATE_EMPHASIS);\n}\nfunction singleLeaveEmphasis(el) {\n  // Only mark the flag.\n  // States will be applied in the echarts.ts in next frame.\n  if (el.hoverState === HOVER_STATE_EMPHASIS) {\n    doChangeHoverState(el, 'normal', HOVER_STATE_NORMAL);\n  }\n}\nfunction singleEnterBlur(el) {\n  doChangeHoverState(el, 'blur', HOVER_STATE_BLUR);\n}\nfunction singleLeaveBlur(el) {\n  if (el.hoverState === HOVER_STATE_BLUR) {\n    doChangeHoverState(el, 'normal', HOVER_STATE_NORMAL);\n  }\n}\nfunction singleEnterSelect(el) {\n  el.selected = true;\n}\nfunction singleLeaveSelect(el) {\n  el.selected = false;\n}\nfunction updateElementState(el, updater, commonParam) {\n  updater(el, commonParam);\n}\nfunction traverseUpdateState(el, updater, commonParam) {\n  updateElementState(el, updater, commonParam);\n  el.isGroup && el.traverse(function (child) {\n    updateElementState(child, updater, commonParam);\n  });\n}\nexport function setStatesFlag(el, stateName) {\n  switch (stateName) {\n    case 'emphasis':\n      el.hoverState = HOVER_STATE_EMPHASIS;\n      break;\n    case 'normal':\n      el.hoverState = HOVER_STATE_NORMAL;\n      break;\n    case 'blur':\n      el.hoverState = HOVER_STATE_BLUR;\n      break;\n    case 'select':\n      el.selected = true;\n  }\n}\n/**\r\n * If we reuse elements when rerender.\r\n * DON'T forget to clearStates before we update the style and shape.\r\n * Or we may update on the wrong state instead of normal state.\r\n */\nexport function clearStates(el) {\n  if (el.isGroup) {\n    el.traverse(function (child) {\n      child.clearStates();\n    });\n  } else {\n    el.clearStates();\n  }\n}\nfunction getFromStateStyle(el, props, toStateName, defaultValue) {\n  var style = el.style;\n  var fromState = {};\n  for (var i = 0; i < props.length; i++) {\n    var propName = props[i];\n    var val = style[propName];\n    fromState[propName] = val == null ? defaultValue && defaultValue[propName] : val;\n  }\n  for (var i = 0; i < el.animators.length; i++) {\n    var animator = el.animators[i];\n    if (animator.__fromStateTransition\n    // Don't consider the animation to emphasis state.\n    && animator.__fromStateTransition.indexOf(toStateName) < 0 && animator.targetName === 'style') {\n      animator.saveTo(fromState, props);\n    }\n  }\n  return fromState;\n}\nfunction createEmphasisDefaultState(el, stateName, targetStates, state) {\n  var hasSelect = targetStates && indexOf(targetStates, 'select') >= 0;\n  var cloned = false;\n  if (el instanceof Path) {\n    var store = getSavedStates(el);\n    var fromFill = hasSelect ? store.selectFill || store.normalFill : store.normalFill;\n    var fromStroke = hasSelect ? store.selectStroke || store.normalStroke : store.normalStroke;\n    if (hasFillOrStroke(fromFill) || hasFillOrStroke(fromStroke)) {\n      state = state || {};\n      var emphasisStyle = state.style || {};\n      // inherit case\n      if (emphasisStyle.fill === 'inherit') {\n        cloned = true;\n        state = extend({}, state);\n        emphasisStyle = extend({}, emphasisStyle);\n        emphasisStyle.fill = fromFill;\n      }\n      // Apply default color lift\n      else if (!hasFillOrStroke(emphasisStyle.fill) && hasFillOrStroke(fromFill)) {\n        cloned = true;\n        // Not modify the original value.\n        state = extend({}, state);\n        emphasisStyle = extend({}, emphasisStyle);\n        // Already being applied 'emphasis'. DON'T lift color multiple times.\n        emphasisStyle.fill = liftColor(fromFill);\n      }\n      // Not highlight stroke if fill has been highlighted.\n      else if (!hasFillOrStroke(emphasisStyle.stroke) && hasFillOrStroke(fromStroke)) {\n        if (!cloned) {\n          state = extend({}, state);\n          emphasisStyle = extend({}, emphasisStyle);\n        }\n        emphasisStyle.stroke = liftColor(fromStroke);\n      }\n      state.style = emphasisStyle;\n    }\n  }\n  if (state) {\n    // TODO Share with textContent?\n    if (state.z2 == null) {\n      if (!cloned) {\n        state = extend({}, state);\n      }\n      var z2EmphasisLift = el.z2EmphasisLift;\n      state.z2 = el.z2 + (z2EmphasisLift != null ? z2EmphasisLift : Z2_EMPHASIS_LIFT);\n    }\n  }\n  return state;\n}\nfunction createSelectDefaultState(el, stateName, state) {\n  // const hasSelect = indexOf(el.currentStates, stateName) >= 0;\n  if (state) {\n    // TODO Share with textContent?\n    if (state.z2 == null) {\n      state = extend({}, state);\n      var z2SelectLift = el.z2SelectLift;\n      state.z2 = el.z2 + (z2SelectLift != null ? z2SelectLift : Z2_SELECT_LIFT);\n    }\n  }\n  return state;\n}\nfunction createBlurDefaultState(el, stateName, state) {\n  var hasBlur = indexOf(el.currentStates, stateName) >= 0;\n  var currentOpacity = el.style.opacity;\n  var fromState = !hasBlur ? getFromStateStyle(el, ['opacity'], stateName, {\n    opacity: 1\n  }) : null;\n  state = state || {};\n  var blurStyle = state.style || {};\n  if (blurStyle.opacity == null) {\n    // clone state\n    state = extend({}, state);\n    blurStyle = extend({\n      // Already being applied 'emphasis'. DON'T mul opacity multiple times.\n      opacity: hasBlur ? currentOpacity : fromState.opacity * 0.1\n    }, blurStyle);\n    state.style = blurStyle;\n  }\n  return state;\n}\nfunction elementStateProxy(stateName, targetStates) {\n  var state = this.states[stateName];\n  if (this.style) {\n    if (stateName === 'emphasis') {\n      return createEmphasisDefaultState(this, stateName, targetStates, state);\n    } else if (stateName === 'blur') {\n      return createBlurDefaultState(this, stateName, state);\n    } else if (stateName === 'select') {\n      return createSelectDefaultState(this, stateName, state);\n    }\n  }\n  return state;\n}\n/**\r\n * Set hover style (namely \"emphasis style\") of element.\r\n * @param el Should not be `zrender/graphic/Group`.\r\n * @param focus 'self' | 'selfInSeries' | 'series'\r\n */\nexport function setDefaultStateProxy(el) {\n  el.stateProxy = elementStateProxy;\n  var textContent = el.getTextContent();\n  var textGuide = el.getTextGuideLine();\n  if (textContent) {\n    textContent.stateProxy = elementStateProxy;\n  }\n  if (textGuide) {\n    textGuide.stateProxy = elementStateProxy;\n  }\n}\nexport function enterEmphasisWhenMouseOver(el, e) {\n  !shouldSilent(el, e)\n  // \"emphasis\" event highlight has higher priority than mouse highlight.\n  && !el.__highByOuter && traverseUpdateState(el, singleEnterEmphasis);\n}\nexport function leaveEmphasisWhenMouseOut(el, e) {\n  !shouldSilent(el, e)\n  // \"emphasis\" event highlight has higher priority than mouse highlight.\n  && !el.__highByOuter && traverseUpdateState(el, singleLeaveEmphasis);\n}\nexport function enterEmphasis(el, highlightDigit) {\n  el.__highByOuter |= 1 << (highlightDigit || 0);\n  traverseUpdateState(el, singleEnterEmphasis);\n}\nexport function leaveEmphasis(el, highlightDigit) {\n  !(el.__highByOuter &= ~(1 << (highlightDigit || 0))) && traverseUpdateState(el, singleLeaveEmphasis);\n}\nexport function enterBlur(el) {\n  traverseUpdateState(el, singleEnterBlur);\n}\nexport function leaveBlur(el) {\n  traverseUpdateState(el, singleLeaveBlur);\n}\nexport function enterSelect(el) {\n  traverseUpdateState(el, singleEnterSelect);\n}\nexport function leaveSelect(el) {\n  traverseUpdateState(el, singleLeaveSelect);\n}\nfunction shouldSilent(el, e) {\n  return el.__highDownSilentOnTouch && e.zrByTouch;\n}\nexport function allLeaveBlur(api) {\n  var model = api.getModel();\n  var leaveBlurredSeries = [];\n  var allComponentViews = [];\n  model.eachComponent(function (componentType, componentModel) {\n    var componentStates = getComponentStates(componentModel);\n    var isSeries = componentType === 'series';\n    var view = isSeries ? api.getViewOfSeriesModel(componentModel) : api.getViewOfComponentModel(componentModel);\n    !isSeries && allComponentViews.push(view);\n    if (componentStates.isBlured) {\n      // Leave blur anyway\n      view.group.traverse(function (child) {\n        singleLeaveBlur(child);\n      });\n      isSeries && leaveBlurredSeries.push(componentModel);\n    }\n    componentStates.isBlured = false;\n  });\n  each(allComponentViews, function (view) {\n    if (view && view.toggleBlurSeries) {\n      view.toggleBlurSeries(leaveBlurredSeries, false, model);\n    }\n  });\n}\nexport function blurSeries(targetSeriesIndex, focus, blurScope, api) {\n  var ecModel = api.getModel();\n  blurScope = blurScope || 'coordinateSystem';\n  function leaveBlurOfIndices(data, dataIndices) {\n    for (var i = 0; i < dataIndices.length; i++) {\n      var itemEl = data.getItemGraphicEl(dataIndices[i]);\n      itemEl && leaveBlur(itemEl);\n    }\n  }\n  if (targetSeriesIndex == null) {\n    return;\n  }\n  if (!focus || focus === 'none') {\n    return;\n  }\n  var targetSeriesModel = ecModel.getSeriesByIndex(targetSeriesIndex);\n  var targetCoordSys = targetSeriesModel.coordinateSystem;\n  if (targetCoordSys && targetCoordSys.master) {\n    targetCoordSys = targetCoordSys.master;\n  }\n  var blurredSeries = [];\n  ecModel.eachSeries(function (seriesModel) {\n    var sameSeries = targetSeriesModel === seriesModel;\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys && coordSys.master) {\n      coordSys = coordSys.master;\n    }\n    var sameCoordSys = coordSys && targetCoordSys ? coordSys === targetCoordSys : sameSeries; // If there is no coordinate system. use sameSeries instead.\n    if (!(\n    // Not blur other series if blurScope series\n    blurScope === 'series' && !sameSeries\n    // Not blur other coordinate system if blurScope is coordinateSystem\n    || blurScope === 'coordinateSystem' && !sameCoordSys\n    // Not blur self series if focus is series.\n    || focus === 'series' && sameSeries\n    // TODO blurScope: coordinate system\n    )) {\n      var view = api.getViewOfSeriesModel(seriesModel);\n      view.group.traverse(function (child) {\n        // For the elements that have been triggered by other components,\n        // and are still required to be highlighted,\n        // because the current is directly forced to blur the element,\n        // it will cause the focus self to be unable to highlight, so skip the blur of this element.\n        if (child.__highByOuter && sameSeries && focus === 'self') {\n          return;\n        }\n        singleEnterBlur(child);\n      });\n      if (isArrayLike(focus)) {\n        leaveBlurOfIndices(seriesModel.getData(), focus);\n      } else if (isObject(focus)) {\n        var dataTypes = keys(focus);\n        for (var d = 0; d < dataTypes.length; d++) {\n          leaveBlurOfIndices(seriesModel.getData(dataTypes[d]), focus[dataTypes[d]]);\n        }\n      }\n      blurredSeries.push(seriesModel);\n      getComponentStates(seriesModel).isBlured = true;\n    }\n  });\n  ecModel.eachComponent(function (componentType, componentModel) {\n    if (componentType === 'series') {\n      return;\n    }\n    var view = api.getViewOfComponentModel(componentModel);\n    if (view && view.toggleBlurSeries) {\n      view.toggleBlurSeries(blurredSeries, true, ecModel);\n    }\n  });\n}\nexport function blurComponent(componentMainType, componentIndex, api) {\n  if (componentMainType == null || componentIndex == null) {\n    return;\n  }\n  var componentModel = api.getModel().getComponent(componentMainType, componentIndex);\n  if (!componentModel) {\n    return;\n  }\n  getComponentStates(componentModel).isBlured = true;\n  var view = api.getViewOfComponentModel(componentModel);\n  if (!view || !view.focusBlurEnabled) {\n    return;\n  }\n  view.group.traverse(function (child) {\n    singleEnterBlur(child);\n  });\n}\nexport function blurSeriesFromHighlightPayload(seriesModel, payload, api) {\n  var seriesIndex = seriesModel.seriesIndex;\n  var data = seriesModel.getData(payload.dataType);\n  if (!data) {\n    if (process.env.NODE_ENV !== 'production') {\n      error(\"Unknown dataType \" + payload.dataType);\n    }\n    return;\n  }\n  var dataIndex = queryDataIndex(data, payload);\n  // Pick the first one if there is multiple/none exists.\n  dataIndex = (isArray(dataIndex) ? dataIndex[0] : dataIndex) || 0;\n  var el = data.getItemGraphicEl(dataIndex);\n  if (!el) {\n    var count = data.count();\n    var current = 0;\n    // If data on dataIndex is NaN.\n    while (!el && current < count) {\n      el = data.getItemGraphicEl(current++);\n    }\n  }\n  if (el) {\n    var ecData = getECData(el);\n    blurSeries(seriesIndex, ecData.focus, ecData.blurScope, api);\n  } else {\n    // If there is no element put on the data. Try getting it from raw option\n    // TODO Should put it on seriesModel?\n    var focus_1 = seriesModel.get(['emphasis', 'focus']);\n    var blurScope = seriesModel.get(['emphasis', 'blurScope']);\n    if (focus_1 != null) {\n      blurSeries(seriesIndex, focus_1, blurScope, api);\n    }\n  }\n}\nexport function findComponentHighDownDispatchers(componentMainType, componentIndex, name, api) {\n  var ret = {\n    focusSelf: false,\n    dispatchers: null\n  };\n  if (componentMainType == null || componentMainType === 'series' || componentIndex == null || name == null) {\n    return ret;\n  }\n  var componentModel = api.getModel().getComponent(componentMainType, componentIndex);\n  if (!componentModel) {\n    return ret;\n  }\n  var view = api.getViewOfComponentModel(componentModel);\n  if (!view || !view.findHighDownDispatchers) {\n    return ret;\n  }\n  var dispatchers = view.findHighDownDispatchers(name);\n  // At presnet, the component (like Geo) only blur inside itself.\n  // So we do not use `blurScope` in component.\n  var focusSelf;\n  for (var i = 0; i < dispatchers.length; i++) {\n    if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatchers[i])) {\n      error('param should be highDownDispatcher');\n    }\n    if (getECData(dispatchers[i]).focus === 'self') {\n      focusSelf = true;\n      break;\n    }\n  }\n  return {\n    focusSelf: focusSelf,\n    dispatchers: dispatchers\n  };\n}\nexport function handleGlobalMouseOverForHighDown(dispatcher, e, api) {\n  if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatcher)) {\n    error('param should be highDownDispatcher');\n  }\n  var ecData = getECData(dispatcher);\n  var _a = findComponentHighDownDispatchers(ecData.componentMainType, ecData.componentIndex, ecData.componentHighDownName, api),\n    dispatchers = _a.dispatchers,\n    focusSelf = _a.focusSelf;\n  // If `findHighDownDispatchers` is supported on the component,\n  // highlight/downplay elements with the same name.\n  if (dispatchers) {\n    if (focusSelf) {\n      blurComponent(ecData.componentMainType, ecData.componentIndex, api);\n    }\n    each(dispatchers, function (dispatcher) {\n      return enterEmphasisWhenMouseOver(dispatcher, e);\n    });\n  } else {\n    // Try blur all in the related series. Then emphasis the hoverred.\n    // TODO. progressive mode.\n    blurSeries(ecData.seriesIndex, ecData.focus, ecData.blurScope, api);\n    if (ecData.focus === 'self') {\n      blurComponent(ecData.componentMainType, ecData.componentIndex, api);\n    }\n    // Other than series, component that not support `findHighDownDispatcher` will\n    // also use it. But in this case, highlight/downplay are only supported in\n    // mouse hover but not in dispatchAction.\n    enterEmphasisWhenMouseOver(dispatcher, e);\n  }\n}\nexport function handleGlobalMouseOutForHighDown(dispatcher, e, api) {\n  if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatcher)) {\n    error('param should be highDownDispatcher');\n  }\n  allLeaveBlur(api);\n  var ecData = getECData(dispatcher);\n  var dispatchers = findComponentHighDownDispatchers(ecData.componentMainType, ecData.componentIndex, ecData.componentHighDownName, api).dispatchers;\n  if (dispatchers) {\n    each(dispatchers, function (dispatcher) {\n      return leaveEmphasisWhenMouseOut(dispatcher, e);\n    });\n  } else {\n    leaveEmphasisWhenMouseOut(dispatcher, e);\n  }\n}\nexport function toggleSelectionFromPayload(seriesModel, payload, api) {\n  if (!isSelectChangePayload(payload)) {\n    return;\n  }\n  var dataType = payload.dataType;\n  var data = seriesModel.getData(dataType);\n  var dataIndex = queryDataIndex(data, payload);\n  if (!isArray(dataIndex)) {\n    dataIndex = [dataIndex];\n  }\n  seriesModel[payload.type === TOGGLE_SELECT_ACTION_TYPE ? 'toggleSelect' : payload.type === SELECT_ACTION_TYPE ? 'select' : 'unselect'](dataIndex, dataType);\n}\nexport function updateSeriesElementSelection(seriesModel) {\n  var allData = seriesModel.getAllData();\n  each(allData, function (_a) {\n    var data = _a.data,\n      type = _a.type;\n    data.eachItemGraphicEl(function (el, idx) {\n      seriesModel.isSelected(idx, type) ? enterSelect(el) : leaveSelect(el);\n    });\n  });\n}\nexport function getAllSelectedIndices(ecModel) {\n  var ret = [];\n  ecModel.eachSeries(function (seriesModel) {\n    var allData = seriesModel.getAllData();\n    each(allData, function (_a) {\n      var data = _a.data,\n        type = _a.type;\n      var dataIndices = seriesModel.getSelectedDataIndices();\n      if (dataIndices.length > 0) {\n        var item = {\n          dataIndex: dataIndices,\n          seriesIndex: seriesModel.seriesIndex\n        };\n        if (type != null) {\n          item.dataType = type;\n        }\n        ret.push(item);\n      }\n    });\n  });\n  return ret;\n}\n/**\r\n * Enable the function that mouseover will trigger the emphasis state.\r\n *\r\n * NOTE:\r\n * This function should be used on the element with dataIndex, seriesIndex.\r\n *\r\n */\nexport function enableHoverEmphasis(el, focus, blurScope) {\n  setAsHighDownDispatcher(el, true);\n  traverseUpdateState(el, setDefaultStateProxy);\n  enableHoverFocus(el, focus, blurScope);\n}\nexport function disableHoverEmphasis(el) {\n  setAsHighDownDispatcher(el, false);\n}\nexport function toggleHoverEmphasis(el, focus, blurScope, isDisabled) {\n  isDisabled ? disableHoverEmphasis(el) : enableHoverEmphasis(el, focus, blurScope);\n}\nexport function enableHoverFocus(el, focus, blurScope) {\n  var ecData = getECData(el);\n  if (focus != null) {\n    // TODO dataIndex may be set after this function. This check is not useful.\n    // if (ecData.dataIndex == null) {\n    //     if (__DEV__) {\n    //         console.warn('focus can only been set on element with dataIndex');\n    //     }\n    // }\n    // else {\n    ecData.focus = focus;\n    ecData.blurScope = blurScope;\n    // }\n  } else if (ecData.focus) {\n    ecData.focus = null;\n  }\n}\nvar OTHER_STATES = ['emphasis', 'blur', 'select'];\nvar defaultStyleGetterMap = {\n  itemStyle: 'getItemStyle',\n  lineStyle: 'getLineStyle',\n  areaStyle: 'getAreaStyle'\n};\n/**\r\n * Set emphasis/blur/selected states of element.\r\n */\nexport function setStatesStylesFromModel(el, itemModel, styleType,\n// default itemStyle\ngetter) {\n  styleType = styleType || 'itemStyle';\n  for (var i = 0; i < OTHER_STATES.length; i++) {\n    var stateName = OTHER_STATES[i];\n    var model = itemModel.getModel([stateName, styleType]);\n    var state = el.ensureState(stateName);\n    // Let it throw error if getterType is not found.\n    state.style = getter ? getter(model) : model[defaultStyleGetterMap[styleType]]();\n  }\n}\n/**\r\n *\r\n * Set element as highlight / downplay dispatcher.\r\n * It will be checked when element received mouseover event or from highlight action.\r\n * It's in change of all highlight/downplay behavior of it's children.\r\n *\r\n * @param el\r\n * @param el.highDownSilentOnTouch\r\n *        In touch device, mouseover event will be trigger on touchstart event\r\n *        (see module:zrender/dom/HandlerProxy). By this mechanism, we can\r\n *        conveniently use hoverStyle when tap on touch screen without additional\r\n *        code for compatibility.\r\n *        But if the chart/component has select feature, which usually also use\r\n *        hoverStyle, there might be conflict between 'select-highlight' and\r\n *        'hover-highlight' especially when roam is enabled (see geo for example).\r\n *        In this case, `highDownSilentOnTouch` should be used to disable\r\n *        hover-highlight on touch device.\r\n * @param asDispatcher If `false`, do not set as \"highDownDispatcher\".\r\n */\nexport function setAsHighDownDispatcher(el, asDispatcher) {\n  var disable = asDispatcher === false;\n  var extendedEl = el;\n  // Make `highDownSilentOnTouch` and `onStateChange` only work after\n  // `setAsHighDownDispatcher` called. Avoid it is modified by user unexpectedly.\n  if (el.highDownSilentOnTouch) {\n    extendedEl.__highDownSilentOnTouch = el.highDownSilentOnTouch;\n  }\n  // Simple optimize, since this method might be\n  // called for each elements of a group in some cases.\n  if (!disable || extendedEl.__highDownDispatcher) {\n    // Emphasis, normal can be triggered manually by API or other components like hover link.\n    // el[method]('emphasis', onElementEmphasisEvent)[method]('normal', onElementNormalEvent);\n    // Also keep previous record.\n    extendedEl.__highByOuter = extendedEl.__highByOuter || 0;\n    extendedEl.__highDownDispatcher = !disable;\n  }\n}\nexport function isHighDownDispatcher(el) {\n  return !!(el && el.__highDownDispatcher);\n}\n/**\r\n * Enable component highlight/downplay features:\r\n * + hover link (within the same name)\r\n * + focus blur in component\r\n */\nexport function enableComponentHighDownFeatures(el, componentModel, componentHighDownName) {\n  var ecData = getECData(el);\n  ecData.componentMainType = componentModel.mainType;\n  ecData.componentIndex = componentModel.componentIndex;\n  ecData.componentHighDownName = componentHighDownName;\n}\n/**\r\n * Support highlight/downplay record on each elements.\r\n * For the case: hover highlight/downplay (legend, visualMap, ...) and\r\n * user triggered highlight/downplay should not conflict.\r\n * Only all of the highlightDigit cleared, return to normal.\r\n * @param {string} highlightKey\r\n * @return {number} highlightDigit\r\n */\nexport function getHighlightDigit(highlightKey) {\n  var highlightDigit = _highlightKeyMap[highlightKey];\n  if (highlightDigit == null && _highlightNextDigit <= 32) {\n    highlightDigit = _highlightKeyMap[highlightKey] = _highlightNextDigit++;\n  }\n  return highlightDigit;\n}\nexport function isSelectChangePayload(payload) {\n  var payloadType = payload.type;\n  return payloadType === SELECT_ACTION_TYPE || payloadType === UNSELECT_ACTION_TYPE || payloadType === TOGGLE_SELECT_ACTION_TYPE;\n}\nexport function isHighDownPayload(payload) {\n  var payloadType = payload.type;\n  return payloadType === HIGHLIGHT_ACTION_TYPE || payloadType === DOWNPLAY_ACTION_TYPE;\n}\nexport function savePathStates(el) {\n  var store = getSavedStates(el);\n  store.normalFill = el.style.fill;\n  store.normalStroke = el.style.stroke;\n  var selectState = el.states.select || {};\n  store.selectFill = selectState.style && selectState.style.fill || null;\n  store.selectStroke = selectState.style && selectState.style.stroke || null;\n}", "map": {"version": 3, "names": ["extend", "indexOf", "isArrayLike", "isObject", "keys", "isArray", "each", "getECData", "liftColor", "queryDataIndex", "makeInner", "Path", "error", "_highlightNextDigit", "_highlightKeyMap", "getSavedStates", "getComponentStates", "HOVER_STATE_NORMAL", "HOVER_STATE_BLUR", "HOVER_STATE_EMPHASIS", "SPECIAL_STATES", "DISPLAY_STATES", "Z2_EMPHASIS_LIFT", "Z2_SELECT_LIFT", "HIGHLIGHT_ACTION_TYPE", "DOWNPLAY_ACTION_TYPE", "SELECT_ACTION_TYPE", "UNSELECT_ACTION_TYPE", "TOGGLE_SELECT_ACTION_TYPE", "hasFillOrStroke", "fillOrStroke", "doChangeHoverState", "el", "stateName", "hoverStateEnum", "onHoverStateChange", "hoverState", "singleEnterEmphasis", "singleLeaveEmphasis", "singleEnterBlur", "singleLeaveBlur", "singleEnterSelect", "selected", "singleLeaveSelect", "updateElementState", "updater", "commonParam", "traverseUpdateState", "isGroup", "traverse", "child", "setStatesFlag", "clearStates", "getFromStateStyle", "props", "toStateName", "defaultValue", "style", "fromState", "i", "length", "propName", "val", "animators", "animator", "__fromStateTransition", "targetName", "saveTo", "createEmphasisDefaultState", "targetStates", "state", "hasSelect", "cloned", "store", "fromFill", "selectFill", "normalFill", "fromStroke", "selectStroke", "normalStroke", "emphasisStyle", "fill", "stroke", "z2", "z2EmphasisLift", "createSelectDefaultState", "z2SelectLift", "createBlurDefaultState", "<PERSON><PERSON><PERSON><PERSON>", "currentStates", "currentOpacity", "opacity", "blurStyle", "elementStateProxy", "states", "setDefaultStateProxy", "stateProxy", "textContent", "getTextContent", "textGuide", "getTextGuideLine", "enterEmphasisWhenMouseOver", "e", "shouldSilent", "__highByOuter", "leaveEmphasisWhenMouseOut", "enterEmphasis", "highlightDigit", "leaveEmphasis", "enterBlur", "leaveBlur", "enterSelect", "leaveSelect", "__highDownSilentOnTouch", "zrByTouch", "allLeaveBlur", "api", "model", "getModel", "leaveBlurredSeries", "allComponentViews", "eachComponent", "componentType", "componentModel", "componentStates", "isSeries", "view", "getViewOfSeriesModel", "getViewOfComponentModel", "push", "isBlured", "group", "toggleBlurSeries", "blurSeries", "targetSeriesIndex", "focus", "blurScope", "ecModel", "leaveBlurOfIndices", "data", "dataIndices", "itemEl", "getItemGraphicEl", "targetSeriesModel", "getSeriesByIndex", "targetCoordSys", "coordinateSystem", "master", "blurredSeries", "eachSeries", "seriesModel", "sameSeries", "coordSys", "sameCoordSys", "getData", "dataTypes", "d", "blurComponent", "componentMainType", "componentIndex", "getComponent", "focusBlurEnabled", "blurSeriesFromHighlightPayload", "payload", "seriesIndex", "dataType", "process", "env", "NODE_ENV", "dataIndex", "count", "current", "ecData", "focus_1", "get", "findComponentHighDownDispatchers", "name", "ret", "focusSelf", "dispatchers", "findHighDownDispatchers", "is<PERSON>ighD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleGlobalMouseOverForHighDown", "dispatcher", "_a", "componentHighDownName", "handleGlobalMouseOutForHighDown", "toggleSelectionFromPayload", "isSelectChangePayload", "type", "updateSeriesElementSelection", "allData", "getAllData", "eachItemGraphicEl", "idx", "isSelected", "getAllSelectedIndices", "getSelectedDataIndices", "item", "enableHoverEmphasis", "setAs<PERSON>ighDownD<PERSON><PERSON><PERSON><PERSON>", "enableHoverFocus", "disableHoverEmphasis", "toggleHoverEmphasis", "isDisabled", "OTHER_STATES", "defaultStyleGetterMap", "itemStyle", "lineStyle", "areaStyle", "setStatesStylesFromModel", "itemModel", "styleType", "getter", "ensureState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disable", "extendedEl", "highDownSilentOnTouch", "__highDownD<PERSON><PERSON>tcher", "enableComponentHighDownFeatures", "mainType", "getHighlightDigit", "highlight<PERSON><PERSON>", "payloadType", "isHighDownPayload", "savePathStates", "selectState", "select"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/util/states.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend, indexOf, isArrayLike, isObject, keys, isArray, each } from 'zrender/lib/core/util.js';\nimport { getECData } from './innerStore.js';\nimport { liftColor } from 'zrender/lib/tool/color.js';\nimport { queryDataIndex, makeInner } from './model.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { error } from './log.js';\n// Reserve 0 as default.\nvar _highlightNextDigit = 1;\nvar _highlightKeyMap = {};\nvar getSavedStates = makeInner();\nvar getComponentStates = makeInner();\nexport var HOVER_STATE_NORMAL = 0;\nexport var HOVER_STATE_BLUR = 1;\nexport var HOVER_STATE_EMPHASIS = 2;\nexport var SPECIAL_STATES = ['emphasis', 'blur', 'select'];\nexport var DISPLAY_STATES = ['normal', 'emphasis', 'blur', 'select'];\nexport var Z2_EMPHASIS_LIFT = 10;\nexport var Z2_SELECT_LIFT = 9;\nexport var HIGHLIGHT_ACTION_TYPE = 'highlight';\nexport var DOWNPLAY_ACTION_TYPE = 'downplay';\nexport var SELECT_ACTION_TYPE = 'select';\nexport var UNSELECT_ACTION_TYPE = 'unselect';\nexport var TOGGLE_SELECT_ACTION_TYPE = 'toggleSelect';\nfunction hasFillOrStroke(fillOrStroke) {\n  return fillOrStroke != null && fillOrStroke !== 'none';\n}\nfunction doChangeHoverState(el, stateName, hoverStateEnum) {\n  if (el.onHoverStateChange && (el.hoverState || 0) !== hoverStateEnum) {\n    el.onHoverStateChange(stateName);\n  }\n  el.hoverState = hoverStateEnum;\n}\nfunction singleEnterEmphasis(el) {\n  // Only mark the flag.\n  // States will be applied in the echarts.ts in next frame.\n  doChangeHoverState(el, 'emphasis', HOVER_STATE_EMPHASIS);\n}\nfunction singleLeaveEmphasis(el) {\n  // Only mark the flag.\n  // States will be applied in the echarts.ts in next frame.\n  if (el.hoverState === HOVER_STATE_EMPHASIS) {\n    doChangeHoverState(el, 'normal', HOVER_STATE_NORMAL);\n  }\n}\nfunction singleEnterBlur(el) {\n  doChangeHoverState(el, 'blur', HOVER_STATE_BLUR);\n}\nfunction singleLeaveBlur(el) {\n  if (el.hoverState === HOVER_STATE_BLUR) {\n    doChangeHoverState(el, 'normal', HOVER_STATE_NORMAL);\n  }\n}\nfunction singleEnterSelect(el) {\n  el.selected = true;\n}\nfunction singleLeaveSelect(el) {\n  el.selected = false;\n}\nfunction updateElementState(el, updater, commonParam) {\n  updater(el, commonParam);\n}\nfunction traverseUpdateState(el, updater, commonParam) {\n  updateElementState(el, updater, commonParam);\n  el.isGroup && el.traverse(function (child) {\n    updateElementState(child, updater, commonParam);\n  });\n}\nexport function setStatesFlag(el, stateName) {\n  switch (stateName) {\n    case 'emphasis':\n      el.hoverState = HOVER_STATE_EMPHASIS;\n      break;\n    case 'normal':\n      el.hoverState = HOVER_STATE_NORMAL;\n      break;\n    case 'blur':\n      el.hoverState = HOVER_STATE_BLUR;\n      break;\n    case 'select':\n      el.selected = true;\n  }\n}\n/**\r\n * If we reuse elements when rerender.\r\n * DON'T forget to clearStates before we update the style and shape.\r\n * Or we may update on the wrong state instead of normal state.\r\n */\nexport function clearStates(el) {\n  if (el.isGroup) {\n    el.traverse(function (child) {\n      child.clearStates();\n    });\n  } else {\n    el.clearStates();\n  }\n}\nfunction getFromStateStyle(el, props, toStateName, defaultValue) {\n  var style = el.style;\n  var fromState = {};\n  for (var i = 0; i < props.length; i++) {\n    var propName = props[i];\n    var val = style[propName];\n    fromState[propName] = val == null ? defaultValue && defaultValue[propName] : val;\n  }\n  for (var i = 0; i < el.animators.length; i++) {\n    var animator = el.animators[i];\n    if (animator.__fromStateTransition\n    // Don't consider the animation to emphasis state.\n    && animator.__fromStateTransition.indexOf(toStateName) < 0 && animator.targetName === 'style') {\n      animator.saveTo(fromState, props);\n    }\n  }\n  return fromState;\n}\nfunction createEmphasisDefaultState(el, stateName, targetStates, state) {\n  var hasSelect = targetStates && indexOf(targetStates, 'select') >= 0;\n  var cloned = false;\n  if (el instanceof Path) {\n    var store = getSavedStates(el);\n    var fromFill = hasSelect ? store.selectFill || store.normalFill : store.normalFill;\n    var fromStroke = hasSelect ? store.selectStroke || store.normalStroke : store.normalStroke;\n    if (hasFillOrStroke(fromFill) || hasFillOrStroke(fromStroke)) {\n      state = state || {};\n      var emphasisStyle = state.style || {};\n      // inherit case\n      if (emphasisStyle.fill === 'inherit') {\n        cloned = true;\n        state = extend({}, state);\n        emphasisStyle = extend({}, emphasisStyle);\n        emphasisStyle.fill = fromFill;\n      }\n      // Apply default color lift\n      else if (!hasFillOrStroke(emphasisStyle.fill) && hasFillOrStroke(fromFill)) {\n        cloned = true;\n        // Not modify the original value.\n        state = extend({}, state);\n        emphasisStyle = extend({}, emphasisStyle);\n        // Already being applied 'emphasis'. DON'T lift color multiple times.\n        emphasisStyle.fill = liftColor(fromFill);\n      }\n      // Not highlight stroke if fill has been highlighted.\n      else if (!hasFillOrStroke(emphasisStyle.stroke) && hasFillOrStroke(fromStroke)) {\n        if (!cloned) {\n          state = extend({}, state);\n          emphasisStyle = extend({}, emphasisStyle);\n        }\n        emphasisStyle.stroke = liftColor(fromStroke);\n      }\n      state.style = emphasisStyle;\n    }\n  }\n  if (state) {\n    // TODO Share with textContent?\n    if (state.z2 == null) {\n      if (!cloned) {\n        state = extend({}, state);\n      }\n      var z2EmphasisLift = el.z2EmphasisLift;\n      state.z2 = el.z2 + (z2EmphasisLift != null ? z2EmphasisLift : Z2_EMPHASIS_LIFT);\n    }\n  }\n  return state;\n}\nfunction createSelectDefaultState(el, stateName, state) {\n  // const hasSelect = indexOf(el.currentStates, stateName) >= 0;\n  if (state) {\n    // TODO Share with textContent?\n    if (state.z2 == null) {\n      state = extend({}, state);\n      var z2SelectLift = el.z2SelectLift;\n      state.z2 = el.z2 + (z2SelectLift != null ? z2SelectLift : Z2_SELECT_LIFT);\n    }\n  }\n  return state;\n}\nfunction createBlurDefaultState(el, stateName, state) {\n  var hasBlur = indexOf(el.currentStates, stateName) >= 0;\n  var currentOpacity = el.style.opacity;\n  var fromState = !hasBlur ? getFromStateStyle(el, ['opacity'], stateName, {\n    opacity: 1\n  }) : null;\n  state = state || {};\n  var blurStyle = state.style || {};\n  if (blurStyle.opacity == null) {\n    // clone state\n    state = extend({}, state);\n    blurStyle = extend({\n      // Already being applied 'emphasis'. DON'T mul opacity multiple times.\n      opacity: hasBlur ? currentOpacity : fromState.opacity * 0.1\n    }, blurStyle);\n    state.style = blurStyle;\n  }\n  return state;\n}\nfunction elementStateProxy(stateName, targetStates) {\n  var state = this.states[stateName];\n  if (this.style) {\n    if (stateName === 'emphasis') {\n      return createEmphasisDefaultState(this, stateName, targetStates, state);\n    } else if (stateName === 'blur') {\n      return createBlurDefaultState(this, stateName, state);\n    } else if (stateName === 'select') {\n      return createSelectDefaultState(this, stateName, state);\n    }\n  }\n  return state;\n}\n/**\r\n * Set hover style (namely \"emphasis style\") of element.\r\n * @param el Should not be `zrender/graphic/Group`.\r\n * @param focus 'self' | 'selfInSeries' | 'series'\r\n */\nexport function setDefaultStateProxy(el) {\n  el.stateProxy = elementStateProxy;\n  var textContent = el.getTextContent();\n  var textGuide = el.getTextGuideLine();\n  if (textContent) {\n    textContent.stateProxy = elementStateProxy;\n  }\n  if (textGuide) {\n    textGuide.stateProxy = elementStateProxy;\n  }\n}\nexport function enterEmphasisWhenMouseOver(el, e) {\n  !shouldSilent(el, e)\n  // \"emphasis\" event highlight has higher priority than mouse highlight.\n  && !el.__highByOuter && traverseUpdateState(el, singleEnterEmphasis);\n}\nexport function leaveEmphasisWhenMouseOut(el, e) {\n  !shouldSilent(el, e)\n  // \"emphasis\" event highlight has higher priority than mouse highlight.\n  && !el.__highByOuter && traverseUpdateState(el, singleLeaveEmphasis);\n}\nexport function enterEmphasis(el, highlightDigit) {\n  el.__highByOuter |= 1 << (highlightDigit || 0);\n  traverseUpdateState(el, singleEnterEmphasis);\n}\nexport function leaveEmphasis(el, highlightDigit) {\n  !(el.__highByOuter &= ~(1 << (highlightDigit || 0))) && traverseUpdateState(el, singleLeaveEmphasis);\n}\nexport function enterBlur(el) {\n  traverseUpdateState(el, singleEnterBlur);\n}\nexport function leaveBlur(el) {\n  traverseUpdateState(el, singleLeaveBlur);\n}\nexport function enterSelect(el) {\n  traverseUpdateState(el, singleEnterSelect);\n}\nexport function leaveSelect(el) {\n  traverseUpdateState(el, singleLeaveSelect);\n}\nfunction shouldSilent(el, e) {\n  return el.__highDownSilentOnTouch && e.zrByTouch;\n}\nexport function allLeaveBlur(api) {\n  var model = api.getModel();\n  var leaveBlurredSeries = [];\n  var allComponentViews = [];\n  model.eachComponent(function (componentType, componentModel) {\n    var componentStates = getComponentStates(componentModel);\n    var isSeries = componentType === 'series';\n    var view = isSeries ? api.getViewOfSeriesModel(componentModel) : api.getViewOfComponentModel(componentModel);\n    !isSeries && allComponentViews.push(view);\n    if (componentStates.isBlured) {\n      // Leave blur anyway\n      view.group.traverse(function (child) {\n        singleLeaveBlur(child);\n      });\n      isSeries && leaveBlurredSeries.push(componentModel);\n    }\n    componentStates.isBlured = false;\n  });\n  each(allComponentViews, function (view) {\n    if (view && view.toggleBlurSeries) {\n      view.toggleBlurSeries(leaveBlurredSeries, false, model);\n    }\n  });\n}\nexport function blurSeries(targetSeriesIndex, focus, blurScope, api) {\n  var ecModel = api.getModel();\n  blurScope = blurScope || 'coordinateSystem';\n  function leaveBlurOfIndices(data, dataIndices) {\n    for (var i = 0; i < dataIndices.length; i++) {\n      var itemEl = data.getItemGraphicEl(dataIndices[i]);\n      itemEl && leaveBlur(itemEl);\n    }\n  }\n  if (targetSeriesIndex == null) {\n    return;\n  }\n  if (!focus || focus === 'none') {\n    return;\n  }\n  var targetSeriesModel = ecModel.getSeriesByIndex(targetSeriesIndex);\n  var targetCoordSys = targetSeriesModel.coordinateSystem;\n  if (targetCoordSys && targetCoordSys.master) {\n    targetCoordSys = targetCoordSys.master;\n  }\n  var blurredSeries = [];\n  ecModel.eachSeries(function (seriesModel) {\n    var sameSeries = targetSeriesModel === seriesModel;\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys && coordSys.master) {\n      coordSys = coordSys.master;\n    }\n    var sameCoordSys = coordSys && targetCoordSys ? coordSys === targetCoordSys : sameSeries; // If there is no coordinate system. use sameSeries instead.\n    if (!(\n    // Not blur other series if blurScope series\n    blurScope === 'series' && !sameSeries\n    // Not blur other coordinate system if blurScope is coordinateSystem\n    || blurScope === 'coordinateSystem' && !sameCoordSys\n    // Not blur self series if focus is series.\n    || focus === 'series' && sameSeries\n    // TODO blurScope: coordinate system\n    )) {\n      var view = api.getViewOfSeriesModel(seriesModel);\n      view.group.traverse(function (child) {\n        // For the elements that have been triggered by other components,\n        // and are still required to be highlighted,\n        // because the current is directly forced to blur the element,\n        // it will cause the focus self to be unable to highlight, so skip the blur of this element.\n        if (child.__highByOuter && sameSeries && focus === 'self') {\n          return;\n        }\n        singleEnterBlur(child);\n      });\n      if (isArrayLike(focus)) {\n        leaveBlurOfIndices(seriesModel.getData(), focus);\n      } else if (isObject(focus)) {\n        var dataTypes = keys(focus);\n        for (var d = 0; d < dataTypes.length; d++) {\n          leaveBlurOfIndices(seriesModel.getData(dataTypes[d]), focus[dataTypes[d]]);\n        }\n      }\n      blurredSeries.push(seriesModel);\n      getComponentStates(seriesModel).isBlured = true;\n    }\n  });\n  ecModel.eachComponent(function (componentType, componentModel) {\n    if (componentType === 'series') {\n      return;\n    }\n    var view = api.getViewOfComponentModel(componentModel);\n    if (view && view.toggleBlurSeries) {\n      view.toggleBlurSeries(blurredSeries, true, ecModel);\n    }\n  });\n}\nexport function blurComponent(componentMainType, componentIndex, api) {\n  if (componentMainType == null || componentIndex == null) {\n    return;\n  }\n  var componentModel = api.getModel().getComponent(componentMainType, componentIndex);\n  if (!componentModel) {\n    return;\n  }\n  getComponentStates(componentModel).isBlured = true;\n  var view = api.getViewOfComponentModel(componentModel);\n  if (!view || !view.focusBlurEnabled) {\n    return;\n  }\n  view.group.traverse(function (child) {\n    singleEnterBlur(child);\n  });\n}\nexport function blurSeriesFromHighlightPayload(seriesModel, payload, api) {\n  var seriesIndex = seriesModel.seriesIndex;\n  var data = seriesModel.getData(payload.dataType);\n  if (!data) {\n    if (process.env.NODE_ENV !== 'production') {\n      error(\"Unknown dataType \" + payload.dataType);\n    }\n    return;\n  }\n  var dataIndex = queryDataIndex(data, payload);\n  // Pick the first one if there is multiple/none exists.\n  dataIndex = (isArray(dataIndex) ? dataIndex[0] : dataIndex) || 0;\n  var el = data.getItemGraphicEl(dataIndex);\n  if (!el) {\n    var count = data.count();\n    var current = 0;\n    // If data on dataIndex is NaN.\n    while (!el && current < count) {\n      el = data.getItemGraphicEl(current++);\n    }\n  }\n  if (el) {\n    var ecData = getECData(el);\n    blurSeries(seriesIndex, ecData.focus, ecData.blurScope, api);\n  } else {\n    // If there is no element put on the data. Try getting it from raw option\n    // TODO Should put it on seriesModel?\n    var focus_1 = seriesModel.get(['emphasis', 'focus']);\n    var blurScope = seriesModel.get(['emphasis', 'blurScope']);\n    if (focus_1 != null) {\n      blurSeries(seriesIndex, focus_1, blurScope, api);\n    }\n  }\n}\nexport function findComponentHighDownDispatchers(componentMainType, componentIndex, name, api) {\n  var ret = {\n    focusSelf: false,\n    dispatchers: null\n  };\n  if (componentMainType == null || componentMainType === 'series' || componentIndex == null || name == null) {\n    return ret;\n  }\n  var componentModel = api.getModel().getComponent(componentMainType, componentIndex);\n  if (!componentModel) {\n    return ret;\n  }\n  var view = api.getViewOfComponentModel(componentModel);\n  if (!view || !view.findHighDownDispatchers) {\n    return ret;\n  }\n  var dispatchers = view.findHighDownDispatchers(name);\n  // At presnet, the component (like Geo) only blur inside itself.\n  // So we do not use `blurScope` in component.\n  var focusSelf;\n  for (var i = 0; i < dispatchers.length; i++) {\n    if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatchers[i])) {\n      error('param should be highDownDispatcher');\n    }\n    if (getECData(dispatchers[i]).focus === 'self') {\n      focusSelf = true;\n      break;\n    }\n  }\n  return {\n    focusSelf: focusSelf,\n    dispatchers: dispatchers\n  };\n}\nexport function handleGlobalMouseOverForHighDown(dispatcher, e, api) {\n  if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatcher)) {\n    error('param should be highDownDispatcher');\n  }\n  var ecData = getECData(dispatcher);\n  var _a = findComponentHighDownDispatchers(ecData.componentMainType, ecData.componentIndex, ecData.componentHighDownName, api),\n    dispatchers = _a.dispatchers,\n    focusSelf = _a.focusSelf;\n  // If `findHighDownDispatchers` is supported on the component,\n  // highlight/downplay elements with the same name.\n  if (dispatchers) {\n    if (focusSelf) {\n      blurComponent(ecData.componentMainType, ecData.componentIndex, api);\n    }\n    each(dispatchers, function (dispatcher) {\n      return enterEmphasisWhenMouseOver(dispatcher, e);\n    });\n  } else {\n    // Try blur all in the related series. Then emphasis the hoverred.\n    // TODO. progressive mode.\n    blurSeries(ecData.seriesIndex, ecData.focus, ecData.blurScope, api);\n    if (ecData.focus === 'self') {\n      blurComponent(ecData.componentMainType, ecData.componentIndex, api);\n    }\n    // Other than series, component that not support `findHighDownDispatcher` will\n    // also use it. But in this case, highlight/downplay are only supported in\n    // mouse hover but not in dispatchAction.\n    enterEmphasisWhenMouseOver(dispatcher, e);\n  }\n}\nexport function handleGlobalMouseOutForHighDown(dispatcher, e, api) {\n  if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatcher)) {\n    error('param should be highDownDispatcher');\n  }\n  allLeaveBlur(api);\n  var ecData = getECData(dispatcher);\n  var dispatchers = findComponentHighDownDispatchers(ecData.componentMainType, ecData.componentIndex, ecData.componentHighDownName, api).dispatchers;\n  if (dispatchers) {\n    each(dispatchers, function (dispatcher) {\n      return leaveEmphasisWhenMouseOut(dispatcher, e);\n    });\n  } else {\n    leaveEmphasisWhenMouseOut(dispatcher, e);\n  }\n}\nexport function toggleSelectionFromPayload(seriesModel, payload, api) {\n  if (!isSelectChangePayload(payload)) {\n    return;\n  }\n  var dataType = payload.dataType;\n  var data = seriesModel.getData(dataType);\n  var dataIndex = queryDataIndex(data, payload);\n  if (!isArray(dataIndex)) {\n    dataIndex = [dataIndex];\n  }\n  seriesModel[payload.type === TOGGLE_SELECT_ACTION_TYPE ? 'toggleSelect' : payload.type === SELECT_ACTION_TYPE ? 'select' : 'unselect'](dataIndex, dataType);\n}\nexport function updateSeriesElementSelection(seriesModel) {\n  var allData = seriesModel.getAllData();\n  each(allData, function (_a) {\n    var data = _a.data,\n      type = _a.type;\n    data.eachItemGraphicEl(function (el, idx) {\n      seriesModel.isSelected(idx, type) ? enterSelect(el) : leaveSelect(el);\n    });\n  });\n}\nexport function getAllSelectedIndices(ecModel) {\n  var ret = [];\n  ecModel.eachSeries(function (seriesModel) {\n    var allData = seriesModel.getAllData();\n    each(allData, function (_a) {\n      var data = _a.data,\n        type = _a.type;\n      var dataIndices = seriesModel.getSelectedDataIndices();\n      if (dataIndices.length > 0) {\n        var item = {\n          dataIndex: dataIndices,\n          seriesIndex: seriesModel.seriesIndex\n        };\n        if (type != null) {\n          item.dataType = type;\n        }\n        ret.push(item);\n      }\n    });\n  });\n  return ret;\n}\n/**\r\n * Enable the function that mouseover will trigger the emphasis state.\r\n *\r\n * NOTE:\r\n * This function should be used on the element with dataIndex, seriesIndex.\r\n *\r\n */\nexport function enableHoverEmphasis(el, focus, blurScope) {\n  setAsHighDownDispatcher(el, true);\n  traverseUpdateState(el, setDefaultStateProxy);\n  enableHoverFocus(el, focus, blurScope);\n}\nexport function disableHoverEmphasis(el) {\n  setAsHighDownDispatcher(el, false);\n}\nexport function toggleHoverEmphasis(el, focus, blurScope, isDisabled) {\n  isDisabled ? disableHoverEmphasis(el) : enableHoverEmphasis(el, focus, blurScope);\n}\nexport function enableHoverFocus(el, focus, blurScope) {\n  var ecData = getECData(el);\n  if (focus != null) {\n    // TODO dataIndex may be set after this function. This check is not useful.\n    // if (ecData.dataIndex == null) {\n    //     if (__DEV__) {\n    //         console.warn('focus can only been set on element with dataIndex');\n    //     }\n    // }\n    // else {\n    ecData.focus = focus;\n    ecData.blurScope = blurScope;\n    // }\n  } else if (ecData.focus) {\n    ecData.focus = null;\n  }\n}\nvar OTHER_STATES = ['emphasis', 'blur', 'select'];\nvar defaultStyleGetterMap = {\n  itemStyle: 'getItemStyle',\n  lineStyle: 'getLineStyle',\n  areaStyle: 'getAreaStyle'\n};\n/**\r\n * Set emphasis/blur/selected states of element.\r\n */\nexport function setStatesStylesFromModel(el, itemModel, styleType,\n// default itemStyle\ngetter) {\n  styleType = styleType || 'itemStyle';\n  for (var i = 0; i < OTHER_STATES.length; i++) {\n    var stateName = OTHER_STATES[i];\n    var model = itemModel.getModel([stateName, styleType]);\n    var state = el.ensureState(stateName);\n    // Let it throw error if getterType is not found.\n    state.style = getter ? getter(model) : model[defaultStyleGetterMap[styleType]]();\n  }\n}\n/**\r\n *\r\n * Set element as highlight / downplay dispatcher.\r\n * It will be checked when element received mouseover event or from highlight action.\r\n * It's in change of all highlight/downplay behavior of it's children.\r\n *\r\n * @param el\r\n * @param el.highDownSilentOnTouch\r\n *        In touch device, mouseover event will be trigger on touchstart event\r\n *        (see module:zrender/dom/HandlerProxy). By this mechanism, we can\r\n *        conveniently use hoverStyle when tap on touch screen without additional\r\n *        code for compatibility.\r\n *        But if the chart/component has select feature, which usually also use\r\n *        hoverStyle, there might be conflict between 'select-highlight' and\r\n *        'hover-highlight' especially when roam is enabled (see geo for example).\r\n *        In this case, `highDownSilentOnTouch` should be used to disable\r\n *        hover-highlight on touch device.\r\n * @param asDispatcher If `false`, do not set as \"highDownDispatcher\".\r\n */\nexport function setAsHighDownDispatcher(el, asDispatcher) {\n  var disable = asDispatcher === false;\n  var extendedEl = el;\n  // Make `highDownSilentOnTouch` and `onStateChange` only work after\n  // `setAsHighDownDispatcher` called. Avoid it is modified by user unexpectedly.\n  if (el.highDownSilentOnTouch) {\n    extendedEl.__highDownSilentOnTouch = el.highDownSilentOnTouch;\n  }\n  // Simple optimize, since this method might be\n  // called for each elements of a group in some cases.\n  if (!disable || extendedEl.__highDownDispatcher) {\n    // Emphasis, normal can be triggered manually by API or other components like hover link.\n    // el[method]('emphasis', onElementEmphasisEvent)[method]('normal', onElementNormalEvent);\n    // Also keep previous record.\n    extendedEl.__highByOuter = extendedEl.__highByOuter || 0;\n    extendedEl.__highDownDispatcher = !disable;\n  }\n}\nexport function isHighDownDispatcher(el) {\n  return !!(el && el.__highDownDispatcher);\n}\n/**\r\n * Enable component highlight/downplay features:\r\n * + hover link (within the same name)\r\n * + focus blur in component\r\n */\nexport function enableComponentHighDownFeatures(el, componentModel, componentHighDownName) {\n  var ecData = getECData(el);\n  ecData.componentMainType = componentModel.mainType;\n  ecData.componentIndex = componentModel.componentIndex;\n  ecData.componentHighDownName = componentHighDownName;\n}\n/**\r\n * Support highlight/downplay record on each elements.\r\n * For the case: hover highlight/downplay (legend, visualMap, ...) and\r\n * user triggered highlight/downplay should not conflict.\r\n * Only all of the highlightDigit cleared, return to normal.\r\n * @param {string} highlightKey\r\n * @return {number} highlightDigit\r\n */\nexport function getHighlightDigit(highlightKey) {\n  var highlightDigit = _highlightKeyMap[highlightKey];\n  if (highlightDigit == null && _highlightNextDigit <= 32) {\n    highlightDigit = _highlightKeyMap[highlightKey] = _highlightNextDigit++;\n  }\n  return highlightDigit;\n}\nexport function isSelectChangePayload(payload) {\n  var payloadType = payload.type;\n  return payloadType === SELECT_ACTION_TYPE || payloadType === UNSELECT_ACTION_TYPE || payloadType === TOGGLE_SELECT_ACTION_TYPE;\n}\nexport function isHighDownPayload(payload) {\n  var payloadType = payload.type;\n  return payloadType === HIGHLIGHT_ACTION_TYPE || payloadType === DOWNPLAY_ACTION_TYPE;\n}\nexport function savePathStates(el) {\n  var store = getSavedStates(el);\n  store.normalFill = el.style.fill;\n  store.normalStroke = el.style.stroke;\n  var selectState = el.states.select || {};\n  store.selectFill = selectState.style && selectState.style.fill || null;\n  store.selectStroke = selectState.style && selectState.style.stroke || null;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,OAAO,EAAEC,IAAI,QAAQ,0BAA0B;AACtG,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,cAAc,EAAEC,SAAS,QAAQ,YAAY;AACtD,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,SAASC,KAAK,QAAQ,UAAU;AAChC;AACA,IAAIC,mBAAmB,GAAG,CAAC;AAC3B,IAAIC,gBAAgB,GAAG,CAAC,CAAC;AACzB,IAAIC,cAAc,GAAGL,SAAS,CAAC,CAAC;AAChC,IAAIM,kBAAkB,GAAGN,SAAS,CAAC,CAAC;AACpC,OAAO,IAAIO,kBAAkB,GAAG,CAAC;AACjC,OAAO,IAAIC,gBAAgB,GAAG,CAAC;AAC/B,OAAO,IAAIC,oBAAoB,GAAG,CAAC;AACnC,OAAO,IAAIC,cAAc,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC;AAC1D,OAAO,IAAIC,cAAc,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC;AACpE,OAAO,IAAIC,gBAAgB,GAAG,EAAE;AAChC,OAAO,IAAIC,cAAc,GAAG,CAAC;AAC7B,OAAO,IAAIC,qBAAqB,GAAG,WAAW;AAC9C,OAAO,IAAIC,oBAAoB,GAAG,UAAU;AAC5C,OAAO,IAAIC,kBAAkB,GAAG,QAAQ;AACxC,OAAO,IAAIC,oBAAoB,GAAG,UAAU;AAC5C,OAAO,IAAIC,yBAAyB,GAAG,cAAc;AACrD,SAASC,eAAeA,CAACC,YAAY,EAAE;EACrC,OAAOA,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK,MAAM;AACxD;AACA,SAASC,kBAAkBA,CAACC,EAAE,EAAEC,SAAS,EAAEC,cAAc,EAAE;EACzD,IAAIF,EAAE,CAACG,kBAAkB,IAAI,CAACH,EAAE,CAACI,UAAU,IAAI,CAAC,MAAMF,cAAc,EAAE;IACpEF,EAAE,CAACG,kBAAkB,CAACF,SAAS,CAAC;EAClC;EACAD,EAAE,CAACI,UAAU,GAAGF,cAAc;AAChC;AACA,SAASG,mBAAmBA,CAACL,EAAE,EAAE;EAC/B;EACA;EACAD,kBAAkB,CAACC,EAAE,EAAE,UAAU,EAAEb,oBAAoB,CAAC;AAC1D;AACA,SAASmB,mBAAmBA,CAACN,EAAE,EAAE;EAC/B;EACA;EACA,IAAIA,EAAE,CAACI,UAAU,KAAKjB,oBAAoB,EAAE;IAC1CY,kBAAkB,CAACC,EAAE,EAAE,QAAQ,EAAEf,kBAAkB,CAAC;EACtD;AACF;AACA,SAASsB,eAAeA,CAACP,EAAE,EAAE;EAC3BD,kBAAkB,CAACC,EAAE,EAAE,MAAM,EAAEd,gBAAgB,CAAC;AAClD;AACA,SAASsB,eAAeA,CAACR,EAAE,EAAE;EAC3B,IAAIA,EAAE,CAACI,UAAU,KAAKlB,gBAAgB,EAAE;IACtCa,kBAAkB,CAACC,EAAE,EAAE,QAAQ,EAAEf,kBAAkB,CAAC;EACtD;AACF;AACA,SAASwB,iBAAiBA,CAACT,EAAE,EAAE;EAC7BA,EAAE,CAACU,QAAQ,GAAG,IAAI;AACpB;AACA,SAASC,iBAAiBA,CAACX,EAAE,EAAE;EAC7BA,EAAE,CAACU,QAAQ,GAAG,KAAK;AACrB;AACA,SAASE,kBAAkBA,CAACZ,EAAE,EAAEa,OAAO,EAAEC,WAAW,EAAE;EACpDD,OAAO,CAACb,EAAE,EAAEc,WAAW,CAAC;AAC1B;AACA,SAASC,mBAAmBA,CAACf,EAAE,EAAEa,OAAO,EAAEC,WAAW,EAAE;EACrDF,kBAAkB,CAACZ,EAAE,EAAEa,OAAO,EAAEC,WAAW,CAAC;EAC5Cd,EAAE,CAACgB,OAAO,IAAIhB,EAAE,CAACiB,QAAQ,CAAC,UAAUC,KAAK,EAAE;IACzCN,kBAAkB,CAACM,KAAK,EAAEL,OAAO,EAAEC,WAAW,CAAC;EACjD,CAAC,CAAC;AACJ;AACA,OAAO,SAASK,aAAaA,CAACnB,EAAE,EAAEC,SAAS,EAAE;EAC3C,QAAQA,SAAS;IACf,KAAK,UAAU;MACbD,EAAE,CAACI,UAAU,GAAGjB,oBAAoB;MACpC;IACF,KAAK,QAAQ;MACXa,EAAE,CAACI,UAAU,GAAGnB,kBAAkB;MAClC;IACF,KAAK,MAAM;MACTe,EAAE,CAACI,UAAU,GAAGlB,gBAAgB;MAChC;IACF,KAAK,QAAQ;MACXc,EAAE,CAACU,QAAQ,GAAG,IAAI;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,WAAWA,CAACpB,EAAE,EAAE;EAC9B,IAAIA,EAAE,CAACgB,OAAO,EAAE;IACdhB,EAAE,CAACiB,QAAQ,CAAC,UAAUC,KAAK,EAAE;MAC3BA,KAAK,CAACE,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC,MAAM;IACLpB,EAAE,CAACoB,WAAW,CAAC,CAAC;EAClB;AACF;AACA,SAASC,iBAAiBA,CAACrB,EAAE,EAAEsB,KAAK,EAAEC,WAAW,EAAEC,YAAY,EAAE;EAC/D,IAAIC,KAAK,GAAGzB,EAAE,CAACyB,KAAK;EACpB,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIE,QAAQ,GAAGP,KAAK,CAACK,CAAC,CAAC;IACvB,IAAIG,GAAG,GAAGL,KAAK,CAACI,QAAQ,CAAC;IACzBH,SAAS,CAACG,QAAQ,CAAC,GAAGC,GAAG,IAAI,IAAI,GAAGN,YAAY,IAAIA,YAAY,CAACK,QAAQ,CAAC,GAAGC,GAAG;EAClF;EACA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,EAAE,CAAC+B,SAAS,CAACH,MAAM,EAAED,CAAC,EAAE,EAAE;IAC5C,IAAIK,QAAQ,GAAGhC,EAAE,CAAC+B,SAAS,CAACJ,CAAC,CAAC;IAC9B,IAAIK,QAAQ,CAACC;IACb;IAAA,GACGD,QAAQ,CAACC,qBAAqB,CAAChE,OAAO,CAACsD,WAAW,CAAC,GAAG,CAAC,IAAIS,QAAQ,CAACE,UAAU,KAAK,OAAO,EAAE;MAC7FF,QAAQ,CAACG,MAAM,CAACT,SAAS,EAAEJ,KAAK,CAAC;IACnC;EACF;EACA,OAAOI,SAAS;AAClB;AACA,SAASU,0BAA0BA,CAACpC,EAAE,EAAEC,SAAS,EAAEoC,YAAY,EAAEC,KAAK,EAAE;EACtE,IAAIC,SAAS,GAAGF,YAAY,IAAIpE,OAAO,CAACoE,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC;EACpE,IAAIG,MAAM,GAAG,KAAK;EAClB,IAAIxC,EAAE,YAAYrB,IAAI,EAAE;IACtB,IAAI8D,KAAK,GAAG1D,cAAc,CAACiB,EAAE,CAAC;IAC9B,IAAI0C,QAAQ,GAAGH,SAAS,GAAGE,KAAK,CAACE,UAAU,IAAIF,KAAK,CAACG,UAAU,GAAGH,KAAK,CAACG,UAAU;IAClF,IAAIC,UAAU,GAAGN,SAAS,GAAGE,KAAK,CAACK,YAAY,IAAIL,KAAK,CAACM,YAAY,GAAGN,KAAK,CAACM,YAAY;IAC1F,IAAIlD,eAAe,CAAC6C,QAAQ,CAAC,IAAI7C,eAAe,CAACgD,UAAU,CAAC,EAAE;MAC5DP,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC;MACnB,IAAIU,aAAa,GAAGV,KAAK,CAACb,KAAK,IAAI,CAAC,CAAC;MACrC;MACA,IAAIuB,aAAa,CAACC,IAAI,KAAK,SAAS,EAAE;QACpCT,MAAM,GAAG,IAAI;QACbF,KAAK,GAAGtE,MAAM,CAAC,CAAC,CAAC,EAAEsE,KAAK,CAAC;QACzBU,aAAa,GAAGhF,MAAM,CAAC,CAAC,CAAC,EAAEgF,aAAa,CAAC;QACzCA,aAAa,CAACC,IAAI,GAAGP,QAAQ;MAC/B;MACA;MAAA,KACK,IAAI,CAAC7C,eAAe,CAACmD,aAAa,CAACC,IAAI,CAAC,IAAIpD,eAAe,CAAC6C,QAAQ,CAAC,EAAE;QAC1EF,MAAM,GAAG,IAAI;QACb;QACAF,KAAK,GAAGtE,MAAM,CAAC,CAAC,CAAC,EAAEsE,KAAK,CAAC;QACzBU,aAAa,GAAGhF,MAAM,CAAC,CAAC,CAAC,EAAEgF,aAAa,CAAC;QACzC;QACAA,aAAa,CAACC,IAAI,GAAGzE,SAAS,CAACkE,QAAQ,CAAC;MAC1C;MACA;MAAA,KACK,IAAI,CAAC7C,eAAe,CAACmD,aAAa,CAACE,MAAM,CAAC,IAAIrD,eAAe,CAACgD,UAAU,CAAC,EAAE;QAC9E,IAAI,CAACL,MAAM,EAAE;UACXF,KAAK,GAAGtE,MAAM,CAAC,CAAC,CAAC,EAAEsE,KAAK,CAAC;UACzBU,aAAa,GAAGhF,MAAM,CAAC,CAAC,CAAC,EAAEgF,aAAa,CAAC;QAC3C;QACAA,aAAa,CAACE,MAAM,GAAG1E,SAAS,CAACqE,UAAU,CAAC;MAC9C;MACAP,KAAK,CAACb,KAAK,GAAGuB,aAAa;IAC7B;EACF;EACA,IAAIV,KAAK,EAAE;IACT;IACA,IAAIA,KAAK,CAACa,EAAE,IAAI,IAAI,EAAE;MACpB,IAAI,CAACX,MAAM,EAAE;QACXF,KAAK,GAAGtE,MAAM,CAAC,CAAC,CAAC,EAAEsE,KAAK,CAAC;MAC3B;MACA,IAAIc,cAAc,GAAGpD,EAAE,CAACoD,cAAc;MACtCd,KAAK,CAACa,EAAE,GAAGnD,EAAE,CAACmD,EAAE,IAAIC,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAG9D,gBAAgB,CAAC;IACjF;EACF;EACA,OAAOgD,KAAK;AACd;AACA,SAASe,wBAAwBA,CAACrD,EAAE,EAAEC,SAAS,EAAEqC,KAAK,EAAE;EACtD;EACA,IAAIA,KAAK,EAAE;IACT;IACA,IAAIA,KAAK,CAACa,EAAE,IAAI,IAAI,EAAE;MACpBb,KAAK,GAAGtE,MAAM,CAAC,CAAC,CAAC,EAAEsE,KAAK,CAAC;MACzB,IAAIgB,YAAY,GAAGtD,EAAE,CAACsD,YAAY;MAClChB,KAAK,CAACa,EAAE,GAAGnD,EAAE,CAACmD,EAAE,IAAIG,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAG/D,cAAc,CAAC;IAC3E;EACF;EACA,OAAO+C,KAAK;AACd;AACA,SAASiB,sBAAsBA,CAACvD,EAAE,EAAEC,SAAS,EAAEqC,KAAK,EAAE;EACpD,IAAIkB,OAAO,GAAGvF,OAAO,CAAC+B,EAAE,CAACyD,aAAa,EAAExD,SAAS,CAAC,IAAI,CAAC;EACvD,IAAIyD,cAAc,GAAG1D,EAAE,CAACyB,KAAK,CAACkC,OAAO;EACrC,IAAIjC,SAAS,GAAG,CAAC8B,OAAO,GAAGnC,iBAAiB,CAACrB,EAAE,EAAE,CAAC,SAAS,CAAC,EAAEC,SAAS,EAAE;IACvE0D,OAAO,EAAE;EACX,CAAC,CAAC,GAAG,IAAI;EACTrB,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC;EACnB,IAAIsB,SAAS,GAAGtB,KAAK,CAACb,KAAK,IAAI,CAAC,CAAC;EACjC,IAAImC,SAAS,CAACD,OAAO,IAAI,IAAI,EAAE;IAC7B;IACArB,KAAK,GAAGtE,MAAM,CAAC,CAAC,CAAC,EAAEsE,KAAK,CAAC;IACzBsB,SAAS,GAAG5F,MAAM,CAAC;MACjB;MACA2F,OAAO,EAAEH,OAAO,GAAGE,cAAc,GAAGhC,SAAS,CAACiC,OAAO,GAAG;IAC1D,CAAC,EAAEC,SAAS,CAAC;IACbtB,KAAK,CAACb,KAAK,GAAGmC,SAAS;EACzB;EACA,OAAOtB,KAAK;AACd;AACA,SAASuB,iBAAiBA,CAAC5D,SAAS,EAAEoC,YAAY,EAAE;EAClD,IAAIC,KAAK,GAAG,IAAI,CAACwB,MAAM,CAAC7D,SAAS,CAAC;EAClC,IAAI,IAAI,CAACwB,KAAK,EAAE;IACd,IAAIxB,SAAS,KAAK,UAAU,EAAE;MAC5B,OAAOmC,0BAA0B,CAAC,IAAI,EAAEnC,SAAS,EAAEoC,YAAY,EAAEC,KAAK,CAAC;IACzE,CAAC,MAAM,IAAIrC,SAAS,KAAK,MAAM,EAAE;MAC/B,OAAOsD,sBAAsB,CAAC,IAAI,EAAEtD,SAAS,EAAEqC,KAAK,CAAC;IACvD,CAAC,MAAM,IAAIrC,SAAS,KAAK,QAAQ,EAAE;MACjC,OAAOoD,wBAAwB,CAAC,IAAI,EAAEpD,SAAS,EAAEqC,KAAK,CAAC;IACzD;EACF;EACA,OAAOA,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyB,oBAAoBA,CAAC/D,EAAE,EAAE;EACvCA,EAAE,CAACgE,UAAU,GAAGH,iBAAiB;EACjC,IAAII,WAAW,GAAGjE,EAAE,CAACkE,cAAc,CAAC,CAAC;EACrC,IAAIC,SAAS,GAAGnE,EAAE,CAACoE,gBAAgB,CAAC,CAAC;EACrC,IAAIH,WAAW,EAAE;IACfA,WAAW,CAACD,UAAU,GAAGH,iBAAiB;EAC5C;EACA,IAAIM,SAAS,EAAE;IACbA,SAAS,CAACH,UAAU,GAAGH,iBAAiB;EAC1C;AACF;AACA,OAAO,SAASQ,0BAA0BA,CAACrE,EAAE,EAAEsE,CAAC,EAAE;EAChD,CAACC,YAAY,CAACvE,EAAE,EAAEsE,CAAC;EACnB;EAAA,GACG,CAACtE,EAAE,CAACwE,aAAa,IAAIzD,mBAAmB,CAACf,EAAE,EAAEK,mBAAmB,CAAC;AACtE;AACA,OAAO,SAASoE,yBAAyBA,CAACzE,EAAE,EAAEsE,CAAC,EAAE;EAC/C,CAACC,YAAY,CAACvE,EAAE,EAAEsE,CAAC;EACnB;EAAA,GACG,CAACtE,EAAE,CAACwE,aAAa,IAAIzD,mBAAmB,CAACf,EAAE,EAAEM,mBAAmB,CAAC;AACtE;AACA,OAAO,SAASoE,aAAaA,CAAC1E,EAAE,EAAE2E,cAAc,EAAE;EAChD3E,EAAE,CAACwE,aAAa,IAAI,CAAC,KAAKG,cAAc,IAAI,CAAC,CAAC;EAC9C5D,mBAAmB,CAACf,EAAE,EAAEK,mBAAmB,CAAC;AAC9C;AACA,OAAO,SAASuE,aAAaA,CAAC5E,EAAE,EAAE2E,cAAc,EAAE;EAChD,EAAE3E,EAAE,CAACwE,aAAa,IAAI,EAAE,CAAC,KAAKG,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI5D,mBAAmB,CAACf,EAAE,EAAEM,mBAAmB,CAAC;AACtG;AACA,OAAO,SAASuE,SAASA,CAAC7E,EAAE,EAAE;EAC5Be,mBAAmB,CAACf,EAAE,EAAEO,eAAe,CAAC;AAC1C;AACA,OAAO,SAASuE,SAASA,CAAC9E,EAAE,EAAE;EAC5Be,mBAAmB,CAACf,EAAE,EAAEQ,eAAe,CAAC;AAC1C;AACA,OAAO,SAASuE,WAAWA,CAAC/E,EAAE,EAAE;EAC9Be,mBAAmB,CAACf,EAAE,EAAES,iBAAiB,CAAC;AAC5C;AACA,OAAO,SAASuE,WAAWA,CAAChF,EAAE,EAAE;EAC9Be,mBAAmB,CAACf,EAAE,EAAEW,iBAAiB,CAAC;AAC5C;AACA,SAAS4D,YAAYA,CAACvE,EAAE,EAAEsE,CAAC,EAAE;EAC3B,OAAOtE,EAAE,CAACiF,uBAAuB,IAAIX,CAAC,CAACY,SAAS;AAClD;AACA,OAAO,SAASC,YAAYA,CAACC,GAAG,EAAE;EAChC,IAAIC,KAAK,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC;EAC1B,IAAIC,kBAAkB,GAAG,EAAE;EAC3B,IAAIC,iBAAiB,GAAG,EAAE;EAC1BH,KAAK,CAACI,aAAa,CAAC,UAAUC,aAAa,EAAEC,cAAc,EAAE;IAC3D,IAAIC,eAAe,GAAG5G,kBAAkB,CAAC2G,cAAc,CAAC;IACxD,IAAIE,QAAQ,GAAGH,aAAa,KAAK,QAAQ;IACzC,IAAII,IAAI,GAAGD,QAAQ,GAAGT,GAAG,CAACW,oBAAoB,CAACJ,cAAc,CAAC,GAAGP,GAAG,CAACY,uBAAuB,CAACL,cAAc,CAAC;IAC5G,CAACE,QAAQ,IAAIL,iBAAiB,CAACS,IAAI,CAACH,IAAI,CAAC;IACzC,IAAIF,eAAe,CAACM,QAAQ,EAAE;MAC5B;MACAJ,IAAI,CAACK,KAAK,CAAClF,QAAQ,CAAC,UAAUC,KAAK,EAAE;QACnCV,eAAe,CAACU,KAAK,CAAC;MACxB,CAAC,CAAC;MACF2E,QAAQ,IAAIN,kBAAkB,CAACU,IAAI,CAACN,cAAc,CAAC;IACrD;IACAC,eAAe,CAACM,QAAQ,GAAG,KAAK;EAClC,CAAC,CAAC;EACF5H,IAAI,CAACkH,iBAAiB,EAAE,UAAUM,IAAI,EAAE;IACtC,IAAIA,IAAI,IAAIA,IAAI,CAACM,gBAAgB,EAAE;MACjCN,IAAI,CAACM,gBAAgB,CAACb,kBAAkB,EAAE,KAAK,EAAEF,KAAK,CAAC;IACzD;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASgB,UAAUA,CAACC,iBAAiB,EAAEC,KAAK,EAAEC,SAAS,EAAEpB,GAAG,EAAE;EACnE,IAAIqB,OAAO,GAAGrB,GAAG,CAACE,QAAQ,CAAC,CAAC;EAC5BkB,SAAS,GAAGA,SAAS,IAAI,kBAAkB;EAC3C,SAASE,kBAAkBA,CAACC,IAAI,EAAEC,WAAW,EAAE;IAC7C,KAAK,IAAIjF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiF,WAAW,CAAChF,MAAM,EAAED,CAAC,EAAE,EAAE;MAC3C,IAAIkF,MAAM,GAAGF,IAAI,CAACG,gBAAgB,CAACF,WAAW,CAACjF,CAAC,CAAC,CAAC;MAClDkF,MAAM,IAAI/B,SAAS,CAAC+B,MAAM,CAAC;IAC7B;EACF;EACA,IAAIP,iBAAiB,IAAI,IAAI,EAAE;IAC7B;EACF;EACA,IAAI,CAACC,KAAK,IAAIA,KAAK,KAAK,MAAM,EAAE;IAC9B;EACF;EACA,IAAIQ,iBAAiB,GAAGN,OAAO,CAACO,gBAAgB,CAACV,iBAAiB,CAAC;EACnE,IAAIW,cAAc,GAAGF,iBAAiB,CAACG,gBAAgB;EACvD,IAAID,cAAc,IAAIA,cAAc,CAACE,MAAM,EAAE;IAC3CF,cAAc,GAAGA,cAAc,CAACE,MAAM;EACxC;EACA,IAAIC,aAAa,GAAG,EAAE;EACtBX,OAAO,CAACY,UAAU,CAAC,UAAUC,WAAW,EAAE;IACxC,IAAIC,UAAU,GAAGR,iBAAiB,KAAKO,WAAW;IAClD,IAAIE,QAAQ,GAAGF,WAAW,CAACJ,gBAAgB;IAC3C,IAAIM,QAAQ,IAAIA,QAAQ,CAACL,MAAM,EAAE;MAC/BK,QAAQ,GAAGA,QAAQ,CAACL,MAAM;IAC5B;IACA,IAAIM,YAAY,GAAGD,QAAQ,IAAIP,cAAc,GAAGO,QAAQ,KAAKP,cAAc,GAAGM,UAAU,CAAC,CAAC;IAC1F,IAAI;IACJ;IACAf,SAAS,KAAK,QAAQ,IAAI,CAACe;IAC3B;IAAA,GACGf,SAAS,KAAK,kBAAkB,IAAI,CAACiB;IACxC;IAAA,GACGlB,KAAK,KAAK,QAAQ,IAAIgB;IACzB;IAAA,CACC,EAAE;MACD,IAAIzB,IAAI,GAAGV,GAAG,CAACW,oBAAoB,CAACuB,WAAW,CAAC;MAChDxB,IAAI,CAACK,KAAK,CAAClF,QAAQ,CAAC,UAAUC,KAAK,EAAE;QACnC;QACA;QACA;QACA;QACA,IAAIA,KAAK,CAACsD,aAAa,IAAI+C,UAAU,IAAIhB,KAAK,KAAK,MAAM,EAAE;UACzD;QACF;QACAhG,eAAe,CAACW,KAAK,CAAC;MACxB,CAAC,CAAC;MACF,IAAIhD,WAAW,CAACqI,KAAK,CAAC,EAAE;QACtBG,kBAAkB,CAACY,WAAW,CAACI,OAAO,CAAC,CAAC,EAAEnB,KAAK,CAAC;MAClD,CAAC,MAAM,IAAIpI,QAAQ,CAACoI,KAAK,CAAC,EAAE;QAC1B,IAAIoB,SAAS,GAAGvJ,IAAI,CAACmI,KAAK,CAAC;QAC3B,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAAC/F,MAAM,EAAEgG,CAAC,EAAE,EAAE;UACzClB,kBAAkB,CAACY,WAAW,CAACI,OAAO,CAACC,SAAS,CAACC,CAAC,CAAC,CAAC,EAAErB,KAAK,CAACoB,SAAS,CAACC,CAAC,CAAC,CAAC,CAAC;QAC5E;MACF;MACAR,aAAa,CAACnB,IAAI,CAACqB,WAAW,CAAC;MAC/BtI,kBAAkB,CAACsI,WAAW,CAAC,CAACpB,QAAQ,GAAG,IAAI;IACjD;EACF,CAAC,CAAC;EACFO,OAAO,CAAChB,aAAa,CAAC,UAAUC,aAAa,EAAEC,cAAc,EAAE;IAC7D,IAAID,aAAa,KAAK,QAAQ,EAAE;MAC9B;IACF;IACA,IAAII,IAAI,GAAGV,GAAG,CAACY,uBAAuB,CAACL,cAAc,CAAC;IACtD,IAAIG,IAAI,IAAIA,IAAI,CAACM,gBAAgB,EAAE;MACjCN,IAAI,CAACM,gBAAgB,CAACgB,aAAa,EAAE,IAAI,EAAEX,OAAO,CAAC;IACrD;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASoB,aAAaA,CAACC,iBAAiB,EAAEC,cAAc,EAAE3C,GAAG,EAAE;EACpE,IAAI0C,iBAAiB,IAAI,IAAI,IAAIC,cAAc,IAAI,IAAI,EAAE;IACvD;EACF;EACA,IAAIpC,cAAc,GAAGP,GAAG,CAACE,QAAQ,CAAC,CAAC,CAAC0C,YAAY,CAACF,iBAAiB,EAAEC,cAAc,CAAC;EACnF,IAAI,CAACpC,cAAc,EAAE;IACnB;EACF;EACA3G,kBAAkB,CAAC2G,cAAc,CAAC,CAACO,QAAQ,GAAG,IAAI;EAClD,IAAIJ,IAAI,GAAGV,GAAG,CAACY,uBAAuB,CAACL,cAAc,CAAC;EACtD,IAAI,CAACG,IAAI,IAAI,CAACA,IAAI,CAACmC,gBAAgB,EAAE;IACnC;EACF;EACAnC,IAAI,CAACK,KAAK,CAAClF,QAAQ,CAAC,UAAUC,KAAK,EAAE;IACnCX,eAAe,CAACW,KAAK,CAAC;EACxB,CAAC,CAAC;AACJ;AACA,OAAO,SAASgH,8BAA8BA,CAACZ,WAAW,EAAEa,OAAO,EAAE/C,GAAG,EAAE;EACxE,IAAIgD,WAAW,GAAGd,WAAW,CAACc,WAAW;EACzC,IAAIzB,IAAI,GAAGW,WAAW,CAACI,OAAO,CAACS,OAAO,CAACE,QAAQ,CAAC;EAChD,IAAI,CAAC1B,IAAI,EAAE;IACT,IAAI2B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC5J,KAAK,CAAC,mBAAmB,GAAGuJ,OAAO,CAACE,QAAQ,CAAC;IAC/C;IACA;EACF;EACA,IAAII,SAAS,GAAGhK,cAAc,CAACkI,IAAI,EAAEwB,OAAO,CAAC;EAC7C;EACAM,SAAS,GAAG,CAACpK,OAAO,CAACoK,SAAS,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,KAAK,CAAC;EAChE,IAAIzI,EAAE,GAAG2G,IAAI,CAACG,gBAAgB,CAAC2B,SAAS,CAAC;EACzC,IAAI,CAACzI,EAAE,EAAE;IACP,IAAI0I,KAAK,GAAG/B,IAAI,CAAC+B,KAAK,CAAC,CAAC;IACxB,IAAIC,OAAO,GAAG,CAAC;IACf;IACA,OAAO,CAAC3I,EAAE,IAAI2I,OAAO,GAAGD,KAAK,EAAE;MAC7B1I,EAAE,GAAG2G,IAAI,CAACG,gBAAgB,CAAC6B,OAAO,EAAE,CAAC;IACvC;EACF;EACA,IAAI3I,EAAE,EAAE;IACN,IAAI4I,MAAM,GAAGrK,SAAS,CAACyB,EAAE,CAAC;IAC1BqG,UAAU,CAAC+B,WAAW,EAAEQ,MAAM,CAACrC,KAAK,EAAEqC,MAAM,CAACpC,SAAS,EAAEpB,GAAG,CAAC;EAC9D,CAAC,MAAM;IACL;IACA;IACA,IAAIyD,OAAO,GAAGvB,WAAW,CAACwB,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IACpD,IAAItC,SAAS,GAAGc,WAAW,CAACwB,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;IAC1D,IAAID,OAAO,IAAI,IAAI,EAAE;MACnBxC,UAAU,CAAC+B,WAAW,EAAES,OAAO,EAAErC,SAAS,EAAEpB,GAAG,CAAC;IAClD;EACF;AACF;AACA,OAAO,SAAS2D,gCAAgCA,CAACjB,iBAAiB,EAAEC,cAAc,EAAEiB,IAAI,EAAE5D,GAAG,EAAE;EAC7F,IAAI6D,GAAG,GAAG;IACRC,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE;EACf,CAAC;EACD,IAAIrB,iBAAiB,IAAI,IAAI,IAAIA,iBAAiB,KAAK,QAAQ,IAAIC,cAAc,IAAI,IAAI,IAAIiB,IAAI,IAAI,IAAI,EAAE;IACzG,OAAOC,GAAG;EACZ;EACA,IAAItD,cAAc,GAAGP,GAAG,CAACE,QAAQ,CAAC,CAAC,CAAC0C,YAAY,CAACF,iBAAiB,EAAEC,cAAc,CAAC;EACnF,IAAI,CAACpC,cAAc,EAAE;IACnB,OAAOsD,GAAG;EACZ;EACA,IAAInD,IAAI,GAAGV,GAAG,CAACY,uBAAuB,CAACL,cAAc,CAAC;EACtD,IAAI,CAACG,IAAI,IAAI,CAACA,IAAI,CAACsD,uBAAuB,EAAE;IAC1C,OAAOH,GAAG;EACZ;EACA,IAAIE,WAAW,GAAGrD,IAAI,CAACsD,uBAAuB,CAACJ,IAAI,CAAC;EACpD;EACA;EACA,IAAIE,SAAS;EACb,KAAK,IAAIvH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwH,WAAW,CAACvH,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C,IAAI2G,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACa,oBAAoB,CAACF,WAAW,CAACxH,CAAC,CAAC,CAAC,EAAE;MAClF/C,KAAK,CAAC,oCAAoC,CAAC;IAC7C;IACA,IAAIL,SAAS,CAAC4K,WAAW,CAACxH,CAAC,CAAC,CAAC,CAAC4E,KAAK,KAAK,MAAM,EAAE;MAC9C2C,SAAS,GAAG,IAAI;MAChB;IACF;EACF;EACA,OAAO;IACLA,SAAS,EAAEA,SAAS;IACpBC,WAAW,EAAEA;EACf,CAAC;AACH;AACA,OAAO,SAASG,gCAAgCA,CAACC,UAAU,EAAEjF,CAAC,EAAEc,GAAG,EAAE;EACnE,IAAIkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACa,oBAAoB,CAACE,UAAU,CAAC,EAAE;IAC9E3K,KAAK,CAAC,oCAAoC,CAAC;EAC7C;EACA,IAAIgK,MAAM,GAAGrK,SAAS,CAACgL,UAAU,CAAC;EAClC,IAAIC,EAAE,GAAGT,gCAAgC,CAACH,MAAM,CAACd,iBAAiB,EAAEc,MAAM,CAACb,cAAc,EAAEa,MAAM,CAACa,qBAAqB,EAAErE,GAAG,CAAC;IAC3H+D,WAAW,GAAGK,EAAE,CAACL,WAAW;IAC5BD,SAAS,GAAGM,EAAE,CAACN,SAAS;EAC1B;EACA;EACA,IAAIC,WAAW,EAAE;IACf,IAAID,SAAS,EAAE;MACbrB,aAAa,CAACe,MAAM,CAACd,iBAAiB,EAAEc,MAAM,CAACb,cAAc,EAAE3C,GAAG,CAAC;IACrE;IACA9G,IAAI,CAAC6K,WAAW,EAAE,UAAUI,UAAU,EAAE;MACtC,OAAOlF,0BAA0B,CAACkF,UAAU,EAAEjF,CAAC,CAAC;IAClD,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACA;IACA+B,UAAU,CAACuC,MAAM,CAACR,WAAW,EAAEQ,MAAM,CAACrC,KAAK,EAAEqC,MAAM,CAACpC,SAAS,EAAEpB,GAAG,CAAC;IACnE,IAAIwD,MAAM,CAACrC,KAAK,KAAK,MAAM,EAAE;MAC3BsB,aAAa,CAACe,MAAM,CAACd,iBAAiB,EAAEc,MAAM,CAACb,cAAc,EAAE3C,GAAG,CAAC;IACrE;IACA;IACA;IACA;IACAf,0BAA0B,CAACkF,UAAU,EAAEjF,CAAC,CAAC;EAC3C;AACF;AACA,OAAO,SAASoF,+BAA+BA,CAACH,UAAU,EAAEjF,CAAC,EAAEc,GAAG,EAAE;EAClE,IAAIkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACa,oBAAoB,CAACE,UAAU,CAAC,EAAE;IAC9E3K,KAAK,CAAC,oCAAoC,CAAC;EAC7C;EACAuG,YAAY,CAACC,GAAG,CAAC;EACjB,IAAIwD,MAAM,GAAGrK,SAAS,CAACgL,UAAU,CAAC;EAClC,IAAIJ,WAAW,GAAGJ,gCAAgC,CAACH,MAAM,CAACd,iBAAiB,EAAEc,MAAM,CAACb,cAAc,EAAEa,MAAM,CAACa,qBAAqB,EAAErE,GAAG,CAAC,CAAC+D,WAAW;EAClJ,IAAIA,WAAW,EAAE;IACf7K,IAAI,CAAC6K,WAAW,EAAE,UAAUI,UAAU,EAAE;MACtC,OAAO9E,yBAAyB,CAAC8E,UAAU,EAAEjF,CAAC,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC,MAAM;IACLG,yBAAyB,CAAC8E,UAAU,EAAEjF,CAAC,CAAC;EAC1C;AACF;AACA,OAAO,SAASqF,0BAA0BA,CAACrC,WAAW,EAAEa,OAAO,EAAE/C,GAAG,EAAE;EACpE,IAAI,CAACwE,qBAAqB,CAACzB,OAAO,CAAC,EAAE;IACnC;EACF;EACA,IAAIE,QAAQ,GAAGF,OAAO,CAACE,QAAQ;EAC/B,IAAI1B,IAAI,GAAGW,WAAW,CAACI,OAAO,CAACW,QAAQ,CAAC;EACxC,IAAII,SAAS,GAAGhK,cAAc,CAACkI,IAAI,EAAEwB,OAAO,CAAC;EAC7C,IAAI,CAAC9J,OAAO,CAACoK,SAAS,CAAC,EAAE;IACvBA,SAAS,GAAG,CAACA,SAAS,CAAC;EACzB;EACAnB,WAAW,CAACa,OAAO,CAAC0B,IAAI,KAAKjK,yBAAyB,GAAG,cAAc,GAAGuI,OAAO,CAAC0B,IAAI,KAAKnK,kBAAkB,GAAG,QAAQ,GAAG,UAAU,CAAC,CAAC+I,SAAS,EAAEJ,QAAQ,CAAC;AAC7J;AACA,OAAO,SAASyB,4BAA4BA,CAACxC,WAAW,EAAE;EACxD,IAAIyC,OAAO,GAAGzC,WAAW,CAAC0C,UAAU,CAAC,CAAC;EACtC1L,IAAI,CAACyL,OAAO,EAAE,UAAUP,EAAE,EAAE;IAC1B,IAAI7C,IAAI,GAAG6C,EAAE,CAAC7C,IAAI;MAChBkD,IAAI,GAAGL,EAAE,CAACK,IAAI;IAChBlD,IAAI,CAACsD,iBAAiB,CAAC,UAAUjK,EAAE,EAAEkK,GAAG,EAAE;MACxC5C,WAAW,CAAC6C,UAAU,CAACD,GAAG,EAAEL,IAAI,CAAC,GAAG9E,WAAW,CAAC/E,EAAE,CAAC,GAAGgF,WAAW,CAAChF,EAAE,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,OAAO,SAASoK,qBAAqBA,CAAC3D,OAAO,EAAE;EAC7C,IAAIwC,GAAG,GAAG,EAAE;EACZxC,OAAO,CAACY,UAAU,CAAC,UAAUC,WAAW,EAAE;IACxC,IAAIyC,OAAO,GAAGzC,WAAW,CAAC0C,UAAU,CAAC,CAAC;IACtC1L,IAAI,CAACyL,OAAO,EAAE,UAAUP,EAAE,EAAE;MAC1B,IAAI7C,IAAI,GAAG6C,EAAE,CAAC7C,IAAI;QAChBkD,IAAI,GAAGL,EAAE,CAACK,IAAI;MAChB,IAAIjD,WAAW,GAAGU,WAAW,CAAC+C,sBAAsB,CAAC,CAAC;MACtD,IAAIzD,WAAW,CAAChF,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI0I,IAAI,GAAG;UACT7B,SAAS,EAAE7B,WAAW;UACtBwB,WAAW,EAAEd,WAAW,CAACc;QAC3B,CAAC;QACD,IAAIyB,IAAI,IAAI,IAAI,EAAE;UAChBS,IAAI,CAACjC,QAAQ,GAAGwB,IAAI;QACtB;QACAZ,GAAG,CAAChD,IAAI,CAACqE,IAAI,CAAC;MAChB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOrB,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsB,mBAAmBA,CAACvK,EAAE,EAAEuG,KAAK,EAAEC,SAAS,EAAE;EACxDgE,uBAAuB,CAACxK,EAAE,EAAE,IAAI,CAAC;EACjCe,mBAAmB,CAACf,EAAE,EAAE+D,oBAAoB,CAAC;EAC7C0G,gBAAgB,CAACzK,EAAE,EAAEuG,KAAK,EAAEC,SAAS,CAAC;AACxC;AACA,OAAO,SAASkE,oBAAoBA,CAAC1K,EAAE,EAAE;EACvCwK,uBAAuB,CAACxK,EAAE,EAAE,KAAK,CAAC;AACpC;AACA,OAAO,SAAS2K,mBAAmBA,CAAC3K,EAAE,EAAEuG,KAAK,EAAEC,SAAS,EAAEoE,UAAU,EAAE;EACpEA,UAAU,GAAGF,oBAAoB,CAAC1K,EAAE,CAAC,GAAGuK,mBAAmB,CAACvK,EAAE,EAAEuG,KAAK,EAAEC,SAAS,CAAC;AACnF;AACA,OAAO,SAASiE,gBAAgBA,CAACzK,EAAE,EAAEuG,KAAK,EAAEC,SAAS,EAAE;EACrD,IAAIoC,MAAM,GAAGrK,SAAS,CAACyB,EAAE,CAAC;EAC1B,IAAIuG,KAAK,IAAI,IAAI,EAAE;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACAqC,MAAM,CAACrC,KAAK,GAAGA,KAAK;IACpBqC,MAAM,CAACpC,SAAS,GAAGA,SAAS;IAC5B;EACF,CAAC,MAAM,IAAIoC,MAAM,CAACrC,KAAK,EAAE;IACvBqC,MAAM,CAACrC,KAAK,GAAG,IAAI;EACrB;AACF;AACA,IAAIsE,YAAY,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC;AACjD,IAAIC,qBAAqB,GAAG;EAC1BC,SAAS,EAAE,cAAc;EACzBC,SAAS,EAAE,cAAc;EACzBC,SAAS,EAAE;AACb,CAAC;AACD;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAAClL,EAAE,EAAEmL,SAAS,EAAEC,SAAS;AACjE;AACAC,MAAM,EAAE;EACND,SAAS,GAAGA,SAAS,IAAI,WAAW;EACpC,KAAK,IAAIzJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkJ,YAAY,CAACjJ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC5C,IAAI1B,SAAS,GAAG4K,YAAY,CAAClJ,CAAC,CAAC;IAC/B,IAAI0D,KAAK,GAAG8F,SAAS,CAAC7F,QAAQ,CAAC,CAACrF,SAAS,EAAEmL,SAAS,CAAC,CAAC;IACtD,IAAI9I,KAAK,GAAGtC,EAAE,CAACsL,WAAW,CAACrL,SAAS,CAAC;IACrC;IACAqC,KAAK,CAACb,KAAK,GAAG4J,MAAM,GAAGA,MAAM,CAAChG,KAAK,CAAC,GAAGA,KAAK,CAACyF,qBAAqB,CAACM,SAAS,CAAC,CAAC,CAAC,CAAC;EAClF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASZ,uBAAuBA,CAACxK,EAAE,EAAEuL,YAAY,EAAE;EACxD,IAAIC,OAAO,GAAGD,YAAY,KAAK,KAAK;EACpC,IAAIE,UAAU,GAAGzL,EAAE;EACnB;EACA;EACA,IAAIA,EAAE,CAAC0L,qBAAqB,EAAE;IAC5BD,UAAU,CAACxG,uBAAuB,GAAGjF,EAAE,CAAC0L,qBAAqB;EAC/D;EACA;EACA;EACA,IAAI,CAACF,OAAO,IAAIC,UAAU,CAACE,oBAAoB,EAAE;IAC/C;IACA;IACA;IACAF,UAAU,CAACjH,aAAa,GAAGiH,UAAU,CAACjH,aAAa,IAAI,CAAC;IACxDiH,UAAU,CAACE,oBAAoB,GAAG,CAACH,OAAO;EAC5C;AACF;AACA,OAAO,SAASnC,oBAAoBA,CAACrJ,EAAE,EAAE;EACvC,OAAO,CAAC,EAAEA,EAAE,IAAIA,EAAE,CAAC2L,oBAAoB,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,+BAA+BA,CAAC5L,EAAE,EAAE2F,cAAc,EAAE8D,qBAAqB,EAAE;EACzF,IAAIb,MAAM,GAAGrK,SAAS,CAACyB,EAAE,CAAC;EAC1B4I,MAAM,CAACd,iBAAiB,GAAGnC,cAAc,CAACkG,QAAQ;EAClDjD,MAAM,CAACb,cAAc,GAAGpC,cAAc,CAACoC,cAAc;EACrDa,MAAM,CAACa,qBAAqB,GAAGA,qBAAqB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqC,iBAAiBA,CAACC,YAAY,EAAE;EAC9C,IAAIpH,cAAc,GAAG7F,gBAAgB,CAACiN,YAAY,CAAC;EACnD,IAAIpH,cAAc,IAAI,IAAI,IAAI9F,mBAAmB,IAAI,EAAE,EAAE;IACvD8F,cAAc,GAAG7F,gBAAgB,CAACiN,YAAY,CAAC,GAAGlN,mBAAmB,EAAE;EACzE;EACA,OAAO8F,cAAc;AACvB;AACA,OAAO,SAASiF,qBAAqBA,CAACzB,OAAO,EAAE;EAC7C,IAAI6D,WAAW,GAAG7D,OAAO,CAAC0B,IAAI;EAC9B,OAAOmC,WAAW,KAAKtM,kBAAkB,IAAIsM,WAAW,KAAKrM,oBAAoB,IAAIqM,WAAW,KAAKpM,yBAAyB;AAChI;AACA,OAAO,SAASqM,iBAAiBA,CAAC9D,OAAO,EAAE;EACzC,IAAI6D,WAAW,GAAG7D,OAAO,CAAC0B,IAAI;EAC9B,OAAOmC,WAAW,KAAKxM,qBAAqB,IAAIwM,WAAW,KAAKvM,oBAAoB;AACtF;AACA,OAAO,SAASyM,cAAcA,CAAClM,EAAE,EAAE;EACjC,IAAIyC,KAAK,GAAG1D,cAAc,CAACiB,EAAE,CAAC;EAC9ByC,KAAK,CAACG,UAAU,GAAG5C,EAAE,CAACyB,KAAK,CAACwB,IAAI;EAChCR,KAAK,CAACM,YAAY,GAAG/C,EAAE,CAACyB,KAAK,CAACyB,MAAM;EACpC,IAAIiJ,WAAW,GAAGnM,EAAE,CAAC8D,MAAM,CAACsI,MAAM,IAAI,CAAC,CAAC;EACxC3J,KAAK,CAACE,UAAU,GAAGwJ,WAAW,CAAC1K,KAAK,IAAI0K,WAAW,CAAC1K,KAAK,CAACwB,IAAI,IAAI,IAAI;EACtER,KAAK,CAACK,YAAY,GAAGqJ,WAAW,CAAC1K,KAAK,IAAI0K,WAAW,CAAC1K,KAAK,CAACyB,MAAM,IAAI,IAAI;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}