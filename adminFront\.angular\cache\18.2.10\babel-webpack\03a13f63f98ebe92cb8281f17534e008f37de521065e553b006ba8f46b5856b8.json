{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/helper/petternHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nimport * as i12 from \"../../../@theme/pipes/mapping.pipe\";\nfunction NotificationSettingComponent_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.label, \" \");\n  }\n}\nfunction NotificationSettingComponent_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.label, \" \");\n  }\n}\nfunction NotificationSettingComponent_tr_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"getTypeMailName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 30);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 28);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"getStatusMailName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 31)(14, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function NotificationSettingComponent_tr_48_Template_button_click_14_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      const dialog_r4 = i0.ɵɵreference(52);\n      return i0.ɵɵresetView(ctx_r6.onSelectedBuildCaseMail(item_r6, dialog_r4));\n    });\n    i0.ɵɵtext(15, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function NotificationSettingComponent_tr_48_Template_button_click_16_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onDelete(item_r6));\n    });\n    i0.ɵɵtext(17, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CBuildCaseMailId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 5, item_r6.CMailType));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r6.CMail);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 7, item_r6.CStatus));\n  }\n}\nfunction NotificationSettingComponent_ng_template_51_nb_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r9.label, \" \");\n  }\n}\nfunction NotificationSettingComponent_ng_template_51_nb_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r10);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r10.label, \" \");\n  }\n}\nfunction NotificationSettingComponent_ng_template_51_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r11);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r11.label, \" \");\n  }\n}\nfunction NotificationSettingComponent_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 34);\n    i0.ɵɵelement(1, \"nb-card-header\");\n    i0.ɵɵelementStart(2, \"nb-card-body\", 35)(3, \"div\", 36)(4, \"label\", 37);\n    i0.ɵɵtext(5, \"\\u5EFA\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-select\", 38);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_ng_template_51_Template_nb_select_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedDetail.CBuildCaseId, $event) || (ctx_r6.selectedDetail.CBuildCaseId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(7, NotificationSettingComponent_ng_template_51_nb_option_7_Template, 2, 2, \"nb-option\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 39)(9, \"label\", 40);\n    i0.ɵɵtext(10, \"\\u96FB\\u5B50\\u90F5\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"textarea\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_ng_template_51_Template_textarea_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedBuildCaseMail.CMail, $event) || (ctx_r6.selectedBuildCaseMail.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(12, \"        \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 36)(14, \"label\", 42);\n    i0.ɵɵtext(15, \"\\u767C\\u9001\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-select\", 12);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_ng_template_51_Template_nb_select_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedDetail.CMailType, $event) || (ctx_r6.selectedDetail.CMailType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(17, NotificationSettingComponent_ng_template_51_nb_option_17_Template, 2, 2, \"nb-option\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 36)(19, \"label\", 43);\n    i0.ɵɵtext(20, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"nb-select\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_ng_template_51_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedDetail.CStatus, $event) || (ctx_r6.selectedDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, NotificationSettingComponent_ng_template_51_nb_option_22_Template, 2, 2, \"nb-option\", 7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"nb-card-footer\", 25)(24, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function NotificationSettingComponent_ng_template_51_Template_button_click_24_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r8).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onClose(ref_r12));\n    });\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function NotificationSettingComponent_ng_template_51_Template_button_click_26_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r8).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onSubmit(ref_r12));\n    });\n    i0.ɵɵtext(27, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedDetail.CBuildCaseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.buildCaseOptionsDialog);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedBuildCaseMail.CMail);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedDetail.CMailType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.typeOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.statusOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r6.isNew ? \"\\u53D6\\u6D88\" : \"\\u95DC\\u9589\");\n  }\n}\nexport let NotificationSettingComponent = /*#__PURE__*/(() => {\n  class NotificationSettingComponent extends BaseComponent {\n    constructor(_allow, dialogService, message, valid, _buildCaseService, _buildCaseMaildService, pettern) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._buildCaseService = _buildCaseService;\n      this._buildCaseMaildService = _buildCaseMaildService;\n      this.pettern = pettern;\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      this.buildCaseMailList = [];\n      this.initBuildCaseMail = {\n        CStatus: 0,\n        CBuildCaseId: 0,\n        CMailType: 0,\n        CMail: \"\"\n      };\n      this.statusOptions = [{\n        value: 0,\n        label: '停用'\n      }, {\n        value: 1,\n        label: '啟用'\n      }];\n      this.typeOptionsAll = [{\n        value: '',\n        label: '全部'\n      }, {\n        value: 1,\n        label: '簽署完成'\n      }, {\n        value: 2,\n        label: '已預約客變'\n      }];\n      this.typeOptions = [{\n        value: 1,\n        label: '簽署完成'\n      }, {\n        value: 2,\n        label: '已預約客變'\n      }];\n      this.buildCaseOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      this.buildCaseOptionsDialog = [];\n      this.isNew = true;\n    }\n    ngOnInit() {\n      this.getListBuildCase();\n      this.selected = {\n        CMailType: this.typeOptionsAll[0],\n        CBuildCaseId: this.buildCaseOptions[0]\n      };\n      this.selectedDetail = {\n        CMailType: this.typeOptions[0],\n        CBuildCaseId: this.buildCaseOptionsDialog[0],\n        CStatus: this.statusOptions[0]\n      };\n      this.searchQuery = {\n        CBuildCaseId: this.buildCaseOptions[0].value,\n        CMailType: this.typeOptions[0].value,\n        CMail: ''\n      };\n      this.getListBuildCaseMail();\n      this.selectedBuildCaseMail = this.initBuildCaseMail;\n    }\n    removeEmptyValues(obj) {\n      const newObj = {\n        ...obj\n      };\n      for (const key in newObj) {\n        if (newObj[key] === '' || newObj[key] === 0) {\n          delete newObj[key];\n        }\n      }\n      return newObj;\n    }\n    handleParamRequest() {\n      this.searchQuery = {\n        ...this.searchQuery,\n        CBuildCaseId: this.selected.CBuildCaseId.value,\n        CMailType: this.selected.CMailType.value,\n        pageIndex: this.pageIndex,\n        pageSize: this.pageSize\n      };\n      return {\n        ...this.removeEmptyValues(this.searchQuery)\n      };\n    }\n    getListBuildCaseMail() {\n      this._buildCaseMaildService.apiBuildCaseMailGetBuildCaseMailListPost$Json({\n        body: this.handleParamRequest()\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode === 0) {\n          this.buildCaseMailList = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n        }\n      });\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getListBuildCaseMail();\n    }\n    getListBuildCase() {\n      this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n        body: {\n          CIsPagi: false,\n          CStatus: 1\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          const options = res.Entries.map(e => {\n            return {\n              label: e?.CBuildCaseName,\n              value: e?.cID\n            };\n          });\n          this.buildCaseOptions = [{\n            label: '全部',\n            value: ''\n          }, ...options];\n          this.buildCaseOptionsDialog = [...options];\n        }\n      });\n    }\n    addNew(ref) {\n      this.isNew = true;\n      this.selectedBuildCaseMail = this.initBuildCaseMail;\n      this.selectedDetail = {\n        CMailType: this.typeOptions[0],\n        CBuildCaseId: this.buildCaseOptionsDialog[0],\n        CStatus: this.statusOptions[0]\n      };\n      this.dialogService.open(ref);\n    }\n    onSelectedBuildCaseMail(data, ref) {\n      this._buildCaseMaildService.apiBuildCaseMailGetBuildCaseMailListPost$Json({\n        body: {\n          CBuildCaseMailId: data.CBuildCaseMailId\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0 && res.Entries.length) {\n          this.selectedBuildCaseMail = {\n            CBuildCaseMailId: res.Entries[0].CBuildCaseMailId,\n            CBuildCaseId: res.Entries[0].CBuildCaseid,\n            CMail: res.Entries[0].CMail,\n            CMailType: res.Entries[0].CMailType,\n            CStatus: res.Entries[0].CStatus\n          };\n          this.selectedDetail = {\n            CMailType: this.typeOptions.find(item => item.value === this.selectedBuildCaseMail.CMailType),\n            CBuildCaseId: this.buildCaseOptionsDialog.find(item => item.value === this.selectedBuildCaseMail.CBuildCaseId),\n            CStatus: this.statusOptions.find(item => item.value === this.selectedBuildCaseMail.CStatus)\n          };\n        }\n      });\n      this.selectedBuildCaseMail = data;\n      this.dialogService.open(ref);\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[建案]', this.selectedBuildCaseMail.CBuildCaseId);\n      // this.valid.pattern('[電子郵件]', this.selectedBuildCaseMail.CMail, this.pettern.MailPettern)\n    }\n    onSubmit(ref) {\n      this.selectedBuildCaseMail = {\n        ...this.selectedBuildCaseMail,\n        CBuildCaseId: this.selectedDetail.CBuildCaseId.value,\n        CMailType: this.selectedDetail.CMailType.value,\n        CStatus: this.selectedDetail.CStatus.value\n      };\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._buildCaseMaildService.apiBuildCaseMailSaveBuildCaseMailPost$Json({\n        body: this.selectedBuildCaseMail\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getListBuildCaseMail();\n          ref.close();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n    onDelete(data) {\n      if (window.confirm(`確定要刪除【項目${data.CBuildCaseName}】?`)) {\n        this._buildCaseMaildService.apiBuildCaseMailDeleteBuildCaseMailPost$Json({\n          body: data.CBuildCaseMailId\n        }).subscribe(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG(\"執行成功\");\n            this.getListBuildCaseMail();\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        });\n      }\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    static {\n      this.ɵfac = function NotificationSettingComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NotificationSettingComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.BuildCaseMailService), i0.ɵɵdirectiveInject(i6.PetternHelper));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NotificationSettingComponent,\n        selectors: [[\"ngx-notification-setting\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 53,\n        vars: 9,\n        consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"search\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"id\", \"search\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"preOrderDate\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u767C\\u9001\\u985E\\u578B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-info\", \"mx-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"1000px\", \"table-layout\", \"fixed\"], [1, \"table-header\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"col-1\"], [1, \"col-2\"], [1, \"col-4\", \"ellipsis\"], [1, \"w-32\", \"col-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", \"text-red-500\", \"border-red-500\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", \"mt-[10px]\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\"], [\"for\", \"CMail\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CMailType\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", \"mt-[10px]\", 2, \"min-width\", \"75px\"], [\"for\", \"CStatus\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", \"mt-[10px]\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"]],\n        template: function NotificationSettingComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 2)(5, \"div\", 3)(6, \"div\", 4)(7, \"label\", 5);\n            i0.ɵɵtext(8, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"nb-select\", 6);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_Template_nb_select_ngModelChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selected.CBuildCaseId, $event) || (ctx.selected.CBuildCaseId = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(10, NotificationSettingComponent_nb_option_10_Template, 2, 2, \"nb-option\", 7);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"div\", 3)(12, \"div\", 8)(13, \"label\", 9);\n            i0.ɵɵtext(14, \"\\u96FB\\u5B50\\u90F5\\u4EF6 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"nb-form-field\")(16, \"input\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_Template_input_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.cMail, $event) || (ctx.searchQuery.cMail = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(17, \"div\", 3)(18, \"div\", 8)(19, \"label\", 11);\n            i0.ɵɵtext(20, \"\\u767C\\u9001\\u985E\\u578B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"nb-select\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationSettingComponent_Template_nb_select_ngModelChange_21_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selected.CMailType, $event) || (ctx.selected.CMailType = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(22, NotificationSettingComponent_nb_option_22_Template, 2, 2, \"nb-option\", 7);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(23, \"div\", 3)(24, \"div\", 13)(25, \"button\", 14);\n            i0.ɵɵlistener(\"click\", function NotificationSettingComponent_Template_button_click_25_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const dialog_r4 = i0.ɵɵreference(52);\n              return i0.ɵɵresetView(ctx.addNew(dialog_r4));\n            });\n            i0.ɵɵtext(26, \" \\u65B0\\u589E \");\n            i0.ɵɵelement(27, \"i\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function NotificationSettingComponent_Template_button_click_28_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getListBuildCaseMail());\n            });\n            i0.ɵɵtext(29, \" \\u67E5\\u8A62 \");\n            i0.ɵɵelement(30, \"i\", 17);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(31, \"div\", 18)(32, \"table\", 19)(33, \"thead\", 20)(34, \"tr\")(35, \"th\", 21);\n            i0.ɵɵtext(36, \"ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"th\", 22);\n            i0.ɵɵtext(38, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"th\", 22);\n            i0.ɵɵtext(40, \"\\u767C\\u9001\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"th\", 23);\n            i0.ɵɵtext(42, \"\\u96FB\\u5B50\\u90F5\\u4EF6\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"th\", 21);\n            i0.ɵɵtext(44, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"th\", 22);\n            i0.ɵɵtext(46, \"\\u52D5\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(47, \"tbody\");\n            i0.ɵɵtemplate(48, NotificationSettingComponent_tr_48_Template, 18, 9, \"tr\", 24);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(49, \"nb-card-footer\", 25)(50, \"ngb-pagination\", 26);\n            i0.ɵɵtwoWayListener(\"pageChange\", function NotificationSettingComponent_Template_ngb_pagination_pageChange_50_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"pageChange\", function NotificationSettingComponent_Template_ngb_pagination_pageChange_50_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(51, NotificationSettingComponent_ng_template_51_Template, 28, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selected.CBuildCaseId);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseOptions);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.cMail);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selected.CMailType);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.typeOptionsAll);\n            i0.ɵɵadvance(26);\n            i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseMailList);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i7.NgForOf, SharedModule, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i9.NgbPagination, i10.BreadcrumbComponent, i11.BaseLabelDirective, i12.StatusMailPipe, i12.TypeMailPipe],\n        styles: [\".ellipsis[_ngcontent-%COMP%]{word-wrap:break-word;overflow-wrap:break-word;white-space:normal}\"]\n      });\n    }\n  }\n  return NotificationSettingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}