{"ast": null, "code": "export var EnumHouseType = /*#__PURE__*/function (EnumHouseType) {\n  EnumHouseType[EnumHouseType[\"\\u5730\\u4E3B\\u6236\"] = 1] = \"\\u5730\\u4E3B\\u6236\";\n  EnumHouseType[EnumHouseType[\"\\u92B7\\u552E\\u6236\"] = 2] = \"\\u92B7\\u552E\\u6236\"; //sales account\n  return EnumHouseType;\n}(EnumHouseType || {});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}