{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/dashboard.service\";\nimport * as i2 from \"ngx-echarts\";\nexport class QuotationTrendsComponent {\n  constructor(dashboardService) {\n    this.dashboardService = dashboardService;\n    this.chartOption = {};\n  }\n  ngOnInit() {\n    this.loadQuotationData();\n  }\n  loadQuotationData() {\n    this.dashboardService.getQuotationTrendData().subscribe(data => {\n      this.setupChart(data);\n    });\n  }\n  setupChart(data) {\n    this.chartOption = {\n      title: {\n        text: '報價單狀態趨勢',\n        left: 'center',\n        textStyle: {\n          fontSize: 16,\n          fontWeight: 'bold',\n          color: '#2c3e50'\n        }\n      },\n      tooltip: {\n        trigger: 'axis',\n        formatter: params => {\n          let result = `${params[0].axisValue}<br/>`;\n          params.forEach(param => {\n            result += `${param.marker}${param.seriesName}: ${param.value}件<br/>`;\n          });\n          return result;\n        }\n      },\n      legend: {\n        bottom: 10,\n        data: data.series.map(s => s.name)\n      },\n      grid: {\n        left: '5%',\n        right: '5%',\n        bottom: '20%',\n        top: '20%',\n        containLabel: true\n      },\n      xAxis: {\n        type: 'category',\n        data: data.months,\n        axisLabel: {\n          rotate: 45\n        }\n      },\n      yAxis: {\n        type: 'value',\n        axisLabel: {\n          formatter: '{value}件'\n        }\n      },\n      series: data.series.map(series => ({\n        name: series.name,\n        type: 'line',\n        stack: 'total',\n        areaStyle: {\n          opacity: 0.6\n        },\n        data: series.data,\n        itemStyle: {\n          color: series.color\n        },\n        lineStyle: {\n          width: 3\n        },\n        symbol: 'circle',\n        symbolSize: 8,\n        emphasis: {\n          focus: 'series'\n        }\n      }))\n    };\n  }\n  static {\n    this.ɵfac = function QuotationTrendsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuotationTrendsComponent)(i0.ɵɵdirectiveInject(i1.DashboardService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QuotationTrendsComponent,\n      selectors: [[\"app-quotation-trends\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"chart-container\", \"dashboard-card\"], [\"echarts\", \"\", 1, \"chart\", 3, \"options\"]],\n      template: function QuotationTrendsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.chartOption);\n        }\n      },\n      dependencies: [CommonModule, NgxEchartsModule, i2.NgxEchartsDirective],\n      styles: [\".chart-container[_ngcontent-%COMP%] {\\n  height: 350px;\\n  width: 100%;\\n  background: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  padding: 20px;\\n  border: 1px solid #f0f0f0;\\n}\\n\\n.chart[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInF1b3RhdGlvbi10cmVuZHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSx5Q0FBQTtFQUNBLGFBQUE7RUFDQSx5QkFBQTtBQUNGOztBQUVBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUFDRiIsImZpbGUiOiJxdW90YXRpb24tdHJlbmRzLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmNoYXJ0LWNvbnRhaW5lciB7XG4gIGhlaWdodDogMzUwcHg7XG4gIHdpZHRoOiAxMDAlO1xuICBiYWNrZ3JvdW5kOiAjZmZmZmZmO1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgcGFkZGluZzogMjBweDtcbiAgYm9yZGVyOiAxcHggc29saWQgI2YwZjBmMDtcbn1cblxuLmNoYXJ0IHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG9tZS9jb21wb25lbnRzL3F1b3RhdGlvbi10cmVuZHMvcXVvdGF0aW9uLXRyZW5kcy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLHlDQUFBO0VBQ0EsYUFBQTtFQUNBLHlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtBQUNGO0FBQ0Esd3RCQUF3dEIiLCJzb3VyY2VzQ29udGVudCI6WyIuY2hhcnQtY29udGFpbmVyIHtcbiAgaGVpZ2h0OiAzNTBweDtcbiAgd2lkdGg6IDEwMCU7XG4gIGJhY2tncm91bmQ6ICNmZmZmZmY7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICBwYWRkaW5nOiAyMHB4O1xuICBib3JkZXI6IDFweCBzb2xpZCAjZjBmMGYwO1xufVxuXG4uY2hhcnQge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NgxEchartsModule", "QuotationTrendsComponent", "constructor", "dashboardService", "chartOption", "ngOnInit", "loadQuotationData", "getQuotationTrendData", "subscribe", "data", "<PERSON><PERSON><PERSON>", "title", "text", "left", "textStyle", "fontSize", "fontWeight", "color", "tooltip", "trigger", "formatter", "params", "result", "axisValue", "for<PERSON>ach", "param", "marker", "seriesName", "value", "legend", "bottom", "series", "map", "s", "name", "grid", "right", "top", "containLabel", "xAxis", "type", "months", "axisLabel", "rotate", "yAxis", "stack", "areaStyle", "opacity", "itemStyle", "lineStyle", "width", "symbol", "symbolSize", "emphasis", "focus", "i0", "ɵɵdirectiveInject", "i1", "DashboardService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "QuotationTrendsComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "i2", "NgxEchartsDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\quotation-trends\\quotation-trends.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\quotation-trends\\quotation-trends.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport { EChartsOption } from 'echarts';\nimport { QuotationTrendData } from '../../models/dashboard.interface';\nimport { DashboardService } from '../../services/dashboard.service';\n\n@Component({\n  selector: 'app-quotation-trends',\n  standalone: true,\n  imports: [CommonModule, NgxEchartsModule],\n  templateUrl: './quotation-trends.component.html',\n  styleUrls: ['./quotation-trends.component.scss']\n})\nexport class QuotationTrendsComponent implements OnInit {\n  chartOption: EChartsOption = {};\n\n  constructor(private dashboardService: DashboardService) { }\n\n  ngOnInit(): void {\n    this.loadQuotationData();\n  }\n\n  private loadQuotationData(): void {\n    this.dashboardService.getQuotationTrendData().subscribe(data => {\n      this.setupChart(data);\n    });\n  }\n\n  private setupChart(data: QuotationTrendData): void {\n    this.chartOption = {\n      title: {\n        text: '報價單狀態趨勢',\n        left: 'center',\n        textStyle: {\n          fontSize: 16,\n          fontWeight: 'bold',\n          color: '#2c3e50'\n        }\n      },\n      tooltip: {\n        trigger: 'axis',\n        formatter: (params: any) => {\n          let result = `${params[0].axisValue}<br/>`;\n          params.forEach((param: any) => {\n            result += `${param.marker}${param.seriesName}: ${param.value}件<br/>`;\n          });\n          return result;\n        }\n      },\n      legend: {\n        bottom: 10,\n        data: data.series.map(s => s.name)\n      },\n      grid: {\n        left: '5%',\n        right: '5%',\n        bottom: '20%',\n        top: '20%',\n        containLabel: true\n      },\n      xAxis: {\n        type: 'category',\n        data: data.months,\n        axisLabel: {\n          rotate: 45\n        }\n      },\n      yAxis: {\n        type: 'value',\n        axisLabel: {\n          formatter: '{value}件'\n        }\n      },\n      series: data.series.map(series => ({\n        name: series.name,\n        type: 'line',\n        stack: 'total',\n        areaStyle: {\n          opacity: 0.6\n        },\n        data: series.data,\n        itemStyle: {\n          color: series.color\n        },\n        lineStyle: {\n          width: 3\n        },\n        symbol: 'circle',\n        symbolSize: 8,\n        emphasis: {\n          focus: 'series'\n        }\n      }))\n    };\n  }\n}", "<div class=\"chart-container dashboard-card\">\n  <div echarts [options]=\"chartOption\" class=\"chart\"></div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,aAAa;;;;AAY9C,OAAM,MAAOC,wBAAwB;EAGnCC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAFpC,KAAAC,WAAW,GAAkB,EAAE;EAE2B;EAE1DC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,CAACH,gBAAgB,CAACI,qBAAqB,EAAE,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7D,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQC,UAAUA,CAACD,IAAwB;IACzC,IAAI,CAACL,WAAW,GAAG;MACjBO,KAAK,EAAE;QACLC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,QAAQ;QACdC,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,MAAM;UAClBC,KAAK,EAAE;;OAEV;MACDC,OAAO,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAGC,MAAW,IAAI;UACzB,IAAIC,MAAM,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACE,SAAS,OAAO;UAC1CF,MAAM,CAACG,OAAO,CAAEC,KAAU,IAAI;YAC5BH,MAAM,IAAI,GAAGG,KAAK,CAACC,MAAM,GAAGD,KAAK,CAACE,UAAU,KAAKF,KAAK,CAACG,KAAK,QAAQ;UACtE,CAAC,CAAC;UACF,OAAON,MAAM;QACf;OACD;MACDO,MAAM,EAAE;QACNC,MAAM,EAAE,EAAE;QACVrB,IAAI,EAAEA,IAAI,CAACsB,MAAM,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI;OAClC;MACDC,IAAI,EAAE;QACJtB,IAAI,EAAE,IAAI;QACVuB,KAAK,EAAE,IAAI;QACXN,MAAM,EAAE,KAAK;QACbO,GAAG,EAAE,KAAK;QACVC,YAAY,EAAE;OACf;MACDC,KAAK,EAAE;QACLC,IAAI,EAAE,UAAU;QAChB/B,IAAI,EAAEA,IAAI,CAACgC,MAAM;QACjBC,SAAS,EAAE;UACTC,MAAM,EAAE;;OAEX;MACDC,KAAK,EAAE;QACLJ,IAAI,EAAE,OAAO;QACbE,SAAS,EAAE;UACTtB,SAAS,EAAE;;OAEd;MACDW,MAAM,EAAEtB,IAAI,CAACsB,MAAM,CAACC,GAAG,CAACD,MAAM,KAAK;QACjCG,IAAI,EAAEH,MAAM,CAACG,IAAI;QACjBM,IAAI,EAAE,MAAM;QACZK,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE;UACTC,OAAO,EAAE;SACV;QACDtC,IAAI,EAAEsB,MAAM,CAACtB,IAAI;QACjBuC,SAAS,EAAE;UACT/B,KAAK,EAAEc,MAAM,CAACd;SACf;QACDgC,SAAS,EAAE;UACTC,KAAK,EAAE;SACR;QACDC,MAAM,EAAE,QAAQ;QAChBC,UAAU,EAAE,CAAC;QACbC,QAAQ,EAAE;UACRC,KAAK,EAAE;;OAEV,CAAC;KACH;EACH;;;uCAjFWrD,wBAAwB,EAAAsD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAxBzD,wBAAwB;MAAA0D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdrCb,EAAA,CAAAe,cAAA,aAA4C;UAC1Cf,EAAA,CAAAgB,SAAA,aAAyD;UAC3DhB,EAAA,CAAAiB,YAAA,EAAM;;;UADSjB,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAAmB,UAAA,YAAAL,GAAA,CAAAjE,WAAA,CAAuB;;;qBDS1BL,YAAY,EAAEC,gBAAgB,EAAA2E,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}