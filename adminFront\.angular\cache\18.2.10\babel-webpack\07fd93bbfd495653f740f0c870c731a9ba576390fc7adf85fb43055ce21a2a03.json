{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { tap } from 'rxjs';\nlet SpaceComponent = class SpaceComponent extends BaseComponent {\n  constructor(allow, dialogService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.spaceList = [];\n    this.spaceDetail = {};\n    this.searchKeyword = '';\n    this.searchLocation = '';\n    this.searchStatus = null;\n  }\n  ngOnInit() {\n    this.getSpaceList();\n  }\n  getSpaceList() {\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CSpaceName: this.searchKeyword || null,\n        CLocation: this.searchLocation || null,\n        CStatus: this.searchStatus\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    })).subscribe();\n  }\n  onSearch() {\n    this.pageIndex = 1;\n    this.getSpaceList();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getSpaceList();\n  }\n  openCreateModal(ref) {\n    this.spaceDetail = {\n      CSpaceName: '',\n      CLocation: '',\n      CStatus: 1 // 1 = 啟用, 0 = 停用\n    };\n    this.dialogService.open(ref);\n  }\n  openEditModal(ref, item) {\n    this.getSpaceById(item.CSpaceID, ref);\n  }\n  getSpaceById(spaceId, ref) {\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\n      body: {\n        CSpaceID: spaceId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.spaceDetail = {\n          ...res.Entries\n        };\n        this.dialogService.open(ref);\n      } else {\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\n      }\n    });\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._spaceService.apiSpaceSaveSpacePost$Json({\n      body: this.spaceDetail\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n        this.getSpaceList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    })).subscribe();\n  }\n  deleteSpace(item) {\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\n        body: {\n          CSpaceID: item.CSpaceId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"刪除成功\");\n          this.getSpaceList();\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除失敗');\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[空間名稱]', this.spaceDetail.CSpaceName);\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CSpaceName, 50);\n    this.valid.required('[所屬區域]', this.spaceDetail.CLocation);\n    this.valid.isStringMaxLength('[所屬區域]', this.spaceDetail.CLocation, 50);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n};\nSpaceComponent = __decorate([Component({\n  selector: 'ngx-space',\n  templateUrl: './space.component.html',\n  styleUrls: ['./space.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, BreadcrumbComponent]\n})], SpaceComponent);\nexport { SpaceComponent };", "map": {"version": 3, "names": ["Component", "SharedModule", "CommonModule", "BaseComponent", "BreadcrumbComponent", "tap", "SpaceComponent", "constructor", "allow", "dialogService", "_spaceService", "message", "valid", "pageFirst", "pageSize", "pageIndex", "totalRecords", "spaceList", "spaceDetail", "searchKeyword", "searchLocation", "searchStatus", "ngOnInit", "getSpaceList", "apiSpaceGetSpaceListPost$Json", "body", "PageIndex", "PageSize", "CSpaceName", "CLocation", "CStatus", "pipe", "res", "Entries", "StatusCode", "TotalItems", "showErrorMSG", "Message", "subscribe", "onSearch", "pageChanged", "newPage", "openCreateModal", "ref", "open", "openEditModal", "item", "getSpaceById", "CSpaceID", "spaceId", "apiSpaceGetSpaceByIdPost$Json", "onSubmit", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpaceSaveSpacePost$Json", "showSucessMSG", "close", "deleteSpace", "confirm", "apiSpaceDeleteSpacePost$Json", "CSpaceId", "clear", "required", "isStringMaxLength", "onClose", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\space\\space.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { tap } from 'rxjs';\r\nimport { GetSpaceListResponse, SaveSpaceRequest } from 'src/services/api/models';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-space',\r\n  templateUrl: './space.component.html',\r\n  styleUrls: ['./space.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent,\r\n  ],\r\n})\r\n\r\nexport class SpaceComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  spaceList: GetSpaceListResponse[] = [];\r\n  spaceDetail: SaveSpaceRequest = {};\r\n  searchKeyword: string = '';\r\n  searchLocation: string = '';\r\n  searchStatus: number | null = null;\r\n\r\n  override ngOnInit(): void {\r\n    this.getSpaceList();\r\n  }\r\n\r\n  getSpaceList() {\r\n    return this._spaceService.apiSpaceGetSpaceListPost$Json({\r\n      body: {\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CSpaceName: this.searchKeyword || null,\r\n        CLocation: this.searchLocation || null,\r\n        CStatus: this.searchStatus\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.spaceList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSearch() {\r\n    this.pageIndex = 1;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getSpaceList();\r\n  }\r\n\r\n  openCreateModal(ref: any) {\r\n    this.spaceDetail = {\r\n      CSpaceName: '',\r\n      CLocation: '',\r\n      CStatus: 1 // 1 = 啟用, 0 = 停用\r\n    };\r\n    this.dialogService.open(ref);\r\n  }\r\n\r\n  openEditModal(ref: any, item: any) {\r\n    this.getSpaceById(item.CSpaceID, ref);\r\n  }\r\n\r\n  getSpaceById(spaceId: number, ref: any) {\r\n    this._spaceService.apiSpaceGetSpaceByIdPost$Json({\r\n      body: { CSpaceID: spaceId }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.spaceDetail = { ...res.Entries };\r\n        this.dialogService.open(ref);\r\n      } else {\r\n        this.message.showErrorMSG(res.Message || '載入資料失敗');\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this._spaceService.apiSpaceSaveSpacePost$Json({\r\n      body: this.spaceDetail\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  deleteSpace(item: any) {\r\n    if (confirm(\"您確定要刪除此空間設定嗎？\")) {\r\n      this._spaceService.apiSpaceDeleteSpacePost$Json({\r\n        body: { CSpaceID: item.CSpaceId }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"刪除成功\");\r\n          this.getSpaceList();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '刪除失敗');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[空間名稱]', this.spaceDetail.CSpaceName);\r\n    this.valid.isStringMaxLength('[空間名稱]', this.spaceDetail.CSpaceName, 50);\r\n    this.valid.required('[所屬區域]', this.spaceDetail.CLocation);\r\n    this.valid.isStringMaxLength('[所屬區域]', this.spaceDetail.CLocation, 50);\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,kCAAkC;AAKhE,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,GAAG,QAAQ,MAAM;AAqBnB,IAAMC,cAAc,GAApB,MAAMA,cAAe,SAAQH,aAAa;EAC/CI,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACJ,KAAK,CAAC;IANO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAKN,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,SAAS,GAA2B,EAAE;IACtC,KAAAC,WAAW,GAAqB,EAAE;IAClC,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAkB,IAAI;EAXlC;EAaSC,QAAQA,CAAA;IACf,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,OAAO,IAAI,CAACb,aAAa,CAACc,6BAA6B,CAAC;MACtDC,IAAI,EAAE;QACJC,SAAS,EAAE,IAAI,CAACX,SAAS;QACzBY,QAAQ,EAAE,IAAI,CAACb,QAAQ;QACvBc,UAAU,EAAE,IAAI,CAACT,aAAa,IAAI,IAAI;QACtCU,SAAS,EAAE,IAAI,CAACT,cAAc,IAAI,IAAI;QACtCU,OAAO,EAAE,IAAI,CAACT;;KAEjB,CAAC,CAACU,IAAI,CACL1B,GAAG,CAAC2B,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACjB,SAAS,GAAGe,GAAG,CAACC,OAAO;QAC5B,IAAI,CAACjB,YAAY,GAAGgB,GAAG,CAACG,UAAW;MACrC,CAAC,MAAM;QACL,IAAI,CAACxB,OAAO,CAACyB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACxB,SAAS,GAAG,CAAC;IAClB,IAAI,CAACQ,YAAY,EAAE;EACrB;EAEAiB,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAC1B,SAAS,GAAG0B,OAAO;IACxB,IAAI,CAAClB,YAAY,EAAE;EACrB;EAEAmB,eAAeA,CAACC,GAAQ;IACtB,IAAI,CAACzB,WAAW,GAAG;MACjBU,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,CAAC,CAAC;KACZ;IACD,IAAI,CAACrB,aAAa,CAACmC,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEAE,aAAaA,CAACF,GAAQ,EAAEG,IAAS;IAC/B,IAAI,CAACC,YAAY,CAACD,IAAI,CAACE,QAAQ,EAAEL,GAAG,CAAC;EACvC;EAEAI,YAAYA,CAACE,OAAe,EAAEN,GAAQ;IACpC,IAAI,CAACjC,aAAa,CAACwC,6BAA6B,CAAC;MAC/CzB,IAAI,EAAE;QAAEuB,QAAQ,EAAEC;MAAO;KAC1B,CAAC,CAACX,SAAS,CAACN,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAChB,WAAW,GAAG;UAAE,GAAGc,GAAG,CAACC;QAAO,CAAE;QACrC,IAAI,CAACxB,aAAa,CAACmC,IAAI,CAACD,GAAG,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAAChC,OAAO,CAACyB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC;EACJ;EAEAc,QAAQA,CAACR,GAAQ;IACf,IAAI,CAACS,UAAU,EAAE;IACjB,IAAI,IAAI,CAACxC,KAAK,CAACyC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC3C,OAAO,CAAC4C,aAAa,CAAC,IAAI,CAAC3C,KAAK,CAACyC,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC3C,aAAa,CAAC8C,0BAA0B,CAAC;MAC5C/B,IAAI,EAAE,IAAI,CAACP;KACZ,CAAC,CAACa,IAAI,CACL1B,GAAG,CAAC2B,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACvB,OAAO,CAAC8C,aAAa,CAAC,MAAM,CAAC;QAClCd,GAAG,CAACe,KAAK,EAAE;QACX,IAAI,CAACnC,YAAY,EAAE;MACrB,CAAC,MAAM;QACL,IAAI,CAACZ,OAAO,CAACyB,YAAY,CAACJ,GAAG,CAACK,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAqB,WAAWA,CAACb,IAAS;IACnB,IAAIc,OAAO,CAAC,eAAe,CAAC,EAAE;MAC5B,IAAI,CAAClD,aAAa,CAACmD,4BAA4B,CAAC;QAC9CpC,IAAI,EAAE;UAAEuB,QAAQ,EAAEF,IAAI,CAACgB;QAAQ;OAChC,CAAC,CAACxB,SAAS,CAACN,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACvB,OAAO,CAAC8C,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAAClC,YAAY,EAAE;QACrB,CAAC,MAAM;UACL,IAAI,CAACZ,OAAO,CAACyB,YAAY,CAACJ,GAAG,CAACK,OAAO,IAAI,MAAM,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EACF;EAEAe,UAAUA,CAAA;IACR,IAAI,CAACxC,KAAK,CAACmD,KAAK,EAAE;IAClB,IAAI,CAACnD,KAAK,CAACoD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9C,WAAW,CAACU,UAAU,CAAC;IAC1D,IAAI,CAAChB,KAAK,CAACqD,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC/C,WAAW,CAACU,UAAU,EAAE,EAAE,CAAC;IACvE,IAAI,CAAChB,KAAK,CAACoD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9C,WAAW,CAACW,SAAS,CAAC;IACzD,IAAI,CAACjB,KAAK,CAACqD,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC/C,WAAW,CAACW,SAAS,EAAE,EAAE,CAAC;EACxE;EAEAqC,OAAOA,CAACvB,GAAQ;IACdA,GAAG,CAACe,KAAK,EAAE;EACb;CACD;AAnIYpD,cAAc,GAAA6D,UAAA,EAZ1BnE,SAAS,CAAC;EACToE,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE,CAAC,wBAAwB,CAAC;EACrCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPtE,YAAY,EACZD,YAAY,EACZG,mBAAmB;CAEtB,CAAC,C,EAEWE,cAAc,CAmI1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}