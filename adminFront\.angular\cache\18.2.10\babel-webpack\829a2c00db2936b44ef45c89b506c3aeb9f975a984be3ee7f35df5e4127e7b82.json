{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// import AngleAxis from './AngleAxis.js';\nfunction dataToCoordSize(dataSize, dataItem) {\n  // dataItem is necessary in log axis.\n  dataItem = dataItem || [0, 0];\n  return zrUtil.map(['Radius', 'Angle'], function (dim, dimIdx) {\n    var getterName = 'get' + dim + 'Axis';\n    // TODO: TYPE Check Angle Axis\n    var axis = this[getterName]();\n    var val = dataItem[dimIdx];\n    var halfSize = dataSize[dimIdx] / 2;\n    var result = axis.type === 'category' ? axis.getBandWidth() : Math.abs(axis.dataToCoord(val - halfSize) - axis.dataToCoord(val + halfSize));\n    if (dim === 'Angle') {\n      result = result * Math.PI / 180;\n    }\n    return result;\n  }, this);\n}\nexport default function polarPrepareCustom(coordSys) {\n  var radiusAxis = coordSys.getRadiusAxis();\n  var angleAxis = coordSys.getAngleAxis();\n  var radius = radiusAxis.getExtent();\n  radius[0] > radius[1] && radius.reverse();\n  return {\n    coordSys: {\n      type: 'polar',\n      cx: coordSys.cx,\n      cy: coordSys.cy,\n      r: radius[1],\n      r0: radius[0]\n    },\n    api: {\n      coord: function (data) {\n        var radius = radiusAxis.dataToRadius(data[0]);\n        var angle = angleAxis.dataToAngle(data[1]);\n        var coord = coordSys.coordToPoint([radius, angle]);\n        coord.push(radius, angle * Math.PI / 180);\n        return coord;\n      },\n      size: zrUtil.bind(dataToCoordSize, coordSys)\n    }\n  };\n}", "map": {"version": 3, "names": ["zrUtil", "dataToCoordSize", "dataSize", "dataItem", "map", "dim", "dimIdx", "getterName", "axis", "val", "halfSize", "result", "type", "getBandWidth", "Math", "abs", "dataToCoord", "PI", "polarPrepareCustom", "coordSys", "radiusAxis", "getRadiusAxis", "angleAxis", "getAngleAxis", "radius", "getExtent", "reverse", "cx", "cy", "r", "r0", "api", "coord", "data", "dataToRadius", "angle", "dataToAngle", "coordToPoint", "push", "size", "bind"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/coord/polar/prepareCustom.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// import AngleAxis from './AngleAxis.js';\nfunction dataToCoordSize(dataSize, dataItem) {\n  // dataItem is necessary in log axis.\n  dataItem = dataItem || [0, 0];\n  return zrUtil.map(['Radius', 'Angle'], function (dim, dimIdx) {\n    var getterName = 'get' + dim + 'Axis';\n    // TODO: TYPE Check Angle Axis\n    var axis = this[getterName]();\n    var val = dataItem[dimIdx];\n    var halfSize = dataSize[dimIdx] / 2;\n    var result = axis.type === 'category' ? axis.getBandWidth() : Math.abs(axis.dataToCoord(val - halfSize) - axis.dataToCoord(val + halfSize));\n    if (dim === 'Angle') {\n      result = result * Math.PI / 180;\n    }\n    return result;\n  }, this);\n}\nexport default function polarPrepareCustom(coordSys) {\n  var radiusAxis = coordSys.getRadiusAxis();\n  var angleAxis = coordSys.getAngleAxis();\n  var radius = radiusAxis.getExtent();\n  radius[0] > radius[1] && radius.reverse();\n  return {\n    coordSys: {\n      type: 'polar',\n      cx: coordSys.cx,\n      cy: coordSys.cy,\n      r: radius[1],\n      r0: radius[0]\n    },\n    api: {\n      coord: function (data) {\n        var radius = radiusAxis.dataToRadius(data[0]);\n        var angle = angleAxis.dataToAngle(data[1]);\n        var coord = coordSys.coordToPoint([radius, angle]);\n        coord.push(radius, angle * Math.PI / 180);\n        return coord;\n      },\n      size: zrUtil.bind(dataToCoordSize, coordSys)\n    }\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD;AACA,SAASC,eAAeA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC3C;EACAA,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7B,OAAOH,MAAM,CAACI,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,UAAUC,GAAG,EAAEC,MAAM,EAAE;IAC5D,IAAIC,UAAU,GAAG,KAAK,GAAGF,GAAG,GAAG,MAAM;IACrC;IACA,IAAIG,IAAI,GAAG,IAAI,CAACD,UAAU,CAAC,CAAC,CAAC;IAC7B,IAAIE,GAAG,GAAGN,QAAQ,CAACG,MAAM,CAAC;IAC1B,IAAII,QAAQ,GAAGR,QAAQ,CAACI,MAAM,CAAC,GAAG,CAAC;IACnC,IAAIK,MAAM,GAAGH,IAAI,CAACI,IAAI,KAAK,UAAU,GAAGJ,IAAI,CAACK,YAAY,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACP,IAAI,CAACQ,WAAW,CAACP,GAAG,GAAGC,QAAQ,CAAC,GAAGF,IAAI,CAACQ,WAAW,CAACP,GAAG,GAAGC,QAAQ,CAAC,CAAC;IAC3I,IAAIL,GAAG,KAAK,OAAO,EAAE;MACnBM,MAAM,GAAGA,MAAM,GAAGG,IAAI,CAACG,EAAE,GAAG,GAAG;IACjC;IACA,OAAON,MAAM;EACf,CAAC,EAAE,IAAI,CAAC;AACV;AACA,eAAe,SAASO,kBAAkBA,CAACC,QAAQ,EAAE;EACnD,IAAIC,UAAU,GAAGD,QAAQ,CAACE,aAAa,CAAC,CAAC;EACzC,IAAIC,SAAS,GAAGH,QAAQ,CAACI,YAAY,CAAC,CAAC;EACvC,IAAIC,MAAM,GAAGJ,UAAU,CAACK,SAAS,CAAC,CAAC;EACnCD,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAACE,OAAO,CAAC,CAAC;EACzC,OAAO;IACLP,QAAQ,EAAE;MACRP,IAAI,EAAE,OAAO;MACbe,EAAE,EAAER,QAAQ,CAACQ,EAAE;MACfC,EAAE,EAAET,QAAQ,CAACS,EAAE;MACfC,CAAC,EAAEL,MAAM,CAAC,CAAC,CAAC;MACZM,EAAE,EAAEN,MAAM,CAAC,CAAC;IACd,CAAC;IACDO,GAAG,EAAE;MACHC,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAE;QACrB,IAAIT,MAAM,GAAGJ,UAAU,CAACc,YAAY,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAIE,KAAK,GAAGb,SAAS,CAACc,WAAW,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAID,KAAK,GAAGb,QAAQ,CAACkB,YAAY,CAAC,CAACb,MAAM,EAAEW,KAAK,CAAC,CAAC;QAClDH,KAAK,CAACM,IAAI,CAACd,MAAM,EAAEW,KAAK,GAAGrB,IAAI,CAACG,EAAE,GAAG,GAAG,CAAC;QACzC,OAAOe,KAAK;MACd,CAAC;MACDO,IAAI,EAAEvC,MAAM,CAACwC,IAAI,CAACvC,eAAe,EAAEkB,QAAQ;IAC7C;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}