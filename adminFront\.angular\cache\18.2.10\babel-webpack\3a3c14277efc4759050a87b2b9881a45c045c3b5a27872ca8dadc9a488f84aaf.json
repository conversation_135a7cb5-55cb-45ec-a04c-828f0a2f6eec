{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/dashboard.service\";\nimport * as i2 from \"ngx-echarts\";\nexport class ProgressChartComponent {\n  constructor(dashboardService) {\n    this.dashboardService = dashboardService;\n    this.chartOption = {};\n  }\n  ngOnInit() {\n    this.loadProgressData();\n  }\n  loadProgressData() {\n    this.dashboardService.getProgressData().subscribe(data => {\n      this.setupChart(data);\n    });\n  }\n  setupChart(data) {\n    this.chartOption = {\n      title: {\n        text: '客變進度分布',\n        left: 'center',\n        textStyle: {\n          fontSize: 16,\n          fontWeight: 'bold',\n          color: '#2c3e50'\n        }\n      },\n      tooltip: {\n        trigger: 'axis',\n        axisPointer: {\n          type: 'shadow'\n        },\n        formatter: params => {\n          let result = `${params[0].name}<br/>`;\n          params.forEach(param => {\n            result += `${param.marker}${param.seriesName}: ${param.value}戶<br/>`;\n          });\n          return result;\n        }\n      },\n      legend: {\n        bottom: 10,\n        data: data.series.map(s => s.name)\n      },\n      grid: {\n        left: '5%',\n        right: '5%',\n        bottom: '20%',\n        top: '20%',\n        containLabel: true\n      },\n      xAxis: {\n        type: 'value',\n        max: 100,\n        axisLabel: {\n          formatter: '{value}戶'\n        }\n      },\n      yAxis: {\n        type: 'category',\n        data: data.categories,\n        axisLabel: {\n          fontSize: 12\n        }\n      },\n      series: data.series.map(series => ({\n        name: series.name,\n        type: 'bar',\n        stack: 'total',\n        data: series.data,\n        itemStyle: {\n          color: series.color\n        },\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowOffsetX: 0,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          }\n        }\n      }))\n    };\n  }\n  static {\n    this.ɵfac = function ProgressChartComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProgressChartComponent)(i0.ɵɵdirectiveInject(i1.DashboardService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProgressChartComponent,\n      selectors: [[\"app-progress-chart\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"chart-container\", \"dashboard-card\"], [\"echarts\", \"\", 1, \"chart\", 3, \"options\"]],\n      template: function ProgressChartComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.chartOption);\n        }\n      },\n      dependencies: [CommonModule, NgxEchartsModule, i2.NgxEchartsDirective],\n      styles: [\".chart-container[_ngcontent-%COMP%] {\\n  height: 350px;\\n  width: 100%;\\n  background: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  padding: 20px;\\n  border: 1px solid #f0f0f0;\\n}\\n\\n.chart[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2dyZXNzLWNoYXJ0LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUNBQUE7RUFDQSxhQUFBO0VBQ0EseUJBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0FBQ0YiLCJmaWxlIjoicHJvZ3Jlc3MtY2hhcnQuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuY2hhcnQtY29udGFpbmVyIHtcbiAgaGVpZ2h0OiAzNTBweDtcbiAgd2lkdGg6IDEwMCU7XG4gIGJhY2tncm91bmQ6ICNmZmZmZmY7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICBwYWRkaW5nOiAyMHB4O1xuICBib3JkZXI6IDFweCBzb2xpZCAjZjBmMGYwO1xufVxuXG4uY2hhcnQge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG9tZS9jb21wb25lbnRzL3Byb2dyZXNzLWNoYXJ0L3Byb2dyZXNzLWNoYXJ0LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUNBQUE7RUFDQSxhQUFBO0VBQ0EseUJBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0FBQ0Y7QUFDQSxvdEJBQW90QiIsInNvdXJjZXNDb250ZW50IjpbIi5jaGFydC1jb250YWluZXIge1xuICBoZWlnaHQ6IDM1MHB4O1xuICB3aWR0aDogMTAwJTtcbiAgYmFja2dyb3VuZDogI2ZmZmZmZjtcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIHBhZGRpbmc6IDIwcHg7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNmMGYwZjA7XG59XG5cbi5jaGFydCB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NgxEchartsModule", "ProgressChartComponent", "constructor", "dashboardService", "chartOption", "ngOnInit", "loadProgressData", "getProgressData", "subscribe", "data", "<PERSON><PERSON><PERSON>", "title", "text", "left", "textStyle", "fontSize", "fontWeight", "color", "tooltip", "trigger", "axisPointer", "type", "formatter", "params", "result", "name", "for<PERSON>ach", "param", "marker", "seriesName", "value", "legend", "bottom", "series", "map", "s", "grid", "right", "top", "containLabel", "xAxis", "max", "axisLabel", "yAxis", "categories", "stack", "itemStyle", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "i0", "ɵɵdirectiveInject", "i1", "DashboardService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProgressChartComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "i2", "NgxEchartsDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\progress-chart\\progress-chart.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\progress-chart\\progress-chart.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NgxEchartsModule } from 'ngx-echarts';\nimport { EChartsOption } from 'echarts';\nimport { ProgressData } from '../../models/dashboard.interface';\nimport { DashboardService } from '../../services/dashboard.service';\n\n@Component({\n  selector: 'app-progress-chart',\n  standalone: true,\n  imports: [CommonModule, NgxEchartsModule],\n  templateUrl: './progress-chart.component.html',\n  styleUrls: ['./progress-chart.component.scss']\n})\nexport class ProgressChartComponent implements OnInit {\n  chartOption: EChartsOption = {};\n\n  constructor(private dashboardService: DashboardService) { }\n\n  ngOnInit(): void {\n    this.loadProgressData();\n  }\n\n  private loadProgressData(): void {\n    this.dashboardService.getProgressData().subscribe(data => {\n      this.setupChart(data);\n    });\n  }\n\n  private setupChart(data: ProgressData): void {\n    this.chartOption = {\n      title: {\n        text: '客變進度分布',\n        left: 'center',\n        textStyle: {\n          fontSize: 16,\n          fontWeight: 'bold',\n          color: '#2c3e50'\n        }\n      },\n      tooltip: {\n        trigger: 'axis',\n        axisPointer: {\n          type: 'shadow'\n        },\n        formatter: (params: any) => {\n          let result = `${params[0].name}<br/>`;\n          params.forEach((param: any) => {\n            result += `${param.marker}${param.seriesName}: ${param.value}戶<br/>`;\n          });\n          return result;\n        }\n      },\n      legend: {\n        bottom: 10,\n        data: data.series.map(s => s.name)\n      },\n      grid: {\n        left: '5%',\n        right: '5%',\n        bottom: '20%',\n        top: '20%',\n        containLabel: true\n      },\n      xAxis: {\n        type: 'value',\n        max: 100,\n        axisLabel: {\n          formatter: '{value}戶'\n        }\n      },\n      yAxis: {\n        type: 'category',\n        data: data.categories,\n        axisLabel: {\n          fontSize: 12\n        }\n      },\n      series: data.series.map(series => ({\n        name: series.name,\n        type: 'bar',\n        stack: 'total',\n        data: series.data,\n        itemStyle: {\n          color: series.color\n        },\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowOffsetX: 0,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          }\n        }\n      }))\n    };\n  }\n}", "<div class=\"chart-container dashboard-card\">\n  <div echarts [options]=\"chartOption\" class=\"chart\"></div>\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,aAAa;;;;AAY9C,OAAM,MAAOC,sBAAsB;EAGjCC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAFpC,KAAAC,WAAW,GAAkB,EAAE;EAE2B;EAE1DC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB,IAAI,CAACH,gBAAgB,CAACI,eAAe,EAAE,CAACC,SAAS,CAACC,IAAI,IAAG;MACvD,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQC,UAAUA,CAACD,IAAkB;IACnC,IAAI,CAACL,WAAW,GAAG;MACjBO,KAAK,EAAE;QACLC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,QAAQ;QACdC,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,MAAM;UAClBC,KAAK,EAAE;;OAEV;MACDC,OAAO,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE;UACXC,IAAI,EAAE;SACP;QACDC,SAAS,EAAGC,MAAW,IAAI;UACzB,IAAIC,MAAM,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,OAAO;UACrCF,MAAM,CAACG,OAAO,CAAEC,KAAU,IAAI;YAC5BH,MAAM,IAAI,GAAGG,KAAK,CAACC,MAAM,GAAGD,KAAK,CAACE,UAAU,KAAKF,KAAK,CAACG,KAAK,QAAQ;UACtE,CAAC,CAAC;UACF,OAAON,MAAM;QACf;OACD;MACDO,MAAM,EAAE;QACNC,MAAM,EAAE,EAAE;QACVvB,IAAI,EAAEA,IAAI,CAACwB,MAAM,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACV,IAAI;OAClC;MACDW,IAAI,EAAE;QACJvB,IAAI,EAAE,IAAI;QACVwB,KAAK,EAAE,IAAI;QACXL,MAAM,EAAE,KAAK;QACbM,GAAG,EAAE,KAAK;QACVC,YAAY,EAAE;OACf;MACDC,KAAK,EAAE;QACLnB,IAAI,EAAE,OAAO;QACboB,GAAG,EAAE,GAAG;QACRC,SAAS,EAAE;UACTpB,SAAS,EAAE;;OAEd;MACDqB,KAAK,EAAE;QACLtB,IAAI,EAAE,UAAU;QAChBZ,IAAI,EAAEA,IAAI,CAACmC,UAAU;QACrBF,SAAS,EAAE;UACT3B,QAAQ,EAAE;;OAEb;MACDkB,MAAM,EAAExB,IAAI,CAACwB,MAAM,CAACC,GAAG,CAACD,MAAM,KAAK;QACjCR,IAAI,EAAEQ,MAAM,CAACR,IAAI;QACjBJ,IAAI,EAAE,KAAK;QACXwB,KAAK,EAAE,OAAO;QACdpC,IAAI,EAAEwB,MAAM,CAACxB,IAAI;QACjBqC,SAAS,EAAE;UACT7B,KAAK,EAAEgB,MAAM,CAAChB;SACf;QACD8B,QAAQ,EAAE;UACRD,SAAS,EAAE;YACTE,UAAU,EAAE,EAAE;YACdC,aAAa,EAAE,CAAC;YAChBC,WAAW,EAAE;;;OAGlB,CAAC;KACH;EACH;;;uCAjFWjD,sBAAsB,EAAAkD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAtBrD,sBAAsB;MAAAsD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdnCb,EAAA,CAAAe,cAAA,aAA4C;UAC1Cf,EAAA,CAAAgB,SAAA,aAAyD;UAC3DhB,EAAA,CAAAiB,YAAA,EAAM;;;UADSjB,EAAA,CAAAkB,SAAA,EAAuB;UAAvBlB,EAAA,CAAAmB,UAAA,YAAAL,GAAA,CAAA7D,WAAA,CAAuB;;;qBDS1BL,YAAY,EAAEC,gBAAgB,EAAAuE,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}