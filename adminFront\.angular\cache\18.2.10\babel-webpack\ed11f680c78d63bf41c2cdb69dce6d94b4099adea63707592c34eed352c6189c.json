{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nimport * as i12 from \"../../../@theme/pipes/date-format.pipe\";\nimport * as i13 from \"../../../@theme/pipes/mapping.pipe\";\nimport * as i14 from \"../../../@theme/pipes/specialChangeSource.pipe\";\nfunction SampleSelectionResultComponent_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 16)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"dateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getDocumentStatus\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 17)(15, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function SampleSelectionResultComponent_tr_29_Template_button_click_15_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openPdfInNewTab(item_r4));\n    });\n    i0.ɵɵtext(16, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.CIsChange === true ? \"\\u5BA2\\u8B8A\" : item_r4.CIsChange === false ? \"\\u9078\\u6A23\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.CDocumentName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 5, item_r4.CCreateDt, \"yyyy/MM/dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r4.CSignDate ? i0.ɵɵpipeBind1(10, 8, item_r4.CSignDate) : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 10, item_r4.CDocumentStatus));\n  }\n}\nfunction SampleSelectionResultComponent_ng_template_36_tr_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 38);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function SampleSelectionResultComponent_ng_template_36_tr_35_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const drawing_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      i0.ɵɵtwoWayBindingSet(drawing_r8.isChecked, $event) || (drawing_r8.isChecked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"specialChangeSource\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const drawing_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"checked\", drawing_r8.isChecked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 6, drawing_r8.CSource));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.formatDate(drawing_r8.CChangeDate));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(drawing_r8.CDrawingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.formatDate(drawing_r8.CCreateDT));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.formatDate(drawing_r8.CApproveDate));\n  }\n}\nfunction SampleSelectionResultComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 19)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 20)(4, \"h4\");\n    i0.ɵɵtext(5, \" \\u8ACB\\u78BA\\u8A8D\\u8981\\u5C07\\u54EA\\u4E9B\\u5716\\u9762\\u6574\\u5408\\u70BA\\u4E00\\u4EFD\\u6587\\u4EF6\\u4F9B\\u5BA2\\u6236\\u7C3D\\u540D\\u78BA\\u8A8D\\u3002 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 21)(7, \"label\", 22);\n    i0.ɵɵtext(8, \" \\u6587\\u4EF6\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 23);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SampleSelectionResultComponent_ng_template_36_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.finalDoc.CDocumentName, $event) || (ctx_r4.finalDoc.CDocumentName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 21)(11, \"label\", 24);\n    i0.ɵɵtext(12, \" \\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"nb-checkbox\", 25);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function SampleSelectionResultComponent_ng_template_36_Template_nb_checkbox_checkedChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.isChecked, $event) || (ctx_r4.isChecked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(14, \"\\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 21)(16, \"label\", 26);\n    i0.ɵɵtext(17, \" \\u5BA2\\u8B8A\\u5716 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"h4\");\n    i0.ɵɵtext(19, \"\\u50C5\\u80FD\\u52FE\\u9078\\u5DF2\\u901A\\u904E\\u5BE9\\u6838\\u4E4B\\u5716\\u9762\\u3002\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"table\", 27)(21, \"thead\")(22, \"tr\", 28);\n    i0.ɵɵelement(23, \"td\");\n    i0.ɵɵelementStart(24, \"th\");\n    i0.ɵɵtext(25, \"\\u4F86\\u6E90\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\");\n    i0.ɵɵtext(27, \"\\u8A0E\\u8AD6\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"th\");\n    i0.ɵɵtext(29, \"\\u5716\\u9762\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\");\n    i0.ɵɵtext(31, \"\\u4E0A\\u50B3\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"th\");\n    i0.ɵɵtext(33, \"\\u901A\\u904E\\u65E5\\u671F\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"tbody\");\n    i0.ɵɵtemplate(35, SampleSelectionResultComponent_ng_template_36_tr_35_Template, 14, 8, \"tr\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 30)(37, \"label\", 31);\n    i0.ɵɵtext(38, \"\\u9001\\u5BE9\\u8CC7\\u8A0A \");\n    i0.ɵɵelementStart(39, \"p\", 32);\n    i0.ɵɵtext(40, \"\\u5167\\u90E8\\u5BE9\\u6838\\u4EBA\\u54E1\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"textarea\", 33);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SampleSelectionResultComponent_ng_template_36_Template_textarea_ngModelChange_41_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.finalDoc.CApproveRemark, $event) || (ctx_r4.finalDoc.CApproveRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(42, \"        \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 30)(44, \"label\", 34);\n    i0.ɵɵtext(45, \"\\u6458\\u8981\\u8A3B\\u8A18 \");\n    i0.ɵɵelementStart(46, \"p\", 32);\n    i0.ɵɵtext(47, \"\\u5BA2\\u6236\\u65BC\\u6587\\u4EF6\\u4E2D\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"textarea\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SampleSelectionResultComponent_ng_template_36_Template_textarea_ngModelChange_48_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.finalDoc.CNote, $event) || (ctx_r4.finalDoc.CNote = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(49, \"        \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"nb-card-footer\", 13)(51, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function SampleSelectionResultComponent_ng_template_36_Template_button_click_51_listener() {\n      const ref_r9 = i0.ɵɵrestoreView(_r6).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r9));\n    });\n    i0.ɵɵtext(52, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function SampleSelectionResultComponent_ng_template_36_Template_button_click_53_listener() {\n      const ref_r9 = i0.ɵɵrestoreView(_r6).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onCreateFinalDoc(ref_r9));\n    });\n    i0.ɵɵtext(54, \"\\u78BA\\u8A8D\\u9001\\u51FA\\u5BE9\\u6838\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u5BA2\\u8B8A\\u78BA\\u8A8D\\u5716\\u8AAA > \", ctx_r4.houseByID.CHousehold, \" \", ctx_r4.houseByID.CFloor, \"F \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.finalDoc.CDocumentName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r4.isChecked);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.listSpecialChangeAvailable);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.finalDoc.CApproveRemark);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.finalDoc.CNote);\n  }\n}\nexport let SampleSelectionResultComponent = /*#__PURE__*/(() => {\n  class SampleSelectionResultComponent extends BaseComponent {\n    constructor(_allow, dialogService, valid, _finalDocumentService, message, route, location, _eventService, _houseService, fileService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.valid = valid;\n      this._finalDocumentService = _finalDocumentService;\n      this.message = message;\n      this.route = route;\n      this.location = location;\n      this._eventService = _eventService;\n      this._houseService = _houseService;\n      this.fileService = fileService;\n      this.documentStatusOptions = ['待審核', '已駁回', '待客戶簽回', '客戶已簽回'];\n      this.isChecked = true;\n    }\n    ngOnInit() {\n      this.route.paramMap.subscribe(params => {\n        if (params) {\n          this.buildCaseId = +(params.get('id1') ?? 0);\n          this.houseID = +(params.get('id2') ?? 0);\n          if (this.houseID) {\n            this.getHouseById();\n          }\n          this.getListFinalDoc();\n        }\n      });\n    }\n    getHouseById() {\n      this._houseService.apiHouseGetHouseByIdPost$Json({\n        body: {\n          CHouseID: this.houseID\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseByID = res.Entries;\n        }\n      });\n    }\n    openPdfInNewTab(data) {\n      if (data && data.CFile) {\n        // 使用 FileService.GetFile 取得檔案 blob\n        this.fileService.getFile(data.CFile, data.CDocumentName || 'document.pdf').subscribe({\n          next: blob => {\n            // 建立 blob URL\n            const url = URL.createObjectURL(blob);\n            // 在新分頁開啟 PDF\n            window.open(url, '_blank');\n            // 延遲清理 URL 以確保檔案能正確載入\n            setTimeout(() => URL.revokeObjectURL(url), 10000);\n          },\n          error: error => {\n            console.error('取得檔案失敗:', error);\n            this.message.showErrorMSG('無法開啟檔案，請稍後再試');\n          }\n        });\n      }\n    }\n    getListFinalDoc() {\n      this._finalDocumentService.apiFinalDocumentGetListFinalDocPost$Json({\n        body: {\n          CHouseID: this.houseID,\n          PageIndex: this.pageIndex,\n          PageSize: this.pageSize\n        }\n      }).subscribe(res => {\n        if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n          this.listFinalDoc = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n        }\n      });\n    }\n    getListSpecialChangeAvailable() {\n      this._finalDocumentService.apiFinalDocumentGetListSpecialChangeAvailablePost$Json({\n        body: {\n          CHouseID: this.houseID\n        }\n      }).subscribe(res => {\n        this.listSpecialChangeAvailable = [];\n        if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n          this.listSpecialChangeAvailable = res.Entries ?? [];\n          if (res.Entries.length) {\n            this.listSpecialChangeAvailable = res.Entries.map(e => {\n              return {\n                ...e,\n                isChecked: false\n              };\n            });\n          }\n        }\n      });\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[文件名稱]', this.finalDoc.CDocumentName);\n      this.valid.required('[送審資訊]', this.finalDoc.CApproveRemark);\n      this.valid.required('[系統操作說明]', this.finalDoc.CNote);\n    }\n    goBack() {\n      this._eventService.push({\n        action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n        payload: this.buildCaseId\n      });\n      this.location.back();\n    }\n    getCheckedCIDs(changeArray) {\n      if (changeArray && changeArray.length) {\n        return changeArray.filter(change => change.isChecked).map(change => change.CID);\n      }\n      return [];\n    }\n    onCreateFinalDoc(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      const param = {\n        ...this.finalDoc,\n        CSpecialChange: this.getCheckedCIDs(this.listSpecialChangeAvailable)\n      };\n      this._finalDocumentService.apiFinalDocumentCreateFinalDocPost$Json({\n        body: param\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.getListFinalDoc();\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getListFinalDoc();\n    }\n    addNew(ref) {\n      this.finalDoc = {\n        CHouseID: this.houseID,\n        CDocumentName: '',\n        CApproveRemark: '',\n        CNote: \"\"\n      };\n      this.getListSpecialChangeAvailable();\n      this.dialogService.open(ref);\n    }\n    onOpenModel(ref) {\n      this.dialogService.open(ref);\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    formatDate(date) {\n      return moment(date).format('YYYY/MM/DD HH:mm');\n    }\n    static {\n      this.ɵfac = function SampleSelectionResultComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SampleSelectionResultComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.FinalDocumentService), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.EventService), i0.ɵɵdirectiveInject(i4.HouseService), i0.ɵɵdirectiveInject(i4.FileService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SampleSelectionResultComponent,\n        selectors: [[\"ngx-sample-selection-result\"]],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 38,\n        vars: 4,\n        consts: [[\"dialogConfirmImage\", \"\"], [\"accent\", \"success\"], [2, \"font-size\", \"32px\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"width\", \"1000px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"CDocumentName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6587\\u4EF6\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"isChecked\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"status\", \"basic\", \"disabled\", \"\", 3, \"checkedChange\", \"checked\"], [\"for\", \"\\u5BA2\\u8B8A\\u5716\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"table\", \"border\", \"table-striped\", 2, \"min-width\", \"600px\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"col-3\"], [2, \"color\", \"red\"], [\"name\", \"remark\", \"id\", \"remark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", \"max-width\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CNote\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"col-3\"], [\"name\", \"CNote\", \"id\", \"CNote\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", \"max-width\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"]],\n        template: function SampleSelectionResultComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\")(2, \"div\", 2);\n            i0.ɵɵtext(3, \"\\u6236\\u5225\\u7BA1\\u7406 / \\u5BA2\\u8B8A\\u78BA\\u8A8D\\u5716\\u8AAA\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(4, \"nb-card-body\")(5, \"h1\", 3);\n            i0.ɵɵtext(6, \"\\u60A8\\u53EF\\u8207\\u6B64\\u6AA2\\u8996\\u8A72\\u6236\\u5225\\u5BA2\\u6236\\u5C0D\\u65BC\\u9078\\u6A23\\u7D50\\u679C\\u4E4B\\u7C3D\\u8A8D\\u6587\\u4EF6\\uFF0C\\u4E26\\u53EF\\u9078\\u64C7\\u8981\\u5C07\\u54EA\\u4E9B\\u5BA2\\u8B8A\\u5716\\u9762\\u6574\\u5408\\u70BA\\u4E00\\u4EFD\\u5716\\u9762\\u8ACB\\u5BA2\\u6236\\u7C3D\\u56DE\\u78BA\\u8A8D\\u3002 \\u5982\\u679C\\u8A72\\u4F4D\\u5BA2\\u6236\\u6709\\u591A\\u4EFD\\u7C3D\\u56DE\\u6A94\\u6848\\uFF0C\\u65BC\\u5BA2\\u6236\\u7AEF\\u50C5\\u6703\\u986F\\u793A\\u6700\\u65B0\\u7684\\u4E00\\u4EFD\\u6587\\u4EF6\\u3002\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 4)(8, \"div\", 5)(9, \"div\", 6)(10, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function SampleSelectionResultComponent_Template_button_click_10_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const dialogConfirmImage_r2 = i0.ɵɵreference(37);\n              return i0.ɵɵresetView(ctx.addNew(dialogConfirmImage_r2));\n            });\n            i0.ɵɵtext(11, \" \\u65B0\\u589E\\u78BA\\u8A8D\\u5BA2\\u8B8A\\u5716\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(12, \"div\", 8)(13, \"table\", 9)(14, \"thead\")(15, \"tr\", 10)(16, \"th\", 11);\n            i0.ɵɵtext(17, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"th\", 11);\n            i0.ɵɵtext(19, \"\\u6587\\u4EF6\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"th\", 11);\n            i0.ɵɵtext(21, \"\\u5EFA\\u7ACB\\u65E5\\u671F \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"th\", 11);\n            i0.ɵɵtext(23, \"\\u7C3D\\u56DE\\u65E5\\u671F \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"th\", 11);\n            i0.ɵɵtext(25, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"th\", 11);\n            i0.ɵɵtext(27, \"\\u64CD\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(28, \"tbody\");\n            i0.ɵɵtemplate(29, SampleSelectionResultComponent_tr_29_Template, 17, 12, \"tr\", 12);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(30, \"nb-card-footer\", 13)(31, \"ngb-pagination\", 14);\n            i0.ɵɵtwoWayListener(\"pageChange\", function SampleSelectionResultComponent_Template_ngb_pagination_pageChange_31_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"pageChange\", function SampleSelectionResultComponent_Template_ngb_pagination_pageChange_31_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"nb-card-footer\")(33, \"div\", 13)(34, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function SampleSelectionResultComponent_Template_button_click_34_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goBack());\n            });\n            i0.ɵɵtext(35, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(36, SampleSelectionResultComponent_ng_template_36_Template, 55, 7, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(29);\n            i0.ɵɵproperty(\"ngForOf\", ctx.listFinalDoc);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [i7.NgForOf, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i10.NgbPagination, i11.BaseLabelDirective, i7.DatePipe, i12.DateFormatPipe, i13.DocumentStatusPipe, i14.SpecialChangeSourcePipe]\n      });\n    }\n  }\n  return SampleSelectionResultComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}