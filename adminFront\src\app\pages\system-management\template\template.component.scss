// Template component styles

.btn-group {
  button {
    margin-right: 0.25rem;

    &:last-child {
      margin-right: 0;
    }
  }
}

.input-group {
  .input-group-append {
    .btn {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

nb-card-header {
  h5 {
    color: var(--color-fg-heading);
  }
}

// 模板管理特定樣式
.table-actions {
  button {
    margin-right: 0.25rem;

    &:last-child {
      margin-right: 0;
    }
  }
}

.badge {
  &.badge-success {
    background-color: #28a745;
    color: white;
  }

  &.badge-secondary {
    background-color: #6c757d;
    color: white;
  }

  &.badge-info {
    background-color: #17a2b8;
    color: white;
  }

  &.badge-primary {
    background-color: #007bff;
    color: white;
  }
}

.required-field {
  &::after {
    content: " *";
    color: #dc3545;
  }
}

.alert {
  border-radius: 0.375rem;
}

// 模態框樣式
.modal-content {
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.form-check-input:checked {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-close-white {
  filter: invert(1) grayscale(100%) brightness(200%);
}

// 模板類型頁簽樣式
.template-type-tabs {
  ::ng-deep {
    nb-tabset {
      .tabset {
        border-bottom: 2px solid #f1f3f4;
        margin-bottom: 0;

        .tab {
          padding: 0;
          margin-right: 8px;

          .tab-link {
            padding: 14px 24px;
            border: none;
            border-radius: 8px 8px 0 0;
            background-color: transparent;
            color: #6c757d;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            position: relative;

            &:hover {
              background-color: #f8f9fa;
              color: #495057;
            }

            &.active {
              background-color: #007bff;
              color: #fff;
              font-weight: 600;
              box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);

              &::after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 0;
                right: 0;
                height: 2px;
                background-color: #007bff;
              }
            }
          }
        }

        .tab-content {
          padding: 0;
          border: none;
          background: transparent;

          .tab-pane {
            &.active {
              display: block;
            }
          }
        }
      }
    }
  }
}

// 空間選擇網格樣式
.space-grid {
  display: grid !important;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  margin-bottom: 20px;
  width: 100%;

  // 調試樣式 - 可以看到網格容器
  border: 1px dashed #ccc;

  .space-item {
    cursor: pointer;
    transition: all 0.3s ease;
    display: block;
    width: 100%;

    .space-card {
      padding: 16px;
      border: 2px solid #e9ecef !important;
      border-radius: 12px;
      background-color: #fff !important;
      transition: all 0.3s ease;
      min-height: 80px;
      display: flex !important;
      flex-direction: column;
      justify-content: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      position: relative;
      width: 100%;
      box-sizing: border-box;

      .space-name {
        font-weight: 600;
        color: #2c3e50 !important;
        font-size: 0.95rem;
        line-height: 1.4;
        margin-bottom: 4px;
        text-align: center;
        display: block;
      }

      .space-location {
        font-size: 0.8rem;
        color: #6c757d !important;
        line-height: 1.3;
        font-weight: 400;
        text-align: center;
        display: block;
      }
    }

    &:hover .space-card {
      border-color: #007bff;
      background-color: #f8f9ff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    }

    &.selected .space-card {
      border-color: #007bff;
      background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);

      .space-name {
        color: #0056b3;
        font-weight: 700;
      }

      .space-location {
        color: #495057;
      }

      &::after {
        content: '✓';
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        background-color: #007bff;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
      }
    }
  }
}

// 響應式設計
@media (max-width: 1200px) {
  .space-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 992px) {
  .space-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .space-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
}

@media (max-width: 576px) {
  .space-grid {
    grid-template-columns: 1fr;
  }
}