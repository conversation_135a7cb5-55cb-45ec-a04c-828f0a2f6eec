{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nvar AxisModelCommonMixin = /** @class */function () {\n  function AxisModelCommonMixin() {}\n  AxisModelCommonMixin.prototype.getNeedCrossZero = function () {\n    var option = this.option;\n    return !option.scale;\n  };\n  /**\r\n   * Should be implemented by each axis model if necessary.\r\n   * @return coordinate system model\r\n   */\n  AxisModelCommonMixin.prototype.getCoordSysModel = function () {\n    return;\n  };\n  return AxisModelCommonMixin;\n}();\nexport { AxisModelCommonMixin };", "map": {"version": 3, "names": ["AxisModelCommonMixin", "prototype", "getNeedCrossZero", "option", "scale", "getCoordSysModel"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/coord/axisModelCommonMixin.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nvar AxisModelCommonMixin = /** @class */function () {\n  function AxisModelCommonMixin() {}\n  AxisModelCommonMixin.prototype.getNeedCrossZero = function () {\n    var option = this.option;\n    return !option.scale;\n  };\n  /**\r\n   * Should be implemented by each axis model if necessary.\r\n   * @return coordinate system model\r\n   */\n  AxisModelCommonMixin.prototype.getCoordSysModel = function () {\n    return;\n  };\n  return AxisModelCommonMixin;\n}();\nexport { AxisModelCommonMixin };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,oBAAoB,GAAG,aAAa,YAAY;EAClD,SAASA,oBAAoBA,CAAA,EAAG,CAAC;EACjCA,oBAAoB,CAACC,SAAS,CAACC,gBAAgB,GAAG,YAAY;IAC5D,IAAIC,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,OAAO,CAACA,MAAM,CAACC,KAAK;EACtB,CAAC;EACD;AACF;AACA;AACA;EACEJ,oBAAoB,CAACC,SAAS,CAACI,gBAAgB,GAAG,YAAY;IAC5D;EACF,CAAC;EACD,OAAOL,oBAAoB;AAC7B,CAAC,CAAC,CAAC;AACH,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}