{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { prepareDataCoordInfo, getStackedOnPoint } from './helper.js';\nimport { createFloat32Array } from '../../util/vendor.js';\nfunction diffData(oldData, newData) {\n  var diffResult = [];\n  newData.diff(oldData).add(function (idx) {\n    diffResult.push({\n      cmd: '+',\n      idx: idx\n    });\n  }).update(function (newIdx, oldIdx) {\n    diffResult.push({\n      cmd: '=',\n      idx: oldIdx,\n      idx1: newIdx\n    });\n  }).remove(function (idx) {\n    diffResult.push({\n      cmd: '-',\n      idx: idx\n    });\n  }).execute();\n  return diffResult;\n}\nexport default function lineAnimationDiff(oldData, newData, oldStackedOnPoints, newStackedOnPoints, oldCoordSys, newCoordSys, oldValueOrigin, newValueOrigin) {\n  var diff = diffData(oldData, newData);\n  // let newIdList = newData.mapArray(newData.getId);\n  // let oldIdList = oldData.mapArray(oldData.getId);\n  // convertToIntId(newIdList, oldIdList);\n  // // FIXME One data ?\n  // diff = arrayDiff(oldIdList, newIdList);\n  var currPoints = [];\n  var nextPoints = [];\n  // Points for stacking base line\n  var currStackedPoints = [];\n  var nextStackedPoints = [];\n  var status = [];\n  var sortedIndices = [];\n  var rawIndices = [];\n  var newDataOldCoordInfo = prepareDataCoordInfo(oldCoordSys, newData, oldValueOrigin);\n  // const oldDataNewCoordInfo = prepareDataCoordInfo(newCoordSys, oldData, newValueOrigin);\n  var oldPoints = oldData.getLayout('points') || [];\n  var newPoints = newData.getLayout('points') || [];\n  for (var i = 0; i < diff.length; i++) {\n    var diffItem = diff[i];\n    var pointAdded = true;\n    var oldIdx2 = void 0;\n    var newIdx2 = void 0;\n    // FIXME, animation is not so perfect when dataZoom window moves fast\n    // Which is in case remvoing or add more than one data in the tail or head\n    switch (diffItem.cmd) {\n      case '=':\n        oldIdx2 = diffItem.idx * 2;\n        newIdx2 = diffItem.idx1 * 2;\n        var currentX = oldPoints[oldIdx2];\n        var currentY = oldPoints[oldIdx2 + 1];\n        var nextX = newPoints[newIdx2];\n        var nextY = newPoints[newIdx2 + 1];\n        // If previous data is NaN, use next point directly\n        if (isNaN(currentX) || isNaN(currentY)) {\n          currentX = nextX;\n          currentY = nextY;\n        }\n        currPoints.push(currentX, currentY);\n        nextPoints.push(nextX, nextY);\n        currStackedPoints.push(oldStackedOnPoints[oldIdx2], oldStackedOnPoints[oldIdx2 + 1]);\n        nextStackedPoints.push(newStackedOnPoints[newIdx2], newStackedOnPoints[newIdx2 + 1]);\n        rawIndices.push(newData.getRawIndex(diffItem.idx1));\n        break;\n      case '+':\n        var newIdx = diffItem.idx;\n        var newDataDimsForPoint = newDataOldCoordInfo.dataDimsForPoint;\n        var oldPt = oldCoordSys.dataToPoint([newData.get(newDataDimsForPoint[0], newIdx), newData.get(newDataDimsForPoint[1], newIdx)]);\n        newIdx2 = newIdx * 2;\n        currPoints.push(oldPt[0], oldPt[1]);\n        nextPoints.push(newPoints[newIdx2], newPoints[newIdx2 + 1]);\n        var stackedOnPoint = getStackedOnPoint(newDataOldCoordInfo, oldCoordSys, newData, newIdx);\n        currStackedPoints.push(stackedOnPoint[0], stackedOnPoint[1]);\n        nextStackedPoints.push(newStackedOnPoints[newIdx2], newStackedOnPoints[newIdx2 + 1]);\n        rawIndices.push(newData.getRawIndex(newIdx));\n        break;\n      case '-':\n        pointAdded = false;\n    }\n    // Original indices\n    if (pointAdded) {\n      status.push(diffItem);\n      sortedIndices.push(sortedIndices.length);\n    }\n  }\n  // Diff result may be crossed if all items are changed\n  // Sort by data index\n  sortedIndices.sort(function (a, b) {\n    return rawIndices[a] - rawIndices[b];\n  });\n  var len = currPoints.length;\n  var sortedCurrPoints = createFloat32Array(len);\n  var sortedNextPoints = createFloat32Array(len);\n  var sortedCurrStackedPoints = createFloat32Array(len);\n  var sortedNextStackedPoints = createFloat32Array(len);\n  var sortedStatus = [];\n  for (var i = 0; i < sortedIndices.length; i++) {\n    var idx = sortedIndices[i];\n    var i2 = i * 2;\n    var idx2 = idx * 2;\n    sortedCurrPoints[i2] = currPoints[idx2];\n    sortedCurrPoints[i2 + 1] = currPoints[idx2 + 1];\n    sortedNextPoints[i2] = nextPoints[idx2];\n    sortedNextPoints[i2 + 1] = nextPoints[idx2 + 1];\n    sortedCurrStackedPoints[i2] = currStackedPoints[idx2];\n    sortedCurrStackedPoints[i2 + 1] = currStackedPoints[idx2 + 1];\n    sortedNextStackedPoints[i2] = nextStackedPoints[idx2];\n    sortedNextStackedPoints[i2 + 1] = nextStackedPoints[idx2 + 1];\n    sortedStatus[i] = status[idx];\n  }\n  return {\n    current: sortedCurrPoints,\n    next: sortedNextPoints,\n    stackedOnCurrent: sortedCurrStackedPoints,\n    stackedOnNext: sortedNextStackedPoints,\n    status: sortedStatus\n  };\n}", "map": {"version": 3, "names": ["prepareDataCoordInfo", "getStackedOnPoint", "createFloat32Array", "diffData", "oldData", "newData", "diffResult", "diff", "add", "idx", "push", "cmd", "update", "newIdx", "oldIdx", "idx1", "remove", "execute", "lineAnimationDiff", "oldStackedOnPoints", "newStackedOnPoints", "oldCoordSys", "newCoordSys", "oldV<PERSON>ue<PERSON><PERSON><PERSON>", "newV<PERSON>ue<PERSON><PERSON><PERSON>", "currPoints", "nextPoints", "currStackedPoints", "nextStackedPoints", "status", "sortedIndices", "rawIndices", "newDataOldCoordInfo", "oldPoints", "getLayout", "newPoints", "i", "length", "diffItem", "pointAdded", "oldIdx2", "newIdx2", "currentX", "currentY", "nextX", "nextY", "isNaN", "getRawIndex", "newDataDimsForPoint", "dataDimsForPoint", "oldPt", "dataToPoint", "get", "stackedOnPoint", "sort", "a", "b", "len", "sortedCurrPoints", "sortedNextPoints", "sortedCurrStackedPoints", "sortedNextStackedPoints", "sortedStatus", "i2", "idx2", "current", "next", "stackedOnCurrent", "stackedOnNext"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/line/lineAnimationDiff.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { prepareDataCoordInfo, getStackedOnPoint } from './helper.js';\nimport { createFloat32Array } from '../../util/vendor.js';\nfunction diffData(oldData, newData) {\n  var diffResult = [];\n  newData.diff(oldData).add(function (idx) {\n    diffResult.push({\n      cmd: '+',\n      idx: idx\n    });\n  }).update(function (newIdx, oldIdx) {\n    diffResult.push({\n      cmd: '=',\n      idx: oldIdx,\n      idx1: newIdx\n    });\n  }).remove(function (idx) {\n    diffResult.push({\n      cmd: '-',\n      idx: idx\n    });\n  }).execute();\n  return diffResult;\n}\nexport default function lineAnimationDiff(oldData, newData, oldStackedOnPoints, newStackedOnPoints, oldCoordSys, newCoordSys, oldValueOrigin, newValueOrigin) {\n  var diff = diffData(oldData, newData);\n  // let newIdList = newData.mapArray(newData.getId);\n  // let oldIdList = oldData.mapArray(oldData.getId);\n  // convertToIntId(newIdList, oldIdList);\n  // // FIXME One data ?\n  // diff = arrayDiff(oldIdList, newIdList);\n  var currPoints = [];\n  var nextPoints = [];\n  // Points for stacking base line\n  var currStackedPoints = [];\n  var nextStackedPoints = [];\n  var status = [];\n  var sortedIndices = [];\n  var rawIndices = [];\n  var newDataOldCoordInfo = prepareDataCoordInfo(oldCoordSys, newData, oldValueOrigin);\n  // const oldDataNewCoordInfo = prepareDataCoordInfo(newCoordSys, oldData, newValueOrigin);\n  var oldPoints = oldData.getLayout('points') || [];\n  var newPoints = newData.getLayout('points') || [];\n  for (var i = 0; i < diff.length; i++) {\n    var diffItem = diff[i];\n    var pointAdded = true;\n    var oldIdx2 = void 0;\n    var newIdx2 = void 0;\n    // FIXME, animation is not so perfect when dataZoom window moves fast\n    // Which is in case remvoing or add more than one data in the tail or head\n    switch (diffItem.cmd) {\n      case '=':\n        oldIdx2 = diffItem.idx * 2;\n        newIdx2 = diffItem.idx1 * 2;\n        var currentX = oldPoints[oldIdx2];\n        var currentY = oldPoints[oldIdx2 + 1];\n        var nextX = newPoints[newIdx2];\n        var nextY = newPoints[newIdx2 + 1];\n        // If previous data is NaN, use next point directly\n        if (isNaN(currentX) || isNaN(currentY)) {\n          currentX = nextX;\n          currentY = nextY;\n        }\n        currPoints.push(currentX, currentY);\n        nextPoints.push(nextX, nextY);\n        currStackedPoints.push(oldStackedOnPoints[oldIdx2], oldStackedOnPoints[oldIdx2 + 1]);\n        nextStackedPoints.push(newStackedOnPoints[newIdx2], newStackedOnPoints[newIdx2 + 1]);\n        rawIndices.push(newData.getRawIndex(diffItem.idx1));\n        break;\n      case '+':\n        var newIdx = diffItem.idx;\n        var newDataDimsForPoint = newDataOldCoordInfo.dataDimsForPoint;\n        var oldPt = oldCoordSys.dataToPoint([newData.get(newDataDimsForPoint[0], newIdx), newData.get(newDataDimsForPoint[1], newIdx)]);\n        newIdx2 = newIdx * 2;\n        currPoints.push(oldPt[0], oldPt[1]);\n        nextPoints.push(newPoints[newIdx2], newPoints[newIdx2 + 1]);\n        var stackedOnPoint = getStackedOnPoint(newDataOldCoordInfo, oldCoordSys, newData, newIdx);\n        currStackedPoints.push(stackedOnPoint[0], stackedOnPoint[1]);\n        nextStackedPoints.push(newStackedOnPoints[newIdx2], newStackedOnPoints[newIdx2 + 1]);\n        rawIndices.push(newData.getRawIndex(newIdx));\n        break;\n      case '-':\n        pointAdded = false;\n    }\n    // Original indices\n    if (pointAdded) {\n      status.push(diffItem);\n      sortedIndices.push(sortedIndices.length);\n    }\n  }\n  // Diff result may be crossed if all items are changed\n  // Sort by data index\n  sortedIndices.sort(function (a, b) {\n    return rawIndices[a] - rawIndices[b];\n  });\n  var len = currPoints.length;\n  var sortedCurrPoints = createFloat32Array(len);\n  var sortedNextPoints = createFloat32Array(len);\n  var sortedCurrStackedPoints = createFloat32Array(len);\n  var sortedNextStackedPoints = createFloat32Array(len);\n  var sortedStatus = [];\n  for (var i = 0; i < sortedIndices.length; i++) {\n    var idx = sortedIndices[i];\n    var i2 = i * 2;\n    var idx2 = idx * 2;\n    sortedCurrPoints[i2] = currPoints[idx2];\n    sortedCurrPoints[i2 + 1] = currPoints[idx2 + 1];\n    sortedNextPoints[i2] = nextPoints[idx2];\n    sortedNextPoints[i2 + 1] = nextPoints[idx2 + 1];\n    sortedCurrStackedPoints[i2] = currStackedPoints[idx2];\n    sortedCurrStackedPoints[i2 + 1] = currStackedPoints[idx2 + 1];\n    sortedNextStackedPoints[i2] = nextStackedPoints[idx2];\n    sortedNextStackedPoints[i2 + 1] = nextStackedPoints[idx2 + 1];\n    sortedStatus[i] = status[idx];\n  }\n  return {\n    current: sortedCurrPoints,\n    next: sortedNextPoints,\n    stackedOnCurrent: sortedCurrStackedPoints,\n    stackedOnNext: sortedNextStackedPoints,\n    status: sortedStatus\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,oBAAoB,EAAEC,iBAAiB,QAAQ,aAAa;AACrE,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,QAAQA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAClC,IAAIC,UAAU,GAAG,EAAE;EACnBD,OAAO,CAACE,IAAI,CAACH,OAAO,CAAC,CAACI,GAAG,CAAC,UAAUC,GAAG,EAAE;IACvCH,UAAU,CAACI,IAAI,CAAC;MACdC,GAAG,EAAE,GAAG;MACRF,GAAG,EAAEA;IACP,CAAC,CAAC;EACJ,CAAC,CAAC,CAACG,MAAM,CAAC,UAAUC,MAAM,EAAEC,MAAM,EAAE;IAClCR,UAAU,CAACI,IAAI,CAAC;MACdC,GAAG,EAAE,GAAG;MACRF,GAAG,EAAEK,MAAM;MACXC,IAAI,EAAEF;IACR,CAAC,CAAC;EACJ,CAAC,CAAC,CAACG,MAAM,CAAC,UAAUP,GAAG,EAAE;IACvBH,UAAU,CAACI,IAAI,CAAC;MACdC,GAAG,EAAE,GAAG;MACRF,GAAG,EAAEA;IACP,CAAC,CAAC;EACJ,CAAC,CAAC,CAACQ,OAAO,CAAC,CAAC;EACZ,OAAOX,UAAU;AACnB;AACA,eAAe,SAASY,iBAAiBA,CAACd,OAAO,EAAEC,OAAO,EAAEc,kBAAkB,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEC,cAAc,EAAE;EAC5J,IAAIjB,IAAI,GAAGJ,QAAQ,CAACC,OAAO,EAAEC,OAAO,CAAC;EACrC;EACA;EACA;EACA;EACA;EACA,IAAIoB,UAAU,GAAG,EAAE;EACnB,IAAIC,UAAU,GAAG,EAAE;EACnB;EACA,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,mBAAmB,GAAGhC,oBAAoB,CAACqB,WAAW,EAAEhB,OAAO,EAAEkB,cAAc,CAAC;EACpF;EACA,IAAIU,SAAS,GAAG7B,OAAO,CAAC8B,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE;EACjD,IAAIC,SAAS,GAAG9B,OAAO,CAAC6B,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE;EACjD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7B,IAAI,CAAC8B,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIE,QAAQ,GAAG/B,IAAI,CAAC6B,CAAC,CAAC;IACtB,IAAIG,UAAU,GAAG,IAAI;IACrB,IAAIC,OAAO,GAAG,KAAK,CAAC;IACpB,IAAIC,OAAO,GAAG,KAAK,CAAC;IACpB;IACA;IACA,QAAQH,QAAQ,CAAC3B,GAAG;MAClB,KAAK,GAAG;QACN6B,OAAO,GAAGF,QAAQ,CAAC7B,GAAG,GAAG,CAAC;QAC1BgC,OAAO,GAAGH,QAAQ,CAACvB,IAAI,GAAG,CAAC;QAC3B,IAAI2B,QAAQ,GAAGT,SAAS,CAACO,OAAO,CAAC;QACjC,IAAIG,QAAQ,GAAGV,SAAS,CAACO,OAAO,GAAG,CAAC,CAAC;QACrC,IAAII,KAAK,GAAGT,SAAS,CAACM,OAAO,CAAC;QAC9B,IAAII,KAAK,GAAGV,SAAS,CAACM,OAAO,GAAG,CAAC,CAAC;QAClC;QACA,IAAIK,KAAK,CAACJ,QAAQ,CAAC,IAAII,KAAK,CAACH,QAAQ,CAAC,EAAE;UACtCD,QAAQ,GAAGE,KAAK;UAChBD,QAAQ,GAAGE,KAAK;QAClB;QACApB,UAAU,CAACf,IAAI,CAACgC,QAAQ,EAAEC,QAAQ,CAAC;QACnCjB,UAAU,CAAChB,IAAI,CAACkC,KAAK,EAAEC,KAAK,CAAC;QAC7BlB,iBAAiB,CAACjB,IAAI,CAACS,kBAAkB,CAACqB,OAAO,CAAC,EAAErB,kBAAkB,CAACqB,OAAO,GAAG,CAAC,CAAC,CAAC;QACpFZ,iBAAiB,CAAClB,IAAI,CAACU,kBAAkB,CAACqB,OAAO,CAAC,EAAErB,kBAAkB,CAACqB,OAAO,GAAG,CAAC,CAAC,CAAC;QACpFV,UAAU,CAACrB,IAAI,CAACL,OAAO,CAAC0C,WAAW,CAACT,QAAQ,CAACvB,IAAI,CAAC,CAAC;QACnD;MACF,KAAK,GAAG;QACN,IAAIF,MAAM,GAAGyB,QAAQ,CAAC7B,GAAG;QACzB,IAAIuC,mBAAmB,GAAGhB,mBAAmB,CAACiB,gBAAgB;QAC9D,IAAIC,KAAK,GAAG7B,WAAW,CAAC8B,WAAW,CAAC,CAAC9C,OAAO,CAAC+C,GAAG,CAACJ,mBAAmB,CAAC,CAAC,CAAC,EAAEnC,MAAM,CAAC,EAAER,OAAO,CAAC+C,GAAG,CAACJ,mBAAmB,CAAC,CAAC,CAAC,EAAEnC,MAAM,CAAC,CAAC,CAAC;QAC/H4B,OAAO,GAAG5B,MAAM,GAAG,CAAC;QACpBY,UAAU,CAACf,IAAI,CAACwC,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;QACnCxB,UAAU,CAAChB,IAAI,CAACyB,SAAS,CAACM,OAAO,CAAC,EAAEN,SAAS,CAACM,OAAO,GAAG,CAAC,CAAC,CAAC;QAC3D,IAAIY,cAAc,GAAGpD,iBAAiB,CAAC+B,mBAAmB,EAAEX,WAAW,EAAEhB,OAAO,EAAEQ,MAAM,CAAC;QACzFc,iBAAiB,CAACjB,IAAI,CAAC2C,cAAc,CAAC,CAAC,CAAC,EAAEA,cAAc,CAAC,CAAC,CAAC,CAAC;QAC5DzB,iBAAiB,CAAClB,IAAI,CAACU,kBAAkB,CAACqB,OAAO,CAAC,EAAErB,kBAAkB,CAACqB,OAAO,GAAG,CAAC,CAAC,CAAC;QACpFV,UAAU,CAACrB,IAAI,CAACL,OAAO,CAAC0C,WAAW,CAAClC,MAAM,CAAC,CAAC;QAC5C;MACF,KAAK,GAAG;QACN0B,UAAU,GAAG,KAAK;IACtB;IACA;IACA,IAAIA,UAAU,EAAE;MACdV,MAAM,CAACnB,IAAI,CAAC4B,QAAQ,CAAC;MACrBR,aAAa,CAACpB,IAAI,CAACoB,aAAa,CAACO,MAAM,CAAC;IAC1C;EACF;EACA;EACA;EACAP,aAAa,CAACwB,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACjC,OAAOzB,UAAU,CAACwB,CAAC,CAAC,GAAGxB,UAAU,CAACyB,CAAC,CAAC;EACtC,CAAC,CAAC;EACF,IAAIC,GAAG,GAAGhC,UAAU,CAACY,MAAM;EAC3B,IAAIqB,gBAAgB,GAAGxD,kBAAkB,CAACuD,GAAG,CAAC;EAC9C,IAAIE,gBAAgB,GAAGzD,kBAAkB,CAACuD,GAAG,CAAC;EAC9C,IAAIG,uBAAuB,GAAG1D,kBAAkB,CAACuD,GAAG,CAAC;EACrD,IAAII,uBAAuB,GAAG3D,kBAAkB,CAACuD,GAAG,CAAC;EACrD,IAAIK,YAAY,GAAG,EAAE;EACrB,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,aAAa,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IAC7C,IAAI3B,GAAG,GAAGqB,aAAa,CAACM,CAAC,CAAC;IAC1B,IAAI2B,EAAE,GAAG3B,CAAC,GAAG,CAAC;IACd,IAAI4B,IAAI,GAAGvD,GAAG,GAAG,CAAC;IAClBiD,gBAAgB,CAACK,EAAE,CAAC,GAAGtC,UAAU,CAACuC,IAAI,CAAC;IACvCN,gBAAgB,CAACK,EAAE,GAAG,CAAC,CAAC,GAAGtC,UAAU,CAACuC,IAAI,GAAG,CAAC,CAAC;IAC/CL,gBAAgB,CAACI,EAAE,CAAC,GAAGrC,UAAU,CAACsC,IAAI,CAAC;IACvCL,gBAAgB,CAACI,EAAE,GAAG,CAAC,CAAC,GAAGrC,UAAU,CAACsC,IAAI,GAAG,CAAC,CAAC;IAC/CJ,uBAAuB,CAACG,EAAE,CAAC,GAAGpC,iBAAiB,CAACqC,IAAI,CAAC;IACrDJ,uBAAuB,CAACG,EAAE,GAAG,CAAC,CAAC,GAAGpC,iBAAiB,CAACqC,IAAI,GAAG,CAAC,CAAC;IAC7DH,uBAAuB,CAACE,EAAE,CAAC,GAAGnC,iBAAiB,CAACoC,IAAI,CAAC;IACrDH,uBAAuB,CAACE,EAAE,GAAG,CAAC,CAAC,GAAGnC,iBAAiB,CAACoC,IAAI,GAAG,CAAC,CAAC;IAC7DF,YAAY,CAAC1B,CAAC,CAAC,GAAGP,MAAM,CAACpB,GAAG,CAAC;EAC/B;EACA,OAAO;IACLwD,OAAO,EAAEP,gBAAgB;IACzBQ,IAAI,EAAEP,gBAAgB;IACtBQ,gBAAgB,EAAEP,uBAAuB;IACzCQ,aAAa,EAAEP,uBAAuB;IACtChC,MAAM,EAAEiC;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}