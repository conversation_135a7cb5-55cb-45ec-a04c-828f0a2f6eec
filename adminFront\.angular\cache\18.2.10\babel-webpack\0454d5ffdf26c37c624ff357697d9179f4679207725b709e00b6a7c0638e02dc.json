{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./x64-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./x64-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function (Math) {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var Hasher = C_lib.Hasher;\n    var C_x64 = C.x64;\n    var X64Word = C_x64.Word;\n    var C_algo = C.algo;\n\n    // Constants tables\n    var RHO_OFFSETS = [];\n    var PI_INDEXES = [];\n    var ROUND_CONSTANTS = [];\n\n    // Compute Constants\n    (function () {\n      // Compute rho offset constants\n      var x = 1,\n        y = 0;\n      for (var t = 0; t < 24; t++) {\n        RHO_OFFSETS[x + 5 * y] = (t + 1) * (t + 2) / 2 % 64;\n        var newX = y % 5;\n        var newY = (2 * x + 3 * y) % 5;\n        x = newX;\n        y = newY;\n      }\n\n      // Compute pi index constants\n      for (var x = 0; x < 5; x++) {\n        for (var y = 0; y < 5; y++) {\n          PI_INDEXES[x + 5 * y] = y + (2 * x + 3 * y) % 5 * 5;\n        }\n      }\n\n      // Compute round constants\n      var LFSR = 0x01;\n      for (var i = 0; i < 24; i++) {\n        var roundConstantMsw = 0;\n        var roundConstantLsw = 0;\n        for (var j = 0; j < 7; j++) {\n          if (LFSR & 0x01) {\n            var bitPosition = (1 << j) - 1;\n            if (bitPosition < 32) {\n              roundConstantLsw ^= 1 << bitPosition;\n            } else /* if (bitPosition >= 32) */{\n                roundConstantMsw ^= 1 << bitPosition - 32;\n              }\n          }\n\n          // Compute next LFSR\n          if (LFSR & 0x80) {\n            // Primitive polynomial over GF(2): x^8 + x^6 + x^5 + x^4 + 1\n            LFSR = LFSR << 1 ^ 0x71;\n          } else {\n            LFSR <<= 1;\n          }\n        }\n        ROUND_CONSTANTS[i] = X64Word.create(roundConstantMsw, roundConstantLsw);\n      }\n    })();\n\n    // Reusable objects for temporary values\n    var T = [];\n    (function () {\n      for (var i = 0; i < 25; i++) {\n        T[i] = X64Word.create();\n      }\n    })();\n\n    /**\n     * SHA-3 hash algorithm.\n     */\n    var SHA3 = C_algo.SHA3 = Hasher.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {number} outputLength\n       *   The desired number of bits in the output hash.\n       *   Only values permitted are: 224, 256, 384, 512.\n       *   Default: 512\n       */\n      cfg: Hasher.cfg.extend({\n        outputLength: 512\n      }),\n      _doReset: function () {\n        var state = this._state = [];\n        for (var i = 0; i < 25; i++) {\n          state[i] = new X64Word.init();\n        }\n        this.blockSize = (1600 - 2 * this.cfg.outputLength) / 32;\n      },\n      _doProcessBlock: function (M, offset) {\n        // Shortcuts\n        var state = this._state;\n        var nBlockSizeLanes = this.blockSize / 2;\n\n        // Absorb\n        for (var i = 0; i < nBlockSizeLanes; i++) {\n          // Shortcuts\n          var M2i = M[offset + 2 * i];\n          var M2i1 = M[offset + 2 * i + 1];\n\n          // Swap endian\n          M2i = (M2i << 8 | M2i >>> 24) & 0x00ff00ff | (M2i << 24 | M2i >>> 8) & 0xff00ff00;\n          M2i1 = (M2i1 << 8 | M2i1 >>> 24) & 0x00ff00ff | (M2i1 << 24 | M2i1 >>> 8) & 0xff00ff00;\n\n          // Absorb message into state\n          var lane = state[i];\n          lane.high ^= M2i1;\n          lane.low ^= M2i;\n        }\n\n        // Rounds\n        for (var round = 0; round < 24; round++) {\n          // Theta\n          for (var x = 0; x < 5; x++) {\n            // Mix column lanes\n            var tMsw = 0,\n              tLsw = 0;\n            for (var y = 0; y < 5; y++) {\n              var lane = state[x + 5 * y];\n              tMsw ^= lane.high;\n              tLsw ^= lane.low;\n            }\n\n            // Temporary values\n            var Tx = T[x];\n            Tx.high = tMsw;\n            Tx.low = tLsw;\n          }\n          for (var x = 0; x < 5; x++) {\n            // Shortcuts\n            var Tx4 = T[(x + 4) % 5];\n            var Tx1 = T[(x + 1) % 5];\n            var Tx1Msw = Tx1.high;\n            var Tx1Lsw = Tx1.low;\n\n            // Mix surrounding columns\n            var tMsw = Tx4.high ^ (Tx1Msw << 1 | Tx1Lsw >>> 31);\n            var tLsw = Tx4.low ^ (Tx1Lsw << 1 | Tx1Msw >>> 31);\n            for (var y = 0; y < 5; y++) {\n              var lane = state[x + 5 * y];\n              lane.high ^= tMsw;\n              lane.low ^= tLsw;\n            }\n          }\n\n          // Rho Pi\n          for (var laneIndex = 1; laneIndex < 25; laneIndex++) {\n            var tMsw;\n            var tLsw;\n\n            // Shortcuts\n            var lane = state[laneIndex];\n            var laneMsw = lane.high;\n            var laneLsw = lane.low;\n            var rhoOffset = RHO_OFFSETS[laneIndex];\n\n            // Rotate lanes\n            if (rhoOffset < 32) {\n              tMsw = laneMsw << rhoOffset | laneLsw >>> 32 - rhoOffset;\n              tLsw = laneLsw << rhoOffset | laneMsw >>> 32 - rhoOffset;\n            } else /* if (rhoOffset >= 32) */{\n                tMsw = laneLsw << rhoOffset - 32 | laneMsw >>> 64 - rhoOffset;\n                tLsw = laneMsw << rhoOffset - 32 | laneLsw >>> 64 - rhoOffset;\n              }\n\n            // Transpose lanes\n            var TPiLane = T[PI_INDEXES[laneIndex]];\n            TPiLane.high = tMsw;\n            TPiLane.low = tLsw;\n          }\n\n          // Rho pi at x = y = 0\n          var T0 = T[0];\n          var state0 = state[0];\n          T0.high = state0.high;\n          T0.low = state0.low;\n\n          // Chi\n          for (var x = 0; x < 5; x++) {\n            for (var y = 0; y < 5; y++) {\n              // Shortcuts\n              var laneIndex = x + 5 * y;\n              var lane = state[laneIndex];\n              var TLane = T[laneIndex];\n              var Tx1Lane = T[(x + 1) % 5 + 5 * y];\n              var Tx2Lane = T[(x + 2) % 5 + 5 * y];\n\n              // Mix rows\n              lane.high = TLane.high ^ ~Tx1Lane.high & Tx2Lane.high;\n              lane.low = TLane.low ^ ~Tx1Lane.low & Tx2Lane.low;\n            }\n          }\n\n          // Iota\n          var lane = state[0];\n          var roundConstant = ROUND_CONSTANTS[round];\n          lane.high ^= roundConstant.high;\n          lane.low ^= roundConstant.low;\n        }\n      },\n      _doFinalize: function () {\n        // Shortcuts\n        var data = this._data;\n        var dataWords = data.words;\n        var nBitsTotal = this._nDataBytes * 8;\n        var nBitsLeft = data.sigBytes * 8;\n        var blockSizeBits = this.blockSize * 32;\n\n        // Add padding\n        dataWords[nBitsLeft >>> 5] |= 0x1 << 24 - nBitsLeft % 32;\n        dataWords[(Math.ceil((nBitsLeft + 1) / blockSizeBits) * blockSizeBits >>> 5) - 1] |= 0x80;\n        data.sigBytes = dataWords.length * 4;\n\n        // Hash final blocks\n        this._process();\n\n        // Shortcuts\n        var state = this._state;\n        var outputLengthBytes = this.cfg.outputLength / 8;\n        var outputLengthLanes = outputLengthBytes / 8;\n\n        // Squeeze\n        var hashWords = [];\n        for (var i = 0; i < outputLengthLanes; i++) {\n          // Shortcuts\n          var lane = state[i];\n          var laneMsw = lane.high;\n          var laneLsw = lane.low;\n\n          // Swap endian\n          laneMsw = (laneMsw << 8 | laneMsw >>> 24) & 0x00ff00ff | (laneMsw << 24 | laneMsw >>> 8) & 0xff00ff00;\n          laneLsw = (laneLsw << 8 | laneLsw >>> 24) & 0x00ff00ff | (laneLsw << 24 | laneLsw >>> 8) & 0xff00ff00;\n\n          // Squeeze state to retrieve hash\n          hashWords.push(laneLsw);\n          hashWords.push(laneMsw);\n        }\n\n        // Return final computed hash\n        return new WordArray.init(hashWords, outputLengthBytes);\n      },\n      clone: function () {\n        var clone = Hasher.clone.call(this);\n        var state = clone._state = this._state.slice(0);\n        for (var i = 0; i < 25; i++) {\n          state[i] = state[i].clone();\n        }\n        return clone;\n      }\n    });\n\n    /**\n     * Shortcut function to the hasher's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     *\n     * @return {WordArray} The hash.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hash = CryptoJS.SHA3('message');\n     *     var hash = CryptoJS.SHA3(wordArray);\n     */\n    C.SHA3 = Hasher._createHelper(SHA3);\n\n    /**\n     * Shortcut function to the HMAC's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     * @param {WordArray|string} key The secret key.\n     *\n     * @return {WordArray} The HMAC.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hmac = CryptoJS.HmacSHA3(message, key);\n     */\n    C.HmacSHA3 = Hasher._createHmacHelper(SHA3);\n  })(Math);\n  return CryptoJS.SHA3;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}