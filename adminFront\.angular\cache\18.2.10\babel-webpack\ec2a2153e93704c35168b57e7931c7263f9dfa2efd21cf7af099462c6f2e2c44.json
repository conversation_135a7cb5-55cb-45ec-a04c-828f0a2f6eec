{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport { getSymbolSize, getNodeGlobalScale } from './graphHelper.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getCurvenessForEdge } from '../helper/multipleGraphEdgeHelper.js';\nvar PI = Math.PI;\nvar _symbolRadiansHalf = [];\n/**\r\n * `basedOn` can be:\r\n * 'value':\r\n *     This layout is not accurate and have same bad case. For example,\r\n *     if the min value is very smaller than the max value, the nodes\r\n *     with the min value probably overlap even though there is enough\r\n *     space to layout them. So we only use this approach in the as the\r\n *     init layout of the force layout.\r\n *     FIXME\r\n *     Probably we do not need this method any more but use\r\n *     `basedOn: 'symbolSize'` in force layout if\r\n *     delay its init operations to GraphView.\r\n * 'symbolSize':\r\n *     This approach work only if all of the symbol size calculated.\r\n *     That is, the progressive rendering is not applied to graph.\r\n *     FIXME\r\n *     If progressive rendering is applied to graph some day,\r\n *     probably we have to use `basedOn: 'value'`.\r\n */\nexport function circularLayout(seriesModel, basedOn, draggingNode, pointer) {\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys && coordSys.type !== 'view') {\n    return;\n  }\n  var rect = coordSys.getBoundingRect();\n  var nodeData = seriesModel.getData();\n  var graph = nodeData.graph;\n  var cx = rect.width / 2 + rect.x;\n  var cy = rect.height / 2 + rect.y;\n  var r = Math.min(rect.width, rect.height) / 2;\n  var count = nodeData.count();\n  nodeData.setLayout({\n    cx: cx,\n    cy: cy\n  });\n  if (!count) {\n    return;\n  }\n  if (draggingNode) {\n    var _a = coordSys.pointToData(pointer),\n      tempX = _a[0],\n      tempY = _a[1];\n    var v = [tempX - cx, tempY - cy];\n    vec2.normalize(v, v);\n    vec2.scale(v, v, r);\n    draggingNode.setLayout([cx + v[0], cy + v[1]], true);\n    var circularRotateLabel = seriesModel.get(['circular', 'rotateLabel']);\n    rotateNodeLabel(draggingNode, circularRotateLabel, cx, cy);\n  }\n  _layoutNodesBasedOn[basedOn](seriesModel, graph, nodeData, r, cx, cy, count);\n  graph.eachEdge(function (edge, index) {\n    var curveness = zrUtil.retrieve3(edge.getModel().get(['lineStyle', 'curveness']), getCurvenessForEdge(edge, seriesModel, index), 0);\n    var p1 = vec2.clone(edge.node1.getLayout());\n    var p2 = vec2.clone(edge.node2.getLayout());\n    var cp1;\n    var x12 = (p1[0] + p2[0]) / 2;\n    var y12 = (p1[1] + p2[1]) / 2;\n    if (+curveness) {\n      curveness *= 3;\n      cp1 = [cx * curveness + x12 * (1 - curveness), cy * curveness + y12 * (1 - curveness)];\n    }\n    edge.setLayout([p1, p2, cp1]);\n  });\n}\nvar _layoutNodesBasedOn = {\n  value: function (seriesModel, graph, nodeData, r, cx, cy, count) {\n    var angle = 0;\n    var sum = nodeData.getSum('value');\n    var unitAngle = Math.PI * 2 / (sum || count);\n    graph.eachNode(function (node) {\n      var value = node.getValue('value');\n      var radianHalf = unitAngle * (sum ? value : 1) / 2;\n      angle += radianHalf;\n      node.setLayout([r * Math.cos(angle) + cx, r * Math.sin(angle) + cy]);\n      angle += radianHalf;\n    });\n  },\n  symbolSize: function (seriesModel, graph, nodeData, r, cx, cy, count) {\n    var sumRadian = 0;\n    _symbolRadiansHalf.length = count;\n    var nodeScale = getNodeGlobalScale(seriesModel);\n    graph.eachNode(function (node) {\n      var symbolSize = getSymbolSize(node);\n      // Normally this case will not happen, but we still add\n      // some the defensive code (2px is an arbitrary value).\n      isNaN(symbolSize) && (symbolSize = 2);\n      symbolSize < 0 && (symbolSize = 0);\n      symbolSize *= nodeScale;\n      var symbolRadianHalf = Math.asin(symbolSize / 2 / r);\n      // when `symbolSize / 2` is bigger than `r`.\n      isNaN(symbolRadianHalf) && (symbolRadianHalf = PI / 2);\n      _symbolRadiansHalf[node.dataIndex] = symbolRadianHalf;\n      sumRadian += symbolRadianHalf * 2;\n    });\n    var halfRemainRadian = (2 * PI - sumRadian) / count / 2;\n    var angle = 0;\n    graph.eachNode(function (node) {\n      var radianHalf = halfRemainRadian + _symbolRadiansHalf[node.dataIndex];\n      angle += radianHalf;\n      // init circular layout for\n      // 1. layout undefined node\n      // 2. not fixed node\n      (!node.getLayout() || !node.getLayout().fixed) && node.setLayout([r * Math.cos(angle) + cx, r * Math.sin(angle) + cy]);\n      angle += radianHalf;\n    });\n  }\n};\nexport function rotateNodeLabel(node, circularRotateLabel, cx, cy) {\n  var el = node.getGraphicEl();\n  // need to check if el exists. '-' value may not create node element.\n  if (!el) {\n    return;\n  }\n  var nodeModel = node.getModel();\n  var labelRotate = nodeModel.get(['label', 'rotate']) || 0;\n  var symbolPath = el.getSymbolPath();\n  if (circularRotateLabel) {\n    var pos = node.getLayout();\n    var rad = Math.atan2(pos[1] - cy, pos[0] - cx);\n    if (rad < 0) {\n      rad = Math.PI * 2 + rad;\n    }\n    var isLeft = pos[0] < cx;\n    if (isLeft) {\n      rad = rad - Math.PI;\n    }\n    var textPosition = isLeft ? 'left' : 'right';\n    symbolPath.setTextConfig({\n      rotation: -rad,\n      position: textPosition,\n      origin: 'center'\n    });\n    var emphasisState = symbolPath.ensureState('emphasis');\n    zrUtil.extend(emphasisState.textConfig || (emphasisState.textConfig = {}), {\n      position: textPosition\n    });\n  } else {\n    symbolPath.setTextConfig({\n      rotation: labelRotate *= Math.PI / 180\n    });\n  }\n}", "map": {"version": 3, "names": ["vec2", "getSymbolSize", "getNodeGlobalScale", "zrUtil", "getCurvenessForEdge", "PI", "Math", "_symbolRadiansHalf", "circularLayout", "seriesModel", "basedOn", "draggingNode", "pointer", "coordSys", "coordinateSystem", "type", "rect", "getBoundingRect", "nodeData", "getData", "graph", "cx", "width", "x", "cy", "height", "y", "r", "min", "count", "setLayout", "_a", "pointToData", "tempX", "tempY", "v", "normalize", "scale", "circularRotateLabel", "get", "rotateNodeLabel", "_layoutNodesBasedOn", "eachEdge", "edge", "index", "curveness", "retrieve3", "getModel", "p1", "clone", "node1", "getLayout", "p2", "node2", "cp1", "x12", "y12", "value", "angle", "sum", "getSum", "unitAngle", "eachNode", "node", "getValue", "radianHalf", "cos", "sin", "symbolSize", "sumRadian", "length", "nodeScale", "isNaN", "symbolRadianHalf", "asin", "dataIndex", "halfRemainRadian", "fixed", "el", "getGraphicEl", "nodeModel", "labelRotate", "symbolPath", "getSymbolPath", "pos", "rad", "atan2", "isLeft", "textPosition", "setTextConfig", "rotation", "position", "origin", "emphasisState", "ensureState", "extend", "textConfig"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/graph/circularLayoutHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport { getSymbolSize, getNodeGlobalScale } from './graphHelper.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getCurvenessForEdge } from '../helper/multipleGraphEdgeHelper.js';\nvar PI = Math.PI;\nvar _symbolRadiansHalf = [];\n/**\r\n * `basedOn` can be:\r\n * 'value':\r\n *     This layout is not accurate and have same bad case. For example,\r\n *     if the min value is very smaller than the max value, the nodes\r\n *     with the min value probably overlap even though there is enough\r\n *     space to layout them. So we only use this approach in the as the\r\n *     init layout of the force layout.\r\n *     FIXME\r\n *     Probably we do not need this method any more but use\r\n *     `basedOn: 'symbolSize'` in force layout if\r\n *     delay its init operations to GraphView.\r\n * 'symbolSize':\r\n *     This approach work only if all of the symbol size calculated.\r\n *     That is, the progressive rendering is not applied to graph.\r\n *     FIXME\r\n *     If progressive rendering is applied to graph some day,\r\n *     probably we have to use `basedOn: 'value'`.\r\n */\nexport function circularLayout(seriesModel, basedOn, draggingNode, pointer) {\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys && coordSys.type !== 'view') {\n    return;\n  }\n  var rect = coordSys.getBoundingRect();\n  var nodeData = seriesModel.getData();\n  var graph = nodeData.graph;\n  var cx = rect.width / 2 + rect.x;\n  var cy = rect.height / 2 + rect.y;\n  var r = Math.min(rect.width, rect.height) / 2;\n  var count = nodeData.count();\n  nodeData.setLayout({\n    cx: cx,\n    cy: cy\n  });\n  if (!count) {\n    return;\n  }\n  if (draggingNode) {\n    var _a = coordSys.pointToData(pointer),\n      tempX = _a[0],\n      tempY = _a[1];\n    var v = [tempX - cx, tempY - cy];\n    vec2.normalize(v, v);\n    vec2.scale(v, v, r);\n    draggingNode.setLayout([cx + v[0], cy + v[1]], true);\n    var circularRotateLabel = seriesModel.get(['circular', 'rotateLabel']);\n    rotateNodeLabel(draggingNode, circularRotateLabel, cx, cy);\n  }\n  _layoutNodesBasedOn[basedOn](seriesModel, graph, nodeData, r, cx, cy, count);\n  graph.eachEdge(function (edge, index) {\n    var curveness = zrUtil.retrieve3(edge.getModel().get(['lineStyle', 'curveness']), getCurvenessForEdge(edge, seriesModel, index), 0);\n    var p1 = vec2.clone(edge.node1.getLayout());\n    var p2 = vec2.clone(edge.node2.getLayout());\n    var cp1;\n    var x12 = (p1[0] + p2[0]) / 2;\n    var y12 = (p1[1] + p2[1]) / 2;\n    if (+curveness) {\n      curveness *= 3;\n      cp1 = [cx * curveness + x12 * (1 - curveness), cy * curveness + y12 * (1 - curveness)];\n    }\n    edge.setLayout([p1, p2, cp1]);\n  });\n}\nvar _layoutNodesBasedOn = {\n  value: function (seriesModel, graph, nodeData, r, cx, cy, count) {\n    var angle = 0;\n    var sum = nodeData.getSum('value');\n    var unitAngle = Math.PI * 2 / (sum || count);\n    graph.eachNode(function (node) {\n      var value = node.getValue('value');\n      var radianHalf = unitAngle * (sum ? value : 1) / 2;\n      angle += radianHalf;\n      node.setLayout([r * Math.cos(angle) + cx, r * Math.sin(angle) + cy]);\n      angle += radianHalf;\n    });\n  },\n  symbolSize: function (seriesModel, graph, nodeData, r, cx, cy, count) {\n    var sumRadian = 0;\n    _symbolRadiansHalf.length = count;\n    var nodeScale = getNodeGlobalScale(seriesModel);\n    graph.eachNode(function (node) {\n      var symbolSize = getSymbolSize(node);\n      // Normally this case will not happen, but we still add\n      // some the defensive code (2px is an arbitrary value).\n      isNaN(symbolSize) && (symbolSize = 2);\n      symbolSize < 0 && (symbolSize = 0);\n      symbolSize *= nodeScale;\n      var symbolRadianHalf = Math.asin(symbolSize / 2 / r);\n      // when `symbolSize / 2` is bigger than `r`.\n      isNaN(symbolRadianHalf) && (symbolRadianHalf = PI / 2);\n      _symbolRadiansHalf[node.dataIndex] = symbolRadianHalf;\n      sumRadian += symbolRadianHalf * 2;\n    });\n    var halfRemainRadian = (2 * PI - sumRadian) / count / 2;\n    var angle = 0;\n    graph.eachNode(function (node) {\n      var radianHalf = halfRemainRadian + _symbolRadiansHalf[node.dataIndex];\n      angle += radianHalf;\n      // init circular layout for\n      // 1. layout undefined node\n      // 2. not fixed node\n      (!node.getLayout() || !node.getLayout().fixed) && node.setLayout([r * Math.cos(angle) + cx, r * Math.sin(angle) + cy]);\n      angle += radianHalf;\n    });\n  }\n};\nexport function rotateNodeLabel(node, circularRotateLabel, cx, cy) {\n  var el = node.getGraphicEl();\n  // need to check if el exists. '-' value may not create node element.\n  if (!el) {\n    return;\n  }\n  var nodeModel = node.getModel();\n  var labelRotate = nodeModel.get(['label', 'rotate']) || 0;\n  var symbolPath = el.getSymbolPath();\n  if (circularRotateLabel) {\n    var pos = node.getLayout();\n    var rad = Math.atan2(pos[1] - cy, pos[0] - cx);\n    if (rad < 0) {\n      rad = Math.PI * 2 + rad;\n    }\n    var isLeft = pos[0] < cx;\n    if (isLeft) {\n      rad = rad - Math.PI;\n    }\n    var textPosition = isLeft ? 'left' : 'right';\n    symbolPath.setTextConfig({\n      rotation: -rad,\n      position: textPosition,\n      origin: 'center'\n    });\n    var emphasisState = symbolPath.ensureState('emphasis');\n    zrUtil.extend(emphasisState.textConfig || (emphasisState.textConfig = {}), {\n      position: textPosition\n    });\n  } else {\n    symbolPath.setTextConfig({\n      rotation: labelRotate *= Math.PI / 180\n    });\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,IAAI,MAAM,4BAA4B;AAClD,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,kBAAkB;AACpE,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,IAAIC,EAAE,GAAGC,IAAI,CAACD,EAAE;AAChB,IAAIE,kBAAkB,GAAG,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,WAAW,EAAEC,OAAO,EAAEC,YAAY,EAAEC,OAAO,EAAE;EAC1E,IAAIC,QAAQ,GAAGJ,WAAW,CAACK,gBAAgB;EAC3C,IAAID,QAAQ,IAAIA,QAAQ,CAACE,IAAI,KAAK,MAAM,EAAE;IACxC;EACF;EACA,IAAIC,IAAI,GAAGH,QAAQ,CAACI,eAAe,CAAC,CAAC;EACrC,IAAIC,QAAQ,GAAGT,WAAW,CAACU,OAAO,CAAC,CAAC;EACpC,IAAIC,KAAK,GAAGF,QAAQ,CAACE,KAAK;EAC1B,IAAIC,EAAE,GAAGL,IAAI,CAACM,KAAK,GAAG,CAAC,GAAGN,IAAI,CAACO,CAAC;EAChC,IAAIC,EAAE,GAAGR,IAAI,CAACS,MAAM,GAAG,CAAC,GAAGT,IAAI,CAACU,CAAC;EACjC,IAAIC,CAAC,GAAGrB,IAAI,CAACsB,GAAG,CAACZ,IAAI,CAACM,KAAK,EAAEN,IAAI,CAACS,MAAM,CAAC,GAAG,CAAC;EAC7C,IAAII,KAAK,GAAGX,QAAQ,CAACW,KAAK,CAAC,CAAC;EAC5BX,QAAQ,CAACY,SAAS,CAAC;IACjBT,EAAE,EAAEA,EAAE;IACNG,EAAE,EAAEA;EACN,CAAC,CAAC;EACF,IAAI,CAACK,KAAK,EAAE;IACV;EACF;EACA,IAAIlB,YAAY,EAAE;IAChB,IAAIoB,EAAE,GAAGlB,QAAQ,CAACmB,WAAW,CAACpB,OAAO,CAAC;MACpCqB,KAAK,GAAGF,EAAE,CAAC,CAAC,CAAC;MACbG,KAAK,GAAGH,EAAE,CAAC,CAAC,CAAC;IACf,IAAII,CAAC,GAAG,CAACF,KAAK,GAAGZ,EAAE,EAAEa,KAAK,GAAGV,EAAE,CAAC;IAChCxB,IAAI,CAACoC,SAAS,CAACD,CAAC,EAAEA,CAAC,CAAC;IACpBnC,IAAI,CAACqC,KAAK,CAACF,CAAC,EAAEA,CAAC,EAAER,CAAC,CAAC;IACnBhB,YAAY,CAACmB,SAAS,CAAC,CAACT,EAAE,GAAGc,CAAC,CAAC,CAAC,CAAC,EAAEX,EAAE,GAAGW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;IACpD,IAAIG,mBAAmB,GAAG7B,WAAW,CAAC8B,GAAG,CAAC,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;IACtEC,eAAe,CAAC7B,YAAY,EAAE2B,mBAAmB,EAAEjB,EAAE,EAAEG,EAAE,CAAC;EAC5D;EACAiB,mBAAmB,CAAC/B,OAAO,CAAC,CAACD,WAAW,EAAEW,KAAK,EAAEF,QAAQ,EAAES,CAAC,EAAEN,EAAE,EAAEG,EAAE,EAAEK,KAAK,CAAC;EAC5ET,KAAK,CAACsB,QAAQ,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACpC,IAAIC,SAAS,GAAG1C,MAAM,CAAC2C,SAAS,CAACH,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACR,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,EAAEnC,mBAAmB,CAACuC,IAAI,EAAElC,WAAW,EAAEmC,KAAK,CAAC,EAAE,CAAC,CAAC;IACnI,IAAII,EAAE,GAAGhD,IAAI,CAACiD,KAAK,CAACN,IAAI,CAACO,KAAK,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3C,IAAIC,EAAE,GAAGpD,IAAI,CAACiD,KAAK,CAACN,IAAI,CAACU,KAAK,CAACF,SAAS,CAAC,CAAC,CAAC;IAC3C,IAAIG,GAAG;IACP,IAAIC,GAAG,GAAG,CAACP,EAAE,CAAC,CAAC,CAAC,GAAGI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7B,IAAII,GAAG,GAAG,CAACR,EAAE,CAAC,CAAC,CAAC,GAAGI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7B,IAAI,CAACP,SAAS,EAAE;MACdA,SAAS,IAAI,CAAC;MACdS,GAAG,GAAG,CAACjC,EAAE,GAAGwB,SAAS,GAAGU,GAAG,IAAI,CAAC,GAAGV,SAAS,CAAC,EAAErB,EAAE,GAAGqB,SAAS,GAAGW,GAAG,IAAI,CAAC,GAAGX,SAAS,CAAC,CAAC;IACxF;IACAF,IAAI,CAACb,SAAS,CAAC,CAACkB,EAAE,EAAEI,EAAE,EAAEE,GAAG,CAAC,CAAC;EAC/B,CAAC,CAAC;AACJ;AACA,IAAIb,mBAAmB,GAAG;EACxBgB,KAAK,EAAE,SAAAA,CAAUhD,WAAW,EAAEW,KAAK,EAAEF,QAAQ,EAAES,CAAC,EAAEN,EAAE,EAAEG,EAAE,EAAEK,KAAK,EAAE;IAC/D,IAAI6B,KAAK,GAAG,CAAC;IACb,IAAIC,GAAG,GAAGzC,QAAQ,CAAC0C,MAAM,CAAC,OAAO,CAAC;IAClC,IAAIC,SAAS,GAAGvD,IAAI,CAACD,EAAE,GAAG,CAAC,IAAIsD,GAAG,IAAI9B,KAAK,CAAC;IAC5CT,KAAK,CAAC0C,QAAQ,CAAC,UAAUC,IAAI,EAAE;MAC7B,IAAIN,KAAK,GAAGM,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC;MAClC,IAAIC,UAAU,GAAGJ,SAAS,IAAIF,GAAG,GAAGF,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAClDC,KAAK,IAAIO,UAAU;MACnBF,IAAI,CAACjC,SAAS,CAAC,CAACH,CAAC,GAAGrB,IAAI,CAAC4D,GAAG,CAACR,KAAK,CAAC,GAAGrC,EAAE,EAAEM,CAAC,GAAGrB,IAAI,CAAC6D,GAAG,CAACT,KAAK,CAAC,GAAGlC,EAAE,CAAC,CAAC;MACpEkC,KAAK,IAAIO,UAAU;IACrB,CAAC,CAAC;EACJ,CAAC;EACDG,UAAU,EAAE,SAAAA,CAAU3D,WAAW,EAAEW,KAAK,EAAEF,QAAQ,EAAES,CAAC,EAAEN,EAAE,EAAEG,EAAE,EAAEK,KAAK,EAAE;IACpE,IAAIwC,SAAS,GAAG,CAAC;IACjB9D,kBAAkB,CAAC+D,MAAM,GAAGzC,KAAK;IACjC,IAAI0C,SAAS,GAAGrE,kBAAkB,CAACO,WAAW,CAAC;IAC/CW,KAAK,CAAC0C,QAAQ,CAAC,UAAUC,IAAI,EAAE;MAC7B,IAAIK,UAAU,GAAGnE,aAAa,CAAC8D,IAAI,CAAC;MACpC;MACA;MACAS,KAAK,CAACJ,UAAU,CAAC,KAAKA,UAAU,GAAG,CAAC,CAAC;MACrCA,UAAU,GAAG,CAAC,KAAKA,UAAU,GAAG,CAAC,CAAC;MAClCA,UAAU,IAAIG,SAAS;MACvB,IAAIE,gBAAgB,GAAGnE,IAAI,CAACoE,IAAI,CAACN,UAAU,GAAG,CAAC,GAAGzC,CAAC,CAAC;MACpD;MACA6C,KAAK,CAACC,gBAAgB,CAAC,KAAKA,gBAAgB,GAAGpE,EAAE,GAAG,CAAC,CAAC;MACtDE,kBAAkB,CAACwD,IAAI,CAACY,SAAS,CAAC,GAAGF,gBAAgB;MACrDJ,SAAS,IAAII,gBAAgB,GAAG,CAAC;IACnC,CAAC,CAAC;IACF,IAAIG,gBAAgB,GAAG,CAAC,CAAC,GAAGvE,EAAE,GAAGgE,SAAS,IAAIxC,KAAK,GAAG,CAAC;IACvD,IAAI6B,KAAK,GAAG,CAAC;IACbtC,KAAK,CAAC0C,QAAQ,CAAC,UAAUC,IAAI,EAAE;MAC7B,IAAIE,UAAU,GAAGW,gBAAgB,GAAGrE,kBAAkB,CAACwD,IAAI,CAACY,SAAS,CAAC;MACtEjB,KAAK,IAAIO,UAAU;MACnB;MACA;MACA;MACA,CAAC,CAACF,IAAI,CAACZ,SAAS,CAAC,CAAC,IAAI,CAACY,IAAI,CAACZ,SAAS,CAAC,CAAC,CAAC0B,KAAK,KAAKd,IAAI,CAACjC,SAAS,CAAC,CAACH,CAAC,GAAGrB,IAAI,CAAC4D,GAAG,CAACR,KAAK,CAAC,GAAGrC,EAAE,EAAEM,CAAC,GAAGrB,IAAI,CAAC6D,GAAG,CAACT,KAAK,CAAC,GAAGlC,EAAE,CAAC,CAAC;MACtHkC,KAAK,IAAIO,UAAU;IACrB,CAAC,CAAC;EACJ;AACF,CAAC;AACD,OAAO,SAASzB,eAAeA,CAACuB,IAAI,EAAEzB,mBAAmB,EAAEjB,EAAE,EAAEG,EAAE,EAAE;EACjE,IAAIsD,EAAE,GAAGf,IAAI,CAACgB,YAAY,CAAC,CAAC;EAC5B;EACA,IAAI,CAACD,EAAE,EAAE;IACP;EACF;EACA,IAAIE,SAAS,GAAGjB,IAAI,CAAChB,QAAQ,CAAC,CAAC;EAC/B,IAAIkC,WAAW,GAAGD,SAAS,CAACzC,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC;EACzD,IAAI2C,UAAU,GAAGJ,EAAE,CAACK,aAAa,CAAC,CAAC;EACnC,IAAI7C,mBAAmB,EAAE;IACvB,IAAI8C,GAAG,GAAGrB,IAAI,CAACZ,SAAS,CAAC,CAAC;IAC1B,IAAIkC,GAAG,GAAG/E,IAAI,CAACgF,KAAK,CAACF,GAAG,CAAC,CAAC,CAAC,GAAG5D,EAAE,EAAE4D,GAAG,CAAC,CAAC,CAAC,GAAG/D,EAAE,CAAC;IAC9C,IAAIgE,GAAG,GAAG,CAAC,EAAE;MACXA,GAAG,GAAG/E,IAAI,CAACD,EAAE,GAAG,CAAC,GAAGgF,GAAG;IACzB;IACA,IAAIE,MAAM,GAAGH,GAAG,CAAC,CAAC,CAAC,GAAG/D,EAAE;IACxB,IAAIkE,MAAM,EAAE;MACVF,GAAG,GAAGA,GAAG,GAAG/E,IAAI,CAACD,EAAE;IACrB;IACA,IAAImF,YAAY,GAAGD,MAAM,GAAG,MAAM,GAAG,OAAO;IAC5CL,UAAU,CAACO,aAAa,CAAC;MACvBC,QAAQ,EAAE,CAACL,GAAG;MACdM,QAAQ,EAAEH,YAAY;MACtBI,MAAM,EAAE;IACV,CAAC,CAAC;IACF,IAAIC,aAAa,GAAGX,UAAU,CAACY,WAAW,CAAC,UAAU,CAAC;IACtD3F,MAAM,CAAC4F,MAAM,CAACF,aAAa,CAACG,UAAU,KAAKH,aAAa,CAACG,UAAU,GAAG,CAAC,CAAC,CAAC,EAAE;MACzEL,QAAQ,EAAEH;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLN,UAAU,CAACO,aAAa,CAAC;MACvBC,QAAQ,EAAET,WAAW,IAAI3E,IAAI,CAACD,EAAE,GAAG;IACrC,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}