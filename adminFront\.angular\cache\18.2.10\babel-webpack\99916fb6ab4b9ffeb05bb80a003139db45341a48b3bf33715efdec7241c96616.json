{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { each } from 'zrender/lib/core/util.js';\nvar BaseBarSeriesModel = /** @class */function (_super) {\n  __extends(BaseBarSeriesModel, _super);\n  function BaseBarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BaseBarSeriesModel.type;\n    return _this;\n  }\n  BaseBarSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  BaseBarSeriesModel.prototype.getMarkerPosition = function (value, dims, startingAtTick) {\n    var coordSys = this.coordinateSystem;\n    if (coordSys && coordSys.clampData) {\n      // PENDING if clamp ?\n      var clampData_1 = coordSys.clampData(value);\n      var pt_1 = coordSys.dataToPoint(clampData_1);\n      if (startingAtTick) {\n        each(coordSys.getAxes(), function (axis, idx) {\n          // If axis type is category, use tick coords instead\n          if (axis.type === 'category' && dims != null) {\n            var tickCoords = axis.getTicksCoords();\n            var alignTicksWithLabel = axis.getTickModel().get('alignWithLabel');\n            var targetTickId = clampData_1[idx];\n            // The index of rightmost tick of markArea is 1 larger than x1/y1 index\n            var isEnd = dims[idx] === 'x1' || dims[idx] === 'y1';\n            if (isEnd && !alignTicksWithLabel) {\n              targetTickId += 1;\n            }\n            // The only contains one tick, tickCoords is\n            // like [{coord: 0, tickValue: 0}, {coord: 0}]\n            // to the length should always be larger than 1\n            if (tickCoords.length < 2) {\n              return;\n            } else if (tickCoords.length === 2) {\n              // The left value and right value of the axis are\n              // the same. coord is 0 in both items. Use the max\n              // value of the axis as the coord\n              pt_1[idx] = axis.toGlobalCoord(axis.getExtent()[isEnd ? 1 : 0]);\n              return;\n            }\n            var leftCoord = void 0;\n            var coord = void 0;\n            var stepTickValue = 1;\n            for (var i = 0; i < tickCoords.length; i++) {\n              var tickCoord = tickCoords[i].coord;\n              // The last item of tickCoords doesn't contain\n              // tickValue\n              var tickValue = i === tickCoords.length - 1 ? tickCoords[i - 1].tickValue + stepTickValue : tickCoords[i].tickValue;\n              if (tickValue === targetTickId) {\n                coord = tickCoord;\n                break;\n              } else if (tickValue < targetTickId) {\n                leftCoord = tickCoord;\n              } else if (leftCoord != null && tickValue > targetTickId) {\n                coord = (tickCoord + leftCoord) / 2;\n                break;\n              }\n              if (i === 1) {\n                // Here we assume the step of category axes is\n                // the same\n                stepTickValue = tickValue - tickCoords[0].tickValue;\n              }\n            }\n            if (coord == null) {\n              if (!leftCoord) {\n                // targetTickId is smaller than all tick ids in the\n                // visible area, use the leftmost tick coord\n                coord = tickCoords[0].coord;\n              } else if (leftCoord) {\n                // targetTickId is larger than all tick ids in the\n                // visible area, use the rightmost tick coord\n                coord = tickCoords[tickCoords.length - 1].coord;\n              }\n            }\n            pt_1[idx] = axis.toGlobalCoord(coord);\n          }\n        });\n      } else {\n        var data = this.getData();\n        var offset = data.getLayout('offset');\n        var size = data.getLayout('size');\n        var offsetIndex = coordSys.getBaseAxis().isHorizontal() ? 0 : 1;\n        pt_1[offsetIndex] += offset + size / 2;\n      }\n      return pt_1;\n    }\n    return [NaN, NaN];\n  };\n  BaseBarSeriesModel.type = 'series.__base_bar__';\n  BaseBarSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    // stack: null\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    barMinHeight: 0,\n    barMinAngle: 0,\n    // cursor: null,\n    large: false,\n    largeThreshold: 400,\n    progressive: 3e3,\n    progressiveChunkMode: 'mod'\n  };\n  return BaseBarSeriesModel;\n}(SeriesModel);\nSeriesModel.registerClass(BaseBarSeriesModel);\nexport default BaseBarSeriesModel;", "map": {"version": 3, "names": ["__extends", "SeriesModel", "createSeriesData", "each", "BaseBarSeriesModel", "_super", "_this", "apply", "arguments", "type", "prototype", "getInitialData", "option", "ecModel", "useEncodeDefaulter", "getMarkerPosition", "value", "dims", "startingAtTick", "coordSys", "coordinateSystem", "clampData", "clampData_1", "pt_1", "dataToPoint", "getAxes", "axis", "idx", "tickCoords", "getTicksCoords", "alignTicksWithLabel", "getTickModel", "get", "targetTickId", "isEnd", "length", "toGlobalCoord", "getExtent", "leftCoord", "coord", "step<PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "tickCoord", "tickValue", "data", "getData", "offset", "getLayout", "size", "offsetIndex", "getBaseAxis", "isHorizontal", "NaN", "defaultOption", "z", "legendHoverLink", "barMinHeight", "barMinAngle", "large", "largeThreshold", "progressive", "progressiveChunkMode", "registerClass"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/chart/bar/BaseBarSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { each } from 'zrender/lib/core/util.js';\nvar BaseBarSeriesModel = /** @class */function (_super) {\n  __extends(BaseBarSeriesModel, _super);\n  function BaseBarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BaseBarSeriesModel.type;\n    return _this;\n  }\n  BaseBarSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  BaseBarSeriesModel.prototype.getMarkerPosition = function (value, dims, startingAtTick) {\n    var coordSys = this.coordinateSystem;\n    if (coordSys && coordSys.clampData) {\n      // PENDING if clamp ?\n      var clampData_1 = coordSys.clampData(value);\n      var pt_1 = coordSys.dataToPoint(clampData_1);\n      if (startingAtTick) {\n        each(coordSys.getAxes(), function (axis, idx) {\n          // If axis type is category, use tick coords instead\n          if (axis.type === 'category' && dims != null) {\n            var tickCoords = axis.getTicksCoords();\n            var alignTicksWithLabel = axis.getTickModel().get('alignWithLabel');\n            var targetTickId = clampData_1[idx];\n            // The index of rightmost tick of markArea is 1 larger than x1/y1 index\n            var isEnd = dims[idx] === 'x1' || dims[idx] === 'y1';\n            if (isEnd && !alignTicksWithLabel) {\n              targetTickId += 1;\n            }\n            // The only contains one tick, tickCoords is\n            // like [{coord: 0, tickValue: 0}, {coord: 0}]\n            // to the length should always be larger than 1\n            if (tickCoords.length < 2) {\n              return;\n            } else if (tickCoords.length === 2) {\n              // The left value and right value of the axis are\n              // the same. coord is 0 in both items. Use the max\n              // value of the axis as the coord\n              pt_1[idx] = axis.toGlobalCoord(axis.getExtent()[isEnd ? 1 : 0]);\n              return;\n            }\n            var leftCoord = void 0;\n            var coord = void 0;\n            var stepTickValue = 1;\n            for (var i = 0; i < tickCoords.length; i++) {\n              var tickCoord = tickCoords[i].coord;\n              // The last item of tickCoords doesn't contain\n              // tickValue\n              var tickValue = i === tickCoords.length - 1 ? tickCoords[i - 1].tickValue + stepTickValue : tickCoords[i].tickValue;\n              if (tickValue === targetTickId) {\n                coord = tickCoord;\n                break;\n              } else if (tickValue < targetTickId) {\n                leftCoord = tickCoord;\n              } else if (leftCoord != null && tickValue > targetTickId) {\n                coord = (tickCoord + leftCoord) / 2;\n                break;\n              }\n              if (i === 1) {\n                // Here we assume the step of category axes is\n                // the same\n                stepTickValue = tickValue - tickCoords[0].tickValue;\n              }\n            }\n            if (coord == null) {\n              if (!leftCoord) {\n                // targetTickId is smaller than all tick ids in the\n                // visible area, use the leftmost tick coord\n                coord = tickCoords[0].coord;\n              } else if (leftCoord) {\n                // targetTickId is larger than all tick ids in the\n                // visible area, use the rightmost tick coord\n                coord = tickCoords[tickCoords.length - 1].coord;\n              }\n            }\n            pt_1[idx] = axis.toGlobalCoord(coord);\n          }\n        });\n      } else {\n        var data = this.getData();\n        var offset = data.getLayout('offset');\n        var size = data.getLayout('size');\n        var offsetIndex = coordSys.getBaseAxis().isHorizontal() ? 0 : 1;\n        pt_1[offsetIndex] += offset + size / 2;\n      }\n      return pt_1;\n    }\n    return [NaN, NaN];\n  };\n  BaseBarSeriesModel.type = 'series.__base_bar__';\n  BaseBarSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    // stack: null\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    barMinHeight: 0,\n    barMinAngle: 0,\n    // cursor: null,\n    large: false,\n    largeThreshold: 400,\n    progressive: 3e3,\n    progressiveChunkMode: 'mod'\n  };\n  return BaseBarSeriesModel;\n}(SeriesModel);\nSeriesModel.registerClass(BaseBarSeriesModel);\nexport default BaseBarSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,IAAIC,kBAAkB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACtDL,SAAS,CAACI,kBAAkB,EAAEC,MAAM,CAAC;EACrC,SAASD,kBAAkBA,CAAA,EAAG;IAC5B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,kBAAkB,CAACK,IAAI;IACpC,OAAOH,KAAK;EACd;EACAF,kBAAkB,CAACM,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACvE,OAAOX,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE;MAClCY,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EACDV,kBAAkB,CAACM,SAAS,CAACK,iBAAiB,GAAG,UAAUC,KAAK,EAAEC,IAAI,EAAEC,cAAc,EAAE;IACtF,IAAIC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;IACpC,IAAID,QAAQ,IAAIA,QAAQ,CAACE,SAAS,EAAE;MAClC;MACA,IAAIC,WAAW,GAAGH,QAAQ,CAACE,SAAS,CAACL,KAAK,CAAC;MAC3C,IAAIO,IAAI,GAAGJ,QAAQ,CAACK,WAAW,CAACF,WAAW,CAAC;MAC5C,IAAIJ,cAAc,EAAE;QAClBf,IAAI,CAACgB,QAAQ,CAACM,OAAO,CAAC,CAAC,EAAE,UAAUC,IAAI,EAAEC,GAAG,EAAE;UAC5C;UACA,IAAID,IAAI,CAACjB,IAAI,KAAK,UAAU,IAAIQ,IAAI,IAAI,IAAI,EAAE;YAC5C,IAAIW,UAAU,GAAGF,IAAI,CAACG,cAAc,CAAC,CAAC;YACtC,IAAIC,mBAAmB,GAAGJ,IAAI,CAACK,YAAY,CAAC,CAAC,CAACC,GAAG,CAAC,gBAAgB,CAAC;YACnE,IAAIC,YAAY,GAAGX,WAAW,CAACK,GAAG,CAAC;YACnC;YACA,IAAIO,KAAK,GAAGjB,IAAI,CAACU,GAAG,CAAC,KAAK,IAAI,IAAIV,IAAI,CAACU,GAAG,CAAC,KAAK,IAAI;YACpD,IAAIO,KAAK,IAAI,CAACJ,mBAAmB,EAAE;cACjCG,YAAY,IAAI,CAAC;YACnB;YACA;YACA;YACA;YACA,IAAIL,UAAU,CAACO,MAAM,GAAG,CAAC,EAAE;cACzB;YACF,CAAC,MAAM,IAAIP,UAAU,CAACO,MAAM,KAAK,CAAC,EAAE;cAClC;cACA;cACA;cACAZ,IAAI,CAACI,GAAG,CAAC,GAAGD,IAAI,CAACU,aAAa,CAACV,IAAI,CAACW,SAAS,CAAC,CAAC,CAACH,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;cAC/D;YACF;YACA,IAAII,SAAS,GAAG,KAAK,CAAC;YACtB,IAAIC,KAAK,GAAG,KAAK,CAAC;YAClB,IAAIC,aAAa,GAAG,CAAC;YACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,UAAU,CAACO,MAAM,EAAEM,CAAC,EAAE,EAAE;cAC1C,IAAIC,SAAS,GAAGd,UAAU,CAACa,CAAC,CAAC,CAACF,KAAK;cACnC;cACA;cACA,IAAII,SAAS,GAAGF,CAAC,KAAKb,UAAU,CAACO,MAAM,GAAG,CAAC,GAAGP,UAAU,CAACa,CAAC,GAAG,CAAC,CAAC,CAACE,SAAS,GAAGH,aAAa,GAAGZ,UAAU,CAACa,CAAC,CAAC,CAACE,SAAS;cACnH,IAAIA,SAAS,KAAKV,YAAY,EAAE;gBAC9BM,KAAK,GAAGG,SAAS;gBACjB;cACF,CAAC,MAAM,IAAIC,SAAS,GAAGV,YAAY,EAAE;gBACnCK,SAAS,GAAGI,SAAS;cACvB,CAAC,MAAM,IAAIJ,SAAS,IAAI,IAAI,IAAIK,SAAS,GAAGV,YAAY,EAAE;gBACxDM,KAAK,GAAG,CAACG,SAAS,GAAGJ,SAAS,IAAI,CAAC;gBACnC;cACF;cACA,IAAIG,CAAC,KAAK,CAAC,EAAE;gBACX;gBACA;gBACAD,aAAa,GAAGG,SAAS,GAAGf,UAAU,CAAC,CAAC,CAAC,CAACe,SAAS;cACrD;YACF;YACA,IAAIJ,KAAK,IAAI,IAAI,EAAE;cACjB,IAAI,CAACD,SAAS,EAAE;gBACd;gBACA;gBACAC,KAAK,GAAGX,UAAU,CAAC,CAAC,CAAC,CAACW,KAAK;cAC7B,CAAC,MAAM,IAAID,SAAS,EAAE;gBACpB;gBACA;gBACAC,KAAK,GAAGX,UAAU,CAACA,UAAU,CAACO,MAAM,GAAG,CAAC,CAAC,CAACI,KAAK;cACjD;YACF;YACAhB,IAAI,CAACI,GAAG,CAAC,GAAGD,IAAI,CAACU,aAAa,CAACG,KAAK,CAAC;UACvC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAIK,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;QACzB,IAAIC,MAAM,GAAGF,IAAI,CAACG,SAAS,CAAC,QAAQ,CAAC;QACrC,IAAIC,IAAI,GAAGJ,IAAI,CAACG,SAAS,CAAC,MAAM,CAAC;QACjC,IAAIE,WAAW,GAAG9B,QAAQ,CAAC+B,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAC/D5B,IAAI,CAAC0B,WAAW,CAAC,IAAIH,MAAM,GAAGE,IAAI,GAAG,CAAC;MACxC;MACA,OAAOzB,IAAI;IACb;IACA,OAAO,CAAC6B,GAAG,EAAEA,GAAG,CAAC;EACnB,CAAC;EACDhD,kBAAkB,CAACK,IAAI,GAAG,qBAAqB;EAC/CL,kBAAkB,CAACiD,aAAa,GAAG;IACjC;IACAC,CAAC,EAAE,CAAC;IACJlC,gBAAgB,EAAE,aAAa;IAC/BmC,eAAe,EAAE,IAAI;IACrB;IACA;IACA;IACA;IACAC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACd;IACAC,KAAK,EAAE,KAAK;IACZC,cAAc,EAAE,GAAG;IACnBC,WAAW,EAAE,GAAG;IAChBC,oBAAoB,EAAE;EACxB,CAAC;EACD,OAAOzD,kBAAkB;AAC3B,CAAC,CAACH,WAAW,CAAC;AACdA,WAAW,CAAC6D,aAAa,CAAC1D,kBAAkB,CAAC;AAC7C,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}