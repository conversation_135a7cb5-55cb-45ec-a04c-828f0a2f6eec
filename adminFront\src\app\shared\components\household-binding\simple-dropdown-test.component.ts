import { Component } from '@angular/core';

@Component({
    selector: 'app-simple-dropdown-test',
    template: `
    <div style="margin: 20px; padding: 20px; border: 2px solid red;">
      <h3>極簡下拉選單測試</h3>

      <div style="position: relative; width: 300px;">
        <button
          type="button"
          (click)="toggle()"
          style="width: 100%; padding: 10px; border: 1px solid #ccc; background: white; cursor: pointer;">
          點擊測試 (isOpen: {{isOpen}})
        </button>

        <!-- 強制覆蓋所有可能的樣式 -->
        <div
          *ngIf="isOpen"
          style="
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 99999 !important;
            background: white !important;
            border: 2px solid red !important;
            padding: 20px !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            transform: none !important;
            overflow: visible !important;
            max-height: none !important;
            height: auto !important;
            width: auto !important;
          ">

          <div style="background: yellow; padding: 10px; margin: 5px;">
            <h4>測試內容區域</h4>
            <p>如果你能看到這個，說明下拉選單正常顯示</p>
          </div>

          <div style="background: lightblue; padding: 10px; margin: 5px;">
            <h5>棟別列表</h5>
            <div *ngFor="let building of buildings; let i = index">
              <button
                type="button"
                (click)="selectBuilding(building)"
                style="
                  display: block;
                  width: 100%;
                  padding: 8px;
                  margin: 2px 0;
                  border: 1px solid #333;
                  background: {{selectedBuilding === building ? 'green' : 'white'}};
                  cursor: pointer;
                ">
                {{building}} ({{i}})
              </button>
            </div>
          </div>

          <div style="background: lightgreen; padding: 10px; margin: 5px;">
            <h5>當前選擇: {{selectedBuilding || '無'}}</h5>
            <button type="button" (click)="close()" style="padding: 5px 10px; background: red; color: white; border: none; cursor: pointer;">
              關閉
            </button>
          </div>

        </div>
      </div>

      <div style="margin-top: 20px; padding: 10px; background: #f0f0f0;">
        <h4>除錯資訊</h4>
        <p>isOpen: {{isOpen}}</p>
        <p>selectedBuilding: {{selectedBuilding}}</p>
        <p>buildings.length: {{buildings.length}}</p>
        <button type="button" (click)="forceUpdate()" style="padding: 5px 10px;">強制更新</button>
      </div>
    </div>
  `
})
export class SimpleDropdownTestComponent {
    isOpen = false;
    selectedBuilding = '';
    buildings = ['A棟', 'B棟', 'C棟', 'D棟', 'E棟'];

    toggle() {
        this.isOpen = !this.isOpen;
        console.log('Simple dropdown toggled:', this.isOpen);
    }

    selectBuilding(building: string) {
        this.selectedBuilding = building;
        console.log('Building selected:', building);
    }

    close() {
        this.isOpen = false;
        console.log('Simple dropdown closed');
    }

    forceUpdate() {
        console.log('Force update triggered');
        // 觸發變更檢測
    }
}
