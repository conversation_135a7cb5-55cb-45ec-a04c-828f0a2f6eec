{"ast": null, "code": "export var EnumTemplateType;\n(function (EnumTemplateType) {\n  EnumTemplateType[EnumTemplateType[\"\\u7A7A\\u9593\\u6A21\\u677F\"] = 1] = \"\\u7A7A\\u9593\\u6A21\\u677F\";\n  EnumTemplateType[EnumTemplateType[\"\\u9805\\u76EE\\u6A21\\u677F\"] = 2] = \"\\u9805\\u76EE\\u6A21\\u677F\";\n})(EnumTemplateType || (EnumTemplateType = {}));\nexport class EnumTemplateTypeHelper {\n  static getDisplayName(templateType) {\n    switch (templateType) {\n      case EnumTemplateType.空間模板:\n        return '空間模板';\n      case EnumTemplateType.項目模板:\n        return '項目模板';\n      default:\n        return '未知';\n    }\n  }\n  static getTemplateTypeList() {\n    return [{\n      value: EnumTemplateType.空間模板,\n      label: '空間模板'\n    }, {\n      value: EnumTemplateType.項目模板,\n      label: '項目模板'\n    }];\n  }\n}", "map": {"version": 3, "names": ["EnumTemplateType", "EnumTemplateTypeHelper", "getDisplayName", "templateType", "空間模板", "項目模板", "getTemplateTypeList", "value", "label"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\enum\\enumTemplateType.ts"], "sourcesContent": ["export enum EnumTemplateType {\n  空間模板 = 1,\n  項目模板 = 2\n}\n\nexport class EnumTemplateTypeHelper {\n  static getDisplayName(templateType: EnumTemplateType): string {\n    switch (templateType) {\n      case EnumTemplateType.空間模板:\n        return '空間模板';\n      case EnumTemplateType.項目模板:\n        return '項目模板';\n      default:\n        return '未知';\n    }\n  }\n\n  static getTemplateTypeList(): Array<{ value: EnumTemplateType; label: string }> {\n    return [\n      { value: EnumTemplateType.空間模板, label: '空間模板' },\n      { value: EnumTemplateType.項目模板, label: '項目模板' }\n    ];\n  }\n}\n"], "mappings": "AAAA,WAAYA,gBAGX;AAHD,WAAYA,gBAAgB;EAC1BA,gBAAA,CAAAA,gBAAA,8DAAQ;EACRA,gBAAA,CAAAA,gBAAA,8DAAQ;AACV,CAAC,EAHWA,gBAAgB,KAAhBA,gBAAgB;AAK5B,OAAM,MAAOC,sBAAsB;EACjC,OAAOC,cAAcA,CAACC,YAA8B;IAClD,QAAQA,YAAY;MAClB,KAAKH,gBAAgB,CAACI,IAAI;QACxB,OAAO,MAAM;MACf,KAAKJ,gBAAgB,CAACK,IAAI;QACxB,OAAO,MAAM;MACf;QACE,OAAO,IAAI;IACf;EACF;EAEA,OAAOC,mBAAmBA,CAAA;IACxB,OAAO,CACL;MAAEC,KAAK,EAAEP,gBAAgB,CAACI,IAAI;MAAEI,KAAK,EAAE;IAAM,CAAE,EAC/C;MAAED,KAAK,EAAEP,gBAAgB,CAACK,IAAI;MAAEG,KAAK,EAAE;IAAM,CAAE,CAChD;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}