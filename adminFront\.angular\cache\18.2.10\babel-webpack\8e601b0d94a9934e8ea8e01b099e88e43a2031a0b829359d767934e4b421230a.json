{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../Axis.js';\nvar RadiusAxis = /** @class */function (_super) {\n  __extends(RadiusAxis, _super);\n  function RadiusAxis(scale, radiusExtent) {\n    return _super.call(this, 'radius', scale, radiusExtent) || this;\n  }\n  RadiusAxis.prototype.pointToData = function (point, clamp) {\n    return this.polar.pointToData(point, clamp)[this.dim === 'radius' ? 0 : 1];\n  };\n  return RadiusAxis;\n}(Axis);\nRadiusAxis.prototype.dataToRadius = Axis.prototype.dataToCoord;\nRadiusAxis.prototype.radiusToData = Axis.prototype.coordToData;\nexport default RadiusAxis;", "map": {"version": 3, "names": ["__extends", "Axis", "<PERSON>dius<PERSON><PERSON><PERSON>", "_super", "scale", "radiusExtent", "call", "prototype", "pointToData", "point", "clamp", "polar", "dim", "dataToRadius", "dataToCoord", "radiusToData", "coordToData"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/coord/polar/RadiusAxis.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../Axis.js';\nvar RadiusAxis = /** @class */function (_super) {\n  __extends(RadiusAxis, _super);\n  function RadiusAxis(scale, radiusExtent) {\n    return _super.call(this, 'radius', scale, radiusExtent) || this;\n  }\n  RadiusAxis.prototype.pointToData = function (point, clamp) {\n    return this.polar.pointToData(point, clamp)[this.dim === 'radius' ? 0 : 1];\n  };\n  return RadiusAxis;\n}(Axis);\nRadiusAxis.prototype.dataToRadius = Axis.prototype.dataToCoord;\nRadiusAxis.prototype.radiusToData = Axis.prototype.coordToData;\nexport default RadiusAxis;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,IAAIC,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9CH,SAAS,CAACE,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAACE,KAAK,EAAEC,YAAY,EAAE;IACvC,OAAOF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAEF,KAAK,EAAEC,YAAY,CAAC,IAAI,IAAI;EACjE;EACAH,UAAU,CAACK,SAAS,CAACC,WAAW,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACzD,OAAO,IAAI,CAACC,KAAK,CAACH,WAAW,CAACC,KAAK,EAAEC,KAAK,CAAC,CAAC,IAAI,CAACE,GAAG,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5E,CAAC;EACD,OAAOV,UAAU;AACnB,CAAC,CAACD,IAAI,CAAC;AACPC,UAAU,CAACK,SAAS,CAACM,YAAY,GAAGZ,IAAI,CAACM,SAAS,CAACO,WAAW;AAC9DZ,UAAU,CAACK,SAAS,CAACQ,YAAY,GAAGd,IAAI,CAACM,SAAS,CAACS,WAAW;AAC9D,eAAed,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}