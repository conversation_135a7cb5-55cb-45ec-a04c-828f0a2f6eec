{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { findEffectedDataZooms } from './helper.js';\nimport { each } from 'zrender/lib/core/util.js';\nexport default function installDataZoomAction(registers) {\n  registers.registerAction('dataZoom', function (payload, ecModel) {\n    var effectedModels = findEffectedDataZooms(ecModel, payload);\n    each(effectedModels, function (dataZoomModel) {\n      dataZoomModel.setRawRange({\n        start: payload.start,\n        end: payload.end,\n        startValue: payload.startValue,\n        endValue: payload.endValue\n      });\n    });\n  });\n}", "map": {"version": 3, "names": ["findEffectedDataZooms", "each", "installDataZoomAction", "registers", "registerAction", "payload", "ecModel", "effectedModels", "dataZoomModel", "setRawRange", "start", "end", "startValue", "endValue"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/component/dataZoom/dataZoomAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { findEffectedDataZooms } from './helper.js';\nimport { each } from 'zrender/lib/core/util.js';\nexport default function installDataZoomAction(registers) {\n  registers.registerAction('dataZoom', function (payload, ecModel) {\n    var effectedModels = findEffectedDataZooms(ecModel, payload);\n    each(effectedModels, function (dataZoomModel) {\n      dataZoomModel.setRawRange({\n        start: payload.start,\n        end: payload.end,\n        startValue: payload.startValue,\n        endValue: payload.endValue\n      });\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,aAAa;AACnD,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,eAAe,SAASC,qBAAqBA,CAACC,SAAS,EAAE;EACvDA,SAAS,CAACC,cAAc,CAAC,UAAU,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAE;IAC/D,IAAIC,cAAc,GAAGP,qBAAqB,CAACM,OAAO,EAAED,OAAO,CAAC;IAC5DJ,IAAI,CAACM,cAAc,EAAE,UAAUC,aAAa,EAAE;MAC5CA,aAAa,CAACC,WAAW,CAAC;QACxBC,KAAK,EAAEL,OAAO,CAACK,KAAK;QACpBC,GAAG,EAAEN,OAAO,CAACM,GAAG;QAChBC,UAAU,EAAEP,OAAO,CAACO,UAAU;QAC9BC,QAAQ,EAAER,OAAO,CAACQ;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}