{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbSelectModule, NbOptionModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/dashboard.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction BuildCaseSelectorComponent_nb_option_6_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", buildCase_r1.totalHouses, \"\\u6236)\");\n  }\n}\nfunction BuildCaseSelectorComponent_nb_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, BuildCaseSelectorComponent_nb_option_6_span_2_Template, 2, 1, \"span\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r1.name, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", buildCase_r1.totalHouses);\n  }\n}\nexport class BuildCaseSelectorComponent {\n  constructor(dashboardService) {\n    this.dashboardService = dashboardService;\n    this.buildCaseChange = new EventEmitter();\n    this.buildCaseList = [];\n    this.selectedBuildCase = 'all';\n  }\n  ngOnInit() {\n    this.loadBuildCaseList();\n  }\n  loadBuildCaseList() {\n    this.dashboardService.getBuildCaseList().subscribe(data => {\n      this.buildCaseList = data;\n    });\n  }\n  onBuildCaseChange(value) {\n    this.selectedBuildCase = value;\n    this.buildCaseChange.emit(value);\n  }\n  static {\n    this.ɵfac = function BuildCaseSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildCaseSelectorComponent)(i0.ɵɵdirectiveInject(i1.DashboardService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BuildCaseSelectorComponent,\n      selectors: [[\"app-build-case-selector\"]],\n      outputs: {\n        buildCaseChange: \"buildCaseChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 2,\n      consts: [[1, \"build-case-selector\"], [1, \"selector-label\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", 1, \"selector-dropdown\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"all\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [\"class\", \"house-count\", 4, \"ngIf\"], [1, \"house-count\"]],\n      template: function BuildCaseSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"label\", 1);\n          i0.ɵɵtext(2, \"\\u9078\\u64C7\\u5EFA\\u6848\\uFF1A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-select\", 2);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildCaseSelectorComponent_Template_nb_select_ngModelChange_3_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCase, $event) || (ctx.selectedBuildCase = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectedChange\", function BuildCaseSelectorComponent_Template_nb_select_selectedChange_3_listener($event) {\n            return ctx.onBuildCaseChange($event);\n          });\n          i0.ɵɵelementStart(4, \"nb-option\", 3);\n          i0.ɵɵtext(5, \"\\u5168\\u90E8\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, BuildCaseSelectorComponent_nb_option_6_Template, 3, 3, \"nb-option\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCase);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.NgControlStatus, i3.NgModel, NbSelectModule, i4.NbSelectComponent, i4.NbOptionComponent, NbOptionModule],\n      styles: [\".build-case-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 24px;\\n}\\n.build-case-selector[_ngcontent-%COMP%]   .selector-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #2c3e50;\\n  white-space: nowrap;\\n}\\n.build-case-selector[_ngcontent-%COMP%]   .selector-dropdown[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n  max-width: 300px;\\n}\\n.build-case-selector[_ngcontent-%COMP%]   .house-count[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 12px;\\n}\\n\\n@media (max-width: 768px) {\\n  .build-case-selector[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n  .build-case-selector[_ngcontent-%COMP%]   .selector-dropdown[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImJ1aWxkLWNhc2Utc2VsZWN0b3IuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUFDRjtBQUNFO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0FBQ0o7QUFFRTtFQUNFLGdCQUFBO0VBQ0EsZ0JBQUE7QUFBSjtBQUdFO0VBQ0UsY0FBQTtFQUNBLGVBQUE7QUFESjs7QUFLQTtFQUNFO0lBQ0Usc0JBQUE7SUFDQSx1QkFBQTtJQUNBLFFBQUE7RUFGRjtFQUlFO0lBQ0UsV0FBQTtJQUNBLGVBQUE7RUFGSjtBQUNGIiwiZmlsZSI6ImJ1aWxkLWNhc2Utc2VsZWN0b3IuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuYnVpbGQtY2FzZS1zZWxlY3RvciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMTJweDtcbiAgbWFyZ2luLWJvdHRvbTogMjRweDtcbiAgXG4gIC5zZWxlY3Rvci1sYWJlbCB7XG4gICAgZm9udC1zaXplOiAxNHB4O1xuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgY29sb3I6ICMyYzNlNTA7XG4gICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgfVxuICBcbiAgLnNlbGVjdG9yLWRyb3Bkb3duIHtcbiAgICBtaW4td2lkdGg6IDIwMHB4O1xuICAgIG1heC13aWR0aDogMzAwcHg7XG4gIH1cbiAgXG4gIC5ob3VzZS1jb3VudCB7XG4gICAgY29sb3I6ICM3ZjhjOGQ7XG4gICAgZm9udC1zaXplOiAxMnB4O1xuICB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuYnVpbGQtY2FzZS1zZWxlY3RvciB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgICBnYXA6IDhweDtcbiAgICBcbiAgICAuc2VsZWN0b3ItZHJvcGRvd24ge1xuICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICBtYXgtd2lkdGg6IG5vbmU7XG4gICAgfVxuICB9XG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG9tZS9jb21wb25lbnRzL2J1aWxkLWNhc2Utc2VsZWN0b3IvYnVpbGQtY2FzZS1zZWxlY3Rvci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQUNGO0FBQ0U7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7QUFDSjtBQUVFO0VBQ0UsZ0JBQUE7RUFDQSxnQkFBQTtBQUFKO0FBR0U7RUFDRSxjQUFBO0VBQ0EsZUFBQTtBQURKOztBQUtBO0VBQ0U7SUFDRSxzQkFBQTtJQUNBLHVCQUFBO0lBQ0EsUUFBQTtFQUZGO0VBSUU7SUFDRSxXQUFBO0lBQ0EsZUFBQTtFQUZKO0FBQ0Y7QUFDQSx3MkNBQXcyQyIsInNvdXJjZXNDb250ZW50IjpbIi5idWlsZC1jYXNlLXNlbGVjdG9yIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAxMnB4O1xuICBtYXJnaW4tYm90dG9tOiAyNHB4O1xuICBcbiAgLnNlbGVjdG9yLWxhYmVsIHtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICBjb2xvcjogIzJjM2U1MDtcbiAgICB3aGl0ZS1zcGFjZTogbm93cmFwO1xuICB9XG4gIFxuICAuc2VsZWN0b3ItZHJvcGRvd24ge1xuICAgIG1pbi13aWR0aDogMjAwcHg7XG4gICAgbWF4LXdpZHRoOiAzMDBweDtcbiAgfVxuICBcbiAgLmhvdXNlLWNvdW50IHtcbiAgICBjb2xvcjogIzdmOGM4ZDtcbiAgICBmb250LXNpemU6IDEycHg7XG4gIH1cbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5idWlsZC1jYXNlLXNlbGVjdG9yIHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgIGdhcDogOHB4O1xuICAgIFxuICAgIC5zZWxlY3Rvci1kcm9wZG93biB7XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICAgIG1heC13aWR0aDogbm9uZTtcbiAgICB9XG4gIH1cbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbSelectModule", "NbOptionModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "buildCase_r1", "totalHouses", "ɵɵtemplate", "BuildCaseSelectorComponent_nb_option_6_span_2_Template", "ɵɵproperty", "id", "name", "BuildCaseSelectorComponent", "constructor", "dashboardService", "buildCaseChange", "buildCaseList", "selectedBuildCase", "ngOnInit", "loadBuildCaseList", "getBuildCaseList", "subscribe", "data", "onBuildCaseChange", "value", "emit", "ɵɵdirectiveInject", "i1", "DashboardService", "selectors", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BuildCaseSelectorComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "BuildCaseSelectorComponent_Template_nb_select_ngModelChange_3_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵlistener", "BuildCaseSelectorComponent_Template_nb_select_selectedChange_3_listener", "BuildCaseSelectorComponent_nb_option_6_Template", "ɵɵtwoWayProperty", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "NgControlStatus", "NgModel", "i4", "NbSelectComponent", "NbOptionComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\build-case-selector\\build-case-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\components\\build-case-selector\\build-case-selector.component.html"], "sourcesContent": ["import { Component, OnInit, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbSelectModule, NbOptionModule } from '@nebular/theme';\nimport { BuildCase } from '../../models/dashboard.interface';\nimport { DashboardService } from '../../services/dashboard.service';\n\n@Component({\n  selector: 'app-build-case-selector',\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbSelectModule, NbOptionModule],\n  templateUrl: './build-case-selector.component.html',\n  styleUrls: ['./build-case-selector.component.scss']\n})\nexport class BuildCaseSelectorComponent implements OnInit {\n  @Output() buildCaseChange = new EventEmitter<string>();\n\n  buildCaseList: BuildCase[] = [];\n  selectedBuildCase: string = 'all';\n\n  constructor(private dashboardService: DashboardService) { }\n\n  ngOnInit(): void {\n    this.loadBuildCaseList();\n  }\n\n  private loadBuildCaseList(): void {\n    this.dashboardService.getBuildCaseList().subscribe(data => {\n      this.buildCaseList = data;\n    });\n  }\n\n  onBuildCaseChange(value: string): void {\n    this.selectedBuildCase = value;\n    this.buildCaseChange.emit(value);\n  }\n}", "<div class=\"build-case-selector\">\n  <label class=\"selector-label\">選擇建案：</label>\n  <nb-select \n    [(ngModel)]=\"selectedBuildCase\" \n    (selectedChange)=\"onBuildCaseChange($event)\"\n    placeholder=\"請選擇建案\"\n    class=\"selector-dropdown\">\n    <nb-option value=\"all\">全部建案</nb-option>\n    <nb-option \n      *ngFor=\"let buildCase of buildCaseList\" \n      [value]=\"buildCase.id\">\n      {{ buildCase.name }} \n      <span *ngIf=\"buildCase.totalHouses\" class=\"house-count\">({{ buildCase.totalHouses }}戶)</span>\n    </nb-option>\n  </nb-select>\n</div>"], "mappings": "AAAA,SAAoCA,YAAY,QAAQ,eAAe;AACvE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;;;;;;;;ICSzDC,EAAA,CAAAC,cAAA,cAAwD;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAArCH,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAK,kBAAA,MAAAC,YAAA,CAAAC,WAAA,YAA8B;;;;;IAJxFP,EAAA,CAAAC,cAAA,mBAEyB;IACvBD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAQ,UAAA,IAAAC,sDAAA,kBAAwD;IAC1DT,EAAA,CAAAG,YAAA,EAAY;;;;IAHVH,EAAA,CAAAU,UAAA,UAAAJ,YAAA,CAAAK,EAAA,CAAsB;IACtBX,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAK,kBAAA,MAAAC,YAAA,CAAAM,IAAA,MACA;IAAOZ,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAU,UAAA,SAAAJ,YAAA,CAAAC,WAAA,CAA2B;;;ADExC,OAAM,MAAOM,0BAA0B;EAMrCC,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAL1B,KAAAC,eAAe,GAAG,IAAIrB,YAAY,EAAU;IAEtD,KAAAsB,aAAa,GAAgB,EAAE;IAC/B,KAAAC,iBAAiB,GAAW,KAAK;EAEyB;EAE1DC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,CAACL,gBAAgB,CAACM,gBAAgB,EAAE,CAACC,SAAS,CAACC,IAAI,IAAG;MACxD,IAAI,CAACN,aAAa,GAAGM,IAAI;IAC3B,CAAC,CAAC;EACJ;EAEAC,iBAAiBA,CAACC,KAAa;IAC7B,IAAI,CAACP,iBAAiB,GAAGO,KAAK;IAC9B,IAAI,CAACT,eAAe,CAACU,IAAI,CAACD,KAAK,CAAC;EAClC;;;uCArBWZ,0BAA0B,EAAAb,EAAA,CAAA2B,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAA1BhB,0BAA0B;MAAAiB,SAAA;MAAAC,OAAA;QAAAf,eAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GAAAjC,EAAA,CAAAkC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbrCxC,EADF,CAAAC,cAAA,aAAiC,eACD;UAAAD,EAAA,CAAAE,MAAA,qCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,mBAI4B;UAH1BD,EAAA,CAAA0C,gBAAA,2BAAAC,uEAAAC,MAAA;YAAA5C,EAAA,CAAA6C,kBAAA,CAAAJ,GAAA,CAAAvB,iBAAA,EAAA0B,MAAA,MAAAH,GAAA,CAAAvB,iBAAA,GAAA0B,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAC/B5C,EAAA,CAAA8C,UAAA,4BAAAC,wEAAAH,MAAA;YAAA,OAAkBH,GAAA,CAAAjB,iBAAA,CAAAoB,MAAA,CAAyB;UAAA,EAAC;UAG5C5C,EAAA,CAAAC,cAAA,mBAAuB;UAAAD,EAAA,CAAAE,MAAA,+BAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAQ,UAAA,IAAAwC,+CAAA,uBAEyB;UAK7BhD,EADE,CAAAG,YAAA,EAAY,EACR;;;UAZFH,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAiD,gBAAA,YAAAR,GAAA,CAAAvB,iBAAA,CAA+B;UAMPlB,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAU,UAAA,YAAA+B,GAAA,CAAAxB,aAAA,CAAgB;;;qBDChCrB,YAAY,EAAAsD,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEvD,WAAW,EAAAwD,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAEzD,cAAc,EAAA0D,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAE3D,cAAc;MAAA4D,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}