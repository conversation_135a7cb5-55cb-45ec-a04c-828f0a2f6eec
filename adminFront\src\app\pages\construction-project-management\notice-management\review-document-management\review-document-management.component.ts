import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { SharedModule } from '../../../components/shared.module';
import { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';
import { CommonModule } from '@angular/common';
import { NbDatepickerModule, NbDialogService } from '@nebular/theme';
import { MessageService } from 'src/app/shared/services/message.service';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { BuildCaseService, HouseService, ReviewService } from 'src/services/api/services';
import { LabelInOptionsPipe } from 'src/app/@theme/pipes/mapping.pipe';
import { BaseComponent } from '../../../components/base/baseComponent';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { GetReviewListRes, HouseReview, ReviewHouseHold, TblExamineLog, TblReview } from 'src/services/api/models';
import { tap } from 'rxjs';
import { NbDateFnsDateModule } from '@nebular/date-fns';
import * as moment from 'moment';
import { EnumFileType } from 'src/app/shared/enum/enumFileType';
import { ReviewServiceCustom } from 'src/app/@core/service/review.service';
import { DateFormatHourPipe, DateFormatPipe } from 'src/app/@theme/pipes';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { STORAGE_KEY } from 'src/app/shared/constant/constant';
import { co } from '@fullcalendar/core/internal-common';
import { FileUploadComponent, FileUploadConfig, FileUploadResult } from '../../../components/file-upload/file-upload.component';
import { HouseholdItem, BuildingData } from 'src/app/shared/components/household-binding/household-binding.component';

export interface selectItem {
  label: string,
  value: number | string,
}

export interface HouseList {
  CFloor?: number | null;
  CHouseHold?: string | null;
  CHouseID?: number;
  CID?: number;
  CIsSelect?: boolean | null;
  CHouseType?: number | null;
  CIsEnable?: boolean | null;
}

export interface SaveReviewPostParam {
  CBuildCaseId?: number;
  CReviewId?: number;
  CReviewType?: number;
  CReviewName?: string;
  CSort?: number;
  CStatus?: number;
  CFile?: Blob;
  CExamineNote?: string;
  HouseReviews?: Array<HouseReview>;
  CIsSelectAll?: boolean;
  selectedCNoticeType?: any
}

export interface ReviewType {
  CBuildCaseId?: number;
  CReviewId?: number;
  CReviewType?: number | null;
  CReviewName?: string;
  CSort?: number;
  CStatus?: number;
  CFile?: Blob;
  CExamineNote?: string;
  HouseReviews?: Array<HouseReview>;
  CIsSelectAll?: boolean;
  selectedCNoticeType?: any,
  CFileUrl?: string | null;
  selectedBuildCase?: any | null;
  selectedReviewType?: any | null;
  selectedExamineStatus?: any | null;
  seletedStatus?: any | null;
  reviewHouseHolds?: Array<ReviewHouseHold>;
  tblExamineLogs?: Array<TblExamineLog> | null;
  tblReview?: TblReview;
}
export interface SearchQuery {
  selectedBuildCase?: any | null;
  selectedReviewType?: any | null;
  selectedExamineStatus?: any | null;
  seletedStatus?: any | null;
  CReviewName?: any | null;
}
@Component({
  selector: 'ngx-review-document-management',
  templateUrl: './review-document-management.component.html',
  styleUrls: ['./review-document-management.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule, AppSharedModule, NbDatepickerModule, NbDateFnsDateModule, DateFormatPipe, LabelInOptionsPipe, DateFormatHourPipe, FileUploadComponent],
})

export class ReviewDocumentManagementComponent extends BaseComponent implements OnInit {
  constructor(
    private _allow: AllowHelper,
    private dialogService: NbDialogService,
    private message: MessageService,
    private valid: ValidationHelper,
    private _houseService: HouseService,
    private _buildCaseService: BuildCaseService,
    private _reviewService: ReviewService,
    private reviewService: ReviewServiceCustom,
    private utilityService: UtilityService,
  ) { super(_allow) }

  override pageFirst = 1;
  override pageSize = 10;
  override pageIndex = 1;
  override totalRecords = 0;

  reviewTypeOptions = [{
    value: 1,
    label: '標準圖', //standard drawing
  }, {
    value: 2,
    label: '設備圖', //equipment drawing
  }
  ]

  reviewTypeOptionsQuery = [
    {
      value: -1,
      label: '全部'
    },
    {
      value: 1,
      label: '標準圖', //standard drawing
    }, {
      value: 2,
      label: '設備圖', //equipment drawing
    }
  ]

  examineStatusOptions = [
    {
      value: -1,
      label: '待審核' //Pending review

    }, {
      value: 1,
      label: '已通過' //passed

    },
    {
      value: 2,
      label: '已駁回' //rejected

    }
  ]

  examineStatusOptionsQuery = [{
    value: -1,
    label: '全部'
  },
  {
    value: 0,
    label: '待審核' //Pending review

  }, {
    value: 1,
    label: '已通過' //passed

  },
  {
    value: 2,
    label: '已駁回' //rejected
  }
  ]

  statusOptions = [{
    value: 1, //0停用 1啟用 9刪除
    label: '啟用' //enable
  }, {
    value: 2,
    label: '停用' //Disable
  },]

  statusOptionsQuery = [{
    value: -1,
    label: '全部'
  }, {
    value: 1, //0停用 1啟用 9刪除
    label: '啟用' //enable
  }, {
    value: 2,
    label: '停用' //Disable
  },]


  searchQuery: SearchQuery

  buildCaseOptions: any[] = [{ label: '全部', value: '' }]

  userBuildCaseOptions: any

  // 新增：新戶別選擇器相關屬性
  buildingData: BuildingData = {} // 存放建築物戶別資料
  selectedHouseholds: number[] = [] // 選中的戶別ID (使用 houseId)


  override ngOnInit(): void {
    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null
      && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined
      && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != "") {
      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));
      this.searchQuery = {
        selectedBuildCase: null,
        selectedReviewType: this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value)
          ? this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value)
          : this.reviewTypeOptionsQuery[0],
        selectedExamineStatus: this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value)
          ? this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value)
          : this.examineStatusOptionsQuery[0],
        seletedStatus: this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value)
          ? this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value)
          : this.statusOptionsQuery[0],
        CReviewName: previous_search.CReviewName
      }
    }
    else {
      this.searchQuery = {
        selectedBuildCase: null,
        selectedReviewType: this.reviewTypeOptionsQuery[0],
        selectedExamineStatus: this.examineStatusOptionsQuery[0],
        seletedStatus: this.statusOptionsQuery[0],
        CReviewName: ''
      }
    }
    this.getUserBuildCase()
  }
  @ViewChild('fileInput') fileInput!: ElementRef;
  fileName: string | null = null;
  imageUrl: any = undefined;
  // 文件上傳配置
  get fileUploadConfigWithState(): FileUploadConfig {
    return {
      ...this.fileUploadConfig,
      disabled: this.latestAction === 1
    };
  }
  fileUploadConfig: FileUploadConfig = {
    acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', '.dwg', '.dxf'],
    acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,
    acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',
    label: '上傳檔案',
    helpText: '*請上傳PDF格式或CAD檔案（.dwg, .dxf）',
    required: true,
    disabled: false,
    autoFillName: true,
    buttonText: '上傳',
    buttonIcon: 'fa-solid fa-cloud-arrow-up',
    maxFileSize: 10,
    multiple: false,
    showPreview: false
  };
  clearImage() {
    if (this.imageUrl) {
      this.imageUrl = null;
      this.fileName = null;
      if (this.fileInput) {
        this.fileInput.nativeElement.value = null; // Xóa giá trị input file
      }
    }
  }

  // 處理共用元件的文件選擇事件
  onFileUploaded(result: FileUploadResult) {
    this.fileName = result.fileName;
    this.imageUrl = {
      CName: result.CName,
      CFile: result.CFile,
      Cimg: result.Cimg,
      CFileUpload: result.CFileUpload,
      CFileType: result.CFileType,
    };
  }

  // 處理共用元件的文件清除事件
  onFileCleared() {
    this.fileName = null;
    this.imageUrl = null;
  }

  // 處理共用元件的自動填入名稱事件
  onNameAutoFilled(fileName: string) {
    if (!this.selectedReview.CReviewName) {
      this.selectedReview.CReviewName = fileName;
    }
  }

  // 更新 disabled 狀態
  get isFileUploadDisabled(): boolean {
    return this.latestAction === 1;
  }

  // 舊的文件上傳方法 - 已被共用元件取代
  /*
  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;
    if (!fileRegex.test(file.name)) {
      this.message.showErrorMSG('檔案格式錯誤，僅限pdf或CAD檔案（dwg, dxf）');
      return;
    }
    if (file) {
      this.fileName = file.name;

      // 如果名稱欄位為空，自動填入檔案名稱（去除副檔名）
      if (!this.selectedReview.CReviewName) {
        const fileName = file.name;
        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
        this.selectedReview.CReviewName = fileNameWithoutExtension;
      }

      const reader = new FileReader();
      reader.onload = (e: any) => {        // 判斷檔案類型
        let fileType: number;
        if (file.type.startsWith('image/')) {
          fileType = EnumFileType.JPG; // 圖片
        } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {
          fileType = 3; // CAD檔案
        } else {
          fileType = EnumFileType.PDF; // PDF
        }

        this.imageUrl = {
          CName: file.name,
          CFile: e.target?.result?.toString().split(',')[1],
          Cimg: file.name.includes('pdf') ? file : file,
          CFileUpload: file,
          CFileType: fileType,
        };
        if (this.fileInput) {
          this.fileInput.nativeElement.value = null;
        }
      };
      reader.readAsDataURL(file);
    }
  }
  */


  selectedReview: ReviewType

  getItemByValue(value: any, options: any[]) {
    for (const item of options) {
      if (item.value === value) {
        return item;
      }
    }
    return null;
  }
  latestAction: any = 0

  getReviewById(item: any, ref: any) {

    this._reviewService.apiReviewGetReviewByIdPost$Json({
      body: item.CReviewId
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        const data = res.Entries
        this.selectedReview = {
          CBuildCaseId: data.tblReview?.CBuildCaseId,
          CReviewId: data.tblReview?.CReviewId,
          CReviewType: data.tblReview?.CReviewType,
          CReviewName: data.tblReview?.CReviewName ? data.tblReview?.CReviewName : '',
          CSort: data.tblReview?.CSort,
          CStatus: data.tblReview?.CStatus,
          CFileUrl: data.tblReview?.CFileUrl,
          CExamineNote: data?.CExamineNote ? data?.CExamineNote : '',
          seletedStatus: data.tblReview?.CStatus ? this.getItemByValue(data.tblReview?.CStatus, this.statusOptions) : this.statusOptions[0],
          selectedReviewType: data.tblReview?.CReviewType ? this.getItemByValue(data.tblReview?.CReviewType, this.reviewTypeOptions) : this.reviewTypeOptions[0],
          tblExamineLogs: data.tblExamineLogs,
          reviewHouseHolds: data?.reviewHouseHolds?.filter((i: any) => i.CIsSelect),
          tblReview: data.tblReview
        }

        if (data && data?.tblExamineLogs && data?.tblExamineLogs.length) {
          if (data?.tblExamineLogs.length === 0) return undefined;
          this.latestAction = data?.tblExamineLogs[0].CAction;
          let latestDate = data?.tblExamineLogs[0].CCreateDt ? new Date(data?.tblExamineLogs[0].CCreateDt) : ''
          for (let i = 1; i < data.tblExamineLogs.length; i++) {
            if (data.tblExamineLogs[i].CCreateDt) {
              const currentDate = new Date(data.tblExamineLogs[i].CCreateDt!)
              if (currentDate > latestDate) {
                latestDate = currentDate;
                this.latestAction = data?.tblExamineLogs[i].CAction;
              }
            }
          }
        }

        // 先準備選中的戶別ID資料（API 已更新，ReviewHouseHold 現在包含 CHouseID 為 number 類型）
        let selectedHouseholdIds: number[] = [];
        if (data?.reviewHouseHolds) {
          selectedHouseholdIds = data.reviewHouseHolds
            .filter((item: any) => item.CIsSelect && item.CHouseID)
            .map((item: any) => item.CHouseID);
        }

        // 載入建築物資料，並在載入完成後設置選中的戶別
        this.loadBuildingDataFromAPI(selectedHouseholdIds, ref);
      }
    })
  }

  saveReviewPostRes: SaveReviewPostParam





  onSaveReview(ref: any) {
    this.validation()
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return
    }

    // 使用新戶別選擇器的結果
    let houseReviews: HouseReview[] = [];

    if (this.selectedHouseholds && this.selectedHouseholds.length > 0) {
      houseReviews = this.convertSelectedHouseholdsToHouseReview();
    }

    this.saveReviewPostRes = {
      CBuildCaseId: this.searchQuery.selectedBuildCase.value,
      CReviewId: this.selectedReview.CReviewId,
      CReviewType: this.selectedReview.selectedReviewType.value,
      CReviewName: this.selectedReview.CReviewName,
      CSort: this.selectedReview?.CSort,
      CStatus: this.selectedReview.seletedStatus.value,
      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,
      CExamineNote: this.selectedReview.CExamineNote,
      HouseReviews: houseReviews,
    }
    this.reviewService.SaveReview(this.saveReviewPostRes).subscribe(res => {
      if (res && res.body! && res.body.StatusCode! === 0) {
        this.message.showSucessMSG("執行成功");
        this.clearImage()
        this.getReviewList()
        ref.close();
      } else {
        this.message.showErrorMSG(res && res.body && res.body.Message!);
      }
    })
  }

  onSearch() {
    let previous_search = {
      CReviewName: this.searchQuery.CReviewName,
      CSelectedBuildCase: this.searchQuery.selectedBuildCase,
      CSeletedStatus: this.searchQuery.seletedStatus,
      CReviewType: this.searchQuery.selectedReviewType,
      CExamineStatus: this.searchQuery.selectedExamineStatus,
    }
    LocalStorageService.AddSessionStorage(STORAGE_KEY.REVIEW_SEARCH, JSON.stringify(previous_search));
    this.getReviewList()
  }

  pageChanged(newPage: number) {
    this.pageIndex = newPage;
    this.getReviewList()
  }
















  reviewList: GetReviewListRes[] | undefined

  isNew = true

  openPdfInNewTab(CFileUrl?: any) {
    if (CFileUrl) {
      this.utilityService.openFileNewTab(CFileUrl)
    }
  }

  getReviewList() {
    return this._reviewService.apiReviewGetReviewListPost$Json({
      body: {
        PageIndex: this.pageIndex,
        PageSize: this.pageSize,
        CReviewName: this.searchQuery.CReviewName,
        CBuildCaseID: this.searchQuery.selectedBuildCase.value,
        CStatus: this.searchQuery.seletedStatus.value,
        CReviewType: this.searchQuery.selectedReviewType.value,
        CExamineStatus: this.searchQuery.selectedExamineStatus.value,
      }
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.reviewList = res.Entries
          this.totalRecords = res.TotalItems!
        }
      })
    ).subscribe()
  }


  onSelectionChangeBuildCase() {
    if (this.searchQuery.selectedBuildCase.value) {
      this.getReviewList()
      // 載入新戶別選擇器的建築物資料
      this.loadBuildingDataFromAPI()
    }
  }

  getUserBuildCase() {
    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.userBuildCaseOptions = res.Entries.map(res => {
            return {
              label: res.CBuildCaseName,
              value: res.cID
            }
          })
          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null
            && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined
            && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != "") {
            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));
            if (previous_search.CSelectedBuildCase != null && previous_search.CSelectedBuildCase != undefined) {
              let index = this.userBuildCaseOptions.findIndex((x: any) => x.value == previous_search.CSelectedBuildCase.value)
              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[index]
            } else {
              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0]
            }
          }
          else {
            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0]
          }
          if (this.searchQuery.selectedBuildCase.value) {
            this.getReviewList()
            // 載入新戶別選擇器的建築物資料
            this.loadBuildingDataFromAPI()
          }
        }
      }),
    ).subscribe()
  }

  openModel(ref: any, item?: any) {
    this.latestAction = 0
    this.isNew = true
    this.clearImage()

    // 初始化新戶別選擇器
    this.selectedHouseholds = []

    this.selectedReview = {
      selectedReviewType: this.reviewTypeOptions[0],
      seletedStatus: this.statusOptions[0],
      selectedExamineStatus: this.examineStatusOptions[0],
      CReviewName: '',
      CSort: 0,
      CFileUrl: '',
      CExamineNote: '',
      CIsSelectAll: false
    }

    if (item) {
      this.isNew = false
      this.getReviewById(item, ref)
    } else {
      this.isNew = true
      // 載入新戶別選擇器的建築物資料，並在載入完成後開啟對話框
      this.loadBuildingDataFromAPI(undefined, ref)
    }
  }

  formatDate(CChangeDate: string): string {
    if (CChangeDate) {
      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')
    }
    return ''
  }

  onSubmit(ref: any) {

  }

  onClose(ref: any) {
    ref.close();
  }


  validation() {
    this.valid.clear();
    if (this.isNew && !this.imageUrl) {
      this.valid.addErrorMessage(`前台圖片`);
    }

    // 驗證戶別選擇
    if (!this.selectedHouseholds || this.selectedHouseholds.length === 0) {
      this.valid.addErrorMessage('[適用戶別]請至少選擇一個戶別');
    }

    this.valid.required('[送審說明]', this.selectedReview.CExamineNote)
  }

  getActionName(actionID: number | undefined) {
    let textR = "";
    if (actionID != undefined) {
      switch (actionID) {
        case 1:
          textR = "傳送";
          break;
        case 2:
          textR = "通過";
          break;
        case 3:
          textR = "駁回";
          break;
        default:
          break;
      }
    }
    return textR;
  }



  // 新增：載入建築物戶別資料 (使用 GetDropDown API)
  private loadBuildingDataFromAPI(selectedHouseholdIds?: number[], dialogRef?: any): void {
    if (!this.searchQuery.selectedBuildCase?.value) {
      console.warn('沒有選擇建案，無法載入建築物資料');
      return;
    }

    console.log('開始載入建築物資料，建案ID:', this.searchQuery.selectedBuildCase.value);

    this._houseService.apiHouseGetDropDownPost$Json({ buildCaseId: this.searchQuery.selectedBuildCase.value }).subscribe({
      next: (response) => {
        console.log('API 回應:', response);
        if (response && response.Entries) {
          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);
          console.log('轉換後的建築物資料:', this.buildingData);

          // 在建築物資料載入完成後，設置選中的戶別ID
          if (selectedHouseholdIds && selectedHouseholdIds.length > 0) {
            console.log('設置選中的戶別ID:', selectedHouseholdIds);
            this.selectedHouseholds = [...selectedHouseholdIds];
            // 觸發 ngModel 更新以確保戶別選擇器顯示選中的戶別
            setTimeout(() => {
              this.selectedHouseholds = [...selectedHouseholdIds];
            }, 50);
          }

          // 開啟對話框
          if (dialogRef) {
            setTimeout(() => {
              this.dialogService.open(dialogRef);
            }, 100);
          }
        } else {
          console.warn('API 回應無資料');
          this.buildingData = {};
          if (dialogRef) {
            this.dialogService.open(dialogRef);
          }
        }
      },
      error: (error) => {
        console.error('載入建築物戶別資料失敗:', error);
        this.buildingData = {};
        if (dialogRef) {
          this.dialogService.open(dialogRef);
        }
      }
    });
  }

  // 新增：將 API 回應轉換為建築物資料格式
  private convertApiResponseToBuildingData(entries: any): BuildingData {
    const buildingData: BuildingData = {};

    Object.entries(entries).forEach(([building, houses]: [string, any]) => {
      if (Array.isArray(houses) && houses.length > 0) {
        buildingData[building] = houses.map((house: any) => ({
          houseName: house.HouseName || '',
          building: building,
          floor: house.Floor ? `${house.Floor}F` : '',
          houseId: house.HouseId,
          houseType: house.HouseType,
          isSelected: false,
          isDisabled: false
        }));
      }
    });

    console.log('轉換後的 buildingData:', buildingData);
    return buildingData;
  }



  // 新增：處理戶別選擇變更
  onHouseholdSelectionChange(selectedItems: HouseholdItem[]) {
    // 更新 selectedHouseholds (使用 houseId)
    this.selectedHouseholds = selectedItems.map(item => item.houseId).filter(id => id !== undefined) as number[];
  }

  // 新增：處理戶別ID變更事件
  onHouseholdIdChange(selectedIds: number[]) {
    this.selectedHouseholds = selectedIds;
  }





  // 新增：從新戶別選擇器的選擇結果轉換為原有格式
  convertSelectedHouseholdsToHouseReview(): HouseReview[] {
    const result: HouseReview[] = [];

    // 從 buildingData 中找到對應的戶別資訊
    if (this.buildingData && this.selectedHouseholds.length > 0) {
      Object.values(this.buildingData).forEach(houses => {
        houses.forEach(house => {
          if (house.houseId && this.selectedHouseholds.includes(house.houseId)) {
            result.push({
              CHouseID: house.houseId,
              CIsSelect: true,
              CFloor: house.floor ? parseInt(house.floor.replace('F', '')) : undefined,
              CHouseHold: house.houseName,
            });
          }
        });
      });
    }

    return result;
  }

}
