import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'base64Image',
  standalone: true
})
export class Base64ImagePipe implements PipeTransform {
  transform(value: string | null | undefined, imageType: string = 'png'): string {
    if (!value) {
      return '';
    }

    // 如果已經包含前綴，直接返回
    if (value.startsWith('data:image/')) {
      return value;
    }

    // 如果是 HTTP/HTTPS URL，直接返回
    if (value.startsWith('http://') || value.startsWith('https://')) {
      return value;
    }

    // 添加 Base64 前綴
    return `data:image/${imageType};base64,${value}`;
  }
}
