{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { TemplateGetListResponse } from 'src/services/api/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction SpaceTemplateSelectorComponent_div_13_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_13_div_6_Template_input_ngModelChange_1_listener($event) {\n      const template_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(template_r2.selected, $event) || (template_r2.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SpaceTemplateSelectorComponent_div_13_div_6_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onTemplateItemChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 25)(3, \"div\")(4, \"div\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 27);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 28);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 29);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const template_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", template_r2.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(template_r2.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", template_r2.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u72C0\\u614B: \", template_r2.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u985E\\u578B: \", template_r2.CTemplateType, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"div\", 19);\n    i0.ɵɵelement(3, \"i\", 20);\n    i0.ɵɵtext(4, \"\\u9078\\u64C7\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 21);\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_13_div_6_Template, 12, 5, \"div\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templates);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_div_15_div_10_div_5_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u4F4D\\u7F6E: \", detail_r4.CLocation, \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_div_15_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 53)(4, \"div\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_14_div_15_div_10_div_5_div_6_Template, 2, 1, \"div\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const detail_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r5 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(detail_r4.CPart);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", detail_r4.CLocation);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_div_15_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"span\", 48);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 49);\n    i0.ɵɵtemplate(5, SpaceTemplateSelectorComponent_div_14_div_15_div_10_div_5_Template, 7, 3, \"div\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5305\\u542B \", ctx_r2.getTemplateDetails(item_r6.CTemplateId).length, \" \\u500B\\u660E\\u7D30\\u9805\\u76EE\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getTemplateDetails(item_r6.CTemplateId));\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_div_15_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵtext(2, \" \\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u660E\\u7D30\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"h5\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"span\", 42);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 44);\n    i0.ɵɵtemplate(10, SpaceTemplateSelectorComponent_div_14_div_15_div_10_Template, 6, 2, \"div\", 45)(11, SpaceTemplateSelectorComponent_div_14_div_15_ng_template_11_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const noDetails_r7 = i0.ɵɵreference(12);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r6.CTemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", item_r6.CTemplateId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTemplateDetails(item_r6.CTemplateId).length > 0)(\"ngIfElse\", noDetails_r7);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"strong\");\n    i0.ɵɵelement(3, \"i\", 61);\n    i0.ɵɵtext(4, \"\\u885D\\u7A81\\u6AA2\\u6E2C\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6AA2\\u6E2C\\u5230 \", ctx_r2.getConflictCount(), \" \\u500B\\u9805\\u76EE\\u53EF\\u80FD\\u8207\\u73FE\\u6709\\u9700\\u6C42\\u91CD\\u8907\\uFF0C\\u7CFB\\u7D71\\u5C07\\u81EA\\u52D5\\u8655\\u7406\\u885D\\u7A81\\u9805\\u76EE\\u3002 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 30)(2, \"div\", 19);\n    i0.ɵɵelement(3, \"i\", 31);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u5957\\u7528\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 32)(6, \"div\", 33);\n    i0.ɵɵtext(7, \" \\u5C07\\u5957\\u7528 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9, \"\\u901A\\u7528\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 34);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 35);\n    i0.ɵɵtemplate(15, SpaceTemplateSelectorComponent_div_14_div_15_Template, 13, 5, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, SpaceTemplateSelectorComponent_div_14_div_16_Template, 6, 1, \"div\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\\uFF1A\", ctx_r2.getSelectedItems().length, \"\\u500B\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u7E3D\\u50F9\\u503C $\", i0.ɵɵpipeBind1(13, 4, ctx_r2.getSelectedTotalPrice()), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSelectedItems());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasConflicts());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.previousStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0B\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canProceed());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyTemplate());\n    });\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u5957\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.getSelectedItems().length === 0);\n  }\n}\nexport class SpaceTemplateSelectorComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.isVisible = false;\n    this.buildCaseId = '';\n    this.templateApplied = new EventEmitter();\n    this.closed = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templates = []; // 直接使用 API 資料\n    this.selectedTemplateDetails = new Map(); // 存儲已載入的模板詳情\n  }\n  ngOnInit() {\n    // 移除自動載入，改為在開啟時才載入\n  }\n  loadTemplatesFromAPI() {\n    console.log('開始載入模板資料...');\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: 1,\n      // 1=客變需求\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        console.log('API 回應:', response);\n        if (response.StatusCode === 0 && response.Entries) {\n          // 直接使用 API 資料，只添加 selected 屬性\n          this.templates = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n          console.log('模板資料載入成功，共', this.templates.length, '筆');\n        } else {\n          // API 返回錯誤\n          console.log('API 返回錯誤:', response.Message);\n          this.templates = [];\n        }\n      },\n      error: error => {\n        // HTTP 請求錯誤\n        console.error('API 請求失敗:', error);\n        this.templates = [];\n      }\n    });\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  getSelectedItems() {\n    return this.templates.filter(item => item.selected);\n  }\n  getSelectedTotalPrice() {\n    // 由於 API 沒有價格資訊，返回 0\n    return 0;\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      if (this.currentStep === 1) {\n        // 進入步驟2前，載入選中模板的詳情\n        this.loadSelectedTemplateDetails();\n      }\n      this.currentStep++;\n    }\n  }\n  loadSelectedTemplateDetails() {\n    const selectedItems = this.getSelectedItems();\n    selectedItems.forEach(item => {\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\n        // 只載入尚未載入過的模板詳情\n        this.loadTemplateDetailById(item.CTemplateId);\n      }\n    });\n  }\n  loadTemplateDetailById(templateId) {\n    const args = {\n      templateId: templateId\n    };\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: args\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          this.selectedTemplateDetails.set(templateId, response.Entries);\n        }\n      },\n      error: () => {\n        // 錯誤處理：設置空陣列\n        this.selectedTemplateDetails.set(templateId, []);\n      }\n    });\n  }\n  getTemplateDetails(templateId) {\n    return this.selectedTemplateDetails.get(templateId) || [];\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要套用的模板項目',\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: '通用模板',\n      selectedItems: this.getSelectedItems(),\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.isVisible = false;\n    this.resetSelections();\n    this.closed.emit();\n  }\n  onBackdropClick(event) {\n    if (event.target === event.currentTarget) {\n      this.close();\n    }\n  }\n  reset() {\n    this.currentStep = 1;\n    this.templates = [];\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    // 保留 templates 資料，只重置選擇狀態\n    this.templates.forEach(template => {\n      template.selected = false;\n    });\n    // 清空詳情快取\n    this.selectedTemplateDetails.clear();\n  }\n  // 公共API方法\n  open() {\n    this.isVisible = true;\n    if (this.templates.length === 0) {\n      // 只有在沒有資料時才載入\n      this.loadTemplatesFromAPI();\n    } else {\n      // 如果已有資料，只重置選擇狀態和步驟\n      this.currentStep = 1;\n      this.templates.forEach(template => {\n        template.selected = false;\n      });\n      this.selectedTemplateDetails.clear();\n    }\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceTemplateSelectorComponent,\n      selectors: [[\"app-space-template-selector\"]],\n      inputs: {\n        isVisible: \"isVisible\",\n        buildCaseId: \"buildCaseId\"\n      },\n      outputs: {\n        templateApplied: \"templateApplied\",\n        closed: \"closed\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 25,\n      vars: 18,\n      consts: [[\"noDetails\", \"\"], [1, \"space-template-modal\", 3, \"click\"], [1, \"space-template-content\", 3, \"click\"], [1, \"space-template-header\"], [1, \"space-template-title\"], [1, \"close-btn\", 3, \"click\"], [1, \"space-template-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"space-template-footer\"], [1, \"progress-info\"], [1, \"step-buttons\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"class\", \"btn btn-secondary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"template-selection\"], [1, \"section-title\"], [1, \"fas\", \"fa-layer-group\", \"mr-2\"], [1, \"template-list\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-item\"], [\"type\", \"checkbox\", 1, \"template-checkbox\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"template-info\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"item-status\"], [1, \"item-type\"], [1, \"confirmation-area\"], [1, \"fas\", \"fa-check-circle\", \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"summary-price\"], [1, \"selected-templates-details\"], [\"class\", \"template-detail-section\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"conflict-warning\", 4, \"ngIf\"], [1, \"template-detail-section\"], [1, \"template-detail-header\"], [1, \"template-name\"], [1, \"template-meta\"], [1, \"template-id\"], [1, \"template-status\"], [1, \"template-detail-content\"], [\"class\", \"detail-items\", 4, \"ngIf\", \"ngIfElse\"], [1, \"detail-items\"], [1, \"detail-items-header\"], [1, \"detail-count\"], [1, \"detail-items-list\"], [\"class\", \"detail-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\"], [1, \"detail-index\"], [1, \"detail-info\"], [1, \"detail-part\"], [\"class\", \"detail-location\", 4, \"ngIf\"], [1, \"detail-location\"], [1, \"no-details\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [1, \"conflict-warning\"], [1, \"warning-text\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-success\", 3, \"click\", \"disabled\"]],\n      template: function SpaceTemplateSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_div_click_0_listener($event) {\n            return ctx.onBackdropClick($event);\n          });\n          i0.ɵɵelementStart(1, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_div_click_1_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(2, \"div\", 3)(3, \"div\", 4);\n          i0.ɵɵtext(4, \"\\u7A7A\\u9593\\u6A21\\u677F\\u9078\\u64C7\\u5668\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_5_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(6, \"\\u00D7\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8);\n          i0.ɵɵtext(10, \"1. \\u9078\\u64C7\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8);\n          i0.ɵɵtext(12, \"2. \\u78BA\\u8A8D\\u5957\\u7528\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, SpaceTemplateSelectorComponent_div_13_Template, 7, 1, \"div\", 9)(14, SpaceTemplateSelectorComponent_div_14_Template, 17, 6, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\", 11)(17, \"span\");\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 12)(20, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_20_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(21, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, SpaceTemplateSelectorComponent_button_22_Template, 2, 0, \"button\", 14)(23, SpaceTemplateSelectorComponent_button_23_Template, 2, 1, \"button\", 15)(24, SpaceTemplateSelectorComponent_button_24_Template, 2, 1, \"button\", 16);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"show\", ctx.isVisible);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(10, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(14, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.getProgressText());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i2.NgClass, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.space-template-modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.space-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--color-basic-800);\\n  margin-bottom: 4px;\\n}\\n\\n.space-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-basic-600);\\n}\\n\\n.space-template-modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.space-template-content[_ngcontent-%COMP%] {\\n  background: var(--color-basic-100);\\n  border-radius: 12px;\\n  width: 90%;\\n  max-width: 1000px;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n\\n.space-template-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);\\n  color: var(--color-basic-100);\\n  padding: 20px 30px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.space-template-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  color: var(--color-basic-100);\\n  font-size: 24px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  border: none;\\n  background: none;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: background 0.3s ease;\\n}\\n\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n.space-template-body[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n\\n\\n\\n.step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n  gap: 10px;\\n}\\n\\n.step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n\\n.step-item.active[_ngcontent-%COMP%] {\\n  background: var(--color-primary-600);\\n  color: var(--color-basic-100);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.3);\\n}\\n\\n.step-item.completed[_ngcontent-%COMP%] {\\n  background: var(--color-success-600);\\n  color: var(--color-basic-100);\\n}\\n\\n.step-item.pending[_ngcontent-%COMP%] {\\n  background: var(--color-basic-200);\\n  color: var(--color-basic-600);\\n}\\n\\n\\n\\n.space-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-basic-800);\\n  margin-bottom: 15px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.space-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n\\n.space-card[_ngcontent-%COMP%] {\\n  border: 2px solid var(--color-basic-300);\\n  border-radius: 8px;\\n  padding: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-align: center;\\n  background: var(--color-basic-100);\\n}\\n\\n.space-card[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-primary-500);\\n  background: var(--color-primary-100);\\n}\\n\\n.space-card.selected[_ngcontent-%COMP%] {\\n  border-color: var(--color-primary-600);\\n  background: var(--color-primary-100);\\n}\\n\\n.space-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 8px;\\n}\\n\\n.space-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 4px;\\n}\\n\\n.space-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n\\n\\n.template-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.template-list[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 15px;\\n}\\n\\n.template-groups[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 15px;\\n}\\n\\n.template-group[_ngcontent-%COMP%] {\\n  border: 1px solid var(--color-basic-300);\\n  border-radius: 8px;\\n  overflow: hidden;\\n  background: var(--color-basic-100);\\n}\\n\\n.template-group-header[_ngcontent-%COMP%] {\\n  background: var(--color-basic-200);\\n  padding: 16px 20px;\\n  cursor: pointer;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  transition: background 0.3s ease;\\n}\\n\\n.template-group-header[_ngcontent-%COMP%]:hover {\\n  background: var(--color-basic-300);\\n}\\n\\n.template-group-header.active[_ngcontent-%COMP%] {\\n  background: var(--color-primary-100);\\n  border-color: var(--color-primary-400);\\n  border-left: 4px solid var(--color-primary-600);\\n}\\n\\n.group-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.group-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.group-stats[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n.group-toggle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  transition: transform 0.3s ease;\\n}\\n\\n.group-toggle.expanded[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.template-items[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n\\n.template-items.expanded[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n}\\n\\n.template-item[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  border: 1px solid var(--color-basic-300);\\n  border-radius: 8px;\\n  background: var(--color-basic-100);\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  transition: all 0.2s ease;\\n}\\n\\n.template-item[_ngcontent-%COMP%]:hover {\\n  background: var(--color-basic-200);\\n  border-color: var(--color-primary-300);\\n}\\n\\n.template-checkbox[_ngcontent-%COMP%] {\\n  transform: scale(1.2);\\n}\\n\\n.template-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr;\\n  gap: 20px;\\n  align-items: center;\\n}\\n\\n.item-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.item-code[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 12px;\\n}\\n\\n.item-status[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  text-align: center;\\n}\\n\\n.item-type[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-basic-600);\\n  text-align: center;\\n}\\n\\n.item-price[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n\\n.price-input[_ngcontent-%COMP%] {\\n  width: 90px;\\n  padding: 4px 8px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  text-align: right;\\n  font-size: 12px;\\n}\\n\\n\\n\\n.confirmation-area[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.selected-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.summary-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.summary-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n\\n.selected-items-list[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 15px;\\n  border-radius: 6px;\\n  margin-bottom: 15px;\\n}\\n\\n.items-list-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 10px;\\n}\\n\\n.items-list[_ngcontent-%COMP%] {\\n  margin-left: 20px;\\n  line-height: 1.6;\\n}\\n\\n.conflict-warning[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n\\n.warning-text[_ngcontent-%COMP%] {\\n  color: #856404;\\n  font-size: 14px;\\n}\\n\\n\\n\\n.space-template-footer[_ngcontent-%COMP%] {\\n  padding: 20px 30px;\\n  border-top: 1px solid #e9ecef;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background: #f8f9fa;\\n  border-radius: 0 0 12px 12px;\\n}\\n\\n.step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.selected-templates-details[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n.template-detail-section[_ngcontent-%COMP%] {\\n  border: 1px solid var(--color-basic-300);\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n  overflow: hidden;\\n  background: var(--color-basic-100);\\n}\\n\\n.template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--color-primary-100) 0%, var(--color-primary-200) 100%);\\n  padding: 16px 20px;\\n  border-bottom: 1px solid var(--color-basic-300);\\n}\\n\\n.template-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-primary-700);\\n  margin: 0 0 8px 0;\\n}\\n\\n.template-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  font-size: 14px;\\n}\\n\\n.template-id[_ngcontent-%COMP%] {\\n  color: var(--color-basic-600);\\n}\\n\\n.template-status[_ngcontent-%COMP%] {\\n  color: var(--color-success-600);\\n  font-weight: 500;\\n}\\n\\n.template-detail-content[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n}\\n\\n.detail-items-header[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n\\n.detail-count[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-basic-700);\\n}\\n\\n.detail-items-list[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 8px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px;\\n  background: var(--color-basic-200);\\n  border-radius: 6px;\\n  gap: 12px;\\n}\\n\\n.detail-index[_ngcontent-%COMP%] {\\n  background: var(--color-primary-600);\\n  color: white;\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 600;\\n  flex-shrink: 0;\\n}\\n\\n.detail-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.detail-part[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--color-basic-800);\\n  margin-bottom: 4px;\\n}\\n\\n.detail-location[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-basic-600);\\n}\\n\\n.no-details[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 20px;\\n  color: var(--color-basic-500);\\n  font-style: italic;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    transform: translateY(-50px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  .template-info[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n  .space-template-content[_ngcontent-%COMP%] {\\n    width: 95%;\\n    margin: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "TemplateGetListResponse", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "SpaceTemplateSelectorComponent_div_13_div_6_Template_input_ngModelChange_1_listener", "$event", "template_r2", "ɵɵrestoreView", "_r1", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "SpaceTemplateSelectorComponent_div_13_div_6_Template_input_change_1_listener", "ctx_r2", "ɵɵnextContext", "onTemplateItemChange", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CTemplateName", "ɵɵtextInterpolate1", "CTemplateId", "CStatus", "CTemplateType", "ɵɵelement", "ɵɵtemplate", "SpaceTemplateSelectorComponent_div_13_div_6_Template", "ɵɵproperty", "templates", "detail_r4", "CLocation", "SpaceTemplateSelectorComponent_div_14_div_15_div_10_div_5_div_6_Template", "i_r5", "<PERSON>art", "SpaceTemplateSelectorComponent_div_14_div_15_div_10_div_5_Template", "getTemplateDetails", "item_r6", "length", "SpaceTemplateSelectorComponent_div_14_div_15_div_10_Template", "SpaceTemplateSelectorComponent_div_14_div_15_ng_template_11_Template", "ɵɵtemplateRefExtractor", "noDetails_r7", "getConflictCount", "SpaceTemplateSelectorComponent_div_14_div_15_Template", "SpaceTemplateSelectorComponent_div_14_div_16_Template", "getSelectedItems", "ɵɵpipeBind1", "getSelectedTotalPrice", "hasConflicts", "SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener", "_r8", "previousStep", "SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener", "_r9", "nextStep", "canProceed", "SpaceTemplateSelectorComponent_button_24_Template_button_click_0_listener", "_r10", "applyTemplate", "SpaceTemplateSelectorComponent", "constructor", "templateService", "isVisible", "buildCaseId", "templateApplied", "closed", "currentStep", "selectedTemplateDetails", "Map", "ngOnInit", "loadTemplatesFromAPI", "console", "log", "getTemplateListArgs", "PageIndex", "PageSize", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "map", "item", "Message", "error", "filter", "loadSelectedTemplateDetails", "selectedItems", "for<PERSON>ach", "has", "loadTemplateDetailById", "templateId", "args", "apiTemplateGetTemplateDetailByIdPost$Json", "set", "get", "getProgressText", "progressTexts", "config", "spaceId", "spaceName", "totalPrice", "emit", "close", "resetSelections", "onBackdropClick", "event", "target", "currentTarget", "reset", "template", "clear", "open", "ɵɵdirectiveInject", "i1", "TemplateService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "SpaceTemplateSelectorComponent_Template", "rf", "ctx", "SpaceTemplateSelectorComponent_Template_div_click_0_listener", "SpaceTemplateSelectorComponent_Template_div_click_1_listener", "stopPropagation", "SpaceTemplateSelectorComponent_Template_button_click_5_listener", "SpaceTemplateSelectorComponent_div_13_Template", "SpaceTemplateSelectorComponent_div_14_Template", "SpaceTemplateSelectorComponent_Template_button_click_20_listener", "SpaceTemplateSelectorComponent_button_22_Template", "SpaceTemplateSelectorComponent_button_23_Template", "SpaceTemplateSelectorComponent_button_24_Template", "ɵɵclassProp", "ɵɵpureFunction3", "_c0", "i2", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "CheckboxControlValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs, TemplateGetListResponse, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';\r\n\r\n// 擴展 API 模型以支援前端選擇功能\r\nexport interface ExtendedTemplateItem extends TemplateGetListResponse {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n  spaceId: string;\r\n  spaceName: string;\r\n  selectedItems: ExtendedTemplateItem[];\r\n  totalPrice: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-space-template-selector',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule\r\n  ],\r\n  templateUrl: './space-template-selector.component.html',\r\n  styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n  @Input() isVisible: boolean = false;\r\n  @Input() buildCaseId: string = '';\r\n  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n  @Output() closed = new EventEmitter<void>();\r\n\r\n  currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n  templates: ExtendedTemplateItem[] = []; // 直接使用 API 資料\r\n  selectedTemplateDetails: Map<number, TemplateDetailItem[]> = new Map(); // 存儲已載入的模板詳情\r\n\r\n  constructor(private templateService: TemplateService) { }\r\n\r\n  ngOnInit() {\r\n    // 移除自動載入，改為在開啟時才載入\r\n  }\r\n\r\n  loadTemplatesFromAPI() {\r\n    console.log('開始載入模板資料...');\r\n    \r\n    // 準備 API 請求參數\r\n    const getTemplateListArgs: TemplateGetListArgs = {\r\n      CTemplateType: 1, // 1=客變需求\r\n      PageIndex: 1,\r\n      PageSize: 100, // 載入足夠的資料\r\n      CTemplateName: null // 不篩選名稱\r\n    };\r\n\r\n    // 調用 GetTemplateListForCommon API\r\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n      body: getTemplateListArgs\r\n    }).subscribe({\r\n      next: (response) => {\r\n        console.log('API 回應:', response);\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          // 直接使用 API 資料，只添加 selected 屬性\r\n          this.templates = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n          console.log('模板資料載入成功，共', this.templates.length, '筆');\r\n        } else {\r\n          // API 返回錯誤\r\n          console.log('API 返回錯誤:', response.Message);\r\n          this.templates = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        // HTTP 請求錯誤\r\n        console.error('API 請求失敗:', error);\r\n        this.templates = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onTemplateItemChange() {\r\n    // 當模板項目選擇變更時的處理\r\n  }\r\n\r\n  getSelectedItems(): ExtendedTemplateItem[] {\r\n    return this.templates.filter(item => item.selected);\r\n  }\r\n\r\n  getSelectedTotalPrice(): number {\r\n    // 由於 API 沒有價格資訊，返回 0\r\n    return 0;\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      if (this.currentStep === 1) {\r\n        // 進入步驟2前，載入選中模板的詳情\r\n        this.loadSelectedTemplateDetails();\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  loadSelectedTemplateDetails() {\r\n    const selectedItems = this.getSelectedItems();\r\n\r\n    selectedItems.forEach(item => {\r\n      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {\r\n        // 只載入尚未載入過的模板詳情\r\n        this.loadTemplateDetailById(item.CTemplateId);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTemplateDetailById(templateId: number) {\r\n    const args: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({\r\n      body: args\r\n    }).subscribe({\r\n      next: (response) => {\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.selectedTemplateDetails.set(templateId, response.Entries);\r\n        }\r\n      },\r\n      error: () => {\r\n        // 錯誤處理：設置空陣列\r\n        this.selectedTemplateDetails.set(templateId, []);\r\n      }\r\n    });\r\n  }\r\n\r\n  getTemplateDetails(templateId: number): TemplateDetailItem[] {\r\n    return this.selectedTemplateDetails.get(templateId) || [];\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要套用的模板項目',\r\n      2: '確認套用詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  hasConflicts(): boolean {\r\n    // 模擬衝突檢測邏輯\r\n    return this.getSelectedItems().length > 2;\r\n  }\r\n\r\n  getConflictCount(): number {\r\n    // 模擬衝突數量\r\n    return this.hasConflicts() ? 1 : 0;\r\n  }\r\n\r\n  applyTemplate() {\r\n    const config: SpaceTemplateConfig = {\r\n      spaceId: 'common', // 通用模板，不特定空間\r\n      spaceName: '通用模板',\r\n      selectedItems: this.getSelectedItems(),\r\n      totalPrice: this.getSelectedTotalPrice()\r\n    };\r\n\r\n    this.templateApplied.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.isVisible = false;\r\n    this.resetSelections();\r\n    this.closed.emit();\r\n  }\r\n\r\n  onBackdropClick(event: Event) {\r\n    if (event.target === event.currentTarget) {\r\n      this.close();\r\n    }\r\n  }\r\n\r\n  private reset() {\r\n    this.currentStep = 1;\r\n    this.templates = [];\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    // 保留 templates 資料，只重置選擇狀態\r\n    this.templates.forEach(template => {\r\n      template.selected = false;\r\n    });\r\n    // 清空詳情快取\r\n    this.selectedTemplateDetails.clear();\r\n  }\r\n\r\n  // 公共API方法\r\n  open() {\r\n    this.isVisible = true;\r\n    if (this.templates.length === 0) {\r\n      // 只有在沒有資料時才載入\r\n      this.loadTemplatesFromAPI();\r\n    } else {\r\n      // 如果已有資料，只重置選擇狀態和步驟\r\n      this.currentStep = 1;\r\n      this.templates.forEach(template => {\r\n        template.selected = false;\r\n      });\r\n      this.selectedTemplateDetails.clear();\r\n    }\r\n  }\r\n}\r\n", "<!-- 空間模板選擇器共用元件 -->\r\n<div class=\"space-template-modal\" [class.show]=\"isVisible\" (click)=\"onBackdropClick($event)\">\r\n  <div class=\"space-template-content\" (click)=\"$event.stopPropagation()\">\r\n    <div class=\"space-template-header\">\r\n      <div class=\"space-template-title\">空間模板選擇器</div>\r\n      <button class=\"close-btn\" (click)=\"close()\">&times;</button>\r\n    </div>\r\n\r\n    <div class=\"space-template-body\">\r\n      <!-- 步驟導航 -->\r\n      <div class=\"step-nav\">\r\n        <div class=\"step-item\" [ngClass]=\"{\r\n          'active': currentStep === 1,\r\n          'completed': currentStep > 1,\r\n          'pending': currentStep < 1\r\n        }\">1. 選擇模板</div>\r\n        <div class=\"step-item\" [ngClass]=\"{\r\n          'active': currentStep === 2,\r\n          'completed': currentStep > 2,\r\n          'pending': currentStep < 2\r\n        }\">2. 確認套用</div>\r\n      </div>\r\n\r\n      <!-- 步驟1: 選擇模板 -->\r\n      <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n        <div class=\"template-selection\">\r\n          <div class=\"section-title\">\r\n            <i class=\"fas fa-layer-group mr-2\"></i>選擇模板項目\r\n          </div>\r\n          <div class=\"template-list\">\r\n            <div *ngFor=\"let template of templates\" class=\"template-item\">\r\n              <input type=\"checkbox\" class=\"template-checkbox\" [(ngModel)]=\"template.selected\"\r\n                (change)=\"onTemplateItemChange()\">\r\n              <div class=\"template-info\">\r\n                <div>\r\n                  <div class=\"item-name\">{{ template.CTemplateName }}</div>\r\n                  <div class=\"item-code\">ID: {{ template.CTemplateId }}</div>\r\n                </div>\r\n                <div class=\"item-status\">\r\n                  狀態: {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n                </div>\r\n                <div class=\"item-type\">\r\n                  類型: {{ template.CTemplateType }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 步驟2: 確認套用 -->\r\n      <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n        <div class=\"confirmation-area\">\r\n          <div class=\"section-title\">\r\n            <i class=\"fas fa-check-circle mr-2\"></i>確認套用詳情\r\n          </div>\r\n\r\n          <div class=\"selected-summary\">\r\n            <div class=\"summary-text\">\r\n              將套用 <strong>通用模板</strong>：{{ getSelectedItems().length }}個模板項目\r\n            </div>\r\n            <div class=\"summary-price\">總價值 ${{ getSelectedTotalPrice() | number }}</div>\r\n          </div>\r\n\r\n          <!-- 選中的模板詳情展開 -->\r\n          <div class=\"selected-templates-details\">\r\n            <div *ngFor=\"let item of getSelectedItems()\" class=\"template-detail-section\">\r\n              <div class=\"template-detail-header\">\r\n                <h5 class=\"template-name\">{{ item.CTemplateName }}</h5>\r\n                <div class=\"template-meta\">\r\n                  <span class=\"template-id\">ID: {{ item.CTemplateId }}</span>\r\n                  <span class=\"template-status\">{{ item.CStatus === 1 ? '啟用' : '停用' }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"template-detail-content\">\r\n                <div *ngIf=\"getTemplateDetails(item.CTemplateId!).length > 0; else noDetails\" class=\"detail-items\">\r\n                  <div class=\"detail-items-header\">\r\n                    <span class=\"detail-count\">包含 {{ getTemplateDetails(item.CTemplateId!).length }} 個明細項目：</span>\r\n                  </div>\r\n                  <div class=\"detail-items-list\">\r\n                    <div *ngFor=\"let detail of getTemplateDetails(item.CTemplateId!); let i = index\"\r\n                      class=\"detail-item\">\r\n                      <div class=\"detail-index\">{{ i + 1 }}</div>\r\n                      <div class=\"detail-info\">\r\n                        <div class=\"detail-part\">{{ detail.CPart }}</div>\r\n                        <div class=\"detail-location\" *ngIf=\"detail.CLocation\">\r\n                          位置: {{ detail.CLocation }}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <ng-template #noDetails>\r\n                  <div class=\"no-details\">\r\n                    <i class=\"fas fa-info-circle mr-2\"></i>\r\n                    此模板暫無明細項目\r\n                  </div>\r\n                </ng-template>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div *ngIf=\"hasConflicts()\" class=\"conflict-warning\">\r\n            <div class=\"warning-text\">\r\n              <strong><i class=\"fas fa-exclamation-triangle mr-1\"></i>衝突檢測：</strong>\r\n              檢測到 {{ getConflictCount() }} 個項目可能與現有需求重複，系統將自動處理衝突項目。\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"space-template-footer\">\r\n      <div class=\"progress-info\">\r\n        <span>{{ getProgressText() }}</span>\r\n      </div>\r\n      <div class=\"step-buttons\">\r\n        <button class=\"btn btn-secondary\" (click)=\"close()\">取消</button>\r\n        <button *ngIf=\"currentStep > 1\" class=\"btn btn-secondary\" (click)=\"previousStep()\">上一步</button>\r\n        <button *ngIf=\"currentStep < 2\" class=\"btn btn-primary\" [disabled]=\"!canProceed()\"\r\n          (click)=\"nextStep()\">下一步</button>\r\n        <button *ngIf=\"currentStep === 2\" class=\"btn btn-success\" [disabled]=\"getSelectedItems().length === 0\"\r\n          (click)=\"applyTemplate()\">確認套用</button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAA8BC,uBAAuB,QAAuD,yBAAyB;;;;;;;;;;;;;IC2BvHC,EADF,CAAAC,cAAA,cAA8D,gBAExB;IADaD,EAAA,CAAAE,gBAAA,2BAAAC,oFAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAJ,WAAA,CAAAK,QAAA,EAAAN,MAAA,MAAAC,WAAA,CAAAK,QAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAC9EJ,EAAA,CAAAY,UAAA,oBAAAC,6EAAA;MAAAb,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAE,oBAAA,EAAsB;IAAA,EAAC;IADnChB,EAAA,CAAAiB,YAAA,EACoC;IAGhCjB,EAFJ,CAAAC,cAAA,cAA2B,UACpB,cACoB;IAAAD,EAAA,CAAAkB,MAAA,GAA4B;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IACzDjB,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAkB,MAAA,GAA8B;IACvDlB,EADuD,CAAAiB,YAAA,EAAM,EACvD;IACNjB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAkB,MAAA,GACF;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAkB,MAAA,IACF;IAEJlB,EAFI,CAAAiB,YAAA,EAAM,EACF,EACF;;;;IAd6CjB,EAAA,CAAAmB,SAAA,EAA+B;IAA/BnB,EAAA,CAAAoB,gBAAA,YAAAf,WAAA,CAAAK,QAAA,CAA+B;IAIrDV,EAAA,CAAAmB,SAAA,GAA4B;IAA5BnB,EAAA,CAAAqB,iBAAA,CAAAhB,WAAA,CAAAiB,aAAA,CAA4B;IAC5BtB,EAAA,CAAAmB,SAAA,GAA8B;IAA9BnB,EAAA,CAAAuB,kBAAA,SAAAlB,WAAA,CAAAmB,WAAA,KAA8B;IAGrDxB,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAuB,kBAAA,oBAAAlB,WAAA,CAAAoB,OAAA,8CACF;IAEEzB,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAuB,kBAAA,oBAAAlB,WAAA,CAAAqB,aAAA,MACF;;;;;IAjBN1B,EAFJ,CAAAC,cAAA,cAAoD,cAClB,cACH;IACzBD,EAAA,CAAA2B,SAAA,YAAuC;IAAA3B,EAAA,CAAAkB,MAAA,4CACzC;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAA4B,UAAA,IAAAC,oDAAA,mBAA8D;IAkBpE7B,EAFI,CAAAiB,YAAA,EAAM,EACF,EACF;;;;IAlB0BjB,EAAA,CAAAmB,SAAA,GAAY;IAAZnB,EAAA,CAAA8B,UAAA,YAAAhB,MAAA,CAAAiB,SAAA,CAAY;;;;;IAwD1B/B,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAkB,MAAA,GACF;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;;;;IADJjB,EAAA,CAAAmB,SAAA,EACF;IADEnB,EAAA,CAAAuB,kBAAA,oBAAAS,SAAA,CAAAC,SAAA,MACF;;;;;IALFjC,EAFF,CAAAC,cAAA,cACsB,cACM;IAAAD,EAAA,CAAAkB,MAAA,GAAW;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAEzCjB,EADF,CAAAC,cAAA,cAAyB,cACE;IAAAD,EAAA,CAAAkB,MAAA,GAAkB;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IACjDjB,EAAA,CAAA4B,UAAA,IAAAM,wEAAA,kBAAsD;IAI1DlC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IAPsBjB,EAAA,CAAAmB,SAAA,GAAW;IAAXnB,EAAA,CAAAqB,iBAAA,CAAAc,IAAA,KAAW;IAEVnC,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAAqB,iBAAA,CAAAW,SAAA,CAAAI,KAAA,CAAkB;IACbpC,EAAA,CAAAmB,SAAA,EAAsB;IAAtBnB,EAAA,CAAA8B,UAAA,SAAAE,SAAA,CAAAC,SAAA,CAAsB;;;;;IARxDjC,EAFJ,CAAAC,cAAA,cAAmG,cAChE,eACJ;IAAAD,EAAA,CAAAkB,MAAA,GAA4D;IACzFlB,EADyF,CAAAiB,YAAA,EAAO,EAC1F;IACNjB,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA4B,UAAA,IAAAS,kEAAA,kBACsB;IAU1BrC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IAdyBjB,EAAA,CAAAmB,SAAA,GAA4D;IAA5DnB,EAAA,CAAAuB,kBAAA,kBAAAT,MAAA,CAAAwB,kBAAA,CAAAC,OAAA,CAAAf,WAAA,EAAAgB,MAAA,0CAA4D;IAG/DxC,EAAA,CAAAmB,SAAA,GAA0C;IAA1CnB,EAAA,CAAA8B,UAAA,YAAAhB,MAAA,CAAAwB,kBAAA,CAAAC,OAAA,CAAAf,WAAA,EAA0C;;;;;IAcpExB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA2B,SAAA,YAAuC;IACvC3B,EAAA,CAAAkB,MAAA,+DACF;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;;;;;IA9BRjB,EAFJ,CAAAC,cAAA,cAA6E,cACvC,aACR;IAAAD,EAAA,CAAAkB,MAAA,GAAwB;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;IAErDjB,EADF,CAAAC,cAAA,cAA2B,eACC;IAAAD,EAAA,CAAAkB,MAAA,GAA0B;IAAAlB,EAAA,CAAAiB,YAAA,EAAO;IAC3DjB,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAkB,MAAA,GAAsC;IAExElB,EAFwE,CAAAiB,YAAA,EAAO,EACvE,EACF;IAENjB,EAAA,CAAAC,cAAA,cAAqC;IAmBnCD,EAlBA,CAAA4B,UAAA,KAAAa,4DAAA,kBAAmG,KAAAC,oEAAA,gCAAA1C,EAAA,CAAA2C,sBAAA,CAkB3E;IAO5B3C,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;;IAjCwBjB,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAAqB,iBAAA,CAAAkB,OAAA,CAAAjB,aAAA,CAAwB;IAEtBtB,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAuB,kBAAA,SAAAgB,OAAA,CAAAf,WAAA,KAA0B;IACtBxB,EAAA,CAAAmB,SAAA,GAAsC;IAAtCnB,EAAA,CAAAqB,iBAAA,CAAAkB,OAAA,CAAAd,OAAA,yCAAsC;IAKhEzB,EAAA,CAAAmB,SAAA,GAAwD;IAAAnB,EAAxD,CAAA8B,UAAA,SAAAhB,MAAA,CAAAwB,kBAAA,CAAAC,OAAA,CAAAf,WAAA,EAAAgB,MAAA,KAAwD,aAAAI,YAAA,CAAc;;;;;IA8B9E5C,EAFJ,CAAAC,cAAA,cAAqD,cACzB,aAChB;IAAAD,EAAA,CAAA2B,SAAA,YAAgD;IAAA3B,EAAA,CAAAkB,MAAA,qCAAK;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IACtEjB,EAAA,CAAAkB,MAAA,GACF;IACFlB,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IAFFjB,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAuB,kBAAA,yBAAAT,MAAA,CAAA+B,gBAAA,+JACF;;;;;IAvDF7C,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAA2B,SAAA,YAAwC;IAAA3B,EAAA,CAAAkB,MAAA,4CAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAGJjB,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAkB,MAAA,2BAAI;IAAAlB,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAkB,MAAA,+BAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IAAAjB,EAAA,CAAAkB,MAAA,IAC3B;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAkB,MAAA,IAA2C;;IACxElB,EADwE,CAAAiB,YAAA,EAAM,EACxE;IAGNjB,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAA4B,UAAA,KAAAkB,qDAAA,mBAA6E;IAoC/E9C,EAAA,CAAAiB,YAAA,EAAM;IAENjB,EAAA,CAAA4B,UAAA,KAAAmB,qDAAA,kBAAqD;IAOzD/C,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IApD2BjB,EAAA,CAAAmB,SAAA,IAC3B;IAD2BnB,EAAA,CAAAuB,kBAAA,WAAAT,MAAA,CAAAkC,gBAAA,GAAAR,MAAA,oCAC3B;IAC2BxC,EAAA,CAAAmB,SAAA,GAA2C;IAA3CnB,EAAA,CAAAuB,kBAAA,yBAAAvB,EAAA,CAAAiD,WAAA,QAAAnC,MAAA,CAAAoC,qBAAA,QAA2C;IAKhDlD,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAA8B,UAAA,YAAAhB,MAAA,CAAAkC,gBAAA,GAAqB;IAsCvChD,EAAA,CAAAmB,SAAA,EAAoB;IAApBnB,EAAA,CAAA8B,UAAA,SAAAhB,MAAA,CAAAqC,YAAA,GAAoB;;;;;;IAgB5BnD,EAAA,CAAAC,cAAA,iBAAmF;IAAzBD,EAAA,CAAAY,UAAA,mBAAAwC,0EAAA;MAAApD,EAAA,CAAAM,aAAA,CAAA+C,GAAA;MAAA,MAAAvC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAwC,YAAA,EAAc;IAAA,EAAC;IAACtD,EAAA,CAAAkB,MAAA,yBAAG;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;;;;;;IAC/FjB,EAAA,CAAAC,cAAA,iBACuB;IAArBD,EAAA,CAAAY,UAAA,mBAAA2C,0EAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAAkD,GAAA;MAAA,MAAA1C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAA2C,QAAA,EAAU;IAAA,EAAC;IAACzD,EAAA,CAAAkB,MAAA,yBAAG;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;;;;IADqBjB,EAAA,CAAA8B,UAAA,cAAAhB,MAAA,CAAA4C,UAAA,GAA0B;;;;;;IAElF1D,EAAA,CAAAC,cAAA,iBAC4B;IAA1BD,EAAA,CAAAY,UAAA,mBAAA+C,0EAAA;MAAA3D,EAAA,CAAAM,aAAA,CAAAsD,IAAA;MAAA,MAAA9C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAA+C,aAAA,EAAe;IAAA,EAAC;IAAC7D,EAAA,CAAAkB,MAAA,+BAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;;;;IADiBjB,EAAA,CAAA8B,UAAA,aAAAhB,MAAA,CAAAkC,gBAAA,GAAAR,MAAA,OAA4C;;;AD/F9G,OAAM,MAAOsB,8BAA8B;EAUzCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAT1B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,WAAW,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAG,IAAIvE,YAAY,EAAuB;IACzD,KAAAwE,MAAM,GAAG,IAAIxE,YAAY,EAAQ;IAE3C,KAAAyE,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAAtC,SAAS,GAA2B,EAAE,CAAC,CAAC;IACxC,KAAAuC,uBAAuB,GAAsC,IAAIC,GAAG,EAAE,CAAC,CAAC;EAEhB;EAExDC,QAAQA,CAAA;IACN;EAAA;EAGFC,oBAAoBA,CAAA;IAClBC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAE1B;IACA,MAAMC,mBAAmB,GAAwB;MAC/ClD,aAAa,EAAE,CAAC;MAAE;MAClBmD,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACfxD,aAAa,EAAE,IAAI,CAAC;KACrB;IAED;IACA,IAAI,CAAC0C,eAAe,CAACe,4CAA4C,CAAC;MAChEC,IAAI,EAAEJ;KACP,CAAC,CAACK,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjBT,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEQ,QAAQ,CAAC;QAChC,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD;UACA,IAAI,CAACtD,SAAS,GAAGoD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,IAAI,KAAK;YAC7C,GAAGA,IAAI;YACP7E,QAAQ,EAAE;WACX,CAAC,CAAC;UACHgE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC5C,SAAS,CAACS,MAAM,EAAE,GAAG,CAAC;QACvD,CAAC,MAAM;UACL;UACAkC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEQ,QAAQ,CAACK,OAAO,CAAC;UAC1C,IAAI,CAACzD,SAAS,GAAG,EAAE;QACrB;MACF,CAAC;MACD0D,KAAK,EAAGA,KAAK,IAAI;QACf;QACAf,OAAO,CAACe,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAAC1D,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEAf,oBAAoBA,CAAA;IAClB;EAAA;EAGFgC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACjB,SAAS,CAAC2D,MAAM,CAACH,IAAI,IAAIA,IAAI,CAAC7E,QAAQ,CAAC;EACrD;EAEAwC,qBAAqBA,CAAA;IACnB;IACA,OAAO,CAAC;EACV;EAEAQ,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACW,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACrB,gBAAgB,EAAE,CAACR,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAiB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACW,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B;QACA,IAAI,CAACsB,2BAA2B,EAAE;MACpC;MACA,IAAI,CAACtB,WAAW,EAAE;IACpB;EACF;EAEAsB,2BAA2BA,CAAA;IACzB,MAAMC,aAAa,GAAG,IAAI,CAAC5C,gBAAgB,EAAE;IAE7C4C,aAAa,CAACC,OAAO,CAACN,IAAI,IAAG;MAC3B,IAAIA,IAAI,CAAC/D,WAAW,IAAI,CAAC,IAAI,CAAC8C,uBAAuB,CAACwB,GAAG,CAACP,IAAI,CAAC/D,WAAW,CAAC,EAAE;QAC3E;QACA,IAAI,CAACuE,sBAAsB,CAACR,IAAI,CAAC/D,WAAW,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAuE,sBAAsBA,CAACC,UAAkB;IACvC,MAAMC,IAAI,GAA8B;MACtCD,UAAU,EAAEA;KACb;IAED,IAAI,CAAChC,eAAe,CAACkC,yCAAyC,CAAC;MAC7DlB,IAAI,EAAEiB;KACP,CAAC,CAAChB,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACjD,IAAI,CAACf,uBAAuB,CAAC6B,GAAG,CAACH,UAAU,EAAEb,QAAQ,CAACE,OAAO,CAAC;QAChE;MACF,CAAC;MACDI,KAAK,EAAEA,CAAA,KAAK;QACV;QACA,IAAI,CAACnB,uBAAuB,CAAC6B,GAAG,CAACH,UAAU,EAAE,EAAE,CAAC;MAClD;KACD,CAAC;EACJ;EAEA1D,kBAAkBA,CAAC0D,UAAkB;IACnC,OAAO,IAAI,CAAC1B,uBAAuB,CAAC8B,GAAG,CAACJ,UAAU,CAAC,IAAI,EAAE;EAC3D;EAEA1C,YAAYA,CAAA;IACV,IAAI,IAAI,CAACe,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAgC,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAACjC,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAlB,YAAYA,CAAA;IACV;IACA,OAAO,IAAI,CAACH,gBAAgB,EAAE,CAACR,MAAM,GAAG,CAAC;EAC3C;EAEAK,gBAAgBA,CAAA;IACd;IACA,OAAO,IAAI,CAACM,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACpC;EAEAU,aAAaA,CAAA;IACX,MAAM0C,MAAM,GAAwB;MAClCC,OAAO,EAAE,QAAQ;MAAE;MACnBC,SAAS,EAAE,MAAM;MACjBb,aAAa,EAAE,IAAI,CAAC5C,gBAAgB,EAAE;MACtC0D,UAAU,EAAE,IAAI,CAACxD,qBAAqB;KACvC;IAED,IAAI,CAACiB,eAAe,CAACwC,IAAI,CAACJ,MAAM,CAAC;IACjC,IAAI,CAACK,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAAC3C,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC4C,eAAe,EAAE;IACtB,IAAI,CAACzC,MAAM,CAACuC,IAAI,EAAE;EACpB;EAEAG,eAAeA,CAACC,KAAY;IAC1B,IAAIA,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,aAAa,EAAE;MACxC,IAAI,CAACL,KAAK,EAAE;IACd;EACF;EAEQM,KAAKA,CAAA;IACX,IAAI,CAAC7C,WAAW,GAAG,CAAC;IACpB,IAAI,CAACtC,SAAS,GAAG,EAAE;EACrB;EAEQ8E,eAAeA,CAAA;IACrB,IAAI,CAACxC,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACtC,SAAS,CAAC8D,OAAO,CAACsB,QAAQ,IAAG;MAChCA,QAAQ,CAACzG,QAAQ,GAAG,KAAK;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAAC4D,uBAAuB,CAAC8C,KAAK,EAAE;EACtC;EAEA;EACAC,IAAIA,CAAA;IACF,IAAI,CAACpD,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAAClC,SAAS,CAACS,MAAM,KAAK,CAAC,EAAE;MAC/B;MACA,IAAI,CAACiC,oBAAoB,EAAE;IAC7B,CAAC,MAAM;MACL;MACA,IAAI,CAACJ,WAAW,GAAG,CAAC;MACpB,IAAI,CAACtC,SAAS,CAAC8D,OAAO,CAACsB,QAAQ,IAAG;QAChCA,QAAQ,CAACzG,QAAQ,GAAG,KAAK;MAC3B,CAAC,CAAC;MACF,IAAI,CAAC4D,uBAAuB,CAAC8C,KAAK,EAAE;IACtC;EACF;;;uCAxMWtD,8BAA8B,EAAA9D,EAAA,CAAAsH,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAA9B1D,8BAA8B;MAAA2D,SAAA;MAAAC,MAAA;QAAAzD,SAAA;QAAAC,WAAA;MAAA;MAAAyD,OAAA;QAAAxD,eAAA;QAAAC,MAAA;MAAA;MAAAwD,UAAA;MAAAC,QAAA,GAAA7H,EAAA,CAAA8H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAd,QAAA,WAAAe,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3B3CnI,EAAA,CAAAC,cAAA,aAA6F;UAAlCD,EAAA,CAAAY,UAAA,mBAAAyH,6DAAAjI,MAAA;YAAA,OAASgI,GAAA,CAAAtB,eAAA,CAAA1G,MAAA,CAAuB;UAAA,EAAC;UAC1FJ,EAAA,CAAAC,cAAA,aAAuE;UAAnCD,EAAA,CAAAY,UAAA,mBAAA0H,6DAAAlI,MAAA;YAAA,OAASA,MAAA,CAAAmI,eAAA,EAAwB;UAAA,EAAC;UAElEvI,EADF,CAAAC,cAAA,aAAmC,aACC;UAAAD,EAAA,CAAAkB,MAAA,iDAAO;UAAAlB,EAAA,CAAAiB,YAAA,EAAM;UAC/CjB,EAAA,CAAAC,cAAA,gBAA4C;UAAlBD,EAAA,CAAAY,UAAA,mBAAA4H,gEAAA;YAAA,OAASJ,GAAA,CAAAxB,KAAA,EAAO;UAAA,EAAC;UAAC5G,EAAA,CAAAkB,MAAA,aAAO;UACrDlB,EADqD,CAAAiB,YAAA,EAAS,EACxD;UAKFjB,EAHJ,CAAAC,cAAA,aAAiC,aAET,aAKjB;UAAAD,EAAA,CAAAkB,MAAA,mCAAO;UAAAlB,EAAA,CAAAiB,YAAA,EAAM;UAChBjB,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAkB,MAAA,mCAAO;UACZlB,EADY,CAAAiB,YAAA,EAAM,EACZ;UA8BNjB,EA3BA,CAAA4B,UAAA,KAAA6G,8CAAA,iBAAoD,KAAAC,8CAAA,kBA2BA;UA6DtD1I,EAAA,CAAAiB,YAAA,EAAM;UAIFjB,EAFJ,CAAAC,cAAA,eAAmC,eACN,YACnB;UAAAD,EAAA,CAAAkB,MAAA,IAAuB;UAC/BlB,EAD+B,CAAAiB,YAAA,EAAO,EAChC;UAEJjB,EADF,CAAAC,cAAA,eAA0B,kBAC4B;UAAlBD,EAAA,CAAAY,UAAA,mBAAA+H,iEAAA;YAAA,OAASP,GAAA,CAAAxB,KAAA,EAAO;UAAA,EAAC;UAAC5G,EAAA,CAAAkB,MAAA,oBAAE;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UAI/DjB,EAHA,CAAA4B,UAAA,KAAAgH,iDAAA,qBAAmF,KAAAC,iDAAA,qBAE5D,KAAAC,iDAAA,qBAEK;UAIpC9I,EAHM,CAAAiB,YAAA,EAAM,EACF,EACF,EACF;;;UA/H4BjB,EAAA,CAAA+I,WAAA,SAAAX,GAAA,CAAAnE,SAAA,CAAwB;UAU3BjE,EAAA,CAAAmB,SAAA,GAIrB;UAJqBnB,EAAA,CAAA8B,UAAA,YAAA9B,EAAA,CAAAgJ,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAA/D,WAAA,QAAA+D,GAAA,CAAA/D,WAAA,MAAA+D,GAAA,CAAA/D,WAAA,MAIrB;UACqBrE,EAAA,CAAAmB,SAAA,GAIrB;UAJqBnB,EAAA,CAAA8B,UAAA,YAAA9B,EAAA,CAAAgJ,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAA/D,WAAA,QAAA+D,GAAA,CAAA/D,WAAA,MAAA+D,GAAA,CAAA/D,WAAA,MAIrB;UAIErE,EAAA,CAAAmB,SAAA,GAAuB;UAAvBnB,EAAA,CAAA8B,UAAA,SAAAsG,GAAA,CAAA/D,WAAA,OAAuB;UA2BvBrE,EAAA,CAAAmB,SAAA,EAAuB;UAAvBnB,EAAA,CAAA8B,UAAA,SAAAsG,GAAA,CAAA/D,WAAA,OAAuB;UAiErBrE,EAAA,CAAAmB,SAAA,GAAuB;UAAvBnB,EAAA,CAAAqB,iBAAA,CAAA+G,GAAA,CAAA/B,eAAA,GAAuB;UAIpBrG,EAAA,CAAAmB,SAAA,GAAqB;UAArBnB,EAAA,CAAA8B,UAAA,SAAAsG,GAAA,CAAA/D,WAAA,KAAqB;UACrBrE,EAAA,CAAAmB,SAAA,EAAqB;UAArBnB,EAAA,CAAA8B,UAAA,SAAAsG,GAAA,CAAA/D,WAAA,KAAqB;UAErBrE,EAAA,CAAAmB,SAAA,EAAuB;UAAvBnB,EAAA,CAAA8B,UAAA,SAAAsG,GAAA,CAAA/D,WAAA,OAAuB;;;qBDrGpCxE,YAAY,EAAAqJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,WAAA,EACZxJ,WAAW,EAAAyJ,EAAA,CAAAC,4BAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}