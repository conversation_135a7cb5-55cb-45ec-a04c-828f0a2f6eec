{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar ORIGIN_METHOD = '\\0__throttleOriginMethod';\nvar RATE = '\\0__throttleRate';\nvar THROTTLE_TYPE = '\\0__throttleType';\n;\n/**\r\n * @public\r\n * @param {(Function)} fn\r\n * @param {number} [delay=0] Unit: ms.\r\n * @param {boolean} [debounce=false]\r\n *        true: If call interval less than `delay`, only the last call works.\r\n *        false: If call interval less than `delay, call works on fixed rate.\r\n * @return {(Function)} throttled fn.\r\n */\nexport function throttle(fn, delay, debounce) {\n  var currCall;\n  var lastCall = 0;\n  var lastExec = 0;\n  var timer = null;\n  var diff;\n  var scope;\n  var args;\n  var debounceNextCall;\n  delay = delay || 0;\n  function exec() {\n    lastExec = new Date().getTime();\n    timer = null;\n    fn.apply(scope, args || []);\n  }\n  var cb = function () {\n    var cbArgs = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      cbArgs[_i] = arguments[_i];\n    }\n    currCall = new Date().getTime();\n    scope = this;\n    args = cbArgs;\n    var thisDelay = debounceNextCall || delay;\n    var thisDebounce = debounceNextCall || debounce;\n    debounceNextCall = null;\n    diff = currCall - (thisDebounce ? lastCall : lastExec) - thisDelay;\n    clearTimeout(timer);\n    // Here we should make sure that: the `exec` SHOULD NOT be called later\n    // than a new call of `cb`, that is, preserving the command order. Consider\n    // calculating \"scale rate\" when roaming as an example. When a call of `cb`\n    // happens, either the `exec` is called dierectly, or the call is delayed.\n    // But the delayed call should never be later than next call of `cb`. Under\n    // this assurance, we can simply update view state each time `dispatchAction`\n    // triggered by user roaming, but not need to add extra code to avoid the\n    // state being \"rolled-back\".\n    if (thisDebounce) {\n      timer = setTimeout(exec, thisDelay);\n    } else {\n      if (diff >= 0) {\n        exec();\n      } else {\n        timer = setTimeout(exec, -diff);\n      }\n    }\n    lastCall = currCall;\n  };\n  /**\r\n   * Clear throttle.\r\n   * @public\r\n   */\n  cb.clear = function () {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  };\n  /**\r\n   * Enable debounce once.\r\n   */\n  cb.debounceNextCall = function (debounceDelay) {\n    debounceNextCall = debounceDelay;\n  };\n  return cb;\n}\n/**\r\n * Create throttle method or update throttle rate.\r\n *\r\n * @example\r\n * ComponentView.prototype.render = function () {\r\n *     ...\r\n *     throttle.createOrUpdate(\r\n *         this,\r\n *         '_dispatchAction',\r\n *         this.model.get('throttle'),\r\n *         'fixRate'\r\n *     );\r\n * };\r\n * ComponentView.prototype.remove = function () {\r\n *     throttle.clear(this, '_dispatchAction');\r\n * };\r\n * ComponentView.prototype.dispose = function () {\r\n *     throttle.clear(this, '_dispatchAction');\r\n * };\r\n *\r\n */\nexport function createOrUpdate(obj, fnAttr, rate, throttleType) {\n  var fn = obj[fnAttr];\n  if (!fn) {\n    return;\n  }\n  var originFn = fn[ORIGIN_METHOD] || fn;\n  var lastThrottleType = fn[THROTTLE_TYPE];\n  var lastRate = fn[RATE];\n  if (lastRate !== rate || lastThrottleType !== throttleType) {\n    if (rate == null || !throttleType) {\n      return obj[fnAttr] = originFn;\n    }\n    fn = obj[fnAttr] = throttle(originFn, rate, throttleType === 'debounce');\n    fn[ORIGIN_METHOD] = originFn;\n    fn[THROTTLE_TYPE] = throttleType;\n    fn[RATE] = rate;\n  }\n  return fn;\n}\n/**\r\n * Clear throttle. Example see throttle.createOrUpdate.\r\n */\nexport function clear(obj, fnAttr) {\n  var fn = obj[fnAttr];\n  if (fn && fn[ORIGIN_METHOD]) {\n    // Clear throttle\n    fn.clear && fn.clear();\n    obj[fnAttr] = fn[ORIGIN_METHOD];\n  }\n}", "map": {"version": 3, "names": ["ORIGIN_METHOD", "RATE", "THROTTLE_TYPE", "throttle", "fn", "delay", "debounce", "curr<PERSON>all", "lastCall", "lastExec", "timer", "diff", "scope", "args", "debounceNextCall", "exec", "Date", "getTime", "apply", "cb", "cbArgs", "_i", "arguments", "length", "this<PERSON><PERSON><PERSON>", "thisDebounce", "clearTimeout", "setTimeout", "clear", "deboun<PERSON><PERSON><PERSON><PERSON>", "createOrUpdate", "obj", "fnAttr", "rate", "throttleType", "originFn", "lastThrottleType", "lastRate"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/echarts/lib/util/throttle.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar ORIGIN_METHOD = '\\0__throttleOriginMethod';\nvar RATE = '\\0__throttleRate';\nvar THROTTLE_TYPE = '\\0__throttleType';\n;\n/**\r\n * @public\r\n * @param {(Function)} fn\r\n * @param {number} [delay=0] Unit: ms.\r\n * @param {boolean} [debounce=false]\r\n *        true: If call interval less than `delay`, only the last call works.\r\n *        false: If call interval less than `delay, call works on fixed rate.\r\n * @return {(Function)} throttled fn.\r\n */\nexport function throttle(fn, delay, debounce) {\n  var currCall;\n  var lastCall = 0;\n  var lastExec = 0;\n  var timer = null;\n  var diff;\n  var scope;\n  var args;\n  var debounceNextCall;\n  delay = delay || 0;\n  function exec() {\n    lastExec = new Date().getTime();\n    timer = null;\n    fn.apply(scope, args || []);\n  }\n  var cb = function () {\n    var cbArgs = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      cbArgs[_i] = arguments[_i];\n    }\n    currCall = new Date().getTime();\n    scope = this;\n    args = cbArgs;\n    var thisDelay = debounceNextCall || delay;\n    var thisDebounce = debounceNextCall || debounce;\n    debounceNextCall = null;\n    diff = currCall - (thisDebounce ? lastCall : lastExec) - thisDelay;\n    clearTimeout(timer);\n    // Here we should make sure that: the `exec` SHOULD NOT be called later\n    // than a new call of `cb`, that is, preserving the command order. Consider\n    // calculating \"scale rate\" when roaming as an example. When a call of `cb`\n    // happens, either the `exec` is called dierectly, or the call is delayed.\n    // But the delayed call should never be later than next call of `cb`. Under\n    // this assurance, we can simply update view state each time `dispatchAction`\n    // triggered by user roaming, but not need to add extra code to avoid the\n    // state being \"rolled-back\".\n    if (thisDebounce) {\n      timer = setTimeout(exec, thisDelay);\n    } else {\n      if (diff >= 0) {\n        exec();\n      } else {\n        timer = setTimeout(exec, -diff);\n      }\n    }\n    lastCall = currCall;\n  };\n  /**\r\n   * Clear throttle.\r\n   * @public\r\n   */\n  cb.clear = function () {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  };\n  /**\r\n   * Enable debounce once.\r\n   */\n  cb.debounceNextCall = function (debounceDelay) {\n    debounceNextCall = debounceDelay;\n  };\n  return cb;\n}\n/**\r\n * Create throttle method or update throttle rate.\r\n *\r\n * @example\r\n * ComponentView.prototype.render = function () {\r\n *     ...\r\n *     throttle.createOrUpdate(\r\n *         this,\r\n *         '_dispatchAction',\r\n *         this.model.get('throttle'),\r\n *         'fixRate'\r\n *     );\r\n * };\r\n * ComponentView.prototype.remove = function () {\r\n *     throttle.clear(this, '_dispatchAction');\r\n * };\r\n * ComponentView.prototype.dispose = function () {\r\n *     throttle.clear(this, '_dispatchAction');\r\n * };\r\n *\r\n */\nexport function createOrUpdate(obj, fnAttr, rate, throttleType) {\n  var fn = obj[fnAttr];\n  if (!fn) {\n    return;\n  }\n  var originFn = fn[ORIGIN_METHOD] || fn;\n  var lastThrottleType = fn[THROTTLE_TYPE];\n  var lastRate = fn[RATE];\n  if (lastRate !== rate || lastThrottleType !== throttleType) {\n    if (rate == null || !throttleType) {\n      return obj[fnAttr] = originFn;\n    }\n    fn = obj[fnAttr] = throttle(originFn, rate, throttleType === 'debounce');\n    fn[ORIGIN_METHOD] = originFn;\n    fn[THROTTLE_TYPE] = throttleType;\n    fn[RATE] = rate;\n  }\n  return fn;\n}\n/**\r\n * Clear throttle. Example see throttle.createOrUpdate.\r\n */\nexport function clear(obj, fnAttr) {\n  var fn = obj[fnAttr];\n  if (fn && fn[ORIGIN_METHOD]) {\n    // Clear throttle\n    fn.clear && fn.clear();\n    obj[fnAttr] = fn[ORIGIN_METHOD];\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,aAAa,GAAG,0BAA0B;AAC9C,IAAIC,IAAI,GAAG,kBAAkB;AAC7B,IAAIC,aAAa,GAAG,kBAAkB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,EAAE,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EAC5C,IAAIC,QAAQ;EACZ,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIC,IAAI;EACR,IAAIC,KAAK;EACT,IAAIC,IAAI;EACR,IAAIC,gBAAgB;EACpBT,KAAK,GAAGA,KAAK,IAAI,CAAC;EAClB,SAASU,IAAIA,CAAA,EAAG;IACdN,QAAQ,GAAG,IAAIO,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAC/BP,KAAK,GAAG,IAAI;IACZN,EAAE,CAACc,KAAK,CAACN,KAAK,EAAEC,IAAI,IAAI,EAAE,CAAC;EAC7B;EACA,IAAIM,EAAE,GAAG,SAAAA,CAAA,EAAY;IACnB,IAAIC,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC5CD,MAAM,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC5B;IACAd,QAAQ,GAAG,IAAIS,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAC/BL,KAAK,GAAG,IAAI;IACZC,IAAI,GAAGO,MAAM;IACb,IAAII,SAAS,GAAGV,gBAAgB,IAAIT,KAAK;IACzC,IAAIoB,YAAY,GAAGX,gBAAgB,IAAIR,QAAQ;IAC/CQ,gBAAgB,GAAG,IAAI;IACvBH,IAAI,GAAGJ,QAAQ,IAAIkB,YAAY,GAAGjB,QAAQ,GAAGC,QAAQ,CAAC,GAAGe,SAAS;IAClEE,YAAY,CAAChB,KAAK,CAAC;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIe,YAAY,EAAE;MAChBf,KAAK,GAAGiB,UAAU,CAACZ,IAAI,EAAES,SAAS,CAAC;IACrC,CAAC,MAAM;MACL,IAAIb,IAAI,IAAI,CAAC,EAAE;QACbI,IAAI,CAAC,CAAC;MACR,CAAC,MAAM;QACLL,KAAK,GAAGiB,UAAU,CAACZ,IAAI,EAAE,CAACJ,IAAI,CAAC;MACjC;IACF;IACAH,QAAQ,GAAGD,QAAQ;EACrB,CAAC;EACD;AACF;AACA;AACA;EACEY,EAAE,CAACS,KAAK,GAAG,YAAY;IACrB,IAAIlB,KAAK,EAAE;MACTgB,YAAY,CAAChB,KAAK,CAAC;MACnBA,KAAK,GAAG,IAAI;IACd;EACF,CAAC;EACD;AACF;AACA;EACES,EAAE,CAACL,gBAAgB,GAAG,UAAUe,aAAa,EAAE;IAC7Cf,gBAAgB,GAAGe,aAAa;EAClC,CAAC;EACD,OAAOV,EAAE;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASW,cAAcA,CAACC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,YAAY,EAAE;EAC9D,IAAI9B,EAAE,GAAG2B,GAAG,CAACC,MAAM,CAAC;EACpB,IAAI,CAAC5B,EAAE,EAAE;IACP;EACF;EACA,IAAI+B,QAAQ,GAAG/B,EAAE,CAACJ,aAAa,CAAC,IAAII,EAAE;EACtC,IAAIgC,gBAAgB,GAAGhC,EAAE,CAACF,aAAa,CAAC;EACxC,IAAImC,QAAQ,GAAGjC,EAAE,CAACH,IAAI,CAAC;EACvB,IAAIoC,QAAQ,KAAKJ,IAAI,IAAIG,gBAAgB,KAAKF,YAAY,EAAE;IAC1D,IAAID,IAAI,IAAI,IAAI,IAAI,CAACC,YAAY,EAAE;MACjC,OAAOH,GAAG,CAACC,MAAM,CAAC,GAAGG,QAAQ;IAC/B;IACA/B,EAAE,GAAG2B,GAAG,CAACC,MAAM,CAAC,GAAG7B,QAAQ,CAACgC,QAAQ,EAAEF,IAAI,EAAEC,YAAY,KAAK,UAAU,CAAC;IACxE9B,EAAE,CAACJ,aAAa,CAAC,GAAGmC,QAAQ;IAC5B/B,EAAE,CAACF,aAAa,CAAC,GAAGgC,YAAY;IAChC9B,EAAE,CAACH,IAAI,CAAC,GAAGgC,IAAI;EACjB;EACA,OAAO7B,EAAE;AACX;AACA;AACA;AACA;AACA,OAAO,SAASwB,KAAKA,CAACG,GAAG,EAAEC,MAAM,EAAE;EACjC,IAAI5B,EAAE,GAAG2B,GAAG,CAACC,MAAM,CAAC;EACpB,IAAI5B,EAAE,IAAIA,EAAE,CAACJ,aAAa,CAAC,EAAE;IAC3B;IACAI,EAAE,CAACwB,KAAK,IAAIxB,EAAE,CAACwB,KAAK,CAAC,CAAC;IACtBG,GAAG,CAACC,MAAM,CAAC,GAAG5B,EAAE,CAACJ,aAAa,CAAC;EACjC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}