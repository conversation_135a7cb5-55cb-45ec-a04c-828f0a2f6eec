{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbButtonModule, NbIconModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./space-template-selector.service\";\nimport * as i2 from \"@angular/common\";\nfunction SpaceTemplateSelectorButtonComponent_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.icon + \" mr-1\");\n  }\n}\nexport let SpaceTemplateSelectorButtonComponent = /*#__PURE__*/(() => {\n  class SpaceTemplateSelectorButtonComponent {\n    constructor(spaceTemplateSelectorService) {\n      this.spaceTemplateSelectorService = spaceTemplateSelectorService;\n      this.buildCaseId = '';\n      this.text = '模板新增';\n      this.icon = 'fas fa-layer-group';\n      this.buttonClass = 'btn btn-warning mr-2';\n      this.disabled = false;\n      this.config = {};\n      this.templateApplied = new EventEmitter();\n      this.beforeOpen = new EventEmitter();\n      this.error = new EventEmitter();\n    }\n    openSelector() {\n      this.beforeOpen.emit();\n      // 建立完整的配置\n      const fullConfig = {\n        buildCaseId: this.buildCaseId,\n        buttonText: this.text,\n        buttonIcon: this.icon,\n        buttonClass: this.buttonClass,\n        ...this.config\n      };\n      this.spaceTemplateSelectorService.openSelector(fullConfig).subscribe({\n        next: result => {\n          if (result) {\n            this.templateApplied.emit(result);\n          }\n        },\n        error: error => {\n          this.error.emit(error.message || '開啟模板選擇器時發生錯誤');\n        }\n      });\n    }\n    static {\n      this.ɵfac = function SpaceTemplateSelectorButtonComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SpaceTemplateSelectorButtonComponent)(i0.ɵɵdirectiveInject(i1.SpaceTemplateSelectorService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SpaceTemplateSelectorButtonComponent,\n        selectors: [[\"app-space-template-selector-button\"]],\n        inputs: {\n          buildCaseId: \"buildCaseId\",\n          text: \"text\",\n          icon: \"icon\",\n          buttonClass: \"buttonClass\",\n          disabled: \"disabled\",\n          config: \"config\"\n        },\n        outputs: {\n          templateApplied: \"templateApplied\",\n          beforeOpen: \"beforeOpen\",\n          error: \"error\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 5,\n        consts: [[\"type\", \"button\", 3, \"click\", \"disabled\"], [3, \"class\", 4, \"ngIf\"]],\n        template: function SpaceTemplateSelectorButtonComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"button\", 0);\n            i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorButtonComponent_Template_button_click_0_listener() {\n              return ctx.openSelector();\n            });\n            i0.ɵɵtemplate(1, SpaceTemplateSelectorButtonComponent_i_1_Template, 1, 2, \"i\", 1);\n            i0.ɵɵtext(2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassMap(ctx.buttonClass);\n            i0.ɵɵproperty(\"disabled\", ctx.disabled);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.icon);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.text, \" \");\n          }\n        },\n        dependencies: [CommonModule, i2.NgIf, NbButtonModule, NbIconModule],\n        styles: [\".mr-1[_ngcontent-%COMP%]{margin-right:.25rem}\"]\n      });\n    }\n  }\n  return SpaceTemplateSelectorButtonComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}