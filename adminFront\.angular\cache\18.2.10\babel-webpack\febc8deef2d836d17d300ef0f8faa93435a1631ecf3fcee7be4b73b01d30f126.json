{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SystemManagementRoutingModule } from './system-management-routing.module';\nimport { RolePermissionsComponent } from './role-permissions/role-permissions.component';\nimport { UserManagementComponent } from './user-management/user-management.component';\nimport { LogsManagementComponent } from './logs-management/logs-management.component';\nimport { SharedModule } from '../components/shared.module';\nimport { NotificationSettingComponent } from './notification-setting/notification-setting.component';\nimport * as i0 from \"@angular/core\";\nexport class SystemManagementModule {\n  static {\n    this.ɵfac = function SystemManagementModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SystemManagementModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SystemManagementModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, SystemManagementRoutingModule, RolePermissionsComponent, UserManagementComponent, LogsManagementComponent, NotificationSettingComponent]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SystemManagementModule, {\n    imports: [CommonModule, SharedModule, SystemManagementRoutingModule, RolePermissionsComponent, UserManagementComponent, LogsManagementComponent, NotificationSettingComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "SystemManagementRoutingModule", "RolePermissionsComponent", "UserManagementComponent", "LogsManagementComponent", "SharedModule", "NotificationSettingComponent", "SystemManagementModule", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\system-management.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { SystemManagementRoutingModule } from './system-management-routing.module';\r\nimport { RolePermissionsComponent } from './role-permissions/role-permissions.component';\r\nimport { UserManagementComponent } from './user-management/user-management.component';\r\nimport { LogsManagementComponent } from './logs-management/logs-management.component';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { NotificationSettingComponent } from './notification-setting/notification-setting.component';\r\n\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    SystemManagementRoutingModule,\r\n    RolePermissionsComponent,\r\n    UserManagementComponent,\r\n    LogsManagementComponent,\r\n    NotificationSettingComponent,\r\n  ]\r\n})\r\nexport class SystemManagementModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,6BAA6B,QAAQ,oCAAoC;AAClF,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,4BAA4B,QAAQ,uDAAuD;;AAcpG,OAAM,MAAOC,sBAAsB;;;uCAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAT/BP,YAAY,EACZK,YAAY,EACZJ,6BAA6B,EAC7BC,wBAAwB,EACxBC,uBAAuB,EACvBC,uBAAuB,EACvBE,4BAA4B;IAAA;EAAA;;;2EAGnBC,sBAAsB;IAAAC,OAAA,GAT/BR,YAAY,EACZK,YAAY,EACZJ,6BAA6B,EAC7BC,wBAAwB,EACxBC,uBAAuB,EACvBC,uBAAuB,EACvBE,4BAA4B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}