{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule, NbPopoverModule } from '@nebular/theme';\nimport { HouseholdBindingComponent } from './components/household-binding/household-binding.component';\nimport { TestDropdownComponent } from './components/household-binding/test-dropdown.component';\nimport { SimpleDropdownTestComponent } from './components/household-binding/simple-dropdown-test.component';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule, NbPopoverModule,\n      // 也可以導出常用的模組供其他地方使用\n      CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule, NbPopoverModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [HouseholdBindingComponent, TestDropdownComponent, SimpleDropdownTestComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule, NbPopoverModule],\n    exports: [HouseholdBindingComponent, TestDropdownComponent, SimpleDropdownTestComponent,\n    // 也可以導出常用的模組供其他地方使用\n    CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule, NbPopoverModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "NbThemeModule", "NbLayoutModule", "NbCardModule", "NbButtonModule", "NbSelectModule", "NbInputModule", "NbCheckboxModule", "NbIconModule", "NbListModule", "NbTagModule", "NbPopoverModule", "HouseholdBindingComponent", "TestDropdownComponent", "SimpleDropdownTestComponent", "SharedModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  NbThemeModule,\r\n  NbLayoutModule,\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbSelectModule,\r\n  NbInputModule,\r\n  NbCheckboxModule,\r\n  NbIconModule,\r\n  NbListModule,\r\n  NbTagModule,\r\n  NbPopoverModule\r\n} from '@nebular/theme';\r\n\r\nimport { HouseholdBindingComponent } from './components/household-binding/household-binding.component';\r\nimport { TestDropdownComponent } from './components/household-binding/test-dropdown.component';\r\nimport { SimpleDropdownTestComponent } from './components/household-binding/simple-dropdown-test.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    HouseholdBindingComponent,\r\n    TestDropdownComponent,\r\n    SimpleDropdownTestComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    NbThemeModule,\r\n    NbLayoutModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbSelectModule,\r\n    NbInputModule,\r\n    NbCheckboxModule,\r\n    NbIconModule,\r\n    NbListModule,\r\n    NbTagModule,\r\n    NbPopoverModule], exports: [\r\n      HouseholdBindingComponent,\r\n      TestDropdownComponent,\r\n      SimpleDropdownTestComponent,\r\n      // 也可以導出常用的模組供其他地方使用\r\n      CommonModule,\r\n      FormsModule,\r\n      ReactiveFormsModule,\r\n      NbThemeModule,\r\n      NbLayoutModule,\r\n      NbCardModule,\r\n      NbButtonModule,\r\n      NbSelectModule,\r\n      NbInputModule,\r\n      NbCheckboxModule,\r\n      NbIconModule,\r\n      NbListModule,\r\n      NbTagModule,\r\n      NbPopoverModule\r\n    ]\r\n})\r\nexport class SharedModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SACEC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,eAAe,QACV,gBAAgB;AAEvB,SAASC,yBAAyB,QAAQ,4DAA4D;AACtG,SAASC,qBAAqB,QAAQ,wDAAwD;AAC9F,SAASC,2BAA2B,QAAQ,+DAA+D;;AA2C3G,OAAM,MAAOC,YAAY;;;uCAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAlCrBjB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,eAAe;MAIb;MACAb,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,eAAe;IAAA;EAAA;;;2EAGRI,YAAY;IAAAC,YAAA,GAvCrBJ,yBAAyB,EACzBC,qBAAqB,EACrBC,2BAA2B;IAAAG,OAAA,GAG3BnB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,eAAe;IAAAO,OAAA,GACbN,yBAAyB,EACzBC,qBAAqB,EACrBC,2BAA2B;IAC3B;IACAhB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}