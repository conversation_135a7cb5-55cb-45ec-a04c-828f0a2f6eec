/* tslint:disable */
/* eslint-disable */
/* Code generated by ng-openapi-gen DO NOT EDIT. */

import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { BooleanResponseBase } from '../../models/boolean-response-base';

export interface ApiPictureDeletePicturePost$Json$Params {
      body?: Array<number>
}

export function apiPictureDeletePicturePost$Json(http: HttpClient, rootUrl: string, params?: ApiPictureDeletePicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BooleanResponseBase>> {
  const rb = new RequestBuilder(rootUrl, apiPictureDeletePicturePost$Json.PATH, 'post');
  if (params) {
    rb.body(params.body, 'application/*+json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'text/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BooleanResponseBase>;
    })
  );
}

apiPictureDeletePicturePost$Json.PATH = '/api/Picture/DeletePicture';
