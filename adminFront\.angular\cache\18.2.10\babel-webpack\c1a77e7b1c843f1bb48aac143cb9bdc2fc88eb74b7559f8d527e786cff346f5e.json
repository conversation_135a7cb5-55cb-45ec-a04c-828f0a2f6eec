{"ast": null, "code": "import { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class DashboardService {\n  constructor() {}\n  getBuildCaseList() {\n    const mockBuildCases = [{\n      id: 'bc001',\n      name: '陽光花園',\n      code: 'YGH001',\n      totalHouses: 200\n    }, {\n      id: 'bc002',\n      name: '翡翠山莊',\n      code: 'FCSZ002',\n      totalHouses: 150\n    }, {\n      id: 'bc003',\n      name: '黃金海岸',\n      code: 'HJHA003',\n      totalHouses: 180\n    }, {\n      id: 'bc004',\n      name: '水岸人家',\n      code: 'SARJ004',\n      totalHouses: 120\n    }];\n    return of(mockBuildCases);\n  }\n  getDashboardKPI(buildCaseId) {\n    // 根據建案調整數據\n    let baseData = {\n      totalHouses: 200,\n      soldHouses: 180,\n      customizedHouses: 125,\n      signedHouses: 95,\n      paidHouses: 85\n    };\n    if (buildCaseId && buildCaseId !== 'all') {\n      // 根據不同建案調整假資料\n      switch (buildCaseId) {\n        case 'bc002':\n          // 翡翠山莊\n          baseData = {\n            totalHouses: 150,\n            soldHouses: 135,\n            customizedHouses: 98,\n            signedHouses: 72,\n            paidHouses: 65\n          };\n          break;\n        case 'bc003':\n          // 黃金海岸\n          baseData = {\n            totalHouses: 180,\n            soldHouses: 162,\n            customizedHouses: 115,\n            signedHouses: 88,\n            paidHouses: 78\n          };\n          break;\n        case 'bc004':\n          // 水岸人家\n          baseData = {\n            totalHouses: 120,\n            soldHouses: 108,\n            customizedHouses: 85,\n            signedHouses: 65,\n            paidHouses: 58\n          };\n          break;\n      }\n    }\n    const mockData = {\n      totalHouses: {\n        value: baseData.totalHouses,\n        label: '總戶數',\n        icon: 'fas fa-home',\n        color: '#2E86AB'\n      },\n      soldHouses: {\n        value: baseData.soldHouses,\n        label: '已售戶數',\n        icon: 'fas fa-handshake',\n        color: '#A23B72',\n        percentage: Math.round(baseData.soldHouses / baseData.totalHouses * 100)\n      },\n      customizedHouses: {\n        value: baseData.customizedHouses,\n        label: '客變戶數',\n        icon: 'fas fa-tools',\n        color: '#F18F01',\n        percentage: Math.round(baseData.customizedHouses / baseData.soldHouses * 100)\n      },\n      signedHouses: {\n        value: baseData.signedHouses,\n        label: '已簽署戶數',\n        icon: 'fas fa-file-signature',\n        color: '#C73E1D',\n        percentage: Math.round(baseData.signedHouses / baseData.customizedHouses * 100)\n      },\n      paidHouses: {\n        value: baseData.paidHouses,\n        label: '已付款戶數',\n        icon: 'fas fa-credit-card',\n        color: '#16537e',\n        percentage: Math.round(baseData.paidHouses / baseData.signedHouses * 100)\n      },\n      completionRate: {\n        value: Math.round(baseData.paidHouses / baseData.totalHouses * 100),\n        label: '整體完成率',\n        icon: 'fas fa-chart-line',\n        color: '#27ae60'\n      }\n    };\n    return of(mockData);\n  }\n  getProgressData(buildCaseId) {\n    const mockData = {\n      categories: ['尚未開始', '已閱讀操作說明', '選樣完成', '簽署完成'],\n      series: [{\n        name: '地主戶',\n        data: [20, 15, 30, 45],\n        color: '#3498db'\n      }, {\n        name: '銷售戶',\n        data: [35, 25, 40, 60],\n        color: '#e74c3c'\n      }]\n    };\n    return of(mockData);\n  }\n  getPaymentStatusData() {\n    const mockData = [{\n      name: '已付款',\n      value: 85,\n      percentage: 42.5,\n      amount: 12500\n    }, {\n      name: '未付款',\n      value: 95,\n      percentage: 47.5,\n      amount: 8200\n    }, {\n      name: '無須付款',\n      value: 20,\n      percentage: 10.0,\n      amount: 0\n    }];\n    return of(mockData);\n  }\n  getQuotationTrendData() {\n    const mockData = {\n      months: ['2024-08', '2024-09', '2024-10', '2024-11', '2024-12', '2025-01'],\n      series: [{\n        name: '待報價',\n        data: [25, 30, 22, 18, 12, 8],\n        color: '#f39c12'\n      }, {\n        name: '已報價',\n        data: [15, 25, 35, 40, 45, 50],\n        color: '#3498db'\n      }, {\n        name: '已簽回',\n        data: [5, 10, 15, 25, 35, 42],\n        color: '#27ae60'\n      }]\n    };\n    return of(mockData);\n  }\n  getHeatmapData() {\n    const mockData = {\n      xAxis: ['2房2廳', '3房2廳', '4房2廳', '4房3廳', '5房3廳'],\n      yAxis: ['尚未開始', '已閱讀操作說明', '選樣完成', '簽署完成'],\n      data: [[15, 8, 12, 5, 3],\n      // 尚未開始\n      [10, 15, 8, 12, 5],\n      // 已閱讀操作說明\n      [25, 30, 20, 15, 10],\n      // 選樣完成\n      [20, 25, 15, 18, 12] // 簽署完成\n      ]\n    };\n    return of(mockData);\n  }\n  static {\n    this.ɵfac = function DashboardService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DashboardService,\n      factory: DashboardService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "DashboardService", "constructor", "getBuildCaseList", "mockBuildCases", "id", "name", "code", "totalHouses", "getDashboardKPI", "buildCaseId", "baseData", "soldHouses", "customizedHouses", "signedHouses", "paidHouses", "mockData", "value", "label", "icon", "color", "percentage", "Math", "round", "completionRate", "getProgressData", "categories", "series", "data", "getPaymentStatusData", "amount", "getQuotationTrendData", "months", "getHeatmapData", "xAxis", "yAxis", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\services\\dashboard.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, of } from 'rxjs';\nimport { DashboardKPI, ProgressData, PaymentStatusData, QuotationTrendData, HeatmapData, BuildCase } from '../models/dashboard.interface';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DashboardService {\n\n  constructor() { }\n\n  getBuildCaseList(): Observable<BuildCase[]> {\n    const mockBuildCases: BuildCase[] = [\n      { id: 'bc001', name: '陽光花園', code: 'YGH001', totalHouses: 200 },\n      { id: 'bc002', name: '翡翠山莊', code: 'FCSZ002', totalHouses: 150 },\n      { id: 'bc003', name: '黃金海岸', code: 'HJHA003', totalHouses: 180 },\n      { id: 'bc004', name: '水岸人家', code: 'SARJ004', totalHouses: 120 }\n    ];\n    return of(mockBuildCases);\n  }\n\n  getDashboardKPI(buildCaseId?: string): Observable<DashboardKPI> {\n    // 根據建案調整數據\n    let baseData = { totalHouses: 200, soldHouses: 180, customizedHouses: 125, signedHouses: 95, paidHouses: 85 };\n    \n    if (buildCaseId && buildCaseId !== 'all') {\n      // 根據不同建案調整假資料\n      switch (buildCaseId) {\n        case 'bc002': // 翡翠山莊\n          baseData = { totalHouses: 150, soldHouses: 135, customizedHouses: 98, signedHouses: 72, paidHouses: 65 };\n          break;\n        case 'bc003': // 黃金海岸\n          baseData = { totalHouses: 180, soldHouses: 162, customizedHouses: 115, signedHouses: 88, paidHouses: 78 };\n          break;\n        case 'bc004': // 水岸人家\n          baseData = { totalHouses: 120, soldHouses: 108, customizedHouses: 85, signedHouses: 65, paidHouses: 58 };\n          break;\n      }\n    }\n\n    const mockData: DashboardKPI = {\n      totalHouses: {\n        value: baseData.totalHouses,\n        label: '總戶數',\n        icon: 'fas fa-home',\n        color: '#2E86AB'\n      },\n      soldHouses: {\n        value: baseData.soldHouses,\n        label: '已售戶數',\n        icon: 'fas fa-handshake',\n        color: '#A23B72',\n        percentage: Math.round((baseData.soldHouses / baseData.totalHouses) * 100)\n      },\n      customizedHouses: {\n        value: baseData.customizedHouses,\n        label: '客變戶數',\n        icon: 'fas fa-tools',\n        color: '#F18F01',\n        percentage: Math.round((baseData.customizedHouses / baseData.soldHouses) * 100)\n      },\n      signedHouses: {\n        value: baseData.signedHouses,\n        label: '已簽署戶數',\n        icon: 'fas fa-file-signature',\n        color: '#C73E1D',\n        percentage: Math.round((baseData.signedHouses / baseData.customizedHouses) * 100)\n      },\n      paidHouses: {\n        value: baseData.paidHouses,\n        label: '已付款戶數',\n        icon: 'fas fa-credit-card',\n        color: '#16537e',\n        percentage: Math.round((baseData.paidHouses / baseData.signedHouses) * 100)\n      },\n      completionRate: {\n        value: Math.round((baseData.paidHouses / baseData.totalHouses) * 100),\n        label: '整體完成率',\n        icon: 'fas fa-chart-line',\n        color: '#27ae60'\n      }\n    };\n    return of(mockData);\n  }\n\n  getProgressData(buildCaseId?: string): Observable<ProgressData> {\n    const mockData: ProgressData = {\n      categories: ['尚未開始', '已閱讀操作說明', '選樣完成', '簽署完成'],\n      series: [\n        {\n          name: '地主戶',\n          data: [20, 15, 30, 45],\n          color: '#3498db'\n        },\n        {\n          name: '銷售戶',\n          data: [35, 25, 40, 60],\n          color: '#e74c3c'\n        }\n      ]\n    };\n    return of(mockData);\n  }\n\n  getPaymentStatusData(): Observable<PaymentStatusData[]> {\n    const mockData: PaymentStatusData[] = [\n      { name: '已付款', value: 85, percentage: 42.5, amount: 12500 },\n      { name: '未付款', value: 95, percentage: 47.5, amount: 8200 },\n      { name: '無須付款', value: 20, percentage: 10.0, amount: 0 }\n    ];\n    return of(mockData);\n  }\n\n  getQuotationTrendData(): Observable<QuotationTrendData> {\n    const mockData: QuotationTrendData = {\n      months: ['2024-08', '2024-09', '2024-10', '2024-11', '2024-12', '2025-01'],\n      series: [\n        {\n          name: '待報價',\n          data: [25, 30, 22, 18, 12, 8],\n          color: '#f39c12'\n        },\n        {\n          name: '已報價',\n          data: [15, 25, 35, 40, 45, 50],\n          color: '#3498db'\n        },\n        {\n          name: '已簽回',\n          data: [5, 10, 15, 25, 35, 42],\n          color: '#27ae60'\n        }\n      ]\n    };\n    return of(mockData);\n  }\n\n  getHeatmapData(): Observable<HeatmapData> {\n    const mockData: HeatmapData = {\n      xAxis: ['2房2廳', '3房2廳', '4房2廳', '4房3廳', '5房3廳'],\n      yAxis: ['尚未開始', '已閱讀操作說明', '選樣完成', '簽署完成'],\n      data: [\n        [15, 8, 12, 5, 3],   // 尚未開始\n        [10, 15, 8, 12, 5],  // 已閱讀操作說明\n        [25, 30, 20, 15, 10], // 選樣完成\n        [20, 25, 15, 18, 12]  // 簽署完成\n      ]\n    };\n    return of(mockData);\n  }\n}"], "mappings": "AACA,SAAqBA,EAAE,QAAQ,MAAM;;AAMrC,OAAM,MAAOC,gBAAgB;EAE3BC,YAAA,GAAgB;EAEhBC,gBAAgBA,CAAA;IACd,MAAMC,cAAc,GAAgB,CAClC;MAAEC,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,QAAQ;MAAEC,WAAW,EAAE;IAAG,CAAE,EAC/D;MAAEH,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEC,WAAW,EAAE;IAAG,CAAE,EAChE;MAAEH,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEC,WAAW,EAAE;IAAG,CAAE,EAChE;MAAEH,EAAE,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,SAAS;MAAEC,WAAW,EAAE;IAAG,CAAE,CACjE;IACD,OAAOR,EAAE,CAACI,cAAc,CAAC;EAC3B;EAEAK,eAAeA,CAACC,WAAoB;IAClC;IACA,IAAIC,QAAQ,GAAG;MAAEH,WAAW,EAAE,GAAG;MAAEI,UAAU,EAAE,GAAG;MAAEC,gBAAgB,EAAE,GAAG;MAAEC,YAAY,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAE,CAAE;IAE7G,IAAIL,WAAW,IAAIA,WAAW,KAAK,KAAK,EAAE;MACxC;MACA,QAAQA,WAAW;QACjB,KAAK,OAAO;UAAE;UACZC,QAAQ,GAAG;YAAEH,WAAW,EAAE,GAAG;YAAEI,UAAU,EAAE,GAAG;YAAEC,gBAAgB,EAAE,EAAE;YAAEC,YAAY,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAE,CAAE;UACxG;QACF,KAAK,OAAO;UAAE;UACZJ,QAAQ,GAAG;YAAEH,WAAW,EAAE,GAAG;YAAEI,UAAU,EAAE,GAAG;YAAEC,gBAAgB,EAAE,GAAG;YAAEC,YAAY,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAE,CAAE;UACzG;QACF,KAAK,OAAO;UAAE;UACZJ,QAAQ,GAAG;YAAEH,WAAW,EAAE,GAAG;YAAEI,UAAU,EAAE,GAAG;YAAEC,gBAAgB,EAAE,EAAE;YAAEC,YAAY,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAE,CAAE;UACxG;MACJ;IACF;IAEA,MAAMC,QAAQ,GAAiB;MAC7BR,WAAW,EAAE;QACXS,KAAK,EAAEN,QAAQ,CAACH,WAAW;QAC3BU,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE;OACR;MACDR,UAAU,EAAE;QACVK,KAAK,EAAEN,QAAQ,CAACC,UAAU;QAC1BM,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,kBAAkB;QACxBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAEC,IAAI,CAACC,KAAK,CAAEZ,QAAQ,CAACC,UAAU,GAAGD,QAAQ,CAACH,WAAW,GAAI,GAAG;OAC1E;MACDK,gBAAgB,EAAE;QAChBI,KAAK,EAAEN,QAAQ,CAACE,gBAAgB;QAChCK,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAEC,IAAI,CAACC,KAAK,CAAEZ,QAAQ,CAACE,gBAAgB,GAAGF,QAAQ,CAACC,UAAU,GAAI,GAAG;OAC/E;MACDE,YAAY,EAAE;QACZG,KAAK,EAAEN,QAAQ,CAACG,YAAY;QAC5BI,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,uBAAuB;QAC7BC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAEC,IAAI,CAACC,KAAK,CAAEZ,QAAQ,CAACG,YAAY,GAAGH,QAAQ,CAACE,gBAAgB,GAAI,GAAG;OACjF;MACDE,UAAU,EAAE;QACVE,KAAK,EAAEN,QAAQ,CAACI,UAAU;QAC1BG,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,oBAAoB;QAC1BC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAEC,IAAI,CAACC,KAAK,CAAEZ,QAAQ,CAACI,UAAU,GAAGJ,QAAQ,CAACG,YAAY,GAAI,GAAG;OAC3E;MACDU,cAAc,EAAE;QACdP,KAAK,EAAEK,IAAI,CAACC,KAAK,CAAEZ,QAAQ,CAACI,UAAU,GAAGJ,QAAQ,CAACH,WAAW,GAAI,GAAG,CAAC;QACrEU,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,mBAAmB;QACzBC,KAAK,EAAE;;KAEV;IACD,OAAOpB,EAAE,CAACgB,QAAQ,CAAC;EACrB;EAEAS,eAAeA,CAACf,WAAoB;IAClC,MAAMM,QAAQ,GAAiB;MAC7BU,UAAU,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;MAC/CC,MAAM,EAAE,CACN;QACErB,IAAI,EAAE,KAAK;QACXsB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QACtBR,KAAK,EAAE;OACR,EACD;QACEd,IAAI,EAAE,KAAK;QACXsB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QACtBR,KAAK,EAAE;OACR;KAEJ;IACD,OAAOpB,EAAE,CAACgB,QAAQ,CAAC;EACrB;EAEAa,oBAAoBA,CAAA;IAClB,MAAMb,QAAQ,GAAwB,CACpC;MAAEV,IAAI,EAAE,KAAK;MAAEW,KAAK,EAAE,EAAE;MAAEI,UAAU,EAAE,IAAI;MAAES,MAAM,EAAE;IAAK,CAAE,EAC3D;MAAExB,IAAI,EAAE,KAAK;MAAEW,KAAK,EAAE,EAAE;MAAEI,UAAU,EAAE,IAAI;MAAES,MAAM,EAAE;IAAI,CAAE,EAC1D;MAAExB,IAAI,EAAE,MAAM;MAAEW,KAAK,EAAE,EAAE;MAAEI,UAAU,EAAE,IAAI;MAAES,MAAM,EAAE;IAAC,CAAE,CACzD;IACD,OAAO9B,EAAE,CAACgB,QAAQ,CAAC;EACrB;EAEAe,qBAAqBA,CAAA;IACnB,MAAMf,QAAQ,GAAuB;MACnCgB,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAC1EL,MAAM,EAAE,CACN;QACErB,IAAI,EAAE,KAAK;QACXsB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC7BR,KAAK,EAAE;OACR,EACD;QACEd,IAAI,EAAE,KAAK;QACXsB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAC9BR,KAAK,EAAE;OACR,EACD;QACEd,IAAI,EAAE,KAAK;QACXsB,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAC7BR,KAAK,EAAE;OACR;KAEJ;IACD,OAAOpB,EAAE,CAACgB,QAAQ,CAAC;EACrB;EAEAiB,cAAcA,CAAA;IACZ,MAAMjB,QAAQ,GAAgB;MAC5BkB,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;MAC/CC,KAAK,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;MAC1CP,IAAI,EAAE,CACJ,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;MAAI;MACrB,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;MAAG;MACrB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAAE;MACtB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAE;MAAA;KAEzB;IACD,OAAO5B,EAAE,CAACgB,QAAQ,CAAC;EACrB;;;uCA9IWf,gBAAgB;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAAmC,OAAA,EAAhBnC,gBAAgB,CAAAoC,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}