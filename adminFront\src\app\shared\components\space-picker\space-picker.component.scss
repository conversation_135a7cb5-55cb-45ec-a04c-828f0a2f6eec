.space-picker-container {
  .search-section {
    .btn {
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .space-list-section {
    min-height: 200px;
    
    .space-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 12px;
      margin-bottom: 1rem;

      .space-item {
        cursor: pointer;
        transition: all 0.2s ease;
        
        .space-card {
          padding: 12px;
          border: 2px solid #e4e7ea;
          border-radius: 8px;
          background-color: #ffffff;
          text-align: center;
          transition: all 0.2s ease;

          .space-name {
            font-weight: 600;
            font-size: 0.9rem;
            color: #2c3e50;
            margin-bottom: 4px;
          }

          .space-location {
            font-size: 0.8rem;
            color: #7f8c8d;
          }
        }

        &:hover .space-card {
          border-color: #4a90e2;
          box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);
          transform: translateY(-1px);
        }

        &.selected .space-card {
          border-color: #28a745;
          background-color: #f8fff9;
          box-shadow: 0 2px 12px rgba(40, 167, 69, 0.2);

          .space-name {
            color: #28a745;
          }
        }
      }
    }
  }

  .selected-summary {
    .selected-spaces-list {
      .badge {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        background-color: #007bff;
        color: white;
        border-radius: 20px;

        .btn-close {
          background: none;
          border: none;
          color: white;
          opacity: 0.8;
          cursor: pointer;
          padding: 0;
          margin: 0;
          width: 12px;
          height: 12px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            opacity: 1;
          }

          &::before {
            content: '×';
            font-size: 12px;
            line-height: 1;
          }
        }
      }
    }
  }

  // 響應式設計
  @media (max-width: 768px) {
    .search-section {
      .row > div {
        margin-bottom: 0.5rem;
      }
    }

    .space-list-section {
      .space-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 8px;
      }
    }
  }

  // 分頁樣式
  .pagination {
    .page-link {
      color: #007bff;
      border-color: #dee2e6;
      padding: 0.375rem 0.75rem;
      font-size: 0.875rem;

      &:hover {
        color: #0056b3;
        background-color: #e9ecef;
        border-color: #dee2e6;
      }
    }

    .page-item.active .page-link {
      background-color: #007bff;
      border-color: #007bff;
      color: white;
    }

    .page-item.disabled .page-link {
      color: #6c757d;
      background-color: #fff;
      border-color: #dee2e6;
      cursor: not-allowed;
    }
  }
}

// 全域樣式覆蓋
:host {
  display: block;
  width: 100%;
}
