{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/api/services/template.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"input\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_ngModelChange_1_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r5.selected, $event) || (item_r5.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onTemplateItemChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 32)(3, \"div\")(4, \"div\", 33);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 34);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 35);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 36);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_ngModelChange_13_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r5.price, $event) || (item_r5.price = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_change_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onTemplateItemChange());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r5.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.unit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", i0.ɵɵpipeBind1(12, 6, item_r5.price), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r5.price);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_div_13_div_6_Template_div_click_1_listener() {\n      const group_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleTemplateGroup(group_r2));\n    });\n    i0.ɵɵelementStart(2, \"div\", 24)(3, \"div\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 27);\n    i0.ɵɵtext(9, \"\\u25BC\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 28);\n    i0.ɵɵtemplate(11, SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template, 14, 8, \"div\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const group_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", group_r2.expanded);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", group_r2.icon, \" \", group_r2.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", group_r2.items.length, \"\\u500B\\u9805\\u76EE \\u2022 $\", i0.ɵɵpipeBind1(7, 11, ctx_r2.getTotalPrice(group_r2.items)), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"expanded\", group_r2.expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"expanded\", group_r2.expanded);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", group_r2.items);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18);\n    i0.ɵɵelement(3, \"i\", 19);\n    i0.ɵɵtext(4, \"\\u9078\\u64C7\\u6A21\\u677F\\u7FA4\\u7D44 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 20);\n    i0.ɵɵtemplate(6, SpaceTemplateSelectorComponent_div_13_div_6_Template, 12, 13, \"div\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateGroups);\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_li_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", item_r6.name, \" (\", item_r6.code, \") - $\", i0.ɵɵpipeBind1(2, 3, item_r6.price), \" \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48)(2, \"strong\");\n    i0.ɵɵelement(3, \"i\", 49);\n    i0.ɵɵtext(4, \"\\u885D\\u7A81\\u6AA2\\u6E2C\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6AA2\\u6E2C\\u5230 \", ctx_r2.getConflictCount(), \" \\u500B\\u9805\\u76EE\\u53EF\\u80FD\\u8207\\u73FE\\u6709\\u9700\\u6C42\\u91CD\\u8907\\uFF0C\\u7CFB\\u7D71\\u5C07\\u81EA\\u52D5\\u8655\\u7406\\u885D\\u7A81\\u9805\\u76EE\\u3002 \");\n  }\n}\nfunction SpaceTemplateSelectorComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 37)(2, \"div\", 18);\n    i0.ɵɵelement(3, \"i\", 38);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u5957\\u7528\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 39)(6, \"div\", 40);\n    i0.ɵɵtext(7, \" \\u5C07\\u5957\\u7528 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9, \"\\u901A\\u7528\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 41);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 42)(15, \"div\", 43);\n    i0.ɵɵtext(16, \"\\u5957\\u7528\\u9805\\u76EE\\u6E05\\u55AE\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"ul\", 44);\n    i0.ɵɵtemplate(18, SpaceTemplateSelectorComponent_div_14_li_18_Template, 3, 5, \"li\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, SpaceTemplateSelectorComponent_div_14_div_19_Template, 6, 1, \"div\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\\uFF1A\", ctx_r2.getSelectedItems().length, \"\\u500B\\u6A21\\u677F\\u9805\\u76EE \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u7E3D\\u50F9\\u503C $\", i0.ɵɵpipeBind1(13, 4, ctx_r2.getSelectedTotalPrice()), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSelectedItems());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasConflicts());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.previousStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(1, \"\\u4E0B\\u4E00\\u6B65\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canProceed());\n  }\n}\nfunction SpaceTemplateSelectorComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyTemplate());\n    });\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u5957\\u7528\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.getSelectedItems().length === 0);\n  }\n}\nexport class SpaceTemplateSelectorComponent {\n  constructor(templateService) {\n    this.templateService = templateService;\n    this.isVisible = false;\n    this.buildCaseId = '';\n    this.templateApplied = new EventEmitter();\n    this.closed = new EventEmitter();\n    this.currentStep = 1; // 現在從步驟1開始（選擇模板）\n    this.templateGroups = [];\n  }\n  ngOnInit() {\n    // 自動載入模板資料\n    this.loadTemplatesFromAPI();\n  }\n  loadTemplatesFromAPI() {\n    // 準備 API 請求參數\n    const getTemplateListArgs = {\n      CTemplateType: 1,\n      // 1=客變需求\n      PageIndex: 1,\n      PageSize: 100,\n      // 載入足夠的資料\n      CTemplateName: null // 不篩選名稱\n    };\n    // 調用 GetTemplateListForCommon API\n    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\n      body: getTemplateListArgs\n    }).subscribe({\n      next: response => {\n        if (response.StatusCode === 0 && response.Entries) {\n          // 將API資料轉換為組件需要的格式\n          this.convertAPIDataToTemplateGroups(response.Entries);\n        } else {\n          // API 返回錯誤\n          this.templateGroups = [];\n        }\n      },\n      error: () => {\n        // HTTP 請求錯誤\n        this.templateGroups = [];\n      }\n    });\n  }\n  convertAPIDataToTemplateGroups(apiData) {\n    // 將API資料轉換為模板群組格式\n    const groupMap = new Map();\n    apiData.forEach(item => {\n      const groupName = item.CGroupName || '預設群組';\n      if (!groupMap.has(groupName)) {\n        groupMap.set(groupName, []);\n      }\n      groupMap.get(groupName).push({\n        id: item.CTemplateId?.toString() || '',\n        name: item.CTemplateName || '',\n        code: item.CTemplateId?.toString() || '',\n        price: 0,\n        // API中沒有價格資訊，預設為0\n        unit: '個',\n        selected: false\n      });\n    });\n    // 轉換為模板群組陣列\n    this.templateGroups = Array.from(groupMap.entries()).map(([groupName, items], index) => ({\n      id: `group-${index}`,\n      name: groupName,\n      icon: '📋',\n      // 預設圖示\n      items: items,\n      expanded: false\n    }));\n  }\n  selectSpace(space) {\n    // 移除此方法，不再需要\n  }\n  loadTemplatesForSpace(spaceId) {\n    // 根據空間ID載入對應的模板群組\n    // 這裡可以調用API或使用預設資料\n    this.templateGroups = this.getDefaultTemplatesForSpace(spaceId);\n  }\n  getDefaultTemplatesForSpace(spaceId) {\n    const templates = {\n      kitchen: [{\n        id: 'kitchen-standard',\n        name: '廚房標準配備',\n        icon: '🔧',\n        expanded: false,\n        items: [{\n          id: 'kt001',\n          name: '洗碗機',\n          code: 'KT001',\n          price: 38000,\n          unit: '台',\n          selected: false\n        }, {\n          id: 'kt002',\n          name: '烤箱',\n          code: 'KT002',\n          price: 25000,\n          unit: '台',\n          selected: false\n        }, {\n          id: 'kt003',\n          name: '抽油煙機升級',\n          code: 'KT003',\n          price: 15000,\n          unit: '台',\n          selected: false\n        }]\n      }, {\n        id: 'kitchen-premium',\n        name: '廚房高級配備',\n        icon: '⭐',\n        expanded: false,\n        items: [{\n          id: 'kt004',\n          name: '中島檯面',\n          code: 'KT004',\n          price: 80000,\n          unit: '組',\n          selected: false\n        }, {\n          id: 'kt005',\n          name: '智能電磁爐',\n          code: 'KT005',\n          price: 45000,\n          unit: '台',\n          selected: false\n        }]\n      }],\n      living: [{\n        id: 'living-basic',\n        name: '客廳基本配備',\n        icon: '🏠',\n        expanded: false,\n        items: [{\n          id: 'lv001',\n          name: '投影設備',\n          code: 'LV001',\n          price: 50000,\n          unit: '組',\n          selected: false\n        }, {\n          id: 'lv002',\n          name: '音響系統',\n          code: 'LV002',\n          price: 35000,\n          unit: '組',\n          selected: false\n        }]\n      }]\n    };\n    return templates[spaceId] || [];\n  }\n  toggleTemplateGroup(group) {\n    // 收合其他群組\n    this.templateGroups.forEach(g => {\n      if (g.id !== group.id) {\n        g.expanded = false;\n      }\n    });\n    // 切換當前群組\n    group.expanded = !group.expanded;\n  }\n  onTemplateItemChange() {\n    // 當模板項目選擇變更時的處理\n  }\n  getTotalPrice(items) {\n    return items.reduce((total, item) => total + (item.selected ? item.price : 0), 0);\n  }\n  getSelectedItems() {\n    const selected = [];\n    this.templateGroups.forEach(group => {\n      group.items.forEach(item => {\n        if (item.selected) {\n          selected.push(item);\n        }\n      });\n    });\n    return selected;\n  }\n  getSelectedTotalPrice() {\n    return this.getSelectedItems().reduce((total, item) => total + item.price, 0);\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要套用的模板項目',\n      2: '確認套用詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  hasConflicts() {\n    // 模擬衝突檢測邏輯\n    return this.getSelectedItems().length > 2;\n  }\n  getConflictCount() {\n    // 模擬衝突數量\n    return this.hasConflicts() ? 1 : 0;\n  }\n  applyTemplate() {\n    const config = {\n      spaceId: 'common',\n      // 通用模板，不特定空間\n      spaceName: '通用模板',\n      selectedItems: this.getSelectedItems(),\n      totalPrice: this.getSelectedTotalPrice()\n    };\n    this.templateApplied.emit(config);\n    this.close();\n  }\n  close() {\n    this.isVisible = false;\n    this.reset();\n    this.closed.emit();\n  }\n  onBackdropClick(event) {\n    if (event.target === event.currentTarget) {\n      this.close();\n    }\n  }\n  reset() {\n    this.currentStep = 1;\n    this.templateGroups = [];\n  }\n  // 公共API方法\n  open() {\n    this.isVisible = true;\n    this.reset();\n    this.loadTemplatesFromAPI();\n  }\n  static {\n    this.ɵfac = function SpaceTemplateSelectorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpaceTemplateSelectorComponent)(i0.ɵɵdirectiveInject(i1.TemplateService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SpaceTemplateSelectorComponent,\n      selectors: [[\"app-space-template-selector\"]],\n      inputs: {\n        isVisible: \"isVisible\",\n        buildCaseId: \"buildCaseId\"\n      },\n      outputs: {\n        templateApplied: \"templateApplied\",\n        closed: \"closed\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 25,\n      vars: 18,\n      consts: [[1, \"space-template-modal\", 3, \"click\"], [1, \"space-template-content\", 3, \"click\"], [1, \"space-template-header\"], [1, \"space-template-title\"], [1, \"close-btn\", 3, \"click\"], [1, \"space-template-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"space-template-footer\"], [1, \"progress-info\"], [1, \"step-buttons\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"class\", \"btn btn-secondary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"template-selection\"], [1, \"section-title\"], [1, \"fas\", \"fa-layer-group\", \"mr-2\"], [1, \"template-groups\"], [\"class\", \"template-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-group\"], [1, \"template-group-header\", 3, \"click\"], [1, \"group-info\"], [1, \"group-name\"], [1, \"group-stats\"], [1, \"group-toggle\"], [1, \"template-items\"], [\"class\", \"template-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-item\"], [\"type\", \"checkbox\", 1, \"template-checkbox\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"template-info\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"item-price\"], [\"type\", \"number\", 1, \"price-input\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"confirmation-area\"], [1, \"fas\", \"fa-check-circle\", \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"summary-price\"], [1, \"selected-items-list\"], [1, \"items-list-title\"], [1, \"items-list\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"conflict-warning\", 4, \"ngIf\"], [1, \"conflict-warning\"], [1, \"warning-text\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-success\", 3, \"click\", \"disabled\"]],\n      template: function SpaceTemplateSelectorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_div_click_0_listener($event) {\n            return ctx.onBackdropClick($event);\n          });\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_div_click_1_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵtext(4, \"\\u7A7A\\u9593\\u6A21\\u677F\\u9078\\u64C7\\u5668\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_5_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(6, \"\\u00D7\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7);\n          i0.ɵɵtext(10, \"1. \\u9078\\u64C7\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 7);\n          i0.ɵɵtext(12, \"2. \\u78BA\\u8A8D\\u5957\\u7528\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, SpaceTemplateSelectorComponent_div_13_Template, 7, 1, \"div\", 8)(14, SpaceTemplateSelectorComponent_div_14_Template, 20, 6, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"div\", 10)(17, \"span\");\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 11)(20, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function SpaceTemplateSelectorComponent_Template_button_click_20_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(21, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, SpaceTemplateSelectorComponent_button_22_Template, 2, 0, \"button\", 13)(23, SpaceTemplateSelectorComponent_button_23_Template, 2, 1, \"button\", 14)(24, SpaceTemplateSelectorComponent_button_24_Template, 2, 1, \"button\", 15);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"show\", ctx.isVisible);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(10, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(14, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.getProgressText());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i2.NgClass, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.space-template-modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n.space-template-modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.space-template-content[_ngcontent-%COMP%] {\\n  background: var(--color-basic-100);\\n  border-radius: 12px;\\n  width: 90%;\\n  max-width: 1000px;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n\\n.space-template-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);\\n  color: var(--color-basic-100);\\n  padding: 20px 30px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.space-template-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  color: var(--color-basic-100);\\n  font-size: 24px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  border: none;\\n  background: none;\\n  padding: 0;\\n  width: 30px;\\n  height: 30px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  transition: background 0.3s ease;\\n}\\n\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n.space-template-body[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n\\n\\n\\n.step-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n  gap: 10px;\\n}\\n\\n.step-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n\\n.step-item.active[_ngcontent-%COMP%] {\\n  background: var(--color-primary-600);\\n  color: var(--color-basic-100);\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.3);\\n}\\n\\n.step-item.completed[_ngcontent-%COMP%] {\\n  background: var(--color-success-600);\\n  color: var(--color-basic-100);\\n}\\n\\n.step-item.pending[_ngcontent-%COMP%] {\\n  background: var(--color-basic-200);\\n  color: var(--color-basic-600);\\n}\\n\\n\\n\\n.space-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-basic-800);\\n  margin-bottom: 15px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.space-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n\\n.space-card[_ngcontent-%COMP%] {\\n  border: 2px solid var(--color-basic-300);\\n  border-radius: 8px;\\n  padding: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-align: center;\\n  background: var(--color-basic-100);\\n}\\n\\n.space-card[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-primary-500);\\n  background: var(--color-primary-100);\\n}\\n\\n.space-card.selected[_ngcontent-%COMP%] {\\n  border-color: var(--color-primary-600);\\n  background: var(--color-primary-100);\\n}\\n\\n.space-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 8px;\\n}\\n\\n.space-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 4px;\\n}\\n\\n.space-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n\\n\\n.template-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.template-groups[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 15px;\\n}\\n\\n.template-group[_ngcontent-%COMP%] {\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.template-group-header[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 16px 20px;\\n  cursor: pointer;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  transition: background 0.3s ease;\\n}\\n\\n.template-group-header[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n}\\n\\n.template-group-header.active[_ngcontent-%COMP%] {\\n  background: #e7f3ff;\\n  border-left: 4px solid #667eea;\\n}\\n\\n.group-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.group-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.group-stats[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n.group-toggle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  transition: transform 0.3s ease;\\n}\\n\\n.group-toggle.expanded[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.template-items[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n\\n.template-items.expanded[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n}\\n\\n.template-item[_ngcontent-%COMP%] {\\n  padding: 12px 20px;\\n  border-bottom: 1px solid #f0f0f0;\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  transition: background 0.2s ease;\\n}\\n\\n.template-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n\\n.template-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.template-checkbox[_ngcontent-%COMP%] {\\n  transform: scale(1.2);\\n}\\n\\n.template-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr 1fr;\\n  gap: 20px;\\n  align-items: center;\\n}\\n\\n.item-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.item-code[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 12px;\\n}\\n\\n.item-price[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n\\n.price-input[_ngcontent-%COMP%] {\\n  width: 90px;\\n  padding: 4px 8px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  text-align: right;\\n  font-size: 12px;\\n}\\n\\n\\n\\n.confirmation-area[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.selected-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.summary-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.summary-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n\\n.selected-items-list[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 15px;\\n  border-radius: 6px;\\n  margin-bottom: 15px;\\n}\\n\\n.items-list-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 10px;\\n}\\n\\n.items-list[_ngcontent-%COMP%] {\\n  margin-left: 20px;\\n  line-height: 1.6;\\n}\\n\\n.conflict-warning[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n\\n.warning-text[_ngcontent-%COMP%] {\\n  color: #856404;\\n  font-size: 14px;\\n}\\n\\n\\n\\n.space-template-footer[_ngcontent-%COMP%] {\\n  padding: 20px 30px;\\n  border-top: 1px solid #e9ecef;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background: #f8f9fa;\\n  border-radius: 0 0 12px 12px;\\n}\\n\\n.step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    transform: translateY(-50px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  .template-info[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 8px;\\n  }\\n  .space-template-content[_ngcontent-%COMP%] {\\n    width: 95%;\\n    margin: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_ngModelChange_1_listener", "$event", "item_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_change_1_listener", "ctx_r2", "ɵɵnextContext", "onTemplateItemChange", "ɵɵelementEnd", "ɵɵtext", "SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_ngModelChange_13_listener", "price", "SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template_input_change_13_listener", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "name", "code", "unit", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "SpaceTemplateSelectorComponent_div_13_div_6_Template_div_click_1_listener", "group_r2", "_r1", "toggleTemplateGroup", "ɵɵtemplate", "SpaceTemplateSelectorComponent_div_13_div_6_div_11_Template", "ɵɵclassProp", "expanded", "ɵɵtextInterpolate2", "icon", "items", "length", "getTotalPrice", "ɵɵproperty", "ɵɵelement", "SpaceTemplateSelectorComponent_div_13_div_6_Template", "templateGroups", "ɵɵtextInterpolate3", "item_r6", "getConflictCount", "SpaceTemplateSelectorComponent_div_14_li_18_Template", "SpaceTemplateSelectorComponent_div_14_div_19_Template", "getSelectedItems", "getSelectedTotalPrice", "hasConflicts", "SpaceTemplateSelectorComponent_button_22_Template_button_click_0_listener", "_r7", "previousStep", "SpaceTemplateSelectorComponent_button_23_Template_button_click_0_listener", "_r8", "nextStep", "canProceed", "SpaceTemplateSelectorComponent_button_24_Template_button_click_0_listener", "_r9", "applyTemplate", "SpaceTemplateSelectorComponent", "constructor", "templateService", "isVisible", "buildCaseId", "templateApplied", "closed", "currentStep", "ngOnInit", "loadTemplatesFromAPI", "getTemplateListArgs", "CTemplateType", "PageIndex", "PageSize", "CTemplateName", "apiTemplateGetTemplateListForCommonPost$Json", "body", "subscribe", "next", "response", "StatusCode", "Entries", "convertAPIDataToTemplateGroups", "error", "apiData", "groupMap", "Map", "for<PERSON>ach", "item", "groupName", "CGroupName", "has", "set", "get", "push", "id", "CTemplateId", "toString", "Array", "from", "entries", "map", "index", "selectSpace", "space", "loadTemplatesForSpace", "spaceId", "getDefaultTemplatesForSpace", "templates", "kitchen", "living", "group", "g", "reduce", "total", "getProgressText", "progressTexts", "config", "spaceName", "selectedItems", "totalPrice", "emit", "close", "reset", "onBackdropClick", "event", "target", "currentTarget", "open", "ɵɵdirectiveInject", "i1", "TemplateService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SpaceTemplateSelectorComponent_Template", "rf", "ctx", "SpaceTemplateSelectorComponent_Template_div_click_0_listener", "SpaceTemplateSelectorComponent_Template_div_click_1_listener", "stopPropagation", "SpaceTemplateSelectorComponent_Template_button_click_5_listener", "SpaceTemplateSelectorComponent_div_13_Template", "SpaceTemplateSelectorComponent_div_14_Template", "SpaceTemplateSelectorComponent_Template_button_click_20_listener", "SpaceTemplateSelectorComponent_button_22_Template", "SpaceTemplateSelectorComponent_button_23_Template", "SpaceTemplateSelectorComponent_button_24_Template", "ɵɵpureFunction3", "_c0", "i2", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "DefaultValueAccessor", "NumberValueAccessor", "CheckboxControlValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\space-template-selector\\space-template-selector.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { TemplateService } from '../../../../services/api/services/template.service';\r\nimport { TemplateService } from 'src/services/api/services/template.service';\r\nimport { TemplateGetListArgs } from 'src/services/api/models';\r\n\r\nexport interface SpaceOption {\r\n    id: string;\r\n    name: string;\r\n    icon: string;\r\n    templateCount: number;\r\n}\r\n\r\nexport interface TemplateItem {\r\n    id: string;\r\n    name: string;\r\n    code: string;\r\n    price: number;\r\n    unit: string;\r\n    selected: boolean;\r\n}\r\n\r\nexport interface TemplateGroup {\r\n    id: string;\r\n    name: string;\r\n    icon: string;\r\n    items: TemplateItem[];\r\n    expanded: boolean;\r\n}\r\n\r\nexport interface SpaceTemplateConfig {\r\n    spaceId: string;\r\n    spaceName: string;\r\n    selectedItems: TemplateItem[];\r\n    totalPrice: number;\r\n}\r\n\r\n@Component({\r\n    selector: 'app-space-template-selector',\r\n    standalone: true,\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule\r\n    ],\r\n    templateUrl: './space-template-selector.component.html',\r\n    styleUrls: ['./space-template-selector.component.scss']\r\n})\r\nexport class SpaceTemplateSelectorComponent implements OnInit {\r\n    @Input() isVisible: boolean = false;\r\n    @Input() buildCaseId: string = '';\r\n    @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();\r\n    @Output() closed = new EventEmitter<void>();\r\n\r\n    currentStep: number = 1; // 現在從步驟1開始（選擇模板）\r\n    templateGroups: TemplateGroup[] = [];\r\n\r\n    constructor(private templateService: TemplateService) {}\r\n\r\n    ngOnInit() {\r\n        // 自動載入模板資料\r\n        this.loadTemplatesFromAPI();\r\n    }\r\n\r\n    loadTemplatesFromAPI() {\r\n        // 準備 API 請求參數\r\n        const getTemplateListArgs: TemplateGetListArgs = {\r\n            CTemplateType: 1, // 1=客變需求\r\n            PageIndex: 1,\r\n            PageSize: 100, // 載入足夠的資料\r\n            CTemplateName: null // 不篩選名稱\r\n        };\r\n\r\n        // 調用 GetTemplateListForCommon API\r\n        this.templateService.apiTemplateGetTemplateListForCommonPost$Json({\r\n            body: getTemplateListArgs\r\n        }).subscribe({\r\n            next: (response) => {\r\n                if (response.StatusCode === 0 && response.Entries) {\r\n                    // 將API資料轉換為組件需要的格式\r\n                    this.convertAPIDataToTemplateGroups(response.Entries);\r\n                } else {\r\n                    // API 返回錯誤\r\n                    this.templateGroups = [];\r\n                }\r\n            },\r\n            error: () => {\r\n                // HTTP 請求錯誤\r\n                this.templateGroups = [];\r\n            }\r\n        });\r\n    }\r\n\r\n    convertAPIDataToTemplateGroups(apiData: any[]) {\r\n        // 將API資料轉換為模板群組格式\r\n        const groupMap = new Map<string, TemplateItem[]>();\r\n\r\n        apiData.forEach(item => {\r\n            const groupName = item.CGroupName || '預設群組';\r\n            if (!groupMap.has(groupName)) {\r\n                groupMap.set(groupName, []);\r\n            }\r\n            \r\n            groupMap.get(groupName)!.push({\r\n                id: item.CTemplateId?.toString() || '',\r\n                name: item.CTemplateName || '',\r\n                code: item.CTemplateId?.toString() || '',\r\n                price: 0, // API中沒有價格資訊，預設為0\r\n                unit: '個',\r\n                selected: false\r\n            });\r\n        });\r\n\r\n        // 轉換為模板群組陣列\r\n        this.templateGroups = Array.from(groupMap.entries()).map(([groupName, items], index) => ({\r\n            id: `group-${index}`,\r\n            name: groupName,\r\n            icon: '📋', // 預設圖示\r\n            items: items,\r\n            expanded: false\r\n        }));\r\n    }\r\n\r\n    selectSpace(space: SpaceOption) {\r\n        // 移除此方法，不再需要\r\n    }\r\n\r\n    loadTemplatesForSpace(spaceId: string) {\r\n        // 根據空間ID載入對應的模板群組\r\n        // 這裡可以調用API或使用預設資料\r\n        this.templateGroups = this.getDefaultTemplatesForSpace(spaceId);\r\n    }\r\n\r\n    getDefaultTemplatesForSpace(spaceId: string): TemplateGroup[] {\r\n        const templates: Record<string, TemplateGroup[]> = {\r\n            kitchen: [\r\n                {\r\n                    id: 'kitchen-standard',\r\n                    name: '廚房標準配備',\r\n                    icon: '🔧',\r\n                    expanded: false,\r\n                    items: [\r\n                        { id: 'kt001', name: '洗碗機', code: 'KT001', price: 38000, unit: '台', selected: false },\r\n                        { id: 'kt002', name: '烤箱', code: 'KT002', price: 25000, unit: '台', selected: false },\r\n                        { id: 'kt003', name: '抽油煙機升級', code: 'KT003', price: 15000, unit: '台', selected: false }\r\n                    ]\r\n                },\r\n                {\r\n                    id: 'kitchen-premium',\r\n                    name: '廚房高級配備',\r\n                    icon: '⭐',\r\n                    expanded: false,\r\n                    items: [\r\n                        { id: 'kt004', name: '中島檯面', code: 'KT004', price: 80000, unit: '組', selected: false },\r\n                        { id: 'kt005', name: '智能電磁爐', code: 'KT005', price: 45000, unit: '台', selected: false }\r\n                    ]\r\n                }\r\n            ],\r\n            living: [\r\n                {\r\n                    id: 'living-basic',\r\n                    name: '客廳基本配備',\r\n                    icon: '🏠',\r\n                    expanded: false,\r\n                    items: [\r\n                        { id: 'lv001', name: '投影設備', code: 'LV001', price: 50000, unit: '組', selected: false },\r\n                        { id: 'lv002', name: '音響系統', code: 'LV002', price: 35000, unit: '組', selected: false }\r\n                    ]\r\n                }\r\n            ]\r\n        };\r\n\r\n        return templates[spaceId] || [];\r\n    }\r\n\r\n    toggleTemplateGroup(group: TemplateGroup) {\r\n        // 收合其他群組\r\n        this.templateGroups.forEach(g => {\r\n            if (g.id !== group.id) {\r\n                g.expanded = false;\r\n            }\r\n        });\r\n\r\n        // 切換當前群組\r\n        group.expanded = !group.expanded;\r\n    }\r\n\r\n    onTemplateItemChange() {\r\n        // 當模板項目選擇變更時的處理\r\n    }\r\n\r\n    getTotalPrice(items: TemplateItem[]): number {\r\n        return items.reduce((total, item) => total + (item.selected ? item.price : 0), 0);\r\n    }\r\n\r\n    getSelectedItems(): TemplateItem[] {\r\n        const selected: TemplateItem[] = [];\r\n        this.templateGroups.forEach(group => {\r\n            group.items.forEach(item => {\r\n                if (item.selected) {\r\n                    selected.push(item);\r\n                }\r\n            });\r\n        });\r\n        return selected;\r\n    }\r\n\r\n    getSelectedTotalPrice(): number {\r\n        return this.getSelectedItems().reduce((total, item) => total + item.price, 0);\r\n    }\r\n\r\n    canProceed(): boolean {\r\n        switch (this.currentStep) {\r\n            case 1:\r\n                return this.getSelectedItems().length > 0;\r\n            case 2:\r\n                return true;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n\r\n    nextStep() {\r\n        if (this.canProceed() && this.currentStep < 2) {\r\n            this.currentStep++;\r\n        }\r\n    }\r\n\r\n    previousStep() {\r\n        if (this.currentStep > 1) {\r\n            this.currentStep--;\r\n        }\r\n    }\r\n\r\n    getProgressText(): string {\r\n        const progressTexts = {\r\n            1: '請選擇要套用的模板項目',\r\n            2: '確認套用詳情'\r\n        };\r\n        return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n    }\r\n\r\n    hasConflicts(): boolean {\r\n        // 模擬衝突檢測邏輯\r\n        return this.getSelectedItems().length > 2;\r\n    }\r\n\r\n    getConflictCount(): number {\r\n        // 模擬衝突數量\r\n        return this.hasConflicts() ? 1 : 0;\r\n    }\r\n\r\n    applyTemplate() {\r\n        const config: SpaceTemplateConfig = {\r\n            spaceId: 'common', // 通用模板，不特定空間\r\n            spaceName: '通用模板',\r\n            selectedItems: this.getSelectedItems(),\r\n            totalPrice: this.getSelectedTotalPrice()\r\n        };\r\n\r\n        this.templateApplied.emit(config);\r\n        this.close();\r\n    }\r\n\r\n    close() {\r\n        this.isVisible = false;\r\n        this.reset();\r\n        this.closed.emit();\r\n    }\r\n\r\n    onBackdropClick(event: Event) {\r\n        if (event.target === event.currentTarget) {\r\n            this.close();\r\n        }\r\n    }\r\n\r\n    private reset() {\r\n        this.currentStep = 1;\r\n        this.templateGroups = [];\r\n    }\r\n\r\n    // 公共API方法\r\n    open() {\r\n        this.isVisible = true;\r\n        this.reset();\r\n        this.loadTemplatesFromAPI();\r\n    }\r\n}\r\n", "<!-- 空間模板選擇器共用元件 -->\r\n<div class=\"space-template-modal\" [class.show]=\"isVisible\" (click)=\"onBackdropClick($event)\">\r\n  <div class=\"space-template-content\" (click)=\"$event.stopPropagation()\">\r\n    <div class=\"space-template-header\">\r\n      <div class=\"space-template-title\">空間模板選擇器</div>\r\n      <button class=\"close-btn\" (click)=\"close()\">&times;</button>\r\n    </div>\r\n\r\n    <div class=\"space-template-body\">\r\n      <!-- 步驟導航 -->\r\n      <div class=\"step-nav\">\r\n        <div class=\"step-item\" [ngClass]=\"{\r\n          'active': currentStep === 1,\r\n          'completed': currentStep > 1,\r\n          'pending': currentStep < 1\r\n        }\">1. 選擇模板</div>\r\n        <div class=\"step-item\" [ngClass]=\"{\r\n          'active': currentStep === 2,\r\n          'completed': currentStep > 2,\r\n          'pending': currentStep < 2\r\n        }\">2. 確認套用</div>\r\n      </div>\r\n\r\n      <!-- 步驟1: 選擇模板 -->\r\n      <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n        <div class=\"template-selection\">\r\n          <div class=\"section-title\">\r\n            <i class=\"fas fa-layer-group mr-2\"></i>選擇模板群組\r\n          </div>\r\n          <div class=\"template-groups\">\r\n            <div *ngFor=\"let group of templateGroups\" class=\"template-group\">\r\n              <div class=\"template-group-header\" [class.active]=\"group.expanded\" (click)=\"toggleTemplateGroup(group)\">\r\n                <div class=\"group-info\">\r\n                  <div class=\"group-name\">{{ group.icon }} {{ group.name }}</div>\r\n                  <div class=\"group-stats\">{{ group.items.length }}個項目 • ${{ getTotalPrice(group.items) | number }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"group-toggle\" [class.expanded]=\"group.expanded\">▼</div>\r\n              </div>\r\n              <div class=\"template-items\" [class.expanded]=\"group.expanded\">\r\n                <div *ngFor=\"let item of group.items\" class=\"template-item\">\r\n                  <input type=\"checkbox\" class=\"template-checkbox\" [(ngModel)]=\"item.selected\"\r\n                    (change)=\"onTemplateItemChange()\">\r\n                  <div class=\"template-info\">\r\n                    <div>\r\n                      <div class=\"item-name\">{{ item.name }}</div>\r\n                      <div class=\"item-code\">{{ item.code }}</div>\r\n                    </div>\r\n                    <div>{{ item.unit }}</div>\r\n                    <div class=\"item-price\">${{ item.price | number }}</div>\r\n                    <input type=\"number\" class=\"price-input\" [(ngModel)]=\"item.price\" (change)=\"onTemplateItemChange()\">\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 步驟2: 確認套用 -->\r\n      <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n        <div class=\"confirmation-area\">\r\n          <div class=\"section-title\">\r\n            <i class=\"fas fa-check-circle mr-2\"></i>確認套用詳情\r\n          </div>\r\n\r\n          <div class=\"selected-summary\">\r\n            <div class=\"summary-text\">\r\n              將套用 <strong>通用模板</strong>：{{ getSelectedItems().length }}個模板項目\r\n            </div>\r\n            <div class=\"summary-price\">總價值 ${{ getSelectedTotalPrice() | number }}</div>\r\n          </div>\r\n\r\n          <div class=\"selected-items-list\">\r\n            <div class=\"items-list-title\">套用項目清單：</div>\r\n            <ul class=\"items-list\">\r\n              <li *ngFor=\"let item of getSelectedItems()\">\r\n                {{ item.name }} ({{ item.code }}) - ${{ item.price | number }}\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div *ngIf=\"hasConflicts()\" class=\"conflict-warning\">\r\n            <div class=\"warning-text\">\r\n              <strong><i class=\"fas fa-exclamation-triangle mr-1\"></i>衝突檢測：</strong>\r\n              檢測到 {{ getConflictCount() }} 個項目可能與現有需求重複，系統將自動處理衝突項目。\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"space-template-footer\">\r\n      <div class=\"progress-info\">\r\n        <span>{{ getProgressText() }}</span>\r\n      </div>\r\n      <div class=\"step-buttons\">\r\n        <button class=\"btn btn-secondary\" (click)=\"close()\">取消</button>\r\n        <button *ngIf=\"currentStep > 1\" class=\"btn btn-secondary\" (click)=\"previousStep()\">上一步</button>\r\n        <button *ngIf=\"currentStep < 2\" class=\"btn btn-primary\" [disabled]=\"!canProceed()\"\r\n          (click)=\"nextStep()\">下一步</button>\r\n        <button *ngIf=\"currentStep === 2\" class=\"btn btn-success\" [disabled]=\"getSelectedItems().length === 0\"\r\n          (click)=\"applyTemplate()\">確認套用</button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAAuC,eAAe;AACtF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;ICuC1BC,EADF,CAAAC,cAAA,cAA4D,gBAEtB;IADaD,EAAA,CAAAE,gBAAA,2BAAAC,2FAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAJ,OAAA,CAAAK,QAAA,EAAAN,MAAA,MAAAC,OAAA,CAAAK,QAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA2B;IAC1EJ,EAAA,CAAAY,UAAA,oBAAAC,oFAAA;MAAAb,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAE,oBAAA,EAAsB;IAAA,EAAC;IADnChB,EAAA,CAAAiB,YAAA,EACoC;IAGhCjB,EAFJ,CAAAC,cAAA,cAA2B,UACpB,cACoB;IAAAD,EAAA,CAAAkB,MAAA,GAAe;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAC5CjB,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAkB,MAAA,GAAe;IACxClB,EADwC,CAAAiB,YAAA,EAAM,EACxC;IACNjB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAkB,MAAA,GAAe;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAC1BjB,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAkB,MAAA,IAA0B;;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IACxDjB,EAAA,CAAAC,cAAA,iBAAoG;IAA3DD,EAAA,CAAAE,gBAAA,2BAAAiB,4FAAAf,MAAA;MAAA,MAAAC,OAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAJ,OAAA,CAAAe,KAAA,EAAAhB,MAAA,MAAAC,OAAA,CAAAe,KAAA,GAAAhB,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAACJ,EAAA,CAAAY,UAAA,oBAAAS,qFAAA;MAAArB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAAUG,MAAA,CAAAE,oBAAA,EAAsB;IAAA,EAAC;IAEvGhB,EAFI,CAAAiB,YAAA,EAAoG,EAChG,EACF;;;;IAX6CjB,EAAA,CAAAsB,SAAA,EAA2B;IAA3BtB,EAAA,CAAAuB,gBAAA,YAAAlB,OAAA,CAAAK,QAAA,CAA2B;IAIjDV,EAAA,CAAAsB,SAAA,GAAe;IAAftB,EAAA,CAAAwB,iBAAA,CAAAnB,OAAA,CAAAoB,IAAA,CAAe;IACfzB,EAAA,CAAAsB,SAAA,GAAe;IAAftB,EAAA,CAAAwB,iBAAA,CAAAnB,OAAA,CAAAqB,IAAA,CAAe;IAEnC1B,EAAA,CAAAsB,SAAA,GAAe;IAAftB,EAAA,CAAAwB,iBAAA,CAAAnB,OAAA,CAAAsB,IAAA,CAAe;IACI3B,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,QAAAxB,OAAA,CAAAe,KAAA,MAA0B;IACTpB,EAAA,CAAAsB,SAAA,GAAwB;IAAxBtB,EAAA,CAAAuB,gBAAA,YAAAlB,OAAA,CAAAe,KAAA,CAAwB;;;;;;IAnBvEpB,EADF,CAAAC,cAAA,cAAiE,cACyC;IAArCD,EAAA,CAAAY,UAAA,mBAAAkB,0EAAA;MAAA,MAAAC,QAAA,GAAA/B,EAAA,CAAAM,aAAA,CAAA0B,GAAA,EAAAxB,SAAA;MAAA,MAAAM,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAmB,mBAAA,CAAAF,QAAA,CAA0B;IAAA,EAAC;IAEnG/B,EADF,CAAAC,cAAA,cAAwB,cACE;IAAAD,EAAA,CAAAkB,MAAA,GAAiC;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAC/DjB,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAkB,MAAA,GACzB;;IACFlB,EADE,CAAAiB,YAAA,EAAM,EACF;IACNjB,EAAA,CAAAC,cAAA,cAA4D;IAAAD,EAAA,CAAAkB,MAAA,aAAC;IAC/DlB,EAD+D,CAAAiB,YAAA,EAAM,EAC/D;IACNjB,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAkC,UAAA,KAAAC,2DAAA,mBAA4D;IAchEnC,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IAvB+BjB,EAAA,CAAAsB,SAAA,EAA+B;IAA/BtB,EAAA,CAAAoC,WAAA,WAAAL,QAAA,CAAAM,QAAA,CAA+B;IAEtCrC,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAsC,kBAAA,KAAAP,QAAA,CAAAQ,IAAA,OAAAR,QAAA,CAAAN,IAAA,KAAiC;IAChCzB,EAAA,CAAAsB,SAAA,GACzB;IADyBtB,EAAA,CAAAsC,kBAAA,KAAAP,QAAA,CAAAS,KAAA,CAAAC,MAAA,iCAAAzC,EAAA,CAAA6B,WAAA,QAAAf,MAAA,CAAA4B,aAAA,CAAAX,QAAA,CAAAS,KAAA,QACzB;IAEwBxC,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAoC,WAAA,aAAAL,QAAA,CAAAM,QAAA,CAAiC;IAEjCrC,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAoC,WAAA,aAAAL,QAAA,CAAAM,QAAA,CAAiC;IACrCrC,EAAA,CAAAsB,SAAA,EAAc;IAAdtB,EAAA,CAAA2C,UAAA,YAAAZ,QAAA,CAAAS,KAAA,CAAc;;;;;IAd1CxC,EAFJ,CAAAC,cAAA,cAAoD,cAClB,cACH;IACzBD,EAAA,CAAA4C,SAAA,YAAuC;IAAA5C,EAAA,CAAAkB,MAAA,4CACzC;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAkC,UAAA,IAAAW,oDAAA,oBAAiE;IA2BvE7C,EAFI,CAAAiB,YAAA,EAAM,EACF,EACF;;;;IA3BuBjB,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAA2C,UAAA,YAAA7B,MAAA,CAAAgC,cAAA,CAAiB;;;;;IA8CtC9C,EAAA,CAAAC,cAAA,SAA4C;IAC1CD,EAAA,CAAAkB,MAAA,GACF;;IAAAlB,EAAA,CAAAiB,YAAA,EAAK;;;;IADHjB,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAA+C,kBAAA,MAAAC,OAAA,CAAAvB,IAAA,QAAAuB,OAAA,CAAAtB,IAAA,WAAA1B,EAAA,CAAA6B,WAAA,OAAAmB,OAAA,CAAA5B,KAAA,OACF;;;;;IAMApB,EAFJ,CAAAC,cAAA,cAAqD,cACzB,aAChB;IAAAD,EAAA,CAAA4C,SAAA,YAAgD;IAAA5C,EAAA,CAAAkB,MAAA,qCAAK;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IACtEjB,EAAA,CAAAkB,MAAA,GACF;IACFlB,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IAFFjB,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAA4B,kBAAA,yBAAAd,MAAA,CAAAmC,gBAAA,+JACF;;;;;IAxBFjD,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAA4C,SAAA,YAAwC;IAAA5C,EAAA,CAAAkB,MAAA,4CAC1C;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAGJjB,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAkB,MAAA,2BAAI;IAAAlB,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAkB,MAAA,+BAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;IAAAjB,EAAA,CAAAkB,MAAA,IAC3B;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IACNjB,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAkB,MAAA,IAA2C;;IACxElB,EADwE,CAAAiB,YAAA,EAAM,EACxE;IAGJjB,EADF,CAAAC,cAAA,eAAiC,eACD;IAAAD,EAAA,CAAAkB,MAAA,kDAAO;IAAAlB,EAAA,CAAAiB,YAAA,EAAM;IAC3CjB,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAkC,UAAA,KAAAgB,oDAAA,iBAA4C;IAIhDlD,EADE,CAAAiB,YAAA,EAAK,EACD;IAENjB,EAAA,CAAAkC,UAAA,KAAAiB,qDAAA,kBAAqD;IAOzDnD,EADE,CAAAiB,YAAA,EAAM,EACF;;;;IArB2BjB,EAAA,CAAAsB,SAAA,IAC3B;IAD2BtB,EAAA,CAAA4B,kBAAA,WAAAd,MAAA,CAAAsC,gBAAA,GAAAX,MAAA,oCAC3B;IAC2BzC,EAAA,CAAAsB,SAAA,GAA2C;IAA3CtB,EAAA,CAAA4B,kBAAA,yBAAA5B,EAAA,CAAA6B,WAAA,QAAAf,MAAA,CAAAuC,qBAAA,QAA2C;IAM/CrD,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAA2C,UAAA,YAAA7B,MAAA,CAAAsC,gBAAA,GAAqB;IAMxCpD,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAA2C,UAAA,SAAA7B,MAAA,CAAAwC,YAAA,GAAoB;;;;;;IAgB5BtD,EAAA,CAAAC,cAAA,iBAAmF;IAAzBD,EAAA,CAAAY,UAAA,mBAAA2C,0EAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAAkD,GAAA;MAAA,MAAA1C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAA2C,YAAA,EAAc;IAAA,EAAC;IAACzD,EAAA,CAAAkB,MAAA,yBAAG;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;;;;;;IAC/FjB,EAAA,CAAAC,cAAA,iBACuB;IAArBD,EAAA,CAAAY,UAAA,mBAAA8C,0EAAA;MAAA1D,EAAA,CAAAM,aAAA,CAAAqD,GAAA;MAAA,MAAA7C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAA8C,QAAA,EAAU;IAAA,EAAC;IAAC5D,EAAA,CAAAkB,MAAA,yBAAG;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;;;;IADqBjB,EAAA,CAAA2C,UAAA,cAAA7B,MAAA,CAAA+C,UAAA,GAA0B;;;;;;IAElF7D,EAAA,CAAAC,cAAA,iBAC4B;IAA1BD,EAAA,CAAAY,UAAA,mBAAAkD,0EAAA;MAAA9D,EAAA,CAAAM,aAAA,CAAAyD,GAAA;MAAA,MAAAjD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAW,WAAA,CAASG,MAAA,CAAAkD,aAAA,EAAe;IAAA,EAAC;IAAChE,EAAA,CAAAkB,MAAA,+BAAI;IAAAlB,EAAA,CAAAiB,YAAA,EAAS;;;;IADiBjB,EAAA,CAAA2C,UAAA,aAAA7B,MAAA,CAAAsC,gBAAA,GAAAX,MAAA,OAA4C;;;ADrD9G,OAAM,MAAOwB,8BAA8B;EASvCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAR1B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,WAAW,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAG,IAAIzE,YAAY,EAAuB;IACzD,KAAA0E,MAAM,GAAG,IAAI1E,YAAY,EAAQ;IAE3C,KAAA2E,WAAW,GAAW,CAAC,CAAC,CAAC;IACzB,KAAA1B,cAAc,GAAoB,EAAE;EAEmB;EAEvD2B,QAAQA,CAAA;IACJ;IACA,IAAI,CAACC,oBAAoB,EAAE;EAC/B;EAEAA,oBAAoBA,CAAA;IAChB;IACA,MAAMC,mBAAmB,GAAwB;MAC7CC,aAAa,EAAE,CAAC;MAAE;MAClBC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,GAAG;MAAE;MACfC,aAAa,EAAE,IAAI,CAAC;KACvB;IAED;IACA,IAAI,CAACZ,eAAe,CAACa,4CAA4C,CAAC;MAC9DC,IAAI,EAAEN;KACT,CAAC,CAACO,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACf,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,IAAID,QAAQ,CAACE,OAAO,EAAE;UAC/C;UACA,IAAI,CAACC,8BAA8B,CAACH,QAAQ,CAACE,OAAO,CAAC;QACzD,CAAC,MAAM;UACH;UACA,IAAI,CAACxC,cAAc,GAAG,EAAE;QAC5B;MACJ,CAAC;MACD0C,KAAK,EAAEA,CAAA,KAAK;QACR;QACA,IAAI,CAAC1C,cAAc,GAAG,EAAE;MAC5B;KACH,CAAC;EACN;EAEAyC,8BAA8BA,CAACE,OAAc;IACzC;IACA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,EAA0B;IAElDF,OAAO,CAACG,OAAO,CAACC,IAAI,IAAG;MACnB,MAAMC,SAAS,GAAGD,IAAI,CAACE,UAAU,IAAI,MAAM;MAC3C,IAAI,CAACL,QAAQ,CAACM,GAAG,CAACF,SAAS,CAAC,EAAE;QAC1BJ,QAAQ,CAACO,GAAG,CAACH,SAAS,EAAE,EAAE,CAAC;MAC/B;MAEAJ,QAAQ,CAACQ,GAAG,CAACJ,SAAS,CAAE,CAACK,IAAI,CAAC;QAC1BC,EAAE,EAAEP,IAAI,CAACQ,WAAW,EAAEC,QAAQ,EAAE,IAAI,EAAE;QACtC7E,IAAI,EAAEoE,IAAI,CAACd,aAAa,IAAI,EAAE;QAC9BrD,IAAI,EAAEmE,IAAI,CAACQ,WAAW,EAAEC,QAAQ,EAAE,IAAI,EAAE;QACxClF,KAAK,EAAE,CAAC;QAAE;QACVO,IAAI,EAAE,GAAG;QACTjB,QAAQ,EAAE;OACb,CAAC;IACN,CAAC,CAAC;IAEF;IACA,IAAI,CAACoC,cAAc,GAAGyD,KAAK,CAACC,IAAI,CAACd,QAAQ,CAACe,OAAO,EAAE,CAAC,CAACC,GAAG,CAAC,CAAC,CAACZ,SAAS,EAAEtD,KAAK,CAAC,EAAEmE,KAAK,MAAM;MACrFP,EAAE,EAAE,SAASO,KAAK,EAAE;MACpBlF,IAAI,EAAEqE,SAAS;MACfvD,IAAI,EAAE,IAAI;MAAE;MACZC,KAAK,EAAEA,KAAK;MACZH,QAAQ,EAAE;KACb,CAAC,CAAC;EACP;EAEAuE,WAAWA,CAACC,KAAkB;IAC1B;EAAA;EAGJC,qBAAqBA,CAACC,OAAe;IACjC;IACA;IACA,IAAI,CAACjE,cAAc,GAAG,IAAI,CAACkE,2BAA2B,CAACD,OAAO,CAAC;EACnE;EAEAC,2BAA2BA,CAACD,OAAe;IACvC,MAAME,SAAS,GAAoC;MAC/CC,OAAO,EAAE,CACL;QACId,EAAE,EAAE,kBAAkB;QACtB3E,IAAI,EAAE,QAAQ;QACdc,IAAI,EAAE,IAAI;QACVF,QAAQ,EAAE,KAAK;QACfG,KAAK,EAAE,CACH;UAAE4D,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,KAAK;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE,EACrF;UAAE0F,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,IAAI;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE,EACpF;UAAE0F,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,QAAQ;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE;OAE/F,EACD;QACI0F,EAAE,EAAE,iBAAiB;QACrB3E,IAAI,EAAE,QAAQ;QACdc,IAAI,EAAE,GAAG;QACTF,QAAQ,EAAE,KAAK;QACfG,KAAK,EAAE,CACH;UAAE4D,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE,EACtF;UAAE0F,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,OAAO;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE;OAE9F,CACJ;MACDyG,MAAM,EAAE,CACJ;QACIf,EAAE,EAAE,cAAc;QAClB3E,IAAI,EAAE,QAAQ;QACdc,IAAI,EAAE,IAAI;QACVF,QAAQ,EAAE,KAAK;QACfG,KAAK,EAAE,CACH;UAAE4D,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE,EACtF;UAAE0F,EAAE,EAAE,OAAO;UAAE3E,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE,OAAO;UAAEN,KAAK,EAAE,KAAK;UAAEO,IAAI,EAAE,GAAG;UAAEjB,QAAQ,EAAE;QAAK,CAAE;OAE7F;KAER;IAED,OAAOuG,SAAS,CAACF,OAAO,CAAC,IAAI,EAAE;EACnC;EAEA9E,mBAAmBA,CAACmF,KAAoB;IACpC;IACA,IAAI,CAACtE,cAAc,CAAC8C,OAAO,CAACyB,CAAC,IAAG;MAC5B,IAAIA,CAAC,CAACjB,EAAE,KAAKgB,KAAK,CAAChB,EAAE,EAAE;QACnBiB,CAAC,CAAChF,QAAQ,GAAG,KAAK;MACtB;IACJ,CAAC,CAAC;IAEF;IACA+E,KAAK,CAAC/E,QAAQ,GAAG,CAAC+E,KAAK,CAAC/E,QAAQ;EACpC;EAEArB,oBAAoBA,CAAA;IAChB;EAAA;EAGJ0B,aAAaA,CAACF,KAAqB;IAC/B,OAAOA,KAAK,CAAC8E,MAAM,CAAC,CAACC,KAAK,EAAE1B,IAAI,KAAK0B,KAAK,IAAI1B,IAAI,CAACnF,QAAQ,GAAGmF,IAAI,CAACzE,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACrF;EAEAgC,gBAAgBA,CAAA;IACZ,MAAM1C,QAAQ,GAAmB,EAAE;IACnC,IAAI,CAACoC,cAAc,CAAC8C,OAAO,CAACwB,KAAK,IAAG;MAChCA,KAAK,CAAC5E,KAAK,CAACoD,OAAO,CAACC,IAAI,IAAG;QACvB,IAAIA,IAAI,CAACnF,QAAQ,EAAE;UACfA,QAAQ,CAACyF,IAAI,CAACN,IAAI,CAAC;QACvB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAOnF,QAAQ;EACnB;EAEA2C,qBAAqBA,CAAA;IACjB,OAAO,IAAI,CAACD,gBAAgB,EAAE,CAACkE,MAAM,CAAC,CAACC,KAAK,EAAE1B,IAAI,KAAK0B,KAAK,GAAG1B,IAAI,CAACzE,KAAK,EAAE,CAAC,CAAC;EACjF;EAEAyC,UAAUA,CAAA;IACN,QAAQ,IAAI,CAACW,WAAW;MACpB,KAAK,CAAC;QACF,OAAO,IAAI,CAACpB,gBAAgB,EAAE,CAACX,MAAM,GAAG,CAAC;MAC7C,KAAK,CAAC;QACF,OAAO,IAAI;MACf;QACI,OAAO,KAAK;IACpB;EACJ;EAEAmB,QAAQA,CAAA;IACJ,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACW,WAAW,GAAG,CAAC,EAAE;MAC3C,IAAI,CAACA,WAAW,EAAE;IACtB;EACJ;EAEAf,YAAYA,CAAA;IACR,IAAI,IAAI,CAACe,WAAW,GAAG,CAAC,EAAE;MACtB,IAAI,CAACA,WAAW,EAAE;IACtB;EACJ;EAEAgD,eAAeA,CAAA;IACX,MAAMC,aAAa,GAAG;MAClB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACN;IACD,OAAOA,aAAa,CAAC,IAAI,CAACjD,WAAyC,CAAC,IAAI,EAAE;EAC9E;EAEAlB,YAAYA,CAAA;IACR;IACA,OAAO,IAAI,CAACF,gBAAgB,EAAE,CAACX,MAAM,GAAG,CAAC;EAC7C;EAEAQ,gBAAgBA,CAAA;IACZ;IACA,OAAO,IAAI,CAACK,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;EACtC;EAEAU,aAAaA,CAAA;IACT,MAAM0D,MAAM,GAAwB;MAChCX,OAAO,EAAE,QAAQ;MAAE;MACnBY,SAAS,EAAE,MAAM;MACjBC,aAAa,EAAE,IAAI,CAACxE,gBAAgB,EAAE;MACtCyE,UAAU,EAAE,IAAI,CAACxE,qBAAqB;KACzC;IAED,IAAI,CAACiB,eAAe,CAACwD,IAAI,CAACJ,MAAM,CAAC;IACjC,IAAI,CAACK,KAAK,EAAE;EAChB;EAEAA,KAAKA,CAAA;IACD,IAAI,CAAC3D,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC4D,KAAK,EAAE;IACZ,IAAI,CAACzD,MAAM,CAACuD,IAAI,EAAE;EACtB;EAEAG,eAAeA,CAACC,KAAY;IACxB,IAAIA,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,aAAa,EAAE;MACtC,IAAI,CAACL,KAAK,EAAE;IAChB;EACJ;EAEQC,KAAKA,CAAA;IACT,IAAI,CAACxD,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC1B,cAAc,GAAG,EAAE;EAC5B;EAEA;EACAuF,IAAIA,CAAA;IACA,IAAI,CAACjE,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC4D,KAAK,EAAE;IACZ,IAAI,CAACtD,oBAAoB,EAAE;EAC/B;;;uCA9OST,8BAA8B,EAAAjE,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAA9BvE,8BAA8B;MAAAwE,SAAA;MAAAC,MAAA;QAAAtE,SAAA;QAAAC,WAAA;MAAA;MAAAsE,OAAA;QAAArE,eAAA;QAAAC,MAAA;MAAA;MAAAqE,UAAA;MAAAC,QAAA,GAAA7I,EAAA,CAAA8I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/C3CpJ,EAAA,CAAAC,cAAA,aAA6F;UAAlCD,EAAA,CAAAY,UAAA,mBAAA0I,6DAAAlJ,MAAA;YAAA,OAASiJ,GAAA,CAAApB,eAAA,CAAA7H,MAAA,CAAuB;UAAA,EAAC;UAC1FJ,EAAA,CAAAC,cAAA,aAAuE;UAAnCD,EAAA,CAAAY,UAAA,mBAAA2I,6DAAAnJ,MAAA;YAAA,OAASA,MAAA,CAAAoJ,eAAA,EAAwB;UAAA,EAAC;UAElExJ,EADF,CAAAC,cAAA,aAAmC,aACC;UAAAD,EAAA,CAAAkB,MAAA,iDAAO;UAAAlB,EAAA,CAAAiB,YAAA,EAAM;UAC/CjB,EAAA,CAAAC,cAAA,gBAA4C;UAAlBD,EAAA,CAAAY,UAAA,mBAAA6I,gEAAA;YAAA,OAASJ,GAAA,CAAAtB,KAAA,EAAO;UAAA,EAAC;UAAC/H,EAAA,CAAAkB,MAAA,aAAO;UACrDlB,EADqD,CAAAiB,YAAA,EAAS,EACxD;UAKFjB,EAHJ,CAAAC,cAAA,aAAiC,aAET,aAKjB;UAAAD,EAAA,CAAAkB,MAAA,mCAAO;UAAAlB,EAAA,CAAAiB,YAAA,EAAM;UAChBjB,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAkB,MAAA,mCAAO;UACZlB,EADY,CAAAiB,YAAA,EAAM,EACZ;UAuCNjB,EApCA,CAAAkC,UAAA,KAAAwH,8CAAA,iBAAoD,KAAAC,8CAAA,kBAoCA;UA8BtD3J,EAAA,CAAAiB,YAAA,EAAM;UAIFjB,EAFJ,CAAAC,cAAA,cAAmC,eACN,YACnB;UAAAD,EAAA,CAAAkB,MAAA,IAAuB;UAC/BlB,EAD+B,CAAAiB,YAAA,EAAO,EAChC;UAEJjB,EADF,CAAAC,cAAA,eAA0B,kBAC4B;UAAlBD,EAAA,CAAAY,UAAA,mBAAAgJ,iEAAA;YAAA,OAASP,GAAA,CAAAtB,KAAA,EAAO;UAAA,EAAC;UAAC/H,EAAA,CAAAkB,MAAA,oBAAE;UAAAlB,EAAA,CAAAiB,YAAA,EAAS;UAI/DjB,EAHA,CAAAkC,UAAA,KAAA2H,iDAAA,qBAAmF,KAAAC,iDAAA,qBAE5D,KAAAC,iDAAA,qBAEK;UAIpC/J,EAHM,CAAAiB,YAAA,EAAM,EACF,EACF,EACF;;;UAzG4BjB,EAAA,CAAAoC,WAAA,SAAAiH,GAAA,CAAAjF,SAAA,CAAwB;UAU3BpE,EAAA,CAAAsB,SAAA,GAIrB;UAJqBtB,EAAA,CAAA2C,UAAA,YAAA3C,EAAA,CAAAgK,eAAA,KAAAC,GAAA,EAAAZ,GAAA,CAAA7E,WAAA,QAAA6E,GAAA,CAAA7E,WAAA,MAAA6E,GAAA,CAAA7E,WAAA,MAIrB;UACqBxE,EAAA,CAAAsB,SAAA,GAIrB;UAJqBtB,EAAA,CAAA2C,UAAA,YAAA3C,EAAA,CAAAgK,eAAA,KAAAC,GAAA,EAAAZ,GAAA,CAAA7E,WAAA,QAAA6E,GAAA,CAAA7E,WAAA,MAAA6E,GAAA,CAAA7E,WAAA,MAIrB;UAIExE,EAAA,CAAAsB,SAAA,GAAuB;UAAvBtB,EAAA,CAAA2C,UAAA,SAAA0G,GAAA,CAAA7E,WAAA,OAAuB;UAoCvBxE,EAAA,CAAAsB,SAAA,EAAuB;UAAvBtB,EAAA,CAAA2C,UAAA,SAAA0G,GAAA,CAAA7E,WAAA,OAAuB;UAkCrBxE,EAAA,CAAAsB,SAAA,GAAuB;UAAvBtB,EAAA,CAAAwB,iBAAA,CAAA6H,GAAA,CAAA7B,eAAA,GAAuB;UAIpBxH,EAAA,CAAAsB,SAAA,GAAqB;UAArBtB,EAAA,CAAA2C,UAAA,SAAA0G,GAAA,CAAA7E,WAAA,KAAqB;UACrBxE,EAAA,CAAAsB,SAAA,EAAqB;UAArBtB,EAAA,CAAA2C,UAAA,SAAA0G,GAAA,CAAA7E,WAAA,KAAqB;UAErBxE,EAAA,CAAAsB,SAAA,EAAuB;UAAvBtB,EAAA,CAAA2C,UAAA,SAAA0G,GAAA,CAAA7E,WAAA,OAAuB;;;qBD3DhC1E,YAAY,EAAAoK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,WAAA,EACZvK,WAAW,EAAAwK,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,4BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}