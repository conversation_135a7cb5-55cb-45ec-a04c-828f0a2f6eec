{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInSecond } from \"../constants/index.js\";\n/**\n * @name millisecondsToSeconds\n * @category Conversion Helpers\n * @summary Convert milliseconds to seconds.\n *\n * @description\n * Convert a number of milliseconds to a full number of seconds.\n *\n * @param {number} milliseconds - number of milliseconds to be converted\n *\n * @returns {number} the number of milliseconds converted in seconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 1000 miliseconds to seconds:\n * const result = millisecondsToSeconds(1000)\n * //=> 1\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToSeconds(1999)\n * //=> 1\n */\nexport default function millisecondsToSeconds(milliseconds) {\n  requiredArgs(1, arguments);\n  var seconds = milliseconds / millisecondsInSecond;\n  return Math.floor(seconds);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}