/**
 * 全域表格樣式 - 統一表格設計系統
 * 使用主題配色參數，確保一致性
 */

// ===== 全域表格基礎樣式覆蓋 =====
.table {
  background-color: var(--color-basic-100);
  border-radius: 8px;
  overflow: hidden;

  // 表格標題行樣式
  thead {
    th {
      background-color: var(--color-success-600) !important;
      color: var(--color-basic-100) !important;
      border-bottom: none !important;
      font-weight: 600;
      padding: 12px 16px;
      font-size: 14px;
      text-align: left;
      white-space: nowrap;

      &:first-child {
        border-top-left-radius: 8px;
      }

      &:last-child {
        border-top-right-radius: 8px;
      }
    }
  }

  // 表格內容行樣式
  tbody {
    tr {
      transition: background-color 0.2s ease;
      border-bottom: 1px solid var(--color-basic-300);

      &:hover {
        background-color: var(--color-success-100) !important;
      }

      &:last-child {
        border-bottom: none;
      }

      td {
        padding: 12px 16px;
        font-size: 13px;
        vertical-align: middle;
        border-bottom: none;
        background-color: var(--color-basic-100);

        // 狀態標籤樣式
        nb-badge,
        .badge {
          font-size: 11px;
          font-weight: 500;
        }
      }
    }
  }

  // 小型表格樣式
  &.table-sm {
    thead th {
      padding: 8px 12px;
      font-size: 12px;
    }

    tbody td {
      padding: 8px 12px;
      font-size: 12px;
    }
  }
}

// ===== 表格操作按鈕樣式 =====
.table-actions {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;

  .btn {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    min-width: auto;

    i {
      margin-right: 4px;
      font-size: 11px;
    }

    &.btn-outline-success {
      border-color: var(--color-success-600);
      color: var(--color-success-600);

      &:hover {
        background-color: var(--color-success-600);
        border-color: var(--color-success-600);
        color: var(--color-basic-100);
      }
    }

    &.btn-outline-danger {
      border-color: var(--color-danger-600);
      color: var(--color-danger-600);

      &:hover {
        background-color: var(--color-danger-600);
        border-color: var(--color-danger-600);
        color: var(--color-basic-100);
      }
    }

    &.btn-outline-info {
      border-color: var(--color-info-600);
      color: var(--color-info-600);

      &:hover {
        background-color: var(--color-info-600);
        border-color: var(--color-info-600);
        color: var(--color-basic-100);
      }
    }

    &.btn-outline-warning {
      border-color: var(--color-warning-600);
      color: var(--color-warning-600);

      &:hover {
        background-color: var(--color-warning-600);
        border-color: var(--color-warning-600);
        color: var(--color-basic-100);
      }
    }
  }
}

// ===== 響應式表格 =====
.table-responsive {
  border-radius: 8px;

  .table {
    margin-bottom: 0;

    @media (max-width: 768px) {
      font-size: 12px;

      thead th {
        padding: 8px 12px;
        font-size: 13px;
      }

      tbody td {
        padding: 8px 12px;
        font-size: 12px;
      }

      .table-actions .btn {
        font-size: 11px;
        padding: 3px 6px;

        i {
          margin-right: 2px;
        }
      }
    }
  }
}

// ===== 表格載入狀態 =====
.table-loading {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
}

// ===== 空資料狀態 =====
.table-empty {
  text-align: center;
  padding: 40px 20px;
  color: var(--color-basic-600);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-message {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .empty-description {
    font-size: 14px;
    opacity: 0.7;
  }
}

// ===== 表格工具欄 =====
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;

    .toolbar-left,
    .toolbar-right {
      justify-content: center;
    }
  }
}

// ===== 搜尋區域統一樣式 =====
.search-area {
  background-color: var(--color-basic-100);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid var(--color-basic-300);

  .search-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-basic-900);
    margin-bottom: 16px;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: var(--color-success-600);
    }
  }

  .search-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: end;

    .form-group {
      min-width: 200px;

      label {
        font-weight: 500;
        color: var(--color-basic-800);
        margin-bottom: 4px;
        display: block;
      }

      nb-form-field,
      nb-select {
        width: 100%;
      }
    }

    .search-actions {
      display: flex;
      gap: 8px;

      .btn {
        min-width: 80px;
      }
    }
  }
}

// ===== 頁面標題統一樣式 =====
.page-header {
  margin-bottom: 24px;

  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--color-basic-900);
    margin-bottom: 8px;
  }

  .page-description {
    font-size: 14px;
    color: var(--color-basic-700);
    line-height: 1.5;
  }
}