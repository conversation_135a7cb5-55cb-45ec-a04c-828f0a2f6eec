{"ast": null, "code": "import { Component as n, createElement as t, options as e, toChildArray as r, Fragment as u, render as o, hydrate as i, createContext as l, createRef as c, cloneElement as f } from \"preact\";\nexport { Component, Fragment, createContext, createElement, createRef } from \"preact\";\nimport { useState as a, useId as s, useReducer as h, useEffect as v, useLayoutEffect as d, useRef as p, useImperativeHandle as m, useMemo as y, useCallback as _, useContext as b, useDebugValue as S } from \"preact/hooks\";\nexport * from \"preact/hooks\";\nfunction g(n, t) {\n  for (var e in t) n[e] = t[e];\n  return n;\n}\nfunction C(n, t) {\n  for (var e in n) if (\"__source\" !== e && !(e in t)) return !0;\n  for (var r in t) if (\"__source\" !== r && n[r] !== t[r]) return !0;\n  return !1;\n}\nfunction E(n, t) {\n  return n === t && (0 !== n || 1 / n == 1 / t) || n != n && t != t;\n}\nfunction w(n) {\n  this.props = n;\n}\nfunction R(n, e) {\n  function r(n) {\n    var t = this.props.ref,\n      r = t == n.ref;\n    return !r && t && (t.call ? t(null) : t.current = null), e ? !e(this.props, n) || !r : C(this.props, n);\n  }\n  function u(e) {\n    return this.shouldComponentUpdate = r, t(n, e);\n  }\n  return u.displayName = \"Memo(\" + (n.displayName || n.name) + \")\", u.prototype.isReactComponent = !0, u.__f = !0, u;\n}\n(w.prototype = new n()).isPureReactComponent = !0, w.prototype.shouldComponentUpdate = function (n, t) {\n  return C(this.props, n) || C(this.state, t);\n};\nvar x = e.__b;\ne.__b = function (n) {\n  n.type && n.type.__f && n.ref && (n.props.ref = n.ref, n.ref = null), x && x(n);\n};\nvar N = \"undefined\" != typeof Symbol && Symbol.for && Symbol.for(\"react.forward_ref\") || 3911;\nfunction k(n) {\n  function t(t) {\n    var e = g({}, t);\n    return delete e.ref, n(e, t.ref || null);\n  }\n  return t.$$typeof = N, t.render = t, t.prototype.isReactComponent = t.__f = !0, t.displayName = \"ForwardRef(\" + (n.displayName || n.name) + \")\", t;\n}\nvar A = function (n, t) {\n    return null == n ? null : r(r(n).map(t));\n  },\n  O = {\n    map: A,\n    forEach: A,\n    count: function (n) {\n      return n ? r(n).length : 0;\n    },\n    only: function (n) {\n      var t = r(n);\n      if (1 !== t.length) throw \"Children.only\";\n      return t[0];\n    },\n    toArray: r\n  },\n  T = e.__e;\ne.__e = function (n, t, e, r) {\n  if (n.then) for (var u, o = t; o = o.__;) if ((u = o.__c) && u.__c) return null == t.__e && (t.__e = e.__e, t.__k = e.__k), u.__c(n, t);\n  T(n, t, e, r);\n};\nvar I = e.unmount;\nfunction L(n, t, e) {\n  return n && (n.__c && n.__c.__H && (n.__c.__H.__.forEach(function (n) {\n    \"function\" == typeof n.__c && n.__c();\n  }), n.__c.__H = null), null != (n = g({}, n)).__c && (n.__c.__P === e && (n.__c.__P = t), n.__c = null), n.__k = n.__k && n.__k.map(function (n) {\n    return L(n, t, e);\n  })), n;\n}\nfunction U(n, t, e) {\n  return n && (n.__v = null, n.__k = n.__k && n.__k.map(function (n) {\n    return U(n, t, e);\n  }), n.__c && n.__c.__P === t && (n.__e && e.insertBefore(n.__e, n.__d), n.__c.__e = !0, n.__c.__P = e)), n;\n}\nfunction D() {\n  this.__u = 0, this.t = null, this.__b = null;\n}\nfunction F(n) {\n  var t = n.__.__c;\n  return t && t.__a && t.__a(n);\n}\nfunction M(n) {\n  var e, r, u;\n  function o(o) {\n    if (e || (e = n()).then(function (n) {\n      r = n.default || n;\n    }, function (n) {\n      u = n;\n    }), u) throw u;\n    if (!r) throw e;\n    return t(r, o);\n  }\n  return o.displayName = \"Lazy\", o.__f = !0, o;\n}\nfunction V() {\n  this.u = null, this.o = null;\n}\ne.unmount = function (n) {\n  var t = n.__c;\n  t && t.__R && t.__R(), t && !0 === n.__h && (n.type = null), I && I(n);\n}, (D.prototype = new n()).__c = function (n, t) {\n  var e = t.__c,\n    r = this;\n  null == r.t && (r.t = []), r.t.push(e);\n  var u = F(r.__v),\n    o = !1,\n    i = function () {\n      o || (o = !0, e.__R = null, u ? u(l) : l());\n    };\n  e.__R = i;\n  var l = function () {\n      if (! --r.__u) {\n        if (r.state.__a) {\n          var n = r.state.__a;\n          r.__v.__k[0] = U(n, n.__c.__P, n.__c.__O);\n        }\n        var t;\n        for (r.setState({\n          __a: r.__b = null\n        }); t = r.t.pop();) t.forceUpdate();\n      }\n    },\n    c = !0 === t.__h;\n  r.__u++ || c || r.setState({\n    __a: r.__b = r.__v.__k[0]\n  }), n.then(i, i);\n}, D.prototype.componentWillUnmount = function () {\n  this.t = [];\n}, D.prototype.render = function (n, e) {\n  if (this.__b) {\n    if (this.__v.__k) {\n      var r = document.createElement(\"div\"),\n        o = this.__v.__k[0].__c;\n      this.__v.__k[0] = L(this.__b, r, o.__O = o.__P);\n    }\n    this.__b = null;\n  }\n  var i = e.__a && t(u, null, n.fallback);\n  return i && (i.__h = null), [t(u, null, e.__a ? null : n.children), i];\n};\nvar W = function (n, t, e) {\n  if (++e[1] === e[0] && n.o.delete(t), n.props.revealOrder && (\"t\" !== n.props.revealOrder[0] || !n.o.size)) for (e = n.u; e;) {\n    for (; e.length > 3;) e.pop()();\n    if (e[1] < e[0]) break;\n    n.u = e = e[2];\n  }\n};\nfunction P(n) {\n  return this.getChildContext = function () {\n    return n.context;\n  }, n.children;\n}\nfunction $(n) {\n  var e = this,\n    r = n.i;\n  e.componentWillUnmount = function () {\n    o(null, e.l), e.l = null, e.i = null;\n  }, e.i && e.i !== r && e.componentWillUnmount(), n.__v ? (e.l || (e.i = r, e.l = {\n    nodeType: 1,\n    parentNode: r,\n    childNodes: [],\n    appendChild: function (n) {\n      this.childNodes.push(n), e.i.appendChild(n);\n    },\n    insertBefore: function (n, t) {\n      this.childNodes.push(n), e.i.appendChild(n);\n    },\n    removeChild: function (n) {\n      this.childNodes.splice(this.childNodes.indexOf(n) >>> 1, 1), e.i.removeChild(n);\n    }\n  }), o(t(P, {\n    context: e.context\n  }, n.__v), e.l)) : e.l && e.componentWillUnmount();\n}\nfunction j(n, e) {\n  var r = t($, {\n    __v: n,\n    i: e\n  });\n  return r.containerInfo = e, r;\n}\n(V.prototype = new n()).__a = function (n) {\n  var t = this,\n    e = F(t.__v),\n    r = t.o.get(n);\n  return r[0]++, function (u) {\n    var o = function () {\n      t.props.revealOrder ? (r.push(u), W(t, n, r)) : u();\n    };\n    e ? e(o) : o();\n  };\n}, V.prototype.render = function (n) {\n  this.u = null, this.o = new Map();\n  var t = r(n.children);\n  n.revealOrder && \"b\" === n.revealOrder[0] && t.reverse();\n  for (var e = t.length; e--;) this.o.set(t[e], this.u = [1, 0, this.u]);\n  return n.children;\n}, V.prototype.componentDidUpdate = V.prototype.componentDidMount = function () {\n  var n = this;\n  this.o.forEach(function (t, e) {\n    W(n, e, t);\n  });\n};\nvar z = \"undefined\" != typeof Symbol && Symbol.for && Symbol.for(\"react.element\") || 60103,\n  B = /^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,\n  H = \"undefined\" != typeof document,\n  Z = function (n) {\n    return (\"undefined\" != typeof Symbol && \"symbol\" == typeof Symbol() ? /fil|che|rad/i : /fil|che|ra/i).test(n);\n  };\nfunction Y(n, t, e) {\n  return null == t.__k && (t.textContent = \"\"), o(n, t), \"function\" == typeof e && e(), n ? n.__c : null;\n}\nfunction q(n, t, e) {\n  return i(n, t), \"function\" == typeof e && e(), n ? n.__c : null;\n}\nn.prototype.isReactComponent = {}, [\"componentWillMount\", \"componentWillReceiveProps\", \"componentWillUpdate\"].forEach(function (t) {\n  Object.defineProperty(n.prototype, t, {\n    configurable: !0,\n    get: function () {\n      return this[\"UNSAFE_\" + t];\n    },\n    set: function (n) {\n      Object.defineProperty(this, t, {\n        configurable: !0,\n        writable: !0,\n        value: n\n      });\n    }\n  });\n});\nvar G = e.event;\nfunction J() {}\nfunction K() {\n  return this.cancelBubble;\n}\nfunction Q() {\n  return this.defaultPrevented;\n}\ne.event = function (n) {\n  return G && (n = G(n)), n.persist = J, n.isPropagationStopped = K, n.isDefaultPrevented = Q, n.nativeEvent = n;\n};\nvar X,\n  nn = {\n    configurable: !0,\n    get: function () {\n      return this.class;\n    }\n  },\n  tn = e.vnode;\ne.vnode = function (n) {\n  var t = n.type,\n    e = n.props,\n    u = e;\n  if (\"string\" == typeof t) {\n    var o = -1 === t.indexOf(\"-\");\n    for (var i in u = {}, e) {\n      var l = e[i];\n      H && \"children\" === i && \"noscript\" === t || \"value\" === i && \"defaultValue\" in e && null == l || (\"defaultValue\" === i && \"value\" in e && null == e.value ? i = \"value\" : \"download\" === i && !0 === l ? l = \"\" : /ondoubleclick/i.test(i) ? i = \"ondblclick\" : /^onchange(textarea|input)/i.test(i + t) && !Z(e.type) ? i = \"oninput\" : /^onfocus$/i.test(i) ? i = \"onfocusin\" : /^onblur$/i.test(i) ? i = \"onfocusout\" : /^on(Ani|Tra|Tou|BeforeInp|Compo)/.test(i) ? i = i.toLowerCase() : o && B.test(i) ? i = i.replace(/[A-Z0-9]/g, \"-$&\").toLowerCase() : null === l && (l = void 0), /^oninput$/i.test(i) && (i = i.toLowerCase(), u[i] && (i = \"oninputCapture\")), u[i] = l);\n    }\n    \"select\" == t && u.multiple && Array.isArray(u.value) && (u.value = r(e.children).forEach(function (n) {\n      n.props.selected = -1 != u.value.indexOf(n.props.value);\n    })), \"select\" == t && null != u.defaultValue && (u.value = r(e.children).forEach(function (n) {\n      n.props.selected = u.multiple ? -1 != u.defaultValue.indexOf(n.props.value) : u.defaultValue == n.props.value;\n    })), n.props = u, e.class != e.className && (nn.enumerable = \"className\" in e, null != e.className && (u.class = e.className), Object.defineProperty(u, \"className\", nn));\n  }\n  n.$$typeof = z, tn && tn(n);\n};\nvar en = e.__r;\ne.__r = function (n) {\n  en && en(n), X = n.__c;\n};\nvar rn = {\n    ReactCurrentDispatcher: {\n      current: {\n        readContext: function (n) {\n          return X.__n[n.__c].props.value;\n        }\n      }\n    }\n  },\n  un = \"17.0.2\";\nfunction on(n) {\n  return t.bind(null, n);\n}\nfunction ln(n) {\n  return !!n && n.$$typeof === z;\n}\nfunction cn(n) {\n  return ln(n) ? f.apply(null, arguments) : n;\n}\nfunction fn(n) {\n  return !!n.__k && (o(null, n), !0);\n}\nfunction an(n) {\n  return n && (n.base || 1 === n.nodeType && n) || null;\n}\nvar sn = function (n, t) {\n    return n(t);\n  },\n  hn = function (n, t) {\n    return n(t);\n  },\n  vn = u;\nfunction dn(n) {\n  n();\n}\nfunction pn(n) {\n  return n;\n}\nfunction mn() {\n  return [!1, dn];\n}\nvar yn = d;\nfunction _n(n, t) {\n  var e = t(),\n    r = a({\n      h: {\n        __: e,\n        v: t\n      }\n    }),\n    u = r[0].h,\n    o = r[1];\n  return d(function () {\n    u.__ = e, u.v = t, E(u.__, t()) || o({\n      h: u\n    });\n  }, [n, e, t]), v(function () {\n    return E(u.__, u.v()) || o({\n      h: u\n    }), n(function () {\n      E(u.__, u.v()) || o({\n        h: u\n      });\n    });\n  }, [n]), e;\n}\nvar bn = {\n  useState: a,\n  useId: s,\n  useReducer: h,\n  useEffect: v,\n  useLayoutEffect: d,\n  useInsertionEffect: yn,\n  useTransition: mn,\n  useDeferredValue: pn,\n  useSyncExternalStore: _n,\n  startTransition: dn,\n  useRef: p,\n  useImperativeHandle: m,\n  useMemo: y,\n  useCallback: _,\n  useContext: b,\n  useDebugValue: S,\n  version: \"17.0.2\",\n  Children: O,\n  render: Y,\n  hydrate: q,\n  unmountComponentAtNode: fn,\n  createPortal: j,\n  createElement: t,\n  createContext: l,\n  createFactory: on,\n  cloneElement: cn,\n  createRef: c,\n  Fragment: u,\n  isValidElement: ln,\n  findDOMNode: an,\n  Component: n,\n  PureComponent: w,\n  memo: R,\n  forwardRef: k,\n  flushSync: hn,\n  unstable_batchedUpdates: sn,\n  StrictMode: vn,\n  Suspense: D,\n  SuspenseList: V,\n  lazy: M,\n  __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: rn\n};\nexport { O as Children, w as PureComponent, vn as StrictMode, D as Suspense, V as SuspenseList, rn as __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED, cn as cloneElement, on as createFactory, j as createPortal, bn as default, an as findDOMNode, hn as flushSync, k as forwardRef, q as hydrate, ln as isValidElement, M as lazy, R as memo, Y as render, dn as startTransition, fn as unmountComponentAtNode, sn as unstable_batchedUpdates, pn as useDeferredValue, yn as useInsertionEffect, _n as useSyncExternalStore, mn as useTransition, un as version };\n//# sourceMappingURL=compat.module.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}