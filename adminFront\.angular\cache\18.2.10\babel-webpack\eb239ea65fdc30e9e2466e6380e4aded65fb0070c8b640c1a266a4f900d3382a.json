{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiQuotationGetDataPost$Json } from '../fn/quotation/api-quotation-get-data-post-json';\nimport { apiQuotationGetDataPost$Plain } from '../fn/quotation/api-quotation-get-data-post-plain';\nimport { apiQuotationGetListByHouseIdPost$Json } from '../fn/quotation/api-quotation-get-list-by-house-id-post-json';\nimport { apiQuotationGetListByHouseIdPost$Plain } from '../fn/quotation/api-quotation-get-list-by-house-id-post-plain';\nimport { apiQuotationGetListPost$Json } from '../fn/quotation/api-quotation-get-list-post-json';\nimport { apiQuotationGetListPost$Plain } from '../fn/quotation/api-quotation-get-list-post-plain';\nimport { apiQuotationGetQuotationHistoryPost$Json } from '../fn/quotation/api-quotation-get-quotation-history-post-json';\nimport { apiQuotationGetQuotationHistoryPost$Plain } from '../fn/quotation/api-quotation-get-quotation-history-post-plain';\nimport { apiQuotationLoadDefaultItemsPost$Json } from '../fn/quotation/api-quotation-load-default-items-post-json';\nimport { apiQuotationLoadDefaultItemsPost$Plain } from '../fn/quotation/api-quotation-load-default-items-post-plain';\nimport { apiQuotationLoadRegularItemsPost$Json } from '../fn/quotation/api-quotation-load-regular-items-post-json';\nimport { apiQuotationLoadRegularItemsPost$Plain } from '../fn/quotation/api-quotation-load-regular-items-post-plain';\nimport { apiQuotationLockQuotationPost$Json } from '../fn/quotation/api-quotation-lock-quotation-post-json';\nimport { apiQuotationLockQuotationPost$Plain } from '../fn/quotation/api-quotation-lock-quotation-post-plain';\nimport { apiQuotationSaveDataPost$Json } from '../fn/quotation/api-quotation-save-data-post-json';\nimport { apiQuotationSaveDataPost$Plain } from '../fn/quotation/api-quotation-save-data-post-plain';\nimport { apiQuotationSignQuotationPost$Json } from '../fn/quotation/api-quotation-sign-quotation-post-json';\nimport { apiQuotationSignQuotationPost$Plain } from '../fn/quotation/api-quotation-sign-quotation-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class QuotationService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiQuotationGetListPost()` */\n  static {\n    this.ApiQuotationGetListPostPath = '/api/Quotation/GetList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationGetListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListPost$Plain$Response(params, context) {\n    return apiQuotationGetListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationGetListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListPost$Plain(params, context) {\n    return this.apiQuotationGetListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationGetListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListPost$Json$Response(params, context) {\n    return apiQuotationGetListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationGetListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListPost$Json(params, context) {\n    return this.apiQuotationGetListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationGetDataPost()` */\n  static {\n    this.ApiQuotationGetDataPostPath = '/api/Quotation/GetData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationGetDataPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetDataPost$Plain$Response(params, context) {\n    return apiQuotationGetDataPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationGetDataPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetDataPost$Plain(params, context) {\n    return this.apiQuotationGetDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationGetDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetDataPost$Json$Response(params, context) {\n    return apiQuotationGetDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationGetDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetDataPost$Json(params, context) {\n    return this.apiQuotationGetDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationSaveDataPost()` */\n  static {\n    this.ApiQuotationSaveDataPostPath = '/api/Quotation/SaveData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationSaveDataPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationSaveDataPost$Plain$Response(params, context) {\n    return apiQuotationSaveDataPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationSaveDataPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationSaveDataPost$Plain(params, context) {\n    return this.apiQuotationSaveDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationSaveDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationSaveDataPost$Json$Response(params, context) {\n    return apiQuotationSaveDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationSaveDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationSaveDataPost$Json(params, context) {\n    return this.apiQuotationSaveDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationGetListByHouseIdPost()` */\n  static {\n    this.ApiQuotationGetListByHouseIdPostPath = '/api/Quotation/GetListByHouseID';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationGetListByHouseIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListByHouseIdPost$Plain$Response(params, context) {\n    return apiQuotationGetListByHouseIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationGetListByHouseIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListByHouseIdPost$Plain(params, context) {\n    return this.apiQuotationGetListByHouseIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationGetListByHouseIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListByHouseIdPost$Json$Response(params, context) {\n    return apiQuotationGetListByHouseIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationGetListByHouseIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListByHouseIdPost$Json(params, context) {\n    return this.apiQuotationGetListByHouseIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationLoadDefaultItemsPost()` */\n  static {\n    this.ApiQuotationLoadDefaultItemsPostPath = '/api/Quotation/LoadDefaultItems';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationLoadDefaultItemsPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLoadDefaultItemsPost$Plain$Response(params, context) {\n    return apiQuotationLoadDefaultItemsPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationLoadDefaultItemsPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLoadDefaultItemsPost$Plain(params, context) {\n    return this.apiQuotationLoadDefaultItemsPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationLoadDefaultItemsPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLoadDefaultItemsPost$Json$Response(params, context) {\n    return apiQuotationLoadDefaultItemsPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationLoadDefaultItemsPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLoadDefaultItemsPost$Json(params, context) {\n    return this.apiQuotationLoadDefaultItemsPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationLoadRegularItemsPost()` */\n  static {\n    this.ApiQuotationLoadRegularItemsPostPath = '/api/Quotation/LoadRegularItems';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationLoadRegularItemsPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLoadRegularItemsPost$Plain$Response(params, context) {\n    return apiQuotationLoadRegularItemsPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationLoadRegularItemsPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLoadRegularItemsPost$Plain(params, context) {\n    return this.apiQuotationLoadRegularItemsPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationLoadRegularItemsPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLoadRegularItemsPost$Json$Response(params, context) {\n    return apiQuotationLoadRegularItemsPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationLoadRegularItemsPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLoadRegularItemsPost$Json(params, context) {\n    return this.apiQuotationLoadRegularItemsPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationLockQuotationPost()` */\n  static {\n    this.ApiQuotationLockQuotationPostPath = '/api/Quotation/LockQuotation';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationLockQuotationPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLockQuotationPost$Plain$Response(params, context) {\n    return apiQuotationLockQuotationPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationLockQuotationPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLockQuotationPost$Plain(params, context) {\n    return this.apiQuotationLockQuotationPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationLockQuotationPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLockQuotationPost$Json$Response(params, context) {\n    return apiQuotationLockQuotationPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationLockQuotationPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationLockQuotationPost$Json(params, context) {\n    return this.apiQuotationLockQuotationPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationGetQuotationHistoryPost()` */\n  static {\n    this.ApiQuotationGetQuotationHistoryPostPath = '/api/Quotation/GetQuotationHistory';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationGetQuotationHistoryPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiQuotationGetQuotationHistoryPost$Plain$Response(params, context) {\n    return apiQuotationGetQuotationHistoryPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationGetQuotationHistoryPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiQuotationGetQuotationHistoryPost$Plain(params, context) {\n    return this.apiQuotationGetQuotationHistoryPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationGetQuotationHistoryPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiQuotationGetQuotationHistoryPost$Json$Response(params, context) {\n    return apiQuotationGetQuotationHistoryPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationGetQuotationHistoryPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiQuotationGetQuotationHistoryPost$Json(params, context) {\n    return this.apiQuotationGetQuotationHistoryPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationSignQuotationPost()` */\n  static {\n    this.ApiQuotationSignQuotationPostPath = '/api/Quotation/SignQuotation';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationSignQuotationPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationSignQuotationPost$Plain$Response(params, context) {\n    return apiQuotationSignQuotationPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationSignQuotationPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationSignQuotationPost$Plain(params, context) {\n    return this.apiQuotationSignQuotationPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationSignQuotationPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationSignQuotationPost$Json$Response(params, context) {\n    return apiQuotationSignQuotationPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationSignQuotationPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationSignQuotationPost$Json(params, context) {\n    return this.apiQuotationSignQuotationPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuotationService,\n      factory: QuotationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiQuotationGetDataPost$Json", "apiQuotationGetDataPost$Plain", "apiQuotationGetListByHouseIdPost$Json", "apiQuotationGetListByHouseIdPost$Plain", "apiQuotationGetListPost$Json", "apiQuotationGetListPost$Plain", "apiQuotationGetQuotationHistoryPost$Json", "apiQuotationGetQuotationHistoryPost$Plain", "apiQuotationLoadDefaultItemsPost$Json", "apiQuotationLoadDefaultItemsPost$Plain", "apiQuotationLoadRegularItemsPost$Json", "apiQuotationLoadRegularItemsPost$Plain", "apiQuotationLockQuotationPost$Json", "apiQuotationLockQuotationPost$Plain", "apiQuotationSaveDataPost$Json", "apiQuotationSaveDataPost$Plain", "apiQuotationSignQuotationPost$Json", "apiQuotationSignQuotationPost$Plain", "QuotationService", "constructor", "config", "http", "ApiQuotationGetListPostPath", "apiQuotationGetListPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiQuotationGetListPost$Json$Response", "ApiQuotationGetDataPostPath", "apiQuotationGetDataPost$Plain$Response", "apiQuotationGetDataPost$Json$Response", "ApiQuotationSaveDataPostPath", "apiQuotationSaveDataPost$Plain$Response", "apiQuotationSaveDataPost$Json$Response", "ApiQuotationGetListByHouseIdPostPath", "apiQuotationGetListByHouseIdPost$Plain$Response", "apiQuotationGetListByHouseIdPost$Json$Response", "ApiQuotationLoadDefaultItemsPostPath", "apiQuotationLoadDefaultItemsPost$Plain$Response", "apiQuotationLoadDefaultItemsPost$Json$Response", "ApiQuotationLoadRegularItemsPostPath", "apiQuotationLoadRegularItemsPost$Plain$Response", "apiQuotationLoadRegularItemsPost$Json$Response", "ApiQuotationLockQuotationPostPath", "apiQuotationLockQuotationPost$Plain$Response", "apiQuotationLockQuotationPost$Json$Response", "ApiQuotationGetQuotationHistoryPostPath", "apiQuotationGetQuotationHistoryPost$Plain$Response", "apiQuotationGetQuotationHistoryPost$Json$Response", "ApiQuotationSignQuotationPostPath", "apiQuotationSignQuotationPost$Plain$Response", "apiQuotationSignQuotationPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\quotation.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiQuotationGetDataPost$Json } from '../fn/quotation/api-quotation-get-data-post-json';\r\nimport { ApiQuotationGetDataPost$Json$Params } from '../fn/quotation/api-quotation-get-data-post-json';\r\nimport { apiQuotationGetDataPost$Plain } from '../fn/quotation/api-quotation-get-data-post-plain';\r\nimport { ApiQuotationGetDataPost$Plain$Params } from '../fn/quotation/api-quotation-get-data-post-plain';\r\nimport { apiQuotationGetListByHouseIdPost$Json } from '../fn/quotation/api-quotation-get-list-by-house-id-post-json';\r\nimport { ApiQuotationGetListByHouseIdPost$Json$Params } from '../fn/quotation/api-quotation-get-list-by-house-id-post-json';\r\nimport { apiQuotationGetListByHouseIdPost$Plain } from '../fn/quotation/api-quotation-get-list-by-house-id-post-plain';\r\nimport { ApiQuotationGetListByHouseIdPost$Plain$Params } from '../fn/quotation/api-quotation-get-list-by-house-id-post-plain';\r\nimport { apiQuotationGetListPost$Json } from '../fn/quotation/api-quotation-get-list-post-json';\r\nimport { ApiQuotationGetListPost$Json$Params } from '../fn/quotation/api-quotation-get-list-post-json';\r\nimport { apiQuotationGetListPost$Plain } from '../fn/quotation/api-quotation-get-list-post-plain';\r\nimport { ApiQuotationGetListPost$Plain$Params } from '../fn/quotation/api-quotation-get-list-post-plain';\r\nimport { apiQuotationGetQuotationHistoryPost$Json } from '../fn/quotation/api-quotation-get-quotation-history-post-json';\r\nimport { ApiQuotationGetQuotationHistoryPost$Json$Params } from '../fn/quotation/api-quotation-get-quotation-history-post-json';\r\nimport { apiQuotationGetQuotationHistoryPost$Plain } from '../fn/quotation/api-quotation-get-quotation-history-post-plain';\r\nimport { ApiQuotationGetQuotationHistoryPost$Plain$Params } from '../fn/quotation/api-quotation-get-quotation-history-post-plain';\r\nimport { apiQuotationLoadDefaultItemsPost$Json } from '../fn/quotation/api-quotation-load-default-items-post-json';\r\nimport { ApiQuotationLoadDefaultItemsPost$Json$Params } from '../fn/quotation/api-quotation-load-default-items-post-json';\r\nimport { apiQuotationLoadDefaultItemsPost$Plain } from '../fn/quotation/api-quotation-load-default-items-post-plain';\r\nimport { ApiQuotationLoadDefaultItemsPost$Plain$Params } from '../fn/quotation/api-quotation-load-default-items-post-plain';\r\nimport { apiQuotationLoadRegularItemsPost$Json } from '../fn/quotation/api-quotation-load-regular-items-post-json';\r\nimport { ApiQuotationLoadRegularItemsPost$Json$Params } from '../fn/quotation/api-quotation-load-regular-items-post-json';\r\nimport { apiQuotationLoadRegularItemsPost$Plain } from '../fn/quotation/api-quotation-load-regular-items-post-plain';\r\nimport { ApiQuotationLoadRegularItemsPost$Plain$Params } from '../fn/quotation/api-quotation-load-regular-items-post-plain';\r\nimport { apiQuotationLockQuotationPost$Json } from '../fn/quotation/api-quotation-lock-quotation-post-json';\r\nimport { ApiQuotationLockQuotationPost$Json$Params } from '../fn/quotation/api-quotation-lock-quotation-post-json';\r\nimport { apiQuotationLockQuotationPost$Plain } from '../fn/quotation/api-quotation-lock-quotation-post-plain';\r\nimport { ApiQuotationLockQuotationPost$Plain$Params } from '../fn/quotation/api-quotation-lock-quotation-post-plain';\r\nimport { apiQuotationSaveDataPost$Json } from '../fn/quotation/api-quotation-save-data-post-json';\r\nimport { ApiQuotationSaveDataPost$Json$Params } from '../fn/quotation/api-quotation-save-data-post-json';\r\nimport { apiQuotationSaveDataPost$Plain } from '../fn/quotation/api-quotation-save-data-post-plain';\r\nimport { ApiQuotationSaveDataPost$Plain$Params } from '../fn/quotation/api-quotation-save-data-post-plain';\r\nimport { apiQuotationSignQuotationPost$Json } from '../fn/quotation/api-quotation-sign-quotation-post-json';\r\nimport { ApiQuotationSignQuotationPost$Json$Params } from '../fn/quotation/api-quotation-sign-quotation-post-json';\r\nimport { apiQuotationSignQuotationPost$Plain } from '../fn/quotation/api-quotation-sign-quotation-post-plain';\r\nimport { ApiQuotationSignQuotationPost$Plain$Params } from '../fn/quotation/api-quotation-sign-quotation-post-plain';\r\nimport { GetQuotationListResponseBase } from '../models/get-quotation-list-response-base';\r\nimport { GetQuotationResponseBase } from '../models/get-quotation-response-base';\r\nimport { GetQuotationVersionsListResponseBase } from '../models/get-quotation-versions-list-response-base';\r\nimport { SaveDataQuotationResponseBase } from '../models/save-data-quotation-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class QuotationService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationGetListPost()` */\r\n  static readonly ApiQuotationGetListPostPath = '/api/Quotation/GetList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationGetListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListPost$Plain$Response(params?: ApiQuotationGetListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {\r\n    return apiQuotationGetListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationGetListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListPost$Plain(params?: ApiQuotationGetListPost$Plain$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {\r\n    return this.apiQuotationGetListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationGetListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListPost$Json$Response(params?: ApiQuotationGetListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {\r\n    return apiQuotationGetListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationGetListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListPost$Json(params?: ApiQuotationGetListPost$Json$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {\r\n    return this.apiQuotationGetListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationGetDataPost()` */\r\n  static readonly ApiQuotationGetDataPostPath = '/api/Quotation/GetData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationGetDataPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetDataPost$Plain$Response(params?: ApiQuotationGetDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationResponseBase>> {\r\n    return apiQuotationGetDataPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationGetDataPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetDataPost$Plain(params?: ApiQuotationGetDataPost$Plain$Params, context?: HttpContext): Observable<GetQuotationResponseBase> {\r\n    return this.apiQuotationGetDataPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationResponseBase>): GetQuotationResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationGetDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetDataPost$Json$Response(params?: ApiQuotationGetDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationResponseBase>> {\r\n    return apiQuotationGetDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationGetDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetDataPost$Json(params?: ApiQuotationGetDataPost$Json$Params, context?: HttpContext): Observable<GetQuotationResponseBase> {\r\n    return this.apiQuotationGetDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationResponseBase>): GetQuotationResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationSaveDataPost()` */\r\n  static readonly ApiQuotationSaveDataPostPath = '/api/Quotation/SaveData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationSaveDataPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationSaveDataPost$Plain$Response(params?: ApiQuotationSaveDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiQuotationSaveDataPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationSaveDataPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationSaveDataPost$Plain(params?: ApiQuotationSaveDataPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiQuotationSaveDataPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationSaveDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationSaveDataPost$Json$Response(params?: ApiQuotationSaveDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiQuotationSaveDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationSaveDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationSaveDataPost$Json(params?: ApiQuotationSaveDataPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiQuotationSaveDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationGetListByHouseIdPost()` */\r\n  static readonly ApiQuotationGetListByHouseIdPostPath = '/api/Quotation/GetListByHouseID';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationGetListByHouseIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListByHouseIdPost$Plain$Response(params?: ApiQuotationGetListByHouseIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<SaveDataQuotationResponseBase>> {\r\n    return apiQuotationGetListByHouseIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationGetListByHouseIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListByHouseIdPost$Plain(params?: ApiQuotationGetListByHouseIdPost$Plain$Params, context?: HttpContext): Observable<SaveDataQuotationResponseBase> {\r\n    return this.apiQuotationGetListByHouseIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SaveDataQuotationResponseBase>): SaveDataQuotationResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationGetListByHouseIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListByHouseIdPost$Json$Response(params?: ApiQuotationGetListByHouseIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<SaveDataQuotationResponseBase>> {\r\n    return apiQuotationGetListByHouseIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationGetListByHouseIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListByHouseIdPost$Json(params?: ApiQuotationGetListByHouseIdPost$Json$Params, context?: HttpContext): Observable<SaveDataQuotationResponseBase> {\r\n    return this.apiQuotationGetListByHouseIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SaveDataQuotationResponseBase>): SaveDataQuotationResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationLoadDefaultItemsPost()` */\r\n  static readonly ApiQuotationLoadDefaultItemsPostPath = '/api/Quotation/LoadDefaultItems';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationLoadDefaultItemsPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLoadDefaultItemsPost$Plain$Response(params?: ApiQuotationLoadDefaultItemsPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {\r\n    return apiQuotationLoadDefaultItemsPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationLoadDefaultItemsPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLoadDefaultItemsPost$Plain(params?: ApiQuotationLoadDefaultItemsPost$Plain$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {\r\n    return this.apiQuotationLoadDefaultItemsPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationLoadDefaultItemsPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLoadDefaultItemsPost$Json$Response(params?: ApiQuotationLoadDefaultItemsPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {\r\n    return apiQuotationLoadDefaultItemsPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationLoadDefaultItemsPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLoadDefaultItemsPost$Json(params?: ApiQuotationLoadDefaultItemsPost$Json$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {\r\n    return this.apiQuotationLoadDefaultItemsPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationLoadRegularItemsPost()` */\r\n  static readonly ApiQuotationLoadRegularItemsPostPath = '/api/Quotation/LoadRegularItems';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationLoadRegularItemsPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLoadRegularItemsPost$Plain$Response(params?: ApiQuotationLoadRegularItemsPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {\r\n    return apiQuotationLoadRegularItemsPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationLoadRegularItemsPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLoadRegularItemsPost$Plain(params?: ApiQuotationLoadRegularItemsPost$Plain$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {\r\n    return this.apiQuotationLoadRegularItemsPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationLoadRegularItemsPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLoadRegularItemsPost$Json$Response(params?: ApiQuotationLoadRegularItemsPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {\r\n    return apiQuotationLoadRegularItemsPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationLoadRegularItemsPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLoadRegularItemsPost$Json(params?: ApiQuotationLoadRegularItemsPost$Json$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {\r\n    return this.apiQuotationLoadRegularItemsPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationLockQuotationPost()` */\r\n  static readonly ApiQuotationLockQuotationPostPath = '/api/Quotation/LockQuotation';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationLockQuotationPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLockQuotationPost$Plain$Response(params?: ApiQuotationLockQuotationPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiQuotationLockQuotationPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationLockQuotationPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLockQuotationPost$Plain(params?: ApiQuotationLockQuotationPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiQuotationLockQuotationPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationLockQuotationPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLockQuotationPost$Json$Response(params?: ApiQuotationLockQuotationPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiQuotationLockQuotationPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationLockQuotationPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationLockQuotationPost$Json(params?: ApiQuotationLockQuotationPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiQuotationLockQuotationPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationGetQuotationHistoryPost()` */\r\n  static readonly ApiQuotationGetQuotationHistoryPostPath = '/api/Quotation/GetQuotationHistory';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationGetQuotationHistoryPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiQuotationGetQuotationHistoryPost$Plain$Response(params?: ApiQuotationGetQuotationHistoryPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationVersionsListResponseBase>> {\r\n    return apiQuotationGetQuotationHistoryPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationGetQuotationHistoryPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiQuotationGetQuotationHistoryPost$Plain(params?: ApiQuotationGetQuotationHistoryPost$Plain$Params, context?: HttpContext): Observable<GetQuotationVersionsListResponseBase> {\r\n    return this.apiQuotationGetQuotationHistoryPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationVersionsListResponseBase>): GetQuotationVersionsListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationGetQuotationHistoryPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiQuotationGetQuotationHistoryPost$Json$Response(params?: ApiQuotationGetQuotationHistoryPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationVersionsListResponseBase>> {\r\n    return apiQuotationGetQuotationHistoryPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationGetQuotationHistoryPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiQuotationGetQuotationHistoryPost$Json(params?: ApiQuotationGetQuotationHistoryPost$Json$Params, context?: HttpContext): Observable<GetQuotationVersionsListResponseBase> {\r\n    return this.apiQuotationGetQuotationHistoryPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationVersionsListResponseBase>): GetQuotationVersionsListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationSignQuotationPost()` */\r\n  static readonly ApiQuotationSignQuotationPostPath = '/api/Quotation/SignQuotation';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationSignQuotationPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationSignQuotationPost$Plain$Response(params?: ApiQuotationSignQuotationPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiQuotationSignQuotationPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationSignQuotationPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationSignQuotationPost$Plain(params?: ApiQuotationSignQuotationPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiQuotationSignQuotationPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationSignQuotationPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationSignQuotationPost$Json$Response(params?: ApiQuotationSignQuotationPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiQuotationSignQuotationPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationSignQuotationPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationSignQuotationPost$Json(params?: ApiQuotationSignQuotationPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiQuotationSignQuotationPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAOA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,4BAA4B,QAAQ,kDAAkD;AAE/F,SAASC,6BAA6B,QAAQ,mDAAmD;AAEjG,SAASC,qCAAqC,QAAQ,8DAA8D;AAEpH,SAASC,sCAAsC,QAAQ,+DAA+D;AAEtH,SAASC,4BAA4B,QAAQ,kDAAkD;AAE/F,SAASC,6BAA6B,QAAQ,mDAAmD;AAEjG,SAASC,wCAAwC,QAAQ,+DAA+D;AAExH,SAASC,yCAAyC,QAAQ,gEAAgE;AAE1H,SAASC,qCAAqC,QAAQ,4DAA4D;AAElH,SAASC,sCAAsC,QAAQ,6DAA6D;AAEpH,SAASC,qCAAqC,QAAQ,4DAA4D;AAElH,SAASC,sCAAsC,QAAQ,6DAA6D;AAEpH,SAASC,kCAAkC,QAAQ,wDAAwD;AAE3G,SAASC,mCAAmC,QAAQ,yDAAyD;AAE7G,SAASC,6BAA6B,QAAQ,mDAAmD;AAEjG,SAASC,8BAA8B,QAAQ,oDAAoD;AAEnG,SAASC,kCAAkC,QAAQ,wDAAwD;AAE3G,SAASC,mCAAmC,QAAQ,yDAAyD;;;;AAS7G,OAAM,MAAOC,gBAAiB,SAAQnB,WAAW;EAC/CoB,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,sCAAsCA,CAACC,MAA6C,EAAEC,OAAqB;IACzG,OAAOpB,6BAA6B,CAAC,IAAI,CAACgB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMApB,6BAA6BA,CAACmB,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACF,sCAAsC,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtE7B,GAAG,CAAE8B,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAC,qCAAqCA,CAACN,MAA4C,EAAEC,OAAqB;IACvG,OAAOrB,4BAA4B,CAAC,IAAI,CAACiB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMArB,4BAA4BA,CAACoB,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACK,qCAAqC,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrE7B,GAAG,CAAE8B,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;IACgB,KAAAE,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,sCAAsCA,CAACR,MAA6C,EAAEC,OAAqB;IACzG,OAAOxB,6BAA6B,CAAC,IAAI,CAACoB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAxB,6BAA6BA,CAACuB,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACO,sCAAsC,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtE7B,GAAG,CAAE8B,CAA+C,IAA+BA,CAAC,CAACC,IAAI,CAAC,CAC3F;EACH;EAEA;;;;;;EAMAI,qCAAqCA,CAACT,MAA4C,EAAEC,OAAqB;IACvG,OAAOzB,4BAA4B,CAAC,IAAI,CAACqB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAzB,4BAA4BA,CAACwB,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACQ,qCAAqC,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrE7B,GAAG,CAAE8B,CAA+C,IAA+BA,CAAC,CAACC,IAAI,CAAC,CAC3F;EACH;EAEA;;IACgB,KAAAK,4BAA4B,GAAG,yBAAyB;EAAC;EAEzE;;;;;;EAMAC,uCAAuCA,CAACX,MAA8C,EAAEC,OAAqB;IAC3G,OAAOV,8BAA8B,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMAV,8BAA8BA,CAACS,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAACU,uCAAuC,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvE7B,GAAG,CAAE8B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAO,sCAAsCA,CAACZ,MAA6C,EAAEC,OAAqB;IACzG,OAAOX,6BAA6B,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAX,6BAA6BA,CAACU,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACW,sCAAsC,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtE7B,GAAG,CAAE8B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAQ,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAACd,MAAsD,EAAEC,OAAqB;IAC3H,OAAOtB,sCAAsC,CAAC,IAAI,CAACkB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMAtB,sCAAsCA,CAACqB,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAACa,+CAA+C,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/E7B,GAAG,CAAE8B,CAAoD,IAAoCA,CAAC,CAACC,IAAI,CAAC,CACrG;EACH;EAEA;;;;;;EAMAU,8CAA8CA,CAACf,MAAqD,EAAEC,OAAqB;IACzH,OAAOvB,qCAAqC,CAAC,IAAI,CAACmB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAvB,qCAAqCA,CAACsB,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACc,8CAA8C,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E7B,GAAG,CAAE8B,CAAoD,IAAoCA,CAAC,CAACC,IAAI,CAAC,CACrG;EACH;EAEA;;IACgB,KAAAW,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAACjB,MAAsD,EAAEC,OAAqB;IAC3H,OAAOhB,sCAAsC,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMAhB,sCAAsCA,CAACe,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAACgB,+CAA+C,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/E7B,GAAG,CAAE8B,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAa,8CAA8CA,CAAClB,MAAqD,EAAEC,OAAqB;IACzH,OAAOjB,qCAAqC,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAjB,qCAAqCA,CAACgB,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACiB,8CAA8C,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E7B,GAAG,CAAE8B,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;IACgB,KAAAc,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAACpB,MAAsD,EAAEC,OAAqB;IAC3H,OAAOd,sCAAsC,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMAd,sCAAsCA,CAACa,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAACmB,+CAA+C,CAACpB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/E7B,GAAG,CAAE8B,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAgB,8CAA8CA,CAACrB,MAAqD,EAAEC,OAAqB;IACzH,OAAOf,qCAAqC,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAf,qCAAqCA,CAACc,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACoB,8CAA8C,CAACrB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E7B,GAAG,CAAE8B,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;IACgB,KAAAiB,iCAAiC,GAAG,8BAA8B;EAAC;EAEnF;;;;;;EAMAC,4CAA4CA,CAACvB,MAAmD,EAAEC,OAAqB;IACrH,OAAOZ,mCAAmC,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAZ,mCAAmCA,CAACW,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACsB,4CAA4C,CAACvB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E7B,GAAG,CAAE8B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAmB,2CAA2CA,CAACxB,MAAkD,EAAEC,OAAqB;IACnH,OAAOb,kCAAkC,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAb,kCAAkCA,CAACY,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAACuB,2CAA2C,CAACxB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3E7B,GAAG,CAAE8B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAoB,uCAAuC,GAAG,oCAAoC;EAAC;EAE/F;;;;;;EAMAC,kDAAkDA,CAAC1B,MAAyD,EAAEC,OAAqB;IACjI,OAAOlB,yCAAyC,CAAC,IAAI,CAACc,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC5F;EAEA;;;;;;EAMAlB,yCAAyCA,CAACiB,MAAyD,EAAEC,OAAqB;IACxH,OAAO,IAAI,CAACyB,kDAAkD,CAAC1B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAClF7B,GAAG,CAAE8B,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;EAEA;;;;;;EAMAsB,iDAAiDA,CAAC3B,MAAwD,EAAEC,OAAqB;IAC/H,OAAOnB,wCAAwC,CAAC,IAAI,CAACe,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3F;EAEA;;;;;;EAMAnB,wCAAwCA,CAACkB,MAAwD,EAAEC,OAAqB;IACtH,OAAO,IAAI,CAAC0B,iDAAiD,CAAC3B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjF7B,GAAG,CAAE8B,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;EAEA;;IACgB,KAAAuB,iCAAiC,GAAG,8BAA8B;EAAC;EAEnF;;;;;;EAMAC,4CAA4CA,CAAC7B,MAAmD,EAAEC,OAAqB;IACrH,OAAOR,mCAAmC,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAR,mCAAmCA,CAACO,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAAC4B,4CAA4C,CAAC7B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E7B,GAAG,CAAE8B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAyB,2CAA2CA,CAAC9B,MAAkD,EAAEC,OAAqB;IACnH,OAAOT,kCAAkC,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAT,kCAAkCA,CAACQ,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAAC6B,2CAA2C,CAAC9B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3E7B,GAAG,CAAE8B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;;;uCA1aWX,gBAAgB,EAAAqC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhB1C,gBAAgB;MAAA2C,OAAA,EAAhB3C,gBAAgB,CAAA4C,IAAA;MAAAC,UAAA,EADH;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}